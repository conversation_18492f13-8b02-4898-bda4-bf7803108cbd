#!/bin/bash

set -u # or set -o nounset
: "$TENANT" "$REGION" "$AWS_ACCOUNT_ID"

cd ..

export CLUSTER_NAME="$TENANT-bill-payment-cluster"
export SERVICE_NAME="$TENANT-bill-payment-service"
export TASK_DEFINITION_NAME="$TENANT-bill-payment-task"
export IMAGE_NAME=bill-payment-api
export AWS_ECR="${AWS_ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com"
export AWS_ECR_REPOSITORY="${AWS_ECR}/${IMAGE_NAME}"

main() {
  TS=$(date +%s)
  VERSION=test-qa-$TS

  echo deploy start: version["$VERSION"]

  gradle_build
  ecr_login
  image_build_and_push "$VERSION"
  ecs_new_task_definition "$VERSION"
  ecs_update_service

  echo deploy finish: version["$VERSION"]
}

function gradle_build() {
  make format
  ./gradlew build -x test
}

function ecr_login() {
  aws ecr get-login-password --region $REGION | docker login --username AWS --password-stdin $AWS_ECR
}

function image_build_and_push() {
  local VERSION=$1

  if [ ! -f "dd-java-agent.jar" ]; then
    wget -O dd-java-agent.jar 'https://dtdg.co/latest-java-tracer'
  fi
  set -x
  docker build --platform=linux/amd64 --build-arg APP_VERSION="$VERSION" -t $IMAGE_NAME:"$VERSION" .
  docker tag $IMAGE_NAME:"$VERSION" $AWS_ECR_REPOSITORY:"$VERSION"
  docker push $AWS_ECR_REPOSITORY:"$VERSION"
  set +x
}

function ecs_new_task_definition() {
  local VERSION=$1

  TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TASK_DEFINITION_NAME")
  NEW_CONTAINER_DEFINITION=$(echo "$TASK_DEFINITION" | python3 ./ci/update_task_definition_image.py $AWS_ECR_REPOSITORY:"$VERSION")

  CURRENT_CPU=$(echo "$TASK_DEFINITION" | jq '.taskDefinition.cpu|tonumber')
  CURRENT_MEMORY=$(echo "$TASK_DEFINITION" | jq '.taskDefinition.memory|tonumber')

  TASK_ROLE_ARN=$(echo "$TASK_DEFINITION" | jq -r '.taskDefinition.taskRoleArn')
  EXECUTION_ROLE_ARN=$(echo "$TASK_DEFINITION" | jq -r '.taskDefinition.executionRoleArn')

  echo "==== ECS - New Task Definition ===="
  set -x
  aws ecs register-task-definition \
    --family "$TASK_DEFINITION_NAME" \
    --container-definitions "$NEW_CONTAINER_DEFINITION" \
    --requires-compatibilities "FARGATE" \
    --network-mode awsvpc \
    --cpu "$CURRENT_CPU" \
    --memory "$CURRENT_MEMORY" \
    --task-role-arn "$TASK_ROLE_ARN" \
    --execution-role-arn "$EXECUTION_ROLE_ARN" >/dev/null 2>&1
  set +x
}

function ecs_update_service() {
  ECS_SERVICE=$(aws ecs describe-services --cluster $CLUSTER_NAME --services $SERVICE_NAME)

  CURRENT_DESIRED_COUNT=$(echo "$ECS_SERVICE" | jq ".services[] | select(.serviceName = \"${SERVICE_NAME}\") | .desiredCount")

  echo "==== ECS - Update Service ===="
  set -x
  aws ecs update-service \
    --cluster "$CLUSTER_NAME" \
    --service "$SERVICE_NAME" \
    --task-definition "$TASK_DEFINITION_NAME" \
    --desired-count "$CURRENT_DESIRED_COUNT" >/dev/null 2>&1
  set +x
}

main