#!/bin/bash

cd ..

export REGION=sa-east-1

export CLUSTER_NAME=bill-payment-cluster
export SERVICE_NAME=bill-payment-service
export TASK_DEFINITION_NAME=bill-payment-task
export AWS_ACCOUNT_ID=************
export IMAGE_NAME=bill-payment-api
export AWS_ECR=${AWS_ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com
export AWS_ECR_REPOSITORY=${AWS_ECR}/${IMAGE_NAME}

main() {

  echo "AWSPROFILE " + $AWS_PROFILE
  aws sts get-caller-identity | head

  TS=$(date +%s)
  VERSION=test-qa-$TS

  echo deploy start: version["$VERSION"]

  gradle_build
  ecr_login
  image_build_and_push "$VERSION"
  ecs_new_task_definition "$VERSION"
  ecs_update_service
  ecs_show_latest_task_definition_version

  echo deploy finish: version["$VERSION"]
}

function ecs_show_latest_task_definition_version() {
  TASK_DEFINITION_ARN=$(aws ecs describe-task-definition --task-definition "${TASK_DEFINITION_NAME}" | jq '.taskDefinition.taskDefinitionArn' | cut -d '/' -f2- | sed -e 's/"//g')
  printf 'Run this query in Cloudwatch, if you want:\n\n'
  echo "fields @timestamp, @message, @logStream, @log
| filter ecs_task_definition = '${TASK_DEFINITION_ARN}'
| sort @timestamp desc
| limit 2000"
  printf '\n\n'
}

function gradle_build() {
  make format
  ./gradlew build -x test
}

function ecr_login() {
  aws ecr get-login-password --region $REGION | docker login --username AWS --password-stdin $AWS_ECR
}

function image_build_and_push() {
  local VERSION=$1

  if [ ! -f "dd-java-agent.jar" ]; then
    wget -O dd-java-agent.jar 'https://dtdg.co/latest-java-tracer'
  fi
  docker build --platform=linux/amd64 --build-arg APP_VERSION="$VERSION" -t $IMAGE_NAME:"$VERSION" .
  docker tag $IMAGE_NAME:"$VERSION" $AWS_ECR_REPOSITORY:"$VERSION"
  docker push $AWS_ECR_REPOSITORY:"$VERSION"
}

function ecs_new_task_definition() {
  local VERSION=$1

  TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TASK_DEFINITION_NAME")
  NEW_CONTAINER_DEFINITION=$(echo "$TASK_DEFINITION" | jq --arg NEW_IMAGE "$AWS_ECR_REPOSITORY:$VERSION" '.taskDefinition.containerDefinitions[0].image |= $NEW_IMAGE' | jq '.taskDefinition.containerDefinitions')

  CURRENT_CPU=$(echo "$TASK_DEFINITION" | jq '.taskDefinition.cpu|tonumber')
  CURRENT_MEMORY=$(echo "$TASK_DEFINITION" | jq '.taskDefinition.memory|tonumber')
  VOLUMES=$(echo "$TASK_DEFINITION" | jq -c '.taskDefinition.volumes')

  echo "==== ECS - New Task Definition ===="
  aws ecs register-task-definition \
    --family "$TASK_DEFINITION_NAME" \
    --container-definitions "$NEW_CONTAINER_DEFINITION" \
    --requires-compatibilities "FARGATE" \
    --network-mode awsvpc \
    --volumes "$VOLUMES" \
    --cpu "$CURRENT_CPU" \
    --memory "$CURRENT_MEMORY" \
    --task-role-arn arn:aws:iam::${AWS_ACCOUNT_ID}:role/bill-payment-task-role \
    --execution-role-arn arn:aws:iam::${AWS_ACCOUNT_ID}:role/bill-payment-task-exec-role >/dev/null 2>&1
}

function ecs_update_service() {
  ECS_SERVICE=$(aws ecs describe-services --cluster $CLUSTER_NAME --services $SERVICE_NAME)

  CURRENT_DIRED_COUNT=$(echo "$ECS_SERVICE" | jq '.services[] | select( .serviceName = "bill-payment-service") | .desiredCount')

  echo "==== ECS - Update Service ===="
  aws ecs update-service \
    --cluster "$CLUSTER_NAME" \
    --service "$SERVICE_NAME" \
    --task-definition "$TASK_DEFINITION_NAME" \
    --desired-count "$CURRENT_DIRED_COUNT" >/dev/null 2>&1
}

main