plugins {
    id("com.github.johnrengelman.shadow") version "8.1.1"
    id("jacoco-report-aggregation")
    id("org.barfuin.gradle.jacocolog") version "3.1.0"
}

version = "0.1"
group = "ai.friday.billpayment.application"

dependencies {
    implementation(project(path = ":"/*, configuration = "shadow"*/))
    implementation(project(":apps:me-poupe"))
    implementation(project(":apps:friday"))
    implementation(project(":apps:motorola"))
    implementation(project(":apps:gigu"))
}

application {
    mainClass.set("ai.friday.billpayment.ApplicationKt")
}

tasks {
    shadowJar {
        isZip64 = true
        archiveBaseName = rootProject.name
    }
}

tasks.check {
    dependsOn(tasks.named<JacocoReport>("testCodeCoverageReport"))
}