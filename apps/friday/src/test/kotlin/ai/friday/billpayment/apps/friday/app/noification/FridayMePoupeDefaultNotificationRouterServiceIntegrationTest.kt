package ai.friday.billpayment.apps.friday.app.noification

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.notification.DefaultNotificationRouterService
import ai.friday.billpayment.app.notification.EmailNotProcessedNotificationRequest
import ai.friday.billpayment.app.notification.EmailNotProcessedNotificationService
import ai.friday.billpayment.app.notification.InsecureBillNotificationRequest
import ai.friday.billpayment.app.notification.InsecureBillNotificationService
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import java.time.ZoneId
import java.time.ZonedDateTime
import org.junit.jupiter.api.Test

@MicronautTest(environments = [FRIDAY_ENV])
class FridayMePoupeDefaultNotificationRouterServiceIntegrationTest(private val notificationRouterService: DefaultNotificationRouterService) {
    @Test
    fun `should send an email not processed notification to the right service bean`() {
        val notification = EmailNotProcessedNotificationRequest(
            members = listOf(),
            walletName = "wallet_name",
            sender = EmailAddress("email"),
            subject = "subject",
            dateTime = ZonedDateTime.of(2023, 1, 1, 10, 0, 0, 0, ZoneId.systemDefault()),
        )

        val bean = notificationRouterService.getBean(notification)

        bean.shouldNotBeNull()
        bean.shouldBeTypeOf<EmailNotProcessedNotificationService>()
    }

    @Test
    fun `should send an insecure bill notification to the right service bean`() {
        val notification = InsecureBillNotificationRequest(
            members = listOf(),
            subject = "subject",
        )

        val bean = notificationRouterService.getBean(notification)

        bean.shouldNotBeNull()
        bean.shouldBeTypeOf<InsecureBillNotificationService>()
    }
}