package ai.friday.billpayment.adapters.vehicledebts

import ai.friday.billpayment.Wm
import ai.friday.billpayment.Wm.aJsonResponse
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.rxClient
import com.github.tomakehurst.wiremock.client.WireMock.get
import io.kotest.matchers.booleans.shouldBeTrue
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test

class VehicleDebtsAdapterTest {
    val wm = Wm.mockServer()
    val httpClient = wm.rxClient()

    private val configuration = mockk<VehicleDebtsConfiguration> {
        every { username } returns "username"
        every { password } returns "password"
        every { retrieveVehiclesPath } returns "/retrieveVehicles"
        every { enrollmentUserPath } returns "/enrollmentUser"
        every { enrollmentVehiclePath } returns "/enrollmentVehicle"
        every { tenant } returns "tenant"
    }

    private val vehicleDebtsAdapter = VehicleDebtsAdapter(configuration = configuration, httpClient = httpClient, scopeReader = mockk(relaxed = true))

    @Test
    fun getAllVehicleByUser() {
        wm.stubFor(
            get("/retrieveVehicles")
                .willReturn(
                    aJsonResponse(
                        status = 200,
                        body = """
                            [
                                {
                                    "clientUserId":"ACCOUNT-ecafe3dc-eaca-42b2-8a50-f65d00bb5ea1",
                                    "document":"***********",
                                    "licensePlate":"JBQ6C74",
                                    "source":"System",
                                    "createdAt":"2025-04-01T20:47:07.369342902Z",
                                    "updatedAt":"2025-04-01T20:47:07.369348598Z",
                                    "status":"ACTIVE",
                                    "description":""
                                }
                            ]
                        """.trimIndent(),
                    ),
                ),
        )

        val result = vehicleDebtsAdapter.getAllVehicleByUser(AccountId("ACCOUNT-ecafe3dc-eaca-42b2-8a50-f65d00bb5ea1"), Document("***********"))

        result.isSuccess.shouldBeTrue()
    }
}