package ai.friday.billpayment.modules.pfm.adapters.messaging

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.pfm.app.WalletBillCategoryService
import arrow.core.right
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message

class SQSCreateDefaultCategoriesHandlerTest {
    private val walletBillCategoryService = mockk<WalletBillCategoryService> {
        every {
            findWalletCategories(any())
        } returns emptyList()

        every {
            createDefaultWalletCategories(any())
        } returns emptyList<WalletBillCategory>().right()
    }
    private val handler = SQSCreateDefaultCategoriesHandler(
        amazonSQS = mockk(),
        configuration = mockk {
            every { createDefaultCategories } returns "createDefaultCategories"
        },
        walletBillCategoryService = walletBillCategoryService,
    )

    @Test
    fun `deve desserializar a mensagem para o TO`() {
        val message = Message.builder().body(getObjectMapper().writeValueAsString(mapOf("walletId" to "1"))).build()
        handler.handleMessage(message)

        verify {
            walletBillCategoryService.createDefaultWalletCategories(WalletId("1"))
        }
    }
}