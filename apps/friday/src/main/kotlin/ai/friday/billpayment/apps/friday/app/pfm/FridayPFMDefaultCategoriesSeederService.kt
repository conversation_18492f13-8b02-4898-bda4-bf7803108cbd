package ai.friday.billpayment.apps.friday.app.pfm

import ai.friday.billpayment.app.pfm.DefaultWalletBillCategory
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.apps.friday.app.Friday
import ai.friday.billpayment.modules.pfm.app.PFMDefaultCategoriesSeederService

@Friday
open class FridayPFMDefaultCategoriesSeederService : PFMDefaultCategoriesSeederService {
    override fun listDefaultCategories(): List<DefaultWalletBillCategory> {
        return defaultPFMCategories
    }
}

val defaultPFMCategories = listOf(
    DefaultWalletBillCategory(
        categoryId = PFMCategoryId("ALIMENTACAO"),
        name = "Alimentação",
        icon = "ALIMENTACAO",
    ),
    DefaultWalletBillCategory(
        categoryId = PFMCategoryId("CUIDADOS_PESSOAIS"),
        name = "Cuidados pessoais",
        icon = "CUIDADOS_PESSOAIS",
    ),
    DefaultWalletBillCategory(
        categoryId = PFMCategoryId("DIVIDA"),
        name = "Dívida",
        icon = "DIVIDA",
    ),
    DefaultWalletBillCategory(
        categoryId = PFMCategoryId("EDUCACAO"),
        name = "Educação",
        icon = "EDUCACAO",
    ),
    DefaultWalletBillCategory(
        categoryId = PFMCategoryId("IMPOSTOS"),
        name = "Impostos",
        icon = "IMPOSTOS",
    ),
    DefaultWalletBillCategory(
        categoryId = PFMCategoryId("INVESTIMENTOS"),
        name = "Investimentos",
        icon = "INVESTIMENTOS",
    ),
    DefaultWalletBillCategory(
        categoryId = PFMCategoryId("LAZER"),
        name = "Lazer",
        icon = "LAZER",
    ),
    DefaultWalletBillCategory(
        categoryId = PFMCategoryId("MORADIA"),
        name = "Moradia",
        icon = "MORADIA",
    ),
    DefaultWalletBillCategory(
        categoryId = PFMCategoryId("OUTROS"),
        name = "Diversos",
        icon = "OUTROS",
    ),
    DefaultWalletBillCategory(
        categoryId = PFMCategoryId("PRESENTES_DOACOES"),
        name = "Presentes e doações",
        icon = "PRESENTES_DOACOES",
    ),
    DefaultWalletBillCategory(
        categoryId = PFMCategoryId("ROUPAS_ACESSORIOS"),
        name = "Roupas e acessórios",
        icon = "ROUPAS_ACESSORIOS",
    ),
    DefaultWalletBillCategory(
        categoryId = PFMCategoryId("SAUDE"),
        name = "Saúde",
        icon = "SAUDE",
    ),
    DefaultWalletBillCategory(
        categoryId = PFMCategoryId("TRANSPORTE"),
        name = "Transporte",
        icon = "TRANSPORTE",
    ),
)