package ai.friday.billpayment.apps.motorola.app.register

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.account.RegisterService
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.dda.DDAStatus
import ai.friday.billpayment.app.dda.FullDDAPostProcessor
import ai.friday.billpayment.app.dda.optedOut
import ai.friday.billpayment.app.fingerprint.DeviceFingerprint
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintRepository
import ai.friday.billpayment.app.fingerprint.DeviceId
import ai.friday.billpayment.app.fingerprint.DeviceScreenResolution
import ai.friday.billpayment.app.fingerprint.DeviceStatus
import ai.friday.billpayment.app.fingerprint.DeviceType
import ai.friday.billpayment.app.fingerprint.MobileDeviceDetails
import ai.friday.billpayment.app.fingerprint.RegisteredDevice
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.login.LoginService
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.RegisterPixKeyCommand
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.apps.motorola.Motorola
import ai.friday.billpayment.apps.motorola.app.register.MotorolaRegister.ARBI_BANK_NO
import ai.friday.billpayment.apps.motorola.app.register.MotorolaRegister.ARBI_ISPB
import ai.friday.billpayment.apps.motorola.app.register.MotorolaRegister.ARBI_ROUTING_NO
import ai.friday.billpayment.apps.motorola.app.register.MotorolaRegister.DEFAULT_CREDIT_CARD_QUOTA
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.micronaut.context.annotation.Property
import java.util.UUID

@Motorola
open class MotorolaRegisterService(
    private val registerService: RegisterService,
    private val ddaService: DDAService,
    private val loginService: LoginService,
    private val accountService: AccountService,
    private val postProcessor: FullDDAPostProcessor,
    private val messagePublisher: MessagePublisher,
    @Property(name = "sqs.registerPixKeyQueueName") private val registerPixKeyQueueName: String,
    private val deviceFingerprintRepository: DeviceFingerprintRepository,
) {
    open fun register(command: MotorolaCreateAccountCommand): Result<RegisterResult> {
        return runCatching {
            command.run {
                val bankAccount = buildBankAccount(command)

                val account: Account = run {
                    val existingAccount = accountService.findAccountByDocumentOrNull(document.value)
                        ?.also {
                            if (it.status == AccountStatus.ACTIVE) {
                                return Result.success(RegisterResult.AccountAlreadyExists(it.accountId))
                            }
                        }

                    val accountId = existingAccount?.accountId ?: AccountId()

                    // create account
                    registerService.setupAccountInternally(
                        accountId = accountId,
                        document = document,
                        email = email,
                        phone = phone,
                        name = name,
                        groups = groups,
                        externalId = externalId,
                        type = type,
                        subscriptionType = subscriptionType,
                        bankAccount = bankAccount,
                        creditCardQuota = command.creditCardQuota,
                    )
                }

                val accountId = account.accountId

                val result = ddaService.register(
                    accountId = accountId,
                    document = bankAccount.document,
                )

                if (command.ddaAlreadyActive) {
                    ddaService.activate(accountId)
                    ddaService.findDDARegister(command.document)?.let {
                        postProcessor.process(it) // FIXME no momento estamos usando o DDA da FRIDAY que já possui o DDA do usuário ativado
                    }
                }

                // Default value for RECEIVE_NOTIFICATION is true when account is created
                if (!command.activateNotifications) {
                    accountService.save(
                        account.copy(
                            configuration = account.configuration.copy(
                                receiveNotification = false,
                                receiveDDANotification = false,
                            ),
                        ),
                    )
                }

                if (result.optedOut()) {
                    return Result.success(RegisterResult.RegisterDDAFailure(result))
                }

                messagePublisher.sendMessage(
                    registerPixKeyQueueName,
                    RegisterPixKeyCommand(
                        accountNo = AccountNumber(bankAccount.accountNo, bankAccount.accountDv),
                        key = PixKey(value = "", type = PixKeyType.EVP),
                        document = document.value,
                        name = name,
                        walletId = WalletId(accountId.value),
                    ),
                )

                loginService.createLogin(
                    providerUser = ProviderUser(
                        id = externalId.value,
                        providerName = ProviderName.MOTOROLA,
                        username = name,
                        emailAddress = email,
                        created = getZonedDateTime(),
                    ),
                    accountId = accountId,
                    role = Role.OWNER,
                )

                if (arbiDeviceId != null) {
                    registerExistingDevice(accountId, arbiDeviceId, accountNumber)
                }

                return Result.success(RegisterResult.Success(accountId))
            }
        }
    }

    protected open fun registerExistingDevice(accountId: AccountId, arbiDeviceId: String, accountNumber: AccountNumber) {
        // Cria um fingerprint temporário para o dispositivo
        val temporaryFingerprint = UUID.randomUUID().toString()

        // Cria detalhes do dispositivo com o fingerprint temporário
        val deviceDetails = MobileDeviceDetails(
            fingerprint = temporaryFingerprint,
            uuid = UUID.randomUUID(),
            alias = "Motorola Device",
            screenResolution = DeviceScreenResolution(375, 812),
            type = DeviceType.ANDROID,
            dpi = 3.0,
            manufacturer = "Motorola",
            model = "Moto G",
            rooted = false,
            storageCapacity = *********,
            osId = UUID.randomUUID().toString().uppercase(),
            fresh = false,
        )

        // Cria um dispositivo registrado com o deviceId fornecido
        val registeredDevice = RegisteredDevice(
            deviceIds = mapOf(accountNumber to DeviceId(arbiDeviceId)),
            fingerprint = DeviceFingerprint(temporaryFingerprint),
            status = DeviceStatus.ACTIVE,
            creationDate = getZonedDateTime(),
            details = deviceDetails,
            liveness = null,
        )

        // Salva o dispositivo no repositório
        deviceFingerprintRepository.save(registeredDevice, accountId)
    }
}

private fun buildBankAccount(command: MotorolaCreateAccountCommand) = BankAccount(
    accountType = AccountType.CHECKING,
    bankNo = ARBI_BANK_NO,
    routingNo = ARBI_ROUTING_NO,
    accountNo = command.accountNumber.number,
    accountDv = command.accountNumber.dv,
    document = command.document.value,
    ispb = ARBI_ISPB,
)

object MotorolaRegister {
    const val ARBI_BANK_NO = 213L
    const val ARBI_ROUTING_NO = 19L
    const val ARBI_ISPB = "0535701"
    const val DEFAULT_CREDIT_CARD_QUOTA = 200000L
}

data class MotorolaCreateAccountCommand(
    val email: EmailAddress,
    val phone: MobilePhone,
    val name: String,
    val groups: List<AccountGroup>,
    val externalId: ExternalId,
    val type: UserAccountType,
    val subscriptionType: SubscriptionType,
    val accountNumber: AccountNumber,
    val document: Document,
    val activateNotifications: Boolean,
    val ddaAlreadyActive: Boolean,
    val arbiDeviceId: String? = null,
    val creditCardQuota: Long = DEFAULT_CREDIT_CARD_QUOTA,
)

sealed class RegisterResult {
    data class Success(val accountId: AccountId) : RegisterResult()
    data class RegisterDDAFailure(val ddaStatus: DDAStatus) : RegisterResult()
    data class AccountAlreadyExists(val accountId: AccountId) : RegisterResult()
}