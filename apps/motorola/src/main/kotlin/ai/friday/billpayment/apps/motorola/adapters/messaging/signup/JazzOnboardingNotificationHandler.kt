package ai.friday.billpayment.apps.motorola.adapters.messaging.signup

import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.ParallelMessageHandlerConfiguration
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.messaging.markers
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.account.SystemActivityType
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.account.toSystemActivityKey
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.apps.motorola.app.register.MotorolaCreateAccountCommand
import ai.friday.billpayment.apps.motorola.app.register.MotorolaRegisterService
import ai.friday.billpayment.plus
import io.micronaut.context.annotation.Property
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

// @Motorola
open class JazzOnboardingNotificationHandler(
    amazonSQS: SqsClient,
    configuration: ParallelMessageHandlerConfiguration,
    private val service: MotorolaRegisterService,
    @Property(name = "sqs.jazzOnboardingQueueName") private val queueName: String,
    private val systemActivityService: SystemActivityService,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = queueName,
) {
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val markers = message.markers()
        val payload = parseObjectFrom<OnboardingMessage>(message.body())

        markers.plus("onboardingMessage" to payload)

        val command = buildCommand(payload)

        if (!payload.enableAssistant && !isUserEligibleOnPartner(command.document)) {
            logger.warn(markers.plus("result" to "USER_NOT_ELIGIBLE"), "JazzOnboardingNotificationHandler")
            return SQSHandlerResponse(shouldDeleteMessage = true)
        }

        val result = service.register(command)
        logger.info(markers.plus("result" to result), "JazzOnboardingNotificationHandler")

        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    private fun isUserEligibleOnPartner(document: Document): Boolean =
        systemActivityService.getFlag(document.toSystemActivityKey(), SystemActivityType.UserEligibleOnPartner)

    data class OnboardingMessage(
        val accountKey: String,
        val document: String,
        val documentType: String,
        val email: String,
        val fullName: String,
        val phone: String,
        val bankDetails: BankDetails,
        val device: Device,
        val enableAssistant: Boolean = false,
    ) {
        data class BankDetails(
            val bank: String,
            val agency: String,
            val account: String,
        )

        data class Device(val arbiDeviceId: String)
    }

    private fun buildCommand(message: OnboardingMessage) = MotorolaCreateAccountCommand(
        email = EmailAddress(message.email),
        phone = MobilePhone.createOrNull(message.phone) ?: throw IllegalArgumentException("Invalid phone number: ${message.phone}"),
        name = message.fullName,
        groups = defaultUserGroups,
        externalId = ExternalId(value = message.accountKey, providerName = AccountProviderName.MOTOROLA),
        type = UserAccountType.FULL_ACCOUNT,
        subscriptionType = SubscriptionType.IN_APP,
        accountNumber = AccountNumber(message.bankDetails.account),
        document = Document.sanitized(message.document),
        activateNotifications = false,
        ddaAlreadyActive = false,
        arbiDeviceId = message.device.arbiDeviceId,
    )

    private val defaultUserGroups = listOf<AccountGroup>()
    private val logger = LoggerFactory.getLogger(JazzOnboardingNotificationHandler::class.java)
}