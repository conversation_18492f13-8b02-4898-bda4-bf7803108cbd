package ai.friday.billpayment.apps.motorola.adapters.auth

import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.security.EncryptionService
import ai.friday.billpayment.apps.motorola.Motorola
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getEpochMilli

@Motorola
internal open class MotorolaAuthenticationService(
    private val authRepository: ExternalAuthDbRepository,
    private val accountRepository: AccountRepository,
    private val jazzAdapter: JazzAuthenticationAdapter,
    private val encryptionService: EncryptionService,
) {
    fun authenticate(nonce: String): Result<Account> = runCatching {
        val tokenResponse = jazzAdapter.exchangeToken(nonce).getOrThrow()
        val account = findAccountByExternalId(tokenResponse).getOrThrow()

        val encryptedToken = encryptionService.encrypt(tokenResponse.jwtToken, tokenResponse.userId)

        authRepository.save(
            ExternalAuthToken(
                accountId = account.accountId,
                provider = AccountProviderName.MOTOROLA.name,
                token = encryptedToken,
                externalUserId = account.externalId()?.value.orEmpty(),
                nonce = nonce,
                expiration = getEpochMilli() + (tokenResponse.expiresIn * 1000),
            ),
        )

        account
    }

    fun authenticationToken(accountId: AccountId): Result<DecryptedExternalAuthToken?> = runCatching {
        authRepository.findByAccountId(accountId).getOrNull()?.let {
            DecryptedExternalAuthToken(
                accountId = accountId,
                decryptedToken = encryptionService.decrypt(it.token, it.externalUserId),
                encryptedToken = it.token,
                expiration = it.expiration,
                externalUserId = it.externalUserId,
                provider = it.provider,
            )
        }
    }

    private fun findAccountByExternalId(tokenResponse: JazzAuthenticationAdapter.NonceExchangeResponse) = runCatching {
        val externalId = ExternalId(tokenResponse.userId, AccountProviderName.MOTOROLA)

        accountRepository.findByExternalId(externalId).firstOrNull {
            it.status == AccountStatus.ACTIVE || it.status == AccountStatus.APPROVED
        } ?: throw AccountNotFoundException("Account not found")
    }
}