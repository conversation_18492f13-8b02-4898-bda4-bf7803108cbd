package ai.friday.billpayment.apps.motorola.adapters.api

import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.apps.motorola.Motorola
import io.micronaut.context.annotation.Property
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured

@Motorola
@Secured(BACKOFFICE)
@Controller("/motorola/backoffice")
class BackofficeController(
    private val messagePublisher: MessagePublisher,
    @Property(name = "sqs.signUpQueueName") private val queueName: String,
) {
    @Post("/register")
    fun register(@Body createAccountTO: MotorolaCreateAccountTO): HttpResponse<*> {
        messagePublisher.sendMessage(queueName, createAccountTO)

        return HttpResponse.noContent<Unit>()
    }
}

data class MotorolaCreateAccountTO(
    val email: String,
    val phone: String,
    val document: String,
    val name: String,
    val externalId: String,
    val fullAccountNumber: String,
    val activateNotifications: Boolean,
    val ddaAlreadyActive: Boolean,
)