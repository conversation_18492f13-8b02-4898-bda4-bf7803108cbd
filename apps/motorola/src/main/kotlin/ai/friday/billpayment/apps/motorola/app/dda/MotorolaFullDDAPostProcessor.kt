package ai.friday.billpayment.apps.motorola.app.dda

import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.dda.DDARegister
import ai.friday.billpayment.app.dda.FullDDAPostProcessor
import ai.friday.billpayment.app.dda.FullDDAPostProcessorError
import ai.friday.billpayment.app.integrations.RegisterInstrumentationService
import ai.friday.billpayment.apps.motorola.Motorola
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Motorola
class MotorolaFullDDAPostProcessor(
    private val systemActivityService: SystemActivityService,
    private val registerInstrumentationService: RegisterInstrumentationService,
    private val accountService: AccountService,
) : FullDDAPostProcessor {
    override fun process(ddaRegister: DDARegister): Either<FullDDAPostProcessorError, Unit> {
        val account = accountService.findAccountByIdOrNull(ddaRegister.accountId) ?: return FullDDAPostProcessorError.FindAccountError(Exception("Account not found")).left()

        registerInstrumentationService.activated(account.accountId, RegistrationType.FULL)

        systemActivityService.setAccountActivated(accountId = account.accountId)

        updateAccount(account)

        logger.info(
            Markers.appendEntries(
                mapOf(
                    "accountId" to account.accountId.value,
                    "status" to ddaRegister.status,
                ),
            ),
            "MotorolaFullDDAPostProcessor#process",
        )

        return Unit.right()
    }

    private fun updateAccount(account: Account) {
        val receiveNotifications = account.configuration.receiveNotification

        accountService.save(
            account.copy(
                status = AccountStatus.ACTIVE,
                configuration = account.configuration.copy(
                    receiveDDANotification = receiveNotifications,
                ),
                activated = getZonedDateTime(),
            ),
        )
    }

    private val logger = LoggerFactory.getLogger(MotorolaFullDDAPostProcessor::class.java)
}