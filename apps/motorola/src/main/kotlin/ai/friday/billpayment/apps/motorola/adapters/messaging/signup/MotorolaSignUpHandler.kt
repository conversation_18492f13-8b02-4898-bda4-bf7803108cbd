package ai.friday.billpayment.apps.motorola.adapters.messaging.signup

import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.ParallelMessageHandlerConfiguration
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.apps.motorola.Motorola
import ai.friday.billpayment.apps.motorola.app.register.MotorolaCreateAccountCommand
import ai.friday.billpayment.apps.motorola.app.register.MotorolaRegister
import ai.friday.billpayment.apps.motorola.app.register.MotorolaRegisterService
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Motorola
open class MotorolaSignUpHandler(
    amazonSQS: SqsClient,
    configuration: ParallelMessageHandlerConfiguration,
    private val service: MotorolaRegisterService,
    @Property(name = "sqs.signUpQueueName") private val queueName: String,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = queueName,
) {
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val messageBody = parseObjectFrom<MotorolaCreateAccountMessage>(message.body())

        val result = service.register(buildCommand(messageBody))

        logger.info(Markers.append("body", message.body()).andAppend("result", result), "MotorolaSignUpHandler")

        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    override fun handleError(message: Message, e: Exception): SQSHandlerResponse {
        logger.error(Markers.append("body", message.body()), "MotorolaSignUpHandler", e)

        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    private fun buildCommand(messageBody: MotorolaCreateAccountMessage) = MotorolaCreateAccountCommand(
        email = EmailAddress(messageBody.email),
        phone = MobilePhone(messageBody.phone),
        name = messageBody.name,
        groups = listOf(AccountGroup.CHATBOT_LEGACY),
        externalId = ExternalId(value = messageBody.externalId, providerName = AccountProviderName.MOTOROLA),
        type = UserAccountType.FULL_ACCOUNT,
        subscriptionType = SubscriptionType.IN_APP,
        accountNumber = AccountNumber(messageBody.fullAccountNumber),
        document = Document(messageBody.document),
        activateNotifications = messageBody.activateNotifications,
        ddaAlreadyActive = messageBody.ddaAlreadyActive,
        arbiDeviceId = messageBody.arbiDeviceId,
        creditCardQuota = messageBody.creditCardQuota,
    )

    private val logger = LoggerFactory.getLogger(MotorolaSignUpHandler::class.java)
}

data class MotorolaCreateAccountMessage(
    val email: String,
    val phone: String,
    val document: String,
    val name: String,
    val externalId: String,
    val fullAccountNumber: String,
    val activateNotifications: Boolean,
    val ddaAlreadyActive: Boolean,
    val arbiDeviceId: String? = null,
    val creditCardQuota: Long = MotorolaRegister.DEFAULT_CREDIT_CARD_QUOTA,
)