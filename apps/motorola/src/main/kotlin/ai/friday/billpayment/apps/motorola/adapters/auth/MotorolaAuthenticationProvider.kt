package ai.friday.billpayment.apps.motorola.adapters.auth

import ai.friday.billpayment.adapters.auth.AbstractJwtAuthenticationFactory
import ai.friday.billpayment.adapters.auth.JwtValidator
import ai.friday.billpayment.adapters.auth.TO_CLAIM_ACCOUNT_ID
import ai.friday.billpayment.adapters.auth.TO_CLAIM_EMAIL
import ai.friday.billpayment.adapters.auth.TO_CLAIM_NAME
import ai.friday.billpayment.adapters.auth.TO_CLAIM_PROVIDER_NAME
import ai.friday.billpayment.adapters.auth.TO_CLAIM_PROVIDER_USERNAME
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.apps.motorola.Motorola
import ai.friday.billpayment.log
import ai.friday.billpayment.markers
import ai.friday.billpayment.originalSimpleName
import com.nimbusds.jwt.JWT
import com.nimbusds.jwt.JWTClaimNames
import io.micronaut.context.annotation.Context
import io.micronaut.context.annotation.Primary
import io.micronaut.http.HttpRequest
import io.micronaut.security.authentication.Authentication
import io.micronaut.security.authentication.AuthenticationResponse
import io.micronaut.security.filters.AuthenticationFetcher
import io.micronaut.security.token.jwt.validator.JwtAuthenticationFactory
import io.reactivex.Flowable
import jakarta.inject.Named
import java.util.Optional
import kotlin.jvm.optionals.getOrNull
import org.reactivestreams.Publisher
import org.slf4j.LoggerFactory

internal interface NonceAuthenticationProvider {
    fun authenticate(nonce: String): Optional<Authentication>
    fun authenticate(jwt: JWT): Optional<Authentication>
}

@Motorola
internal class NonceAuthenticationFetcher(private val provider: NonceAuthenticationProvider) : AuthenticationFetcher<HttpRequest<*>> {
    override fun fetchAuthentication(request: HttpRequest<*>): Publisher<Authentication> {
        // Verificar se o nonce foi enviado como um header
        val nonce = request.headers.get("X-Nonce", String::class.java).getOrNull()
            ?: return Flowable.empty()

        val authentication = provider.authenticate(nonce).getOrNull()
            ?: return Flowable.error(AuthenticationResponse.exception())

        return Flowable.just(authentication)
    }
}

@Motorola
internal class MotorolaNonceAuthenticationProvider(
    @Named("nonce") private val jwtValidator: JwtValidator,
    private val accountRepository: AccountRepository,
    private val authenticationService: MotorolaAuthenticationService,
) : NonceAuthenticationProvider {
    private val logger = LoggerFactory.getLogger(MotorolaNonceAuthenticationProvider::class.java)
    private val authenticationFailure = AuthenticationResponse.failure("account not found").authentication

    override fun authenticate(nonce: String): Optional<Authentication> {
        val account = when {
            nonce.startsWith("ACCOUNT") -> return createAuthentication(AccountId(nonce)).also {
                val roles = it.getOrNull()?.roles.orEmpty()

                if (Role.ADMIN.name !in roles) {
                    logger.error(markers("error" to "admin login failed"), javaClass.originalSimpleName())
                    return authenticationFailure
                }
            }

            else -> authenticationService.authenticate(nonce).getOrElse { e ->
                logger.error(markers("error" to e.message), javaClass.originalSimpleName(), e)
                return authenticationFailure
            }
        }

        return createAuthentication(account)
    }

    override fun authenticate(jwt: JWT): Optional<Authentication> {
        if (!jwtValidator.validate(jwt.jwtClaimsSet)) {
            AbstractJwtAuthenticationFactory.logger.warn(
                log(
                    "provider" to ProviderName.MOTOROLA.name,
                    "reason" to "invalid issuer or audience",
                    "issuer" to jwt.jwtClaimsSet.issuer,
                    "audience" to jwt.jwtClaimsSet.audience,
                    "subject" to jwt.jwtClaimsSet.subject,
                ),
                javaClass.originalSimpleName(),
            )

            return authenticationFailure
        }

        return createAuthentication(accountId = AccountId(jwt.jwtClaimsSet.subject))
    }

    private fun createAuthentication(accountId: AccountId): Optional<Authentication> {
        val account = accountRepository.findByIdOrNull(accountId) ?: return authenticationFailure

        return createAuthentication(account)
    }

    private fun createAuthentication(account: Account): Optional<Authentication> {
        val accountId = account.accountId.value

        val attributes =
            mapOf(
                JWTClaimNames.SUBJECT to accountId,
                JWTClaimNames.ISSUER to jwtValidator.configuration.issuer,
                JWTClaimNames.AUDIENCE to jwtValidator.configuration.audience,
                TO_CLAIM_EMAIL to account.emailAddress.value,
                TO_CLAIM_ACCOUNT_ID to accountId,
                TO_CLAIM_NAME to account.name,
                TO_CLAIM_PROVIDER_NAME to ProviderName.MOTOROLA.name,
                TO_CLAIM_PROVIDER_USERNAME to accountId,
                TO_CLAIM_PROVIDER_USERNAME to accountId,
            )

        val roles = buildList {
            add(Role.OWNER.name)
            if (AccountGroup.DEVELOPER in account.configuration.groups) add(Role.ADMIN.name)
        }

        return AuthenticationResponse.success(accountId, roles, attributes).authentication
    }
}

@Motorola
internal class NonceJwtAuthenticationFactory(
    private val provider: NonceAuthenticationProvider,
    @Named("nonce") private val jwtValidator: JwtValidator,
) : AbstractJwtAuthenticationFactory() {
    override fun createAuthentication(token: JWT): Optional<Authentication> =
        provider.authenticate(token).also { authentication ->
            logger.debug(log("authentication" to authentication), javaClass.originalSimpleName())
        }
}

@Primary
@Context
@Motorola
internal class MultipleJwtAuthenticationFactory(private val authenticationFactories: List<AbstractJwtAuthenticationFactory>) : JwtAuthenticationFactory {
    private val logger = LoggerFactory.getLogger(AbstractJwtAuthenticationFactory::class.java)

    override fun createAuthentication(token: JWT): Optional<Authentication> {
        authenticationFactories.forEach { factory ->
            runCatching {
                val authentication = factory.createAuthentication(token)

                if (authentication.isPresent) {
                    return authentication
                }
            }.getOrElse {
                logger.error(log("error" to it.message), javaClass.originalSimpleName())
            }
        }

        return Optional.empty()
    }
}