package ai.friday.billpayment.apps.motorola.adapters.jazz

import ai.friday.billpayment.and
import ai.friday.billpayment.app.EncryptUtils
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.payment.pinCode.PinCode
import ai.friday.billpayment.app.payment.pinCode.PinCodeRepository
import ai.friday.billpayment.app.payment.pinCode.ValidationResult
import ai.friday.billpayment.apps.motorola.Motorola
import ai.friday.billpayment.apps.motorola.adapters.auth.MotorolaAuthenticationService
import ai.friday.billpayment.markers
import ai.friday.billpayment.merge
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import org.slf4j.LoggerFactory

@Motorola
internal class JazzPinCodeAdapter(
    @Client(id = "jazz-pin-code") private val httpClient: RxHttpClient,
    private val authenticationService: MotorolaAuthenticationService,
) : PinCodeRepository {
    private val path = "frd/v1/pin/validate"

    private val logger = LoggerFactory.getLogger(JazzPinCodeAdapter::class.java)

    override fun validate(accountId: AccountId, pinCode: PinCode): ValidationResult {
        val markers = markers("accountId" to accountId)

        try {
            val token = authenticationService.authenticationToken(accountId).getOrElse {
                logger.error(markers, "JazzPinCodeAdapter#validate", it)
                return ValidationResult(valid = false, maxAttemptsReached = false)
            } ?: return ValidationResult(valid = false, maxAttemptsReached = false)

            val result = EncryptUtils.sign(
                data = pinCode.value,
                path = "pem/jazz_pin_code.pem",
            ).getOrElse {
                logger.error(markers, "JazzPinCodeAdapter#validate", it)
                return ValidationResult(valid = false, maxAttemptsReached = false)
            }

            val requestBody = mapOf(
                "pin" to result,
                "userId" to token.externalUserId,
            )

            markers.and("pin" to result, "tokenStart" to token.decryptedToken.take(5))

            val request = HttpRequest.POST(path, requestBody).bearerAuth(token.decryptedToken)

            val response = httpClient.exchange(
                request,
                Argument.of(ValidationResponseTO::class.java),
                Argument.STRING,
            ).blockingFirst()

            val maxAttemptsReached = response.status.code == HttpStatus.LOCKED.code

            logger.info(markers.merge(response.markers().and("maxAttemptsReached" to maxAttemptsReached)), "JazzPinCodeAdapter#validate")

            return ValidationResult(
                valid = response.body().valid,
                maxAttemptsReached = maxAttemptsReached,
            )
        } catch (e: Throwable) {
            if (e is HttpClientResponseException) {
                markers.merge(e.markers())
            }

            logger.error(markers, "JazzPinCodeAdapter#validate", e)

            return ValidationResult(valid = false, maxAttemptsReached = false)
        }
    }
}

private data class ValidationResponseTO(
    val valid: Boolean,
)