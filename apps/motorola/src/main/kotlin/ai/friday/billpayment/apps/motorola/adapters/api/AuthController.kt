package ai.friday.billpayment.apps.motorola.adapters.api

import ai.friday.billpayment.apps.motorola.Motorola
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.Header
import io.micronaut.http.cookie.Cookie
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.security.rules.SecurityRule
import io.micronaut.security.token.jwt.generator.JwtTokenGenerator
import kotlin.jvm.optionals.getOrElse

@Secured(SecurityRule.IS_ANONYMOUS)
@Controller("/auth")
@Version("2")
@Motorola
internal class AuthController(private val jwtTokenGenerator: JwtTokenGenerator) {
    @Get("/nonce")
    fun auth(
        @Header("x-nonce") nonce: String,
        authentication: Authentication,
    ): HttpResponse<Any> {
        val attributes = authentication.attributes.toMutableMap()
        attributes["nonce"] = nonce
        attributes["iat"] = getZonedDateTime().toEpochSecond()
        attributes["exp"] = getZonedDateTime().plusMonths(30).toEpochSecond()

        val token = jwtTokenGenerator.generateToken(attributes)
            .getOrElse { return HttpResponse.serverError() }

        val cookie = Cookie.of("JWT", token)
            .httpOnly(true)
            .secure(true)
            .path("/")

        return HttpResponse.ok<Any?>(
            mapOf("token" to token),
        ).cookie(cookie)
    }
}