package ai.friday.billpayment.apps.motorola.adapters.auth

import ai.friday.billpayment.apps.motorola.Motorola
import ai.friday.billpayment.markers
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.http.HttpRequest.POST
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import org.slf4j.LoggerFactory

@Motorola
internal class JazzAuthenticationAdapter(
    @Client(id = "jazz-auth") private val httpClient: RxHttpClient,
    private val configuration: JazzConfiguration,
) {
    fun exchangeToken(nonce: String): Result<NonceExchangeResponse> {
        val request = POST(
            "/onboarding-dimo/v1/auth/nonce-exchange",
            NonceExchangeRequest(
                oneTimeCode = nonce,
                clientId = configuration.clientId,
            ),
        )

        val markers = request.markers()

        return runCatching {
            httpClient.exchange(request, NonceExchangeResponse::class.java).blockingFirst().let { response ->
                logger.info(markers.and(response.markers()), "JazzAuthenticationAdapter#exchangeToken")

                response.body()
            }
        }.onFailure { e ->
            if (e is HttpClientResponseException) {
                markers.add(e.markers())
            }
            logger.error(markers, "JazzAuthenticationAdapter#exchangeToken", e)
        }
    }

    data class NonceExchangeRequest(
        val oneTimeCode: String,
        val clientId: String,
    )

    data class NonceExchangeResponse(
        val jwtToken: String,
        val expiresIn: Int,
        val userId: String,
        val refreshToken: String? = null,
    )

    private val logger = LoggerFactory.getLogger(JazzAuthenticationAdapter::class.java)

    init {
        logger.info(
            markers(
                "clientId" to configuration.clientId,
                "host" to configuration.host,
            ),
            "JazzAuthenticationAdapter#initialized",
        )
    }
}

@Motorola
@ConfigurationProperties("integrations.jazz")
interface JazzConfiguration {
    val clientId: String
    val host: String
}