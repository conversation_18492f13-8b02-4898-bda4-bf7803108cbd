tenant:
  name: motorola
  api-subdomain: api-${tenant.name}
  client-subdomain: use-${tenant.name}

micronaut:
  application:
    name: bill-payment-service
  otel:
    enabled: false
  metrics:
    enabled: ${micronaut.otel.enabled}
    export:
      statsd:
        enabled: ${micronaut.otel.enabled}
      datadog:
        enabled: ${micronaut.otel.enabled}
  server:
    cors:
      enabled: true
      configurations:
        web:
          allowed-origins:
            - https://${tenant.client-subdomain}.meupagador.com.br
          allowed-methods:
            - GET
            - OPTIONS
  http:
    services:
      celcoin:
        url: ${configuration.staging.celcoin.url}
        read-timeout: 10s
        pool:
          max-connections: 5
          enabled: true
      jazz-auth:
        url: ${integrations.jazz.host}
        read-timeout: 10s
        pool:
          max-connections: 5
          enabled: true
      settlement-service:
        url: https://liquidacao.meupagador.com.br
        read-timeout: 20s
        pool:
          max-connections: 30
          enabled: true
      vehicle-debts-service:
        url: https://debitos-veiculares.meupagador.com.br
        read-timeout: 20s
        pool:
          max-connections: 30
          enabled: true
      arbi:
        url: https://gaphs.bancoarbi.com.br
        read-timeout: 60s
        pool:
          max-connections: 30
          enabled: true
        ssl:
          enabled: true
          isInsecureTrustAllCertificates: true
  #          client-authentication: WANT
  #          key-store:
  #            path: classpath:ssl/friday-arbi.p12
  #            type: PKCS12
  #            password: FROM_SECRETS
  session:
    http:
      redis:
        value-serializer: io.micronaut.jackson.serialize.JacksonObjectSerializer
  executors:
    scheduled:
      type: scheduled
      core-pool-size: 50
  security:
    enabled: true
    authentication: cookie
    token:
      jwt:
        enabled: true
        generator:
          access-token-expiration: 720000
        cookie:
          enabled: true
          login-success-target-url: https://${tenant.client-subdomain}.meupagador.com.br/
          login-failure-target-url: https://${tenant.client-subdomain}.meupagador.com.br/falha-ao-autenticar
          cookie-same-site: Lax
          cookie-secure: true
          cookie-domain: .meupagador.com.br
        signatures:
          jwks: [ ]
          secret:
            generator:
              secret: ${jwt.friday.secret}
              jws-algorithm: HS256
  router:
    versioning:
      enabled: true
      default-version: 2
      header:
        enabled: true
        names:
          - 'X-API-VERSION'

modules:
  manual-entry:
    enabled: true
  pfm:
    enabled: true
  chatbot-ai:
    enabled: true
  event-api:
    enabled: false
  openfinance:
    enabled: false
  push-notification:
    enabled: true
  in-app-subscription-coupon:
    enabled: false

application:
  region: us-east-1
  accountNumber: ************

endpoints:
  caches:
    enabled: true
    sensitive: false
  env:
    enabled: true
    sensitive: false

concessionariaDireto:
  financialGateway: CELCOIN

integrations:
  celcoin:
    enabled: false
  jazz:
    host: https://onboarding-dimo.dev.jazztech.com.br
    clientId: "mobile-app"
  cielo:
    host: https://apisandbox.cieloecommerce.cielo.com.br
    checkStatusHost: https://apiquerysandbox.cieloecommerce.cielo.com.br
    credentials:
      MerchantId: ce45e57f-3877-4e92-a938-4871dafb1a1f
      MerchantKey: ZZKJTBGSHQSUDAXEQGIGQAKTYGUPFMYOUUYLHOVB
  bigdatacorp:
    host: https://bigboost.bigdatacorp.com.br
    accessToken: xxx
    peoplePath: /peoplev2
    companyPath: /companies
  arbi:
    host: https://apihml-bancoarbi.sensedia.com
    authHost: https://gaphs.bancoarbi.com.br
    newHost: https://gaphs.bancoarbi.com.br
    newAuthHost: https://gaphs.bancoarbi.com.br
    userToken: KEv7OxEmpjiy2scHdDOTMFaMfCjFV2sp
    clientId: 1553934b-b107-34c1-91e0-a7c2adf45c38 #Postman: 1847c0b1-e9db-3e45-b964-00b08ebabf6f
    clientSecret: 24498b2a-ed8f-30dd-9281-0d57b35a5e93
    contaTitular: "*********0"
    contaLiquidacao: "7140014793"
    inscricao: 34701685000215
    contaCashin: ${integrations.arbi.contaLiquidacao}
    pixCheckoutWaitTime: 100 #miliseconds
    fepweb:
      username: friday.hml
      password: Fri#2023
  cognito:
    userPoolId: ${application.region}_S0DGZEW5J
    jwtCookie:
      userPoolClientId: 3og5ib4dn9av4vdv3u8rqthvtd
  intercom:
    enabled: false
    token: ************************************************************
    webIdVerificationSecret: uEQeg-W3ddKuYirm3LMXpOh-ePWjbSdjXCw0SdcR
    iosIdVerificationSecret: QCKelP1l1XhDIN49NT6X3ju7ECD1xwH8ORfs6FGk
    androidIdVerificationSecret: Q7sS4G6eVmbdYJO42Op-bWcrvV7xW4pRSwp6019Y
  firebase:
    measurementId: "G-BDYBP1PJEG"
  liveness:
    host: "https://liveness.meupagador.com.br"
    user: "LIVENESS_CLIENT_ID-f96298e0-a338-11ed-8b23-4f406dbefac8"
    password: "STG_ENVIRONMENT_PASSWORD"
  modatta:
    host: "https://api-h.developer.btgpactual.com/"
    user: "ac700a4b-dd2f-4c01-a142-0e165fa86d71"
    password: "ftSb6vg6QXXNPEIN0hT0EB2rKatgtq"
  settlement:
    host: https://liquidacao.meupagador.com.br
    username: 77cfdc76-111e-4e8c-82e8-d3acdc013fb5
    password: $2y$19$npO3lIz0ToTa.YZ/xWx07eymYxtSw.Y8QigUc9HaT4uJsewvsLKL2
  btg:
    host: https://api-h.developer.btgpactual.com/
    clientId: fc1dddad-08bd-44ed-a368-6e0e8e22f8b1
    clientSecret: K5GDFBJZFF8VQSWS29SUB64ZDXTMFR5MM161645VWLB97UBD
    redirectUrl: "https://use.meupagador.com.br/web/saldo-adicionado-itp"
  openfinance:
    host: "https://open-finance.meupagador.com.br"
    clientid: OPEN_FINANCE_CLIENT_ID-5daed27f-daf1-49ae-93cd-67195a758064
    clientsecret: OPEN_FINANCE_CLIENT_SECRET-9c87d947-e943-4154-82d9-37286cac2816
    participants:
      - name: mock bank
        shortName: mocked
        id: 0b919e9b-bee0-4549-baa3-bb6d003575ce

urls:
  site: https://${tenant.client-subdomain}.meupagador.com.br
  api: https://${tenant.api-subdomain}.meupagador.com.br

email:
  sender:
    email: <EMAIL>
    display-name: Notificações Via1
  return-path: <EMAIL>
  bucket-unprocessed-emails: stgses-unprocessed-emails-via1
  configuration-set-name: notification_sender_configuration_set
  notification:
    email: <EMAIL>
    display-name: Notificações Via1
  receipt:
    email: <EMAIL>
    display-name: Comprovantes Via1
  newAccountReport:
    recipients: <EMAIL>
  newUpgradeAccount:
    recipients: <EMAIL>
    sensitiveRecipients: <EMAIL>
    subject: "[upgrade de conta está pendente de aprovação interna] - %s"
    message: |
      Um upgrade de conta está pendente de aprovação interna:
      Id: %s
      Nome: %s
      Email: %s
  newAccount:
    pendingInternalApprove:
      recipients: <EMAIL>
      sensitive-recipients: ${email.newAccount.pendingInternalApprove.recipients}
    pendingInternalReview:
      recipients: <EMAIL>
      sensitive-recipients: ${email.newAccount.pendingInternalReview.recipients}
    pendingActivation:
      recipients: <EMAIL>
      sensitive-recipients: ${email.newAccount.pendingActivation.recipients}

celcoin-callback:
  identity: fde714cc-61b8-493e-9086-9e36e17d1c7b
  secret: $2y$17$bkBQLvoS/NrktlZu9jilK.vofvK1Na90wpWIZonmO1g/f9zRW6dX2

revenue-cat-callback:
  identity: c5fcf4ee-966e-446f-b56a-4d9e26373c54
  secret: password

arbi-callback:
  identity: fde714cc-61b8-493e-9086-9e36e17d1c7c
  secret: $2y$17$bkBQLvoS/NrktlZu9jilK.vofvK1Na90wpWIZonmO1g/f9zRW6dX2

modatta-b2b:
  identity: 8da2b063-bff2-43d2-8377-07b00a458c31
  secret: $2y$17$bkBQLvoS/NrktlZu9jilK.vofvK1Na90wpWIZonmO1g/f9zRW6dX2

communication-centre:
  email:
    sender:
      email: <EMAIL>
      display-name: Notificações Via1
    return-path: <EMAIL>
    bucket-unprocessed-emails: stgses-unprocessed-emails-via1
    configuration-set-name: failure_rendering_notification_configuration_set
    notification:
      email: <EMAIL>
      display-name: Notificações Via1
    receipt:
      email: <EMAIL>
      display-name: Comprovantes Via1
    virus:
      bucket: stg-quarantine-emails
    templates:
      local:
        walletSummaryReportPath: "templates/wallet-summary-report.motorola"
  forward:
    configuration-set: failure_rendering_notification_configuration_set
    sender: <EMAIL>
  integration:
    blip:
      host: https://via1-pagamentos-digitais.http.msging.net
      namespace: 1d3afeae_c48c_4c2a_8d65_02b4bbf01f83
      command:
        path: /commands
      message:
        path: /messages

emailDomain: "meupagador.com.br"

accountRegister:
  user_files:
    bucket: stg-user-documents
    modattaUserDocumentsBucket: ************-modatta-user-documents

sqs:
  emailQueueName: stg-incoming-emails
  rollbackTransactionQueueName: bill_payment_rollback_transaction
  walletEventsQueueName: wallet-events-queue-stg
  accountEventsQueueName: account-events-queue-stg
  sqsWaitTime: 20
  dlqArn: arn:aws:sqs:${application.region}:${application.accountNumber}:bill_events_dlq

sns:
  billEventTopicArn: arn:aws:sns:${application.region}:${application.accountNumber}:bill-events
  walletEventTopicArn: arn:aws:sns:${application.region}:${application.accountNumber}:wallet-events
  accountEventTopicArn: arn:aws:sns:${application.region}:${application.accountNumber}:account-events
  sms:
    maxPrice: 20.00

features:
  inAppSubscription: 1
  asyncSettlement: true
  forwardEmailToManualWorkflow: false
  automaticUserRegister: true
  zeroAuthEnabled: false
  creditCardChallenge: true
  credit-card-risk-analisys:
    provider: "clearsale"
  updateScheduleOnAmountLowered: true

receipt:
  bucketRegion: ${application.region}
  bucketName: stg-bill-receipts

creditCard:
  installments:
    fees:
      1: 2.12
      2: 2.57
      3: 2.57
      4: 2.57
      5: 2.57
      6: 2.57
      7: 2.86
      8: 2.86
      9: 2.86
      10: 2.86
      11: 2.86
      12: 2.86

jwt:
  friday:
    audience: "friday.ai"
    issuer: "https://friday.ai"
    duration: "720h"
    secret: "BillPaymentGiganteBillPaymentGigante"

jwtValidation:
  providers:
    msisdn:
      issuer: ${jwt.friday.issuer}
      audience:
        - "phone"
    nonce:
      issuer: ${jwt.friday.issuer}
      audience:
        - "webapp"

settlementFundsTransfer:
  originSettlementBankAccount:
    accountNo: *********
    accountDv: 0
  originCashinBankAccount:
    accountType: CHECKING
    accountNo: *********
    accountDv: 0
    ispb: ********

internalBankService:
  omnibusBankAccount:
    accountNo: 325513

createBillService:
  idSubscriptionBy:
    recipientDocument: **************

subscription:
  amount: 990
  dayOfMonth: 10
  description: "%s"
  recipientName: Assinatura Friday
  recipientDocument: **************
  bankAccount:
    accountType: CHECKING
    bankNo: 213
    routingNo: 1
    accountNo: *********
    accountDv: 2
    ispb: ********

redis:
  #uri: redis://motorola-bill-payment-cache.7sv9r5.0001.use1.cache.amazonaws.com:6379
  uri: redis://bill-payment-cache.friday.internal:6379
  cache:
    value-serializer: io.micronaut.jackson.serialize.JacksonObjectSerializer
  caches:
    balance:
      charset: 'UTF-8'
      expire-after-write: 2m
    find-pix-key-details:
      charset: 'UTF-8'
      expire-after-write: 5m
      expire-after-access: 5m

modatta:
  accounts:
    - ********** # cash in
    - ********** # bolsão
    - ********** # fee modatta

kms:
  openfinance:
    keyid: 43764d7e-efd9-48f7-8fe0-20375978f17e
  hmacKey: UJKvKAPO2aErdBmfI3Vgs7rnKJ9GmEr3P642VCqHw3odGzzKQQ1Wb2s3chozVVQDCvmdghqHmznSenYNQ+y/IQ==

disable:
  export-s3: true
  cognito: true

jackson:
  deserialization:
    fail-on-unknown-properties: false
    read-unknown-enum-values-using-default-value: true

logger:
  levels:
    ROOT: INFO
    ai.friday.billpayment: INFO
    io.micrometer: OFF
    io.micrometer.core.instrument.push.PushMeterRegistry: OFF
    #ai.friday.billpayment.app.job: INFO
    #ai.friday.billpayment.adapters.jobs: INFO
    #ai.friday.billpayment.adapters.messaging: INFO
    #io.micronaut: DEBUG
    #io.micronaut.context: OFF
    #io.micronaut.context.condition: OFF
    #io.micronaut.management: OFF