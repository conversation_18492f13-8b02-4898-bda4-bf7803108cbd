micronaut:
  inject.eager-init: false

application:
  region: "us-east-1"

jwtValidation.providers:
  nonce:
    issuer: "https://friday.ai"
    audience:
      - "webapp"
  msisdn:
    issuer: "https://friday.ai"
    audience:
      - "phone"

disable:
  cognito: true
  export-s3: true

modules:
  manual-entry:
    enabled: true
  pfm:
    enabled: true
  chatbot-ai:
    enabled: false
  openfinance:
    enabled: false
  push-notification:
    enabled: false

sqs:
  sign-up-queue: sign-up-users

mailbox:
  security:
    rule:
      email-allowed-list:
        enabled: false
      email-blocked-list:
        enabled: false
      user-ignored-bill:
        enabled: false

logger:
  levels:
    ROOT: INFO
#    ai.friday.billpayment: DEBUG
#    ai.friday.billpayment.app.job: INFO
#    ai.friday.billpayment.adapters.jobs: INFO
#    ai.friday.billpayment.adapters.messaging: INFO
#    io.micronaut: DEBUG
#    io.micronaut.context: OFF
#    io.micronaut.context.condition: OFF
#    io.micronaut.management: OFF
    io.micrometer: OFF
    io.micrometer.core.instrument.push.PushMeterRegistry: OFF