package ai.friday.billpayment.apps.motorola.adapters.messaging.signup

import ai.friday.billpayment.adapters.messaging.ParallelMessageHandlerConfiguration
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.account.SystemActivityType
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.apps.motorola.app.register.MotorolaCreateAccountCommand
import ai.friday.billpayment.apps.motorola.app.register.MotorolaRegisterService
import io.kotest.matchers.booleans.shouldBeTrue
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

class JazzOnboardingNotificationHandlerTest {

    private val registerService = mockk<MotorolaRegisterService>(relaxed = true)
    private val mockedSqs = mockk<SqsClient>()
    private val mockedConfiguration = mockk<ParallelMessageHandlerConfiguration>(relaxed = true)
    private val systemActivityService = mockk<SystemActivityService>(relaxed = true)

    private val handler = JazzOnboardingNotificationHandler(
        amazonSQS = mockedSqs,
        configuration = mockedConfiguration,
        service = registerService,
        queueName = "jazz-onboarding-test",
        systemActivityService = systemActivityService,
    )

    @Test
    fun `quando chegar uma mensagem de onboarding, deve chamar a criação de usuário se ele estiver elegível`() {
        val document = "***********"

        val message = mockk<Message> {
            every { attributes() } returns emptyMap()
            every { body() } returns """
                {
                    "accountKey": "123",
                    "document": "$document",
                    "documentType": "CPF",
                    "email": "<EMAIL>",
                    "fullName": "Test User",
                    "phone": "*************",
                    "enableAssistant": true,
                    "bankDetails": {
                        "bank": "213",
                        "agency": "0001",
                        "account": "*********"
                    },
                    "device": {
                        "arbiDeviceId": "device123"
                    }
                }
            """.trimIndent()
        }

        handler.handleMessage(message).shouldDeleteMessage.shouldBeTrue()

        verify {
            systemActivityService wasNot Called
            registerService.register(
                MotorolaCreateAccountCommand(
                    email = EmailAddress("<EMAIL>"),
                    phone = MobilePhone("+*************"),
                    name = "Test User",
                    groups = emptyList(),
                    externalId = ExternalId("123", AccountProviderName.MOTOROLA),
                    type = UserAccountType.FULL_ACCOUNT,
                    subscriptionType = SubscriptionType.IN_APP,
                    accountNumber = AccountNumber("*********"),
                    document = Document.sanitized("***********"),
                    activateNotifications = false,
                    ddaAlreadyActive = false,
                    arbiDeviceId = "device123",
                ),
            )
        }
    }

    @Test
    fun `quando chegar uma mensagem de onboarding, deve ignorar o usuário se ele não estiver elegível`() {
        val document = "***********"

        every {
            systemActivityService.getFlag(
                match { document in it.value },
                SystemActivityType.UserEligibleOnPartner,
            )
        } returns false

        val message = mockk<Message> {
            every { attributes() } returns emptyMap()
            every { body() } returns """
                {
                    "accountKey": "123",
                    "document": "$document",
                    "documentType": "CPF",
                    "email": "<EMAIL>",
                    "fullName": "Test User",
                    "phone": "*************",
                    "enableAssistant": false,
                    "bankDetails": {
                        "bank": "213",
                        "agency": "0001",
                        "account": "*********"
                    },
                    "device": {
                        "arbiDeviceId": "device123"
                    }
                }
            """.trimIndent()
        }

        handler.handleMessage(message).shouldDeleteMessage.shouldBeTrue()

        verify {
            registerService wasNot Called
        }
    }

    @Test
    fun `quando chegar uma mensagem de onboarding, deve criar o usuário se ele estiver elegível internamente`() {
        val document = "***********"

        every {
            systemActivityService.getFlag(
                match { document in it.value },
                SystemActivityType.UserEligibleOnPartner,
            )
        } returns true

        val message = mockk<Message> {
            every { attributes() } returns emptyMap()
            every { body() } returns """
                {
                    "accountKey": "123",
                    "document": "$document",
                    "documentType": "CPF",
                    "email": "<EMAIL>",
                    "fullName": "Test User",
                    "phone": "*************",
                    "enableAssistant": false,
                    "bankDetails": {
                        "bank": "213",
                        "agency": "0001",
                        "account": "*********"
                    },
                    "device": {
                        "arbiDeviceId": "device123"
                    }
                }
            """.trimIndent()
        }

        handler.handleMessage(message).shouldDeleteMessage.shouldBeTrue()

        verify {
            registerService.register(any())
            systemActivityService.getFlag(any(), any())
        }
    }
}