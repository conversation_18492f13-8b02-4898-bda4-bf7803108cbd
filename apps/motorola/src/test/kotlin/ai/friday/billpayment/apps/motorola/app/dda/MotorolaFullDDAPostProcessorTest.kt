package ai.friday.billpayment.apps.motorola.app.dda

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SystemActivityDbRepository
import ai.friday.billpayment.adapters.dynamodb.SystemActivityDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.account.SystemActivityType
import ai.friday.billpayment.app.account.toSystemActivityKey
import ai.friday.billpayment.app.dda.DDAProvider
import ai.friday.billpayment.app.dda.DDARegister
import ai.friday.billpayment.app.dda.DDAStatus
import ai.friday.billpayment.app.dda.FullDDAPostProcessorError
import ai.friday.billpayment.app.integrations.RegisterInstrumentationService
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.nulls.beNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNot
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.clearAllMocks
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class MotorolaFullDDAPostProcessorTest {
    @BeforeEach
    fun setup() {
        accountRepository.save(ACCOUNT)

        clearAllMocks()
    }

    @Test
    fun `se não encontrar account deve dar erro`() {
        with(processor.process(ddaRegister.copy(accountId = AccountId()))) {
            isLeft().shouldBeTrue()
            leftOrNull()!!.shouldBeTypeOf<FullDDAPostProcessorError.FindAccountError>()
        }
    }

    @Test
    fun `deve ativar a conta do usuário`() {
        processor.process(ddaRegister).isRight().shouldBeTrue()

        with(accountRepository.findById(accountId)) {
            status shouldBe AccountStatus.ACTIVE
            configuration.receiveDDANotification.shouldBeTrue()
            configuration.receiveNotification.shouldBeTrue()
        }
    }

    @Test
    fun `deve publicar no system activity`() {
        processor.process(ddaRegister).isRight().shouldBeTrue()

        val systemActivity = systemActivityRepository.find(
            accountId.toSystemActivityKey(),
            SystemActivityType.AccountActivated,
        )

        systemActivity shouldNot beNull()
        LocalDateTime.parse(systemActivity!!.value, dateTimeFormat).toLocalDate() shouldBe getLocalDate()
    }

    @Test
    fun `deve chamar a instumentação de ativação`() {
        processor.process(ddaRegister).isRight().shouldBeTrue()

        verify { mockedRegisterInstrumentationService.activated(accountId, RegistrationType.FULL) }
    }

    private companion object Setup {
        private val enhancedClient = DynamoDBUtils.setupDynamoDB()

        private val accountDAO = AccountDynamoDAO(enhancedClient)
        private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
        private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
        private val nsuDAO = NSUDynamoDAO(enhancedClient)
        private val transactionDynamo = TransactionDynamo(enhancedClient)

        private val accountRepository = AccountDbRepository(
            accountDAO = accountDAO,
            partialAccountDAO = partialAccountDAO,
            paymentMethodDAO = paymentMethodDAO,
            nsuDAO = nsuDAO,
            transactionDynamo = transactionDynamo,
        )

        private val systemActivityRepository = SystemActivityDbRepository(
            SystemActivityDynamoDAO(enhancedClient),
        )

        private val systemActivityService = SystemActivityService(systemActivityRepository)

        private val mockedRegisterInstrumentationService = mockk<RegisterInstrumentationService>(relaxUnitFun = true)

        private val accountService = AccountService(
            accountConfigurationService = mockk(),
            accountRepository = accountRepository,
            chatbotMessagePublisher = mockk(),
            crmService = mockk(),
            notificationAdapter = mockk(),
            walletRepository = mockk(),
        )

        private val processor =
            MotorolaFullDDAPostProcessor(
                systemActivityService = systemActivityService,
                registerInstrumentationService = mockedRegisterInstrumentationService,
                accountService = accountService,
            )

        private val accountId = AccountId(ACCOUNT_ID)

        private val ddaRegister = DDARegister(
            accountId = accountId,
            document = DOCUMENT,
            created = getZonedDateTime(),
            status = DDAStatus.ACTIVE,
            lastUpdated = getZonedDateTime(),
            lastSuccessfullExecution = null,
            provider = DDAProvider.ARBI,
            migrated = null,
        )
    }
}