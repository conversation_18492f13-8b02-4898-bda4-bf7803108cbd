package ai.friday.billpayment.apps.motorola.adapters.auth

import ai.friday.billpayment.Wm
import ai.friday.billpayment.Wm.aJsonResponse
import ai.friday.billpayment.apps.motorola.adapters.auth.JazzAuthenticationAdapterTest.Setup.jazzAuthenticationAdapter
import ai.friday.billpayment.apps.motorola.adapters.auth.JazzAuthenticationAdapterTest.Setup.wm
import ai.friday.billpayment.rxClient
import com.github.tomakehurst.wiremock.client.WireMock.equalToJson
import com.github.tomakehurst.wiremock.client.WireMock.post
import io.kotest.matchers.result.shouldBeFailure
import io.kotest.matchers.result.shouldBeSuccess
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode

@Execution(ExecutionMode.CONCURRENT)
class JazzAuthenticationAdapterTest {

    @Test
    fun `should return JWT token on successful nonce exchange`() {
        wm.stubFor(
            post("/onboarding-dimo/v1/auth/nonce-exchange")
                .withRequestBody(equalToJson("""{"oneTimeCode":"nonce","clientId":"friday_client_id"}"""))
                .willReturn(
                    aJsonResponse(
                        status = 200,
                        body = """
                        {
                           "jwtToken": "mockedToken",
                           "expiresIn": 7200,
                           "userId": "123456"
                        }
                        """.trimIndent(),
                    ),
                ),
        )

        jazzAuthenticationAdapter.exchangeToken("nonce").shouldBeSuccess().let { response ->
            response.jwtToken shouldBe "mockedToken"
            response.expiresIn shouldBe 7200
        }
    }

    @Test
    fun `should fail when token cannot be exchanged`() {
        wm.stubFor(
            post("/auth/nonce-exchange")
                .withRequestBody(equalToJson("""{"oneTimeCode":"failnonce","clientId":"friday_client_id"}"""))
                .willReturn(
                    aJsonResponse(
                        status = 500,
                        body = """
                        {
                           "title": "example",
                           "status": 500,
                           "errorCode": 0,
                           "detail": "An unexpected error occurred. Please try again later.",
                           "instance": "/service/transaction/123",
                           "timestamp": "1730407650842"
                           
                        }
                        """.trimIndent(),
                    ),
                ),
        )

        jazzAuthenticationAdapter
            .exchangeToken("failnonce")
            .shouldBeFailure()
            .shouldBeTypeOf<HttpClientResponseException>()
    }

    private object Setup {
        val wm = Wm.mockServer()
        val httpClient = wm.rxClient()

        val jazzConfiguration = mockk<JazzConfiguration> {
            every { clientId } returns "friday_client_id"
            every { host } returns wm.baseUrl()
        }

        val jazzAuthenticationAdapter = JazzAuthenticationAdapter(
            httpClient = httpClient,
            configuration = jazzConfiguration,
        )
    }
}