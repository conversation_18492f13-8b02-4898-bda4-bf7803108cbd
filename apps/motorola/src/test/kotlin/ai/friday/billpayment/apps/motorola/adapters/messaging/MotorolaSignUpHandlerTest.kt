package ai.friday.billpayment.apps.motorola.adapters.messaging

import ai.friday.billpayment.adapters.messaging.ParallelMessageHandlerConfiguration
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.apps.motorola.adapters.messaging.signup.MotorolaSignUpHandler
import ai.friday.billpayment.apps.motorola.app.register.MotorolaCreateAccountCommand
import ai.friday.billpayment.apps.motorola.app.register.MotorolaRegisterService
import ai.friday.billpayment.integration.DOCUMENT
import io.kotest.matchers.booleans.shouldBeTrue
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

class MotorolaSignUpHandlerTest {

    private val registerService = mockk<MotorolaRegisterService>(relaxed = true)

    private val mockedAmazonSQS = mockk<SqsClient>()

    private val mockedConfiguration = mockk<ParallelMessageHandlerConfiguration>(relaxed = true)

    private val handler = MotorolaSignUpHandler(
        amazonSQS = mockedAmazonSQS,
        configuration = mockedConfiguration,
        service = registerService,
        queueName = "sign-up-test",
    )

    @Test
    fun `quando chegar uma mensagem de novo usuário, deve chamar a criação de usuário`() {
        val message = mockk<Message> {
            every { body() } returns """
                {
                    "accountId": "123",
                    "email": "email",
                    "phone": "phone",
                    "name": "name",
                    "externalId": "externalId",
                    "type": "FULL_ACCOUNT",
                    "subscriptionType": "IN_APP",
                    "document": "***********",
                    "fullAccountNumber": "*********",
                    "activateNotifications": false,
                    "ddaAlreadyActive": false,
                    "arbiDeviceId": "device123"
                }
            """.trimIndent()
        }

        handler.handleMessage(message).shouldDeleteMessage.shouldBeTrue()

        verify {
            registerService.register(
                MotorolaCreateAccountCommand(
                    email = EmailAddress("email"),
                    phone = MobilePhone("phone"),
                    name = "name",
                    groups = listOf(AccountGroup.CHATBOT_LEGACY),
                    externalId = ExternalId("externalId", AccountProviderName.MOTOROLA),
                    type = UserAccountType.FULL_ACCOUNT,
                    subscriptionType = SubscriptionType.IN_APP,
                    accountNumber = AccountNumber("*********"),
                    document = Document(DOCUMENT),
                    activateNotifications = false,
                    ddaAlreadyActive = false,
                    arbiDeviceId = "device123",
                ),
            )
        }
    }
}