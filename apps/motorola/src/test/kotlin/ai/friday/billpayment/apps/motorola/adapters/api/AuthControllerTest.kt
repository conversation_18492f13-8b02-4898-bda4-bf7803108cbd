package ai.friday.billpayment.apps.motorola.adapters.api

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.auth.JwtValidator
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.contact.ContactService
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.apps.motorola.MOTOROLA_ENV
import ai.friday.billpayment.apps.motorola.adapters.auth.MotorolaAuthenticationService
import ai.friday.billpayment.apps.motorola.adapters.auth.MotorolaNonceAuthenticationProvider
import ai.friday.billpayment.apps.motorola.adapters.auth.NonceAuthenticationProvider
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.blockingHttpClient
import ai.friday.billpayment.integration.toMember
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.kotest.matchers.string.shouldMatch
import io.micronaut.context.annotation.Primary
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.BlockingHttpClient
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import jakarta.inject.Named
import java.util.UUID
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV, MOTOROLA_ENV])
internal class AuthControllerTest(
    server: EmbeddedServer,
    @Named("nonce") val jwtValidator: JwtValidator,
) {
    private val client: BlockingHttpClient = server.blockingHttpClient()
    private val walletFixture = WalletFixture()

    @get:Primary
    @get:MockBean
    val contactService = mockk<ContactService>()

    @get:Primary
    @get:MockBean
    val accountRepository = mockk<AccountRepository>()

    @get:Primary
    @get:MockBean
    val authenticationService = mockk<MotorolaAuthenticationService>()

    @get:Primary
    @get:MockBean
    val walletService = mockk<WalletService>()

    @get:Primary
    @get:MockBean
    val nonceAuthenticationProvider: NonceAuthenticationProvider = spyk(
        MotorolaNonceAuthenticationProvider(
            jwtValidator,
            accountRepository,
            authenticationService,
        ),
    )

    @Test
    fun `should return a valid token when nounce is a valid developer ACCOUNT and authorize it`() {
        val accountId = AccountId()
        val nonce = accountId.value // ONLY DURING TESTING PHASE, must be removed before project launch

        val account = ACCOUNT.copy(
            accountId = accountId,
            configuration = ACCOUNT.configuration.copy(
                defaultWalletId = WalletId(accountId.value),
                groups = ACCOUNT.configuration.groups + AccountGroup.DEVELOPER,
            ),
        )

        every { accountRepository.findByIdOrNull(accountId) } returns account

        every { walletService.findWalletOrNull(account.defaultWalletId()) } returns walletFixture.buildWallet(
            walletFounder = account.toMember(MemberType.FOUNDER, MemberPermissions.of(MemberType.FOUNDER)),
        )

        // NO AUTHORIZATION
        assertThrows<HttpClientResponseException> {
            val anonymousRequest = HttpRequest.GET<Any>("/recipient").header("x-wallet-id", accountId.value)

            client.exchange(anonymousRequest, Argument.STRING)
        }.status shouldBe HttpStatus.UNAUTHORIZED

        // AUTHENTICATION REQUEST with TEST nonce
        val authRequest = HttpRequest.GET<Any>("/auth/nonce").header("x-nonce", nonce)
        val response = client.exchange(authRequest, Argument.STRING)

        val authCookie = response.cookies["JWT"].shouldNotBeNull()

        response.run {
            status shouldBe HttpStatus.OK
            body() shouldMatch """\{"token":"([^"]+)"\}"""
            body() shouldContain authCookie.value
        }

        every { contactService.getAll(accountId) } returns emptyList()

        // AUTHORIZATION by cookie generated by nonce
        val securedRequest = HttpRequest.GET<Any>("/recipient")
            .cookie(authCookie).header("x-wallet-id", accountId.value)

        client.exchange(securedRequest, Argument.STRING).status shouldBe HttpStatus.OK

        verify {
            authenticationService wasNot Called
        }
    }

    @Test
    fun `should return a valid token when nounce is valid and authorize it`() {
        val nonce = UUID.randomUUID().toString()
        val accountId = AccountId()

        val account = ACCOUNT.copy(
            accountId = accountId,
            configuration = ACCOUNT.configuration.copy(
                defaultWalletId = WalletId(accountId.value),
            ),
        )

        every { walletService.findWalletOrNull(account.defaultWalletId()) } returns walletFixture.buildWallet(
            walletFounder = account.toMember(MemberType.FOUNDER, MemberPermissions.of(MemberType.FOUNDER)),
        )

        every { accountRepository.findByIdOrNull(accountId) } returns account
        every { authenticationService.authenticate(nonce) } returns Result.success(account)

        // AUTHENTICATION REQUEST with valid nonce
        val authRequest = HttpRequest.GET<Any>("/auth/nonce").header("x-nonce", nonce)
        val response = client.exchange(authRequest, Argument.STRING)

        val authCookie = response.cookies["JWT"].shouldNotBeNull()

        response.run {
            status shouldBe HttpStatus.OK
            body() shouldMatch """\{"token":"([^"]+)"\}"""
            body() shouldContain authCookie.value
        }

        every { contactService.getAll(accountId) } returns emptyList()

        // AUTHORIZATION by cookie generated by nonce
        val securedRequest = HttpRequest.GET<Any>("/recipient")
            .cookie(authCookie).header("x-wallet-id", accountId.value)

        client.exchange(securedRequest, Argument.STRING).status shouldBe HttpStatus.OK

        verify {
            authenticationService.authenticate(nonce)
        }
    }
}