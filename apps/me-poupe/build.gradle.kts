group = "ai.friday.billpayment.apps.mepoupe"
version = "0.1"

dependencies {
    implementation(project(":"))
    implementation(project(":modules:automatic-pix"))
    implementation(project(":modules:chatbot-ai"))
    implementation(project(":modules:event-api"))
    implementation(project(":modules:in-app-subscription-coupon"))
    implementation(project(":modules:investment-goals"))
    implementation(project(":modules:manual-entry"))
    implementation(project(":modules:openfinance"))
    implementation(project(":modules:pfm"))
    implementation(project(":modules:push-notification"))

    testImplementation(testFixtures(project(":")))

    testImplementation("io.mockk:mockk-jvm:1.13.8")
    testImplementation("io.micronaut.test:micronaut-test-junit5")
    testImplementation("io.micronaut.test:micronaut-test-kotest5")
    testImplementation("org.junit.jupiter:junit-jupiter")
    testImplementation("org.wiremock:wiremock:3.2.0")
    testImplementation("com.tngtech.archunit:archunit-junit5:1.1.0") // ao invés de 0.23.1
    testImplementation("org.jetbrains.kotlin:kotlin-test-junit")
    testImplementation("org.testcontainers:testcontainers")
    testImplementation("org.awaitility:awaitility-kotlin:4.2.0")
}

java {
    sourceCompatibility = JavaVersion.toVersion("17")
}

micronaut {
    processing {
        incremental(true)
        annotations("ai.friday.billpayment.*")
    }
}