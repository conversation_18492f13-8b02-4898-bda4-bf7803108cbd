package ai.friday.billpayment.apps.mepoupe.adapters.api.auth

import ai.friday.billpayment.adapters.auth.FridayFixedIdentityAuthenticationProvider
import ai.friday.billpayment.apps.mepoupe.app.MePoupe
import io.micronaut.context.annotation.Property

const val VOOMP_CALLBACK = "VOOMP_CALLBACK"

@MePoupe
class VoompCallbackAuthenticationProvider(
    @Property(name = "integrations.voomp.callback.identity") private val identity: String,
    @Property(name = "integrations.voomp.callback.secret") private val secret: String,
) : FridayFixedIdentityAuthenticationProvider(
    identity,
    secret,
    VOOMP_CALLBACK,
)