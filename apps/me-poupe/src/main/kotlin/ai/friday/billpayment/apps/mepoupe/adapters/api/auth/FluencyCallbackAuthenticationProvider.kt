package ai.friday.billpayment.apps.mepoupe.adapters.api.auth

import ai.friday.billpayment.adapters.auth.FridayFixedIdentityAuthenticationProvider
import ai.friday.billpayment.apps.mepoupe.app.MePoupe
import io.micronaut.context.annotation.Property

const val FLUENCY_CALLBACK = "FLUENCY_CALLBACK"

@MePoupe
class FluencyCallbackAuthenticationProvider(
    @Property(name = "integrations.fluency.callback.identity") private val identity: String,
    @Property(name = "integrations.fluency.callback.secret") private val secret: String,
) : FridayFixedIdentityAuthenticationProvider(
    identity,
    secret,
    FLUENCY_CALLBACK,
)