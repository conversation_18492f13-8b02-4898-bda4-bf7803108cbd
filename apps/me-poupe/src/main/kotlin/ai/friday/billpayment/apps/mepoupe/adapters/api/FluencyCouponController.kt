package ai.friday.billpayment.apps.mepoupe.adapters.api

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.fixOrNull
import ai.friday.billpayment.app.integrations.CrmRepository
import ai.friday.billpayment.app.integrations.NotificationService
import ai.friday.billpayment.app.integrations.TemporaryCrmContactMinimal
import ai.friday.billpayment.app.notification.NotificationTemplate
import ai.friday.billpayment.app.notification.NotificationType
import ai.friday.billpayment.app.notification.WhatsappNotification
import ai.friday.billpayment.apps.mepoupe.adapters.api.auth.FLUENCY_CALLBACK
import ai.friday.billpayment.apps.mepoupe.app.MePoupe
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.InAppSubscriptionCouponModule
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCoupon
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCouponExternalId
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCouponRepository
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCouponService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateFormatBR
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.micronaut.http.HttpResponse
import io.micronaut.http.MediaType
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.QueryValue
import io.micronaut.http.multipart.CompletedFileUpload
import io.micronaut.security.annotation.Secured
import java.io.BufferedReader
import java.io.ByteArrayInputStream
import java.time.LocalDate
import java.time.Period
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

private const val FLUENCY_COUPON_PROVIDER = "FLUENCY"

@InAppSubscriptionCouponModule
@Controller("/fluency/coupon")
@Secured(FLUENCY_CALLBACK)
@MePoupe
class FluencyCouponController(
    private val inAppSubscriptionCouponService: InAppSubscriptionCouponService,
    private val inAppSubscriptionCouponRepository: InAppSubscriptionCouponRepository,
    private val crmRepository: CrmRepository,
    private val notificationService: NotificationService,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)
    private val className = this::class.java.simpleName

    @Post("/create")
    fun createCoupon(@Body rawBody: String): HttpResponse<*> {
        val logName = "$className#createCoupon"
        val body = try {
            parseObjectFrom<FluencyCreateCouponRequestTO>(rawBody)
        } catch (e: Exception) {
            logger.error(append("body", rawBody).andAppend("created", false), logName, e)
            return HttpResponse.badRequest("não foi possível fazer o parse do body")
        }
        val markers = append("body", body)

        val couponDuration = Period.ofMonths(6)

        val coupon = inAppSubscriptionCouponService.createCouponAndSendToCRM(
            inAppSubscriptionCouponExternalId = InAppSubscriptionCouponExternalId(provider = FLUENCY_COUPON_PROVIDER, value = body.activationId),
            description = "Cupom de desconto Fluency",
            couponExpiration = getLocalDate().plus(Period.ofDays(90)).plusDays(1),
            subscriptionPeriod = couponDuration,
            emailAddress = EmailAddress(body.email),
            mobilePhone = body.msisdn?.let { MobilePhone(it) },
            externalOfferId = body.offerId,
            internalOfferId = couponDuration.toString(),
            "fluency_external_coupon_created",
        )
            .getOrElse {
                logger.error(markers.andAppend("created", false), logName, it)
                return HttpResponse.serverError("não foi possível criar o cupom")
            }
        logger.info(markers.andAppend("created", true).andAppend("cuponCode", coupon.code.value), logName)
        return HttpResponse.noContent<Unit>()
    }

    @Post("/disable")
    fun disableCoupon(@Body rawBody: String): HttpResponse<*> {
        val logName = "$className#disableCoupon"
        val body = try {
            parseObjectFrom<FluencyDisableCouponRequestTO>(rawBody)
        } catch (e: Exception) {
            logger.error(append("body", rawBody).andAppend("disabled", false), logName, e)
            return HttpResponse.badRequest("não foi possível fazer o parse do body")
        }
        val markers = append("body", body)

        inAppSubscriptionCouponService.cancelCoupon(InAppSubscriptionCouponExternalId(provider = FLUENCY_COUPON_PROVIDER, value = body.activationId)).getOrElse {
            logger.error(markers.andAppend("disabled", false), logName, it)
            return HttpResponse.serverError("não foi possível desativar o cupom")
        }
        logger.info(markers.andAppend("disabled", true), logName)
        return HttpResponse.noContent<Unit>()
    }

    @Post("/resend")
    @Secured(Role.Code.BACKOFFICE)
    fun resendFailed(@Body failedRequests: List<FailedCouponTO>): HttpResponse<*> {
        failedRequests.forEach { failed ->
            inAppSubscriptionCouponService.resendFailed(
                emailAddress = EmailAddress(failed.emailAddress),
                mobilePhone = MobilePhone(failed.mobilePhone),
                inAppSubscriptionCouponExternalId = InAppSubscriptionCouponExternalId(provider = FLUENCY_COUPON_PROVIDER, value = failed.inAppSubscriptionCouponExternalId),
                couponExpiration = LocalDate.parse(failed.couponExpiration, dateFormatBR),
                externalOfferId = failed.externalOfferId,
                internalOfferId = failed.internalOfferId,
            )
        }
        return HttpResponse.noContent<Unit>()
    }

    @Post("/resendWithEmailAndWatsapp{?whatsappTemplate,intercomEvent}", consumes = [MediaType.MULTIPART_FORM_DATA], produces = [MediaType.TEXT_JSON])
    @Secured(Role.Code.BACKOFFICE)
    fun resendWithEmailAndWatsapp(file: CompletedFileUpload, @QueryValue whatsappTemplate: String, @QueryValue intercomEvent: String): HttpResponse<*> {
        val logName = "$className#resendWithEmailAndWatsapp"
        try {
            val byteArray = file.inputStream.use {
                it.readAllBytes()
            }
            val reader = BufferedReader(ByteArrayInputStream(byteArray).reader())
            var processedLines = 0
            var intercomSent = 0
            var whatsappSent = 0

            reader.useLines { lines ->
                lines.forEachIndexed { index, line ->
                    val markers = append("fileName", file.filename)
                    markers.andAppend("fileLineRaw", line).andAppend("fileLineNumber", index + 1)
                    if (index == 0) {
                        logger.info(markers.andAppend("fileHeader", line), logName)
                        return@forEachIndexed
                    }
                    val values = line.split(",")
                    if (values.size >= 6) {
                        val lineData = ResendWithEmailAndWatsappTO(
                            date = values[0],
                            externalId = InAppSubscriptionCouponExternalId(FLUENCY_COUPON_PROVIDER, values[1]),
                            email = EmailAddress(values[2]),
                            offerId = values[3],
                            msisdn = MobilePhone(values[4]).fixOrNull(),
                            created = values[5].toBoolean(),
                        )
                        val coupon = inAppSubscriptionCouponRepository.findByExternalId(lineData.externalId)
                        if (coupon == null) {
                            logger.error(markers.andAppend("context", "coupon not found"), logName)
                            return@forEachIndexed
                        }

                        if (coupon.availableQuantity == 0) {
                            logger.info(markers.andAppend("context", "coupon already redeemed"), logName)
                            return@forEachIndexed
                        }

                        if (!coupon.enabled) {
                            logger.error(markers.andAppend("context", "coupon not enabled"), logName)
                            return@forEachIndexed
                        }

                        if (coupon.expiresAt < getZonedDateTime()) {
                            logger.error(markers.andAppend("context", "coupon expired"), logName)
                            return@forEachIndexed
                        }

                        try {
                            resendIntercom(lineData, coupon, intercomEvent, markers)
                            intercomSent++
                        } catch (e: Exception) {
                            logger.error(markers.andAppend("context", "error sending to intercom"), logName, e)
                            return@forEachIndexed
                        }

                        if (lineData.msisdn != null) {
                            try {
                                notificationService.sendNotification(
                                    account = null,
                                    type = NotificationType.COUPON_MANUAL_RESEND,
                                    billPaymentNotification = WhatsappNotification(
                                        accountId = null,
                                        receiver = lineData.msisdn,
                                        template = NotificationTemplate(whatsappTemplate),
                                        configurationKey = whatsappTemplate,
                                        parameters = listOf(
                                            coupon.code.value,
                                        ),
                                        quickReplyButtonsWhatsAppParameter = emptyList(),
                                        buttonWhatsAppParameter = null,
                                    ),
                                )
                                whatsappSent++
                                markers.andAppend("whatsappSent", "true")
                            } catch (e: Exception) {
                                markers.andAppend("whatsappSent", "false")
                                logger.error(markers.andAppend("context", "error sending to whatsapp"), logName, e)
                                return@forEachIndexed
                            }
                        } else {
                            markers.andAppend("whatsappSent", "false")
                        }

                        markers.andAppend("fileLineParsed", lineData)
                        logger.info(markers, logName)
                        processedLines++
                    } else {
                        logger.error(markers.andAppend("fileLineSizeError", values.size), logName)
                    }
                }
            }
            logger.info(append("status", "FINISHED").andAppend("processedLines", processedLines), logName)
            return HttpResponse.ok(
                ResendWithEmailAndWatsappResponseTO(
                    processedLines = processedLines,
                    intercomSent = intercomSent,
                    whatsappSent = whatsappSent,
                ),
            )
        } catch (e: Exception) {
            logger.error(append("fileName", file.filename).andAppend("status", "ERROR"), logName, e)
            throw e
        }
    }

    private fun resendIntercom(data: ResendWithEmailAndWatsappTO, coupon: InAppSubscriptionCoupon, eventName: String, markers: LogstashMarker) {
        val emailAddress = data.email

        val crmContact = TemporaryCrmContactMinimal(
            emailAddress = emailAddress,
            mobilePhone = data.msisdn,
        )

        val customParams = mapOf(
            "coupon_code" to coupon.code.value,
            "external_id" to coupon.externalId!!.value,
            "end_date" to coupon.expiresAt.format(dateFormatBR),
            "end_date_iso" to coupon.expiresAt.format(dateFormat),
            "external_offer_id" to data.offerId,
            "internal_offer_id" to Period.ofMonths(3).toString(),
        )
        markers.andAppend("customParams", customParams)

        inAppSubscriptionCouponService.createContact(crmContact)
        markers.andAppend("intercomContactCreated", "true")

        crmRepository.publishEvent(
            emailAddress = emailAddress,
            eventName = eventName,
            customParams = customParams,
        )
        markers.andAppend("intercomEventPublished", "true")
    }
}

data class ResendWithEmailAndWatsappRequestTO(
    val whatsappTemplate: String,
    val intercomEvent: String,
)

data class ResendWithEmailAndWatsappTO(
    val date: String,
    val externalId: InAppSubscriptionCouponExternalId,
    val email: EmailAddress,
    val offerId: String,
    val msisdn: MobilePhone?,
    val created: Boolean,
)

data class FailedCouponTO(
    val emailAddress: String,
    val mobilePhone: String,
    val inAppSubscriptionCouponExternalId: String,
    val couponExpiration: String,
    val externalOfferId: String,
    val internalOfferId: String,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class FluencyCreateCouponRequestTO(
    val email: String,
    val startDate: LocalDate,
    val activationId: String,
    val offerId: String,
    val msisdn: String?,
    val endDate: LocalDate,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class FluencyDisableCouponRequestTO(
    val activationId: String,
)

data class ResendWithEmailAndWatsappResponseTO(
    val processedLines: Int,
    val intercomSent: Int,
    val whatsappSent: Int,
)