package ai.friday.billpayment.apps.mepoupe.app.pfm

import ai.friday.billpayment.app.pfm.DefaultWalletBillCategory
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.apps.mepoupe.app.MePoupe
import ai.friday.billpayment.modules.pfm.app.PFMDefaultCategoriesSeederService
import jakarta.inject.Singleton

@MePoupe
@Singleton
open class MePoupePFMDefaultCategoriesSeederService : PFMDefaultCategoriesSeederService {
    override fun listDefaultCategories(): List<DefaultWalletBillCategory> {
        return defaultPFMCategories
    }
}

val defaultPFMCategories = listOf(
    DefaultWalletBillCategory(
        categoryId = PFMCategoryId("ESSENTIAL_EXPENSE"),
        name = "Essencial",
        icon = "ESSENTIAL_EXPENSE",
    ),
    DefaultWalletBillCategory(
        categoryId = PFMCategoryId("NON_ESSENTIAL_EXPENSE"),
        name = "Não essencial",
        icon = "NON_ESSENTIAL_EXPENSE",
    ),
    DefaultWalletBillCategory(
        categoryId = PFMCategoryId("GOALS_EXPENSE"),
        name = "Investimento",
        icon = "GOALS_EXPENSE",
    ),
    DefaultWalletBillCategory(
        categoryId = PFMCategoryId("PERSONAL_IMPROVEMENT_EXPENSE"),
        name = "Crescimento Pessoal",
        icon = "PERSONAL_IMPROVEMENT_EXPENSE",
    ),
    DefaultWalletBillCategory(
        categoryId = PFMCategoryId("FIXED_INCOME"),
        name = "Ganho fixo",
        icon = "FIXED_INCOME",
    ),
    DefaultWalletBillCategory(
        categoryId = PFMCategoryId("EXTRA_INCOME"),
        name = "Renda Extra",
        icon = "EXTRA_INCOME",
    ),
    DefaultWalletBillCategory(
        categoryId = PFMCategoryId("INVESTMENT_INCOME"),
        name = "Rendimento",
        icon = "INVESTMENT_INCOME",
    ),
)