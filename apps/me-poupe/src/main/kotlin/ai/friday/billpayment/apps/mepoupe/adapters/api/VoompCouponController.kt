package ai.friday.billpayment.apps.mepoupe.adapters.api

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.sanitizeDocumentNumber
import ai.friday.billpayment.apps.mepoupe.app.MePoupe
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.InAppSubscriptionCouponModule
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCouponExternalId
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCouponRepository
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCouponService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonValue
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.micronaut.context.annotation.Property
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.QueryValue
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import java.time.LocalDate
import java.time.Period
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

private const val VOOMP_COUPON_PROVIDER = "VOOMP"
private const val VOOMP_COUPON_DURATION = 12

@InAppSubscriptionCouponModule
@Controller("/voomp/coupon")
@MePoupe
class VoompCouponController(
    private val inAppSubscriptionCouponService: InAppSubscriptionCouponService,
    private val inAppSubscriptionCouponRepository: InAppSubscriptionCouponRepository,
    @Property(name = "integrations.voomp.callback.identity") private val identity: String,
    @Property(name = "integrations.voomp.callback.secret") private val secret: String,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)
    private val className = this::class.java.simpleName

    @Post("/create")
    @Secured(BACKOFFICE)
    fun createCoupon(@Body rawBody: String): HttpResponse<*> {
        val logName = "$className#createCoupon"
        val body = try {
            parseObjectFrom<VoompCreateCouponRequestTO2>(rawBody)
        } catch (e: Exception) {
            logger.error(append("body", rawBody).andAppend("created", false), logName, e)
            return HttpResponse.badRequest("não foi possível fazer o parse do body")
        }
        val markers = append("body", body)

        val couponDuration = Period.ofMonths(VOOMP_COUPON_DURATION)

        val parsedDocument = body.cpf.sanitizeDocumentNumber()

        val externalId = "$parsedDocument#${body.contractId}"

        val coupon = inAppSubscriptionCouponService.createCouponAndSendToCRM(
            inAppSubscriptionCouponExternalId = InAppSubscriptionCouponExternalId(provider = VOOMP_COUPON_PROVIDER, value = externalId),
            description = "Cupom de desconto Voomp",
            couponExpiration = getLocalDate().plus(Period.ofDays(90)).plusDays(1),
            subscriptionPeriod = couponDuration,
            emailAddress = EmailAddress(body.email),
            mobilePhone = body.msisdn?.let { MobilePhone(it) },
            externalOfferId = body.productId,
            internalOfferId = couponDuration.toString(),
            providerEvent = "voomp_external_coupon_created",
            extraParams = mapOf(
                "document" to parsedDocument,
                "name" to body.name,
                "email" to body.email,
            ),
        ).getOrElse {
            logger.error(markers.andAppend("created", false), logName, it)
            return HttpResponse.serverError("não foi possível criar o cupom")
        }
        logger.info(markers.andAppend("created", true).andAppend("cuponCode", coupon.code.value), logName)
        return HttpResponse.noContent<Unit>()
    }

    @Secured(SecurityRule.IS_ANONYMOUS)
    @Post("/webhook{?user,password}")
    fun createCoupon(
        @Body rawBody: String,
        @QueryValue user: String?,
        @QueryValue password: String?,
    ): HttpResponse<*> {
        val logName = "$className#webhook"

        if (user != identity || password != secret) {
            logger.error(append("credential", "invalid"), logName)

            return HttpResponse.badRequest("autenticação inválida")
        }

        val body = try {
            parseObjectFrom<VoompCreateCouponRequestTO>(rawBody)
        } catch (e: Exception) {
            logger.error(append("body", rawBody).andAppend("created", false), logName, e)
            return HttpResponse.badRequest("não foi possível fazer o parse do body")
        }

        val markers = append("body", body)
        val currentStatusValue = CurrentStatus.fromValue(body.currentStatus)

        val errorMessage = if (body.type != EventType.CONTRACT) {
            "não foi possível criar o cupom, o event não é um contrato"
        } else {
            when (currentStatusValue) {
                CurrentStatus.PAID, CurrentStatus.CANCELED, CurrentStatus.ENDED -> null
                CurrentStatus.PENDING_PAYMENT, CurrentStatus.UNPAID, CurrentStatus.TRIALING -> "não foi possível criar o cupom, o status não deve ser '${body.currentStatus}'"
            }
        }

        errorMessage?.let {
            logger.error(markers.andAppend("validation", false), logName, it)
            return HttpResponse.badRequest(it)
        }

        val parsedDocument = when {
            !body.client.document.isNullOrBlank() -> body.client.document.sanitizeDocumentNumber()
            !body.client.cpfCnpj.isNullOrBlank() -> body.client.cpfCnpj.sanitizeDocumentNumber()
            else -> {
                logger.error(markers.andAppend("client.document", "null"), logName)

                return HttpResponse.badRequest("não foi possível encontrar o documento do cliente")
            }
        }

        val externalId = "$parsedDocument#${body.contract.id}"
        val inAppSubscriptionCoupon = inAppSubscriptionCouponRepository.findByExternalId(InAppSubscriptionCouponExternalId(provider = VOOMP_COUPON_PROVIDER, value = externalId))

        if (
            currentStatusValue == CurrentStatus.CANCELED ||
            currentStatusValue == CurrentStatus.ENDED
        ) {
            if (inAppSubscriptionCoupon != null) {
                inAppSubscriptionCouponService.cancelCoupon(InAppSubscriptionCouponExternalId(provider = VOOMP_COUPON_PROVIDER, value = externalId))
            }
            logger.info(markers.andAppend("$currentStatusValue", true).andAppend("externalId", externalId), logName)

            return HttpResponse.noContent<Unit>()
        }

        val couponDuration = Period.ofMonths(VOOMP_COUPON_DURATION)

        val dueDate = body.sale.coupon?.dueDate
            ?.let { LocalDate.parse(it, dateFormat) }
            ?: getLocalDate().plus(Period.ofDays(90)).plusDays(1)

        val coupon = inAppSubscriptionCouponService.createCouponAndSendToCRM(
            inAppSubscriptionCouponExternalId = InAppSubscriptionCouponExternalId(provider = VOOMP_COUPON_PROVIDER, value = externalId),
            description = "Cupom de desconto Voomp",
            couponExpiration = dueDate,
            subscriptionPeriod = couponDuration,
            emailAddress = EmailAddress(body.client.email),
            mobilePhone = body.client.cellphone.let { MobilePhone(it) },
            externalOfferId = body.product.id.toString(),
            internalOfferId = couponDuration.toString(),
            providerEvent = "voomp_external_coupon_created",
            extraParams = mapOf(
                "document" to parsedDocument,
                "name" to body.client.name,
                "email" to body.client.email,
            ),
        )
            .getOrElse {
                logger.error(markers.andAppend("created", false), logName, it)
                return HttpResponse.serverError("não foi possível criar o cupom")
            }
        logger.info(markers.andAppend("created", true).andAppend("cuponCode", coupon.code.value), logName)
        return HttpResponse.noContent<Unit>()
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class VoompCreateCouponRequestTO(
    @JsonAlias("currentStatus", "current_status")
    val currentStatus: String,
    val type: EventType,
    val event: String?,
    val product: ProductDTO,

    @JsonAlias("sale", "currentSale")
    val sale: SaleDTO,
    val client: ClientDTO,
    val contract: ContractTO,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class ProductDTO(
    val id: Int,
    val name: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class SaleDTO(
    val status: String,
    val id: Int,
    val coupon: CouponDTO? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class CouponDTO(
    val id: Int,
    val name: String?,
    val type: String?,
    val limit: Int?,
    val dueDate: String?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class ClientDTO(
    val name: String,
    val cellphone: String,
    val email: String,
    val cpfCnpj: String?,
    val document: String?,
    val id: Int,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class ContractTO(
    val id: Int,
)

enum class EventType(val value: String) {
    SALE("sale"),
    CONTRACT("contract"),
    ;

    @JsonValue
    override fun toString(): String = value

    companion object {
        @JsonCreator
        @JvmStatic
        fun fromValue(value: String): EventType {
            val normalizedValue = value.lowercase()
            return entries.find { it.value == normalizedValue }
                ?: throw IllegalArgumentException("type desconhecido: $value")
        }
    }
}

enum class CurrentStatus(val value: String) {
    // São de sale
    // CREATED("created"),
    // PAID("paid"),
    // WAITING_PAYMENT("waiting_payment"),
    // REFUSED("refused"),
    // REFUNDED("refunded"),
    // CHARGEBACK("chargedback"),

    // São de contract (inicialmente iremos usar somente esse)
    PAID("paid"),
    ENDED("ended"),
    TRIALING("trialing"),
    PENDING_PAYMENT("pendingPayment"),
    UNPAID("unpaid"),
    CANCELED("canceled"),
    ;

    @JsonValue
    override fun toString(): String = value

    companion object {
        @JsonCreator
        @JvmStatic
        fun fromValue(value: String): CurrentStatus {
            return entries.find { it.value.equals(value, ignoreCase = true) }
                ?: throw IllegalArgumentException("Status desconhecido: $value")
        }
    }
}

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class VoompCreateCouponRequestTO2(
    val name: String,
    val email: String,
    val cpf: String,
    val msisdn: String?,
    val startDate: LocalDate,
    val productId: String,
    val contractId: String,
)