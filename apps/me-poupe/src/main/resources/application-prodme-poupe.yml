application:
  region: us-east-1
  accountNumber: ************

micronaut:
  metrics:
    tags:
      tags:
        application: "${tenant.name}-${micronaut.application.name}"
        provider: ${tenant.name}
        env: ${tenant.env}
  http:
    services:
      arbi:
        url: https://gapp.bancoarbi.com.br
        read-timeout: 60s
        pool:
          max-connections: 30
          enabled: true
        ssl:
          enabled: true
          isInsecureTrustAllCertificates: true
      celcoin:
        url: ${configuration.production.celcoin.url}
        read-timeout: 30s
        pool:
          max-connections: 30
          enabled: true
        ssl:
          enabled: true
          client-authentication: NEED
          key-store:
            path: classpath:ssl/celcoin-mtls.p12
            type: PKCS12
            password: FROM_SECRETS
      investment-manager:
        url: ${integrations.investment-manager.host}
        read-timeout: 60s
        pool:
          max-connections: 15
          enabled: true

redis:
  uri: "redis://me-poupe-bill-payment-cache.9d99iz.0001.use1.cache.amazonaws.com:6379"
  cache:
    value-serializer: io.micronaut.jackson.serialize.JacksonObjectSerializer
  caches:
    balance:
      charset: 'UTF-8'
      expire-after-write: 2m
      expire-after-access: 2m
    find-pix-key-details:
      charset: 'UTF-8'
      expire-after-write: 5m
      expire-after-access: 5m


ALLOWED_ORIGINS_REGEX: '^https:\/\/use.mepoupe\.app(|.)$'
LOGIN_SUCCESS_TARGET_URL: "https://use.mepoupe.app/"
LOGIN_FAILURE_TARGET_URL: "https://use.mepoupe.app/falha-ao-autenticar"
JWKS_COGNITO_ME_POUPE_URL: 'https://cognito-idp.${application.region}.amazonaws.com/${integrations.cognito.userPoolId}/.well-known/jwks.json'
COOKIE_DOMAIN: '.mepoupe.app'

subscription:
  amount: 2490
  dayOfMonth: 10
  description: "%s"
  recipientName: Assinatura Me Poupe!
  # Rever os dados abaixo
  recipientDocument: **************
  bankAccount:
    accountType: CHECKING
    bankNo: 213
    routingNo: 1
    accountNo: 38884
    accountDv: 7
    ispb: ********

# Rever esses dados
msisdn-authentication:
  jwt:
    audience: use-me-poupe-contas.mepoupe.app
    issuer: https://use-me-poupe-contas.mepoupe.app
    duration: 48h
    secret: ${micronaut.security.token.jwt.signatures.secret.generator.secret}

jwtValidation:
  providers:
    msisdn:
      audience:
        - ${msisdn-authentication.jwt.audience}
      issuer: ${msisdn-authentication.jwt.issuer}


ARBI_CONTA_LIQUIDACAO: "38750"
ARBI_CONTA_LIQUIDACAO_DV: "6"
ARBI_CONTA_LIQUIDACAO_ZEROES: "0000"

integrations:
  firebase:
    measurementId: "G-0TVQ876DGP"
  arbi:
    contaTitular: ${ARBI_CONTA_LIQUIDACAO_ZEROES}${ARBI_CONTA_LIQUIDACAO}${ARBI_CONTA_LIQUIDACAO_DV}
    contaLiquidacao: ${ARBI_CONTA_LIQUIDACAO_ZEROES}${ARBI_CONTA_LIQUIDACAO}${ARBI_CONTA_LIQUIDACAO_DV}
    inscricao: ${CNPJ_FRIDAY_ME_POUPE}
  cognito:
    userPoolId: ${application.region}_0VDqoRic1
    jwtCookie:
      userPoolClientId: 1p9240j3f77u0ovb3ius7ispr8
  investment-manager:
    host: "https://investment-api.mepoupe.app"
    clientId: "FROM_SECRETS"
    clientSecret: "FROM_SECRETS"
  fluency:
    callback:
      identity: "FROM_SECRETS"
      secret: "FROM_SECRETS"
  voomp:
    callback:
      identity: "FROM_SECRETS"
      secret: "FROM_SECRETS"
  openfinance:
    host: "https://open-finance.mepoupe.app"
    clientid: OPEN_FINANCE_CLIENT_ID-74989ee0-d34a-11ef-9c8c-5b306bd7a337
    clientsecret: OPEN_FINANCE_mepoupe_pwd_prod_!@#_to_be_replaced

internalBankService:
  omnibusBankAccount:
    accountNo: 3204097
    document: ${CNPJ_FRIDAY_ME_POUPE}
    name: Friday Instituição de pagamentos Ltda

settlementFundsTransfer:
  payerName: Me Poupe Pagamentos Digitais LTDA
  payerDocument: ${CNPJ_FRIDAY_ME_POUPE}
  recipientName: Friday Pagamentos Digitais LTDA
  recipientDocument: **************
  description: pagamento de conta
  fraudFundsAccount: 3201012 # precisamos da conta da me poupe. Por enquanto está a da friday
  originSettlementBankAccount:
    accountType: CHECKING
    bankNo: 213
    routingNo: 1
    accountNo: ${ARBI_CONTA_LIQUIDACAO}
    accountDv: ${ARBI_CONTA_LIQUIDACAO_DV}
  originCashinBankAccount:
    accountType: CHECKING
    bankNo: 213
    routingNo: 1
    accountNo: 0
    accountDv: 0
    ispb: ********
  recipientBankAccount:
    accountType: CHECKING
    routingNo: 1
    accountNo: ********
    accountDv: 2
    ispb: ********

features:
  userPilotEnabled: true
  updateScheduleOnAmountLowered: true
  updateAmountAfterPaymentWindow: true
  filterAccountsThatReceiveNotification: true

export-s3:
  bucket: me-poupe-dynamodb-exports
  prefix: "billpayment"
  tableArn: "arn:aws:dynamodb:us-east-1:************:table/Via1-BillPayment"

export-events-s3:
  bucket: me-poupe-dynamodb-exports
  prefix: "userevents"
  tableArn: "arn:aws:dynamodb:us-east-1:************:table/Via1-UserEvents"


export-openfinancedata-s3:
  bucket: me-poupe-dynamodb-exports
  prefix: "openfinancedata"
  tableArn: "arn:aws:dynamodb:us-east-1:************:table/Friday-OpenFinanceData"

