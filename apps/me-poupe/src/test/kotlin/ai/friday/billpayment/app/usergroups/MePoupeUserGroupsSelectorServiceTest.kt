package ai.friday.billpayment.app.usergroups

import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.shouldBe
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

@MicronautTest(environments = [ME_POUPE_ENV, "test"])
class MePoupeUserGroupsSelectorServiceTest {
    private val userGroupsSelectorService = UserGroupsSelectorService(
        listOf(
            AccountGroupSelectorProperties(
                name = "register",
                enabled = true,
                beginDate = LocalDate.of(2025, 4, 11),
                endDate = LocalDate.of(2025, 5, 7),
                groups = listOf("INVESTMENT_CAMPAIGN"),
            ),
        ),
    )

    @ParameterizedTest
    @CsvSource(
        "2024-01-01",
        "2026-01-01",
    )
    fun `deve retornar vazio quando não está na data correta`(date: String) {
        withGivenDateTime(ZonedDateTime.of(LocalDate.parse(date, DateTimeFormatter.ISO_LOCAL_DATE), LocalTime.NOON, brazilTimeZone)) {
            val groups = userGroupsSelectorService.selectGroups(AccountGroupSelectorType.REGISTER)
            groups.size shouldBe 0
        }
    }

    @Test
    fun `deve retorar o grupo correto quando esta na data ativa`() {
        withGivenDateTime(ZonedDateTime.of(LocalDate.of(2025, 4, 11), LocalTime.NOON, brazilTimeZone)) {
            val groups = userGroupsSelectorService.selectGroups(AccountGroupSelectorType.REGISTER)
            groups.size shouldBe 1
            groups.single() shouldBe AccountGroup.INVESTMENT_CAMPAIGN
        }
    }
}