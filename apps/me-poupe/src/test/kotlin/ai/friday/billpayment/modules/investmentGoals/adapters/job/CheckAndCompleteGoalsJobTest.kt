package ai.friday.billpayment.modules.investmentGoals.adapters.job

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryId
import ai.friday.billpayment.modules.investmentGoals.app.goal.Goal
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalInstallments
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalRepository
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalStatus
import ai.friday.billpayment.modules.investmentGoals.app.goal.InstallmentFrequency
import ai.friday.billpayment.modules.investmentGoals.app.messaging.GoalMessagePublisher
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductId
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductLiquidity
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

class CheckAndCompleteGoalsJobTest {

    private val goalMessagePublisher = mockk<GoalMessagePublisher>(relaxUnitFun = true)
    private val goalRepository = mockk<GoalRepository>()
    private val checkAndCompleteGoalsJob = CheckAndCompleteGoalsJob(
        goalRepository = goalRepository,
        goalMessagePublisher = goalMessagePublisher,
        cron = "0 0 0 1 * ?",
    )

    @Test
    fun `deve pulbicar na fila de verificação de meta completa com valor false`() {
        every {
            goalRepository.findGoalsCloseToCompletion()
        } returns listOf(
            Goal(
                id = GoalId("1"),
                accountId = AccountId("1"),
                walletId = WalletId("1"),
                categoryId = GoalCategoryId("1"),
                name = "Meta 1",
                status = GoalStatus.ACTIVE,
                amount = 1000,
                endDate = getLocalDate(),
                liquidity = GoalProductLiquidity.MATURITY,
                installments = GoalInstallments.buildGoalInstallment(InstallmentFrequency.MONTHLY, amount = 100_00, "20"),
                productId = GoalProductId("1"),
                imageUrl = "image",
                createdAt = getZonedDateTime(),
                updatedAt = getZonedDateTime(),
                installmentInitialAmount = 0,
                installmentOptimizedAmount = null,
                lastValueOptimizationAt = null,
                lastKnownNetBalanceAmount = 0,
                lastKnownNetBalanceAt = getZonedDateTime(),
            ),
        )

        checkAndCompleteGoalsJob.execute()

        verify {
            goalMessagePublisher.publishCheckGoalCompletion(any(), false)
        }
    }
}