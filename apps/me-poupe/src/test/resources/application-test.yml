application:
  region: us-east-1
  accountNumber: **************

micronaut:
  metrics:
    enabled: true
  caches:
    wallet:
      charset: 'UTF-8'
      expire-after-write: 0s
      maximum-size: 0
  otel:
    enabled: false
  server:
    ssl:
      enabled: false
    netty:
      listeners:
        httpsListener:
          port: -1
          ssl: false
    port: -1
    max-request-size: 6291456 #1024L*1024*6=>6MB
    multipart:
      enabled: true
      disk: false
      mixed: false
      max-file-size: 6291456 #6291456
  application:
    name: bill-payment-service
  security:
    enabled: true
    endpoints:
      login:
        enabled: true
    token:
      cookie:
        enabled: true
      bearer:
        enabled: false
      jwt:
        enabled: true
        cookie:
          secure: true
        signatures:
          jwks:
            aggregator:
              url: 'https://www.googleapis.com/oauth2/v3/certs'
          secret:
            generator:
              secret: PleaseChangeThisSecretForANewOne
  http:
    client:
      read-timeout: 15s

modules:
  manual-entry:
    enabled: true
  pfm:
    enabled: true
  chatbot-ai:
    enabled: true

msisdn-authentication:
  jwt:
    audience: mepoupe.app
    issuer: https://www.mepoupe.app
    duration: 12h
    secret: ${micronaut.security.token.jwt.signatures.secret.generator.secret}

jwtValidation:
  providers:
    msisdn:
      audience:
        - ${msisdn-authentication.jwt.audience}
      issuer: ${msisdn-authentication.jwt.issuer}

ALLOWED_ORIGINS_REGEX: '*'
LOGIN_SUCCESS_TARGET_URL: "https://use-me-poupe-contas.meupagador.com.br/"
LOGIN_FAILURE_TARGET_URL: "https://use-me-poupe-contas.meupagador.com.br/falha-ao-autenticar"
JWKS_COGNITO_ME_POUPE_URL: 'https://cognito-idp.${application.region}.amazonaws.com/${integrations.cognito.userPoolId}/.well-known/jwks.json'
COOKIE_DOMAIN: '.meupagador.com.br'

ME_POUPE_API_CLIENT_ID: CLIENT_ID
ME_POUPE_API_CLIENT_SECRET: CLIENT_SECRET

chatbotAI-auth:
  secret: CHATBOT_AI_SECRET