tenant:
  id: GIGU
  name: gigu
  displayName: Gigu
  domain: via1.app
  mail-domain: "gigu.com.br"
  citation: "a Gigu"

friday-callback:
  identity: ${integrations.settlement.username}
  secret: ${integrations.settlement.password}

arbi-callback:
  identity: fde714cc-61b8-493e-9086-9e36e17d1c7c
  secret: FROM_AWS_SECRETS

modules:
  manual-entry.enabled: true
  pfm.enabled: true
  chatbot-ai.enabled: true
  event-api.enabled: true
  openfinance.enabled: false
  push-notification.enabled: true
  in-app-subscription-coupon.enabled: false

application:
  region: us-east-1
  accountNumber: ************

email:
  domain: "@meupagador.com.br"
  finance-report-recipients: ""
  newAccountReport.recipients: <EMAIL>
  newUpgradeAccount:
    recipients: <EMAIL>
    sensitiveRecipients: <EMAIL>
  newAccount:
    pendingInternalApprove.recipients: <EMAIL>
    pendingInternalReview.recipients: <EMAIL>
    pendingActivation.recipients: <EMAIL>

features:
  createTopic: true
  vehicleDebts: false
  sendEmailReceipt: false
  eventBus:
    enabled: true
    publisher.sns.enabled: true
  inAppSubscription: 0
  asyncSettlement: true
  forwardEmailToManualWorkflow: false
  automaticUserRegister: false
  zeroAuthEnabled: false
  creditCardChallenge: true
  updateScheduleOnAmountLowered: true
  pinCode.enabled: false
  credit-card.provider: "software-express"
  credit-card-risk-analisys.provider: "clearsale"
  imageReceipt: true

creditCard:
  installments:
    fees:
      1: 2.12
      2: 2.57
      3: 2.57
      4: 2.57
      5: 2.57
      6: 2.57
      7: 2.86
      8: 2.86
      9: 2.86
      10: 2.86
      11: 2.86
      12: 2.86

# Só pra não quebrar Subscription deveria se desativado
subscription:
  amount: 0
  dayOfMonth: 10
  recipientDocument: **************
  bankAccount:
    accountType: CHECKING
    bankNo: 0
    routingNo: 0
    accountNo: 0
    accountDv: 0
    ispb: 0

LOGIN_SUCCESS_TARGET_URL: "https://use-gigu.via1.app/"
LOGIN_FAILURE_TARGET_URL: "https://use-gigu.via1.app/falha-ao-autenticar"
JWKS_COGNITO_GIGU_URL: 'https://cognito-idp.${application.region}.amazonaws.com/${integrations.cognito.userPoolId}/.well-known/jwks.json'
COOKIE_DOMAIN: '.mepoupe.app'

jwt:
  friday:
    audience: "friday.ai"
    issuer: "https://friday.ai"
    duration: "720h"
    secret: "BillPaymentGiganteBillPaymentGigante"

msisdn-authentication:
  jwt:
    audience: "friday.ai"
    issuer: "https://friday.ai"
    duration: 48h
    secret: "BillPaymentGiganteBillPaymentGigante"

jwtValidation:
  providers:
    msisdn:
      audience:
        - ${msisdn-authentication.jwt.audience}
      issuer: ${msisdn-authentication.jwt.issuer}

disable.export-s3: true

integrations:
  clearsale:
    trustedCodes:
      - CRT0004
      - CRT0005
  softwareExpress:
    url: https://esitef.softwareexpress.com.br
  celcoin:
    enabled: false
  bigdatacorp:
    host: https://bigboost.bigdatacorp.com.br
    accessToken: xxx
    peoplePath: /peoplev2
    companyPath: /companies
  arbi:
    authHost: ${arbi.host}
    newHost: ${arbi.host}
    newAuthHost: ${arbi.host}
    contaTitular: "**********" # CONTA DDA
    contaLiquidacao: "**********"
    inscricao: **************
    contaCashin: "**********" # DEFINIR CONTA CASHIN
    pixCheckoutWaitTime: 100 #miliseconds
    fepweb:
      username: friday.hml
      password: Fri#2023
  cognito:
    userPoolId: ${application.region}_CojOrzW7e
    jwtCookie:
      userPoolClientId: 6mnjfvonu9lofa2sacodv5sd8m
  intercom:
    enabled: false
    token: XXX
    webIdVerificationSecret: XXX
    iosIdVerificationSecret: XXX
    androidIdVerificationSecret: XXX
  firebase:
    measurementId: "G-MYD87JFPG2"
  liveness:
    host: "https://liveness.friday.ai"
    user: XXX
    password: XXX
  settlement:
    host: https://liquidacao.friday.ai
    username: 24cbcc7f-4408-4d12-b18d-************
    password: $2y$19$npO3lIz0ToTa.YZ/xWx07eymYxtSw.Y8QigUc9HaT4uJsewvsLLLL
    requestProtocol: HTTP
  chatbotai:
    sendMessageProtocol: HTTP

settlementFundsTransfer:
  payerName: Stopclub Tecnologia, Solucoes e Servicos LTDA
  payerDocument: "**************"
  recipientName: Friday Pagamentos Digitais LTDA
  recipientDocument: **************
  description: pagamento de conta
  fraudFundsAccount: 3201012 # precisamos da conta da me poupe. Por enquanto está a da friday
  originSettlementBankAccount:
    accountType: CHECKING
    bankNo: 213
    routingNo: 1
    accountNo: 320559
    accountDv: 0
  originCashinBankAccount:
    accountType: CHECKING
    bankNo: 213
    routingNo: 1
    accountNo: 320559
    accountDv: 0
    ispb: ********
  recipientBankAccount: # é o fluxo da celcoin. Botamos um valor qualquer para não transferir
    accountType: CHECKING
    routingNo: 1
    accountNo: 0000000
    accountDv: 2
    ispb: ********