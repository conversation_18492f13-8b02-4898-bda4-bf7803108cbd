ARG DOCKER_IMAGE_MIRROR
FROM ${DOCKER_IMAGE_MIRROR}eclipse-temurin:17-jre-alpine
ARG APP_VERSION
ARG DEFAULT_AGENT_ARGS="-javaagent:dd-java-agent.jar -Ddd.trace.config=dd-java-agent.properties -Ddd.profiling.enabled=true -Ddd.profiling.allocation.enabled=true"

ENV DD_VERSION $APP_VERSION
ENV SERVICE bill-payment-service

ENV AGENT_ARGS=${DEFAULT_AGENT_ARGS}

LABEL com.datadoghq.tags.version="$APP_VERSION"

RUN apk add curl
COPY dd-java-agent.jar dd-java-agent.jar
COPY dd-java-agent.properties dd-java-agent.properties
COPY application/build/libs/${SERVICE}-*-all.jar ${SERVICE}.jar

EXPOSE 8443
CMD java -XX:+UseContainerSupport ${AGENT_ARGS} -XX:FlightRecorderOptions=stackdepth=256 -Dcom.sun.management.jmxremote -Djava.locale.providers=COMPAT,CLDR -noverify ${JAVA_OPTS} -jar ${SERVICE}.jar