package ai.friday.billpayment.modules.investmentGoals.app.account

import ai.friday.billpayment.app.account.AccountClosureDetails
import ai.friday.billpayment.app.account.AccountClosureReason
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.CloseAccountError
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test

class InvestmentCloseAccountDiscoveryTest {

    private val investmentManagerService: InvestmentManagerService = mockk()
    private val investmentCloseAccountDiscovery = InvestmentCloseAccountDiscovery(investmentManagerService)

    @Test
    fun `deve retornar InvestmentDifferentThanZero quando usuario tiver investimento`() {
        every {
            investmentManagerService.userInvestmentsNetValue(AccountId(ACCOUNT_ID))
        } returns 10L.right()
        val result = investmentCloseAccountDiscovery.prepareClose(AccountId(ACCOUNT_ID), AccountClosureDetails.WithReason(reason = AccountClosureReason.USER_REQUEST, description = null, at = getZonedDateTime()))

        result.isLeft() shouldBe true
        result.mapLeft {
            it shouldBe CloseAccountError.CloseAccountErrorWithMessageAndAmount("INVESTMENT_BALANCE_NOT_ZERO", 10L)
        }
    }

    @Test
    fun `deve retornar lista vazia quando o saldo de investimento for 0`() {
        every {
            investmentManagerService.userInvestmentsNetValue(AccountId(ACCOUNT_ID))
        } returns 0L.right()
        val result = investmentCloseAccountDiscovery.prepareClose(AccountId(ACCOUNT_ID), AccountClosureDetails.WithReason(reason = AccountClosureReason.USER_REQUEST, description = null, at = getZonedDateTime()))

        result.isRight() shouldBe true
        result.map {
            it shouldBe emptyList()
        }
    }
}