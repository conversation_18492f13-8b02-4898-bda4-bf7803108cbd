package ai.friday.billpayment.modules.investmentGoals.adapters.api.messaging

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.billAddedInvestment
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.modules.investmentGoals.adapters.messaging.GoalInvestmentSynchronizeHandler
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalStatusService
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestment
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentRepository
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentStatus
import ai.friday.billpayment.modules.investmentGoals.app.messaging.CreateGoalInvestmentMessage
import ai.friday.billpayment.modules.investmentGoals.app.subscription.InvestmentDiscountService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message

class GoalInvestmentSynchronizeHandlerTest {

    private val goalInvestmentRepository = mockk<GoalInvestmentRepository> {
        every {
            save(any())
        } just runs
    }
    private val messagePublisher = mockk<MessagePublisher> {
        every {
            sendMessage(any(), any())
        } just runs
    }

    private val goalStatusService = mockk<GoalStatusService>() {
        every {
            checkAndUpdateGoalToCompleted(any())
        } returns false.right()
    }

    private val billEventRepository = mockk<BillEventRepository> {
        every {
            getBillById(billPaid.billId)
        } returns Bill.build(billAddedInvestment, billPaid).right()
    }
    private val investmentDiscountService = mockk<InvestmentDiscountService> {
        every {
            checkAndApplyInvestmentDiscount(any())
        } returns false
    }

    private val handler = GoalInvestmentSynchronizeHandler(
        amazonSQS = mockk(),
        amazonSNS = mockk(),
        configuration = mockk(),
        billEventRepository = billEventRepository,
        goalInvestmentRepository = goalInvestmentRepository,
        messagePublisher = messagePublisher,
        goalStatusService = goalStatusService,
        investmentDiscountService = investmentDiscountService,
        goalInvestmentSynchronizeQueueName = "sync-queue",
        createGoalInvestmentQueueName = "create-queue",
        topicArn = "topic-arn",
    )

    private val message = Message.builder().body(
        getObjectMapper().writeValueAsString(billPaid.toBillEventDetailEntity()),
    ).build()

    @Test
    fun `quando a bill nao tem um investimento de meta associado deve remover a mensagem da fila`() {
        every {
            goalInvestmentRepository.findByBillId(billPaid.billId)
        } returns null

        val result = handler.handleMessage(message)

        result.shouldDeleteMessage shouldBe true

        verify {
            goalStatusService wasNot called
            messagePublisher wasNot called
            investmentDiscountService wasNot called
        }
    }

    @Test
    fun `quando a bill esta associada a um investimento avulso de meta deve remover a mensagem da fila`() {
        val goalInvestment = setupInvestment(
            extraInstallment = true,
        )

        every {
            goalInvestmentRepository.findByBillId(billPaid.billId)
        } returns goalInvestment

        val result = handler.handleMessage(message)

        result.shouldDeleteMessage shouldBe true

        verify {
            goalStatusService.checkAndUpdateGoalToCompleted(any())
            investmentDiscountService.checkAndApplyInvestmentDiscount(any())
            messagePublisher wasNot called
        }
    }

    @Test
    fun `quando a bill esta associada a um investimento ordinario de meta deve comandar a criacao de mais uma bill`() {
        val goalInvestment = setupInvestment(
            extraInstallment = false,
        )

        every {
            goalInvestmentRepository.findByBillId(billPaid.billId)
        } returns goalInvestment

        val result = handler.handleMessage(message)

        result.shouldDeleteMessage shouldBe true

        val slot = slot<CreateGoalInvestmentMessage>()
        verify {
            goalStatusService.checkAndUpdateGoalToCompleted(any())
            investmentDiscountService.checkAndApplyInvestmentDiscount(any())
            messagePublisher.sendMessage("create-queue", capture(slot))
        }
        slot.captured.goalId shouldBe goalInvestment.goalId.value
    }

    @Test
    fun `quando a bill esta associada a um investimento ordinario de meta mas a meta estive concluida não deve comandar a criacao de mais uma bill`() {
        val goalInvestment = setupInvestment(
            extraInstallment = false,
        )

        every {
            goalStatusService.checkAndUpdateGoalToCompleted(any())
        } returns true.right()

        every {
            goalInvestmentRepository.findByBillId(billPaid.billId)
        } returns goalInvestment

        val result = handler.handleMessage(message)

        result.shouldDeleteMessage shouldBe true

        verify {
            goalStatusService.checkAndUpdateGoalToCompleted(any())
            investmentDiscountService.checkAndApplyInvestmentDiscount(any())
        }

        verify(exactly = 0) {
            messagePublisher.sendMessage(any(), any())
        }
    }

    @Test
    fun `quando houver um erro deve manter a mensagem na fila`() {
        val result = handler.handleError(message, NoStackTraceException())

        result.shouldDeleteMessage shouldBe false
    }

    private fun setupInvestment(extraInstallment: Boolean) = GoalInvestment(
        goalId = GoalId(),
        billId = BillId(),
        dueDate = getLocalDate(),
        paidAt = null,
        extraInstallment = extraInstallment,
        amount = 100_00,
        status = GoalInvestmentStatus.CREATED,
    )
}