package ai.friday.billpayment.modules.investmentGoals

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategory
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryId
import ai.friday.billpayment.modules.investmentGoals.app.goal.CreateGoalCommand
import ai.friday.billpayment.modules.investmentGoals.app.goal.Goal
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalStatus
import ai.friday.billpayment.modules.investmentGoals.app.goal.InstallmentFrequency
import ai.friday.billpayment.modules.investmentGoals.app.goal.MonthlyInstallments
import ai.friday.billpayment.modules.investmentGoals.app.goal.WeeklyInstallmentDay
import ai.friday.billpayment.modules.investmentGoals.app.goal.WeeklyInstallments
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndex
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProduct
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductLiquidity
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductRisk
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductType
import ai.friday.billpayment.modules.investmentGoals.app.product.IndexInterestRate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.equality.shouldBeEqualToIgnoringFields
import io.kotest.matchers.nulls.shouldNotBeNull
import java.time.LocalDate

val goalAccountId = AccountId()
val goalAccount = ACCOUNT.copy(accountId = goalAccountId)
val goalWallet = WalletFixture().buildPrimaryWallet(founderAccount = goalAccount)
val goalWalletId = goalWallet.id

val cdiInterestRate = IndexInterestRate(
    index = FixedIncomeIndex.CDI,
    defaultScale = "DAILY",
    yearlyInterest = 0.1065,
    monthlyInterest = 0.********,
    weeklyInterest = 0.********,
    dailyInterest = 4.0168E-4,
)

val savingsInterestRate = IndexInterestRate(
    index = FixedIncomeIndex.SAVINGS,
    defaultScale = "MONTHLY",
    yearlyInterest = 0.0709,
    monthlyInterest = 0.005728,
    weeklyInterest = 0.********,
    dailyInterest = 2.7202E-4,
)

val enabledMonthlyCDIProduct = GoalProduct(
    type = GoalProductType.CDB,
    index = FixedIncomeIndex.CDI,
    indexIncome = FixedIncomeIndexRate(120),
    liquidity = GoalProductLiquidity.MATURITY,
    minimumTermDays = 0,
    maximumTermDays = 360,
    asset = "CDB Arbi",
    risk = GoalProductRisk.LOW,
    issuer = "Arbi",
    provider = "Arbi",
)
val disabledMonthlyCDIProduct = GoalProduct(
    type = GoalProductType.CDB,
    index = FixedIncomeIndex.CDI,
    indexIncome = FixedIncomeIndexRate(105),
    liquidity = GoalProductLiquidity.MATURITY,
    minimumTermDays = 361,
    maximumTermDays = 720,
    asset = "CDB Arbi",
    risk = GoalProductRisk.LOW,
    issuer = "Arbi",
    provider = "Arbi",
    enabled = false,
)
val dailyCDIProduct = GoalProduct(
    type = GoalProductType.CDB,
    index = FixedIncomeIndex.CDI,
    indexIncome = FixedIncomeIndexRate(100),
    liquidity = GoalProductLiquidity.DAILY,
    minimumTermDays = null,
    maximumTermDays = null,
    asset = "CDB Arbi",
    risk = GoalProductRisk.LOW,
    issuer = "Arbi",
    provider = "Arbi",
)
val savingsProduct = GoalProduct(
    type = GoalProductType.SAVINGS,
    index = FixedIncomeIndex.SAVINGS,
    indexIncome = FixedIncomeIndexRate(100),
    liquidity = GoalProductLiquidity.DAILY,
    minimumTermDays = null,
    maximumTermDays = null,
    asset = "Poupança",
    risk = GoalProductRisk.LOW,
    issuer = "Arbi",
    provider = "Arbi",
)

val weeklyGoal = Goal(
    id = GoalId(),
    accountId = goalAccountId,
    walletId = goalWalletId,
    categoryId = GoalCategoryId(),
    name = "weekly goal",
    imageUrl = "weekly image url",
    endDate = LocalDate.now().plusYears(1),
    amount = 10_000_00,
    liquidity = GoalProductLiquidity.DAILY,
    installments = WeeklyInstallments(
        amount = 185_00,
        dayOfWeek = WeeklyInstallmentDay.TUESDAY,
    ),
    productId = dailyCDIProduct.id,
    installmentInitialAmount = 185_00,
    status = GoalStatus.ACTIVE,
    lastKnownNetBalanceAmount = 0,
    lastKnownNetBalanceAt = getZonedDateTime(),
)

val monthlyGoal = Goal(
    id = GoalId(),
    accountId = goalAccountId,
    walletId = goalWalletId,
    categoryId = GoalCategoryId(),
    name = "monthly goal",
    imageUrl = "monthly image url",
    endDate = LocalDate.now().plusYears(1),
    amount = 10_000_00,
    liquidity = GoalProductLiquidity.MATURITY,
    installments = MonthlyInstallments(
        amount = 927,
        dayOfMonth = 1,
    ),
    productId = enabledMonthlyCDIProduct.id,
    installmentInitialAmount = 927,
    status = GoalStatus.ACTIVE,
    lastKnownNetBalanceAmount = 0,
    lastKnownNetBalanceAt = getZonedDateTime(),
)

val createWeeklyGoalCommand = CreateGoalCommand(
    accountId = goalAccountId,
    categoryId = GoalCategoryId(),
    name = "weekly goal",
    imageUrl = "weekly image url",
    endDate = getLocalDate().plusWeeks(10),
    amount = 100_00,
    liquidity = GoalProductLiquidity.DAILY,

    installmentFrequency = InstallmentFrequency.WEEKLY,
    installmentAmount = 59_89,
    installmentExpirationDay = "FRIDAY",
)

val createMonthlyGoalCommand = CreateGoalCommand(
    accountId = goalAccountId,
    categoryId = GoalCategoryId(),
    name = "monthly goal",
    imageUrl = "monthly image url",
    endDate = LocalDate.now().plusMonths(6),
    amount = 1200_00,
    liquidity = GoalProductLiquidity.MATURITY,

    installmentFrequency = InstallmentFrequency.MONTHLY,
    installmentAmount = 92_70,
    installmentExpirationDay = "1",
)

val createGoalWithInitialInvestmentCommand = createMonthlyGoalCommand.copy(
    initialInvestmentAmount = 10_000_00,
)

infix fun Goal?.shouldBeGoal(goal: Goal) {
    this.shouldNotBeNull()
    this.shouldBeEqualToIgnoringFields(
        goal,
        goal::createdAt,
        goal::updatedAt,
        goal::lastKnownNetBalanceAt,
    )
}

infix fun List<Goal>.shouldHaveGoal(goal: Goal) {
    this.single { it.id == goal.id } shouldBeGoal goal
}

infix fun GoalProduct?.shouldBeGoalProduct(product: GoalProduct) {
    this.shouldNotBeNull()
    this.shouldBeEqualToIgnoringFields(
        product,
        product::createdAt,
        product::updatedAt,
    )
}

infix fun List<GoalProduct>.shouldHaveGoalProduct(product: GoalProduct) {
    this.single { it.id == product.id } shouldBeGoalProduct product
}

infix fun GoalCategory?.shouldBeGoalCategory(category: GoalCategory) {
    this.shouldNotBeNull()
    this.shouldBeEqualToIgnoringFields(
        category,
        category::createdAt,
        category::updatedAt,
    )
}

infix fun List<GoalCategory>.shouldHaveGoalCategory(category: GoalCategory) {
    this.single { it.id == category.id } shouldBeGoalCategory category
}