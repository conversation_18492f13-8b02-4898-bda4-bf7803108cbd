package ai.friday.billpayment.modules.investmentGoals.app.goal

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.shouldBe
import java.time.LocalDate
import java.time.ZonedDateTime
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class GoalDomainTest {

    @Nested
    @DisplayName("ao calcular a data de vencimento")
    inner class NextDueDate {
        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentServiceTest#installmentsAndDates")
        fun `deve retornar a data esperada`(installments: GoalInstallments, expectedDueDate: LocalDate) {
            withGivenDateTime(ZonedDateTime.of(2024, 5, 15, 0, 0, 0, 0, brazilTimeZone)) {
                GoalInstallments.calcNextInstallment(getLocalDate(), null, installments) shouldBe (expectedDueDate to 1)
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentServiceTest#installmentsAndDates")
        fun `deve retornar a data esperada quando há data inicial`(installments: GoalInstallments, initialDueAndLastDate: LocalDate) {
            withGivenDateTime(ZonedDateTime.of(2024, 5, 15, 0, 0, 0, 0, brazilTimeZone)) {
                val expectedDueDate2 = when (installments.frequency) {
                    InstallmentFrequency.WEEKLY -> initialDueAndLastDate.plusWeeks(1)
                    InstallmentFrequency.MONTHLY -> initialDueAndLastDate.plusMonths(1)
                }
                val secondInstallment = GoalInstallments.calcNextInstallment(initialDueAndLastDate, initialDueAndLastDate, installments)
                secondInstallment shouldBe (expectedDueDate2 to 2)

                val expectedDueDate3 = when (installments.frequency) {
                    InstallmentFrequency.WEEKLY -> expectedDueDate2.plusWeeks(1)
                    InstallmentFrequency.MONTHLY -> expectedDueDate2.plusMonths(1)
                }

                val thirdInstallment = GoalInstallments.calcNextInstallment(initialDueAndLastDate, expectedDueDate2, installments)
                thirdInstallment shouldBe (expectedDueDate3 to 3)
            }
        }
    }
}