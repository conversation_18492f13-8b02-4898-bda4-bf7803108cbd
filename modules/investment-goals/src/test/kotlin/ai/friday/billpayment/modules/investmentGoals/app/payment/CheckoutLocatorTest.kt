package ai.friday.billpayment.modules.investmentGoals.app.payment

import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.payment.DefaultCheckoutLocator
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.wallet.WalletId
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test

class CheckoutLocatorTest {

    private val featureConfiguration = mockk<FeatureConfiguration> {
        every { asyncSettlement } returns false
    }

    private val defaultCheckoutLocator = DefaultCheckoutLocator(
        balanceInvoiceCheckout = mockk(),
        balancePixCheckout = mockk(),
        balanceDirectInvoiceCheckout = mockk(),
        boletoCheckout = mockk(),
        asyncSettlementBoletoCheckout = mockk(),
        featureConfiguration = featureConfiguration,
        investmentCheckout = mockk<GoalInvestmentCheckout>(),
    )

    private val transaction = mockk<Transaction> {
        every { walletId } returns WalletId("123")
    }

    @Test
    fun `quando a bill for um INVESTIMENTO, deve devolver o InvestmentCheckout`() {
        every { transaction.settlementData.getTarget<Bill>().billType } returns BillType.INVESTMENT
        every { transaction.type } returns TransactionType.GOAL_INVESTMENT

        val checkout = defaultCheckoutLocator.getCheckout(transaction)

        checkout.shouldBeTypeOf<GoalInvestmentCheckout>()
    }
}