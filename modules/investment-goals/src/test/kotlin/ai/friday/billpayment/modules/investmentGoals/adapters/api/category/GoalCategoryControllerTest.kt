package ai.friday.billpayment.modules.investmentGoals.adapters.api.category

import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategory
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryService
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryType
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpStatus
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class GoalCategoryControllerTest {
    private val goalCategoryService = mockk<GoalCategoryService>()
    private val goalCategoryController = GoalCategoryController(goalCategoryService)

    @Nested
    @DisplayName("getAll")
    inner class GetAll {
        @Test
        fun `deve retornar uma lista de categorias`() {
            val categories = GoalCategoryType.entries.map {
                GoalCategory(
                    name = it.name,
                    type = it,
                    imageUrl = "https://fake.url/$it.png",
                )
            }

            every {
                goalCategoryService.findAllEnabled()
            } returns categories

            val result = goalCategoryController.getAll()
            result.status() shouldBe HttpStatus.OK

            val response = result.getBody(Argument.listOf((GoalCategoryResponseTO::class.java))).get()
            response shouldMatchGoalCategories categories
        }

        private infix fun GoalCategoryResponseTO.shouldMatchGoalCategory(category: GoalCategory) {
            id shouldBe category.id.value
            type shouldBe category.type
            imageUrl shouldBe category.imageUrl
        }

        private infix fun List<GoalCategoryResponseTO>.shouldHaveGoalCategory(category: GoalCategory) {
            single { it.type == category.type } shouldMatchGoalCategory category
        }

        private infix fun List<GoalCategoryResponseTO>.shouldMatchGoalCategories(categories: List<GoalCategory>) {
            size shouldBe categories.size
            categories.forEach {
                this shouldHaveGoalCategory it
            }
        }

        @Test
        fun `deve retornar uma lista vazia quando nao houver categorias`() {
            every {
                goalCategoryService.findAllEnabled()
            } returns emptyList()

            val result = goalCategoryController.getAll()
            result.status() shouldBe HttpStatus.OK

            val response = result.getBody(Argument.listOf((GoalCategoryResponseTO::class.java))).get()
            response.size shouldBe 0
        }
    }
}