package ai.friday.billpayment.modules.investmentGoals.app.payment

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.payment.NoPaymentData
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.toPayer
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeInvestmentRedemptionResult
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeProvider
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalRequestOperationId
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerExternalId
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerRequestStatus
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerService
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerWithRedemptionResultException
import ai.friday.billpayment.modules.investmentGoals.app.messaging.GoalMessagePublisher
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemption
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionRepository
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionStatus
import ai.friday.billpayment.modules.investmentGoals.goalWalletId
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class GoalRedemptionCashInHandlerTest {

    private val redemption = GoalRedemption(
        goalId = GoalId("GOAL_ID_FAKE"),
        walletId = WalletId("WALLET_ID_FAKE"),
        netAmount = 1000_00,
        penaltyRate = FixedIncomeIndexRate(85),
        actionSource = ActionSource.Api(AccountId()),
    )

    private val investmentManagerService = mockk<InvestmentManagerService>()
    private val goalRedemptionRepository = mockk<GoalRedemptionRepository>(relaxUnitFun = true) {
        every {
            findOrNull(redemption.id)
        } returns redemption
    }

    private val balanceService = mockk<BalanceService>(relaxUnitFun = true)
    private val walletService = mockk<WalletService> {
        every {
            findWallet(any())
        } returns WalletFixture().buildWallet()
    }

    private val internalBankService = mockk<InternalBankService>() {
        every {
            synchronizeBankAccount(any(), any())
        } returns true
    }
    private val goalsInvestmentNotificationService = mockk<GoalsInvestmentNotificationService>(relaxed = true)

    private val goalMessagePublisher = mockk<GoalMessagePublisher>(relaxUnitFun = true)
    private val handler = GoalRedemptionCashInHandler(
        waitTime = 1,
        poolingInterval = 1,
        investmentManagerService = investmentManagerService,
        goalRedemptionRepository = goalRedemptionRepository,
        balanceService = balanceService,
        walletService = walletService,
        internalBankService = internalBankService,
        goalsInvestmentNotificationService = goalsInvestmentNotificationService,
        goalMessagePublisher = goalMessagePublisher,
    )

    private val originalRedemptionSettlementOperation = GoalRedemptionSettlementOperation(
        amount = redemption.netAmount,
    )

    private val transaction = Transaction(
        type = TransactionType.GOAL_REDEMPTION,
        payer = ACCOUNT.toPayer(),
        paymentData = NoPaymentData,
        settlementData = SettlementData(
            settlementTarget = redemption,
            serviceAmountTax = 0,
            totalAmount = redemption.netAmount,
            settlementOperation = originalRedemptionSettlementOperation,
        ),
        nsu = 0,
        actionSource = ActionSource.System,
        walletId = goalWalletId,
    )

    @Nested
    @DisplayName("execute")
    inner class ExecuteTransaction {
        @Test
        fun `deve falhar quando o resgate falhar e salvar os dados da chamada`() {
            every {
                investmentManagerService.redemption(any(), any(), any(), any(), any())
            } returns InvestmentManagerWithRedemptionResultException(
                result = FixedIncomeInvestmentRedemptionResult(
                    operationId = GoalRequestOperationId("OPERATION_1"),
                    provider = FixedIncomeProvider.ARBI,
                    status = InvestmentManagerRequestStatus.FAILED,
                    redemptionGroupExternalId = null,
                    redemptionExternalIds = emptyList(),
                    errorMessage = null,
                    amounts = null,
                ),
                throwable = IllegalStateException("FAKE"),
            ).left()

            val result = handler.execute(transaction = transaction)

            result.status shouldBe TransactionStatus.FAILED
            with(result.settlementData.settlementOperation) {
                this.shouldNotBeNull()
                this.shouldBeTypeOf<GoalRedemptionSettlementOperation>()
                this.operationId shouldBe originalRedemptionSettlementOperation.operationId
                this.status shouldBe InvestmentManagerRequestStatus.FAILED
                this.provider shouldBe FixedIncomeProvider.ARBI
            }
            result.paymentData.shouldBeTypeOf<NoPaymentData>()

            verify {
                balanceService.invalidate(any())
                internalBankService.synchronizeBankAccount(any(), any())
                goalsInvestmentNotificationService.notifyInvestmentRedemptionFinished(any(), any())
            }

            verify(exactly = 0) {
                goalMessagePublisher.publishCheckGoalCompletion(any(), any())
            }
        }

        @Test
        fun `deve deixar UNKNOWN quando o resgate estiver como CREATED ou REQUESTED e salvar os dados da chamada`() {
            every {
                investmentManagerService.redemption(any(), any(), any(), any(), any())
            } returns FixedIncomeInvestmentRedemptionResult(
                operationId = GoalRequestOperationId("OPERATION_1"),
                provider = FixedIncomeProvider.ARBI,
                status = InvestmentManagerRequestStatus.CREATED,
                redemptionGroupExternalId = null,
                redemptionExternalIds = listOf(InvestmentManagerExternalId("123")),
                errorMessage = null,
                amounts = null,
            ).right()

            val result = handler.execute(transaction = transaction)

            result.status shouldBe TransactionStatus.PROCESSING
            val settlementOperation = result.settlementData.settlementOperation
            settlementOperation.shouldBeTypeOf<GoalRedemptionSettlementOperation>()
            settlementOperation.status shouldBe InvestmentManagerRequestStatus.CREATED
            settlementOperation.redemptionGroupExternalId shouldBe null
            settlementOperation.redemptionExternalIds shouldBe listOf(InvestmentManagerExternalId("123"))
            settlementOperation.amount shouldBe redemption.netAmount
            settlementOperation.errorDescription shouldBe ""
            settlementOperation.provider shouldBe FixedIncomeProvider.ARBI
            settlementOperation.singleExternalId shouldBe InvestmentManagerExternalId("123")

            result.paymentData.shouldBeTypeOf<NoPaymentData>()

            verify(exactly = 0) {
                balanceService.invalidate(any())
                internalBankService.synchronizeBankAccount(any(), any())
                goalsInvestmentNotificationService.notifyInvestmentRedemptionFinished(any(), any())
                goalMessagePublisher.publishCheckGoalCompletion(any(), any())
            }
        }

        @Test
        fun `deve deixar COMPLETAR quando o resgate estiver como COMPLETED e salvar os dados da chamada e também da liquidação`() {
            every {
                investmentManagerService.redemption(any(), any(), any(), any(), any())
            } returns FixedIncomeInvestmentRedemptionResult(
                operationId = GoalRequestOperationId("OPERATION_1"),
                redemptionGroupExternalId = InvestmentManagerExternalId("123"),
                redemptionExternalIds = listOf(InvestmentManagerExternalId("456"), InvestmentManagerExternalId("789")),
                provider = FixedIncomeProvider.ARBI,
                status = InvestmentManagerRequestStatus.COMPLETED,
                errorMessage = null,
                amounts = null,
            ).right()

            val result = handler.execute(transaction = transaction)

            result.status shouldBe TransactionStatus.COMPLETED
            val settlementOperation = result.settlementData.settlementOperation
            settlementOperation.shouldBeTypeOf<GoalRedemptionSettlementOperation>()
            settlementOperation.status shouldBe InvestmentManagerRequestStatus.COMPLETED
            settlementOperation.redemptionGroupExternalId shouldBe InvestmentManagerExternalId("123")
            settlementOperation.redemptionExternalIds shouldBe listOf(InvestmentManagerExternalId("456"), InvestmentManagerExternalId("789"))
            settlementOperation.amount shouldBe redemption.netAmount
            settlementOperation.errorDescription shouldBe ""
            settlementOperation.provider shouldBe FixedIncomeProvider.ARBI
            settlementOperation.singleExternalId.shouldBeNull()

            verify {
                balanceService.invalidate(any())
                internalBankService.synchronizeBankAccount(any(), any())
                goalsInvestmentNotificationService.notifyInvestmentRedemptionFinished(any(), any())
                goalMessagePublisher.publishCheckGoalCompletion(any(), true)
            }
        }
    }

    @Nested
    @DisplayName("RetryTransaction")
    inner class RetryTransactionTest {

        private fun setupCheckStatusByOperationId(status: InvestmentManagerRequestStatus) {
            every {
                investmentManagerService.checkInvestmentRedemptionStatus(transaction.payer.accountId, originalRedemptionSettlementOperation.operationId)
            } returns FixedIncomeInvestmentRedemptionResult(
                operationId = GoalRequestOperationId("OPERATION_1"),
                redemptionGroupExternalId = null,
                redemptionExternalIds = listOf(InvestmentManagerExternalId("123")),
                provider = FixedIncomeProvider.ARBI,
                status = status,
                errorMessage = null,
                amounts = null,
            ).right()
        }

        @Test
        fun `transacao deve continuar como PROCESSING quando não conseguir consultar o status do resgate`() {
            every {
                investmentManagerService.checkInvestmentRedemptionStatus(transaction.payer.accountId, originalRedemptionSettlementOperation.operationId)
            } returns InvestmentManagerWithRedemptionResultException(
                result = FixedIncomeInvestmentRedemptionResult(
                    operationId = GoalRequestOperationId("OPERATION_1"),
                    redemptionGroupExternalId = null,
                    redemptionExternalIds = listOf(InvestmentManagerExternalId("123")),
                    provider = FixedIncomeProvider.ARBI,
                    status = InvestmentManagerRequestStatus.UNKNOWN,
                    errorMessage = null,
                    amounts = null,
                ),
                throwable = IllegalStateException("FAKE"),
            ).left()

            handler.retry(transaction).status shouldBe TransactionStatus.PROCESSING
        }

        @ParameterizedTest
        @CsvSource(
            "CREATED  , PROCESSING",
            "UNKNOWN  , PROCESSING",
            "REQUESTED, PROCESSING",
            "COMPLETED, COMPLETED",
            "NOT_FOUND, FAILED",
            "FAILED   , FAILED",
        )
        fun `deve atualizar o estado da transacao de acordo com o estado do investimento`(investmentStatus: InvestmentManagerRequestStatus, transactionStatus: TransactionStatus) {
            setupCheckStatusByOperationId(investmentStatus)

            handler.retry(transaction).status shouldBe transactionStatus
        }
    }

    @Nested
    @DisplayName("NotifyStatus")
    inner class NotifyStatus {
        @Test
        fun `quando comecar a processar a transacao deve atualizar o estado do resgate para PROCESSING`() {
            handler.notifyStarted(transaction)

            val slot = slot<GoalRedemption>()
            verify {
                goalRedemptionRepository.save(capture(slot))
            }
            slot.captured shouldBe redemption.copy(status = GoalRedemptionStatus.PROCESSING)
        }

        @Test
        fun `quando terminar de processar a transacao com sucesso deve atualizar o estado do resgate para DONE`() {
            handler.notifyCompleted(transaction)

            val slot = slot<GoalRedemption>()
            verify {
                goalRedemptionRepository.save(capture(slot))
            }
            slot.captured shouldBe redemption.copy(status = GoalRedemptionStatus.DONE)
        }

        @Test
        fun `quando terminar de processar a transacao com falha deve atualizar o estado do resgate para FAILED`() {
            handler.notifyFailed(transaction)

            val slot = slot<GoalRedemption>()
            verify {
                goalRedemptionRepository.save(capture(slot))
            }
            slot.captured shouldBe redemption.copy(status = GoalRedemptionStatus.FAILED)
        }
    }
}