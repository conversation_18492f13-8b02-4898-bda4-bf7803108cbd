package ai.friday.billpayment.modules.investmentGoals.adapters.api.goal

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.investmentGoals.app.goal.CreateGoalCommand
import ai.friday.billpayment.modules.investmentGoals.app.goal.CreateGoalError
import ai.friday.billpayment.modules.investmentGoals.app.goal.DetailedGoal
import ai.friday.billpayment.modules.investmentGoals.app.goal.DetailedGoals
import ai.friday.billpayment.modules.investmentGoals.app.goal.Goal
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalBalance
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalInstallments
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalService
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalStatus
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalStatusService
import ai.friday.billpayment.modules.investmentGoals.app.goal.InstallmentFrequency
import ai.friday.billpayment.modules.investmentGoals.app.goal.UpdateGoalCommand
import ai.friday.billpayment.modules.investmentGoals.app.goal.UpdateGoalError
import ai.friday.billpayment.modules.investmentGoals.app.goal.WeeklyInstallmentDay
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomePositionId
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeProvider
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeRedemptionQuotation
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeRedemptionQuotations
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeRedemptionType
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentId
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentService
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerExternalId
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProduct
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductLiquidity
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionCommand
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionService
import ai.friday.billpayment.modules.investmentGoals.app.statement.GoalStatementItem
import ai.friday.billpayment.modules.investmentGoals.app.statement.GoalStatementItemId
import ai.friday.billpayment.modules.investmentGoals.app.statement.GoalStatementItemStatus
import ai.friday.billpayment.modules.investmentGoals.app.statement.GoalStatementItemType
import ai.friday.billpayment.modules.investmentGoals.enabledMonthlyCDIProduct
import ai.friday.billpayment.modules.investmentGoals.monthlyGoal
import ai.friday.billpayment.modules.investmentGoals.weeklyGoal
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpStatus
import io.micronaut.security.authentication.Authentication
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class GoalControllerTest {
    private val goalService = mockk<GoalService>()
    private val goalStatusService = mockk<GoalStatusService>()
    private val accountService = mockk<AccountService> {
        every {
            findAccountById(ACCOUNT.accountId)
        } returns ACCOUNT
    }
    private val goalRedemptionService = mockk<GoalRedemptionService>()
    private val goalInvestmentService = mockk<GoalInvestmentService>()
    private val goalController = GoalController(goalService, goalStatusService, accountService, goalRedemptionService, goalInvestmentService)

    private val authentication: Authentication = mockk {
        every {
            name
        } returns ACCOUNT.accountId.value
    }

    @Nested
    @DisplayName("ao tentar criar uma meta")
    inner class Create {

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.adapters.api.goal.GoalControllerTest#invalidRequests")
        fun `deve retornar BAD_REQUEST quando eh uma requisicao invalida`(request: CreateGoalRequestTO) {
            val response = goalController.create(authentication, request)

            response.status shouldBe HttpStatus.BAD_REQUEST
            response.body.isPresent shouldBe true
            with(response.body()) {
                this.shouldBeTypeOf<ResponseTO>()
                this.code shouldBe "4001"
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.adapters.api.goal.GoalControllerTest#validRequests")
        fun `deve retornar CONFLICT quando a conta precisa de upgrade`(request: CreateGoalRequestTO) {
            every {
                goalService.create(any())
            } returns CreateGoalError.MustUpgradeAccount.left()

            val response = goalController.create(authentication, request)

            response.status shouldBe HttpStatus.CONFLICT
            response.body.isPresent shouldBe true
            with(response.body()) {
                this.shouldBeTypeOf<ResponseTO>()
                this.code shouldBe "4009"
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.adapters.api.goal.GoalControllerTest#validRequests")
        fun `deve retornar OK quando eh uma requisicao valida`(request: CreateGoalRequestTO) {
            every {
                goalService.create(any())
            } answers {
                val command: CreateGoalCommand = firstArg()

                command.toGoal(ACCOUNT.defaultWalletId(), enabledMonthlyCDIProduct).right()
            }

            val response = goalController.create(authentication, request)

            response.status shouldBe HttpStatus.OK
            response.body.isPresent shouldBe true
            with(response.body()) {
                this.shouldBeTypeOf<GoalResponseTO>()
                this.accountId shouldBe ACCOUNT.accountId.value
                this.walletId shouldBe ACCOUNT.defaultWalletId().value
                this.name shouldBe request.name
                this.categoryId shouldBe request.categoryId
                this.imageUrl shouldBe request.imageUrl
                this.amount shouldBe request.amount
                this.endDate shouldBe request.endDate
                this.liquidity shouldBe request.liquidity
                this.installments.frequency shouldBe request.installmentFrequency
                this.installments.amount shouldBe request.installmentAmount
                this.installments.expirationDay shouldBe request.installmentExpirationDay
            }
        }
    }

    @Nested
    @DisplayName("ao tentar consultar meta")
    inner class Find {

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.adapters.api.goal.GoalControllerTest#goals")
        fun `deve retornar uma meta especifica`(goal: Goal) {
            val detailedGoal = goal.withDetails()

            every {
                goalService.getDetailedGoal(goal.accountId, goal.id)
            } returns detailedGoal.right()

            every {
                authentication.name
            } returns goal.accountId.value

            val response = goalController.getDetailedGoal(authentication, goal.id.value)

            response.status shouldBe HttpStatus.OK
            response.body.isPresent shouldBe true

            val body = response.getBody(Argument.of(DetailedGoalTO::class.java)).get()
            body shouldMatchDetailedGoal detailedGoal
        }

        @Test
        fun `deve retornar todas as metas`() {
            val detailedGoals = listOf(weeklyGoal, monthlyGoal).withDetails()

            every {
                goalService.getDetailedGoals(ACCOUNT.accountId)
            } returns detailedGoals.right()

            val response = goalController.getDetailedGoals(authentication)

            response.status shouldBe HttpStatus.OK
            response.body.isPresent shouldBe true

            val body = response.getBody(Argument.of(DetailedGoalsTO::class.java)).get()
            body shouldMatchDetailedGoals detailedGoals
        }
    }

    @Nested
    @DisplayName("ao consultar o valor total disponivel para resgate")
    inner class AvailableToRedeem {
        private val goalId = GoalId()

        @Test
        fun `deve retornar OK em caso de sucesso`() {
            val accountIdSlot = slot<AccountId>()
            val goalIdSlot = slot<GoalId>()
            every {
                goalRedemptionService.availableToRedeem(capture(accountIdSlot), capture(goalIdSlot))
            } returns 123L.right()

            val response = goalController.availableToRedeem(authentication, goalId.value)

            response.status shouldBe HttpStatus.OK

            val body = response.getBody(Argument.of(AvailableToRedeemResponseTO::class.java)).get()
            body.netValue shouldBe 123L

            accountIdSlot.captured shouldBe authentication.toAccountId()
            goalIdSlot.captured shouldBe goalId
        }

        @Test
        fun `deve retornar INTERNAL_SERVER_ERROR em caso de erro`() {
            every {
                goalRedemptionService.availableToRedeem(any(), any())
            } returns NoStackTraceException("mocked exception").left()

            val response = goalController.availableToRedeem(authentication, goalId.value)

            response.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR

            val body = response.getBody(Argument.of(ResponseTO::class.java)).get()
            body.message shouldBe "mocked exception"
        }
    }

    @Nested
    @DisplayName("ao cotar um resgate")
    inner class QuoteRedemption {
        private val goalId = GoalId()
        private val request = QuoteRedemptionRequestTO(
            goalId = goalId.value,
            amount = 6184,
        )

        @Test
        fun `deve retornar OK em caso de sucesso`() {
            val quotations = FixedIncomeRedemptionQuotations(
                accountId = AccountId(value = "instructior"),
                goalId = GoalId(value = "fugit"),
                quotations = listOf(
                    FixedIncomeRedemptionQuotation(
                        quotationId = InvestmentManagerExternalId(value = "errem"),
                        provider = FixedIncomeProvider.ARBI,
                        positionId = FixedIncomePositionId(value = "efficitur"),
                        principalValue = 3947,
                        grossValue = 6315,
                        netValue = 7796,
                        quantity = 6757,
                        pu = 2.3,
                        penaltyRate = FixedIncomeIndexRate(rate = 4608),
                        taxValue = 4338,
                        redemptionType = FixedIncomeRedemptionType.TOTAL,
                        penaltyValue = 0,
                    ),
                ),
                principalValue = 7592,
                grossValue = 2960,
                netValue = 8327,
                taxValue = 3896,
                penaltyValue = 5437,
            )

            val accountIdSlot = slot<AccountId>()
            val goalIdSlot = slot<GoalId>()
            val netAmountSlot = slot<Long>()
            every {
                goalRedemptionService.quoteRedemption(capture(accountIdSlot), capture(goalIdSlot), capture(netAmountSlot))
            } returns quotations.right()

            val response = goalController.quoteRedemption(authentication, request)

            response.status shouldBe HttpStatus.OK

            val body = response.getBody(Argument.of(QuoteRedemptionResponseTO::class.java)).get()
            body.grossValue shouldBe quotations.grossValue
            body.netValue shouldBe quotations.netValue
            body.taxValue shouldBe quotations.taxValue
            body.penaltyValue shouldBe quotations.penaltyValue

            accountIdSlot.captured shouldBe authentication.toAccountId()
            goalIdSlot.captured shouldBe goalId
            netAmountSlot.captured shouldBe request.amount
        }

        @Test
        fun `deve retornar INTERNAL_SERVER_ERROR em caso de erro`() {
            every {
                goalRedemptionService.quoteRedemption(any(), any(), any())
            } returns NoStackTraceException("mocked exception").left()

            val response = goalController.quoteRedemption(authentication, request)

            response.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR

            val body = response.getBody(Argument.of(ResponseTO::class.java)).get()
            body.message shouldBe "mocked exception"
        }
    }

    @Nested
    @DisplayName("ao solicitar um resgate")
    inner class RequestRedemption {
        private val goalId = GoalId()
        private val request = QuoteRedemptionRequestTO(
            goalId = goalId.value,
            amount = 6184,
        )

        @Test
        fun `deve retornar CREATED em caso de sucesso`() {
            val transactionId = TransactionId("mockedTransactionId")
            val slot = slot<GoalRedemptionCommand>()
            every {
                goalRedemptionService.requestRedemption(capture(slot))
            } returns transactionId.right()

            val response = goalController.requestRedemption(authentication, request)

            response.status shouldBe HttpStatus.CREATED

            val body = response.getBody(Argument.of(ExecuteRedemptionResponseTO::class.java)).get()
            body.transactionId shouldBe transactionId.value

            with(slot.captured) {
                this.goalId shouldBe goalId
                this.netAmount shouldBe request.amount
                this.actionSource.accountId shouldBe authentication.toAccountId()
            }
        }

        @Test
        fun `deve retornar INTERNAL_SERVER_ERROR em caso de erro`() {
            every {
                goalRedemptionService.requestRedemption(any())
            } returns NoStackTraceException("mocked exception").left()

            val response = goalController.requestRedemption(authentication, request)

            response.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR

            val body = response.getBody(Argument.of(ResponseTO::class.java)).get()
            body.message shouldBe "mocked exception"
        }
    }

    @Nested
    @DisplayName("ao ignorar uma meta")
    inner class IgnoreGoal {

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.adapters.api.goal.GoalControllerTest#removeGoalConflicts")
        fun `deve retornar CONFLICT quando a meta nao pode ser removida`(expectedCode: String, error: UpdateGoalError) {
            val goalIdSlot = slot<GoalId>()
            val actionSourceSlot = slot<ActionSource.WalletActionSource>()
            every {
                goalStatusService.remove(capture(goalIdSlot), capture(actionSourceSlot))
            } returns error.left()

            val response = goalController.remove(authentication, weeklyGoal.id.value)

            response.status shouldBe HttpStatus.CONFLICT
            val body = response.getBody(Argument.of(ResponseTO::class.java)).get()

            body.code shouldBe expectedCode

            goalIdSlot.captured shouldBe weeklyGoal.id
            with(actionSourceSlot.captured) {
                this.shouldBeTypeOf<ActionSource.Api>()
                this.accountId.value shouldBe authentication.name
            }
        }

        @Test
        fun `deve retornar OK quando a meta pode ser removida`() {
            val goalIdSlot = slot<GoalId>()
            val actionSourceSlot = slot<ActionSource.WalletActionSource>()
            every {
                goalStatusService.remove(capture(goalIdSlot), capture(actionSourceSlot))
            } returns GoalStatus.REMOVED.right()

            val response = goalController.remove(authentication, weeklyGoal.id.value)

            response.status shouldBe HttpStatus.NO_CONTENT

            goalIdSlot.captured shouldBe weeklyGoal.id
            with(actionSourceSlot.captured) {
                this.shouldBeTypeOf<ActionSource.Api>()
                this.accountId.value shouldBe authentication.name
            }
        }
    }

    @Nested
    @DisplayName("ao atualizar uma meta")
    inner class UpdateGoal {

        @Test
        fun `ao atualizar o nome deve retornar OK`() {
            val slot = slot<UpdateGoalCommand>()
            every {
                goalService.update(capture(slot))
            } returns weeklyGoal.right()

            val request = UpdateGoalRequestTO(
                name = "updated mocked name",
                amount = null,
                endDate = null,
                installmentFrequency = null,
                installmentExpirationDay = null,
            )

            val response = goalController.update(authentication, weeklyGoal.id.value, request)

            response.status shouldBe HttpStatus.OK

            with(slot.captured) {
                this.goalId shouldBe weeklyGoal.id
                this.name shouldBe request.name
            }
        }
    }

    private infix fun GoalBalanceTO?.shouldMatchGoalBalance(balance: GoalBalance?) {
        if (balance == null) {
            this.shouldBeNull()
        } else {
            this.shouldNotBeNull()
            netAmount shouldBe balance.netValue
            grossAmount shouldBe balance.grossValue
            taxAmount shouldBe balance.irValue + balance.iofValue
            earningAmount shouldBe balance.earningValue
            savingsReferenceAmount shouldBe balance.savingsReferenceValue
            investedAmount shouldBe balance.investedValue
        }
    }

    private infix fun GoalStatementItemTO.shouldMatchGoalStatementItem(item: GoalStatementItem) {
        id shouldBe item.id.value
        type shouldBe item.type
        amount shouldBe item.amount
        date shouldBe item.timestamp.format(dateFormat)
        status shouldBe item.status
    }

    private infix fun List<GoalStatementItemTO>.shouldHaveGoalStatementItem(item: GoalStatementItem) {
        single { it.id == item.id.value } shouldMatchGoalStatementItem item
    }

    private infix fun List<GoalStatementItemTO>.shouldMatchGoalStatementItems(statementItems: List<GoalStatementItem>) {
        size shouldBe statementItems.size
        statementItems.forEach { item ->
            this shouldHaveGoalStatementItem item
        }
    }

    private infix fun DetailedGoalTO.shouldMatchDetailedGoal(detailedGoal: DetailedGoal) {
        goal shouldMatchGoal detailedGoal.goal
        balance shouldMatchGoalBalance detailedGoal.balance
        statementItems shouldMatchGoalStatementItems detailedGoal.statementItems
    }

    private infix fun List<DetailedGoalTO>.shouldHaveDetailedGoal(detailedGoal: DetailedGoal) {
        single { it.goal.id == detailedGoal.goal.id.value } shouldMatchDetailedGoal detailedGoal
    }

    private infix fun List<DetailedGoalTO>.shouldMatchDetailedGoals(items: List<DetailedGoal>) {
        size shouldBe items.size
        items.forEach {
            this shouldHaveDetailedGoal it
        }
    }

    private infix fun DetailedGoalsTO.shouldMatchDetailedGoals(detailedGoals: DetailedGoals) {
        totalBalance shouldMatchGoalBalance detailedGoals.totalBalance
        items shouldMatchDetailedGoals detailedGoals.items
    }

    private fun List<Goal>.withDetails() = DetailedGoals(
        totalBalance = GoalBalance(
            grossValue = 800,
            netValue = 780,
            irValue = 8,
            iofValue = 12,
            savingsReferenceValue = 500,
            investedValue = 400,
            earningValue = 380,
        ),
        items = this.map { it.withDetails() },
    )

    // FIXME - variar os detalhes
    private fun Goal.withDetails() = DetailedGoal(
        goal = this,
        balance = GoalBalance(
            grossValue = 200,
            netValue = 190,
            irValue = 4,
            iofValue = 6,
            savingsReferenceValue = 125,
            investedValue = 100,
            earningValue = 90,
        ),
        statementItems = listOf(
            GoalStatementItem(
                id = GoalStatementItemId("C1"),
                type = GoalStatementItemType.CREDIT,
                amount = 100,
                status = GoalStatementItemStatus.DONE,
                timestamp = getZonedDateTime().minusMonths(5),
            ),
            GoalStatementItem(
                id = GoalStatementItemId("D1"),
                type = GoalStatementItemType.DEBIT,
                amount = 200,
                status = GoalStatementItemStatus.PROCESSING,
                timestamp = getZonedDateTime(),
            ),
        ),
        positions = null,
    )

    private infix fun GoalResponseTO.shouldMatchGoal(goal: Goal) {
        this.id shouldBe goal.id.value
        this.accountId shouldBe goal.accountId.value
        this.walletId shouldBe goal.walletId.value
        this.name shouldBe goal.name
        this.categoryId shouldBe goal.categoryId.value
        this.imageUrl shouldBe goal.imageUrl
        this.amount shouldBe goal.amount
        this.endDate shouldBe goal.endDate.format(dateFormat)
        this.liquidity shouldBe goal.liquidity
        this.installments.frequency shouldBe goal.installments.frequency
        this.installments.amount shouldBe goal.installments.amount
        this.installments.expirationDay shouldBe goal.installments.expirationDay
    }

    companion object {
        private val validWeeklyGoalRequest = CreateGoalRequestTO(
            categoryId = "GOAL-CATEGORY-1234",
            name = "weekly goal",
            imageUrl = "weekly image url",
            endDate = "2024-12-31",
            amount = 1,
            liquidity = GoalProductLiquidity.DAILY,
            installmentFrequency = InstallmentFrequency.WEEKLY,
            installments = 8539,
            installmentAmount = 5989,
            installmentExpirationDay = WeeklyInstallmentDay.TUESDAY.name,
        )

        private val validMonthlyGoalRequest = CreateGoalRequestTO(
            categoryId = "GOAL-CATEGORY-5678",
            name = "monthly goal",
            imageUrl = "monthly image url",
            endDate = "2025-01-01",
            amount = 1987,
            liquidity = GoalProductLiquidity.MATURITY,
            installmentFrequency = InstallmentFrequency.MONTHLY,
            installments = 2,
            installmentAmount = 342,
            installmentExpirationDay = "5",
            initialInvestmentAmount = 10_000_00,
        )

        @JvmStatic
        fun validRequests() = listOf(validWeeklyGoalRequest, validMonthlyGoalRequest)

        private val invalidEndDateRequest = validMonthlyGoalRequest.copy(endDate = "2025-31-12")

        @JvmStatic
        fun invalidRequests() = listOf(
            invalidEndDateRequest,
        )

        @JvmStatic
        fun goals() = listOf(weeklyGoal, monthlyGoal)

        @JvmStatic
        fun removeGoalConflicts() = listOf(
            Arguments.of("4091", UpdateGoalError.BalanceDifferentThanZero(1)),
            Arguments.of("4092", UpdateGoalError.InvestmentProcessing(GoalInvestmentId())),
        )
    }
}

private fun CreateGoalCommand.toGoal(walletId: WalletId, product: GoalProduct) = Goal(
    id = GoalId(value = ""),
    accountId = accountId,
    walletId = walletId,
    categoryId = categoryId,
    name = name,
    amount = amount,
    endDate = endDate,
    installments = GoalInstallments.buildGoalInstallment(
        frequency = installmentFrequency,
        amount = installmentAmount,
        expirationDay = installmentExpirationDay,
    ),
    imageUrl = imageUrl,
    liquidity = liquidity,
    productId = product.id,
    installmentInitialAmount = installmentAmount,
    status = GoalStatus.ACTIVE,
    lastKnownNetBalanceAmount = 0,
    lastKnownNetBalanceAt = getZonedDateTime(),
)