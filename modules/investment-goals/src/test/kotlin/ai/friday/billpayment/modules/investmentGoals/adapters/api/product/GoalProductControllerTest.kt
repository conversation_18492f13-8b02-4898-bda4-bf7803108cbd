package ai.friday.billpayment.modules.investmentGoals.adapters.api.product

import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProduct
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductService
import ai.friday.billpayment.modules.investmentGoals.app.product.IndexInterestRate
import ai.friday.billpayment.modules.investmentGoals.cdiInterestRate
import ai.friday.billpayment.modules.investmentGoals.dailyCDIProduct
import ai.friday.billpayment.modules.investmentGoals.disabledMonthlyCDIProduct
import ai.friday.billpayment.modules.investmentGoals.enabledMonthlyCDIProduct
import ai.friday.billpayment.modules.investmentGoals.savingsInterestRate
import ai.friday.billpayment.modules.investmentGoals.savingsProduct
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.dateFormat
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpStatus
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test

class GoalProductControllerTest {
    private val goalProductService = mockk<GoalProductService>()
    private val goalProductController = GoalProductController(goalProductService)

    @Test
    fun `deve retornar uma lista de produtos`() {
        every {
            goalProductService.findAllEnabled()
        } returns listOf(enabledMonthlyCDIProduct, disabledMonthlyCDIProduct, dailyCDIProduct, savingsProduct)

        val result = goalProductController.getAll()
        result.status() shouldBe HttpStatus.OK

        val response = result.getBody(Argument.listOf((GoalProductResponseTO::class.java))).get()
        response.size shouldBe 4
        response shouldHaveGoalProduct enabledMonthlyCDIProduct
        response shouldHaveGoalProduct disabledMonthlyCDIProduct
        response shouldHaveGoalProduct dailyCDIProduct
        response shouldHaveGoalProduct savingsProduct
    }

    @Test
    fun `deve retornar uma lista de indices`() {
        every {
            goalProductService.getIndexInterestRates()
        } returns listOf(cdiInterestRate, savingsInterestRate).right()

        val result = goalProductController.getIndexInterestRates()
        result.status() shouldBe HttpStatus.OK

        val response = result.getBody(Argument.listOf((IndexInterestRateTO::class.java))).get()
        response.size shouldBe 2
        response shouldHaveIndexInterestRate cdiInterestRate
        response shouldHaveIndexInterestRate savingsInterestRate
    }

    private infix fun GoalProductResponseTO.shouldMatchGoalProduct(product: GoalProduct) {
        id shouldBe product.id.value
        type shouldBe product.type
        index shouldBe product.index
        indexIncome shouldBe product.indexIncome.rate
        liquidity shouldBe product.liquidity
        minimumTermDays shouldBe product.minimumTermDays
        maximumTermDays shouldBe product.maximumTermDays
        asset shouldBe product.asset
        risk shouldBe product.risk
        issuer shouldBe product.issuer
        provider shouldBe product.provider
        startDate shouldBe product.minimumTermDays?.let { getLocalDate().plusDays(it).format(dateFormat) }
        endDate shouldBe product.maximumTermDays?.let { getLocalDate().plusDays(it).format(dateFormat) }
    }

    private infix fun List<GoalProductResponseTO>.shouldHaveGoalProduct(product: GoalProduct) {
        this.single { it.id == product.id.value } shouldMatchGoalProduct product
    }

    private infix fun IndexInterestRateTO.shouldMatchIndexInterestRate(rate: IndexInterestRate) {
        index shouldBe rate.index
        defaultScale shouldBe rate.defaultScale
        yearlyInterest shouldBe rate.yearlyInterest
        monthlyInterest shouldBe rate.monthlyInterest
        dailyInterest shouldBe rate.dailyInterest
    }

    private infix fun List<IndexInterestRateTO>.shouldHaveIndexInterestRate(rate: IndexInterestRate) {
        this.single { it.index == rate.index } shouldMatchIndexInterestRate rate
    }
}