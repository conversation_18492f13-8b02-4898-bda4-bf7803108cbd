/*
package ai.friday.billpayment.modules.investmentGoals.adapters.investmentManager

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.Address
import ai.friday.billpayment.app.account.DocumentInfo
import ai.friday.billpayment.app.account.Gender
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.liveness.LivenessEnrollmentVerification
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndex
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerExternalId
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerRequestStatus
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.getOrElse
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import java.time.LocalDate
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@MicronautTest
@Disabled
class InvestmentManagerAdapterConfigurationTest(
    private val investmentManagerAdapter: InvestmentManagerAdapter,
) {

    private val accountId = AccountId(value = "ACCOUNT_ID_POSTMAN_994e2b12-7fe2-11ef-9a7c-674003a0597e")

    @Test
    fun `deve criar uma conta de investimento no microserviço`() {
        val response = investmentManagerAdapter.enableFixedIncomeAccount(
            accountRegisterData = AccountRegisterData(
                accountId = accountId,
                emailAddress = EmailAddress("<EMAIL>"),
                emailVerified = true,
                emailTokenExpiration = null,
                nickname = "Marlon",
                mobilePhone = null,
                mobilePhoneVerified = false,
                mobilePhoneTokenExpiration = null,
                created = getZonedDateTime(),
                lastUpdated = getZonedDateTime(),
                upgradeStarted = null,
                documentInfo = DocumentInfo(
                    name = "Nome do usuário de Teste",
                    cpf = "***********",
                    birthDate = null,
                    fatherName = "",
                    motherName = "",
                    rg = "***********",
                    docType = DocumentType.CNH,
                    cnhNumber = "***********",
                    orgEmission = "",
                    expeditionDate = LocalDate.parse("2010-01-01"),
                    birthCity = "",
                    birthState = "",
                ),
                calculatedGender = Gender.M,
                isDocumentEdited = false,
                uploadedCNH = null,
                uploadedDocument = null,
                livenessId = null,
                uploadedSelfie = null,
                uploadedSelfieDateTime = null,
                address = Address(
                    streetType = "Rua",
                    streetName = "Silvia Pozzano",
                    number = "3",
                    complement = null,
                    neighborhood = "Recreio",
                    city = "Rio de Janeiro",
                    state = "RJ",
                    zipCode = "22790-671",
                ),
                monthlyIncome = null,
                politicallyExposed = null,
                agreementData = null,
                upgradeAgreementData = null,
                kycFile = null,
                openForUserReview = false,
                openedForUserReviewAt = null,
                externalId = null,
                livenessEnrollmentVerification = LivenessEnrollmentVerification(),
                riskAnalysisFailedReasons = listOf(),
                riskAnalysisFailedReasonsHistory = listOf(),
                document = null,
                birthDate = LocalDate.parse("1990-01-01"),
                identityValidationStatus = null,
                identityValidationPercentage = null,
                registrationType = RegistrationType.FULL,
                fraudListMatch = false,
                accountClosureDetails = null,
                clientIP = null,
                documentScan = null,
            ),
            bankAccount = InternalBankAccount(
                accountType = AccountType.CHECKING,
                bankNo = 0,
                routingNo = 1,
                accountNo = ********,
                accountDv = "0",
                document = "***********",
                bankAccountMode = BankAccountMode.PHYSICAL,
            ),
        ).getOrElse { throw it }
    }

    @Test
    fun `deve consultar um pedido de investimento corretamente`() {
        val response = investmentManagerAdapter.checkInvestmentRequestStatus(InvestmentManagerExternalId("INVESTMENT_REQUEST_1e7be2a2-8d57-468a-9972-febf47af8c4c")).getOrElse { throw it }

        response.shouldNotBeNull()
        response.status shouldBe InvestmentManagerRequestStatus.COMPLETED
    }

    @Test
    fun `deve consultar a posição atual do usuaŕio em uma meta`() {
        val response = investmentManagerAdapter.userGoalPositions(accountId, GoalId("grupo 2")).getOrElse { throw it }

        response.shouldNotBeNull()
        response.investedValue shouldBe 200_00
        response.positions.size shouldBe 1
    }

    @Test
    fun `deve otimizar o valor da meta do usuário`() {
        val response = investmentManagerAdapter.optimizeMonthlyContribution(
            currentGrossAmount = 2000_00,
            dateToComplete = getLocalDate().plusYears(30).plusDays(4),
            goalNetAmount = 1_000_000_00,
            index = FixedIncomeIndex.CDI,
            indexPercentage = FixedIncomeIndexRate(rate = 110),

        ).getOrElse { throw it }

        response shouldBe 372_36L // ESSE teste vai quebrar se a SELIC mudar
    }
}*/