package ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemption
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionId
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionStatus
import io.kotest.matchers.equality.shouldBeEqualToIgnoringFields
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class GoalRedemptionDbRepositoryTest {
    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val goalRedemptionRepository = GoalRedemptionDbRepository(client = GoalRedemptionDynamoDAO(dynamoDbEnhancedClient))

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        redemptions().forEach {
            goalRedemptionRepository.save(it)
        }
    }

    @ParameterizedTest
    @MethodSource("redemptions")
    fun `deve encontrar pelo id`(goalRedemption: GoalRedemption) {
        goalRedemptionRepository.findOrNull(goalRedemption.id) shouldBeGoalRedemption goalRedemption
    }

    @ParameterizedTest
    @MethodSource("redemptions")
    fun `deve encontrar pelo id da meta`(goalRedemption: GoalRedemption) {
        val redemptions = goalRedemptionRepository.findByGoalId(goalRedemption.goalId)

        redemptions.size shouldBe 1
        redemptions.single() shouldBeGoalRedemption goalRedemption
    }

    @ParameterizedTest
    @MethodSource("redemptions")
    fun `deve encontrar pelo id da carteira`(goalRedemption: GoalRedemption) {
        val redemptions = goalRedemptionRepository.findByWalletId(goalRedemption.walletId)

        redemptions.size shouldBe 1
        redemptions.single() shouldBeGoalRedemption goalRedemption
    }

    private infix fun GoalRedemption?.shouldBeGoalRedemption(goalRedemption: GoalRedemption) {
        this.shouldNotBeNull()
        this.shouldBeEqualToIgnoringFields(
            goalRedemption,
            goalRedemption::createdAt,
            goalRedemption::updatedAt,
        )
    }

    companion object {
        private val processingGoalRedemption = GoalRedemption(
            id = GoalRedemptionId(value = "REDEMPTION-1"),
            goalId = GoalId(value = "GOAL-1"),
            walletId = WalletId(value = "WALLET-1"),
            netAmount = 3275,
            penaltyRate = null,
            status = GoalRedemptionStatus.PROCESSING,
            actionSource = ActionSource.Api(AccountId()),
        )

        private val doneGoalRedemption = GoalRedemption(
            id = GoalRedemptionId(value = "REDEMPTION-2"),
            goalId = GoalId(value = "GOAL-2"),
            walletId = WalletId(value = "WALLET-2"),
            netAmount = 3275,
            penaltyRate = FixedIncomeIndexRate(643),
            status = GoalRedemptionStatus.DONE,
            actionSource = ActionSource.GoalInvestment(AccountId(), GoalId("GOAL-2")),
        )

        @JvmStatic
        fun redemptions() = listOf(processingGoalRedemption, doneGoalRedemption)
    }
}