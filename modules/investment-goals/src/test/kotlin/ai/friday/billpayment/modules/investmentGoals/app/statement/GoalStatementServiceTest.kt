package ai.friday.billpayment.modules.investmentGoals.app.statement

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndex
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestment
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentId
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentRepository
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentStatus
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerService
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemption
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionId
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionRepository
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionStatus
import ai.friday.billpayment.modules.investmentGoals.goalWalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.getOrElse
import arrow.core.right
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import kotlin.test.fail
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class GoalStatementServiceTest {
    private val investmentManagerService = mockk<InvestmentManagerService>()

    private val goalInvestmentRepository = mockk<GoalInvestmentRepository>()
    private val goalRedemptionRepository = mockk<GoalRedemptionRepository>()
    private val goalStatementService = GoalStatementService(
        investmentManagerService = investmentManagerService,
        goalInvestmentRepository = goalInvestmentRepository,
        goalRedemptionRepository = goalRedemptionRepository,
    )

    private val accountId = AccountId()
    private val goalId = GoalId()
    private val today = getLocalDate()

    @Test
    fun `deve retornar uma lista vazia se não houver elementos`() {
        every {
            goalInvestmentRepository.findByGoalId(goalId)
        } returns emptyList()

        every {
            goalRedemptionRepository.findByGoalId(goalId)
        } returns emptyList()

        val result = goalStatementService.getValidGoalStatements(accountId, goalId).getOrElse {
            fail("deveria ser right")
        }

        result.size shouldBe 0
    }

    @Test
    fun `deve ignorar investimentos criados`() {
        every {
            goalInvestmentRepository.findByGoalId(goalId)
        } returns listOf(
            GoalInvestment(
                id = GoalInvestmentId("IID1"),
                goalId = goalId,
                billId = BillId(),
                amount = 100_00,
                dueDate = getLocalDate(),
                paidAt = null,
                extraInstallment = false,
                status = GoalInvestmentStatus.CREATED,
            ),
        )

        every {
            goalRedemptionRepository.findByGoalId(goalId)
        } returns listOf(
            GoalRedemption(
                id = GoalRedemptionId("RID1"),
                goalId = goalId,
                walletId = goalWalletId,
                netAmount = 100_00,
                penaltyRate = null,
                status = GoalRedemptionStatus.CREATED,
                actionSource = ActionSource.Api(accountId),
                createdAt = getZonedDateTime().minusMinutes(5),
                updatedAt = getZonedDateTime().minusMinutes(5),
            ),
        )

        val result = goalStatementService.getValidGoalStatements(accountId, goalId).getOrElse {
            fail("deveria ser right")
        }
        result.size shouldBe 1
        result.single().id.value shouldBe "RID1"
    }

    @Test
    fun `deve retornar os elementos ordenados corretamente quando houver investimentos e resgates`() {
        every {
            goalInvestmentRepository.findByGoalId(goalId)
        } returns listOf(
            GoalInvestment(
                id = GoalInvestmentId("IID1"),
                goalId = goalId,
                billId = BillId(),
                amount = 100_00,
                dueDate = getLocalDate(),
                paidAt = getZonedDateTime().minusMinutes(10),
                extraInstallment = false,
                status = GoalInvestmentStatus.DONE,
                createdAt = getZonedDateTime().minusMinutes(20),
                updatedAt = getZonedDateTime().minusMinutes(10),
            ),
            GoalInvestment(
                id = GoalInvestmentId("IID2"),
                goalId = goalId,
                billId = BillId(),
                amount = 100_00,
                dueDate = getLocalDate(),
                paidAt = null,
                extraInstallment = false,
                status = GoalInvestmentStatus.PROCESSING,
                createdAt = getZonedDateTime().minusMinutes(15),
                updatedAt = getZonedDateTime().minusMinutes(2),
            ),
        )

        every {
            goalRedemptionRepository.findByGoalId(goalId)
        } returns listOf(
            GoalRedemption(
                id = GoalRedemptionId("RID1"),
                goalId = goalId,
                walletId = goalWalletId,
                netAmount = 100_00,
                penaltyRate = null,
                status = GoalRedemptionStatus.PROCESSING,
                actionSource = ActionSource.Api(accountId),
                createdAt = getZonedDateTime().minusMinutes(12),
                updatedAt = getZonedDateTime().minusMinutes(5),
            ),
        )

        val result = goalStatementService.getValidGoalStatements(accountId, goalId).getOrElse {
            fail("deveria ser right")
        }

        result.size shouldBe 3

        result.map { it.id.value } shouldContainExactly listOf("IID1", "RID1", "IID2")
    }

    @ParameterizedTest
    @CsvSource(
        "0.8, 1.0, 80",
        "0.88, 1.0, 88",
        "0.876, 1.0, 88",
        "0.874, 1.0, 87",
        "0.333, 0.5, 67",
        "0.735, 0.735, 100",
    )
    fun calcSavingsToCDIRate(savingsRate: Double, cdiRate: Double, expectedRatio: Int) {
        every {
            investmentManagerService.compareAllRates(any(), any(), any())
        } returns mapOf(
            FixedIncomeIndex.SAVINGS to savingsRate,
            FixedIncomeIndex.CDI to cdiRate,
        ).right()

        val result = goalStatementService.calcSavingsToCDIRate(getLocalDate(), FixedIncomeIndexRate(80))

        result.isRight() shouldBe true
        result.map {
            it.toDouble() shouldBe expectedRatio
        }
    }
}