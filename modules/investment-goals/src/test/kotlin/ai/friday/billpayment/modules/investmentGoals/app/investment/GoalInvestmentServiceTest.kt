package ai.friday.billpayment.modules.investmentGoals.app.investment

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateInvestmentRequest
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.bill.schedule.CancelSchedulePaymentService
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.pfm.CreateWalletBillCategoryError
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.balance
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.modules.investmentGoals.app.bill.CreateInvestmentBillServiceInterface
import ai.friday.billpayment.modules.investmentGoals.app.goal.Goal
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalNotFoundException
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalRepository
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalStatus
import ai.friday.billpayment.modules.investmentGoals.app.goal.MonthlyInstallments
import ai.friday.billpayment.modules.investmentGoals.app.goal.UpdateGoalError
import ai.friday.billpayment.modules.investmentGoals.app.goal.WeeklyInstallmentDay
import ai.friday.billpayment.modules.investmentGoals.app.goal.WeeklyInstallments
import ai.friday.billpayment.modules.investmentGoals.app.goal.toGoal
import ai.friday.billpayment.modules.investmentGoals.createMonthlyGoalCommand
import ai.friday.billpayment.modules.investmentGoals.createWeeklyGoalCommand
import ai.friday.billpayment.modules.investmentGoals.enabledMonthlyCDIProduct
import ai.friday.billpayment.modules.investmentGoals.goalAccountId
import ai.friday.billpayment.modules.investmentGoals.goalWallet
import ai.friday.billpayment.modules.investmentGoals.goalWalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.CapturingSlot
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.verify
import java.math.RoundingMode
import java.time.LocalDate
import java.time.ZonedDateTime
import kotlin.test.fail
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class GoalInvestmentServiceTest {

    val pfmCategory = WalletBillCategory(
        walletId = WalletId(),
        categoryId = PFMCategoryId("existing-category-id"),
        name = "Meta",
        icon = "META",
        enabled = true,
        default = false,
    )

    val invalidCommand = CreateRegularGoalInvestmentCommand(
        goalId = GoalId(),
    )

    private val createInvestmentBillService = mockk<CreateInvestmentBillServiceInterface>()

    private val walletCategoryService = mockk<PFMWalletCategoryService>() {
        every {
            findWalletCategories(any())
        } returns listOf(pfmCategory)
    }

    private val goalInvestmentRepository = mockk<GoalInvestmentRepository>() {
        every {
            save(any())
        } just runs

        every {
            findByGoalId(any())
        } returns emptyList()
    }

    private val cancelSchedulePaymentService = mockk<CancelSchedulePaymentService>() {
        every {
            userCancelScheduledPayment(
                walletId = any(),
                billId = any(),
                member = any(),
                actionSource = any(),
            )
        } returns false
    }

    private val walletRepository = mockk<WalletRepository> {
        every {
            findWallets(goalAccountId)
        } returns listOf(goalWallet)

        every {
            findWallet(goalWallet.id)
        } returns goalWallet

        every {
            findAccountPaymentMethod(goalWallet.id)
        } returns balance
    }

    private val updateBillService = mockk<UpdateBillService> {
        every {
            ignoreBill(any(), any(), any(), any())
        } returns mockk<Bill>().right()
    }

    private val goalInvestmentService = GoalInvestmentService(
        goalRepository = goalRepository,
        createInvestmentBillService = createInvestmentBillService,
        walletCategoryService = walletCategoryService,
        goalInvestmentRepository = goalInvestmentRepository,
        walletRepository = walletRepository,
        updateBillService = updateBillService,
        cancelSchedulePaymentService = cancelSchedulePaymentService,
    )

    @Nested
    @DisplayName("ao buscar a categoria META")
    inner class PFMCategory {
        @Test
        fun `quando a categoria ja existe deve retornar a existente`() {
            val result = goalInvestmentService.getPFMGoalCategoryId(pfmCategory.walletId)

            result.isRight() shouldBe true
            result.map {
                it shouldBe pfmCategory.categoryId
            }

            verify(exactly = 0) {
                walletCategoryService.create(any(), any(), any())
            }
        }

        @Test
        fun `quando a categoria nao existe deve criar uma nova`() {
            every {
                walletCategoryService.findWalletCategories(pfmCategory.walletId)
            } returns emptyList()

            every {
                walletCategoryService.create(pfmCategory.walletId, pfmCategory.name, pfmCategory.icon)
            } returns pfmCategory.right()

            val result = goalInvestmentService.getPFMGoalCategoryId(pfmCategory.walletId)

            result.isRight() shouldBe true
            result.map {
                it shouldBe pfmCategory.categoryId
            }

            verify(exactly = 1) {
                walletCategoryService.create(pfmCategory.walletId, pfmCategory.name, pfmCategory.icon)
            }
        }
    }

    @Nested
    @DisplayName("ao criar um investimento")
    inner class Create {

        @Test
        fun `quando nao encontrar a meta deve retornar erro generico`() {
            every {
                goalRepository.find(invalidCommand.goalId)
            } returns GoalNotFoundException(invalidCommand.goalId).left()

            val result = withGivenDateTime(executionTime) {
                goalInvestmentService.create(invalidCommand)
            }

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<CreateGoalInvestmentError.GenericException>()
                it.exception.shouldBeTypeOf<GoalNotFoundException>()
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentServiceTest#goals")
        fun `quando nao encontrar a categoria e nao conseguir criar deve retornar CouldNotAssignPFMCategory`(goal: Goal) {
            every {
                walletCategoryService.findWalletCategories(goal.walletId)
            } returns emptyList()

            every {
                walletCategoryService.create(any(), any(), any())
            } returns CreateWalletBillCategoryError.AlreadyExists.left()

            val result = goalInvestmentService.create(goal.toCreateRegularGoalInvestmentCommand())

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<CreateGoalInvestmentError.CouldNotAssignPFMCategory>()
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentServiceTest#goals")
        fun `quando a meta estiver concluida não pode criar a bill`(goal: Goal) {
            every {
                goalRepository.find(any())
            } returns goal.copy(status = GoalStatus.COMPLETED).right()

            val result = goalInvestmentService.create(goal.toCreateRegularGoalInvestmentCommand())

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<CreateGoalInvestmentError.GoalCantHaveAnyNewInvestment>()
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentServiceTest#goals")
        fun `quando a meta estiver pausada não pode criar a bill`(goal: Goal) {
            every {
                goalRepository.find(any())
            } returns goal.copy(status = GoalStatus.PAUSED).right()

            val result = goalInvestmentService.create(goal.toCreateRegularGoalInvestmentCommand())

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<CreateGoalInvestmentError.GoalCantHaveAnyNewInvestment>()
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentServiceTest#goals")
        fun `quando nao criar a bill deve retornar erro`(goal: Goal) {
            every {
                goalRepository.find(any())
            } returns goal.right()

            val exception = NoStackTraceException("mocked")

            every {
                createInvestmentBillService.createInvestment(any(), false)
            } returns CreateBillResult.FAILURE.ServerError(exception)

            val result = goalInvestmentService.create(goal.toCreateRegularGoalInvestmentCommand())

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<CreateGoalInvestmentError.CouldNotCreateBill>()
                with(it.failure) {
                    shouldBeTypeOf<CreateBillResult.FAILURE.ServerError>()
                    throwable.message shouldBe exception.message
                }
            }
        }

        private fun setupCreateInvestment(createdBillId: BillId): CapturingSlot<CreateInvestmentRequest> {
            val createInvestmentRequestSlot = slot<CreateInvestmentRequest>()
            every {
                createInvestmentBillService.createInvestment(capture(createInvestmentRequestSlot), false)
            } answers {
                val createInvestmentRequest = firstArg<CreateInvestmentRequest>()
                CreateBillResult.SUCCESS(
                    bill = mockk() {
                        every {
                            billId
                        } returns createdBillId
                        every { dueDate } returns createInvestmentRequest.dueDate
                    },
                )
            }
            return createInvestmentRequestSlot
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentServiceTest#goalsAndFirstDueDates")
        fun `quando criar a bill deve retornar sucesso e arredondar o valor para cima`(goal: Goal, expectedDueDate: LocalDate, endDate: LocalDate, totalInstallments: Int) {
            val createdBillId = BillId()
            val createInvestmentRequestSlot = setupCreateInvestment(createdBillId)

            every {
                goalRepository.find(any())
            } answers {
                goal.copy(endDate = endDate).right()
            }

            val result = withGivenDateTime(executionTime) {
                goalInvestmentService.create(goal.toCreateRegularGoalInvestmentCommand())
            }

            result.isRight() shouldBe true

            with(createInvestmentRequestSlot.captured) {
                goalId shouldBe goal.id
                walletId shouldBe goal.walletId
                dueDate shouldBe expectedDueDate
                description shouldBe "Parcela 1 / $totalInstallments"
                amount shouldBe (goal.installments.amount.toBigDecimal().divide(100L.toBigDecimal(), RoundingMode.UP)).times(100L.toBigDecimal()).toLong()
                with(source) {
                    shouldBeTypeOf<ActionSource.GoalInvestment>()
                    accountId shouldBe goal.accountId
                    goalId shouldBe goal.id
                }
                categoryId shouldBe pfmCategory.categoryId
            }

            val goalInvestmentSlot = slot<GoalInvestment>()
            verify(exactly = 1) {
                goalInvestmentRepository.save(capture(goalInvestmentSlot))
            }
            with(goalInvestmentSlot.captured) {
                goalId shouldBe goal.id
                billId shouldBe createdBillId
                dueDate shouldBe expectedDueDate
                paidAt shouldBe null
                extraInstallment shouldBe false
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentServiceTest#goalsAndNextDueDates")
        fun `quando jah existir investimento ordinario para a meta deve incrementar a parcela e a data de vencimento`(goal: Goal, expectedDueDate: LocalDate, expectedInstallmentNumber: Int, expectedTotalInstallment: Int) {
            val createdBillId = BillId()
            val createInvestmentRequestSlot = setupCreateInvestment(createdBillId)

            every {
                goalRepository.find(any())
            } returns goal.right()

            every {
                goalInvestmentRepository.findByGoalId(goal.id)
            } returns listOf(
                setupInvestment(extraInstallment = true),
            )

            val result = withGivenDateTime(executionTime) {
                goalInvestmentService.create(goal.toCreateRegularGoalInvestmentCommand())
            }

            result.isRight() shouldBe true

            with(createInvestmentRequestSlot.captured) {
                description shouldBe "Parcela $expectedInstallmentNumber / $expectedTotalInstallment"
                dueDate shouldBe expectedDueDate
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentServiceTest#goals")
        fun `quando passar da data da meta da meta nao deve criar a conta`(goal: Goal) {
            every {
                goalInvestmentRepository.findByGoalId(goal.id)
            } returns emptyList()

            every {
                goalRepository.find(any())
            } returns goal.copy(endDate = executionTime.toLocalDate().minusDays(1)).right()

            val result = withGivenDateTime(time = executionTime) {
                goalInvestmentService.create(goal.toCreateRegularGoalInvestmentCommand())
            }

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<CreateGoalInvestmentError.GoalCantHaveAnyNewInvestment>()
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentServiceTest#goals")
        fun `quando jah existir um investimento ordinario da meta ativo nao deve criar a conta`(goal: Goal) {
            val activeInvestment = setupInvestment(paidAt = null, dueDate = getLocalDate())

            every {
                goalInvestmentRepository.findByGoalId(goal.id)
            } returns listOf(
                setupInvestment().copy(dueDate = getLocalDate().minusMonths(1)),
                activeInvestment,
            )

            every {
                goalRepository.find(any())
            } returns goal.right()

            val result = goalInvestmentService.create(goal.toCreateRegularGoalInvestmentCommand())

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<CreateGoalInvestmentError.RegularInvestmentActive>()
                it.goalInvestment.id shouldBe activeInvestment.id
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentServiceTest#goals")
        fun `quando jah existir um investimento extra da meta ativo nao deve criar a conta extra`(goal: Goal) {
            val activeInvestment = setupInvestment(paidAt = null, extraInstallment = true)

            every {
                goalRepository.find(any())
            } returns goal.right()

            every {
                goalInvestmentRepository.findByGoalId(goal.id)
            } returns listOf(
                setupInvestment(),
                activeInvestment,
            )

            val result = goalInvestmentService.create(goal.toCreateExtraGoalInvestmentCommand())

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<CreateGoalInvestmentError.ExtraInvestmentActive>()
                it.goalInvestment.id shouldBe activeInvestment.id
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentServiceTest#goals")
        fun `quando criar a conta extra deve retornar sucesso`(goal: Goal) {
            testExtraInstallmentWithStatus(goal, GoalStatus.ACTIVE)
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentServiceTest#goals")
        fun `quando criar a conta extra deve retornar sucesso mesmo que a meta esteja pausada`(goal: Goal) {
            testExtraInstallmentWithStatus(goal, GoalStatus.PAUSED)
        }

        private fun testExtraInstallmentWithStatus(goal: Goal, status: GoalStatus) {
            val activeInvestment = setupInvestment(paidAt = null, extraInstallment = false)
            val createdBillId = BillId()

            val expectedDueDate = LocalDate.of(2024, 10, 22)

            every {
                goalRepository.find(any())
            } returns goal.copy(status = status).right()

            every {
                goalInvestmentRepository.findByGoalId(goal.id)
            } returns listOf(
                setupInvestment(),
                setupInvestment(paidAt = getZonedDateTime().minusDays(1), extraInstallment = true),
                activeInvestment,
            )

            val createInvestmentRequestSlot = slot<CreateInvestmentRequest>()
            every {
                createInvestmentBillService.createInvestment(capture(createInvestmentRequestSlot), false)
            } answers {
                val createInvestmentRequest = firstArg<CreateInvestmentRequest>()
                CreateBillResult.SUCCESS(

                    bill = mockk() {
                        every {
                            billId
                        } returns createdBillId
                        every { dueDate } returns createInvestmentRequest.dueDate
                    },
                )
            }

            val command = goal.toCreateExtraGoalInvestmentCommand()
            val result = withGivenDateTime(executionTime) {
                goalInvestmentService.create(command)
            }

            result.isRight() shouldBe true

            with(createInvestmentRequestSlot.captured) {
                goalId shouldBe goal.id
                walletId shouldBe goal.walletId
                description shouldBe "Parcela extra 2"
                amount shouldBe command.extraInstallmentAmount.toBigDecimal().divide(100.toBigDecimal(), RoundingMode.UP).times(100.toBigDecimal()).toLong()
                with(source) {
                    shouldBeTypeOf<ActionSource.GoalInvestment>()
                    accountId shouldBe goal.accountId
                    goalId shouldBe goal.id
                }
                dueDate shouldBe expectedDueDate
                categoryId shouldBe pfmCategory.categoryId
            }

            val goalInvestmentSlot = slot<GoalInvestment>()
            verify(exactly = 1) {
                goalInvestmentRepository.save(capture(goalInvestmentSlot))
            }
            with(goalInvestmentSlot.captured) {
                goalId shouldBe goal.id
                billId shouldBe createdBillId
                dueDate shouldBe expectedDueDate
                paidAt shouldBe null
                extraInstallment shouldBe true
            }
        }
    }

    @Nested
    @DisplayName("IgnoreAllGoalInvestments")
    inner class IgnoreAllGoalInvestments {

        @Test
        fun `deve falhar quando algum investimento estiver processando`() {
            every {
                goalRepository.find(any())
            } returns weeklyGoal.right()

            every {
                goalInvestmentRepository.findByGoalId(weeklyGoal.id)
            } returns listOf(
                setupInvestment(paidAt = null, extraInstallment = false).copy(status = GoalInvestmentStatus.PROCESSING),
            )

            val response = goalInvestmentService.ignoreAllGoalInvestments(weeklyGoal.id, ActionSource.GoalInvestment(AccountId(ACCOUNT_ID), weeklyGoal.id)).map {
                fail("Should not have succeeded")
            }.getOrElse { it }

            response.shouldBeTypeOf<UpdateGoalError.InvestmentProcessing>()
        }

        @Test
        fun `deve ignorar investimentos CRIADOS E FAILED apenas`() {
            every {
                goalRepository.find(any())
            } returns weeklyGoal.right()

            val createdInvestment = setupInvestment(paidAt = null, extraInstallment = false).copy(status = GoalInvestmentStatus.CREATED)
            val failedInvestment = setupInvestment(paidAt = null, extraInstallment = false).copy(status = GoalInvestmentStatus.FAILED)
            val doneInvestment = setupInvestment(paidAt = null, extraInstallment = false).copy(status = GoalInvestmentStatus.DONE)
            val canceledInvestment = setupInvestment(paidAt = null, extraInstallment = false).copy(status = GoalInvestmentStatus.CANCELED)

            every {
                goalInvestmentRepository.findByGoalId(weeklyGoal.id)
            } returns listOf(
                createdInvestment,
                failedInvestment,
                doneInvestment,
                canceledInvestment,
            )

            val response = goalInvestmentService.ignoreAllGoalInvestments(weeklyGoal.id, ActionSource.GoalInvestment(AccountId(ACCOUNT_ID), weeklyGoal.id)).getOrElse { fail("Should have succeeded") }

            response shouldBe Unit

            verify(exactly = 1) {
                goalInvestmentRepository.save(
                    withArg {
                        it.status shouldBe GoalInvestmentStatus.CANCELED
                        it.id shouldBe createdInvestment.id
                    },
                )
                goalInvestmentRepository.save(
                    withArg {
                        it.status shouldBe GoalInvestmentStatus.CANCELED
                        it.id shouldBe failedInvestment.id
                    },
                )

                cancelSchedulePaymentService.userCancelScheduledPayment(
                    walletId = goalWalletId,
                    billId = createdInvestment.billId,
                    member = any(),
                    actionSource = any(),
                )

                cancelSchedulePaymentService.userCancelScheduledPayment(
                    walletId = goalWalletId,
                    billId = failedInvestment.billId,
                    member = any(),
                    actionSource = any(),
                )

                updateBillService.ignoreBill(
                    walletId = goalWalletId,
                    billId = createdInvestment.billId,
                    member = any(),
                    actionSource = any(),
                )
                updateBillService.ignoreBill(
                    walletId = goalWalletId,
                    billId = failedInvestment.billId,
                    member = any(),
                    actionSource = any(),
                )
            }

            verify(exactly = 0) {
                goalInvestmentRepository.save(
                    withArg {
                        it.status shouldBe GoalInvestmentStatus.CANCELED
                        it.id shouldBe doneInvestment.id
                    },
                )
                goalInvestmentRepository.save(
                    withArg {
                        it.status shouldBe GoalInvestmentStatus.CANCELED
                        it.id shouldBe canceledInvestment.id
                    },
                )

                cancelSchedulePaymentService.userCancelScheduledPayment(
                    walletId = goalWalletId,
                    billId = doneInvestment.billId,
                    member = any(),
                    actionSource = any(),
                )

                cancelSchedulePaymentService.userCancelScheduledPayment(
                    walletId = goalWalletId,
                    billId = canceledInvestment.billId,
                    member = any(),
                    actionSource = any(),
                )

                updateBillService.ignoreBill(
                    walletId = goalWalletId,
                    billId = doneInvestment.billId,
                    member = any(),
                    actionSource = any(),
                )
                updateBillService.ignoreBill(
                    walletId = goalWalletId,
                    billId = canceledInvestment.billId,
                    member = any(),
                    actionSource = any(),
                )
            }
        }
    }

    private fun setupInvestment(
        id: GoalInvestmentId = GoalInvestmentId(),
        goalId: GoalId = GoalId(),
        billId: BillId = BillId(),
        dueDate: LocalDate = getLocalDate(),
        paidAt: ZonedDateTime? = getZonedDateTime(),
        extraInstallment: Boolean = false,
    ) = GoalInvestment(
        id = id,
        goalId = goalId,
        billId = billId,
        dueDate = dueDate,
        paidAt = paidAt,
        extraInstallment = extraInstallment,
        amount = 100_00,
        status = GoalInvestmentStatus.CREATED,
    )

    private fun Goal.toCreateRegularGoalInvestmentCommand() = CreateRegularGoalInvestmentCommand(
        goalId = this.id,
    )

    private fun Goal.toCreateExtraGoalInvestmentCommand() = CreateExtraGoalInvestmentCommand(
        goalId = this.id,
        extraInstallmentAmount = 150_20,
        accountId = this.accountId,
    )

    companion object {
        private val weeklyGoal = createWeeklyGoalCommand.toGoal(goalWalletId, enabledMonthlyCDIProduct)
        private val monthlyGoal = createMonthlyGoalCommand.toGoal(goalWalletId, enabledMonthlyCDIProduct)

        private val executionTime = ZonedDateTime.of(2024, 10, 22, 0, 0, 0, 0, brazilTimeZone)

        private val goalRepository = mockk<GoalRepository> {
        }

        @JvmStatic
        fun goals() = listOf(weeklyGoal, monthlyGoal)

        @JvmStatic
        fun goalsAndFirstDueDates() = listOf(
            Arguments.of(
                weeklyGoal.copy(createdAt = executionTime),
                LocalDate.of(2024, 10, 25),
                executionTime.plusWeeks(10).toLocalDate(),
                10,
            ),
            Arguments.of(
                monthlyGoal.copy(createdAt = executionTime),
                LocalDate.of(2024, 11, 1),
                executionTime.plusMonths(10).toLocalDate(),
                10,
            ),
        )

        @JvmStatic
        fun goalsAndNextDueDates() = listOf(
            Arguments.of(
                weeklyGoal.copy(endDate = executionTime.plusWeeks(10).toLocalDate(), createdAt = executionTime.minusWeeks(3)),
                LocalDate.of(2024, 10, 25),
                4,
                13,
            ),
            Arguments.of(
                monthlyGoal.copy(endDate = executionTime.plusMonths(8).toLocalDate(), createdAt = executionTime.minusMonths(2)),
                LocalDate.of(2024, 11, 1),
                3,
                10,
            ),
        )

        private fun WeeklyInstallmentDay.toWeeklyInstallments() = WeeklyInstallments(
            amount = 1,
            dayOfWeek = this,
        )

        private fun Int.toMonthlyInstallments() = MonthlyInstallments(
            amount = 1,
            dayOfMonth = this,
        )

        @JvmStatic
        fun installmentsAndDates() = listOf(
            Arguments.of(WeeklyInstallmentDay.MONDAY.toWeeklyInstallments(), LocalDate.of(2024, 5, 20)),
            Arguments.of(WeeklyInstallmentDay.TUESDAY.toWeeklyInstallments(), LocalDate.of(2024, 5, 21)),
            Arguments.of(WeeklyInstallmentDay.WEDNESDAY.toWeeklyInstallments(), LocalDate.of(2024, 5, 15)),
            Arguments.of(WeeklyInstallmentDay.THURSDAY.toWeeklyInstallments(), LocalDate.of(2024, 5, 16)),
            Arguments.of(WeeklyInstallmentDay.FRIDAY.toWeeklyInstallments(), LocalDate.of(2024, 5, 17)),
            Arguments.of(1.toMonthlyInstallments(), LocalDate.of(2024, 6, 1)),
            Arguments.of(5.toMonthlyInstallments(), LocalDate.of(2024, 6, 5)),
            Arguments.of(10.toMonthlyInstallments(), LocalDate.of(2024, 6, 10)),
            Arguments.of(15.toMonthlyInstallments(), LocalDate.of(2024, 5, 15)),
            Arguments.of(20.toMonthlyInstallments(), LocalDate.of(2024, 5, 20)),
            Arguments.of(25.toMonthlyInstallments(), LocalDate.of(2024, 5, 25)),
        )
    }
}