package ai.friday.billpayment.modules.investmentGoals.app.bill

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateInvestmentRequest
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalRepository
import ai.friday.billpayment.modules.investmentGoals.weeklyGoal
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import arrow.core.right
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.DayOfWeek.SATURDAY
import java.time.temporal.TemporalAdjusters
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class CreateInvestmentBillServiceTest {

    private val createInvestmentRequest = CreateInvestmentRequest(
        description = "description",
        dueDate = getLocalDate(),
        amount = 456L,
        source = ActionSource.Api(AccountId(ACCOUNT_ID)),
        categoryId = PFMCategoryId("META_ID"),
        walletId = WalletId("RANDOM-WALLET-ID"),
        goalId = GoalId("GOAL-ID"),
    )

    private val billEventPublisher = mockk<BillEventPublisher>(relaxed = true) {
        every { publish(any(), any()) } answers {
            firstArg<Bill>().apply(secondArg())
        }
    }

    private val categoryService = mockk<PFMWalletCategoryService>()
    private val goalRepository = mockk<GoalRepository>()
    private val createInvestmentBillService = CreateInvestmentBillService(
        categoryService = categoryService,
        billEventPublisher = billEventPublisher,
        goalRepository = goalRepository,
        investmentsStartAtString = "08:00",
        investmentsEndAtString = "16:00",
    )

    @BeforeEach
    fun setUp() {
        every { categoryService.findCategory(any(), any()) } answers {
            WalletBillCategory(
                walletId = WalletId(WALLET_ID),
                categoryId = PFMCategoryId("META_ID"),
                name = "Meta",
                icon = "icon",
                enabled = true,
                default = false,
            )
        }
        every {
            goalRepository.find(any())
        } returns weeklyGoal.right()
    }

    @Test
    fun `deve mudar a data efetiva quando não for dia útil e for um investimento`() {
        val dueDate = getLocalDate().plusMonths(1).with(TemporalAdjusters.dayOfWeekInMonth(1, SATURDAY))

        val result = createInvestmentBillService.createInvestment(
            request = createInvestmentRequest.copy(dueDate = dueDate),
            dryRun = false,
        )

        result.shouldBeTypeOf<CreateBillResult.SUCCESS>()
        result.bill.effectiveDueDate shouldBe dueDate.plusDays(2)

        verify {
            billEventPublisher.publish(
                any(),
                withArg {
                    it.shouldBeTypeOf<BillAdded>()
                    it.assignor shouldBe weeklyGoal.name
                    it.goalId.shouldNotBeNull()
                    it.recurrenceRule.shouldBeNull()
                    it.categoryId?.value shouldBe "META_ID"
                },
            )
        }
    }
}