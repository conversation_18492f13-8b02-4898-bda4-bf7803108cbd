package ai.friday.billpayment.modules.investmentGoals.adapters.api.messaging

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.modules.investmentGoals.adapters.messaging.CreateGoalInvestmentMessageHandler
import ai.friday.billpayment.modules.investmentGoals.app.investment.CreateGoalInvestmentCommand
import ai.friday.billpayment.modules.investmentGoals.app.investment.CreateGoalInvestmentError
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentService
import ai.friday.billpayment.modules.investmentGoals.app.messaging.CreateGoalInvestmentMessage
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message

class CreateGoalInvestmentMessageHandlerTest {

    private val service = mockk<GoalInvestmentService>()

    private val handler = CreateGoalInvestmentMessageHandler(
        amazonSQS = mockk(),
        configuration = mockk(),
        goalInvestmentService = service,
        createGoalInvestmentQueueName = "create-queue",
    )

    private val messageBody = CreateGoalInvestmentMessage(
        goalId = "GOAL-ID",
        extraInstallment = false,
        extraInstallmentAmount = null,
        accountId = ACCOUNT_ID,
    )

    private fun buildMessage(messageBody: CreateGoalInvestmentMessage) =
        Message.builder()
            .body(
                getObjectMapper().writeValueAsString(
                    messageBody,
                ),
            ).build()

    @Test
    fun `deve remover da fila em caso de sucesso`() {
        val slot = slot<CreateGoalInvestmentCommand>()

        every {
            service.create(capture(slot))
        } returns Unit.right()

        val result = handler.handleMessage(buildMessage(messageBody))

        result.shouldDeleteMessage shouldBe true

        slot.captured.goalId.value shouldBe messageBody.goalId
    }

    @Test
    fun `deve manter na fila em caso de erro retentavel`() {
        every {
            service.create(any())
        } returns mockk<CreateGoalInvestmentError> { every { retryable } returns true }.left()

        val result = handler.handleMessage(buildMessage(messageBody))

        result.shouldDeleteMessage shouldBe false
    }

    @Test
    fun `deve remover da fila em caso de erro nao retentavel`() {
        every {
            service.create(any())
        } returns mockk<CreateGoalInvestmentError> { every { retryable } returns false }.left()

        val result = handler.handleMessage(buildMessage(messageBody))

        result.shouldDeleteMessage shouldBe true
    }

    @Test
    fun `deve manter na fila em caso de erro`() {
        val result = handler.handleError(buildMessage(messageBody), IllegalStateException())

        result.shouldDeleteMessage shouldBe false
    }

    @Test
    fun `deve falhar e remover da fila se for regular e tiver valor`() {
        val result = handler.handleMessage(
            buildMessage(
                CreateGoalInvestmentMessage(
                    goalId = "GOAL-ID",
                    extraInstallment = false,
                    extraInstallmentAmount = 10,
                    accountId = ACCOUNT_ID,
                ),
            ),
        )

        verify {
            service wasNot Called
        }

        result.shouldDeleteMessage shouldBe true
    }

    @Test
    fun `deve falhar e remover da fila se for extra e não tiver valor`() {
        val result = handler.handleMessage(
            buildMessage(
                CreateGoalInvestmentMessage(
                    goalId = "GOAL-ID",
                    extraInstallment = true,
                    extraInstallmentAmount = null,
                    accountId = ACCOUNT_ID,
                ),
            ),
        )

        verify {
            service wasNot Called
        }

        result.shouldDeleteMessage shouldBe true
    }

    @Test
    fun `deve falhar e remover da fila se for extra e valor não for positivo`() {
        val result = handler.handleMessage(
            buildMessage(
                CreateGoalInvestmentMessage(
                    goalId = "GOAL-ID",
                    extraInstallment = true,
                    extraInstallmentAmount = 0,
                    accountId = ACCOUNT_ID,
                ),
            ),
        )

        verify {
            service wasNot Called
        }

        result.shouldDeleteMessage shouldBe true
    }
}