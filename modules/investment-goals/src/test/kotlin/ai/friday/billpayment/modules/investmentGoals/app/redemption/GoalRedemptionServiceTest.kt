package ai.friday.billpayment.modules.investmentGoals.app.redemption

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.cashIn.StartCashInMessage
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.payment.NoPaymentData
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.toPayer
import ai.friday.billpayment.modules.investmentGoals.app.goal.Goal
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalNotFoundException
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalService
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomePositionId
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeProvider
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeRedemptionQuotation
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeRedemptionQuotations
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeRedemptionType
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerException
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerExternalId
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerService
import ai.friday.billpayment.modules.investmentGoals.app.investment.UserGoalAvailableToRedeemPositions
import ai.friday.billpayment.modules.investmentGoals.monthlyGoal
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.equality.shouldBeEqualToIgnoringFields
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail

class GoalRedemptionServiceTest {

    private val goalRedemptionRepository = mockk<GoalRedemptionRepository>(relaxUnitFun = true)
    private val transactionService = mockk<TransactionService>(relaxUnitFun = true)
    private val accountRepository = mockk<AccountRepository>()
    private val goalService = mockk<GoalService>() {
        every {
            findGoal(any(), any())
        } answers { GoalNotFoundException(firstArg()).left() }

        every {
            calculateCurrentPenaltyRate(any<Goal>())
        } returns NoStackTraceException("mocked exception").left()
    }

    private val investmentManagerService = mockk<InvestmentManagerService> {
        every {
            userGoalPositionAvailableToRedeem(any(), any(), any())
        } returns InvestmentManagerException(NoStackTraceException("mocked exception")).left()

        every {
            quoteRedemption(any(), any(), any(), any())
        } returns InvestmentManagerException(NoStackTraceException("mocked exception")).left()
    }

    private val messagePublisher = mockk<MessagePublisher>(relaxUnitFun = true)

    private val goalRedemptionService = GoalRedemptionService(
        goalRedemptionRepository = goalRedemptionRepository,
        transactionService = transactionService,
        accountRepository = accountRepository,
        goalService = goalService,
        investmentManagerService = investmentManagerService,
        messagePublisher = messagePublisher,
        goalInstrumentationRepository = mockk(relaxUnitFun = true),
        startCashInQueueName = "queueName",
    )

    private fun setupFindGoal(): Goal {
        every {
            goalService.findGoal(monthlyGoal.id, monthlyGoal.accountId)
        } returns monthlyGoal.right()

        return monthlyGoal
    }

    private fun Goal.setupPenalty(): FixedIncomeIndexRate {
        val goal = this
        val penalty = FixedIncomeIndexRate(80)

        every {
            goalService.calculateCurrentPenaltyRate(goal)
        } returns penalty.right()

        return penalty
    }

    @Nested
    @DisplayName("ao consultar o valor total disponivel para resgate")
    inner class AvailableToRedeemTest {

        @Test
        fun `quando nao encontra a meta deve retornar erro`() {
            val result = goalRedemptionService.availableToRedeem(AccountId(), GoalId())

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<GoalNotFoundException>()
            }
        }

        @Test
        fun `quando a meta nao pertence ao usuario deve retornar erro`() {
            val goal = setupFindGoal()

            val result = goalRedemptionService.availableToRedeem(AccountId(), goal.id).map { fail("deveria ser left") }.getOrElse { it }

            result.shouldBeTypeOf<GoalNotFoundException>()
        }

        @Test
        fun `quando nao consegue calcular a penalidade deve retornar erro`() {
            val goal = setupFindGoal()

            val result = goalRedemptionService.availableToRedeem(goal.accountId, goal.id)

            result.isLeft() shouldBe true
            result.mapLeft {
                it.message shouldBe "mocked exception"
            }
        }

        @Test
        fun `quando nao consegue calcular o valor disponivel deve retornar erro`() {
            val goal = setupFindGoal()
            goal.setupPenalty()

            val result = goalRedemptionService.availableToRedeem(goal.accountId, goal.id)

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<InvestmentManagerException>()
                it.throwable.message shouldBe "mocked exception"
            }
        }

        @Test
        fun `quando consegue calcular o valor disponivel deve retornar o valor liquido`() {
            val goal = setupFindGoal()
            val penalty = goal.setupPenalty()

            every {
                investmentManagerService.userGoalPositionAvailableToRedeem(goal.accountId, goal.id, penalty)
            } returns UserGoalAvailableToRedeemPositions(
                accountId = goal.accountId,
                goalId = goal.id,
                positions = listOf(),
                netValue = 6528,
            ).right()

            val result = goalRedemptionService.availableToRedeem(goal.accountId, goal.id)

            result.isRight() shouldBe true
            result.map {
                it shouldBe 6528
            }
        }
    }

    @Nested
    @DisplayName("ao cotar um resgate")
    inner class QuoteRedemptionTest {

        val redemptionAmount = 9573L

        @Test
        fun `quando nao encontra a meta deve retornar erro`() {
            val result = goalRedemptionService.quoteRedemption(AccountId(), GoalId(), redemptionAmount)

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<GoalNotFoundException>()
            }
        }

        @Test
        fun `quando a meta nao pertence ao usuario deve retornar erro`() {
            val goal = setupFindGoal()

            val result = goalRedemptionService.quoteRedemption(AccountId(), goal.id, redemptionAmount).map { fail("deveria ser left") }.getOrElse { it }

            result.shouldBeTypeOf<GoalNotFoundException>()
        }

        @Test
        fun `quando nao consegue calcular a penalidade deve retornar erro`() {
            val goal = setupFindGoal()

            val result = goalRedemptionService.quoteRedemption(goal.accountId, goal.id, redemptionAmount)

            result.isLeft() shouldBe true
            result.mapLeft {
                it.message shouldBe "mocked exception"
            }
        }

        @Test
        fun `quando nao consegue cotar o resgate deve retornar erro`() {
            val goal = setupFindGoal()
            goal.setupPenalty()

            val result = goalRedemptionService.quoteRedemption(goal.accountId, goal.id, redemptionAmount)

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<InvestmentManagerException>()
                it.throwable.message shouldBe "mocked exception"
            }
        }

        @Test
        fun `quando consegue cotar o resgate deve retornar a cotacao`() {
            val goal = setupFindGoal()
            val penalty = goal.setupPenalty()

            val penaltyValue = 1830_73L

            val quotation = FixedIncomeRedemptionQuotations(
                accountId = goal.accountId,
                goalId = goal.id,
                quotations = listOf(
                    FixedIncomeRedemptionQuotation(
                        quotationId = InvestmentManagerExternalId("9584099f-264d-4f5d-9c16-08dd0025e822"),
                        provider = FixedIncomeProvider.ARBI,
                        positionId = FixedIncomePositionId("611473"),
                        principalValue = 4320000,
                        grossValue = 4935372, // 5176223, ->2408_51
                        netValue = 4843067, // 5047790, -> 2047_23
                        quantity = *********,
                        pu = 0.01,
                        penaltyRate = FixedIncomeIndexRate(80),
                        taxValue = 92305, // 128433,
                        redemptionType = FixedIncomeRedemptionType.PARTIAL,
                        penaltyValue = penaltyValue,
                    ),
                ),
                netValue = 4843067,
                principalValue = 4320000,
                grossValue = 4935372,
                taxValue = 92305, // ,128433,
                penaltyValue = 1830_73L,
            )

            every {
                investmentManagerService.quoteRedemption(goal.accountId, goal.id, redemptionAmount, penalty)
            } returns quotation.right()

            val result = goalRedemptionService.quoteRedemption(goal.accountId, goal.id, redemptionAmount)

            result.isRight() shouldBe true
            result.map {
                it.shouldBeEqualToIgnoringFields(quotation, quotation::penaltyValue)
                it.penaltyValue shouldBe penaltyValue
            }
        }

        @Test
        fun `quando consegue cotar o resgate deve retornar a cotacao com o penalty correto`() {
            val goal = setupFindGoal()
            val penalty = goal.setupPenalty()

            val quotation = FixedIncomeRedemptionQuotations(
                accountId = goal.accountId,
                goalId = goal.id,
                quotations = listOf(
                    FixedIncomeRedemptionQuotation(
                        quotationId = InvestmentManagerExternalId("9584099f-264d-4f5d-9c16-08dd0025e822"),
                        provider = FixedIncomeProvider.ARBI,
                        positionId = FixedIncomePositionId("611473"),
                        principalValue = 4999,
                        grossValue = 5002,
                        netValue = 5000,
                        quantity = 4999,
                        pu = 0.01,
                        penaltyRate = FixedIncomeIndexRate(80),
                        taxValue = 2,
                        redemptionType = FixedIncomeRedemptionType.PARTIAL,
                        penaltyValue = 0,
                    ),
                ),
                netValue = 5000,
                principalValue = 4999,
                grossValue = 5002,
                taxValue = 2,
                penaltyValue = 0,
            )

            every {
                investmentManagerService.quoteRedemption(goal.accountId, goal.id, redemptionAmount, penalty)
            } returns quotation.right()

            val result = goalRedemptionService.quoteRedemption(goal.accountId, goal.id, redemptionAmount)

            result.isRight() shouldBe true
            result.map {
                it.shouldBeEqualToIgnoringFields(quotation, quotation::penaltyValue)
                it.penaltyValue shouldBe 0
            }
        }
    }

    @Nested
    @DisplayName("ao solicitar um resgate")
    inner class RequestRedemptionTest {

        private fun Goal.toCommand() = GoalRedemptionCommand(
            goalId = id,
            netAmount = 759351,
            actionSource = ActionSource.Api(accountId),
        )

        private fun Goal.toInvalidUserCommand() = GoalRedemptionCommand(
            goalId = id,
            netAmount = 759351,
            actionSource = ActionSource.Api(AccountId()),
        )

        @Test
        fun `quando nao encontra a meta deve retornar erro`() {
            val result = goalRedemptionService.requestRedemption(
                GoalRedemptionCommand(
                    goalId = GoalId(),
                    netAmount = 759351,
                    actionSource = ActionSource.Api(AccountId()),
                ),
            )

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<GoalNotFoundException>()
            }
        }

        @Test
        fun `quando a meta nao pertence ao usuario deve retornar erro`() {
            val goal = setupFindGoal()

            val result = goalRedemptionService.requestRedemption(goal.toInvalidUserCommand()).map { fail("deveria ser left") }.getOrElse { it }

            result.shouldBeTypeOf<GoalNotFoundException>()
        }

        @Test
        fun `quando nao consegue calcular a penalidade deve retornar erro`() {
            val goal = setupFindGoal()

            val result = goalRedemptionService.requestRedemption(goal.toCommand())

            result.isLeft() shouldBe true
            result.mapLeft {
                it.message shouldBe "mocked exception"
            }
        }

        @Test
        fun `quando consegue calcular a penalidade deve iniciar o resgate`() {
            val goal = setupFindGoal()
            val penalty = goal.setupPenalty()

            every {
                accountRepository.findById(goal.accountId)
            } returns ACCOUNT

            val command = goal.toCommand()

            val result = goalRedemptionService.requestRedemption(command)

            result.isRight() shouldBe true
            result.map { transactionId ->
                val redemptionSlot = slot<GoalRedemption>()
                val transactionSlot = slot<Transaction>()
                val messageSlot = slot<StartCashInMessage>()

                verify {
                    goalRedemptionRepository.save(capture(redemptionSlot))
                    transactionService.save(capture(transactionSlot))
                    messagePublisher.sendMessage(goalRedemptionService.startCashInQueueName, capture(messageSlot))
                }

                with(redemptionSlot.captured) {
                    goalId shouldBe command.goalId
                    walletId shouldBe goal.walletId
                    netAmount shouldBe command.netAmount
                    penaltyRate shouldBe penalty
                    actionSource shouldBe command.actionSource
                    status shouldBe GoalRedemptionStatus.CREATED
                }

                with(transactionSlot.captured) {
                    id shouldBe transactionId
                    type shouldBe TransactionType.GOAL_REDEMPTION
                    actionSource shouldBe command.actionSource
                    payer shouldBe ACCOUNT.toPayer()
                    paymentData shouldBe NoPaymentData
                    with(settlementData.settlementTarget) {
                        this.shouldBeTypeOf<GoalRedemption>()
                        this.goalId shouldBe goalId
                        this.netAmount shouldBe command.netAmount
                        this.penaltyRate shouldBe penalty
                    }
                    walletId shouldBe goal.walletId
                    status shouldBe TransactionStatus.PROCESSING
                }

                messageSlot.captured.transactionId shouldBe transactionId.value
            }
        }
    }

    @Nested
    @DisplayName("ao iniciar um resgate")
    inner class StartRedemptionTest
}