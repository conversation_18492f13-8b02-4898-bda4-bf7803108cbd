package ai.friday.billpayment.modules.investmentGoals.app.redemption

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BalanceAuthorizationPaymentOperationConverter
import ai.friday.billpayment.adapters.dynamodb.BankTransferSettlementOperationConverter
import ai.friday.billpayment.adapters.dynamodb.BillSettlementTargetConverter
import ai.friday.billpayment.adapters.dynamodb.BoletoSettlementOperationConverter
import ai.friday.billpayment.adapters.dynamodb.CreditCardAuthorizationPaymentOperationConverter
import ai.friday.billpayment.adapters.dynamodb.CreditCardCashInSettlementTargetConverter
import ai.friday.billpayment.adapters.dynamodb.CreditCardChargebackTrackDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbTransactionRepository
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionEntityConverter
import ai.friday.billpayment.adapters.messaging.StartCashInMessageHandler
import ai.friday.billpayment.adapters.messaging.TransactionRetryHandler
import ai.friday.billpayment.adapters.messaging.retryCashinTransactionEventType
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.cashIn.DefaultCashInExecutor
import ai.friday.billpayment.app.cashIn.DefaultCashInHandlerLocator
import ai.friday.billpayment.app.cashIn.StartCashInMessage
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.ScheduledBillPaymentService
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.payment.NoPaymentData
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.toPayer
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb.GoalDbRepository
import ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb.GoalDynamoDAO
import ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb.GoalInvestmentPaymentOperationConverter
import ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb.GoalInvestmentSettlementOperationConverter
import ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb.GoalProductDbRepository
import ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb.GoalProductDynamoDAO
import ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb.GoalRedemptionDbRepository
import ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb.GoalRedemptionDynamoDAO
import ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb.GoalRedemptionSettlementOperationConverter
import ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb.GoalRedemptionSettlementTargetConverter
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryService
import ai.friday.billpayment.modules.investmentGoals.app.goal.Goal
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalNotFoundException
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalService
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndex
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeInvestmentRedemptionResult
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeProvider
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentRepository
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalRequestOperationId
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerException
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerExternalId
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerRequestStatus
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerService
import ai.friday.billpayment.modules.investmentGoals.app.messaging.GoalMessagePublisher
import ai.friday.billpayment.modules.investmentGoals.app.payment.GoalRedemptionCashInHandler
import ai.friday.billpayment.modules.investmentGoals.app.payment.GoalsInvestmentNotificationService
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductService
import ai.friday.billpayment.modules.investmentGoals.app.statement.GoalStatementService
import ai.friday.billpayment.modules.investmentGoals.monthlyGoal
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import software.amazon.awssdk.services.sqs.model.Message

class RequestRedemptionIntegrationTest {

    private val investmentManagerService = mockk<InvestmentManagerService> {
        every {
            compareAllRates(any(), any(), any())
        } returns InvestmentManagerException(NoStackTraceException("compareAllRates exception")).left()
    }

    private val messagePublisher = mockk<MessagePublisher>(relaxUnitFun = true)
    private val goalMessagePublisher = mockk<GoalMessagePublisher>(relaxUnitFun = true)

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val accountDAO = AccountDynamoDAO(dynamoDbEnhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(dynamoDbEnhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(dynamoDbEnhancedClient)
    private val nsuDAO = NSUDynamoDAO(dynamoDbEnhancedClient)
    private val transactionDAO = TransactionDynamoDAO(dynamoDbEnhancedClient)
    private val creditCardChargebackTrackDAO = CreditCardChargebackTrackDynamoDAO(dynamoDbEnhancedClient)
    private val transactionDynamo = TransactionDynamo(dynamoDbEnhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )
    private val goalRepository = GoalDbRepository(GoalDynamoDAO(dynamoDbEnhancedClient))
    private val goalProductRepository = GoalProductDbRepository(GoalProductDynamoDAO(dynamoDbEnhancedClient))
    private val goalRedemptionRepository = GoalRedemptionDbRepository(GoalRedemptionDynamoDAO(dynamoDbEnhancedClient))

    private val transactionService = TransactionService(
        accountRepository = accountRepository,
        updateBillService = mockk(),
        notificationAdapter = mockk(),
        walletService = mockk(),
        transactionRepository = DynamoDbTransactionRepository(
            transactionDAO = transactionDAO,
            creditCardChargebackTrackDAO = creditCardChargebackTrackDAO,
            accountRepository = accountRepository,
            transactionEntityConverter = goalTransactionEntityConverter(),
        ),
    )

    private val goalProductService = GoalProductService(
        goalProductRepository = goalProductRepository,
        goalRepository = goalRepository,
        investmentManagerService = investmentManagerService,
    )

    private val goalInvestmentRepository = mockk<GoalInvestmentRepository>()

    private val goalCategoryService = mockk<GoalCategoryService> {
        every {
            findAll()
        } returns emptyList()

        every {
            findAllEnabled()
        } returns emptyList()
    }

    private val goalService = GoalService(
        goalRepository = goalRepository,
        goalProductService = goalProductService,
        goalStatementService = GoalStatementService(
            investmentManagerService = investmentManagerService,
            goalInvestmentRepository = goalInvestmentRepository,
            goalRedemptionRepository = goalRedemptionRepository,
        ),
        goalInvestmentService = mockk(),
        accountRepository = accountRepository,
        accountRegisterRepository = mockk(),
        walletRepository = mockk(),
        investmentManagerService = investmentManagerService,
        goalMessagePublisher = goalMessagePublisher,
        userAddressConfiguration = mockk(),
        updateBillService = mockk(),
        goalInstrumentationRepository = mockk(relaxUnitFun = true),
        goalsInvestmentNotificationService = mockk(),
        goalCategoryService = goalCategoryService,
        userJourneyService = mockk(relaxUnitFun = true),
    )

    private val goalRedemptionService = GoalRedemptionService(
        goalRedemptionRepository = goalRedemptionRepository,
        transactionService = transactionService,
        accountRepository = accountRepository,
        goalService = goalService,
        investmentManagerService = investmentManagerService,
        messagePublisher = messagePublisher,
        goalInstrumentationRepository = mockk(relaxUnitFun = true),
        startCashInQueueName = "queueName",
    )

    private val scheduledBillPaymentService = mockk<ScheduledBillPaymentService>(relaxUnitFun = true)

    private val balanceService = mockk<BalanceService>(relaxUnitFun = true)
    private val walletService = mockk<WalletService> {
        every {
            findWallet(any())
        } returns WalletFixture().buildWallet()
    }
    private val internalBankService = mockk<InternalBankService> {
        every {
            synchronizeBankAccount(any(), any())
        } returns true
    }
    private val goalsInvestmentNotificationService = mockk<GoalsInvestmentNotificationService>(relaxed = true)

    private val cashInExecutor = DefaultCashInExecutor(
        transactionService = transactionService,
        cashInHandlerLocator = DefaultCashInHandlerLocator(
            listOf(
                GoalRedemptionCashInHandler(
                    waitTime = 0,
                    poolingInterval = 1,
                    investmentManagerService = investmentManagerService,
                    goalRedemptionRepository = goalRedemptionRepository,
                    balanceService = balanceService,
                    walletService = walletService,
                    internalBankService = internalBankService,
                    goalsInvestmentNotificationService = goalsInvestmentNotificationService,
                    goalMessagePublisher = goalMessagePublisher,
                ),
            ),
        ),
        messagePublisher = messagePublisher,
        scheduledBillPaymentService = scheduledBillPaymentService,
    )

    private fun setupFindGoal(): Goal {
        val goal = monthlyGoal.copy(
            accountId = ACCOUNT.accountId,
        )

        goalRepository.save(goal)

        return goal
    }

    private fun Goal.setupPenalty(): FixedIncomeIndexRate {
        val penalty = FixedIncomeIndexRate(80)

        every {
            investmentManagerService.compareAllRates(any(), any(), any())
        } returns mapOf(
            FixedIncomeIndex.SAVINGS to penalty.rate.toDouble() / 100,
            FixedIncomeIndex.CDI to 1.0,
        ).right()

        return penalty
    }

    private fun goalTransactionEntityConverter() = TransactionEntityConverter(
        settlementOperationConverters = listOf(
            BoletoSettlementOperationConverter(),
            BankTransferSettlementOperationConverter(),
            GoalInvestmentSettlementOperationConverter(),
            GoalRedemptionSettlementOperationConverter(),
        ),
        paymentOperationConverters = listOf(
            CreditCardAuthorizationPaymentOperationConverter(),
            BalanceAuthorizationPaymentOperationConverter(),
            GoalInvestmentPaymentOperationConverter(),
        ),
        settlementTargetConverters = listOf(
            BillSettlementTargetConverter(mockk()),
            CreditCardCashInSettlementTargetConverter(mockk()),
            GoalRedemptionSettlementTargetConverter(goalRedemptionRepository),
        ),
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    private fun Goal.toCommand() = GoalRedemptionCommand(
        goalId = id,
        netAmount = 759351,
        actionSource = ActionSource.Api(accountId),
    )

    private fun Goal.toInvalidUserCommand() = GoalRedemptionCommand(
        goalId = id,
        netAmount = 759351,
        actionSource = ActionSource.Api(AccountId()),
    )

    private val startHandler = StartCashInMessageHandler(
        amazonSQS = mockk(),
        configuration = mockk(),
        transactionService = transactionService,
        cashInExecutor = cashInExecutor,
        startCashInQueueName = "queueName",
    )

    private fun TransactionId.toStartMessage() = Message.builder()
        .body(
            getObjectMapper().writeValueAsString(
                StartCashInMessage(
                    transactionId = value,
                ),
            ),
        ).build()

    private fun TransactionStatus?.toExpectedPublisherCount() = if (this in listOf(TransactionStatus.COMPLETED, TransactionStatus.FAILED)) {
        0
    } else {
        1
    }

    private fun TransactionStatus?.toExpectedSynchronizeCount() = if (this in listOf(TransactionStatus.COMPLETED, TransactionStatus.FAILED)) {
        1
    } else {
        0
    }

    private fun requestRedemption(): Pair<Goal, TransactionId> {
        val goal = setupFindGoal()
        goal.setupPenalty()
        accountRepository.save(ACCOUNT)

        val command = goal.toCommand()

        val result = goalRedemptionService.requestRedemption(command)
        result.isRight() shouldBe true

        return result.map {
            goal to it
        }.getOrElse {
            throw it
        }
    }

    @Nested
    @DisplayName("ao solicitar um resgate")
    inner class RequestRedemptionTest {

        @Test
        fun `quando nao encontra a meta deve retornar erro`() {
            val result = goalRedemptionService.requestRedemption(
                GoalRedemptionCommand(
                    goalId = GoalId(),
                    netAmount = 759351,
                    actionSource = ActionSource.Api(AccountId()),
                ),
            )

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<GoalNotFoundException>()
            }
        }

        @Test
        fun `quando a meta nao pertence ao usuario deve retornar erro`() {
            val goal = setupFindGoal()

            val result = goalRedemptionService.requestRedemption(goal.toInvalidUserCommand()).map { fail("deveria ser left") }.getOrElse { it }

            result.shouldBeTypeOf<GoalNotFoundException>()
        }

        @Test
        fun `quando nao consegue calcular a penalidade deve retornar erro`() {
            val goal = setupFindGoal()

            val result = goalRedemptionService.requestRedemption(goal.toCommand())

            result.isLeft() shouldBe true
            result.mapLeft {
                it.message shouldContain "compareAllRates exception"
            }
        }

        @Test
        fun `quando consegue calcular a penalidade deve iniciar o resgate`() {
            val goal = setupFindGoal()
            val penalty = goal.setupPenalty()

            accountRepository.save(ACCOUNT)

            val command = goal.toCommand()

            val result = goalRedemptionService.requestRedemption(command)

            result.isRight() shouldBe true
            result.map { transactionId ->

                val redemptionSlot = goalRedemptionRepository.findByGoalId(goal.id).single()
                with(redemptionSlot) {
                    goalId shouldBe command.goalId
                    walletId shouldBe goal.walletId
                    netAmount shouldBe command.netAmount
                    penaltyRate shouldBe penalty
                    actionSource shouldBe command.actionSource
                    status shouldBe GoalRedemptionStatus.CREATED
                }

                val transactionSlot = transactionService.findTransactionById(transactionId)
                with(transactionSlot) {
                    id shouldBe transactionId
                    type shouldBe TransactionType.GOAL_REDEMPTION
                    actionSource shouldBe command.actionSource
                    payer shouldBe ACCOUNT.toPayer()
                    paymentData shouldBe NoPaymentData
                    with(settlementData.settlementTarget) {
                        this.shouldBeTypeOf<GoalRedemption>()
                        this.goalId shouldBe goalId
                        this.netAmount shouldBe command.netAmount
                        this.penaltyRate shouldBe penalty
                    }
                    walletId shouldBe goal.walletId
                    status shouldBe TransactionStatus.PROCESSING
                }

                val messageSlot = slot<StartCashInMessage>()
                verify {
                    messagePublisher.sendMessage(goalRedemptionService.startCashInQueueName, capture(messageSlot))
                }
                messageSlot.captured.transactionId shouldBe transactionId.value
            }
        }
    }

    @Nested
    @DisplayName("ao iniciar um resgate")
    inner class StartRedemptionTest {

        @ParameterizedTest
        @CsvSource(
            "NOT_FOUND, FAILED    , FAILED    ",
            "UNKNOWN  , PROCESSING, PROCESSING",
            "CREATED  , PROCESSING, PROCESSING",
            "REQUESTED, PROCESSING, PROCESSING",
            "COMPLETED, COMPLETED , DONE      ",
            "FAILED   , FAILED    , FAILED    ",
        )
        fun `quando inicia com sucesso deve atualizar a transacao, o resgate e remover da fila`(
            redemptionStatus: InvestmentManagerRequestStatus,
            expectedTransactionStatus: TransactionStatus,
            expectedRedemptionStatus: GoalRedemptionStatus,
        ) {
            every {
                investmentManagerService.redemption(any(), any(), any(), any(), any())
            } returns FixedIncomeInvestmentRedemptionResult(
                operationId = GoalRequestOperationId("OPERATION_1"),
                provider = FixedIncomeProvider.ARBI,
                status = redemptionStatus,
                redemptionGroupExternalId = null,
                redemptionExternalIds = listOf(),
                errorMessage = null,
                amounts = null,
            ).right()

            val (goal, transactionId) = requestRedemption()

            val result = startHandler.handleMessage(transactionId.toStartMessage())

            result.shouldDeleteMessage shouldBe true

            val transaction = transactionService.findTransactionById(transactionId)
            transaction.status shouldBe expectedTransactionStatus

            val redemption = goalRedemptionRepository.findByGoalId(goal.id).single()
            redemption.status shouldBe expectedRedemptionStatus

            verify(exactly = expectedTransactionStatus.toExpectedPublisherCount()) {
                messagePublisher.sendRetryCashinMessage(transactionId, null)
            }

            verify(exactly = expectedTransactionStatus.toExpectedSynchronizeCount()) {
                balanceService.invalidate(any())
                internalBankService.synchronizeBankAccount(any(), any())
            }

            verify(exactly = if (expectedRedemptionStatus == GoalRedemptionStatus.DONE) 1 else 0) {
                goalMessagePublisher.publishCheckGoalCompletion(any(), true)
            }
        }
    }

    @Nested
    @DisplayName("ao retentar um resgate")
    inner class RetryRedemptionTest {

        private val transactionRepository = mockk<TransactionRepository>()
        private val retryHandler = TransactionRetryHandler(
            amazonSQS = mockk(),
            configuration = mockk() { every { rollbackTransactionQueueName } returns "queueName" },
            retryTransaction = mockk(),
            cashInExecutor = cashInExecutor,
            messagePublisher = messagePublisher,
            transactionRepository = transactionRepository,
        )

        private fun TransactionId.toRetryMessage() = Message.builder()
            .body(
                getObjectMapper().writeValueAsString(
                    mapOf(
                        "transactionId" to value,
                        "eventType" to retryCashinTransactionEventType,
                    ),
                ),
            ).build()

        private fun startRedemption(): Pair<Goal, TransactionId> {
            val goal = setupFindGoal()
            goal.setupPenalty()
            accountRepository.save(ACCOUNT)

            val command = goal.toCommand()

            val result = goalRedemptionService.requestRedemption(command)
            result.isRight() shouldBe true

            return result.map {
                startHandler.handleMessage(it.toStartMessage())
                goal to it
            }.getOrElse {
                throw it
            }
        }

        @ParameterizedTest
        @CsvSource(
            "NOT_FOUND, FAILED    , FAILED    ",
            "COMPLETED, COMPLETED , DONE      ",
            "FAILED   , FAILED    , FAILED    ",
        )
        fun `quando retenta e terminou deve atualizar a transacao, o resgate e remover da fila`(
            checkInvestmentRedemptionStatus: InvestmentManagerRequestStatus,
            expectedTransactionStatus: TransactionStatus,
            expectedRedemptionStatus: GoalRedemptionStatus,
        ) {
            every {
                investmentManagerService.checkInvestmentRedemptionStatus(any<AccountId>(), any())
            } returns FixedIncomeInvestmentRedemptionResult(
                operationId = GoalRequestOperationId("OPERATION_1"),
                provider = FixedIncomeProvider.ARBI,
                status = checkInvestmentRedemptionStatus,
                redemptionGroupExternalId = InvestmentManagerExternalId("externalId"),
                redemptionExternalIds = listOf(),
                errorMessage = null,
                amounts = null,
            ).right()

            val (goal, transactionId) = startRedemption()

            val result = retryHandler.handleMessage(transactionId.toRetryMessage())

            result.shouldDeleteMessage shouldBe true

            val transaction = transactionService.findTransactionById(transactionId)
            transaction.status shouldBe expectedTransactionStatus

            val redemption = goalRedemptionRepository.findByGoalId(goal.id).single()
            redemption.status shouldBe expectedRedemptionStatus

            verify(exactly = 1) {
                investmentManagerService.checkInvestmentRedemptionStatus(any<AccountId>(), any())
            }

            verify(exactly = expectedTransactionStatus.toExpectedPublisherCount()) {
                messagePublisher.sendRetryCashinMessage(transactionId)
            }

            verify(exactly = expectedTransactionStatus.toExpectedSynchronizeCount()) {
                internalBankService.synchronizeBankAccount(any(), any())
                balanceService.invalidate(any())
            }

            verify(exactly = if (expectedRedemptionStatus == GoalRedemptionStatus.DONE) 1 else 0) {
                goalMessagePublisher.publishCheckGoalCompletion(any(), true)
            }
        }

        @ParameterizedTest
        @CsvSource(
            "UNKNOWN  , PROCESSING, PROCESSING",
            "CREATED  , PROCESSING, PROCESSING",
            "REQUESTED, PROCESSING, PROCESSING",
        )
        fun `quando retenta e ainda esta processando deve manter na fila`(
            checkInvestmentRedemptionStatus: InvestmentManagerRequestStatus,
            expectedTransactionStatus: TransactionStatus,
            expectedRedemptionStatus: GoalRedemptionStatus,
        ) {
            every {
                investmentManagerService.checkInvestmentRedemptionStatus(any<AccountId>(), any())
            } returns FixedIncomeInvestmentRedemptionResult(
                operationId = GoalRequestOperationId("OPERATION_1"),
                provider = FixedIncomeProvider.ARBI,
                status = checkInvestmentRedemptionStatus,
                redemptionGroupExternalId = InvestmentManagerExternalId("externalId"),
                redemptionExternalIds = listOf(),
                errorMessage = null,
                amounts = null,
            ).right()

            val (goal, transactionId) = startRedemption()

            every {
                transactionRepository.findById(transactionId)
            } returns mockk() {
                every { created } returns getZonedDateTime().minusSeconds(10)
            }

            val result = retryHandler.handleMessage(transactionId.toRetryMessage())

            result.shouldDeleteMessage shouldBe true

            val transaction = transactionService.findTransactionById(transactionId)
            transaction.status shouldBe expectedTransactionStatus

            val redemption = goalRedemptionRepository.findByGoalId(goal.id).single()
            redemption.status shouldBe expectedRedemptionStatus

            verify(exactly = 1) {
                messagePublisher.sendMessage(any())
                investmentManagerService.checkInvestmentRedemptionStatus(any<AccountId>(), any())
            }

            verify(exactly = expectedTransactionStatus.toExpectedSynchronizeCount()) {
                balanceService.invalidate(any())
                internalBankService.synchronizeBankAccount(any(), any())
            }
        }
    }
}