package ai.friday.billpayment.modules.investmentGoals.app.subscription

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.UserEventService
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.subscription.IgnoreSubscriptionFeeResult
import ai.friday.billpayment.app.subscription.Subscription
import ai.friday.billpayment.app.subscription.SubscriptionPaymentStatus
import ai.friday.billpayment.app.subscription.SubscriptionService
import ai.friday.billpayment.app.subscription.SubscriptionStatus
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryId
import ai.friday.billpayment.modules.investmentGoals.app.goal.Goal
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalNotFoundException
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalRepository
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalStatus
import ai.friday.billpayment.modules.investmentGoals.app.goal.MonthlyInstallments
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestment
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentId
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentService
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentStatus
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductId
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductLiquidity
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class InvestmentDiscountServiceTest {
    private val goalInvestmentService: GoalInvestmentService = mockk()
    private val goalRepository: GoalRepository = mockk()
    private val subscriptionService: SubscriptionService = mockk()
    private val walletService: WalletService = mockk()
    private val userEventService: UserEventService = mockk()
    private val accountService: AccountService = mockk()
    private val notificationAdapter = mockk<NotificationAdapter>(relaxUnitFun = true)

    private val investmentDiscountService = InvestmentDiscountService(
        goalInvestmentService = goalInvestmentService,
        goalRepository = goalRepository,
        subscriptionService = subscriptionService,
        walletService = walletService,
        userEventService = userEventService,
        accountService = accountService,
        notificationAdapter = notificationAdapter,
    )

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    private val goalInvestmentDefault = GoalInvestment(
        id = GoalInvestmentId(UUID.randomUUID().toString()),
        goalId = GoalId(UUID.randomUUID().toString()),
        amount = 50_00L,
        status = GoalInvestmentStatus.DONE,
        paidAt = getZonedDateTime(),
        billId = BillId(value = "BillId"),
        dueDate = getLocalDate(),
        extraInstallment = false,
    )

    private val goal = Goal(
        id = goalInvestmentDefault.goalId,
        walletId = wallet.id,
        accountId = AccountId(ACCOUNT_ID),
        name = "Test Goal",
        amount = 1000_00L,
        status = GoalStatus.ACTIVE,
        createdAt = getZonedDateTime(),
        endDate = getLocalDate().plusMonths(1),
        installments = MonthlyInstallments(
            amount = 100_00L,
            dayOfMonth = 10,
        ),
        categoryId = GoalCategoryId(value = ""),
        liquidity = GoalProductLiquidity.MATURITY,
        productId = GoalProductId(value = ""),
        imageUrl = "",
        installmentInitialAmount = 0,
        lastKnownNetBalanceAmount = 0L,
        lastKnownNetBalanceAt = getZonedDateTime(),
    )

    private val subscriptionDefault = Subscription(
        accountId = goal.accountId,
        nextEffectiveDueDate = getLocalDate().withDayOfMonth(10),
        dayOfMonth = 10,
        document = Document(value = "***********"),
        status = SubscriptionStatus.ACTIVE,
        amount = 10_00,
        recurrenceId = RecurrenceId(value = ""),
        paymentStatus = SubscriptionPaymentStatus.PAID,
        walletId = wallet.id,
    )

    @BeforeEach
    fun init() {
        every { goalInvestmentService.findAll(goal.id) } returns listOf(goalInvestmentDefault)
        every { accountService.findAccountById(any()) } returns walletFixture.founderAccount.copy(configuration = walletFixture.founderAccount.configuration.copy(groups = listOf(AccountGroup.INVESTMENT_CAMPAIGN)))
        every {
            subscriptionService.ignoreSubscriptionFeeComingMonths(any(), 1, AccountGroup.INVESTMENT_CAMPAIGN.name)
        } returns listOf(IgnoreSubscriptionFeeResult.Success)
    }

    @Nested
    @DisplayName("checkAndApplyInvestmentDiscount")
    inner class CheckAndApplyInvestmentDiscount {
        @Test
        fun `deve retornar false quando o investimento tem valor menor que o mínimo`() {
            val goalInvestment = goalInvestmentDefault.copy(amount = 49_99)

            val result = investmentDiscountService.checkAndApplyInvestmentDiscount(goalInvestment)

            result.shouldBeFalse()

            verify(exactly = 0) {
                goalRepository.find(any())
                walletService.findWallet(any())
                subscriptionService.findOrNull(any())
                accountService.findAccountById(any())
                notificationAdapter.notifySubscriptionGrantedByInvestment(any(), any())
            }
        }

        @Test
        fun `deve retornar false quando o investimento não está pago`() {
            val goalInvestment = goalInvestmentDefault.copy(paidAt = null)
            val result = investmentDiscountService.checkAndApplyInvestmentDiscount(goalInvestment)

            result.shouldBeFalse()
            verify(exactly = 0) {
                goalRepository.find(any())
                walletService.findWallet(any())
                subscriptionService.findOrNull(any())
                accountService.findAccountById(any())
                notificationAdapter.notifySubscriptionGrantedByInvestment(any(), any())
            }
        }

        @Test
        fun `deve retornar false quando o investimento não está com status DONE`() {
            val goalInvestment = goalInvestmentDefault.copy(status = GoalInvestmentStatus.PROCESSING)
            val result = investmentDiscountService.checkAndApplyInvestmentDiscount(goalInvestment)

            result.shouldBeFalse()
            verify(exactly = 0) {
                goalRepository.find(any())
                walletService.findWallet(any())
                subscriptionService.findOrNull(any())
                accountService.findAccountById(any())
                notificationAdapter.notifySubscriptionGrantedByInvestment(any(), any())
            }
        }

        @Test
        fun `deve retornar false quando o goal não é encontrado`() {
            every { goalRepository.find(any()) } returns GoalNotFoundException(goalInvestmentDefault.goalId).left()
            val result = investmentDiscountService.checkAndApplyInvestmentDiscount(goalInvestmentDefault)

            result.shouldBeFalse()
            verify(exactly = 1) { goalRepository.find(any()) }
            verify(exactly = 0) {
                walletService.findWallet(any())
                subscriptionService.findOrNull(any())
                accountService.findAccountById(any())
                notificationAdapter.notifySubscriptionGrantedByInvestment(any(), any())
            }
        }

        @Test
        fun `deve retornar false quando o usuário não está no grupo INVESTMENT_CAMPAIGN`() {
            every { goalRepository.find(any()) } returns goal.right()
            every { walletService.findWallet(any()) } returns wallet
            every { goalRepository.findByAccountId(any()) } returns listOf(goal)
            every { accountService.findAccountById(any()) } returns walletFixture.founderAccount

            val result = investmentDiscountService.checkAndApplyInvestmentDiscount(goalInvestmentDefault)

            result.shouldBeFalse()
            verify(exactly = 1) {
                goalRepository.find(any())
                walletService.findWallet(any())
                accountService.findAccountById(any())
            }

            verify(exactly = 0) {
                goalInvestmentService.findAll(any())
                goalRepository.findByAccountId(any())
                subscriptionService.findOrNull(any())
                subscriptionService.ignoreSubscriptionFeeComingMonths(any(), 1, AccountGroup.INVESTMENT_CAMPAIGN.name)
                userEventService.save(any())
                notificationAdapter.notifySubscriptionGrantedByInvestment(any(), any())
            }
        }

        @Test
        fun `deve retornar false quando não existe assinatura`() {
            every { goalRepository.find(any()) } returns goal.right()
            every { walletService.findWallet(any()) } returns wallet
            every { subscriptionService.findOrNull(any()) } returns null
            every { userEventService.save(any()) } just Runs

            val result = investmentDiscountService.checkAndApplyInvestmentDiscount(goalInvestmentDefault)

            result.shouldBeFalse()
            verify(exactly = 1) {
                goalRepository.find(any())
                walletService.findWallet(any())
                subscriptionService.findOrNull(any())
                accountService.findAccountById(any())
            }
            verify(exactly = 0) {
                subscriptionService.ignoreSubscriptionFeeComingMonths(any(), 1, AccountGroup.INVESTMENT_CAMPAIGN.name)
                userEventService.save(any())
                notificationAdapter.notifySubscriptionGrantedByInvestment(any(), any())
            }
        }

        @ParameterizedTest
        @CsvSource(
            value = [
                "2025-04-04, 2025-05-10",
                "2025-04-10, 2025-06-10",
                "2025-04-11, 2025-06-10",
            ],
        )
        fun `deve retornar false quando a assinatura não pode ser dada pela data`(currentDateStr: String, nextDueDateStr: String) {
            val currentDate = LocalDate.parse(currentDateStr, DateTimeFormatter.ISO_DATE)
            val nextDueDate = LocalDate.parse(nextDueDateStr, DateTimeFormatter.ISO_DATE)

            val subscription = subscriptionDefault.copy(nextEffectiveDueDate = nextDueDate)

            every { goalRepository.find(any()) } returns goal.right()
            every { walletService.findWallet(any()) } returns wallet
            every { goalRepository.findByAccountId(any()) } returns listOf(goal)
            every { subscriptionService.findOrNull(any()) } returns subscription

            val result = withGivenDateTime(ZonedDateTime.of(currentDate, LocalTime.NOON, brazilTimeZone)) {
                investmentDiscountService.checkAndApplyInvestmentDiscount(goalInvestmentDefault)
            }

            result.shouldBeFalse()

            verify(exactly = 1) {
                goalRepository.find(any())
                walletService.findWallet(any())
                subscriptionService.findOrNull(any())
                accountService.findAccountById(any())
            }
            verify(exactly = 0) {
                subscriptionService.ignoreSubscriptionFeeComingMonths(any(), 1, AccountGroup.INVESTMENT_CAMPAIGN.name)
                userEventService.save(any())
                notificationAdapter.notifySubscriptionGrantedByInvestment(any(), any())
            }
        }

        @ParameterizedTest
        @CsvSource(
            value = [
                "2025-04-04, 2025-04-10",
                "2025-04-10, 2025-05-10",
                "2025-04-11, 2025-05-10",
            ],
        )
        fun `deve enviar UserEvent quando o desconto é aplicado com sucesso`(currentDateStr: String, nextDueDateStr: String) {
            val currentDate = LocalDate.parse(currentDateStr, DateTimeFormatter.ISO_DATE)
            val nextDueDate = LocalDate.parse(nextDueDateStr, DateTimeFormatter.ISO_DATE)

            val subscription = subscriptionDefault.copy(nextEffectiveDueDate = nextDueDate)

            every { goalRepository.find(any()) } returns goal.right()
            every { walletService.findWallet(any()) } returns wallet
            every { goalRepository.findByAccountId(any()) } returns listOf(goal)
            every { subscriptionService.findOrNull(any()) } returns subscription
            every { userEventService.save(any()) } just Runs

            val result = withGivenDateTime(ZonedDateTime.of(currentDate, LocalTime.NOON, brazilTimeZone)) {
                investmentDiscountService.checkAndApplyInvestmentDiscount(goalInvestmentDefault)
            }

            result.shouldBeTrue()

            verify(exactly = 1) {
                goalRepository.find(any())
                walletService.findWallet(any())
                subscriptionService.findOrNull(any())
                subscriptionService.ignoreSubscriptionFeeComingMonths(any(), 1, AccountGroup.INVESTMENT_CAMPAIGN.name)
                accountService.findAccountById(any())
                userEventService.save(any())
                notificationAdapter.notifySubscriptionGrantedByInvestment(any(), any())
            }
        }
    }

    @Nested
    @DisplayName("checkAndApplyFirstInstallmentDiscount")
    inner class CheckAndApplyFirstInstallmentDiscount {

        private val account = walletFixture.founderAccount.copy(configuration = walletFixture.founderAccount.configuration.copy(groups = listOf(AccountGroup.INVESTMENT_CAMPAIGN)))

        @Test
        fun `não deve aplicar desconto na primeira cobrança quando conta não está no grupo INVESTMENT_CAMPAIGN`() {
            val result = investmentDiscountService.checkAndApplyFirstInstallmentDiscount(
                account.copy(configuration = walletFixture.founderAccount.configuration.copy(groups = listOf())),
                subscriptionDefault,
            )

            result.shouldBeFalse()

            verify {
                subscriptionService wasNot Called
                userEventService wasNot Called
                notificationAdapter wasNot Called
            }
        }

        @Test
        fun `não deve aplicar desconto na primeira cobrança quando conta está no grupo INVESTMENT_CAMPAIGN mas não teve investimento no último mês`() {
            every { walletService.findWallet(wallet.id) } returns wallet
            every { goalRepository.findByAccountId(any()) } returns listOf(goal)

            every { goalInvestmentService.findAll(goal.id) } returns listOf(goalInvestmentDefault.copy(paidAt = getZonedDateTime().minusMonths(1).withDayOfMonth(1).minusDays(1)))

            val result = investmentDiscountService.checkAndApplyFirstInstallmentDiscount(account, subscriptionDefault)

            result.shouldBeFalse()

            verify {
                subscriptionService wasNot Called
                userEventService wasNot Called
                notificationAdapter wasNot Called
            }
        }

        @Test
        fun `não deve aplicar desconto na primeira cobrança quando conta está no grupo INVESTMENT_CAMPAIGN e o investimento do último mês é menor que o mínimo`() {
            every { walletService.findWallet(wallet.id) } returns wallet
            every { goalRepository.findByAccountId(any()) } returns listOf(goal)

            every { goalInvestmentService.findAll(goal.id) } returns listOf(goalInvestmentDefault.copy(amount = 49_99))

            val result = investmentDiscountService.checkAndApplyFirstInstallmentDiscount(account, subscriptionDefault)

            result.shouldBeFalse()

            verify {
                subscriptionService wasNot Called
                userEventService wasNot Called
                notificationAdapter wasNot Called
            }
        }

        @Test
        fun `deve aplicar desconto na primeira cobrança quando conta está no grupo INVESTMENT_CAMPAIGN e teve investimento no último mês`() {
            every { walletService.findWallet(wallet.id) } returns wallet
            every { goalRepository.findByAccountId(any()) } returns listOf(goal)
            every { userEventService.save(any()) } just Runs

            val result = investmentDiscountService.checkAndApplyFirstInstallmentDiscount(account, subscriptionDefault)

            result.shouldBeTrue()

            verify(exactly = 1) {
                subscriptionService.ignoreSubscriptionFeeComingMonths(any(), 1, AccountGroup.INVESTMENT_CAMPAIGN.name)
                userEventService.save(any())
                notificationAdapter.notifySubscriptionGrantedByInvestment(any(), any())
            }
        }
    }
}