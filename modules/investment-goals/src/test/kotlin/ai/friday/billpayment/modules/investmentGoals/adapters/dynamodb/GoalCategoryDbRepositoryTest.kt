package ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategory
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryType
import ai.friday.billpayment.modules.investmentGoals.shouldBeGoalCategory
import ai.friday.billpayment.modules.investmentGoals.shouldHaveGoalCategory
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class GoalCategoryDbRepositoryTest {
    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val goalCategoryRepository = GoalCategoryDbRepository(GoalCategoryDynamoDAO(dynamoDbEnhancedClient))

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @ParameterizedTest
    @MethodSource("categories")
    fun `deve conseguir criar e buscar uma categoria`(category: GoalCategory) {
        goalCategoryRepository.save(category)

        goalCategoryRepository.findOrNull(category.id) shouldBeGoalCategory category
    }

    @ParameterizedTest
    @MethodSource("categories")
    fun `deve atualizar uma categoria existente`(category: GoalCategory) {
        goalCategoryRepository.save(category)

        val updatedCategory = category.copy(
            name = "updated ${category.name}",
            imageUrl = "updated ${category.imageUrl}",
            enabled = !category.enabled,
        )

        goalCategoryRepository.save(updatedCategory)

        goalCategoryRepository.findOrNull(updatedCategory.id) shouldBeGoalCategory updatedCategory
    }

    @Test
    fun `deve conseguir conseguir buscar todas as categorias`() {
        goalCategoryRepository.save(enabledCategory)
        goalCategoryRepository.save(disabledCategory)

        val result = goalCategoryRepository.findAll()

        result.size shouldBe 2
        result shouldHaveGoalCategory enabledCategory
        result shouldHaveGoalCategory disabledCategory
    }

    companion object {
        private val enabledCategory = GoalCategory(
            type = GoalCategoryType.HOME,
            name = "enabled category",
            imageUrl = "https://duckduckgo.com/?q=mel",
        )

        private val disabledCategory = GoalCategory(
            type = GoalCategoryType.HOME,
            name = "disabled category",
            imageUrl = "https://duckduckgo.com/?q=mel",
            enabled = false,
        )

        @JvmStatic
        fun categories() = listOf(enabledCategory, disabledCategory)
    }
}