package ai.friday.billpayment.modules.investmentGoals.app.goal

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.balance
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb.GoalDbRepository
import ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb.GoalDynamoDAO
import ai.friday.billpayment.modules.investmentGoals.adapters.instrumentation.GoalInstrumentationRepository
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategory
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryId
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryService
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryType
import ai.friday.billpayment.modules.investmentGoals.app.goal.instrumentation.GoalCompletedByAmountInstrumentationEvent
import ai.friday.billpayment.modules.investmentGoals.app.goal.instrumentation.GoalCompletedByDateInstrumentationEvent
import ai.friday.billpayment.modules.investmentGoals.app.investment.CreateRegularGoalInvestmentCommand
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestment
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentService
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentStatus
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerService
import ai.friday.billpayment.modules.investmentGoals.app.investment.UserGoalPositions
import ai.friday.billpayment.modules.investmentGoals.app.payment.GoalsInvestmentNotificationService
import ai.friday.billpayment.modules.investmentGoals.weeklyGoal
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.ints.exactly
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import kotlin.test.fail
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class GoalStatusServiceTest {

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val goalRepository = GoalDbRepository(client = GoalDynamoDAO(dynamoDbEnhancedClient))

    private val investmentManagerService = mockk<InvestmentManagerService> {
        every {
            enableFixedIncomeAccount(any(), any())
        } returns Unit.right()
    }

    private val goalInvestmentService = mockk<GoalInvestmentService>() {
        every {
            ignoreAllGoalInvestments(any(), any())
        } returns Unit.right()
    }

    private val goalCategoryService = mockk<GoalCategoryService>() {
        val categories = GoalCategoryType.entries.map {
            GoalCategory(
                id = GoalCategoryId(it.name),
                name = it.name,
                type = it,
                imageUrl = "https://fake.url/$it.png",
            )
        }

        every {
            findAll()
        } returns categories

        every {
            findAllEnabled()
        } returns categories
    }

    private val goalsInvestmentNotificationService = mockk<GoalsInvestmentNotificationService>(relaxUnitFun = true)

    private val goalService = spyk(
        GoalService(
            goalRepository = goalRepository,
            goalProductService = mockk(),
            goalStatementService = mockk(),
            goalInvestmentService = goalInvestmentService,
            accountRepository = mockk(),
            accountRegisterRepository = mockk(),
            walletRepository = mockk(),
            updateBillService = mockk(),
            investmentManagerService = mockk(),
            goalMessagePublisher = mockk(),
            userAddressConfiguration = mockk(),
            goalsInvestmentNotificationService = goalsInvestmentNotificationService,
            goalInstrumentationRepository = mockk(relaxUnitFun = true),
            goalCategoryService = goalCategoryService,
            userJourneyService = mockk(relaxUnitFun = true),
        ),
    )

    private val goalInstrumentationRepository = mockk<GoalInstrumentationRepository>(relaxUnitFun = true)

    private val goalStatusService = GoalStatusService(
        goalService = goalService,
        goalRepository = goalRepository,
        goalInvestmentService = goalInvestmentService,
        investmentManagerService = investmentManagerService,
        goalCategoryService = goalCategoryService,
        goalsInvestmentNotificationService = goalsInvestmentNotificationService,
        goalInstrumentationRepository = goalInstrumentationRepository,
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    private fun Goal.setupRepository() {
        goalRepository.save(this)
    }

    private fun Goal.setupBalance(balance: Long) {
        every {
            investmentManagerService.userGoalPositions(accountId = accountId, goalId = id)
        } returns UserGoalPositions(
            accountId = accountId,
            goalId = id,
            positions = emptyList(),
            netValue = 0,
            grossValue = balance,
            investedValue = 0,
            irValue = 0,
            iofValue = 0,
            earningValue = 0,
        ).right()
    }

    private fun Goal.createInvestment(goalInvestmentStatus: GoalInvestmentStatus) = GoalInvestment(
        goalId = id,
        billId = BillId(),
        amount = 1,
        dueDate = getLocalDate(),
        paidAt = null,
        extraInstallment = false,
        status = goalInvestmentStatus,
    )

    private fun Goal.setupInvestments(vararg investments: GoalInvestment) {
        every {
            goalInvestmentService.findAll(id)
        } returns investments.asList()
    }

    @Nested
    @DisplayName("ao remover uma meta")
    inner class RemoveGoal {

        @EnumSource(GoalStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["ACTIVE", "PAUSED"])
        @ParameterizedTest
        fun `deve retornar InvalidGoalStatus quando a meta não está ATIVA`(status: GoalStatus) {
            val goal = weeklyGoal.copy(status = status)
            goal.setupRepository()
            goal.setupBalance(1)

            val result = goalStatusService.remove(goal.id, ActionSource.Api(goal.accountId)).map { fail("should be left") }.getOrElse { it }
            result.shouldBeTypeOf<UpdateGoalError.InvalidGoalStatus>()
        }

        @Test
        fun `deve retornar BalanceDifferentThanZero quando a meta tem saldo`() {
            weeklyGoal.setupRepository()
            weeklyGoal.setupBalance(1)

            val result = goalStatusService.remove(weeklyGoal.id, ActionSource.Api(weeklyGoal.accountId))

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<UpdateGoalError.BalanceDifferentThanZero>()
                it.balance shouldBe 1
            }
        }

        @Test
        fun `deve retornar InvestmentProcessing quando a meta nao tem saldo mas esta processando um investimento`() {
            weeklyGoal.setupRepository()
            weeklyGoal.setupBalance(0)

            val processingGoalInvestment = weeklyGoal.createInvestment(GoalInvestmentStatus.PROCESSING)
            val doneGoalInvestment = weeklyGoal.createInvestment(GoalInvestmentStatus.DONE)
            val createdInvestment = weeklyGoal.createInvestment(GoalInvestmentStatus.CREATED)
            val failedInvestment = weeklyGoal.createInvestment(GoalInvestmentStatus.FAILED)

            weeklyGoal.setupInvestments(processingGoalInvestment, doneGoalInvestment, createdInvestment, failedInvestment)

            every {
                goalInvestmentService.ignoreAllGoalInvestments(any(), any())
            } returns UpdateGoalError.InvestmentProcessing(processingGoalInvestment.id).left()

            val result = goalStatusService.remove(weeklyGoal.id, ActionSource.Api(weeklyGoal.accountId))

            result.isLeft() shouldBe true
            result.map {
                it.shouldBeTypeOf<UpdateGoalError.InvestmentProcessing>()
                it.goalInvestmentId shouldBe processingGoalInvestment.id
            }
        }

        @Test
        fun `deve retornar Removed quando a meta nao tem saldo mas jah teve investimento`() {
            weeklyGoal.setupRepository()
            weeklyGoal.setupBalance(0)

            val doneGoalInvestment = weeklyGoal.createInvestment(GoalInvestmentStatus.DONE)
            val createdInvestment = weeklyGoal.createInvestment(GoalInvestmentStatus.CREATED)
            val failedInvestment = weeklyGoal.createInvestment(GoalInvestmentStatus.FAILED)

            weeklyGoal.setupInvestments(doneGoalInvestment, createdInvestment, failedInvestment)

            val result = goalStatusService.remove(weeklyGoal.id, ActionSource.Api(weeklyGoal.accountId))

            result.isRight() shouldBe true
            result.map {
                it shouldBe GoalStatus.REMOVED
            }

            with(goalRepository.findOrNull(weeklyGoal.id)) {
                this.shouldNotBeNull()
                this.status shouldBe GoalStatus.REMOVED
            }

            verify {
                goalInvestmentService.ignoreAllGoalInvestments(weeklyGoal.id, any())
            }
        }

        @Test
        fun `deve retornar Removed quando a meta nao tem saldo nem investimento`() {
            weeklyGoal.setupRepository()
            weeklyGoal.setupBalance(0)

            val createdInvestment = weeklyGoal.createInvestment(GoalInvestmentStatus.CREATED)
            val failedInvestment = weeklyGoal.createInvestment(GoalInvestmentStatus.FAILED)

            weeklyGoal.setupInvestments(createdInvestment, failedInvestment)

            val result = goalStatusService.remove(weeklyGoal.id, ActionSource.Api(weeklyGoal.accountId))

            result.isRight() shouldBe true
            result.map {
                it shouldBe GoalStatus.REMOVED
            }

            with(goalRepository.findOrNull(weeklyGoal.id)) {
                this.shouldNotBeNull()
                this.status shouldBe GoalStatus.REMOVED
            }

            verify {
                goalInvestmentService.ignoreAllGoalInvestments(weeklyGoal.id, any())
            }
        }
    }

    @Nested
    @DisplayName("ao pausar uma meta")
    inner class PauseGoal {

        @EnumSource(GoalStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["ACTIVE"])
        @ParameterizedTest
        fun `deve retornar InvalidGoalStatus quando a meta não está ATIVA`(status: GoalStatus) {
            val goal = weeklyGoal.copy(status = status)
            goal.setupRepository()
            goal.setupBalance(1)

            val result = goalStatusService.pause(goal.id, ActionSource.Api(goal.accountId)).map { fail("should be left") }.getOrElse { it }
            result.shouldBeTypeOf<UpdateGoalError.InvalidGoalStatus>()
        }

        @Test
        fun `deve retornar CANCELED quando a meta tem saldo`() {
            weeklyGoal.setupRepository()
            weeklyGoal.setupBalance(1)

            val doneGoalInvestment = weeklyGoal.createInvestment(GoalInvestmentStatus.DONE)
            val createdInvestment = weeklyGoal.createInvestment(GoalInvestmentStatus.CREATED)
            val failedInvestment = weeklyGoal.createInvestment(GoalInvestmentStatus.FAILED)
            val canceledInvestment = weeklyGoal.createInvestment(GoalInvestmentStatus.CANCELED)

            weeklyGoal.setupInvestments(doneGoalInvestment, createdInvestment, failedInvestment, canceledInvestment)

            val result = goalStatusService.pause(weeklyGoal.id, ActionSource.Api(weeklyGoal.accountId)).getOrElse {
                fail("deveria ser right")
            }

            result shouldBe GoalStatus.PAUSED

            with(goalRepository.findOrNull(weeklyGoal.id)) {
                this.shouldNotBeNull()
                this.status shouldBe GoalStatus.PAUSED
            }

            verify {
                goalInvestmentService.ignoreAllGoalInvestments(weeklyGoal.id, any())
            }
        }

        @Test
        fun `deve retornar InvestmentProcessing quando a meta nao tem saldo mas esta processando um investimento`() {
            weeklyGoal.setupRepository()
            weeklyGoal.setupBalance(0)

            val processingGoalInvestment = weeklyGoal.createInvestment(GoalInvestmentStatus.PROCESSING)
            val doneGoalInvestment = weeklyGoal.createInvestment(GoalInvestmentStatus.DONE)
            val createdInvestment = weeklyGoal.createInvestment(GoalInvestmentStatus.CREATED)
            val failedInvestment = weeklyGoal.createInvestment(GoalInvestmentStatus.FAILED)

            weeklyGoal.setupInvestments(processingGoalInvestment, doneGoalInvestment, createdInvestment, failedInvestment)

            every {
                goalInvestmentService.ignoreAllGoalInvestments(any(), any())
            } returns UpdateGoalError.InvestmentProcessing(processingGoalInvestment.id).left()

            val result = goalStatusService.pause(weeklyGoal.id, ActionSource.Api(weeklyGoal.accountId)).map {
                fail("shouldBeRight")
            }.getOrElse { it }

            result.shouldBeTypeOf<UpdateGoalError.InvestmentProcessing>()
            result.goalInvestmentId shouldBe processingGoalInvestment.id
        }

        @Test
        fun `deve retornar CANCELED quando a meta nao tem saldo mas jah teve investimento`() {
            weeklyGoal.setupRepository()
            weeklyGoal.setupBalance(0)

            val doneGoalInvestment = weeklyGoal.createInvestment(GoalInvestmentStatus.DONE)
            val createdInvestment = weeklyGoal.createInvestment(GoalInvestmentStatus.CREATED)
            val failedInvestment = weeklyGoal.createInvestment(GoalInvestmentStatus.FAILED)

            weeklyGoal.setupInvestments(doneGoalInvestment, createdInvestment, failedInvestment)

            val result = goalStatusService.pause(weeklyGoal.id, ActionSource.Api(weeklyGoal.accountId))

            result.isRight() shouldBe true
            result.map {
                it shouldBe GoalStatus.PAUSED
            }

            with(goalRepository.findOrNull(weeklyGoal.id)) {
                this.shouldNotBeNull()
                this.status shouldBe GoalStatus.PAUSED
            }

            verify {
                goalInvestmentService.ignoreAllGoalInvestments(weeklyGoal.id, any())
            }
        }
    }

    @Nested
    @DisplayName("ao resumir uma meta")
    inner class ResumeGoal {

        @EnumSource(GoalStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["PAUSED"])
        @ParameterizedTest
        fun `deve retornar InvalidGoalStatus quando a meta não está CANCELED`(status: GoalStatus) {
            val goal = weeklyGoal.copy(status = status)
            goal.setupRepository()
            goal.setupBalance(1)

            val result = goalStatusService.resume(goal.id, ActionSource.Api(goal.accountId)).map { fail("should be left") }.getOrElse { it }
            result.shouldBeTypeOf<UpdateGoalError.InvalidGoalStatus>()
        }

        @Test
        fun `deve retornar InvalidGoalStatus quando a meta está CANCELED mas o prazo passou`() {
            val goal = weeklyGoal.copy(status = GoalStatus.PAUSED, endDate = getLocalDate().minusDays(1))
            goal.setupRepository()
            goal.setupBalance(1)

            val result = goalStatusService.resume(goal.id, ActionSource.Api(goal.accountId)).map { fail("should be left") }.getOrElse { it }
            result.shouldBeTypeOf<UpdateGoalError.InvalidGoalStatus>()
        }

        @Test
        fun `deve retornar ACTIVE quando reativar a meta`() {
            val goal = weeklyGoal.copy(status = GoalStatus.PAUSED)
            goal.setupRepository()
            goal.setupBalance(1)

            val doneGoalInvestment = goal.createInvestment(GoalInvestmentStatus.DONE)
            val failedInvestment = goal.createInvestment(GoalInvestmentStatus.FAILED)
            val canceledInvestment = goal.createInvestment(GoalInvestmentStatus.CANCELED)

            goal.setupInvestments(doneGoalInvestment, failedInvestment, canceledInvestment)

            every {
                goalService.optimizeGoalInstallmentAmount(goal.id, shouldNotify = false)
            } returns true.right()

            every {
                goalInvestmentService.create(any())
            } returns Unit.right()

            val result = goalStatusService.resume(goal.id, ActionSource.Api(goal.accountId)).getOrElse {
                fail("deveria ser right")
            }

            result shouldBe GoalStatus.ACTIVE

            with(goalRepository.findOrNull(weeklyGoal.id)) {
                this.shouldNotBeNull()
                this.status shouldBe GoalStatus.ACTIVE
            }

            verify {
                goalService.optimizeGoalInstallmentAmount(goal.id, shouldNotify = false)
                goalInvestmentService.create(
                    CreateRegularGoalInvestmentCommand(
                        goalId = goal.id,
                    ),
                )
            }
        }
    }

    @Nested
    @DisplayName("ao concluir uma meta")
    inner class CheckAndUpdateGoalToCompleted {

        @Test
        fun `se a meta já estiver completa, deve retornar true`() {
            val goal = weeklyGoal.copy(status = GoalStatus.COMPLETED)
            goal.setupRepository()

            val result = goalStatusService.checkAndUpdateGoalToCompleted(goal.id).getOrElse { fail("deveria ser right") }
            verify(exactly = 0) {
                goalInvestmentService.ignoreAllGoalInvestments(any(), any())
            }

            result shouldBe true
        }

        @Test
        fun `se a meta não puder ser concluida deve retornar true e pausar a meta`() {
            val goal = weeklyGoal.copy(status = GoalStatus.ACTIVE, categoryId = GoalCategoryId(GoalCategoryType.EMERGENCY_FUND.name), endDate = getLocalDate())
            goal.setupRepository()

            every {
                investmentManagerService.userGoalPositions(any(), any())
            } returns UserGoalPositions(
                accountId = goal.accountId,
                goalId = goal.id,
                positions = emptyList(),
                netValue = 1,
                grossValue = 1,
                investedValue = 0,
                irValue = 0,
                iofValue = 0,
                earningValue = 0,
            ).right()

            val result = goalStatusService.checkAndUpdateGoalToCompleted(goal.id).getOrElse { fail("deveria ser right") }

            verify(exactly = 1) {
                goalInvestmentService.ignoreAllGoalInvestments(any(), any())
                goalInstrumentationRepository.publishEvent(any<GoalCompletedByDateInstrumentationEvent>())
            }

            result shouldBe true

            goalRepository.find(goal.id).getOrElse { fail("deveria ser right") }.status shouldBe GoalStatus.PAUSED
        }

        @Test
        fun `se a meta tiver saldo 0 deve remover a meta`() {
            val goal = weeklyGoal.copy(status = GoalStatus.ACTIVE, categoryId = GoalCategoryId(GoalCategoryType.EMERGENCY_FUND.name), endDate = getLocalDate())
            goal.setupRepository()

            every {
                investmentManagerService.userGoalPositions(any(), any())
            } returns UserGoalPositions(
                accountId = goal.accountId,
                goalId = goal.id,
                positions = emptyList(),
                netValue = 0,
                grossValue = 0,
                investedValue = 0,
                irValue = 0,
                iofValue = 0,
                earningValue = 0,
            ).right()

            val result = goalStatusService.checkAndUpdateGoalToCompleted(goal.id).getOrElse { fail("deveria ser right") }

            verify(exactly = 1) {
                goalInvestmentService.ignoreAllGoalInvestments(any(), any())
            }

            verify(exactly = 0) {
                goalInstrumentationRepository.publishEvent(any())
            }

            result shouldBe true

            goalRepository.find(goal.id).getOrElse { fail("deveria ser right") }.status shouldBe GoalStatus.REMOVED
        }

        @Test
        fun `se a meta estiver vencida deve concluir a meta e retornar true`() {
            every {
                goalInvestmentService.ignoreAllGoalInvestments(any(), any())
            } returns Unit.right()

            every {
                investmentManagerService.userGoalPositions(any(), any())
            } returns UserGoalPositions(
                accountId = weeklyGoal.accountId,
                goalId = weeklyGoal.id,
                positions = emptyList(),
                netValue = weeklyGoal.amount,
                grossValue = weeklyGoal.amount - 10,
                investedValue = 0,
                irValue = 0,
                iofValue = 0,
                earningValue = 0,
            ).right()

            val goal = weeklyGoal.copy(status = GoalStatus.ACTIVE, categoryId = GoalCategoryId(GoalCategoryType.HOME.name), endDate = getLocalDate().minusDays(1))
            goal.setupRepository()

            val result = goalStatusService.checkAndUpdateGoalToCompleted(goal.id).getOrElse { fail("deveria ser right") }

            verify {
                goalInvestmentService.ignoreAllGoalInvestments(goal.id, any())
                goalsInvestmentNotificationService.notifyGoalCompleted(any(), any())
                goalInstrumentationRepository.publishEvent(any<GoalCompletedByDateInstrumentationEvent>())
            }

            goalRepository.find(weeklyGoal.id).getOrElse { fail("deveria ser right") }.status shouldBe GoalStatus.COMPLETED

            result shouldBe true
        }

        @Test
        fun `se a meta estiver concluida pelo saldo deve concluir a meta e retornar true`() {
            every {
                goalInvestmentService.ignoreAllGoalInvestments(any(), any())
            } returns Unit.right()

            every {
                investmentManagerService.userGoalPositions(any(), any())
            } returns UserGoalPositions(
                accountId = weeklyGoal.accountId,
                goalId = weeklyGoal.id,
                positions = emptyList(),
                netValue = weeklyGoal.amount,
                grossValue = weeklyGoal.amount + 10,
                investedValue = 0,
                irValue = 0,
                iofValue = 0,
                earningValue = 0,
            ).right()

            val goal = weeklyGoal.copy(status = GoalStatus.ACTIVE, categoryId = GoalCategoryId(GoalCategoryType.HOME.name), endDate = getLocalDate())
            goal.setupRepository()

            val result = goalStatusService.checkAndUpdateGoalToCompleted(goal.id).getOrElse { fail("deveria ser right") }

            verify {
                goalInvestmentService.ignoreAllGoalInvestments(goal.id, any())
                investmentManagerService.userGoalPositions(any(), any())
                goalsInvestmentNotificationService.notifyGoalCompleted(any(), any())
                goalInstrumentationRepository.publishEvent(any<GoalCompletedByAmountInstrumentationEvent>())
            }

            goalRepository.find(weeklyGoal.id).getOrElse { fail("deveria ser right") }.status shouldBe GoalStatus.COMPLETED

            result shouldBe true
        }

        @Test
        fun `se a não estiver concluida não deve concluir a meta e retornar false`() {
            every {
                goalInvestmentService.ignoreAllGoalInvestments(any(), any())
            } returns Unit.right()

            every {
                investmentManagerService.userGoalPositions(any(), any())
            } returns UserGoalPositions(
                accountId = weeklyGoal.accountId,
                goalId = weeklyGoal.id,
                positions = emptyList(),
                netValue = weeklyGoal.amount - 1,
                grossValue = weeklyGoal.amount + 10,
                investedValue = 0,
                irValue = 0,
                iofValue = 0,
                earningValue = 0,
            ).right()

            val goal = weeklyGoal.copy(status = GoalStatus.ACTIVE, categoryId = GoalCategoryId(GoalCategoryType.HOME.name), endDate = getLocalDate().plusDays(1))
            goal.setupRepository()

            val result = goalStatusService.checkAndUpdateGoalToCompleted(goal.id).getOrElse { fail("deveria ser right") }

            verify {
                investmentManagerService.userGoalPositions(any(), any())
            }

            verify(exactly = 0) {
                goalInvestmentService.ignoreAllGoalInvestments(goal.id, any())
                goalInvestmentService.ignoreAllGoalInvestments(any(), any())
                goalInstrumentationRepository.publishEvent(any())
            }

            goalRepository.find(weeklyGoal.id).getOrElse { fail("deveria ser right") }.status shouldBe GoalStatus.ACTIVE

            result shouldBe false
        }
    }

    @Nested
    @DisplayName("ao reativar uma meta")
    inner class CheckAndUpdateGoalToActiveFromPaused {

        @EnumSource(GoalStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["PAUSED"])
        @ParameterizedTest
        fun `se a meta não estiver pausada deve retornar false`(status: GoalStatus) {
            val goal = weeklyGoal.copy(status = status)
            goal.setupRepository()

            val result = goalStatusService.checkAndUpdateGoalToActiveFromPaused(goal.id).getOrElse { fail("deveria ser right") }
            verify(exactly = 0) {
                goalInvestmentService.ignoreAllGoalInvestments(any(), any())
            }

            result shouldBe false
        }

        @Test
        fun `se a data final da meta tiver passado não pode despausar`() {
            val goal = weeklyGoal.copy(status = GoalStatus.PAUSED, endDate = getLocalDate())
            goal.setupRepository()

            val result = goalStatusService.checkAndUpdateGoalToActiveFromPaused(goal.id).getOrElse { fail("deveria ser right") }

            result shouldBe false

            goalRepository.find(goal.id).getOrElse { fail("deveria ser right") }.status shouldBe GoalStatus.PAUSED
        }

        @Test
        fun `se a categoria não permitir resumo automatico deve retornar false`() {
            val goal = weeklyGoal.copy(status = GoalStatus.PAUSED, categoryId = GoalCategoryId(GoalCategoryType.HOME.name), endDate = getLocalDate().plusMonths(1))
            goal.setupRepository()

            val result = goalStatusService.checkAndUpdateGoalToActiveFromPaused(goal.id).getOrElse { fail("deveria ser right") }

            result shouldBe false

            goalRepository.find(goal.id).getOrElse { fail("deveria ser right") }.status shouldBe GoalStatus.PAUSED
        }

        @Test
        fun `se a meta tiver saldo maior que o do objetivo não pode reativar automaticamente`() {
            val goal = weeklyGoal.copy(status = GoalStatus.PAUSED, categoryId = GoalCategoryId(GoalCategoryType.EMERGENCY_FUND.name), endDate = getLocalDate().plusWeeks(1))
            goal.setupRepository()

            every {
                investmentManagerService.userGoalPositions(any(), any())
            } returns UserGoalPositions(
                accountId = goal.accountId,
                goalId = goal.id,
                positions = emptyList(),
                netValue = goal.amount,
                grossValue = 0,
                investedValue = 0,
                irValue = 0,
                iofValue = 0,
                earningValue = 0,
            ).right()

            val result = goalStatusService.checkAndUpdateGoalToActiveFromPaused(goal.id).getOrElse { fail("deveria ser right") }

            result shouldBe false

            verify {
                investmentManagerService.userGoalPositions(any(), any())
            }

            goalRepository.find(goal.id).getOrElse { fail("deveria ser right") }.status shouldBe GoalStatus.PAUSED
        }

        @Test
        fun `se a meta tiver saldo menor que o do objetivo e não estiver vencida e a categoria permitir, a meta deverá ser ativada automaticmaente`() {
            val goal = weeklyGoal.copy(status = GoalStatus.PAUSED, categoryId = GoalCategoryId(GoalCategoryType.EMERGENCY_FUND.name), endDate = getLocalDate().plusWeeks(1))
            goal.setupRepository()

            every {
                investmentManagerService.userGoalPositions(any(), any())
            } returns UserGoalPositions(
                accountId = goal.accountId,
                goalId = goal.id,
                positions = emptyList(),
                netValue = goal.amount - 1,
                grossValue = 0,
                investedValue = 0,
                irValue = 0,
                iofValue = 0,
                earningValue = 0,
            ).right()

            every {
                goalInvestmentService.create(CreateRegularGoalInvestmentCommand(goal.id))
            } returns Unit.right()

            every {
                goalService.optimizeGoalInstallmentAmount(goal.id, shouldNotify = false)
            } returns true.right()

            val result = goalStatusService.checkAndUpdateGoalToActiveFromPaused(goal.id).getOrElse { fail("deveria ser right") }

            result shouldBe true

            verify {
                investmentManagerService.userGoalPositions(any(), any())
                goalService.optimizeGoalInstallmentAmount(goal.id, shouldNotify = false)
                goalInvestmentService.create(CreateRegularGoalInvestmentCommand(goal.id))
            }

            goalRepository.find(goal.id).getOrElse { fail("deveria ser right") }.status shouldBe GoalStatus.ACTIVE
        }
    }
}