package ai.friday.billpayment.modules.investmentGoals.adapters.api.messaging

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.modules.investmentGoals.adapters.messaging.CheckGoalCompletionHandler
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalStatusService
import ai.friday.billpayment.modules.investmentGoals.app.messaging.CheckGoalCompletionHandlerMessage
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message

class CheckGoalCompletionHandlerTest {

    private val goalStatusService = mockk<GoalStatusService>() {
        every {
            checkAndUpdateGoalToCompleted(any())
        } returns false.right()

        every {
            checkAndUpdateGoalToActiveFromPaused(any())
        } returns false.right()
    }

    private val handler = CheckGoalCompletionHandler(
        amazonSQS = mockk(),
        configuration = mockk(),
        goalStatusService = goalStatusService,
        checkGoalCompletionQueueName = "checkGoalCompletionQueueName",
    )

    private val messageFromRedemption = Message.builder().body(getObjectMapper().writeValueAsString(CheckGoalCompletionHandlerMessage("1", true))).build()
    private val messageNotFromRedemption = Message.builder().body(getObjectMapper().writeValueAsString(CheckGoalCompletionHandlerMessage("1", false))).build()

    @Test
    fun `quando o evento for de um resgate deve verificar a reativação da meta`() {
        val result = handler.handleMessage(messageFromRedemption)

        result.shouldDeleteMessage shouldBe true

        verify {
            goalStatusService.checkAndUpdateGoalToActiveFromPaused(GoalId("1"))
        }

        verify(exactly = 0) {
            goalStatusService.checkAndUpdateGoalToCompleted(any())
        }
    }

    @Test
    fun `quando o evento não for de um resgate deve verificar a conclusão da meta`() {
        val result = handler.handleMessage(messageNotFromRedemption)

        result.shouldDeleteMessage shouldBe true

        verify {
            goalStatusService.checkAndUpdateGoalToCompleted(GoalId("1"))
        }

        verify(exactly = 0) {
            goalStatusService.checkAndUpdateGoalToActiveFromPaused(any())
        }
    }
}