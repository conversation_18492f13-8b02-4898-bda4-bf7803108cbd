package ai.friday.billpayment.modules.investmentGoals.app.category

import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class GoalCategoryServiceTest {
    private val goalCategoryRepository = mockk<GoalCategoryRepository>() {
        every {
            save(any())
        } answers { firstArg() }
    }
    private val goalCategoryService = GoalCategoryService(goalCategoryRepository)

    private val goalCategoryId = GoalCategoryId("FAKE GOAL CATEGORY ID")

    @Test
    fun `deve retornar nulo quando não encontrar uma categoria para o id`() {
        every {
            goalCategoryRepository.findOrNull(any())
        } returns null

        val category = goalCategoryService.findOrNull(goalCategoryId)
        category.shouldBeNull()
    }

    @Test
    fun `deve retornar a categoria quando encontrar`() {
        val category = GoalCategory(
            type = GoalCategoryType.PARTY,
            id = goalCategoryId,
            name = "fake name",
            imageUrl = "https://fake.url/image.png",
        )

        every {
            goalCategoryRepository.findOrNull(any())
        } returns category

        goalCategoryService.findOrNull(goalCategoryId) shouldBe category
    }

    @Test
    fun `deve retornar uma lista vazia quando não houver nenhuma categoria`() {
        every {
            goalCategoryRepository.findAll()
        } returns emptyList()

        goalCategoryService.findAll().shouldBeEmpty()
    }

    @Test
    fun `deve retornar todas as categorias cadastradas na base`() {
        val categories = listOf(
            GoalCategory(name = "category 1", imageUrl = "https://fake.url/image.png", type = GoalCategoryType.HOME),
            GoalCategory(name = "category 2", imageUrl = "https://fake.url/image.png", type = GoalCategoryType.HOME),
            GoalCategory(name = "category 3", imageUrl = "https://fake.url/image.png", type = GoalCategoryType.HOME),
            GoalCategory(name = "category 4", imageUrl = "https://fake.url/image.png", enabled = false, type = GoalCategoryType.HOME),
        )

        every {
            goalCategoryRepository.findAll()
        } returns categories

        goalCategoryService.findAll() shouldContainExactlyInAnyOrder categories
    }

    @Test
    fun `deve retornar apenas as categorias habilitadas`() {
        val categories = listOf(
            GoalCategory(name = "category 1", imageUrl = "https://fake.url/image.png", type = GoalCategoryType.HOME),
            GoalCategory(name = "category 2", imageUrl = "https://fake.url/image.png", type = GoalCategoryType.HOME),
            GoalCategory(name = "category 3", imageUrl = "https://fake.url/image.png", type = GoalCategoryType.HOME),
            GoalCategory(name = "category 4", imageUrl = "https://fake.url/image.png", enabled = false, type = GoalCategoryType.HOME),
        )

        every {
            goalCategoryRepository.findAll()
        } returns categories

        goalCategoryService.findAllEnabled() shouldContainExactlyInAnyOrder categories.filter { it.enabled }
    }

    @ParameterizedTest
    @MethodSource("validSaveCommands")
    fun `deve criar uma nova categoria`(command: SaveGoalCategoryCommand) {
        val result = goalCategoryService.create(command)

        result.isRight() shouldBe true
        result.map {
            it.name shouldBe command.name
            it.type shouldBe command.type
            it.imageUrl shouldBe command.imageUrl
            it.enabled shouldBe command.enabled
        }

        verify(exactly = 1) {
            goalCategoryRepository.save(any())
        }
    }

    @ParameterizedTest
    @MethodSource("invalidSaveCommands")
    fun `nao deve criar quando a url for inválida`(command: SaveGoalCategoryCommand) {
        val result = goalCategoryService.create(command)

        result.isLeft() shouldBe true
        result.mapLeft { it shouldBe GoalCategoryError.InvalidImageUrl }
    }

    @ParameterizedTest
    @MethodSource("validSaveCommands")
    fun `deve atualizar a categoria`(command: SaveGoalCategoryCommand) {
        every {
            goalCategoryRepository.findOrNull(goalCategoryId)
        } returns GoalCategory(
            id = goalCategoryId,
            type = GoalCategoryType.OTHER,
            name = "",
            imageUrl = "",
            enabled = !command.enabled,
        )

        val result = goalCategoryService.update(
            categoryId = goalCategoryId,
            command = command,
        )

        result.isRight() shouldBe true
        result.map {
            it.name shouldBe command.name
            it.type shouldBe command.type
            it.imageUrl shouldBe command.imageUrl
            it.enabled shouldBe command.enabled
        }

        verify(exactly = 1) {
            goalCategoryRepository.save(any())
        }
    }

    @Test
    fun `nao deve atualizar quando não encontrar a categoria`() {
        every {
            goalCategoryRepository.findOrNull(goalCategoryId)
        } returns null

        val result = goalCategoryService.update(
            categoryId = goalCategoryId,
            command = saveEnabledCategoryCommand,
        )

        result.isLeft() shouldBe true
        result.mapLeft { it shouldBe GoalCategoryError.CategoryNotFound }
    }

    @ParameterizedTest
    @MethodSource("invalidSaveCommands")
    fun `nao deve atualizar quando a url for invalida`(command: SaveGoalCategoryCommand) {
        val result = goalCategoryService.update(
            categoryId = goalCategoryId,
            command = command,
        )

        result.isLeft() shouldBe true
        result.mapLeft { it shouldBe GoalCategoryError.InvalidImageUrl }
    }

    companion object {
        private val saveEnabledCategoryCommand = SaveGoalCategoryCommand(
            type = GoalCategoryType.HOME,
            name = "enabled category",
            imageUrl = "https://fake.url/image.png",
            enabled = true,
        )

        private val saveDisabledCategoryCommand = SaveGoalCategoryCommand(
            type = GoalCategoryType.EDUCATION,
            name = "disabled category",
            imageUrl = "https://fake.url/image.png",
            enabled = false,
        )

        private val saveInvalidCategoryCommand = SaveGoalCategoryCommand(
            type = GoalCategoryType.FINANCIAL_INDEPENDENCE,
            name = "inalid url category",
            imageUrl = "image.png",
            enabled = true,
        )

        @JvmStatic
        fun validSaveCommands() = listOf(saveEnabledCategoryCommand, saveDisabledCategoryCommand)

        @JvmStatic
        fun invalidSaveCommands() = listOf(saveInvalidCategoryCommand)
    }
}