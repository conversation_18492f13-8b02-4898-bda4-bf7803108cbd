package ai.friday.billpayment.modules.investmentGoals.app.goal

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.EMAIL_ADDRESS
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.accountRegisterCompleted
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.AgreementData
import ai.friday.billpayment.app.account.Gender
import ai.friday.billpayment.app.account.IdentityValidationStatus
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.account.UserAddressConfiguration
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.balance
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb.GoalDbRepository
import ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb.GoalDynamoDAO
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategory
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryId
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryService
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryType
import ai.friday.billpayment.modules.investmentGoals.app.investment.CreateRegularGoalInvestmentCommand
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomePosition
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeProvider
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestment
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentService
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentStatus
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerException
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerService
import ai.friday.billpayment.modules.investmentGoals.app.investment.UserGoalPositions
import ai.friday.billpayment.modules.investmentGoals.app.messaging.GoalMessagePublisher
import ai.friday.billpayment.modules.investmentGoals.app.payment.GoalsInvestmentNotificationService
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductLiquidity
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductService
import ai.friday.billpayment.modules.investmentGoals.app.product.SelectGoalProductError
import ai.friday.billpayment.modules.investmentGoals.app.statement.GoalStatementItem
import ai.friday.billpayment.modules.investmentGoals.app.statement.GoalStatementItemId
import ai.friday.billpayment.modules.investmentGoals.app.statement.GoalStatementItemStatus
import ai.friday.billpayment.modules.investmentGoals.app.statement.GoalStatementItemType
import ai.friday.billpayment.modules.investmentGoals.app.statement.GoalStatementService
import ai.friday.billpayment.modules.investmentGoals.createGoalWithInitialInvestmentCommand
import ai.friday.billpayment.modules.investmentGoals.createMonthlyGoalCommand
import ai.friday.billpayment.modules.investmentGoals.createWeeklyGoalCommand
import ai.friday.billpayment.modules.investmentGoals.enabledMonthlyCDIProduct
import ai.friday.billpayment.modules.investmentGoals.goalAccountId
import ai.friday.billpayment.modules.investmentGoals.goalWallet
import ai.friday.billpayment.modules.investmentGoals.monthlyGoal
import ai.friday.billpayment.modules.investmentGoals.shouldBeGoal
import ai.friday.billpayment.modules.investmentGoals.shouldHaveGoal
import ai.friday.billpayment.modules.investmentGoals.weeklyGoal
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.withGivenDateTime
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.equality.shouldBeEqualToIgnoringFields
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.verify
import io.mockk.verifyOrder
import java.time.LocalDate
import java.time.ZonedDateTime
import kotlin.test.fail
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.junit.jupiter.params.provider.ValueSource

class GoalServiceTest {

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val goalRepository = GoalDbRepository(client = GoalDynamoDAO(dynamoDbEnhancedClient))

    private val goalProductService = mockk<GoalProductService>() {
        every {
            choose(any(), any())
        } returns enabledMonthlyCDIProduct.right()

        every {
            findProduct(any())
        } returns enabledMonthlyCDIProduct.right()
    }

    private val goalStatementService = mockk<GoalStatementService> {
        every {
            getValidGoalStatements(any(), any())
        } answers {
            listOf(
                GoalStatementItem(
                    id = GoalStatementItemId("C1"),
                    type = GoalStatementItemType.CREDIT,
                    amount = 100,
                    status = GoalStatementItemStatus.DONE,
                    timestamp = getZonedDateTime().minusDays(3),
                ),
                GoalStatementItem(
                    id = GoalStatementItemId("D1"),
                    type = GoalStatementItemType.DEBIT,
                    amount = 70,
                    status = GoalStatementItemStatus.DONE,
                    timestamp = getZonedDateTime().minusDays(2),
                ),
            ).right()
        }
    }

    private val goalMessagePublisher = mockk<GoalMessagePublisher> {
        every {
            publishGoalInvestmentCreated(any(), any(), any(), any())
        } just runs
    }

    private val accountRegisterRepository = mockk<AccountRegisterRepository> {
        every {
            findByAccountId(any(), any())
        } returns accountRegisterCompleted
    }

    private val walletRepository = mockk<WalletRepository> {
        every {
            findWallets(goalAccountId)
        } returns listOf(goalWallet)

        every {
            findWallet(goalWallet.id)
        } returns goalWallet

        every {
            findAccountPaymentMethod(goalWallet.id)
        } returns balance
    }

    private val investmentManagerService = mockk<InvestmentManagerService> {
        every {
            enableFixedIncomeAccount(any(), any())
        } returns Unit.right()
    }

    private val accountRepository = mockk<AccountRepository> {
        every {
            findById(any())
        } returns ACCOUNT
    }

    val userAddressConfiguration = UserAddressConfiguration().apply {
        streetType = "streetType"
        streetName = "streetName"
        number = "number"
        complement = "complement"
        neighborhood = "neighborhood"
        city = "city"
        state = "state"
        zipCode = "zipCode"
    }

    private val goalInvestmentService = mockk<GoalInvestmentService>()

    private val updateBillService = mockk<UpdateBillService> {
        every {
            ignoreBill(any(), any(), any(), any())
        } returns mockk<Bill>().right()
    }

    private val goalCategoryService = mockk<GoalCategoryService> {
        val list = listOf(
            GoalCategory(
                id = goalCategoryIdEF,
                type = GoalCategoryType.EMERGENCY_FUND,
                name = "Emergency Fund",
                imageUrl = "https://image.com",
            ),
            GoalCategory(
                id = goalCategoryIdHome,
                type = GoalCategoryType.HOME,
                name = "HOME",
                imageUrl = "https://image.com",
            ),
        )
        every {
            findAll()
        } returns list

        every {
            findAllEnabled()
        } returns list
    }

    private val goalsInvestmentNotificationService = mockk<GoalsInvestmentNotificationService>()

    private val goalService = GoalService(
        goalRepository = goalRepository,
        goalProductService = goalProductService,
        goalStatementService = goalStatementService,
        goalInvestmentService = goalInvestmentService,
        accountRepository = accountRepository,
        accountRegisterRepository = accountRegisterRepository,
        walletRepository = walletRepository,
        updateBillService = updateBillService,
        investmentManagerService = investmentManagerService,
        goalMessagePublisher = goalMessagePublisher,
        userAddressConfiguration = userAddressConfiguration,
        goalsInvestmentNotificationService = goalsInvestmentNotificationService,
        goalInstrumentationRepository = mockk(relaxUnitFun = true),
        goalCategoryService = goalCategoryService,
        userJourneyService = mockk(relaxUnitFun = true),
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Nested
    @DisplayName("ao tentar criar uma meta")
    inner class Create {

        @Test
        fun `deve retornar erro se nao encontrar um produto`() {
            every {
                goalProductService.choose(createWeeklyGoalCommand.liquidity, createWeeklyGoalCommand.endDate)
            } returns SelectGoalProductError.ProductNotFound.left()

            val result = goalService.create(createWeeklyGoalCommand)

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<CreateGoalError.UnableToFindProduct>()
                it.reason shouldBe SelectGoalProductError.ProductNotFound
            }

            goalRepository.findByAccountId(createWeeklyGoalCommand.accountId).shouldBeEmpty()
        }

        /*
                @Test
                @Disabled("conta basica pode investir, mas envia dados fake na ativacao")
                fun `deve retornar erro se for uma conta basica`() {
                    every {
                        accountRepository.findById(any())
                    } returns ACCOUNT.copy(type = UserAccountType.BASIC_ACCOUNT)

                    val result = goalService.create(createWeeklyGoalCommand)

                    result.isLeft() shouldBe true
                    result.mapLeft {
                        it.shouldBeTypeOf<CreateGoalError.MustUpgradeAccount>()
                    }

                    goalRepository.findByAccountId(createWeeklyGoalCommand.accountId).shouldBeEmpty()
                }
        */

        @Test
        fun `deve retornar erro se nao habilitar a conta de investimento`() {
            every {
                investmentManagerService.enableFixedIncomeAccount(any(), any())
            } returns InvestmentManagerException(NoStackTraceException("mocked")).left()

            val result = goalService.create(createWeeklyGoalCommand)

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<CreateGoalError.CouldNotEnableFixedIncomeAccount>()
                it.message shouldContain "mocked"
            }

            goalRepository.findByAccountId(createWeeklyGoalCommand.accountId).shouldBeEmpty()
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.goal.GoalServiceTest#invalidRequests")
        fun `deve falhar se as configurações de parcelas estiverem erradas`(invalidRequest: CreateGoalCommand) {
            val result = goalService.create(invalidRequest)

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<CreateGoalError.InvalidInstallmentConfiguration>()
                it.installmentExpirationDay shouldBe invalidRequest.installmentExpirationDay
                it.installmentFrequency shouldBe invalidRequest.installmentFrequency
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.goal.GoalServiceTest#createGoalCommands")
        fun `deve falhar se for do tipo RESERVA DE EMERGENCIA e usuário já tiver uma`(command: CreateGoalCommand) {
            goalRepository.save(
                Goal(
                    id = GoalId("1"),
                    accountId = command.accountId,
                    walletId = goalWallet.id,
                    categoryId = goalCategoryIdEF,
                    name = "Emergency Fund",
                    status = GoalStatus.ACTIVE,
                    amount = 1000,
                    endDate = LocalDate.now().plusMonths(6),
                    liquidity = GoalProductLiquidity.MATURITY,
                    installments = MonthlyInstallments(6, 1),
                    productId = enabledMonthlyCDIProduct.id,
                    imageUrl = "https://image.com",
                    installmentInitialAmount = 100,
                    lastKnownNetBalanceAmount = 0,
                    lastKnownNetBalanceAt = getZonedDateTime(),
                ),
            )
            val result = goalService.create(command.copy(categoryId = goalCategoryIdEF))

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<CreateGoalError.InvalidGoalCategory>()
                it.goalCategory.shouldNotBeNull()
                it.reason shouldBe "CATEGORY_ALREADY_EXISTS"
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.goal.GoalServiceTest#createGoalCommands")
        fun `deve retornar suceso e comandar a criacao dos investimentos se for uma requisicao valida`(command: CreateGoalCommand) {
            val result = goalService.create(command)

            result.isRight() shouldBe true

            result.map { goal ->
                goal.status shouldBe GoalStatus.ACTIVE
                goal shouldMatchCreateCommandGoal command
                goalRepository.findOrNull(goal.id) shouldBeGoal goal

                verify {
                    investmentManagerService.enableFixedIncomeAccount(accountRegisterCompleted, balance.method as InternalBankAccount)
                }

                verify(exactly = 1) {
                    goalMessagePublisher.publishGoalInvestmentCreated(goal.id, goal.accountId, false)
                }
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.goal.GoalServiceTest#createGoalWithInitialInvestmentCommands")
        fun `deve retornar suceso e comandar a criacao da primeira parcela + investimento inicial se for uma requisicao valida`(command: CreateGoalCommand) {
            every {
                accountRegisterRepository.findByAccountId(any(), any())
            } returns accountRegisterCompleted.copy(accountId = command.accountId)

            val result = goalService.create(command)

            result.isRight() shouldBe true

            result.map { goal ->
                goal.status shouldBe GoalStatus.ACTIVE
                goal shouldMatchCreateCommandGoal command
                goalRepository.findOrNull(goal.id) shouldBeGoal goal

                verify {
                    investmentManagerService.enableFixedIncomeAccount(accountRegisterCompleted.copy(accountId = goal.accountId), balance.method as InternalBankAccount)
                }

                verify(exactly = 1) {
                    goalMessagePublisher.publishGoalInvestmentCreated(goal.id, goal.accountId, false)
                    goalMessagePublisher.publishGoalInvestmentCreated(goal.id, goal.accountId, true, extraInstallmentAmount = command.initialInvestmentAmount)
                }
            }
        }

        private val basicAccountRegisterData = AccountRegisterData(
            accountId = AccountId(ACCOUNT_ID),
            agreementData = AgreementData(
                acceptedAt = null,
                userContractFile = StoredObject("region", "bucket", "userContract.pdf"),
                userContractSignature = "signature",
                declarationOfResidencyFile = StoredObject("region", "bucket", "declarationOfResidencyFile.pdf"),
            ),
            birthDate = getLocalDate(),
            clientIP = "0.0.0.0",
            created = getZonedDateTime(),
            document = Document("***********"),
            isDocumentEdited = false,
            emailAddress = EMAIL_ADDRESS,
            emailVerified = true,
            fraudListMatch = false,
            identityValidationPercentage = 0.95,
            identityValidationStatus = IdentityValidationStatus.CHECKED,
            kycFile = StoredObject("region", "bucket", "kycReport.pdf"),
            lastUpdated = getZonedDateTime().minusDays(1),
            mobilePhone = MobilePhone("+*************"),
            mobilePhoneVerified = true,
            nickname = "nick",
            openForUserReview = false,
            registrationType = RegistrationType.BASIC,
            uploadedSelfie = StoredObject("region", "bucket", "kycReport.pdf"),
            upgradeStarted = null,
            documentInfo = null,
            openedForUserReviewAt = null,
            calculatedGender = Gender.F,
            externalId = null,
            riskAnalysisFailedReasons = null,
            riskAnalysisFailedReasonsHistory = listOf(),
        )

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.goal.GoalServiceTest#createGoalCommands")
        fun `deve enviar dados fake se for conta basica`(command: CreateGoalCommand) {
            every {
                accountRepository.findById(any())
            } returns ACCOUNT.copy(type = UserAccountType.BASIC_ACCOUNT)

            every {
                accountRegisterRepository.findByAccountId(any(), any())
            } returns basicAccountRegisterData

            val result = goalService.create(command)

            result.isRight() shouldBe true

            result.map { goal ->
                goal.status shouldBe GoalStatus.ACTIVE
                goal shouldMatchCreateCommandGoal command

                val slot = slot<AccountRegisterData>()
                verify {
                    investmentManagerService.enableFixedIncomeAccount(capture(slot), any())
                }
                with(slot.captured) {
                    address?.streetType shouldBe userAddressConfiguration.streetType
                    address?.streetName shouldBe userAddressConfiguration.streetName
                    address?.number shouldBe userAddressConfiguration.number
                    address?.complement shouldBe userAddressConfiguration.complement
                    address?.neighborhood shouldBe userAddressConfiguration.neighborhood
                    address?.city shouldBe userAddressConfiguration.city
                    address?.state shouldBe userAddressConfiguration.state
                    address?.zipCode shouldBe userAddressConfiguration.zipCode

                    documentInfo?.docType shouldBe DocumentType.CPF
                }
            }
        }
    }

    @Nested
    @DisplayName("ao tentar consultar meta")
    inner class Find {

        private val statementItems = listOf(
            GoalStatementItem(
                id = GoalStatementItemId("C1"),
                type = GoalStatementItemType.CREDIT,
                amount = 100,
                status = GoalStatementItemStatus.DONE,
                timestamp = getZonedDateTime().minusDays(3),
            ),
            GoalStatementItem(
                id = GoalStatementItemId("D1"),
                type = GoalStatementItemType.DEBIT,
                amount = 70,
                status = GoalStatementItemStatus.DONE,
                timestamp = getZonedDateTime().minusDays(2),
            ),
            GoalStatementItem(
                id = GoalStatementItemId("C2"),
                type = GoalStatementItemType.CREDIT,
                amount = 50,
                status = GoalStatementItemStatus.DONE,
                timestamp = getZonedDateTime().minusDays(1),
            ),
            GoalStatementItem(
                id = GoalStatementItemId("D2"),
                type = GoalStatementItemType.DEBIT,
                amount = 200,
                status = GoalStatementItemStatus.PROCESSING,
                timestamp = getZonedDateTime(),
            ),
        )

        private val incomePosition1 = FixedIncomePosition(
            provider = FixedIncomeProvider.ARBI,
            product = null,
            positionId = "P1",
            operationDate = LocalDate.now(),
            liquidityDueDate = LocalDate.now(),
            maturityDate = LocalDate.now(),
            unitPrice = 2,
            quantity = 13,
            netValue = 21,
            grossValue = 26,
            name = "CDB Cartão Garantido",
            investedValue = 16,
            irValue = 3,
            iofValue = 4,
            earningValue = 6,
        )

        private val incomePosition2 = FixedIncomePosition(
            provider = FixedIncomeProvider.ARBI,
            product = null,
            positionId = "non",
            operationDate = LocalDate.now(),
            liquidityDueDate = LocalDate.now(),
            maturityDate = LocalDate.now(),
            unitPrice = 2,
            quantity = 12,
            netValue = 19,
            grossValue = 24,
            name = "CDB Cartão Garantido",
            investedValue = 14,
            irValue = 1,
            iofValue = 2,
            earningValue = 4,
        )

        @BeforeEach
        fun setup() {
            every {
                goalStatementService.calcSavingsToCDIRate(any(), any())
            } returns 85.right()

            every {
                goalStatementService.getValidGoalStatements(any(), any())
            } returns statementItems.right()

            every {
                investmentManagerService.userGoalPositions(any(), any())
            } answers {
                UserGoalPositions(
                    accountId = firstArg(),
                    goalId = secondArg(),
                    positions = listOf(incomePosition1, incomePosition2),
                    netValue = 2003,
                    grossValue = 5488,
                    investedValue = 7933,
                    irValue = 4279,
                    iofValue = 3500,
                    earningValue = 1426,
                ).right()
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.goal.GoalServiceTest#goals")
        fun `deve retornar uma meta especifica`(goal: Goal) {
            goalRepository.save(goal)

            val response = goalService.findGoal(goal.id)

            response.isRight() shouldBe true

            response.map {
                it shouldBeGoal goal
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.goal.GoalServiceTest#goals")
        fun `deve retornar uma meta especifica com saldo`(goal: Goal) {
            val goalStartDateSlot = slot<LocalDate>()
            val goalIndexRelativeRateSlot = slot<FixedIncomeIndexRate>()

            goalRepository.save(goal)

            val response = goalService.getDetailedGoal(goal.accountId, goal.id)

            response.isRight() shouldBe true

            response.map {
                it.goal shouldBeGoal goal
                it.balance?.netValue shouldBe 2003
                it.balance?.grossValue shouldBe 5488
                it.balance?.investedValue shouldBe 7933
                it.balance?.irValue shouldBe 4279
                it.balance?.iofValue shouldBe 3500
                it.balance?.earningValue shouldBe 1426
                it.balance?.savingsReferenceValue shouldBe 1702L
                it.statementItems shouldContainExactly statementItems
                it.positions shouldContainExactly listOf(incomePosition1, incomePosition2)
            }

            verify {
                goalStatementService.calcSavingsToCDIRate(capture(goalStartDateSlot), capture(goalIndexRelativeRateSlot))
            }

            goalStartDateSlot.captured shouldBe goal.createdAt.toLocalDate()
            goalIndexRelativeRateSlot.captured shouldBe enabledMonthlyCDIProduct.indexIncome
        }

        @Test
        fun `deve retornar todas as metas do usuario`() {
            weeklyGoal.accountId shouldBe monthlyGoal.accountId

            goalRepository.save(weeklyGoal)
            goalRepository.save(monthlyGoal)

            val response = goalService.findUserGoals(weeklyGoal.accountId)

            response.isRight() shouldBe true

            response.map {
                it.size shouldBe 2
                it shouldHaveGoal weeklyGoal
                it shouldHaveGoal monthlyGoal
            }
        }

        @Test
        fun `não deve consultar saldo de usuários que não tem meta`() {
            val response = goalService.getDetailedGoals(weeklyGoal.accountId)

            response.isRight() shouldBe true

            response.map {
                it.totalBalance?.netValue shouldBe 0
                it.totalBalance?.grossValue shouldBe 0
                it.totalBalance?.investedValue shouldBe 0
                it.totalBalance?.irValue shouldBe 0
                it.totalBalance?.iofValue shouldBe 0
                it.totalBalance?.earningValue shouldBe 0
                it.totalBalance?.savingsReferenceValue shouldBe 0

                it.items.size shouldBe 0
            }
            verify(exactly = 0) {
                investmentManagerService.userPositions(any())
            }
        }

        @Test
        fun `deve retornar todas as metas do usuario com saldo`() {
            weeklyGoal.accountId shouldBe monthlyGoal.accountId

            goalRepository.save(weeklyGoal)
            goalRepository.save(monthlyGoal)

            every {
                investmentManagerService.userPositions(any())
            } answers {
                listOf(
                    UserGoalPositions(
                        accountId = firstArg(),
                        goalId = weeklyGoal.id,
                        positions = listOf(incomePosition1, incomePosition2),
                        netValue = 2003,
                        grossValue = 5488,
                        investedValue = 7933,
                        irValue = 4279,
                        iofValue = 3500,
                        earningValue = 1426,
                    ),
                    UserGoalPositions(
                        accountId = firstArg(),
                        goalId = monthlyGoal.id,
                        positions = listOf(incomePosition1, incomePosition2),
                        netValue = 2003,
                        grossValue = 5488,
                        investedValue = 7933,
                        irValue = 4279,
                        iofValue = 3500,
                        earningValue = 1426,
                    ),
                ).right()
            }

            val response = goalService.getDetailedGoals(weeklyGoal.accountId)

            response.isRight() shouldBe true

            response.map {
                it.totalBalance?.netValue shouldBe 2003 * 2
                it.totalBalance?.grossValue shouldBe 5488 * 2
                it.totalBalance?.investedValue shouldBe 7933 * 2
                it.totalBalance?.irValue shouldBe 4279 * 2
                it.totalBalance?.iofValue shouldBe 3500 * 2
                it.totalBalance?.earningValue shouldBe 1426 * 2
                it.totalBalance?.savingsReferenceValue shouldBe 1702L * 2

                it.items.size shouldBe 2
                it.items.forEach { goalDetails ->
                    goalDetails.balance?.netValue shouldBe 2003
                    goalDetails.balance?.grossValue shouldBe 5488
                    goalDetails.balance?.investedValue shouldBe 7933
                    goalDetails.balance?.irValue shouldBe 4279
                    goalDetails.balance?.iofValue shouldBe 3500
                    goalDetails.balance?.earningValue shouldBe 1426
                    goalDetails.balance?.savingsReferenceValue shouldBe 1702L
                    goalDetails.statementItems shouldContainExactly statementItems
                }
                it.items shouldHaveGoal weeklyGoal
                it.items shouldHaveGoal monthlyGoal
            }
        }

        @Test
        fun `deve retornar todas as metas do usuario mesmo se a consulta de saldo falhar`() {
            every {
                investmentManagerService.userPositions(any())
            } returns InvestmentManagerException(NoStackTraceException()).left()

            weeklyGoal.accountId shouldBe monthlyGoal.accountId

            goalRepository.save(weeklyGoal)
            goalRepository.save(monthlyGoal)

            val response = goalService.getDetailedGoals(weeklyGoal.accountId)

            response.isRight() shouldBe true

            response.map {
                it.totalBalance.shouldBeNull()

                it.items.size shouldBe 2
                it.items.forEach {
                    it.balance.shouldBeNull()
                    it.statementItems.shouldNotBeEmpty()
                    it.positions.shouldBeNull()
                }
                it.items shouldHaveGoal weeklyGoal
                it.items shouldHaveGoal monthlyGoal
            }
        }

        private infix fun List<DetailedGoal>.shouldHaveGoal(goal: Goal) {
            this.single { it.goal.id == goal.id }.goal shouldBeGoal goal
        }
    }

    @Nested
    @DisplayName("ao consultar as penalidades de uma meta")
    inner class GoalPenalties {

        @Test
        fun `quando a meta for diaria nao deve ter penalty`() {
            val goal = mockkGoal(0)

            every {
                goal.liquidity
            } returns GoalProductLiquidity.DAILY

            val result = goalService.calculateGoalPenaltyRates(goal).getOrElse { fail("deveria ser right") }
            result.shouldBeEmpty()
        }

        @ParameterizedTest
        @ValueSource(longs = [0, 75, 90, 100, 110])
        fun `quando a meta for no vencimento deve aplicar os 3 penalties existentes nas datas corretas independentemente do progresso atual`(achievement: Long) {
            val goal = mockkGoal(achievement)

            every {
                goalStatementService.calcSavingsToCDIRate(
                    startDate = any(),
                    indexRelativeRate = any(),
                )
            } returns 66.right()

            val result = goalService.calculateGoalPenaltyRates(goal).getOrElse { fail("deveria ser right") }
            result.size shouldBe 3

            result[0].penaltyRate shouldBe FixedIncomeIndexRate(66)
            result[0].limitDate shouldBe getLocalDate().plusDays(75 - achievement - 1)

            result[1].penaltyRate shouldBe FixedIncomeIndexRate(100)
            result[1].limitDate shouldBe getLocalDate().plusDays(90 - achievement - 1)

            result[2].penaltyRate shouldBe FixedIncomeIndexRate(103)
            result[2].limitDate shouldBe getLocalDate().plusDays(100 - achievement - 1)
        }
    }

    @Nested
    @DisplayName("ao consultar a penalidade no resgate")
    inner class PenaltyRate {
        @Test
        fun `quando nao atingiu 75pc do prazo da meta deve resgatar com rendimento da poupanca`() {
            val startDateSlot = slot<LocalDate>()
            val indexRelativeRateSlot = slot<FixedIncomeIndexRate>()

            every {
                goalStatementService.calcSavingsToCDIRate(
                    startDate = capture(startDateSlot),
                    indexRelativeRate = capture(indexRelativeRateSlot),
                )
            } returns 66.right()

            val goal = mockkGoal(74)

            val result = goalService.calculateCurrentPenaltyRate(goal)

            result.isRight() shouldBe true
            result.map {
                it.shouldNotBeNull()
                it.rate shouldBe 66
            }

            startDateSlot.captured shouldBe goal.createdAt.toLocalDate()
            indexRelativeRateSlot.captured shouldBe FixedIncomeIndexRate(100)
        }

        @ParameterizedTest
        @ValueSource(longs = [75, 89])
        fun `quando atingiu entre 75pc e 90pc do prazo da meta deve resgatar com 100pc do CDI`(achievement: Long) {
            val goal = mockkGoal(achievement)

            every {
                goalStatementService.calcSavingsToCDIRate(
                    startDate = any(),
                    indexRelativeRate = any(),
                )
            } returns 66.right()

            val result = goalService.calculateCurrentPenaltyRate(goal)

            result.isRight() shouldBe true
            result.map {
                it.shouldNotBeNull()
                it.rate shouldBe 100
            }
        }

        @ParameterizedTest
        @ValueSource(longs = [90, 99])
        fun `quando atingiu entre 90pc e 100pc do prazo da meta deve resgatar com 103pc do CDI`(achievement: Long) {
            val goal = mockkGoal(achievement)

            every {
                goalStatementService.calcSavingsToCDIRate(
                    startDate = any(),
                    indexRelativeRate = any(),
                )
            } returns 66.right()

            val result = goalService.calculateCurrentPenaltyRate(goal)

            result.isRight() shouldBe true
            result.map {
                it.shouldNotBeNull()
                it.rate shouldBe 103
            }
        }

        @Test
        fun `quando atingiu 100pc do prazo da meta deve resgatar sem penalidade`() {
            val goal = mockkGoal(100)

            every {
                goalStatementService.calcSavingsToCDIRate(
                    startDate = any(),
                    indexRelativeRate = any(),
                )
            } returns 66.right()

            val result = goalService.calculateCurrentPenaltyRate(goal)

            result.isRight() shouldBe true
            result.map {
                it.shouldBeNull()
            }
        }
    }

    @Nested
    @DisplayName("ao atualizar uma meta")
    inner class UpdateGoal {

        @BeforeEach
        fun init() {
            every {
                goalMessagePublisher.publishOptimizeGoalInstallmentAmount(any())
            } just runs
        }

        @Test
        fun `deve atualizar o nome quando a meta está concluida`() {
            val createdGoal = goalRepository.save(
                weeklyGoal.copy(
                    endDate = getLocalDate().minusDays(1),
                    createdAt = getZonedDateTime().minusDays(7),
                    updatedAt = getZonedDateTime().minusDays(7),
                    status = GoalStatus.COMPLETED,
                ),
            )

            val command = UpdateGoalCommand(
                goalId = weeklyGoal.id,
                name = "updated ${weeklyGoal.name}",
                amount = null,
                endDate = null,
                installmentFrequency = null,
                installmentExpirationDay = null,
                actionSource = ActionSource.Api(weeklyGoal.accountId),
            )

            val now = getZonedDateTime()
            val result = goalService.update(command).getOrElse { fail("deveria ser right") }

            result.name shouldBe command.name

            val updatedGoal = goalRepository.find(weeklyGoal.id).getOrElse { fail("deveria ser right") }

            updatedGoal.shouldBeEqualToIgnoringFields(
                createdGoal,
                createdGoal::name,
                createdGoal::updatedAt,
            )
            updatedGoal.name shouldBe command.name
            (updatedGoal.updatedAt > now.minusSeconds(1)) shouldBe true
        }

        @Test
        fun `deve atualizar o valor quando a meta está ativa`() {
            val createdGoal = goalRepository.save(
                weeklyGoal.copy(
                    endDate = getLocalDate().plusDays(1),
                    createdAt = getZonedDateTime().minusDays(7),
                    updatedAt = getZonedDateTime().minusDays(7),
                    status = GoalStatus.ACTIVE,
                    liquidity = GoalProductLiquidity.MATURITY,
                ),
            )

            val command = UpdateGoalCommand(
                goalId = weeklyGoal.id,
                name = null,
                amount = createdGoal.amount * 2,
                endDate = null,
                installmentFrequency = null,
                installmentExpirationDay = null,
                actionSource = ActionSource.Api(weeklyGoal.accountId),
            )

            val now = getZonedDateTime()
            val result = goalService.update(command).getOrElse { fail("deveria ser right") }

            result.amount shouldBe command.amount

            val updatedGoal = goalRepository.find(weeklyGoal.id).getOrElse { fail("deveria ser right") }

            updatedGoal.shouldBeEqualToIgnoringFields(
                createdGoal,
                createdGoal::amount,
                createdGoal::updatedAt,
            )
            updatedGoal.amount shouldBe command.amount
            (updatedGoal.updatedAt > now.minusSeconds(1)) shouldBe true

            verify {
                goalMessagePublisher.publishOptimizeGoalInstallmentAmount(createdGoal.id)
            }
        }

        @Test
        fun `não deve atualizar o valor quando a meta está concluida`() {
            val createdGoal = goalRepository.save(
                weeklyGoal.copy(
                    endDate = getLocalDate().minusDays(1),
                    createdAt = getZonedDateTime().minusDays(7),
                    updatedAt = getZonedDateTime().minusDays(7),
                    status = GoalStatus.COMPLETED,
                    liquidity = GoalProductLiquidity.MATURITY,
                ),
            )

            val command = UpdateGoalCommand(
                goalId = weeklyGoal.id,
                name = null,
                amount = createdGoal.amount * 2,
                endDate = null,
                installmentFrequency = null,
                installmentExpirationDay = null,
                actionSource = ActionSource.Api(weeklyGoal.accountId),
            )

            val result = goalService.update(command).map { fail("deveria ser left") }.getOrElse { it }

            result.shouldBeTypeOf<UpdateGoalError.InvalidGoalStatus>()

            val updatedGoal = goalRepository.find(weeklyGoal.id).getOrElse { fail("deveria ser right") }

            updatedGoal shouldBe createdGoal

            verify(exactly = 0) {
                goalMessagePublisher.publishOptimizeGoalInstallmentAmount(createdGoal.id)
            }
        }

        @Test
        fun `deve atualizar o vencimento quando a meta está ativa e a liquidez é diaria`() {
            val createdGoal = goalRepository.save(
                weeklyGoal.copy(
                    endDate = getLocalDate().plusDays(1),
                    createdAt = getZonedDateTime().minusWeeks(1),
                    updatedAt = getZonedDateTime().minusWeeks(1),
                    status = GoalStatus.ACTIVE,
                    liquidity = GoalProductLiquidity.DAILY,
                ),
            )

            val command = UpdateGoalCommand(
                goalId = weeklyGoal.id,
                name = null,
                amount = null,
                endDate = getLocalDate().plusWeeks(5),
                installmentFrequency = null,
                installmentExpirationDay = null,
                actionSource = ActionSource.Api(weeklyGoal.accountId),
            )

            val now = getZonedDateTime()
            val result = goalService.update(command).getOrElse { fail("deveria ser right") }

            result.endDate shouldBe command.endDate

            val updatedGoal = goalRepository.find(weeklyGoal.id).getOrElse { fail("deveria ser right") }

            updatedGoal.shouldBeEqualToIgnoringFields(
                createdGoal,
                createdGoal::endDate,
                createdGoal::totalInstallments,
                createdGoal::updatedAt,
            )
            updatedGoal.endDate shouldBe command.endDate
            updatedGoal.totalInstallments shouldBe 6
            (updatedGoal.updatedAt > now.minusSeconds(1)) shouldBe true

            verify {
                goalMessagePublisher.publishOptimizeGoalInstallmentAmount(createdGoal.id)
            }

            verify(exactly = 0) {
                goalInvestmentService.findAll(any())
                goalInvestmentService.ignoreInvestments(any(), any(), any())
            }
        }

        @Test
        fun `deve atualizar o vencimento quando a meta está ativa e a liquidez é diaria e verificar e cancelar parcelas após novo vencimento`() {
            val createdGoal = goalRepository.save(
                weeklyGoal.copy(
                    endDate = getLocalDate().plusWeeks(10),
                    createdAt = getZonedDateTime().minusWeeks(1),
                    updatedAt = getZonedDateTime().minusWeeks(1),
                    status = GoalStatus.ACTIVE,
                    liquidity = GoalProductLiquidity.DAILY,
                ),
            )

            val command = UpdateGoalCommand(
                goalId = weeklyGoal.id,
                name = null,
                amount = null,
                endDate = getLocalDate().plusWeeks(9),
                installmentFrequency = null,
                installmentExpirationDay = null,
                actionSource = ActionSource.Api(weeklyGoal.accountId),
            )

            every {
                goalInvestmentService.findAll(goalId = createdGoal.id)
            } returns emptyList()

            every {
                goalInvestmentService.ignoreInvestments(createdGoal, any(), command.actionSource)
            } returns Unit.right()

            val now = getZonedDateTime()
            val result = goalService.update(command).getOrElse { fail("deveria ser right") }

            result.endDate shouldBe command.endDate

            val updatedGoal = goalRepository.find(weeklyGoal.id).getOrElse { fail("deveria ser right") }

            updatedGoal.shouldBeEqualToIgnoringFields(
                createdGoal,
                createdGoal::endDate,
                createdGoal::totalInstallments,
                createdGoal::updatedAt,
            )
            updatedGoal.endDate shouldBe command.endDate
            updatedGoal.totalInstallments shouldBe 10
            (updatedGoal.updatedAt > now.minusSeconds(1)) shouldBe true

            verify {
                goalMessagePublisher.publishOptimizeGoalInstallmentAmount(createdGoal.id)
                goalInvestmentService.findAll(any())
                goalInvestmentService.ignoreInvestments(any(), any(), any())
            }
        }

        @Test
        fun `não deve atualizar o vencimento quando a meta está ativa e a liquidez é diaria e o final for menor do que um mes do inicio`() {
            val createdGoal = goalRepository.save(
                weeklyGoal.copy(
                    endDate = getLocalDate().plusWeeks(10),
                    createdAt = getZonedDateTime().minusWeeks(1),
                    updatedAt = getZonedDateTime().minusWeeks(1),
                    status = GoalStatus.ACTIVE,
                    liquidity = GoalProductLiquidity.DAILY,
                ),
            )

            val command = UpdateGoalCommand(
                goalId = weeklyGoal.id,
                name = null,
                amount = null,
                endDate = getZonedDateTime().minusWeeks(1).toLocalDate().plusMonths(1).minusDays(1),
                installmentFrequency = null,
                installmentExpirationDay = null,
                actionSource = ActionSource.Api(weeklyGoal.accountId),
            )

            val result = goalService.update(command).map { fail("deveria ser left") }.getOrElse { it }

            result.shouldBeTypeOf<UpdateGoalError.InvalidGoalStatus>()

            val updatedGoal = goalRepository.find(weeklyGoal.id).getOrElse { fail("deveria ser right") }

            updatedGoal shouldBe createdGoal

            verify(exactly = 0) {
                goalMessagePublisher.publishOptimizeGoalInstallmentAmount(any())
                goalInvestmentService.findAll(any())
                goalInvestmentService.ignoreInvestments(any(), any(), any())
            }
        }

        @Test
        fun `não deve atualizar o vencimento quando a meta está concluida`() {
            val createdGoal = goalRepository.save(
                weeklyGoal.copy(
                    endDate = getLocalDate().minusDays(1),
                    createdAt = getZonedDateTime().minusDays(7),
                    updatedAt = getZonedDateTime().minusDays(7),
                    status = GoalStatus.COMPLETED,
                    liquidity = GoalProductLiquidity.DAILY,
                ),
            )

            val command = UpdateGoalCommand(
                goalId = weeklyGoal.id,
                name = null,
                amount = null,
                endDate = getLocalDate().plusWeeks(5),
                installmentFrequency = null,
                installmentExpirationDay = null,
                actionSource = ActionSource.Api(weeklyGoal.accountId),
            )

            val result = goalService.update(command).map { fail("deveria ser left") }.getOrElse { it }

            result.shouldBeTypeOf<UpdateGoalError.InvalidGoalStatus>()

            val updatedGoal = goalRepository.find(weeklyGoal.id).getOrElse { fail("deveria ser right") }

            updatedGoal shouldBe createdGoal

            verify(exactly = 0) {
                goalMessagePublisher.publishOptimizeGoalInstallmentAmount(createdGoal.id)
            }
        }

        @Test
        fun `não deve atualizar o vencimento quando a meta está ativa e a liquidez é no vencimento`() {
            val createdGoal = goalRepository.save(
                weeklyGoal.copy(
                    endDate = getLocalDate().plusDays(1),
                    createdAt = getZonedDateTime().minusDays(7),
                    updatedAt = getZonedDateTime().minusDays(7),
                    status = GoalStatus.ACTIVE,
                    liquidity = GoalProductLiquidity.MATURITY,
                ),
            )

            val command = UpdateGoalCommand(
                goalId = weeklyGoal.id,
                name = null,
                amount = null,
                endDate = getLocalDate().plusWeeks(5),
                installmentFrequency = null,
                installmentExpirationDay = null,
                actionSource = ActionSource.Api(weeklyGoal.accountId),
            )

            val result = goalService.update(command).map { fail("deveria ser left") }.getOrElse { it }

            result.shouldBeTypeOf<UpdateGoalError.InvalidGoalStatus>()

            val updatedGoal = goalRepository.find(weeklyGoal.id).getOrElse { fail("deveria ser right") }

            updatedGoal shouldBe createdGoal

            verify(exactly = 0) {
                goalMessagePublisher.publishOptimizeGoalInstallmentAmount(createdGoal.id)
            }
        }

        @Test
        fun `não deve atualizar as parcelas se a meta estiver concluida`() {
            val createdGoal = goalRepository.save(
                weeklyGoal.copy(
                    endDate = getLocalDate().minusDays(1),
                    createdAt = getZonedDateTime().minusDays(7),
                    updatedAt = getZonedDateTime().minusDays(7),
                    status = GoalStatus.COMPLETED,
                    liquidity = GoalProductLiquidity.MATURITY,
                ),
            )

            val command = UpdateGoalCommand(
                goalId = weeklyGoal.id,
                name = null,
                amount = null,
                endDate = null,
                installmentFrequency = InstallmentFrequency.MONTHLY,
                installmentExpirationDay = "10",
                actionSource = ActionSource.Api(weeklyGoal.accountId),
            )

            val result = goalService.update(command).map { fail("deveria ser left") }.getOrElse { it }

            result.shouldBeTypeOf<UpdateGoalError.InvalidGoalStatus>()

            val updatedGoal = goalRepository.find(weeklyGoal.id).getOrElse { fail("deveria ser right") }

            updatedGoal shouldBe createdGoal

            verify(exactly = 0) {
                goalMessagePublisher.publishOptimizeGoalInstallmentAmount(createdGoal.id)
            }
        }

        @Test
        @Disabled
        fun `deve atualizar as parcelas quando a meta está ativa remover as parcelas em aberto e gerar a proxima`() {
            val createdGoal = goalRepository.save(
                weeklyGoal.copy(
                    endDate = LocalDate.of(2025, 3, 3),
                    createdAt = LocalDate.of(2024, 12, 9).atStartOfDay(brazilTimeZone),
                    updatedAt = LocalDate.of(2024, 12, 9).atStartOfDay(brazilTimeZone),
                    status = GoalStatus.ACTIVE,
                    liquidity = GoalProductLiquidity.MATURITY,
                ),
            )

            val command = UpdateGoalCommand(
                goalId = weeklyGoal.id,
                name = null,
                amount = null,
                endDate = null,
                installmentFrequency = InstallmentFrequency.MONTHLY,
                installmentExpirationDay = "10",
                actionSource = ActionSource.Api(weeklyGoal.accountId),
            )

            every {
                goalInvestmentService.findAll(goalId = createdGoal.id)
            } returns emptyList()

            every {
                goalInvestmentService.ignoreInvestments(createdGoal, any(), command.actionSource)
            } returns Unit.right()

            every {
                goalInvestmentService.create(CreateRegularGoalInvestmentCommand(createdGoal.id))
            } returns Unit.right()

            val currentDate = LocalDate.of(2024, 12, 23).atStartOfDay(brazilTimeZone)
            val result = withGivenDateTime(currentDate) { goalService.update(command).getOrElse { fail("deveria ser right") } }

            result.installments.frequency shouldBe command.installmentFrequency
            result.installments.expirationDay shouldBe command.installmentExpirationDay

            val updatedGoal = goalRepository.find(weeklyGoal.id).getOrElse { fail("deveria ser right") }

            updatedGoal.shouldBeEqualToIgnoringFields(
                createdGoal,
                createdGoal::endDate,
                createdGoal::installments,
                createdGoal::totalInstallments,
                createdGoal::updatedAt,
            )
            updatedGoal.installments.frequency shouldBe command.installmentFrequency
            updatedGoal.installments.expirationDay shouldBe command.installmentExpirationDay
            updatedGoal.totalInstallments shouldBe 3

            (updatedGoal.updatedAt > currentDate.minusSeconds(1)) shouldBe true

            verify {
                goalMessagePublisher.publishOptimizeGoalInstallmentAmount(createdGoal.id)
                goalInvestmentService.findAll(any())
                goalInvestmentService.ignoreInvestments(any(), any(), any())
                goalInvestmentService.create(CreateRegularGoalInvestmentCommand(createdGoal.id))
            }
        }
    }

    @Nested
    @DisplayName("ao otimizar uma meta")
    inner class OptimizeGoalInstallmentAmount {

        @BeforeEach
        fun init() {
            every {
                investmentManagerService.userGoalPositions(any(), any())
            } answers {
                UserGoalPositions(
                    accountId = weeklyGoal.accountId,
                    goalId = secondArg(),
                    positions = emptyList(),
                    netValue = 0,
                    grossValue = 0,
                    investedValue = 0,
                    irValue = 0,
                    iofValue = 0,
                    earningValue = 0,
                ).right()
            }

            every {
                goalInvestmentService.findAll(any())
            } returns emptyList()
        }

        @Test
        fun `quando a meta não for encontrada deve falhar com GoalNotFoundException`() {
            val result = goalService.optimizeGoalInstallmentAmount(GoalId())

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<GoalNotFoundException>()
            }
        }

        @Test
        fun `quando a meta estiver concluida não deve otimizar`() {
            val createdGoal = goalRepository.save(weeklyGoal.copy(endDate = getLocalDate().minusDays(1)))

            every {
                goalProductService.findProduct(any())
            } returns IllegalStateException().left()

            val result = goalService.optimizeGoalInstallmentAmount(createdGoal.id).getOrElse {
                fail("deveria ser right")
            }

            result.shouldBeFalse()
        }

        @Test
        fun `quando o produto da meta não for encontrado deve falhar`() {
            val createdGoal = goalRepository.save(weeklyGoal)

            every {
                goalProductService.findProduct(any())
            } returns IllegalStateException().left()

            val result = goalService.optimizeGoalInstallmentAmount(createdGoal.id)

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<IllegalStateException>()
            }
        }

        @Test
        fun `quando a consulta a posição atual do usuário falhar`() {
            val createdGoal = goalRepository.save(weeklyGoal)
            every {
                investmentManagerService.userGoalPositions(any(), any())
            } returns InvestmentManagerException(NoStackTraceException()).left()

            val result = goalService.optimizeGoalInstallmentAmount(createdGoal.id)

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<InvestmentManagerException>()
            }
        }

        @Test
        fun `quando a consulta de simulação de valor futuro falhar`() {
            val createdGoal = goalRepository.save(weeklyGoal)

            every {
                investmentManagerService.simulateFutureNetValue(any(), any(), any(), any(), any(), any(), any())
            } returns InvestmentManagerException(NoStackTraceException()).left()

            val result = goalService.optimizeGoalInstallmentAmount(createdGoal.id)

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<InvestmentManagerException>()
            }
        }

        @Test
        fun `quando a consulta de valor otimizado de valor futuro falhar`() {
            val createdGoal = goalRepository.save(weeklyGoal)

            every {
                investmentManagerService.simulateFutureNetValue(any(), any(), any(), any(), any(), any(), any())
            } returns createdGoal.amount.right()

            every {
                investmentManagerService.optimizeMonthlyContribution(any(), any(), any(), any(), any(), any(), any())
            } returns InvestmentManagerException(NoStackTraceException()).left()

            val result = goalService.optimizeGoalInstallmentAmount(createdGoal.id)

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<InvestmentManagerException>()
            }
        }

        @Test
        fun `quando tiver parcela extra não vencida, deve considera-la na otimização se a meta tiver sido criada a 7 dias ou menos`() {
            val createdGoal = goalRepository.save(weeklyGoal.copy(createdAt = getZonedDateTime().minusDays(7).plusMinutes(2)))

            every {
                investmentManagerService.simulateFutureNetValue(any(), any(), any(), any(), any(), any(), any())
            } returns (createdGoal.amount.toDouble()).toLong().right()

            every {
                investmentManagerService.optimizeMonthlyContribution(any(), any(), any(), any(), any(), any(), any())
            } returns 0L.right()

            every {
                goalInvestmentService.ignoreInvestments(any(), any(), any())
            } returns Unit.right()

            every {
                goalInvestmentService.create(CreateRegularGoalInvestmentCommand(createdGoal.id))
            } returns Unit.right()

            val extraAmount = 123_45L

            every {
                goalInvestmentService.findAll(createdGoal.id)
            } returns listOf(
                GoalInvestment(
                    goalId = createdGoal.id,
                    billId = BillId(),
                    amount = createdGoal.installments.amount,
                    dueDate = getLocalDate().minusDays(1),
                    paidAt = null,
                    extraInstallment = false,
                    status = GoalInvestmentStatus.CREATED,
                ),

                GoalInvestment(
                    goalId = createdGoal.id,
                    billId = BillId(),
                    amount = extraAmount,
                    dueDate = getLocalDate(),
                    paidAt = getZonedDateTime(),
                    extraInstallment = true,
                    status = GoalInvestmentStatus.CREATED,
                ),

                GoalInvestment(
                    goalId = createdGoal.id,
                    billId = BillId(),
                    amount = extraAmount,
                    dueDate = getLocalDate().minusDays(8),
                    paidAt = null,
                    extraInstallment = true,
                    status = GoalInvestmentStatus.CREATED,
                ),

                GoalInvestment(
                    goalId = createdGoal.id,
                    billId = BillId(),
                    amount = extraAmount,
                    dueDate = getLocalDate().minusDays(7),
                    paidAt = null,
                    extraInstallment = true,
                    status = GoalInvestmentStatus.CREATED,
                ),
            )

            val result = goalService.optimizeGoalInstallmentAmount(createdGoal.id).getOrElse {
                fail("deveria ser right")
            }

            result.shouldBeFalse()

            verify {
                investmentManagerService.simulateFutureNetValue(extraAmount, any(), any(), any(), any(), any(), any())
                investmentManagerService.optimizeMonthlyContribution(extraAmount, any(), any(), any(), any(), any(), any())
            }
        }

        @Test
        fun `não deve considerar parcelas extras vencidas recentes, se a meta tiver mais de 7 dias`() {
            val createdGoal = goalRepository.save(weeklyGoal.copy(createdAt = getZonedDateTime().minusDays(8)))

            every {
                investmentManagerService.simulateFutureNetValue(any(), any(), any(), any(), any(), any(), any())
            } returns (createdGoal.amount.toDouble()).toLong().right()

            every {
                investmentManagerService.optimizeMonthlyContribution(any(), any(), any(), any(), any(), any(), any())
            } returns 0L.right()

            every {
                goalInvestmentService.ignoreInvestments(any(), any(), any())
            } returns Unit.right()

            every {
                goalInvestmentService.create(CreateRegularGoalInvestmentCommand(createdGoal.id))
            } returns Unit.right()

            val extraAmount = 123_45L

            every {
                goalInvestmentService.findAll(createdGoal.id)
            } returns listOf(
                GoalInvestment(
                    goalId = createdGoal.id,
                    billId = BillId(),
                    amount = createdGoal.installments.amount,
                    dueDate = getLocalDate().minusDays(1),
                    paidAt = null,
                    extraInstallment = false,
                    status = GoalInvestmentStatus.CREATED,
                ),

                GoalInvestment(
                    goalId = createdGoal.id,
                    billId = BillId(),
                    amount = extraAmount,
                    dueDate = getLocalDate(),
                    paidAt = getZonedDateTime(),
                    extraInstallment = true,
                    status = GoalInvestmentStatus.CREATED,
                ),

                GoalInvestment(
                    goalId = createdGoal.id,
                    billId = BillId(),
                    amount = extraAmount,
                    dueDate = getLocalDate().minusDays(8),
                    paidAt = null,
                    extraInstallment = true,
                    status = GoalInvestmentStatus.CREATED,
                ),

                GoalInvestment(
                    goalId = createdGoal.id,
                    billId = BillId(),
                    amount = extraAmount,
                    dueDate = getLocalDate().minusDays(7),
                    paidAt = null,
                    extraInstallment = true,
                    status = GoalInvestmentStatus.CREATED,
                ),
            )

            val result = goalService.optimizeGoalInstallmentAmount(createdGoal.id).getOrElse {
                fail("deveria ser right")
            }

            result.shouldBeFalse()

            verify {
                investmentManagerService.simulateFutureNetValue(0, any(), any(), any(), any(), any(), any())
                investmentManagerService.optimizeMonthlyContribution(0, any(), any(), any(), any(), any(), any())
            }
        }

        @ParameterizedTest
        @ValueSource(doubles = [1.0, 1.0499, 0.951])
        fun `quando a diferenca para o valor da meta for menor que 5por cento nao deve atualizar o valor e cancelar a parcela vencida`(perc: Double) {
            val createdGoal = goalRepository.save(weeklyGoal)

            every {
                investmentManagerService.simulateFutureNetValue(any(), any(), any(), any(), any(), any(), any())
            } returns (createdGoal.amount.toDouble() * perc).toLong().right()

            every {
                investmentManagerService.optimizeMonthlyContribution(any(), any(), any(), any(), any(), any(), any())
            } returns 0L.right()

            every {
                goalInvestmentService.ignoreInvestments(any(), any(), any())
            } returns Unit.right()

            every {
                goalInvestmentService.create(CreateRegularGoalInvestmentCommand(createdGoal.id))
            } returns Unit.right()

            every {
                goalInvestmentService.findAll(createdGoal.id)
            } returns listOf(
                GoalInvestment(
                    goalId = createdGoal.id,
                    billId = BillId(),
                    amount = createdGoal.installments.amount,
                    dueDate = getLocalDate().minusDays(1),
                    paidAt = null,
                    extraInstallment = false,
                    status = GoalInvestmentStatus.CREATED,
                ),
            )

            val result = goalService.optimizeGoalInstallmentAmount(createdGoal.id).getOrElse {
                fail("deveria ser right")
            }

            result.shouldBeFalse()

            verify(exactly = 0) {
                goalInvestmentService.updateInvestmentAmount(any(), any())
                updateBillService.updateAmount(any(), any(), any<ActionSource.GoalInvestment>())
                goalsInvestmentNotificationService.notifyInvestmentValueOptmized(any(), any(), any())
            }

            verify(exactly = 1) {
                goalInvestmentService.ignoreInvestments(any(), any(), any())
                goalInvestmentService.create(CreateRegularGoalInvestmentCommand(createdGoal.id))
            }

            val storedGoal = goalRepository.findOrNull(createdGoal.id).shouldNotBeNull()
            storedGoal.amount shouldBe createdGoal.amount
            storedGoal.installmentOptimizedAmount.shouldBeNull()
            storedGoal.lastValueOptimizationAt.shouldNotBeNull()
        }

        @ParameterizedTest
        @ValueSource(doubles = [1.5, 0.95])
        fun `quando a diferença para o valor da meta semanal for maior ou igual que 5 por cento deve atualizar o valor e notificar o usuario`(perc: Double) {
            val createdGoal = goalRepository.save(weeklyGoal)

            every {
                investmentManagerService.simulateFutureNetValue(any(), any(), any(), any(), any(), any(), any())
            } returns (createdGoal.amount.toDouble() * perc).toLong().right()

            every {
                investmentManagerService.optimizeMonthlyContribution(any(), any(), any(), any(), any(), any(), any())
            } returns 400_00L.right()

            every {
                goalInvestmentService.findAll(createdGoal.id)
            } returns listOf(
                GoalInvestment(
                    goalId = createdGoal.id,
                    billId = BillId(),
                    amount = createdGoal.installments.amount,
                    dueDate = getLocalDate().minusDays(1),
                    paidAt = null,
                    extraInstallment = false,
                    status = GoalInvestmentStatus.CREATED,
                ),
                GoalInvestment(
                    goalId = createdGoal.id,
                    billId = BillId(),
                    amount = createdGoal.installments.amount,
                    dueDate = getLocalDate(),
                    paidAt = getZonedDateTime(),
                    extraInstallment = false,
                    status = GoalInvestmentStatus.DONE,
                ),
                GoalInvestment(
                    goalId = createdGoal.id,
                    billId = BillId(),
                    amount = createdGoal.installments.amount,
                    dueDate = getLocalDate(),
                    paidAt = null,
                    extraInstallment = true,
                    status = GoalInvestmentStatus.CREATED,
                ),
            )

            every {
                goalInvestmentService.ignoreInvestments(any(), any(), any())
            } returns Unit.right()

            every {
                goalInvestmentService.updateInvestmentAmount(any(), any())
            } just runs

            every {
                updateBillService.updateAmount(any(), any(), any<ActionSource.GoalInvestment>())
            } returns mockk<Bill>().right()

            every {
                goalsInvestmentNotificationService.notifyInvestmentValueOptmized(any(), any(), any())
            } just runs

            every {
                goalInvestmentService.create(any())
            } returns Unit.right()

            val result = goalService.optimizeGoalInstallmentAmount(createdGoal.id).getOrElse {
                fail("deveria ser right")
            }

            result.shouldBeTrue()

            verify(exactly = 1) {
                goalInvestmentService.findAll(any())
                goalInvestmentService.updateInvestmentAmount(any(), any())
                updateBillService.updateAmount(any(), any(), any<ActionSource.GoalInvestment>())
            }

            verifyOrder {
                goalInvestmentService.ignoreInvestments(any(), any(), any())
                goalInvestmentService.create(CreateRegularGoalInvestmentCommand(createdGoal.id))
                goalsInvestmentNotificationService.notifyInvestmentValueOptmized(any(), any(), any())
            }

            val storedGoal = goalRepository.findOrNull(createdGoal.id).shouldNotBeNull()
            storedGoal.amount shouldBe createdGoal.amount
            storedGoal.installmentOptimizedAmount shouldBe 400_00L
            storedGoal.lastValueOptimizationAt.shouldNotBeNull()
        }

        @ParameterizedTest
        @ValueSource(doubles = [1.5, 0.95])
        fun `quando a diferença para o valor da meta mensal for maior ou igual que 5 porcento deve atualizar o valor mensal e notificar o usuário`(perc: Double) {
            val createdGoal = goalRepository.save(monthlyGoal)

            every {
                investmentManagerService.simulateFutureNetValue(any(), any(), any(), any(), any(), any(), any())
            } returns (createdGoal.amount.toDouble() * perc).toLong().right()

            every {
                investmentManagerService.optimizeMonthlyContribution(any(), any(), any(), any(), any(), any(), any())
            } returns 400_00L.right()

            every {
                goalInvestmentService.findAll(createdGoal.id)
            } returns listOf(
                GoalInvestment(
                    goalId = createdGoal.id,
                    billId = BillId(),
                    amount = createdGoal.installments.amount,
                    dueDate = getLocalDate(),
                    paidAt = null,
                    extraInstallment = false,
                    status = GoalInvestmentStatus.CREATED,
                ),
                GoalInvestment(
                    goalId = createdGoal.id,
                    billId = BillId(),
                    amount = createdGoal.installments.amount,
                    dueDate = getLocalDate(),
                    paidAt = getZonedDateTime(),
                    extraInstallment = false,
                    status = GoalInvestmentStatus.DONE,
                ),
                GoalInvestment(
                    goalId = createdGoal.id,
                    billId = BillId(),
                    amount = createdGoal.installments.amount,
                    dueDate = getLocalDate(),
                    paidAt = null,
                    extraInstallment = true,
                    status = GoalInvestmentStatus.CREATED,
                ),
            )

            every {
                goalInvestmentService.ignoreInvestments(any(), any(), any())
            } returns Unit.right()

            every {
                goalInvestmentService.updateInvestmentAmount(any(), any())
            } just runs

            every {
                updateBillService.updateAmount(any(), any(), any<ActionSource.GoalInvestment>())
            } returns mockk<Bill>().right()

            every {
                goalsInvestmentNotificationService.notifyInvestmentValueOptmized(any(), any(), any())
            } just runs

            val result = goalService.optimizeGoalInstallmentAmount(createdGoal.id).getOrElse {
                fail("deveria ser right")
            }

            result.shouldBeTrue()

            verify(exactly = 1) {
                goalInvestmentService.findAll(any())
                goalInvestmentService.updateInvestmentAmount(any(), any())
                updateBillService.updateAmount(any(), any(), any<ActionSource.GoalInvestment>())
                goalsInvestmentNotificationService.notifyInvestmentValueOptmized(any(), any(), any())
            }

            verify(exactly = 0) {
                goalInvestmentService.ignoreInvestments(any(), any(), any())
                goalInvestmentService.create(any())
            }

            val storedGoal = goalRepository.findOrNull(createdGoal.id).shouldNotBeNull()
            storedGoal.amount shouldBe createdGoal.amount
            storedGoal.installmentOptimizedAmount shouldBe 400_00L
            storedGoal.lastValueOptimizationAt.shouldNotBeNull()
        }
    }

    @Nested
    @DisplayName("ao buscar metas para serem otimizadas")
    inner class FindGoalsToOptimize {

        private val baseGoal = Goal(
            id = GoalId("1"),
            accountId = AccountId(ACCOUNT_ID),
            walletId = goalWallet.id,
            categoryId = goalCategoryIdEF,
            name = "Emergency Fund",
            status = GoalStatus.ACTIVE,
            amount = 1000,
            endDate = LocalDate.now().plusMonths(6),
            liquidity = GoalProductLiquidity.MATURITY,
            installments = MonthlyInstallments(6, 1),
            productId = enabledMonthlyCDIProduct.id,
            imageUrl = "https://image.com",
            installmentInitialAmount = 100,
            lastKnownNetBalanceAmount = 0,
            lastKnownNetBalanceAt = getZonedDateTime(),
        )

        val monday = ZonedDateTime.of(2025, 5, 19, 7, 30, 0, 0, brazilTimeZone)
        val dayOne = ZonedDateTime.of(2025, 4, 1, 7, 30, 0, 0, brazilTimeZone)

        @Test
        fun `deve encontrar metas semanais que nunca foram otimizadas`() {
            withGivenDateTime(monday) {
                goalRepository.save(
                    baseGoal.copy(
                        lastValueOptimizationAt = null,
                        installments = WeeklyInstallments(6, WeeklyInstallmentDay.valueOf(getLocalDate().dayOfWeek.name)),
                    ),
                )
                val goals = goalService.findGoalsEligibleToOptimization(installmentFrequency = InstallmentFrequency.WEEKLY)
                goals.size shouldBe 1
            }
        }

        @Test
        fun `deve encontrar metas semanais otimizadas a exatamente uma semana`() {
            withGivenDateTime(monday) {
                goalRepository.save(
                    baseGoal.copy(
                        lastValueOptimizationAt = getZonedDateTime().minusWeeks(1).plusMinutes(10),
                        installments = WeeklyInstallments(6, WeeklyInstallmentDay.valueOf(getLocalDate().dayOfWeek.name)),
                    ),
                )
                val goals = goalService.findGoalsEligibleToOptimization(installmentFrequency = InstallmentFrequency.WEEKLY)
                goals.size shouldBe 1
            }
        }

        @Test
        fun `deve encontrar metas semanais otimizadas a mais de uma semana`() {
            withGivenDateTime(monday) {
                goalRepository.save(
                    baseGoal.copy(
                        lastValueOptimizationAt = getZonedDateTime().minusWeeks(1).minusDays(1).plusMinutes(10),
                        installments = WeeklyInstallments(6, WeeklyInstallmentDay.valueOf(getLocalDate().dayOfWeek.name)),
                    ),
                )

                val goals = goalService.findGoalsEligibleToOptimization(installmentFrequency = InstallmentFrequency.WEEKLY)
                goals.size shouldBe 1
            }
        }

        @Test
        fun `não deve encontrar metas semanais otimizadas a menos de uma semana`() {
            withGivenDateTime(monday) {
                goalRepository.save(
                    baseGoal.copy(
                        lastValueOptimizationAt = getZonedDateTime().minusWeeks(1).plusDays(1).minusMinutes(10),
                        installments = WeeklyInstallments(6, WeeklyInstallmentDay.valueOf(getLocalDate().dayOfWeek.name)),
                    ),
                )

                val goals = goalService.findGoalsEligibleToOptimization(installmentFrequency = InstallmentFrequency.WEEKLY)
                goals.size shouldBe 0
            }
        }

        @Test
        fun `deve encontrar metas mensais que nunca foram otimizadas`() {
            withGivenDateTime(dayOne) {
                goalRepository.save(
                    baseGoal.copy(
                        lastValueOptimizationAt = null,
                        installments = MonthlyInstallments(6, getLocalDate().dayOfMonth),
                    ),
                )

                val goals = goalService.findGoalsEligibleToOptimization(installmentFrequency = InstallmentFrequency.MONTHLY)
                goals.size shouldBe 1
            }
        }

        @Test
        fun `deve encontrar metas mensais otimizadas a exatamente um mes`() {
            withGivenDateTime(dayOne) {
                goalRepository.save(
                    baseGoal.copy(
                        lastValueOptimizationAt = getZonedDateTime().minusMonths(1).plusMinutes(10),
                        installments = MonthlyInstallments(6, getLocalDate().dayOfMonth),
                    ),
                )

                val goals = goalService.findGoalsEligibleToOptimization(installmentFrequency = InstallmentFrequency.MONTHLY)
                goals.size shouldBe 1
            }
        }

        @Test
        fun `deve encontrar metas mensais otimizadas a mais de um mes`() {
            withGivenDateTime(dayOne) {
                goalRepository.save(
                    baseGoal.copy(
                        lastValueOptimizationAt = getZonedDateTime().minusMonths(1).minusDays(1).plusMinutes(10),
                        installments = MonthlyInstallments(6, getLocalDate().dayOfMonth),
                    ),
                )

                val goals = goalService.findGoalsEligibleToOptimization(installmentFrequency = InstallmentFrequency.MONTHLY)
                goals.size shouldBe 1
            }
        }

        @Test
        fun `não deve encontrar metas mensais otimizadas a menos de um mes`() {
            withGivenDateTime(dayOne) {
                goalRepository.save(
                    baseGoal.copy(
                        lastValueOptimizationAt = getZonedDateTime().minusMonths(1).plusDays(1).minusMinutes(10),
                        installments = MonthlyInstallments(6, getLocalDate().dayOfMonth),
                    ),
                )

                val goals = goalService.findGoalsEligibleToOptimization(installmentFrequency = InstallmentFrequency.MONTHLY)
                goals.size shouldBe 0
            }
        }
    }

    private fun mockkGoal(achievement: Long): Goal {
        val goalId = GoalId()
        val today = getZonedDateTime()
        val goalStartDate = today.minusDays(achievement)
        val goalEndDate = goalStartDate.plusDays(100)

        return mockk {
            every {
                id
            } returns goalId
            every {
                createdAt
            } returns goalStartDate
            every {
                endDate
            } returns goalEndDate.toLocalDate()
            every {
                liquidity
            } returns GoalProductLiquidity.MATURITY
        }
    }

    private infix fun Goal.shouldMatchCreateCommandGoal(command: CreateGoalCommand) {
        this.accountId shouldBe command.accountId
        this.name shouldBe command.name
        this.categoryId shouldBe command.categoryId
        this.imageUrl shouldBe command.imageUrl
        this.amount shouldBe command.amount
        this.endDate shouldBe command.endDate
        this.liquidity shouldBe command.liquidity
        this.installments.frequency shouldBe command.installmentFrequency
        this.installments.amount shouldBe command.installmentAmount
        this.installments.expirationDay shouldBe command.installmentExpirationDay
    }

    companion object {

        private val goalCategoryIdEF = GoalCategoryId("GOAL-CATEGORY-EMERGENCY_FUND")
        private val goalCategoryIdHome = GoalCategoryId("GOAL-CATEGORY-HOME")

        private val validWeeklyGoalRequest = CreateGoalCommand(
            accountId = goalAccountId,
            categoryId = goalCategoryIdHome,
            name = "weekly goal",
            amount = 1,
            endDate = LocalDate.parse("2024-12-31", dateFormat),

            installmentFrequency = InstallmentFrequency.WEEKLY,
            installmentAmount = 5989,
            installmentExpirationDay = WeeklyInstallmentDay.TUESDAY.name,

            imageUrl = "weekly image url",
            liquidity = GoalProductLiquidity.DAILY,
            initialInvestmentAmount = 10_000_00,
        )

        private val validMonthlyGoalRequest = CreateGoalCommand(
            accountId = goalAccountId,
            categoryId = goalCategoryIdHome,
            name = "monthly goal",
            amount = 1987,
            endDate = LocalDate.parse("2025-01-01", dateFormat),

            installmentFrequency = InstallmentFrequency.MONTHLY,
            installmentAmount = 342,
            installmentExpirationDay = "5",

            imageUrl = "monthly image url",
            liquidity = GoalProductLiquidity.MATURITY,
            initialInvestmentAmount = 10_000_00,
        )

        private val invalidMontlhyExpirationDayRequest = validMonthlyGoalRequest.copy(installmentExpirationDay = "MONDAY", categoryId = goalCategoryIdHome)
        private val invalidWeeklyExpirationDayRequest = validWeeklyGoalRequest.copy(installmentExpirationDay = "1", categoryId = goalCategoryIdHome)

        @JvmStatic
        fun goals() = listOf(weeklyGoal.copy(categoryId = goalCategoryIdHome), monthlyGoal.copy(categoryId = goalCategoryIdHome))

        @JvmStatic
        fun createGoalCommands() = listOf(createWeeklyGoalCommand.copy(categoryId = goalCategoryIdHome), createMonthlyGoalCommand.copy(categoryId = goalCategoryIdHome))

        @JvmStatic
        fun createGoalWithInitialInvestmentCommands() = listOf(createGoalWithInitialInvestmentCommand.copy(categoryId = goalCategoryIdHome))

        @JvmStatic
        fun invalidRequests() = listOf(
            invalidMontlhyExpirationDayRequest,
            invalidWeeklyExpirationDayRequest,
        )
    }
}