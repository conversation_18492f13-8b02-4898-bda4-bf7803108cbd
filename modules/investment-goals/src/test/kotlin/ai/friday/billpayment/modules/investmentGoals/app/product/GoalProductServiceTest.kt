package ai.friday.billpayment.modules.investmentGoals.app.product

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb.GoalProductDbRepository
import ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb.GoalProductDynamoDAO
import ai.friday.billpayment.modules.investmentGoals.app.goal.Goal
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalRepository
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndex
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerService
import ai.friday.billpayment.modules.investmentGoals.cdiInterestRate
import ai.friday.billpayment.modules.investmentGoals.dailyCDIProduct
import ai.friday.billpayment.modules.investmentGoals.enabledMonthlyCDIProduct
import ai.friday.billpayment.modules.investmentGoals.savingsInterestRate
import ai.friday.billpayment.modules.investmentGoals.shouldBeGoalProduct
import ai.friday.billpayment.modules.investmentGoals.shouldHaveGoalProduct
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import arrow.core.getOrElse
import arrow.core.right
import io.kotest.core.spec.DisplayName
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.ints.shouldBeLessThan
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource

class GoalProductServiceTest {
    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val goalProductRepository = GoalProductDbRepository(GoalProductDynamoDAO(dynamoDbEnhancedClient))
    private val goalRepository = mockk<GoalRepository> {
        every {
            findByProductId(any())
        } returns emptyList()
    }

    private val investmentManagerService = mockk<InvestmentManagerService>()

    private val goalProductService = GoalProductService(
        goalProductRepository = goalProductRepository,
        goalRepository = goalRepository,
        investmentManagerService = investmentManagerService,
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Nested
    @DisplayName("ao salvar um produto")
    inner class Save {
        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductServiceTest#validSaveCommands")
        fun `deve criar um novo`(command: SaveGoalProductCommand) {
            val result = goalProductService.create(command)

            result.isRight() shouldBe true
            result.map {
                it shouldMatchSaveCommand command
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductServiceTest#validSaveCommands")
        fun `deve atualizar um existente`(command: SaveGoalProductCommand) {
            val product = goalProductService.create(dummyCommand).getOrElse { throw it }

            val result = goalProductService.update(product.id, command)

            result.isRight() shouldBe true
            result.map {
                it shouldMatchSaveCommand command
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductServiceTest#validSaveCommands")
        fun `nao deve atualizar se estiver em uso`(command: SaveGoalProductCommand) {
            val product = goalProductService.create(dummyCommand).getOrElse { throw it }

            every {
                goalRepository.findByProductId(product.id)
            } returns listOf(
                mockk<Goal> {
                    every {
                        id
                    } returns GoalId()
                },
            )

            val result = goalProductService.update(product.id, command)

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<IllegalStateException>()
            }
        }
    }

    @Nested
    @DisplayName("ao buscar produtos")
    inner class Find {
        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductServiceTest#validSaveCommands")
        fun `deve buscar um`(command: SaveGoalProductCommand) {
            val product = goalProductService.create(command).getOrElse { throw it }

            val result = goalProductService.findProduct(product.id)

            result.isRight() shouldBe true
            result.map {
                it shouldMatchSaveCommand command
                it shouldBeGoalProduct product
            }
        }

        @Test
        fun `deve buscar todos os existentes`() {
            val allProducts = validSaveCommands().map { command ->
                goalProductService.create(command).getOrElse { throw it }
            }

            val products = goalProductService.findAll()

            products.size shouldBe allProducts.size
            allProducts.forEach {
                products shouldHaveGoalProduct it
            }
        }

        @Test
        fun `deve buscar todos os habilitados`() {
            val allProducts = validSaveCommands().map { command ->
                goalProductService.create(command).getOrElse { throw it }
            }

            val enabledProducts = allProducts.filter { it.enabled }
            enabledProducts.size shouldBeLessThan allProducts.size

            val products = goalProductService.findAllEnabled()

            products.size shouldBe enabledProducts.size
            enabledProducts.forEach {
                products shouldHaveGoalProduct it
            }
        }
    }

    @Nested
    @DisplayName("ao eleger um produto")
    inner class Choose {

        @Test
        fun `quando a liquidez eh diaria deve retornar 100pc CDI`() {
            goalProductRepository.save(enabledMonthlyCDIProduct)
            goalProductRepository.save(dailyCDIProduct)

            val result = goalProductService.choose(
                liquidity = GoalProductLiquidity.DAILY,
                endDate = null,
            )

            result.isRight() shouldBe true
            result.map {
                it shouldBeGoalProduct dailyCDIProduct
            }
        }

        @Test
        fun `quando a liquidez eh no vencimento a data deve ser obrigatoria`() {
            val result = goalProductService.choose(
                liquidity = GoalProductLiquidity.MATURITY,
                endDate = null,
            )

            result.isLeft() shouldBe true
            result.mapLeft {
                it shouldBe SelectGoalProductError.MandatoryEndDate
            }
        }

        @ParameterizedTest
        @CsvSource(value = ["0,1", "364,1", "365,2", "729,2", "730,3", "2000,3"])
        fun `quando a liquidez eh no vencimento a data deve ser considerada`(daysAhead: Long, expectedIncome: Int) {
            createProduct(indexIncome = 1, minimumTermDays = 0, maximumTermDays = 365)
            createProduct(indexIncome = 2, minimumTermDays = 365, maximumTermDays = 730)
            createProduct(indexIncome = 3, minimumTermDays = 730, maximumTermDays = null)

            val result = goalProductService.choose(
                liquidity = GoalProductLiquidity.MATURITY,
                endDate = getLocalDate().plusDays(daysAhead),
            )

            result.isRight() shouldBe true
            result.map {
                it.indexIncome.rate shouldBe expectedIncome
            }
        }

        @Test
        fun `quando mais de um produto eh elegivel deve retornar erro`() {
            val product1 = createProduct(minimumTermDays = 0, maximumTermDays = 10)
            val product2 = createProduct(minimumTermDays = 5, maximumTermDays = 15)

            val result = goalProductService.choose(
                liquidity = GoalProductLiquidity.MATURITY,
                endDate = getLocalDate().plusDays(7),
            )

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<SelectGoalProductError.MultipleProductsFound>()
                it.productIds shouldContainExactlyInAnyOrder listOf(product1.id, product2.id)
            }
        }

        @Test
        fun `deve considerar apenas produtos habilitados`() {
            createProduct(minimumTermDays = 0, maximumTermDays = 10)
            createProduct(minimumTermDays = 5, maximumTermDays = 15, enabled = false)

            val result = goalProductService.choose(
                liquidity = GoalProductLiquidity.MATURITY,
                endDate = getLocalDate().plusDays(7),
            )

            result.isRight() shouldBe true
        }

        @Test
        fun `deve retornar erro se nenhum produto eh encontrado`() {
            createProduct(minimumTermDays = 0, maximumTermDays = 10, enabled = false)
            createProduct(minimumTermDays = 5, maximumTermDays = 15, enabled = false)

            val result = goalProductService.choose(
                liquidity = GoalProductLiquidity.MATURITY,
                endDate = getLocalDate().plusDays(7),
            )

            result.isLeft() shouldBe true
            result.mapLeft {
                it shouldBe SelectGoalProductError.ProductNotFound
            }
        }
    }

    @Nested
    @DisplayName("ao verificar a integridade dos prazos")
    inner class CheckTermIntegrity {

        @ParameterizedTest
        @CsvSource(
            "3,5",
            "3,6",
            "3,7",
            "4,5",
            "4,6",
            "4,7",
            "5,6",
            "5,7",
        )
        fun `deve indicar sobreposicao`(min: Long, max: Long) {
            val product = createProduct(minimumTermDays = 4, maximumTermDays = 6)
            val anotherProduct = createProduct(minimumTermDays = min, maximumTermDays = max)

            product.overlapsWith(anotherProduct) shouldBe true
        }

        @ParameterizedTest
        @CsvSource(
            "1,3",
            "1,4",
            "6,8",
            "7,8",
        )
        fun `nao deve indicar sobreposicao`(min: Long, max: Long) {
            val product = createProduct(minimumTermDays = 4, maximumTermDays = 6)
            val anotherProduct = createProduct(minimumTermDays = min, maximumTermDays = max)

            product.overlapsWith(anotherProduct) shouldBe false
        }

        @Test
        fun `nao deve indicar sobreposicao quando eh o mesmo produto`() {
            val product = createProduct(minimumTermDays = 4, maximumTermDays = 6)

            product.overlapsWith(product) shouldBe false
        }

        @Test
        fun `deve retornar erro quando existe intervalo sobreposto`() {
            createProduct(minimumTermDays = 0, maximumTermDays = 10)
            val product2 = createProduct(minimumTermDays = 10, maximumTermDays = 20)
            val product3 = createProduct(minimumTermDays = 15, maximumTermDays = 25)
            val product4 = createProduct(minimumTermDays = 20, maximumTermDays = 30)
            createProduct(minimumTermDays = 30, maximumTermDays = 40)
            val product6 = createProduct(minimumTermDays = 40, maximumTermDays = 46)
            val product7 = createProduct(minimumTermDays = 45, maximumTermDays = 50)
            createProduct(minimumTermDays = 50, maximumTermDays = 60)

            val result = goalProductService.checkTermIntegrity()

            result.isLeft() shouldBe true
            result.mapLeft { error ->
                error.shouldBeTypeOf<TermIntegrityError.OverlapingTerms>()
                error.productIds shouldContainExactlyInAnyOrder listOf(
                    product2,
                    product3,
                    product4,
                    product6,
                    product7,
                ).map { it.id }
            }
        }

        @Test
        fun `deve retornar erro quando existe intervalo aberto`() {
            createProduct(minimumTermDays = 0, maximumTermDays = 10)
            val product2 = createProduct(minimumTermDays = 10, maximumTermDays = 14)
            val product3 = createProduct(minimumTermDays = 15, maximumTermDays = 25)
            val product4 = createProduct(minimumTermDays = 27, maximumTermDays = 30)
            createProduct(minimumTermDays = 30, maximumTermDays = 40)
            val product6 = createProduct(minimumTermDays = 40, maximumTermDays = 46)
            val product7 = createProduct(minimumTermDays = 47, maximumTermDays = 50)
            createProduct(minimumTermDays = 50, maximumTermDays = 60)

            val result = goalProductService.checkTermIntegrity()

            result.isLeft() shouldBe true
            result.mapLeft { error ->
                error.shouldBeTypeOf<TermIntegrityError.GapingTerms>()
                error.productIds shouldContainExactly listOf(
                    product2,
                    product3,
                    product4,
                    product6,
                    product7,
                ).map { it.id }
            }
        }

        @Test
        fun `deve retornar ok quando nao existe intervalo aberto nem sobreposto`() {
            createProduct(minimumTermDays = 0, maximumTermDays = 10)
            createProduct(minimumTermDays = 10, maximumTermDays = 20)
            createProduct(minimumTermDays = 20, maximumTermDays = 21)
            createProduct(minimumTermDays = 21, maximumTermDays = 25)
            createProduct(minimumTermDays = 25, maximumTermDays = 40)
            createProduct(minimumTermDays = 40, maximumTermDays = 46)
            createProduct(minimumTermDays = 46, maximumTermDays = 50)
            createProduct(minimumTermDays = 50, maximumTermDays = 60)

            val result = goalProductService.checkTermIntegrity()

            result.isRight() shouldBe true
        }

        @Test
        fun `deve desconsiderar produto desabilitado`() {
            createProduct(minimumTermDays = 0, maximumTermDays = 10)
            createProduct(minimumTermDays = 15, maximumTermDays = 25, enabled = false)
            createProduct(minimumTermDays = 10, maximumTermDays = 20)
            createProduct(minimumTermDays = 30, maximumTermDays = 40, enabled = false)

            val result = goalProductService.checkTermIntegrity()

            result.isRight() shouldBe true
        }

        @Test
        fun `deve desconsiderar liquidez diaria`() {
            createProduct(minimumTermDays = 0, maximumTermDays = 10)
            createProduct(minimumTermDays = 15, maximumTermDays = 25, liquidity = GoalProductLiquidity.DAILY)
            createProduct(minimumTermDays = 10, maximumTermDays = 20)
            createProduct(minimumTermDays = 30, maximumTermDays = 40, liquidity = GoalProductLiquidity.DAILY)

            val result = goalProductService.checkTermIntegrity()

            result.isRight() shouldBe true
        }
    }

    @Nested
    @DisplayName("ao consultar as taxas de juros")
    inner class GetInterestRate {

        @Test
        fun `deve retornar a taxa de todos os indices em todas as escalas`() {
            val interestRatesList = listOf(
                cdiInterestRate,
                savingsInterestRate,
            )

            every {
                investmentManagerService.getIndexInterestRates()
            } returns interestRatesList.right()

            val result = goalProductService.getIndexInterestRates()

            result.isRight() shouldBe true
            result.map {
                it shouldContainExactlyInAnyOrder interestRatesList
            }
        }
    }

    private infix fun GoalProduct.shouldMatchSaveCommand(command: SaveGoalProductCommand) {
        index shouldBe command.index
        indexIncome.rate shouldBe command.indexIncome
        liquidity shouldBe command.liquidity
        minimumTermDays shouldBe command.minimumTermDays
        maximumTermDays shouldBe command.maximumTermDays
        asset shouldBe command.asset
        risk shouldBe command.risk
        issuer shouldBe command.issuer
        provider shouldBe command.provider
        enabled shouldBe command.enabled
    }

    fun createProduct(
        type: GoalProductType = GoalProductType.CDB,
        index: FixedIncomeIndex = FixedIncomeIndex.CDI,
        indexIncome: Int = 0,
        liquidity: GoalProductLiquidity = GoalProductLiquidity.MATURITY,
        minimumTermDays: Long? = null,
        maximumTermDays: Long? = null,
        asset: String = "asset",
        risk: GoalProductRisk = GoalProductRisk.LOW,
        issuer: String = "issuer",
        provider: String = "provider",
        enabled: Boolean = true,
    ) = goalProductService.create(
        SaveGoalProductCommand(
            type = type,
            index = index,
            indexIncome = indexIncome,
            liquidity = liquidity,
            minimumTermDays = minimumTermDays,
            maximumTermDays = maximumTermDays,
            asset = asset,
            risk = risk,
            issuer = issuer,
            provider = provider,
            enabled = enabled,
        ),
    ).getOrElse { throw it }

    companion object {

        private fun createSaveCommand(
            type: GoalProductType = GoalProductType.CDB,
            index: FixedIncomeIndex = FixedIncomeIndex.CDI,
            indexIncome: Int = 100,
            liquidity: GoalProductLiquidity = GoalProductLiquidity.MATURITY,
            minimumTermDays: Long? = null,
            asset: String = "CDB banco Arbi",
            risk: GoalProductRisk = GoalProductRisk.LOW,
            issuer: String = "Banco Arbi",
            provider: String = "Banco Arbi",
            enabled: Boolean = true,
        ) = SaveGoalProductCommand(
            type = type,
            index = index,
            indexIncome = indexIncome,
            liquidity = liquidity,
            minimumTermDays = minimumTermDays,
            maximumTermDays = minimumTermDays?.plus(1),
            asset = asset,
            risk = risk,
            issuer = issuer,
            provider = provider,
            enabled = enabled,
        )

        private val saveEnabledMonthlyCDICommand = createSaveCommand(
            minimumTermDays = 0,
            indexIncome = 10200,
        )
        private val saveDisabledMonthlyCDICommand = createSaveCommand(
            minimumTermDays = 360,
            indexIncome = 10500,
            enabled = false,
        )
        private val saveDailyCDICommand = createSaveCommand(
            liquidity = GoalProductLiquidity.DAILY,
        )
        private val saveSavingsCommand = createSaveCommand(
            type = GoalProductType.SAVINGS,
            index = FixedIncomeIndex.SAVINGS,
            indexIncome = 1,
            liquidity = GoalProductLiquidity.DAILY,
            asset = "asset",
            risk = GoalProductRisk.HIGH,
            issuer = "issuer",
            provider = "provider",
        )
        val dummyCommand = createSaveCommand()

        @JvmStatic
        fun validSaveCommands() = listOf(
            saveEnabledMonthlyCDICommand,
            saveDisabledMonthlyCDICommand,
            saveDailyCDICommand,
            saveSavingsCommand,
        )
    }
}