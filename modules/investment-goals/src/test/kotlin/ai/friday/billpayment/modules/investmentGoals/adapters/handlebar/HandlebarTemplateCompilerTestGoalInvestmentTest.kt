package ai.friday.billpayment.modules.investmentGoals.adapters.handlebar

import ai.friday.billpayment.adapters.pdf.FlyingSaucerPDFConverter
import ai.friday.billpayment.adapters.pdf.PDFConfiguration
import ai.friday.billpayment.adapters.s3.ReceiptConfiguration
import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPayer
import ai.friday.billpayment.app.integrations.Html2ImageConverter
import ai.friday.billpayment.app.notification.HandlebarsTempate2Html2Image
import ai.friday.billpayment.app.notification.HandlebarsTempate2Html2ImageCommand
import ai.friday.billpayment.app.payment.InvestmentReceiptData
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.utils.TemplateHelper
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.createEmailTemplatesConfiguration
import ai.friday.billpayment.createTemplatesHtmlToImageConfiguration
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.investmentGoals.app.payment.InvestmentReceiptHandlebarCompiler
import ai.friday.billpayment.modules.investmentGoals.app.payment.InvestmentReceiptImageBuilder
import ai.friday.billpayment.modules.investmentGoals.weeklyGoal
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.io.File
import java.io.FileOutputStream
import java.time.Duration
import java.time.LocalDate
import java.time.ZonedDateTime
import org.junit.jupiter.api.Test

class HandlebarTemplateCompilerTestGoalInvestmentTest {

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    private val html2ImageConverter: Html2ImageConverter = mockk {
        every {
            convert(any())
        } returns byteArrayOf()
    }

    private val templatesHtmlToImageConfiguration = createTemplatesHtmlToImageConfiguration(environment = ME_POUPE_ENV)

    private val investmentReceiptHandlebarCompiler = InvestmentReceiptHandlebarCompiler(
        emailTemplatesConfiguration = createEmailTemplatesConfiguration(),
    )

    private val handlebarsTempate2Html2Image = mockk<HandlebarsTempate2Html2Image> {
        every {
            renderTemplateImageAndCreatePublicLink(any())
        } returns "LINK_TO_IMAGE"
    }

    private val investmentReceiptImageBuilder = InvestmentReceiptImageBuilder(
        templatesHtmlToImageConfiguration,
        receiptConfiguration = ReceiptConfiguration(
            bucketRegion = "US-EAST-1",
            bucketName = "bucket-fake",
            imageResolution = 300,
            imageFormat = "png",
            shouldSaveReceiptFilesOnDisk = false,
            linkDuration = Duration.ofMinutes(10),
        ),
        handlebarsTempate2Html2Image = handlebarsTempate2Html2Image,
    )
    private val pdfConverter = FlyingSaucerPDFConverter(
        configuration = PDFConfiguration(
            fontFiles = listOf(
                "templates/fonts/poppins-medium.ttf",
                "templates/fonts/mulish.ttf",
                "templates/fonts/quicksand.ttf",
                "templates/fonts/quicksand-bold.ttf",
                "templates/fonts/mulish-bold.ttf",
            ),
            folderName = "target",
            dotsPerPoint = 35f,
            dotsPerPixel = 20,
        ),
    )

    @Test
    fun buildInvestmentReceiptTemplate() {
        val receiptData = InvestmentReceiptData(
            billId = BillId("Test"),
            walletId = WalletId("Test"),
            source = ActionSource.System,
            dateTime = ZonedDateTime.now(),

            totalAmount = 100,
            payer = BillPayer(document = "***********", name = "Eduard", alias = null),
            payerFinancialInstitution = FinancialInstitution(
                name = "teste",
                ispb = "teste",
                compe = 12345,
            ),
            payerBankAccount = InternalBankAccount(
                accountType = AccountType.CHECKING,
                bankNo = 0,
                routingNo = 0,
                accountNo = 0,
                accountDv = "",
                document = "",
                bankAccountMode = BankAccountMode.PHYSICAL,
            ),
            walletName = null,
            scheduledBy = null,
            transactionId = TransactionId("transactionId"),
            goalId = "GOAL_ID",
            assignor = "Minha meta 1",
            productProvider = "ARBI",
            productName = "CDB Banco ARBI",
            productIndex = "CDI",
            productIndexPercentage = 110,
            productInterestRateLabel = "110% CDI",
            positionId = "123",
            maturityDate = LocalDate.now().plusYears(1),
            investmentGroupLabel = "Meta",
            goalEndDate = null, // LocalDate.now().plusYears(1).minusDays(1),
        )
        val receiptHtml = investmentReceiptHandlebarCompiler.buildReceiptHtml(receiptData, wallet)
        val html = File("target/receipt/investment-receipt.html")
        html.parentFile.mkdirs()
        FileOutputStream(html).write(receiptHtml.value.toByteArray())
        val file = File("target/receipt/investment-receipt.pdf")
        file.parentFile.mkdirs()
        FileOutputStream(file).write(pdfConverter.convert(receiptHtml.value, false))
        val receiptMailHtml = investmentReceiptHandlebarCompiler.buildReceiptMailHtml(receiptData)
        val mailHtml = File("target/receipt/investment-receipt-mail.html")
        mailHtml.parentFile.mkdirs()
        FileOutputStream(mailHtml).write(receiptMailHtml.value.toByteArray())
    }

    @Test
    fun buildInstagrammableInvestmentReceipt() {
        val goal = weeklyGoal.copy(
            imageUrl = "https://notification-templates-cdn.mepoupe.app/static/education.png",
            name = "Curso de inglês",
        )

        investmentReceiptImageBuilder.buildInstagrammableInvestmentReceiptHtml(goal, getLocalDate().plusWeeks(20))

        val commandSlot = slot<HandlebarsTempate2Html2ImageCommand>()

        verify {
            handlebarsTempate2Html2Image.renderTemplateImageAndCreatePublicLink(capture(commandSlot))
        }
        val command = commandSlot.captured

        val path = "target/receipt/"
        val filename = "instagrammable-investment-receipt"
        createHTMLAndImage(path, filename, command)
    }

    @Test
    fun buildInstagrammableExtraInvestmentReceipt() {
        val goal = weeklyGoal.copy(
            imageUrl = "https://notification-templates-cdn.mepoupe.app/static/education.png",
            name = "Curso de inglês",
        )

        investmentReceiptImageBuilder.buildInstagrammableExtraInvestmentReceiptHtml(goal)

        val commandSlot = slot<HandlebarsTempate2Html2ImageCommand>()
        verify {
            handlebarsTempate2Html2Image.renderTemplateImageAndCreatePublicLink(capture(commandSlot))
        }
        val command = commandSlot.captured

        val path = "target/receipt/"
        val filename = "instagrammable-extra-investment-receipt"

        createHTMLAndImage(path, filename, command)
    }

    @Test
    fun buildGoalCompletedReceipt() {
        val goal = weeklyGoal.copy(
            imageUrl = "https://notification-templates-cdn.mepoupe.app/static/education.png",
            name = "Curso de inglês",
        )

        investmentReceiptImageBuilder.buildInstagrammableGoalCompletedReceiptHtml(goal)

        val commandSlot = slot<HandlebarsTempate2Html2ImageCommand>()
        verify {
            handlebarsTempate2Html2Image.renderTemplateImageAndCreatePublicLink(capture(commandSlot))
        }
        val command = commandSlot.captured

        val path = "target/receipt/"
        val filename = "instagrammable-goal-completed-receipt"

        createHTMLAndImage(path, filename, command)
    }

    @Test
    fun buildGoalEndDateReachedReceipt() {
        val goal = weeklyGoal.copy(
            imageUrl = "https://notification-templates-cdn.mepoupe.app/static/education.png",
            name = "Curso de inglês",
        )

        investmentReceiptImageBuilder.buildInstagrammableGoalEndDateReachedReceiptHtml(goal)

        val commandSlot = slot<HandlebarsTempate2Html2ImageCommand>()
        verify {
            handlebarsTempate2Html2Image.renderTemplateImageAndCreatePublicLink(capture(commandSlot))
        }
        val command = commandSlot.captured

        val path = "target/receipt/"
        val filename = "instagrammable-goal-end-date-reached-receipt"

        createHTMLAndImage(path, filename, command)
    }

    private fun createHTMLAndImage(
        path: String,
        filename: String,
        command: HandlebarsTempate2Html2ImageCommand,
    ) {
        val html = File("$path$filename.html")
        html.parentFile.mkdirs()
        val receiptHtml = TemplateHelper.applyTemplate(command.templatePath, command.templateData)
        FileOutputStream(html).write(receiptHtml.toByteArray())

        val imageBytes = html2ImageConverter.convert(CompiledHtml(receiptHtml))
        val image = File("$path$filename.jpg")
        FileOutputStream(image).write(imageBytes)
    }
}