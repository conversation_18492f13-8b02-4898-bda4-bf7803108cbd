package ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.modules.investmentGoals.app.goal.Goal
import ai.friday.billpayment.modules.investmentGoals.monthlyGoal
import ai.friday.billpayment.modules.investmentGoals.shouldBeGoal
import ai.friday.billpayment.modules.investmentGoals.shouldHaveGoal
import ai.friday.billpayment.modules.investmentGoals.weeklyGoal
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class GoalDbRepositoryTest {
    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val goalRepository = GoalDbRepository(client = GoalDynamoDAO(dynamoDbEnhancedClient))

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @ParameterizedTest
    @MethodSource("goals")
    fun `deve conseguir criar e buscar uma meta`(goal: Goal) {
        goalRepository.save(goal)

        goalRepository.findOrNull(goal.id) shouldBeGoal goal
    }

    @ParameterizedTest
    @MethodSource("goals")
    fun `deve atualizar uma meta existente`(goal: Goal) {
        goalRepository.save(goal)

        val updatedGoal = goal.copy(name = "updated goal name")

        goalRepository.save(updatedGoal)

        goalRepository.findOrNull(goal.id) shouldBeGoal updatedGoal
    }

    @Test
    fun `deve conseguir buscar todas as metas de um usuario`() {
        weeklyGoal.accountId shouldBe monthlyGoal.accountId

        goalRepository.save(weeklyGoal)
        goalRepository.save(monthlyGoal)

        val result = goalRepository.findByAccountId(weeklyGoal.accountId)

        result.size shouldBe 2
        result shouldHaveGoal weeklyGoal
        result shouldHaveGoal monthlyGoal
    }

    @Test
    fun `deve conseguir buscar todas as metas de uma carteira`() {
        weeklyGoal.walletId shouldBe monthlyGoal.walletId

        goalRepository.save(weeklyGoal)
        goalRepository.save(monthlyGoal)

        val result = goalRepository.findByWalletId(weeklyGoal.walletId)

        result.size shouldBe 2
        result shouldHaveGoal weeklyGoal
        result shouldHaveGoal monthlyGoal
    }

    @Test
    fun `deve conseguir buscar todas as metas de um produto`() {
        weeklyGoal.productId shouldNotBe monthlyGoal.productId

        goalRepository.save(weeklyGoal)
        goalRepository.save(monthlyGoal)

        val result = goalRepository.findByProductId(weeklyGoal.productId)

        result.size shouldBe 1
        result shouldHaveGoal weeklyGoal
    }

    @Nested
    @DisplayName("ao buscar metas perto de completarem")
    inner class FindGoalsCloseToCompletion {

        @Test
        fun `deve retornar metas proximas a expirarem por valor`() {
            goalRepository.save(weeklyGoal.copy(id = GoalId(), amount = 1000_00, lastKnownNetBalanceAmount = 990_00, endDate = getLocalDate().plusYears(1)))
            goalRepository.save(monthlyGoal.copy(id = GoalId(), amount = 1000_00, lastKnownNetBalanceAmount = 990_00, endDate = getLocalDate().plusYears(1)))
            goalRepository.save(monthlyGoal.copy(id = GoalId(), amount = 1000_00, lastKnownNetBalanceAmount = 500_00, endDate = getLocalDate().plusYears(1)))

            val result = goalRepository.findGoalsCloseToCompletion()
            result.size shouldBe 2
        }

        @Test
        fun `deve retornar metas proximas a expirarem por data`() {
            goalRepository.save(weeklyGoal.copy(id = GoalId(), amount = 1000_00, lastKnownNetBalanceAmount = 500_00, endDate = getLocalDate()))
            goalRepository.save(monthlyGoal.copy(id = GoalId(), amount = 1000_00, lastKnownNetBalanceAmount = 500_00, endDate = getLocalDate().minusMonths(1)))
            goalRepository.save(monthlyGoal.copy(id = GoalId(), amount = 1000_00, lastKnownNetBalanceAmount = 500_00, endDate = getLocalDate().minusMonths(1).minusDays(1)))
            goalRepository.save(monthlyGoal.copy(id = GoalId(), amount = 1000_00, lastKnownNetBalanceAmount = 500_00, endDate = getLocalDate().plusDays(1)))
            goalRepository.save(monthlyGoal.copy(id = GoalId(), amount = 1000_00, lastKnownNetBalanceAmount = 500_00, endDate = getLocalDate().plusYears(1)))

            val result = goalRepository.findGoalsCloseToCompletion()
            result.size shouldBe 2
        }
    }

    companion object {
        @JvmStatic
        fun goals() = listOf(weeklyGoal, monthlyGoal)
    }
}