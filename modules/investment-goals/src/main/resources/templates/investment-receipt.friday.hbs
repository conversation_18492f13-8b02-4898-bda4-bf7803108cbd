<!DOCTYPE html>
<html lang="pt-BR" style="box-sizing: border-box; cursor: default; line-height: 1.5; -moz-tab-size: 4; tab-size: 4; -webkit-tap-highlight-color: transparent; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; word-break: break-word;">
    <head>
        <title>Friday</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
        <meta charset="utf-8"/>
        <style>
          @page {
              size: 8.3cm {{pageHeight}}cm;
              margin: .1cm .1cm 0 .1cm;
          }
        </style>
    <style type="text/css">/* Document
 * ========================================================================== *//**
 * Add border box sizing in all browsers (opinionated).
 */*,
::before,
::after {
  box-sizing: border-box;
}/**
 * 1. Add text decoration inheritance in all browsers (opinionated).
 * 2. Add vertical alignment inheritance in all browsers (opinionated).
 */::before,
::after {
  text-decoration: inherit; /* 1 */
  vertical-align: inherit; /* 2 */
}/**
 * 1. Use the default cursor in all browsers (opinionated).
 * 2. Change the line height in all browsers (opinionated).
 * 3. Use a 4-space tab width in all browsers (opinionated).
 * 4. Remove the grey highlight on links in iOS (opinionated).
 * 5. Prevent adjustments of font size after orientation changes in
 *    IE on Windows Phone and in iOS.
 * 6. Breaks words to prevent overflow in all browsers (opinionated).
 */html {
  cursor: default; /* 1 */
  line-height: 1.5; /* 2 */
  -moz-tab-size: 4; /* 3 */
  tab-size: 4; /* 3 */
  -webkit-tap-highlight-color: transparent /* 4 */;
  -ms-text-size-adjust: 100%; /* 5 */
  -webkit-text-size-adjust: 100%; /* 5 */
  word-break: break-word; /* 6 */
}/* Sections
 * ========================================================================== *//**
 * Remove the margin in all browsers (opinionated).
 */body {
  margin: 0;
}/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Edge, Firefox, and Safari.
 */h1 {
  font-size: 2em;
  margin: 0.67em 0;
}/* Grouping content
 * ========================================================================== *//**
 * Remove the margin on nested lists in Chrome, Edge, IE, and Safari.
 */dl dl,
dl ol,
dl ul,
ol dl,
ul dl {
  margin: 0;
}/**
 * Remove the margin on nested lists in Edge 18- and IE.
 */ol ol,
ol ul,
ul ol,
ul ul {
  margin: 0;
}/**
 * 1. Add the correct sizing in Firefox.
 * 2. Show the overflow in Edge 18- and IE.
 */hr {
  height: 0; /* 1 */
  overflow: visible; /* 2 */
}/**
 * Add the correct display in IE.
 */main {
  display: block;
}/**
 * Remove the list style on navigation lists in all browsers (opinionated).
 */nav ol,
nav ul {
  list-style: none;
  padding: 0;
}/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */pre {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}/* Text-level semantics
 * ========================================================================== *//**
 * Add the correct text decoration in Edge 18-, IE, and Safari.
 */abbr[title] {
  text-decoration: underline;
  text-decoration: underline dotted;
}/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */b,
strong {
  font-weight: bolder;
}/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */code,
kbd,
samp {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}/**
 * Add the correct font size in all browsers.
 */small {
  font-size: 80%;
}/* Embedded content
 * ========================================================================== *//*
 * Change the alignment on media elements in all browsers (opinionated).
 */audio,
canvas,
iframe,
img,
svg,
video {
  vertical-align: middle;
}/**
 * Remove the border on iframes in all browsers (opinionated).
 */iframe {
  border-style: none;
}/**
 * Change the fill color to match the text color in all browsers (opinionated).
 */svg:not([fill]) {
  fill: currentColor;
}/**
 * Hide the overflow in IE.
 */svg:not(:root) {
  overflow: hidden;
}/* Tabular data
 * ========================================================================== *//**
 * Collapse border spacing in all browsers (opinionated).
 */table {
  border-collapse: collapse;
}/* Forms
 * ========================================================================== *//**
 * Remove the margin on controls in Safari.
 */button,
input,
select {
  margin: 0;
}/**
 * 1. Show the overflow in IE.
 * 2. Remove the inheritance of text transform in Edge 18-, Firefox, and IE.
 */button {
  overflow: visible; /* 1 */
  text-transform: none; /* 2 */
}/**
 * Correct the inability to style buttons in iOS and Safari.
 */button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}/**
 * 1. Change the inconsistent appearance in all browsers (opinionated).
 * 2. Correct the padding in Firefox.
 */fieldset {
  border: 1px solid #a0a0a0; /* 1 */
  padding: 0.35em 0.75em 0.625em; /* 2 */
}/**
 * Show the overflow in Edge 18- and IE.
 */input {
  overflow: visible;
}/**
 * 1. Correct the text wrapping in Edge 18- and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 */legend {
  color: inherit; /* 2 */
  display: table; /* 1 */
  max-width: 100%; /* 1 */
  white-space: normal; /* 1 */
}/**
 * 1. Add the correct display in Edge 18- and IE.
 * 2. Add the correct vertical alignment in Chrome, Edge, and Firefox.
 */progress {
  display: inline-block; /* 1 */
  vertical-align: baseline; /* 2 */
}/**
 * Remove the inheritance of text transform in Firefox.
 */select {
  text-transform: none;
}/**
 * 1. Remove the margin in Firefox and Safari.
 * 2. Remove the default vertical scrollbar in IE.
 * 3. Change the resize direction in all browsers (opinionated).
 */textarea {
  margin: 0; /* 1 */
  overflow: auto; /* 2 */
  resize: vertical; /* 3 */
}/**
 * 1. Correct the odd appearance in Chrome, Edge, and Safari.
 * 2. Correct the outline style in Safari.
 */[type="search"] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}/**
 * Correct the cursor style of increment and decrement buttons in Safari.
 */::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}/**
 * Correct the text style of placeholders in Chrome, Edge, and Safari.
 */::-webkit-input-placeholder {
  color: inherit;
  opacity: 0.54;
}/**
 * Remove the inner padding in Chrome, Edge, and Safari on macOS.
 */::-webkit-search-decoration {
  -webkit-appearance: none;
}/**
 * 1. Correct the inability to style upload buttons in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}/**
 * Remove the inner border and padding of focus outlines in Firefox.
 */::-moz-focus-inner {
  border-style: none;
  padding: 0;
}/**
 * Restore the focus outline styles unset by the previous rule in Firefox.
 */:-moz-focusring {
  outline: 1px dotted ButtonText;
}/**
 * Remove the additional :invalid styles in Firefox.
 */:-moz-ui-invalid {
  box-shadow: none;
}/* Interactive
 * ========================================================================== *//*
 * Add the correct display in Edge 18- and IE.
 */details {
  display: block;
}/*
 * Add the correct styles in Edge 18-, IE, and Safari.
 */dialog {
  background-color: white;
  border: solid;
  color: black;
  display: block;
  height: -moz-fit-content;
  height: -webkit-fit-content;
  height: fit-content;
  left: 0;
  margin: auto;
  padding: 1em;
  position: absolute;
  right: 0;
  width: -moz-fit-content;
  width: -webkit-fit-content;
  width: fit-content;
}dialog:not([open]) {
  display: none;
}/*
 * Add the correct display in all browsers.
 */summary {
  display: list-item;
}/* Scripting
 * ========================================================================== *//**
 * Add the correct display in IE.
 */template {
  display: none;
}/* User interaction
 * ========================================================================== *//*
 * 1. Remove the tapping delay in IE 10.
 * 2. Remove the tapping delay on clickable elements
      in all browsers (opinionated).
 */a,
area,
button,
input,
label,
select,
summary,
textarea,
[tabindex] {
  -ms-touch-action: manipulation; /* 1 */
  touch-action: manipulation; /* 2 */
}/* Accessibility
 * ========================================================================== *//**
 * Change the cursor on busy elements in all browsers (opinionated).
 */[aria-busy="true"] {
  cursor: progress;
}/*
 * Change the cursor on control elements in all browsers (opinionated).
 */[aria-controls] {
  cursor: pointer;
}/*
 * Change the cursor on disabled, not-editable, or otherwise
 * inoperable elements in all browsers (opinionated).
 */[aria-disabled="true"],
[disabled] {
  cursor: not-allowed;
}/*
 * Change the display on visually hidden accessible elements
 * in all browsers (opinionated).
 */[aria-hidden="false"][hidden] {
  display: initial;
}[aria-hidden="false"][hidden]:not(:focus) {
  clip: rect(0, 0, 0, 0);
  position: absolute;
}@import-sanitize;body{background-color:#f5f6f7;color:#444a4b}#base-layout{font-size:16px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Oxygen","Ubuntu","Cantarell","Fira Sans","Droid Sans","Helvetica Neue",sans-serif;-webkit-font-smoothing:antialiased;padding:30px 0;background-color:#f5f6f7}#base-layout>.main,#base-layout>.footer{max-width:600px;margin:0 auto}#base-layout>.main{background-color:#fff;border-radius:10px}#base-layout>.footer{padding:30px 40px;font-size:13px;text-align:center;color:#7a869a}#base-layout>.footer .links{margin-top:20px}#base-layout>.footer .links a+a{margin-left:12px}#base-layout>.footer p{text-align:center;max-width:300px;margin-left:auto;margin-right:auto}#base-layout h1,#base-layout h2,#base-layout h3,#base-layout h4,#base-layout li,#base-layout p{margin:0}#base-layout p{text-align:left;line-height:1.5;color:#444a4b}#base-layout p+p,#base-layout p+a,#base-layout p+div{margin-top:16px}#base-layout a{color:#444a4b}#base-layout .action{display:block;padding:20px;margin:30px 0;text-align:center;font-size:18px;text-decoration:none;font-weight:800;border-radius:10px;background-color:#ff5757;color:#fff;box-shadow:0 13px 10px -10px rgba(239,102,6,.5)}body{font-family:"Mulish",-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Oxygen","Ubuntu","Cantarell","Fira Sans","Droid Sans","Helvetica Neue",sans-serif;background:#fff;max-width:400px;letter-spacing:.42px;color:#444a4b}.header{padding:24px 16px 0 16px;text-align:justify}.header .top{margin-bottom:16px}.header .image{width:112px;height:32px}.header .column{float:right}.header .column .dateTime,.header .column .totalAmount{display:block}.header .column .totalAmount{font-size:14px;font-weight:700;line-height:20px;text-align:right}.header .column .dateTime{font-size:12px;font-weight:500;line-height:16px;text-align:right}.header .box{background-color:#fff4ef;height:80px;margin:0 -16px;font-family:"Quicksand"}.header .box p{padding:24px 16px;text-align:center;font-size:24px;font-weight:700;line-height:32px;color:#f55;text-align:left}.header .box p span{color:#475569}.footer{font-family:"Mulish",-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Oxygen","Ubuntu","Cantarell","Fira Sans","Droid Sans","Helvetica Neue",sans-serif;text-align:center;font-size:12px}.footer span{display:block;margin-top:10px}.logo{width:120px}.receipt-template{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.receipt-template .subtitle{font-family:"Quicksand";color:#475569;font-size:16px;font-weight:700;margin:16px 16px 0 16px;line-height:24px}.receipt-template .label{display:inline-block;font-size:14px;font-weight:500;color:#475569}.receipt-template .column-value,.receipt-template .value{display:inline-block;font-size:14px;font-weight:500;color:#64748b}.receipt-template .amount-container{margin:16px;position:relative}.receipt-template .amount-container .label,.receipt-template .amount-container .amount{display:inline-block;font-weight:700;font-size:20px;line-height:28px;color:#475569}.receipt-template .amount-container .label{font-family:"Quicksand";text-align:left}.receipt-template .amount-container .amount{position:absolute;bottom:0;right:0}.receipt-template .details .label{text-align:left}.receipt-template .details .value{text-align:right}.receipt-template .details .details-item{padding:8px 16px;border-bottom:1px solid #f1f5f9;position:relative}.receipt-template .details .details-item .value{position:absolute;bottom:0;right:0;margin-bottom:9px;margin-right:16px}.receipt-template .column-value{float:none;display:block;text-align:left}.receipt-template h1,.receipt-template h2,.receipt-template h3,.receipt-template h4,.receipt-template li,.receipt-template span,.receipt-template p{margin:0;padding:0}
</style></head>
    <body style="box-sizing: border-box; margin: 0; background-color: #f5f6f7; font-family: 'Mulish',-apple-system,BlinkMacSystemFont,'Segoe UI','Roboto','Oxygen','Ubuntu','Cantarell','Fira Sans','Droid Sans','Helvetica Neue',sans-serif; background: #fff; max-width: 400px; letter-spacing: .42px; color: #444a4b;">
        <div class="header" style="box-sizing: border-box; padding: 24px 16px 0 16px; text-align: justify;">
            <div class="top" style="box-sizing: border-box; margin-bottom: 16px;">
                <img class="image" src="data:image/png;base64,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" alt="Friday" style="box-sizing: border-box; vertical-align: middle; width: 112px; height: 32px;" width="112" height="32"/>
                <div class="column" style="box-sizing: border-box; float: right;">
                    <span class="totalAmount" style="box-sizing: border-box; display: block; font-size: 14px; font-weight: 700; line-height: 20px; text-align: right;">{{totalAmount}}</span>
                    <span class="dateTime" style="box-sizing: border-box; display: block; font-size: 12px; font-weight: 500; line-height: 16px; text-align: right;">{{dateTime}}</span>
                </div>
            </div>
            <div class="box" style="box-sizing: border-box; background-color: #fff4ef; height: 80px; margin: 0 -16px; font-family: 'Quicksand';">
                <p style="box-sizing: border-box; padding: 24px 16px; text-align: center; font-size: 24px; font-weight: 700; line-height: 32px; color: #f55; text-align: left;"><span style="box-sizing: border-box; color: #475569;">Investido</span> com Friday</p>
            </div>
        </div>
        <div class="receipt-template" style="box-sizing: border-box; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;">
            <div class="details" style="box-sizing: border-box;">
                <div class="amount-container" style="box-sizing: border-box; margin: 16px; position: relative;">
                    <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-weight: 700; font-size: 20px; line-height: 28px; color: #475569; font-family: 'Quicksand'; text-align: left;">Valor</span>
                    <span class="amount" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-weight: 700; font-size: 20px; line-height: 28px; color: #475569; position: absolute; bottom: 0; right: 0;">{{totalAmount}}</span>
                </div>
                <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
                    <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Investido em</span>
                    <span class="value" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #64748b; text-align: right; position: absolute; bottom: 0; right: 0; margin-bottom: 9px; margin-right: 16px;">{{dateTime}}</span>
                </div>
            {{#if groupName}}
                <h2 class="subtitle" style="box-sizing: border-box; padding: 0; font-family: 'Quicksand'; color: #475569; font-size: 16px; font-weight: 700; margin: 16px 16px 0 16px; line-height: 24px;">{{groupName}}</h2>
                <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
                    <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Nome</span>
                    <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{recipientName}}</span>
                </div>
            {{/if}}
                <h2 class="subtitle" style="box-sizing: border-box; padding: 0; font-family: 'Quicksand'; color: #475569; font-size: 16px; font-weight: 700; margin: 16px 16px 0 16px; line-height: 24px;">Produto</h2>
                <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
                    <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Nome</span>
                    <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{productName}}</span>
                </div>
                <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
                    <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Emissor</span>
                    <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{productProvider}}</span>
                </div>
                <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
                    <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Rentabilidade</span>
                    <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{productInterestRateLabel}}</span>
                </div>
                {{#if goalEndDate}}
                    <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
                        <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Vencimento da meta</span>
                        <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{goalEndDate}}</span>
                    </div>
                {{/if}}

                {{#unless goalEndDate}}
                    <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
                        <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Vencimento do investimento</span>
                        <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{maturityDate}}</span>
                    </div>
                {{/unless}}

                <h2 class="subtitle" style="box-sizing: border-box; padding: 0; font-family: 'Quicksand'; color: #475569; font-size: 16px; font-weight: 700; margin: 16px 16px 0 16px; line-height: 24px;">Informações do investidor</h2>
                <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
                    <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Nome</span>
                    <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{payerName}}</span>
                </div>
                <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
                    <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Documento</span>
                    <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{payerDocument}}</span>
                </div>

                <h2 class="subtitle" style="box-sizing: border-box; padding: 0; font-family: 'Quicksand'; color: #475569; font-size: 16px; font-weight: 700; margin: 16px 16px 0 16px; line-height: 24px;">Dados da conta de origem</h2>
                <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
                    <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Instituição</span>
                    <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{payerBankNumber}}</span>
                </div>
                <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
                    <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Agência</span>
                    <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{payerRoutingNumber}}</span>
                </div>
                <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
                    <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Conta</span>
                    <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{payerAccountNumber}}</span>
                </div>


                <h2 class="subtitle" style="box-sizing: border-box; padding: 0; font-family: 'Quicksand'; color: #475569; font-size: 16px; font-weight: 700; margin: 16px 16px 0 16px; line-height: 24px;">Dados da transação</h2>
                <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
                    <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">ID do investimento</span>
                    <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{positionId}}</span>
                </div>
            </div>
        </div>
        <div class="footer" style="box-sizing: border-box; font-family: 'Mulish',-apple-system,BlinkMacSystemFont,'Segoe UI','Roboto','Oxygen','Ubuntu','Cantarell','Fira Sans','Droid Sans','Helvetica Neue',sans-serif; text-align: center; font-size: 12px;">
            <span style="box-sizing: border-box; display: block; margin-top: 10px;">Este investimento foi feito por <a style="box-sizing: border-box; -ms-touch-action: manipulation; touch-action: manipulation;">https://friday.ai</a></span>
            <span style="box-sizing: border-box; display: block; margin-top: 10px;">CNPJ 34.701.685/0001-15</span>
            <span style="box-sizing: border-box; display: block; margin-top: 10px;">Através de: Banco Arbi - CNPJ 54.403.563/0001-50</span>
        </div>
    </body>
</html>

