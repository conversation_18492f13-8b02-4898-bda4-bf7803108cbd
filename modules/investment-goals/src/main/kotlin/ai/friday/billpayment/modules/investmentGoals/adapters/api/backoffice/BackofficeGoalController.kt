package ai.friday.billpayment.modules.investmentGoals.adapters.api.backoffice

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.Html2ImageConverter
import ai.friday.billpayment.app.integrations.SubscriptionRepository
import ai.friday.billpayment.app.journey.UserJourneyService
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb.GoalDbRepository
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalService
import ai.friday.billpayment.modules.investmentGoals.app.investment.ClosedAccountsWithInvestmentsReportService
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentRepository
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProduct
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductRepository
import ai.friday.billpayment.modules.investmentGoals.app.subscription.InvestmentDiscountService
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

data class TestLambdaTO(
    val html: String,
)

@Secured(Role.Code.BACKOFFICE)
@Controller("/backoffice/goal")
@InvestmentGoalsModule
class BackofficeGoalController(
    private val goalService: GoalService,
    private val goalRepository: GoalDbRepository,
    private val html2ImageConverter: Html2ImageConverter,
    private val userJourneyService: UserJourneyService,
    private val accountService: AccountService,
    private val accountRepository: AccountRepository,
    private val investmentDiscountService: InvestmentDiscountService,
    private val subscriptionRepository: SubscriptionRepository,
    private val closedAccountsWithInvestmentsReportService: ClosedAccountsWithInvestmentsReportService,
    private val subscriptionRefundService: SubscriptionRefundService,
    private val goalProductRepository: GoalProductRepository,
    private val goalInvestmentRepository: GoalInvestmentRepository,
) {

    @Get("/totalInvestedByProductEstimate")
    fun totalInvestedByProductEstimate(): HttpResponse<*> {
        val result = goalRepository.findAllGoals().groupBy { goal ->
            goal.productId
        }.map {
            val product = goalProductRepository.findOrNull(it.key)!!

            val totalInvested = it.value.sumOf { goal ->
                goal.lastKnownNetBalanceAmount
            }

            product.toFixedIncomeString() to totalInvested
        }.toMap()
        logger.info(append("response", result), "totalInvestedByProductEstimate")
        return HttpResponse.ok(result)
    }

    @Post("/{goalId}/optimizeInstallmentAmount")
    fun optimizeInstallmentAmount(
        @PathVariable goalId: String,
        @Body body: OptimizeInstallmentAmountTO,
    ): HttpResponse<*> {
        try {
            goalService.optimizeGoalInstallmentAmount(GoalId(goalId), body.shouldNotify, body.shouldForce)
            logger.info(append("goalId", goalId), "BackofficeGoalController#optimizeInstallmentAmount")
            return HttpResponse.noContent<Unit>()
        } catch (e: Exception) {
            logger.error(append("goalId", goalId), "BackofficeGoalController#optimizeInstallmentAmount", e)
            return HttpResponse.serverError(e.message)
        }
    }

    @Post("/refundPaidSubscriptionByAccountId")
    fun refundSubscriptionByAccountId(@Body body: RefundSubscriptionAccountIdTO): HttpResponse<*> {
        val response = subscriptionRefundService.refundAccountId(AccountId(body.accountId), body.dryRun, java.time.LocalDate.parse(body.dueDate, DateTimeFormatter.ISO_DATE))
        return response.fold(
            ifLeft = {
                when (it) {
                    is SubscriptionRefundError.BillNotFound -> HttpResponse.badRequest("BillNotFound")
                    is SubscriptionRefundError.GenericError -> HttpResponse.serverError<Unit>()
                    is SubscriptionRefundError.PaidMethodNotFound -> HttpResponse.badRequest("PaidMethodNotFound")
                    is SubscriptionRefundError.PaidStatementsNotFound -> HttpResponse.badRequest("PaidStatementsNotFound")
                    is SubscriptionRefundError.SubscriptionNotFound -> HttpResponse.badRequest("SubscriptionNotFound")
                }
            },
            ifRight = {
                HttpResponse.ok<Unit>()
            },
        )
    }

    /*
    @Put("/testLambda")
    fun testLambda(@Body body: TestLambdaTO): HttpResponse<*> {
        val configuration = DefaultHttpClientConfiguration()
        configuration.setReadTimeout(Duration.ofSeconds(60))
        configuration.setConnectTimeout(Duration.ofSeconds(60))

        val html = body.html

        val imageBytes = html2ImageConverter.convert(CompiledHtml(body.html))
        return HttpResponse.ok(imageBytes)
    }
     */

    /*
    @Post("/resetGoalInstallmentAmountAndUpdateGoalAmount")
    fun resetGoalInstallmentAmountAndUpdateGoalAmount(
        @Body body: ResetGoalInstallmentAmountAndUpdateGoalAmountTO,
    ): HttpResponse<*> {
        try {
            val markers = append("goalId", body.goalId)
            val response = goalService.resetGoalInstallmentAmountAndUpdateGoalAmount(GoalId(body.goalId), body.shouldNotify).map {
                logger.info(markers.andAppend("hasUpdatedAmount", it), "BackofficeGoalController#resetGoalInstallmentAmountAndUpdateGoalAmount")
                it
            }.getOrElse {
                logger.error(markers.andAppend("hasUpdatedAmount", false), "BackofficeGoalController#resetGoalInstallmentAmountAndUpdateGoalAmount", it)
                false
            }
            return HttpResponse.ok(body.goalId to response)
        } catch (e: Exception) {
            logger.error(append("hasUpdatedAmount", false), "BackofficeGoalController#resetGoalInstallmentAmountAndUpdateGoalAmount", e)
            return HttpResponse.serverError(e.message)
        }
    }
    */

    /*
    @Post("/fixUserPilot")
    fun fixUserPilot(@Body body: FixLimitTO): HttpResponse<*> {
        val logName = "BackofficeGoalController#fixUserPilot"
        val limit = body.limit ?: 0
        val goals = if (limit > 0) {
            goalRepository.findAllGoals().take(limit)
        } else {
            goalRepository.findAllGoals()
        }

        val grouped = goals.groupBy { it.accountId }

        grouped.forEach { (accountId, _) ->
            userJourneyService.trackEventAsync(accountId, UserJourneyEvent.GoalCreated)
        }

        logger.info(append("totalUsersAdded", grouped.size), logName)
        return HttpResponse.noContent<Unit>()
    }
     */

    /*
    @Post("fixInvestmentCampaign")
    fun fixInvestmentCampaign(@Body body: FixLimitTO): HttpResponse<*> {
        val logName = "BackofficeGoalController#fixInvestmentCampaign"
        val accounts = accountRepository.findAllAccountsActivatedSince(LocalDate.of(2025, 4, 1)).filter {
            it.subscriptionType == SubscriptionType.PIX && !it.hasGroup(AccountGroup.INVESTMENT_CAMPAIGN)
        }
        val groupsToAdd = listOf(AccountGroup.INVESTMENT_CAMPAIGN)

        val limit = body.limit ?: 0
        logger.info(append("totalAccounts", accounts.size), logName)

        accounts.take(limit).forEach { account ->
            val markers = append("accountId", account.accountId)
            try {
                accountService.addGroups(account.accountId, groupsToAdd)
                markers.andAppend("groupAdded", true)
                val updatedAccount = accountRepository.findById(account.accountId)

                val currentSubscription = subscriptionRepository.find(account.accountId)
                markers.andAppend("hasSubscription", currentSubscription != null)

                if (currentSubscription != null) {
                    val receivedDiscount = investmentDiscountService.checkAndApplyFirstInstallmentDiscount(updatedAccount, currentSubscription)
                    markers.andAppend("receivedDiscount", receivedDiscount)
                } else {
                    markers.andAppend("receivedDiscount", false)
                }
                logger.info(markers, logName)
            } catch (e: Exception) {
                logger.error(markers, logName, e)
            }
        }

        return HttpResponse.ok(accounts.take(limit).map { it.accountId.value })
    }
     */

    /*
    @Get("closedAccountsWithInvestments")
    fun closedAccountsWithInvestments(): HttpResponse<*> {
        closedAccountsWithInvestmentsReportService.check()
        return HttpResponse.ok<Unit>()
    }
    */

    /*
    @Post("refundPaidSubscription")
    fun refundSubscription(@Body body: RefundSubscriptionTO): HttpResponse<*> {
        val response = subscriptionRefundService.refundBy(body.dryRun)
        return HttpResponse.ok(response)
    }
    */

    /*
    @Get("/checkNotAppliedDiscounts")
    fun checkNotAppliedDiscounts(): HttpResponse<*> {
        val result = goalRepository.findAllGoals().mapNotNull { goal ->
            val validInvestment = goalInvestmentRepository.findByGoalId(goalId = goal.id).firstOrNull {
                val validPaymentDateOnCurrentWindow = it.paidAt?.toLocalDate()?.isAfter(getLocalDate().withDayOfMonth(9)) ?: false
                it.status == GoalInvestmentStatus.DONE && validPaymentDateOnCurrentWindow
            }
            if (validInvestment != null) {
                val received = investmentDiscountService.checkAndApplyInvestmentDiscount(validInvestment)
                if (received) {
                    logger.info(append("accountId", goal.accountId.value), "BackofficeGoalController#checkNotAppliedDiscounts")
                    goal.accountId
                } else {
                    null
                }
            } else {
                null
            }
        }
        return HttpResponse.ok(result.map { it.value })
    }
    */

    private fun GoalProduct.toFixedIncomeString(): String {
        return "${this.indexIncome.rate}% ${this.index.name}"
    }

    companion object {
        private val logger = LoggerFactory.getLogger(BackofficeGoalController::class.java)
    }
}

data class RefundSubscriptionTO(
    val dryRun: Boolean,
)

data class RefundSubscriptionAccountIdTO(
    val accountId: String,
    val dryRun: Boolean,
    val dueDate: String,
)

data class FixLimitTO(
    val limit: Int?,
)

data class OptimizeInstallmentAmountTO(
    val shouldNotify: Boolean,
    val shouldForce: Boolean,
)

data class ResetGoalInstallmentAmountAndUpdateGoalAmountTO(
    val goalId: String,
    val shouldNotify: Boolean,
)