package ai.friday.billpayment.modules.investmentGoals.adapters.messaging

import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModuleNoTest
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalService
import ai.friday.billpayment.modules.investmentGoals.app.messaging.OptimizeGoalInstallmentAmountHandlerMessage
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.context.annotation.Property
import io.micronaut.http.annotation.Trace
import io.micronaut.tracing.annotation.NewSpan
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@InvestmentGoalsModuleNoTest
open class OptimizeGoalInstallmentAmountHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val goalService: GoalService,
    @Property(name = "sqs.optimizeGoalInstallmentAmount") val optimizeGoalInstallmentAmountQueueName: String,
) : AbstractSQSHandler(amazonSQS, configuration, optimizeGoalInstallmentAmountQueueName, consumers = 2) {
    private val logger = LoggerFactory.getLogger(OptimizeGoalInstallmentAmountHandler::class.java)
    private val logName = "OptimizeGoalInstallmentAmountHandler"

    @Trace
    @NewSpan
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val messageBody = parseObjectFrom<OptimizeGoalInstallmentAmountHandlerMessage>(message.body())

        val markers = Markers.append("goalId", messageBody.goalId)

        val shouldDeleteMessage = goalService.optimizeGoalInstallmentAmount(GoalId(messageBody.goalId)).map { result ->
            logger.info(markers.andAppend("optimized", result), logName)
            true
        }.getOrElse {
            logger.error(markers, logName, it)
            false
        }

        return SQSHandlerResponse(shouldDeleteMessage = shouldDeleteMessage)
    }

    override fun handleError(message: Message, e: Exception): SQSHandlerResponse {
        logger.error(logName, e)
        return SQSHandlerResponse(false)
    }
}