package ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.AbstractBillPaymentDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndex
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProduct
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductId
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductLiquidity
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductRepository
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductRisk
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val ENTITY_ID = "GOAL_PRODUCT"

@InvestmentGoalsModule
class GoalProductDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<GoalProductEntity>(cli, GoalProductEntity::class.java)

@InvestmentGoalsModule
class GoalProductDbRepository(
    private val client: GoalProductDynamoDAO,
) : GoalProductRepository {
    override fun save(product: GoalProduct): GoalProduct {
        val entity =
            GoalProductEntity().apply {
                partitionKey = ENTITY_ID
                sortKey = product.id.value
                type = product.type
                index = product.index
                indexIncome = product.indexIncome.rate
                liquidity = product.liquidity
                minimumTermDays = product.minimumTermDays
                maximumTermDays = product.maximumTermDays
                asset = product.asset
                risk = product.risk
                issuer = product.issuer
                provider = product.provider
                enabled = product.enabled
                createdAt = product.createdAt.format(dateTimeFormat)
                updatedAt = getZonedDateTime().format(dateTimeFormat)
            }
        client.save(entity)
        return product
    }

    override fun findAll(): List<GoalProduct> =
        client.findByPartitionKey(ENTITY_ID).map { it.toGoalProduct() }

    override fun findOrNull(id: GoalProductId): GoalProduct? =
        client.findByPrimaryKey(ENTITY_ID, id.value)?.toGoalProduct()
}

@DynamoDbBean
class GoalProductEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String

    @get:DynamoDbAttribute(value = "Type")
    lateinit var type: GoalProductType

    @get:DynamoDbAttribute(value = "Index")
    lateinit var index: FixedIncomeIndex

    @get:DynamoDbAttribute(value = "IndexIncome")
    var indexIncome: Int = 0

    @get:DynamoDbAttribute(value = "Liquidity")
    lateinit var liquidity: GoalProductLiquidity

    @get:DynamoDbAttribute(value = "MinimumTermDays")
    var minimumTermDays: Long? = null

    @get:DynamoDbAttribute(value = "MaximumTermDays")
    var maximumTermDays: Long? = null

    @get:DynamoDbAttribute(value = "Asset")
    lateinit var asset: String

    @get:DynamoDbAttribute(value = "Risk")
    lateinit var risk: GoalProductRisk

    @get:DynamoDbAttribute(value = "Issuer")
    lateinit var issuer: String

    @get:DynamoDbAttribute(value = "Provider")
    lateinit var provider: String

    @get:DynamoDbAttribute(value = "Enabled")
    var enabled: Boolean = false

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute(value = "UpdatedAt")
    lateinit var updatedAt: String
}

private fun GoalProductEntity.toGoalProduct() = GoalProduct(
    id = GoalProductId(value = sortKey),
    type = type,
    index = index,
    indexIncome = FixedIncomeIndexRate(indexIncome),
    liquidity = liquidity,
    minimumTermDays = minimumTermDays,
    maximumTermDays = maximumTermDays,
    asset = asset,
    risk = risk,
    issuer = issuer,
    provider = provider,
    enabled = enabled,
    createdAt = ZonedDateTime.parse(createdAt, dateTimeFormat),
    updatedAt = ZonedDateTime.parse(updatedAt, dateTimeFormat),
)