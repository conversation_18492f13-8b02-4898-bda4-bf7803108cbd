package ai.friday.billpayment.modules.investmentGoals.app.payment.receipt.builder

import ai.friday.billpayment.app.FinancialInstitutionGlobalData
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillPayer
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.payment.InvestmentReceiptData
import ai.friday.billpayment.app.payment.receipt.builder.ReceiptDataBuilder
import ai.friday.billpayment.app.payment.receipt.builder.getScheduler
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.simpleName
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalRepository
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndex
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeProduct
import ai.friday.billpayment.modules.investmentGoals.app.payment.GoalInvestmentSettlementOperation
import ai.friday.morning.date.brazilTimeZone
import java.time.Instant
import java.time.ZonedDateTime

@InvestmentGoalsModule
class InvestmentReceiptDataBuilder(
    private val accountRepository: AccountRepository,
    private val transactionRepository: TransactionRepository,
    private val goalRepository: GoalRepository,
) : ReceiptDataBuilder {
    override val buildFor: List<BillType> = listOf(BillType.INVESTMENT)

    override fun build(bill: Bill, billPaid: BillPaid, wallet: Wallet): InvestmentReceiptData {
        val transaction = transactionRepository.findById(billPaid.transactionId!!)
        val goalInvestmentSettlementOperation = transaction.settlementData.getOperation<GoalInvestmentSettlementOperation>()
        val goalId = bill.goalId!!

        val goal = goalRepository.find(goalId).getOrNull()

        val product = goalInvestmentSettlementOperation.product!!

        val paymentMethods = accountRepository.findAccountPaymentMethodsByAccountIdAndStatus(
            wallet.founder.accountId,
            AccountPaymentMethodStatus.ACTIVE,
        )
        val bankAccount = paymentMethods.single {
            transaction.paymentData.toSingle().accountPaymentMethod.id == it.id
        }.method as InternalBankAccount
        val account = accountRepository.findById(wallet.founder.accountId)

        val payerFinancialInstitution =
            FinancialInstitutionGlobalData.pixParticipants.first { it.compe == bankAccount.bankNo }

        val scheduler = wallet.getScheduler(bill)

        return InvestmentReceiptData(
            billId = bill.billId,
            walletId = wallet.id,
            source = bill.source,
            dateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(bill.paidDate!!), brazilTimeZone),
            totalAmount = bill.amountTotal,
            walletName = wallet.name,
            scheduledBy = scheduler.simpleName(),
            transactionId = transaction.id,
            payer = BillPayer(document = account.document, name = account.name),
            payerFinancialInstitution = payerFinancialInstitution,
            payerBankAccount = bankAccount,
            goalId = goalId.value,
            assignor = bill.assignor!!,
            productProvider = product.provider.name,
            productName = product.name,
            productIndex = product.index.name,
            maturityDate = goalInvestmentSettlementOperation.maturityDate!!,
            productIndexPercentage = product.indexPercentage.rate,
            positionId = goalInvestmentSettlementOperation.positionId!!.value,
            productInterestRateLabel = buildProductInterestRateLabel(product),
            investmentGroupLabel = "Meta",
            goalEndDate = goal?.endDate,
        )
    }
}

fun buildProductInterestRateLabel(product: FixedIncomeProduct): String {
    return when (product.index) {
        FixedIncomeIndex.CDI -> "${product.indexPercentage.rate}% CDI"
        FixedIncomeIndex.SAVINGS -> "Poupança"
    }
}