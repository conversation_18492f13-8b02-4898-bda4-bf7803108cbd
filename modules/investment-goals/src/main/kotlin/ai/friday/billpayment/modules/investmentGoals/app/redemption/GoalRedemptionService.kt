package ai.friday.billpayment.modules.investmentGoals.app.redemption

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.cashIn.StartCashInMessage
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.payment.NoPaymentData
import ai.friday.billpayment.app.payment.Payer
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.toPayer
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.adapters.instrumentation.GoalInstrumentationRepository
import ai.friday.billpayment.modules.investmentGoals.app.goal.Goal
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalNotFoundException
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalService
import ai.friday.billpayment.modules.investmentGoals.app.goal.instrumentation.InvestmentRedemptionInstrumentationEvent
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeRedemptionQuotations
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerService
import ai.friday.billpayment.modules.investmentGoals.app.payment.GoalRedemptionSettlementOperation
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Property
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@InvestmentGoalsModule
class GoalRedemptionService(
    private val goalRedemptionRepository: GoalRedemptionRepository,
    private val transactionService: TransactionService,
    private val accountRepository: AccountRepository,
    private val goalService: GoalService,
    private val investmentManagerService: InvestmentManagerService,
    private val messagePublisher: MessagePublisher,
    private val goalInstrumentationRepository: GoalInstrumentationRepository,
    @Property(name = "sqs.startCashIn") val startCashInQueueName: String,
) {
    private val logger = LoggerFactory.getLogger(GoalRedemptionService::class.java)

    fun availableToRedeem(accountId: AccountId, goalId: GoalId): Either<Exception, Long> {
        val logName = "GoalRedemptionService#availableToRedeem"
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("goalId", goalId.value)

        val (_, penalty) = findGoalAndPenalty(accountId, goalId).getOrElse {
            logger.error(markers, logName, it)
            return it.left()
        }
        markers.andAppend("penalty", penalty)

        return investmentManagerService.userGoalPositionAvailableToRedeem(
            accountId = accountId,
            goalId = goalId,
            penalty = penalty,
        ).map {
            markers.andAppend("netValue", it.netValue)
            logger.info(markers, logName)
            it.netValue.right()
        }.getOrElse {
            logger.error(markers, logName, it)
            it.left()
        }
    }

    fun quoteRedemption(accountId: AccountId, goalId: GoalId, amount: Long): Either<Exception, FixedIncomeRedemptionQuotations> {
        val logName = "GoalRedemptionService#quoteRedemption"
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("goalId", goalId.value)
            .andAppend("amount", amount)

        val (_, penaltyRate) = findGoalAndPenalty(accountId, goalId).getOrElse {
            logger.error(markers, logName, it)
            return it.left()
        }
        markers.andAppend("penaltyRate", penaltyRate)

        return investmentManagerService.quoteRedemption(
            accountId = accountId,
            goalId = goalId,
            amount = amount,
            penaltyRate = penaltyRate,
        ).map {
            logger.info(markers.andAppend("quotations", it), logName)
            it.right()
        }.getOrElse {
            logger.error(markers, logName, it)
            it.left()
        }
    }

    fun requestRedemption(command: GoalRedemptionCommand): Either<Exception, TransactionId> {
        val logName = "GoalRedemptionService#requestRedemption"
        val markers = Markers.append("command", command)

        return try {
            val (goal, penalty) = findGoalAndPenalty(command.actionSource.accountId, command.goalId).getOrElse {
                logger.error(markers, logName, it)
                return it.left()
            }
            markers.andAppend("penalty", penalty)

            val account = accountRepository.findById(goal.accountId)

            val goalRedemption = command.toGoalRedemption(goal.walletId, penalty)
            markers.andAppend("goalRedemption", goalRedemption)

            goalRedemptionRepository.save(goalRedemption)

            val transaction = goalRedemption.toTransaction(account.toPayer())
            transactionService.save(transaction)
            markers.andAppend("transactionId", transaction.id.value)

            messagePublisher.sendMessage(
                queueName = startCashInQueueName,
                body = StartCashInMessage(
                    transactionId = transaction.id.value,
                ),
            )
            goalInstrumentationRepository.publishEvent(
                InvestmentRedemptionInstrumentationEvent(
                    accountId = goal.accountId,
                    redemption = goalRedemption,
                ),
            )

            logger.info(markers, logName)
            transaction.id.right()
        } catch (e: Exception) {
            logger.info(markers, logName, e)
            e.left()
        }
    }

    private fun GoalRedemptionCommand.toGoalRedemption(walletId: WalletId, penalty: FixedIncomeIndexRate?) = GoalRedemption(
        goalId = goalId,
        walletId = walletId,
        netAmount = netAmount,
        penaltyRate = penalty,
        actionSource = actionSource,
    )

    private fun GoalRedemption.toTransaction(payer: Payer) = Transaction(
        created = getZonedDateTime(),
        type = TransactionType.GOAL_REDEMPTION,
        payer = payer,
        actionSource = actionSource,
        nsu = 0L,
        paymentData = NoPaymentData,
        settlementData = SettlementData(
            settlementTarget = this,
            serviceAmountTax = 0,
            totalAmount = netAmount,
            settlementOperation = GoalRedemptionSettlementOperation(
                amount = netAmount,
            ),
        ),
        walletId = walletId,
        status = TransactionStatus.PROCESSING,
    )

    private fun findGoalAndPenalty(accountId: AccountId?, goalId: GoalId): Either<Exception, Pair<Goal, FixedIncomeIndexRate?>> {
        if (accountId == null) {
            return GoalNotFoundException(goalId).left()
        }
        val goal = goalService.findGoal(goalId, accountId).getOrElse {
            return it.left()
        }

        return goalService.calculateCurrentPenaltyRate(goal).map {
            Pair(goal, it)
        }
    }
}

data class GoalRedemptionCommand(
    val goalId: GoalId,
    val netAmount: Long,
    val actionSource: ActionSource.WalletActionSource,
)