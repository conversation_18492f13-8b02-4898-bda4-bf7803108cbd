package ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.AbstractBillPaymentDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.GlobalSecondaryIndexNames
import ai.friday.billpayment.adapters.dynamodb.INDEX_1
import ai.friday.billpayment.adapters.dynamodb.INDEX_2
import ai.friday.billpayment.adapters.dynamodb.INDEX_3
import ai.friday.billpayment.adapters.dynamodb.INDEX_4
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.isBetween
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryId
import ai.friday.billpayment.modules.investmentGoals.app.goal.Goal
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalInstallments
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalNotFoundException
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalRepository
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalStatus
import ai.friday.billpayment.modules.investmentGoals.app.goal.InstallmentFrequency
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductId
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductLiquidity
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import java.time.LocalDate
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val ENTITY_ID = "GOAL"

@InvestmentGoalsModule
class GoalDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<GoalEntity>(cli, GoalEntity::class.java)

@InvestmentGoalsModule
class GoalDbRepository(
    private val client: GoalDynamoDAO,
) : GoalRepository {
    override fun save(goal: Goal): Goal {
        val entity =
            GoalEntity().apply {
                partitionKey = ENTITY_ID
                sortKey = goal.id.value
                gSIndex1PartitionKey = goal.accountId.value
                gSIndex1SortKey = ENTITY_ID
                gSIndex2PartitionKey = goal.walletId.value
                gSIndex2SortKey = ENTITY_ID
                gSIndex3PartitionKey = goal.productId.value
                gSIndex3SortKey = ENTITY_ID
                gSIndex4PartitionKey = goal.id.value
                gSIndex4SortKey = ENTITY_ID
                accountId = goal.accountId.value
                walletId = goal.walletId.value
                categoryId = goal.categoryId.value
                name = goal.name
                imageUrl = goal.imageUrl
                endDate = goal.endDate.format(dateFormat)
                amount = goal.amount
                installments = goal.totalInstallments
                installmentFrequency = goal.installments.frequency
                installmentInitialAmount = goal.installmentInitialAmount
                liquidity = goal.liquidity
                installmentExpirationDay = goal.installments.expirationDay
                productId = goal.productId.value
                createdAt = goal.createdAt.format(dateTimeFormat)
                updatedAt = getZonedDateTime().format(dateTimeFormat)
                lastInstallmentAmountOptimizedAt = goal.lastValueOptimizationAt?.format(dateTimeFormat)
                installmentOptimizedAmount = goal.installmentOptimizedAmount
                status = goal.status
                lastKnownNetBalanceAmount = goal.lastKnownNetBalanceAmount
                lastKnownNetBalanceAt = goal.lastKnownNetBalanceAt.format(dateTimeFormat)
                lastKnownNetBalancePerc = goal.lastKnownNetBalancePerc.toDouble()
            }
        client.save(entity)

        return entity.toGoal()
    }

    override fun findByAccountId(accountId: AccountId): List<Goal> =
        client.findByPartitionKeyAndScanKeyOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            accountId.value,
            ENTITY_ID,
        ).map { it.toGoal() }

    override fun findByWalletId(walletId: WalletId): List<Goal> =
        client.findByPartitionKeyAndScanKeyOnIndex(
            GlobalSecondaryIndexNames.GSIndex2,
            walletId.value,
            ENTITY_ID,
        ).map { it.toGoal() }

    override fun findByProductId(productId: GoalProductId): List<Goal> =
        client.findByPartitionKeyAndScanKeyOnIndex(
            GlobalSecondaryIndexNames.GSIndex3,
            productId.value,
            ENTITY_ID,
        ).map { it.toGoal() }

    // FIXME estranho usar o índice 4 aqui. Imagino que seja por conta da primary com hash fixo.
    override fun findOrNull(goalId: GoalId): Goal? =
        client.findByPartitionKeyAndScanKeyOnIndex(
            GlobalSecondaryIndexNames.GSIndex4,
            goalId.value,
            ENTITY_ID,
        ).firstOrNull()?.toGoal()

    override fun find(goalId: GoalId): Either<GoalNotFoundException, Goal> {
        return findOrNull(goalId)?.right() ?: GoalNotFoundException(goalId).left()
    }

    override fun findGoalsToOptimize(optimizedSince: LocalDate): List<Goal> =
        client.findByPartitionKey(ENTITY_ID)
            .filter { it.status == GoalStatus.ACTIVE }
            .filter { LocalDate.parse(it.endDate, dateFormat) > getLocalDate() && (it.lastInstallmentAmountOptimizedAt == null || ZonedDateTime.parse(it.lastInstallmentAmountOptimizedAt, dateTimeFormat).toLocalDate() <= optimizedSince) }
            .map { it.toGoal() }

    override fun updateLastKnowNetBalance(goalId: GoalId, netAmount: Long): Either<GoalNotFoundException, Goal> {
        val goal = find(goalId).getOrElse { return it.left() }

        return save(
            goal.copy(
                lastKnownNetBalanceAmount = netAmount,
                lastKnownNetBalanceAt = getZonedDateTime(),
            ),
        ).right()
    }

    fun findAllGoals(): List<Goal> {
        return client.findByPartitionKey(ENTITY_ID).map { it.toGoal() }
    }

    override fun findGoalsCloseToCompletion(): List<Goal> =
        client.findByPartitionKey(ENTITY_ID)
            .filter {
                when (it.status) {
                    GoalStatus.ACTIVE,
                    GoalStatus.PAUSED,
                    -> true

                    GoalStatus.REMOVED,
                    GoalStatus.COMPLETED,
                    -> false
                }
            }
            .filter { it.lastKnownNetBalancePerc >= 99.0 || LocalDate.parse(it.endDate, dateFormat).isBetween(getLocalDate().minusMonths(1), getLocalDate()) }
            .map { it.toGoal() }
}

@DynamoDbBean
class GoalEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1SortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    lateinit var gSIndex2PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    lateinit var gSIndex2SortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_3])
    @get:DynamoDbAttribute(value = "GSIndex3PrimaryKey")
    lateinit var gSIndex3PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_3])
    @get:DynamoDbAttribute(value = "GSIndex3ScanKey")
    lateinit var gSIndex3SortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_4])
    @get:DynamoDbAttribute(value = "GSIndex4PrimaryKey")
    lateinit var gSIndex4PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_4])
    @get:DynamoDbAttribute(value = "GSIndex4ScanKey")
    lateinit var gSIndex4SortKey: String

    @get:DynamoDbAttribute(value = "AccountId")
    lateinit var accountId: String

    @get:DynamoDbAttribute(value = "WalletId")
    lateinit var walletId: String

    @get:DynamoDbAttribute(value = "CategoryId")
    lateinit var categoryId: String

    @get:DynamoDbAttribute(value = "Name")
    lateinit var name: String

    @get:DynamoDbAttribute(value = "ImageURL")
    lateinit var imageUrl: String

    @get:DynamoDbAttribute(value = "EndDate")
    lateinit var endDate: String

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute(value = "UpdatedAt")
    lateinit var updatedAt: String

    @get:DynamoDbAttribute(value = "Amount")
    var amount: Long = 0

    @get:DynamoDbAttribute(value = "Installments")
    var installments: Int = 0

    @get:DynamoDbAttribute(value = "Liquidity")
    lateinit var liquidity: GoalProductLiquidity

    @get:DynamoDbAttribute(value = "InstallmentFrequency")
    lateinit var installmentFrequency: InstallmentFrequency

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: GoalStatus

    @get:DynamoDbAttribute(value = "InstallmentAmount")
    var installmentInitialAmount: Long = 0

    @get:DynamoDbAttribute(value = "InstallmentExpirationDay")
    lateinit var installmentExpirationDay: String

    @get:DynamoDbAttribute(value = "ProductId")
    lateinit var productId: String

    @get:DynamoDbAttribute(value = "LastInstallmentAmountOptimizedAt")
    var lastInstallmentAmountOptimizedAt: String? = null

    @get:DynamoDbAttribute(value = "InstallmentOptimizedAmount")
    var installmentOptimizedAmount: Long? = null

    @get:DynamoDbAttribute(value = "LastKnownNetBalanceAmount")
    var lastKnownNetBalanceAmount: Long? = null

    @get:DynamoDbAttribute(value = "LastKnownNetBalanceAt")
    var lastKnownNetBalanceAt: String? = null

    @get:DynamoDbAttribute(value = "LastKnownNetBalancePerc")
    var lastKnownNetBalancePerc: Double = 0.0
}

private fun GoalEntity.toGoal(): Goal =
    Goal(
        id = GoalId(sortKey),
        name = this.name,
        imageUrl = this.imageUrl,
        accountId = AccountId(this.accountId),
        categoryId = GoalCategoryId(this.categoryId),
        endDate = LocalDate.parse(this.endDate, dateFormat),
        updatedAt = ZonedDateTime.parse(this.updatedAt, dateTimeFormat),
        createdAt = ZonedDateTime.parse(this.createdAt, dateTimeFormat),
        walletId = WalletId(this.walletId),
        amount = this.amount,
        liquidity = this.liquidity,
        installments = GoalInstallments.buildGoalInstallment(
            frequency = installmentFrequency,
            amount = installmentOptimizedAmount ?: installmentInitialAmount,
            expirationDay = installmentExpirationDay,
        ),
        productId = GoalProductId(productId),
        installmentInitialAmount = installmentInitialAmount,
        installmentOptimizedAmount = installmentOptimizedAmount,
        lastValueOptimizationAt = lastInstallmentAmountOptimizedAt?.let { ZonedDateTime.parse(it, dateTimeFormat) },
        status = status,

        lastKnownNetBalanceAmount = lastKnownNetBalanceAmount ?: 0,
        lastKnownNetBalanceAt = lastKnownNetBalanceAt?.let { ZonedDateTime.parse(it, dateTimeFormat) } ?: ZonedDateTime.parse(this.createdAt, dateTimeFormat),
    )