package ai.friday.billpayment.modules.investmentGoals.app

import ai.friday.billpayment.app.bill.INVESTMENT_MAX_VALUE
import ai.friday.billpayment.app.bill.INVESTMENT_MIN_VALUE

fun Long.roundToAllowedInvestmentAmount(): Long {
    val remainder = this % 100
    val rounded = if (remainder == 0L) {
        this
    } else {
        this + 100 - remainder
    }

    return if (rounded < INVESTMENT_MIN_VALUE) {
        INVESTMENT_MIN_VALUE
    } else if (rounded > INVESTMENT_MAX_VALUE) {
        INVESTMENT_MAX_VALUE
    } else {
        rounded
    }
}