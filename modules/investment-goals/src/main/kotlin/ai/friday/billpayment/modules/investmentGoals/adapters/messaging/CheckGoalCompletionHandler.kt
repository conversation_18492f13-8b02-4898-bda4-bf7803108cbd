package ai.friday.billpayment.modules.investmentGoals.adapters.messaging

import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModuleNoTest
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalStatusService
import ai.friday.billpayment.modules.investmentGoals.app.messaging.CheckGoalCompletionHandlerMessage
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.context.annotation.Property
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@InvestmentGoalsModuleNoTest
open class CheckGoalCompletionHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val goalStatusService: GoalStatusService,
    @Property(name = "sqs.checkGoalCompletion") val checkGoalCompletionQueueName: String,
) : AbstractSQSHandler(amazonSQS, configuration, checkGoalCompletionQueueName) {
    private val logger = LoggerFactory.getLogger(CheckGoalCompletionHandler::class.java)
    private val logName = "CheckGoalCompletionHandler"

    override fun handleMessage(message: Message): SQSHandlerResponse {
        val messageBody = parseObjectFrom<CheckGoalCompletionHandlerMessage>(message.body())

        val markers = Markers.append("goalId", messageBody.goalId)

        val shouldDeleteMessage = if (!messageBody.createdFromRedemption) {
            goalStatusService.checkAndUpdateGoalToCompleted(GoalId(messageBody.goalId)).map { result ->
                logger.info(markers.andAppend("hasCompleted", result), logName)
                true
            }.getOrElse {
                logger.error(markers.andAppend("errorMessage", it), logName)
                false
            }
        } else {
            goalStatusService.checkAndUpdateGoalToActiveFromPaused(GoalId(messageBody.goalId)).map { result ->
                logger.info(markers.andAppend("hasReactivated", result), logName)
                true
            }.getOrElse {
                logger.error(markers.andAppend("errorMessage", it), logName)
                false
            }
        }

        return SQSHandlerResponse(shouldDeleteMessage = shouldDeleteMessage)
    }

    override fun handleError(message: Message, e: Exception): SQSHandlerResponse {
        logger.error(logName, e)
        return SQSHandlerResponse(false)
    }
}