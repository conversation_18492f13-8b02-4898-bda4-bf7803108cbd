package ai.friday.billpayment.modules.investmentGoals.adapters.api.category

import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.log
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategory
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryService
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryType
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.security.annotation.Secured
import org.slf4j.LoggerFactory

@Secured(Role.Code.OWNER)
@Controller("/goal/category")
@Version("2")
@InvestmentGoalsModule
class GoalCategoryController(
    private val goalCategoryService: GoalCategoryService,
) {
    @Get
    fun getAll(): HttpResponse<*> {
        val logName = "GoalCategoryController#getAll"
        return try {
            val categories =
                goalCategoryService.findAllEnabled().map { it.toGoalCategoryResponseTO() }

            HttpResponse.ok(categories).also {
                logger.info(log("categories" to categories), logName)
            }
        } catch (ex: Exception) {
            logger.error(logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(GoalCategoryController::class.java)
    }
}

data class GoalCategoryResponseTO(
    val id: String,
    val type: GoalCategoryType,
    val name: String,
    val imageUrl: String,
)

private fun GoalCategory.toGoalCategoryResponseTO(): GoalCategoryResponseTO =
    GoalCategoryResponseTO(
        id = id.value,
        type = type,
        name = name,
        imageUrl = imageUrl,
    )