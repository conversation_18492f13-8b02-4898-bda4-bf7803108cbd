package ai.friday.billpayment.modules.investmentGoals.app.account

import ai.friday.billpayment.app.account.AccountClosureDetails
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.CloseAccountError
import ai.friday.billpayment.app.account.CloseAccountStep
import ai.friday.billpayment.app.account.CloseAccountStepDiscovery
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerService
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@InvestmentGoalsModule
class InvestmentCloseAccountDiscovery(
    private val investmentManagerService: InvestmentManagerService,
) : CloseAccountStepDiscovery {
    override val discoveryName: String = "InvestmentCloseAccountDiscovery"

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun prepareClose(accountId: AccountId, closureDetails: AccountClosureDetails): Either<CloseAccountError, List<CloseAccountStep>> {
        val logName = "InvestmentCloseAccountDiscovery#prepareClose"
        val markers = append("accountId", accountId.value)
            .andAppend("closureDetails", closureDetails)

        val investmentNetValue = getInvestmentNetValue(accountId).getOrElse {
            markers.andAppend("error", it)
            logger.warn(markers, logName)
            return it.left()
        }
        markers.andAppend("investmentNetValue", investmentNetValue)
        if (investmentNetValue > 0) {
            logger.info(markers, logName)
            return CloseAccountError.CloseAccountErrorWithMessageAndAmount(messageKey = "INVESTMENT_BALANCE_NOT_ZERO", messageAmount = investmentNetValue).left()
        }
        return emptyList<CloseAccountStep>().right()
    }

    private fun getInvestmentNetValue(accountId: AccountId): Either<CloseAccountError, Long> {
        val result = investmentManagerService.userInvestmentsNetValue(accountId)
        val investmentNetValue = result.getOrElse { return CloseAccountError.Unknown(exception = it).left() }
        return investmentNetValue.right()
    }
}