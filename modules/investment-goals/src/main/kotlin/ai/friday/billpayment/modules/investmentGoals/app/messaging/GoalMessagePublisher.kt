package ai.friday.billpayment.modules.investmentGoals.app.messaging

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import io.micronaut.context.annotation.Property

@InvestmentGoalsModule
class GoalMessagePublisher(
    private val messagePublisher: MessagePublisher,
    @Property(name = "sqs.optimizeGoalInstallmentAmount") val optimizeGoalInstallmentAmountQueueName: String,
    @Property(name = "sqs.checkGoalCompletion") val checkGoalCompletion: String,
    @Property(name = "sqs.createGoalInvestment") val createGoalInvestmentQueueName: String,
) {
    fun publishGoalInvestmentCreated(goalId: GoalId, accountId: AccountId, extraInstallment: Boolean, extraInstallmentAmount: Long? = null) {
        messagePublisher.sendMessage(
            createGoalInvestmentQueueName,
            CreateGoalInvestmentMessage(
                goalId = goalId.value,
                extraInstallment = extraInstallment,
                extraInstallmentAmount = extraInstallmentAmount,
                accountId = accountId.value,
            ),
        )
    }

    fun publishOptimizeGoalInstallmentAmount(goalId: GoalId) {
        messagePublisher.sendMessage(
            optimizeGoalInstallmentAmountQueueName,
            OptimizeGoalInstallmentAmountHandlerMessage(
                goalId = goalId.value,
            ),
        )
    }

    fun publishCheckGoalCompletion(goalId: GoalId, createdFromRedemption: Boolean) {
        messagePublisher.sendMessage(
            checkGoalCompletion,
            CheckGoalCompletionHandlerMessage(
                goalId = goalId.value,
                createdFromRedemption = createdFromRedemption,
            ),
        )
    }
}

data class CreateGoalInvestmentMessage(
    val goalId: String,
    val accountId: String?,
    val extraInstallment: Boolean,
    val extraInstallmentAmount: Long? = null,
)

data class OptimizeGoalInstallmentAmountHandlerMessage(
    val goalId: String,
)

data class CheckGoalCompletionHandlerMessage(
    val goalId: String,
    val createdFromRedemption: Boolean = false,
)