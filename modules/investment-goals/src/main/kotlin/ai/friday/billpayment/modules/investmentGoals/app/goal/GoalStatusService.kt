package ai.friday.billpayment.modules.investmentGoals.app.goal

import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.adapters.instrumentation.GoalInstrumentationRepository
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryService
import ai.friday.billpayment.modules.investmentGoals.app.goal.instrumentation.GoalCompletedByAmountInstrumentationEvent
import ai.friday.billpayment.modules.investmentGoals.app.goal.instrumentation.GoalCompletedByDateInstrumentationEvent
import ai.friday.billpayment.modules.investmentGoals.app.investment.CreateRegularGoalInvestmentCommand
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentService
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerService
import ai.friday.billpayment.modules.investmentGoals.app.payment.GoalsInvestmentNotificationService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@InvestmentGoalsModule
open class GoalStatusService(
    private val goalService: GoalService,
    private val goalRepository: GoalRepository,
    private val goalInvestmentService: GoalInvestmentService,
    private val investmentManagerService: InvestmentManagerService,
    private val goalsInvestmentNotificationService: GoalsInvestmentNotificationService,
    private val goalInstrumentationRepository: GoalInstrumentationRepository,
    goalCategoryService: GoalCategoryService,
) {
    private val logger = LoggerFactory.getLogger(GoalStatusService::class.java)
    private val goalCategories = goalCategoryService.findAll()

    open fun remove(goalId: GoalId, actionSource: ActionSource.WalletActionSource): Either<UpdateGoalError, GoalStatus> {
        val logName = "GoalService#remove"
        val markers = append("goalId", goalId.value)

        val goal = goalService.findGoal(goalId, actionSource.accountId).getOrElse {
            logger.warn(markers, logName, it)
            return UpdateGoalError.GoalNotFound.left()
        }
        return remove(goal, actionSource)
    }

    private fun remove(goal: Goal, actionSource: ActionSource): Either<UpdateGoalError, GoalStatus> {
        val goalId = goal.id
        val logName = "GoalService#remove"
        val markers = append("goalId", goalId.value)
        markers.andAppend("accountId", goal.accountId.value)
            .andAppend("goalStatus", goal.status)

        val userGoalPositions = investmentManagerService.userGoalPositions(
            accountId = goal.accountId,
            goalId = goal.id,
        ).getOrElse {
            logger.error(markers, logName, it)
            return UpdateGoalError.GenericException(it).left()
        }
        markers.andAppend("balance", userGoalPositions.grossValue)

        when (goal.status) {
            GoalStatus.ACTIVE, GoalStatus.PAUSED -> {}
            GoalStatus.REMOVED, GoalStatus.COMPLETED -> {
                logger.warn(markers, logName)
                return UpdateGoalError.InvalidGoalStatus.left()
            }
        }

        if (userGoalPositions.grossValue > 0) {
            logger.warn(markers, logName)
            return UpdateGoalError.BalanceDifferentThanZero(userGoalPositions.grossValue).left()
        }

        goalInvestmentService.ignoreAllGoalInvestments(goal.id, actionSource).getOrElse {
            logger.error(markers.andAppend("context", it), logName)
            return it.left()
        }

        logger.info(markers, logName)
        return goalRepository.save(goal.copy(status = GoalStatus.REMOVED)).status.right()
    }

    open fun pause(goalId: GoalId, actionSource: ActionSource.WalletActionSource): Either<UpdateGoalError, GoalStatus> {
        val logName = "GoalService#pause"
        val markers = append("goalId", goalId.value)

        val goal = goalService.findGoal(goalId, actionSource.accountId).getOrElse {
            logger.warn(markers, logName, it)
            return UpdateGoalError.GoalNotFound.left()
        }
        return pause(goal, actionSource)
    }

    private fun pause(goal: Goal, actionSource: ActionSource): Either<UpdateGoalError, GoalStatus> {
        val goalId = goal.id
        val logName = "GoalService#pause"
        val markers = append("goalId", goalId.value)

        when (goal.status) {
            GoalStatus.ACTIVE -> {}
            GoalStatus.PAUSED, GoalStatus.REMOVED, GoalStatus.COMPLETED -> return UpdateGoalError.InvalidGoalStatus.left()
        }

        goalInvestmentService.ignoreAllGoalInvestments(goal.id, actionSource).getOrElse {
            logger.error(markers.andAppend("context", it), logName)
            return it.left()
        }

        return goalRepository.save(goal.copy(status = GoalStatus.PAUSED)).status.right()
    }

    open fun resume(goalId: GoalId, actionSource: ActionSource.WalletActionSource): Either<UpdateGoalError, GoalStatus> {
        val logName = "GoalService#resume"
        val markers = append("goalId", goalId.value)

        val goal = goalService.findGoal(goalId, actionSource.accountId).getOrElse {
            logger.warn(markers, logName, it)
            return UpdateGoalError.GoalNotFound.left()
        }
        return resume(goal, actionSource)
    }

    private fun resume(goal: Goal, actionSource: ActionSource): Either<UpdateGoalError, GoalStatus> {
        val logName = "GoalService#resume"
        val goalId = goal.id
        val markers = append("goalId", goalId.value)
        markers.andAppend("accountId", goal.accountId.value)

        when (goal.status) {
            GoalStatus.PAUSED -> {}
            GoalStatus.ACTIVE, GoalStatus.REMOVED, GoalStatus.COMPLETED -> return UpdateGoalError.InvalidGoalStatus.left()
        }

        if (goal.endDate < getLocalDate()) {
            logger.warn(markers, logName)
            return UpdateGoalError.InvalidGoalStatus.left()
        }

        goalInvestmentService.create(
            CreateRegularGoalInvestmentCommand(
                goalId = goalId,
            ),
        ).getOrElse {
            logger.error(markers.andAppend("context", it), logName)
            return UpdateGoalError.GenericException(Exception(it.toString())).left()
        }

        goalService.optimizeGoalInstallmentAmount(goalId, shouldNotify = false).getOrElse {
            logger.error(markers, logName, it)
            return UpdateGoalError.GenericException(it).left()
        }

        return goalRepository.save(goal.copy(status = GoalStatus.ACTIVE)).status.right()
    }

    open fun checkAndUpdateGoalToCompleted(goalId: GoalId): Either<UpdateGoalError, Boolean> {
        val logName = "GoalStatusService#checkAndUpdateGoalToCompleted"
        val markers = append("goalId", goalId.value)

        val goal = goalService.findGoal(goalId).getOrElse {
            logger.warn(markers, logName, it)
            return UpdateGoalError.GenericException(it).left()
        }

        if (goal.status == GoalStatus.COMPLETED) {
            return true.right()
        }

        val userGoalPosition = investmentManagerService.userGoalPositions(
            accountId = goal.accountId,
            goalId = goal.id,
        ).getOrElse {
            logger.error(markers, logName, it)
            return UpdateGoalError.GenericException(it).left()
        }

        if (goal.endDate <= getLocalDate()) {
            return doCompleteGoal(goal, userGoalPosition.netValue)
        }

        if (userGoalPosition.netValue >= goal.amount) {
            return doCompleteGoal(goal, userGoalPosition.netValue)
        }

        return false.right()
    }

    fun checkAndUpdateGoalToActiveFromPaused(goalId: GoalId): Either<UpdateGoalError, Boolean> {
        val logName = "GoalStatusService#checkReactivation"
        val markers = append("goalId", goalId.value)
        val goal = goalService.findGoal(goalId).getOrElse {
            logger.warn(markers, logName, it)
            return UpdateGoalError.GenericException(it).left()
        }

        if (goal.status != GoalStatus.PAUSED) {
            return false.right()
        }

        if (goal.endDate <= getLocalDate()) {
            return false.right()
        }

        val category = goalCategories.find { it.id == goal.categoryId }
        val canBeAutoResumed = category?.type?.canBeAutoResumed() ?: false
        if (!canBeAutoResumed) {
            return false.right()
        }

        val userGoalPosition = investmentManagerService.userGoalPositions(
            accountId = goal.accountId,
            goalId = goal.id,
        ).getOrElse {
            logger.error(markers, logName, it)
            return UpdateGoalError.GenericException(it).left()
        }

        if (userGoalPosition.netValue >= goal.amount) {
            return false.right()
        }

        return resume(goal, ActionSource.System).map { true }
    }

    private fun doCompleteGoal(goal: Goal, netAmount: Long): Either<UpdateGoalError, Boolean> {
        if (goal.status == GoalStatus.COMPLETED) {
            return true.right()
        }

        if (netAmount == 0L) {
            remove(goal, ActionSource.System).getOrElse {
                return it.left()
            }
            return true.right()
        }

        val category = goalCategories.find { it.id == goal.categoryId }
        val canBeCompleted = category?.type?.canBeCompleted() ?: false

        if (canBeCompleted) {
            goalInvestmentService.ignoreAllGoalInvestments(goal.id, ActionSource.GoalInvestment(goalId = goal.id, accountId = goal.accountId)).getOrElse {
                return it.left()
            }
            goalRepository.save(goal.copy(status = GoalStatus.COMPLETED))
            goalsInvestmentNotificationService.notifyGoalCompleted(goal, currentNetAmount = netAmount)
        } else if (goal.status == GoalStatus.ACTIVE) {
            pause(goal, ActionSource.System).getOrElse {
                return it.left()
            }
            goalsInvestmentNotificationService.notifyGoalPausedViaCompletion(goal, currentNetAmount = netAmount)
        }

        if (goal.endDate >= getLocalDate()) {
            goalInstrumentationRepository.publishEvent(GoalCompletedByDateInstrumentationEvent(goal))
        } else {
            goalInstrumentationRepository.publishEvent(GoalCompletedByAmountInstrumentationEvent(goal))
        }

        return true.right()
    }
}