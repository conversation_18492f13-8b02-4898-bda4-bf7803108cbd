package ai.friday.billpayment.modules.investmentGoals.app.goal.instrumentation

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.modules.investmentGoals.app.goal.Goal
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProduct
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemption

sealed interface GoalInstrumentationEvents

data class GoalCreatedInstrumentationEvent(
    val goal: Goal,
    val goalProduct: GoalProduct,
) : GoalInstrumentationEvents

data class InvestmentRedemptionInstrumentationEvent(
    val accountId: AccountId,
    val redemption: GoalRedemption,
) : GoalInstrumentationEvents

data class GoalCompletedByAmountInstrumentationEvent(
    val goal: Goal,
) : GoalInstrumentationEvents

data class GoalCompletedByDateInstrumentationEvent(
    val goal: Goal,
) : GoalInstrumentationEvents