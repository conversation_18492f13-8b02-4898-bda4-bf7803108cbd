package ai.friday.billpayment.modules.investmentGoals.app.investment

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.modules.investmentGoals.app.goal.InstallmentFrequency
import ai.friday.billpayment.modules.investmentGoals.app.product.IndexInterestRate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import java.util.*

interface FixedIncomeManagerService {
    fun enableFixedIncomeAccount(accountRegisterData: AccountRegisterData, bankAccount: InternalBankAccount): Either<InvestmentManagerException, Unit>
    fun invest(operationId: GoalRequestOperationId, accountId: AccountId, goalId: GoalId, amount: Long, index: FixedIncomeIndex, indexRate: FixedIncomeIndexRate, forceRedemptionDate: LocalDate?): Either<InvestmentManagerWithInvestmentResultException, FixedIncomeInvestmentRequestResult>
    fun checkInvestmentRequestStatus(externalId: InvestmentManagerExternalId): Either<InvestmentManagerWithInvestmentResultException, FixedIncomeInvestmentRequestResult>
    fun checkInvestmentRequestStatus(accountId: AccountId, operationId: GoalRequestOperationId): Either<InvestmentManagerWithInvestmentResultException, FixedIncomeInvestmentRequestResult>
    fun quoteRedemption(accountId: AccountId, goalId: GoalId, amount: Long, penaltyRate: FixedIncomeIndexRate?): Either<InvestmentManagerException, FixedIncomeRedemptionQuotations>
    fun redemption(operationId: GoalRequestOperationId, accountId: AccountId, goalId: GoalId, amount: Long, penaltyRate: FixedIncomeIndexRate?): Either<InvestmentManagerWithRedemptionResultException, FixedIncomeInvestmentRedemptionResult>

    fun checkInvestmentRedemptionStatus(singleExternalId: InvestmentManagerExternalId?, groupExternalId: InvestmentManagerExternalId?): Either<InvestmentManagerWithRedemptionResultException, FixedIncomeInvestmentRedemptionResult>
    fun checkInvestmentRedemptionStatus(accountId: AccountId, operationId: GoalRequestOperationId): Either<InvestmentManagerWithRedemptionResultException, FixedIncomeInvestmentRedemptionResult>
    fun findRedemptionRequest(externalId: InvestmentManagerExternalId): Either<InvestmentManagerException, FixedIncomeRedemptionRequest?>
    fun findRedemptionRequest(accountId: AccountId, operationId: GoalRequestOperationId): Either<InvestmentManagerException, List<FixedIncomeRedemptionRequest>>
    fun findRedemptionGroupRequests(externalId: InvestmentManagerExternalId): Either<InvestmentManagerException, List<FixedIncomeRedemptionRequest>>

    fun userPositions(accountId: AccountId): Either<InvestmentManagerException, List<UserGoalPositions>>
    fun userGoalPositions(accountId: AccountId, goalId: GoalId): Either<InvestmentManagerException, UserGoalPositions>

    fun userGoalPositionAvailableToRedeem(accountId: AccountId, goalId: GoalId, penalty: FixedIncomeIndexRate?): Either<InvestmentManagerException, UserGoalAvailableToRedeemPositions>
    fun optimizeMonthlyContribution(currentGrossAmount: Long, nextPaymentDate: LocalDate, dateToComplete: LocalDate, goalNetAmount: Long, installmentFrequency: InstallmentFrequency, index: FixedIncomeIndex, indexPercentage: FixedIncomeIndexRate): Either<InvestmentManagerException, Long>

    fun simulateFutureNetValue(currentGrossAmount: Long, nextPaymentDate: LocalDate, dateToComplete: LocalDate, installmentAmount: Long, installmentFrequency: InstallmentFrequency, index: FixedIncomeIndex, indexPercentage: FixedIncomeIndexRate): Either<InvestmentManagerException, Long>
    fun getIndexInterestRates(): Either<Exception, List<IndexInterestRate>>
    fun compareAllRates(startDate: LocalDate, endDate: LocalDate, indexRelativeRate: FixedIncomeIndexRate): Either<InvestmentManagerException, Map<FixedIncomeIndex, Double>>
}

interface FixedIncomeManagerProvider : FixedIncomeManagerService

enum class FixedIncomeIndex {
    CDI, SAVINGS
}

data class FixedIncomeIndexRate(val rate: Int)

data class GoalPenaltyRate(
    val limitDate: LocalDate,
    val penaltyRate: FixedIncomeIndexRate,
)

data class FixedIncomeInvestmentRequestResult(
    val operationId: GoalRequestOperationId?,
    val externalId: InvestmentManagerExternalId?,
    val provider: FixedIncomeProvider,
    val status: InvestmentManagerRequestStatus,
    val fixedIncomeProduct: FixedIncomeProduct?,
    val errorMessage: String?,
    val positionId: FixedIncomePositionId?,
    val maturityDate: LocalDate?,
)

data class FixedIncomeInvestmentRedemptionResult(
    val operationId: GoalRequestOperationId?,
    val provider: FixedIncomeProvider,
    val status: InvestmentManagerRequestStatus,
    val redemptionGroupExternalId: InvestmentManagerExternalId?,
    val redemptionExternalIds: List<InvestmentManagerExternalId>,
    val errorMessage: String?,
    val amounts: FixedIncomeInvestmentRedemptionAmountsResult?,

) {
    val singleExternalId: InvestmentManagerExternalId? = calculateSingleExternalRedemptionId(redemptionGroupExternalId, redemptionExternalIds)
}

data class FixedIncomeInvestmentRedemptionAmountsResult(
    val amount: Long,
    val completedAmount: Long,
    val failedAmount: Long,
) {
    val pendingAmount = amount - completedAmount - failedAmount
}

internal fun calculateSingleExternalRedemptionId(groupExternalId: InvestmentManagerExternalId?, redemptionExternalIds: List<InvestmentManagerExternalId>): InvestmentManagerExternalId? {
    return if (groupExternalId != null) {
        null
    } else {
        redemptionExternalIds.firstOrNull()
    }
}

data class FixedIncomeRedemptionRequest(
    val externalId: InvestmentManagerExternalId,
    val goalId: GoalId,
    val redemptionGroupExternalId: InvestmentManagerExternalId?,
    val accountId: AccountId,
    val provider: FixedIncomeProvider,
    val amount: Long,
    val status: InvestmentManagerRequestStatus,
    val positionId: FixedIncomePositionId?,
    val createdAt: ZonedDateTime,
)

enum class FixedIncomeProvider {
    ARBI,
}

data class InvestmentManagerExternalId(val value: String)

data class FixedIncomePositionId(val value: String)

enum class InvestmentManagerRequestStatus {
    CREATED, REQUESTED, COMPLETED, FAILED, UNKNOWN, NOT_FOUND;

    fun isError(): Boolean {
        return this in listOf(FAILED)
    }
}

data class FixedIncomeProduct(
    val provider: FixedIncomeProvider,
    val productId: String,
    val name: String,
    val index: FixedIncomeIndex,
    val indexPercentage: FixedIncomeIndexRate,
    val minTransactionValue: Long,
    val maxTransactionValue: Long,
    val tradeTimeLimit: LocalTime,
)

data class UserGoalAvailableToRedeemPositions(
    val accountId: AccountId,
    val goalId: GoalId,
    val positions: List<UserGoalPositionAvailableToRedeemAmount>,
    val netValue: Long,
)

data class UserGoalPositionAvailableToRedeemAmount(
    val provider: FixedIncomeProvider,
    val positionId: FixedIncomePositionId,
    val availableToRedeem: Long,
)

data class UserGoalPositions(
    val accountId: AccountId,
    val goalId: GoalId,
    val positions: List<FixedIncomePosition>,
    val netValue: Long,
    val grossValue: Long,
    val investedValue: Long,
    val irValue: Long,
    val iofValue: Long,
    val earningValue: Long,
) {
    companion object {
        @JvmStatic
        fun ofEmpty(accountId: AccountId, goalId: GoalId): UserGoalPositions {
            return UserGoalPositions(
                accountId = accountId,
                goalId = goalId,
                positions = emptyList(),
                netValue = 0,
                grossValue = 0,
                investedValue = 0,
                irValue = 0,
                iofValue = 0,
                earningValue = 0,
            )
        }
    }
}

data class FixedIncomePosition(
    val provider: FixedIncomeProvider,
    val product: FixedIncomeProduct?,
    val positionId: String,

    val operationDate: LocalDate,
    val liquidityDueDate: LocalDate,
    val maturityDate: LocalDate,
    val unitPrice: Long,
    val quantity: Long,
    val netValue: Long,
    val grossValue: Long,
    val name: String,

    val investedValue: Long,
    val irValue: Long,
    val iofValue: Long,
    val earningValue: Long,
)

enum class FixedIncomeRedemptionType { TOTAL, PARTIAL }

data class FixedIncomeRedemptionQuotations(
    val accountId: AccountId,
    val goalId: GoalId,
    val quotations: List<FixedIncomeRedemptionQuotation>,
    val principalValue: Long,
    val grossValue: Long,
    val netValue: Long,
    val taxValue: Long,
    val penaltyValue: Long,
)

data class FixedIncomeRedemptionQuotation(
    val quotationId: InvestmentManagerExternalId?,
    val provider: FixedIncomeProvider,
    val positionId: FixedIncomePositionId,
    val principalValue: Long,
    val grossValue: Long,
    val netValue: Long,
    val quantity: Int,
    val pu: Double,
    val penaltyRate: FixedIncomeIndexRate,
    val taxValue: Long,
    val redemptionType: FixedIncomeRedemptionType,
    val penaltyValue: Long,
)

class InvestmentManagerWithInvestmentResultException(val result: FixedIncomeInvestmentRequestResult, val throwable: Throwable?) : Exception(throwable)
class InvestmentManagerWithRedemptionResultException(val result: FixedIncomeInvestmentRedemptionResult, val throwable: Throwable) : Exception(throwable)
class InvestmentManagerException(val throwable: Throwable) : Exception(throwable)

data class GoalRequestOperationId(val value: String) {
    constructor() : this("GOAL_REQUEST_OID_${UUID.randomUUID()}")
}

data class GoalInvestmentId(
    val value: String = "GOAL-INVESTMENT-${UUID.randomUUID()}",
)

data class GoalInvestment(
    val id: GoalInvestmentId = GoalInvestmentId(),
    val goalId: GoalId,
    val billId: BillId,
    val amount: Long,
    val dueDate: LocalDate,
    val paidAt: ZonedDateTime?,
    val extraInstallment: Boolean,
    val status: GoalInvestmentStatus,
    val createdAt: ZonedDateTime = getZonedDateTime(),
    val updatedAt: ZonedDateTime = getZonedDateTime(),
)

enum class GoalInvestmentStatus {
    CREATED, PROCESSING, DONE, FAILED, CANCELED
}

interface GoalInvestmentRepository {
    fun save(goalInvestment: GoalInvestment)
    fun findByGoalId(goalId: GoalId): List<GoalInvestment>
    fun findByBillId(billId: BillId): GoalInvestment?
}