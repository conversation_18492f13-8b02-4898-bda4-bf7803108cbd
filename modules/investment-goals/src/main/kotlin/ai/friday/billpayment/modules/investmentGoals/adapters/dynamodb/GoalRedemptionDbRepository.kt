package ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.AbstractBillPaymentDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.GlobalSecondaryIndexNames
import ai.friday.billpayment.adapters.dynamodb.INDEX_1
import ai.friday.billpayment.adapters.dynamodb.INDEX_2
import ai.friday.billpayment.adapters.dynamodb.INDEX_3
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemption
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionId
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionRepository
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionStatus
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val ENTITY_ID = "GOAL_REDEMPTION"

@InvestmentGoalsModule
class GoalRedemptionDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<GoalRedemptionEntity>(cli, GoalRedemptionEntity::class.java)

@InvestmentGoalsModule
class GoalRedemptionDbRepository(
    private val client: GoalRedemptionDynamoDAO,
) : GoalRedemptionRepository {
    private val mapper = jacksonObjectMapper()

    override fun save(goalRedemption: GoalRedemption) {
        val entity =
            GoalRedemptionEntity().apply {
                partitionKey = ENTITY_ID
                sortKey = goalRedemption.id.value
                gSIndex1PartitionKey = goalRedemption.goalId.value
                gSIndex1SortKey = "$ENTITY_ID#${goalRedemption.status}"
                gSIndex2PartitionKey = goalRedemption.walletId.value
                gSIndex2SortKey = "$ENTITY_ID#${goalRedemption.status}"
                gSIndex3PartitionKey = goalRedemption.id.value
                gSIndex3SortKey = ENTITY_ID
                goalId = goalRedemption.goalId.value
                walletId = goalRedemption.walletId.value
                netAmount = goalRedemption.netAmount
                penaltyRate = goalRedemption.penaltyRate?.rate
                status = goalRedemption.status
                source = mapper.writeValueAsString(goalRedemption.actionSource)
                createdAt = goalRedemption.createdAt.format(dateTimeFormat)
                updatedAt = getZonedDateTime().format(dateTimeFormat)
            }
        client.save(entity)
    }

    override fun findByGoalId(goalId: GoalId): List<GoalRedemption> =
        client.findBeginsWithOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            goalId.value,
            ENTITY_ID,
        ).map { it.toGoalRedemption() }

    override fun findByWalletId(walletId: WalletId): List<GoalRedemption> =
        client.findBeginsWithOnIndex(
            GlobalSecondaryIndexNames.GSIndex2,
            walletId.value,
            ENTITY_ID,
        ).map { it.toGoalRedemption() }

    override fun findOrNull(goalRedemptionId: GoalRedemptionId): GoalRedemption? =
        client.findByPartitionKeyAndScanKeyOnIndex(
            GlobalSecondaryIndexNames.GSIndex3,
            goalRedemptionId.value,
            ENTITY_ID,
        ).firstOrNull()?.toGoalRedemption()

    private fun GoalRedemptionEntity.toGoalRedemption() = GoalRedemption(
        id = GoalRedemptionId(value = sortKey),
        goalId = GoalId(value = goalId),
        walletId = WalletId(value = walletId),
        netAmount = netAmount,
        penaltyRate = penaltyRate?.let { FixedIncomeIndexRate(it) },
        status = status,
        actionSource = mapper.readValue(source, ActionSource.WalletActionSource::class.java),
        createdAt = ZonedDateTime.parse(createdAt, dateTimeFormat),
        updatedAt = ZonedDateTime.parse(updatedAt, dateTimeFormat),
    )
}

@DynamoDbBean
class GoalRedemptionEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1SortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    lateinit var gSIndex2PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    lateinit var gSIndex2SortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_3])
    @get:DynamoDbAttribute(value = "GSIndex3PrimaryKey")
    lateinit var gSIndex3PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_3])
    @get:DynamoDbAttribute(value = "GSIndex3ScanKey")
    lateinit var gSIndex3SortKey: String

    @get:DynamoDbAttribute(value = "GoalId")
    lateinit var goalId: String

    @get:DynamoDbAttribute(value = "WalletId")
    lateinit var walletId: String

    @get:DynamoDbAttribute(value = "NetAmount")
    var netAmount: Long = 0

    @get:DynamoDbAttribute(value = "PenaltyRate")
    var penaltyRate: Int? = null

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: GoalRedemptionStatus

    @get:DynamoDbAttribute(value = "Source")
    lateinit var source: String

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute(value = "UpdatedAt")
    lateinit var updatedAt: String
}