package ai.friday.billpayment.modules.investmentGoals.app.product

import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndex
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.time.ZonedDateTime
import java.util.UUID

data class GoalProductId(
    val value: String,
) {
    constructor() : this("GOAL-PRODUCT-${UUID.randomUUID()}")
}

enum class GoalProductRisk {
    LOW, MEDIUM, HIGH
}

enum class GoalProductType {
    CDB, SAVINGS
}

enum class GoalProductLiquidity {
    DAILY, MATURITY
}

data class GoalProduct(
    val id: GoalProductId = GoalProductId(),
    val type: GoalProductType,
    val index: FixedIncomeIndex,
    val indexIncome: FixedIncomeIndexRate,
    val liquidity: GoalProductLiquidity,
    val minimumTermDays: Long?,
    val maximumTermDays: Long?,
    val asset: String,
    val risk: GoalProductRisk,
    val issuer: String,
    val provider: String,
    val enabled: Boolean = true,
    val createdAt: ZonedDateTime = getZonedDateTime(),
    val updatedAt: ZonedDateTime = getZonedDateTime(),
)

data class SaveGoalProductCommand(
    val type: GoalProductType,
    val index: FixedIncomeIndex,
    val indexIncome: Int,
    val liquidity: GoalProductLiquidity,
    val minimumTermDays: Long?,
    val maximumTermDays: Long?,
    val asset: String,
    val risk: GoalProductRisk,
    val issuer: String,
    val provider: String,
    val enabled: Boolean = true,
)

interface GoalProductRepository {
    fun save(product: GoalProduct): GoalProduct
    fun findAll(): List<GoalProduct>
    fun findOrNull(id: GoalProductId): GoalProduct?
}