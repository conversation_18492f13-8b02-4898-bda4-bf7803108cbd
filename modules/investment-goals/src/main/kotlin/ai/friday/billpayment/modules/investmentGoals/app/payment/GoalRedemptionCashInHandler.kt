package ai.friday.billpayment.modules.investmentGoals.app.payment

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.cashIn.CashInHandler
import ai.friday.billpayment.app.payment.SettlementOperation
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeInvestmentRedemptionResult
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeProvider
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalRequestOperationId
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerExternalId
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerRequestStatus
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerService
import ai.friday.billpayment.modules.investmentGoals.app.investment.calculateSingleExternalRedemptionId
import ai.friday.billpayment.modules.investmentGoals.app.messaging.GoalMessagePublisher
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemption
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionId
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionRepository
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionStatus
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getEpochMilli
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.context.annotation.Property
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@InvestmentGoalsModule
class GoalRedemptionCashInHandler(
    @Property(name = "integrations.investment-manager.investment-wait-time") private val waitTime: Long,
    @Property(name = "integrations.investment-manager.investment-pooling-interval") private val poolingInterval: Long,
    private val investmentManagerService: InvestmentManagerService,
    private val goalRedemptionRepository: GoalRedemptionRepository,
    private val balanceService: BalanceService,
    private val walletService: WalletService,
    private val internalBankService: InternalBankService,
    private val goalsInvestmentNotificationService: GoalsInvestmentNotificationService,
    private val goalMessagePublisher: GoalMessagePublisher,
) : CashInHandler {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override val supportedTransactionType = TransactionType.GOAL_REDEMPTION

    override fun execute(transaction: Transaction): Transaction {
        val logName = "GoalRedemptionCashInHandler#execute"
        val goalRedemption = transaction.settlementData.getTarget<GoalRedemption>()
        val markers = append("investmentRedemption", goalRedemption).andAppend("transactionId", transaction.id.value)

        val settlementOperation = (transaction.settlementData.settlementOperation!! as GoalRedemptionSettlementOperation)

        // TODO - evitar duplicar o resgate. como feito na aplicação

        val operationId = settlementOperation.operationId
        goalsInvestmentNotificationService.notifyInvestmentRedemptionCreated(goalRedemption)

        val redemptionResult = investmentManagerService.redemption(
            operationId = operationId,
            accountId = transaction.payer.accountId,
            goalId = goalRedemption.goalId,
            amount = transaction.settlementData.totalAmount,
            penaltyRate = goalRedemption.penaltyRate,
        ).getOrElse {
            logger.error(markers.andAppend("context", "erro na resposta no InvestmentManager.").andAppend("result", it.result), logName, it.throwable)
            it.result
        }
        markers.andAppend("redemptionResult", redemptionResult)

        return processRedemptionResult(transaction, redemptionResult)
    }

    override fun retry(transaction: Transaction): Transaction {
        val logName = "GoalRedemptionCashInHandler#retry"
        val markers = append("transactionId", transaction.id.value)

        val settlementOperation = transaction.settlementData.getOperation<GoalRedemptionSettlementOperation>()

        val operationId = settlementOperation.operationId
        markers.andAppend("operationId", operationId.value)

        val redemptionResult = checkRedemptionStatusManyTimes(
            accountId = transaction.payer.accountId,
            operationId = operationId,
            externalId = settlementOperation.singleExternalId,
            externalGroupId = settlementOperation.redemptionGroupExternalId,
            poolingInterval = poolingInterval,
            maxWaitTime = waitTime,
            waitForStatuses = listOf(InvestmentManagerRequestStatus.FAILED, InvestmentManagerRequestStatus.COMPLETED),
        )
        markers.andAppend("redemptionResult", redemptionResult)

        logger.info(markers, logName)
        return processRedemptionResult(transaction, redemptionResult)
    }

    private fun Transaction.updateWithRedemptionResult(
        redemptionResult: FixedIncomeInvestmentRedemptionResult,
    ): Transaction {
        status = when (redemptionResult.status) {
            InvestmentManagerRequestStatus.CREATED,
            InvestmentManagerRequestStatus.REQUESTED,
            InvestmentManagerRequestStatus.UNKNOWN,
            -> TransactionStatus.PROCESSING

            InvestmentManagerRequestStatus.COMPLETED -> TransactionStatus.COMPLETED
            InvestmentManagerRequestStatus.FAILED, InvestmentManagerRequestStatus.NOT_FOUND -> TransactionStatus.FAILED
        }
        settlementData.settlementOperation = settlementData.getOperation<GoalRedemptionSettlementOperation>().updateWithRedemptionResult(redemptionResult)
        return this
    }

    override fun notifyStarted(transaction: Transaction) {
        updateRedemptionStatus(transaction.settlementData.getTarget<GoalRedemption>().id, GoalRedemptionStatus.PROCESSING)
    }

    override fun notifyCompleted(transaction: Transaction) {
        updateRedemptionStatus(transaction.settlementData.getTarget<GoalRedemption>().id, GoalRedemptionStatus.DONE)
    }

    override fun notifyFailed(transaction: Transaction) {
        updateRedemptionStatus(transaction.settlementData.getTarget<GoalRedemption>().id, GoalRedemptionStatus.FAILED)
    }

    private fun processRedemptionResult(transaction: Transaction, redemptionResult: FixedIncomeInvestmentRedemptionResult): Transaction {
        when (redemptionResult.status) {
            InvestmentManagerRequestStatus.COMPLETED, InvestmentManagerRequestStatus.FAILED, InvestmentManagerRequestStatus.NOT_FOUND -> {
                invalidateBalanceAndSynchronizeBankAccount(transaction)
                val goalRedemption = transaction.settlementData.getTarget<GoalRedemption>()
                goalsInvestmentNotificationService.notifyInvestmentRedemptionFinished(goalRedemption, redemptionResult)
                if (redemptionResult.status == InvestmentManagerRequestStatus.COMPLETED) {
                    goalMessagePublisher.publishCheckGoalCompletion(goalRedemption.goalId, true)
                }
            }

            InvestmentManagerRequestStatus.CREATED,
            InvestmentManagerRequestStatus.REQUESTED,
            InvestmentManagerRequestStatus.UNKNOWN,
            -> { }
        }

        return transaction.updateWithRedemptionResult(redemptionResult)
    }

    private fun invalidateBalanceAndSynchronizeBankAccount(transaction: Transaction) {
        val goalRedemption = transaction.settlementData.getTarget<GoalRedemption>()
        val wallet = walletService.findWallet(goalRedemption.walletId)
        val accountId = wallet.founder.accountId
        val accountPaymentMethodId = wallet.paymentMethodId
        try {
            balanceService.invalidate(wallet.paymentMethodId)
            internalBankService.synchronizeBankAccount(accountId = accountId, accountPaymentMethodId = accountPaymentMethodId)
        } catch (e: Exception) {
            logger.error(append("context", "Erro ao sincronizar conta bancária").andAppend("accountId", accountId.value).andAppend("accountPaymentMethodId", accountPaymentMethodId), "GoalRedemptionCashInHandler#updateWithRedemptionResult", e)
        }
    }

    private fun updateRedemptionStatus(goalRedemptionId: GoalRedemptionId, goalRedemptionStatus: GoalRedemptionStatus) {
        val markers = append("goalRedemptionId", goalRedemptionId.value)
            .andAppend("newStatus", goalRedemptionStatus)

        val redemption = goalRedemptionRepository.findOrNull(goalRedemptionId) ?: throw IllegalStateException("goal redemption not found ${goalRedemptionId.value}")
        markers.andAppend("oldStatus", redemption.status)

        logger.info(markers, "GoalRedemptionCashInHandler#updateRedemptionStatus")
        if (redemption.status != goalRedemptionStatus) {
            goalRedemptionRepository.save(redemption.copy(status = goalRedemptionStatus))
        }
    }

    private fun checkRedemptionStatusManyTimes(
        accountId: AccountId,
        operationId: GoalRequestOperationId,
        externalId: InvestmentManagerExternalId?,
        externalGroupId: InvestmentManagerExternalId?,
        poolingInterval: Long,
        maxWaitTime: Long,
        waitForStatuses: List<InvestmentManagerRequestStatus>,
    ): FixedIncomeInvestmentRedemptionResult {
        val logName = "GoalRedemptionCashInHandler#checkRedemptionStatusManyTimes"
        val startTime = getEpochMilli()

        val markers = append("operationId", operationId.value).andAppend("externalId", externalId?.value).andAppend("externalGroupId", externalGroupId?.value).andAppend("poolingInterval", poolingInterval)
            .andAppend("maxWaitTime", maxWaitTime)

        while (true) {
            Thread.sleep(poolingInterval)

            val redemptionRequestUpdated = if (externalGroupId != null || externalId != null) {
                investmentManagerService.checkInvestmentRedemptionStatus(singleExternalId = externalId, groupExternalId = externalGroupId)
            } else {
                investmentManagerService.checkInvestmentRedemptionStatus(accountId = accountId, operationId = operationId)
            }.getOrElse {
                logger.error(markers.andAppend("context", "erro na resposta no InvestmentManager").andAppend("result", it.result), logName, it.throwable)
                return it.result
            }

            val elapsedTime = getEpochMilli() - startTime

            val statusMatch = waitForStatuses.contains(redemptionRequestUpdated.status)

            markers.andAppend("status", redemptionRequestUpdated.status).andAppend("elapsedTime", elapsedTime).andAppend("statusMatch", statusMatch)
            logger.info(markers, "InvestmentCheckout#checkInvestmentStatusManyTimes")

            if (statusMatch || maxWaitTime < elapsedTime) {
                return redemptionRequestUpdated
            }
        }
    }
}

data class GoalRedemptionSettlementOperation(
    val operationId: GoalRequestOperationId = GoalRequestOperationId(),
    var status: InvestmentManagerRequestStatus = InvestmentManagerRequestStatus.CREATED,
    val redemptionGroupExternalId: InvestmentManagerExternalId? = null,
    val redemptionExternalIds: List<InvestmentManagerExternalId> = listOf(),
    val amount: Long,
    var errorDescription: String = "",
    val provider: FixedIncomeProvider? = null,
) : SettlementOperation {
    override val authentication: String? = null

    val singleExternalId: InvestmentManagerExternalId? = calculateSingleExternalRedemptionId(redemptionGroupExternalId, redemptionExternalIds)

    override val gateway: FinancialServiceGateway = when (provider) {
        FixedIncomeProvider.ARBI -> FinancialServiceGateway.ARBI
        null -> FinancialServiceGateway.FRIDAY // FIXME deveria ser UNKNOWN
    }

    override fun isRetryable() = true

    override fun getErrorMessage() = errorDescription

    override fun confirm() {
        this.status = InvestmentManagerRequestStatus.COMPLETED
        this.errorDescription = ""
    }

    override fun shouldCheckSettlementStatus(): Boolean {
        return when (this.status) {
            InvestmentManagerRequestStatus.CREATED,
            InvestmentManagerRequestStatus.REQUESTED,
            InvestmentManagerRequestStatus.UNKNOWN,
            -> true

            InvestmentManagerRequestStatus.COMPLETED,
            InvestmentManagerRequestStatus.FAILED,
            InvestmentManagerRequestStatus.NOT_FOUND,
            -> false
        }
    }

    override fun settlementId(): String {
        return this.operationId.value
    }
}

private fun GoalRedemptionSettlementOperation.updateWithRedemptionResult(redemptionResult: FixedIncomeInvestmentRedemptionResult): GoalRedemptionSettlementOperation {
    return copy(
        status = redemptionResult.status,
        redemptionGroupExternalId = redemptionResult.redemptionGroupExternalId,
        redemptionExternalIds = redemptionResult.redemptionExternalIds,
        errorDescription = redemptionResult.errorMessage ?: "",
        provider = redemptionResult.provider,
    )
}