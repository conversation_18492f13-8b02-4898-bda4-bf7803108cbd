package ai.friday.billpayment.modules.investmentGoals.adapters.instrumentation

import ai.friday.billpayment.app.instrumentation.InstrumentationRepository
import ai.friday.billpayment.app.integrations.CrmRepository
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.goal.instrumentation.GoalCompletedByAmountInstrumentationEvent
import ai.friday.billpayment.modules.investmentGoals.app.goal.instrumentation.GoalCompletedByDateInstrumentationEvent
import ai.friday.billpayment.modules.investmentGoals.app.goal.instrumentation.GoalCreatedInstrumentationEvent
import ai.friday.billpayment.modules.investmentGoals.app.goal.instrumentation.GoalInstrumentationEvents
import ai.friday.billpayment.modules.investmentGoals.app.goal.instrumentation.InvestmentRedemptionInstrumentationEvent
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@InvestmentGoalsModule
class GoalInstrumentationRepository(
    private val intercomAdapter: CrmRepository,
) : InstrumentationRepository<GoalInstrumentationEvents> {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun publishEvent(event: GoalInstrumentationEvents) {
        try {
            when (event) {
                is GoalCreatedInstrumentationEvent -> {
                    val intercomEventName = "goal_created"
                    intercomAdapter.publishEvent(
                        accountId = event.goal.accountId,
                        eventName = intercomEventName,
                        customParams = mapOf(
                            "goalId" to event.goal.id.value,
                            "goalName" to event.goal.name,
                            "goalInstallmentTotal" to event.goal.totalInstallments,
                            "goalEndDate" to event.goal.endDate.format(DateTimeFormatter.ISO_DATE),
                            "goalLiquidity" to event.goal.liquidity.name,
                            "goalIndex" to "${event.goalProduct.indexIncome.rate}% ${event.goalProduct.index.name}",
                        ),
                    )
                }

                is InvestmentRedemptionInstrumentationEvent -> {
                    val intercomEventName = "investment_redemption"
                    intercomAdapter.publishEvent(
                        accountId = event.accountId,
                        eventName = intercomEventName,
                        customParams = mapOf(
                            "goalId" to event.redemption.goalId.value,
                            "hasPenalty" to (event.redemption.penaltyRate != null).toString(),
                        ),
                    )
                }

                is GoalCompletedByAmountInstrumentationEvent -> {
                    val intercomEventName = "goal_completed_by_amount"
                    intercomAdapter.publishEvent(
                        accountId = event.goal.accountId,
                        eventName = intercomEventName,
                        customParams = mapOf(
                            "goalId" to event.goal.id.value,
                            "goalName" to event.goal.name,
                            "goalInstallmentTotal" to event.goal.totalInstallments,
                            "goalEndDate" to event.goal.endDate.format(DateTimeFormatter.ISO_DATE),
                            "goalLiquidity" to event.goal.liquidity.name,
                        ),
                    )
                }

                is GoalCompletedByDateInstrumentationEvent -> {
                    val intercomEventName = "goal_completed_by_date"
                    intercomAdapter.publishEvent(
                        accountId = event.goal.accountId,
                        eventName = intercomEventName,
                        customParams = mapOf(
                            "goalId" to event.goal.id.value,
                            "goalName" to event.goal.name,
                            "goalInstallmentTotal" to event.goal.totalInstallments,
                            "goalEndDate" to event.goal.endDate.format(DateTimeFormatter.ISO_DATE),
                            "goalLiquidity" to event.goal.liquidity.name,
                        ),
                    )
                }
            }
        } catch (e: Exception) {
            logger.error(append("eventData", event), "GoalInstrumentationRepository#publishEvent", e)
        }
    }
}