package ai.friday.billpayment.modules.investmentGoals.adapters.messaging

import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModuleNoTest
import ai.friday.billpayment.modules.investmentGoals.app.investment.CreateExtraGoalInvestmentCommand
import ai.friday.billpayment.modules.investmentGoals.app.investment.CreateRegularGoalInvestmentCommand
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentService
import ai.friday.billpayment.modules.investmentGoals.app.messaging.CreateGoalInvestmentMessage
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.context.annotation.Property
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@InvestmentGoalsModuleNoTest
open class CreateGoalInvestmentMessageHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val goalInvestmentService: GoalInvestmentService,
    @Property(name = "sqs.createGoalInvestment") val createGoalInvestmentQueueName: String,
) : AbstractSQSHandler(amazonSQS, configuration, createGoalInvestmentQueueName) {
    private val logger = LoggerFactory.getLogger(CreateGoalInvestmentMessageHandler::class.java)

    override fun handleMessage(message: Message): SQSHandlerResponse {
        val messageBody = parseObjectFrom<CreateGoalInvestmentMessage>(message.body())

        val markers = Markers.append("goalId", messageBody.goalId)

        val command = if (messageBody.extraInstallment) {
            if (messageBody.extraInstallmentAmount == null || messageBody.extraInstallmentAmount <= 0) {
                logger.error(markers.andAppend("context", "extra installment should have a positive amount"), "CreateGoalInvestmentMessageHandler")
                return SQSHandlerResponse(true)
            }
            CreateExtraGoalInvestmentCommand(
                goalId = GoalId(messageBody.goalId),
                extraInstallmentAmount = messageBody.extraInstallmentAmount,
                accountId = messageBody.accountId?.let { AccountId(it) },
            )
        } else {
            if (messageBody.extraInstallmentAmount != null) {
                logger.error(markers.andAppend("context", "regular installment should not have amount"), "CreateGoalInvestmentMessageHandler")
                return SQSHandlerResponse(true)
            }
            CreateRegularGoalInvestmentCommand(
                goalId = GoalId(messageBody.goalId),
            )
        }

        val shouldDeleteMessage = goalInvestmentService.create(
            command = command,
        ).map {
            logger.info(markers, "CreateGoalInvestmentMessageHandler")
            true
        }.getOrElse {
            logger.warn(markers.andAppend("error", it), "CreateGoalInvestmentMessageHandler")
            !it.retryable
        }

        return SQSHandlerResponse(shouldDeleteMessage = shouldDeleteMessage)
    }

    override fun handleError(message: Message, e: Exception): SQSHandlerResponse {
        logger.error("CreateGoalInvestmentMessageHandler", e)
        return SQSHandlerResponse(false)
    }
}