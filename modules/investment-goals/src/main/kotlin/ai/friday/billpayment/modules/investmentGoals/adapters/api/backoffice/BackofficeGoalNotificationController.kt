package ai.friday.billpayment.modules.investmentGoals.adapters.api.backoffice

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.receipt.builder.ReceiptDataBuilderService
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalRepository
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeInvestmentRedemptionAmountsResult
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeInvestmentRedemptionResult
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeProvider
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentRepository
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalRequestOperationId
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerRequestStatus
import ai.friday.billpayment.modules.investmentGoals.app.payment.GoalsInvestmentNotificationService
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionId
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionRepository
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.BACKOFFICE)
@Controller("/backoffice/investment/notify")
@InvestmentGoalsModule
class BackofficeGoalNotificationController(
    private val goalRedemptionRepository: GoalRedemptionRepository,
    private val goalsInvestmentNotificationService: GoalsInvestmentNotificationService,
    private val goalInvestmentRepository: GoalInvestmentRepository,
    private val goalRepository: GoalRepository,
    private val billRepository: BillEventRepository,
    private val receiptDataBuilderService: ReceiptDataBuilderService,
    private val walletRepository: WalletRepository,
) {

    @Post("/message/{message}")
    fun notifyMessage(
        @Body rawBody: String,
        @PathVariable message: String,
    ): HttpResponse<*> {
        try {
            when (message) {
                "notifyInvestmentRedemptionCreated" -> notifyInvestmentRedemptionCreated(rawBody)
                "notifyInvestmentRedemptionFinished" -> notifyInvestmentRedemptionFinished(rawBody)
                "notifyInvestmentReceipt" -> notifyInvestmentReceipt(rawBody)
                "notifyGoalCompleted" -> notifyGoalCompleted(rawBody)
                else -> throw Exception("Message not found")
            }
        } catch (e: Exception) {
            logger.error(
                Markers.append("rawBody", rawBody).andAppend("path_message", message),
                "BackofficeGoalNotificationController#notifyMessage",
                e,
            )
            return HttpResponse.serverError<Unit>()
        }
        logger.info(Markers.append("rawBody", rawBody).andAppend("path_message", message), "BackofficeGoalNotificationController#notifyMessage")

        return HttpResponse.ok("")
    }

    private fun notifyGoalCompleted(rawBody: String) {
        val body = parseObjectFrom<NotifyInvestimentCompletedTO>(rawBody)
        val goal = goalRepository.find(GoalId(body.goalId)).getOrElse {
            logger.error(Markers.append("rawBody", rawBody), "BackofficeGoalNotificationController#notifyGoalCompleted")
            return
        }
        goalsInvestmentNotificationService.notifyGoalCompleted(goal, body.amount)
    }

    private fun notifyInvestmentRedemptionCreated(rawBody: String) {
        val body = parseObjectFrom<NotifyInvestmentRedemptionTO>(rawBody)
        val goalRedemption = goalRedemptionRepository.findOrNull(GoalRedemptionId(body.goalRedemptionId)) ?: run {
            logger.error(Markers.append("rawBody", rawBody), "BackofficeGoalNotificationController#notifyInvestmentRedemption")
            return
        }
        goalsInvestmentNotificationService.notifyInvestmentRedemptionCreated(goalRedemption)
    }

    private fun notifyInvestmentReceipt(rawBody: String) {
        val body = parseObjectFrom<NotifyInvestmentReceiptTO>(rawBody)
        val markers = Markers.append("rawBody", rawBody)
        val logName = "BackofficeGoalNotificationController#notifyInvestmentReceipt"
        val goalInvestment = goalInvestmentRepository.findByBillId(BillId(body.billId)) ?: run {
            logger.error(markers.andAppend("errorMessage", "goalInvestment not found"), logName)
            return
        }
        val bill = billRepository.getBillById(goalInvestment.billId).getOrElse {
            logger.error(markers.andAppend("errorMessage", "bill not found"), logName)
            return
        }

        val billPaid = bill.history.firstOrNull {
            it is BillPaid
        } ?: run {
            logger.error(markers.andAppend("errorMessage", "bill paid event not found"), logName)
            return
        }

        val wallet = walletRepository.findWallet(billPaid.walletId)

        val receiptData = receiptDataBuilderService.getReceiptData(bill, billPaid as BillPaid, wallet)

        goalsInvestmentNotificationService.notifyReceipt(bill, receiptData, listOf(wallet.founder), wallet)
    }

    private fun notifyInvestmentRedemptionFinished(rawBody: String) {
        val body = parseObjectFrom<NotifyInvestmentRedemptionTO>(rawBody)
        val goalRedemption = goalRedemptionRepository.findOrNull(GoalRedemptionId(body.goalRedemptionId)) ?: run {
            logger.error(Markers.append("rawBody", rawBody), "BackofficeGoalNotificationController#notifyInvestmentRedemption")
            return
        }
        val status = InvestmentManagerRequestStatus.valueOf(body.status!!)

        goalsInvestmentNotificationService.notifyInvestmentRedemptionFinished(
            goalRedemption,
            FixedIncomeInvestmentRedemptionResult(
                operationId = GoalRequestOperationId("123"),
                provider = FixedIncomeProvider.ARBI,
                status = status,
                redemptionGroupExternalId = null,
                redemptionExternalIds = listOf(),
                errorMessage = null,
                amounts = when (status) {
                    InvestmentManagerRequestStatus.COMPLETED -> FixedIncomeInvestmentRedemptionAmountsResult(
                        goalRedemption.netAmount,
                        goalRedemption.netAmount,
                        0,
                    )

                    InvestmentManagerRequestStatus.FAILED -> if (body.fullFailure) {
                        FixedIncomeInvestmentRedemptionAmountsResult(
                            goalRedemption.netAmount,
                            0,
                            goalRedemption.netAmount,
                        )
                    } else {
                        val failed = goalRedemption.netAmount / 3
                        FixedIncomeInvestmentRedemptionAmountsResult(
                            goalRedemption.netAmount,
                            goalRedemption.netAmount - failed,
                            failed,
                        )
                    }

                    InvestmentManagerRequestStatus.NOT_FOUND -> null
                    else -> throw IllegalStateException("Invalid status")
                },
            ),
        )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(BackofficeGoalNotificationController::class.java)
    }
}

private data class NotifyInvestmentReceiptTO(
    val billId: String,
)

private data class NotifyInvestmentRedemptionTO(
    val goalRedemptionId: String,
    val status: String?,
    val fullFailure: Boolean = false,
)

private data class NotifyInvestimentCompletedTO(
    val goalId: String,
    val amount: Long,
)