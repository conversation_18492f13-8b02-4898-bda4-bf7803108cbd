package ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.AbstractBillPaymentDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategory
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryId
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryRepository
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val ENTITY_ID = "GOAL_CATEGORY"

@InvestmentGoalsModule
class GoalCategoryDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<GoalCategoryEntity>(cli, GoalCategoryEntity::class.java)

@InvestmentGoalsModule
class GoalCategoryDbRepository(
    private val client: GoalCategoryDynamoDAO,
) : GoalCategoryRepository {
    override fun save(category: GoalCategory): GoalCategory {
        val entity =
            GoalCategoryEntity().apply {
                partitionKey = ENTITY_ID
                sortKey = category.id.value
                type = category.type
                name = category.name
                imageUrl = category.imageUrl
                enabled = category.enabled
                createdAt = category.createdAt.format(dateTimeFormat)
                updatedAt = getZonedDateTime().format(dateTimeFormat)
            }
        client.save(entity)
        return category
    }

    override fun findOrNull(id: GoalCategoryId): GoalCategory? =
        client.findByPrimaryKey(ENTITY_ID, id.value)?.toGoalCategory()

    override fun findAll(): List<GoalCategory> =
        client.findByPartitionKey(ENTITY_ID).map { it.toGoalCategory() }
}

@DynamoDbBean
class GoalCategoryEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String

    @get:DynamoDbAttribute(value = "Type")
    var type: GoalCategoryType = GoalCategoryType.OTHER

    @get:DynamoDbAttribute(value = "Name")
    lateinit var name: String

    @get:DynamoDbAttribute(value = "ImageURL")
    lateinit var imageUrl: String

    @get:DynamoDbAttribute(value = "Enabled")
    var enabled: Boolean = false

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute(value = "UpdatedAt")
    lateinit var updatedAt: String
}

private fun GoalCategoryEntity.toGoalCategory(): GoalCategory =
    GoalCategory(
        id = GoalCategoryId(sortKey),
        type = type,
        name = this.name,
        imageUrl = this.imageUrl,
        enabled = this.enabled,
        createdAt = ZonedDateTime.parse(this.createdAt, dateTimeFormat),
        updatedAt = ZonedDateTime.parse(this.updatedAt, dateTimeFormat),
    )