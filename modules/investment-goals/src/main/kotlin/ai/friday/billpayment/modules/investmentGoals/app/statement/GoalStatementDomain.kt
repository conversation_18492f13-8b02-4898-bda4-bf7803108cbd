package ai.friday.billpayment.modules.investmentGoals.app.statement

import java.time.ZonedDateTime

data class GoalStatementItem(
    val id: GoalStatementItemId,
    val positionId: String? = null,
    val type: GoalStatementItemType,
    val amount: Long,
    val status: GoalStatementItemStatus,
    val timestamp: ZonedDateTime,
)

data class GoalStatementItemId(val value: String)

enum class GoalStatementItemType {
    CREDIT, DEBIT
}

enum class GoalStatementItemStatus {
    PROCESSING, DONE, FAILED, CREATED
}