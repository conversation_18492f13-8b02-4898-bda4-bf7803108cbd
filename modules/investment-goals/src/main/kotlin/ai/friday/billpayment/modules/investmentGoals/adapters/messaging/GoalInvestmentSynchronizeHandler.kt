package ai.friday.billpayment.modules.investmentGoals.adapters.messaging

import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.EventTypeFilter
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.messaging.billEventTopicArnPropertyName
import ai.friday.billpayment.adapters.messaging.parseEvent
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModuleNoTest
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalStatusService
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestment
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentRepository
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentStatus
import ai.friday.billpayment.modules.investmentGoals.app.messaging.CreateGoalInvestmentMessage
import ai.friday.billpayment.modules.investmentGoals.app.subscription.InvestmentDiscountService
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.context.annotation.Property
import java.time.Instant
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@InvestmentGoalsModuleNoTest
open class GoalInvestmentSynchronizeHandler(
    amazonSQS: SqsClient,
    amazonSNS: SnsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val billEventRepository: BillEventRepository,
    private val goalInvestmentRepository: GoalInvestmentRepository,
    private val goalStatusService: GoalStatusService,
    private val messagePublisher: MessagePublisher,
    private val investmentDiscountService: InvestmentDiscountService,

    @Property(name = "sqs.goalInvestmentSynchronize") val goalInvestmentSynchronizeQueueName: String,
    @Property(name = "sqs.createGoalInvestment") val createGoalInvestmentQueueName: String,
    @Property(name = billEventTopicArnPropertyName) private val topicArn: String,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = goalInvestmentSynchronizeQueueName,
    amazonSNS = amazonSNS,
    topicArn = topicArn,
    filter = EventTypeFilter(
        eventTypes = listOf(
            BillEventType.PAID,
            BillEventType.AMOUNT_UPDATED,
            BillEventType.IGNORED,
            BillEventType.REGISTER_UPDATED,
            BillEventType.REACTIVATED,
            BillEventType.MARKED_AS_PAID,
            BillEventType.CANCELED_MARKED_AS_PAID,
        ),
    ),
) {
    private val logger = LoggerFactory.getLogger(GoalInvestmentSynchronizeHandler::class.java)

    override fun handleMessage(message: Message): SQSHandlerResponse {
        val billEvent = parseEvent(message)

        val markers = Markers.append("billId", billEvent.billId.value)

        val investment = goalInvestmentRepository.findByBillId(billEvent.billId)
        markers.andAppend("investment", investment)

        if (investment != null) {
            val bill = billEventRepository.getBillById(billEvent.billId).getOrElse {
                logger.error(markers.andAppend("billError", it), "GoalInvestmentSynchronizeHandler")
                return SQSHandlerResponse(false)
            }
            val updatedInvestment = investment.updateWith(bill)
            goalInvestmentRepository.save(updatedInvestment)

            val paidDate = updatedInvestment.paidAt
            markers.andAppend("paidDate", paidDate)

            if (billEvent is BillPaid) {
                val completed = goalStatusService.checkAndUpdateGoalToCompleted(investment.goalId).getOrElse {
                    logger.error(markers.andAppend("goalError", it), "GoalInvestmentSynchronizeHandler")
                    return SQSHandlerResponse(false)
                }

                investmentDiscountService.checkAndApplyInvestmentDiscount(updatedInvestment)

                if (!completed && !investment.extraInstallment) {
                    messagePublisher.sendMessage(
                        createGoalInvestmentQueueName,
                        CreateGoalInvestmentMessage(goalId = investment.goalId.value, accountId = null, false),
                    )
                }
            }
        }

        logger.info(markers, "GoalInvestmentSynchronizeHandler")
        return SQSHandlerResponse(true)
    }

    override fun handleError(message: Message, e: Exception): SQSHandlerResponse {
        logger.error("GoalInvestmentSynchronizeHandler", e)
        return SQSHandlerResponse(false)
    }
}

private fun GoalInvestment.updateWith(bill: Bill): GoalInvestment {
    return this.copy(
        amount = bill.amount,
        dueDate = bill.dueDate,
        paidAt = bill.paidDate?.let { paidDate -> ZonedDateTime.ofInstant(Instant.ofEpochMilli(paidDate), brazilTimeZone) },
        status = when (bill.status) {
            BillStatus.ACTIVE, BillStatus.WAITING_APPROVAL -> GoalInvestmentStatus.CREATED

            BillStatus.PROCESSING -> GoalInvestmentStatus.PROCESSING

            BillStatus.ALREADY_PAID,
            BillStatus.IGNORED,
            BillStatus.NOT_PAYABLE,
            BillStatus.MOVED,
            BillStatus.PAID_ON_PARTNER,
            BillStatus.WAITING_BENEFICIARY_UPDATE,
            -> GoalInvestmentStatus.CANCELED

            BillStatus.PAID -> GoalInvestmentStatus.DONE
        },
    )
}