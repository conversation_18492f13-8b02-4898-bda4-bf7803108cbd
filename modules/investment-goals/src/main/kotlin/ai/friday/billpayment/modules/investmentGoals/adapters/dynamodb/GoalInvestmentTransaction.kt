package ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.PaymentOperationEntityType
import ai.friday.billpayment.adapters.dynamodb.ProductEntity
import ai.friday.billpayment.adapters.dynamodb.SettlementDataDocument
import ai.friday.billpayment.adapters.dynamodb.SettlementOperationEntityType
import ai.friday.billpayment.adapters.dynamodb.TransactionPaymentOperationConverter
import ai.friday.billpayment.adapters.dynamodb.TransactionSettlementOperationConverter
import ai.friday.billpayment.app.payment.PaymentOperation
import ai.friday.billpayment.app.payment.SettlementOperation
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndex
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomePositionId
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeProduct
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeProvider
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalRequestOperationId
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerExternalId
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerRequestStatus
import ai.friday.billpayment.modules.investmentGoals.app.payment.GoalInvestmentAuthorization
import ai.friday.billpayment.modules.investmentGoals.app.payment.GoalInvestmentSettlementOperation
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeFormatter

@InvestmentGoalsModule
class GoalInvestmentSettlementOperationConverter : TransactionSettlementOperationConverter<GoalInvestmentSettlementOperation, SettlementOperationEntityType.GoalInvestment> {
    override val domainType = GoalInvestmentSettlementOperation::class
    override val entityType = SettlementOperationEntityType.GoalInvestment::class

    override fun toDomain(element: SettlementOperationEntityType, settlementData: SettlementDataDocument): GoalInvestmentSettlementOperation {
        return (element as SettlementOperationEntityType.GoalInvestment).toDomain()
    }

    override fun toEntity(element: SettlementOperation): SettlementOperationEntityType.GoalInvestment {
        return (element as GoalInvestmentSettlementOperation).toEntity()
    }
}

@InvestmentGoalsModule
class GoalInvestmentPaymentOperationConverter : TransactionPaymentOperationConverter<GoalInvestmentAuthorization, PaymentOperationEntityType.GoalInvestment> {
    override val domainType = GoalInvestmentAuthorization::class
    override val entityType = PaymentOperationEntityType.GoalInvestment::class

    override fun toDomain(element: PaymentOperationEntityType): GoalInvestmentAuthorization {
        return (element as PaymentOperationEntityType.GoalInvestment).toDomain()
    }

    override fun toEntity(element: PaymentOperation): PaymentOperationEntityType.GoalInvestment {
        return (element as GoalInvestmentAuthorization).toEntity()
    }
}

fun SettlementOperationEntityType.GoalInvestment.toDomain(): GoalInvestmentSettlementOperation {
    return GoalInvestmentSettlementOperation(
        operationId = GoalRequestOperationId(operationId),
        investmentRequestId = InvestmentManagerExternalId(investmentRequestId),
        status = InvestmentManagerRequestStatus.valueOf(status),
        amount = amount,
        errorDescription = errorDescription,
        provider = FixedIncomeProvider.valueOf(provider),
        positionId = positionId?.let { FixedIncomePositionId(it) },
        maturityDate = maturityDate?.let { LocalDate.parse(it, DateTimeFormatter.ISO_LOCAL_DATE) },
        product = product?.let {
            FixedIncomeProduct(
                provider = FixedIncomeProvider.valueOf(it.provider),
                productId = it.productId,
                name = it.name,
                index = FixedIncomeIndex.valueOf(it.index),
                indexPercentage = FixedIncomeIndexRate(it.indexPercentage),
                minTransactionValue = it.minTransactionValue,
                maxTransactionValue = it.maxTransactionValue,
                tradeTimeLimit = LocalTime.parse(it.tradeTimeLimit, DateTimeFormatter.ISO_LOCAL_TIME),
            )
        },
    )
}

fun GoalInvestmentSettlementOperation.toEntity(): SettlementOperationEntityType.GoalInvestment {
    return SettlementOperationEntityType.GoalInvestment(
        operationId = operationId.value,
        investmentRequestId = investmentRequestId.value,
        status = status.name,
        amount = amount,
        errorDescription = errorDescription,
        provider = provider.name,
        positionId = positionId?.value,
        maturityDate = maturityDate?.format(DateTimeFormatter.ISO_LOCAL_DATE),
        product = product?.let {
            ProductEntity().apply {
                provider = it.provider.name
                productId = it.productId
                name = it.name
                index = it.index.name
                indexPercentage = it.indexPercentage.rate
                minTransactionValue = it.minTransactionValue
                maxTransactionValue = it.maxTransactionValue
                tradeTimeLimit = it.tradeTimeLimit.format(DateTimeFormatter.ISO_LOCAL_TIME)
            }
        },
    )
}

private fun PaymentOperationEntityType.GoalInvestment.toDomain(): GoalInvestmentAuthorization {
    return GoalInvestmentAuthorization(
        operationId = operationId?.let { GoalRequestOperationId(it) },
        status = InvestmentManagerRequestStatus.valueOf(status),
        amount = amount,
        errorDescription = errorDescription,
    )
}

private fun GoalInvestmentAuthorization.toEntity(): PaymentOperationEntityType.GoalInvestment {
    return PaymentOperationEntityType.GoalInvestment(
        operationId = operationId?.value,
        status = status.name,
        amount = amount,
        errorDescription = errorDescription,
    )
}