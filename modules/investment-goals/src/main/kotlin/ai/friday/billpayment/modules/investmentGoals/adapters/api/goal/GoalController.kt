package ai.friday.billpayment.modules.investmentGoals.adapters.api.goal

import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.api.StandardHttpResponses
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryId
import ai.friday.billpayment.modules.investmentGoals.app.goal.CreateGoalCommand
import ai.friday.billpayment.modules.investmentGoals.app.goal.CreateGoalError
import ai.friday.billpayment.modules.investmentGoals.app.goal.DetailedGoal
import ai.friday.billpayment.modules.investmentGoals.app.goal.DetailedGoals
import ai.friday.billpayment.modules.investmentGoals.app.goal.Goal
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalBalance
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalInstallments
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalService
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalStatus
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalStatusService
import ai.friday.billpayment.modules.investmentGoals.app.goal.InstallmentFrequency
import ai.friday.billpayment.modules.investmentGoals.app.goal.UpdateGoalCommand
import ai.friday.billpayment.modules.investmentGoals.app.goal.UpdateGoalError
import ai.friday.billpayment.modules.investmentGoals.app.investment.CreateExtraGoalInvestmentCommand
import ai.friday.billpayment.modules.investmentGoals.app.investment.CreateGoalInvestmentError
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndex
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomePosition
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeProduct
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeProvider
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentService
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductLiquidity
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionCommand
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionService
import ai.friday.billpayment.modules.investmentGoals.app.statement.GoalStatementItem
import ai.friday.billpayment.modules.investmentGoals.app.statement.GoalStatementItemStatus
import ai.friday.billpayment.modules.investmentGoals.app.statement.GoalStatementItemType
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import java.time.LocalDate
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.OWNER)
@Controller("/goal")
@Version("2")
@InvestmentGoalsModule
class GoalController(
    private val goalService: GoalService,
    private val goalStatusService: GoalStatusService,
    private val accountService: AccountService,
    private val goalRedemptionService: GoalRedemptionService,
    private val goalInvestmentService: GoalInvestmentService,
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    @Post("/create")
    fun create(authentication: Authentication, @Body body: CreateGoalRequestTO): HttpResponse<*> {
        val logName = "GoalController#create"

        val accountId = authentication.toAccountId()

        val markers = Markers.append("accountId", accountId.value)
            .andAppend("body", body)

        val account = accountService.findAccountById(accountId)

        return body.toCreateGoalCommand(account).map { command ->
            goalService.create(command).map {
                logger.info(markers, logName)
                HttpResponse.ok(it.toGoalResponseTO())
            }.getOrElse {
                markers.andAppend("error", it)
                when (it) {
                    CreateGoalError.MustUpgradeAccount -> {
                        logger.warn(markers, logName)
                        StandardHttpResponses.conflict(ResponseTO("4009", it.toString()))
                    }

                    is CreateGoalError.CouldNotEnableFixedIncomeAccount,
                    is CreateGoalError.UnableToFindProduct,
                    -> {
                        logger.error(markers, logName)
                        StandardHttpResponses.serverError(it.toString())
                    }

                    is CreateGoalError.InvalidInstallmentConfiguration -> {
                        logger.warn(markers, logName)
                        StandardHttpResponses.badRequest(4001, "${it.installmentExpirationDay} - ${it.installmentFrequency}")
                    }

                    is CreateGoalError.InvalidGoalCategory -> {
                        logger.warn(markers, logName)
                        StandardHttpResponses.badRequest(4002, it.reason)
                    }
                }
            }
        }.getOrElse {
            logger.warn(markers, logName, it)
            StandardHttpResponses.badRequest(4001, it.message.orEmpty())
        }
    }

    @Get
    fun getDetailedGoals(authentication: Authentication): HttpResponse<*> {
        val logName = "GoalController#getDetailedGoals"

        val accountId = authentication.toAccountId()

        val markers = Markers.append("accountId", accountId.value)

        return goalService.getDetailedGoals(accountId).map {
            logger.info(markers, logName)
            HttpResponse.ok(it.toDetailedGoalsTO())
        }.getOrElse {
            logger.error(markers, logName, it)
            StandardHttpResponses.serverError(it.message.orEmpty())
        }
    }

    @Post("/{goalId}")
    fun update(authentication: Authentication, @PathVariable goalId: String, @Body body: UpdateGoalRequestTO): HttpResponse<*> {
        val logName = "GoalController#update"

        val accountId = authentication.toAccountId()

        val markers = Markers.append("accountId", accountId.value)
            .andAppend("body", body)

        return goalService.update(
            command = UpdateGoalCommand(
                goalId = GoalId(goalId),
                name = body.name,
                amount = body.amount,
                endDate = body.endDate?.let { LocalDate.parse(it, dateFormat) },
                installmentFrequency = body.installmentFrequency,
                installmentExpirationDay = body.installmentExpirationDay,
                actionSource = ActionSource.Api(accountId),
            ),
        ).map {
            markers.andAppend("result", it)
            logger.info(markers, logName)
            HttpResponse.ok(it.toGoalResponseTO())
        }.getOrElse {
            when (it) {
                is UpdateGoalError.GenericException -> logger.error(markers, logName, it.exception)
                is UpdateGoalError.BalanceDifferentThanZero,
                UpdateGoalError.InvalidGoalStatus,
                is UpdateGoalError.InvestmentProcessing,
                is UpdateGoalError.ErrorCreatingInvestment,
                -> logger.error(markers.andAppend("errorMessage", it), logName)
                UpdateGoalError.GoalNotFound -> return HttpResponse.notFound<Unit>()
            }
            HttpResponse.serverError<Unit>()
        }
    }

    @Delete("/{goalId}")
    fun remove(authentication: Authentication, @PathVariable goalId: String): HttpResponse<*> {
        val logName = "GoalController#remove"

        val accountId = authentication.toAccountId()

        val markers = Markers.append("accountId", accountId.value)
            .andAppend("goalId", goalId)

        return goalStatusService.remove(
            goalId = GoalId(goalId),
            actionSource = ActionSource.Api(accountId),
        ).map {
            markers.andAppend("result", it)
            logger.info(markers, logName)
            HttpResponse.noContent<Unit>()
        }.getOrElse {
            markers.andAppend("error", it)
            when (it) {
                is UpdateGoalError.BalanceDifferentThanZero -> {
                    logger.warn(markers, logName)
                    StandardHttpResponses.conflict(
                        ResponseTO(
                            code = "4091",
                            message = it.toString(),
                        ),
                    )
                }

                is UpdateGoalError.InvestmentProcessing, UpdateGoalError.InvalidGoalStatus -> {
                    logger.warn(markers, logName)
                    StandardHttpResponses.conflict(
                        ResponseTO(
                            code = "4092",
                            message = it.toString(),
                        ),
                    )
                }

                is UpdateGoalError.ErrorCreatingInvestment -> {
                    logger.error(markers.andAppend("errorMessage", it.error), logName)
                    HttpResponse.serverError<Unit>()
                }

                is UpdateGoalError.GenericException -> {
                    logger.error(markers, logName, it.exception)
                    HttpResponse.serverError<Unit>()
                }

                UpdateGoalError.GoalNotFound -> HttpResponse.notFound<Unit>()
            }
        }
    }

    @Put("/{goalId}/pause")
    fun pause(authentication: Authentication, @PathVariable goalId: String): HttpResponse<*> {
        val logName = "GoalController#pause"

        val accountId = authentication.toAccountId()

        val markers = Markers.append("accountId", accountId.value)
            .andAppend("goalId", goalId)

        return goalStatusService.pause(
            goalId = GoalId(goalId),
            actionSource = ActionSource.Api(accountId),
        ).map {
            markers.andAppend("result", it)
            logger.info(markers, logName)
            HttpResponse.noContent<Unit>()
        }.getOrElse {
            markers.andAppend("error", it)
            when (it) {
                is UpdateGoalError.BalanceDifferentThanZero -> {
                    logger.warn(markers, logName)
                    StandardHttpResponses.conflict(
                        ResponseTO(
                            code = "4091",
                            message = it.toString(),
                        ),
                    )
                }

                is UpdateGoalError.InvestmentProcessing, UpdateGoalError.InvalidGoalStatus -> {
                    logger.warn(markers, logName)
                    StandardHttpResponses.conflict(
                        ResponseTO(
                            code = "4092",
                            message = it.toString(),
                        ),
                    )
                }

                is UpdateGoalError.ErrorCreatingInvestment -> {
                    logger.error(markers.andAppend("errorMessage", it), logName)
                    HttpResponse.serverError<Unit>()
                }

                is UpdateGoalError.GenericException -> {
                    logger.error(markers, logName, it.exception)
                    HttpResponse.serverError<Unit>()
                }

                UpdateGoalError.GoalNotFound -> HttpResponse.notFound<Unit>()
            }
        }
    }

    @Put("/{goalId}/resume")
    fun resume(authentication: Authentication, @PathVariable goalId: String): HttpResponse<*> {
        val logName = "GoalController#resume"

        val accountId = authentication.toAccountId()

        val markers = Markers.append("accountId", accountId.value)
            .andAppend("goalId", goalId)

        return goalStatusService.resume(
            goalId = GoalId(goalId),
            actionSource = ActionSource.Api(accountId),
        ).map {
            markers.andAppend("result", it)
            logger.info(markers, logName)
            HttpResponse.noContent<Unit>()
        }.getOrElse {
            markers.andAppend("error", it)
            logger.error(markers, logName)
            HttpResponse.serverError()
        }
    }

    @Get("/{goalId}")
    fun getDetailedGoal(authentication: Authentication, @PathVariable goalId: String): HttpResponse<*> {
        val logName = "GoalController#getDetailedGoal"

        val accountId = authentication.toAccountId()

        val markers = Markers.append("accountId", accountId.value)
            .andAppend("goalId", goalId)

        return goalService.getDetailedGoal(accountId, GoalId(goalId)).map {
            logger.info(markers, logName)
            HttpResponse.ok(it.toDetailedGoalTO())
        }.getOrElse {
            logger.error(markers, logName, it)
            StandardHttpResponses.serverError(it.message.orEmpty())
        }
    }

    @Post("/{goalId}/extraInstallment")
    fun createExtraInstallment(authentication: Authentication, @PathVariable goalId: String, @Body body: CreateExtraInstallmentRequestTO): HttpResponse<*> {
        val logName = "GoalController#createExtraInstallment"

        val accountId = authentication.toAccountId()

        val markers = Markers.append("accountId", accountId.value)
            .andAppend("goalId", goalId)

        goalInvestmentService.create(
            CreateExtraGoalInvestmentCommand(
                goalId = GoalId(goalId),
                extraInstallmentAmount = body.extraInstallmentAmount,
                accountId = accountId,
            ),
        ).map {
            logger.info(markers, logName)
            return HttpResponse.noContent<Unit>()
        }.getOrElse { error ->
            when (error) {
                is CreateGoalInvestmentError.ExtraInvestmentActive -> {
                    logger.warn(markers.andAppend("activeInvestment", error.goalInvestment), logName)
                    return HttpResponse.status<Unit>(HttpStatus.CONFLICT)
                }

                else -> {
                    logger.error(markers, logName, error)
                    return HttpResponse.serverError<Unit>()
                }
            }
        }
    }

    @Get("/{goalId}/availableToRedeem")
    fun availableToRedeem(authentication: Authentication, @PathVariable goalId: String): HttpResponse<*> {
        val logName = "GoalController#availableToRedeem"

        val accountId = authentication.toAccountId()

        val markers = Markers.append("accountId", accountId.value)
            .andAppend("goalId", goalId)

        return goalRedemptionService.availableToRedeem(accountId, GoalId(goalId)).map {
            logger.info(markers.andAppend("availableToRedeem", it), logName)
            HttpResponse.ok(AvailableToRedeemResponseTO(it))
        }.getOrElse {
            logger.error(markers, logName, it)
            StandardHttpResponses.serverError(it.message.orEmpty())
        }
    }

    @Get("/{goalId}/penalties")
    fun getPenalties(authentication: Authentication, @PathVariable goalId: String): HttpResponse<*> {
        val logName = "GoalController#getPenalties"

        val accountId = authentication.toAccountId()

        val markers = Markers.append("accountId", accountId.value)
            .andAppend("goalId", goalId)

        val response = goalService.calculateGoalPenaltyRates(GoalId(goalId), accountId).map { penalties ->
            penalties.map { penalty ->
                GetGoalPenaltiesResponseTO(
                    limitDate = penalty.limitDate.format(dateFormat),
                    penaltyRate = penalty.penaltyRate.rate,
                )
            }
        }.getOrElse {
            logger.error(markers, logName, it)
            return StandardHttpResponses.serverError(it.message.orEmpty())
        }
        logger.info(markers.andAppend("response", response), logName)
        return HttpResponse.ok(response)
    }

    @Put("/quoteRedemption")
    fun quoteRedemption(authentication: Authentication, @Body body: QuoteRedemptionRequestTO): HttpResponse<*> {
        val logName = "GoalController#quoteRedemption"

        val accountId = authentication.toAccountId()

        val markers = Markers.append("accountId", accountId.value)
            .andAppend("body", body)

        return goalRedemptionService.quoteRedemption(accountId, GoalId(body.goalId), body.amount).map {
            val response = QuoteRedemptionResponseTO(
                grossValue = it.grossValue,
                netValue = it.netValue,
                taxValue = it.taxValue,
                penaltyValue = it.penaltyValue,
            )
            logger.info(markers.andAppend("response", response), logName)
            return HttpResponse.ok(response)
        }.getOrElse {
            logger.error(markers, logName, it)
            StandardHttpResponses.serverError(it.message.orEmpty())
        }
    }

    @Put("/redemption")
    fun requestRedemption(authentication: Authentication, @Body body: QuoteRedemptionRequestTO): HttpResponse<*> {
        val logName = "GoalController#requestRedemption"

        val accountId = authentication.toAccountId()

        val goalRedemptionCommand = GoalRedemptionCommand(
            goalId = GoalId(body.goalId),
            netAmount = body.amount,
            actionSource = ActionSource.Api(accountId),
        )

        val markers = Markers.append("accountId", accountId.value)
            .andAppend("body", body)

        return goalRedemptionService.requestRedemption(goalRedemptionCommand).map {
            val response = ExecuteRedemptionResponseTO(
                transactionId = it.value,
            )
            logger.info(markers.andAppend("response", response), logName)
            return HttpResponse.created(response)
        }.getOrElse {
            logger.error(markers, logName, it)
            StandardHttpResponses.serverError(it.message.orEmpty())
        }
    }

    private fun CreateGoalRequestTO.toCreateGoalCommand(account: Account) = try {
        CreateGoalCommand(
            accountId = account.accountId,
            categoryId = GoalCategoryId(value = categoryId),
            name = name,
            amount = amount,
            endDate = LocalDate.parse(endDate, dateFormat),

            installmentFrequency = installmentFrequency,
            installmentAmount = installmentAmount,
            installmentExpirationDay = installmentExpirationDay,

            imageUrl = imageUrl,
            liquidity = liquidity,
            initialInvestmentAmount = initialInvestmentAmount,
        ).right()
    } catch (e: Exception) {
        e.left()
    }

    private fun Goal.toGoalResponseTO() = GoalResponseTO(
        id = id.value,
        accountId = accountId.value,
        walletId = walletId.value,
        name = name,
        categoryId = categoryId.value,
        imageUrl = imageUrl,
        amount = amount,
        endDate = endDate.format(dateFormat),
        liquidity = liquidity,
        installments = installments.toGoalInstallmentsTO(),
        productId = productId.value,
        status = when (status) {
            GoalStatus.PAUSED -> "CANCELED"
            else -> status.name
        },
        createdAt = createdAt.format(dateTimeFormat),
    )

    private fun DetailedGoal.toDetailedGoalTO() = DetailedGoalTO(
        goal = goal.toGoalResponseTO(),
        balance = balance?.toGoalBalanceTO(),
        statementItems = statementItems.map {
            it.toGoalStatementItemTO()
        },
        positions = positions?.map {
            it.toFixedIncomePositionItemTO()
        },
    )

    private fun DetailedGoals.toDetailedGoalsTO() = DetailedGoalsTO(
        totalBalance = totalBalance?.toGoalBalanceTO(),
        items = items.map { it.toDetailedGoalTO() },
    )

    private fun GoalInstallments.toGoalInstallmentsTO() = GoalInstallmentsTO(
        frequency = frequency,
        amount = amount,
        expirationDay = expirationDay,
    )

    private fun GoalBalance.toGoalBalanceTO() = GoalBalanceTO(
        netAmount = netValue,
        grossAmount = grossValue,
        taxAmount = irValue + iofValue,
        investedAmount = investedValue,
        earningAmount = earningValue,
        savingsReferenceAmount = savingsReferenceValue,
    )

    private fun GoalStatementItem.toGoalStatementItemTO() = GoalStatementItemTO(
        id = id.value,
        type = type,
        amount = amount,
        date = timestamp.format(dateFormat),
        status = status,
    )
}

private fun FixedIncomePosition.toFixedIncomePositionItemTO() = FixedIncomePositionItemTO(
    provider = provider,
    product = product?.toFixedIncomeProductTO(),
    positionId = positionId,
    operationDate = operationDate.format(dateFormat),
    liquidityDueDate = liquidityDueDate.format(dateFormat),
    maturityDate = maturityDate.format(dateFormat),
    unitPrice = unitPrice,
    quantity = quantity,
    netValue = netValue,
    grossValue = grossValue,
    name = name,
    investedValue = investedValue,
    irValue = irValue,
    iofValue = iofValue,
    earningValue = earningValue,
)

private fun FixedIncomeProduct.toFixedIncomeProductTO() = FixedIncomeProductTO(
    name = name,
    index = index,
    indexPercentage = indexPercentage.rate,
)

data class GetGoalPenaltiesResponseTO(
    val limitDate: String,
    val penaltyRate: Int,
)

data class CreateGoalRequestTO(
    val name: String,
    val categoryId: String,
    val imageUrl: String,
    val amount: Long,
    val endDate: String,
    val liquidity: GoalProductLiquidity,
    val installments: Int,
    val installmentFrequency: InstallmentFrequency,
    val installmentAmount: Long,
    val installmentExpirationDay: String,
    val initialInvestmentAmount: Long? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class UpdateGoalRequestTO(
    val name: String?,
    val amount: Long?,
    val endDate: String?,
    val installmentFrequency: InstallmentFrequency?,
    val installmentExpirationDay: String?,
)

data class GoalResponseTO(
    val id: String,
    val accountId: String,
    val walletId: String,
    val name: String,
    val categoryId: String,
    val imageUrl: String? = null,
    val amount: Long,
    val endDate: String,
    val liquidity: GoalProductLiquidity,
    val installments: GoalInstallmentsTO,
    val productId: String,
    val status: String,
    val createdAt: String,
)

data class DetailedGoalTO(
    val goal: GoalResponseTO,
    val balance: GoalBalanceTO?,
    val statementItems: List<GoalStatementItemTO>,
    val positions: List<FixedIncomePositionItemTO>?,
)

data class FixedIncomeProductTO(
    val name: String,
    val index: FixedIncomeIndex,
    val indexPercentage: Int,
)

data class FixedIncomePositionItemTO(
    val provider: FixedIncomeProvider,
    val product: FixedIncomeProductTO?,
    val positionId: String,

    val operationDate: String,
    val liquidityDueDate: String,
    val maturityDate: String,
    val unitPrice: Long,
    val quantity: Long,
    val netValue: Long,
    val grossValue: Long,
    val name: String,

    val investedValue: Long,
    val irValue: Long,
    val iofValue: Long,
    val earningValue: Long,
)

data class DetailedGoalsTO(
    val totalBalance: GoalBalanceTO?,
    val items: List<DetailedGoalTO>,
)

data class GoalInstallmentsTO(
    val frequency: InstallmentFrequency,
    val amount: Long,
    val expirationDay: String,
)

data class GoalBalanceTO(
    val netAmount: Long,
    val grossAmount: Long,
    val taxAmount: Long,
    val savingsReferenceAmount: Long?,
    val investedAmount: Long,
    val earningAmount: Long,
    val redeemedAmount: Long = 0, // TODO - remover
)

data class GoalStatementItemTO(
    val id: String,
    val type: GoalStatementItemType,
    val amount: Long,
    val date: String,
    val status: GoalStatementItemStatus,
)

data class AvailableToRedeemResponseTO(
    val netValue: Long,
)

data class QuoteRedemptionRequestTO(
    val goalId: String,
    val amount: Long,
)

data class QuoteRedemptionResponseTO(
    val grossValue: Long,
    val netValue: Long,
    val taxValue: Long,
    val penaltyValue: Long,
)

data class ExecuteRedemptionResponseTO(
    val transactionId: String,
)

data class CreateExtraInstallmentRequestTO(
    val extraInstallmentAmount: Long,
)