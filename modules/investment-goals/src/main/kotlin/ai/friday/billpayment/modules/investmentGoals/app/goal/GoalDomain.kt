package ai.friday.billpayment.modules.investmentGoals.app.goal

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource.WalletActionSource
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryId
import ai.friday.billpayment.modules.investmentGoals.app.investment.CreateGoalInvestmentError
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomePosition
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentId
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductId
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductLiquidity
import ai.friday.billpayment.modules.investmentGoals.app.statement.GoalStatementItem
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.ZonedDateTime

interface GoalRepository {
    fun save(goal: Goal): Goal

    fun findByAccountId(accountId: AccountId): List<Goal>
    fun findByWalletId(walletId: WalletId): List<Goal>
    fun findByProductId(productId: GoalProductId): List<Goal>

    fun findOrNull(goalId: GoalId): Goal?
    fun find(goalId: GoalId): Either<GoalNotFoundException, Goal>
    fun findGoalsToOptimize(optimizedSince: LocalDate): List<Goal>
    fun updateLastKnowNetBalance(goalId: GoalId, netAmount: Long): Either<GoalNotFoundException, Goal>
    fun findGoalsCloseToCompletion(): List<Goal>
}

enum class GoalStatus {
    ACTIVE, PAUSED, REMOVED, COMPLETED
}

data class Goal(
    val id: GoalId,
    val accountId: AccountId,
    val walletId: WalletId,
    val categoryId: GoalCategoryId,
    val name: String,

    val status: GoalStatus,

    val amount: Long,
    val endDate: LocalDate,
    val liquidity: GoalProductLiquidity,
    val installments: GoalInstallments,
    val productId: GoalProductId,

    val imageUrl: String,
    val createdAt: ZonedDateTime = getZonedDateTime(),
    val updatedAt: ZonedDateTime = getZonedDateTime(),

    val installmentInitialAmount: Long,
    val installmentOptimizedAmount: Long? = null,
    val lastValueOptimizationAt: ZonedDateTime? = null,

    val lastKnownNetBalanceAmount: Long,
    val lastKnownNetBalanceAt: ZonedDateTime,
) {
    val totalInstallments = GoalInstallments.calculateTotalInstallments(createdAt, installments.frequency, installments.expirationDay, endDate)
    val lastKnownNetBalancePerc: BigDecimal = BigDecimal(lastKnownNetBalanceAmount * 100).divide(amount.toBigDecimal(), 2, RoundingMode.HALF_UP)

    fun canCreateExtraInvestment(): Boolean {
        return when (status) {
            GoalStatus.ACTIVE,
            GoalStatus.PAUSED,
            -> true

            GoalStatus.REMOVED,
            GoalStatus.COMPLETED,
            -> false
        }
    }

    fun canCreateRegularInvestment(): Boolean {
        if (endDate <= getLocalDate()) {
            return false
        }
        return when (status) {
            GoalStatus.ACTIVE -> true
            GoalStatus.PAUSED,
            GoalStatus.REMOVED,
            GoalStatus.COMPLETED,
            -> false
        }
    }

    fun isOptimizeable(): Boolean {
        return canCreateRegularInvestment()
    }

    fun canBeUpdated(): Boolean {
        return when (status) {
            GoalStatus.ACTIVE, GoalStatus.PAUSED -> true
            GoalStatus.COMPLETED, GoalStatus.REMOVED -> false
        }
    }
}

data class DetailedGoals(
    val totalBalance: GoalBalance?,
    val items: List<DetailedGoal>,
)

data class DetailedGoal(
    val goal: Goal,
    val balance: GoalBalance?,
    val statementItems: List<GoalStatementItem>,
    val positions: List<FixedIncomePosition>?,
)

data class GoalBalance(
    val netValue: Long,
    val grossValue: Long,
    val irValue: Long,
    val iofValue: Long,
    val earningValue: Long,
    val savingsReferenceValue: Long?,
    val investedValue: Long,
) {
    operator fun plus(otherBalance: GoalBalance) = GoalBalance(
        netValue = netValue + otherBalance.netValue,
        grossValue = grossValue + otherBalance.grossValue,
        irValue = irValue + otherBalance.irValue,
        iofValue = iofValue + otherBalance.iofValue,
        earningValue = earningValue + otherBalance.earningValue,
        savingsReferenceValue = if (savingsReferenceValue != null && otherBalance.savingsReferenceValue != null) {
            savingsReferenceValue + otherBalance.savingsReferenceValue
        } else {
            null
        },
        investedValue = investedValue + otherBalance.investedValue,
    )
}

sealed interface GoalInstallments {
    val frequency: InstallmentFrequency
    val amount: Long
    val expirationDay: String

    companion object {
        fun buildGoalInstallment(
            frequency: InstallmentFrequency,
            amount: Long,
            expirationDay: String,
        ) = when (frequency) {
            InstallmentFrequency.WEEKLY -> WeeklyInstallments(
                amount = amount,
                dayOfWeek = WeeklyInstallmentDay.valueOf(expirationDay),
            )

            InstallmentFrequency.MONTHLY -> MonthlyInstallments(
                amount = amount,
                dayOfMonth = expirationDay.toInt(),
            )
        }

        fun calcNextInstallment(goalCreatedAt: LocalDate, lastDueDate: LocalDate?, installments: GoalInstallments): Pair<LocalDate, Int> {
            var firstDueDate = goalCreatedAt
            while (!installments.isSameDayOf(firstDueDate)) {
                firstDueDate = firstDueDate.plusDays(1)
            }

            val yesterday = getLocalDate().minusDays(1)
            val validLastDueDate = if (lastDueDate == null || lastDueDate < yesterday) yesterday else lastDueDate

            var nextDueDate: LocalDate = firstDueDate
            var installmentNo = 1

            while (nextDueDate <= validLastDueDate) {
                nextDueDate = when (installments) {
                    is MonthlyInstallments -> nextDueDate.plusMonths(1)
                    is WeeklyInstallments -> nextDueDate.plusWeeks(1)
                }
                installmentNo++
            }
            return nextDueDate to installmentNo
        }

        fun calculateTotalInstallments(goalCreatedAt: ZonedDateTime, installmentFrequency: InstallmentFrequency, installmentExpirationDay: String, endDate: LocalDate): Int {
            val installments = buildGoalInstallment(installmentFrequency, 0, installmentExpirationDay)
            val (firstInstallment, firstInstallmentNo) = calcNextInstallment(goalCreatedAt.toLocalDate(), null, installments)
            return calculateTotalInstallments(firstInstallment, firstInstallmentNo, installments, endDate)
        }

        fun calculateInstallmentNumber(goal: Goal, dueDate: LocalDate): Int {
            return calculateInstallmentNumber(goal.createdAt.toLocalDate(), goal.installments.frequency, goal.installments.expirationDay, dueDate)
        }

        fun calculateInstallmentNumber(goalCreatedAt: LocalDate, installmentFrequency: InstallmentFrequency, installmentExpirationDay: String, dueDate: LocalDate): Int {
            val installments = buildGoalInstallment(installmentFrequency, 0, installmentExpirationDay)
            val (_, currenInstallmentNo) = calcNextInstallment(goalCreatedAt, dueDate.minusDays(1), installments)
            return currenInstallmentNo
        }

        fun calculateTotalInstallments(knownInstallmentDueDate: LocalDate, knownInstallmentNo: Int, frequency: GoalInstallments, endDate: LocalDate): Int {
            var nextDueDate: LocalDate = knownInstallmentDueDate
            var installmentNo = knownInstallmentNo

            do {
                nextDueDate = when (frequency) {
                    is MonthlyInstallments -> nextDueDate.plusMonths(1)
                    is WeeklyInstallments -> nextDueDate.plusWeeks(1)
                }
                installmentNo++
            } while (nextDueDate < endDate)

            return installmentNo - 1
        }
    }
}

private fun GoalInstallments.isSameDayOf(date: LocalDate) = when (this) {
    is MonthlyInstallments -> date.dayOfMonth == dayOfMonth
    is WeeklyInstallments -> date.dayOfWeek == dayOfWeek.dayOfWeek
}

data class WeeklyInstallments(
    override val amount: Long,
    val dayOfWeek: WeeklyInstallmentDay,
) : GoalInstallments {
    override val frequency = InstallmentFrequency.WEEKLY
    override val expirationDay = dayOfWeek.name
}

data class MonthlyInstallments(
    override val amount: Long,
    val dayOfMonth: Int,
) : GoalInstallments {
    override val frequency = InstallmentFrequency.MONTHLY
    override val expirationDay = dayOfMonth.toString()
}

enum class InstallmentFrequency {
    WEEKLY, MONTHLY
}

enum class WeeklyInstallmentDay(val dayOfWeek: DayOfWeek) {
    MONDAY(DayOfWeek.MONDAY), TUESDAY(DayOfWeek.TUESDAY), WEDNESDAY(DayOfWeek.WEDNESDAY), THURSDAY(DayOfWeek.THURSDAY), FRIDAY(DayOfWeek.FRIDAY)
}

data class CreateGoalCommand(
    val accountId: AccountId,
    val categoryId: GoalCategoryId,
    val name: String,

    val amount: Long,
    val endDate: LocalDate,

    val liquidity: GoalProductLiquidity,

    val installmentFrequency: InstallmentFrequency,
    val installmentAmount: Long,
    val installmentExpirationDay: String,

    val initialInvestmentAmount: Long? = null,

    val imageUrl: String,
)

data class GoalNotFoundException(
    val goalId: GoalId,
) : Exception("goal not found: ${goalId.value}")

sealed class UpdateGoalError : PrintableSealedClassV2() {
    data class BalanceDifferentThanZero(val balance: Long) : UpdateGoalError()
    data class InvestmentProcessing(val goalInvestmentId: GoalInvestmentId) : UpdateGoalError()
    data class GenericException(val exception: Exception) : UpdateGoalError()
    data object InvalidGoalStatus : UpdateGoalError()
    data object GoalNotFound : UpdateGoalError()
    data class ErrorCreatingInvestment(val error: CreateGoalInvestmentError) : UpdateGoalError()
}

data class UpdateGoalCommand(
    val goalId: GoalId,
    val name: String?,
    val amount: Long?,
    val endDate: LocalDate?,
    val installmentFrequency: InstallmentFrequency?,
    val installmentExpirationDay: String?,
    val actionSource: WalletActionSource,
)