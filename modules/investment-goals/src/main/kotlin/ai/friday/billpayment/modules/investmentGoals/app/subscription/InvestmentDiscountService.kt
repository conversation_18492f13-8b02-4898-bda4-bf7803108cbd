package ai.friday.billpayment.modules.investmentGoals.app.subscription

import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.UserEvent
import ai.friday.billpayment.app.integrations.UserEventService
import ai.friday.billpayment.app.integrations.UserEventSource
import ai.friday.billpayment.app.subscription.Subscription
import ai.friday.billpayment.app.subscription.SubscriptionService
import ai.friday.billpayment.app.subscription.SubscritionDiscountInterface
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalRepository
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestment
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentService
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentStatus
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import java.time.LocalDate
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

private const val MINIMUM_INVESTMENT_AMOUNT = 50_00L
private val DISCOUNT_REASON = AccountGroup.INVESTMENT_CAMPAIGN.name

@InvestmentGoalsModule
class InvestmentDiscountService(
    private val goalInvestmentService: GoalInvestmentService,
    private val goalRepository: GoalRepository,
    private val subscriptionService: SubscriptionService,
    private val walletService: WalletService,
    private val userEventService: UserEventService,
    private val accountService: AccountService,
    private val notificationAdapter: NotificationAdapter,
) : SubscritionDiscountInterface {
    private val logger = LoggerFactory.getLogger(InvestmentDiscountService::class.java)

    fun checkAndApplyInvestmentDiscount(goalInvestment: GoalInvestment): Boolean {
        if (goalInvestment.amount < MINIMUM_INVESTMENT_AMOUNT || goalInvestment.paidAt == null || goalInvestment.status != GoalInvestmentStatus.DONE) {
            return false
        }
        val goal = goalRepository.find(goalInvestment.goalId).getOrElse { return false }
        val wallet = walletService.findWallet(goal.walletId)

        val accountId = wallet.founder.accountId
        val account = accountService.findAccountById(accountId)

        if (!account.hasGroup(AccountGroup.INVESTMENT_CAMPAIGN)) {
            return false
        }

        val subscription = subscriptionService.findOrNull(accountId) ?: return false

        val limitDate = if (getLocalDate().dayOfMonth < subscription.nextEffectiveDueDate.dayOfMonth) {
            getLocalDate().withDayOfMonth(subscription.nextEffectiveDueDate.dayOfMonth)
        } else {
            getLocalDate().plusMonths(1).withDayOfMonth(subscription.nextEffectiveDueDate.dayOfMonth)
        }
        if (subscription.nextEffectiveDueDate <= limitDate) {
            doApplyInvestmentDiscount(accountId, goalInvestment)
            notificationAdapter.notifySubscriptionGrantedByInvestment(
                accountId = accountId,
                dueDate = subscription.nextEffectiveDueDate,
            )
            return true
        }
        return false
    }

    override fun checkAndApplyFirstInstallmentDiscount(account: Account, newSubscription: Subscription): Boolean {
        if (!account.hasGroup(AccountGroup.INVESTMENT_CAMPAIGN)) {
            return false
        }

        val anyValidIntervalInvestment = pickAnyValidInvestmentAfter(account.accountId, getLocalDate().minusMonths(1).withDayOfMonth(1)) ?: return false

        doApplyInvestmentDiscount(account.accountId, anyValidIntervalInvestment)
        notificationAdapter.notifySubscriptionGrantedByInvestment(
            accountId = account.accountId,
            dueDate = newSubscription.nextEffectiveDueDate,
        )
        return true
    }

    private fun pickAnyValidInvestmentAfter(accountId: AccountId, limitDate: LocalDate): GoalInvestment? {
        val goals = goalRepository.findByAccountId(accountId)
        return goals.firstNotNullOfOrNull { goal ->
            val investments = goalInvestmentService.findAll(goal.id)
            investments.firstOrNull {
                it.paidAt != null &&
                    it.status == GoalInvestmentStatus.DONE &&
                    it.paidAt.toLocalDate() >= limitDate &&
                    it.amount >= MINIMUM_INVESTMENT_AMOUNT
            }
        }
    }

    private fun doApplyInvestmentDiscount(accountId: AccountId, goalInvestment: GoalInvestment) {
        subscriptionService.ignoreSubscriptionFeeComingMonths(accountId, 1, DISCOUNT_REASON)
        logger.info(append("accountId", accountId.value).andAppend("context", "Investment discount applied"), "InvestmentDiscountService#doApplyInvestmentDiscount")
        userEventService.save(
            UserEvent(
                accountId = accountId,
                event = AccountGroup.INVESTMENT_CAMPAIGN.name,
                metadata = mapOf(
                    "investmentId" to goalInvestment.id.toString(),
                    "investmentAmount" to goalInvestment.amount.toString(),
                    "goalId" to goalInvestment.goalId.toString(),
                    "billId" to goalInvestment.billId.toString(),
                ),
                source = UserEventSource.BILL_PAYMENT,
            ),
        )
    }
}