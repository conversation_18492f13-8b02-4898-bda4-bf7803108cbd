package ai.friday.billpayment.modules.investmentGoals.app.bill

import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateInvestmentRequest
import ai.friday.billpayment.app.bill.calculateEffectiveDate
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalRepository
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalTime
import io.micronaut.context.annotation.Property
import java.time.LocalTime

@InvestmentGoalsModule
class CreateInvestmentBillService(
    private val categoryService: PFMWalletCategoryService,
    private val billEventPublisher: BillEventPublisher,
    private val goalRepository: GoalRepository,
    @Property(name = "investments.start-at") investmentsStartAtString: String,
    @Property(name = "investments.end-at") investmentsEndAtString: String,
) : CreateInvestmentBillServiceInterface {

    private val investmentsStartAt = LocalTime.parse(investmentsStartAtString)
    private val investmentsEndAt = LocalTime.parse(investmentsEndAtString)

    override fun createInvestment(
        request: CreateInvestmentRequest,
        dryRun: Boolean,
    ): CreateBillResult {
        val category: WalletBillCategory? = request.categoryId.let { categoryService.findCategory(request.walletId, it) }

        val investmentAdded = if (dryRun) {
            buildInvestmentAdded(request, category)
        } else {
            buildInvestmentAdded(request, category).also {
                billEventPublisher.publish(Bill.build(), it)
            }
        }

        val bill = Bill.build(investmentAdded)

        return CreateBillResult.SUCCESS(
            bill = bill,
            warningCode = null,
        )
    }

    private fun buildInvestmentAdded(
        request: CreateInvestmentRequest,
        walletBillCategory: WalletBillCategory? = null,
    ): BillAdded {
        val effectiveDueDate = calculateEffectiveDate(
            dueDate = if (getLocalTime() < investmentsEndAt.minusMinutes(10)) {
                request.dueDate
            } else {
                request.dueDate.plusDays(1)
            },
            billType = BillType.INVESTMENT,
            actionSource = request.source,
        )

        return BillAdded(
            billId = request.id,
            assignor = goalRepository.find(request.goalId).map { it.name }.getOrNull(),
            created = request.createdOn.toInstant().toEpochMilli(),
            walletId = request.walletId,
            description = request.description,
            dueDate = request.dueDate,
            amount = request.amount,
            billType = BillType.INVESTMENT,
            paymentLimitTime = investmentsEndAt,
            actionSource = request.source,
            effectiveDueDate = effectiveDueDate,
            categoryId = walletBillCategory?.categoryId ?: request.categoryId,
            goalId = request.goalId,
        )
    }
}

interface CreateInvestmentBillServiceInterface {
    fun createInvestment(
        request: CreateInvestmentRequest,
        dryRun: Boolean,
    ): CreateBillResult
}