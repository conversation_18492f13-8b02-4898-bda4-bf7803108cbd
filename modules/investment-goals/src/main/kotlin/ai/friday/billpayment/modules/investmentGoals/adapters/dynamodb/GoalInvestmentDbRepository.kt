package ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.AbstractBillPaymentDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.GlobalSecondaryIndexNames
import ai.friday.billpayment.adapters.dynamodb.INDEX_1
import ai.friday.billpayment.adapters.dynamodb.INDEX_2
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestment
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentId
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentRepository
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentStatus
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.date.isoDateTimeFormat
import java.time.LocalDate
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val ENTITY_ID = "GOAL_INVESTMENT"

@InvestmentGoalsModule
class GoalInvestmentDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<GoalInvestmentEntity>(cli, GoalInvestmentEntity::class.java)

@InvestmentGoalsModule
class GoalInvestmentDbRepository(
    private val client: GoalInvestmentDynamoDAO,
) : GoalInvestmentRepository {
    override fun save(goalInvestment: GoalInvestment) {
        val entity =
            GoalInvestmentEntity().apply {
                partitionKey = ENTITY_ID
                sortKey = goalInvestment.id.value
                gSIndex1PartitionKey = goalInvestment.goalId.value
                gSIndex1SortKey = "$ENTITY_ID#${goalInvestment.dueDate.format(dateFormat)}"
                gSIndex2PartitionKey = goalInvestment.billId.value
                gSIndex2SortKey = "$ENTITY_ID#${goalInvestment.dueDate.format(dateFormat)}"
                goalId = goalInvestment.goalId.value
                billId = goalInvestment.billId.value
                dueDate = goalInvestment.dueDate.format(dateFormat)
                paidAt = goalInvestment.paidAt?.format(isoDateTimeFormat)
                extraInstallment = goalInvestment.extraInstallment
                status = goalInvestment.status.name
                amount = goalInvestment.amount
                createdAt = goalInvestment.createdAt.format(dateTimeFormat)
                updatedAt = getZonedDateTime().format(dateTimeFormat)
            }
        client.save(entity)
    }

    override fun findByGoalId(goalId: GoalId): List<GoalInvestment> =
        client.findBeginsWithOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            goalId.value,
            ENTITY_ID,
        ).map { it.toGoalInvestment() }

    override fun findByBillId(billId: BillId): GoalInvestment? {
        val entities = client.findBeginsWithOnIndex(
            GlobalSecondaryIndexNames.GSIndex2,
            billId.value,
            ENTITY_ID,
        )

        if (entities.size > 1) {
            throw IllegalStateException("bill ${billId.value} is associated to more multiple investments: ${entities.map{ it.sortKey }}")
        }

        return entities.firstOrNull()?.toGoalInvestment()
    }
}

@DynamoDbBean
class GoalInvestmentEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1SortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    lateinit var gSIndex2PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    lateinit var gSIndex2SortKey: String

    @get:DynamoDbAttribute(value = "GoalId")
    lateinit var goalId: String

    @get:DynamoDbAttribute(value = "BillId")
    lateinit var billId: String

    @get:DynamoDbAttribute(value = "DueDate")
    lateinit var dueDate: String

    @get:DynamoDbAttribute(value = "PaidAt")
    var paidAt: String? = null

    @get:DynamoDbAttribute(value = "Amount")
    var amount: Long? = null

    @get:DynamoDbAttribute(value = "ExtraInstallment")
    var extraInstallment: Boolean = false

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: String

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute(value = "UpdatedAt")
    lateinit var updatedAt: String
}

private fun GoalInvestmentEntity.toGoalInvestment() = GoalInvestment(
    id = GoalInvestmentId(value = sortKey),
    goalId = GoalId(value = goalId),
    billId = BillId(value = billId),
    dueDate = LocalDate.parse(dueDate, dateFormat),
    paidAt = paidAt?.let { ZonedDateTime.parse(it, isoDateTimeFormat) },
    extraInstallment = extraInstallment,
    amount = amount ?: 0,
    createdAt = ZonedDateTime.parse(createdAt, dateTimeFormat),
    updatedAt = ZonedDateTime.parse(updatedAt, dateTimeFormat),
    status = GoalInvestmentStatus.valueOf(status),
)