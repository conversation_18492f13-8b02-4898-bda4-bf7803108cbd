package ai.friday.billpayment.modules.investmentGoals.adapters.api.product

import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.log
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndex
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProduct
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductId
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductLiquidity
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductRisk
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductService
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductType
import ai.friday.billpayment.modules.investmentGoals.app.product.SaveGoalProductCommand
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.Patch
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(Role.Code.BACKOFFICE)
@Controller("/backoffice/goal/product")
@InvestmentGoalsModule
class BackofficeGoalProductController(
    private val goalProductService: GoalProductService,
) {
    private val logger = LoggerFactory.getLogger(BackofficeGoalProductController::class.java)

    @Get
    fun getAll(): HttpResponse<*> {
        val logName = "BackofficeGoalProductController#getAll"

        val products = goalProductService.findAll()

        logger.info(log("products" to products), logName)
        return HttpResponse.ok(products.map { it.toGoalProductTO() })
    }

    @Get("/checkTermIntegrity")
    fun checkTermIntegrity(): HttpResponse<*> {
        val logName = "BackofficeGoalProductController#checkTermIntegrity"

        return goalProductService.checkTermIntegrity().map {
            logger.info(logName)
            HttpResponse.ok<Unit>()
        }.getOrElse {
            logger.warn(append("error", it), logName)
            HttpResponse.status<Unit?>(HttpStatus.CONFLICT).body(it)
        }
    }

    @Post
    fun create(
        @Body body: SaveProductRequestTO,
    ): HttpResponse<*> {
        val logName = "BackofficeGoalProductController#create"
        val markers = append("body", body)

        return goalProductService.create(
            command = body.toCommand(),
        ).map {
            logger.info(markers.andAppend("product", it), logName)
            HttpResponse.ok(it.toGoalProductTO())
        }.getOrElse {
            logger.warn(markers, logName, it)
            HttpResponse.badRequest(it)
        }
    }

    @Put("/{productId}")
    fun update(
        @PathVariable productId: String,
        @Body body: SaveProductRequestTO,
    ): HttpResponse<*> {
        val logName = "BackofficeGoalProductController#update"
        val markers = append("body", body)
            .andAppend("productId", productId)

        return goalProductService.update(
            productId = GoalProductId(productId),
            command = body.toCommand(),
        ).map {
            logger.info(markers.andAppend("product", it), logName)
            HttpResponse.ok(it.toGoalProductTO())
        }.getOrElse {
            logger.warn(markers, logName, it)
            HttpResponse.badRequest(it)
        }
    }

    @Delete("/{productId}")
    fun disable(
        @PathVariable productId: String,
    ): HttpResponse<*> {
        val logName = "BackofficeGoalProductController#disable"
        val markers = append("productId", productId)

        return goalProductService.setStatus(
            productId = GoalProductId(productId),
            enabled = false,
        ).map {
            logger.info(markers.andAppend("product", it), logName)
            HttpResponse.ok(it.toGoalProductTO())
        }.getOrElse {
            logger.warn(markers, logName, it)
            HttpResponse.badRequest(it)
        }
    }

    @Patch("/{productId}")
    fun enable(
        @PathVariable productId: String,
    ): HttpResponse<*> {
        val logName = "BackofficeGoalProductController#enable"
        val markers = append("productId", productId)

        return goalProductService.setStatus(
            productId = GoalProductId(productId),
            enabled = true,
        ).map {
            logger.info(markers.andAppend("product", it), logName)
            HttpResponse.ok(it.toGoalProductTO())
        }.getOrElse {
            logger.warn(markers, logName, it)
            HttpResponse.badRequest(it)
        }
    }

    private fun SaveProductRequestTO.toCommand() = SaveGoalProductCommand(
        type = type,
        index = index,
        indexIncome = indexIncome,
        liquidity = liquidity,
        minimumTermDays = minimumTermDays,
        maximumTermDays = maximumTermDays,
        asset = asset,
        risk = risk,
        issuer = issuer,
        provider = provider,
        enabled = enabled,
    )

    private fun GoalProduct.toGoalProductTO() = GoalProductTO(
        id = id.value,
        type = type,
        index = index,
        indexIncome = indexIncome.rate,
        liquidity = liquidity,
        minimumTermDays = minimumTermDays,
        maximumTermDays = maximumTermDays,
        asset = asset,
        risk = risk,
        issuer = issuer,
        provider = provider,
        enabled = enabled,
        createdAt = createdAt.format(dateTimeFormat),
        updatedAt = updatedAt.format(dateTimeFormat),
    )
}

data class GoalProductTO(
    val id: String,
    val type: GoalProductType,
    val index: FixedIncomeIndex,
    val indexIncome: Int,
    val liquidity: GoalProductLiquidity,
    val minimumTermDays: Long?,
    val maximumTermDays: Long?,
    val asset: String,
    val risk: GoalProductRisk,
    val issuer: String,
    val provider: String,
    val enabled: Boolean = true,
    val createdAt: String,
    val updatedAt: String,
)

data class SaveProductRequestTO(
    val type: GoalProductType,
    val index: FixedIncomeIndex,
    val indexIncome: Int,
    val liquidity: GoalProductLiquidity,
    val minimumTermDays: Long?,
    val maximumTermDays: Long?,
    val asset: String,
    val risk: GoalProductRisk,
    val issuer: String,
    val provider: String,
    val enabled: Boolean = true,
)