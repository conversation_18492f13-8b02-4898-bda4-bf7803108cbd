package ai.friday.billpayment.modules.investmentGoals.app.investment

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalRepository
import arrow.core.Either
import arrow.core.right

@InvestmentGoalsModule
class InvestmentManagerService(
    private val fixedIncomeManagerProvider: FixedIncomeManagerProvider,
    private val goalRepository: GoalRepository,
) : FixedIncomeManagerService by fixedIncomeManagerProvider {

    override fun userGoalPositions(accountId: AccountId, goalId: GoalId): Either<InvestmentManagerException, UserGoalPositions> {
        return fixedIncomeManagerProvider.userGoalPositions(accountId, goalId).map {
            goalRepository.updateLastKnowNetBalance(goalId, it.netValue)
            it
        }
    }

    override fun userPositions(accountId: AccountId): Either<InvestmentManagerException, List<UserGoalPositions>> {
        return fixedIncomeManagerProvider.userPositions(accountId).map { goalPositions ->
            goalPositions.forEach {
                goalRepository.updateLastKnowNetBalance(it.goalId, it.netValue)
            }
            goalPositions
        }
    }

    fun userInvestmentsNetValue(accountId: AccountId): Either<Exception, Long> {
        if (goalRepository.findByAccountId(accountId).isEmpty()) {
            return 0L.right()
        }

        val result = userPositions(accountId)
        return result.map {
            it.sumOf { position -> position.netValue }
        }
    }
}