package ai.friday.billpayment.modules.investmentGoals.adapters.api.product

import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndex
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProduct
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductLiquidity
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductRisk
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductService
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductType
import ai.friday.billpayment.modules.investmentGoals.app.product.IndexInterestRate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.OWNER)
@Controller("/goal/product")
@Version("2")
@InvestmentGoalsModule
class GoalProductController(
    private val goalProductService: GoalProductService,
) {
    private val logger = LoggerFactory.getLogger(GoalProductController::class.java)

    @Get
    fun getAll(): HttpResponse<*> {
        val logName = "GoalProductController#getAll"
        return try {
            val products = goalProductService.findAllEnabled().map { it.toGoalProductResponseTO() }

            HttpResponse.ok(products)
        } catch (ex: Exception) {
            logger.error(logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    @Get("/interestRates")
    fun getIndexInterestRates(): HttpResponse<*> {
        val logName = "GoalProductController#getIndexInterestRates"
        val markers = Markers.empty()

        return goalProductService.getIndexInterestRates().map { interestRates ->
            markers.andAppend("interestRates", interestRates)

            logger.info(markers, logName)
            HttpResponse.ok(
                interestRates.map {
                    it.toIndexInterestRateTO()
                },
            )
        }.getOrElse {
            logger.error(markers, logName, it)
            HttpResponse.serverError<Unit>()
        }
    }

    private fun IndexInterestRate.toIndexInterestRateTO() = IndexInterestRateTO(
        index = index,
        defaultScale = defaultScale,
        yearlyInterest = yearlyInterest,
        monthlyInterest = monthlyInterest,
        weeklyInterest = weeklyInterest,
        dailyInterest = dailyInterest,
    )

    private fun GoalProduct.toGoalProductResponseTO() = GoalProductResponseTO(
        id = id.value,
        type = type,
        index = index,
        indexIncome = indexIncome.rate,
        liquidity = liquidity,
        minimumTermDays = minimumTermDays,
        maximumTermDays = maximumTermDays,
        asset = asset,
        risk = risk,
        issuer = issuer,
        provider = provider,
        startDate = minimumTermDays?.let { getLocalDate().plusDays(it).format(dateFormat) },
        endDate = maximumTermDays?.let { getLocalDate().plusDays(it).format(dateFormat) },
    )
}

data class GoalProductResponseTO(
    val id: String,
    val type: GoalProductType,
    val index: FixedIncomeIndex,
    val indexIncome: Int,
    val liquidity: GoalProductLiquidity,
    val minimumTermDays: Long?,
    val maximumTermDays: Long?,
    val asset: String,
    val risk: GoalProductRisk,
    val issuer: String,
    val provider: String,
    val startDate: String?,
    val endDate: String?,
)

data class IndexInterestRateTO(
    val index: FixedIncomeIndex,
    val defaultScale: String,
    val yearlyInterest: Double,
    val monthlyInterest: Double,
    val weeklyInterest: Double,
    val dailyInterest: Double,
)