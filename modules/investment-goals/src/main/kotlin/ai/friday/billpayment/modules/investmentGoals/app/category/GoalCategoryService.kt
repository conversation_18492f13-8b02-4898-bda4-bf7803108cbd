package ai.friday.billpayment.modules.investmentGoals.app.category

import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import java.net.URL
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@InvestmentGoalsModule
class GoalCategoryService(
    private val goalCategoryRepository: GoalCategoryRepository,
) {
    fun findOrNull(goalCategoryId: GoalCategoryId): GoalCategory? = goalCategoryRepository.findOrNull(goalCategoryId)

    fun findAll(): List<GoalCategory> = goalCategoryRepository.findAll()

    fun findAllEnabled(): List<GoalCategory> = goalCategoryRepository.findAll().filter { it.enabled }

    fun create(command: SaveGoalCategoryCommand): Either<GoalCategoryError, GoalCategory> {
        val logName = "GoalCategoryService#create"
        val markers = append("command", command)

        if (!isValidUrl(command.imageUrl)) {
            return logAndReturnCategoryError(markers, GoalCategoryError.InvalidImageUrl, logName)
        }

        return goalCategoryRepository.save(
            GoalCategory(
                name = command.name,
                type = command.type,
                imageUrl = command.imageUrl,
                enabled = command.enabled,
            ),
        ).also {
            logger.info(markers.andAppend("category", it), logName)
        }.right()
    }

    fun update(categoryId: GoalCategoryId, command: SaveGoalCategoryCommand): Either<GoalCategoryError, GoalCategory> {
        val logName = "GoalCategoryService#update"
        val markers = append("command", command)
            .andAppend("categoryId", categoryId)

        if (!isValidUrl(command.imageUrl)) {
            return logAndReturnCategoryError(markers, GoalCategoryError.InvalidImageUrl, logName)
        }

        val category = findOrNull(categoryId)
            ?: return logAndReturnCategoryError(markers, GoalCategoryError.CategoryNotFound, logName)

        return goalCategoryRepository.save(
            category.copy(
                name = command.name,
                type = command.type,
                imageUrl = command.imageUrl,
                enabled = command.enabled,
            ),
        ).also {
            logger.info(markers.andAppend("category", it), logName)
        }.right()
    }

    private fun <T> logAndReturnCategoryError(
        markers: LogstashMarker,
        error: GoalCategoryError,
        logName: String,
    ): Either<GoalCategoryError, T> =
        error.left().also { logger.error(markers.andAppend("categoryError", error), logName) }

    private fun isValidUrl(urlString: String): Boolean =
        runCatching { URL(urlString).toURI() }.isSuccess

    companion object {
        private val logger = LoggerFactory.getLogger(GoalCategoryService::class.java)
    }
}