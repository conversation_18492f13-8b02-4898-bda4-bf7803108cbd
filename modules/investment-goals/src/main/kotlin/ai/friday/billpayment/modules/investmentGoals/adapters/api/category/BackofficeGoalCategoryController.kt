package ai.friday.billpayment.modules.investmentGoals.adapters.api.category

import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.log
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategory
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryId
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryService
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryType
import ai.friday.billpayment.modules.investmentGoals.app.category.SaveGoalCategoryCommand
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(Role.Code.BACKOFFICE)
@Controller("/backoffice/goal/category")
@InvestmentGoalsModule
class BackofficeGoalCategoryController(
    private val goalCategoryService: GoalCategoryService,
) {
    private val logger = LoggerFactory.getLogger(BackofficeGoalCategoryController::class.java)

    @Get("/{categoryId}")
    fun getCategory(
        @PathVariable categoryId: String,
    ): HttpResponse<*> {
        val logName = "BackofficeGoalCategoryController#getCategory"
        val markers = log("goalCategoryId" to categoryId)

        val category = goalCategoryService.findOrNull(GoalCategoryId(categoryId))
        markers.andAppend("category", category)

        logger.info(markers, logName)
        return if (category == null) {
            HttpResponse.notFound(categoryId)
        } else {
            HttpResponse.ok(category.toGoalCategoryTO())
        }
    }

    @Get
    fun getAll(): HttpResponse<*> {
        val logName = "BackofficeGoalCategoryController#getAll"

        val categories = goalCategoryService.findAll()

        logger.info(log("categories" to categories), logName)
        return HttpResponse.ok(categories.map { it.toGoalCategoryTO() })
    }

    @Post
    fun create(
        @Body body: SaveCategoryRequestTO,
    ): HttpResponse<*> {
        val logName = "BackofficeGoalCategoryController#create"
        val markers = append("body", body)

        return goalCategoryService.create(
            command = body.toCommand(),
        ).map {
            logger.info(markers.andAppend("category", it), logName)
            HttpResponse.ok(it.toGoalCategoryTO())
        }.getOrElse {
            logger.warn(markers.andAppend("error", it), logName)
            HttpResponse.badRequest(it)
        }
    }

    @Put("/{categoryId}")
    fun update(
        @PathVariable categoryId: String,
        @Body body: SaveCategoryRequestTO,
    ): HttpResponse<*> {
        val logName = "BackofficeGoalCategoryController#update"
        val markers = append("body", body)
            .andAppend("categoryId", categoryId)

        return goalCategoryService.update(
            categoryId = GoalCategoryId(categoryId),
            command = body.toCommand(),
        ).map {
            logger.info(markers.andAppend("category", it), logName)
            HttpResponse.ok(it.toGoalCategoryTO())
        }.getOrElse {
            logger.warn(markers.andAppend("error", it), logName)
            HttpResponse.badRequest(it)
        }
    }
}

data class GoalCategoryTO(
    val id: String,
    val type: GoalCategoryType,
    val name: String,
    val imageUrl: String,
    val enabled: Boolean,
)

data class SaveCategoryRequestTO(
    val type: GoalCategoryType,
    val name: String,
    val imageUrl: String,
    val enabled: Boolean,
)

private fun SaveCategoryRequestTO.toCommand() =
    SaveGoalCategoryCommand(
        type = type,
        name = name,
        imageUrl = imageUrl,
        enabled = enabled,
    )

private fun GoalCategory.toGoalCategoryTO() =
    GoalCategoryTO(
        id = id.value,
        type = type,
        name = name,
        imageUrl = imageUrl,
        enabled = enabled,
    )