package ai.friday.billpayment.modules.investmentGoals.adapters.api.backoffice

import ai.friday.billpayment.adapters.dynamodb.BillRecurrenceDBRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.PaymentMethodNotFound
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BankAccountService
import ai.friday.billpayment.app.integrations.SubscriptionRepository
import ai.friday.billpayment.app.statement.StatementService
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Property
import java.time.LocalDate
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@InvestmentGoalsModule
class SubscriptionRefundService(
    private val subscriptionRepository: SubscriptionRepository,
    private val recurrenceRepository: BillRecurrenceDBRepository,
    private val findBillRepository: DynamoDbBillRepository,
    private val statementService: StatementService,
    private val accountRepository: AccountRepository,
    private val bankAccountService: BankAccountService,
    @Property(name = "subscription.amount") private val subscriptionAmount: Long,
) {
    private val logger = LoggerFactory.getLogger(SubscriptionRefundService::class.java)

    fun refundAccountId(accountId: AccountId, dryRun: Boolean, dueDate: LocalDate? = null): Either<SubscriptionRefundError, Unit> {
        val logName = "SubscriptionRefundService#accountId"
        val markers = append("accountId", accountId.value)
        val subscription = subscriptionRepository.find(accountId)
        if (subscription == null) {
            markers.andAppend("context", "assinatura não encontrada")
            logger.warn(markers, logName)
            return SubscriptionRefundError.SubscriptionNotFound().left()
        }

        markers.andAppend("document", subscription.document.value)
            .andAppend("walletId", subscription.walletId.value)
            .andAppend("recurrenceId", subscription.recurrenceId.value)

        val referenceDate = dueDate ?: getLocalDate()

        val recurrence = recurrenceRepository.find(subscription.recurrenceId, subscription.walletId)
        val paidSubscriptionBill: BillView? =
            recurrence.bills
                .mapNotNull { billId ->
                    val bill = try {
                        findBillRepository.findBill(billId, subscription.walletId)
                    } catch (e: IllegalStateException) {
                        null
                    }
                    val isInPeriod = if (referenceDate.dayOfMonth >= 10) {
                        bill?.dueDate?.isAfter(referenceDate.withDayOfMonth(1))
                    } else {
                        bill?.dueDate?.isAfter(referenceDate.minusMonths(1).withDayOfMonth(1))
                    }
                    if (bill?.status == BillStatus.PAID && isInPeriod == true) bill else null
                }
                .firstOrNull()

        val paidSubscriptionBillId = paidSubscriptionBill?.billId
        markers.andAppend("paidSubscriptionBillId", paidSubscriptionBillId?.value)
        markers.andAppend("hasPaidSubscription", paidSubscriptionBillId != null)

        if (paidSubscriptionBillId == null) {
            markers.andAppend("context", "paidSubscription não encontrado")
            logger.warn(markers, logName)
            return SubscriptionRefundError.BillNotFound().left()
        }

        val statements = statementService.findAllStatementsByDate(subscription.walletId, LocalDate.now().minusMonths(1).withDayOfMonth(1), LocalDate.now()).getOrElse {
            logger.error(markers.andAppend("context", "erro ao pegar depositos"), logName, it)
            return SubscriptionRefundError.PaidStatementsNotFound().left()
        }

        val validDepositThreshold = 50_00L

        val hasValidDeposit =
            statements.statementItems.any { statement ->
                statement.flow == BankStatementItemFlow.CREDIT && statement.amount >= validDepositThreshold
            }
        markers.andAppend("hasValidDeposit", hasValidDeposit)

        val totalCreditAmount =
            statements.statementItems
                .filter { statement ->
                    statement.flow == BankStatementItemFlow.CREDIT
                }.sumOf { it.amount }
        markers.andAppend("totalCreditAmount", totalCreditAmount)

        val totalDebitAmount =
            statements.statementItems
                .filter { statement ->
                    statement.flow == BankStatementItemFlow.DEBIT
                }.sumOf { it.amount }
        markers.andAppend("totalDebitAmount", totalDebitAmount)

        val totalCreditMinusDebit = totalCreditAmount - totalDebitAmount
        markers.andAppend("totalCreditMinusDebit", totalCreditMinusDebit)

        val shouldRefund = hasValidDeposit || totalCreditMinusDebit >= validDepositThreshold
        markers.andAppend("shouldRefund", shouldRefund)

        if (shouldRefund) {
            val toBankAccount = try {
                accountRepository.findPhysicalBankAccountByAccountId(subscription.accountId).firstOrNull { it.status == AccountPaymentMethodStatus.ACTIVE }
            } catch (e: PaymentMethodNotFound) {
                logger.error(markers, logName, e)
                return SubscriptionRefundError.PaidMethodNotFound().left()
            }

            val internalBankAccount = toBankAccount?.method as? InternalBankAccount
            markers.andAppend("internalBankAccount", internalBankAccount)
            markers.andAppend("hasInternalBankAccount", internalBankAccount != null)

            markers.andAppend("dryRun", dryRun)

            val originAccountNo = "387506"
            markers.andAppend("originAccountNo", originAccountNo)

            val targetAccountNo = internalBankAccount?.buildFullAccountNumber()
            markers.andAppend("targetAccountNo", targetAccountNo)

            if (!dryRun && targetAccountNo != null) {
                val bankOperationId = BankOperationId("REFUND-SUB-${subscription.document.value}-${paidSubscriptionBill.dueDate}")
                markers.andAppend("bankOperationId", bankOperationId)

                val transferResult =
                    try {
                        bankAccountService.transfer(
                            originAccountNo = originAccountNo,
                            targetAccountNo = targetAccountNo,
                            amount = subscriptionAmount,
                            operationId = bankOperationId,
                        )
                    } catch (e: Exception) {
                        logger.error(markers, logName, e)
                        return SubscriptionRefundError.GenericError(e).left()
                    }
                markers.andAppend("transferResult", transferResult)
            }
        }
        logger.info(markers, logName)
        return Unit.right()
    }

    fun refundBy(dryRun: Boolean): List<CreateBillResult> {
        val logName = "SubscriptionRefundService"
        val createdPixList = mutableListOf<CreateBillResult>()

        val subscriptions = subscriptionRepository.findAll()
        logger.info(append("totalSubscriptions", subscriptions), logName)
        subscriptions.forEach { subscription ->
            val markers = append("document", subscription.document.value).andAppend("walletId", subscription.walletId.value).andAppend("recurrenceId", subscription.recurrenceId.value)

            val recurrence = recurrenceRepository.find(subscription.recurrenceId, subscription.walletId)
            val paidSubscriptionBillId =
                recurrence.bills.firstOrNull { billId ->
                    val bill =
                        try {
                            findBillRepository.findBill(billId, subscription.walletId)
                        } catch (e: IllegalStateException) {
                            null
                        }
                    bill?.status == BillStatus.PAID && bill.dueDate.isBefore(LocalDate.now()) && bill.dueDate.isAfter(LocalDate.now().withDayOfMonth(1))
                }
            markers.andAppend("paidSubscriptionBillId", paidSubscriptionBillId?.value)
            markers.andAppend("hasPaidSubscription", paidSubscriptionBillId != null)

            if (paidSubscriptionBillId != null) {
                val statements = statementService.findAllStatementsByDate(subscription.walletId, LocalDate.now().minusMonths(1).withDayOfMonth(1), LocalDate.now()).getOrElse {
                    logger.error(markers.andAppend("context", "erro ao pegar depositos"), logName, it)
                    return@forEach
                }

                val validDepositThreshold = 50_00L

                val hasValidDeposit =
                    statements.statementItems.any { statement ->
                        statement.flow == BankStatementItemFlow.CREDIT && statement.amount >= validDepositThreshold
                    }
                markers.andAppend("hasValidDeposit", hasValidDeposit)

                val totalCreditAmount =
                    statements.statementItems
                        .filter { statement ->
                            statement.flow == BankStatementItemFlow.CREDIT
                        }.sumOf { it.amount }
                markers.andAppend("totalCreditAmount", totalCreditAmount)

                val totalDebitAmount =
                    statements.statementItems
                        .filter { statement ->
                            statement.flow == BankStatementItemFlow.DEBIT
                        }.sumOf { it.amount }
                markers.andAppend("totalDebitAmount", totalDebitAmount)

                val totalCreditMinusDebit = totalCreditAmount - totalDebitAmount
                markers.andAppend("totalCreditMinusDebit", totalCreditMinusDebit)

                val shouldRefund = hasValidDeposit || totalCreditMinusDebit >= validDepositThreshold
                markers.andAppend("shouldRefund", shouldRefund)

                if (shouldRefund) {
                    val toBankAccount = try {
                        accountRepository.findPhysicalBankAccountByAccountId(subscription.accountId).firstOrNull { it.status == AccountPaymentMethodStatus.ACTIVE }
                    } catch (e: PaymentMethodNotFound) {
                        null
                    }

                    val internalBankAccount = toBankAccount?.method as? InternalBankAccount
                    markers.andAppend("internalBankAccount", internalBankAccount)
                    markers.andAppend("hasInternalBankAccount", internalBankAccount != null)

                    markers.andAppend("dryRun", dryRun)

                    val originAccountNo = "387506"
                    markers.andAppend("originAccountNo", originAccountNo)

                    val targetAccountNo = internalBankAccount?.buildFullAccountNumber()
                    markers.andAppend("targetAccountNo", targetAccountNo)

                    val bankOperationId = BankOperationId("REFUND-SUB-${subscription.document.value}")

                    if (!dryRun && targetAccountNo != null) {
                        val transferResult =
                            try {
                                bankAccountService.transfer(
                                    originAccountNo = originAccountNo,
                                    targetAccountNo = targetAccountNo,
                                    amount = subscriptionAmount,
                                    operationId = bankOperationId,
                                )
                            } catch (e: Exception) {
                                null
                            }
                        markers.andAppend("transferResult", transferResult)
                    }
                }
            }

            logger.info(markers, logName)
        }

        return createdPixList
    }
}

sealed class SubscriptionRefundError() {
    class SubscriptionNotFound : SubscriptionRefundError()
    class BillNotFound() : SubscriptionRefundError()
    class PaidStatementsNotFound : SubscriptionRefundError()
    class PaidMethodNotFound : SubscriptionRefundError()
    class GenericError(exception: Exception) : SubscriptionRefundError()
}