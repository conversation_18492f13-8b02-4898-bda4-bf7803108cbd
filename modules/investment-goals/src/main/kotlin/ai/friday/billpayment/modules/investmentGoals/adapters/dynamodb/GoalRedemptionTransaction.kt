package ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.SettlementDataDocument
import ai.friday.billpayment.adapters.dynamodb.SettlementOperationEntityType
import ai.friday.billpayment.adapters.dynamodb.TransactionEntity
import ai.friday.billpayment.adapters.dynamodb.TransactionSettlementOperationConverter
import ai.friday.billpayment.adapters.dynamodb.TransactionSettlementTargetConverter
import ai.friday.billpayment.app.payment.SettlementOperation
import ai.friday.billpayment.app.payment.SettlementTarget
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeProvider
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalRequestOperationId
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerExternalId
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerRequestStatus
import ai.friday.billpayment.modules.investmentGoals.app.payment.GoalRedemptionSettlementOperation
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemption
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionId
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionRepository

@InvestmentGoalsModule
class GoalRedemptionSettlementOperationConverter : TransactionSettlementOperationConverter<GoalRedemptionSettlementOperation, SettlementOperationEntityType.GoalRedemption> {
    override val domainType = GoalRedemptionSettlementOperation::class
    override val entityType = SettlementOperationEntityType.GoalRedemption::class

    override fun toDomain(element: SettlementOperationEntityType, settlementData: SettlementDataDocument): GoalRedemptionSettlementOperation {
        return (element as SettlementOperationEntityType.GoalRedemption).toDomain()
    }

    override fun toEntity(element: SettlementOperation): SettlementOperationEntityType.GoalRedemption {
        return (element as GoalRedemptionSettlementOperation).toEntity()
    }
}

@InvestmentGoalsModule
class GoalRedemptionSettlementTargetConverter(
    private val goalRedemptionRepository: GoalRedemptionRepository,
) : TransactionSettlementTargetConverter<GoalRedemption> {
    override val domainType = GoalRedemption::class
    override val transactionTypes = listOf(TransactionType.GOAL_REDEMPTION)

    override fun toDomain(settlementTargetId: String, transactionEntity: TransactionEntity): GoalRedemption {
        return goalRedemptionRepository.findOrNull(GoalRedemptionId(settlementTargetId)) ?: throw IllegalStateException("settlement target $settlementTargetId not found")
    }

    override fun toSettlementTargetId(element: SettlementTarget): String {
        return (element as GoalRedemption).id.value
    }
}

private fun SettlementOperationEntityType.GoalRedemption.toDomain(): GoalRedemptionSettlementOperation {
    return GoalRedemptionSettlementOperation(
        operationId = GoalRequestOperationId(operationId),
        status = InvestmentManagerRequestStatus.valueOf(status),
        redemptionGroupExternalId = redemptionGroupExternalId?.let { InvestmentManagerExternalId(it) },
        redemptionExternalIds = redemptionExternalIds.map { InvestmentManagerExternalId(it) },
        amount = amount,
        errorDescription = errorDescription,
        provider = provider?.let { FixedIncomeProvider.valueOf(it) },
    )
}

private fun GoalRedemptionSettlementOperation.toEntity(): SettlementOperationEntityType.GoalRedemption {
    return SettlementOperationEntityType.GoalRedemption(
        operationId = operationId.value,
        status = status.name,
        redemptionGroupExternalId = redemptionGroupExternalId?.value,
        redemptionExternalIds = redemptionExternalIds.map { it.value },
        amount = amount,
        errorDescription = errorDescription,
        provider = provider?.name,
    )
}