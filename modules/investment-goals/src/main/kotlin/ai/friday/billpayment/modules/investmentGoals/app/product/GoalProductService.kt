package ai.friday.billpayment.modules.investmentGoals.app.product

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalRepository
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndex
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import java.time.LocalDate
import java.time.temporal.ChronoUnit

@InvestmentGoalsModule
open class GoalProductService(
    private val goalProductRepository: GoalProductRepository,
    private val goalRepository: GoalRepository,
    private val investmentManagerService: InvestmentManagerService,
) {
    open fun save(product: GoalProduct): GoalProduct = goalProductRepository.save(product)

    open fun create(command: SaveGoalProductCommand): Either<Exception, GoalProduct> {
        return try {
            goalProductRepository.save(
                GoalProduct(
                    type = command.type,
                    index = command.index,
                    indexIncome = FixedIncomeIndexRate(command.indexIncome),
                    liquidity = command.liquidity,
                    minimumTermDays = command.minimumTermDays,
                    maximumTermDays = command.maximumTermDays,
                    asset = command.asset,
                    risk = command.risk,
                    issuer = command.issuer,
                    provider = command.provider,
                    enabled = command.enabled,
                ),
            ).right()
        } catch (e: Exception) {
            e.left()
        }
    }

    open fun update(productId: GoalProductId, command: SaveGoalProductCommand): Either<Exception, GoalProduct> {
        return try {
            val product = goalProductRepository.findOrNull(productId)

            if (product == null) {
                IllegalStateException("${productId.value} not found").left()
            } else if (goalRepository.findByProductId(productId).isNotEmpty()) {
                IllegalStateException("${productId.value} is used by goals").left()
            } else {
                goalProductRepository.save(
                    product.copy(
                        type = command.type,
                        index = command.index,
                        indexIncome = FixedIncomeIndexRate(command.indexIncome),
                        liquidity = command.liquidity,
                        minimumTermDays = command.minimumTermDays,
                        maximumTermDays = command.maximumTermDays,
                        asset = command.asset,
                        risk = command.risk,
                        issuer = command.issuer,
                        provider = command.provider,
                        enabled = command.enabled,
                    ),
                ).right()
            }
        } catch (e: Exception) {
            e.left()
        }
    }

    open fun setStatus(productId: GoalProductId, enabled: Boolean): Either<Exception, GoalProduct> {
        return try {
            val product = goalProductRepository.findOrNull(productId)

            if (product == null) {
                IllegalStateException("${productId.value} not found").left()
            } else {
                goalProductRepository.save(
                    product.copy(
                        enabled = enabled,
                    ),
                ).right()
            }
        } catch (e: Exception) {
            e.left()
        }
    }

    open fun findProduct(productId: GoalProductId): Either<Exception, GoalProduct> {
        return try {
            goalProductRepository.findOrNull(productId)?.right() ?: IllegalStateException().left() // FIXME
        } catch (e: Exception) {
            e.left()
        }
    }

    open fun findAll(): List<GoalProduct> = goalProductRepository.findAll()

    open fun findAllEnabled(): List<GoalProduct> = goalProductRepository.findAll().filter { it.enabled }

    open fun choose(liquidity: GoalProductLiquidity, endDate: LocalDate?): Either<SelectGoalProductError, GoalProduct> {
        if (endDate == null && liquidity == GoalProductLiquidity.MATURITY) {
            return SelectGoalProductError.MandatoryEndDate.left()
        }

        return findAllEnabled().filter {
            it.liquidity == liquidity && endDate.matchesTermsInterval(it)
        }.singleProduct()
    }

    open fun getIndexInterestRates() = investmentManagerService.getIndexInterestRates()

    private fun LocalDate?.matchesTermsInterval(product: GoalProduct): Boolean {
        return if (this == null) {
            true
        } else {
            val delta = ChronoUnit.DAYS.between(getLocalDate(), this)

            (product.minimumTermDays == null || product.minimumTermDays <= delta) && (product.maximumTermDays == null || delta < product.maximumTermDays)
        }
    }

    private fun List<GoalProduct>.singleProduct() = when (size) {
        0 -> SelectGoalProductError.ProductNotFound.left()
        1 -> first().right()
        else -> SelectGoalProductError.MultipleProductsFound(map { it.id }).left()
    }

    fun checkTermIntegrity(): Either<TermIntegrityError, Unit> {
        val sortedProducts = goalProductRepository.findAll().filter {
            it.enabled && it.liquidity == GoalProductLiquidity.MATURITY
        }.sortedBy {
            it.minTerm()
        }

        val gapingTerms = sortedProducts.findGapingTerms()

        if (gapingTerms.isNotEmpty()) {
            return TermIntegrityError.GapingTerms(gapingTerms).left()
        }

        val overlapingTerms = sortedProducts.findOverlapingTerms()

        if (overlapingTerms.isNotEmpty()) {
            return TermIntegrityError.OverlapingTerms(overlapingTerms).left()
        }

        return Unit.right()
    }

    private fun List<GoalProduct>.findGapingTerms(): List<GoalProductId> {
        var lastMaxTerm = 0L
        var lastProductId: GoalProductId? = null
        val gapingTerms = mutableListOf<GoalProductId>()

        forEach {
            if (it.minTerm() > lastMaxTerm) {
                if (lastProductId != null && !gapingTerms.contains(lastProductId)) {
                    gapingTerms.add(lastProductId!!)
                }
                gapingTerms.add(it.id)
            }
            lastMaxTerm = it.maxTerm()
            lastProductId = it.id
        }

        return gapingTerms
    }

    private fun List<GoalProduct>.findOverlapingTerms(): List<GoalProductId> {
        return filter { oneProduct ->
            any { anotherProduct ->
                oneProduct.overlapsWith(anotherProduct)
            }
        }.map {
            it.id
        }
    }
}

fun GoalProduct.overlapsWith(anotherProduct: GoalProduct): Boolean {
    return this.id != anotherProduct.id && (
        (this.minTerm() <= anotherProduct.minTerm() && anotherProduct.minTerm() < this.maxTerm()) ||
            (this.minTerm() < anotherProduct.maxTerm() && anotherProduct.maxTerm() <= this.maxTerm()) ||
            (anotherProduct.minTerm() <= this.minTerm() && this.minTerm() < anotherProduct.maxTerm()) ||
            (anotherProduct.minTerm() < this.maxTerm() && this.maxTerm() <= anotherProduct.maxTerm())
        )
}

private fun GoalProduct.minTerm(): Long = minimumTermDays ?: 0
private fun GoalProduct.maxTerm(): Long = maximumTermDays ?: Long.MAX_VALUE

sealed class SelectGoalProductError : PrintableSealedClassV2() {
    data object MandatoryEndDate : SelectGoalProductError()
    data object ProductNotFound : SelectGoalProductError()
    data class MultipleProductsFound(val productIds: List<GoalProductId>) : SelectGoalProductError()
}

sealed class TermIntegrityError : PrintableSealedClassV2() {
    data class OverlapingTerms(val productIds: List<GoalProductId>) : TermIntegrityError()
    data class GapingTerms(val productIds: List<GoalProductId>) : TermIntegrityError()
}

data class IndexInterestRate(
    val index: FixedIncomeIndex,
    val defaultScale: String,
    val yearlyInterest: Double,
    val monthlyInterest: Double,
    val weeklyInterest: Double,
    val dailyInterest: Double,
)