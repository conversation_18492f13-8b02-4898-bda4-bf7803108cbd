package ai.friday.billpayment.modules.investmentGoals.app.statement

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndex
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestment
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentRepository
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentStatus
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerService
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemption
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionRepository
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionStatus
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate

@InvestmentGoalsModule
open class GoalStatementService(
    private val investmentManagerService: InvestmentManagerService,
    private val goalInvestmentRepository: GoalInvestmentRepository,
    private val goalRedemptionRepository: GoalRedemptionRepository,
) {

    open fun getValidGoalStatements(accountId: AccountId, goalId: GoalId): Either<Exception, List<GoalStatementItem>> {
        val investmentStatements = goalInvestmentRepository.findByGoalId(goalId).filter {
            when (it.status) {
                GoalInvestmentStatus.CREATED, GoalInvestmentStatus.CANCELED -> false
                GoalInvestmentStatus.PROCESSING, GoalInvestmentStatus.DONE, GoalInvestmentStatus.FAILED -> true
            }
        }.map { it.toGoalStatement() }
        val redemptionStatements = goalRedemptionRepository.findByGoalId(goalId).map { it.toGoalStatement() }
        val orderedStatements = (investmentStatements + redemptionStatements).sortedByDescending { it.timestamp }

        return orderedStatements.right()
    }

    open fun calcSavingsToCDIRate(startDate: LocalDate, indexRelativeRate: FixedIncomeIndexRate): Either<Exception, Int> {
        val rates = investmentManagerService.compareAllRates(
            startDate = startDate,
            endDate = getLocalDate(),
            indexRelativeRate = indexRelativeRate,
        ).getOrElse {
            return it.left()
        }

        val savingsRate = rates.getRate(FixedIncomeIndex.SAVINGS).getOrElse {
            return it.left()
        }

        val cdiRate = rates.getRate(FixedIncomeIndex.CDI).getOrElse {
            return it.left()
        }

        return savingsRate.divide(cdiRate, 2, RoundingMode.HALF_EVEN).multiply(100L.toBigDecimal()).toInt().right()
    }

    private fun Map<FixedIncomeIndex, Double>.getRate(index: FixedIncomeIndex): Either<Exception, BigDecimal> {
        val rate = this[index]

        return if (rate == null) {
            IllegalStateException("missing $index rate").left()
        } else {
            BigDecimal.valueOf(rate).right()
        }
    }
}

private fun GoalRedemption.toGoalStatement(): GoalStatementItem {
    return GoalStatementItem(
        id = GoalStatementItemId(this.id.value),
        positionId = null,
        type = GoalStatementItemType.DEBIT,
        amount = this.netAmount,
        status = this.status.toGoalStatementItemStatus(),
        timestamp = this.createdAt,
    )
}

private fun GoalInvestment.toGoalStatement(): GoalStatementItem {
    return GoalStatementItem(
        id = GoalStatementItemId(this.id.value),
        positionId = null,
        type = GoalStatementItemType.CREDIT,
        amount = this.amount,
        status = this.status.toGoalStatementItemStatus(),
        timestamp = this.paidAt ?: this.createdAt,
    )
}

private fun GoalRedemptionStatus.toGoalStatementItemStatus(): GoalStatementItemStatus {
    return when (this) {
        GoalRedemptionStatus.CREATED -> GoalStatementItemStatus.CREATED
        GoalRedemptionStatus.PROCESSING -> GoalStatementItemStatus.PROCESSING
        GoalRedemptionStatus.DONE -> GoalStatementItemStatus.DONE
        GoalRedemptionStatus.FAILED -> GoalStatementItemStatus.FAILED
    }
}

private fun GoalInvestmentStatus.toGoalStatementItemStatus(): GoalStatementItemStatus {
    return when (this) {
        GoalInvestmentStatus.CREATED -> GoalStatementItemStatus.CREATED
        GoalInvestmentStatus.PROCESSING -> GoalStatementItemStatus.PROCESSING
        GoalInvestmentStatus.DONE -> GoalStatementItemStatus.DONE
        GoalInvestmentStatus.FAILED -> GoalStatementItemStatus.FAILED
        GoalInvestmentStatus.CANCELED -> GoalStatementItemStatus.FAILED
    }
}