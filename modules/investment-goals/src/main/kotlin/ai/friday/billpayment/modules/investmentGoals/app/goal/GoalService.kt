package ai.friday.billpayment.modules.investmentGoals.app.goal

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.DocumentInfo
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.account.UserAddressConfiguration
import ai.friday.billpayment.app.account.toAddress
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.ImageUrlProvider
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.journey.UserJourneyEvent
import ai.friday.billpayment.app.journey.UserJourneyService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletType
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.adapters.instrumentation.GoalInstrumentationRepository
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategory
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryService
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryType
import ai.friday.billpayment.modules.investmentGoals.app.goal.instrumentation.GoalCreatedInstrumentationEvent
import ai.friday.billpayment.modules.investmentGoals.app.investment.CreateRegularGoalInvestmentCommand
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentService
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentStatus
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalPenaltyRate
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerService
import ai.friday.billpayment.modules.investmentGoals.app.investment.UserGoalPositions
import ai.friday.billpayment.modules.investmentGoals.app.messaging.GoalMessagePublisher
import ai.friday.billpayment.modules.investmentGoals.app.payment.GoalsInvestmentNotificationService
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProduct
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductLiquidity
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductService
import ai.friday.billpayment.modules.investmentGoals.app.product.SelectGoalProductError
import ai.friday.billpayment.modules.investmentGoals.app.roundToAllowedInvestmentAmount
import ai.friday.billpayment.modules.investmentGoals.app.statement.GoalStatementService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import java.math.RoundingMode
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import kotlin.math.abs
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@InvestmentGoalsModule
open class GoalService(
    private val goalRepository: GoalRepository,
    private val goalProductService: GoalProductService,
    private val goalStatementService: GoalStatementService,
    private val goalInvestmentService: GoalInvestmentService,
    private val accountRepository: AccountRepository,
    private val accountRegisterRepository: AccountRegisterRepository,
    private val walletRepository: WalletRepository,
    private val updateBillService: UpdateBillService,
    private val investmentManagerService: InvestmentManagerService,
    private val goalMessagePublisher: GoalMessagePublisher,
    private val userAddressConfiguration: UserAddressConfiguration,
    private val goalsInvestmentNotificationService: GoalsInvestmentNotificationService,
    private val goalInstrumentationRepository: GoalInstrumentationRepository,
    private val userJourneyService: UserJourneyService,
    goalCategoryService: GoalCategoryService,

) : ImageUrlProvider<GoalId> {
    private val logger = LoggerFactory.getLogger(GoalService::class.java)
    private val enabledGoalCategories = goalCategoryService.findAllEnabled()

    open fun create(command: CreateGoalCommand): Either<CreateGoalError, Goal> {
        val logName = "GoalService#create"
        val markers = Markers.append("command", command)

        val account = accountRepository.findById(command.accountId)
        markers.andAppend("account", account)

        val product = goalProductService.choose(
            liquidity = command.liquidity,
            endDate = command.endDate,
        ).getOrElse {
            logger.warn(markers.andAppend("error", it), logName)
            return CreateGoalError.UnableToFindProduct(it).left()
        }
        markers.andAppend("product", product)

        val accountRegister = if (account.type == UserAccountType.BASIC_ACCOUNT) {
            val originalRegisterData = accountRegisterRepository.findByAccountId(command.accountId)

            originalRegisterData.copy(
                address = userAddressConfiguration.toAddress(),
                documentInfo = DocumentInfo(
                    docType = DocumentType.CPF,
                    name = originalRegisterData.nickname,
                    cpf = originalRegisterData.document!!.value,
                    birthDate = originalRegisterData.birthDate,
                    fatherName = "",
                    motherName = "",
                    rg = "",
                    cnhNumber = null,
                    orgEmission = "",
                    expeditionDate = originalRegisterData.birthDate?.plusYears(18),
                    birthCity = "",
                    birthState = "",
                ),
            )
        } else {
            accountRegisterRepository.findByAccountId(command.accountId)
        }

        val primaryWallet = walletRepository.findWallets(command.accountId).single {
            it.founder.accountId == command.accountId && it.type == WalletType.PRIMARY
        }

        val paymentMethod = walletRepository.findAccountPaymentMethod(primaryWallet.id)

        investmentManagerService.enableFixedIncomeAccount(accountRegister, paymentMethod.method as InternalBankAccount).getOrElse {
            logger.warn(markers.andAppend("error", it), logName, it)
            return CreateGoalError.CouldNotEnableFixedIncomeAccount(it.message.orEmpty()).left()
        }

        val goalCategory = enabledGoalCategories.firstOrNull { category -> category.id == command.categoryId }
        if (goalCategory == null || !goalCategory.enabled) {
            return CreateGoalError.InvalidGoalCategory(goalCategory, "CATEGORY_NOT_FOUND").left()
        }

        if (!goalCategory.type.canHaveMultiple()) {
            val hasOther = goalRepository.findByAccountId(command.accountId).any {
                when (it.status) {
                    GoalStatus.ACTIVE, GoalStatus.PAUSED -> true
                    GoalStatus.REMOVED, GoalStatus.COMPLETED -> false
                } && it.categoryId == command.categoryId
            }
            if (hasOther) {
                return CreateGoalError.InvalidGoalCategory(goalCategory, "CATEGORY_ALREADY_EXISTS").left()
            }
        }

        val goal = try {
            command.toGoal(primaryWallet.id, product)
        } catch (e: Exception) {
            logger.error(markers.andAppend("error", e), logName, e)
            return CreateGoalError.InvalidInstallmentConfiguration(command.installmentFrequency, command.installmentExpirationDay).left()
        }
        markers.andAppend("goal", goal)

        goalRepository.save(goal)

        goalMessagePublisher.publishGoalInvestmentCreated(goal.id, accountId = command.accountId, extraInstallment = false)

        if (command.initialInvestmentAmount != null) {
            goalMessagePublisher.publishGoalInvestmentCreated(goal.id, accountId = command.accountId, extraInstallment = true, extraInstallmentAmount = command.initialInvestmentAmount)
        }

        goalInstrumentationRepository.publishEvent(GoalCreatedInstrumentationEvent(goal, product))

        userJourneyService.trackEventAsync(
            accountId = command.accountId,
            event = UserJourneyEvent.GoalCreated,
        )

        logger.info(markers, logName)
        return goal.right()
    }

    open fun findUserGoals(accountId: AccountId): Either<Exception, List<Goal>> {
        return try {
            goalRepository.findByAccountId(accountId).defaultSort().right()
        } catch (e: Exception) {
            e.left()
        }
    }

    open fun getDetailedGoals(accountId: AccountId): Either<Exception, DetailedGoals> {
        val logName = "GoalService#getDetailedGoals"
        val markers = append("accountId", accountId.value)
        return try {
            if (goalRepository.findByAccountId(accountId).isEmpty()) {
                return DetailedGoals(
                    totalBalance = GoalBalance(0, 0, 0, 0, 0, 0, 0),
                    items = emptyList(),
                ).right()
            }

            val userAllGoalsPositions = investmentManagerService.userPositions(
                accountId = accountId,
            ).getOrElse {
                logger.error(markers, logName, it)
                null
            }

            val nonGoalPositions = userAllGoalsPositions?.filter { it.goalId == GoalId("DEFAULT_GROUP") }
            if (!nonGoalPositions.isNullOrEmpty()) {
                logger.error(markers.andAppend("ACTION", "VERIFY").andAppend("nonGoalPositions", nonGoalPositions).andAppend("context", "posição sem meta associada"), logName)
            }

            val detailedGoals = goalRepository.findByAccountId(accountId).defaultSort().map { goal ->
                val userGoalsPositions = userAllGoalsPositions?.let {
                    it.firstOrNull { position -> position.goalId == goal.id } ?: UserGoalPositions.ofEmpty(accountId, goal.id)
                }
                getDetailedGoal(goal, userGoalsPositions).getOrElse {
                    logger.error("GoalService#getDetailedGoals", it)
                    return it.left()
                }
            }
            DetailedGoals(
                totalBalance = detailedGoals.totalBalance(),
                items = detailedGoals,
            ).right()
        } catch (e: Exception) {
            e.left()
        }
    }

    private fun List<Goal>.defaultSort(): List<Goal> {
        return sortedWith(
            compareBy<Goal> {
                it.status.order()
            }.thenBy { enabledGoalCategories.firstOrNull { category -> category.id == it.categoryId }?.type?.order() }
                .thenBy { it.liquidity.order() }
                .thenBy { it.name },
        )
    }

    open fun getDetailedGoal(accountId: AccountId, goalId: GoalId): Either<Exception, DetailedGoal> {
        val logName = "GoalService#getDetailedGoal"
        val markers = append("goalId", goalId.value).andAppend("accountId", accountId.value)

        val goal = findGoal(goalId, accountId).getOrElse {
            return it.left()
        }

        val userGoalPositions = investmentManagerService.userGoalPositions(
            accountId = goal.accountId,
            goalId = goal.id,
        ).getOrElse {
            logger.error(markers, logName, it)
            null
        }

        return getDetailedGoal(goal, userGoalPositions)
    }

    open fun update(command: UpdateGoalCommand): Either<UpdateGoalError, Goal> {
        fun updateName(goal: Goal, newName: String?): Either<UpdateGoalError, Pair<Goal, Boolean>> {
            return if (newName != null && goal.name != newName) {
                (goalRepository.save(goal.copy(name = newName)) to true).right()
            } else {
                (goal to false).right()
            }
        }

        fun updateAmount(goal: Goal, newAmount: Long?): Either<UpdateGoalError, Pair<Goal, Boolean>> {
            return if (newAmount != null && goal.amount != newAmount) {
                if (!goal.canBeUpdated()) {
                    return UpdateGoalError.InvalidGoalStatus.left()
                }

                if (newAmount < goal.amount) {
                    val currentPosition = investmentManagerService.userGoalPositions(goal.accountId, goal.id).getOrElse { return UpdateGoalError.GenericException(it).left() }
                    if (newAmount < currentPosition.netValue) {
                        return UpdateGoalError.InvalidGoalStatus.left()
                    }
                }
                (goalRepository.save(goal.copy(amount = newAmount)) to true).right()
            } else {
                (goal to false).right()
            }
        }

        fun updateEndDate(goal: Goal, endDate: LocalDate?, actionSource: ActionSource.WalletActionSource): Either<UpdateGoalError, Pair<Goal, Boolean>> {
            return if (endDate != null && goal.endDate != endDate) {
                if (!goal.canBeUpdated()) {
                    return UpdateGoalError.InvalidGoalStatus.left()
                }
                if (endDate < getLocalDate() || goal.liquidity != GoalProductLiquidity.DAILY) {
                    return UpdateGoalError.InvalidGoalStatus.left()
                }

                if (endDate < goal.createdAt.toLocalDate().plusMonths(1)) {
                    return UpdateGoalError.InvalidGoalStatus.left()
                }

                if (endDate < goal.endDate) {
                    val investmentsToBeCanceled = goalInvestmentService.findAll(goal.id).filter { it.dueDate > endDate && it.paidAt == null }
                    goalInvestmentService.ignoreInvestments(goal, investmentsToBeCanceled, actionSource).getOrElse { return it.left() }
                }

                (goalRepository.save(goal.copy(endDate = endDate)) to true).right()
            } else {
                (goal to false).right()
            }
        }

        fun updateInstallmentConfiguration(goal: Goal, installmentFrequency: InstallmentFrequency?, installmentExpirationDay: String?, actionSource: ActionSource.WalletActionSource): Either<UpdateGoalError, Pair<Goal, Boolean>> {
            val validInstallmentFrequency = installmentFrequency ?: goal.installments.frequency
            val validInstallmentExpirationDay = installmentExpirationDay ?: goal.installments.expirationDay

            return if (validInstallmentFrequency != goal.installments.frequency || validInstallmentExpirationDay != goal.installments.expirationDay) {
                if (!goal.canBeUpdated()) {
                    return UpdateGoalError.InvalidGoalStatus.left()
                }

                val installments = GoalInstallments.buildGoalInstallment(validInstallmentFrequency, goal.installments.amount, validInstallmentExpirationDay)

                val investments = goalInvestmentService.findAll(goal.id).filter { it.paidAt == null && !it.extraInstallment }
                goalInvestmentService.ignoreInvestments(goal, investments, actionSource).getOrElse { return it.left() }
                goalInvestmentService.create(CreateRegularGoalInvestmentCommand(goalId = goal.id)).getOrElse { return UpdateGoalError.ErrorCreatingInvestment(it).left() }

                (goalRepository.save(goal.copy(installments = installments)) to true).right()
            } else {
                (goal to false).right()
            }
        }

        var goal = findGoal(command.goalId).getOrElse { return UpdateGoalError.GenericException(it).left() }

        if (command.actionSource.accountId == null) {
            return UpdateGoalError.InvalidGoalStatus.left()
        }

        updateName(goal = goal, command.name).map {
            goal = it.first
            it.second
        }.getOrElse { return it.left() }

        val hasUpdatedAmount = updateAmount(goal = goal, command.amount).map {
            goal = it.first
            it.second
        }.getOrElse { return it.left() }

        val hasUpdatedEndDate = updateEndDate(goal = goal, command.endDate, command.actionSource).map {
            goal = it.first
            it.second
        }.getOrElse { return it.left() }

        val hasUpdatedInstallments = updateInstallmentConfiguration(goal = goal, command.installmentFrequency, command.installmentExpirationDay, command.actionSource).map {
            goal = it.first
            it.second
        }.getOrElse { return it.left() }

        if (hasUpdatedAmount || hasUpdatedEndDate || hasUpdatedInstallments) {
            goalMessagePublisher.publishOptimizeGoalInstallmentAmount(goal.id)
        }

        return goal.right()
    }

    private fun getDetailedGoal(goal: Goal, userGoalPositions: UserGoalPositions?): Either<Exception, DetailedGoal> {
        val logName = "GoalService#getDetailedGoal"
        val markers = Markers.append("goalId", goal.id.value)

        val product = goalProductService.findProduct(goal.productId).getOrElse {
            return it.left()
        }

        markers.andAppend("accountId", goal.accountId.value)

        val statements = goalStatementService.getValidGoalStatements(goal.accountId, goal.id).getOrElse {
            logger.error(markers, logName, it)
            return it.left()
        }

        val savingsReferenceValue = userGoalPositions?.let {
            val savingsReferenceRatio = goalStatementService.calcSavingsToCDIRate(
                startDate = goal.createdAt.toLocalDate(),
                indexRelativeRate = product.indexIncome,
            ).getOrElse {
                logger.error(markers, logName, it)
                null
            }
            markers.andAppend("savingsReferenceRatio", savingsReferenceRatio)
            savingsReferenceRatio?.let { savingsReferenceRatio.toBigDecimal().divide(100L.toBigDecimal(), 6, RoundingMode.HALF_EVEN).multiply(userGoalPositions.netValue.toBigDecimal()).toLong() }
        }
        markers.andAppend("savingsReferenceValue", savingsReferenceValue)

        return DetailedGoal(
            goal = goal,
            balance = userGoalPositions?.toGoalBalance(savingsReferenceValue),
            statementItems = statements,
            positions = userGoalPositions?.positions,
        ).right()
    }

    open fun findGoal(goalId: GoalId): Either<GoalNotFoundException, Goal> {
        return goalRepository.find(goalId)
    }

    open fun findGoal(goalId: GoalId, accountId: AccountId?): Either<GoalNotFoundException, Goal> {
        return goalRepository.find(goalId).map { goal ->
            if (goal.accountId != accountId) {
                return GoalNotFoundException(goalId).left()
            }
            goal
        }
    }

    open fun findGoalsEligibleToOptimization(installmentFrequency: InstallmentFrequency): List<Goal> {
        val optimizedSince = when (installmentFrequency) {
            InstallmentFrequency.WEEKLY -> getLocalDate().minusWeeks(1)
            InstallmentFrequency.MONTHLY -> getLocalDate().minusMonths(1)
        }
        return goalRepository.findGoalsToOptimize(
            optimizedSince = optimizedSince,
        ).filter { it.installments.frequency == installmentFrequency }
    }

    open fun resetGoalInstallmentAmountAndUpdateGoalAmount(goalId: GoalId, notify: Boolean): Either<Exception, Boolean> {
        val markers = append("goalId", goalId.value)
        val logName = "GoalService#resetGoalInstallmentAmountAndUpdateGoalAmount"

        val goal = findGoal(goalId).getOrElse {
            return it.left()
        }

        markers.andAppend("accountId", goal.accountId.value)

        if (!goal.isOptimizeable()) {
            return false.right()
        }

        val actionSourceGoalInvestment = ActionSource.GoalInvestment(
            accountId = goal.accountId,
            goalId = goal.id,
        )

        val goalProduct = goalProductService.findProduct(goal.productId).getOrElse {
            logger.error(markers, logName, it)
            return it.left()
        }

        val userGoalPosition = investmentManagerService.userGoalPositions(goal.accountId, goal.id).getOrElse {
            logger.error(markers, logName, it)
            return it.left()
        }

        val installmentInitialAmount = goal.installmentInitialAmount

        val currentGrossValue = userGoalPosition.grossValue
        val dateToComplete = goal.endDate
        val index = goalProduct.index
        val indexPercentage = goalProduct.indexIncome

        markers
            .andAppend("currentInstallmentValue", goal.installmentOptimizedAmount)
            .andAppend("currentGrossValue", currentGrossValue)
            .andAppend("dateToComplete", dateToComplete)
            .andAppend("index", index)
            .andAppend("indexPercentage", indexPercentage)

        val investmentRequests = goalInvestmentService.findAll(goalId)
        val extraNotPaidAndNotExpiredAmount = if (goal.createdAt >= getZonedDateTime().minusDays(7)) {
            investmentRequests.filter { it.extraInstallment && it.paidAt == null && it.dueDate >= getLocalDate().minusDays(7) && it.status != GoalInvestmentStatus.CANCELED }.sumOf { it.amount }
        } else {
            0
        }

        val regularNotPaidInvestments = investmentRequests.filter { !it.extraInstallment && it.paidAt == null && it.status != GoalInvestmentStatus.CANCELED }
        val nextInvestmentDueDate = regularNotPaidInvestments.minByOrNull { it.dueDate }?.dueDate ?: getLocalDate()

        markers
            .andAppend("extraNotPaidAndNotExpiredAmount", extraNotPaidAndNotExpiredAmount)

        val futureNetValue = investmentManagerService.simulateFutureNetValue(
            currentGrossAmount = currentGrossValue + extraNotPaidAndNotExpiredAmount,
            nextPaymentDate = nextInvestmentDueDate,
            dateToComplete = dateToComplete,
            installmentAmount = installmentInitialAmount,
            installmentFrequency = goal.installments.frequency,
            index = index,
            indexPercentage = indexPercentage,
        ).getOrElse {
            logger.error(markers, logName, it)
            return it.left()
        }
        markers
            .andAppend("futureNetValue", futureNetValue)

        val expiredInvestments = regularNotPaidInvestments.filter { it.dueDate < getLocalDate() }
        markers
            .andAppend("expiredInvestments", expiredInvestments.map { it.billId })

        val oldValue = goal.installmentOptimizedAmount ?: installmentInitialAmount
        val percChange = abs(oldValue - installmentInitialAmount).toDouble() / installmentInitialAmount

        markers.andAppend("percChange", percChange)

        val shouldUpdate = percChange > 0.05

        val (updatedGoal, hasUpdatedAmount) = if (shouldUpdate) {
            val goalInvestmentsToUpdate = regularNotPaidInvestments.filter { it.status == GoalInvestmentStatus.CREATED }

            goalInvestmentsToUpdate.forEach { goalInvestment ->
                goalInvestmentService.updateInvestmentAmount(goalInvestment, amount = installmentInitialAmount)

                updateBillService.updateAmount(
                    billId = goalInvestment.billId,
                    amount = installmentInitialAmount,
                    actionSource = actionSourceGoalInvestment,
                )
            }
            markers.andAppend("updatedBills", goalInvestmentsToUpdate.map { it.billId }).andAppend("updatedBillsTotal", goalInvestmentsToUpdate.size)
            goalRepository.save(goal.copy(lastValueOptimizationAt = getZonedDateTime(), installmentOptimizedAmount = installmentInitialAmount, amount = futureNetValue)) to true
        } else {
            goal to false
        }

        if (hasUpdatedAmount && notify) {
            goalsInvestmentNotificationService.notifyInvestmentValueOptmized(updatedGoal, oldValue = oldValue, optimizedValue = installmentInitialAmount)
        }

        logger.info(markers.andAppend("hasUpdatedAmount", hasUpdatedAmount).andAppend("hasIncreasedAmount", installmentInitialAmount > oldValue).andAppend("hasDecreasedAmount", installmentInitialAmount < oldValue), logName)
        return hasUpdatedAmount.right()
    }

    open fun optimizeGoalInstallmentAmount(goalId: GoalId, shouldNotify: Boolean = true, shouldForce: Boolean = false): Either<Exception, Boolean> {
        val markers = append("goalId", goalId.value)
        val logName = "GoalService#optimizeGoalInstallmentAmount"

        val goal = findGoal(goalId).getOrElse {
            return it.left()
        }
        markers.andAppend("accountId", goal.accountId.value)

        if (!goal.isOptimizeable()) {
            return false.right()
        }

        val actionSourceGoalInvestment = ActionSource.GoalInvestment(
            accountId = goal.accountId,
            goalId = goal.id,
        )

        val goalProduct = goalProductService.findProduct(goal.productId).getOrElse {
            logger.error(markers, logName, it)
            return it.left()
        }

        val userGoalPosition = investmentManagerService.userGoalPositions(goal.accountId, goal.id).getOrElse {
            logger.error(markers, logName, it)
            return it.left()
        }

        val currentInstallmentValue = goal.installmentOptimizedAmount ?: goal.installmentInitialAmount

        val currentGrossValue = userGoalPosition.grossValue
        val dateToComplete = goal.endDate
        val goalNetValue = goal.amount
        val index = goalProduct.index
        val indexPercentage = goalProduct.indexIncome

        markers
            .andAppend("currentInstallmentValue", currentInstallmentValue)
            .andAppend("currentGrossValue", currentGrossValue)
            .andAppend("dateToComplete", dateToComplete)
            .andAppend("goalNetValue", goalNetValue)
            .andAppend("index", index)
            .andAppend("indexPercentage", indexPercentage)

        val investmentRequests = goalInvestmentService.findAll(goalId)
        val extraNotPaidAndNotExpiredAmount = if (goal.createdAt >= getZonedDateTime().minusDays(7)) {
            investmentRequests.filter { it.extraInstallment && it.paidAt == null && it.dueDate >= getLocalDate().minusDays(7) && it.status != GoalInvestmentStatus.CANCELED }.sumOf { it.amount }
        } else {
            0
        }

        val regularNotPaidInvestments = investmentRequests.filter { !it.extraInstallment && it.paidAt == null && it.status != GoalInvestmentStatus.CANCELED }
        val nextInvestmentDueDate = regularNotPaidInvestments.minByOrNull { it.dueDate }?.dueDate ?: getLocalDate()

        markers
            .andAppend("extraNotPaidAndNotExpiredAmount", extraNotPaidAndNotExpiredAmount)

        val futureNetValue = investmentManagerService.simulateFutureNetValue(
            currentGrossAmount = currentGrossValue + extraNotPaidAndNotExpiredAmount,
            nextPaymentDate = nextInvestmentDueDate,
            dateToComplete = dateToComplete,
            installmentAmount = currentInstallmentValue,
            installmentFrequency = goal.installments.frequency,
            index = index,
            indexPercentage = indexPercentage,
        ).getOrElse {
            logger.error(markers, logName, it)
            return it.left()
        }
        markers
            .andAppend("futureNetValue", futureNetValue)

        val optimizedInstallmentValue = investmentManagerService.optimizeMonthlyContribution(
            currentGrossAmount = currentGrossValue + extraNotPaidAndNotExpiredAmount,
            nextPaymentDate = nextInvestmentDueDate,
            dateToComplete = goal.endDate,
            goalNetAmount = goalNetValue,
            installmentFrequency = goal.installments.frequency,
            index = index,
            indexPercentage = indexPercentage,
        ).getOrElse {
            logger.error(markers, logName, it)
            return it.left()
        }
        markers
            .andAppend("optimizedInstallmentValue", optimizedInstallmentValue)

        val difference = abs(futureNetValue - goalNetValue)
        val perc = (difference.toDouble() / goalNetValue.toDouble())
        markers
            .andAppend("difference", difference)
            .andAppend("perc", perc)

        val roundedAndOptimizedValue = optimizedInstallmentValue.roundToAllowedInvestmentAmount()
        markers
            .andAppend("roundedAndOptimizedValue", roundedAndOptimizedValue)

        val expiredInvestments = regularNotPaidInvestments.filter { it.dueDate < getLocalDate() }
        markers
            .andAppend("expiredInvestments", expiredInvestments.map { it.billId })

        val shouldUpdate = perc >= 0.05 && roundedAndOptimizedValue != currentInstallmentValue
        val (updatedGoal, hasUpdatedAmount) = if (shouldForce || shouldUpdate) {
            val goalInvestmentsToUpdate = regularNotPaidInvestments.filter { it.status == GoalInvestmentStatus.CREATED }

            goalInvestmentsToUpdate.forEach { goalInvestment ->
                goalInvestmentService.updateInvestmentAmount(goalInvestment, amount = roundedAndOptimizedValue)

                updateBillService.updateAmount(
                    billId = goalInvestment.billId,
                    amount = roundedAndOptimizedValue,
                    actionSource = actionSourceGoalInvestment,
                )
            }
            markers.andAppend("updatedBills", goalInvestmentsToUpdate.map { it.billId }).andAppend("updatedBillsTotal", goalInvestmentsToUpdate.size)
            goalRepository.save(goal.copy(lastValueOptimizationAt = getZonedDateTime(), installmentOptimizedAmount = roundedAndOptimizedValue)) to true
        } else {
            goalRepository.save(goal.copy(lastValueOptimizationAt = getZonedDateTime())) to false
        }

        if (expiredInvestments.isNotEmpty()) {
            goalInvestmentService.ignoreInvestments(goal, expiredInvestments, actionSourceGoalInvestment)
            goalInvestmentService.create(CreateRegularGoalInvestmentCommand(goalId = goal.id))
        }

        if (hasUpdatedAmount && shouldNotify) {
            goalsInvestmentNotificationService.notifyInvestmentValueOptmized(updatedGoal, oldValue = currentInstallmentValue, optimizedValue = roundedAndOptimizedValue)
        }

        logger.info(markers.andAppend("hasUpdatedAmount", hasUpdatedAmount).andAppend("hasIncreasedAmount", roundedAndOptimizedValue > currentInstallmentValue).andAppend("hasDecreasedAmount", roundedAndOptimizedValue < currentInstallmentValue), logName)
        return hasUpdatedAmount.right()
    }

    open fun calculateCurrentPenaltyRate(goal: Goal): Either<Exception, FixedIncomeIndexRate?> {
        val logName = "GoalService#calculateCurrentPenaltyRate"
        val markers = Markers.append("goalId", goal.id.value)

        val goalPenaltyRates = calculateGoalPenaltyRates(goal).getOrElse { return it.left() }
        markers.andAppend("goalPenaltyRates", goalPenaltyRates)

        return goalPenaltyRates.firstOrNull { it.limitDate >= getLocalDate() }?.penaltyRate.also {
            markers.andAppend("goalPenaltyRate", it)
            logger.info(markers, logName)
        }.right()
    }

    fun calculateGoalPenaltyRates(goalId: GoalId, accountId: AccountId): Either<Exception, List<GoalPenaltyRate>> {
        return findGoal(goalId, accountId).map { calculateGoalPenaltyRates(it) }.getOrElse { it.left() }
    }

    fun calculateGoalPenaltyRates(goal: Goal): Either<Exception, List<GoalPenaltyRate>> {
        if (goal.liquidity == GoalProductLiquidity.DAILY) {
            return emptyList<GoalPenaltyRate>().right()
        }

        val startDate = goal.createdAt.toLocalDate()
        val totalDays = ChronoUnit.DAYS.between(startDate, goal.endDate)

        val penaltiesConfig = listOf(
            0.75 to 0,
            0.9 to 100,
            1.0 to 103,
        ).map { (goalAchievement, penaltyRate) ->
            val penalty = if (penaltyRate == 0) {
                goalStatementService.calcSavingsToCDIRate(
                    startDate = startDate,
                    indexRelativeRate = FixedIncomeIndexRate(100),
                ).map {
                    FixedIncomeIndexRate(it.coerceAtMost(80))
                }.getOrElse {
                    return it.left()
                }
            } else {
                FixedIncomeIndexRate(penaltyRate)
            }

            val daysToComplete = (totalDays * goalAchievement).toBigDecimal().setScale(0, RoundingMode.CEILING).toLong()

            GoalPenaltyRate(
                limitDate = startDate.plusDays(daysToComplete - 1),
                penaltyRate = penalty,
            )
        }

        return penaltiesConfig.sortedBy { it.limitDate }.right()
    }

    override fun getImageUrl(id: GoalId): String? {
        return goalRepository.findOrNull(id)?.imageUrl
    }

    override fun urlProviderFor(): Class<GoalId> {
        return GoalId::class.java
    }
}

private fun List<DetailedGoal>.totalBalance(): GoalBalance? {
    return this.map { it.balance }.fold(
        GoalBalance(0, 0, 0, 0, 0, 0, 0),
    ) { acc, balance ->
        if (balance == null) {
            return null
        }
        acc + balance
    }
}

private fun UserGoalPositions.toGoalBalance(savingsReferenceValue: Long?): GoalBalance {
    return GoalBalance(
        netValue = netValue,
        grossValue = grossValue,
        investedValue = investedValue,
        irValue = irValue,
        iofValue = iofValue,
        earningValue = earningValue,
        savingsReferenceValue = savingsReferenceValue,
    )
}

sealed class CreateGoalError : PrintableSealedClassV2() {
    data object MustUpgradeAccount : CreateGoalError()
    data class UnableToFindProduct(val reason: SelectGoalProductError) : CreateGoalError()
    data class CouldNotEnableFixedIncomeAccount(val message: String) : CreateGoalError()
    data class InvalidInstallmentConfiguration(val installmentFrequency: InstallmentFrequency, val installmentExpirationDay: String) : CreateGoalError()
    data class InvalidGoalCategory(val goalCategory: GoalCategory?, val reason: String) : CreateGoalError()
}

fun CreateGoalCommand.toGoal(walletId: WalletId, product: GoalProduct) = Goal(
    id = GoalId(),
    accountId = accountId,
    walletId = walletId,
    categoryId = categoryId,
    name = name,
    amount = amount,
    endDate = endDate,
    liquidity = liquidity,
    installments = GoalInstallments.buildGoalInstallment(installmentFrequency, installmentAmount, installmentExpirationDay),
    imageUrl = imageUrl,
    productId = product.id,
    installmentInitialAmount = installmentAmount,
    installmentOptimizedAmount = null,
    lastValueOptimizationAt = null,
    status = GoalStatus.ACTIVE,
    lastKnownNetBalanceAmount = 0,
    lastKnownNetBalanceAt = getZonedDateTime(),
)

private fun GoalStatus.order() = when (this) {
    GoalStatus.ACTIVE -> 0
    GoalStatus.PAUSED -> 1
    GoalStatus.COMPLETED -> 2
    GoalStatus.REMOVED -> 3
}

private fun GoalProductLiquidity.order() = when (this) {
    GoalProductLiquidity.DAILY -> 1
    GoalProductLiquidity.MATURITY -> 2
}

private fun GoalCategoryType.order() = when (this) {
    GoalCategoryType.EMERGENCY_FUND -> 0
    GoalCategoryType.SHOPPING -> 1
    GoalCategoryType.EDUCATION -> 1
    GoalCategoryType.TRAVEL -> 1
    GoalCategoryType.PARTY -> 1
    GoalCategoryType.OTHER -> 1
    GoalCategoryType.HOME -> 1
    GoalCategoryType.FINANCIAL_INDEPENDENCE -> 1
}