package ai.friday.billpayment.modules.investmentGoals.app.investment

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb.GoalDbRepository
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@InvestmentGoalsModule
class ClosedAccountsWithInvestmentsReportService(
    private val accountRepository: AccountRepository,
    private val goalRepository: GoalDbRepository,
    private val fixedIncomeManagerProvider: FixedIncomeManagerProvider,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun check() {
        val logName = "ListClosedAccountsWithInvestmentsTest#check"

        logger.info(Markers.append("status", "INICIO"), logName)

        val goalsByAccountId = goalRepository.findAllGoals().groupBy { it.accountId }

        logger.info(Markers.append("Total de contas", goalsByAccountId.keys.size), logName)

        goalsByAccountId.keys.forEach { accountId ->
            val account = accountRepository.findById(accountId)
            val markers = Markers.append("accountId", accountId).andAppend("cpf", account.document)

            if (account.status == AccountStatus.CLOSED) {
                fixedIncomeManagerProvider.userPositions(accountId).map { positions ->
                    if (positions.isNotEmpty()) {
                        val report = ClosedAccountsWithInvestmentsReportData(
                            accountId = account.accountId,
                            cpf = account.document,
                            positions = positions.map { userGoalPositions -> userGoalPositions.positions.map { it.positionId } }.flatten(),
                            netBalance = positions.sumOf { userGoalPositions -> userGoalPositions.netValue },
                        )
                        logger.info(markers.andAppend("report", report), logName)
                    }
                }.getOrElse {
                    logger.error(markers, logName, it)
                }
            }
        }

        logger.info(Markers.append("status", "FIM"), logName)
    }
}

private data class ClosedAccountsWithInvestmentsReportData(
    val accountId: AccountId,
    val cpf: String,
    val positions: List<String>,
    val netBalance: Long,
)