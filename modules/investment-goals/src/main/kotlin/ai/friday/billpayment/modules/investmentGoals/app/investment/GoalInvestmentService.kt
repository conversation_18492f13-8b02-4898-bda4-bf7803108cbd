package ai.friday.billpayment.modules.investmentGoals.app.investment

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillNotIgnorableException
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateInvestmentRequest
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.bill.schedule.CancelSchedulePaymentService
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.pfm.CreateWalletBillCategoryError
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.bill.CreateInvestmentBillServiceInterface
import ai.friday.billpayment.modules.investmentGoals.app.goal.Goal
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalInstallments
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalRepository
import ai.friday.billpayment.modules.investmentGoals.app.goal.UpdateGoalError
import ai.friday.billpayment.modules.investmentGoals.app.roundToAllowedInvestmentAmount
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@InvestmentGoalsModule
open class GoalInvestmentService(
    private val goalRepository: GoalRepository,
    private val createInvestmentBillService: CreateInvestmentBillServiceInterface,
    private val walletCategoryService: PFMWalletCategoryService,
    private val goalInvestmentRepository: GoalInvestmentRepository,
    private val walletRepository: WalletRepository,
    private val updateBillService: UpdateBillService,
    private val cancelSchedulePaymentService: CancelSchedulePaymentService,
) {
    private val logger = LoggerFactory.getLogger(GoalInvestmentService::class.java)

    private fun createRegularInvestmentRequest(goal: Goal, regularInvestments: List<GoalInvestment>, pfmCategoryId: PFMCategoryId): Either<CreateGoalInvestmentError, CreateInvestmentRequest> {
        if (!goal.canCreateRegularInvestment()) {
            return CreateGoalInvestmentError.GoalCantHaveAnyNewInvestment.left()
        }

        val lastRegularInstallment = regularInvestments.maxByOrNull { it.dueDate }
        if (lastRegularInstallment != null && lastRegularInstallment.paidAt == null) {
            return CreateGoalInvestmentError.RegularInvestmentActive(lastRegularInstallment).left()
        }
        val (dueDate, installmentNo) = GoalInstallments.calcNextInstallment(goal.createdAt.toLocalDate(), lastRegularInstallment?.dueDate, goal.installments)

        val totalInstallmentsNo = GoalInstallments.calculateTotalInstallments(
            knownInstallmentDueDate = dueDate,
            knownInstallmentNo = installmentNo,
            frequency = goal.installments,
            endDate = goal.endDate,
        )

        return CreateInvestmentRequest(
            goalId = goal.id,
            walletId = goal.walletId,
            categoryId = pfmCategoryId,
            amount = goal.installments.amount.roundToAllowedInvestmentAmount(),
            dueDate = dueDate,
            description = "Parcela $installmentNo / $totalInstallmentsNo",
            source = ActionSource.GoalInvestment(
                accountId = goal.accountId,
                goalId = goal.id,
            ),
        ).right()
    }

    private fun createExtraInvestmentRequest(goal: Goal, extraInvestments: List<GoalInvestment>, pfmCategoryId: PFMCategoryId, extraInstallmentAmount: Long): Either<CreateGoalInvestmentError, CreateInvestmentRequest> {
        if (!goal.canCreateExtraInvestment()) {
            return CreateGoalInvestmentError.GoalCantHaveAnyNewInvestment.left()
        }

        val activeInvestment = extraInvestments.firstOrNull { it.paidAt == null }
        if (activeInvestment != null) {
            return CreateGoalInvestmentError.ExtraInvestmentActive(activeInvestment).left()
        }

        val dueDate = getLocalDate()

        return CreateInvestmentRequest(
            goalId = goal.id,
            walletId = goal.walletId,
            categoryId = pfmCategoryId,
            amount = extraInstallmentAmount.roundToAllowedInvestmentAmount(),
            dueDate = dueDate,
            description = "Parcela extra ${extraInvestments.size + 1}",
            source = ActionSource.GoalInvestment(
                accountId = goal.accountId,
                goalId = goal.id,
            ),
        ).right()
    }

    open fun findAll(goalId: GoalId): List<GoalInvestment> {
        return goalInvestmentRepository.findByGoalId(goalId)
    }

    open fun findByBillId(billId: BillId): GoalInvestment? {
        return goalInvestmentRepository.findByBillId(billId)
    }

    open fun create(command: CreateGoalInvestmentCommand, amount: Long? = null): Either<CreateGoalInvestmentError, Unit> {
        val markers = append("command", command)

        val goal = goalRepository.find(command.goalId).getOrElse {
            logger.error(markers, "GoalInvestmentService#create")
            return CreateGoalInvestmentError.GenericException(it).left()
        }

        val pfmCategoryId = getPFMGoalCategoryId(goal.walletId).getOrElse {
            logger.error(markers.andAppend("error", it), "GoalInvestmentService#create")
            return CreateGoalInvestmentError.CouldNotAssignPFMCategory.left()
        }
        markers.andAppend("pfmCategoryId", pfmCategoryId)

        val (regularInvestments, extraInvestments) = goalInvestmentRepository.findByGoalId(goal.id).filter { it.status != GoalInvestmentStatus.CANCELED }.partition { !it.extraInstallment }

        markers.andAppend("regularInvestments", regularInvestments.size).andAppend("extraInvestments", extraInvestments.size)

        val createInvestmentRequest = when (command) {
            is CreateRegularGoalInvestmentCommand -> {
                createRegularInvestmentRequest(goal, regularInvestments, pfmCategoryId)
            }

            is CreateExtraGoalInvestmentCommand -> {
                if (command.accountId != null && command.accountId != goal.accountId) {
                    logger.warn(markers.andAppend("error", "accountIdMismatch"), "GoalInvestmentService#create")
                    return CreateGoalInvestmentError.GoalNotFound.left()
                }
                createExtraInvestmentRequest(goal, extraInvestments, pfmCategoryId, command.extraInstallmentAmount)
            }
        }.getOrElse { error ->
            when (error) {
                is CreateGoalInvestmentError.RegularInvestmentActive -> {
                    markers.andAppend("activeInvestment", error.goalInvestment)
                }

                is CreateGoalInvestmentError.ExtraInvestmentActive -> {
                    markers.andAppend("activeInvestment", error.goalInvestment)
                }

                else -> {}
            }
            logger.warn(markers.andAppend("error", error), "GoalInvestmentService#create")
            return error.left()
        }

        val createBillResult = createInvestmentBillService.createInvestment(
            request = createInvestmentRequest,
            dryRun = false,
        )

        return when (createBillResult) {
            is CreateBillResult.FAILURE -> CreateGoalInvestmentError.CouldNotCreateBill(createBillResult).left()
            is CreateBillResult.SUCCESS -> {
                markers.andAppend("billId", createBillResult.bill.billId.value).andAppend("dueDate", createBillResult.bill.dueDate)
                goalInvestmentRepository.save(
                    GoalInvestment(
                        goalId = goal.id,
                        billId = createBillResult.bill.billId,
                        dueDate = createInvestmentRequest.dueDate,
                        amount = createInvestmentRequest.amount,
                        paidAt = null,
                        extraInstallment = command.extraInstallment,
                        status = GoalInvestmentStatus.CREATED,
                    ),
                )
                Unit.right()
            }
        }.also { result ->
            result.map {
                logger.info(markers, "GoalInvestmentService#create")
            }.getOrElse {
                logger.warn(markers.andAppend("error", it), "GoalInvestmentService#create")
            }
        }
    }

    fun getPFMGoalCategoryId(walletId: WalletId): Either<CreateWalletBillCategoryError, PFMCategoryId> {
        val existingCategory = walletCategoryService.findWalletCategories(walletId).firstOrNull {
            it.enabled && it.icon == "META"
        }

        return existingCategory?.categoryId?.right() ?: walletCategoryService.create(
            walletId = walletId,
            name = "Meta",
            icon = "META",
        ).map {
            it.categoryId.right()
        }.getOrElse {
            it.left()
        }
    }

    fun ignoreAllGoalInvestments(goalId: GoalId, actionSource: ActionSource): Either<UpdateGoalError, Unit> {
        val logName = "GoalInvestmentService#ignoreAllOpenBillsOnGoal"
        val markers = append("goalId", goalId.value)

        val goal = goalRepository.find(goalId = goalId).getOrElse {
            logger.error(markers, logName, it)
            return UpdateGoalError.GenericException(it).left()
        }

        val goalInvestments = findAll(goal.id)
        val processingInvestment = goalInvestments.firstOrNull { it.status == GoalInvestmentStatus.PROCESSING }
        if (processingInvestment != null) {
            markers.andAppend("processingInvestmentId", processingInvestment.id.value)
            logger.warn(markers, logName)
            return UpdateGoalError.InvestmentProcessing(processingInvestment.id).left()
        }

        val goalInvestmentsToIgnore = goalInvestments.filter {
            when (it.status) {
                GoalInvestmentStatus.CREATED, GoalInvestmentStatus.FAILED -> true
                GoalInvestmentStatus.PROCESSING, GoalInvestmentStatus.CANCELED, GoalInvestmentStatus.DONE -> false
            }
        }

        ignoreInvestments(goal, goalInvestmentsToIgnore, actionSource)

        return Unit.right()
    }

    fun ignoreInvestments(goal: Goal, investmentsToBeIgnored: List<GoalInvestment>, actionSource: ActionSource): Either<UpdateGoalError, Unit> {
        val markers = append("goalId", goal.id.value).andAppend("accountId", goal.accountId.value).andAppend("walletId", goal.walletId.value)
        if (investmentsToBeIgnored.isEmpty()) {
            logger.info(markers.andAppend("context", "listIsEmpty"), "GoalInvestmentService#ignoreInvestments")
            return Unit.right()
        }
        val wallet = walletRepository.findWallet(goal.walletId)
        val member = wallet.getMember(goal.accountId)

        investmentsToBeIgnored.forEach { goalInvestment ->
            val shouldIgnore = when (goalInvestment.status) {
                GoalInvestmentStatus.PROCESSING -> return UpdateGoalError.InvestmentProcessing(goalInvestment.id).left()
                GoalInvestmentStatus.CREATED, GoalInvestmentStatus.FAILED -> true
                GoalInvestmentStatus.DONE, GoalInvestmentStatus.CANCELED -> false
            }

            if (shouldIgnore) {
                markers.andAppend("ignoreBillId", goalInvestment.billId.value)
                goalInvestment.copy(status = GoalInvestmentStatus.CANCELED).also {
                    goalInvestmentRepository.save(it)
                }
                cancelSchedulePaymentService.userCancelScheduledPayment(
                    walletId = wallet.id,
                    billId = goalInvestment.billId,
                    member = member,
                    actionSource = actionSource,
                )
                updateBillService.ignoreBill(
                    billId = goalInvestment.billId,
                    walletId = goal.walletId,
                    member = member,
                    actionSource = actionSource,
                ).getOrElse { error ->
                    when (error) {
                        is BillNotIgnorableException -> {
                            logger.warn(markers.andAppend("billStatus", error.bill.status), "GoalInvestmentService#ignoreInvestments", error)
                        }

                        else -> {
                            logger.error(markers, "GoalInvestmentService#ignoreInvestments", error)
                        }
                    }
                }
                logger.info(markers, "GoalInvestmentService#ignoreInvestments")
            }
        }
        return Unit.right()
    }

    fun updateInvestmentAmount(goalInvestment: GoalInvestment, amount: Long) {
        goalInvestmentRepository.save(goalInvestment.copy(amount = amount))
    }
}

sealed interface CreateGoalInvestmentCommand {
    val goalId: GoalId
    val extraInstallment: Boolean
}

data class CreateRegularGoalInvestmentCommand(
    override val goalId: GoalId,
) : CreateGoalInvestmentCommand {
    override val extraInstallment = false
}

data class CreateExtraGoalInvestmentCommand(
    override val goalId: GoalId,
    val extraInstallmentAmount: Long,
    val accountId: AccountId?,
) : CreateGoalInvestmentCommand {
    override val extraInstallment = true
}

sealed class CreateGoalInvestmentError(val retryable: Boolean) : PrintableSealedClassV2() {
    data object GoalNotFound : CreateGoalInvestmentError(false)
    data class RegularInvestmentActive(val goalInvestment: GoalInvestment) : CreateGoalInvestmentError(false)
    data object GoalCantHaveAnyNewInvestment : CreateGoalInvestmentError(false)
    data class ExtraInvestmentActive(val goalInvestment: GoalInvestment) : CreateGoalInvestmentError(false)
    data object CouldNotAssignPFMCategory : CreateGoalInvestmentError(true)
    data class CouldNotCreateBill(val failure: CreateBillResult.FAILURE) : CreateGoalInvestmentError(true)
    data class GenericException(val exception: Exception) : CreateGoalInvestmentError(true)
}