package ai.friday.billpayment.modules.investmentGoals.adapters.job

import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModuleNoTest
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalRepository
import ai.friday.billpayment.modules.investmentGoals.app.messaging.GoalMessagePublisher
import io.micronaut.context.annotation.Property
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@InvestmentGoalsModuleNoTest
open class CheckAndCompleteGoalsJob(
    private val goalRepository: GoalRepository,
    private val goalMessagePublisher: GoalMessagePublisher,
    @Property(name = "schedules.checkAndCompleteGoalsJob.cron") cron: String,
) : AbstractJob(cron = cron) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun execute() {
        val logName = "CheckAndCompleteGoalsJob#execute"

        goalRepository.findGoalsCloseToCompletion().forEach { goal ->
            val marker = append("goalId", goal.id.value)
            goalMessagePublisher.publishCheckGoalCompletion(goal.id, false)
            logger.info(marker, logName)
        }
        logger.info(append("finished", true), logName)
    }
}