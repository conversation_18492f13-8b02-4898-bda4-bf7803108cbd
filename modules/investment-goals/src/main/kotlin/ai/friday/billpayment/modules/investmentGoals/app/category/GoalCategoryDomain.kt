package ai.friday.billpayment.modules.investmentGoals.app.category

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.time.ZonedDateTime
import java.util.UUID

interface GoalCategoryRepository {
    fun save(category: GoalCategory): GoalCategory

    fun findAll(): List<GoalCategory>

    fun findOrNull(id: GoalCategoryId): GoalCategory?
}

data class GoalCategoryId(
    val value: String,
) {
    constructor() : this("GOAL-CATEGORY-${UUID.randomUUID()}")
}

enum class GoalCategoryType {
    EMERGENCY_FUND,
    SHOPPING,
    EDUCATION,
    TRAVEL,
    PARTY,
    OTHER,
    HOME,
    FINANCIAL_INDEPENDENCE, ;

    fun canHaveMultiple(): Boolean {
        return this != EMERGENCY_FUND
    }

    fun canBeCompleted(): Boolean {
        return this != EMERGENCY_FUND
    }

    fun canBeAutoResumed(): Boolean {
        return !canBeCompleted()
    }
}

data class GoalCategory(
    val id: GoalCategoryId = GoalCategoryId(),
    val type: GoalCategoryType,
    val name: String,
    val imageUrl: String,
    val enabled: Boolean = true,
    val createdAt: ZonedDateTime = getZonedDateTime(),
    val updatedAt: ZonedDateTime = getZonedDateTime(),
)

data class SaveGoalCategoryCommand(
    val type: GoalCategoryType,
    val name: String,
    val imageUrl: String,
    val enabled: Boolean,
)

sealed class GoalCategoryError : PrintableSealedClassV2() {
    data object CategoryNotFound : GoalCategoryError()

    data object InvalidImageUrl : GoalCategoryError()

    data object CategoryNameAlreadyExists : GoalCategoryError()
}