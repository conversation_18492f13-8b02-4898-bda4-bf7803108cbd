package ai.friday.billpayment.modules.automaticpix.app.services

import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.automaticpix.adapters.arbi.ArbiAutomaticPixAdapter
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateId
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateRepository
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateStatus
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixNotPendingMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPendingMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixProvider
import ai.friday.billpayment.modules.automaticpix.app.CanceledBy
import ai.friday.billpayment.modules.automaticpix.app.ClientRejectionReason
import ai.friday.billpayment.modules.automaticpix.createAuthorization
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class AutomaticPixMandateServiceTest {

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet(otherMembers = listOf(walletFixture.cantPayParticipant))
    private val walletId = wallet.id
    private val accountId = wallet.founder.accountId

    private val bankAccount = mockk<InternalBankAccount> {
        every { buildFullAccountNumber() } returns "123456"
    }

    private val pendingAuthorizations = listOf(
        createAuthorization(walletId = walletId) as AutomaticPixPendingMandate,
        createAuthorization(mandateId = "ext-456", recurringPaymentId = "rec-456", walletId = walletId) as AutomaticPixPendingMandate,
    )

    private val arbiAutomaticPixAdapter: ArbiAutomaticPixAdapter = mockk {
        every { getMandates(walletId, "123456", null) } returns Result.success(emptyList())
        every { getPendingMandates(walletId, "123456") } returns Result.success(pendingAuthorizations.map { it })
    }

    private val automaticPixMandateRepository: AutomaticPixMandateRepository = mockk {
        every { findByWalletId(walletId, AutomaticPixMandateStatus.PENDING) } returns pendingAuthorizations
        every { findByWalletId(walletId, null) } returns pendingAuthorizations
        every { save(any()) } answers { firstArg() }
    }
    private val walletService: WalletService = mockk {
        every {
            findWallet(walletId)
        } returns wallet
    }
    private val accountService: AccountService = mockk {
        every { findAccountPaymentMethodByIdAndAccountId(wallet.paymentMethodId, wallet.founder.accountId) } returns mockk {
            every { method } returns bankAccount
        }
    }
    private val automaticPixMessagePublisher: AutomaticPixMessagePublisher = mockk {
        every { publishRecurreceRefresh(any()) } just Runs
        every { publishMandateRefresh(any()) } just Runs
        every { notifyMandatePending(any()) } just Runs
        every { notifyMandateStatusChange(any(), any()) } just Runs
    }

    private val service = AutomaticPixMandateService(
        automaticPixRemoteManager = arbiAutomaticPixAdapter,
        automaticPixMandateRepository = automaticPixMandateRepository,
        walletService = walletService,
        accountService = accountService,
        automaticPixMessagePublisher = automaticPixMessagePublisher,
    )

    @Nested
    inner class GetAllStoredMandates {
        @Test
        fun `deve retornar todos as autorizacoes do repositorio`() {
            every { automaticPixMandateRepository.findByWalletId(walletId, null) } returns pendingAuthorizations

            val result = service.getAllStoredMandates(walletId).getOrThrow()

            result shouldBe pendingAuthorizations

            verify(exactly = 1) { automaticPixMandateRepository.findByWalletId(walletId, null) }
            verify {
                arbiAutomaticPixAdapter wasNot Called
            }
        }
    }

    @Nested
    inner class RefreshPendingMandates {

        @Test
        fun `deve criar autorizacoes novas pendentes`() {
            every { walletService.findWallet(walletId) } returns wallet
            every { automaticPixMandateRepository.findByWalletId(walletId, any()) } returns emptyList()
            every { automaticPixMandateRepository.findByWalletId(walletId, AutomaticPixMandateStatus.PENDING) } returns emptyList()

            val result = service.refreshMandates(walletId).getOrThrow()

            result.size shouldBe 2

            verify(exactly = 1) { arbiAutomaticPixAdapter.getPendingMandates(walletId, "123456") }
            verify(exactly = 2) {
                automaticPixMandateRepository.save(
                    withArg {
                        it.status shouldBe AutomaticPixMandateStatus.PENDING
                    },
                )
            }
            verify(exactly = 2) { automaticPixMessagePublisher.notifyMandatePending(any()) }
        }

        @Test
        fun `deve atualizar as autorizacoes pendentes na base e nao encontradas no remoto como pendentes`() {
            every { walletService.findWallet(walletId) } returns wallet
            every { automaticPixMandateRepository.findByWalletId(walletId, any()) } returns emptyList()
            every { automaticPixMandateRepository.save(any()) } answers { firstArg() }
            every { arbiAutomaticPixAdapter.getPendingMandates(walletId, "123456") } returns Result.success(emptyList())
            every { arbiAutomaticPixAdapter.getMandates(walletId, "123456", null) } answers {
                Result.success(
                    pendingAuthorizations.map { pendingAuthorization ->
                        AutomaticPixNotPendingMandate(
                            walletId = walletId,
                            mandateId = pendingAuthorization.recurringPayment.mandateId!!,
                            status = AutomaticPixMandateStatus.CANCELED,
                            recurringPayment = pendingAuthorization.recurringPayment,
                            startDateValidity = null,
                            endDateValidity = null,
                            expiredValidityDate = null,
                            canceledAt = getZonedDateTime(),
                            canceledBy = CanceledBy.PAYEE,
                            provider = AutomaticPixProvider.ARBI,
                            expiresAt = null,
                        )
                    },
                )
            }

            every { automaticPixMandateRepository.findByWalletId(walletId, AutomaticPixMandateStatus.PENDING) } returns pendingAuthorizations
            every { automaticPixMandateRepository.updateStatus(any(), any()) } answers { firstArg<AutomaticPixMandate>().withNewStatus(secondArg()) }

            val result = service.refreshMandates(walletId).getOrThrow()

            result.size shouldBe 2

            verify(exactly = 1) {
                arbiAutomaticPixAdapter.getPendingMandates(walletId, "123456")
                arbiAutomaticPixAdapter.getMandates(walletId, "123456", null)
            }
            verify(exactly = 2) {
                automaticPixMandateRepository.save(any())
            }
            verify(exactly = 2) { automaticPixMessagePublisher.notifyMandateStatusChange(any(), AutomaticPixMandateStatus.CANCELED) }
        }

        @Test
        fun `deve notificar mudança de status quando autorização local não é encontrada no remoto`() {
            val localMandate = createAuthorization(walletId = walletId)
            every { walletService.findWallet(walletId) } returns wallet
            every { automaticPixMandateRepository.findByWalletId(walletId, any()) } returns listOf(localMandate)
            every { automaticPixMandateRepository.findByWalletId(walletId, AutomaticPixMandateStatus.PENDING) } returns emptyList()
            every { arbiAutomaticPixAdapter.getPendingMandates(walletId, "123456") } returns Result.success(emptyList())
            every { arbiAutomaticPixAdapter.getMandates(walletId, "123456", null) } returns Result.success(emptyList())
            every { automaticPixMandateRepository.updateStatus(any(), any()) } answers { firstArg<AutomaticPixMandate>().withNewStatus(secondArg()) }

            val result = service.refreshMandates(walletId).getOrThrow()

            result.size shouldBe 0

            verify(exactly = 1) { automaticPixMandateRepository.updateStatus(localMandate, AutomaticPixMandateStatus.CANCELED) }
            verify(exactly = 1) { automaticPixMessagePublisher.notifyMandateStatusChange(localMandate, AutomaticPixMandateStatus.CANCELED) }
        }

        @Test
        fun `deve falhar quando busca de mandatos remotos falha`() {
            every { arbiAutomaticPixAdapter.getMandates(walletId, "123456", null) } returns Result.failure(Exception("Erro na busca remota"))

            val result = service.refreshMandates(walletId)

            result.isFailure shouldBe true
            result.exceptionOrNull() shouldBe Exception("Erro na busca remota")
        }
    }

    @Nested
    inner class RejectAuthorization {

        @Test
        fun `deve rejeitar autorizacao com sucesso`() {
            val authorizationWallet = createAuthorization(walletId = walletId)
            val newStatus = AutomaticPixMandateStatus.CANCELED

            every { automaticPixMandateRepository.findByMandateIdAndWalletId(authorizationWallet.mandateId, authorizationWallet.walletId) } returns authorizationWallet
            every { arbiAutomaticPixAdapter.rejectMandate(authorizationWallet.mandateId, authorizationWallet.recurringPayment.recurringPaymentId, ClientRejectionReason.DECLINED) } returns Result.success(newStatus)
            every { automaticPixMandateRepository.findByMandateIdAndWalletId(authorizationWallet.mandateId, authorizationWallet.walletId) } returns authorizationWallet.withNewStatus(newStatus)

            service.rejectMandate(accountId, walletId, authorizationWallet.mandateId, ClientRejectionReason.DECLINED).getOrThrow()

            verify(exactly = 1) {
                arbiAutomaticPixAdapter.rejectMandate(authorizationWallet.mandateId, authorizationWallet.recurringPayment.recurringPaymentId, ClientRejectionReason.DECLINED)
                automaticPixMessagePublisher.publishRecurreceRefresh(any())
            }
        }

        @Test
        fun `deve retornar erro quando autorizacao nao for encontrada`() {
            every { automaticPixMandateRepository.findByMandateIdAndWalletId(any(), any()) } returns null

            val result = service.rejectMandate(accountId, walletId, AutomaticPixMandateId("ext-123"), ClientRejectionReason.DECLINED)

            result.isFailure shouldBe true
            result.exceptionOrNull()?.shouldBeTypeOf<IllegalArgumentException>()
            result.exceptionOrNull()?.message shouldBe "A autorização não encontrada."
        }

        @Test
        fun `deve retornar erro quando autorizacao nao pertence a carteira`() {
            val authorizationWallet = createAuthorization()
            val differentWalletId = WalletId("different-wallet")

            every { automaticPixMandateRepository.findByMandateIdAndWalletId(authorizationWallet.mandateId, authorizationWallet.walletId) } returns authorizationWallet
            every { automaticPixMandateRepository.findByMandateIdAndWalletId(authorizationWallet.mandateId, differentWalletId) } returns authorizationWallet

            val result = service.rejectMandate(accountId, differentWalletId, authorizationWallet.mandateId, ClientRejectionReason.DECLINED)

            result.isFailure shouldBe true
            result.exceptionOrNull()?.shouldBeTypeOf<IllegalArgumentException>()
            result.exceptionOrNull()?.message shouldBe "A autorização não pertence à carteira informada."
        }

        @Test
        fun `deve retornar erro quando usuário não tem permissão`() {
            val authorizationWallet = createAuthorization(walletId = walletId)

            every { automaticPixMandateRepository.findByMandateIdAndWalletId(authorizationWallet.mandateId, authorizationWallet.walletId) } returns authorizationWallet
            every { walletService.findWallet(walletId) } returns wallet

            val result = service.rejectMandate(walletFixture.cantPayParticipant.accountId, walletId, authorizationWallet.mandateId, ClientRejectionReason.DECLINED)

            result.isFailure shouldBe true
            result.exceptionOrNull()?.shouldBeTypeOf<IllegalStateException>()
            result.exceptionOrNull()?.message shouldBe "Usuário não tem permissão para agendar pagamentos na carteira"
        }
    }

    @Nested
    inner class ApprovePendingAuthorization {

        @Test
        fun `deve aprovar autorizacao pendente com sucesso`() {
            val authorizationWallet = createAuthorization(walletId = walletId)
            val newStatus = AutomaticPixMandateStatus.PROCESSING
            val maximumAmount = 1000_00L

            every { walletService.findWallet(walletId) } returns wallet
            every { automaticPixMandateRepository.findByMandateIdAndWalletId(authorizationWallet.mandateId, authorizationWallet.walletId) } returns authorizationWallet
            every { arbiAutomaticPixAdapter.approveMandate(authorizationWallet.mandateId, authorizationWallet.recurringPayment.recurringPaymentId, any(), maximumAmount) } returns Result.success(newStatus)
            every { automaticPixMandateRepository.updateStatus(any(), any()) } answers { firstArg<AutomaticPixMandate>().withNewStatus(secondArg()) }
            every { automaticPixMandateRepository.findByMandateIdAndWalletId(authorizationWallet.mandateId, authorizationWallet.walletId) } returns authorizationWallet.withNewStatus(newStatus)

            service.approveMandate(accountId, walletId, authorizationWallet.mandateId, maximumAmount).getOrThrow()

            verify(exactly = 1) {
                arbiAutomaticPixAdapter.approveMandate(authorizationWallet.mandateId, authorizationWallet.recurringPayment.recurringPaymentId, any(), maximumAmount)
                automaticPixMessagePublisher.publishRecurreceRefresh(any())
            }
        }

        @Test
        fun `deve retornar erro quando autorização não for encontrada`() {
            every { automaticPixMandateRepository.findByMandateIdAndWalletId(any(), any()) } returns null

            val result = service.approveMandate(accountId, walletId, AutomaticPixMandateId("ext-123"), 1000_00L)

            result.isFailure shouldBe true
            result.exceptionOrNull()?.shouldBeTypeOf<IllegalArgumentException>()
            result.exceptionOrNull()?.message shouldBe "A autorização não encontrada."
        }

        @Test
        fun `deve retornar erro quando autorização não pertence à carteira`() {
            val authorizationWallet = createAuthorization()
            val differentWalletId = WalletId("different-wallet")

            every { automaticPixMandateRepository.findByMandateIdAndWalletId(authorizationWallet.mandateId, authorizationWallet.walletId) } returns authorizationWallet
            every { automaticPixMandateRepository.findByMandateIdAndWalletId(authorizationWallet.mandateId, differentWalletId) } returns authorizationWallet

            val result = service.approveMandate(accountId, differentWalletId, authorizationWallet.mandateId, 1000_00L)

            result.isFailure shouldBe true
            result.exceptionOrNull()?.shouldBeTypeOf<IllegalArgumentException>()
            result.exceptionOrNull()?.message shouldBe "A autorização não pertence à carteira informada."
        }

        @Test
        fun `deve retornar erro quando usuário não tem permissão`() {
            val authorizationWallet = createAuthorization(walletId = walletId)

            every { automaticPixMandateRepository.findByMandateIdAndWalletId(authorizationWallet.mandateId, authorizationWallet.walletId) } returns authorizationWallet
            every { walletService.findWallet(walletId) } returns wallet

            val result = service.approveMandate(walletFixture.cantPayParticipant.accountId, walletId, authorizationWallet.mandateId, 1000_00L)

            result.isFailure shouldBe true
            result.exceptionOrNull()?.shouldBeTypeOf<IllegalStateException>()
            result.exceptionOrNull()?.message shouldBe "Usuário não tem permissão para agendar pagamentos na carteira"
        }
    }
}

private fun AutomaticPixMandate.withNewStatus(newStatus: AutomaticPixMandateStatus): AutomaticPixMandate {
    return when (this) {
        is AutomaticPixPendingMandate -> this.copy(status = newStatus)
        is AutomaticPixNotPendingMandate -> this.copy(status = newStatus)
    }
}