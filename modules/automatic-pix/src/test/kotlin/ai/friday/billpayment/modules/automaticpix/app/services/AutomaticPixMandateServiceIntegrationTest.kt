package ai.friday.billpayment.modules.automaticpix.app.services

import DynamoDBUtils
import ai.friday.billpayment.adapters.arbi.NewArbiAuthenticationManager
import ai.friday.billpayment.adapters.parsers.parseListFrom
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.automaticpix.adapters.api.toResponseTO
import ai.friday.billpayment.modules.automaticpix.adapters.arbi.ArbiAutomaticPixAdapter
import ai.friday.billpayment.modules.automaticpix.adapters.arbi.ArbiAutomaticPixConfiguration
import ai.friday.billpayment.modules.automaticpix.adapters.arbi.MandateResponseTO
import ai.friday.billpayment.modules.automaticpix.adapters.arbi.PendingMandateResponseTO
import ai.friday.billpayment.modules.automaticpix.adapters.dynamodb.AutomaticPixMandateDynamoDAO
import ai.friday.billpayment.modules.automaticpix.adapters.dynamodb.AutomaticPixMandateDynamoDBRepository
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateStatus
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpResponse
import io.micronaut.http.MutableHttpRequest
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.reactivex.Flowable
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class AutomaticPixMandateServiceIntegrationTest {
    private val dynamoDbEnhancedClient = DynamoDBUtils.setupDynamoDB()
    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()
    private val walletId = wallet.id

    private val httpClient: RxHttpClient = mockk()

    private val configuration: ArbiAutomaticPixConfiguration = mockk {
        every { clientId } returns "test-client-id"
        every { automaticPixMandatePath } returns "automaticPixMandatePath"
        every { automaticPixPendingMandatePath } returns "automaticPixPendingMandatePath"
    }

    private val authenticationManager: NewArbiAuthenticationManager = mockk {
        every { getToken() } returns "test-token"
    }

    private val arbiAutomaticPixAdapter: ArbiAutomaticPixAdapter = ArbiAutomaticPixAdapter(
        httpClient = httpClient,
        configuration = configuration,
        authenticationManager = authenticationManager,
    )

    private val walletService: WalletService = mockk()
    private val accountService: AccountService = mockk()
    private val automaticPixMessagePublisher: AutomaticPixMessagePublisher = mockk {
        every { notifyMandatePending(any()) } returns Unit
        every { notifyMandateStatusChange(any(), any()) } returns Unit
    }

    private val repository =
        AutomaticPixMandateDynamoDBRepository(dynamoDAO = AutomaticPixMandateDynamoDAO(dynamoDbEnhancedClient))
    private val service = AutomaticPixMandateService(
        automaticPixRemoteManager = arbiAutomaticPixAdapter,
        automaticPixMandateRepository = repository,
        walletService = walletService,
        accountService = accountService,
        automaticPixMessagePublisher = automaticPixMessagePublisher,
    )

    @BeforeEach
    fun init() {
        every {
            walletService.findWallet(walletId)
        } returns wallet

        every {
            accountService.findAccountPaymentMethodByIdAndAccountId(wallet.paymentMethodId, wallet.founder.accountId)
        } returns mockk() {
            every { method } returns mockk<InternalBankAccount> {
                every {
                    buildFullAccountNumber()
                } returns "**************"
            }
        }
    }

    @Test
    fun `deve atualizar as autorizações no DynamoDB com sucesso`() {
        every {
            httpClient.exchange(
                any<MutableHttpRequest<*>>(),
                Argument.listOf(MandateResponseTO::class.java),
                Argument.STRING,
            )
        } returns Flowable.just(HttpResponse.ok(parseListFrom<MandateResponseTO>(allMandatesArbiResponse)))

        every {
            httpClient.exchange(
                any<MutableHttpRequest<*>>(),
                Argument.listOf(PendingMandateResponseTO::class.java),
                Argument.STRING,
            )
        } returns Flowable.just(HttpResponse.ok(parseListFrom<PendingMandateResponseTO>(allPendingMandatesArbiResponse)))

        val response = service.refreshMandates(walletId).getOrThrow()

        response.size shouldBe 8

        val responseToList = response.map { it.toResponseTO() }
        responseToList.size shouldBe 8
        responseToList.forEach { responseTo ->
            if (responseTo.status == AutomaticPixMandateStatus.ACTIVE.name) {
                responseTo.activatedAt shouldNotBe null
            }

            if (responseTo.status == AutomaticPixMandateStatus.PROCESSING.name) {
                responseTo.expiresAt shouldNotBe null
            }
        }

        repository.findByWalletId(walletId, null).size shouldBe 7

        // Verifica que as notificações foram enviadas para as novas autorizações pendentes
        verify(exactly = 1) { automaticPixMessagePublisher.notifyMandatePending(any()) }
    }

    @Test
    fun `deve lidar com autorizações existentes sem notificar novamente`() {
        // Primeira execução para criar as autorizações
        every {
            httpClient.exchange(
                any<MutableHttpRequest<*>>(),
                Argument.listOf(MandateResponseTO::class.java),
                Argument.STRING,
            )
        } returns Flowable.just(HttpResponse.ok(parseListFrom<MandateResponseTO>(allMandatesArbiResponse)))

        every {
            httpClient.exchange(
                any<MutableHttpRequest<*>>(),
                Argument.listOf(PendingMandateResponseTO::class.java),
                Argument.STRING,
            )
        } returns Flowable.just(HttpResponse.ok(parseListFrom<PendingMandateResponseTO>(allPendingMandatesArbiResponse)))

        service.refreshMandates(walletId).getOrThrow()

        // Segunda execução com as mesmas autorizações
        val response = service.refreshMandates(walletId).getOrThrow()

        response.size shouldBe 8

        // Verifica que as notificações foram enviadas apenas na primeira vez
        verify(exactly = 1) { automaticPixMessagePublisher.notifyMandatePending(any()) }
    }
}

private val allPendingMandatesArbiResponse = """
    [
        {
            "id": "SC9999991820250613GLXrE0DkkZK",
            "expirationDate": "2025-06-15T17:21:40.058-03:00",
            "recurringPayment": {
                "id": "RR999999182025061310681229561"
            },
            "billing": {
                "startDate": "2025-06-13T00:00:00",
                "endDate": "0001-01-01T00:00:00",
                "amount": 330.4,
                "minimumReceiverAmount": 0,
                "maximumPayerAmount": 0,
                "receiver": {
                    "documentNumber": "**************",
                    "name": "Pix Tester 918",
                    "documentType": "CNPJ"
                },
                "debtor": {
                    "documentNumber": "54403563000150",
                    "name": "BCO ARBI S.A.",
                    "documentType": "CNPJ"
                },
                "contractNumber": "93702894",
                "periodicity": "MONTHLY",
                "description": null
            }
        }
    ]
""".trimIndent()

private val allMandatesArbiResponse = """
    [
    {
        "id": "SC9999991820250613u7q4AW6ftk1",
        "expirationDate": "2025-06-15T00:00:00",
        "status": "ACTIVE",
        "date": "2025-06-13T00:00:00",
        "startDateValidity": "2025-06-13T00:00:00",
        "endDateValidity": null,
        "expiredValidityDate": null,
        "cancellationDate": null,
        "userCancellation": null,
        "recurringPayment": {
            "id": "RR999999182025061367529091657"
        },
        "billing": {
            "receiver": {
                "documentNumber": "**************",
                "name": "Pix Tester 918",
                "documentType": "CNPJ"
            }
        }
    },
    {
        "id": "SC9999991820250613vFtDAsGlTXN",
        "expirationDate": "2025-06-15T00:00:00",
        "status": "CANCELED",
        "date": "2025-06-13T00:00:00",
        "startDateValidity": "2025-06-13T00:00:00",
        "endDateValidity": null,
        "expiredValidityDate": null,
        "cancellationDate": "2025-06-16T00:00:00",
        "userCancellation": null,
        "recurringPayment": {
            "id": "RR999999182025061358493886710"
        },
        "billing": {
            "receiver": {
                "documentNumber": "**************",
                "name": "Pix Tester 918",
                "documentType": "CNPJ"
            }
        }
    },
    {
        "id": "SC9999991820250613GLXrE0DkkZK",
        "expirationDate": "2025-06-15T00:00:00",
        "status": "CANCELED",
        "date": null,
        "startDateValidity": "2025-06-13T00:00:00",
        "endDateValidity": null,
        "expiredValidityDate": null,
        "cancellationDate": null,
        "userCancellation": null,
        "recurringPayment": {
            "id": "RR999999182025061310681229561"
        },
        "billing": {
            "receiver": {
                "documentNumber": "**************",
                "name": "Pix Tester 918",
                "documentType": "CNPJ"
            }
        }
    },
    {
        "id": "SC5440356320250612FJG1D26QBRy",
        "expirationDate": "2025-06-12T00:00:00",
        "status": "ACTIVE",
        "date": "2025-06-12T00:00:00",
        "startDateValidity": "2025-06-12T00:00:00",
        "endDateValidity": null,
        "expiredValidityDate": null,
        "cancellationDate": null,
        "userCancellation": null,
        "recurringPayment": {
            "id": "RR999999182025061222675340329"
        },
        "billing": {
            "receiver": {
                "documentNumber": "**************",
                "name": "Pix Tester 918",
                "documentType": "CNPJ"
            }
        }
    },
    {
        "id": "SC5440356320250612VylTbS38gGy",
        "expirationDate": "2025-06-12T00:00:00",
        "status": "ACTIVE",
        "date": "2025-06-12T00:00:00",
        "startDateValidity": "2025-06-12T00:00:00",
        "endDateValidity": null,
        "expiredValidityDate": null,
        "cancellationDate": null,
        "userCancellation": null,
        "recurringPayment": {
            "id": "RR999999182025061262115046113"
        },
        "billing": {
            "receiver": {
                "documentNumber": "**************",
                "name": "Pix Tester 918",
                "documentType": "CNPJ"
            }
        }
    },
    {
        "id": "SC54403563202506129qi7cnFY0YF",
        "expirationDate": "2025-06-12T00:00:00",
        "status": "ACTIVE",
        "date": "2025-06-12T00:00:00",
        "startDateValidity": "2025-06-12T00:00:00",
        "endDateValidity": null,
        "expiredValidityDate": null,
        "cancellationDate": null,
        "userCancellation": null,
        "recurringPayment": {
            "id": "RR999999182025061288979352231"
        },
        "billing": {
            "receiver": {
                "documentNumber": "**************",
                "name": "Pix Tester 918",
                "documentType": "CNPJ"
            }
        }
    },
    {
        "id": "SC9999991820250616VyxF3hYI7th",
        "expirationDate": "2025-06-18T00:00:00",
        "status": "PROCESSING",
        "date": null,
        "startDateValidity": "2025-06-16T00:00:00",
        "endDateValidity": null,
        "expiredValidityDate": null,
        "cancellationDate": null,
        "userCancellation": null,
        "recurringPayment": {
            "id": "RR999999182025061619603127953"
        },
        "billing": {
            "receiver": {
                "documentNumber": "**************",
                "name": "Pix Tester 918",
                "documentType": "CNPJ"
            }
        }
    }
]
""".trimIndent()