package ai.friday.billpayment.modules.automaticpix.app.services

import DynamoDBUtils
import ai.friday.billpayment.adapters.arbi.NewArbiAuthenticationManager
import ai.friday.billpayment.adapters.parsers.parseListFrom
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.automaticpix.adapters.api.toResponseTO
import ai.friday.billpayment.modules.automaticpix.adapters.arbi.ArbiAutomaticPixAdapter
import ai.friday.billpayment.modules.automaticpix.adapters.arbi.ArbiAutomaticPixConfiguration
import ai.friday.billpayment.modules.automaticpix.adapters.arbi.RecurrenceOperationResponseTO
import ai.friday.billpayment.modules.automaticpix.adapters.dynamodb.AutomaticPixRecurringPaymentDynamoDAO
import ai.friday.billpayment.modules.automaticpix.adapters.dynamodb.AutomaticPixRecurringPaymentDynamoDBRepository
import ai.friday.billpayment.modules.automaticpix.createBaseAuthorization
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpResponse
import io.micronaut.http.MutableHttpRequest
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.reactivex.Flowable
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class AutomaticPixRecurrenceServiceIntegrationTest {
    private val dynamoDbEnhancedClient = DynamoDBUtils.setupDynamoDB()
    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()
    private val walletId = wallet.id

    private val httpClient: RxHttpClient = mockk()

    private val configuration: ArbiAutomaticPixConfiguration = mockk {
        every { clientId } returns "test-client-id"
        every { automaticPixRecurringPaymentsPath } returns "automaticPixRecurringPaymentsPath"
        every { automaticPixRecurringPaymentsStatusPath } returns "automaticPixRecurringPaymentsStatusPath"
    }

    private val authenticationManager: NewArbiAuthenticationManager = mockk {
        every { getToken() } returns "test-token"
    }

    private val arbiAutomaticPixAdapter: ArbiAutomaticPixAdapter = ArbiAutomaticPixAdapter(
        httpClient = httpClient,
        configuration = configuration,
        authenticationManager = authenticationManager,
    )

    private val walletService: WalletService = mockk()
    private val accountService: AccountService = mockk()
    private val automaticPixMessagePublisher: AutomaticPixMessagePublisher = mockk {
        every { notifyRecurrenceApproved(any()) } returns Unit
        every { notifyRecurrenceStatusChange(any(), any()) } returns Unit
    }

    private val repository =
        AutomaticPixRecurringPaymentDynamoDBRepository(dynamoDAO = AutomaticPixRecurringPaymentDynamoDAO(dynamoDbEnhancedClient))
    private val service = AutomaticPixRecurrenceService(
        automaticPixRemoteManager = arbiAutomaticPixAdapter,
        automaticPixRecurringPaymentRepository = repository,
        walletService = walletService,
        accountService = accountService,
        automaticPixMessagePublisher = automaticPixMessagePublisher,
        mandateRepository = mockk {
            every {
                findByMandateIdAndWalletId(any(), any())
            } returns createBaseAuthorization()
        },
        findBillService = mockk(),
        qrCodeParser = mockk(),
    )

    @BeforeEach
    fun init() {
        every {
            walletService.findWallet(walletId)
        } returns wallet

        every {
            accountService.findAccountPaymentMethodByIdAndAccountId(wallet.paymentMethodId, wallet.founder.accountId)
        } returns mockk() {
            every { method } returns mockk<InternalBankAccount> {
                every {
                    buildFullAccountNumber()
                } returns "**************"
            }
        }
    }

    @Test
    fun `deve atualizar as recorrencias no DynamoDB com sucesso e conseguir gerar o TO`() {
        every {
            httpClient.exchange(
                any<MutableHttpRequest<*>>(),
                Argument.listOf(RecurrenceOperationResponseTO::class.java),
                Argument.STRING,
            )
        } returns Flowable.just(HttpResponse.ok(parseListFrom<RecurrenceOperationResponseTO>(allRecurrencesArbiResponse)))

        val response = service.refreshRecurrences(walletId).getOrThrow()

        response.size shouldBe 3

        response.map { it.toResponseTO() }.size shouldBe 3

        repository.findByWalletId(walletId).size shouldBe 3

        // Verifica que as notificações foram enviadas para as novas recorrências
        verify(exactly = 3) { automaticPixMessagePublisher.notifyRecurrenceApproved(any()) }
    }

    @Test
    fun `deve lidar com recorrências existentes sem notificar novamente`() {
        // Primeira execução para criar as recorrências
        every {
            httpClient.exchange(
                any<MutableHttpRequest<*>>(),
                Argument.listOf(RecurrenceOperationResponseTO::class.java),
                Argument.STRING,
            )
        } returns Flowable.just(HttpResponse.ok(parseListFrom<RecurrenceOperationResponseTO>(allRecurrencesArbiResponse)))

        service.refreshRecurrences(walletId).getOrThrow()

        // Segunda execução com as mesmas recorrências
        val response = service.refreshRecurrences(walletId).getOrThrow()

        response.size shouldBe 3

        // Verifica que as notificações foram enviadas apenas na primeira vez
        verify(exactly = 3) { automaticPixMessagePublisher.notifyRecurrenceApproved(any()) }
    }
}

private val allRecurrencesArbiResponse = """
    [
        {
            "id": "RR999999182025061222675340329",
            "authorization": {
                "id": "SC5440356320250612FJG1D26QBRy"
            },
            "billing": {
                "receiver": {
                    "documentNumber": "**************",
                    "name": "Pix Tester 918",
                    "documentType": "CNPJ"
                },
                "debtor": {
                    "documentNumber": "***03563***",
                    "name": "BCO ARBI S.A.",
                    "documentType": "CPF"
                },
                "contractNumber": "87871098",
                "startDate": "2025-06-12T00:00:00",
                "endDate": "0001-01-01T00:00:00",
                "periodicity": "MONTHLY",
                "amount": 645.09,
                "minimumReceiverAmount": 0,
                "maximumPayerAmount": 0,
                "description": "Serviço de Streaming de música",
                "notificationsScheduling": true,
                "creditLimitUsage": false
            }
        },
        {
            "id": "RR999999182025061262115046113",
            "authorization": {
                "id": "SC5440356320250612VylTbS38gGy"
            },
            "billing": {
                "receiver": {
                    "documentNumber": "**************",
                    "name": "Pix Tester 918",
                    "documentType": "CNPJ"
                },
                "debtor": {
                    "documentNumber": "***03563***",
                    "name": "BCO ARBI S.A.",
                    "documentType": "CPF"
                },
                "contractNumber": "40502310",
                "startDate": "2025-06-12T00:00:00",
                "endDate": "0001-01-01T00:00:00",
                "periodicity": "MONTHLY",
                "amount": 117.85,
                "minimumReceiverAmount": 0,
                "maximumPayerAmount": 0,
                "description": "Serviço de Academia",
                "notificationsScheduling": true,
                "creditLimitUsage": false
            }
        },
        {
            "id": "RR999999182025061288979352231",
            "authorization": {
                "id": "SC54403563202506129qi7cnFY0YF"
            },
            "billing": {
                "receiver": {
                    "documentNumber": "**************",
                    "name": "Pix Tester 918",
                    "documentType": "CNPJ"
                },
                "debtor": {
                    "documentNumber": "***03563***",
                    "name": "BCO ARBI S.A.",
                    "documentType": "CPF"
                },
                "contractNumber": "42837191",
                "startDate": "2025-06-12T00:00:00",
                "endDate": "0001-01-01T00:00:00",
                "periodicity": "MONTHLY",
                "amount": 332.8,
                "minimumReceiverAmount": 0,
                "maximumPayerAmount": 0,
                "description": "Serviço de Energia elétrica",
                "notificationsScheduling": true,
                "creditLimitUsage": false
            }
        }
    ]
""".trimIndent()