package ai.friday.billpayment.modules.automaticpix.adapters.messaging

import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.integration.BILL_ID
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.modules.automaticpix.adapters.api.BillingLifecycleEventType
import ai.friday.billpayment.modules.automaticpix.adapters.api.MandateAndRecurrenceLifecycleEventType
import ai.friday.billpayment.modules.automaticpix.adapters.api.ParsedCallbackData
import ai.friday.billpayment.modules.automaticpix.adapters.api.PaymentLifecycleEventType
import ai.friday.billpayment.modules.automaticpix.adapters.api.PaymentLifecycleStatus
import ai.friday.billpayment.modules.automaticpix.adapters.api.PixParticipantCallbackData
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixBillOnWallet
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixBillRepository
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateId
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixProvider
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentId
import ai.friday.billpayment.modules.automaticpix.app.services.AutomaticPixBillingService
import ai.friday.billpayment.modules.automaticpix.app.services.AutomaticPixMessagePublisher
import ai.friday.billpayment.modules.automaticpix.app.services.ManageAutomaticPixPaymentCommand
import ai.friday.billpayment.modules.automaticpix.createAutomaticPixBill
import ai.friday.morning.json.getObjectMapper
import arrow.core.right
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message

class AutomaticPixCallbackHandlerTest {

    private val automaticPixBillRepository: AutomaticPixBillRepository = mockk()
    private val automaticPixMessagePublisher: AutomaticPixMessagePublisher = mockk()
    private val accountService: AccountService = mockk()
    private val walletService: WalletService = mockk()
    private val automaticPixBillingService: AutomaticPixBillingService = mockk()

    private val handler = AutomaticPixCallbackHandler(
        automaticPixBillRepository,
        automaticPixMessagePublisher,
        accountService,
        walletService,
        automaticPixBillingService,
    )

    private val payer = PixParticipantCallbackData(
        ispbCode = "********",
        taxId = null,
        accountType = null,
        accountNumber = "123456",
        agencyCode = 1,
        name = null,
    )

    private val payee = PixParticipantCallbackData(
        ispbCode = "********",
        taxId = null,
        accountType = null,
        accountNumber = "654321",
        agencyCode = 1222,
        name = null,
    )

    private val walletId = WalletId(WALLET_ID)
    private val message = mockk<Message>()

    @BeforeEach
    fun init() {
        every {
            accountService.findPhysicalAccountPaymentMethod(
                bankNo = ********L,
                routingNo = 1,
                fullAccountNo = "123456",
            )
        } returns mockk<AccountPaymentMethod>(relaxed = true).right()

        every { walletService.findWallet(any(), any()) } returns mockk(relaxed = true) {
            every { id } returns walletId
        }

        every { automaticPixMessagePublisher.publishMandateRefresh(walletId) } returns Unit
        every { automaticPixMessagePublisher.publishRecurreceRefresh(walletId) } returns Unit
    }

    @Test
    fun `should handle mandate and recurrence lifecycle callback successfully`() {
        val callback = ParsedCallbackData.MandateAndRecurrenceLifecycleCallback(
            eventType = MandateAndRecurrenceLifecycleEventType.RECURRENCE_CANCELLED,
            recurrenceId = AutomaticPixRecurringPaymentId(value = ""),
            mandateId = AutomaticPixMandateId(value = ""),
            payer = payer,
            provider = AutomaticPixProvider.ARBI,
        )

        every {
            message.body()
        } returns getObjectMapper().writeValueAsString(callback)

        // when
        val response = handler.handleMessage(message)

        // then
        response.shouldDeleteMessage.shouldBeTrue()

        verify(exactly = 1) {
            automaticPixMessagePublisher.publishMandateRefresh(walletId)
            automaticPixMessagePublisher.publishRecurreceRefresh(walletId)
        }
    }

    @Test
    fun `should handle billing lifecycle callback successfully`() {
        // given
        val callback = ParsedCallbackData.BillingLifecycleCallback(
            eventType = BillingLifecycleEventType.BILLING_CANCELLED,
            recurrenceId = AutomaticPixRecurringPaymentId(value = ""),
            mandateId = AutomaticPixMandateId(value = ""),
            payer = payer,
            endToEnd = "ETE********9",
            provider = AutomaticPixProvider.ARBI,
        )

        every {
            message.body()
        } returns getObjectMapper().writeValueAsString(callback)

        every { automaticPixMessagePublisher.publishBillingRefresh(walletId) } returns Unit

        // when
        val response = handler.handleMessage(message)

        // then
        response.shouldDeleteMessage.shouldBeTrue()

        verify(exactly = 1) {
            automaticPixMessagePublisher.publishBillingRefresh(walletId)
        }
    }

    @Test
    fun `should handle payment lifecycle callback successfully`() {
        // given
        val endToEnd = "E********9"
        val eventType = PaymentLifecycleEventType.PAYMENT_CANCELED
        val paymentStatus = PaymentLifecycleStatus.CANCELED

        val callback = ParsedCallbackData.PaymentLifecycleCallback(
            eventType = eventType,
            payer = payer,
            payee = payee,
            endToEnd = endToEnd,
            provider = AutomaticPixProvider.ARBI,
            value = null,
            schedulingDate = null,
            settlementDate = null,
            internalReference = null,
            paymentStatus = paymentStatus,
            message = null,
        )

        every {
            message.body()
        } returns getObjectMapper().writeValueAsString(callback)

        val billOnWallet = AutomaticPixBillOnWallet(
            walletId = walletId,
            billId = BillId(BILL_ID),
            automaticPixBill = createAutomaticPixBill(endToEnd),
        )

        every { automaticPixBillRepository.findByEndToEnd(endToEnd) } returns listOf(billOnWallet)

        every { automaticPixBillingService.manageAutomaticPixPayment(any()) } returns mockk(relaxed = true)

        val response = handler.handleMessage(message)

        response.shouldDeleteMessage.shouldBeTrue()

        val slot = slot<ManageAutomaticPixPaymentCommand>()
        verify(exactly = 1) {
            automaticPixBillRepository.findByEndToEnd(endToEnd)
            automaticPixBillingService.manageAutomaticPixPayment(capture(slot))
        }

        slot.captured.apply {
            automaticPixBillOnWallet shouldBe billOnWallet
            paymentLifecycleEventType shouldBe eventType
            paymentLifecycleStatus shouldBe paymentStatus
        }
    }
}