package ai.friday.billpayment.modules.automaticpix.adapters.dynamodb

import DynamoDBUtils
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateStatus
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixNotPendingMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPendingMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentId
import ai.friday.billpayment.modules.automaticpix.createAuthorization
import ai.friday.billpayment.modules.automaticpix.createBaseAuthorization
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import java.time.LocalDate
import org.junit.jupiter.api.Test

class AutomaticPixMandateDynamoDBRepositoryTest {
    private val dynamoDbEnhancedClient = DynamoDBUtils.setupDynamoDB()

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()
    private val walletId = wallet.id

    private val repository = AutomaticPixMandateDynamoDBRepository(dynamoDAO = AutomaticPixMandateDynamoDAO(dynamoDbEnhancedClient))

    @Test
    fun `deve salvar uma autorização com sucesso`() {
        val authorization = createAuthorization(walletId)
        val authorization2 = createBaseAuthorization(walletId, mandateId = "sdlkfj", recurringPaymentId = "rec-dsfds")

        val saved = repository.save(authorization)
        val saved2 = repository.save(authorization2)

        saved.shouldBeTypeOf<AutomaticPixPendingMandate>()
        saved.walletId shouldBe walletId
        saved shouldBe authorization

        saved2.shouldBeTypeOf<AutomaticPixNotPendingMandate>()
        saved2.walletId shouldBe walletId
        saved2 shouldBe authorization2

        repository.findByMandateIdAndWalletId(authorization.mandateId, walletId) shouldBe authorization
        repository.findByMandateIdAndWalletId(authorization2.mandateId, walletId) shouldBe authorization2
    }

    @Test
    fun `deve encontrar uma autorização pelo ID`() {
        val authorization = createAuthorization()
        val saved = repository.save(authorization)

        saved shouldBe authorization

        val found = repository.findByMandateIdAndWalletId(authorization.mandateId, authorization.walletId)
        found.shouldNotBeNull()
        found shouldBe authorization
    }

    @Test
    fun `deve encontrar autorizações por walletId e status`() {
        val authorization1 = createAuthorization(walletId = walletId)
        val authorization2 = createAuthorization(
            walletId = walletId,
            mandateId = "ext-456",
            recurringPaymentId = "rec-456",
            status = AutomaticPixMandateStatus.ACTIVE,
        )

        repository.save(authorization1)
        repository.save(authorization2)

        val pendingAuthorizations = repository.findByWalletId(walletId, AutomaticPixMandateStatus.PENDING)
        val activeAuthorizations = repository.findByWalletId(walletId, AutomaticPixMandateStatus.ACTIVE)
        val allAuthorizations = repository.findByWalletId(walletId, null)

        pendingAuthorizations.size shouldBe 1
        pendingAuthorizations.first() shouldBe authorization1

        activeAuthorizations.size shouldBe 1
        activeAuthorizations.first() shouldBe authorization2

        allAuthorizations.size shouldBe 2
        allAuthorizations.map { it }.shouldContainExactlyInAnyOrder(
            authorization1,
            authorization2,
        )
    }

    @Test
    fun `deve encontrar autorizações por recurrenceExternalId e status`() {
        val authorization1 = createAuthorization()
        val authorization2 = createAuthorization(
            mandateId = "ext-456",
            recurringPaymentId = "rec-456",
            status = AutomaticPixMandateStatus.ACTIVE,
        )

        repository.save(authorization1)
        repository.save(authorization2)

        val pendingAuthorizations = repository.findByExternalId(
            AutomaticPixRecurringPaymentId("rec-123"),
            AutomaticPixMandateStatus.PENDING,
        )
        val activeAuthorizations = repository.findByExternalId(
            AutomaticPixRecurringPaymentId("rec-456"),
            AutomaticPixMandateStatus.ACTIVE,
        )
        val withoutStatus = repository.findByExternalId(
            AutomaticPixRecurringPaymentId("rec-123"),
            null,
        )

        val wrongStatus = repository.findByExternalId(
            AutomaticPixRecurringPaymentId("rec-123"),
            AutomaticPixMandateStatus.ACTIVE,
        )

        pendingAuthorizations.size shouldBe 1
        pendingAuthorizations.first() shouldBe authorization1

        activeAuthorizations.size shouldBe 1
        activeAuthorizations.first() shouldBe authorization2

        withoutStatus.size shouldBe 1
        pendingAuthorizations.first() shouldBe authorization1

        wrongStatus.size shouldBe 0
    }

    @Test
    fun `deve encontrar autorizações por status e data de expiração`() {
        val authorization1 = createAuthorization(
            walletId = walletId,
            mandateId = "ext-123",
            recurringPaymentId = "rec-123",
            status = AutomaticPixMandateStatus.PENDING,
        )
        val authorization2 = createAuthorization(
            walletId = walletId,
            mandateId = "ext-456",
            recurringPaymentId = "rec-456",
            status = AutomaticPixMandateStatus.PENDING,
        )
        val authorization3 = createAuthorization(
            walletId = walletId,
            mandateId = "ext-789",
            recurringPaymentId = "rec-789",
            status = AutomaticPixMandateStatus.ACTIVE,
        )

        repository.save(authorization1)
        repository.save(authorization2)
        repository.save(authorization3)

        val tomorrow = LocalDate.now().plusDays(1)
        val expiringAuthorizations = repository.findByStatusExpringBefore(AutomaticPixMandateStatus.PENDING, tomorrow)

        expiringAuthorizations.size shouldBe 2
        expiringAuthorizations.shouldContainExactlyInAnyOrder(authorization1, authorization2)
    }

    @Test
    fun `deve atualizar o status de uma autorização`() {
        val authorization = createAuthorization(walletId = walletId)
        val saved = repository.save(authorization)

        saved.status shouldBe AutomaticPixMandateStatus.PENDING

        val updated = repository.updateStatus(authorization, AutomaticPixMandateStatus.ACTIVE)

        updated.status shouldBe AutomaticPixMandateStatus.ACTIVE
        updated.mandateId shouldBe authorization.mandateId
        updated.walletId shouldBe authorization.walletId

        val found = repository.findByMandateIdAndWalletId(authorization.mandateId, walletId)
        found.shouldNotBeNull()
        found.status shouldBe AutomaticPixMandateStatus.ACTIVE
    }

    @Test
    fun `deve encontrar autorizações pelo ID do mandato em diferentes wallets`() {
        val authorization1 = createAuthorization(walletId = walletId)
        val authorization2 = createAuthorization(
            walletId = WalletId(WALLET_ID),
            mandateId = authorization1.mandateId.value,
            recurringPaymentId = "rec-456",
        )

        repository.save(authorization1)
        repository.save(authorization2)

        val foundByMandateId = repository.findByMandateId(authorization1.mandateId)

        foundByMandateId.size shouldBe 2
        foundByMandateId.shouldContainExactlyInAnyOrder(authorization1, authorization2)
    }
}