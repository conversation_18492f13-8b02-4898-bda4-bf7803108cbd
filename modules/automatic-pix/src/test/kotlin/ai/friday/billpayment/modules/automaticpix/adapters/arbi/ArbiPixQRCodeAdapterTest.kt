package ai.friday.billpayment.modules.automaticpix.adapters.arbi

import ai.friday.billpayment.adapters.arbi.ArbiPixQRCodeAdapter
import ai.friday.billpayment.adapters.arbi.NewArbiAuthenticationManager
import ai.friday.billpayment.adapters.arbi.NewArbiPixKeyManagementConfiguration
import ai.friday.billpayment.adapters.arbi.QRCodeResponseWrapperTO
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.bill.PixCopyAndPaste
import ai.friday.billpayment.app.bill.PixQrCodeType
import ai.friday.billpayment.app.pix.AutomaticPixQrCodeRecurringDataExtractor
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixDocumentType
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixJourney
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPeriodicity
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixQrCodeRecurringData
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRetryType
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.core.type.Argument
import io.micronaut.http.MutableHttpRequest
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.mockk.every
import io.mockk.mockk
import io.reactivex.Flowable
import org.junit.jupiter.api.Test

class ArbiPixQRCodeAdapterTest {
    private val httpClient: RxHttpClient = mockk()
    private val configuration: NewArbiPixKeyManagementConfiguration = mockk {
        every { clientId } returns "test-client-id"
        every { qrCodeProcessPath } returns "/api/v1/qrcode/process"
    }
    private val authenticationManager: NewArbiAuthenticationManager = mockk {
        every { getToken() } returns "test-token"
    }
    private val automaticPixQRCodeRecurringDataExtractor: AutomaticPixQrCodeRecurringDataExtractor = ArbiAutomaticAutomaticPixQrCodeRecurringDataExtractor()
    private val adapter: ArbiPixQRCodeAdapter = ArbiPixQRCodeAdapter(httpClient, configuration, authenticationManager, automaticPixQRCodeRecurringDataExtractor)

    @Test
    fun `deve processar QR Code com sucesso da Jornada 2`() {
        // given
        val pixCopyAndPaste = PixCopyAndPaste("00020126180014br.gov.bcb.pix5204000053039865802BR5913Fulano de Tal6008BRASILIA62070503***80950014br.gov.bcb.pix2573qr-h.sandbox.pix.bcb.gov.br/rest/api/rec/********************************6304EBFF")
        val document = "12345678900"
        val jornada2Json = parseObjectFrom<String>(
            """
            "{\"referencia\":\"***\",\"info\":\"Dados recuperados do link 'https://qr-h.sandbox.pix.bcb.gov.br/rest/api/rec/********************************'\",\"tipoQRCode\":\"COMPOSTO\",\"tipoPix\":\"RECORRENTE\",\"dadosRecorrencia\":{\"idRec\":\"RR999999182025061237370296873\",\"jornada\":\"JORNADA_2\",\"retentativa\":\"PERMITE_3R_7D\",\"valorRec\":660.80,\"ispbRecebedor\":\"99999918\",\"nomeRecebedor\":\"Pix Tester 918\",\"cnpjRecebedor\":\"99999918999924\",\"cnpjPagador\":\"54403563000150\",\"nomePagador\":\"BCO ARBI S.A.\",\"calendarioInicial\":\"2025-06-12\",\"periodicidade\":\"MENSAL\",\"contratoVinculo\":\"1301198\",\"objetoVinculo\":\"Serviço de Academia\",\"atualizacao\":[{\"data\":\"2025-06-12T00:00:00.000Z\",\"status\":\"CRIADA\"}]}}"
            """.trimIndent(),
        )

        every {
            httpClient.retrieve(
                any<MutableHttpRequest<*>>(),
                Argument.listOf(QRCodeResponseWrapperTO::class.java),
                Argument.STRING,
            )
        } returns Flowable.just(listOf(QRCodeResponseWrapperTO(jornada2Json)))

        // when
        val result = adapter.parseQRCodeCacheable(pixCopyAndPaste, document)

        // then
        result.isRight() shouldBe true
        val successResult = result.getOrNull()
        successResult.shouldNotBeNull()
        successResult.pixKeyDetails.shouldBeNull()
        successResult.qrCodeInfo?.type shouldBe PixQrCodeType.COMPOUND
        successResult.qrCodeInfo?.pixId.shouldBeNull()
        val parsed = parseObjectFrom<AutomaticPixQrCodeRecurringData>(successResult.qrCodeInfo?.automaticPixRecurringDataJson!!)
        parsed.fixedAmount shouldBe 66080L
        parsed.payer.documentNumber shouldBe "54403563000150"
        parsed.payer.name shouldBe "BCO ARBI S.A."
        parsed.payer.documentType shouldBe AutomaticPixDocumentType.CNPJ
        parsed.payee.documentNumber shouldBe "99999918999924"
        parsed.payee.name shouldBe "Pix Tester 918"
        parsed.payee.documentType shouldBe AutomaticPixDocumentType.CNPJ
        parsed.startDate.shouldNotBeNull()
        parsed.periodicity shouldBe AutomaticPixPeriodicity.MONTHLY
        parsed.journey shouldBe AutomaticPixJourney.JOURNEY_2
        parsed.retryType shouldBe AutomaticPixRetryType.ALLOWED_3R_7D
        parsed.recurringPaymentId.value shouldBe "RR999999182025061237370296873"
    }

    @Test
    fun `deve processar QR Code com sucesso da Jornada 3`() {
        // given
        val pixCopyAndPaste = PixCopyAndPaste("00020101021226940014br.gov.bcb.pix2572qr-h.sandbox.pix.bcb.gov.br/rest/api/v2/18ce881383734b74bfbe376e040e764c5204000053039865802BR5913Fulano de Tal6008BRASILIA62070503***80950014br.gov.bcb.pix2573qr-h.sandbox.pix.bcb.gov.br/rest/api/rec/********************************6304C147")
        val document = "12345678900"
        val jornada3Json = parseObjectFrom<String>(
            """
            "{\"nome\":\"Pix Tester 918\",\"cpfCnpj\":\"99999918999924\",\"nomePsp\":\"Banco 99999918\",\"codInstituicao\":\"99999918\",\"codAgencia\":\"0001\",\"nroConta\":\"94937\",\"tipoConta\":\"CACC\",\"dataCriacao\":\"2025-05-08T18:30:07.583Z\",\"dataPosse\":\"2025-05-08T18:30:07.583Z\",\"dataAbertura\":\"2010-01-10T03:00:00Z\",\"referencia\":\"18ce881383734b74bfbe376e040e764c\",\"info\":\"Dados recuperados do link 'https://qr-h.sandbox.pix.bcb.gov.br/rest/api/rec/********************************'\",\"valor\":828.84,\"tipoQRCode\":\"COMPOSTO\",\"endToEnd\":\"E544035632025061217408rjGgV0443z\",\"calendarioExpiracaoSegundos\":86400,\"calendarioCriacao\":\"2025-06-12T17:25:43.611Z\",\"tipoPessoa\":\"LEGAL_PERSON\",\"pagadorCnpj\":\"54403563000150\",\"pagadorNome\":\"BCO ARBI S.A.\",\"recebedorCnpj\":\"99999918999924\",\"recebedorNome\":\"Pix Tester 99999918\",\"recebedorCidade\":\"BRASILIA\",\"valorFinal\":413.92,\"tipoChave\":\"EVP\",\"chaveEnderecamento\":\"f4c6089a-bfde-4c00-a2d9-9eaa584b0219\",\"estatisticas\":{},\"tipoPix\":\"RECORRENTE\",\"dadosRecorrencia\":{\"idRec\":\"RR999999182025061231681110433\",\"jornada\":\"JORNADA_3\",\"retentativa\":\"PERMITE_3R_7D\",\"valorRec\":828.84,\"ispbRecebedor\":\"99999918\",\"nomeRecebedor\":\"Pix Tester 918\",\"cnpjRecebedor\":\"99999918999924\",\"cnpjPagador\":\"54403563000150\",\"nomePagador\":\"BCO ARBI S.A.\",\"calendarioInicial\":\"2025-06-12\",\"periodicidade\":\"MENSAL\",\"contratoVinculo\":\"60181540\",\"objetoVinculo\":\"Serviço de TV a cabo\",\"atualizacao\":[{\"data\":\"2025-06-12T00:00:00.000Z\",\"status\":\"CRIADA\"}]}}"
            """.trimIndent(),
        )

        every {
            httpClient.retrieve(
                any<MutableHttpRequest<*>>(),
                Argument.listOf(QRCodeResponseWrapperTO::class.java),
                Argument.STRING,
            )
        } returns Flowable.just(listOf(QRCodeResponseWrapperTO(jornada3Json)))

        // when
        val result = adapter.parseQRCodeCacheable(pixCopyAndPaste, document)

        // then
        result.isRight() shouldBe true
        val successResult = result.getOrNull()
        successResult.shouldNotBeNull()
        successResult.qrCodeInfo?.type shouldBe PixQrCodeType.COMPOUND
        successResult.qrCodeInfo?.automaticPixRecurringDataJson.shouldBeTypeOf<String>()
        val parsed = parseObjectFrom<AutomaticPixQrCodeRecurringData>(successResult.qrCodeInfo?.automaticPixRecurringDataJson!!)
        parsed.fixedAmount shouldBe 82884L
        parsed.payer.documentNumber shouldBe "54403563000150"
        parsed.payer.name shouldBe "BCO ARBI S.A."
        parsed.payer.documentType shouldBe AutomaticPixDocumentType.CNPJ
        parsed.payee.documentNumber shouldBe "99999918999924"
        parsed.payee.name shouldBe "Pix Tester 918"
        parsed.payee.documentType shouldBe AutomaticPixDocumentType.CNPJ
        parsed.startDate.shouldNotBeNull()
        parsed.periodicity shouldBe AutomaticPixPeriodicity.MONTHLY
        parsed.journey shouldBe AutomaticPixJourney.JOURNEY_3
        parsed.retryType shouldBe AutomaticPixRetryType.ALLOWED_3R_7D
        parsed.recurringPaymentId.value shouldBe "RR999999182025061231681110433"
    }

    @Test
    fun `deve processar QR Code com sucesso da Jornada 4`() {
        // given
        val pixCopyAndPaste = PixCopyAndPaste("00020101021226990014br.gov.bcb.pix2577qr-h.sandbox.pix.bcb.gov.br/rest/api/v2/cobv/8029c7eae9f74b9e8b3ba1f3fee550ba5204000053039865802BR5913Fulano de Tal6008BRASILIA62070503***80950014br.gov.bcb.pix2573qr-h.sandbox.pix.bcb.gov.br/rest/api/rec/********************************630420B5")
        val document = "12345678900"
        val jornada3Json = parseObjectFrom<String>(
            """
            "{\"nome\":\"Pix Tester 918\",\"cpfCnpj\":\"99999918999924\",\"nomePsp\":\"Banco 99999918\",\"codInstituicao\":\"99999918\",\"codAgencia\":\"0001\",\"nroConta\":\"94937\",\"tipoConta\":\"CACC\",\"dataCriacao\":\"2025-05-08T18:30:07.583Z\",\"dataPosse\":\"2025-05-08T18:30:07.583Z\",\"dataAbertura\":\"2010-01-10T03:00:00Z\",\"referencia\":\"8029c7eae9f74b9e8b3ba1f3fee550ba\",\"info\":\"Dados recuperados do link 'https://qr-h.sandbox.pix.bcb.gov.br/rest/api/rec/********************************'\",\"valor\":117.85,\"tipoQRCode\":\"COMPOSTO\",\"endToEnd\":\"E54403563202506121938uZN8NO0Grhn\",\"calendarioVencimento\":\"2025-06-22\",\"calendarioCriacao\":\"2025-06-12T16:36:48.036Z\",\"validadeAposVencimento\":30,\"tipoPessoa\":\"LEGAL_PERSON\",\"pagadorCnpj\":\"54403563000150\",\"pagadorNome\":\"BCO ARBI S.A.\",\"recebedorCnpj\":\"99999918999924\",\"recebedorNome\":\"Pix Tester 99999918\",\"recebedorLogradouro\":\"Setor Bancario Sul SBS Quadra 3 Bloco B Ed Sede\",\"recebedorCidade\":\"Brasilia\",\"recebedorUF\":\"DF\",\"recebedorCep\":\"70074900\",\"valorFinal\":911.96,\"tipoChave\":\"EVP\",\"chaveEnderecamento\":\"f4c6089a-bfde-4c00-a2d9-9eaa584b0219\",\"estatisticas\":{},\"tipoPix\":\"RECORRENTE\",\"dadosRecorrencia\":{\"idRec\":\"RR999999182025061262115046113\",\"jornada\":\"JORNADA_4\",\"retentativa\":\"PERMITE_3R_7D\",\"valorRec\":117.85,\"ispbRecebedor\":\"99999918\",\"nomeRecebedor\":\"Pix Tester 918\",\"cnpjRecebedor\":\"99999918999924\",\"cnpjPagador\":\"54403563000150\",\"nomePagador\":\"BCO ARBI S.A.\",\"calendarioInicial\":\"2025-06-12\",\"periodicidade\":\"MENSAL\",\"contratoVinculo\":\"40502310\",\"objetoVinculo\":\"Serviço de Academia\",\"atualizacao\":[{\"data\":\"2025-06-12T00:00:00.000Z\",\"status\":\"CRIADA\"}]}}"
            """.trimIndent(),
        )

        every {
            httpClient.retrieve(
                any<MutableHttpRequest<*>>(),
                Argument.listOf(QRCodeResponseWrapperTO::class.java),
                Argument.STRING,
            )
        } returns Flowable.just(listOf(QRCodeResponseWrapperTO(jornada3Json)))

        // when
        val result = adapter.parseQRCodeCacheable(pixCopyAndPaste, document)

        // then
        result.isRight() shouldBe true
        assertSoftly {
            val successResult = result.getOrNull()
            successResult.shouldNotBeNull()
            successResult.qrCodeInfo?.type shouldBe PixQrCodeType.COMPOUND
            successResult.qrCodeInfo?.automaticPixRecurringDataJson.shouldBeTypeOf<String>()
            val parsed = parseObjectFrom<AutomaticPixQrCodeRecurringData>(successResult.qrCodeInfo?.automaticPixRecurringDataJson!!)
            parsed.fixedAmount shouldBe 11785L
            parsed.payer.documentNumber shouldBe "54403563000150"
            parsed.payer.name shouldBe "BCO ARBI S.A."
            parsed.payer.documentType shouldBe AutomaticPixDocumentType.CNPJ
            parsed.payee.documentNumber shouldBe "99999918999924"
            parsed.payee.name shouldBe "Pix Tester 918"
            parsed.payee.documentType shouldBe AutomaticPixDocumentType.CNPJ
            parsed.startDate.shouldNotBeNull()
            parsed.periodicity shouldBe AutomaticPixPeriodicity.MONTHLY
            parsed.journey shouldBe AutomaticPixJourney.JOURNEY_4
            parsed.retryType shouldBe AutomaticPixRetryType.ALLOWED_3R_7D
            parsed.recurringPaymentId.value shouldBe "RR999999182025061262115046113"
        }
    }

    @Test
    fun `deve processar QR Code com sucesso da Jornada 4 - com qr de cobranca`() {
        // given
        val pixCopyAndPaste = PixCopyAndPaste("00020126780014br.gov.bcb.pix0136f4c6089a-bfde-4c00-a2d9-9eaa584b02190216CobrancaEstatica5204000053039865406532.875802BR5903Pix6008BRASILIA62290525b1cd4f3c8d324e45887b5eed180950014br.gov.bcb.pix2573qr-h.sandbox.pix.bcb.gov.br/rest/api/rec/********************************63040F1A")
        val document = "12345678900"
        val jornada3Json = parseObjectFrom<String>(
            """
            "{\"nome\":\"Pix Tester 918\",\"cpfCnpj\":\"99999918999924\",\"nomePsp\":\"Banco 99999918\",\"codInstituicao\":\"99999918\",\"codAgencia\":\"0001\",\"nroConta\":\"94937\",\"tipoConta\":\"CACC\",\"dataCriacao\":\"2025-05-08T18:30:07.583Z\",\"dataPosse\":\"2025-05-08T18:30:07.583Z\",\"dataAbertura\":\"2010-01-10T03:00:00Z\",\"referencia\":\"b1cd4f3c8d324e45887b5eed1\",\"info\":\"Dados recuperados do link 'https://qr-h.sandbox.pix.bcb.gov.br/rest/api/rec/********************************'\",\"valor\":645.09,\"tipoQRCode\":\"COMPOSTO\",\"endToEnd\":\"E54403563202506121953jDIRgHQSb4P\",\"tipoPessoa\":\"LEGAL_PERSON\",\"pagadorCnpj\":\"54403563000150\",\"pagadorNome\":\"BCO ARBI S.A.\",\"recebedorCnpj\":\"99999918999924\",\"recebedorNome\":\"Pix Tester 99999918\",\"recebedorCidade\":\"BRASILIA\",\"valorFinal\":532.87,\"tipoChave\":\"EVP\",\"chaveEnderecamento\":\"f4c6089a-bfde-4c00-a2d9-9eaa584b0219\",\"estatisticas\":{},\"tipoPix\":\"RECORRENTE\",\"dadosRecorrencia\":{\"idRec\":\"RR999999182025061222675340329\",\"jornada\":\"JORNADA_4\",\"retentativa\":\"PERMITE_3R_7D\",\"valorRec\":645.09,\"ispbRecebedor\":\"99999918\",\"nomeRecebedor\":\"Pix Tester 918\",\"cnpjRecebedor\":\"99999918999924\",\"cnpjPagador\":\"54403563000150\",\"nomePagador\":\"BCO ARBI S.A.\",\"calendarioInicial\":\"2025-06-12\",\"periodicidade\":\"MENSAL\",\"contratoVinculo\":\"87871098\",\"objetoVinculo\":\"Serviço de Streaming de música\",\"atualizacao\":[{\"data\":\"2025-06-12T00:00:00.000Z\",\"status\":\"CRIADA\"}]}}"
            """.trimIndent(),
        )

        every {
            httpClient.retrieve(
                any<MutableHttpRequest<*>>(),
                Argument.listOf(QRCodeResponseWrapperTO::class.java),
                Argument.STRING,
            )
        } returns Flowable.just(listOf(QRCodeResponseWrapperTO(jornada3Json)))

        // when
        val result = adapter.parseQRCodeCacheable(pixCopyAndPaste, document)

        // then
        result.isRight() shouldBe true
        assertSoftly {
            val successResult = result.getOrNull()
            successResult.shouldNotBeNull()
            successResult.qrCodeInfo?.type shouldBe PixQrCodeType.COMPOUND
            successResult.qrCodeInfo?.automaticPixRecurringDataJson.shouldBeTypeOf<String>()
            val parsed = parseObjectFrom<AutomaticPixQrCodeRecurringData>(successResult.qrCodeInfo?.automaticPixRecurringDataJson!!)
            parsed.fixedAmount shouldBe 64509L
            parsed.payer.documentNumber shouldBe "54403563000150"
            parsed.payer.name shouldBe "BCO ARBI S.A."
            parsed.payer.documentType shouldBe AutomaticPixDocumentType.CNPJ
            parsed.payee.documentNumber shouldBe "99999918999924"
            parsed.payee.name shouldBe "Pix Tester 918"
            parsed.payee.documentType shouldBe AutomaticPixDocumentType.CNPJ
            parsed.startDate.shouldNotBeNull()
            parsed.periodicity shouldBe AutomaticPixPeriodicity.MONTHLY
            parsed.journey shouldBe AutomaticPixJourney.JOURNEY_4
            parsed.retryType shouldBe AutomaticPixRetryType.ALLOWED_3R_7D
            parsed.recurringPaymentId.value shouldBe "RR999999182025061222675340329"
        }
    }

    @Test
    fun `deve processar com sucesso o QRCode da Jornada 3 de producao`() {
        val pixCopyAndPaste = PixCopyAndPaste("ANY")
        val document = "12345678900"
        val jornada3Json = parseObjectFrom<String>(
            """
        "{\"nome\":\"GLOBO COMUNICACAO E PARTICIPACOES SA\",\"cpfCnpj\":\"27865757006992\",\"nomePsp\":\"BCO BRADESCO S.A.\",\"codInstituicao\":\"60746948\",\"codAgencia\":\"2753\",\"nroConta\":\"99066\",\"tipoConta\":\"CACC\",\"dataCriacao\":\"2025-06-12T14:03:43.554Z\",\"dataPosse\":\"2025-06-12T14:03:43.554Z\",\"dataAbertura\":\"2018-08-08T00:00:00Z\",\"referencia\":\"0000000000000e36310F18256nbvo681382\",\"info\":\"Dados recuperados do link 'https://apipix.bradesco.com.br/qr/v2/rec/38ab7442c98c4e55ad15580f6ee5b723'\",\"valor\":178.80,\"tipoQRCode\":\"COMPOSTO\",\"endToEnd\":\"E544035632025062314318bhGLDOxg3l\",\"calendarioExpiracaoSegundos\":480,\"calendarioCriacao\":\"2025-06-23T14:31:25.237Z\",\"tipoPessoa\":\"LEGAL_PERSON\",\"pagadorCpf\":\"11555264735\",\"pagadorNome\":\"Marlon Monçores\",\"recebedorCnpj\":\"27865757006992\",\"recebedorNome\":\"GLOBO COMUNICACAO E PARTICIPACOES S/A\",\"recebedorNomeFantasia\":\"GLOBOPAR, TV GLOBO, REDE GLOBO E GLOBO.COM\",\"recebedorCidade\":\"SAO PAULO\",\"valorFinal\":178.80,\"tipoChave\":\"EVP\",\"chaveEnderecamento\":\"ef5f4ae6-1c11-4cca-858b-79fdf9a65cf4\",\"estatisticas\":{},\"tipoPix\":\"RECORRENTE\",\"dadosRecorrencia\":{\"idRec\":\"RR6074694820250623c7d33f43d15\",\"jornada\":\"JORNADA_3\",\"retentativa\":\"PERMITE_3R_7D\",\"valorMinimo\":215.00,\"ispbRecebedor\":\"60746948\",\"nomeRecebedor\":\"GLOBO COMUNICACAO E PARTICIPACOES SA\",\"cnpjRecebedor\":\"27865757006992\",\"cpfPagador\":\"11555264735\",\"nomePagador\":\"Marlon Monçores\",\"calendarioInicial\":\"2026-06-23\",\"periodicidade\":\"ANUAL\",\"contratoVinculo\":\"e36310F18256nbvo681382\",\"atualizacao\":[{\"data\":\"2025-06-23T11:31:25.621Z\",\"status\":\"CRIADA\"}]}}"    
            """.trimIndent(),
        )

        every {
            httpClient.retrieve(
                any<MutableHttpRequest<*>>(),
                Argument.listOf(QRCodeResponseWrapperTO::class.java),
                Argument.STRING,
            )
        } returns Flowable.just(listOf(QRCodeResponseWrapperTO(jornada3Json)))

        // when
        val result = adapter.parseQRCodeCacheable(pixCopyAndPaste, document)

        // then
        result.isRight() shouldBe true
        assertSoftly {
            val successResult = result.getOrNull()
            successResult.shouldNotBeNull()
            successResult.qrCodeInfo?.type shouldBe PixQrCodeType.COMPOUND
            successResult.qrCodeInfo?.automaticPixRecurringDataJson.shouldBeTypeOf<String>()
            val parsed = parseObjectFrom<AutomaticPixQrCodeRecurringData>(successResult.qrCodeInfo?.automaticPixRecurringDataJson!!)
            parsed.fixedAmount shouldBe null
            parsed.payer.documentNumber shouldBe "11555264735"
            parsed.payer.name shouldBe "Marlon Monçores"
            parsed.payer.documentType shouldBe AutomaticPixDocumentType.CPF
            parsed.payee.documentNumber shouldBe "27865757006992"
            parsed.payee.name shouldBe "GLOBO COMUNICACAO E PARTICIPACOES SA"
            parsed.payee.documentType shouldBe AutomaticPixDocumentType.CNPJ
            parsed.startDate.shouldNotBeNull()
            parsed.periodicity shouldBe AutomaticPixPeriodicity.YEARLY
            parsed.journey shouldBe AutomaticPixJourney.JOURNEY_3
            parsed.retryType shouldBe AutomaticPixRetryType.ALLOWED_3R_7D
            parsed.recurringPaymentId.value shouldBe "RR6074694820250623c7d33f43d15"
        }
    }
}