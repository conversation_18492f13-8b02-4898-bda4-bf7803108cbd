package ai.friday.billpayment.modules.automaticpix.adapters.dynamodb

import DynamoDBUtils
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPayment
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentOnWallet
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentStatus
import ai.friday.billpayment.modules.automaticpix.createAutomaticPixRecurringPayment
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class AutomaticPixRecurringPaymentDynamoDBRepositoryTest {
    private val dynamoDbEnhancedClient = DynamoDBUtils.setupDynamoDB()

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()
    private val walletId = wallet.id

    private val repository = AutomaticPixRecurringPaymentDynamoDBRepository(dynamoDAO = AutomaticPixRecurringPaymentDynamoDAO(dynamoDbEnhancedClient))

    @Test
    fun `deve salvar um AutomaticPixRecurringPayment com sucesso`() {
        val automaticPixRecurringPayment = createAutomaticPixRecurringPayment() onWallet walletId

        val saved = repository.save(automaticPixRecurringPayment)

        saved shouldBe automaticPixRecurringPayment
        repository.findByIdAndWalletId(automaticPixRecurringPayment.automaticPixRecurringPayment.recurringPaymentId, walletId = walletId) shouldBe automaticPixRecurringPayment
    }

    @Test
    fun `deve encontrar um AutomaticPixRecurringPayment pelo ID`() {
        val automaticPixRecurringPayment = createAutomaticPixRecurringPayment() onWallet walletId
        val saved = repository.save(automaticPixRecurringPayment)

        saved shouldBe automaticPixRecurringPayment

        val found = repository.findByIdAndWalletId(automaticPixRecurringPayment.automaticPixRecurringPayment.recurringPaymentId, walletId = walletId)
        found shouldBe automaticPixRecurringPayment
    }

    @Test
    fun `deve encontrar AutomaticPixRecurringPayment por walletId`() {
        val automaticPixRecurringPayment1 = createAutomaticPixRecurringPayment(status = AutomaticPixRecurringPaymentStatus.APPROVED) onWallet walletId
        val automaticPixRecurringPayment2 = createAutomaticPixRecurringPayment(
            recurringPaymentId = "rec-456",
        ) onWallet walletId
        val automaticPixRecurringPayment3 = createAutomaticPixRecurringPayment(
            recurringPaymentId = "rec-789",
        ) onWallet WalletId(WALLET_ID)

        repository.save(automaticPixRecurringPayment1)
        repository.save(automaticPixRecurringPayment2)
        repository.save(automaticPixRecurringPayment3)

        val foundByWalletId = repository.findByWalletId(walletId)

        foundByWalletId.size shouldBe 2
        foundByWalletId.shouldContainExactlyInAnyOrder(automaticPixRecurringPayment1, automaticPixRecurringPayment2)
    }

    @Test
    fun `deve encontrar AutomaticPixRecurringPayment por ID em diferentes wallets`() {
        val automaticPixRecurringPayment1 = createAutomaticPixRecurringPayment() onWallet walletId
        val automaticPixRecurringPayment2 = createAutomaticPixRecurringPayment() onWallet WalletId(WALLET_ID)

        repository.save(automaticPixRecurringPayment1)
        repository.save(automaticPixRecurringPayment2)

        val foundById = repository.findById(automaticPixRecurringPayment1.automaticPixRecurringPayment.recurringPaymentId)

        foundById.size shouldBe 2
        foundById.shouldContainExactlyInAnyOrder(automaticPixRecurringPayment1, automaticPixRecurringPayment2)
    }
}

private infix fun AutomaticPixRecurringPayment.onWallet(walletId: WalletId): AutomaticPixRecurringPaymentOnWallet {
    return AutomaticPixRecurringPaymentOnWallet(
        walletId = walletId,
        automaticPixRecurringPayment = this,
    )
}