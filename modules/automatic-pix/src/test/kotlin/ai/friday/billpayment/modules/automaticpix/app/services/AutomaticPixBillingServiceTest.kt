package ai.friday.billpayment.modules.automaticpix.app.services

import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateBillService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.internalBankAccount
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixBillOnWallet
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixBillRepository
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRemoteManager
import ai.friday.billpayment.modules.automaticpix.createAutomaticPixBill
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate
import org.junit.jupiter.api.Test

class AutomaticPixBillingServiceTest {

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    private val walletId = wallet.id
    private val accountId = wallet.founder.accountId
    private val paymentMethodId = wallet.paymentMethodId

    private val bankAccount = internalBankAccount

    private val automaticPixRemoteManager: AutomaticPixRemoteManager = mockk()
    private val walletService: WalletService = mockk {
        every { findWallet(walletId) } returns wallet
    }
    private val accountService: AccountService = mockk {
        every { findAccountPaymentMethodByIdAndAccountId(paymentMethodId, accountId) } returns
            mockk {
                every { method } returns bankAccount
            }
    }
    private val automaticPixMessagePublisher: AutomaticPixMessagePublisher = mockk {
        every { publishBillingRefresh(any()) } just Runs
        every { notifyBillCancelled(any()) } just Runs
        every { notifyBillPaymentFailed(any(), any()) } just Runs
    }
    private val automaticPixBillRepository: AutomaticPixBillRepository = mockk()
    private val createBillService: CreateBillService = mockk()

    private val service: AutomaticPixBillingService = AutomaticPixBillingService(
        automaticPixRemoteManager,
        walletService,
        accountService,
        automaticPixMessagePublisher,
        automaticPixBillRepository,
        createBillService,
        startTransaction = mockk(),
        completeTransaction = mockk(),
        failTransaction = mockk(),
        transactionService = mockk(),
    )

    @Test
    fun `deve atualizar cobrancas com sucesso quando houver novas cobrancas remotas`() {
        val remoteBills = listOf(
            createAutomaticPixBill("end-to-end-1"),
            createAutomaticPixBill("end-to-end-2"),
        )
        every { automaticPixRemoteManager.listBilling(any(), null) } returns Result.success(remoteBills)

        val localBills = listOf(
            createAutomaticPixBillOnWallet("end-to-end-1"),
        )
        every { automaticPixBillRepository.findByWalletId(walletId) } returns localBills

        every { createBillService.createPix(any(), false) } returns CreateBillResult.SUCCESS(
            bill = mockk(relaxed = true),
        )

        every { automaticPixBillRepository.save(any(), any(), any()) } returns mockk()

        // Act
        val result = service.refreshBilling(walletId)

        result.isSuccess shouldBe true
        val bills = result.getOrNull()

        bills.shouldNotBeNull()
        bills.size shouldBe 2

        verify(exactly = 1) { automaticPixBillRepository.save(any(), any(), any()) }
        verify(exactly = 0) { automaticPixBillRepository.remove(any(), any()) }
    }

    @Test
    fun `deve remover cobrancas locais que nao existem mais remotamente`() {
        val remoteBills = listOf(
            createAutomaticPixBill("end-to-end-1"),
        )
        every { automaticPixRemoteManager.listBilling(any(), null) } returns Result.success(remoteBills)

        val localBills = listOf(
            createAutomaticPixBillOnWallet("end-to-end-1"),
            createAutomaticPixBillOnWallet("end-to-end-2"),
        )
        every { automaticPixBillRepository.findByWalletId(walletId) } returns localBills
        every { automaticPixBillRepository.remove(any(), any()) } returns Unit

        // Act
        val result = service.refreshBilling(walletId)

        result.isSuccess shouldBe true
        val bills = result.getOrNull()

        bills.shouldNotBeNull()
        bills.size shouldBe 1

        verify(exactly = 0) { automaticPixBillRepository.save(any(), any(), any()) }
        verify(exactly = 1) { automaticPixBillRepository.remove(any(), any()) }
        verify(exactly = 0) { automaticPixMessagePublisher.notifyBillPaymentFailed(any(), any()) }
    }

    @Test
    fun `deve lidar com erro ao criar nova cobranca`() {
        val remoteBills = listOf(
            createAutomaticPixBill("end-to-end-1"),
            createAutomaticPixBill("end-to-end-2"),
        )
        every { automaticPixRemoteManager.listBilling(any(), null) } returns Result.success(remoteBills)

        val localBills = emptyList<AutomaticPixBillOnWallet>()
        every { automaticPixBillRepository.findByWalletId(walletId) } returns localBills

        every { createBillService.createPix(any(), false) } returns CreateBillResult.FAILURE.ServerError(
            Exception("Erro ao criar cobrança"),
        )

        val result = service.refreshBilling(walletId)

        result.isSuccess shouldBe true
        val bills = result.getOrNull()

        bills.shouldNotBeNull()
        bills.size shouldBe 0

        verify(exactly = 0) { automaticPixBillRepository.save(any(), any(), any()) }
        verify(exactly = 0) { automaticPixBillRepository.remove(any(), any()) }
    }

    @Test
    fun `deve atualizar cobranca existente quando houver mudancas remotas`() {
        val remoteBill = createAutomaticPixBill("end-to-end-1", settlementDate = getLocalDate().plusDays(1))
        val remoteBills = listOf(remoteBill)
        every { automaticPixRemoteManager.listBilling(any(), null) } returns Result.success(remoteBills)

        val localBill = createAutomaticPixBillOnWallet("end-to-end-1", settlementDate = getLocalDate())
        val localBills = listOf(localBill)
        every { automaticPixBillRepository.findByWalletId(walletId) } returns localBills
        every { automaticPixBillRepository.save(any(), any(), any()) } returns mockk()

        // Act
        val result = service.refreshBilling(walletId)

        result.isSuccess shouldBe true
        val bills = result.getOrNull()

        bills.shouldNotBeNull()
        bills.size shouldBe 1

        verify(exactly = 1) { automaticPixBillRepository.save(any(), any(), any()) }
        verify(exactly = 1) { automaticPixMessagePublisher.notifyBillCancelled(any()) }
    }

    @Test
    fun `deve cancelar cobrança automatica com sucesso quando cobrança existe e tem data futura`() {
        val endToEnd = "end-to-end-123"
        val bill = createAutomaticPixBillOnWallet(endToEnd, settlementDate = getLocalDate().plusDays(1))

        every { automaticPixBillRepository.findByEndToEnd(endToEnd) } returns listOf(bill)
        every { automaticPixRemoteManager.cancelBilling(endToEnd) } returns Result.success(Unit)
        every { automaticPixMessagePublisher.publishBillingRefresh(walletId) } just Runs

        val result = service.cancelBilling(walletId, endToEnd)

        result.isSuccess shouldBe true
        verify(exactly = 1) { automaticPixRemoteManager.cancelBilling(endToEnd) }
        verify(exactly = 1) { automaticPixMessagePublisher.publishBillingRefresh(walletId) }
    }

    @Test
    fun `deve falhar ao cancelar cobrança quando não existe cobrança com endToEnd fornecido`() {
        val endToEnd = "end-to-end-inexistente"

        every { automaticPixBillRepository.findByEndToEnd(endToEnd) } returns emptyList()

        val result = service.cancelBilling(walletId, endToEnd)

        result.isFailure shouldBe true
        result.exceptionOrNull() shouldBe IllegalStateException("Bill not found for endToEnd: $endToEnd")
        verify(exactly = 0) { automaticPixRemoteManager.cancelBilling(any()) }
        verify(exactly = 0) { automaticPixMessagePublisher.publishBillingRefresh(any()) }
    }

    @Test
    fun `deve falhar ao cancelar cobrança quando cobrança não pertence à carteira`() {
        val endToEnd = "end-to-end-123"
        val otherWalletId = WalletId()
        val bill = createAutomaticPixBillOnWallet(endToEnd).copy(walletId = otherWalletId)

        every { automaticPixBillRepository.findByEndToEnd(endToEnd) } returns listOf(bill)

        val result = service.cancelBilling(walletId, endToEnd)

        result.isFailure shouldBe true
        result.exceptionOrNull() shouldBe IllegalStateException("Bill not found for walletId: $walletId and endToEnd: $endToEnd")
        verify(exactly = 0) { automaticPixRemoteManager.cancelBilling(any()) }
        verify(exactly = 0) { automaticPixMessagePublisher.publishBillingRefresh(any()) }
    }

    @Test
    fun `deve falhar ao cancelar cobrança quando data de liquidação não é futura`() {
        val endToEnd = "end-to-end-123"
        val billWithPastDate = createAutomaticPixBillOnWallet(endToEnd, settlementDate = getLocalDate().minusDays(1))

        every { automaticPixBillRepository.findByEndToEnd(endToEnd) } returns listOf(billWithPastDate)

        val result = service.cancelBilling(walletId, endToEnd)

        result.isFailure shouldBe true
        result.exceptionOrNull() shouldBe IllegalStateException("Cannot cancel bill that is not in the future: $endToEnd")
        verify(exactly = 0) { automaticPixRemoteManager.cancelBilling(any()) }
        verify(exactly = 0) { automaticPixMessagePublisher.publishBillingRefresh(any()) }
    }

    @Test
    fun `deve falhar ao cancelar cobrança quando data de liquidação é hoje`() {
        val endToEnd = "end-to-end-123"
        val billWithTodayDate = createAutomaticPixBillOnWallet(endToEnd, settlementDate = getLocalDate())

        every { automaticPixBillRepository.findByEndToEnd(endToEnd) } returns listOf(billWithTodayDate)

        val result = service.cancelBilling(walletId, endToEnd)

        result.isFailure shouldBe true
        result.exceptionOrNull() shouldBe IllegalStateException("Cannot cancel bill that is not in the future: $endToEnd")
        verify(exactly = 0) { automaticPixRemoteManager.cancelBilling(any()) }
        verify(exactly = 0) { automaticPixMessagePublisher.publishBillingRefresh(any()) }
    }

    @Test
    fun `deve falhar quando cancelamento remoto falha`() {
        val endToEnd = "end-to-end-123"
        val bill = createAutomaticPixBillOnWallet(endToEnd, settlementDate = getLocalDate().plusDays(1))

        every { automaticPixBillRepository.findByEndToEnd(endToEnd) } returns listOf(bill)
        every { automaticPixRemoteManager.cancelBilling(endToEnd) } returns Result.failure(Exception("Erro no cancelamento remoto"))

        val result = service.cancelBilling(walletId, endToEnd)

        result.isFailure shouldBe true
        result.exceptionOrNull() shouldBe Exception("Erro no cancelamento remoto")
        verify(exactly = 1) { automaticPixRemoteManager.cancelBilling(endToEnd) }
        verify(exactly = 0) { automaticPixMessagePublisher.publishBillingRefresh(any()) }
    }

    @Test
    fun `deve retornar todas as cobranças armazenadas localmente`() {
        val localBills = listOf(
            createAutomaticPixBillOnWallet("end-to-end-1"),
            createAutomaticPixBillOnWallet("end-to-end-2"),
        )
        every { automaticPixBillRepository.findByWalletId(walletId) } returns localBills

        val result = service.getAllStoredBills(walletId)

        result.isSuccess shouldBe true
        val bills = result.getOrNull()
        bills.shouldNotBeNull()
        bills.size shouldBe 2
        bills shouldBe localBills
    }

    @Test
    fun `deve falhar quando listagem remota falha`() {
        val localBills = listOf(
            createAutomaticPixBillOnWallet("end-to-end-1"),
            createAutomaticPixBillOnWallet("end-to-end-2"),
        )
        every { automaticPixBillRepository.findByWalletId(walletId) } returns localBills

        every { automaticPixRemoteManager.listBilling(any(), null) } returns Result.failure(Exception("Erro na listagem remota"))

        val result = service.refreshBilling(walletId)

        result.isFailure shouldBe true
        result.exceptionOrNull() shouldBe Exception("Erro na listagem remota")
    }

    private fun createAutomaticPixBillOnWallet(endToEnd: String, settlementDate: LocalDate = getLocalDate()): AutomaticPixBillOnWallet {
        return AutomaticPixBillOnWallet(
            walletId = walletId,
            billId = BillId(),
            automaticPixBill = createAutomaticPixBill(endToEnd, settlementDate),
        )
    }
}