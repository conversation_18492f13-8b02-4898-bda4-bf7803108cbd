package ai.friday.billpayment.modules.automaticpix.app.services

import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.PixCopyAndPaste
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.integrations.PixQRCodeParserService
import ai.friday.billpayment.app.pix.PixQRCodeDetailsResult
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixJourney
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateRepository
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurrenceCancellationReason
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPayment
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentId
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentOnWallet
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentRepository
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentStatus
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRemoteManager
import ai.friday.billpayment.modules.automaticpix.app.RecurrenceUpdateableValues
import ai.friday.billpayment.modules.automaticpix.createAutomaticPixQrCodeRecurringData
import ai.friday.billpayment.modules.automaticpix.createBaseAuthorization
import ai.friday.morning.json.getObjectMapper
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class AutomaticPixRecurrenceServiceTest {

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet(otherMembers = listOf(walletFixture.cantPayParticipant))
    private val walletId = wallet.id
    private val accountId = wallet.founder.accountId

    private val bankAccount = mockk<InternalBankAccount> {
        every { buildFullAccountNumber() } returns "123456"
    }

    private val automaticPixRemoteManager: AutomaticPixRemoteManager = mockk()
    private val walletService: WalletService = mockk {
        every { findWallet(walletId) } returns wallet
    }
    private val accountService: AccountService = mockk {
        every { findAccountPaymentMethodByIdAndAccountId(wallet.paymentMethodId, wallet.founder.accountId) } returns mockk {
            every { method } returns bankAccount
        }
    }
    private val automaticPixMessagePublisher: AutomaticPixMessagePublisher = mockk {
        every { publishMandateRefresh(any()) } just Runs
        every { publishRecurreceRefresh(any()) } just Runs
        every { notifyRecurrenceApproved(any()) } just Runs
        every { notifyRecurrenceStatusChange(any(), any()) } just Runs
        every { notifyAutomaticRecurrenceAcceptFailed(any(), any(), any()) } just Runs
    }
    private val automaticPixRecurringPaymentRepository: AutomaticPixRecurringPaymentRepository = mockk {
        every { save(any()) } answers { firstArg() }
        every { findByWalletId(walletId) } returns emptyList()
    }
    private val findBillService: FindBillService = mockk()
    private val qrCodeParser: PixQRCodeParserService = mockk()
    private val mandateRepository: AutomaticPixMandateRepository = mockk {
        every { findByMandateIdAndWalletId(any(), any()) } returns createBaseAuthorization()
    }

    private val service = AutomaticPixRecurrenceService(
        automaticPixRemoteManager = automaticPixRemoteManager,
        automaticPixRecurringPaymentRepository = automaticPixRecurringPaymentRepository,
        walletService = walletService,
        accountService = accountService,
        automaticPixMessagePublisher = automaticPixMessagePublisher,
        findBillService = findBillService,
        qrCodeParser = qrCodeParser,
        mandateRepository = mandateRepository,
    )

    @Nested
    inner class GetAllStoredRecurrences {
        @Test
        fun `deve retornar todas as recorrências armazenadas`() {
            val storedRecurrences = listOf(
                mockk<AutomaticPixRecurringPaymentOnWallet>(relaxed = true),
                mockk<AutomaticPixRecurringPaymentOnWallet>(relaxed = true),
            )
            every { automaticPixRecurringPaymentRepository.findByWalletId(walletId) } returns storedRecurrences

            val result = service.getAllStoredRecurrences(walletId).getOrThrow()

            result.size shouldBe 2
            verify(exactly = 1) { automaticPixRecurringPaymentRepository.findByWalletId(walletId) }
        }
    }

    @Nested
    inner class RefreshRecurrences {
        @Test
        fun `deve atualizar recorrências com sucesso`() {
            val remoteRecurrences = listOf(
                mockk<AutomaticPixRecurringPayment>(relaxed = true),
                mockk<AutomaticPixRecurringPayment>(relaxed = true),
            )
            every { automaticPixRemoteManager.getApprovedRecurrences("123456", null) } returns Result.success(remoteRecurrences)

            val result = service.refreshRecurrences(walletId).getOrThrow()

            result.size shouldBe 2
            verify(exactly = 2) { automaticPixRecurringPaymentRepository.save(any()) }
            verify(exactly = 2) { automaticPixMessagePublisher.notifyRecurrenceApproved(any()) }
        }

        @Test
        fun `deve notificar mudança de status quando recorrência local não é encontrada no remoto`() {
            val localRecurrence = mockk<AutomaticPixRecurringPaymentOnWallet>(relaxed = true) {
                every { automaticPixRecurringPayment.recurringPaymentId } returns AutomaticPixRecurringPaymentId("rec-123")
                every { automaticPixRecurringPayment.status } returns AutomaticPixRecurringPaymentStatus.CREATED
            }
            every { automaticPixRecurringPaymentRepository.findByWalletId(walletId) } returns listOf(localRecurrence)
            every { automaticPixRemoteManager.getApprovedRecurrences("123456", null) } returns Result.success(emptyList())
            every { automaticPixRemoteManager.getRecurrenceStatus(AutomaticPixRecurringPaymentId("rec-123")) } returns Result.success(AutomaticPixRecurringPaymentStatus.APPROVED)

            val result = service.refreshRecurrences(walletId).getOrThrow()

            result.size shouldBe 0
            verify(exactly = 1) { automaticPixMessagePublisher.notifyRecurrenceStatusChange(localRecurrence, AutomaticPixRecurringPaymentStatus.APPROVED) }
        }

        @Test
        fun `deve falhar quando busca de recorrências remotas falha`() {
            every { automaticPixRemoteManager.getApprovedRecurrences("123456", null) } returns Result.failure(Exception("Erro na busca remota"))

            val result = service.refreshRecurrences(walletId)

            result.isFailure shouldBe true
            result.exceptionOrNull() shouldBe Exception("Erro na busca remota")
        }
    }

    @Nested
    inner class CancelRecurrence {

        @BeforeEach
        fun setup() {
            every { automaticPixRemoteManager.getApprovedRecurrences("123456", null) } returns Result.success(emptyList())
        }

        @Test
        fun `deve cancelar recorrência com sucesso`() {
            val recurringPaymentId = AutomaticPixRecurringPaymentId("rec-123")
            val cancellationReason = AutomaticPixRecurrenceCancellationReason.USER_REQUEST

            every { automaticPixRemoteManager.cancelRecurrence(recurringPaymentId, cancellationReason) } returns Result.success(Unit)
            every { automaticPixMessagePublisher.publishMandateRefresh(walletId) } just Runs
            every { automaticPixRecurringPaymentRepository.findByIdAndWalletId(recurringPaymentId, walletId) } returns mockk(relaxed = true)

            val result = service.cancelRecurrence(recurringPaymentId, cancellationReason, accountId, walletId)

            result.isSuccess shouldBe true
            verify(exactly = 1) { automaticPixRemoteManager.cancelRecurrence(recurringPaymentId, cancellationReason) }
            verify(exactly = 1) { automaticPixMessagePublisher.publishMandateRefresh(walletId) }
        }

        @Test
        fun `deve retornar erro quando usuário não tem permissão`() {
            val recurringPaymentId = AutomaticPixRecurringPaymentId("rec-123")

            every { walletService.findWallet(walletId) } returns wallet

            val result = service.cancelRecurrence(recurringPaymentId, AutomaticPixRecurrenceCancellationReason.USER_REQUEST, walletFixture.cantPayParticipant.accountId, walletId)

            result.isFailure shouldBe true
            result.exceptionOrNull()?.shouldBeTypeOf<IllegalStateException>()
            result.exceptionOrNull()?.message shouldBe "Usuário não tem permissão para agendar pagamentos na carteira"
        }
    }

    @Nested
    inner class UpdateRecurrence {

        @BeforeEach
        fun setup() {
            every { automaticPixRemoteManager.getApprovedRecurrences("123456", null) } returns Result.success(emptyList())
        }

        @Test
        fun `deve atualizar recorrência com sucesso`() {
            val recurringPaymentId = AutomaticPixRecurringPaymentId("rec-123")
            val updateableValues = RecurrenceUpdateableValues(
                maximumPayerAmount = 1000_00L,
                creditLimitUsage = true,
                schedulingNotifications = true,
            )

            every { automaticPixRemoteManager.updateRecurrence(recurringPaymentId, updateableValues) } returns Result.success(updateableValues)
            every { automaticPixMessagePublisher.publishMandateRefresh(walletId) } just Runs
            every { automaticPixRecurringPaymentRepository.findByIdAndWalletId(recurringPaymentId, walletId) } returns mockk(relaxed = true)

            val result = service.updateRecurrence(recurringPaymentId, updateableValues, accountId, walletId)

            result.isSuccess shouldBe true
            verify(exactly = 1) { automaticPixRemoteManager.updateRecurrence(recurringPaymentId, updateableValues) }
            verify(exactly = 1) { automaticPixMessagePublisher.publishMandateRefresh(walletId) }
        }

        @Test
        fun `deve retornar erro quando usuário não tem permissão`() {
            val recurringPaymentId = AutomaticPixRecurringPaymentId("rec-123")
            val updateableValues = RecurrenceUpdateableValues(
                maximumPayerAmount = 1000_00L,
                creditLimitUsage = true,
                schedulingNotifications = true,
            )

            every { walletService.findWallet(walletId) } returns wallet

            val result = service.updateRecurrence(recurringPaymentId, updateableValues, walletFixture.cantPayParticipant.accountId, walletId)

            result.isFailure shouldBe true
            result.exceptionOrNull()?.shouldBeTypeOf<IllegalStateException>()
            result.exceptionOrNull()?.message shouldBe "Usuário não tem permissão para agendar pagamentos na carteira"
        }
    }

    @Nested
    inner class ApproveRecurrenceFromBill {
        val internalWalletId = walletId

        @Test
        fun `deve aprovar recorrência a partir de bill com sucesso`() {
            val billId = BillId()
            val bill = mockk<BillView>(relaxed = true) {
                every { billType } returns BillType.PIX
                every { walletId } returns internalWalletId
                every { status } returns BillStatus.PAID
                every { pixQrCodeData?.automaticPixRecurringDataJson } returns getObjectMapper().writeValueAsString(createAutomaticPixQrCodeRecurringData(recurringPaymentId = "rec-123", journey = AutomaticPixJourney.JOURNEY_3))
                every { automaticPixAuthorizationMaximumAmount } returns 1000_00L
            }

            every { findBillService.find(walletId, billId) } returns bill
            every { automaticPixRemoteManager.approveRecurrence(any(), any(), any(), any()) } returns Result.success(Unit)

            val result = service.approveRecurrence(billId, accountId, walletId, true, 1000_00L)

            result.isSuccess shouldBe true
            verify(exactly = 1) { automaticPixRecurringPaymentRepository.save(any()) }
            verify(exactly = 1) { automaticPixMessagePublisher.publishMandateRefresh(walletId) }
            verify(exactly = 1) { automaticPixMessagePublisher.publishRecurreceRefresh(walletId) }
        }

        @Test
        fun `deve notificar falha quando aprovação automática falha`() {
            val internalBillId = BillId()
            val bill = mockk<BillView>(relaxed = true) {
                every { billType } returns BillType.PIX
                every { walletId } returns internalWalletId
                every { status } returns BillStatus.PAID
                every { pixQrCodeData?.automaticPixRecurringDataJson } returns getObjectMapper().writeValueAsString(createAutomaticPixQrCodeRecurringData(recurringPaymentId = "rec-123", journey = AutomaticPixJourney.JOURNEY_3))
                every { automaticPixAuthorizationMaximumAmount } returns 1000_00L
                every { billId } returns internalBillId
            }

            every { findBillService.find(walletId, internalBillId) } returns bill
            every { automaticPixRemoteManager.approveRecurrence(any(), any(), any(), any()) } returns Result.failure(Exception("Erro na aprovação"))

            val result = service.approveRecurrence(internalBillId, accountId, walletId, true, 1000_00L)

            result.isFailure shouldBe true
            verify(exactly = 1) { automaticPixMessagePublisher.notifyAutomaticRecurrenceAcceptFailed(walletId, internalBillId, 1000_00L) }
        }

        @Test
        fun `deve retornar erro quando bill não é do tipo PIX`() {
            val billId = BillId()
            val bill = mockk<BillView>(relaxed = true) {
                every { billType } returns BillType.AUTOMATIC_PIX
            }

            every { findBillService.find(walletId, billId) } returns bill

            val result = service.approveRecurrence(billId, accountId, walletId, true, 1000_00L)

            result.isFailure shouldBe true
            result.exceptionOrNull()?.shouldBeTypeOf<IllegalArgumentException>()
            result.exceptionOrNull()?.message shouldBe "Bill type is not PIX: AUTOMATIC_PIX"
        }

        @Test
        fun `deve retornar erro quando bill não está na carteira`() {
            val billId = BillId()
            val differentWalletId = WalletId("different-wallet")
            val bill = mockk<BillView>(relaxed = true) {
                every { billType } returns BillType.PIX
                every { walletId } returns differentWalletId
            }

            every { findBillService.find(walletId, billId) } returns bill

            val result = service.approveRecurrence(billId, accountId, walletId, true, 1000_00L)

            result.isFailure shouldBe true
            result.exceptionOrNull()?.shouldBeTypeOf<IllegalArgumentException>()
            result.exceptionOrNull()?.message shouldBe "Bill não está na carteira atual"
        }

        @Test
        fun `deve retornar erro quando bill não está pago`() {
            val billId = BillId()
            val bill = mockk<BillView>(relaxed = true) {
                every { billType } returns BillType.PIX
                every { walletId } returns internalWalletId
                every { status } returns BillStatus.PROCESSING
                every { pixQrCodeData?.automaticPixRecurringDataJson } returns getObjectMapper().writeValueAsString(createAutomaticPixQrCodeRecurringData(recurringPaymentId = "rec-123", journey = AutomaticPixJourney.JOURNEY_3))
            }

            every { findBillService.find(walletId, billId) } returns bill

            val result = service.approveRecurrence(billId, accountId, walletId, true, 1000_00L)

            result.isFailure shouldBe true
            result.exceptionOrNull()?.shouldBeTypeOf<IllegalArgumentException>()
            result.exceptionOrNull()?.message shouldBe "Bill is not paid: PROCESSING"
        }
    }

    @Nested
    inner class ApproveRecurrenceFromPixCopyAndPaste {
        @Test
        fun `deve aprovar recorrência a partir de PIX copy and paste com sucesso`() {
            val pixCopyAndPaste = PixCopyAndPaste("pix-copy-and-paste")
            val account = mockk<ai.friday.billpayment.app.account.Account>(relaxed = true) {
                every { document } returns "***********"
            }

            val pixQRCodeDetailsResult = mockk<PixQRCodeDetailsResult>(relaxed = true) {
                every { qrCodeInfo?.automaticPixRecurringDataJson } returns getObjectMapper().writeValueAsString(createAutomaticPixQrCodeRecurringData(recurringPaymentId = "rec-123", journey = AutomaticPixJourney.JOURNEY_2))
            }

            every { accountService.findAccountById(accountId) } returns account
            every { qrCodeParser.parseQRCodeCacheable(pixCopyAndPaste, "***********") } returns pixQRCodeDetailsResult.right()
            every { automaticPixRemoteManager.approveRecurrence(any(), any(), any(), any()) } returns Result.success(Unit)

            val result = service.approveRecurrence(pixCopyAndPaste, accountId, walletId, 1000_00L)

            result.isSuccess shouldBe true
            verify(exactly = 1) { automaticPixRecurringPaymentRepository.save(any()) }
            verify(exactly = 1) { automaticPixMessagePublisher.publishMandateRefresh(walletId) }
            verify(exactly = 1) { automaticPixMessagePublisher.publishRecurreceRefresh(walletId) }
        }

        @Test
        fun `deve retornar erro quando QR code não tem dados de recorrência`() {
            val pixCopyAndPaste = PixCopyAndPaste("pix-copy-and-paste")
            val account = mockk<ai.friday.billpayment.app.account.Account>(relaxed = true) {
                every { document } returns "***********"
            }

            val pixQRCodeDetailsResult = mockk<PixQRCodeDetailsResult>(relaxed = true) {
                every { qrCodeInfo?.automaticPixRecurringDataJson } returns null
            }

            every { accountService.findAccountById(accountId) } returns account
            every { qrCodeParser.parseQRCodeCacheable(pixCopyAndPaste, "***********") } returns pixQRCodeDetailsResult.right()

            val result = service.approveRecurrence(pixCopyAndPaste, accountId, walletId, 1000_00L)

            result.isFailure shouldBe true
            result.exceptionOrNull()?.shouldBeTypeOf<IllegalArgumentException>()
            result.exceptionOrNull()?.message shouldBe "Bill does not have automaticPixRecurringDataJson"
        }
    }
}