package ai.friday.billpayment.modules.automaticpix.adapters.dynamodb

import DynamoDBUtils
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.BILL_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.automaticpix.createAutomaticPixBill
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test

class AutomaticPixBillingDynamoDBRepositoryTest {

    private val dynamoDbEnhancedClient = DynamoDBUtils.setupDynamoDB()

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()
    private val walletId = wallet.id

    private val billId = BillId(BILL_ID)

    private val repository = AutomaticPixBillingDynamoDBRepository(
        dynamoDAO = AutomaticPixBillingDynamoDAO(dynamoDbEnhancedClient),
    )

    @Test
    fun `deve salvar e recuperar um AutomaticPixBill`() {
        val automaticPixBill = createAutomaticPixBill()

        // when
        val savedBill = repository.save(automaticPixBill, walletId, billId)
        val retrievedBill = repository.findByEndToEnd(automaticPixBill.endToEnd, walletId)

        // then

        savedBill shouldNotBe null
        savedBill.automaticPixBill shouldBe automaticPixBill
        savedBill.walletId shouldBe walletId

        retrievedBill shouldNotBe null
        retrievedBill?.automaticPixBill shouldBe automaticPixBill
        retrievedBill?.walletId shouldBe walletId
    }

    @Test
    fun `não deve retornar uma bill removida`() {
        val automaticPixBill = createAutomaticPixBill()

        // when
        val savedBill = repository.save(automaticPixBill, walletId, billId)
        repository.remove(automaticPixBill, walletId)

        val retrievedBill = repository.findByEndToEnd(automaticPixBill.endToEnd, walletId)
        retrievedBill shouldBe null

        val retrievedBill2 = repository.findByWalletId(walletId)
        retrievedBill2 shouldBe emptyList()

        val retrievedBill3 = repository.findByRecurringPaymentId(automaticPixBill.recurringPaymentId, walletId)
        retrievedBill3 shouldBe emptyList()
    }

    @Test
    fun `deve encontrar AutomaticPixBill por RecurringPaymentId`() {
        // given
        val walletId = WalletId("wallet-123")
        val automaticPixBill = createAutomaticPixBill()
        repository.save(automaticPixBill, walletId, billId)

        // when
        val bills = repository.findByRecurringPaymentId(automaticPixBill.recurringPaymentId, walletId)

        // then
        bills.size shouldBe 1
        bills.first().automaticPixBill shouldBe automaticPixBill
        bills.first().walletId shouldBe walletId
    }

    @Test
    fun `deve encontrar AutomaticPixBill por WalletId`() {
        // given
        val walletId = WalletId("wallet-123")
        val automaticPixBill = createAutomaticPixBill()
        repository.save(automaticPixBill, walletId, billId)

        // when
        val bills = repository.findByWalletId(walletId)

        // then
        bills.size shouldBe 1
        bills.first().automaticPixBill shouldBe automaticPixBill
        bills.first().walletId shouldBe walletId
    }

    @Test
    fun `deve retornar null quando AutomaticPixBill não for encontrado por EndToEnd`() {
        // given
        val walletId = WalletId("wallet-123")
        val nonExistentEndToEnd = "non-existent-end-to-end"

        // when
        val bill = repository.findByEndToEnd(nonExistentEndToEnd, walletId)

        // then
        bill shouldBe null
    }
}