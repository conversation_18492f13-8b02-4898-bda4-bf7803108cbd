package ai.friday.billpayment.modules.automaticpix.app.services

import DynamoDBUtils
import ai.friday.billpayment.adapters.arbi.NewArbiAuthenticationManager
import ai.friday.billpayment.adapters.parsers.parseListFrom
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.integration.BILL_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.automaticpix.adapters.arbi.ArbiAutomaticPixAdapter
import ai.friday.billpayment.modules.automaticpix.adapters.arbi.ArbiAutomaticPixConfiguration
import ai.friday.billpayment.modules.automaticpix.adapters.arbi.BillingResponseTO
import ai.friday.billpayment.modules.automaticpix.adapters.dynamodb.AutomaticPixBillingDynamoDAO
import ai.friday.billpayment.modules.automaticpix.adapters.dynamodb.AutomaticPixBillingDynamoDBRepository
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpResponse
import io.micronaut.http.MutableHttpRequest
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.mockk.every
import io.mockk.mockk
import io.reactivex.Flowable
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class AutomaticPixBillingServiceIntegrationTest {
    private val dynamoDbEnhancedClient = DynamoDBUtils.setupDynamoDB()
    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()
    private val walletId = wallet.id

    private val httpClient: RxHttpClient = mockk()

    private val configuration: ArbiAutomaticPixConfiguration = mockk {
        every { clientId } returns "test-client-id"
        every { automaticPixBillingPath } returns "automaticPixBillingPath"
    }

    private val authenticationManager: NewArbiAuthenticationManager = mockk {
        every { getToken() } returns "test-token"
    }

    private val arbiAutomaticPixAdapter: ArbiAutomaticPixAdapter = ArbiAutomaticPixAdapter(
        httpClient = httpClient,
        configuration = configuration,
        authenticationManager = authenticationManager,
    )

    private val walletService: WalletService = mockk()
    private val accountService: AccountService = mockk()
    private val automaticPixMessagePublisher: AutomaticPixMessagePublisher = mockk()

    private val repository =
        AutomaticPixBillingDynamoDBRepository(dynamoDAO = AutomaticPixBillingDynamoDAO(dynamoDbEnhancedClient))

    private val service = AutomaticPixBillingService(
        automaticPixRemoteManager = arbiAutomaticPixAdapter,
        walletService = walletService,
        accountService = accountService,
        automaticPixMessagePublisher = automaticPixMessagePublisher,
        automaticPixBillRepository = repository,
        createBillService = mockk {
            every {
                createPix(any(), any())
            } returns CreateBillResult.SUCCESS(
                bill = mockk {
                    every { billId } returns BillId(BILL_ID)
                },
                warningCode = null,
                possibleDuplicateBills = emptyList(),
            )
        },
        startTransaction = mockk(),
        completeTransaction = mockk(),
        transactionService = mockk(),
        failTransaction = mockk(),
    )

    @BeforeEach
    fun init() {
        every {
            walletService.findWallet(walletId)
        } returns wallet

        every {
            accountService.findAccountPaymentMethodByIdAndAccountId(wallet.paymentMethodId, wallet.founder.accountId)
        } returns mockk() {
            every { method } returns mockk<InternalBankAccount> {
                every {
                    buildFullAccountNumber()
                } returns "**************"
            }
        }
    }

    @Test
    fun `deve atualizar as bills no DynamoDB com sucesso`() {
        every {
            httpClient.exchange(
                any<MutableHttpRequest<*>>(),
                Argument.listOf(BillingResponseTO::class.java),
                Argument.STRING,
            )
        } returns Flowable.just(HttpResponse.ok(parseListFrom<BillingResponseTO>(allBillsArbiResponse)))

        val response = service.refreshBilling(walletId).getOrThrow()

        response.size shouldBe 1

        // response.map { it.toResponseTO() }.size shouldBe 4

        repository.findByWalletId(walletId).size shouldBe 1
    }
}

private val allBillsArbiResponse = """
    [
        {
            "receiver": {
                "documentNumber": "**************",
                "name": "Pix Tester 918",
                "documentType": "CNPJ"
            },
            "debtor": {
                "documentNumber": "54403563000150",
                "name": "BCO ARBI S.A.",
                "documentType": "CNPJ"
            },
            "contractNumber": "8630659",
            "endToEnd": "E9999991820250615150062604544232",
            "amount": 954.83,
            "settlementDate": "2025-06-15T00:00:00",
            "description": null,
            "additionalInformation": "Serviço de Telefone",
            "recurringPayment": {
                "id": "RR999999182025061367529091657"
            }
        }
    ]
""".trimIndent()