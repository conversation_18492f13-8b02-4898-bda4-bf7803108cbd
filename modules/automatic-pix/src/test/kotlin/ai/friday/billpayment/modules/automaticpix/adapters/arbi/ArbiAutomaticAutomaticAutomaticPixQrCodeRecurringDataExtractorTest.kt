package ai.friday.billpayment.modules.automaticpix.adapters.arbi

import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixJourney
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRetryType
import ai.friday.billpayment.modules.automaticpix.createQRCodeDetailsResponse
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class ArbiAutomaticAutomaticAutomaticPixQrCodeRecurringDataExtractorTest {
    private val extractor: ArbiAutomaticAutomaticPixQrCodeRecurringDataExtractor = ArbiAutomaticAutomaticPixQrCodeRecurringDataExtractor()

    @Test
    fun `deve extrair dados do QR Code com sucesso`() {
        // given
        val qrCodeResponse = createQRCodeDetailsResponse()

        // when
        val extractedData = extractor.extractQRCodeRecurringData(qrCodeResponse.dadosRecorrencia!!).getOrThrow()

        extractedData.shouldNotBeNull()
        extractedData.recurringPaymentId.value shouldBe qrCodeResponse.dadosRecorrencia?.idRec
        extractedData.journey shouldBe AutomaticPixJourney.JOURNEY_1
        extractedData.retryType shouldBe AutomaticPixRetryType.ALLOWED_3R_7D
        extractedData.fixedAmount shouldBe 1500L
        extractedData.minimumReceiverAmount shouldBe 1000L
    }

    @Test
    fun `deve falhar ao extrair dados com tipo inválido`() {
        // given
        val invalidData = "dados inválidos"

        // when
        val result = extractor.extractQRCodeRecurringData(invalidData)

        // then
        result.isFailure shouldBe true
        result.exceptionOrNull()?.message shouldBe "Invalid data type: java.lang.String. Expected QRCodeDetailsResponseTO."
    }
}