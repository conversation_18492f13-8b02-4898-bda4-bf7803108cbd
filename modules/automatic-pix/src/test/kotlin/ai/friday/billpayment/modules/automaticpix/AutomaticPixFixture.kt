package ai.friday.billpayment.modules.automaticpix

import ai.friday.billpayment.adapters.arbi.ArbiDadosRecorrenciaTO
import ai.friday.billpayment.adapters.arbi.AtualizacaoRecorrenciaTO
import ai.friday.billpayment.adapters.arbi.QRCodeDetailsFullResponseTO
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixBill
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixDocumentType
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixJourney
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateId
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateStatus
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixNotPendingMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPendingMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPeriodicity
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPersonData
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixProvider
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPspInformation
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixQrCodeRecurringData
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPayment
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentBasicData
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentId
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentStatus
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRetryType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import java.time.LocalDate
import java.time.ZonedDateTime

fun createBaseAuthorization(
    walletId: WalletId = WalletId(WALLET_ID),
    mandateId: String = "ext-123",
    recurringPaymentId: String = "rec-123",
): AutomaticPixMandate {
    val recurringPayment = AutomaticPixRecurringPaymentBasicData(
        recurringPaymentId = AutomaticPixRecurringPaymentId(recurringPaymentId),
        mandateId = AutomaticPixMandateId(mandateId),
        payee = AutomaticPixPersonData(
            documentNumber = "12345678901234",
            name = "Empresa Teste",
            documentType = AutomaticPixDocumentType.CNPJ,
            pspInformation = AutomaticPixPspInformation("123", "Banco Teste"),
        ),
        provider = AutomaticPixProvider.ARBI,
        status = AutomaticPixRecurringPaymentStatus.CREATED,
    )

    return AutomaticPixNotPendingMandate(
        walletId = walletId,
        mandateId = AutomaticPixMandateId(mandateId),
        status = AutomaticPixMandateStatus.ACTIVE,
        recurringPayment = recurringPayment,
        startDateValidity = null,
        endDateValidity = null,
        expiredValidityDate = null,
        canceledAt = null,
        canceledBy = null,
        provider = AutomaticPixProvider.ARBI,
        expiresAt = null,
    )
}

fun createAuthorization(
    walletId: WalletId = WalletId(WALLET_ID),
    mandateId: String = "ext-123",
    recurringPaymentId: String = "rec-123",
    status: AutomaticPixMandateStatus = AutomaticPixMandateStatus.PENDING,
): AutomaticPixMandate {
    val recurringPayment = AutomaticPixRecurringPayment(
        recurringPaymentId = AutomaticPixRecurringPaymentId(recurringPaymentId),
        mandateId = AutomaticPixMandateId(mandateId),
        startDate = LocalDate.now(),
        endDate = null,
        fixedAmount = 1000_00,
        minimumReceiverAmount = null,
        maximumPayerAmount = null,
        payee = AutomaticPixPersonData(
            documentNumber = "12345678901234",
            name = "Empresa Teste",
            documentType = AutomaticPixDocumentType.CNPJ,
            pspInformation = AutomaticPixPspInformation("123", "Banco Teste"),
        ),
        payer = AutomaticPixPersonData(
            documentNumber = "12345678901",
            name = "Pessoa Teste",
            documentType = AutomaticPixDocumentType.CPF,
            pspInformation = null,
        ),
        contractNumber = "123456",
        periodicity = AutomaticPixPeriodicity.MONTHLY,
        description = "Teste",
        provider = AutomaticPixProvider.ARBI,
        notificationsScheduling = null,
        creditLimitUsage = null,
        status = AutomaticPixRecurringPaymentStatus.CREATED,
    )

    return if (status == AutomaticPixMandateStatus.PENDING) {
        AutomaticPixPendingMandate(
            walletId = walletId,
            mandateId = AutomaticPixMandateId(mandateId),
            status = status,
            expiresAt = ZonedDateTime.now().plusDays(1),
            recurringPayment = recurringPayment,
            provider = AutomaticPixProvider.ARBI,
        )
    } else {
        AutomaticPixNotPendingMandate(
            walletId = walletId,
            mandateId = AutomaticPixMandateId(mandateId),
            status = status,
            recurringPayment = recurringPayment,
            startDateValidity = null,
            endDateValidity = null,
            expiredValidityDate = null,
            canceledAt = null,
            canceledBy = null,
            provider = AutomaticPixProvider.ARBI,
            expiresAt = null,
        )
    }
}

fun createQRCodeDetailsResponse() = QRCodeDetailsFullResponseTO(
    dadosRecorrencia = ArbiDadosRecorrenciaTO(
        idRec = "rec-123",
        jornada = "JORNADA_1",
        retentativa = "PERMITE_3R_7D",
        valorRec = 15.00,
        valorMinimo = 10.00,
        cnpjRecebedor = "12345678901234",
        nomeRecebedor = "Beneficiário Teste",
        ispbRecebedor = "341",
        cpfPagador = "12345678900",
        nomePagador = "Pagador Teste",
        calendarioInicial = LocalDate.now().toString(),
        calendarioFinal = LocalDate.now().plusMonths(12).toString(),
        periodicidade = "MENSAL",
        contratoVinculo = "Teste de descrição",
        objetoVinculo = "123456",
        atualizacao = listOf(AtualizacaoRecorrenciaTO(data = "suscipiantur", status = "autem")),
        cnpjPagador = null,
    ),
    nome = "",
    cpfCnpj = "",
    nomePsp = "",
    codInstituicao = "",
    codAgencia = "",
    nroConta = "",
    tipoConta = "",
    referencia = null,
    info = "",
    tipoQRCode = "",
    endToEnd = "",
    tipoChave = "",
    chaveEnderecamento = "",
    identificador = null,
    tipoPix = "",
    valor = null,
    valorFinal = null,
    infoAdicional = listOf(),
    calendarioCriacao = null,
    recebedorNomeFantasia = null,
    recebedorNome = null,
    calendarioExpiracaoSegundos = null,
    dataCriacao = null,
    dataPosse = null,
    dataAbertura = null,
    tipoPessoa = null,
    pagadorCnpj = null,
    pagadorNome = null,
    recebedorCnpj = null,
    recebedorCidade = null,
    estatisticas = mapOf(),
)

fun createAutomaticPixQrCodeRecurringData(
    recurringPaymentId: String = "rec-123",
    journey: AutomaticPixJourney = AutomaticPixJourney.JOURNEY_1,
) = AutomaticPixQrCodeRecurringData(
    recurringPaymentId = AutomaticPixRecurringPaymentId(recurringPaymentId),
    journey = journey,
    retryType = AutomaticPixRetryType.ALLOWED_3R_7D,
    fixedAmount = 1000L,
    minimumReceiverAmount = 1000L,
    payee = AutomaticPixPersonData(
        documentNumber = "12345678901234",
        name = "Beneficiário Teste",
        documentType = AutomaticPixDocumentType.CNPJ,
        pspInformation = AutomaticPixPspInformation(
            code = "341",
            name = null,
        ),
    ),
    payer = AutomaticPixPersonData(
        documentNumber = "12345678900",
        name = "Pagador Teste",
        documentType = AutomaticPixDocumentType.CPF,
        pspInformation = null,
    ),
    startDate = LocalDate.now(),
    endDate = LocalDate.now().plusMonths(12),
    periodicity = AutomaticPixPeriodicity.MONTHLY,
    description = "Teste de descrição",
    contractNumber = "123456",
    provider = AutomaticPixProvider.ARBI,
    updates = listOf("{\"campo\":\"valor\"}"),
)

fun createAutomaticPixRecurringPayment(
    recurringPaymentId: String = "rec-123",
    mandateId: String = "mand-123",
    status: AutomaticPixRecurringPaymentStatus = AutomaticPixRecurringPaymentStatus.CREATED,
): AutomaticPixRecurringPayment {
    return AutomaticPixRecurringPayment(
        recurringPaymentId = AutomaticPixRecurringPaymentId(recurringPaymentId),
        mandateId = AutomaticPixMandateId(mandateId),
        startDate = LocalDate.now(),
        endDate = LocalDate.now().plusMonths(12),
        fixedAmount = 1000L,
        minimumReceiverAmount = 1000L,
        maximumPayerAmount = 2000L,
        payee = AutomaticPixPersonData(
            documentNumber = "12345678901234",
            name = "Beneficiário Teste",
            documentType = AutomaticPixDocumentType.CNPJ,
            pspInformation = AutomaticPixPspInformation(
                code = "341",
                name = "Itaú",
            ),
        ),
        payer = AutomaticPixPersonData(
            documentNumber = "98765432100",
            name = "Pagador Teste",
            documentType = AutomaticPixDocumentType.CPF,
            pspInformation = null,
        ),
        contractNumber = "123456",
        periodicity = AutomaticPixPeriodicity.MONTHLY,
        description = "Teste de descrição",
        provider = AutomaticPixProvider.ARBI,
        notificationsScheduling = true,
        creditLimitUsage = false,
        status = status,
    )
}

fun createAutomaticPixBill(endToEnd: String = "end-to-end-123", settlementDate: LocalDate = getLocalDate()) = AutomaticPixBill(
    recurringPaymentId = AutomaticPixRecurringPaymentId("rec-123"),
    endToEnd = endToEnd,
    payee = AutomaticPixPersonData(
        documentNumber = "12345678900",
        name = "Payee Name",
        documentType = AutomaticPixDocumentType.CPF,
        pspInformation = AutomaticPixPspInformation("001", "Banco do Brasil"),
    ),
    payer = AutomaticPixPersonData(
        documentNumber = "98765432100",
        name = "Payer Name",
        documentType = AutomaticPixDocumentType.CPF,
        pspInformation = AutomaticPixPspInformation("001", "Banco do Brasil"),
    ),
    contractNumber = "contract-123",
    amount = 1000_00L,
    settlementDate = settlementDate,
    description = "Test Description",
    additionalInformation = "Test Additional Information",
)