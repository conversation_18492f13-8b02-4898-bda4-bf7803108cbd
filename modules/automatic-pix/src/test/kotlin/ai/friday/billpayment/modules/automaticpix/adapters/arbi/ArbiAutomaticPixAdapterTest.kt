package ai.friday.billpayment.modules.automaticpix.adapters.arbi

import ai.friday.billpayment.adapters.arbi.NewArbiAuthenticationManager
import ai.friday.billpayment.adapters.parsers.parseListFrom
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixDocumentType
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateStatus
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixNotPendingMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixProvider
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentId
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpResponse
import io.micronaut.http.MutableHttpRequest
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.mockk.every
import io.mockk.mockk
import io.reactivex.Flowable
import java.time.format.DateTimeFormatter
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ArbiAutomaticPixAdapterTest {

    private lateinit var adapter: ArbiAutomaticPixAdapter
    private lateinit var httpClient: RxHttpClient
    private lateinit var configuration: ArbiAutomaticPixConfiguration
    private lateinit var authenticationManager: NewArbiAuthenticationManager

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()
    private val walletId = wallet.id

    @BeforeEach
    fun setup() {
        httpClient = mockk()
        configuration = mockk()
        authenticationManager = mockk()

        // Configuração básica do configuration
        every { configuration.clientId } returns "test-client-id"
        every { configuration.automaticPixPendingMandatePath } returns "/api/v1/pending-authorizations"
        every { configuration.automaticPixMandatePath } returns "/api/v1/authorizations"

        // Configuração do authenticationManager
        every { authenticationManager.getToken() } returns "test-token"

        adapter = ArbiAutomaticPixAdapter(
            httpClient = httpClient,
            configuration = configuration,
            authenticationManager = authenticationManager,
        )
    }

    @Test
    fun `AuthorizationPendingResponseTO deve fazer o parse corretamente`() {
        val accountNumber = "123456"
        val rawResponse = """
        [{
        "id": "SC99999918202506032GGepkSYU1f",
        "expirationDate": "2025-06-05T15:24:03.926-03:00",
        "recurringPayment": {
            "id": "*****************************"
        },
        "billing": {
            "startDate": "2025-06-03T00:00:00",
            "endDate": "0001-01-01T00:00:00",
            "amount": 827.76,
            "minimumReceiverAmount": 0.0,
            "maximumPayerAmount": 0.0,
            "receiver": {
                "documentNumber": "**************",
                "name": "Pix Tester 918",
                "documentType": "CNPJ"
            },
            "debtor": {
                "documentNumber": "**************",
                "name": "BCO ARBI S.A.",
                "documentType": "CNPJ"
            },
            "contractNumber": "********",
            "periodicity": "MONTHLY"
        }
    }
]
        """.trimIndent()
        val responseTO = parseListFrom<PendingMandateResponseTO>(rawResponse)

        every {
            httpClient.exchange(
                any<MutableHttpRequest<*>>(),
                Argument.listOf(PendingMandateResponseTO::class.java),
                Argument.STRING,
            )
        } returns Flowable.just(HttpResponse.ok(responseTO))

        val result = adapter.getPendingMandates(walletId, accountNumber).getOrThrow()

        result.size shouldBe 1

        with(result.single()) {
            this.status shouldBe AutomaticPixMandateStatus.PENDING
            this.expiresAt?.format(DateTimeFormatter.ISO_DATE_TIME) shouldBe "2025-06-05T15:24:03.926-03:00"
            this.provider shouldBe AutomaticPixProvider.ARBI

            this.recurringPayment.payee.documentNumber shouldBe "**************"
            this.recurringPayment.payee.name shouldBe "Pix Tester 918"
            this.recurringPayment.payee.documentType shouldBe AutomaticPixDocumentType.CNPJ
            this.recurringPayment.payee.pspInformation.shouldBeNull()
            this.recurringPayment.startDate?.format(DateTimeFormatter.ISO_DATE) shouldBe "2025-06-03"
            this.recurringPayment.endDate?.format(DateTimeFormatter.ISO_DATE) shouldBe "0001-01-01"
            this.recurringPayment.fixedAmount shouldBe 827_76
        }
    }

    @Test
    fun `AuthorizationResponseTO deve fazer o parse corretamente`() {
        val accountNumber = "123456"
        val rawResponse = """[
    {
        "id": "SC99999918202506032GGepkSYU1f",
        "expirationDate": "2025-06-05T00:00:00",
        "status": "PENDING",
        "date": "0001-01-01T00:00:00",
        "startDateValidity": "2025-06-03T00:00:00",
        "endDateValidity": "0001-01-01T00:00:00",
        "expiredValidityDate": "0001-01-01T00:00:00",
        "cancellationDate": null,
        "userCancellation": null,
        "recurringPayment": {
            "id": "*****************************"
        },
        "billing": {
            "receiver": {
                "documentNumber": "**************",
                "name": "Pix Tester 918",
                "documentType": "CNPJ"
            }
        }
    }
]
        """.trimIndent()
        val responseTO = parseListFrom<MandateResponseTO>(rawResponse)

        every {
            httpClient.exchange(
                any<MutableHttpRequest<*>>(),
                Argument.listOf(MandateResponseTO::class.java),
                Argument.STRING,
            )
        } returns Flowable.just(HttpResponse.ok(responseTO))

        val result = adapter.getMandates(walletId, accountNumber, AutomaticPixRecurringPaymentId("ext1")).getOrThrow()

        result.size shouldBe 1

        with(result.single()) {
            this.status shouldBe AutomaticPixMandateStatus.PENDING
            this.shouldBeTypeOf<AutomaticPixNotPendingMandate>()

            this.provider shouldBe AutomaticPixProvider.ARBI
            this.recurringPayment.payee.documentNumber shouldBe "**************"
            this.recurringPayment.payee.name shouldBe "Pix Tester 918"
            this.recurringPayment.payee.documentType shouldBe AutomaticPixDocumentType.CNPJ
            this.recurringPayment.payee.pspInformation.shouldBeNull()
        }
    }
}