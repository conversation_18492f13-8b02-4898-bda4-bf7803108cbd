package ai.friday.billpayment.modules.automaticpix.adapters.api

import ai.friday.billpayment.modules.automaticpix.app.services.AutomaticPixMessagePublisher
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.http.HttpStatus
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class ArbiAutomaticPixCallbackControllerTest {

    private val automaticPixMessagePublisher = mockk<AutomaticPixMessagePublisher>(relaxUnitFun = true)
    private val callbackController = ArbiAutomaticPixCallbackController(automaticPixMessagePublisher)

    @Test
    fun `deve funcionar com o payload recebido em STG`() {
        val response = callbackController.handleNotification(c1RecebidaEmSTG)
        response.status shouldBe HttpStatus.OK
    }

    @ParameterizedTest
    @MethodSource("allDocumentationPayloads")
    fun `deve funcionar com todos os exemplos da documentacao`(payload: String, notificationType: NotificationType) {
        val response = callbackController.handleNotification(payload)
        response.status shouldBe HttpStatus.OK

        val callbackSlot = slot<ParsedCallbackData>()
        verify {
            automaticPixMessagePublisher.publishCallbackMessage(capture(callbackSlot))
        }
        val callbackData = callbackSlot.captured

        when (notificationType) {
            NotificationType.SOLREC_RECEBIDA,
            NotificationType.SOLREC_EXPIRADA,
            NotificationType.SOLREC_APROVADA_SEMRESPOSTA,
            NotificationType.SOLREC_CANCELADA,
            NotificationType.REC_CANCELADA,
            NotificationType.REC_CONFIRMADA,
            -> {
                callbackData.shouldBeTypeOf<ParsedCallbackData.MandateAndRecurrenceLifecycleCallback>()
            }

            NotificationType.COBR_CANCELADA,
            NotificationType.COBR_REJEITADA,
            NotificationType.COBR_RECEBIDA,
            -> {
                callbackData.shouldBeTypeOf<ParsedCallbackData.BillingLifecycleCallback>()
            }

            NotificationType.RECEBIDO,
            NotificationType.LIQUIDADO,
            NotificationType.CANCELADO,
            NotificationType.FALTA_LIMITE,

            NotificationType.SLD_INSUFICIENTE_NGRADE,
            NotificationType.SLD_INSUFICIENTE_ULTGRADE,

            NotificationType.FALHA_CHAVE_DICT,
            NotificationType.FALHA_OPERACIONAL,
            NotificationType.EXPIRADO,

            NotificationType.FALTA_LIMITE_ULTGRADE,
            NotificationType.FALTA_LIMITE_NGRADE,
            -> {
                callbackData.shouldBeTypeOf<ParsedCallbackData.PaymentLifecycleCallback>()
                callbackData.payer?.name?.shouldNotBeNull()
                callbackData.payee?.name?.shouldNotBeNull()
                callbackData.value?.shouldNotBeNull()
                callbackData.schedulingDate?.shouldNotBeNull()
                callbackData.settlementDate?.shouldNotBeNull()
                callbackData.internalReference?.shouldNotBeNull()
                callbackData.paymentStatus?.shouldNotBeNull()
            }
        }
    }

    companion object {
        @JvmStatic
        fun allDocumentationPayloads(): List<Arguments> = listOf(
            Arguments.of(paylodDoc1_1, NotificationType.valueOf("SOLREC_RECEBIDA")),
            Arguments.of(paylodDoc1_2, NotificationType.valueOf("REC_CONFIRMADA")),
            Arguments.of(paylodDoc1_3, NotificationType.valueOf("COBR_RECEBIDA")),
            Arguments.of(paylodDoc2_1, NotificationType.valueOf("RECEBIDO")),
            Arguments.of(paylodDoc2_2, NotificationType.valueOf("SLD_INSUFICIENTE_NGRADE")),
            Arguments.of(paylodDoc2_3, NotificationType.valueOf("FALHA_CHAVE_DICT")),
            Arguments.of(paylodDoc3_1, NotificationType.valueOf("SLD_INSUFICIENTE_NGRADE")),
            Arguments.of(paylodDoc3_2, NotificationType.valueOf("FALTA_LIMITE_NGRADE")),
            Arguments.of(paylodDoc3_3, NotificationType.valueOf("FALHA_OPERACIONAL")),
        )
    }
}

val c1RecebidaEmSTG = """
    {
        "notificationType": "SOLREC_RECEBIDA",
        "message": "A solicitação de autorização para pagamento da fatura de  à Pix Tester 918, em nome de BCO ARBI S.A., já está disponível, até 18/06/2025, no Pix Automático.",
        "recurrence": {
            "authorizationId": "SC9999991820250616VyxF3hYI7th",
            "recurrenceId": "*****************************",
            "payer": {
                "ispbCode": "********",
                "taxId": "**************",
                "accountNumber": **********,
                "agencyCode": 1
            },
            "receiver": {
                "name": "Pix Tester 918"
            },
            "debtor": {
                "name": "BCO ARBI S.A."
            },
            "expirationDate": "2025-06-18T15:22:46.297-03:00",
            "value": 656.14,
            "maximumPayerValue": 0
        },
        "billing": {
            "value": 0
        }
    }
""".trimIndent()

val paylodDoc1_1 = """
    {
      "sendAt": "2025-05-23T17:33:25.2596608-03:00",
      "notificationType": "SOLREC_RECEBIDA",
      "complement": "Some complement",
      "message": "Pix Automático agendado para João, no valor de R${'$'} 100,00, a ser pago no dia 01/05/2025.",
      "recurrence": {
        "authorizationId": "*****************************",
        "recurrenceId": "RN********2024011577825445612",
        "payer": {
          "ispbCode": "********",
          "taxId": "***********",
          "accountNumber": **********,
          "agencyCode": 1234
        },
        "receiver": {
          "name": "Receiver Name"
        },
        "debtor": {
          "name": "Debtor Name"
        },
        "expirationDate": "2025-05-25T17:33:24.473-03:00",
        "description": "Informação ao pagador",
        "value": 1000.50,
        "maximumPayerValue": 1500.75
      },
      "billing": {
        "endToEnd": "E123456asdFGH123ASD123f10895aAa11",
        "value": 500.25,
        "dueDate": "2025-04-30"
      }
    }
""".trimIndent()

val paylodDoc1_2 = """
    {
      "sendAt": "2025-05-23T17:33:25.2596608-03:00",
      "notificationType": "REC_CONFIRMADA",
      "complement": "Some complement",
      "message": "Pix Automático agendado para João, no valor de R${'$'} 100,00, a ser pago no dia 01/05/2025.",
      "recurrence": {
        "authorizationId": "*****************************",
        "recurrenceId": "RN********2024011577825445612",
        "payer": {
          "ispbCode": "********",
          "taxId": "***********",
          "accountNumber": **********,
          "agencyCode": 1234
        },
        "receiver": {
          "name": "Receiver Name"
        },
        "debtor": {
          "name": "Debtor Name"
        },
        "expirationDate": "2025-05-25T17:33:24.473-03:00",
        "description": "Informação ao pagador",
        "value": 1000.50,
        "maximumPayerValue": 1500.75
      },
      "billing": {
        "endToEnd": "E123456asdFGH123ASD123f10895aAa11",
        "value": 500.25,
        "dueDate": "2025-04-30"
      }
    }
""".trimIndent()

val paylodDoc1_3 = """
    {
      "sendAt": "2025-05-23T17:33:25.2596608-03:00",
      "notificationType": "COBR_RECEBIDA",
      "complement": "Some complement",
      "message": "Pix Automático agendado para João, no valor de R${'$'} 100,00, a ser pago no dia 01/05/2025.",
      "recurrence": {
        "authorizationId": "*****************************",
        "recurrenceId": "RN********2024011577825445612",
        "payer": {
          "ispbCode": "********",
          "taxId": "***********",
          "accountNumber": **********,
          "agencyCode": 1234
        },
        "receiver": {
          "name": "Receiver Name"
        },
        "debtor": {
          "name": "Debtor Name"
        },
        "expirationDate": "2025-05-25T17:33:24.473-03:00",
        "description": "Informação ao pagador",
        "value": 1000.50,
        "maximumPayerValue": 1500.75
      },
      "billing": {
        "endToEnd": "E123456asdFGH123ASD123f10895aAa11",
        "value": 500.25,
        "dueDate": "2025-04-30"
      }
    }
""".trimIndent()

val paylodDoc2_1 = """
    {
      "notificationDateTime": "2025-04-11T14:37:52.123456-03:00",
      "notificationType": "RECEBIDO",
      "message": "Pix para João no valor de R${'$'} 100,00 agendado com sucesso para 01/01/2025.",
      "payer": {
        "ispbCode": "********",
        "taxId": "**********1",
        "accountType": "CACC",
        "accountNumber": **********,
        "agencyCode": 1234,
        "name": "Payer Name"
      },
      "receiver": {
        "ispbCode": "********",
        "taxId": "109********",
        "accountType": "CACC",
        "accountNumber": 9********0,
        "agencyCode": 4321,
        "name": "Receiver Name"
      },
      "endToEnd": "E123456asdFGH123ASD123f10895aAa11",
      "value": 2500.75,
      "schedulingDate": "2025-04-01",
      "settlementDate": "2025-04-02",
      "inputChannel": "MOBILE_APP",
      "internalReference": "7978c0c97ea847e78e8849634473c1f1",
      "dictKey": "***********",
      "freeField": "Some free field",
      "status": "APROVADO"
    }
""".trimIndent()

val paylodDoc2_2 = """
    {
      "notificationDateTime": "2025-04-11T14:37:52.123456-03:00",
      "notificationType": "SLD_INSUFICIENTE_NGRADE",
      "message": "Pix para João no valor de R${'$'} 100,00 agendado com sucesso para 01/01/2025.",
      "payer": {
        "ispbCode": "********",
        "taxId": "**********1",
        "accountType": "CONTA_CORRENTE",
        "accountNumber": **********,
        "agencyCode": 1234,
        "name": "Payer Name"
      },
      "receiver": {
        "ispbCode": "********",
        "taxId": "109********",
        "accountType": "CONTA_POUPANCA",
        "accountNumber": 9********0,
        "agencyCode": 4321,
        "name": "Receiver Name"
      },
      "endToEnd": "E123456asdFGH123ASD123f10895aAa11",
      "value": 2500.75,
      "schedulingDate": "2025-04-01",
      "settlementDate": "2025-04-02",
      "inputChannel": "MOBILE_APP",
      "internalReference": "7978c0c97ea847e78e8849634473c1f1",
      "dictKey": "***********",
      "freeField": "Some free field",
      "status": "APROVADO"
    }
""".trimIndent()

val paylodDoc2_3 = """
    {
      "notificationDateTime": "2025-04-11T14:37:52.123456-03:00",
      "notificationType": "FALHA_CHAVE_DICT",
      "message": "Pix para João no valor de R${'$'} 100,00 agendado com sucesso para 01/01/2025.",
      "payer": {
        "ispbCode": "********",
        "taxId": "**********1",
        "accountType": "CONTA_CORRENTE",
        "accountNumber": **********,
        "agencyCode": 1234,
        "name": "Payer Name"
      },
      "receiver": {
        "ispbCode": "********",
        "taxId": "109********",
        "accountType": "CONTA_POUPANCA",
        "accountNumber": 9********0,
        "agencyCode": 4321,
        "name": "Receiver Name"
      },
      "endToEnd": "E123456asdFGH123ASD123f10895aAa11",
      "value": 2500.75,
      "schedulingDate": "2025-04-01",
      "settlementDate": "2025-04-02",
      "inputChannel": "MOBILE_APP",
      "internalReference": "7978c0c97ea847e78e8849634473c1f1",
      "dictKey": "***********",
      "freeField": "Some free field",
      "status": "APROVADO"
    }
""".trimIndent()

val paylodDoc3_1 = """
    {
      "dataHourNotification": "2025-04-11T14:37:52.123456-03:00",
      "notificationType": "SLD_INSUFICIENTE_NGRADE",
      "complement": "Some complement",
      "message": "Seu limite diário para o Pix Automático foi excedido, logo o pagamento à João, no valor de R${'$'} 100,00, não foi realizado. Novas tentativas poderão ser realizadas nos próximos dias.",
      "recurrence": {
        "payer": {
          "ispbCode": "********",
          "taxId": "**********1",
          "accountNumber": **********,
          "agencyCode": 1234
        },
        "receiver": {
          "name": "Receiver Name"
        }
      },
      "charge": {
        "endToEnd": "E2E********9",
        "value": 1500.75
      }
    }
""".trimIndent()

val paylodDoc3_2 = """
    {
      "dataHourNotification": "2025-04-11T14:37:52.123456-03:00",
      "notificationType": "FALTA_LIMITE_NGRADE",
      "complement": "Some complement",
      "message": "Seu limite diário para o Pix Automático foi excedido, logo o pagamento à João, no valor de R${'$'} 100,00, não foi realizado. Novas tentativas poderão ser realizadas nos próximos dias.",
      "recurrence": {
        "payer": {
          "ispbCode": "********",
          "taxId": "**********1",
          "accountNumber": **********,
          "agencyCode": 1234
        },
        "receiver": {
          "name": "Receiver Name"
        }
      },
      "charge": {
        "endToEnd": "E2E********9",
        "value": 1500.75
      }
    }
""".trimIndent()

val paylodDoc3_3 = """
    {
      "dataHourNotification": "2025-04-11T14:37:52.123456-03:00",
      "notificationType": "FALHA_OPERACIONAL",
      "complement": "Some complement",
      "message": "Seu limite diário para o Pix Automático foi excedido, logo o pagamento à João, no valor de R${'$'} 100,00, não foi realizado. Novas tentativas poderão ser realizadas nos próximos dias.",
      "recurrence": {
        "payer": {
          "ispbCode": "********",
          "taxId": "**********1",
          "accountNumber": **********,
          "agencyCode": 1234
        },
        "receiver": {
          "name": "Receiver Name"
        }
      },
      "charge": {
        "endToEnd": "E2E********9",
        "value": 1500.75
      }
    }
""".trimIndent()