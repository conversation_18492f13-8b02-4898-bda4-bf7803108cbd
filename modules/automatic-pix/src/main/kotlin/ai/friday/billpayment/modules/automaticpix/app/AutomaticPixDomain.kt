package ai.friday.billpayment.modules.automaticpix.app

import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId
import java.time.LocalDate
import java.time.ZonedDateTime

interface AutomaticPixRemoteManager {
    fun getPendingMandates(walletId: WalletId, accountNumber: String, automaticPixRecurringPaymentId: AutomaticPixRecurringPaymentId? = null): Result<List<AutomaticPixPendingMandate>>
    fun getMandates(walletId: WalletId, accountNumber: String, automaticPixRecurringPaymentId: AutomaticPixRecurringPaymentId?): Result<List<AutomaticPixNotPendingMandate>>
    fun rejectMandate(automaticPixMandateId: AutomaticPixMandateId, automaticPixRecurringPaymentId: AutomaticPixRecurringPaymentId, clientRejectionReason: ClientRejectionReason): Result<AutomaticPixMandateStatus>
    fun approveMandate(automaticPixMandateId: AutomaticPixMandateId, automaticPixRecurringPaymentId: AutomaticPixRecurringPaymentId, payerName: String, maximumAmount: Long?): Result<AutomaticPixMandateStatus>

    fun getApprovedRecurrences(accountNumber: String, recurringPaymentId: AutomaticPixRecurringPaymentId? = null): Result<List<AutomaticPixRecurringPayment>>
    fun getRecurrenceStatus(recurringPaymentId: AutomaticPixRecurringPaymentId): Result<AutomaticPixRecurringPaymentStatus>
    fun approveRecurrence(automaticPixQrCodeRecurringData: AutomaticPixQrCodeRecurringData, payer: Member, accountNumber: String, maximumAmount: Long?): Result<Unit>
    fun cancelRecurrence(recurringPaymentId: AutomaticPixRecurringPaymentId, automaticPixRecurrenceCancellationReason: AutomaticPixRecurrenceCancellationReason): Result<Unit>
    fun updateRecurrence(recurringPaymentId: AutomaticPixRecurringPaymentId, command: RecurrenceUpdateableValues): Result<RecurrenceUpdateableValues>
    fun listBilling(accountNumber: String, recurringPaymentId: AutomaticPixRecurringPaymentId?): Result<List<AutomaticPixBill>>
    fun cancelBilling(endToEnd: String): Result<Unit>
}

interface AutomaticPixMandateRepository {
    fun save(automaticPixMandate: AutomaticPixMandate): AutomaticPixMandate
    fun updateStatus(automaticPixMandate: AutomaticPixMandate, status: AutomaticPixMandateStatus): AutomaticPixMandate
    fun findByExternalId(recurrenceId: AutomaticPixRecurringPaymentId, status: AutomaticPixMandateStatus?): List<AutomaticPixMandate>
    fun findByWalletId(walletId: WalletId, status: AutomaticPixMandateStatus?): List<AutomaticPixMandate>
    fun findByMandateId(automaticPixMandateId: AutomaticPixMandateId): List<AutomaticPixMandate>
    fun findByMandateIdAndWalletId(automaticPixMandateId: AutomaticPixMandateId, walletId: WalletId): AutomaticPixMandate?
    fun findByStatusExpringBefore(status: AutomaticPixMandateStatus, expireDate: LocalDate): List<AutomaticPixMandate>
}

interface AutomaticPixRecurringPaymentRepository {
    fun save(automaticPixRecurringPaymentOnWallet: AutomaticPixRecurringPaymentOnWallet): AutomaticPixRecurringPaymentOnWallet
    fun findById(automaticPixRecurringPaymentId: AutomaticPixRecurringPaymentId): List<AutomaticPixRecurringPaymentOnWallet>
    fun findByIdAndWalletId(automaticPixRecurringPaymentId: AutomaticPixRecurringPaymentId, walletId: WalletId): AutomaticPixRecurringPaymentOnWallet?
    fun findByWalletId(walletId: WalletId): List<AutomaticPixRecurringPaymentOnWallet>
}

interface AutomaticPixBillRepository {
    fun save(automaticPixBill: AutomaticPixBill, walletId: WalletId, billId: BillId): AutomaticPixBillOnWallet
    fun remove(automaticPixBill: AutomaticPixBill, walletId: WalletId)
    fun findByEndToEnd(endToEnd: String): List<AutomaticPixBillOnWallet>
    fun findByEndToEnd(endToEnd: String, walletId: WalletId): AutomaticPixBillOnWallet?
    fun findByRecurringPaymentId(recurringPaymentId: AutomaticPixRecurringPaymentId, walletId: WalletId): List<AutomaticPixBillOnWallet>
    fun findByWalletId(walletId: WalletId): List<AutomaticPixBillOnWallet>
    fun findByBillId(billId: BillId, walletId: WalletId): AutomaticPixBillOnWallet?
}

data class AutomaticPixMandateId(val value: String)

data class AutomaticPixRecurringPaymentId(val value: String)

sealed interface AutomaticPixMandate {
    val walletId: WalletId
    val mandateId: AutomaticPixMandateId
    val status: AutomaticPixMandateStatus
    val expiresAt: ZonedDateTime?
    val recurringPayment: AutomaticPixRecurringPaymentBase
    val provider: AutomaticPixProvider
}

data class AutomaticPixPendingMandate(
    override val walletId: WalletId,
    override val mandateId: AutomaticPixMandateId,
    override val status: AutomaticPixMandateStatus = AutomaticPixMandateStatus.PENDING,
    override val expiresAt: ZonedDateTime?,
    override val recurringPayment: AutomaticPixRecurringPayment,
    override val provider: AutomaticPixProvider,
) : AutomaticPixMandate

data class AutomaticPixNotPendingMandate(
    override val walletId: WalletId,
    override val mandateId: AutomaticPixMandateId,
    override val status: AutomaticPixMandateStatus,
    override val expiresAt: ZonedDateTime?,
    override val recurringPayment: AutomaticPixRecurringPaymentBase,
    val startDateValidity: LocalDate?,
    val endDateValidity: LocalDate?,
    val expiredValidityDate: LocalDate?,
    val canceledAt: ZonedDateTime?,
    val canceledBy: CanceledBy?,
    override val provider: AutomaticPixProvider,
) : AutomaticPixMandate

sealed interface AutomaticPixRecurringPaymentBase {
    val recurringPaymentId: AutomaticPixRecurringPaymentId
    val mandateId: AutomaticPixMandateId?
    val payee: AutomaticPixPersonData
    val provider: AutomaticPixProvider
    val status: AutomaticPixRecurringPaymentStatus

    fun checkRetryType(): AutomaticPixRetryType = if (recurringPaymentId.value[1] == 'R') {
        AutomaticPixRetryType.ALLOWED_3R_7D
    } else {
        AutomaticPixRetryType.NOT_ALLOWED
    }
}

enum class AutomaticPixRetryType {
    NOT_ALLOWED,
    ALLOWED_3R_7D,
}

data class AutomaticPixRecurringPaymentBasicData(
    override val recurringPaymentId: AutomaticPixRecurringPaymentId,
    override val mandateId: AutomaticPixMandateId?,
    override val payee: AutomaticPixPersonData,
    override val provider: AutomaticPixProvider,
    override val status: AutomaticPixRecurringPaymentStatus,
) : AutomaticPixRecurringPaymentBase

data class AutomaticPixRecurringPayment(
    override val recurringPaymentId: AutomaticPixRecurringPaymentId,
    override val mandateId: AutomaticPixMandateId?,
    val startDate: LocalDate?, // Estimated date of the first payment
    val endDate: LocalDate?, // Estimated date of the last payment (Should not be filled if term is indefinite)
    val fixedAmount: Long?, // Fixed amount of the billing
    val minimumReceiverAmount: Long?, // Minimum receivable amount
    val maximumPayerAmount: Long?, // Maximum payable amount
    override val payee: AutomaticPixPersonData,
    val payer: AutomaticPixPersonData,
    val contractNumber: String?, // Agreement reference number (contract or client code)
    val periodicity: AutomaticPixPeriodicity,
    val description: String?, // Contract description to help the payer identify the recurring payment purpose
    override val provider: AutomaticPixProvider,
    val notificationsScheduling: Boolean?, // Enable/Disable scheduling notifications
    val creditLimitUsage: Boolean?, // Enables/Disables the use of credit limit
    override val status: AutomaticPixRecurringPaymentStatus,
) : AutomaticPixRecurringPaymentBase

data class AutomaticPixRecurringPaymentOnWalletWithItsMandate(
    val walletId: WalletId,
    val recurringPayment: AutomaticPixRecurringPayment,
    val mandate: AutomaticPixNotPendingMandate?,
)

data class AutomaticPixRecurringPaymentOnWallet(
    val walletId: WalletId,
    val automaticPixRecurringPayment: AutomaticPixRecurringPayment,
)

data class AutomaticPixRecurringPaymentWithJourney(
    val recurrenceData: AutomaticPixRecurringPayment,
    val journey: AutomaticPixJourney,
)

data class AutomaticPixPersonData(
    val documentNumber: String?,
    val name: String?,
    val documentType: AutomaticPixDocumentType,
    val pspInformation: AutomaticPixPspInformation?,
)

data class AutomaticPixPspInformation(
    val code: String,
    val name: String?,
)

enum class AutomaticPixMandateStatus {
    PENDING,
    ACTIVE,
    EXPIRED,
    CANCELED,
    PROCESSING,
}

enum class AutomaticPixRecurringPaymentStatus {
    CREATED,
    APPROVED,
    REJECTED,
    EXPIRED,
    CANCELED,
}

enum class AutomaticPixDocumentType {
    CPF,
    CNPJ,
}

enum class AutomaticPixPeriodicity {
    WEEKLY,
    MONTHLY,
    QUARTERLY,
    SEMESTRAL,
    YEARLY,
}

enum class ClientRejectionReason {
    UNRECOGNIZED_PAYEE,
    DECLINED,
}

enum class CanceledBy {
    PAYEE,
    PAYER,
}

enum class AutomaticPixRecurrenceCancellationReason {
    ACCOUNT_CLOSED,
    DECEASED,
    FRAUD,
    USER_REQUEST,
}

enum class AutomaticPixJourney {
    JOURNEY_1,
    JOURNEY_2,
    JOURNEY_3,
    JOURNEY_4,

    ;

    fun requiresManualApprove() = !approveAutomaticallyAfterPayment()

    fun approveAutomaticallyAfterPayment() = when (this) {
        JOURNEY_3 -> true
        JOURNEY_1, JOURNEY_2, JOURNEY_4 -> false
    }

    fun canBeApprovedByQRCodeWithoutPayment() = when (this) {
        JOURNEY_2 -> true
        JOURNEY_1, JOURNEY_3, JOURNEY_4 -> false
    }

    fun userMayApproveAfterPayment() = when (this) {
        JOURNEY_1, JOURNEY_2, JOURNEY_3 -> false
        JOURNEY_4 -> true
    }
}

enum class AutomaticPixProvider {
    ARBI,
}

data class AutomaticPixQrCodeRecurringData(
    val recurringPaymentId: AutomaticPixRecurringPaymentId,
    val journey: AutomaticPixJourney,
    val retryType: AutomaticPixRetryType,
    val fixedAmount: Long?,
    val minimumReceiverAmount: Long?,
    val payee: AutomaticPixPersonData,
    val payer: AutomaticPixPersonData,
    val startDate: LocalDate,
    val endDate: LocalDate?,
    val periodicity: AutomaticPixPeriodicity,
    val description: String?,
    val contractNumber: String,
    val updates: List<String>,
    val provider: AutomaticPixProvider,
)

data class RecurrenceUpdateableValues(
    val maximumPayerAmount: Long?,
    val creditLimitUsage: Boolean,
    val schedulingNotifications: Boolean,
)

data class AutomaticPixBill(
    val recurringPaymentId: AutomaticPixRecurringPaymentId,
    val payee: AutomaticPixPersonData,
    val payer: AutomaticPixPersonData,
    val endToEnd: String,
    val contractNumber: String?,
    val amount: Long,
    val settlementDate: LocalDate,
    val description: String? = null,
    val additionalInformation: String?,
)

data class AutomaticPixBillOnWallet(
    val walletId: WalletId,
    val billId: BillId,
    val automaticPixBill: AutomaticPixBill,
)