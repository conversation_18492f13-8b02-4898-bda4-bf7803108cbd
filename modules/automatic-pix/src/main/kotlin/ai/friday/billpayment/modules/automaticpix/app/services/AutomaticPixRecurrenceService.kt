package ai.friday.billpayment.modules.automaticpix.app.services

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.PixCopyAndPaste
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.integrations.PixQRCodeParserService
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.canScheduleAny
import ai.friday.billpayment.modules.automaticpix.AutomaticPix
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateRepository
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixNotPendingMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixQrCodeRecurringData
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurrenceCancellationReason
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPayment
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentId
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentOnWallet
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentOnWalletWithItsMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentRepository
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentStatus
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentWithJourney
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRemoteManager
import ai.friday.billpayment.modules.automaticpix.app.RecurrenceUpdateableValues
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@AutomaticPix
class AutomaticPixRecurrenceService(
    private val automaticPixRemoteManager: AutomaticPixRemoteManager,
    private val walletService: WalletService,
    private val accountService: AccountService,
    private val automaticPixMessagePublisher: AutomaticPixMessagePublisher,
    private val automaticPixRecurringPaymentRepository: AutomaticPixRecurringPaymentRepository,
    private val findBillService: FindBillService,
    private val qrCodeParser: PixQRCodeParserService,
    private val mandateRepository: AutomaticPixMandateRepository,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun buildRecurringPayment(automaticPixQrCodeRecurringData: AutomaticPixQrCodeRecurringData): Result<AutomaticPixRecurringPaymentWithJourney> = runCatching {
        AutomaticPixRecurringPaymentWithJourney(
            AutomaticPixRecurringPayment(
                recurringPaymentId = automaticPixQrCodeRecurringData.recurringPaymentId,
                mandateId = null,
                startDate = automaticPixQrCodeRecurringData.startDate,
                endDate = automaticPixQrCodeRecurringData.endDate,
                fixedAmount = automaticPixQrCodeRecurringData.fixedAmount,
                minimumReceiverAmount = automaticPixQrCodeRecurringData.minimumReceiverAmount,
                maximumPayerAmount = null,
                payee = automaticPixQrCodeRecurringData.payee,
                payer = automaticPixQrCodeRecurringData.payer,
                contractNumber = automaticPixQrCodeRecurringData.contractNumber,
                periodicity = automaticPixQrCodeRecurringData.periodicity,
                description = automaticPixQrCodeRecurringData.description,

                provider = automaticPixQrCodeRecurringData.provider,
                notificationsScheduling = null,
                creditLimitUsage = null,
                status = AutomaticPixRecurringPaymentStatus.CREATED,
            ),
            journey = automaticPixQrCodeRecurringData.journey,
        )
    }

    fun getAllStoredRecurrences(walletId: WalletId): Result<List<AutomaticPixRecurringPaymentOnWalletWithItsMandate>> = runCatching {
        automaticPixRecurringPaymentRepository.findByWalletId(walletId).map {
            it.withItsMandate()
        }
    }

    private fun AutomaticPixRecurringPaymentOnWallet.withItsMandate(): AutomaticPixRecurringPaymentOnWalletWithItsMandate {
        return AutomaticPixRecurringPaymentOnWalletWithItsMandate(
            walletId = this.walletId,
            recurringPayment = this.automaticPixRecurringPayment,
            mandate = this.automaticPixRecurringPayment.mandateId?.let { mandateId -> mandateRepository.findByMandateIdAndWalletId(mandateId, walletId) as? AutomaticPixNotPendingMandate },
        )
    }

    private fun refreshAndReturnRecurrence(walletId: WalletId, automaticPixRecurringPaymentId: AutomaticPixRecurringPaymentId): AutomaticPixRecurringPaymentOnWalletWithItsMandate {
        automaticPixMessagePublisher.publishMandateRefresh(walletId)
        refreshRecurrences(walletId).getOrThrow()
        return automaticPixRecurringPaymentRepository.findByIdAndWalletId(automaticPixRecurringPaymentId, walletId)?.withItsMandate() ?: throw IllegalStateException("Recurrence not found")
    }

    fun approveRecurrence(
        billId: BillId,
        accountId: AccountId,
        walletId: WalletId,
        automaticApprove: Boolean,
        maximumAmount: Long? = null,
    ): Result<Unit> = runCatching {
        val bill = findBillService.find(walletId, billId)
        if (bill.billType != BillType.PIX) {
            throw IllegalArgumentException("Bill type is not PIX: ${bill.billType}")
        }

        if (bill.walletId != walletId) {
            throw IllegalArgumentException("Bill não está na carteira atual")
        }

        val automaticPixRecurringDataJson = bill.pixQrCodeData?.automaticPixRecurringDataJson ?: throw IllegalArgumentException("Bill does not have automaticPixRecurringDataJson")
        val automaticPixRecurringData = parseObjectFrom<AutomaticPixQrCodeRecurringData>(automaticPixRecurringDataJson)

        if (bill.status != BillStatus.PAID) {
            throw IllegalArgumentException("Bill is not paid: ${bill.status}")
        }

        if (automaticApprove && !automaticPixRecurringData.journey.approveAutomaticallyAfterPayment()) {
            throw IllegalArgumentException("Jornada não permite aprovação automatica: ${automaticPixRecurringData.journey}")
        }

        if (!automaticApprove && !automaticPixRecurringData.journey.userMayApproveAfterPayment()) {
            throw IllegalArgumentException("Jornada não permite aprovação manual: ${automaticPixRecurringData.journey}")
        }

        approveRecurrence(automaticPixRecurringData, accountId, walletId, maximumAmount)
            .onFailure {
                if (automaticApprove) {
                    automaticPixMessagePublisher.notifyAutomaticRecurrenceAcceptFailed(walletId, bill.billId, bill.automaticPixAuthorizationMaximumAmount)
                }
            }.getOrThrow()
    }

    fun approveRecurrence(
        pixCopyAndPaste: PixCopyAndPaste,
        accountId: AccountId,
        walletId: WalletId,
        maximumAmount: Long? = null,
    ): Result<Unit> = runCatching {
        val account = accountService.findAccountById(accountId = accountId)
        val pixQRCodeDetailsResult = qrCodeParser.parseQRCodeCacheable(pixCopyAndPaste, account.document).getOrElse { throw IllegalStateException("Erro ao consultar qrCode") }

        val automaticPixRecurringDataJson = pixQRCodeDetailsResult.qrCodeInfo?.automaticPixRecurringDataJson ?: throw IllegalArgumentException("Bill does not have automaticPixRecurringDataJson")
        val automaticPixRecurringData = parseObjectFrom<AutomaticPixQrCodeRecurringData>(automaticPixRecurringDataJson)

        if (!automaticPixRecurringData.journey.canBeApprovedByQRCodeWithoutPayment()) {
            throw IllegalArgumentException("Jornada não permite aprovação por QR Code sem pagamento: ${automaticPixRecurringData.journey}")
        }

        approveRecurrence(automaticPixRecurringData, accountId, walletId, maximumAmount).getOrThrow()
    }

    private fun approveRecurrence(
        automaticPixQrCodeRecurringData: AutomaticPixQrCodeRecurringData,
        accountId: AccountId,
        walletId: WalletId,
        maximumAmount: Long? = null,
    ): Result<Unit> = runCatching {
        val wallet = walletService.findWallet(walletId)
        if (!hasPermission(accountId, wallet)) {
            throw IllegalStateException("Usuário não tem permissão para agendar pagamentos na carteira")
        }

        val bankAccount = accountService.findAccountPaymentMethodByIdAndAccountId(wallet.paymentMethodId, wallet.founder.accountId).method as? InternalBankAccount ?: throw IllegalStateException("método de pagamento da carteira não é um InternalBankAccount")

        automaticPixRemoteManager.approveRecurrence(
            automaticPixQrCodeRecurringData = automaticPixQrCodeRecurringData,
            payer = wallet.founder,
            accountNumber = bankAccount.buildFullAccountNumber(),
            maximumAmount = maximumAmount,
        ).getOrThrow()

        automaticPixRecurringPaymentRepository.save(
            AutomaticPixRecurringPaymentOnWallet(
                walletId = walletId,
                automaticPixRecurringPayment = automaticPixQrCodeRecurringData.toAutomaticPixRecurringPayment(),
            ),
        )

        automaticPixMessagePublisher.publishMandateRefresh(walletId)
        automaticPixMessagePublisher.publishRecurreceRefresh(walletId)
    }

    fun cancelRecurrence(
        automaticPixRecurringPaymentId: AutomaticPixRecurringPaymentId,
        automaticPixRecurrenceCancellationReason: AutomaticPixRecurrenceCancellationReason,
        accountId: AccountId,
        walletId: WalletId,
    ): Result<AutomaticPixRecurringPaymentOnWalletWithItsMandate> = runCatching {
        if (!hasPermission(accountId, walletId)) {
            throw IllegalStateException("Usuário não tem permissão para agendar pagamentos na carteira")
        }
        automaticPixRemoteManager.cancelRecurrence(automaticPixRecurringPaymentId, automaticPixRecurrenceCancellationReason).getOrThrow()
        refreshAndReturnRecurrence(walletId, automaticPixRecurringPaymentId)
    }

    fun updateRecurrence(
        automaticPixRecurringPaymentId: AutomaticPixRecurringPaymentId,
        updateableValues: RecurrenceUpdateableValues,
        accountId: AccountId,
        walletId: WalletId,
    ): Result<AutomaticPixRecurringPaymentOnWalletWithItsMandate> = runCatching {
        if (!hasPermission(accountId, walletId)) {
            throw IllegalStateException("Usuário não tem permissão para agendar pagamentos na carteira")
        }

        automaticPixRemoteManager.updateRecurrence(automaticPixRecurringPaymentId, updateableValues).getOrThrow()
        refreshAndReturnRecurrence(walletId, automaticPixRecurringPaymentId)
    }

    private fun hasPermission(accountId: AccountId, walletId: WalletId): Boolean {
        val wallet = walletService.findWallet(walletId)
        return hasPermission(accountId, wallet)
    }

    private fun hasPermission(accountId: AccountId, wallet: Wallet): Boolean {
        val walletMember = wallet.getActiveMember(accountId)
        return walletMember.canScheduleAny()
    }

    fun refreshRecurrences(walletId: WalletId): Result<List<AutomaticPixRecurringPaymentOnWalletWithItsMandate>> = runCatching {
        val logName = "AutomaticPixRecurrenceService#refreshRecurrences"
        val markers = append("walletId", walletId.value)
        val wallet = walletService.findWallet(walletId)
        val bankAccount = accountService.findAccountPaymentMethodByIdAndAccountId(wallet.paymentMethodId, wallet.founder.accountId).method as? InternalBankAccount ?: throw IllegalStateException("método de pagamento da carteira não é um InternalBankAccount")

        val localRecurrences = automaticPixRecurringPaymentRepository.findByWalletId(walletId)
        val localRecurrencesIdsMap = localRecurrences.associateBy { it.automaticPixRecurringPayment.recurringPaymentId }

        val remoteRecurrences = automaticPixRemoteManager.getApprovedRecurrences(bankAccount.buildFullAccountNumber(), null).getOrThrow()
            .map { remoteRecurrence ->
                val automaticPixRecurringPaymentOnWallet = automaticPixRecurringPaymentRepository.save(
                    AutomaticPixRecurringPaymentOnWallet(
                        walletId = walletId,
                        automaticPixRecurringPayment = remoteRecurrence,
                    ),
                )
                val localRecurrence = localRecurrencesIdsMap[remoteRecurrence.recurringPaymentId]
                if (localRecurrence == null || localRecurrence.automaticPixRecurringPayment.status != remoteRecurrence.status) {
                    automaticPixMessagePublisher.notifyRecurrenceApproved(
                        automaticPixRecurringPaymentOnWallet = automaticPixRecurringPaymentOnWallet,
                    )
                }
                automaticPixRecurringPaymentOnWallet
            }

        val remoteRecurrencesIds = remoteRecurrences.associateBy { it.automaticPixRecurringPayment.recurringPaymentId }

        localRecurrences.filter { it.automaticPixRecurringPayment.recurringPaymentId !in remoteRecurrencesIds }.forEach { localRecurrence ->
            automaticPixRemoteManager.getRecurrenceStatus(localRecurrence.automaticPixRecurringPayment.recurringPaymentId).onFailure {
                logger.error(markers.andAppend("recurringPaymentId", localRecurrence.automaticPixRecurringPayment.recurringPaymentId.value).andAppend("context", "Erro ao buscar status da recorrência no remoto"), logName, it)
            }.onSuccess { remoteStatus ->
                if (remoteStatus != localRecurrence.automaticPixRecurringPayment.status) {
                    logger.info(markers.andAppend("recurringPaymentId", localRecurrence.automaticPixRecurringPayment.recurringPaymentId.value).andAppend("oldStatus", localRecurrence.automaticPixRecurringPayment.status).andAppend("updatedStatus", remoteStatus).andAppend("context", "recurrence status updated"), logName)
                    automaticPixMessagePublisher.notifyRecurrenceStatusChange(
                        automaticPixRecurringPaymentOnWallet = localRecurrence,
                        newStatus = remoteStatus,
                    )

                    automaticPixRecurringPaymentRepository.save(
                        AutomaticPixRecurringPaymentOnWallet(
                            walletId = walletId,
                            automaticPixRecurringPayment = localRecurrence.automaticPixRecurringPayment.copy(
                                status = remoteStatus,
                            ),
                        ),
                    )
                }
            }
        }

        remoteRecurrences.map {
            it.withItsMandate()
        }
    }
}

private fun AutomaticPixQrCodeRecurringData.toAutomaticPixRecurringPayment(): AutomaticPixRecurringPayment {
    return AutomaticPixRecurringPayment(
        recurringPaymentId = this.recurringPaymentId,
        mandateId = null, // Mandate will be set later
        startDate = this.startDate,
        endDate = this.endDate,
        fixedAmount = this.fixedAmount,
        minimumReceiverAmount = this.minimumReceiverAmount,
        maximumPayerAmount = null, // This can be set later if needed
        payee = this.payee,
        payer = this.payer,
        contractNumber = this.contractNumber,
        periodicity = this.periodicity,
        description = this.description,
        provider = this.provider,
        notificationsScheduling = null, // This can be set later if needed
        creditLimitUsage = null, // This can be set later if needed
        status = AutomaticPixRecurringPaymentStatus.CREATED,
    )
}