package ai.friday.billpayment.modules.automaticpix.app.services

import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.AutomaticPixData
import ai.friday.billpayment.app.bill.AutomaticPixPayer
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateBillService
import ai.friday.billpayment.app.bill.CreatePixRequest
import ai.friday.billpayment.app.bill.PspInformation
import ai.friday.billpayment.app.bill.RecipientRequest
import ai.friday.billpayment.app.payment.BalanceAuthorization
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.BillPaymentCommand
import ai.friday.billpayment.app.payment.PaymentMethodsDetail
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.transaction.CompleteTransaction
import ai.friday.billpayment.app.payment.transaction.FailTransaction
import ai.friday.billpayment.app.payment.transaction.StartTransaction
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.modules.automaticpix.AutomaticPix
import ai.friday.billpayment.modules.automaticpix.adapters.api.PaymentLifecycleEventType
import ai.friday.billpayment.modules.automaticpix.adapters.api.PaymentLifecycleStatus
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixBill
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixBillOnWallet
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixBillRepository
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRemoteManager
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.log.andAppend
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@AutomaticPix
class AutomaticPixBillingService(
    private val automaticPixRemoteManager: AutomaticPixRemoteManager,
    private val walletService: WalletService,
    private val accountService: AccountService,
    private val automaticPixMessagePublisher: AutomaticPixMessagePublisher,
    private val automaticPixBillRepository: AutomaticPixBillRepository,
    private val createBillService: CreateBillService,
    private val startTransaction: StartTransaction,
    private val completeTransaction: CompleteTransaction,
    private val transactionService: TransactionService,
    private val failTransaction: FailTransaction,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    private fun refreshBillingAsync(walletId: WalletId): Result<Unit> = runCatching {
        automaticPixMessagePublisher.publishBillingRefresh(walletId)
    }

    fun getAllStoredBills(walletId: WalletId): Result<List<AutomaticPixBillOnWallet>> = runCatching {
        automaticPixBillRepository.findByWalletId(walletId)
    }

    fun refreshBilling(walletId: WalletId): Result<List<AutomaticPixBillOnWallet>> = runCatching {
        val wallet = walletService.findWallet(walletId)
        val bankAccount = accountService.findAccountPaymentMethodByIdAndAccountId(wallet.paymentMethodId, wallet.founder.accountId).method as? InternalBankAccount ?: throw IllegalStateException("método de pagamento da carteira não é um InternalBankAccount")

        val localBills = automaticPixBillRepository.findByWalletId(walletId)
        val localBillsEndToEndMap = localBills.associateBy { it.automaticPixBill.endToEnd }

        val remoteBills = automaticPixRemoteManager.listBilling(bankAccount.buildFullAccountNumber(), null).getOrThrow()
        val remoteEndToEnd = remoteBills.map { it.endToEnd }.toSet()

        val billsToRemove = localBills.filter { localBill -> localBill.automaticPixBill.endToEnd !in remoteEndToEnd }

        val toReturnBills = remoteBills.mapNotNull { remoteBill ->
            val localFoundBill = localBillsEndToEndMap[remoteBill.endToEnd]

            if (localFoundBill == null) {
                val created = createBillFromAutomaticPix(remoteBill, walletId).getOrElse { null }
                if (created != null) {
                    automaticPixBillRepository.save(created.automaticPixBill, walletId, created.billId)
                    created
                } else {
                    null
                }
            } else {
                if (localFoundBill.automaticPixBill != remoteBill) {
                    automaticPixBillRepository.save(remoteBill, localFoundBill.walletId, localFoundBill.billId)
                    val bill = localFoundBill.copy(automaticPixBill = remoteBill)
                    automaticPixMessagePublisher.notifyBillCancelled(bill)
                    bill
                } else {
                    localFoundBill
                }
            }
        }

        billsToRemove.forEach { billToRemove ->
            automaticPixBillRepository.remove(billToRemove.automaticPixBill, walletId)
            /*automaticPixMessagePublisher.notifyBillPaymentFailed(
                AutomaticPixBillOnWallet(
                    walletId = walletId,
                    billId = billToRemove.billId,
                    automaticPixBill = billToRemove.automaticPixBill,
                ),
                PaymentLifecycleStatus.CANCELED,
            )*/ // o esperado é que aconteca por evento o aviso de que a cobrança foi cancelada, não é necessário enviar manualmente
        }

        toReturnBills
    }

    fun cancelBilling(walletId: WalletId, endToEnd: String): Result<Unit> = runCatching {
        val bills = automaticPixBillRepository.findByEndToEnd(endToEnd)
        val logName = "AutomaticPixBillingService#cancelBilling"
        val markers = append("walletId", walletId.value).andAppend("endToEnd", endToEnd)
        if (bills.isEmpty()) {
            logger.warn(markers, "No automatic pix bill found for cancellation")
            return Result.failure(IllegalStateException("Bill not found for endToEnd: $endToEnd"))
        }

        val bill = bills[0]
        markers.andAppend("billId", bill.billId.value)

        if (bill.walletId != walletId) {
            return Result.failure<Unit>(IllegalStateException("Bill not found for walletId: $walletId and endToEnd: $endToEnd")).also {
                logger.warn(markers, logName, it)
            }
        }

        if (!bill.automaticPixBill.settlementDate.isAfter(getLocalDate())) {
            return Result.failure<Unit>(IllegalStateException("Cannot cancel bill that is not in the future: $endToEnd")).also {
                logger.warn(markers, logName, it)
            }
        }

        automaticPixRemoteManager.cancelBilling(endToEnd).getOrThrow()
        refreshBillingAsync(walletId)
    }

    fun manageAutomaticPixPayment(command: ManageAutomaticPixPaymentCommand): Result<Unit> = runCatching {
        val logName = "AutomaticPixBillingService#manageAutomaticPixPayment"
        val automaticPixBillOnWallet = command.automaticPixBillOnWallet
        val paymentLifecycleEventType = command.paymentLifecycleEventType
        val paymentLifecycleStatus = command.paymentLifecycleStatus

        val wallet = walletService.findWallet(automaticPixBillOnWallet.walletId)

        val actionSource = ActionSource.AutomaticPix(
            accountId = wallet.founder.accountId,
            endToEnd = automaticPixBillOnWallet.automaticPixBill.endToEnd,
            recurringPaymentId = automaticPixBillOnWallet.automaticPixBill.recurringPaymentId.value,
        )

        val transaction: Transaction? = when (paymentLifecycleStatus) {
            PaymentLifecycleStatus.SCHEDULED -> {
                automaticPixMessagePublisher.notifyBillPaymentScheduled(automaticPixBillOnWallet)
                null
            }

            PaymentLifecycleStatus.PROCESSING -> {
                automaticPixMessagePublisher.notifyBillPaymentProcessing(automaticPixBillOnWallet)
                null
            }

            PaymentLifecycleStatus.STARTED -> {
                getProcessingOrStartAutomaticPixPaymentTransaction(automaticPixBillOnWallet, actionSource, wallet)
            }

            PaymentLifecycleStatus.PAID -> {
                val transaction = getProcessingOrStartAutomaticPixPaymentTransaction(automaticPixBillOnWallet, actionSource, wallet)
                completeAutomaticPixPaymentTransaction(automaticPixBillOnWallet, transaction).getOrThrow()
            }

            PaymentLifecycleStatus.REJECTED -> {
                val transaction = getProcessingOrStartAutomaticPixPaymentTransaction(automaticPixBillOnWallet, actionSource, wallet)
                failAutomaticPixPaymentTransaction(transaction).getOrThrow().also {
                    automaticPixMessagePublisher.notifyBillPaymentFailed(automaticPixBillOnWallet, command.paymentLifecycleStatus)
                }
            }

            PaymentLifecycleStatus.CANCELED -> {
                null
            }

            PaymentLifecycleStatus.UNKNOWN -> {
                when (paymentLifecycleEventType) {
                    PaymentLifecycleEventType.PAYMENT_FAILED_NO_DAILY_LIMIT_NGRADE,
                    PaymentLifecycleEventType.PAYMENT_FAILED_NO_DAILY_LIMIT_ULTGRADE,
                    -> {
                        val transaction = getProcessingOrStartAutomaticPixPaymentTransaction(automaticPixBillOnWallet, actionSource, wallet)
                        failAutomaticPixPaymentTransaction(transaction).getOrThrow().also {
                            automaticPixMessagePublisher.notifyBillPaymentFailed(automaticPixBillOnWallet, command.paymentLifecycleStatus)
                        }
                    }

                    PaymentLifecycleEventType.PAYMENT_FAILED_INSUFFICIENT_BALANCE_NGRADE,
                    PaymentLifecycleEventType.PAYMENT_FAILED_INSUFFICIENT_BALANCE_ULTGRADE,
                    PaymentLifecycleEventType.PAYMENT_SCHEDULED,
                    PaymentLifecycleEventType.PAYMENT_COMPLETED,
                    PaymentLifecycleEventType.PAYMENT_CANCELED,
                    PaymentLifecycleEventType.PAYMENT_FAILED_DICT_KEY_MISMATCH,
                    PaymentLifecycleEventType.PAYMENT_FAILED_OPERATIONAL_ERROR,
                    PaymentLifecycleEventType.PAYMENT_FAILED_EXPIRED,
                    -> null
                }
            }
        }

        logger.info(append("transactionId", transaction?.id?.value).andAppend("billId", command.automaticPixBillOnWallet.billId.value).andAppend("walletId", command.automaticPixBillOnWallet.walletId.value), logName)
    }

    private fun getProcessingOrStartAutomaticPixPaymentTransaction(
        automaticPixBillOnWallet: AutomaticPixBillOnWallet,
        actionSource: ActionSource.AutomaticPix,
        wallet: Wallet,
    ): Transaction {
        return transactionService.findTransactionsByBill(automaticPixBillOnWallet.billId, status = TransactionStatus.PROCESSING).firstOrNull() ?: startAutomaticPixPaymentTransaction(
            automaticPixBillOnWallet = automaticPixBillOnWallet,
            actionSource = actionSource,
            paymentMethodsDetail = PaymentMethodsDetailWithBalance(
                amount = automaticPixBillOnWallet.automaticPixBill.amount,
                paymentMethodId = wallet.paymentMethodId,
                calculationId = null,
            ),
        ).getOrThrow()
    }

    private fun startAutomaticPixPaymentTransaction(
        automaticPixBillOnWallet: AutomaticPixBillOnWallet,
        actionSource: ActionSource.AutomaticPix,
        paymentMethodsDetail: PaymentMethodsDetail,
    ): Result<Transaction> = runCatching {
        val command = BillPaymentCommand(
            walletId = automaticPixBillOnWallet.walletId,
            billId = automaticPixBillOnWallet.billId,
            actionSource = actionSource,
            paymentDetails = paymentMethodsDetail,
        )
        val transaction = startTransaction.execute(
            command = command,
        )
        transaction
    }

    private fun completeAutomaticPixPaymentTransaction(
        automaticPixBillOnWallet: AutomaticPixBillOnWallet,
        transaction: Transaction,
    ): Result<Transaction> = runCatching {
        if (transaction.status == TransactionStatus.COMPLETED) {
            return@runCatching transaction
        }
        val bankOperationId = BankOperationId(transaction.id.value)

        val bankTransfer = BankTransfer(
            operationId = bankOperationId,
            gateway = FinancialServiceGateway.ARBI,
            status = BankOperationStatus.SUCCESS,
            amount = transaction.settlementData.totalAmount,
            authentication = automaticPixBillOnWallet.automaticPixBill.endToEnd,
            pixKeyDetails = null,
        )

        transaction.apply {
            settlementData.settlementOperation = bankTransfer
            status = TransactionStatus.COMPLETED
            paymentData.toSingle().payment = BalanceAuthorization(
                operationId = bankOperationId,
                status = BankOperationStatus.SUCCESS,
                amount = transaction.settlementData.totalAmount,
                paymentGateway = FinancialServiceGateway.ARBI,
            )
        }
        transactionService.save(transaction)
        completeTransaction.execute(transaction = transaction)
        transaction
    }

    private fun failAutomaticPixPaymentTransaction(
        transaction: Transaction,
    ): Result<Transaction> = runCatching {
        val bankOperationId = BankOperationId(transaction.id.value)

        transaction.apply {
            status = TransactionStatus.FAILED
            paymentData.toSingle().payment = BalanceAuthorization(
                operationId = bankOperationId,
                status = BankOperationStatus.ERROR,
                amount = transaction.settlementData.totalAmount,
                paymentGateway = FinancialServiceGateway.ARBI,
            )
        }
        transactionService.save(transaction)
        failTransaction.execute(transaction)
        transaction
    }

    private fun createBillFromAutomaticPix(
        automaticPixBill: AutomaticPixBill,
        walletId: WalletId,
    ): Result<AutomaticPixBillOnWallet?> = runCatching {
        val logName = "AutomaticPixBillingService#createBillFromAutomaticPix"
        val wallet = walletService.findWallet(walletId)

        val createPixRequest = CreatePixRequest(
            description = listOfNotNull(automaticPixBill.description, automaticPixBill.contractNumber).joinToString { " - " },
            dueDate = automaticPixBill.settlementDate,
            amount = automaticPixBill.amount,
            recipient = RecipientRequest(
                id = null,
                accountId = wallet.founder.accountId,
                name = automaticPixBill.payee.name,
                document = automaticPixBill.payee.documentNumber,
                alias = "",
                bankAccount = null,
                pixKey = null,
                qrCode = null,
                pspInfo = automaticPixBill.payee.pspInformation?.let { psp ->
                    PspInformation(
                        code = psp.code,
                        name = psp.name,
                    )
                },
            ),
            source = ActionSource.AutomaticPix(
                accountId = wallet.founder.accountId, // TODO - tem que ser o accountId de quem autorizou o pagamento recorrente
                endToEnd = automaticPixBill.endToEnd,
                recurringPaymentId = automaticPixBill.recurringPaymentId.value,
            ),
            recurrenceRule = null,
            categoryId = null,
            walletId = walletId,
            automaticPixAuthorizationMaximumAmount = null,
            automaticPixData = AutomaticPixData(
                payer = AutomaticPixPayer(automaticPixBill.payer.documentNumber, automaticPixBill.payer.name),
                description = automaticPixBill.description,
                additionalInformation = automaticPixBill.additionalInformation,
                contractNumber = automaticPixBill.contractNumber,
            ),
        )

        val createPixResult = createBillService.createPix(
            request = createPixRequest,
            dryRun = false,
        )

        if (createPixResult is CreateBillResult.SUCCESS) {
            logger.info(append("walletId", walletId.value).andAppend("endToEnd", automaticPixBill.endToEnd), logName)
            AutomaticPixBillOnWallet(
                walletId = walletId,
                billId = createPixResult.bill.billId,
                automaticPixBill = automaticPixBill,
            )
        } else {
            logger.error(append("walletId", walletId.value).andAppend("endToEnd", automaticPixBill.endToEnd).andAppend("createPixResult", createPixResult), logName)
            null
        }
    }
}

data class ManageAutomaticPixPaymentCommand(
    val automaticPixBillOnWallet: AutomaticPixBillOnWallet,
    val paymentLifecycleEventType: PaymentLifecycleEventType,
    val paymentLifecycleStatus: PaymentLifecycleStatus,
    val correlationId: String?,
    val message: String?,
)