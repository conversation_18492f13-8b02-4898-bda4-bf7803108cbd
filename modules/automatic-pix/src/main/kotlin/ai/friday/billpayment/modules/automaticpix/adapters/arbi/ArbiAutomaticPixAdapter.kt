package ai.friday.billpayment.modules.automaticpix.adapters.arbi

import ai.friday.billpayment.adapters.arbi.NewArbiAuthenticationManager
import ai.friday.billpayment.app.isValidCpf
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.automaticpix.AutomaticPix
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixBill
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixDocumentType
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixJourney
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateId
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateStatus
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixNotPendingMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPendingMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPeriodicity
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPersonData
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixProvider
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPspInformation
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixQrCodeRecurringData
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurrenceCancellationReason
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPayment
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentBasicData
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentId
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentStatus
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRemoteManager
import ai.friday.billpayment.modules.automaticpix.app.CanceledBy
import ai.friday.billpayment.modules.automaticpix.app.ClientRejectionReason
import ai.friday.billpayment.modules.automaticpix.app.RecurrenceUpdateableValues
import ai.friday.billpayment.request
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.log.andAppend
import arrow.core.mapNotNull
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpMethod
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.uri.UriBuilder
import io.micronaut.rxjava2.http.client.RxHttpClient
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

private const val ARBI_ROUTING_NO = "0001"
private const val ARBI_ISPB_CODE = "********"

private val arbiDateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX")

@ConfigurationProperties("integrations.arbi")
class ArbiAutomaticPixConfiguration {
    lateinit var clientId: String
    lateinit var automaticPixBillingPath: String
    lateinit var automaticPixBillingCancelPath: String

    lateinit var automaticPixPendingMandatePath: String
    lateinit var automaticPixMandatePath: String
    lateinit var automaticPixRejectMandatePath: String
    lateinit var automaticPixApproveMandatePath: String

    lateinit var automaticPixRecurringPaymentsStatusPath: String
    lateinit var automaticPixRecurringPaymentsPath: String
    lateinit var automaticPixRecurringPaymentsApprovePath: String
    lateinit var automaticPixRecurringPaymentsCancelPath: String
    lateinit var automaticPixRecurringPaymentsUpdatePath: String
}

@AutomaticPix
class ArbiAutomaticPixAdapter(
    @param:Client(
        id = "arbi",
    ) private val httpClient: RxHttpClient,
    private val configuration: ArbiAutomaticPixConfiguration,
    private val authenticationManager: NewArbiAuthenticationManager,
) : AutomaticPixRemoteManager {

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun proxyCall(path: String, method: HttpMethod, body: Map<String, Any>?, qs: Map<String, String>?, headers: Map<String, String>?): Result<String> {
        val logName = "ArbiAutomaticPixAdapter#proxyCall"
        val markers = append("message", "This is a proxy call to test the Arbi ArbiAutomaticPixAdapter")
        return retrieveSingle<Map<String, Any>, String>(
            method = method,
            uri = path,
            body = body,
            qs = qs ?: emptyMap(),
            headers = headers ?: emptyMap(),
            logContext = { logName to markers },
        ).map {
            it.data
        }
    }

    override fun getPendingMandates(walletId: WalletId, accountNumber: String, automaticPixRecurringPaymentId: AutomaticPixRecurringPaymentId?): Result<List<AutomaticPixPendingMandate>> = runCatching {
        val logName = "ArbiAutomaticPixAdapter#getPendingMandates"

        val requestPayload = mapOf(
            "branch" to ARBI_ROUTING_NO,
            "accountNumber" to accountNumber,
            "recurrenceId" to automaticPixRecurringPaymentId?.value,
        ).mapNotNull { it.value }

        val markers = append("requestPayload", requestPayload)

        return retrieveAllPages<Unit, PendingMandateResponseTO>(
            HttpMethod.GET,
            configuration.automaticPixPendingMandatePath,
            qs = requestPayload,
            logContext = { logName to markers },
        ).map { arbiResponseWrapper ->
            arbiResponseWrapper.data.map { current -> current.toDomain(walletId) }
        }
    }

    override fun getMandates(walletId: WalletId, accountNumber: String, automaticPixRecurringPaymentId: AutomaticPixRecurringPaymentId?): Result<List<AutomaticPixNotPendingMandate>> = runCatching {
        val logName = "ArbiAutomaticPixAdapter#getMandates"

        val requestPayload = mapOf(
            "branch" to ARBI_ROUTING_NO,
            "accountNumber" to accountNumber,
            "recurrenceId" to automaticPixRecurringPaymentId?.value, // deveria ser nulável pela documentação. mas a API do arbi não está aceitando null
        ).mapNotNull { it.value }

        val markers = append("requestPayload", requestPayload)

        return retrieveAllPages<Unit, MandateResponseTO>(
            HttpMethod.GET,
            configuration.automaticPixMandatePath,
            qs = requestPayload,
            logContext = { logName to markers },
        ).map {
            it.data.map {
                    current ->
                current.toDomain(walletId)
            }
        }
    }

    override fun rejectMandate(automaticPixMandateId: AutomaticPixMandateId, automaticPixRecurringPaymentId: AutomaticPixRecurringPaymentId, clientRejectionReason: ClientRejectionReason): Result<AutomaticPixMandateStatus> = runCatching {
        val logName = "ArbiAutomaticPixAdapter#rejectMandate"

        val requestPayload = RejectMandateRequestTO(
            recurrenceId = automaticPixRecurringPaymentId.value,
            rejectionReason = ArbiRejectionReason.of(clientRejectionReason),
        )
        val markers = append("requestPayload", requestPayload)

        val uri = UriBuilder.of(configuration.automaticPixRejectMandatePath).expand(mutableMapOf("id" to automaticPixMandateId.value)).toString()

        return retrieveSingle<_, Unit>(
            method = HttpMethod.POST,
            uri = uri,
            body = requestPayload,
            logContext = { logName to markers },
        ).map { AutomaticPixMandateStatus.CANCELED }
    }

    override fun approveMandate(automaticPixMandateId: AutomaticPixMandateId, automaticPixRecurringPaymentId: AutomaticPixRecurringPaymentId, payerName: String, maximumAmount: Long?): Result<AutomaticPixMandateStatus> = runCatching {
        val logName = "ArbiAutomaticPixAdapter#approveMandate"

        val requestPayload = ApproveMandateRequestTO(
            recurrenceId = automaticPixRecurringPaymentId.value,
            maximumAmount = maximumAmount?.toDoubleMoneyAmount(),
            payer = ApproveMandatePayerRequestTO(
                name = payerName,
                accountType = "CACC",
            ),
        )
        val markers = append("requestPayload", requestPayload)

        val uri = UriBuilder.of(configuration.automaticPixApproveMandatePath).expand(mutableMapOf("id" to automaticPixMandateId.value)).toString()

        return retrieveSingle<_, Unit>(
            method = HttpMethod.POST,
            uri = uri,
            body = requestPayload,
            logContext = { logName to markers },
        ).map { AutomaticPixMandateStatus.PROCESSING }
    }

    override fun getRecurrenceStatus(recurringPaymentId: AutomaticPixRecurringPaymentId): Result<AutomaticPixRecurringPaymentStatus> = runCatching {
        val logName = "ArbiAutomaticPixAdapter#getRecurrenceStatus"
        val markers = append("recurringPaymentId", recurringPaymentId.value)
        val uri = UriBuilder.of(configuration.automaticPixRecurringPaymentsStatusPath).expand(mutableMapOf("id" to recurringPaymentId.value)).toString()
        return retrieveSingle<Unit, RecurrenceStatusResponseTO>(
            method = HttpMethod.GET,
            uri = uri,
            logContext = { logName to markers },
        ).map {
            it.data.status
        }
    }

    override fun getApprovedRecurrences(accountNumber: String, recurringPaymentId: AutomaticPixRecurringPaymentId?): Result<List<AutomaticPixRecurringPayment>> = runCatching {
        val logName = "ArbiAutomaticPixAdapter#getRecurrences"

        val requestPayload = mapOf(
            "branch" to ARBI_ROUTING_NO,
            "accountNumber" to accountNumber,
            "recurrenceId" to recurringPaymentId?.value,
        ).mapNotNull { it.value }

        val markers = append("requestPayload", requestPayload)

        return retrieveAllPages<Unit, RecurrenceOperationResponseTO>(
            method = HttpMethod.GET,
            uri = configuration.automaticPixRecurringPaymentsPath,
            qs = requestPayload,
            logContext = { logName to markers },
        ).map { arbiResponseWrapper ->
            arbiResponseWrapper.data.map { current -> current.toDomain() }
        }
    }

    override fun approveRecurrence(automaticPixQrCodeRecurringData: AutomaticPixQrCodeRecurringData, payer: Member, accountNumber: String, maximumAmount: Long?): Result<Unit> = runCatching {
        val billingTO = RecurrenceBillingRequestTO(
            startDate = automaticPixQrCodeRecurringData.startDate.format(DateTimeFormatter.ISO_DATE),
            endDate = automaticPixQrCodeRecurringData.endDate?.format(DateTimeFormatter.ISO_DATE),
            periodicity = ArbiPeriodicity.of(automaticPixQrCodeRecurringData.periodicity),
            amount = automaticPixQrCodeRecurringData.fixedAmount?.toDoubleMoneyAmount(),
            minimumReceiverAmount = automaticPixQrCodeRecurringData.minimumReceiverAmount?.toDoubleMoneyAmount(),
            maximumPayerAmount = maximumAmount?.toDoubleMoneyAmount(),
            receiver = automaticPixQrCodeRecurringData.payee.toReceiverTO(),
            debtor = automaticPixQrCodeRecurringData.payer.toPersonTO(),
            payer = payer.toPayerTO(accountNumber),
            contractNumber = automaticPixQrCodeRecurringData.contractNumber,
            description = automaticPixQrCodeRecurringData.description,
        )

        return approveRecurrence(automaticPixQrCodeRecurringData.recurringPaymentId, automaticPixQrCodeRecurringData.journey, billingTO)
    }

    private fun approveRecurrence(recurringPaymentId: AutomaticPixRecurringPaymentId, journey: AutomaticPixJourney, billing: RecurrenceBillingRequestTO): Result<Unit> = runCatching {
        val logName = "ArbiAutomaticPixAdapter#approveRecurrence"

        val requestPayload = RecurrenceApproveRequestTO(
            journey = journey,
            createAt = getZonedDateTime().format(arbiDateTimeFormatter),
            billing = billing,
        )

        val uri = UriBuilder.of(configuration.automaticPixRecurringPaymentsApprovePath).expand(mutableMapOf("id" to recurringPaymentId.value)).toString()
        val markers = append("requestPayload", requestPayload).andAppend("uri", uri)

        return retrieveSingle<_, Unit>(
            method = HttpMethod.POST,
            uri = uri,
            body = requestPayload,
            logContext = { logName to markers },
        ).map {
            it.data
        }
    }

    override fun cancelRecurrence(recurringPaymentId: AutomaticPixRecurringPaymentId, automaticPixRecurrenceCancellationReason: AutomaticPixRecurrenceCancellationReason): Result<Unit> = runCatching {
        val logName = "ArbiAutomaticPixAdapter#cancelRecurrence"

        val requestPayload = RecurrenceCancelRequestTO(
            cancellationReason = ArbiAutomaticPixRecurrenceCancellationReason.of(automaticPixRecurrenceCancellationReason),
        )
        val markers = append("requestPayload", requestPayload)

        val uri = UriBuilder.of(configuration.automaticPixRecurringPaymentsCancelPath).expand(mutableMapOf("id" to recurringPaymentId.value)).toString()
        return retrieveSingle<_, String>(
            method = HttpMethod.POST,
            uri = uri,
            body = requestPayload,
            logContext = { logName to markers },
        ).map { it.data }
    }

    override fun updateRecurrence(recurringPaymentId: AutomaticPixRecurringPaymentId, command: RecurrenceUpdateableValues): Result<RecurrenceUpdateableValues> = runCatching {
        val logName = "ArbiAutomaticPixAdapter#updateRecurrence"

        val requestPayload = RecurrenceUpdateRequestTO(
            maximumPayerAmount = command.maximumPayerAmount?.toDoubleMoneyAmount(),
            creditLimitUsage = command.creditLimitUsage,
            schedulingNotifications = command.schedulingNotifications,
        )
        val markers = append("requestPayload", requestPayload)

        val uri = UriBuilder.of(configuration.automaticPixRecurringPaymentsUpdatePath).expand(mutableMapOf("id" to recurringPaymentId.value)).toString()
        return retrieveSingle<_, RecurrenceUpdateResponseTO>(
            method = HttpMethod.PUT,
            uri = uri,
            body = requestPayload,
            logContext = { logName to markers },
        ).map {
            RecurrenceUpdateableValues(
                maximumPayerAmount = it.data.maximumPayerAmount?.toLongMoneyAmount(),
                creditLimitUsage = it.data.creditLimitUsage,
                schedulingNotifications = it.data.schedulingNotifications,
            )
        }
    }

    override fun listBilling(accountNumber: String, recurringPaymentId: AutomaticPixRecurringPaymentId?): Result<List<AutomaticPixBill>> = runCatching {
        val logName = "ArbiAutomaticPixAdapter#getBilling"

        val requestPayload = mapOf(
            "branch" to ARBI_ROUTING_NO,
            "accountNumber" to accountNumber,
            "recurrenceId" to recurringPaymentId?.value,
        ).mapNotNull { it.value }

        val markers = append("requestPayload", requestPayload)
        return retrieveAllPages<Unit, BillingResponseTO>(
            method = HttpMethod.GET,
            uri = configuration.automaticPixBillingPath,
            qs = requestPayload,
            logContext = { logName to markers },
        ).map { arbiResponseWrapper ->
            arbiResponseWrapper.data.map { it.toDomain() }
        }
    }

    override fun cancelBilling(endToEnd: String): Result<Unit> = runCatching {
        val logName = "ArbiAutomaticPixAdapter#cancelBilling"
        val markers = append("endToEnd", endToEnd)

        val uri = UriBuilder.of(configuration.automaticPixBillingCancelPath).expand(mutableMapOf("endToEnd" to endToEnd)).toString()
        return retrieveSingle<Unit, Unit>(method = HttpMethod.POST, uri = uri, logContext = { logName to markers }).map {
            it.data
        }
    }

    private inline fun <I, reified O> retrieveSingle(
        method: HttpMethod,
        uri: String,
        body: I? = null,
        qs: Map<String, String> = emptyMap(),
        headers: Map<String, String> = emptyMap(),
        noinline logContext: () -> Pair<String, LogstashMarker> = { "ArbiAutomaticPixAdapter#retrieveSingleResponse" to Markers.empty() },
    ): Result<ArbiResponseWrapper<O>> = runCatching {
        val httpRequest = createHttpRequest(method = method, uri = uri, body = body, qs = qs, headers = headers, pageNumber = null, pageSize = null)
        val response = httpClient.exchange(httpRequest, Argument.of(O::class.java), Argument.of(String::class.java)).firstOrError().blockingGet()
        ArbiResponseWrapper(
            data = response.body(),
            arbiResponseInfo = response.extractResponseInfo(),
        )
    }.onSuccess {
        successLog(it, logContext)
    }.onFailure { error ->
        defaultErrorHandler(error, logContext)
    }

    private inline fun <I, reified O> retrievePage(
        method: HttpMethod,
        uri: String,
        body: I?,
        qs: Map<String, String>,
        headers: Map<String, String>,
        pageNumber: Int,
        pageSize: Int,
        noinline logContext: () -> Pair<String, LogstashMarker>,
    ): Result<ArbiResponseWrapper<List<O>>> = runCatching {
        val httpRequest = createHttpRequest(method = method, uri = uri, body = body, qs = qs, headers = headers, pageNumber = pageNumber, pageSize = pageSize)
        val response = httpClient.exchange(httpRequest, Argument.listOf(O::class.java), Argument.STRING).firstOrError().blockingGet()
        ArbiResponseWrapper(
            data = response.body(),
            arbiResponseInfo = response.extractResponseInfo(),
        )
    }.onSuccess {
        successLog(it, logContext)
    }.onFailure { error ->
        defaultErrorHandler(error, logContext)
    }

    private inline fun <I, reified O> retrieveAllPages(
        method: HttpMethod,
        uri: String,
        body: I? = null,
        qs: Map<String, String> = emptyMap(),
        headers: Map<String, String> = emptyMap(),
        noinline logContext: () -> Pair<String, LogstashMarker> = { "ArbiAutomaticPixAdapter#retrievePaginatedResponse" to Markers.empty() },
    ): Result<ArbiResponseWrapper<List<O>>> = runCatching {
        val pageSize = 10
        var currentPage = 0
        var lastPageData: ArbiResponseInfo?
        val allItems = mutableListOf<O>()

        do {
            currentPage++
            val pageResult = retrievePage<I, O>(method = method, uri = uri, body = body, qs = qs, headers = headers, pageNumber = currentPage, pageSize = pageSize, logContext = logContext).getOrThrow()
            allItems.addAll(pageResult.data)
            lastPageData = pageResult.arbiResponseInfo
        } while (lastPageData?.arbiResponsePageData?.hasNextPage == true)

        ArbiResponseWrapper(
            data = allItems,
            arbiResponseInfo = lastPageData,
        )
    }

    private fun <T> successLog(response: ArbiResponseWrapper<T>, logContext: () -> Pair<String, LogstashMarker>) {
        val (logName, markers) = logContext()
        logger.info(markers.andAppend("response", response), logName)
    }

    private fun defaultErrorHandler(error: Throwable, logContext: () -> Pair<String, LogstashMarker>) {
        val (logName, markers) = logContext()

        when (error) {
            is HttpClientResponseException -> {
                if (error.status == HttpStatus.UNAUTHORIZED) {
                    logger.warn(markers.andAppend("error_message", "Token is expired"), logName)
                    authenticationManager.cleanTokens()
                } else {
                    logger.error(markers, logName, error)
                }
            }

            else -> {
                logger.error(markers.andAppend("error", error), "Erro inesperado: ${error.message}")
            }
        }
    }

    private fun <I> createHttpRequest(
        method: HttpMethod,
        uri: String,
        body: I?,
        qs: Map<String, String>,
        headers: Map<String, String>,
        pageNumber: Int?,
        pageSize: Int?,
    ): MutableHttpRequest<I?> {
        return request(
            method = method,
            path = uri,
            body = body,
            qs = qs,
            headers = (
                mapOf(
                    "Content-Type" to "application/json",
                    "Accept" to "application/json",
                    "client_id" to configuration.clientId,
                    "access_token" to authenticationManager.getToken(),
                    "x-page-number" to pageNumber?.toString(),
                    "x-page-size" to pageSize?.toString(),
                ) + headers
                ).mapNotNull { it.value },
        )
    }
}

private fun BillingResponseTO.toDomain(): AutomaticPixBill {
    return AutomaticPixBill(
        recurringPaymentId = AutomaticPixRecurringPaymentId(this.recurringPayment.id!!),
        payee = this.receiver.toDomain(),
        payer = this.debtor.toDomain(),
        endToEnd = this.endToEnd!!,
        contractNumber = this.contractNumber,
        amount = this.amount.toLongMoneyAmount(),
        settlementDate = LocalDate.parse(this.settlementDate.take(10), DateTimeFormatter.ISO_DATE),
        description = this.description,
        additionalInformation = this.additionalInformation,
    )
}

private fun RecurrenceOperationResponseTO.toDomain(): AutomaticPixRecurringPayment {
    return AutomaticPixRecurringPayment(
        recurringPaymentId = AutomaticPixRecurringPaymentId(this.id),
        mandateId = this.authorization.id?.let { AutomaticPixMandateId(it) },
        startDate = this.billing.startDate?.let { LocalDate.parse(it.take(10), DateTimeFormatter.ISO_DATE) },
        endDate = this.billing.endDate?.let { LocalDate.parse(it.take(10), DateTimeFormatter.ISO_DATE) },
        fixedAmount = this.billing.amount.toLongMoneyAmount(),
        minimumReceiverAmount = this.billing.minimumReceiverAmount.toLongMoneyAmount(),
        maximumPayerAmount = this.billing.maximumPayerAmount.toLongMoneyAmount(),
        payer = this.billing.debtor.toDomain(),
        payee = this.billing.receiver.toDomain(),
        periodicity = this.billing.periodicity.toDomain(),
        contractNumber = this.billing.contractNumber,
        description = this.billing.description,
        status = AutomaticPixRecurringPaymentStatus.APPROVED,
        provider = AutomaticPixProvider.ARBI,
        notificationsScheduling = this.billing.notificationsScheduling,
        creditLimitUsage = this.billing.creditLimitUsage,
    )
}

private fun Member.toPayerTO(accountNumber: String): PayerTO {
    return PayerTO(
        person = this.toPersonTO(),
        account = AccountTO(
            branch = ARBI_ROUTING_NO,
            number = accountNumber,
            pspInformation = PspInformationTO(
                code = ARBI_ISPB_CODE,
                name = null,
            ),

        ),

    )
}

private fun Member.toPersonTO(): PersonTO {
    return PersonTO(
        documentNumber = this.document,
        name = this.name,
        documentType = if (this.document.isValidCpf()) {
            AutomaticPixDocumentType.CPF
        } else {
            AutomaticPixDocumentType.CNPJ
        },
    )
}

private fun AutomaticPixPersonData.toPersonTO(): PersonTO {
    return PersonTO(
        documentNumber = this.documentNumber,
        name = this.name,
        documentType = this.documentType,
    )
}

private fun AutomaticPixPersonData.toReceiverTO(): ReceiverTO {
    return ReceiverTO(
        documentNumber = this.documentNumber,
        name = this.name,
        documentType = this.documentType,
        pspInformation = this.pspInformation?.let {
            PspInformationTO(
                code = it.code,
                name = it.name,
            )
        },
    )
}

data class ArbiResponseInfo(
    val requestId: String?,
    val clientRequestId: String?,
    val traceId: String?,
    val arbiResponsePageData: ArbiResponsePageData?,
)

data class ArbiResponsePageData(
    val pageNumber: Int?,
    val pageSize: Int?,
    val totalPages: Int?,
    val totalItens: Int?,
) {
    val hasNextPage: Boolean
        get() = pageNumber != null && totalPages != null && pageNumber < totalPages
}

private data class ArbiResponseWrapper<T>(
    val data: T,
    val arbiResponseInfo: ArbiResponseInfo?,
)

private fun HttpResponse<*>.extractPageData(): ArbiResponsePageData? {
    return this.headers.get("x-page-number")?.let {
        ArbiResponsePageData(
            pageNumber = it.toInt(),
            pageSize = this.headers.get("x-page-size")?.toInt(),
            totalPages = this.headers.get("x-total-page")?.toInt(),
            totalItens = this.headers.get("x-total-count")?.toInt(),
        )
    }
}

private fun HttpResponse<*>.extractResponseInfo(): ArbiResponseInfo? {
    return this.headers.get("x-request-id")?.let {
        ArbiResponseInfo(
            requestId = this.headers.get("x-request-id"),
            clientRequestId = this.headers.get("x-client-request-id"),
            traceId = this.headers.get("x-trace-id"),
            arbiResponsePageData = this.extractPageData(),
        )
    }
}

private val ONE_HUNDRED = BigDecimal("100")
fun Double.toLongMoneyAmount() = BigDecimal(this.toString()).times(ONE_HUNDRED).toLong()
private fun Long.toDoubleMoneyAmount() = BigDecimal(this).divide(ONE_HUNDRED, 2, RoundingMode.HALF_UP).toDouble()

private data class PaginatedResponse<T>(
    val items: List<T>,
    val totalItems: Int,
    val totalPages: Int,
    val currentPage: Int,
    val pageSize: Int,
)

data class BillingResponseTO(
    val receiver: PersonTO,
    val debtor: PersonTO,
    val endToEnd: String?,
    val contractNumber: String?,
    val amount: Double,
    val settlementDate: String,
    val description: String?,
    val additionalInformation: String?,
    val recurringPayment: BillingRecurringPaymentResponseTO,
)

data class PersonTO(
    val documentNumber: String?,
    val name: String?,
    val documentType: AutomaticPixDocumentType,
)

data class BillingRecurringPaymentResponseTO(
    val id: String?,
)

data class PendingMandateResponseTO(
    val id: String,
    val expirationDate: String?,
    val recurringPayment: RecurringPaymentPendingResponseTO,
    val billing: BillingPendingResponseTO,
)

data class RecurringPaymentPendingResponseTO(
    val id: String,
)

data class BillingPendingResponseTO(
    val startDate: String?,
    val endDate: String?,
    val amount: Double,
    val minimumReceiverAmount: Double,
    val maximumPayerAmount: Double,
    val receiver: ReceiverTO,
    val debtor: PersonTO,
    val contractNumber: String?,
    val periodicity: ArbiPeriodicity,
    val description: String?,
)

enum class ArbiCanceledBy {
    RECEIVER,
    PAYER,
    ;

    fun toDomain() = when (this) {
        RECEIVER -> CanceledBy.PAYEE
        PAYER -> CanceledBy.PAYER
    }
}

enum class ArbiPeriodicity {
    WEEKLY,
    MONTHLY,
    QUARTERLY,
    SEMIANNUAL,
    ANNUAL,
    ;

    fun toDomain() = when (this) {
        WEEKLY -> AutomaticPixPeriodicity.WEEKLY
        MONTHLY -> AutomaticPixPeriodicity.MONTHLY
        QUARTERLY -> AutomaticPixPeriodicity.QUARTERLY
        SEMIANNUAL -> AutomaticPixPeriodicity.SEMESTRAL
        ANNUAL -> AutomaticPixPeriodicity.YEARLY
    }

    companion object {
        fun of(periodicity: AutomaticPixPeriodicity): ArbiPeriodicity {
            return when (periodicity) {
                AutomaticPixPeriodicity.WEEKLY -> WEEKLY
                AutomaticPixPeriodicity.MONTHLY -> MONTHLY
                AutomaticPixPeriodicity.QUARTERLY -> QUARTERLY
                AutomaticPixPeriodicity.SEMESTRAL -> SEMIANNUAL
                AutomaticPixPeriodicity.YEARLY -> ANNUAL
            }
        }
    }
}

data class MandateResponseTO(
    val id: String,
    val expirationDate: String?,
    val status: AutomaticPixMandateStatus,
    val date: String?,
    val startDateValidity: String?,
    val endDateValidity: String?,
    val expiredValidityDate: String?,
    val cancellationDate: String?,
    val userCancellation: ArbiCanceledBy?,
    val recurringPayment: RecurringPaymentResponseTO,
    val billing: RecurringBillingResponseTO,
)

data class RecurringPaymentResponseTO(
    val id: String?,
)

data class RecurringBillingResponseTO(
    val receiver: ReceiverTO,
)

data class ReceiverTO(
    val documentNumber: String?,
    val name: String?,
    val documentType: AutomaticPixDocumentType,
    val pspInformation: PspInformationTO?,
)

data class PspInformationTO(
    val code: String,
    val name: String?,
)

data class RejectMandateRequestTO(
    val recurrenceId: String,
    val rejectionReason: ArbiRejectionReason,
)

data class ApproveMandateRequestTO(
    val recurrenceId: String,
    val payer: ApproveMandatePayerRequestTO,
    val maximumAmount: Double?,
)

data class ApproveMandatePayerRequestTO(
    val name: String,
    val accountType: String,
)

data class RecurrenceStatusResponseTO(
    val id: String,
    val status: AutomaticPixRecurringPaymentStatus,
    val statusDate: String,
)

data class RecurrenceOperationResponseTO(
    val id: String,
    val authorization: RecurrenceMandateResponseTO,
    val billing: RecurrenceBillingResponseTO,
)

data class RecurrenceMandateResponseTO(
    val id: String?,
)

data class RecurrenceBillingResponseTO(
    val receiver: ReceiverTO,
    val debtor: PersonTO,
    val contractNumber: String?,
    val startDate: String?,
    val endDate: String?,
    val periodicity: ArbiPeriodicity,
    val amount: Double,
    val minimumReceiverAmount: Double,
    val maximumPayerAmount: Double,
    val description: String?,
    val notificationsScheduling: Boolean?,
    val creditLimitUsage: Boolean?,
)

data class RecurrenceApproveRequestTO(
    val journey: AutomaticPixJourney,
    val createAt: String?,
    val billing: RecurrenceBillingRequestTO,
)

data class RecurrenceBillingRequestTO(
    val startDate: String,
    val endDate: String?,
    val periodicity: ArbiPeriodicity,
    val amount: Double?,
    val minimumReceiverAmount: Double?,
    val maximumPayerAmount: Double?,
    val receiver: ReceiverTO?,
    val debtor: PersonTO?,
    val payer: PayerTO?,
    val contractNumber: String,
    val description: String?,
)

data class PayerTO(
    val person: PersonTO,
    val account: AccountTO,
)

data class AccountTO(
    val branch: String?,
    val number: String?,
    val pspInformation: PspInformationTO,
)

data class RecurrenceCancelRequestTO(
    val cancellationReason: ArbiAutomaticPixRecurrenceCancellationReason,
)

data class RecurrenceUpdateRequestTO(
    val maximumPayerAmount: Double?,
    val creditLimitUsage: Boolean,
    val schedulingNotifications: Boolean,
)

data class RecurrenceUpdateResponseTO(
    val id: String?,
    val maximumPayerAmount: Double?,
    val creditLimitUsage: Boolean,
    val schedulingNotifications: Boolean,
)

fun PendingMandateResponseTO.toDomain(walletId: WalletId): AutomaticPixPendingMandate {
    return AutomaticPixPendingMandate(
        walletId = walletId,
        mandateId = AutomaticPixMandateId(id),
        expiresAt = expirationDate?.let { ZonedDateTime.parse(it) },
        recurringPayment = AutomaticPixRecurringPayment(
            recurringPaymentId = AutomaticPixRecurringPaymentId(value = recurringPayment.id),
            mandateId = AutomaticPixMandateId(id),
            startDate = billing.startDate?.let { LocalDate.parse(it.take(10), DateTimeFormatter.ISO_DATE) },
            endDate = billing.endDate?.let { LocalDate.parse(it.take(10), DateTimeFormatter.ISO_DATE) },
            fixedAmount = billing.amount.toLongMoneyAmount(),
            minimumReceiverAmount = billing.minimumReceiverAmount.toLongMoneyAmount(),
            maximumPayerAmount = billing.maximumPayerAmount.toLongMoneyAmount(),
            payee = billing.receiver.toDomain(),
            payer = billing.debtor.toDomain(),
            contractNumber = billing.contractNumber,
            periodicity = billing.periodicity.toDomain(),
            description = billing.description,
            provider = AutomaticPixProvider.ARBI,
            notificationsScheduling = null, // Not provided in PendingMandateResponseTO
            creditLimitUsage = null, // Not provided in PendingMandateResponseTO
            status = AutomaticPixRecurringPaymentStatus.CREATED,
        ),
        provider = AutomaticPixProvider.ARBI,
    )
}

private fun MandateResponseTO.toDomain(walletId: WalletId): AutomaticPixNotPendingMandate {
    return AutomaticPixNotPendingMandate(
        walletId = walletId,
        mandateId = AutomaticPixMandateId(id),
        status = status,
        recurringPayment = AutomaticPixRecurringPaymentBasicData(
            recurringPaymentId = recurringPayment.id?.let { AutomaticPixRecurringPaymentId(it) } ?: throw IllegalArgumentException("Recurring payment ID cannot be null"),
            mandateId = AutomaticPixMandateId(id),
            payee = billing.receiver.toDomain(),
            provider = AutomaticPixProvider.ARBI,
            status = AutomaticPixRecurringPaymentStatus.CREATED,
        ),
        startDateValidity = startDateValidity?.let { LocalDate.parse(it.take(10), DateTimeFormatter.ISO_DATE) },
        endDateValidity = endDateValidity?.let { LocalDate.parse(it.take(10), DateTimeFormatter.ISO_DATE) },
        expiredValidityDate = expiredValidityDate?.let { LocalDate.parse(it.take(10), DateTimeFormatter.ISO_DATE) },
        canceledAt = cancellationDate?.let { LocalDate.parse(it.take(10), DateTimeFormatter.ISO_DATE).atStartOfDay(brazilTimeZone) },
        canceledBy = userCancellation?.toDomain(),
        provider = AutomaticPixProvider.ARBI,
        expiresAt = expirationDate?.let { LocalDateTime.parse(it, DateTimeFormatter.ISO_DATE_TIME).atZone(brazilTimeZone) },
    )
}

private fun PersonTO.toDomain(): AutomaticPixPersonData {
    return AutomaticPixPersonData(
        documentNumber = documentNumber,
        name = name,
        documentType = documentType,
        pspInformation = null,
    )
}

private fun ReceiverTO.toDomain(): AutomaticPixPersonData {
    return AutomaticPixPersonData(
        documentNumber = documentNumber,
        name = name,
        documentType = documentType,
        pspInformation = pspInformation?.let {
            AutomaticPixPspInformation(
                code = pspInformation.code,
                name = pspInformation.name,
            )
        },
    )
}

enum class ArbiRejectionReason {
    AP13, // AP13 - Solicitação de confirmação da recorrência para os pagamentos periódicos rejeitada pelo usuário pagador por não reconhecimento do usuário recebedor
    AP14, // AP14 - Solicitação de confirmação da recorrência para os pagamentos periódicos rejeitada pelo usuário pagador por não ter interesse no uso do Pix Automático para o usuário recebedor
    ;

    companion object {
        fun of(clientRejectionReason: ClientRejectionReason) = when (clientRejectionReason) {
            ClientRejectionReason.UNRECOGNIZED_PAYEE -> AP13
            ClientRejectionReason.DECLINED -> AP14
        }
    }
}

enum class ArbiAutomaticPixRecurrenceCancellationReason {
    ACCL, // Cancelamento motivado por encerramento de conta - Cancelamento motivado por cancelamento da conta do usuário pagador ou do usuário recebedor
    DCSD, // Cancelamento motivado por falecimento - Cancelamento motivado por falecimento do usuário pagador
    FRUD, // Cancelamento motivado por fraude - Cancelamento motivado por suspeita de fraude
    SLDB, // Cancelamento solicitado pelo usuário pagador - Cancelamento solicitado pelo usuário pagador
    ;
    companion object {
        fun of(clientRejectionReason: AutomaticPixRecurrenceCancellationReason) = when (clientRejectionReason) {
            AutomaticPixRecurrenceCancellationReason.ACCOUNT_CLOSED -> ACCL
            AutomaticPixRecurrenceCancellationReason.DECEASED -> DCSD
            AutomaticPixRecurrenceCancellationReason.FRAUD -> FRUD
            AutomaticPixRecurrenceCancellationReason.USER_REQUEST -> SLDB
        }
    }
}