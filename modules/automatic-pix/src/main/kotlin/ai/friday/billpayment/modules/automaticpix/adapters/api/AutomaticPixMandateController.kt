package ai.friday.billpayment.modules.automaticpix.adapters.api

import ai.friday.billpayment.adapters.api.getWallet
import ai.friday.billpayment.app.account.Role.Code.OWNER
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.payment.obfuscateCPF
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.automaticpix.AutomaticPix
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixDocumentType
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateId
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixNotPendingMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPendingMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPeriodicity
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPersonData
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixProvider
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPspInformation
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPayment
import ai.friday.billpayment.modules.automaticpix.app.CanceledBy
import ai.friday.billpayment.modules.automaticpix.app.ClientRejectionReason
import ai.friday.billpayment.modules.automaticpix.app.services.AutomaticPixMandateService
import ai.friday.billpayment.modules.automaticpix.app.services.AutomaticPixMessagePublisher
import ai.friday.billpayment.modules.automaticpix.app.services.AutomaticPixRecurrenceService
import ai.friday.billpayment.modules.automaticpix.app.services.withDefaultSorting
import ai.friday.morning.log.andAppend
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(OWNER)
@AutomaticPix
@Version("2")
@Controller("/automatic-pix/mandate")
class AutomaticPixMandateController(
    private val automaticPixMandateService: AutomaticPixMandateService,
    private val automaticPixRecurrenceService: AutomaticPixRecurrenceService,
    private val automaticPixMessagePublisher: AutomaticPixMessagePublisher,
) {
    private val logger = LoggerFactory.getLogger(AutomaticPixMandateController::class.java)

    @Get
    fun getMandatesAndRecurrences(authentication: Authentication): HttpResponse<*> {
        val logName = "AutomaticPixMandateController#getMandatesAndRecurrences"
        val wallet = authentication.getWallet()
        val accountId = authentication.toAccountId()
        val markers = append("accountId", accountId.value).andAppend("walletId", wallet.id.value)

        automaticPixMessagePublisher.publishRecurreceRefresh(walletId = wallet.id)
        automaticPixMessagePublisher.publishMandateRefresh(walletId = wallet.id)
        // não deveria fazer o refresh aqui, mas via webhook. Mas por enquanto é o que temos.

        val allEntries = getAllMandatesAndAllRecurrences(wallet.id)

        logger.info(markers.andAppend("automaticPixEntries", allEntries.map { it.mandateId }).andAppend("recurringPaymentIds", allEntries.map { it.recurringPaymentId }), logName)

        return HttpResponse.ok(allEntries)
    }

    private fun getAllMandatesAndAllRecurrences(
        walletId: WalletId,
    ): List<AutomaticPixMandateOrRecurrenceResponseTO> {
        val allMandates = automaticPixMandateService.getAllStoredMandates(walletId).getOrThrow()
        val allRecurrences = automaticPixRecurrenceService.getAllStoredRecurrences(walletId).getOrThrow()

        val allRecurrencesMandatesIdsSet = allRecurrences.mapNotNull { it.mandate?.mandateId }.toSet()

        val mandatesWithoutRecurrences = allMandates.filter { it.mandateId !in allRecurrencesMandatesIdsSet }

        return mandatesWithoutRecurrences.map { it.toResponseTO() } + allRecurrences.map { it.toResponseTO() }
    }

    @Get("/all")
    fun getAllMandates(authentication: Authentication): HttpResponse<*> {
        val logName = "AutomaticPixMandateController#getAllMandates"
        val wallet = authentication.getWallet()
        val accountId = authentication.toAccountId()
        val markers = append("accountId", accountId.value).andAppend("walletId", wallet.id.value)

        val mandates = automaticPixMandateService.getAllStoredMandates(wallet.id).getOrElse {
            logger.error(markers, logName, it)
            return HttpResponse.serverError(it)
        }.withDefaultSorting().map { it.toResponseTO() }

        logger.info(markers.andAppend("mandatesAndStatus", mandates.map { it.mandateId to it.status }), logName)

        return HttpResponse.ok(mandates)
    }

    @Get("/refresh")
    fun refresh(authentication: Authentication): HttpResponse<*> {
        val logName = "AutomaticPixMandateController#refresh"
        val wallet = authentication.getWallet()
        val accountId = authentication.toAccountId()
        val markers = append("accountId", accountId.value).andAppend("walletId", wallet.id.value)

        val mandates = automaticPixMandateService.refreshMandates(wallet.id).getOrElse {
            logger.error(markers, logName, it)
            return HttpResponse.serverError(it)
        }.withDefaultSorting().map { it.toResponseTO() }

        logger.info(markers.andAppend("mandatesAndStatus", mandates.map { it.mandateId to it.status }), logName)

        return HttpResponse.ok(mandates)
    }

    @Post("/approve")
    fun approveMandate(authentication: Authentication, @Body body: ApproveMandateRequestTO): HttpResponse<*> {
        val logName = "AutomaticPixMandateController#approveMandate"
        val wallet = authentication.getWallet()
        val accountId = authentication.toAccountId()
        val markers = append("accountId", accountId.value).andAppend("walletId", wallet.id.value)

        val updated = automaticPixMandateService.approveMandate(accountId, wallet.id, AutomaticPixMandateId(body.mandateId), body.maximumAmount).getOrElse {
            logger.error(markers, logName, it)
            return HttpResponse.serverError(it)
        }.toResponseTO()

        logger.info(markers.andAppend("updated", updated), logName)

        return HttpResponse.ok(updated)
    }

    @Post("/reject")
    fun rejectMandate(authentication: Authentication, @Body body: RejectMandateRequestTO): HttpResponse<*> {
        val logName = "AutomaticPixMandateController#rejectMandate"
        val wallet = authentication.getWallet()
        val accountId = authentication.toAccountId()
        val markers = append("accountId", accountId.value).andAppend("walletId", wallet.id.value)

        val updated = automaticPixMandateService.rejectMandate(accountId, wallet.id, AutomaticPixMandateId(body.mandateId), body.rejectionReason).getOrElse {
            logger.error(markers, logName, it)
            return HttpResponse.serverError(it)
        }.toResponseTO()

        logger.info(markers.andAppend("updated", updated), logName)

        return HttpResponse.ok(updated)
    }
}

data class ApproveMandateRequestTO(
    val mandateId: String,
    val maximumAmount: Long?,
)

data class RejectMandateRequestTO(
    val mandateId: String,
    val rejectionReason: ClientRejectionReason,
)

fun AutomaticPixPersonData.toResponseTO(): PayeeOrPayerResponseTO {
    return PayeeOrPayerResponseTO(
        document = obfuscateCPF(this.documentNumber),
        name = this.name,
        documentType = this.documentType,
        pspInformation = this.pspInformation?.toResponseTO(),
    )
}

private fun AutomaticPixPspInformation.toResponseTO(): PspInformationResponseTO {
    return PspInformationResponseTO(
        code = this.code,
        name = this.name,
    )
}

fun AutomaticPixMandate.toResponseTO(): AutomaticPixMandateOrRecurrenceResponseTO {
    val completeRecurrenceData = this.recurringPayment as? AutomaticPixRecurringPayment
    val notPendingMandate = this as? AutomaticPixNotPendingMandate
    val pendingMandate = this as? AutomaticPixPendingMandate

    return AutomaticPixMandateOrRecurrenceResponseTO(
        mandateId = mandateId.value,
        status = status.name,
        recurringPaymentId = recurringPayment.recurringPaymentId.value,
        payee = recurringPayment.payee.toResponseTO(),
        payer = completeRecurrenceData?.payer?.toResponseTO(),
        contract = ContractResponseTO(
            id = completeRecurrenceData?.contractNumber,
            subject = completeRecurrenceData?.description,
        ),
        billingCycle = BillingCycleResponseTO(
            frequency = completeRecurrenceData?.periodicity,
            startsAt = completeRecurrenceData?.startDate?.format(DateTimeFormatter.ISO_DATE),
            endsAt = completeRecurrenceData?.endDate?.format(DateTimeFormatter.ISO_DATE),
        ),
        amount = MandateAmountResponseTO(
            min = completeRecurrenceData?.minimumReceiverAmount,
            max = completeRecurrenceData?.maximumPayerAmount,
            base = completeRecurrenceData?.fixedAmount,
        ),
        activatedAt = notPendingMandate?.startDateValidity?.format(DateTimeFormatter.ISO_DATE),
        canceledAt = notPendingMandate?.canceledAt?.format(DateTimeFormatter.ISO_DATE_TIME),
        expiresAt = pendingMandate?.expiresAt?.format(DateTimeFormatter.ISO_DATE_TIME) ?: notPendingMandate?.expiresAt?.format(DateTimeFormatter.ISO_DATE_TIME),
        canceledBy = notPendingMandate?.canceledBy,

        endDateValidity = notPendingMandate?.endDateValidity?.format(DateTimeFormatter.ISO_DATE),
        retryType = recurringPayment.checkRetryType().name,
        provider = provider,
        notificationsScheduling = completeRecurrenceData?.notificationsScheduling,
        creditLimitUsage = completeRecurrenceData?.creditLimitUsage,
    )
}

data class AutomaticPixMandateOrRecurrenceResponseTO(
    val mandateId: String?,
    val recurringPaymentId: String,
    val status: String,
    val payee: PayeeOrPayerResponseTO,
    val payer: PayeeOrPayerResponseTO?,
    val contract: ContractResponseTO,
    val billingCycle: BillingCycleResponseTO,
    val amount: MandateAmountResponseTO,
    val activatedAt: String?,
    val canceledAt: String?,
    val expiresAt: String?,
    val canceledBy: CanceledBy?,

    val endDateValidity: String?,
    val retryType: String,
    val provider: AutomaticPixProvider,
    val notificationsScheduling: Boolean?,
    val creditLimitUsage: Boolean?,
)

data class MandateAmountResponseTO(
    val min: Long?,
    val max: Long?,
    val base: Long?,
)

data class ContractResponseTO(
    val id: String?,
    val subject: String?,
)

data class BillingCycleResponseTO(
    val frequency: AutomaticPixPeriodicity?,
    val startsAt: String?,
    val endsAt: String?,
)

data class PayeeOrPayerResponseTO(
    val document: String?,
    val name: String?,
    val documentType: AutomaticPixDocumentType,
    val pspInformation: PspInformationResponseTO?,
)

data class PspInformationResponseTO(
    val code: String,
    val name: String?,
)