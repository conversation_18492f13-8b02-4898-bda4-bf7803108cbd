package ai.friday.billpayment.modules.automaticpix.adapters.arbi

import ai.friday.billpayment.adapters.arbi.ArbiDadosRecorrenciaTO
import ai.friday.billpayment.app.pix.AutomaticPixQrCodeRecurringDataExtractor
import ai.friday.billpayment.modules.automaticpix.AutomaticPix
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixDocumentType
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixJourney
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPeriodicity
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPersonData
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixProvider
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPspInformation
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixQrCodeRecurringData
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentId
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRetryType
import ai.friday.morning.json.getObjectMapper
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import org.slf4j.LoggerFactory

@AutomaticPix
class ArbiAutomaticAutomaticPixQrCodeRecurringDataExtractor : AutomaticPixQrCodeRecurringDataExtractor {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun extractQRCodeRecurringData(rawData: Any): Result<AutomaticPixQrCodeRecurringData?> = runCatching {
        if (rawData !is ArbiDadosRecorrenciaTO) {
            logger.error("Invalid data type: ${rawData::class.java.name}. Expected ArbiDadosRecorrenciaTO.")
            throw IllegalArgumentException("Invalid data type: ${rawData::class.java.name}. Expected QRCodeDetailsResponseTO.")
        }

        val pixQrCodeCompoundData = AutomaticPixQrCodeRecurringData(
            recurringPaymentId = AutomaticPixRecurringPaymentId(rawData.idRec),
            journey = ArbiQRCodeJourney.valueOf(rawData.jornada).toDomain(),
            retryType = ArbiAutomaticPixRetryType.valueOf(rawData.retentativa).toDomain(),
            fixedAmount = rawData.valorRec?.toLongMoneyAmount(),
            minimumReceiverAmount = rawData.valorMinimo?.toLongMoneyAmount(),
            payee = AutomaticPixPersonData(
                documentNumber = rawData.cnpjRecebedor,
                name = rawData.nomeRecebedor,
                documentType = AutomaticPixDocumentType.CNPJ,
                pspInformation = AutomaticPixPspInformation(
                    code = rawData.ispbRecebedor,
                    name = null,
                ),
            ),
            payer = AutomaticPixPersonData(
                documentNumber = rawData.cpfPagador ?: rawData.cnpjPagador,
                name = rawData.nomePagador,
                documentType = rawData.cpfPagador?.let { AutomaticPixDocumentType.CPF } ?: AutomaticPixDocumentType.CNPJ,
                pspInformation = null,
            ),
            startDate = LocalDate.parse(rawData.calendarioInicial, DateTimeFormatter.ISO_DATE),
            endDate = rawData.calendarioFinal?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) },
            periodicity = ArbiQRCodePeriodicity.valueOf(rawData.periodicidade).toDomain(),
            description = rawData.objetoVinculo,
            contractNumber = rawData.contratoVinculo,
            provider = AutomaticPixProvider.ARBI,
            updates = rawData.atualizacao.map { getObjectMapper().writeValueAsString(it) },
        )

        pixQrCodeCompoundData
    }
}

enum class ArbiQRCodeJourney {
    JORNADA_1,
    JORNADA_2,
    JORNADA_3,
    JORNADA_4,
    ;

    fun toDomain(): AutomaticPixJourney {
        return when (this) {
            JORNADA_1 -> AutomaticPixJourney.JOURNEY_1
            JORNADA_2 -> AutomaticPixJourney.JOURNEY_2
            JORNADA_3 -> AutomaticPixJourney.JOURNEY_3
            JORNADA_4 -> AutomaticPixJourney.JOURNEY_4
        }
    }
}

enum class ArbiQRCodePeriodicity {
    SEMANAL,
    MENSAL,
    TRIMESTRAL,
    SEMESTRAL,
    ANUAL,
    ;

    fun toDomain(): AutomaticPixPeriodicity {
        return when (this) {
            SEMANAL -> AutomaticPixPeriodicity.WEEKLY
            MENSAL -> AutomaticPixPeriodicity.MONTHLY
            TRIMESTRAL -> AutomaticPixPeriodicity.QUARTERLY
            SEMESTRAL -> AutomaticPixPeriodicity.SEMESTRAL
            ANUAL -> AutomaticPixPeriodicity.YEARLY
        }
    }
}

enum class ArbiAutomaticPixRetryType {
    NAO_PERMITE,
    PERMITE_3R_7D,
    ;

    fun toDomain(): AutomaticPixRetryType {
        return when (this) {
            PERMITE_3R_7D -> AutomaticPixRetryType.ALLOWED_3R_7D
            NAO_PERMITE -> AutomaticPixRetryType.NOT_ALLOWED
        }
    }
}