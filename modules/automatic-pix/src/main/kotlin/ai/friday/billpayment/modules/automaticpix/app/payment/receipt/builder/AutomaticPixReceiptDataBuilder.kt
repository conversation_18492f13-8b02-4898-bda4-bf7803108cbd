package ai.friday.billpayment.modules.automaticpix.app.payment.receipt.builder

import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.payment.AutomaticPixReceiptData
import ai.friday.billpayment.app.payment.receipt.builder.PixReceiptDataBuilder
import ai.friday.billpayment.app.payment.receipt.builder.ReceiptDataBuilder
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.modules.automaticpix.AutomaticPix
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixBillRepository
import ai.friday.morning.log.andAppend
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@AutomaticPix
class AutomaticPixReceiptDataBuilder(
    private val pixReceiptDataBuilder: PixReceiptDataBuilder,
    private val automaticPixBillRepository: AutomaticPixBillRepository,
) : ReceiptDataBuilder {
    override val buildFor: List<BillType> = listOf(BillType.AUTOMATIC_PIX)

    private val logger = LoggerFactory.getLogger(AutomaticPixReceiptDataBuilder::class.java)

    override fun build(bill: Bill, billPaid: BillPaid, wallet: Wallet): AutomaticPixReceiptData {
        val markers = Markers.append("walletId", wallet.id).andAppend("billId", bill.billId)
        try {
            val pixReceiptData = pixReceiptDataBuilder.build(bill, billPaid, wallet) // TODO - talvez os dados não estejam todos aqui e tenhamos que buscar na bill do pix automatico (AutomaticPixBillingDynamoDBRepository)
            val automaticPixBillOnWallet = automaticPixBillRepository.findByBillId(bill.billId, bill.walletId)

            return AutomaticPixReceiptData(
                pixReceiptData = pixReceiptData,
                contractNumber = automaticPixBillOnWallet?.automaticPixBill?.contractNumber,
                automaticPixDescription = automaticPixBillOnWallet?.automaticPixBill?.description,
            )
        } catch (e: Exception) {
            logger.error(markers, "AutomaticPixReceiptDataBuilder#build", e)
            throw e
        }
    }
}