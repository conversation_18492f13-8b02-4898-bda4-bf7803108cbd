package ai.friday.billpayment.modules.automaticpix.adapters.api

import ai.friday.billpayment.adapters.api.getWallet
import ai.friday.billpayment.app.account.Role.Code.OWNER
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.modules.automaticpix.AutomaticPix
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixBill
import ai.friday.billpayment.modules.automaticpix.app.services.AutomaticPixBillingService
import ai.friday.morning.log.andAppend
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import java.time.LocalDate
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(OWNER)
@AutomaticPix
@Version("2")
@Controller("/automatic-pix/billing")
class AutomaticPixBillingController(
    private val automaticPixBillingService: AutomaticPixBillingService,
) {
    private val logger = LoggerFactory.getLogger(AutomaticPixMandateController::class.java)

    @Get("/all")
    fun getAllStoredBills(authentication: Authentication): HttpResponse<*> {
        val logName = "AutomaticPixBillingController#getAllStoredBills"
        val wallet = authentication.getWallet()
        val markers = append("accountId", authentication.toAccountId().value).andAppend("walletId", wallet.id.value)

        val bills = automaticPixBillingService.getAllStoredBills(walletId = wallet.id).getOrElse {
            logger.error(markers, logName, it)
            return HttpResponse.serverError(it)
        }.map { it.automaticPixBill.toResponseTO() }

        logger.info(markers.andAppend("billsEndToEnd", bills.map { it.recurringPaymentId to it.endToEnd }), logName)

        return HttpResponse.ok(bills)
    }

    @Get("/refresh")
    fun refresh(authentication: Authentication): HttpResponse<*> {
        val logName = "AutomaticPixBillingController#refresh"
        val wallet = authentication.getWallet()
        val markers = append("accountId", authentication.toAccountId().value).andAppend("walletId", wallet.id.value)

        val bills = automaticPixBillingService.refreshBilling(walletId = wallet.id).getOrElse {
            logger.error(markers, logName, it)
            return HttpResponse.serverError(it)
        }.map { it.automaticPixBill.toResponseTO() }

        logger.info(markers.andAppend("billsEndToEnd", bills.map { it.recurringPaymentId to it.endToEnd }), logName)

        return HttpResponse.ok(bills)
    }

    @Post("/cancel")
    fun cancel(authentication: Authentication, @Body body: AutomaticPixCancelBillRequestTO): HttpResponse<*> {
        val logName = "AutomaticPixBillingController#cancel"
        val wallet = authentication.getWallet()
        val markers = append("accountId", authentication.toAccountId().value).andAppend("walletId", wallet.id.value).andAppend("requestBody", body)

        automaticPixBillingService.cancelBilling(walletId = wallet.id, endToEnd = body.endToEnd).getOrElse {
            if (it is IllegalStateException) {
                logger.warn(markers, logName, it)
                return HttpResponse.badRequest(it)
            }
            logger.error(markers, logName, it)
            return HttpResponse.serverError(it)
        }
        return HttpResponse.noContent<Unit>()
    }
}

private fun AutomaticPixBill.toResponseTO(): AutomaticPixBillResponseTO {
    return AutomaticPixBillResponseTO(
        recurringPaymentId = this.recurringPaymentId.value,
        payee = this.payee.toResponseTO(),
        payer = this.payer.toResponseTO(),
        endToEnd = this.endToEnd,
        contractNumber = this.contractNumber,
        amount = this.amount,
        settlementDate = this.settlementDate,
        description = this.description,
        additionalInformation = this.additionalInformation,
    )
}

data class AutomaticPixBillResponseTO(
    val recurringPaymentId: String,
    val payee: PayeeOrPayerResponseTO,
    val payer: PayeeOrPayerResponseTO,
    val endToEnd: String,
    val contractNumber: String?,
    val amount: Long,
    val settlementDate: LocalDate,
    val description: String? = null,
    val additionalInformation: String?,
)

data class AutomaticPixCancelBillRequestTO(
    val endToEnd: String,
)