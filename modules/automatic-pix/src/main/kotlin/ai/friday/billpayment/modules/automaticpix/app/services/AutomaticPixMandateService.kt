package ai.friday.billpayment.modules.automaticpix.app.services

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.canScheduleAny
import ai.friday.billpayment.modules.automaticpix.AutomaticPix
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateId
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateRepository
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateStatus
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixNotPendingMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPendingMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRemoteManager
import ai.friday.billpayment.modules.automaticpix.app.ClientRejectionReason
import ai.friday.morning.log.andAppend
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@AutomaticPix
class AutomaticPixMandateService(
    private val automaticPixRemoteManager: AutomaticPixRemoteManager,
    private val automaticPixMandateRepository: AutomaticPixMandateRepository,
    private val walletService: WalletService,
    private val accountService: AccountService,
    private val automaticPixMessagePublisher: AutomaticPixMessagePublisher,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun getAllStoredMandates(walletId: WalletId): Result<List<AutomaticPixMandate>> = runCatching {
        automaticPixMandateRepository.findByWalletId(walletId, null)
    }

    fun refreshMandates(walletId: WalletId): Result<List<AutomaticPixMandate>> = runCatching {
        val logName = "AutomaticPixMandateService#refreshMandates"
        val wallet = walletService.findWallet(walletId)
        val bankAccount = accountService.findAccountPaymentMethodByIdAndAccountId(wallet.paymentMethodId, wallet.founder.accountId).method as? InternalBankAccount ?: throw IllegalStateException("método de pagamento da carteira não é um InternalBankAccount")

        val localMandates = automaticPixMandateRepository.findByWalletId(walletId = walletId, null)
        val localMandatesIds = localMandates.associateBy { it.mandateId }

        val remoteNotPendingMandates = automaticPixRemoteManager.getMandates(walletId, bankAccount.buildFullAccountNumber(), null).getOrThrow().filter { it.status != AutomaticPixMandateStatus.PENDING }
            .map { remoteNotPending ->
                automaticPixMandateRepository.save(automaticPixMandate = remoteNotPending).also {
                    val localMandate = localMandatesIds[remoteNotPending.mandateId]
                    if (localMandate == null || localMandate.status != remoteNotPending.status) {
                        automaticPixMessagePublisher.notifyMandateStatusChange(remoteNotPending, remoteNotPending.status)
                    }
                }
            }

        val remotePendingMandates = automaticPixRemoteManager.getPendingMandates(walletId, bankAccount.buildFullAccountNumber()).getOrThrow()
        remotePendingMandates.map { remotePending ->
            automaticPixMandateRepository.save(automaticPixMandate = remotePending).also {
                if (localMandatesIds[remotePending.mandateId] == null) {
                    automaticPixMessagePublisher.notifyMandatePending(remotePending)
                }
            }
        }

        val remoteMandateIds = remotePendingMandates.associateBy { it.mandateId } + remoteNotPendingMandates.associateBy { it.mandateId }

        localMandates.filter { it.mandateId !in remoteMandateIds }.forEach { localMandate ->
            logger.error(append("context", "mandate local nao encontrado no remoto").andAppend("mandateId", localMandate.mandateId.value).andAppend("walletId", walletId.value), logName)
            automaticPixMandateRepository.updateStatus(automaticPixMandate = localMandate, status = AutomaticPixMandateStatus.CANCELED).also {
                automaticPixMessagePublisher.notifyMandateStatusChange(localMandate, AutomaticPixMandateStatus.CANCELED)
            }
        }

        val allRemoteMandates = remotePendingMandates + remoteNotPendingMandates
        allRemoteMandates
    }

    fun rejectMandate(accountId: AccountId, walletId: WalletId, automaticPixMandateId: AutomaticPixMandateId, clientRejectionReason: ClientRejectionReason): Result<AutomaticPixMandate> = runCatching {
        val walletMandate = automaticPixMandateRepository.findByMandateIdAndWalletId(automaticPixMandateId, walletId) ?: throw IllegalArgumentException("A autorização não encontrada.")

        if (walletId != walletMandate.walletId) {
            throw IllegalArgumentException("A autorização não pertence à carteira informada.")
        }

        if (!hasPermission(accountId, walletId)) {
            throw IllegalStateException("Usuário não tem permissão para agendar pagamentos na carteira")
        }

        automaticPixRemoteManager.rejectMandate(walletMandate.mandateId, walletMandate.recurringPayment.recurringPaymentId, clientRejectionReason).getOrThrow()
        refreshAndReturnMandate(walletId, automaticPixMandateId)
    }

    private fun refreshAndReturnMandate(walletId: WalletId, automaticPixMandateId: AutomaticPixMandateId): AutomaticPixMandate {
        refreshMandates(walletId).getOrThrow()
        automaticPixMessagePublisher.publishRecurreceRefresh(walletId)
        return automaticPixMandateRepository.findByMandateIdAndWalletId(automaticPixMandateId, walletId) ?: throw IllegalStateException("A autorização não foi encontrada")
    }

    fun approveMandate(accountId: AccountId, walletId: WalletId, automaticPixMandateId: AutomaticPixMandateId, maximumAmount: Long?): Result<AutomaticPixMandate> = runCatching {
        val walletMandate = automaticPixMandateRepository.findByMandateIdAndWalletId(automaticPixMandateId, walletId) ?: throw IllegalArgumentException("A autorização não encontrada.")

        if (walletId != walletMandate.walletId) {
            throw IllegalArgumentException("A autorização não pertence à carteira informada.")
        }

        if (!hasPermission(accountId, walletId)) {
            throw IllegalStateException("Usuário não tem permissão para agendar pagamentos na carteira")
        }

        val wallet = walletService.findWallet(walletId)

        automaticPixRemoteManager.approveMandate(walletMandate.mandateId, walletMandate.recurringPayment.recurringPaymentId, wallet.founder.name, maximumAmount).getOrThrow()
        refreshAndReturnMandate(walletId, automaticPixMandateId)
    }

    private fun hasPermission(accountId: AccountId, walletId: WalletId): Boolean {
        val wallet = walletService.findWallet(walletId)
        return hasPermission(accountId, wallet)
    }

    private fun hasPermission(accountId: AccountId, wallet: Wallet): Boolean {
        val walletMember = wallet.getActiveMember(accountId)
        return walletMember.canScheduleAny()
    }
}

fun List<AutomaticPixMandate>.withDefaultSorting() = this.sortedWith(
    compareByDescending<AutomaticPixMandate> {
        when (it.status) {
            AutomaticPixMandateStatus.PENDING -> 0
            AutomaticPixMandateStatus.PROCESSING -> 1
            AutomaticPixMandateStatus.ACTIVE -> 2
            AutomaticPixMandateStatus.EXPIRED -> 3
            AutomaticPixMandateStatus.CANCELED -> 4
        }
    }.thenBy {
        when (it) {
            is AutomaticPixNotPendingMandate -> it.expiredValidityDate ?: it.endDateValidity
            is AutomaticPixPendingMandate -> it.expiresAt
        }
    },
)