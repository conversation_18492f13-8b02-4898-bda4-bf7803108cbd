package ai.friday.billpayment.modules.automaticpix.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.AbstractBillPaymentDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.GlobalSecondaryIndexNames
import ai.friday.billpayment.adapters.dynamodb.INDEX_1
import ai.friday.billpayment.adapters.dynamodb.INDEX_2
import ai.friday.billpayment.adapters.dynamodb.INDEX_3
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.automaticpix.AutomaticPix
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixBill
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixBillOnWallet
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixBillRepository
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPersonData
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentId
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@AutomaticPix
class AutomaticPixBillingDynamoDAO(
    cli: DynamoDbEnhancedClient,
) : AbstractBillPaymentDynamoDAO<AutomaticPixBillingEntity>(cli, AutomaticPixBillingEntity::class.java)

private const val SCAN_KEY = "AUTOMATIC_PIX_BILLING"

@AutomaticPix
class AutomaticPixBillingDynamoDBRepository(
    private val dynamoDAO: AutomaticPixBillingDynamoDAO,
) : AutomaticPixBillRepository {
    override fun save(automaticPixBill: AutomaticPixBill, walletId: WalletId, billId: BillId): AutomaticPixBillOnWallet {
        val (primaryKey, scanKey) = buildKey(automaticPixBill, walletId)

        val entity = AutomaticPixBillingEntity().apply {
            this.primaryKey = primaryKey
            this.scanKey = scanKey
            gSIndex1PartitionKey = automaticPixBill.recurringPaymentId.value
            gSIndex1RangeKey = "$SCAN_KEY#${walletId.value}#${automaticPixBill.settlementDate.format(DateTimeFormatter.ISO_DATE)}"
            gSIndex2PartitionKey = walletId.value
            gSIndex2RangeKey = "$SCAN_KEY#${automaticPixBill.settlementDate.format(DateTimeFormatter.ISO_DATE)}"
            gSIndex3PartitionKey = billId.value
            gSIndex3RangeKey = "$SCAN_KEY#${walletId.value}"
            this.walletId = walletId.value
            this.billId = billId.value
            recurringPaymentId = automaticPixBill.recurringPaymentId.value
            endToEnd = automaticPixBill.endToEnd
            payee = automaticPixBill.payee.toEntity()
            payer = automaticPixBill.payer.toEntity()
            contractNumber = automaticPixBill.contractNumber
            amount = automaticPixBill.amount
            settlementDate = automaticPixBill.settlementDate.format(DateTimeFormatter.ISO_DATE)
            description = automaticPixBill.description
            additionalInformation = automaticPixBill.additionalInformation
            updatedAt = ZonedDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME)
            createdAt = ZonedDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME)
            removed = false
        }
        dynamoDAO.save(entity)
        return entity.toDomain()
    }

    override fun remove(automaticPixBill: AutomaticPixBill, walletId: WalletId) {
        dynamoDAO.findByPrimaryKey(automaticPixBill.endToEnd, "$SCAN_KEY#${walletId.value}")?.let { entity ->

            dynamoDAO.save(
                entity.apply {
                    removed = true
                    updatedAt = ZonedDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME)
                },
            )
        }
    }

    override fun findByEndToEnd(endToEnd: String): List<AutomaticPixBillOnWallet> {
        return dynamoDAO.findBeginsWithOnSortKey(endToEnd, "$SCAN_KEY#").mapNotNull {
            if (it.removed == true) {
                null
            } else {
                it.toDomain()
            }
        }
    }

    override fun findByEndToEnd(endToEnd: String, walletId: WalletId): AutomaticPixBillOnWallet? {
        return dynamoDAO.findByPrimaryKey(endToEnd, "$SCAN_KEY#${walletId.value}")?.let {
            if (it.removed == true) {
                null
            } else {
                it.toDomain()
            }
        }
    }

    override fun findByRecurringPaymentId(recurringPaymentId: AutomaticPixRecurringPaymentId, walletId: WalletId): List<AutomaticPixBillOnWallet> {
        return dynamoDAO.findBeginsWithOnIndex(GlobalSecondaryIndexNames.GSIndex1, recurringPaymentId.value, "$SCAN_KEY#${walletId.value}#")
            .filter { it.removed == false }
            .map { it.toDomain() }
    }

    override fun findByWalletId(walletId: WalletId): List<AutomaticPixBillOnWallet> {
        return dynamoDAO.findBeginsWithOnIndex(GlobalSecondaryIndexNames.GSIndex2, walletId.value, "$SCAN_KEY#")
            .filter { it.removed == false }
            .map { it.toDomain() }
    }

    override fun findByBillId(billId: BillId, walletId: WalletId): AutomaticPixBillOnWallet? {
        return dynamoDAO.findBeginsWithOnIndex(GlobalSecondaryIndexNames.GSIndex3, billId.value, "$SCAN_KEY#${walletId.value}")
            .filter { it.removed == false }.firstOrNull()?.let { it.toDomain() }
    }

    private fun buildKey(automaticPixBill: AutomaticPixBill, walletId: WalletId): Pair<String, String> =
        automaticPixBill.endToEnd to "$SCAN_KEY#${walletId.value}"
}

private fun AutomaticPixPersonData.toEntity(): AutomaticPixPersonDataEntity {
    val domain = this
    return AutomaticPixPersonDataEntity().apply {
        documentNumber = domain.documentNumber
        name = domain.name
        documentType = domain.documentType.name
        pspInformationCode = domain.pspInformation?.code
        pspInformationName = domain.pspInformation?.name
    }
}

private fun AutomaticPixBillingEntity.toDomain(): AutomaticPixBillOnWallet {
    return AutomaticPixBillOnWallet(
        walletId = WalletId(<EMAIL>),
        billId = BillId(billId),
        automaticPixBill = AutomaticPixBill(
            recurringPaymentId = AutomaticPixRecurringPaymentId(recurringPaymentId),
            endToEnd = endToEnd,
            payee = payee.toDomain(),
            payer = payer.toDomain(),
            contractNumber = contractNumber,
            amount = amount,
            settlementDate = LocalDate.parse(settlementDate, DateTimeFormatter.ISO_DATE),
            description = description,
            additionalInformation = additionalInformation,
        ),
    )
}

@DynamoDbBean
class AutomaticPixBillingEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1RangeKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    lateinit var gSIndex2PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    lateinit var gSIndex2RangeKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_3])
    @get:DynamoDbAttribute(value = "GSIndex3PrimaryKey")
    lateinit var gSIndex3PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_3])
    @get:DynamoDbAttribute(value = "GSIndex3ScanKey")
    lateinit var gSIndex3RangeKey: String

    @get:DynamoDbAttribute("WalletId")
    lateinit var walletId: String

    @get:DynamoDbAttribute("RecurringPaymentId")
    lateinit var recurringPaymentId: String

    @get:DynamoDbAttribute("EndToEnd")
    lateinit var endToEnd: String

    @get:DynamoDbAttribute("BillId")
    lateinit var billId: String

    @get:DynamoDbAttribute("Payee")
    lateinit var payee: AutomaticPixPersonDataEntity

    @get:DynamoDbAttribute("Payer")
    lateinit var payer: AutomaticPixPersonDataEntity

    @get:DynamoDbAttribute("ContractNumber")
    var contractNumber: String? = null

    @get:DynamoDbAttribute("Amount")
    var amount: Long = 0

    @get:DynamoDbAttribute("SettlementDate")
    lateinit var settlementDate: String

    @get:DynamoDbAttribute("Description")
    var description: String? = null

    @get:DynamoDbAttribute("AdditionalInformation")
    var additionalInformation: String? = null

    @get:DynamoDbAttribute("CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute("Removed")
    var removed: Boolean? = false

    @get:DynamoDbAttribute("UpdatedAt")
    lateinit var updatedAt: String
}