package ai.friday.billpayment.modules.automaticpix.adapters.api

import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.modules.automaticpix.AutomaticPix
import ai.friday.billpayment.modules.automaticpix.adapters.arbi.ArbiAutomaticPixAdapter
import io.micronaut.http.HttpMethod
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured

data class AutomaticPixProxyRequest(
    val method: String,
    val path: String,
    val body: Map<String, Any>? = null,
    val qs: Map<String, String>? = null,
    val headers: Map<String, String>? = null,
)

@Controller("/automatic-pix/test/arbi")
@Secured(BACKOFFICE)
@AutomaticPix
class AutomaticPixTestController(
    private val arbiAutomaticPixAdapter: ArbiAutomaticPixAdapter,
) {

    @Post("proxy")
    fun proxyRequest(
        @Body body: AutomaticPixProxyRequest,
    ): HttpResponse<*> {
        return arbiAutomaticPixAdapter.proxyCall(
            path = body.path,
            method = HttpMethod.valueOf(body.method),
            body = body.body,
            headers = body.headers,
            qs = body.qs,
        ).fold(
            onSuccess = { HttpResponse.ok(it) },
            onFailure = { HttpResponse.serverError(it) },
        )
    }
}