package ai.friday.billpayment.modules.automaticpix.adapters.messaging

import ai.friday.billpayment.adapters.messaging.Handler
import ai.friday.billpayment.adapters.messaging.parseEvent
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.modules.automaticpix.AutomaticPix
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixQrCodeRecurringData
import ai.friday.billpayment.modules.automaticpix.app.services.AutomaticPixRecurrenceService
import ai.friday.morning.log.andAppend
import ai.friday.morning.messaging.MessageHandler
import ai.friday.morning.messaging.MessageHandlerResponse
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

@Handler
@AutomaticPix
open class AutomaticPixPaidApproveRecurrenceHandler(
    private val automaticPixRecurrenceService: AutomaticPixRecurrenceService,
    private val findBillService: FindBillService,
    private val walletService: WalletService,

) : MessageHandler {
    private val logger = LoggerFactory.getLogger(AutomaticPixPaidApproveRecurrenceHandler::class.java)

    override val configurationName = "automatic-pix-approve-recurrence"

    override fun handleMessage(message: Message): MessageHandlerResponse {
        val logName = "AutomaticPixPaidApproveRecurrenceHandler"
        val markers = Markers.empty()

        val billEvent = parseEvent(message)
        if (billEvent !is BillPaid) {
            return MessageHandlerResponse.delete()
        }

        val bill = findBillService.find(billId = billEvent.billId, walletId = billEvent.walletId)

        val automaticPixRecurringDataJson = bill.pixQrCodeData?.automaticPixRecurringDataJson
        if (bill.billType != BillType.PIX || automaticPixRecurringDataJson == null) {
            return MessageHandlerResponse.delete()
        }
        val automaticPixRecurringData = parseObjectFrom<AutomaticPixQrCodeRecurringData>(automaticPixRecurringDataJson)
        if (!automaticPixRecurringData.journey.approveAutomaticallyAfterPayment()) {
            return MessageHandlerResponse.delete()
        }

        val wallet = walletService.findWallet(bill.walletId)

        markers.andAppend("billId", bill.billId.value)
            .andAppend("walletId", bill.walletId.value)
            .andAppend("billType", bill.billType.name)

        automaticPixRecurrenceService.approveRecurrence(bill.billId, accountId = wallet.founder.accountId, walletId = bill.walletId, automaticApprove = true, maximumAmount = bill.automaticPixAuthorizationMaximumAmount)
            .onFailure {
                logger.error(markers, logName, it)
            }.onSuccess {
                logger.info(markers, logName)
            }.getOrThrow()

        return MessageHandlerResponse.delete()
    }

    override fun handleException(e: Exception): MessageHandlerResponse {
        return MessageHandlerResponse.keep()
    }
}