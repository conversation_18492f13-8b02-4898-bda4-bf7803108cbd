package ai.friday.billpayment.modules.automaticpix.adapters.messaging

import ai.friday.billpayment.adapters.messaging.Handler
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.modules.automaticpix.AutomaticPix
import ai.friday.billpayment.modules.automaticpix.adapters.api.ParsedCallbackData
import ai.friday.billpayment.modules.automaticpix.adapters.api.PixParticipantCallbackData
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixBillRepository
import ai.friday.billpayment.modules.automaticpix.app.services.AutomaticPixBillingService
import ai.friday.billpayment.modules.automaticpix.app.services.AutomaticPixMessagePublisher
import ai.friday.billpayment.modules.automaticpix.app.services.ManageAutomaticPixPaymentCommand
import ai.friday.morning.json.parseObjectFrom
import ai.friday.morning.log.andAppend
import ai.friday.morning.messaging.MessageHandler
import ai.friday.morning.messaging.MessageHandlerResponse
import arrow.core.getOrElse
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

@Handler
@AutomaticPix
open class AutomaticPixCallbackHandler(
    private val automaticPixBillRepository: AutomaticPixBillRepository,
    private val automaticPixMessagePublisher: AutomaticPixMessagePublisher,
    private val accountService: AccountService,
    private val walletService: WalletService,
    private val automaticPixBillingService: AutomaticPixBillingService,
) : MessageHandler {
    private val logger = LoggerFactory.getLogger(AutomaticPixCallbackHandler::class.java)

    override val configurationName = "automatic-pix-callback"

    override fun handleMessage(message: Message): MessageHandlerResponse {
        val logName = "AutomaticPixCallbackHandler#handleMessage"
        val markers = Markers.empty()

        val parsedCallbackData = parseObjectFrom<ParsedCallbackData>(message.body())
        markers.andAppend("parsedCallbackData", parsedCallbackData)

        val shouldDelete = when (parsedCallbackData) {
            is ParsedCallbackData.MandateAndRecurrenceLifecycleCallback -> {
                handleMandateAndRecurrenceLifecycle(parsedCallbackData)
            }

            is ParsedCallbackData.BillingLifecycleCallback -> {
                handleBillingLifecycle(parsedCallbackData)
            }

            is ParsedCallbackData.PaymentLifecycleCallback -> {
                handlePaymentLifecycle(parsedCallbackData)
            }
        }.onFailure {
            logger.error(markers, logName, it)
        }.onSuccess {
            logger.info(markers, logName)
        }.isSuccess

        markers.andAppend("shouldDeleteMessage", shouldDelete)

        return MessageHandlerResponse.build(shouldDeleteMessage = shouldDelete)
    }

    private fun handleMandateAndRecurrenceLifecycle(callback: ParsedCallbackData.MandateAndRecurrenceLifecycleCallback): Result<Unit> = runCatching {
        val walletId = discoverWallet(callback.payer).getOrThrow()
        automaticPixMessagePublisher.publishMandateRefresh(walletId)
        automaticPixMessagePublisher.publishRecurreceRefresh(walletId)
    }

    private fun handleBillingLifecycle(callback: ParsedCallbackData.BillingLifecycleCallback): Result<Unit> = runCatching {
        val walletId = discoverWallet(callback.payer).getOrThrow()
        automaticPixMessagePublisher.publishBillingRefresh(walletId)
    }

    private fun handlePaymentLifecycle(callback: ParsedCallbackData.PaymentLifecycleCallback): Result<Unit> = runCatching {
        val logName = "AutomaticPixCallbackHandler#handlePaymentLifecycle"
        val bills = automaticPixBillRepository.findByEndToEnd(callback.endToEnd)
        if (bills.isEmpty()) {
            return Result.failure(IllegalStateException("No bills found for endToEnd: ${callback.endToEnd}"))
        }
        if (bills.size > 1) {
            logger.warn(append("context", "Multiple bills found with same endToEnd. Using only the first one").andAppend("endToEnd", callback.endToEnd).andAppend("billIds", bills.map { it.billId.value }), logName)
        }

        val bill = bills.first()

        automaticPixBillingService.manageAutomaticPixPayment(
            ManageAutomaticPixPaymentCommand(
                automaticPixBillOnWallet = bill,
                paymentLifecycleEventType = callback.eventType,
                paymentLifecycleStatus = callback.paymentStatus,
                correlationId = callback.internalReference,
                message = callback.message,
            ),

        ).getOrThrow()
    }

    private fun discoverWallet(payer: PixParticipantCallbackData): Result<WalletId> = runCatching {
        val bankNo = payer.ispbCode?.toLong() ?: throw IllegalStateException("Routing number is required for the callback")
        val routingNo = payer.agencyCode?.toLong() ?: throw IllegalStateException("Routing number is required for the callback")
        val fullAccountNo = payer.accountNumber ?: throw IllegalStateException("Account number is required for the callback")

        val accountPaymentMethod = accountService.findPhysicalAccountPaymentMethod(bankNo = bankNo, routingNo = routingNo, fullAccountNo = fullAccountNo).getOrElse {
            throw IllegalStateException("Account payment method not found for bankNo: $bankNo, routingNo: $routingNo, fullAccountNo: $fullAccountNo")
        }
        val wallet = walletService.findWallet(accountPaymentMethod.accountId, accountPaymentMethod.id)
        wallet.id
    }

    override fun handleException(e: Exception): MessageHandlerResponse {
        val logName = "AutomaticPixCallbackHandler#handleException"
        logger.error(Markers.empty(), logName, e)
        return MessageHandlerResponse.keep()
    }
}