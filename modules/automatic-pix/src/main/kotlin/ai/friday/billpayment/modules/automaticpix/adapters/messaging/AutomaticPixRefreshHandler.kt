package ai.friday.billpayment.modules.automaticpix.adapters.messaging

import ai.friday.billpayment.adapters.messaging.Handler
import ai.friday.billpayment.modules.automaticpix.AutomaticPix
import ai.friday.billpayment.modules.automaticpix.app.services.AutomaticPixBillingService
import ai.friday.billpayment.modules.automaticpix.app.services.AutomaticPixMandateService
import ai.friday.billpayment.modules.automaticpix.app.services.AutomaticPixRecurrenceService
import ai.friday.billpayment.modules.automaticpix.app.services.AutomaticPixRefreshMessage
import ai.friday.billpayment.modules.automaticpix.app.services.AutomaticPixRefreshType
import ai.friday.morning.json.parseObjectFrom
import ai.friday.morning.log.andAppend
import ai.friday.morning.messaging.MessageHandler
import ai.friday.morning.messaging.MessageHandlerResponse
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

@Handler
@AutomaticPix
open class AutomaticPixRefreshHandler(
    private val automaticPixMandateService: AutomaticPixMandateService,
    private val automaticPixRecurrenceService: AutomaticPixRecurrenceService,
    private val automaticPixBillingService: AutomaticPixBillingService,

) : MessageHandler {
    private val logger = LoggerFactory.getLogger(AutomaticPixRefreshHandler::class.java)

    override val configurationName = "automatic-pix-refresh"

    override fun handleMessage(message: Message): MessageHandlerResponse {
        val logName = "AutomaticPixRefreshHandler"
        val markers = Markers.empty()

        val automaticPixRefreshMessage = parseObjectFrom<AutomaticPixRefreshMessage>(message.body())
        markers.andAppend("automaticPixRefreshMessage", automaticPixRefreshMessage)

        val shouldDelete = when (automaticPixRefreshMessage.refreshType) {
            AutomaticPixRefreshType.MANDATE -> automaticPixMandateService.refreshMandates(walletId = automaticPixRefreshMessage.walletId)
            AutomaticPixRefreshType.RECURRENCE -> automaticPixRecurrenceService.refreshRecurrences(walletId = automaticPixRefreshMessage.walletId)
            AutomaticPixRefreshType.BILLING -> automaticPixBillingService.refreshBilling(walletId = automaticPixRefreshMessage.walletId)
        }.onFailure {
            logger.error(markers, logName, it)
        }.onSuccess {
            logger.info(markers, logName)
        }.isSuccess

        markers.andAppend("shouldDeleteMessage", shouldDelete)

        return MessageHandlerResponse.build(shouldDeleteMessage = shouldDelete)
    }

    override fun handleException(e: Exception): MessageHandlerResponse {
        return MessageHandlerResponse.keep()
    }
}