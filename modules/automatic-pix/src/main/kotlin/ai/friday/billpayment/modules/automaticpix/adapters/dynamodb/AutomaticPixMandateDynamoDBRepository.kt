package ai.friday.billpayment.modules.automaticpix.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.AbstractBillPaymentDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.GlobalSecondaryIndexNames
import ai.friday.billpayment.adapters.dynamodb.INDEX_1
import ai.friday.billpayment.adapters.dynamodb.INDEX_2
import ai.friday.billpayment.adapters.dynamodb.INDEX_3
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.automaticpix.AutomaticPix
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixDocumentType
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateId
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateRepository
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateStatus
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixNotPendingMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPendingMandate
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPeriodicity
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPersonData
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixProvider
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPspInformation
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPayment
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentBase
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentBasicData
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentId
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentStatus
import ai.friday.billpayment.modules.automaticpix.app.CanceledBy
import ai.friday.morning.date.brazilTimeZone
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@AutomaticPix
class AutomaticPixMandateDynamoDAO(
    cli: DynamoDbEnhancedClient,
) : AbstractBillPaymentDynamoDAO<AutomaticPixMandateEntity>(cli, AutomaticPixMandateEntity::class.java)

private const val SCAN_KEY = "AUTOMATIC_PIX_MANDATE"

@AutomaticPix
class AutomaticPixMandateDynamoDBRepository(
    private val dynamoDAO: AutomaticPixMandateDynamoDAO,
) : AutomaticPixMandateRepository {
    override fun save(automaticPixMandate: AutomaticPixMandate): AutomaticPixMandate {
        val (primaryKey, scanKey) = buildKey(automaticPixMandate)

        val entity = AutomaticPixMandateEntity().apply {
            this.primaryKey = primaryKey
            this.scanKey = scanKey
            gSIndex1PartitionKey = automaticPixMandate.walletId.value
            gSIndex1RangeKey = "$SCAN_KEY#${automaticPixMandate.status.name}"
            gSIndex2PartitionKey = automaticPixMandate.recurringPayment.recurringPaymentId.value
            gSIndex2RangeKey = "$SCAN_KEY#${automaticPixMandate.status.name}"
            gSIndex3PartitionKey = buildIndex3Key(automaticPixMandate.status)
            gSIndex3RangeKey = buildIndex3Scan(automaticPixMandate)
            walletId = automaticPixMandate.walletId.value
            mandateId = automaticPixMandate.mandateId.value
            status = automaticPixMandate.status.name
            recurringPaymentId = automaticPixMandate.recurringPayment.recurringPaymentId.value
            provider = automaticPixMandate.provider.name
            expirationDate = automaticPixMandate.expiresAt?.format(DateTimeFormatter.ISO_DATE_TIME)
            when (automaticPixMandate) {
                is AutomaticPixPendingMandate -> {
                    startDateValidity = null
                    endDateValidity = null
                    expiredValidityDate = null
                    canceledAt = null
                    canceledBy = null
                }

                is AutomaticPixNotPendingMandate -> {
                    startDateValidity = automaticPixMandate.startDateValidity?.format(DateTimeFormatter.ISO_DATE)
                    endDateValidity = automaticPixMandate.endDateValidity?.format(DateTimeFormatter.ISO_DATE)
                    expiredValidityDate = automaticPixMandate.expiredValidityDate?.format(DateTimeFormatter.ISO_DATE)
                    canceledAt = automaticPixMandate.canceledAt?.format(DateTimeFormatter.ISO_DATE_TIME)
                    canceledBy = automaticPixMandate.canceledBy?.name
                }
            }
            recurringPayment = automaticPixMandate.recurringPayment.toEntity()
            updatedAt = ZonedDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME)
            createdAt = ZonedDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME)
        }
        dynamoDAO.save(entity)
        return entity.toDomain()
    }

    private fun buildIndex3Key(status: AutomaticPixMandateStatus): String {
        return "$SCAN_KEY#${status.name}"
    }

    private fun buildIndex3Scan(automaticPixMandate: AutomaticPixMandate): String {
        val expiresAt = when (automaticPixMandate) {
            is AutomaticPixNotPendingMandate -> automaticPixMandate.endDateValidity?.atStartOfDay(brazilTimeZone)
            is AutomaticPixPendingMandate -> automaticPixMandate.expiresAt
        }

        return "$SCAN_KEY#EXPIRATION#${expiresAt?.format(DateTimeFormatter.ISO_DATE_TIME) ?: "NO_EXPIRATION_DEFINED"}"
    }

    override fun updateStatus(automaticPixMandate: AutomaticPixMandate, status: AutomaticPixMandateStatus): AutomaticPixMandate {
        val (primaryKey, scanKey) = buildKey(automaticPixMandate)
        return dynamoDAO.findByPrimaryKey(primaryKey, scanKey)?.apply {
            this.status = status.name
            updatedAt = ZonedDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME)
        }?.let {
            dynamoDAO.save(it)
            it.toDomain()
        } ?: throw IllegalStateException("AutomaticPixMandate not found: ${automaticPixMandate.mandateId.value}")
    }

    override fun findByMandateIdAndWalletId(automaticPixMandateId: AutomaticPixMandateId, walletId: WalletId): AutomaticPixMandate? {
        return dynamoDAO.findBeginsWithOnSortKey(automaticPixMandateId.value, "$SCAN_KEY#${walletId.value}#")
            .firstOrNull()?.toDomain()
    }

    override fun findByMandateId(automaticPixMandateId: AutomaticPixMandateId): List<AutomaticPixMandate> {
        return dynamoDAO.findBeginsWithOnSortKey(automaticPixMandateId.value, "$SCAN_KEY#").map { it.toDomain() }
    }

    override fun findByWalletId(walletId: WalletId, status: AutomaticPixMandateStatus?): List<AutomaticPixMandate> {
        val scanKey = status?.let { "$SCAN_KEY#${it.name}" } ?: SCAN_KEY
        return dynamoDAO.findBeginsWithOnIndex(GlobalSecondaryIndexNames.GSIndex1, walletId.value, scanKey)
            .map { it.toDomain() }
    }

    override fun findByStatusExpringBefore(status: AutomaticPixMandateStatus, expireDate: LocalDate): List<AutomaticPixMandate> {
        val dateString = expireDate.format(DateTimeFormatter.ISO_DATE)
        return dynamoDAO.findBeginsWithOnIndex(GlobalSecondaryIndexNames.GSIndex3, buildIndex3Key(status), "$SCAN_KEY#EXPIRATION#$dateString")
            .map { it.toDomain() }
    }

    override fun findByExternalId(
        recurrenceId: AutomaticPixRecurringPaymentId,
        status: AutomaticPixMandateStatus?,
    ): List<AutomaticPixMandate> {
        val scanKey = status?.let { "$SCAN_KEY#${it.name}" } ?: SCAN_KEY
        return dynamoDAO.findBeginsWithOnIndex(GlobalSecondaryIndexNames.GSIndex2, recurrenceId.value, scanKey)
            .map { it.toDomain() }
    }

    private fun buildKey(automaticPixMandate: AutomaticPixMandate): Pair<String, String> = automaticPixMandate.mandateId.value to "$SCAN_KEY#${automaticPixMandate.walletId.value}#${automaticPixMandate.provider.name}"
}

private fun AutomaticPixRecurringPaymentBase.toEntity(): RecurringPaymentDataEntity {
    val domain = this
    return RecurringPaymentDataEntity().apply {
        recurringPaymentId = domain.recurringPaymentId.value
        payee = domain.payee.toEntity()
        status = domain.status.name

        when (domain) {
            is AutomaticPixRecurringPaymentBasicData -> {}
            is AutomaticPixRecurringPayment -> {
                startDate = domain.startDate?.format(DateTimeFormatter.ISO_DATE)
                endDate = domain.endDate?.format(DateTimeFormatter.ISO_DATE)
                fixedAmount = domain.fixedAmount
                minimumReceiverAmount = domain.minimumReceiverAmount
                maximumPayerAmount = domain.maximumPayerAmount
                payer = domain.payer.toEntity()
                contractNumber = domain.contractNumber
                periodicity = domain.periodicity.name
                description = domain.description
            }
        }
    }
}

private fun AutomaticPixPersonData.toEntity(): AutomaticPixPersonDataEntity {
    val domain = this
    return AutomaticPixPersonDataEntity().apply {
        documentNumber = domain.documentNumber
        name = domain.name
        documentType = domain.documentType.name
        pspInformationCode = domain.pspInformation?.code
        pspInformationName = domain.pspInformation?.name
    }
}

private fun AutomaticPixMandateEntity.toDomain(): AutomaticPixMandate {
    val status = AutomaticPixMandateStatus.valueOf(status)
    val walletId = WalletId(walletId)
    val mandateId = AutomaticPixMandateId(mandateId)
    val provider = AutomaticPixProvider.valueOf(provider)

    return when (status) {
        AutomaticPixMandateStatus.PENDING -> {
            AutomaticPixPendingMandate(
                walletId = walletId,
                mandateId = mandateId,
                expiresAt = expirationDate?.let { ZonedDateTime.parse(it, DateTimeFormatter.ISO_DATE_TIME) },
                recurringPayment = recurringPayment.toAutomaticPixRecurringPayment(mandateId, provider),
                provider = provider,
            )
        }

        AutomaticPixMandateStatus.ACTIVE, AutomaticPixMandateStatus.EXPIRED, AutomaticPixMandateStatus.CANCELED, AutomaticPixMandateStatus.PROCESSING -> {
            AutomaticPixNotPendingMandate(
                walletId = walletId,
                mandateId = mandateId,
                status = status,
                recurringPayment = recurringPayment.toAutomaticPixRecurringPaymentBase(mandateId, provider),
                startDateValidity = startDateValidity?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) },
                endDateValidity = endDateValidity?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) },
                expiredValidityDate = expiredValidityDate?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) },
                canceledAt = canceledAt?.let { ZonedDateTime.parse(it, DateTimeFormatter.ISO_DATE_TIME) },
                canceledBy = canceledBy?.let { CanceledBy.valueOf(it) },
                provider = provider,
                expiresAt = expirationDate?.let { ZonedDateTime.parse(it, DateTimeFormatter.ISO_DATE_TIME) },
            )
        }
    }
}

private fun RecurringPaymentDataEntity.toAutomaticPixRecurringPaymentBase(mandateId: AutomaticPixMandateId, provider: AutomaticPixProvider): AutomaticPixRecurringPaymentBase {
    return if (periodicity != null) {
        toAutomaticPixRecurringPayment(mandateId, provider)
    } else {
        AutomaticPixRecurringPaymentBasicData(
            recurringPaymentId = AutomaticPixRecurringPaymentId(recurringPaymentId),
            mandateId = mandateId,
            payee = payee.toDomain(),
            provider = provider,
            status = AutomaticPixRecurringPaymentStatus.valueOf(status),
        )
    }
}

private fun RecurringPaymentDataEntity.toAutomaticPixRecurringPayment(mandateId: AutomaticPixMandateId, provider: AutomaticPixProvider): AutomaticPixRecurringPayment {
    return AutomaticPixRecurringPayment(
        recurringPaymentId = AutomaticPixRecurringPaymentId(recurringPaymentId),
        mandateId = mandateId,
        startDate = startDate?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) },
        endDate = endDate?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) },
        fixedAmount = fixedAmount,
        minimumReceiverAmount = minimumReceiverAmount,
        maximumPayerAmount = maximumPayerAmount,
        payee = payee.toDomain(),
        payer = payer?.toDomain() ?: throw IllegalStateException("Debtor cannot be null for AutomaticPixRecurringPayment"),
        contractNumber = contractNumber,
        periodicity = periodicity?.let { AutomaticPixPeriodicity.valueOf(it) } ?: throw IllegalStateException("Periodicity cannot be null for AutomaticPixRecurringPayment"),
        description = description,
        provider = provider,
        notificationsScheduling = null,
        creditLimitUsage = null,
        status = AutomaticPixRecurringPaymentStatus.valueOf(status),
    )
}

fun AutomaticPixPersonDataEntity.toDomain(): AutomaticPixPersonData {
    return AutomaticPixPersonData(
        documentNumber = documentNumber,
        name = name,
        documentType = AutomaticPixDocumentType.valueOf(documentType),
        pspInformation = pspInformationCode?.let {
            AutomaticPixPspInformation(
                code = it,
                name = pspInformationName,
            )
        },
    )
}

@DynamoDbBean
class AutomaticPixMandateEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1RangeKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    lateinit var gSIndex2PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    lateinit var gSIndex2RangeKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_3])
    @get:DynamoDbAttribute(value = "GSIndex3PrimaryKey")
    lateinit var gSIndex3PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_3])
    @get:DynamoDbAttribute(value = "GSIndex3ScanKey")
    lateinit var gSIndex3RangeKey: String

    @get:DynamoDbAttribute("WalletId")
    lateinit var walletId: String

    @get:DynamoDbAttribute("MandateId")
    lateinit var mandateId: String

    @get:DynamoDbAttribute("Status")
    lateinit var status: String

    @get:DynamoDbAttribute("RecurringPaymentId")
    lateinit var recurringPaymentId: String

    @get:DynamoDbAttribute("Provider")
    lateinit var provider: String

    @get:DynamoDbAttribute("ExpirationDate")
    var expirationDate: String? = null

    @get:DynamoDbAttribute("StartDateValidity")
    var startDateValidity: String? = null

    @get:DynamoDbAttribute("EndDateValidity")
    var endDateValidity: String? = null

    @get:DynamoDbAttribute("ExpiredValidityDate")
    var expiredValidityDate: String? = null

    @get:DynamoDbAttribute("CanceledAt")
    var canceledAt: String? = null

    @get:DynamoDbAttribute("CanceledBy")
    var canceledBy: String? = null

    @get:DynamoDbAttribute("RecurringPaymentData")
    lateinit var recurringPayment: RecurringPaymentDataEntity

    @get:DynamoDbAttribute("CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute("UpdatedAt")
    lateinit var updatedAt: String
}

@DynamoDbBean
class RecurringPaymentDataEntity {
    lateinit var recurringPaymentId: String
    lateinit var payee: AutomaticPixPersonDataEntity
    var startDate: String? = null
    var endDate: String? = null
    var fixedAmount: Long? = null
    var minimumReceiverAmount: Long? = null
    var maximumPayerAmount: Long? = null
    var payer: AutomaticPixPersonDataEntity? = null
    var contractNumber: String? = null
    var periodicity: String? = null
    var description: String? = null
    lateinit var status: String
}

@DynamoDbBean
class AutomaticPixPersonDataEntity {
    var documentNumber: String? = null
    var name: String? = null
    lateinit var documentType: String
    var pspInformationCode: String? = null
    var pspInformationName: String? = null
}