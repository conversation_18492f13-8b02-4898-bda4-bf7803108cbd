package ai.friday.billpayment.modules.automaticpix.adapters.api.builders

import ai.friday.billpayment.adapters.api.builders.PixQrCodeCompoundDataBuilder
import ai.friday.billpayment.adapters.api.builders.PixQrCompoundDataTOData
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.modules.automaticpix.AutomaticPix
import ai.friday.billpayment.modules.automaticpix.adapters.api.AutomaticPixMandateOrRecurrenceResponseTO
import ai.friday.billpayment.modules.automaticpix.adapters.api.BillingCycleResponseTO
import ai.friday.billpayment.modules.automaticpix.adapters.api.ContractResponseTO
import ai.friday.billpayment.modules.automaticpix.adapters.api.MandateAmountResponseTO
import ai.friday.billpayment.modules.automaticpix.adapters.api.toResponseTO
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateStatus
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixQrCodeRecurringData
import ai.friday.billpayment.modules.automaticpix.app.services.AutomaticPixRecurrenceService
import jakarta.inject.Provider
import java.time.format.DateTimeFormatter

@AutomaticPix
class PixQrCodeCompoundDataBuilder(
    private val automaticPixRecurrenceService: Provider<AutomaticPixRecurrenceService>,
) : PixQrCodeCompoundDataBuilder {

    override fun toResponseTO(recurringPixDataJson: String): Result<PixQrCompoundDataTOData> {
        val recurringPixData = parseObjectFrom<AutomaticPixQrCodeRecurringData>(recurringPixDataJson)
        return automaticPixRecurrenceService.get().buildRecurringPayment(recurringPixData).mapCatching { automaticPixRecurringPaymentWithJourney ->
            val automaticPixRecurringPayment = automaticPixRecurringPaymentWithJourney.recurrenceData

            val mandate = AutomaticPixMandateOrRecurrenceResponseTO(
                mandateId = null,
                status = AutomaticPixMandateStatus.PENDING.name,
                recurringPaymentId = automaticPixRecurringPayment.recurringPaymentId.value,
                payee = automaticPixRecurringPayment.payee.toResponseTO(),
                payer = automaticPixRecurringPayment.payer.toResponseTO(),
                contract = ContractResponseTO(
                    id = automaticPixRecurringPayment.contractNumber,
                    subject = automaticPixRecurringPayment.description,
                ),
                billingCycle = BillingCycleResponseTO(
                    frequency = automaticPixRecurringPayment.periodicity,
                    startsAt = automaticPixRecurringPayment.startDate?.format(DateTimeFormatter.ISO_DATE),
                    endsAt = automaticPixRecurringPayment.endDate?.format(DateTimeFormatter.ISO_DATE),
                ),
                amount = MandateAmountResponseTO(
                    min = automaticPixRecurringPayment.minimumReceiverAmount,
                    max = automaticPixRecurringPayment.maximumPayerAmount,
                    base = automaticPixRecurringPayment.fixedAmount,
                ),
                activatedAt = null,
                canceledAt = null,
                expiresAt = null,
                canceledBy = null,

                endDateValidity = null,
                retryType = automaticPixRecurringPayment.checkRetryType().name,
                provider = automaticPixRecurringPayment.provider,
                notificationsScheduling = automaticPixRecurringPayment.notificationsScheduling,
                creditLimitUsage = automaticPixRecurringPayment.creditLimitUsage,
            )

            PixQrCompoundDataTO(
                mandate = mandate,
                userMustConfirm = automaticPixRecurringPaymentWithJourney.journey.requiresManualApprove(),
            )
        }
    }
}

data class PixQrCompoundDataTO(
    val mandate: AutomaticPixMandateOrRecurrenceResponseTO,
    val userMustConfirm: Boolean,
) : PixQrCompoundDataTOData