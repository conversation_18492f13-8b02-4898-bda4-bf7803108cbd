package ai.friday.billpayment.modules.automaticpix.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.AbstractBillPaymentDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.GlobalSecondaryIndexNames
import ai.friday.billpayment.adapters.dynamodb.INDEX_1
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.automaticpix.AutomaticPix
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateId
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPeriodicity
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixPersonData
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixProvider
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPayment
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentId
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentOnWallet
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentRepository
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentStatus
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@AutomaticPix
class AutomaticPixRecurringPaymentDynamoDAO(
    cli: DynamoDbEnhancedClient,
) : AbstractBillPaymentDynamoDAO<AutomaticPixRecurringPaymentEntity>(cli, AutomaticPixRecurringPaymentEntity::class.java)

private const val SCAN_KEY = "AUTOMATIC_PIX_RECURRING_PAYMENT"

@AutomaticPix
class AutomaticPixRecurringPaymentDynamoDBRepository(
    private val dynamoDAO: AutomaticPixRecurringPaymentDynamoDAO,
) : AutomaticPixRecurringPaymentRepository {

    private fun buildPrimaryKey(automaticPixRecurringPaymentOnWallet: AutomaticPixRecurringPaymentOnWallet): Pair<String, String> {
        return automaticPixRecurringPaymentOnWallet.automaticPixRecurringPayment.recurringPaymentId.value to "$SCAN_KEY#${automaticPixRecurringPaymentOnWallet.walletId.value}#${automaticPixRecurringPaymentOnWallet.automaticPixRecurringPayment.provider.name}"
    }

    override fun save(automaticPixRecurringPaymentOnWallet: AutomaticPixRecurringPaymentOnWallet): AutomaticPixRecurringPaymentOnWallet {
        val automaticPixRecurringPayment = automaticPixRecurringPaymentOnWallet.automaticPixRecurringPayment
        val (primaryKey, scanKey) = buildPrimaryKey(automaticPixRecurringPaymentOnWallet)
        val entity = AutomaticPixRecurringPaymentEntity().apply {
            this.primaryKey = primaryKey
            this.scanKey = scanKey

            gSIndex1PartitionKey = automaticPixRecurringPaymentOnWallet.walletId.value
            gSIndex1RangeKey = SCAN_KEY

            recurringPaymentId = automaticPixRecurringPayment.recurringPaymentId.value
            walletId = automaticPixRecurringPaymentOnWallet.walletId.value
            mandateId = automaticPixRecurringPayment.mandateId?.value
            startDate = automaticPixRecurringPayment.startDate?.format(DateTimeFormatter.ISO_DATE)
            endDate = automaticPixRecurringPayment.endDate?.format(DateTimeFormatter.ISO_DATE)
            fixedAmount = automaticPixRecurringPayment.fixedAmount
            minimumReceiverAmount = automaticPixRecurringPayment.minimumReceiverAmount
            maximumPayerAmount = automaticPixRecurringPayment.maximumPayerAmount
            receiver = automaticPixRecurringPayment.payee.toEntity()
            debtor = automaticPixRecurringPayment.payer.toEntity()
            contractNumber = automaticPixRecurringPayment.contractNumber
            periodicity = automaticPixRecurringPayment.periodicity.name
            description = automaticPixRecurringPayment.description
            provider = automaticPixRecurringPayment.provider.name
            notificationsScheduling = automaticPixRecurringPayment.notificationsScheduling ?: false
            creditLimitUsage = automaticPixRecurringPayment.creditLimitUsage ?: false
            status = automaticPixRecurringPayment.status.name
            updatedAt = ZonedDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME)
            createdAt = ZonedDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME)
        }
        dynamoDAO.save(entity)
        return entity.toDomain()
    }

    override fun findByIdAndWalletId(automaticPixRecurringPaymentId: AutomaticPixRecurringPaymentId, walletId: WalletId): AutomaticPixRecurringPaymentOnWallet? {
        return dynamoDAO.findBeginsWithOnSortKey(automaticPixRecurringPaymentId.value, "$SCAN_KEY#${walletId.value}#")
            .singleOrNull()?.toDomain()
    }

    override fun findById(automaticPixRecurringPaymentId: AutomaticPixRecurringPaymentId): List<AutomaticPixRecurringPaymentOnWallet> {
        return dynamoDAO.findBeginsWithOnSortKey(automaticPixRecurringPaymentId.value, "$SCAN_KEY#")
            .map { it.toDomain() }
    }

    override fun findByWalletId(walletId: WalletId): List<AutomaticPixRecurringPaymentOnWallet> {
        return dynamoDAO.findBeginsWithOnIndex(GlobalSecondaryIndexNames.GSIndex1, walletId.value, SCAN_KEY)
            .map { it.toDomain() }
    }

    private fun AutomaticPixPersonData.toEntity(): AutomaticPixPersonDataEntity {
        return AutomaticPixPersonDataEntity().apply {
            documentNumber = <EMAIL>
            name = <EMAIL>
            documentType = <EMAIL>
            pspInformationCode = <EMAIL>?.code
            pspInformationName = <EMAIL>?.name
        }
    }

    private fun AutomaticPixRecurringPaymentEntity.toDomain(): AutomaticPixRecurringPaymentOnWallet {
        return AutomaticPixRecurringPaymentOnWallet(
            walletId = WalletId(walletId),
            automaticPixRecurringPayment = AutomaticPixRecurringPayment(
                recurringPaymentId = AutomaticPixRecurringPaymentId(recurringPaymentId),
                mandateId = mandateId?.let { AutomaticPixMandateId(it) },
                startDate = startDate?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) },
                endDate = endDate?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) },
                fixedAmount = fixedAmount,
                minimumReceiverAmount = minimumReceiverAmount,
                maximumPayerAmount = maximumPayerAmount,
                payee = receiver.toDomain(),
                payer = debtor.toDomain(),
                contractNumber = contractNumber,
                periodicity = AutomaticPixPeriodicity.valueOf(periodicity),
                description = description,
                provider = AutomaticPixProvider.valueOf(provider),
                notificationsScheduling = notificationsScheduling,
                creditLimitUsage = creditLimitUsage,
                status = AutomaticPixRecurringPaymentStatus.valueOf(status),
            ),
        )
    }
}

@DynamoDbBean
class AutomaticPixRecurringPaymentEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    var gSIndex1PartitionKey: String? = null

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    var gSIndex1RangeKey: String? = null

    @get:DynamoDbAttribute("RecurringPaymentId")
    lateinit var recurringPaymentId: String

    @get:DynamoDbAttribute("WalletId")
    lateinit var walletId: String

    @get:DynamoDbAttribute("MandateId")
    var mandateId: String? = null

    @get:DynamoDbAttribute("StartDate")
    var startDate: String? = null

    @get:DynamoDbAttribute("EndDate")
    var endDate: String? = null

    @get:DynamoDbAttribute("FixedAmount")
    var fixedAmount: Long? = null

    @get:DynamoDbAttribute("MinimumReceiverAmount")
    var minimumReceiverAmount: Long? = null

    @get:DynamoDbAttribute("MaximumPayerAmount")
    var maximumPayerAmount: Long? = null

    @get:DynamoDbAttribute("Receiver")
    lateinit var receiver: AutomaticPixPersonDataEntity

    @get:DynamoDbAttribute("Debtor")
    lateinit var debtor: AutomaticPixPersonDataEntity

    @get:DynamoDbAttribute("ContractNumber")
    var contractNumber: String? = null

    @get:DynamoDbAttribute("Periodicity")
    lateinit var periodicity: String

    @get:DynamoDbAttribute("Description")
    var description: String? = null

    @get:DynamoDbAttribute("Provider")
    lateinit var provider: String

    @get:DynamoDbAttribute("NotificationsScheduling")
    var notificationsScheduling: Boolean = false

    @get:DynamoDbAttribute("CreditLimitUsage")
    var creditLimitUsage: Boolean = false

    @get:DynamoDbAttribute("Status")
    lateinit var status: String

    @get:DynamoDbAttribute("CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute("UpdatedAt")
    lateinit var updatedAt: String
}