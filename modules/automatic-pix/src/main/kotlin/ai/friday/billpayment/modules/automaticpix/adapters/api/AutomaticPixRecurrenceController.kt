package ai.friday.billpayment.modules.automaticpix.adapters.api

import ai.friday.billpayment.adapters.api.getWallet
import ai.friday.billpayment.app.account.Role.Code.OWNER
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.PixCopyAndPaste
import ai.friday.billpayment.modules.automaticpix.AutomaticPix
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurrenceCancellationReason
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentId
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentOnWalletWithItsMandate
import ai.friday.billpayment.modules.automaticpix.app.RecurrenceUpdateableValues
import ai.friday.billpayment.modules.automaticpix.app.services.AutomaticPixRecurrenceService
import ai.friday.morning.log.andAppend
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(OWNER)
@AutomaticPix
@Version("2")
@Controller("/automatic-pix/recurrence")
class AutomaticPixRecurrenceController(
    private val automaticPixRecurrenceService: AutomaticPixRecurrenceService,
) {
    private val logger = LoggerFactory.getLogger(AutomaticPixMandateController::class.java)

    @Get("/all")
    fun getAllRecurrences(authentication: Authentication): HttpResponse<*> {
        val logName = "AutomaticPixRecurrenceController#getAllRecurrences"
        val wallet = authentication.getWallet()
        val markers = append("accountId", authentication.toAccountId().value).andAppend("walletId", wallet.id.value)

        val mandates = automaticPixRecurrenceService.getAllStoredRecurrences(wallet.id).getOrElse {
            logger.error(markers, logName, it)
            return HttpResponse.serverError(it)
        }.map { it.toResponseTO() }

        logger.info(markers.andAppend("mandatesAndStatus", mandates.map { it.mandateId to it.status }), logName)

        return HttpResponse.ok(mandates)
    }

    @Get("/refresh")
    fun refresh(authentication: Authentication): HttpResponse<*> {
        val logName = "AutomaticPixRecurrenceController#refresh"
        val wallet = authentication.getWallet()
        val markers = append("accountId", authentication.toAccountId().value).andAppend("walletId", wallet.id.value)

        val mandates = automaticPixRecurrenceService.refreshRecurrences(wallet.id).getOrElse {
            logger.error(markers, logName, it)
            return HttpResponse.serverError(it)
        }.map { it.toResponseTO() }

        logger.info(markers.andAppend("mandatesAndStatus", mandates.map { it.mandateId to it.status }), logName)

        return HttpResponse.ok(mandates)
    }

    @Post("/approveByBill")
    fun approveByBill(authentication: Authentication, @Body body: ApproveRecurrenceByBillRequestTO): HttpResponse<*> {
        val logName = "AutomaticPixRecurrenceController#approveByBill"
        val wallet = authentication.getWallet()
        val markers = append("accountId", authentication.toAccountId().value).andAppend("walletId", wallet.id.value).andAppend("requestBody", body)

        automaticPixRecurrenceService.approveRecurrence(
            billId = BillId(body.billId),
            accountId = authentication.toAccountId(),
            walletId = wallet.id,
            automaticApprove = false,
            maximumAmount = body.maximumAmount,

        ).getOrElse {
            logger.error(markers, logName, it)
            return HttpResponse.serverError(it)
        }

        return HttpResponse.ok<Unit>()
    }

    @Post("/approveByQrCode")
    fun approveByQrCode(authentication: Authentication, @Body body: ApproveRecurrenceByQrCodeRequestTO): HttpResponse<*> {
        val logName = "AutomaticPixRecurrenceController#approveByQrCode"
        val wallet = authentication.getWallet()
        val markers = append("accountId", authentication.toAccountId().value).andAppend("walletId", wallet.id.value).andAppend("requestBody", body)

        automaticPixRecurrenceService.approveRecurrence(
            pixCopyAndPaste = PixCopyAndPaste(body.qrCode),
            accountId = authentication.toAccountId(),
            walletId = wallet.id,
            maximumAmount = body.maximumAmount,
        ).getOrElse {
            logger.error(markers, logName, it)
            return HttpResponse.serverError(it)
        }

        return HttpResponse.ok<Unit>()
    }

    @Post("/cancel")
    fun cancel(authentication: Authentication, @Body body: CancelRecurrenceRequestTO): HttpResponse<*> {
        val logName = "AutomaticPixRecurrenceController#approveByQrCode"
        val wallet = authentication.getWallet()
        val accountId = authentication.toAccountId()
        val markers = append("accountId", accountId.value).andAppend("walletId", wallet.id.value).andAppend("requestBody", body)

        val updated = automaticPixRecurrenceService.cancelRecurrence(
            automaticPixRecurringPaymentId = AutomaticPixRecurringPaymentId(body.recurringPaymentId),
            automaticPixRecurrenceCancellationReason = AutomaticPixRecurrenceCancellationReason.USER_REQUEST,
            accountId = accountId,
            walletId = wallet.id,
        ).getOrElse {
            logger.error(markers, logName, it)
            return HttpResponse.serverError(it)
        }.toResponseTO()

        return HttpResponse.ok(updated)
    }

    @Post("/update")
    fun update(authentication: Authentication, @Body body: UpdateRecurrenceRequestTO): HttpResponse<*> {
        val logName = "AutomaticPixRecurrenceController#update"
        val wallet = authentication.getWallet()
        val accountId = authentication.toAccountId()
        val markers = append("accountId", accountId.value).andAppend("walletId", wallet.id.value).andAppend("requestBody", body)

        val updated = automaticPixRecurrenceService.updateRecurrence(
            automaticPixRecurringPaymentId = AutomaticPixRecurringPaymentId(body.recurringPaymentId),
            updateableValues = RecurrenceUpdateableValues(
                maximumPayerAmount = body.maximumPayerAmount,
                creditLimitUsage = body.creditLimitUsage ?: false,
                schedulingNotifications = body.schedulingNotifications ?: true,
            ),
            walletId = wallet.id,
            accountId = accountId,
        ).getOrElse {
            logger.error(markers, logName, it)
            return HttpResponse.serverError(it)
        }.toResponseTO()

        return HttpResponse.ok(updated)
    }
}

data class UpdateRecurrenceRequestTO(
    val recurringPaymentId: String,
    val maximumPayerAmount: Long?,
    val creditLimitUsage: Boolean?,
    val schedulingNotifications: Boolean?,
)

data class ApproveRecurrenceByBillRequestTO(
    val billId: String,
    val maximumAmount: Long?,
)

data class ApproveRecurrenceByQrCodeRequestTO(
    val qrCode: String,
    val maximumAmount: Long?,
)

data class CancelRecurrenceRequestTO(
    val recurringPaymentId: String,
)

fun AutomaticPixRecurringPaymentOnWalletWithItsMandate.toResponseTO(): AutomaticPixMandateOrRecurrenceResponseTO {
    return AutomaticPixMandateOrRecurrenceResponseTO(
        mandateId = this.recurringPayment.mandateId?.value,
        recurringPaymentId = this.recurringPayment.recurringPaymentId.value,
        status = this.recurringPayment.status.name,
        payee = this.recurringPayment.payee.toResponseTO(),
        payer = this.recurringPayment.payer.toResponseTO(),
        contract = ContractResponseTO(
            id = this.recurringPayment.contractNumber,
            subject = this.recurringPayment.description,
        ),
        billingCycle = BillingCycleResponseTO(
            frequency = this.recurringPayment.periodicity,
            startsAt = this.recurringPayment.startDate?.format(DateTimeFormatter.ISO_DATE),
            endsAt = this.recurringPayment.endDate?.format(DateTimeFormatter.ISO_DATE),
        ),
        amount = MandateAmountResponseTO(
            min = this.recurringPayment.minimumReceiverAmount,
            max = this.recurringPayment.maximumPayerAmount,
            base = this.recurringPayment.fixedAmount,
        ),
        activatedAt = this.mandate?.startDateValidity?.format(DateTimeFormatter.ISO_DATE),
        canceledAt = this.mandate?.canceledAt?.format(DateTimeFormatter.ISO_DATE_TIME),
        expiresAt = null,
        canceledBy = this.mandate?.canceledBy,
        endDateValidity = this.mandate?.endDateValidity?.format(DateTimeFormatter.ISO_DATE),
        retryType = this.recurringPayment.checkRetryType().name,
        provider = this.recurringPayment.provider,
        notificationsScheduling = this.recurringPayment.notificationsScheduling,
        creditLimitUsage = this.recurringPayment.creditLimitUsage,
    )
}