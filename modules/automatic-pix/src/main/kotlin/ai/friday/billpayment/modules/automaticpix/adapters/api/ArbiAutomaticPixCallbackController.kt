package ai.friday.billpayment.modules.automaticpix.adapters.api

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.Role.Code.ARBI_CALLBACK
import ai.friday.billpayment.modules.automaticpix.AutomaticPix
import ai.friday.billpayment.modules.automaticpix.adapters.arbi.toLongMoneyAmount
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixMandateId
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixProvider
import ai.friday.billpayment.modules.automaticpix.app.AutomaticPixRecurringPaymentId
import ai.friday.billpayment.modules.automaticpix.app.services.AutomaticPixMessagePublisher
import ai.friday.morning.log.andAppend
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonTypeInfo
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(ARBI_CALLBACK)
@AutomaticPix
@Controller("/arbi/automatic-pix/callback")
class ArbiAutomaticPixCallbackController(
    private val automaticPixMessagePublisher: AutomaticPixMessagePublisher,
) {
    private val logger = LoggerFactory.getLogger(ArbiAutomaticPixCallbackController::class.java)

    @Post
    fun handleNotification(@Body payload: String): HttpResponse<*> {
        val logName = "ArbiAutomaticPixCallbackController#handleNotification"
        logger.info(append("rawPayload", payload), logName)

        val markers = Markers.empty()
        runCatching {
            val arbiNotificationWrapperTO = parseObjectFrom<ArbiNotificationWrapperTO>(payload)
            markers.andAppend("notificationType", arbiNotificationWrapperTO.notificationType)
            parseCallbackData(arbiNotificationWrapperTO).getOrThrow()
        }.map { parsedCallbackData ->
            automaticPixMessagePublisher.publishCallbackMessage(
                parsedCallbackData = parsedCallbackData,
            )
            logger.info(markers, logName)
            return HttpResponse.ok("Callback processed successfully")
        }.getOrElse {
            logger.error(markers, logName, it)
            return HttpResponse.badRequest(it.message)
        }
    }

    private fun parseCallbackData(arbiNotificationWrapperTO: ArbiNotificationWrapperTO): Result<ParsedCallbackData> = runCatching {
        when (arbiNotificationWrapperTO.notificationType) {
            NotificationType.SOLREC_RECEBIDA,
            NotificationType.SOLREC_EXPIRADA,
            NotificationType.SOLREC_APROVADA_SEMRESPOSTA,
            NotificationType.SOLREC_CANCELADA,
            NotificationType.REC_CANCELADA,
            NotificationType.REC_CONFIRMADA,
            -> {
                val recurrence = arbiNotificationWrapperTO.recurrence ?: throw IllegalArgumentException("Recurrence details are required for ${arbiNotificationWrapperTO.notificationType}")
                ParsedCallbackData.MandateAndRecurrenceLifecycleCallback(
                    eventType = arbiNotificationWrapperTO.notificationType.toMandateAndRecurrenceLifecycleEventType(),
                    recurrenceId = recurrence.recurrenceId?.let { AutomaticPixRecurringPaymentId(it) } ?: throw IllegalArgumentException("Recurrence recurrenceId is required for ${arbiNotificationWrapperTO.notificationType}"),
                    mandateId = recurrence.authorizationId?.let { AutomaticPixMandateId(it) } ?: throw IllegalArgumentException("Recurrence authorizationId is required for ${arbiNotificationWrapperTO.notificationType}"),
                    payer = recurrence.payer?.toPaymentLifecyclePixParticipantCallback() ?: throw IllegalArgumentException("Recurrence payer is required for ${arbiNotificationWrapperTO.notificationType}"),
                )
            }

            NotificationType.COBR_CANCELADA,
            NotificationType.COBR_REJEITADA,
            NotificationType.COBR_RECEBIDA,
            -> {
                val recurrence = arbiNotificationWrapperTO.recurrence ?: throw IllegalArgumentException("Recurrence details are required for ${arbiNotificationWrapperTO.notificationType}")
                val endToEnd = arbiNotificationWrapperTO.billing?.endToEnd ?: throw IllegalArgumentException("Billing details and endToEnd are required for ${arbiNotificationWrapperTO.notificationType}")
                ParsedCallbackData.BillingLifecycleCallback(
                    eventType = arbiNotificationWrapperTO.notificationType.toBillingLifecycleEventType(),
                    recurrenceId = recurrence.recurrenceId?.let { AutomaticPixRecurringPaymentId(it) } ?: throw IllegalArgumentException("Recurrence recurrenceId is required for ${arbiNotificationWrapperTO.notificationType}"),
                    mandateId = recurrence.authorizationId?.let { AutomaticPixMandateId(it) } ?: throw IllegalArgumentException("Recurrence authorizationId is required for ${arbiNotificationWrapperTO.notificationType}"),
                    endToEnd = endToEnd,
                    payer = recurrence.payer?.toPaymentLifecyclePixParticipantCallback() ?: throw IllegalArgumentException("Recurrence payer is required for ${arbiNotificationWrapperTO.notificationType}"),
                )
            }

            NotificationType.RECEBIDO,
            NotificationType.LIQUIDADO,
            NotificationType.CANCELADO,
            NotificationType.FALTA_LIMITE,

            NotificationType.SLD_INSUFICIENTE_NGRADE,
            NotificationType.SLD_INSUFICIENTE_ULTGRADE,

            NotificationType.FALHA_CHAVE_DICT,
            NotificationType.FALHA_OPERACIONAL,
            NotificationType.EXPIRADO,

            NotificationType.FALTA_LIMITE_ULTGRADE,
            NotificationType.FALTA_LIMITE_NGRADE,
            -> {
                val endToEnd = arbiNotificationWrapperTO.endToEnd ?: arbiNotificationWrapperTO.charge?.endToEnd ?: throw IllegalArgumentException("EndToEnd is required for ${arbiNotificationWrapperTO.notificationType}")

                ParsedCallbackData.PaymentLifecycleCallback(
                    eventType = arbiNotificationWrapperTO.notificationType.toPaymentLifecycleEventType(),
                    endToEnd = endToEnd,
                    payer = arbiNotificationWrapperTO.payer?.toPaymentLifecyclePixParticipantCallback() ?: arbiNotificationWrapperTO.recurrence?.payer?.toPaymentLifecyclePixParticipantCallback(),
                    payee = arbiNotificationWrapperTO.receiver?.toPaymentLifecyclePixParticipantCallback() ?: arbiNotificationWrapperTO.recurrence?.receiver?.toPaymentLifecyclePixParticipantCallback(),
                    value = arbiNotificationWrapperTO.value?.toLongMoneyAmount(),
                    schedulingDate = arbiNotificationWrapperTO.schedulingDate?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) },
                    settlementDate = arbiNotificationWrapperTO.settlementDate?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) },
                    internalReference = arbiNotificationWrapperTO.internalReference,
                    paymentStatus = arbiNotificationWrapperTO.status?.toPaymentLifecycleStatus() ?: PaymentLifecycleStatus.UNKNOWN,
                    message = arbiNotificationWrapperTO.message,
                )
            }
        }
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
private data class ArbiNotificationWrapperTO(
    val notificationType: NotificationType,
    val recurrence: ArbiRecurrenceDetailsTO?,
    val billing: BillingDetailsTO?,
    val payer: ArbiPixParticipantTO?,
    val receiver: ArbiPixParticipantTO?,
    val endToEnd: String?,
    val value: Double?,
    val schedulingDate: String?,
    val settlementDate: String?,
    val internalReference: String?,
    val status: ArbiCallbackPaymentStatus?,
    val message: String?,
    val charge: ArbiChargeTO?,
) {

    data class ArbiRecurrenceDetailsTO(
        val authorizationId: String?,
        val recurrenceId: String?,
        val payer: ArbiPixParticipantTO?,
        val receiver: ArbiPixParticipantTO?,
    )

    data class BillingDetailsTO(
        val endToEnd: String?,
        val value: Double,
        val dueDate: String?,
    )

    data class ArbiPixParticipantTO(
        val ispbCode: String?,
        val taxId: String?,
        val accountType: String?,
        val accountNumber: Long?,
        val agencyCode: Int?,
        val name: String?,
    ) {
        fun toPaymentLifecyclePixParticipantCallback(): PixParticipantCallbackData {
            return PixParticipantCallbackData(
                ispbCode = ispbCode,
                taxId = taxId,
                accountType = accountType,
                accountNumber = accountNumber?.toString(),
                agencyCode = agencyCode,
                name = name,
            )
        }
    }

    data class ArbiChargeTO(
        val endToEnd: String?,
        val value: Double?,
    )

    enum class ArbiCallbackPaymentStatus {
        AGENDADO, // Pagamento foi agendado
        PROCESSANDO, // Pagamento em processamento no sistema
        ENVIADO, // Pagamento enviado ao sistema Pix
        APROVADO, // Pagamento aprovado pelo sistema Pix
        REJEITADO, // Pagamento rejeitado pelo sistema Pix
        CANCELADO, // Pagamento foi cancelado
        ;

        fun toPaymentLifecycleStatus(): PaymentLifecycleStatus {
            return when (this) {
                AGENDADO -> PaymentLifecycleStatus.SCHEDULED
                PROCESSANDO -> PaymentLifecycleStatus.PROCESSING
                ENVIADO -> PaymentLifecycleStatus.STARTED
                APROVADO -> PaymentLifecycleStatus.PAID
                REJEITADO -> PaymentLifecycleStatus.REJECTED
                CANCELADO -> PaymentLifecycleStatus.CANCELED
            }
        }
    }
}

/*
SLD_INSUFICIENTE_NGRADE - Tentativa de liquidação falhou por saldo insuficiente, mas haverá novas tentativas.
SLD_INSUFICIENTE_ULTGRADE - Tentativa final de liquidação falhou por saldo insuficiente.

FALTA_LIMITE_NGRADE - Tentativa de liquidação falhou por falta de limite, mas haverá novas tentativas.
FALTA_LIMITE_ULTGRADE - Tentativa final de liquidação falhou por falta de limite.

FALHA_OPERACIONAL - Falha operacional impediu a liquidação do pagamento.
------
RECEBIDO – Pagamento agendado
LIQUIDADO – Agendamento liquidado
CANCELADO – Agendamento cancelado
FALTA_LIMITE – Não efetivado por insuficiência de limite diário

SLD_INSUFICIENTE_NGRADE – Tentativa falhou por saldo insuficiente (não é a última grade)
SLD_INSUFICIENTE_ULTGRADE – Tentativa falhou por saldo insuficiente (última tentativa)

FALHA_CHAVE_DICT – Divergência entre os dados do recebedor e o DICT
FALHA_OPERACIONAL – Falha operacional durante a liquidação
EXPIRADO – Todas as tentativas de liquidação foram esgotadas
------
SOLREC_RECEBIDA – Solicitação de autorização de recorrência recebida
SOLREC_EXPIRADA – Solicitação de autorização de recorrência expirada
SOLREC_APROVADA_SEMRESPOSTA – Solicitação de autorização aprovada sem resposta do PSP recebedor
SOLREC_CANCELADA – Solicitação de autorização cancelada pelo recebedor

REC_CANCELADA – Recorrência cancelada pelo recebedor
REC_CONFIRMADA – Autorização de Pix automático confirmada

COBR_CANCELADA – Cobrança cancelada
COBR_REJEITADA – Cobrança rejeitada
COBR_RECEBIDA – Cobrança recebida
 */

enum class NotificationType {
    // Pix Automático
    SLD_INSUFICIENTE_NGRADE, SLD_INSUFICIENTE_ULTGRADE, FALTA_LIMITE_NGRADE, FALTA_LIMITE_ULTGRADE, FALHA_OPERACIONAL,

    // Pix Agendado
    RECEBIDO, LIQUIDADO, CANCELADO, FALTA_LIMITE, FALHA_CHAVE_DICT, EXPIRADO,

    // Pix Recorrente - Autorização
    SOLREC_RECEBIDA, SOLREC_EXPIRADA, SOLREC_APROVADA_SEMRESPOSTA, SOLREC_CANCELADA,

    // Pix Recorrente - Agendamento
    REC_CANCELADA, REC_CONFIRMADA,

    // Pix Recorrente - Cobrança
    COBR_CANCELADA, COBR_REJEITADA, COBR_RECEBIDA;

    fun toMandateAndRecurrenceLifecycleEventType(): MandateAndRecurrenceLifecycleEventType {
        return when (this) {
            SOLREC_RECEBIDA -> MandateAndRecurrenceLifecycleEventType.MANDATE_RECEIVED
            SOLREC_EXPIRADA -> MandateAndRecurrenceLifecycleEventType.MANDATE_EXPIRED
            SOLREC_APROVADA_SEMRESPOSTA -> MandateAndRecurrenceLifecycleEventType.MANDATE_APPROVED_WITHOUT_RESPONSE
            SOLREC_CANCELADA -> MandateAndRecurrenceLifecycleEventType.MANDATE_CANCELLED
            REC_CANCELADA -> MandateAndRecurrenceLifecycleEventType.RECURRENCE_CANCELLED
            REC_CONFIRMADA -> MandateAndRecurrenceLifecycleEventType.RECURRENCE_CONFIRMED
            else -> throw IllegalArgumentException("Unsupported notification type for callback: $this")
        }
    }

    fun toBillingLifecycleEventType(): BillingLifecycleEventType {
        return when (this) {
            COBR_CANCELADA -> BillingLifecycleEventType.BILLING_CANCELLED
            COBR_REJEITADA -> BillingLifecycleEventType.BILLING_REJECTED
            COBR_RECEBIDA -> BillingLifecycleEventType.BILLING_RECEIVED
            else -> throw IllegalArgumentException("Unsupported notification type for callback: $this")
        }
    }

    fun toPaymentLifecycleEventType(): PaymentLifecycleEventType {
        return when (this) {
            RECEBIDO -> PaymentLifecycleEventType.PAYMENT_SCHEDULED
            LIQUIDADO -> PaymentLifecycleEventType.PAYMENT_COMPLETED
            CANCELADO -> PaymentLifecycleEventType.PAYMENT_CANCELED
            FALTA_LIMITE -> PaymentLifecycleEventType.PAYMENT_FAILED_NO_DAILY_LIMIT_ULTGRADE
            SLD_INSUFICIENTE_NGRADE -> PaymentLifecycleEventType.PAYMENT_FAILED_INSUFFICIENT_BALANCE_NGRADE
            SLD_INSUFICIENTE_ULTGRADE -> PaymentLifecycleEventType.PAYMENT_FAILED_INSUFFICIENT_BALANCE_ULTGRADE
            FALHA_CHAVE_DICT -> PaymentLifecycleEventType.PAYMENT_FAILED_DICT_KEY_MISMATCH
            FALHA_OPERACIONAL -> PaymentLifecycleEventType.PAYMENT_FAILED_OPERATIONAL_ERROR
            EXPIRADO -> PaymentLifecycleEventType.PAYMENT_FAILED_EXPIRED
            FALTA_LIMITE_ULTGRADE -> PaymentLifecycleEventType.PAYMENT_FAILED_NO_DAILY_LIMIT_ULTGRADE
            FALTA_LIMITE_NGRADE -> PaymentLifecycleEventType.PAYMENT_FAILED_NO_DAILY_LIMIT_NGRADE
            else -> throw IllegalArgumentException("Unsupported notification type for callback: $this")
        }
    }
}

@JsonTypeInfo(
    include = JsonTypeInfo.As.PROPERTY,
    use = JsonTypeInfo.Id.NAME,
    property = "instanceType",
    visible = true,
)
sealed interface ParsedCallbackData {
    val provider: AutomaticPixProvider

    data class MandateAndRecurrenceLifecycleCallback(
        val eventType: MandateAndRecurrenceLifecycleEventType,
        val recurrenceId: AutomaticPixRecurringPaymentId,
        val mandateId: AutomaticPixMandateId,
        val payer: PixParticipantCallbackData,
        override val provider: AutomaticPixProvider = AutomaticPixProvider.ARBI,
    ) : ParsedCallbackData

    data class BillingLifecycleCallback(
        val eventType: BillingLifecycleEventType,
        val recurrenceId: AutomaticPixRecurringPaymentId,
        val mandateId: AutomaticPixMandateId,
        val endToEnd: String,
        val payer: PixParticipantCallbackData,
        override val provider: AutomaticPixProvider = AutomaticPixProvider.ARBI,
    ) : ParsedCallbackData

    data class PaymentLifecycleCallback(
        val eventType: PaymentLifecycleEventType,
        val endToEnd: String,
        val payer: PixParticipantCallbackData?,
        val payee: PixParticipantCallbackData?,
        val value: Long?,
        val schedulingDate: LocalDate?,
        val settlementDate: LocalDate?,
        val internalReference: String?,
        val message: String?,
        val paymentStatus: PaymentLifecycleStatus,
        override val provider: AutomaticPixProvider = AutomaticPixProvider.ARBI,
    ) : ParsedCallbackData
}

enum class MandateAndRecurrenceLifecycleEventType {
    MANDATE_RECEIVED,
    MANDATE_EXPIRED,
    MANDATE_APPROVED_WITHOUT_RESPONSE,
    MANDATE_CANCELLED,
    RECURRENCE_CANCELLED,
    RECURRENCE_CONFIRMED,
}

enum class BillingLifecycleEventType {
    BILLING_RECEIVED,
    BILLING_CANCELLED,
    BILLING_REJECTED,
}

enum class PaymentLifecycleEventType {
    PAYMENT_SCHEDULED,
    PAYMENT_COMPLETED,
    PAYMENT_CANCELED,
    PAYMENT_FAILED_NO_DAILY_LIMIT_NGRADE,
    PAYMENT_FAILED_NO_DAILY_LIMIT_ULTGRADE,
    PAYMENT_FAILED_INSUFFICIENT_BALANCE_NGRADE,
    PAYMENT_FAILED_INSUFFICIENT_BALANCE_ULTGRADE,
    PAYMENT_FAILED_DICT_KEY_MISMATCH,
    PAYMENT_FAILED_OPERATIONAL_ERROR,
    PAYMENT_FAILED_EXPIRED,
    ;
    fun isRetryable(): Boolean {
        return when (this) {
            PAYMENT_FAILED_NO_DAILY_LIMIT_NGRADE,
            PAYMENT_FAILED_INSUFFICIENT_BALANCE_NGRADE,
            PAYMENT_FAILED_INSUFFICIENT_BALANCE_ULTGRADE,
            PAYMENT_FAILED_OPERATIONAL_ERROR,
            -> true

            PAYMENT_SCHEDULED,
            PAYMENT_COMPLETED,
            PAYMENT_CANCELED,
            PAYMENT_FAILED_EXPIRED,
            PAYMENT_FAILED_NO_DAILY_LIMIT_ULTGRADE,
            PAYMENT_FAILED_DICT_KEY_MISMATCH,
            -> false
        }
    }
}

data class PixParticipantCallbackData(
    val ispbCode: String?,
    val taxId: String?,
    val accountType: String?,
    val accountNumber: String?,
    val agencyCode: Int?,
    val name: String?,
)

enum class PaymentLifecycleStatus {
    SCHEDULED,
    PROCESSING,
    STARTED,
    PAID,
    REJECTED,
    CANCELED,
    UNKNOWN, // Used when the status is not recognized or not provided
}