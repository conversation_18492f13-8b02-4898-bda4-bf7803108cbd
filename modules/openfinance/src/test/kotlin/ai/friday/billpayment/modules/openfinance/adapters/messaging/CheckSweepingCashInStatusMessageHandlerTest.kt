package ai.friday.billpayment.modules.openfinance.adapters.messaging

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.integrations.SweepingCashInId
import ai.friday.billpayment.app.integrations.SweepingCashInSource
import ai.friday.billpayment.app.integrations.SweepingCashInStatus
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.openfinance.adapters.OpenFinanceSweepingCashIn
import ai.friday.billpayment.modules.openfinance.app.CheckSweepingCashInStatusMessage
import ai.friday.billpayment.modules.openfinance.app.CreditorId
import ai.friday.billpayment.modules.openfinance.app.GetSweepingCashInError
import ai.friday.billpayment.modules.openfinance.app.SweepingAccountService
import ai.friday.billpayment.modules.openfinance.app.SweepingConsentId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.messaging.MessageHandlerResponse
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import software.amazon.awssdk.services.sqs.model.Message

class CheckSweepingCashInStatusMessageHandlerTest {

    private val sweepingAccountService: SweepingAccountService = mockk()

    private val handler = CheckSweepingCashInStatusMessageHandler(sweepingAccountService)

    private val sweepingCashInId = SweepingCashInId()

    private val messageBody = CheckSweepingCashInStatusMessage(sweepingCashInId.value)

    @Test
    fun `deve remover a mensagem da fila se nao encontrou a transferencia`() {
        every {
            sweepingAccountService.getSweepingCashIn(sweepingCashInId)
        } returns GetSweepingCashInError.ItemNotFound(sweepingCashInId).left()

        val result = handler.handleMessage(messageBody.toMessage())

        result shouldBe MessageHandlerResponse.delete()
    }

    @Test
    fun `deve manter a mensagem na fila se nao conseguir consultar o estado da transferencia`() {
        every {
            sweepingAccountService.getSweepingCashIn(sweepingCashInId)
        } returns GetSweepingCashInError.GenericError("message").left()

        val result = handler.handleMessage(messageBody.toMessage())

        result shouldBe MessageHandlerResponse.keep()
    }

    @ParameterizedTest
    @EnumSource(SweepingCashInStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["WAITING_SETTLEMENT", "FAILED", "SUCCESS"])
    fun `deve manter a mensagem na fila se estado da transferencia nao eh final`(status: SweepingCashInStatus) {
        every {
            sweepingAccountService.getSweepingCashIn(sweepingCashInId)
        } returns status.buildSweepingCashIn().right()

        val result = handler.handleMessage(messageBody.toMessage())

        result shouldBe MessageHandlerResponse.keep()
    }

    @ParameterizedTest
    @EnumSource(SweepingCashInStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["CREATED", "UNKNOWN"])
    fun `deve remover a mensagem da fila se estado da transferencia eh final`(status: SweepingCashInStatus) {
        every {
            sweepingAccountService.getSweepingCashIn(sweepingCashInId)
        } returns status.buildSweepingCashIn().right()

        val result = handler.handleMessage(messageBody.toMessage())

        result shouldBe MessageHandlerResponse.delete()
    }

    private fun CheckSweepingCashInStatusMessage.toMessage() = Message.builder().body(getObjectMapper().writeValueAsString(this)).build()

    private fun SweepingCashInStatus.buildSweepingCashIn() = OpenFinanceSweepingCashIn(
        id = SweepingCashInId(value = "legere"),
        walletId = WalletId(value = "rhoncus"),
        consentId = SweepingConsentId(value = "posse"),
        creditorId = CreditorId(value = "aptent"),
        amount = 8729,
        description = "eget",
        status = this,
        endToEnd = null,
        error = null,
        errorDescription = null,
        createdAt = getZonedDateTime(),
        updatedAt = getZonedDateTime(),
        requestSource = SweepingCashInSource.WEB_APP,
        approvalSource = SweepingCashInSource.WEB_APP,
        externalTransactionId = null,
    )
}