package ai.friday.billpayment.modules.openfinance.adapters.api

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.api.AUTHENTICATION_ATTRIBUTE_WALLET
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.SweepingParticipant
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.openfinance.app.ConsentService
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpStatus
import io.micronaut.security.authentication.Authentication
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class ParticipantsControllerTest {

    private val consentService: ConsentService = mockk()

    private val accountRepository: AccountRepository = mockk {
        every { findById(any()) } returns ACCOUNT
    }

    private val controller = ParticipantsController(
        consentService = consentService,
        accountRepository = accountRepository,
    )

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    private val authentication: Authentication = mockk {
        every {
            name
        } returns wallet.founder.accountId.value
        every {
            attributes
        } returns mapOf(AUTHENTICATION_ATTRIBUTE_WALLET to wallet)
    }

    private val participantsTO = listOf(
        SweepingParticipantTO(
            id = "*********-0000-0000-0000-*********002",
            name = "Banco do B",
            enabled = true,
        ),
        SweepingParticipantTO(
            id = "*********-0000-0000-0000-*********001",
            name = "Banco do C",
            enabled = true,
        ),
        SweepingParticipantTO(
            id = "*********-0000-0000-0000-************",
            name = "BCG",
            enabled = true,
        ),
    )

    private val participants = listOf(
        SweepingParticipant(
            id = "*********-0000-0000-0000-************",
            name = "BCG",
            shortName = "BCG",
            compe = "",
            ispb = "",
            temporarilyUnavailable = false,
            restrictedTo = emptyList(),
        ),
        SweepingParticipant(
            id = "*********-0000-0000-0000-*********001",
            name = "Banco do C",
            shortName = "Banco do C",
            compe = "",
            ispb = "",
            temporarilyUnavailable = false,
            restrictedTo = emptyList(),
        ),
        SweepingParticipant(
            id = "*********-0000-0000-0000-*********002",
            name = "Banco do B",
            shortName = "Banco do B",
            compe = "",
            ispb = "",
            temporarilyUnavailable = false,
            restrictedTo = emptyList(),
        ),
    )

    @DisplayName("ao solicitar a lista de participantes")
    @Nested
    inner class GetParticipantsTest {
        @Test
        fun `deve retornar a lista ordenada de participantes em caso de sucesso`() {
            every {
                consentService.getParticipants()
            } returns participants

            val response = controller.getParticipants(authentication)

            response.status shouldBe HttpStatus.OK
            response.body.isPresent shouldBe true
            response.body.get() shouldBe participantsTO
        }
    }
}