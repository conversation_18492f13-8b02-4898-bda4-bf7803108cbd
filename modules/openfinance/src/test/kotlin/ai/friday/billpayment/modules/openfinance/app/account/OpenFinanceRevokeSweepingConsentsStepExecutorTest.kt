package ai.friday.billpayment.modules.openfinance.app.account

import ai.friday.billpayment.app.account.CloseWalletStepError
import ai.friday.billpayment.app.account.CloseWalletStepStatus
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.openfinance.app.ConsentService
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceSweepingAccountError
import ai.friday.billpayment.modules.openfinance.app.SweepingConsent
import ai.friday.billpayment.modules.openfinance.app.SweepingConsentId
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlin.test.junit.JUnitAsserter.fail
import org.junit.jupiter.api.Test

class OpenFinanceRevokeSweepingConsentsStepExecutorTest {

    private val consentService = mockk<ConsentService>()

    private val openFinanceRevokeSweepingConsentsStepExecutor = OpenFinanceRevokeSweepingConsentsStepExecutor(
        consentService = consentService,
    )

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    private val sweepingConsent = mockk<SweepingConsent> {
        every { id } returns SweepingConsentId("sweepingConsentId")
    }

    @Test
    fun `deve retornar sucesso se não houver nenhum consentimento`() {
        every { consentService.getSweepingConsent(any<WalletId>()) } returns OpenFinanceSweepingAccountError.ConsentNotFound.left()

        val result = openFinanceRevokeSweepingConsentsStepExecutor.execute(wallet).getOrElse {
            fail("Esperado uma lista vazia, mas retornou um erro: $it")
        }

        result shouldBe CloseWalletStepStatus.Success

        verify(exactly = 0) {
            consentService.revokeConsent(any<SweepingConsentId>(), any())
        }
    }

    @Test
    fun `deve retornar sucesso e remover os consentimentos`() {
        every { consentService.getSweepingConsent(any<WalletId>()) } returns sweepingConsent.right()
        every { consentService.revokeConsent(any<SweepingConsentId>(), any()) } returns mockk<SweepingConsent>().right()

        val result = openFinanceRevokeSweepingConsentsStepExecutor.execute(wallet).getOrElse {
            fail("Esperado uma lista vazia, mas retornou um erro: $it")
        }

        result shouldBe CloseWalletStepStatus.Success

        verify(exactly = 1) {
            consentService.revokeConsent(any<SweepingConsentId>(), any())
        }
    }

    @Test
    fun `deve retornar falha se não conseguir remover algum consentimento`() {
        every { consentService.getSweepingConsent(any<WalletId>()) } returns sweepingConsent.right()
        every { consentService.revokeConsent(any<SweepingConsentId>(), any()) } returns OpenFinanceSweepingAccountError.ConsentNotFound.left()

        openFinanceRevokeSweepingConsentsStepExecutor.execute(wallet).map {
            fail("Era esperado um erro, mas retornou: $it")
        }.getOrElse {
            it.shouldBeTypeOf<CloseWalletStepError.GenericError>()
        }

        verify(exactly = 1) {
            consentService.revokeConsent(any<SweepingConsentId>(), any())
        }
    }
}