package ai.friday.billpayment.modules.openfinance.app

import DynamoDBUtils
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.integrations.CrmRepository
import ai.friday.billpayment.app.integrations.NotificationService
import ai.friday.billpayment.app.integrations.OpenFinanceParticipantId
import ai.friday.billpayment.app.integrations.SweepingParticipant
import ai.friday.billpayment.app.notification.BillPaymentNotification
import ai.friday.billpayment.app.notification.TemplatesConfiguration
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.balance
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.createOpenFinanceDataTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.modules.openfinance.adapters.OpenFinanceBankAccountDBRepository
import ai.friday.billpayment.modules.openfinance.adapters.OpenFinanceBankAccountDynamoDAO
import ai.friday.billpayment.modules.openfinance.adapters.OpenFinanceDataConsentDBRepository
import ai.friday.billpayment.modules.openfinance.adapters.OpenFinanceDataConsentDynamoDAO
import ai.friday.billpayment.modules.openfinance.adapters.OpenFinanceSweepingAccountDBRepository
import ai.friday.billpayment.modules.openfinance.adapters.OpenFinanceSweepingAccountDynamoDAO
import ai.friday.morning.date.BrazilZonedDateTimeSupplier
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.maps.shouldContain
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import java.net.URI
import java.time.ZonedDateTime
import java.util.UUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.EnumSource
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient

class ConsentServiceTest {

    val sweepingLimitsConfiguration = SweepingLimits(
        totalAllowedAmount = 1000_00,
        transactionLimit = 1000_00,
        periodicLimits = PeriodicLimits(
            day = PeriodicLimit(
                quantityLimit = 10,
                transactionLimit = 1_000_00,
            ),
            week = PeriodicLimit(
                quantityLimit = 50,
                transactionLimit = 5_000_00,
            ),
            month = PeriodicLimit(
                quantityLimit = 100,
                transactionLimit = 10_000_00,
            ),
            year = PeriodicLimit(
                quantityLimit = 300,
                transactionLimit = 300_000_00,
            ),
        ),
    )

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    private val dynamoDB: AmazonDynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val asyncClient: DynamoDbEnhancedAsyncClient =
        DynamoDbEnhancedAsyncClient
            .builder()
            .dynamoDbClient(DynamoDBUtils.getDynamoDbClientAsync())
            .build()
    private val client: DynamoDbEnhancedClient =
        DynamoDbEnhancedClient
            .builder()
            .dynamoDbClient(DynamoDBUtils.getDynamoDbClient())
            .build()

    private val walletService: WalletService = mockk {
        every {
            findPrimaryWallet(wallet.founder.accountId)
        } returns wallet
        every {
            findWallet(wallet.id)
        } returns wallet
    }

    private val sweepingAccountRepository: OpenFinanceSweepingAccountRepository =
        spyk(OpenFinanceSweepingAccountDBRepository(OpenFinanceSweepingAccountDynamoDAO(asyncClient)))

    private val whatsappTemplateConnected = "connected"
    private val whatsappTemplateConnectedWithDataIncentive = "connected_with_data_incentive"
    private val whatsappTemplateConnectionFailed = "failed"
    private val whatsappTemplateEdit = "edit"
    private val whatsappTemplateEditFailed = "edit_failed"
    private val whatsappTemplateDataConsentConnected = "data-consent-connected"
    private val whatsappTemplateDataConsentConnectionFailed = "data-consent-connection-failed"
    private val whatsappTemplateTransferError = "error"

    private val notificationService: NotificationService = mockk(relaxed = true)
    private val templatesConfiguration: TemplatesConfiguration = mockk {
        every { whatsappTemplates } returns mockk {
            every { sweepingAccountConnected } returns whatsappTemplateConnected
            every { sweepingAccountConnectedWithDataIncentive } returns whatsappTemplateConnectedWithDataIncentive
            every { sweepingAccountConnectionFailed } returns whatsappTemplateConnectionFailed
            every { sweepingAccountEdit } returns whatsappTemplateEdit
            every { sweepingAccountEditFailed } returns whatsappTemplateEditFailed
            every { dataConsentConnected } returns whatsappTemplateDataConsentConnected
            every { dataConsentConnectionFailed } returns whatsappTemplateDataConsentConnectionFailed
            every { sweepingCashInError } returns whatsappTemplateTransferError
            every { sweepingCashInInsufficientBalanceError } returns "balance"
            every { sweepingTransferChatbotRetryableError } returns "sweepingTransferChatbotRetryableError"
            every { sweepingTransferChatbotParticipantBalanceError } returns "sweepingTransferChatbotParticipantBalanceError"
            every { sweepingTransferChatbotParticipantLimitError } returns "sweepingTransferChatbotParticipantLimitError"
            every { sweepingTransferChatbotInvalidConsentError } returns "sweepingTransferChatbotInvalidConsentError"
            every { sweepingTransferChatbotConsentLimitError } returns "sweepingTransferChatbotConsentLimitError"
            every { sweepingTransferWebappRetryableError } returns "sweepingTransferWebappRetryableError"
            every { sweepingTransferWebappParticipantBalanceError } returns "sweepingTransferWebappParticipantBalanceError"
            every { sweepingTransferWebappParticipantLimitError } returns "sweepingTransferWebappParticipantLimitError"
            every { sweepingTransferWebappInvalidConsentError } returns "sweepingTransferWebappInvalidConsentError"
            every { sweepingTransferWebappConsentLimitError } returns "sweepingTransferWebappConsentLimitError"
        }
    }

    private val openFinanceAdapter: OpenFinanceAdapter = mockk()
    private val accountService: AccountService = mockk() {
        every { findAccountPaymentMethodByIdAndAccountId(any(), any()) } returns balance
        every { findAccountById(wallet.founder.accountId) } returns walletFixture.founderAccount
    }
    private val crmRepository: CrmRepository = mockk {
        every { publishEvent(any<AccountId>(), any(), any()) } just runs
    }

    val sweepingParticipant = SweepingParticipant(
        id = "1",
        name = "Banco",
        shortName = "Banco",
        compe = "compe",
        ispb = "ispbe",
        restrictedTo = emptyList(),
    )

    val sweepingDebtor = SweepingDebtor(
        ispb = "********",
        routingNumber = "1111",
        accountNumber = "********",
        accountType = "CACC",
        bankName = "Bradesco",
        bankAccountNumberId = BankAccountNumberId(),
    )

    private val dataConsentRepository = OpenFinanceDataConsentDBRepository(OpenFinanceDataConsentDynamoDAO(client))

    private val bankAccountRepository = OpenFinanceBankAccountDBRepository(OpenFinanceBankAccountDynamoDAO(client))

    private val consentService = ConsentService(
        accountService = accountService,
        dataConsentRepository = dataConsentRepository,
        bankAccountRepository = bankAccountRepository,
        sweepingAccountRepository = sweepingAccountRepository,
        openFinanceAdapter = openFinanceAdapter,
        participants = listOf(sweepingParticipant),
        notificationService = notificationService,
        templatesConfiguration = templatesConfiguration,
        walletService = walletService,
        crmRepository = crmRepository,
    )

    @BeforeEach
    fun setUp() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        loadAccountIntoDb(dynamoDB, accountId = wallet.founder.accountId)
        createOpenFinanceDataTable(dynamoDB)
    }

    @Nested
    @DisplayName("ao consultar um consentimento")
    inner class GetConsentsTest {

        @Nested
        @DisplayName("de dados")
        inner class GetDataConsentTest {
            @Test
            fun `deve retornar um consentimento temporario enquanto nao tem dados de conta bancaria`() {
                val dataConsent = setupDataConsent(status = ConsentStatus.AWAITING_AUTHORISATION)

                val consents = consentService.getActiveBankAccountDataConsents(dataConsent.accountId)

                consents.size shouldBe 1
                consents shouldHave dataConsent
            }

            @Test
            fun `deve retornar um consentimento definitivo para cada conta quando tem dados de conta bancaria`() {
                val dataConsent = setupDataConsent(status = ConsentStatus.AUTHORISED)
                val bankAccount1 = setupBankAccount(dataConsent)
                val bankAccount2 = setupBankAccount(dataConsent)

                val consents = consentService.getActiveBankAccountDataConsents(dataConsent.accountId)

                consents.size shouldBe 2
                consents shouldHave bankAccount1
                consents shouldHave bankAccount2
            }
        }

        @ParameterizedTest
        @EnumSource(ConsentStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["REJECTED", "REVOKED", "CONSUMED", "CANCELED"])
        fun `deve retornar os consentimentos ativos`(status: ConsentStatus) {
            val sweepingConsent = setupSweepingConsent(status = status)
            val dataConsent = setupDataConsent(status = status)

            val consents = consentService.getActiveConsents(sweepingConsent.accountId, sweepingConsent.walletId)

            consents.size shouldBe 2
            consents shouldHave sweepingConsent
            consents shouldHave dataConsent
        }

        @ParameterizedTest
        @EnumSource(ConsentStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["AWAITING_AUTHORISATION", "PARTIALLY_ACCEPTED", "AUTHORISED"])
        fun `nao deve retornar consentimentos inativos`(status: ConsentStatus) {
            val sweepingConsent = setupSweepingConsent(status = status)
            val dataConsent = setupDataConsent(status = status)
            setupBankAccount(dataConsent)
            setupBankAccount(dataConsent)

            val consents = consentService.getActiveConsents(sweepingConsent.accountId, sweepingConsent.walletId)

            consents.shouldBeEmpty()
        }

        @Test
        fun `deve retornar apenas os consentimentos do usuário`() {
            val sweepingConsent = setupSweepingConsent(status = ConsentStatus.AUTHORISED)
            val dataConsent = setupDataConsent(status = ConsentStatus.AUTHORISED)
            val bankAccount1 = setupBankAccount(dataConsent)
            val bankAccount2 = setupBankAccount(dataConsent)
            setupSweepingConsent(status = ConsentStatus.AUTHORISED, walletId = WalletId())
            setupDataConsent(status = ConsentStatus.AUTHORISED, accountId = AccountId())

            val consents = consentService.getActiveConsents(sweepingConsent.accountId, sweepingConsent.walletId)

            consents.size shouldBe 3
            consents shouldHave sweepingConsent
            consents shouldHave bankAccount1
            consents shouldHave bankAccount2
        }

        @Test
        fun `não deve retornar consentimento se o usuario nao e o fundador da carteira`() {
            val sweepingConsent = setupSweepingConsent(status = ConsentStatus.AUTHORISED)
            val dataConsent = setupDataConsent(status = ConsentStatus.AUTHORISED, accountId = AccountId())

            val consents = consentService.getActiveConsents(dataConsent.accountId, sweepingConsent.walletId)

            consents.shouldBeEmpty()
        }

        private infix fun List<OpenFinanceBankAccountConsent>.shouldHave(otherConsent: SweepingConsent) {
            val consent = firstOrNull {
                it.id == otherConsent.id
            }

            consent.shouldNotBeNull()
            consent.status shouldBe otherConsent.status
            consent.bankAccountData.id shouldBe otherConsent.debtor?.bankAccountNumberId
            consent.bankAccountData.bankName shouldBe otherConsent.debtor?.bankName
            consent.bankAccountData.ispb shouldBe otherConsent.debtor?.ispb
            consent.bankAccountData.routingNumber shouldBe otherConsent.debtor?.routingNumber
            consent.bankAccountData.accountNumber shouldBe otherConsent.debtor?.accountNumber
        }

        private infix fun List<OpenFinanceBankAccountConsent>.shouldHave(bankAccount: OpenFinanceBankAccount) {
            val consent = firstOrNull {
                it.id == bankAccount.dataConsentId && it.bankAccountData.id == bankAccount.bankAccountNumberId
            }

            consent.shouldNotBeNull()
            consent.status shouldBe ConsentStatus.AUTHORISED
            consent.bankAccountData.bankName shouldBe bankAccount.brandName
            consent.bankAccountData.ispb shouldBe bankAccount.ispb
            consent.bankAccountData.routingNumber shouldBe bankAccount.routingNumber
            consent.bankAccountData.accountNumber shouldBe bankAccount.accountNumber
        }

        private infix fun List<OpenFinanceBankAccountConsent>.shouldHave(otherConsent: DataConsent) {
            val consent = firstOrNull {
                it.id == otherConsent.dataConsentId
            }

            consent.shouldNotBeNull()
            consent.status shouldBe otherConsent.status
            consent.participantId shouldBe otherConsent.participantId
        }
    }

    @Nested
    @DisplayName("transferência inteligente")
    inner class SweepingConsentTest {

        @Nested
        @DisplayName("ao solicitar a criação de um consentimento")
        inner class CreateConsentTest {

            @Test
            fun `e o id do participant nao existir deve retornar InvalidParticipant`() {
                val result = consentService.createSweepingConsent(wallet.founder.accountId, OpenFinanceParticipantId("invalid"))

                result.isLeft() shouldBe true
                result.onLeft {
                    it.shouldBeTypeOf<OpenFinanceSweepingAccountError.InvalidParticipant>()
                }
            }

            @Test
            fun `e a chamada ao open finance falhar deve retornar ConsentCreationError`() {
                every { openFinanceAdapter.createSweepingConsent(any(), any(), any(), any()) } returns OpenFinanceSweepingAccountError.ConsentCreationError("mocked error").left()

                val result = consentService.createSweepingConsent(wallet.founder.accountId, OpenFinanceParticipantId("1"), defaultSweepingLimits)

                result.isLeft() shouldBe true
                result.onLeft {
                    it.shouldBeTypeOf<OpenFinanceSweepingAccountError.ConsentCreationError>()
                }
            }

            @ParameterizedTest
            @CsvSource(
                value = [
                    "AWAITING_AUTHORISATION, 2024-10-17T02:37:44.000Z, 2026-10-17T02:37:44.000Z",
                    "PARTIALLY_ACCEPTED    , null                    , null",
                    "REJECTED              , 2024-10-17T02:37:44.000Z, 2026-10-17T02:37:44.000Z",
                    "REVOKED               , null                    , null",
                    "CONSUMED              , 2024-10-17T02:37:44.000Z, 2026-10-17T02:37:44.000Z",
                    "CANCELED              , null                    , null",
                    "null                  , null                    , null",
                ],
                nullValues = ["null"],
            )
            fun `e nao existir consentimento autorizado na carteira deve retornar uma url para autorizar a conexao na detentora`(status: ConsentStatus?, startDateTime: String?, expirationDateTime: String?) {
                status?.setupConsent()

                val participantId = OpenFinanceParticipantId(value = "1")

                val response = SweepingConsentLink(
                    sweepingConsentId = SweepingConsentId("ID_1"),
                    creditorId = CreditorId("ID_2"),
                    consentUrl = URI.create("http://example.com").toURL(),
                    startDateTime = startDateTime?.let { BrazilZonedDateTimeSupplier.parseToZonedDateTime(it) },
                    expirationDateTime = expirationDateTime?.let { BrazilZonedDateTimeSupplier.parseToZonedDateTime(it) },
                    configuration = sweepingLimitsConfiguration,
                    participantId = participantId,
                )
                every { openFinanceAdapter.createSweepingConsent(any(), any(), any(), any()) } returns response.right()

                val result = consentService.createSweepingConsent(wallet.founder.accountId, participantId = participantId, sweepingLimitsConfiguration)

                result.isRight() shouldBe true

                with(sweepingAccountRepository.findByWalletId(wallet.id).maxByOrNull { it.createdAt }) {
                    this.shouldNotBeNull()
                    this.participantId shouldBe participantId
                    this.id shouldBe response.sweepingConsentId
                    this.creditorId shouldBe response.creditorId
                    this.url shouldBe response.consentUrl
                    this.startDateTime?.toInstant() shouldBe response.startDateTime?.toInstant()
                    this.expirationDateTime?.toInstant() shouldBe response.expirationDateTime?.toInstant()
                    this.configuration shouldBe sweepingLimitsConfiguration
                }
            }

            private fun ConsentStatus.setupConsent() {
                val currentTime = ZonedDateTime.now()
                sweepingAccountRepository.save(
                    SweepingConsent(
                        accountId = wallet.founder.accountId,
                        id = SweepingConsentId(value = "leo"),
                        creditorId = CreditorId(value = "prompta"),
                        walletId = wallet.id,
                        participantId = OpenFinanceParticipantId("1"),
                        status = this,
                        startDateTime = currentTime,
                        expirationDateTime = currentTime.plusYears(2),
                        createdAt = currentTime,
                        updatedAt = currentTime,
                        url = URI.create("http://example.com").toURL(),
                        configuration = sweepingLimitsConfiguration,
                        lastSuccessfulCashIn = null,
                    ),
                )
            }
        }

        @Nested
        @DisplayName("ao solicitar o cancelamento de um consentimento")
        inner class RevokeConsentTest {

            @Test
            fun `should update consent status on revoke`() {
                val consent = setupSweepingConsent(status = ConsentStatus.AUTHORISED)

                every { openFinanceAdapter.revokeSweepingConsent(any()) } returns Unit.right()
                val result = consentService.revokeConsent(consent.id, consent.accountId)
                result.isRight() shouldBe true

                sweepingAccountRepository.findByWalletId(wallet.id).firstOrNull {
                    it.walletId == wallet.id
                }!!.let {
                    it.status shouldBe ConsentStatus.REVOKED
                }

                val eventSlot = slot<String>()
                val paramsSlot = slot<Map<String, String>>()
                verify {
                    crmRepository.publishEvent(wallet.founder.accountId, capture(eventSlot), capture(paramsSlot))
                }
                eventSlot.captured shouldBe "open_finance_conta_desconectada"
            }
        }

        @Nested
        @DisplayName("ao atualizar o estado de um consentimento")
        inner class UpdateConsentTest {
            @Nested
            @DisplayName("para autorizado")
            inner class AuthorizeConsentTest {
                @Test
                fun `deve notificar o usuario e atualizar o CRM`() {
                    val account = walletFixture.founderAccount.copy(
                        configuration = walletFixture.founderAccount.configuration.copy(
                            groups = emptyList(),
                        ),
                    )

                    every { accountService.findAccountById(wallet.founder.accountId) } returns account

                    val consent = setupSweepingConsent()

                    val status = ConsentStatus.AUTHORISED

                    val result = consentService.updateConsentAndNotify(
                        consentId = consent.id,
                        status = status,
                        debtor = sweepingDebtor,
                    )
                    result.isRight() shouldBe true
                    result.map {
                        it.status shouldBe status
                    }

                    val accountSlot = slot<Account>()
                    val notificationSlot = slot<(account: Account) -> BillPaymentNotification?>()
                    verify {
                        notificationService.notifyAccount(capture(accountSlot), any(), capture(notificationSlot))
                    }
                    accountSlot.captured.accountId shouldBe wallet.founder.accountId
                    notificationSlot.captured.invoke(accountSlot.captured)?.template?.value shouldBe whatsappTemplateConnected

                    val eventSlot = slot<String>()
                    val paramsSlot = slot<Map<String, String>>()
                    verify {
                        crmRepository.publishEvent(wallet.founder.accountId, capture(eventSlot), capture(paramsSlot))
                    }
                    eventSlot.captured shouldBe "open_finance_conta_conectada"
                    paramsSlot.captured shouldContain Pair("bankName", "Bradesco")
                }

                @Test
                fun `deve notificar com template de incentivo quando usuario esta nos grupos alpha e beta`() {
                    val account = walletFixture.founderAccount.copy(
                        configuration = walletFixture.founderAccount.configuration.copy(
                            groups = listOf(AccountGroup.ALPHA, AccountGroup.BETA),
                        ),
                    )

                    every { accountService.findAccountById(wallet.founder.accountId) } returns account

                    val consent = setupSweepingConsent()

                    val status = ConsentStatus.AUTHORISED

                    val result = consentService.updateConsentAndNotify(
                        consentId = consent.id,
                        status = status,
                        debtor = sweepingDebtor,
                    )
                    result.isRight() shouldBe true
                    result.map {
                        it.status shouldBe status
                    }

                    val accountSlot = slot<Account>()
                    val notificationSlot = slot<(account: Account) -> BillPaymentNotification?>()
                    verify {
                        notificationService.notifyAccount(capture(accountSlot), any(), capture(notificationSlot))
                    }
                    accountSlot.captured.accountId shouldBe wallet.founder.accountId
                    notificationSlot.captured.invoke(accountSlot.captured)?.template?.value shouldBe whatsappTemplateConnectedWithDataIncentive

                    val eventSlot = slot<String>()
                    val paramsSlot = slot<Map<String, String>>()
                    verify {
                        crmRepository.publishEvent(wallet.founder.accountId, capture(eventSlot), capture(paramsSlot))
                    }
                    eventSlot.captured shouldBe "open_finance_conta_conectada"
                    paramsSlot.captured shouldContain Pair("bankName", "Bradesco")
                }

                @Test
                fun `deve notificar sucesso quando todos os consentimentos sao aprovados`() {
                    val firstConsent = setupSweepingConsent(status = ConsentStatus.AWAITING_AUTHORISATION)
                    val secondConsent = setupSweepingConsent(status = ConsentStatus.AWAITING_AUTHORISATION)

                    val result1 = consentService.updateConsentAndNotify(
                        consentId = firstConsent.id,
                        status = ConsentStatus.AUTHORISED,
                        debtor = sweepingDebtor,
                    )
                    result1.isRight() shouldBe true

                    verify(exactly = 0) {
                        notificationService.sendNotification(any(), any(), any())
                    }

                    val result2 = consentService.updateConsentAndNotify(
                        consentId = secondConsent.id,
                        status = ConsentStatus.AUTHORISED,
                        debtor = sweepingDebtor,
                    )
                    result2.isRight() shouldBe true

                    val accountSlot = slot<Account>()
                    val notificationSlot = slot<(account: Account) -> BillPaymentNotification?>()
                    verify {
                        notificationService.notifyAccount(capture(accountSlot), any(), capture(notificationSlot))
                    }
                    accountSlot.captured.accountId shouldBe wallet.founder.accountId
                    notificationSlot.captured.invoke(accountSlot.captured)?.template?.value shouldBe whatsappTemplateConnected
                }

                @Test
                fun `deve notificar sucesso mesmo quando um dos consentimentos falhou`() {
                    val firstConsent = setupSweepingConsent(status = ConsentStatus.AWAITING_AUTHORISATION)
                    val secondConsent = setupSweepingConsent(status = ConsentStatus.AWAITING_AUTHORISATION)

                    val result1 = consentService.updateConsentAndNotify(
                        consentId = firstConsent.id,
                        status = ConsentStatus.AUTHORISED,
                        debtor = sweepingDebtor,
                    )
                    result1.isRight() shouldBe true

                    verify(exactly = 0) {
                        notificationService.sendNotification(any(), any(), any())
                    }

                    val result2 = consentService.updateConsentAndNotify(
                        consentId = secondConsent.id,
                        status = ConsentStatus.REJECTED,
                        debtor = null,
                    )
                    result2.isRight() shouldBe true

                    val accountSlot = slot<Account>()
                    val notificationSlot = slot<(account: Account) -> BillPaymentNotification?>()
                    verify {
                        notificationService.notifyAccount(capture(accountSlot), any(), capture(notificationSlot))
                    }
                    accountSlot.captured.accountId shouldBe wallet.founder.accountId
                    notificationSlot.captured.invoke(accountSlot.captured)?.template?.value shouldBe whatsappTemplateConnected
                }

                @Test
                fun `deve revogar consentimentos antigos ativos`() {
                    val olderConsent = setupSweepingConsent(status = ConsentStatus.AUTHORISED)
                    val consentToBeAuthorized = setupSweepingConsent(createdAt = olderConsent.createdAt.plusSeconds(1))

                    val status = ConsentStatus.AUTHORISED

                    val result = consentService.updateConsentAndNotify(
                        consentId = consentToBeAuthorized.id,
                        status = status,
                        debtor = sweepingDebtor,
                    )
                    result.isRight() shouldBe true

                    sweepingAccountRepository.findBySweepingAccountId(olderConsent.id).status shouldBe ConsentStatus.REVOKED
                }

                @ParameterizedTest
                @EnumSource(ConsentStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["AWAITING_AUTHORISATION", "PARTIALLY_ACCEPTED", "AUTHORISED"])
                fun `nao deve revogar consentimentos antigos inativos`(olderStatus: ConsentStatus) {
                    val olderConsent = setupSweepingConsent(status = olderStatus)
                    val consentToBeAuthorized = setupSweepingConsent()

                    val status = ConsentStatus.AUTHORISED

                    val result = consentService.updateConsentAndNotify(
                        consentId = consentToBeAuthorized.id,
                        status = status,
                        debtor = sweepingDebtor,
                    )
                    result.isRight() shouldBe true

                    with(sweepingAccountRepository.findBySweepingAccountId(olderConsent.id)) {
                        this.status shouldBe olderConsent.status
                        this.updatedAt shouldBe olderConsent.updatedAt
                    }
                }

                @Test
                fun `nao deve revogar consentimentos mais recentes`() {
                    val consentToBeAuthorized = setupSweepingConsent()
                    val newerConsent = setupSweepingConsent()

                    val status = ConsentStatus.AUTHORISED

                    val result = consentService.updateConsentAndNotify(
                        consentId = consentToBeAuthorized.id,
                        status = status,
                        debtor = sweepingDebtor,
                    )
                    result.isRight() shouldBe true

                    with(sweepingAccountRepository.findBySweepingAccountId(newerConsent.id)) {
                        this.status shouldBe newerConsent.status
                        this.updatedAt shouldBe newerConsent.updatedAt
                    }
                }
            }

            @Nested
            @DisplayName("para revogado")
            inner class RevokeConsentTest {
                @Test
                fun `deve atualizar o CRM`() {
                    val consent = setupSweepingConsent()

                    val status = ConsentStatus.REVOKED

                    val result = consentService.updateConsentAndNotify(consent.id, status, null)
                    result.isRight() shouldBe true
                    result.map {
                        it.status shouldBe status
                    }

                    verify(exactly = 0) {
                        notificationService.sendNotification(any(), any(), any())
                    }

                    val eventSlot = slot<String>()
                    val paramsSlot = slot<Map<String, String>>()
                    verify {
                        crmRepository.publishEvent(wallet.founder.accountId, capture(eventSlot), capture(paramsSlot))
                    }
                    eventSlot.captured shouldBe "open_finance_conta_desconectada"
                }
            }

            @Nested
            @DisplayName("para rejeitado")
            inner class RejectConsentTest {
                @Test
                fun `deve notificar o usuario se nao existir consentimento pendente`() {
                    val consent = setupSweepingConsent()

                    val status = ConsentStatus.REJECTED

                    val result = consentService.updateConsentAndNotify(consent.id, status, null)
                    result.isRight() shouldBe true
                    result.map {
                        it.status shouldBe status
                    }

                    val accountSlot = slot<Account>()
                    val notificationSlot = slot<(account: Account) -> BillPaymentNotification?>()
                    verify {
                        notificationService.notifyAccount(capture(accountSlot), any(), capture(notificationSlot))
                    }
                    accountSlot.captured.accountId shouldBe wallet.founder.accountId
                    notificationSlot.captured.invoke(accountSlot.captured)?.template?.value shouldBe whatsappTemplateConnectionFailed

                    verify {
                        crmRepository wasNot called
                    }
                }

                @Test
                fun `deve notificar erro quando todos os consentimentos falham`() {
                    val firstConsent = setupSweepingConsent(status = ConsentStatus.AWAITING_AUTHORISATION)
                    val secondConsent = setupSweepingConsent(status = ConsentStatus.AWAITING_AUTHORISATION)

                    val result1 = consentService.updateConsentAndNotify(
                        consentId = firstConsent.id,
                        status = ConsentStatus.REJECTED,
                        debtor = null,
                    )
                    result1.isRight() shouldBe true

                    verify(exactly = 0) {
                        notificationService.sendNotification(any(), any(), any())
                    }

                    val result2 = consentService.updateConsentAndNotify(
                        consentId = secondConsent.id,
                        status = ConsentStatus.REJECTED,
                        debtor = null,
                    )
                    result2.isRight() shouldBe true

                    val accountSlot = slot<Account>()
                    val notificationSlot = slot<(account: Account) -> BillPaymentNotification?>()
                    verify {
                        notificationService.notifyAccount(capture(accountSlot), any(), capture(notificationSlot))
                    }
                    accountSlot.captured.accountId shouldBe wallet.founder.accountId
                    notificationSlot.captured.invoke(accountSlot.captured)?.template?.value shouldBe whatsappTemplateConnectionFailed
                }

                @Test
                fun `nao deve notificar o usuario quando ainda existem consentimentos pendentes`() {
                    val firstConsent = setupSweepingConsent(status = ConsentStatus.AWAITING_AUTHORISATION)
                    setupSweepingConsent(status = ConsentStatus.AWAITING_AUTHORISATION)

                    val result1 = consentService.updateConsentAndNotify(
                        consentId = firstConsent.id,
                        status = ConsentStatus.REJECTED,
                        debtor = null,
                    )
                    result1.isRight() shouldBe true

                    verify(exactly = 0) {
                        notificationService.sendNotification(any(), any(), any())
                    }
                }
            }
        }

        @Nested
        @DisplayName("ao editar um consentimento")
        inner class EditConsentTest {
            @BeforeEach
            fun setup() {
                setupSweepingConsent(
                    status = ConsentStatus.AUTHORISED,
                    createdAt = getZonedDateTime().minusMinutes(11),
                )
            }

            @Test
            fun `deve notificar edição quando o consentimento pendente é aprovado após 10 minutos do último autorizado`() {
                val pendingConsent = setupSweepingConsent(
                    status = ConsentStatus.AWAITING_AUTHORISATION,
                )

                val result = consentService.updateConsentAndNotify(
                    consentId = pendingConsent.id,
                    status = ConsentStatus.AUTHORISED,
                    debtor = sweepingDebtor,
                )
                result.isRight() shouldBe true

                val accountSlot = slot<Account>()
                val notificationSlot = slot<(account: Account) -> BillPaymentNotification?>()
                verify {
                    notificationService.notifyAccount(capture(accountSlot), any(), capture(notificationSlot))
                }
                accountSlot.captured.accountId shouldBe wallet.founder.accountId
                notificationSlot.captured.invoke(accountSlot.captured)?.template?.value shouldBe whatsappTemplateEdit
            }

            @Test
            fun `deve notificar falha de edição quando o consentimento pendente é rejeitado após 10 minutos do último autorizado`() {
                val pendingConsent = setupSweepingConsent(
                    status = ConsentStatus.AWAITING_AUTHORISATION,
                )

                val result = consentService.updateConsentAndNotify(
                    consentId = pendingConsent.id,
                    status = ConsentStatus.REJECTED,
                    debtor = null,
                )
                result.isRight() shouldBe true

                val accountSlot = slot<Account>()
                val notificationSlot = slot<(account: Account) -> BillPaymentNotification?>()
                verify {
                    notificationService.notifyAccount(capture(accountSlot), any(), capture(notificationSlot))
                }
                accountSlot.captured.accountId shouldBe wallet.founder.accountId
                notificationSlot.captured.invoke(accountSlot.captured)?.template?.value shouldBe whatsappTemplateEditFailed
            }

            @Test
            fun `não deve notificar quando existem múltiplos consentimentos pendentes e o primeiro falha`() {
                val firstPendingConsent = setupSweepingConsent(
                    status = ConsentStatus.AWAITING_AUTHORISATION,
                )

                setupSweepingConsent(
                    status = ConsentStatus.AWAITING_AUTHORISATION,
                )

                val result = consentService.updateConsentAndNotify(
                    consentId = firstPendingConsent.id,
                    status = ConsentStatus.REJECTED,
                    debtor = null,
                )
                result.isRight() shouldBe true

                verify(exactly = 0) {
                    notificationService.sendNotification(any(), any(), any())
                }
            }

            @Test
            fun `deve notificar sucesso de edição quando o último consentimento pendente é aprovado`() {
                val firstPendingConsent = setupSweepingConsent(
                    status = ConsentStatus.AWAITING_AUTHORISATION,
                )
                val secondPendingConsent = setupSweepingConsent(
                    status = ConsentStatus.AWAITING_AUTHORISATION,
                )

                val result1 = consentService.updateConsentAndNotify(
                    consentId = firstPendingConsent.id,
                    status = ConsentStatus.REJECTED,
                    debtor = null,
                )
                result1.isRight() shouldBe true

                verify(exactly = 0) {
                    notificationService.sendNotification(any(), any(), any())
                }

                val result2 = consentService.updateConsentAndNotify(
                    consentId = secondPendingConsent.id,
                    status = ConsentStatus.AUTHORISED,
                    debtor = sweepingDebtor,
                )
                result2.isRight() shouldBe true

                val accountSlot = slot<Account>()
                val notificationSlot = slot<(account: Account) -> BillPaymentNotification?>()
                verify {
                    notificationService.notifyAccount(capture(accountSlot), any(), capture(notificationSlot))
                }
                accountSlot.captured.accountId shouldBe wallet.founder.accountId
                notificationSlot.captured.invoke(accountSlot.captured)?.template?.value shouldBe whatsappTemplateEdit
            }

            @Test
            fun `deve notificar erro de edição quando o último consentimento pendente falha`() {
                val firstPendingConsent = setupSweepingConsent(
                    status = ConsentStatus.AWAITING_AUTHORISATION,
                )
                val secondPendingConsent = setupSweepingConsent(
                    status = ConsentStatus.AWAITING_AUTHORISATION,
                )

                val result1 = consentService.updateConsentAndNotify(
                    consentId = firstPendingConsent.id,
                    status = ConsentStatus.REJECTED,
                    debtor = null,
                )
                result1.isRight() shouldBe true

                verify(exactly = 0) {
                    notificationService.sendNotification(any(), any(), any())
                }

                val result2 = consentService.updateConsentAndNotify(
                    consentId = secondPendingConsent.id,
                    status = ConsentStatus.REJECTED,
                    debtor = null,
                )
                result2.isRight() shouldBe true

                val accountSlot = slot<Account>()
                val notificationSlot = slot<(account: Account) -> BillPaymentNotification?>()
                verify {
                    notificationService.notifyAccount(capture(accountSlot), any(), capture(notificationSlot))
                }
                accountSlot.captured.accountId shouldBe wallet.founder.accountId
                notificationSlot.captured.invoke(accountSlot.captured)?.template?.value shouldBe whatsappTemplateEditFailed
            }
        }
    }

    @Nested
    @DisplayName("dados")
    inner class DataConsentTest {

        @Nested
        @DisplayName("ao solicitar a criação de um consentimento")
        inner class CreateConsentTest {

            @Test
            fun `e o id do participant nao existir deve retornar erro`() {
                val result = consentService.createDataConsent(wallet.founder.accountId, Document(wallet.founder.document), OpenFinanceParticipantId("invalid"))

                result.isLeft() shouldBe true
                result.onLeft {
                    it.shouldBeTypeOf<OpenFinanceError>()
                }
            }

            @Test
            fun `e a chamada ao open finance falhar deve retornar erro`() {
                every { openFinanceAdapter.createDataConsent(any(), any(), any()) } returns OpenFinanceAdapterException(NoStackTraceException("mocked error")).left()

                val result = consentService.createDataConsent(wallet.founder.accountId, Document(wallet.founder.document), OpenFinanceParticipantId("1"))

                result.isLeft() shouldBe true
                result.onLeft {
                    it.shouldBeTypeOf<OpenFinanceAdapterException>()
                }
            }
        }

        @Nested
        @DisplayName("ao solicitar o cancelamento de um consentimento")
        inner class RevokeConsentTest {

            @Test
            fun `should update consent status on revoke`() {
                val consent = setupDataConsent(status = ConsentStatus.AUTHORISED)

                every { openFinanceAdapter.revokeDataConsent(any()) } returns Unit.right()
                val result = consentService.revokeConsent(consent.dataConsentId, consent.accountId)
                result.isRight() shouldBe true

                dataConsentRepository.findOrNull(consent.dataConsentId, consent.accountId)?.let {
                    it.status shouldBe ConsentStatus.REVOKED
                }
            }
        }

        @Nested
        @DisplayName("ao atualizar o estado de um consentimento")
        inner class UpdateConsentTest {
            @Nested
            @DisplayName("para autorizado")
            inner class AuthorizeConsentTest {

                @Test
                fun `deve notificar o usuario`() {
                    val consent = setupDataConsent()

                    val status = ConsentStatus.AUTHORISED

                    val result = consentService.updateConsentAndNotify(
                        consentId = consent.dataConsentId,
                        accountId = consent.accountId,
                        status = status,
                    )
                    result.isRight() shouldBe true
                    result.map {
                        it.status shouldBe status
                    }

                    val accountSlot = slot<Account>()
                    val notificationSlot = slot<(account: Account) -> BillPaymentNotification?>()
                    verify {
                        notificationService.notifyAccount(capture(accountSlot), any(), capture(notificationSlot))
                    }
                    accountSlot.captured.accountId shouldBe wallet.founder.accountId
                    notificationSlot.captured.invoke(accountSlot.captured)?.template?.value shouldBe whatsappTemplateDataConsentConnected
                }

                @Test
                fun `deve notificar sucesso quando todos os consentimentos são aprovados`() {
                    val firstConsent = setupDataConsent(status = ConsentStatus.AWAITING_AUTHORISATION)
                    val secondConsent = setupDataConsent(status = ConsentStatus.AWAITING_AUTHORISATION)

                    val result1 = consentService.updateConsentAndNotify(
                        consentId = firstConsent.dataConsentId,
                        accountId = firstConsent.accountId,
                        status = ConsentStatus.AUTHORISED,
                    )
                    result1.isRight() shouldBe true

                    verify(exactly = 0) {
                        notificationService.sendNotification(any(), any(), any())
                    }

                    val result2 = consentService.updateConsentAndNotify(
                        consentId = secondConsent.dataConsentId,
                        accountId = secondConsent.accountId,
                        status = ConsentStatus.AUTHORISED,
                    )
                    result2.isRight() shouldBe true

                    val accountSlot = slot<Account>()
                    val notificationSlot = slot<(account: Account) -> BillPaymentNotification?>()
                    verify {
                        notificationService.notifyAccount(capture(accountSlot), any(), capture(notificationSlot))
                    }
                    accountSlot.captured.accountId shouldBe wallet.founder.accountId
                    notificationSlot.captured.invoke(accountSlot.captured)?.template?.value shouldBe whatsappTemplateDataConsentConnected
                }

                @Test
                fun `deve notificar sucesso mesmo quando um dos consentimentos falhou`() {
                    val firstConsent = setupDataConsent(status = ConsentStatus.AWAITING_AUTHORISATION)
                    val secondConsent = setupDataConsent(status = ConsentStatus.AWAITING_AUTHORISATION)

                    val result1 = consentService.updateConsentAndNotify(
                        consentId = firstConsent.dataConsentId,
                        accountId = firstConsent.accountId,
                        status = ConsentStatus.AUTHORISED,
                    )
                    result1.isRight() shouldBe true

                    verify(exactly = 0) {
                        notificationService.sendNotification(any(), any(), any())
                    }

                    val result2 = consentService.updateConsentAndNotify(
                        consentId = secondConsent.dataConsentId,
                        accountId = secondConsent.accountId,
                        status = ConsentStatus.REJECTED,
                    )
                    result2.isRight() shouldBe true

                    val accountSlot = slot<Account>()
                    val notificationSlot = slot<(account: Account) -> BillPaymentNotification?>()
                    verify {
                        notificationService.notifyAccount(capture(accountSlot), any(), capture(notificationSlot))
                    }
                    accountSlot.captured.accountId shouldBe wallet.founder.accountId
                    notificationSlot.captured.invoke(accountSlot.captured)?.template?.value shouldBe whatsappTemplateDataConsentConnected
                }

                @Test
                fun `deve revogar consentimentos antigos ativos`() {
                    val olderConsent = setupDataConsent(status = ConsentStatus.AUTHORISED)
                    val consentToBeAuthorized = setupDataConsent(createdAt = olderConsent.createdAt.plusSeconds(1))

                    val status = ConsentStatus.AUTHORISED

                    val result = consentService.updateConsentAndNotify(
                        consentId = consentToBeAuthorized.dataConsentId,
                        accountId = consentToBeAuthorized.accountId,
                        status = status,
                    )
                    result.isRight() shouldBe true

                    dataConsentRepository.findOrNull(olderConsent.dataConsentId, olderConsent.accountId)?.status shouldBe ConsentStatus.REVOKED
                }

                @ParameterizedTest
                @EnumSource(ConsentStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["AWAITING_AUTHORISATION", "PARTIALLY_ACCEPTED", "AUTHORISED"])
                fun `não deve revogar consentimentos antigos inativos`(olderStatus: ConsentStatus) {
                    val olderConsent = setupDataConsent(status = olderStatus)
                    val consentToBeAuthorized = setupDataConsent()

                    val status = ConsentStatus.AUTHORISED

                    val result = consentService.updateConsentAndNotify(
                        consentId = consentToBeAuthorized.dataConsentId,
                        accountId = consentToBeAuthorized.accountId,
                        status = status,
                    )
                    result.isRight() shouldBe true

                    with(dataConsentRepository.findOrNull(olderConsent.dataConsentId, olderConsent.accountId)) {
                        this?.status shouldBe olderConsent.status
                        this?.updatedAt shouldBe olderConsent.updatedAt
                    }
                }

                @Test
                fun `não deve revogar consentimentos mais recentes`() {
                    val consentToBeAuthorized = setupDataConsent()
                    val newerConsent = setupDataConsent()

                    val status = ConsentStatus.AUTHORISED

                    val result = consentService.updateConsentAndNotify(
                        consentId = consentToBeAuthorized.dataConsentId,
                        accountId = consentToBeAuthorized.accountId,
                        status = status,
                    )
                    result.isRight() shouldBe true

                    with(dataConsentRepository.findOrNull(newerConsent.dataConsentId, newerConsent.accountId)) {
                        this?.status shouldBe newerConsent.status
                        this?.updatedAt shouldBe newerConsent.updatedAt
                    }
                }
            }

            @Nested
            @DisplayName("para revogado")
            inner class RevokeConsentTest {
                @Test
                fun `não deve notificar o usuário`() {
                    val consent = setupDataConsent()

                    val status = ConsentStatus.REVOKED

                    val result = consentService.updateConsentAndNotify(consent.dataConsentId, consent.accountId, status)
                    result.isRight() shouldBe true
                    result.map {
                        it.status shouldBe status
                    }

                    verify(exactly = 0) {
                        notificationService.sendNotification(any(), any(), any())
                    }
                }
            }

            @Nested
            @DisplayName("para rejeitado")
            inner class RejectConsentTest {
                @Test
                fun `deve notificar o usuario se não existir consentimento pendente`() {
                    val consent = setupDataConsent()

                    val status = ConsentStatus.REJECTED

                    val result = consentService.updateConsentAndNotify(consent.dataConsentId, consent.accountId, status)
                    result.isRight() shouldBe true
                    result.map {
                        it.status shouldBe status
                    }

                    val accountSlot = slot<Account>()
                    val notificationSlot = slot<(account: Account) -> BillPaymentNotification?>()
                    verify {
                        notificationService.notifyAccount(capture(accountSlot), any(), capture(notificationSlot))
                    }
                    accountSlot.captured.accountId shouldBe wallet.founder.accountId
                    notificationSlot.captured.invoke(accountSlot.captured)?.template?.value shouldBe whatsappTemplateDataConsentConnectionFailed

                    verify {
                        crmRepository wasNot called
                    }
                }

                @Test
                fun `deve notificar erro quando todos os consentimentos falham`() {
                    val firstConsent = setupDataConsent(status = ConsentStatus.AWAITING_AUTHORISATION)
                    val secondConsent = setupDataConsent(status = ConsentStatus.AWAITING_AUTHORISATION)

                    val result1 = consentService.updateConsentAndNotify(
                        consentId = firstConsent.dataConsentId,
                        accountId = firstConsent.accountId,
                        status = ConsentStatus.REJECTED,
                    )
                    result1.isRight() shouldBe true

                    verify(exactly = 0) {
                        notificationService.sendNotification(any(), any(), any())
                    }

                    val result2 = consentService.updateConsentAndNotify(
                        consentId = secondConsent.dataConsentId,
                        accountId = secondConsent.accountId,
                        status = ConsentStatus.REJECTED,
                    )
                    result2.isRight() shouldBe true

                    val accountSlot = slot<Account>()
                    val notificationSlot = slot<(account: Account) -> BillPaymentNotification?>()
                    verify {
                        notificationService.notifyAccount(capture(accountSlot), any(), capture(notificationSlot))
                    }
                    accountSlot.captured.accountId shouldBe wallet.founder.accountId
                    notificationSlot.captured.invoke(accountSlot.captured)?.template?.value shouldBe whatsappTemplateDataConsentConnectionFailed
                }

                @Test
                fun `não deve notificar o usuário quando ainda existem consentimentos pendentes`() {
                    val firstConsent = setupDataConsent(status = ConsentStatus.AWAITING_AUTHORISATION)
                    setupDataConsent(status = ConsentStatus.AWAITING_AUTHORISATION)

                    val result1 = consentService.updateConsentAndNotify(
                        consentId = firstConsent.dataConsentId,
                        accountId = firstConsent.accountId,
                        status = ConsentStatus.REJECTED,
                    )
                    result1.isRight() shouldBe true

                    verify(exactly = 0) {
                        notificationService.sendNotification(any(), any(), any())
                    }
                }
            }
        }
    }

    private fun setupSweepingConsent(status: ConsentStatus = ConsentStatus.AWAITING_AUTHORISATION, participantId: OpenFinanceParticipantId = OpenFinanceParticipantId(sweepingParticipant.id), createdAt: ZonedDateTime = getZonedDateTime(), walletId: WalletId = wallet.id): SweepingConsent {
        val consentId = SweepingConsentId(UUID.randomUUID().toString())
        val consent = SweepingConsent(
            accountId = wallet.founder.accountId,
            id = consentId,
            creditorId = CreditorId("ID_2"),
            walletId = walletId,
            participantId = participantId,
            status = status,
            startDateTime = createdAt,
            expirationDateTime = createdAt.plusYears(2),
            createdAt = createdAt,
            updatedAt = createdAt,
            url = URI.create("http://example.com").toURL(),
            configuration = sweepingLimitsConfiguration,
            debtor = sweepingDebtor,
            lastSuccessfulCashIn = null,
        )

        return sweepingAccountRepository.save(consent)
    }

    private fun setupDataConsent(status: ConsentStatus = ConsentStatus.AWAITING_AUTHORISATION, participantId: OpenFinanceParticipantId = OpenFinanceParticipantId(sweepingParticipant.id), createdAt: ZonedDateTime = getZonedDateTime(), accountId: AccountId = wallet.founder.accountId): DataConsent {
        val consentId = DataConsentId(UUID.randomUUID().toString())
        val consent = DataConsent(
            accountId = accountId,
            dataConsentId = consentId,
            participantId = participantId,
            status = status,
            createdAt = createdAt,
            updatedAt = createdAt,
            url = URI.create("http://example.com").toURL(),
            document = Document(wallet.founder.document),
        )

        return dataConsentRepository.save(consent)
    }

    private fun setupBankAccount(dataConsent: DataConsent): OpenFinanceBankAccount {
        val bankAccount = OpenFinanceBankAccount(
            userAccountId = dataConsent.accountId,
            bankAccountResourceId = BankAccountResourceId(value = UUID.randomUUID().toString()),
            bankCode = "his",
            data = null,
            createdAt = getZonedDateTime(),
            updatedAt = getZonedDateTime(),
            dataConsentId = dataConsent.dataConsentId,
            brandName = "brandName",
            ispb = "********",
            routingNumber = "1234",
            accountNumber = "**********",
            accountDv = "0",
            accountType = "accountType",
            accountSubType = "accountSubType",
            currency = "currency",
            bankAccountNumberId = BankAccountNumberId(),
        )
        bankAccountRepository.save(bankAccount)

        return bankAccount
    }
}