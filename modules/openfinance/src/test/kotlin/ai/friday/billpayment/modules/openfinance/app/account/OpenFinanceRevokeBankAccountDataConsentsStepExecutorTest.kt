package ai.friday.billpayment.modules.openfinance.app.account

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.app.account.CloseAccountStepError
import ai.friday.billpayment.app.account.CloseAccountStepStatus
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.openfinance.app.BankAccountDataConsent
import ai.friday.billpayment.modules.openfinance.app.ConsentService
import ai.friday.billpayment.modules.openfinance.app.DataConsent
import ai.friday.billpayment.modules.openfinance.app.DataConsentId
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlin.test.junit.JUnitAsserter.fail
import org.junit.jupiter.api.Test

class OpenFinanceRevokeBankAccountDataConsentsStepExecutorTest {

    private val consentService = mockk<ConsentService>()

    private val openFinanceRevokeBankAccountDataConsentsStepExecutor = OpenFinanceRevokeBankAccountDataConsentsStepExecutor(
        consentService = consentService,
    )

    private val walletFixture = WalletFixture()

    private val bankAccountDataConsent = mockk<BankAccountDataConsent> {
        every { id } returns DataConsentId("bankAccountDataConsentId")
    }

    @Test
    fun `deve retornar sucesso se não houver nenhum consentimento`() {
        every { consentService.getActiveBankAccountDataConsents(any()) } returns emptyList()

        val result = openFinanceRevokeBankAccountDataConsentsStepExecutor.execute(walletFixture.founderAccount, mockk()).getOrElse {
            fail("Esperado uma lista vazia, mas retornou um erro: $it")
        }

        result shouldBe CloseAccountStepStatus.Success

        verify(exactly = 0) {
            consentService.revokeConsent(any<DataConsentId>(), any())
        }
    }

    @Test
    fun `deve retornar sucesso e remover os consentimentos`() {
        every { consentService.getActiveBankAccountDataConsents(any()) } returns listOf(bankAccountDataConsent, bankAccountDataConsent)
        every { consentService.revokeConsent(any<DataConsentId>(), any()) } returns mockk<DataConsent>().right()

        val result = openFinanceRevokeBankAccountDataConsentsStepExecutor.execute(walletFixture.founderAccount, mockk()).getOrElse {
            fail("Esperado uma lista vazia, mas retornou um erro: $it")
        }

        result shouldBe CloseAccountStepStatus.Success

        verify(exactly = 2) {
            consentService.revokeConsent(any<DataConsentId>(), any())
        }
    }

    @Test
    fun `deve retornar falha se não conseguir remover algum consentimento`() {
        every { consentService.getActiveBankAccountDataConsents(any()) } returns listOf(bankAccountDataConsent, bankAccountDataConsent)
        every { consentService.revokeConsent(any<DataConsentId>(), any()) } returns NoStackTraceException().left()

        openFinanceRevokeBankAccountDataConsentsStepExecutor.execute(walletFixture.founderAccount, mockk()).map {
            fail("Era esperado um erro, mas retornou: $it")
        }.getOrElse {
            it.shouldBeTypeOf<CloseAccountStepError.GenericException>()
        }

        verify(exactly = 1) {
            consentService.revokeConsent(any<DataConsentId>(), any())
        }
    }
}