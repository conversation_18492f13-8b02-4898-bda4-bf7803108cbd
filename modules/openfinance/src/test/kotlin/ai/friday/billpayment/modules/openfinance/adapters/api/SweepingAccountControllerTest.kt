package ai.friday.billpayment.modules.openfinance.adapters.api

import ai.friday.billpayment.adapters.api.AUTHENTICATION_ATTRIBUTE_WALLET
import ai.friday.billpayment.app.integrations.EndToEnd
import ai.friday.billpayment.app.integrations.RequestSweepingCashInCommand
import ai.friday.billpayment.app.integrations.SweepingCashInSource
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.openfinance.app.SweepingAccountService
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpStatus
import io.micronaut.security.authentication.Authentication
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class SweepingAccountControllerTest {

    private val sweepingAccountService: SweepingAccountService = mockk()

    private val controller = SweepingAccountController(
        sweepingAccountService = sweepingAccountService,
    )

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    private val authentication: Authentication = mockk {
        every {
            name
        } returns wallet.founder.accountId.value
        every {
            attributes
        } returns mapOf(AUTHENTICATION_ATTRIBUTE_WALLET to wallet)
    }

    @DisplayName("ao solicitar uma transferência inteligente")
    @Nested
    inner class RequestSweepingCashInTest {
        @Test
        fun `deve retornar NO_CONTENT em caso de sucesso`() {
            val commandSlot = slot<RequestSweepingCashInCommand>()
            every {
                sweepingAccountService.requestSweepingCashIn(capture(commandSlot))
            } returns EndToEnd().right()

            val request = SweepingCashInRequestTO(
                id = "requestId",
                amount = 8903,
                description = "description",
                consentId = null,
            )

            val response = controller.sweepingCashIn(authentication, request)

            response.status shouldBe HttpStatus.NO_CONTENT

            with(commandSlot.captured) {
                requestSource shouldBe SweepingCashInSource.WEB_APP
                approvalSource shouldBe SweepingCashInSource.WEB_APP
                externalTransactionId shouldBe null
                sweepingCashInId.value shouldBe request.id
                walletId shouldBe wallet.id
                amount shouldBe request.amount
                description shouldBe request.description
            }
        }
    }
}