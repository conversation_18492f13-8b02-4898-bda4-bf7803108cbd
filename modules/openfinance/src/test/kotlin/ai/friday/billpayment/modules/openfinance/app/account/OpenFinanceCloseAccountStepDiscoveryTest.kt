package ai.friday.billpayment.modules.openfinance.app.account

import ai.friday.billpayment.app.account.AccountClosureDetails
import ai.friday.billpayment.app.account.AccountClosureReason
import ai.friday.billpayment.app.account.CloseAccountStepStatus
import ai.friday.billpayment.app.account.CloseWalletStep
import ai.friday.billpayment.app.account.CloseWalletStepStatus
import ai.friday.billpayment.app.account.SimpleCloseAccountStep
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.openfinance.app.BankAccountDataConsent
import ai.friday.billpayment.modules.openfinance.app.ConsentService
import ai.friday.billpayment.modules.openfinance.app.DataConsentId
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceSweepingAccountError
import ai.friday.billpayment.modules.openfinance.app.SweepingConsent
import ai.friday.billpayment.modules.openfinance.app.SweepingConsentId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.mockk.every
import io.mockk.mockk
import kotlin.test.junit.JUnitAsserter.fail
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class OpenFinanceCloseAccountStepDiscoveryTest {

    private val consentService = mockk<ConsentService>()

    private val openFinanceCloseAccountStepDiscovery = OpenFinanceCloseAccountStepDiscovery(
        consentService = consentService,
    )

    private val wallet = WalletFixture().buildWallet()

    private val closureDetails = AccountClosureDetails.WithReason(reason = AccountClosureReason.USER_REQUEST, at = getZonedDateTime(), description = null)

    private val bankAccountDataConsent = mockk<BankAccountDataConsent> {
        every { id } returns DataConsentId("bankAccountDataConsentId")
    }

    private val sweepingConsent = mockk<SweepingConsent> {
        every { id } returns SweepingConsentId("sweepingConsentId")
    }

    @DisplayName("prepareClose")
    @Nested
    inner class PrepareClose {
        @Test
        fun `deve retornar uma lista vazia quando não houver consentimentos de dados bancários ativos`() {
            every { consentService.getActiveBankAccountDataConsents(any()) } returns emptyList()

            val result = openFinanceCloseAccountStepDiscovery.prepareClose(wallet.founder.accountId, closureDetails).getOrElse {
                fail("Esperado uma lista vazia, mas retornou um erro: $it")
            }

            result.isEmpty().shouldBeTrue()
        }

        @Test
        fun `deve retornar uma lista com um step se houver um consentimento`() {
            every { consentService.getActiveBankAccountDataConsents(any()) } returns listOf(bankAccountDataConsent)

            val result = openFinanceCloseAccountStepDiscovery.prepareClose(wallet.founder.accountId, closureDetails).getOrElse {
                fail("Esperado uma lista com um consentimento, mas retornou um erro: $it")
            }

            result.size shouldBe 1
            with(result.single()) {
                this.shouldBeInstanceOf<SimpleCloseAccountStep>()
                this.status shouldBe CloseAccountStepStatus.Pending
                this.type shouldBe CloseAccountStepTypeOpenFinanceRevokeBankAccountDataConsents
            }
        }
    }

    @DisplayName("prepareCloseWallet")
    @Nested
    inner class PrepareCloseWallet {
        @Test
        fun `deve retornar uma lista vazia quando não houver consentimentos de sweeping`() {
            every { consentService.getSweepingConsent(any<WalletId>()) } returns OpenFinanceSweepingAccountError.ConsentNotFound.left()

            val result = openFinanceCloseAccountStepDiscovery.prepareCloseWallet(wallet, closureDetails).getOrElse {
                fail("Esperado uma lista vazia, mas retornou um erro: $it")
            }

            result.isEmpty().shouldBeTrue()
        }

        @Test
        fun `deve retornar uma lista com um step se houver um consentimento de sweeping`() {
            every { consentService.getSweepingConsent(any<WalletId>()) } returns sweepingConsent.right()

            val result = openFinanceCloseAccountStepDiscovery.prepareCloseWallet(wallet, closureDetails).getOrElse {
                fail("Esperado uma lista com um consentimento, mas retornou um erro: $it")
            }

            result.size shouldBe 1
            with(result.single()) {
                this.shouldBeInstanceOf<CloseWalletStep>()
                this.status shouldBe CloseWalletStepStatus.Pending
                this.type shouldBe CloseWalletStepTypeOpenFinanceRevokeSweepingConsents
            }
        }
    }
}