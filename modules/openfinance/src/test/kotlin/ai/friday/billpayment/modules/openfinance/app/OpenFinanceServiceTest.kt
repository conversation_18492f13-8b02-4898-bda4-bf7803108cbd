package ai.friday.billpayment.modules.openfinance.app

import DynamoDBUtils
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.bill.DefaultBillEventPublisher
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BigDataService
import ai.friday.billpayment.app.integrations.KmsService
import ai.friday.billpayment.app.integrations.OpenFinanceParticipantId
import ai.friday.billpayment.app.manualentry.ManualEntry
import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.app.manualentry.ManualEntryStatus
import ai.friday.billpayment.app.manualentry.ManualEntryType
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createOpenFinanceDataTable
import ai.friday.billpayment.modules.openfinance.adapters.OpenFinanceBankAccountDBRepository
import ai.friday.billpayment.modules.openfinance.adapters.OpenFinanceBankAccountDynamoDAO
import ai.friday.billpayment.modules.openfinance.adapters.OpenFinanceBankTransactionDBRepository
import ai.friday.billpayment.modules.openfinance.adapters.OpenFinanceTransactionDynamoDAO
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.equality.shouldBeEqualToIgnoringFields
import io.kotest.matchers.equality.shouldBeEqualUsingFields
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import java.net.URI
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient

class OpenFinanceServiceTest {
    private val dynamoDB: AmazonDynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val client: DynamoDbEnhancedClient =
        DynamoDbEnhancedClient
            .builder()
            .dynamoDbClient(DynamoDBUtils.getDynamoDbClient())
            .build()
    private val asyncClient: DynamoDbEnhancedAsyncClient =
        DynamoDbEnhancedAsyncClient
            .builder()
            .dynamoDbClient(DynamoDBUtils.getDynamoDbClientAsync())
            .build()
    private val transactionRepository: OpenFinanceBankTransactionRepository =
        spyk(OpenFinanceBankTransactionDBRepository(OpenFinanceTransactionDynamoDAO(asyncClient)))

    private val bankRepository: OpenFinanceBankAccountRepository =
        spyk(OpenFinanceBankAccountDBRepository(OpenFinanceBankAccountDynamoDAO(client)))

    private val kmsService = mockk<KmsService>(relaxed = true)

    private val billEventPublisher = mockk<DefaultBillEventPublisher>(relaxed = true)
    private val bigDataService = mockk<BigDataService>(relaxed = true) {
        every { getPersonName(any()) } returns "Regina Zimmerman".right()
    }

    private val document = DocumentOpenFinance(value = DOCUMENT, type = "CPF", issuerRegion = "", issuer = "")
    private val manualEntryService = mockk<ManualEntryService>(relaxed = true)
    private val accountRepository = mockk<AccountRepository>(relaxed = true)

    private val openFinanceService =
        OpenFinanceService(
            kmsService,
            transactionRepository,
            bankRepository,
            billEventPublisher,
            bigDataService,
            manualEntryService,
            accountRepository,
            "key-id",
        )

    private val creditTransaction =
        BankTransaction(
            transactionStatus = CompletedAuthorisedPaymentType.TRANSACAO_EFETIVADA,
            bankAccountId = BankAccountId(value = "tellus"),
            agencyNumber = null,
            transactionName = "Regina Zimmerman",
            transactionId = TransactionId(value = "prompta"),
            document = document,
            type = TransactionType.PIX,
            transactionDate = getZonedDateTime(),
            bankCode = null,
            amount = BankAccountAmount("174.27", BankAccountCurrency.BRL),
            personType = TransactionPersonType.PESSOA_NATURAL,
            creditDebitType = CreditDebitType.CREDITO,
            accountNumber = null,
            accountDv = null,
        )

    private val transaction = OpenFinanceBankTransaction(
        userAccountId = "**********",
        bankAccountId = "ridiculus",
        transactionId = "ea",
        transactionDate = "2024-09-11 20:18:18",
        createdAt = "2024-07-10",
        updatedAt = "2024-07-10",
        data = "lorem ipsum",
    )

    private val bankCode = 123L
    private val bankAccount = OpenFinanceBankAccount(
        userAccountId = AccountId("**********"),
        bankAccountResourceId = BankAccountResourceId("ridiculus"),
        bankCode = bankCode.toString(),
        createdAt = getZonedDateTime(),
        updatedAt = getZonedDateTime(),
        data = null,
        dataConsentId = null,
        brandName = null,
        ispb = null,
        routingNumber = null,
        accountNumber = null,
        accountDv = null,
        accountType = null,
        accountSubType = null,
        currency = null,
    )

    private val dataConsent = DataConsent(
        dataConsentId = DataConsentId("test-consent-id"),
        participantId = OpenFinanceParticipantId("test-participant"),
        status = ConsentStatus.AWAITING_AUTHORISATION,
        createdAt = getZonedDateTime(),
        accountId = AccountId("**********"),
        document = Document("**********1"),
        url = URI.create("http://example.com").toURL(),
        updatedAt = getZonedDateTime(),
    )

    @BeforeEach
    fun setUp() {
        createOpenFinanceDataTable(dynamoDB)
    }

    @Test
    fun `should process a bank account transaction`() {
        coEvery { kmsService.decryptData(any(), any()) } returns getObjectMapper().writeValueAsString(creditTransaction)

        val result = openFinanceService.process(transaction)

        result.isRight() shouldBe true
        verify {
            transactionRepository.save(any())
        }
        val persistedTransaction = transactionRepository.find(transaction.transactionId).block()
        persistedTransaction shouldBeEqualUsingFields transaction
    }

    @Test
    fun `should not create a pix on a pix credit transaction`() {
        coEvery { kmsService.decryptData(any(), any()) } returns getObjectMapper().writeValueAsString(creditTransaction)

        val result = openFinanceService.process(transaction)

        result.isRight() shouldBe true
        verify {
            transactionRepository.save(any())
        }
        verify(exactly = 0) {
            billEventPublisher.storePastBill(any<BillAdded>(), any<BillPaid>())
        }
        coVerify {
            kmsService.decryptData(any(), any())
        }
        val persistedTransaction = transactionRepository.find(transaction.transactionId).block()
        persistedTransaction shouldBeEqualUsingFields transaction
    }

    @Test
    fun `should create a manual entry on a bank account debit convenio transaction`() {
        bankRepository.save(bankAccount)

        val debitTransaction =
            BankTransaction(
                transactionStatus = CompletedAuthorisedPaymentType.TRANSACAO_EFETIVADA,
                bankAccountId = BankAccountId(value = "tellus"),
                agencyNumber = null,
                transactionName = "Regina Zimmerman",
                transactionId = TransactionId(value = "prompta"),
                document = document,
                type = TransactionType.CONVENIO_ARRECADACAO,
                transactionDate = getZonedDateTime(),
                bankCode = null,
                amount = BankAccountAmount("100", BankAccountCurrency.BRL),
                personType = TransactionPersonType.PESSOA_NATURAL,
                creditDebitType = CreditDebitType.DEBITO,
                accountNumber = null,
                accountDv = null,
            )
        coEvery { kmsService.decryptData(any(), any()) } returns getObjectMapper().writeValueAsString(debitTransaction)
        every { manualEntryService.create(any(), any(), any(), any(), any(), any(), any(), any(), any()) } returns ManualEntry(
            title = "Regina Zimmerman",
            amount = 100,
            dueDate = getZonedDateTime().toLocalDate(),
            walletId = WalletId("**********"),
            type = ManualEntryType.EXTERNAL_PAYMENT,
            status = ManualEntryStatus.ACTIVE,
            source = ActionSource.OpenFinance(AccountId("**********"), 120),
            createdAt = getZonedDateTime(),
            updatedAt = getZonedDateTime(),
        ).right()

        val result = openFinanceService.process(transaction)

        result.isRight() shouldBe true
        verify {
            transactionRepository.save(any())
            manualEntryService.create(any(), any(), any(), any(), any(), any(), any(), any(), any())
        }
        coVerify {
            kmsService.decryptData(any(), any())
        }
        val persistedTransaction = transactionRepository.find(transaction.transactionId).block()
        persistedTransaction shouldBeEqualUsingFields transaction
    }

    @Test
    fun `should create a bill on a bank account debit pix transaction`() {
        bankRepository.save(bankAccount.copy(bankCode = bankCode.toString()))

        val debitTransaction =
            BankTransaction(
                transactionStatus = CompletedAuthorisedPaymentType.TRANSACAO_EFETIVADA,
                bankAccountId = BankAccountId(value = "tellus"),
                agencyNumber = "123",
                transactionName = "Regina Zimmerman",
                transactionId = TransactionId(value = "prompta"),
                document = document,
                type = TransactionType.PIX,
                transactionDate = getZonedDateTime(),
                bankCode = "123",
                amount = BankAccountAmount("100", BankAccountCurrency.BRL),
                personType = TransactionPersonType.PESSOA_NATURAL,
                creditDebitType = CreditDebitType.DEBITO,
                accountNumber = "123",
                accountDv = "3",
            )
        coEvery { kmsService.decryptData(any(), any()) } returns getObjectMapper().writeValueAsString(debitTransaction)
        every { accountRepository.findAccountPaymentMethodsByAccountId(any()) } returns listOf(
            AccountPaymentMethod(
                id = AccountPaymentMethodId(value = "test"),
                accountId = AccountId(value = "**********"),
                method = InternalBankAccount(
                    accountType = AccountType.CHECKING,
                    bankNo = 123L,
                    routingNo = 123L,
                    accountNo = 123L,
                    accountDv = "3",
                    bankAccountMode = BankAccountMode.PHYSICAL,
                ),
                status = AccountPaymentMethodStatus.ACTIVE,
                created = null,
            ),
        )

        val transaction =
            OpenFinanceBankTransaction(
                userAccountId = "**********",
                bankAccountId = "ridiculus",
                transactionId = "ea",
                transactionDate = "consectetuer",
                createdAt = "adipiscing",
                updatedAt = "elit",
                data = "lorem ipsum",
            )
        val result = openFinanceService.process(transaction)

        result.isRight() shouldBe true
        verify {
            transactionRepository.save(any())
            billEventPublisher.storePastBill(any<BillAdded>(), any<BillPaid>())
        }
        coVerify {
            kmsService.decryptData(any(), any())
        }
        val persistedTransaction = transactionRepository.find(transaction.transactionId).block()
        persistedTransaction shouldBeEqualUsingFields transaction
    }

    @Test
    fun `should throw error if document is not present`() {
        val debitTransaction =
            BankTransaction(
                transactionStatus = CompletedAuthorisedPaymentType.TRANSACAO_EFETIVADA,
                bankAccountId = BankAccountId(value = "tellus"),
                agencyNumber = null,
                transactionName = "Regina Zimmerman",
                transactionId = TransactionId(value = "prompta"),
                document = null,
                type = TransactionType.PIX,
                transactionDate = getZonedDateTime(),
                bankCode = null,
                amount = BankAccountAmount("100", BankAccountCurrency.BRL),
                personType = TransactionPersonType.PESSOA_NATURAL,
                creditDebitType = CreditDebitType.DEBITO,
                accountNumber = null,
                accountDv = null,
            )
        coEvery { kmsService.decryptData(any(), any()) } returns getObjectMapper().writeValueAsString(debitTransaction)

        val result = openFinanceService.process(transaction)

        result.isRight() shouldBe false
    }

    @Test
    fun `should process a bank account`() {
        val result = openFinanceService.process(bankAccount)

        result.isRight() shouldBe true
        verify {
            bankRepository.save(any())
        }
        val persistedAccount = bankRepository.findOrNull(bankAccount.bankAccountResourceId, AccountId("**********"))
        persistedAccount.shouldNotBeNull()
        persistedAccount.shouldBeEqualToIgnoringFields(bankAccount, bankAccount::createdAt, bankAccount::updatedAt, bankAccount::data)
    }

    @Test
    fun `should not create pix bill if bank account is the same of friday user`() {
        val bankCode = 123L
        bankRepository.save(bankAccount.copy(bankCode = bankCode.toString()))

        val debitTransaction =
            BankTransaction(
                transactionStatus = CompletedAuthorisedPaymentType.TRANSACAO_EFETIVADA,
                bankAccountId = BankAccountId(value = "tellus"),
                agencyNumber = "123",
                transactionName = "Regina Zimmerman",
                transactionId = TransactionId(value = "prompta"),
                document = document,
                type = TransactionType.PIX,
                transactionDate = getZonedDateTime(),
                bankCode = "123",
                amount = BankAccountAmount("100", BankAccountCurrency.BRL),
                personType = TransactionPersonType.PESSOA_NATURAL,
                creditDebitType = CreditDebitType.DEBITO,
                accountNumber = "123",
                accountDv = "3",
            )
        coEvery { kmsService.decryptData(any(), any()) } returns getObjectMapper().writeValueAsString(debitTransaction)
        every { accountRepository.findAccountPaymentMethodsByAccountId(any()) } returns listOf(
            AccountPaymentMethod(
                id = AccountPaymentMethodId(value = "test"),
                accountId = AccountId(value = "**********"),
                method = InternalBankAccount(
                    accountType = AccountType.CHECKING,
                    bankNo = 123L,
                    routingNo = 123L,
                    accountNo = 123L,
                    accountDv = "3",
                    bankAccountMode = BankAccountMode.PHYSICAL,
                ),
                status = AccountPaymentMethodStatus.ACTIVE,
                created = null,
            ),
        )
        every { accountRepository.findById(any()) } returns ACCOUNT

        val transaction =
            OpenFinanceBankTransaction(
                userAccountId = "**********",
                bankAccountId = "ridiculus",
                transactionId = "ea",
                transactionDate = "consectetuer",
                createdAt = "adipiscing",
                updatedAt = "elit",
                data = "lorem ipsum",
            )
        val result = openFinanceService.process(transaction)

        result.isLeft() shouldBe true
        result.mapLeft { it shouldBe OpenFinanceProcessTransactionError.OwnAccountTransferError }
    }

    @Test
    fun `should find all transactions in a period of time`() {
        val now = getZonedDateTime()
        for (i in 1..10) {
            transactionRepository.save(
                OpenFinanceBankTransaction(
                    userAccountId = "**********",
                    bankAccountId = "ridiculus",
                    transactionId = "ea$i",
                    transactionDate = now.minusDays(i.toLong()).format(dateTimeFormat),
                    createdAt = "adipiscing",
                    updatedAt = "elit",
                    data = "lorem ipsum",
                ),
            )
        }
        val results =
            openFinanceService
                .findByResourceTypeAndDate(
                    type = OpenFinanceResourceType.BANK_TRANSACTION,
                    accountId = AccountId("**********"),
                    from = now.minusDays(10).toLocalDate(),
                    to = now.minusDays(2).toLocalDate(),
                ).collectList()
                .block()
        results?.size shouldBe 8
    }
}