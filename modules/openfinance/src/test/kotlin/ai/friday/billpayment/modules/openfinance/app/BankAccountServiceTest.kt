package ai.friday.billpayment.modules.openfinance.app

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createOpenFinanceDataTable
import ai.friday.billpayment.modules.openfinance.adapters.OpenFinanceBankAccountNumberDBRepository
import ai.friday.billpayment.modules.openfinance.adapters.OpenFinanceBankAccountNumberDynamoDAO
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import net.javacrumbs.shedlock.core.SimpleLock
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class BankAccountServiceTest {

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    val enhancedClient = DynamoDBUtils.setupDynamoDB()

    private val consentService: ConsentService = mockk()
    private val openFinanceAdapter: OpenFinanceAdapter = mockk()
    private val bankAccountNumberRepository: OpenFinanceBankAccountNumberRepository = OpenFinanceBankAccountNumberDBRepository(
        OpenFinanceBankAccountNumberDynamoDAO(enhancedClient),
    )

    private val lock: SimpleLock = mockk {
        every {
            unlock()
        } just Runs
    }

    private val lockProvider: InternalLock = mockk {
        every {
            acquireLock(any())
        } returns lock
    }

    val service = BankAccountService(
        consentService = consentService,
        openFinanceAdapter = openFinanceAdapter,
        bankAccountNumberRepository = bankAccountNumberRepository,
        lockProvider = lockProvider,
    )

    @Nested
    @DisplayName("ao verificar se o número de uma conta corresponde a outro número de conta")
    inner class BankAccountNumberDataMatchesTest {
        @ParameterizedTest
        @CsvSource(
            "********#3071#21508#2       , ********#3071#21508#2",
            "********#03071#**********#2 , ********#03071#**********#2",
        )
        fun `deve retornar false se o ispb for diferente`(number: String, anotherNumber: String) {
            val one = parseBankAccountRoutingNumber(number)
            val another = parseBankAccountRoutingNumber(anotherNumber)

            one.matches(another) shouldBe false
        }

        @ParameterizedTest
        @CsvSource(
            "********#3071#21508#2       , ********#3072#21508#2",
            "********#03071#**********#2 , ********#03072#**********#2",
        )
        fun `deve retornar false se o número da agencia for diferente`(number: String, anotherNumber: String) {
            val one = parseBankAccountRoutingNumber(number)
            val another = parseBankAccountRoutingNumber(anotherNumber)

            one.matches(another) shouldBe false
        }

        @ParameterizedTest
        @CsvSource(
            "********#3071#21508#2       , ********#3071#21508#2",
            "********#03071#**********#2 , ********#03071#**********#2",
        )
        fun `deve retornar true se os dois números forem exatamente iguais`(number: String, anotherNumber: String) {
            val one = parseBankAccountRoutingNumber(number)
            val another = parseBankAccountRoutingNumber(anotherNumber)

            one.matches(another) shouldBe true
        }

        @ParameterizedTest
        @CsvSource(
            "********#3071#21508#2      , ********#03071#21508#2",
            "********#3071#21508#2      , ********#3071#**********#2",
            "********#3071#21508#2      , ********#03071#**********#2",
            "********#03071#21508#2     , ********#3071#21508#2",
            "********#3071#**********#2 , ********#3071#21508#2",
            "********#03071#**********#2, ********#3071#21508#2",
        )
        fun `deve retornar true se os dois números forem iguais mas com zeros à esquerda`(number: String, anotherNumber: String) {
            val one = parseBankAccountRoutingNumber(number)
            val another = parseBankAccountRoutingNumber(anotherNumber)

            one.matches(another) shouldBe true
        }

        @ParameterizedTest
        @CsvSource(
            "********#3071#21508#2       , ********#3071#215082",
            "********#3071#215082        , ********#3071#21508#2",
        )
        fun `deve retornar true se os dois números forem iguais mas com digito verificador no lugar errado`(number: String, anotherNumber: String) {
            val one = parseBankAccountRoutingNumber(number)
            val another = parseBankAccountRoutingNumber(anotherNumber)

            one.matches(another) shouldBe true
        }

        @ParameterizedTest
        @CsvSource(
            "********#3071#21508#2       , ********#3071#21508",
            "********#3071#21508         , ********#3071#21508#2",
        )
        fun `deve retornar true se os dois números forem iguais mas faltando digito verificador`(number: String, anotherNumber: String) {
            val one = parseBankAccountRoutingNumber(number)
            val another = parseBankAccountRoutingNumber(anotherNumber)

            one.matches(another) shouldBe true
        }

        @ParameterizedTest
        @CsvSource(
            "********#3071#215082        , ********#3071#21508",
            "********#3071#21508         , ********#3071#215082",
        )
        fun `deve retornar true se os dois números forem iguais mas faltando digito verificador em um e no lugar errado em outro`(number: String, anotherNumber: String) {
            val one = parseBankAccountRoutingNumber(number)
            val another = parseBankAccountRoutingNumber(anotherNumber)

            one.matches(another) shouldBe true
        }

        private fun parseBankAccountRoutingNumber(number: String): BankAccountNumberData {
            val tokens = number.split("#")

            return BankAccountNumberData(
                ispb = tokens[0],
                bankName = "",
                routingNumber = tokens[1],
                accountNumber = tokens[2],
                accountDv = if (tokens.size == 4) tokens[3] else null,
            )
        }
    }

    @Nested
    @DisplayName("ao salvar o número de uma conta")
    inner class SaveBankAccountNumberTest {

        val accountId = AccountId()

        val existingNumber = OpenFinanceBankAccountNumber(
            id = BankAccountNumberId(),
            accountId = accountId,
            data = BankAccountNumberData(
                ispb = "********",
                bankName = "",
                routingNumber = "03071",
                accountNumber = "**********",
                accountDv = "2",
            ),
            createdAt = getZonedDateTime(),
            updatedAt = getZonedDateTime(),
        )

        @BeforeEach
        fun setup() {
            createOpenFinanceDataTable(dynamoDB)
            bankAccountNumberRepository.save(existingNumber)
        }

        @Test
        fun `deve encontrar pelo ispb e agência, mesmo com zeros à esquerda`() {
            val numbers = bankAccountNumberRepository.findByAccountIdAndIspbAndRoutingNumber(
                accountId = accountId,
                ispb = existingNumber.data.ispb,
                routingNumber = "0" + existingNumber.data.routingNumber,
            )

            numbers.size shouldBe 1
            numbers.single().id shouldBe existingNumber.id
        }
    }

    @Nested
    @DisplayName("ao registrar o número de uma conta")
    inner class RegisterBankAccountNumberTest {

        val accountId = AccountId()

        val existingNumber = OpenFinanceBankAccountNumber(
            id = BankAccountNumberId(),
            accountId = accountId,
            data = BankAccountNumberData(
                ispb = "********",
                bankName = "",
                routingNumber = "03071",
                accountNumber = "**********2",
                accountDv = null, // necessário para teste de DV
            ),
            createdAt = getZonedDateTime(),
            updatedAt = getZonedDateTime(),
        )

        @BeforeEach
        fun setup() {
            createOpenFinanceDataTable(dynamoDB)
            bankAccountNumberRepository.save(existingNumber)
        }

        @Test
        fun `deve retornar AlreadyLocked se não conseguir pegar o lock`() {
            val slot = slot<String>()
            every {
                lockProvider.acquireLock(capture(slot))
            } returns null

            val result = service.registerBankAccountNumber(
                accountId = accountId,
                data = existingNumber.data,
            )

            result.isLeft() shouldBe true
            result.mapLeft {
                it.accountId shouldBe accountId
                it.ispb shouldBe existingNumber.data.ispb
            }

            slot.captured shouldBe "${accountId.value}#${existingNumber.data.ispb}"
        }

        @Test
        fun `deve retornar um id novo quando não encontrar uma conta igual para o mesmo usuário`() {
            val result = service.registerBankAccountNumber(
                accountId = accountId,
                data = existingNumber.data.copy(ispb = "********"),
            )

            result.isRight() shouldBe true

            result.map { accountNumber ->
                accountNumber.id shouldNotBe existingNumber.id
                accountNumber.accountId shouldBe existingNumber.accountId
                accountNumber.data.ispb shouldBe "********"
                accountNumber.data.bankName shouldBe existingNumber.data.bankName
                accountNumber.data.routingNumber shouldBe existingNumber.data.routingNumber.sanitized()
                accountNumber.data.accountNumber shouldBe existingNumber.data.accountNumber.sanitized()
                accountNumber.data.accountDv shouldBe existingNumber.data.accountDv

                bankAccountNumberRepository.findOrNull(accountNumber.id) shouldBe accountNumber
            }
        }

        @Test
        fun `deve retornar um id existente quando encontrar uma conta igual para o mesmo usuário`() {
            val result = service.registerBankAccountNumber(
                accountId = existingNumber.accountId,
                data = existingNumber.data,
            )

            result.isRight() shouldBe true
            result.map { accountNumber ->
                accountNumber.id shouldBe existingNumber.id

                bankAccountNumberRepository.findOrNull(accountNumber.id) shouldBe accountNumber
            }
        }

        @Test
        fun `deve atualizar o digito verificador quando encontrar uma conta igual sem digito verificador para o mesmo usuário`() {
            existingNumber.data.accountDv shouldBe null

            val dataWithDV = existingNumber.data.copy(
                accountNumber = "**********",
                accountDv = "2",
            )

            val result = service.registerBankAccountNumber(
                accountId = existingNumber.accountId,
                data = dataWithDV,
            )

            result.isRight() shouldBe true
            result.map { accountNumber ->
                accountNumber.id shouldBe existingNumber.id
                accountNumber.data.accountNumber shouldBe dataWithDV.accountNumber.sanitized()
                accountNumber.data.accountDv shouldBe dataWithDV.accountDv

                bankAccountNumberRepository.findOrNull(accountNumber.id) shouldBe accountNumber
            }
        }
    }

    private fun String.sanitized() = dropWhile { it == '0' }
}