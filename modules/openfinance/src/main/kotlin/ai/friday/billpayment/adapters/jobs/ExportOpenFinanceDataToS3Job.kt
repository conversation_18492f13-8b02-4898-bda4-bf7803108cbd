package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.feature.RequiresExportS3
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.modules.openfinance.OpenFinance
import ai.friday.morning.date.BrazilZonedDateTimeSupplier
import ai.friday.morning.date.dateFormat
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import java.time.Instant
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.dynamodb.model.ExportFormat
import software.amazon.awssdk.services.dynamodb.model.ExportTableToPointInTimeRequest

@OpenFinance
@RequiresExportS3
open class ExportOpenFinanceDataToS3Job(private val amazonDynamoDB: DynamoDbAsyncClient, private val configuration: ExportOpenFinanceDataToS3Configuration) :
    AbstractJob(
        cron = "15 5 * * *",
    ) {
    override fun execute() {
        val builder = ExportTableToPointInTimeRequest.builder()
        val date = BrazilZonedDateTimeSupplier.getZonedDateTime().format(dateFormat)
        val request =
            builder.s3Bucket(configuration.bucket)
                .s3Prefix("${configuration.prefix}/$date")
                .exportFormat(ExportFormat.DYNAMODB_JSON)
                .tableArn(configuration.tableArn)
                .exportTime(Instant.now()).build()
        val response = amazonDynamoDB.exportTableToPointInTime(request)
        LOG.info(Markers.append("response", response), "ExportOpenFinanceDataToS3Job")
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ExportToS3Job::class.java)
    }
}

@OpenFinance
@RequiresExportS3
@ConfigurationProperties("export-openfinancedata-s3")
data class ExportOpenFinanceDataToS3Configuration
@ConfigurationInject constructor(val bucket: String, val prefix: String, val tableArn: String)