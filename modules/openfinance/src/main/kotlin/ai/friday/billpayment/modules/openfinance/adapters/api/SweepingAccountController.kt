package ai.friday.billpayment.modules.openfinance.adapters.api

import ai.friday.billpayment.adapters.api.toWalletId
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.integrations.RequestSweepingCashInCommand
import ai.friday.billpayment.app.integrations.SweepingCashInId
import ai.friday.billpayment.app.integrations.SweepingCashInSource
import ai.friday.billpayment.app.integrations.SweepingCashInStatus
import ai.friday.billpayment.modules.openfinance.OpenFinance
import ai.friday.billpayment.modules.openfinance.adapters.OpenFinanceSweepingCashIn
import ai.friday.billpayment.modules.openfinance.app.GetSweepingCashInError
import ai.friday.billpayment.modules.openfinance.app.SweepingAccountService
import ai.friday.billpayment.modules.openfinance.app.isWarning
import ai.friday.billpayment.modules.openfinance.app.toErrorCode
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.validation.Validated
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Validated
@Secured(Role.Code.OWNER)
@Controller("/openfinance")
@OpenFinance
@Version("2")
class SweepingAccountController(
    private val sweepingAccountService: SweepingAccountService,
) {
    private val logger = LoggerFactory.getLogger(SweepingAccountController::class.java)

    @Post("/sweepingTransfer")
    fun sweepingCashIn(
        authentication: Authentication,
        @Body request: SweepingCashInRequestTO,
    ): HttpResponse<*> {
        val logName = "SweepingAccountController#sweepingCashIn"
        val accountId = authentication.toAccountId()
        val walletId = authentication.toWalletId()
        val marker = Markers.append("accountId", accountId.value)
            .andAppend("walletId", walletId.value)
            .andAppend("request", request)
        return try {
            val response = sweepingAccountService.requestSweepingCashIn(
                RequestSweepingCashInCommand(
                    consentId = request.consentId,
                    requestSource = SweepingCashInSource.WEB_APP,
                    sweepingCashInId = SweepingCashInId(request.id),
                    walletId = walletId,
                    amount = request.amount,
                    description = request.description,
                    externalTransactionId = null,
                    participantId = null,
                ),
            )
            response.fold(
                ifLeft = { error ->
                    val errorCode = error.toErrorCode()
                    marker.andAppend("errorCode", errorCode)
                    if (error.isWarning()) {
                        logger.warn(marker, logName)
                    } else {
                        logger.error(marker, logName)
                    }
                    HttpResponse.badRequest(mapOf("code" to errorCode, "message" to "Error ao fazer o cashIn com conta conectada."))
                },

                ifRight = {
                    logger.info(marker, logName)
                    HttpResponse.noContent<Unit>()
                },
            )
        } catch (e: Exception) {
            handleException(marker, e, logName)
        }
    }

    @Get("/sweepingTransfer/{requestId}")
    fun getSweepingCashIn(
        authentication: Authentication,
        @PathVariable requestId: String,
    ): HttpResponse<*> {
        val logName = "SweepingAccountController#getSweepingCashIn"
        val accountId = AccountId(authentication.name)
        val marker = Markers.append("accountId", accountId.value)
            .andAppend("sweepingCashInRequestId", requestId)
        return try {
            sweepingAccountService.getSweepingCashIn(SweepingCashInId(requestId)).map {
                marker.andAppend("sweepingCashIn", it)
                logger.info(marker, logName)
                HttpResponse.ok(it.toSweepingCashInResponseTO())
            }.getOrElse {
                marker.andAppend("error", it)
                when (it) {
                    is GetSweepingCashInError.GenericError -> {
                        logger.error(marker, logName)
                        HttpResponse.serverError(it.message)
                    }
                    is GetSweepingCashInError.ItemNotFound -> {
                        logger.warn(marker, logName)
                        HttpResponse.notFound(it.sweepingCashInId.value)
                    }
                }
            }
        } catch (e: Exception) {
            handleException(marker, e, logName)
        }
    }

    private fun handleException(
        markers: LogstashMarker,
        e: Exception,
        logName: String,
    ): HttpResponse<*> {
        logger.error(markers.andAppend("httpStatus", HttpStatus.INTERNAL_SERVER_ERROR), logName, e)
        return HttpResponse.status<Unit>(HttpStatus.INTERNAL_SERVER_ERROR)
    }
}

data class SweepingCashInRequestTO(
    val id: String,
    val consentId: String?,
    val amount: Long,
    val description: String,
)

data class SweepingCashInResponseTO(
    val requestId: String,
    val amount: Long,
    val status: SweepingCashInStatus,
    val error: String? = null,
    val errorDescription: String? = null,
)

fun OpenFinanceSweepingCashIn.toSweepingCashInResponseTO() = SweepingCashInResponseTO(
    requestId = this.id.value,
    amount = this.amount,
    status = this.status,
    error = this.error,
    errorDescription = this.errorDescription,
)