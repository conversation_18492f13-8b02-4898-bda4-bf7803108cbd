package ai.friday.billpayment.modules.openfinance.adapters

import ai.friday.billpayment.adapters.dynamodb.AbstractOpenFinanceDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.GlobalSecondaryIndexNames
import ai.friday.billpayment.adapters.dynamodb.OPEN_FINANCE_DATA_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.OPEN_FINANCE_DATA_RANGE_KEY
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.OpenFinanceParticipantId
import ai.friday.billpayment.modules.openfinance.OpenFinance
import ai.friday.billpayment.modules.openfinance.app.ConsentStatus
import ai.friday.billpayment.modules.openfinance.app.DataConsent
import ai.friday.billpayment.modules.openfinance.app.DataConsentId
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceDataConsentRepository
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceResourceType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import jakarta.inject.Singleton
import java.net.URI
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class OpenFinanceDataConsentDynamoDAO(
    cli: DynamoDbEnhancedClient,
) : AbstractOpenFinanceDynamoDAO<OpenFinanceDataConsentEntity>(cli, OpenFinanceDataConsentEntity::class.java)

@OpenFinance
class OpenFinanceDataConsentDBRepository(
    private val client: OpenFinanceDataConsentDynamoDAO,
) : OpenFinanceDataConsentRepository {
    override fun save(dataConsent: DataConsent): DataConsent {
        val entity =
            OpenFinanceDataConsentEntity().apply {
                primaryKey = dataConsent.dataConsentId.value
                scanKey = dataConsent.accountId.value
                gSIndex1PartitionKey = dataConsent.accountId.value
                gSIndex2RangeKey = dataConsent.accountId.value
                accountId = dataConsent.accountId.value
                document = dataConsent.document.value
                createdAt = dataConsent.createdAt.format(dateTimeFormat)
                updatedAt = getZonedDateTime().format(dateTimeFormat)
                participant = dataConsent.participantId.value
                status = dataConsent.status
                url = dataConsent.url.toExternalForm()
            }
        client.save(entity)

        return entity.toDomain()
    }

    override fun findOrNull(dataConsentId: DataConsentId, accountId: AccountId): DataConsent? {
        return client.findByPrimaryKey(
            partitionKey = dataConsentId.value,
            sortKey = accountId.value,
        )?.toDomain()
    }

    override fun findByAccountId(accountId: AccountId): List<DataConsent> {
        return client.findByPartitionKeyAndScanKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = accountId.value,
            sortKey = OpenFinanceResourceType.DATA_CONSENT.name,
        ).map {
            it.toDomain()
        }
    }

    override fun findAll(): List<DataConsent> {
        return client.findByPartitionKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex2,
            partitionKey = OpenFinanceResourceType.DATA_CONSENT.name,
        ).map {
            it.toDomain()
        }
    }
}

@DynamoDbBean
class OpenFinanceDataConsentEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_DATA_PARTITION_KEY)
    lateinit var primaryKey: String // consentId

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_DATA_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1PartitionKey")
    lateinit var gSIndex1PartitionKey: String // accountId

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1RangeKey")
    var gSIndex1RangeKey: String = OpenFinanceResourceType.DATA_CONSENT.name

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2PartitionKey")
    var gSIndex2PartitionKey: String = OpenFinanceResourceType.DATA_CONSENT.name

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2RangeKey")
    lateinit var gSIndex2RangeKey: String // accountId

    @get:DynamoDbAttribute(value = "AccountId")
    lateinit var accountId: String

    @get:DynamoDbAttribute(value = "ResourceType")
    var resourceType: OpenFinanceResourceType = OpenFinanceResourceType.DATA_CONSENT

    @get:DynamoDbAttribute(value = "Document")
    lateinit var document: String

    @get:DynamoDbAttribute(value = "Participant")
    lateinit var participant: String

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: ConsentStatus

    @get:DynamoDbAttribute(value = "Url")
    lateinit var url: String

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute(value = "UpdatedAt")
    lateinit var updatedAt: String

    fun toDomain() = DataConsent(
        dataConsentId = DataConsentId(primaryKey),
        participantId = OpenFinanceParticipantId(participant),
        accountId = AccountId(accountId),
        document = Document(document),
        url = URI.create(url).toURL(),
        status = status,
        createdAt = ZonedDateTime.parse(createdAt, dateTimeFormat),
        updatedAt = ZonedDateTime.parse(updatedAt, dateTimeFormat),
    )
}