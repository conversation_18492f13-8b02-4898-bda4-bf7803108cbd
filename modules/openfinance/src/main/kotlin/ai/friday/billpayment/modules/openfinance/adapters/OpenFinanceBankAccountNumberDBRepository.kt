package ai.friday.billpayment.modules.openfinance.adapters

import ai.friday.billpayment.adapters.dynamodb.AbstractOpenFinanceDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.GlobalSecondaryIndexNames
import ai.friday.billpayment.adapters.dynamodb.OPEN_FINANCE_DATA_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.OPEN_FINANCE_DATA_RANGE_KEY
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.modules.openfinance.OpenFinance
import ai.friday.billpayment.modules.openfinance.app.BankAccountNumberData
import ai.friday.billpayment.modules.openfinance.app.BankAccountNumberId
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceBankAccountNumber
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceBankAccountNumberRepository
import ai.friday.billpayment.modules.openfinance.app.removeLeadingZeros
import ai.friday.morning.date.dateTimeFormat
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val ENTITY_ID = "BANK_ACCOUNT_NUMBER"

@Singleton
class OpenFinanceBankAccountNumberDynamoDAO(
    cli: DynamoDbEnhancedClient,
) : AbstractOpenFinanceDynamoDAO<OpenFinanceBankAccountNumberEntity>(cli, OpenFinanceBankAccountNumberEntity::class.java)

@OpenFinance
class OpenFinanceBankAccountNumberDBRepository(
    private val client: OpenFinanceBankAccountNumberDynamoDAO,
) : OpenFinanceBankAccountNumberRepository {
    private val logger = LoggerFactory.getLogger(OpenFinanceBankAccountNumberDBRepository::class.java)

    override fun save(bankAccountNumber: OpenFinanceBankAccountNumber): OpenFinanceBankAccountNumber {
        val sanitizedRoutingNumber = bankAccountNumber.data.routingNumber.removeLeadingZeros()
        val sanitizedAccountNumber = bankAccountNumber.data.accountNumber.removeLeadingZeros()

        val entity =
            OpenFinanceBankAccountNumberEntity().apply {
                primaryKey = bankAccountNumber.id.value
                scanKey = ENTITY_ID
                gSIndex1PartitionKey = bankAccountNumber.accountId.value
                gSIndex1RangeKey = "$ENTITY_ID#${bankAccountNumber.data.ispb}#$sanitizedRoutingNumber"
                gSIndex2RangeKey = "${bankAccountNumber.data.ispb}#$sanitizedRoutingNumber#$sanitizedAccountNumber#${bankAccountNumber.data.accountDv.orEmpty()}"
                accountId = bankAccountNumber.accountId.value
                ispb = bankAccountNumber.data.ispb
                bankName = bankAccountNumber.data.bankName
                routingNumber = sanitizedRoutingNumber
                accountNumber = sanitizedAccountNumber
                accountDv = bankAccountNumber.data.accountDv
                createdAt = bankAccountNumber.createdAt.format(dateTimeFormat)
                updatedAt = bankAccountNumber.updatedAt.format(dateTimeFormat)
            }
        client.save(entity)

        return entity.toDomain()
    }

    override fun findOrNull(bankAccountNumberId: BankAccountNumberId): OpenFinanceBankAccountNumber? = client.findByPrimaryKey(
        partitionKey = bankAccountNumberId.value,
        sortKey = ENTITY_ID,
    )?.toDomain()

    override fun findByAccountIdAndIspbAndRoutingNumber(accountId: AccountId, ispb: String, routingNumber: String): List<OpenFinanceBankAccountNumber> = client.findByPartitionKeyAndScanKeyOnIndex(
        index = GlobalSecondaryIndexNames.GSIndex1,
        partitionKey = accountId.value,
        sortKey = "$ENTITY_ID#$ispb#${routingNumber.removeLeadingZeros()}",
    ).map {
        it.toDomain()
    }

    private fun OpenFinanceBankAccountNumberEntity.toDomain() = OpenFinanceBankAccountNumber(
        id = BankAccountNumberId(primaryKey),
        accountId = AccountId(accountId),
        data = BankAccountNumberData(
            ispb = ispb,
            bankName = bankName,
            routingNumber = routingNumber,
            accountNumber = accountNumber,
            accountDv = accountDv,
        ),
        createdAt = ZonedDateTime.parse(createdAt, dateTimeFormat),
        updatedAt = ZonedDateTime.parse(updatedAt, dateTimeFormat),
    )
}

@DynamoDbBean
class OpenFinanceBankAccountNumberEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_DATA_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_DATA_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1PartitionKey")
    lateinit var gSIndex1PartitionKey: String // accountId

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1RangeKey")
    lateinit var gSIndex1RangeKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2PartitionKey")
    var gSIndex2PartitionKey: String = ENTITY_ID

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2RangeKey")
    lateinit var gSIndex2RangeKey: String

    @get:DynamoDbAttribute(value = "AccountId")
    lateinit var accountId: String

    @get:DynamoDbAttribute(value = "Ispb")
    lateinit var ispb: String

    @get:DynamoDbAttribute(value = "BankName")
    lateinit var bankName: String

    @get:DynamoDbAttribute(value = "RoutingNumber")
    lateinit var routingNumber: String

    @get:DynamoDbAttribute(value = "AccountNumber")
    lateinit var accountNumber: String

    @get:DynamoDbAttribute(value = "AccountDv")
    var accountDv: String? = null

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute(value = "UpdatedAt")
    lateinit var updatedAt: String
}