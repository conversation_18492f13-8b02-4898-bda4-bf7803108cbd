package ai.friday.billpayment.modules.openfinance.adapters.api

import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.modules.openfinance.OpenFinance
import ai.friday.billpayment.modules.openfinance.app.BankAccountNumberId
import ai.friday.billpayment.modules.openfinance.app.BankAccountService
import ai.friday.morning.date.javascriptDateTimeFormat
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.validation.Validated
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Validated
@Secured(Role.Code.OWNER)
@Controller("/openfinance")
@OpenFinance
@Version("2")
class BankAccountController(
    private val bankAccountService: BankAccountService,
) {
    private val logger = LoggerFactory.getLogger(ConsentController::class.java)

    @Get("/bankAccount/{bankAccountId}/balance")
    fun getBalance(
        @PathVariable bankAccountId: String,
        authentication: Authentication,
    ): HttpResponse<*> {
        val logName = "BankAccountController#getBalance"
        val accountId = authentication.toAccountId()
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("bankAccountId", bankAccountId)

        return try {
            bankAccountService.getBalance(accountId, BankAccountNumberId(bankAccountId)).map {
                logger.info(markers.andAppend("balance", it), logName)
                HttpResponse.ok(
                    BankAccountBalanceTO(
                        amount = it.automaticallyInvestedAmount,
                        updateDateTime = it.updateDateTime.format(javascriptDateTimeFormat),
                    ),
                )
            }.getOrElse {
                logger.error(markers.andAppend("error", it), logName)
                HttpResponse.serverError(it)
            }
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            HttpResponse.serverError(e.message.orEmpty())
        }
    }
}

data class BankAccountBalanceTO(
    val amount: Long,
    val updateDateTime: String,
)