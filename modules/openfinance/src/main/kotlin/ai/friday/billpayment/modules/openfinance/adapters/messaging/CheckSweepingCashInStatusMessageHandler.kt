package ai.friday.billpayment.modules.openfinance.adapters.messaging

import ai.friday.billpayment.adapters.messaging.Handler
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.integrations.SweepingCashInId
import ai.friday.billpayment.modules.openfinance.OpenFinance
import ai.friday.billpayment.modules.openfinance.app.CheckSweepingCashInStatusMessage
import ai.friday.billpayment.modules.openfinance.app.GetSweepingCashInError
import ai.friday.billpayment.modules.openfinance.app.SweepingAccountService
import ai.friday.morning.log.andAppend
import ai.friday.morning.messaging.MessageHandler
import ai.friday.morning.messaging.MessageHandlerResponse
import arrow.core.getOrElse
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

@Handler
@OpenFinance
open class CheckSweepingCashInStatusMessageHandler(
    private val sweepingAccountService: SweepingAccountService,
) : MessageHandler {
    private val logger = LoggerFactory.getLogger(CheckSweepingCashInStatusMessageHandler::class.java)

    override val configurationName = "check-sweeping-cash-in-status"

    override fun handleException(e: Exception) = MessageHandlerResponse.keep()

    override fun handleMessage(message: Message): MessageHandlerResponse {
        val markers = Markers.empty()
        val logName = "CheckSweepingCashInStatusMessageHandler#handleMessage"

        val messageBody = parseObjectFrom<CheckSweepingCashInStatusMessage>(message.body())
        markers.andAppend("messageBody", messageBody)

        val shouldDeleteMessage = sweepingAccountService.getSweepingCashIn(SweepingCashInId(messageBody.sweepingCashInId)).map {
            markers.andAppend("sweepingCashIn", it)
            it.status.finalExternalStatus
        }.getOrElse {
            markers.andAppend("error", it)
            when (it) {
                is GetSweepingCashInError.GenericError -> false
                is GetSweepingCashInError.ItemNotFound -> true
            }
        }
        markers.andAppend("shouldDeleteMessage", shouldDeleteMessage)

        logger.info(markers, logName)
        return MessageHandlerResponse.build(shouldDeleteMessage = shouldDeleteMessage)
    }
}