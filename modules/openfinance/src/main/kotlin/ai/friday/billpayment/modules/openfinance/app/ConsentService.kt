package ai.friday.billpayment.modules.openfinance.app

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.PaymentMethodNotFound
import ai.friday.billpayment.app.account.isAlphaGroup
import ai.friday.billpayment.app.account.isBetaGroup
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.integrations.CrmRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.NotificationService
import ai.friday.billpayment.app.integrations.OFSweepingConsent
import ai.friday.billpayment.app.integrations.OpenFinanceConsentId
import ai.friday.billpayment.app.integrations.OpenFinanceConsentService
import ai.friday.billpayment.app.integrations.OpenFinanceParticipantId
import ai.friday.billpayment.app.integrations.SweepingParticipant
import ai.friday.billpayment.app.notification.ButtonDeeplinkParameter
import ai.friday.billpayment.app.notification.NotificationTemplate
import ai.friday.billpayment.app.notification.NotificationType
import ai.friday.billpayment.app.notification.TemplatesConfiguration
import ai.friday.billpayment.app.notification.WhatsappNotification
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.modules.openfinance.OpenFinance
import ai.friday.billpayment.modules.openfinance.adapters.api.OpenFinanceConsentType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Primary
import java.net.URL
import java.time.ZonedDateTime
import java.util.UUID
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Primary
@OpenFinance
class ConsentService(
    private val accountService: AccountService,
    private val dataConsentRepository: OpenFinanceDataConsentRepository,
    private val bankAccountRepository: OpenFinanceBankAccountRepository,
    private val sweepingAccountRepository: OpenFinanceSweepingAccountRepository,
    private val openFinanceAdapter: OpenFinanceAdapter,
    private val participants: List<SweepingParticipant>,
    private val notificationService: NotificationService,
    private val templatesConfiguration: TemplatesConfiguration,
    private val walletService: WalletService,
    private val crmRepository: CrmRepository,
) : OpenFinanceConsentService {
    private val logger = LoggerFactory.getLogger(ConsentService::class.java)

    init {
        logger.info(append("participants", participants), "ConsentService#init")
    }

    override fun getParticipants(): List<SweepingParticipant> {
        return participants
    }

    fun getSweepingConsent(walletId: WalletId): Either<OpenFinanceSweepingAccountError, SweepingConsent> {
        val consent = sweepingAccountRepository.findByWalletId(walletId).firstOrNull {
            it.status.isActive()
        }
        return consent?.right() ?: OpenFinanceSweepingAccountError.ConsentNotFound.left()
    }

    fun getSweepingConsent(sweepingConsentId: SweepingConsentId) = sweepingAccountRepository.findBySweepingAccountId(sweepingConsentId = sweepingConsentId)

    private fun SweepingConsent.toSweepingConsentWithBankAccountData() = SweepingConsentWithBankAccountData(
        id = id,
        accountId = accountId,
        walletId = walletId,
        creditorId = creditorId,
        participantId = participantId,
        status = status,
        startDateTime = startDateTime,
        expirationDateTime = expirationDateTime,
        createdAt = createdAt,
        updatedAt = updatedAt,
        url = url,
        configuration = configuration,
        debtor = debtor,
        bankAccountData = if (debtor != null) {
            SweepingDebtor2BankAccountDataAdapter(debtor)
        } else {
            BankAccountDataPlaceholder(participantId.toParticipant())
        },
    )

    private fun getActiveBankAccountSweepingConsents(walletId: WalletId) = sweepingAccountRepository.findByWalletId(walletId).filter {
        it.status.isActive()
    }.map {
        it.toSweepingConsentWithBankAccountData()
    }

    fun getActiveConsents(accountId: AccountId, walletId: WalletId): List<OpenFinanceBankAccountConsent> {
        val wallet = walletService.findWallet(walletId)

        return if (wallet.founder.accountId != accountId) {
            emptyList()
        } else {
            getActiveBankAccountSweepingConsents(walletId) + getActiveBankAccountDataConsents(accountId)
        }
    }

    fun getActiveBankAccountDataConsents(accountId: AccountId): List<BankAccountDataConsent> = dataConsentRepository.findByAccountId(accountId).filter {
        it.status.isActive()
    }.flatMap { dataConsent ->
        bankAccountRepository.findByDataConsentId(dataConsent.dataConsentId).map { bankAccount ->
            BankAccount2BankAccountDataAdapter(bankAccount)
        }.ifEmpty {
            listOf(BankAccountDataPlaceholder(dataConsent.participantId.toParticipant()))
        }.map {
            BankAccountDataConsent(
                id = dataConsent.dataConsentId,
                participantId = dataConsent.participantId,
                status = dataConsent.status,
                createdAt = dataConsent.createdAt,
                bankAccountData = it,
            )
        }
    }

    override fun getAuthorizedSweepingConsents(walletId: WalletId): List<OFSweepingConsent> {
        return sweepingAccountRepository.findByWalletId(walletId).filter {
            it.status == ConsentStatus.AUTHORISED
        }.mapNotNull { consent ->
            consent.participantId.toParticipant().let { participant ->
                openFinanceAdapter.getSweepingConsentPeriodicUsage(consent.id).map { periodicUsage ->
                    OFSweepingConsent(
                        participantId = consent.participantId,
                        participantName = participant.name,
                        participantShortName = participant.shortName,
                        transactionLimit = consent.configuration.transactionLimit,
                        lastSucessfulCashIn = consent.lastSuccessfulCashIn,
                        periodicUsage = periodicUsage,
                    )
                }.getOrElse {
                    val markers = append("consentId", consent.id.value)
                        .andAppend("error", it)
                    logger.error(markers, "ConsentService#getAuthorizedSweepingConsents#mapNotNull")
                    null
                }
            }
        }
    }

    fun createDataConsent(
        id: AccountId,
        document: Document,
        participantId: OpenFinanceParticipantId,
    ): Either<CustomErrors, ConsentLink> {
        if (!participants.any { it.id == participantId.value }) {
            return OpenFinanceError(IllegalArgumentException(participantId.value)).left()
        }

        return openFinanceAdapter.createDataConsent(id, document, participantId).map {
            dataConsentRepository.save(it)
            DataConsentLink(
                consentUrl = it.url,
            )
        }
    }

    fun createSweepingConsent(accountId: AccountId, participantId: OpenFinanceParticipantId, sweepingLimits: SweepingLimits = defaultSweepingLimits): Either<OpenFinanceSweepingAccountError, SweepingConsentLink> {
        if (!participants.any { it.id == participantId.value }) {
            return OpenFinanceSweepingAccountError.InvalidParticipant.left()
        }

        val wallet = walletService.findPrimaryWallet(accountId)

        val paymentMethod = try {
            accountService.findAccountPaymentMethodByIdAndAccountId(wallet.paymentMethodId, wallet.founder.accountId).method as InternalBankAccount
        } catch (e: PaymentMethodNotFound) {
            return OpenFinanceSweepingAccountError.PaymentMethodNotFound.left()
        }

        return openFinanceAdapter.createSweepingConsent(
            wallet = wallet,
            paymentMethod = paymentMethod,
            participantId = participantId,
            sweepingLimits = sweepingLimits,
        ).map { consentLink ->
            consentLink.also {
                sweepingAccountRepository.save(
                    it.toSweepingConsent(accountId, wallet.id),
                )
            }
        }
    }

    fun updateConsentAndNotify(consentId: SweepingConsentId, status: ConsentStatus, debtor: SweepingDebtor?): Either<Exception, SweepingConsent> {
        val logName = "ConsentService#updateConsentAndNotify"
        val markers = append("consentId", consentId.value)
            .andAppend("updatedStatus", status)

        return try {
            val consent = sweepingAccountRepository.findBySweepingAccountId(consentId)
            markers.andAppend("consentStatus", consent.status)

            val participantConsents = sweepingAccountRepository.findByWalletId(consent.walletId).filter {
                it.id != consent.id && it.participantId == consent.participantId // TODO: checkagem pelo identificador do debtor (?)
            }
            val pendingConsents = participantConsents.filter {
                it.status.isPending()
            }

            if (status == consent.status) {
                consent
            } else {
                if (status == ConsentStatus.AUTHORISED) {
                    revokeAuthorisedConsentsOnPast(consent, participantConsents)
                }

                consent.copy(status = status, updatedAt = ZonedDateTime.now(), debtor = debtor).also {
                    sweepingAccountRepository.save(it)
                    if (pendingConsents.isEmpty()) {
                        notifyChatbot(it, participantConsents)
                    }
                    updateCrm(it)
                }
            }.also {
                logger.info(markers, logName)
            }.right()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            e.left()
        }
    }

    fun updateConsentAndNotify(consentId: DataConsentId, accountId: AccountId, status: ConsentStatus): Either<Exception, DataConsent> {
        val logName = "ConsentService#updateConsentAndNotify"
        val markers = append("consentId", consentId.value)
            .andAppend("accountId", accountId.value)
            .andAppend("updatedStatus", status)

        return try {
            val consent = dataConsentRepository.findOrNull(consentId, accountId)
            if (consent == null) {
                logger.error(markers.andAppend("error", "Sweeping account not found"), logName)
                return ItemNotFoundException("Sweeping account not found").left()
            }

            markers.andAppend("consentStatus", consent.status)

            val participantConsents = dataConsentRepository.findByAccountId(accountId).filter {
                it.dataConsentId != consent.dataConsentId && it.participantId == consent.participantId
            }
            val pendingConsents = participantConsents.filter {
                it.status.isPending()
            }

            if (status == consent.status) {
                consent
            } else {
                if (status == ConsentStatus.AUTHORISED) {
                    revokeAuthorisedConsentsOnPast(consent, participantConsents)
                }

                consent.copy(status = status, updatedAt = ZonedDateTime.now()).also {
                    dataConsentRepository.save(it)
                    if (pendingConsents.isEmpty()) {
                        notifyChatbot(it, participantConsents)
                    }
                }
            }.also {
                logger.info(markers, logName)
            }.right()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            e.left()
        }
    }

    private fun revokeAuthorisedConsentsOnPast(
        consent: SweepingConsent,
        participantConsents: List<SweepingConsent>,
    ) {
        val authorisedOnPast =
            participantConsents.filter {
                it.createdAt.isBefore(consent.createdAt) && it.status == ConsentStatus.AUTHORISED
            }
        authorisedOnPast.forEach {
            sweepingAccountRepository.save(it.copy(status = ConsentStatus.REVOKED))
        }
    }

    private fun revokeAuthorisedConsentsOnPast(
        consent: DataConsent,
        participantConsents: List<DataConsent>,
    ) {
        val authorisedOnPast =
            participantConsents.filter {
                it.createdAt.isBefore(consent.createdAt) && it.status == ConsentStatus.AUTHORISED
            }
        authorisedOnPast.forEach {
            dataConsentRepository.save(it.copy(status = ConsentStatus.REVOKED))
        }
    }

    private fun OpenFinanceParticipantId.toParticipant(): SweepingParticipant {
        return participants.firstOrNull {
            it.id == value
        } ?: SweepingParticipant(
            id = value,
            name = value,
            shortName = value,
            compe = value,
            ispb = value,
            restrictedTo = null,
        )
    }

    private fun updateCrm(consent: SweepingConsent) {
        try {
            when (consent.status) {
                ConsentStatus.AWAITING_AUTHORISATION -> {}
                ConsentStatus.PARTIALLY_ACCEPTED -> {}
                ConsentStatus.CONSUMED -> {}
                ConsentStatus.CANCELED -> {}
                ConsentStatus.REJECTED -> {}
                ConsentStatus.AUTHORISED -> crmRepository.publishEvent(
                    accountId = consent.accountId,
                    eventName = "open_finance_conta_conectada",
                    customParams = mapOf(
                        "bankName" to (consent.debtor?.bankName ?: "unknown"),
                    ),
                )

                ConsentStatus.REVOKED -> crmRepository.publishEvent(
                    accountId = consent.accountId,
                    eventName = "open_finance_conta_desconectada",
                    customParams = mapOf(
                        "bankName" to (consent.debtor?.bankName ?: "unknown"),
                    ),
                )
            }
        } catch (e: Exception) {
            logger.error(append("consent", consent), "ConsentService#updateCrm", e)
        }
    }

    private fun notifyChatbot(consent: SweepingConsent, participantConsents: List<SweepingConsent>) {
        val markers = append("consent", consent)
        val logName = "ConsentService#notifyChatbot"

        val bankName = getBankName(consent)
        val parameters = listOf(bankName)
        markers.andAppend("parameters", parameters)

        val editingRange = getZonedDateTime().minusMinutes(10)

        val isEdit = participantConsents.any {
            it.status == ConsentStatus.AUTHORISED && it.createdAt.isBefore(editingRange)
        }

        val recentConsents = participantConsents.filter { it.createdAt.isAfter(editingRange) }

        val statusToNotify = if (recentConsents.any { it.status == ConsentStatus.AUTHORISED }) {
            ConsentStatus.AUTHORISED
        } else {
            consent.status
        }

        markers.andAppend("statusToNotify", statusToNotify)

        val account = accountService.findAccountById(consent.accountId)

        val shouldNotifyDataIncentive = account.isAlphaGroup() || account.isBetaGroup()

        logger.info(markers, logName)

        when {
            isEdit && statusToNotify == ConsentStatus.AUTHORISED -> notifyAccountEdited(account, parameters)
            isEdit && statusToNotify == ConsentStatus.REJECTED -> notifyAccountEditFails(account, parameters)
            !isEdit && !shouldNotifyDataIncentive && statusToNotify == ConsentStatus.AUTHORISED -> notifyAccountConnected(account, parameters)
            !isEdit && shouldNotifyDataIncentive && statusToNotify == ConsentStatus.AUTHORISED -> notifyAccountConnectedWithDataIncentive(account, parameters)
            !isEdit && statusToNotify == ConsentStatus.REJECTED -> notifyConnectionFails(account, parameters)
        }
    }

    private fun notifyChatbot(consent: DataConsent, participantConsents: List<DataConsent>) {
        val markers = append("consent", consent)
        val logName = "ConsentService#notifyChatbot"

        val editingRange = getZonedDateTime().minusMinutes(10)

        val recentConsents = participantConsents.filter { it.createdAt.isAfter(editingRange) }

        val statusToNotify = if (recentConsents.any { it.status == ConsentStatus.AUTHORISED }) {
            ConsentStatus.AUTHORISED
        } else {
            consent.status
        }

        markers.andAppend("statusToNotify", statusToNotify)

        val account = accountService.findAccountById(consent.accountId)

        logger.info(markers, logName)

        if (statusToNotify == ConsentStatus.AUTHORISED) {
            notifyDataConsentConnected(account)
        } else if (statusToNotify == ConsentStatus.REJECTED) {
            notifyDataConsentFailed(account)
        }
    }

    fun notifyAccountConnected(account: Account, parameters: List<String>) {
        val logName = "ConsentService#notifyAccountConnected"
        val markers = append("mobilePhone", account.mobilePhone)
            .andAppend("accountId", account.accountId.value)
        logger.info(markers, logName)

        notificationService.notifyAccount(
            account = account,
            type = NotificationType.SWEEPING_ACCOUNT_CONNECTED,
        ) {
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.sweepingAccountConnected),
                configurationKey = "sweeping-account-connected",
                parameters = parameters,
                buttonWhatsAppParameter = null,
            )
        }
    }

    fun notifyAccountConnectedWithDataIncentive(account: Account, parameters: List<String>) {
        val logName = "ConsentService#notifyAccountConnectedWithDataIncentive"
        val markers = append("mobilePhone", account.mobilePhone)
            .andAppend("accountId", account.accountId.value)
        logger.info(markers, logName)

        notificationService.notifyAccount(
            account = account,
            type = NotificationType.SWEEPING_ACCOUNT_CONNECTED_WITH_DATA_INCENTIVE,
        ) {
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.sweepingAccountConnectedWithDataIncentive),
                configurationKey = "sweeping-account-connected-with-data-incentive",
                parameters = parameters,
                buttonWhatsAppParameter = ButtonDeeplinkParameter("open-finance"),
            )
        }
    }

    fun notifyAccountEdited(account: Account, parameters: List<String>) {
        val logName = "ConsentService#notifyAccountEdited"
        val markers = append("mobilePhone", account.mobilePhone)
            .andAppend("accountId", account.accountId.value)
        logger.info(markers, logName)

        notificationService.notifyAccount(
            account = account,
            type = NotificationType.SWEEPING_ACCOUNT_EDIT,
        ) {
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.sweepingAccountEdit),
                configurationKey = "sweeping-account-edit",
                parameters = parameters,
                buttonWhatsAppParameter = null,
            )
        }
    }

    fun notifyConnectionFails(account: Account, parameters: List<String>) {
        val logName = "ConsentService#notifyConnectionFails"
        val markers = append("mobilePhone", account.mobilePhone)
            .andAppend("accountId", account.accountId.value)
        logger.info(markers, logName)

        notificationService.notifyAccount(
            account = account,
            type = NotificationType.SWEEPING_ACCOUNT_CONNECTION_FAILED,
        ) {
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.sweepingAccountConnectionFailed),
                configurationKey = "sweeping-account-connection-failed",
                parameters = parameters,
                buttonWhatsAppParameter = ButtonDeeplinkParameter("open-finance"),
            )
        }
    }

    fun notifyAccountEditFails(
        account: Account,
        parameters: List<String>,
    ) {
        val logName = "ConsentService#notifyAccountEditFails"
        val markers = append("mobilePhone", account.mobilePhone)
            .andAppend("accountId", account.accountId.value)
        logger.info(markers, logName)

        notificationService.notifyAccount(
            account = account,
            type = NotificationType.SWEEPING_ACCOUNT_EDIT_FAILED,
        ) {
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.sweepingAccountEditFailed),
                configurationKey = "sweeping-account-edit-failed",
                parameters = parameters,
                buttonWhatsAppParameter = ButtonDeeplinkParameter("open-finance"),
            )
        }
    }

    fun notifyDataConsentConnected(account: Account) {
        val logName = "ConsentService#notifyDataConsentConnected"
        val markers = append("mobilePhone", account.mobilePhone)
            .andAppend("accountId", account.accountId.value)

        logger.info(markers, logName)

        notificationService.notifyAccount(
            account = account,
            type = NotificationType.DATA_CONSENT_CONNECTED,
        ) {
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.dataConsentConnected),
                configurationKey = "data-consent-connected",
                buttonWhatsAppParameter = null,
            )
        }
    }

    fun notifyDataConsentFailed(account: Account) {
        val logName = "ConsentService#notifyDataConsentFailed"
        val markers = append("mobilePhone", account.mobilePhone)
            .andAppend("accountId", account.accountId.value)

        logger.info(markers, logName)

        notificationService.notifyAccount(
            account = account,
            type = NotificationType.DATA_CONSENT_CONNECTION_FAILED,
        ) {
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.dataConsentConnectionFailed),
                configurationKey = "data-consent-connection-failed",
                buttonWhatsAppParameter = null,
            )
        }
    }

    fun revokeConsent(consentId: SweepingConsentId, accountId: AccountId): Either<OpenFinanceSweepingAccountError, SweepingConsent> {
        val logName = "ConsentService#revokeConsent"
        val markers = append("consentId", consentId.value)
            .andAppend("accountId", accountId.value)

        return try {
            val consent = sweepingAccountRepository.findBySweepingAccountId(consentId)
            markers.andAppend("consent", consent)

            when {
                consent.accountId != accountId -> {
                    logger.warn(markers, logName)
                    OpenFinanceSweepingAccountError.ConsentNotFound.left()
                }

                consent.status == ConsentStatus.REVOKED -> {
                    logger.info(markers, logName)
                    consent.right()
                }

                else -> {
                    val revokeConsentResponse = openFinanceAdapter.revokeSweepingConsent(consentId)
                    revokeConsentResponse.isLeft {
                        logger.error(markers, logName, it)
                        return OpenFinanceSweepingAccountError.ConsentRevokeError(it.message.orEmpty()).left()
                    }
                    consent.copy(status = ConsentStatus.REVOKED, updatedAt = ZonedDateTime.now()).also {
                        sweepingAccountRepository.save(it)
                        updateCrm(it)
                        logger.info(markers, logName)
                    }.right()
                }
            }
        } catch (e: ItemNotFoundException) {
            logger.warn(markers, logName, e)
            OpenFinanceSweepingAccountError.ConsentNotFound.left()
        }
    }

    fun revokeConsent(consentId: DataConsentId, accountId: AccountId): Either<Exception, DataConsent> {
        val logName = "ConsentService#revokeConsent"
        val markers = append("consentId", consentId.value)
            .andAppend("accountId", accountId.value)

        return try {
            val consent = dataConsentRepository.findOrNull(consentId, accountId)
            if (consent == null) {
                logger.warn(markers, logName)
                return Exception("Data consent not found").left()
            }

            markers.andAppend("consent", consent)

            when {
                consent.accountId != accountId -> {
                    logger.warn(markers, logName)
                    Exception("Data consent not found").left()
                }

                consent.status == ConsentStatus.REVOKED -> {
                    logger.info(markers, logName)
                    consent.right()
                }

                else -> {
                    val revokeConsentResponse = openFinanceAdapter.revokeDataConsent(consentId)
                    revokeConsentResponse.isLeft {
                        logger.error(markers, logName, it)
                        return it.left()
                    }
                    consent.copy(status = ConsentStatus.REVOKED, updatedAt = ZonedDateTime.now()).also {
                        dataConsentRepository.save(it)
                        logger.info(markers, logName)
                    }.right()
                }
            }
        } catch (e: ItemNotFoundException) {
            logger.warn(markers, logName, e)
            Exception("Data consent not found").left()
        }
    }

    private fun getBankName(consent: SweepingConsent): String =
        consent.debtor?.bankName ?: participants.firstOrNull { it.id == consent.participantId.value }?.name ?: "escolhido"
}

interface OpenFinanceBankAccountConsent {
    val id: OpenFinanceConsentId
    val type: OpenFinanceConsentType
    val participantId: OpenFinanceParticipantId
    val status: ConsentStatus
    val createdAt: ZonedDateTime
    val bankAccountData: OpenFinanceBankAccountData
}

data class BankAccountNumberId(val value: String = "BANK-ACCOUNT-NUMBER-${UUID.randomUUID()}")

interface OpenFinanceBankAccountData {
    val id: BankAccountNumberId
    val ispb: String
    val bankName: String
    val routingNumber: String
    val accountNumber: String
}

data class BankAccountDataPlaceholder(
    val participant: SweepingParticipant,
) : OpenFinanceBankAccountData {
    override val id: BankAccountNumberId = BankAccountNumberId(participant.id)
    override val ispb: String = participant.ispb
    override val bankName: String = participant.name
    override val routingNumber: String = "****"
    override val accountNumber: String = "******"
}

data class BankAccount2BankAccountDataAdapter(
    val bankAccount: OpenFinanceBankAccount,
) : OpenFinanceBankAccountData {
    override val id: BankAccountNumberId = bankAccount.bankAccountNumberId ?: BankAccountNumberId(bankAccount.routingNumber + "#" + bankAccount.accountNumber)
    override val ispb: String = bankAccount.ispb.orEmpty()
    override val bankName: String = bankAccount.brandName ?: bankAccount.ispb.orEmpty()
    override val routingNumber: String = bankAccount.routingNumber.orEmpty()
    override val accountNumber: String = bankAccount.accountNumber.orEmpty()
}

data class SweepingDebtor2BankAccountDataAdapter(
    val debtor: SweepingDebtor,
) : OpenFinanceBankAccountData {
    override val id: BankAccountNumberId = debtor.bankAccountNumberId ?: BankAccountNumberId(debtor.routingNumber + "#" + debtor.accountNumber)
    override val ispb: String = debtor.ispb
    override val bankName: String = debtor.bankName ?: debtor.ispb
    override val routingNumber: String = debtor.routingNumber
    override val accountNumber: String = debtor.accountNumber
}

data class BankAccountDataConsent(
    override val id: DataConsentId,
    override val participantId: OpenFinanceParticipantId,
    override val status: ConsentStatus,
    override val createdAt: ZonedDateTime,
    override val bankAccountData: OpenFinanceBankAccountData,
) : OpenFinanceBankAccountConsent {
    override val type = OpenFinanceConsentType.DATA
}

data class SweepingConsent(
    val id: SweepingConsentId,
    val accountId: AccountId,
    val walletId: WalletId,
    val creditorId: CreditorId,
    val participantId: OpenFinanceParticipantId,
    val status: ConsentStatus,
    val startDateTime: ZonedDateTime?,
    val expirationDateTime: ZonedDateTime?,
    val lastSuccessfulCashIn: ZonedDateTime?,
    val createdAt: ZonedDateTime,
    val updatedAt: ZonedDateTime,
    val url: URL,
    val configuration: SweepingLimits,
    val debtor: SweepingDebtor? = null,
)

data class SweepingConsentWithBankAccountData(
    override val id: SweepingConsentId,
    val accountId: AccountId,
    val walletId: WalletId,
    val creditorId: CreditorId,
    override val participantId: OpenFinanceParticipantId,
    override val status: ConsentStatus,
    val startDateTime: ZonedDateTime?,
    val expirationDateTime: ZonedDateTime?,
    override val createdAt: ZonedDateTime,
    val updatedAt: ZonedDateTime,
    val url: URL,
    val configuration: SweepingLimits,
    val debtor: SweepingDebtor? = null,
    override val bankAccountData: OpenFinanceBankAccountData,
) : OpenFinanceBankAccountConsent {
    override val type = OpenFinanceConsentType.SWEEPING
}

enum class ConsentStatus {
    AWAITING_AUTHORISATION, PARTIALLY_ACCEPTED, AUTHORISED, REJECTED, REVOKED, CONSUMED, CANCELED;

    fun isActive(): Boolean {
        return when (this) {
            AWAITING_AUTHORISATION, PARTIALLY_ACCEPTED, AUTHORISED -> true
            CONSUMED, REJECTED, REVOKED, CANCELED -> false
        }
    }

    fun isPending(): Boolean {
        return when (this) {
            AWAITING_AUTHORISATION, PARTIALLY_ACCEPTED -> true
            AUTHORISED, REJECTED, REVOKED, CANCELED, CONSUMED -> false
        }
    }
}

data class SweepingConsentId(override val value: String) : OpenFinanceConsentId

data class CreditorId(val value: String)

data class SweepingDebtor(
    val ispb: String,
    val routingNumber: String,
    val accountNumber: String,
    val accountType: String,
    val bankName: String?,
    val bankAccountNumberId: BankAccountNumberId? = null,
)

interface ConsentLink {
    val consentUrl: URL
}

data class DataConsentLink(
    override val consentUrl: URL,
) : ConsentLink

data class SweepingConsentLink(
    val sweepingConsentId: SweepingConsentId,
    val participantId: OpenFinanceParticipantId,
    val creditorId: CreditorId,
    override val consentUrl: URL,
    val expirationDateTime: ZonedDateTime? = null,
    val startDateTime: ZonedDateTime? = null,
    val configuration: SweepingLimits,
) : ConsentLink {
    fun toSweepingConsent(accountId: AccountId, walletId: WalletId) = SweepingConsent(
        accountId = accountId,
        id = sweepingConsentId,
        creditorId = creditorId,
        walletId = walletId,
        participantId = participantId,
        status = ConsentStatus.AWAITING_AUTHORISATION,
        startDateTime = startDateTime,
        expirationDateTime = expirationDateTime,
        createdAt = ZonedDateTime.now(),
        updatedAt = ZonedDateTime.now(),
        url = consentUrl,
        configuration = configuration,
        lastSuccessfulCashIn = null,
    )
}

sealed class GetActiveConsentError : PrintableSealedClassV2() {
    data class ActiveConsentNotFound(val walletId: WalletId) : GetActiveConsentError()
}