package ai.friday.billpayment.modules.openfinance

import io.micronaut.context.annotation.DefaultScope
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton

@DefaultScope(Singleton::class)
@Requires(property = "modules.openfinance.enabled", value = "true")
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
annotation class OpenFinance

@DefaultScope(Singleton::class)
@Requirements(Requires(notEnv = ["test"]), Requires(property = "modules.openfinance.enabled", value = "true"))
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
annotation class OpenFinanceNoTest