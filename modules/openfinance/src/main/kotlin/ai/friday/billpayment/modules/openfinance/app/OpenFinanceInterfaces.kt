package ai.friday.billpayment.modules.openfinance.app

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.integrations.EndToEnd
import ai.friday.billpayment.app.integrations.OFSweepingConsentPeriodicUsage
import ai.friday.billpayment.app.integrations.OpenFinanceParticipantId
import ai.friday.billpayment.app.integrations.RequestSweepingCashInError
import ai.friday.billpayment.app.integrations.SweepingCashInId
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.openfinance.adapters.OpenFinanceSweepingCashIn
import ai.friday.billpayment.modules.openfinance.adapters.SweepingCashInStatusResponse
import arrow.core.Either
import java.time.Duration
import java.time.LocalDate
import java.time.ZonedDateTime
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

interface OpenFinanceBankTransactionRepository {
    fun save(transaction: OpenFinanceBankTransaction)

    fun find(transactionId: String): Mono<OpenFinanceBankTransaction>

    fun findByAccountId(accountId: AccountId): Flux<OpenFinanceBankTransaction>

    fun findByAccountIdAndDate(
        accountId: AccountId,
        from: LocalDate,
        to: LocalDate,
    ): Flux<OpenFinanceBankTransaction>
}

interface OpenFinanceBankAccountRepository {
    fun save(bankAccount: OpenFinanceBankAccount)

    fun findOrNull(
        bankAccountResourceId: BankAccountResourceId,
        accountId: AccountId,
    ): OpenFinanceBankAccount?

    fun findByDataConsentId(dataConsentId: DataConsentId): List<OpenFinanceBankAccount>
}

interface OpenFinanceBankAccountNumberRepository {
    fun save(bankAccountNumber: OpenFinanceBankAccountNumber): OpenFinanceBankAccountNumber

    fun findOrNull(bankAccountNumberId: BankAccountNumberId): OpenFinanceBankAccountNumber?
    fun findByAccountIdAndIspbAndRoutingNumber(accountId: AccountId, ispb: String, routingNumber: String): List<OpenFinanceBankAccountNumber>
}

interface OpenFinanceDataConsentRepository {
    fun save(dataConsent: DataConsent): DataConsent
    fun findOrNull(dataConsentId: DataConsentId, accountId: AccountId): DataConsent?
    fun findByAccountId(accountId: AccountId): List<DataConsent>
    fun findAll(): List<DataConsent>
}

interface OpenFinanceSweepingAccountRepository {
    fun save(account: SweepingConsent): SweepingConsent
    fun findByWalletId(walletId: WalletId): List<SweepingConsent>
    fun findBySweepingAccountId(sweepingConsentId: SweepingConsentId): SweepingConsent
    fun findAll(): List<SweepingConsent>
}

interface OpenFinanceSweepingCashInRepository {
    fun save(sweepingCashIn: OpenFinanceSweepingCashIn)
    fun find(sweepingCashInId: SweepingCashInId): OpenFinanceSweepingCashIn
    fun find(endToEnd: EndToEnd): OpenFinanceSweepingCashIn
    fun findByWalletId(walletId: WalletId, since: Duration): List<OpenFinanceSweepingCashIn>
    fun findOrNull(sweepingCashInId: SweepingCashInId): OpenFinanceSweepingCashIn?
    fun findOrNull(endToEnd: EndToEnd): OpenFinanceSweepingCashIn?
}

interface OpenFinanceAdapter {
    fun createDataConsent(
        accountId: AccountId,
        document: Document,
        participantId: OpenFinanceParticipantId,
    ): Either<CustomErrors, DataConsent>

    fun getBankAccountBalance(
        dataConsentId: DataConsentId,
        bankAccountResourceId: BankAccountResourceId,
    ): Either<String, BankAccountBalance>

    fun createSweepingConsent(
        wallet: Wallet,
        paymentMethod: InternalBankAccount,
        participantId: OpenFinanceParticipantId,
        sweepingLimits: SweepingLimits,
    ): Either<OpenFinanceSweepingAccountError, SweepingConsentLink>

    fun requestSweepingCashIn(
        sweepingCashInId: SweepingCashInId,
        consentId: SweepingConsentId,
        amount: Long,
        description: String,
        creditorId: CreditorId,
        riskSignals: SweepingRiskSignals?,
    ): Either<RequestSweepingCashInError, EndToEnd?>

    fun getSweepingCashInStatus(
        sweepingCashInId: SweepingCashInId,
    ): Either<GetSweepingCashInStatusError, SweepingCashInStatusResponse>

    fun revokeSweepingConsent(
        consentId: SweepingConsentId,
    ): Either<Exception, Unit>

    fun revokeDataConsent(
        dataConsentId: DataConsentId,
    ): Either<Exception, Unit>

    fun getSweepingConsentPeriodicUsage(sweepingConsentId: SweepingConsentId): Either<GetSweepingCashInStatusError, OFSweepingConsentPeriodicUsage>
}

enum class SweepingRiskSignalsType {
    AUTOMATIC, MANUAL
}

sealed interface SweepingRiskSignals {
    val type: SweepingRiskSignalsType
}

data class AutomaticSweepingRiskSignals(
    val lastLoginDateTime: ZonedDateTime,
    val pixKeyRegistrationDateTime: ZonedDateTime? = null,
) : SweepingRiskSignals {
    override val type = SweepingRiskSignalsType.AUTOMATIC
}

data class ManualSweepingRiskSignals(
    val deviceId: String,
    val isRootedDevice: Boolean? = null,
    val screenBrightness: Number? = null,
    val elapsedTimeSinceBoot: java.time.Duration? = null,
    val osVersion: String? = null,
    val userTimeZoneOffset: String? = null,
    val language: String? = null,
    val screenDimensions: ScreenDimensions? = null,
    val accountTenure: LocalDate? = null,
) : SweepingRiskSignals {
    override val type = SweepingRiskSignalsType.MANUAL
}

data class ScreenDimensions(
    val width: Int,
    val height: Int,
)

sealed class GetSweepingCashInStatusError : PrintableSealedClassV2() {
    data class GenericError(val message: String) : GetSweepingCashInStatusError()
}