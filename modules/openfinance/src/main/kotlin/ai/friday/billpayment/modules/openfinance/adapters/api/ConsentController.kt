package ai.friday.billpayment.modules.openfinance.adapters.api

import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.api.asWalletMember
import ai.friday.billpayment.adapters.api.toWalletId
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.integrations.OpenFinanceParticipantId
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.modules.openfinance.OpenFinance
import ai.friday.billpayment.modules.openfinance.app.BankAccountDataConsent
import ai.friday.billpayment.modules.openfinance.app.ConsentService
import ai.friday.billpayment.modules.openfinance.app.ConsentStatus
import ai.friday.billpayment.modules.openfinance.app.DataConsentId
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceBankAccountConsent
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceBankAccountData
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceSweepingAccountError
import ai.friday.billpayment.modules.openfinance.app.PeriodicLimit
import ai.friday.billpayment.modules.openfinance.app.PeriodicLimits
import ai.friday.billpayment.modules.openfinance.app.SweepingConsent
import ai.friday.billpayment.modules.openfinance.app.SweepingConsentId
import ai.friday.billpayment.modules.openfinance.app.SweepingConsentWithBankAccountData
import ai.friday.billpayment.modules.openfinance.app.SweepingDebtor
import ai.friday.billpayment.modules.openfinance.app.SweepingLimits
import ai.friday.morning.date.javascriptDateTimeFormat
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.validation.Validated
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@Validated
@Secured(Role.Code.OWNER)
@Controller("/openfinance")
@OpenFinance
@Version("2")
class ConsentController(
    private val consentService: ConsentService,
) {
    private val logger = LoggerFactory.getLogger(ConsentController::class.java)

    @Get("/consent")
    fun getConsents(
        authentication: Authentication,
    ): HttpResponse<*> {
        val logName = "ConsentController#getConsents"
        val accountId = authentication.toAccountId()
        val walletId = authentication.toWalletId()
        val marker = Markers.append("accountId", accountId.value)
            .andAppend("walletId", walletId.value)
        return try {
            val activeConsents = consentService.getActiveConsents(accountId, walletId)

            logger.info(marker.andAppend("activeConsents", activeConsents), logName)
            HttpResponse.ok(activeConsents.map { it.toOpenFinanceConsentTO() })
        } catch (e: Exception) {
            handleException(marker, e, logName)
        }
    }

    @Post("/consent/data")
    fun createDataConsent(
        authentication: Authentication,
        @Body request: CreateDataConsentRequestTO,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val logName = "ConsentController#createDataConsent"
        val marker = Markers.append("accountId", accountId.value).andAppend("consentRequest", request)
        val walletMember = authentication.asWalletMember()

        if (walletMember.type != MemberType.FOUNDER) {
            return HttpResponse.badRequest(
                ResponseTO(code = "4003", message = "Only the wallet FOUNDER can create a data consent, but current user member type is ${walletMember.type}"),
            )
        }

        return consentService.createDataConsent(accountId, Document(walletMember.document), OpenFinanceParticipantId(request.participant)).map { consent ->
            logger.info(marker, logName)
            HttpResponse.ok(CreateConsentResponseTO(consentPage = consent.consentUrl.toExternalForm()))
        }.getOrElse { e ->
            handleException(marker, e.exception, logName)
        }
    }

    @Post(
        uris = [
            "/sweepingaccount",
            "/consent/sweeping",
        ],
    )
    fun createSweepingConsent(
        authentication: Authentication,
        @Body request: CreateSweepingConsentRequestTO,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val logName = "ConsentController#createSweepingConsent"
        val marker = Markers.append("accountId", accountId.value).andAppend("consentRequest", request)

        return try {
            consentService.createSweepingConsent(
                accountId = accountId,
                participantId = OpenFinanceParticipantId(request.participant),
                sweepingLimits = request.consentLimits.toSweepingLimits(),
            ).map { consent ->
                marker.andAppend("consentId", consent.sweepingConsentId.value)
                logger.info(marker, logName)
                HttpResponse.ok(CreateConsentResponseTO(consentPage = consent.consentUrl.toExternalForm()))
            }.getOrElse {
                it.handle(logger, marker, logName)
            }
        } catch (e: Exception) {
            handleException(marker, e, logName)
        }
    }

    @Delete(
        uris = [
            "/sweepingaccount/{consentId}",
            "/consent/sweeping/{consentId}",
        ],
    )
    fun revokeSweepingAccountConsent(
        authentication: Authentication,
        @PathVariable consentId: String,
    ): HttpResponse<*> {
        val accountId = AccountId(authentication.name)
        val logName = "ConsentController#revokeSweepingAccountConsent"
        val marker = Markers.append("accountId", accountId.value)
        return try {
            val response = consentService.revokeConsent(SweepingConsentId(consentId), accountId)
            response.fold(
                ifLeft = { it.handle(logger, marker, logName) },
                ifRight = { consent ->
                    logger.info(marker.andAppend("consent", consent), logName)
                    HttpResponse.ok("Consent revoked")
                },
            )
        } catch (e: Exception) {
            handleException(marker, e, logName)
        }
    }

    @Delete("/consent/data/{consentId}")
    fun revokeDataConsent(
        authentication: Authentication,
        @PathVariable consentId: String,
    ): HttpResponse<*> {
        val accountId = AccountId(authentication.name)
        val logName = "ConsentController#revokeDataConsent"
        val marker = Markers.append("accountId", accountId.value)
        return try {
            val response = consentService.revokeConsent(DataConsentId(consentId), accountId)
            response.fold(
                ifLeft = { handleException(marker, it, logName) },
                ifRight = { consent ->
                    logger.info(marker.andAppend("consent", consent), logName)
                    HttpResponse.ok("Consent revoked")
                },
            )
        } catch (e: Exception) {
            handleException(marker, e, logName)
        }
    }

    @Get("/sweepingaccount")
    @Deprecated("matar quando atualizar o front")
    fun getSweepingAccountConsent(
        authentication: Authentication,
    ): HttpResponse<*> {
        val walletId = authentication.toWalletId()
        val logName = "ConsentController#getSweepingAccountConsent"
        val marker = Markers.append("walletId", walletId.value)
        return try {
            val response = consentService.getSweepingConsent(walletId)
            response.fold(
                ifLeft = { it.handle(logger, marker, logName) },
                ifRight = { consent ->
                    logger.info(marker.andAppend("consent", consent), logName)
                    HttpResponse.ok(consent.toSweepingConsentTO())
                },
            )
        } catch (e: Exception) {
            handleException(marker, e, logName)
        }
    }

    private fun SweepingConsent.toSweepingConsentTO(): SweepingConsentTO {
        val participantName = participantId.participantName()

        return SweepingConsentTO(
            sweepingAccountId = this.id.value,
            participantId = this.participantId.value,
            participantName = participantName,
            status = this.status,
            startDateTime = this.startDateTime?.format(javascriptDateTimeFormat),
            expirationDateTime = this.expirationDateTime?.format(javascriptDateTimeFormat),
            createdAt = this.createdAt.format(javascriptDateTimeFormat),
            configuration = this.configuration.toSweepingLimitsResponseTO(),
            debtor = this.debtor?.toSweepingDebtorTO(participantName),
        )
    }

    private fun OpenFinanceBankAccountConsent.toOpenFinanceConsentTO(): OpenFinanceConsentTO = when (type) {
        OpenFinanceConsentType.DATA -> (this as BankAccountDataConsent).toOpenFinanceBankAccountDataConsentTO()
        OpenFinanceConsentType.SWEEPING -> (this as SweepingConsentWithBankAccountData).toOpenFinanceSweepingConsentTO()
    }

    private fun BankAccountDataConsent.toOpenFinanceBankAccountDataConsentTO() = OpenFinanceBankAccountDataConsentTO(
        id = this.id.value,
        participantId = this.participantId.value,
        participantName = participantId.participantName(),
        status = this.status,
        createdAt = this.createdAt.format(javascriptDateTimeFormat),
        bankAccount = this.bankAccountData.toOpenFinanceBankAccountTO(),
    )

    private fun SweepingConsentWithBankAccountData.toOpenFinanceSweepingConsentTO() = OpenFinanceSweepingConsentTO(
        id = this.id.value,
        participantId = this.participantId.value,
        participantName = participantId.participantName(),
        status = this.status,
        createdAt = this.createdAt.format(javascriptDateTimeFormat),
        configuration = this.configuration.toSweepingLimitsResponseTO(),
        bankAccount = this.bankAccountData.toOpenFinanceBankAccountTO(),
    )

    private fun OpenFinanceParticipantId.participantName(): String {
        return consentService.getParticipants().firstOrNull { it.id == value }?.name ?: value
    }

    private fun handleException(
        markers: LogstashMarker,
        e: Exception,
        logName: String,
    ): HttpResponse<*> {
        logger.error(markers.andAppend("httpStatus", HttpStatus.INTERNAL_SERVER_ERROR), logName, e)
        return HttpResponse.status<Unit>(HttpStatus.INTERNAL_SERVER_ERROR)
    }
}

data class SweepingParticipantTO(
    val id: String,
    val name: String,
    val enabled: Boolean = true,
    val temporarilyUnavailable: Boolean = false,
)

enum class OpenFinanceConsentType {
    DATA, SWEEPING
}

enum class OpenFinanceConsentStatus {
    AWAITING_AUTHORISATION, PARTIALLY_ACCEPTED, AUTHORISED, REJECTED, REVOKED, CONSUMED, CANCELED
}

interface OpenFinanceConsentTO {
    val id: String
    val type: OpenFinanceConsentType
    val participantName: String
    val participantId: String
    val status: ConsentStatus
    val createdAt: String
    val bankAccount: OpenFinanceBankAccountTO?
}

data class OpenFinanceSweepingConsentTO(
    override val id: String,
    override val participantName: String,
    override val participantId: String,
    override val status: ConsentStatus,
    override val createdAt: String,
    override val bankAccount: OpenFinanceBankAccountTO?,
    val configuration: SweepingLimitsResponseTO,
) : OpenFinanceConsentTO {
    override val type = OpenFinanceConsentType.SWEEPING
}

data class OpenFinanceBankAccountDataConsentTO(
    override val id: String,
    override val participantName: String,
    override val participantId: String,
    override val status: ConsentStatus,
    override val createdAt: String,
    override val bankAccount: OpenFinanceBankAccountTO?,
) : OpenFinanceConsentTO {
    override val type = OpenFinanceConsentType.DATA
}

data class OpenFinanceBankAccountTO(
    val id: String,
    val ispb: String,
    val bankName: String,
    val routingNumber: String,
    val accountNumber: String,
)

@Deprecated("usar OpenFinanceSweepingConsentTO")
data class SweepingConsentTO(
    val sweepingAccountId: String,
    val participantName: String,
    val participantId: String,
    val status: ConsentStatus,
    val startDateTime: String?,
    val expirationDateTime: String?,
    val createdAt: String,
    val configuration: SweepingLimitsResponseTO,
    val debtor: SweepingDebtorTO?,
    // val bankRoutingNumber: String,
    // val bankAccountNumber: String,
    // val bankAccountId: String,
)

data class CreateDataConsentRequestTO(
    val participant: String,
)

data class CreateSweepingConsentRequestTO(
    val participant: String,
    val consentLimits: SweepingLimitsRequestTO,
)

data class CreateConsentResponseTO(
    val consentPage: String,
)

data class SweepingLimitsRequestTO(
    val dayTransactionLimit: Long,
    val monthTransactionLimit: Long,
    val yearTransactionLimit: Long,
    val dayQuantityLimit: Long,
) {
    fun toSweepingLimits() = SweepingLimits(
        totalAllowedAmount = this.yearTransactionLimit * 5,
        transactionLimit = this.dayTransactionLimit,
        periodicLimits = PeriodicLimits(
            day = PeriodicLimit(
                transactionLimit = this.dayTransactionLimit,
                quantityLimit = this.dayQuantityLimit,
            ),
            week = PeriodicLimit(
                transactionLimit = this.dayTransactionLimit * 7,
                quantityLimit = this.dayQuantityLimit * 7,
            ),
            month = PeriodicLimit(
                transactionLimit = this.monthTransactionLimit,
                quantityLimit = this.dayQuantityLimit * 30,
            ),
            year = PeriodicLimit(
                transactionLimit = this.yearTransactionLimit,
                quantityLimit = this.dayQuantityLimit * 365,
            ),
        ),
    )
}

data class SweepingLimitsResponseTO(
    val totalAllowedAmount: Long,
    val transactionLimit: Long,
    val dayTransactionLimit: Long,
    val weekTransactionLimit: Long,
    val monthTransactionLimit: Long,
    val yearTransactionLimit: Long,
    val dayQuantityLimit: Long,
)

data class SweepingDebtorTO(
    val bankName: String?,
    val issuer: String?,
    val number: String?,
)

fun OpenFinanceSweepingAccountError.handle(
    logger: Logger,
    markers: LogstashMarker,
    logName: String,
): HttpResponse<*> {
    markers.andAppend("errorType", this.javaClass.simpleName)
    markers.andAppend("errorMessage", this.message)
    val httpStatus =
        when (this) {
            is OpenFinanceSweepingAccountError.InvalidParticipant -> HttpStatus.PRECONDITION_FAILED
            is OpenFinanceSweepingAccountError.WalletNotFound -> HttpStatus.NOT_FOUND
            is OpenFinanceSweepingAccountError.PaymentMethodNotFound -> HttpStatus.NOT_FOUND
            is OpenFinanceSweepingAccountError.ConsentExpired -> HttpStatus.BAD_REQUEST
            is OpenFinanceSweepingAccountError.ConsentNotFound -> HttpStatus.NOT_FOUND
            is OpenFinanceSweepingAccountError.ConsentCreationError -> HttpStatus.INTERNAL_SERVER_ERROR
            is OpenFinanceSweepingAccountError.ConsentRevokeError -> HttpStatus.INTERNAL_SERVER_ERROR
        }

    if (httpStatus == HttpStatus.INTERNAL_SERVER_ERROR) {
        logger.error(markers.andAppend("httpStatus", httpStatus.code), logName)
    } else {
        logger.warn(markers.andAppend("httpStatus", httpStatus.code), logName)
    }
    return HttpResponse.status<Unit>(httpStatus).body(ResponseTO(httpStatus.code.toString(), httpStatus.name))
}

private fun SweepingLimits.toSweepingLimitsResponseTO() = SweepingLimitsResponseTO(
    totalAllowedAmount = this.totalAllowedAmount,
    transactionLimit = this.transactionLimit,
    dayTransactionLimit = this.periodicLimits.day.transactionLimit,
    dayQuantityLimit = this.periodicLimits.day.quantityLimit,
    weekTransactionLimit = this.periodicLimits.week.transactionLimit,
    monthTransactionLimit = this.periodicLimits.month.transactionLimit,
    yearTransactionLimit = this.periodicLimits.year.transactionLimit,
)

private fun SweepingDebtor.toSweepingDebtorTO(participantName: String?) = SweepingDebtorTO(
    issuer = this.routingNumber,
    bankName = this.bankName ?: participantName,
    number = this.accountNumber,
)

private fun OpenFinanceBankAccountData.toOpenFinanceBankAccountTO() = OpenFinanceBankAccountTO(
    id = id.value,
    bankName = bankName,
    ispb = ispb,
    routingNumber = routingNumber,
    accountNumber = accountNumber,
)