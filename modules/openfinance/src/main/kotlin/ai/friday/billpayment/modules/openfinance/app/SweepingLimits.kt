package ai.friday.billpayment.modules.openfinance.app

val defaultSweepingLimits = SweepingLimits(
    totalAllowedAmount = 800_000_00,
    transactionLimit = 5_000_00,
    periodicLimits = PeriodicLimits(
        day = PeriodicLimit(
            quantityLimit = 10,
            transactionLimit = 5_000_00,
        ),
        week = PeriodicLimit(
            quantityLimit = 50,
            transactionLimit = 35_000_00,
        ),
        month = PeriodicLimit(
            quantityLimit = 100,
            transactionLimit = 150_000_00,
        ),
        year = PeriodicLimit(
            quantityLimit = 300,
            transactionLimit = 400_000_00,
        ),
    ),
)

data class SweepingLimits(
    val totalAllowedAmount: Long,
    val transactionLimit: Long,
    val periodicLimits: PeriodicLimits,
)

data class PeriodicLimits(
    val day: PeriodicLimit,
    val week: PeriodicLimit,
    val month: PeriodicLimit,
    val year: PeriodicLimit,
)

data class PeriodicLimit(
    val quantityLimit: Long,
    val transactionLimit: Long,
)