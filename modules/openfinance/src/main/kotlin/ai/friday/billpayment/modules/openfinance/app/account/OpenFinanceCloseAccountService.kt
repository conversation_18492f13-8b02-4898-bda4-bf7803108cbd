package ai.friday.billpayment.modules.openfinance.app.account

import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountClosureDetails
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.CloseAccountError
import ai.friday.billpayment.app.account.CloseAccountStep
import ai.friday.billpayment.app.account.CloseAccountStepDiscovery
import ai.friday.billpayment.app.account.CloseAccountStepError
import ai.friday.billpayment.app.account.CloseAccountStepStatus
import ai.friday.billpayment.app.account.CloseAccountStepType
import ai.friday.billpayment.app.account.CloseWalletError
import ai.friday.billpayment.app.account.CloseWalletStep
import ai.friday.billpayment.app.account.CloseWalletStepDiscovery
import ai.friday.billpayment.app.account.CloseWalletStepError
import ai.friday.billpayment.app.account.CloseWalletStepExecutor
import ai.friday.billpayment.app.account.CloseWalletStepStatus
import ai.friday.billpayment.app.account.CloseWalletStepType
import ai.friday.billpayment.app.account.SimpleCloseAccountStep
import ai.friday.billpayment.app.account.SimpleCloseAccountStepExecutor
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.modules.openfinance.OpenFinance
import ai.friday.billpayment.modules.openfinance.app.ConsentService
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

val CloseAccountStepTypeOpenFinanceRevokeBankAccountDataConsents = CloseAccountStepType("OpenFinanceRevokeBankAccountDataConsents")
val CloseWalletStepTypeOpenFinanceRevokeSweepingConsents = CloseWalletStepType("OpenFinanceRevokeSweepingConsents")

@OpenFinance
class OpenFinanceCloseAccountStepDiscovery(
    private val consentService: ConsentService,
) : CloseAccountStepDiscovery, CloseWalletStepDiscovery {
    override val discoveryName: String = "OpenFinanceCloseAccountDiscovery"

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun prepareClose(accountId: AccountId, closureDetails: AccountClosureDetails): Either<CloseAccountError, List<CloseAccountStep>> {
        val logName = "OpenFinanceCloseAccountDiscovery#prepareClose"
        val markers = append("accountId", accountId.value)
            .andAppend("closureDetails", closureDetails)

        val bankAccountDataConsents = consentService.getActiveBankAccountDataConsents(accountId)

        logger.info(markers.andAppend("bankAccountDataConsents", bankAccountDataConsents.map { it.id }), logName)
        return if (bankAccountDataConsents.isEmpty()) {
            emptyList<CloseAccountStep>().right()
        } else {
            listOf(SimpleCloseAccountStep(CloseAccountStepTypeOpenFinanceRevokeBankAccountDataConsents, CloseAccountStepStatus.Pending)).right()
        }
    }

    override fun prepareCloseWallet(wallet: Wallet, closureDetails: AccountClosureDetails): Either<CloseWalletError, List<CloseWalletStep>> {
        val logName = "OpenFinanceCloseAccountDiscovery#prepareCloseWallet"
        val markers = append("wallet", wallet.id.value)
            .andAppend("closureDetails", closureDetails)

        val sweepingConsent = consentService.getSweepingConsent(wallet.id).getOrElse {
            null
        }

        logger.info(markers.andAppend("sweepingConsent", sweepingConsent), logName)
        return if (sweepingConsent == null) {
            emptyList<CloseWalletStep>().right()
        } else {
            listOf(CloseWalletStep(CloseWalletStepTypeOpenFinanceRevokeSweepingConsents, CloseWalletStepStatus.Pending)).right()
        }
    }
}

@OpenFinance
class OpenFinanceRevokeBankAccountDataConsentsStepExecutor(
    private val consentService: ConsentService,
) : SimpleCloseAccountStepExecutor(CloseAccountStepTypeOpenFinanceRevokeBankAccountDataConsents) {

    override fun doExecute(account: Account, step: SimpleCloseAccountStep, markers: LogstashMarker): Either<CloseAccountStepError, CloseAccountStepStatus> {
        val bankAccountDataConsents = consentService.getActiveBankAccountDataConsents(account.accountId)
        bankAccountDataConsents.forEach {
            consentService.revokeConsent(it.id, accountId = account.accountId).getOrElse { exception ->
                return CloseAccountStepError.GenericException(exception = exception).left()
            }
        }
        return CloseAccountStepStatus.Success.right()
    }
}

@OpenFinance
class OpenFinanceRevokeSweepingConsentsStepExecutor(
    private val consentService: ConsentService,
) : CloseWalletStepExecutor(CloseWalletStepTypeOpenFinanceRevokeSweepingConsents) {
    override fun doExecute(wallet: Wallet, markers: LogstashMarker): Either<CloseWalletStepError, CloseWalletStepStatus> {
        val sweepingConsent = consentService.getSweepingConsent(wallet.id).getOrElse {
            null
        }

        if (sweepingConsent != null) {
            consentService.revokeConsent(sweepingConsent.id, wallet.founder.accountId).getOrElse { error ->
                return CloseWalletStepError.GenericError(error = error.message).left()
            }
        }

        return CloseWalletStepStatus.Success.right()
    }
}