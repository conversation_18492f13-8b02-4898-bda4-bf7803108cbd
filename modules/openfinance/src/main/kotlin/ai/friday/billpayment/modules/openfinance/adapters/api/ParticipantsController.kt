package ai.friday.billpayment.modules.openfinance.adapters.api

import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.SweepingParticipant
import ai.friday.billpayment.modules.openfinance.OpenFinance
import ai.friday.billpayment.modules.openfinance.app.ConsentService
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.validation.Validated

@Validated
@Secured(Role.Code.OWNER)
@Controller("/openfinance")
@OpenFinance
@Version("2")
class ParticipantsController(
    private val consentService: ConsentService,
    private val accountRepository: AccountRepository,
) {

    @Get(
        uris = [
            "/sweepingaccount/participants",
            "/participants",
        ],
    )
    fun getParticipants(
        authentication: Authentication,
    ): HttpResponse<*> {
        val account = accountRepository.findById(authentication.toAccountId())

        val participants = consentService.getParticipants().map {
            it.toSweepingParticipantTO(account)
        }.sortedBy {
            it.name.lowercase()
        }

        return HttpResponse.ok(participants)
    }

    private fun SweepingParticipant.toSweepingParticipantTO(account: Account) = SweepingParticipantTO(
        id = id,
        name = name,
        enabled = enabledTo(account),
        temporarilyUnavailable = temporarilyUnavailable ?: false,
    )

    private fun SweepingParticipant.enabledTo(account: Account): Boolean {
        return with(restrictedTo) {
            isNullOrEmpty() || account.configuration.groups.any { it.value in this }
        }
    }
}