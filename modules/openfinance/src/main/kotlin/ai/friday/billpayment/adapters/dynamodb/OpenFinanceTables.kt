package ai.friday.billpayment.adapters.dynamodb

import io.micronaut.context.annotation.Property
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient

private const val DYNAMODB_OPEN_FINANCE_DATA_TABLE_NAME = "dynamodb.openFinanceDataTableName"

abstract class AbstractOpenFinanceDynamoDAO<T>(
    cli: DynamoDbEnhancedClient,
    type: Class<T>,
) : AbstractDynamoDAO<T>(
    cli = cli,
    type = type,
) {
    @field:Property(name = DYNAMODB_OPEN_FINANCE_DATA_TABLE_NAME)
    private var tName: String = OPEN_FINANCE_DATA_TABLE_NAME // Else legacy name

    override val tableName by lazy { tName }
}

abstract class AbstractOpenFinanceDynamoDAOAsync<T>(
    cli: <PERSON>DbEnhancedAsyncClient,
    type: Class<T>,
) : AbstractAsyncDynamoDAO<T>(
    cli = cli,
    type = type,
) {
    @field:Property(name = DYNAMODB_OPEN_FINANCE_DATA_TABLE_NAME)
    private var tName: String = OPEN_FINANCE_DATA_TABLE_NAME // Else legacy name

    override val tableName by lazy { tName }
}