package ai.friday.billpayment.modules.openfinance.adapters

import ai.friday.billpayment.adapters.dynamodb.AbstractOpenFinanceDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.GlobalSecondaryIndexNames
import ai.friday.billpayment.adapters.dynamodb.OPEN_FINANCE_DATA_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.OPEN_FINANCE_DATA_RANGE_KEY
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.modules.openfinance.OpenFinance
import ai.friday.billpayment.modules.openfinance.app.BankAccountNumberId
import ai.friday.billpayment.modules.openfinance.app.BankAccountResourceId
import ai.friday.billpayment.modules.openfinance.app.DataConsentId
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceBankAccount
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceBankAccountRepository
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceResourceType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class OpenFinanceBankAccountDynamoDAO(
    cli: DynamoDbEnhancedClient,
) : AbstractOpenFinanceDynamoDAO<OpenFinanceBankAccountEntity>(cli, OpenFinanceBankAccountEntity::class.java)

@OpenFinance
class OpenFinanceBankAccountDBRepository(
    private val client: OpenFinanceBankAccountDynamoDAO,
) : OpenFinanceBankAccountRepository {
    private val logger = LoggerFactory.getLogger(OpenFinanceBankAccountDBRepository::class.java)

    override fun save(bankAccount: OpenFinanceBankAccount) {
        val entity =
            OpenFinanceBankAccountEntity().apply {
                primaryKey = bankAccount.bankAccountResourceId.value
                scanKey = OpenFinanceResourceType.BANK_ACCOUNT.name + "#${bankAccount.userAccountId.value}"
                gSIndex1PartitionKey = bankAccount.userAccountId.value
                gSIndex2PartitionKey = bankAccount.dataConsentId?.value ?: "LEGACY_BANK_ACCOUNT"
                accountId = bankAccount.userAccountId.value
                bankAccountResourceId = bankAccount.bankAccountResourceId.value
                bankAccountNumberId = bankAccount.bankAccountNumberId?.value
                bankCode = bankAccount.bankCode
                resourceType = OpenFinanceResourceType.BANK_ACCOUNT
                createdAt = bankAccount.createdAt.format(dateTimeFormat)
                updatedAt = bankAccount.updatedAt.format(dateTimeFormat)

                data = bankAccount.data.orEmpty()
                dataConsentId = bankAccount.dataConsentId?.value
                brandName = bankAccount.brandName
                ispb = bankAccount.ispb
                routingNumber = bankAccount.routingNumber
                accountNumber = bankAccount.accountNumber
                accountDv = bankAccount.accountDv
                accountType = bankAccount.accountType
                accountSubType = bankAccount.accountSubType
                currency = bankAccount.currency
            }
        client.save(entity)
    }

    override fun findOrNull(
        bankAccountResourceId: BankAccountResourceId,
        accountId: AccountId,
    ): OpenFinanceBankAccount? = client.findByPrimaryKey(
        partitionKey = bankAccountResourceId.value,
        sortKey = OpenFinanceResourceType.BANK_ACCOUNT.name + "#${accountId.value}",
    )?.toDomain()

    override fun findByDataConsentId(dataConsentId: DataConsentId): List<OpenFinanceBankAccount> = client.findByPartitionKeyAndScanKeyOnIndex(
        index = GlobalSecondaryIndexNames.GSIndex2,
        partitionKey = dataConsentId.value,
        sortKey = OpenFinanceResourceType.BANK_ACCOUNT.name,
    ).map {
        it.toDomain()
    }

    private fun tryParseZonedDateTime(dateTime: String): ZonedDateTime = try {
        ZonedDateTime.parse(dateTime, dateTimeFormat)
    } catch (e: Exception) {
        val markers = Markers.append("dateTime", dateTime)
        logger.warn(markers, "OpenFinanceBankAccountDBRepository.tryParseZonedDateTime", e)
        tryParseDate(dateTime).atStartOfDay(brazilTimeZone)
    }

    private fun tryParseDate(date: String): LocalDate = try {
        LocalDate.parse(date, dateFormat)
    } catch (e: Exception) {
        val markers = Markers.append("date", date)
        logger.warn(markers, "OpenFinanceBankAccountDBRepository.tryParseDate", e)
        getLocalDate()
    }

    private fun OpenFinanceBankAccountEntity.toDomain() = OpenFinanceBankAccount(
        userAccountId = AccountId(accountId),
        bankAccountResourceId = BankAccountResourceId(bankAccountResourceId),
        bankAccountNumberId = bankAccountNumberId?.let { BankAccountNumberId(it) },
        bankCode = bankCode,
        createdAt = tryParseZonedDateTime(createdAt),
        updatedAt = tryParseZonedDateTime(updatedAt),
        data = data,
        dataConsentId = dataConsentId?.let { DataConsentId(it) },
        brandName = brandName,
        ispb = ispb,
        routingNumber = routingNumber,
        accountNumber = accountNumber,
        accountDv = accountDv,
        accountType = accountType,
        accountSubType = accountSubType,
        currency = currency,
    )
}

@DynamoDbBean
class OpenFinanceBankAccountEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_DATA_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_DATA_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1PartitionKey")
    lateinit var gSIndex1PartitionKey: String // accountId

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1RangeKey")
    var gSIndex1RangeKey: String = OpenFinanceResourceType.BANK_ACCOUNT.name

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2PartitionKey")
    lateinit var gSIndex2PartitionKey: String // dataConsentId

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2RangeKey")
    var gSIndex2RangeKey: String = OpenFinanceResourceType.BANK_ACCOUNT.name

    @get:DynamoDbAttribute(value = "AccountId")
    lateinit var accountId: String

    @get:DynamoDbAttribute(value = "ResourceId")
    lateinit var bankAccountResourceId: String

    @get:DynamoDbAttribute(value = "BankAccountNumberId")
    var bankAccountNumberId: String? = null

    @get:DynamoDbAttribute(value = "BankCompeCode")
    lateinit var bankCode: String

    @get:DynamoDbAttribute(value = "ResourceType")
    lateinit var resourceType: OpenFinanceResourceType

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute(value = "UpdatedAt")
    lateinit var updatedAt: String

    @get:DynamoDbAttribute(value = "EncryptedData")
    lateinit var data: String

    @get:DynamoDbAttribute(value = "DataConsentId")
    var dataConsentId: String? = null

    @get:DynamoDbAttribute(value = "BrandName")
    var brandName: String? = null

    @get:DynamoDbAttribute(value = "Ispb")
    var ispb: String? = null

    @get:DynamoDbAttribute(value = "RoutingNumber")
    var routingNumber: String? = null

    @get:DynamoDbAttribute(value = "AccountNumber")
    var accountNumber: String? = null

    @get:DynamoDbAttribute(value = "AccountDv")
    var accountDv: String? = null

    @get:DynamoDbAttribute(value = "AccountType")
    var accountType: String? = null

    @get:DynamoDbAttribute(value = "AccountSubType")
    var accountSubType: String? = null

    @get:DynamoDbAttribute(value = "Currency")
    var currency: String? = null
}