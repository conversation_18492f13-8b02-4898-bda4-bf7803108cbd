package ai.friday.billpayment.modules.openfinance.app

import ai.friday.billpayment.adapters.arbi.convertToLong
import ai.friday.billpayment.adapters.bill.DefaultBillEventPublisher
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.bill.RegisterUpdated
import ai.friday.billpayment.app.bill.UpdatedRegisterData
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BigDataService
import ai.friday.billpayment.app.integrations.KmsService
import ai.friday.billpayment.app.integrations.OpenFinanceConsentId
import ai.friday.billpayment.app.integrations.OpenFinanceParticipantId
import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.app.manualentry.ManualEntryStatus
import ai.friday.billpayment.app.manualentry.ManualEntryType
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.getDocumentType
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixKeyOwner
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.openfinance.OpenFinance
import ai.friday.billpayment.toEpochMillis
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.date.timeFormat
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.micronaut.context.annotation.Property
import java.math.BigInteger
import java.net.URL
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZonedDateTime
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import reactor.core.publisher.Flux

@OpenFinance
class OpenFinanceService(
    private val kmsService: KmsService,
    private val bankTransactionRepository: OpenFinanceBankTransactionRepository,
    private val bankAccountRepository: OpenFinanceBankAccountRepository,
    private val billEventPublisher: DefaultBillEventPublisher,
    private val bigDataService: BigDataService,
    private val manualEntryService: ManualEntryService,
    private val accountRepository: AccountRepository,
    @Property(name = "kms.openfinance.keyid") private val keyId: String,
) {
    fun process(transaction: OpenFinanceBankTransaction): Either<OpenFinanceProcessTransactionError, Unit> {
        val logName = "OpenFinanceService#process"
        val decryptedData = runBlocking { kmsService.decryptData(transaction.data, keyId) }
        val parsedData = getObjectMapper().readValue(decryptedData, BankTransaction::class.java)

        val markers = Markers.append("transaction", transaction.transactionId).andAppend("parsedData", parsedData)

        if (parsedData.document == null) {
            logger.warn(markers.andAppend("error", "Document is required"), logName)
            return OpenFinanceProcessTransactionError.DocumentNotFound.left()
        }

        val document = parsedData.document.value
        val documentType = getDocumentType(document)

        markers.andAppend("documentType", documentType)

        val name = if (documentType == "CPF") {
            bigDataService.getPersonName(document).getOrElse { return OpenFinanceProcessTransactionError.NameNotFound(it).left() }
        } else {
            bigDataService.getCompanyName(document).getOrElse { return OpenFinanceProcessTransactionError.NameNotFound(it).left() }
        }

        markers.andAppend("name", name)

        if (parsedData.creditDebitType == CreditDebitType.DEBITO) {
            val bankCode = bankAccountRepository.findOrNull(BankAccountResourceId(transaction.bankAccountId), AccountId(transaction.userAccountId))?.bankCode
            if (bankCode == null) {
                logger.warn(markers.andAppend("error", "bankAccountId not found"), logName)
                return OpenFinanceProcessTransactionError.BankAccountIdNotFound.left()
            }

            if (parsedData.type == TransactionType.CONVENIO_ARRECADACAO) {
                manualEntryService.create(
                    walletId = WalletId(transaction.userAccountId),
                    dueDate = LocalDateTime.parse(transaction.transactionDate, dateTimeFormat).toLocalDate(),
                    amount = convertToLong(parsedData.amount.amount),
                    type = ManualEntryType.EXTERNAL_PAYMENT,
                    status = ManualEntryStatus.PAID,
                    title = "Fatura $name",
                    categoryId = null,
                    source = ActionSource.OpenFinance(accountId = AccountId(transaction.userAccountId), bankNumber = bankCode.toLong()),
                    externalId = null,
                ).getOrElse {
                    logger.warn(markers.andAppend("error", "Error creating manual entry: $it"), logName)
                    return OpenFinanceProcessTransactionError.ManualEntryCreationError.left()
                }
            }

            if (parsedData.type == TransactionType.PIX) {
                val user = accountRepository.findById(AccountId(transaction.userAccountId))
                val paymentMethod = accountRepository.findAccountPaymentMethodsByAccountId(user.accountId).filter { it.method.type == PaymentMethodType.BALANCE }.first().method as InternalBankAccount
                val recipientBankAccount = parsedData.accountNumber + parsedData.accountDv

                if (document == user.document && recipientBankAccount == paymentMethod.buildFullAccountNumber()) {
                    logger.warn(markers.andAppend("error", "own friday account transaction"), logName)
                    return OpenFinanceProcessTransactionError.OwnAccountTransferError.left()
                }
                try {
                    createOpenFinancePix(transaction, parsedData, name, documentType, bankCode.toLong())
                } catch (e: Exception) {
                    logger.warn(markers.andAppend("error", "Error creating open finance pix: $e"), logName)
                    return OpenFinanceProcessTransactionError.OpenFinancePixCreationError.left()
                }
            }
        }
        logger.info(markers, logName)
        bankTransactionRepository.save(transaction)
        return Unit.right()
    }

    fun process(bankAccount: OpenFinanceBankAccount): Either<Exception, Unit> {
        val logName = "OpenFinanceService#process"
        val markers = Markers.append("bankAccount", bankAccount)

        bankAccountRepository.save(bankAccount)

        logger.info(markers, logName)
        return Unit.right()
    }

    fun findByResourceTypeAndDate(
        accountId: AccountId,
        type: OpenFinanceResourceType,
        from: LocalDate,
        to: LocalDate,
    ): Flux<OpenFinanceBankTransaction> =
        when (type) {
            OpenFinanceResourceType.BANK_TRANSACTION -> bankTransactionRepository.findByAccountIdAndDate(accountId, from, to)
            else -> Flux.empty()
        }

    private fun createOpenFinancePix(
        transaction: OpenFinanceBankTransaction,
        parsedData: BankTransaction,
        name: String,
        documentType: String,
        resourceName: Long,
    ) {
        val recipient = if (parsedData.bankCode == null || parsedData.agencyNumber == null || parsedData.accountNumber == null || parsedData.accountDv == null || parsedData.document == null) {
            Recipient(
                name = name,
                document = parsedData.document?.value,
                bankAccount = null,
                pixKeyDetails = PixKeyDetails(
                    key = PixKey(value = parsedData.document!!.value, type = PixKeyType.valueOf(documentType)),
                    holder = PixKeyHolder(
                        accountNo = BigInteger.ONE,
                        accountDv = "1",
                        ispb = "********",
                        institutionName = "N.D.",
                        accountType = AccountType.CHECKING,
                        routingNo = 1,
                    ),
                    owner = PixKeyOwner(name = name, document = parsedData.document.value),
                ),
            )
        } else {
            Recipient(
                name = name,
                document = parsedData.document.value,
                bankAccount = BankAccount(
                    accountType = AccountType.CHECKING,
                    bankNo = parsedData.bankCode.toLong(),
                    routingNo = parsedData.agencyNumber.toLong(),
                    accountNo = parsedData.accountNumber.toBigInteger(),
                    accountDv = parsedData.accountDv,
                    document = parsedData.document.value,
                    ispb = null,
                ),
            )
        }
        val createdAt = getZonedDateTime().toEpochMillis()
        val billAdded = BillAdded(
            billId = BillId(),
            created = createdAt,
            walletId = WalletId(transaction.userAccountId),
            description = "",
            dueDate = parsedData.transactionDate.toLocalDate(),
            amount = convertToLong(parsedData.amount.amount),
            recipient = recipient,
            contactId = null,
            billType = BillType.PIX,
            paymentLimitTime = LocalTime.parse("23:59", timeFormat),
            actionSource = ActionSource.OpenFinance(accountId = AccountId(transaction.userAccountId), resourceName),
            recurrenceRule = null,
            effectiveDueDate = parsedData.transactionDate.toLocalDate(),
            subscriptionFee = false,
            pixQrCodeData = null,
            categoryId = null,
        )
        val billAlreadyPaid = RegisterUpdated(
            billId = billAdded.billId,
            created = createdAt + 1,
            walletId = WalletId(transaction.userAccountId),
            updatedRegisterData = UpdatedRegisterData.AlreadyPaidBill(convertToLong(parsedData.amount.amount)),
            actionSource = ActionSource.OpenFinance(accountId = AccountId(transaction.userAccountId), resourceName),
        )
        billEventPublisher.storePastBill(
            billAdded,
            billAlreadyPaid,
        )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(OpenFinanceService::class.java)
    }
}

data class DataConsentId(override val value: String) : OpenFinanceConsentId

data class DataConsent(
    val dataConsentId: DataConsentId,
    val participantId: OpenFinanceParticipantId,
    val accountId: AccountId,
    val document: Document,
    val url: URL,
    val status: ConsentStatus,
    val createdAt: ZonedDateTime = getZonedDateTime(),
    val updatedAt: ZonedDateTime = getZonedDateTime(),
)

data class OpenFinanceBankAccount(
    val userAccountId: AccountId,
    val bankAccountResourceId: BankAccountResourceId,
    val bankCode: String,
    val data: String?, // FIXME remover quando popular os campos novos a partir desse no banco
    val createdAt: ZonedDateTime,
    val updatedAt: ZonedDateTime,

    val dataConsentId: DataConsentId?,

    val brandName: String?,
    val ispb: String?,

    val routingNumber: String?,
    val accountNumber: String?,
    val accountDv: String?,
    val accountType: String?,
    val accountSubType: String?,
    val currency: String?,
    val bankAccountNumberId: BankAccountNumberId? = null,
)

data class OpenFinanceBankTransaction(
    val userAccountId: String,
    val bankAccountId: String,
    val transactionId: String,
    val transactionDate: String,
    val data: String,
    val createdAt: String?,
    val updatedAt: String?,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as OpenFinanceBankTransaction

        if (userAccountId != other.userAccountId) return false
        if (bankAccountId != other.bankAccountId) return false
        if (transactionId != other.transactionId) return false
        if (transactionDate != other.transactionDate) return false
        if (!data.contentEquals(other.data)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = userAccountId.hashCode()
        result = 31 * result + bankAccountId.hashCode()
        result = 31 * result + transactionId.hashCode()
        result = 31 * result + transactionDate.hashCode()
        result = 31 * result + data.hashCode()
        return result
    }

    override fun toString(): String = "OpenFinanceBankTransaction(userAccountId='$userAccountId', bankAccountId='$bankAccountId', transactionId='$transactionId', transactionDate='$transactionDate'})"
}

enum class OpenFinanceResourceType {
    BANK_ACCOUNT,
    BANK_TRANSACTION,
    CREDIT_CARD,
    CREDIT_CARD_TRANSACTION,
    INVESTMENT_TRANSACTION,
    SWEEPING_ACCOUNT,
    SWEEPING_ACCOUNT_TRANSACTION, // FIXME remover
    SWEEPING_PAYMENT,
    DATA_CONSENT,
}

/**  TOS **/
data class BankTransaction(
    val transactionStatus: CompletedAuthorisedPaymentType,
    val bankAccountId: BankAccountId,
    val agencyNumber: String? = null,
    val transactionName: String,
    val transactionId: TransactionId,
    val document: DocumentOpenFinance? = null,
    val type: TransactionType,
    val transactionDate: ZonedDateTime,
    val bankCode: String? = null,
    val amount: BankAccountAmount,
    val personType: TransactionPersonType? = null,
    val creditDebitType: CreditDebitType,
    val accountNumber: String? = null,
    val accountDv: String? = null,
)

data class DocumentOpenFinance(
    val value: String,
    val type: String,
    val issuerRegion: String,
    val issuer: String,
)

data class BankAccountId(
    val value: String,
)

data class BankAccountResourceId(
    val value: String,
)

enum class BankAccountCurrency {
    BRL,
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class BankAccountAmount(
    val amount: String,
    val currency: BankAccountCurrency,
)

enum class CompletedAuthorisedPaymentType {
    TRANSACAO_EFETIVADA,
    LANCAMENTO_FUTURO,
    TRANSACAO_PROCESSANDO,
}

enum class CreditDebitType {
    CREDITO,
    DEBITO,
}

enum class TransactionType {
    TED,
    DOC,
    PIX,
    TRANSFERENCIA_MESMA_INSTITUICAO,
    BOLETO,
    CONVENIO_ARRECADACAO,
    PACOTE_TARIFA_SERVICOS,
    TARIFA_SERVICOS_AVULSOS,
    FOLHA_PAGAMENTO,
    DEPOSITO,
    SAQUE,
    CARTAO,
    ENCARGOS_JUROS_CHEQUE_ESPECIAL,
    RENDIMENTO_APLIC_FINANCEIRA,
    PORTABILIDADE_SALARIO,
    RESGATE_APLIC_FINANCEIRA,
    OPERACAO_CREDITO,
    OUTROS,
}

enum class TransactionPersonType {
    PESSOA_NATURAL,
    PESSOA_JURIDICA,
}