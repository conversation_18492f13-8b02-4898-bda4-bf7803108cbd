package ai.friday.billpayment.modules.openfinance.adapters

import ai.friday.billpayment.adapters.dynamodb.AbstractOpenFinanceDynamoDAOAsync
import ai.friday.billpayment.adapters.dynamodb.ConditionalType
import ai.friday.billpayment.adapters.dynamodb.GlobalSecondaryIndexNames
import ai.friday.billpayment.adapters.dynamodb.OPEN_FINANCE_DATA_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.OPEN_FINANCE_DATA_RANGE_KEY
import ai.friday.billpayment.app.integrations.EndToEnd
import ai.friday.billpayment.app.integrations.ExternalTransactionId
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.SweepingCashInId
import ai.friday.billpayment.app.integrations.SweepingCashInSource
import ai.friday.billpayment.app.integrations.SweepingCashInStatus
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.openfinance.OpenFinance
import ai.friday.billpayment.modules.openfinance.app.CreditorId
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceSweepingCashInRepository
import ai.friday.billpayment.modules.openfinance.app.SweepingConsentId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import jakarta.inject.Singleton
import java.time.Duration
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class OpenFinanceSweepingCashInDynamoDAO(
    cli: DynamoDbEnhancedAsyncClient,
) : AbstractOpenFinanceDynamoDAOAsync<OpenFinanceSweepingCashInEntity>(cli, OpenFinanceSweepingCashInEntity::class.java)

@OpenFinance
class OpenFinanceSweepingCashInDBRepository(
    private val client: OpenFinanceSweepingCashInDynamoDAO,
) : OpenFinanceSweepingCashInRepository {

    override fun save(sweepingCashIn: OpenFinanceSweepingCashIn) {
        val entity = OpenFinanceSweepingCashInEntity().apply {
            partitionKey = sweepingCashIn.id.value
            scanKey = "SWEEPING_CASH_IN"
            gSIndex1PartitionKey = sweepingCashIn.walletId.value
            gSIndex1RangeKey = "SWEEPING_CASH_IN#${sweepingCashIn.createdAt.format(dateTimeFormat)}"
            gSIndex2PartitionKey = sweepingCashIn.endToEnd?.value
            gSIndex2RangeKey = "SWEEPING_CASH_IN"
            walletId = sweepingCashIn.walletId.value
            description = sweepingCashIn.description
            consentId = sweepingCashIn.consentId.value
            status = sweepingCashIn.status
            amount = sweepingCashIn.amount
            creditorId = sweepingCashIn.creditorId.value
            requestId = sweepingCashIn.id.value
            requestSource = sweepingCashIn.requestSource
            approvalSource = sweepingCashIn.approvalSource
            externalTransactionId = sweepingCashIn.externalTransactionId?.value
            endToEnd = sweepingCashIn.endToEnd?.value
            error = sweepingCashIn.error
            errorDescription = sweepingCashIn.errorDescription
            createdAt = sweepingCashIn.createdAt.format(dateTimeFormat)
            updatedAt = getZonedDateTime().format(dateTimeFormat)
        }
        client.save(entity).block()
    }

    override fun findOrNull(sweepingCashInId: SweepingCashInId): OpenFinanceSweepingCashIn? {
        return client
            .listByPartitionKey(sweepingCashInId.value)
            .map {
                it.toDomain()
            }.blockFirst()
    }

    override fun findOrNull(endToEnd: EndToEnd): OpenFinanceSweepingCashIn? {
        return client
            .findByPrimaryKeyOnIndex(
                index = GlobalSecondaryIndexNames.GSIndex2,
                partitionKey = endToEnd.value,
                sortKey = "SWEEPING_CASH_IN",
                conditionalType = ConditionalType.EQUALS,
            )
            .map {
                it.toDomain()
            }.blockFirst()
    }

    override fun find(sweepingCashInId: SweepingCashInId): OpenFinanceSweepingCashIn {
        return findOrNull(sweepingCashInId) ?: throw ItemNotFoundException("Sweeping transaction not found")
    }

    override fun find(endToEnd: EndToEnd): OpenFinanceSweepingCashIn {
        return findOrNull(endToEnd) ?: throw ItemNotFoundException("Sweeping transaction not found")
    }

    override fun findByWalletId(walletId: WalletId, since: Duration): List<OpenFinanceSweepingCashIn> {
        val sinceDateTime = getZonedDateTime().minusSeconds(since.toSeconds())

        return client
            .findByPrimaryKeyOnIndex(
                index = GlobalSecondaryIndexNames.GSIndex1,
                partitionKey = walletId.value,
                sortKey = "SWEEPING_CASH_IN#${sinceDateTime.format(dateTimeFormat)}",
                conditionalType = ConditionalType.GREATER_THAN_OR_EQUAL_TO,
            ).collectList().block()
            ?.map {
                it.toDomain()
            } ?: emptyList()
    }

    private fun OpenFinanceSweepingCashInEntity.toDomain(): OpenFinanceSweepingCashIn {
        val defaultSource = if (description.contains("chatbot", ignoreCase = true)) {
            SweepingCashInSource.CHATBOT
        } else {
            SweepingCashInSource.WEB_APP
        }

        return OpenFinanceSweepingCashIn(
            id = SweepingCashInId(requestId),
            walletId = WalletId(walletId),
            description = description,
            consentId = SweepingConsentId(consentId),
            status = status,
            amount = amount,
            creditorId = CreditorId(creditorId),
            error = error,
            errorDescription = errorDescription,
            createdAt = ZonedDateTime.parse(createdAt, dateTimeFormat),
            updatedAt = ZonedDateTime.parse(updatedAt, dateTimeFormat),
            endToEnd = endToEnd?.let { EndToEnd(it) },
            requestSource = requestSource ?: defaultSource,
            approvalSource = approvalSource ?: defaultSource,
            externalTransactionId = externalTransactionId?.let { ExternalTransactionId(it) },
        )
    }
}

@DynamoDbBean
class OpenFinanceSweepingCashInEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_DATA_PARTITION_KEY)
    lateinit var partitionKey: String // requestId

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_DATA_RANGE_KEY)
    lateinit var scanKey: String // SWEEPING_CASH_IN

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1PartitionKey")
    lateinit var gSIndex1PartitionKey: String // accountId

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1RangeKey")
    lateinit var gSIndex1RangeKey: String // SWEEPING_CASH_IN#createdAt

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2PartitionKey")
    var gSIndex2PartitionKey: String? = null // endToEnd

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2RangeKey")
    var gSIndex2RangeKey: String? = null // SWEEPING_CASH_IN

    @get:DynamoDbAttribute(value = "RequestId")
    lateinit var requestId: String

    @get:DynamoDbAttribute(value = "AccountId") // FIXME - acertar nome no dynamo
    lateinit var walletId: String

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute(value = "UpdatedAt")
    lateinit var updatedAt: String

    @get:DynamoDbAttribute(value = "Description")
    lateinit var description: String

    @get:DynamoDbAttribute(value = "ConsentId")
    lateinit var consentId: String

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: SweepingCashInStatus

    @get:DynamoDbAttribute(value = "Amount")
    var amount: Long = 0

    @get:DynamoDbAttribute(value = "CreditorId")
    lateinit var creditorId: String

    @get:DynamoDbAttribute(value = "Error")
    var error: String? = null

    @get:DynamoDbAttribute(value = "ErrorDescription")
    var errorDescription: String? = null

    @get:DynamoDbAttribute(value = "EndToEnd")
    var endToEnd: String? = null

    @get:DynamoDbAttribute(value = "RequestSource")
    var requestSource: SweepingCashInSource? = null

    @get:DynamoDbAttribute(value = "ApprovalSource")
    var approvalSource: SweepingCashInSource? = null

    @get:DynamoDbAttribute(value = "ExternalTransactionId")
    var externalTransactionId: String? = null
}