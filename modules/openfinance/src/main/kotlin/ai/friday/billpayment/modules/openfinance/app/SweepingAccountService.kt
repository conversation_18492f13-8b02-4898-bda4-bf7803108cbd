package ai.friday.billpayment.modules.openfinance.app

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.fingerprint.DeviceType
import ai.friday.billpayment.app.fingerprint.RegisteredDevice
import ai.friday.billpayment.app.integrations.ConfirmSweepingCashInError
import ai.friday.billpayment.app.integrations.EndToEnd
import ai.friday.billpayment.app.integrations.GetSweepingConsentLimitError
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.LimitType
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationService
import ai.friday.billpayment.app.integrations.OFSweepingCashIn
import ai.friday.billpayment.app.integrations.OFSweepingConsentPeriodicUsage
import ai.friday.billpayment.app.integrations.OpenFinanceParticipantId
import ai.friday.billpayment.app.integrations.RequestSweepingCashInCommand
import ai.friday.billpayment.app.integrations.RequestSweepingCashInError
import ai.friday.billpayment.app.integrations.SweepingAccountServiceInterface
import ai.friday.billpayment.app.integrations.SweepingCashInId
import ai.friday.billpayment.app.integrations.SweepingCashInSource
import ai.friday.billpayment.app.integrations.SweepingCashInStatus
import ai.friday.billpayment.app.integrations.SweepingParticipant
import ai.friday.billpayment.app.notification.ButtonDeeplinkParameter
import ai.friday.billpayment.app.notification.ButtonParameter
import ai.friday.billpayment.app.notification.NotificationTemplate
import ai.friday.billpayment.app.notification.NotificationType
import ai.friday.billpayment.app.notification.TemplatesConfiguration
import ai.friday.billpayment.app.notification.WhatsappNotification
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.modules.chatbotai.app.ChatbotAiTransactionPaymentStatus
import ai.friday.billpayment.modules.chatbotai.app.ChatbotAiTransactionService
import ai.friday.billpayment.modules.openfinance.OpenFinance
import ai.friday.billpayment.modules.openfinance.adapters.OpenFinanceSweepingCashIn
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonInclude
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Primary
import io.micronaut.context.annotation.Property
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Duration
import java.time.LocalDate
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Primary
@OpenFinance
open class SweepingAccountService(
    private val accountService: AccountService,
    private val sweepingAccountRepository: OpenFinanceSweepingAccountRepository,
    private val sweepingCashInRepository: OpenFinanceSweepingCashInRepository,
    private val openFinanceAdapter: OpenFinanceAdapter,
    private val participants: List<SweepingParticipant>,
    private val notificationService: NotificationService,
    private val templatesConfiguration: TemplatesConfiguration,
    private val walletService: WalletService,
    private val deviceFingerprintService: DeviceFingerprintService,
    private val messagePublisher: MessagePublisher,
    private val chatbotAiTransactionService: ChatbotAiTransactionService,
    @Property(name = "friday.morning.messaging.consumer.check-sweeping-cash-in-status.queueName") private val checkSweepingCashInStatusQueueName: String,
) : SweepingAccountServiceInterface {

    init {
        logger.info(append("participants", participants), "SweepingAccountService#init")
    }

    fun getSweepingAccount(consentId: SweepingConsentId): Either<OpenFinanceSweepingAccountError, SweepingConsent> {
        return try {
            val consent = sweepingAccountRepository.findBySweepingAccountId(consentId)
            if (!consent.status.isActive()) {
                OpenFinanceSweepingAccountError.ConsentExpired.left()
            } else {
                consent.right()
            }
        } catch (e: ItemNotFoundException) {
            OpenFinanceSweepingAccountError.ConsentNotFound.left()
        }
    }

    private fun RequestSweepingCashInCommand.toSweepingCashIn(consent: SweepingConsent) = OpenFinanceSweepingCashIn(
        id = sweepingCashInId,
        walletId = walletId,
        consentId = consent.id,
        creditorId = consent.creditorId,
        amount = amount,
        description = description,
        status = SweepingCashInStatus.CREATED,
        endToEnd = null,
        requestSource = requestSource,
        approvalSource = approvalSource,
        externalTransactionId = externalTransactionId,
    )

    private fun getActiveConsent(command: RequestSweepingCashInCommand): Either<GetActiveConsentError, SweepingConsent> {
        val consentId = command.consentId

        val consent = if (consentId !== null) {
            sweepingAccountRepository.findBySweepingAccountId(SweepingConsentId(consentId))
        } else {
            sweepingAccountRepository.findByWalletId(command.walletId).firstOrNull {
                it.status == ConsentStatus.AUTHORISED && (command.participantId == null || command.participantId == it.participantId)
            }
        }

        return consent?.right() ?: GetActiveConsentError.ActiveConsentNotFound(command.walletId).left()
    }

    private fun RequestSweepingCashInCommand.matches(sweepingCashIn: OpenFinanceSweepingCashIn): Boolean {
        return walletId == sweepingCashIn.walletId &&
            amount == sweepingCashIn.amount &&
            description == sweepingCashIn.description &&
            requestSource == sweepingCashIn.requestSource &&
            approvalSource == sweepingCashIn.approvalSource &&
            externalTransactionId == sweepingCashIn.externalTransactionId
    }

    override fun requestSweepingCashIn(command: RequestSweepingCashInCommand): Either<RequestSweepingCashInError, EndToEnd?> {
        val logName = "SweepingAccountService#requestSweepingCashIn"
        val markers = append("command", command)

        val existingSweepingCashIn = sweepingCashInRepository.findOrNull(command.sweepingCashInId)
        markers.andAppend("existingSweepingCashIn", existingSweepingCashIn)

        return if (existingSweepingCashIn != null) {
            if (command.matches(existingSweepingCashIn)) {
                existingSweepingCashIn.endToEnd.right()
            } else {
                RequestSweepingCashInError.Conflict.left()
            }
        } else {
            val wallet = walletService.findWallet(command.walletId)

            val consent = getActiveConsent(command).getOrElse {
                markers.andAppend("error", it)
                logger.warn(markers, logName)
                return when (it) {
                    is GetActiveConsentError.ActiveConsentNotFound -> RequestSweepingCashInError.ActiveConsentNotFound(it.walletId).left()
                }
            }

            val sweepingCashIn = command.toSweepingCashIn(consent)
            markers.andAppend("sweepingCashIn", sweepingCashIn)

            sweepingCashInRepository.save(sweepingCashIn)

            val registeredDevice = deviceFingerprintService.getOrNull(wallet.founder.accountId)

            openFinanceAdapter.requestSweepingCashIn(
                sweepingCashInId = command.sweepingCashInId,
                consentId = consent.id,
                amount = command.amount,
                description = command.description,
                creditorId = consent.creditorId,
                riskSignals = registeredDevice?.toManualSweepingRiskSignals(consent.createdAt.toLocalDate()),
            ).onLeft {
                sweepingCashInRepository.save(
                    sweepingCashIn.copy(
                        status = it.toSweepingCashInStatus(),
                        error = it.toErrorCode(),
                        errorDescription = it.toErrorDescription(),
                    ),
                )

                if (it.shouldCheckStatus()) {
                    messagePublisher.sendMessage(checkSweepingCashInStatusQueueName, CheckSweepingCashInStatusMessage(sweepingCashIn.id.value))
                }
            }.onRight { endToEnd ->
                endToEnd?.let {
                    sweepingCashInRepository.save(
                        sweepingCashIn.copy(
                            endToEnd = it,
                        ),
                    )
                }
            }
        }.also {
            it.onRight {
                markers.andAppend("endToEnd", it)
                logger.info(markers, logName)
            }.onLeft { error ->
                markers.andAppend("error", error)
                logger.error(markers, logName)
            }
        }
    }

    fun getSweepingConsentPeriodicUsage(sweepingConsentId: SweepingConsentId): Either<GetSweepingConsentLimitError, OFSweepingConsentPeriodicUsage> {
        return openFinanceAdapter.getSweepingConsentPeriodicUsage(sweepingConsentId).mapLeft {
            GetSweepingConsentLimitError.GenericError(it.toString())
        }
    }

    fun getSweepingCashIn(sweepingCashInId: SweepingCashInId): Either<GetSweepingCashInError, OpenFinanceSweepingCashIn> {
        return try {
            val payment = sweepingCashInRepository.find(sweepingCashInId)

            if (payment.status.finalExternalStatus) {
                payment.right()
            } else {
                val response = openFinanceAdapter.getSweepingCashInStatus(sweepingCashInId).getOrElse {
                    return when (it) {
                        is GetSweepingCashInStatusError.GenericError -> GetSweepingCashInError.GenericError(it.message).left()
                    }
                }

                return updateSweepingCashIn(
                    sweepingCashInId = payment.id,
                    status = response.status,
                    error = response.error,
                    errorDescription = response.errorDescription,
                ).mapLeft {
                    when (it) {
                        is UpdateSweepingCashInError.ItemNotFound -> GetSweepingCashInError.ItemNotFound(it.sweepingCashInId)
                        is UpdateSweepingCashInError.GenericError -> GetSweepingCashInError.GenericError(it.message)
                    }
                }
            }
        } catch (e: ItemNotFoundException) {
            GetSweepingCashInError.ItemNotFound(sweepingCashInId).left()
        }
    }

    override fun confirmSweepingCashIn(endToEnd: EndToEnd): Either<ConfirmSweepingCashInError, SweepingCashInId> {
        val logName = "SweepingAccountService#confirmSweepingCashIn"
        val markers = append("endToEnd", endToEnd.value)

        val sweepingCashIn = sweepingCashInRepository.findOrNull(endToEnd)

        return if (sweepingCashIn == null) {
            ConfirmSweepingCashInError.ItemNotFound(endToEnd).left()
        } else {
            updateSweepingCashIn(sweepingCashIn.id, SweepingCashInStatus.SUCCESS, null, null).map {
                it.id.right()
            }.getOrElse {
                when (it) {
                    is UpdateSweepingCashInError.ItemNotFound -> ConfirmSweepingCashInError.ItemNotFound(endToEnd).left()
                    is UpdateSweepingCashInError.GenericError -> ConfirmSweepingCashInError.GenericError(it.message).left()
                }
            }
        }.also {
            it.onRight {
                logger.info(markers, logName)
            }.onLeft {
                logger.error(markers, logName)
            }
        }
    }

    fun updateSweepingCashIn(sweepingCashInId: SweepingCashInId, status: SweepingCashInStatus, error: String?, errorDescription: String?): Either<UpdateSweepingCashInError, OpenFinanceSweepingCashIn> {
        val logName = "SweepingAccountService#updateSweepingCashIn"
        val markers = append("sweepingCashInRequestId", sweepingCashInId.value)
            .andAppend("status", status)
            .andAppend("error", error)
            .andAppend("errorDescription", errorDescription)

        val payment = sweepingCashInRepository.findOrNull(sweepingCashInId)
        markers.andAppend("payment", payment)

        if (payment == null) {
            logger.warn(markers, logName)
            return UpdateSweepingCashInError.ItemNotFound(sweepingCashInId).left()
        }

        return if (payment.status.order >= status.order) {
            payment.right()
        } else {
            doUpdateSweepingCashIn(payment, status, error, errorDescription)
        }.also {
            it.onLeft { updateError ->
                logger.info(markers.andAppend("error", updateError), logName)
            }.onRight {
                logger.info(markers, logName)
            }
        }
    }

    private fun SweepingCashInStatus.toChatbotTransactionPaymentStatus() = when (this) {
        SweepingCashInStatus.CREATED -> null
        SweepingCashInStatus.UNKNOWN -> null
        SweepingCashInStatus.WAITING_SETTLEMENT -> null
        SweepingCashInStatus.FAILED -> ChatbotAiTransactionPaymentStatus.FAILED
        SweepingCashInStatus.SUCCESS -> ChatbotAiTransactionPaymentStatus.SUCCESS
    }

    private fun doUpdateSweepingCashIn(
        sweepingCashIn: OpenFinanceSweepingCashIn,
        status: SweepingCashInStatus,
        error: String?,
        errorDescription: String?,
    ): Either<UpdateSweepingCashInError, OpenFinanceSweepingCashIn> {
        val logName = "SweepingAccountService#doUpdateSweepingCashIn"
        val markers = append("sweepingCashInRequestId", sweepingCashIn.id.value)
            .andAppend("status", status)
            .andAppend("error", error)
            .andAppend("errorDescription", errorDescription)
            .andAppend("requestSource", sweepingCashIn.requestSource)
            .andAppend("externalTransactionId", sweepingCashIn.externalTransactionId?.value)

        val updatedCashIn = sweepingCashIn.copy(
            status = status,
            error = error,
            errorDescription = errorDescription,
        )

        val accountHelper = WalletIdToAccountHelper(
            walletId = sweepingCashIn.walletId,
            accountService = accountService,
            walletService = walletService,
        )

        if (sweepingCashIn.requestSource == SweepingCashInSource.CHATBOT) {
            val account = accountHelper.findAccount()

            status.toChatbotTransactionPaymentStatus()?.let {
                chatbotAiTransactionService.updatePaymentStatus(
                    transactionId = sweepingCashIn.externalTransactionId!!.value,
                    userId = account.chatbotUserId(),
                    paymentStatus = it,
                ).onLeft { updateError ->
                    logger.error(markers.andAppend("error", updateError), logName)
                    return UpdateSweepingCashInError.GenericError(updateError.toString()).left()
                }
            }
        }

        if (updatedCashIn.status == SweepingCashInStatus.SUCCESS) {
            val consent = sweepingAccountRepository.findBySweepingAccountId(sweepingCashIn.consentId)

            sweepingAccountRepository.save(consent.copy(lastSuccessfulCashIn = getZonedDateTime()))
        }

        if (updatedCashIn.shouldNotifyError()) {
            val account = accountHelper.findAccount()

            val consent = sweepingAccountRepository.findBySweepingAccountId(sweepingCashIn.consentId)

            val bankName = consent.participantId.toName("seu banco")
            markers.andAppend("bankName", bankName)

            notificationService.notifyAccount(
                account = account,
                type = NotificationType.SWEEPING_TRANSACTION_FAILED,
            ) {
                updatedCashIn.buildTransactionFailedNotification(account, consent.participantId, bankName)
            }
        }

        sweepingCashInRepository.save(updatedCashIn)

        logger.info(markers, logName)
        return updatedCashIn.right()
    }

    private fun OpenFinanceSweepingCashIn.shouldNotifyError() = when (status) {
        SweepingCashInStatus.FAILED -> true
        SweepingCashInStatus.CREATED -> false
        SweepingCashInStatus.UNKNOWN -> false
        SweepingCashInStatus.WAITING_SETTLEMENT -> false
        SweepingCashInStatus.SUCCESS -> false
    }

    private fun OpenFinanceSweepingCashIn.buildTransactionFailedNotification(account: Account, participantId: OpenFinanceParticipantId, bankName: String): WhatsappNotification {
        fun buildNotification(template: String, configurationKey: String, buttonParameter: ButtonParameter? = null, quickReplyButtonsWhatsAppParameter: List<BlipPayload> = emptyList(), bankNameCount: Int) = WhatsappNotification(
            accountId = account.accountId,
            receiver = MobilePhone(account.mobilePhone),
            template = NotificationTemplate(template),
            configurationKey = configurationKey,
            buttonWhatsAppParameter = buttonParameter,
            quickReplyButtonsWhatsAppParameter = quickReplyButtonsWhatsAppParameter.map { it.toJsonString() },
            parameters = buildList {
                repeat(bankNameCount) {
                    add(bankName)
                }
            },
        )

        val buttonRefazerConsentimento = ButtonDeeplinkParameter("open-finance")

        return when (requestSource) {
            SweepingCashInSource.CHATBOT -> {
                val buttonTentarNovamente = BlipPayload(
                    action = "TRANSACTION_RETRY",
                    transactionId = externalTransactionId!!.value,
                )
                val buttonPrefiroPagarComUmPix = BlipPayload(
                    action = "SEND_PIX_CODE",
                    transactionId = externalTransactionId.value,
                )
                when (error) {
                    "VALOR_ACIMA_LIMITE" -> buildNotification(
                        template = templatesConfiguration.whatsappTemplates.sweepingTransferChatbotParticipantLimitError,
                        configurationKey = "sweeping-transfer-chatbot-participant-limit-error",
                        quickReplyButtonsWhatsAppParameter = listOf(
                            buttonPrefiroPagarComUmPix,
                        ),
                        bankNameCount = 3,
                    )
                    "SALDO_INSUFICIENTE" -> buildNotification(
                        template = templatesConfiguration.whatsappTemplates.sweepingTransferChatbotParticipantBalanceError,
                        configurationKey = "sweeping-transfer-chatbot-participant-balance-error",
                        quickReplyButtonsWhatsAppParameter = listOf(
                            buttonTentarNovamente,
                            buttonPrefiroPagarComUmPix,
                        ),
                        bankNameCount = 3,
                    )
                    "LIMITE_PERIODO_VALOR_EXCEDIDO", "LIMITE_PERIODO_QUANTIDADE_EXCEDIDO" -> {
                        if (errorDescription?.contains("Consentimento invalido", true) == true) {
                            buildNotification(
                                template = templatesConfiguration.whatsappTemplates.sweepingTransferChatbotInvalidConsentError,
                                configurationKey = "sweeping-transfer-chatbot-invalid-consent-error",
                                buttonParameter = buttonRefazerConsentimento,
                                quickReplyButtonsWhatsAppParameter = listOf(
                                    buttonPrefiroPagarComUmPix,
                                ),
                                bankNameCount = 2,
                            )
                        } else {
                            buildNotification(
                                template = templatesConfiguration.whatsappTemplates.sweepingTransferChatbotRetryableError,
                                configurationKey = "sweeping-transfer-chatbot-retryable-error",
                                quickReplyButtonsWhatsAppParameter = listOf(
                                    buttonTentarNovamente,
                                    buttonPrefiroPagarComUmPix,
                                ),
                                bankNameCount = 2,
                            )
                        }
                    }
                    else -> buildNotification(
                        template = templatesConfiguration.whatsappTemplates.sweepingTransferChatbotRetryableError,
                        configurationKey = "sweeping-transfer-chatbot-retryable-error",
                        quickReplyButtonsWhatsAppParameter = listOf(
                            buttonTentarNovamente,
                            buttonPrefiroPagarComUmPix,
                        ),
                        bankNameCount = 2,
                    )
                }
            }
            SweepingCashInSource.WEB_APP -> {
                val buttonTentarNovamente = BlipPayload(
                    action = "RETRY_SWEEPING",
                    amount = amount.toString(),
                    transactionId = participantId.value, // FIXME nao eh transactionId, criar atributo novo quando sair da Blip
                )
                when (error) {
                    "VALOR_ACIMA_LIMITE" -> buildNotification(
                        template = templatesConfiguration.whatsappTemplates.sweepingTransferWebappParticipantLimitError,
                        configurationKey = "sweeping-transfer-webapp-participant-limit-error",
                        bankNameCount = 3,
                    )
                    "SALDO_INSUFICIENTE" -> buildNotification(
                        template = templatesConfiguration.whatsappTemplates.sweepingTransferWebappParticipantBalanceError,
                        configurationKey = "sweeping-transfer-webapp-participant-balance-error",
                        quickReplyButtonsWhatsAppParameter = listOf(
                            buttonTentarNovamente,
                        ),
                        bankNameCount = 3,
                    )
                    "LIMITE_PERIODO_VALOR_EXCEDIDO", "LIMITE_PERIODO_QUANTIDADE_EXCEDIDO" -> {
                        if (errorDescription?.contains("Consentimento invalido", true) == true) {
                            buildNotification(
                                template = templatesConfiguration.whatsappTemplates.sweepingTransferWebappInvalidConsentError,
                                configurationKey = "sweeping-transfer-webapp-invalid-consent-error",
                                buttonParameter = buttonRefazerConsentimento,
                                bankNameCount = 2,
                            )
                        } else {
                            buildNotification(
                                template = templatesConfiguration.whatsappTemplates.sweepingTransferWebappRetryableError,
                                configurationKey = "sweeping-transfer-webapp-retryable-error",
                                quickReplyButtonsWhatsAppParameter = listOf(
                                    buttonTentarNovamente,
                                ),
                                bankNameCount = 2,
                            )
                        }
                    }
                    else -> buildNotification(
                        template = templatesConfiguration.whatsappTemplates.sweepingTransferWebappRetryableError,
                        configurationKey = "sweeping-transfer-webapp-retryable-error",
                        quickReplyButtonsWhatsAppParameter = listOf(
                            buttonTentarNovamente,
                        ),
                        bankNameCount = 2,
                    )
                }
            }
        }
    }

    private fun OpenFinanceParticipantId.toName(defaultValue: String): String {
        return participants.firstOrNull {
            it.id == value
        }?.name ?: defaultValue
    }

    private fun findLastActiveCashIns(walletId: WalletId): List<OpenFinanceSweepingCashIn> {
        val lastCashIns = sweepingCashInRepository.findByWalletId(walletId, Duration.ofDays(1))

        val hasPendingCashIn = lastCashIns.any { !it.status.finalExternalStatus }

        return lastCashIns.filter {
            if (hasPendingCashIn) {
                !it.status.finalExternalStatus
            } else {
                it.createdAt.plusMinutes(30).isAfter(getZonedDateTime())
            }
        }
    }

    private fun List<OpenFinanceSweepingCashIn>.toParticipantsInUse() = map {
        it.consentId
    }.toSet().associateWith { sweepingAccountId ->
        participants.firstOrNull {
            it.id == sweepingAccountRepository.findBySweepingAccountId(sweepingAccountId).participantId.value
        }
    }

    private fun Map<SweepingConsentId, SweepingParticipant?>.checkMissingParticipants(markers: LogstashMarker, logName: String) {
        val invalidConsentIds = filter {
            it.value == null
        }.map {
            it.key
        }

        if (invalidConsentIds.isNotEmpty()) {
            markers.andAppend("ACTION", "VERIFY")
                .andAppend("invalidConsentIds", invalidConsentIds)
            logger.error(markers, logName)
        } else {
            logger.info(markers, logName)
        }
    }

    @Trace
    override fun getLastActiveCashIns(walletId: WalletId): List<OFSweepingCashIn> {
        val logName = "SweepingAccountService#getLastCashIns"
        val markers = append("walletId", walletId.value)

        val lastCashIns = findLastActiveCashIns(walletId)
        markers.andAppend("lastCashIns", lastCashIns)

        val participantsInUse = lastCashIns.toParticipantsInUse()

        participantsInUse.checkMissingParticipants(markers, logName)

        return lastCashIns.mapNotNull { cashIn ->
            participantsInUse[cashIn.consentId]?.let {
                OFSweepingCashIn(
                    id = cashIn.id,
                    participant = it,
                    amount = cashIn.amount,
                    status = cashIn.status,
                    updatedAt = cashIn.updatedAt,
                )
            }
        }
    }

    private fun RequestSweepingCashInError.shouldCheckStatus() = when (this) {
        is RequestSweepingCashInError.ActiveConsentNotFound -> false
        is RequestSweepingCashInError.Conflict -> false
        is RequestSweepingCashInError.ItemNotFound -> false
        is RequestSweepingCashInError.LimitError -> false
        is RequestSweepingCashInError.PaymentNotCreated -> false
        is RequestSweepingCashInError.GenericError -> true
    }

    private data class WalletIdToAccountHelper(
        val walletId: WalletId,
        private val walletService: WalletService,
        private val accountService: AccountService,
    ) {
        lateinit var account: Account

        fun findAccount(): Account {
            if (!this::account.isInitialized) {
                val wallet = walletService.findWallet(walletId)

                account = accountService.findAccountById(wallet.founder.accountId)
            }

            return account
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SweepingAccountService::class.java)
    }
}

private fun RegisteredDevice.toManualSweepingRiskSignals(accountTenure: LocalDate) = ManualSweepingRiskSignals(
    deviceId = details.uuid.toString(),
    isRootedDevice = details.rooted,
    screenBrightness = when (details.type) {
        DeviceType.ANDROID -> (Math.random() * 100).toInt() + 150 // 0 - 255
        DeviceType.IOS -> BigDecimal((Math.random() / 2) + 0.35).setScale(2, RoundingMode.HALF_EVEN).toDouble() // 0.0 - 1.0
    },
    elapsedTimeSinceBoot = Duration.ofDays(7).plusMinutes((Math.random() * 10000).toLong()).plusMillis((Math.random() * 60000).toLong()),
    osVersion = details.type.name,
    userTimeZoneOffset = "-03", // FIXME precisa receber essa info do nativo
    language = "pt", // FIXME precisa receber essa info do nativo
    screenDimensions = ScreenDimensions(
        width = details.screenResolution.width,
        height = details.screenResolution.height,
    ),
    accountTenure = accountTenure,
)

fun RequestSweepingCashInError.toErrorCode() = when (this) {
    is RequestSweepingCashInError.PaymentNotCreated -> "4100"
    is RequestSweepingCashInError.Conflict -> "4009"
    is RequestSweepingCashInError.GenericError -> "4001"
    is RequestSweepingCashInError.ItemNotFound -> "4001"
    is RequestSweepingCashInError.LimitError -> {
        when (limitType) {
            LimitType.DAILY -> "4002"
            LimitType.WEEKLY -> "4003"
            LimitType.MONTHLY -> "4004"
            LimitType.YEARLY -> "4005"
            LimitType.GLOBAL -> "4006"
            LimitType.TRANSACTION -> "4007"
            LimitType.UNKNOWN -> "4008"
        }
    }
    is RequestSweepingCashInError.ActiveConsentNotFound -> "4001"
}

fun RequestSweepingCashInError.isWarning() = when (this) {
    is RequestSweepingCashInError.Conflict -> false
    is RequestSweepingCashInError.GenericError -> false
    is RequestSweepingCashInError.ItemNotFound -> false
    is RequestSweepingCashInError.PaymentNotCreated -> true
    is RequestSweepingCashInError.LimitError -> true
    is RequestSweepingCashInError.ActiveConsentNotFound -> false
}

fun RequestSweepingCashInError.toErrorDescription() = when (this) {
    is RequestSweepingCashInError.Conflict -> "Conflict"
    is RequestSweepingCashInError.GenericError -> "GenericError"
    is RequestSweepingCashInError.ItemNotFound -> "ItemNotFound"
    is RequestSweepingCashInError.PaymentNotCreated -> "PaymentNotCreated"
    is RequestSweepingCashInError.LimitError -> "LimitExceeded: $limitType"
    is RequestSweepingCashInError.ActiveConsentNotFound -> "ActiveConsentNotFound"
}

fun RequestSweepingCashInError.toSweepingCashInStatus() = when (this) {
    is RequestSweepingCashInError.Conflict -> SweepingCashInStatus.FAILED
    is RequestSweepingCashInError.GenericError -> SweepingCashInStatus.UNKNOWN
    is RequestSweepingCashInError.ItemNotFound -> SweepingCashInStatus.FAILED
    is RequestSweepingCashInError.PaymentNotCreated -> SweepingCashInStatus.FAILED
    is RequestSweepingCashInError.LimitError -> SweepingCashInStatus.FAILED
    is RequestSweepingCashInError.ActiveConsentNotFound -> SweepingCashInStatus.FAILED
}

sealed class UpdateSweepingCashInError : PrintableSealedClassV2() {
    data class ItemNotFound(val sweepingCashInId: SweepingCashInId) : UpdateSweepingCashInError()
    data class GenericError(val message: String) : UpdateSweepingCashInError()
}

sealed class GetSweepingCashInError : PrintableSealedClassV2() {
    data class ItemNotFound(val sweepingCashInId: SweepingCashInId) : GetSweepingCashInError()
    data class GenericError(val message: String) : GetSweepingCashInError()
}

data class CheckSweepingCashInStatusMessage(
    val sweepingCashInId: String,
)

@JsonInclude(JsonInclude.Include.NON_EMPTY)
data class BlipPayload(
    val payload: String = getLocalDate().format(dateFormat),
    val action: String,
    val transactionId: String? = null,
    val amount: String? = null,
) {
    fun toJsonString(): String = getObjectMapper().writeValueAsString(this)
}