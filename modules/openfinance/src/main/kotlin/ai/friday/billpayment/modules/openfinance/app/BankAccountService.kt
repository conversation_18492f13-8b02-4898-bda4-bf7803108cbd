package ai.friday.billpayment.modules.openfinance.app

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.OpenFinanceBankAccountService
import ai.friday.billpayment.app.integrations.OpenFinanceParticipantId
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.openfinance.OpenFinance
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import jakarta.inject.Named
import java.time.ZonedDateTime
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

const val bankAccountNumberLockProvider = "bank-account-number"

@OpenFinance
class BankAccountService(
    private val consentService: ConsentService,
    private val openFinanceAdapter: OpenFinanceAdapter,
    private val bankAccountNumberRepository: OpenFinanceBankAccountNumberRepository,
    @Named(bankAccountNumberLockProvider) private val lockProvider: InternalLock,
) : OpenFinanceBankAccountService {
    private val logger = LoggerFactory.getLogger(BankAccountService::class.java)

    fun registerBankAccountNumber(accountId: AccountId, data: BankAccountNumberData): Either<AlreadyLockedError, OpenFinanceBankAccountNumber> {
        val lock = lockProvider.acquireLock("${accountId.value}#${data.ispb}")
            ?: return AlreadyLockedError(accountId, data.ispb).left()

        val logName = "BankAccountService#registerBankAccountNumber"
        val markers =
            append("accountId", accountId.value)
                .andAppend("bankAccountNumberData", data)

        return try {
            bankAccountNumberRepository.findByAccountIdAndIspbAndRoutingNumber(
                accountId = accountId,
                ispb = data.ispb,
                routingNumber = data.routingNumber,
            ).firstOrNull {
                data.matches(it.data)
            }?.let { existingNumber ->
                markers.andAppend("existingNumber", existingNumber)
                if (existingNumber.data.accountDv == null && data.accountDv != null) {
                    bankAccountNumberRepository.save(existingNumber.copy(data = data))
                } else {
                    existingNumber
                }
            } ?: bankAccountNumberRepository.save(
                OpenFinanceBankAccountNumber(
                    id = BankAccountNumberId(),
                    accountId = accountId,
                    data = data,
                    createdAt = getZonedDateTime(),
                    updatedAt = getZonedDateTime(),
                ),
            ).also {
                markers.andAppend("bankAccountNumber", it)
                logger.info(markers, logName)
            }
        } finally {
            lock.unlock()
        }.right()
    }

    override fun getBalances(accountId: AccountId, participantIds: List<OpenFinanceParticipantId>, walletId: WalletId): Map<OpenFinanceParticipantId, Long?> {
        val logName = "BankAccountService#getBalances"
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("walletId", walletId.value)
            .andAppend("participantIds", participantIds.map { it.value })

        val activeConsents = consentService.getActiveConsents(accountId, walletId)
        markers.andAppend("activeConsents", activeConsents)

        return runBlocking {
            participantIds.associateWith { participantId ->
                async {
                    activeConsents.singleOrNull {
                        it is SweepingConsentWithBankAccountData && it.participantId == participantId
                    }?.let { sweepingConsent ->
                        activeConsents.filterIsInstance<BankAccountDataConsent>().singleOrNull {
                            it.participantId == participantId && it.bankAccountData.id == sweepingConsent.bankAccountData.id
                        }?.let { dataConsent ->
                            dataConsent.balance().getOrElse { error ->
                                logger.warn(markers.andAppend("error", error), logName)
                                null
                            }?.automaticallyInvestedAmount
                        }
                    }
                }
            }.mapValues {
                it.value.await()
            }
        }.also {
            markers.andAppend("balances", it)
            logger.info(markers, logName)
        }
    }

    fun getBalance(accountId: AccountId, bankAccountNumberId: BankAccountNumberId): Either<String, BankAccountBalance> {
        val logName = "BankAccountService#getBalance"
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("bankAccountDataId", bankAccountNumberId.value)

        val activeConsents = consentService.getActiveBankAccountDataConsents(accountId)
        markers.andAppend("activeConsents", activeConsents)

        val consent = activeConsents.firstOrNull { it.bankAccountData.id == bankAccountNumberId }
        markers.andAppend("consent", consent)

        if (consent == null) {
            logger.warn(markers, logName)
            return "consent not found for ${bankAccountNumberId.value}".left()
        }

        return consent.balance().mapLeft {
            logger.warn(markers, logName)
            return it.left()
        }
    }

    private fun BankAccountDataConsent.balance(): Either<String, BankAccountBalance> {
        if (bankAccountData !is BankAccount2BankAccountDataAdapter) {
            return "invalid bankAccountData type ${bankAccountData::class.java.simpleName}".left()
        }

        return openFinanceAdapter.getBankAccountBalance(
            dataConsentId = id,
            bankAccountResourceId = bankAccountData.bankAccount.bankAccountResourceId,
        )
    }
}

data class BankAccountBalance(
    val availableAmount: Long,
    val blockedAmount: Long,
    val automaticallyInvestedAmount: Long,
    val updateDateTime: ZonedDateTime,
)

data class OpenFinanceBankAccountNumber(
    val id: BankAccountNumberId,
    val accountId: AccountId,
    val data: BankAccountNumberData,
    val createdAt: ZonedDateTime,
    val updatedAt: ZonedDateTime,
)

data class BankAccountNumberData(
    val ispb: String,
    val bankName: String,
    val routingNumber: String,
    val accountNumber: String,
    val accountDv: String?,
) {
    private val sanitizedRoutingNumber = routingNumber.removeLeadingZeros()
    private val sanitizedAccountNumber = accountNumber.removeLeadingZeros()
    private val fullAccountNumber = sanitizedAccountNumber + accountDv.orEmpty()

    fun matches(other: BankAccountNumberData): Boolean {
        return ispb == other.ispb &&
            sanitizedRoutingNumber == other.sanitizedRoutingNumber &&
            when {
                accountDv != null && other.accountDv != null -> fullAccountNumber == other.fullAccountNumber
                accountDv == null && other.accountDv == null -> fullAccountNumber == other.fullAccountNumber || fullAccountNumber.dropLast(1) == other.fullAccountNumber || fullAccountNumber == other.fullAccountNumber.dropLast(1)
                accountDv != null -> fullAccountNumber == other.fullAccountNumber || sanitizedAccountNumber == other.fullAccountNumber
                else -> fullAccountNumber == other.fullAccountNumber || fullAccountNumber == other.sanitizedAccountNumber
            }
    }

    override fun toString(): String = "$ispb#$sanitizedRoutingNumber#$sanitizedAccountNumber#${accountDv.orEmpty()}"
}

fun String.removeLeadingZeros() = dropWhile { it == '0' }

data class AlreadyLockedError(
    val accountId: AccountId,
    val ispb: String,
)