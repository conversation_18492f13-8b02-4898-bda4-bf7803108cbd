package ai.friday.billpayment.modules.openfinance.adapters

import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.EndToEnd
import ai.friday.billpayment.app.integrations.ExternalTransactionId
import ai.friday.billpayment.app.integrations.SweepingCashInId
import ai.friday.billpayment.app.integrations.SweepingCashInSource
import ai.friday.billpayment.app.integrations.SweepingCashInStatus
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.openfinance.OpenFinance
import ai.friday.billpayment.modules.openfinance.app.BankAccountNumberData
import ai.friday.billpayment.modules.openfinance.app.BankAccountNumberId
import ai.friday.billpayment.modules.openfinance.app.BankAccountResourceId
import ai.friday.billpayment.modules.openfinance.app.BankAccountService
import ai.friday.billpayment.modules.openfinance.app.ConsentService
import ai.friday.billpayment.modules.openfinance.app.ConsentStatus
import ai.friday.billpayment.modules.openfinance.app.CreditorId
import ai.friday.billpayment.modules.openfinance.app.DataConsentId
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceBankAccount
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceBankTransaction
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceProcessTransactionError
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceResourceType
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceService
import ai.friday.billpayment.modules.openfinance.app.SweepingAccountService
import ai.friday.billpayment.modules.openfinance.app.SweepingConsentId
import ai.friday.billpayment.modules.openfinance.app.SweepingDebtor
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Property
import io.micronaut.tracing.annotation.NewSpan
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@OpenFinance
open class OpenFinanceDataHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val openFinanceService: OpenFinanceService,
    private val consentService: ConsentService,
    private val sweepingAccountService: SweepingAccountService,
    private val bankAccountService: BankAccountService,
    @Property(name = "sqs.openFinanceQueueName") private val queueName: String,
) : AbstractSQSHandler(amazonSQS, configuration, queueName) {
    @Trace
    @NewSpan
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val messageAttributes = message.messageAttributes()
        val resourceType = messageAttributes["resourceType"]?.stringValue()
        logger.info(
            append("body", message.body())
                .andAppend("attributes", messageAttributes.entries)
                .andAppend("resourceType", resourceType),
            "OpenFinanceDataHandler#handleMessage",
        )

        if (resourceType == null) {
            return SQSHandlerResponse(shouldDeleteMessage = false)
        }
        val type = OpenFinanceResourceType.valueOf(resourceType)
        return when (type) {
            OpenFinanceResourceType.BANK_TRANSACTION -> processBankTransaction(message)
            OpenFinanceResourceType.BANK_ACCOUNT -> processBankAccount(message)
            OpenFinanceResourceType.DATA_CONSENT -> processDataConsentStatus(message)
            OpenFinanceResourceType.SWEEPING_ACCOUNT -> processSweepingAccountStatus(message)
            OpenFinanceResourceType.SWEEPING_PAYMENT -> processSweepingCashIn(message)
            else -> SQSHandlerResponse(shouldDeleteMessage = false)
        }
    }

    private fun processSweepingCashIn(m: Message): SQSHandlerResponse {
        val logName = "OpenFinanceDataHandler#processSweepingCashIn"
        val markers = append("messageId", m.messageId())
            .andAppend("messageAttributes", m.messageAttributes())

        val sweepingCashIn = parseObjectFrom<SweepingCashInMessage>(m.body())
        markers.andAppend("sweepingCashIn", sweepingCashIn)

        val result = sweepingAccountService.updateSweepingCashIn(
            sweepingCashInId = SweepingCashInId(sweepingCashIn.id),
            status = sweepingCashIn.status.openFinanceStatus,
            error = sweepingCashIn.error,
            errorDescription = sweepingCashIn.errorDescription,
        )
        return result.fold(
            ifLeft = {
                markers.andAppend("error", it)
                logger.error(markers, logName)
                return SQSHandlerResponse(shouldDeleteMessage = false)
            },
            ifRight = {
                logger.info(markers, logName)
                SQSHandlerResponse(shouldDeleteMessage = true)
            },
        )
    }

    private fun processDataConsentStatus(m: Message): SQSHandlerResponse {
        val logName = "OpenFinanceDataHandler#processDataConsentStatus"
        val messageBody = parseObjectFrom<OpenFinanceDataConsentStatusMessage>(m.body())
        val markers =
            append("messageId", m.messageId())
                .andAppend("messageAttributes", m.messageAttributes())
                .andAppend("messageBody", messageBody)
        try {
            consentService.updateConsentAndNotify(
                consentId = DataConsentId(messageBody.dataConsentId),
                accountId = AccountId(messageBody.userAccountId),
                status = messageBody.status.internalStatus,
            )

            logger.info(markers, logName)
            return SQSHandlerResponse(shouldDeleteMessage = true)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return SQSHandlerResponse(shouldDeleteMessage = false)
        }
    }

    private fun processSweepingAccountStatus(m: Message): SQSHandlerResponse {
        val logName = "OpenFinanceDataHandler#processSweepingAccountStatus"
        val messageBody = parseObjectFrom<OpenFinanceSweepingConsentStatusMessage>(m.body())
        val markers =
            append("messageId", m.messageId())
                .andAppend("messageAttributes", m.messageAttributes())
                .andAppend("messageBody", messageBody)
        try {
            val debtor = messageBody.debtor?.let { debtorTO ->
                val sweepingConsent = consentService.getSweepingConsent(
                    sweepingConsentId = SweepingConsentId(
                        messageBody.sweepingConsentId,
                    ),
                )
                markers.andAppend("sweepingConsent", sweepingConsent)

                val bankAccountNumber = bankAccountService.registerBankAccountNumber(
                    accountId = sweepingConsent.accountId,
                    data = BankAccountNumberData(
                        ispb = debtorTO.ispb,
                        bankName = debtorTO.bankName.orEmpty(),
                        routingNumber = debtorTO.routingNumber,
                        accountNumber = debtorTO.accountNumber,
                        accountDv = null,
                    ),
                ).getOrElse {
                    markers.andAppend("error", it)
                    logger.error(markers, logName)
                    return SQSHandlerResponse(shouldDeleteMessage = false)
                }
                markers.andAppend("bankAccountNumber", bankAccountNumber)

                debtorTO.toDomain(bankAccountNumber.id)
            }

            consentService.updateConsentAndNotify(SweepingConsentId(messageBody.sweepingConsentId), messageBody.status, debtor)

            logger.info(markers, logName)
            return SQSHandlerResponse(shouldDeleteMessage = true)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return SQSHandlerResponse(shouldDeleteMessage = false)
        }
    }

    private fun processBankTransaction(m: Message): SQSHandlerResponse {
        val transaction = parseObjectFrom<OpenFinanceBankTransaction>(m.body())
        val markers =
            append("messageId", m.messageId())
                .andAppend("messageAttributes", m.messageAttributes())
                .andAppend("transaction", transaction)

        val result = openFinanceService.process(transaction)
        return result.fold(
            ifLeft = { e ->
                when (e) {
                    is OpenFinanceProcessTransactionError.DocumentNotFound -> {
                        logger.warn(markers, "OpenFinanceDataHandler#processBankTransaction")
                        SQSHandlerResponse(shouldDeleteMessage = true)
                    }

                    is OpenFinanceProcessTransactionError.OpenFinancePixCreationError -> {
                        logger.warn(markers, "OpenFinanceDataHandler#processBankTransaction")
                        SQSHandlerResponse(shouldDeleteMessage = true)
                    }

                    else -> {
                        logger.error(markers, "OpenFinanceDataHandler#processBankTransaction")
                        return SQSHandlerResponse(shouldDeleteMessage = false)
                    }
                }
            },
            ifRight = {
                logger.info(markers, "OpenFinanceDataHandler#processBankTransaction")
                SQSHandlerResponse(shouldDeleteMessage = true)
            },
        )
    }

    private fun processBankAccount(m: Message): SQSHandlerResponse {
        val bankAccount = parseObjectFrom<OpenFinanceBankAccountMessage>(m.body())
        val logName = "OpenFinanceDataHandler#processBankAccount"
        val markers =
            append("messageId", m.messageId())
                .andAppend("messageAttributes", m.messageAttributes())
                .andAppend("bankAccount", bankAccount)

        val bankAccountNumber = bankAccountService.registerBankAccountNumber(
            accountId = AccountId(bankAccount.userAccountId),
            data = BankAccountNumberData(
                ispb = bankAccount.ispb,
                bankName = bankAccount.brandName,
                routingNumber = bankAccount.routingNumber,
                accountNumber = bankAccount.accountNumber,
                accountDv = bankAccount.accountDv,
            ),
        ).getOrElse {
            markers.andAppend("error", it)
            logger.error(markers, logName)
            return SQSHandlerResponse(shouldDeleteMessage = false)
        }
        markers.andAppend("bankAccountNumber", bankAccountNumber)

        val result = openFinanceService.process(bankAccount.toDomain(bankAccountNumber.id))
        return result.fold(
            ifLeft = { e ->
                logger.error(markers, logName, e)
                SQSHandlerResponse(shouldDeleteMessage = false)
            },
            ifRight = {
                logger.info(markers, logName)
                SQSHandlerResponse(shouldDeleteMessage = true)
            },
        )
    }

    override fun handleError(
        message: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(append("event", message), "OpenFinanceDataHandler", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(OpenFinanceDataHandler::class.java)
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class OpenFinanceSweepingConsentStatusMessage(
    @JsonAlias("sweepingAccountId")
    val sweepingConsentId: String,
    val status: ConsentStatus,
    val debtor: OpenFinanceSweepingAccountDebtorTO? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class OpenFinanceDataConsentStatusMessage(
    val dataConsentId: String,
    val userAccountId: String,
    val status: DataConsentStatus,
)

enum class DataConsentStatus(val final: Boolean, val internalStatus: ConsentStatus) {
    AVAILABLE(true, ConsentStatus.AUTHORISED),
    UNAVAILABLE(true, ConsentStatus.REJECTED),
    REVOKED(true, ConsentStatus.REVOKED),
    TEMPORARILY_UNAVAILABLE(false, ConsentStatus.AWAITING_AUTHORISATION),
    PENDING_AUTHORISATION(false, ConsentStatus.AWAITING_AUTHORISATION),
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class OpenFinanceSweepingAccountDebtorTO(
    val ispb: String,
    @JsonAlias("issuer")
    val routingNumber: String,
    @JsonAlias("number")
    val accountNumber: String,
    val accountType: String,
    val bankName: String?,
)

fun OpenFinanceSweepingAccountDebtorTO.toDomain(bankAccountNumberId: BankAccountNumberId) = SweepingDebtor(
    ispb = this.ispb,
    routingNumber = this.routingNumber,
    accountNumber = this.accountNumber,
    accountType = this.accountType,
    bankName = this.bankName,
    bankAccountNumberId = bankAccountNumberId,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SweepingCashInMessage(
    @JsonAlias("requestId")
    val id: String,
    val status: SweepingPaymentStatus,
    val amount: Long,
    val error: String? = null,
    val errorDescription: String? = null,
)

enum class SweepingPaymentStatus(val openFinanceStatus: SweepingCashInStatus) {
    CREATED(SweepingCashInStatus.CREATED),
    PROCESSING(SweepingCashInStatus.CREATED),
    UNKNOWN(SweepingCashInStatus.UNKNOWN),
    FAILED(SweepingCashInStatus.FAILED),
    SUCCESS(SweepingCashInStatus.WAITING_SETTLEMENT),
}

data class SweepingCashInStatusResponse(
    val status: SweepingCashInStatus,
    val error: String?,
    val errorDescription: String?,
)

data class OpenFinanceSweepingCashIn(
    val id: SweepingCashInId,
    val walletId: WalletId,
    val consentId: SweepingConsentId,
    val creditorId: CreditorId,
    val amount: Long,
    val description: String,
    val status: SweepingCashInStatus,
    val endToEnd: EndToEnd?,
    val error: String? = null,
    val errorDescription: String? = null,
    val createdAt: ZonedDateTime = getZonedDateTime(),
    val updatedAt: ZonedDateTime = getZonedDateTime(),
    val requestSource: SweepingCashInSource,
    val approvalSource: SweepingCashInSource,
    val externalTransactionId: ExternalTransactionId?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class OpenFinanceBankAccountMessage(
    val userAccountId: String,
    val bankAccountId: String,
    @JsonAlias("bankCode")
    val compe: String,

    val dataConsentId: String,

    val brandName: String,
    val ispb: String,

    val routingNumber: String,
    val accountNumber: String,
    val accountDv: String,
    val accountType: String,
    val accountSubType: String,
    val currency: String,
) {
    fun toDomain(bankAccountNumberId: BankAccountNumberId) = OpenFinanceBankAccount(
        userAccountId = AccountId(userAccountId),
        bankAccountResourceId = BankAccountResourceId(bankAccountId),
        bankCode = compe,
        createdAt = getZonedDateTime(),
        updatedAt = getZonedDateTime(),
        data = null,
        dataConsentId = DataConsentId(dataConsentId),
        brandName = brandName,
        ispb = ispb,
        routingNumber = routingNumber,
        accountNumber = accountNumber,
        accountDv = accountDv,
        accountType = accountType,
        accountSubType = accountSubType,
        currency = currency,
        bankAccountNumberId = bankAccountNumberId,
    )
}