package ai.friday.billpayment.modules.openfinance.app

sealed class CustomErrors(val exception: Exception)

class OpenFinanceAdapterException(exception: Exception) : CustomErrors(exception)
class OpenFinanceError(exception: Exception) : CustomErrors(exception)

sealed class OpenFinanceProcessTransactionError(val message: String) {
    class NameNotFound(exception: Exception) : OpenFinanceProcessTransactionError(exception.message.orEmpty())
    data object BankAccountIdNotFound : OpenFinanceProcessTransactionError("bankAccountId not found")
    data object DocumentNotFound : OpenFinanceProcessTransactionError("Document is required")
    data object ManualEntryCreationError : OpenFinanceProcessTransactionError("Error creating manual entry")
    data object OpenFinancePixCreationError : OpenFinanceProcessTransactionError("Error creating OpenFinance Pix")
    data object OwnAccountTransferError : OpenFinanceProcessTransactionError("Transfer to own account")
}

sealed class OpenFinanceSweepingAccountError(val message: String) {
    data object InvalidParticipant : OpenFinanceSweepingAccountError("Invalid participant")
    data object WalletNotFound : OpenFinanceSweepingAccountError("Wallet not found")
    data object PaymentMethodNotFound : OpenFinanceSweepingAccountError("Payment method not found")
    class ConsentCreationError(message: String) : OpenFinanceSweepingAccountError(message)
    class ConsentRevokeError(message: String) : OpenFinanceSweepingAccountError(message)
    data object ConsentNotFound : OpenFinanceSweepingAccountError("Consent not found")
    data object ConsentExpired : OpenFinanceSweepingAccountError("Consent expired")
}

sealed class FindError {
    data object NotFound : FindError()
    data object MultipleItemsFound : FindError()
    class ServerError(val exception: Exception) : FindError()
}