package ai.friday.billpayment.modules.pfm.adapters.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.pfm.PFMBillCategoryService
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.integration.onWallet
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.netty.handler.codec.http.HttpResponseStatus
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

@MicronautTest(environments = [FRIDAY_ENV, ME_POUPE_ENV])
class BillCategoryControllerIntegrationTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @MockBean(PFMBillCategoryService::class)
    fun billCategoryService(): PFMBillCategoryService = billCategoryService
    private val billCategoryService = mockk<PFMBillCategoryService> {
        every {
            setBillCategory(any(), any(), any(), any())
        } returns Unit.right()
    }

    private val enhancedClient = getDynamoDB()

    private val wallet = WalletFixture().buildWallet()

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = mockk(),
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        loadAccountIntoDb(dynamoDB)

        walletRepository.save(wallet)
    }

    @Test
    fun `deve categorizar mesmo se não enviar receber recorrencia na requisição`() {
        val request = HttpRequest.PUT("/bill/id/123/category/BILL_CATEGORY_ID", Unit)
            .onWallet(wallet = wallet)

        val response = client.toBlocking().exchange(request, Argument.of(Unit::class.java))

        response.status.code shouldBe HttpResponseStatus.OK.code()
    }
}