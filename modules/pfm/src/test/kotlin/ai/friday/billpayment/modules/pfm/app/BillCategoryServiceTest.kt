package ai.friday.billpayment.modules.pfm.app

import DynamoDBUtils
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.bill.DefaultBillEventPublisher
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillRecurrenceDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillRecurrenceDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletBillCategoryDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletBillCategoryDynamoDAO
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillCategoryAdded
import ai.friday.billpayment.app.bill.BillCategoryDeleted
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.RemoveBillCategoryError
import ai.friday.billpayment.app.pfm.SetBillCategoryError
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.recurrence.Range
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.weeklyRecurrenceNoEndDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.dateFormat
import arrow.core.getOrElse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDate
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class BillCategoryServiceTest {

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    val dbEnhancedClient = DynamoDBUtils.setupDynamoDB()

    private val billEventDAO = BillEventDynamoDAO(dbEnhancedClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(dbEnhancedClient)
    private val transactionDynamo = TransactionDynamo(dbEnhancedClient)

    private val billEventRepository = BillEventDBRepository(
        billEventDAO = billEventDAO,
        uniqueConstraintDAO = uniqueConstraintDAO,
        featureConfiguration = mockk(relaxed = true),
        transactionDynamo = transactionDynamo,
    )
    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(dbEnhancedClient),
        refundedClient = RefundedBillDynamoDAO(dbEnhancedClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(dbEnhancedClient),
    )
    private val walletBillCategoryRepository =
        WalletBillCategoryDbRepository(client = WalletBillCategoryDynamoDAO(cli = DynamoDBUtils.setupDynamoDB()))
    private val dynamoDbEnhancedClient = DynamoDBUtils.setupDynamoDB()
    private val billRecurrenceDynamoDAO = BillRecurrenceDynamoDAO(dynamoDbEnhancedClient)
    private val recurrenceRepository = BillRecurrenceDBRepository(
        client = billRecurrenceDynamoDAO,
        lastLimitDateString = getLocalDate().plusYears(1).format(dateFormat),
    )

    private val eventPublisher: EventPublisher = mockk(relaxUnitFun = true)

    private val billEventPublisher = DefaultBillEventPublisher(
        eventRepository = billEventRepository,
        billRepository = billRepository,
        eventPublisher = eventPublisher,
    )

    private val lockProvider: InternalLock = mockk(relaxed = true)

    private val billCategoryService = BillCategoryService(
        billEventRepository = billEventRepository,
        walletBillCategoryRepository = walletBillCategoryRepository,
        billEventPublisher = billEventPublisher,
        billRecurrenceRepository = recurrenceRepository,
        lockProvider = lockProvider,
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
    }

    @Nested
    @DisplayName("ao tentar aplicar uma categoria")
    inner class SetBillCategory {
        private val billCategoryId = PFMCategoryId("EDUCACAO")

        @Test
        fun `deve retornar AlreadyLocked quando a bill estiver lockada por outro processo`() {
            every { lockProvider.acquireLock(any()) } returns null

            val result = billCategoryService.setBillCategory(billAdded.billId, billCategoryId, ACCOUNT.accountId)

            result.isLeft() shouldBe true
            result.getOrElse {
                it shouldBe SetBillCategoryError.AlreadyLocked
            }
        }

        @Test
        fun `deve retornar BillNotFound quando nao encontra a bill`() {
            val result = billCategoryService.setBillCategory(billAdded.billId, billCategoryId, ACCOUNT.accountId)

            result.isLeft() shouldBe true
            result.getOrElse {
                it shouldBe SetBillCategoryError.BillNotFound
            }
        }

        @Test
        fun `deve retornar CategoryNotFound quando nao encontra a categoria`() {
            billEventRepository.save(billAdded)

            val result = billCategoryService.setBillCategory(billAdded.billId, billCategoryId, ACCOUNT.accountId)

            result.isLeft() shouldBe true
            result.getOrElse {
                it shouldBe SetBillCategoryError.CategoryNotFound
            }
        }

        @Nested
        @DisplayName("quando é uma recorrencia")
        inner class SetRecurrenceCategory {
            private val oldRecurrencePix = pixAdded.copy(
                effectiveDueDate = LocalDate.now()
                    .minusWeeks(1),
                dueDate = LocalDate.now()
                    .minusWeeks(1),
                billId = BillId(),
                actionSource = ActionSource.WalletRecurrence(
                    accountId = ACCOUNT.accountId,
                    recurrenceId = weeklyRecurrenceNoEndDate.id,
                ),
            )
            private val originalPix = pixAdded.copy(
                actionSource = ActionSource.WalletRecurrence(
                    accountId = ACCOUNT.accountId,
                    recurrenceId = weeklyRecurrenceNoEndDate.id,
                ),
            )
            private val recurrencePix = pixAdded.copy(
                effectiveDueDate = LocalDate.now()
                    .plusWeeks(1),
                dueDate = LocalDate.now()
                    .plusWeeks(1),
                billId = BillId(),
                actionSource = ActionSource.WalletRecurrence(
                    accountId = ACCOUNT.accountId,
                    recurrenceId = weeklyRecurrenceNoEndDate.id,
                ),
            )

            private val walletBillCategory = WalletBillCategory(
                walletId = originalPix.walletId,
                categoryId = billCategoryId,
                name = "EDUCACAO",
                icon = "EDUCACAO",
                enabled = false,
                default = true,
            )

            @BeforeEach
            fun setup() {
                recurrenceRepository.save(weeklyRecurrenceNoEndDate.copy(bills = listOf(originalPix.billId, recurrencePix.billId)))
                billEventRepository.save(oldRecurrencePix)
                billEventRepository.save(originalPix)
                billEventRepository.save(recurrencePix)

                walletBillCategoryRepository.save(walletBillCategory)
            }

            @Test
            fun `quando é uma recorrencia e for pedido para atualizar apenas uma conta, deve atualizar apenas uma conta`() {
                val result = billCategoryService.setBillCategory(
                    originalPix.billId,
                    billCategoryId,
                    ACCOUNT.accountId,
                    Range.THIS,
                )

                result.isRight().shouldBeTrue()

                val eventSlot = mutableListOf<BillCategoryAdded>()
                verify {
                    eventPublisher.publish(capture(eventSlot), any())
                }
                eventSlot.shouldHaveSize(1)

                with(eventSlot.first()) {
                    this.billId shouldBe originalPix.billId
                    this.walletId shouldBe originalPix.walletId
                    with(this.categoryId) {
                        walletId shouldBe walletBillCategory.walletId
                        billCategoryId shouldBe walletBillCategory.categoryId
                    }
                }
            }

            @Test
            fun `quando é uma recorrencia e for pedido para atualizar todas as contas deve atualizar as recorrencias`() {
                val result = billCategoryService.setBillCategory(
                    originalPix.billId,
                    billCategoryId,
                    ACCOUNT.accountId,
                    Range.THIS_AND_FUTURE,
                )

                result.isRight().shouldBeTrue()

                val recurrence = recurrenceRepository.find(weeklyRecurrenceNoEndDate.id, originalPix.walletId)

                recurrence.billCategoryId shouldBe walletBillCategory.categoryId

                val eventSlot = mutableListOf<BillCategoryAdded>()
                verify {
                    eventPublisher.publish(capture(eventSlot), any())
                }
                eventSlot.shouldHaveSize(2)

                with(eventSlot.first()) {
                    this.billId shouldBe originalPix.billId
                    this.walletId shouldBe originalPix.walletId
                    with(this.categoryId) {
                        walletId shouldBe walletBillCategory.walletId
                        billCategoryId shouldBe walletBillCategory.categoryId
                    }
                }

                with(eventSlot.last()) {
                    this.billId shouldBe recurrencePix.billId
                    this.walletId shouldBe recurrencePix.walletId
                    with(this.categoryId) {
                        walletId shouldBe walletBillCategory.walletId
                        billCategoryId shouldBe walletBillCategory.categoryId
                    }
                }
            }
        }

        @Test
        fun `deve retornar sucesso quando consegue aplicar a categoria`() {
            billEventRepository.save(billAdded)

            val walletBillCategory = WalletBillCategory(
                walletId = billAdded.walletId,
                categoryId = billCategoryId,
                name = "EDUCACAO",
                icon = "EDUCACAO",
                enabled = false,
                default = true,
            )

            walletBillCategoryRepository.save(walletBillCategory)

            val result = billCategoryService.setBillCategory(billAdded.billId, billCategoryId, ACCOUNT.accountId)

            result.isRight() shouldBe true

            val eventSlot = slot<BillCategoryAdded>()
            verify {
                eventPublisher.publish(capture(eventSlot), any())
            }
            with(eventSlot.captured) {
                this.billId shouldBe billAdded.billId
                this.walletId shouldBe billAdded.walletId
                with(this.actionSource) {
                    this.shouldBeTypeOf<ActionSource.Api>()
                    this.accountId shouldBe ACCOUNT.accountId
                }
                with(this.categoryId) {
                    walletId shouldBe walletBillCategory.walletId
                    billCategoryId shouldBe walletBillCategory.categoryId
                }
            }

            val billView = billRepository.findBill(billAdded.billId, billAdded.walletId)

            billView.categoryId shouldBe walletBillCategory.categoryId
        }
    }

    @Nested
    @DisplayName("ao tentar remover uma categoria")
    inner class DeleteBillCategory {
        @Test
        fun `deve retornar BillNotFound quando nao encontra a bill`() {
            val result = billCategoryService.removeBillCategory(billAdded.billId, ACCOUNT.accountId)

            result.isLeft() shouldBe true
            result.getOrElse {
                it shouldBe RemoveBillCategoryError.BillNotFound
            }
        }

        @Test
        fun `deve retornar sucesso quando consegue remover a categoria`() {
            billEventRepository.save(billAdded)

            val result = billCategoryService.removeBillCategory(billAdded.billId, ACCOUNT.accountId)
            result.isRight() shouldBe true

            val eventSlot = slot<BillCategoryDeleted>()
            verify {
                eventPublisher.publish(capture(eventSlot), any())
            }
            with(eventSlot.captured) {
                this.billId shouldBe billAdded.billId
                this.walletId shouldBe billAdded.walletId
                with(this.actionSource) {
                    this.shouldBeTypeOf<ActionSource.Api>()
                    this.accountId shouldBe ACCOUNT.accountId
                }
            }

            val billView = billRepository.findBill(billAdded.billId, billAdded.walletId)

            billView.categoryId shouldBe null
        }
    }
}