package ai.friday.billpayment.modules.pfm.app

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.adapters.dynamodb.DefaultWalletBillCategoryDbRepository
import ai.friday.billpayment.adapters.dynamodb.DefaultWalletBillCategoryDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletBillCategoryDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletBillCategoryDynamoDAO
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.pfm.CreateDefaultCategoriesError
import ai.friday.billpayment.app.pfm.CreateWalletBillCategoryError
import ai.friday.billpayment.app.pfm.DefaultWalletBillCategory
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.UpdateWalletBillCategoryError
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.pfm.WalletBillCategoryRepository
import ai.friday.billpayment.app.wallet.WalletId
import arrow.core.getOrElse
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlin.test.junit.JUnitAsserter.fail
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

class WalletBillCategoryServiceTest {
    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val defaultBillCategoryRepository =
        DefaultWalletBillCategoryDbRepository(
            client =
            DefaultWalletBillCategoryDynamoDAO(
                cli = dynamoDbEnhancedClient,
            ),
        )

    private val lockProvider: InternalLock = mockk(relaxed = true)

    private val walletId = WalletId("wallet")

    @Nested
    @DisplayName("ao tentar inicializar as categorias de uma carteira")
    inner class InitWalletCategory {
        private val walletBillCategoryRepository: WalletBillCategoryRepository = mockk(relaxUnitFun = true)

        private val walletBillCategoryService =
            WalletBillCategoryService(
                defaultWalletBillCategoryRepository = defaultBillCategoryRepository,
                walletBillCategoryRepository = walletBillCategoryRepository,
                lockProvider = lockProvider,
                messagePublisher = mockk(relaxed = true),
                configuration = mockk(relaxed = true),
            )

        private val defaultBillCategories = listOf(
            DefaultWalletBillCategory(
                categoryId = PFMCategoryId(value = "CARTAO"),
                name = "CARTAO",
                icon = "CARTAO",
            ),
        )

        @BeforeEach
        fun setup() {
            defaultBillCategories.forEach {
                defaultBillCategoryRepository.save(it)
            }
        }

        @Test
        fun `deve criar as categorias padrão quando nao tem categoria na carteira`() {
            every {
                walletBillCategoryRepository.findByWalletId(walletId)
            } returns emptyList()

            val result = walletBillCategoryService.createDefaultWalletCategories(walletId)

            result.isRight() shouldBe true

            result.map { categories ->
                categories.size shouldBe defaultBillCategories.size

                categories.forEach {
                    val category = defaultBillCategories.find { category -> it.name == category.name }

                    category.shouldNotBeNull()

                    it.default shouldBe true
                    it.enabled shouldBe true
                    it.icon shouldBe category.icon
                    it.walletId shouldBe walletId
                }
            }
        }

        @Test
        fun `deve dar erro se não conseguir o lock para criação de categorias`() {
            every { lockProvider.acquireLock(any()) } returns null

            val result = walletBillCategoryService.createDefaultWalletCategories(walletId)

            result.isLeft() shouldBe true
            result.mapLeft {
                it shouldBe CreateDefaultCategoriesError.CouldNotAcquireLock
            }
        }

        @Test
        fun `deve dar erro se a carteira já possuir categorias`() {
            val billCategories = listOf(
                WalletBillCategory(
                    walletId = walletId,
                    categoryId = PFMCategoryId(value = "COMPRAS"),
                    name = "COMPRAS",
                    icon = "COMPRAS",
                    enabled = true,
                    default = true,
                ),
            )

            every { walletBillCategoryRepository.findByWalletId(any()) } returns billCategories

            val result = walletBillCategoryService.createDefaultWalletCategories(walletId)

            result.isLeft() shouldBe true
            result.mapLeft {
                it shouldBe CreateDefaultCategoriesError.WalletAlreadyHasCategories
            }
        }
    }

    @Nested
    @DisplayName("ao tentar listar as categorias de uma carteira")
    inner class FindWalletCategories {
        private val defaultBillCategories = listOf(
            DefaultWalletBillCategory(
                categoryId = PFMCategoryId(value = "CARTAO"),
                name = "CARTAO",
                icon = "CARTAO",
            ),
        )

        @BeforeEach
        fun setup() {
            defaultBillCategories.forEach {
                defaultBillCategoryRepository.save(it)
            }
        }

        private val billCategoryRepository =
            WalletBillCategoryDbRepository(
                client =
                WalletBillCategoryDynamoDAO(
                    cli = dynamoDbEnhancedClient,
                ),
            )

        private val messagePublisher = mockk<MessagePublisher>(relaxed = true)

        private val walletBillCategoryService =
            WalletBillCategoryService(
                defaultWalletBillCategoryRepository = defaultBillCategoryRepository,
                walletBillCategoryRepository = billCategoryRepository,
                lockProvider = lockProvider,
                messagePublisher = messagePublisher,
                configuration = mockk(relaxed = true),
            )

        @Test
        fun `deve retornar todas as categorias`() {
            val billCategories = listOf(
                WalletBillCategory(
                    walletId = walletId,
                    categoryId = PFMCategoryId(value = "COMPRAS"),
                    name = "COMPRAS",
                    icon = "COMPRAS",
                    enabled = true,
                    default = true,
                ),
            )

            billCategories.forEach {
                billCategoryRepository.save(it)
            }

            val categories = walletBillCategoryService.findWalletCategories(walletId)

            categories.size shouldBe billCategories.size
            categories.shouldContainExactlyInAnyOrder(billCategories)
        }

        @Test
        fun `deve retornar as categorias arquivadas`() {
            val billCategories = listOf(
                WalletBillCategory(
                    walletId = walletId,
                    categoryId = PFMCategoryId(value = "COMPRAS"),
                    name = "COMPRAS",
                    icon = "COMPRAS",
                    enabled = true,
                    default = true,
                ),
            )

            billCategories.forEach {
                billCategoryRepository.save(it)
            }

            val categories = walletBillCategoryService.findWalletCategories(walletId)

            categories.size shouldBe billCategories.size
        }

        @Test
        fun `deve retornar uma lista vazia se não tiver categoria cadastrada`() {
            val categories = walletBillCategoryService.findWalletCategories(walletId)

            categories.size shouldBe 0
        }

        @Test
        fun `deve criar assincronamente as categorias padrão caso a carteira ainda não tenha categorias`() {
            val categories = walletBillCategoryService.findWalletCategories(walletId)

            categories.size shouldBe 0

            verify {
                messagePublisher.sendMessage(any<String>(), any<Any>())
            }
        }

        @Test
        fun `deve criar sincronamente as categorias padrão caso a carteira ainda não tenha categorias`() {
            val categories = walletBillCategoryService.findWalletCategories(walletId, createAsync = false)

            categories.shouldNotBeEmpty()

            verify(exactly = 0) {
                messagePublisher.sendMessage(any<String>(), any<Any>())
            }
        }
    }

    @Nested
    @DisplayName("ao tentar criar uma categoria")
    inner class CreateCategory {
        private val billCategoryRepository =
            WalletBillCategoryDbRepository(
                client =
                WalletBillCategoryDynamoDAO(
                    cli = dynamoDbEnhancedClient,
                ),
            )

        private val walletBillCategoryService =
            WalletBillCategoryService(
                defaultWalletBillCategoryRepository = defaultBillCategoryRepository,
                walletBillCategoryRepository = billCategoryRepository,
                lockProvider = lockProvider,
                messagePublisher = mockk(),
                configuration = mockk(),
            )

        private val walletId = WalletId("wallet")

        @Test
        fun `deve criar se a categoria nao existir`() {
            val result = walletBillCategoryService.create(walletId, "custom", "EDUCACAO")

            result.isRight().shouldBeTrue()
            result.map {
                it.walletId shouldBe walletId
                it.name shouldBe "custom"
                it.icon shouldBe "EDUCACAO"
                it.enabled.shouldBeTrue()
                it.default.shouldBeFalse()
            }

            val categories = billCategoryRepository.findByWalletId(walletId)

            categories.size shouldBe 1
            categories.first() shouldBe result.getOrNull()
        }

        @ParameterizedTest
        @ValueSource(booleans = [true, false])
        fun `nao deve criar se a categoria existir e estiver habilitada`(defaultCategory: Boolean) {
            billCategoryRepository.save(
                WalletBillCategory(
                    walletId = walletId,
                    categoryId = PFMCategoryId(value = "random"),
                    name = "custom",
                    icon = "IMPOSTOS",
                    enabled = true,
                    default = defaultCategory,
                ),
            )

            val result = walletBillCategoryService.create(walletId, "custom", "EDUCACAO")

            result.isLeft().shouldBeTrue()
            result.getOrElse {
                it shouldBe CreateWalletBillCategoryError.AlreadyExists
            }
        }

        /*
                @Disabled
                @ParameterizedTest
                @ValueSource(booleans = [true, false])
                fun `deve habilitar se a categoria existir e estiver desabilitada`(defaultCategory: Boolean) {
                    billCategoryRepository.save(
                        WalletBillCategory(
                            walletId = walletId,
                            categoryId = PFMCategoryId(value = "random"),
                            name = "custom",
                            icon = "IMPOSTOS",
                            enabled = false,
                            default = defaultCategory,
                        ),
                    )

                    val result = walletBillCategoryService.create(walletId, "custom", "EDUCACAO")

                    result.isRight().shouldBeTrue()
                    result.map {
                        it.categoryId.value shouldBe "random"
                        it.walletId shouldBe walletId
                        it.name shouldBe "custom"
                        it.icon shouldBe "EDUCACAO"
                        it.enabled.shouldBeTrue()
                        it.default shouldBe defaultCategory
                    }

                    val categories = billCategoryRepository.findByWalletId(walletId)

                    categories.size shouldBe 1
                    categories.first() shouldBe result.getOrNull()
                }
        */
    }

    @Nested
    @DisplayName("ao tentar atualizar uma categoria")
    inner class UpdateCategory {
        private val billCategoryRepository =
            WalletBillCategoryDbRepository(
                client =
                WalletBillCategoryDynamoDAO(
                    cli = dynamoDbEnhancedClient,
                ),
            )

        private val walletBillCategoryService =
            WalletBillCategoryService(
                defaultWalletBillCategoryRepository = defaultBillCategoryRepository,
                walletBillCategoryRepository = billCategoryRepository,
                lockProvider = lockProvider,
                messagePublisher = mockk(),
                configuration = mockk(),
            )

        @Test
        fun `deve retornar erro ao não encontrar a categoria`() {
            val category = WalletBillCategory(walletId = walletId, categoryId = PFMCategoryId(value = "mediocrem"), name = "Lorene Barker", icon = "VIAGEM", enabled = false, default = false)
            val result = walletBillCategoryService.update(walletId, category.categoryId, "custom", "VIAGEM", enabled = true)

            result.isLeft() shouldBe true
            result.getOrElse {
                it shouldBe UpdateWalletBillCategoryError.NotFound
            }
        }

        @Test
        fun `deve salvar a categoria atualizada`() {
            val category = walletBillCategoryService.create(walletId, "custom", "EDUCACAO").getOrElse {
                fail("should not fail")
            }

            val result = walletBillCategoryService.update(walletId = walletId, categoryId = category.categoryId, billCategoryName = "new", billCategoryIcon = "COFRE", enabled = true).getOrNull()
            val resultCategory = billCategoryRepository.findByWalletIdAndBillCategoryId(walletId, category.categoryId)

            result.shouldNotBeNull()
            resultCategory.shouldNotBeNull()
            resultCategory.name shouldBe "new"
            resultCategory.icon shouldBe "COFRE"
        }
    }
}