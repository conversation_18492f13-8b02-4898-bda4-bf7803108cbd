package ai.friday.billpayment.modules.pfm.adapters.api

import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.api.WalletBillCategoryTO
import ai.friday.billpayment.app.pfm.CreateWalletBillCategoryError
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.wallet.WalletId
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpStatus
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

class WalletBillCategoryControllerTest {

    private val service: PFMWalletCategoryService = mockk()

    private val controller = WalletBillCategoryController(
        walletBillCategoryService = service,
    )

    @Nested
    @DisplayName("ao tentar criar uma categoria")
    inner class CreateCategory {

        private val walletId = WalletId("wallet")
        private val walletBillCategoryTO = CreateCustomWalletBillCategoryTO(
            name = "category",
            icon = "IMPOSTOS",
        )

        @Test
        fun `deve retornar BAD_REQUEST se a categoria já existir`() {
            every {
                service.create(any(), any(), any())
            } returns CreateWalletBillCategoryError.AlreadyExists.left()

            val result = controller.create(
                walletId = walletId.value,
                body = walletBillCategoryTO,
            )

            result.status() shouldBe HttpStatus.BAD_REQUEST
            with(result.getBody(ResponseTO::class.java).get()) {
                this.code shouldBe "4001"
                this.message shouldBe "AlreadyExists"
            }
        }

        @ParameterizedTest
        @ValueSource(booleans = [true, false])
        fun `deve retornar CREATED quando conseguir criar a categoria`(defaultCategory: Boolean) {
            every {
                service.create(any(), any(), any())
            } answers {
                WalletBillCategory(
                    walletId = firstArg(),
                    categoryId = PFMCategoryId(),
                    name = secondArg(),
                    icon = thirdArg(),
                    enabled = true,
                    default = defaultCategory,
                ).right()
            }

            val result = controller.create(
                walletId = walletId.value,
                body = walletBillCategoryTO,
            )

            result.status() shouldBe HttpStatus.CREATED
            with(result.getBody(WalletBillCategoryTO::class.java).get()) {
                this.name shouldBe walletBillCategoryTO.name
                this.icon shouldBe walletBillCategoryTO.icon
                this.enabled shouldBe true
                this.default shouldBe defaultCategory
            }

            verify {
                service.create(
                    walletId = walletId,
                    name = walletBillCategoryTO.name,
                    icon = walletBillCategoryTO.icon,
                )
            }
        }
    }
}