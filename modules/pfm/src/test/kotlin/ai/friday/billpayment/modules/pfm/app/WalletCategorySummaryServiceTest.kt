package ai.friday.billpayment.modules.pfm.app

import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.notification.ByteArrayWithNameAndType
import ai.friday.billpayment.app.pfm.BillCategory
import ai.friday.billpayment.app.pfm.CategorySummary
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.SummaryEntry
import ai.friday.billpayment.app.pfm.SummaryEntryType
import ai.friday.billpayment.app.pfm.SummaryService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.integration.WalletFixture
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDate
import java.time.Month
import java.time.Year
import org.junit.jupiter.api.Test

class WalletCategorySummaryServiceTest {
    private val accountService = mockk<AccountService>(relaxed = true)
    private val walletService = mockk<WalletService>(relaxed = true)
    private val messagePublisher = mockk<MessagePublisher>(relaxed = true)
    private val notificationAdapter = mockk<NotificationAdapter>(relaxed = true)

    private val summaryService1 = mockk<SummaryService>()
    private val summaryService2 = mockk<SummaryService>()

    private val service =
        object : WalletCategorySummaryService(
            accountService = accountService,
            walletService = walletService,
            sqsMessagePublisher = messagePublisher,
            notificationAdapter = notificationAdapter,
            queueName = "test-queue",
            displayName = "Friday",
        ) {
            override fun getServices(): List<SummaryService> {
                return listOf(summaryService1, summaryService2)
            }
        }

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    val educacao =
        BillCategory(
            categoryId = PFMCategoryId(value = "1"),
            name = "educacao",
            icon = "EDUCACAO",
            default = false,
        )
    val alimentacao =
        BillCategory(
            categoryId = PFMCategoryId(value = "2"),
            name = "alimentacao",
            icon = "ALIMENTACAO",
            default = false,
        )

    @Test
    fun `deve retornar o total pago em contas sem categoria no periodo`() {
        every {
            summaryService1.generateSummaryList(wallet.id, wallet.founder, Year.of(2024), Month.JANUARY)
        } returns
            listOf(
                SummaryEntry(
                    category = null,
                    totalAmount = 100_00,
                    title = "AMERICANAS",
                    description = "ACTIVE BILL",
                    date = LocalDate.of(2024, Month.JANUARY, 1),
                    type = SummaryEntryType.PAYMENT,
                    entryType = "Boleto",
                ),
                SummaryEntry(
                    category = null,
                    totalAmount = 200_00,
                    title = "AMERICANAS",
                    description = "ACTIVE BILL",
                    date = LocalDate.of(2024, Month.JANUARY, 2),
                    type = SummaryEntryType.PAYMENT,
                    entryType = "Boleto",
                ),
                SummaryEntry(
                    category = null,
                    totalAmount = 300_00,
                    title = "AMERICANAS",
                    description = "ACTIVE BILL",
                    date = LocalDate.of(2024, Month.JANUARY, 3),
                    type = SummaryEntryType.PAYMENT,
                    entryType = "Boleto",
                ),
            )

        every {
            summaryService2.generateSummaryList(wallet.id, wallet.founder, Year.of(2024), Month.JANUARY)
        } returns
            listOf(
                SummaryEntry(
                    category = null,
                    totalAmount = 100_00,
                    title = "AMERICANAS",
                    description = "REMINDER",
                    date = LocalDate.of(2024, Month.JANUARY, 1),
                    type = SummaryEntryType.REMINDER,
                    entryType = "Lançamento manual",
                ),
                SummaryEntry(
                    category = null,
                    totalAmount = 200_00,
                    title = "AMERICANAS",
                    description = "REMINDER",
                    date = LocalDate.of(2024, Month.JANUARY, 2),
                    type = SummaryEntryType.REMINDER,
                    entryType = "Lançamento manual",
                ),
            )

        val result = service.getSummary(wallet.id, walletFixture.founder, Year.of(2024), Month.JANUARY)
        result shouldContainExactlyInAnyOrder
            listOf(
                CategorySummary(
                    category = null,
                    totalExpenseAmount = 900_00L,
                    totalIncomeAmount = 0,
                    billCount = 3,
                    reminderCount = 2,
                    incomeCount = 0,
                ),
            )
    }

    @Test
    fun `deve retornar uma lista vazia se não houver nenhum pagamento daquele período`() {
        every {
            summaryService1.generateSummaryList(wallet.id, wallet.founder, Year.of(2024), Month.JANUARY)
        } returns emptyList()

        every {
            summaryService2.generateSummaryList(wallet.id, wallet.founder, Year.of(2024), Month.JANUARY)
        } returns emptyList()

        val result = service.getSummary(wallet.id, walletFixture.founder, Year.of(2024), Month.JANUARY)
        result.shouldContainExactly(listOf(CategorySummary(category = null, totalExpenseAmount = 0, totalIncomeAmount = 0, billCount = 0, reminderCount = 0, incomeCount = 0)))
    }

    @Test
    fun `deve retornar os valores em cada categoria corretamente`() {
        every {
            summaryService1.generateSummaryList(wallet.id, wallet.founder, Year.of(2024), Month.JANUARY)
        } returns
            listOf(
                SummaryEntry(
                    category = null,
                    totalAmount = 600_00,
                    title = "AMERICANAS",
                    description = "ACTIVE BILL",
                    date = LocalDate.of(2024, Month.JANUARY, 1),
                    type = SummaryEntryType.PAYMENT,
                    entryType = "Boleto",
                ),
                SummaryEntry(
                    category = educacao,
                    totalAmount = 200_00,
                    title = "AMERICANAS",
                    description = "ACTIVE BILL",
                    date = LocalDate.of(2024, Month.JANUARY, 2),
                    type = SummaryEntryType.PAYMENT,
                    entryType = "Boleto",
                ),
                SummaryEntry(
                    category = alimentacao,
                    totalAmount = 300_00,
                    title = "AMERICANAS",
                    description = "ACTIVE BILL",
                    date = LocalDate.of(2024, Month.JANUARY, 3),
                    type = SummaryEntryType.PAYMENT,
                    entryType = "Boleto",
                ),
                SummaryEntry(
                    category = alimentacao,
                    totalAmount = 400_00,
                    title = "AMERICANAS",
                    description = "ACTIVE BILL",
                    date = LocalDate.of(2024, Month.JANUARY, 3),
                    type = SummaryEntryType.PAYMENT,
                    entryType = "Boleto",
                ),
            )

        every {
            summaryService2.generateSummaryList(wallet.id, wallet.founder, Year.of(2024), Month.JANUARY)
        } returns
            listOf(
                SummaryEntry(
                    category = educacao,
                    totalAmount = 100_00,
                    title = "AMERICANAS",
                    description = "REMINDER",
                    date = LocalDate.of(2024, Month.JANUARY, 1),
                    type = SummaryEntryType.REMINDER,
                    entryType = "Lançamento manual",
                ),
                SummaryEntry(
                    category = educacao,
                    totalAmount = 200_00,
                    title = "AMERICANAS",
                    description = "REMINDER",
                    date = LocalDate.of(2024, Month.JANUARY, 1),
                    type = SummaryEntryType.REMINDER,
                    entryType = "Lançamento manual",
                ),
                SummaryEntry(
                    category = alimentacao,
                    totalAmount = 200_00,
                    title = "AMERICANAS",
                    description = "REMINDER",
                    date = LocalDate.of(2024, Month.JANUARY, 2),
                    type = SummaryEntryType.REMINDER,
                    entryType = "Lançamento manual",
                ),
            )

        val result = service.getSummary(wallet.id, walletFixture.founder, Year.of(2024), Month.JANUARY)
        result shouldContainExactly
            listOf(
                CategorySummary(
                    category = alimentacao,
                    totalExpenseAmount = 900_00L,
                    totalIncomeAmount = 0,
                    billCount = 2,
                    reminderCount = 1,
                    incomeCount = 0,
                ),
                CategorySummary(
                    category = educacao,
                    totalExpenseAmount = 500_00L,
                    totalIncomeAmount = 0,
                    billCount = 1,
                    reminderCount = 2,
                    incomeCount = 0,
                ),
                CategorySummary(
                    category = null,
                    totalExpenseAmount = 600_00L,
                    totalIncomeAmount = 0,
                    billCount = 1,
                    reminderCount = 0,
                    incomeCount = 0,
                ),
            )
    }

    @Test
    fun `deve juntar as entradas e as saidas, na mesma categoria`() {
        every {
            summaryService1.generateSummaryList(wallet.id, wallet.founder, Year.of(2024), Month.JANUARY)
        } returns
            listOf(
                SummaryEntry(
                    category = null,
                    totalAmount = 100_00,
                    title = "AMERICANAS",
                    description = "ACTIVE BILL",
                    date = LocalDate.of(2024, Month.JANUARY, 1),
                    type = SummaryEntryType.PAYMENT,
                    entryType = "Boleto",
                ),
                SummaryEntry(
                    category = educacao,
                    totalAmount = 200_00,
                    title = "AMERICANAS",
                    description = "ACTIVE BILL",
                    date = LocalDate.of(2024, Month.JANUARY, 2),
                    type = SummaryEntryType.PAYMENT,
                    entryType = "Boleto",
                ),
                SummaryEntry(
                    category = alimentacao,
                    totalAmount = 300_00,
                    title = "AMERICANAS",
                    description = "ACTIVE BILL",
                    date = LocalDate.of(2024, Month.JANUARY, 3),
                    type = SummaryEntryType.PAYMENT,
                    entryType = "Boleto",
                ),
                SummaryEntry(
                    category = alimentacao,
                    totalAmount = 400_00,
                    title = "VALE ALIMENTAÇÃO",
                    description = "VALE ALIMENTAÇÃO RECEBIDO",
                    date = LocalDate.of(2024, Month.JANUARY, 3),
                    type = SummaryEntryType.INCOME,
                    entryType = "Lançamento manual",
                ),
            )

        every {
            summaryService2.generateSummaryList(wallet.id, wallet.founder, Year.of(2024), Month.JANUARY)
        } returns
            listOf(
                SummaryEntry(
                    category = educacao,
                    totalAmount = 100_00,
                    title = "AMERICANAS",
                    description = "REMINDER",
                    date = LocalDate.of(2024, Month.JANUARY, 1),
                    type = SummaryEntryType.REMINDER,
                    entryType = "Lançamento manual",
                ),
                SummaryEntry(
                    category = educacao,
                    totalAmount = 200_00,
                    title = "AMERICANAS",
                    description = "REMINDER",
                    date = LocalDate.of(2024, Month.JANUARY, 1),
                    type = SummaryEntryType.REMINDER,
                    entryType = "Lançamento manual",
                ),
                SummaryEntry(
                    category = alimentacao,
                    totalAmount = 200_00,
                    title = "AMERICANAS",
                    description = "REMINDER",
                    date = LocalDate.of(2024, Month.JANUARY, 2),
                    type = SummaryEntryType.REMINDER,
                    entryType = "Lançamento manual",
                ),
                SummaryEntry(
                    category = null,
                    totalAmount = 500_00,
                    title = "AMERICANAS",
                    description = "REMINDER",
                    date = LocalDate.of(2024, Month.JANUARY, 2),
                    type = SummaryEntryType.INCOME,
                    entryType = "Lançamento manual",
                ),
            )

        val result = service.getSummary(wallet.id, walletFixture.founder, Year.of(2024), Month.JANUARY)

        result shouldContainExactly
            listOf(
                CategorySummary(
                    category = alimentacao,
                    totalExpenseAmount = 500_00L,
                    totalIncomeAmount = 400_00L,
                    billCount = 1,
                    reminderCount = 1,
                    incomeCount = 1,
                ),
                CategorySummary(
                    category = educacao,
                    totalExpenseAmount = 500_00L,
                    totalIncomeAmount = 0,
                    billCount = 1,
                    reminderCount = 2,
                    incomeCount = 0,
                ),
                CategorySummary(
                    category = null,
                    totalExpenseAmount = 100_00L,
                    totalIncomeAmount = 500_00L,
                    billCount = 1,
                    reminderCount = 0,
                    incomeCount = 1,
                ),
            )
    }

    @Test
    fun `deve gerar a categoria padrão se não houver elementos`() {
        every {
            summaryService1.generateSummaryList(any(), any(), any(), any())
        } returns emptyList()

        every {
            summaryService2.generateSummaryList(any(), any(), any(), any())
        } returns emptyList()

        val result = service.getSummary(wallet.id, walletFixture.founder, Year.of(2024), Month.JANUARY)

        result.size shouldBe 1
        result[0].category shouldBe null
        result[0].totalExpenseAmount shouldBe 0
        result[0].totalIncomeAmount shouldBe 0
    }

    @Test
    fun `ao requisitar envio do relatório, deve mandar mensagem para a fila`() {
        service.requestSummaryCsv(wallet.id, walletFixture.founder.accountId, "2024", "1")

        verify {
            messagePublisher.sendMessage(
                "test-queue",
                RequestSummaryCsvTO(
                    accountId = walletFixture.founder.accountId.value,
                    walletId = wallet.id.value,
                    year = "2024",
                    month = "1",
                ),
            )
        }
    }

    @Test
    fun `deve enviar email com o relatório`() {
        val category =
            BillCategory(
                categoryId = PFMCategoryId(value = "1"),
                name = "Categoria 1",
                icon = "EDUCACAO",
                default = false,
            )

        every { walletService.findWalletOrNull(any()) } returns wallet
        every { accountService.findAccountById(any()) } returns walletFixture.founderAccount

        every {
            summaryService1.generateSummaryList(wallet.id, wallet.founder, Year.of(2024), Month.JANUARY)
        } returns
            listOf(
                SummaryEntry(
                    category = null,
                    totalAmount = 200_00,
                    title = "AMERICANAS",
                    description = "ACTIVE BILL",
                    date = LocalDate.of(2024, 1, 1),
                    type = SummaryEntryType.PAYMENT,
                    entryType = "Boleto",
                ),
            )

        every {
            summaryService2.generateSummaryList(wallet.id, wallet.founder, Year.of(2024), Month.JANUARY)
        } returns
            listOf(
                SummaryEntry(
                    category = category,
                    totalAmount = 150_00,
                    title = "Lançamento manual",
                    description = "",
                    date = LocalDate.of(2024, Month.JANUARY, 5),
                    type = SummaryEntryType.PAYMENT,
                    entryType = "Lançamento manual",
                ),
            )

        val result = service.sendSummaryCsvByEmail(wallet.founder.accountId, wallet.id, Year.of(2024), Month.JANUARY)

        result.isRight() shouldBe true

        val attatchmentSlot = slot<List<ByteArrayWithNameAndType>>()

        verify {
            notificationAdapter.notifyWalletSummary(
                periodMessage = "01/2024",
                emailAddress = walletFixture.founderAccount.emailAddress,
                name = walletFixture.founderAccount.name,
                files = capture(attatchmentSlot),
            )
        }

        attatchmentSlot.captured.size shouldBe 1

        with(attatchmentSlot.captured[0]) {
            fileName shouldBe "Resumo_Friday_2024_1.csv"
            mediaType shouldBe "text/csv;charset=UTF-16"
            String(data, Charsets.UTF_16) shouldBe """data,nome,descricao,tipo,valor,categoria
01/01/2024,AMERICANAS,"ACTIVE BILL",Boleto,"200,00",
05/01/2024,"Lançamento manual",,"Lançamento manual","150,00","Categoria 1"
"""
        }
    }
}