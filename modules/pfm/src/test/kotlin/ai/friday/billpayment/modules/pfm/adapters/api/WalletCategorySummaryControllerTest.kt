package ai.friday.billpayment.modules.pfm.adapters.api

import ai.friday.billpayment.adapters.api.getWallet
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.pfm.app.WalletCategorySummaryService
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpStatus
import io.micronaut.security.authentication.Authentication
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test

class WalletCategorySummaryControllerTest {

    private val summaryService = mockk<WalletCategorySummaryService> {
        every { getSummary(any(), any(), any(), any()) } returns listOf()
    }
    private val controller = WalletCategorySummaryController(summaryService = summaryService)
    private val participant = WalletFixture().ultraLimitedParticipant
    private val wallet = WalletFixture().buildWallet(otherMembers = listOf(participant))
    private val auth = mockk<Authentication>() {
        every { getWallet() } returns wallet
        every { name } returns wallet.founder.accountId.value
    }

    @Test
    fun `deve gerar relatorio se usuario tiver permissao de ver todas as contas`() {
        val response = controller.generateSummary(body = SummaryRequestTO(2021, 1), walletId = "123", authentication = auth)
        response.status shouldBe HttpStatus.OK
    }
}