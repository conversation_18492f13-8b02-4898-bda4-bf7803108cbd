package ai.friday.billpayment.modules.pfm.app

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletBillCategoryDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletBillCategoryDynamoDAO
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.pfm.BillCategory
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.SummaryEntry
import ai.friday.billpayment.app.pfm.SummaryEntryType
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billCategoryAdded
import ai.friday.billpayment.billIgnored
import ai.friday.billpayment.billMarkedAsPaid
import ai.friday.billpayment.billMoved
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.billRegisterUpdatedNotPayable
import ai.friday.billpayment.integration.WalletFixture
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import java.time.LocalDate
import java.time.Month
import java.time.Year
import java.util.UUID
import org.junit.jupiter.api.Test

class BillSummaryServiceTest {
    val enhancedClient = setupDynamoDB()
    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(enhancedClient),
        refundedClient = RefundedBillDynamoDAO(enhancedClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(enhancedClient),
    )
    private val walletBillCategoryRepository = WalletBillCategoryDbRepository(client = WalletBillCategoryDynamoDAO(cli = setupDynamoDB()))
    private val service = BillSummaryService(repository = billRepository, walletBillCategoryDbRepository = walletBillCategoryRepository)

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    @Test
    fun `deve retornar apenas as bills que não estejam ignoradas ou tenham sido movidas`() {
        billRepository.save(buildBill(amountTotal = 100_00, effectiveDueDate = LocalDate.of(2024, Month.JANUARY, 1), billStatus = BillStatus.PAID))
        billRepository.save(buildBill(amountTotal = 200_00, effectiveDueDate = LocalDate.of(2024, Month.JANUARY, 2), billStatus = BillStatus.PAID))
        billRepository.save(buildBill(amountTotal = 100_00, effectiveDueDate = LocalDate.of(2024, Month.JANUARY, 4), billStatus = BillStatus.IGNORED))
        billRepository.save(buildBill(amountTotal = 200_00, effectiveDueDate = LocalDate.of(2024, Month.JANUARY, 2), billStatus = BillStatus.WAITING_APPROVAL))
        billRepository.save(buildBill(amountTotal = 200_00, effectiveDueDate = LocalDate.of(2024, Month.JANUARY, 2), billStatus = BillStatus.ACTIVE))
        billRepository.save(buildBill(amountTotal = 200_00, effectiveDueDate = LocalDate.of(2024, Month.JANUARY, 4), billStatus = BillStatus.PROCESSING))
        billRepository.save(buildBill(amountTotal = 100_00, effectiveDueDate = LocalDate.of(2024, Month.JANUARY, 2), billStatus = BillStatus.ALREADY_PAID))
        billRepository.save(buildBill(amountTotal = 300_00, effectiveDueDate = LocalDate.of(2024, Month.JANUARY, 10), billStatus = BillStatus.NOT_PAYABLE))
        billRepository.save(buildBill(amountTotal = 100_00, effectiveDueDate = LocalDate.of(2024, Month.JANUARY, 5), billStatus = BillStatus.MOVED))
        billRepository.save(buildBill(amountTotal = 0, effectiveDueDate = LocalDate.of(2024, Month.JANUARY, 3), billStatus = BillStatus.WAITING_BENEFICIARY_UPDATE))

        val result = service.generateSummaryList(wallet.id, walletFixture.founder, Year.of(2024), Month.JANUARY)
        result.size shouldBe 8

        result.sumOf {
            it.totalAmount
        } shouldBe 1300_00L
    }

    @Test
    fun `deve retornar um resumo do periodo com as bills`() {
        val educacao =
            BillCategory(
                categoryId = PFMCategoryId(value = "1"),
                name = "Categoria 1",
                icon = "EDUCACAO",
                default = false,
            )
        val alimentacao =
            BillCategory(
                categoryId = PFMCategoryId(value = "2"),
                name = "Categoria 2",
                icon = "ALIMENTACAO",
                default = false,
            )

        listOf(educacao, alimentacao).forEach {
            walletBillCategoryRepository.save(
                WalletBillCategory(walletId = wallet.id, categoryId = it.categoryId, name = it.name, icon = it.icon, enabled = true, default = it.default),
            )
        }

        billRepository.save(buildBill(category = educacao, amountTotal = 100_00, effectiveDueDate = LocalDate.of(2024, Month.JANUARY, 1), billStatus = BillStatus.ACTIVE))
        billRepository.save(buildBill(amountTotal = 200_00, effectiveDueDate = LocalDate.of(2024, Month.JANUARY, 2), billStatus = BillStatus.ACTIVE))
        billRepository.save(buildBill(amountTotal = 300_00, effectiveDueDate = LocalDate.of(2024, Month.JANUARY, 3), billStatus = BillStatus.PAID))
        billRepository.save(buildBill(category = alimentacao, amountTotal = 200_00, effectiveDueDate = LocalDate.of(2024, Month.JANUARY, 5), billStatus = BillStatus.PAID))

        billRepository.save(buildBill(category = educacao, amountTotal = 100_00, effectiveDueDate = LocalDate.of(2024, Month.FEBRUARY, 1), billStatus = BillStatus.ACTIVE))
        billRepository.save(buildBill(category = alimentacao, amountTotal = 200_00, effectiveDueDate = LocalDate.of(2023, Month.JANUARY, 2), billStatus = BillStatus.PAID))

        val result = service.generateSummaryList(wallet.id, walletFixture.founder, Year.of(2024), Month.JANUARY)

        result.size shouldBe 4

        result shouldContainExactlyInAnyOrder listOf(
            SummaryEntry(
                category = educacao,
                totalAmount = 100_00,
                title = "AMERICANAS",
                description = "ACTIVE BILL",
                date = LocalDate.of(2024, Month.JANUARY, 1),
                type = SummaryEntryType.PAYMENT,
                entryType = "Boleto",
            ),
            SummaryEntry(
                category = null,
                totalAmount = 200_00,
                title = "AMERICANAS",
                description = "ACTIVE BILL",
                date = LocalDate.of(2024, Month.JANUARY, 2),
                type = SummaryEntryType.PAYMENT,
                entryType = "Boleto",
            ),
            SummaryEntry(
                category = null,
                totalAmount = 300_00,
                title = "AMERICANAS",
                description = "ACTIVE BILL",
                date = LocalDate.of(2024, Month.JANUARY, 3),
                type = SummaryEntryType.PAYMENT,
                entryType = "Boleto",
            ),
            SummaryEntry(
                category = alimentacao,
                totalAmount = 200_00,
                title = "AMERICANAS",
                description = "ACTIVE BILL",
                date = LocalDate.of(2024, Month.JANUARY, 5),
                type = SummaryEntryType.PAYMENT,
                entryType = "Boleto",
            ),
        )
    }

    @Test
    fun `deve retornar somente com as contas visíveis pelo usuário`() {
        val founderSource = ActionSource.Api(walletFixture.founder.accountId)
        val limitedParticipantSource = ActionSource.Api(walletFixture.ultraLimitedParticipant.accountId)

        billRepository.save(buildBill(actionSource = founderSource, category = null, amountTotal = 100_00, effectiveDueDate = LocalDate.of(2024, Month.JANUARY, 1), billStatus = BillStatus.ACTIVE))
        billRepository.save(buildBill(actionSource = limitedParticipantSource, amountTotal = 200_00, effectiveDueDate = LocalDate.of(2024, Month.JANUARY, 2), billStatus = BillStatus.ACTIVE))

        val result = service.generateSummaryList(wallet.id, walletFixture.ultraLimitedParticipant, Year.of(2024), Month.JANUARY)

        result shouldBe listOf(
            SummaryEntry(
                category = null,
                totalAmount = 200_00,
                title = "AMERICANAS",
                description = "ACTIVE BILL",
                date = LocalDate.of(2024, Month.JANUARY, 2),
                type = SummaryEntryType.PAYMENT,
                entryType = "Boleto",
            ),
        )
    }

    private fun buildBill(
        effectiveDueDate: LocalDate,
        amountTotal: Long,
        category: BillCategory? = null,
        billStatus: BillStatus,
        actionSource: ActionSource? = null,
    ): Bill {
        val billId = BillId("BILL-${UUID.randomUUID()}")

        var firstBillEvent =
            Bill.build(
                billAddedFicha.copy(
                    walletId = wallet.id,
                    billId = billId,
                    effectiveDueDate = effectiveDueDate,
                    amountTotal = amountTotal,
                    actionSource = actionSource ?: billAddedFicha.actionSource,
                ),
            )

        val lastBillEvent: BillEvent? =
            when (billStatus) {
                BillStatus.WAITING_APPROVAL -> {
                    firstBillEvent =
                        Bill.build(
                            billAdded.copy(
                                walletId = wallet.id,
                                billId = billId,
                                effectiveDueDate = effectiveDueDate,
                                amountTotal = amountTotal,
                                securityValidationResult = listOf("teste"),
                            ),
                        )
                    null
                }

                BillStatus.ACTIVE -> null
                BillStatus.PAID_ON_PARTNER -> {
                    throw IllegalStateException("PAID_ON_PARTNER nunca é atribuído a uma bill no momento da criação")
                }

                BillStatus.WAITING_BENEFICIARY_UPDATE -> {
                    firstBillEvent =
                        Bill.build(
                            billAdded.copy(
                                walletId = wallet.id,
                                billId = billId,
                                amountTotal = amountTotal,
                                effectiveDueDate = effectiveDueDate,
                                amountCalculationModel = AmountCalculationModel.BENEFICIARY_ONLY,
                            ),
                        )
                    null
                }

                BillStatus.PAID -> billPaid.copy(walletId = wallet.id, billId = billId)
                BillStatus.IGNORED -> billIgnored.copy(walletId = wallet.id, billId = billId)
                BillStatus.PROCESSING -> billPaymentStart.copy(walletId = wallet.id, billId = billId)
                BillStatus.ALREADY_PAID -> billMarkedAsPaid.copy(walletId = wallet.id, billId = billId)
                BillStatus.NOT_PAYABLE -> billRegisterUpdatedNotPayable.copy(walletId = wallet.id, billId = billId)
                BillStatus.MOVED -> billMoved.copy(walletId = wallet.id, billId = billId)
            }

        return firstBillEvent.also { bill ->
            category?.let { bill.apply(billCategoryAdded.copy(walletId = wallet.id, billId = billId, categoryId = it.categoryId)) }
        }.also { bill ->
            lastBillEvent?.let { bill.apply(it) }
        }
    }
}