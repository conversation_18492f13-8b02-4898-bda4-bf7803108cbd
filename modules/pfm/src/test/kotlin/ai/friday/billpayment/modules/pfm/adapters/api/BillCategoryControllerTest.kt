package ai.friday.billpayment.modules.pfm.adapters.api

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.pfm.PFMBillCategoryService
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.RemoveBillCategoryError
import ai.friday.billpayment.app.pfm.SetBillCategoryError
import ai.friday.billpayment.app.recurrence.Range
import ai.friday.billpayment.integration.BILL_ID
import ai.friday.billpayment.modules.pfm.app.SetBillCategoryTO
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpStatus
import io.micronaut.security.authentication.Authentication
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import io.mockk.verifyAll
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class BillCategoryControllerTest {

    private val billCategoryService: PFMBillCategoryService = mockk()

    private val messagePublisher: MessagePublisher = mockk(relaxed = true)

    private val billCategoryController = BillCategoryController(
        billCategoryService = billCategoryService,
        messagePublisher = messagePublisher,
        configuration = mockk(relaxed = true),
    )

    private val billId = BillId(BILL_ID)
    private val billCategoryId = PFMCategoryId("EDUCACAO")
    private val authentication: Authentication = mockk() {
        every {
            name
        } returns ACCOUNT.accountId.value
    }

    @Nested
    @DisplayName("ao tentar aplicar uma categoria")
    inner class SetBillCategory {

        @Test
        fun `deve retornar server error se bill estiver lockada por outro processo e não deve aplicar a categoria nas bills similares`() {
            every {
                billCategoryService.setBillCategory(any(), any(), any(), any())
            } returns SetBillCategoryError.AlreadyLocked.left()

            val result = billCategoryController.setBillCategory(
                billId = billId.value,
                billCategoryId = billCategoryId.value,
                authentication = authentication,
                billCategoryRequestTO = BillCategoryRequestTO("THIS", listOf("1", "2", "3")),

            )

            result.status() shouldBe HttpStatus.INTERNAL_SERVER_ERROR
            val response = result.getBody(ResponseTO::class.java).get()

            response.code shouldBe "5000"
            response.message shouldBe "ServerError"

            verify(exactly = 0) {
                messagePublisher.sendMessage(
                    any(),
                    SetBillCategoryTO(
                        billId = "1",
                        billCategoryId = "EDUCACAO",
                        accountId = ACCOUNT.accountId.value,
                    ),
                )
                messagePublisher.sendMessage(
                    any(),
                    SetBillCategoryTO(
                        billId = "2",
                        billCategoryId = "EDUCACAO",
                        accountId = ACCOUNT.accountId.value,
                    ),
                )
                messagePublisher.sendMessage(
                    any(),
                    SetBillCategoryTO(
                        billId = "3",
                        billCategoryId = "EDUCACAO",
                        accountId = ACCOUNT.accountId.value,
                    ),
                )
            }
        }

        @Test
        fun `deve retornar server error se aconteceu um erro inesperado e não deve aplicar a categoria nas bills similares`() {
            every {
                billCategoryService.setBillCategory(any(), any(), any(), any())
            } returns SetBillCategoryError.ServerError.left()

            val result = billCategoryController.setBillCategory(
                billId = billId.value,
                billCategoryId = billCategoryId.value,
                authentication = authentication,
                billCategoryRequestTO = BillCategoryRequestTO("THIS", listOf("1", "2", "3")),

            )

            result.status() shouldBe HttpStatus.INTERNAL_SERVER_ERROR
            val response = result.getBody(ResponseTO::class.java).get()

            response.code shouldBe "5000"
            response.message shouldBe "ServerError"

            verify(exactly = 0) {
                messagePublisher.sendMessage(
                    any(),
                    SetBillCategoryTO(
                        billId = "1",
                        billCategoryId = "EDUCACAO",
                        accountId = ACCOUNT.accountId.value,
                    ),
                )
                messagePublisher.sendMessage(
                    any(),
                    SetBillCategoryTO(
                        billId = "2",
                        billCategoryId = "EDUCACAO",
                        accountId = ACCOUNT.accountId.value,
                    ),
                )
                messagePublisher.sendMessage(
                    any(),
                    SetBillCategoryTO(
                        billId = "3",
                        billCategoryId = "EDUCACAO",
                        accountId = ACCOUNT.accountId.value,
                    ),
                )
            }
        }

        @Test
        fun `deve retornar not found se nao encontrou a bill e não deve aplicar a categoria nas bills similares`() {
            every {
                billCategoryService.setBillCategory(any(), any(), any(), any())
            } returns SetBillCategoryError.BillNotFound.left()

            val result = billCategoryController.setBillCategory(
                billId = billId.value,
                billCategoryId = billCategoryId.value,
                authentication = authentication,
                billCategoryRequestTO = BillCategoryRequestTO("THIS", listOf("1", "2", "3")),
            )

            result.status() shouldBe HttpStatus.NOT_FOUND
            val response = result.getBody(ResponseTO::class.java).get()

            response.code shouldBe "4041"
            response.message shouldBe "BillNotFound"

            verify(exactly = 0) {
                messagePublisher.sendMessage(
                    any(),
                    SetBillCategoryTO(
                        billId = "1",
                        billCategoryId = "EDUCACAO",
                        accountId = ACCOUNT.accountId.value,
                    ),
                )
                messagePublisher.sendMessage(
                    any(),
                    SetBillCategoryTO(
                        billId = "2",
                        billCategoryId = "EDUCACAO",
                        accountId = ACCOUNT.accountId.value,
                    ),
                )
                messagePublisher.sendMessage(
                    any(),
                    SetBillCategoryTO(
                        billId = "3",
                        billCategoryId = "EDUCACAO",
                        accountId = ACCOUNT.accountId.value,
                    ),
                )
            }
        }

        @Test
        fun `deve retornar not found se nao encontrou a categoria e não deve aplicar a categoria nas bills similares`() {
            every {
                billCategoryService.setBillCategory(any(), any(), any(), any())
            } returns SetBillCategoryError.CategoryNotFound.left()

            val result = billCategoryController.setBillCategory(
                billId = billId.value,
                billCategoryId = billCategoryId.value,
                authentication = authentication,
                billCategoryRequestTO = BillCategoryRequestTO("THIS", listOf("1", "2", "3")),
            )

            result.status() shouldBe HttpStatus.NOT_FOUND
            val response = result.getBody(ResponseTO::class.java).get()

            response.code shouldBe "4042"
            response.message shouldBe "CategoryNotFound"

            verify(exactly = 0) {
                messagePublisher.sendMessage(
                    any(),
                    SetBillCategoryTO(
                        billId = "1",
                        billCategoryId = "EDUCACAO",
                        accountId = ACCOUNT.accountId.value,
                    ),
                )
                messagePublisher.sendMessage(
                    any(),
                    SetBillCategoryTO(
                        billId = "2",
                        billCategoryId = "EDUCACAO",
                        accountId = ACCOUNT.accountId.value,
                    ),
                )
                messagePublisher.sendMessage(
                    any(),
                    SetBillCategoryTO(
                        billId = "3",
                        billCategoryId = "EDUCACAO",
                        accountId = ACCOUNT.accountId.value,
                    ),
                )
            }
        }

        @Test
        fun `deve retornar ok se conseguiu aplicar a categoria`() {
            every {
                billCategoryService.setBillCategory(any(), any(), any(), any())
            } returns Unit.right()

            val result = billCategoryController.setBillCategory(
                billId = billId.value,
                billCategoryId = billCategoryId.value,
                authentication = authentication,
                billCategoryRequestTO = BillCategoryRequestTO("THIS"),
            )

            result.status() shouldBe HttpStatus.OK

            val billIdSlot = slot<BillId>()
            val billCategoryIdSlot = slot<PFMCategoryId>()
            val accountIdSlot = slot<AccountId>()
            verify {
                billCategoryService.setBillCategory(
                    billId = capture(billIdSlot),
                    billCategoryId = capture(billCategoryIdSlot),
                    accountId = capture(accountIdSlot),
                    range = Range.THIS,
                )
            }
            billIdSlot.captured shouldBe billId
            billCategoryIdSlot.captured shouldBe billCategoryId
            accountIdSlot.captured shouldBe ACCOUNT.accountId
        }

        @Test
        fun `deve aplicar a recorrência caso não for nula`() {
            every {
                billCategoryService.setBillCategory(any(), any(), any(), any())
            } returns Unit.right()

            val result = billCategoryController.setBillCategory(
                billId = billId.value,
                billCategoryId = billCategoryId.value,
                authentication = authentication,
                billCategoryRequestTO = BillCategoryRequestTO("THIS_AND_FUTURE"),
            )

            result.status() shouldBe HttpStatus.OK

            val billIdSlot = slot<BillId>()
            val billCategoryIdSlot = slot<PFMCategoryId>()
            val accountIdSlot = slot<AccountId>()
            verify {
                billCategoryService.setBillCategory(
                    billId = capture(billIdSlot),
                    billCategoryId = capture(billCategoryIdSlot),
                    accountId = capture(accountIdSlot),
                    range = Range.THIS_AND_FUTURE,

                )
            }
            billIdSlot.captured shouldBe billId
            billCategoryIdSlot.captured shouldBe billCategoryId
            accountIdSlot.captured shouldBe ACCOUNT.accountId
        }

        @Test
        fun `deve retornar ok se conseguiu aplicar a categoria na bill e nas bills similares`() {
            every {
                billCategoryService.setBillCategory(any(), any(), any(), any())
            } returns Unit.right()

            every {
                billCategoryService.setBillCategory(BillId("2"), any(), any(), any())
            } returns SetBillCategoryError.BillNotFound.left()

            val result = billCategoryController.setBillCategory(
                billId = billId.value,
                billCategoryId = billCategoryId.value,
                authentication = authentication,
                billCategoryRequestTO = BillCategoryRequestTO("THIS_AND_FUTURE", listOf("1", "2", "3")),
            )

            result.status() shouldBe HttpStatus.OK

            verifyAll {
                billCategoryService.setBillCategory(
                    billId = billId,
                    billCategoryId = billCategoryId,
                    accountId = ACCOUNT.accountId,
                    range = Range.THIS_AND_FUTURE,
                )
                messagePublisher.sendMessage(
                    any(),
                    SetBillCategoryTO(
                        billId = "1",
                        billCategoryId = "EDUCACAO",
                        accountId = ACCOUNT.accountId.value,
                    ),
                )
                messagePublisher.sendMessage(
                    any(),
                    SetBillCategoryTO(
                        billId = "2",
                        billCategoryId = "EDUCACAO",
                        accountId = ACCOUNT.accountId.value,
                    ),
                )
                messagePublisher.sendMessage(
                    any(),
                    SetBillCategoryTO(
                        billId = "3",
                        billCategoryId = "EDUCACAO",
                        accountId = ACCOUNT.accountId.value,
                    ),
                )
            }
        }
    }

    @Nested
    @DisplayName("ao tentar remover uma categoria")
    inner class RemoveBillCategory {
        @Test
        fun `deve retornar server error se aconteceu um erro inesperado`() {
            every {
                billCategoryService.removeBillCategory(any(), any(), any())
            } returns RemoveBillCategoryError.ServerError.left()

            val result = billCategoryController.removeBillCategory(
                billId = billId.value,
                authentication = authentication,
            )

            result.status() shouldBe HttpStatus.INTERNAL_SERVER_ERROR
            val response = result.getBody(ResponseTO::class.java).get()

            response.code shouldBe "5000"
            response.message shouldBe "ServerError"
        }

        @Test
        fun `deve retornar not found se nao encontrou a bill`() {
            every {
                billCategoryService.removeBillCategory(any(), any(), any())
            } returns RemoveBillCategoryError.BillNotFound.left()

            val result = billCategoryController.removeBillCategory(
                billId = billId.value,
                authentication = authentication,
            )

            result.status() shouldBe HttpStatus.NOT_FOUND
            val response = result.getBody(ResponseTO::class.java).get()

            response.code shouldBe "4041"
            response.message shouldBe "BillNotFound"
        }

        @Test
        fun `deve retornar ok se conseguiu remover a categoria`() {
            every {
                billCategoryService.removeBillCategory(any(), any(), any())
            } returns Unit.right()

            val result = billCategoryController.removeBillCategory(
                billId = billId.value,
                authentication = authentication,
                billCategoryRequestTO = BillCategoryRequestTO("THIS"),
            )

            result.status() shouldBe HttpStatus.OK

            val billIdSlot = slot<BillId>()
            val accountIdSlot = slot<AccountId>()
            verify {
                billCategoryService.removeBillCategory(
                    billId = capture(billIdSlot),
                    accountId = capture(accountIdSlot),
                    range = Range.THIS,
                )
            }
            billIdSlot.captured shouldBe billId
            accountIdSlot.captured shouldBe ACCOUNT.accountId
        }
    }
}