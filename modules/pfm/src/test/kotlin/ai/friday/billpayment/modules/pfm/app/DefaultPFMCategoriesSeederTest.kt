package ai.friday.billpayment.modules.pfm.app

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.adapters.dynamodb.DefaultWalletBillCategoryDbRepository
import ai.friday.billpayment.adapters.dynamodb.DefaultWalletBillCategoryDynamoDAO
import ai.friday.billpayment.app.pfm.DefaultWalletBillCategory
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class DefaultPFMCategoriesSeederTest {
    private var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val defaultCategoryRepository = spyk(
        DefaultWalletBillCategoryDbRepository(
            DefaultWalletBillCategoryDynamoDAO(
                cli = dynamoDbEnhancedClient,
            ),
        ),
    )

    private val defaultCategories = listOf(
        DefaultWalletBillCategory(
            categoryId = PFMCategoryId(value = "CARTAO"),
            name = "Cartão",
            icon = "CARTAO",
        ),
        DefaultWalletBillCategory(
            categoryId = PFMCategoryId(value = "CASA"),
            name = "Casa",
            icon = "CASA",
        ),
    )

    private val mockedSeederService = mockk<PFMDefaultCategoriesSeederService> {
        every {
            listDefaultCategories()
        } returns defaultCategories
    }

    private val seederService = DefaultPFMCategoriesSeeder(service = mockedSeederService, defaultWalletBillCategoryRepository = defaultCategoryRepository)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `deve salvar as categorias padrão no banco caso não houver`() {
        seederService.loadData()

        val categories = defaultCategoryRepository.findAll()

        verify(exactly = 0) {
            defaultCategoryRepository.delete(any())
        }

        categories.size shouldBe 2
    }

    @Test
    fun `deve salvar as novas categorias e apagar as antigas caso a lista mude`() {
        val category = DefaultWalletBillCategory(
            categoryId = PFMCategoryId(value = "adversarium"),
            name = "Denver Humphrey",
            icon = "aliquid",
        )

        defaultCategoryRepository.save(category)

        seederService.loadData()

        val categories = defaultCategoryRepository.findAll()

        categories.size shouldBe 2
    }

    @Test
    fun `deve manter as categorias já existentes se elas não mudarem`() {
        defaultCategories.forEach {
            defaultCategoryRepository.save(it)
        }

        seederService.loadData()

        val categories = defaultCategoryRepository.findAll()

        verify(exactly = 0) {
            defaultCategoryRepository.delete(any())
        }

        categories.size shouldBe 2
    }
}