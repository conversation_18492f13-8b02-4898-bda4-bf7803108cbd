package ai.friday.billpayment.modules.pfm.adapters.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletBillCategoryDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletBillCategoryDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.integration.onWallet
import ai.friday.billpayment.modules.pfm.app.WalletBillCategoryService
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV, ME_POUPE_ENV])
class WalletBillCategoryControllerIntegrationTest(embeddedServer: EmbeddedServer) {

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val enhancedClient = getDynamoDB()

    private val wallet = WalletFixture().buildWallet()

    private lateinit var walletRepository: WalletDbRepository

    private lateinit var walletBillCategoryRepository: WalletBillCategoryDbRepository

    private lateinit var walletBillCategoryService: PFMWalletCategoryService

    private val categoryId = PFMCategoryId()

    private val category = WalletBillCategory(walletId = wallet.id, categoryId = categoryId, name = "Compras", icon = "CARRINHO_DE_COMPRAS", enabled = true, default = false)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        loadAccountIntoDb(dynamoDB)

        val walletDAO = WalletDynamoDAO(enhancedClient)
        val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
        val inviteDAO = InviteDynamoDAO(enhancedClient)
        val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
        val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

        walletRepository = WalletDbRepository(
            walletDAO = walletDAO,
            walletMemberDAO = walletMemberDAO,
            inviteDAO = inviteDAO,
            inviteReminderDAO = inviteReminderDAO,
            walletLimitDAO = walletLimitDAO,
            accountRepository = mockk(),
        )
        walletRepository.save(wallet)
        walletBillCategoryRepository = WalletBillCategoryDbRepository(WalletBillCategoryDynamoDAO(enhancedClient))
        walletBillCategoryService = WalletBillCategoryService(
            defaultWalletBillCategoryRepository = mockk(),
            walletBillCategoryRepository = walletBillCategoryRepository,
            lockProvider = mockk(relaxed = true),
            messagePublisher = mockk(),
            configuration = mockk(),
        )
    }

    @Test
    fun `deve fazer o update de uma categoria`() {
        walletBillCategoryRepository.save(category)
        val updateBody = mapOf("name" to "Assinaturas", "icon" to "MALETA", "enabled" to false)
        val request = HttpRequest.PUT("/wallet/${wallet.id.value}/category/${category.categoryId.value}", updateBody)
            .onWallet(wallet = wallet)

        val response = client.toBlocking().exchange(request, Argument.of(UpdateWalletBillCategoryResponseTO::class.java))

        response.status() shouldBe HttpStatus.OK
        response.body().shouldNotBeNull()

        with(response.body()) {
            name shouldBe "Assinaturas"
            icon shouldBe "MALETA"
            enabled shouldBe false
        }
    }

    @Nested
    @DisplayName("ao tentar editar o nome de uma categoria")
    inner class EditCategoryNameTest {

        @Test
        fun `deve retornar erro de conflito para uma categoria já existente que não seja a que está sendo editada`() {
            walletBillCategoryRepository.save(category)
            val otherCategory = category.copy(categoryId = PFMCategoryId(), name = "Teste", enabled = false)
            walletBillCategoryRepository.save(otherCategory)

            val updateBody = mapOf("name" to "teste", "icon" to "MALETA", "enabled" to true)
            val request = HttpRequest.PUT("/wallet/${wallet.id.value}/category/${category.categoryId.value}", updateBody)
                .onWallet(wallet = wallet)

            val response = assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, Argument.of(UpdateWalletBillCategoryResponseTO::class.java), Argument.of(ResponseTO::class.java)) }

            response.status shouldBe HttpStatus.CONFLICT

            val updatedCategory = walletBillCategoryRepository.findByWalletIdAndBillCategoryId(wallet.id, categoryId)

            updatedCategory.shouldNotBeNull()
            with(updatedCategory) {
                name shouldBe category.name
                icon shouldBe category.icon
                enabled shouldBe category.enabled
            }
        }

        @Test
        fun `não deve retornar erro caso o nome seja da mesma categoria sendo editada`() {
            walletBillCategoryRepository.save(category)
            val updateBody = mapOf("name" to "Compras", "icon" to "MALETA", "enabled" to true)
            val request = HttpRequest.PUT("/wallet/${wallet.id.value}/category/${category.categoryId.value}", updateBody)
                .onWallet(wallet = wallet)

            val response = client.toBlocking().exchange(request, Argument.of(UpdateWalletBillCategoryResponseTO::class.java))

            response.status() shouldBe HttpStatus.OK
            response.body().shouldNotBeNull()

            with(response.body()) {
                name shouldBe "Compras"
                icon shouldBe "MALETA"
                enabled shouldBe category.enabled
            }
        }
    }
}