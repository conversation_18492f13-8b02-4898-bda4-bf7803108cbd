package ai.friday.billpayment.modules.pfm.adapters.api

import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.pfm.PFMBillCategoryService
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.RemoveBillCategoryError
import ai.friday.billpayment.app.pfm.SetBillCategoryError
import ai.friday.billpayment.app.recurrence.Range
import ai.friday.billpayment.modules.pfm.PFMModule
import ai.friday.billpayment.modules.pfm.app.SetBillCategoryTO
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpResponseFactory
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.OWNER)
@Controller("/bill/id/{billId}/category")
@PFMModule
@Version("2")
class BillCategoryController(
    private val configuration: SQSMessageHandlerConfiguration,
    private val billCategoryService: PFMBillCategoryService,
    private val messagePublisher: MessagePublisher,
) {
    private val logger = LoggerFactory.getLogger(BillCategoryController::class.java)

    @Put("/{billCategoryId}")
    fun setBillCategory(@PathVariable billId: String, @PathVariable billCategoryId: String, authentication: Authentication, @Body billCategoryRequestTO: BillCategoryRequestTO? = null): HttpResponse<*> {
        val accountId = authentication.toAccountId()

        val markers = Markers.append("billId", billId)
            .andAppend("billCategoryId", billCategoryId)
            .andAppend("accountId", accountId.value)
            .andAppend("billCategoryRequestTO", billCategoryRequestTO)

        billCategoryService.setBillCategory(
            billId = BillId(billId),
            billCategoryId = PFMCategoryId(billCategoryId),
            accountId = accountId,
            range = billCategoryRequestTO?.toRange(),
        ).getOrElse { setBillCategoryError ->
            val (responseTO, httpStatus) = when (setBillCategoryError) {
                SetBillCategoryError.BillNotFound -> ResponseTO(code = "4041", message = "BillNotFound") to HttpStatus.NOT_FOUND
                SetBillCategoryError.CategoryNotFound -> ResponseTO(code = "4042", message = "CategoryNotFound") to HttpStatus.NOT_FOUND
                SetBillCategoryError.AlreadyLocked,
                SetBillCategoryError.ServerError,
                -> ResponseTO(code = "5000", message = "ServerError") to HttpStatus.INTERNAL_SERVER_ERROR
            }
            markers.andAppend("response", responseTO)
            if (httpStatus == HttpStatus.INTERNAL_SERVER_ERROR) {
                logger.error(markers, "BillCategoryController#setBillCategory")
            } else {
                logger.warn(markers, "BillCategoryController#setBillCategory")
            }
            return HttpResponseFactory.INSTANCE.status<Any>(httpStatus).body(responseTO)
        }

        billCategoryRequestTO?.similarBills?.map { id ->
            messagePublisher.sendMessage(
                configuration.setBillCategoryQueueName,
                SetBillCategoryTO(
                    billId = id,
                    billCategoryId = billCategoryId,
                    accountId = accountId.value,
                ),
            )
        }

        logger.info(markers, "BillCategoryController#setBillCategory")
        return HttpResponse.ok<Unit>()
    }

    @Delete
    fun removeBillCategory(@PathVariable billId: String, authentication: Authentication, @Body billCategoryRequestTO: BillCategoryRequestTO? = null): HttpResponse<*> {
        val accountId = authentication.toAccountId()

        val markers = Markers.append("billId", billId)
            .andAppend("accountId", accountId.value)
            .andAppend("billCategoryRequestTO", billCategoryRequestTO)

        return billCategoryService.removeBillCategory(
            billId = BillId(billId),
            accountId = accountId,
            range = billCategoryRequestTO?.toRange(),
        ).map {
            logger.info(markers, "BillCategoryController#removeBillCategory")
            HttpResponse.ok<Unit>()
        }.getOrElse { setBillCategoryError ->
            val (responseTO, httpStatus) = when (setBillCategoryError) {
                RemoveBillCategoryError.BillNotFound -> ResponseTO(code = "4041", message = "BillNotFound") to HttpStatus.NOT_FOUND
                RemoveBillCategoryError.ServerError -> ResponseTO(code = "5000", message = "ServerError") to HttpStatus.INTERNAL_SERVER_ERROR
            }

            markers.andAppend("response", responseTO)
            if (httpStatus == HttpStatus.INTERNAL_SERVER_ERROR) {
                logger.error(markers, "BillCategoryController#removeBillCategory")
            } else {
                logger.warn(markers, "BillCategoryController#removeBillCategory")
            }
            return HttpResponseFactory.INSTANCE.status<Any>(httpStatus).body(responseTO)
        }
    }
}

data class BillCategoryRequestTO(val recurrenceRange: String?, val similarBills: List<String>? = null) {
    fun toRange() = recurrenceRange?.let { Range.valueOf(it) }
}