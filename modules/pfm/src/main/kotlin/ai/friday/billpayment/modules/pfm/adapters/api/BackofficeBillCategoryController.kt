package ai.friday.billpayment.modules.pfm.adapters.api

import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.billcategory.BillCategorySuggestion
import ai.friday.billpayment.app.billcategory.BillCategorySuggestionService
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.pfm.PFMBillCategoryService
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.recurrence.Range
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.modules.pfm.PFMModule
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@Secured(BACKOFFICE)
@Controller("/backoffice/category")
@PFMModule
class BackofficeBillCategoryController(
    private val billCategoryService: PFMBillCategoryService,
    private val billCategorySuggestionService: BillCategorySuggestionService,
    private val billEventRepository: BillEventRepository,
    private val walletService: WalletService,
    private val billRepository: BillRepository,
) {
    private val logger: Logger = LoggerFactory.getLogger(BackofficeBillCategoryController::class.java)

    @Post("/wallet/{walletId}/bill/{billId}/category/{billCategoryId}")
    fun addBillCategory(@PathVariable walletId: String, @PathVariable billId: String, @PathVariable billCategoryId: String): HttpResponse<*> {
        val markers = Markers.append("walletId", walletId)
            .andAppend("billId", billId)
            .andAppend("billCategoryId", billCategoryId)
        return try {
            val wallet = walletService.findWallet(WalletId(walletId))
            markers.andAppend("accountId", wallet.founder.accountId.value)

            billCategoryService.setBillCategory(
                billId = BillId(value = billId),
                billCategoryId = PFMCategoryId(value = billCategoryId),
                accountId = wallet.founder.accountId,
                range = Range.THIS, // TODO: deveria receber por parâmetro
            ).map {
                logger.info(markers, "BackofficeCategoryController#addBillCategory")
                HttpResponse.ok<Unit>()
            }.getOrElse {
                markers.andAppend("error", it)
                logger.error(markers, "BackofficeCategoryController#addBillCategory")
                HttpResponse.serverError(it)
            }
        } catch (e: Exception) {
            logger.error(markers, "BackofficeCategoryController#addBillCategory", e)
            HttpResponse.serverError<Unit>()
        }
    }

    @Delete("/wallet/{walletId}/bill/{billId}")
    fun removeBillCategory(@PathVariable walletId: String, @PathVariable billId: String): HttpResponse<*> {
        val markers = Markers.append("walletId", walletId)
            .andAppend("billId", billId)
        return try {
            val wallet = walletService.findWallet(WalletId(walletId))
            markers.andAppend("accountId", wallet.founder.accountId.value)

            billCategoryService.removeBillCategory(
                billId = BillId(value = billId),
                accountId = wallet.founder.accountId,
            ).map {
                logger.info(markers, "BackofficeCategoryController#removeBillCategory")
                HttpResponse.ok<Unit>()
            }.getOrElse {
                markers.andAppend("error", it)
                logger.error(markers, "BackofficeCategoryController#removeBillCategory")
                HttpResponse.serverError(it)
            }
        } catch (e: Exception) {
            logger.error(markers, "BackofficeCategoryController#removeBillCategory", e)
            HttpResponse.serverError<Unit>()
        }
    }

    @Post("/categorySuggestion")
    fun generateWalletBillsCategorySuggestion(@Body wallets: List<String>): HttpResponse<*> {
        val markers = Markers.append("walletsSize", wallets.size)

        wallets.map { it ->
            markers.andAppend("walletId", it)

            val wallet = walletService.findWallet(WalletId(it))

            markers.andAppend("accountId", wallet.founder.accountId.value)

            val bills = billRepository.findByWallet(wallet.id)

            markers.andAppend("billsSize", bills.size)

            val nonCategoryBills = bills.filter { it.categoryId == null }

            markers.andAppend("nonCategoryBillsSize", nonCategoryBills.size)

            nonCategoryBills.map {
                val categorySuggestions = billCategorySuggestionService.suggestCategories(it, bills)
                val filteredSuggestions = billCategorySuggestionService.filterCategories(categorySuggestions)
                billCategorySuggestionService.setBillCategorySuggestions(
                    billId = it.toBill().billId,
                    walletId = wallet.id,
                    categorySuggestions = filteredSuggestions,
                )
            }
        }

        logger.info(markers, "BackofficeCategoryController#generateWalletBillsCategorySuggestion")
        return HttpResponse.ok<Unit>()
    }

    @Post("/wallet/{walletId}/bill/{billId}/categorySuggestion")
    fun addBillCategorySuggestion(@PathVariable walletId: String, @PathVariable billId: String, @Body categorySuggestions: List<BillCategorySuggestion>): HttpResponse<*> {
        val markers = Markers.append("walletId", walletId)
            .andAppend("billId", billId)
            .andAppend("categorySuggestions", categorySuggestions)

        val wallet = walletService.findWallet(WalletId(walletId))
        markers.andAppend("accountId", wallet.founder.accountId.value)

        val bill = billEventRepository.getBillById(BillId(value = billId)).getOrElse {
            markers.andAppend("error", it)
            logger.error(markers, "BackofficeCategoryController#addBillCategorySuggestion")
            return HttpResponse.serverError(it)
        }

        markers.andAppend("bill", bill)

        billCategorySuggestionService.setBillCategorySuggestions(
            billId = bill.billId,
            walletId = wallet.id,
            categorySuggestions = categorySuggestions,
        )

        logger.info(markers, "BackofficeCategoryController#addBillCategorySuggestion")
        return HttpResponse.ok<Unit>()
    }

    private fun BillView.toBill() = billEventRepository.getBill(this.walletId, this.billId).getOrElse { throw it }
}