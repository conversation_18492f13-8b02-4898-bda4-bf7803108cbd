package ai.friday.billpayment.modules.pfm.app

import ai.friday.billpayment.app.pfm.DefaultWalletBillCategory
import ai.friday.billpayment.app.pfm.DefaultWalletBillCategoryRepository
import ai.friday.billpayment.modules.pfm.PFMModule
import jakarta.annotation.PostConstruct
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@PFMModule
class DefaultPFMCategoriesSeeder(private val service: PFMDefaultCategoriesSeederService, private val defaultWalletBillCategoryRepository: DefaultWalletBillCategoryRepository) {
    @PostConstruct
    fun loadData() {
        val logName = "DefaultPFMCategoriesSeeder#loadData"
        val currentCategories = defaultWalletBillCategoryRepository.findAll()
        val newCategories = service.listDefaultCategories()

        val allCategories = currentCategories + newCategories

        val categoriesHaveChanged = allCategories.groupBy { it.categoryId }.filter { it.value.size == 1 }.flatMap { it.value }.isNotEmpty()

        if (!categoriesHaveChanged) {
            logger.info("$logName#alreadyUpdated")
            return
        }

        newCategories.forEach {
            defaultWalletBillCategoryRepository.save(it)
        }

        if (currentCategories.isNotEmpty()) {
            currentCategories.forEach {
                if (!newCategories.contains(it)) {
                    logger.info(Markers.append("categoryId", it.categoryId), "$logName#categoryDeleted")
                    defaultWalletBillCategoryRepository.delete(it)
                }
            }
        }

        logger.info(Markers.append("newCategories", newCategories), logName)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(DefaultPFMCategoriesSeeder::class.java)
    }
}

interface PFMDefaultCategoriesSeederService {
    fun listDefaultCategories(): List<DefaultWalletBillCategory>
}