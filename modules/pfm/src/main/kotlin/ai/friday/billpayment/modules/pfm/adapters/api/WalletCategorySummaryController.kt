package ai.friday.billpayment.modules.pfm.adapters.api

import ai.friday.billpayment.adapters.api.BillCategoryTO
import ai.friday.billpayment.adapters.api.asWalletMember
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.pfm.BillCategory
import ai.friday.billpayment.app.pfm.SummaryError
import ai.friday.billpayment.app.statement.StatementError
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.pfm.PFMModule
import ai.friday.billpayment.modules.pfm.app.WalletCategorySummaryService
import ai.friday.morning.log.andAppend
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import java.time.Month
import java.time.Year
import java.time.format.DateTimeParseException
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.OWNER)
@Controller("/category-summary")
@Version("2")
@PFMModule
class WalletCategorySummaryController(
    private val summaryService: WalletCategorySummaryService,
) {
    private val logger = LoggerFactory.getLogger(WalletCategorySummaryController::class.java)

    @Post("/{walletId}")
    fun generateSummary(
        authentication: Authentication,
        @PathVariable walletId: String,
        @Body body: SummaryRequestTO,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val member = authentication.asWalletMember()
        val marker = Markers.append("accountId", accountId.value)
            .andAppend("walletId", walletId)
            .andAppend("memberType", member.type)
            .andAppend("year", body.year)
            .andAppend("month", body.month)
        return try {
            val result = summaryService.getSummary(WalletId(walletId), authentication.asWalletMember(), Year.of(body.year), Month.of(body.month))

            logger.info(marker, "WalletCategorySummaryController#generateSummary")
            HttpResponse.ok(
                result.map {
                    CategorySummaryTO(
                        category = it.category?.toBillCategoryTO(),
                        amountTotal = it.totalExpenseAmount,
                        amountExpenseTotal = it.totalExpenseAmount,
                        amountIncomeTotal = it.totalIncomeAmount,
                        billCount = it.billCount,
                        reminderCount = it.reminderCount,
                        incomeCount = it.incomeCount,
                    )
                },
            )
        } catch (e: Exception) {
            handleError(marker, e, "WalletCategorySummaryController#generateSummary")
        }
    }

    @Post("/{walletId}/csv")
    fun generateSummaryCsv(
        authentication: Authentication,
        @PathVariable walletId: String,
        @Body body: SummaryRequestTO,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val member = authentication.asWalletMember()
        val marker = Markers.append("accountId", accountId.value)
            .andAppend("walletId", walletId)
            .andAppend("memberType", member.type)
            .andAppend("year", body.year)
            .andAppend("month", body.month)
        return try {
            if (member.permissions.viewBills != BillPermission.ALL_BILLS) {
                logger.warn(marker, "WalletCategorySummaryController#generateSummaryCsv")
                return HttpResponse.status<Unit>(HttpStatus.FORBIDDEN)
            }

            val result = summaryService.requestSummaryCsv(WalletId(walletId), accountId, body.year.toString(), body.month.toString())

            result.mapLeft {
                logger.error(marker.andAppend("error", it), "WalletCategorySummaryController#generateSummaryCsv")

                return when (it) {
                    is SummaryError.WalletNotFound -> HttpResponse.notFound<Unit>()
                    is SummaryError.NotAllowed -> HttpResponse.status<Unit>(HttpStatus.FORBIDDEN)

                    is SummaryError.FailedToSendEmail,
                    is SummaryError.ServerError,
                    ->
                        HttpResponse.serverError<Unit>()
                }
            }

            logger.info(marker, "WalletCategorySummaryController#generateSummaryCsv")
            HttpResponse.noContent<Unit>()
        } catch (e: Exception) {
            handleError(marker, e, "WalletCategorySummaryController#generateSummaryCsv")
        }
    }

    private fun BillCategory.toBillCategoryTO(): BillCategoryTO {
        return BillCategoryTO(
            billCategoryId = this.categoryId.value,
            name = this.name,
            icon = this.icon,
            enabled = true, // FIXME:
        )
    }

    private fun handleError(markers: LogstashMarker, e: Exception, message: String): HttpResponse<*> {
        val httpStatus = when (e) {
            is DateTimeParseException -> HttpStatus.BAD_REQUEST
            is StatementError.NotAllowed -> HttpStatus.METHOD_NOT_ALLOWED

            is StatementError.ServerError,
            is StatementError.WalletNotFound,
            is StatementError.FailedToSendEmail,
            -> HttpStatus.INTERNAL_SERVER_ERROR

            else -> HttpStatus.INTERNAL_SERVER_ERROR
        }

        markers.andAppend("httpStatus", httpStatus.code).also {
            logger.error(it, message, e)
        }

        return HttpResponse.status<Unit>(httpStatus)
    }
}

data class SummaryRequestTO(
    val year: Int,
    val month: Int,
)

data class CategorySummaryTO(
    val category: BillCategoryTO?,
    @Deprecated("usar amountExpenseTotal") val amountTotal: Long,
    val amountExpenseTotal: Long,
    val amountIncomeTotal: Long,
    val billCount: Int,
    val reminderCount: Int,
    val incomeCount: Int,
)