package ai.friday.billpayment.modules.pfm.adapters.api

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.pfm.SummaryError
import ai.friday.billpayment.app.statement.StatementError
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.pfm.PFMModule
import ai.friday.billpayment.modules.pfm.app.WalletCategorySummaryService
import ai.friday.morning.log.andAppend
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import java.time.format.DateTimeParseException
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.BACKOFFICE)
@Controller("/backoffice/category-summary")
@Version("2")
@PFMModule
class BackofficeSummaryController(
    private val summaryService: WalletCategorySummaryService,
) {
    private val logger = LoggerFactory.getLogger(BackofficeSummaryController::class.java)

    @Post("/accountId/{accountId}/walletId/{walletId}/csv")
    fun generateSummaryCsv(
        @PathVariable walletId: String,
        @PathVariable accountId: String,
        @Body body: SummaryRequestTO,
    ): HttpResponse<*> {
        val marker = Markers.append("accountId", accountId)
            .andAppend("walletId", walletId)
            .andAppend("year", body.year)
            .andAppend("month", body.month)
        return try {
            val result = summaryService.requestSummaryCsv(WalletId(walletId), AccountId(accountId), body.year.toString(), body.month.toString())

            result.mapLeft {
                logger.error(marker.andAppend("error", it), "BackofficeSummaryController#generateSummaryCsv")

                return when (it) {
                    is SummaryError.WalletNotFound -> HttpResponse.notFound<Unit>()
                    is SummaryError.NotAllowed -> HttpResponse.status<Unit>(HttpStatus.FORBIDDEN)

                    is SummaryError.FailedToSendEmail,
                    is SummaryError.ServerError,
                    ->
                        HttpResponse.serverError<Unit>()
                }
            }

            logger.info(marker, "BackofficeSummaryController#generateSummaryCsv")
            HttpResponse.noContent<Unit>()
        } catch (e: Exception) {
            handleError(marker, e, "BackofficeSummaryController#generateSummaryCsv")
        }
    }

    private fun handleError(markers: LogstashMarker, e: Exception, message: String): HttpResponse<*> {
        val httpStatus = when (e) {
            is DateTimeParseException -> HttpStatus.BAD_REQUEST
            is StatementError.NotAllowed -> HttpStatus.METHOD_NOT_ALLOWED

            is StatementError.ServerError,
            is StatementError.WalletNotFound,
            is StatementError.FailedToSendEmail,
            -> HttpStatus.INTERNAL_SERVER_ERROR

            else -> HttpStatus.INTERNAL_SERVER_ERROR
        }

        markers.andAppend("httpStatus", httpStatus.code).also {
            logger.error(it, message, e)
        }

        return HttpResponse.status<Unit>(httpStatus)
    }
}