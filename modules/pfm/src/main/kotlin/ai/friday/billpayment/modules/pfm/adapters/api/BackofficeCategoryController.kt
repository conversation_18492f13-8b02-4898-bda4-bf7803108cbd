package ai.friday.billpayment.modules.pfm.adapters.api

import ai.friday.billpayment.adapters.api.toWalletBillCategoryTO
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.pfm.PFMModule
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@Secured(BACKOFFICE)
@Controller("/backoffice/category")
@PFMModule
class BackofficeCategoryController(
    private val walletBillControllerService: PFMWalletCategoryService,
) {
    private val logger: Logger = LoggerFactory.getLogger(BackofficeCategoryController::class.java)

    @Get("/wallet/{walletId}")
    fun listCategories(@PathVariable walletId: String): HttpResponse<*> {
        return try {
            val categories = walletBillControllerService.findWalletCategories(walletId = WalletId(value = walletId))

            logger.info(Markers.append("categoriesSize", categories.size), "BackofficeCategoryController#listCategories")
            HttpResponse.ok(categories.map { it.toWalletBillCategoryTO() })
        } catch (e: Exception) {
            logger.error("BackofficeCategoryController#listCategories", e)
            return HttpResponse.serverError<Unit>()
        }
    }
}