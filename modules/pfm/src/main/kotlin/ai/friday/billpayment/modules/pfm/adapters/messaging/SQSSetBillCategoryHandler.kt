package ai.friday.billpayment.modules.pfm.adapters.messaging

import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.pfm.PFMBillCategoryService
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.SetBillCategoryError
import ai.friday.billpayment.modules.pfm.PFMModuleNoTest
import ai.friday.billpayment.modules.pfm.app.SetBillCategoryTO
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@PFMModuleNoTest
open class SQSSetBillCategoryHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val billCategoryService: PFMBillCategoryService,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = configuration.setBillCategoryQueueName,
) {
    private val logger = LoggerFactory.getLogger(SQSSetBillCategoryHandler::class.java)
    private val logName = "BillCategorySuggestionHandler"
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val body = message.body()

        val setBillCategoryTO = parseObjectFrom<SetBillCategoryTO>(body)

        val markers =
            Markers.append("billId", setBillCategoryTO.billId)
                .andAppend("billCategoryId", setBillCategoryTO.billCategoryId)
                .andAppend("accountId", setBillCategoryTO.accountId)

        billCategoryService.setBillCategory(
            billId = BillId(setBillCategoryTO.billId),
            billCategoryId = PFMCategoryId(setBillCategoryTO.billCategoryId),
            accountId = AccountId(setBillCategoryTO.accountId),
        ).getOrElse {
            logger.error(markers.andAppend("reason", it), logName)
            return when (it) {
                SetBillCategoryError.BillNotFound,
                SetBillCategoryError.CategoryNotFound,
                -> {
                    SQSHandlerResponse(shouldDeleteMessage = true)
                }
                SetBillCategoryError.ServerError,
                SetBillCategoryError.AlreadyLocked,
                -> {
                    SQSHandlerResponse(shouldDeleteMessage = false)
                }
            }
        }

        logger.info(markers, logName)
        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    override fun handleError(message: Message, e: Exception): SQSHandlerResponse {
        logger.error(Markers.append("event", message.body()), logName, e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }
}