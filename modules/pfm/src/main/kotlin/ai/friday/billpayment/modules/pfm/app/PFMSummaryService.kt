package ai.friday.billpayment.modules.pfm.app

import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.pfm.SummaryService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.modules.manualentry.app.category.ManualEntrySummaryService
import ai.friday.billpayment.modules.pfm.PFMModule
import io.micronaut.context.annotation.Property

@PFMModule
open class PFMSummaryService(
    accountService: AccountService,
    walletService: WalletService,
    sqsMessagePublisher: MessagePublisher,
    notificationAdapter: NotificationAdapter,
    @Property(name = "sqs.summaryQueueName") queueName: String,
    private val billSummaryService: BillSummaryService,
    private val manualEntrySummaryService: ManualEntrySummaryService,
    @Property(name = "tenant.displayName") displayName: String,
) :
    WalletCategorySummaryService(
        accountService,
        walletService,
        sqsMessagePublisher,
        notificationAdapter,
        queueName,
        displayName,
    ) {
    override fun getServices(): List<SummaryService> {
        return listOf(billSummaryService, manualEntrySummaryService)
    }
}