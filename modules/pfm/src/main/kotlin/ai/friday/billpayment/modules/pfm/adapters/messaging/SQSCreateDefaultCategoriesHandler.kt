package ai.friday.billpayment.modules.pfm.adapters.messaging

import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.pfm.PFMModuleNoTest
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@PFMModuleNoTest
open class SQSCreateDefaultCategoriesHandler(
    amazonSQS: SqsClient,
    configuration: PFMSQSMessageHandlerConfiguration,
    private val walletBillCategoryService: PFMWalletCategoryService,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = configuration.createDefaultCategories,
) {
    private val logger = LoggerFactory.getLogger(SQSCreateDefaultCategoriesHandler::class.java)
    private val logName = "SQSCreateDefaultCategoriesHandler"
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val body = message.body()

        val createBillCategoryTO = parseObjectFrom<CreateDefaultCategoriesTO>(body)

        val walletId = WalletId(createBillCategoryTO.walletId)

        val markers =
            Markers.append("walletId", createBillCategoryTO.walletId)

        walletBillCategoryService.createDefaultWalletCategories(walletId).getOrElse {
            logger.warn(markers.andAppend("createDefaultCategoriesError", it), logName)
            return SQSHandlerResponse(shouldDeleteMessage = true)
        }

        logger.info(markers, logName)

        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    override fun handleError(message: Message, e: Exception): SQSHandlerResponse {
        logger.error(Markers.append("event", message.body()), logName, e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }
}

data class CreateDefaultCategoriesTO(val walletId: String)