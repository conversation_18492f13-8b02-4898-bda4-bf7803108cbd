package ai.friday.billpayment.modules.pfm.app

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillCategoryAdded
import ai.friday.billpayment.app.bill.BillCategoryDeleted
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.billLockProvider
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillRecurrenceRepository
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.pfm.PFMBillCategoryService
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.RemoveBillCategoryError
import ai.friday.billpayment.app.pfm.SetBillCategoryError
import ai.friday.billpayment.app.pfm.WalletBillCategoryRepository
import ai.friday.billpayment.app.recurrence.Range
import ai.friday.billpayment.modules.pfm.PFMModule
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import jakarta.inject.Named
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@PFMModule
open class BillCategoryService(
    private val billEventRepository: BillEventRepository,
    private val walletBillCategoryRepository: WalletBillCategoryRepository,
    private val billEventPublisher: BillEventPublisher,
    private val billRecurrenceRepository: BillRecurrenceRepository,
    @Named(billLockProvider) private val lockProvider: InternalLock,
) : PFMBillCategoryService {
    private val logger = LoggerFactory.getLogger(BillCategoryService::class.java)

    override fun setBillCategory(billId: BillId, billCategoryId: PFMCategoryId, accountId: AccountId, range: Range?): Either<SetBillCategoryError, Unit> {
        val markers = Markers.append("billId", billId.value)
            .andAppend("billCategoryId", billCategoryId.value)
            .andAppend("accountId", accountId.value)
        val logName = "BillCategoryService#setBillCategory"

        val lock = lockProvider.acquireLock(billId.value)
        if (lock == null) {
            logger.warn(markers, logName)
            return SetBillCategoryError.AlreadyLocked.left()
        }

        try {
            val bill = billEventRepository.getBillById(billId).getOrElse {
                logger.warn(markers, logName)
                return SetBillCategoryError.BillNotFound.left()
            }
            markers.andAppend("bill", bill)

            val walletBillCategories = walletBillCategoryRepository.findByWalletId(bill.walletId)

            val walletBillCategory = walletBillCategories.singleOrNull {
                it.categoryId == billCategoryId
            }
            markers.andAppend("walletBillCategory", walletBillCategory)

            if (walletBillCategory == null) {
                logger.warn(markers, logName)
                return SetBillCategoryError.CategoryNotFound.left()
            }

            billEventPublisher.publish(
                bill,
                BillCategoryAdded(
                    billId = bill.billId,
                    walletId = bill.walletId,
                    actionSource = ActionSource.Api(accountId = accountId),
                    categoryId = walletBillCategory.categoryId,
                    category = null,
                ),
            )

            val recurrenceId = when (bill.source) {
                is ActionSource.WalletRecurrence -> (bill.source as ActionSource.WalletRecurrence).recurrenceId
                is ActionSource.SubscriptionRecurrence -> (bill.source as ActionSource.SubscriptionRecurrence).recurrenceId
                is ActionSource.Recurrence -> (bill.source as ActionSource.Recurrence).recurrenceId
                else -> null
            }

            recurrenceId?.let {
                if (range == Range.THIS) {
                    return@let
                }

                billRecurrenceRepository.findOrNull(recurrenceId, bill.walletId)
                    ?.let { recurrence ->
                        billRecurrenceRepository.save(recurrence.copy(billCategoryId = billCategoryId))

                        recurrence.bills.dropWhile { it != bill.billId }.stream().parallel().forEach { billId ->
                            if (billId != bill.billId) {
                                billEventRepository.getBillById(billId).getOrNull()?.let { bill ->
                                    billEventPublisher.publish(
                                        bill,
                                        BillCategoryAdded(
                                            billId = bill.billId,
                                            walletId = bill.walletId,
                                            actionSource = ActionSource.Api(accountId = accountId),
                                            categoryId = walletBillCategory.categoryId,
                                            category = null,
                                        ),
                                    )
                                }
                            }
                        }
                    }
            }
            logger.info(markers, logName)
            return Unit.right()
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            return SetBillCategoryError.ServerError.left()
        } finally {
            lock.unlock()
        }
    }

    override fun removeBillCategory(billId: BillId, accountId: AccountId, range: Range?): Either<RemoveBillCategoryError, Unit> {
        val markers = Markers.append("billId", billId.value)
            .andAppend("accountId", accountId.value)

        try {
            val bill = billEventRepository.getBillById(billId).getOrElse {
                logger.warn(markers, "BillCategoryService#removeBillCategory")
                return RemoveBillCategoryError.BillNotFound.left()
            }

            billEventPublisher.publish(
                bill,
                BillCategoryDeleted(
                    billId = bill.billId,
                    walletId = bill.walletId,
                    actionSource = ActionSource.Api(accountId = accountId),
                ),
            )

            val recurrenceId = when (bill.source) {
                is ActionSource.WalletRecurrence -> (bill.source as ActionSource.WalletRecurrence).recurrenceId
                is ActionSource.SubscriptionRecurrence -> (bill.source as ActionSource.SubscriptionRecurrence).recurrenceId
                is ActionSource.Recurrence -> (bill.source as ActionSource.Recurrence).recurrenceId
                else -> null
            }

            recurrenceId?.let {
                if (range == Range.THIS) {
                    return@let
                }

                billRecurrenceRepository.findOrNull(recurrenceId, bill.walletId)
                    ?.let { recurrence ->
                        billRecurrenceRepository.save(recurrence.copy(billCategoryId = null))

                        recurrence.bills.dropWhile { it != bill.billId }.stream().parallel().forEach { billId ->
                            if (billId != bill.billId) {
                                billEventRepository.getBillById(billId).getOrNull()?.let { bill ->
                                    billEventPublisher.publish(
                                        bill,
                                        BillCategoryDeleted(
                                            billId = bill.billId,
                                            walletId = bill.walletId,
                                            actionSource = ActionSource.Api(accountId = accountId),
                                        ),
                                    )
                                }
                            }
                        }
                    }
            }

            logger.info(markers, "BillCategoryService#removeBillCategory")
            return Unit.right()
        } catch (ex: Exception) {
            logger.error(markers, "BillCategoryService#removeBillCategory", ex)
            return RemoveBillCategoryError.ServerError.left()
        }
    }
}

data class SetBillCategoryTO(val billId: String, val billCategoryId: String, val accountId: String)