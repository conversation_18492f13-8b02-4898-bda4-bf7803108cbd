package ai.friday.billpayment.modules.pfm.adapters.api

import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.api.StandardHttpResponses
import ai.friday.billpayment.adapters.api.toWalletBillCategoryTO
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.pfm.CreateWalletBillCategoryError
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pfm.UpdateWalletBillCategoryError
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.pfm.PFMModule
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import io.micronaut.validation.Validated
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Validated
@Secured(Role.Code.OWNER)
@Controller("/wallet")
@Version("2")
@PFMModule
class WalletBillCategoryController(private val walletBillCategoryService: PFMWalletCategoryService) {
    private val logger = LoggerFactory.getLogger(WalletBillCategoryController::class.java)

    @Post("/{walletId}/category")
    fun create(@PathVariable walletId: String, @Body body: CreateCustomWalletBillCategoryTO): HttpResponse<*> {
        val markers = Markers.append("walletId", walletId)
            .andAppend("body", body)

        return walletBillCategoryService.create(
            walletId = WalletId(value = walletId),
            name = body.name,
            icon = body.icon,
        ).map {
            markers.andAppend("category", it)

            logger.info(markers, "WalletBillCategoryController#create")
            HttpResponse.created(it.toWalletBillCategoryTO())
        }.getOrElse {
            markers.andAppend("error", it)

            val response = when (it) {
                CreateWalletBillCategoryError.AlreadyExists -> ResponseTO(code = "4001", message = it.toString())
            }
            markers.andAppend("response", response)

            logger.info(markers, "WalletBillCategoryController#create")
            StandardHttpResponses.badRequest(response = response)
        }
    }

    @Put("/{walletId}/category/{billCategoryId}")
    fun update(@PathVariable walletId: String, @PathVariable billCategoryId: String, @Body body: UpdateWalletBillCategoryRequestTO): HttpResponse<*> {
        val logName = "WalletBillCategoryController#update"
        val markers = Markers.append("walletId", walletId)
            .andAppend("billCategoryId", billCategoryId)

        return walletBillCategoryService.update(
            walletId = WalletId(value = walletId),
            categoryId = PFMCategoryId(value = billCategoryId),
            billCategoryName = body.name,
            billCategoryIcon = body.icon,
            enabled = body.enabled,
        ).map {
            logger.info(markers, logName)
            HttpResponse.ok(UpdateWalletBillCategoryResponseTO(name = it.name, icon = it.icon, enabled = it.enabled, billCategoryId = it.categoryId.value))
        }.getOrElse {
            markers.andAppend("error", it)

            return when (it) {
                UpdateWalletBillCategoryError.NotFound -> StandardHttpResponses.badRequest(ResponseTO(code = "4001", message = it.toString()))
                UpdateWalletBillCategoryError.NameAlreadyExists -> StandardHttpResponses.conflict(ResponseTO(code = "4009", message = it.toString()))
            }
        }
    }
}

data class CreateCustomWalletBillCategoryTO(
    val name: String,
    val icon: String,
)

data class UpdateWalletBillCategoryRequestTO(
    val name: String,
    val icon: String,
    val enabled: Boolean,
)

data class UpdateWalletBillCategoryResponseTO(
    val name: String,
    val icon: String,
    val enabled: Boolean,
    val billCategoryId: String,
)