package ai.friday.billpayment.modules.pfm.app

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.notification.ByteArrayWithNameAndType
import ai.friday.billpayment.app.pfm.CategorySummary
import ai.friday.billpayment.app.pfm.SummaryEntry
import ai.friday.billpayment.app.pfm.SummaryEntryType
import ai.friday.billpayment.app.pfm.SummaryError
import ai.friday.billpayment.app.pfm.SummaryService
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.dateFormatMonthBR
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonPropertyOrder
import com.fasterxml.jackson.dataformat.csv.CsvMapper
import io.micronaut.context.annotation.Property
import io.micronaut.http.MediaType
import java.io.StringWriter
import java.time.LocalDate
import java.time.Month
import java.time.Year
import java.time.format.DateTimeFormatter
import java.util.Locale
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

abstract class WalletCategorySummaryService(
    private val accountService: AccountService,
    private val walletService: WalletService,
    private val sqsMessagePublisher: MessagePublisher,
    private val notificationAdapter: NotificationAdapter,
    @Property(name = "sqs.summaryQueueName") private val queueName: String,
    @Property(name = "tenant.displayName") private val displayName: String,
) {
    abstract fun getServices(): List<SummaryService>

    private fun generateSummaryList(
        walletId: WalletId,
        walletMember: Member,
        year: Year,
        month: Month,
    ): List<SummaryEntry> {
        val summaries = getServices().map { service ->
            service.generateSummaryList(walletId, walletMember, year, month)
        }.flatten()

        return summaries.toList().sortedWith(compareBy<SummaryEntry> { it.date }.thenBy { it.category?.name })
    }

    fun sendSummaryCsvByEmail(
        accountId: AccountId,
        walletId: WalletId,
        year: Year,
        month: Month,
    ): Either<SummaryError, Unit> {
        val logName = "WalletCategorySummaryService#sendSummaryCsvByEmail"
        val marker = Markers.append("accountId", accountId.value)
            .andAppend("walletId", walletId.value)
            .andAppend("year", year)
            .andAppend("month", month)

        val account = accountService.findAccountById(accountId)
        val fileName = "${year.value}_${month.value}"
        val periodMessage = dateFormatMonthBR.format(LocalDate.of(year.value, month.value, 1))

        try {
            val wallet = walletService.findWalletOrNull(walletId) ?: return SummaryError.WalletNotFound().left()
            val walletMember = wallet.getActiveMember(accountId)

            val summaryList = generateSummaryList(walletId, walletMember, year, month)
            val csvData = summaryEntryConverter(summaryList)

            notificationAdapter.notifyWalletSummary(
                periodMessage = periodMessage,
                emailAddress = account.emailAddress,
                name = account.name,
                files = listOf(
                    ByteArrayWithNameAndType(
                        fileName = "Resumo_${displayName}_$fileName.csv",
                        mediaType = "${MediaType.TEXT_CSV};charset=UTF-16",
                        data = csvData.toByteArray(Charsets.UTF_16),
                    ),
                ),
            )

            return Unit.right()
        } catch (e: NoSuchElementException) {
            LOG.error(marker.andAppend("errorMessage", e.toString()), logName)
            return SummaryError.NotAllowed().left()
        } catch (e: Exception) {
            LOG.error(marker.andAppend("errorMessage", e.toString()), logName)
            return SummaryError.ServerError().left()
        }
    }

    fun requestSummaryCsv(walletId: WalletId, accountId: AccountId, year: String, month: String): Either<SummaryError, Unit> {
        val logName = "WalletCategorySummaryService#requestSummaryCsv"
        val marker = Markers.append("accountId", accountId.value)
            .andAppend("walletId", walletId.value)
            .andAppend("year", year)
            .andAppend("month", month)

        sqsMessagePublisher.sendMessage(
            queueName = queueName,
            body = RequestSummaryCsvTO(
                accountId = accountId.value,
                walletId = walletId.value,
                year = year,
                month = month,
            ),
        )

        LOG.info(marker, logName)

        return Unit.right()
    }

    fun getSummary(
        walletId: WalletId,
        walletMember: Member,
        year: Year,
        month: Month,
    ): List<CategorySummary> {
        val listCategory = generateSummaryList(walletId, walletMember, year, month)
            .groupBy { it.category?.categoryId }
            .map { (_, entries) ->
                val (incomes, expenses) = entries.partition { it.isIncome() }
                CategorySummary(
                    category = entries.firstOrNull()?.category,
                    totalExpenseAmount = expenses.sumOf { it.totalAmount },
                    totalIncomeAmount = incomes.sumOf { it.totalAmount },
                    billCount = entries.count { it.type == SummaryEntryType.PAYMENT },
                    reminderCount = entries.count { it.type == SummaryEntryType.REMINDER },
                    incomeCount = entries.count { it.type == SummaryEntryType.INCOME },
                )
            }.sortedWith(compareBy<CategorySummary> { it.category == null }.thenByDescending { it.totalExpenseAmount + it.totalIncomeAmount }.thenBy { it.category?.name })

        val hasNullCategory = listCategory.isNotEmpty() && listCategory.last().category == null
        return if (hasNullCategory) {
            listCategory
        } else {
            listCategory + CategorySummary(category = null, totalExpenseAmount = 0, totalIncomeAmount = 0, billCount = 0, reminderCount = 0, incomeCount = 0)
        }
    }

    companion object {
        internal val LOG = LoggerFactory.getLogger(WalletCategorySummaryService::class.java)
    }

    private fun summaryEntryConverter(entries: List<SummaryEntry>): String {
        val csvSchema = CsvMapper().schemaFor(SummaryCSV::class.java).withHeader()
        val csvData = StringWriter()

        val sequenceWriter = CsvMapper().writer(csvSchema).writeValues(csvData)
        if (entries.isEmpty()) {
            sequenceWriter.write("")
        }
        entries.forEach {
            sequenceWriter.write(it.toSummaryEntryCSV())
        }
        return csvData.toString()
    }

    private fun SummaryEntry.toSummaryEntryCSV() = SummaryCSV(
        valor = String.format(Locale.forLanguageTag("pt-BR"), "%.2f", (totalAmount.toFloat() / 100f)),
        nome = title,
        data = date.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")),
        descricao = description,
        tipo = entryType,
        categoria = this.category?.name ?: "",
    )
}

data class RequestSummaryCsvTO(
    val walletId: String,
    val accountId: String,
    val year: String,
    val month: String,
)

@JsonPropertyOrder(value = ["data", "nome", "descricao", "tipo", "valor", "categoria"])
data class SummaryCSV(
    val valor: String,
    val nome: String,
    val data: String,
    val descricao: String,
    val tipo: String,
    val categoria: String,
)