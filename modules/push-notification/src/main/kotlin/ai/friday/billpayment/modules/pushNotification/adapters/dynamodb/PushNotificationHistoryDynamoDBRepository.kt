package ai.friday.billpayment.modules.pushNotification.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.AbstractBillPaymentDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.notification.PushNotificationContent
import ai.friday.billpayment.app.notification.SendPushNotificationToAccountIdResult
import ai.friday.billpayment.modules.pushNotification.PushNotificationModule
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationHistoryRepository
import java.net.URI
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val PUSH_NOTIFICATION_HISTORY_PREFIX = "PUSH_NOTIFICATION_SENT"

@PushNotificationModule
class PushNotificationHistoryDynamoDAO(
    cli: DynamoDbEnhancedClient,
) : AbstractBillPaymentDynamoDAO<PushNotificationHistoryEntity>(cli, PushNotificationHistoryEntity::class.java)

@PushNotificationModule
class PushNotificationHistoryDynamoDBRepository(
    private val dynamoDAO: PushNotificationHistoryDynamoDAO,
) : PushNotificationHistoryRepository {
    override fun save(
        accountId: AccountId,
        content: PushNotificationContent,
        result: SendPushNotificationToAccountIdResult,
    ) {
        val entity = PushNotificationHistoryEntity().apply {
            primaryKey = "$PUSH_NOTIFICATION_HISTORY_PREFIX#${accountId.value}"
            scanKey = content.sentAt.format(DateTimeFormatter.ISO_DATE_TIME)
            this.accountId = accountId.value
            title = content.title
            body = content.body
            url = content.url?.toString()
            imageUrl = content.imageUrl?.toString()
            createdAt = content.sentAt.format(DateTimeFormatter.ISO_DATE_TIME)
            sentResult = SentResultEntity().apply {
                sent = result.sent
                failures = result.failures
                disabled = result.disabled
                stale = result.stale
                purged = result.purged
            }
        }

        dynamoDAO.save(entity)
    }

    override fun findByAccountId(accountId: AccountId): List<PushNotificationContent> {
        val partitionKey = "$PUSH_NOTIFICATION_HISTORY_PREFIX#${accountId.value}"
        return dynamoDAO.findByPartitionKey(partitionKey).map { it.toDomain() }
    }
}

private fun PushNotificationHistoryEntity.toDomain(): PushNotificationContent {
    return PushNotificationContent(
        title = this.title,
        body = this.body,
        url = this.url?.let { URI.create(it) },
        imageUrl = this.imageUrl?.let { URI.create(it) },
        sentAt = ZonedDateTime.parse(this.createdAt, DateTimeFormatter.ISO_DATE_TIME),
    )
}

@DynamoDbBean
class PushNotificationHistoryEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbAttribute("AccountId")
    lateinit var accountId: String

    @get:DynamoDbAttribute("Title")
    lateinit var title: String

    @get:DynamoDbAttribute("Body")
    lateinit var body: String

    @get:DynamoDbAttribute("Url")
    var url: String? = null

    @get:DynamoDbAttribute("ImageUrl")
    var imageUrl: String? = null

    @get:DynamoDbAttribute("CreatedAt")
    lateinit var createdAt: String

    var sentResult: SentResultEntity? = null
}

@DynamoDbBean
class SentResultEntity {
    var sent: Int = 0
    var failures: Int = 0
    var disabled: Int = 0
    var stale: Int = 0
    var purged: Int = 0
}