package ai.friday.billpayment.modules.pushNotification.adapters.api

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.notification.NotificationTemplate
import ai.friday.billpayment.modules.pushNotification.PushNotificationModule
import ai.friday.billpayment.modules.pushNotification.app.DefaultPushNotificationService
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationData
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationMessage
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationTemplate
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationTemplateService
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(BACKOFFICE)
@Controller("/backoffice/pushNotification")
@PushNotificationModule
class BackofficePushNotificationController(
    private val defaultPushNotificationSenderService: DefaultPushNotificationService,
    private val pushNotificationTemplates: List<PushNotificationMessage>,
    private val pushNotificationTemplateService: PushNotificationTemplateService,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Post("/template")
    fun createTemplate(
        authentication: Authentication,
        @Body request: CreatePushNotificationTemplateRequestTO,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val logName = "PushNotificationTemplateController#createTemplate"

        return try {
            val template = pushNotificationTemplateService.createTemplate(
                template = request.template,
                title = request.title,
                message = request.message,
                url = request.url,
                createdBy = accountId,
            )

            logger.info(append("request", request), logName)
            HttpResponse.ok(template.toResponseTO())
        } catch (e: Exception) {
            logger.error(append("request", request), logName, e)
            HttpResponse.serverError("Erro ao criar template de push notification")
        }
    }

    @Delete("/template/{template}/deactivate")
    fun deactivateTemplate(
        authentication: Authentication,
        @PathVariable template: String,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val logName = "PushNotificationTemplateController#deactivateTemplate"

        return try {
            val template = pushNotificationTemplateService.deactivateTemplate(NotificationTemplate(template), accountId)
            logger.info(append("template", template), logName)
            HttpResponse.noContent<Unit>()
        } catch (e: Exception) {
            logger.error(append("template", template), logName, e)
            HttpResponse.serverError("Erro ao desativar template de push notification")
        }
    }

    @Post("/template/{template}/activate")
    fun activateTemplate(
        authentication: Authentication,
        @PathVariable template: String,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val logName = "PushNotificationTemplateController#activateTemplate"

        return try {
            val activated = pushNotificationTemplateService.activateTemplate(NotificationTemplate(template), accountId)
            logger.info(append("template", activated), logName)
            HttpResponse.ok(activated?.toResponseTO())
        } catch (e: Exception) {
            logger.error(append("template", template), logName, e)
            HttpResponse.serverError("Erro ao ativar template de push notification")
        }
    }

    @Get("/template")
    fun getAllActiveTemplates(): HttpResponse<*> {
        val logName = "PushNotificationTemplateController#getAllActiveTemplates"

        return try {
            val templates = pushNotificationTemplateService.getAllActiveTemplates()
            HttpResponse.ok(templates.map { it.toResponseTO() })
        } catch (e: Exception) {
            logger.error(logName, e)
            HttpResponse.serverError("Erro ao buscar templates ativos de push notification")
        }
    }

    @Get("/template/{template}")
    fun getTemplate(@PathVariable template: String): HttpResponse<*> {
        val logName = "PushNotificationTemplateController#getTemplate"

        return try {
            val result = pushNotificationTemplateService.getActiveTemplate(NotificationTemplate(template))
            if (result != null) {
                HttpResponse.ok(result.toResponseTO())
            } else {
                HttpResponse.notFound()
            }
        } catch (e: Exception) {
            logger.error(append("template", template), logName, e)
            HttpResponse.serverError("Erro ao buscar template de push notification")
        }
    }

    @Get("/template/{template}/history")
    fun getTemplateHistory(@PathVariable template: String): HttpResponse<*> {
        val logName = "PushNotificationTemplateController#getTemplateHistory"

        return try {
            val templates = pushNotificationTemplateService.getTemplateHistory(NotificationTemplate(template))
            HttpResponse.ok(templates.map { it.toResponseTO() })
        } catch (e: Exception) {
            logger.error(append("template", template), logName, e)
            HttpResponse.serverError("Erro ao buscar histórico do template de push notification")
        }
    }

    @Put("migrateTemplates")
    fun migrateTemplates(authentication: Authentication): HttpResponse<*> {
        val logName = "PushNotificationTemplateController#migrateTemplates"
        val accountId = authentication.toAccountId()
        return try {
            val templates = pushNotificationTemplateService.migrateExistingTemplates(accountId)
            HttpResponse.ok(templates.map { it.toResponseTO() })
        } catch (e: Exception) {
            logger.error(append("accountId", accountId.value), logName, e)
            HttpResponse.serverError("Erro ao buscar histórico do template de push notification")
        }
    }

    @Get("/listTemplates")
    fun listTemplates(): HttpResponse<*> {
        return HttpResponse.ok(pushNotificationTemplates)
    }

    @Post("/send/template")
    fun sendPushNotificationTemplate(@Body body: SendPushNotificationTemplateTO): HttpResponse<*> {
        val logName = "BackofficePushNotificationController#sendPushNotificationTemplate"
        try {
            val result = defaultPushNotificationSenderService.sendNotificationAsync(
                AccountId(body.accountId),
                PushNotificationData(
                    template = NotificationTemplate(body.template),
                    urlParameter = body.urlParameter,
                    parameters = body.parameters,
                ),
            ).getOrElse {
                return HttpResponse.serverError(it)
            }

            if (result == null) {
                return HttpResponse.notFound("Template not found or not active")
            } else {
                return HttpResponse.ok(result)
            }
        } catch (e: Exception) {
            logger.error(append("body", body), logName, e)
            return HttpResponse.serverError("Error sending push notification")
        }
    }

    @Post("/send/custom")
    fun sendPushNotificationCustom(@Body body: SendPushNotificationCustomTO): HttpResponse<*> {
        val logName = "BackofficePushNotificationController#sendPushNotificationCustom"
        try {
            val message = PushNotificationMessage(
                name = "custom_message_without_template",
                template = "custom_message_without_template",
                message = body.message,
                title = body.title,
                url = body.url,
            )

            val result = defaultPushNotificationSenderService.sendNotificationAsync(
                AccountId(body.accountId),
                message.toPushNotificationContent(),
            )
            return HttpResponse.ok(result)
        } catch (e: Exception) {
            logger.error(append("body", body), logName, e)
            return HttpResponse.serverError("Error sending push notification")
        }
    }
}

data class SendPushNotificationCustomTO(
    val accountId: String,
    val message: String,
    val title: String,
    val url: String,
)

data class SendPushNotificationTemplateTO(
    val accountId: String,
    val template: String,
    val urlParameter: String? = null,
    val parameters: Map<String, String> = emptyMap(),
)

data class CreatePushNotificationTemplateRequestTO(
    val template: String,
    val title: String,
    val message: String,
    val url: String?,
)

data class PushNotificationTemplateResponseTO(
    val template: String,
    val title: String,
    val message: String,
    val url: String?,
    val isActive: Boolean,
    val lastUpdated: String,
    val lastUpdatedBy: String,
)

private fun PushNotificationTemplate.toResponseTO(): PushNotificationTemplateResponseTO {
    return PushNotificationTemplateResponseTO(
        template = this.template.value,
        title = this.title,
        message = this.message,
        url = this.url,
        isActive = this.isActive,
        lastUpdated = this.lastUpdated.format(DateTimeFormatter.ISO_DATE_TIME),
        lastUpdatedBy = this.lastUpdatedBy.value,
    )
}