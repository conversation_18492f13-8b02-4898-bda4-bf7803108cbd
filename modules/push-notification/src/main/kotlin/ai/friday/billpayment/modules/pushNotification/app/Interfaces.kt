package ai.friday.billpayment.modules.pushNotification.app

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.notification.PushNotificationContent
import ai.friday.billpayment.app.notification.SendPushNotificationToAccountIdResult

interface PushNotificationSender {
    fun sendNotification(
        enrollment: PushNotificationEnrollment.Active,
        content: PushNotificationContent,
    ): SendPushNotificationResult
}

interface PushNotificationEnrollmentRepository {
    fun upsert(accountId: AccountId, token: PushNotificationToken): PushNotificationEnrollment.Active

    fun delete(accountId: AccountId, token: PushNotificationToken): Unit

    fun findByToken(accountId: AccountId, pushNotificationToken: PushNotificationToken): PushNotificationEnrollment?

    fun findByAccountId(accountId: AccountId): List<PushNotificationEnrollment>
}

interface PushNotificationHistoryRepository {
    fun save(
        accountId: AccountId,
        content: PushNotificationContent,
        result: SendPushNotificationToAccountIdResult,
    )

    fun findByAccountId(accountId: AccountId): List<PushNotificationContent>
}