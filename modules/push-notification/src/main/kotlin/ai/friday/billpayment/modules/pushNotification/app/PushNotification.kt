package ai.friday.billpayment.modules.pushNotification.app

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.hasDeveloperEarlyAccess
import ai.friday.billpayment.app.notification.NotificationTemplate
import ai.friday.billpayment.app.notification.PushNotificationContent
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Parameter
import java.net.URI
import java.time.ZonedDateTime

data class PushNotificationToken(val value: String)

/**
 * O código só começou a atualizar a updateAt para tokens que já existiam na base nesse dia.
 * Se não tiver essa data de corte, todos os tokens seriam considerados stale ou passíveis de serem purged.
 */
val staleEnrollmentCutOffDate: ZonedDateTime = ZonedDateTime.of(2025, 6, 2, 0, 0, 0, 0, brazilTimeZone)

sealed class PushNotificationEnrollment() {
    data class Active(val token: PushNotificationToken, val updatedAt: ZonedDateTime) : PushNotificationEnrollment()
    data class Stale(val token: PushNotificationToken, val updatedAt: ZonedDateTime) : PushNotificationEnrollment() {
        fun shouldBePurged(): Boolean = updatedAt.isAfter(staleEnrollmentCutOffDate) && updatedAt.isBefore(getZonedDateTime().minusDays(120))
    }

    companion object {
        fun create(accountId: AccountId, token: PushNotificationToken, updatedAt: ZonedDateTime): PushNotificationEnrollment {
            return if ((accountId.hasDeveloperEarlyAccess() || updatedAt.isAfter(staleEnrollmentCutOffDate)) && updatedAt.isBefore(getZonedDateTime().minusDays(30))) {
                Stale(token, updatedAt)
            } else {
                Active(token, updatedAt)
            }
        }
    }
}

sealed class SavePushNotificationResult {
    data object Success : SavePushNotificationResult()
    data class Failure(val exception: Exception) : SavePushNotificationResult()
}

sealed class SendPushNotificationResult {
    data object Success : SendPushNotificationResult()
    data class Failure(val exception: Exception) : SendPushNotificationResult()

    data object EnrollmentDisabled : SendPushNotificationResult()
}

@EachProperty(value = "push-notifications")
class PushNotificationMessage @ConfigurationInject constructor(
    @param:Parameter val name: String,
    val template: String,
    val message: String,
    val title: String,
    val url: String?,
) {
    fun toPushNotificationContent(): PushNotificationContent {
        return PushNotificationContent(
            title = title,
            body = message,
            url = url?.let { URI.create(it) },
            imageUrl = null,
        )
    }
}

interface PushNotificationTemplateRepository {
    fun save(pushNotificationTemplate: PushNotificationTemplate): PushNotificationTemplate

    fun findByName(notificationTemplate: NotificationTemplate, isActive: Boolean?): List<PushNotificationTemplate>

    fun findAllActive(): List<PushNotificationTemplate>

    fun findAll(): List<PushNotificationTemplate>

    fun deactivate(notificationTemplate: NotificationTemplate, deactivatedBy: AccountId)
}

data class PushNotificationTemplate(
    val template: NotificationTemplate,
    val title: String,
    val message: String,
    val url: String?,
    val isActive: Boolean,
    val lastUpdated: ZonedDateTime,
    val lastUpdatedBy: AccountId,
)

data class PushNotificationData(
    val template: NotificationTemplate,
    val urlParameter: String?,
    val parameters: Map<String, String>,
)