package ai.friday.billpayment.modules.pushNotification.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.AbstractBillPaymentDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.GlobalSecondaryIndexNames
import ai.friday.billpayment.adapters.dynamodb.INDEX_1
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.notification.NotificationTemplate
import ai.friday.billpayment.modules.pushNotification.PushNotificationModule
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationTemplate
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationTemplateRepository
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@PushNotificationModule
class PushNotificationTemplateDynamoDAO(
    cli: DynamoDbEnhancedClient,
) : AbstractBillPaymentDynamoDAO<PushNotificationTemplateEntity>(cli, PushNotificationTemplateEntity::class.java)

private const val SCAN_KEY = "PUSH_NOTIFICATION_TEMPLATE"

@PushNotificationModule
class PushNotificationTemplateDynamoDBRepository(
    private val dynamoDAO: PushNotificationTemplateDynamoDAO,
) : PushNotificationTemplateRepository {

    override fun save(pushNotificationTemplate: PushNotificationTemplate): PushNotificationTemplate {
        deactivatePreviousBySave(pushNotificationTemplate)

        val dateTimeStr = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
        val entity = PushNotificationTemplateEntity().apply {
            this.primaryKey = pushNotificationTemplate.template.value
            this.scanKey = "$SCAN_KEY#$dateTimeStr"
            gSIndex1PartitionKey = SCAN_KEY
            gSIndex1RangeKey = "${activeFilter(pushNotificationTemplate.isActive)}#${pushNotificationTemplate.template}"
            this.template = pushNotificationTemplate.template.value
            this.title = pushNotificationTemplate.title
            this.message = pushNotificationTemplate.message
            this.url = pushNotificationTemplate.url
            this.active = pushNotificationTemplate.isActive
            this.createdAt = dateTimeStr
            this.updatedAt = dateTimeStr
            this.createdBy = pushNotificationTemplate.lastUpdatedBy.value
            this.updatedBy = pushNotificationTemplate.lastUpdatedBy.value
        }

        dynamoDAO.save(entity)
        return entity.toDomain()
    }

    override fun findByName(notificationTemplate: NotificationTemplate, isActive: Boolean?): List<PushNotificationTemplate> {
        return findEntityByName(notificationTemplate, isActive).map { it.toDomain() }
    }

    override fun findAllActive(): List<PushNotificationTemplate> {
        return dynamoDAO.findBeginsWithOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            SCAN_KEY,
            "${activeFilter(true)}#",
        ).map { it.toDomain() }
    }

    override fun findAll(): List<PushNotificationTemplate> {
        return dynamoDAO.findByPartitionKeyOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            SCAN_KEY,
        ).map { it.toDomain() }
    }

    override fun deactivate(notificationTemplate: NotificationTemplate, deactivatedBy: AccountId) {
        findEntityByName(notificationTemplate, true).forEach {
            deactivateEntity(it, deactivatedBy)
        }
    }

    private fun activeFilter(isActive: Boolean?) = when (isActive) {
        true -> "ACTIVE"
        false -> "DISABLED"
        null -> ""
    }

    private fun findEntityByName(notificationTemplate: NotificationTemplate, isActive: Boolean?): List<PushNotificationTemplateEntity> {
        return dynamoDAO.findBeginsWithOnSortKey(notificationTemplate.value, "$SCAN_KEY#").filter {
            isActive == null || isActive == it.active
        }
    }

    private fun deactivatePreviousBySave(
        newTemplate: PushNotificationTemplate,
    ) {
        findEntityByName(newTemplate.template, true).forEach { found ->
            deactivateEntity(found, newTemplate.lastUpdatedBy)
        }
    }

    private fun deactivateEntity(
        entity: PushNotificationTemplateEntity,
        disabledBy: AccountId,
    ) {
        val dateTimeStr = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
        dynamoDAO.save(
            entity.apply {
                gSIndex1RangeKey = "${activeFilter(false)}#${entity.template}"
                this.active = false
                updatedAt = dateTimeStr
                updatedBy = disabledBy.value
            },
        )
    }
}

private fun PushNotificationTemplateEntity.toDomain(): PushNotificationTemplate {
    return PushNotificationTemplate(
        template = NotificationTemplate(this.template),
        title = this.title,
        message = this.message,
        url = this.url,
        isActive = this.active,
        lastUpdated = ZonedDateTime.parse(this.updatedAt, DateTimeFormatter.ISO_DATE_TIME),
        lastUpdatedBy = AccountId(this.updatedBy),
    )
}

@DynamoDbBean
class PushNotificationTemplateEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1RangeKey: String

    @get:DynamoDbAttribute("Template")
    lateinit var template: String

    @get:DynamoDbAttribute("Title")
    lateinit var title: String

    @get:DynamoDbAttribute("Message")
    lateinit var message: String

    @get:DynamoDbAttribute("Url")
    var url: String? = null

    @get:DynamoDbAttribute("Version")
    var version: Int = 1

    @get:DynamoDbAttribute("IsActive")
    var active: Boolean = true

    @get:DynamoDbAttribute("CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute("UpdatedAt")
    lateinit var updatedAt: String

    @get:DynamoDbAttribute("CreatedBy")
    lateinit var createdBy: String

    @get:DynamoDbAttribute("UpdatedBy")
    lateinit var updatedBy: String
}