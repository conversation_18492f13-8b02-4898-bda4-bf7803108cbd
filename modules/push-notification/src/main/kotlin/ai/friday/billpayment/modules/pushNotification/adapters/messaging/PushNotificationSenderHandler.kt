package ai.friday.billpayment.modules.pushNotification.adapters.messaging

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.notification.PushNotificationContent
import ai.friday.billpayment.modules.pushNotification.PushNotificationModuleNoTest
import ai.friday.billpayment.modules.pushNotification.app.DefaultPushNotificationService
import ai.friday.morning.messaging.MessageHandler
import ai.friday.morning.messaging.MessageHandlerResponse
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

@PushNotificationModuleNoTest
open class PushNotificationSenderHandler(
    private val pushNotificationService: DefaultPushNotificationService,
) : MessageHandler {
    private val logger = LoggerFactory.getLogger(this::class.java)
    private val logName = "PushNotificationSenderHandler"

    override val configurationName = "push-notification"

    override fun handleMessage(message: Message): MessageHandlerResponse {
        val messageBody = parseObjectFrom<PushNotificationQueueMessage>(message.body())

        pushNotificationService.sendNotification(
            messageBody.accountId,
            messageBody.content,
        ).getOrThrow()

        return MessageHandlerResponse.delete()
    }

    override fun handleException(e: Exception): MessageHandlerResponse {
        logger.error(Markers.empty(), logName, e)
        return MessageHandlerResponse.keep()
    }
}

data class PushNotificationQueueMessage(
    val accountId: AccountId,
    val content: PushNotificationContent,
)