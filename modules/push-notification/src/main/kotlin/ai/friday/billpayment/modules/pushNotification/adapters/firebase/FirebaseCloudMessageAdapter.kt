package ai.friday.billpayment.modules.pushNotification.adapters.firebase

import ai.friday.billpayment.app.notification.PushNotificationContent
import ai.friday.billpayment.modules.pushNotification.PushNotificationModule
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationEnrollment
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationSender
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationToken
import ai.friday.billpayment.modules.pushNotification.app.SendPushNotificationResult
import ai.friday.morning.log.andAppend
import com.google.auth.oauth2.GoogleCredentials
import com.google.firebase.ErrorCode
import com.google.firebase.FirebaseApp
import com.google.firebase.FirebaseOptions
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.messaging.FirebaseMessagingException
import com.google.firebase.messaging.Message
import com.google.firebase.messaging.Notification
import io.micronaut.context.annotation.Property
import java.io.ByteArrayInputStream
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@PushNotificationModule
class FirebaseCloudMessageAdapter(
    @Property(name = "integrations.firebase.cloud-messaging.json") private val firebaseJsonContent: String,
) : PushNotificationSender {

    private val logger = LoggerFactory.getLogger(this::class.java)
    private val initialized: Boolean = try {
        val credentials = GoogleCredentials.fromStream(ByteArrayInputStream(firebaseJsonContent.toByteArray()))
        val options = FirebaseOptions.builder()
            .setCredentials(credentials)
            .build()
        FirebaseApp.initializeApp(options)
        logger.info(append("initialized", true), "FirebaseCloudMessageAdapter#init")
        true
    } catch (e: Exception) {
        logger.error(append("initialized", false), "FirebaseCloudMessageAdapter#init", e)
        false
    }

    override fun sendNotification(enrollment: PushNotificationEnrollment.Active, content: PushNotificationContent): SendPushNotificationResult {
        return send(enrollment.token, content)
    }

    private fun send(token: PushNotificationToken, content: PushNotificationContent): SendPushNotificationResult {
        val logName = "FirebaseCloudMessageAdapter#sendNotification"
        val markers = append("content", content)
        if (!initialized) {
            logger.error(append("errorMessage", "Firebase not initialized"), logName)
            return SendPushNotificationResult.Failure(IllegalStateException("Firebase not initialized"))
        }

        try {
            val notificationBuilder = Notification.builder()
                .setTitle(content.title)
                .setBody(content.body)

            content.imageUrl?.let {
                notificationBuilder.setImage(it.toString())
            }

            val messageBuilder = Message.builder()
                .setToken(token.value)
                .setNotification(notificationBuilder.build())

            content.url?.let {
                messageBuilder.putData("url", it.toString())
            }

            val response = FirebaseMessaging.getInstance().send(messageBuilder.build())
            logger.info(markers.andAppend("response", response), logName)
            return SendPushNotificationResult.Success
        } catch (e: FirebaseMessagingException) {
            return when (e.errorCode) {
                ErrorCode.NOT_FOUND -> {
                    logger.info(markers.andAppend("response", e.errorCode), logName)
                    SendPushNotificationResult.EnrollmentDisabled
                }

                else -> {
                    logger.error(append("message", content), logName, e)
                    SendPushNotificationResult.Failure(e)
                }
            }
        }
    }
}