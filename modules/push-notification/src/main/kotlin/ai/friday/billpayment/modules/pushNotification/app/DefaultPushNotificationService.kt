package ai.friday.billpayment.modules.pushNotification.app

import ai.friday.billpayment.adapters.notification.BILL_PAYMENT_LIMIT_TIME
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.OpenFinanceIncentiveType
import ai.friday.billpayment.app.notification.BillComingDueLastWarnDetails
import ai.friday.billpayment.app.notification.BillComingDueRegularDetails
import ai.friday.billpayment.app.notification.BillPaymentNotification
import ai.friday.billpayment.app.notification.ChatbotNotification
import ai.friday.billpayment.app.notification.EmailNotification
import ai.friday.billpayment.app.notification.MultiChannelNotification
import ai.friday.billpayment.app.notification.NotificationTemplate
import ai.friday.billpayment.app.notification.OpenFinanceIncentiveDetails
import ai.friday.billpayment.app.notification.PixTransactionDetails
import ai.friday.billpayment.app.notification.PushNotificationContent
import ai.friday.billpayment.app.notification.PushNotificationPublisher
import ai.friday.billpayment.app.notification.PushNotificationService
import ai.friday.billpayment.app.notification.RegisterCompleted
import ai.friday.billpayment.app.notification.SendPushAsyncResult
import ai.friday.billpayment.app.notification.SendPushNotificationToAccountIdResult
import ai.friday.billpayment.app.notification.TestPixreminderDetails
import ai.friday.billpayment.app.notification.UpdateAccountStatus
import ai.friday.billpayment.app.notification.WelcomeDetails
import ai.friday.billpayment.app.notification.WhatsappNotification
import ai.friday.billpayment.app.notification.buildWalletTimelinePath
import ai.friday.billpayment.app.notification.buildWhatsAppTrackedDeeplinkParameter
import ai.friday.billpayment.markers
import ai.friday.billpayment.modules.pushNotification.PushNotificationModule
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.ConfigurationProperties
import java.net.URI
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@ConfigurationProperties("communication-centre.integration.blip")
class KnownWhatsappTemplatesProps {
    lateinit var templates: Map<String, String>
}

@PushNotificationModule
class DefaultPushNotificationService(
    private val pushNotificationSender: PushNotificationSender,
    private val pushNotificationPublisher: PushNotificationPublisher,
    private val pushNotificationEnrollmentRepository: PushNotificationEnrollmentRepository,
    private val pushNotificationTemplateService: PushNotificationTemplateService,
    private val pushNotificationHistoryRepository: PushNotificationHistoryRepository,
    knownWhatsappTemplatesProps: KnownWhatsappTemplatesProps,

) : PushNotificationService {
    private val logger = LoggerFactory.getLogger(this::class.java)

    private val knownWhatsappTemplatesPropsByValue = knownWhatsappTemplatesProps.templates.entries.associate { (key, value) -> value to key } // TODO - se houver 2 templates com o mesmo nome vai valer apenas o ultimo

    fun upsert(accountId: AccountId, token: PushNotificationToken): PushNotificationEnrollment.Active {
        return pushNotificationEnrollmentRepository.upsert(accountId, token)
    }

    override fun sendNotificationAsync(accountId: AccountId, billPaymentNotification: BillPaymentNotification): Result<SendPushAsyncResult> = runCatching {
        val content = billPaymentNotification.toPushNotificationData()
        content?.let {
            return sendNotificationAsync(accountId = accountId, content)
        } ?: SendPushAsyncResult.UnableToCreatePush
    }

    fun sendNotificationAsync(accountId: AccountId, pushNotificationData: PushNotificationData): Result<SendPushAsyncResult> = runCatching {
        pushNotificationData.toPushNotificationContent()?.let { push ->
            return sendNotificationAsync(accountId, content = push)
        } ?: SendPushAsyncResult.TemplateNotFound
    }

    override fun sendNotificationAsync(accountId: AccountId, content: PushNotificationContent): Result<SendPushAsyncResult> = runCatching {
        return pushNotificationPublisher.publish(accountId, content)
    }

    override fun sendNotification(accountId: AccountId, content: PushNotificationContent): Result<SendPushNotificationToAccountIdResult> = runCatching {
        val allEnrollments = pushNotificationEnrollmentRepository.findByAccountId(accountId)

        var sent = 0
        var failures = 0
        var disabled = 0
        var stale = 0
        var purged = 0

        allEnrollments.forEach {
            when (it) {
                is PushNotificationEnrollment.Active -> {
                    val result = pushNotificationSender.sendNotification(it, content)
                    when (result) {
                        SendPushNotificationResult.EnrollmentDisabled -> {
                            disabled++
                            disableEnrollment(it, accountId)
                        }

                        is SendPushNotificationResult.Failure -> failures++
                        SendPushNotificationResult.Success -> sent++
                    }
                }

                is PushNotificationEnrollment.Stale -> {
                    if (it.shouldBePurged()) {
                        purged++
                        purgeEnrollment(it, accountId)
                    } else {
                        stale++
                    }
                }
            }
        }

        SendPushNotificationToAccountIdResult(sent = sent, failures = failures, disabled = disabled, stale = stale, purged = purged).also {
            pushNotificationHistoryRepository.save(
                accountId = accountId,
                content = content,
                result = it,
            )
            logger.info(
                markers(
                    "accountId" to accountId.value,
                    "content" to content,
                    "result" to it,
                ),
                "PushNotificationService#sendNotification",
            )
        }
    }

    override fun canSendViaPush(billPaymentNotification: BillPaymentNotification): Boolean {
        val pushNotificationTemplate = billPaymentNotification.toPushNotificationData() ?: return false
        pushNotificationTemplateService.getActiveTemplate(pushNotificationTemplate.template) ?: return false
        return true
    }

    private fun disableEnrollment(enrollment: PushNotificationEnrollment.Active, accountId: AccountId) {
        pushNotificationEnrollmentRepository.delete(accountId = accountId, token = enrollment.token)
    }

    // TODO: quando estivermos confiantes que devemos fazer o purge, apagar os tokens do repositório (hard ou soft delete?).
    private fun purgeEnrollment(enrollment: PushNotificationEnrollment.Stale, accountId: AccountId) {
        logger.info(
            markers(
                "enrollment" to enrollment,
                "accountId" to accountId.value,
            ),
            "PushNotificationService#purgeEnrollment",
        )
    }

    private fun PushNotificationData.toPushNotificationContent(): PushNotificationContent? {
        val pushNotificationRawTemplate = pushNotificationTemplateService.getActiveTemplate(this.template) ?: return null
        val url = this.urlParameter?.let { replaceURLParameter(pushNotificationRawTemplate, it) } ?: pushNotificationRawTemplate.url

        return PushNotificationContent(
            body = replaceTemplateParameters(pushNotificationRawTemplate.message, this.parameters),
            title = replaceTemplateParameters(pushNotificationRawTemplate.title, this.parameters),
            url = url?.let { URI.create(it) },
            imageUrl = null,
        )
    }

    private fun replaceTemplateParameters(rawMessage: String, parameters: Map<String, String>): String {
        var notificationMessageString = rawMessage
        parameters.map { entry ->
            notificationMessageString = notificationMessageString.replace("{{${entry.key}}}", entry.value)
        }

        if (notificationMessageString.contains("{{") && notificationMessageString.contains("}}")) {
            logger.error(append("message", rawMessage).andAppend("context", "Push Template parameters not fully replaced").andAppend("availableParameters", parameters).andAppend("ACTION", "VERIFY"), "PushNotificationService#replaceTemplateParameters")
        }

        return notificationMessageString
    }

    private fun replaceURLParameter(rawMessage: PushNotificationTemplate, parameter: String): String? {
        return rawMessage.url?.replace("{{1}}", parameter)
    }

    private fun BillPaymentNotification.toPushNotificationData(): PushNotificationData? {
        return when (this) {
            is ChatbotNotification -> toPushNotificationData()
            is EmailNotification -> null
            is WhatsappNotification -> toPushNotificationData()
            is MultiChannelNotification -> toPushNotificationData()
        }
    }

    private fun ChatbotNotification.toPushNotificationData(): PushNotificationData {
        val push = when (val details = this.details) {
            is BillComingDueLastWarnDetails -> PushNotificationData(
                template = NotificationTemplate(details.javaClass.simpleName),
                urlParameter = buildWalletTimelinePath(walletId),
                parameters = details.bills.map { it.paymentLimitTime }.minByOrNull { it }?.format(DateTimeFormatter.ofPattern("HH:mm"))?.let { mapOf(BILL_PAYMENT_LIMIT_TIME to it) } ?: emptyMap(),
            )

            is BillComingDueRegularDetails -> PushNotificationData(
                template = NotificationTemplate(details.javaClass.simpleName),
                urlParameter = buildWalletTimelinePath(walletId),
                parameters = emptyMap(),
            )

            is OpenFinanceIncentiveDetails -> {
                val (eventType, template) = when (details.type) {
                    OpenFinanceIncentiveType.DDA -> "of_incentive.click_dda" to NotificationTemplate("OpenFinanceIncentiveDetails_DDA")
                    OpenFinanceIncentiveType.CASH_IN -> "of_incentive.click_cash_in" to NotificationTemplate("OpenFinanceIncentiveDetails_CASH_IN")
                } // TODO - precisamos validar a logica de envio. Hoje está no chatbot.

                PushNotificationData(
                    template = template,
                    urlParameter = buildWhatsAppTrackedDeeplinkParameter("open-finance", eventType).value,
                    parameters = emptyMap(),
                )
            }

            is PixTransactionDetails,
            RegisterCompleted,
            is TestPixreminderDetails,
            is UpdateAccountStatus,
            is WelcomeDetails,
            -> PushNotificationData(
                template = NotificationTemplate(details.javaClass.simpleName),
                urlParameter = buildWalletTimelinePath(walletId),
                parameters = emptyMap(),
            )
        }
        return push
    }

    private fun MultiChannelNotification.toPushNotificationData(): PushNotificationData {
        return PushNotificationData(
            template = NotificationTemplate(configurationName.name),
            urlParameter = this.actionButton?.value,
            parameters = parameters.mapKeys { it.key },
        )
    }

    @Deprecated("apagar junto com o WhatsappNotification")
    private fun WhatsappNotification.toPushNotificationData(): PushNotificationData? {
        logger.warn(append("template", this.template.value).andAppend("context", "WhatsappNotification#toPushNotificationFromTemplate está deprecated. Será usado position parameter ao invés de named parameter"), "PushNotificationService#toPushNotificationFromTemplate")
        return PushNotificationData(
            template = knownWhatsappTemplatesPropsByValue[template.value]?.let { NotificationTemplate(it) } ?: return null,
            urlParameter = buttonWhatsAppParameter?.value,
            parameters = parameters.mapIndexed { index, parameter -> (index + 1).toString() to parameter }.toMap(),
        )
    }
}