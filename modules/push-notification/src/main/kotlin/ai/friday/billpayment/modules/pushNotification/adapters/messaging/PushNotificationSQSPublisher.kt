package ai.friday.billpayment.modules.pushNotification.adapters.messaging

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.message.QueueMessage
import ai.friday.billpayment.app.notification.PushNotificationContent
import ai.friday.billpayment.app.notification.PushNotificationPublisher
import ai.friday.billpayment.app.notification.SendPushAsyncResult
import ai.friday.billpayment.modules.pushNotification.PushNotificationModule
import ai.friday.morning.json.getObjectMapper
import io.micronaut.context.annotation.Property

@PushNotificationModule
open class PushNotificationSQSPublisher(
    private val messagePublisher: MessagePublisher,
    @Property(name = "sqs.queues.pushNotification") private val pushNotificationQueueName: String,
) : PushNotificationPublisher {
    override fun publish(accountId: AccountId, content: PushNotificationContent): Result<SendPushAsyncResult> = runCatching {
        messagePublisher.sendMessage(
            QueueMessage(
                queueName = pushNotificationQueueName,
                jsonObject = getObjectMapper().writeValueAsString(
                    PushNotificationQueueMessage(
                        accountId = accountId,
                        content = content,
                    ),
                ),
                delaySeconds = null,
            ),
        )
        SendPushAsyncResult.Requested
    }
}