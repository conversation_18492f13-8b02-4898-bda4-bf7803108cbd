package ai.friday.billpayment.modules.pushNotification.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.AbstractBillPaymentDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.modules.pushNotification.PushNotificationModule
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationEnrollment
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationEnrollmentRepository
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationToken
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val PUSH_NOTIFICATION_TOKEN_PREFIX = "PUSH-NOTIFICATION-TOKEN"

@PushNotificationModule
class PushNotificationEnrollmentDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<PushNotificationEnrollmentEntity>(cli, PushNotificationEnrollmentEntity::class.java)

@PushNotificationModule
class PushNotificationEnrollmentDbEnrollmentRepository(
    private val client: PushNotificationEnrollmentDynamoDAO,
) : PushNotificationEnrollmentRepository {

    override fun upsert(accountId: AccountId, token: PushNotificationToken): PushNotificationEnrollment.Active {
        val entity = save(accountId, token, enabled = true)
        val model = entity.buildPushNotificationEnrollment()

        if (model == null || model !is PushNotificationEnrollment.Active) {
            throw IllegalStateException("Recently upserted enrollment must exist and be active")
        }

        return model
    }

    override fun delete(accountId: AccountId, token: PushNotificationToken) {
        val entity = client.findByPrimaryKey(
            accountId.value,
            scanKey(token),
        )

        if (entity != null && entity.enabled) {
            save(accountId = accountId, token = token, enabled = false)
        }
    }

    override fun findByAccountId(accountId: AccountId): List<PushNotificationEnrollment> {
        return client.findByPartitionKey(
            partitionKey = accountId.value,
        ).mapNotNull { it.buildPushNotificationEnrollment() }
    }

    override fun findByToken(
        accountId: AccountId,
        pushNotificationToken: PushNotificationToken,
    ): PushNotificationEnrollment? {
        val pushNotificationEnrollment = client.findByPrimaryKey(
            accountId.value,
            scanKey(pushNotificationToken),
        )

        return pushNotificationEnrollment?.buildPushNotificationEnrollment()
    }

    private fun save(accountId: AccountId, token: PushNotificationToken, enabled: Boolean): PushNotificationEnrollmentEntity {
        val entity = PushNotificationEnrollmentEntity().apply {
            partitionKey = accountId.value
            sortKey = scanKey(token)
            this.accountId = accountId.value
            pushNotificationToken = token.value
            this.enabled = enabled
        }

        client.save(entity)

        return entity
    }

    private fun PushNotificationEnrollmentEntity.buildPushNotificationEnrollment(): PushNotificationEnrollment? {
        if (!this.enabled) {
            return null
        }

        return PushNotificationEnrollment.create(
            accountId = AccountId(this.accountId),
            token = PushNotificationToken(this.pushNotificationToken),
            updatedAt = ZonedDateTime.parse(this.updatedAt, DateTimeFormatter.ISO_DATE_TIME),
        )
    }
}

private fun scanKey(pushNotificationToken: PushNotificationToken): String {
    return "$PUSH_NOTIFICATION_TOKEN_PREFIX#${pushNotificationToken.value}"
}

@DynamoDbBean
class PushNotificationEnrollmentEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String

    @get:DynamoDbAttribute(value = "AccountId")
    lateinit var accountId: String

    @get:DynamoDbAttribute(value = "PushNotificationToken")
    lateinit var pushNotificationToken: String

    @get:DynamoDbAttribute(value = "Enabled")
    var enabled: Boolean = false

    @get:DynamoDbAttribute(value = "UpdatedAt")
    var updatedAt: String = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
}