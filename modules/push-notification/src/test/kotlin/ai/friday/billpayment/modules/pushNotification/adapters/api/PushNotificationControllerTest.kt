package ai.friday.billpayment.modules.pushNotification.adapters.api

import DynamoDBUtils.getDynamoDbClient
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.notification.SendPushAsyncResult
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.modules.pushNotification.adapters.dynamodb.PushNotificationEnrollmentDbEnrollmentRepository
import ai.friday.billpayment.modules.pushNotification.adapters.dynamodb.PushNotificationEnrollmentDynamoDAO
import ai.friday.billpayment.modules.pushNotification.app.DefaultPushNotificationService
import ai.friday.billpayment.modules.pushNotification.app.KnownWhatsappTemplatesProps
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationEnrollment
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationHistoryRepository
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationSender
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationTemplateService
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationToken
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpStatus
import io.micronaut.security.authentication.Authentication
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient

internal class PushNotificationControllerTest {
    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val dynamoDbEnhancedClient = DynamoDbEnhancedClient.builder()
        .dynamoDbClient(getDynamoDbClient())
        .build()

    private val pushNotificationEnrollmentDynamoDAO = PushNotificationEnrollmentDynamoDAO(dynamoDbEnhancedClient)
    private val pushNotificationEnrollmentRepository =
        PushNotificationEnrollmentDbEnrollmentRepository(
            pushNotificationEnrollmentDynamoDAO,
        )
    private val pushNotificationToken = "d938j98j3982j98dj9283jd2"
    private val pushNotificationSender = mockk<PushNotificationSender>(relaxed = true)
    private val pushNotificationTemplateService = mockk<PushNotificationTemplateService>(relaxUnitFun = true)
    private val pushNotificationHistoryRepository = mockk<PushNotificationHistoryRepository>(relaxUnitFun = true)
    private val defaultPushNotificationSenderService = DefaultPushNotificationService(
        pushNotificationSender = pushNotificationSender,
        pushNotificationPublisher = mockk {
            every { publish(any(), any()) } returns Result.success(SendPushAsyncResult.Requested)
        },
        pushNotificationEnrollmentRepository = pushNotificationEnrollmentRepository,
        pushNotificationTemplateService = pushNotificationTemplateService,
        pushNotificationHistoryRepository = pushNotificationHistoryRepository,
        knownWhatsappTemplatesProps = KnownWhatsappTemplatesProps().apply { templates = emptyMap() },
    )
    private val pushNotificationController = PushNotificationController(defaultPushNotificationSenderService)
    private val accountId = AccountId(ACCOUNT_ID)

    private val authentication =
        mockk<Authentication> { every { name } returns ACCOUNT_ID }

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `deve salvar um pushNotificationToken`() {
        val now = getZonedDateTime()

        val response = withGivenDateTime(now) {
            pushNotificationController.addPushNotification(authentication, PushNotificationTO(pushNotificationToken))
        }

        response.status() shouldBe HttpStatus.NO_CONTENT
        pushNotificationEnrollmentRepository.findByAccountId(AccountId(ACCOUNT_ID)) shouldBe
            listOf(PushNotificationEnrollment.Active(token = PushNotificationToken(pushNotificationToken), updatedAt = now))
    }

    @Test
    fun `deve salvar um segundo pushNotificationToken`() {
        pushNotificationEnrollmentRepository.upsert(
            accountId,
            token = PushNotificationToken("outro"),
        )

        val response =
            pushNotificationController.addPushNotification(authentication, PushNotificationTO(pushNotificationToken))

        response.status() shouldBe HttpStatus.NO_CONTENT

        pushNotificationEnrollmentRepository.findByAccountId(AccountId(ACCOUNT_ID)).size shouldBe 2
    }

    @Test
    fun `nao deve salvar o mesmo pushNotificationToken`() {
        val now = getZonedDateTime()

        withGivenDateTime(now.minusDays(10)) {
            pushNotificationEnrollmentRepository.upsert(
                accountId,
                token = PushNotificationToken(pushNotificationToken),
            )
        }

        val response = withGivenDateTime(now) {
            pushNotificationController.addPushNotification(authentication, PushNotificationTO(pushNotificationToken))
        }

        response.status() shouldBe HttpStatus.NO_CONTENT
        pushNotificationEnrollmentRepository.findByAccountId(AccountId(ACCOUNT_ID)) shouldBe
            listOf(PushNotificationEnrollment.Active(token = PushNotificationToken(pushNotificationToken), updatedAt = now))
    }
}