package ai.friday.billpayment.modules.pushNotification.adapters.dynamodb

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.notification.PushNotificationContent
import ai.friday.billpayment.app.notification.SendPushNotificationToAccountIdResult
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldHaveSize
import java.net.URI
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class PushNotificationHistoryDynamoDBRepositoryTest {
    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val enhancedClient = setupDynamoDB()
    private val pushNotificationHistoryDynamoDAO = PushNotificationHistoryDynamoDAO(enhancedClient)
    private val repository = PushNotificationHistoryDynamoDBRepository(pushNotificationHistoryDynamoDAO)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `deve salvar e buscar histórico de push notification`() {
        val now = getZonedDateTime()
        val accountId = AccountId(ACCOUNT_ID)
        val content = PushNotificationContent(
            title = "Título de Teste",
            body = "Mensagem de Teste",
            url = URI.create("https://meusite.com"),
            imageUrl = URI.create("https://meusite.com/imagem.png"),
            sentAt = now,
        )
        val content2 = PushNotificationContent(
            title = "Título de Teste2",
            body = "Mensagem de Teste2",
            url = URI.create("https://meusite.com"),
            imageUrl = URI.create("https://meusite.com/imagem.png"),
            sentAt = now.plusDays(1),
        )

        val result = SendPushNotificationToAccountIdResult(
            sent = 1,
            failures = 0,
            disabled = 0,
            stale = 0,
            purged = 0,
        )

        repository.save(accountId, content, result)
        repository.save(accountId, content2, result)

        val found = repository.findByAccountId(accountId)
        found shouldHaveSize 2
        found shouldContainExactlyInAnyOrder listOf(content, content2)
    }

    @Test
    fun `deve retornar lista vazia quando não houver histórico para o accountId`() {
        val accountId = AccountId("nao-existe")
        val found = repository.findByAccountId(accountId)
        found shouldHaveSize 0
    }
}