package ai.friday.billpayment.modules.pushNotification.adapters.dynamodb

import DynamoDBUtils.getDynamoDbClient
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationEnrollment
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationToken
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient

internal class PushNotificationEnrollmentDbEnrollmentRepositoryTest {
    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val dynamoDbEnhancedClient = DynamoDbEnhancedClient.builder()
        .dynamoDbClient(getDynamoDbClient())
        .build()

    private val pushNotificationEnrollmentDynamoDAO = PushNotificationEnrollmentDynamoDAO(dynamoDbEnhancedClient)
    private val pushNotificationEnrollmentDbEnrollmentRepository =
        PushNotificationEnrollmentDbEnrollmentRepository(
            pushNotificationEnrollmentDynamoDAO,
        )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `cria um push notification enrollment e lê do banco`() {
        val now = getZonedDateTime()
        val token = PushNotificationToken("*************")

        withGivenDateTime(now) {
            pushNotificationEnrollmentDbEnrollmentRepository.upsert(
                AccountId(ACCOUNT_ID),
                token = token,
            )
        }

        val storedEnrollment = pushNotificationEnrollmentDbEnrollmentRepository.findByToken(
            AccountId(ACCOUNT_ID),
            token,
        )

        storedEnrollment shouldBe PushNotificationEnrollment.Active(token, updatedAt = now)
    }
}