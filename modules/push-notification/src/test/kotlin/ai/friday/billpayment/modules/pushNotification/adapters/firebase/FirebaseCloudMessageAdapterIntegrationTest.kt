/*
package ai.friday.billpayment.modules.pushNotification.adapters.firebase

import ai.friday.billpayment.modules.pushNotification.app.PushNotificationEnrollment
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationMessage
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationToken
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
class FirebaseCloudMessageAdapterIntegrationTest {

    private val firebaseCloudMessageAdapter = FirebaseCloudMessageAdapter(
        firebaseJsonContent = """

        """.trimIndent(),
    )

    @Test
    fun sendTest() {
        val tokenMarlon = "cCcTb7KuQlqWy7I72zlCPr:APA91bGskFNTzjUhYOvg9YHv21HDmLLc_qnNl1jJEWtmbBpfSuguWG88J5W-dU1XY7dgtx9m6tz8tUYThJ7KR83UbssG33BK0Vva7OZ5Qeg_awMq5hDoJpeqbMEXpW0pX7KRkfi5_Akq"
        val tokenJoao = "fHq-L5tcdkpZvawEE259TZ:APA91bGnThDzQuedkajEz6XJLC_ta3NArYgh8gEhdFt0dNGvPzKkIXBs8qHsN95CyhD6vSAX_QKyBaTakSF0sZpLwa5oeHT9FYGIFKX2buKeViu5YqNlaXW8G_Cm6oGRVkKg4g1vosdL"

        val message = PushNotificationMessage(
            name = "message name",
            template = "message template",
            message = "message body",
            title = "message title",
            url = "https://use-me-poupe-contas.meupagador.com.br/entrar",
        )
        val result = firebaseCloudMessageAdapter.sendNotification(
            pushNotificationEnrollment = PushNotificationEnrollment(
                token = PushNotificationToken(value = tokenMarlon),
                enabled = true,
            ),
            pushNotificationMessage = message,
        )
        println(result)
    }
}*/