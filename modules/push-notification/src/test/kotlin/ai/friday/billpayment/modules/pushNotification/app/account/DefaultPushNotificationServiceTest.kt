package ai.friday.billpayment.modules.pushNotification.app.account

import DynamoDBUtils.getDynamoDbClient
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.notification.PushNotificationContent
import ai.friday.billpayment.app.notification.SendPushAsyncResult
import ai.friday.billpayment.app.notification.SendPushNotificationToAccountIdResult
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.modules.pushNotification.adapters.dynamodb.PushNotificationEnrollmentDbEnrollmentRepository
import ai.friday.billpayment.modules.pushNotification.adapters.dynamodb.PushNotificationEnrollmentDynamoDAO
import ai.friday.billpayment.modules.pushNotification.app.DefaultPushNotificationService
import ai.friday.billpayment.modules.pushNotification.app.KnownWhatsappTemplatesProps
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationEnrollment
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationHistoryRepository
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationSender
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationTemplateService
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationToken
import ai.friday.billpayment.modules.pushNotification.app.SendPushNotificationResult
import ai.friday.billpayment.modules.pushNotification.app.staleEnrollmentCutOffDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.result.shouldBeSuccess
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.net.URI
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient

class DefaultPushNotificationServiceTest {
    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val dynamoDbEnhancedClient = DynamoDbEnhancedClient.builder()
        .dynamoDbClient(getDynamoDbClient())
        .build()

    private val pushNotificationSender = mockk<PushNotificationSender>(relaxUnitFun = true)
    private val pushNotificationTemplateService = mockk<PushNotificationTemplateService>(relaxUnitFun = true)
    private val pushNotificationHistoryRepository = mockk<PushNotificationHistoryRepository>(relaxUnitFun = true)

    private val pushNotificationEnrollmentDynamoDAO = PushNotificationEnrollmentDynamoDAO(dynamoDbEnhancedClient)
    private val pushNotificationEnrollmentRepository = PushNotificationEnrollmentDbEnrollmentRepository(client = pushNotificationEnrollmentDynamoDAO)

    private val defaultPushNotificationSenderService = DefaultPushNotificationService(
        pushNotificationSender = pushNotificationSender,
        pushNotificationPublisher = mockk {
            every { publish(any(), any()) } returns Result.success(SendPushAsyncResult.Requested)
        },
        pushNotificationEnrollmentRepository = pushNotificationEnrollmentRepository,
        pushNotificationTemplateService = pushNotificationTemplateService,
        pushNotificationHistoryRepository = pushNotificationHistoryRepository,
        knownWhatsappTemplatesProps = KnownWhatsappTemplatesProps().apply { templates = emptyMap() },
    )

    private val token = PushNotificationToken("token")

    private val accountId = AccountId(ACCOUNT_ID)

    private val content =
        PushNotificationContent(body = "test", title = "test", url = URI.create("https://foo.com/test"), imageUrl = null)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Nested
    @DisplayName("Save")
    inner class Save {
        @Test
        fun `se o token não existir deve salvar o token`() {
            val now = getZonedDateTime()

            withGivenDateTime(now) {
                defaultPushNotificationSenderService.upsert(accountId, token) shouldBe
                    PushNotificationEnrollment.Active(token, updatedAt = now)
            }

            pushNotificationEnrollmentRepository.findByToken(accountId, token) shouldBe
                PushNotificationEnrollment.Active(token, updatedAt = now)
        }

        @Test
        fun `se o token existir mas não estiver habilitado deve habilitar e atualizar a hora`() {
            val now = getZonedDateTime()

            withGivenDateTime(now.minusDays(3)) {
                pushNotificationEnrollmentRepository.upsert(accountId, token)
                pushNotificationEnrollmentRepository.delete(accountId, token)
            }

            val updatedEnrollment = withGivenDateTime(now) {
                defaultPushNotificationSenderService.upsert(accountId, token)
            }

            updatedEnrollment shouldBe PushNotificationEnrollment.Active(token, updatedAt = now)
            pushNotificationEnrollmentRepository.findByToken(accountId, token) shouldBe updatedEnrollment
        }

        @Test
        fun `se o token existir e estiver habilitado deve atualizar a hora`() {
            val now = getZonedDateTime()

            withGivenDateTime(now.minusDays(10)) {
                pushNotificationEnrollmentRepository.upsert(accountId, token)
            }

            val updatedEnrollment = withGivenDateTime(now) {
                defaultPushNotificationSenderService.upsert(accountId, token)
            }

            updatedEnrollment shouldBe PushNotificationEnrollment.Active(token, updatedAt = now)
            pushNotificationEnrollmentRepository.findByToken(accountId, token) shouldBe updatedEnrollment
        }
    }

    @Nested
    @DisplayName("sendNotification")
    inner class SendNotification {
        @Test
        fun `se o usuário possui n tokens válidos deve enviar n push notifications`() {
            val now = staleEnrollmentCutOffDate.plusYears(1)

            withGivenDateTime(staleEnrollmentCutOffDate) {
                pushNotificationEnrollmentRepository.upsert(accountId, PushNotificationToken("token-1"))
            }

            withGivenDateTime(now) {
                pushNotificationEnrollmentRepository.upsert(accountId, token = PushNotificationToken("token-2"))
            }

            withGivenDateTime(now.minusDays(30)) {
                pushNotificationEnrollmentRepository.upsert(accountId, token = PushNotificationToken("token-3"))
            }

            every { pushNotificationSender.sendNotification(any(), any()) } returns SendPushNotificationResult.Success

            withGivenDateTime(now) {
                defaultPushNotificationSenderService.sendNotification(
                    accountId,
                    content,
                )
            }

            verify(exactly = 3) { pushNotificationSender.sendNotification(any(), any()) }
        }

        @Test
        fun `não deve notificar se o usuário não possui nenhum token`() {
            defaultPushNotificationSenderService.sendNotification(accountId, content)

            verify(exactly = 0) { pushNotificationSender.sendNotification(any(), any()) }
        }

        @Test
        fun `não deve notificar se o usuário só possui token stale ou desabilitado`() {
            val now = staleEnrollmentCutOffDate.plusYears(1)

            withGivenDateTime(now) {
                val token = PushNotificationToken("token-1") // disabled (nem é contabilizada)

                pushNotificationEnrollmentRepository.upsert(accountId, token)
                pushNotificationEnrollmentRepository.delete(accountId, token)
            }

            withGivenDateTime(staleEnrollmentCutOffDate.plusDays(1)) {
                pushNotificationEnrollmentRepository.upsert(accountId, PushNotificationToken("token-2")) // purged
            }

            withGivenDateTime(now.minusDays(180)) {
                pushNotificationEnrollmentRepository.upsert(accountId, PushNotificationToken("token-3")) // purged
            }

            withGivenDateTime(now.minusDays(31)) {
                pushNotificationEnrollmentRepository.upsert(accountId, PushNotificationToken("token-4")) // stale
            }

            val result = withGivenDateTime(now) {
                defaultPushNotificationSenderService.sendNotification(accountId, content)
            }

            verify(exactly = 0) { pushNotificationSender.sendNotification(any(), any()) }

            result shouldBeSuccess SendPushNotificationToAccountIdResult(
                sent = 0,
                failures = 0,
                disabled = 0,
                stale = 1,
                purged = 2,
            )
        }

        @Test
        fun `se o envio falhar por EnrollmentDisabled deve desabilitar o token`() {
            val now = getZonedDateTime()

            withGivenDateTime(now) {
                pushNotificationEnrollmentRepository.upsert(accountId, token)
            }

            every { pushNotificationSender.sendNotification(any(), any()) } returns SendPushNotificationResult.EnrollmentDisabled

            withGivenDateTime(now) {
                defaultPushNotificationSenderService.sendNotification(
                    accountId,
                    content,
                )
            }

            verify(exactly = 1) {
                pushNotificationSender.sendNotification(any(), any())
            }

            pushNotificationEnrollmentRepository.findByAccountId(accountId) shouldBe emptyList()
        }

        @Test
        fun `se falhar por outro motivo não deve desabilitar o enrollment`() {
            val now = getZonedDateTime()

            withGivenDateTime(now) {
                pushNotificationEnrollmentRepository.upsert(accountId, token)
            }

            every { pushNotificationSender.sendNotification(any(), any()) } returns SendPushNotificationResult.Failure(Exception())

            withGivenDateTime(now.plusDays(10)) {
                defaultPushNotificationSenderService.sendNotification(accountId, content)
            }

            verify(exactly = 1) {
                pushNotificationSender.sendNotification(any(), any())
            }

            pushNotificationEnrollmentRepository.findByAccountId(accountId) shouldBe
                listOf(PushNotificationEnrollment.Active(token, updatedAt = now))
        }

        @Test
        fun `deve logar a tentativa de envio de push notification`() {
            val now = getZonedDateTime()

            withGivenDateTime(now) {
                pushNotificationEnrollmentRepository.upsert(accountId, token)
            }

            every { pushNotificationSender.sendNotification(any(), any()) } returns SendPushNotificationResult.Success

            withGivenDateTime(now) {
                defaultPushNotificationSenderService.sendNotification(accountId, content)
            }

            verify(exactly = 1) {
                pushNotificationHistoryRepository.save(
                    accountId = accountId,
                    content = content,
                    any(),
                )
            }
        }
    }
}