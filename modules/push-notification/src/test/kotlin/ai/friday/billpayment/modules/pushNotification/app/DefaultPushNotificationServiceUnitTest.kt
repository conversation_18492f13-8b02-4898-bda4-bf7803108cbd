package ai.friday.billpayment.modules.pushNotification.app

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.WelcomeMessageType
import ai.friday.billpayment.app.notification.ChatbotNotificationWithAccount
import ai.friday.billpayment.app.notification.NotificationTemplate
import ai.friday.billpayment.app.notification.PushNotificationContent
import ai.friday.billpayment.app.notification.PushNotificationPublisher
import ai.friday.billpayment.app.notification.SendPushAsyncResult
import ai.friday.billpayment.app.notification.WelcomeDetails
import ai.friday.billpayment.app.notification.createEmailNotification
import ai.friday.billpayment.app.notification.createMultiChannelNotification
import ai.friday.billpayment.app.notification.createWhatsappNotification
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.AccountFixture.createAccount
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.result.shouldBeSuccess
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class DefaultPushNotificationServiceUnitTest {

    private val pushNotificationSender = mockk<PushNotificationSender>(relaxUnitFun = true)
    private val pushNotificationPublisher = mockk<PushNotificationPublisher>(relaxUnitFun = true)
    private val pushNotificationEnrollmentRepository = mockk<PushNotificationEnrollmentRepository>(relaxUnitFun = true)
    private val pushNotificationTemplateService = mockk<PushNotificationTemplateService>(relaxUnitFun = true)
    private val pushNotificationHistoryRepository = mockk<PushNotificationHistoryRepository>(relaxUnitFun = true)
    private val knownWhatsappTemplatesProps = KnownWhatsappTemplatesProps().apply { templates = mapOf("template-key-prop" to "whatsapp-template") }

    private val service = DefaultPushNotificationService(
        pushNotificationSender = pushNotificationSender,
        pushNotificationPublisher = pushNotificationPublisher,
        pushNotificationEnrollmentRepository = pushNotificationEnrollmentRepository,
        pushNotificationTemplateService = pushNotificationTemplateService,
        pushNotificationHistoryRepository = pushNotificationHistoryRepository,
        knownWhatsappTemplatesProps = knownWhatsappTemplatesProps,
    )

    private val accountId = AccountId("test-account-id")
    private val account = createAccount().copy(accountId = accountId)
    private val walletId = WalletId(WALLET_ID)
    private val template = NotificationTemplate("template")

    fun createPushNotificationTemplate(): PushNotificationTemplate {
        return PushNotificationTemplate(
            template = template,
            title = "Test {{TITLE_REP}} Title",
            message = "Test {{MESSAGE_REP}} {{OTHER_VAL}} Message",
            url = null,
            isActive = true,
            lastUpdated = getZonedDateTime(),
            lastUpdatedBy = AccountId(ACCOUNT_ID_2),
        )
    }

    @Nested
    @DisplayName("canSendViaPush")
    inner class CanSendViaPush {

        @Test
        fun `deve retornar true quando ChatbotNotification pode ser convertida para push e o template está ativo`() {
            // given
            val notification = ChatbotNotificationWithAccount(
                walletId = walletId,
                account = account,
                details = WelcomeDetails(WelcomeMessageType.WELCOME_CHATBOT_ONBOARDING),
            )

            every { pushNotificationTemplateService.getActiveTemplate(any()) } returns createPushNotificationTemplate()

            // when
            val result = service.canSendViaPush(notification)

            // then
            result.shouldBeTrue()
            verify { pushNotificationTemplateService.getActiveTemplate(NotificationTemplate("WelcomeDetails")) }
        }

        @Test
        fun `deve retornar false quando EmailNotification não pode ser convertida para push`() {
            // given
            val notification = createEmailNotification()

            // when
            val result = service.canSendViaPush(notification)

            // then
            result.shouldBeFalse()
            verify(exactly = 0) { pushNotificationTemplateService.getActiveTemplate(any()) }
        }

        @Test
        fun `deve retornar true quando WhatsappNotification pode ser convertida para push`() {
            // given
            val notification = createWhatsappNotification()
            every { pushNotificationTemplateService.getActiveTemplate(any()) } returns createPushNotificationTemplate()

            // when
            val result = service.canSendViaPush(notification)

            // then
            result.shouldBeTrue()
            verify(exactly = 1) { pushNotificationTemplateService.getActiveTemplate(NotificationTemplate("template-key-prop")) }
        }

        @Test
        fun `deve retornar true quando MultiChannelNotification pode ser convertida para push e o template está ativo`() {
            // given
            val notification = createMultiChannelNotification()

            every { pushNotificationTemplateService.getActiveTemplate(any()) } returns createPushNotificationTemplate()

            // when
            val result = service.canSendViaPush(notification)

            // then
            result.shouldBeTrue()
            verify { pushNotificationTemplateService.getActiveTemplate(NotificationTemplate(notification.configurationName.name)) }
        }

        @Test
        fun `deve retornar false quando o template do WhatsappNotification nao existir como valor na lista de props`() {
            // given
            val notification = createWhatsappNotification().copy(template = NotificationTemplate("non-existent-template"))

            // when
            val result = service.canSendViaPush(notification)

            // then
            result.shouldBeFalse()
            verify(exactly = 0) { pushNotificationTemplateService.getActiveTemplate(any()) }
        }

        @Test
        fun `deve retornar false quando o template não está ativo`() {
            // given
            val notification = ChatbotNotificationWithAccount(
                walletId = walletId,
                account = account,
                details = WelcomeDetails(WelcomeMessageType.WELCOME_CHATBOT_ONBOARDING),
            )

            every { pushNotificationTemplateService.getActiveTemplate(any()) } returns null

            // when
            val result = service.canSendViaPush(notification)

            // then
            result.shouldBeFalse()
            verify { pushNotificationTemplateService.getActiveTemplate(any()) }
        }
    }

    @Nested
    @DisplayName("sendNotificationAsync")
    inner class SendNotificationAsync {

        @Test
        fun `deve enviar quando for ChatbotNotification`() {
            // given
            val notification = ChatbotNotificationWithAccount(
                walletId = walletId,
                account = account,
                details = WelcomeDetails(WelcomeMessageType.WELCOME_CHATBOT_ONBOARDING),
            )
            val pushNotificationTemplate = createPushNotificationTemplate()

            every { pushNotificationTemplateService.getActiveTemplate(any()) } returns pushNotificationTemplate
            every { pushNotificationPublisher.publish(any(), any()) } returns Result.success(SendPushAsyncResult.Requested)

            // when
            val result = service.sendNotificationAsync(account.accountId, notification)

            // then
            result.shouldBeSuccess(SendPushAsyncResult.Requested)

            val contentSlot = slot<PushNotificationContent>()
            verify {
                pushNotificationPublisher.publish(account.accountId, capture(contentSlot))
                pushNotificationTemplateService.getActiveTemplate(NotificationTemplate("WelcomeDetails"))
            }
        }

        @Test
        fun `não deve enviar quando for EmailNotification`() {
            // given
            val notification = createEmailNotification()

            // when
            val result = service.sendNotificationAsync(account.accountId, notification)

            // then
            result.shouldBeSuccess(SendPushAsyncResult.UnableToCreatePush)
            verify(exactly = 0) { pushNotificationPublisher.publish(any(), any()) }
        }

        @Test
        fun `deve enviar quando for WhatsappNotification`() {
            // given
            val notification = createWhatsappNotification()
            val pushNotificationTemplate = createPushNotificationTemplate().copy(
                title = "Test {{1}} Title",
                message = "Test {{1}} {{2}} Message",
            )

            every { pushNotificationTemplateService.getActiveTemplate(any()) } returns pushNotificationTemplate
            every { pushNotificationPublisher.publish(any(), any()) } returns Result.success(SendPushAsyncResult.Requested)

            // when
            val result = service.sendNotificationAsync(account.accountId, notification)

            // then
            result.shouldBeSuccess(SendPushAsyncResult.Requested)

            val contentSlot = slot<PushNotificationContent>()
            verify {
                pushNotificationPublisher.publish(account.accountId, capture(contentSlot))
                pushNotificationTemplateService.getActiveTemplate(NotificationTemplate("template-key-prop"))
            }

            contentSlot.captured.title shouldBe "Test primeiro Title"
            contentSlot.captured.body shouldBe "Test primeiro segundo Message"
        }

        @Test
        fun `deve enviar quando for MultiChannelNotification`() {
            // given
            val notification = createMultiChannelNotification().copy(
                parameters = mapOf("NOME_P1" to "primeiro", "NOME_P2" to "segundo"),
            )
            val pushNotificationTemplate = createPushNotificationTemplate().copy(
                title = "Test {{NOME_P1}} Title",
                message = "Test {{NOME_P1}} {{NOME_P2}} Message",
            )

            every { pushNotificationTemplateService.getActiveTemplate(any()) } returns pushNotificationTemplate
            every { pushNotificationPublisher.publish(any(), any()) } returns Result.success(SendPushAsyncResult.Requested)

            // when
            val result = service.sendNotificationAsync(account.accountId, notification)

            // then
            result.shouldBeSuccess(SendPushAsyncResult.Requested)

            val contentSlot = slot<PushNotificationContent>()
            verify {
                pushNotificationPublisher.publish(account.accountId, capture(contentSlot))
                pushNotificationTemplateService.getActiveTemplate(NotificationTemplate(notification.configurationName.name))
            }

            contentSlot.captured.title shouldBe "Test primeiro Title"
            contentSlot.captured.body shouldBe "Test primeiro segundo Message"
        }
    }
}