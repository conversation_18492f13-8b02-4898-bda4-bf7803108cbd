package ai.friday.billpayment.modules.chatbotai.adapters.api

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.chatbot.ChatbotNotificationService
import ai.friday.billpayment.app.chatbot.ChatbotNotificationSpec
import ai.friday.billpayment.app.notification.NotificationMedia
import ai.friday.billpayment.app.notification.NotificationMediaType
import ai.friday.billpayment.modules.chatbotai.ChatbotAI
import ai.friday.morning.log.andAppend
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(BACKOFFICE)
@Controller("/backoffice/chatbot")
@ChatbotAI
class BackofficeChatbotAiController(
    private val chatbotNotificationService: ChatbotNotificationService,
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    @Post("/broadcast")
    fun broadcast(
        @Body body: SendChatbotNotificationTO,
        @PathVariable message: String,
    ): HttpResponse<*> {
        try {
            val media = body.notification.media?.let {
                val mediaTypeRaw = it["type"] ?: return HttpResponse.badRequest("media is missing type")

                val mediaType = try {
                    NotificationMediaType.valueOf(mediaTypeRaw as String)
                } catch (e: Exception) {
                    return HttpResponse.badRequest("unknown media type")
                }

                val mediaRaw = getObjectMapper().writeValueAsString(it)

                when (mediaType) {
                    NotificationMediaType.DOCUMENT -> getObjectMapper().readValue(mediaRaw, NotificationMedia.Document::class.java)
                    NotificationMediaType.IMAGE -> getObjectMapper().readValue(mediaRaw, NotificationMedia.Image::class.java)
                    NotificationMediaType.VIDEO -> getObjectMapper().readValue(mediaRaw, NotificationMedia.Video::class.java)
                }
            }

            val accountIds = body.accountIds.map { AccountId(it) }

            val spec = ChatbotNotificationSpec(
                template = body.notification.template,
                configurationKey = body.notification.configurationKey ?: body.notification.template,
                parameters = body.notification.parameters,
                quickReplyButtonsWhatsAppParameter = body.notification.quickReplyButtonsWhatsAppParameter ?: listOf(),
                buttonWhatsAppParameter = body.notification.buttonWhatsAppParameter,
                media = media,
                historyMessage = body.notification.historyMessage,
                sendEvent = body.notification.sendEvent,
                clickEvent = body.notification.clickEvent,
            )

            chatbotNotificationService.sendNotificationsAsync(accountIds, spec)

            logger.info(
                append("body", body).andAppend("path_message", message),
                "BackofficeChatbotAiController#notifyMessage",
            )
        } catch (e: Exception) {
            logger.error(
                append("body", body).andAppend("path_message", message),
                "BackofficeChatbotAiController#notifyMessage",
                e,
            )
            return HttpResponse.serverError<Unit>()
        }

        return HttpResponse.ok<Unit>()
    }
}

data class SendChatbotNotificationTO(
    val accountIds: List<String>,
    val notification: ChatbotNotificationSpecTO,
)

data class ChatbotNotificationSpecTO(
    val template: String,
    val configurationKey: String? = null,
    val parameters: List<String>,
    val quickReplyButtonsWhatsAppParameter: List<String>? = null,
    val buttonWhatsAppParameter: String? = null,
    val media: Map<String, Any>? = null,
    val historyMessage: String,
    val sendEvent: String? = null,
    val clickEvent: String? = null,
)