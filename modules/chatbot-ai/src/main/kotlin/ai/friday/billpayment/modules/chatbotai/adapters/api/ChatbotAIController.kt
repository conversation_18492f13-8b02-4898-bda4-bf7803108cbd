package ai.friday.billpayment.modules.chatbotai.adapters.api

import ai.friday.billpayment.adapters.api.AccountTO
import ai.friday.billpayment.adapters.api.BalanceForecastDatesTO
import ai.friday.billpayment.adapters.api.BalanceForecastTO
import ai.friday.billpayment.adapters.api.BillTO
import ai.friday.billpayment.adapters.api.ContactTO
import ai.friday.billpayment.adapters.api.CreatePixHelper
import ai.friday.billpayment.adapters.api.CreatePixTO
import ai.friday.billpayment.adapters.api.RequestPixRecipientTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.api.WalletBalanceForecastTO
import ai.friday.billpayment.adapters.api.builders.BillTOBuilder
import ai.friday.billpayment.adapters.api.convertToContactTO
import ai.friday.billpayment.adapters.api.toAccountTO
import ai.friday.billpayment.adapters.api.toWalletBalanceForecastTO
import ai.friday.billpayment.adapters.arbi.ArbiPixQRCodeAdapter
import ai.friday.billpayment.adapters.chatbot.PixQRCodeRequestTO
import ai.friday.billpayment.adapters.itp.mapToHttpResponse
import ai.friday.billpayment.adapters.settlement.SettlementAdapter
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountPaymentStatus
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.PartialAccount
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillImageProvider
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillValidationException
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateBillService
import ai.friday.billpayment.app.bill.CreateConcessionariaRequest
import ai.friday.billpayment.app.bill.CreateFichaDeCompensacaoRequest
import ai.friday.billpayment.app.bill.FichaCompensacaoService
import ai.friday.billpayment.app.bill.InvalidBillStateChangeException
import ai.friday.billpayment.app.bill.MemberNotAllowedException
import ai.friday.billpayment.app.bill.PixCopyAndPaste
import ai.friday.billpayment.app.bill.PixQrCodeData
import ai.friday.billpayment.app.bill.and
import ai.friday.billpayment.app.bill.isFicha
import ai.friday.billpayment.app.chatbot.ChatbotNotificationService
import ai.friday.billpayment.app.chatbot.ChatbotService
import ai.friday.billpayment.app.contact.ContactService
import ai.friday.billpayment.app.inappsubscription.InAppSubscription
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionService
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStatus
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.ChatBotTransactionId
import ai.friday.billpayment.app.integrations.ExternalTransactionId
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.LimitType
import ai.friday.billpayment.app.integrations.OFSweepingConsent
import ai.friday.billpayment.app.integrations.OFSweepingConsentPeriodicLimitUsage
import ai.friday.billpayment.app.integrations.OFSweepingConsentPeriodicUsage
import ai.friday.billpayment.app.integrations.OpenFinanceBankAccountService
import ai.friday.billpayment.app.integrations.OpenFinanceConsentService
import ai.friday.billpayment.app.integrations.OpenFinanceParticipantId
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.SweepingRequest
import ai.friday.billpayment.app.itp.ITPService
import ai.friday.billpayment.app.manualentry.ManualEntryError
import ai.friday.billpayment.app.manualentry.ManualEntryId
import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.app.manualentry.ManualEntryStatus
import ai.friday.billpayment.app.manualentry.ManualEntryType
import ai.friday.billpayment.app.notification.MessageProcessorService
import ai.friday.billpayment.app.onboarding.CreateOnboardingTestPixError
import ai.friday.billpayment.app.onboarding.OnboardingTestPixService
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.isValid
import ai.friday.billpayment.app.subscription.Subscription
import ai.friday.billpayment.app.subscription.SubscriptionPaymentStatus
import ai.friday.billpayment.app.subscription.SubscriptionService
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.log
import ai.friday.billpayment.modules.chatbotai.ChatbotAI
import ai.friday.billpayment.modules.chatbotai.app.AvailableLimitResult
import ai.friday.billpayment.modules.chatbotai.app.ChatBotScheduleBillResult
import ai.friday.billpayment.modules.chatbotai.app.ChatbotAIService
import ai.friday.billpayment.modules.chatbotai.app.ChatbotSystemActivity
import ai.friday.billpayment.modules.chatbotai.app.FindBillError
import ai.friday.billpayment.modules.chatbotai.app.PixKeyDetailsTO
import ai.friday.billpayment.modules.chatbotai.app.SweepingTransferErrorReason
import ai.friday.billpayment.modules.chatbotai.app.SystemActivityErrorReason
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import io.micronaut.core.annotation.Introspected
import io.micronaut.core.annotation.Nullable
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.Header
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.http.annotation.QueryValue
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import jakarta.validation.Valid
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Positive
import jakarta.validation.constraints.Size
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Controller("/chatbotAI")
@ChatbotAI
@Secured(Role.Code.OWNER)
open class ChatbotAIController(
    private val chatbotAIService: ChatbotAIService,
    private val chatbotService: ChatbotService,
    private val itpService: ITPService,
    private val pixKeyManagement: PixKeyManagement,
    private val createBillService: CreateBillService,
    private val accountService: AccountService,
    private val walletService: WalletService,
    private val contactService: ContactService,
    private val chatBotNotificationService: ChatbotNotificationService,
    private val subscriptionService: SubscriptionService,
    private val inAppSubscriptionService: InAppSubscriptionService,
    private val manualEntryService: ManualEntryService,
    private val accountRegisterRepository: AccountRegisterRepository,
    private val messageProcessorService: MessageProcessorService,
    private val openFinanceConsentService: OpenFinanceConsentService,
    private val billImageProvider: BillImageProvider,
    private val onboardingTestPixService: OnboardingTestPixService,
    private val qrCodeService: ArbiPixQRCodeAdapter,
    private val settlementService: SettlementAdapter,
    private val fichaCompensacaoService: FichaCompensacaoService,
    private val billTOBuilder: BillTOBuilder,
    private val openFinanceBankAccountService: OpenFinanceBankAccountService,
) {
    private val createPixHelper = CreatePixHelper(createBillService, billImageProvider, billTOBuilder, logger)

    @Post("/one-pix-pay")
    fun onePixPay(authentication: Authentication, @Body body: OnePixPayTO): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val markers = Markers.append("body", body).andAppend("accountId", accountId.value)
        val logName = "ChatbotAIController#OnePixPay"
        return try {
            val billIds = body.bills.map {
                BillId(it)
            }
            val onePixPay =
                chatbotAIService.createOnePixPay(
                    accountId = accountId,
                    bills = billIds,
                    useCurrentBalance = body.useCurrentBalance,
                    walletId = body.walletId?.let { WalletId(it) },
                )
                    .getOrElse {
                        logger.info(markers.andAppend("ErrorMessage", it), logName)
                        return it.mapToHttpResponse()
                    }

            HttpResponse.created(QrCodeResponseTO(onePixPay.qrCode))
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    @Post("/one-pix-pay-itp")
    fun getOnePixPayItp(
        authentication: Authentication,
        @Body body: OnePixPayITPTO,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()

        val markers = Markers.append("accountId", accountId.value).andAppend("bills", body.bills)
        val logName = "ChatbotAIController#getOnePixPayItp"

        return try {
            val billIds = body.bills.map {
                BillId(it)
            }

            val authorizationServerId = body.authorizationServerId

            val paymentIntentId =
                chatbotAIService.createOnePixPayITP(
                    accountId = accountId,
                    bills = billIds,
                    authorizationServerId = authorizationServerId,
                    useCurrentBalance = body.useCurrentBalance,
                    walletId = body.walletId?.let { WalletId(it) },
                )
                    .getOrElse {
                        return it.mapToHttpResponse(
                            onBadRequest = {
                                logger.info(
                                    markers.andAppend("ErrorMessage", it),
                                    logName,
                                )
                            },
                            onServerError = { error ->
                                logger.error(markers.andAppend("ErrorMessage", error.message), logName, error.exception)
                            },
                        )
                    }
            HttpResponse.created(ItpTokenResponseTO(paymentIntentId.value))
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    @Post("/validateBill")
    fun arbiValidateBill(authentication: Authentication, @Body body: ValidateBillTO): HttpResponse<*> {
        val logName = "ChatbotAIController#validateBill"
        val barCode = BarCode.ofDigitable(body.digitableLine)
        val wallet = walletService.findWallet(WalletId(authentication.toAccountId().value))
        val dueDate = body.dueDate?.let { LocalDate.parse(it, dateFormat) } ?: LocalDate.now()
        val markers = Markers.append("accountId", authentication.toAccountId().value)
            .andAppend("walletId", wallet.id.value)
            .andAppend("body", body)
            .andAppend("dueDate", dueDate)

        try {
            val request = if (barCode.isFicha()) {
                CreateFichaDeCompensacaoRequest(
                    barcode = barCode,
                    description = "",
                    walletId = wallet.id,
                    source = ActionSource.VirtualAssistant(
                        accountId = authentication.toAccountId(),
                    ),
                    member = wallet.founder,
                )
            } else {
                CreateConcessionariaRequest(
                    barcode = barCode,
                    description = "",
                    dueDate = dueDate,
                    walletId = wallet.id,
                    source = ActionSource.VirtualAssistant(
                        accountId = authentication.toAccountId(),
                    ),
                    member = wallet.founder,
                )
            }
            val response = settlementService.validateBill(request)

            markers.andAppend("response", response)

            logger.info(markers, logName)

            return when {
                response.isValid() -> {
                    return HttpResponse.ok(
                        BillValidationResultTO(
                            dueDate = response.billRegisterData!!.dueDate?.format(dateFormat),
                            amount = response.billRegisterData!!.amountTotal,
                            assignor = response.billRegisterData!!.assignor,
                            settleDate = response.billRegisterData!!.settleDate?.format(dateFormat),
                        ),
                    )
                }

                response.notPayable() -> HttpResponse.badRequest(BillErrors.NOT_PAYABLE.toResponseTO())
                response.alreadyPaid() -> HttpResponse.badRequest(BillErrors.ALREADY_PAID.toResponseTO())
                response.isPaymentLimitExpired() -> HttpResponse.badRequest(BillErrors.PAYMENT_LIMIT_EXPIRED.toResponseTO())
                response.isBarcodeNotFound() -> HttpResponse.badRequest(BillErrors.BARCODE_NOT_FOUND.toResponseTO())
                response.isEmptyAmount() -> HttpResponse.badRequest(BillErrors.EMPTY_AMOUNT.toResponseTO())

                else -> {
                    return HttpResponse.serverError(
                        ResponseTO(
                            "5001",
                            "Internal server error",
                        ),
                    )
                }
            }
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return HttpResponse.serverError(
                ResponseTO(
                    "5001",
                    "Internal server error",
                ),
            )
        }
    }

    @Post("/ignore")
    fun ignoreBill(
        authentication: Authentication,
        @Body body: WalletIdAndBillsListTO,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val walletId = WalletId(body.walletId)

        val markers = Markers.append("accountId", accountId.value).andAppend("bills", body.bills)
            .andAppend("walletId", walletId.value)
        val logName = "ChatbotAIController#ignore"

        return try {
            val billIds = body.bills.map {
                BillId(it)
            }
            markers.andAppend("billIds", billIds)

            val result = chatbotAIService.ignore(
                walletId = walletId,
                accountId = accountId,
                bills = billIds,

            )
            result.fold(
                ifLeft = {
                    logger.error(markers, logName, it)
                    when (it) {
                        is InvalidBillStateChangeException, is MemberNotAllowedException -> HttpResponse.badRequest(it.message)
                        else -> HttpResponse.serverError(it.message)
                    }
                },
                ifRight = {
                    logger.info(markers, logName)
                    HttpResponse.noContent<Unit>()
                },
            )
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    @Post("/schedule")
    fun scheduleBill(
        authentication: Authentication,
        @Body body: ChatbotScheduleBillsTO,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val walletId = WalletId(body.walletId)

        val markers = Markers.append("accountId", accountId.value).andAppend("bills", body.bills)
            .andAppend("walletId", walletId.value).andAppend("body", body)
        val logName = "ChatbotAIController#schedule"

        return try {
            val result =
                chatbotAIService.scheduleBills(
                    walletId = walletId,
                    accountId = accountId,
                    bills = body.bills.map { BillId(it) },
                    sweepingRequest = body.sweepingRequest?.toDomain(),
                    authorizationToken = body.authorizationToken,
                    toDueDate = body.scheduleToDueDate,
                )

            markers.andAppend("result", result)

            when (result) {
                is ChatBotScheduleBillResult.BillScheduled, is ChatBotScheduleBillResult.BillsScheduled -> {
                    logger.info(markers, logName)
                    return HttpResponse.noContent<Unit>()
                }

                is ChatBotScheduleBillResult.BillNotFound -> {
                    logger.error(markers.andAppend("error", result.error), logName)
                    return HttpResponse.badRequest(
                        ResponseTO(
                            "4002",
                            "Bill not found",
                        ),
                    )
                }

                is ChatBotScheduleBillResult.BillNotActive -> {
                    logger.error(markers.andAppend("error", result.error), logName)
                    return HttpResponse.badRequest(
                        ResponseTO(
                            "4001",
                            "Bill not active",
                        ),
                    )
                }

                is ChatBotScheduleBillResult.BillNotScheduled -> {
                    logger.error(markers.andAppend("error", result.error), logName)
                    return HttpResponse.serverError(
                        ResponseTO(
                            "5001",
                            "Bill not scheduled",
                        ),
                    )
                }

                is ChatBotScheduleBillResult.SweepingTransferError -> {
                    logger.error(markers.andAppend("reason", result.reason), logName)
                    return handleSweepingTransferError(result.reason)
                }

                is ChatBotScheduleBillResult.AssistantLimitExceeded -> {
                    logger.warn(markers.andAppend("error", result.error), logName)
                    return HttpResponse.badRequest(
                        ResponseTO(
                            "4004",
                            "Assistant limit exceeded",
                        ),
                    )
                }

                else -> {
                    logger.error(markers.andAppend("error", result.toString()), logName)
                    return HttpResponse.serverError<Unit>()
                }
            }
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    @Post("/pix/qrcode/validate")
    fun validateQRCode(authentication: Authentication, @Body body: ChatbotPixQRCodeValidateTO): HttpResponse<*> {
        val logName = "ChatbotAIController#validatePixQrCode"
        val accountId = authentication.toAccountId()
        val walletId = WalletId(accountId.value)
        val markers = Markers.append("accountId", accountId)
            .andAppend("walletId", walletId.value)
            .andAppend("body", body)

        val account = accountService.findAccountByIdOrNull(accountId)

        if (account == null) {
            logger.error(markers, logName, ItemNotFoundException("Account not found"))
            return HttpResponse.badRequest("Account not found")
        }

        val response = qrCodeService.parseQRCodeCacheable(pixCopyAndPaste = PixCopyAndPaste(body.qrCodeValue), document = account.document).getOrElse {
            logger.error(markers.andAppend("error", it.toString()), logName)
            return when (it) {
                is PixKeyError.InvalidQrCode -> {
                    HttpResponse.badRequest(
                        ResponseTO(
                            "40036",
                            "Invalid QR code",
                        ),
                    )
                }

                else -> {
                    HttpResponse.badRequest(
                        ResponseTO(
                            "40035",
                            "Error validating QR code",
                        ),
                    )
                }
            }
        }.asPixKeyDetailsResult()

        val pixAmount = response.qrCodeInfo?.fixedAmount ?: response.qrCodeInfo?.originalAmount ?: body.amount

        markers.andAppend("pixAmount", pixAmount)

        if (pixAmount == null || pixAmount <= 0L) {
            logger.error(markers, logName, Exception("Pix amount not found"))
            return HttpResponse.badRequest(ResponseTO("40039", "Pix amount not found"))
        }

        val code = chatbotAIService.validateAvailableLimit(walletId, accountId, pixAmount, AvailableLimitType.PIX).toCode()

        logger.info(markers, logName)

        return HttpResponse.ok(
            PixQrCodeDetailsResultTO(
                pixKeyDetails = response.pixKeyDetails,
                e2e = response.e2e,
                qrCodeInfo = response.qrCodeInfo,
                code = code,
                amount = pixAmount,
            ),
        )
    }

    @Post("/addBill{?dryRun}")
    fun addBill(
        @Body @Valid
        addBoletoTO: AddBoletoTO,
        @QueryValue(defaultValue = "false") dryRun: Boolean,
        authentication: Authentication,
    ): HttpResponse<*> {
        val logName = "ChatbotAIController#addBill"
        val accountId = authentication.toAccountId()
        val markers = Markers.append("accountId", accountId).andAppend("request", addBoletoTO)

        val wallet = walletService.findWallet(WalletId(accountId.value))
        val barCode = BarCode.ofDigitable(addBoletoTO.digitableLine)

        val result = if (barCode.checkIsConcessionaria()) {
            val dueDate = addBoletoTO.dueDate?.let { LocalDate.parse(it, DateTimeFormatter.ofPattern("yyyy-MM-dd")) } ?: LocalDate.now()
            val request = CreateConcessionariaRequest(
                barcode = barCode,
                description = addBoletoTO.description ?: "",
                dueDate = dueDate,
                walletId = wallet.id,
                source = ActionSource.VirtualAssistant(accountId),
                member = wallet.founder,
            )

            markers.andAppend("request", request)

            createBillService.createConcessionaria(
                request = request,
                dryRun = dryRun,
            )
        } else {
            val request = CreateFichaDeCompensacaoRequest(
                barcode = barCode,
                description = addBoletoTO.description ?: "",
                walletId = wallet.id,
                source = ActionSource.VirtualAssistant(accountId),
                member = wallet.founder,
            )

            markers.andAppend("request", request)

            fichaCompensacaoService.createFichaDeCompensacao(
                request = request,
                dryRun = dryRun,
            )
        }

        markers.andAppend("result", result)
        logger.info(markers, logName)

        return when (result) {
            is CreateBillResult.FAILURE.AlreadyPaid.WithData, is CreateBillResult.FAILURE.AlreadyPaid.WithoutData -> HttpResponse.badRequest(BillErrors.ALREADY_PAID.toResponseTO())
            is CreateBillResult.FAILURE.BillAlreadyExists -> HttpResponse.badRequest(BillErrors.ALREADY_EXISTS.toResponseTO())
            is CreateBillResult.FAILURE.BillNotPayable -> HttpResponse.badRequest(BillErrors.NOT_PAYABLE.toResponseTO())
            is CreateBillResult.FAILURE.BillUnableToValidate -> HttpResponse.badRequest(BillErrors.UNABLE_TO_VALIDATE.toResponseTO())
            is CreateBillResult.FAILURE.ServerError -> HttpResponse.serverError(ResponseTO("5001", "Internal server error"))
            is CreateBillResult.SUCCESS -> {
                val bill = result.bill
                val createBillResult = CreateBillResultTO(
                    billId = bill.billId.value,
                    amount = bill.amountTotal,
                    dueDate = bill.dueDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                    assignor = bill.getPayee(),
                    billType = bill.billType,
                )
                return HttpResponse.ok(createBillResult)
            }
        }
    }

    @Post("/pix")
    open fun createPix(
        @Body @Valid
        createChatbotPixTO: CreateChatbotPixTO,
        authentication: Authentication,
        @Header("X-WALLET-ID") walletId: String?,
    ): HttpResponse<*> {
        val logName = "ChatbotAIController#createPix"
        val accountId = authentication.toAccountId()
        val markers = Markers.append("accountId", accountId).andAppend("walletId", walletId).andAppend("request", createChatbotPixTO)

        val wallet = walletService.findWallets(accountId).firstOrNull {
            when {
                walletId != null -> it.id == WalletId(walletId) && it.founder.accountId == accountId
                else -> it.checkPrimary(accountId)
            }
        }

        if (wallet == null) {
            logger.error(markers, logName, ItemNotFoundException("Wallet not found"))
            return HttpResponse.badRequest("Wallet not found")
        }

        val createPixTO = createChatbotPixTO.toCreatePixTO()

        val pixKey = createPixTO.recipient.pixKey?.let { PixKey(it.value, it.type) }

        val invalidPixKey = pixKey == null || !pixKey.isValid()

        if (createPixTO.recipient.qrCode == null && invalidPixKey) {
            logger.error(markers, logName, Exception("Pix key invalid"))
            return HttpResponse.badRequest("Pix key invalid")
        }

        val member = wallet.getMember(accountId)

        val contactId = if (member.permissions.founderContactsEnabled) {
            wallet.founder.accountId
        } else {
            accountId
        }

        val recipient = createPixHelper.buildRecipient(createPixTO, contactId)
        val request = createPixHelper.buildCreateChatbotPixRequest(accountId, createPixTO, recipient, wallet.id, createChatbotPixTO.transactionId)

        return when (
            val result = chatbotAIService.schedulePix(
                accountId = accountId,
                wallet = wallet,
                member = member,
                createPixRequest = request,
                authorizationToken = createChatbotPixTO.authorizationToken,
                transactionId = createChatbotPixTO.transactionId,
                retryTransaction = createChatbotPixTO.retryTransaction,
                sweepingRequest = createChatbotPixTO.sweepingRequest?.toDomain(),
            )
        ) {
            is ChatBotScheduleBillResult.PixScheduled -> {
                logger.info(markers.andAppend("result", result), logName)
                HttpResponse.noContent<Unit>()
            }

            is ChatBotScheduleBillResult.Failure -> {
                logger.error(markers, logName, result.error)
                HttpResponse.serverError(result.error)
            }

            is ChatBotScheduleBillResult.PixKeyInvalid -> {
                logger.error(markers, logName, Exception("Pix key invalid"))
                HttpResponse.badRequest("Pix key invalid")
            }

            is ChatBotScheduleBillResult.RecipientNotFound -> {
                logger.error(markers, logName, ItemNotFoundException("Recipient not found"))
                HttpResponse.badRequest("Recipient not found")
            }

            is ChatBotScheduleBillResult.WalletNotFound -> {
                logger.error(markers, logName, ItemNotFoundException("Wallet not found"))
                HttpResponse.badRequest("Wallet not found")
            }

            is ChatBotScheduleBillResult.BillNotScheduled -> {
                logger.error(markers, logName, Exception("Bill not scheduled"))
                HttpResponse.serverError("Bill not scheduled")
            }

            is ChatBotScheduleBillResult.UnableToValidatePix -> {
                logger.error(markers, logName, BillValidationException("Unable to validate pix"))
                HttpResponse.serverError("Unable to validate pix")
            }

            is ChatBotScheduleBillResult.AssistantLimitExceeded -> {
                logger.error(markers.andAppend("error", result.error), logName)
                return HttpResponse.badRequest(
                    ResponseTO(
                        "4004",
                        "Assistant limit exceeded",
                    ),
                )
            }

            is ChatBotScheduleBillResult.PixLimitExceeded -> {
                logger.error(markers.andAppend("error", result.error), logName)
                return HttpResponse.badRequest(
                    ResponseTO(
                        "4006",
                        "Daily limit exceeded",
                    ),
                )
            }

            is ChatBotScheduleBillResult.BillAlreadyExists -> {
                logger.error(markers.andAppend("billId", result.billId.value), logName)
                return HttpResponse.badRequest(
                    ResponseTO(
                        "4007",
                        "Bill already exists",
                    ),
                )
            }

            is ChatBotScheduleBillResult.SweepingTransferError -> {
                logger.error(markers.andAppend("reason", result.reason), logName)
                return handleSweepingTransferError(result.reason)
            }

            is ChatBotScheduleBillResult.BillNotActive,
            is ChatBotScheduleBillResult.BillNotFound,
            is ChatBotScheduleBillResult.BillScheduled,
            is ChatBotScheduleBillResult.BillsScheduled,
            is ChatBotScheduleBillResult.PixKeyNotFound,
            is ChatBotScheduleBillResult.PixKeyValid,
            -> {
                logger.error(markers, logName, Exception("should not happen: $result"))
                HttpResponse.serverError(result)
            }
        }
    }

    @Post("/retrySweepingTransfer")
    open fun retrySweepingTransfer(
        @Body @Valid
        request: RetrySweepingTransferTO,
        authentication: Authentication,
        @Header("X-WALLET-ID") walletId: String?,
    ): HttpResponse<*> {
        val logName = "ChatbotAIController#retrySweepingTransfer"
        val accountId = authentication.toAccountId()
        val markers = Markers.append("accountId", accountId).andAppend("walletId", walletId).andAppend("request", request)

        val wallet = walletService.findWallets(accountId).firstOrNull {
            when {
                walletId != null -> it.id == WalletId(walletId) && it.founder.accountId == accountId
                else -> it.checkPrimary(accountId)
            }
        }

        if (wallet == null) {
            logger.error(markers, logName, ItemNotFoundException("Wallet not found"))
            return HttpResponse.badRequest("Wallet not found")
        }

        return chatbotAIService.retrySweepingTransfer(
            walletId = wallet.id,
            amount = request.amount,
            participantId = request.participantId?.let { OpenFinanceParticipantId(it) },
        ).map {
            HttpResponse.ok(it?.value)
        }.getOrElse {
            logger.error(markers.andAppend("reason", it), logName)
            handleSweepingTransferError(it)
        }
    }

    @Post("/pix/validate")
    fun validatePix(
        authentication: Authentication,
        @Body validatePixTO: ValidatePixTO,
    ): HttpResponse<*> {
        val logName = "ChatbotAIController#validatePix"
        val pixKey = PixKey(validatePixTO.keyValue.lowercase(), validatePixTO.keyType)
        val accountId = authentication.toAccountId()
        val walletId = WalletId(validatePixTO.walletId)
        val markers = Markers.append("accountId", accountId)
            .andAppend("walletId", walletId.value)
            .andAppend("keyType", validatePixTO.keyType)
            .andAppend("keyValue", validatePixTO.keyValue)
            .andAppend("amount", validatePixTO.amount)

        if (!pixKey.isValid()) {
            logger.error(markers, logName, Exception("Pix key invalid"))
            return HttpResponse.badRequest<Unit>()
        }
        val result = chatbotAIService.validatePix(
            accountId = accountId,
            walletId = walletId,
            pixKey = pixKey,
            amount = validatePixTO.amount,
        )
        when (result) {
            is ChatBotScheduleBillResult.PixKeyValid -> {
                logger.info(markers.andAppend("result", result), logName)
                val code = chatbotAIService.validateAvailableLimit(walletId, accountId, validatePixTO.amount, AvailableLimitType.PIX).toCode()
                return HttpResponse.ok(ValidatePixResponseTO(pixDetails = result.pixKeyDetailsTO, code = code))
            }

            is ChatBotScheduleBillResult.PixKeyInvalid -> {
                logger.error(markers.andAppend("error", result), logName)
                return HttpResponse.badRequest<Unit>()
            }

            is ChatBotScheduleBillResult.PixKeyNotFound -> {
                logger.error(markers.andAppend("error", result), logName)
                return HttpResponse.notFound<Unit>()
            }

            is ChatBotScheduleBillResult.UnableToValidatePix -> {
                logger.error(markers.andAppend("error", result), logName)
                return HttpResponse.serverError<Unit>()
            }

            is ChatBotScheduleBillResult.Failure -> {
                logger.error(markers.andAppend("error", result), logName, result.error)
                return HttpResponse.serverError<Unit>()
            }

            else -> {
                logger.error(markers.andAppend("error", result), logName)
                return HttpResponse.serverError<Unit>()
            }
        }
    }

    // TODO remover esse endpoint depois de finalizar os testes do /pix/validate
    @Get("checkPix/{keyType}/{keyValue}/{transactionId}")
    fun checkPixKey(
        authentication: Authentication,
        @PathVariable keyType: PixKeyType,
        @PathVariable keyValue: String,
        @PathVariable transactionId: String,
    ): HttpResponse<*> {
        val logName = "ChatbotAIController#findPixKey"
        val pixKey = PixKey(keyValue.lowercase(), keyType)
        val markers = Markers.append("keyType", keyType).andAppend("keyValue", keyValue)
        if (!pixKey.isValid()) {
            logger.error(markers, logName, Exception("Pix key invalid"))
            return HttpResponse.badRequest<Unit>()
        }
        val account = accountService.findAccountById(authentication.toAccountId())
        return pixKeyManagement.findKeyDetails(pixKey, account.document)
            .map { (pixKeyDetails, _) ->
                chatBotNotificationService.notifyPixKeyFindResult(
                    account = account,
                    pixDetails = pixKeyDetails,
                    transactionId = ChatBotTransactionId(transactionId),
                )
                HttpResponse.ok(billTOBuilder.toPixKeyResponseTO(pixKeyDetails))
            }.getOrElse { pixKeyError ->
                logger.warn(markers.andAppend("pixKeyError", pixKeyError), logName)
                when (pixKeyError) {
                    is PixKeyError.MalformedKey -> HttpResponse.badRequest<Unit>()
                    is PixKeyError.KeyNotFound, is PixKeyError.KeyNotConfirmed -> HttpResponse.notFound()
                    is PixKeyError.UnknownError, PixKeyError.SystemUnavailable, PixKeyError.InvalidQrCode -> HttpResponse.serverError()
                }
            }
    }

    @Post("/mark-as-paid")
    fun markAsPaid(
        authentication: Authentication,
        @Body body: WalletIdAndBillsListTO,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val walletId = WalletId(body.walletId)

        val markers = Markers.append("accountId", accountId.value).andAppend("bills", body.bills)
            .andAppend("walletId", walletId.value)
        val logName = "ChatbotAIController#markAsPaid"

        return try {
            val billIds = body.bills.map {
                BillId(it)
            }
            markers.andAppend("billIds", billIds)

            val result = chatbotAIService.markAsPaid(
                walletId = walletId,
                accountId = accountId,
                bills = billIds,

            )
            result.fold(
                ifLeft = {
                    logger.error(markers, logName, it)
                    when (it) {
                        is InvalidBillStateChangeException, is MemberNotAllowedException -> HttpResponse.badRequest(it.message)
                        else -> HttpResponse.serverError(it.message)
                    }
                },
                ifRight = {
                    logger.info(markers, logName)
                    HttpResponse.noContent<Unit>()
                },
            )
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    @Post("/mark-reminder-as-done")
    fun markReminderAsDone(
        authentication: Authentication,
        @Body body: MarkReminderAsDoneTO,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()

        val markers = Markers.append("accountId", accountId.value).andAppend("manualEntryId", body.reminderId)
        val logName = "ChatbotAIController#markReminderAsDone"

        return try {
            val result = manualEntryService.markAsPaid(ManualEntryId(body.reminderId))
            result.fold(
                ifLeft = {
                    logger.error(markers.andAppend("error", it), logName)
                    when (it) {
                        ManualEntryError.ManualEntryNotFound -> HttpResponse.notFound()
                        ManualEntryError.InvalidStatus -> HttpResponse.status(HttpStatus.CONFLICT)
                        ManualEntryError.CategoryNotFound,
                        ManualEntryError.UnknownError,
                        -> HttpResponse.serverError()
                    }
                },
                ifRight = {
                    logger.info(markers, logName)
                    HttpResponse.noContent<Unit>()
                },
            )
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    @Get("/payment-organizations")
    fun listPaymentOrganizations(
        authentication: Authentication,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val logName = "ChatbotAIController#listPaymentOrganizations"

        val markers = Markers.append("accountId", accountId.value)

        try {
            val accountOrganization = itpService.listPaymentOrganizations(accountId).getOrElse {
                return it.mapToHttpResponse(
                    onBadRequest = {
                        logger.info(
                            markers.andAppend("ErrorMessage", it),
                            logName,
                        )
                    },
                    onServerError = { error ->
                        logger.error(markers.andAppend("ErrorMessage", error.message), logName, error.exception)
                    },
                )
            }

            logger.info(
                markers.and("bankISPBList" to accountOrganization.result.map { it.value.bankISPB }),
                logName,
            )

            return HttpResponse.ok(
                ListPaymentOrganizationsResponseTO(
                    lastUsed = accountOrganization.result[0]?.let {
                        FinancialInstitutionTO(
                            id = it.authorizationServerId,
                            title = it.institutionName,
                        )
                    },
                    otherFinancialInstitutions = accountOrganization.otherFinancialInstitutions
                        .map {
                            FinancialInstitutionTO(
                                id = it.id,
                                title = it.title,
                            )
                        }.sortedBy {
                            it.title
                        },
                ),
            )
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            return HttpResponse.serverError<Unit>()
        }
    }

    @Get("/sweeping-consent{?walletId}")
    @Secured(Role.Code.GUEST, Role.Code.OWNER)
    fun sweepingConsent(
        authentication: Authentication,
        @QueryValue walletId: String?,
    ): HttpResponse<*> {
        val currentWalletId = WalletId(walletId ?: authentication.name)
        val logName = "ChatbotAIController#sweepingConsent"
        val markers = Markers.append("walletId", walletId)
            .andAppend("currentWalletId", currentWalletId.value)

        return try {
            val consents = openFinanceConsentService.getAuthorizedSweepingConsents(currentWalletId)
            logger.info(markers.andAppend("consents", consents), logName)
            HttpResponse.ok(
                GetSweepingConsentResponseTO(
                    activeConsents = consents.map { it.toSweepingConsentTO() },
                ),
            )
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    @Put("/open-finance/balances")
    @Secured(Role.Code.GUEST, Role.Code.OWNER)
    fun openFinanceBalances(
        authentication: Authentication,
        @Body body: OFBalancesRequest,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val logName = "ChatbotAIController#openFinanceBalances"
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("body", body)

        return try {
            val balances = openFinanceBankAccountService.getBalances(
                accountId = authentication.toAccountId(),
                walletId = WalletId(body.walletId),
                participantIds = body.participantIds.map { OpenFinanceParticipantId(it) },
            )

            logger.info(markers.andAppend("balances", balances), logName)
            HttpResponse.ok(
                OFBalancesResponse(
                    balances = balances.map {
                        OFBalanceTO(
                            participantId = it.key.value,
                            amount = it.value,
                        )
                    },
                ),
            )
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    @Get(
        uris = [
            "/pending-bills{?startDate,endDate,walletId}",
            "/pending-and-waiting-approval-bills{?startDate,endDate,walletId}",
        ],
    )
    @Secured(Role.Code.GUEST, Role.Code.OWNER)
    fun listPendingAndWaitingApprovalBills(
        authentication: Authentication,
        @QueryValue @Nullable
        startDate: String,
        @QueryValue @Nullable
        endDate: String,
        @QueryValue @Nullable
        walletId: String?,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val currentWalletId = WalletId(walletId ?: authentication.name)
        val logName = "ChatbotAIController#listPendingBills"

        val markers = Markers.append("accountId", accountId.value)
            .andAppend("walletId", walletId)
            .andAppend("currentWalletId", currentWalletId.value)
            .andAppend("startDate", startDate)
            .andAppend("endDate", endDate)

        val parsedStartDate = LocalDate.parse(startDate, dateFormat)
        val parsedEndDate = LocalDate.parse(endDate, dateFormat)
        val consents = openFinanceConsentService.getAuthorizedSweepingConsents(currentWalletId)

        try {
            val account = accountService.findAccountById(accountId)
            val wallets = chatbotAIService.findPendingAndWaitingApprovalBillsByWallet(accountId, startDate = parsedStartDate, endDate = parsedEndDate)

            val response = AccountAndWalletsTO(
                account = account.toAccountTO(consents.isNotEmpty()),
                wallets = wallets.map { wallet ->
                    WalletBillsComingDueToNotifyTO(
                        walletName = wallet.walletName,
                        walletId = wallet.walletId.value,
                        bills = wallet.bills.map {
                            val imageUrl = billImageProvider.getBillImageUrl(it)
                            billTOBuilder.toBillTO(it, wallet.walletFounder, imageUrl = imageUrl)
                        },
                        activeConsents = consents.map { it.toSweepingConsentTO() },
                    )
                },
            )
            logger.info(markers.andAppend("response", response), logName)
            return HttpResponse.ok(response)
        } catch (ex: AccountNotFoundException) {
            val account = accountService.findPartialAccountById(accountId)
            val accountRegister = accountRegisterRepository.findByAccountId(accountId)

            val response = AccountAndWalletsTO(
                account = account.toAccountTO(accountRegister.mobilePhone?.msisdn ?: throw IllegalStateException("MSISDN should be present")),
                wallets = emptyList(),
            )
            logger.info(markers.andAppend("partialAccount", account).andAppend("response", response), logName)
            return HttpResponse.ok(response)
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            return HttpResponse.serverError<Unit>()
        }
    }

    @Get("/pixKeyContacts")
    @Secured(Role.Code.GUEST, Role.Code.OWNER)
    fun pixKeyContacts(
        authentication: Authentication,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val logName = "ChatbotAIController#pixKeyContacts"
        val markers = Markers.append("accountId", accountId.value)
        return try {
            val savedRecipients = contactService.getAll(accountId)
            val response = ContactsListTO(contacts = savedRecipients.convertToContactTO())
            logger.info(markers, logName)
            HttpResponse.ok(response)
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    @Get("/balance-and-forecast")
    @Secured(Role.Code.GUEST, Role.Code.OWNER)
    fun getBalanceAndForecast(
        authentication: Authentication,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val logName = "ChatbotAIController#getBalanceAndForecast"
        val markers = Markers.append("accountId", accountId.value)
        try {
            val response =
                chatbotAIService.calculateWalletBalanceForecast(WalletId(accountId.value)).getOrElse {
                    if (it is ItemNotFoundException) {
                        val response = emptyBalanceForecast()
                        logger.info(markers.andAppend("response", response), "$logName/guest")
                        return HttpResponse.ok(response)
                    }

                    logger.error(markers, logName, it)
                    return HttpResponse.serverError<Unit>()
                }.toWalletBalanceForecastTO()

            logger.info(markers.andAppend("response", response), logName)
            return HttpResponse.ok(response)
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            return HttpResponse.serverError<Unit>()
        }
    }

    @Post("/pix-qr-code")
    fun getPixQRCode(
        @Body pixQRCodeRequest: PixQRCodeRequestTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val markers = Markers.append("accountId", accountId)
            .andAppend("pixQRCodeRequest", pixQRCodeRequest)

        return try {
            val wallet = walletService.findWallet(WalletId(pixQRCodeRequest.walletId))
            markers.andAppend("walletFounder", wallet.founder.accountId.value)

            if (wallet.founder.accountId != accountId) {
                logger.error(markers, "ChatbotAIController#getPixQRCode")
                return HttpResponse.notFound<Unit>()
            }

            return chatbotService.createAmountQrCode(
                accountId = accountId,
                walletId = wallet.id,
                amount = pixQRCodeRequest.amount,
                message = pixQRCodeRequest.message,
            ).map {
                HttpResponse.ok(QrCodeResponseTO(it.qrCode.value))
            }.getOrElse {
                HttpResponse.badRequest(it.toString())
            }
        } catch (e: Exception) {
            logger.error(markers, "ChatbotAIController#getPixQRCode", e)
            HttpResponse.serverError<Unit>()
        }
    }

    @Get("/subscription/fee")
    @Secured(Role.Code.GUEST, Role.Code.OWNER)
    fun getFee(authentication: Authentication): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val logName = "ChatbotAIController#getFee"
        val markers = log("accountId" to accountId.value)

        return try {
            val result = subscriptionService.find(accountId)

            logger.info(markers.andAppend("result", result), logName)

            HttpResponse.ok(SubscriptionFeeResponseTO(result.amount))
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    @Get("/subscription")
    @Secured(Role.Code.GUEST, Role.Code.OWNER)
    fun getSubscription(authentication: Authentication): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val logName = "ChatbotAIController#getSubscription"
        val markers = log("accountId" to accountId.value)

        return try {
            val account = accountService.findAccountById(accountId)

            val result = when (account.subscriptionType) {
                SubscriptionType.PIX -> subscriptionService.findOrNull(accountId)?.toTO()
                SubscriptionType.IN_APP -> inAppSubscriptionService.getSubscription(accountId)?.toTO()
            }

            logger.info(markers.andAppend("result", result), logName)

            HttpResponse.ok(SubscriptionResponseTO(subscription = result))
        } catch (ex: AccountNotFoundException) {
            val account = accountService.findPartialAccountById(accountId)

            logger.info(markers.andAppend("partialAccount", account), logName)
            return HttpResponse.ok(SubscriptionResponseTO(subscription = null))
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    // TODO deveríamos remover esse endpoint
    @Get("/whatsAppPayment/{walletId}/limit")
    fun getWhatsAppPaymentLimit(authentication: Authentication, @PathVariable walletId: String): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val logName = "ChatbotAIController#getWhatsAppPaymentLimit"
        val markers = Markers.append("accountId", accountId.value).andAppend("walletId", walletId)
        return try {
            val limit = chatbotAIService.getAssistantPaymentLimit(WalletId(walletId), accountId)
            logger.info(markers.andAppend("limit", limit), logName)
            HttpResponse.ok(limit)
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    @Post("/limit/validate")
    fun validateAvailableLimit(
        authentication: Authentication,
        @Body body: AvailableLimitTO,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val logName = "ChatbotAIController#validateAvailableLimit"
        val markers = Markers.append("accountId", accountId.value).andAppend("body", body)
        try {
            return when (val result = chatbotAIService.validateAvailableLimit(WalletId(body.walletId), accountId, body.amount, body.type)) {
                is AvailableLimitResult.Success -> {
                    logger.info(markers.andAppend("result", result), logName)
                    HttpResponse.ok(
                        ResponseTO(
                            code = result.toCode(),
                            message = "Success",
                        ),
                    )
                }

                is AvailableLimitResult.AssistantLimitExceeded -> {
                    logger.error(markers.andAppend("error", result), logName)
                    return HttpResponse.ok(
                        ResponseTO(
                            code = result.toCode(),
                            message = "Assistant limit exceeded",
                        ),
                    )
                }

                is AvailableLimitResult.PixLimitExceeded -> {
                    logger.error(markers.andAppend("error", result), logName)
                    return HttpResponse.ok(
                        ResponseTO(
                            code = result.toCode(),
                            message = "Daily limit exceeded",
                        ),
                    )
                }
            }
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            return HttpResponse.serverError<Unit>()
        }
    }

    @Post("/send-message")
    fun sendMessageSync(
        @Body messageBody: String,
        authentication: Authentication,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val markers = Markers.append("accountId", accountId)
            .andAppend("messageBody", messageBody)

        return try {
            messageProcessorService.process(messageBody)
            HttpResponse.ok<Unit>()
        } catch (e: Exception) {
            logger.error(markers, "ChatbotAIController#sendMessageSync", e)
            HttpResponse.serverError<Unit>()
        }
    }

    @Post("/onboarding-test-pix")
    open fun createOnboardingTestPix(
        @Body body: CreateOnboardingTestPixTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        val logName = "ChatbotAIController#createOnboardingTestPix"

        try {
            val accountId = authentication.toAccountId()
            val pixKey = body.key?.let { PixKey(value = it.keyValue, type = it.keyType) }

            val markers = Markers.append("accountId", accountId.value).andAppend("customPixKey", pixKey)

            val wallet = walletService.findAllFounderWallets(accountId).firstOrNull {
                it.checkPrimary(accountId)
            }

            if (wallet == null) {
                val response = ResponseTO(code = "4012", message = "Wallet not found")
                logger.error(markers.andAppend("error", response), logName)
                return HttpResponse.badRequest(response)
            }

            val testPix = onboardingTestPixService.createChatbotExamplePayment(accountId, pixKey).getOrElse {
                val response = when (it) {
                    CreateOnboardingTestPixError.Conflict -> ResponseTO(code = "4008", message = "Test pix already created")
                    CreateOnboardingTestPixError.NoPixKeyFound -> ResponseTO(code = "4009", message = "Pix key not found")
                    CreateOnboardingTestPixError.PixKeyFromDifferentOwner -> ResponseTO(code = "4010", message = "Pix key does not belong to account owner")

                    CreateOnboardingTestPixError.AccountIdNotEligible,
                    CreateOnboardingTestPixError.PixNotCreated,
                    CreateOnboardingTestPixError.FundsNotTransferred,
                    -> ResponseTO(code = "4011", message = "Error creating example payment")

                    is CreateOnboardingTestPixError.PixAlreadyCreated -> {
                        logger.warn(markers.andAppend("pixCreated", it.billView), logName)
                        return HttpResponse.ok(billTOBuilder.toBillTO(it.billView, member = wallet.founder, imageUrl = null))
                    }
                }

                markers.andAppend("error", response)

                if (response.code == "4011") {
                    logger.error(markers, logName)
                } else {
                    logger.warn(markers, logName)
                }

                return HttpResponse.badRequest(response)
            }

            logger.info(markers.andAppend("pixCreated", testPix), logName)
            return HttpResponse.ok(billTOBuilder.toBillTO(testPix, member = wallet.founder, imageUrl = null))
        } catch (ex: Exception) {
            logger.error(logName, ex)
            return HttpResponse.serverError<Unit>()
        }
    }

    @Post("/systemActivities")
    fun getSystemActivities(
        authentication: Authentication,
        @Body systemActivityRequestTO: SystemActivityRequestTO,
    ): HttpResponse<*> {
        val logName = "ChatbotAIController#getSystemActivity"

        val accountId = authentication.toAccountId()
        val markers = Markers.append("accountId", accountId.value).andAppend("systemActivityRequestTO", systemActivityRequestTO)

        val systemActivitiesTO = chatbotAIService.getSystemActivities(accountId, systemActivityRequestTO.systemActivityTypes).map {
            it.toSystemActivityTO()
        }
        val systemActivityResponseTO = SystemActivityResponseTO(systemActivitiesTO)

        logger.info(markers.andAppend("systemActivityResponseTO", systemActivityResponseTO), logName)
        return HttpResponse.ok(systemActivityResponseTO)
    }

    @Post("/systemActivity")
    fun setSystemActivityFlag(
        authentication: Authentication,
        @Body systemActivity: SystemActivityTO,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val logName = "ChatbotAIController#setSystemActivityFlag"
        val markers = Markers.append("accountId", accountId.value).andAppend("systemActivity", systemActivity)
        return chatbotAIService.setSystemActivityFlag(accountId, systemActivity.systemActivityType, systemActivity.value).fold(
            ifLeft = {
                when (it) {
                    SystemActivityErrorReason.NotImplemented -> {
                        logger.error(markers.andAppend("error", it), logName)
                        HttpResponse.badRequest()
                    }

                    is SystemActivityErrorReason.Failure -> {
                        logger.error(markers, logName, it.error)
                        HttpResponse.serverError()
                    }
                }
            },
            ifRight = {
                logger.info(markers, logName)
                HttpResponse.ok<Unit>()
            },
        )
    }

    @Post("/checkBills")
    fun checkBills(
        authentication: Authentication,
        @Body body: CheckBillsTO,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val logName = "ChatbotAIController#getBill"
        val markers = Markers.append("accountId", accountId.value).andAppend("body", body)
        if (body.bills.isEmpty()) {
            return HttpResponse.badRequest(ResponseTO("4001", "Bill not found"))
        }

        if (body.bills.size > 60) {
            return HttpResponse.badRequest(ResponseTO("40038", "Too many bills"))
        }

        return try {
            val wallet = walletService.findWallets(accountId).firstOrNull { it.checkPrimary(accountId) }
                ?: return HttpResponse.notFound<Unit>()

            val billIds = body.bills.map { BillId(it) }
            val bills = chatbotAIService.findBills(WalletId(wallet.id.value), billIds).getOrElse {
                logger.error(markers.andAppend("error", it), logName)
                return when (it) {
                    is FindBillError.BillNotFound -> HttpResponse.badRequest(ResponseTO("4001", "Bill not found"))
                    is FindBillError.BillNotActive -> HttpResponse.badRequest(ResponseTO("40037", "Bill not active"))
                }
            }
            logger.info(markers, logName)
            HttpResponse.ok(BillsListTO(bills = bills.map { billTOBuilder.toBillTO(it, member = wallet.founder, imageUrl = null) }))
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    @Post("/manualEntry")
    fun createManualEntry(
        authentication: Authentication,
        @Body body: CreateManualEntryTO,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val logName = "ChatbotAIController#createManualEntry"
        val markers = Markers.append("accountId", accountId.value).andAppend("body", body)

        return try {
            val wallet = walletService.findWallets(accountId).firstOrNull { it.checkPrimary(accountId) }
                ?: return HttpResponse.notFound<Unit>()

            val dueDate = LocalDate.parse(body.dueDate, dateFormat)

            val manualEntryType = ManualEntryType.valueOf(body.type)

            val status = when (manualEntryType) {
                ManualEntryType.REMINDER -> ManualEntryStatus.ACTIVE

                ManualEntryType.EXTERNAL_PAYMENT,
                ManualEntryType.INCOME,
                ManualEntryType.UTILITY_INVOICE,
                ManualEntryType.VEHICLE_DEBT,
                -> ManualEntryStatus.PAID
            }

            val result = manualEntryService.create(
                walletId = wallet.id,
                title = body.title,
                description = body.description,
                amount = body.amount,
                dueDate = dueDate,
                source = ActionSource.VirtualAssistant(accountId),
                type = manualEntryType,
                status = status,
                categoryId = null,
                externalId = null,
            )

            result.fold(
                ifLeft = {
                    logger.error(markers.andAppend("error", it), logName)
                    when (it) {
                        ManualEntryError.CategoryNotFound -> HttpResponse.badRequest(ResponseTO("40011", "Category not found"))
                        else -> HttpResponse.serverError(ResponseTO("5001", "Internal server error"))
                    }
                },
                ifRight = {
                    logger.info(markers.andAppend("manualEntryId", it.id.value), logName)
                    HttpResponse.created(CreateManualEntryResponseTO(manualEntryId = it.id.value))
                },
            )
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    @Delete("/manualEntry/{manualEntryId}")
    fun removeManualEntry(
        authentication: Authentication,
        @PathVariable manualEntryId: String,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val logName = "ChatbotAIController#createManualEntry"
        val markers = Markers.append("accountId", accountId.value).andAppend("manualEntryId", manualEntryId)

        return try {
            val wallet = walletService.findWallets(accountId).firstOrNull { it.checkPrimary(accountId) }
                ?: return HttpResponse.notFound<Unit>()

            val result = manualEntryService.ignore(ManualEntryId(manualEntryId), wallet.id)
            result.fold(
                ifLeft = {
                    logger.error(markers.andAppend("error", it), logName)
                    HttpResponse.serverError(ResponseTO("5001", "Internal server error"))
                },
                ifRight = {
                    logger.info(markers, logName)
                    HttpResponse.ok<Unit>()
                },
            )
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    private fun handleSweepingTransferError(errorReason: SweepingTransferErrorReason): MutableHttpResponse<ResponseTO> {
        when (errorReason) {
            is SweepingTransferErrorReason.GenericReason -> {
                return HttpResponse.badRequest(
                    ResponseTO(
                        "4003",
                        errorReason.error,
                    ),
                )
            }

            is SweepingTransferErrorReason.LimitExceeded -> {
                val code = when (errorReason.limitType) {
                    LimitType.DAILY -> "40031"
                    LimitType.WEEKLY -> "40032"
                    LimitType.MONTHLY -> "40033"
                    LimitType.YEARLY -> "40034"
                    LimitType.GLOBAL -> "40035"
                    LimitType.TRANSACTION -> "40036"
                    LimitType.UNKNOWN -> "40037"
                }

                return HttpResponse.badRequest(
                    ResponseTO(
                        code,
                        "Open finance limit exceeded",
                    ),
                )
            }
        }
    }

    private fun OFSweepingConsent.toSweepingConsentTO() = SweepingConsentTO(
        participantId = participantId.value,
        participantName = participantName,
        participantShortName = participantShortName,
        transactionLimit = transactionLimit,
        lastSuccessfulCashIn = lastSucessfulCashIn?.format(dateTimeFormat),
        periodicUsage = periodicUsage.toSweepingConsentPeriodicUsageTO(),
    )

    companion object {
        private val logger = LoggerFactory.getLogger(ChatbotAIController::class.java)
    }
}

private fun OFSweepingConsentPeriodicUsage.toSweepingConsentPeriodicUsageTO() = SweepingConsentPeriodicUsageTO(
    consentId = consentId.value,
    daily = daily.toSweepingConsentPeriodicLimitUsageTO(),
    weekly = weekly.toSweepingConsentPeriodicLimitUsageTO(),
    monthly = monthly.toSweepingConsentPeriodicLimitUsageTO(),
    yearly = yearly.toSweepingConsentPeriodicLimitUsageTO(),
    totalLimit = totalLimit,
    totalUsed = totalUsed,
)

private fun OFSweepingConsentPeriodicLimitUsage.toSweepingConsentPeriodicLimitUsageTO() = SweepingConsentPeriodicLimitUsageTO(
    amountLimit = amountLimit,
    amountUsed = amountUsed,
    quantityLimit = quantityLimit,
    quantityUsed = quantityUsed,
)

data class SystemActivityRequestTO(
    val systemActivityTypes: List<String>,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class SystemActivityResponseTO(
    val systemActivities: List<SystemActivityTO>,
)

data class SystemActivityTO(
    val systemActivityType: String,
    val value: Boolean,
)

fun ChatbotSystemActivity.toSystemActivityTO() = SystemActivityTO(
    systemActivityType = type.name,
    value = value,
)

data class QrCodeResponseTO(
    val qrCode: String,
)

private data class SubscriptionFeeResponseTO(val fee: Long)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class SubscriptionResponseTO(val subscription: SubscriptionTO? = null)

data class SubscriptionTO(val fee: Long?, val paymentStatus: String, val dueDate: String, val type: SubscriptionType)

data class OnePixPayITPTO(
    val authorizationServerId: String,
    val walletId: String?,
    val bills: List<String>,
    val useCurrentBalance: Boolean = true,
)

data class WalletIdAndBillsListTO(
    val walletId: String,
    val bills: List<String>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ChatbotScheduleBillsTO(
    val walletId: String,
    val bills: List<String>,
    val sweepingRequest: SweepingRequestTO? = null,
    val authorizationToken: String? = null,
    val scheduleToDueDate: Boolean = false,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SweepingRequestTO(
    val transactionId: String,
    val amount: Long,
    val participantId: String? = null,
    val retry: Boolean = false,
) {
    fun toDomain(): SweepingRequest {
        return SweepingRequest(
            transactionId = ExternalTransactionId(transactionId),
            amount = amount,
            participantId = participantId?.let { OpenFinanceParticipantId(it) },
            retry = retry,
        )
    }
}

data class MarkReminderAsDoneTO(
    val reminderId: String,
)

data class OnePixPayTO(
    val bills: List<String>,
    val walletId: String?,
    val useCurrentBalance: Boolean = true,
)

data class ItpTokenResponseTO(
    val token: String,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class ContactsListTO(
    val contacts: List<ContactTO>,
)

data class ListPaymentOrganizationsResponseTO(
    @JsonInclude val otherFinancialInstitutions: List<FinancialInstitutionTO>,
    @JsonInclude val lastUsed: FinancialInstitutionTO?,
)

data class FinancialInstitutionTO(
    val id: String,
    val title: String,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class AccountAndWalletsTO(
    val account: AccountTO,
    val wallets: List<WalletBillsComingDueToNotifyTO>,
)

fun PartialAccount.toAccountTO(msisdn: String): AccountTO {
    return AccountTO(
        id = id.value,
        fullName = name,
        status = status,
        msisdn = msisdn,
        paymentStatus = AccountPaymentStatus.UpToDate,
        subscriptionType = subscriptionType,
    )
}

@JsonInclude(JsonInclude.Include.ALWAYS)
data class WalletBillsComingDueToNotifyTO(
    val walletName: String,
    val walletId: String,
    val bills: List<BillTO>,
    val activeConsents: List<SweepingConsentTO>,
)

data class BillsListTO(
    val bills: List<BillTO>,
)

fun Subscription.toTO() = SubscriptionTO(
    fee = amount,
    paymentStatus = paymentStatus.name,
    dueDate = nextEffectiveDueDate.format(dateFormat),
    type = SubscriptionType.PIX,
)

fun InAppSubscription.toTO() = SubscriptionTO(
    fee = null,
    paymentStatus = when (status) {
        InAppSubscriptionStatus.EXPIRED -> SubscriptionPaymentStatus.OVERDUE.name
        InAppSubscriptionStatus.ACTIVE -> SubscriptionPaymentStatus.PAID.name
    },
    dueDate = endsAt.format(dateFormat),
    type = SubscriptionType.IN_APP,
)

fun emptyBalanceForecast(): WalletBalanceForecastTO {
    return WalletBalanceForecastTO(
        walletId = "",
        amount = 0L,
        open = BalanceForecastTO(
            amountToday = 0L,
            amountWeek = 0L,
            amountFifteenDays = 0L,
            amountThirtyDays = 0L,
            amountMonth = 0L,
            amountNextMonth = 0L,
        ),
        dates = BalanceForecastDatesTO(
            today = "",
            week = "",
            fifteenDays = "",
            month = "",
            thirtyDays = "",
            nextMonth = "",
        ),
        overdueAmount = 0L,
        scheduled = BalanceForecastTO(
            amountToday = 0L,
            amountWeek = 0L,
            amountFifteenDays = 0L,
            amountThirtyDays = 0L,
            amountMonth = 0L,
            amountNextMonth = 0L,
        ),
    )
}

fun AvailableLimitResult.toCode(): String {
    return when (this) {
        is AvailableLimitResult.Success -> "0"
        is AvailableLimitResult.AssistantLimitExceeded -> "4004"
        is AvailableLimitResult.PixLimitExceeded -> "4006"
    }
}

@JsonInclude(JsonInclude.Include.ALWAYS)
data class GetSweepingConsentResponseTO(
    val activeConsents: List<SweepingConsentTO>,
) {
    val hasConsent: Boolean = activeConsents.isNotEmpty()
}

data class SweepingConsentTO(
    val participantId: String,
    val participantName: String,
    val participantShortName: String,
    val transactionLimit: Long,
    val lastSuccessfulCashIn: String?,
    val periodicUsage: SweepingConsentPeriodicUsageTO,
)

data class SweepingConsentPeriodicUsageTO(
    val consentId: String,
    val daily: SweepingConsentPeriodicLimitUsageTO,
    val weekly: SweepingConsentPeriodicLimitUsageTO,
    val monthly: SweepingConsentPeriodicLimitUsageTO,
    val yearly: SweepingConsentPeriodicLimitUsageTO,
    val totalLimit: Long,
    val totalUsed: Long,
)

data class SweepingConsentPeriodicLimitUsageTO(
    val amountLimit: Long,
    val amountUsed: Long,
    val quantityLimit: Long,
    val quantityUsed: Long,
)

data class AvailableLimitTO(
    val walletId: String,
    val amount: Long,
    val type: AvailableLimitType,
)

enum class AvailableLimitType {
    PIX, SCHEDULE_BILLS
}

data class ValidatePixTO(
    val walletId: String,
    val keyType: PixKeyType,
    val keyValue: String,
    val amount: Long,
)

data class ValidatePixResponseTO(
    val pixDetails: PixKeyDetailsTO,
    val code: String,
)

@Introspected
@JsonIgnoreProperties(ignoreUnknown = true)
data class CreateChatbotPixTO(
    @field:Valid val recipient: RequestPixRecipientTO,
    @field:Positive(message = "Amount must be natural number") val amount: Long,
    val description: String? = null,
    @field:Pattern(
        regexp = "^\\d{4}-\\d{2}-\\d{2}$",
        message = "Due date should be format yyyy-MM-dd",
    ) val dueDate: String,
    val authorizationToken: String? = null,
    val transactionId: String,
    val retryTransaction: Boolean,
    val sweepingRequest: SweepingRequestTO?,
) {
    fun toCreatePixTO(): CreatePixTO {
        return CreatePixTO(
            recipient = recipient,
            amount = amount,
            description = description ?: "",
            dueDate = dueDate,
        )
    }
}

@Introspected
@JsonIgnoreProperties(ignoreUnknown = true)
data class RetrySweepingTransferTO(
    @field:Positive(message = "Amount must be natural number") val amount: Long,
    val participantId: String?,
)

data class ChatbotPixQRCodeValidateTO(
    val qrCodeValue: String,
    val amount: Long?,
)

data class PixQrCodeDetailsResultTO(
    val pixKeyDetails: PixKeyDetails,
    val e2e: String,
    val qrCodeInfo: PixQrCodeData? = null,
    val code: String,
    val amount: Long,
)

data class CreateOnboardingTestPixTO(
    val key: OnboardingTestPixKeyTO?,
)

data class OnboardingTestPixKeyTO(
    val keyType: PixKeyType,
    val keyValue: String,
)

data class ValidateBillTO(
    val digitableLine: String,
    val dueDate: String?,
)

data class AddBoletoTO(
    @field:Size(min = 47, max = 48, message = "Digitable line should be 47 or 48 digits") val digitableLine: String,
    val dueDate: String?,
    val description: String?,
)

data class BillValidationResultTO(
    val dueDate: String?,
    val settleDate: String?,
    val amount: Long,
    val assignor: String,
)

data class CreateBillResultTO(
    val billId: String,
    val amount: Long,
    val dueDate: String,
    val assignor: String,
    val billType: BillType,
)

data class CheckBillsTO(
    val bills: List<String>,
)

enum class BillErrors {
    ALREADY_PAID,
    PAYMENT_LIMIT_EXPIRED,
    NOT_PAYABLE,
    BARCODE_NOT_FOUND,
    EMPTY_AMOUNT,
    ALREADY_EXISTS,
    UNABLE_TO_VALIDATE,
}

fun BillErrors.toResponseTO(): ResponseTO {
    return when (this) {
        BillErrors.NOT_PAYABLE -> {
            ResponseTO(
                code = "4001",
                message = "Bill not payable",
            )
        }

        BillErrors.ALREADY_PAID -> {
            ResponseTO(
                code = "4002",
                message = "Bill already paid",
            )
        }

        BillErrors.PAYMENT_LIMIT_EXPIRED -> {
            ResponseTO(
                code = "4003",
                message = "Payment limit expired",
            )
        }

        BillErrors.BARCODE_NOT_FOUND -> {
            ResponseTO(
                code = "4004",
                message = "Barcode not found",
            )
        }

        BillErrors.EMPTY_AMOUNT -> {
            ResponseTO(
                code = "4005",
                message = "Empty amount",
            )
        }

        BillErrors.ALREADY_EXISTS -> {
            ResponseTO(
                code = "4006",
                message = "Bill already exists",
            )
        }

        BillErrors.UNABLE_TO_VALIDATE -> {
            ResponseTO(
                code = "4007",
                message = "Unable to validate bill",
            )
        }
    }
}

data class CreateManualEntryTO(
    val title: String,
    val type: String,
    val description: String,
    val amount: Long,
    val dueDate: String,
)

data class CreateManualEntryResponseTO(
    val manualEntryId: String,
)

data class OFBalancesRequest(
    val walletId: String,
    val participantIds: List<String>,
)

data class OFBalancesResponse(
    val balances: List<OFBalanceTO>,
)

data class OFBalanceTO(
    val participantId: String,
    val amount: Long?,
)