package ai.friday.billpayment.modules.chatbotai.adapters.chatbot

import ai.friday.billpayment.adapters.api.AccountTO
import ai.friday.billpayment.adapters.api.BillTO
import ai.friday.billpayment.adapters.api.builders.BillTOBuilder
import ai.friday.billpayment.adapters.api.toAccountTO
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountPaymentStatus
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.chatbot.ChatbotMessagePublisher
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.OpenFinanceIncentiveType
import ai.friday.billpayment.app.integrations.TestPixReminderType
import ai.friday.billpayment.app.integrations.WelcomeMessageType
import ai.friday.billpayment.app.message.QueueMessage
import ai.friday.billpayment.app.notification.BillComingDueLastWarnDetails
import ai.friday.billpayment.app.notification.BillComingDueRegularDetails
import ai.friday.billpayment.app.notification.BillPaymentNotification
import ai.friday.billpayment.app.notification.ChatBotNotificationDetails
import ai.friday.billpayment.app.notification.ChatbotNotification
import ai.friday.billpayment.app.notification.ChatbotNotificationWithAccount
import ai.friday.billpayment.app.notification.ChatbotNotificationWithPartialAccount
import ai.friday.billpayment.app.notification.MultiChannelNotification
import ai.friday.billpayment.app.notification.OpenFinanceIncentiveDetails
import ai.friday.billpayment.app.notification.PixTransactionDetails
import ai.friday.billpayment.app.notification.RegisterCompleted
import ai.friday.billpayment.app.notification.TestPixreminderDetails
import ai.friday.billpayment.app.notification.UpdateAccountStatus
import ai.friday.billpayment.app.notification.WelcomeDetails
import ai.friday.billpayment.app.notification.WhatsappNotification
import ai.friday.billpayment.app.notification.WhatsappNotificationBuilder
import ai.friday.billpayment.app.payment.checkout.RequestProtocol
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.modules.chatbotai.ChatbotAI
import ai.friday.morning.log.andAppend
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonTypeInfo
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Property
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@ConfigurationProperties("integrations.chatbotai")
data class ChatbotConfiguration @ConfigurationInject constructor(
    val clientid: String,
    val secret: String,
    val sendMessageProtocol: RequestProtocol = RequestProtocol.QUEUE,
) {
    fun isHttp() = sendMessageProtocol == RequestProtocol.HTTP
}

@ChatbotAI
open class ChatbotAdapter(
    private val messagePublisher: MessagePublisher,
    @Property(name = "sqs.queues.chatBotNotificationGateway") private val chatBotNotificationGateway: String,
    @Property(name = "sqs.queues.chatBotWebhooks") private val chatBotWebhooksQueue: String,
    @Property(name = "sqs.queues.chatBotStateUpdate") private val chatBotStateUpdate: String,
    private val chatBotHttpClient: ChatBotHttpClient,
    private val chatbotConfiguration: ChatbotConfiguration,
    private val billTOBuilder: BillTOBuilder,
    private val whatsappNotificationBuilder: WhatsappNotificationBuilder,
) : ChatbotMessagePublisher {

    private val logger = LoggerFactory.getLogger(this::class.java)
    override fun publishNotification(multiChannelNotification: MultiChannelNotification) {
        val msisdn = multiChannelNotification.receiver.mobilePhone

        val notification = whatsappNotificationBuilder.buildFromMultiChannelNotification(multiChannelNotification)

        val message = ChatBotNotificationGatewayTO(
            walletId = multiChannelNotification.walletId.value,
            account = multiChannelNotification.receiver.toAccountTO(),
            details = GenericNotificationDetailsTO(
                notification = notification,
            ),
        )

        logger.info(
            Markers.append("accountId", multiChannelNotification.receiver.accountId.value)
                .andAppend("mobilePhone", msisdn)
                .andAppend("notification", notification),
            "ChatbotAdapter#publishGenericNotification",
        )

        send(message = message, queueName = chatBotNotificationGateway)
    }

    override fun publishGenericWhatsappNotification(account: Account?, notification: WhatsappNotification) {
        val msisdn = account?.mobilePhone ?: notification.receiver.msisdn

        val message = ChatBotNotificationGatewayTO(
            walletId = account?.accountId?.value ?: "",
            account = account?.toAccountTO() ?: buildUnregisteredAccountTO(msisdn),
            details = GenericNotificationDetailsTO(
                notification = notification,
            ),
        )

        logger.info(
            Markers.append("accountId", account?.accountId?.value)
                .andAppend("mobilePhone", msisdn)
                .andAppend("notification", notification),
            "ChatbotAdapter#publishGenericNotification",
        )

        send(message = message, queueName = chatBotNotificationGateway)
    }

    override fun publishChatbotNotification(chatbotNotification: ChatbotNotification) {
        val notification = when (chatbotNotification) {
            is ChatbotNotificationWithAccount -> ChatBotNotificationGatewayTO(
                walletId = chatbotNotification.walletId.value,
                account = chatbotNotification.account.toAccountTO(),
                details = chatbotNotification.details.toChatBotNotificationDetailsTO(),
            )

            is ChatbotNotificationWithPartialAccount -> ChatBotNotificationGatewayTO(
                walletId = chatbotNotification.walletId.value,
                account = AccountTO(
                    id = chatbotNotification.register.accountId.value,
                    fullName = chatbotNotification.partialAccount.name,
                    document = chatbotNotification.register.document!!.value,
                    documentType = chatbotNotification.register.document!!.type.value,
                    status = chatbotNotification.partialAccount.status,
                    msisdn = chatbotNotification.register.mobilePhone!!.msisdn,
                    paymentStatus = AccountPaymentStatus.UpToDate,
                    accountGroups = chatbotNotification.partialAccount.groups,
                    subscriptionType = chatbotNotification.partialAccount.subscriptionType,
                ),
                details = chatbotNotification.details.toChatBotNotificationDetailsTO(),
            )
        }
        send(message = notification, queueName = chatBotNotificationGateway)
    }

    override fun publishWebhook(webhook: Map<String, Any>) {
        send(message = webhook, queueName = chatBotWebhooksQueue)
    }

    override fun publishStateUpdate(account: Account, paymentStatus: AccountPaymentStatus, status: AccountStatus) {
        val state = UpdateAccountStatusTO(
            accountId = account.accountId.value,
            paymentStatus = paymentStatus,
            msisdn = account.mobilePhone,
            status = status,
        )

        send(message = state, queueName = chatBotStateUpdate)
    }

    private fun send(message: Any, queueName: String) {
        if (chatbotConfiguration.isHttp()) {
            val markers = Markers.append("queueName", queueName)
                .andAppend("message", message)
            try {
                chatBotHttpClient.send(queueName, message)
            } catch (e: Exception) {
                logger.error(markers, "ChatbotAdapter#send", e)
                throw e
            }
        } else {
            val queueMessage = QueueMessage(
                queueName = queueName,
                jsonObject = getObjectMapper().writeValueAsString(message),
                delaySeconds = null,
            )

            messagePublisher.sendMessage(queueMessage = queueMessage)
        }
    }

    private fun buildUnregisteredAccountTO(msisdn: String): AccountTO {
        return AccountTO(
            id = "",
            fullName = "",
            document = "",
            documentType = "",
            accountGroups = listOf(),
            status = AccountStatus.REGISTER_INCOMPLETE,
            msisdn = msisdn,
            paymentStatus = AccountPaymentStatus.UpToDate,
            sweepingAccount = false,
            subscriptionType = SubscriptionType.PIX, // TODO - será que é melhor ser null?
        )
    }

    private fun ChatBotNotificationDetails.toChatBotNotificationDetailsTO(): ChatBotNotificationDetailsTO {
        return when (this) {
            is BillComingDueLastWarnDetails -> BillComingDueLastWarnDetailsTO(
                bills = bills.map { billTOBuilder.toBillTO(it, wallet.founder) },
                walletName = wallet.name,
                hint = this.hint,
            )

            is BillComingDueRegularDetails -> BillComingDueRegularDetailsTO(
                bills = bills.map { billTOBuilder.toBillTO(it, wallet.founder) },
                walletName = wallet.name,
            )

            is OpenFinanceIncentiveDetails -> OpenFinanceIncentiveDetailsTO(
                type = this.type,
                userOptedOut = this.userOptedOut,
                bank = this.bank,
                bill = this.billInfo?.let { (wallet, billView) -> billTOBuilder.toBillTO(billView, wallet.founder) },
            )

            is PixTransactionDetails -> PixTransactionDetailsTO(
                transactionId = this.transactionId,
                recipientName = this.recipientName,
                recipientDocument = this.recipientDocument,
                recipientInstitution = this.recipientInstitution,
            )

            RegisterCompleted -> RegisterCompletedTO
            is TestPixreminderDetails -> TestPixreminderDetailsTO(
                bills = this.bills.map { billTOBuilder.toBillTO(it, wallet.founder) },
                reminderType = this.reminderType,
            )

            is UpdateAccountStatus -> UpdateAccountStatusTO(
                accountId = this.accountId.value,
                msisdn = this.msisdn,
                paymentStatus = this.paymentStatus,
                status = this.status,
            )

            is WelcomeDetails -> WelcomeDetailsTO(
                type = this.type,
            )
        }
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class ChatBotNotificationGatewayTO(
    val walletId: String,
    val account: AccountTO,
    val details: ChatBotNotificationDetailsTO,
)

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY)
sealed interface ChatBotNotificationDetailsTO

data class BillComingDueRegularDetailsTO(
    val bills: List<BillTO>,
    val walletName: String,
) : ChatBotNotificationDetailsTO

data class BillComingDueLastWarnDetailsTO(
    val bills: List<BillTO>,
    val walletName: String,
    val hint: String?,
) : ChatBotNotificationDetailsTO

data class WelcomeDetailsTO(
    val type: WelcomeMessageType,
) : ChatBotNotificationDetailsTO

data class PixTransactionDetailsTO(
    val transactionId: String,
    val recipientName: String,
    val recipientDocument: String,
    val recipientInstitution: String,
) : ChatBotNotificationDetailsTO

data class TestPixreminderDetailsTO(
    val bills: List<BillTO>,
    val reminderType: TestPixReminderType,
) : ChatBotNotificationDetailsTO

data class OpenFinanceIncentiveDetailsTO(
    val type: OpenFinanceIncentiveType,
    val userOptedOut: Boolean,
    val bank: String?,
    val bill: BillTO?,
) : ChatBotNotificationDetailsTO

data class UpdateAccountStatusTO(
    val accountId: String,
    val msisdn: String,
    val paymentStatus: AccountPaymentStatus,
    val status: AccountStatus,
) : ChatBotNotificationDetailsTO

data class GenericNotificationDetailsTO(
    val notification: BillPaymentNotification,
) : ChatBotNotificationDetailsTO

data object RegisterCompletedTO : ChatBotNotificationDetailsTO