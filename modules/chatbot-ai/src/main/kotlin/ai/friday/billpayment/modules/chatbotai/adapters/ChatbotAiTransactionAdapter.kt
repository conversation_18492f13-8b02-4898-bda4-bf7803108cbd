package ai.friday.billpayment.modules.chatbotai.adapters

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.app.integrations.ChatbotUserId
import ai.friday.billpayment.app.integrations.LimitType
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.modules.chatbotai.ChatbotAI
import ai.friday.billpayment.modules.chatbotai.app.ChatbotAiTransactionPaymentStatus
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.annotation.ClientFilter
import io.micronaut.http.annotation.FilterMatcher
import io.micronaut.http.annotation.RequestFilter
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import kotlin.jvm.optionals.getOrElse
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FilterMatcher
@MustBeDocumented
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS, AnnotationTarget.VALUE_PARAMETER)
annotation class TenantHeader

@TenantHeader
@Singleton
@ClientFilter
class TenantHeaderClientFilter(@Property(name = "tenant.id") private val tenantId: String) {
    @RequestFilter
    fun doFilter(request: MutableHttpRequest<*>) {
        request.header("X-TENANT-ID", tenantId)
    }
}

@ChatbotAI
class ChatbotAiTransactionAdapter(
    @param:TenantHeader
    @param:Client(value = "\${integrations.chatbotai.host}")
    private val httpClient: RxHttpClient,
    @Property(name = "integrations.chatbotai.clientid") private val clientId: String,
    @Property(name = "integrations.chatbotai.secret") private val secretId: String,
) {
    fun findTransaction(transactionId: String): Either<ChatbotAiTransactionError, ChatbotAiTransactionTO> {
        val httpRequest =
            HttpRequest
                .GET<ChatbotAiTransactionTO>("/transaction/$transactionId")
                .basicAuth(clientId, secretId)
                .accept(MediaType.APPLICATION_JSON_TYPE)

        val call = httpClient.retrieve(httpRequest, ChatbotAiTransactionTO::class.java)

        val markers = Markers.append("transactionId", transactionId)
        val logName = "ChatbotAiTransactionAdapter#findTransaction"

        return try {
            val response = call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", response), logName)
            response.right()
        } catch (e: HttpClientResponseException) {
            logger.error(
                markers.andAppend("httpStatus", e.status).andAppend("responseBody", e.response.body()),
                logName,
                e,
            )
            when (e.status) {
                HttpStatus.NOT_FOUND -> ChatbotAiTransactionError.TransactionNotFound.left()
                else -> ChatbotAiTransactionError.UnknownError(e.message.orEmpty()).left()
            }
        } catch (e: Exception) {
            logger.error(
                markers.andAppend("error", e.localizedMessage),
                logName,
                e,
            )
            ChatbotAiTransactionError.UnknownError(e.message.orEmpty()).left()
        }
    }

    fun findTransactions(transactionGroupId: String, userId: ChatbotUserId): Either<ChatbotAiTransactionError, List<ChatbotAiTransactionTO>> {
        val markers = Markers.append("transactionGroupId", transactionGroupId)
            .andAppend("userId", userId.value)
        val logName = "ChatbotAiTransactionAdapter#findTransactions"

        return try {
            val httpRequest =
                HttpRequest
                    .GET<ChatbotAiTransactionTO>("/transaction/group/$transactionGroupId/${userId.value}")
                    .basicAuth(clientId, secretId)
                    .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.retrieve(httpRequest, Argument.listOf(ChatbotAiTransactionTO::class.java))

            val response = call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", response), logName)
            response.right()
        } catch (e: HttpClientResponseException) {
            logger.error(
                markers.andAppend("httpStatus", e.status).andAppend("responseBody", e.response.body()),
                logName,
                e,
            )
            when (e.status) {
                HttpStatus.NOT_FOUND -> ChatbotAiTransactionError.TransactionNotFound.left()
                else -> ChatbotAiTransactionError.UnknownError(e.message.orEmpty()).left()
            }
        } catch (e: Exception) {
            logger.error(
                markers.andAppend("error", e.localizedMessage),
                logName,
                e,
            )
            ChatbotAiTransactionError.UnknownError(e.message.orEmpty()).left()
        }
    }

    fun confirmTransaction(
        transactionId: String,
        userId: ChatbotUserId,
        authorizationToken: String,
        hasSetLimit: Boolean,
        transactionsAuthorized: Int,
    ): Either<ChatbotAiTransactionError, TransactionResult> {
        val httpRequest =
            HttpRequest
                .POST("/transaction/$transactionId/${userId.value}", Unit)
                .body(AuthorizationTO(authorizationToken, hasSetLimit, transactionsAuthorized))
                .basicAuth(clientId, secretId)
                .accept(MediaType.APPLICATION_JSON_TYPE)

        val call = httpClient.exchange(httpRequest)

        val markers = Markers.append("transactionId", transactionId).andAppend("userId", userId.value)
        val logName = "ChatbotAiTransactionAdapter#confirmTransaction"

        return try {
            call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", "Success"), logName)
            TransactionResult.Success.right()
        } catch (e: HttpClientResponseException) {
            logger.error(
                markers.andAppend("httpStatus", e.status).andAppend("responseBody", e.response.body()),
                logName,
                e,
            )
            when (e.status) {
                HttpStatus.NOT_FOUND -> ChatbotAiTransactionError.TransactionNotFound.left()
                HttpStatus.CONFLICT -> ChatbotAiTransactionError.IllegalTransactionState.left()
                HttpStatus.UNPROCESSABLE_ENTITY -> {
                    val responseTO = e.response.getBody(ResponseTO::class.java).getOrElse {
                        ResponseTO("40037", "")
                    }
                    val limitType = when (responseTO.code) {
                        "40031" -> LimitType.DAILY
                        "40032" -> LimitType.WEEKLY
                        "40033" -> LimitType.MONTHLY
                        "40034" -> LimitType.YEARLY
                        "40035" -> LimitType.GLOBAL
                        "40036" -> LimitType.TRANSACTION
                        else -> LimitType.UNKNOWN
                    }
                    ChatbotAiTransactionError.SweepingLimitExceeded(limitType).left()
                }

                else -> ChatbotAiTransactionError.UnknownError(e.message.orEmpty()).left()
            }
        } catch (e: Exception) {
            logger.error(
                markers.andAppend("error", e.localizedMessage),
                logName,
                e,
            )
            ChatbotAiTransactionError.UnknownError(e.message.orEmpty()).left()
        }
    }

    fun cancelTransaction(transactionId: String, userId: ChatbotUserId): Either<ChatbotAiTransactionError, TransactionResult> {
        val httpRequest =
            HttpRequest
                .DELETE("/transaction/$transactionId/${userId.value}", Unit)
                .basicAuth(clientId, secretId)
                .accept(MediaType.APPLICATION_JSON_TYPE)

        val call = httpClient.exchange(httpRequest)

        val markers = Markers.append("transactionId", transactionId).andAppend("userId", userId.value)
        val logName = "ChatbotAiTransactionAdapter#cancelTransaction"

        return try {
            call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", "Success"), logName)
            TransactionResult.Success.right()
        } catch (e: HttpClientResponseException) {
            logger.error(
                markers.andAppend("httpStatus", e.status).andAppend("responseBody", e.response.body()),
                logName,
                e,
            )
            when (e.status) {
                HttpStatus.NOT_FOUND -> ChatbotAiTransactionError.TransactionNotFound.left()
                HttpStatus.CONFLICT -> ChatbotAiTransactionError.IllegalTransactionState.left()
                else -> ChatbotAiTransactionError.UnknownError(e.message.orEmpty()).left()
            }
        } catch (e: Exception) {
            logger.error(
                markers.andAppend("error", e.localizedMessage),
                logName,
                e,
            )
            ChatbotAiTransactionError.UnknownError(e.message.orEmpty()).left()
        }
    }

    fun updatePaymentStatus(transactionId: String, userId: ChatbotUserId, paymentStatus: ChatbotAiTransactionPaymentStatus): Either<ChatbotAiTransactionError, TransactionResult> {
        val logName = "ChatbotAiTransactionAdapter#updatePaymentStatus"
        val markers = Markers.append("transactionId", transactionId)
            .andAppend("userId", userId.value)
            .andAppend("paymentStatus", paymentStatus)

        return try {
            val httpRequest =
                HttpRequest
                    .POST(
                        "/transaction/$transactionId/${userId.value}/updatePaymentStatus",
                        UpdateTransactionPaymentStatusTO(paymentStatus),
                    )
                    .basicAuth(clientId, secretId)
                    .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.exchange(httpRequest)

            call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", "Success"), logName)
            TransactionResult.Success.right()
        } catch (e: HttpClientResponseException) {
            logger.error(
                markers.andAppend("httpStatus", e.status).andAppend("responseBody", e.response.body()),
                logName,
                e,
            )
            when (e.status) {
                HttpStatus.NOT_FOUND -> ChatbotAiTransactionError.TransactionNotFound.left()
                HttpStatus.CONFLICT -> ChatbotAiTransactionError.IllegalTransactionState.left()
                else -> ChatbotAiTransactionError.UnknownError(e.message.orEmpty()).left()
            }
        } catch (e: Exception) {
            logger.error(
                markers.andAppend("error", e.localizedMessage),
                logName,
                e,
            )
            ChatbotAiTransactionError.UnknownError(e.message.orEmpty()).left()
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ChatbotAiTransactionAdapter::class.java)
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class ChatbotAiTransactionTO(
    val id: TransactionId,
    val groupId: String,
    val status: ChatbotAiTransactionStatusTO,
    val details: TransactionDetailsTO,
    val createdAt: String,
    val updatedAt: String,
)

enum class ChatbotAiTransactionTypeTO {
    PIX, SWEEPING, SCHEDULE_BILLS
}

enum class ChatbotAiTransactionStatusTO {
    ACTIVE,
    COMPLETED,
    EXPIRED,
    CANCELED,
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class TransactionDetailsTO(
    val type: ChatbotAiTransactionTypeTO,
    val totalAmount: Long? = null,
    val sweepingAmount: Long? = null,
    val sweepingParticipantId: String? = null,
    val bills: List<String>? = null,
    val pixKey: String? = null,
    val name: String? = null,
    val document: String? = null,
    val institution: String? = null,
)

data class AuthorizationTO(
    val authorizationToken: String,
    val hasSetLimit: Boolean,
    val transactionsAuthorized: Int,
)

sealed class ChatbotAiTransactionError : PrintableSealedClassV2() {
    data object TransactionNotFound : ChatbotAiTransactionError()
    data object IllegalTransactionState : ChatbotAiTransactionError()
    data class SweepingLimitExceeded(val limitType: LimitType) : ChatbotAiTransactionError()
    class UnknownError(val message: String) : ChatbotAiTransactionError()
}

sealed class TransactionResult {
    data object Success : TransactionResult()
}

data class UpdateTransactionPaymentStatusTO(
    val status: ChatbotAiTransactionPaymentStatus,
)