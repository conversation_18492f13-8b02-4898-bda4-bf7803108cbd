package ai.friday.billpayment.modules.chatbotai.app

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.integrations.ChatbotUserId
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.DailyPaymentLimitType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.chatbotai.ChatbotAI
import ai.friday.billpayment.modules.chatbotai.adapters.ChatbotAiTransactionAdapter
import ai.friday.billpayment.modules.chatbotai.adapters.ChatbotAiTransactionError
import ai.friday.billpayment.modules.chatbotai.adapters.ChatbotAiTransactionStatusTO
import ai.friday.billpayment.modules.chatbotai.adapters.ChatbotAiTransactionTO
import ai.friday.billpayment.modules.chatbotai.adapters.ChatbotAiTransactionTypeTO
import ai.friday.billpayment.modules.chatbotai.adapters.TransactionResult
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.module.kotlin.readValue
import com.nimbusds.jose.JOSEObjectType
import com.nimbusds.jose.JWSAlgorithm
import com.nimbusds.jose.JWSHeader
import com.nimbusds.jose.crypto.MACSigner
import com.nimbusds.jose.crypto.MACVerifier
import com.nimbusds.jose.util.Base64URL
import com.nimbusds.jwt.JWTClaimsSet
import com.nimbusds.jwt.SignedJWT
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@ChatbotAI
open class DefaultChatbotAiTransactionService(
    private val chatbotAiTransactionAdapter: ChatbotAiTransactionAdapter,
    private val findBillService: FindBillService,
    private val chatbotTransactionAuthorizationConfiguration: ChatbotTransactionAuthorizationConfiguration,
    private val walletLimitsService: WalletLimitsService,
    private val systemActivityService: SystemActivityService,
) : ChatbotAiTransactionService {
    override fun findTransaction(transactionId: String, walletId: WalletId): Either<ChatbotAiTransactionError, ChatbotAiTransaction> {
        return chatbotAiTransactionAdapter.findTransaction(transactionId).map { transaction ->
            transaction.toDomain(walletId)
        }
    }

    override fun findTransactions(transactionGroupId: String, walletId: WalletId, userId: ChatbotUserId): Either<ChatbotAiTransactionError, List<ChatbotAiTransaction>> {
        return chatbotAiTransactionAdapter.findTransactions(transactionGroupId, userId).map {
            it.map { transaction ->
                transaction.toDomain(walletId)
            }
        }
    }

    override fun confirmTransaction(transactionId: String, account: Account, details: AuthorizationDetails): Either<ChatbotAiTransactionError, TransactionResult> {
        val authorizationToken = buildAuthorizationToken(transactionId, account.accountId, details)
        val hasSetLimit = walletLimitsService.findWalletLimitOrNull(account.defaultWalletId(), DailyPaymentLimitType.WHATSAPP_PAYMENT) != null
        val transactionsAuthorized = systemActivityService.incrementChatbotTransactionAuthorized(account.defaultWalletId())

        return chatbotAiTransactionAdapter.confirmTransaction(transactionId, account.chatbotUserId(), authorizationToken, hasSetLimit, transactionsAuthorized)
    }

    override fun cancelTransaction(transactionId: String, userId: ChatbotUserId): Either<ChatbotAiTransactionError, TransactionResult> {
        return chatbotAiTransactionAdapter.cancelTransaction(transactionId, userId)
    }

    override fun updatePaymentStatus(transactionId: String, userId: ChatbotUserId, paymentStatus: ChatbotAiTransactionPaymentStatus): Either<ChatbotAiTransactionError, TransactionResult> {
        return chatbotAiTransactionAdapter.updatePaymentStatus(transactionId, userId, paymentStatus)
    }

    override fun verifyAuthorization(authorizationToken: String?, billIds: List<BillId>): Boolean {
        val sortedBillIds = billIds.map { it.value }.sorted()

        val authorized = verifyAuthorization(authorizationToken) { details ->
            details.billIds == sortedBillIds
        }

        if (authorizationToken != null && !authorized) {
            logger.warn(
                Markers.append("authorizationToken", authorizationToken).andAppend("billIds", billIds),
                "DefaultChatbotAiTransactionService#verifyAuthorization",
            )
        }
        return authorized
    }

    override fun verifyAuthorization(authorizationToken: String?, pixKey: String, amount: Long): Boolean {
        val authorized = verifyAuthorization(authorizationToken) { details ->
            details.pixKey?.lowercase() == pixKey.lowercase() && details.totalAmount == amount
        }

        if (authorizationToken != null && !authorized) {
            logger.warn(
                Markers.append("authorizationToken", authorizationToken).andAppend("pixKey", pixKey).andAppend("amount", amount),
                "DefaultChatbotAiTransactionService#verifyAuthorization",
            )
        }
        return authorized
    }

    private fun ChatbotAiTransactionTO.toDomain(walletId: WalletId): ChatbotAiTransaction {
        val bills = details.bills?.map { billId ->
            findBillService.find(walletId = walletId, billId = BillId(billId))
        }
        val totalAmount = details.totalAmount ?: bills?.sumOf { it.amountTotal } ?: 0

        return ChatbotAiTransaction(
            id = id.value,
            groupId = groupId,
            status = status.toChatbotAiTransactionStatus(),
            details = TransactionDetails(
                type = details.type.toChatbotAiTransactionType(),
                totalAmount = totalAmount,
                bills = bills,
                pixKey = details.pixKey,
                sweepingAmount = details.sweepingAmount,
                sweepingParticipantId = details.sweepingParticipantId,
                name = details.name,
                document = details.document,
                institution = details.institution,
            ),
        )
    }

    private fun buildAuthorizationToken(transactionId: String, accountId: AccountId, details: AuthorizationDetails): String {
        // Para evitar que os campos null apareçam no JWT. Ele é serializado pelo GSON então não temos controle pelas anotações.
        val parsedDetails: Map<String, Any> = getObjectMapper().readValue(getObjectMapper().writeValueAsString(details))

        val claimsSet = JWTClaimsSet.Builder()
            .claim("accountId", accountId.value)
            .claim("transactionId", transactionId)
            .claim("details", parsedDetails)
            .claim("iat", getZonedDateTime().toEpochSecond())
            .build()

        val signer = MACSigner(chatbotTransactionAuthorizationConfiguration.secret.toByteArray())

        val signedJWT = SignedJWT(
            JWSHeader.Builder(JWSAlgorithm.HS256)
                .type(JOSEObjectType.JWT)
                .build(),
            claimsSet,
        )

        signedJWT.sign(signer)

        return signedJWT.serialize()
    }

    private fun verifyAuthorization(authorizationToken: String?, verifierFunction: (AuthorizationDetails) -> Boolean): Boolean {
        if (authorizationToken == null) return false

        val tokenParts = authorizationToken.split(".")

        if (tokenParts.size < 3) {
            return false
        }

        val signedJWT = SignedJWT(Base64URL(tokenParts[0]), Base64URL(tokenParts[1]), Base64URL(tokenParts[2]))

        val verifier = MACVerifier(chatbotTransactionAuthorizationConfiguration.secret)

        if (!signedJWT.verify(verifier)) {
            return false
        }

        val details: AuthorizationDetails = try {
            val detailsClaim = signedJWT.jwtClaimsSet.getJSONObjectClaim("details")
            getObjectMapper().readValue(getObjectMapper().writeValueAsString(detailsClaim))
        } catch (e: Throwable) {
            return false
        }

        return verifierFunction(details)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ChatbotAiTransactionService::class.java)
    }
}

data class ChatbotAiTransaction(
    val id: String,
    val groupId: String,
    val status: ChatbotAiTransactionStatus,
    val details: TransactionDetails,
)

enum class ChatbotAiTransactionType {
    PIX, SWEEPING, SCHEDULE_BILLS
}

fun ChatbotAiTransactionTypeTO.toChatbotAiTransactionType(): ChatbotAiTransactionType {
    return when (this) {
        ChatbotAiTransactionTypeTO.PIX -> ChatbotAiTransactionType.PIX
        ChatbotAiTransactionTypeTO.SWEEPING -> ChatbotAiTransactionType.SWEEPING
        ChatbotAiTransactionTypeTO.SCHEDULE_BILLS -> ChatbotAiTransactionType.SCHEDULE_BILLS
    }
}

enum class ChatbotAiTransactionStatus {
    ACTIVE,
    COMPLETED,
    EXPIRED,
    CANCELED,
}

fun ChatbotAiTransactionStatusTO.toChatbotAiTransactionStatus(): ChatbotAiTransactionStatus {
    return when (this) {
        ChatbotAiTransactionStatusTO.ACTIVE -> ChatbotAiTransactionStatus.ACTIVE
        ChatbotAiTransactionStatusTO.COMPLETED -> ChatbotAiTransactionStatus.COMPLETED
        ChatbotAiTransactionStatusTO.EXPIRED -> ChatbotAiTransactionStatus.EXPIRED
        ChatbotAiTransactionStatusTO.CANCELED -> ChatbotAiTransactionStatus.CANCELED
    }
}

data class TransactionDetails(
    val type: ChatbotAiTransactionType,
    val totalAmount: Long? = null,
    val sweepingAmount: Long? = null,
    val sweepingParticipantId: String?,
    val bills: List<BillView>? = null,
    val pixKey: String? = null,
    val name: String? = null,
    val document: String? = null,
    val institution: String? = null,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class AuthorizationDetails(
    val totalAmount: Long? = null,
    val sweepingAmount: Long? = null,
    val billIds: List<String>? = null,
    val pixKey: String? = null,
)

interface ChatbotAiTransactionService {
    fun findTransaction(transactionId: String, walletId: WalletId): Either<ChatbotAiTransactionError, ChatbotAiTransaction>
    fun findTransactions(transactionGroupId: String, walletId: WalletId, userId: ChatbotUserId): Either<ChatbotAiTransactionError, List<ChatbotAiTransaction>>
    fun confirmTransaction(transactionId: String, account: Account, details: AuthorizationDetails): Either<ChatbotAiTransactionError, TransactionResult>
    fun cancelTransaction(transactionId: String, userId: ChatbotUserId): Either<ChatbotAiTransactionError, TransactionResult>
    fun updatePaymentStatus(transactionId: String, userId: ChatbotUserId, paymentStatus: ChatbotAiTransactionPaymentStatus): Either<ChatbotAiTransactionError, TransactionResult>
    fun verifyAuthorization(authorizationToken: String?, billIds: List<BillId>): Boolean
    fun verifyAuthorization(authorizationToken: String?, pixKey: String, amount: Long): Boolean
}

enum class ChatbotAiTransactionPaymentStatus {
    UNKNOWN, SUCCESS, FAILED
}

@ConfigurationProperties(value = "chatbot-transaction-authorization")
data class ChatbotTransactionAuthorizationConfiguration @ConfigurationInject constructor(
    val secret: String,
)