package ai.friday.billpayment.modules.chatbotai.app

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.account.SystemActivityType
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateBillService
import ai.friday.billpayment.app.bill.CreatePixRequest
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.bill.checkExistsOneRecipient
import ai.friday.billpayment.app.bill.schedule.ScheduleBillService
import ai.friday.billpayment.app.chatbot.ChatbotNotificationService
import ai.friday.billpayment.app.forecast.ForecastService
import ai.friday.billpayment.app.forecast.WalletBalanceForecast
import ai.friday.billpayment.app.integrations.BillRecurrenceRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.EndToEnd
import ai.friday.billpayment.app.integrations.ExternalTransactionId
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.integrations.LimitType
import ai.friday.billpayment.app.integrations.OpenFinanceParticipantId
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.RequestSweepingCashInCommand
import ai.friday.billpayment.app.integrations.RequestSweepingCashInError
import ai.friday.billpayment.app.integrations.SweepingAccountServiceInterface
import ai.friday.billpayment.app.integrations.SweepingCashInId
import ai.friday.billpayment.app.integrations.SweepingCashInSource
import ai.friday.billpayment.app.integrations.SweepingRequest
import ai.friday.billpayment.app.itp.CreatePaymentIntentCommand
import ai.friday.billpayment.app.itp.ITPService
import ai.friday.billpayment.app.itp.ITPServiceError
import ai.friday.billpayment.app.itp.PaymentIntentId
import ai.friday.billpayment.app.onepixpay.OnePixPay
import ai.friday.billpayment.app.onepixpay.OnePixPayErrors
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.ScheduleStrategy
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.isValid
import ai.friday.billpayment.app.subscription.SubscriptionService
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.measureTimedValue
import ai.friday.billpayment.modules.chatbotai.ChatbotAI
import ai.friday.billpayment.modules.chatbotai.adapters.api.AvailableLimitType
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Primary
import java.time.LocalDate
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import parallelMap

@Primary
@ChatbotAI
open class ChatbotAIService(
    private val onePixPayService: OnePixPayService,
    private val findBillService: FindBillService,
    private val itpService: ITPService,
    private val accountService: AccountService,
    private val walletService: WalletService,
    private val forecastService: ForecastService,
    private val updateBillService: UpdateBillService,
    private val createBillService: CreateBillService,
    private val scheduleBillService: ScheduleBillService,
    private val subscriptionService: SubscriptionService,
    private val recurrenceRepository: BillRecurrenceRepository,
    private val walletLimitService: WalletLimitsService,
    private val sweepingAccountServiceInterface: SweepingAccountServiceInterface,
    private val chatbotAiTransactionService: ChatbotAiTransactionService,
    private val pixKeyManagement: PixKeyManagement,
    private val billRepository: BillRepository,
    private val systemActivityService: SystemActivityService,
    private val chatbotNotificationService: ChatbotNotificationService,
) {
    fun createOnePixPay(
        accountId: AccountId,
        walletId: WalletId?,
        bills: List<BillId>,
        useCurrentBalance: Boolean,
    ): Either<OnePixPayErrors, OnePixPay> {
        val account = accountService.findAccountById(accountId)
        val walletIdToUse = walletId ?: account.defaultWalletId()
        val billsToPay = bills.map {
            findBillService.find(walletIdToUse, it)
        }

        val subscriptionsToPayToday = findSubscriptionsToPayToday(accountId, walletIdToUse)

        val onePixPay = onePixPayService.create(billsToPay + subscriptionsToPayToday, useCurrentBalance).map {
            onePixPayService.save(it.asRequested())
        }.getOrElse {
            return it.left()
        }
        return onePixPay.right()
    }

    fun findSubscriptionsToPayToday(
        accountId: AccountId,
        walletIdToUse: WalletId,
    ): List<BillView> {
        return try {
            val subscription = subscriptionService.findOrNull(accountId) ?: return emptyList()

            if (walletIdToUse == subscription.walletId) {
                val recurrence = recurrenceRepository.find(subscription.recurrenceId, subscription.walletId)
                recurrence.bills.mapNotNull { billId ->
                    try {
                        findBillService.find(subscription.walletId, billId)
                    } catch (e: Exception) {
                        null
                    }
                }.filter { bill -> !bill.effectiveDueDate.isAfter(LocalDate.now()) }.filter {
                    it.status == BillStatus.ACTIVE
                }
            } else {
                emptyList()
            }
        } catch (exception: Exception) {
            logger.error(Markers.append("accountId", accountId.value).andAppend("walletId", walletIdToUse.value), "findSubscriptionsToPayToday", exception)
            emptyList()
        }
    }

    fun createOnePixPayITP(
        walletId: WalletId?,
        accountId: AccountId,
        bills: List<BillId>,
        authorizationServerId: String,
        useCurrentBalance: Boolean,
    ): Either<ITPServiceError, PaymentIntentId> {
        val account = accountService.findAccountById(accountId)
        val walletIdToUse = walletId ?: account.defaultWalletId()
        val billsToPay = bills.map {
            findBillService.find(walletIdToUse, it)
        }

        val subscriptionsToPayToday = findSubscriptionsToPayToday(accountId, walletIdToUse)

        return itpService.createPaymentIntentWithOnePixPay(
            CreatePaymentIntentCommand(
                accountId = accountId,
                walletId = walletIdToUse,
                authorizationServerId = authorizationServerId,
            ),
            billsToPay + subscriptionsToPayToday,
            useCurrentBalance,
        )
    }

    fun ignore(
        walletId: WalletId,
        accountId: AccountId,
        bills: List<BillId>,
    ): Either<Exception, Unit> {
        val wallet = walletService.findWallet(walletId)
        val member = wallet.getActiveMember(accountId)

        val billsToIgnore = unscheduleAndFilterNotSubscription(walletId, bills, member)

        val results = billsToIgnore.map { billId ->
            updateBillService.ignoreBill(
                billId = billId,
                walletId = walletId,
                member = member,
                actionSource = ActionSource.Api(accountId),
            )
        }
        results.firstOrNull { it.isLeft() }?.mapLeft { return it.left() }
        return Unit.right()
    }

    fun markAsPaid(
        walletId: WalletId,
        accountId: AccountId,
        bills: List<BillId>,
    ): Either<Exception, Unit> {
        val wallet = walletService.findWallet(walletId)
        val member = wallet.getActiveMember(accountId)

        val billsToMark = unscheduleAndFilterNotSubscription(walletId, bills, member)

        val results = billsToMark.map { billId ->
            updateBillService.markAsPaid(
                billId = billId,
                walletId = walletId,
                member = member,
                amountPaid = null,
                actionSource = ActionSource.Api(
                    accountId = accountId,
                    originalBillId = null,
                ),
            )
        }

        results.firstOrNull { it.isLeft() }?.mapLeft { return it.left() }
        return Unit.right()
    }

    fun calculateWalletBalanceForecast(walletId: WalletId): Either<Exception, WalletBalanceForecast> {
        return try {
            val wallet = walletService.findWallet(walletId)

            val walletBalanceForecast = forecastService.calculateWalletBalanceForecast(wallet)

            walletBalanceForecast.right()
        } catch (exception: Exception) {
            exception.left()
        }
    }

    fun findPendingAndWaitingApprovalBillsByWallet(accountId: AccountId, startDate: LocalDate, endDate: LocalDate): List<WalletWithPendingBills> {
        val wallets = walletService.findAllFounderWallets(accountId)
        // TODO: hoje estamos retornando apenas a carteira primaria do usuário pois o Fred não sabe lidar com mais de uma carteira.
        // Esse código tem que mudar quando for implementado o comportamento de lidar com multiplas carteiras.
        return wallets.filter { it.checkPrimary(accountId) }.map { wallet ->
            val bills = findBillService.findActiveAndWaitingApprovalBills(walletId = wallet.id).filterByRange(startDate, endDate)

            val billIds = bills.map { it.billId.value }
            val otherBillsWaitingFunds = findBillService.findBillsWaitingFunds(walletId = wallet.id).filter { it.billId.value !in billIds }

            WalletWithPendingBills(
                walletId = wallet.id,
                bills = otherBillsWaitingFunds + bills,
                walletName = wallet.name,
                walletFounder = wallet.founder,
            )
        }
    }

    fun scheduleBills(
        accountId: AccountId,
        walletId: WalletId,
        bills: List<BillId>,
        sweepingRequest: SweepingRequest?,
        authorizationToken: String? = null,
        toDueDate: Boolean = false,
    ): ChatBotScheduleBillResult {
        val logName = "ChatbotAIService#scheduleBills"
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("walletId", walletId.value)
            .andAppend("bills", bills.map { it.value })
            .andAppend("sweepingRequest", sweepingRequest)
            .andAppend("authorizationToken", authorizationToken)

        val timeElapsedMap = mutableMapOf<String, Long>()

        val wallet = walletService.findWallet(walletId)

        val billViews = measureTimedValue(timeElapsedMap, "findBills") {
            bills.map {
                try {
                    val bill = findBillService.find(walletId = walletId, billId = it)

                    if (bill.status != BillStatus.ACTIVE) {
                        return ChatBotScheduleBillResult.BillNotActive("Bill not active")
                    }

                    bill
                } catch (e: Exception) {
                    return ChatBotScheduleBillResult.BillNotFound("Bill not found")
                }
            }
        }

        if (!toDueDate) {
            val assistantPaymentLimitAvailable = measureTimedValue(timeElapsedMap, "getAssistantLimit") {
                getAssistantPaymentLimitAvailable(walletId = wallet.id, accountId = accountId)
            }

            if (billViews.sumOf { it.amountTotal } > assistantPaymentLimitAvailable) {
                val transactionAuthorized = measureTimedValue(timeElapsedMap, "checkAuth") {
                    chatbotAiTransactionService.verifyAuthorization(authorizationToken, bills)
                }

                if (!transactionAuthorized) {
                    logger.info(markers.andAppend("timeElapsed", timeElapsedMap), logName)
                    return ChatBotScheduleBillResult.AssistantLimitExceeded("Insufficient assistant payment limit")
                }
            }

            val endToEnd = measureTimedValue(timeElapsedMap, "sweepingTransfer") {
                if (sweepingRequest != null) {
                    handleChatbotSweepingTransfer(walletId, sweepingRequest, authorizationToken).getOrElse {
                        markers.andAppend("timeElapsed", timeElapsedMap)
                            .andAppend("error", it)

                        logger.error(markers, "scheduleBills")
                        return it
                    }
                } else {
                    null
                }
            }
            markers.andAppend("endToEnd", endToEnd)

            val account = accountService.findAccountById(accountId)
            markers.andAppend("accountGroups", account.configuration.groups)

            if (endToEnd != null) {
                OnePixPay.create(billViews, endToEnd).map {
                    onePixPayService.save(it.asRequested())
                }.getOrElse {
                    markers.andAppend("error", it)
                    logger.warn(markers, logName)
                    return ChatBotScheduleBillResult.BillNotScheduled(it.toString())
                }

                return ChatBotScheduleBillResult.BillsScheduled(bills.map { it.value }, true)
            }
        }
        val scheduleStrategy = if (toDueDate) {
            ScheduleStrategy.ofDueDate()
        } else {
            ScheduleStrategy.ofAsap()
        }
        measureTimedValue(timeElapsedMap, "schedulePayments") {
            scheduleBillService.schedulePayments(
                paymentWallet = wallet,
                billIdsWithMethods = billViews.filter { it.billType != BillType.AUTOMATIC_PIX }.map {
                    Pair(
                        it.billId,
                        PaymentMethodsDetailWithBalance(
                            paymentMethodId = wallet.paymentMethodId,
                            amount = it.amountTotal,
                            calculationId = null,
                        ),
                    )
                },
                accountId = accountId,
                actionSource = ActionSource.VirtualAssistant(accountId),
                scheduleStrategy = scheduleStrategy,
            )

            scheduleBillService.processScheduleAsync(walletId)
        }

        logger.info(markers.andAppend("timeElapsed", timeElapsedMap), logName)
        return ChatBotScheduleBillResult.BillsScheduled(bills.map { it.value }, true)
    }

    fun validatePix(
        accountId: AccountId,
        walletId: WalletId,
        pixKey: PixKey,
        amount: Long,
    ): ChatBotScheduleBillResult {
        val logName = "ChatbotAIService#validatePix"
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("walletId", walletId.value)
            .andAppend("pixKey", pixKey)
            .andAppend("amount", amount)

        val account = try {
            accountService.findAccountById(accountId)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return ChatBotScheduleBillResult.Failure(e)
        }
        val result = pixKeyManagement.findKeyDetails(pixKey, account.document).getOrElse { pixKeyError ->
            logger.error(markers.andAppend("pixKeyError", pixKeyError), logName)

            return when (pixKeyError) {
                is PixKeyError.MalformedKey -> ChatBotScheduleBillResult.PixKeyInvalid("Pix key invalid")
                is PixKeyError.KeyNotFound, is PixKeyError.KeyNotConfirmed -> ChatBotScheduleBillResult.PixKeyNotFound("Pix key not found")
                is PixKeyError.UnknownError, PixKeyError.SystemUnavailable, PixKeyError.InvalidQrCode -> ChatBotScheduleBillResult.UnableToValidatePix("Unable to validate pix key")
            }
        }.pixKeyDetails.toPixKeyDetailsTO()

        logger.info(markers, logName)
        return ChatBotScheduleBillResult.PixKeyValid(result)
    }

    fun schedulePix(
        accountId: AccountId,
        wallet: Wallet,
        member: Member,
        createPixRequest: CreatePixRequest,
        authorizationToken: String? = null,
        transactionId: String,
        retryTransaction: Boolean,
        sweepingRequest: SweepingRequest? = null,
    ): ChatBotScheduleBillResult {
        val logName = "ChatbotAIService#schedulePix"
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("walletId", wallet.id.value)
            .andAppend("createPixRequest", createPixRequest)
            .andAppend("authorizationToken", authorizationToken)
            .andAppend("transactionId", transactionId)
            .andAppend("sweepingRequest", sweepingRequest)
            .andAppend("retryTransaction", retryTransaction)

        val recipientExists = createPixRequest.recipient.checkExistsOneRecipient()
        markers.andAppend("recipientExists", recipientExists)

        if (!recipientExists) {
            logger.info(markers, logName)
            return ChatBotScheduleBillResult.RecipientNotFound("Recipient not found")
        }

        val pixToRetry = checkPixAlreadyCreated(createPixRequest, transactionId, retryTransaction).getOrElse {
            markers.andAppend("pixAlreadyCreated", true)
            logger.info(markers, logName)
            return ChatBotScheduleBillResult.BillAlreadyExists(it)
        }
        markers.andAppend("pixToRetry", pixToRetry?.billId?.value)

        val validPixKey = createPixRequest.recipient.pixKey?.takeIf { it.isValid() }?.value ?: createPixRequest.recipient.qrCode
        markers.andAppend("validPixKey", validPixKey)

        if (validPixKey == null) {
            logger.info(markers, logName)
            return ChatBotScheduleBillResult.PixKeyInvalid("Pix key invalid")
        }

        val availableLimitResult = validateAvailableLimit(wallet.id, accountId, createPixRequest.amount, AvailableLimitType.PIX)
        markers.andAppend("availableLimitResult", availableLimitResult)

        when (availableLimitResult) {
            is AvailableLimitResult.Success -> {
            }

            is AvailableLimitResult.AssistantLimitExceeded -> {
                val transactionAuthorized = chatbotAiTransactionService.verifyAuthorization(authorizationToken, validPixKey, createPixRequest.amount)
                markers.andAppend("transactionAuthorized", transactionAuthorized)

                if (!transactionAuthorized) {
                    logger.warn(markers, logName)
                    return ChatBotScheduleBillResult.AssistantLimitExceeded("Insufficient assistant payment limit")
                }
            }

            is AvailableLimitResult.PixLimitExceeded -> {
                logger.info(markers, logName)
                return ChatBotScheduleBillResult.PixLimitExceeded("Insufficient pix payment limit")
            }
        }

        val endToEnd = if (sweepingRequest != null) {
            handleChatbotSweepingTransfer(wallet.id, sweepingRequest, authorizationToken).getOrElse {
                markers.andAppend("error", it)

                logger.error(markers, logName)
                return it
            }
        } else {
            null
        }
        markers.andAppend("endToEnd", endToEnd)

        val pixToSchedule = if (pixToRetry != null) {
            pixToRetry
        } else {
            val pixResult = createBillService.createPix(createPixRequest, false)
            markers.andAppend("pixResult", pixResult)

            if (pixResult !is CreateBillResult.SUCCESS) {
                logger.info(markers, logName)
                return when (pixResult) {
                    is CreateBillResult.FAILURE.ServerError -> ChatBotScheduleBillResult.Failure(pixResult.throwable)
                    is CreateBillResult.FAILURE.BillUnableToValidate -> ChatBotScheduleBillResult.UnableToValidatePix(pixResult.description)
                    else -> ChatBotScheduleBillResult.Failure(Exception("Unknown error"))
                }
            }

            billRepository.findBill(pixResult.bill.billId, pixResult.bill.walletId)
        }
        markers.andAppend("pixToSchedule", pixToSchedule.billId.value)

        val account = accountService.findAccountById(accountId)
        markers.andAppend("accountGroups", account.configuration.groups)

        val scheduled = if (endToEnd != null) {
            OnePixPay.create(listOf(pixToSchedule), endToEnd).map {
                onePixPayService.save(it.asRequested())
            }.getOrElse {
                markers.andAppend("error", it)
                logger.warn(markers, logName)
                return ChatBotScheduleBillResult.BillNotScheduled(it.toString())
            }

            false
        } else {
            val paymentMethodsDetail = PaymentMethodsDetailWithBalance(
                paymentMethodId = wallet.paymentMethodId,
                amount = createPixRequest.amount,
                calculationId = null,
            )
            markers.andAppend("paymentMethodsDetail", paymentMethodsDetail)

            val result = scheduleBillService.schedulePayment(
                paymentWallet = wallet,
                billId = pixToSchedule.billId,
                paymentMethodsDetail = paymentMethodsDetail,
                member = member,
                actionSource = createPixRequest.source,
                scheduleStrategy = ScheduleStrategy.ofAsap(),
            ).getOrElse {
                logger.error(markers, logName, it)
                return ChatBotScheduleBillResult.BillNotScheduled(it.message ?: "Unknown error")
            }
            markers.andAppend("result", result)

            scheduleBillService.processScheduleAsync(wallet.id)

            result.isPaymentScheduled()
        }
        markers.andAppend("scheduled", scheduled)

        logger.info(markers, logName)
        return ChatBotScheduleBillResult.PixScheduled(pixToSchedule.billId.value, scheduled)
    }

    fun getPixPaymentLimitAvailable(walletId: WalletId): Long {
        return walletLimitService.getAvailableLimit(walletId).coerceAtLeast(0)
    }

    fun getAssistantPaymentLimitAvailable(walletId: WalletId, accountId: AccountId): Long {
        return walletLimitService.getAvailableAssistantLimit(walletId, accountId).coerceAtLeast(0)
    }

    private fun RequestSweepingCashInError.toSweepingTransferErrorReason() = when (this) {
        is RequestSweepingCashInError.Conflict,
        is RequestSweepingCashInError.GenericError,
        is RequestSweepingCashInError.ItemNotFound,
        is RequestSweepingCashInError.PaymentNotCreated,
        -> SweepingTransferErrorReason.GenericReason("Erro na transferência inteligente")

        is RequestSweepingCashInError.LimitError -> SweepingTransferErrorReason.LimitExceeded(limitType)

        is RequestSweepingCashInError.ActiveConsentNotFound -> SweepingTransferErrorReason.GenericReason("Usuário não possui consentimento ativo")
    }

    private fun handleChatbotSweepingTransfer(walletId: WalletId, sweepingRequest: SweepingRequest, authorizationToken: String?) = handleSweepingTransfer(
        walletId = walletId,
        amount = sweepingRequest.amount,
        externalTransactionId = sweepingRequest.transactionId,
        requestSource = SweepingCashInSource.CHATBOT,
        approvalSource = if (authorizationToken == null) {
            SweepingCashInSource.CHATBOT
        } else {
            SweepingCashInSource.WEB_APP
        },
        description = "Transferência Chatbot",
        participantId = sweepingRequest.participantId,
        retry = sweepingRequest.retry,
    ).mapLeft {
        ChatBotScheduleBillResult.SweepingTransferError(it)
    }

    private fun handleSweepingTransfer(walletId: WalletId, amount: Long, externalTransactionId: ExternalTransactionId?, requestSource: SweepingCashInSource, approvalSource: SweepingCashInSource, description: String, participantId: OpenFinanceParticipantId?, retry: Boolean): Either<SweepingTransferErrorReason, EndToEnd?> {
        val markers = Markers.append("walletId", walletId.value)
            .andAppend("amount", amount)
            .andAppend("externalTransactionId", externalTransactionId)
            .andAppend("requestSource", requestSource)
            .andAppend("approvalSource", approvalSource)
            .andAppend("description", description)
            .andAppend("retry", retry)

        return try {
            val requestId = if (retry || externalTransactionId == null) {
                SweepingCashInId()
            } else {
                SweepingCashInId(externalTransactionId.value.removePrefix("TRANSACTION-"))
            }

            val command = RequestSweepingCashInCommand(
                requestSource = requestSource,
                approvalSource = approvalSource,
                externalTransactionId = externalTransactionId,
                sweepingCashInId = requestId,
                walletId = walletId,
                amount = amount,
                description = description,
                consentId = null,
                participantId = participantId,
            )
            markers.andAppend("command", command)

            sweepingAccountServiceInterface.requestSweepingCashIn(
                command,
            ).mapLeft {
                markers.andAppend("requestError", it)

                val errorReason = it.toSweepingTransferErrorReason()
                markers.andAppend("requestErrorReason", errorReason)

                errorReason
            }
        } catch (e: Exception) {
            logger.error(markers, "handleSweepingTransfer", e)
            SweepingTransferErrorReason.GenericReason("Erro na transferência inteligente").left()
        }.also {
            it.onLeft {
                logger.error(markers.andAppend("error", it), "handleSweepingTransfer")
            }.onRight {
                logger.info(markers, "handleSweepingTransfer")
            }
        }
    }

    private fun checkPixAlreadyCreated(createPixRequest: CreatePixRequest, transactionId: String, retryTransaction: Boolean): Either<BillId, BillView?> {
        val duplicatePix = billRepository.findByWalletAndEffectiveDueDate(
            walletId = createPixRequest.walletId,
            dueDate = createPixRequest.dueDate,
        ).firstOrNull {
            it.source is ActionSource.VirtualAssistant && (it.source as ActionSource.VirtualAssistant).transactionId == transactionId
        }

        return if (duplicatePix != null && !retryTransaction) {
            duplicatePix.billId.left()
        } else {
            duplicatePix.right()
        }
    }

    fun validateAvailableLimit(walletId: WalletId, accountId: AccountId, amount: Long, type: AvailableLimitType): AvailableLimitResult {
        return when (type) {
            AvailableLimitType.PIX -> {
                val pixLimitAvailable = getPixPaymentLimitAvailable(walletId = walletId)
                if (amount > pixLimitAvailable) {
                    return AvailableLimitResult.PixLimitExceeded
                }

                val assistantPaymentLimitAvailable = getAssistantPaymentLimitAvailable(walletId = walletId, accountId = accountId)
                if (amount > assistantPaymentLimitAvailable) {
                    return AvailableLimitResult.AssistantLimitExceeded
                }

                AvailableLimitResult.Success
            }

            AvailableLimitType.SCHEDULE_BILLS -> {
                val assistantPaymentLimitAvailable = getAssistantPaymentLimitAvailable(walletId = walletId, accountId = accountId)
                if (amount > assistantPaymentLimitAvailable) {
                    return AvailableLimitResult.AssistantLimitExceeded
                }

                AvailableLimitResult.Success
            }
        }
    }

    fun getAssistantPaymentLimit(walletId: WalletId, accountId: AccountId): AssistantPaymentLimit {
        return AssistantPaymentLimit(
            amount = minOf(getAssistantPaymentLimitAvailable(walletId, accountId), getPixPaymentLimitAvailable(walletId)),
            enabled = walletLimitService.getWhatsAppPaymentLimit(walletId).activeAmount > 0,
        )
    }

    fun unscheduleAndFilterNotSubscription(
        walletId: WalletId,
        billIds: List<BillId>,
        member: Member,
    ): List<BillId> {
        val scheduledBills = billIds.map { findBillService.find(walletId = walletId, billId = it) }.filter { it.isPaymentScheduled() }

        scheduledBills.forEach {
            if (!it.subscriptionFee) {
                updateBillService.userCancelScheduledPayment(
                    walletId = walletId,
                    billId = it.billId,
                    member = member,
                    actionSource = ActionSource.Api(member.accountId),
                )
            }
        }
        val subscriptions = scheduledBills.filter { it.subscriptionFee }
        return billIds - subscriptions.map { it.billId }.toSet()
    }

    fun List<BillView>.filterByRange(startDate: LocalDate, endDate: LocalDate): List<BillView> {
        return this.filter {
            !it.isTriPix() &&
                (it.expectedPaymentDate.isAfter(startDate) || it.expectedPaymentDate == startDate) && (it.expectedPaymentDate.isBefore(endDate) || it.expectedPaymentDate == endDate)
        }
    }

    fun getSystemActivities(accountId: AccountId, systemActivities: List<String>): List<ChatbotSystemActivity> {
        val logName = "ChatbotAIService#getSystemActivityFlag"
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("systemActivities", systemActivities)
        val result = runBlocking {
            systemActivities.parallelMap { systemActivity ->
                try {
                    val systemActivityType = SystemActivityType.valueOf(systemActivity)
                    val value = systemActivityService.getSystemActivityFlag(accountId, systemActivityType)
                    ChatbotSystemActivity(systemActivityType, value)
                } catch (ex: Exception) {
                    logger.error(markers, logName, ex)
                    null
                }
            }.filterNotNull()
        }
        logger.info(markers.andAppend("result", result), logName)
        return result
    }

    fun setSystemActivityFlag(accountId: AccountId, systemActivity: String, value: Boolean): Either<SystemActivityErrorReason, Unit> {
        return try {
            val systemActivityType = SystemActivityType.valueOf(systemActivity)
            systemActivityService.setSystemActivityFlag(
                accountId = accountId,
                activityType = systemActivityType,
                value = value,
            ).right()
        } catch (ex: IllegalArgumentException) {
            SystemActivityErrorReason.NotImplemented.left()
        } catch (ex: Exception) {
            SystemActivityErrorReason.Failure(ex).left()
        }
    }

    fun retrySweepingTransfer(walletId: WalletId, amount: Long, participantId: OpenFinanceParticipantId?) = handleSweepingTransfer(
        walletId = walletId,
        amount = amount,
        externalTransactionId = null,
        requestSource = SweepingCashInSource.WEB_APP,
        approvalSource = SweepingCashInSource.WEB_APP,
        description = "Transferência via WebApp - retentativa",
        participantId = participantId,
        retry = true,
    )

    fun findBills(walletId: WalletId, billIds: List<BillId>): Either<FindBillError, List<BillView>> {
        val logName = "ChatbotAIService#findBill"
        val markers = Markers.append("walletId", walletId.value)
            .andAppend("billId", billIds)

        val bills = try {
            billIds.map { billId ->
                findBillService.find(walletId, billId)
            }
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return FindBillError.BillNotFound.left()
        }

        if (bills.any { it.status != BillStatus.ACTIVE }) {
            logger.error(markers, logName, Exception("Bill not active"))
            return FindBillError.BillNotActive.left()
        } else {
            logger.info(markers, logName)
            return bills.right()
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ChatbotAIService::class.java)
    }
}

data class ChatbotSystemActivity(
    val type: SystemActivityType,
    val value: Boolean,
)

sealed class SystemActivityErrorReason : PrintableSealedClassV2() {
    data object NotImplemented : SystemActivityErrorReason()
    data class Failure(val error: Throwable) : SystemActivityErrorReason()
}

sealed class SweepingTransferErrorReason : PrintableSealedClassV2() {
    data class GenericReason(val error: String) : SweepingTransferErrorReason()
    data class LimitExceeded(val limitType: LimitType) : SweepingTransferErrorReason()
}

sealed class ChatBotScheduleBillResult : PrintableSealedClassV2() {
    data class WalletNotFound(val walletId: String?) : ChatBotScheduleBillResult()
    data class Failure(val error: Throwable) : ChatBotScheduleBillResult()
    data class UnableToValidatePix(val error: String) : ChatBotScheduleBillResult()
    data class RecipientNotFound(val error: String) : ChatBotScheduleBillResult()
    data class PixKeyValid(val pixKeyDetailsTO: PixKeyDetailsTO) : ChatBotScheduleBillResult()
    data class PixKeyInvalid(val error: String) : ChatBotScheduleBillResult()
    data class PixKeyNotFound(val error: String) : ChatBotScheduleBillResult()
    data class PixScheduled(val billId: String, val scheduled: Boolean) : ChatBotScheduleBillResult()
    data class BillScheduled(val billId: String, val scheduled: Boolean) : ChatBotScheduleBillResult()
    data class BillsScheduled(val bill: List<String>, val scheduled: Boolean) : ChatBotScheduleBillResult()
    data class BillNotScheduled(val error: String) : ChatBotScheduleBillResult()
    data class BillNotActive(val error: String) : ChatBotScheduleBillResult()
    data class BillNotFound(val error: String) : ChatBotScheduleBillResult()
    data class SweepingTransferError(val reason: SweepingTransferErrorReason) : ChatBotScheduleBillResult()
    data class AssistantLimitExceeded(val error: String) : ChatBotScheduleBillResult()
    data class PixLimitExceeded(val error: String) : ChatBotScheduleBillResult()
    data class BillAlreadyExists(val billId: BillId) : ChatBotScheduleBillResult()
}

sealed class AvailableLimitResult : PrintableSealedClassV2() {
    data object Success : AvailableLimitResult()
    data object AssistantLimitExceeded : AvailableLimitResult()
    data object PixLimitExceeded : AvailableLimitResult()
}

data class WalletWithPendingBills(
    val walletId: WalletId,
    val bills: List<BillView>,
    val walletName: String,
    val walletFounder: Member,
)

data class AssistantPaymentLimit(
    val amount: Long,
    val enabled: Boolean,
)

data class PixKeyDetailsTO(
    val keyType: PixKeyType,
    val keyValue: String,
    val recipientInstitution: String,
    val recipientDocument: String,
    val recipientName: String,
)

fun PixKeyDetails.toPixKeyDetailsTO() =
    PixKeyDetailsTO(
        keyType = key.type,
        keyValue = key.value,
        recipientInstitution = holder.institutionName,
        recipientDocument = owner.document,
        recipientName = owner.name,
    )

sealed class FindBillError : PrintableSealedClassV2() {
    data object BillNotFound : FindBillError()
    data object BillNotActive : FindBillError()
}