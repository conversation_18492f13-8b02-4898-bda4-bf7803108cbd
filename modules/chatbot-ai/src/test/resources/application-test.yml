micronaut:
  metrics:
    enabled: true
  caches:
    wallet:
      charset: 'UTF-8'
      expire-after-write: 0s
      maximum-size: 0
  otel:
    enabled: false
  server:
    ssl:
      enabled: false
    netty:
      listeners:
        httpsListener:
          port: -1
          ssl: false
    port: -1
    max-request-size: 6291456 #1024L*1024*6=>6MB
    multipart:
      enabled: true
      disk: false
      mixed: false
      max-file-size: 6291456 #6291456
  application:
    name: bill-payment-service
  security:
    enabled: true
    endpoints:
      login:
        enabled: true
    token:
      cookie:
        enabled: true
      bearer:
        enabled: false
      jwt:
        enabled: true
        cookie:
          secure: true
        signatures:
          jwks:
            aggregator:
              url: 'https://www.googleapis.com/oauth2/v3/certs'
          secret:
            generator:
              secret: PleaseChangeThisSecretForANewOne
  http:
    client:
      read-timeout: 15s

chatbotAI-auth:
  secret: CHATBOT_AI_SECRET

jwtValidation:
  providers:
    msisdn:
      audience:
        - account
      issuer: https://chatbot.ai

modules:
  pfm:
    enabled: true
  manual-entry:
    enabled: true

subscription:
  amount: 990
  dayOfMonth: 10
  description: "%s"
  recipientName: Assinatura
  recipientDocument: 1234
  bankAccount:
    accountType: CHECKING
    bankNo: 213
    routingNo: 1
    accountNo: 1234
    accountDv: 2
    ispb: ********

blip-callback:
  allowed-ips: [ ]