package ai.friday.billpayment.modules.chatbotai.adapters.chatbot

import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentStatus
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.LegacyAccountConfiguration
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.message.QueueMessage
import ai.friday.billpayment.app.payment.checkout.RequestProtocol
import io.micronaut.http.HttpResponse
import io.micronaut.http.MutableHttpRequest
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class ChatbotAdapterTest {

    private val messagePublisher = mockk<MessagePublisher>()
    private val chatBotHttpClient = mockk<ChatBotHttpClient>()

    private val account = mockk<Account> {
        every { accountId } returns AccountId("account-id")
        every { mobilePhone } returns "*************"
        every { name } returns "Test User"
        every { document } returns "***********"
        every { documentType } returns "CPF"
        every { status } returns AccountStatus.ACTIVE
        every { paymentStatus } returns AccountPaymentStatus.UpToDate
        every { configuration } returns LegacyAccountConfiguration(
            accountId = null,
            creditCardConfiguration = mockk(),
            defaultWalletId = null,
            receiveDDANotification = false,
            receiveNotification = true,
            accessToken = null,
            refreshToken = null,
            externalId = null,
            groups = emptyList(),
        )
    }

    @Nested
    @DisplayName("ao enviar uma mensagem")
    inner class SendMessage {

        @Test
        fun `quando o sendMessageProtocol é QUEUE deve chamar o messagePublisher`() {
            val chatbotConfiguration = ChatbotConfiguration(
                clientid = "client-id",
                secret = "secret",
                sendMessageProtocol = RequestProtocol.QUEUE,
            )

            val publisher = ChatbotAdapter(
                messagePublisher = messagePublisher,
                chatBotNotificationGateway = "chatBotNotificationGateway",
                chatBotWebhooksQueue = "chatBotWebhooksQueue",
                chatBotStateUpdate = "chatBotStateUpdate",
                chatBotHttpClient = chatBotHttpClient,
                chatbotConfiguration = chatbotConfiguration,
                billTOBuilder = mockk(),
                whatsappNotificationBuilder = mockk(),
            )

            every { messagePublisher.sendMessage(any<QueueMessage>()) } just runs

            publisher.publishStateUpdate(account, AccountPaymentStatus.UpToDate, AccountStatus.ACTIVE)

            verify(exactly = 1) {
                messagePublisher.sendMessage(any<QueueMessage>())
            }
            verify(exactly = 0) {
                chatBotHttpClient.send(any(), any())
            }
        }

        @Test
        fun `quando o sendMessageProtocol é HTTP deve chamar o chatBotHttpClient com tenant correto`() {
            val chatbotConfiguration = ChatbotConfiguration(
                clientid = "client-id",
                secret = "secret",
                sendMessageProtocol = RequestProtocol.HTTP,
            )

            val publisher = ChatbotAdapter(
                messagePublisher = messagePublisher,
                chatBotNotificationGateway = "chatBotNotificationGateway",
                chatBotWebhooksQueue = "chatBotWebhooksQueue",
                chatBotStateUpdate = "chatBotStateUpdate",
                chatBotHttpClient = chatBotHttpClient,
                chatbotConfiguration = chatbotConfiguration,
                billTOBuilder = mockk(),
                whatsappNotificationBuilder = mockk(),
            )

            every { chatBotHttpClient.send(any(), any()) } returns HttpResponse.ok()

            publisher.publishStateUpdate(account, AccountPaymentStatus.UpToDate, AccountStatus.ACTIVE)

            verify(exactly = 0) {
                messagePublisher.sendMessage(any<QueueMessage>())
            }
            verify(exactly = 1) {
                chatBotHttpClient.send(any(), any())
            }
        }
    }

    @Nested
    @DisplayName("BasicAuthClientFilter")
    inner class BasicAuthClientFilterTest {

        @Test
        fun `deve adicionar header X-TENANT-ID e basic auth nas requisições`() {
            val chatbotConfiguration = ChatbotConfiguration(
                clientid = "test-client-id",
                secret = "test-secret",
                sendMessageProtocol = RequestProtocol.HTTP,
            )
            val tenantId = "test-tenant-id"

            val filter = BasicAuthClientFilter(chatbotConfiguration, tenantId)

            val request = mockk<MutableHttpRequest<*>>(relaxed = true)

            filter.doFilter(request)

            verify(exactly = 1) {
                request.basicAuth("test-client-id", "test-secret")
            }
            verify(exactly = 1) {
                request.header("X-TENANT-ID", "test-tenant-id")
            }
        }
    }
}