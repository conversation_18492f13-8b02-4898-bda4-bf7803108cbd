package ai.friday.billpayment.modules.chatbotai.app

import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.integrations.ChatbotUserId
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.DailyPaymentLimit
import ai.friday.billpayment.app.wallet.DailyPaymentLimitType
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.BILL_ID
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.BILL_ID_3
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.chatbotai.adapters.ChatbotAiTransactionAdapter
import ai.friday.billpayment.modules.chatbotai.adapters.ChatbotAiTransactionError
import ai.friday.billpayment.modules.chatbotai.adapters.TransactionResult
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Test

class ChatbotAiTransactionServiceTest {

    private val billIds = listOf(BillId(BILL_ID), BillId(BILL_ID_2), BillId(BILL_ID_3))

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()
    private val walletId = wallet.id
    private val account = walletFixture.founderAccount

    private val transactionDetailsWithBillIds = AuthorizationDetails(billIds = billIds.map { it.value })

    private val transactionDetailsWithPixKey = AuthorizationDetails(totalAmount = 100L, pixKey = "pixkey")

    private val chatbotAiTransactionAdapter = mockk<ChatbotAiTransactionAdapter>(relaxed = true)

    private val systemActivityService = mockk<SystemActivityService>()

    private val walletLimitService = mockk<WalletLimitsService>()

    private val findBillService = mockk<FindBillService>(relaxed = true) {
        every {
            find(walletId, any())
        } answers {
            getActiveBill(billId = secondArg())
        }
    }

    private val authorizationConfiguration = ChatbotTransactionAuthorizationConfiguration("YcVHOBsHSbK5CJRF7n2v0U0FXh5X0zoMcCEGW2rvsgecEQCkptExaudfVnSQz/lL")

    private val chatbotAiTransactionService = DefaultChatbotAiTransactionService(
        chatbotAiTransactionAdapter = chatbotAiTransactionAdapter,
        findBillService = findBillService,
        chatbotTransactionAuthorizationConfiguration = authorizationConfiguration,
        systemActivityService = systemActivityService,
        walletLimitsService = walletLimitService,
    )

    @Test
    fun `deve confirmar a transação`() {
        every {
            chatbotAiTransactionAdapter.confirmTransaction("123", ChatbotUserId("*************"), any(), any(), any())
        } returns TransactionResult.Success.right()

        every { walletLimitService.findWalletLimitOrNull(any(), any()) } returns null

        every { systemActivityService.incrementChatbotTransactionAuthorized(any()) } returns 1

        val result = chatbotAiTransactionService.confirmTransaction("123", account, transactionDetailsWithBillIds)

        // Verificar que o token de autorização gerado pode ser validado
        val tokenSlot = slot<String>()
        verify { chatbotAiTransactionAdapter.confirmTransaction("123", ChatbotUserId("*************"), capture(tokenSlot), false, 1) }
        chatbotAiTransactionService.verifyAuthorization(tokenSlot.captured, billIds) shouldBe true

        result.isRight() shouldBe true
    }

    @Test
    fun `deve confirmar a transação de pix`() {
        every {
            chatbotAiTransactionAdapter.confirmTransaction("123", ChatbotUserId("*************"), any(), any(), any())
        } returns TransactionResult.Success.right()

        every { walletLimitService.findWalletLimitOrNull(any(), any()) } returns null

        every { systemActivityService.incrementChatbotTransactionAuthorized(any()) } returns 1

        val result = chatbotAiTransactionService.confirmTransaction("123", account, transactionDetailsWithPixKey)

        // Verificar que o token de autorização gerado pode ser validado
        val tokenSlot = slot<String>()
        verify { chatbotAiTransactionAdapter.confirmTransaction("123", ChatbotUserId("*************"), capture(tokenSlot), false, 1) }
        chatbotAiTransactionService.verifyAuthorization(tokenSlot.captured, "pixkey", 100L) shouldBe true

        result.isRight() shouldBe true
    }

    @Test
    fun `deve passar informações de limite ao confirmar`() {
        every {
            chatbotAiTransactionAdapter.confirmTransaction("123", ChatbotUserId("*************"), any(), any(), any())
        } returns TransactionResult.Success.right()

        every { walletLimitService.findWalletLimitOrNull(any(), any()) } returns DailyPaymentLimit(getZonedDateTime(), 100_00, 50_00, DailyPaymentLimitType.WHATSAPP_PAYMENT)

        every { systemActivityService.incrementChatbotTransactionAuthorized(any()) } returns 5

        val result = chatbotAiTransactionService.confirmTransaction("123", account, transactionDetailsWithBillIds)

        verify { chatbotAiTransactionAdapter.confirmTransaction("123", ChatbotUserId("*************"), any(), true, 5) }

        result.isRight() shouldBe true
    }

    @Test
    fun `não deve confirmar a transação se ela não existir`() {
        every {
            chatbotAiTransactionAdapter.confirmTransaction("123", ChatbotUserId("*************"), any(), any(), any())
        } returns ChatbotAiTransactionError.TransactionNotFound.left()

        every { walletLimitService.findWalletLimitOrNull(any(), any()) } returns null

        every { systemActivityService.incrementChatbotTransactionAuthorized(any()) } returns 1

        val result = chatbotAiTransactionService.confirmTransaction("123", account, transactionDetailsWithBillIds)

        result.isLeft() shouldBe true
        result.onLeft { it shouldBe ChatbotAiTransactionError.TransactionNotFound }
    }

    @Test
    fun `não deve confirmar a transação se ela não estiver ativa`() {
        every {
            chatbotAiTransactionAdapter.confirmTransaction("123", ChatbotUserId("*************"), any(), any(), any())
        } returns ChatbotAiTransactionError.IllegalTransactionState.left()

        every { walletLimitService.findWalletLimitOrNull(any(), any()) } returns null

        every { systemActivityService.incrementChatbotTransactionAuthorized(any()) } returns 1

        val result = chatbotAiTransactionService.confirmTransaction("123", account, transactionDetailsWithBillIds)

        result.isLeft() shouldBe true
        result.onLeft { it shouldBe ChatbotAiTransactionError.IllegalTransactionState }
    }

    @Test
    fun `deve validar token de autorização correto`() {
        // Token gerado com: listOf(BillId(BILL_ID), BillId(BILL_ID_2), BillId(BILL_ID_3))
        val authorizationToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1D4CGFO-UMwioJwZOJqDGP-2XTD4v2eucXXeq60VWqk"

        val anotherBillList = listOf(BillId(BILL_ID), BillId(BILL_ID_2), BillId(BILL_ID_3))

        chatbotAiTransactionService.verifyAuthorization(authorizationToken, anotherBillList) shouldBe true
    }

    @Test
    fun `deve validar token de autorização correto para o pix`() {
        val authorizationToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************.wFOid-_J92Pl8Cwh9ZJ4szwJ50FpbtB88yYcsrPfTAU"

        chatbotAiTransactionService.verifyAuthorization(authorizationToken, "pixkey", 100L) shouldBe true
    }

    @Test
    fun `não deve validar token de autorização nulo`() {
        chatbotAiTransactionService.verifyAuthorization(null, billIds) shouldBe false
    }

    @Test
    fun `não deve validar token de autorização mal formatado`() {
        chatbotAiTransactionService.verifyAuthorization("token invalido", billIds) shouldBe false
    }

    @Test
    fun `não deve validar token de autorização mal formatado quando faz um pix`() {
        chatbotAiTransactionService.verifyAuthorization("token invalido", "pixkey", 100L) shouldBe false
    }

    @Test
    fun `não deve validar token de autorização errado`() {
        // Token gerado com: listOf(BillId(BILL_ID), BillId(BILL_ID_2), BillId(BILL_ID_3))
        val authorizationToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1D4CGFO-UMwioJwZOJqDGP-2XTD4v2eucXXeq60VWqk"

        val anotherBillList = listOf(BillId(BILL_ID), BillId(BILL_ID_2), BillId(BILL_ID_3), BillId("OUTRA-BILL"))

        chatbotAiTransactionService.verifyAuthorization(authorizationToken, anotherBillList) shouldBe false
    }

    @Test
    fun `não deve validar token de autorização com um valor errado`() {
        val authorizationToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************.wFOid-_J92Pl8Cwh9ZJ4szwJ50FpbtB88yYcsrPfTAU"

        chatbotAiTransactionService.verifyAuthorization(authorizationToken, "pixkey", 10L) shouldBe false
    }

    @Test
    fun `não deve validar token de autorização com um pixkey errado`() {
        val authorizationToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************.wFOid-_J92Pl8Cwh9ZJ4szwJ50FpbtB88yYcsrPfTAU"

        chatbotAiTransactionService.verifyAuthorization(authorizationToken, "anotherPixkey", 100L) shouldBe false
    }

    @Test
    fun `deve cancelar a transação`() {
        every {
            chatbotAiTransactionAdapter.cancelTransaction("123", ChatbotUserId("*************"))
        } returns TransactionResult.Success.right()

        val result = chatbotAiTransactionService.cancelTransaction("123", ChatbotUserId("*************"))

        result.isRight() shouldBe true
    }

    @Test
    fun `não deve cancelar a transação se ela não existir`() {
        every {
            chatbotAiTransactionAdapter.cancelTransaction("123", ChatbotUserId("*************"))
        } returns ChatbotAiTransactionError.TransactionNotFound.left()

        val result = chatbotAiTransactionService.cancelTransaction("123", ChatbotUserId("*************"))

        result.isLeft() shouldBe true
        result.onLeft { it shouldBe ChatbotAiTransactionError.TransactionNotFound }
    }

    @Test
    fun `não deve cancelar a transação se ela não estiver ativa`() {
        every {
            chatbotAiTransactionAdapter.cancelTransaction("123", ChatbotUserId("*************"))
        } returns ChatbotAiTransactionError.IllegalTransactionState.left()

        val result = chatbotAiTransactionService.cancelTransaction("123", ChatbotUserId("*************"))

        result.isLeft() shouldBe true
        result.onLeft { it shouldBe ChatbotAiTransactionError.IllegalTransactionState }
    }
}