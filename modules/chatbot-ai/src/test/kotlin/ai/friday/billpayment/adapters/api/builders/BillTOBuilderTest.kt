package ai.friday.billpayment.adapters.api.builders

import ai.friday.billpayment.adapters.api.BillAction
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDateTime
import io.kotest.matchers.shouldBe
import java.time.LocalDate
import java.time.LocalTime
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class BillTOBuilderTest {

    private val billTOBuilder = BillTOBuilder(pixQrCodeCompoundDataBuilder = null)
    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()
    private val member = wallet.founder

    @Nested
    @DisplayName("allowedActions")
    inner class AllowedActions {

        @ParameterizedTest
        @EnumSource(value = BillType::class, mode = EnumSource.Mode.EXCLUDE, names = ["AUTOMATIC_PIX"])
        fun `deve retornar lista vazia para todos os tipos de bill exceto AUTOMATIC_PIX`(billType: BillType) {
            // given
            val billView = createBillView(billType = billType)

            // when
            val result = billTOBuilder.toBillTO(billView, member)

            // then
            result.allowedActions shouldBe emptyList()
        }

        @Test
        fun `deve retornar lista vazia quando billType é AUTOMATIC_PIX e dueDate é hoje`() {
            // given
            val today = getLocalDate()
            val billView = createBillView(
                billType = BillType.AUTOMATIC_PIX,
                dueDate = today,
            )

            // when
            val result = billTOBuilder.toBillTO(billView, member)

            // then
            result.allowedActions shouldBe emptyList()
        }

        @Test
        fun `deve retornar IGNORE_BILL quando billType é AUTOMATIC_PIX e dueDate é futura`() {
            // given
            val futureDate = getLocalDate().plusDays(1)
            val billView = createBillView(
                billType = BillType.AUTOMATIC_PIX,
                dueDate = futureDate,
            )

            // when
            val result = billTOBuilder.toBillTO(billView, member)

            // then
            result.allowedActions shouldBe listOf(BillAction.IGNORE_BILL)
        }

        @Test
        fun `deve retornar lista vazia quando billType = AUTOMATIC_PIX e dueDate é passada`() {
            // given
            val pastDate = getLocalDate().minusDays(1)
            val billView = createBillView(
                billType = BillType.AUTOMATIC_PIX,
                dueDate = pastDate,
            )

            // when
            val result = billTOBuilder.toBillTO(billView, member)

            // then
            result.allowedActions shouldBe listOf()
        }

        @Test
        fun `deve retornar IGNORE_BILL quando billType é AUTOMATIC_PIX e dueDate é alguns dias no futuro`() {
            // given
            val futureDate = getLocalDate().plusDays(7)
            val billView = createBillView(
                billType = BillType.AUTOMATIC_PIX,
                dueDate = futureDate,
            )

            // when
            val result = billTOBuilder.toBillTO(billView, member)

            // then
            result.allowedActions shouldBe listOf(BillAction.IGNORE_BILL)
        }
    }

    private fun createBillView(
        billType: BillType = BillType.CONCESSIONARIA,
        dueDate: LocalDate = getLocalDate().plusDays(1),
    ): BillView {
        return BillView(
            billId = ai.friday.billpayment.app.bill.BillId("TEST-BILL-ID"),
            walletId = ai.friday.billpayment.app.wallet.WalletId("TEST-WALLET-ID"),
            billDescription = "Test Bill",
            amount = 1000L,
            amountTotal = 1000L,
            amountPaid = null,
            interest = 0L,
            fine = 0L,
            discount = 0L,
            paymentLimitTime = LocalTime.NOON,
            dueDate = dueDate,
            effectiveDueDate = dueDate,
            status = ai.friday.billpayment.app.bill.BillStatus.ACTIVE,
            createdOn = getLocalDateTime(),
            billType = billType,
            recipient = null,
            barCode = null,
            paidDate = null,
            assignor = "Test Assignor",
            source = ai.friday.billpayment.app.bill.ActionSource.Api(
                ai.friday.billpayment.app.account.AccountId("TEST-ACCOUNT-ID"),
            ),
            payerDocument = null,
            payerName = null,
            payerAlias = null,
            warningCode = ai.friday.billpayment.app.bill.WarningCode.NONE,
            amountCalculationModel = null,
            externalId = null,
            recurrenceRule = null,
            schedule = null,
            fichaCompensacaoType = null,
            markedAsPaidBy = null,
            subscriptionFee = false,
            securityValidationResult = null,
            transactionCorrelationId = null,
            paymentDetails = null,
            tags = emptySet(),
            categoryId = null,
            categorySuggestions = emptyList(),
            pixQrCodeData = null,
            brand = null,
            participants = null,
            goalId = null,
            automaticPixData = null,
            automaticPixAuthorizationMaximumAmount = null,
        )
    }
}