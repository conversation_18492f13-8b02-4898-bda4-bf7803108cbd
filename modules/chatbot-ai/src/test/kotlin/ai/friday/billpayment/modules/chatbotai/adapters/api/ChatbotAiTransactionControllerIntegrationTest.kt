package ai.friday.billpayment.modules.chatbotai.adapters.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillImageProvider
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.BILL_ID
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.BILL_ID_3
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.buildCookie
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.modules.chatbotai.adapters.ChatbotAiTransactionError
import ai.friday.billpayment.modules.chatbotai.adapters.TransactionResult
import ai.friday.billpayment.modules.chatbotai.app.AuthorizationDetails
import ai.friday.billpayment.modules.chatbotai.app.ChatbotAiTransaction
import ai.friday.billpayment.modules.chatbotai.app.ChatbotAiTransactionService
import ai.friday.billpayment.modules.chatbotai.app.ChatbotAiTransactionStatus
import ai.friday.billpayment.modules.chatbotai.app.ChatbotAiTransactionType
import ai.friday.billpayment.modules.chatbotai.app.TransactionDetails
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
class ChatbotAiTransactionControllerIntegrationTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {
    private val enhancedClient = getDynamoDB()

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private var walletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = accountRepository,
    )

    private val walletFixture = WalletFixture()

    private val wallet = walletFixture.buildWallet()
    private val transactionId = "TRANSACTION-123"
    private val transactionGroupId = "TRANSACTION-GROUP-456"

    private val bills = listOf(
        getActiveBill(billId = BillId(BILL_ID)),
        getActiveBill(billId = BillId(BILL_ID_2)),
        getActiveBill(billId = BillId(BILL_ID_3)),
    )

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @MockBean(ChatbotAiTransactionService::class)
    fun chatbotAiTransactionService() = chatbotAiTransactionService
    private val chatbotAiTransactionService: ChatbotAiTransactionService = mockk()

    @MockBean(BillImageProvider::class)
    fun billImageProvider() = billImageProvider
    private val billImageProvider: BillImageProvider = mockk()

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        loadAccountIntoDb(dynamoDB)

        accountRepository.create(walletFixture.founderAccount)
        walletRepository.save(wallet)
    }

    @Test
    fun `deve retornar a transação`() {
        every {
            chatbotAiTransactionService.findTransaction(any(), any())
        } returns ChatbotAiTransaction(
            id = transactionId,
            groupId = transactionGroupId,
            status = ChatbotAiTransactionStatus.ACTIVE,
            details = TransactionDetails(
                type = ChatbotAiTransactionType.SWEEPING,
                totalAmount = 46773,
                bills = bills,
                sweepingAmount = 1500,
                sweepingParticipantId = "participant-1",
            ),
        ).right()

        every {
            billImageProvider.getBillImageUrl(any<BillView>())
        } returns null

        val request =
            HttpRequest.GET<ChatbotAiTransaction>("/chatbotAI/transaction/$transactionId")
                .cookie(buildCookie(walletFixture.founderAccount))

        val response = client.toBlocking().exchange(request, Argument.of(ChatbotAiTransactionResponseTO::class.java))

        response.status shouldBe HttpStatus.OK

        response.body().status shouldBe ChatbotAiTransactionStatus.ACTIVE
        response.body().details.type shouldBe ChatbotAiTransactionType.SWEEPING
    }

    @Test
    fun `não deve retornar a transação se ela não existir`() {
        every {
            chatbotAiTransactionService.findTransaction(any(), any())
        } returns ChatbotAiTransactionError.TransactionNotFound.left()

        val request =
            HttpRequest.GET<ChatbotAiTransaction>("/chatbotAI/transaction/$transactionId")
                .cookie(buildCookie(walletFixture.founderAccount))

        val thrown =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

        thrown.status shouldBe HttpStatus.NOT_FOUND
    }

    @Test
    fun `deve confirmar a transação`() {
        every {
            chatbotAiTransactionService.confirmTransaction(any(), any(), any())
        } returns TransactionResult.Success.right()

        val request =
            HttpRequest.POST("/chatbotAI/transaction/$transactionId", Unit)
                .body(AuthorizationDetails())
                .cookie(buildCookie(walletFixture.founderAccount))

        val response = client.toBlocking().exchange(request, String::class.java)

        response.status shouldBe HttpStatus.OK
    }

    @Test
    fun `não deve confirmar a transação se ela não existir`() {
        every {
            chatbotAiTransactionService.confirmTransaction(any(), any(), any())
        } returns ChatbotAiTransactionError.TransactionNotFound.left()

        val request =
            HttpRequest.POST("/chatbotAI/transaction/$transactionId", Unit)
                .body(AuthorizationDetails())
                .cookie(buildCookie(walletFixture.founderAccount))

        val thrown =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

        thrown.status shouldBe HttpStatus.NOT_FOUND
    }

    @Test
    fun `não deve confirmar a transação se ela não estiver ativa`() {
        every {
            chatbotAiTransactionService.confirmTransaction(any(), any(), any())
        } returns ChatbotAiTransactionError.IllegalTransactionState.left()

        val request =
            HttpRequest.POST("/chatbotAI/transaction/$transactionId", Unit)
                .body(AuthorizationDetails())
                .cookie(buildCookie(walletFixture.founderAccount))

        val thrown =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

        thrown.status shouldBe HttpStatus.CONFLICT
    }

    @Test
    fun `deve cancelar a transação`() {
        every {
            chatbotAiTransactionService.cancelTransaction(any(), any())
        } returns TransactionResult.Success.right()

        val request =
            HttpRequest.DELETE<Unit>("/chatbotAI/transaction/$transactionId")
                .cookie(buildCookie(walletFixture.founderAccount))

        val response = client.toBlocking().exchange(request, String::class.java)

        response.status shouldBe HttpStatus.OK
    }

    @Test
    fun `não deve cancelar a transação se ela não existir`() {
        every {
            chatbotAiTransactionService.cancelTransaction(any(), any())
        } returns ChatbotAiTransactionError.TransactionNotFound.left()

        val request =
            HttpRequest.DELETE<Unit>("/chatbotAI/transaction/$transactionId")
                .cookie(buildCookie(walletFixture.founderAccount))

        val thrown =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

        thrown.status shouldBe HttpStatus.NOT_FOUND
    }

    @Test
    fun `não deve cancelar a transação se ela não estiver ativa`() {
        every {
            chatbotAiTransactionService.cancelTransaction(any(), any())
        } returns ChatbotAiTransactionError.IllegalTransactionState.left()

        val request =
            HttpRequest.DELETE<Unit>("/chatbotAI/transaction/$transactionId")
                .cookie(buildCookie(walletFixture.founderAccount))

        val thrown =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

        thrown.status shouldBe HttpStatus.CONFLICT
    }
}