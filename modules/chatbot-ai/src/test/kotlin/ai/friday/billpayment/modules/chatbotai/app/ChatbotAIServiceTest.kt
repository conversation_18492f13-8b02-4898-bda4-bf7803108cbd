package ai.friday.billpayment.modules.chatbotai.app

import DynamoDBUtils
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.account.SystemActivityType
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BatchSchedulingId
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillViewSchedule
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateBillService
import ai.friday.billpayment.app.bill.CreatePixRequest
import ai.friday.billpayment.app.bill.RecipientRequest
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.bill.schedule.ScheduleBillService
import ai.friday.billpayment.app.bill.schedule.ScheduleResult
import ai.friday.billpayment.app.chatbot.ChatbotNotificationService
import ai.friday.billpayment.app.chatbot.GetAccountOrganizations
import ai.friday.billpayment.app.chatbot.OtherInstitutionsResponseTO
import ai.friday.billpayment.app.chatbot.PaymentOrganizationResult
import ai.friday.billpayment.app.integrations.BillRecurrenceRepository
import ai.friday.billpayment.app.integrations.EndToEnd
import ai.friday.billpayment.app.integrations.ExternalTransactionId
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.RequestSweepingCashInCommand
import ai.friday.billpayment.app.integrations.RequestSweepingCashInError
import ai.friday.billpayment.app.integrations.SweepingAccountServiceInterface
import ai.friday.billpayment.app.integrations.SweepingCashInSource
import ai.friday.billpayment.app.integrations.SweepingRequest
import ai.friday.billpayment.app.itp.ConsentId
import ai.friday.billpayment.app.itp.CreatePaymentIntentCommand
import ai.friday.billpayment.app.itp.ITPService
import ai.friday.billpayment.app.itp.PaymentIntentId
import ai.friday.billpayment.app.itp.PaymentIntentResponse
import ai.friday.billpayment.app.onepixpay.OnePixPay
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.onepixpay.OnePixPayStatus
import ai.friday.billpayment.app.payment.ScheduleStrategy
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyDetailsResult
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixKeyOwner
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.recurrence.BillRecurrence
import ai.friday.billpayment.app.recurrence.RecurrenceFrequency
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.recurrence.RecurrenceStatus
import ai.friday.billpayment.app.subscription.Subscription
import ai.friday.billpayment.app.subscription.SubscriptionPaymentStatus
import ai.friday.billpayment.app.subscription.SubscriptionService
import ai.friday.billpayment.app.subscription.SubscriptionStatus
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.BILL_ID
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.BILL_ID_3
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.chatbotai.adapters.api.AvailableLimitType
import ai.friday.billpayment.pixAdded
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.string.shouldEndWith
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Called
import io.mockk.Runs
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.math.BigInteger
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.Arguments.arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource

class ChatbotAIServiceTest {
    private val enhancedClient = DynamoDBUtils.setupDynamoDB()

    private val itpService: ITPService = mockk(relaxed = true)
    private val onePixPayService: OnePixPayService = mockk(relaxed = true)
    private val accountService: AccountService = mockk(relaxed = true)

    val billIds = listOf(BillId(BILL_ID), BillId(BILL_ID_2), BillId(BILL_ID_3))
    val authorizationServerId = "1234-4567-8901-2345"
    val organizationsList = listOf(OtherInstitutionsResponseTO(id = authorizationServerId, title = "Banco", description = null))

    val walletFixture = WalletFixture()
    val wallet = walletFixture.buildWallet()
    val accountId = wallet.founder.accountId
    val walletId = wallet.id
    val findBillService = mockk<FindBillService>(relaxed = true) {
        every {
            find(walletId, any())
        } answers {
            getActiveBill(billId = secondArg())
        }

        every {
            findActiveAndWaitingApprovalBills(any())
        } returns listOf(
            getActiveBill(effectiveDueDate = getLocalDate().minusDays(4)),
            getActiveBill(effectiveDueDate = getLocalDate().minusDays(3)),
            getActiveBill(effectiveDueDate = getLocalDate().minusDays(1)),
            getActiveBill(effectiveDueDate = getLocalDate()),
            getActiveBill(effectiveDueDate = getLocalDate().plusDays(1)),
            getActiveBill(effectiveDueDate = getLocalDate().plusDays(10)),
            getActiveBill(
                effectiveDueDate = getLocalDate().plusDays(20),
                schedule = BillViewSchedule(date = getLocalDate()),
            ),
        )

        every { findBillsWaitingFunds(any()) } returns emptyList()
    }
    val updateBillService = mockk<UpdateBillService>()
    val walletService = mockk<WalletService>() {
        every {
            findAllFounderWallets(accountId)
        } returns listOf(wallet)
        every {
            findWallets(any())
        } returns listOf(wallet)
        every {
            findWallet(any())
        } returns wallet
    }

    val createBillService = mockk<CreateBillService>()
    val scheduleBillService = mockk<ScheduleBillService>()
    val subscriptionService = mockk<SubscriptionService>()
    val recurrenceRepository = mockk<BillRecurrenceRepository>()
    val walletLimitsService = mockk<WalletLimitsService>()
    val sweepingAccountServiceInterface = mockk<SweepingAccountServiceInterface>()
    val systemActivityService = mockk<SystemActivityService>(relaxed = true)
    private val chatbotAiTransactionService = mockk<ChatbotAiTransactionService>()
    private val pixKeyManagement = mockk<PixKeyManagement>()
    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(enhancedClient),
        refundedClient = RefundedBillDynamoDAO(enhancedClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(enhancedClient),
    )
    private val chatbotNotificationService: ChatbotNotificationService = mockk(relaxed = true)

    private val chatbotAIService = ChatbotAIService(
        onePixPayService = onePixPayService,
        findBillService = findBillService,
        itpService = itpService,
        accountService = accountService,
        walletService = walletService,
        forecastService = mockk(),
        updateBillService = updateBillService,
        createBillService = createBillService,
        scheduleBillService = scheduleBillService,
        subscriptionService = subscriptionService,
        recurrenceRepository = recurrenceRepository,
        walletLimitService = walletLimitsService,
        sweepingAccountServiceInterface = sweepingAccountServiceInterface,
        chatbotAiTransactionService = chatbotAiTransactionService,
        pixKeyManagement = pixKeyManagement,
        billRepository = billRepository,
        systemActivityService = systemActivityService,
        chatbotNotificationService = chatbotNotificationService,
    )

    @BeforeEach
    fun setup() {
        val paymentOrganizationResult = PaymentOrganizationResult(authorizationServerId = "1234-4567-8901-2345", bankISPB = "1234", routingNo = 6451, accountNo = 1732, accountDv = "1", accountType = null, bankNo = null, institutionName = "Laurie Ruiz")
        every {
            itpService.listPaymentOrganizations(any())
        } returns GetAccountOrganizations(1, Document(DOCUMENT), organizationsList, mapOf(0 to paymentOrganizationResult)).right()
        every {
            itpService.createPaymentIntentWithOnePixPay(any(), any(), any())
        } returns PaymentIntentId().right()
        every {
            itpService.retrievePaymentRedirectUrl(any())
        } returns PaymentIntentResponse(PaymentIntentId(), "payment-url", ConsentId("")).right()

        every {
            accountService.findAccountById(accountId)
        } returns ACCOUNT.copy(configuration = ACCOUNT.configuration.copy(defaultWalletId = walletId))
    }

    @Nested
    @DisplayName("quando criar um link itp para pagar contas do usuário")
    inner class CreateOnePixPayITPTest {

        @Test
        fun `deve chamar o ITP com as bills passadas como parametro`() {
            chatbotAIService.createOnePixPayITP(walletId, accountId, billIds, authorizationServerId, false)
            verify {
                itpService.createPaymentIntentWithOnePixPay(
                    CreatePaymentIntentCommand(
                        accountId = accountId,
                        walletId = walletId,
                        authorizationServerId = authorizationServerId,
                    ),
                    withArg {
                        it.map { billView -> billView.billId } shouldContainExactlyInAnyOrder billIds
                    },
                    false,
                )
            }
        }

        @Test
        fun `deve chamar o servico que gera PaymentIntent`() {
            chatbotAIService.createOnePixPayITP(walletId, accountId, billIds, authorizationServerId, false)

            verify {
                itpService.createPaymentIntentWithOnePixPay(
                    withArg {
                        it.authorizationServerId shouldBe authorizationServerId
                        it.walletId shouldBe walletId
                        it.accountId shouldBe accountId
                    },
                    withArg {
                        it.size shouldBe billIds.size
                    },
                    false,
                )
            }
        }
    }

    @Nested
    @DisplayName("ao marcar contas como pagas")
    inner class MarkAsPaid {

        @BeforeEach
        fun init() {
            every {
                walletService.findWallet(walletId)
            } returns wallet
            every {
                updateBillService.markAsPaid(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                )
            } returns mockk<Bill>().right()
        }

        @Test
        fun `deve retornar right quando todas as operacoes funcionarem`() {
            val response = chatbotAIService.markAsPaid(
                walletId = walletId,
                accountId = accountId,
                bills = listOf("billId1", "billId2", "billId3").map { BillId(it) },
            )

            response.isRight() shouldBe true
        }

        @Test
        fun `deve retornar left quando alguma operacao falhar`() {
            every {
                updateBillService.markAsPaid(
                    BillId("billId2"),
                    any(),
                    any(),
                    any(),
                    any(),
                )
            } returns mockk<Exception>().left()
            val response = chatbotAIService.markAsPaid(
                walletId = walletId,
                accountId = accountId,
                bills = listOf("billId1", "billId2", "billId3").map { BillId(it) },
            )

            response.isLeft() shouldBe true
        }

        @Test
        fun `deve desagendar contas agendadas antes marcar como pagas`() {
            every { updateBillService.userCancelScheduledPayment(any(), any(), any(), any()) } returns true

            every { findBillService.find(any(), BillId("billId1")) } returns getActiveBill(walletId = WalletId("walletId"), billId = BillId("billId1"), subscriptionFee = false, schedule = BillViewSchedule(batchSchedulingId = null, date = LocalDate.now(), waitingFunds = false, waitingRetry = false))

            val response = chatbotAIService.markAsPaid(
                walletId = walletId,
                accountId = accountId,
                bills = listOf("billId1", "billId2", "billId3").map { BillId(it) },
            )

            val slot = slot<BillId>()

            verify {
                updateBillService.userCancelScheduledPayment(any(), capture(slot), any(), any())
            }

            slot.captured shouldBe BillId("billId1")

            response.isRight() shouldBe true
        }

        @Test
        fun `nao deve marcar assinaturas como pagas`() {
            every { updateBillService.userCancelScheduledPayment(any(), any(), any(), any()) } returns true

            every { findBillService.find(any(), BillId("billId1")) } returns getActiveBill(walletId = WalletId("walletId"), billId = BillId("billId1"), subscriptionFee = true, schedule = BillViewSchedule(batchSchedulingId = null, date = LocalDate.now(), waitingFunds = false, waitingRetry = false))

            val response = chatbotAIService.markAsPaid(
                walletId = walletId,
                accountId = accountId,
                bills = listOf("billId1", "billId2", "billId3").map { BillId(it) },
            )

            verify(exactly = 0) {
                updateBillService.userCancelScheduledPayment(any(), any(), any(), any())
            }

            verify(exactly = 0) {
                updateBillService.markAsPaid(
                    billId = BillId(value = "billId1"),
                    walletId = any(),
                    member = any(),
                    amountPaid = any(),
                    actionSource = any(),
                )
            }

            response.isRight() shouldBe true
        }
    }

    @Nested
    @DisplayName("ao ignorar contas")
    inner class Ignore {

        @BeforeEach
        fun init() {
            every {
                walletService.findWallet(walletId)
            } returns wallet
            every {
                updateBillService.ignoreBill(
                    any(),
                    any(),
                    any(),
                    any(),
                )
            } returns mockk<Bill>().right()
        }

        @Test
        fun `deve retornar right quando todas as operacoes funcionarem`() {
            val response = chatbotAIService.ignore(
                walletId = walletId,
                accountId = accountId,
                bills = listOf("billId1", "billId2", "billId3").map { BillId(it) },
            )

            response.isRight() shouldBe true
        }

        @Test
        fun `deve retornar left quando alguma operacao falhar`() {
            every {
                updateBillService.ignoreBill(
                    BillId("billId2"),
                    any(),
                    any(),
                    any(),
                )
            } returns mockk<Exception>().left()
            val response = chatbotAIService.ignore(
                walletId = walletId,
                accountId = accountId,
                bills = listOf("billId1", "billId2", "billId3").map { BillId(it) },
            )

            response.isLeft() shouldBe true
        }

        @Test
        fun `deve desagendar contas agendadas antes de ignorar`() {
            every { updateBillService.userCancelScheduledPayment(any(), any(), any(), any()) } returns true

            every { findBillService.find(any(), BillId("billId1")) } returns getActiveBill(walletId = WalletId("walletId"), billId = BillId("billId1"), subscriptionFee = false, schedule = BillViewSchedule(batchSchedulingId = null, date = LocalDate.now(), waitingFunds = false, waitingRetry = false))

            val response = chatbotAIService.ignore(
                walletId = walletId,
                accountId = accountId,
                bills = listOf("billId1", "billId2", "billId3").map { BillId(it) },
            )

            val slot = slot<BillId>()

            verify {
                updateBillService.userCancelScheduledPayment(any(), capture(slot), any(), any())
            }

            slot.captured shouldBe BillId("billId1")

            response.isRight() shouldBe true
        }

        @Test
        fun `nao deve marcar ignorar assinaturas`() {
            every { updateBillService.userCancelScheduledPayment(any(), any(), any(), any()) } returns true

            every { findBillService.find(any(), BillId("billId1")) } returns getActiveBill(walletId = WalletId("walletId"), billId = BillId("billId1"), subscriptionFee = true, schedule = BillViewSchedule(batchSchedulingId = null, date = LocalDate.now(), waitingFunds = false, waitingRetry = false))

            val response = chatbotAIService.ignore(
                walletId = walletId,
                accountId = accountId,
                bills = listOf("billId1", "billId2", "billId3").map { BillId(it) },
            )

            verify(exactly = 0) {
                updateBillService.userCancelScheduledPayment(any(), any(), any(), any())
            }

            verify(exactly = 0) {
                updateBillService.ignoreBill(
                    billId = BillId(value = "billId1"),
                    walletId = any(),
                    member = any(),
                    actionSource = any(),
                )
            }

            response.isRight() shouldBe true
        }
    }

    @Nested
    @DisplayName("quando listar contas por data")
    inner class FindBillsTest {
        @Test
        fun `deve listar apenas as contas vencidas quando o range de data for anterior a hoje`() {
            val response = chatbotAIService.findPendingAndWaitingApprovalBillsByWallet(accountId, getLocalDate().minusDays(7), getLocalDate().minusDays(1))

            with(response.first()) {
                this.bills.forEach { bill ->
                    bill.expectedPaymentDate.isBefore(getLocalDate()) shouldBe true
                }

                this.bills.size shouldBe 3
            }
        }

        @Test
        fun `deve listar as contas vencendo hoje e as agendadas para hoje caso o range de data seja o mesmo dia de hoje`() {
            val response = chatbotAIService.findPendingAndWaitingApprovalBillsByWallet(accountId, getLocalDate(), getLocalDate())

            with(response.first()) {
                this.bills.forEach { bill ->
                    bill.expectedPaymentDate shouldBe getLocalDate()
                }

                this.bills.size shouldBe 2
            }
        }

        @Test
        fun `deve listar as contas futuras quando o range de datas forem datas futuras`() {
            val response = chatbotAIService.findPendingAndWaitingApprovalBillsByWallet(accountId, getLocalDate().plusDays(1), getLocalDate().plusDays(30))

            with(response.first()) {
                this.bills.size shouldBe 2
                this.bills.forEach { bill ->
                    bill.expectedPaymentDate.isAfter(getLocalDate()) shouldBe true
                }
            }
        }

        @Test
        fun `deve sempre listar as contas aguardando saldo, mesmo quando nao estiverem no range`() {
            val activeBills = findBillService.findActiveAndWaitingApprovalBills(WalletId(WALLET_ID))

            val billsWaitingFunds = listOf(
                activeBills[0].copy(schedule = BillViewSchedule(date = getLocalDate(), waitingFunds = true)), // Bill repetida não deve ficar duplicada na lista
                getActiveBill(billId = BillId(), dueDate = getLocalDate().minusDays(1), schedule = BillViewSchedule(date = getLocalDate(), waitingFunds = true)),
            )

            every { findBillService.findBillsWaitingFunds(any()) } returns billsWaitingFunds

            val response = chatbotAIService.findPendingAndWaitingApprovalBillsByWallet(accountId, getLocalDate().plusDays(1), getLocalDate().plusDays(30))

            with(response.first()) {
                this.bills.size shouldBe 3
            }
        }
    }

    @Nested
    @DisplayName("subscriptions to pay today")
    inner class SubscriptionsToPayToday {
        private val billsIdList = listOf(BillId("billId1"), BillId("billId2"))
        private val recurrenceId = RecurrenceId(value = "recurrence")
        private val subscription = Subscription(accountId = accountId, document = Document(value = "***********"), status = SubscriptionStatus.ACTIVE, amount = 990, dayOfMonth = 10, recurrenceId = recurrenceId, paymentStatus = SubscriptionPaymentStatus.PAID, walletId = walletId, nextEffectiveDueDate = LocalDate.now())
        private val recurrence = BillRecurrence(
            id = recurrenceId, walletId = walletId, description = "", amount = 990,
            rule = RecurrenceRule(
                frequency = RecurrenceFrequency.MONTHLY,
                startDate = LocalDate.now().minusDays(30),
                pattern = "",
                endDate = null,
            ),
            contactId = null, contactAccountId = AccountId(value = ""), recipientName = "", recipientDocument = null, recipientAlias = "", recipientBankAccount = null, recipientPixKey = null, actionSource = ActionSource.Subscription(accountId = accountId), created = ZonedDateTime.now().minusDays(30), status = RecurrenceStatus.ACTIVE, bills = billsIdList, billType = BillType.PIX, lastDueDate = null, billCategoryId = null,
        )

        @Test
        fun `deve retornar as bills vencidas se a assinatura estiver vencida`() {
            every { subscriptionService.findOrNull(accountId) } returns subscription
            every { recurrenceRepository.find(recurrenceId, subscription.walletId) } returns recurrence
            every { findBillService.find(any(), any()) } returns getActiveBill(walletId = walletId, billId = BillId("billId1"), subscriptionFee = true, effectiveDueDate = LocalDate.now().minusDays(30))

            val response = chatbotAIService.findSubscriptionsToPayToday(accountId, walletId)

            response.size shouldBe 2
        }

        @Test
        fun `se nao tiver uma assinatura, deve retornar uma lista vazia`() {
            every { subscriptionService.findOrNull(accountId) } returns null

            val response = chatbotAIService.findSubscriptionsToPayToday(accountId, walletId)

            response shouldBe emptyList()
        }

        @Test
        fun `nao deve retornar nada se a assinatura estiver em dia`() {
            every { subscriptionService.findOrNull(accountId) } returns subscription
            every { recurrenceRepository.find(recurrenceId, subscription.walletId) } returns recurrence
            every { findBillService.find(any(), any()) } returns getActiveBill(walletId = walletId, billId = BillId("billId1"), subscriptionFee = true, effectiveDueDate = LocalDate.now().plusDays(1))

            val response = chatbotAIService.findSubscriptionsToPayToday(accountId, walletId)

            response.size shouldBe 0
        }

        @Test
        fun `devem ser retornadas as contas que estao vencendo hoje`() {
            every { subscriptionService.findOrNull(accountId) } returns subscription
            every { recurrenceRepository.find(recurrenceId, subscription.walletId) } returns recurrence
            every { findBillService.find(any(), any()) } returns getActiveBill(walletId = walletId, billId = BillId("billId1"), subscriptionFee = true, effectiveDueDate = LocalDate.now())

            val response = chatbotAIService.findSubscriptionsToPayToday(accountId, walletId)

            response.size shouldBe 2
        }
    }

    @Nested
    @DisplayName("quando for validar uma chave pix")
    inner class ValidatePixKeyTest {
        @Test
        fun `deve retornar PixKeyInvalid se a chave do pix nao for valida`() {
            val invalidPixKey = PixKey("invalid-pix-key", PixKeyType.CPF)

            every { pixKeyManagement.findKeyDetails(any(), any()) } returns PixKeyError.MalformedKey.left()

            val result = chatbotAIService.validatePix(
                accountId = accountId,
                walletId = wallet.id,
                pixKey = invalidPixKey,
                amount = 100L,
            )

            result.shouldBeTypeOf<ChatBotScheduleBillResult.PixKeyInvalid>()
        }

        @Test
        fun `deve retornar PixKeyValid se a chave do pix for valida`() {
            val validPixKey = PixKey("***********", PixKeyType.CPF)
            every {
                pixKeyManagement.findKeyDetails(
                    any(),
                    any(),
                )
            } returns Either.Right(
                PixKeyDetailsResult(
                    PixKeyDetails(
                        key = validPixKey,
                        holder = PixKeyHolder(
                            accountNo = BigInteger("4321"),
                            accountDv = "7",
                            ispb = "********",
                            institutionName = "Test",
                            accountType = AccountType.CHECKING,
                            routingNo = 1234,
                        ),
                        owner = PixKeyOwner(
                            name = "Teste",
                            document = "***********",
                        ),
                    ),
                    "e2e",
                ),
            )

            val result = chatbotAIService.validatePix(
                accountId = accountId,
                walletId = wallet.id,
                pixKey = validPixKey,
                amount = 100L,
            )

            result.shouldBeTypeOf<ChatBotScheduleBillResult.PixKeyValid>()
            result.pixKeyDetailsTO.keyValue shouldBe validPixKey.value
            result.pixKeyDetailsTO.recipientInstitution shouldBe "Test"
            result.pixKeyDetailsTO.recipientName shouldBe "Teste"
            result.pixKeyDetailsTO.recipientDocument shouldBe "***********"
            result.pixKeyDetailsTO.keyType shouldBe validPixKey.type
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.chatbotai.app.ChatbotAIServiceTest#pixKeyNotFound")
        fun `deve retornar PixKeyNotFound se a chave do pix nao for encontrada ou confirmada`(error: PixKeyError) {
            val validPixKey = PixKey("***********", PixKeyType.CPF)

            every { pixKeyManagement.findKeyDetails(any(), any()) } returns error.left()

            val result = chatbotAIService.validatePix(
                accountId = accountId,
                walletId = wallet.id,
                pixKey = validPixKey,
                amount = 100L,
            )

            result.shouldBeTypeOf<ChatBotScheduleBillResult.PixKeyNotFound>()
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.chatbotai.app.ChatbotAIServiceTest#unableToValidatePix")
        fun `deve retornar UnableToValidatePix se a chave do pix nao conseguir validar`(error: PixKeyError) {
            val validPixKey = PixKey("***********", PixKeyType.CPF)

            every { pixKeyManagement.findKeyDetails(any(), any()) } returns error.left()

            val result = chatbotAIService.validatePix(
                accountId = accountId,
                walletId = wallet.id,
                pixKey = validPixKey,
                amount = 100L,
            )

            result.shouldBeTypeOf<ChatBotScheduleBillResult.UnableToValidatePix>()
        }
    }

    @Nested
    @DisplayName("quando for agendar um pix")
    inner class SchedulePix {
        val invalidPixKey = PixKey("invalid-pix-key", PixKeyType.CPF)
        val invalidRecipientRequest = RecipientRequest(id = null, accountId = accountId, name = "John Doe", document = "***********", alias = "", bankAccount = null, pixKey = invalidPixKey, qrCode = null)
        val invalidCreatePixRequest = CreatePixRequest(
            walletId = walletId,
            source = ActionSource.VirtualAssistant(accountId = accountId),
            recipient = invalidRecipientRequest,
            amount = 100,
            description = "description",
            dueDate = getLocalDate(),
            automaticPixData = null,
            automaticPixAuthorizationMaximumAmount = null,
        )

        val blankCreatePixRequest = CreatePixRequest(
            walletId = walletId,
            source = ActionSource.VirtualAssistant(accountId = accountId),
            recipient = invalidRecipientRequest.copy(qrCode = null, pixKey = null),
            amount = 100,
            description = "description",
            dueDate = getLocalDate(),
            automaticPixData = null,
            automaticPixAuthorizationMaximumAmount = null,
        )

        @Nested
        @DisplayName("ao validar o beneficiário")
        inner class ValidateRecipientTest {
            @Test
            fun `deve retornar erro de chave invalida quando a chave for invalida`() {
                val response = chatbotAIService.schedulePix(
                    wallet = wallet,
                    accountId = accountId,
                    createPixRequest = invalidCreatePixRequest,
                    member = wallet.getActiveMember(accountId),
                    retryTransaction = false,
                    transactionId = "transactionId",
                )

                response.shouldBeTypeOf<ChatBotScheduleBillResult.PixKeyInvalid>()
            }

            @Test
            fun `deve retornar erro de chave invalida quando nao possuir chave pix nem qrCode`() {
                val response = chatbotAIService.schedulePix(
                    wallet = wallet,
                    accountId = accountId,
                    createPixRequest = blankCreatePixRequest,
                    member = wallet.getActiveMember(accountId),
                    retryTransaction = false,
                    transactionId = "transactionId",
                )

                response.shouldBeTypeOf<ChatBotScheduleBillResult.RecipientNotFound>()
            }
        }

        @Nested
        @DisplayName("ao validar os limites")
        inner class ValidateLimitTest {
            @Test
            fun `deve retornar AssistantLimitExceeded se o valor do pix ultrapassar o limite via whatsapp e nao tiver token de autorizacao`() {
                val validPixKey = PixKey("***********", PixKeyType.CPF)
                val validRecipientRequest = RecipientRequest(id = null, accountId = accountId, name = "John Doe", document = "***********", alias = "", bankAccount = null, pixKey = validPixKey, qrCode = null)
                val createPixRequest = CreatePixRequest(
                    walletId = walletId,
                    source = ActionSource.VirtualAssistant(accountId = accountId),
                    recipient = validRecipientRequest,
                    amount = 100,
                    description = "description",
                    dueDate = getLocalDate(),
                    automaticPixData = null,
                    automaticPixAuthorizationMaximumAmount = null,
                )

                val bill = Bill.build(pixAdded)

                every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns 50L
                every { walletLimitsService.getAvailableLimit(any(), any()) } returns 100L
                every { createBillService.createPix(any(), any()) } returns CreateBillResult.SUCCESS(bill = bill, possibleDuplicateBills = listOf())
                every { scheduleBillService.schedulePayment(any<Wallet>(), any(), any(), any(), any(), any()) } returns bill.right()
                every { scheduleBillService.processScheduleAsync(any()) } just Runs
                every { chatbotAiTransactionService.verifyAuthorization(any(), any(), any()) } returns false

                val response = chatbotAIService.schedulePix(
                    wallet = wallet,
                    accountId = accountId,
                    createPixRequest = createPixRequest,
                    member = wallet.getActiveMember(accountId),
                    retryTransaction = false,
                    transactionId = "transactionId",
                )

                response.shouldBeTypeOf<ChatBotScheduleBillResult.AssistantLimitExceeded>()
            }

            @Test
            fun `deve retornar PixLimitExceeded se o valor do pix ultrapassar o limite diario de pix`() {
                val validPixKey = PixKey("***********", PixKeyType.CPF)
                val validRecipientRequest = RecipientRequest(id = null, accountId = accountId, name = "John Doe", document = "***********", alias = "", bankAccount = null, pixKey = validPixKey, qrCode = null)
                val createPixRequest = CreatePixRequest(
                    walletId = walletId,
                    source = ActionSource.VirtualAssistant(accountId = accountId),
                    recipient = validRecipientRequest,
                    amount = 100,
                    description = "description",
                    dueDate = getLocalDate(),
                    automaticPixData = null,
                    automaticPixAuthorizationMaximumAmount = null,
                )

                val bill = Bill.build(pixAdded)

                every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns 100L
                every { walletLimitsService.getAvailableLimit(any(), any()) } returns 50L
                every { createBillService.createPix(any(), any()) } returns CreateBillResult.SUCCESS(bill = bill, possibleDuplicateBills = listOf())
                every { scheduleBillService.schedulePayment(any<Wallet>(), any(), any(), any(), any(), any()) } returns bill.right()
                every { scheduleBillService.processScheduleAsync(any()) } just Runs

                val response = chatbotAIService.schedulePix(
                    wallet = wallet,
                    accountId = accountId,
                    createPixRequest = createPixRequest,
                    member = wallet.getActiveMember(accountId),
                    retryTransaction = false,
                    transactionId = "transactionId",
                )

                response.shouldBeTypeOf<ChatBotScheduleBillResult.PixLimitExceeded>()
            }
        }

        @Nested
        @DisplayName("se for uma requisicao valida")
        inner class ValidPixRequestTest {

            private val validPixKey = PixKey("***********", PixKeyType.CPF)
            private val validRecipientRequest = RecipientRequest(id = null, accountId = accountId, name = "John Doe", document = "***********", alias = "", bankAccount = null, pixKey = validPixKey, qrCode = null)
            private val createPixRequest = CreatePixRequest(
                walletId = walletId,
                source = ActionSource.VirtualAssistant(accountId = accountId),
                recipient = validRecipientRequest,
                amount = 100,
                description = "description",
                dueDate = getLocalDate(),
                automaticPixData = null,
                automaticPixAuthorizationMaximumAmount = null,
            )
            private val bill = Bill.build(pixAdded)

            @BeforeEach
            fun setup() {
                every { createBillService.createPix(any(), any()) } answers {
                    billRepository.save(bill)
                    CreateBillResult.SUCCESS(bill = bill, possibleDuplicateBills = listOf())
                }
                every { scheduleBillService.schedulePayment(any<Wallet>(), any(), any(), any(), any(), any()) } returns bill.right()
                every { scheduleBillService.processScheduleAsync(any()) } just Runs
            }

            private fun setupLimits(availableAssistantLimit: Long, availablePixLimit: Long) {
                every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns availableAssistantLimit
                every { walletLimitsService.getAvailableLimit(any(), any()) } returns availablePixLimit
            }

            @Nested
            @DisplayName("e usar o saldo em conta")
            inner class BalanceAvailableTest {

                @Test
                fun `deve retornar BillAlreadyExists se houver um pix com a mesma transaction id e nao estiver retentando a transacao`() {
                    billRepository.save(Bill.build(pixAdded.copy(walletId = walletId, actionSource = ActionSource.VirtualAssistant(accountId, transactionId = "TEST-TRANSACTION"))))

                    setupLimits(
                        availableAssistantLimit = 50L,
                        availablePixLimit = 100L,
                    )
                    every { chatbotAiTransactionService.verifyAuthorization(any(), any(), any()) } returns true

                    val response = chatbotAIService.schedulePix(
                        wallet = wallet,
                        accountId = accountId,
                        createPixRequest = createPixRequest,
                        member = wallet.getActiveMember(accountId),
                        retryTransaction = false,
                        transactionId = "TEST-TRANSACTION",
                    )

                    response.shouldBeTypeOf<ChatBotScheduleBillResult.BillAlreadyExists>()
                }

                @Test
                fun `nao deve retornar BillAlreadyExists se houver um pix com a mesma transaction id mas estiver retentando a transacao`() {
                    billRepository.save(Bill.build(pixAdded.copy(walletId = walletId, actionSource = ActionSource.VirtualAssistant(accountId, transactionId = "TEST-TRANSACTION"))))

                    setupLimits(
                        availableAssistantLimit = 50L,
                        availablePixLimit = 100L,
                    )
                    every { chatbotAiTransactionService.verifyAuthorization(any(), any(), any()) } returns true

                    val response = chatbotAIService.schedulePix(
                        wallet = wallet,
                        accountId = accountId,
                        createPixRequest = createPixRequest,
                        member = wallet.getActiveMember(accountId),
                        retryTransaction = true,
                        transactionId = "TEST-TRANSACTION",
                    )

                    response.shouldBeTypeOf<ChatBotScheduleBillResult.PixScheduled>()
                }

                @Test
                fun `nao deve retornar BillAlreadyExists se o pix tiver outro transaction id`() {
                    billRepository.save(Bill.build(pixAdded.copy(walletId = walletId, actionSource = ActionSource.VirtualAssistant(accountId, transactionId = "ANOTHER-TRANSACTION"))))

                    setupLimits(
                        availableAssistantLimit = 50L,
                        availablePixLimit = 100L,
                    )
                    every { chatbotAiTransactionService.verifyAuthorization(any(), any(), any()) } returns true

                    val response = chatbotAIService.schedulePix(
                        wallet = wallet,
                        accountId = accountId,
                        createPixRequest = createPixRequest,
                        member = wallet.getActiveMember(accountId),
                        retryTransaction = false,
                        transactionId = "TEST-TRANSACTION",
                    )

                    response.shouldBeTypeOf<ChatBotScheduleBillResult.PixScheduled>()
                }

                @Test
                fun `deve agendar o pix quando nao enviar uma chave pix mas enviar um qrcode`() {
                    val qrCode = "00020126360014BR.GOV.BCB.PIX0114+5553981083254520400005303986540525.005802BR5920Rafael Haertel Peres6009SAO PAULO61080540900062240520JSPvpLbB3No0M431d2fd63043E25"
                    val validRecipientRequest = RecipientRequest(id = null, accountId = accountId, name = "John Doe", document = "***********", alias = "", bankAccount = null, pixKey = null, qrCode = qrCode)
                    val createPixRequest = CreatePixRequest(
                        walletId = walletId,
                        source = ActionSource.VirtualAssistant(accountId = accountId),
                        recipient = validRecipientRequest,
                        amount = 100,
                        description = "description",
                        dueDate = getLocalDate(),
                        automaticPixData = null,
                        automaticPixAuthorizationMaximumAmount = null,
                    )

                    setupLimits(
                        availableAssistantLimit = 100L,
                        availablePixLimit = 100L,
                    )

                    val response = chatbotAIService.schedulePix(
                        wallet = wallet,
                        accountId = accountId,
                        createPixRequest = createPixRequest,
                        member = wallet.getActiveMember(accountId),
                        retryTransaction = false,
                        transactionId = "transactionId",
                    )

                    response.shouldBeTypeOf<ChatBotScheduleBillResult.PixScheduled>()
                }

                @Test
                fun `deve retornar PixScheduled se o valor do pix nao ultrapassar o limite via whatsapp e o limite de pix`() {
                    setupLimits(
                        availableAssistantLimit = 100L,
                        availablePixLimit = 100L,
                    )

                    val response = chatbotAIService.schedulePix(
                        wallet = wallet,
                        accountId = accountId,
                        createPixRequest = createPixRequest,
                        member = wallet.getActiveMember(accountId),
                        retryTransaction = false,
                        transactionId = "transactionId",
                    )

                    response.shouldBeTypeOf<ChatBotScheduleBillResult.PixScheduled>()
                }

                @Test
                fun `deve retornar PixScheduled se o valor do pix ultrapassar o limite via whatsapp e tiver token de autorizacao valido`() {
                    setupLimits(
                        availableAssistantLimit = 50L,
                        availablePixLimit = 100L,
                    )
                    every { chatbotAiTransactionService.verifyAuthorization(any(), any(), any()) } returns true

                    val response = chatbotAIService.schedulePix(
                        wallet = wallet,
                        accountId = accountId,
                        createPixRequest = createPixRequest,
                        member = wallet.getActiveMember(accountId),
                        retryTransaction = false,
                        transactionId = "transactionId",
                    )

                    response.shouldBeTypeOf<ChatBotScheduleBillResult.PixScheduled>()
                }
            }

            @Nested
            @DisplayName("e precisar usar a conta conectada")
            inner class SweepingCashInTest {

                private val sweepingRequest = SweepingRequest(
                    amount = 10_00L,
                    transactionId = ExternalTransactionId(),
                    participantId = null,
                )

                private val endToEnd = EndToEnd()

                @BeforeEach
                fun setup() {
                    every { sweepingAccountServiceInterface.requestSweepingCashIn(any()) } returns endToEnd.right()
                    billRepository.save(bill)
                }

                @Test
                fun `deve retornar SweepingTransferError se der erro no pix inteligente`() {
                    setupLimits(
                        availableAssistantLimit = 100L,
                        availablePixLimit = 100L,
                    )
                    every { sweepingAccountServiceInterface.requestSweepingCashIn(any()) } returns RequestSweepingCashInError.GenericError("test").left()

                    val response = chatbotAIService.schedulePix(
                        wallet = wallet,
                        accountId = accountId,
                        createPixRequest = createPixRequest,
                        member = wallet.getActiveMember(accountId),
                        retryTransaction = false,
                        sweepingRequest = sweepingRequest,
                        transactionId = "transactionId",
                    )

                    response.shouldBeTypeOf<ChatBotScheduleBillResult.SweepingTransferError>()
                }

                @Test
                fun `deve criar o pix mas nao agendar se precisar completar o saldo com transferencia inteligente`() {
                    setupLimits(
                        availableAssistantLimit = 100L,
                        availablePixLimit = 100L,
                    )

                    val response = chatbotAIService.schedulePix(
                        wallet = wallet,
                        accountId = accountId,
                        createPixRequest = createPixRequest,
                        member = wallet.getActiveMember(accountId),
                        retryTransaction = false,
                        sweepingRequest = sweepingRequest,
                        transactionId = "transactionId",
                    )

                    response.shouldBeTypeOf<ChatBotScheduleBillResult.PixScheduled>()
                    response.scheduled shouldBe false
                    response.billId shouldBe bill.billId.value

                    verify(exactly = 0) {
                        scheduleBillService.schedulePayment(any<Wallet>(), any(), any(), any(), any(), any())
                        scheduleBillService.schedulePayment(any<BatchSchedulingId>(), any(), any(), any(), any(), any(), any(), any())
                        scheduleBillService.processScheduleAsync(any())
                    }

                    val slot = slot<OnePixPay>()
                    verify {
                        onePixPayService.save(capture(slot))
                    }

                    slot.captured.id.value shouldEndWith endToEnd.value
                    slot.captured.walletId shouldBe bill.walletId
                    slot.captured.billIds shouldContainExactlyInAnyOrder listOf(bill.billId)
                    slot.captured.qrCode shouldBe ""
                    slot.captured.status shouldBe OnePixPayStatus.REQUESTED
                    slot.captured.paymentLimitTime shouldBe bill.paymentLimitTime
                    slot.captured.fundsReceived shouldBe false
                    slot.captured.fundsReceivedAt shouldBe null
                    slot.captured.errorDescription shouldBe null
                }

                @Test
                fun `deve retornar PixScheduled se o valor do pix nao ultrapassar o limite via whatsapp e o limite de pix`() {
                    setupLimits(
                        availableAssistantLimit = 100L,
                        availablePixLimit = 100L,
                    )

                    val response = chatbotAIService.schedulePix(
                        wallet = wallet,
                        accountId = accountId,
                        createPixRequest = createPixRequest,
                        member = wallet.getActiveMember(accountId),
                        retryTransaction = false,
                        sweepingRequest = sweepingRequest,
                        transactionId = "transactionId",
                    )

                    response.shouldBeTypeOf<ChatBotScheduleBillResult.PixScheduled>()

                    val commandSlot = slot<RequestSweepingCashInCommand>()
                    verify {
                        sweepingAccountServiceInterface.requestSweepingCashIn(capture(commandSlot))
                    }

                    with(commandSlot.captured) {
                        sweepingCashInId.value shouldBe sweepingRequest.transactionId.value.removePrefix("TRANSACTION-")
                        requestSource shouldBe SweepingCashInSource.CHATBOT
                        approvalSource shouldBe SweepingCashInSource.CHATBOT
                        externalTransactionId shouldBe sweepingRequest.transactionId
                        walletId shouldBe wallet.id
                        amount shouldBe sweepingRequest.amount
                    }
                }

                @Test
                fun `deve retornar PixScheduled se o valor do pix ultrapassar o limite via whatsapp e tiver token de autorizacao valido`() {
                    setupLimits(
                        availableAssistantLimit = 50L,
                        availablePixLimit = 100L,
                    )
                    every { chatbotAiTransactionService.verifyAuthorization(any(), any(), any()) } returns true

                    val response = chatbotAIService.schedulePix(
                        wallet = wallet,
                        accountId = accountId,
                        createPixRequest = createPixRequest,
                        member = wallet.getActiveMember(accountId),
                        retryTransaction = false,
                        sweepingRequest = sweepingRequest,
                        authorizationToken = "auth code that works",
                        transactionId = "transactionId",
                    )

                    response.shouldBeTypeOf<ChatBotScheduleBillResult.PixScheduled>()

                    val commandSlot = slot<RequestSweepingCashInCommand>()
                    verify {
                        sweepingAccountServiceInterface.requestSweepingCashIn(capture(commandSlot))
                    }

                    with(commandSlot.captured) {
                        requestSource shouldBe SweepingCashInSource.CHATBOT
                        approvalSource shouldBe SweepingCashInSource.WEB_APP
                        externalTransactionId shouldBe sweepingRequest.transactionId
                        walletId shouldBe wallet.id
                        amount shouldBe sweepingRequest.amount
                    }
                }
            }
        }
    }

    @Nested
    @DisplayName("quando buscar o limite de pagamento via WhatsApp")
    inner class AssistantPaymentLimitTest {

        @ParameterizedTest
        @CsvSource("50,50", "13,13", "0,0", "-1,0", "-150,0")
        fun `deve retornar o valor do limite disponivel para pagamento pelo assistente`(availableLimit: Long, expectedAvailableLimit: Long) {
            every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns availableLimit

            chatbotAIService.getAssistantPaymentLimitAvailable(walletId = walletId, accountId = accountId) shouldBe expectedAvailableLimit
        }

        @ParameterizedTest
        @CsvSource("50,50", "13,13", "0,0", "-1,0", "-150,0")
        fun `deve retornar o valor do limite diario disponivel para pagamento por pix`(availableLimit: Long, expectedAvailableLimit: Long) {
            every { walletLimitsService.getAvailableLimit(any()) } returns availableLimit

            chatbotAIService.getPixPaymentLimitAvailable(walletId = walletId) shouldBe expectedAvailableLimit
        }

        @Test
        fun `deve validar o valor de pagamento por pix se estiver abaixo do limite de pix e do whatsapp`() {
            every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns 100L
            every { walletLimitsService.getAvailableLimit(any()) } returns 100L

            chatbotAIService.validateAvailableLimit(
                walletId = walletId,
                accountId = accountId,
                amount = 50L,
                type = AvailableLimitType.PIX,
            ) shouldBe AvailableLimitResult.Success
        }

        @Test
        fun `deve validar o valor de pagamento de bills se estiver abaixo do limite do whatsapp`() {
            every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns 100L

            chatbotAIService.validateAvailableLimit(
                walletId = walletId,
                accountId = accountId,
                amount = 50L,
                type = AvailableLimitType.SCHEDULE_BILLS,
            ) shouldBe AvailableLimitResult.Success
        }

        @Test
        fun `nao deve validar o valor de pagamento de bills se estiver acima do limite do whatsapp`() {
            every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns 100L

            chatbotAIService.validateAvailableLimit(
                walletId = walletId,
                accountId = accountId,
                amount = 150L,
                type = AvailableLimitType.SCHEDULE_BILLS,
            ) shouldBe AvailableLimitResult.AssistantLimitExceeded
        }

        @Test
        fun `nao deve validar o valor de pagamento por pix se estiver acima do limite de pix`() {
            every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns 100L
            every { walletLimitsService.getAvailableLimit(any()) } returns 10L

            chatbotAIService.validateAvailableLimit(
                walletId = walletId,
                accountId = accountId,
                amount = 50L,
                type = AvailableLimitType.PIX,
            ) shouldBe AvailableLimitResult.PixLimitExceeded
        }

        @Test
        fun `nao deve validar o valor de pagamento por pix se estiver acima do limite de pix do whatsapp`() {
            every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns 10L
            every { walletLimitsService.getAvailableLimit(any()) } returns 10L

            chatbotAIService.validateAvailableLimit(
                walletId = walletId,
                accountId = accountId,
                amount = 50L,
                type = AvailableLimitType.PIX,
            ) shouldBe AvailableLimitResult.PixLimitExceeded
        }
    }

    @Nested
    @DisplayName("quando for agendar contas")
    inner class SweepingAccountScheduleTest {

        @Test
        fun `deve retornar AssistantLimitExceeded se o valor total das contas ultrapassar o limite via whatsapp`() {
            every {
                walletLimitsService.getAvailableAssistantLimit(any(), any())
            } returns 0L
            every { chatbotAiTransactionService.verifyAuthorization(any(), any()) } returns false

            val amount = 100_00L
            val bill = getActiveBill(walletId = walletId, billId = BillId("billId1"), amount = amount, effectiveDueDate = LocalDate.now())
            every { findBillService.find(any(), any()) } returns bill

            val response = chatbotAIService.scheduleBills(
                walletId = walletId,
                accountId = accountId,
                bills = listOf(bill.billId),
                sweepingRequest = SweepingRequest(
                    amount = 50_00L,
                    transactionId = ExternalTransactionId(),
                    participantId = null,
                ),
            )
            (response is ChatBotScheduleBillResult.AssistantLimitExceeded) shouldBe true
        }

        @Test
        fun `deve agendar para o vencimento sem checar o limite do assistente se o toDueDate for true`() {
            val bill = getActiveBill(walletId = walletId, billId = BillId("billId1"), amount = 100L, effectiveDueDate = LocalDate.now().plusMonths(3))
            every { findBillService.find(any(), any()) } returns bill
            every { scheduleBillService.schedulePayments(any(), any(), any(), any(), ScheduleStrategy.ofDueDate()) } returns ScheduleResult.Success(emptyList())
            every { scheduleBillService.processScheduleAsync(any()) } returns Unit

            val response = chatbotAIService.scheduleBills(
                walletId = walletId,
                accountId = accountId,
                bills = listOf(bill.billId),
                toDueDate = true,
                sweepingRequest = SweepingRequest(
                    amount = 50_00L,
                    transactionId = ExternalTransactionId(),
                    participantId = null,
                ),
            )

            val slot = slot<ScheduleStrategy>()

            verify(exactly = 0) { walletLimitsService.getAvailableAssistantLimit(any(), any()) }
            verify { scheduleBillService.schedulePayments(any(), any(), any(), any(), scheduleStrategy = capture(slot)) }

            slot.captured shouldBe ScheduleStrategy.ofDueDate()

            (response is ChatBotScheduleBillResult.BillsScheduled) shouldBe true
        }

        @Test
        fun `deve retornar SweepingTransferError se der erro no pix inteligente`() {
            every {
                walletLimitsService.getAvailableAssistantLimit(any(), any())
            } returns 0L
            every { chatbotAiTransactionService.verifyAuthorization(any(), any()) } returns true

            val amount = 60_00L
            val bill = getActiveBill(walletId = walletId, billId = BillId("billId1"), amount = amount, effectiveDueDate = LocalDate.now())
            every { findBillService.find(any(), any()) } returns bill
            every { sweepingAccountServiceInterface.requestSweepingCashIn(any()) } returns RequestSweepingCashInError.GenericError("test").left()

            val response = chatbotAIService.scheduleBills(
                walletId = walletId,
                accountId = accountId,
                bills = listOf(bill.billId),
                sweepingRequest = SweepingRequest(
                    amount = 50_00L,
                    transactionId = ExternalTransactionId(),
                    participantId = null,
                ),
            )
            (response is ChatBotScheduleBillResult.SweepingTransferError) shouldBe true
        }

        @Test
        fun `deve retornar BillsScheduled se o valor total das contas ultrapassar o limite via whatsapp e o token de autorizacao for valido`() {
            every {
                walletLimitsService.getAvailableAssistantLimit(any(), any())
            } returns 0L
            every { chatbotAiTransactionService.verifyAuthorization(any(), any()) } returns true

            val billAmount = 60_00L
            val bill = getActiveBill(walletId = walletId, billId = BillId("billId1"), amount = billAmount, effectiveDueDate = LocalDate.now())
            every { findBillService.find(any(), any()) } returns bill
            val commandSlot = slot<RequestSweepingCashInCommand>()
            every { sweepingAccountServiceInterface.requestSweepingCashIn(capture(commandSlot)) } returns EndToEnd().right()
            every { scheduleBillService.schedulePayments(any(), any(), any(), any(), any()) } returns ScheduleResult.Success(emptyList())
            every { scheduleBillService.processScheduleAsync(any()) } returns Unit

            val sweepingRequest = SweepingRequest(
                amount = 50_00L,
                transactionId = ExternalTransactionId(),
                participantId = null,
                retry = false,
            )
            val response = chatbotAIService.scheduleBills(
                walletId = walletId,
                accountId = accountId,
                bills = listOf(bill.billId),
                sweepingRequest = sweepingRequest,
                authorizationToken = "auth code that works",
            )
            (response is ChatBotScheduleBillResult.BillsScheduled) shouldBe true

            with(commandSlot.captured) {
                sweepingCashInId.value shouldBe sweepingRequest.transactionId.value.removePrefix("TRANSACTION-")
                requestSource shouldBe SweepingCashInSource.CHATBOT
                approvalSource shouldBe SweepingCashInSource.WEB_APP
                externalTransactionId shouldBe sweepingRequest.transactionId
                walletId shouldBe wallet.id
                amount shouldBe sweepingRequest.amount
            }
        }

        @Test
        fun `deve usar um sweepingCashInId se for retentativa`() {
            every {
                walletLimitsService.getAvailableAssistantLimit(any(), any())
            } returns 0L
            every { chatbotAiTransactionService.verifyAuthorization(any(), any()) } returns true

            val billAmount = 60_00L
            val bill = getActiveBill(walletId = walletId, billId = BillId("billId1"), amount = billAmount, effectiveDueDate = LocalDate.now())
            every { findBillService.find(any(), any()) } returns bill
            val commandSlot = slot<RequestSweepingCashInCommand>()
            every { sweepingAccountServiceInterface.requestSweepingCashIn(capture(commandSlot)) } returns EndToEnd().right()
            every { scheduleBillService.schedulePayments(any(), any(), any(), any(), any()) } returns ScheduleResult.Success(emptyList())
            every { scheduleBillService.processScheduleAsync(any()) } returns Unit

            val sweepingRequest = SweepingRequest(
                amount = 50_00L,
                transactionId = ExternalTransactionId(),
                participantId = null,
                retry = true,
            )
            val response = chatbotAIService.scheduleBills(
                walletId = walletId,
                accountId = accountId,
                bills = listOf(bill.billId),
                sweepingRequest = sweepingRequest,
                authorizationToken = "auth code that works",
            )
            (response is ChatBotScheduleBillResult.BillsScheduled) shouldBe true

            with(commandSlot.captured) {
                sweepingCashInId.value shouldNotBe sweepingRequest.transactionId.value.removePrefix("TRANSACTION-")
                requestSource shouldBe SweepingCashInSource.CHATBOT
                approvalSource shouldBe SweepingCashInSource.WEB_APP
                externalTransactionId shouldBe sweepingRequest.transactionId
                walletId shouldBe wallet.id
                amount shouldBe sweepingRequest.amount
            }
        }

        @Test
        fun `deve retornar BillsScheduled se o valor total das contas nao ultrapassar o limite via whatsapp`() {
            every {
                walletLimitsService.getAvailableAssistantLimit(any(), any())
            } returns 80L
            every { chatbotAiTransactionService.verifyAuthorization(any(), any()) } returns true

            val billAmount = 60_00L
            val bill = getActiveBill(walletId = walletId, billId = BillId("billId1"), amount = billAmount, effectiveDueDate = LocalDate.now())
            every { findBillService.find(any(), any()) } returns bill
            val commandSlot = slot<RequestSweepingCashInCommand>()
            every { sweepingAccountServiceInterface.requestSweepingCashIn(capture(commandSlot)) } returns EndToEnd().right()
            every { scheduleBillService.schedulePayments(any(), any(), any(), any(), any()) } returns ScheduleResult.Success(emptyList())
            every { scheduleBillService.processScheduleAsync(any()) } returns Unit

            val sweepingRequest = SweepingRequest(
                amount = 30_00L,
                transactionId = ExternalTransactionId(),
                participantId = null,
            )
            val response = chatbotAIService.scheduleBills(
                walletId = walletId,
                accountId = accountId,
                bills = listOf(bill.billId),
                sweepingRequest = sweepingRequest,
            )
            (response is ChatBotScheduleBillResult.BillsScheduled) shouldBe true

            with(commandSlot.captured) {
                requestSource shouldBe SweepingCashInSource.CHATBOT
                approvalSource shouldBe SweepingCashInSource.CHATBOT
                externalTransactionId shouldBe sweepingRequest.transactionId
                walletId shouldBe wallet.id
                amount shouldBe sweepingRequest.amount
            }
        }

        @Test
        fun `deve salvar a intencao de pagamento como OPP se o valor total das contas nao ultrapassar o limite via whatsapp`() {
            every {
                walletLimitsService.getAvailableAssistantLimit(any(), any())
            } returns 80L
            every { chatbotAiTransactionService.verifyAuthorization(any(), any()) } returns true

            val amount = 60_00L
            val bill = getActiveBill(walletId = walletId, billId = BillId("billId1"), amount = amount, effectiveDueDate = LocalDate.now())
            every { findBillService.find(any(), any()) } returns bill

            val endToEnd = EndToEnd()
            every { sweepingAccountServiceInterface.requestSweepingCashIn(any()) } returns endToEnd.right()

            val response = chatbotAIService.scheduleBills(
                walletId = walletId,
                accountId = accountId,
                bills = listOf(bill.billId),
                sweepingRequest = SweepingRequest(
                    amount = 30_00L,
                    transactionId = ExternalTransactionId(),
                    participantId = null,
                ),
            )
            (response is ChatBotScheduleBillResult.BillsScheduled) shouldBe true

            val slot = slot<OnePixPay>()
            verify {
                scheduleBillService wasNot called
                onePixPayService.save(capture(slot))
            }

            slot.captured.id.value shouldEndWith endToEnd.value
            slot.captured.walletId shouldBe walletId
            slot.captured.billIds shouldContainExactlyInAnyOrder listOf(bill.billId)
            slot.captured.qrCode shouldBe ""
            slot.captured.status shouldBe OnePixPayStatus.REQUESTED
            slot.captured.paymentLimitTime shouldBe bill.paymentLimitTime
            slot.captured.fundsReceived shouldBe false
            slot.captured.fundsReceivedAt shouldBe null
            slot.captured.errorDescription shouldBe null
        }
    }

    @Nested
    @DisplayName("quando for retentar uma transferência inteligente")
    inner class RetrySweepingCashInTest {

        private val endToEnd = EndToEnd()

        @BeforeEach
        fun setup() {
            every { sweepingAccountServiceInterface.requestSweepingCashIn(any()) } returns endToEnd.right()
        }

        @Test
        fun `deve retornar SweepingTransferError se der erro no pix inteligente`() {
            every { sweepingAccountServiceInterface.requestSweepingCashIn(any()) } returns RequestSweepingCashInError.GenericError("test").left()

            val response = chatbotAIService.retrySweepingTransfer(
                walletId = wallet.id,
                amount = 100,
                participantId = null,
            )

            response.isLeft() shouldBe true
            response.mapLeft {
                it.shouldBeTypeOf<SweepingTransferErrorReason.GenericReason>()
            }
        }

        @Test
        fun `deve retornar sucesso se conseguir solicitar o pix inteligente`() {
            val response = chatbotAIService.retrySweepingTransfer(
                walletId = wallet.id,
                amount = 100,
                participantId = null,
            )

            response.isRight() shouldBe true
            response.map {
                it shouldBe endToEnd
            }
        }
    }

    @Nested
    @DisplayName("system activity")
    inner class SystemActivityTest {

        @Test
        fun `deve retornar a lista de system activities se existir`() {
            every { systemActivityService.getSystemActivityFlag(any(), SystemActivityType.PromotedSweepingAccountOptOut) } returns true

            val response = chatbotAIService.getSystemActivities(
                accountId = accountId,
                listOf(SystemActivityType.PromotedSweepingAccountOptOut.name, "NonExistentType"),
            )

            verify(exactly = 1) {
                systemActivityService.getSystemActivityFlag(any(), any())
            }

            response.size shouldBe 1
            response[0].type shouldBe SystemActivityType.PromotedSweepingAccountOptOut
        }

        @Test
        fun `deve retornar uma lista vazia se nao achar system activities`() {
            val response = chatbotAIService.getSystemActivities(
                accountId = accountId,
                listOf("NonExistentType", "NonExistentType2"),
            )

            response.size shouldBe 0
        }

        @Test
        fun `deve retornar vazio se nao existir a system activity`() {
            val response = chatbotAIService.getSystemActivities(
                accountId = accountId,
                listOf("NonExistentType"),
            )

            verify { systemActivityService wasNot Called }

            response.size shouldBe 0
        }

        @Test
        fun `deve conseguir salvar o system activity se existir o tipo`() {
            val response = chatbotAIService.setSystemActivityFlag(
                accountId = accountId,
                SystemActivityType.PromotedSweepingAccountOptOut.name,
                true,
            )

            verify {
                systemActivityService.setSystemActivityFlag(accountId, SystemActivityType.PromotedSweepingAccountOptOut, true)
            }

            response.isRight() shouldBe true
        }

        @Test
        fun `deve retornar NotImplemented ao salvar o system activity se nao existir o tipo`() {
            val response = chatbotAIService.setSystemActivityFlag(
                accountId = accountId,
                "NonExistentType",
                true,
            )

            verify {
                systemActivityService wasNot Called
            }

            response.isLeft() shouldBe true
            response.mapLeft {
                it shouldBe SystemActivityErrorReason.NotImplemented
            }
        }

        @Test
        fun `deve retornar Failure ao salvar o system activity se um erro nao esperado`() {
            every {
                systemActivityService.setSystemActivityFlag(any(), any(), any())
            } throws RuntimeException("test")

            val response = chatbotAIService.setSystemActivityFlag(
                accountId = accountId,
                SystemActivityType.PromotedSweepingAccountOptOut.name,
                true,
            )

            verify {
                systemActivityService.setSystemActivityFlag(accountId, SystemActivityType.PromotedSweepingAccountOptOut, true)
            }

            response.isLeft() shouldBe true
            response.mapLeft {
                it.shouldBeTypeOf<SystemActivityErrorReason.Failure>()
                it.error.message shouldBe "test"
                it.error.shouldBeTypeOf<RuntimeException>()
            }
        }
    }

    companion object {
        @JvmStatic
        fun unableToValidatePix(): Stream<Arguments> = Stream.of(
            arguments(PixKeyError.UnknownError),
            arguments(PixKeyError.SystemUnavailable),
        )

        @JvmStatic
        fun pixKeyNotFound(): Stream<Arguments> = Stream.of(
            arguments(PixKeyError.KeyNotFound),
            arguments(PixKeyError.KeyNotConfirmed),
        )
    }
}