package ai.friday.billpayment.modules.manualentry.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.AbstractBillPaymentDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.GlobalSecondaryIndexNames
import ai.friday.billpayment.adapters.dynamodb.INDEX_1
import ai.friday.billpayment.adapters.dynamodb.RecurrenceRuleEntity
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.manualentry.ManualEntryId
import ai.friday.billpayment.app.manualentry.ManualEntryType
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.recurrence.RecurrenceStatus
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.manualentry.ManualEntryModule
import ai.friday.billpayment.modules.manualentry.app.integrations.ManualEntryRecurrenceRepository
import ai.friday.billpayment.modules.manualentry.app.recurrence.ManualEntryRecurrence
import ai.friday.morning.date.dateFormat
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.context.annotation.Property
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val INDEX_1_PRIMARY_KEY = "MANUAL_RECURRENCE"

@ManualEntryModule
class ManualEntryRecurrenceDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<ManualEntryRecurrenceEntity>(cli, ManualEntryRecurrenceEntity::class.java)

@ManualEntryModule
class ManualEntryRecurrenceDBRepository(
    private val client: ManualEntryRecurrenceDynamoDAO,
    @Property(name = "recurrence.lastLimitDate") private val lastLimitDateString: String,
) : ManualEntryRecurrenceRepository {

    private val mapper = jacksonObjectMapper()

    override fun findAll(status: RecurrenceStatus): List<ManualEntryRecurrence> {
        return client.findByPartitionKeyAndScanKeyOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            INDEX_1_PRIMARY_KEY,
            status.name,
        ).map { it.toRecurrence() }
    }

    override fun find(recurrenceId: RecurrenceId, walletId: WalletId): ManualEntryRecurrence {
        return findOrNull(recurrenceId, walletId)
            ?: throw ItemNotFoundException("Manual Entry Recurrence ${recurrenceId.value} not found for wallet ${walletId.value}")
    }

    override fun findOrNull(recurrenceId: RecurrenceId, walletId: WalletId): ManualEntryRecurrence? {
        return client.findByPrimaryKey(recurrenceId.value, walletId.value)?.toRecurrence()
    }

    override fun findByWalletId(walletId: WalletId): List<ManualEntryRecurrence> {
        return client.findByPartitionKeyAndScanKeyOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            INDEX_1_PRIMARY_KEY,
            walletId.value,
        ).map { it.toRecurrence() }
    }

    private fun ManualEntryRecurrenceEntity.toRecurrence(): ManualEntryRecurrence {
        val actionSource: ActionSource.WalletActionSource = if (actionSource.contains("Webapp")) {
            ActionSource.Api(accountId = AccountId(sortKey))
        } else {
            mapper.readerFor(ActionSource::class.java).readValue(actionSource)
        }

        return ManualEntryRecurrence(
            id = RecurrenceId(partitionKey),
            walletId = WalletId(sortKey),
            rule = RecurrenceRule(
                frequency = rule.frequency,
                startDate = LocalDate.parse(rule.startDate, dateFormat),
                endDate = rule.endDate?.let { LocalDate.parse(it, dateFormat) },
                pattern = rule.pattern.orEmpty(),
            ),
            actionSource = actionSource,
            created = ZonedDateTime.parse(created, DateTimeFormatter.ISO_DATE_TIME),
            status = status,
            manualEntries = manualEntryIds.map { ManualEntryId(it) },
            lastDueDate = LocalDate.parse(lastDueDate ?: lastLimitDateString, dateFormat),
            type = manualEntryType,
            title = title,
            description = description.orEmpty(),
            amount = amount,
            categoryId = categoryId?.let { PFMCategoryId(it) },
        )
    }

    override fun save(recurrence: ManualEntryRecurrence) {
        val entity = ManualEntryRecurrenceEntity().apply {
            partitionKey = recurrence.id.value
            sortKey = recurrence.walletId.value
            rule = RecurrenceRuleEntity().apply {
                frequency = recurrence.rule.frequency
                startDate = recurrence.rule.startDate.format(dateFormat)
                endDate = recurrence.rule.endDate?.format(dateFormat)
                pattern = recurrence.rule.pattern
            }
            actionSource = mapper.writeValueAsString(recurrence.actionSource)
            created = recurrence.created.format(DateTimeFormatter.ISO_DATE_TIME)
            manualEntryIds = recurrence.manualEntries.map { it.value }
            status = recurrence.status
            gSIndex1SortKey = recurrence.status.name
            lastDueDate = recurrence.lastDueDate?.format(dateFormat)
            manualEntryType = recurrence.type
            title = recurrence.title
            description = recurrence.description
            amount = recurrence.amount
            categoryId = recurrence.categoryId?.value
        }
        client.save(entity)
    }
}

@DynamoDbBean
class ManualEntryRecurrenceEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    var gSIndex1PartitionKey: String = INDEX_1_PRIMARY_KEY

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1SortKey: String

    @get:DynamoDbAttribute(value = "Rule")
    lateinit var rule: RecurrenceRuleEntity

    @get:DynamoDbAttribute(value = "Source")
    lateinit var actionSource: String

    @get:DynamoDbAttribute(value = "Created")
    lateinit var created: String

    @get:DynamoDbAttribute(value = "ManualEntryIds")
    lateinit var manualEntryIds: List<String>

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: RecurrenceStatus

    @get:DynamoDbAttribute(value = "ManualEntryType")
    lateinit var manualEntryType: ManualEntryType

    @get:DynamoDbAttribute(value = "LastDueDate")
    var lastDueDate: String? = null

    @get:DynamoDbAttribute(value = "Title")
    lateinit var title: String

    @get:DynamoDbAttribute(value = "Description")
    var description: String? = null

    @get:DynamoDbAttribute(value = "Amount")
    var amount: Long = 0

    @get:DynamoDbAttribute(value = "CategoryId")
    var categoryId: String? = null
}