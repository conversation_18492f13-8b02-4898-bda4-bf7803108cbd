package ai.friday.billpayment.modules.manualentry.adapters.notification

import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.integrations.NotificationService
import ai.friday.billpayment.app.manualentry.ManualEntry
import ai.friday.billpayment.app.notification.NotificationTemplate
import ai.friday.billpayment.app.notification.NotificationType
import ai.friday.billpayment.app.notification.TemplatesConfiguration
import ai.friday.billpayment.app.notification.WhatsappNotification
import ai.friday.billpayment.app.notification.buildWalletTimelineDatePath
import ai.friday.billpayment.app.payment.buildFormattedAmount
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.modules.manualentry.ManualEntryModule
import ai.friday.billpayment.modules.manualentry.app.integrations.ManualEntryNotificationService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate

@ManualEntryModule
class ManualEntryNotificationAdapter(
    private val notificationService: NotificationService,
    private val notificationConfig: TemplatesConfiguration,
) : ManualEntryNotificationService {
    override fun notifyReminder(
        members: List<Member>,
        wallet: Wallet,
        reminder: ManualEntry,
    ) {
        notificationService.notifyMembers(members, NotificationType.MANUAL_ENTRY_REMINDER) { account ->
            val amountText = if (reminder.amount == 0L) "-" else buildFormattedAmount(reminder.amount)
            val descriptionText = reminder.description.ifEmpty { "-" }

            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(notificationConfig.whatsappTemplates.reminderNotification),
                configurationKey = "reminder-notification",
                parameters = buildList {
                    add(reminder.title)
                    add(descriptionText)
                    add(amountText)
                    add(wallet.name)
                },
                quickReplyButtonsWhatsAppParameter = buildList {
                    add("""{"action":"MARK_REMINDER_AS_DONE","payload":"${reminder.id.value}"}""")
                },
            )
        }
    }

    override fun notifyExpiredReminderWallet(
        members: List<Member>,
        wallet: Wallet,
        entries: List<String>,
    ) {
        notificationService.notifyMembers(members, NotificationType.MANUAL_ENTRY_REMINDER) { account ->
            val (template, configurationKey) = if (entries.size > 1) {
                notificationConfig.whatsappTemplates.reminderExpiredNotification to "reminder-expired-notification"
            } else {
                notificationConfig.whatsappTemplates.reminderExpiredNotificationSingular to "reminder-expired-notification-singular"
            }

            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(template),
                configurationKey = configurationKey,
                parameters = buildList {
                    add(wallet.name)
                },
                buttonWhatsAppParameter = buildWalletTimelineDatePath(getLocalDate().minusDays(1)),
            )
        }
    }
}