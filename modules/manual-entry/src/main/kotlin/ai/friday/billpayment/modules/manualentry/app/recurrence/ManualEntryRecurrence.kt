package ai.friday.billpayment.modules.manualentry.app.recurrence

import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.manualentry.ManualEntryId
import ai.friday.billpayment.app.manualentry.ManualEntryType
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.recurrence.BaseRecurrence
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.recurrence.RecurrenceStatus
import ai.friday.billpayment.app.wallet.WalletId
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.UUID

data class ManualEntryRecurrence(
    override val id: RecurrenceId = RecurrenceId("MANUAL_RECURRENCE-${UUID.randomUUID()}"),
    override val walletId: WalletId,
    override val rule: RecurrenceRule,
    override val actionSource: ActionSource.WalletActionSource,
    override val created: ZonedDateTime,
    override val status: RecurrenceStatus,
    override val lastDueDate: LocalDate? = null,
    val manualEntries: List<ManualEntryId> = listOf(),
    val type: ManualEntryType,
    val amount: Long = 0L,
    val title: String,
    val description: String = "",
    val categoryId: PFMCategoryId? = null,
) : BaseRecurrence {
    fun plusManualEntryId(manualEntryId: ManualEntryId) = copy(manualEntries = manualEntries + manualEntryId)
    fun setLastDueDate(localDate: LocalDate) = copy(lastDueDate = localDate)
}