package ai.friday.billpayment.modules.manualentry.app.recurrence

import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.WarningCode
import ai.friday.billpayment.app.bill.duplication.PossibleDuplicate
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.manualentry.ManualEntry
import ai.friday.billpayment.app.manualentry.ManualEntryId
import ai.friday.billpayment.app.manualentry.ManualEntryStatus
import ai.friday.billpayment.app.manualentry.ManualEntryType
import ai.friday.billpayment.app.recurrence.BaseRecurrenceService
import ai.friday.billpayment.app.recurrence.RecurrenceConfiguration
import ai.friday.billpayment.app.recurrence.RecurrenceStatus
import ai.friday.billpayment.modules.manualentry.ManualEntryModule
import ai.friday.billpayment.modules.manualentry.app.integrations.ManualEntryRecurrenceRepository
import ai.friday.billpayment.modules.manualentry.app.integrations.ManualEntryRepository
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.time.LocalDate
import java.util.concurrent.CompletableFuture
import java.util.concurrent.CompletionStage
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@ManualEntryModule
open class ManualEntryRecurrenceService(
    private val manualEntryRecurrenceRepository: ManualEntryRecurrenceRepository,
    accountRepository: AccountRepository,
    walletRepository: WalletRepository,
    recurrenceConfiguration: RecurrenceConfiguration,
    private val manualEntryRepository: ManualEntryRepository,
) : BaseRecurrenceService<ManualEntryRecurrence>(
    recurrenceRepository = manualEntryRecurrenceRepository,
    recurrenceConfiguration = recurrenceConfiguration,
    accountRepository = accountRepository,
    walletRepository = walletRepository,
) {

    override val LOG = LoggerFactory.getLogger(ManualEntryRecurrenceService::class.java)
    override val canStartInThePast: Boolean = true

    override fun getPossibleDuplicates(recurrence: ManualEntryRecurrence): List<PossibleDuplicate> = listOf()

    override fun calculateWarningCode(recurrence: ManualEntryRecurrence, firstDueDate: LocalDate): WarningCode? = null

    override fun doCreate(
        recurrence: ManualEntryRecurrence,
        sequence: List<LocalDate>,
    ): CompletionStage<ManualEntryRecurrence> {
        return try {
            CompletableFuture.completedStage(
                sequence
                    .fold(recurrence) { aggregate, current -> generateRecurringInstance(aggregate, current, false) },
            )
        } catch (e: Exception) {
            LOG.error(Markers.append("recurrence", recurrence), "doCreate", e)
            CompletableFuture.failedStage(e)
        }
    }

    override fun generateRecurringInstance(
        recurrence: ManualEntryRecurrence,
        dueDate: LocalDate,
        isFirstRecurrenceInstance: Boolean,
    ): ManualEntryRecurrence {
        val manualEntry = ManualEntry(
            title = recurrence.title,
            description = recurrence.description,
            amount = recurrence.amount,
            dueDate = dueDate,
            type = recurrence.type,
            status = when (recurrence.type) {
                ManualEntryType.EXTERNAL_PAYMENT,
                ManualEntryType.INCOME,
                -> ManualEntryStatus.PAID

                ManualEntryType.REMINDER,
                ManualEntryType.VEHICLE_DEBT,
                -> ManualEntryStatus.ACTIVE

                ManualEntryType.UTILITY_INVOICE -> throw IllegalStateException("Recurrence should never be UTILITY_INVOICE")
            },
            categoryId = recurrence.categoryId,
            walletId = recurrence.walletId,
            source = ActionSource.WalletRecurrence(
                recurrenceId = recurrence.id,
                accountId = recurrence.actionSource.accountId!!,
            ),
            recurrenceRule = recurrence.rule,
            createdAt = getZonedDateTime(),
            updatedAt = getZonedDateTime(),
        )

        manualEntryRepository.save(manualEntry)

        return recurrence.plusManualEntryId(manualEntry.id).setLastDueDate(dueDate)
            .also { recurrenceRepository.save(it) }
    }

    fun getThisAndFuture(manualEntry: ManualEntry): List<ManualEntryId> {
        val recurrence = manualEntryRecurrenceRepository.find(manualEntry.getRecurrenceId(), manualEntry.walletId)

        return recurrence.manualEntries.dropWhile { it != manualEntry.id }
    }

    fun updateRecurrence(manualEntry: ManualEntry) {
        val recurrence = find(manualEntry.getRecurrenceId(), manualEntry.walletId)

        val recurenceStatus = if (manualEntry.status == ManualEntryStatus.IGNORED) {
            RecurrenceStatus.IGNORED
        } else {
            RecurrenceStatus.ACTIVE
        }

        recurrenceRepository.save(
            recurrence.copy(
                title = manualEntry.title,
                description = manualEntry.description,
                amount = manualEntry.amount,
                categoryId = manualEntry.categoryId,
                status = recurenceStatus,
            ),
        )
    }
}