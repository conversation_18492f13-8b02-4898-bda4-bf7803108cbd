package ai.friday.billpayment.modules.manualentry.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.AbstractBillPaymentDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.BillCategorySuggestionDocument
import ai.friday.billpayment.adapters.dynamodb.GlobalSecondaryIndexNames
import ai.friday.billpayment.adapters.dynamodb.INDEX_1
import ai.friday.billpayment.adapters.dynamodb.INDEX_2
import ai.friday.billpayment.adapters.dynamodb.RecurrenceRuleEntity
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.ExternalBillId
import ai.friday.billpayment.app.bill.ExternalBillProvider
import ai.friday.billpayment.app.billcategory.BillCategorySuggestion
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.manualentry.ManualEntry
import ai.friday.billpayment.app.manualentry.ManualEntryId
import ai.friday.billpayment.app.manualentry.ManualEntryStatus
import ai.friday.billpayment.app.manualentry.ManualEntryType
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.manualentry.ManualEntryModule
import ai.friday.billpayment.modules.manualentry.app.integrations.ManualEntryRepository
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import java.time.LocalDate
import java.time.Month
import java.time.Year
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val SCAN_KEY_PREFIX = "MANUAL_ENTRY"

@ManualEntryModule
class ManualEntryDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<ManualEntryEntity>(cli, ManualEntryEntity::class.java)

@ManualEntryModule
class ManualEntryDbRepository(private val client: ManualEntryDynamoDAO) : ManualEntryRepository {

    private val mapper = jacksonObjectMapper()

    override fun save(manualEntry: ManualEntry): ManualEntry {
        val currentTime = getZonedDateTime()

        val manualEntryEntity = ManualEntryEntity().apply {
            partitionKey = manualEntry.id.value
            sortKey = manualEntry.walletId.value
            gSIndex1PartitionKey = manualEntry.walletId.value
            gSIndex1SortKey = "$SCAN_KEY_PREFIX#${manualEntry.dueDate}#${manualEntry.status.name}"
            gSIndex2PartitionKey = manualEntry.type.name
            gSIndex2SortKey = "${manualEntry.status.name}#${manualEntry.dueDate.format(dateFormat)}"
            amount = manualEntry.amount
            createdAt = manualEntry.createdAt.format(DateTimeFormatter.ISO_DATE_TIME)
            updatedAt = currentTime.format(DateTimeFormatter.ISO_DATE_TIME)
            markedAsPaidAt = manualEntry.markedAsPaidAt?.format(DateTimeFormatter.ISO_DATE_TIME)
            ignoredAt = manualEntry.ignoredAt?.format(DateTimeFormatter.ISO_DATE_TIME)
            title = manualEntry.title
            description = manualEntry.description
            status = manualEntry.status
            dueDate = manualEntry.dueDate.format(dateFormat)
            type = manualEntry.type
            source = mapper.writeValueAsString(manualEntry.source)
            recurrenceRule = manualEntry.recurrenceRule?.let {
                RecurrenceRuleEntity().apply {
                    frequency = it.frequency
                    startDate = it.startDate.format(dateFormat)
                    endDate = it.endDate?.format(dateFormat)
                    pattern = it.pattern
                }
            }
            markedAsPaidBy = manualEntry.markedAsPaidBy?.value
            categoryId = manualEntry.categoryId?.value
            categorySuggestions = manualEntry.categorySuggestions.map {
                BillCategorySuggestionDocument().apply {
                    this.categoryId = it.categoryId.value
                    this.probability = it.probability.toLong()
                }
            }
            externalId = manualEntry.externalId?.value
            externalIdProvider = manualEntry.externalId?.provider
        }

        client.save(manualEntryEntity)

        return manualEntry.copy(updatedAt = currentTime)
    }

    override fun findById(id: ManualEntryId, walletId: WalletId): ManualEntry {
        return client.findByPrimaryKey(id.value, walletId.value)?.toManualEntry()
            ?: throw ItemNotFoundException("ManualEntry ${id.value} not found for wallet ${walletId.value}")
    }

    override fun findById(id: ManualEntryId): ManualEntry {
        return client.findByPartitionKey(id.value).firstOrNull()?.toManualEntry()
            ?: throw ItemNotFoundException("ManualEntry ${id.value} not found")
    }

    override fun findAllByWallet(walletId: WalletId): List<ManualEntry> {
        return client.findBeginsWithOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            walletId.value,
            SCAN_KEY_PREFIX,
        ).map { it.toManualEntry() }
    }

    override fun findByWalletAndDueDateMonth(walletId: WalletId, year: Year, month: Month): List<ManualEntry> {
        return client.findBeginsWithOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            walletId.value,
            "$SCAN_KEY_PREFIX#${year.value}-${month.value.toString().padStart(2, '0')}",
        ).map { it.toManualEntry() }
    }

    override fun findByWalletAndDueDate(walletId: WalletId, date: LocalDate): List<ManualEntry> {
        return client.findBeginsWithOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            walletId.value,
            "$SCAN_KEY_PREFIX#${date.format(dateFormat)}",
        ).map { it.toManualEntry() }
    }

    override fun findByTypeStatusAndDueDate(type: ManualEntryType, status: ManualEntryStatus, dueDate: LocalDate): List<ManualEntry> {
        return client.findByPartitionKeyAndScanKeyOnIndex(
            GlobalSecondaryIndexNames.GSIndex2,
            type.name,
            "${status.name}#${dueDate.format(dateFormat)}",
        ).map { it.toManualEntry() }
    }

    override fun delete(manualEntry: ManualEntry) {
        save(manualEntry.copy(status = ManualEntryStatus.IGNORED, ignoredAt = getZonedDateTime()))
    }

    private fun ManualEntryEntity.toManualEntry(): ManualEntry {
        val actionSource: ActionSource = mapper.readerFor(ActionSource::class.java).readValue(this.source)

        val externalId = if (this.externalId != null && this.externalIdProvider != null) {
            ExternalBillId(this.externalId!!, this.externalIdProvider!!)
        } else {
            null
        }

        return ManualEntry(
            id = ManualEntryId(this.partitionKey),
            walletId = WalletId(this.sortKey),
            title = this.title,
            description = this.description,
            amount = this.amount,
            status = this.status,
            type = this.type,
            createdAt = ZonedDateTime.parse(this.createdAt, DateTimeFormatter.ISO_DATE_TIME),
            updatedAt = ZonedDateTime.parse(this.updatedAt, DateTimeFormatter.ISO_DATE_TIME),
            markedAsPaidAt = this.markedAsPaidAt?.let { ZonedDateTime.parse(it, DateTimeFormatter.ISO_DATE_TIME) },
            ignoredAt = this.ignoredAt?.let { ZonedDateTime.parse(it, DateTimeFormatter.ISO_DATE_TIME) },
            dueDate = LocalDate.parse(this.dueDate, dateFormat),
            recurrenceRule = this.recurrenceRule?.let {
                RecurrenceRule(
                    frequency = it.frequency,
                    startDate = LocalDate.parse(it.startDate, dateFormat),
                    pattern = it.pattern.orEmpty(),
                    endDate = it.endDate?.let { endDate -> LocalDate.parse(endDate, dateFormat) },
                )
            },
            source = actionSource,
            markedAsPaidBy = this.markedAsPaidBy?.let { AccountId(it) },
            categoryId = this.categoryId?.let { PFMCategoryId(value = it) },
            categorySuggestions = this.categorySuggestions.map {
                BillCategorySuggestion(categoryId = PFMCategoryId(it.categoryId), probability = it.probability.toInt())
            },
            externalId = externalId,
        )
    }
}

@DynamoDbBean
class ManualEntryEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1SortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    lateinit var gSIndex2PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    lateinit var gSIndex2SortKey: String

    @get:DynamoDbAttribute(value = "Title")
    lateinit var title: String

    @get:DynamoDbAttribute(value = "Description")
    var description: String = ""

    @get:DynamoDbAttribute(value = "DueDate")
    lateinit var dueDate: String

    @get:DynamoDbAttribute(value = "Amount")
    var amount: Long = 0

    @get:DynamoDbAttribute(value = "Type")
    lateinit var type: ManualEntryType

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: ManualEntryStatus

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute(value = "UpdatedAt")
    lateinit var updatedAt: String

    @get:DynamoDbAttribute(value = "MarkedAsPaidAt")
    var markedAsPaidAt: String? = null

    @get:DynamoDbAttribute(value = "IgnoredAt")
    var ignoredAt: String? = null

    @get:DynamoDbAttribute(value = "Source")
    lateinit var source: String

    @get:DynamoDbAttribute(value = "RecurrenceRule")
    var recurrenceRule: RecurrenceRuleEntity? = null

    @get:DynamoDbAttribute(value = "MarkedAsPaidBy")
    var markedAsPaidBy: String? = null

    @get:DynamoDbAttribute(value = "CategoryId")
    var categoryId: String? = null

    @get:DynamoDbAttribute(value = "CategorySuggestions")
    var categorySuggestions: List<BillCategorySuggestionDocument> = listOf()

    @get:DynamoDbAttribute(value = "ExternalId")
    var externalId: String? = null

    @get:DynamoDbAttribute(value = "ExternalIdProvider")
    var externalIdProvider: ExternalBillProvider? = null
}