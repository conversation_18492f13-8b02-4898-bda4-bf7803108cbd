package ai.friday.billpayment.modules.manualentry.app

import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.ExternalBillId
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.manualentry.ExpiredReminderTO
import ai.friday.billpayment.app.manualentry.ManualEntry
import ai.friday.billpayment.app.manualentry.ManualEntryError
import ai.friday.billpayment.app.manualentry.ManualEntryId
import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.app.manualentry.ManualEntryStatus
import ai.friday.billpayment.app.manualentry.ManualEntryType
import ai.friday.billpayment.app.manualentry.ReminderTO
import ai.friday.billpayment.app.manualentry.UpdateManualEntryRequest
import ai.friday.billpayment.app.manualentry.canView
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.recurrence.Range
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.modules.manualentry.ManualEntryModule
import ai.friday.billpayment.modules.manualentry.app.integrations.ManualEntryNotificationService
import ai.friday.billpayment.modules.manualentry.app.integrations.ManualEntryRepository
import ai.friday.billpayment.modules.manualentry.app.recurrence.ManualEntryRecurrenceService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Property
import java.time.LocalDate
import java.time.Month
import java.time.Year
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@ManualEntryModule
open class ManualEntryServiceImpl(
    private val manualEntryRepository: ManualEntryRepository,
    private val manualEntryRecurrenceService: ManualEntryRecurrenceService,
    private val walletBillCategoryService: PFMWalletCategoryService,
    private val walletService: WalletService,
    private val notificationAdapter: ManualEntryNotificationService,
    private val messagePublisher: MessagePublisher,
    @Property(name = "sqs.notifyReminderQueueName") private val notifyReminderQueueName: String,
    @Property(name = "sqs.notifyExpiredReminderQueueName") private val notifyExpiredReminderQueueName: String,
) : ManualEntryService {

    private val logger = LoggerFactory.getLogger(ManualEntryServiceImpl::class.java)

    override fun find(id: ManualEntryId, walletId: WalletId): Either<ManualEntryError, ManualEntry> {
        return try {
            manualEntryRepository.findById(id, walletId).right()
        } catch (e: ItemNotFoundException) {
            ManualEntryError.ManualEntryNotFound.left()
        } catch (t: Throwable) {
            ManualEntryError.UnknownError.left()
        }
    }

    override fun find(id: ManualEntryId): Either<ManualEntryError, ManualEntry> {
        return try {
            manualEntryRepository.findById(id).right()
        } catch (e: ItemNotFoundException) {
            ManualEntryError.ManualEntryNotFound.left()
        } catch (t: Throwable) {
            ManualEntryError.UnknownError.left()
        }
    }

    override fun findAllByWalletAndDueDateMonth(walletId: WalletId, year: Year, month: Month) = manualEntryRepository.findByWalletAndDueDateMonth(walletId, year, month)

    override fun findAllByWalletAndDueDate(walletId: WalletId, date: LocalDate) = manualEntryRepository.findByWalletAndDueDate(walletId, date)

    override fun create(
        walletId: WalletId,
        title: String,
        description: String,
        amount: Long,
        dueDate: LocalDate,
        source: ActionSource,
        type: ManualEntryType,
        status: ManualEntryStatus,
        categoryId: PFMCategoryId?,
        externalId: ExternalBillId?,
    ): Either<ManualEntryError, ManualEntry> {
        categoryId?.let {
            if (walletBillCategoryService.findCategory(walletId, it) == null) {
                return ManualEntryError.CategoryNotFound.left()
            }
        }

        val manualEntry = ManualEntry(
            walletId = walletId,
            title = title,
            description = description,
            amount = amount,
            dueDate = dueDate,
            source = source,
            type = type,
            status = status,
            categoryId = categoryId,
            createdAt = getZonedDateTime(),
            updatedAt = getZonedDateTime(),
            externalId = externalId,
        )

        manualEntryRepository.save(manualEntry)

        return manualEntry.right()
    }

    override fun update(request: UpdateManualEntryRequest, range: Range): Either<ManualEntryError, Unit> {
        val manualEntry = find(request.manualEntryId, request.walletId).getOrElse {
            return it.left()
        }

        applyUpdate(manualEntry, range) { entry ->
            manualEntryRepository.save(
                entry.copy(
                    title = request.title?.takeIf { it.isNotBlank() } ?: manualEntry.title,
                    description = request.description ?: manualEntry.description,
                    amount = request.amount ?: manualEntry.amount,
                ),
            )
        }

        return Unit.right()
    }

    override fun setCategory(manualEntryId: ManualEntryId, walletId: WalletId, categoryId: PFMCategoryId?, range: Range): Either<ManualEntryError, ManualEntry> {
        val manualEntry = find(manualEntryId, walletId).getOrElse {
            return it.left()
        }

        if (categoryId != null && walletBillCategoryService.findCategory(walletId, categoryId) == null) {
            return ManualEntryError.CategoryNotFound.left()
        }

        val updated = applyUpdate(manualEntry, range) { entry ->
            manualEntryRepository.save(entry.copy(categoryId = categoryId))
        }

        return updated.first { e -> e.id == manualEntryId }.right()
    }

    override fun listAll(walletId: WalletId): List<ManualEntry> {
        return manualEntryRepository.findAllByWallet(walletId)
    }

    @Trace
    override fun listAllForWalletMember(walletId: WalletId, member: Member): List<ManualEntry> {
        return listAll(walletId).filter { member.canView(it) }
    }

    override fun ignore(manualEntryId: ManualEntryId, walletId: WalletId, range: Range): Either<ManualEntryError, Unit> {
        val manualEntry = find(manualEntryId, walletId).getOrElse {
            return it.left()
        }

        applyUpdate(manualEntry, range) { entry ->
            manualEntryRepository.save(entry.copy(status = ManualEntryStatus.IGNORED, ignoredAt = getZonedDateTime()))
        }

        return Unit.right()
    }

    override fun restoreIgnored(manualEntryId: ManualEntryId, walletId: WalletId): Either<ManualEntryError, ManualEntry> {
        val manualEntry = find(manualEntryId, walletId).getOrElse {
            return it.left()
        }

        if (manualEntry.status != ManualEntryStatus.IGNORED) {
            return ManualEntryError.InvalidStatus.left()
        }

        val newStatus = when (manualEntry.type) {
            ManualEntryType.EXTERNAL_PAYMENT,
            ManualEntryType.UTILITY_INVOICE,
            ManualEntryType.INCOME,
            -> ManualEntryStatus.PAID

            ManualEntryType.REMINDER,
            ManualEntryType.VEHICLE_DEBT,
            -> ManualEntryStatus.ACTIVE
        }

        return manualEntryRepository.save(manualEntry.copy(status = newStatus, ignoredAt = null)).right()
    }

    override fun markAsPaid(manualEntryId: ManualEntryId, walletId: WalletId): Either<ManualEntryError, Unit> {
        val manualEntry = find(manualEntryId, walletId).getOrElse {
            return it.left()
        }

        if (manualEntry.type == ManualEntryType.EXTERNAL_PAYMENT) {
            return ManualEntryError.InvalidStatus.left()
        }
        manualEntryRepository.save(
            manualEntry.copy(
                status = ManualEntryStatus.PAID,
                markedAsPaidAt = getZonedDateTime(),
            ),
        )

        return Unit.right()
    }

    override fun markAsPaid(manualEntryId: ManualEntryId): Either<ManualEntryError, Unit> {
        val manualEntry = find(manualEntryId).getOrElse {
            return it.left()
        }

        markAsPaid(manualEntry.id, manualEntry.walletId).getOrElse {
            return it.left()
        }

        return Unit.right()
    }

    override fun unmarkAsPaid(manualEntryId: ManualEntryId, walletId: WalletId): Either<ManualEntryError, Unit> {
        val manualEntry = find(manualEntryId, walletId).getOrElse {
            return it.left()
        }

        if (manualEntry.type == ManualEntryType.EXTERNAL_PAYMENT) {
            return ManualEntryError.InvalidStatus.left()
        }
        manualEntryRepository.save(manualEntry.copy(status = ManualEntryStatus.ACTIVE, markedAsPaidAt = null))

        return Unit.right()
    }

    private fun applyUpdate(manualEntry: ManualEntry, range: Range, operation: (ManualEntry) -> ManualEntry): List<ManualEntry> {
        when (range) {
            Range.THIS -> {
                return listOf(operation(manualEntry))
            }

            Range.THIS_AND_FUTURE -> {
                val walletId = manualEntry.walletId

                val updatedEntries = manualEntryRecurrenceService.getThisAndFuture(manualEntry).mapNotNull { id ->
                    find(id, walletId).fold(
                        ifLeft = {
                            logger.warn(
                                Markers.append("recurrenceId", manualEntry.getRecurrenceId())
                                    .andAppend("manualEntryId", id.value),
                                "manualEntryNotFoundFromRecurrence",
                            )

                            null
                        },
                        ifRight = { operation(it) },
                    )
                }

                manualEntryRecurrenceService.updateRecurrence(updatedEntries.last())
                return updatedEntries
            }
        }
    }

    override fun notifyRemindersDueToday(): Int {
        val reminders = manualEntryRepository.findByTypeStatusAndDueDate(ManualEntryType.REMINDER, ManualEntryStatus.ACTIVE, getLocalDate())

        reminders.map {
            ReminderTO(
                id = it.id,
                walletId = it.walletId,
            )
        }.forEach {
            messagePublisher.sendMessage(notifyReminderQueueName, it)
        }

        return reminders.size
    }

    override fun notifyRemindersExpired(): Int {
        val reminders = manualEntryRepository.findByTypeStatusAndDueDate(ManualEntryType.REMINDER, ManualEntryStatus.ACTIVE, getLocalDate().minusDays(1))

        val walletIds = reminders.groupBy {
            it.walletId.value
        }

        walletIds.forEach {
            val manualEntryIds = it.value.map { entry -> entry.id.value }
            messagePublisher.sendMessage(notifyExpiredReminderQueueName, ExpiredReminderTO(WalletId(it.key), manualEntryIds))
        }

        return walletIds.size
    }

    override fun notifyReminder(reminderTO: ReminderTO): Either<ManualEntryError, Unit> {
        val markers = Markers.append("reminderId", reminderTO.id.value).andAppend("walletId", reminderTO.walletId.value)

        val reminder = find(reminderTO.id, reminderTO.walletId).getOrElse {
            logger.error(markers.andAppend("error", it), "ManualEntryService#notifyReminder")
            return it.left()
        }

        val wallet = walletService.findWallet(reminderTO.walletId)

        val membersToNotify = wallet.activeMembers.filter { it.canView(reminder) }

        notificationAdapter.notifyReminder(membersToNotify, wallet, reminder)
        logger.info(markers, "ManualEntryService#notifyReminder")

        return Unit.right()
    }

    override fun notifyExpiredReminderWallet(walletId: WalletId, entries: List<String>): Either<ManualEntryError, Unit> {
        val markers = Markers.append("walletId", walletId.value)

        val wallet = walletService.findWallet(walletId)

        notificationAdapter.notifyExpiredReminderWallet(wallet.activeMembers, wallet, entries)
        logger.info(markers, "ManualEntryService#notifyExpiredReminderWallet")

        return Unit.right()
    }
}