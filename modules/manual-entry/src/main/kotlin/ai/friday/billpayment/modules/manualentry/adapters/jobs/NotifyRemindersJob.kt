package ai.friday.billpayment.modules.manualentry.adapters.jobs

import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.modules.manualentry.ManualEntryModule
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@ManualEntryModule
open class NotifyRemindersJob(
    private val service: ManualEntryService,
) : AbstractJob(
    cron = "0 13,19 * * *",
    lockAtLeastFor = "30m",
) {
    private val logger = LoggerFactory.getLogger(NotifyRemindersJob::class.java)
    override fun execute() {
        val notified = service.notifyRemindersDueToday()

        logger.info(Markers.append("numberOfRemindersNotified", notified), "NotifyRemindersJob")
    }
}