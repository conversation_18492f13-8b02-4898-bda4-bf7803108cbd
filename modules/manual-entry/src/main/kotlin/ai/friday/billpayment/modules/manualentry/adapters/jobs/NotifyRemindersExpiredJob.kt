package ai.friday.billpayment.modules.manualentry.adapters.jobs

import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.modules.manualentry.ManualEntryModule
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@ManualEntryModule
open class NotifyRemindersExpiredJob(
    private val service: ManualEntryService,
) : AbstractJob(
    cron = "30 12 * * *",
    lockAtLeastFor = "30m",
) {
    private val logger = LoggerFactory.getLogger(NotifyRemindersExpiredJob::class.java)
    override fun execute() {
        val notified = service.notifyRemindersExpired()

        logger.info(Markers.append("numberOfRemindersExpiredNotified", notified), "NotifyRemindersExpiredJob")
    }
}