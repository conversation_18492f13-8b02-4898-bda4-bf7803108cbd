package ai.friday.billpayment.modules.manualentry.adapters.messaging

import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.manualentry.ManualEntryError
import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.app.manualentry.ReminderTO
import ai.friday.billpayment.modules.manualentry.ManualEntryModuleNoTest
import ai.friday.morning.log.andAppend
import io.micronaut.http.annotation.Trace
import io.micronaut.tracing.annotation.NewSpan
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@ManualEntryModuleNoTest
open class SQSReminderNotificationHandler(
    sqsClient: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val manualEntryService: ManualEntryService,
) :
    AbstractSQSHandler(
        amazonSQS = sqsClient,
        configuration = configuration,
        queueName = configuration.notifyReminderQueueName,
        consumers = 1,
    ) {

    private val logger = LoggerFactory.getLogger(SQSReminderNotificationHandler::class.java)

    @Trace
    @NewSpan
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val markers = Markers.append("queue_message", message)

        val reminder = parseObjectFrom<ReminderTO>(message.body())

        manualEntryService.notifyReminder(reminder).mapLeft {
            logger.info(markers.andAppend("error", it), "SQSReminderNotificationHandler#handleMessage")

            return when (it) {
                ManualEntryError.ManualEntryNotFound -> SQSHandlerResponse(true)
                else -> SQSHandlerResponse(false)
            }
        }

        logger.info(markers, "SQSReminderNotificationHandler#handleMessage")
        return SQSHandlerResponse(true)
    }

    override fun handleError(message: Message, e: Exception): SQSHandlerResponse {
        logger.error(Markers.append("event", message), "SQSReminderNotificationHandler#handleError", e)
        return SQSHandlerResponse(false)
    }
}