package ai.friday.billpayment.modules.manualentry.app.category

import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.app.manualentry.ManualEntryStatus
import ai.friday.billpayment.app.manualentry.ManualEntryType
import ai.friday.billpayment.app.manualentry.canView
import ai.friday.billpayment.app.pfm.SummaryEntry
import ai.friday.billpayment.app.pfm.SummaryEntryType
import ai.friday.billpayment.app.pfm.SummaryService
import ai.friday.billpayment.app.pfm.WalletBillCategoryRepository
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.manualentry.ManualEntryModule
import java.time.Month
import java.time.Year

@ManualEntryModule
class ManualEntrySummaryService(private val manualEntryService: ManualEntryService, private val walletBillCategoryDbRepository: WalletBillCategoryRepository) : SummaryService {
    override fun generateSummaryList(walletId: WalletId, walletMember: Member, year: Year, month: Month): List<SummaryEntry> {
        return manualEntryService.findAllByWalletAndDueDateMonth(walletId, year, month)
            .filter { it.status != ManualEntryStatus.IGNORED }
            .filter { walletMember.canView(it) }
            .map {
                val category = it.categoryId?.let { categoryId -> walletBillCategoryDbRepository.findByWalletIdAndBillCategoryId(walletId, categoryId) }
                SummaryEntry(
                    category = category?.toBillCategory(),
                    totalAmount = it.amount,
                    title = it.title,
                    description = it.description,
                    date = it.dueDate,
                    type = when (it.type) {
                        ManualEntryType.EXTERNAL_PAYMENT -> SummaryEntryType.PAYMENT
                        ManualEntryType.UTILITY_INVOICE -> SummaryEntryType.PAYMENT
                        ManualEntryType.INCOME -> SummaryEntryType.INCOME
                        ManualEntryType.REMINDER -> {
                            if (it.status == ManualEntryStatus.PAID && it.amount > 0) {
                                SummaryEntryType.PAYMENT
                            } else {
                                SummaryEntryType.REMINDER
                            }
                        }

                        ManualEntryType.VEHICLE_DEBT,
                        -> SummaryEntryType.REMINDER
                    },
                    entryType = when (it.type) {
                        ManualEntryType.EXTERNAL_PAYMENT -> "Lançamento manual"
                        ManualEntryType.REMINDER -> "Lembrete"
                        ManualEntryType.UTILITY_INVOICE -> "Conta de consumo"
                        ManualEntryType.INCOME -> "Renda"
                        ManualEntryType.VEHICLE_DEBT,
                        -> "Débito veicular"
                    },
                )
            }
    }
}