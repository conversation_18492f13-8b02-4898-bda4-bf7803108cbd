package ai.friday.billpayment.modules.manualentry.adapters.api

import ai.friday.billpayment.adapters.api.RecurrenceRequestTO
import ai.friday.billpayment.adapters.api.StandardHttpResponses
import ai.friday.billpayment.adapters.api.buildTO
import ai.friday.billpayment.adapters.api.getActionSource
import ai.friday.billpayment.adapters.api.toWalletId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.manualentry.ManualEntry
import ai.friday.billpayment.app.manualentry.ManualEntryError
import ai.friday.billpayment.app.manualentry.ManualEntryId
import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.app.manualentry.ManualEntryStatus
import ai.friday.billpayment.app.manualentry.ManualEntryType
import ai.friday.billpayment.app.manualentry.UpdateManualEntryRequest
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.recurrence.Range
import ai.friday.billpayment.app.recurrence.RecurrenceCreationError
import ai.friday.billpayment.app.recurrence.RecurrenceResult
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.recurrence.RecurrenceStatus
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.manualentry.ManualEntryModule
import ai.friday.billpayment.modules.manualentry.app.recurrence.ManualEntryRecurrence
import ai.friday.billpayment.modules.manualentry.app.recurrence.ManualEntryRecurrenceService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.http.annotation.QueryValue
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.validation.Validated
import java.time.LocalDate
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Validated
@Secured(Role.Code.OWNER)
@Controller("/manualEntry")
@Version("2")
@ManualEntryModule
class ManualEntryController(
    private val manualEntryService: ManualEntryService,
    private val manualEntryRecurrenceService: ManualEntryRecurrenceService,
    private val walletBillCategoryService: PFMWalletCategoryService,
) {

    private val logger = LoggerFactory.getLogger(ManualEntryController::class.java)

    @Post("/externalPayment")
    fun createExternalPayment(
        @Body request: CreateExternalPaymentTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        val logName = "ManualEntryController#createExternalPayment"

        val walletId = authentication.toWalletId()

        val markers = Markers.append("accountId", authentication.toAccountId().value)
            .andAppend("walletId", walletId.value)
            .andAppend("request", request)

        val dueDate = try {
            LocalDate.parse(request.dueDate, dateFormat)
        } catch (e: Exception) {
            logger.warn(markers, logName, e)
            return HttpResponse.badRequest<Unit>()
        }

        return if (request.recurrence == null) {
            handleCreateResponse(
                manualEntryService.create(
                    walletId = walletId,
                    title = request.title,
                    description = request.description ?: "",
                    amount = request.amount,
                    dueDate = dueDate,
                    source = authentication.getActionSource(),
                    type = ManualEntryType.EXTERNAL_PAYMENT,
                    status = ManualEntryStatus.PAID,
                    categoryId = request.categoryId?.let { PFMCategoryId(it) },
                ),
                markers,
                logName,
            )
        } else {
            handleRecurrenceResponse(
                manualEntryRecurrenceService.create(request.toRecurrence(authentication), dryRun = false),
                markers,
                logName,
                StandardHttpResponses.created(),
            )
        }
    }

    @Post("/reminder")
    fun createReminder(
        @Body request: CreateReminderTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        val logName = "ManualEntryController#createReminder"

        val walletId = authentication.toWalletId()

        val markers = Markers.append("accountId", authentication.toAccountId().value)
            .andAppend("walletId", walletId.value)
            .andAppend("request", request)

        val dueDate = try {
            LocalDate.parse(request.dueDate, dateFormat)
        } catch (e: Exception) {
            logger.warn(markers, logName, e)
            return HttpResponse.badRequest<Unit>()
        }

        return if (request.recurrence == null) {
            handleCreateResponse(
                manualEntryService.create(
                    walletId = walletId,
                    title = request.title,
                    description = request.description ?: "",
                    amount = request.amount ?: 0L,
                    dueDate = dueDate,
                    source = authentication.getActionSource(),
                    type = ManualEntryType.REMINDER,
                    status = ManualEntryStatus.ACTIVE,
                    categoryId = request.categoryId?.let { PFMCategoryId(it) },
                ),
                markers,
                logName,
            )
        } else {
            handleRecurrenceResponse(
                manualEntryRecurrenceService.create(request.toRecurrence(authentication), dryRun = false),
                markers,
                logName,
                StandardHttpResponses.created(),
            )
        }
    }

    @Post("/income")
    fun createIncome(
        @Body request: CreateIncomeTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        val logName = "ManualEntryController#createIncome"

        val walletId = authentication.toWalletId()

        val markers = Markers.append("accountId", authentication.toAccountId().value)
            .andAppend("walletId", walletId.value)
            .andAppend("request", request)

        val dueDate = try {
            LocalDate.parse(request.dueDate, dateFormat)
        } catch (e: Exception) {
            logger.warn(markers, logName, e)
            return HttpResponse.badRequest<Unit>()
        }

        return if (request.recurrence == null) {
            handleCreateResponse(
                manualEntryService.create(
                    walletId = walletId,
                    title = request.title,
                    description = request.description ?: "",
                    amount = request.amount,
                    dueDate = dueDate,
                    source = authentication.getActionSource(),
                    type = ManualEntryType.INCOME,
                    status = ManualEntryStatus.PAID,
                    categoryId = request.categoryId?.let { PFMCategoryId(it) },
                ),
                markers,
                logName,
            )
        } else {
            handleRecurrenceResponse(
                manualEntryRecurrenceService.create(request.toRecurrence(authentication), dryRun = false),
                markers,
                logName,
                StandardHttpResponses.created(),
            )
        }
    }

    @Put("/id/{manualEntryId}")
    fun update(
        @PathVariable manualEntryId: String,
        @Body request: UpdateManualEntryTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        val logName = "ManualEntryController#update"

        val walletId = authentication.toWalletId()

        val markers = Markers.append("accountId", authentication.toAccountId().value)
            .andAppend("walletId", walletId.value)
            .andAppend("request", request)

        return handleResponse(
            manualEntryService.update(request.toUpdateRequest(ManualEntryId(manualEntryId), walletId), request.toRange()),
            markers,
            logName,
        )
    }

    @Put("/id/{manualEntryId}/category/{categoryId}")
    fun setCategory(
        @PathVariable manualEntryId: String,
        @PathVariable categoryId: String,
        @Body request: CategoryRequestTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        val logName = "ManualEntryController#setCategory"

        val walletId = authentication.toWalletId()

        val markers = Markers.append("walletId", walletId.value)
            .andAppend("manualEntryId", manualEntryId)
            .andAppend("categoryId", categoryId)
            .andAppend("request", request)

        val categories = walletBillCategoryService.findWalletCategories(walletId)
        return handleSetCategoryResponse(
            manualEntryService.setCategory(ManualEntryId(manualEntryId), walletId, PFMCategoryId(categoryId), request.toRange()),
            categories,
            markers,
            logName,
        )
    }

    @Delete("/id/{manualEntryId}/category")
    fun removeCategory(
        @PathVariable manualEntryId: String,
        @Body request: CategoryRequestTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        val logName = "ManualEntryController#removeCategory"

        val walletId = authentication.toWalletId()

        val markers = Markers.append("walletId", walletId.value)
            .andAppend("manualEntryId", manualEntryId)
            .andAppend("request", request)

        val categories = walletBillCategoryService.findWalletCategories(walletId)
        return handleSetCategoryResponse(
            manualEntryService.setCategory(ManualEntryId(manualEntryId), walletId, null, request.toRange()),
            categories,
            markers,
            logName,
        )
    }

    @Put("/id/{manualEntryId}/ignore")
    fun ignore(
        @PathVariable manualEntryId: String,
        @QueryValue range: Range? = Range.THIS,
        authentication: Authentication,
    ): HttpResponse<*> {
        val logName = "ManualEntryController#ignore"

        val walletId = authentication.toWalletId()

        val markers = Markers.append("walletId", walletId.value)
            .andAppend("manualEntryId", manualEntryId)

        return handleResponse(
            manualEntryService.ignore(ManualEntryId(manualEntryId), walletId, range ?: Range.THIS),
            markers,
            logName,
        )
    }

    @Put("/id/{manualEntryId}/reactivate")
    fun activate(
        @PathVariable manualEntryId: String,
        authentication: Authentication,
    ): HttpResponse<*> {
        val logName = "ManualEntryController#ignore"

        val walletId = authentication.toWalletId()

        val markers = Markers.append("walletId", walletId.value)
            .andAppend("manualEntryId", manualEntryId)

        val result = manualEntryService.restoreIgnored(ManualEntryId(manualEntryId), walletId)

        val manualEntry = result.getOrElse {
            logger.error(markers.andAppend("error", it), logName)
            return getErrorResponse(it)
        }

        val responseTO = manualEntry.buildTO(walletBillCategoryService.findWalletCategories(walletId))

        logger.info(markers.andAppend("response", responseTO), logName)

        return HttpResponse.ok(responseTO)
    }

    @Put("/id/{manualEntryId}/mark-as-paid")
    fun markReminderAsPaid(
        @PathVariable manualEntryId: String,
        authentication: Authentication,
    ): HttpResponse<*> {
        val logName = "ManualEntryController#markReminderAsPaid"

        val walletId = authentication.toWalletId()

        val markers = Markers.append("walletId", walletId.value)
            .andAppend("manualEntryId", manualEntryId)

        return handleResponse(
            manualEntryService.markAsPaid(ManualEntryId(manualEntryId), walletId),
            markers,
            logName,
        )
    }

    @Put("/id/{manualEntryId}/cancel-marked-as-paid")
    fun cancelMarkReminderAsPaid(
        @PathVariable manualEntryId: String,
        authentication: Authentication,
    ): HttpResponse<*> {
        val logName = "ManualEntryController#cancelMarkReminderAsPaid"

        val walletId = authentication.toWalletId()

        val markers = Markers.append("walletId", walletId.value)
            .andAppend("manualEntryId", manualEntryId)

        return handleResponse(
            manualEntryService.unmarkAsPaid(ManualEntryId(manualEntryId), walletId),
            markers,
            logName,
        )
    }

    private fun handleCreateResponse(
        result: Either<ManualEntryError, ManualEntry>,
        markers: LogstashMarker,
        logName: String,
    ): HttpResponse<*> {
        val manualEntry = result.getOrElse {
            logger.error(markers.andAppend("error", it), logName)
            return getErrorResponse(it)
        }

        logger.info(markers.andAppend("manualEntry", manualEntry), logName)
        return StandardHttpResponses.created()
    }

    private fun handleResponse(result: Either<ManualEntryError, Unit>, markers: LogstashMarker, logName: String): HttpResponse<*> {
        result.getOrElse {
            logger.error(markers.andAppend("error", it), logName)
            return getErrorResponse(it)
        }

        logger.info(markers, logName)
        return HttpResponse.ok<Unit>()
    }

    private fun handleSetCategoryResponse(result: Either<ManualEntryError, ManualEntry>, categories: List<WalletBillCategory>, markers: LogstashMarker, logName: String): HttpResponse<*> {
        val entry = result.getOrElse {
            logger.error(markers.andAppend("error", it), logName)
            return getErrorResponse(it)
        }

        logger.info(markers, logName)

        return HttpResponse.ok(entry.buildTO(categories))
    }

    private fun handleRecurrenceResponse(
        result: Either<RecurrenceCreationError, RecurrenceResult<ManualEntryRecurrence>>,
        markers: LogstashMarker,
        logName: String,
        successResponse: HttpResponse<*> = HttpResponse.ok<Unit>(),
    ): HttpResponse<*> {
        val manualEntryRecurrence = result.getOrElse {
            logger.error(markers.andAppend("error", it), logName)
            return getErrorResponse(it)
        }

        logger.info(markers.andAppend("manualEntryRecurrence", manualEntryRecurrence), logName)
        return successResponse
    }

    private fun getErrorResponse(error: ManualEntryError): HttpResponse<*> {
        return when (error) {
            ManualEntryError.CategoryNotFound -> StandardHttpResponses.badRequest("001", "category not found")
            ManualEntryError.ManualEntryNotFound -> StandardHttpResponses.notFound("manual entry not found")
            ManualEntryError.InvalidStatus -> StandardHttpResponses.badRequest("004", "invalid status")
            ManualEntryError.UnknownError -> StandardHttpResponses.serverError("unknown error")
        }
    }

    private fun getErrorResponse(error: RecurrenceCreationError): HttpResponse<*> {
        return when (error) {
            RecurrenceCreationError.DueDateAfterLimit -> StandardHttpResponses.badRequest("002", "due date after limit")
            RecurrenceCreationError.DueDateInThePast -> StandardHttpResponses.badRequest("003", "start date in the past")
            is RecurrenceCreationError.ServerError -> StandardHttpResponses.serverError("unknown error creating recurring manual entry")
        }
    }

    data class CreateExternalPaymentTO(
        val title: String,
        val description: String? = null,
        val dueDate: String,
        val amount: Long,
        val recurrence: RecurrenceRequestTO? = null,
        val categoryId: String? = null,
    ) {
        fun toRecurrence(authentication: Authentication): ManualEntryRecurrence {
            return ManualEntryRecurrence(
                type = ManualEntryType.EXTERNAL_PAYMENT,
                title = title,
                description = description ?: "",
                amount = amount,
                categoryId = categoryId?.let { PFMCategoryId(it) },
                walletId = authentication.toWalletId(),
                actionSource = authentication.getActionSource(),
                status = RecurrenceStatus.ACTIVE,
                rule = RecurrenceRule(
                    frequency = recurrence!!.frequency,
                    startDate = LocalDate.parse(dueDate, dateFormat),
                    pattern = recurrence.pattern.orEmpty(),
                    endDate = recurrence.endDate?.let { endDate -> LocalDate.parse(endDate, dateFormat) },
                ),
                created = getZonedDateTime(),
            )
        }
    }

    data class CreateReminderTO(
        val title: String,
        val description: String? = null,
        val dueDate: String,
        val amount: Long? = null,
        val recurrence: RecurrenceRequestTO? = null,
        val categoryId: String? = null,
    ) {
        fun toRecurrence(authentication: Authentication): ManualEntryRecurrence {
            return ManualEntryRecurrence(
                type = ManualEntryType.REMINDER,
                title = title,
                description = description ?: "",
                amount = amount ?: 0L,
                categoryId = categoryId?.let { PFMCategoryId(it) },
                walletId = authentication.toWalletId(),
                actionSource = authentication.getActionSource(),
                status = RecurrenceStatus.ACTIVE,
                rule = RecurrenceRule(
                    frequency = recurrence!!.frequency,
                    startDate = LocalDate.parse(dueDate, dateFormat),
                    pattern = recurrence.pattern.orEmpty(),
                    endDate = recurrence.endDate?.let { endDate -> LocalDate.parse(endDate, dateFormat) },
                ),
                created = getZonedDateTime(),
            )
        }
    }

    data class CreateIncomeTO(
        val title: String,
        val description: String? = null,
        val dueDate: String,
        val amount: Long,
        val recurrence: RecurrenceRequestTO? = null,
        val categoryId: String? = null,
    ) {
        fun toRecurrence(authentication: Authentication): ManualEntryRecurrence {
            return ManualEntryRecurrence(
                type = ManualEntryType.INCOME,
                title = title,
                description = description ?: "",
                amount = amount,
                categoryId = categoryId?.let { PFMCategoryId(it) },
                walletId = authentication.toWalletId(),
                actionSource = authentication.getActionSource(),
                status = RecurrenceStatus.ACTIVE,
                rule = RecurrenceRule(
                    frequency = recurrence!!.frequency,
                    startDate = LocalDate.parse(dueDate, dateFormat),
                    pattern = recurrence.pattern.orEmpty(),
                    endDate = recurrence.endDate?.let { endDate -> LocalDate.parse(endDate, dateFormat) },
                ),
                created = getZonedDateTime(),
            )
        }
    }

    data class UpdateManualEntryTO(
        val title: String? = null,
        val description: String? = null,
        val amount: Long? = null,
        val range: String? = null,
    ) {
        fun toUpdateRequest(manualEntryId: ManualEntryId, walletId: WalletId): UpdateManualEntryRequest {
            return UpdateManualEntryRequest(
                manualEntryId = manualEntryId,
                walletId = walletId,
                title = title,
                description = description,
                amount = amount,
            )
        }

        fun toRange() = range?.let { Range.valueOf(it) } ?: Range.THIS
    }

    data class CategoryRequestTO(val recurrenceRange: String?) {
        fun toRange() = recurrenceRange?.let { Range.valueOf(it) } ?: Range.THIS
    }
}