package ai.friday.billpayment.modules.manualentry.adapters.jobs

import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.modules.manualentry.ManualEntryModule
import ai.friday.billpayment.modules.manualentry.app.recurrence.ManualEntryRecurrenceService
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@ManualEntryModule
open class ContinueManualEntryRecurrencesJob(
    private val manualEntryRecurrenceService: ManualEntryRecurrenceService,
) : AbstractJob(
    cron = "0 17 15 * *",
    lockAtLeastFor = "10m",
    lockAtMostFor = null,
    shutdownGracefully = false,
    shutdownGracefullyMaxWaitTime = 0,
) {
    override fun execute() {
        LOG.info(Markers.append("step", "begin"), "ManualEntryRecurrenceService")
        manualEntryRecurrenceService.continueRecurrences()
        LOG.info(Markers.append("step", "end"), "ManualEntryRecurrenceService")
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ContinueManualEntryRecurrencesJob::class.java)
    }
}