package ai.friday.billpayment.modules.manualentry.app.integrations

import ai.friday.billpayment.app.integrations.RecurenceRepository
import ai.friday.billpayment.app.manualentry.ManualEntry
import ai.friday.billpayment.app.manualentry.ManualEntryId
import ai.friday.billpayment.app.manualentry.ManualEntryStatus
import ai.friday.billpayment.app.manualentry.ManualEntryType
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.manualentry.app.recurrence.ManualEntryRecurrence
import java.time.LocalDate
import java.time.Month
import java.time.Year

interface ManualEntryRecurrenceRepository : RecurenceRepository<ManualEntryRecurrence>

interface ManualEntryRepository {
    fun save(manualEntry: ManualEntry): ManualEntry

    fun findById(id: ManualEntryId, walletId: WalletId): ManualEntry

    fun findById(id: ManualEntryId): ManualEntry

    fun findAllByWallet(walletId: WalletId): List<ManualEntry>
    fun findByWalletAndDueDateMonth(walletId: WalletId, year: Year, month: Month): List<ManualEntry>
    fun findByWalletAndDueDate(walletId: WalletId, date: LocalDate): List<ManualEntry>
    fun findByTypeStatusAndDueDate(type: ManualEntryType, status: ManualEntryStatus, dueDate: LocalDate): List<ManualEntry>
    fun delete(manualEntry: ManualEntry)
}

interface ManualEntryNotificationService {
    fun notifyReminder(members: List<Member>, wallet: Wallet, reminder: ManualEntry)
    fun notifyExpiredReminderWallet(members: List<Member>, wallet: Wallet, entries: List<String>)
}