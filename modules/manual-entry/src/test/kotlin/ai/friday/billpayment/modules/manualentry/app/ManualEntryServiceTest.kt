package ai.friday.billpayment.modules.manualentry.app

import DynamoDB<PERSON>tils.setupDynamoDB
import ai.friday.billpayment.WALLET_BILL_CATEGORY
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.manualentry.ExpiredReminderTO
import ai.friday.billpayment.app.manualentry.ManualEntry
import ai.friday.billpayment.app.manualentry.ManualEntryError
import ai.friday.billpayment.app.manualentry.ManualEntryId
import ai.friday.billpayment.app.manualentry.ManualEntryStatus
import ai.friday.billpayment.app.manualentry.ManualEntryType
import ai.friday.billpayment.app.manualentry.ReminderTO
import ai.friday.billpayment.app.manualentry.UpdateManualEntryRequest
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.recurrence.Range
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.modules.manualentry.MANUAL_ENTRY
import ai.friday.billpayment.modules.manualentry.adapters.dynamodb.ManualEntryDbRepository
import ai.friday.billpayment.modules.manualentry.adapters.dynamodb.ManualEntryDynamoDAO
import ai.friday.billpayment.modules.manualentry.adapters.notification.ManualEntryNotificationAdapter
import ai.friday.billpayment.modules.manualentry.app.recurrence.ManualEntryRecurrenceService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import io.kotest.matchers.collections.shouldBeIn
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDate
import java.time.Month
import java.time.Year
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class ManualEntryServiceTest {

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val manualEntryRepository = ManualEntryDbRepository(ManualEntryDynamoDAO(dynamoDbEnhancedClient))

    private val walletBillCategoryServiceMock = mockk<PFMWalletCategoryService>(relaxed = true)

    private val manualEntryRecurrenceService = mockk<ManualEntryRecurrenceService>(relaxed = true)

    private val messagePublisher = mockk<MessagePublisher>(relaxed = true)

    private val notificationAdapter = mockk<ManualEntryNotificationAdapter>(relaxed = true)

    private val walletService = mockk<WalletService>(relaxed = true)

    private val manualEntryService = ManualEntryServiceImpl(
        manualEntryRepository = manualEntryRepository,
        manualEntryRecurrenceService = manualEntryRecurrenceService,
        walletBillCategoryService = walletBillCategoryServiceMock,
        walletService = walletService,
        notificationAdapter = notificationAdapter,
        messagePublisher = messagePublisher,
        notifyReminderQueueName = "test-notify-reminder-queue",
        notifyExpiredReminderQueueName = "test-notify-expired-reminder-queue",
    )

    private val manualEntry = ManualEntry(
        id = ManualEntryId("MANUAL-ENTRY-1"),
        title = "Teste",
        dueDate = getLocalDate(),
        amount = 12345L,
        walletId = WalletId(WALLET_ID),
        source = ActionSource.Api(AccountId(WALLET_ID)),
        type = ManualEntryType.EXTERNAL_PAYMENT,
        status = ManualEntryStatus.PAID,
        createdAt = getZonedDateTime(),
        updatedAt = getZonedDateTime(),
    )

    val walletId = WalletId(WALLET_ID)
    val date = getLocalDate()

    val walletFixture = WalletFixture()

    @BeforeEach
    fun setUp() {
        createBillPaymentTable(LocalDbCreationRule.dynamoDB)
    }

    @Nested
    @DisplayName("ao tentar criar um registro manual")
    inner class CreateManualEntry {

        @Test
        fun `deve criar entrada no banco`() {
            every { walletBillCategoryServiceMock.findCategory(any(), any()) } returns WALLET_BILL_CATEGORY

            val result = manualEntryService.create(
                walletId = walletId,
                title = "Título",
                amount = 12345L,
                dueDate = date,
                source = ActionSource.Api(AccountId(walletId.value)),
                type = ManualEntryType.EXTERNAL_PAYMENT,
                status = ManualEntryStatus.ACTIVE,
                categoryId = WALLET_BILL_CATEGORY.categoryId,
            )

            result.isRight() shouldBe true

            val manualEntries = manualEntryRepository.findAllByWallet(walletId)

            manualEntries.size shouldBe 1

            manualEntries[0].let {
                it.walletId shouldBe walletId
                it.dueDate shouldBe date
                it.amount shouldBe 12345L
                it.type shouldBe ManualEntryType.EXTERNAL_PAYMENT
                it.status shouldBe ManualEntryStatus.ACTIVE
                it.source shouldBe ActionSource.Api(AccountId(walletId.value))
                it.categoryId shouldBe WALLET_BILL_CATEGORY.categoryId
            }
        }

        @Test
        fun `ao tentar criar com categoria que não existe deve dar erro de categoria não encontrada`() {
            every { walletBillCategoryServiceMock.findCategory(any(), any()) } returns null

            val result = manualEntryService.create(
                walletId = walletId,
                title = "Título",
                amount = 12345L,
                dueDate = date,
                source = ActionSource.Api(AccountId(walletId.value)),
                type = ManualEntryType.EXTERNAL_PAYMENT,
                status = ManualEntryStatus.ACTIVE,
                categoryId = PFMCategoryId("CATEGORY-THAT-DOESNT-EXIST"),
            )

            result.isLeft() shouldBe true

            result.mapLeft { it shouldBe ManualEntryError.CategoryNotFound }
        }

        @Test
        fun `ao tentar criar sem categoria não deve dar erro`() {
            val result = manualEntryService.create(
                walletId = walletId,
                title = "Título",
                amount = 12345L,
                dueDate = date,
                source = ActionSource.Api(AccountId(walletId.value)),
                type = ManualEntryType.EXTERNAL_PAYMENT,
                status = ManualEntryStatus.ACTIVE,
                categoryId = null,
            )

            result.isRight() shouldBe true
        }
    }

    @Nested
    @DisplayName("ao tentar atualizar um registro manual")
    inner class UpdateManualEntry {

        @Test
        fun `deve atualizar registro no banco`() {
            manualEntryRepository.save(MANUAL_ENTRY)

            val result = manualEntryService.update(
                UpdateManualEntryRequest(
                    manualEntryId = MANUAL_ENTRY.id,
                    walletId = MANUAL_ENTRY.walletId,
                    title = "Novo título",
                    description = "Nova descrição",
                    amount = 999L,
                ),
            )

            result.isRight() shouldBe true

            with(manualEntryRepository.findById(MANUAL_ENTRY.id, MANUAL_ENTRY.walletId)) {
                title shouldBe "Novo título"
                description shouldBe "Nova descrição"
                amount shouldBe 999L
            }
        }

        @Test
        fun `não deve atualizar um registro com titulo em branco`() {
            manualEntryRepository.save(MANUAL_ENTRY)

            val result = manualEntryService.update(
                UpdateManualEntryRequest(
                    manualEntryId = MANUAL_ENTRY.id,
                    walletId = MANUAL_ENTRY.walletId,
                    title = "",
                ),
            )

            result.isRight() shouldBe true

            with(manualEntryRepository.findById(MANUAL_ENTRY.id, MANUAL_ENTRY.walletId)) {
                title shouldBe MANUAL_ENTRY.title
                description shouldBe MANUAL_ENTRY.description
                amount shouldBe MANUAL_ENTRY.amount
            }
        }

        @Test
        fun `deve conseguir atualizar apenas um campo`() {
            val updatedTitleId = ManualEntryId("MANUAL-ENTRY-UPDATED-TITLE")
            val updatedDescriptionId = ManualEntryId("MANUAL-ENTRY-UPDATED-DESCRIPTION")
            val updatedAmountId = ManualEntryId("MANUAL-ENTRY-UPDATED-AMOUNT")

            manualEntryRepository.save(MANUAL_ENTRY.copy(id = updatedTitleId))
            manualEntryRepository.save(MANUAL_ENTRY.copy(id = updatedDescriptionId))
            manualEntryRepository.save(MANUAL_ENTRY.copy(id = updatedAmountId))

            manualEntryService.update(
                UpdateManualEntryRequest(
                    manualEntryId = updatedTitleId,
                    walletId = MANUAL_ENTRY.walletId,
                    title = "Novo título",
                ),
            ).isRight() shouldBe true

            manualEntryService.update(
                UpdateManualEntryRequest(
                    manualEntryId = updatedDescriptionId,
                    walletId = MANUAL_ENTRY.walletId,
                    description = "Nova descrição",
                ),
            ).isRight() shouldBe true

            manualEntryService.update(
                UpdateManualEntryRequest(
                    manualEntryId = updatedAmountId,
                    walletId = MANUAL_ENTRY.walletId,
                    amount = 999L,
                ),
            ).isRight() shouldBe true

            with(manualEntryRepository.findById(updatedTitleId, MANUAL_ENTRY.walletId)) {
                title shouldBe "Novo título"
                description shouldBe MANUAL_ENTRY.description
                amount shouldBe MANUAL_ENTRY.amount
            }

            with(manualEntryRepository.findById(updatedDescriptionId, MANUAL_ENTRY.walletId)) {
                title shouldBe MANUAL_ENTRY.title
                description shouldBe "Nova descrição"
                amount shouldBe MANUAL_ENTRY.amount
            }

            with(manualEntryRepository.findById(updatedAmountId, MANUAL_ENTRY.walletId)) {
                title shouldBe MANUAL_ENTRY.title
                description shouldBe MANUAL_ENTRY.description
                amount shouldBe 999L
            }
        }

        @Test
        fun `se o lançamento manual não existir deve dar erro`() {
            val result = manualEntryService.update(
                UpdateManualEntryRequest(
                    manualEntryId = MANUAL_ENTRY.id,
                    walletId = MANUAL_ENTRY.walletId,
                    title = "Novo título",
                    description = "Nova descrição",
                    amount = 999L,
                ),
            )

            result.isLeft() shouldBe true

            result.mapLeft {
                it shouldBe ManualEntryError.ManualEntryNotFound
            }
        }

        @Test
        fun `caso a range passada seja THIS_AND_FUTURE deve atualizar todos os lançamentos manuais futuros da recorrência`() {
            every { manualEntryRecurrenceService.getThisAndFuture(any()) } returns listOf(
                MANUAL_ENTRY.id,
                ManualEntryId("MANUAL-ENTRY-2"),
                ManualEntryId("MANUAL-ENTRY-3"),
            )

            manualEntryRepository.save(MANUAL_ENTRY.copy(status = ManualEntryStatus.ACTIVE))
            manualEntryRepository.save(MANUAL_ENTRY.copy(id = ManualEntryId("MANUAL-ENTRY-2"), status = ManualEntryStatus.ACTIVE))
            manualEntryRepository.save(MANUAL_ENTRY.copy(id = ManualEntryId("MANUAL-ENTRY-3"), status = ManualEntryStatus.ACTIVE))

            val result = manualEntryService.update(
                UpdateManualEntryRequest(
                    manualEntryId = MANUAL_ENTRY.id,
                    walletId = MANUAL_ENTRY.walletId,
                    title = "Novo título",
                    description = "Nova descrição",
                    amount = 999L,
                ),
                range = Range.THIS_AND_FUTURE,
            )

            result.isRight() shouldBe true

            listOf(MANUAL_ENTRY.id.value, "MANUAL-ENTRY-2", "MANUAL-ENTRY-3").forEach {
                with(manualEntryRepository.findById(ManualEntryId(it), MANUAL_ENTRY.walletId)) {
                    title shouldBe "Novo título"
                    description shouldBe "Nova descrição"
                    amount shouldBe 999L
                }
            }

            val lastEntry = manualEntryRepository.findById(ManualEntryId("MANUAL-ENTRY-3"), MANUAL_ENTRY.walletId)
            verify { manualEntryRecurrenceService.updateRecurrence(lastEntry) }
        }
    }

    @Nested
    @DisplayName("ao listar todas os registros manuais")
    inner class ListAllManualEntries {

        @Test
        fun `deve retornar tudo que está no banco`() {
            val walletId = WalletId(WALLET_ID)

            manualEntryRepository.save(manualEntry)
            manualEntryRepository.save(manualEntry.copy(id = ManualEntryId("MANUAL-ENTRY-2"), dueDate = manualEntry.dueDate.plusDays(1)))

            val entries = manualEntryService.listAll(walletId)

            entries.size shouldBe 2

            entries[0].let {
                it.id shouldBe ManualEntryId("MANUAL-ENTRY-1")
            }

            entries[1].let {
                it.id shouldBe ManualEntryId("MANUAL-ENTRY-2")
            }
        }
    }

    @Nested
    @DisplayName("ao tentar atualizar categoria de um registro manual")
    inner class SetManualEntryCategory {

        @Test
        fun `deve atualizar categoria no banco`() {
            every { walletBillCategoryServiceMock.findCategory(any(), any()) } returns WALLET_BILL_CATEGORY

            manualEntryRepository.save(MANUAL_ENTRY.copy(categoryId = PFMCategoryId("CATEGORIA-ANTIGA")))

            val result = manualEntryService.setCategory(MANUAL_ENTRY.id, WalletId(WALLET_ID), WALLET_BILL_CATEGORY.categoryId)

            result.isRight() shouldBe true

            result.map {
                it.categoryId shouldBe WALLET_BILL_CATEGORY.categoryId
            }

            val manualEntry = manualEntryRepository.findById(MANUAL_ENTRY.id, WalletId(WALLET_ID))

            with(manualEntry) {
                categoryId shouldBe WALLET_BILL_CATEGORY.categoryId
            }
        }

        @Test
        fun `se entrada não possuir categoria, a categoria deve ser registrada`() {
            every { walletBillCategoryServiceMock.findCategory(any(), any()) } returns WALLET_BILL_CATEGORY

            manualEntryRepository.save(MANUAL_ENTRY.copy(categoryId = null))

            val result = manualEntryService.setCategory(MANUAL_ENTRY.id, WalletId(WALLET_ID), WALLET_BILL_CATEGORY.categoryId)

            result.isRight() shouldBe true

            result.map {
                it.categoryId shouldBe WALLET_BILL_CATEGORY.categoryId
            }

            val manualEntry = manualEntryRepository.findById(MANUAL_ENTRY.id, WalletId(WALLET_ID))

            with(manualEntry) {
                categoryId shouldBe WALLET_BILL_CATEGORY.categoryId
            }
        }

        @Test
        fun `se a categoria passada for null, deve remover do lançamento manual`() {
            manualEntryRepository.save(MANUAL_ENTRY.copy(categoryId = PFMCategoryId("CATEGORIA-ANTIGA")))

            val result = manualEntryService.setCategory(MANUAL_ENTRY.id, WalletId(WALLET_ID), null)

            result.isRight() shouldBe true

            result.map {
                it.categoryId shouldBe null
            }

            val manualEntry = manualEntryRepository.findById(MANUAL_ENTRY.id, WalletId(WALLET_ID))

            with(manualEntry) {
                categoryId shouldBe null
            }
        }

        @Test
        fun `se o lançamento manual não existir, deve dar erro`() {
            val result = manualEntryService.setCategory(MANUAL_ENTRY.id, WalletId(WALLET_ID), WALLET_BILL_CATEGORY.categoryId)

            result.isLeft() shouldBe true

            result.mapLeft {
                it shouldBe ManualEntryError.ManualEntryNotFound
            }
        }

        @Test
        fun `se a categoria não existir, deve dar erro`() {
            every { walletBillCategoryServiceMock.findCategory(any(), any()) } returns null

            manualEntryRepository.save(MANUAL_ENTRY)

            val result = manualEntryService.setCategory(MANUAL_ENTRY.id, WalletId(WALLET_ID), WALLET_BILL_CATEGORY.categoryId)

            result.isLeft() shouldBe true

            result.mapLeft {
                it shouldBe ManualEntryError.CategoryNotFound
            }
        }

        @Test
        fun `caso a range passada seja THIS_AND_FUTURE deve atualizar todos os lançamentos manuais futuros da recorrência`() {
            every { walletBillCategoryServiceMock.findCategory(any(), any()) } returns WALLET_BILL_CATEGORY
            every { manualEntryRecurrenceService.getThisAndFuture(any()) } returns listOf(
                MANUAL_ENTRY.id,
                ManualEntryId("MANUAL-ENTRY-2"),
                ManualEntryId("MANUAL-ENTRY-3"),
            )

            manualEntryRepository.save(MANUAL_ENTRY.copy(categoryId = PFMCategoryId("CATEGORIA-ANTIGA")))
            manualEntryRepository.save(MANUAL_ENTRY.copy(id = ManualEntryId("MANUAL-ENTRY-2"), categoryId = PFMCategoryId("CATEGORIA-ANTIGA")))
            manualEntryRepository.save(MANUAL_ENTRY.copy(id = ManualEntryId("MANUAL-ENTRY-3"), categoryId = PFMCategoryId("CATEGORIA-ANTIGA")))

            val result = manualEntryService.setCategory(MANUAL_ENTRY.id, WalletId(WALLET_ID), WALLET_BILL_CATEGORY.categoryId, range = Range.THIS_AND_FUTURE)

            result.isRight() shouldBe true

            result.map {
                it.categoryId shouldBe WALLET_BILL_CATEGORY.categoryId
            }

            listOf(MANUAL_ENTRY.id.value, "MANUAL-ENTRY-2", "MANUAL-ENTRY-3").forEach {
                val manualEntry = manualEntryRepository.findById(ManualEntryId(it), WalletId(WALLET_ID))

                with(manualEntry) {
                    categoryId shouldBe WALLET_BILL_CATEGORY.categoryId
                }
            }

            val lastEntry = manualEntryRepository.findById(ManualEntryId("MANUAL-ENTRY-3"), MANUAL_ENTRY.walletId)
            verify { manualEntryRecurrenceService.updateRecurrence(lastEntry) }
        }
    }

    @Nested
    @DisplayName("ao tentar ignorar um registro manual")
    inner class IgnoreManualEntry {

        @Test
        fun `deve atualizar status no banco e setar a data`() {
            manualEntryRepository.save(MANUAL_ENTRY.copy(status = ManualEntryStatus.PAID))

            val result = manualEntryService.ignore(MANUAL_ENTRY.id, WalletId(WALLET_ID))

            result.isRight() shouldBe true

            val manualEntry = manualEntryRepository.findById(MANUAL_ENTRY.id, WalletId(WALLET_ID))

            with(manualEntry) {
                status shouldBe ManualEntryStatus.IGNORED
                ignoredAt shouldNotBe null
            }
        }

        @Test
        fun `se o lançamento manual não existir, deve dar erro`() {
            val result = manualEntryService.ignore(MANUAL_ENTRY.id, WalletId(WALLET_ID))

            result.isLeft() shouldBe true

            result.mapLeft {
                it shouldBe ManualEntryError.ManualEntryNotFound
            }
        }

        @Test
        fun `caso a range passada seja THIS_AND_FUTURE deve atualizar todos os lançamentos manuais futuros da recorrência`() {
            every { manualEntryRecurrenceService.getThisAndFuture(any()) } returns listOf(
                MANUAL_ENTRY.id,
                ManualEntryId("MANUAL-ENTRY-2"),
                ManualEntryId("MANUAL-ENTRY-3"),
            )

            manualEntryRepository.save(MANUAL_ENTRY.copy(status = ManualEntryStatus.ACTIVE))
            manualEntryRepository.save(MANUAL_ENTRY.copy(id = ManualEntryId("MANUAL-ENTRY-2"), status = ManualEntryStatus.ACTIVE))
            manualEntryRepository.save(MANUAL_ENTRY.copy(id = ManualEntryId("MANUAL-ENTRY-3"), status = ManualEntryStatus.ACTIVE))

            val result = manualEntryService.ignore(MANUAL_ENTRY.id, WalletId(WALLET_ID), range = Range.THIS_AND_FUTURE)

            result.isRight() shouldBe true

            listOf(MANUAL_ENTRY.id.value, "MANUAL-ENTRY-2", "MANUAL-ENTRY-3").forEach {
                val manualEntry = manualEntryRepository.findById(ManualEntryId(it), WalletId(WALLET_ID))

                with(manualEntry) {
                    status shouldBe ManualEntryStatus.IGNORED
                    ignoredAt shouldNotBe null
                }
            }
        }

        @Test
        fun `caso um dos lançamentos da recorrência não exista, não deve quebrar`() {
            every { manualEntryRecurrenceService.getThisAndFuture(any()) } returns listOf(
                MANUAL_ENTRY.id,
                ManualEntryId("MANUAL-ENTRY-2"),
                ManualEntryId("MANUAL-ENTRY-THAT-DOESNT-EXIST"),
            )

            val manualEntry = MANUAL_ENTRY.copy(
                status = ManualEntryStatus.ACTIVE,
                source = ActionSource.WalletRecurrence(AccountId(MANUAL_ENTRY.walletId.value), RecurrenceId("RECURRENCE-ID")),
            )

            manualEntryRepository.save(manualEntry)
            manualEntryRepository.save(manualEntry.copy(id = ManualEntryId("MANUAL-ENTRY-2"), status = ManualEntryStatus.ACTIVE))

            val result = manualEntryService.ignore(MANUAL_ENTRY.id, WalletId(WALLET_ID), range = Range.THIS_AND_FUTURE)

            result.isRight() shouldBe true

            listOf(MANUAL_ENTRY.id.value, "MANUAL-ENTRY-2").forEach {
                val entry = manualEntryRepository.findById(ManualEntryId(it), WalletId(WALLET_ID))

                with(entry) {
                    status shouldBe ManualEntryStatus.IGNORED
                    ignoredAt shouldNotBe null
                }
            }

            val lastEntry = manualEntryRepository.findById(ManualEntryId("MANUAL-ENTRY-2"), MANUAL_ENTRY.walletId)
            verify { manualEntryRecurrenceService.updateRecurrence(lastEntry) }
        }
    }

    @Nested
    @DisplayName("ao tentar restorar um registro manual ignorado")
    inner class RestoreIgnoredManualEntry {

        @Test
        fun `pagamentos externos devem ter seu status setado para PAID`() {
            manualEntryRepository.save(MANUAL_ENTRY.copy(type = ManualEntryType.EXTERNAL_PAYMENT, status = ManualEntryStatus.IGNORED))

            val result = manualEntryService.restoreIgnored(MANUAL_ENTRY.id, WalletId(WALLET_ID))

            result.isRight() shouldBe true

            val manualEntry = manualEntryRepository.findById(MANUAL_ENTRY.id, WalletId(WALLET_ID))

            with(manualEntry) {
                status shouldBe ManualEntryStatus.PAID
                ignoredAt shouldBe null
            }
        }

        @Test
        fun `lembretes devem ter seu status setado para ACTIVE`() {
            manualEntryRepository.save(MANUAL_ENTRY.copy(type = ManualEntryType.REMINDER, status = ManualEntryStatus.IGNORED))

            val result = manualEntryService.restoreIgnored(MANUAL_ENTRY.id, WalletId(WALLET_ID))

            result.isRight() shouldBe true

            val manualEntry = manualEntryRepository.findById(MANUAL_ENTRY.id, WalletId(WALLET_ID))

            with(manualEntry) {
                status shouldBe ManualEntryStatus.ACTIVE
                ignoredAt shouldBe null
            }
        }

        @Test
        fun `se o lançamento manual não existir, deve dar erro`() {
            val result = manualEntryService.restoreIgnored(MANUAL_ENTRY.id, WalletId(WALLET_ID))

            result.isLeft() shouldBe true

            result.mapLeft {
                it shouldBe ManualEntryError.ManualEntryNotFound
            }
        }

        @Test
        fun `se o status do lançamento não for IGNORED, deve dar erro`() {
            manualEntryRepository.save(MANUAL_ENTRY.copy(status = ManualEntryStatus.PAID))

            val result = manualEntryService.restoreIgnored(MANUAL_ENTRY.id, WalletId(WALLET_ID))

            result.isLeft() shouldBe true

            result.mapLeft {
                it shouldBe ManualEntryError.InvalidStatus
            }

            val manualEntry = manualEntryRepository.findById(MANUAL_ENTRY.id, WalletId(WALLET_ID))

            with(manualEntry) {
                status shouldBe ManualEntryStatus.PAID
            }
        }
    }

    @Nested
    @DisplayName("ao marcar como pago um registro manual")
    inner class SetManualEntryStatus {

        @Test
        fun `deve atualizar status no banco e setar a data que foi marcado`() {
            manualEntryRepository.save(MANUAL_ENTRY.copy(type = ManualEntryType.REMINDER, status = ManualEntryStatus.ACTIVE))

            val result = manualEntryService.markAsPaid(MANUAL_ENTRY.id, WalletId(WALLET_ID))

            result.isRight() shouldBe true

            val manualEntry = manualEntryRepository.findById(MANUAL_ENTRY.id, WalletId(WALLET_ID))

            with(manualEntry) {
                status shouldBe ManualEntryStatus.PAID
                markedAsPaidAt shouldNotBe null
            }
        }

        @Test
        fun `se o lançamento manual não existir, deve dar erro`() {
            val result = manualEntryService.markAsPaid(MANUAL_ENTRY.id, WalletId(WALLET_ID))

            result.isLeft() shouldBe true

            result.mapLeft {
                it shouldBe ManualEntryError.ManualEntryNotFound
            }
        }

        @Test
        fun `não deve permitir atualizar um pagamento externo para ACTIVE`() {
            manualEntryRepository.save(MANUAL_ENTRY.copy(type = ManualEntryType.EXTERNAL_PAYMENT, status = ManualEntryStatus.PAID))

            val result = manualEntryService.markAsPaid(MANUAL_ENTRY.id, WalletId(WALLET_ID))

            result.isLeft() shouldBe true

            result.mapLeft {
                it shouldBe ManualEntryError.InvalidStatus
            }
        }
    }

    @Nested
    @DisplayName("ao desmarcar como pago um registro manual")
    inner class SetManualEntryAsActive {

        @Test
        fun `deve atualizar status no banco e setar remover a data`() {
            manualEntryRepository.save(
                MANUAL_ENTRY.copy(
                    type = ManualEntryType.REMINDER,
                    status = ManualEntryStatus.PAID,
                    markedAsPaidAt = getZonedDateTime(),
                ),
            )

            val result = manualEntryService.unmarkAsPaid(MANUAL_ENTRY.id, WalletId(WALLET_ID))

            result.isRight() shouldBe true

            val manualEntry = manualEntryRepository.findById(MANUAL_ENTRY.id, WalletId(WALLET_ID))

            with(manualEntry) {
                status shouldBe ManualEntryStatus.ACTIVE
                markedAsPaidAt shouldBe null
            }
        }

        @Test
        fun `se o lançamento manual não existir, deve dar erro`() {
            val result = manualEntryService.unmarkAsPaid(MANUAL_ENTRY.id, WalletId(WALLET_ID))

            result.isLeft() shouldBe true

            result.mapLeft {
                it shouldBe ManualEntryError.ManualEntryNotFound
            }
        }

        @Test
        fun `não deve permitir atualizar um pagamento externo para ACTIVE`() {
            manualEntryRepository.save(MANUAL_ENTRY.copy(type = ManualEntryType.EXTERNAL_PAYMENT, status = ManualEntryStatus.PAID))

            val result = manualEntryService.unmarkAsPaid(MANUAL_ENTRY.id, WalletId(WALLET_ID))

            result.isLeft() shouldBe true

            result.mapLeft {
                it shouldBe ManualEntryError.InvalidStatus
            }
        }
    }

    @Nested
    @DisplayName("ao buscar todas os lançamentos manuais por mês")
    inner class FindAllManualEntriesByMonth {
        @Test
        fun `deve retornar apenas os lançamentos do mês em questão`() {
            for (date in listOf("2023-11-05", "2023-11-07", "2023-11-25", "2024-02-03", "2024-02-19")) {
                manualEntryRepository.save(
                    MANUAL_ENTRY.copy(
                        id = ManualEntryId(),
                        dueDate = LocalDate.parse(date, dateFormat),
                    ),
                )
            }

            val resultNov2023 = manualEntryService.findAllByWalletAndDueDateMonth(MANUAL_ENTRY.walletId, Year.of(2023), Month.NOVEMBER)
            resultNov2023.size shouldBe 3

            val resultFeb2024 = manualEntryService.findAllByWalletAndDueDateMonth(MANUAL_ENTRY.walletId, Year.of(2024), Month.FEBRUARY)
            resultFeb2024.size shouldBe 2
        }
    }

    @Nested
    @DisplayName("ao solicitar o envio de notificação de lembretes")
    inner class NotifyRemindersDueToday {

        @Test
        fun `deve produzir mensagem na fila para os reminders ativos que vencem hoje`() {
            // Devem enviar

            manualEntryRepository.save(
                MANUAL_ENTRY.copy(
                    id = ManualEntryId("REMINDER-TODAY-ACTIVE-1"),
                    type = ManualEntryType.REMINDER,
                    status = ManualEntryStatus.ACTIVE,
                    dueDate = getLocalDate(),
                ),
            )

            manualEntryRepository.save(
                MANUAL_ENTRY.copy(
                    id = ManualEntryId("REMINDER-TODAY-ACTIVE-2"),
                    type = ManualEntryType.REMINDER,
                    status = ManualEntryStatus.ACTIVE,
                    dueDate = getLocalDate(),
                ),
            )

            // Não devem enviar

            manualEntryRepository.save(
                MANUAL_ENTRY.copy(
                    id = ManualEntryId("EXTERNAL-PAYMENT-TODAY"),
                    type = ManualEntryType.EXTERNAL_PAYMENT,
                    dueDate = getLocalDate(),
                ),
            )

            manualEntryRepository.save(
                MANUAL_ENTRY.copy(
                    id = ManualEntryId("REMINDER-YERSTERDAY"),
                    type = ManualEntryType.REMINDER,
                    status = ManualEntryStatus.ACTIVE,
                    dueDate = getLocalDate().minusDays(1),
                ),
            )

            manualEntryRepository.save(
                MANUAL_ENTRY.copy(
                    id = ManualEntryId("REMINDER-TOMORROW"),
                    type = ManualEntryType.REMINDER,
                    status = ManualEntryStatus.ACTIVE,
                    dueDate = getLocalDate().plusDays(1),
                ),
            )

            manualEntryRepository.save(
                MANUAL_ENTRY.copy(
                    id = ManualEntryId("REMINDER-TODAY-PAID"),
                    type = ManualEntryType.REMINDER,
                    status = ManualEntryStatus.PAID,
                    dueDate = getLocalDate(),
                ),
            )

            manualEntryService.notifyRemindersDueToday()

            val messages = mutableListOf<Any>()
            verify(exactly = 2) { messagePublisher.sendMessage("test-notify-reminder-queue", capture(messages)) }

            messages.forEach { message ->
                (message is ReminderTO) shouldBe true

                with(message as ReminderTO) {
                    id.value shouldBeIn listOf("REMINDER-TODAY-ACTIVE-1", "REMINDER-TODAY-ACTIVE-2")
                }
            }
        }
    }

    @Nested
    @DisplayName("ao solicitar o envio de notificação de lembretes vencidos")
    inner class NotifyExpiredReminders {

        @Test
        fun `deve produzir mensagem na fila para as carteiras que possuem reminders ativos que venceram ontem`() {
            // Devem enviar

            manualEntryRepository.save(
                MANUAL_ENTRY.copy(
                    id = ManualEntryId("REMINDER-YESTERDAY-ACTIVE-1"),
                    walletId = WalletId("WALLET-1"),
                    type = ManualEntryType.REMINDER,
                    status = ManualEntryStatus.ACTIVE,
                    dueDate = getLocalDate().minusDays(1),
                ),
            )

            manualEntryRepository.save(
                MANUAL_ENTRY.copy(
                    id = ManualEntryId("REMINDER-YESTERDAY-ACTIVE-2"),
                    walletId = WalletId("WALLET-1"),
                    type = ManualEntryType.REMINDER,
                    status = ManualEntryStatus.ACTIVE,
                    dueDate = getLocalDate().minusDays(1),
                ),
            )
            manualEntryRepository.save(
                MANUAL_ENTRY.copy(
                    id = ManualEntryId("REMINDER-YESTERDAY-ACTIVE-3"),
                    walletId = WalletId("WALLET-2"),
                    type = ManualEntryType.REMINDER,
                    status = ManualEntryStatus.ACTIVE,
                    dueDate = getLocalDate().minusDays(1),
                ),
            )

            // Não devem enviar

            manualEntryRepository.save(
                MANUAL_ENTRY.copy(
                    id = ManualEntryId("EXTERNAL-PAYMENT-TODAY"),
                    walletId = WalletId("WALLET-3"),
                    type = ManualEntryType.EXTERNAL_PAYMENT,
                    status = ManualEntryStatus.ACTIVE,
                    dueDate = getLocalDate().minusDays(-1),
                ),
            )

            manualEntryRepository.save(
                MANUAL_ENTRY.copy(
                    id = ManualEntryId("REMINDER-DAY-BEFORE-YESTERDAY"),
                    walletId = WalletId("WALLET-4"),
                    type = ManualEntryType.REMINDER,
                    status = ManualEntryStatus.ACTIVE,
                    dueDate = getLocalDate().minusDays(2),
                ),
            )

            manualEntryRepository.save(
                MANUAL_ENTRY.copy(
                    id = ManualEntryId("REMINDER-TODAY"),
                    walletId = WalletId("WALLET-5"),
                    type = ManualEntryType.REMINDER,
                    status = ManualEntryStatus.ACTIVE,
                    dueDate = getLocalDate(),
                ),
            )

            manualEntryRepository.save(
                MANUAL_ENTRY.copy(
                    id = ManualEntryId("REMINDER-YESTERDAY-PAID"),
                    walletId = WalletId("WALLET-6"),
                    type = ManualEntryType.REMINDER,
                    status = ManualEntryStatus.PAID,
                    dueDate = getLocalDate().minusDays(1),
                ),
            )

            manualEntryService.notifyRemindersExpired()

            val messages = mutableListOf<Any>()
            verify(exactly = 2) { messagePublisher.sendMessage("test-notify-expired-reminder-queue", capture(messages)) }

            messages.forEach { message ->
                (message is ExpiredReminderTO) shouldBe true

                with(message as ExpiredReminderTO) {
                    walletId.value shouldBeIn listOf("WALLET-1", "WALLET-2")
                }
            }
        }
    }

    @Nested
    @DisplayName("ao solicitar notificação de um lembrete")
    inner class NotifyReminder {

        @Test
        fun `deve enviar mensagem`() {
            val wallet = walletFixture.buildWallet(otherMembers = listOf(walletFixture.participant, walletFixture.ultraLimitedParticipant))

            val reminder = MANUAL_ENTRY.copy(
                type = ManualEntryType.REMINDER,
                status = ManualEntryStatus.ACTIVE,
                dueDate = getLocalDate(),
                walletId = wallet.id,
            )

            manualEntryRepository.save(reminder)

            every { walletService.findWallet(any()) } returns wallet

            val result = manualEntryService.notifyReminder(ReminderTO(reminder.id, reminder.walletId))

            result.isRight() shouldBe true

            val reminderSlot = slot<ManualEntry>()

            verify { notificationAdapter.notifyReminder(listOf(wallet.founder, walletFixture.participant), wallet, capture(reminderSlot)) }

            reminderSlot.captured.id shouldBe reminder.id
        }
    }
}