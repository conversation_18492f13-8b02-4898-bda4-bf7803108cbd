package ai.friday.billpayment.modules.manualentry.app.category

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.adapters.dynamodb.WalletBillCategoryDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletBillCategoryDynamoDAO
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.manualentry.ManualEntryId
import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.app.manualentry.ManualEntryStatus
import ai.friday.billpayment.app.manualentry.ManualEntryType
import ai.friday.billpayment.app.pfm.BillCategory
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.SummaryEntry
import ai.friday.billpayment.app.pfm.SummaryEntryType
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.modules.manualentry.MANUAL_ENTRY
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import java.time.LocalDate
import java.time.Month
import java.time.Year
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ManualEntrySummaryServiceTest {
    private val amazonDynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val walletBillCategoryRepository = WalletBillCategoryDbRepository(client = WalletBillCategoryDynamoDAO(cli = setupDynamoDB()))

    private val manualEntryService = mockk<ManualEntryService>(relaxed = true)

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    private val service = ManualEntrySummaryService(
        manualEntryService = manualEntryService,
        walletBillCategoryDbRepository = walletBillCategoryRepository,
    )

    @BeforeEach
    fun setUp() {
        createBillPaymentTable(amazonDynamoDB)
    }

    @Test
    fun `deve retornar um resumo do periodo com  as entradas manuais`() {
        val educacao =
            BillCategory(
                categoryId = PFMCategoryId(value = "1"),
                name = "Categoria 1",
                icon = "EDUCACAO",
                default = false,
            )
        val alimentacao =
            BillCategory(
                categoryId = PFMCategoryId(value = "2"),
                name = "Categoria 2",
                icon = "ALIMENTACAO",
                default = false,
            )

        listOf(educacao, alimentacao).forEach {
            walletBillCategoryRepository.save(
                WalletBillCategory(walletId = wallet.id, categoryId = it.categoryId, name = it.name, icon = it.icon, enabled = true, default = it.default),
            )
        }

        every { manualEntryService.findAllByWalletAndDueDateMonth(wallet.id, Year.of(2024), Month.JANUARY) } returns listOf(
            MANUAL_ENTRY.copy(id = ManualEntryId(), walletId = wallet.id, status = ManualEntryStatus.PAID, amount = 150_00, categoryId = educacao.categoryId, title = "Manual Entry 1", description = "Teste", dueDate = LocalDate.of(2024, Month.JANUARY, 4)),
            MANUAL_ENTRY.copy(id = ManualEntryId(), walletId = wallet.id, status = ManualEntryStatus.PAID, amount = 350_00, categoryId = null, title = "Manual Entry 2", description = "Teste2", dueDate = LocalDate.of(2024, Month.JANUARY, 6), type = ManualEntryType.REMINDER),
            MANUAL_ENTRY.copy(id = ManualEntryId(), walletId = wallet.id, status = ManualEntryStatus.IGNORED, amount = 100_00, categoryId = educacao.categoryId),
            MANUAL_ENTRY.copy(id = ManualEntryId(), walletId = wallet.id, status = ManualEntryStatus.IGNORED, amount = 300_00, categoryId = null),
        )

        val result = service.generateSummaryList(wallet.id, walletFixture.founder, Year.of(2024), Month.JANUARY)

        result shouldBe listOf(
            SummaryEntry(
                category = educacao,
                totalAmount = 150_00,
                title = "Manual Entry 1",
                description = "Teste",
                date = LocalDate.of(2024, Month.JANUARY, 4),
                type = SummaryEntryType.PAYMENT,
                entryType = "Lançamento manual",
            ),
            SummaryEntry(
                category = null,
                totalAmount = 350_00,
                title = "Manual Entry 2",
                description = "Teste2",
                date = LocalDate.of(2024, Month.JANUARY, 6),
                type = SummaryEntryType.PAYMENT,
                entryType = "Lembrete",
            ),
        )
    }

    @Test
    fun `deve retornar um resumo do periodo somente com os lançamentos manuais visíveis pelo usuário`() {
        val founderSource = ActionSource.Api(walletFixture.founder.accountId)
        val limitedParticipantSource = ActionSource.Api(walletFixture.ultraLimitedParticipant.accountId)

        every { manualEntryService.findAllByWalletAndDueDateMonth(wallet.id, Year.of(2024), Month.JANUARY) } returns listOf(
            MANUAL_ENTRY.copy(source = founderSource, id = ManualEntryId(), walletId = wallet.id, status = ManualEntryStatus.PAID, amount = 150_00, categoryId = null, title = "Manual Entry 1", description = "Teste", dueDate = LocalDate.of(2024, Month.JANUARY, 4)),
            MANUAL_ENTRY.copy(source = limitedParticipantSource, id = ManualEntryId(), walletId = wallet.id, status = ManualEntryStatus.PAID, amount = 350_00, categoryId = null, title = "Manual Entry 2", description = "Teste2", dueDate = LocalDate.of(2024, Month.JANUARY, 6)),
        )

        val result = service.generateSummaryList(wallet.id, walletFixture.ultraLimitedParticipant, Year.of(2024), Month.JANUARY)

        result shouldBe listOf(
            SummaryEntry(
                category = null,
                totalAmount = 350_00,
                title = "Manual Entry 2",
                description = "Teste2",
                date = LocalDate.of(2024, Month.JANUARY, 6),
                type = SummaryEntryType.PAYMENT,
                entryType = "Lançamento manual",
            ),
        )
    }

    @Test
    fun `não deve incluir lançamentos manuais ignorados `() {
        val educacao =
            BillCategory(
                categoryId = PFMCategoryId(value = "1"),
                name = "Categoria 1",
                icon = "EDUCACAO",
                default = false,
            )

        listOf(educacao).forEach {
            walletBillCategoryRepository.save(
                WalletBillCategory(walletId = wallet.id, categoryId = it.categoryId, name = it.name, icon = it.icon, enabled = true, default = it.default),
            )
        }

        every { manualEntryService.findAllByWalletAndDueDateMonth(wallet.id, Year.of(2024), Month.JANUARY) } returns listOf(
            MANUAL_ENTRY.copy(id = ManualEntryId(), walletId = wallet.id, status = ManualEntryStatus.PAID, amount = 150_00, categoryId = educacao.categoryId, title = "Manual Entry 1", description = "Teste1", dueDate = LocalDate.of(2024, Month.JANUARY, 4)),
            MANUAL_ENTRY.copy(id = ManualEntryId(), walletId = wallet.id, status = ManualEntryStatus.PAID, amount = 350_00, categoryId = null, title = "Manual Entry 2", description = "Teste2", dueDate = LocalDate.of(2024, Month.JANUARY, 6)),
            MANUAL_ENTRY.copy(id = ManualEntryId(), walletId = wallet.id, status = ManualEntryStatus.IGNORED, amount = 100_00, categoryId = educacao.categoryId),
            MANUAL_ENTRY.copy(id = ManualEntryId(), walletId = wallet.id, status = ManualEntryStatus.IGNORED, amount = 300_00, categoryId = null),
        )

        val result = service.generateSummaryList(wallet.id, walletFixture.founder, Year.of(2024), Month.JANUARY)

        result shouldContainExactlyInAnyOrder listOf(
            SummaryEntry(
                category = educacao,
                totalAmount = 150_00,
                title = "Manual Entry 1",
                description = "Teste1",
                date = LocalDate.of(2024, Month.JANUARY, 4),
                type = SummaryEntryType.PAYMENT,
                entryType = "Lançamento manual",
            ),
            SummaryEntry(
                category = null,
                totalAmount = 350_00,
                title = "Manual Entry 2",
                description = "Teste2",
                date = LocalDate.of(2024, Month.JANUARY, 6),
                type = SummaryEntryType.PAYMENT,
                entryType = "Lançamento manual",
            ),
        )
    }

    @Test
    fun `deve incluir receitas`() {
        every { manualEntryService.findAllByWalletAndDueDateMonth(wallet.id, Year.of(2024), Month.JANUARY) } returns listOf(
            MANUAL_ENTRY.copy(
                title = "Manual Entry 1",
                description = "Teste1",
                type = ManualEntryType.EXTERNAL_PAYMENT,
                id = ManualEntryId(),
                walletId = wallet.id,
                status = ManualEntryStatus.PAID,
                amount = 150_00,
                dueDate = LocalDate.of(2024, Month.JANUARY, 4),
                categoryId = null,
            ),
            MANUAL_ENTRY.copy(
                title = "Income 1",
                description = "Income 1 desc",
                type = ManualEntryType.INCOME,
                id = ManualEntryId(),
                walletId = wallet.id,
                status = ManualEntryStatus.PAID,
                amount = 200_00,
                dueDate = LocalDate.of(2024, Month.JANUARY, 4),
                categoryId = null,
            ),
        )

        val result = service.generateSummaryList(wallet.id, walletFixture.founder, Year.of(2024), Month.JANUARY)

        result shouldContainExactlyInAnyOrder listOf(
            SummaryEntry(
                category = null,
                totalAmount = 150_00,
                title = "Manual Entry 1",
                description = "Teste1",
                date = LocalDate.of(2024, Month.JANUARY, 4),
                type = SummaryEntryType.PAYMENT,
                entryType = "Lançamento manual",
            ),
            SummaryEntry(
                category = null,
                totalAmount = 200_00,
                title = "Income 1",
                description = "Income 1 desc",
                date = LocalDate.of(2024, Month.JANUARY, 4),
                type = SummaryEntryType.INCOME,
                entryType = "Renda",
            ),
        )
    }

    @Test
    fun `deve categorizar os lembretes de acordo com o status e valor`() {
        every { manualEntryService.findAllByWalletAndDueDateMonth(wallet.id, Year.of(2024), Month.JANUARY) } returns listOf(
            MANUAL_ENTRY.copy(type = ManualEntryType.REMINDER, id = ManualEntryId(), walletId = wallet.id, status = ManualEntryStatus.PAID, amount = 0, title = "Manual Entry 1", description = "Teste1", dueDate = LocalDate.of(2024, Month.JANUARY, 4)),
            MANUAL_ENTRY.copy(type = ManualEntryType.REMINDER, id = ManualEntryId(), walletId = wallet.id, status = ManualEntryStatus.PAID, amount = 150_00, title = "Manual Entry 2", description = "Teste2", dueDate = LocalDate.of(2024, Month.JANUARY, 4)),
            MANUAL_ENTRY.copy(type = ManualEntryType.REMINDER, id = ManualEntryId(), walletId = wallet.id, status = ManualEntryStatus.ACTIVE, amount = 0, title = "Manual Entry 3", description = "Teste3", dueDate = LocalDate.of(2024, Month.JANUARY, 4)),
            MANUAL_ENTRY.copy(type = ManualEntryType.REMINDER, id = ManualEntryId(), walletId = wallet.id, status = ManualEntryStatus.ACTIVE, amount = 100_00, title = "Manual Entry 4", description = "Teste4", dueDate = LocalDate.of(2024, Month.JANUARY, 4)),
        )
        val result = service.generateSummaryList(wallet.id, walletFixture.founder, Year.of(2024), Month.JANUARY)

        result shouldBe listOf(
            SummaryEntry(
                category = null,
                totalAmount = 0,
                title = "Manual Entry 1",
                description = "Teste1",
                date = LocalDate.of(2024, Month.JANUARY, 4),
                type = SummaryEntryType.REMINDER,
                entryType = "Lembrete",
            ),
            SummaryEntry(
                category = null,
                totalAmount = 150_00,
                title = "Manual Entry 2",
                description = "Teste2",
                date = LocalDate.of(2024, Month.JANUARY, 4),
                type = SummaryEntryType.PAYMENT,
                entryType = "Lembrete",
            ),
            SummaryEntry(
                category = null,
                totalAmount = 0,
                title = "Manual Entry 3",
                description = "Teste3",
                date = LocalDate.of(2024, Month.JANUARY, 4),
                type = SummaryEntryType.REMINDER,
                entryType = "Lembrete",
            ),
            SummaryEntry(
                category = null,
                totalAmount = 100_00,
                title = "Manual Entry 4",
                description = "Teste4",
                date = LocalDate.of(2024, Month.JANUARY, 4),
                type = SummaryEntryType.REMINDER,
                entryType = "Lembrete",
            ),
        )
    }
}