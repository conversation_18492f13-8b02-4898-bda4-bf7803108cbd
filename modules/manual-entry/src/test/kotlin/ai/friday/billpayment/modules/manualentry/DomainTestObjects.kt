package ai.friday.billpayment.modules.manualentry

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.manualentry.ManualEntry
import ai.friday.billpayment.app.manualentry.ManualEntryId
import ai.friday.billpayment.app.manualentry.ManualEntryStatus
import ai.friday.billpayment.app.manualentry.ManualEntryType
import ai.friday.billpayment.app.recurrence.RecurrenceFrequency
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.recurrence.RecurrenceStatus
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.modules.manualentry.app.recurrence.ManualEntryRecurrence
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime

val MANUAL_ENTRY = ManualEntry(
    id = ManualEntryId("TEST-MANUAL-ENTRY"),
    walletId = WalletId(WALLET_ID),
    title = "Test manual entry",
    amount = 12345L,
    dueDate = getLocalDate(),
    type = ManualEntryType.EXTERNAL_PAYMENT,
    status = ManualEntryStatus.PAID,
    source = ActionSource.Api(accountId = AccountId(ACCOUNT_ID)),
    categorySuggestions = listOf(),
    createdAt = getZonedDateTime(),
    updatedAt = getZonedDateTime(),
)

val weeklyManualEntryRecurrenceNoEndDate = ManualEntryRecurrence(
    id = RecurrenceId("RECURRENCE-ID"),
    walletId = WalletId(WALLET_ID),
    rule = RecurrenceRule(frequency = RecurrenceFrequency.WEEKLY, startDate = getZonedDateTime().toLocalDate()),
    actionSource = ActionSource.Api(
        accountId = AccountId(ACCOUNT_ID),
        originalBillId = BillId("13"),
    ),
    created = getZonedDateTime(),
    status = RecurrenceStatus.ACTIVE,
    manualEntries = listOf(),
    type = ManualEntryType.EXTERNAL_PAYMENT,
    title = "Recurrence title",
    description = "fake description",
    amount = 100L,
)

val monthlyIncomeRecurrence = ManualEntryRecurrence(
    id = RecurrenceId("RECURRENCE-ID"),
    walletId = WalletId(WALLET_ID),
    rule = RecurrenceRule(frequency = RecurrenceFrequency.MONTHLY, startDate = getZonedDateTime().toLocalDate()),
    actionSource = ActionSource.Api(
        accountId = AccountId(ACCOUNT_ID),
        originalBillId = BillId("14"),
    ),
    created = getZonedDateTime(),
    status = RecurrenceStatus.ACTIVE,
    manualEntries = listOf(),
    type = ManualEntryType.INCOME,
    title = "Income recurrence title",
    description = "Income fake description",
    amount = 100L,
)