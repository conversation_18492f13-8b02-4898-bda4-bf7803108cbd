package ai.friday.billpayment.modules.manualentry.integration.services

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletBillCategoryDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletBillCategoryDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.adapters.micronaut.RecurrenceConfigurationMicronaut
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.manualentry.ManualEntry
import ai.friday.billpayment.app.manualentry.ManualEntryId
import ai.friday.billpayment.app.manualentry.ManualEntryStatus
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.recurrence.RecurrenceCreationError
import ai.friday.billpayment.app.recurrence.RecurrenceFrequency
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.recurrence.RecurrenceStatus
import ai.friday.billpayment.app.recurrence.generateDateSequence
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.billCategory
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.modules.manualentry.MANUAL_ENTRY
import ai.friday.billpayment.modules.manualentry.adapters.dynamodb.ManualEntryDbRepository
import ai.friday.billpayment.modules.manualentry.adapters.dynamodb.ManualEntryDynamoDAO
import ai.friday.billpayment.modules.manualentry.adapters.dynamodb.ManualEntryRecurrenceDBRepository
import ai.friday.billpayment.modules.manualentry.adapters.dynamodb.ManualEntryRecurrenceDynamoDAO
import ai.friday.billpayment.modules.manualentry.app.recurrence.ManualEntryRecurrence
import ai.friday.billpayment.modules.manualentry.app.recurrence.ManualEntryRecurrenceService
import ai.friday.billpayment.modules.manualentry.monthlyIncomeRecurrence
import ai.friday.billpayment.modules.manualentry.weeklyManualEntryRecurrenceNoEndDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.dateFormat
import arrow.core.getOrElse
import io.kotest.inspectors.forAll
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import java.time.LocalDate
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.ValueSource

class ManualEntryRecurrenceServiceIntegrationTest {
    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val walletFixture =
        WalletFixture(founderAccountId = AccountId(ACCOUNT_ID), defaultWalletId = WalletId(WALLET_ID))
    private val wallet = walletFixture.buildWallet()

    private val recurrenceRepository = ManualEntryRecurrenceDBRepository(ManualEntryRecurrenceDynamoDAO(dynamoDbEnhancedClient), "2021-12-31")

    private val categoryDbRepository =
        WalletBillCategoryDbRepository(WalletBillCategoryDynamoDAO(dynamoDbEnhancedClient))

    private val walletDAO = WalletDynamoDAO(dynamoDbEnhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(dynamoDbEnhancedClient)
    private val inviteDAO = InviteDynamoDAO(dynamoDbEnhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(dynamoDbEnhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(dynamoDbEnhancedClient)

    private val walletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = mockk(),
    )

    private val fakeLastLimitDate: LocalDate = LocalDate.of(2020, 12, 31)
    private val fakeLimitDate: LocalDate =
        LocalDate.now().plusYears(2)
            .withDayOfMonth(LocalDate.now().month.length(LocalDate.now().plusYears(2).isLeapYear))

    private val recurrenceConfiguration =
        RecurrenceConfigurationMicronaut(
            lastLimitDate = fakeLastLimitDate.format(dateFormat),
            limitDate = fakeLimitDate.format(dateFormat),
        )

    private val accountRepository: AccountRepository =
        mockk {
            every {
                findById(walletFixture.founderAccount.accountId)
            } returns walletFixture.founderAccount
        }

    private val manualEntryRepository = ManualEntryDbRepository(ManualEntryDynamoDAO(dynamoDbEnhancedClient))

    private val recurrenceService =
        ManualEntryRecurrenceService(
            manualEntryRecurrenceRepository = recurrenceRepository,
            recurrenceConfiguration = recurrenceConfiguration,
            accountRepository = accountRepository,
            walletRepository = walletRepository,
            manualEntryRepository = manualEntryRepository,
        )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        walletRepository.save(wallet)
    }

    @Test
    fun `should return illegal argument exception when start date gt limit date`() {
        val recurrence = buildRecurrence(startDate = fakeLimitDate.plusDays(1))
        val createdRecurrence = recurrenceService.create(recurrence, false)

        createdRecurrence.isLeft() shouldBe true
        createdRecurrence.mapLeft {
            it shouldBe RecurrenceCreationError.DueDateAfterLimit
        }
    }

    @ParameterizedTest
    @ValueSource(ints = [1, 2, 3])
    fun `should create weekly recurrence with any number of manual entries`(expectedCreatedBillsSize: Int) {
        val weeksToSubstract: Long = expectedCreatedBillsSize - 1L
        val recurrence = buildRecurrence(startDate = fakeLimitDate.minusWeeks(weeksToSubstract))

        val completedRecurrence =
            recurrenceService.doCreate(recurrence, recurrence.rule.generateDateSequence(limitDate = fakeLimitDate))
                .toCompletableFuture().get()

        completedRecurrence.manualEntries.size shouldBe expectedCreatedBillsSize
        recurrenceRepository.find(recurrence.id, recurrence.walletId) shouldBe completedRecurrence
        val manualEntries = manualEntryRepository.findAllByWallet(recurrence.walletId)
        manualEntries.size shouldBe expectedCreatedBillsSize
        manualEntries.forEachIndexed { index, it ->
            it.amount shouldBe recurrence.amount
            it.dueDate shouldBe recurrence.rule.startDate.plusWeeks(index.toLong())
            it.title shouldBe recurrence.title
            it.description shouldBe recurrence.description
            it.type shouldBe recurrence.type
            it.walletId.value shouldBe recurrence.walletId.value
            it.recurrenceRule shouldBe recurrence.rule
            it.source shouldBe
                ActionSource.WalletRecurrence(
                    recurrenceId = recurrence.id,
                    accountId = walletFixture.founder.accountId,
                )
        }
    }

    @Test
    fun `should create income recurrence`() {
        val startDate = LocalDate.of(2024, 1, 15)
        val endDate = LocalDate.of(2025, 1, 14)
        val expectedCreatedBillsSize = 12

        val recurrence = monthlyIncomeRecurrence.copy(
            rule = RecurrenceRule(
                frequency = RecurrenceFrequency.MONTHLY,
                startDate = startDate,
                endDate = endDate,
            ),
        )

        val completedRecurrence =
            recurrenceService.doCreate(recurrence, recurrence.rule.generateDateSequence(limitDate = endDate))
                .toCompletableFuture().get()

        completedRecurrence.manualEntries.size shouldBe expectedCreatedBillsSize
        recurrenceRepository.find(recurrence.id, recurrence.walletId) shouldBe completedRecurrence
        val manualEntries = manualEntryRepository.findAllByWallet(recurrence.walletId)
        manualEntries.size shouldBe expectedCreatedBillsSize
        manualEntries.forEachIndexed { index, it ->
            it.amount shouldBe recurrence.amount
            it.dueDate shouldBe recurrence.rule.startDate.plusMonths(index.toLong())
            it.title shouldBe recurrence.title
            it.description shouldBe recurrence.description
            it.type shouldBe recurrence.type
            it.walletId.value shouldBe recurrence.walletId.value
            it.recurrenceRule shouldBe recurrence.rule
            it.status shouldBe ManualEntryStatus.PAID
            it.source shouldBe
                ActionSource.WalletRecurrence(
                    recurrenceId = recurrence.id,
                    accountId = walletFixture.founder.accountId,
                )
        }
    }

    @Test
    fun `should create weekly recurrence with manual entries until end date`() {
        val start = 10L
        val end = 4L
        val total = start - end + 1

        val recurrence =
            buildRecurrence(startDate = fakeLimitDate.minusWeeks(start), endDate = fakeLimitDate.minusWeeks(end))

        val completedRecurrence =
            recurrenceService.doCreate(recurrence, recurrence.rule.generateDateSequence(limitDate = fakeLimitDate))
                .toCompletableFuture().get()

        completedRecurrence.manualEntries.size shouldBe total
        recurrenceRepository.find(recurrence.id, recurrence.walletId) shouldBe completedRecurrence
        val manualEntries = manualEntryRepository.findAllByWallet(recurrence.walletId)
        manualEntries.size shouldBe total
    }

    @Test
    fun `should create biweekly recurrence with manual entries until end date`() {
        val start = 6L
        val end = 2L
        val total = 3

        val recurrence =
            buildRecurrence(
                frequency = RecurrenceFrequency.BIWEEKLY,
                startDate = fakeLimitDate.minusWeeks(start),
                endDate = fakeLimitDate.minusWeeks(end),
            )

        val completedRecurrence =
            recurrenceService.doCreate(recurrence, recurrence.rule.generateDateSequence(limitDate = fakeLimitDate))
                .toCompletableFuture().get()

        completedRecurrence.manualEntries.size shouldBe total
        recurrenceRepository.find(recurrence.id, recurrence.walletId) shouldBe completedRecurrence
        val manualEntries = manualEntryRepository.findAllByWallet(recurrence.walletId)
        manualEntries.size shouldBe total
    }

    @Test
    fun `deve criar recorrência em qualquer dia do mês`() {
        val monthsToSubtract: Long = 0
        val recurrence =
            buildRecurrence(
                frequency = RecurrenceFrequency.MONTHLY,
                startDate = fakeLimitDate.minusMonths(monthsToSubtract).withDayOfMonth(28),
            )

        val completedRecurrence =
            recurrenceService.create(recurrence, false).getOrElse {
                fail(it.message)
            }

        val expectedCreatedBillsSize = 1

        completedRecurrence.recurrence.manualEntries.size shouldBe expectedCreatedBillsSize
        recurrenceRepository.find(recurrence.id, recurrence.walletId).rule shouldBe completedRecurrence.recurrence.rule

        val manualEntries = manualEntryRepository.findAllByWallet(recurrence.walletId)
        manualEntries.size shouldBe expectedCreatedBillsSize
        manualEntries.forEachIndexed { index, it ->
            it.amount shouldBe recurrence.amount
            it.dueDate shouldBe recurrence.rule.startDate.plusMonths(index.toLong())
            it.title shouldBe recurrence.title
            it.description shouldBe recurrence.description
            it.type shouldBe recurrence.type
            it.walletId.value shouldBe recurrence.walletId.value
            it.source shouldBe
                ActionSource.WalletRecurrence(
                    recurrenceId = recurrence.id,
                    accountId = walletFixture.founder.accountId,
                )
        }
    }

    @ParameterizedTest
    @ValueSource(ints = [1, 2, 3, 13])
    fun `should create monthly recurrence with any number of manual entries`(expectedCreatedBillsSize: Int) {
        val monthsToSubtract: Long = expectedCreatedBillsSize - 1L
        val recurrence =
            buildRecurrence(
                frequency = RecurrenceFrequency.MONTHLY,
                startDate = fakeLimitDate.minusMonths(monthsToSubtract).withDayOfMonth(28),
            )

        val completedRecurrence =
            recurrenceService.doCreate(recurrence, recurrence.rule.generateDateSequence(limitDate = fakeLimitDate))
                .toCompletableFuture().get()

        completedRecurrence.manualEntries.size shouldBe expectedCreatedBillsSize
        recurrenceRepository.find(recurrence.id, recurrence.walletId) shouldBe completedRecurrence

        val manualEntries = manualEntryRepository.findAllByWallet(recurrence.walletId)
        manualEntries.size shouldBe expectedCreatedBillsSize
        manualEntries.forEachIndexed { index, it ->
            it.amount shouldBe recurrence.amount
            it.dueDate shouldBe recurrence.rule.startDate.plusMonths(index.toLong())
            it.description shouldBe recurrence.description
            it.type shouldBe recurrence.type
            it.title shouldBe recurrence.title
            it.walletId.value shouldBe recurrence.walletId.value
            it.source shouldBe
                ActionSource.WalletRecurrence(
                    recurrenceId = recurrence.id,
                    accountId = walletFixture.founder.accountId,
                )
        }
    }

    @Test
    fun `should not create any manual entry when there is only ignored recurrence`() {
        val recurrence =
            buildRecurrence(
                frequency = RecurrenceFrequency.WEEKLY,
                startDate = fakeLimitDate,
            ).copy(status = RecurrenceStatus.IGNORED)

        recurrenceRepository.save(recurrence)

        recurrenceService.continueRecurrences()

        val savedRecurrence = recurrenceRepository.find(recurrence.id, recurrence.walletId)

        savedRecurrence.manualEntries.shouldBeEmpty()
    }

    @Test
    fun `should not create any manual entry when there is only active recurrence with endDate before lastLimitDate`() {
        val recurrence =
            buildRecurrence(
                frequency = RecurrenceFrequency.WEEKLY,
                startDate = fakeLastLimitDate.minusDays(1),
                endDate = fakeLastLimitDate,
            )

        recurrenceRepository.save(recurrence)

        recurrenceService.continueRecurrences()

        val savedRecurrence = recurrenceRepository.find(recurrence.id, recurrence.walletId)

        savedRecurrence.manualEntries.shouldBeEmpty()
    }

    @Test
    fun `should create manual entry when there is active recurrence with endDate after lastDueDate`() {
        val recurrence =
            buildRecurrence(
                frequency = RecurrenceFrequency.WEEKLY,
                startDate = fakeLastLimitDate.minusDays(1),
                endDate = fakeLastLimitDate.plusWeeks(1),
            )
                .setLastDueDate(fakeLastLimitDate.minusDays(1))

        recurrenceRepository.save(recurrence)

        recurrenceService.continueRecurrences()

        val savedRecurrence = recurrenceRepository.find(recurrence.id, recurrence.walletId)

        savedRecurrence.manualEntries.shouldNotBeEmpty()
        val manualEntries = manualEntryRepository.findAllByWallet(recurrence.walletId)
        manualEntries.size shouldBe 1
        manualEntries.first().dueDate shouldBe recurrence.rule.startDate.plusWeeks(1)
        savedRecurrence.lastDueDate shouldBe manualEntries.last().dueDate
    }

    @Test
    fun `deve salvar categoria nos pagamentos quando há uma categoria na recorrencia`() {
        val recurrence = buildRecurrence(
            frequency = RecurrenceFrequency.WEEKLY,
            startDate = fakeLastLimitDate.minusDays(1),
            endDate = fakeLastLimitDate.plusWeeks(1),
            billCategoryId = billCategory.categoryId,
        )
            .setLastDueDate(fakeLastLimitDate.minusDays(1))

        categoryDbRepository.save(
            WalletBillCategory(
                walletId = WalletId(WALLET_ID),
                categoryId = billCategory.categoryId,
                name = billCategory.name,
                icon = billCategory.icon,
                enabled = true,
                default = billCategory.default,
            ),
        )
        recurrenceRepository.save(recurrence)

        recurrenceService.continueRecurrences()

        val savedRecurrence = recurrenceRepository.find(recurrence.id, recurrence.walletId)

        savedRecurrence.manualEntries.forAll {
            val manualEntry = manualEntryRepository.findById(it, recurrence.walletId)
            manualEntry.categoryId.shouldNotBeNull()
        }
    }

    @Test
    fun `should not create bill when there is active recurrence with endDate after lastLimitDate but bill was already created`() {
        val recurrence =
            buildRecurrence(
                frequency = RecurrenceFrequency.WEEKLY,
                startDate = fakeLastLimitDate.minusDays(1),
                endDate = fakeLastLimitDate.plusWeeks(1),
            )
        recurrenceRepository.save(recurrence)

        val (_, firstManualEntry) = addManualEntryOnRecurrence(recurrence, recurrence.rule.startDate.plusWeeks(1))

        recurrenceService.continueRecurrences()

        val savedRecurrence = recurrenceRepository.find(recurrence.id, recurrence.walletId)

        savedRecurrence.manualEntries.shouldNotBeEmpty()
        val manualEntries = manualEntryRepository.findAllByWallet(recurrence.walletId)
        manualEntries.size shouldBe 1
        with(manualEntries.first()) {
            dueDate shouldBe firstManualEntry.dueDate
            id shouldBe firstManualEntry.id
        }
    }

    @ParameterizedTest
    @EnumSource(AccountStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["ACTIVE"])
    fun `não deve considerar uma recorrência ativa de uma conta que não está ativa`(accountStatus: AccountStatus) {
        every {
            accountRepository.findById(walletFixture.founderAccount.accountId)
        } returns walletFixture.founderAccount.copy(status = accountStatus)

        val recurrence =
            buildRecurrence(
                frequency = RecurrenceFrequency.WEEKLY,
                startDate = fakeLastLimitDate.minusDays(1),
                endDate = fakeLastLimitDate.plusWeeks(1),
            )
                .setLastDueDate(fakeLastLimitDate.minusDays(1))

        recurrenceRepository.save(recurrence)

        recurrenceService.continueRecurrences()

        val savedRecurrence = recurrenceRepository.find(recurrence.id, recurrence.walletId)

        savedRecurrence.manualEntries.shouldBeEmpty()
        val manualEntries = manualEntryRepository.findAllByWallet(recurrence.walletId)
        manualEntries.size shouldBe 0
    }

    @Test
    fun `deve retornar todas os lançamentos manuais futuros de uma recorrência`() {
        val currentDate = getLocalDate()

        val recurrence = buildRecurrence(startDate = currentDate.minusWeeks(1))
        recurrenceRepository.save(recurrence)

        val (recurrence1, _) = addManualEntryOnRecurrence(
            recurrence,
            currentDate.minusWeeks(1),
            manualEntryId = ManualEntryId("PREVIOUS-ID"),
        )
        val (recurrence2, currentEntry) = addManualEntryOnRecurrence(
            recurrence1,
            currentDate,
            manualEntryId = ManualEntryId("CURRENT-ID"),
        )
        val (recurrence3, nextEntry1) = addManualEntryOnRecurrence(
            recurrence2,
            currentDate.plusWeeks(1),
            manualEntryId = ManualEntryId("FUTURE-ID-1"),
        )
        val (_, nextEntry2) = addManualEntryOnRecurrence(
            recurrence3,
            currentDate.plusWeeks(2),
            manualEntryId = ManualEntryId("FUTURE-ID-2"),
        )

        val result = recurrenceService.getThisAndFuture(currentEntry)

        result shouldBe listOf(currentEntry.id, nextEntry1.id, nextEntry2.id)
    }

    @Test
    fun `deve atualizar recorrência de acordo com um lançamento manual`() {
        val recurrence = buildRecurrence(startDate = getLocalDate()).copy(
            title = "Old title",
            description = "Old description",
            amount = 123456L,
            categoryId = PFMCategoryId("OLD-CATEGORY"),
        )

        recurrenceRepository.save(recurrence)

        val (_, manualEntry) = addManualEntryOnRecurrence(recurrence, getLocalDate())

        recurrenceService.updateRecurrence(
            manualEntry.copy(
                title = "New title",
                description = "New description",
                amount = 456789L,
                categoryId = PFMCategoryId("NEW-CATEGORY"),
            ),
        )

        val recurrenceFromDb = recurrenceRepository.find(recurrence.id, recurrence.walletId)

        with(recurrenceFromDb) {
            title shouldBe "New title"
            description shouldBe "New description"
            amount shouldBe 456789L
            categoryId shouldBe PFMCategoryId("NEW-CATEGORY")
        }
    }

    @Test
    fun `deve ignorar recorrência se for atualizada com um lançamento ignorado`() {
        val recurrence = buildRecurrence(startDate = getLocalDate())
        recurrenceRepository.save(recurrence)

        val (_, manualEntry) = addManualEntryOnRecurrence(recurrence, getLocalDate())

        recurrenceService.updateRecurrence(
            manualEntry.copy(
                status = ManualEntryStatus.IGNORED,
            ),
        )

        val recurrenceFromDb = recurrenceRepository.find(recurrence.id, recurrence.walletId)

        with(recurrenceFromDb) {
            status shouldBe RecurrenceStatus.IGNORED
        }
    }

    private fun addManualEntryOnRecurrence(
        recurrence: ManualEntryRecurrence,
        recurrentDate: LocalDate,
        manualEntryStatus: ManualEntryStatus = ManualEntryStatus.ACTIVE,
        manualEntryId: ManualEntryId? = null,
    ): Pair<ManualEntryRecurrence, ManualEntry> {
        val actionSource = ActionSource.WalletRecurrence(recurrenceId = recurrence.id, accountId = AccountId(wallet.id.value))

        val manualEntry = MANUAL_ENTRY.copy(
            id = manualEntryId ?: ManualEntryId(),
            title = recurrence.title,
            description = recurrence.description,
            amount = recurrence.amount,
            type = recurrence.type,
            categoryId = recurrence.categoryId,
            status = manualEntryStatus,
            dueDate = recurrentDate,
            source = actionSource,
        )

        manualEntryRepository.save(manualEntry)

        val recurrenceNew = recurrence.plusManualEntryId(manualEntry.id)
        recurrenceRepository.save(recurrenceNew)

        return Pair(recurrenceNew, manualEntry)
    }

    private fun buildRecurrence(
        frequency: RecurrenceFrequency = RecurrenceFrequency.WEEKLY,
        startDate: LocalDate,
        endDate: LocalDate? = null,
        billCategoryId: PFMCategoryId? = null,
    ): ManualEntryRecurrence {
        return weeklyManualEntryRecurrenceNoEndDate.copy(
            categoryId = billCategoryId,
            rule = RecurrenceRule(frequency = frequency, startDate = startDate, endDate = endDate),
            walletId = wallet.id,
        )
    }
}