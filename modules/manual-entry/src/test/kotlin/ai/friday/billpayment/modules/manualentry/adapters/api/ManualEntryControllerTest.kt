package ai.friday.billpayment.modules.manualentry.adapters.api

import ai.friday.billpayment.adapters.api.RecurrenceRequestTO
import ai.friday.billpayment.adapters.api.getWallet
import ai.friday.billpayment.app.manualentry.ManualEntry
import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.app.manualentry.ManualEntryType
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.recurrence.RecurrenceFrequency
import ai.friday.billpayment.app.recurrence.RecurrenceResult
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.manualentry.adapters.api.ManualEntryController.CreateIncomeTO
import ai.friday.billpayment.modules.manualentry.app.recurrence.ManualEntryRecurrence
import ai.friday.billpayment.modules.manualentry.app.recurrence.ManualEntryRecurrenceService
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpStatus
import io.micronaut.security.authentication.Authentication
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

class ManualEntryControllerTest {

    private val manualEntryService = mockk<ManualEntryService>(relaxed = true)
    private val manualEntryRecurrenceService = mockk<ManualEntryRecurrenceService>(relaxed = true)
    private val walletBillCategoryService = mockk<PFMWalletCategoryService>(relaxed = true)

    private val controller = ManualEntryController(
        manualEntryService = manualEntryService,
        manualEntryRecurrenceService = manualEntryRecurrenceService,
        walletBillCategoryService = walletBillCategoryService,
    )
    private val participant = WalletFixture().ultraLimitedParticipant
    private val wallet = WalletFixture().buildWallet(otherMembers = listOf(participant))
    private val auth = mockk<Authentication> {
        every { getWallet() } returns wallet
        every { name } returns wallet.founder.accountId.value
    }

    @Test
    fun `deve criar income não recorrente`() {
        val income = CreateIncomeTO(
            categoryId = null,
            title = "Teste",
            amount = 12345L,
            dueDate = "2024-05-15",
            recurrence = null,
            description = "",
        )

        every {
            manualEntryService.create(any(), any(), any(), any(), any(), any(), ManualEntryType.INCOME, any(), any())
        } returns mockk<ManualEntry>().right()

        val response = controller.createIncome(request = income, authentication = auth)
        response.status shouldBe HttpStatus.CREATED

        verify {
            manualEntryService.create(any(), any(), any(), any(), any(), any(), ManualEntryType.INCOME, any(), any())
            manualEntryRecurrenceService wasNot called
        }
    }

    @Test
    fun `deve criar income recorrente`() {
        val income = CreateIncomeTO(
            categoryId = null,
            title = "Teste",
            amount = 12345L,
            dueDate = "2024-05-15",
            recurrence = RecurrenceRequestTO(
                frequency = RecurrenceFrequency.WEEKLY,
            ),
            description = "",
        )

        every {
            manualEntryRecurrenceService.create(any(), any())
        } returns mockk<RecurrenceResult<ManualEntryRecurrence>>().right()

        val response = controller.createIncome(request = income, authentication = auth)

        response.status shouldBe HttpStatus.CREATED

        verify {
            manualEntryService wasNot called
            manualEntryRecurrenceService.create(any(), any())
        }
    }
}