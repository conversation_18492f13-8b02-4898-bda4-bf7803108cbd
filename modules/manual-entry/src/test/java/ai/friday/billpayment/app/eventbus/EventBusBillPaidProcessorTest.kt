package ai.friday.billpayment.app.eventbus

import DynamoDBUtils
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.CreditCardChargebackTrackDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbTransactionRepository
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardBrand
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.metrics.push
import ai.friday.billpayment.app.payment.BoletoSettlementResult
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.SinglePaymentData
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.uuid
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.createTransaction
import ai.friday.billpayment.defaultTransactionEntityConverter
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.createLockTable
import ai.friday.billpayment.toEpochMillis
import ai.friday.billpayment.withMockedMetric
import io.kotest.matchers.result.shouldBeSuccess
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import java.math.BigInteger
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class EventBusBillPaidProcessorTest {

    @Test
    fun `should publish PaymentEvent on EventBus when bill is paid using credit card`() {
        val authenticationCode = uuid()
        val accountId = AccountId()
        val billId = BillId()
        val walletId = WalletId()
        val externalId = ExternalId(value = uuid(), AccountProviderName.MOTOROLA)
        val paymentMethodId = AccountPaymentMethodId(value = uuid())
        val transactionId = TransactionId.build()

        val installments = 11
        val originalAmount = 10000L
        val interest = 300L
        val fine = 500L
        val fee = 800L
        val amountTotal = 10800L

        val cc = CreditCard(
            CreditCardBrand.VISA,
            "455187******0183",
            "12/2099",
            "777",
            binDetails = null,
            riskLevel = RiskLevel.LOW,
        )

        val messageId = uuid()

        val account = ACCOUNT.copy(
            accountId = accountId,
            configuration = ACCOUNT.configuration.copy(
                externalId = externalId,
            ),
        )

        val accountPaymentMethod = AccountPaymentMethod(
            id = paymentMethodId,
            accountId = accountId,
            method = cc,
        )

        val billPaidEvent = billPaid.copy(
            billId = billId,
            walletId = walletId,
            transactionId = transactionId,
        )

        val bill = Bill.build(
            billAdded.copy(
                billId = billId,
                walletId = walletId,
                amount = originalAmount,
                amountTotal = amountTotal,
                interest = interest,
                fine = fine,
            ),
            billPaidEvent,
        ).also {
            billRepository.save(it)
            it.history.onEach(eventRepository::save)
        }

        accountRepository.save(account)
        accountRepository.createAccountPaymentMethod(
            accountId = accountId,
            paymentMethodId = paymentMethodId,
            creditCard = cc,
            position = 1,
            status = AccountPaymentMethodStatus.ACTIVE,
        )

        val wallet = WalletFixture(
            founderAccountId = accountId,
            defaultWalletId = walletId,
            founderDocument = account.document,
        ).buildWallet()

        walletRepository.save(wallet)

        val transaction = createTransaction(
            account = account,
            transactionId = transactionId,
            walletId = walletId,
            paymentData = SinglePaymentData(
                accountPaymentMethod = accountPaymentMethod,
                details = PaymentMethodsDetailWithCreditCard(
                    paymentMethodId = paymentMethodId,
                    netAmount = bill.amountTotal,
                    feeAmount = fee,
                    installments = installments,
                    fee = fee.toDouble(),
                    calculationId = null,
                ),
                payment = null,
            ),
            settlementData = SettlementData(
                settlementTarget = bill,
                serviceAmountTax = 0,
                totalAmount = bill.amountTotal,
                settlementOperation = BoletoSettlementResult(
                    gateway = FinancialServiceGateway.FRIDAY,
                    status = BoletoSettlementStatus.CONFIRMED,
                    bankTransactionId = "312312",
                    externalNsu = 1,
                    externalTerminal = "********",
                    errorCode = "",
                    errorDescription = null,
                    authentication = authenticationCode,
                    paymentPartnerName = "ARBI",
                    finalPartnerName = FinancialServiceGateway.CELCOIN,
                    errorRetryable = false,
                ),
            ),
        )

        transactionRepository.save(transaction)

        every { eventBusPublisher.publish(any()) } returns Result.success(EventBus.MessageId(messageId))

        // when
        val result = withMockedMetric(BillPaidSummaryMetric) {
            val result = processor.process(billPaidEvent)
            verify { BillPaidSummaryMetric.push(any(), any<Number>()) }
            result
        }

        // then
        result.shouldBeSuccess().value shouldBe messageId
        val eventSlot = slot<EventBus.Message>()

        verify { eventBusPublisher.publish(capture(eventSlot)) }

        val e = eventSlot.captured.shouldBeInstanceOf<EventBusMessage.PaymentEvent>()

        e.transaction.let {
            it.authenticationCode shouldBe authenticationCode
            it.billId shouldBe bill.billId.value
            it.payer.document shouldBe account.document
            it.payer.accountId shouldBe accountId.value
            it.payer.walletId shouldBe walletId.value
            it.payer.partnerId shouldBe externalId.value
            it.transactionId shouldBe transactionId.value
        }

        e.bill.let {
            it.amount shouldBe bill.amount
            it.amountTotal shouldBe bill.amountTotal
            it.billType shouldBe bill.billType
            it.barcode shouldBe bill.barcode?.number
            it.digitable shouldBe bill.barcode?.digitable
            it.issuer shouldBe bill.assignor.orEmpty()
            it.dueDate shouldBe bill.dueDate
            it.effectiveDueDate shouldBe bill.effectiveDueDate
            it.recipientDocument shouldBe bill.recipient?.document.orEmpty()
            it.recipientName shouldBe bill.recipient?.name.orEmpty()
            it.discount shouldBe bill.discount
            it.fine shouldBe bill.fine
            it.interest shouldBe bill.interest
        }

        e.payments.first().let {
            it.amountPaid shouldBe bill.amountTotal
            it.paidAt.toEpochMillis() shouldBe billPaidEvent.created
            it.type shouldBe accountPaymentMethod.method.type
        }
    }

    @Test
    fun `should publish PaymentEvent on EventBus when bill is paid using balance`() {
        val authenticationCode = uuid()
        val accountId = AccountId()
        val billId = BillId()
        val walletId = WalletId()
        val externalId = ExternalId(value = uuid(), AccountProviderName.MOTOROLA)

        val transactionId = TransactionId.build()

        val originalAmount = 10000L
        val interest = 300L
        val fine = 500L
        val amountTotal = 10800L

        val bankAccount = InternalBankAccount(
            accountType = AccountType.PAYMENT,
            bankNo = 1,
            routingNo = 2,
            accountNo = 3456,
            accountDv = "7",
            document = ACCOUNT.document,
            bankAccountMode = BankAccountMode.PHYSICAL,
        )

        val messageId = uuid()

        val account = ACCOUNT.copy(
            accountId = accountId,
            configuration = ACCOUNT.configuration.copy(
                externalId = externalId,
            ),
        )

        val billPaidEvent = billPaid.copy(
            billId = billId,
            walletId = walletId,
            transactionId = transactionId,
        )

        val bill = Bill.build(
            billAdded.copy(
                billId = billId,
                walletId = walletId,
                amount = originalAmount,
                amountTotal = amountTotal,
                interest = interest,
                fine = fine,
            ),
            billPaidEvent,
        ).also {
            billRepository.save(it)
            it.history.onEach(eventRepository::save)
        }

        accountRepository.save(account)

        val accountPaymentMethod = accountRepository.createAccountPaymentMethod(
            accountId = accountId,
            bankAccount = BankAccount(
                accountType = bankAccount.accountType,
                bankNo = bankAccount.bankNo,
                routingNo = bankAccount.routingNo,
                accountNo = BigInteger.valueOf(bankAccount.accountNo),
                accountDv = bankAccount.accountDv,
                document = bankAccount.document,
            ),
            position = 1,
        )

        val paymentMethodId = accountPaymentMethod.id

        val wallet = WalletFixture(
            founderAccountId = accountId,
            defaultWalletId = walletId,
            founderDocument = account.document,
        ).buildWallet()

        walletRepository.save(wallet)

        val transaction = createTransaction(
            account = account,
            transactionId = transactionId,
            walletId = walletId,
            paymentData = SinglePaymentData(
                accountPaymentMethod = accountPaymentMethod,
                details = PaymentMethodsDetailWithBalance(
                    amount = bill.amountTotal,
                    paymentMethodId = paymentMethodId,
                    calculationId = null,
                ),
                payment = null,
            ),
            settlementData = SettlementData(
                settlementTarget = bill,
                serviceAmountTax = 0,
                totalAmount = bill.amountTotal,
                settlementOperation = BoletoSettlementResult(
                    gateway = FinancialServiceGateway.FRIDAY,
                    status = BoletoSettlementStatus.CONFIRMED,
                    bankTransactionId = "312312",
                    externalNsu = 1,
                    externalTerminal = "********",
                    errorCode = "",
                    errorDescription = null,
                    authentication = authenticationCode,
                    paymentPartnerName = "ARBI",
                    finalPartnerName = FinancialServiceGateway.CELCOIN,
                    errorRetryable = false,
                ),
            ),
        )

        transactionRepository.save(transaction)

        every { eventBusPublisher.publish(any()) } returns Result.success(EventBus.MessageId(messageId))

        // when
        val result = processor.process(billPaidEvent)

        // then
        result.shouldBeSuccess().value shouldBe messageId
        val eventSlot = slot<EventBus.Message>()

        verify { eventBusPublisher.publish(capture(eventSlot)) }

        val e = eventSlot.captured.shouldBeInstanceOf<EventBusMessage.PaymentEvent>()

        e.transaction.let {
            it.authenticationCode shouldBe authenticationCode
            it.billId shouldBe bill.billId.value
            it.payer.document shouldBe account.document
            it.payer.accountId shouldBe accountId.value
            it.payer.walletId shouldBe walletId.value
            it.payer.partnerId shouldBe externalId.value
            it.transactionId shouldBe transactionId.value
        }

        e.bill.let {
            it.amount shouldBe bill.amount
            it.amountTotal shouldBe bill.amountTotal
            it.billType shouldBe bill.billType
            it.barcode shouldBe bill.barcode?.number
            it.digitable shouldBe bill.barcode?.digitable
            it.issuer shouldBe bill.assignor.orEmpty()
            it.dueDate shouldBe bill.dueDate
            it.effectiveDueDate shouldBe bill.effectiveDueDate
            it.recipientDocument shouldBe bill.recipient?.document.orEmpty()
            it.recipientName shouldBe bill.recipient?.name.orEmpty()
            it.discount shouldBe bill.discount
            it.fine shouldBe bill.fine
            it.interest shouldBe bill.interest
        }

        e.payments.first().let {
            it.amountPaid shouldBe bill.amountTotal
            it.paidAt.toEpochMillis() shouldBe billPaidEvent.created
            it.type shouldBe accountPaymentMethod.method.type
        }
    }

    @BeforeEach
    fun beforeEach() = setup()

    private companion object {
        val enhancedClient = DynamoDBUtils.setupDynamoDB()
        val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
        val billEventDAO = BillEventDynamoDAO(enhancedClient)
        val uniqueConstraintDAO = UniqueConstraintDynamoDAO(enhancedClient)

        val transactionDynamo = TransactionDynamo(enhancedClient)
        val transactionDAO = TransactionDynamoDAO(enhancedClient)
        val creditCardChargebackTrackDAO = CreditCardChargebackTrackDynamoDAO(enhancedClient)

        val eventRepository = spyk(
            BillEventDBRepository(
                billEventDAO = billEventDAO,
                uniqueConstraintDAO = uniqueConstraintDAO,
                featureConfiguration = mockk(),
                transactionDynamo = transactionDynamo,
            ),
        )
        val billRepository = spyk(
            DynamoDbBillRepository(
                billClient = BillDynamoDAO(enhancedClient),
                refundedClient = RefundedBillDynamoDAO(enhancedClient),
                settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(enhancedClient),
            ),
        )

        val accountDAO = AccountDynamoDAO(enhancedClient)
        val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
        val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
        val nsuDAO = NSUDynamoDAO(enhancedClient)

        val accountRepository = spyk(
            AccountDbRepository(
                accountDAO = accountDAO,
                partialAccountDAO = partialAccountDAO,
                paymentMethodDAO = paymentMethodDAO,
                nsuDAO = nsuDAO,
                transactionDynamo = transactionDynamo,
            ),
        )

        val walletDAO = WalletDynamoDAO(enhancedClient)
        val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
        val inviteDAO = InviteDynamoDAO(enhancedClient)
        val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
        val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

        val walletRepository = spyk(
            WalletDbRepository(
                walletDAO = walletDAO,
                walletMemberDAO = walletMemberDAO,
                inviteDAO = inviteDAO,
                inviteReminderDAO = inviteReminderDAO,
                walletLimitDAO = walletLimitDAO,
                accountRepository = accountRepository,
            ),
        )

        private val transactionRepository = spyk(
            DynamoDbTransactionRepository(
                transactionDAO = transactionDAO,
                creditCardChargebackTrackDAO = creditCardChargebackTrackDAO,
                accountRepository = accountRepository,
                transactionEntityConverter = defaultTransactionEntityConverter(eventRepository, walletRepository, transactionDynamo),
            ),
        )

        val eventBusPublisher = mockk<EventBus.Publisher>()

        val processor = EventBusBillPaidProcessor(
            accountRepository = accountRepository,
            billRepository = billRepository,
            transactionRepository = transactionRepository,
            walletRepository = walletRepository,
            eventBusPublisher = eventBusPublisher,
        )

        fun setup() {
            clearAllMocks()
            createBillPaymentTable(dynamoDB)
            createBillEventTable(dynamoDB)
            createLockTable(dynamoDB, "Shedlock")
        }
    }
}