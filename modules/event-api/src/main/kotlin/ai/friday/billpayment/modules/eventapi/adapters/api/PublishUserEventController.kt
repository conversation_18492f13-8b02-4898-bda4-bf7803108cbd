package ai.friday.billpayment.modules.eventapi.adapters.api

import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.modules.eventapi.EventAPI
import ai.friday.billpayment.modules.eventapi.adapters.messaging.UserEventMessage
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory

@Requires(property = "integrations.chatbotai.sendMessageProtocol", value = "HTTP")
@EventAPI
@Singleton
@Secured(Role.Code.FRIDAY_CALLBACK)
@Controller("/user-events")
class PublishUserEventController(
    private val messagePublisher: MessagePublisher,
    @Property(name = "sqs.queues.userEvent") private val queueName: String,
) {
    private val logger = LoggerFactory.getLogger(PublishUserEventController::class.java)

    @Post("/publish")
    fun publishUserEvent(@Body userEventMessage: UserEventMessage): HttpResponse<Any> {
        logger.info("Received user event: $userEventMessage")

        try {
            messagePublisher.sendMessage(
                queueName = queueName,
                body = userEventMessage,
            )

            logger.info("User event sent to queue successfully")
            return HttpResponse.noContent()
        } catch (e: Exception) {
            logger.error("Error sending user event to queue", e)
            return HttpResponse.serverError()
        }
    }
}