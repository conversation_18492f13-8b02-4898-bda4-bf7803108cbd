package ai.friday.billpayment.modules.eventapi.adapters.jobs

import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.modules.eventapi.EventAPIExport
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.dynamodb.model.ExportFormat
import software.amazon.awssdk.services.dynamodb.model.ExportTableToPointInTimeRequest
import software.amazon.awssdk.services.dynamodb.model.ExportType

@EventAPIExport
open class ExportEventsToS3Job(private val amazonDynamoDB: DynamoDbAsyncClient, private val configuration: ExportEventsToS3Configuration) :
    AbstractJob(
        cron = "15 4 * * *",
    ) {
    override fun execute() {
        val builder = ExportTableToPointInTimeRequest.builder()
        val date = getZonedDateTime().format(dateFormat)
        val request =
            builder.s3Bucket(configuration.bucket)
                .s3Prefix("${configuration.prefix}/$date")
                .exportFormat(ExportFormat.DYNAMODB_JSON)
                .tableArn(configuration.tableArn)
                .exportType(ExportType.FULL_EXPORT)
                .build()

        val response = amazonDynamoDB.exportTableToPointInTime(request)
        LOG.info(Markers.append("response", response), "ExportEventsToS3Job")
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ExportEventsToS3Job::class.java)
    }
}

@EventAPIExport
@ConfigurationProperties("export-events-s3")
data class ExportEventsToS3Configuration
@ConfigurationInject constructor(val bucket: String, val prefix: String, val tableArn: String)