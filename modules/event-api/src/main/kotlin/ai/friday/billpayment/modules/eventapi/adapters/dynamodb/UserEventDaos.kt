package ai.friday.billpayment.modules.eventapi.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.AbstractAsyncDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.AbstractDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.USER_EVENT_TABLE_NAME
import io.micronaut.context.annotation.Property
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient

private const val DYNAMODB_USER_EVENT_TABLE_NAME = "dynamodb.userEventsTableName"

abstract class AbstractUserEventDynamoDAO<T>(
    cli: DynamoDbEnhancedClient,
    type: Class<T>,
) : AbstractDynamoDAO<T>(
    cli = cli,
    type = type,
) {
    @field:Property(name = DYNAMODB_USER_EVENT_TABLE_NAME)
    private var tName: String = USER_EVENT_TABLE_NAME // Else legacy name

    override val tableName by lazy { tName }
}

abstract class AbstractUserEventDynamoDAOAsync<T>(
    cli: DynamoDbEnhancedAsyncClient,
    type: Class<T>,
) : AbstractAsyncDynamoDAO<T>(
    cli = cli,
    type = type,
) {
    @field:Property(name = DYNAMODB_USER_EVENT_TABLE_NAME)
    private var tName: String = USER_EVENT_TABLE_NAME // Else legacy name

    override val tableName by lazy { tName }
}