package ai.friday.billpayment.modules.eventapi.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.INDEX_1
import ai.friday.billpayment.adapters.dynamodb.INDEX_2
import ai.friday.billpayment.app.integrations.UserEvent
import ai.friday.billpayment.app.integrations.UserEventRepository
import ai.friday.billpayment.app.integrations.UserEventSource
import ai.friday.billpayment.app.integrations.UserEventType
import ai.friday.billpayment.modules.eventapi.EventAPI
import ai.friday.morning.date.isoDateTimeFormat
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@EventAPI
class UserEventDynamoDAO(
    cli: DynamoDbEnhancedClient,
) : AbstractUserEventDynamoDAO<UserEventEntity>(cli, UserEventEntity::class.java)

@EventAPI
class UserEventDynamoDBRepository(
    private val client: UserEventDynamoDAO,
) : UserEventRepository {

    override fun save(userEvent: UserEvent) {
        val formattedTimestamp = userEvent.timestamp.format(isoDateTimeFormat)

        val entity = UserEventEntity().apply {
            partitionKey = "${userEvent.entityType}#${userEvent.entityId}"
            sortKey = formattedTimestamp
            gSIndex1PartitionKey = userEvent.event
            gSIndex1SortKey = formattedTimestamp
            gSIndex2PartitionKey = userEvent.correlationId
            gSIndex2SortKey = formattedTimestamp
            entityId = userEvent.entityId
            entityType = userEvent.entityType
            source = userEvent.source
            timestamp = formattedTimestamp
            event = userEvent.event
            metadata = userEvent.metadata
            correlationId = userEvent.correlationId
        }

        client.save(entity)
    }
}

@DynamoDbBean
class UserEventEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    var gSIndex1PartitionKey: String? = null

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    var gSIndex1SortKey: String? = null

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    var gSIndex2PartitionKey: String? = null

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    var gSIndex2SortKey: String? = null

    @get:DynamoDbAttribute(value = "EntityId")
    lateinit var entityId: String

    @get:DynamoDbAttribute(value = "EntityType")
    lateinit var entityType: UserEventType

    @get:DynamoDbAttribute(value = "Source")
    lateinit var source: UserEventSource

    @get:DynamoDbAttribute(value = "Timestamp")
    lateinit var timestamp: String

    @get:DynamoDbAttribute(value = "Event")
    lateinit var event: String

    @get:DynamoDbAttribute(value = "Metadata")
    lateinit var metadata: Map<String, String>

    @get:DynamoDbAttribute(value = "CorrelationId")
    lateinit var correlationId: String
}