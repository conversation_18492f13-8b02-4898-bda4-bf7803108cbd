package ai.friday.billpayment.modules.eventapi

import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.ME_POUPE_ENV
import io.micronaut.context.annotation.DefaultScope
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton

@DefaultScope(Singleton::class)
@Requires(property = "modules.event-api.enabled", value = "true")
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
annotation class EventAPI

@DefaultScope(Singleton::class)
@Requirements(
    Requires(property = "disable.export-s3", notEquals = "true"),
    Requires(notEnv = ["test", "staging"]),
    Requires(env = [FRIDAY_ENV, ME_POUPE_ENV]),
    Requires(property = "modules.event-api.enabled", value = "true"),
)
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
annotation class EventAPIExport