package ai.friday.billpayment.modules.eventapi.app

import ai.friday.billpayment.app.integrations.UserEvent
import ai.friday.billpayment.app.integrations.UserEventRepository
import ai.friday.billpayment.app.integrations.UserEventService
import ai.friday.billpayment.modules.eventapi.EventAPI
import io.micronaut.context.annotation.Primary
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@EventAPI
@Primary
open class DefaultUserEventService(
    private val repository: UserEventRepository,
) : UserEventService {

    companion object {
        private val logger = LoggerFactory.getLogger(DefaultUserEventService::class.java)
    }

    override fun save(event: UserEvent) {
        repository.save(event)
        logger.info(Markers.append("event", event), "UserEventService#save")
    }
}