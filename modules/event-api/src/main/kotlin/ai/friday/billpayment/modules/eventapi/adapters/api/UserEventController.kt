package ai.friday.billpayment.modules.eventapi.adapters.api

import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.integrations.UserEvent
import ai.friday.billpayment.app.integrations.UserEventService
import ai.friday.billpayment.app.integrations.UserEventSource
import ai.friday.billpayment.modules.eventapi.EventAPI
import ai.friday.billpayment.modules.eventapi.app.DefaultUserEventService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Header
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import java.util.UUID
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Controller("/event")
@EventAPI
@Version("2")
@Secured(Role.Code.OWNER)
class UserEventController(
    private val userEventService: UserEventService,
) {
    private val logName = "UserEventController"

    @Put("/{event}")
    fun saveEvent(
        authentication: Authentication,
        event: String,
        @Header("X-Correlation-Id") correlationId: String?,
        @Body metadata: Map<String, String>,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val markers = Markers.append("accountId", accountId.value).andAppend("event", event).andAppend("metadata", metadata)

        try {
            userEventService.save(UserEvent(accountId, event, metadata, getZonedDateTime(), UserEventSource.API, correlationId ?: UUID.randomUUID().toString()))
            logger.info(markers, logName)
            return HttpResponse.noContent<Unit>()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return HttpResponse.serverError<Unit>()
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(DefaultUserEventService::class.java)
    }
}