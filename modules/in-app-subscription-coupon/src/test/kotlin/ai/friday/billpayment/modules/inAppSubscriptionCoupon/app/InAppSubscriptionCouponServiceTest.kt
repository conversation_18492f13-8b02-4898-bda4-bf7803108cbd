package ai.friday.billpayment.modules.inAppSubscriptionCoupon.app

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.inappsubscription.InAppSubscription
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionReason
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionService
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStatus
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStore
import ai.friday.billpayment.app.integrations.CrmRepository
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.inAppSubscriptionCoupon
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.right
import io.kotest.assertions.fail
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.Period
import java.time.ZonedDateTime
import net.javacrumbs.shedlock.core.SimpleLock
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class InAppSubscriptionCouponServiceTest {
    private val inAppSubscriptionCouponRepository = mockk<InAppSubscriptionCouponRepository>()
    private val inAppSubscriptionCouponRedeemRepository = mockk<InAppSubscriptionCouponRedeemRepository>()
    private val inAppSubscriptionService = mockk<InAppSubscriptionService>()

    private val simpleLock = mockk<SimpleLock>(relaxed = true)
    private val internalLock: InternalLock =
        mockk {
            every {
                acquireLock(any())
            } returns simpleLock
        }

    private val crmRepository = mockk<CrmRepository>()

    private val inAppSubscriptionCouponService = InAppSubscriptionCouponService(
        inAppSubscriptionCouponRepository = inAppSubscriptionCouponRepository,
        inAppSubscriptionCouponRedeemRepository = inAppSubscriptionCouponRedeemRepository,
        inAppSubscriptionService = inAppSubscriptionService,
        crmRepository = crmRepository,
        lockProvider = internalLock,
    )

    private val couponId = CouponId("fake coupon id")
    private val accountId = AccountId("fake account id")
    private val couponCode = CouponCode("fake coupon code")
    private val activeCoupon = inAppSubscriptionCoupon(couponId = couponId, code = couponCode)
    private val activeSubscription =
        InAppSubscription(
            accountId = accountId,
            status = InAppSubscriptionStatus.ACTIVE,
            endsAt = getZonedDateTime().plusDays(3),
            store = InAppSubscriptionStore.APP_STORE,
            reason = InAppSubscriptionReason.TRIAL,
            autoRenew = false,
            inAppSubscriptionAccessConcessionId = null,
            createdAt = getZonedDateTime(),
            price = 0,
            productId = null,
            offStoreProductId = null,
        )
    private val inactiveSubscription =
        InAppSubscription(
            accountId = accountId,
            status = InAppSubscriptionStatus.EXPIRED,
            endsAt = getZonedDateTime().minusDays(3),
            store = InAppSubscriptionStore.APP_STORE,
            reason = InAppSubscriptionReason.TRIAL,
            autoRenew = false,
            inAppSubscriptionAccessConcessionId = null,
            createdAt = getZonedDateTime().minusDays(6),
            price = 0,
            productId = null,
            offStoreProductId = null,
        )

    private fun callCreateCoupon(
        code: CouponCode? = null,
        free: Boolean = false,
        offerGroup: String? = null,
        quantity: Int = 100,
        externalId: InAppSubscriptionCouponExternalId? = null,
        expiresAt: ZonedDateTime = getZonedDateTime().plusMonths(1),
        subscriptionPeriod: Period = Period.ofYears(1),
    ): Either<CouponError, InAppSubscriptionCoupon> {
        return inAppSubscriptionCouponService.createCoupon(
            code = code,
            description = "Cupom teste",
            free = free,
            offerGroup = offerGroup,
            quantity = quantity,
            externalId = externalId,
            expiresAt = expiresAt,
            subscriptionPeriod = subscriptionPeriod,
            externalOfferId = null,
        )
    }

    @Nested
    @DisplayName("Validate")
    inner class Validate {
        @Test
        fun `deve retorar um cupom valido`() {
            every {
                inAppSubscriptionCouponRepository.find(any())
            } returns listOf(activeCoupon)

            every {
                inAppSubscriptionCouponRedeemRepository.findAllBy(any<AccountId>())
            } returns emptyList()

            val result = inAppSubscriptionCouponService.validate(AccountId(ACCOUNT_ID), activeCoupon.code)
            result.isRight() shouldBe true
        }

        @Test
        fun `deve retorar um cupom valido memso que ele tenha sido resgatado pelo account`() {
            val redeemedCoupon = activeCoupon.copy(availableQuantity = 0, enabled = false)
            every {
                inAppSubscriptionCouponRepository.find(any())
            } returns listOf(redeemedCoupon)

            every {
                inAppSubscriptionCouponRedeemRepository.findAllBy(any<AccountId>())
            } returns listOf(
                InAppSubscriptionCouponRedeem(
                    id = CouponRedeemId(),
                    accountId = accountId,
                    couponId = couponId,
                    offerGroup = null,
                    subscriptionEndsAt = getZonedDateTime().plusYears(1),
                    updatedAt = getZonedDateTime(),
                    createdAt = getZonedDateTime(),
                ),
            )

            val result = inAppSubscriptionCouponService.validate(AccountId(ACCOUNT_ID), activeCoupon.code).map { fail("deveria ser left") }.getOrElse { it }
            result.shouldBeTypeOf<CouponError.CouponAlreadyRedeemedByAccount>()
            result.coupon shouldBe redeemedCoupon
        }

        @Test
        fun `deve retornar o erro ExpiredCoupon quando o cupom estiver expirado`() {
            every {
                inAppSubscriptionCouponRepository.find(any())
            } returns listOf(inAppSubscriptionCoupon(endsAt = getZonedDateTime().minusMonths(1)))

            every {
                inAppSubscriptionCouponRedeemRepository.findAllBy(any<AccountId>())
            } returns emptyList()

            val result = inAppSubscriptionCouponService.validate(AccountId(ACCOUNT_ID), activeCoupon.code)
            result.isRight() shouldBe false
            result.fold({ error -> error shouldBe CouponError.ExpiredCoupon }, {})
        }

        @Test
        fun `deve retornar o erro CouponNotFound quando o cupom estiver desabilitado`() {
            every {
                inAppSubscriptionCouponRepository.find(any())
            } returns listOf(inAppSubscriptionCoupon(enabled = false))

            every {
                inAppSubscriptionCouponRedeemRepository.findAllBy(any<AccountId>())
            } returns emptyList()

            val result = inAppSubscriptionCouponService.validate(AccountId(ACCOUNT_ID), activeCoupon.code)
            result.isRight() shouldBe false
            result.fold({ error -> error shouldBe CouponError.CouponNotFound }, {})
        }

        @Test
        fun `deve retornar o erro CouponWithoutQuantity quando o cupom não possuir quantidade disponivel`() {
            every {
                inAppSubscriptionCouponRepository.find(any())
            } returns listOf(inAppSubscriptionCoupon(quantity = 0))

            every {
                inAppSubscriptionCouponRedeemRepository.findAllBy(any<AccountId>())
            } returns emptyList()

            val result = inAppSubscriptionCouponService.validate(AccountId(ACCOUNT_ID), activeCoupon.code)
            result.isRight() shouldBe false
            result.fold({ error -> error shouldBe CouponError.CouponWithoutQuantity }, {})
        }
    }

    @Nested
    @DisplayName("Create")
    inner class Create {
        @Test
        fun `deve criar um cupom gratuito`() {
            val coupon = inAppSubscriptionCoupon(free = true)

            every {
                inAppSubscriptionCouponRepository.findEnabledOrNull(any())
            } returns null

            every {
                inAppSubscriptionCouponRepository.create(any())
            } returns mockk() {
                every {
                    availableQuantity
                } returns 100
            }

            val result = callCreateCoupon(free = true, code = coupon.code).getOrElse { fail("Deveria ter funcionado") }

            verify(exactly = 1) {
                inAppSubscriptionCouponRepository.create(any())
            }

            result.availableQuantity shouldBe 100
        }

        @Test
        fun `deve falhar quando existir um cupom de mesmo código`() {
            val coupon = inAppSubscriptionCoupon(free = true)
            every {
                inAppSubscriptionCouponRepository.findEnabledOrNull(any())
            } returns coupon

            val result = callCreateCoupon(free = true, code = coupon.code).map {
                fail("Deveria ter falhado")
            }.getOrElse { it }

            verify(exactly = 0) {
                inAppSubscriptionCouponRepository.create(any())
            }

            result.shouldBeTypeOf<CouponError.CouponCodeAlreadyExists>()
        }

        @Test
        fun `deve falhar ao tentar criar um cupom gratuito com grupo de oferta`() {
            every {
                inAppSubscriptionCouponRepository.findEnabledOrNull(any())
            } returns null

            val result = callCreateCoupon(free = true, offerGroup = "fake offer")

            verify(exactly = 0) {
                inAppSubscriptionCouponRepository.create(any())
            }

            result.isRight() shouldBe false
            result.fold({ error -> error shouldBe CouponError.InvalidCoupon }, {})
        }

        @Test
        fun `deve falhar ao criar um cupom com a data de expiracao invalida`() {
            every {
                inAppSubscriptionCouponRepository.findEnabledOrNull(any())
            } returns null

            val result = callCreateCoupon(
                free = true,
                expiresAt = ZonedDateTime.now().minusMonths(1),
            )

            verify(exactly = 0) {
                inAppSubscriptionCouponRepository.create(any())
            }

            result.isRight() shouldBe false
            result.fold({ error -> error shouldBe CouponError.InvalidCoupon }, {})
        }

        @Test
        fun `deve falhar ao criar um cupom sem quantidade disponivel`() {
            every {
                inAppSubscriptionCouponRepository.findEnabledOrNull(any())
            } returns null

            val result = callCreateCoupon(
                free = true,
                quantity = 0,
            )
            verify(exactly = 0) {
                inAppSubscriptionCouponRepository.create(any())
            }

            result.isRight() shouldBe false
            result.fold({ error -> error shouldBe CouponError.CouponWithoutQuantity }, {})
        }
    }

    @Nested
    @DisplayName("Redeem")
    inner class Redeem {
        @Test
        fun `deve resgatar um cupom com quantidade disponivel quando o usuario não possui assinatura`() {
            every {
                inAppSubscriptionCouponRepository.find(any())
            } returns listOf(activeCoupon)

            every {
                inAppSubscriptionService.getSubscription(accountId)
            } returns null

            every {
                inAppSubscriptionService.createSubscription(any())
            } returns activeSubscription.right()

            every {
                inAppSubscriptionCouponRedeemRepository.createCouponRedeem(any(), any(), any())
            } returns mockk()

            every {
                inAppSubscriptionCouponRepository.create(any())
            } returns mockk()

            every { inAppSubscriptionCouponRedeemRepository.findAllBy(any<AccountId>()) } returns emptyList()

            every {
                inAppSubscriptionCouponRepository.redeem(any())
            } returns mockk<InAppSubscriptionCoupon> {
                every {
                    code
                } returns couponCode
            }.right()

            val result =
                inAppSubscriptionCouponService.redeem(
                    accountId = accountId,
                    code = couponCode,
                )

            val subscriptionSlot = slot<InAppSubscription>()
            verify(exactly = 1) {
                inAppSubscriptionService.createSubscription(capture(subscriptionSlot))
                inAppSubscriptionCouponRedeemRepository.createCouponRedeem(any(), any(), any())
                inAppSubscriptionCouponRepository.redeem(any())
            }

            subscriptionSlot.captured.shouldNotBeNull()
            subscriptionSlot.captured.status shouldBe InAppSubscriptionStatus.ACTIVE
            subscriptionSlot.captured.store.shouldBeNull()
            subscriptionSlot.captured.reason shouldBe InAppSubscriptionReason.NO_STORE_COUPON
            subscriptionSlot.captured.offStoreProductId shouldBe couponId.value
            subscriptionSlot.captured.autoRenew shouldBe false

            result.isRight() shouldBe true
            result.fold({}, { coupon -> coupon.code shouldBe couponCode })
        }

        @Test
        fun `deve resgatar um cupom com quantidade disponivel quando o usuario não possui assinatura sem gerar a assinatura se o cupom tiver offergroup`() {
            every {
                inAppSubscriptionCouponRepository.find(any())
            } returns listOf(activeCoupon.copy(offerGroup = "fake offer"))

            every {
                inAppSubscriptionCouponRedeemRepository.createCouponRedeem(any(), any(), any())
            } returns mockk()

            every {
                inAppSubscriptionCouponRepository.create(any())
            } returns mockk()

            every { inAppSubscriptionCouponRedeemRepository.findAllBy(any<AccountId>()) } returns emptyList()

            every {
                inAppSubscriptionCouponRepository.redeem(any())
            } returns mockk<InAppSubscriptionCoupon> {
                every {
                    code
                } returns couponCode
            }.right()

            val result =
                inAppSubscriptionCouponService.redeem(
                    accountId = accountId,
                    code = couponCode,
                )

            verify(exactly = 0) {
                inAppSubscriptionService.getSubscription(accountId)
                inAppSubscriptionService.createSubscription(any())
            }

            verify(exactly = 1) {
                inAppSubscriptionCouponRedeemRepository.createCouponRedeem(any(), any(), null)
                inAppSubscriptionCouponRepository.redeem(any())
            }

            result.isRight() shouldBe true
            result.fold({}, { coupon -> coupon.code shouldBe couponCode })
        }

        @Test
        fun `deve desabilitar o cupom quando a quantidade disponivel para resgate acabar`() {
            every {
                inAppSubscriptionCouponRepository.find(any())
            } returns
                listOf(activeCoupon.copy(availableQuantity = 1))

            every {
                inAppSubscriptionService.getSubscription(accountId)
            } returns null

            every {
                inAppSubscriptionService.createSubscription(any())
            } returns activeSubscription.right()

            every {
                inAppSubscriptionCouponRedeemRepository.createCouponRedeem(any(), any(), any())
            } returns mockk()

            every {
                inAppSubscriptionCouponRepository.create(any())
            } returns mockk()

            every { inAppSubscriptionCouponRedeemRepository.findAllBy(any<AccountId>()) } returns emptyList()

            every {
                inAppSubscriptionCouponRepository.redeem(any())
            } returns mockk<InAppSubscriptionCoupon> {
                every {
                    code
                } returns couponCode
            }.right()

            val result =
                inAppSubscriptionCouponService.redeem(
                    accountId = accountId,
                    code = couponCode,
                )

            val subscriptionSlot = slot<InAppSubscription>()
            verify(exactly = 1) {
                inAppSubscriptionService.createSubscription(capture(subscriptionSlot))
                inAppSubscriptionCouponRedeemRepository.createCouponRedeem(any(), any(), any())
                inAppSubscriptionCouponRepository.redeem(any())
            }

            subscriptionSlot.captured.shouldNotBeNull()
            subscriptionSlot.captured.status shouldBe InAppSubscriptionStatus.ACTIVE
            subscriptionSlot.captured.store.shouldBeNull()
            subscriptionSlot.captured.reason shouldBe InAppSubscriptionReason.NO_STORE_COUPON
            subscriptionSlot.captured.offStoreProductId shouldBe couponId.value
            subscriptionSlot.captured.autoRenew shouldBe false

            result.isRight() shouldBe true
            result.fold({}, { coupon -> coupon.code shouldBe couponCode })
        }

        @Test
        fun `deve resgatar um cupom com quantidade disponivel quando o usuario possui assinatura inativa`() {
            every {
                inAppSubscriptionCouponRepository.find(any())
            } returns listOf(activeCoupon)

            every {
                inAppSubscriptionService.getSubscription(accountId)
            } returns inactiveSubscription

            every {
                inAppSubscriptionService.extendSubscription(any())
            } returns activeSubscription.right()

            every { inAppSubscriptionCouponRedeemRepository.findAllBy(any<AccountId>()) } returns emptyList()

            every {
                inAppSubscriptionCouponRedeemRepository.createCouponRedeem(any(), any(), any())
            } returns mockk()

            every {
                inAppSubscriptionCouponRepository.create(any())
            } returns mockk()

            every {
                inAppSubscriptionCouponRepository.redeem(any())
            } returns mockk<InAppSubscriptionCoupon> {
                every {
                    code
                } returns couponCode
            }.right()

            val result =
                inAppSubscriptionCouponService.redeem(
                    accountId = accountId,
                    code = couponCode,
                )

            val subscriptionSlot = slot<InAppSubscription>()
            verify(exactly = 1) {
                inAppSubscriptionService.extendSubscription(capture(subscriptionSlot))
                inAppSubscriptionCouponRedeemRepository.createCouponRedeem(any(), any(), any())
                inAppSubscriptionCouponRepository.redeem(any())
            }

            subscriptionSlot.captured.shouldNotBeNull()
            subscriptionSlot.captured.status shouldBe InAppSubscriptionStatus.ACTIVE
            subscriptionSlot.captured.store.shouldBeNull()
            subscriptionSlot.captured.reason shouldBe InAppSubscriptionReason.NO_STORE_COUPON
            subscriptionSlot.captured.offStoreProductId shouldBe couponId.value
            subscriptionSlot.captured.autoRenew shouldBe false

            result.isRight() shouldBe true
            result.fold({}, { coupon -> coupon.code shouldBe couponCode })
        }

        @Test
        fun `deve resgatar um cupom com quantidade disponivel quando o usuario possui assinatura ativa`() {
            every {
                inAppSubscriptionCouponRepository.find(any())
            } returns listOf(inAppSubscriptionCoupon(code = couponCode, couponId = couponId))

            every {
                inAppSubscriptionService.getSubscription(accountId)
            } returns activeSubscription

            every {
                inAppSubscriptionService.extendSubscription(any())
            } returns activeSubscription.right()

            every {
                inAppSubscriptionCouponRedeemRepository.createCouponRedeem(any(), any(), any())
            } returns mockk()

            every { inAppSubscriptionCouponRedeemRepository.findAllBy(any<AccountId>()) } returns listOf(
                InAppSubscriptionCouponRedeem(
                    id = CouponRedeemId(),
                    accountId = accountId,
                    couponId = CouponId(),
                    offerGroup = null,
                    subscriptionEndsAt = getZonedDateTime().plusYears(1),
                    updatedAt = getZonedDateTime(),
                    createdAt = getZonedDateTime(),
                ),
            )

            every {
                inAppSubscriptionCouponRepository.create(any())
            } returns mockk()

            every {
                inAppSubscriptionCouponRepository.redeem(any())
            } returns mockk<InAppSubscriptionCoupon> {
                every {
                    code
                } returns couponCode
            }.right()

            every { inAppSubscriptionCouponRedeemRepository.findAllBy(any<AccountId>()) } returns listOf(
                InAppSubscriptionCouponRedeem(
                    id = CouponRedeemId(),
                    accountId = accountId,
                    couponId = CouponId(),
                    offerGroup = null,
                    subscriptionEndsAt = getZonedDateTime().plusYears(1),
                    updatedAt = getZonedDateTime(),
                    createdAt = getZonedDateTime(),
                ),
            )

            val result =
                inAppSubscriptionCouponService.redeem(
                    accountId = accountId,
                    code = couponCode,
                )

            val subscriptionSlot = slot<InAppSubscription>()
            verify(exactly = 1) {
                inAppSubscriptionService.extendSubscription(capture(subscriptionSlot))
                inAppSubscriptionCouponRedeemRepository.createCouponRedeem(any(), any(), any())
                inAppSubscriptionCouponRepository.redeem(any())
            }

            subscriptionSlot.captured.shouldNotBeNull()
            subscriptionSlot.captured.status shouldBe InAppSubscriptionStatus.ACTIVE
            subscriptionSlot.captured.store.shouldBeNull()
            subscriptionSlot.captured.reason shouldBe InAppSubscriptionReason.NO_STORE_COUPON
            subscriptionSlot.captured.offStoreProductId shouldBe couponId.value
            subscriptionSlot.captured.autoRenew shouldBe false

            result.isRight() shouldBe true
            result.fold({}, { coupon -> coupon.code shouldBe couponCode })
        }

        @Test
        fun `deve falhar ao resgatar um cupom desabilitado`() {
            every {
                inAppSubscriptionCouponRepository.find(any())
            } returns listOf(inAppSubscriptionCoupon(code = couponCode, enabled = false, endsAt = getZonedDateTime()))

            every { inAppSubscriptionCouponRedeemRepository.findAllBy(any<AccountId>()) } returns emptyList()

            val result = inAppSubscriptionCouponService.redeem(accountId = accountId, code = couponCode)

            verify(exactly = 0) {
                inAppSubscriptionCouponRedeemRepository.createCouponRedeem(any(), any(), any())
            }

            result.isRight() shouldBe false
            result.fold({ error -> error shouldBe CouponError.CouponNotFound }, {})
        }

        @Test
        fun `deve falhar ao tentar resgatar um cupom expirado`() {
            every {
                inAppSubscriptionCouponRepository.find(any())
            } returns listOf(inAppSubscriptionCoupon(code = couponCode, endsAt = getZonedDateTime()))

            every { inAppSubscriptionCouponRedeemRepository.findAllBy(any<AccountId>()) } returns emptyList()

            withGivenDateTime(getZonedDateTime().plusDays(1)) {
                val result = inAppSubscriptionCouponService.redeem(accountId = accountId, code = couponCode)

                verify(exactly = 0) {
                    inAppSubscriptionCouponRedeemRepository.createCouponRedeem(any(), any(), any())
                }

                result.isRight() shouldBe false
                result.fold({ error -> error shouldBe CouponError.ExpiredCoupon }, {})
            }
        }

        @Test
        fun `deve falhar ao tentar resgatar um cupom sem quantidade disponivel`() {
            every {
                inAppSubscriptionCouponRepository.find(any())
            } returns listOf(inAppSubscriptionCoupon(code = couponCode, quantity = 0))

            every { inAppSubscriptionCouponRedeemRepository.findAllBy(any<AccountId>()) } returns emptyList()

            val result = inAppSubscriptionCouponService.redeem(accountId = accountId, code = couponCode)

            verify(exactly = 0) {
                inAppSubscriptionCouponRedeemRepository.createCouponRedeem(any(), any(), any())
            }

            result.isRight() shouldBe false
            result.fold({ error -> error shouldBe CouponError.CouponWithoutQuantity }, {})
        }

        @Test
        fun `deve falhar ao resgatar um cupom já resgatado pelo mesmo usuário`() {
            val couponId = CouponId()
            every {
                inAppSubscriptionCouponRepository.find(any())
            } returns listOf(inAppSubscriptionCoupon(couponId = couponId, code = couponCode, quantity = 10))

            every { inAppSubscriptionCouponRedeemRepository.findAllBy(any<AccountId>()) } returns emptyList()

            every { inAppSubscriptionCouponRedeemRepository.findAllBy(any<AccountId>()) } returns listOf(
                InAppSubscriptionCouponRedeem(
                    id = CouponRedeemId(),
                    accountId = accountId,
                    couponId = couponId,
                    offerGroup = null,
                    subscriptionEndsAt = getZonedDateTime().plusYears(1),
                    updatedAt = getZonedDateTime(),
                    createdAt = getZonedDateTime(),
                ),
            )

            val result = inAppSubscriptionCouponService.redeem(accountId = accountId, code = couponCode).map {
                fail("deveria ser left")
            }.getOrElse { it }

            verify(exactly = 0) {
                inAppSubscriptionCouponRedeemRepository.createCouponRedeem(any(), any(), any())
            }
            result.shouldBeTypeOf<CouponError.CouponAlreadyRedeemedByAccount>()
        }
    }

    @Nested
    @DisplayName("Update")
    inner class Update {
        @Test
        fun `deve atualizar o cupom corretamente com seus respectivos campos`() {
            val couponCode = CouponCode("fake updated coupon code")
            val command =
                UpdateCouponCommand(
                    id = couponId,
                    code = couponCode,
                    description = "fake updated description",
                    free = true,
                    offerGroup = null,
                    quantity = 23,
                    enabled = true,
                    expiresAt = getZonedDateTime().plusDays(10),
                )

            every {
                inAppSubscriptionCouponRepository.findOrNull(any())
            } returns activeCoupon.copy(id = couponId, code = couponCode)

            every {
                inAppSubscriptionCouponRepository.findEnabledOrNull(any())
            } returns activeCoupon.copy(id = couponId, code = couponCode)

            every {
                inAppSubscriptionCouponRepository.create(any())
            } returns mockk()

            every {
                inAppSubscriptionCouponRepository.update(any())
            } returns mockk()

            val result = inAppSubscriptionCouponService.updateCoupon(command)

            val capturedCoupon = slot<InAppSubscriptionCoupon>()
            verify(exactly = 1) {
                inAppSubscriptionCouponRepository.update(capture(capturedCoupon))
            }

            result.isRight() shouldBe true
            capturedCoupon.captured.code.value shouldBe "fake updated coupon code"
            capturedCoupon.captured.availableQuantity shouldBe 23
            capturedCoupon.captured.free shouldBe true
            capturedCoupon.captured.offerGroup shouldBe null
            capturedCoupon.captured.description shouldBe "fake updated description"
            capturedCoupon.captured.enabled shouldBe true
        }

        @Test
        fun `deve atualizar o cupom e altear o enabled para false quando a quantidade for zero`() {
            val couponCode = CouponCode("fake updated coupon code")
            val command =
                UpdateCouponCommand(
                    id = couponId,
                    code = couponCode,
                    description = "fake updated description",
                    free = true,
                    offerGroup = null,
                    quantity = 0,
                    enabled = true,
                    expiresAt = getZonedDateTime().plusDays(10),
                )

            every {
                inAppSubscriptionCouponRepository.findOrNull(any())
            } returns activeCoupon.copy(id = couponId, code = couponCode)

            every {
                inAppSubscriptionCouponRepository.findEnabledOrNull(any())
            } returns activeCoupon.copy(id = couponId, code = couponCode)

            every {
                inAppSubscriptionCouponRepository.create(any())
            } returns mockk()

            every {
                inAppSubscriptionCouponRepository.update(any())
            } returns mockk()

            val result = inAppSubscriptionCouponService.updateCoupon(command)

            val capturedCoupon = slot<InAppSubscriptionCoupon>()
            verify(exactly = 1) {
                inAppSubscriptionCouponRepository.update(capture(capturedCoupon))
            }

            result.isRight() shouldBe true
            capturedCoupon.captured.code.value shouldBe "fake updated coupon code"
            capturedCoupon.captured.availableQuantity shouldBe 0
            capturedCoupon.captured.free shouldBe true
            capturedCoupon.captured.offerGroup shouldBe null
            capturedCoupon.captured.description shouldBe "fake updated description"
            capturedCoupon.captured.enabled shouldBe false
        }

        @Test
        fun `deve falhar quando não encontrar o cupom para atualizar`() {
            val command =
                UpdateCouponCommand(
                    id = couponId,
                    code = couponCode,
                    description = "fake description",
                    free = true,
                    offerGroup = null,
                    quantity = 10,
                    enabled = true,
                    expiresAt = getZonedDateTime().plusDays(10),
                )

            every {
                inAppSubscriptionCouponRepository.findOrNull(any())
            } returns null

            val result = inAppSubscriptionCouponService.updateCoupon(command)

            verify(exactly = 0) {
                inAppSubscriptionCouponRepository.create(any())
            }

            result.isRight() shouldBe false
            result.fold({ error -> error shouldBe CouponError.CouponNotFound }, {})
        }

        @Test
        fun `deve falhar quando existir um cupom de mesmo codigo diferente do atual`() {
            val command =
                UpdateCouponCommand(
                    id = couponId,
                    code = couponCode,
                    description = "fake description",
                    free = true,
                    offerGroup = null,
                    quantity = 10,
                    enabled = true,
                    expiresAt = getZonedDateTime().plusDays(10),
                )

            every {
                inAppSubscriptionCouponRepository.findOrNull(any())
            } returns activeCoupon

            every {
                inAppSubscriptionCouponRepository.findEnabledOrNull(any())
            } returns activeCoupon.copy(id = CouponId())

            val result = inAppSubscriptionCouponService.updateCoupon(command)

            verify(exactly = 0) {
                inAppSubscriptionCouponRepository.create(any())
            }

            result.isRight() shouldBe false
            result.fold({ error -> error shouldBe CouponError.CouponCodeAlreadyExists }, {})
        }

        @Test
        fun `deve falhar ao tentar atualizar a quantidade para um valor negativo`() {
            val command =
                UpdateCouponCommand(
                    id = couponId,
                    code = couponCode,
                    description = "fake description",
                    free = true,
                    offerGroup = null,
                    quantity = -1,
                    enabled = true,
                    expiresAt = getZonedDateTime().plusDays(10),
                )

            val result = inAppSubscriptionCouponService.updateCoupon(command)

            verify(exactly = 0) {
                inAppSubscriptionCouponRepository.create(any())
            }

            result.isRight() shouldBe false
            result.fold({ error -> error shouldBe CouponError.CouponWithoutQuantity }, {})
        }

        @Test
        fun `deve falhar ao tentar a data de finalizacao do cupom para um dia mais antigo que o atual`() {
            val command =
                UpdateCouponCommand(
                    id = couponId,
                    code = couponCode,
                    description = "fake description",
                    free = true,
                    offerGroup = null,
                    quantity = 10,
                    enabled = true,
                    expiresAt = getZonedDateTime().minusDays(10),
                )

            val result = inAppSubscriptionCouponService.updateCoupon(command)

            verify(exactly = 0) {
                inAppSubscriptionCouponRepository.create(any())
            }

            result.isRight() shouldBe false
            result.fold({ error -> error shouldBe CouponError.InvalidCoupon }, {})
        }

        @Test
        fun `deve falhar ao tentar atualizar um cupom free com grupo de oferta`() {
            val command =
                UpdateCouponCommand(
                    id = couponId,
                    code = couponCode,
                    description = "fake description",
                    free = true,
                    offerGroup = "fake offer grupo",
                    quantity = 10,
                    enabled = true,
                    expiresAt = getZonedDateTime().plusYears(1),
                )

            val result = inAppSubscriptionCouponService.updateCoupon(command)

            verify(exactly = 0) {
                inAppSubscriptionCouponRepository.create(any())
            }

            result.isRight() shouldBe false
            result.fold({ error -> error shouldBe CouponError.InvalidCoupon }, {})
        }
    }
}