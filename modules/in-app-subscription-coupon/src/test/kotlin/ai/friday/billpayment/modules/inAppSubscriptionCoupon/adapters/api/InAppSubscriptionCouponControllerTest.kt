package ai.friday.billpayment.modules.inAppSubscriptionCoupon.adapters.api

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponCode
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponError
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCouponService
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.inAppSubscriptionCoupon
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpStatus
import io.micronaut.security.authentication.Authentication
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class InAppSubscriptionCouponControllerTest {
    private val inAppSubscriptionCouponService = mockk<InAppSubscriptionCouponService>()

    private val inAppSubscriptionCouponController = InAppSubscriptionCouponController(inAppSubscriptionCouponService)

    private val authentication: Authentication =
        mockk {
            every {
                name
            } returns ACCOUNT.accountId.value
        }

    private val couponCode = "fake coupon code"

    @Nested
    @DisplayName("Redeem")
    inner class Redeem {
        @Test
        fun `deve resgatar um cupom e retornar NO_CONTENT`() {
            every {
                inAppSubscriptionCouponService.redeem(any(), any())
            } returns inAppSubscriptionCoupon(code = CouponCode(couponCode)).right()

            val result =
                inAppSubscriptionCouponController.redeem(
                    authentication,
                    couponCode,
                )

            verify(exactly = 1) {
                inAppSubscriptionCouponService.redeem(ACCOUNT.accountId, CouponCode(couponCode))
            }

            result.status() shouldBe HttpStatus.OK
        }

        @Test
        fun `deve resgatar um cupom que já está resgatado pelo usuário e retornar NO_CONTENT`() {
            every {
                inAppSubscriptionCouponService.redeem(any(), any())
            } returns CouponError.CouponAlreadyRedeemedByAccount(inAppSubscriptionCoupon(code = CouponCode(couponCode))).left()

            val result =
                inAppSubscriptionCouponController.redeem(
                    authentication,
                    couponCode,
                )

            verify(exactly = 1) {
                inAppSubscriptionCouponService.redeem(ACCOUNT.accountId, CouponCode(couponCode))
            }

            result.status() shouldBe HttpStatus.NO_CONTENT
        }

        @Test
        fun `deve retornar BAD_REQUEST quando um resgate falhar com CouponNotFound`() {
            every {
                inAppSubscriptionCouponService.redeem(any(), any())
            } returns CouponError.CouponNotFound.left()

            val result =
                inAppSubscriptionCouponController.redeem(
                    authentication,
                    code = couponCode,
                )

            verify(exactly = 1) {
                inAppSubscriptionCouponService.redeem(ACCOUNT.accountId, CouponCode(couponCode))
            }

            result.status() shouldBe HttpStatus.BAD_REQUEST
            result.body.isPresent shouldBe false
        }

        @Test
        fun `deve retornar CONFLICT quando um resgate falhar com CouponWithoutQuantity`() {
            every {
                inAppSubscriptionCouponService.redeem(any(), any())
            } returns CouponError.CouponWithoutQuantity.left()

            val result =
                inAppSubscriptionCouponController.redeem(
                    authentication,
                    code = couponCode,
                )

            verify(exactly = 1) {
                inAppSubscriptionCouponService.redeem(ACCOUNT.accountId, CouponCode(couponCode))
            }

            result.status() shouldBe HttpStatus.CONFLICT
            result.body.isPresent shouldBe false
        }

        @Test
        fun `deve retornar INTERNAL_SERVER_ERROR quando um resgate falhar com outros erros`() {
            every {
                inAppSubscriptionCouponService.redeem(any(), any())
            } returns CouponError.SubscriptionError.left()

            val result =
                inAppSubscriptionCouponController.redeem(
                    authentication,
                    code = couponCode,
                )

            verify(exactly = 1) {
                inAppSubscriptionCouponService.redeem(ACCOUNT.accountId, CouponCode(couponCode))
            }

            result.status() shouldBe HttpStatus.INTERNAL_SERVER_ERROR
            result.body.isPresent shouldBe false
        }
    }

    @Nested
    @DisplayName("Validate")
    inner class Validate {
        @Test
        fun `deve validar um cupom e retornar 200`() {
            every {
                inAppSubscriptionCouponService.validate(any(), any())
            } returns inAppSubscriptionCoupon(code = CouponCode(couponCode)).right()

            val result =
                inAppSubscriptionCouponController.validate(
                    authentication,
                    couponCode,
                )

            verify(exactly = 1) {
                inAppSubscriptionCouponService.validate(any(), CouponCode(couponCode))
            }

            result.status() shouldBe HttpStatus.OK

            val response = result.getBody(ValidateCouponResponseTO::class.java).get()
            response.free shouldBe true
            response.offerGroup shouldBe null
        }

        @Test
        fun `deve validar um cupom que já está resgatado pelo usuário e retornar NO_CONTENT`() {
            every {
                inAppSubscriptionCouponService.validate(any(), any())
            } returns CouponError.CouponAlreadyRedeemedByAccount(inAppSubscriptionCoupon(code = CouponCode(couponCode))).left()

            val result =
                inAppSubscriptionCouponController.validate(
                    authentication,
                    couponCode,
                )

            verify(exactly = 1) {
                inAppSubscriptionCouponService.validate(any(), CouponCode(couponCode))
            }

            result.status() shouldBe HttpStatus.OK

            val response = result.getBody(ValidateCouponResponseTO::class.java).get()
            response.free shouldBe true
            response.offerGroup shouldBe null
        }

        @Test
        fun `deve retornar BAD_REQUEST quando um resgate falhar com CouponNotFound`() {
            every {
                inAppSubscriptionCouponService.validate(any(), any())
            } returns CouponError.CouponNotFound.left()

            val result =
                inAppSubscriptionCouponController.validate(
                    authentication,
                    couponCode,
                )

            verify(exactly = 1) {
                inAppSubscriptionCouponService.validate(any(), CouponCode(couponCode))
            }

            result.status() shouldBe HttpStatus.BAD_REQUEST
            result.body.isPresent shouldBe false
        }

        @Test
        fun `deve retornar CONFLICT quando um resgate falhar com CouponWithoutQuantity`() {
            every {
                inAppSubscriptionCouponService.validate(any(), any())
            } returns CouponError.CouponWithoutQuantity.left()

            val result =
                inAppSubscriptionCouponController.validate(
                    authentication,
                    couponCode,
                )

            verify(exactly = 1) {
                inAppSubscriptionCouponService.validate(any(), CouponCode(couponCode))
            }

            result.status() shouldBe HttpStatus.CONFLICT
            result.body.isPresent shouldBe false
        }

        @Test
        fun `deve retornar INTERNAL_SERVER_ERROR quando um resgate falhar com outros erros`() {
            every {
                inAppSubscriptionCouponService.validate(any(), any())
            } returns CouponError.SubscriptionError.left()

            val result =
                inAppSubscriptionCouponController.validate(
                    authentication,
                    couponCode,
                )

            verify(exactly = 1) {
                inAppSubscriptionCouponService.validate(any(), CouponCode(couponCode))
            }

            result.status() shouldBe HttpStatus.INTERNAL_SERVER_ERROR
            result.body.isPresent shouldBe false
        }
    }
}