package ai.friday.billpayment.modules.inAppSubscriptionCoupon.adapters.dynamodb

import DynamoDBUtils
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponCode
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponError
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.inAppSubscriptionCouponCommand
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.getOrElse
import io.kotest.assertions.fail
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class InAppSubscriptionCouponDbRepositoryTest {
    private val dynamoDbEnhancedClient = DynamoDBUtils.setupDynamoDB()

    private val inAppSubscriptionCouponRepository =
        InAppSubscriptionCouponDbRepository(
            client = InAppSubscriptionCouponDynamoDAO(dynamoDbEnhancedClient),
        )

    @Nested
    @DisplayName("Create")
    inner class Create {
        @Test
        fun `deve conseguir criar um coupom com gratuidade 100`() {
            val endsAt = getZonedDateTime().plusYears(1)

            val freeCoupon =
                inAppSubscriptionCouponCommand(
                    code = CouponCode("CUPONGURIZES"),
                    free = true,
                    quantity = 10,
                    endsAt = endsAt,
                )

            val result = inAppSubscriptionCouponRepository.create(freeCoupon)

            result.code.value shouldBe "CUPONGURIZES"
            result.free shouldBe true
            result.offerGroup shouldBe null
            result.availableQuantity shouldBe 10
            result.enabled shouldBe true
            result.expiresAt shouldBe endsAt

            inAppSubscriptionCouponRepository.findOrNull(result.id) shouldBe result
        }

        @Test
        fun `deve conseguir criar um cupom com grupo de oferta`() {
            val endsAt = getZonedDateTime().plusYears(1)

            val coupon =
                inAppSubscriptionCouponCommand(
                    code = CouponCode("CUPONGURIZES"),
                    free = false,
                    offerGroup = "fake_offer_group",
                    quantity = 10,
                    endsAt = endsAt,
                )

            val result = inAppSubscriptionCouponRepository.create(coupon)

            result.code.value shouldBe "CUPONGURIZES"
            result.free shouldBe false
            result.offerGroup shouldBe "fake_offer_group"
            result.availableQuantity shouldBe 10
            result.enabled shouldBe true
            result.expiresAt shouldBe endsAt

            inAppSubscriptionCouponRepository.findOrNull(result.id) shouldBe result
        }
    }

    @Nested
    @DisplayName("Update")
    inner class Update {
        @Test
        fun `deve atualizar um cupom existente`() {
            val freeCoupon =
                inAppSubscriptionCouponCommand(
                    code = CouponCode("CUPONGURIZES"),
                    free = true,
                    quantity = 10,
                    endsAt = getZonedDateTime().plusYears(1),
                )

            val result1 = inAppSubscriptionCouponRepository.create(freeCoupon)
            inAppSubscriptionCouponRepository.findOrNull(result1.id)

            result1.shouldNotBeNull()
            result1.code.value shouldBe "CUPONGURIZES"
            result1.free shouldBe true
            result1.offerGroup shouldBe null
            result1.availableQuantity shouldBe 10
            result1.enabled shouldBe true

            inAppSubscriptionCouponRepository.update(result1.copy(enabled = false, availableQuantity = 0))

            val result2 = inAppSubscriptionCouponRepository.findOrNull(result1.id)
            result2.shouldNotBeNull()
            result2.code.value shouldBe "CUPONGURIZES"
            result2.free shouldBe true
            result2.offerGroup shouldBe null
            result2.availableQuantity shouldBe 0
            result2.enabled shouldBe false
        }
    }

    @Nested
    @DisplayName("Find")
    inner class Find {
        @Test
        fun `deve conseguir conseguir buscar um cupom por id`() {
            val coupon =
                inAppSubscriptionCouponCommand(
                    code = CouponCode("CUPONGURIZES"),
                    free = false,
                    offerGroup = "fake_offer_group",
                )

            val result1 = inAppSubscriptionCouponRepository.create(coupon)
            inAppSubscriptionCouponRepository.findOrNull(result1.id)

            result1.shouldNotBeNull()
            result1.code.value shouldBe "CUPONGURIZES"
            result1.free shouldBe false
            result1.offerGroup shouldBe "fake_offer_group"
            result1.availableQuantity shouldBe 100
            result1.enabled shouldBe true
        }

        @Test
        fun `deve conseguir conseguir buscar um cupom por codigo`() {
            val coupon =
                inAppSubscriptionCouponCommand(
                    code = CouponCode("CUPONGURIZES"),
                    free = false,
                    offerGroup = "fake_offer_group",
                    extraParams = mapOf("extra" to "param", "extra2" to "param2"),
                )

            inAppSubscriptionCouponRepository.create(coupon)
            val result1 = inAppSubscriptionCouponRepository.findEnabledOrNull(coupon.code)

            result1.shouldNotBeNull()
            result1.code.value shouldBe "CUPONGURIZES"
            result1.free shouldBe false
            result1.offerGroup shouldBe "fake_offer_group"
            result1.availableQuantity shouldBe 100
            result1.enabled shouldBe true
            result1.extraParams shouldBe mapOf("extra" to "param", "extra2" to "param2")
        }

        @Test
        fun `deve conseguir conseguir buscar todos`() {
            val coupon1 = inAppSubscriptionCouponCommand(code = CouponCode("CUPOM1"), offerGroup = "fake_offer_group1")
            val coupon2 = inAppSubscriptionCouponCommand(code = CouponCode("CUPOM2"), offerGroup = "fake_offer_group2")
            val coupon3 = inAppSubscriptionCouponCommand(code = CouponCode("CUPOM3"), offerGroup = "fake_offer_group3")

            inAppSubscriptionCouponRepository.create(coupon1)
            inAppSubscriptionCouponRepository.create(coupon2)
            inAppSubscriptionCouponRepository.create(coupon3)
            val result = inAppSubscriptionCouponRepository.findAll()

            result.size shouldBe 3
            result[0].code.value shouldBe "CUPOM1"
            result[1].code.value shouldBe "CUPOM2"
            result[2].code.value shouldBe "CUPOM3"
        }
    }

    @Nested
    @DisplayName("Redeem")
    inner class Redeem {
        @Test
        fun `deve conseguir resgatar um cupom que tenha quantidade disponivel e decrementar a quantidade`() {
            val coupon =
                inAppSubscriptionCouponCommand(
                    code = CouponCode("CUPONGURIZES"),
                    free = false,
                    offerGroup = "fake_offer_group",
                    quantity = 20,
                )

            val result1 = inAppSubscriptionCouponRepository.create(coupon)

            val result2 = inAppSubscriptionCouponRepository.redeem(result1.id).getOrElse { fail("deveria ser right") }

            result2.availableQuantity shouldBe 19
            result2.enabled shouldBe true
        }

        @Test
        fun `deve conseguir resgatar um cupom que tenha apenas 1 unidade disponivel e desativar`() {
            val coupon =
                inAppSubscriptionCouponCommand(
                    code = CouponCode("CUPONGURIZES"),
                    free = false,
                    offerGroup = "fake_offer_group",
                    quantity = 1,
                )

            val result1 = inAppSubscriptionCouponRepository.create(coupon)

            val result2 = inAppSubscriptionCouponRepository.redeem(result1.id).getOrElse { fail("deveria ser right") }

            result2.availableQuantity shouldBe 0
            result2.enabled shouldBe false
        }

        @Test
        fun `deve falhar ao resgatar um cupom que tenha apenas 0 unidades disponivel`() {
            val coupon =
                inAppSubscriptionCouponCommand(
                    code = CouponCode("CUPONGURIZES"),
                    free = false,
                    offerGroup = "fake_offer_group",
                    quantity = 0,
                )

            val result1 = inAppSubscriptionCouponRepository.create(coupon)

            val result2 = inAppSubscriptionCouponRepository.redeem(result1.id).map {
                fail("deveria ser left")
            }.getOrElse { it }

            result2.shouldBeTypeOf<CouponError.CouponWithoutQuantity>()
        }

        @Test
        fun `deve falhar ao resgatar um cupom que esteja desativado`() {
            val coupon =
                inAppSubscriptionCouponCommand(
                    code = CouponCode("CUPONGURIZES"),
                    free = false,
                    offerGroup = "fake_offer_group",
                    quantity = 100,
                )

            val result1 = inAppSubscriptionCouponRepository.create(coupon)
            inAppSubscriptionCouponRepository.update(result1.copy(enabled = false))

            val result2 = inAppSubscriptionCouponRepository.redeem(result1.id).map {
                fail("deveria ser left")
            }.getOrElse { it }

            result2.shouldBeTypeOf<CouponError.CouponWithoutQuantity>()
        }
    }
}