package ai.friday.billpayment.modules.inAppSubscriptionCoupon

import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponCode
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponId
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CreateInAppSubscriptionCouponCommand
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCoupon
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCouponExternalId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.time.Period
import java.time.ZonedDateTime

fun inAppSubscriptionCoupon(
    couponId: CouponId = CouponId(),
    code: CouponCode = CouponCode(value = "fake coupon code"),
    free: Boolean = false,
    offerGroup: String? = null,
    quantity: Int = 100,
    enabled: Boolean = true,
    endsAt: ZonedDateTime = getZonedDateTime().plusMonths(1),
) =
    InAppSubscriptionCoupon(
        id = couponId,
        code = code,
        description = "fake coupon description",
        free = free,
        offerGroup = offerGroup,
        availableQuantity = quantity,
        originalQuantity = quantity,
        enabled = enabled,
        expiresAt = endsAt,
        createdAt = getZonedDateTime(),
        subscriptionPeriod = Period.ofYears(1),
        externalId = null,
        extraParams = null,
    )

fun inAppSubscriptionCouponCommand(
    code: CouponCode = CouponCode(value = "fake coupon code"),
    free: Boolean = false,
    offerGroup: String? = null,
    quantity: Int = 100,
    endsAt: ZonedDateTime = getZonedDateTime().plusMonths(1),
    externalId: InAppSubscriptionCouponExternalId? = null,
    extraParams: Map<String, String>? = null,
) =
    CreateInAppSubscriptionCouponCommand(
        code = code,
        description = "fake coupon description",
        free = free,
        offerGroup = offerGroup,
        quantity = quantity,
        expiresAt = endsAt,
        subscriptionPeriod = Period.ofYears(1),
        externalId = externalId,
        externalOfferId = null,
        extraParams = extraParams,
    )