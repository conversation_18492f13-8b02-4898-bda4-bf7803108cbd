package ai.friday.billpayment.app.payment.pinCode

import ai.friday.billpayment.app.account.AccountId
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.result.shouldBeSuccess
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class PinCodeServiceTest {
    private val mockedRepository: PinCodeRepository = mockk()

    @ParameterizedTest
    @MethodSource("pinCodes")
    fun `quando a flag estiver desligada deve retornar como válido`(pinCode: PinCode?) {
        val service = PinCodeService(mockedRepository, false)

        service.validate(AccountId(), pinCode).shouldBeSuccess().valid.shouldBeTrue()
    }

    @Nested
    @DisplayName("pinCode")
    inner class PinCodeOn {
        private val service = PinCodeService(mockedRepository, true)

        @Test
        fun `quando pinCode for nulo, deve retornar invalido`() {
            service.validate(AccountId(), null).shouldBeSuccess().valid.shouldBeFalse()
        }

        @Test
        fun `quando o pinCode estiver errado, deve retornar invalido`() {
            every { mockedRepository.validate(any(), any()) } returns ValidationResult(valid = false, maxAttemptsReached = false)

            service.validate(AccountId(), PinCode("1234")).shouldBeSuccess().valid.shouldBeFalse()
            service.validate(AccountId(), PinCode("1234")).shouldBeSuccess().maxAttemptsReached.shouldBeFalse()
        }

        @Test
        fun `quando o pinCode estiver errado, e tiver atingido o número máximo de tentativas deve retornar invalido`() {
            every { mockedRepository.validate(any(), any()) } returns ValidationResult(valid = false, maxAttemptsReached = true)

            service.validate(AccountId(), PinCode("1234")).shouldBeSuccess().valid.shouldBeFalse()
            service.validate(AccountId(), PinCode("1234")).shouldBeSuccess().maxAttemptsReached.shouldBeTrue()
        }

        @Test
        fun `quando o pinCode estiver certo, deve retornar valido`() {
            every { mockedRepository.validate(any(), any()) } returns ValidationResult(valid = true, maxAttemptsReached = false)

            service.validate(AccountId(), PinCode("1234")).shouldBeSuccess().valid.shouldBeTrue()
            service.validate(AccountId(), PinCode("1234")).shouldBeSuccess().maxAttemptsReached.shouldBeFalse()
        }
    }

    companion object {
        @JvmStatic
        fun pinCodes() = listOf(PinCode("1234"), null)
    }
}