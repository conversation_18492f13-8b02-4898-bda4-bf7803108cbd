package ai.friday.billpayment.modules.inAppSubscriptionCoupon.adapters.api

import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.log
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.InAppSubscriptionCouponModule
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponCode
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponError
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCoupon
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCouponService
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import java.time.Period
import org.slf4j.LoggerFactory

@Secured(Role.Code.OWNER, Role.Code.GUEST)
@Controller("/in-app-subscription/coupon")
@InAppSubscriptionCouponModule
@Version("2")
class InAppSubscriptionCouponController(
    private val inAppSubscriptionCouponService: InAppSubscriptionCouponService,
) {
    @Get("/{code}")
    fun validate(
        authentication: Authentication,
        @PathVariable code: String,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val logName = "InAppSubscriptionCouponController#validate"
        val markers = log("accountId" to accountId.value, "couponCode" to code)

        return try {
            inAppSubscriptionCouponService.validate(accountId, CouponCode(code)).map { coupon ->
                logger.info(markers.andAppend("coupon", coupon), logName)
                HttpResponse.ok(coupon.toRedeemCouponResponseTO())
            }
                .getOrElse { error ->
                    when (error) {
                        is CouponError.CouponAlreadyRedeemedByAccount -> {
                            logger.info(markers.andAppend("coupon", error.coupon), logName)
                            HttpResponse.ok(error.coupon.toRedeemCouponResponseTO())
                        }

                        is CouponError.CouponNotFound, is CouponError.ExpiredCoupon -> {
                            logger.warn(markers.andAppend("couponError", error), logName)
                            HttpResponse.badRequest<Unit>()
                        }

                        is CouponError.CouponWithoutQuantity -> {
                            logger.warn(markers.andAppend("couponError", error), logName)
                            HttpResponse.status<Unit>(HttpStatus.CONFLICT)
                        }

                        else -> {
                            logger.error(markers.andAppend("couponError", error), logName)
                            HttpResponse.serverError<Unit>()
                        }
                    }
                }
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    @Post("/{code}")
    fun redeem(
        authentication: Authentication,
        @PathVariable code: String,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val logName = "InAppSubscriptionCouponController#redeem"
        val markers = log("accountId" to accountId.value, "couponCode" to code)

        try {
            val coupon = inAppSubscriptionCouponService.redeem(accountId, CouponCode(code)).getOrElse { error ->
                return when (error) {
                    is CouponError.CouponAlreadyRedeemedByAccount -> {
                        logger.info(markers.andAppend("coupon", error.coupon), logName)
                        HttpResponse.noContent<Unit>()
                    }

                    is CouponError.CouponNotFound, is CouponError.ExpiredCoupon -> {
                        logger.warn(markers.andAppend("couponError", error), logName)
                        HttpResponse.badRequest<Unit>()
                    }

                    is CouponError.CouponWithoutQuantity -> {
                        logger.warn(markers.andAppend("couponError", error), logName)
                        HttpResponse.status<Unit>(HttpStatus.CONFLICT)
                    }

                    else -> {
                        logger.error(markers.andAppend("couponError", error), logName)
                        HttpResponse.serverError<Unit>()
                    }
                }
            }

            logger.info(markers.andAppend("coupon", coupon), logName)
            return HttpResponse.ok(coupon.toRedeemCouponResponseTO())
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            return HttpResponse.serverError<Unit>()
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(InAppSubscriptionCouponController::class.java)
    }
}

data class ValidateCouponResponseTO(
    val free: Boolean = false,
    val offerGroup: String? = null,
    val subscriptionPeriod: Period? = null,
)

private fun InAppSubscriptionCoupon.toRedeemCouponResponseTO(): ValidateCouponResponseTO =
    ValidateCouponResponseTO(
        free = this.offerGroup.isNullOrEmpty(),
        offerGroup = this.offerGroup,
        subscriptionPeriod = this.subscriptionPeriod,
    )