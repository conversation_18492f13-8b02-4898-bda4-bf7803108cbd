package ai.friday.billpayment.modules.inAppSubscriptionCoupon.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.AbstractBillPaymentDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.GlobalSecondaryIndexNames
import ai.friday.billpayment.adapters.dynamodb.INDEX_1
import ai.friday.billpayment.adapters.dynamodb.INDEX_2
import ai.friday.billpayment.adapters.dynamodb.INDEX_3
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.InAppSubscriptionCouponModule
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponCode
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponError
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponId
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CreateInAppSubscriptionCouponCommand
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCoupon
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCouponExternalId
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCouponRepository
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import java.time.Period
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val SCAN_KEY = "IN_APP_SUBSCRIPTION_COUPON"
private const val INDEX1_PARTITION_KEY = "IN_APP_SUBSCRIPTION_COUPON_CODE"

@InAppSubscriptionCouponModule
class InAppSubscriptionCouponDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<CouponEntity>(cli, CouponEntity::class.java)

@InAppSubscriptionCouponModule
class InAppSubscriptionCouponDbRepository(
    private val client: InAppSubscriptionCouponDynamoDAO,
) : InAppSubscriptionCouponRepository {
    override fun create(coupon: CreateInAppSubscriptionCouponCommand): InAppSubscriptionCoupon {
        val createdAt = getZonedDateTime()
        val entity =
            CouponEntity().apply {
                partitionKey = CouponId().value
                sortKey = SCAN_KEY
                gSIndex1PartitionKey = "$INDEX1_PARTITION_KEY#${coupon.code.value}"
                gSIndex1SortKey = true.toString()
                gSIndex2PartitionKey = SCAN_KEY
                gSIndex2SortKey = createdAt.format(DateTimeFormatter.ISO_DATE_TIME)
                gSIndex3PartitionKey = coupon.externalId?.let { externalId -> "$SCAN_KEY#${externalId.provider}#${externalId.value}" }
                gSIndex3SortKey = createdAt.format(DateTimeFormatter.ISO_DATE_TIME)
                externalIdProvider = coupon.externalId?.provider
                externalIdValue = coupon.externalId?.value
                externalOfferIdValue = coupon.externalOfferId
                code = coupon.code.value
                description = coupon.description
                enabled = true
                free = coupon.free
                quantity = coupon.quantity
                originalQuantity = coupon.quantity
                offerGroup = coupon.offerGroup
                expiresAt = coupon.expiresAt.format(DateTimeFormatter.ISO_DATE_TIME)
                subscriptionPeriod = coupon.subscriptionPeriod.toString()
                this.createdAt = createdAt.format(DateTimeFormatter.ISO_DATE_TIME)
                updatedAt = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
                extraParams = coupon.extraParams
            }
        client.save(entity)
        return entity.toInAppSubscriptionCoupon()
    }

    override fun redeem(couponId: CouponId): Either<CouponError, InAppSubscriptionCoupon> {
        val entity = findEntity(couponId) ?: return CouponError.CouponNotFound.left()

        if (entity.quantity < 1 || !entity.enabled) {
            return CouponError.CouponWithoutQuantity.left()
        }

        val newQuantity = entity.quantity - 1
        val stillEnabled = newQuantity > 0

        val updatedCouponEntity = entity.apply {
            quantity = newQuantity
            gSIndex1SortKey = stillEnabled.toString()
            enabled = stillEnabled
            updatedAt = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
        }
        client.save(updatedCouponEntity)
        return updatedCouponEntity.toInAppSubscriptionCoupon().right()
    }

    override fun findEnabledOrNull(code: CouponCode): InAppSubscriptionCoupon? =
        client
            .findByPartitionKeyAndScanKeyOnIndex(
                index = GlobalSecondaryIndexNames.GSIndex1,
                partitionKey = "$INDEX1_PARTITION_KEY#${code.value}",
                sortKey = true.toString(),
            ).singleOrNull()
            ?.toInAppSubscriptionCoupon()

    override fun find(code: CouponCode): List<InAppSubscriptionCoupon> =
        client
            .findByPartitionKeyOnIndex(
                index = GlobalSecondaryIndexNames.GSIndex1,
                partitionKey = "$INDEX1_PARTITION_KEY#${code.value}",
            ).map { it.toInAppSubscriptionCoupon() }

    override fun findByExternalId(externalId: InAppSubscriptionCouponExternalId): InAppSubscriptionCoupon? =
        client
            .findByPartitionKeyOnIndex(
                index = GlobalSecondaryIndexNames.GSIndex3,
                partitionKey = "$SCAN_KEY#${externalId.provider}#${externalId.value}",
            ).singleOrNull()?.toInAppSubscriptionCoupon()

    override fun update(toBeUpdated: InAppSubscriptionCoupon): InAppSubscriptionCoupon {
        val enabled = toBeUpdated.availableQuantity > 0 && toBeUpdated.enabled
        findEntity(toBeUpdated.id)?.let {
            val updatedAt = getZonedDateTime()
            it.apply {
                gSIndex1PartitionKey = "$INDEX1_PARTITION_KEY#${toBeUpdated.code.value}"
                gSIndex1SortKey = enabled.toString()
                gSIndex3PartitionKey = toBeUpdated.externalId?.let { externalId -> "$SCAN_KEY#${externalId.provider}#${externalId.value}" }
                code = toBeUpdated.code.value
                description = toBeUpdated.description
                this.enabled = enabled
                free = toBeUpdated.free
                quantity = toBeUpdated.availableQuantity
                offerGroup = toBeUpdated.offerGroup
                expiresAt = toBeUpdated.expiresAt.format(DateTimeFormatter.ISO_DATE_TIME)
                this.updatedAt = updatedAt.format(DateTimeFormatter.ISO_DATE_TIME)
                externalIdProvider = toBeUpdated.externalId?.provider
                externalIdValue = toBeUpdated.externalId?.value
                subscriptionPeriod = toBeUpdated.subscriptionPeriod.toString()
            }
            client.save(it)
            return it.toInAppSubscriptionCoupon()
        } ?: throw IllegalArgumentException("Coupon not found")
    }

    override fun findOrNull(id: CouponId): InAppSubscriptionCoupon? =
        findEntity(id)
            ?.toInAppSubscriptionCoupon()

    private fun findEntity(id: CouponId) = client
        .findByPrimaryKey(
            partitionKey = id.value,
            sortKey = SCAN_KEY,
        )

    override fun findAll(): List<InAppSubscriptionCoupon> =
        client
            .findByPartitionKeyOnIndex(
                index = GlobalSecondaryIndexNames.GSIndex2,
                partitionKey = SCAN_KEY,
            ).map { it.toInAppSubscriptionCoupon() }
}

// TODO: ajustar primary key e criar index para consultar todos os cupons
@DynamoDbBean
class CouponEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String

    @get:DynamoDbAttribute(value = "Code")
    lateinit var code: String

    @get:DynamoDbAttribute(value = "Description")
    var description: String? = null

    @get:DynamoDbAttribute(value = "Quantity")
    var quantity: Int = 0

    @get:DynamoDbAttribute(value = "OriginalQuantity")
    var originalQuantity: Int = 0

    @get:DynamoDbAttribute(value = "Enabled")
    var enabled: Boolean = false

    @get:DynamoDbAttribute(value = "Free")
    var free: Boolean = false

    @get:DynamoDbAttribute(value = "ExpiresAt")
    lateinit var expiresAt: String

    @get:DynamoDbAttribute(value = "SubscriptionPeriod")
    lateinit var subscriptionPeriod: String

    @get:DynamoDbAttribute(value = "OfferGroup")
    var offerGroup: String? = null

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1SortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    lateinit var gSIndex2PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    lateinit var gSIndex2SortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_3])
    @get:DynamoDbAttribute(value = "GSIndex3PrimaryKey")
    var gSIndex3PartitionKey: String? = null

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_3])
    @get:DynamoDbAttribute(value = "GSIndex3ScanKey")
    var gSIndex3SortKey: String? = null

    @get:DynamoDbAttribute(value = "ExternalIdProvider")
    var externalIdProvider: String? = null

    @get:DynamoDbAttribute(value = "ExternalIdValue")
    var externalIdValue: String? = null

    @get:DynamoDbAttribute(value = "ExternalOfferIdValue")
    var externalOfferIdValue: String? = null

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute(value = "UpdatedAt")
    lateinit var updatedAt: String

    @get:DynamoDbAttribute(value = "ExtraParams")
    var extraParams: Map<String, String>? = null
}

private fun CouponEntity.toInAppSubscriptionCoupon(): InAppSubscriptionCoupon =
    InAppSubscriptionCoupon(
        id = CouponId(partitionKey),
        code = CouponCode(this.code),
        description = this.description ?: "",
        expiresAt = ZonedDateTime.parse(this.expiresAt, DateTimeFormatter.ISO_DATE_TIME),
        subscriptionPeriod = Period.parse(this.subscriptionPeriod),
        free = this.free,
        availableQuantity = this.quantity,
        originalQuantity = this.originalQuantity,
        offerGroup = this.offerGroup,
        enabled = this.enabled,
        createdAt = ZonedDateTime.parse(this.createdAt, DateTimeFormatter.ISO_DATE_TIME),
        externalId = externalIdProvider?.let {
            InAppSubscriptionCouponExternalId(it, externalIdValue ?: "")
        },
        extraParams = this.extraParams,
    )