package ai.friday.billpayment.modules.inAppSubscriptionCoupon.app

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.feature.RequiresInAppSubscriptionCoupon
import ai.friday.billpayment.app.fixOrNull
import ai.friday.billpayment.app.inappsubscription.InAppSubscription
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionReason
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionService
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStatus
import ai.friday.billpayment.app.integrations.CrmRepository
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.TemporaryCrmContactMinimal
import ai.friday.billpayment.log
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateFormatBR
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.mapNotNull
import arrow.core.right
import io.micronaut.logging.LogLevel
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.Period
import java.time.ZonedDateTime
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
@RequiresInAppSubscriptionCoupon
class InAppSubscriptionCouponService(
    private val inAppSubscriptionCouponRepository: InAppSubscriptionCouponRepository,
    private val inAppSubscriptionCouponRedeemRepository: InAppSubscriptionCouponRedeemRepository,
    private val inAppSubscriptionService: InAppSubscriptionService,
    private val crmRepository: CrmRepository,
    @Named(IN_APP_SUBSCRIPTION_COUPON_REDEEM_LOCK_PROVIDER) private val lockProvider: InternalLock,
) {
    fun find(code: CouponCode): List<InAppSubscriptionCoupon> {
        val coupon = inAppSubscriptionCouponRepository.find(code)
        return coupon.also {
            logger.info(
                Markers
                    .append("couponCode", code.value)
                    .andAppend("coupon", coupon),
                "InAppSubscriptionCouponService#findOrNull",
            )
        }
    }

    fun createCouponAndSendToCRM(inAppSubscriptionCouponExternalId: InAppSubscriptionCouponExternalId, description: String, couponExpiration: LocalDate, subscriptionPeriod: Period, emailAddress: EmailAddress, mobilePhone: MobilePhone?, externalOfferId: String?, internalOfferId: String?, providerEvent: String, extraParams: Map<String, String>? = null): Either<CouponError, InAppSubscriptionCoupon> {
        val coupon = createCoupon(
            code = null,
            description = description,
            free = true,
            offerGroup = null,
            quantity = 1,
            externalId = inAppSubscriptionCouponExternalId,
            externalOfferId = externalOfferId,
            expiresAt = couponExpiration.plusDays(1).atStartOfDay(brazilTimeZone).minusSeconds(1),
            subscriptionPeriod = subscriptionPeriod,
            extraParams = (extraParams ?: emptyMap()) + mapOf(
                "email" to emailAddress.value,
                "mobilePhone" to mobilePhone?.msisdn,
            ).mapNotNull { it.value },
        ).getOrElse {
            if (it is CouponError.CouponExternalIdAlreadyExists) {
                inAppSubscriptionCouponRepository.findByExternalId(inAppSubscriptionCouponExternalId)!!
            } else {
                return it.left()
            }
        }

        val fixedMobilePhone = mobilePhone?.fixOrNull()

        val crmContact = TemporaryCrmContactMinimal(
            emailAddress = emailAddress,
            mobilePhone = fixedMobilePhone,
            cupomPromo = coupon.code.value,
        )

        val customParams = mapOf(
            "coupon_code" to coupon.code.value,
            "external_id" to inAppSubscriptionCouponExternalId.value,
            "end_date" to couponExpiration.format(dateFormatBR),
            "end_date_iso" to couponExpiration.format(dateFormat),
            "external_offer_id" to externalOfferId,
            "internal_offer_id" to internalOfferId,
        )

        try {
            createContact(crmContact)
            crmRepository.publishEvent(
                emailAddress = emailAddress,
                eventName = providerEvent,
                customParams = customParams,
            )
        } catch (e: Exception) {
            logger.error(append("crmContact", crmContact).andAppend("eventName", providerEvent).andAppend("customParams", customParams), "InAppSubscriptionCouponService#createCouponAndSendToCRM", e)
        }

        return coupon.right()
    }

    fun createContact(crmContact: TemporaryCrmContactMinimal) {
        try {
            crmRepository.createContact(crmContact)
        } catch (e: Exception) {
            logger.warn(append("crmContact", crmContact), "InAppSubscriptionCouponService#createContact", e)
            crmRepository.createContact(crmContact.copy(mobilePhone = null))
        }
    }

    fun resendFailed(emailAddress: EmailAddress, mobilePhone: MobilePhone, inAppSubscriptionCouponExternalId: InAppSubscriptionCouponExternalId, couponExpiration: LocalDate, externalOfferId: String, internalOfferId: String) {
        val markers = append("emailAddress", emailAddress.value)
            .andAppend("mobilePhone", mobilePhone.msisdn)
            .andAppend("inAppSubscriptionCouponExternalId", inAppSubscriptionCouponExternalId.value)
            .andAppend("couponExpiration", couponExpiration)
            .andAppend("externalOfferId", externalOfferId)
            .andAppend("internalOfferId", internalOfferId)

        try {
            val fixedMobilePhone = mobilePhone.fixOrNull()
            markers.andAppend("fixedMobilePhone", fixedMobilePhone?.msisdn)

            val coupon = inAppSubscriptionCouponRepository.findByExternalId(inAppSubscriptionCouponExternalId) ?: throw IllegalArgumentException("coupon not found")
            markers.andAppend("couponCode", coupon.code.value)

            val crmContact = TemporaryCrmContactMinimal(
                emailAddress = emailAddress,
                mobilePhone = fixedMobilePhone,
            )
            markers.andAppend("crmContact", crmContact)

            val eventName = "external_coupon_created"
            val customParams = mapOf(
                "coupon_code" to coupon.code.value,
                "external_id" to inAppSubscriptionCouponExternalId.value,
                "end_date" to couponExpiration.format(dateFormatBR),
                "end_date_iso" to couponExpiration.format(dateFormat),
                "external_offer_id" to externalOfferId,
                "internal_offer_id" to internalOfferId,
            )
            markers.andAppend("customParams", customParams)

            createContact(crmContact)
            markers.andAppend("contactCreated", "true")

            crmRepository.publishEvent(
                emailAddress = emailAddress,
                eventName = eventName,
                customParams = customParams,
            )

            logger.info(markers, "InAppSubscriptionCouponService#resendFailed")
        } catch (e: Exception) {
            logger.error(markers, "InAppSubscriptionCouponService#resendFailed", e)
        }
    }

    fun cancelCoupon(inAppSubscriptionCouponExternalId: InAppSubscriptionCouponExternalId): Either<CouponError, Int> {
        val coupon = inAppSubscriptionCouponRepository.findByExternalId(inAppSubscriptionCouponExternalId)
            ?: return CouponError.CouponNotFound.left()

        return cancelCoupon(coupon)
    }

    private fun cancelCoupon(coupon: InAppSubscriptionCoupon): Either<CouponError, Int> {
        inAppSubscriptionCouponRepository.update(coupon.copy(enabled = false))

        val redemptions = inAppSubscriptionCouponRedeemRepository.findAllBy(coupon.id)
        val canceled = redemptions.fold(0) { acc, redemption ->
            var canceled = false
            inAppSubscriptionService.getSubscription(redemption.accountId)?.let { subscription ->
                if (subscription.reason == InAppSubscriptionReason.NO_STORE_COUPON && subscription.offStoreProductId == coupon.id.value) {
                    inAppSubscriptionService.cancelSubscription(subscription)
                    canceled = true
                }
            }
            if (canceled) {
                acc + 1
            } else {
                acc
            }
        }

        return canceled.right()
    }

    fun createCoupon(
        code: CouponCode?,
        description: String,
        free: Boolean,
        offerGroup: String?,
        quantity: Int?,
        externalId: InAppSubscriptionCouponExternalId?,
        externalOfferId: String?,
        expiresAt: ZonedDateTime?,
        subscriptionPeriod: Period,
        extraParams: Map<String, String>? = null,
    ): Either<CouponError, InAppSubscriptionCoupon> {
        val logName = "InAppSubscriptionCouponService#createCoupon"

        val command = CreateInAppSubscriptionCouponCommand(
            code = code ?: generateRandomCouponCode(),
            description = description,
            free = free,
            offerGroup = offerGroup,
            quantity = quantity ?: 1,
            externalId = externalId,
            externalOfferId = externalOfferId,
            expiresAt = expiresAt ?: getZonedDateTime().plusYears(1),
            subscriptionPeriod = subscriptionPeriod,
            extraParams = extraParams,
        )

        val markers = log("command" to command)

        if (command.expiresAt < getZonedDateTime()) {
            return logAndReturnCouponError(
                markers,
                CouponError.InvalidCoupon,
                logName,
            )
        }

        if (command.offerGroup != null && command.free) {
            return logAndReturnCouponError(
                markers,
                CouponError.InvalidCoupon,
                logName,
            )
        }

        if (command.quantity <= 0) {
            return logAndReturnCouponError(
                markers,
                CouponError.CouponWithoutQuantity,
                logName,
            )
        }

        if (command.externalId != null) {
            val previousCoupon = inAppSubscriptionCouponRepository.findByExternalId(command.externalId)
            if (previousCoupon != null) {
                return CouponError.CouponExternalIdAlreadyExists.left()
            }
        }

        val existingCoupon = inAppSubscriptionCouponRepository.findEnabledOrNull(command.code)
        if (existingCoupon != null) {
            return logAndReturnCouponError(
                markers,
                CouponError.CouponCodeAlreadyExists,
                logName,
            )
        }

        return inAppSubscriptionCouponRepository.create(command).also {
            logger.info(markers, logName)
        }.right()
    }

    private fun generateRandomCouponCode(): CouponCode {
        var code: CouponCode
        do {
            code = CouponCode(generateRandomString())
        } while (inAppSubscriptionCouponRepository.findEnabledOrNull(code) != null)

        return code
    }

    private fun generateRandomString(size: Int = 6): String {
        val dictionary = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789"
        return (1..size)
            .map { dictionary.random() }
            .joinToString("")
    }

    fun updateCoupon(command: UpdateCouponCommand): Either<CouponError, Unit> {
        val logName = "InAppSubscriptionCouponService#updateCoupon"
        val markers = log("command" to command)

        if (command.expiresAt.isBefore(getZonedDateTime())) {
            return logAndReturnCouponError(
                markers,
                CouponError.InvalidCoupon,
                logName,
            )
        }

        if (command.quantity < 0) {
            return logAndReturnCouponError(
                markers,
                CouponError.CouponWithoutQuantity,
                logName,
            )
        }

        if (command.offerGroup != null && command.free) {
            return logAndReturnCouponError(
                markers,
                CouponError.InvalidCoupon,
                logName,
            )
        }

        val dbCoupon =
            inAppSubscriptionCouponRepository.findOrNull(command.id)
                ?: return logAndReturnCouponError(markers, CouponError.CouponNotFound, logName)

        inAppSubscriptionCouponRepository
            .findEnabledOrNull(command.code)
            ?.takeIf { it.id.value != command.id.value }
            ?.run {
                return logAndReturnCouponError(markers, CouponError.CouponCodeAlreadyExists, logName)
            }

        val enabled = if (command.quantity == 0) false else command.enabled

        inAppSubscriptionCouponRepository.update(
            dbCoupon.copy(
                code = command.code,
                description = command.description,
                free = command.free,
                offerGroup = command.offerGroup,
                enabled = enabled,
                expiresAt = command.expiresAt,
                availableQuantity = command.quantity,
            ),
        )

        return Unit.right().also {
            logger.info(markers, logName)
        }
    }

    fun redeem(
        accountId: AccountId,
        code: CouponCode,
    ): Either<CouponError, InAppSubscriptionCoupon> {
        val logName = "InAppSubscriptionCouponService#redeem"
        val markers = log("accountId" to accountId.value, "couponCode" to code.value)

        val lock =
            lockProvider.acquireLock(code.value) ?: return logAndReturnCouponError(
                markers = markers,
                couponError = CouponError.AlreadyLockedException,
                logName = logName,
            )

        try {
            val lastCouponWithSameCode = inAppSubscriptionCouponRepository.find(code).maxByOrNull { it.createdAt } ?: return logAndReturnCouponError(
                markers,
                CouponError.CouponNotFound,
                logName,
            )

            if (inAppSubscriptionCouponRedeemRepository.findAllBy(accountId).any { it.couponId == lastCouponWithSameCode.id }) {
                return logAndReturnCouponError(
                    markers,
                    CouponError.CouponAlreadyRedeemedByAccount(lastCouponWithSameCode),
                    logName,
                )
            }

            markers.andAppend("coupon", lastCouponWithSameCode)

            return when {
                lastCouponWithSameCode.availableQuantity <= 0 ->
                    logAndReturnCouponError(
                        markers,
                        CouponError.CouponWithoutQuantity,
                        logName,
                    )

                !lastCouponWithSameCode.enabled ->
                    logAndReturnCouponError(
                        markers,
                        CouponError.CouponNotFound,
                        logName,
                    )

                lastCouponWithSameCode.isExpired ->
                    logAndReturnCouponError(
                        markers,
                        CouponError.ExpiredCoupon,
                        logName,
                    )

                else -> processCouponRedeem(accountId, markers, logName, lastCouponWithSameCode)
            }
        } finally {
            lock.unlock()
        }
    }

    fun validate(accountId: AccountId, code: CouponCode): Either<CouponError, InAppSubscriptionCoupon> {
        val logName = "InAppSubscriptionCouponService#validate"
        val markers = log("couponCode" to code.value)

        val lastCouponWithSameCode = inAppSubscriptionCouponRepository.find(code).maxByOrNull { it.createdAt } ?: return logAndReturnCouponError(
            markers,
            CouponError.CouponNotFound,
            logName,
        )

        if (inAppSubscriptionCouponRedeemRepository.findAllBy(accountId).any { it.couponId == lastCouponWithSameCode.id }) {
            return logAndReturnCouponError(
                markers,
                CouponError.CouponAlreadyRedeemedByAccount(lastCouponWithSameCode),
                logName,
            )
        }

        if (lastCouponWithSameCode.availableQuantity <= 0) {
            return logAndReturnCouponError(
                markers,
                CouponError.CouponWithoutQuantity,
                logName,
            )
        }

        if (lastCouponWithSameCode.isExpired) {
            return logAndReturnCouponError(
                markers,
                CouponError.ExpiredCoupon,
                logName,
            )
        }

        if (!lastCouponWithSameCode.enabled) {
            return logAndReturnCouponError(
                markers,
                CouponError.CouponNotFound,
                logName,
            )
        }

        return lastCouponWithSameCode.right()
    }

    fun findAllCoupons(): List<InAppSubscriptionCoupon> =
        inAppSubscriptionCouponRepository.findAll().also {
            logger.info(log("coupons" to it), "InAppSubscriptionCouponService#findAllCoupons")
        }

    private fun processCouponRedeem(
        accountId: AccountId,
        markers: LogstashMarker,
        logName: String,
        coupon: InAppSubscriptionCoupon,
    ): Either<CouponError, InAppSubscriptionCoupon> {
        val currentSubscription = if (coupon.offerGroup.isNullOrEmpty()) {
            val subscription = inAppSubscriptionService.getSubscription(accountId)
            markers.andAppend("subscription", subscription)

            // TODO: o usuario deve ser notificado
            if (subscription != null && subscription.status == InAppSubscriptionStatus.ACTIVE && subscription.store != null) {
                logger.error(
                    markers
                        .andAppend("couponError", CouponError.AccountHasActiveSubscription)
                        .andAppend("error", "O usuário deve cancelar a assinatura da loja")
                        .andAppend("subscription", subscription)
                        .andAppend("ACTION", "VERIFY"),
                    logName,
                )
            }

            val updatedSubscription =
                subscription?.copy(
                    accountId = accountId,
                    status = InAppSubscriptionStatus.ACTIVE,
                    endsAt = subscription.endsAt.plusDays(1).toLocalDate().atStartOfDay(brazilTimeZone).plus(coupon.subscriptionPeriod).minusSeconds(1),
                    store = null,
                    reason = InAppSubscriptionReason.NO_STORE_COUPON,
                    autoRenew = false,
                    offStoreProductId = coupon.id.value,
                ) ?: InAppSubscription(
                    accountId = accountId,
                    status = InAppSubscriptionStatus.ACTIVE,
                    endsAt = getZonedDateTime().plusDays(1).toLocalDate().atStartOfDay(brazilTimeZone).plus(coupon.subscriptionPeriod).minusSeconds(1),
                    store = null,
                    reason = InAppSubscriptionReason.NO_STORE_COUPON,
                    createdAt = getZonedDateTime(),
                    autoRenew = false,
                    inAppSubscriptionAccessConcessionId = null,
                    price = 0L,
                    productId = null,
                    offStoreProductId = coupon.id.value,
                )

            subscription?.let {
                inAppSubscriptionService.extendSubscription(updatedSubscription).also {
                    markers.andAppend("subscriptionAction", "extend")
                }
            } ?: inAppSubscriptionService.createSubscription(updatedSubscription).also {
                markers.andAppend("subscriptionAction", "create")
            }
        } else {
            null
        }

        val validSubscription = currentSubscription?.map { it }?.getOrElse {
            return logAndReturnCouponError(
                markers,
                CouponError.SubscriptionError,
                logName,
            )
        }

        val updatedCoupon = inAppSubscriptionCouponRepository.redeem(coupon.id).getOrElse {
            return logAndReturnCouponError(
                markers,
                CouponError.SubscriptionError,
                logName,
            )
        }
        inAppSubscriptionCouponRedeemRepository.createCouponRedeem(accountId, coupon, validSubscription?.endsAt)
        logger.info(
            markers
                .andAppend("updatedCoupon", updatedCoupon)
                .andAppend("hasSubscription", currentSubscription != null)
                .andAppend("subscription", currentSubscription),
            logName,
        )
        return updatedCoupon.right()
    }

    companion object {
        private val logger = LoggerFactory.getLogger(InAppSubscriptionService::class.java)
        const val IN_APP_SUBSCRIPTION_COUPON_REDEEM_LOCK_PROVIDER = "in-app-subscription-coupon-redeem"

        private fun logAndReturnCouponError(
            markers: LogstashMarker,
            couponError: CouponError,
            logName: String,
            logLevel: LogLevel = LogLevel.INFO,
        ): Either<CouponError, Nothing> {
            markers
                .andAppend("couponError", couponError)
                .andAppend("error", couponError.errorMessage)

            when (logLevel) {
                LogLevel.WARN -> logger.warn(markers, logName)
                LogLevel.ERROR -> logger.error(markers, logName)
                else -> logger.info(markers, logName)
            }

            return couponError.left()
        }
    }
}