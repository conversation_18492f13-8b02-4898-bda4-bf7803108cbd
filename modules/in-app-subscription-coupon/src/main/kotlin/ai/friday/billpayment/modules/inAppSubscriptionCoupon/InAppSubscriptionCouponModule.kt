package ai.friday.billpayment.modules.inAppSubscriptionCoupon

import io.micronaut.context.annotation.DefaultScope
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton

@DefaultScope(Singleton::class)
@Requires(property = "modules.in-app-subscription-coupon.enabled", value = "true")
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
annotation class InAppSubscriptionCouponModule

@DefaultScope(Singleton::class)
@Requirements(Requires(notEnv = ["test"]), Requires(property = "modules.in-app-subscription-coupon.enabled", value = "true"))
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
annotation class InAppSubscriptionCouponNoTest