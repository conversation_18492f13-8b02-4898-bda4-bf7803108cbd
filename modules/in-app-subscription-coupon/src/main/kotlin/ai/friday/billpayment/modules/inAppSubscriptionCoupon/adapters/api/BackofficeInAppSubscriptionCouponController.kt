package ai.friday.billpayment.modules.inAppSubscriptionCoupon.adapters.api

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.Role.Code.ADMIN
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.log
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.InAppSubscriptionCouponModule
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponCode
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponError
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponId
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCoupon
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCouponService
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.UpdateCouponCommand
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import java.time.LocalDate
import java.time.LocalTime
import java.time.Period
import java.time.format.DateTimeFormatter
import org.slf4j.LoggerFactory

@Secured(BACKOFFICE)
@Controller("/backoffice/in-app-subscription/coupon")
@InAppSubscriptionCouponModule
class BackofficeInAppSubscriptionCouponController(
    private val inAppSubscriptionCouponService: InAppSubscriptionCouponService,
) {
    @Get("/{code}")
    fun getCoupon(
        @PathVariable code: String,
    ): HttpResponse<*> {
        val logName = "BackofficeInAppSubscriptionCouponController#getCoupon"
        val markers = log("code" to code)

        return try {
            val coupons =
                inAppSubscriptionCouponService
                    .find(CouponCode(code))
                    .map { it.toCouponResponseTO() }
            markers.andAppend("coupon", coupons)
            logger.info(markers, logName)
            HttpResponse.ok(coupons)
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    @Post("/redeem")
    @Secured(Role.Code.ADMIN)
    fun redeem(
        authentication: Authentication,
        @Body body: RedeemTO,
    ): HttpResponse<*> {
        val accountIdObject = AccountId(body.accountId)
        val logName = "BackofficeInAppSubscriptionCouponController#redeem"
        val markers = log("accountId" to body.accountId, "couponCode" to body.code)
            .andAppend("admin", authentication.toAccountId())

        try {
            val coupon = inAppSubscriptionCouponService.redeem(accountIdObject, CouponCode(body.code)).getOrElse { error ->
                return when (error) {
                    is CouponError.CouponAlreadyRedeemedByAccount -> {
                        logger.info(markers.andAppend("coupon", error.coupon), logName)
                        HttpResponse.noContent<Unit>()
                    }

                    is CouponError.CouponNotFound, is CouponError.ExpiredCoupon -> {
                        logger.warn(markers.andAppend("couponError", error), logName)
                        HttpResponse.badRequest<Unit>()
                    }

                    is CouponError.CouponWithoutQuantity -> {
                        logger.warn(markers.andAppend("couponError", error), logName)
                        HttpResponse.status<Unit>(HttpStatus.CONFLICT)
                    }

                    else -> {
                        logger.error(markers.andAppend("couponError", error), logName)
                        HttpResponse.serverError<Unit>()
                    }
                }
            }

            logger.info(markers.andAppend("coupon", coupon), logName)
            return HttpResponse.noContent<Unit>()
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            return HttpResponse.serverError<Unit>()
        }
    }

    @Get("/all")
    fun getCoupons(): HttpResponse<*> {
        val logName = "BackofficeInAppSubscriptionCouponController#getCoupons"

        return try {
            val coupons =
                inAppSubscriptionCouponService
                    .findAllCoupons()
                    .map { it.toCouponResponseTO() }
            logger.info(log("couponsSize" to coupons.size), logName)
            HttpResponse.ok(coupons)
        } catch (ex: Exception) {
            logger.error(logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    @Secured(ADMIN)
    @Post
    fun createIndividualCoupon(
        @Body createCouponTO: CreateCouponTO,
    ): HttpResponse<*> {
        val logName = "BackofficeInAppSubscriptionCouponController#createIndividualCoupon"
        val markers = log("couponCode" to createCouponTO.code, "coupon" to createCouponTO)

        return try {
            val result = inAppSubscriptionCouponService.createCoupon(
                code = createCouponTO.code?.let { CouponCode(it) },
                description = createCouponTO.description,
                free = createCouponTO.free,
                offerGroup = createCouponTO.offerGroup,
                quantity = createCouponTO.quantity,
                externalId = null,
                externalOfferId = null,
                expiresAt = LocalDate.parse(createCouponTO.expiresAt, DateTimeFormatter.ISO_DATE).plusDays(1).atStartOfDay(brazilTimeZone).minusSeconds(1),
                subscriptionPeriod = Period.parse(createCouponTO.subscriptionPeriod),
            )

            result.fold(
                { error ->
                    logger.error(markers.andAppend("couponError", error), logName)
                    HttpResponse.serverError<Unit>()
                },
                {
                    val coupons = inAppSubscriptionCouponService.find(it.code)
                    logger.info(markers.andAppend("coupon", coupons), logName)
                    HttpResponse.ok(coupons.map { it.toCouponResponseTO() })
                },
            )
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    @Secured(ADMIN)
    @Put
    fun updateIndividualCoupon(
        @Body updateCouponTO: UpdateCouponTO,
    ): HttpResponse<*> {
        val logName = "BackofficeInAppSubscriptionCouponController#updateIndividualCoupon"
        val markers =
            log(
                "couponId" to updateCouponTO.id,
                "couponCode" to updateCouponTO.code,
                "coupon" to updateCouponTO,
            )

        return try {
            val result = inAppSubscriptionCouponService.updateCoupon(updateCouponTO.toCommand())

            result.fold(
                { error ->
                    logger.error(markers.andAppend("couponError", error), logName)
                    HttpResponse.serverError<Unit>()
                },
                {
                    val coupons = inAppSubscriptionCouponService.find(CouponCode(updateCouponTO.code))
                    logger.info(markers.andAppend("coupon", coupons), logName)
                    HttpResponse.ok(coupons.map { it.toCouponResponseTO() })
                },
            )
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(BackofficeInAppSubscriptionCouponController::class.java)
    }
}

data class RedeemTO(
    val code: String,
    val accountId: String,
)

data class CouponResponseTO(
    val id: String,
    val code: String,
    val description: String,
    val expiresAt: String,
    val free: Boolean = false,
    val offerGroup: String? = null,
    val quantity: Int,
    val originalQuantity: Int,
    val enabled: Boolean,
    val subscriptionPeriod: String,
    val createdAt: String,
    val externalId: String?,
    val extraParams: Map<String, String>?,
)

data class CreateCouponTO(
    val code: String?,
    val description: String,
    val expiresAt: String,
    val subscriptionPeriod: String,
    val free: Boolean = false,
    val offerGroup: String? = null,
    val quantity: Int,
)

data class UpdateCouponTO(
    val id: String,
    val code: String,
    val description: String,
    val expiresAt: String,
    val free: Boolean = false,
    val offerGroup: String? = null,
    val quantity: Int,
    val enabled: Boolean,
)

private fun UpdateCouponTO.toCommand() =
    UpdateCouponCommand(
        id = CouponId(id),
        code = CouponCode(code),
        description = description,
        free = free,
        offerGroup = offerGroup,
        quantity = quantity,
        enabled = enabled,
        expiresAt = LocalDate.parse(expiresAt, DateTimeFormatter.ISO_DATE).atTime(LocalTime.MAX).atZone(brazilTimeZone),
    )

private fun InAppSubscriptionCoupon.toCouponResponseTO() =
    CouponResponseTO(
        id = id.value,
        code = code.value,
        description = description,
        free = free,
        offerGroup = offerGroup,
        quantity = availableQuantity,
        originalQuantity = originalQuantity,
        enabled = enabled,
        expiresAt = expiresAt.format(DateTimeFormatter.ISO_DATE),
        subscriptionPeriod = subscriptionPeriod.toString(),
        createdAt = createdAt.format(DateTimeFormatter.ISO_DATE_TIME),
        externalId = externalId?.value,
        extraParams = extraParams,
    )