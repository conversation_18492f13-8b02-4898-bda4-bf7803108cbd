package ai.friday.billpayment.modules.inAppSubscriptionCoupon.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.AbstractBillPaymentDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.GlobalSecondaryIndexNames
import ai.friday.billpayment.adapters.dynamodb.INDEX_1
import ai.friday.billpayment.adapters.dynamodb.INDEX_2
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.InAppSubscriptionCouponModule
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponId
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponRedeemId
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCoupon
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCouponRedeem
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCouponRedeemRepository
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val SCAN_KEY = "IN_APP_SUBSCRIPTION_COUPON_REDEEM"

@InAppSubscriptionCouponModule
class InAppSubscriptionCouponRedeemDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<CouponRedeemEntity>(cli, CouponRedeemEntity::class.java)

@InAppSubscriptionCouponModule
class InAppSubscriptionCouponRedeemDbRepository(
    private val client: InAppSubscriptionCouponRedeemDynamoDAO,
) : InAppSubscriptionCouponRedeemRepository {
    override fun createCouponRedeem(accountId: AccountId, coupon: InAppSubscriptionCoupon, endsAt: ZonedDateTime?): InAppSubscriptionCouponRedeem {
        val redeemId = CouponRedeemId()
        val createdAt = getZonedDateTime()
        val entity =
            CouponRedeemEntity().apply {
                partitionKey = redeemId.value
                sortKey = SCAN_KEY
                gSIndex1PartitionKey = "$SCAN_KEY#${accountId.value}"
                gSIndex1SortKey = createdAt.format(DateTimeFormatter.ISO_DATE_TIME)
                gSIndex2PartitionKey = "$SCAN_KEY#${coupon.id.value}"
                gSIndex2SortKey = createdAt.format(DateTimeFormatter.ISO_DATE_TIME)
                couponId = coupon.id.value
                this.accountId = accountId.value
                offerGroup = coupon.offerGroup
                subscriptionEndsAt = endsAt?.format(DateTimeFormatter.ISO_DATE_TIME)
                updatedAt = createdAt.format(DateTimeFormatter.ISO_DATE_TIME)
                this.createdAt = createdAt.format(DateTimeFormatter.ISO_DATE_TIME)
            }
        client.save(entity)
        return entity.toInAppSubscriptionCouponRedeem()
    }

    override fun findAllBy(accountId: AccountId): List<InAppSubscriptionCouponRedeem> =
        client
            .findByPartitionKeyOnIndex(
                index = GlobalSecondaryIndexNames.GSIndex1,
                partitionKey = "$SCAN_KEY#${accountId.value}",
            ).map { it.toInAppSubscriptionCouponRedeem() }

    override fun findAllBy(couponId: CouponId): List<InAppSubscriptionCouponRedeem> =
        client
            .findByPartitionKeyOnIndex(
                index = GlobalSecondaryIndexNames.GSIndex2,
                partitionKey = "$SCAN_KEY#${couponId.value}",
            ).map { it.toInAppSubscriptionCouponRedeem() }

    override fun findOrNull(redeemId: CouponRedeemId): InAppSubscriptionCouponRedeem? =
        client
            .findByPrimaryKey(
                partitionKey = redeemId.value,
                sortKey = SCAN_KEY,
            )?.toInAppSubscriptionCouponRedeem()
}

@DynamoDbBean
class CouponRedeemEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String

    @get:DynamoDbAttribute(value = "AccountId")
    lateinit var accountId: String

    @get:DynamoDbAttribute(value = "CouponId")
    lateinit var couponId: String

    @get:DynamoDbAttribute(value = "SubscriptionEndsAt")
    var subscriptionEndsAt: String? = null

    @get:DynamoDbAttribute(value = "OfferGroup")
    var offerGroup: String? = null

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1SortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    lateinit var gSIndex2PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    lateinit var gSIndex2SortKey: String

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute(value = "UpdatedAt")
    lateinit var updatedAt: String
}

private fun CouponRedeemEntity.toInAppSubscriptionCouponRedeem(): InAppSubscriptionCouponRedeem =
    InAppSubscriptionCouponRedeem(
        id = CouponRedeemId(partitionKey),
        couponId = CouponId(this.couponId),
        accountId = AccountId(this.accountId),
        subscriptionEndsAt =
        if (this.subscriptionEndsAt != null) {
            ZonedDateTime.parse(this.subscriptionEndsAt, DateTimeFormatter.ISO_DATE_TIME)
        } else {
            getZonedDateTime().plusMonths(3)
        },
        offerGroup = this.offerGroup,
        updatedAt = ZonedDateTime.parse(this.updatedAt, DateTimeFormatter.ISO_DATE_TIME),
        createdAt = ZonedDateTime.parse(this.createdAt, DateTimeFormatter.ISO_DATE_TIME),
    )