import ai.friday.billpayment.adapters.dynamodb.BILLS_SEARCH_TABLE_NAME
import ai.friday.billpayment.adapters.dynamodb.BILL_EVENT_TABLE_NAME
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_TABLE_NAME
import ai.friday.billpayment.adapters.dynamodb.GlobalSecondaryIndexNames
import ai.friday.billpayment.adapters.dynamodb.OPEN_FINANCE_DATA_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.OPEN_FINANCE_DATA_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.OPEN_FINANCE_DATA_TABLE_NAME
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.LocalDbCreationRule.Companion.getDynamoDBProxyServer
import java.net.URI
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.AttributeDefinition
import software.amazon.awssdk.services.dynamodb.model.CreateTableRequest
import software.amazon.awssdk.services.dynamodb.model.CreateTableResponse
import software.amazon.awssdk.services.dynamodb.model.DeleteTableRequest
import software.amazon.awssdk.services.dynamodb.model.DescribeTableRequest
import software.amazon.awssdk.services.dynamodb.model.GlobalSecondaryIndex
import software.amazon.awssdk.services.dynamodb.model.KeySchemaElement
import software.amazon.awssdk.services.dynamodb.model.KeyType
import software.amazon.awssdk.services.dynamodb.model.Projection
import software.amazon.awssdk.services.dynamodb.model.ProjectionType
import software.amazon.awssdk.services.dynamodb.model.ProvisionedThroughput
import software.amazon.awssdk.services.dynamodb.model.ScalarAttributeType
import software.amazon.awssdk.services.dynamodb.model.TableStatus

object DynamoDBUtils {
    fun setupDynamoDB(): DynamoDbEnhancedClient {
        val dynamoDbClient = getDynamoDbClient()
        createTables(dynamoDbClient, null)

        return DynamoDbEnhancedClient
            .builder()
            .dynamoDbClient(dynamoDbClient).build()
    }

    fun setupDynamoDBAsync() {
        val dynamoDbAsyncClient = getDynamoDbClientAsync()
        createTables(null, dynamoDbAsyncClient)
    }

    private fun createTables(
        dynamoDbClient: DynamoDbClient? = null,
        dynamoDbAsyncClient: DynamoDbAsyncClient? = null,
    ) {
        createBillPaymentTable(dynamoDbClient = dynamoDbClient, dynamoDbAsyncClient = dynamoDbAsyncClient)
        createBillEventTable(dynamoDbClient = dynamoDbClient, dynamoDbAsyncClient = dynamoDbAsyncClient)
        createUtilityTable(dynamoDbClient = dynamoDbClient, dynamoDbAsyncClient = dynamoDbAsyncClient)
        createOpenFinanceTable(dynamoDbClient = dynamoDbClient, dynamoDbAsyncClient = dynamoDbAsyncClient)
    }

    fun getDynamoDB(): DynamoDbEnhancedClient {
        val cli = getDynamoDbClient()
        return DynamoDbEnhancedClient
            .builder()
            .dynamoDbClient(cli).build()
    }

    fun getDynamoDBAsync(): DynamoDbEnhancedAsyncClient {
        val cli = getDynamoDbClientAsync()

        return DynamoDbEnhancedAsyncClient
            .builder()
            .dynamoDbClient(cli).build()
    }

    fun getDynamoDbClientAsync(): DynamoDbAsyncClient {
        getDynamoDBProxyServer()
        return DynamoDbAsyncClient
            .builder()
            .region(Region.US_EAST_1)
            .endpointOverride(URI.create("http://${LocalDbCreationRule.dynamoDBHost}:${LocalDbCreationRule.dynamoDBPort}"))
            .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create("access", "secret")))
            .build()
    }

    fun getDynamoDbClient(): DynamoDbClient {
        getDynamoDBProxyServer()
        return DynamoDbClient
            .builder()
            .region(Region.US_EAST_1)
            .endpointOverride(URI.create("http://${LocalDbCreationRule.dynamoDBHost}:${LocalDbCreationRule.dynamoDBPort}"))
            .region(Region.US_EAST_1)
            .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create("access", "secret")))
            .build()
    }

    fun createUtilityTable(
        dynamoDbClient: DynamoDbClient? = null,
        dynamoDbAsyncClient: DynamoDbAsyncClient? = null,
    ) {
        createTable(
            dynamoDbClient,
            dynamoDbAsyncClient,
            BILL_PAYMENT_TABLE_NAME,
            BILL_PAYMENT_PARTITION_KEY,
            BILL_PAYMENT_RANGE_KEY,
            "GSIndex1PrimaryKey",
            "GSIndex1ScanKey",
            "GSIndex2PrimaryKey",
            "GSIndex2ScanKey",
            "GSIndex3PrimaryKey",
            "GSIndex3ScanKey",
            "GSIndex4PrimaryKey",
            "GSIndex4ScanKey",
        )
    }

    fun createOpenFinanceTable(
        dynamoDbClient: DynamoDbClient? = null,
        dynamoDbAsyncClient: DynamoDbAsyncClient? = null,
    ) {
        createTable(
            dynamoDbClient,
            dynamoDbAsyncClient,
            OPEN_FINANCE_DATA_TABLE_NAME,
            OPEN_FINANCE_DATA_PARTITION_KEY,
            OPEN_FINANCE_DATA_RANGE_KEY,
            "GSIndex1PartitionKey",
            "GSIndex1RangeKey",
            "GSIndex2PartitionKey",
            "GSIndex2RangeKey",
        )
    }

    fun createBillPaymentTable(
        dynamoDbClient: DynamoDbClient? = null,
        dynamoDbAsyncClient: DynamoDbAsyncClient? = null,
    ) {
        createTable(
            dynamoDbClient,
            dynamoDbAsyncClient,
            BILL_PAYMENT_TABLE_NAME,
            BILL_PAYMENT_PARTITION_KEY,
            BILL_PAYMENT_RANGE_KEY,
            "GSIndex1PrimaryKey",
            "GSIndex1ScanKey",
            "GSIndex2PrimaryKey",
            "GSIndex2ScanKey",
            "GSIndex3PrimaryKey",
            "GSIndex3ScanKey",
        )
    }

    fun createBillEventTable(
        dynamoDbClient: DynamoDbClient? = null,
        dynamoDbAsyncClient: DynamoDbAsyncClient? = null,
    ) {
        createTable(
            dynamoDbClient,
            dynamoDbAsyncClient,
            BILL_EVENT_TABLE_NAME,
            BILL_PAYMENT_PARTITION_KEY,
            BILL_PAYMENT_RANGE_KEY,
            "GSIndex1PrimaryKey",
            "GSIndex1ScanKey",
        )
    }

    fun createOpenFinanceDataTable(
        dynamoDbClient: DynamoDbClient? = null,
        dynamoDbAsyncClient: DynamoDbAsyncClient? = null,
    ) {
        createTable(
            dynamoDbClient,
            dynamoDbAsyncClient,
            OPEN_FINANCE_DATA_TABLE_NAME,
            OPEN_FINANCE_DATA_PARTITION_KEY,
            OPEN_FINANCE_DATA_RANGE_KEY,
            "GSIndex1PartitionKey",
            "GSIndex1RangeKey",
        )
    }

    fun createBillsSearchTable(
        dynamoDbClient: DynamoDbClient? = null,
        dynamoDbAsyncClient: DynamoDbAsyncClient? = null,
    ) {
        createTable(
            dynamoDbClient,
            dynamoDbAsyncClient,
            BILLS_SEARCH_TABLE_NAME,
            BILL_PAYMENT_PARTITION_KEY,
            BILL_PAYMENT_RANGE_KEY,
            "GSIndex1PrimaryKey",
            "GSIndex1ScanKey",
        )
    }

    private fun createTable(
        dynamoDbClient: DynamoDbClient? = null,
        dynamoDbAsyncClient: DynamoDbAsyncClient? = null,
        tableName: String,
        hashKeyName: String,
        rangeKeyName: String,
        gsi1HashKeyName: String,
        gsi1RangeKeyName: String,
        gsi2HashKeyName: String? = null,
        gsi2RangeKeyName: String? = null,
        gsi3HashKeyName: String? = null,
        gsi3RangeKeyName: String? = null,
        gsi4HashKeyName: String? = null,
        gsi4RangeKeyName: String? = null,
    ): CreateTableResponse {
        val attributeDefinitions =
            mutableListOf(
                AttributeDefinition.builder().attributeName(hashKeyName).attributeType(ScalarAttributeType.S).build(),
                AttributeDefinition.builder().attributeName(rangeKeyName).attributeType(ScalarAttributeType.S).build(),
                AttributeDefinition.builder().attributeName(gsi1HashKeyName).attributeType(ScalarAttributeType.S).build(),
                AttributeDefinition.builder().attributeName(gsi1RangeKeyName).attributeType(ScalarAttributeType.S).build(),
            )
        gsi2HashKeyName?.let {
            attributeDefinitions.add(
                AttributeDefinition.builder().attributeName(it).attributeType(ScalarAttributeType.S).build(),
            )
        }
        gsi2RangeKeyName?.let {
            attributeDefinitions.add(
                AttributeDefinition.builder().attributeName(it).attributeType(ScalarAttributeType.S).build(),
            )
        }
        gsi3HashKeyName?.let {
            attributeDefinitions.add(
                AttributeDefinition.builder().attributeName(it).attributeType(ScalarAttributeType.S).build(),
            )
        }
        gsi3RangeKeyName?.let {
            attributeDefinitions.add(
                AttributeDefinition.builder().attributeName(it).attributeType(ScalarAttributeType.S).build(),
            )
        }
        gsi4HashKeyName?.let {
            attributeDefinitions.add(
                AttributeDefinition.builder().attributeName(it).attributeType(ScalarAttributeType.S).build(),
            )
        }
        gsi4RangeKeyName?.let {
            attributeDefinitions.add(
                AttributeDefinition.builder().attributeName(it).attributeType(ScalarAttributeType.S).build(),
            )
        }
        return createTable(
            dynamoDbClient,
            dynamoDbAsyncClient,
            tableName,
            hashKeyName,
            rangeKeyName,
            gsi1HashKeyName,
            gsi1RangeKeyName,
            gsi2HashKeyName,
            gsi2RangeKeyName,
            gsi3HashKeyName,
            gsi3RangeKeyName,
            gsi4HashKeyName,
            gsi4RangeKeyName,
            attributeDefinitions,
        )
    }

    private fun createTable(
        dynamoDbClient: DynamoDbClient? = null,
        dynamoDbAsyncClient: DynamoDbAsyncClient? = null,
        tableName: String,
        hashKeyName: String,
        rangeKeyName: String,
        gsi1HashKeyName: String,
        gsi1RangeKeyName: String,
        gsi2HashKeyName: String?,
        gsi2RangeKeyName: String?,
        gsi3HashKeyName: String?,
        gsi3RangeKeyName: String?,
        gsi4HashKeyName: String?,
        gsi4RangeKeyName: String?,
        attributeDefinitions: List<AttributeDefinition>,
    ): CreateTableResponse {
        try {
            val deleteTableRequest = DeleteTableRequest.builder().tableName(tableName).build()
            if (dynamoDbAsyncClient == null) {
                dynamoDbClient!!.deleteTable(deleteTableRequest)
            } else {
                dynamoDbAsyncClient.deleteTable(deleteTableRequest).get()
            }
        } catch (_: Exception) {
        }
        val ks =
            listOf(
                KeySchemaElement.builder().attributeName(hashKeyName).keyType(KeyType.HASH).build(),
                KeySchemaElement.builder().attributeName(rangeKeyName).keyType(KeyType.RANGE).build(),
            )
        val globalSecondaryIndex: MutableList<GlobalSecondaryIndex> = ArrayList()
        addIndex(globalSecondaryIndex, GlobalSecondaryIndexNames.GSIndex1, gsi1HashKeyName, gsi1RangeKeyName)
        addIndex(globalSecondaryIndex, GlobalSecondaryIndexNames.GSIndex2, gsi2HashKeyName, gsi2RangeKeyName)
        addIndex(globalSecondaryIndex, GlobalSecondaryIndexNames.GSIndex3, gsi3HashKeyName, gsi3RangeKeyName)
        addIndex(globalSecondaryIndex, GlobalSecondaryIndexNames.GSIndex4, gsi4HashKeyName, gsi4RangeKeyName)
        val provisionedThroughput =
            ProvisionedThroughput.builder().readCapacityUnits(1000L).writeCapacityUnits(1000L).build()
        val request =
            CreateTableRequest.builder()
                .tableName(tableName)
                .attributeDefinitions(attributeDefinitions)
                .keySchema(ks)
                .globalSecondaryIndexes(globalSecondaryIndex)
                .provisionedThroughput(provisionedThroughput).build()

        val result =
            if (dynamoDbAsyncClient == null) {
                dynamoDbClient!!.createTable(request)
            } else {
                dynamoDbAsyncClient.createTable(request).get()
            }

        val describeTableRequest = DescribeTableRequest.builder().tableName(tableName).build()

        val describeTableResponse =
            if (dynamoDbAsyncClient == null) {
                dynamoDbClient!!.describeTable(describeTableRequest)
            } else {
                dynamoDbAsyncClient.describeTable(describeTableRequest).get()
            }

        while (describeTableResponse.table().tableStatus() != TableStatus.ACTIVE) {
            // wait til table is active or otherwise will not find table
        }
        return result
    }

    private fun addIndex(
        globalSecondaryIndex: MutableList<GlobalSecondaryIndex>,
        index: GlobalSecondaryIndexNames,
        partitionKeyName: String?,
        rangeKeyName: String?,
    ) {
        if (partitionKeyName != null) {
            val gsi =
                listOf(
                    KeySchemaElement.builder().attributeName(partitionKeyName).keyType(KeyType.HASH).build(),
                    KeySchemaElement.builder().attributeName(rangeKeyName).keyType(KeyType.RANGE).build(),
                )
            globalSecondaryIndex.add(
                GlobalSecondaryIndex.builder()
                    .keySchema(gsi)
                    .indexName(index.name)
                    .projection(Projection.builder().projectionType(ProjectionType.ALL).build())
                    .provisionedThroughput(
                        ProvisionedThroughput.builder().readCapacityUnits(1000L).writeCapacityUnits(1000L).build(),
                    ).build(),
            )
        }
    }

//    fun putItem(client: DynamoDbEnhancedClient, tableName: String, item: DDAEntity) {
//        val table = client.table(tableName, TableSchema.fromBean(DDAEntity::class.java))
//        table.putItem(item)
//    }
//
//    fun getItem(client: AmazonDynamoDB?, tableName: String?, primaryKey: String?, scanKey: String?): Item {
//        val dynamoDB = DynamoDB(client)
//        val table = dynamoDB.getTable(tableName)
//        return table.getItem(BILL_PAYMENT_PARTITION_KEY, primaryKey, BILL_PAYMENT_RANGE_KEY, scanKey)
//    }
//
//    fun removeItem(client: AmazonDynamoDB?, tableName: String?, hashKey: String?, rangeKey: String?) {
//        val dynamoDB = DynamoDB(client)
//        val table = dynamoDB.getTable(tableName)
//        table.deleteItem(BILL_PAYMENT_PARTITION_KEY, hashKey, BILL_PAYMENT_RANGE_KEY, rangeKey)
//    }
}