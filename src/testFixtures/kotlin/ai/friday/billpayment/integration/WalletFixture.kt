package ai.friday.billpayment.integration

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.FULL_FORMAT
import ai.friday.billpayment.adapters.dynamodb.DEFAULT_LEGACY_ACCOUNT_CONFIGURATION
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.CreditCardConfiguration
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.DailyPaymentLimitType
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletStatus
import ai.friday.billpayment.app.wallet.WalletType
import ai.friday.billpayment.paymentMethodId
import ai.friday.billpayment.paymentMethodId2
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import com.nimbusds.jwt.JWTClaimsSet
import io.micronaut.http.MutableHttpRequest
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.util.*

class WalletFixture(
    founderAccountId: AccountId = AccountId("ACCOUNT-${UUID.randomUUID()}"),
    val defaultWalletId: WalletId = WalletId("WALLET-AAAAA-BBBBBB-CCCCCCC-DDDDD"),
    founderDocument: String = DOCUMENT_2,
) {

    val founderAccount = ACCOUNT.copy(
        accountId = founderAccountId,
        document = founderDocument,
        name = "Fulano de Tal",
        emailAddress = EmailAddress("<EMAIL>"),
        configuration = DEFAULT_LEGACY_ACCOUNT_CONFIGURATION.copy(
            defaultWalletId = defaultWalletId,
            creditCardConfiguration = CreditCardConfiguration(quota = 20000),
        ),
    )
    val cofounderAccount = ACCOUNT.copy(
        accountId = AccountId("ACCOUNT-${UUID.randomUUID()}"),
        document = DOCUMENT_3,
        name = "Cofulano de Tal",
        emailAddress = EmailAddress("<EMAIL>"),
        configuration = DEFAULT_LEGACY_ACCOUNT_CONFIGURATION.copy(
            defaultWalletId = defaultWalletId,
            creditCardConfiguration = CreditCardConfiguration(quota = 20000),
        ),
    )
    val participantAccount = ACCOUNT.copy(
        accountId = AccountId("ACCOUNT-${UUID.randomUUID()}"),
        document = "***********",
        name = "Ciclano de Tal",
        emailAddress = EmailAddress("<EMAIL>"),
        configuration = DEFAULT_LEGACY_ACCOUNT_CONFIGURATION.copy(
            defaultWalletId = defaultWalletId,
        ),
    )
    val limitedParticipantAccount =
        ACCOUNT.copy(
            accountId = AccountId("ACCOUNT-${UUID.randomUUID()}"),
            configuration = DEFAULT_LEGACY_ACCOUNT_CONFIGURATION.copy(
                defaultWalletId = defaultWalletId,
            ),
        )
    val ultraLimitedParticipantAccount = ACCOUNT.copy(
        accountId = AccountId("ACCOUNT-${UUID.randomUUID()}"),
        document = "***********",
        name = "Beltrano",
        emailAddress = EmailAddress("<EMAIL>"),
        configuration = DEFAULT_LEGACY_ACCOUNT_CONFIGURATION.copy(
            defaultWalletId = defaultWalletId,
        ),
    )
    val cantPayParticipantAccount = ACCOUNT.copy(
        accountId = AccountId("ACCOUNT-${UUID.randomUUID()}"),
        document = "***********",
        name = "Zezinho",
        emailAddress = EmailAddress("<EMAIL>"),
        configuration = DEFAULT_LEGACY_ACCOUNT_CONFIGURATION.copy(defaultWalletId = defaultWalletId),
    )
    val assistantAccount = ACCOUNT.copy(
        accountId = AccountId("ACCOUNT-${UUID.randomUUID()}"),
        document = "***********",
        name = "Assistant",
        emailAddress = EmailAddress("<EMAIL>"),
        configuration = DEFAULT_LEGACY_ACCOUNT_CONFIGURATION.copy(
            defaultWalletId = defaultWalletId,
            creditCardConfiguration = CreditCardConfiguration(
                quota = 5000_00,
            ),
        ),
    )
    val removedAccount = ACCOUNT.copy(
        accountId = AccountId("ACCOUNT-${UUID.randomUUID()}"),
        document = "***********",
        name = "Assistant Removido",
        emailAddress = EmailAddress("<EMAIL>"),
        configuration = DEFAULT_LEGACY_ACCOUNT_CONFIGURATION.copy(defaultWalletId = defaultWalletId),
    )

    val founder = founderAccount.toMember(
        type = MemberType.FOUNDER,
        permissions = MemberPermissions.of(MemberType.FOUNDER),
    )
    val cofounder = cofounderAccount.toMember(
        type = MemberType.COFOUNDER,
        permissions = MemberPermissions.of(MemberType.COFOUNDER),
    )
    val participant = participantAccount.toMember(
        type = MemberType.COLLABORATOR,
        permissions = MemberPermissions(
            viewBills = BillPermission.ALL_BILLS,
            scheduleBills = BillPermission.ALL_BILLS,
            viewBalance = true,
            notification = true,
        ),
    )
    val limitedParticipant = limitedParticipantAccount.toMember(
        type = MemberType.COLLABORATOR,
        permissions = MemberPermissions(
            manageMembers = false,
            viewBills = BillPermission.ALL_BILLS,
            scheduleBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
            viewBalance = true,
            notification = true,
        ),
    )
    val ultraLimitedParticipant = ultraLimitedParticipantAccount.toMember(
        type = MemberType.COLLABORATOR,
        permissions = MemberPermissions(
            viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
            scheduleBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
            viewBalance = true,
            notification = true,
        ),
    )
    val removedParticipant = removedAccount.toMember(
        type = MemberType.COLLABORATOR,
        permissions = MemberPermissions(
            viewBills = BillPermission.ALL_BILLS,
            scheduleBills = BillPermission.ALL_BILLS,
            founderContactsEnabled = false,
            viewBalance = true,
            notification = true,
        ),
        status = MemberStatus.REMOVED,
    )
    val cantPayParticipant = cantPayParticipantAccount.toMember(
        type = MemberType.COLLABORATOR,
        permissions = MemberPermissions(
            viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
            scheduleBills = BillPermission.NO_BILLS,
            viewBalance = true,
            notification = true,
        ),
    )
    val assistant = Member(
        accountId = assistantAccount.accountId,
        document = assistantAccount.document,
        name = assistantAccount.name,
        emailAddress = assistantAccount.emailAddress,
        type = MemberType.ASSISTANT,
        status = MemberStatus.ACTIVE,
        permissions = MemberPermissions(
            viewBills = BillPermission.ALL_BILLS,
            scheduleBills = BillPermission.NO_BILLS,
            founderContactsEnabled = true,
            viewBalance = false,
            notification = true,
        ),
        created = getZonedDateTime(),
        updated = getZonedDateTime(),
    )

    fun buildWallet(
        name: String = "carteira",
        walletFounder: Member = founder,
        otherMembers: List<Member> = listOf(),
        maxOpenInvitations: Int = 10,
        status: WalletStatus = WalletStatus.ACTIVE,
        type: WalletType = WalletType.PRIMARY,
        accountPaymentMethodId: AccountPaymentMethodId = paymentMethodId,
        id: WalletId = defaultWalletId,
    ) =
        Wallet(
            id = if (type == WalletType.SECONDARY && id == defaultWalletId) WalletId("WALLET-${UUID.randomUUID()}") else id,
            name = name,
            members = listOf(walletFounder) + otherMembers,
            maxOpenInvitations = maxOpenInvitations,
            status = status,
            type = type,
            paymentMethodId = accountPaymentMethodId,
        )

    fun buildPrimaryWallet(
        founderAccount: Account,
        name: String = "outra carteira",
        otherMembers: List<Member> = listOf(),
        maxOpenInvitations: Int = 10,
        status: WalletStatus = WalletStatus.ACTIVE,
        type: WalletType = WalletType.PRIMARY,
        accountPaymentMethodId: AccountPaymentMethodId = paymentMethodId,
        walletId: WalletId? = null,
    ): Wallet {
        val participantAsFounder = founderAccount.toMember(
            type = MemberType.FOUNDER,
            permissions = MemberPermissions(
                viewBills = BillPermission.ALL_BILLS,
                scheduleBills = BillPermission.ALL_BILLS,
                viewBalance = true,
                notification = true,
            ),
        )
        return Wallet(
            id = walletId ?: WalletId("WALLET-${UUID.randomUUID()}"),
            name = name,
            members = listOf(participantAsFounder) + otherMembers,
            maxOpenInvitations = maxOpenInvitations,
            status = status,
            type = type,
            paymentMethodId = accountPaymentMethodId,
        )
    }

    fun buildSecondaryWallet(
        founderAccount: Account,
        coFounderAccount: Account? = null,
        name: String = "carteira secundária",
        otherMembers: List<Member> = listOf(),
        maxOpenInvitations: Int = 10,
        status: WalletStatus = WalletStatus.ACTIVE,
        type: WalletType = WalletType.SECONDARY,
        accountPaymentMethodId: AccountPaymentMethodId = paymentMethodId,
        walletId: WalletId? = null,
    ): Wallet {
        val participantAsFounder = founderAccount.toMember(
            type = MemberType.FOUNDER,
            permissions = MemberPermissions(
                viewBills = BillPermission.ALL_BILLS,
                scheduleBills = BillPermission.ALL_BILLS,
                viewBalance = true,
                notification = true,
            ),
        )
        val participantAsCoFounder = coFounderAccount?.toMember(
            type = MemberType.COFOUNDER,
            permissions = MemberPermissions(
                viewBills = BillPermission.ALL_BILLS,
                scheduleBills = BillPermission.ALL_BILLS,
                viewBalance = true,
                notification = true,
            ),
        )
        val founders = participantAsCoFounder?.let { listOf(participantAsFounder, it) } ?: listOf(participantAsFounder)

        return Wallet(
            id = walletId ?: WalletId("WALLET-${UUID.randomUUID()}"),
            name = name,
            members = founders + otherMembers,
            maxOpenInvitations = maxOpenInvitations,
            status = status,
            type = type,
            paymentMethodId = accountPaymentMethodId,
        )
    }

    fun buildBankAccount(wallet: Wallet) = AccountPaymentMethod(
        id = wallet.paymentMethodId,
        status = AccountPaymentMethodStatus.ACTIVE,
        method = InternalBankAccount(
            accountType = AccountType.CHECKING,
            bankNo = 111,
            routingNo = 4,
            accountNo = 123555,
            accountDv = "1",
            document = wallet.founder.document,
            bankAccountMode = BankAccountMode.PHYSICAL,
        ),
        created = getZonedDateTime(),
        accountId = wallet.founder.accountId,
    )

    val pjFounderAccount = ACCOUNT.copy(
        accountId = AccountId(),
        document = CNPJ_2,
        documentType = "CNPJ",
        name = "Empresa LTDA",
        emailAddress = EmailAddress("<EMAIL>"),
    )

    fun buildPJWallet(
        name: String = "Carteira Empresa LTDA",
        status: WalletStatus = WalletStatus.ACTIVE,
        walletId: WalletId? = null,
    ): Wallet {
        val pjMember = pjFounderAccount.toMember(
            type = MemberType.FOUNDER,
            permissions = MemberPermissions(
                viewBills = BillPermission.ALL_BILLS,
                scheduleBills = BillPermission.ALL_BILLS,
                viewBalance = true,
                notification = true,
            ),
        )
        val pfMember = founderAccount.toMember(
            type = MemberType.COFOUNDER,
            permissions = MemberPermissions(
                viewBills = BillPermission.ALL_BILLS,
                scheduleBills = BillPermission.ALL_BILLS,
                viewBalance = true,
                notification = true,
            ),
        )
        return Wallet(
            id = walletId ?: WalletId("WALLET-${UUID.randomUUID()}"),
            name = name,
            members = listOf(pjMember) + pfMember,
            maxOpenInvitations = 10,
            status = status,
            type = WalletType.PRIMARY,
            paymentMethodId = paymentMethodId2,
        )
    }
}

fun configureDailyLimits(
    walletLimitsService: WalletLimitsService,
    accountId: AccountId,
    walletId: WalletId,
    dailyLimit: Long,
    nighttimeLimit: Long,
) {
    withGivenDateTime(getZonedDateTime().minusDays(3)) {
        walletLimitsService.updateLimit(
            accountId = accountId,
            walletId = walletId,
            type = DailyPaymentLimitType.DAILY,
            amount = dailyLimit,
            source = ActionSource.System,
        )
        walletLimitsService.updateLimit(
            accountId = accountId,
            walletId = walletId,
            type = DailyPaymentLimitType.NIGHTTIME,
            amount = nighttimeLimit,
            source = ActionSource.System,
        )
    }
}

fun Account.toMember(type: MemberType, permissions: MemberPermissions, status: MemberStatus? = null) = Member(
    accountId = accountId,
    document = document,
    name = name,
    emailAddress = emailAddress,
    type = type,
    status = status ?: MemberStatus.ACTIVE,
    permissions = permissions,
    created = ZonedDateTime.of(LocalDateTime.parse("2023-10-14 16:51:56", FULL_FORMAT), brazilTimeZone),
    updated = getZonedDateTime(),
)

fun buildCookie(account: Account) = JWTClaimsSet.Builder()
    .withSubject("7fd44993-43dd-4f79-9e27-c2c379fe3767")
    .withIssuer("bill-payment-service")
    .withCognitoUsername(account.document)
    .withCustomAccountId(account.accountId)
    .withCognitoGroups(arrayOf(Role.OWNER.name))
    .withDefaultSetup()
    .toSignedCookie()

fun buildCookie(member: Member) = JWTClaimsSet.Builder()
    .withSubject("7fd44993-43dd-4f79-9e27-c2c379fe3767")
    .withIssuer("bill-payment-service")
    .withCognitoUsername(member.document)
    .withCustomAccountId(member.accountId)
    .withCognitoGroups(arrayOf(Role.OWNER.name))
    .withDefaultSetup()
    .toSignedCookie()

fun <B> MutableHttpRequest<B>.onWallet(wallet: Wallet, account: Account): MutableHttpRequest<B> =
    cookie(buildCookie(account))
        .header("X-API-VERSION", "2")
        .header("X-WALLET-ID", wallet.id.value)

fun <B> MutableHttpRequest<B>.onWallet(wallet: Wallet, member: Member = wallet.founder): MutableHttpRequest<B> =
    cookie(buildCookie(member))
        .header("X-API-VERSION", "2")
        .header("X-WALLET-ID", wallet.id.value)