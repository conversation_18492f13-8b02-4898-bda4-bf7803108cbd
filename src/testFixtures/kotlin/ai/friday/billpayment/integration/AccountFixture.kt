package ai.friday.billpayment.integration

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.CreditCardConfiguration
import ai.friday.billpayment.app.account.LegacyAccountConfiguration
import ai.friday.billpayment.app.subscription.SubscriptionType
import java.time.ZoneId
import java.time.ZonedDateTime

object AccountFixture {
    fun createAccount(
        accountID: String = "*********",
        accountStatus: AccountStatus = AccountStatus.ACTIVE,
        receiveNotification: Boolean = true,
    ) = Account(
        accountId = AccountId(accountID),
        name = "account_name",
        emailAddress = EmailAddress("<EMAIL>"),
        document = "document",
        documentType = "document_type",
        mobilePhone = "*************",
        created = ZonedDateTime.of(2022, 1, 1, 10, 0, 0, 0, ZoneId.of("UTC")),
        updated = ZonedDateTime.of(2022, 1, 1, 10, 0, 0, 0, ZoneId.of("UTC")),
        status = accountStatus,
        configuration = createAccountConfiguration(
            receiveNotification = receiveNotification,
        ),
        imageUrlSmall = "image_url_small",
        imageUrlLarge = "image_url_large",
        subscriptionType = SubscriptionType.PIX,
    )

    fun createAccountConfiguration(
        receiveNotification: Boolean = true,
    ) = LegacyAccountConfiguration(
        creditCardConfiguration = CreditCardConfiguration(
            quota = 10,
        ),
        defaultWalletId = null,
        receiveDDANotification = true,
        receiveNotification = receiveNotification,
        accessToken = null,
        refreshToken = null,
    )
}