package ai.friday.billpayment.integration

import com.amazonaws.auth.AWSStaticCredentialsProvider
import com.amazonaws.auth.BasicAWSCredentials
import com.amazonaws.client.builder.AwsClientBuilder
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.amazonaws.services.dynamodbv2.AmazonDynamoDBClientBuilder
import org.testcontainers.containers.GenericContainer
import org.testcontainers.utility.DockerImageName

class LocalDbCreationRule {

    companion object {
        @JvmStatic
        lateinit var dynamoDB: AmazonDynamoDB

        @JvmStatic
        var dynamoDBPort: Int = 0

        @JvmStatic
        lateinit var dynamoDBHost: String

        @JvmStatic
        fun getDynamoDBProxyServer(): AmazonDynamoDB {
            if (!this::dynamoDB.isInitialized) {
                val (host, port) = startDynamoDbContainer()
                dynamoDBHost = host
                dynamoDBPort = port
                val amazonDynamoDB = AmazonDynamoDBClientBuilder
                    .standard()
                    .withCredentials(AWSStaticCredentialsProvider(BasicAWSCredentials("access", "secret")))
                    .withEndpointConfiguration(
                        AwsClientBuilder.EndpointConfiguration("http://$dynamoDBHost:$dynamoDBPort", "us-east-1"),
                    ).build()
                dynamoDB = amazonDynamoDB
            }
            return dynamoDB
        }

        @JvmStatic
        private fun startDynamoDbContainer(): Pair<String, Int> {
            val image = DockerImageName.parse("amazon/dynamodb-local:1.16.0")
            val container = GenericContainer(image).withExposedPorts(8000)
            container.start()

            return Pair(container.host, container.getMappedPort(8000))
        }
    }
}