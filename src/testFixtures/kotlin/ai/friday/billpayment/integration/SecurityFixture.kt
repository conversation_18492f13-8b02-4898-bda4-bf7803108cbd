package ai.friday.billpayment.integration

import ai.friday.billpayment.adapters.api.MsisdnAuthConfiguration
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.msisdnauth.createTemporaryEmail
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import com.nimbusds.jose.JOSEObjectType
import com.nimbusds.jose.JWSAlgorithm
import com.nimbusds.jose.JWSHeader
import com.nimbusds.jose.crypto.MACSigner
import com.nimbusds.jwt.JWTClaimsSet
import com.nimbusds.jwt.JWTParser
import com.nimbusds.jwt.SignedJWT
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.cookie.Cookie
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.Date

class SecurityFixture {

    private val guest =
        JWTClaimsSet.Builder()
            .withSubject("7fd44993-43dd-4f79-9e27-c2c379fe3767")
            .withIssuer("bill-payment-service")
            .withCognitoGroups(arrayOf("us-east-1_S0DGZEW5J_Google", Role.GUEST.name))
            .withCognitoUsername("Google_106150454355414104863")
            .withCustomAccountId()
            .withDefaultSetup()

    val cookieGuest = guest.toSignedCookie()

    private val msisdnAuth =
        JWTClaimsSet.Builder()
            .withSubject(ACCOUNT_ID_2)
            .withIssuer("https://friday.ai")
            .withAudience("friday.ai")
            .withClaim("email", "+<EMAIL>")

    val cookieMsisdnAuth = msisdnAuth.toSignedCookie()

    val cookieAuthOwner =
        JWTClaimsSet.Builder()
            .withSubject("7fd44993-43dd-4f79-9e27-c2c379fe3767")
            .withIssuer("bill-payment-service")
            .withCognitoGroups()
            .withCognitoUsername()
            .withCustomAccountId()
            .withDefaultSetup()
            .toSignedCookie()

    val cookieAdmin =
        JWTClaimsSet.Builder()
            .withSubject("7fd44993-43dd-4f79-9e27-c2c379fe3767")
            .withIssuer("bill-payment-service")
            .withCognitoGroups(arrayOf("us-east-1_S0DGZEW5J_Google", Role.OWNER.name, Role.ADMIN.name))
            .withCognitoUsername("Google_106150454355414104863")
            .withCustomAccountId()
            .withDefaultSetup()
            .toSignedCookie()

    val cookieBackoffice =
        JWTClaimsSet.Builder()
            .withSubject("7fd44993-43dd-4f79-9e27-c2c379fe3767")
            .withIssuer("bill-payment-service")
            .withCognitoGroups(arrayOf("us-east-1_S0DGZEW5J_Google", Role.OWNER.name, Role.BACKOFFICE.name, Role.BACKOFFICE_ADMIN.name))
            .withCognitoUsername("Google_106150454355414104863")
            .withCustomAccountId()
            .withDefaultSetup()
            .toSignedCookie()

    fun cookieOTPGuest(configuration: MsisdnAuthConfiguration): Cookie =
        JWTClaimsSet.Builder()
            .withSubject(ACCOUNT_ID)
            .withIssuer(configuration.issuer)
            .withAudience(configuration.audience)
            .withClaim("email", createTemporaryEmail(MobilePhone("+*************")).value)
            .withClaim("msisdn:migrated", false)
            .withClaim("actualRole", Role.GUEST.name)
            .withExpiresAt(
                Date.from(
                    getZonedDateTime()
                        .plusMinutes(5).toInstant(),
                ),
            )
            .toSignedCookie(secret = configuration.secret)
}

object CookieFixture {
    private val securityFixture = SecurityFixture()

    fun <T> MutableHttpRequest<T>.withBackofficeCookie(): MutableHttpRequest<T> = cookie(securityFixture.cookieBackoffice)
}

private val algorithm = JWSAlgorithm.HS256
private const val jwtSecret = "PleaseChangeThisSecretForANewOne"

fun JWTClaimsSet.Builder.withSubject(subject: String): JWTClaimsSet.Builder = subject(subject)
fun JWTClaimsSet.Builder.withIssuer(issuer: String): JWTClaimsSet.Builder = issuer(issuer)
fun JWTClaimsSet.Builder.withAudience(audience: String): JWTClaimsSet.Builder = audience(audience)
fun JWTClaimsSet.Builder.withClaim(claimKey: String, claimValue: Any): JWTClaimsSet.Builder = claim(claimKey, claimValue)
fun JWTClaimsSet.Builder.withIssuedAt(issuedAt: Date): JWTClaimsSet.Builder = issueTime(issuedAt)
fun JWTClaimsSet.Builder.withExpiresAt(expiresAt: Date): JWTClaimsSet.Builder = expirationTime(expiresAt)

fun JWTClaimsSet.Builder.withCognitoGroups(groups: Array<String> = arrayOf(Role.OWNER.name)): JWTClaimsSet.Builder =
    claim("cognito:groups", groups)

fun JWTClaimsSet.Builder.withCustomAccountId(accountId: AccountId = AccountId(ACCOUNT_ID)): JWTClaimsSet.Builder =
    claim("custom:accountId", accountId.value)

fun JWTClaimsSet.Builder.withCognitoUsername(username: String = DOCUMENT): JWTClaimsSet.Builder =
    claim("cognito:username", username)

fun JWTClaimsSet.Builder.withDefaultSetup(email: String = EMAIL): JWTClaimsSet.Builder =
    claim("email", email)
        .claim("name", NAME)
        .expirationTime(Date(Instant.now().plus(1, ChronoUnit.DAYS).toEpochMilli()))

fun JWTClaimsSet.Builder.toSignedCookie(secret: String = jwtSecret): Cookie {
    val signedJWT = SignedJWT(
        JWSHeader.Builder(algorithm)
            .type(JOSEObjectType.JWT)
            .build(),
        this.build(),
    )
    signedJWT.sign(MACSigner(secret))
    return Cookie.of("JWT", signedJWT.serialize())
}

fun Cookie.asJWT() = JWTParser.parse(value)

fun generateCookie(
    accountId: AccountId,
    role: Role,
) =
    generateCognitoCookie(accountId = accountId, roles = arrayOf(role.name))

private fun generateCognitoCookie(
    username: String = DOCUMENT,
    accountId: AccountId = AccountId(ACCOUNT_ID),
    roles: Array<String> = arrayOf(Role.OWNER.name),
    email: String = EMAIL,
) = JWTClaimsSet.Builder()
    .withSubject("7fd44993-43dd-4f79-9e27-c2c379fe3767")
    .withIssuer("bill-payment-service")
    .withCognitoGroups(roles)
    .withCognitoUsername(username)
    .withCustomAccountId(accountId)
    .withDefaultSetup(email)
    .toSignedCookie()