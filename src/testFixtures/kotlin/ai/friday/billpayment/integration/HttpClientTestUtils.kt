package ai.friday.billpayment.integration

import io.micronaut.http.client.BlockingHttpClient
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient

fun EmbeddedServer.blockingHttpClient(): BlockingHttpClient =
    applicationContext.createBean(RxHttpClient::class.java, url).toBlocking()

fun EmbeddedServer.rxHttpClient(): RxHttpClient = applicationContext.createBean(RxHttpClient::class.java, url)