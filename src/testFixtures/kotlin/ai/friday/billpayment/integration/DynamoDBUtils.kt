package ai.friday.billpayment.integration

import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.amazonaws.services.dynamodbv2.document.DynamoDB
import com.amazonaws.services.dynamodbv2.document.Item
import com.amazonaws.services.dynamodbv2.document.PutItemOutcome
import com.amazonaws.services.dynamodbv2.model.AttributeDefinition
import com.amazonaws.services.dynamodbv2.model.CreateTableRequest
import com.amazonaws.services.dynamodbv2.model.CreateTableResult
import com.amazonaws.services.dynamodbv2.model.GlobalSecondaryIndex
import com.amazonaws.services.dynamodbv2.model.KeySchemaElement
import com.amazonaws.services.dynamodbv2.model.KeyType
import com.amazonaws.services.dynamodbv2.model.Projection
import com.amazonaws.services.dynamodbv2.model.ProjectionType
import com.amazonaws.services.dynamodbv2.model.ProvisionedThroughput
import com.amazonaws.services.dynamodbv2.model.ResourceNotFoundException
import com.amazonaws.services.dynamodbv2.model.ScalarAttributeType

object DynamoDBUtils {
    fun createBillPaymentTable(
        ddb: AmazonDynamoDB,
        tableName: String,
        hashKeyName: String,
        rangeKeyName: String,
        gsi1HashKeyName: String,
        gsi1RangeKeyName: String,
        gsi2HashKeyName: String,
        gsi2RangeKeyName: String,
        gsi3HashKeyName: String,
        gsi3RangeKeyName: String,
        gsi4HashKeyName: String,
        gsi4RangeKeyName: String,
    ): CreateTableResult {
        val attributeDefinitions =
            listOf(
                AttributeDefinition(hashKeyName, ScalarAttributeType.S),
                AttributeDefinition(rangeKeyName, ScalarAttributeType.S),
                AttributeDefinition(gsi1HashKeyName, ScalarAttributeType.S),
                AttributeDefinition(gsi1RangeKeyName, ScalarAttributeType.S),
                AttributeDefinition(gsi2HashKeyName, ScalarAttributeType.S),
                AttributeDefinition(gsi2RangeKeyName, ScalarAttributeType.S),
                AttributeDefinition(gsi3HashKeyName, ScalarAttributeType.S),
                AttributeDefinition(gsi3RangeKeyName, ScalarAttributeType.S),
                AttributeDefinition(gsi4HashKeyName, ScalarAttributeType.S),
                AttributeDefinition(gsi4RangeKeyName, ScalarAttributeType.S),
            )
        return createBillPaymentTable(
            ddb,
            tableName,
            hashKeyName,
            rangeKeyName,
            gsi1HashKeyName,
            gsi1RangeKeyName,
            gsi2HashKeyName,
            gsi2RangeKeyName,
            gsi3HashKeyName,
            gsi3RangeKeyName,
            gsi4HashKeyName,
            gsi4RangeKeyName,
            attributeDefinitions,
        )
    }

    fun createBillEventTable(
        ddb: AmazonDynamoDB,
        tableName: String,
        hashKeyName: String,
        rangeKeyName: String,
        gsi1HashKeyName: String,
        gsi1RangeKeyName: String,
    ): CreateTableResult {
        val attributeDefinitions =
            listOf(
                AttributeDefinition(hashKeyName, ScalarAttributeType.S),
                AttributeDefinition(rangeKeyName, ScalarAttributeType.N),
                AttributeDefinition(gsi1HashKeyName, ScalarAttributeType.S),
                AttributeDefinition(gsi1RangeKeyName, ScalarAttributeType.S),
            )
        return createBillEventTable(
            ddb,
            tableName,
            hashKeyName,
            rangeKeyName,
            gsi1HashKeyName,
            gsi1RangeKeyName,
            attributeDefinitions,
        )
    }

    private fun createBillEventTable(
        ddb: AmazonDynamoDB,
        tableName: String,
        hashKeyName: String,
        rangeKeyName: String,
        gsi1HashKeyName: String,
        gsi1RangeKeyName: String,
        attributeDefinitions: List<AttributeDefinition>,
    ): CreateTableResult {
        try {
            ddb.deleteTable(tableName)
        } catch (e: ResourceNotFoundException) {
        }
        val ks =
            listOf(
                KeySchemaElement(hashKeyName, KeyType.HASH),
                KeySchemaElement(rangeKeyName, KeyType.RANGE),
            )
        val gsi1 =
            listOf(
                KeySchemaElement(gsi1HashKeyName, KeyType.HASH),
                KeySchemaElement(gsi1RangeKeyName, KeyType.RANGE),
            )
        val globalSecondaryIndexes: MutableList<GlobalSecondaryIndex> = ArrayList()
        globalSecondaryIndexes.add(
            GlobalSecondaryIndex()
                .withKeySchema(gsi1)
                .withIndexName("GSIndex1")
                .withProjection(Projection().withProjectionType(ProjectionType.ALL))
                .withProvisionedThroughput(ProvisionedThroughput(1000L, 1000L)),
        )
        val provisionedThroughput = ProvisionedThroughput(1000L, 1000L)
        val request =
            CreateTableRequest()
                .withTableName(tableName)
                .withAttributeDefinitions(attributeDefinitions)
                .withKeySchema(ks)
                .withGlobalSecondaryIndexes(globalSecondaryIndexes)
                .withProvisionedThroughput(provisionedThroughput)
        val result = ddb.createTable(request)
        while (ddb.describeTable(tableName).table.tableStatus != "ACTIVE") {
            // wait til table is active or otherwise will not find table
        }
        return result
    }

    private fun createBillPaymentTable(
        ddb: AmazonDynamoDB,
        tableName: String,
        hashKeyName: String,
        rangeKeyName: String,
        gsi1HashKeyName: String,
        gsi1RangeKeyName: String,
        gsi2HashKeyName: String,
        gsi2RangeKeyName: String,
        gsi3HashKeyName: String,
        gsi3RangeKeyName: String,
        gsi4HashKeyName: String,
        gsi4RangeKeyName: String,
        attributeDefinitions: List<AttributeDefinition>,
    ): CreateTableResult {
        try {
            ddb.deleteTable(tableName)
        } catch (e: ResourceNotFoundException) {
        }
        val ks =
            listOf(
                KeySchemaElement(hashKeyName, KeyType.HASH),
                KeySchemaElement(rangeKeyName, KeyType.RANGE),
            )
        val gsi1 =
            listOf(
                KeySchemaElement(gsi1HashKeyName, KeyType.HASH),
                KeySchemaElement(gsi1RangeKeyName, KeyType.RANGE),
            )
        val gsi2 =
            listOf(
                KeySchemaElement(gsi2HashKeyName, KeyType.HASH),
                KeySchemaElement(gsi2RangeKeyName, KeyType.RANGE),
            )
        val gsi3 =
            listOf(
                KeySchemaElement(gsi3HashKeyName, KeyType.HASH),
                KeySchemaElement(gsi3RangeKeyName, KeyType.RANGE),
            )
        val gsi4 =
            listOf(
                KeySchemaElement(gsi4HashKeyName, KeyType.HASH),
                KeySchemaElement(gsi4RangeKeyName, KeyType.RANGE),
            )
        val globalSecondaryIndexes: MutableList<GlobalSecondaryIndex> = ArrayList()
        globalSecondaryIndexes.add(
            GlobalSecondaryIndex()
                .withKeySchema(gsi1)
                .withIndexName("GSIndex1")
                .withProjection(Projection().withProjectionType(ProjectionType.ALL))
                .withProvisionedThroughput(ProvisionedThroughput(1000L, 1000L)),
        )
        globalSecondaryIndexes.add(
            GlobalSecondaryIndex()
                .withKeySchema(gsi2)
                .withIndexName("GSIndex2")
                .withProjection(Projection().withProjectionType(ProjectionType.ALL))
                .withProvisionedThroughput(ProvisionedThroughput(1000L, 1000L)),
        )
        globalSecondaryIndexes.add(
            GlobalSecondaryIndex()
                .withKeySchema(gsi3)
                .withIndexName("GSIndex3")
                .withProjection(Projection().withProjectionType(ProjectionType.ALL))
                .withProvisionedThroughput(ProvisionedThroughput(1000L, 1000L)),
        )
        globalSecondaryIndexes.add(
            GlobalSecondaryIndex()
                .withKeySchema(gsi4)
                .withIndexName("GSIndex4")
                .withProjection(Projection().withProjectionType(ProjectionType.ALL))
                .withProvisionedThroughput(ProvisionedThroughput(1000L, 1000L)),
        )
        val provisionedThroughput = ProvisionedThroughput(1000L, 1000L)
        val request =
            CreateTableRequest()
                .withTableName(tableName)
                .withAttributeDefinitions(attributeDefinitions)
                .withKeySchema(ks)
                .withGlobalSecondaryIndexes(globalSecondaryIndexes)
                .withProvisionedThroughput(provisionedThroughput)
        val result = ddb.createTable(request)
        while (ddb.describeTable(tableName).table.tableStatus != "ACTIVE") {
            // wait til table is active or otherwise will not find table
        }
        return result
    }

    fun putItem(
        client: AmazonDynamoDB?,
        tableName: String?,
        item: Item?,
    ): PutItemOutcome {
        val dynamoDB = DynamoDB(client)
        val table = dynamoDB.getTable(tableName)
        return table.putItem(item)
    }

    fun getItem(
        client: AmazonDynamoDB?,
        tableName: String?,
        primaryKey: String?,
        scanKey: String?,
    ): Item {
        val dynamoDB = DynamoDB(client)
        val table = dynamoDB.getTable(tableName)
        return table.getItem(BILL_PAYMENT_PARTITION_KEY, primaryKey, BILL_PAYMENT_RANGE_KEY, scanKey)
    }

    fun removeItem(
        client: AmazonDynamoDB?,
        tableName: String?,
        hashKey: String?,
        rangeKey: String?,
    ) {
        val dynamoDB = DynamoDB(client)
        val table = dynamoDB.getTable(tableName)
        table.deleteItem(BILL_PAYMENT_PARTITION_KEY, hashKey, BILL_PAYMENT_RANGE_KEY, rangeKey)
    }
}