package ai.friday.billpayment.integration

import ai.friday.billpayment.EMAIL_ADDRESS
import ai.friday.billpayment.accountRegisterDataMissingAcceptedAt
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BILL_EVENT_TABLE_NAME
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_TABLE_NAME
import ai.friday.billpayment.adapters.dynamodb.BillCategorySuggestionDocument
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEntity
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.OPEN_FINANCE_DATA_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.OPEN_FINANCE_DATA_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.OPEN_FINANCE_DATA_TABLE_NAME
import ai.friday.billpayment.adapters.dynamodb.OriginalOcrAndPersonDataDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.RecurrenceRuleEntity
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.adapters.dynamodb.toBillRecipientDocument
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.AccountPaymentStatus
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.account.PartialAccount
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.FichaCompensacaoAdded
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.externalCreditCard
import ai.friday.billpayment.integration.DynamoDBUtils.createBillEventTable
import ai.friday.billpayment.integration.DynamoDBUtils.createBillPaymentTable
import ai.friday.billpayment.integration.DynamoDBUtils.putItem
import ai.friday.billpayment.integration.DynamoDBUtils.removeItem
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.invoicePaid
import ai.friday.billpayment.invoicePaymentScheduled
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.pixPaid
import ai.friday.billpayment.pixPaymentScheduled
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.date.timeFormat
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.amazonaws.services.dynamodbv2.document.Item
import com.amazonaws.services.dynamodbv2.model.AttributeDefinition
import com.amazonaws.services.dynamodbv2.model.CreateTableRequest
import com.amazonaws.services.dynamodbv2.model.KeySchemaElement
import com.amazonaws.services.dynamodbv2.model.KeyType
import com.amazonaws.services.dynamodbv2.model.ProvisionedThroughput
import com.amazonaws.services.dynamodbv2.model.ScalarAttributeType
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.mockk.mockk
import java.math.BigInteger
import java.time.Instant
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient

fun loadRecipientWithoutPixKeys(
    amazonDynamoDB: AmazonDynamoDB,
    document: String,
) {
    val recipient =
        Item()
            .withPrimaryKey(
                BILL_PAYMENT_PARTITION_KEY,
                "RECIPIENT-${UUID.randomUUID()}",
                BILL_PAYMENT_RANGE_KEY,
                ACCOUNT_ID,
            )
            .withString("GSIndex1PrimaryKey", ACCOUNT_ID)
            .withString("GSIndex1ScanKey", "RECIPIENT#$document")
            .withString("Document", document)
            .withString("Name", NAME)
            .withString("Alias", "Alias fake")
            .withList("BankAccounts", listOf<Map<String, String>>())
            .withString("Created", LocalDateTime.now().format(dateTimeFormat))
    putItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, recipient)
}

fun createLockTable(
    amazonDynamoDB: AmazonDynamoDB,
    lockTableName: String,
) {
    runCatching { amazonDynamoDB.deleteTable(lockTableName) }

    val attributeDefinitions =
        listOf(
            AttributeDefinition("_id", ScalarAttributeType.S),
        )
    val ks =
        listOf(
            KeySchemaElement("_id", KeyType.HASH),
        )

    val provisionedThroughput = ProvisionedThroughput(1000L, 1000L)
    val request =
        CreateTableRequest()
            .withTableName(lockTableName)
            .withAttributeDefinitions(attributeDefinitions)
            .withKeySchema(ks)
            .withProvisionedThroughput(provisionedThroughput)

    amazonDynamoDB.createTable(request)
    while (amazonDynamoDB.describeTable(lockTableName).table.tableStatus != "ACTIVE") {
        // wait til table is active or otherwise will not find table
    }
}

fun loadLoginIntoDb(
    amazonDynamoDB: AmazonDynamoDB,
    partitionKey: String = "106150454355414104863",
    provider: ProviderName = ProviderName.MSISDN,
    accountId: AccountId = AccountId(ACCOUNT_ID),
) {
    val owner =
        Item()
            .withPrimaryKey(BILL_PAYMENT_PARTITION_KEY, partitionKey, BILL_PAYMENT_RANGE_KEY, "LOGIN#${provider.name}")
            .withString("GSIndex1PrimaryKey", EMAIL)
            .withString("GSIndex1ScanKey", accountId.value)
            .withString("Role", Role.OWNER.name)
            .withString("Created", "2019-09-18T16:51:56Z")

    putItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, owner)
}

fun loadAccountIntoDb(
    amazonDynamoDB: AmazonDynamoDB,
    accountId: AccountId = AccountId(ACCOUNT_ID),
    position: Int = 0,
    hasCreditCard: Boolean = true,
    name: String = NAME,
    email: String = EMAIL,
    personalizedQuota: Long = 20_000_00,
    billCount: Int? = 0,
    nsu: Int? = null,
    status: String = "ACTIVE",
    upgradeStatus: String = "COMPLETED",
    document: String = DOCUMENT,
    defaultWalletId: WalletId? = null,
    mobilePhone: String = "*************",
    groups: List<String>? = null,
    creditCardPaymentMethodId: String = PAYMENT_METHOD_ID,
    hasExternalCreditCard: Boolean = false,
    type: UserAccountType = UserAccountType.FULL_ACCOUNT,
    externalId: ExternalId? = null,
    subscriptionType: SubscriptionType = SubscriptionType.PIX,
    paymentStatus: AccountPaymentStatus = AccountPaymentStatus.UpToDate,
) {
    val configuration =
        mutableMapOf<String, Any?>(
            "creditCardQuota" to personalizedQuota,
            "defaultWalletId" to defaultWalletId?.value,
            "accessToken" to "MOCK_ACCESS_TOKEN",
        )

    if (groups != null) {
        configuration["groups"] = groups
    }

    val owner =
        Item()
            .withPrimaryKey(BILL_PAYMENT_PARTITION_KEY, accountId.value, BILL_PAYMENT_RANGE_KEY, "OWNER")
            .withString("GSIndex1PrimaryKey", document)
            .withString("GSIndex1ScanKey", status)
            .withString("GSIndex2PrimaryKey", "ACCOUNT")
            .withString("UpgradeStatus", upgradeStatus)
            .withString("Document", document)
            .withString("DocumentType", "CPF")
            .withString("MobilePhone", mobilePhone)
            .withString("Email", email)
            .withString("Name", name)
            .withString("UserAccountType", type.name)
            .withMap("Configuration", configuration)
            .withString("Created", "2019-09-18T16:51:56Z")
            .withString("GSIndex3PrimaryKey", "EXTERNAL_ID#${externalId?.providerName?.name}")
            .withString("GSIndex3ScanKey", externalId?.value ?: "MOCK_EXTERNAL_ID")
            .withString("SubscriptionType", subscriptionType.name)
            .withString("AccountPaymentStatus", paymentStatus.name)

    billCount?.let {
        owner.withInt("BillCount", it)
    }
    putItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, owner)

    nsu?.let {
        val nsuEntity =
            Item()
                .withPrimaryKey(BILL_PAYMENT_PARTITION_KEY, accountId.value, BILL_PAYMENT_RANGE_KEY, "NSU")
                .withInt("NSU", it)

        putItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, nsuEntity)
    }

    if (hasCreditCard) {
        Item()
            .withPrimaryKey(
                BILL_PAYMENT_PARTITION_KEY,
                accountId.value,
                BILL_PAYMENT_RANGE_KEY,
                "PAYMENT-METHOD#ACTIVE#$position",
            )
            .withString("CreditCardBrand", "Visa")
            .withString("CreditCardPAN", "****************")
            .withString("CreditCardExpiryDate", "10/2020")
            .withString("PaymentMethodId", creditCardPaymentMethodId)
            .withString("Type", PaymentMethodType.CREDIT_CARD.name)
            .withString("Status", "ACTIVE")
            .withString("Created", "2019-09-18").also {
                putItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, it)
            }
    }

    if (hasExternalCreditCard) {
        val externalCard = externalCreditCard.method as CreditCard

        Item()
            .withPrimaryKey(
                BILL_PAYMENT_PARTITION_KEY,
                accountId.value,
                BILL_PAYMENT_RANGE_KEY,
                "PAYMENT-METHOD#ACTIVE#${position + 2}",
            )
            .withString("CreditCardExternalId", externalCard.externalId?.value)
            .withString("CreditCardExternalProvider", externalCard.externalId?.provider?.name)
            .withBoolean("CreditCardMainCard", externalCard.mainCard ?: false)
            .withString("CreditCardLastFourDigits", externalCard.lastFourDigits)
            .withString("CreditCardBrand", externalCard.brand.name)
            .withString("CreditCardPAN", externalCard.bin + "*".repeat(4) + externalCard.lastFourDigits)
            .withString("CreditCardExpiryDate", externalCard.expiryDate)
            .withString("PaymentMethodId", PAYMENT_METHOD_EXTERNAL_ID)
            .withString("Type", PaymentMethodType.CREDIT_CARD.name)
            .withString("Status", "ACTIVE")
            .withString("Created", "2019-09-18").also {
                putItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, it)
            }
    }
}

fun loadPartialAccountIntoDb(
    amazonDynamoDB: AmazonDynamoDB,
    email: String = EMAIL,
    name: String = NAME,
    status: AccountStatus = AccountStatus.REGISTER_INCOMPLETE,
    accountId: AccountId = AccountId(ACCOUNT_ID),
) {
    val owner =
        Item()
            .withPrimaryKey(BILL_PAYMENT_PARTITION_KEY, accountId.value, BILL_PAYMENT_RANGE_KEY, Role.GUEST.name)
            .withString("Status", status.name)
            .withString("Email", email)
            .withString("Name", name)
    putItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, owner)
}

fun loadBalancePaymentMethod(
    accountRepository: AccountDbRepository,
    bankNo: Long = 341,
    bankRoutingNo: Long = 1,
    bankAccountNo: BigInteger = BigInteger("1"),
    bankAccountDv: String = "1",
    document: String = "***********",
    paymentMethodId: String = PAYMENT_METHOD_ID_2,
    accountId: String = ACCOUNT_ID,
    bankIspb: String = "********",
    mode: BankAccountMode = BankAccountMode.PHYSICAL,
    paymentMethodStatus: AccountPaymentMethodStatus = AccountPaymentMethodStatus.ACTIVE,
    position: Int = 1,
): AccountPaymentMethod {
    return accountRepository.createAccountPaymentMethod(
        accountId = AccountId(accountId),
        bankAccount =
        BankAccount(
            accountType = AccountType.CHECKING,
            bankNo = bankNo,
            routingNo = bankRoutingNo,
            accountNo = bankAccountNo,
            accountDv = bankAccountDv,
            document = document,
            ispb = bankIspb,
        ),
        position = position,
        paymentMethodId = AccountPaymentMethodId(paymentMethodId),
        status = paymentMethodStatus,
        mode = mode,
    )
}

fun loadVirtualBalance(
    amazonDynamoDB: AmazonDynamoDB,
    bankNo: Long = 342,
    bankRoutingNo: Long = 3,
    bankAccountNo: Long = 2,
    bankAccountDv: String = "0",
    document: String = "***********",
    paymentMethodId: String = PAYMENT_METHOD_ID_4,
    accountId: String = ACCOUNT_ID,
    bankIspb: String = "********",
) {
    val bankAccount =
        Item()
            .withPrimaryKey(BILL_PAYMENT_PARTITION_KEY, accountId, BILL_PAYMENT_RANGE_KEY, "PAYMENT-METHOD#ACTIVE#4")
            .withString("BankAccountType", AccountType.CHECKING.name)
            .withLong("BankNo", bankNo)
            .withLong("BankRoutingNo", bankRoutingNo)
            .withLong("BankAccountNo", bankAccountNo)
            .withString("BankAccountDv", bankAccountDv)
            .withString("Document", document)
            .withString("GSIndex1PrimaryKey", PaymentMethodType.BALANCE.name)
            .withString("GSIndex1ScanKey", paymentMethodId)
            .withString("PaymentMethodId", paymentMethodId)
            .withString("Type", PaymentMethodType.BALANCE.name)
            .withString("Status", "ACTIVE")
            .withString("Created", "2021-01-18T16:51:56Z")
            .withString("BankISPB", bankIspb)
            .withString("BankAccountMode", BankAccountMode.VIRTUAL.name)
    putItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, bankAccount)
}

fun loadBlockedPaymentMethods(amazonDynamoDB: AmazonDynamoDB) {
    val creditCard =
        Item()
            .withPrimaryKey(
                BILL_PAYMENT_PARTITION_KEY,
                ACCOUNT_ID,
                BILL_PAYMENT_RANGE_KEY,
                "PAYMENT-METHOD#${AccountPaymentMethodStatus.BLOCKED.name}#1",
            )
            .withString("CreditCardBrand", "Master")
            .withString("CreditCardPAN", "****************")
            .withString("CreditCardExpiryDate", "10/2021")
            .withString("PaymentMethodId", PAYMENT_METHOD_ID_2)
            .withString("Status", AccountPaymentMethodStatus.BLOCKED.name)
            .withString("Type", PaymentMethodType.CREDIT_CARD.name)
            .withString("Created", "2012-09-18T16:51:56Z")
    putItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, creditCard)
    val creditCard2 =
        Item()
            .withPrimaryKey(
                BILL_PAYMENT_PARTITION_KEY,
                ACCOUNT_ID,
                BILL_PAYMENT_RANGE_KEY,
                "PAYMENT-METHOD#${AccountPaymentMethodStatus.BLOCKED.name}#2",
            )
            .withString("CreditCardBrand", "Master")
            .withString("CreditCardPAN", "****************")
            .withString("CreditCardExpiryDate", "10/2022")
            .withString("PaymentMethodId", PAYMENT_METHOD_ID_3)
            .withString("Status", AccountPaymentMethodStatus.BLOCKED.name)
            .withString("Type", PaymentMethodType.CREDIT_CARD.name)
            .withString("Created", "2012-09-18T16:51:56Z")
    putItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, creditCard2)
}

fun loadCreditCard(
    amazonDynamoDB: AmazonDynamoDB,
    paymentMethodStatus: AccountPaymentMethodStatus = AccountPaymentMethodStatus.ACTIVE,
    position: Int = 3,
    creditCardPan: String = "****************",
    accountId: AccountId = AccountId(ACCOUNT_ID),
    paymentMethodId: String = PAYMENT_METHOD_ID_4,
) {
    val creditCard =
        Item()
            .withPrimaryKey(
                BILL_PAYMENT_PARTITION_KEY,
                accountId.value,
                BILL_PAYMENT_RANGE_KEY,
                "PAYMENT-METHOD#${paymentMethodStatus.name}#$position",
            )
            .withString("CreditCardBrand", "Master")
            .withString("CreditCardPAN", creditCardPan)
            .withString("CreditCardExpiryDate", "10/2021")
            .withString("PaymentMethodId", paymentMethodId)
            .withString("Status", paymentMethodStatus.name)
            .withString("Type", PaymentMethodType.CREDIT_CARD.name)
            .withString("Created", "2012-09-18")
    putItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, creditCard)
}

fun loadLoginsIntoDb(
    amazonDynamoDB: AmazonDynamoDB,
    provider: ProviderName = ProviderName.MSISDN,
) {
    val loginOwner =
        Item()
            .withPrimaryKey(BILL_PAYMENT_PARTITION_KEY, SOCIAL_LOGIN_ID, BILL_PAYMENT_RANGE_KEY, "LOGIN#${provider.name}")
            .withString("GSIndex1PrimaryKey", EMAIL)
            .withString("GSIndex1ScanKey", ACCOUNT_ID)
            .withString("Role", Role.OWNER.name)
    putItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, loginOwner)
}

fun addBillEventIntoDb(
    amazonDynamoDB: AmazonDynamoDB,
    billEvent: BillAdded,
) {
    val eventItem =
        Item()
            .withPrimaryKey(
                BILL_PAYMENT_PARTITION_KEY,
                billEvent.billId.value,
                BILL_PAYMENT_RANGE_KEY,
                Instant.now().toEpochMilli(),
            )
            .withString("EventType", billEvent.eventType.name)
            .withString("EventDetails", getObjectMapper().writeValueAsString(billEvent.toBillEventDetailEntity()))

    if (billEvent.billType.isBoleto()) {
        eventItem.withString("GSIndex1PrimaryKey", billEvent.barcode?.digitable)
            .withString("GSIndex1ScanKey", billEvent.walletId.value)
    }

    putItem(amazonDynamoDB, BILL_EVENT_TABLE_NAME, eventItem)
}

fun addBillEventIntoDb(
    amazonDynamoDB: AmazonDynamoDB,
    billEvent: BillEvent,
) {
    val eventItem =
        Item()
            .withPrimaryKey(
                BILL_PAYMENT_PARTITION_KEY,
                billEvent.billId.value,
                BILL_PAYMENT_RANGE_KEY,
                Instant.now().toEpochMilli(),
            )
            .withString("EventType", billEvent.eventType.name)
            .withString("EventDetails", getObjectMapper().writeValueAsString(billEvent.toBillEventDetailEntity()))

    if (billEvent is FichaCompensacaoAdded) {
        eventItem.withString("GSIndex1PrimaryKey", billEvent.barcode.digitable)
            .withString("GSIndex1ScanKey", billEvent.walletId.value)
    }
    putItem(amazonDynamoDB, BILL_EVENT_TABLE_NAME, eventItem)
}

fun addBillIntoDb(
    enhancedClient: DynamoDbEnhancedClient,
    bill: BillView,
) {
    val source = jacksonObjectMapper().writeValueAsString(bill.source)
    val entity =
        BillEntity().apply {
            partitionKey = bill.billId.value
            sortKey = bill.walletId.value
            gSIndex1PartitionKey = bill.walletId.value
            gSIndex1SortKey = "BILL#" + bill.effectiveDueDate + "#" + bill.status.name
            amount = bill.amount
            discount = bill.discount
            interest = bill.interest
            fine = bill.fine
            amountTotal = bill.amountTotal
            paymentLimitTime = bill.paymentLimitTime.format(timeFormat)
            assignor = bill.assignor.orEmpty()
            digitableBarCode = bill.barCode?.digitable
            created = bill.createdOn.format(dateTimeFormat)
            description = bill.billDescription
            status = bill.status.name
            billComingDueMessageSent = bill.isBillComingDueMessageSent
            paidDate = bill.paidDate?.format(dateTimeFormat)
            amountPaid = bill.amountPaid
            dueDate = bill.dueDate.format(dateFormat)
            effectiveDueDate = bill.effectiveDueDate.format(dateFormat)
            type = bill.billType
            notification = bill.notification
            billRecipient = toBillRecipientDocument(bill.recipient)
            lastSettleDate = bill.lastSettleDate?.format(dateFormat)
            expirationDate = bill.expirationDate?.format(dateFormat)
            payerDocument = bill.payerDocument
            payerName = bill.payerName
            waitingFunds = bill.schedule?.waitingFunds
            waitingRetry = bill.schedule?.waitingRetry
            warningCode = bill.warningCode.code
            scheduledDate = bill.schedule?.date?.format(dateFormat)
            payerAlias = bill.payerAlias
            amountCalculationModel = bill.amountCalculationModel
            recurrenceRule =
                bill.recurrenceRule?.let {
                    RecurrenceRuleEntity().apply {
                        frequency = it.frequency
                        startDate = it.startDate.format(dateFormat)
                        endDate = it.endDate?.format(dateFormat)
                        pattern = it.pattern
                    }
                }
            externalId = bill.externalId?.value
            externalProvider = bill.externalId?.provider?.name
            fichaCompensacaoType = bill.fichaCompensacaoType
            this.subscriptionFee = bill.subscriptionFee
            this.source = source
            this.transactionCorrelationId = bill.transactionCorrelationId
            this.batchSchedulingId = bill.schedule?.batchSchedulingId?.value
            this.tags = bill.tags.ifEmpty { null }
            this.categoryId = bill.categoryId?.value
            this.categorySuggestions =
                bill.categorySuggestions.map {
                    BillCategorySuggestionDocument().apply {
                        categoryId = it.categoryId.value
                        probability = it.probability.toLong()
                    }
                }
        }
    BillDynamoDAO(enhancedClient).save(entity)
}

fun createBillPaymentTable(amazonDynamoDB: AmazonDynamoDB?) {
    createBillPaymentTable(
        amazonDynamoDB!!,
        BILL_PAYMENT_TABLE_NAME,
        BILL_PAYMENT_PARTITION_KEY,
        BILL_PAYMENT_RANGE_KEY,
        "GSIndex1PrimaryKey",
        "GSIndex1ScanKey",
        "GSIndex2PrimaryKey",
        "GSIndex2ScanKey",
        "GSIndex3PrimaryKey",
        "GSIndex3ScanKey",
        "GSIndex4PrimaryKey",
        "GSIndex4ScanKey",
    )
}

fun createBillEventTable(amazonDynamoDB: AmazonDynamoDB?) {
    createBillEventTable(
        amazonDynamoDB!!,
        BILL_EVENT_TABLE_NAME,
        BILL_PAYMENT_PARTITION_KEY,
        BILL_PAYMENT_RANGE_KEY,
        "GSIndex1PrimaryKey",
        "GSIndex1ScanKey",
    )
}

fun createOpenFinanceDataTable(amazonDynamoDB: AmazonDynamoDB?) {
    createBillPaymentTable(
        amazonDynamoDB!!,
        OPEN_FINANCE_DATA_TABLE_NAME,
        OPEN_FINANCE_DATA_PARTITION_KEY,
        OPEN_FINANCE_DATA_RANGE_KEY,
        "GSIndex1PartitionKey",
        "GSIndex1RangeKey",
        "GSIndex2PartitionKey",
        "GSIndex2RangeKey",
        "GSIndex3PartitionKey",
        "GSIndex3RangeKey",
        "GSIndex4PartitionKey",
        "GSIndex4RangeKey",
    )
}

fun loadPaidBillsWithInterestIntoDb(
    amazonDynamoDB: AmazonDynamoDB?,
    interest: Int,
) {
    loadBillsIntoDb(amazonDynamoDB, BillStatus.PAID, interest)
}

fun loadPaidBillsIntoDb(amazonDynamoDB: AmazonDynamoDB?) {
    loadPaidBillsWithInterestIntoDb(amazonDynamoDB, 0)
}

fun loadBillsIntoDb(
    amazonDynamoDB: AmazonDynamoDB?,
    status: BillStatus,
    interest: Int,
) {
    val billItem =
        Item()
            .withPrimaryKey(BILL_PAYMENT_PARTITION_KEY, BILL_ID, BILL_PAYMENT_RANGE_KEY, ACCOUNT_ID)
            .withString("GSIndex1PrimaryKey", ACCOUNT_ID)
            .withString("GSIndex1ScanKey", "BILL#2019-12-12#" + status.name)
            .withString("Assignor", "CLARO NET")
            .withString("Recipient", "Claro")
            .withString("Description", "CONTA")
            .withString("DigitableBarcode", CONCESSIONARIA_DIGITABLE_LINE)
            .withString("DueDate", "2019-12-12")
            .withString("PaymentLimitTime", "00:00")
            .withInt("Amount", 5000)
            .withInt("Interest", interest)
            .withString("Type", "FICHA_COMPENSACAO")
            .withString("Status", status.name)
            .withString("Created", "2019-09-18 16:51:56")
            .withString("Source", """{"@type":"Webapp","role":"OWNER"}""")
    putItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, billItem)
    val billItem2 =
        Item.fromMap(billItem.asMap())
            .withPrimaryKey(BILL_PAYMENT_PARTITION_KEY, BILL_ID_2, BILL_PAYMENT_RANGE_KEY, ACCOUNT_ID)
            .withString("DigitableBarcode", FICHA_DE_COMPENSACAO_DIGITABLE_LINE)
            .withString("DueDate", "2019-12-11")
            .withString("Status", status.name)
    putItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, billItem2)
}

fun loadUserIntoDb(amazonDynamoDB: AmazonDynamoDB?) {
    val createdAt = getZonedDateTime().withYear(2019).withMonth(9).withDayOfMonth(18)
    val userItem =
        Item()
            .withPrimaryKey(BILL_PAYMENT_PARTITION_KEY, ACCOUNT_ID, BILL_PAYMENT_RANGE_KEY, "OWNER")
            .withString("GSIndex1PrimaryKey", DOCUMENT)
            .withString("GSIndex1ScanKey", "ACTIVE")
            .withString("GSIndex2PrimaryKey", "ACCOUNT")
            .withString("GSIndex2ScanKey", createdAt.toLocalDate().format(dateFormat))
            .withString("Document", DOCUMENT)
            .withString("DocumentType", "CPF")
            .withString("MobilePhone", "*************")
            .withString("Email", "<EMAIL>")
            .withString("Name", "Fulano Beltrano")
            .withMap("Configuration", mutableMapOf("ReceiveNotification" to true))
            .withInt("BillCount", 0)
            .withString("Created", createdAt.format(DateTimeFormatter.ISO_DATE_TIME))
    putItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, userItem)
    val creditCard =
        Item()
            .withPrimaryKey(BILL_PAYMENT_PARTITION_KEY, ACCOUNT_ID, BILL_PAYMENT_RANGE_KEY, "PAYMENT-METHOD#ACTIVE#0")
            .withString("CreditCardBrand", "Visa")
            .withString("CreditCardPAN", "****************")
            .withString("CreditCardExpiryDate", "10/2020")
            .withString("PaymentMethodId", PAYMENT_METHOD_ID)
            .withString("Type", PaymentMethodType.CREDIT_CARD.name)
            .withString("Status", "ACTIVE")
            .withString("Created", "2019-09-18 16:51:56")
    putItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, creditCard)
    val userItem2 =
        Item()
            .withPrimaryKey(BILL_PAYMENT_PARTITION_KEY, ACCOUNT_ID_2, BILL_PAYMENT_RANGE_KEY, "USER")
            .withString("GSIndex1PrimaryKey", DOCUMENT_2)
            .withString("GSIndex1ScanKey", "ACTIVE")
            .withString("Document", DOCUMENT_2)
            .withString("DocumentType", "CPF")
            .withString("MobilePhone", "*************")
            .withString("Email", "<EMAIL>")
            .withString("Name", "Fulano Beltrano")
            .withInt("BillCount", 0)
            .withString("Created", createdAt.format(DateTimeFormatter.ISO_DATE_TIME))
    putItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, userItem2)
    val creditCard2 =
        Item()
            .withPrimaryKey(BILL_PAYMENT_PARTITION_KEY, ACCOUNT_ID_2, BILL_PAYMENT_RANGE_KEY, "PAYMENT-METHOD#ACTIVE#0")
            .withString("CreditCardBrand", "Visa")
            .withString("CreditCardPAN", "****************")
            .withString("CreditCardExpiryDate", "10/2020")
            .withString("PaymentMethodId", "10ef7a9f-9d55-4631-97c9-b9c735bb936d")
            .withString("Type", PaymentMethodType.CREDIT_CARD.name)
            .withString("Status", "ACTIVE")
            .withString("Created", "2019-09-18 16:51:56")
    putItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, creditCard2)
}

fun cleanupDb(amazonDynamoDB: AmazonDynamoDB) {
    removeItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, BILL_ID, ACCOUNT_ID)
    removeItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, BILL_ID_2, ACCOUNT_ID_2)
    removeItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, BILL_ID_2, ACCOUNT_ID)
    removeItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, BILL_ID_3, ACCOUNT_ID)
    removeItem(amazonDynamoDB, BILL_PAYMENT_TABLE_NAME, BILL_ID_4, ACCOUNT_ID)
}

fun createPartialAccount(
    dynamoDbDAO: DynamoDbDAO,
    client: DynamoDbEnhancedClient,
    accountStatus: AccountStatus,
    accountRegisterData: AccountRegisterData = accountRegisterDataMissingAcceptedAt,
): Pair<PartialAccount, AccountRegisterData> {
    // Create the new DAOs for AccountDbRepository
    val accountDAO = ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO(client)
    val partialAccountDAO = ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO(client)
    val paymentMethodDAO = ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO(client)
    val nsuDAO = ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO(client)
    val transactionDynamo = ai.friday.billpayment.adapters.dynamodb.TransactionDynamo(client)

    val accountRepository = AccountDbRepository(accountDAO, partialAccountDAO, paymentMethodDAO, nsuDAO, transactionDynamo)
    val partialAccount =
        accountRepository.create(
            accountRegisterData.documentInfo?.name ?: "FAKE NAME",
            EMAIL_ADDRESS,
            registrationType = accountRegisterData.registrationType,
        )
    accountRepository.updatePartialAccountStatus(partialAccount.id, accountStatus)
    val updatedAccountRegisterData = accountRegisterData.copy(accountId = partialAccount.id)

    // Create the new DAOs for AccountRegisterDbRepository
    val accountRegisterDAO = AccountRegisterDynamoDAO(client)
    val originalOcrAndPersonDataDAO = OriginalOcrAndPersonDataDynamoDAO(client)
    val objectRepository = mockk<ObjectRepository>()

    AccountRegisterDbRepository(accountRegisterDAO, originalOcrAndPersonDataDAO, objectRepository).save(updatedAccountRegisterData)
    return Pair(partialAccount, updatedAccountRegisterData)
}

fun createPaidPix(
    client: DynamoDbEnhancedClient,
    walletId: WalletId,
    amount: Long,
) {
    val billDynamoDAO = BillDynamoDAO(client)
    val refundedBillDynamoDAO = RefundedBillDynamoDAO(client)
    val settlementFundsTransferDynamoDAO = SettlementFundsTransferDynamoDAO(client)
    val billRepository = DynamoDbBillRepository(billDynamoDAO, refundedBillDynamoDAO, settlementFundsTransferDynamoDAO)
    val billId = BillId("BILL-${UUID.randomUUID()}")
    billRepository.save(
        Bill.build(
            pixAdded.copy(
                walletId = walletId,
                billId = billId,
                amount = amount,
                amountTotal = amount,
                created = getZonedDateTime().toInstant().toEpochMilli() - 20,
            ),
            pixPaymentScheduled.copy(
                walletId = walletId,
                billId = billId,
                created = getZonedDateTime().toInstant().toEpochMilli() - 10,
            ),
            pixPaid.copy(
                walletId = walletId,
                billId = billId,
                created = getZonedDateTime().toInstant().toEpochMilli() - 5,
            ),
        ),
    )
}

fun createPaidInvoice(
    client: DynamoDbEnhancedClient,
    walletId: WalletId,
    amount: Long,
) {
    val billDynamoDAO = BillDynamoDAO(client)
    val refundedBillDynamoDAO = RefundedBillDynamoDAO(client)
    val settlementFundsTransferDynamoDAO = SettlementFundsTransferDynamoDAO(client)
    val billRepository = DynamoDbBillRepository(billDynamoDAO, refundedBillDynamoDAO, settlementFundsTransferDynamoDAO)
    val billId = BillId("BILL-${UUID.randomUUID()}")
    billRepository.save(
        Bill.build(
            invoiceAdded.copy(
                walletId = walletId,
                billId = billId,
                amount = amount,
                amountTotal = amount,
                created = getZonedDateTime().toInstant().toEpochMilli() - 10,
            ),
            invoicePaymentScheduled.copy(
                walletId = walletId,
                billId = billId,
                created = getZonedDateTime().toInstant().toEpochMilli() - 5,
            ),
            invoicePaid.copy(
                walletId = walletId,
                billId = billId,
                created = getZonedDateTime().toInstant().toEpochMilli() - 2,
            ),
        ),
    )
}