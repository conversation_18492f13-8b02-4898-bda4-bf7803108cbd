package ai.friday.billpayment.app.notification

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.AccountFixture.createAccount
import ai.friday.billpayment.integration.WALLET_ID

fun createEmailNotification(): EmailNotification {
    return EmailNotification(
        receiver = EmailAddress("<EMAIL>"),
        accountId = AccountId(ACCOUNT_ID),
        template = NotificationTemplate("email-template"),
        parameters = emptyMap(),
    )
}

fun createWhatsappNotification(): WhatsappNotification {
    return WhatsappNotification(
        receiver = MobilePhone("***********"),
        accountId = AccountId(ACCOUNT_ID),
        template = NotificationTemplate("whatsapp-template"),
        configurationKey = "whatsapp-config",
        parameters = listOf("primeiro", "segundo"),
    )
}

fun createMultiChannelNotification(
    preferredChannel: BillPaymentNotificationChannel = BillPaymentNotificationChannel.CHATBOT,
): MultiChannelNotification {
    return MultiChannelNotification(
        preferredChannel = preferredChannel,
        walletId = WalletId(WALLET_ID),
        receiver = createAccount(),
        notificiationType = NotificationType.BILL_CREATED,
        configurationName = MultiChannelConfigurationName("bill-created"),
        parameters = emptyMap(),
        actionButton = null,
    )
}