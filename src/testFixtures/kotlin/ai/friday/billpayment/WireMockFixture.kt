package ai.friday.billpayment

import com.github.tomakehurst.wiremock.WireMockServer
import com.github.tomakehurst.wiremock.client.ResponseDefinitionBuilder
import com.github.tomakehurst.wiremock.client.WireMock
import com.github.tomakehurst.wiremock.core.WireMockConfiguration
import io.micronaut.http.MediaType
import io.micronaut.rxjava2.http.client.RxHttpClient
import java.net.URL

object Wm {
    fun mockServer() = WireMockServer(WireMockConfiguration.wireMockConfig().dynamicPort()).also { it.start() }

    fun aJsonResponse(body: String? = null, status: Int = 200): ResponseDefinitionBuilder = WireMock.aResponse().json().withBody(body).withStatus(status)

    fun ResponseDefinitionBuilder.json(): ResponseDefinitionBuilder = withHeader("Content-Type", MediaType.APPLICATION_JSON)
}

fun WireMockServer.rxClient(): RxHttpClient = RxHttpClient.create(URL(baseUrl()))