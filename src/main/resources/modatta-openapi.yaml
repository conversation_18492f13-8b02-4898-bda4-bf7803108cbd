openapi: 3.0.1
info:
  title: Modatta API
  version: "1"
servers:
  - url: https://api-modatta.meupagador.com.br
    description: Sandbox server (uses test data)
  - url: https://api-modatta.friday.ai
    description: Production server (uses live data)
paths:
  /baas/{accountNo}/balance:
    get:
      tags:
        - Baas
      description: consulta de saldo de uma conta corrente
      parameters:
        - name: accountNo
          in: path
          description: Account number
          required: true
          schema:
            type: number
      responses:
        200:
          description: saldo de uma conta corrente
          content:
            'json/application':
              schema:
                type: object
                properties:
                  amount:
                    type: number
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          description: Conta corrente não encontrada
        500:
          description: Internal Server Error
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/error'
              examples:
                ServerError:
                  value:
                    code: 5001
                    message:
                      Internal Server Error
  /baas/transfer:
    post:
      tags:
        - Baas
      description: consulta de saldo de uma conta corrente
      requestBody:
        description: Dados da transferência
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/modatta-transfer-request'
        required: true
      responses:
        200:
          description: saldo de uma conta corrente
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/modatta-transfer-response'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          description: Conta corrente não encontrada
        500:
          description: Internal Server Error
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/error'
              examples:
                ServerError:
                  value:
                    code: 5001
                    message:
                      Internal Server Error
  /baas/pix:
    post:
      tags:
        - Baas
      description: envio de pix
      requestBody:
        description: Dados do Pix
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/modatta-pix-request'
        required: true
      responses:
        200:
          description: resposta da requisicao
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/modatta-pix-response'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          description: Conta corrente não encontrada
        500:
          description: Internal Server Error
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/error'
              examples:
                ServerError:
                  value:
                    code: 5001
                    message:
                      Internal Server Error
  /baas/pix/{operationId}:
    get:
      tags:
        - Baas
      description: consulta de status de um pix
      parameters:
        - name: operationId
          in: path
          description: Operation ID used to identify the pix
          required: true
          schema:
            type: string
      responses:
        200:
          description: status de um pix
          content:
            'text/plain':
              schema:
                type: string
                enum:
                  - ACKNOWLEDGED
                  - SUCCESS
                  - REFUSED
                  - SCHEDULED
                  - FAILED
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          description: Internal Server Error
  /simpleSignUp/{userId}/status:
    get:
      tags:
        - SignUp
      description: consulta de estado do cadastro
      responses:
        200:
          description: estado do cadastro
          content:
            'json/application':
              schema:
                type: object
                properties:
                  status:
                    type: string
                description: an error
              examples:
                MISSING_USER_DATA:
                  value:
                    status: MISSING_USER_DATA
                MISSING_ENROLLMENT:
                  value:
                    status: MISSING_ENROLLMENT
                INVALID_PHONE_AND_DOCUMENT:
                  value:
                    status: INVALID_PHONE_AND_DOCUMENT
                MISSING_AGREEMENT:
                  value:
                    status: MISSING_AGREEMENT
                UNDER_REVIEW:
                  value:
                    status: UNDER_REVIEW
                CLOSED:
                  value:
                    status: CLOSED
                DENIED:
                  value:
                    status: DENIED
                ACTIVE:
                  value:
                    status: ACTIVE
                REOPENED:
                  value:
                    status: REOPENED
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          description: Internal Server Error
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/error'
              examples:
                TransferServerError:
                  value:
                    code: 5001
                    message:
                      Internal Server Error
  /simpleSignUp/userData:
    post:
      tags:
        - SignUp
      requestBody:
        description: Enviar dados do usuário
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/user-data-request'
        required: true
      responses:
        200:
          description: estado do cadastro
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/simple-signup-response'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          description: Internal Server Error
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/error'
              examples:
                TransferServerError:
                  value:
                    code: 5001
                    message:
                      Internal Server Error
  /simpleSignUp/agreement:
    post:
      tags:
        - SignUp
      description: Confirma que o usuário aceitou os termos de uso
      responses:
        200:
          description: estado do cadastro
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/simple-signup-response'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          description: Internal Server Error
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/error'
              examples:
                TransferServerError:
                  value:
                    code: 5001
                    message:
                      Internal Server Error
components:
  schemas:
    user-data-request:
      type: object
      properties:
        userId:
          type: string
        name:
          type: string
        birthDate:
          type: string
        document:
          type: string
        mobilePhone:
          type: string
        email:
          type: string
    simple-signup-response:
      type: object
      properties:
        userData:
          $ref: '#/components/schemas/user-data-request'
        enrollment:
          type: object
          properties:
            livenessId:
              type: string
            done:
              type: boolean
        userContract:
          type: object
          properties:
            livenessId:
              type: string
            hasAccepted:
              type: boolean
            contract:
              type: string
        termsOfUse:
          type: string
        validationStatus:
          type: string
    modatta-transfer-request:
      type: object
      properties:
        operationId:
          type: string
        originAccountNo:
          type: string
        destinationAccountNo:
          type: string
        amount:
          type: number
    modatta-transfer-response:
      type: object
      properties:
        operationId:
          type: string
        status:
          type: string
          enum:
            - SUCCESS
            - INSUFFICIENT_FUNDS
            - TIMEOUT
            - ERROR
            - INVALID_DATA
            - UNKNOWN
        gateway:
          type: string
          enum:
            - ARBI
        amount:
          type: number
        authentication:
          type: string
        errorDescription:
          type: string
        debitOperationNumber:
          type: string
        creditOperationNumber:
          type: number
    modatta-pix-request:
      type: object
      description: Requisicao de Pix. Deve passar recipientPixKey ou recipientBankAccount. Nunca os dois.
      properties:
        operationId:
          type: string
        description:
          type: string
        recipientPixKey:
          $ref: '#/components/schemas/pix-key'
        recipientName:
          type: string
        recipientDocument:
          type: string
        recipientBankAccount:
          $ref: '#/components/schemas/bank-details'
        originBankAccountNo:
          type: number
        originBankAccountDv:
          type: string
        amount:
          type: number
    modatta-pix-response:
      type: object
      properties:
        operationId:
          type: string
        status:
          type: string
          enum:
            - ACKNOWLEDGED
            - REFUSED
            - SCHEDULED
            - FAILED
        error:
          type: string
          enum:
            - PIXe-001 # PaymentGenericTemporaryError
            - PIXe-002 # BusinessInsufficientBalance
            - PIXe-003 # BusinessSingleTransactionLimit
            - PIXe-004 # BusinessDailyLimit
            - PIXe-005 # BusinessSameValuePaymentsExceeded
            - PIXe-006 # SettlementDestinationAccountNotFound
            - PIXe-007 # SettlementDestinationAccountNotAvailable
            - PIXe-008 # SettlementGenericPermanentError
            - PIXe-009 # SettlementGenericTemporaryError
            - PIXe-010 # SettlementPaymentRefusedByDestination
            - PIXe-011 # SettlementDestinationInstitutionNotAllowed
            - PIXe-012 # SettlementDestinationAccountTypeInvalid
            - PIXe-013 # SettlementDestinationNotAllowedAmount
            - PIXe-014 # UnknownTemporaryError
            - PIXe-015 # InvalidPixkey
            - PIXe-016 # PaymentSourceAccountLocked
        endToEnd:
          type: string
    error:
      type: object
      properties:
        code:
          type: string
          description: code of the error
        message:
          type: string
          description: descriptive message of the error
      description: an error
    pix-key:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/pix-type'
        value:
          type: string
    pix-type:
      type: string
      enum:
        - CPF
        - CNPJ
        - PHONE
        - EMAIL
        - EVP
    bank-details:
      type: object
      properties:
        accountType:
          type: string
          enum:
            - CHECKING
            - SAVINGS
        bankNo:
          type: integer
        routingNo:
          type: integer
        accountNo:
          type: integer
        accountDv:
          type: string
        ispb:
          type: string
      description: Bank details of the owner of the account