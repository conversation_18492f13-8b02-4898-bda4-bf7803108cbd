openapi: 3.0.1
info:
  title: Bill Payment API
  version: "2"
servers:
  - url: /
paths:
  /intercom/userHashes:
    get:
      tags:
        - Intercom
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      responses:
        200:
          description: Ok
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/intercom-user-hash-response'
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /notification/bills:
    get:
      tags:
        - Notification
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      responses:
        200:
          description: Ok
          content:
            'json/application':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/bills-notification-response'
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /register:
    get:
      tags:
        - Register
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      responses:
        200:
          description: Ok
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/register'
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /register/flow/{name}:
    put:
      tags:
        - Register
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
        - name: name
          in: path
          description: User's flow
          required: true
          schema:
            type: string
            enum:
              - BILLING_CAROUSEL
      responses:
        204:
          description: No content
        400:
          description: Bad request
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          $ref: '#/components/responses/server-error'
  /register/mobile:
    post:
      tags:
        - Register
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      requestBody:
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/mobilePhone'
        required: false
      responses:
        201:
          description: Created
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/issuedToken'
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        409:
          description: Token already exists
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/error'
        500:
          $ref: '#/components/responses/server-error'
  /register/mobile/token:
    put:
      tags:
        - Register
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      requestBody:
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/mobilePhoneToken'
        required: false
      responses:
        200:
          description: ok
          content:
            'json/application':
              schema:
                type: object
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /register/document:
    post:
      deprecated: true
      tags:
        - Register
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                document:
                  type: string
                  description: The user document to upload.
                  format: binary
      responses:
        200:
          description: Register Updated
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/register'
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /register/document/cnh:
    post:
      tags:
        - Register
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - document
              properties:
                document:
                  type: string
                  description: The user CNH full document to upload.
                  format: binary
      responses:
        200:
          description: Register Updated
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/register'
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /register/document/rg/backSide:
    post:
      tags:
        - Register
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                document:
                  type: string
                  description: The user document image to upload.
                  format: binary
      responses:
        200:
          description: Register Updated
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/register'
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /register/document/rg/frontSide:
    post:
      tags:
        - Register
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                document:
                  type: string
                  description: The user document image to upload.
                  format: binary
      responses:
        200:
          description: Register Updated
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/register'
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /register/document/data:
    put:
      tags:
        - Register
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      requestBody:
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/documentData'
        required: false
      responses:
        200:
          description: Register Updated
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/register'
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /register/address:
    put:
      tags:
        - Register
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      requestBody:
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/address'
        required: false
      responses:
        200:
          description: Register Updated
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/register'
        400:
          description: Bad Request
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /register/monthlyIncome:
    put:
      tags:
        - Register
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      requestBody:
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/monthlyIncome'
        required: true
      responses:
        200:
          description: Register Updated
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/register'
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /register/selfie:
    post:
      tags:
        - Register
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                selfie:
                  type: string
                  description: The user selfie to upload.
                  format: binary
      responses:
        200:
          description: Register Updated
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/register'
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /register/politicallyExposed:
    put:
      tags:
        - Register
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      requestBody:
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/politicallyExposed'
        required: false
      responses:
        200:
          description: Register Updated
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/register'
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /register/agreement:
    put:
      tags:
        - Register
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      requestBody:
        content:
          'json/application':
            schema:
              type: object
              properties:
                hasAccepted:
                  type: boolean
        required: false
      responses:
        200:
          description: Register Updated
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/register'
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /register/signup:
    post:
      tags:
        - Register
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      requestBody:
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/signupRequest'
        required: true
      responses:
        201:
          description: Created
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/signupResponse'
        400:
          description: Bad Request
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/error'
              examples:
                signupInvalidPassword:
                  summary: invalid password
                  value:
                    code: "INVALID_PASSWORD"
                    message: "invalid password"
                signupInvalidPhone:
                  summary: invalid phone number
                  value:
                    code: "INVALID_PHONE_NUMBER"
                    message: "invalid phone number"
        401:
          description: Unauthorized
        403:
          description: Forbidden
        409:
          description: User already exists
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/error'
              examples:
                signupUsernameAlreadyExists:
                  summary: username already exists
                  value:
                    code: "USERNAME_ALREADY_EXISTS"
                    message: "username already exists"
        500:
          $ref: '#/components/responses/server-error'
  /backoffice/register/{accountId}/internalApprove:
    post:
      tags:
        - Back Office
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
        - name: ignoreDocument
          in: query
          required: false
          schema:
            default: false
            type: boolean
      responses:
        204:
          description: Send account documents to Arbi approval
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          description: Internal Server Error

  /backoffice/register:
    get:
      tags:
        - Back Office
      responses:
        200:
          description: Ok
          content:
            'json/application':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/backoffice-register'
        401:
          description: Unauthorized
        403:
          description: Forbidden

  /backoffice/register/{accountId}:
    get:
      tags:
        - Back Office
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: Ok
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/backoffice-register'
        401:
          description: Unauthorized
        403:
          description: Forbidden

  /backoffice/register/{accountId}/activate:
    post:
      tags:
        - Back Office
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: account activated
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /backoffice/register/{accountId}/submitInternalReview:
    post:
      tags:
        - Back Office
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: Send account documents to internal review
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /backoffice/register/{accountId}/deny:
    post:
      tags:
        - Back Office
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          'json/application':
            schema:
              type: object
              properties:
                reason:
                  type: string
              description: bill
        required: false
      responses:
        204:
          description: Account Denied
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /backoffice/register/{accountId}/review:
    post:
      tags:
        - Back Office
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
      responses:
        204:
          description: Account Register Incomplete
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /backoffice/resetPassword/{username}:
    post:
      tags:
        - Back Office
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: username
          in: path
          required: true
          schema:
            type: string
      responses:
        204:
          description: Password resetted
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'

  /backoffice/account/{accountId}/bill:
    post:
      tags:
        - Back Office
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/backoffice-add-bill'
      responses:
        201:
          description: Bills created
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /backoffice/account/{accountId}:
    delete:
      tags:
        - Back Office
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: Account deleted
        409:
          description: Conflict
        500:
          $ref: '#/components/responses/server-error'
  /backoffice/partialAccount/{accountId}:
    delete:
      tags:
        - Back Office
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: Account deleted
        409:
          description: Conflict
        500:
          $ref: '#/components/responses/server-error'
  /backoffice/signUpTest/{accountId}:
    delete:
      tags:
        - Back Office
      description: Deleta um Partial account de ums dos emails de accountRegister.testEmails
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: Account deleted
        400:
          description: accountId não é de uma conta de teste
        409:
          description: Conflict
        500:
          $ref: '#/components/responses/server-error'
  /backoffice/account/{accountId}/referrer:
    post:
      tags:
        - Back Office
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      requestBody:
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/account-referrer'
        required: false
      responses:
        204:
          description: No content
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          $ref: '#/components/responses/server-error'
  /backoffice/zipcode:
    post:
      tags:
        - Back Office
      requestBody:
        content:
          'json/application':
            schema:
              type: object
              properties:
                zipCode:
                  type: string
              description: bill
        required: false
      responses:
        201:
          description: Created
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          $ref: '#/components/responses/server-error'

  /backoffice/account/{accountId}/mobilePhone:
    put:
      tags:
        - Back Office
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          'json/application':
            schema:
              type: object
              properties:
                newMobilePhone:
                  type: string
                currentMobilePhone:
                  type: string
      responses:
        204:
          description: Mobile phone updated
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          description: Server error


  /backoffice/account/{accountId}/emailAddress:
    put:
      tags:
        - Back Office
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          'json/application':
            schema:
              type: object
              properties:
                newEmailAddress:
                  type: string
                currentEmailAddress:
                  type: string
      responses:
        204:
          description: Email address updated
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          description: Server error

  /backoffice/register/{accountId}/externalApprove/accept:
    post:
      tags:
        - Back Office
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
      responses:
        204:
          description: Usuário aprovado no parceiro bancário
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          description: Internal Server Error

  /backoffice/register/{accountId}/externalApprove/reject:
    post:
      tags:
        - Back Office
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
      responses:
        204:
          description: Usuário rejeitado no parceiro bancário
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          description: Internal Server Error
  /backoffice/crmUpsertAccount/{accountId}:
    post:
      tags:
        - Back Office
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: crm contact
          'json/application':
            schema:
              $ref: '#/components/schemas/contact'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          description: Internal Server Error

  /backoffice/crmUpsertPartialAccount/{accountId}:
    post:
      tags:
        - Back Office
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: crm contact
          'json/application':
            schema:
              $ref: '#/components/schemas/contact'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          description: Internal Server Error

  /zipcode/{zipcode}:
    get:
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: zipcode
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: logged user
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/address'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'

  /pushNotification:
    post:
      tags:
        - Push Notification
      requestBody:
        content:
          'json/application':
            schema:
              type: object
              properties:
                pushNotificationToken:
                  type: string
        required: true
      responses:
        204:
          description: Created
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          $ref: '#/components/responses/server-error'

  /pushNotification/hasToken:
    post:
      tags:
        - Push Notification
      requestBody:
        content:
          'json/application':
            schema:
              type: object
              properties:
                pushNotificationToken:
                  type: string
        required: true
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      responses:
        200:
          description: ok
          content:
            'json/application':
              schema:
                type: object
                properties:
                  hasToken:
                    type: boolean
        401:
          description: Unauthorized
        403:
          description: Forbidden

  /wallet:
    get:
      tags:
        - Wallet
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      responses:
        200:
          description: Ok
          content:
            'json/application':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/wallet-response'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          $ref: '#/components/responses/server-error'
    post:
      tags:
        - Wallet
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      requestBody:
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/wallet-request'
        required: false
      responses:
        201:
          description: Created
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/wallet-response'
        400:
          $ref: '#/components/responses/bad-request-create-wallet'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          $ref: '#/components/responses/server-error'
  /wallet/{walletId}:
    get:
      tags:
        - Wallet
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: walletId
          in: path
          description: wallet id
          required: true
          schema:
            type: string
      responses:
        200:
          description: Ok
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/wallet-response'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        500:
          $ref: '#/components/responses/server-error'
  /wallet/{walletId}/usage:
    get:
      tags:
        - Wallet
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: walletId
          in: path
          description: wallet id
          required: true
          schema:
            type: string
      responses:
        200:
          description: Ok
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/wallet-usage'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        500:
          $ref: '#/components/responses/server-error'
  /wallet/{walletId}/payment-method:
    get:
      tags:
        - Wallet
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: walletId
          in: path
          description: wallet id
          required: true
          schema:
            type: string
      responses:
        200:
          description: Ok
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/wallet-payment-methods'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        500:
          $ref: '#/components/responses/server-error'

  /wallet/{walletId}/invites:
    get:
      description: list all invites in this wallet
      tags:
        - Wallet
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: walletId
          in: path
          description: wallet id
          required: true
          schema:
            type: string
      responses:
        200:
          description: Ok
          content:
            'json/application':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/invite-response'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        500:
          $ref: '#/components/responses/server-error'
  /wallet/{walletId}/invite:
    get:
      description: get an invite for the current user in this wallet
      tags:
        - Wallet
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: walletId
          in: path
          description: wallet id
          required: true
          schema:
            type: string
      responses:
        200:
          description: Ok
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/invite-response'
        404:
          $ref: '#/components/responses/not-found'
        410:
          description: Gone. Invite is not pending.
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/error'
        500:
          $ref: '#/components/responses/server-error'
    post:
      tags:
        - Wallet
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: walletId
          in: path
          description: wallet id
          required: true
          schema:
            type: string
      requestBody:
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/invite-request'
        required: false
      responses:
        201:
          description: Created
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/invite-response'
        400:
          description: Bad Request
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/error'
              examples:
                PendingInvitationsLimitReached:
                  value:
                    code: "4000"
                    message: "Pending invitations limit reached"
                UserAlreadyMember:
                  value:
                    code: "4001"
                    message: "Invitee is already a wallet member"
                ViewNoBillsMemberPermission:
                  value:
                    code: "4002"
                    message: "MemberPermissions to view NO_BILLS is not allowed"
                IllegalMemberPermission:
                  value:
                    code: "4003"
                    message: "MemberPermissions to view ONLY_BILLS_ADDED_BY_USER and schedule ALL_BILLS is not allowed"
                InvalidDocument:
                  value:
                    code: "4004"
                    message: "document must be 11 digits for CPF or 14 digits for CNPJ"
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          description: Conflict
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/invite-response'
        500:
          $ref: '#/components/responses/server-error'
    put:
      tags:
        - Wallet
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: walletId
          in: path
          description: wallet id
          required: true
          schema:
            type: string
      requestBody:
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/invite-answer'
        required: false
      responses:
        204:
          description: No content
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        410:
          description: Gone. Invite is not pending.
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/error'
        500:
          $ref: '#/components/responses/server-error'
  /wallet/{walletId}/invite/{document}:
    delete:
      tags:
        - Wallet
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: walletId
          in: path
          description: wallet id
          required: true
          schema:
            type: string
        - name: document
          in: path
          description: cpf
          required: true
          schema:
            type: string
      responses:
        202:
          description: Accepted
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        410:
          description: Gone. Invite is not pending.
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/error'
        500:
          $ref: '#/components/responses/server-error'
  /public/invite/{walletId}/{document}:
    get:
      tags:
        - Wallet
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: walletId
          in: path
          description: wallet id
          required: true
          schema:
            type: string
        - name: document
          in: path
          description: cpf
          required: true
          schema:
            type: string
      responses:
        200:
          description: Ok
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/invite-response'
        404:
          $ref: '#/components/responses/not-found'
        410:
          description: Gone. Invite is not pending.
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/error'
        500:
          $ref: '#/components/responses/server-error'
  /wallet/{walletId}/member/{accountId}:
    delete:
      tags:
        - Wallet
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: walletId
          in: path
          description: wallet id
          required: true
          schema:
            type: string
        - name: accountId
          in: path
          description: account id
          required: true
          schema:
            type: string
      responses:
        204:
          description: No content
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          description: Not Found
        422:
          description: Unprocessable Entity
        500:
          $ref: '#/components/responses/server-error'
    put:
      tags:
        - Wallet
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: walletId
          in: path
          description: wallet id
          required: true
          schema:
            type: string
        - name: accountId
          in: path
          description: account id
          required: true
          schema:
            type: string
      requestBody:
        content:
          'json/application':
            schema:
              type: object
              properties:
                type:
                  $ref: '#/components/schemas/member-types'
              description: bill
        required: true
      responses:
        204:
          description: No content
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          description: Not Found
        422:
          description: Unprocessable Entity
        500:
          $ref: '#/components/responses/server-error'
  /account/invite:
    get:
      tags:
        - Wallet
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      responses:
        200:
          description: Ok
          content:
            'json/application':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/invite-response'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          $ref: '#/components/responses/server-error'
  /principal:
    get:
      summary: get logged user
      description: Info on the logged user.
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      responses:
        200:
          description: logged user
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/principal'
              examples:
                usuarioOwner:
                  value:
                    accountId: "ACCOUNT-123456"
                    role: "OWNER"
                    email: "<EMAIL>"
                    nickname: "zezinho"
                    defaultWalletId: "ACCOUNT-123456"
                    document: "***********"
                    migrated: true
                    federated: false
                usuarioGuest:
                  value:
                    accountId: "ACCOUNT-123456"
                    role: "GUEST"
                    email: "<EMAIL>"
                    nickname: "zezinho"
        401:
          description: Unauthorized
        403:
          description: Forbidden

  /account:
    get:
      summary: get via1 account
      description: Info on the via1 account linked to the logged user.
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      responses:
        200:
          description: account
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/account'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        500:
          $ref: '#/components/responses/server-error'
  /account/usage:
    get:
      summary: get via1 account
      description: Info on the via1 account usage.
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      responses:
        200:
          description: account
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/usage'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        500:
          $ref: '#/components/responses/server-error'
  /account/config/{configName}:
    put:
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: configName
          in: path
          required: true
          schema:
            type: string
            enum:
              - DEFAULT_WALLET_ID
              - ACCESS_TOKEN
              - REFRESH_TOKEN
      requestBody:
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/account-config'
        required: false
      responses:
        204:
          description: No content
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          $ref: '#/components/responses/server-error'
  /account/referrer:
    post:
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      requestBody:
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/account-referrer'
        required: false
      responses:
        204:
          description: No content
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          $ref: '#/components/responses/server-error'
  /payment-method:
    get:
      tags:
        - Payment method
      summary: get payment method list
      description: list of payment methods on the via1 account linked to the logged
        user.
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      responses:
        200:
          description: payment method list
          content:
            'json/application':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/payment-method'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          $ref: '#/components/responses/server-error'
  /payment-method/credit-card/{payment-method-id}/challenge:
    post:
      tags:
        - Payment method
      summary: send a challenge to verify credit card
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      responses:
        204:
          description: credit card token sent
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          description: NotFound
        500:
          $ref: '#/components/responses/server-error'
  /payment-method/credit-card/{payment-method-id}/validate:
    put:
      tags:
        - Payment method
      summary: validate credit card challenge
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      requestBody:
        content:
          'json/application':
            schema:
              type: object
              properties:
                token:
                  type: number
        required: true
      responses:
        204:
          description: no content
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          description: NotFound
        500:
          $ref: '#/components/responses/server-error'

  /payment-method/credit-card:
    post:
      tags:
        - Payment method
      summary: save a tokenized credit card
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      requestBody:
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/credit-card-request'
        required: false
      responses:
        201:
          description: credit card saved
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/payment-method'
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          $ref: '#/components/responses/server-error'
  /payment-method/credit-card/bin/{bin}:
    get:
      tags:
        - Payment method
      summary: Retrieive card details from default acquirer and return read data
      parameters:
        - name: bin
          in: path
          description: credit card bin
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      responses:
        200:
          description: 'credit card details. More details available at: https://developercielo.github.io/manual/cielo-ecommerce#consulta-bin'
          content:
            'json/application':
              schema:
                type: object
                properties:
                  Status:
                    type: string
                  Provider:
                    type: string
                  CardType:
                    type: string
                  ForeignCard:
                    type: boolean
                  CorporateCard:
                    type: boolean
                  Issuer:
                    type: string
                  IssuerCode:
                    type: string
                description: Card details
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        500:
          $ref: '#/components/responses/server-error'
  /payment-method/credit-card/{payment-method-id}:
    delete:
      tags:
        - Payment method
      summary: delete a credit card
      parameters:
        - name: payment-method-id
          in: path
          description: Payment method id
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      responses:
        204:
          description: credit card deleted
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        500:
          $ref: '#/components/responses/server-error'
  /balance:
    get:
      summary: get balance
      description: list of bank account balance associated with this account
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      responses:
        200:
          description: balance list
          content:
            'json/application':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/balance'
  /balance/amount:
    get:
      summary: get balance
      description: bank account balance associated with this wallet
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      responses:
        200:
          description: balance
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/balance-amount'
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
  /balance/checkout:
    get:
      summary: get balance
      description: bank account balance associated with this wallet
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      responses:
        200:
          description: balance
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/balance-checkout'
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
  /balance/cashin:
    get:
      summary: get balance
      description: bank account balance associated with this wallet
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      responses:
        200:
          description: balance
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/balance-cashin'
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
  /bill:
    get:
      tags:
        - Bill
      summary: get bill list
      description: list of bills on the via1 account linked to the logged user.
      parameters:
        - name: status
          in: query
          description: Status values that need to be considered for filter
          schema:
            type: string
            enum:
              - ACTIVE
              - IGNORED
              - PAID
              - NOT_PAYABLE
              - PROCESSING
              - ALREADY_PAID
        - name: overdue
          in: query
          description: Get active overdue bills
          schema:
            type: boolean
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      responses:
        200:
          description: bill list
          content:
            'json/application':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/bill'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
        500:
          $ref: '#/components/responses/server-error'
  /bill/concessionaria:
    post:
      tags:
        - Bill
      description: create a new bill of type boleto
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: dryRun
          in: query
          description: run endpoint with no effect
          schema:
            type: boolean
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      requestBody:
        description: new boleto bill
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/concessionaria'
        required: false
      responses:
        200:
          description: Bill accepted successfully(not created)
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/bill'
        201:
          description: Bill created successfully.
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/bill'
        400:
          $ref: '#/components/responses/bad-request-boleto'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
        422:
          $ref: '#/components/responses/unprocessable-entity'
        500:
          $ref: '#/components/responses/server-error'
        502:
          description: Bad Gateway
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/error'
  /bill/ficha-compensacao:
    post:
      tags:
        - Bill
      description: create a new bill of type boleto
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: dryRun
          in: query
          description: run endpoint with no effect
          schema:
            type: boolean
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      requestBody:
        description: new boleto bill
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ficha-compensacao'
        required: false
      responses:
        200:
          description: Bill accepted successfully(not created)
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/bill'
        201:
          description: Bill created successfully.
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/bill'
        400:
          $ref: '#/components/responses/bad-request-boleto'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
        422:
          $ref: '#/components/responses/unprocessable-entity'
        500:
          $ref: '#/components/responses/server-error'
        502:
          description: Bad Gateway
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/error'
  /bill/invoice:
    post:
      tags:
        - Bill
      description: create a new bill of type invoice
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      requestBody:
        description: new invoice bill
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/invoice'
        required: false
      responses:
        201:
          description: Bill created successfully.
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/bill'
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
        500:
          $ref: '#/components/responses/server-error'
  /bill/pix:
    post:
      tags:
        - Bill
      description: create a new bill of type invoice
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      requestBody:
        description: new pix bill
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/pix'
        required: false
      responses:
        201:
          description: Bill created successfully.
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/bill'
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
        500:
          $ref: '#/components/responses/server-error'
  /wallet/{walletId}/moveBillsTo/{destinationWalletId}:
    post:
      tags:
        - Wallet
      description: Move bills to another wallet
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: walletId
          in: path
          required: true
          description: origin wallet id
          schema:
            type: string
        - name: destinationWalletId
          in: path
          required: true
          description: destination wallet id
          schema:
            type: string
      requestBody:
        description: bills to move
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/billsToMove'
        required: false
      responses:
        200:
          description: Bills moved successfully.
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/movedBills'
              examples:
                SomeBillsMoved:
                  value:
                    billList:
                      - billId: BILL-1111
                        moved: true
                      - billId: BILL-2222
                        moved: false
                        reason: INVALID_STATUS
                      - billId: BILL-3333
                        moved: false
                        reason: INVALID_OWNERSHIP
                      - billId: BILL-4444
                        moved: false
                        reason: USER_NOT_ALLOWED
                      - billId: BILL-5555
                        moved: false
                        reason: BILL_ALREADY_EXISTS
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
        500:
          $ref: '#/components/responses/server-error'

  /wallet/{walletId}/limit:
    get:
      tags:
        - Wallet
      description: Get wallet payment daily limits
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: walletId
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: ok
          content:
            'json/application':
              schema:
                type: object
                properties:
                  editable:
                    type: boolean
                  nighttime:
                    $ref: '#/components/schemas/dailyPaymentLimit'
                  daily:
                    $ref: '#/components/schemas/dailyPaymentLimit'
                  monthly:
                    $ref: '#/components/schemas/montlhyPaymentLimit'
                  creditCardMonthly:
                    $ref: '#/components/schemas/creditCardMonthlyLimit'

        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
    put:
      tags:
        - Wallet
      description: Update wallet id limit
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: walletId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        description: Wallet limit data to update
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/wallet-limit-request'
      responses:
        204:
          description: No content
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'

  /v2/schedule:
    post:
      tags:
        - Bill
      description: Schedule payment for billsList with paymentMethod
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      requestBody:
        description: bills and payment method used for each bill
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/billsToScheduleWithPaymentMethod'
        required: false
      responses:
        200:
          description: Bills scheduled successfully.
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/scheduledBills'
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
        500:
          $ref: '#/components/responses/server-error'

  /schedule:
    post:
      tags:
        - Bill
      description: Schedule payment for billsList
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      requestBody:
        description: bill ids to schedule
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/billsToSchedule'
        required: false
      responses:
        200:
          description: Bills scheduled successfully.
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/scheduledBills'
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
        500:
          $ref: '#/components/responses/server-error'
  /schedule/{billId}:
    delete:
      tags:
        - Bill
      summary: cancel bill payment scheduled status
      description: cancel bill payment scheduled status
      parameters:
        - name: billId
          in: path
          description: The id of bill that needs to be unscheduled
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      responses:
        200:
          description: bill payment successfully unscheduled
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
        409:
          $ref: '#/components/responses/conflict-wallet'
        404:
          $ref: '#/components/responses/not-found'
        500:
          $ref: '#/components/responses/server-error'
  /bill/id/{bill-id}/receipt:
    get:
      tags:
        - Bill
      summary: get bill receipt
      description: bill receipt
      parameters:
        - name: bill-id
          in: path
          description: Bill id
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      responses:
        200:
          description: bill receipt
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/boleto-receipt'
                  - $ref: '#/components/schemas/pix-receipt'
                  - $ref: '#/components/schemas/invoice-receipt'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
        500:
          $ref: '#/components/responses/server-error'
  /bill/id/{bill-id}/receipt/files:
    get:
      tags:
        - Bill
      summary: get bill receipt files
      description: bill receipt files
      parameters:
        - name: bill-id
          in: path
          description: Bill id
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      responses:
        200:
          description: bill receipt files
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/receipt-files'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
        500:
          $ref: '#/components/responses/server-error'
  /bill/id/{billId}/description:
    put:
      tags:
        - Bill
      summary: insert/change bill description
      description: insert/change bill description
      parameters:
        - name: billId
          in: path
          description: The id of bill that needs to be updated
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      requestBody:
        description: description of this bill
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/description'
        required: true
      responses:
        200:
          description: bill updated
          content: { }
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        409:
          $ref: '#/components/responses/conflict-wallet'
        404:
          $ref: '#/components/responses/not-found'
        500:
          $ref: '#/components/responses/server-error'
  /bill/id/{billId}/amount:
    put:
      tags:
        - Bill
      summary: change bill amount
      description: change bill amount
      parameters:
        - name: billId
          in: path
          description: The id of bill that needs to be updated
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      requestBody:
        description: amount of the bill
        content:
          'json/application':
            schema:
              type: object
              properties:
                amount:
                  type: integer
                  description: bill amount
                  required: true
                range:
                  description: the range of effectiveness of this command. if missing the default
                    is THIS.
                  required: false
                  type: string
                  enum:
                    - THIS
                    - THIS_AND_FUTURE

        required: true
      responses:
        204:
          description: bill updated
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        409:
          $ref: '#/components/responses/conflict-wallet'
        404:
          $ref: '#/components/responses/not-found'
        500:
          $ref: '#/components/responses/server-error'
  /bill/id/{bill-id}/ignore:
    put:
      tags:
        - Bill
      summary: ignore a bill
      description: ignore a bill
      parameters:
        - name: bill-id
          in: path
          description: The id of bill that needs to be ignored
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
        - name: range
          in: query
          description: the range of effectiveness of this command. if missing the default
            is THIS.
          schema:
            type: string
            enum:
              - THIS
              - THIS_AND_FUTURE
      responses:
        200:
          description: bill ignored
          content: { }
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          description: Bill not ignorable
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/error'
        500:
          $ref: '#/components/responses/server-error'
  /bill/id/{bill-id}/reactivate:
    put:
      tags:
        - Bill
      summary: reactivate a bill
      description: reactivate a bill
      parameters:
        - name: bill-id
          in: path
          description: The id of bill that needs to be reactivated
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      responses:
        200:
          description: bill reactivated
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/bill'
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
        500:
          $ref: '#/components/responses/server-error'
  /bill/id/{bill-id}/mark-as-paid:
    put:
      tags:
        - Bill
      summary: User is setting a bill as already paid
      description: change the bill's state to already paid
      parameters:
        - name: bill-id
          in: path
          description: The id of bill that was paid
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      responses:
        200:
          description: bill status changed to already paid
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/bill'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
        500:
          $ref: '#/components/responses/server-error'
  /bill/id/{bill-id}/cancel-marked-as-paid:
    put:
      tags:
        - Bill
      summary: user is removing the already paid from the bill
      description: change the bill's state to open and check its real state
      parameters:
        - name: bill-id
          in: path
          description: The id of bill
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      responses:
        200:
          description: bill status changed
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/bill'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
        500:
          $ref: '#/components/responses/server-error'
  /transaction/cash-in/{transaction-id}:
    get:
      tags:
        - Transaction
      parameters:
        - name: transaction-id
          in: path
          description: Transaction id
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      responses:
        200:
          description: cash-in completed
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/cash-in-response'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        409:
          $ref: '#/components/responses/conflict-wallet'
        404:
          $ref: '#/components/responses/not-found'
        500:
          $ref: '#/components/responses/server-error'
  /transaction/cash-in:
    post:
      tags:
        - Transaction
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      requestBody:
        description: transaction
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/cash-in-request'
        required: true
      responses:
        202:
          description: Accepted
          content: { }
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
        500:
          $ref: '#/components/responses/server-error'
  /transaction/{transaction-id}:
    get:
      tags:
        - Transaction
      parameters:
        - name: transaction-id
          in: path
          description: The id of the transaction
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      responses:
        200:
          description: transaction
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/transaction'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        500:
          $ref: '#/components/responses/server-error'
  /financialInstitution:
    get:
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      responses:
        200:
          description: bank list
          content:
            'json/application':
              schema:
                type: object
                properties:
                  financialInstitutions:
                    type: array
                    items:
                      $ref: '#/components/schemas/bank'
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /bank:
    get:
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      responses:
        200:
          description: bank list
          content:
            'json/application':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/bank'
        401:
          description: Unauthorized
        403:
          description: Forbidden
      deprecated: true
  /recipient:
    get:
      tags:
        - Recipient
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      responses:
        200:
          description: List of contacts with bank accounts
          content:
            'json/application':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/savedRecipient'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
  /recipient/{id}:
    put:
      tags:
        - Recipient
      parameters:
        - name: id
          in: path
          description: recipient id
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      requestBody:
        description: 'Update recipient '
        content:
          'json/application':
            schema:
              type: object
              properties:
                alias:
                  type: string
        required: true
      responses:
        200:
          description: recipient updated
          content: { }
        400:
          $ref: '#/components/responses/bad-request'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
    delete:
      tags:
        - Recipient
      parameters:
        - name: id
          in: path
          description: recipient id
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      responses:
        202:
          description: recipient deleted
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
        409:
          $ref: '#/components/responses/conflict-wallet'
  /recipient/id/{id}:
    delete:
      tags:
        - Recipient
      parameters:
        - name: id
          in: path
          description: recipient id
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      responses:
        202:
          description: recipient deleted
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
      deprecated: true
  /recipient/{id}/pixKey:
    post:
      tags:
        - Recipient
      parameters:
        - name: id
          in: path
          description: recipient id
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      requestBody:
        description: pixkey
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/pix-key'
        required: true
      responses:
        204:
          description: pix key added
          content: { }
        400:
          $ref: '#/components/responses/bad-request-pix'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
  /recipient/{id}/pixKey/{value}:
    delete:
      tags:
        - Recipient
      parameters:
        - name: id
          in: path
          description: recipient id
          required: true
          schema:
            type: string
        - name: value
          in: path
          description: pix key value
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      responses:
        202:
          description: pix key deleted
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
  /recipient/{id}/bankAccount:
    post:
      tags:
        - Recipient
      parameters:
        - name: id
          in: path
          description: recipient id
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      requestBody:
        description: bank account details
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/recipient-bank-details'
        required: true
      responses:
        204:
          description: bank account added
          content: { }
        400:
          $ref: '#/components/responses/bad-request-pix'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
  /recipient/{id}/bankAccount/{bankAccount-id}:
    put:
      tags:
        - Recipient
      parameters:
        - name: id
          in: path
          description: recipient id
          required: true
          schema:
            type: string
        - name: bankAccount-id
          in: path
          description: bank account id
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      requestBody:
        description: bank account details
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/recipient-bank-details'
        required: true
      responses:
        204:
          description: bank account updated
          content: { }
        400:
          $ref: '#/components/responses/bad-request-pix'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
    delete:
      tags:
        - Recipient
      parameters:
        - name: id
          in: path
          description: recipient id
          required: true
          schema:
            type: string
        - name: bankAccount-id
          in: path
          description: bank account id
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      responses:
        202:
          description: bank account deleted
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
  /recipient/id/{recipient-id}/bankAccounts/id/{bankAccount-id}:
    delete:
      tags:
        - Recipient
      parameters:
        - name: recipient-id
          in: path
          description: recipient id
          required: true
          schema:
            type: string
        - name: bankAccount-id
          in: path
          description: bank account id
          required: true
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: X-WALLET-ID
          in: header
          description: wallet id
          schema:
            type: string
      responses:
        202:
          description: bank account deleted
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
        409:
          $ref: '#/components/responses/conflict-wallet'
      deprecated: true
  /name/{document}:
    get:
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: document
          in: path
          description: document id
          required: true
          schema:
            type: string
      responses:
        200:
          description: Get name by document
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/name'
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          $ref: '#/components/responses/not-found'
  /holiday:
    get:
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      responses:
        200:
          description: Get next holidays list
          content:
            'json/application':
              schema:
                type: array
                items:
                  type: string
                  description: holiday on dd/MM/yyyy
                  example: 08/10/2020
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /pix/{keyType}/{keyValue}:
    get:
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: keyType
          in: path
          required: true
          schema:
            type: string
            enum:
              - CPF
              - CNPJ
              - PHONE
              - EMAIL
              - EVP
        - name: keyValue
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            'json/application':
              schema:
                $ref: '#/components/schemas/pix-key-details'
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /arbi/deposit:
    post:
      tags:
        - Arbi Integration
      requestBody:
        description: Arbi deposit callback data
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/arbi-deposit-callback'
        required: true
      responses:
        204:
          description: Ok
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /arbi/registerStatus:
    post:
      tags:
        - Arbi Integration
      requestBody:
        description: Arbi deposit callback data
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/arbi-register-status-callback'
        required: true
      responses:
        200:
          description: Ok
          content: { }
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /account/{document}/has-password:
    get:
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
        - name: document
          in: path
          required: true
          schema:
            type: string
        - name: phone
          in: query
          required: true
          schema:
            type: string
      responses:
        200:
          description: Ok
          content:
            'json/application':
              schema:
                type: object
                properties:
                  hasPassword:
                    type: boolean
        400:
          description: Bad Request
        500:
          description: Internal Server Error

  /chatbot/pix-copy-and-paste:
    get:
      tags:
        - Chatbot
      parameters:
        - name: X-EXTERNAL-USER-ID
          in: header
          required: true
          schema:
            type: string
      responses:
        200:
          description: Ok
          content:
            'json/application':
              schema:
                type: object
                properties:
                  qrCode:
                    type: string
        401:
          description: Unauthorized
        403:
          description: Forbidden

  /chatbot/payment-organizations:
    get:
      tags:
        - Chatbot
        - ITP
      parameters:
        - name: X-EXTERNAL-USER-ID
          in: header
          required: true
          schema:
            type: string
        - name: X-BUTTON-PAYLOAD
          in: header
          required: true
          schema:
            type: string
      responses:
        200:
          description: Ok
          content:
            'json/application':
              schema:
                type: object
                properties:
                  walletId:
                    type: string
                  document:
                    type: string
                  size:
                    type: number
                  otherFinancialInstitutions:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                        title:
                          type: string
                        description:
                          type: string
                  result:
                    type: object
                    properties:
                      0:
                        type: object
                        properties:
                          authorizationServerId:
                            type: string
                          bankISPB:
                            type: string
                          routingNo:
                            type: number
                          accountNo:
                            type: number
                          accountDv:
                            type: string
                          accountType:
                            type: string
                          bankNo:
                            type: number
                          institutionName:
                            type: string
        401:
          description: Unauthorized
        403:
          description: Forbidden
        400:
          description: User account not found
        500:
          description: Internal Server Error

  /chatbot/payment-intent:
    post:
      tags:
        - Chatbot
        - ITP
      parameters:
        - name: X-EXTERNAL-USER-ID
          in: header
          required: true
          schema:
            type: string
        - name: X-BUTTON-PAYLOAD
          in: header
          required: true
          schema:
            type: string
      requestBody:
        description: Chatbot request to initialize a ITP payment
        content:
          'json/application':
            schema:
              $ref: '#/components/schemas/payment-intent-request'
        required: true
      responses:
        201:
          description: payment intent created
          content:
            'json/application':
              schema:
                type: object
                properties:
                  token:
                    type: string
                    deprecated: true
                  paymentIntentId:
                    type: string
        400:
          description: User account not found or user has enought balance for payments
        401:
          description: Unauthorized
        403:
          description: Forbidden

  /chatbot/notify-balance:
    get:
      tags:
        - Chatbot
      parameters:
        - name: X-EXTERNAL-USER-ID
          in: header
          required: true
          schema:
            type: string
      responses:
        200:
          description: Ok
          content:
            'json/application':
              schema:
                type: object
        401:
          description: Unauthorized
        403:
          description: Forbidden
  /itp/payment/{paymentIntentId}:
    get:
      tags:
        - ITP
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: string
      responses:
        200:
          description: redirect url created
          content:
            'json/application':
              schema:
                type: object
                properties:
                  consentPage:
                    type: string
  /utilityAccount:
    post:
      tags:
        - Connect utility
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
          schema:
            type: object
            properties:
              login:
                type: string
              password:
                type: string
              utility:
                type: string
      responses:
        201:
          description: Utility Account creation
        400:
          description: Utility Account already created
        404:
          description: Utility Account not found
        500:
          description: Internal Server Error
    get:
      tags:
        - Connect utility
      parameters:
        - name: X-API-VERSION
          in: header
          description: api version
          required: true
      responses:
        200:
          description: all utility accounts from user
          content:
            'json/application':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/utility-account-register'
        500:
          description: Internal Server Error

  /utilityAccount/{utilityAccountId}:
    delete:
      tags:
        - Connect utility
      parameters:
        - name: X-EXTERNAL-USER-ID
          in: header
          required: true
          schema:
            type: string
      responses:
        200:
          description: Utility account disconnected
          content: { }
        404:
          description: Utility account not found
          content: { }
        500:
          description: Internal Server Error
  /utility:
    get:
      tags:
        - Connect utility
      parameters:
        - name: X-EXTERNAL-USER-ID
          in: header
          required: true
          schema:
            type: string
      responses:
        200:
          description: list utilities
          content:
            'json/application':
              schema:
                type: array
                items:
                  type: string
components:
  schemas:
    utility-account-register:
      type: object
      properties:
        id:
          type: string
          description: utility account id
        login:
          type: string
        password:
          type: string
        utility:
          type: string
          enum:
            - VIVO_MOBILE
            - CLARO_HOME
            - CLARO_MOBILE
            - TIM_MOBILE
        status:
          type: string
          enum:
            - PENDING
            - REQUESTED
            - CONNECTED
            - CONNECTION_ERROR
            - DISCONNECTED
            - PENDING_DISCONNECTION
            - DISCONNECTION_ERROR
        accountId:
          type: string
        accountEmail:
          type: string
        attemps:
          type: integer
        updatedAt:
          type: string
          nullable: true
        createdAt:
          type: string
          nullable: true
        notificatedAt:
          type: string
          nullable: true
    backoffice-register:
      type: object
      properties:
        accountId:
          type: string
          description: user's account id
        name:
          type: string
          description: user's name
        updated:
          type: string
          description: YYYY-MM-DD HH:MM:SS
        document:
          type: string
          description: user's document
        email:
          type: string
          description: user's email address
        phone:
          type: string
          description: user's phone number with international code
        status:
          type: string
          description: user's account status
          enum:
            - ACTIVE
            - CLOSED
            - REGISTER_INCOMPLETE
            - DENIED
            - UNDER_REVIEW
            - UNDER_EXTERNAL_REVIEW
            - APPROVED

    backoffice-add-bill:
      type: object
      properties:
        from:
          type: string
          description: Whom sent original message
        subject:
          type: string
          description: Bill subject
        digitable:
          type: array
          items:
            type: string

    intercom-user-hash-response:
      type: object
      properties:
        web:
          type: string
          description: intercom user hash for web
        ios:
          type: string
          description: intercom user hash for ios
        android:
          type: string
          description: intercom user hash for android
    bills-notification-response:
      type: object
      properties:
        walletId:
          type: string
          description: Id of the wallet
        billsOverdue:
          type: number
          description: quantity of bills overdue on this wallet
        billsWaitingFunds:
          type: boolean
          description: if there are bills waiting funds on this wallet
    principal:
      type: object
      properties:
        accountId:
          type: string
          description: Id of the account linked to this user
        role:
          type: string
          description: Role of this user on the account
          enum:
            - OWNER
            - GUEST
        email:
          type: string
          description: Authenticated user's e-mail
        nickname:
          type: string
          description: Authenticated user's nickname. Related to Social Login name
        defaultWalletId:
          type: string
        partnerUsername:
          type: string
        document:
          type: string
        federated:
          type: boolean
          description: Whether owner is using federated sign-in or not
      required:
        - accountId
        - role
        - email
        - nickname
      description: principal user
    paymentMethodEnum:
      type: string
      description: type of payment method
      enum:
        - CREDIT_CARD
        - BALANCE
    arbi-register-status-callback:
      type: object
      properties:
        inscricao:
          type: string
          description: ""
        nome:
          type: string
          description: ""
        situacao:
          type: string
          description: ""
        idrequisicaoarbi:
          type: string
          description: ""
        nroconta:
          type: string
          description: ""
    arbi-deposit-callback:
      type: object
      properties:
        valor:
          type: number
          description: ""
        codHistorico:
          type: string
          description: ""
        descricao:
          type: string
          description: ""
        bancoorigem:
          type: string
          description: ""
        agenciaorigem:
          type: string
          description: ""
        contaorigem:
          type: string
          description: ""
        inscricao:
          type: string
          description: ""
        nome:
          type: string
          description: ""
        conta:
          $ref: '#/components/schemas/arbi-deposit-conta'
      description: Arbi deposit callback data
    arbi-deposit-conta:
      type: object
      properties:
        banco:
          type: string
        agencia:
          type: string
        conta:
          type: string
    account:
      type: object
      properties:
        id:
          type: string
          description: Id of the account
        fullName:
          type: string
          description: Name of the owner of the account
        document:
          type: string
          description: Document of the owner of the account
        documentType:
          type: string
          description: Document type of the owner of the account.
          enum:
            - CPF
            - CNPJ
        status:
          type: string
          description: account status
          enum:
            - ACTIVE
            - REGISTER_INCOMPLETE
            - DENIED
            - UNDER_REVIEW
      description: account
    pix-key:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/pix-type'
        value:
          type: string
    saved-pix-key:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/pix-type'
        value:
          type: string
        invalidated:
          type: boolean
        invalidationCode:
          $ref: '#/components/schemas/invalidationCode'
    pix-key-details:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/pix-type'
        value:
          type: string
        routingNo:
          type: number
        accountNo:
          type: number
        accountDv:
          type: string
        ispb:
          type: string
        institutionName:
          type: string
        document:
          type: string
        accountType:
          type: string
        name:
          type: string
    pix-type:
      type: string
      enum:
        - CPF
        - CNPJ
        - PHONE
        - EMAIL
        - EVP
    recipient-bank-details:
      type: object
      properties:
        accountType:
          type: string
          enum:
            - CHECKING
            - SAVINGS
        bankNo:
          type: integer
        routingNo:
          type: integer
        accountNo:
          type: integer
        accountDv:
          type: string
        ispb:
          type: string
      description: Bank details of the owner of the account
    internal-bank-details:
      type: object
      properties:
        accountType:
          type: string
          enum:
            - CHECKING
            - SAVINGS
        bankNo:
          type: integer
        routingNo:
          type: integer
        accountNo:
          type: integer
        accountDv:
          type: string
        mode:
          type: string
          enum:
            - PHYSICAL
            - VIRTUAL
      description: Bank details of the owner of the account
    payment-method-bank-details:
      type: object
      properties:
        accountType:
          type: string
          enum:
            - CHECKING
            - SAVINGS
        bankNo:
          type: integer
        routingNo:
          type: integer
        accountNo:
          type: integer
        accountDv:
          type: string
        mode:
          type: string
          enum:
            - PHYSICAL
            - VIRTUAL
        name:
          type: string
        document:
          type: string
      description: Bank details of the owner of the account
    payment-method:
      type: object
      properties:
        id:
          type: string
          description: id of the payment method
        type:
          $ref: '#/components/schemas/paymentMethodEnum'
        creditCard:
          $ref: '#/components/schemas/credit-card-response'
        bankDetails:
          $ref: '#/components/schemas/internal-bank-details'
      description: A payment method registered to an account. It can be a credit card
        or the balance on a bank account linked to the user
    payment-method-credit-card:
      type: object
      properties:
        id:
          type: string
          description: id of the payment method
        type:
          type: string
          enum:
            - CREDIT_CARD
        creditCard:
          $ref: '#/components/schemas/credit-card-response'
      description: A credit card payment method registered to an account.
    invite-request:
      type: object
      properties:
        document:
          type: string
        name:
          type: string
        email:
          type: string
        type:
          $ref: '#/components/schemas/member-types'
      required:
        - document
        - name
        - email
        - type
    invite-answer:
      type: object
      properties:
        action:
          type: string
          enum:
            - ACCEPT
            - DECLINE
      required:
        - action
    member-permissions:
      type: object
      properties:
        viewBills:
          type: string
          enum:
            - ONLY_BILLS_ADDED_BY_USER
            - ALL_BILLS
        scheduleBills:
          type: string
          enum:
            - NO_BILLS
            - ONLY_BILLS_ADDED_BY_USER
            - ALL_BILLS
        founderContactsEnabled:
          type: boolean
        cashinEnabled:
          type: boolean
        viewBalance:
          type: boolean
        manageMembers:
          type: boolean
      required:
        - viewBills
        - scheduleBills
        - founderContactsEnabled
        - cashinEnabled
        - viewBalance
    account-config:
      type: object
      properties:
        value:
          type: string
    account-referrer:
      required:
        - platform
        - appVersion
      type: object
      properties:
        referrer:
          type: string
        referrerUrl:
          type: string
        platform:
          type: string
        appVersion:
          type: string
    wallet-request:
      type: object
      properties:
        name:
          type: string
    wallet-response:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        type:
          type: string
          enum:
            - PRIMARY
            - SECONDARY
        theme:
          type: number
        thumbnailUrl:
          type: string
        canBeSetAsDefault:
          type: boolean
        isDefault:
          type: boolean
        members:
          type: array
          items:
            $ref: '#/components/schemas/wallet-member'

    dailyPaymentLimit:
      type: object
      properties:
        activeFrom:
          type: string
        currentAmount:
          type: number
        nextAmount:
          type: number
          description: Next amount if user requested a new one and if berofe 24 hours past (Optional)
        maxAmount:
          type: number
          description: Maximum amount allowed in current amount
        usedAmount:
          type: number
          description: Quota used

    montlhyPaymentLimit:
      type: object
      properties:
        activeFrom:
          type: string
        currentAmount:
          type: number
        usedAmount:
          type: number
          description: Quota used

    creditCardMonthlyLimit:
      type: object
      properties:
        activeFrom:
          type: string
        currentAmount:
          type: number
        usedAmount:
          type: number
          description: Quota used

    wallet-limit-request:
      type: object
      properties:
        type:
          type: string
          enum:
            - NIGHTTIME
            - DAILY
        amount:
          type: number
    member-types:
      type: string
      enum:
        - COFOUNDER
        - COLLABORATOR
        - ASSISTANT
    wallet-member:
      type: object
      properties:
        name:
          type: string
        alias:
          type: string
        document:
          type: string
        accountId:
          type: string
        type:
          type: string
          enum:
            - FOUNDER
            - ASSISTANT
            - COLLABORATOR
            - COFOUNDER
        permissions:
          $ref: '#/components/schemas/member-permissions'

    invite-response:
      type: object
      properties:
        walletId:
          type: string
        memberName:
          type: string
        memberDocument:
          type: string
        status:
          type: string
          enum:
            - PENDING
            - DECLINED
            - EXPIRED
            - ACCEPTED
            - CANCELED
        validUntil:
          type: string
        walletName:
          type: string
        founderName:
          type: string
        founderDocument:
          type: string
        permissions:
          $ref: '#/components/schemas/member-permissions'
    credit-card-request:
      type: object
      properties:
        brand:
          type: string
          description: credit card brand
          enum:
            - visa
            - master
            - elo
            - diners
        pan:
          type: string
          description: credit card pan
        expiryDate:
          type: string
          description: credit card expiry date. format mm/yyyy
        cvv:
          type: string
          description: credit card cvv
      description: a credit card
    credit-card-response:
      type: object
      properties:
        brand:
          type: string
          description: credit card brand
          enum:
            - visa
            - master
            - elo
            - diners
        pan:
          type: string
          description: credit card pan
        expiryDate:
          type: string
          description: credit card expiry date. format mm/yyyy
        status:
          type: string
          description: credit card status
          enum:
            - ACTIVE
            - INACTIVE
            - BLOCKED
        holderName:
          type: string
          description: holder namer, as printed on credit card
      description: a credit card
    bill:
      type: object
      properties:
        id:
          type: string
          description: id
        assignor:
          type: string
          description: assignor
        recipient:
          $ref: '#/components/schemas/recipientDetails'
        billRecipient:
          $ref: '#/components/schemas/recipientDetails'
        description:
          type: string
          description: description
        amount:
          type: integer
          description: amount
        interest:
          type: integer
          description: interest
        fine:
          type: integer
          description: fine
        discount:
          type: integer
          description: discount
        amountTotal:
          type: integer
          description: amountTotal
        amountPaid:
          type: integer
          description: amountPaid
        paymentLimitTime:
          type: string
          description: paymentLimitTime
        dueDate:
          type: string
          description: dueDate
        paidDate:
          type: string
          description: paidDate
        payer:
          $ref: '#/components/schemas/payer'
        status:
          type: string
          description: status
          enum:
            - ACTIVE
            - IGNORED
            - PAID
            - NOT_PAYABLE
            - PROCESSING
            - ALREADY_PAID
        amountCalculationModel:
          type: string
          description: Tipo modelo calculo
          enum:
            - UNKNOWN
            - ANYONE
            - BENEFICIARY_AFTER_DUE_DATE
            - BENEFICIARY_ONLY
            - ON_DEMAND
        createdOn:
          type: string
          description: createdOn
        barcode:
          type: string
          description: barcode
        billType:
          $ref: '#/components/schemas/billType'
        fichaCompensacaoType:
          type: string
          description: fichaCompensacaoType
          enum:
            - CARTAO_DE_CREDITO
            - BOLETO_PROPOSTA
            - OUTROS
        isOverdue:
          type: boolean
          description: is bill overdue
        source:
          $ref: '#/components/schemas/bill-source'
        schedule:
          type: object
          properties:
            date:
              type: string
              description: Bill payment schedule date
            waitingRetry:
              type: boolean
              description: Scheduled payment started, failed and will be retried
            waitingFunds:
              type: boolean
              description: Scheduled payment was postponed because there is insufficient
                funds
          description: Schedule info. Only present if bill is scheduled
        warningCode:
          type: string
          enum:
            - NONE
            - PAYMENT
            - SETTLEMENT
            - REFUNDED
            - SERVER_ERROR
            - BUSINESS_INSUFFICIENT_BALANCE
            - BUSINESS_SINGLE_TRANSACTION_LIMIT
            - BUSINESS_DAILY_LIMIT
            - BUSINESS_SAME_VALUE_PAYMENTS_EXCEEDED
            - SETTLEMENT_DESTINATION_ACCOUNT_NOT_FOUND
            - SETTLEMENT_DESTINATION_ACCOUNT_NOT_AVAILABLE
            - SETTLEMENT_GENERIC_PERMANENT_ERROR
            - SETTLEMENT_GENERIC_TEMPORARY_ERROR
            - SETTLEMENT_PAYMENT_REFUSED_BY_DESTINATION
            - SETTLEMENT_DESTINATION_INSTITUTION_NOT_ALLOWED
            - UNSCHEDULED_DUE_PAYMENT_LIMIT_TIME
            - UNSCHEDULED_DUE_AMOUNT_HIGHER_THAN_DAILY_LIMIT
            - POSTPONED_DUE_LIMIT_REACHED
            - SETTLEMENT_DESTINATION_NOT_ALLOWED_AMOUNT
            - NOT_VISIBLE_BILL_ALREADY_EXISTS
            - REFUNDED_DESTINATION_NOT_ALLOWED_AMOUNT
            - SETTLEMENT_INVALID_PIX_KEY
            - PIX_PAYMENT
            - CREDIT_CARD_PAYMENT
            - DEPOSIT_SLIP_SAME_INSTITUTION
            - PAYMENT_SOURCE_ACCOUNT_LOCKED
            - UNSCHEDULED_DUE_AMOUNT_CHANGED
            - CANT_PAY_WITH_CURRENT_CREDIT_CARD
        recurrence:
          $ref: '#/components/schemas/recurrenceResponse'
        effectiveDueDate:
          type: string
        possibleDuplicateBills:
          type: array
          items:
            $ref: '#/components/schemas/possibleDuplicateBill'
        availablePaymentMethods:
          type: string
          enum:
            - BALANCE
            - CREDIT_CARD
        markedAsPaidBy:
          type: string
        subscriptionFee:
          type: boolean
      description: bill
    concessionaria:
      required:
        - digitableLine
        - dueDate
      type: object
      properties:
        digitableLine:
          type: string
          description: digitableLine of the bill. 48 digits
        dueDate:
          pattern: ^\d{4}-\d{2}-\d{2}$
          type: string
          description: due date of the bill on format yyyy-MM-dd
        description:
          type: string
          description: a description of the bill. max 60 chars
      description: a bill of type boleto de concessionaria
    ficha-compensacao:
      required:
        - digitableLine
      type: object
      properties:
        digitableLine:
          type: string
          description: digitableLine of the bill. 47 digits
        description:
          type: string
          description: a description of the bill
      description: a bill of type boleto tipo ficha-compensacao
    invoice:
      required:
        - amount
        - dueDate
        - recipient
      type: object
      properties:
        recipient:
          $ref: '#/components/schemas/invoiceRecipient'
        amount:
          type: number
          description: amount of the bill
        dueDate:
          type: string
          description: due date of the bill
        description:
          type: string
          description: a description of the bill
        recurrence:
          $ref: '#/components/schemas/recurrenceRequest'
      description: invoice
    contact:
      type: object
      properties:
        accountId:
          type: string
        emailAddress:
          type: string
        role:
          type: string
        name:
          type: string
        mobilePhone:
          type: string
        document:
          type: string
        removed:
          type: boolean
        groups:
          type: string
      description: a crm contact
    pix:
      required:
        - amount
        - dueDate
        - recipient
      type: object
      properties:
        recipient:
          $ref: '#/components/schemas/pixRecipient'
        amount:
          type: number
          description: amount of the bill
        dueDate:
          type: string
          description: due date of the bill
        description:
          type: string
          description: a description of the bill
      description: Pix invoice
    range:
      type: string
      enum:
        - THIS
        - THISANDFUTURE
    recurrenceRequest:
      required:
        - frequency
      type: object
      properties:
        endDate:
          type: string
          description: date limit to generate bills if present, unlimited otherwise
        frequency:
          type: string
          enum:
            - WEEKLY
            - MONTHLY
        pattern:
          type: string
          description: required to deal with complex recurrences
      description: Recurrence data
    recurrenceResponse:
      required:
        - frequency
        - startDate
      type: object
      properties:
        id:
          type: string
        startDate:
          type: string
          description: duedate of the first bill
        endDate:
          type: string
          description: date limit to generate bills if present, unlimited otherwise
        frequency:
          type: string
          enum:
            - WEEKLY
            - MONTHLY
        pattern:
          type: string
          description: required to deal with complex recurrences
      description: Recurrence data
    description:
      type: object
      properties:
        description:
          type: string
      description: bill description
    invoiceRecipient:
      type: object
      properties:
        id:
          type: string
          description: Id of an existing recipient
        name:
          type: string
          description: Name of the recipient
        alias:
          type: string
          description: Alias of the recipient (Optional)
        document:
          type: string
          description: Document of the recipient
        documentType:
          type: string
          description: Document type of the recipient.
          enum:
            - CPF
            - CNPJ
        bankDetails:
          $ref: '#/components/schemas/recipient-bank-details'
      description: recipient of a invoice
    pixRecipient:
      type: object
      properties:
        id:
          type: string
          description: Id of the recipient if exists
        name:
          type: string
          description: Name of the recipient
        alias:
          type: string
          description: Alias of the recipient (Optional)
        document:
          type: string
          description: Document of the recipient
        documentType:
          type: string
          description: Document type of the recipient.
          enum:
            - CPF
            - CNPJ
        bankDetails:
          $ref: '#/components/schemas/recipient-bank-details'
        pixKey:
          $ref: '#/components/schemas/pix-key'
    recipientDetails:
      type: object
      properties:
        name:
          type: string
          description: Name of the recipient
        alias:
          type: string
          description: Alias of the recipient (Optional)
        document:
          type: string
          description: Document of the recipient
        documentType:
          type: string
          description: Document type of the recipient.
          enum:
            - CPF
            - CNPJ
        bankDetails:
          $ref: '#/components/schemas/recipient-bank-details'
        pixKey:
          $ref: '#/components/schemas/pix-key-details'
    savedRecipient:
      type: object
      properties:
        id:
          type: string
          description: Id of the saved recipient
        alias:
          type: string
          description: Alias of the saved recipient
        name:
          type: string
          description: Name of the saved recipient
        document:
          type: string
          description: Document of the saved recipient
        bankAccounts:
          type: array
          items:
            $ref: '#/components/schemas/bank-account'
        pixKeys:
          type: array
          items:
            $ref: '#/components/schemas/saved-pix-key'
        lastUsed:
          type: object
          nullable: true
          properties:
            bankAccountId:
              type: string
              nullable: true
            pixKey:
              nullable: true
              $ref: '#/components/schemas/saved-pix-key'
      description: saved recipient
    bank-account:
      type: object
      properties:
        id:
          type: string
        accountType:
          type: string
          enum:
            - CHECKING
            - SAVINGS
        bankNo:
          type: integer
        routingNo:
          type: integer
        accountNo:
          type: integer
        accountDv:
          type: string
        invalidated:
          type: boolean
        invalidationMessage:
          type: string
        invalidationCode:
          $ref: '#/components/schemas/invalidationCode'
      description: bank account
    cash-in-request:
      type: object
      properties:
        id:
          type: string
          description: uuid
          example: aafc8a2f-5083-46c8-a38c-a2755f9d6e13
        paymentMethod:
          $ref: '#/components/schemas/payment-method-ref'
        amount:
          type: number
      description: a via1 cash-in request
    cash-in-response:
      type: object
      properties:
        id:
          type: string
        status:
          type: string
          enum:
            - PROCESSING
            - COMPLETED
            - FAILED
            - UNDONE
        errorDetail:
          type: string
        authorizationNumber:
          type: string
        amount:
          type: number
        serviceAmountTax:
          type: number
        totalAmount:
          type: number
        paymentMethod:
          type: object
          properties:
            id:
              type: string
      description: a via1 cash-in response
    payment-method-ref:
      type: object
      properties:
        id:
          type: string
          description: id of the payment method
        type:
          type: string
          description: Type of the payment method
      description: payment method to be used in the transaction
    transaction:
      type: object
      properties:
        id:
          type: string
          description: id of the transaction
        type:
          type: string
          description: Type of the transaction
        payment:
          $ref: '#/components/schemas/payment'
        status:
          type: string
          description: Status of the transaction
          enum:
            - PROCESSING
            - FAILED
            - COMPLETED
        executedOn:
          type: string
          description: datetime of transaction execution
      description: a via1 transaction
    payment:
      type: object
      properties:
        paymentMethodId:
          type: string
          description: id of the payment method
        type:
          type: string
          description: Type of the payment method
        totalAmount:
          type: integer
          description: Total amount payed/to be payed
        items:
          type: array
          description: Items payed/to be payed
          items:
            $ref: '#/components/schemas/transaction-item'
      description: transaction payment
    transaction-item:
      type: object
      properties:
        type:
          type: string
          description: Type of the item
          enum:
            - BILL
            - FEE
        billId:
          type: string
          description: Id of item of type bill
        amount:
          type: integer
          description: Amount of the item
        settleDate:
          type: string
          description: Settle date of the item
      description: transaction item. ex. a bill, a fee, etc...
    bank:
      type: object
      properties:
        code:
          type: integer
          description: Bank number
        ispb:
          type: string
          description: Bank institution ISPB
        name:
          type: string
          description: Bank institution name
        isPixEnabled:
          type: boolean
        isTedEnabled:
          type: boolean
        isCommon:
          type: boolean
        labels:
          type: array
          items:
            type: string
      description: bank details
    error-add-bill:
      type: object
      properties:
        code:
          type: string
          description: code of the error
        message:
          type: string
          description: descriptive message of the error
        bill:
          $ref: '#/components/schemas/bill'
      description: an error
    error:
      type: object
      properties:
        code:
          type: string
          description: code of the error
        message:
          type: string
          description: descriptive message of the error
      description: an error
    wallet-payment-methods:
      type: object
      properties:
        usage:
          $ref: '#/components/schemas/creditcard-usage'
        creditCards:
          type: array
          items:
            $ref: '#/components/schemas/payment-method-credit-card'
        bankDetails:
          $ref: '#/components/schemas/payment-method-bank-details'
        pixKey:
          $ref: '#/components/schemas/pix-key'
        balance:
          type: object
          properties:
            paymentMethodId:
              type: "string"
            amount:
              type: "string"
      required:
        - usage
        - creditCards
    wallet-usage:
      type: object
      properties:
        monthlyLimit:
          type: integer
          description: monthly limit of scheduled bills on wallet
        monthlyUsage:
          type: integer
          description: current monthly usage of the limit
    usage:
      type: object
      properties:
        creditCard:
          $ref: '#/components/schemas/creditcard-usage'
      description: usage and quotas for an account
    creditcard-usage:
      type: object
      properties:
        monthlyQuota:
          type: integer
          description: monthly quota of bill payment with creditcard
        monthlyUsage:
          type: integer
          description: current monthly usage of the quota
        quota:
          type: integer
          description: quota of bill payment with creditcard
        usage:
          type: integer
          description: usage quota of bill payment with creditcard
        cashInFee:
          type: integer
          description: tax applied over cashin transactions
        quotaType:
          type: string
          description: type of credit card quota
          enum:
            - MONTHLY
            - LIFETIME
      description: usage and quotas for creditcard payment
    name:
      type: object
      properties:
        name:
          type: string
      description: contains full name
    bill-source:
      required:
        - type
      type: object
      properties:
        type:
          type: string
          enum:
            - DDA
            - Webapp
            - MailBox
            - Recurrence
        role:
          type: string
          description: Role of the user who added this bill
          enum:
            - OWNER
            - ASSISTANT
        email:
          type: string
        accountId:
          type: string
          description: AccountId of the user who added this bill
      description: source origin for this bill
    payer:
      type: object
      properties:
        name:
          type: string
        document:
          type: string
        alias:
          type: string
      description: a bill payer
    balance:
      type: object
      properties:
        paymentMethodId:
          type: string
        bankDetails:
          $ref: '#/components/schemas/internal-bank-details'
        amount:
          type: integer
        scheduledToDateAmount:
          type: integer
        scheduledTotalAmount:
          type: integer
        activeBillAmountToday:
          type: integer
        activeBillAmountWeek:
          type: integer
        activeBillAmountMonth:
          type: integer
        activeBillAmountNextMonth:
          type: integer
      description: a bank account balance
    balance-forecast:
      type: object
      properties:
        amountToday:
          type: integer
        amountWeek:
          type: integer
        amountFifteenDays:
          type: integer
        amountThirtyDays:
          type: integer
        amountMonth:
          type: integer
        amountNextMonth:
          type: integer
    balance-forecast-dates:
      type: object
      properties:
        today:
          type: string
          example: yyyy-MM-dd
        week:
          type: string
          example: yyyy-MM-dd
        fifteenDays:
          type: string
          example: yyyy-MM-dd
        thirtyDays:
          type: string
          example: yyyy-MM-dd
        nextMonth:
          type: string
          example: yyyy-MM-dd
    balance-amount:
      type: object
      properties:
        amount:
          type: integer
      description: a bank account balance
    balance-checkout:
      type: object
      properties:
        amount:
          type: integer
        scheduledToDateAmount:
          type: integer
      description: a bank account balance
    balance-cashin:
      type: object
      properties:
        amount:
          type: integer
        open:
          $ref: '#/components/schemas/balance-forecast'
        scheduled:
          $ref: '#/components/schemas/balance-forecast'
        dates:
          $ref: '#/components/schemas/balance-forecast-dates'
      description: a bank account balance
    billsToMove:
      type: object
      properties:
        billList:
          type: array
          items:
            type: string
          uniqueItems: true
      description: Bill ids to move
    movedBills:
      type: object
      properties:
        billList:
          type: array
          items:
            $ref: '#/components/schemas/moveResult'
          uniqueItems: true
      description: Bill ids marked as scheduled
    moveResult:
      type: object
      properties:
        billId:
          type: string
        moved:
          type: boolean
          description: True if bill was moved to destination wallet. False otherwise.
        reason:
          type: string
          description: Indicates why the bill was not moved
          enum:
            - INVALID_STATUS
            - USER_NOT_ALLOWED
            - BILL_ALREADY_EXISTS
            - BILL_NOT_FOUND
            - BILL_ALREADY_LOCKED
      description: result of the bill schedule request
    billsToSchedule:
      type: object
      properties:
        billList:
          type: array
          items:
            type: string
        scheduleTo:
          type: string
          enum:
            - DUE_DATE
            - TODAY
      description: Bill ids to mark as scheduled
    billsToScheduleWithPaymentMethod:
      type: object
      properties:
        bills:
          type: object
          properties:
            billId:
              type: string
            methods:
              type: array
              items:
                type: object
                properties:
                  type:
                    type: string
                    enum:
                      - CREDIT_CARD
                      - BALANCE
                  paymentMethodId:
                    type: string
                  amount:
                    type: integer
                  installments:
                    type: integer
                  calculationId:
                    type: string
        scheduleTo:
          type: string
          enum:
            - DUE_DATE
            - TODAY
      description: Bill ids to mark as scheduled
    scheduledBills:
      type: object
      properties:
        billList:
          type: array
          items:
            $ref: '#/components/schemas/scheduleResult'
      description: Bill ids marked as scheduled
    scheduleResult:
      type: object
      properties:
        billId:
          type: string
        scheduled:
          type: boolean
          description: Current bill payment schedule status
      description: result of the bill schedule request
    address:
      type: object
      properties:
        streetType:
          type: string
        streetName:
          type: string
        number:
          type: string
        complement:
          type: string
        neighborhood:
          type: string
        city:
          type: string
        state:
          type: string
        zipCode:
          type: string
    mobilePhone:
      type: object
      properties:
        number:
          type: string
        channel:
          type: string
          enum:
            - SMS
            - WHATSAPP
    mobilePhoneToken:
      type: object
      properties:
        token:
          type: string
    register:
      type: object
      properties:
        created:
          type: string
        lastUpdate:
          type: string
        email:
          type: string
        nickname:
          type: string
        mobilePhone:
          $ref: '#/components/schemas/mobilePhoneResponse'
        document:
          $ref: '#/components/schemas/documentDataResponse'
        address:
          $ref: '#/components/schemas/address'
        selfie:
          $ref: '#/components/schemas/selfie'
        monthlyIncome:
          $ref: '#/components/schemas/monthlyIncome'
        politicallyExposed:
          $ref: '#/components/schemas/politicallyExposed'
        userContract:
          $ref: '#/components/schemas/agreement'
        agreement:
          type: string
        openForUserReview:
          type: boolean
        livenessId:
          type: string
    selfie:
      type: object
      properties:
        hasValidUpload:
          type: boolean
    monthlyIncome:
      type: object
      properties:
        lowerBound:
          type: integer
        upperBound:
          type: integer
      required:
        - lowerBound
    agreement:
      type: object
      properties:
        hasAccepted:
          type: boolean
          deprecated: true
        url:
          type: string
          deprecated: true
        contract:
          type: string
        declarationOfResidency:
          type: string
    mobilePhoneResponse:
      type: object
      properties:
        number:
          type: string
        isVerified:
          type: boolean
        tokenExpiration:
          type: number
    politicallyExposed:
      type: object
      properties:
        info:
          type: string
        isExposed:
          type: boolean
    documentData:
      type: object
      properties:
        name:
          type: string
        cpf:
          type: string
        birthDate:
          type: string
          description: birthDate no formato dd/MM/yyyy
          example: dd/MM/yyyy
        birthCity:
          type: string
        birthState:
          type: string
        fatherName:
          type: string
        motherName:
          type: string
        orgEmission:
          type: string
        cnhNumber:
          type: string
        rgNumber:
          type: string
        expeditionDate:
          type: string
          description: expeditionDate no formato dd/MM/yyyy
          example: dd/MM/yyyy
    documentDataResponse:
      type: object
      properties:
        uploadedDocumentStatus:
          type: string
          enum:
            - FULL
            - ONLY_FRONT
        name:
          type: string
        cpf:
          type: string
        birthDate:
          type: string
          description: birthDate no formato dd/MM/yyyy
          example: dd/MM/yyyy
        birthCity:
          type: string
        birthState:
          type: string
        fatherName:
          type: string
        motherName:
          type: string
        orgEmission:
          type: string
        cnhNumber:
          type: string
        rgNumber:
          type: string
        expeditionDate:
          type: string
          description: expeditionDate no formato dd/MM/yyyy
          example: dd/MM/yyyy
        isEdited:
          type: boolean
    issuedToken:
      type: object
      properties:
        duration:
          type: string
    invalidationCode:
      type: string
      enum:
        - INVALID_TYPE
        - INVALID_DATA
    boleto-receipt:
      type: object
      properties:
        totalAmount:
          type: number
        dateTime:
          type: string
        recipient:
          $ref: '#/components/schemas/recipientDetails'
        assignorName:
          type: string
        dueDate:
          type: string
        payerName:
          type: string
        payerDocument:
          type: string
        payerDocumentType:
          type: string
        typeableLine:
          type: string
        authentication:
          type: string
        billType:
          $ref: '#/components/schemas/billType'
    invoice-receipt:
      type: object
      properties:
        totalAmount:
          type: number
        dateTime:
          type: string
        recipient:
          $ref: '#/components/schemas/recipientDetails'
        purpose:
          type: string
        payerName:
          type: string
        payerDocument:
          type: string
        payerDocumentType:
          type: string
        payerBankNumber:
          type: number
        payerRoutingNumber:
          type: number
        payerAccountNumber:
          type: number
        payerAccountDv:
          type: string
        authentication:
          type: string
        paymentPartnerName:
          type: string
        paymentPartnerDocumentType:
          type: string
        paymentPartnerDocument:
          type: string
        billType:
          $ref: '#/components/schemas/billType'
    pix-receipt:
      type: object
      properties:
        totalAmount:
          type: number
        dateTime:
          type: string
        recipient:
          $ref: '#/components/schemas/recipientDetails'
        purpose:
          type: string
        payerName:
          type: string
        payerDocument:
          type: string
        payerDocumentType:
          type: string
        payerBankNumber:
          type: number
        payerRoutingNumber:
          type: number
        payerAccountNumber:
          type: number
        payerAccountDv:
          type: string
        transactionId:
          type: string
        billType:
          $ref: '#/components/schemas/billType'
    receipt-files:
      type: object
      properties:
        imageUrl:
          type: string
        pdfUrl:
          type: string
    billType:
      type: string
      enum:
        - CONCESSIONARIA
        - FICHA_COMPENSACAO
        - INVOICE
        - PIX
    signupRequest:
      type: object
      properties:
        password:
          type: string
    signupResponse:
      type: object
      properties:
        username:
          type: string
    possibleDuplicateBill:
      type: object
      properties:
        billId:
          type: string
        dueDate:
          type: string
    payment-intent-request:
      type: object
      properties:
        walletId:
          type: string
          required: true
        document:
          type: string
          required: true
        authorizationServerId:
          type: string
          required: true
        authorizationServerName:
          type: string
          required: true
        routingNo:
          type: number
          required: true
        accountNo:
          type: number
          required: true
        accountDv:
          type: string
          required: true
        bankISPB:
          type: string
          required: false
        accountType:
          type: string
          required: false
        bankNo:
          type: number
          required: false
  parameters:
    apiVersion:
      name: X-API-VERSION
      in: header
      description: api version
      required: true
      schema:
        type: string

  responses:
    bad-request:
      description: Bad Request
      content:
        'json/application':
          schema:
            $ref: '#/components/schemas/error'
    bad-request-boleto:
      description: Bad Request
      content:
        'json/application':
          schema:
            $ref: '#/components/schemas/error-add-bill'
          examples:
            CodigoBarrasNaoLocalizado:
              value:
                code: 642
                message: CODIGO DE BARRAS NAO LOCALIZADO NA BASE CENTRALIZADA
            BoletoJaBaixado:
              value:
                code: 628
                message: BOLETO DE PAGAMENTO JA BAIXADO
    bad-request-create-wallet:
      description: Bad Request
      content:
        'json/application':
          schema:
            $ref: '#/components/schemas/error-add-bill'
          examples:
            MaxWalletsAllowed:
              value:
                code: 4001
                message: User reached max wallets allowed
            WalletNameAlreadyExists:
              value:
                code: 4002
                message: User already has a wallet named XXX
            MalformedRequest:
              value:
                code: 4004
                message: Invalid wallet name size | Wallet name with invalid characters
    bad-request-pix:
      description: Bad Request
      content:
        'json/application':
          schema:
            $ref: '#/components/schemas/error'
          examples:
            validationError:
              summary: Validation error
              value:
                code: "400"
                message: "Validation error"
            pixKeyOwnerChanged:
              summary: Current key owner document is not the same as the saved recipient document
              value:
                code: "401"
                message: "Current key owner document is not the same as the saved recipient document"
            pixKeyNotFound:
              summary: Pix Key not found
              value:
                code: "402"
                message: "Pix Key not found"
            invalidPixKey:
              summary: Invalid Pix Key format
              value:
                code: "403"
                message: "Invalid Pix Key format"
            bankAccountAlreadyExists:
              summary: Bank Account already exists with another id
              value:
                code: "404"
                message: "Bank Account already exists with another id"
            invalidBankNoOrISPB:
              summary: Invalid BankNo and/or ISPB
              value:
                code: "405"
                message: "Invalid BankNo and/or ISPB"
            missingBankNoOrISPB:
              summary: BankNo or ISPB must be present
              value:
                code: "406"
                message: "BankNo or ISPB must be present"
    not-found:
      description: Not Found
      content:
        'json/application':
          schema:
            $ref: '#/components/schemas/error'
          examples:
            RecursoNaoEncontrado:
              value:
                code: 407
                message: "Resource does not exists"
            AccountNaoEncontrado:
              value:
                code: 404
                message: "Account not found"
            BillNaoEncontrado:
              value:
                code: 404
                message: "Bill not found"
    unprocessable-entity:
      description: Unprocessable Entity
      content:
        'json/application':
          schema:
            $ref: '#/components/schemas/error'
          examples:
            BARCODE_NOT_FOUND:
              value:
                code: 4008
                message: "Boleto não encontrado"
    conflict-wallet:
      description: Conflict - User has wallet but there is no x-wallet-id header present in request
      content:
        'json/application':
          schema:
            $ref: '#/components/schemas/error'
          example:
            code: 4009
            message: "Missing x-wallet-id header"
    server-error:
      description: Unknown error
      content:
        'json/application':
          schema:
            $ref: '#/components/schemas/error'
          example:
            code: "5000"
            message: "Internal server error"
