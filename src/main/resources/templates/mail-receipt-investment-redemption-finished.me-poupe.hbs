<!DOCTYPE html>
<html lang="pt-BR"
      style="box-sizing: border-box; cursor: default; line-height: 1.5; -moz-tab-size: 4; tab-size: 4; -webkit-tap-highlight-color: transparent; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; word-break: break-word;">

<head>
    <title>Me Poupe!</title>
    <meta charset="utf-8"/>
    <meta name="color-scheme" content="only"/>
    <meta name="supported-color-schemes" content="only"/>
    <style type="text/css">/* Document
 * ========================================================================== *//**
 * Add border box sizing in all browsers (opinionated).
 */
    *,
    ::before,
    ::after {
        box-sizing: border-box;
    }

    /**
     * 1. Add text decoration inheritance in all browsers (opinionated).
     * 2. Add vertical alignment inheritance in all browsers (opinionated).
     */
    ::before,
    ::after {
        text-decoration: inherit; /* 1 */
        vertical-align: inherit; /* 2 */
    }

    /**
     * 1. Use the default cursor in all browsers (opinionated).
     * 2. Change the line height in all browsers (opinionated).
     * 3. Use a 4-space tab width in all browsers (opinionated).
     * 4. Remove the grey highlight on links in iOS (opinionated).
     * 5. Prevent adjustments of font size after orientation changes in
     *    IE on Windows Phone and in iOS.
     * 6. Breaks words to prevent overflow in all browsers (opinionated).
     */
    html {
        cursor: default; /* 1 */
        line-height: 1.5; /* 2 */
        -moz-tab-size: 4; /* 3 */
        tab-size: 4; /* 3 */
        -webkit-tap-highlight-color: transparent /* 4 */;
        -ms-text-size-adjust: 100%; /* 5 */
        -webkit-text-size-adjust: 100%; /* 5 */
        word-break: break-word; /* 6 */
    }

    /* Sections
     * ========================================================================== *//**
     * Remove the margin in all browsers (opinionated).
     */
    body {
        margin: 0;
    }

    /**
     * Correct the font size and margin on `h1` elements within `section` and
     * `article` contexts in Chrome, Edge, Firefox, and Safari.
     */
    h1 {
        font-size: 2em;
        margin: 0.67em 0;
    }

    /* Grouping content
     * ========================================================================== *//**
     * Remove the margin on nested lists in Chrome, Edge, IE, and Safari.
     */
    dl dl,
    dl ol,
    dl ul,
    ol dl,
    ul dl {
        margin: 0;
    }

    /**
     * Remove the margin on nested lists in Edge 18- and IE.
     */
    ol ol,
    ol ul,
    ul ol,
    ul ul {
        margin: 0;
    }

    /**
     * 1. Add the correct sizing in Firefox.
     * 2. Show the overflow in Edge 18- and IE.
     */
    hr {
        height: 0; /* 1 */
        overflow: visible; /* 2 */
    }

    /**
     * Add the correct display in IE.
     */
    main {
        display: block;
    }

    /**
     * Remove the list style on navigation lists in all browsers (opinionated).
     */
    nav ol,
    nav ul {
        list-style: none;
        padding: 0;
    }

    /**
     * 1. Correct the inheritance and scaling of font size in all browsers.
     * 2. Correct the odd `em` font sizing in all browsers.
     */
    pre {
        font-family: monospace, monospace; /* 1 */
        font-size: 1em; /* 2 */
    }

    /* Text-level semantics
     * ========================================================================== *//**
     * Add the correct text decoration in Edge 18-, IE, and Safari.
     */
    abbr[title] {
        text-decoration: underline;
        text-decoration: underline dotted;
    }

    /**
     * Add the correct font weight in Chrome, Edge, and Safari.
     */
    b,
    strong {
        font-weight: bolder;
    }

    /**
     * 1. Correct the inheritance and scaling of font size in all browsers.
     * 2. Correct the odd `em` font sizing in all browsers.
     */
    code,
    kbd,
    samp {
        font-family: monospace, monospace; /* 1 */
        font-size: 1em; /* 2 */
    }

    /**
     * Add the correct font size in all browsers.
     */
    small {
        font-size: 80%;
    }

    /* Embedded content
     * ========================================================================== *//*
     * Change the alignment on media elements in all browsers (opinionated).
     */
    audio,
    canvas,
    iframe,
    img,
    svg,
    video {
        vertical-align: middle;
    }

    /**
     * Remove the border on iframes in all browsers (opinionated).
     */
    iframe {
        border-style: none;
    }

    /**
     * Change the fill color to match the text color in all browsers (opinionated).
     */
    svg:not([fill]) {
        fill: currentColor;
    }

    /**
     * Hide the overflow in IE.
     */
    svg:not(:root) {
        overflow: hidden;
    }

    /* Tabular data
     * ========================================================================== *//**
     * Collapse border spacing in all browsers (opinionated).
     */
    table {
        border-collapse: collapse;
    }

    /* Forms
     * ========================================================================== *//**
     * Remove the margin on controls in Safari.
     */
    button,
    input,
    select {
        margin: 0;
    }

    /**
     * 1. Show the overflow in IE.
     * 2. Remove the inheritance of text transform in Edge 18-, Firefox, and IE.
     */
    button {
        overflow: visible; /* 1 */
        text-transform: none; /* 2 */
    }

    /**
     * Correct the inability to style buttons in iOS and Safari.
     */
    button,
    [type="button"],
    [type="reset"],
    [type="submit"] {
        -webkit-appearance: button;
    }

    /**
     * 1. Change the inconsistent appearance in all browsers (opinionated).
     * 2. Correct the padding in Firefox.
     */
    fieldset {
        border: 1px solid #a0a0a0; /* 1 */
        padding: 0.35em 0.75em 0.625em; /* 2 */
    }

    /**
     * Show the overflow in Edge 18- and IE.
     */
    input {
        overflow: visible;
    }

    /**
     * 1. Correct the text wrapping in Edge 18- and IE.
     * 2. Correct the color inheritance from `fieldset` elements in IE.
     */
    legend {
        color: inherit; /* 2 */
        display: table; /* 1 */
        max-width: 100%; /* 1 */
        white-space: normal; /* 1 */
    }

    /**
     * 1. Add the correct display in Edge 18- and IE.
     * 2. Add the correct vertical alignment in Chrome, Edge, and Firefox.
     */
    progress {
        display: inline-block; /* 1 */
        vertical-align: baseline; /* 2 */
    }

    /**
     * Remove the inheritance of text transform in Firefox.
     */
    select {
        text-transform: none;
    }

    /**
     * 1. Remove the margin in Firefox and Safari.
     * 2. Remove the default vertical scrollbar in IE.
     * 3. Change the resize direction in all browsers (opinionated).
     */
    textarea {
        margin: 0; /* 1 */
        overflow: auto; /* 2 */
        resize: vertical; /* 3 */
    }

    /**
     * 1. Correct the odd appearance in Chrome, Edge, and Safari.
     * 2. Correct the outline style in Safari.
     */
    [type="search"] {
        -webkit-appearance: textfield; /* 1 */
        outline-offset: -2px; /* 2 */
    }

    /**
     * Correct the cursor style of increment and decrement buttons in Safari.
     */
    ::-webkit-inner-spin-button,
    ::-webkit-outer-spin-button {
        height: auto;
    }

    /**
     * Correct the text style of placeholders in Chrome, Edge, and Safari.
     */
    ::-webkit-input-placeholder {
        color: inherit;
        opacity: 0.54;
    }

    /**
     * Remove the inner padding in Chrome, Edge, and Safari on macOS.
     */
    ::-webkit-search-decoration {
        -webkit-appearance: none;
    }

    /**
     * 1. Correct the inability to style upload buttons in iOS and Safari.
     * 2. Change font properties to `inherit` in Safari.
     */
    ::-webkit-file-upload-button {
        -webkit-appearance: button; /* 1 */
        font: inherit; /* 2 */
    }

    /**
     * Remove the inner border and padding of focus outlines in Firefox.
     */
    ::-moz-focus-inner {
        border-style: none;
        padding: 0;
    }

    /**
     * Restore the focus outline styles unset by the previous rule in Firefox.
     */
    :-moz-focusring {
        outline: 1px dotted ButtonText;
    }

    /**
     * Remove the additional :invalid styles in Firefox.
     */
    :-moz-ui-invalid {
        box-shadow: none;
    }

    /* Interactive
     * ========================================================================== *//*
     * Add the correct display in Edge 18- and IE.
     */
    details {
        display: block;
    }

    /*
     * Add the correct styles in Edge 18-, IE, and Safari.
     */
    dialog {
        background-color: white;
        border: solid;
        color: black;
        display: block;
        height: -moz-fit-content;
        height: -webkit-fit-content;
        height: fit-content;
        left: 0;
        margin: auto;
        padding: 1em;
        position: absolute;
        right: 0;
        width: -moz-fit-content;
        width: -webkit-fit-content;
        width: fit-content;
    }

    dialog:not([open]) {
        display: none;
    }

    /*
     * Add the correct display in all browsers.
     */
    summary {
        display: list-item;
    }

    /* Scripting
     * ========================================================================== *//**
     * Add the correct display in IE.
     */
    template {
        display: none;
    }

    /* User interaction
     * ========================================================================== *//*
     * 1. Remove the tapping delay in IE 10.
     * 2. Remove the tapping delay on clickable elements
          in all browsers (opinionated).
     */
    a,
    area,
    button,
    input,
    label,
    select,
    summary,
    textarea,
    [tabindex] {
        -ms-touch-action: manipulation; /* 1 */
        touch-action: manipulation; /* 2 */
    }

    /* Accessibility
     * ========================================================================== *//**
     * Change the cursor on busy elements in all browsers (opinionated).
     */
    [aria-busy="true"] {
        cursor: progress;
    }

    /*
     * Change the cursor on control elements in all browsers (opinionated).
     */
    [aria-controls] {
        cursor: pointer;
    }

    /*
     * Change the cursor on disabled, not-editable, or otherwise
     * inoperable elements in all browsers (opinionated).
     */
    [aria-disabled="true"],
    [disabled] {
        cursor: not-allowed;
    }

    /*
     * Change the display on visually hidden accessible elements
     * in all browsers (opinionated).
     */
    [aria-hidden="false"][hidden] {
        display: initial;
    }

    [aria-hidden="false"][hidden]:not(:focus) {
        clip: rect(0, 0, 0, 0);
        position: absolute;
    }

    body {
        background-color: #f5f6f7;
        color: #444a4b
    }

    #base-layout {
        font-size: 16px;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
        -webkit-font-smoothing: antialiased;
        padding: 30px 0;
        background-color: #f5f6f7
    }

    #base-layout > .main, #base-layout > .footer {
        max-width: 600px;
        margin: 0 auto
    }

    #base-layout > .main {
        background-color: #ffffff;
        border-radius: 10px
    }

    #base-layout > .footer {
        padding: 30px 40px;
        font-size: 13px;
        text-align: center;
        color: #7a869a
    }

    #base-layout > .footer .links {
        margin-top: 20px
    }

    #base-layout > .footer .links a + a {
        margin-left: 12px
    }

    #base-layout > .footer p {
        text-align: center;
        max-width: 300px;
        margin-left: auto;
        margin-right: auto
    }

    #base-layout ul {
        list-style: none;
        margin: 0;
        padding: 0
    }

    #base-layout h1, #base-layout h2, #base-layout h3, #base-layout h4, #base-layout li, #base-layout p {
        margin: 0
    }

    #base-layout p {
        text-align: left;
        line-height: 1.5;
        color: #444a4b
    }

    #base-layout p + p, #base-layout p + a, #base-layout p + div {
        margin-top: 16px
    }

    #base-layout a {
        color: #444a4b
    }

    #base-layout .action {
        display: block;
        padding: 20px;
        margin: 30px 0;
        text-align: center;
        font-size: 18px;
        text-decoration: none;
        font-weight: 800;
        border-radius: 10px;
        background-color: #f49652;
        color: #ffffff;
        box-shadow: 0 13px 10px -10px rgba(239, 102, 6, 0.5)
    }

    #receipt-template {
        background-color: #fff
    }

    #receipt-template table {
        width: 100%;
        border: 0;
        border-spacing: 0
    }

    #receipt-template > .header, #receipt-template > .summary, #receipt-template > .details {
        padding: 30px
    }

    #receipt-template > .header .header-summary {
        vertical-align: top;
        text-align: right;
        font-size: 12px
    }

    #receipt-template > .summary {
        background-color: #ff5757
    }

    #receipt-template > .summary .name {
        font-size: 20px;
        font-weight: 600;
        line-height: 1;
        margin-bottom: 5px;
        color: #e0dfdc
    }

    #receipt-template > .summary .payee-document {
        font-size: 14px;
        font-weight: 300;
        color: #e0dfdc;
        line-height: 1
    }

    #receipt-template > .summary .amount {
        text-align: right;
        vertical-align: top;
        font-size: 22px;
        font-weight: 800;
        color: #e0dfdc
    }

    #receipt-template > .details {
        padding-top: 10px
    }

    #receipt-template > .details .label {
        font-size: 13px;
        font-weight: 400
    }

    #receipt-template > .details .value {
        font-size: 14px;
        padding: 10px 0;
        font-weight: 300;
        text-align: right;
        color: #444a4b
    }

    #receipt-template > .details .info {
        display: table;
        margin-top: 10px;
        border-top: 1px solid #c6cdd3;
        padding-top: 20px;
        width: 100%;
        opacity: 0.7
    }

    #receipt-template > .details .info span {
        display: table-cell;
        vertical-align: middle;
        font-size: 13px;
        font-style: italic;
        text-align: center
    }

    #receipt-template > .details .column {
        display: block;
        text-align: left
    }

    </style>
</head>

<body style="box-sizing: border-box; margin: 0; background-color: #f5f6f7; color: #444a4b;">
<div id="base-layout"
     style="box-sizing: border-box; font-size: 16px; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI','Roboto','Oxygen','Ubuntu','Cantarell','Fira Sans','Droid Sans','Helvetica Neue',sans-serif; -webkit-font-smoothing: antialiased; padding: 30px 0; background-color: #f5f6f7;">
    <div class="main"
         style="box-sizing: border-box; max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 10px;">
        <div id="receipt-template" style="box-sizing: border-box; background-color: #fff;">
            <div class="header" style="box-sizing: border-box; padding: 30px;">
                <table style="box-sizing: border-box; border-collapse: collapse; width: 100%; border: 0; border-spacing: 0;"
                       width="100%">
                    <tr style="box-sizing: border-box;">
                        <td style="box-sizing: border-box;">
                            <img alt="Me Poupe!" style="box-sizing: border-box; vertical-align: middle; width: 112px"
                                src="https://notification-templates-cdn.mepoupe.app/static/logo-purple.d72e31c09b38ae46fb2772999c3febbb.png"/>
                        </td>
                        <td class="header-summary"
                            style="box-sizing: border-box; vertical-align: top; text-align: right; font-size: 12px;"
                            valign="top" align="right">
                            <div style="box-sizing: border-box;"><strong
                                    style="box-sizing: border-box; font-weight: bolder;">{{totalAmount}}</strong></div>
                            <div style="box-sizing: border-box;">{{date}}</div>
                        </td>
                    </tr>
                </table>
            </div>

            <div class="summary" style="box-sizing: border-box; padding: 30px; background-color: #F1EFFF;">
                <table style="box-sizing: border-box; border-collapse: collapse; width: 100%; border: 0; border-spacing: 0;"
                       width="100%">
                    <tr style="box-sizing: border-box;">
                        <td style="box-sizing: border-box;">
                            <div class="name"
                                 style="box-sizing: border-box; font-size: 20px; font-weight: 600; line-height: 1; margin-bottom: 5px; color: #372797;">{{recipientName}}</div>
                        </td>
                        <td class="amount"
                            style="box-sizing: border-box; text-align: right; vertical-align: top; font-size: 22px; font-weight: 800; color: #372797;"
                            align="right" valign="top">{{totalAmount}}</td>
                    </tr>
                </table>
            </div>
            <div class="details" style="box-sizing: border-box; padding: 30px; padding-top: 10px;">
                <table style="box-sizing: border-box; border-collapse: collapse; width: 100%; border: 0; border-spacing: 0;"
                       width="100%">
                    <tr class="column" style="box-sizing: border-box; display: block; text-align: left;"
                        align="left">
                        <td class="label" style="box-sizing: border-box; font-size: 13px; font-weight: 400;">
                            Status
                        </td>
                        <td class="value column"
                            style="box-sizing: border-box; font-size: 14px; padding: 10px 0; font-weight: 300; color: #444a4b; display: block; text-align: left;"
                            align="left">
                            {{#if success }}
                                Resgate realizado com sucesso
                            {{/if}}
                            {{#if failed }}
                                Resgate não realizado
                            {{/if}}
                            {{#if partialFailed }}
                                Resgate parcialmente realizado
                            {{/if}}
                        </td>
                    </tr>

                    {{#if partialFailed }}
                        <tr class="column" style="box-sizing: border-box; display: block; text-align: left;"
                            align="left">
                            <td class="label" style="box-sizing: border-box; font-size: 13px; font-weight: 400;">
                                Detalhes
                            </td>
                            <td class="value column"
                                style="box-sizing: border-box; font-size: 14px; padding: 10px 0; font-weight: 300; color: #444a4b; display: block; text-align: left;"
                                align="left">
                                Apenas {{partialSuccessAmount}} de {{totalAmount}} foram resgatados.
                            </td>
                        </tr>
                    {{/if}}
                </table>
            </div>
        </div>
    </div>
    <div class="footer"
         style="box-sizing: border-box; max-width: 600px; margin: 0 auto; padding: 30px 40px; font-size: 13px; text-align: center; color: #7a869a;">
        <p style="box-sizing: border-box; margin: 0; line-height: 1.5; color: #444a4b; text-align: center; max-width: 300px; margin-left: auto; margin-right: auto;">
            Me Poupe! &#169; 2024 Todos os direitos reservados.</p>
        <p style="box-sizing: border-box; margin: 0; line-height: 1.5; color: #444a4b; margin-top: 16px; text-align: center; max-width: 300px; margin-left: auto; margin-right: auto;">
            Me Poupe Pagamentos Digitais est&aacute; inscrita no CNPJ 56.152.016/0001-74.</p>
        <div class="links" style="box-sizing: border-box; margin-top: 20px;">
            <a href="https://use.mepoupe.app/" target="_blank" rel="noreferrer noopener"
               style="box-sizing: border-box; -ms-touch-action: manipulation; touch-action: manipulation; color: #444a4b;">Acessar
                sua conta</a>
            <a href="https://me-poupe-contas-bill-payment-public-production.s3.amazonaws.com/current/Me_Poupe_Politica_de_Privacidade.pdf" target="_blank" rel="noreferrer noopener"
               style="box-sizing: border-box; -ms-touch-action: manipulation; touch-action: manipulation; color: #444a4b; margin-left: 12px;">Pol&iacute;tica
                de privacidade</a>
        </div>
    </div>
</div>
</body>

</html>