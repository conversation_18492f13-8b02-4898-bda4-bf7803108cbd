<!DOCTYPE html>
<html lang="pt-BR" style="box-sizing: border-box; cursor: default; line-height: 1.5; -moz-tab-size: 4; tab-size: 4; -webkit-tap-highlight-color: transparent; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; word-break: break-word;">

<head>
    <title>Friday</title>
    <meta charset="utf-8">
    <style type="text/css">
*,
::before,
::after {
	box-sizing: border-box;
}
::before,
::after {
	text-decoration: inherit; /* 1 */
	vertical-align: inherit; /* 2 */
}
html {
	cursor: default; /* 1 */
	line-height: 1.5; /* 2 */
	-moz-tab-size: 4; /* 3 */
	tab-size: 4; /* 3 */
	-webkit-tap-highlight-color: transparent /* 4 */;
	-ms-text-size-adjust: 100%; /* 5 */
	-webkit-text-size-adjust: 100%; /* 5 */
	word-break: break-word; /* 6 */
}
body {
	margin: 0;
}
h1 {
	font-size: 2em;
	margin: 0.67em 0;
}
dl dl,
dl ol,
dl ul,
ol dl,
ul dl {
	margin: 0;
}
ol ol,
ol ul,
ul ol,
ul ul {
	margin: 0;
}
hr {
	height: 0; /* 1 */
	overflow: visible; /* 2 */
}
main {
	display: block;
}
nav ol,
nav ul {
	list-style: none;
	padding: 0;
}
pre {
	font-family: monospace, monospace; /* 1 */
	font-size: 1em; /* 2 */
}
abbr[title] {
	text-decoration: underline;
	text-decoration: underline dotted;
}
b,
strong {
	font-weight: bolder;
}
code,
kbd,
samp {
	font-family: monospace, monospace; /* 1 */
	font-size: 1em; /* 2 */
}
small {
	font-size: 80%;
}
audio,
canvas,
iframe,
img,
svg,
video {
	vertical-align: middle;
}
iframe {
	border-style: none;
}
svg:not([fill]) {
	fill: currentColor;
}
svg:not(:root) {
	overflow: hidden;
}
table {
	border-collapse: collapse;
}
button,
input,
select {
	margin: 0;
}
button {
	overflow: visible; /* 1 */
	text-transform: none; /* 2 */
}
button,
[type="button"],
[type="reset"],
[type="submit"] {
	-webkit-appearance: button;
}
fieldset {
	border: 1px solid #a0a0a0; /* 1 */
	padding: 0.35em 0.75em 0.625em; /* 2 */
}
input {
	overflow: visible;
}
legend {
	color: inherit; /* 2 */
	display: table; /* 1 */
	max-width: 100%; /* 1 */
	white-space: normal; /* 1 */
}
progress {
	display: inline-block; /* 1 */
	vertical-align: baseline; /* 2 */
}
select {
	text-transform: none;
}
textarea {
	margin: 0; /* 1 */
	overflow: auto; /* 2 */
	resize: vertical; /* 3 */
}
[type="search"] {
	-webkit-appearance: textfield; /* 1 */
	outline-offset: -2px; /* 2 */
}
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
	height: auto;
}
::-webkit-input-placeholder {
	color: inherit;
	opacity: 0.54;
}
::-webkit-search-decoration {
	-webkit-appearance: none;
}
::-webkit-file-upload-button {
	-webkit-appearance: button; /* 1 */
	font: inherit; /* 2 */
}
::-moz-focus-inner {
	border-style: none;
	padding: 0;
}
:-moz-focusring {
	outline: 1px dotted ButtonText;
}
:-moz-ui-invalid {
	box-shadow: none;
}
details {
	display: block;
}
dialog {
	background-color: white;
	border: solid;
	color: black;
	display: block;
	height: -moz-fit-content;
	height: -webkit-fit-content;
	height: fit-content;
	left: 0;
	margin: auto;
	padding: 1em;
	position: absolute;
	right: 0;
	width: -moz-fit-content;
	width: -webkit-fit-content;
	width: fit-content;
}
dialog:not([open]) {
	display: none;
}
summary {
	display: list-item;
}
template {
	display: none;
}
a,
area,
button,
input,
label,
select,
summary,
textarea,
[tabindex] {
	-ms-touch-action: manipulation; /* 1 */
	touch-action: manipulation; /* 2 */
}
[aria-busy="true"] {
	cursor: progress;
}
[aria-controls] {
	cursor: pointer;
}
[aria-disabled="true"],
[disabled] {
	cursor: not-allowed;
}
[aria-hidden="false"][hidden] {
	display: initial;
}
[aria-hidden="false"][hidden]:not(:focus) {
	clip: rect(0, 0, 0, 0);
	position: absolute;
}
#forwarded-message {
	background-color: #ffffff;
	font-size: 12px;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
		"Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
		sans-serif;
	-webkit-font-smoothing: antialiased;
	margin: 0 auto;
	padding: 20px 0;
	color: #1e273e;
}
#forwarded-message .header {
	margin: 0 auto 10px auto;
	padding: 0 20px;
}
#forwarded-message .header .logo {
	width: 65px;
}
#forwarded-message .header .logo img {
	height: 30px;
	margin-right: 20px;
}
#forwarded-message .header .title {
	text-align: center;
	font-weight: 400;
	font-size: 13px;
}
#forwarded-message .header .title strong {
	font-weight: 400;
	text-decoration: underline;
}
#forwarded-message .body {
	padding: 10px 20px;
	border-top: 1px dotted #cccccc;
	text-align: center;
	font-weight: 300;
}
#forwarded-message .body p {
	margin: 4px 0;
}
#forwarded-message .body strong {
	font-weight: 400;
	text-decoration: underline;
}
#forwarded-message .disclaimer {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
		"Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
		sans-serif;
	background-color: #ffffff;
	font-weight: 300;
	color: #666666;
	display: block;
	font-size: 12px;
	text-align: center;
	padding: 8px 0;
	border-top: 1px dotted #cccccc;
	border-bottom: 1px dotted #cccccc;
}
    </style></head>

<body style="box-sizing: border-box; margin: 0;">
<div id="forwarded-message" style="box-sizing: border-box; background-color: #ffffff; font-size: 12px; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI','Roboto','Oxygen','Ubuntu','Cantarell','Fira Sans','Droid Sans','Helvetica Neue',sans-serif; -webkit-font-smoothing: antialiased; margin: 0 auto; padding: 20px 0; color: #1e273e;">
    <table class="header" style="box-sizing: border-box; border-collapse: collapse; margin: 0 auto 10px auto; padding: 0 20px;">
        <tr style="box-sizing: border-box;">
            <td class="logo" style="box-sizing: border-box; width: 65px;" width="65"><img src="https://notification-templates-cdn.via1.app/static/logo.0606995011c1f868831172a12fadaa9a.png" alt="Friday" style="box-sizing: border-box; vertical-align: middle; height: 30px; margin-right: 20px;" height="30"></td>
            <td class="title" style="box-sizing: border-box; text-align: center; font-weight: 400; font-size: 13px;" align="center">Esta mensagem foi enviada por <strong style="box-sizing: border-box; font-weight: 400; text-decoration: underline;">{{sender}}</strong></td>
        </tr>
    </table>
    <div class="body" style="box-sizing: border-box; padding: 10px 20px; border-top: 1px dotted #cccccc; text-align: center; font-weight: 300;">
        <p style="box-sizing: border-box; margin: 4px 0;">A mensagem abaixo foi enviada por <strong style="box-sizing: border-box; font-weight: 400; text-decoration: underline;">{{sender}}</strong> para sua caixa postal Friday, e nós
            estamos
            te encaminhando para que
            você não fique por fora de nada.
        </p>
        <p style="box-sizing: border-box; margin: 4px 0;">Se houver alguma conta na mensagem, não se preocupe. A gente já está cuidando dela por você.</p>
    </div>
    <div class="disclaimer" style="box-sizing: border-box; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI','Roboto','Oxygen','Ubuntu','Cantarell','Fira Sans','Droid Sans','Helvetica Neue',sans-serif; background-color: #ffffff; font-weight: 300; color: #666666; display: block; font-size: 12px; text-align: center; padding: 8px 0; border-top: 1px dotted #cccccc; border-bottom: 1px dotted #cccccc;">O conteúdo desta mensagem é de total responsabilidade de seu remetente.</div>
</div>

<div class="content" style="box-sizing: border-box;">
    {{original}}
</div>
</body>

</html>
