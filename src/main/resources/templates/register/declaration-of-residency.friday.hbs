<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <title>Friday - Declaração de residência</title>
    <meta charset="utf-8"/>
    <meta name="color-scheme" content="only"/>
    <meta name="supported-color-schemes" content="only"/>
    <style type="text/css">body {
        padding: 0;
        margin: 0;
        background-color: #ffffff;
        color: #444a4b
    }

    #base-layout {
        font-size: 16px;
        font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
        -webkit-font-smoothing: antialiased;
        padding: 2cm 1cm
    }

    #base-layout header {
        display: block;
        overflow: hidden;
        margin-bottom: 40px
    }

    #base-layout header img {
        height: 60px;
        float: left
    }

    #base-layout header h1 {
        float: right;
        font-size: 20px;
        font-weight: 800;
        color: #444a4b;
        text-transform: uppercase;
        margin-top: 20px
    }

    #base-layout section {
        display: block
    }

    #base-layout section + section {
        margin-top: 20px
    }

    #base-layout address {
        display: block;
        font-weight: 600
    }

    #base-layout .quote {
        margin-left: 10px;
        padding-left: 30px;
        font-style: italic;
        border-left: 4px solid #687189
    }

    #base-layout .signature {
        display: inline-block;
        border: 3px solid #687189;
        background-color: #f6f6f6;
        border-radius: 10px;
        padding: 15px;
        margin-top: 30px;
        overflow: hidden
    }

    #base-layout .signature h2 {
        font-size: 16px;
        margin-bottom: 10px
    }

    #base-layout .signature p {
        font-style: italic;
        white-space: nowrap
    }

    #base-layout .signature span {
        display: inline-block;
        font-style: normal;
        font-weight: 500;
        font-size: 14px;
        min-width: 110px
    }

    #base-layout h1, #base-layout h2, #base-layout h3, #base-layout h4, #base-layout li, #base-layout p {
        margin: 0
    }

    #base-layout p {
        text-align: justify;
        line-height: 1.5;
        color: #444a4b
    }

    </style>
</head>

<body style="padding: 0; margin: 0; background-color: #ffffff; color: #444a4b;">
<div id="base-layout"
     style="font-size: 16px; font-family: 'Poppins',-apple-system,BlinkMacSystemFont,'Segoe UI','Roboto','Oxygen','Ubuntu','Cantarell','Fira Sans','Droid Sans','Helvetica Neue',sans-serif; -webkit-font-smoothing: antialiased; padding: 2cm 1cm;">
    <header style="display: block; overflow: hidden; margin-bottom: 40px;">
        <img src="data:image/png;base64,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"
             alt="Friday" style="height: 60px; float: left;" height="60"/>
        <h1 style="margin: 0; float: right; font-size: 20px; font-weight: 800; color: #444a4b; text-transform: uppercase; margin-top: 20px;">
            Declaração de residência</h1>
    </header>
    <section style="display: block;">
        <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b;">
            Eu, <strong>{{fullName}}, inscrito no CPF sob o n.º {{document}}</strong>, declaro para os devidos fins e
            sob as penas da legislação brasileira em vigor, com a finalidade de realizar prova de residência junto ao
            <strong>BANCO ARBI S/A, inscrito no CNPJ sob o n.º 54.403.563/0001-50</strong>, ser residente e domiciliado
            no endereço abaixo:
        </p>
    </section>
    <section style="display: block; margin-top: 20px;">
        <address style="display: block; font-weight: 600;">
            <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b;">{{fullAddress}}</p>
            <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b;">{{zipCode}} - {{city}}
                / {{federalUnity}}</p>
        </address>
    </section>
    <section style="display: block; margin-top: 20px;">
        <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b;">
            Em conformidade ao disposto nos artigos 2º e 3º da Lei 7.115/1983, declaro ainda, estar ciente de que a
            ausência de autenticidade das informações ora prestadas poderá ensejar a aplicação de sanções cíveis,
            administrativas e penais prevista na legislação brasileira em vigor, inclusive aquelas referentes a
            falsidade prevista no Código Penal Brasileiro:
        </p>
    </section>
    <section class="quote"
             style="display: block; margin-top: 20px; margin-left: 10px; padding-left: 30px; font-style: italic; border-left: 4px solid #687189;">
        <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b;">
            Art. 299: Omitir, em documento público ou particular, declaração que nele deveria constar, ou nele inserir
            ou fazer inserir declaração falsa ou diversa da que devia ser escrita, com o fim de prejudicar direito,
            criar obrigação ou alterar a verdade sobre o fato juridicamente relevante, pena de reclusão de 1 (um) a 5
            (cinco) anos e multa, se o documento é público e reclusão de 1 (um) a 3 (três) anos, se o documento é
            particular.
        </p>
    </section>
    <section style="display: block; margin-top: 20px;">
        <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b;">
            Por ser verdade e de minha responsabilidade todas as declarações ora prestadas, dato e assino a presente
            declaração.
        </p>
    </section>
    <section style="display: block; margin-top: 20px;">
        <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b;">{{city}}, {{signature.date}}.</p>

        <div class="signature"
             style="display: inline-block; border: 3px solid #687189; background-color: #f6f6f6; border-radius: 10px; padding: 15px; margin-top: 30px; overflow: hidden;">
            <h2 style="margin: 0; font-size: 16px; margin-bottom: 10px;">Assinado digitalmente
                em {{signature.shortDate}} por:</h2>
            <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b; font-style: italic; white-space: nowrap;">
                <span style="display: inline-block; font-style: normal; font-weight: 500; font-size: 14px; min-width: 110px;">Nome:</span> {{fullName}}
            </p>
            <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b; font-style: italic; white-space: nowrap;">
                <span style="display: inline-block; font-style: normal; font-weight: 500; font-size: 14px; min-width: 110px;">Email:</span> {{signature.email}}
            </p>
            <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b; font-style: italic; white-space: nowrap;">
                <span style="display: inline-block; font-style: normal; font-weight: 500; font-size: 14px; min-width: 110px;">Telefone:</span> {{signature.phone}}
            </p>
            <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b; font-style: italic; white-space: nowrap;">
                <span style="display: inline-block; font-style: normal; font-weight: 500; font-size: 14px; min-width: 110px;">CPF:</span> {{document}}
            </p>
            <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b; font-style: italic; white-space: nowrap;">
                <span style="display: inline-block; font-style: normal; font-weight: 500; font-size: 14px; min-width: 110px;">Endereço IP:</span> {{signature.clientIP}}
            </p>
            <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b; font-style: italic; white-space: nowrap;">
                <span style="display: inline-block; font-style: normal; font-weight: 500; font-size: 14px; min-width: 110px;">Autenticação:</span> {{signature.key}}
            </p>
        </div>
    </section>
</div>
</body>

</html>