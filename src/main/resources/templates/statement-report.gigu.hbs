<!DOCTYPE html>
<html lang="pt-BR" style="box-sizing: border-box; cursor: default; line-height: 1.5; -moz-tab-size: 4; tab-size: 4; -webkit-tap-highlight-color: transparent; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; word-break: break-word;">

<head>
    <title>Gigu</title>
    <meta charset="utf-8"/>
    <meta name="color-scheme" content="only"/>
    <meta name="supported-color-schemes" content="only"/>
    <style type="text/css">/* Document
 * ========================================================================== *//**
 * Add border box sizing in all browsers (opinionated).
 */*,
::before,
::after {
  box-sizing: border-box;
}/**
 * 1. Add text decoration inheritance in all browsers (opinionated).
 * 2. Add vertical alignment inheritance in all browsers (opinionated).
 */::before,
::after {
  text-decoration: inherit; /* 1 */
  vertical-align: inherit; /* 2 */
}/**
 * 1. Use the default cursor in all browsers (opinionated).
 * 2. Change the line height in all browsers (opinionated).
 * 3. Use a 4-space tab width in all browsers (opinionated).
 * 4. Remove the grey highlight on links in iOS (opinionated).
 * 5. Prevent adjustments of font size after orientation changes in
 *    IE on Windows Phone and in iOS.
 * 6. Breaks words to prevent overflow in all browsers (opinionated).
 */html {
  cursor: default; /* 1 */
  line-height: 1.5; /* 2 */
  -moz-tab-size: 4; /* 3 */
  tab-size: 4; /* 3 */
  -webkit-tap-highlight-color: transparent /* 4 */;
  -ms-text-size-adjust: 100%; /* 5 */
  -webkit-text-size-adjust: 100%; /* 5 */
  word-break: break-word; /* 6 */
}/* Sections
 * ========================================================================== *//**
 * Remove the margin in all browsers (opinionated).
 */body {
  margin: 0;
}/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Edge, Firefox, and Safari.
 */h1 {
  font-size: 2em;
  margin: 0.67em 0;
}/* Grouping content
 * ========================================================================== *//**
 * Remove the margin on nested lists in Chrome, Edge, IE, and Safari.
 */dl dl,
dl ol,
dl ul,
ol dl,
ul dl {
  margin: 0;
}/**
 * Remove the margin on nested lists in Edge 18- and IE.
 */ol ol,
ol ul,
ul ol,
ul ul {
  margin: 0;
}/**
 * 1. Add the correct sizing in Firefox.
 * 2. Show the overflow in Edge 18- and IE.
 */hr {
  height: 0; /* 1 */
  overflow: visible; /* 2 */
}/**
 * Add the correct display in IE.
 */main {
  display: block;
}/**
 * Remove the list style on navigation lists in all browsers (opinionated).
 */nav ol,
nav ul {
  list-style: none;
  padding: 0;
}/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */pre {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}/* Text-level semantics
 * ========================================================================== *//**
 * Add the correct text decoration in Edge 18-, IE, and Safari.
 */abbr[title] {
  text-decoration: underline;
  text-decoration: underline dotted;
}/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */b,
strong {
  font-weight: bolder;
}/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */code,
kbd,
samp {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}/**
 * Add the correct font size in all browsers.
 */small {
  font-size: 80%;
}/* Embedded content
 * ========================================================================== *//*
 * Change the alignment on media elements in all browsers (opinionated).
 */audio,
canvas,
iframe,
img,
svg,
video {
  vertical-align: middle;
}/**
 * Remove the border on iframes in all browsers (opinionated).
 */iframe {
  border-style: none;
}/**
 * Change the fill color to match the text color in all browsers (opinionated).
 */svg:not([fill]) {
  fill: currentColor;
}/**
 * Hide the overflow in IE.
 */svg:not(:root) {
  overflow: hidden;
}/* Tabular data
 * ========================================================================== *//**
 * Collapse border spacing in all browsers (opinionated).
 */table {
  border-collapse: collapse;
}/* Forms
 * ========================================================================== *//**
 * Remove the margin on controls in Safari.
 */button,
input,
select {
  margin: 0;
}/**
 * 1. Show the overflow in IE.
 * 2. Remove the inheritance of text transform in Edge 18-, Firefox, and IE.
 */button {
  overflow: visible; /* 1 */
  text-transform: none; /* 2 */
}/**
 * Correct the inability to style buttons in iOS and Safari.
 */button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}/**
 * 1. Change the inconsistent appearance in all browsers (opinionated).
 * 2. Correct the padding in Firefox.
 */fieldset {
  border: 1px solid #a0a0a0; /* 1 */
  padding: 0.35em 0.75em 0.625em; /* 2 */
}/**
 * Show the overflow in Edge 18- and IE.
 */input {
  overflow: visible;
}/**
 * 1. Correct the text wrapping in Edge 18- and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 */legend {
  color: inherit; /* 2 */
  display: table; /* 1 */
  max-width: 100%; /* 1 */
  white-space: normal; /* 1 */
}/**
 * 1. Add the correct display in Edge 18- and IE.
 * 2. Add the correct vertical alignment in Chrome, Edge, and Firefox.
 */progress {
  display: inline-block; /* 1 */
  vertical-align: baseline; /* 2 */
}/**
 * Remove the inheritance of text transform in Firefox.
 */select {
  text-transform: none;
}/**
 * 1. Remove the margin in Firefox and Safari.
 * 2. Remove the default vertical scrollbar in IE.
 * 3. Change the resize direction in all browsers (opinionated).
 */textarea {
  margin: 0; /* 1 */
  overflow: auto; /* 2 */
  resize: vertical; /* 3 */
}/**
 * 1. Correct the odd appearance in Chrome, Edge, and Safari.
 * 2. Correct the outline style in Safari.
 */[type="search"] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}/**
 * Correct the cursor style of increment and decrement buttons in Safari.
 */::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}/**
 * Correct the text style of placeholders in Chrome, Edge, and Safari.
 */::-webkit-input-placeholder {
  color: inherit;
  opacity: 0.54;
}/**
 * Remove the inner padding in Chrome, Edge, and Safari on macOS.
 */::-webkit-search-decoration {
  -webkit-appearance: none;
}/**
 * 1. Correct the inability to style upload buttons in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}/**
 * Remove the inner border and padding of focus outlines in Firefox.
 */::-moz-focus-inner {
  border-style: none;
  padding: 0;
}/**
 * Restore the focus outline styles unset by the previous rule in Firefox.
 */:-moz-focusring {
  outline: 1px dotted ButtonText;
}/**
 * Remove the additional :invalid styles in Firefox.
 */:-moz-ui-invalid {
  box-shadow: none;
}/* Interactive
 * ========================================================================== *//*
 * Add the correct display in Edge 18- and IE.
 */details {
  display: block;
}/*
 * Add the correct styles in Edge 18-, IE, and Safari.
 */dialog {
  background-color: white;
  border: solid;
  color: black;
  display: block;
  height: -moz-fit-content;
  height: -webkit-fit-content;
  height: fit-content;
  left: 0;
  margin: auto;
  padding: 1em;
  position: absolute;
  right: 0;
  width: -moz-fit-content;
  width: -webkit-fit-content;
  width: fit-content;
}dialog:not([open]) {
  display: none;
}/*
 * Add the correct display in all browsers.
 */summary {
  display: list-item;
}/* Scripting
 * ========================================================================== *//**
 * Add the correct display in IE.
 */template {
  display: none;
}/* User interaction
 * ========================================================================== *//*
 * 1. Remove the tapping delay in IE 10.
 * 2. Remove the tapping delay on clickable elements
      in all browsers (opinionated).
 */a,
area,
button,
input,
label,
select,
summary,
textarea,
[tabindex] {
  -ms-touch-action: manipulation; /* 1 */
  touch-action: manipulation; /* 2 */
}/* Accessibility
 * ========================================================================== *//**
 * Change the cursor on busy elements in all browsers (opinionated).
 */[aria-busy="true"] {
  cursor: progress;
}/*
 * Change the cursor on control elements in all browsers (opinionated).
 */[aria-controls] {
  cursor: pointer;
}/*
 * Change the cursor on disabled, not-editable, or otherwise
 * inoperable elements in all browsers (opinionated).
 */[aria-disabled="true"],
[disabled] {
  cursor: not-allowed;
}/*
 * Change the display on visually hidden accessible elements
 * in all browsers (opinionated).
 */[aria-hidden="false"][hidden] {
  display: initial;
}[aria-hidden="false"][hidden]:not(:focus) {
  clip: rect(0, 0, 0, 0);
  position: absolute;
}body{background-color:#f5f6f7;color:#444a4b}#new-base-layout{font-size:16px;font-family:Arial;-webkit-font-smoothing:antialiased;padding:30px 0;background-color:#f5f6f7}#new-base-layout>.main,#new-base-layout>.footer{max-width:600px;margin:0 auto}#new-base-layout>.main{background-color:#fff}#new-base-layout>.footer{background:#FF5900;padding:30px 40px;font-size:13px;text-align:center}#new-base-layout>.footer .links{margin-top:20px}#new-base-layout>.footer .links a+a{margin-left:12px}#new-base-layout>.footer p{font-size:12px;text-align:center;max-width:500px;margin-left:auto;margin-right:auto;color:#fff}#new-base-layout>.footer .social{font-weight:700;font-size:14px;line-height:24px}#new-base-layout>.footer .social-images{margin-bottom:1rem}#new-base-layout>.footer a+a{margin-left:1rem}#new-base-layout>.footer .link{margin-top:1rem !important;color:#fff}#new-base-layout h1,#new-base-layout h2,#new-base-layout h3,#new-base-layout h4,#new-base-layout li,#new-base-layout p{margin:0}#new-base-layout p{text-align:left;line-height:1.5;color:#444a4b}#new-base-layout p+p,#new-base-layout p+a,#new-base-layout p+div{margin-top:16px}#new-message-layout{padding:30px 40px}#new-message-layout>.header{margin-bottom:30px}#new-message-layout>.signature{margin-top:30px}#new-message-layout>.signature>.company-name{font-weight:bold;color:#ff5757}.title{font-size:1.5rem;font-weight:700;line-height:24px;color:#475569 !important}.title span{color:#FF5900}.paragraph{color:#475569 !important;font-size:16px;font-weight:400;line-height:24px}.paragraph span{font-weight:700}.paragraph .link{font-weight:700;text-decoration:underline;color:#FF5900 !important}
    </style>
</head>

<body style="box-sizing: border-box; margin: 0; background-color: #f5f6f7; color: #444a4b;">
<div id="new-base-layout" style="box-sizing: border-box; font-size: 16px; font-family: Arial; -webkit-font-smoothing: antialiased; padding: 30px 0; background-color: #f5f6f7;">
    <div class="main" style="box-sizing: border-box; max-width: 600px; margin: 0 auto; background-color: #fff;">
        <div id="new-message-layout" style="box-sizing: border-box; padding: 30px 40px;">
            <div class="header" style="box-sizing: border-box; margin-bottom: 30px;">
                <img src="https://notification-templates-cdn.via1.app/static/gigu-conta-logo.png" alt="Gigu" style="width: 150px; height: auto; display: block; margin: 0;"/>
            </div>
            <div class="content" style="box-sizing: border-box;">
                <div id="statement-report" style="box-sizing: border-box;">
                    <p class="title" style="box-sizing: border-box; font-size: 1.5rem; font-weight: 700; margin: 0; text-align: left; line-height: 1.5; color: #475569;">Oi, <span style="box-sizing: border-box; color: #FF5900;">{{name}}</span></p>
                    <p class="paragraph" style="box-sizing: border-box; font-size: 16px; font-weight: 400; margin: 0; text-align: left; line-height: 1.5; margin-top: 16px; color: #475569;">O extrato é o descritivo das movimentações financeiras feitas na conta Gigu.</p>
                    <p class="paragraph" style="box-sizing: border-box; font-size: 16px; font-weight: 400; margin: 0; text-align: left; line-height: 1.5; margin-top: 16px; color: #475569;"><span style="box-sizing: border-box; font-weight: 700;">Baixe o PDF em anexo</span> as transações do período de <span style="box-sizing: border-box; font-weight: 700;">{{periodMessage}}.</span></p>
                    <p class="paragraph" style="box-sizing: border-box; font-size: 16px; font-weight: 400; margin: 0; text-align: left; line-height: 1.5; margin-top: 16px; color: #475569;">Até o próximo mês!</p>
                </div>
            </div>
            <div class="signature" style="box-sizing: border-box; margin-top: 30px;">
                <p style="box-sizing: border-box; margin: 0; text-align: left; line-height: 1.5; color: #444a4b;">Até logo,</p>
                <p class="company-name" style="box-sizing: border-box; margin: 0; text-align: left; line-height: 1.5; margin-top: 16px; font-weight: bold; color: #ff5757;">Equipe Gigu</p>
            </div>
        </div>
    </div>
    <div class="footer" style="box-sizing: border-box; max-width: 600px; margin: 0 auto; background: #FF5900; padding: 30px 40px; font-size: 13px; text-align: center;">
        <p class="social" style="box-sizing: border-box; margin: 0; text-align: center; max-width: 500px; margin-left: auto; margin-right: auto; color: #fff; font-weight: 700; font-size: 14px; line-height: 24px;">Acesse nossas redes sociais</p>
        <div class="social-images" style="box-sizing: border-box; margin-top: 16px; margin-bottom: 1rem;">
            <a href="https://www.instagram.com/gigu/" target="_blank" style="box-sizing: border-box; -ms-touch-action: manipulation; touch-action: manipulation;"><img src="https://notification-templates-cdn.via1.app/static/instagram.bf3927131fb9cda354b8eb752ea71901.png" alt="Instagram" style="box-sizing: border-box; vertical-align: middle;"/></a>
            <a href="https://www.facebook.com/gigu" target="_blank" style="box-sizing: border-box; -ms-touch-action: manipulation; touch-action: manipulation; margin-left: 1rem;"><img src="https://notification-templates-cdn.via1.app/static/facebook.b6c307f755950e91aee092d7c48dbeba.png" alt="Facebook" style="box-sizing: border-box; vertical-align: middle;"/></a>
            <a href="https://www.linkedin.com/company/gigu/" target="_blank" style="box-sizing: border-box; -ms-touch-action: manipulation; touch-action: manipulation; margin-left: 1rem;"><img src="https://notification-templates-cdn.via1.app/static/linkedin.cbaf30e12748afd6b02c1ddd2b0cbfcc.png" alt="Linkedin" style="box-sizing: border-box; vertical-align: middle;"/></a>
        </div>
        <div style="box-sizing: border-box;">
            <p style="color: #64748b; font-size: 12px; text-align: center;">GigU © 2024 Todos os direitos reservados.</p>
            <p style="color: #64748b; font-size: 12px; text-align: center;">GigU Pagamentos Digitais está inscrita no CNPJ XX.XXX.XXX/0001-XX</p>
        </div>
        <a class="link" href="https://gigu.app/privacidade/" target="_blank" rel="noreferrer noopener" style="box-sizing: border-box; -ms-touch-action: manipulation; touch-action: manipulation; color: #fff; margin-top: 1rem;">Pol&iacute;tica de privacidade</a>
    </div>
</div>
</body>
</html>