<!DOCTYPE html>
<html lang="pt-BR" style="box-sizing: border-box; cursor: default; line-height: 1.5; -moz-tab-size: 4; tab-size: 4; -webkit-tap-highlight-color: transparent; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; word-break: break-word;">
<head>
    <title>Me Poupe!</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta charset="utf-8"/>
    <style>
            {{#if paymentPartnerName}}
            @page {
                size: 8.3cm 21.5cm;
                margin: .1cm .1cm 0 .1cm;
            }
            {{else}}
            @page {
                size: 8.3cm 24cm;
                margin: .1cm .1cm 0 .1cm;
            }
            {{/if}}
    </style>
    <style type="text/css">/* Document
 * ========================================================================== *//**
 * Add border box sizing in all browsers (opinionated).
 */
    *,
    ::before,
    ::after {
        box-sizing: border-box;
    }

    /**
     * 1. Add text decoration inheritance in all browsers (opinionated).
     * 2. Add vertical alignment inheritance in all browsers (opinionated).
     */
    ::before,
    ::after {
        text-decoration: inherit; /* 1 */
        vertical-align: inherit; /* 2 */
    }

    /**
     * 1. Use the default cursor in all browsers (opinionated).
     * 2. Change the line height in all browsers (opinionated).
     * 3. Use a 4-space tab width in all browsers (opinionated).
     * 4. Remove the grey highlight on links in iOS (opinionated).
     * 5. Prevent adjustments of font size after orientation changes in
     *    IE on Windows Phone and in iOS.
     * 6. Breaks words to prevent overflow in all browsers (opinionated).
     */
    html {
        cursor: default; /* 1 */
        line-height: 1.5; /* 2 */
        -moz-tab-size: 4; /* 3 */
        tab-size: 4; /* 3 */
        -webkit-tap-highlight-color: transparent /* 4 */;
        -ms-text-size-adjust: 100%; /* 5 */
        -webkit-text-size-adjust: 100%; /* 5 */
        word-break: break-word; /* 6 */
    }

    /* Sections
     * ========================================================================== *//**
     * Remove the margin in all browsers (opinionated).
     */
    body {
        margin: 0;
    }

    /**
     * Correct the font size and margin on `h1` elements within `section` and
     * `article` contexts in Chrome, Edge, Firefox, and Safari.
     */
    h1 {
        font-size: 2em;
        margin: 0.67em 0;
    }

    /* Grouping content
     * ========================================================================== *//**
     * Remove the margin on nested lists in Chrome, Edge, IE, and Safari.
     */
    dl dl,
    dl ol,
    dl ul,
    ol dl,
    ul dl {
        margin: 0;
    }

    /**
     * Remove the margin on nested lists in Edge 18- and IE.
     */
    ol ol,
    ol ul,
    ul ol,
    ul ul {
        margin: 0;
    }

    /**
     * 1. Add the correct sizing in Firefox.
     * 2. Show the overflow in Edge 18- and IE.
     */
    hr {
        height: 0; /* 1 */
        overflow: visible; /* 2 */
    }

    /**
     * Add the correct display in IE.
     */
    main {
        display: block;
    }

    /**
     * Remove the list style on navigation lists in all browsers (opinionated).
     */
    nav ol,
    nav ul {
        list-style: none;
        padding: 0;
    }

    /**
     * 1. Correct the inheritance and scaling of font size in all browsers.
     * 2. Correct the odd `em` font sizing in all browsers.
     */
    pre {
        font-family: monospace, monospace; /* 1 */
        font-size: 1em; /* 2 */
    }

    /* Text-level semantics
     * ========================================================================== *//**
     * Add the correct text decoration in Edge 18-, IE, and Safari.
     */
    abbr[title] {
        text-decoration: underline;
        text-decoration: underline dotted;
    }

    /**
     * Add the correct font weight in Chrome, Edge, and Safari.
     */
    b,
    strong {
        font-weight: bolder;
    }

    /**
     * 1. Correct the inheritance and scaling of font size in all browsers.
     * 2. Correct the odd `em` font sizing in all browsers.
     */
    code,
    kbd,
    samp {
        font-family: monospace, monospace; /* 1 */
        font-size: 1em; /* 2 */
    }

    /**
     * Add the correct font size in all browsers.
     */
    small {
        font-size: 80%;
    }

    /* Embedded content
     * ========================================================================== *//*
     * Change the alignment on media elements in all browsers (opinionated).
     */
    audio,
    canvas,
    iframe,
    img,
    svg,
    video {
        vertical-align: middle;
    }

    /**
     * Remove the border on iframes in all browsers (opinionated).
     */
    iframe {
        border-style: none;
    }

    /**
     * Change the fill color to match the text color in all browsers (opinionated).
     */
    svg:not([fill]) {
        fill: currentColor;
    }

    /**
     * Hide the overflow in IE.
     */
    svg:not(:root) {
        overflow: hidden;
    }

    /* Tabular data
     * ========================================================================== *//**
     * Collapse border spacing in all browsers (opinionated).
     */
    table {
        border-collapse: collapse;
    }

    /* Forms
     * ========================================================================== *//**
     * Remove the margin on controls in Safari.
     */
    button,
    input,
    select {
        margin: 0;
    }

    /**
     * 1. Show the overflow in IE.
     * 2. Remove the inheritance of text transform in Edge 18-, Firefox, and IE.
     */
    button {
        overflow: visible; /* 1 */
        text-transform: none; /* 2 */
    }

    /**
     * Correct the inability to style buttons in iOS and Safari.
     */
    button,
    [type="button"],
    [type="reset"],
    [type="submit"] {
        -webkit-appearance: button;
    }

    /**
     * 1. Change the inconsistent appearance in all browsers (opinionated).
     * 2. Correct the padding in Firefox.
     */
    fieldset {
        border: 1px solid #a0a0a0; /* 1 */
        padding: 0.35em 0.75em 0.625em; /* 2 */
    }

    /**
     * Show the overflow in Edge 18- and IE.
     */
    input {
        overflow: visible;
    }

    /**
     * 1. Correct the text wrapping in Edge 18- and IE.
     * 2. Correct the color inheritance from `fieldset` elements in IE.
     */
    legend {
        color: inherit; /* 2 */
        display: table; /* 1 */
        max-width: 100%; /* 1 */
        white-space: normal; /* 1 */
    }

    /**
     * 1. Add the correct display in Edge 18- and IE.
     * 2. Add the correct vertical alignment in Chrome, Edge, and Firefox.
     */
    progress {
        display: inline-block; /* 1 */
        vertical-align: baseline; /* 2 */
    }

    /**
     * Remove the inheritance of text transform in Firefox.
     */
    select {
        text-transform: none;
    }

    /**
     * 1. Remove the margin in Firefox and Safari.
     * 2. Remove the default vertical scrollbar in IE.
     * 3. Change the resize direction in all browsers (opinionated).
     */
    textarea {
        margin: 0; /* 1 */
        overflow: auto; /* 2 */
        resize: vertical; /* 3 */
    }

    /**
     * 1. Correct the odd appearance in Chrome, Edge, and Safari.
     * 2. Correct the outline style in Safari.
     */
    [type="search"] {
        -webkit-appearance: textfield; /* 1 */
        outline-offset: -2px; /* 2 */
    }

    /**
     * Correct the cursor style of increment and decrement buttons in Safari.
     */
    ::-webkit-inner-spin-button,
    ::-webkit-outer-spin-button {
        height: auto;
    }

    /**
     * Correct the text style of placeholders in Chrome, Edge, and Safari.
     */
    ::-webkit-input-placeholder {
        color: inherit;
        opacity: 0.54;
    }

    /**
     * Remove the inner padding in Chrome, Edge, and Safari on macOS.
     */
    ::-webkit-search-decoration {
        -webkit-appearance: none;
    }

    /**
     * 1. Correct the inability to style upload buttons in iOS and Safari.
     * 2. Change font properties to `inherit` in Safari.
     */
    ::-webkit-file-upload-button {
        -webkit-appearance: button; /* 1 */
        font: inherit; /* 2 */
    }

    /**
     * Remove the inner border and padding of focus outlines in Firefox.
     */
    ::-moz-focus-inner {
        border-style: none;
        padding: 0;
    }

    /**
     * Restore the focus outline styles unset by the previous rule in Firefox.
     */
    :-moz-focusring {
        outline: 1px dotted ButtonText;
    }

    /**
     * Remove the additional :invalid styles in Firefox.
     */
    :-moz-ui-invalid {
        box-shadow: none;
    }

    /* Interactive
     * ========================================================================== *//*
     * Add the correct display in Edge 18- and IE.
     */
    details {
        display: block;
    }

    /*
     * Add the correct styles in Edge 18-, IE, and Safari.
     */
    dialog {
        background-color: white;
        border: solid;
        color: black;
        display: block;
        height: -moz-fit-content;
        height: -webkit-fit-content;
        height: fit-content;
        left: 0;
        margin: auto;
        padding: 1em;
        position: absolute;
        right: 0;
        width: -moz-fit-content;
        width: -webkit-fit-content;
        width: fit-content;
    }

    dialog:not([open]) {
        display: none;
    }

    /*
     * Add the correct display in all browsers.
     */
    summary {
        display: list-item;
    }

    /* Scripting
     * ========================================================================== *//**
     * Add the correct display in IE.
     */
    template {
        display: none;
    }

    /* User interaction
     * ========================================================================== *//*
     * 1. Remove the tapping delay in IE 10.
     * 2. Remove the tapping delay on clickable elements
          in all browsers (opinionated).
     */
    a,
    area,
    button,
    input,
    label,
    select,
    summary,
    textarea,
    [tabindex] {
        -ms-touch-action: manipulation; /* 1 */
        touch-action: manipulation; /* 2 */
    }

    /* Accessibility
     * ========================================================================== *//**
     * Change the cursor on busy elements in all browsers (opinionated).
     */
    [aria-busy="true"] {
        cursor: progress;
    }

    /*
     * Change the cursor on control elements in all browsers (opinionated).
     */
    [aria-controls] {
        cursor: pointer;
    }

    /*
     * Change the cursor on disabled, not-editable, or otherwise
     * inoperable elements in all browsers (opinionated).
     */
    [aria-disabled="true"],
    [disabled] {
        cursor: not-allowed;
    }

    /*
     * Change the display on visually hidden accessible elements
     * in all browsers (opinionated).
     */
    [aria-hidden="false"][hidden] {
        display: initial;
    }

    [aria-hidden="false"][hidden]:not(:focus) {
        clip: rect(0, 0, 0, 0);
        position: absolute;
    }

    @import-sanitize; body {
                          background-color: #f5f6f7;
                          color: #444a4b
                      }

    body {
        font-family: "Mulish", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
        background: #fff;
        max-width: 400px;
        letter-spacing: .42px;
        color: #444a4b
    }

    .header {
        padding: 24px 16px 0 16px;
        text-align: justify
    }

    .header .top {
        margin-bottom: 16px
    }

    .header .image {
        width: 112px;
        height: 32px
    }

    .header .column {
        float: right
    }

    .header .column .dateTime, .header .column .totalAmount {
        display: block
    }

    .header .column .totalAmount {
        font-size: 14px;
        font-weight: 700;
        line-height: 20px;
        text-align: right
    }

    .header .column .dateTime {
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        text-align: right
    }

    .header .box {
        height: 80px;
        margin: 0 -16px;
        font-family: "Quicksand"
    }

    .header .box p {
        padding: 24px 16px;
        font-size: 24px;
        font-weight: 700;
        line-height: 32px;
        color: #7761FD;
        text-align: left
    }

    .header .box p span {
        color: #475569
    }

    .footer {
        font-family: "Mulish", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
        text-align: center;
        font-size: 12px
    }

    .footer span {
        display: block;
        margin-top: 10px
    }

    .receipt-template {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale
    }

    .receipt-template .subtitle {
        font-family: "Quicksand";
        color: #475569;
        font-size: 16px;
        font-weight: 700;
        margin: 16px 16px 0 16px;
        line-height: 24px
    }

    .receipt-template .label {
        display: inline-block;
        font-size: 14px;
        font-weight: 500;
        color: #475569
    }

    .receipt-template .column-value, .receipt-template .value {
        display: inline-block;
        font-size: 14px;
        font-weight: 500;
        color: #64748b
    }

    .receipt-template .amount-container {
        margin: 16px;
        position: relative
    }

    .receipt-template .amount-container .label, .receipt-template .amount-container .amount {
        display: inline-block;
        font-weight: 700;
        font-size: 20px;
        line-height: 28px;
        color: #475569
    }

    .receipt-template .amount-container .label {
        font-family: "Quicksand";
        text-align: left
    }

    .receipt-template .amount-container .amount {
        position: absolute;
        bottom: 0;
        right: 0
    }

    .receipt-template .details .label {
        text-align: left
    }

    .receipt-template .details .value {
        text-align: right
    }

    .receipt-template .details .details-item {
        padding: 8px 16px;
        border-bottom: 1px solid #f1f5f9;
        position: relative
    }

    .receipt-template .details .details-item .value {
        position: absolute;
        bottom: 0;
        right: 0;
        margin-bottom: 9px;
        margin-right: 16px
    }

    .receipt-template .column-value {
        float: none;
        display: block;
        text-align: left
    }

    .receipt-template h1, .receipt-template h2, .receipt-template h3, .receipt-template h4, .receipt-template li, .receipt-template span, .receipt-template p {
        margin: 0;
        padding: 0
    }
    </style>
</head>
<body style="box-sizing: border-box; margin: 0; font-family: 'Mulish',-apple-system,BlinkMacSystemFont,'Segoe UI','Roboto','Oxygen','Ubuntu','Cantarell','Fira Sans','Droid Sans','Helvetica Neue',sans-serif; background: #fff; max-width: 400px; letter-spacing: .42px; color: #444a4b;">
<div class="header" style="box-sizing: border-box; padding: 24px 16px 0 16px; text-align: justify;">
    <div class="top" style="box-sizing: border-box; margin-bottom: 16px;">
        <img alt="Me Poupe!" style="box-sizing: border-box; vertical-align: middle; width: 112px;" src="https://notification-templates-cdn.mepoupe.app/static/logo-purple.d72e31c09b38ae46fb2772999c3febbb.png"/>
        <div class="column" style="box-sizing: border-box; float: right;">
            <span class="totalAmount" style="box-sizing: border-box; display: block; font-size: 14px; font-weight: 700; line-height: 20px; text-align: right;">{{totalAmount}}</span>
            <span class="dateTime" style="box-sizing: border-box; display: block; font-size: 12px; font-weight: 500; line-height: 16px; text-align: right;">{{dateTime}}</span>
        </div>
    </div>
    <div class="box" style="box-sizing: border-box; background-color: #F1EFFF; height: 80px; margin: 0 -16px; font-family: 'Quicksand';">
        <p style="box-sizing: border-box; padding: 24px 16px; text-align: center; font-size: 24px; font-weight: 700; line-height: 32px; color: #7761FD; text-align: left;"><span style="box-sizing: border-box; color: #475569;">TED</span> pago com Me Poupe!</p>
    </div>
</div>
<div class="receipt-template" style="box-sizing: border-box; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;">
    <div class="details" style="box-sizing: border-box;">
        <div class="amount-container" style="box-sizing: border-box; margin: 16px; position: relative;">
            <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-weight: 700; font-size: 20px; line-height: 28px; color: #475569; font-family: 'Quicksand'; text-align: left;">Valor</span>
            <span class="amount" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-weight: 700; font-size: 20px; line-height: 28px; color: #475569; position: absolute; bottom: 0; right: 0;">{{totalAmount}}</span>
        </div>
        <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
            <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Pago em</span>
            <span class="value" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #64748b; text-align: right; position: absolute; bottom: 0; right: 0; margin-bottom: 9px; margin-right: 16px;">{{dateTime}}</span>
        </div>
        <h2 class="subtitle" style="box-sizing: border-box; padding: 0; font-family: 'Quicksand'; color: #475569; font-size: 16px; font-weight: 700; margin: 16px 16px 0 16px; line-height: 24px;">Para</h2>
        <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
            <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Nome</span>
            <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{recipientName}}</span>
        </div>
        <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
            <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">{{recipientDocumentType}}</span>
            <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{recipientDocument}}</span>
        </div>
        <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
            <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Banco</span>
            <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{recipientBankNumber}}</span>
        </div>
        <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
            <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Agência</span>
            <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{recipientRoutingNumber}}</span>
        </div>
        <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
            <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Conta {{recipientAccountType}}</span>
            <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{recipientAccountNumber}}</span>
        </div>
        <h2 class="subtitle" style="box-sizing: border-box; padding: 0; font-family: 'Quicksand'; color: #475569; font-size: 16px; font-weight: 700; margin: 16px 16px 0 16px; line-height: 24px;">De</h2>
        <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
            <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Nome</span>
            <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{payerName}}</span>
        </div>
        {{#if paymentPartnerName}}
            <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
                <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Através de</span>
                <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{paymentPartnerName}}</span>
            </div>
        {{else}}
            <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
                <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Banco</span>
                <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{payerBankNumber}}</span>
            </div>
            <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
                <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Agência</span>
                <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{payerRoutingNumber}}</span>
            </div>
            <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
                <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Conta</span>
                <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{payerAccountNumber}}</span>
            </div>
        {{/if}}
        <h2 class="subtitle" style="box-sizing: border-box; padding: 0; font-family: 'Quicksand'; color: #475569; font-size: 16px; font-weight: 700; margin: 16px 16px 0 16px; line-height: 24px;">Dados da transação</h2>
        {{#if finality}}
            <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
                <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Finalidade da transação</span>
                <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{finality}}</span>
            </div>
        {{/if}}
        <div class="details-item" style="box-sizing: border-box; padding: 8px 16px; border-bottom: 1px solid #f1f5f9; position: relative;">
            <span class="label" style="box-sizing: border-box; margin: 0; padding: 0; display: inline-block; font-size: 14px; font-weight: 500; color: #475569; text-align: left;">Autenticação</span>
            <span class="column-value" style="box-sizing: border-box; margin: 0; padding: 0; font-size: 14px; font-weight: 500; color: #64748b; float: none; display: block; text-align: left;">{{authentication}}</span>
        </div>
    </div>
</div>
<div class="footer" style="box-sizing: border-box; font-family: 'Mulish',-apple-system,BlinkMacSystemFont,'Segoe UI','Roboto','Oxygen','Ubuntu','Cantarell','Fira Sans','Droid Sans','Helvetica Neue',sans-serif; text-align: center; font-size: 12px;">
    <span style="box-sizing: border-box; display: block; margin-top: 10px;">Este pagamento foi feito por <a style="box-sizing: border-box; -ms-touch-action: manipulation; touch-action: manipulation;">https://www.mepoupe.app/</a></span>
    <span style="box-sizing: border-box; display: block; margin-top: 10px;">CNPJ 56.152.016/0001-74</span>
    <span style="box-sizing: border-box; display: block; margin-top: 10px;">Através de: Banco Arbi - CNPJ 54.403.563/0001-50</span>
</div>
</body>
</html>

