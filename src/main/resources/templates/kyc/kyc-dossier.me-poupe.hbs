<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <title>Me Poupe! - <PERSON><PERSON><PERSON></title>
    <meta charset="utf-8"/>
    <meta name="color-scheme" content="only"/>
    <meta name="supported-color-schemes" content="only"/>

    <style type="text/css">
        .data-row.warning .data-value {
            color: red !important;
        }

        .data-value li.warning {
            color: red !important;
        }
    </style>
<style type="text/css">@page{size:A4 portrait;margin:100px 40px;@top-left{content:element(header-left);border-bottom:2px solid #dfdfdf;display:block}@top-center{border-bottom:2px solid #dfdfdf;display:block}@top-right{content:element(header-right);border-bottom:2px solid #dfdfdf;display:block}@bottom-center{content:"Me Poupe Pagamentos Digitais inscrita no 56.152.016/0001-74";font-size:14px;text-align:center;color:#7a869a}@bottom-right{content:counter(page) "/" counter(pages);font-size:14px;color:#7a869a}}body{background-color:#fff;color:#444a4b;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Oxygen","Ubuntu","Cantarell","Fira Sans","Droid Sans","Helvetica Neue",sans-serif;font-size:16px;-webkit-font-smoothing:antialiased}.header-left{position:running(header-left)}.header-left h1{font-size:26px;margin-bottom:6px;color:#626262;font-weight:500}.header-left h2{padding-left:2px;font-size:14px;font-weight:400;color:#626262;margin-bottom:0}.header-left h2 strong{font-weight:500}.header-right{position:running(header-right);text-align:right}.header-right img{display:inline-block}.main{background-color:#fff;padding-top:20px}.main table{page-break-inside:avoid;-fs-table-paginate:paginate;width:100%;border-collapse:collapse;margin-top:20px}.main table:first-child{margin-top:0}.main table h3{margin-bottom:18px}.main table tr{margin:0}.main table tr+tr{border-top:0}.main table td{padding:6px;border:1px solid #9b9b9b}.main table td:first-child{width:160px;background-color:#f3f3f3}.main .data-entry{padding-left:10px}.main .data-entry h4,.main .data-entry h5{color:#454953}.main .data-entry h4+table,.main .data-entry h5+table{margin-top:12px}.main .data-entry h4+h5{font-weight:400;margin-top:8px;margin-bottom:0px}.main .data-entry+.data-entry{margin-top:24px}.main .data-entry .data-row-result .data-value{color:green}.main .data-row{line-height:1}.main .data-row .data-label{font-size:14px;font-weight:500;color:#5b6377}.main .data-row .data-label small{font-size:10px;font-weight:400;text-transform:uppercase;color:#838ca3}.main .data-row .data-value{font-family:monospace,"Courier New"}.main .data-row .data-value small{font-size:12px;color:#687189}.main .data-row .data-value ul{font-size:14px;color:#687189}ul{list-style:none;margin:0;padding:0;list-style:decimal;list-style-position:inside}h1,h2,h3,h4,li,p{margin:0}p{text-align:left;line-height:1.5;color:#1e273e}p+a,p+div{margin-top:16px}a{color:#1e273e}
</style></head>
<body style="background-color: #fff; color: #444a4b; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI','Roboto','Oxygen','Ubuntu','Cantarell','Fira Sans','Droid Sans','Helvetica Neue',sans-serif; font-size: 16px; -webkit-font-smoothing: antialiased;">
    <div class="header-left" style="position: running(header-left);">
        <h1 style="margin: 0; font-size: 26px; margin-bottom: 6px; color: #626262; font-weight: 500;">Relatório KYC</h1>
        <h2 style="margin: 0; padding-left: 2px; font-size: 14px; font-weight: 400; color: #626262; margin-bottom: 0;"><strong style="font-weight: 500;">Referência:</strong> {{accountId}}</h2>
    </div>
    <div class="header-right" style="position: running(header-right); text-align: right;">
        <img src="data:image/png;base64,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" alt="Me Poupe!" style="display: inline-block;"/>
    </div>
    <div class="main" style="background-color: #fff; padding-top: 20px;">
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; margin-top: 0; padding-left: 10px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">Geral</h4></td>
                </tr>
            </thead>
            <tr class="data-row" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Tipo de cadastro</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{registrationType}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Data de criação</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{createdAt}}</td>
            </tr>
            <tr class="data-row {{#if risk.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Classificação de risco</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{risk.result}}</td>
            </tr>
        </table>

        {{#with personalData}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">Dados pessoais</h4></td>
                </tr>
            </thead>
            <tr class="data-row" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Nome completo<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">informado</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{fullName.declared}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Nome completo<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">consultado</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{fullName.query.value}}</td>
            </tr>
            <tr class="data-row {{#if fullName.query.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Coincide com o CPF?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{fullName.query.result}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Data de nascimento<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">informada</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{birthDate.declared}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Data de nascimento<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">consultada</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{birthDate.query.value}}</td>
            </tr>
            <tr class="data-row {{#if birthDate.query.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Coincide com o CPF?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{birthDate.query.result}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Sexo<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">consultado</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{gender}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Nome da mãe<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">consultado</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{motherName.query.value}}</td>
            </tr>
            {{#if motherName.declared}}
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Nome da mãe<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">informado</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{motherName.declared}} </td>
            </tr>
            <tr class="data-row {{#if motherName.query.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Análise do<br/>nome da mãe</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{motherName.query.result}} </td>
            </tr>
            {{/if}}
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Nome do pai<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">consultado</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{fatherName.query.value}}</td>
            </tr>
            {{#if fatherName.declared}}
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Nome do pai<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">informado</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{fatherName.declared}} </td>
            </tr>
            <tr class="data-row {{#if fatherName.query.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Análise do<br/>nome do pai</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{fatherName.query.result}} </td>
            </tr>
            {{/if}}
        </table>
        {{/with}}

        {{#with biometry}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">Biometria</h4></td>
                </tr>
            </thead>
            <tr class="data-row {{#if liveness.warning}}warning{{/if}}" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Prova de vida</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{liveness.result}}</td>
            </tr>
            <tr class="data-row {{#if identity.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Rosto em base<br/>oficial do governo?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">
                    {{identity.result}}<br/>
                    {{#if identity.percentage}}<small style="font-size: 12px; color: #687189;">{{identity.percentage}} de similaridade</small>{{/if}}
                </td>
            </tr>
            <tr class="data-row {{#if duplications.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Rosto já tem<br/>cadastro?</td>
                    <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">
                    {{duplications.result}}<br/>
                    {{#if duplications.accounts}}
                    <small style="font-size: 12px; color: #687189;">
                        {{#each duplications.accounts}}
                        {{#if @last}}{{this}}{{else}}{{this}}<br/>{{/if}}
                        {{/each}}
                    </small>
                    {{/if}}
                </td>
            </tr>
            <tr class="data-row {{#if fraudIndications.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Rosto em lista<br/>de fraudadores?</td>
                    <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">
                    {{fraudIndications.result}}<br/>
                    {{#if fraudIndications.accounts}}
                    <small style="font-size: 12px; color: #687189;">
                        {{#each fraudIndications.accounts}}
                        {{#if @last}}{{this}}{{else}}{{this}}<br/>{{/if}}
                        {{/each}}
                    </small>
                    {{/if}}
                </td>
            </tr>
            {{#if documentFaceMatch}}
            <tr class="data-row {{#if documentFaceMatch.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Similaridade do rosto<br/>com documento enviado</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">
                    {{documentFaceMatch.result}}<br/>
                    {{#if documentFaceMatch.percentage}}<small style="font-size: 12px; color: #687189;">{{documentFaceMatch.percentage}}</small>{{/if}}
                </td>
            </tr>
            {{/if}}
        </table>
        {{/with}}

        {{#with documentScan}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">Análise do documento</h4></td>
                </tr>
            </thead>
            <tr class="data-row {{#if digitalSpoof.warning}}warning{{/if}}" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">É um documento<br/>físico?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{digitalSpoof.value}}</td>
            </tr>
            <tr class="data-row {{#if face.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Foto do rosto<br/>adulterada?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{face.value}}</td>
            </tr>
            <tr class="data-row {{#if text.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Dados do documento<br/>adulterados?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{text.value}}</td>
            </tr>
            <tr class="data-row {{#if ocr.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Leitura de dados<br/>automatizada</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">
                    {{ocr.value}}
                    <br/>
                    <small style="font-size: 12px; color: #687189;">Os dados lidos não são exibidos neste documento</small>
                </td>
            </tr>
        </table>
        {{/with}}

        {{#with cpf}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">Consulta ao CPF</h4></td>
                </tr>
            </thead>
            <tr class="data-row" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">CPF</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{value}}</td>
            </tr>
            <tr class="data-row {{#if status.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Situação</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{status.value}}</td>
            </tr>
            <tr class="data-row {{#if hasObitIndication.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Indicação de óbito</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{hasObitIndication.value}}</td>
            </tr>
            <tr class="data-row {{#if inFraudList.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">CPF em lista<br/>de fraudadores?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{inFraudList.value}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Região</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{region}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Possui alguma sanção?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">
                    {{#if sanctions.length}}
                    <ul style="margin: 0; padding: 0; font-size: 14px; color: #687189; list-style: decimal; list-style-position: inside;">
                        {{#each sanctions}}
                        <li class="{{#if this.warning}}warning{{/if}}" style="margin: 0;">{{this.value}}</li>
                        {{/each}}
                    </ul>
                    {{else}}
                    Nenhuma sanção encontrada
                    {{/if}}
                    <br/>
                    <small style="font-size: 12px; color: #687189;">Lista de sanções analisadas ao final do arquivo</small>
                </td>
            </tr>
        </table>
        {{/with}}

        {{#with email}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">E-mail verificado por código</h4></td>
                </tr>
            </thead>
            <tr class="data-row" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">E-mail</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{value}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Tempo de base</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{createdAt}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Recente</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{recent}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Ativo</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{active}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Associado ao CPF?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{relatedToCpf}}</td>
            </tr>
        </table>
        {{/with}}

        {{#with phone}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">Telefone verificado por código</h4></td>
                </tr>
            </thead>
            <tr class="data-row" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Telefone</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{value}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Tempo de base</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{createdAt}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Recente</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{recent}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Ativo</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{active}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Associado ao CPF?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{relatedToCpf}}</td>
            </tr>
        </table>
        {{/with}}

        {{#with monthlyIncome}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">Faixa de renda</h4></td>
                </tr>
            </thead>
            <tr class="data-row" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Valor autodeclarado</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{value}}<br/><small style="font-size: 12px; color: #687189;">Sem quaisquer verificações adicionais</small></td>
            </tr>
        </table>
        {{/with}}

        {{#with mep}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3">
                        <h4 style="margin: 0; color: #454953;">Exposição e perfil na mídia</h4>
                        <h5 style="color: #454953; font-weight: 400; margin-top: 8px; margin-bottom: 0px;">Classificado de A (mais elevado) a H (menos elevado)</h5>
                    </td>
                </tr>
            </thead>
            <tr class="data-row {{#if exposure.warning}}warning{{/if}}" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Exposição (6 meses)</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{exposure.level}}</td>
            </tr>
            <tr class="data-row {{#if celebrity.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Nível celebridade</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{celebrity.level}}</td>
            </tr>
            <tr class="data-row {{#if unpopularity.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Impopularidade</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{unpopularity.level}}</td>
            </tr>
        </table>
        {{/with}}

        {{#with pep}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3">
                        <h4 style="margin: 0; color: #454953;">Pessoa politicamente exposta</h4>
                    </td>
                </tr>
            </thead>
            <tr class="data-row {{#if selfDeclared.warning}}warning{{/if}}" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Autodeclarado</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{selfDeclared.value}}</td>
            </tr>
            <tr class="data-row {{#if query.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Consultado</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{query.value}}</td>
            </tr>
            {{#if query.level}}
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Exposição</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{query.level}}<br/><small style="font-size: 12px; color: #687189;">Nível 1: pessoas que estão listadas diretamente<br/>Nível 2:
                    pessoas que tem algum relacionamento com PPE</small></td>
            </tr>
            {{/if}}
        </table>
        {{/with}}

        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;"> Lista de sanções analisadas</h4></td>
                </tr>
            </thead>
            <tr class="data-row" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Consultado</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">
                    <ul style="list-style: none; margin: 0; padding: 0; list-style: decimal; list-style-position: inside; font-size: 14px; color: #687189;">
                    {{#each globalSanctionsList}}
                        <li style="margin: 0;">{{this}}</li>
                    {{/each}}
                    </ul>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>
