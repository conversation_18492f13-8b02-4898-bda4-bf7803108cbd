<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <title>Friday - <PERSON><PERSON><PERSON></title>
    <meta charset="utf-8"/>
    <meta name="color-scheme" content="only"/>
    <meta name="supported-color-schemes" content="only"/>

    <style type="text/css">
        .data-row.warning .data-value {
            color: red !important;
        }

        .data-value li.warning {
            color: red !important;
        }
    </style>
<style type="text/css">@page{size:A4 portrait;margin:100px 40px;@top-left{content:element(header-left);border-bottom:2px solid #dfdfdf;display:block}@top-center{border-bottom:2px solid #dfdfdf;display:block}@top-right{content:element(header-right);border-bottom:2px solid #dfdfdf;display:block}@bottom-center{content:"Friday Pagamentos Ltda. inscrita no 34.701.685/0001-15";font-size:14px;text-align:center;color:#7a869a}@bottom-right{content:counter(page) "/" counter(pages);font-size:14px;color:#7a869a}}body{background-color:#fff;color:#444a4b;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Oxygen","Ubuntu","Cantarell","Fira Sans","Droid Sans","Helvetica Neue",sans-serif;font-size:16px;-webkit-font-smoothing:antialiased}.header-left{position:running(header-left)}.header-left h1{font-size:26px;margin-bottom:6px;color:#626262;font-weight:500}.header-left h2{padding-left:2px;font-size:14px;font-weight:400;color:#626262;margin-bottom:0}.header-left h2 strong{font-weight:500}.header-right{position:running(header-right);text-align:right}.header-right img{display:inline-block}.main{background-color:#fff;padding-top:20px}.main table{page-break-inside:avoid;-fs-table-paginate:paginate;width:100%;border-collapse:collapse;margin-top:20px}.main table:first-child{margin-top:0}.main table h3{margin-bottom:18px}.main table tr{margin:0}.main table tr+tr{border-top:0}.main table td{padding:6px;border:1px solid #9b9b9b}.main table td:first-child{width:160px;background-color:#f3f3f3}.main .data-entry{padding-left:10px}.main .data-entry h4,.main .data-entry h5{color:#454953}.main .data-entry h4+table,.main .data-entry h5+table{margin-top:12px}.main .data-entry h4+h5{font-weight:400;margin-top:8px;margin-bottom:0px}.main .data-entry+.data-entry{margin-top:24px}.main .data-entry .data-row-result .data-value{color:green}.main .data-row{line-height:1}.main .data-row .data-label{font-size:14px;font-weight:500;color:#5b6377}.main .data-row .data-label small{font-size:10px;font-weight:400;text-transform:uppercase;color:#838ca3}.main .data-row .data-value{font-family:monospace,"Courier New"}.main .data-row .data-value small{font-size:12px;color:#687189}.main .data-row .data-value ul{font-size:14px;color:#687189}ul{list-style:none;margin:0;padding:0;list-style:decimal;list-style-position:inside}h1,h2,h3,h4,li,p{margin:0}p{text-align:left;line-height:1.5;color:#1e273e}p+a,p+div{margin-top:16px}a{color:#1e273e}
</style></head>
<body style="background-color: #fff; color: #444a4b; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI','Roboto','Oxygen','Ubuntu','Cantarell','Fira Sans','Droid Sans','Helvetica Neue',sans-serif; font-size: 16px; -webkit-font-smoothing: antialiased;">
    <div class="header-left" style="position: running(header-left);">
        <h1 style="margin: 0; font-size: 26px; margin-bottom: 6px; color: #626262; font-weight: 500;">Relatório KYC</h1>
        <h2 style="margin: 0; padding-left: 2px; font-size: 14px; font-weight: 400; color: #626262; margin-bottom: 0;"><strong style="font-weight: 500;">Referência:</strong> {{accountId}}</h2>
    </div>
    <div class="header-right" style="position: running(header-right); text-align: right;">
        <img src="data:image/png;base64,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" alt="Friday" style="display: inline-block;"/>
    </div>
    <div class="main" style="background-color: #fff; padding-top: 20px;">
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; margin-top: 0; padding-left: 10px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">Geral</h4></td>
                </tr>
            </thead>
            <tr class="data-row" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Tipo de cadastro</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{registrationType}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Data de criação</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{createdAt}}</td>
            </tr>
            <tr class="data-row {{#if risk.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Classificação de risco</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{risk.result}}</td>
            </tr>
        </table>

        {{#with personalData}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">Dados pessoais</h4></td>
                </tr>
            </thead>
            <tr class="data-row" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Nome completo<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">informado</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{fullName.declared}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Nome completo<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">consultado</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{fullName.query.value}}</td>
            </tr>
            <tr class="data-row {{#if fullName.query.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Coincide com o CPF?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{fullName.query.result}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Data de nascimento<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">informada</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{birthDate.declared}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Data de nascimento<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">consultada</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{birthDate.query.value}}</td>
            </tr>
            <tr class="data-row {{#if birthDate.query.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Coincide com o CPF?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{birthDate.query.result}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Sexo<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">consultado</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{gender}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Nome da mãe<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">consultado</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{motherName.query.value}}</td>
            </tr>
            {{#if motherName.declared}}
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Nome da mãe<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">informado</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{motherName.declared}} </td>
            </tr>
            <tr class="data-row {{#if motherName.query.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Análise do<br/>nome da mãe</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{motherName.query.result}} </td>
            </tr>
            {{/if}}
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Nome do pai<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">consultado</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{fatherName.query.value}}</td>
            </tr>
            {{#if fatherName.declared}}
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Nome do pai<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">informado</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{fatherName.declared}} </td>
            </tr>
            <tr class="data-row {{#if fatherName.query.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Análise do<br/>nome do pai</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{fatherName.query.result}} </td>
            </tr>
            {{/if}}
        </table>
        {{/with}}

        {{#with biometry}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">Biometria</h4></td>
                </tr>
            </thead>
            <tr class="data-row {{#if liveness.warning}}warning{{/if}}" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Prova de vida</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{liveness.result}}</td>
            </tr>
            <tr class="data-row {{#if identity.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Rosto em base<br/>oficial do governo?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">
                    {{identity.result}}<br/>
                    {{#if identity.percentage}}<small style="font-size: 12px; color: #687189;">{{identity.percentage}} de similaridade</small>{{/if}}
                </td>
            </tr>
            <tr class="data-row {{#if duplications.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Rosto já tem<br/>cadastro?</td>
                    <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">
                    {{duplications.result}}<br/>
                    {{#if duplications.accounts}}
                    <small style="font-size: 12px; color: #687189;">
                        {{#each duplications.accounts}}
                        {{#if @last}}{{this}}{{else}}{{this}}<br/>{{/if}}
                        {{/each}}
                    </small>
                    {{/if}}
                </td>
            </tr>
            <tr class="data-row {{#if fraudIndications.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Rosto em lista<br/>de fraudadores?</td>
                    <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">
                    {{fraudIndications.result}}<br/>
                    {{#if fraudIndications.accounts}}
                    <small style="font-size: 12px; color: #687189;">
                        {{#each fraudIndications.accounts}}
                        {{#if @last}}{{this}}{{else}}{{this}}<br/>{{/if}}
                        {{/each}}
                    </small>
                    {{/if}}
                </td>
            </tr>
            {{#if documentFaceMatch}}
            <tr class="data-row {{#if documentFaceMatch.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Similaridade do rosto<br/>com documento enviado</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">
                    {{documentFaceMatch.result}}<br/>
                    {{#if documentFaceMatch.percentage}}<small style="font-size: 12px; color: #687189;">{{documentFaceMatch.percentage}}</small>{{/if}}
                </td>
            </tr>
            {{/if}}
        </table>
        {{/with}}

        {{#with documentScan}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">Análise do documento</h4></td>
                </tr>
            </thead>
            <tr class="data-row {{#if digitalSpoof.warning}}warning{{/if}}" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">É um documento<br/>físico?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{digitalSpoof.value}}</td>
            </tr>
            <tr class="data-row {{#if face.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Foto do rosto<br/>adulterada?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{face.value}}</td>
            </tr>
            <tr class="data-row {{#if text.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Dados do documento<br/>adulterados?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{text.value}}</td>
            </tr>
            <tr class="data-row {{#if ocr.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Leitura de dados<br/>automatizada</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">
                    {{ocr.value}}
                    <br/>
                    <small style="font-size: 12px; color: #687189;">Os dados lidos não são exibidos neste documento</small>
                </td>
            </tr>
        </table>
        {{/with}}

        {{#with cpf}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">Consulta ao CPF</h4></td>
                </tr>
            </thead>
            <tr class="data-row" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">CPF</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{value}}</td>
            </tr>
            <tr class="data-row {{#if status.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Situação</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{status.value}}</td>
            </tr>
            <tr class="data-row {{#if hasObitIndication.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Indicação de óbito</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{hasObitIndication.value}}</td>
            </tr>
            <tr class="data-row {{#if inFraudList.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">CPF em lista<br/>de fraudadores?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{inFraudList.value}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Região</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{region}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Possui alguma sanção?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">
                    {{#if sanctions.length}}
                    <ul style="margin: 0; padding: 0; font-size: 14px; color: #687189; list-style: decimal; list-style-position: inside;">
                        {{#each sanctions}}
                        <li class="{{#if this.warning}}warning{{/if}}" style="margin: 0;">{{this.value}}</li>
                        {{/each}}
                    </ul>
                    {{else}}
                    Nenhuma sanção encontrada
                    {{/if}}
                    <br/>
                    <small style="font-size: 12px; color: #687189;">Lista de sanções analisadas ao final do arquivo</small>
                </td>
            </tr>
        </table>
        {{/with}}

        {{#with email}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">E-mail verificado por código</h4></td>
                </tr>
            </thead>
            <tr class="data-row" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">E-mail</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{value}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Tempo de base</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{createdAt}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Recente</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{recent}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Ativo</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{active}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Associado ao CPF?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{relatedToCpf}}</td>
            </tr>
        </table>
        {{/with}}

        {{#with phone}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">Telefone verificado por código</h4></td>
                </tr>
            </thead>
            <tr class="data-row" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Telefone</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{value}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Tempo de base</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{createdAt}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Recente</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{recent}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Ativo</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{active}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Associado ao CPF?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{relatedToCpf}}</td>
            </tr>
        </table>
        {{/with}}

        {{#with monthlyIncome}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">Faixa de renda</h4></td>
                </tr>
            </thead>
            <tr class="data-row" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Valor autodeclarado</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{value}}<br/><small style="font-size: 12px; color: #687189;">Sem quaisquer verificações adicionais</small></td>
            </tr>
        </table>
        {{/with}}

        {{#with mep}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3">
                        <h4 style="margin: 0; color: #454953;">Exposição e perfil na mídia</h4>
                        <h5 style="color: #454953; font-weight: 400; margin-top: 8px; margin-bottom: 0px;">Classificado de A (mais elevado) a H (menos elevado)</h5>
                    </td>
                </tr>
            </thead>
            <tr class="data-row {{#if exposure.warning}}warning{{/if}}" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Exposição (6 meses)</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{exposure.level}}</td>
            </tr>
            <tr class="data-row {{#if celebrity.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Nível celebridade</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{celebrity.level}}</td>
            </tr>
            <tr class="data-row {{#if unpopularity.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Impopularidade</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{unpopularity.level}}</td>
            </tr>
        </table>
        {{/with}}

        {{#with pep}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3">
                        <h4 style="margin: 0; color: #454953;">Pessoa politicamente exposta</h4>
                    </td>
                </tr>
            </thead>
            <tr class="data-row {{#if selfDeclared.warning}}warning{{/if}}" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Autodeclarado</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{selfDeclared.value}}</td>
            </tr>
            <tr class="data-row {{#if query.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Consultado</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{query.value}}</td>
            </tr>
            {{#if query.level}}
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Exposição</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{query.level}}<br/><small style="font-size: 12px; color: #687189;">Nível 1: pessoas que estão listadas diretamente<br/>Nível 2:
                    pessoas que tem algum relacionamento com PPE</small></td>
            </tr>
            {{/if}}
        </table>
        {{/with}}

        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;"> Lista de sanções analisadas</h4></td>
                </tr>
            </thead>
            <tr class="data-row" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Consultado</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">
                    <ul style="list-style: none; margin: 0; padding: 0; list-style: decimal; list-style-position: inside; font-size: 14px; color: #687189;">
                    {{#each globalSanctionsList}}
                        <li style="margin: 0;">{{this}}</li>
                    {{/each}}
                    </ul>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>
