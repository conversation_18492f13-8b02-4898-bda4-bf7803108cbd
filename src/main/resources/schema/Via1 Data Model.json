{"ModelName": "Via1 Data Model", "DataModel": [{"TableName": "Via1-BillPayment", "KeyAttributes": {"PartitionKey": {"AttributeName": "<PERSON><PERSON><PERSON>", "AttributeType": "S"}, "SortKey": {"AttributeName": "Scan<PERSON>ey", "AttributeType": "S"}}, "NonKeyAttributes": [{"AttributeName": "GSIndex1PrimaryKey", "AttributeType": "S"}, {"AttributeName": "GSIndex1ScanKey", "AttributeType": "S"}, {"AttributeName": "<PERSON><PERSON><PERSON><PERSON>", "AttributeType": "N"}, {"AttributeName": "Created", "AttributeType": "S"}, {"AttributeName": "Document", "AttributeType": "S"}, {"AttributeName": "DocumentType", "AttributeType": "S"}, {"AttributeName": "Email", "AttributeType": "S"}, {"AttributeName": "MobilePhone", "AttributeType": "S"}, {"AttributeName": "CreditCardBrand", "AttributeType": "S"}, {"AttributeName": "CreditCardExpiryDate", "AttributeType": "S"}, {"AttributeName": "CreditCardPAN", "AttributeType": "S"}, {"AttributeName": "PaymentMethodId", "AttributeType": "S"}, {"AttributeName": "Amount", "AttributeType": "N"}, {"AttributeName": "Assignor", "AttributeType": "S"}, {"AttributeName": "DigitableBarcode", "AttributeType": "S"}, {"AttributeName": "DueDate", "AttributeType": "S"}, {"AttributeName": "PaidDate", "AttributeType": "S"}, {"AttributeName": "Recipient", "AttributeType": "S"}, {"AttributeName": "Type", "AttributeType": "S"}, {"AttributeName": "BankTransactionId", "AttributeType": "N"}, {"AttributeName": "Bill<PERSON>d", "AttributeType": "S"}, {"AttributeName": "<PERSON><PERSON>", "AttributeType": "N"}, {"AttributeName": "TotalAmount", "AttributeType": "N"}, {"AttributeName": "Status", "AttributeType": "S"}], "GlobalSecondaryIndexes": [{"IndexName": "GSIndex1", "KeyAttributes": {"PartitionKey": {"AttributeName": "GSIndex1PrimaryKey", "AttributeType": "S"}, "SortKey": {"AttributeName": "GSIndex1ScanKey", "AttributeType": "S"}}, "Projection": {"ProjectionType": "ALL"}}], "TableFacets": [{"FacetName": "Owner", "KeyAttributeAlias": {"PartitionKeyAlias": "AccountId", "SortKeyAlias": "<PERSON><PERSON><PERSON>"}, "NonKeyAttributes": ["GSIndex1PrimaryKey", "GSIndex1ScanKey", "<PERSON><PERSON><PERSON><PERSON>", "Created", "Document", "DocumentType", "Email", "MobilePhone"], "TableData": [{"PrimaryKey": {"S": "ACCOUNT-0cc4771c-c131-4622-8a6d-97d3014921e8"}, "ScanKey": {"S": "OWNER"}, "GSIndex1PrimaryKey": {"S": "***********"}, "GSIndex1ScanKey": {"S": "ACTIVE"}, "BillCount": {"N": 0}, "Created": {"S": "2020-02-11 17:51:56"}, "Document": {"S": "***********"}, "DocumentType": {"S": "CPF"}, "Email": {"S": "<EMAIL>"}, "MobilePhone": {"S": "*************"}}], "DataAccess": {"MySql": {}}}, {"FacetName": "Assistant", "KeyAttributeAlias": {"PartitionKeyAlias": "AccountId", "SortKeyAlias": "<PERSON><PERSON><PERSON>"}, "NonKeyAttributes": ["Created", "Document", "DocumentType", "Email", "MobilePhone"], "TableData": [{"PrimaryKey": {"S": "ACCOUNT-0cc4771c-c131-4622-8a6d-97d3014921e8"}, "ScanKey": {"S": "ASSISTANT"}, "Created": {"S": "2020-02-11 17:51:56"}, "Document": {"S": "***********"}, "DocumentType": {"S": "CPF"}, "Email": {"S": "<EMAIL>"}, "MobilePhone": {"S": "*************"}}], "DataAccess": {"MySql": {}}}, {"FacetName": "Dependent", "KeyAttributeAlias": {"PartitionKeyAlias": "AccountId", "SortKeyAlias": "<PERSON><PERSON><PERSON>"}, "NonKeyAttributes": ["GSIndex1PrimaryKey", "GSIndex1ScanKey", "Created", "Document", "DocumentType", "Email"], "TableData": [{"PrimaryKey": {"S": "ACCOUNT-0cc4771c-c131-4622-8a6d-97d3014921e8"}, "ScanKey": {"S": "DEPENDENT"}, "GSIndex1PrimaryKey": {"S": "***********"}, "GSIndex1ScanKey": {"S": "ACTIVE"}, "Created": {"S": "2020-02-11 17:51:56"}, "Document": {"S": "***********"}, "DocumentType": {"S": "CPF"}, "Email": {"S": "<EMAIL>"}}], "DataAccess": {"MySql": {}}}, {"FacetName": "<PERSON><PERSON>", "KeyAttributeAlias": {"PartitionKeyAlias": "GoogleId", "SortKeyAlias": "<PERSON><PERSON><PERSON>"}, "NonKeyAttributes": ["GSIndex1PrimaryKey", "GSIndex1ScanKey"], "TableData": [{"PrimaryKey": {"S": "117404139476710913772"}, "ScanKey": {"S": "LOGIN#GOOGLE"}, "GSIndex1PrimaryKey": {"S": "<EMAIL>"}, "GSIndex1ScanKey": {"S": "ACCOUNT-3dc5eb06-71c3-4442-8f9c-3ae65a862612"}}], "DataAccess": {"MySql": {}}}, {"FacetName": "PaymentMethod", "KeyAttributeAlias": {"PartitionKeyAlias": "AccountId", "SortKeyAlias": "<PERSON><PERSON><PERSON>"}, "NonKeyAttributes": ["GSIndex1PrimaryKey", "GSIndex1ScanKey", "Created", "CreditCardBrand", "CreditCardExpiryDate", "CreditCardPAN", "PaymentMethodId", "Status"], "TableData": [{"PrimaryKey": {"S": "ACCOUNT-0cc4771c-c131-4622-8a6d-97d3014921e8"}, "ScanKey": {"S": "PAYMENT-METHOD#ACTIVE#1"}, "Created": {"S": "2020-02-11 17:51:56"}, "CreditCardBrand": {"S": "Visa"}, "CreditCardExpiryDate": {"S": "12/2026"}, "CreditCardPAN": {"S": "****************"}, "PaymentMethodId": {"S": "1745c4af-d6f1-4f4e-b221-ec19d71b5ff3"}, "Status": {"S": "ACTIVE"}, "Type": {"S": "CREDIT_CARD"}}, {"PrimaryKey": {"S": "ACCOUNT-0cc4771c-c131-4622-8a6d-97d3014921e8"}, "ScanKey": {"S": "PAYMENT-METHOD#ACTIVE#2"}, "Created": {"S": "2020-02-11 17:51:56"}, "BankAccountType": {"S": "CHECKING"}, "BankNo": {"S": "341"}, "BankRoutingNo": {"S": "3030"}, "BankAccountNo": {"S": "00036"}, "BankAccountDv": {"S": "1"}, "GSIndex1PrimaryKey": {"S": "BALANCE"}, "GSIndex1ScanKey": {"S": "213#19#325513"}, "PaymentMethodId": {"S": "02fc5746-d8d1-40a8-b305-c9d3a8fb12ef"}, "Status": {"S": "ACTIVE"}, "Type": {"S": "BALANCE"}}], "DataAccess": {"MySql": {}}}, {"FacetName": "ScheduledBill", "KeyAttributeAlias": {"PartitionKeyAlias": "AccountId", "SortKeyAlias": "<PERSON><PERSON><PERSON>"}, "NonKeyAttributes": ["GSIndex1PrimaryKey", "GSIndex1ScanKey", "ScheduledDate", "Bill<PERSON>d", "PaymentMethodId"], "TableData": [{"PrimaryKey": {"S": "ACCOUNT-0cc4771c-c131-4622-8a6d-97d3014921e8"}, "ScanKey": {"S": "SCHEDULED#2020-08-25#BILL-7e8d8d4c-0a7f-4d4f-ac30-2025774cc9e8"}, "GSIndex1PrimaryKey": {"S": "SCHEDULED#BILL-7e8d8d4c-0a7f-4d4f-ac30-2025774cc9e8"}, "GSIndex1ScanKey": {"S": "ACCOUNT-0cc4771c-c131-4622-8a6d-97d3014921e8"}, "ScheduledDate": {"S": "2020-08-25"}, "BillId": {"S": "BILL-7e8d8d4c-0a7f-4d4f-ac30-2025774cc9e8"}, "PaymentMethodId": {"S": "1745c4af-d6f1-4f4e-b221-ec19d71b5ff3"}}], "DataAccess": {"MySql": {}}}, {"FacetName": "ScheduledAccount", "KeyAttributeAlias": {"PartitionKeyAlias": "<PERSON><PERSON><PERSON>", "SortKeyAlias": "AccountId"}, "NonKeyAttributes": [], "TableData": [{"PrimaryKey": {"S": "SCHEDULED-ACCOUNT"}, "ScanKey": {"S": "ACCOUNT-0cc4771c-c131-4622-8a6d-97d3014921e8"}}], "DataAccess": {"MySql": {}}}, {"FacetName": "Bill", "KeyAttributeAlias": {"PartitionKeyAlias": "Bill<PERSON>d", "SortKeyAlias": "<PERSON><PERSON><PERSON>"}, "NonKeyAttributes": ["GSIndex1PrimaryKey", "GSIndex1ScanKey", "Created", "Amount", "Assignor", "DigitableBarcode", "DueDate", "PaidDate", "PaymentLimitTime", "Recipient", "Type", "Status"], "TableData": [{"PrimaryKey": {"S": "BILL-7e8d8d4c-0a7f-4d4f-ac30-2025774cc9e8"}, "ScanKey": {"S": "ACCOUNT-8e8d8d4c-0a3f-4d4f-ac30-2025954cc9e8"}, "GSIndex1PrimaryKey": {"S": "ACCOUNT-8e8d8d4c-0a3f-4d4f-ac30-2025954cc9e8"}, "GSIndex1ScanKey": {"S": "BILL#2019-10-22#PAID"}, "Amount": {"N": 2500}, "Assignor": {"S": "BANCO DO BRASIL S.A."}, "Created": {"S": "2019-10-22 07:50:41"}, "DigitableBarcode": {"S": "00190000090286665907712371179172980510000002500"}, "DueDate": {"S": "2019-10-22"}, "PaidDate": {"S": "2019-10-22"}, "PaymentLimitTime": {"S": "20:00"}, "Recipient": {"S": "BANCO DO BRASIL S.A."}, "Status": {"S": "PAID"}, "Type": {"S": "FICHA_COMPENSACAO"}}], "DataAccess": {"MySql": {}}}, {"FacetName": "Transaction", "KeyAttributeAlias": {"PartitionKeyAlias": "TransactionId", "SortKeyAlias": "UserId"}, "NonKeyAttributes": ["GSIndex1PrimaryKey", "GSIndex1ScanKey", "Created", "BankTransactionId", "Bill<PERSON>d", "<PERSON><PERSON>", "TotalAmount", "PaymentMethodId", "PaymentMethodType", "Status"], "TableData": [{"PrimaryKey": {"S": "TRANSACTION-01b00f98-d11a-4a2f-9c9d-573c94b24584"}, "ScanKey": {"S": "ACCOUNT-532b91c2-a2e2-4dd7-ac93-f236bc8284fd"}, "GSIndex1PrimaryKey": {"S": "ACCOUNT-532b91c2-a2e2-4dd7-ac93-f236bc8284fd"}, "GSIndex1ScanKey": {"S": "TRANSACTION#COMPLETED#2020-02-11T15:33:00.196810"}, "BankTransactionId": {"N": *********}, "BillId": {"S": "23793381286002041466496000063303381630000002007"}, "Created": {"S": "2020-02-11 15:33:00"}, "Nsu": {"N": 66}, "PaymentMethodId": {"S": "5711c513-6454-40aa-b0a8-27df9fbdc6e2"}, "PaymentMethodType": {"S": "CREDIT_CARD"}, "Status": {"S": "COMPLETED"}, "TotalAmount": {"N": 2007}, "Type": {"S": "BILL_PAYMENT"}}], "DataAccess": {"MySql": {}}}, {"FacetName": "Recipient", "KeyAttributeAlias": {"PartitionKeyAlias": "RecipientId", "SortKeyAlias": "AccountId"}, "NonKeyAttributes": ["GSIndex1PrimaryKey", "GSIndex1ScanKey", "Created", "Document", "<PERSON><PERSON>", "Name", "BankAccounts"], "TableData": [{"PrimaryKey": {"S": "RECIPIENT-7e8d8d4c-0a7f-4d4f-ac30-2025774cc9e8"}, "ScanKey": {"S": "ACCOUNT-8e8d8d4c-0a3f-4d4f-ac30-2025954cc9e8"}, "GSIndex1PrimaryKey": {"S": "ACCOUNT-8e8d8d4c-0a3f-4d4f-ac30-2025954cc9e8"}, "GSIndex1ScanKey": {"S": "RECIPIENT#***********"}, "Created": {"S": "2019-10-22 07:50:41"}, "Document": {"S": "***********"}, "Alias": {"S": "<PERSON><PERSON>"}, "Name": {"S": "Name"}, "BankAccounts": {"L": [{"M": {"AccountDv": {"S": "0"}, "AccountNo": {"S": "922346"}, "BankNo": {"S": "212"}, "Id": {"S": "BANK_ACCOUNT-497e59e8-46f0-47ae-98dd-50025797efeb"}, "RoutingNo": {"S": "1"}, "Type": {"S": "CHECKING"}}}]}}], "DataAccess": {"MySql": {}}}, {"FacetName": "UniqueConstraint", "KeyAttributeAlias": {"PartitionKeyAlias": "UniqueField", "SortKeyAlias": "FIELD_NAME#AccountID"}, "NonKeyAttributes": ["GSIndex1PrimaryKey", "GSIndex1ScanKey", "Bill<PERSON>d"], "TableData": [{"PrimaryKey": {"S": "23793381286002041466496000063303381630000002007"}, "ScanKey": {"S": "DIGITABLE#ACCOUNT-8e8d8d4c-0a3f-4d4f-ac30-2025954cc9e8"}, "BillId": {"S": "BILL-8e8d8d4c-0a3f-4d4f-ac30-2025954cc9e8"}}], "DataAccess": {"MySql": {}}}], "DataAccess": {"MySql": {}}}], "ModelMetadata": {"DateCreated": "Feb 12, 2020, 6:31 PM", "DateLastModified": "Feb 12, 2020, 6:31 PM"}}