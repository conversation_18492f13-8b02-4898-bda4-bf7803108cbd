tenant:
  env: production
  api-subdomain: api-${tenant.name}
  client-subdomain: use-${tenant.name}
  document-mail-address: "seucpf@${tenant.mail-domain}"
  supportEmail: atendimento@${tenant.domain}
  encryption:
    masterkey: "FROM_AWS_SECRETS"

micronaut:
  application:
    name: bill-payment-service
  http:
    services:
      settlement-service:
        url: https://liquidacao.friday.ai
        read-timeout: 1m
        pool:
          max-connections: 30
          enabled: true
      vehicle-debts-service:
        url: https://debitos-veiculares.friday.ai
        pool:
          max-connections: 30
          enabled: true
      arbi:
        url: ${arbi.host}
        read-timeout: 60s
        pool:
          max-connections: 30
          enabled: true
        ssl:
          enabled: true
          isInsecureTrustAllCertificates: true
  metrics:
    enabled: ${micronaut.otel.enabled}
    export:
      statsd:
        enabled: ${micronaut.otel.enabled}
      datadog:
        enabled: ${micronaut.otel.enabled}
    tags:
      application: "${tenant.name}-${micronaut.application.name}"
      provider: ${tenant.name}
      env: ${tenant.env}
  session:
    http:
      redis:
        value-serializer: io.micronaut.jackson.serialize.JacksonObjectSerializer
  executors:
    scheduled:
      type: scheduled
      core-pool-size: 50
  security:
    enabled: true
    authentication: cookie
    token:
      jwt:
        enabled: true
        generator:
          access-token-expiration: 720000
        cookie:
          enabled: true
          login-success-target-url: ${LOGIN_SUCCESS_TARGET_URL}
          login-failure-target-url: ${LOGIN_FAILURE_TARGET_URL}
          cookie-same-site: Lax
          cookie-secure: true
          cookie-domain: .via1.app
        signatures:
          jwks:
            awscognito:
              url: ${JWKS_COGNITO_GIGU_URL}
          secret:
            generator:
              secret: ${jwt.friday.secret}
              jws-algorithm: HS256
  router:
    versioning:
      enabled: true
      default-version: 2
      header:
        enabled: true
        names:
          - 'X-API-VERSION'
  server:
    cors:
      enabled: true
      configurations:
        web:
          allowed-origins:
            - https://${tenant.client-subdomain}.${tenant.domain}
          allowed-methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS

arbi.host: "https://gapp.bancoarbi.com.br"

endpoints:
  caches:
    enabled: true
    sensitive: false
  env:
    enabled: true
    sensitive: false

accountRegister:
  user_files:
    bucket: ${application.accountNumber}-${tenant.name}-user-documents

communication-centre:
  email:
    bucket-unprocessed-emails: ${application.accountNumber}-${tenant.name}-ses-unprocessed-emails
    virus:
      bucket: ${application.accountNumber}-${tenant.name}-quarantine-emails

receipt:
  bucketName: ${application.accountNumber}-${tenant.name}-bill-receipts

urls:
  termsOfUse: "https://${tenant.name}-bill-payment-public.s3.amazonaws.com/termo_de_uso/termo_de_uso_friday_btg_2024_05_10.pdf"

sqs:
  signUpQueueName: ${tenant.name}-sign-up-users
  emailQueueName: ${tenant.name}-incoming-emails
  rollbackTransactionQueueName: ${tenant.name}-bill_payment_rollback_transaction
  walletEventsQueueName: ${tenant.name}-wallet-events-queue
  accountEventsQueueName: ${tenant.name}-account-events-queue
  sqsWaitTime: 20
  dlqArn: ${sqs.arnPrefix}${tenant.name}-bill_events_dlq

  # Filas com prefixo de tenant
  startCashIn: ${tenant.name}-cash-in-start
  createGoalInvestment: ${tenant.name}-goal-create-investment
  incomeReport: ${tenant.name}-bill_payment_income_report
  optimizeGoalInstallmentAmount: ${tenant.name}-bill_payment_goal_optimize_installment_amount
  checkGoalCompletion: ${tenant.name}-bill_payment_goal_check_completion
  goalInvestmentSynchronize: ${tenant.name}-bill_payment_goal_investment_synchronize
  eventBusQueueName: ${tenant.name}-event-bus
  addBillFromEmailQueueName: ${tenant.name}-add-bill-from-email
  sendToManualFlowEmailQueueName: ${tenant.name}-send-to-manual-flow-email-queue-name
  chatbotAiMessageQueueName: ${tenant.name}-chatbot_ai_message
  utilityRequestFlowQueueName: ${tenant.name}-utility-api-flow-request
  utilityRequestFlowConnectQueueName: ${tenant.name}-utility-api-flow-request-connect
  utilityFlowResponseQueueName: ${tenant.name}-utility-api-flow-response
  invalidateBankAccountQueueName: ${tenant.name}-bank-account-invalidation
  validateBoletoJaBaixadoQueueName: ${tenant.name}-validate_boleto_ja_baixado
  registerPixKeyQueueName: ${tenant.name}-register_pix_key_queue_name
  billPaymentSchedulingQueueName: ${tenant.name}-bill_payment_scheduling
  bankAccountDepositQueueName: ${tenant.name}-bank_account_deposit
  settlementRequestQueueName: ${tenant.name}-settlement-friday-request
  settlementResponseQueueName: ${tenant.name}-settlement-friday-response
  statementQueueName: ${tenant.name}-statement-request
  billEventNotificationQueueName: ${tenant.name}-bill_event_notification
  billReceiptGeneratorQueueName: ${tenant.name}-bill_receipt_generator
  settlementFundsTransferQueueName: ${tenant.name}-settlement_funds_transfer
  updateScheduledBillQueueName: ${tenant.name}-update_scheduled_bill_queue_name
  trackableBillQueueName: ${tenant.name}-trackable-bill-queue
  bill-tracking.calculate: ${tenant.name}-bill-tracking-calculate
  bill-tracking.query: ${tenant.name}-bill-tracking-query
  ddaQueueName: ${tenant.name}-dda-processing
  billPaymentScheduledQueueName: ${tenant.name}-bill_payment_scheduled
  userJourneyQueueName: ${tenant.name}-user_journey
  ddaBatchAddOrdersQueueName: ${tenant.name}-dda_batch_add_orders
  ddaBillsInternalQueueName: ${tenant.name}-dda-bills-internal
  ddaFullImportQueueName: ${tenant.name}-dda_full_import
  revenueCatQueueName: ${tenant.name}-revenue_cat_event
  ddaBatchMigrationOrdersQueueName: ${tenant.name}-dda_batch_migration_orders
  billComingDueQueueName: ${tenant.name}-bill_coming_due
  billComingDueNotificationQueueName: ${tenant.name}-bill_coming_due_notification
  billComingDueLastWarnNotificationQueueName: ${tenant.name}-bill_coming_due_last_warn_notification
  billCategorySuggestionQueueName: ${tenant.name}-bill_category_suggestion
  setBillCategoryQueueName: ${tenant.name}-set_bill_category
  vehicleDebtsQueueName: ${tenant.name}-vehicle_debts
  vehicleDebtsEnrichmentQueueName: ${tenant.name}-vehicle_debts_enrichment
  createDefaultCategories: ${tenant.name}-create_default_categories
  simpleSignUpQueueName: ${tenant.name}-simple_sign_up_pending_data
  ddaBills: ${tenant.name}-dda-bills
  concessionariaDiretoActiveBills: ${tenant.name}-concessionaria-bills
  concessionariaDiretoInactiveBills: ${tenant.name}-concessionaria-inactive-bills
  billPaidActivityQueueName: ${tenant.name}-bill_paid_activity
  subscriptionBillPaidQueueName: ${tenant.name}-subscription_bill_paid
  passwordCreatedQueueName: ${tenant.name}-password_created
  closeAccountQueueName: ${tenant.name}-close_account
  closeExternalAccountQueueName: ${tenant.name}-close_external_account
  notifyReminderQueueName: ${tenant.name}-notify_reminder
  notifyExpiredReminderQueueName: ${tenant.name}-notify_expired_reminder
  summaryQueueName: ${tenant.name}-summary_request
  updateConcessionariaStatusQueueName: ${tenant.name}-update_concessionaria_status
  anonymousIdInAppSubscriptionQueueName: ${tenant.name}-anonymous_subscription
  openFinanceQueueName: ${tenant.name}-open-finance-queue
  createBillComingDueByIdQueueName: ${tenant.name}-create_bill_coming_due_by_id
  whatsappFlowRegisterLicensePlate: ${tenant.name}-whatsapp-flow-register_license_plate

  payment:
    process-scheduled-bills: ${tenant.name}-process_scheduled_bills

  queues:
    synchronizeBankAccount: ${tenant.name}-synchronize_bank_account
    whatsappNotificationVerifier: ${tenant.name}-whatsapp_notification_verifier
    systemActivityBillPaidHandler: ${tenant.name}-system-activity-bill-paid
    notifyBillComingDue: ${tenant.name}-notify_bill_coming_due
    pixDepositNotification: ${tenant.name}-pix_deposit_notification
    pushNotification: ${tenant.name}-bill_payment_push_notification
    cashInReceived: ${tenant.name}-cash_in_received
    userEvent: ${tenant.name}-user_events
    automaticPixRefresh: ${tenant.name}-bill_payment_automatic_pix_refresh
    automaticPixCallback: ${tenant.name}-bill_payment_automatic_pix_callback

friday.morning:
  messaging:
    consumer:
      bank-account-deposit:
        queueName: ${tenant.name}-bank_account_deposit
      bill-notification:
        queueName: ${tenant.name}-bill_notification
      bill-payment-started:
        queueName: ${tenant.name}-bill_payment_started
      check-sweeping-cash-in-status:
        queueName: ${tenant.name}-check_sweeping_cash_in_status
      one-pix-pay-requested:
        queueName: ${tenant.name}-one_pix_pay_requested
      account-reconciliation-report:
        queueName: ${tenant.name}-bill_payment_account_reconciliation_report
        dlqArn: arn:aws:sqs:${application.region}:${application.accountNumber}:${tenant.name}-bill_payment_account_reconciliation_report_dlq
      automatic-pix-refresh:
        queueName: ${tenant.name}-bill_payment_automatic_pix_refresh
      automatic-pix-callback:
        queueName: ${tenant.name}-bill_payment_automatic_pix_callback
      automatic-pix-approve-recurrence:
        queueName: ${tenant.name}-bill_payment_automatic_pix_approve_recurrence
      pix-deposit-notification:
        queueName: ${tenant.name}-pix_deposit_notification
      push-notification:
        queueName: ${tenant.name}-bill_payment_push_notification

boleto:
  occurrences:
    automation:
      queue: ${tenant.name}-boleto-occurrences

bill-tracking:
  calculate:
    queue: ${tenant.name}-bill-tracking-calculate
  query:
    queue: ${tenant.name}-bill-tracking-query

integrations:
  settlement:
    request-protocol: HTTP
  revenueCat:
    dlqArn: arn:aws:sqs:${application.region}:${application.accountNumber}:${tenant.name}-revenuecat_dlq
  vehicle-debts:
    dlqArn: arn:aws:sqs:${application.region}:${application.accountNumber}:${tenant.name}-vehicle_debts_dlq

sns:
  topics:
    bill-event:
      name: ${tenant.name}-bill-events-${tenant.env}
    wallet-events:
      name: ${tenant.name}-wallet-events-${tenant.env}
    account-events:
      name: ${tenant.name}-account-events-${tenant.env}
    event-bus:
      name: ${tenant.name}-event-bus-${tenant.env}
    incoming-emails:
      name: ${tenant.name}-incoming-emails-${tenant.env}
  billEventTopicArn: ${sns.arnPrefix}${sns.topics.bill-event.name}
  walletEventTopicArn: ${sns.arnPrefix}${sns.topics.wallet-events.name}
  accountEventTopicArn: ${sns.arnPrefix}${sns.topics.account-events.name}
  eventBusTopicArn: ${sns.arnPrefix}${sns.topics.event-bus.name}
  incomingEmailsTopicArn: ${sns.arnPrefix}${sns.topics.incoming-emails.name}
  sms:
    maxPrice: 20.00

redis:
  uri: redis://${tenant.name}-bill-payment-cache.friday.internal:6379
  cache:
    value-serializer: io.micronaut.jackson.serialize.JacksonObjectSerializer
  caches:
    balance:
      charset: 'UTF-8'
      expire-after-write: 2m
    find-pix-key-details:
      charset: 'UTF-8'
      expire-after-write: 5m
      expire-after-access: 5m

email:
  sender:
    email: notificacoes${email.domain}
    display-name: Notificações ${tenant.displayName}
  return-path: bounce${email.domain}
  bucket-unprocessed-emails: ${application.accountNumber}-${tenant.name}-ses-unprocessed-emails
  configuration-set-name: notification_sender_configuration_set
  notification:
    email: notificacoes${email.domain}
    display-name: Notificações ${tenant.displayName}
  receipt:
    email: comprovantes${email.domain}
    display-name: Comprovantes ${tenant.displayName}
  newUpgradeAccount:
    subject: "[upgrade de conta está pendente de aprovação interna] - %s"
    message: |
      Um upgrade de conta está pendente de aprovação interna:
      Id: %s
      Nome: %s
      Email: %s
  new-account:
    pendingInternalApprove:
      sensitive-recipients: ${email.newAccount.pendingInternalApprove.recipients}
    pendingInternalReview:
      sensitive-recipients: ${email.newAccount.pendingInternalApprove.recipients}
    pendingActivation:
      sensitive-recipients: ${email.newAccount.pendingInternalApprove.recipients}

subscription:
  description: "%s"
  recipientName: Assinatura ${tenant.displayName}

jwtValidation:
  providers:
    msisdn:
      issuer: ${jwt.friday.issuer}
      audience:
        - "phone"

deeplink-url: "https://${tenant.client-subdomain}/app/"

chatbotai:
  sendMessageProtocol: HTTP

email-notification:
  emailVerificationTokenSubject: "${tenant.displayName} - Verificação de email"
  emailPasswordRecoveryTokenSubject: "${tenant.displayName} - Recuperação de Senha"

lock:
  tableName: ${tenant.name}-Shedlock

dynamodb:
  billPaymentTableName: ${tenant.name}-BillPayment
  billEventsTableName: ${tenant.name}-BillEvents
  userEventsTableName: ${tenant.name}-UserEvents
#  billsSearchTableName: ${tenant.name}-BillsSearch
#  openFinanceDataTableName: ${tenant.name}-OpenFinanceData
#  billPaymentTableName: Via1-BillPayment
#  billEventsTableName: Via1-BillEvents
#  userEventsTableName: Via1-UserEvents
  billsSearchTableName: Via1-BillsSearch
  openFinanceDataTableName: Friday-OpenFinanceData

jackson:
  deserialization:
    fail-on-unknown-properties: false
    read-unknown-enum-values-using-default-value: true