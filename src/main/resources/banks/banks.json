{"1": {"name": "Banco do Brasil S.A.", "displayName": "001 - Banco do Brasil S.A"}, "10": {"name": "Cc Credicoamo Credito Rural Cooperativa", "displayName": "010 - Cc Credicoamo Credito Rural Cooperativa"}, "100": {"name": "<PERSON>", "displayName": "100 - <PERSON>"}, "101": {"name": "Dtvm Renascenca", "displayName": "101 - Dtvm Renascenca"}, "102": {"name": "Xp Investimentos Corretora", "displayName": "102 - X<PERSON> Investimentos Corretora"}, "103": {"name": "Ebs Capital Corretora de Cambio S.A.", "displayName": "103 - Ebs Capital Corretora de Cambio S.A"}, "104": {"name": "Caixa Economica Federal", "displayName": "104 - Caixa Economica Federal"}, "105": {"name": "Lecca Credito Financiamento e Investimento S.A.", "displayName": "105 - Lecca Credito Financiamento e Investimento S.A"}, "107": {"name": "Banco Bbm S.A.", "displayName": "107 - Banco Bbm S.A"}, "108": {"name": "Portocred S A Credito Financiamento e Investimento", "displayName": "108 - Portocred S A Credito Financiamento e Investimento"}, "109": {"name": "Credibanco S.A.", "displayName": "109 - Credibanco S.A"}, "11": {"name": "Credit Suisse Hedging-Griffo Corretora de Valores", "displayName": "011 - Credit Suisse Hedging-Griffo Corretora de Valores"}, "111": {"name": "Dtvm Oliveira Trust", "displayName": "111 - Dtvm Oliveira Trust"}, "112": {"name": "Cc Unicred Brasil Central", "displayName": "112 - <PERSON><PERSON> Unicred Brasil Central"}, "113": {"name": "Magliano S.A", "displayName": "113 - Magliano S.A"}, "114": {"name": "Cecoopes-Central Das Coop de Econ e Cred Mutuo Do", "displayName": "114 - Cecoopes-Central Das Coop de Econ e Cred Mutuo Do"}, "115": {"name": "Rotula S.A.", "displayName": "115 - Rotula S.A"}, "116": {"name": "Banco Bnl do Brasil S.A.", "displayName": "116 - Banco Bnl do Brasil S.A"}, "117": {"name": "Advanced Corretora de Cambio Ltda", "displayName": "117 - Advanced Corretora de Cambio Ltda"}, "118": {"name": "Standard Chartered Bank Brasil S.A. - Banco de Inv", "displayName": "118 - Standard Chartered Bank Brasil S.A. - Banco de Inv"}, "119": {"name": "Banco Western Union do Brasil S.A.", "displayName": "119 - Banco Western Union do Brasil S.A"}, "120": {"name": "Banco Rodobens S.A.", "displayName": "120 - Banco Rodobens S.A"}, "121": {"name": "Banco Gerador S.A.", "displayName": "121 - Banco Gerador S.A"}, "122": {"name": "Banco Berj S.A.", "displayName": "122 - Banco Berj S.A"}, "123": {"name": "Agiplan Financeira S.A.", "displayName": "123 - Agiplan Financeira S.A"}, "124": {"name": "Banco Woori Bank do Brasil S.A.", "displayName": "124 - Banco Woori Bank do Brasil S.A"}, "125": {"name": "Brasil Plural S.A.", "displayName": "125 - Brasil Plural S.A"}, "126": {"name": "Br Partners Banco de Investimento S.A.", "displayName": "126 - Br Partners Banco de Investimento S.A"}, "127": {"name": "Codepe - Corretora de Valores S.A.", "displayName": "127 - Codepe - Corretora de Valores S.A"}, "128": {"name": "Bcam Msb Bank", "displayName": "128 - Bcam Msb Bank"}, "129": {"name": "Bi Ubs Brasil", "displayName": "129 - <PERSON><PERSON>"}, "13": {"name": "<PERSON>", "displayName": "013 - <PERSON>"}, "130": {"name": "Caruana S.A. Sociedade de Cr0dito Financiamento E", "displayName": "130 - Caruana S.A. Sociedade de Cr0dito Financiamento E"}, "131": {"name": "<PERSON>", "displayName": "131 - <PERSON>"}, "132": {"name": "Icbc do Brasil Banco M0ltiplo S.A.", "displayName": "132 - Icbc do Brasil Banco M0ltiplo S.A"}, "133": {"name": "Confederacao Nacional Das Cooperativas Centrais De", "displayName": "133 - Confederacao Nacional Das Cooperativas Centrais De"}, "134": {"name": "Dtvm Bgc Liquidez", "displayName": "134 - Dtvm Bgc Liquidez"}, "135": {"name": "Sc Gradual", "displayName": "135 - <PERSON>"}, "136": {"name": "Cc Unicred do Brasil", "displayName": "136 - <PERSON><PERSON> <PERSON>icred do <PERSON>"}, "137": {"name": "Sc Multimoney", "displayName": "137 - <PERSON>"}, "138": {"name": "Turmalina Corretora de Cambio S.A.", "displayName": "138 - Turmalina Corretora de Cambio S.A"}, "139": {"name": "Intesa Sanpaolo Brasil S.A.", "displayName": "139 - <PERSON><PERSON><PERSON>paolo Brasil S.A"}, "14": {"name": "Natixis Brasil S.A.", "displayName": "014 - Natixis Brasil S.A"}, "142": {"name": "Broker Brasil Corretora de Cambio Ltda.", "displayName": "142 - Broker Brasil Corretora de Cambio Ltda"}, "143": {"name": "Sc Treviso", "displayName": "143 - <PERSON>"}, "144": {"name": "Bexs Banco de Cambio S.A.", "displayName": "144 - Bexs Banco de Cambio S.A"}, "145": {"name": "<PERSON>", "displayName": "145 - <PERSON>"}, "146": {"name": "Guitta Corretora de Cambio Ltda", "displayName": "146 - Guitta Corretora de Cambio Ltda"}, "147": {"name": "Rico Corretora de Titulos e Valores Mobiliarios S.A.", "displayName": "147 - Rico Corretora de Titulos e Valores Mobiliarios S.A"}, "148": {"name": "Bank of America - Brasil S.A.", "displayName": "148 - Bank of America - Brasil S.A"}, "149": {"name": "Scfi Facta Financeira", "displayName": "149 - Scfi Facta Financeira"}, "15": {"name": "Sc Ubs Brasil", "displayName": "015 - <PERSON> Ubs Brasil"}, "157": {"name": "Icap do Brasil Corretora de Tit. Val. Mobiliarios", "displayName": "157 - Icap do Brasil Corretora de Tit. Val. Mobiliarios"}, "159": {"name": "Casa do Credito S.A. Sociedade de Credito Ao Micro", "displayName": "159 - Casa do Credito S.A. Sociedade de Credito Ao Micro"}, "16": {"name": "Cooperativa de Cr0dito M0tuo Dos Despachantes de T", "displayName": "016 - Cooperativa de Cr0dito M0tuo Dos Despachantes de T"}, "163": {"name": "Commerzbank Brasil S.A.", "displayName": "163 - Commerzbank Brasil S.A"}, "165": {"name": "Banco Norchem S.A.", "displayName": "165 - Banco Norchem S.A"}, "166": {"name": "Banco Inter-Atlantico S.A.", "displayName": "166 - Banco Inter-Atlantico S.A"}, "167": {"name": "S Hayata Corretora de Cambio S.A.", "displayName": "167 - S Hayata Corretora de Cambio S.A"}, "168": {"name": "Hsbc Investment Bank Brasil S.A.-Banco M", "displayName": "168 - Hsbc Investment Bank Brasil S.A.-Banco M"}, "169": {"name": "Bm Ole Bonsucesso Consignado S.A.", "displayName": "169 - Bm <PERSON> Consignado S.A"}, "17": {"name": "Bny Mellon S.A.", "displayName": "017 - Bny Mellon S.A"}, "172": {"name": "Albatross Corretora de Cambio e Value S.A.", "displayName": "172 - Albatross Corretora de Cambio e Value S.A"}, "173": {"name": "Brl Trust", "displayName": "173 - Brl Trust"}, "174": {"name": "Pernambucanas Financiadora S.A. Credito, Financiam", "displayName": "174 - Pernambucanas Financiadora S.A. <PERSON>, Financiam"}, "175": {"name": "Banco Finasa S.A.", "displayName": "175 - Banco Finasa S.A"}, "177": {"name": "Guide Investimentos S.A. Corretora de Valores", "displayName": "177 - Guide Investimentos S.A. Corretora de Valores"}, "18": {"name": "Bm Tricury S.A.", "displayName": "018 - Bm Tricury S.A"}, "180": {"name": "Sc Cm Capital Markets", "displayName": "180 - Sc Cm Capital Markets"}, "182": {"name": "Dacasa Financeira S.A.", "displayName": "182 - Dacasa Financeira S.A"}, "183": {"name": "Socred S.A. - Sociedade de Credito Ao Microempreen", "displayName": "183 - Socred S.A. - Sociedade de Credito Ao Microempreen"}, "188": {"name": "Ativa Investimentos S.A.", "displayName": "188 - Ativa Investimentos S.A"}, "189": {"name": "Hs Financeira S.A.", "displayName": "189 - Hs Financeira S.A"}, "19": {"name": "Banco Azteca do Brasil S.A.", "displayName": "019 - Banco Azteca do Brasil S.A"}, "190": {"name": "Servicoop - Cooperativa de Credito Dos Servidores", "displayName": "190 - Ser<PERSON><PERSON> - Cooperativa de Credito Dos Servidores"}, "191": {"name": "Nova Futura", "displayName": "191 - Nova Futura"}, "194": {"name": "Parmetal", "displayName": "194 - <PERSON><PERSON><PERSON>"}, "196": {"name": "Fair Corretora de Cambio S.A.", "displayName": "196 - Fair Corretora de Cambio S.A"}, "197": {"name": "Stone Pagamentos S.A.", "displayName": "197 - <PERSON>.<PERSON>"}, "199": {"name": "Banco Financial Portugues", "displayName": "199 - Banco Financial Portugues"}, "20": {"name": "Banco do Estado de Alagoas S.A.", "displayName": "020 - Banco do Estado de Alagoas S.A"}, "200": {"name": "Banco Ficrisa <PERSON> S.A.", "displayName": "200 - <PERSON><PERSON>"}, "201": {"name": "Banco Axial S.A.", "displayName": "201 - Banco Axial S.A"}, "204": {"name": "Banco Inter American Express S.A.", "displayName": "204 - Banco Inter American Express S.A"}, "205": {"name": "Banco Sul America S.A.", "displayName": "205 - Banco Sul America S.A"}, "206": {"name": "Banco <PERSON> S.A.", "displayName": "206 - <PERSON><PERSON> S.A"}, "208": {"name": "Banco BTG Pactual S.A.", "displayName": "208 - Banco BTG Pactual S.A"}, "21": {"name": "Banestes S.A. Banco do Estado do Espirito Santo", "displayName": "021 - Banestes S.A. Banco do Estado do Espirito Santo"}, "210": {"name": "Dresdner Bank Lateinamerika Aktiengesell", "displayName": "210 - Dresdner Bank Lateinamerika Aktiengesell"}, "211": {"name": "Banco Sistema S.A.", "displayName": "211 - Banco Sistema S.A"}, "212": {"name": "Banco Original S.A.", "displayName": "212 - Banco Original S.A"}, "213": {"name": "Banco Arbi S.A.", "displayName": "213 - Banco Arbi S.A"}, "214": {"name": "Banco Dibens S.A.", "displayName": "214 - Banco Dibens S.A"}, "215": {"name": "Banco Comercial e de Investimento Sudame", "displayName": "215 - Banco Comercial e de Investimento Sudame"}, "216": {"name": "Banco Regional Malcon S.A.", "displayName": "216 - Banco Regional Malcon S.A"}, "217": {"name": "Banco John Deere S.A.", "displayName": "217 - Ban<PERSON> John <PERSON> S.A"}, "218": {"name": "Banco Bonsucesso S.A.", "displayName": "218 - Banco Bonsucesso S.A"}, "219": {"name": "Banco Zogbi S.A.", "displayName": "219 - Banco <PERSON> S.A"}, "22": {"name": "Credireal -Em Absorcao", "displayName": "022 - <PERSON><PERSON><PERSON>al -Em Absorcao"}, "220": {"name": "Banco Crefisul S.A.", "displayName": "220 - <PERSON><PERSON>A"}, "221": {"name": "Banco Chase Fleming S.A.", "displayName": "221 - Banco Chase Fleming S.A"}, "222": {"name": "Banco Credit Lyonnais Brasil S.A.", "displayName": "222 - Banco Credit Lyonnais Brasil S.A"}, "224": {"name": "Banco Fibra S.A.", "displayName": "224 - Banco Fibra S.A"}, "225": {"name": "Banco Brascan S.A.", "displayName": "225 - Banco Brascan S.A"}, "228": {"name": "Banco Icatu S.A.", "displayName": "228 - Banco Icatu S.A"}, "229": {"name": "Banco Cruzeiro do Sul S.A.", "displayName": "229 - Banco Cruzeiro do Sul S.A"}, "230": {"name": "Banco Bandeirantes S.A.", "displayName": "230 - Banco Bandeirantes S.A"}, "231": {"name": "Banco Boavista Interatlantico S.A-Em Abs", "displayName": "231 - Banco Boavista Interatlantico S.A-Em Abs"}, "232": {"name": "Banco Interpart S.A.", "displayName": "232 - Banco Interpart S.A"}, "233": {"name": "Banco Ge Capital S.A.", "displayName": "233 - Banco Ge Capital S.A"}, "234": {"name": "Banco Lavra S.A.", "displayName": "234 - Banco Lavra S.A"}, "235": {"name": "Bank of America - Liberal S.A.", "displayName": "235 - Bank of America - Liberal S.A"}, "236": {"name": "Banco Cambial S.A.", "displayName": "236 - Banco Cambial S.A"}, "237": {"name": "Banco Bradesco S.A.", "displayName": "237 - Banco Bradesco S.A"}, "239": {"name": "Banco Bancred S.A.", "displayName": "239 - Banco Bancred S.A"}, "24": {"name": "Banco de Pernambuco S.A.-Bandepe", "displayName": "024 - Banco de Pernambuco S.A.-Bandepe"}, "240": {"name": "Banco de Credito Real de Minas Gerais S.", "displayName": "240 - Banco de Credito Real de Minas Gerais S"}, "241": {"name": "Banco Classico S.A.", "displayName": "241 - Banco Classico S.A"}, "242": {"name": "Banco Euroinvest S.A. Eurobanco", "displayName": "242 - Banco Euroinvest S.A. Eurobanco"}, "243": {"name": "Banco Maxima S.A.", "displayName": "243 - Banco Maxima S.A"}, "244": {"name": "Banco Cidade S.A.", "displayName": "244 - Banco Cidade S.A"}, "245": {"name": "Banco Empresarial S.A.", "displayName": "245 - Banco Empresarial S.A"}, "246": {"name": "Banco Abc Brasil S.A.", "displayName": "246 - Banco Abc Brasil S.A"}, "247": {"name": "Banco Ubs S.A.", "displayName": "247 - Banco Ubs S.A"}, "248": {"name": "Banco Boa Vista Interatlantico S.A.", "displayName": "248 - Banco Boa Vista Interatlantico S.A"}, "249": {"name": "Banco Investcred Unibanco S.A.", "displayName": "249 - Banco Investcred Unibanco S.A"}, "25": {"name": "Banco Alfa S.A.", "displayName": "025 - Banco Alfa S.A"}, "250": {"name": "Banco Schahin S.A.", "displayName": "250 - Banco Schahin S.A"}, "252": {"name": "Banco Fininvest S.A.", "displayName": "252 - Banco Fininvest S.A"}, "253": {"name": "Bexs Corretora de Cambio S.A.", "displayName": "253 - Bexs Corretora de Cambio S.A"}, "254": {"name": "Parana Banco S.A.", "displayName": "254 - Parana Banco S.A"}, "255": {"name": "Milbanco S.A.", "displayName": "255 - Milbanco S.A"}, "256": {"name": "Banco Gulfinvest S.A.", "displayName": "256 - Banco Gulfinvest S.A"}, "258": {"name": "Banco Induscred S.A.", "displayName": "258 - Banco Induscred S.A"}, "26": {"name": "Banco do Estado do Acre S.A.", "displayName": "026 - Banco do Estado do Acre S.A"}, "260": {"name": "Nu Pagamentos S.A.", "displayName": "260 - <PERSON><PERSON> Pagamentos S.A"}, "262": {"name": "Banco Boreal S.A.", "displayName": "262 - Banco <PERSON> S.A"}, "263": {"name": "Banco Cacique S.A.", "displayName": "263 - Banco Cacique S.A"}, "265": {"name": "Banco Fator S.A.", "displayName": "265 - Banco Fator S.A"}, "266": {"name": "Banco Cedula S.A.", "displayName": "266 - Banco Cedula S.A"}, "267": {"name": "Banco Bbm-Com.C.Imob.Cfi S.A.", "displayName": "267 - Banco Bbm-Com.C.Imob.Cfi S.A"}, "268": {"name": "Barigui Companhia Hipotecaria", "displayName": "268 - Barigui Companhia Hipotecaria"}, "269": {"name": "Hsbc Brasil S.A. Banco de Investimento", "displayName": "269 - Hsbc Brasil S.A. Banco de Investimento"}, "27": {"name": "Banco do Estado de Santa Catarina S.A.", "displayName": "027 - Banco do Estado de Santa Catarina S.A"}, "270": {"name": "Sagitur Corretora de Cambio Ltda.", "displayName": "270 - Sagitur Corretora de Cambio Ltda"}, "271": {"name": "<PERSON><PERSON> Cambio, Titulos e Value Mobiliar", "displayName": "271 - <PERSON><PERSON>bio, Titulos e Value Mobiliar"}, "272": {"name": "Agk Corretora de Cambio S.A.", "displayName": "272 - Agk Corretora de Cambio S.A"}, "273": {"name": "Cooperativa de Credito Rural de Sao Miguel do Oest", "displayName": "273 - Cooperativa de Credito Rural de Sao Miguel do Oest"}, "274": {"name": "Money Plus Sociedade de Credito Ao Microempreended", "displayName": "274 - Money Plus Sociedade de Credito Ao Microempreended"}, "276": {"name": "Senff S.A.", "displayName": "276 - <PERSON>ff <PERSON>"}, "277": {"name": "Banco Planibanc S.A.", "displayName": "277 - Banco Planibanc S.A"}, "278": {"name": "Genial Investimentos Corretora de Valores Mobiliar", "displayName": "278 - Genial Investimentos Corretora de Valores Mobiliar"}, "279": {"name": "Cooperativa de Credito Rural de Primavera do Leste", "displayName": "279 - Cooperativa de Credito Rural de Primavera do Leste"}, "28": {"name": "Baneb-Em Absorcao", "displayName": "028 - <PERSON><PERSON><PERSON><PERSON> Absorcao"}, "280": {"name": "Avista S.A.", "displayName": "280 - Avista S.A"}, "281": {"name": "Cooperativa de Credito Rural Coopavel", "displayName": "281 - Cooperativa de Credito Rural Coopavel"}, "282": {"name": "Banco Brasileiro Comercial S.A.", "displayName": "282 - Banco Brasileiro Comercial S.A"}, "283": {"name": "Rb Capital", "displayName": "283 - Rb Capital"}, "285": {"name": "Frente Corretora de Cambio Ltda.", "displayName": "285 - Frente Corretora de Cambio Ltda"}, "286": {"name": "Cooperativa de Credito Rural de Ouro Sulcredi/Ouro", "displayName": "286 - Cooperativa de Credito Rural de Ouro Sulcredi/Ouro"}, "288": {"name": "<PERSON>", "displayName": "288 - <PERSON>"}, "289": {"name": "Decyseo Corretora de Cambio Ltda.", "displayName": "289 - <PERSON><PERSON>eo Corretora de Cambio Ltda"}, "290": {"name": "Pagseguro Internet S.A.", "displayName": "290 - Pagseguro Internet S.A"}, "291": {"name": "Banco Bcn S.A.", "displayName": "291 - Banco Bcn S.A"}, "292": {"name": "Bs2", "displayName": "292 - Bs2"}, "293": {"name": "Lastro Rdv", "displayName": "293 - Lastro Rdv"}, "294": {"name": "Bcr/Em Absorcao", "displayName": "294 - Bcr/Em Absorcao"}, "296": {"name": "Vision S.A. Corretora de Cambio", "displayName": "296 - Vision S.A. Corretora de Cambio"}, "298": {"name": "Vip'S Corretora de Cambio Ltda.", "displayName": "298 - Vip'S Corretora de Cambio Ltda"}, "299": {"name": "Sorocred", "displayName": "299 - <PERSON><PERSON><PERSON><PERSON>"}, "3": {"name": "Banco da Amazonia S.A.", "displayName": "003 - Banco da Amazonia S.A"}, "30": {"name": "Paraiban-Banco da Paraiba S.A.", "displayName": "030 - Paraiban-Banco da Paraiba S.A"}, "300": {"name": "Banco de La Nacion Argentina", "displayName": "300 - Banco de La Nacion Argentina"}, "301": {"name": "Bpp Instituicao de Pagamento S.A.", "displayName": "301 - Bpp Instituicao de Pagamento S.A"}, "302": {"name": "Banco do Progresso S.A.", "displayName": "302 - Banco do Progresso S.A"}, "303": {"name": "Banco Hnf S.A.", "displayName": "303 - Banco Hnf S.A"}, "304": {"name": "Banco Pontual S.A.", "displayName": "304 - Banco Pontual S.A"}, "306": {"name": "Portopar", "displayName": "306 - Portopar"}, "307": {"name": "Terra Investimentos", "displayName": "307 - <PERSON> Investimentos"}, "309": {"name": "Cambionet Corretora de Cambio Ltda.", "displayName": "309 - Cambionet Corretora de Cambio Ltda"}, "31": {"name": "Banco Beg S.A.", "displayName": "031 - Banco Beg S.A"}, "310": {"name": "Vortx", "displayName": "310 - Vortx"}, "315": {"name": "Pi", "displayName": "315 - Pi"}, "318": {"name": "Banco Bmg S.A.", "displayName": "318 - Banco Bmg S.A"}, "319": {"name": "OM", "displayName": "319 - OM"}, "32": {"name": "Banco do Estado do Mato Grosso S.A.", "displayName": "032 - Banco do Estado do Mato Grosso S.A"}, "320": {"name": "Banco Industrial e Comercial S.A.", "displayName": "320 - Banco Industrial e Comercial S.A"}, "321": {"name": "Crefaz Sociedade de Credito Ao Microempreendedor E", "displayName": "321 - Crefaz Sociedade de Credito Ao Microempreendedor E"}, "322": {"name": "Cooperativa de Credito Rural de Abelardo Luz - Sul", "displayName": "322 - Cooperativa de Credito Rural de Abelardo Luz - Sul"}, "323": {"name": "Mercadopago.Com Representacoes Ltda.", "displayName": "323 - Mercadopago.Com Representacoes Ltda"}, "325": {"name": "Orama", "displayName": "325 - <PERSON><PERSON>"}, "329": {"name": "Qi Sociedade de Credito Direto S.A.", "displayName": "329 - Qi Sociedade de Credito Direto S.A"}, "33": {"name": "Banco Santander S.A.", "displayName": "033 - Banco Santander S.A"}, "330": {"name": "Banco Bari de Investimentos e Financiamentos S.A.", "displayName": "330 - Banco Bari de Investimentos e Financiamentos S.A"}, "331": {"name": "Fram Capital", "displayName": "331 - Fram Capital"}, "332": {"name": "Acesso Solucoes de Pagamento S.A.", "displayName": "332 - Acesso <PERSON> de Pagamento S.A"}, "336": {"name": "Banco C6 S.A.", "displayName": "336 - Banco C6 S.A"}, "34": {"name": "Banco Bea S.A.", "displayName": "034 - Banco Bea S.A"}, "340": {"name": "Super Pagamentos e Administracao de Meios Eletroni", "displayName": "340 - Super Pagamentos e Administracao de Meios Eletroni"}, "341": {"name": "Banco Itau S.A.", "displayName": "341 - Banco Itaú S.A"}, "342": {"name": "Creditas Sociedade de Credito Direto S.A.", "displayName": "342 - Creditas Sociedade de Credito Direto S.A"}, "347": {"name": "Banco Sudameris do Brasil S.A.", "displayName": "347 - Banco Sudameris do Brasil S.A"}, "349": {"name": "Amaggi S.A.", "displayName": "349 - Amaggi S.A"}, "35": {"name": "Banco do Estado do Ceara S.A. Bec", "displayName": "035 - Banco do Estado do Ceara S.A. Bec"}, "355": {"name": "Otimo Sociedade de Credito Direto S.A.", "displayName": "355 - Otimo Sociedade de Credito Direto S.A"}, "356": {"name": "Banco Abn Amro S.A.", "displayName": "356 - Banco Abn Amro S.A"}, "36": {"name": "Banco do Estado do Maranhao S.A.-Bem", "displayName": "036 - Banco do Estado do Maranhao S.A.-Bem"}, "364": {"name": "Gerencianet Pagamentos do Brasil", "displayName": "364 - Gerencianet Pagamentos do Brasil"}, "366": {"name": "Banco Societe Generale Brasil S.A.", "displayName": "366 - Banco Societe Generale Brasil S.A"}, "369": {"name": "Banco Digibanco S.A.", "displayName": "369 - Banco Digibanco S.A"}, "37": {"name": "Banco do Estado do Para S.A.", "displayName": "37 - Banco do Estado do Para S.A"}, "370": {"name": "Banco Westlb do Brasil  S.A.", "displayName": "370 - Banco Westlb do Brasil  S.A"}, "372": {"name": "Banco Itamarati S.A.", "displayName": "372 - Banco Itamarati S.A"}, "375": {"name": "Banco Fenicia S.A.", "displayName": "375 - Banco Fenicia S.A"}, "376": {"name": "Banco J.P. Morgan S.A.", "displayName": "376 - Banco J.P. Morgan S.A"}, "38": {"name": "Banco Banestado S.A.", "displayName": "38 - Banco Banestado S.A"}, "388": {"name": "Banco Bmd S.A.", "displayName": "388 - Banco Bmd S.A"}, "389": {"name": "Banco Mercantil do Brasil S.A.", "displayName": "389 - Banco Mercantil do Brasil S.A"}, "39": {"name": "Banco do Estado do Piaui S.A.-Bep", "displayName": "39 - Banco do Estado do Piaui S.A.-Bep"}, "392": {"name": "Banco Mercantil de Sao Paulo S.A.", "displayName": "392 - Banco Mercantil de Sao Paulo S.A"}, "394": {"name": "Banco Bmc S.A.", "displayName": "394 - Banco Bmc S.A"}, "399": {"name": "Hsbc Bank Brasil S.A.", "displayName": "399 - Hsbc Bank Brasil S.A"}, "4": {"name": "Banco do Nordeste do Brasil S.A.", "displayName": "004 - Banco do Nordeste do Brasil S.A"}, "40": {"name": "Banco Cargill S.A.", "displayName": "040 - Banco Cargill S.A"}, "409": {"name": "Unibanco - Uniao de Bancos Brasileiros S", "displayName": "409 - <PERSON><PERSON><PERSON> - Uniao de Bancos Brasileiros S"}, "41": {"name": "Banco do Estado do Rio Grande do Sul S.A.", "displayName": "041 - Banco do Estado do Rio Grande do Sul S.A"}, "412": {"name": "Banco Capital S.A.", "displayName": "412 - Banco Capital S.A"}, "415": {"name": "Banco Nacional S.A.", "displayName": "415 - Banco Nacional S.A"}, "42": {"name": "Banco J. Safra S.A.", "displayName": "042 - Banco J. Safra S.A"}, "420": {"name": "Banorte - Banco Nacional do Norte S.A.", "displayName": "420 - Banorte - Banco Nacional do Norte S.A"}, "422": {"name": "Banco Safra S.A.", "displayName": "422 - Banco Safra S.A"}, "434": {"name": "Banfort - Banco Fortaleza S.A.", "displayName": "434 - Banfort - Banco Fortaleza S.A"}, "44": {"name": "Banco Bva S.A.", "displayName": "044 - Banco Bva S.A"}, "45": {"name": "Banco Opportunity S.A.", "displayName": "045 - Banco Opportunity S.A"}, "453": {"name": "Banco Rural S.A.", "displayName": "453 - Banco Rural S.A"}, "456": {"name": "Banco de Tokyo Mitsubishi Brasil S.A.", "displayName": "456 - Banco de Tokyo Mitsubishi Brasil S.A"}, "464": {"name": "Banco Sumitomo Mi<PERSON>i <PERSON>eiro S.A.", "displayName": "464 - Banco Sumitomo <PERSON>eiro S.A"}, "466": {"name": "Banco Mitsubishi S.A.", "displayName": "466 - Banco Mitsubishi S.A"}, "47": {"name": "Banco do Estado de Sergipe S.A.", "displayName": "047 - Banco do Estado de Sergipe S.A"}, "472": {"name": "Lloyds Tsb Bank Plc", "displayName": "472 - Lloyds Tsb Bank Plc"}, "473": {"name": "Banco Financial Portugues-Em Absorcao", "displayName": "473 - Banco Financial Portugues-Em Absorcao"}, "479": {"name": "Bankboston S.A.", "displayName": "479 - Bankboston S.A"}, "48": {"name": "Banco Bemge S.A.", "displayName": "048 - Banco Bemge S.A"}, "480": {"name": "Banco Wachovia S.A.", "displayName": "480 - Banco Wachovia S.A"}, "487": {"name": "Deutsche Bank S. A.", "displayName": "487 - Deutsche Bank S. A"}, "488": {"name": "Jpmorgan Chase Bank", "displayName": "488 - Jpmorgan Chase Bank"}, "489": {"name": "Banco Frances Internacional-Brasil S.A.", "displayName": "489 - Banco Frances Internacional-Brasil S.A"}, "492": {"name": "Ing Bank N.V.", "displayName": "492 - Ing Bank N.V"}, "493": {"name": "Banco Union - Brasil S.A.", "displayName": "493 - Banco Union - Brasil S.A"}, "494": {"name": "Banco de La Republica Oriental Del Urugu", "displayName": "494 - Banco de La Republica Oriental Del Urugu"}, "495": {"name": "Banco de La Provincia de Buenos Aires", "displayName": "495 - Banco de La Provincia de Buenos Aires"}, "496": {"name": "Banco Uno-E Brasil S.A.", "displayName": "496 - Banco Uno-E Brasil S.A"}, "497": {"name": "Banco Hispano Americano S.A.", "displayName": "497 - Banco Hispano Americano S.A"}, "498": {"name": "Centro Hispano Banco", "displayName": "498 - Centro Hispano Banco"}, "499": {"name": "Banco Iochpe S.A.", "displayName": "499 - Banco Iochpe S.A"}, "500": {"name": "Banco Habitasul S.A.", "displayName": "500 - Banco Habitasul S.A"}, "501": {"name": "Banco Brasileiro Iraquiano S.A.", "displayName": "501 - Banco Brasileiro Iraquiano S.A"}, "504": {"name": "Banco Multiplic S.A.", "displayName": "504 - Banco Multiplic S.A"}, "505": {"name": "Banco Credit Suisse First Boston S.A.", "displayName": "505 - Banco Credit Suisse First Boston S.A"}, "545": {"name": "<PERSON>", "displayName": "545 - <PERSON>"}, "59": {"name": "Banco do Estado de Rondonia S.A.", "displayName": "059 - Banco do Estado de Rondonia S.A"}, "60": {"name": "Sc Confidence", "displayName": "060 - Sc Confidence"}, "600": {"name": "Banco Luso Brasileiro S.A.", "displayName": "600 - Banco Luso Brasileiro S.A"}, "602": {"name": "Banco Patente S.A.", "displayName": "602 - Banco Patente S.A"}, "604": {"name": "Banco Industrial do Brasil S. A.", "displayName": "604 - Banco Industrial do Brasil S. A"}, "607": {"name": "Banco Santos Neves S.A.", "displayName": "607 - Banco Santos Neves S.A"}, "61": {"name": "Banco Abb S.A.", "displayName": "061 - Banco Abb S.A"}, "610": {"name": "Banco Vr S.A.", "displayName": "610 - Banco Vr S.A"}, "611": {"name": "Banco Paulista S.A.", "displayName": "611 - Banco Paulista S.A"}, "612": {"name": "Banco Guanabara S.A.", "displayName": "612 - Banco Guanabara S.A"}, "613": {"name": "Banco Pecunia S.A.", "displayName": "613 - Banco Pecunia S.A"}, "618": {"name": "Banco Tendencia S.A.", "displayName": "618 - Banco Tendencia S.A"}, "62": {"name": "Banco1.Net S.A.", "displayName": "062 - Banco1.Net S.A"}, "621": {"name": "Banco Aplicap S.A.", "displayName": "621 - Banco Aplicap S.A"}, "623": {"name": "Banco Panamericano S.A.", "displayName": "623 - Banco Panamericano S.A"}, "624": {"name": "Banco General Motors S.A.", "displayName": "624 - Banco General Motors S.A"}, "625": {"name": "Banco Araucaria S.A.", "displayName": "625 - Banco Araucaria S.A"}, "626": {"name": "Banco Ficsa S.A.", "displayName": "626 - Banco Ficsa S.A"}, "627": {"name": "Banco Destak S.A.", "displayName": "627 - Banco Destak S.A"}, "628": {"name": "Banco Criterium S. A.", "displayName": "628 - Banco Criterium S. A"}, "63": {"name": "Banco Ibi S.A. -", "displayName": "063 - Banco Ibi S.A. "}, "630": {"name": "Banco Intercap S.A.", "displayName": "630 - Banco Intercap S.A"}, "631": {"name": "Banco Columbia S.A.", "displayName": "631 - Banco Columbia S.A"}, "633": {"name": "Banco Rendimento S.A.", "displayName": "633 - Banco Rendimento S.A"}, "634": {"name": "Banco Triangulo S.A.", "displayName": "634 - Banco Triangulo S.A"}, "635": {"name": "Banco do Estado Amapa S.A.", "displayName": "635 - Banco do Estado Amapa S.A"}, "637": {"name": "Banco Sofisa S.A.", "displayName": "637 - Banco Sofisa S.A"}, "638": {"name": "Banco Prosper S.A.", "displayName": "638 - Banco Prosper S.A"}, "64": {"name": "Goldman Sachs do Brasil S", "displayName": "064 - Goldman Sachs do Brasil S"}, "640": {"name": "Banco Credito Metropolitano S.A.", "displayName": "640 - Banco Credito Metropolitano S.A"}, "641": {"name": "Banco Bilbao Vizcaya Argentaria Brasil S", "displayName": "641 - Banco Bilbao Vizcaya Argentaria Brasil S"}, "643": {"name": "Banco Pine S.A.", "displayName": "643 - Banco Pine S.A"}, "645": {"name": "Banco do Estado de Roraima S.A.", "displayName": "645 - Banco do Estado de Roraima S.A"}, "647": {"name": "Banco Marka S.A.", "displayName": "647 - Banco Marka S.A"}, "649": {"name": "Banco Dimensao S.A.", "displayName": "649 - Banco Dimensao S.A"}, "65": {"name": "Banco Bracce S.A.", "displayName": "065 - Banco Bracce S.A"}, "650": {"name": "Banco Pebb S.A.", "displayName": "650 - Banco Pebb S.A"}, "652": {"name": "Banco Frances e Bras. S.A.", "displayName": "652 - Banco Frances e Bras. S.A"}, "653": {"name": "Banco Indusval S.A.", "displayName": "653 - Banco Indusval S.A"}, "654": {"name": "Banco A.J. Renner S.A.", "displayName": "654 - Banco A.J. Renner S.A"}, "655": {"name": "Banco Votorantim S.A.", "displayName": "655 - Banco Votorantim S.A"}, "656": {"name": "Banco Matrix S.A.", "displayName": "656 - Banco Matrix S.A"}, "657": {"name": "Banco Tecnicorp S.A.", "displayName": "657 - Banco Tecnicorp S.A"}, "658": {"name": "Banco Porto Real S.A.", "displayName": "658 - Banco Porto Real S.A"}, "66": {"name": "Banco Morgan Stanley Dean Witter S.A.", "displayName": "066 - Banco Morgan Stanley Dean Witter S.A"}, "67": {"name": "Banco Baneb S.A.", "displayName": "067 - Banco Baneb S.A"}, "68": {"name": "Banco Bea S.A.", "displayName": "068 - Banco Bea S.A"}, "69": {"name": "Bpn Brasil S.A.", "displayName": "069 - Bpn Brasil S.A"}, "7": {"name": "Banco Nacional do Desenvolvimento Econom", "displayName": "007 - Banco Nacional do Desenvolvimento Econom"}, "70": {"name": "Brb - Banco de Brasilia S.A.", "displayName": "070 - Brb - Banco de Brasilia S.A"}, "702": {"name": "Banco Santos S. A.", "displayName": "702 - Banco Santos S. A"}, "705": {"name": "Banco Investcorp S.A.", "displayName": "705 - Banco Investcorp S.A"}, "707": {"name": "Banco Daycoval S.A.", "displayName": "707 - Ban<PERSON>coval S.A"}, "711": {"name": "Banco Vetor S.A.", "displayName": "711 - Banco Vetor S.A"}, "712": {"name": "Bm Ourinvest", "displayName": "712 - <PERSON><PERSON> Ourinvest"}, "713": {"name": "Banco Cindam S.A.", "displayName": "713 - Banco Cindam S.A"}, "715": {"name": "Banco Vega S.A.", "displayName": "715 - Banco Vega S.A"}, "718": {"name": "Banco Operador S.A.", "displayName": "718 - Banco Operador S.A"}, "719": {"name": "Banco Banif Primus S.A.", "displayName": "719 - Banco Banif Primus S.A"}, "72": {"name": "Banco Rural Mais S.A.", "displayName": "072 - Banco Rural Mais S.A"}, "720": {"name": "Banco Maxinvest S.A.", "displayName": "720 - Banco Maxinvest S.A"}, "721": {"name": "Banco Credibel S.A.", "displayName": "721 - Banco Credibel S.A"}, "722": {"name": "Banco Interior de Sao Paulo S.A.", "displayName": "722 - Banco Interior de Sao Paulo S.A"}, "725": {"name": "Banco Finansinos S. A.", "displayName": "725 - Banco Finansinos S. A"}, "728": {"name": "Banco Fital S.A.", "displayName": "728 - Banco Fital S.A"}, "729": {"name": "Banco Fonte Cindam S.A.", "displayName": "729 - Banco Fonte Cindam S.A"}, "732": {"name": "Banco Minas S.A.", "displayName": "732 - Banco Minas S.A"}, "733": {"name": "Banco Das Nacoes S.A.", "displayName": "733 - Banco Das Nacoes S.A"}, "734": {"name": "Banco Gerdau S.A.", "displayName": "734 - Banco Gerdau S.A"}, "735": {"name": "Banco Neon S.A.", "displayName": "735 - Banco Neon S.A"}, "737": {"name": "Banco Theca S.A.", "displayName": "737 - Banco Theca S.A"}, "738": {"name": "Banco Morada S.A.", "displayName": "738 - Banco Morada S.A"}, "739": {"name": "Banco Bgn S.A.", "displayName": "739 - Banco Bgn S.A"}, "74": {"name": "Banco J. Safra S.A.", "displayName": "074 - Banco J. Safra S.A"}, "740": {"name": "Banco Barclays S.A.", "displayName": "740 - Banco Barclays S.A"}, "741": {"name": "Banco Ribeirao Preto S.A.", "displayName": "741 - Banco Ribeirao Preto S.A"}, "742": {"name": "Banco Equatorial S.A.", "displayName": "742 - Banco Equatorial S.A"}, "743": {"name": "Banco Emblema S.A.", "displayName": "743 - Banco Emblema S.A"}, "744": {"name": "Bankboston N.A.", "displayName": "744 - Bankboston N.A"}, "745": {"name": "Banco Citibank S.A.", "displayName": "745 - Banco Citibank S.A"}, "746": {"name": "Banco Modal S.A.", "displayName": "746 - Banco Modal S.A"}, "747": {"name": "Banco Rabobank International Brasil S.A.", "displayName": "747 - Banco Rabobank International Brasil S.A"}, "748": {"name": "Banco Cooperativo Sicredi S.A. - Bansicr", "displayName": "748 - Banco Cooperativo Sicredi S.A. - Bansicr"}, "749": {"name": "Br Banco Mercantil S.A. (Simples)", "displayName": "749 - Br Banco Mercantil S.A. (Simples"}, "75": {"name": "Banco Cr2 S.A.", "displayName": "075 - Banco Cr2 S.A"}, "750": {"name": "Hsbc Republic Bank Brasil S.A-Banco Mult", "displayName": "750 - Hsbc Republic Bank Brasil S.A-Banco Mult"}, "751": {"name": "Dresdner Bank Brasil S.A.", "displayName": "751 - Dresdner Bank Brasil S.A"}, "752": {"name": "Banco Bnp Paribas Brasil S.A.", "displayName": "752 - Banco Bnp Paribas Brasil S.A"}, "753": {"name": "Banco Comercial Uruguai S.A.", "displayName": "753 - Banco Comercial Uruguai S.A"}, "754": {"name": "Sistema", "displayName": "754 - Si<PERSON><PERSON>"}, "755": {"name": "Banco Merrill Lynch S.A.", "displayName": "755 - Banco Merrill Lynch S.A"}, "756": {"name": "Banco Cooperativo do Brasil S.A.", "displayName": "756 - Banco Cooperativo do Brasil S.A"}, "757": {"name": "Banco Keb do Brasil S.A.", "displayName": "757 - Banco Keb do Brasil S.A"}, "76": {"name": "Banco Kdb do Brasil S.A.", "displayName": "076 - Banco Kdb do Brasil S.A"}, "77": {"name": "Banco Intermedium S.A.", "displayName": "077 - Banco Intermedium S.A"}, "78": {"name": "Bes Investimento do Brasil S.A.", "displayName": "078 - <PERSON><PERSON> Inves<PERSON> do Brasil S.A"}, "79": {"name": "Jbs Banco S.A.", "displayName": "079 - Jbs Banco S.A"}, "80": {"name": "Sc Bt Associados", "displayName": "080 - Sc Bt Associados"}, "800": {"name": "Bcr Banco de Credito Real S.A.", "displayName": "800 - Bcr Banco de Credito Real S.A"}, "81": {"name": "Concordia Banco S.A.", "displayName": "081 - Concordia Banco S.A"}, "82": {"name": "Banco Topázio S.A.", "displayName": "082 - Banco Topázio S.A"}, "83": {"name": "Banco da China Brasil S.A.", "displayName": "083 - Banco da China Brasil S.A"}, "84": {"name": "Cc Unicred Norte do Parana", "displayName": "084 - Cc Unicred Norte do Parana"}, "85": {"name": "Cooperativa Central de Credito Urbano - Cecred", "displayName": "085 - Cooperativa Central de Credito Urbano - Cecred"}, "86": {"name": "Oboe S.A.", "displayName": "086 - Oboe S.A"}, "87": {"name": "Unicred Central Santa Catarina", "displayName": "087 - Unicred Central Santa Catarina"}, "88": {"name": "Banco Randon S.A.", "displayName": "088 - Banco Randon S.A"}, "880": {"name": "Incentive House", "displayName": "880 - Incentive House"}, "884": {"name": "Empresa Brasileira de Tecnologia e Adm de Convenio", "displayName": "884 - Empresa Brasileira de Tecnologia e Adm de Convenio"}, "89": {"name": "Cooperativa de Credito Rural da Regiao da Mogiana", "displayName": "089 - Cooperativa de Credito Rural da Regiao da Mogiana"}, "9": {"name": "Bacen", "displayName": "009 - <PERSON><PERSON><PERSON>"}, "90": {"name": "Cooperativa Central de Credito do Estado de Sp", "displayName": "090 - Cooperativa Central de Credito do Estado de Sp"}, "901": {"name": "Sc Souza Barros", "displayName": "901 - <PERSON>"}, "91": {"name": "Central de Cooperativas de Economia e Credito Mutu", "displayName": "091 - Central de Cooperativas de Economia e Credito Mutu"}, "92": {"name": "Brickell S.A.", "displayName": "092 - Brickell S.A"}, "93": {"name": "Scm Polocred", "displayName": "093 - <PERSON><PERSON> Polocred"}, "94": {"name": "Banco Petra S.A.", "displayName": "094 - Banco Petra S.A"}, "95": {"name": "Bcam Confidence", "displayName": "095 - Bcam Confidence"}, "96": {"name": "Banco Bm&F de Serv. de Liquidação e Custodia S.A.", "displayName": "096 - Banco Bm&F de Serv. de Liquidação e Custodia S.A"}, "97": {"name": "Cooperativa Central de Credito Noroeste Brasileiro", "displayName": "097 - Cooperativa Central de Credito Noroeste Brasileiro"}, "98": {"name": "Credicorol Cooperativa de Credito Rural", "displayName": "098 - Credicorol Cooperativa de Credito Rural"}, "99": {"name": "Cooperativa Central Economia e Credito Mutuo Pr/Ms", "displayName": "099 - Cooperativa Central Economia e Credito Mutuo Pr/Ms"}, "991": {"name": "Bbbiro", "displayName": "991 - <PERSON><PERSON><PERSON>"}, "992": {"name": "<PERSON><PERSON><PERSON>", "displayName": "992 - <PERSON><PERSON><PERSON>"}, "993": {"name": "Bureau Bcn", "displayName": "993 - Bureau Bcn"}, "994": {"name": "Bureau Nacional", "displayName": "994 - Bureau Nacional"}, "995": {"name": "Bureau Abbc", "displayName": "995 - Bureau Abbc"}, "996": {"name": "Bureau Febraban", "displayName": "996 - <PERSON>"}, "997": {"name": "Associacao de Bancos Estaduais", "displayName": "997 - Associacao de Bancos Estaduais"}, "998": {"name": "Tecnologia Bancaria S.A.", "displayName": "998 - Tecnologia Bancaria S.A"}}