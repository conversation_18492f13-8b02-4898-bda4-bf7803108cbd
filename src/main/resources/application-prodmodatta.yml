micronaut:
  server:
    cors:
      enabled: true
      configurations:
        web:
          allowed-origins-regex: '^https:\/\/(use-modatta\.friday\.ai|friday-api\.modatta\.org)(|.)$'
  caches:
    balance:
      charset: 'UTF-8'
      expire-after-write: 5m
    find-pix-key-details:
      charset: 'UTF-8'
      expire-after-write: 5m
      expire-after-access: 5m
  executors:
    scheduled:
      type: scheduled
      core-pool-size: 50
  http:
    services:
      arbi:
        url: https://gapp.bancoarbi.com.br
        read-timeout: 60s
        pool:
          max-connections: 30
          enabled: true
        ssl:
          enabled: true
          isInsecureTrustAllCertificates: true
  security:
    enabled: true
    token:
      jwt:
        enabled: true
        signatures:
          jwks:
            modatta:
              url: 'https://friday.modatta.org/api/v1/friday/jwkeys'

application:
  region: us-east-1
  accountNumber: ************

integrations:
  friday:
    host: 'https://api.via1.app'
    paths:
      balance-amount: /b2b/balance/amount/{accountId}
      simple-sign-up-validate: /b2b/simpleSignUp/validate
      simple-sign-up: /b2b/simpleSignUp
      createPix: /b2b/bill/pix
      accountStatus: /b2b/accountStatus/{accountId}
  arbi:
    pixCheckoutPoolingInterval: 5000 #miliseconds
  modatta:
    host: https://friday-api.modatta.org
    paths:
      notify-cash-in: /friday/b2b/notify/cashin
      get-balance: /friday/b2c/{userId}/balance
      redeem: /friday/b2c/redeem
      notify-account-status-changed: /friday/b2c/status/changed

features:
  forwardEmailToManualWorkflow: false
  automaticUserRegister: true
  zeroAuthEnabled: false
  creditCardChallenge: true

jwtValidation:
  providers:
    modatta:
      audience:
        - https://use-modatta.friday.ai
      issuer: "https://friday.modatta.org/api/v1/friday/gettoken"

cnpj:
  modatta: **************

modattaService:
  bankNo: 213
  routingNo: 1
  fullAccountNo: 333333 # FIXME
  document: ${cnpj.modatta}

sns:
  billEventTopicArn: arn:aws:sns:${application.region}:${application.accountNumber}:bill-events
  walletEventTopicArn: arn:aws:sns:${application.region}:${application.accountNumber}:wallet-events
  sms:
    maxPrice: 1.00

baas:
  name: "MODATTA E MODATTA BRASIL LTDA"
  document: ${cnpj.modatta}
  bankNo: 213
  routingNo: 1
  allowedIps:
    - "*************" # Hugo
    - "**************" # João
    - "**************" # Alexandre
    - "**************" # Enzo
    - "************" # Hugo segundo ip
    - ************* # mo-prod-friday-api
    - **************
    - **************
    - *************
    - *************
    - **************
    - *************
    - **************
    - **************
    - **************
    - **************
    - *************
    - *************
    - **************
    - **************
    - *************
    - *************
    - **************
    - **************
    - **************
    - **************
    - *************
    - *************
    - *************
    - *************
    - **************
    - **************
    - *************
    - *************
    - **************
    - *************
    - **************
    - **************
    - **************
    - **************
    - **************
    - ************
    - ************* # mo-prod-app-api
    - **************
    - **************
    - *************
    - *************
    - **************
    - *************
    - **************
    - **************
    - **************
    - **************
    - *************
    - *************
    - **************
    - **************
    - *************
    - *************
    - **************
    - **************
    - **************
    - **************
    - *************
    - *************
    - *************
    - *************
    - **************
    - **************
    - *************
    - *************
    - **************
    - *************
    - **************
    - **************
    - **************
    - **************
    - **************
    - ************
    - ************* #mo-prod-admin-api
    - **************
    - **************
    - *************
    - *************
    - **************
    - *************
    - **************
    - **************
    - **************
    - **************
    - *************
    - *************
    - **************
    - **************
    - *************
    - *************
    - **************
    - **************
    - **************
    - **************
    - *************
    - *************
    - *************
    - *************
    - **************
    - **************
    - *************
    - *************
    - **************
    - *************
    - **************
    - **************
    - **************
    - **************
    - **************
    - ************
    - ************* #mo-prod-activities-fnc
    - **************
    - **************
    - *************
    - *************
    - **************
    - *************
    - **************
    - **************
    - **************
    - **************
    - *************
    - *************
    - **************
    - **************
    - *************
    - *************
    - **************
    - **************
    - **************
    - **************
    - *************
    - *************
    - *************
    - *************
    - **************
    - **************
    - *************
    - *************
    - **************
    - *************
    - **************
    - **************
    - **************
    - **************
    - **************
    - ************

accountRegister:
  user_files:
    modattaUserDocumentsBucket: ************-modatta-user-documents

modatta-corp:
  identity: iNvOiSTErnisTRICildpolorKInoLicO
  secret: FROM_SECRETS

disable:
  cognito: true
  credit-card-risk-analisys: true