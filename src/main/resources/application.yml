tenant:
  id: FRIDAY
  env: production
  name: friday
  displayName: Friday
  chatbotName: Fred
  supportEmail: <EMAIL>
  encryption:
    masterkey: "FROM_AWS_SECRETS"
  allowedOnboardingAssistantIds:
    - "ACCOUNT-a2e87c5c-8d84-44e2-ae3c-f5e68de20818"
    - "ACCOUNT-c933005c-b017-4db0-ab08-71afca18e840"

micronaut:
  application:
    name: bill-payment-service
  http:
    client:
      read-timeout: 30s
      connection-pool-idle-timeout: 10s
    services:
      chatbot:
        url: ${integrations.chatbotai.host}
        read-timeout: 5s
      vehicle-debts-service:
        url: https://debitos-veiculares.friday.ai
        read-timeout: 120s
        pool:
          max-connections: 20
          enabled: true
      whatsapp-client:
        read-timeout: 10s
        url: ${communication-centre.integration.blip.host}
        pool:
          max-connections: 20
          enabled: true
      software-express:
        read-timeout: 35s
        url: ${integrations.softwareExpress.url}
        pool:
          enabled: true
          max-connections: 20
      clearsale:
        url: ${integrations.clearsale.host}
        read-timeout: 30s
        pool:
          max-connections: 20
          enabled: true
      openfinance:
        url: ${integrations.openfinance.host}
        read-timeout: 65s
  otel:
    enabled: true
  metrics:
    enabled: true
    export:
      statsd:
        enabled: true
      cloudwatch:
        enabled: true
        namespace: fridayBillPayment
        batchSize: 20
    tags:
      application: ${micronaut.application.name}
      provider: ${tenant.name}
      env: ${tenant.env}
  caches:
    revenuecat:
      charset: 'UTF-8'
      expire-after-write: 6h
    wallet:
      charset: 'UTF-8'
      expire-after-write: 1m
      maximum-size: 10000
    features:
      charset: 'UTF-8'
      expire-after-write: 30m
    arbi:
      charset: 'UTF-8'
      expire-after-write: 6h
    arbiPix:
      charset: 'UTF-8'
      expire-after-write: 1m
    financial-institutions:
      charset: 'UTF-8'
      expire-after-write: 1h
    pix-keys:
      charset: 'UTF-8'
      expire-after-write: 10m
    new-pix-keys:
      charset: 'UTF-8'
      expire-after-write: 10m
    update-trackable-bill:
      charset: 'UTF-8'
      expire-after-write: 24h
    categories:
      charset: 'UTF-8'
      expire-after-write: 24h
    index-interest-rates:
      charset: 'UTF-8'
      expire-after-write: 30m
    compare-index-interest-rates:
      charset: 'UTF-8'
      expire-after-write: 30m

  server:
    ssl:
      enabled: true
      buildSelfSigned: true
    netty:
      listeners:
        httpsListener:
          port: 8443
          ssl: true
      worker:
        event-loop-group: other
      http2:
        header-table-size: 16384
        max-header-list-size: 16384
    cors:
      single-header: true
      enabled: true
      configurations:
        web:
          allowed-origins-regex: '^https:\/\/(use\.via1\.app|use\.friday\.ai)(|.)$'
    max-request-size: ******** #1024L * 1024 * 10
    multipart:
      enabled: true
      disk: false
      mixed: false
      max-file-size: ******** #1024L * 1024 * 10
    http-version: HTTP_2_0
  netty:
    event-loops:
      other:
        num-threads: 10
  security:
    intercept-url-map:
      - pattern: /bill-payment/proxy/**
        access:
          - ADMIN
    authentication: cookie
    enabled: true
    endpoints:
      login:
        enabled: true
    token:
      generator:
        access-token:
          expiration: 86400
      cookie:
        enabled: true
        cookie-domain: .via1.app
        cookie-same-site: Lax
      jwt:
        enabled: true
        login-success-target-url: https://use.via1.app/
        login-failure-target-url: https://use.via1.app/falha-ao-autenticar
        cookie-secure: true
        signatures:
          secret:
            generator:
              secret: ${BILL_PAYMENT_SECRET}
              jws-algorithm: HS256
  router:
    versioning:
      enabled: true
      default-version: 2
      header:
        enabled: true
        names:
          - 'X-API-VERSION'

application:
  region: us-east-1
  accountNumber: ************

modules:
  manual-entry:
    enabled: false
  pfm:
    enabled: false
  chatbot-ai:
    enabled: true
  event-api:
    enabled: false
  push-notification:
    enabled: true
  in-app-subscription-coupon:
    enabled: false
  automatic-pix:
    enabled: true

kafka:
  enabled: false

dynamodb:
  billPaymentTableName: Via1-BillPayment
  openFinanceDataTableName: Friday-OpenFinanceData
  userEventsTableName: Via1-UserEvents
  billsSearchTableName: Via1-BillsSearch
  billEventsTableName: Via1-BillEvents
  region: ${application.region}

whatsappflow:
  privateKey: FROM_AWS_SECRETS
  salt: FROM_AWS_SECRETS

aws:
  region: ${application.region}

deeplink-url: "https://use.friday.ai/app/"

endpoints:
  health:
    enabled: true
    sensitive: false
    details-visible: ANONYMOUS
    disk-space:
      enabled: false
  metrics:
    enabled: true
    sensitive: false

integrations:
  adjust:
    host: "https://s2s.adjust.com"
    appToken: "lkt1b385tmgw"
    eventTokens:
      accountActivated:
        token: "ab41ra"
        revenues:
          ios:
            amount: 4905
            currency: BRL
          android:
            amount: 3078
            currency: BRL
      onboardingTestPixCreated:
        token: "s83tjo"
      trialStarted:
        token: "bq98a7"
        revenues:
          ios:
            amount: 3141
            currency: BRL
          android:
            amount: 1660
            currency: BRL
      trialConverted:
        token: "ew3i4h"
        revenues:
          ios:
            amount: 15000
            currency: BRL
          android:
            amount: 15000
            currency: BRL
  revenueCat:
    host: "https://api.revenuecat.com"
    projectId: "d4b62f03"
    dlqArn: arn:aws:sqs:${application.region}:${application.accountNumber}:revenuecat_dlq
    secretKey: FROM_AWS_SECRETS
    secretKeyV1: FROM_AWS_SECRETS
  api:
    basic-credentials: FROM_AWS_SECRETS
  whatsapp:
    auth: ${communication-centre.integration.blip.auth}
    host: ${communication-centre.integration.blip.host}
    namespace: ${communication-centre.integration.blip.namespace}
  quod:
    host: https://api.quod.com.br
    authReport: /WsAuthenticity/AuthReport?ver_=
    authReportVersion: 1.5
    authCCScore: /WsAuthenticity/AuthCCScore?ver_=
    authCCScoreVersion: 2.1
    username: FROM_AWS_SECRETS
    password: FROM_AWS_SECRETS
  serpro:
    host: https://gateway.apiserpro.serpro.gov.br
    username: FROM_AWS_SECRETS
    password: FROM_AWS_SECRETS
  celcoin:
    enabled: true
    fichaMaxPaymentLimitTime: "21:00"
    host: https://apicorp.celcoin.com.br
    host-da: https://api.openfinance.celcoin.com.br
    username: teste
    password: teste
    conciliation:
      fileTypesPath: /tools-conciliation/v1/exportfile/types
      filePath: /tools-conciliation/v1/exportfile/
      consolidatedStatement: /tools-conciliation/v1/ConsolidatedStatement
    eda:
      utilitiesPath: /eda/v1/utilities
      subscriptionPath: /eda/v1/utilities/subscriptions
      invoicePath: /eda/v1/invoices
    token:
      path: /v5/token
      client_id: AWS_SECRET
      grant_type: client_credentials
      client_secret: AWS_SECRET
    authorize:
      path: /v5/transactions/billpayments/authorize
    payment:
      path: /v5/transactions/billpayments
    balance:
      path: /v5/merchant/balance
    capture:
      path: /v5/transactions/billpayments/{transactionId}/capture
    void:
      path: /v5/transactions/billpayments/{transactionId}/void
    query:
      path: /v5/transactions/status-consult?transactionId={transactionId}
    pendency:
      path: /v5/transactions/pendency
    receipt:
      path: /v5/transactions/receipt/{transactionId}
    banktransfer:
      path: /v5/transactions/banktransfer
    transferstatus:
      path: /v5/transactions/banktransfer/status-transfer/{transactionId}
    revertboletotransaction:
      path: /v5/transactions/billpayments/{transactionId}/reverse
    banks:
      path: /v5/transactions/banks
  cielo:
    host: https://api.cieloecommerce.cielo.com.br
    checkStatusHost: https://apiquery.cieloecommerce.cielo.com.br
    authorizePath: /1/sales/
    checkStatusByMerchantOrderIdPath: /1/sales
    checkStatusByPaymentIdPath: /1/sales/{PaymentId}
    capturePath: /1/sales/{PaymentId}/capture
    cancelPath: /1/sales/orderid/{MerchantOrderId}/void
    tokenizePath: /1/card/
    zeroAuthPath: /1/zeroauth
    cardBinHost: /1/cardBin/{bin}
    credentials:
      MerchantId: ce45e57f-3877-4e92-a938-4871dafb1a1f
      MerchantKey: ZZKJTBGSHQSUDAXEQGIGQAKTYGUPFMYOUUYLHOVB
  manual-workflow:
    emails: <EMAIL>
  softwareExpress:
    url: https://esitef-homologacao.softwareexpress.com.br
    credentials:
      MerchantId: ***************
      MerchantKey: DC37ECA74822C5854B09F2A90CCAB66C993122705B1C26930930F3DECB7B9AD4
  clearsale:
    host: FROM_AWS_SECRETS # https://datatrustapihml.clearsale.com.br or https://datatrustapi.clearsale.com.br
    username: FROM_AWS_SECRETS
    password: FROM_AWS_SECRETS
    trustedCodes:
      - CRT0004
  arbi:
    newAuthHost: https://gapp.bancoarbi.com.br
    newHost: https://gapp.bancoarbi.com.br
    authHost: https://api-bancoarbi.sensedia.com
    host: https://api-bancoarbi.sensedia.com
    grantCodePath: /oauth/grant-code
    accessTokenPath: /oauth/access-token
    validatePath: /pagamentos/v1/pagamentos
    validateV2Path: /pagamentos/v2/pagamentos
    paymentV2Path: /pagamentos/v2/pagamentos
    ddaPath: /pagamentos/v1/dda
    ddaV2Path: /pagamentos/v2/dda
    ddaCadastroPath: /pagamentos/v2/ddaagregado/
    ddaCadastroLotePath: /pagamentos/v2/ddaagregadolote/
    cadastroPFPath: /cadastropf/v2/cadastropf/
    accountStatus: /account-management/api/v2/accountstatus
    domainPath: /dominios/v1/dominios
    domainV2Path: /dominios/v2/dominios
    checkingPath: /contacorrente/v1/contacorrente/
    tedStatusPath: /contacorrente/v1/consultarequisicaotedintegrada/
    checkingV2Path: /contacorrente/v2/contacorrente/
    getStatementV2Path: /contacorrente/v2/contacorrente/
    tedStatusV2Path: /contacorrente/v2/consultarequisicaotedintegrada/
    accountStatementPath: /checking-account/api/v1/accountstatement
    pixKeyPath: /pix/v1/enderecamento/
    pixKeyV2Path: /pix/v2/enderecamento/
    qrCodeProcessPath: /pix/v2/qrcode/processamento/
    findPixKeyPath: /pix/v1/enderecamento/dict/{key}/{cpfCnpj}
    findPixKeyV2Path: /pix/v2/enderecamento/dict/{key}/{cpfCnpj}
    findInternalPixKeysPath: /pix/v1/enderecamento/cliente/lista/{cpfCnpj}
    claimPath: /pix/v1/reivindicacao/
    cancelClaimPath: /pix/v1/reivindicacao/cancelar/
    completeClaimPath: /pix/v1/reivindicacao/concluir/
    confirmClaimPath: /pix/v1/reivindicacao/confirmar/
    findClaimPath: /pix/v1/reivindicacao/{id}
    listClaimsPath: /pix/v1/reivindicacao/lista
    encerrarContaPath: /cadastromanutencao/v1/cadastromanutencao/
    encerrarContaV2Path: /cadastromanutencao/v2/cadastromanutencao/
    cadastroEnderecoPath: /cadastromanutencao/v2/cadastroendereco
    calculatePath: /pagamentos/v1/calculadora/
    devicesPath: /device-manager/api/v1/devices
    userToken: FROM_AWS_SECRETS
    clientId: FROM_AWS_SECRETS
    clientSecret: FROM_AWS_SECRETS
    contaTitular: "0000325513"
    contaLiquidacao: "0000325882"
    contaCashin: "0000326310"
    inscricao: **************
    tipoPessoa: J
    paymentTimeLimit: ${integrations.celcoin.fichaMaxPaymentLimitTime}
    codInstituicaoPagador: ********
    initPaymentPath: /pix/v1/operacao/ordem_pagamento/
    pixStatementPath: /pix/v1/operacao/extrato_pix/
    pixStatementV2Path: /pix/v2/operacao/extrato_pix/
    initPaymentV2Path: /pix/v2/operacao/ordem_pagamento/
    paymentStatusE2EV2Path: /pix/v2/operacao/ordem_pagamento/end_to_end/{e2e}
    paymentStatusIdIdempotentePath: /pix/v1/operacao/ordem_pagamento/id_idempotente/{idIdempotente}
    paymentStatusIdIdempotenteV2Path: /pix/v2/operacao/ordem_pagamento/id_idempotente/{idIdempotente}
    informeDeRendimentosPath: /informerendimentos/v1/geracaopdf
    createPixQrCodePath: /pix/v2/qrcode/estatico/
    pixCheckoutWaitTime: 16000 #miliseconds
    pixCheckoutPoolingInterval: ${integrations.arbi.pixCheckoutWaitTime}
    gatewayV2ContaCorrente: true

    automaticPixBillingPath: /pix-recurring/api/v1/billing
    automaticPixBillingCancelPath: /pix-recurring/api/v1/billing/{endToEnd}/cancel
    automaticPixPendingMandatePath: /pix-recurring/api/v1/authorization/pending
    automaticPixMandatePath: /pix-recurring/api/v1/authorization
    automaticPixRejectMandatePath: /pix-recurring/api/v1/authorization/{id}/reject
    automaticPixApproveMandatePath: /pix-recurring/api/v1/authorization/{id}/approve
    automaticPixRecurringPaymentsStatusPath: /pix-recurring/api/v1/recurring-payments/{id}/status
    automaticPixRecurringPaymentsPath: /pix-recurring/api/v1/recurring-payments
    automaticPixRecurringPaymentsApprovePath: /pix-recurring/api/v1/recurring-payments/{id}/approve
    automaticPixRecurringPaymentsCancelPath: /pix-recurring/api/v1/recurring-payments/{id}/cancel
    automaticPixRecurringPaymentsUpdatePath: /pix-recurring/api/v1/recurring-payments/{id}/update


    ecm:
      client: f5Zv9r7RD1wf0va8Pg5m/A==
      apiKey: kKOeOjFKdB1LPxBYM3yip31WhzLzbk8O6gNtvcez
      username: X5Maw9cKsFl2cD441m20EFecM7MrJvXOXu854d8rAyQ=
      password: IAC0ywm7NS8tiG+tIIDuVQ==
      host: https://api.bonobotec.com.br
      path: /ecm/ged/insert
      processNumber: 50
      processName: Conta Digital Pessoa Física VIA1
      simpleSignUpProcessNumber: 50 # FIXME
      simpleSignUpProcessName: Conta Digital Pessoa Física VIA1 # FIXME
    fepweb:
      host: https://bancoarbi.fepweb.com.br
      username: FROM_AWS_SECRETS
      password: FROM_AWS_SECRETS
      authorizePath: /fepwebws/api/authenticate/authorize
      userSearchPath: /fepwebws/api/dom/user-filler
      userSavePath: /fepwebws/api/dom/user-filler/save
      formSavePath: /fepwebws/api/dom/form/save
      fillFormPath: /fepwebws/api/dom/form/fill
      documentSavePath: /fepwebws/api/dom/form/{formId}/document/save
      sendToApprovalPath: /fepwebws/api/dom/workflow/execute-external-action
      formTemplateExtCode: friday_cc_simples_pf
      productExtCode: friday_cc_pf
      businessUnitExtCode: "96"
  btg:
    host: https://api.developer.btgpactual.com
    clientId: "46da3fc0-5abb-4007-8442-b8c66d35953c"
    clientSecret: "BA0U7PZCSW4H08VFHJ3HTCKGB5KAE4GD0JHD8ZFKVAC79ZPA"
    grantType: client_credentials
    accessTokenPath: /cm/authentication/token
    listOrganizationPath: /bk/organization/account
    createPaymentPath: /bk/receiver/payment/consent/pix/key
    createQrCodePaymentPath: /bk/receiver/payment/consent/pix/qr-code
    createConsentRedirectUrlPath: /bk/receiver/payment/consent/callback-url
    paymentConsentCallbackPath: /bk/receiver/payment/consent/pix/callback
    redirectUrl: "https://use.friday.ai/web/saldo-adicionado-itp"
    itp.static.institutions:
      general-available:
        "********-ec0d-4398-83ce-68b6b1087e49":
          ispb: "********"
          title: "Itaú"
        "b397dd9c-62b2-4632-bce7-85f56666c083":
          ispb: "********"
          title: "BTG Banking"
        "756b9782-d9d4-4f9b-9756-997eba0e2cbd":
          ispb: "********"
          title: "PicPay"
        "aaacb9cf-e8c3-402b-93b8-cf4d3e2ec497":
          ispb: "********"
          title: "Santander"
        "439a9b5c-2cfb-4e57-b60b-20eea83899ca":
          ispb: "********"
          title: "Bradesco"
        "649ffe9a-503d-4635-a0fd-41f4b5135b6c":
          ispb: "********"
          title: "PagBank PagSeguro"
        "6a0ec228-70b7-4292-9e64-8fa731b2a730":
          ispb: "********"
          title: "C6 Bank"
        "9326f9b2-ae57-42c4-a0d9-acc4ba434696":
          ispb: "********"
          title: "Mercado Pago"
        "06c19499-3412-4125-84b7-d0fbc98b5019":
          ispb: "********"
          title: "Nubank"
      alpha:
        "75db457a-612d-4d62-b557-ba9d32b05216":
          ispb: "********"
          title: "Banco do Brasil"
  cognito:
    userPoolId: ${application.region}_YZ2auLIqm
    jwtCookie:
      userPoolClientId: pp586afugc4jpnkf64erp0k0g
  intercom:
    host: https://api.intercom.io
    token: FROM_AWS_SECRETS
    webIdVerificationSecret: FROM_AWS_SECRETS
    iosIdVerificationSecret: FROM_AWS_SECRETS
    androidIdVerificationSecret: FROM_AWS_SECRETS
  bigdatacorp:
    bigBoostHost: https://bigboost.bigdatacorp.com.br
    bigIdHost: https://bigid.bigdatacorp.com.br
    authHost: https://accesstoken.bigdatacorp.com.br
    accessToken: xxx
    peoplePath: /peoplev2
    companyPath: /companies
    addressPath: /addresses
    ocrPath: /VerifyID
    imageQualityPath: /api/ImageQuality
    faceMatchPath: /VerifyID/FaceMatch
    authPath: /Generate
    login: xxx
    password: xxx
    accesTokenExpiration: 1
    minimum-document-size-to-optimize: 4194304 #1024L * 1024 * 4
  firebase:
    host: https://www.google-analytics.com
    measurementId: "G-F890631B43"
    cloud-messaging:
      json: FROM_SECRETS
  userpilot:
    host: https://analytex.userpilot.io
    token: FROM_AWS_SECRETS
    apiVersion: "2020-09-22"
    identifyPath: /v1/identify
    trackEventPath: /v1/track
  documentscan:
    host: ${integrations.liveness.host}
    user: ${integrations.liveness.user}
    password: ${integrations.liveness.password}
    getImagePath: "/internal/document-scan/{documentScanId}/images"
    getScanResultPath: "/internal/document-scan/{documentScanId}"
    upsertDocumentScanPath: "/internal/document-scan"
  liveness:
    host: "https://liveness.friday.ai"
    user: "LIVENESS_CLIENT_ID-271c3477-e22d-47f3-af7d-aaf95ac18b1a"
    password: "Liveness_gateway_pwd_prod_!@#_to_be_replaced"
    enrollmentPath: "/internal/enrollment"
    matchPath: "/internal/match"
    selfiePath: "/internal/enrollment/{livenessId}/auditImage"
    duplicationVerificationPath: "/internal/duplicationVerification/{externalId}"
    hasCompletedEnrollmentPath: "/internal/enrollment/hasCompletedEnrollment/{externalId}"
    verifyMatchPath: "/internal/match/validated/{livenessId}"
    markAsFraudPath: "/internal/fraud"
  modatta:
    host: "https://api-modatta.friday.ai"
    user: "2c771069-9b8a-4390-a894-318ca9ba742a"
    password: AT_AWS_SECRETS
    updateStatusPath: "/callback/simpleSignUp/updateStatus"
    depositPixCallbackPath: "/callback/deposit-pix"
    depositCallbackPath: "/callback/deposit"
  modattaPublic:
    host: "https://pb.modatta.org"
    redeemCodePath: "/external/pb/{code}"
  settlement:
    host: https://liquidacao.friday.ai
    username: FROM_AWS_SECRETS
    password: FROM_AWS_SECRETS
    validatePath: /validate
    requestProtocol: QUEUE
  settlement-service:
    user: ${integrations.settlement.username}
    password: ${integrations.settlement.password}
    strategy: "DISABLED"
  openfinance:
    host: "https://open-finance.friday.ai"
    clientid: "DEFINIR_NO_YML_CORRETO"
    clientsecret: "DEFINIR_NO_YML_CORRETO"
    participants:
      banco-do-brasil:
        name: Banco do Brasil
        shortName: BB
        id: 75db457a-612d-4d62-b557-ba9d32b05216
        compe: 001
        ispb: ********
      banco-inter:
        name: Banco Inter
        shortName: Inter
        id: d7e27a98-ef6c-4b79-b2d8-c2527eba8d84
        compe: 077
        ispb: 416968
        temporarilyUnavailable: true
        restrictedTo:
          - alpha
          - beta
      bradesco:
        name: Bradesco
        shortName: Bradesco
        id: 439a9b5c-2cfb-4e57-b60b-20eea83899ca
        compe: 237
        ispb: ********
      btg:
        name: BTG
        shortName: BTG
        id: b397dd9c-62b2-4632-bce7-85f56666c083
        compe: 208
        ispb: ********
      c6-bank:
        name: C6 Bank
        shortName: C6 Bank
        id: 6a0ec228-70b7-4292-9e64-8fa731b2a730
        compe: 336
        ispb: ********
        temporarilyUnavailable: true
        restrictedTo:
          - alpha
          - beta
      itau:
        name: Itaú
        shortName: Itaú
        id: ********-ec0d-4398-83ce-68b6b1087e49
        compe: 341
        ispb: ********
      mercado-pago:
        name: Mercado Pago
        shortName: MP
        id: 9326f9b2-ae57-42c4-a0d9-acc4ba434696
        compe: 323
        ispb: ********
      nubank:
        name: Nubank
        shortName: Nubank
        id: 06c19499-3412-4125-84b7-d0fbc98b5019
        compe: 260
        ispb: ********
      pic-pay:
        name: PicPay
        shortName: PicPay
        id: 756b9782-d9d4-4f9b-9756-997eba0e2cbd
        compe: 380
        ispb: ********
      santander:
        name: Santander
        shortName: Santander
        id: aaacb9cf-e8c3-402b-93b8-cf4d3e2ec497
        compe: 33
        ispb: ********
      sicoob:
        name: Sicoob
        shortName: Sicoob
        id: c2d48e71-07af-4442-8c7d-c82d0eb45e5f
        compe: 756
        ispb: 0489185
  chatbotai:
    host: "https://chatbot.friday.ai"
    clientid: "CHATBOT_AI_TRANSACTION_CLIENT_ID-d502c3c4-0b1b-401f-aa75-e86d9f94cd95"
    secret: "5-H1f]QvL-_:uU1:Mk)J]v,s}q4=#UT=GJnoH%p4PnJX-nvjP0HFFPCBBPxA"
    sendMessageProtocol: QUEUE

  investment-manager:
    host: "http://mock-server.com"
    clientId: "FROM_SECRETS"
    clientSecret: "FROM_SECRETS"
    create-account-path: "/account"
    index-rates-path: "/indexRates"
    simulation-optimize-contribution-path: "/monetarySimulation/optimizeMonthlyContribution"
    simulation-simulate-future-value-path: "/monetarySimulation/simulateFutureValue"
    create-fixed-income-account-path: "/account/fixedIncome"
    invest-by-index-path: "/fixedIncome/investment/byIndex"
    invest-status-path: "/fixedIncome/investment/"
    redemption-by-amount-path: "/fixedIncome/redemption/byAmount"
    redemption-status-path: "/fixedIncome/redemption/"
    quotation-path: "/fixedIncome/redemption/quotation/byAmount"
    user-position-path: "/fixedIncome/positions/"
    user-all-position-path: "/fixedIncome/positions/byAccountId"
    user-position-available-to-redeem-path: "/fixedIncome/positions/{accountId}/availableToRedeem"
    investment-wait-time: 15000 #miliseconds
    investment-pooling-interval: 15000 #miliseconds
    compare-all-rates-path: "/monetaryCorrection/compareAllRates"
  html2Image:
    lambdaName: "html-to-image"
  vehicle-debts:
    tenant: ${tenant.name}
    host: "https://vehicle-debts.friday.ai"
    username: FROM_AWS_SECRETS
    password: FROM_AWS_SECRETS
    enrollmentUserPath: "/enrollments/user"
    enrollmentVehiclePath: "/enrollments/vehicle"
    retrieveVehiclesPath: /enrollments/${tenant.name}/document/{document}
    dlqArn: arn:aws:sqs:${application.region}:${application.accountNumber}:vehicle_debts_dlq

email:
  add-bill-email-max-receive-attempts: 10
  bucket-unprocessed-emails: ses-unprocessed-emails-via1
  notification:
    email: <EMAIL>
    display-name: ${tenant.displayName}
  finance-report-recipients: <EMAIL>
  newAccountReport:
    recipients: <EMAIL>
    subject: "relatório de contas novas"
    tableStyle: border-collapse:collapse;border-color:#ccc;border-spacing:0
    thStyle: background-color:#f0f0f0;border-bottom-width:1px;border-color:inherit;border-style:solid;border-top-width:1px;border-width:0px;color:#333;font-family:Arial, sans-serif;font-size:14px;font-weight:normal;overflow:hidden;padding:10px 5px;text-align:left;vertical-align:top;word-break:normal
    tdStyle: background-color:#fff;border-color:inherit;border-style:solid;border-width:1px 0px;color:#333;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;text-align:left;vertical-align:top;word-break:normal
  newUpgradeAccount:
    recipients: <EMAIL>
    sensitiveRecipients: <EMAIL>
    subject: "[upgrade de conta está pendente de aprovação interna] - %s"
    message: |
      Um upgrade de conta está pendente de aprovação interna:
      Id: %s
      Nome: %s
      Email: %s
  newAccount:
    pendingInternalApprove:
      recipients: <EMAIL>
      sensitiveRecipients: <EMAIL>
      subject: "[conta nova pendente de aprovação interna] - %s"
      message: |
        Uma nova conta está pendente de aprovação interna:
        Id: %s
        Nome: %s
        Email: %s
    pendingInternalReview:
      recipients: <EMAIL>
      sensitiveRecipients: <EMAIL>
      subject: "[conta nova pendente de revisão interna] - %s"
      message: |
        Uma nova conta foi reprovada pelo Arbi e está pendente de revisão interna:
        Id: %s
        Nome: %s
        Email: %s
    pendingActivation:
      recipients: <EMAIL>
      sensitiveRecipients: <EMAIL>
      subject: "[conta nova pendente de ativação] - %s"
      message: |
        Uma nova conta foi aprovada pelo Arbi e está pendente de ativação:
        Id: %s
        Nome: %s
        Email: %s
        Chave Pix criada: %s
        DDA ativado: %s

urls:
  site: https://use.via1.app
  api: https://api.via1.app
  termsOfUse: "https://via1-bill-payment-public.s3.amazonaws.com/termo_de_uso/termo_de_uso_friday_btg_2024_05_10.pdf"

integration:
  billpayment:
    username: 1fab734e-305d-4308-ab3a-3f007a14ae1e
    password: $2y$17$9cYihdEzivc0RLQEfITB7eMA7vZ224CnlFO3590gJ28tHHv4KdWLS
    host: https://api.via1.app
    createbill:
      path: /v1/mailbox/message

chatbotAI-auth:
  secret: bKzAm82UAVjUmKUBUpjKu5xUrjml5upVfazJeACYz4P4T2hPNyezrs3BTRmN

celcoin-callback:
  identity: ee829e3b-0531-4b5a-aeb7-d4b4acdbc310
  secret: $2y$12$vwORiEB1bRVOEws7/uM7mO8tdrIHukh7EI/SdA3boTWlLt0tRvI6e #fixme adicionar no secrets

arbi-callback:
  identity: eb49ee38-395f-11eb-a653-db75f0f4f337
  secret: AT_AWS_SECRETS

revenue-cat-callback:
  identity: c5fcf4ee-966e-446f-b56a-4d9e26373c54
  secret: AT_AWS_SECRETS

friday-callback:
  identity: 2c771069-9b8a-4390-a894-318ca9ba742a
  secret: AT_AWS_SECRETS

modatta-b2b:
  identity: 8da2b063-bff2-43d2-8377-07b00a458c31
  secret: AT_AWS_SECRETS

modatta-corp-callback:
  identity: ac700a4b-dd2f-4c01-a142-0e165fa86d71
  secret: AT_AWS_SECRETS # FIXME: adicionar no secrets

vehicle-debts-callback:
  identity: 523a9988-0a0e-44e2-806f-dc3c937d0427
  secret: password

intercom-callback:
  identity: ad90f1c5-af3f-4614-8134-8a31033786bd
  secret: peW*!Z!Y6AWEr3T4bZqP*rsTYf!6Rqh7duD*x8B26kdwsU # FIXME: adicionar no secrets

templates-html-to-image:
  instagrammableInvestmentReceipt: "templates/instagrammable-investment-receipt.friday"
  instagrammableExtraInvestmentReceipt: "templates/instagrammable-extra-investment-receipt.friday"
  instagrammableGoalCompletedReceipt: "templates/instagrammable-goal-completed-receipt.friday"
  instagrammableGoalEndDateReachedReceipt: "templates/instagrammable-goal-end-date-reached-receipt.friday"

multi-channel-notification:
  invoice-add-no-description-success:
    whatsapp:
      template: ${communication-centre.integration.blip.templates.invoiceAddNoDescriptionSuccess}
      parameters: [BILL_PAYEE, BILL_DUE_DATE, BILL_AMOUNT, BILL_SOURCE, BILL_CREATED_BY, WALLET_NAME]
  invoice-add-with-description-success:
    whatsapp:
      template: ${communication-centre.integration.blip.templates.invoiceAddWithDescriptionSuccess}
      parameters: [BILL_PAYEE, BILL_DUE_DATE, BILL_AMOUNT, BILL_DESCRIPTION, BILL_SOURCE, BILL_CREATED_BY, WALLET_NAME]
  barcode-bill-add-waiting-approval:
    whatsapp:
      template: ${communication-centre.integration.blip.templates.barcodeBillAddWaitingApproval}
      parameters: [BILL_PAYEE, BILL_DUE_DATE, BILL_AMOUNT, BILL_SOURCE, BILL_CREATED_BY, WALLET_NAME]
  barcode-bill-add-known-author-with-description-success:
    whatsapp:
      template: ${communication-centre.integration.blip.templates.barcodeBillAddKnownAuthorWithDescriptionSuccess}
      parameters: [BILL_PAYEE, BILL_DUE_DATE, BILL_AMOUNT, BILL_DESCRIPTION, BILL_SOURCE, BILL_CREATED_BY, WALLET_NAME, BILL_HINT]
  barcode-bill-add-known-author-no-description-success:
    whatsapp:
      template: ${communication-centre.integration.blip.templates.barcodeBillAddKnownAuthorNoDescriptionSuccess}
      parameters: [BILL_PAYEE, BILL_DUE_DATE, BILL_AMOUNT, BILL_DESCRIPTION, BILL_SOURCE, BILL_CREATED_BY, WALLET_NAME, BILL_HINT]
  barcode-bill-add-known-author-no-description-success-with-hint:
    whatsapp:
      template: ${communication-centre.integration.blip.templates.barcodeBillAddKnownAuthorNoDescriptionSuccessWithHint}
      parameters: [BILL_PAYEE, BILL_DUE_DATE, BILL_AMOUNT, BILL_DESCRIPTION, BILL_SOURCE, BILL_CREATED_BY, WALLET_NAME, BILL_HINT]
  barcode-bill-add-no-author-with-description-success:
    whatsapp:
      template: ${communication-centre.integration.blip.templates.barcodeBillAddNoAuthorWithDescriptionSuccess}
      parameters: [BILL_PAYEE, BILL_DUE_DATE, BILL_AMOUNT, BILL_DESCRIPTION, BILL_SOURCE, BILL_CREATED_BY, WALLET_NAME, BILL_HINT]
  barcode-bill-add-no-author-no-description-success:
    whatsapp:
      template: ${communication-centre.integration.blip.templates.barcodeBillAddNoAuthorNoDescriptionSuccess}
      parameters: [BILL_PAYEE, BILL_DUE_DATE, BILL_AMOUNT, BILL_DESCRIPTION, BILL_SOURCE, BILL_CREATED_BY, WALLET_NAME, BILL_HINT]
  investment-value-optimized-increased:
    whatsapp:
      template: ${communication-centre.integration.blip.templates.investmentValueOptimizedIncreased}
      parameters: [GOAL_NAME, OLD_VALUE, OPTIMIZED_VALUE]
  investment-value-optimized-decreased:
    whatsapp:
      template: ${communication-centre.integration.blip.templates.investmentValueOptimizedDecreased}
      parameters: [ GOAL_NAME, OLD_VALUE, OPTIMIZED_VALUE ]
  wallet-cash-in-no-payments-pending:
    whatsapp:
      template: ${communication-centre.integration.blip.templates.walletCashInNoPaymentsPending}
      parameters: [ WALLET_NAME, RECEIVED_AMOUNT, WALLET_NAME, SENDER_INFO ]
  wallet-cash-in-sufficient-balance:
    whatsapp:
      template: ${communication-centre.integration.blip.templates.walletCashInSufficientBalance}
      parameters: [ WALLET_NAME, RECEIVED_AMOUNT, WALLET_NAME, SENDER_INFO ]
  wallet-payment-overdue-yesterday:
    whatsapp:
      template: ${communication-centre.integration.blip.templates.walletPaymentOverdueYesterday}
      parameters: [ WALLET_NAME ]
  wallet-payment-overdue-yesterday-with-hint:
    whatsapp:
      template: ${communication-centre.integration.blip.templates.walletPaymentOverdueYesterdayWithHint}
      parameters: [ WALLET_NAME, HINT ]


communication-centre:
  email:
    display-name: Friday
    return-path: <EMAIL>
    bucket-unprocessed-emails: ses-unprocessed-emails-via1
    configuration-set-name: failure_rendering_notification_configuration_set
    receipt:
      email: <EMAIL>
      display-name: Friday
    notification:
      email: <EMAIL>
      display-name: Friday
    max-attachments: 60
    max-pages-per-attachment: 15
    virus:
      bucket: quarantine-emails
    templates:
      local:
        emailVerificationTokenPath: "templates/email-verification-token.friday"
        kycDossierFormPath: "templates/kyc/kyc-dossier.friday"
        declarationOfResidencyFormPath: "templates/register/declaration-of-residency.friday"
        accountStatementReportPath: "templates/statement-report.friday"
        walletSummaryReportPath: "templates/wallet-summary-report.friday"
        emailPasswordRecoveryTokenPath: "templates/password-recovery-token.friday"
        statementPdf: "templates/statement-pdf"
        pixReceipt: "templates/bill-receipt-attachment.friday"
        investmentReceipt: "templates/investment-receipt.friday"
        invoiceReceipt: "templates/bill-receipt-attachment.friday"
        barcodeBillReceipt: "templates/bill-receipt-attachment.friday"
        mailReceipt: "templates/mail-receipt.friday"
        inform: "templates/inform.friday"
        investmentRedemptionFinished: "templates/mail-receipt-investment-redemption-finished"
      ses:
        walletInviteAssistantWithoutAccount: wallet_invite_assistant_without_account__4_13_0
        walletInviteCollaboratorWithoutAccount: wallet_invite_collaborator_without_account__4_13_0
        walletInviteReminderAssistantWithoutAccount: wallet_invite_reminder_assistant_without_account__4_13_0
        walletInviteReminderCollaboratorWithoutAccount: wallet_invite_reminder_collaborator_without_account__4_13_0

  forward:
    configuration-set: failure_rendering_notification_configuration_set
    sender: <EMAIL>
    htmlTemplatePath: "templates/forwarded-email-html.hbs"
    textTemplatePath: "templates/forwarded-email-plain-text.hbs"
    organizationName: Friday
  integration:
    blip:
      auth: xxx
      host: https://via1-pagamentos-digitais.http.msging.net
      namespace: 1d3afeae_c48c_4c2a_8d65_02b4bbf01f83
      command:
        path: /commands
      message:
        path: /messages
      templates:
        barcodeBillAddKnownAuthorWithDescriptionSuccess: barcode_bill_add_known_author_with_description_success__4_11_0
        barcodeBillAddKnownAuthorNoDescriptionSuccess: barcode_bill_add_known_author_no_description_success__4_11_0
        barcodeBillAddKnownAuthorNoDescriptionSuccessWithHint: barcode_bill_add_known_author_no_description_success_with_hint__4_11_1
        barcodeBillAddNoAuthorWithDescriptionSuccess: barcode_bill_add_no_author_with_description_success__4_12_0
        barcodeBillAddNoAuthorNoDescriptionSuccess: barcode_bill_add_no_author_no_description_success__4_11_0
        barcodeBillAddWaitingApproval: barcode_bill_add_waiting_approval__1_0_0
        invoiceAddNoDescriptionSuccess: invoice_add_no_description_success__4_11_1
        invoiceAddWithDescriptionSuccess: invoice_add_with_description_success__4_11_1

        barcodeBillCloseOverdueKnownAuthorWithDescription: barcode_bill_close_overdue_known_author_with_description__4_11_0
        barcodeBillCloseOverdueKnownAuthorNoDescription: barcode_bill_close_overdue_known_author_no_description__4_11_0
        barcodeBillCloseOverdueKnownAuthorNoDescriptionWithHint: barcode_bill_close_overdue_known_author_no_description_with_hint__4_11_1
        barcodeBillCloseOverdueNoAuthorWithDescription: barcode_bill_close_overdue_no_author_with_description__4_17_0
        barcodeBillCloseOverdueNoAuthorNoDescription: barcode_bill_close_overdue_no_author_no_description__4_17_0
        invoiceCloseOverdueWithDescription: invoice_close_overdue_with_description__4_11_0
        invoiceCloseOverdueNoDescription: invoice_close_overdue_no_description__4_17_0
        billComingDueSecondaryWallet: bill_coming_due_secondary_wallet_{{i}}__1_0_5
        walletOnePixPay: wallet_one_pix_pay__4_17_0
        walletOnePixPaySingular: wallet_one_pix_pay_singular__4_17_0
        walletCashInWithCreditCardFailed: wallet_cash_in_with_credit_card_failed__4_17_0
        walletPostalBoxAddDuplicate: wallet_postal_box_add_duplicate__4_17_0
        walletPostalBoxAddNotAuthorized: wallet_postal_box_add_not_authorized__4_12_0
        walletPostalBoxAddNonPayableNoData: wallet_postal_box_add_non_payable_no_data__4_11_1
        walletPostalBoxAddNonPayable: wallet_postal_box_add_non_payable__4_11_0
        walletPostalBoxAddPaidExternally: wallet_postal_box_add_paid_externally__4_11_1
        walletPostalBoxAddPaidExternallyWithoutData: wallet_postal_box_add_paid_externally_without_data__4_12_0
        walletPostalBoxAddValidationFailure: wallet_postal_box_add_validation_failure__4_11_0
        walletInvoicePaymentFailure: wallet_invoice_payment_failure__4_17_0
        walletLastAlertPaymentOverdueToday: wallet_last_alert_payment_overdue_today_{{i}}__4_19_0
        walletLastAlertPaymentOverdueTodayWithHint: wallet_last_alert_payment_overdue_today_with_hint_{{i}}__4_12_0
        walletPaymentOverdueYesterday: wallet_payment_overdue_yesterday__4_20_1
        walletPaymentOverdueYesterdayWithHint: wallet_payment_overdue_yesterday_with_hint__4_12_1
        welcomeAccountCreatedWithoutChatbot: welcome_account_created_without_chatbot__1_0_0
        upgradeCompleted: upgrade_completed__1_0_2
        registerDenied: register_denied__4_26_0
        registerUpgraded: register_upgraded__4_26_0
        walletMemberJoined: wallet_member_joined__4_17_0
        userAuthenticationRequired: user_authentication_required__4_12_0
        registerUpdated: register__updated__4_12_1
        walletInvoicePaymentReturned: wallet_invoice_payment_returned__4_17_0
        walletBarcodeBillReceipt: wallet_barcode_bill_receipt__4_17_0
        walletInvoiceBillReceipt: wallet_invoice_bill_receipt__4_11_1
        walletPixBillReceipt: wallet_pix_bill_receipt__4_11_1
        walletInvestmentBillReceiptWithBadge: wallet_investment_bill_receipt_with_badge__1_0_0
        walletBillReceiptImage: wallet_bill_receipt_image__3_1_0
        walletCashInInsufficientBalance: wallet_cash_in_insufficient_balance__4_17_0
        walletBarcodeBillApprovedPaymentNonPayable: wallet_barcode_bill_approved_payment_non_payable__4_17_0
        walletInsufficientBalanceAfterHours: wallet_insufficient_balance_after_hours__4_12_4
        walletInsufficientBalance: wallet_insufficient_balance__4_12_3
        insufficientBalanceFirstCashIn: insufficient_balance_first_cash_in__4_12_0
        walletInsufficientBalanceSecondaryWallet: wallet_insufficient_balance_secondary_wallet__1_0_2
        walletBillSchedulePostponedDueLimitReached: wallet_bill_schedule_postponed_due_limit_reached__4_11_2
        walletBillScheduleCanceledDueAmountHigherThanDailyLimit: wallet_bill_schedule_canceled_due_amount_higher_than_daily_limit__4_11_4
        fredOnboardingWelcome: fred_onboarding_welcome_1_0_9
        triPixReminderNextDay: tri_pix_reminder_next_day__1_0_0
        triPixReminderLastDay: tri_pix_reminder_last_day__1_0_0
        triPixExpired: tri_pix_expired_1_0_3
        walletBillScheduleCanceledDueCreditCardDenied: wallet_bill_schedule_canceled_due_credit_card_denied__4_24_1
        postalBoxAddNotVisibleBillAlreadyExists: postal_box_add_not_visible_bill_already_exists__4_12_2
        walletInviteAssistantWithAccount: wallet_invite_assistant_with_account__4_13_3
        walletInviteReminderAssistantWithAccount: wallet_invite_reminder_assistant_with_account__4_13_0
        firstBillScheduled: first_bill_scheduled__4_12_0
        registerToken: register_token__4_14_0
        itpTransactionFailed: itp_transaction_failed__4_17_1
        signUpBasicUpdateDataNeeded: sign_up_basic_update_data_needed__4_18_0
        pixNotReceivedFailure: pix_not_received_failure__4_21_0
        subscriptionInsufficientBalance: subscription_insufficient_balance__4_17_0
        inAppSubscriptionOverdue: in_app_subscription_overdue__1_0_0
        subscriptionOverdue: subscription_overdue__4_18_2
        pixSubscriptionOverdueNotificationChannelDowngradeWarning: subscription_overdue_whatsapp_blocked_pix_friday_2_0_0
        inAppSubscriptionOverdueNotificationChannelDowngradeWarning: subscription_overdue_whatsapp_blocked_friday_3_0_0
        subscriptionOverdueCloseAccount: subscription_overdue_close_account__1_0_0
        subscriptionOverdueWarningAccountClosure: subscription_overdue_warning_account_closure__1_0_0
        subscriptionOverdueDay32: subscription_overdue_day32__1_00_3
        subscriptionOverdueDay04: subscription_overdue_day04__1_00_0
        subscriptionOverdueDay02: subscription_overdue_day02__1_00_2
        subscriptionOverdueDay01: subscription_overdue_day01__1_00_3
        subscriptionCreated: subscription_created__4_17_1
        subscriptionGrantedByInvestment: TBD
        walletOnePixPayFailure: wallet_one_pix_pay_failure__4_17_0
        creditCardEnabled: credit_card_enabled__4_20_0
        utilityAccountInvoiceScanError: utility_account_invoice_scan_error__1_0_0
        utilityAccountInvoiceNotFound: utility_account_invoice_not_found__1_0_0
        utilityAccountDisconnectLegacyAccount: utility_account_disconnect_legacy_account__1_0_2
        utilityAccountRequestReconnection: utility_account_request_reconnection__1_0_1
        connectedFlowUtilityAccount: connected_flow_utility_account__2_3_0
        connectedFlowUtilityAccountWithBills: connected_flow_utility_account_with_bills__2_3_0
        connectedUtilityAccount: connected_utility_account__4_26_0
        utilityAccountUpdateStatusWithDetails: utility_account_update_status_with_details__1_0_0
        disconnectedScrapingUtilityAccount: disconnected_scraping_utility_account__1_0_0
        utilityAccountUpdateStatus: utility_account_update_status__4_22_0
        walletCashInNoPaymentsPending: wallet_cash_in_no_payments_pending__4_17_1
        walletCashInSufficientBalance: wallet_cash_in_sufficient_balance__4_17_2
        walletFounderSelfCashInSufficientBalance: wallet_cash_in_sufficient_balance__short_0
        reminderExpiredNotification: reminder_expired_notification__1_0_2
        reminderExpiredNotificationSingular: reminder_expired_notification_singular__1_0_0
        reminderNotification: reminder_notification__1_0_9
        postalBoxAddManualReview: postal_box_add_manual_review__4_10_0
        mailboxBillInsecure: mailbox_bill_insecure__1_0_0
        walletApprovedPaymentCancelled: wallet_approved_payment_cancelled__4_17_0
        walletBillScheduleCanceledDueAmountChanged: wallet_bill_schedule_canceled_due_amount_changed__4_23_1
        sweepingCashInError: sweeping_transfer_error__1_0_0
        sweepingCashInInsufficientBalanceError: sweeping_transfer_balance_error__1_1_0
        investmentRedemptionCreated: investment_redemption_created__1_0_0
        investmentRedemptionCompleted: investment_redemption_completed__1_0_0
        investmentRedemptionFailed: investment_redemption_failed__1_0_0
        investmentRedemptionPartialFailed: investment_redemption_partial_failed__1_0_0
        investmentValueOptimizedIncreased: investment_value_optimized_increased__1_0_0
        investmentValueOptimizedDecreased: investment_value_optimized_decreased__1_0_0
        sweepingAccountConnected: open_finance_connection_success__1_0_1
        sweepingAccountConnectedWithDataIncentive: open_finance_connection_success_with_data_incentive__1_0_1
        sweepingAccountConnectionFailed: open_finance_connection_failed_generic__1_0_2
        sweepingAccountEdit: open_finance_connection_edit_success__1_0_0
        sweepingAccountEditFailed: open_finance_connection_edit_failed__1_0_4
        dataConsentConnected: open_finance_data_consent_success__1_0_0
        dataConsentConnectionFailed: open_finance_data_consent_failed_generic__1_0_0
        goalWithDailyLiquidityCompletedByDate: goal_with_daily_liquidity_completed_by_date__1_0_0
        goalWithDailyLiquidityCompletedByAmount: goal_with_daily_liquidity_completed_by_amount__1_0_0
        goalWithMaturityLiquidityCompletedByDate: goal_with_maturity_liquidity_completed_by_date__1_0_0
        goalWithMaturityLiquidityCompletedByAmount: goal_with_maturity_liquidity_completed_by_amount__1_0_0

        goalWithDailyLiquidityCompletedByDateWithBadge: goal_with_daily_liquidity_completed_by_date_with_badge__1_0_0
        goalWithDailyLiquidityCompletedByAmountWithBadge: goal_with_daily_liquidity_completed_by_amount_with_badge__1_0_0
        goalWithMaturityLiquidityCompletedByDateWithBadge: goal_with_maturity_liquidity_completed_by_date_with_badge__1_0_0
        goalWithMaturityLiquidityCompletedByAmountWithBadge: goal_with_maturity_liquidity_completed_by_amount_with_badge__1_0_0

        goalWithDailyLiquidityPausedByDate: goal_with_daily_liquidity_paused_by_date__1_0_0
        goalWithDailyLiquidityPausedByAmount: goal_with_daily_liquidity_paused_by_amount__1_0_0

        sweepingTransferChatbotParticipantBalanceError: sweeping_transfer_chatbot_participant_balance_error__1_1_0
        sweepingTransferChatbotParticipantLimitError: sweeping_transfer_chatbot_participant_limit_error__1_1_0
        sweepingTransferChatbotConsentLimitError: sweeping_transfer_chatbot_consent_limit_error__1_1_0
        sweepingTransferChatbotInvalidConsentError: sweeping_transfer_chatbot_invalid_consent_error__1_0_0
        sweepingTransferChatbotRetryableError: sweeping_transfer_chatbot_retryable_error__1_1_1
        sweepingTransferWebappParticipantBalanceError: sweeping_transfer_webapp_participant_balance_error__1_2_1
        sweepingTransferWebappParticipantLimitError: sweeping_transfer_webapp_participant_limit_error__1_1_0
        sweepingTransferWebappConsentLimitError: sweeping_transfer_webapp_consent_limit_error__1_1_0
        sweepingTransferWebappInvalidConsentError: sweeping_transfer_webapp_invalid_consent_error__1_0_2
        sweepingTransferWebappRetryableError: sweeping_transfer_webapp_retryable_error__1_2_2

    billpayment:
      username: 1fab734e-305d-4308-ab3a-3f007a14ae1e
      password: $2y$17$9cYihdEzivc0RLQEfITB7eMA7vZ224CnlFO3590gJ28tHHv4KdWLS
      host: https://api.via1.app
      createbill:
        path: /v1/mailbox/message
  landingPageUrl: https://friday.ai

friday.morning:
  messaging:
    defaults.consumer:
      corePoolSize: 1
      maximumPoolSize: 4
      keepAliveTime: 60s
      blockingDequeCapacity: 30
      awaitTerminationTime: 110s
      waitTime: 0s
      coolDownTime: 20s
      maxNumberOfMessages: 10
      visibilityTimeout: 300s
      dlqArn: arn:aws:sqs:${application.region}:${application.accountNumber}:bill_events_dlq
      maxReceiveCount: 3
    consumer:
      bank-account-deposit:
        queueName: bank_account_deposit
        waitTime: 20s
        coolDownTime: 0s
      bill-notification:
        queueName: bill_notification
        waitTime: 20s
        coolDownTime: 0s
        corePoolSize: 3
        maximumPoolSize: 6
      bill-payment-started:
        queueName: bill_payment_started
        waitTime: 20s
        coolDownTime: 0s
        topic:
          arn: ${sns.billEventTopicArn}
          filter: '{"eventType": ["PAYMENT_START"]}'
        corePoolSize: 5
        maximumPoolSize: 20
      check-sweeping-cash-in-status:
        queueName: check_sweeping_cash_in_status
      one-pix-pay-requested:
        queueName: one_pix_pay_requested
        visibilityTimeout: 10m
      account-reconciliation-report:
        queueName: bill_payment_account_reconciliation_report
        waitTime: 20s
        coolDownTime: 5s
        corePoolSize: 3
        maximumPoolSize: 6
        dlqArn: arn:aws:sqs:${application.region}:${application.accountNumber}:${friday.morning.messaging.consumer.account-reconciliation-report.queueName}_dlq
      automatic-pix-refresh:
        queueName: bill_payment_automatic_pix_refresh
      automatic-pix-callback:
        queueName: bill_payment_automatic_pix_callback
      automatic-pix-approve-recurrence:
        queueName: bill_payment_automatic_pix_approve_recurrence
        topic:
          arn: ${sns.billEventTopicArn}
          filter: '{"eventType": ["PAID"]}'
      pix-deposit-notification:
        queueName: pix_deposit_notification
        visibilityTimeout: 300s
        maxReceiveCount: 30
      push-notification:
        queueName: bill_payment_push_notification
        visibilityTimeout: 30s
        maxReceiveCount: 3

sqs:
  arnPrefix: "arn:aws:sqs:${application.region}:${application.accountNumber}:"
  region: ${application.region}
  maxReceiveCount: 60
  sqsWaitTime: 0
  sqsCoolDownTime: 20
  maxNumberOfMessages: 10
  visibilityTimeout: 300
  dlqArn: arn:aws:sqs:${application.region}:${application.accountNumber}:bill_events_dlq
  autoScaleWorkersInParallel: true
  startCashIn: cash-in-start
  createGoalInvestment: goal-create-investment
  incomeReport: bill_payment_income_report
  optimizeGoalInstallmentAmount: bill_payment_goal_optimize_installment_amount
  checkGoalCompletion: bill_payment_goal_check_completion
  goalInvestmentSynchronize: bill_payment_goal_investment_synchronize
  eventBusQueueName: event-bus
  addBillFromEmailQueueName: add-bill-from-email
  sendToManualFlowEmailQueueName: send-to-manual-flow-email-queue-name
  emailQueueName: incoming-emails
  chatbotAiMessageQueueName: chatbot_ai_message
  utilityRequestFlowQueueName: utility-api-flow-request
  utilityRequestFlowConnectQueueName: utility-api-flow-request-connect
  utilityFlowResponseQueueName: utility-api-flow-response
  rollbackTransactionQueueName: bill_payment_rollback_transaction
  invalidateBankAccountQueueName: bank-account-invalidation
  validateBoletoJaBaixadoQueueName: validate_boleto_ja_baixado
  registerPixKeyQueueName: register_pix_key_queue_name
  billPaymentSchedulingQueueName: bill_payment_scheduling
  bankAccountDepositQueueName: bank_account_deposit
  settlementRequestQueueName: settlement-friday-request
  settlementResponseQueueName: settlement-friday-response
  statementQueueName: statement-request
  billEventNotificationQueueName: bill_event_notification
  billReceiptGeneratorQueueName: bill_receipt_generator
  settlementFundsTransferQueueName: settlement_funds_transfer
  updateScheduledBillQueueName: update_scheduled_bill_queue_name
  walletEventsQueueName: wallet-events-queue
  accountEventsQueueName: account-events-queue
  trackableBillQueueName: trackable-bill-queue
  bill-tracking.calculate: bill-tracking-calculate
  bill-tracking.query: bill-tracking-query
  ddaQueueName: dda-processing
  billPaymentScheduledQueueName: bill_payment_scheduled
  userJourneyQueueName: user_journey
  ddaBatchAddOrdersQueueName: dda_batch_add_orders
  ddaBillsInternalQueueName: dda-bills-internal
  ddaFullImportQueueName: dda_full_import
  revenueCatQueueName: revenue_cat_event
  ddaBatchMigrationOrdersQueueName: dda_batch_migration_orders
  billComingDueQueueName: bill_coming_due
  billComingDueNotificationQueueName: bill_coming_due_notification
  billComingDueLastWarnNotificationQueueName: bill_coming_due_last_warn_notification
  billCategorySuggestionQueueName: bill_category_suggestion
  setBillCategoryQueueName: set_bill_category
  vehicleDebtsQueueName: vehicle_debts
  vehicleDebtsEnrichmentQueueName: vehicle_debts_enrichment
  createDefaultCategories: create_default_categories
  simpleSignUpQueueName: simple_sign_up_pending_data
  ddaBills: dda-bills
  concessionariaDiretoActiveBills: concessionaria-bills
  concessionariaDiretoInactiveBills: concessionaria-inactive-bills
  billPaidActivityQueueName: bill_paid_activity
  subscriptionBillPaidQueueName: subscription_bill_paid
  passwordCreatedQueueName: password_created
  closeAccountQueueName: close_account
  closeExternalAccountQueueName: close_external_account
  notifyReminderQueueName: notify_reminder
  notifyExpiredReminderQueueName: notify_expired_reminder
  summaryQueueName: summary_request
  updateConcessionariaStatusQueueName: update_concessionaria_status
  anonymousIdInAppSubscriptionQueueName: anonymous_subscription
  openFinanceQueueName: open-finance-queue
  createBillComingDueByIdQueueName: create_bill_coming_due_by_id
  whatsappFlowRegisterLicensePlate: whatsapp-flow-register_license_plate
  payment:
    process-scheduled-bills: process_scheduled_bills
  queues:
    synchronizeBankAccount: synchronize_bank_account
    whatsappNotificationVerifier: whatsapp_notification_verifier
    systemActivityBillPaidHandler: system-activity-bill-paid
    notifyBillComingDue: notify_bill_coming_due
    chatBotNotificationGateway: chatbot_notification_gateway
    chatBotWebhooks: chatbot_webhooks
    chatBotStateUpdate: chatbot_state_update
    pixDepositNotification: ${friday.morning.messaging.consumer.pix-deposit-notification.queueName}
    pushNotification: ${friday.morning.messaging.consumer.push-notification.queueName}
    cashInReceived: cash_in_received
    userEvent: user_events
    automaticPixRefresh: ${friday.morning.messaging.consumer.automatic-pix-refresh.queueName}
    automaticPixCallback: ${friday.morning.messaging.consumer.automatic-pix-callback.queueName}

sns:
  arnPrefix: "arn:aws:sns:${application.region}:${application.accountNumber}:"
  topics:
    whatsapp-flow:
      name: ${tenant.name}-whatsapp-flow
    wallet-events:
      name: wallet-events
    account-events:
      name: account-events
  region: ${application.region}
  billEventTopicArn: ${sns.arnPrefix}bill-events
  incomingEmailsTopicArn: ${sns.arnPrefix}incoming-emails
  walletEventTopicArn: ${sns.arnPrefix}${sns.topics.wallet-events.name}
  accountEventTopicArn: ${sns.arnPrefix}${sns.topics.account-events.name}
  eventBusTopicArn: ${sns.arnPrefix}event-bus
  pushNotificationArn: ${sns.arnPrefix}app/GCM/firebase_sns_platform_application
  sms:
    maxPrice: 50.00

features:
  inAppSubscription: 1
  forceTokenViaWhatsapp: false
  fallbackCheckTransfer: true
  asyncSettlement: true
  forwardEmailToManualWorkflow: true
  automaticUserRegister: true
  zeroAuthEnabled: true
  blockDuplicateByIdNumber: true
  userPilotEnabled: false
  dda-batch-add-async: false
  eventBus.enabled: true
  silenceNotifications: false
  skipFullImportWhenAccountHasBills: false
  optOutNotification: false
  filterAccountsThatReceiveNotification: false
  billComingDueHandler.enabled: false
  billComingDueLastWarnNotificationHandler.enabled: false
  creditCardChallenge: true
  useInternalCreditCardCalculator: true
  updateAmountAfterPaymentWindow: false
  batchScheduleNotification.enabled: false
  disableOverdueAccountDDA: false
  closeOverdueAccount: false
  pinCode:
    enabled: false
  credit-card-risk-analisys:
    provider: "clearsale"
  credit-card:
    provider: "software-express"
  occurrences:
    pastRangeInDays: 6
    automation:
      enabled: false
      queue: "boleto-occurrences"
  userGroupsFilter:
    enabled: false
    versionRestrictions: [ ]
  userRestrictions.enabled: false
  schedule:
    fingerprint.enabled: false
  creditCardQuota: true
  updateScheduleOnAmountLowered: false
  useQuodCCScore: true
  asyncWhatsappImage: false
  imageReceipt: false
  secureMailbox: true

token:
  onboarding:
    tokenSize: 6
    maxCooldownDuration: 20
    phoneVerificationDuration: 55
    emailVerificationDuration: 120
    passwordRecoveryDuration: 275
    whatsappVerificationDuration: 50
    livenessVerificationDuration: 550
    message: "Seu codigo Friday: %1$s. Nao compartilhe com ninguem.

              @friday.ai %1$s"
    maxErrorCount: 3
    testMobilePhones:
      - "+*************"
      - "+*************"
      - "+*************"
      - "+*************"
      - "+*************"
      - "+*************"
      - "+*************"
    appleMobilePhones:
      - "+*************"
    appleTokenVerify:
      - "261213"

emailDomain: "friday.ai"

accountRegister:
  maxDocumentSize: ******** #1024L * 1024 * 10
  user_files:
    bucket: via1-user-documents
    modattaUserDocumentsBucket: FIXME-modatta-user-documents # FIXME trocar pelo numero da conta de prd da modatta
    path: user_documents
    document_prefix: DOCUMENT_
    selfie_prefix: SELFIE_
    region: ${application.region}
    contractPrefix: CONTRACT_
    declarationOfResidencyPrefix: RESIDENCY_
    kycPrefix: KYC_
    contractLinkDuration: 10m
  pixKey:
    emailDomain: ${emailDomain}
  address:
    streetType: AVENIDA
    streetName: "OSCAR NIEMEYER"
    number: 2000
    complement: "BLC 1 SAL 401"
    neighborhood: "SANTO CRISTO"
    city: "RIO DE JANEIRO"
    state: RJ
    zipCode: "********"

recurrence:
  lastLimitDate: "2025-12-31"
  limitDate: "2026-12-31"

creditCard:
  periodLimit: 30d
  maxPeriodLimit: 3
  maxLimit: 3
  challenge:
    softDescriptor: "Verificacao"
    expiration: 3d
    maxAttempts: 3
    minAmount: 50
    maxAmount: 1000
  installments:
    fees:
      1: 4.00
      2: 4.99
      3: 4.99
      4: 4.99
      5: 4.99
      6: 4.99
      7: 4.99
      8: 4.99
      9: 4.99
      10: 4.99
      11: 4.99
      12: 4.99

cashIn:
  softDescriptor: "Adicao Saldo"

lock:
  tableName: "Shedlock"
  configuration:
    bill:
      maxDuration: 1m
      minDuration: 0s
      prefix: "BILL#"
    cashIn:
      maxDuration: 1m
      minDuration: 0s
      prefix: "CASH-IN#"
    internalBankService:
      maxDuration: 1m
      minDuration: 0s
      prefix: "INTERNAL-BANK-SERVICE#"
    updateAccountStatus:
      maxDuration: 1m
      minDuration: 0s
      prefix: "UPDATE-ACCOUNT-STATUS#"
    wallet:
      maxDuration: 11m
      minDuration: 0s
      prefix: "WALLET#"
    walletDailyPaymentLimit:
      maxDuration: 1m
      minDuration: 0s
      prefix: "LIMIT#"
    undoInvoice:
      maxDuration: 1m
      minDuration: 0s
      prefix: "UNDO-INVOICE#"
    onePixPayNotification:
      maxDuration: 12h
      minDuration: 12h
      prefix: "ONE-PIX-PAY-NOTIFICATION#"
    billComingDueLastWarnNotification:
      maxDuration: 20h
      minDuration: 20h
      prefix: "BILL-COMING-DUE-LAST-WARN-NOTIFICATION#"
    billTrackingCalculate:
      maxDuration: 24h
      minDuration: 0s
      prefix: "BILL_TRACKING_CALCULATE#"
    billTrackingQuery:
      maxDuration: 24h
      minDuration: 0s
      prefix: "BILL_TRACKING_QUERY#"
    limitLockProvider:
      maxDuration: 30s
      minDuration: 0s
      prefix: "LIMIT#"
    issuedTokenLockProvider:
      maxDuration: 1h
      minDuration: 1h
      prefix: "ISSUED_TOKEN#"
    transactionLockProvider:
      maxDuration: 5m
      minDuration: 0s
      prefix: "TRANSACTION#"
    onePixPayLockProvider:
      maxDuration: 1m
      minDuration: 0s
      prefix: "ONE_PIX_PAY#"
    hmac:
      maxDuration: 5m
      minDuration: 0s
      prefix: "HMAC#"
    categories:
      maxDuration: 1m
      minDuration: 0s
      prefix: "CATEGORIES#"
    inAppSubscriptionCouponRedeem:
      maxDuration: 10s
      minDuration: 0s
      prefix: "IN_APP_SUBSCRIPTION_COUPON_REDEEM#"
    deviceFingerprintLockProvider:
      minDuration: 0s
      maxDuration: 1m
      prefix: "DEVICE_FINGERPRINT_LOCK_PROVIDER#"
    bankAccountNumber:
      minDuration: 0s
      maxDuration: 1m
      prefix: "BANK_ACCOUNT_NUMBER#"


shedlock:
  defaults:
    lock-at-most-for: 30m

internalBankService:
  checkStatus:
    startDateMinusDays: 1
    endDatePlusDays: 7
  omnibusBankAccount:
    accountPaymentMethodId: BANK-ACCOUNT-OMNIBUS
    document: **************
    name: Friday Pagamentos Digitais LTDA
    bankNo: 213
    routingNo: 1
    accountNo: 0

log:
  reject-paths:
    - "/health"
    - "/health/"
    - "/metrics/"
    - "/management/health/"
    - "/management/metrics/"

bill:
  restrictPaymentMethods:
    enabled: true
  ted:
    limitTime: "16:45"
  fichaCompensacao:
    untrustedCnpjs:
      - "**************" # PAGSEGURO INTERNET S.A.
      - "**************" # Claro Pay
      - "**************" # CDT SOLUCOES EM MEIOS DE PAGAMENTO LTDA
      - "**************" # RECARGAPAY DO BRASIL SERVICOS DE INFORMATICA
      - "**************" # -
      - "**************" # WIDE PAY FACILITADOR DE PAGAMENTOS LTDA
      - "**************" # BANCO DIGIO S.A.
      - "**************" # PICPAY
      - "**************" # YAPAY
      - "**************" # RAPPIPAY
      - "**************" # PAYBACKBR
      - "**************" # OFFERPAY
      - "**************" # NM PAYMENTS
      - "**************" # NATURAPAY
      - "**************" # LOCALPAY
      - "**************" # GIDEAO PAY
      - "**************" # CARDPAY
      - "24313102000125" # BEPAY
      - "03816413000137" # PAGVELOZ
      - "34006497000177" # ZENEX PAGAMENTOS LTDA
      - "32024691000150" # COMPANHIA GLOBAL DE SOLUCOES E SERVICOS
      - "13794399000171" # IDEA MAKER MEIOS DE PAGAMENTO E CONSULTORIA LTDA
      - "34697707000110" # GRINGO AGENCIA DE SERVICOS RE
      - "27351731000138" # REALIZE CREDITO FINANCIAMENTO E INVESTIM
      - "********000158" # NU PAGAMENTOS SA
      - "********000172" # BANCO C6 S.A.
      - "17991841000100" # KOIN ADM CARTOES M DE PAGTO SA
      - "20855875000182" # NEON PAGAMENTOS SA INSTITUICAO DE P     AGAMENTO

receipt:
  bucketRegion: ${application.region}
  bucketName: bill-receipts
  imageResolution: 400
  imageFormat: png
  shouldSaveReceiptFilesOnDisk: false
  linkDuration: 30m

pdfConverter:
  dotsPerPoint: 35f
  dotsPerPixel: 20
  folderName: target
  fontFiles:
    - templates/fonts/Manrope-Bold.ttf
    - templates/fonts/Manrope-ExtraBold.ttf
    - templates/fonts/Manrope-ExtraLight.ttf
    - templates/fonts/Manrope-Light.ttf
    - templates/fonts/Manrope-Medium.ttf
    - templates/fonts/Manrope-Regular.ttf
    - templates/fonts/Manrope-SemiBold.ttf
    - templates/fonts/Manrope.ttf
    - templates/fonts/Montserrat-Black.ttf
    - templates/fonts/Montserrat-BlackItalic.ttf
    - templates/fonts/Montserrat-Bold.ttf
    - templates/fonts/Montserrat-BoldItalic.ttf
    - templates/fonts/Montserrat-ExtraBold.ttf
    - templates/fonts/Montserrat-ExtraBoldItalic.ttf
    - templates/fonts/Montserrat-ExtraLight.ttf
    - templates/fonts/Montserrat-ExtraLightItalic.ttf
    - templates/fonts/Montserrat-Italic.ttf
    - templates/fonts/Montserrat-Light.ttf
    - templates/fonts/Montserrat-LightItalic.ttf
    - templates/fonts/Montserrat-Medium.ttf
    - templates/fonts/Montserrat-MediumItalic.ttf
    - templates/fonts/Montserrat-Regular.ttf
    - templates/fonts/Montserrat-SemiBold.ttf
    - templates/fonts/Montserrat-SemiBoldItalic.ttf
    - templates/fonts/Montserrat-Thin.ttf
    - templates/fonts/Montserrat-ThinItalic.ttf
    - templates/fonts/Montserrat.ttf
    - templates/fonts/Mulish-Black.ttf
    - templates/fonts/Mulish-BlackItalic.ttf
    - templates/fonts/Mulish-Bold.ttf
    - templates/fonts/Mulish-BoldItalic.ttf
    - templates/fonts/Mulish-ExtraBold.ttf
    - templates/fonts/Mulish-ExtraBoldItalic.ttf
    - templates/fonts/Mulish-ExtraLight.ttf
    - templates/fonts/Mulish-ExtraLightItalic.ttf
    - templates/fonts/Mulish-Italic.ttf
    - templates/fonts/Mulish-Light.ttf
    - templates/fonts/Mulish-LightItalic.ttf
    - templates/fonts/Mulish-Medium.ttf
    - templates/fonts/Mulish-MediumItalic.ttf
    - templates/fonts/Mulish-Regular.ttf
    - templates/fonts/Mulish-SemiBold.ttf
    - templates/fonts/Mulish-SemiBoldItalic.ttf
    - templates/fonts/Mulish.ttf
    - templates/fonts/Quicksand-Bold.ttf
    - templates/fonts/Quicksand-Light.ttf
    - templates/fonts/Quicksand-Medium.ttf
    - templates/fonts/Quicksand-Regular.ttf
    - templates/fonts/Quicksand-SemiBold.ttf
    - templates/fonts/Quicksand.ttf
    - templates/fonts/Roboto-Black.ttf
    - templates/fonts/Roboto-BlackItalic.ttf
    - templates/fonts/Roboto-Bold.ttf
    - templates/fonts/Roboto-BoldItalic.ttf
    - templates/fonts/Roboto-ExtraBold.ttf
    - templates/fonts/Roboto-ExtraBoldItalic.ttf
    - templates/fonts/Roboto-ExtraLight.ttf
    - templates/fonts/Roboto-ExtraLightItalic.ttf
    - templates/fonts/Roboto-Italic.ttf
    - templates/fonts/Roboto-Light.ttf
    - templates/fonts/Roboto-LightItalic.ttf
    - templates/fonts/Roboto-Medium.ttf
    - templates/fonts/Roboto-MediumItalic.ttf
    - templates/fonts/Roboto-Regular.ttf
    - templates/fonts/Roboto-SemiBold.ttf
    - templates/fonts/Roboto-SemiBoldItalic.ttf
    - templates/fonts/Roboto-Thin.ttf
    - templates/fonts/Roboto-ThinItalic.ttf
    - templates/fonts/Roboto.ttf

wallet:
  maxOpenInvites: 10
  inviteExpiration: 15d

settlementFundsTransfer:
  payerName: Friday Pagamentos Digitais LTDA
  payerDocument: **************
  recipientName: Friday Pagamentos Digitais LTDA
  recipientDocument: **************
  description: pagamento de conta
  fraudFundsAccount: 3201012
  originSettlementBankAccount:
    accountType: CHECKING
    bankNo: 213
    routingNo: 1
    accountNo: 32588
    accountDv: 2
  originCashinBankAccount:
    accountType: CHECKING
    bankNo: 213
    routingNo: 1
    accountNo: 32631
    accountDv: 0
    ispb: ********
  recipientBankAccount:
    accountType: CHECKING
    routingNo: 1
    accountNo: ********
    accountDv: 2
    ispb: ********

dda:
  activate-wait-minutes: 90
  max-add-user-async: 1000
  provider: ARBI
  maxAddUser: 20

febraban:
  max-amount: 250_000

schedules:
  registerUsersDda:
    cron: "0-50/5 10-23,0 * * *"
  importPartialDda:
    cron: "40 18 * * 1-5"
  balanceMonitoring:
    cron: "*/10 8-23,00-02 * * *"
  notifyInviteReminder:
    cron: "10,24,45 9-21 * * *"
    timeToRemind: 7d
  expireCreditCard:
    cron: "54 * * * *"
  startProcessScheduledBills:
    cron: "*/10 11-23,00-02 * * *"
  expireScheduledBill:
    cron: "00 4-9 * * *"
  disableMediumCreditCardJob:
    cron: "0 0 29 2 0" # 29 de fevereiro em um domingo
  optimizeGoalInstallmentMonthlyValueJob:
    cron: "0 10 1 * *"
  optimizeGoalInstallmentWeeklyValueJob:
    cron: "0 10 * * 1"
  checkAndCompleteGoalsJob:
    cron: "0 10 * * 1-5"

billNotifications:
  notifyBillAdded: true
  notifyScheduleBillNotPayable: true
  notifyScheduledBillExpired: true
  notifyScheduleBillPostponedDueLimitReached: true
  notifyScheduleBillCanceledDueAmountHigherThanDailyLimit: true
  notifyScheduleBillCanceledDueAmountChanged: true
  notifyScheduleBillCanceledDueCreditCardDenied: true
  notifyNotVisibleBillAlreadyExists: true

notificationHints:
  billCreated:
    - "Além dos WhatsApps te lembrando de pagar as contas, Friday faz seus pagamentos de um jeito automático. Experimente!"
    - "Perdendo tempo pagando conta? Com Friday você paga várias contas de uma vez só com apenas 3 cliques. Experimente e economize tempo."
    - "Com Friday você nunca mais vai esquecer de pagar seu personal trainer, diarista ou terapeuta. Agende transferências recorrentes e receba os avisos de vencimento e comprovantes no WhatsApp."
    - "Quer pagar alguma conta que não está no seu CPF ? Basta mandar o PDF do <NAME_EMAIL> para que ele apareça na sua timeline."
  billComingDue:
    - "Precisa pagar uma conta para outra pessoa? Apenas mande o PDF do <NAME_EMAIL> e ele aparece automaticamente na sua timeline."
    - "Tenha total controle e nunca mais esqueça de fazer pagamentos importantes. Com Friday você agenda recorrências e pode suspendê-las quando quiser."
    - "Imagine um mundo em que você não precisa pagar conta por conta em diferentes dias? Com Friday você resolve todas de uma só vez. Experimente!"
    - "Está gostando de receber os lembretes das suas contas a pagar pelo WhatsApp? Imagina se elas forem pagas de jeito automático? Experimente!"
  billComingDueLastWarn:
    - "Suas contas de luz, Internet e outros serviços também podem ser pagas com Friday. Envie o PDF do <NAME_EMAIL> e a conta aparece na sua timeline. Experimente!"
    - "Agende transferências recorrentes com Friday e receba os lembretes de pagamento no seu WhatsApp. Experimente!"
    - "Com Friday você tem a praticidade de pagar vários tipos diferentes de contas de uma vez só. Experimente!"
    - "Os avisos de vencimento no WhatsApp ajudam a lembrar das contas a pagar. Mas que tal simplificar ainda mais seus pagamentos? Agende e pague tudo pela Friday? Experimente!"
  billOverdueYesterday:
    - "Perdeu um pagamento? Envie o <NAME_EMAIL> e receba os avisos de vencimento no WhatsApp. Experimente!"
    - "Agende transferências recorrentes com Friday e receba os lembretes de pagamento no seu WhatsApp. Experimente!"
    - "Ainda dá tempo de fazer esse pagamento com Friday e aproveite também para agendar futuros pagamentos. Experimente!"
    - "Deixou a conta vencer mesmo com o lembrete no WhatsApp? Agende seus pagamentos e não pague mais multa. Experimente!"

utility-account:
  manual-workflow-email: "<EMAIL>"

bill-tracking:
  calculate:
    partitions: 10
    queue: ${sqs.bill-tracking.calculate}
  query:
    partitions: 5
    queue: ${sqs.bill-tracking.query}
  doNotTrackAfterDays: 60


creditCardConfiguration:
  mediumRiskQuota: 0

pushNotifications:
  barcodeBillAddKnownAuthorNoDescriptionSuccessWithHint:
    template: "barcode_bill_add_known_author_no_description_success_with_hint__4_11_1"
    title: "✉️ Nova conta recebida"
    message: "Conta {{1}} (vencimento {{2}}) no valor de {{3}} já se encontra disponível."
    url: ""
  barcodeBillAddKnownAuthorNoDescriptionSuccess:
    template: "barcode_bill_add_known_author_no_description_success__4_11_0"
    title: "✉️ Nova conta recebida"
    message: "Conta {{1}} (vencimento {{2}}) no valor de {{3}} já se encontra disponível."
    url: "/notifications"
  barcodeBillCloseOverdueKnownAuthorNoDescriptionWithHint:
    template: "barcode_bill_close_overdue_known_author_no_description_with_hint__4_11_1"
    title: "🗓️ Conta com vencimento hoje"
    message: "Vence hoje a conta {{1}} no valor de {{2}}."
    url: ""
  walletLastAlertPaymentOverdueTodayWithHint:
    template: "wallet_last_alert_payment_overdue_today_with_hint__4_10_0"
    title: "⏰  Último aviso de pagamentos vencendo"
    message: "Um ou mais pagamentos da carteira {{1}} estão vencendo hoje às {{2}}. Pague agora pra não perder o vencimento."
    url: ""
  walletPaymentOverdueYesterdayWithHint:
    template: "wallet_payment_overdue_yesterday_with_hint__4_10_1"
    title: "⚠️ Pagamentos vencidos ontem"
    message: "Você tem pagamentos vencidos na carteira {{1}} que ainda podem ser pagos clicando no botão abaixo."
    url: ""
  barcodeBillCloseOverdueKnownAuthorNoDescription:
    template: "barcode_bill_close_overdue_known_author_no_description__4_11_0"
    title: "🗓️ Conta com vencimento hoje"
    message: "Vence hoje a conta {{1}} no valor de {{2}}."
    url: ""
  walletLastAlertPaymentOverdueToday:
    template: "wallet_last_alert_payment_overdue_today__4_17_0"
    title: "⏰  Último aviso de pagamentos vencendo"
    message: "Um ou mais pagamentos da carteira {{1}} estão vencendo hoje às {{2}}. Pague agora pra não perder o vencimento."
    url: ""
  walletPaymentOverdueYesterday:
    template: "wallet_payment_overdue_yesterday__4_17_0"
    title: "⚠️ Pagamentos vencidos ontem"
    message: "Você tem pagamentos vencidos na carteira {{1}} que ainda podem ser pagos clicando no botão abaixo."
    url: ""
  creditCardEnabled:
    template: "credit_card_enabled__4_20_0"
    title: "🤩 Novidade Friday para você!"
    message: "Você pediu e a Friday fez! Agora você pode usar até {{1}} {{2}}, em seu cartão de crédito, para fazer os seus pagamentos. Ao utilizar essa função, haverá acréscimo de {{3}} sobre o valor adicionado à carteira."
    url: ""
  subscriptionOverdueSurvey:
    template: "subscription_overdue_survey__4_19_0"
    title: "🔔 Informação sobre a sua assinatura Friday"
    message: "Olá, ainda não conseguimos efetuar o débito da sua assinatura Friday com vencimento em {{1}}."
    url: ""
  walletPixBillReceipt:
    template: "wallet_pix_bill_receipt__4_11_1"
    title: "✅ Transferência feita com sucesso"
    message: "O valor de {{1}} foi transferido para {{2}} ({{3}}).
              Tipo de transferência: {{4}}
              Autorizada por: {{5}}
              Carteira: {{6}}"
    url: ""
  walletBarcodeBillReceipt:
    template: "wallet_barcode_bill_receipt__4_17_0"
    title: "✅ Pagamento realizado com sucesso"
    message: "Pagamento {{1}} (vencimento {{2}}) no valor de {{3}}."
    url: ""
  walletInsufficientBalance:
    template: "wallet_insufficient_balance__4_12_3"
    title: "📢 Saldo insuficiente para seus pagamentos"
    message: "{{1}} faltando para hoje. {{2}} faltando para os próximos 7 dias."
    url: ""
  utilityAccountUpdatedStatus:
    template: "utility_account_update_status__4_22_0"
    title: "Você tem atualizações nas suas contas de consumo"
    message: "Sua conta {{1}} teve o status atualizado para {{2}}."
    url: ""
  utilityAccountConnected:
    template: "connected_utility_account__4_26_0"
    title: "Atualização nas suas contas de consumo"
    message: "Sua conta {{1}} foi conectada."
    url: ""

kms:
  alias: alias/utility-connect-key
  hmacKey: FROM_SECRETS

msisdn-authentication:
  jwt:
    audience: friday.ai
    issuer: https://friday.ai
    duration: 48h
    secret: ${micronaut.security.token.jwt.signatures.secret.generator.secret}

chatbot-transaction-authorization:
  secret: ${micronaut.security.token.jwt.signatures.secret.generator.secret}

cnpj:
  modatta: **************

modatta:
  accounts:
    - ********** # cash in
    - ********** # bolsão
    - ********** # fee modatta
    - ********** # sem uso ainda
    - ********** # sem uso ainda

tracing:
  zipkin:
    enabled: false
    http:
      log-level: OFF
  opentelemetry:
    logging:
      enabled: true
      exporters:
        logging:
          enabled: true
    propagation:
      enabled: true
    instrumentation:
      enabled: true
      annotations:
        enabled: true

otel:
  traces:
    exporter: logging
  http:
    server:
      request-headers:
        - x-request-id

configuration:
  production:
    celcoin.url: "https://apicorp.celcoin.com.br"
  staging:
    celcoin.url: "https://sandbox.openfinance.celcoin.dev/"

fraud-list:
  documents:
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "03309250830"
    - "03722359210"
    - "03768020223"
    - "03873922118"
    - "04434782100"
    - "05149707406"
    - "06217991225"
    - "06301645502"
    - "07792707113"
    - "09070832771"
    - "11282559605"
    - "15922231766"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"
    - "***********"

onboardingTestPix:
  originAccountNo: ${integrations.arbi.contaLiquidacao}

blip-callback:
  allowed-ips: [ ]

baas:
  name: ""
  document: ""
  bank-no: 0
  routing-no: 0
  allowed-ips: [ ]

email-notification:
  emailVerificationTokenSubject: "Friday - Verificação de email"
  emailPasswordRecoveryTokenSubject: "Friday - Recuperação de Senha"

mailbox:
  security:
    rule:
      email-allowed-list:
        enabled: true
      email-blocked-list:
        enabled: true
      user-ignored-bill:
        enabled: true

backoffice:
  document-email:
    recipient: "<EMAIL>"

account-group-selector:
  register:
    enabled: false
