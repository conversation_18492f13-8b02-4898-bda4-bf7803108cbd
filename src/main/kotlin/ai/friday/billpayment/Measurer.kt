package ai.friday.billpayment

import kotlin.system.measureTimeMillis
import kotlin.time.measureTimedValue

inline fun <T> measure(fn: () -> T): Pair<T, Long> {
    val result: T
    val elapsed = measureTimeMillis { result = fn() }
    return result to elapsed
}

data class MeasureResult<T>(val result: T, val elapsed: Long)

inline fun <T> measureTimeInMillis(fn: () -> T): MeasureResult<T> {
    val (result, elapsed) = measure { fn() }
    return MeasureResult(result, elapsed)
}

inline fun <T> measureTimedValue(timeMap: MutableMap<String, Long>, operationName: String, fn: () -> T): T {
    val (result, time) = measureTimedValue(fn)
    timeMap[operationName] = time.inWholeMilliseconds
    return result
}