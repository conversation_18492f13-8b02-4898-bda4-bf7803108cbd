package ai.friday.billpayment.adapters.noop

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.UserPoolEnableUserError
import ai.friday.billpayment.app.account.UserPoolRemoveUserError
import ai.friday.billpayment.app.account.UserPoolSetMfaPreferenceError
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.integrations.UserPoolListAuthEventsException
import arrow.core.Either
import arrow.core.right
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory

@Singleton
@Requires(missingBeans = [UserPoolAdapter::class])
class NoopUserPoolAdapter : UserPoolAdapter {

    private val logger = LoggerFactory.getLogger(NoopUserPoolAdapter::class.java)

    override fun configureUser(username: String, accountId: AccountId, groupNames: List<String>) {
        logger.info("NoopUserPoolAdapter#configureUser")
    }

    override fun createUser(
        username: String,
        password: String,
        accountId: AccountId,
        emailAddress: EmailAddress,
        phoneNumber: MobilePhone,
        role: Role,
        name: String,
        setMFA: Boolean,
    ) {
        logger.info("NoopUserPoolAdapter#createUser")
    }

    override fun doesUserExist(username: String): Boolean {
        logger.info("NoopUserPoolAdapter#doesUserExist")
        return false
    }

    override fun isUserEnabled(username: String): Boolean {
        logger.info("NoopUserPoolAdapter#isUserEnabled")
        return false
    }

    override fun addUserToGroup(username: String, groupName: String) {
        logger.info("NoopUserPoolAdapter#addUserToGroup")
    }

    override fun removeAccountId(username: String): Either<UserPoolRemoveUserError, Unit> {
        logger.info("NoopUserPoolAdapter#removeAccountId")
        return Unit.right()
    }

    override fun disableUser(username: String): Either<UserPoolRemoveUserError, Unit> {
        logger.info("NoopUserPoolAdapter#disableUser")
        return Unit.right()
    }

    override fun removeUserFromGroup(username: String, groupName: String) {
        logger.info("NoopUserPoolAdapter#removeUserFromGroup")
    }

    override fun signOutUser(username: String) {
        logger.info("NoopUserPoolAdapter#signOutUser")
    }

    override fun resetPassword(username: String) {
        logger.info("NoopUserPoolAdapter#resetPassword")
    }

    override fun setUserPassword(username: String, password: String) {
        logger.info("NoopUserPoolAdapter#setUserPassword")
    }

    override fun updatePhoneNumber(username: String, phoneNumber: MobilePhone) {
        logger.info("NoopUserPoolAdapter#updatePhoneNumber")
    }

    override fun updateEmailAddress(username: String, emailAddress: EmailAddress) {
        logger.info("NoopUserPoolAdapter#updateEmailAddress")
    }

    override fun setAccountId(username: String, accountId: AccountId) {
        logger.info("NoopUserPoolAdapter#setAccountId")
    }

    override fun getAccountId(username: String): AccountId? {
        logger.info("NoopUserPoolAdapter#getAccountId")
        return null
    }

    override fun enableUser(username: String, accountId: AccountId?): Either<UserPoolEnableUserError, Unit> {
        logger.info("NoopUserPoolAdapter#enableUser")
        return Unit.right()
    }

    override fun setMfaPreference(username: String, enabled: Boolean): Either<UserPoolSetMfaPreferenceError, Unit> {
        logger.info("NoopUserPoolAdapter#setMfaPreference")
        return Unit.right()
    }

    override fun listUserEvents(username: String): Either<UserPoolListAuthEventsException, List<String>> {
        logger.info("NoopUserPoolAdapter#listUserEvents")
        return listOf("").right()
    }
}