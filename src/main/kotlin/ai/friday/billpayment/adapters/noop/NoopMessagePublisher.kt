package ai.friday.billpayment.adapters.noop

import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.dda.BatchAddOrder
import ai.friday.billpayment.app.dda.BatchMigrationOrder
import ai.friday.billpayment.app.dda.DDARegisterMessage
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.message.QueueMessage
import ai.friday.billpayment.app.message.QueueMessageBatch
import ai.friday.billpayment.app.payment.TransactionId
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton

// FIXME - rever dependencia do InternalBankService
@Singleton
@Requires(env = [MODATTA_ENV])
open class NoopMessagePublisher : MessagePublisher {
    override fun sendMessage(
        queueName: String,
        body: Any,
        delaySeconds: Int?,
    ) {
        TODO("Not yet implemented")
    }

    override fun sendMessage(queueMessage: QueueMessage) {
        TODO("Not yet implemented")
    }

    override fun sendGenerateBillReceipt(billPaid: BillPaid) {
        TODO("Not yet implemented")
    }

    override fun sendMessageBatch(messageBatch: QueueMessageBatch) {
        TODO("Not yet implemented")
    }

    override fun sendRetryTransactionMessage(
        transactionId: TransactionId,
        delaySeconds: Int?,
    ) {
        TODO("Not yet implemented")
    }

    override fun sendRetryCashinMessage(
        transactionId: TransactionId,
        delaySeconds: Int?,
    ) {
        TODO("Not yet implemented")
    }

    override fun sendBatchAddOrderMessage(batchAddOrder: BatchAddOrder) {
        TODO("Not yet implemented")
    }

    override fun sendBatchDDARequestedMessage(batch: List<DDARegisterMessage>) {
        TODO("Not yet implemented")
    }

    override fun sendBatchMigrationOrderMessage(batchMigrationOrder: BatchMigrationOrder) {
        TODO("Not yet implemented")
    }
}