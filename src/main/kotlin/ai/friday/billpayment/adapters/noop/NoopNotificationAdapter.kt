package ai.friday.billpayment.adapters.noop

import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.itp.PaymentIntentId
import ai.friday.billpayment.app.notification.ByteArrayWithNameAndType
import ai.friday.billpayment.app.notification.NotificationMedia
import ai.friday.billpayment.app.onepixpay.OnePixPayId
import ai.friday.billpayment.app.payment.AutomaticPixReceiptData
import ai.friday.billpayment.app.payment.BoletoReceiptData
import ai.friday.billpayment.app.payment.InvoiceReceiptData
import ai.friday.billpayment.app.payment.PixReceiptData
import ai.friday.billpayment.app.payment.ReceiptFilesData
import ai.friday.billpayment.app.utilityaccount.UtilityAccount
import ai.friday.billpayment.app.utilityaccount.UtilityAccountUpdateInfo
import ai.friday.billpayment.app.wallet.Invite
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import java.time.Duration
import java.time.LocalDate
import java.time.LocalTime

// FIXME - rever dependencia do AccountService
@Singleton
@Requires(env = [MODATTA_ENV])
class ModattaNotificationAdapter : NoopNotificationAdapter()

open class NoopNotificationAdapter : NotificationAdapter {
    override fun notifyBillCreated(
        members: List<Member>,
        payee: String,
        amount: Long,
        dueDate: LocalDate,
        billType: BillType,
        billId: BillId,
        billStatus: BillStatus,
        walletId: WalletId,
        walletName: String,
        author: Member?,
        description: String?,
        actionSource: ActionSource,
        hint: String?,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyAddFailure(
        members: List<Member>,
        source: ActionSource,
        from: EmailAddress,
        subject: String,
        walletName: String,
        billId: BillId?,
        walletId: WalletId,
        result: CreateBillResult,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyBillComingDue(
        members: List<Member>,
        author: Member?,
        walletName: String,
        walletId: WalletId,
        billView: BillView,
        hint: String?,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyOnePixPay(
        members: List<Member>,
        walletName: String,
        onePixPayId: OnePixPayId<*>,
        bills: List<BillView>,
        delay: Duration,
        hasBasicAccountLimitExceeded: Boolean,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyBillComingDueSecondaryWallet(members: List<Member>, wallet: Wallet, bills: List<BillView>) {
        TODO("Not yet implemented")
    }

    override fun notifyInvoicePaymentUndone(
        members: List<Member>,
        walletName: String,
        author: Member,
        recipient: Recipient,
        amount: Long,
        settleDate: LocalDate,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyBoletoReceipt(
        members: List<Member>,
        receiptData: BoletoReceiptData,
        receiptFilesData: ReceiptFilesData,
        mailReceiptHtml: CompiledHtml,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyInvoiceReceipt(
        members: List<Member>,
        receiptData: InvoiceReceiptData,
        receiptFilesData: ReceiptFilesData,
        mailReceiptHtml: CompiledHtml,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyPixReceipt(
        members: List<Member>,
        receiptData: PixReceiptData,
        receiptFilesData: ReceiptFilesData,
        mailReceiptHtml: CompiledHtml,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyAutomaticPixReceipt(members: List<Member>, receiptData: AutomaticPixReceiptData, receiptFilesData: ReceiptFilesData, mailReceiptHtml: CompiledHtml) {
        TODO("Not yet implemented")
    }

    override fun notifyCashIn(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        senderName: String,
        senderDocument: String,
        amount: Long,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyCashInPaymentInsufficientBalanceToday(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        pendingAmountToday: Long,
        senderName: String,
        senderDocument: String,
        amount: Long,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyCashInPaymentSufficientBalanceToday(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        senderName: String,
        senderDocument: String,
        amount: Long,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyFounderSelfCashInPaymentSufficientBalanceToday(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        senderName: String,
        senderDocument: String,
        amount: Long,
        senderAccountNo: String?,
        senderBankName: String?,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyInsufficientBalanceToday(
        members: List<Member>,
        pendingScheduledAmountToday: Long,
        pendingAmountTotal7Days: Long,
        pendingAmountTotal15Days: Long,
        walletId: WalletId,
        walletName: String,
        isAfterHours: Boolean,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyInsufficientBalanceTodaySecondaryWallet(members: List<Member>, walletId: WalletId, walletName: String, pendingScheduledAmountToday: Long) {
        TODO("Not yet implemented")
    }

    override fun notifyScheduledBillNotPayable(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        author: Member,
        payee: String,
        dueDate: LocalDate,
        totalAmount: Long,
        description: String,
        billId: BillId,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyCashInFailure(
        members: List<Member>,
        amount: Long,
        errorMessage: String,
        walletId: WalletId,
        walletName: String,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyTransferNotPayable(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        billId: BillId,
        author: Member,
        recipientName: String,
        dueDate: LocalDate,
        amount: Long,
        errorMessage: String,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyBillComingDueLastWarn(
        members: List<Member>,
        wallet: Wallet,
        dueDate: LocalDate,
        paymentLimitTime: LocalTime,
        bills: List<BillView>,
        hint: String?,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyBillOverdueYesterday(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        hint: String?,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyUserActivated(account: Account) {
        TODO("Not yet implemented")
    }

    override fun notifyUpgradeCompleted(account: Account) {
        TODO("Not yet implemented")
    }

    override fun notifyWalletMemberJoined(founder: Member, inviteeFullName: String, walletName: String) {
        TODO("Not yet implemented")
    }

    override fun notifyUserAuthenticationRequired(accountId: AccountId) {
        TODO("Not yet implemented")
    }

    override fun notifyRegisterUpdated(accountId: AccountId, mobilePhone: MobilePhone, name: String) {
        TODO("Not yet implemented")
    }

    override fun notifyBillSchedulePostponedDueLimitReached(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        payee: String,
        totalAmount: Long,
        type: BillType,
        author: Member,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyBillScheduleCanceledDueAmountHigherThanDailyLimit(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        payee: String,
        totalAmount: Long,
        type: BillType,
        author: Member,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyScheduleBillCanceledDueCreditCardDenied(
        members: List<Member>,
        walletId: WalletId,
        billId: BillId,
        billDescription: String,
        amount: Long,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyNotVisibleBillAlreadyExists(
        member: Member,
        walletId: WalletId,
        billId: BillId,
        payee: String,
        dueDate: LocalDate,
        totalAmount: Long,
        walletName: String,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyToken(accountId: AccountId, mobilePhone: MobilePhone, token: String) {
        TODO("Not yet implemented")
    }

    override fun notifyForgotPasswordToken(
        accountId: AccountId,
        document: String,
        email: EmailAddress,
        token: String,
        duration: Duration,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyEmailVerificationToken(
        accountId: AccountId,
        emailAddress: EmailAddress,
        token: String,
        duration: Duration,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyInvitedMember(invite: Invite, receiver: EmailAddress) {
        TODO("Not yet implemented")
    }

    override fun notifyInviteReminder(invite: Invite, account: Account?) {
        TODO("Not yet implemented")
    }

    override fun notifyFirstBillScheduled(account: Account) {
        TODO("Not yet implemented")
    }

    override fun notifyOptOutNotifications(account: Account) {
        TODO("Not yet implemented")
    }

    override fun notifyOnePixPayFailure(accountId: AccountId) {
        TODO("Not yet implemented")
    }

    override fun notifyPaymentIntentFailed(accountId: AccountId, paymentIntentId: PaymentIntentId) {
        TODO("Not yet implemented")
    }

    override fun notifySubscriptionCreated(accountId: AccountId, dueDate: LocalDate, amount: Long) {
        TODO("Not yet implemented")
    }

    override fun notifySubscriptionOverdue(
        accountId: AccountId,
        effectiveDueDate: LocalDate,
        amount: Long,
        alternativeVersion: Boolean,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyInAppSubscriptionOverdue(accountId: AccountId) {
        TODO("Not yet implemented")
    }

    override fun notifySubscriptionOverdueWarningDetailedNotificationsShutdown(accountId: AccountId, effectiveDueDate: LocalDate) {
        TODO("Not yet implemented")
    }

    override fun notifySubscriptionOverdueNotificationChannelDowngradeWarning(accountId: AccountId) {
        TODO("Not yet implemented")
    }

    override fun notifySubscriptionOverdueWarningBillNotificationsAndDDAShutdown(accountId: AccountId, accountClosureDate: LocalDate) {
        TODO("Not yet implemented")
    }

    override fun notifySubscriptionOverdueWarningAccountClosure(accountId: AccountId, daysUntilClosure: Long, closeDate: LocalDate) {
        TODO("Not yet implemented")
    }

    override fun notifySubscriptionOverdueCloseAccount(accountId: AccountId) {
        TODO("Not yet implemented")
    }

    override fun notifyLegacySubscriptionOverdue(accountId: AccountId, days: Long) {
        TODO("Not yet implemented")
    }

    override fun notifyUtilityAccountUpdatedStatus(utilityAccount: UtilityAccount) {
        TODO("Not yet implemented")
    }

    override fun notifyUtilityAccountConnected(utilityAccount: UtilityAccount, info: UtilityAccountUpdateInfo?) {
        TODO("Not yet implemented")
    }

    override fun notifyUtilityAccountRequestReconnection(utilityAccount: UtilityAccount) {
        TODO("Not yet implemented")
    }

    override fun notifyDisconnectLegacyUtilityAccount(utilityAccount: UtilityAccount) {
        TODO("Not yet implemented")
    }

    override fun notifyInvoicesNotFoundUtilityAccount(utilityAccount: UtilityAccount) {
        TODO("Not yet implemented")
    }

    override fun notifyInvoicesScanErrorUtilityAccount(utilityAccount: UtilityAccount) {
        TODO("Not yet implemented")
    }

    override fun notifyCreditCardEnabled(accountId: AccountId, quota: Long) {
        TODO("Not yet implemented")
    }

    override fun notifySubscriptionInsufficientBalance(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        amount: Long,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyPixNotReceivedFailure(
        accountId: AccountId,
        wallet: Wallet,
        amount: Long,
        senderName: String,
        senderDocument: String,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyRegisterDenied(accountId: AccountId, mobilePhone: MobilePhone) {
        TODO("Not yet implemented")
    }

    override fun notifyRegisterUpgraded(accountId: AccountId, mobilePhone: MobilePhone) {
        TODO("Not yet implemented")
    }

    override fun notifyBasicSignUpReopened(accountId: AccountId, mobilePhone: MobilePhone) {
        TODO("Not yet implemented")
    }

    override fun notifySubscriptionGrantedByInvestment(accountId: AccountId, dueDate: LocalDate) {
        TODO("Not yet implemented")
    }

    override fun notifyAccountStatement(
        periodMessage: String,
        emailAddress: EmailAddress,
        name: String,
        files: List<ByteArrayWithNameAndType>,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyWalletSummary(
        periodMessage: String,
        emailAddress: EmailAddress,
        name: String,
        files: List<ByteArrayWithNameAndType>,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyHumanChatWelcome(accountId: AccountId, mobilePhone: MobilePhone) {
        TODO("Not yet implemented")
    }

    override fun notifyTriPixNextDayReminder(account: Account) {
        TODO("Not yet implemented")
    }

    override fun notifyTriPixLastDayReminder(account: Account) {
        TODO("Not yet implemented")
    }

    override fun notifyTriPixExpired(account: Account) {
        TODO("Not yet implemented")
    }

    override fun notifyCustom(
        accountId: AccountId,
        template: String,
        configurationKey: String,
        parameters: List<String>,
        quickReplyButtonsWhatsAppParameter: List<String>,
        buttonWhatsAppParameter: String?,
        media: NotificationMedia?,
    ) {
        TODO("Not yet implemented")
    }

    override fun notifyCustomEmail(accountId: AccountId, template: String, parameters: Map<String, String>) {
        TODO("Not yet implemented")
    }
}