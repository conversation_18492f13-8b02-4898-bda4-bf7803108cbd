package ai.friday.billpayment.adapters.clearsale

import ai.friday.billpayment.adapters.clearsale.ClearSaleInsights.cpfFraudCodes
import ai.friday.billpayment.adapters.clearsale.ClearSaleInsights.ownershipCodes
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.feature.RequiresClearSale
import ai.friday.billpayment.app.integrations.CreditCardOwnership
import ai.friday.billpayment.app.integrations.CreditCardOwnershipValidationRequest
import ai.friday.billpayment.app.integrations.CreditCardScore
import ai.friday.billpayment.app.integrations.CreditCardScoreService
import ai.friday.billpayment.app.integrations.CreditCardScoreValidationRequest
import ai.friday.billpayment.app.integrations.CreditCardValidationService
import ai.friday.billpayment.markers
import arrow.core.Either
import arrow.core.right
import io.micronaut.context.annotation.Context
import io.micronaut.context.event.ApplicationEventListener
import io.micronaut.discovery.event.ServiceReadyEvent
import org.slf4j.LoggerFactory

@Context
@RequiresClearSale
class ClearSaleCreditCardValidationService(
    private val transactionAdapter: ClearSaleTransactionAdapter,
    private val authAdapter: ClearSaleAuthAdapter,
    private val configuration: ClearSaleConfiguration,
) : CreditCardValidationService,
    CreditCardScoreService,
    ApplicationEventListener<ServiceReadyEvent> {

    private val logger = LoggerFactory.getLogger(ClearSaleCreditCardValidationService::class.java)

    override fun validateOwnership(validationRequest: CreditCardOwnershipValidationRequest): Either<Exception, CreditCardOwnership> =
        findCreditCardInsights(
            bin = validationRequest.bin,
            lastFourDigits = validationRequest.lastFourDigits,
            document = Document.sanitized(validationRequest.cpf),
        ).fold(
            onFailure = { Either.Left(Exception(it)) }, // from Result.failure to Either.Left

            onSuccess = { insights ->
                when (insights.any { it.code in ownershipCodes }) {
                    true -> CreditCardOwnership.IS_OWNER.right()
                    false -> CreditCardOwnership.NOT_OWNER.right()
                }.also { result ->
                    logger.info(
                        markers(
                            "bin" to validationRequest.bin,
                            "last4" to validationRequest.lastFourDigits,
                            "document" to validationRequest.cpf,
                            "result" to result.getOrNull(),
                            "codes" to insights.map { it.code },
                        ),
                        "ClearSaleCreditCardValidationService#validateOwnership",
                    )
                }
            },
        )

    override fun creditcardScore(scoreRequest: CreditCardScoreValidationRequest): Either<Exception, CreditCardScore> =
        findCreditCardInsights(
            bin = scoreRequest.bin,
            lastFourDigits = scoreRequest.lastFourDigits,
            document = Document.sanitized(scoreRequest.cpf),
        ).fold(
            onFailure = { Either.Left(Exception(it)) }, // from Result.failure to Either.Left

            onSuccess = { insights ->
                val codes = insights.map { it.code }

                return Either.Right(
                    when {
                        codes.any { code -> code in configuration.trustedCodes } -> CreditCardScore.MATCH
                        codes.any { code -> code in cpfFraudCodes } -> CreditCardScore.NO_MATCH

                        else -> CreditCardScore.MANUAL_CHECK
                    }.also { result ->
                        logger.info(
                            markers(
                                "bin" to scoreRequest.bin,
                                "last4" to scoreRequest.lastFourDigits,
                                "document" to scoreRequest.cpf,
                                "result" to result,
                                "codes" to insights.map { it.code },
                            ),
                            "ClearSaleCreditCardValidationService#validateOwnership",
                        )
                    },
                )
            },
        )

    private fun findCreditCardInsights(bin: String, lastFourDigits: String, document: Document): Result<List<TrustCardResponse.Insight>> =
        transactionAdapter.createTransaction(document).fold(
            onFailure = { Result.failure(it) },

            onSuccess = {
                transactionAdapter.transactionInsights(
                    transactionId = it.id,
                    bin = bin,
                    lastFourDigits = lastFourDigits,
                )
            },
        )

    override fun onApplicationEvent(event: ServiceReadyEvent?) {
        authAdapter.revalidateToken()
    }
}