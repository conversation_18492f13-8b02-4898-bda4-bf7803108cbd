package ai.friday.billpayment.adapters.clearsale

import com.fasterxml.jackson.annotation.JsonEnumDefaultValue

// most fields are nullable for conservative deserialization once we dont need them all

data class ClearSaleTransaction(
    val id: String,
    val createdAt: String?,
    val documentType: String?,
    val document: String?,
    val name: String?,
    val birthdate: String?,
    val mothersName: String?,
    val email: String?,
    val verifiedEmail: Boolean?,
    val sessionId: String?,
    val identifierId: String?,
    val address: Address?,
    val phone: Phone?,
    val card: Card?,
    val secondaryDocumentType: String?,
    val secondaryDocument: String?,
    val criterion: Int?,
    val referenceDate: String?,
    val identifierDate: String?,
    val type: Int?,
) {
    data class Address(
        val zipCode: String,
        val street: String,
        val number: String,
        val complement: String,
        val district: String,
        val city: String,
        val state: String,
        val country: String,
    )

    data class Phone(
        val countryCode: String?,
        val areaCode: String?,
        val number: String?,
        val verified: Boolean?,
    )

    data class Card(
        val bin: String?,
        val last4: String?,
        val ownerName: String?,
        val ownerDocument: String?,
    )
}

data class CreateTransactionRequest(val document: String, val documentType: String)

data class CreateTrustCardRequest(val bin: String, val last4: String)

data class TrustCardResponse(val insights: List<Insight>) {
    data class Insight(
        val code: InsightCode,
        val description: String?,
        val relevance: String?,
        val type: String?,
        val category: String?,
        val relatedTo: List<String>?,
    )
}

enum class InsightCode {
    CPF0001, // O CPF já foi envolvido em fraude há menos de 1 mês
    CPF0002, // O CPF já foi envolvido em fraude pela última vez entre 1 e 3 meses
    CPF0003, // O CPF já foi envolvido em fraude pela última vez entre 3 e 6 meses
    CPF0004, // O CPF já foi envolvido em fraude pela última vez entre 6 meses e 1 ano
    CPF0005, // O CPF já foi envolvido em fraude pela última vez entre 1 e 2 anos
    CPF0006, // O CPF já foi envolvido em fraude pela última vez entre 2 a 3 anos
    CPF0007, // O CPF já foi envolvido em fraude pela última vez há mais de 3 anos

    CRT0001, // BIN de baixo risco
    CRT0002, // BIN de médio risco
    CRT0003, // BIN de alto risco
    CRT0004, // Cartão de Crédito possui vínculo forte com CPF
    CRT0005, // Cartão de Crédito possui vínculo com CPF
    CRT0006, // Cartão de crédito não possui vínculo com CPF
    CRT0007, // CPF possui vínculo com cartões de crédito de outro BIN

    CRT0008, // Relacionado ao CPF possui vínculo forte com o Cartão de crédito
    CRT0009, // BIN vazado na internet

    @JsonEnumDefaultValue
    UNKNOWN, // Fallback to avoid deserialization crashes
}

object ClearSaleInsights {
    val cpfFraudCodes = listOf(
        InsightCode.CPF0001,
        InsightCode.CPF0002,
        InsightCode.CPF0003,
        InsightCode.CPF0004,
        InsightCode.CPF0005,
        InsightCode.CPF0006,
        InsightCode.CPF0007,
    )

    val ownershipCodes = listOf(InsightCode.CRT0004)
}