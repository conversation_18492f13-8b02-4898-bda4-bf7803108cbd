package ai.friday.billpayment.adapters.clearsale

import ai.friday.billpayment.app.feature.RequiresClearSale
import ai.friday.billpayment.headers
import ai.friday.billpayment.localFormat
import ai.friday.billpayment.markers
import ai.friday.billpayment.plus
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import org.slf4j.LoggerFactory

@Singleton
@RequiresClearSale
class ClearSaleAuthAdapter(
    @param:Client(id = "clearsale") private val httpClient: RxHttpClient,
    private val configuration: ClearSaleConfiguration,
) {
    private var token: ClearSaleToken? = null
    private val tokenMutex = Mutex()

    /*
        * Acquire a valid token to be used in ClearSale API requests.
        * If the token is not valid, a new one is fetched.
        * The mutual exclusion is guaranteed by the tokenMutex only for the token fetching process.
        * Reading the token is not protected by the mutex.
        * Token will be invalidated when the request fails with 401.
        *
        * @return a valid token
     */
    fun acquireToken(): Result<String> {
        token.validValue()?.let { return Result.success(it) }

        return runBlocking { fetchNewToken() }
    }

    fun revalidateToken() = CoroutineScope(Dispatchers.IO).launch {
        token = null
        fetchNewToken(force = true)
    }

    private suspend fun fetchNewToken(force: Boolean = false): Result<String> = tokenMutex.withLock {
        if (!force) {
            token.validValue()?.let { return@withLock Result.success(it) }
        }

        val markers = markers(
            "lastTokenHashCode" to token?.value?.hashCode(),
            "lastTokenExpiration" to token?.expiration?.localFormat(),
        )

        try {
            val request = HttpRequest.POST(
                "/v1/authentication",
                AuthenticationRequest(
                    username = configuration.username,
                    password = configuration.password,
                ),
            ).headers(
                "Content-Type" to "application/json",
            )

            val call = httpClient.retrieve(
                request,
                Argument.of(AuthenticationResponse::class.java),
                Argument.STRING,
            )

            val response = call.firstOrError().blockingGet()
            token = ClearSaleToken(
                value = response.token,
                expiration = getZonedDateTime().plusSeconds(response.expiresInSeconds),
            )

            logger.info(
                markers.plus(
                    "newTokenHashCode" to token?.value?.hashCode(),
                    "newTokenExpiration" to token?.expiration?.localFormat(),
                ),
                "ClearSaleAuthAdapter#getToken",
            )
            return Result.success(response.token)
        } catch (e: Exception) {
            when (e) {
                is HttpClientResponseException -> markers.add(e.markers())
                else -> markers.andAppend("errorMessage", e.message)
            }

            logger.error(markers, "ClearSaleAuthAdapter#getToken", e)
            return Result.failure(e)
        }
    }

    //

    private val logger = LoggerFactory.getLogger(ClearSaleAuthAdapter::class.java)

    init {
        logger.info(markers("host" to configuration.host), "ClearSaleAuthAdapter#initialized")
    }
}

@ConfigurationProperties("integrations.clearsale")
@RequiresClearSale
interface ClearSaleConfiguration {
    val username: String
    val password: String
    val host: String
    val trustedCodes: Set<InsightCode>
}

private data class ClearSaleToken(val value: String, val expiration: ZonedDateTime)

private fun ClearSaleToken?.isValid(): Boolean = this != null && this.expiration > getZonedDateTime()

private fun ClearSaleToken?.validValue(): String? = when {
    this.isValid() -> this?.value
    else -> null
}

private data class AuthenticationResponse(val token: String, val expiresInSeconds: Long)

@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy::class)
private data class AuthenticationRequest(val username: String, val password: String)