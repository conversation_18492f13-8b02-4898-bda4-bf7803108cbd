package ai.friday.billpayment.adapters.clearsale

import ai.friday.billpayment.adapters.clearsale.Path.TRANSACTION_PATH
import ai.friday.billpayment.adapters.clearsale.Path.TRUST_CARD_PATH
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.feature.RequiresClearSale
import ai.friday.billpayment.markers
import ai.friday.billpayment.merge
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory

private object Path {
    const val TRANSACTION_PATH = "/v1/transaction"
    const val TRUST_CARD_PATH = "/v1/transaction/{id}/trustcard"
}

@Singleton
@RequiresClearSale
class ClearSaleTransactionAdapter(
    @param:Client(id = "clearsale") private val httpClient: RxHttpClient,
    private val auth: ClearSaleAuthAdapter,
) {
    private val logger = LoggerFactory.getLogger(ClearSaleTransactionAdapter::class.java)

    private fun auth(request: MutableHttpRequest<*>) = request.bearerAuth(auth.acquireToken().getOrThrow())

    fun createTransaction(document: Document): Result<ClearSaleTransaction> {
        val request = HttpRequest.POST(
            TRANSACTION_PATH,
            CreateTransactionRequest(document = document.value, documentType = document.type.value),
        )

        val markers = request.markers()

        return try {
            val response = httpClient.exchange(
                auth(request),
                Argument.of(ClearSaleTransaction::class.java),
                Argument.STRING,
            ).blockingFirst()

            logger.info(markers.and(response.markers()), "ClearSaleTransactionAdapter#createTransaction")

            Result.success(response.body())
        } catch (e: Exception) {
            if (e is HttpClientResponseException) {
                markers.add(e.markers())

                if (e.status == HttpStatus.UNAUTHORIZED) {
                    auth.revalidateToken()
                }
            }

            logger.error(markers, "ClearSaleTransactionAdapter#createTransaction", e)
            Result.failure(e)
        }
    }

    fun transactionInsights(
        transactionId: String,
        bin: String,
        lastFourDigits: String,
    ): Result<List<TrustCardResponse.Insight>> {
        val request = HttpRequest.POST(
            TRUST_CARD_PATH.replace("{id}", transactionId),
            CreateTrustCardRequest(bin = bin.take(6), last4 = lastFourDigits.takeLast(4)),
        )

        val markers = markers("transactionId" to transactionId, "module" to " trustcards").merge(request.markers())

        return try {
            val call = httpClient.exchange(
                auth(request),
                Argument.of(TrustCardResponse::class.java),
                Argument.STRING,
            )

            val response = call.blockingFirst()

            logger.info(markers.merge(response.markers()), "ClearSaleAdapter#transactionInsights")

            Result.success(response.body().insights)
        } catch (e: Exception) {
            if (e is HttpClientResponseException) markers.add(e.markers())
            logger.error(markers, "ClearSaleAdapter#transactionInsights")

            Result.failure(e)
        }
    }

    fun findTransaction(transactionId: String): Result<ClearSaleTransaction> {
        val request = HttpRequest.GET<Unit>("$TRANSACTION_PATH/$transactionId")

        val markers = markers("transactionId" to transactionId, "module" to " trustcards").merge(request.markers())

        return try {
            val response = httpClient.exchange(
                auth(request),
                Argument.of(ClearSaleTransaction::class.java),
                Argument.STRING,
            ).firstOrError().blockingGet()

            logger.info(markers.and(response.markers()), "ClearSaleTransactionAdapter#getTransaction")

            Result.success(response.body())
        } catch (e: Exception) {
            if (e is HttpClientResponseException) {
                markers.add(e.markers())

                if (e.status == HttpStatus.UNAUTHORIZED) {
                    auth.revalidateToken() // async
                }
            }

            logger.error(markers, "ClearSaleTransactionAdapter#getTransaction", e)
            Result.failure(e)
        }
    }
}