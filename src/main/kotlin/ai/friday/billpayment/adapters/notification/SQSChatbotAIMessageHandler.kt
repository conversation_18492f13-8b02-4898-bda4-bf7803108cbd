package ai.friday.billpayment.adapters.notification

import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.MessageHandlerConfiguration
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.app.notification.MessageProcessorService
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.tracing.annotation.NewSpan
import io.via1.communicationcentre.app.notification.NotificationSendException
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSChatbotAIMessageHandler(
    amazonSQS: SqsClient,
    configuration: ChatbotAiMessageHandlerConfiguration,
    private val messageProcessorService: MessageProcessorService,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = configuration.chatbotAiMessageQueueName,
    consumers = 3,
) {
    @NewSpan
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val markers = Markers.append("eventMessage", message)

        logger.info(markers, "SQSChatbotAIMessageHandler/start")

        messageProcessorService.process(message.body())

        logger.info(markers, "SQSChatbotAIMessageHandler/end")

        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    override fun handleError(
        message: Message,
        e: Exception,
    ): SQSHandlerResponse {
        if (e is NotificationSendException && e.isPermanent) {
            logger.warn("SQSChatbotAIMessageHandler", e)
            return SQSHandlerResponse(shouldDeleteMessage = true)
        }
        logger.error("SQSChatbotAIMessageHandler", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSChatbotAIMessageHandler::class.java)
    }
}

@ConfigurationProperties("sqs")
class ChatbotAiMessageHandlerConfiguration
@ConfigurationInject
constructor(
    override val sqsWaitTime: Int,
    override val dlqArn: String,
    override val maxReceiveCount: Int,
    override val visibilityTimeout: Int,
    override val maxNumberOfMessages: Int,
    val chatbotAiMessageQueueName: String,
) : MessageHandlerConfiguration {
    override val sqsCoolDownTime = 3
}