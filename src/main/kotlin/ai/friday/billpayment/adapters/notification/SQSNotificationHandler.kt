package ai.friday.billpayment.adapters.notification

import ai.friday.billpayment.adapters.messaging.Handler
import ai.friday.billpayment.app.notification.MessageProcessorService
import ai.friday.morning.messaging.MessageHandler
import ai.friday.morning.messaging.MessageHandlerResponse
import io.micronaut.tracing.annotation.NewSpan
import io.via1.communicationcentre.app.notification.NotificationSendException
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

@Handler
@Singleton
open class SQSNotificationHandler(
    private val messageProcessorService: MessageProcessorService,
) : MessageHandler {

    override val configurationName = "bill-notification"

    @NewSpan
    override fun handleMessage(message: Message): MessageHandlerResponse {
        messageProcessorService.process(message.body())

        logger.info("SQSNotificationHandler")

        return MessageHandlerResponse.delete()
    }

    override fun handleException(e: Exception): MessageHandlerResponse {
        if (e is NotificationSendException && e.isPermanent) {
            logger.error("SQSNotificationHandler", e)
            return MessageHandlerResponse.delete()
        }
        logger.warn("SQSNotificationHandler", e)
        return MessageHandlerResponse.keep()
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSNotificationHandler::class.java)
    }
}