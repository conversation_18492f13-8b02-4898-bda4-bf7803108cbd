package ai.friday.billpayment.adapters.notification

import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.app.notification.MessageProcessorService
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSNotificationStatusHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = "sqs.queues.whatsappNotificationVerifier") private val whatsappNotificationVerifierQueueName: String,
    private val messageProcessorService: MessageProcessorService,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    consumers = 3,
    queueName = whatsappNotificationVerifierQueueName,
) {
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val markers = Markers.empty()

        val shouldDeleteMessage = messageProcessorService.verifyStatus(message)

        logger.info(markers.andAppend("shouldDeleteMessage", shouldDeleteMessage), "SQSNotificationStatusHandler")
        return SQSHandlerResponse(shouldDeleteMessage = shouldDeleteMessage)
    }

    override fun handleError(
        message: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error("SQSNotificationStatusHandler", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSNotificationStatusHandler::class.java)
    }
}