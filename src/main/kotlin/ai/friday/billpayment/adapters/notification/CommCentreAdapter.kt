package ai.friday.billpayment.adapters.notification

import ai.friday.billpayment.ByteWrapper
import ai.friday.billpayment.adapters.dynamodb.DEFAULT_LEGACY_ACCOUNT_CONFIGURATION
import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.FinancialInstitutionGlobalData
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.account.getDisplayName
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.AddBillError
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.bill.name
import ai.friday.billpayment.app.forecast.ForecastPeriod
import ai.friday.billpayment.app.formatBrazilCurrency
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.NotificationService
import ai.friday.billpayment.app.itp.PaymentIntentId
import ai.friday.billpayment.app.notification.BillPaymentNotification
import ai.friday.billpayment.app.notification.BillPaymentNotificationChannel
import ai.friday.billpayment.app.notification.ButtonDeeplinkParameter
import ai.friday.billpayment.app.notification.ButtonRawParameter
import ai.friday.billpayment.app.notification.ButtonWebParameter
import ai.friday.billpayment.app.notification.ByteArrayWithNameAndType
import ai.friday.billpayment.app.notification.EmailNotification
import ai.friday.billpayment.app.notification.MultiChannelConfigurationName
import ai.friday.billpayment.app.notification.MultiChannelNotification
import ai.friday.billpayment.app.notification.NotificationMedia
import ai.friday.billpayment.app.notification.NotificationTemplate
import ai.friday.billpayment.app.notification.NotificationType
import ai.friday.billpayment.app.notification.TemplatesConfiguration
import ai.friday.billpayment.app.notification.WhatsappNotification
import ai.friday.billpayment.app.notification.buildChangeWalletLimitsButtonPath
import ai.friday.billpayment.app.notification.buildOnePixPayQuickReply
import ai.friday.billpayment.app.notification.buildReceiptBillButtonPath
import ai.friday.billpayment.app.notification.buildUtilityAccountButtonPath
import ai.friday.billpayment.app.notification.buildWalletBillButtonPath
import ai.friday.billpayment.app.notification.buildWalletButtonPath
import ai.friday.billpayment.app.notification.buildWalletInviteMemberWithAccountButtonPath
import ai.friday.billpayment.app.notification.buildWalletTimelineButtonPath
import ai.friday.billpayment.app.notification.buildWalletTimelineQuickReply
import ai.friday.billpayment.app.onepixpay.OnePixPayId
import ai.friday.billpayment.app.payment.AutomaticPixReceiptData
import ai.friday.billpayment.app.payment.BoletoReceiptData
import ai.friday.billpayment.app.payment.InvoiceReceiptData
import ai.friday.billpayment.app.payment.PixReceiptData
import ai.friday.billpayment.app.payment.ReceiptData
import ai.friday.billpayment.app.payment.ReceiptFilesData
import ai.friday.billpayment.app.payment.buildFormattedAmount
import ai.friday.billpayment.app.payment.formatAccountNumber
import ai.friday.billpayment.app.payment.formatBankData
import ai.friday.billpayment.app.payment.formatRoutingNumber
import ai.friday.billpayment.app.payment.maskAccountNo
import ai.friday.billpayment.app.payment.maskDocumentSpecialAsterisk
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.utilityaccount.UtilityAccount
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionStatus
import ai.friday.billpayment.app.utilityaccount.UtilityAccountUpdateInfo
import ai.friday.billpayment.app.utilityaccount.UtilityConnectionMethod
import ai.friday.billpayment.app.utils.TemplateHelper
import ai.friday.billpayment.app.wallet.Invite
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.simpleName
import ai.friday.billpayment.mediaTypeFrom
import ai.friday.billpayment.toAmountFormat
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.mapNotNull
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import io.micronaut.core.util.StringUtils
import io.via1.communicationcentre.adapters.email.SESAdapterConfiguration
import io.via1.communicationcentre.app.notification.NotificationFormatter
import jakarta.inject.Singleton
import java.time.Duration
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import kotlin.contracts.ExperimentalContracts
import kotlin.contracts.contract
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

private const val BILL_PAYEE = "BILL_PAYEE"
private const val BILL_DUE_DATE = "BILL_DUE_DATE"
private const val BILL_AMOUNT = "BILL_AMOUNT"
private const val BILL_SOURCE = "BILL_SOURCE"
private const val BILL_CREATED_BY = "BILL_CREATED_BY"
private const val BILL_ID = "BILL_ID"
const val WALLET_ID = "WALLET_ID"
private const val BILL_DESCRIPTION = "BILL_DESCRIPTION"
private const val WALLET_NAME = "WALLET_NAME"
private const val BILL_HINT = "BILL_HINT"
private const val HINT = "HINT"
const val BILL_PAYMENT_LIMIT_TIME = "BILL_PAYMENT_LIMIT_TIME"
const val SENDER_INFO = "SENDER_INFO"
const val RECEIVED_AMOUNT = "RECEIVED_AMOUNT"

@Singleton
@Requirements(Requires(beans = [FinancialInstitutionGlobalData::class]), Requires(env = [FRIDAY_ENV, ME_POUPE_ENV]))
class CommCentreAdapter(
    // Essa classe não deveria se chamar CommCentreAdapter. Ela não conhece o commcenter. Ela fala com um SQS que vai no commcentre (para whatsapp)
    private val accountRepository: AccountRepository,
    private val billPaymentEmailSenderService: BillPaymentEmailSenderService,
    private val emailSenderConfiguration: SESAdapterConfiguration,
    private val systemActivityService: SystemActivityService,
    private val notificationService: NotificationService,
    @Property(name = "creditCard.installments.fees.1") private val fee: Double,
    private val templatesConfiguration: TemplatesConfiguration,
    private val featureConfiguration: FeatureConfiguration,
) : NotificationAdapter {
    @field:Property(name = "urls.site")
    lateinit var siteUrl: String

    @field:Property(name = "features.sendEmailReceipt", defaultValue = "true")
    var sendEmailReceipt: Boolean = false

    @field:Property(name = "email-notification.emailVerificationTokenSubject")
    lateinit var emailVerificationTokenSubject: String

    @field:Property(name = "email-notification.emailPasswordRecoveryTokenSubject")
    lateinit var emailPasswordRecoveryTokenSubject: String

    override fun notifyBillCreated(
        members: List<Member>,
        payee: String,
        amount: Long,
        dueDate: LocalDate,
        billType: BillType,
        billId: BillId,
        billStatus: BillStatus,
        walletId: WalletId,
        walletName: String,
        author: Member?,
        description: String?,
        actionSource: ActionSource,
        hint: String?,
    ) {
        notificationService.notifyMembers(members, NotificationType.BILL_CREATED) { account ->
            if (actionSource !is ActionSource.DDA || account.configuration.receiveDDANotification) {
                if (billType.isBoleto()) {
                    notifyBoleto(account, author, payee, dueDate, actionSource, walletName, description, hint, amount, walletId, billId, billStatus)
                } else {
                    notifyPixOrInvoiceOrInvestment(description, account, payee, dueDate, amount, actionSource, author, walletName, walletId, billId)
                }
            } else {
                null
            }
        }
    }

    private fun notifyPixOrInvoiceOrInvestment(
        description: String?,
        account: Account,
        payee: String,
        dueDate: LocalDate,
        amount: Long,
        actionSource: ActionSource,
        author: Member?,
        walletName: String,
        walletId: WalletId,
        billId: BillId,
    ): MultiChannelNotification {
        val configuration = if (description == null) MultiChannelConfigurationName("invoice-add-no-description-success") else MultiChannelConfigurationName("invoice-add-with-description-success")

        return MultiChannelNotification(
            preferredChannel = BillPaymentNotificationChannel.CHATBOT,
            receiver = account,
            walletId = walletId,
            notificiationType = NotificationType.BILL_CREATED,
            configurationName = configuration,
            parameters = mapOf(
                BILL_PAYEE to payee,
                BILL_DUE_DATE to NotificationFormatter.buildFormattedDueDate(dueDate),
                BILL_AMOUNT to NotificationFormatter.buildFormattedAmount(amount),
                BILL_DESCRIPTION to description?.ifEmpty { null },
                BILL_SOURCE to actionSource.name(),
                BILL_CREATED_BY to author?.simpleName(),
                WALLET_NAME to walletName,
                BILL_ID to billId.value,
            ).mapNotNull { it.value },
            actionButton = buildWalletBillButtonPath(walletId, billId),
        )
    }

    private fun notifyBoleto(
        account: Account,
        author: Member?,
        payee: String,
        dueDate: LocalDate,
        actionSource: ActionSource,
        walletName: String,
        description: String?,
        hint: String?,
        amount: Long,
        walletId: WalletId,
        billId: BillId,
        billStatus: BillStatus,
    ): MultiChannelNotification {
        return if (billStatus == BillStatus.WAITING_APPROVAL && actionSource is ActionSource.WalletMailBox) {
            notifyWaitingApprovalBoleto(
                dueDate = dueDate,
                payee = payee,
                account = account,
                amount = amount,
                actionSource = actionSource,
                walletName = walletName,
                walletId = walletId,
                billId = billId,
            )
        } else {
            notifyBoletoForActiveAccount(
                author = author,
                description = description,
                hint = hint,
                account = account,
                payee = payee,
                dueDate = dueDate,
                amount = amount,
                actionSource = actionSource,
                walletName = walletName,
                walletId = walletId,
                billId = billId,
            )
        }
    }

    private fun notifyWaitingApprovalBoleto(
        account: Account,
        payee: String,
        dueDate: LocalDate,
        amount: Long,
        actionSource: ActionSource.WalletMailBox,
        walletName: String,
        walletId: WalletId,
        billId: BillId,
    ): MultiChannelNotification {
        return MultiChannelNotification(
            preferredChannel = BillPaymentNotificationChannel.CHATBOT,
            receiver = account,
            walletId = walletId,
            notificiationType = NotificationType.BILL_CREATED,
            configurationName = MultiChannelConfigurationName("barcode-bill-add-waiting-approval"),
            parameters = mapOf(
                BILL_PAYEE to payee,
                BILL_DUE_DATE to NotificationFormatter.buildFormattedDueDate(dueDate),
                BILL_AMOUNT to NotificationFormatter.buildFormattedAmount(amount),
                BILL_SOURCE to actionSource.name(),
                BILL_CREATED_BY to actionSource.from,
                WALLET_NAME to walletName,
                BILL_ID to billId.value,
            ),
            actionButton = buildWalletBillButtonPath(walletId, billId),
        )
    }

    private fun notifyBoletoForActiveAccount(
        author: Member?,
        description: String?,
        hint: String?,
        account: Account,
        payee: String,
        dueDate: LocalDate,
        amount: Long,
        actionSource: ActionSource,
        walletName: String,
        walletId: WalletId,
        billId: BillId,
    ): MultiChannelNotification {
        var usedHint: String? = null
        val configurationName =
            when {
                (author?.simpleName() != null && description?.ifEmpty { null } != null) -> MultiChannelConfigurationName("barcode-bill-add-known-author-with-description-success")
                (author?.simpleName() != null && description?.ifEmpty { null } == null) ->
                    if (hint == null) {
                        MultiChannelConfigurationName("barcode-bill-add-known-author-no-description-success")
                    } else {
                        usedHint = hint
                        MultiChannelConfigurationName("barcode-bill-add-known-author-no-description-success-with-hint")
                    }

                (author?.simpleName() == null && description?.ifEmpty { null } != null) -> MultiChannelConfigurationName("barcode-bill-add-no-author-with-description-success")
                else -> MultiChannelConfigurationName("barcode-bill-add-no-author-no-description-success")
            }

        return MultiChannelNotification(
            preferredChannel = if (actionSource is ActionSource.DDA && dueDate.isAfter(getLocalDate().plusDays(3))) BillPaymentNotificationChannel.PUSH else BillPaymentNotificationChannel.CHATBOT,
            receiver = account,
            walletId = walletId,
            notificiationType = NotificationType.BILL_CREATED,
            configurationName = configurationName,
            parameters = mapOf(
                BILL_PAYEE to payee,
                BILL_DUE_DATE to NotificationFormatter.buildFormattedDueDate(dueDate),
                BILL_AMOUNT to NotificationFormatter.buildFormattedAmount(amount),
                BILL_DESCRIPTION to description?.ifEmpty { null },
                BILL_SOURCE to actionSource.name(),
                BILL_CREATED_BY to author?.simpleName(),
                WALLET_NAME to walletName,
                BILL_HINT to usedHint,
                BILL_ID to billId.value,
            ).mapNotNull { it.value },
            actionButton = buildWalletBillButtonPath(walletId, billId),
        )
    }

    override fun notifyBillComingDue(
        members: List<Member>,
        author: Member?,
        walletName: String,
        walletId: WalletId,
        billView: BillView,
        hint: String?,
    ) {
        notificationService.notifyMembers(members, NotificationType.BILL_COMING_DUE) { account ->
            if (billView.billType.isBoleto()) {
                var usedHint: String? = null
                val description = billView.billDescription.ifEmpty { null }

                val (template, configurationKey) =
                    when {
                        (author?.simpleName() != null && description != null) -> templatesConfiguration.whatsappTemplates.barcodeBillCloseOverdueKnownAuthorWithDescription to "barcode-bill-close-overdue-known-author-with-description"
                        (author?.simpleName() != null && description == null) ->
                            if (hint == null) {
                                templatesConfiguration.whatsappTemplates.barcodeBillCloseOverdueKnownAuthorNoDescription to "barcode-bill-close-overdue-known-author-no-description"
                            } else {
                                usedHint = hint
                                templatesConfiguration.whatsappTemplates.barcodeBillCloseOverdueKnownAuthorNoDescriptionWithHint to "barcode-bill-close-overdue-known-author-no-description-with-hint"
                            }

                        (author?.simpleName() == null && description != null) -> templatesConfiguration.whatsappTemplates.barcodeBillCloseOverdueNoAuthorWithDescription to "barcode-bill-close-overdue-no-author-with-description"
                        else -> templatesConfiguration.whatsappTemplates.barcodeBillCloseOverdueNoAuthorNoDescription to "barcode-bill-close-overdue-no-author-no-description"
                    }

                // TODO: criar cenario de teste
                WhatsappNotification(
                    accountId = account.accountId,
                    receiver = MobilePhone(account.mobilePhone),
                    template = NotificationTemplate(template),
                    configurationKey = configurationKey,
                    parameters = buildList {
                        add(billView.payee)
                        add(NotificationFormatter.buildFormattedAmount(billView.amount))
                        add(description)
                        add(billView.source.name())
                        add(author?.simpleName())
                        add(walletName)
                        add(usedHint)
                    }.filterNotNull(),
                    buttonWhatsAppParameter = buildWalletBillButtonPath(walletId = walletId, billId = billView.billId),
                )
            } else {
                val authorName =
                    if (author != null) {
                        author.simpleName()
                    } else {
                        logger.warn(
                            Markers.append("billId", billView.billId)
                                .and(Markers.append("errorMessage", "Invoice without author")),
                            "notifyBillComingDue",
                        )
                        StringUtils.EMPTY_STRING
                    }

                val (template, configurationKey) = if (billView.billDescription.ifEmpty { null } != null) {
                    templatesConfiguration.whatsappTemplates.invoiceCloseOverdueWithDescription to "invoice-close-overdue-with-description"
                } else {
                    templatesConfiguration.whatsappTemplates.invoiceCloseOverdueNoDescription to "invoice-close-overdue-no-description"
                }

                // TODO: criar cenario de teste
                WhatsappNotification(
                    accountId = account.accountId,
                    receiver = MobilePhone(account.mobilePhone),
                    template = NotificationTemplate(template),
                    configurationKey = configurationKey,
                    parameters = buildList {
                        add(billView.payee)
                        add(NotificationFormatter.buildFormattedAmount(billView.amount))
                        add(billView.billDescription.ifEmpty { null })
                        add(billView.source.name())
                        add(authorName)
                        add(walletName)
                    }.filterNotNull(),
                    buttonWhatsAppParameter = buildWalletBillButtonPath(walletId, billView.billId),
                )
            }
        }
    }

    // TODO: verificar parametros pois entrou um novo template (walletOnePixPaySingular)
    override fun notifyOnePixPay(
        members: List<Member>,
        walletName: String,
        onePixPayId: OnePixPayId<*>,
        bills: List<BillView>,
        delay: Duration,
        hasBasicAccountLimitExceeded: Boolean,
    ) {
        notificationService.notifyMembers(members, NotificationType.ONE_PIX_PAY) { account ->
            val (template, configurationKey) = if (bills.size > 1) {
                templatesConfiguration.whatsappTemplates.walletOnePixPay to "wallet-one-pix-pay"
            } else {
                templatesConfiguration.whatsappTemplates.walletOnePixPaySingular to "wallet-one-pix-pay-singular"
            }
            val paymentLimitTime = bills.minOf { it.paymentLimitTime }

            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(template),
                configurationKey = configurationKey,
                parameters = buildList {
                    add(bills.size.toString())
                    add(NotificationFormatter.buildFormattedAmount(bills.sumOf { it.amountTotal }))
                    add(walletName)
                    add(NotificationFormatter.buildFormattedTime(paymentLimitTime))
                }.filterNotNull(),
                quickReplyButtonsWhatsAppParameter = buildOnePixPayQuickReply(onePixPayId, paymentLimitTime, hasBasicAccountLimitExceeded),
            )
        }
    }

    override fun notifyBillComingDueSecondaryWallet(
        members: List<Member>,
        wallet: Wallet,
        bills: List<BillView>,
    ) {
        notificationService.notifyMembers(members, NotificationType.BILL_COMING_DUE_SECONDARY_WALLET) { account ->
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.billComingDueSecondaryWallet.resolveTemplateVariation(bills.size)),
                configurationKey = "bill-coming-due-secondary-wallet",
                parameters = buildList {
                    add(wallet.name)
                    add(NotificationFormatter.buildFormattedAmount(bills.sumOf { it.amountTotal }))
                    for (bill in bills.take(10)) {
                        add(bill.formattedDescription())
                        add(bill.amount.toAmountFormat())
                    }
                }.filterNotNull(),
                buttonWhatsAppParameter = buildWalletTimelineButtonPath(wallet.id),
            )
        }
    }

    override fun notifyCashInFailure(
        members: List<Member>,
        amount: Long,
        errorMessage: String,
        walletId: WalletId,
        walletName: String,
    ) {
        notificationService.notifyMembers(members, NotificationType.CASH_IN_FAILURE) { account ->
            // TODO: criar cenario de teste
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletCashInWithCreditCardFailed),
                configurationKey = "wallet-cash-in-with-credit-card-failed",
                parameters = buildList {
                    add(NotificationFormatter.buildFormattedAmount(amount))
                    add(errorMessage)
                    add(walletName)
                }.filterNotNull(),
                buttonWhatsAppParameter = buildWalletButtonPath(walletId),
            )
        }
    }

    override fun notifyAddFailure(
        members: List<Member>,
        source: ActionSource,
        from: EmailAddress,
        subject: String,
        walletName: String,
        billId: BillId?,
        walletId: WalletId,
        result: CreateBillResult,
    ) {
        notificationService.notifyMembers(members, NotificationType.ADD_FAILURE) { account ->
            buildAddFailureNotification(account, source, from, subject, walletName, billId, walletId, result)
        }
    }

    override fun notifyTransferNotPayable(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        billId: BillId,
        author: Member,
        recipientName: String,
        dueDate: LocalDate,
        amount: Long,
        errorMessage: String,
    ) {
        notificationService.notifyMembers(members, NotificationType.TRANSFER_NOT_PAYABLE) { account ->
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletInvoicePaymentFailure),
                configurationKey = "wallet-invoice-payment-failure",
                parameters = buildList {
                    add(recipientName)
                    add(NotificationFormatter.buildFormattedDueDate(dueDate))
                    add(NotificationFormatter.buildFormattedAmount(amount))
                    add(errorMessage)
                    add(author.simpleName())
                    add(walletName)
                },
                buttonWhatsAppParameter = buildWalletBillButtonPath(walletId, billId),
            )
        }
    }

    override fun notifyBillComingDueLastWarn(
        members: List<Member>,
        wallet: Wallet,
        dueDate: LocalDate,
        paymentLimitTime: LocalTime,
        bills: List<BillView>,
        hint: String?,
    ) {
        notificationService.notifyMembers(members, NotificationType.BILL_COMING_DUE_LAST_WARN) { account ->
            val (template, configurationKey) = if (hint == null) {
                templatesConfiguration.whatsappTemplates.walletLastAlertPaymentOverdueToday.resolveTemplateVariation(bills = bills.size) to "wallet-last-alert-payment-overdue-today"
            } else {
                templatesConfiguration.whatsappTemplates.walletLastAlertPaymentOverdueTodayWithHint.resolveTemplateVariation(bills = bills.size) to "wallet-last-alert-payment-overdue-today-with-hint"
            }

            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(template),
                configurationKey = configurationKey,
                parameters = buildList {
                    add(wallet.name)
                    add(NotificationFormatter.buildFormattedTime(paymentLimitTime))
                    for (bill in bills) {
                        add(bill.formattedDescription())
                        add(bill.amount.toAmountFormat())
                    }
                    add(hint)
                }.filterNotNull(),
                buttonWhatsAppParameter = buildWalletTimelineButtonPath(wallet.id),
            )
        }
    }

    override fun notifyBillOverdueYesterday(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        hint: String?,
    ) {
        notificationService.notifyMembers(members, NotificationType.BILL_OVERDUE_YESTERDAY) { account ->
            val configurationName = MultiChannelConfigurationName(
                /**
                 * TODO: como não estamos conseguindo passar uma notificação sem hint como utility, removemos o código abaixo temporariamente
                 * if (hint == null) "wallet-payment-overdue-yesterday" else "wallet-payment-overdue-yesterday-with-hint",
                 */
                "wallet-payment-overdue-yesterday",
            )

            MultiChannelNotification(
                preferredChannel = BillPaymentNotificationChannel.CHATBOT,
                receiver = account,
                walletId = walletId,
                notificiationType = NotificationType.BILL_OVERDUE_YESTERDAY,
                configurationName = configurationName,
                parameters = mapOf(
                    WALLET_NAME to walletName,
                    // TODO: removido temporariamente (HINT to hint)
                ).mapNotNull { it.value },
                actionButton = buildWalletTimelineButtonPath(walletId),
            )
        }
    }

    override fun notifyUserActivated(account: Account) {
        notificationService.sendNotification(
            account = account,
            type = NotificationType.USER_ACTIVATED,
            billPaymentNotification = WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.welcomeAccountCreatedWithoutChatbot),
                configurationKey = "welcome-account-created-without-chatbot",
                buttonWhatsAppParameter = ButtonDeeplinkParameter("entrar?primeiro-acesso"),
            ),
        )
    }

    override fun notifyUpgradeCompleted(account: Account) {
        notificationService.sendNotification(
            account = account,
            type = NotificationType.UPGRADE_COMPLETED,
            billPaymentNotification = WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.upgradeCompleted),
                configurationKey = "upgrade-completed",
                buttonWhatsAppParameter = ButtonDeeplinkParameter("entrar"),
            ),
        )
    }

    override fun notifyRegisterDenied(
        accountId: AccountId,
        mobilePhone: MobilePhone,
    ) {
        notificationService.sendNotification(
            account = null, // FIXME - usar o AccountConfiguration novo. Será necessario migrar esse dado do AccountConfigurationLegacy
            type = NotificationType.REGISTER_DENIED,
            billPaymentNotification = WhatsappNotification(
                accountId = accountId,
                receiver = mobilePhone,
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.registerDenied),
                configurationKey = "register-denied",
            ),
        )
    }

    override fun notifyRegisterUpgraded(
        accountId: AccountId,
        mobilePhone: MobilePhone,
    ) {
        notificationService.sendNotification(
            account = null, // FIXME - usar o AccountConfiguration novo. Será necessario migrar esse dado do AccountConfigurationLegacy
            type = NotificationType.REGISTER_DENIED,
            billPaymentNotification = WhatsappNotification(
                accountId = accountId,
                receiver = MobilePhone(mobilePhone.msisdn),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.registerUpgraded),
                configurationKey = "register-upgraded",
            ),
        )
    }

    override fun notifyWalletMemberJoined(
        founder: Member,
        inviteeFullName: String,
        walletName: String,
    ) {
        val account = accountRepository.findById(founder.accountId)

        notificationService.sendNotification(
            account = account,
            type = NotificationType.WALLET_MEMBER_JOINED,
            billPaymentNotification = WhatsappNotification(
                accountId = founder.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletMemberJoined),
                configurationKey = "wallet-member-joined",
                parameters = buildList {
                    add(founder.name.substringBefore(" "))
                    add(inviteeFullName)
                    add(walletName)
                },
            ),
        )
    }

    override fun notifyUserAuthenticationRequired(accountId: AccountId) {
        val account = accountRepository.findById(accountId)
        notificationService.sendNotification(
            account = account,
            type = NotificationType.USER_AUTHENTICATION_REQUIRED,
            billPaymentNotification = WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.userAuthenticationRequired),
                configurationKey = "user-authentication-required",
                buttonWhatsAppParameter = ButtonDeeplinkParameter("?xp=1"),
            ),
        )
    }

    override fun notifyRegisterUpdated(
        accountId: AccountId,
        mobilePhone: MobilePhone,
        name: String,
    ) {
        notificationService.sendNotification(
            account = null, // FIXME - usar o AccountConfiguration novo. Será necessario migrar esse dado do AccountConfigurationLegacy
            type = NotificationType.REGISTER_UPDATED,
            billPaymentNotification = WhatsappNotification(
                accountId = accountId,
                receiver = mobilePhone,
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.registerUpdated),
                configurationKey = "register-updated",
                parameters = buildList {
                    add(name)
                },
            ),
        )
    }

    override fun notifyInvoicePaymentUndone(
        members: List<Member>,
        walletName: String,
        author: Member,
        recipient: Recipient,
        amount: Long,
        settleDate: LocalDate,
    ) {
        notificationService.notifyMembers(members, NotificationType.INVOICE_PAYMENT_UNDONE) { account ->
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletInvoicePaymentReturned),
                configurationKey = "wallet-invoice-payment-returned",
                parameters = buildList {
                    add(recipient.name)
                    add(NotificationFormatter.buildFormattedDueDate(settleDate))
                    add(NotificationFormatter.buildFormattedAmount(amount))
                    add(author.simpleName())
                    add(walletName)
                },
                buttonWhatsAppParameter = ButtonRawParameter(siteUrl),
            )
        }
    }

    @OptIn(ExperimentalContracts::class)
    private fun shouldSendImageReceipt(imageUrl: String?): Boolean {
        contract {
            returns(true) implies (imageUrl != null)
        }

        return featureConfiguration.imageReceipt && imageUrl != null
    }

    override fun notifyBoletoReceipt(
        members: List<Member>,
        receiptData: BoletoReceiptData,
        receiptFilesData: ReceiptFilesData,
        mailReceiptHtml: CompiledHtml,
    ) {
        notificationService.notifyMembers(members, NotificationType.BOLETO_RECEIPT) { account ->
            if (sendEmailReceipt) {
                billPaymentEmailSenderService.sendReceiptEmail(
                    receiptData,
                    receiptFilesData,
                    mailReceiptHtml,
                    account.emailAddress,
                )
            }

            buildReceiptWithImageNotification(account, receiptData, receiptFilesData) ?: WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletBarcodeBillReceipt),
                configurationKey = "wallet-barcode-bill-receipt",
                parameters = buildList {
                    add(receiptData.recipient?.name ?: receiptData.assignor)
                    add(NotificationFormatter.buildFormattedDueDate(receiptData.dueDate))
                    add(NotificationFormatter.buildFormattedAmount(receiptData.totalAmount))
                    add(receiptData.barcode.formattedDigitable())
                    add(receiptData.scheduledBy!!)
                    add(receiptData.walletName!!)
                    add(templatesConfiguration.landingPageUrl)
                    add(NotificationFormatter.buildFormattedPaymentDateTime(receiptData.dateTime))
                    add(receiptData.authentication)
                },
                buttonWhatsAppParameter = buildReceiptBillButtonPath(receiptData.walletId, receiptData.billId),
            )
        }
    }

    private fun buildReceiptWithImageNotification(
        account: Account,
        receiptData: ReceiptData,
        receiptFilesData: ReceiptFilesData,
    ): WhatsappNotification? {
        return if (shouldSendImageReceipt(receiptFilesData.imageUrl)) {
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletBillReceiptImage),
                configurationKey = "wallet-bill-receipt-image",
                parameters = buildList {
                    add(NotificationFormatter.buildFormattedAmount(receiptData.totalAmount))
                    add(receiptData.recipientName)
                },
                media = NotificationMedia.Image(receiptFilesData.imageUrl, mediaTypeFrom(receiptFilesData.imageFormat).toString(), null, null),
            )
        } else {
            if (receiptFilesData.imageUrl == null) {
                logger.error(
                    Markers
                        .append("accountId", account.accountId.value)
                        .andAppend("billId", receiptData.billId.value)
                        .andAppend("context", "link da imagem deveria ter sido gerado e não foi."),
                    "CommCentreAdapter#buildReceiptWithImageNotification",
                )
            }
            null
        }
    }

    override fun notifyInvoiceReceipt(
        members: List<Member>,
        receiptData: InvoiceReceiptData,
        receiptFilesData: ReceiptFilesData,
        mailReceiptHtml: CompiledHtml,
    ) {
        notificationService.notifyMembers(members, NotificationType.INVOICE_RECEIPT) { account ->
            if (sendEmailReceipt) {
                billPaymentEmailSenderService.sendReceiptEmail(
                    receiptData,
                    receiptFilesData,
                    mailReceiptHtml,
                    account.emailAddress,
                )
            }

            buildReceiptWithImageNotification(account, receiptData, receiptFilesData) ?: WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletInvoiceBillReceipt),
                configurationKey = "wallet-invoice-bill-receipt",
                parameters = buildList {
                    add(NotificationFormatter.buildFormattedAmount(receiptData.totalAmount))
                    add(receiptData.recipient.name)
                    add(maskDocumentSpecialAsterisk(receiptData.recipient.document!!))
                    add("TED")
                    add(receiptData.scheduledBy!!)
                    add(receiptData.walletName!!)
                    add(receiptData.payeeBank)
                    add(formatRoutingNumber(receiptData.recipient.bankAccount!!.routingNo.toString()))
                    add(formatAccountNumber(receiptData.recipient.bankAccount.accountNo, receiptData.recipient.bankAccount.accountDv))
                    add(receiptData.payer.name!!)
                    add(NotificationFormatter.getDocumentType(receiptData.payer.document))
                    add(maskDocumentSpecialAsterisk(receiptData.payer.document))
                    add(receiptData.payerFinancialInstitution!!.name)
                    add(formatRoutingNumber(receiptData.payerBankAccount!!.routingNo.toString()))
                    add(formatAccountNumber(receiptData.payerBankAccount.accountNo.toBigInteger(), receiptData.payerBankAccount.accountDv))
                    add(templatesConfiguration.landingPageUrl)
                    add(NotificationFormatter.buildFormattedPaymentDateTime(receiptData.dateTime))
                    add(receiptData.authentication)
                },
                buttonWhatsAppParameter = buildReceiptBillButtonPath(receiptData.walletId, receiptData.billId),
            )
        }
    }

    override fun notifyPixReceipt(
        members: List<Member>,
        receiptData: PixReceiptData,
        receiptFilesData: ReceiptFilesData,
        mailReceiptHtml: CompiledHtml,
    ) {
        notificationService.notifyMembers(members, NotificationType.PIX_RECEIPT) { account ->
            if (sendEmailReceipt) {
                billPaymentEmailSenderService.sendReceiptEmail(
                    receiptData,
                    receiptFilesData,
                    mailReceiptHtml,
                    account.emailAddress,
                )
            }

            buildReceiptWithImageNotification(account, receiptData, receiptFilesData) ?: WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletPixBillReceipt),
                configurationKey = "wallet-pix-bill-receipt",
                parameters = buildList {
                    add(NotificationFormatter.buildFormattedAmount(receiptData.totalAmount))
                    add(receiptData.recipient.name)
                    add(maskDocumentSpecialAsterisk(receiptData.recipient.document!!))
                    add("PIX")
                    add(receiptData.scheduledBy!!)
                    add(receiptData.walletName!!)
                    add(receiptData.payeeFinancialInstitution.name)
                    add(formatRoutingNumber(receiptData.payeeRoutingNo.toString()))
                    add(formatAccountNumber(receiptData.payeeAccountNo, receiptData.payeeAccountDv))
                    add(receiptData.payer.name!!)
                    add(maskDocumentSpecialAsterisk(receiptData.payer.document))
                    add(formatBankData(receiptData.payerFinancialInstitution))
                    add(formatRoutingNumber(receiptData.payerBankAccount.routingNo.toString()))
                    add(formatAccountNumber(receiptData.payerBankAccount.accountNo.toBigInteger(), receiptData.payerBankAccount.accountDv))
                    add(receiptData.authentication)
                    add(templatesConfiguration.landingPageUrl)
                    add(NotificationFormatter.buildFormattedPaymentDateTime(receiptData.dateTime))
                },
                buttonWhatsAppParameter = buildReceiptBillButtonPath(receiptData.walletId, receiptData.billId),
            )
        }
    }

    override fun notifyAutomaticPixReceipt(
        members: List<Member>,
        receiptData: AutomaticPixReceiptData,
        receiptFilesData: ReceiptFilesData,
        mailReceiptHtml: CompiledHtml,
    ) {
        notificationService.notifyMembers(members, NotificationType.PIX_RECEIPT) { account ->
            if (sendEmailReceipt) {
                billPaymentEmailSenderService.sendReceiptEmail(
                    receiptData,
                    receiptFilesData,
                    mailReceiptHtml,
                    account.emailAddress,
                )
            }

            buildReceiptWithImageNotification(account, receiptData, receiptFilesData) ?: WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletPixBillReceipt),
                configurationKey = "wallet-pix-bill-receipt",
                parameters = buildList {
                    add(NotificationFormatter.buildFormattedAmount(receiptData.totalAmount))
                    add(receiptData.pixReceiptData.recipient.name)
                    add(maskDocumentSpecialAsterisk(receiptData.pixReceiptData.recipient.document!!))
                    add("PIX AUTOMÁTICO")
                    add(receiptData.scheduledBy!!)
                    add(receiptData.walletName!!)
                    add(receiptData.pixReceiptData.payeeFinancialInstitution.name)
                    add(formatRoutingNumber(receiptData.pixReceiptData.payeeRoutingNo.toString()))
                    add(formatAccountNumber(receiptData.pixReceiptData.payeeAccountNo, receiptData.pixReceiptData.payeeAccountDv))
                    add(receiptData.pixReceiptData.payer.name!!)
                    add(maskDocumentSpecialAsterisk(receiptData.pixReceiptData.payer.document))
                    add(formatBankData(receiptData.pixReceiptData.payerFinancialInstitution))
                    add(formatRoutingNumber(receiptData.pixReceiptData.payerBankAccount.routingNo.toString()))
                    add(formatAccountNumber(receiptData.pixReceiptData.payerBankAccount.accountNo.toBigInteger(), receiptData.pixReceiptData.payerBankAccount.accountDv))
                    add(receiptData.pixReceiptData.authentication)
                    add(templatesConfiguration.landingPageUrl)
                    add(NotificationFormatter.buildFormattedPaymentDateTime(receiptData.dateTime))
                },
                buttonWhatsAppParameter = buildReceiptBillButtonPath(receiptData.walletId, receiptData.billId),
            )
        }
    }

    override fun notifyCashIn(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        senderName: String,
        senderDocument: String,
        amount: Long,
    ) {
        notificationService.notifyMembers(members, NotificationType.CASH_IN) { account ->
            MultiChannelNotification(
                preferredChannel = BillPaymentNotificationChannel.CHATBOT,
                receiver = account,
                walletId = walletId,
                notificiationType = NotificationType.CASH_IN,
                configurationName = MultiChannelConfigurationName("wallet-cash-in-no-payments-pending"),
                parameters = mapOf(
                    WALLET_NAME to walletName,
                    RECEIVED_AMOUNT to NotificationFormatter.buildFormattedAmount(amount),
                    SENDER_INFO to String.format("%s (%s)", senderName, NotificationFormatter.formatDocument(maskDocumentSpecialAsterisk(senderDocument))),
                ),
                actionButton = buildWalletButtonPath(walletId),
            )
        }
    }

    override fun notifyCashInPaymentInsufficientBalanceToday(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        pendingAmountToday: Long,
        senderName: String,
        senderDocument: String,
        amount: Long,
    ) {
        notificationService.notifyMembers(members, NotificationType.CASH_IN_PAYMENT_INSUFFICIENT_BALANCE_TODAY) { account ->
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletCashInInsufficientBalance),
                configurationKey = "wallet-cash-in-insufficient-balance",
                parameters = buildList {
                    add(walletName)
                    add(NotificationFormatter.buildFormattedAmount(amount))
                    add(walletName)
                    add(String.format("%s (%s)", senderName, maskDocumentSpecialAsterisk(senderDocument)))
                    add(NotificationFormatter.buildFormattedAmount(pendingAmountToday))
                },
                buttonWhatsAppParameter = buildWalletButtonPath(walletId),
            )
        }
    }

    override fun notifyCashInPaymentSufficientBalanceToday(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        senderName: String,
        senderDocument: String,
        amount: Long,
    ) {
        notificationService.notifyMembers(members, NotificationType.CASH_IN_PAYMENT_SUFFICIENT_BALANCE_TODAY) { account ->
            MultiChannelNotification(
                preferredChannel = BillPaymentNotificationChannel.CHATBOT,
                receiver = account,
                walletId = walletId,
                notificiationType = NotificationType.CASH_IN_PAYMENT_SUFFICIENT_BALANCE_TODAY,
                configurationName = MultiChannelConfigurationName("wallet-cash-in-sufficient-balance"),
                parameters = mapOf(
                    WALLET_NAME to walletName,
                    RECEIVED_AMOUNT to NotificationFormatter.buildFormattedAmount(amount),
                    SENDER_INFO to String.format("%s (%s)", senderName, NotificationFormatter.formatDocument(maskDocumentSpecialAsterisk(senderDocument))),
                ),
                actionButton = buildWalletButtonPath(walletId),
            )
        }
    }

    override fun notifyFounderSelfCashInPaymentSufficientBalanceToday(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        senderName: String,
        senderDocument: String,
        amount: Long,
        senderAccountNo: String?,
        senderBankName: String?,

    ) {
        val listWithMember = members.filter { it.type == MemberType.FOUNDER }

        notificationService.notifyMembers(listWithMember, NotificationType.CASH_IN_PAYMENT_SUFFICIENT_BALANCE_TODAY_OWNER) { account ->
            WhatsappNotification(
                accountId = null, // TODO passar o accountId quando for liberar para a base toda
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletFounderSelfCashInSufficientBalance),
                configurationKey = "wallet-founder-self-cash-in-sufficient-balance",
                parameters = buildList {
                    add(NotificationFormatter.buildFormattedAmount(amount))
                    add(maskAccountNo(senderAccountNo))
                    add(senderBankName!!)
                },
            )
        }
    }

    override fun notifyScheduledBillNotPayable(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        author: Member,
        payee: String,
        dueDate: LocalDate,
        totalAmount: Long,
        description: String,
        billId: BillId,
    ) {
        notificationService.notifyMembers(members, NotificationType.SCHEDULED_BILL_NOT_PAYABLE) { account ->
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletBarcodeBillApprovedPaymentNonPayable),
                configurationKey = "wallet-barcode-bill-approved-payment-non-payable",
                parameters = buildList {
                    add(payee)
                    add(NotificationFormatter.buildFormattedDueDate(dueDate))
                    add(NotificationFormatter.buildFormattedAmount(totalAmount))
                    add(author.simpleName())
                    add(walletName)
                    add(description)
                },
                buttonWhatsAppParameter = buildWalletBillButtonPath(walletId, billId),
            )
        }
    }

    override fun notifyInsufficientBalanceToday(
        members: List<Member>,
        pendingScheduledAmountToday: Long,
        pendingAmountTotal7Days: Long,
        pendingAmountTotal15Days: Long,
        walletId: WalletId,
        walletName: String,
        isAfterHours: Boolean,
    ) {
        notificationService.notifyMembers(members, NotificationType.INSUFFICIENT_BALANCE_TODAY) { account ->
            if (systemActivityService.hasCashedIn(walletId)) {
                val (template, configurationKey) = if (isAfterHours) {
                    templatesConfiguration.whatsappTemplates.walletInsufficientBalanceAfterHours to "wallet-insufficient-balance-after-hours"
                } else {
                    templatesConfiguration.whatsappTemplates.walletInsufficientBalance to "wallet-insufficient-balance"
                }

                WhatsappNotification(
                    accountId = account.accountId,
                    receiver = MobilePhone(account.mobilePhone),
                    template = NotificationTemplate(template),
                    configurationKey = configurationKey,
                    parameters = buildList {
                        add(NotificationFormatter.buildFormattedAmount(pendingScheduledAmountToday))
                        add(NotificationFormatter.buildFormattedAmount(pendingAmountTotal7Days))
                        add(NotificationFormatter.buildFormattedAmount(pendingAmountTotal15Days))
                        add(walletName)
                    },
                    quickReplyButtonsWhatsAppParameter = buildList {
                        add("${walletId.value}#${ForecastPeriod.TODAY}")
                        add("${walletId.value}#${ForecastPeriod.SEVEN_DAYS}")
                        add("${walletId.value}#${ForecastPeriod.FIFTEEN_DAYS}")
                    },
                )
            } else {
                WhatsappNotification(
                    accountId = account.accountId,
                    receiver = MobilePhone(account.mobilePhone),
                    template = NotificationTemplate(templatesConfiguration.whatsappTemplates.insufficientBalanceFirstCashIn),
                    configurationKey = "insufficient-balance-first-cash-in",
                    parameters = buildList {
                        add(NotificationFormatter.buildFormattedAmount(pendingScheduledAmountToday))
                        add(walletName)
                    },
                    quickReplyButtonsWhatsAppParameter = buildList {
                        add(walletId.value)
                    },
                )
            }
        }
    }

    override fun notifyInsufficientBalanceTodaySecondaryWallet(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        pendingScheduledAmountToday: Long,
    ) {
        notificationService.notifyMembers(members, NotificationType.INSUFFICIENT_BALANCE_TODAY_SECONDARY_WALLET) { account ->
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletInsufficientBalanceSecondaryWallet),
                configurationKey = "wallet-insufficient-balance-secondary-wallet",
                parameters = buildList {
                    add(NotificationFormatter.buildFormattedAmount(pendingScheduledAmountToday))
                    add(walletName)
                },
                quickReplyButtonsWhatsAppParameter = buildList {
                    add(buildWalletTimelineQuickReply(walletId).value)
                },
            )
        }
    }

    override fun notifyBillSchedulePostponedDueLimitReached(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        payee: String,
        totalAmount: Long,
        type: BillType,
        author: Member,
    ) {
        notificationService.notifyMembers(members, NotificationType.BILL_SCHEDULE_POSTPONED_DUE_LIMIT_REACHED) { account ->
            val billType = when (type) {
                BillType.PIX -> "PIX"
                BillType.INVOICE -> "TED"
                else -> type.name
            }

            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletBillSchedulePostponedDueLimitReached),
                configurationKey = "wallet-bill-schedule-postponed-due-limit-reached",
                parameters = buildList {
                    add(NotificationFormatter.buildFormattedAmount(totalAmount))
                    add(billType)
                    add(payee)
                    add(walletName)
                    add(author.simpleName())
                },
                buttonWhatsAppParameter = buildChangeWalletLimitsButtonPath(walletId),
            )
        }
    }

    override fun notifyBillScheduleCanceledDueAmountHigherThanDailyLimit(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        payee: String,
        totalAmount: Long,
        type: BillType,
        author: Member,
    ) {
        notificationService.notifyMembers(members, NotificationType.BILL_SCHEDULE_CANCELED_DUE_AMOUNT_HIGHER_THAN_DAILY_LIMIT) { account ->
            val billType = when (type) {
                BillType.PIX -> "PIX"
                BillType.INVOICE -> "TED"
                else -> type.name
            }

            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletBillScheduleCanceledDueAmountHigherThanDailyLimit),
                configurationKey = "wallet-bill-schedule-canceled-due-amount-higher-than-daily-limit",
                parameters = buildList {
                    add(NotificationFormatter.buildFormattedAmount(totalAmount))
                    add(billType)
                    add(payee)
                    add(walletName)
                    add(author.simpleName())
                },
                buttonWhatsAppParameter = buildChangeWalletLimitsButtonPath(walletId),
            )
        }
    }

    override fun notifyHumanChatWelcome(
        accountId: AccountId,
        mobilePhone: MobilePhone,
    ) {
        notificationService.sendNotification(
            account = null,
            type = NotificationType.REGISTER_DENIED,
            billPaymentNotification = WhatsappNotification(
                accountId = accountId,
                receiver = mobilePhone,
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.fredOnboardingWelcome),
                configurationKey = "fred-onboarding-welcome",
            ),
        )
    }

    override fun notifyTriPixNextDayReminder(account: Account) {
        notificationService.notifyAccount(account, NotificationType.TRI_PIX_NEXT_DAY_REMINDER) {
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.triPixReminderNextDay),
                configurationKey = "tri-pix-reminder-next-day",
            )
        }
    }

    override fun notifyTriPixLastDayReminder(account: Account) {
        notificationService.notifyAccount(account, NotificationType.TRI_PIX_LAST_DAY_REMINDER) {
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.triPixReminderLastDay),
                configurationKey = "tri-pix-reminder-last-day",
            )
        }
    }

    override fun notifyTriPixExpired(account: Account) {
        notificationService.notifyAccount(account, NotificationType.TRI_PIX_EXPIRED) {
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.triPixExpired),
                configurationKey = "tri-pix-expired",
            )
        }
    }

    override fun notifyScheduleBillCanceledDueCreditCardDenied(
        members: List<Member>,
        walletId: WalletId,
        billId: BillId,
        payee: String,
        amount: Long,
    ) {
        notificationService.notifyMembers(members, NotificationType.BILL_SCHEDULE_CANCELED_DUE_CREDIT_CARD_DENIED) { account ->
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletBillScheduleCanceledDueCreditCardDenied),
                configurationKey = "wallet-bill-schedule-canceled-due-credit-card-denied",
                parameters = buildList {
                    add(payee)
                    add(NotificationFormatter.buildFormattedAmount(amount))
                },
                buttonWhatsAppParameter = buildWalletBillButtonPath(walletId = walletId, billId = billId),
            )
        }
    }

    override fun notifyNotVisibleBillAlreadyExists(
        member: Member,
        walletId: WalletId,
        billId: BillId,
        payee: String,
        dueDate: LocalDate,
        totalAmount: Long,
        walletName: String,
    ) {
        notificationService.notifyMembers(listOf(member), NotificationType.NOT_VISIBLE_BILL_ALREADY_EXISTS) { account ->
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.postalBoxAddNotVisibleBillAlreadyExists),
                configurationKey = "postal-box-add-not-visible-bill-already-exists",
                parameters = buildList {
                    add(payee)
                    add(NotificationFormatter.buildFormattedDueDate(dueDate))
                    add(NotificationFormatter.buildFormattedAmount(totalAmount))
                    add(walletName)
                },
                buttonWhatsAppParameter = buildWalletBillButtonPath(walletId, billId),
            )
        }
    }

    override fun notifyInvitedMember(
        invite: Invite,
        receiver: EmailAddress,
    ) {
        val memberAccount = accountRepository.findAccountByDocumentOrNull(invite.memberDocument)

        notificationService.sendNotification(
            account = memberAccount,
            type = NotificationType.INVITED_MEMBER,
            billPaymentNotification = buildInviteNotification(
                invite = invite,
                account = memberAccount,
                email = receiver,
            ),
        )
    }

    override fun notifyInviteReminder(
        invite: Invite,
        account: Account?,
    ) {
        if (account == null && invite.emailAddress == null) {
            logger.warn(Markers.append("invite", invite), "notifyInviteReminder")
            return
        }

        val notification = buildInviteReminderNotification(invite, account) ?: return
        notificationService.sendNotification(account, NotificationType.INVITED_MEMBER, notification)
    }

    override fun notifyFirstBillScheduled(account: Account) {
        notificationService.notifyAccount(account, NotificationType.FIRST_BILL_SCHEDULED) {
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.firstBillScheduled),
                configurationKey = "first-bill-scheduled",
            )
        }
    }

    override fun notifyOptOutNotifications(account: Account) {
        logger.info("CommCentreAdapter#notifyOptOut")
    }

    override fun notifyOnePixPayFailure(accountId: AccountId) {
        notificationService.notifyAccount(accountId, NotificationType.ONE_PIX_PAY_FAILURE) { account ->
            WhatsappNotification(
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletOnePixPayFailure),
                configurationKey = "wallet-one-pix-pay-failure",
                buttonWhatsAppParameter = ButtonDeeplinkParameter("entrar"),
            )
        }
    }

    override fun notifySubscriptionGrantedByInvestment(
        accountId: AccountId,
        dueDate: LocalDate,
    ) {
        notificationService.notifyAccount(accountId, NotificationType.SUBSCRIPTION_GRANTED_BY_INVESTMENT) { account ->
            WhatsappNotification(
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.subscriptionGrantedByInvestment),
                configurationKey = "subscription-granted-by-investment",
                parameters = buildList {
                    add(account.name.substringBefore(" "))
                },
            )
        }
    }

    override fun notifySubscriptionCreated(
        accountId: AccountId,
        dueDate: LocalDate,
        amount: Long,
    ) {
        notificationService.notifyAccount(accountId, NotificationType.SUBSCRIPTION_CREATED) { account ->
            WhatsappNotification(
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.subscriptionCreated),
                configurationKey = "subscription-created",
                parameters = buildList {
                    add(NotificationFormatter.buildFormattedDueDate(dueDate))
                    add(NotificationFormatter.buildFormattedAmount(amount))
                },
            )
        }
    }

    override fun notifySubscriptionOverdue(
        accountId: AccountId,
        effectiveDueDate: LocalDate,
        amount: Long,
        alternativeVersion: Boolean,
    ) {
        notificationService.notifyAccount(accountId, NotificationType.SUBSCRIPTION_OVERDUE) { account ->
            val (template, configurationKey) = if (alternativeVersion) {
                templatesConfiguration.whatsappTemplates.subscriptionOverdueDay02 to "subscription-overdue-day-02"
            } else {
                templatesConfiguration.whatsappTemplates.subscriptionOverdueDay01 to "subscription-overdue-day-01"
            }
            WhatsappNotification(
                accountId = accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(template),
                configurationKey = configurationKey,
                parameters = buildList {
                    add(effectiveDueDate.format(DateTimeFormatter.ofPattern("dd/MM")))
                    if (!alternativeVersion) {
                        add(buildFormattedAmount(amount))
                    }
                },
                quickReplyButtonsWhatsAppParameter = buildList {
                    add("""{"payload": "", "action": "SEND_SUBSCRIPTION_PIX_CODE"}""") // TODO: mover para NotificationUtility
                },
            )
        }
    }

    override fun notifyInAppSubscriptionOverdue(accountId: AccountId) {
        notificationService.notifyAccount(accountId, NotificationType.SUBSCRIPTION_OVERDUE) { account ->
            WhatsappNotification(
                accountId = accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.inAppSubscriptionOverdue),
                configurationKey = "in-app-subscription-overdue",
                parameters = emptyList(),
                quickReplyButtonsWhatsAppParameter = emptyList(),
            )
        }
    }

    override fun notifySubscriptionOverdueWarningDetailedNotificationsShutdown(
        accountId: AccountId,
        effectiveDueDate: LocalDate,
    ) {
        notificationService.notifyAccount(accountId, NotificationType.SUBSCRIPTION_OVERDUE) { account ->
            WhatsappNotification(
                accountId = accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.subscriptionOverdueDay04),
                configurationKey = "subscription-overdue-day-04",
                parameters = buildList {
                    add(effectiveDueDate.format(DateTimeFormatter.ofPattern("dd/MM")))
                },
                quickReplyButtonsWhatsAppParameter = buildList {
                    add("""{"payload": "", "action": "SEND_SUBSCRIPTION_PIX_CODE"}""") // TODO: mover para NotificationUtility
                },
            )
        }
    }

    override fun notifySubscriptionOverdueNotificationChannelDowngradeWarning(
        accountId: AccountId,
    ) {
        notificationService.notifyAccount(accountId, NotificationType.SUBSCRIPTION_OVERDUE) { account ->
            when (account.subscriptionType) {
                SubscriptionType.PIX -> WhatsappNotification(
                    accountId = accountId,
                    receiver = MobilePhone(account.mobilePhone),
                    template = NotificationTemplate(templatesConfiguration.whatsappTemplates.pixSubscriptionOverdueNotificationChannelDowngradeWarning),
                    configurationKey = "pix-subscription-overdue-notification-channel-downgrade-warning",
                    quickReplyButtonsWhatsAppParameter = buildList {
                        add("""{"payload": "", "action": "SEND_SUBSCRIPTION_PIX_CODE"}""") // TODO: mover para NotificationUtility
                    },
                )

                SubscriptionType.IN_APP -> WhatsappNotification(
                    accountId = accountId,
                    receiver = MobilePhone(account.mobilePhone),
                    template = NotificationTemplate(templatesConfiguration.whatsappTemplates.inAppSubscriptionOverdueNotificationChannelDowngradeWarning),
                    configurationKey = "in-app-subscription-overdue-notification-channel-downgrade-warning",
                    buttonWhatsAppParameter = ButtonDeeplinkParameter("minha-assinatura"),
                )
            }
        }
    }

    override fun notifySubscriptionOverdueWarningBillNotificationsAndDDAShutdown(
        accountId: AccountId,
        accountClosureDate: LocalDate,
    ) {
        notificationService.notifyAccount(accountId, NotificationType.SUBSCRIPTION_OVERDUE) { account ->
            WhatsappNotification(
                accountId = accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.subscriptionOverdueDay32),
                configurationKey = "subscription-overdue-day-32",
                parameters = buildList {
                    if (getLocalDate() == accountClosureDate) {
                        add("o fim do dia de hoje")
                    } else {
                        add(accountClosureDate.format(DateTimeFormatter.ofPattern("dd/MM")))
                    }
                },
                quickReplyButtonsWhatsAppParameter = buildList {
                    add("""{"payload": "", "action": "SEND_SUBSCRIPTION_PIX_CODE"}""") // TODO: mover para NotificationUtility
                },
            )
        }
    }

    override fun notifySubscriptionOverdueWarningAccountClosure(
        accountId: AccountId,
        daysUntilClosure: Long,
        closeDate: LocalDate,
    ) {
        notificationService.notifyAccount(accountId, NotificationType.SUBSCRIPTION_OVERDUE) { account ->
            WhatsappNotification(
                accountId = accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.subscriptionOverdueWarningAccountClosure),
                configurationKey = "subscription-overdue-warning-account-closure",
                parameters = buildList {
                    add(daysUntilClosure.toString())
                    add(closeDate.format(DateTimeFormatter.ofPattern("dd/MM")))
                },
                quickReplyButtonsWhatsAppParameter = buildList {
                    add("""{"payload": "", "action": "SEND_SUBSCRIPTION_PIX_CODE"}""") // TODO: mover para NotificationUtility
                },
            )
        }
    }

    override fun notifySubscriptionOverdueCloseAccount(accountId: AccountId) {
        notificationService.notifyAccount(accountId, NotificationType.SUBSCRIPTION_OVERDUE) { account ->
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.subscriptionOverdueCloseAccount),
                configurationKey = "subscription-overdue-close-account",
            )
        }
    }

    override fun notifyLegacySubscriptionOverdue(
        accountId: AccountId,
        days: Long,
    ) {
        notificationService.notifyAccount(accountId, NotificationType.SUBSCRIPTION_OVERDUE) { account ->

            WhatsappNotification(
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.subscriptionOverdue),
                configurationKey = "subscription-overdue",
                parameters = buildList {
                    add(getLocalDate().minusDays(days).format(DateTimeFormatter.ofPattern("dd/MM")))
                },
            )
        }
    }

    override fun notifyUtilityAccountUpdatedStatus(utilityAccount: UtilityAccount) {
        notificationService.notifyAccount(utilityAccount.addedBy, NotificationType.UTILITY_ACCOUNT_UPDATED_STATUS) {
            val (template, configurationKey) =
                when (utilityAccount.status) {
                    UtilityAccountConnectionStatus.INVALID_CREDENTIALS,
                    UtilityAccountConnectionStatus.FINAL_ERROR,
                    -> {
                        if (utilityAccount.statusMessage != null) {
                            templatesConfiguration.whatsappTemplates.utilityAccountUpdateStatusWithDetails to "utility-account-update-status-with-details"
                        } else {
                            templatesConfiguration.whatsappTemplates.utilityAccountUpdateStatus to "utility-account-update-status"
                        }
                    }

                    UtilityAccountConnectionStatus.DISCONNECTED -> {
                        if (utilityAccount.connectionMethod == UtilityConnectionMethod.SCRAPING) {
                            templatesConfiguration.whatsappTemplates.disconnectedScrapingUtilityAccount to "disconnected-scraping-utility-account"
                        } else {
                            templatesConfiguration.whatsappTemplates.utilityAccountUpdateStatus to "utility-account-update-status"
                        }
                    }

                    UtilityAccountConnectionStatus.REQUIRES_RECONNECTION -> templatesConfiguration.whatsappTemplates.utilityAccountRequestReconnection to "utility-account-request-reconnection"
                    UtilityAccountConnectionStatus.PENDING,
                    UtilityAccountConnectionStatus.CONNECTION_ERROR,
                    UtilityAccountConnectionStatus.PENDING_DISCONNECTION,
                    UtilityAccountConnectionStatus.DISCONNECTION_ERROR,
                    UtilityAccountConnectionStatus.NOT_SUPPORTED,
                    UtilityAccountConnectionStatus.CONNECTED,
                    -> throw java.lang.IllegalArgumentException("Unsupported notification status for update status notification")
                }

            val shouldAddStatusViewName = utilityAccount.status !in listOf(UtilityAccountConnectionStatus.CONNECTED, UtilityAccountConnectionStatus.REQUIRES_RECONNECTION)
            val hasStatusMessage = utilityAccount.statusMessage != null

            WhatsappNotification(
                accountId = it.accountId,
                receiver = MobilePhone(it.mobilePhone),
                template = NotificationTemplate(template),
                configurationKey = configurationKey,
                parameters = buildList {
                    add(utilityAccount.utility.viewName)
                    if (shouldAddStatusViewName) {
                        add(utilityAccount.status.toViewName())
                    }
                    if (hasStatusMessage) {
                        add(utilityAccount.statusMessage!!)
                    }
                },
                buttonWhatsAppParameter = ButtonDeeplinkParameter("carteira/${utilityAccount.walletId.value}/contas-de-consumo"),
            )
        }
    }

    override fun notifyCustom(
        accountId: AccountId,
        template: String,
        configurationKey: String,
        parameters: List<String>,
        quickReplyButtonsWhatsAppParameter: List<String>,
        buttonWhatsAppParameter: String?,
        media: NotificationMedia?,
    ) {
        notificationService.notifyAccount(accountId, NotificationType.UTILITY_ACCOUNT_UPDATED_STATUS) {
            WhatsappNotification(
                accountId = it.accountId,
                receiver = MobilePhone(it.mobilePhone),
                template = NotificationTemplate(template),
                configurationKey = configurationKey,
                parameters = parameters,
                quickReplyButtonsWhatsAppParameter = quickReplyButtonsWhatsAppParameter,
                buttonWhatsAppParameter = buttonWhatsAppParameter?.let { ButtonRawParameter(buttonWhatsAppParameter) },
                media = media,
            )
        }
    }

    override fun notifyCustomEmail(
        accountId: AccountId,
        template: String,
        parameters: Map<String, String>,
    ) {
        notificationService.notifyAccount(accountId, NotificationType.UTILITY_ACCOUNT_UPDATED_STATUS) {
            EmailNotification(
                accountId = it.accountId,
                template = NotificationTemplate(template),
                receiver = it.emailAddress,
                parameters = parameters,
            )
        }
    }

    override fun notifyUtilityAccountConnected(utilityAccount: UtilityAccount, info: UtilityAccountUpdateInfo?) {
        notificationService.notifyAccount(utilityAccount.addedBy, NotificationType.UTILITY_ACCOUNT_UPDATED_STATUS) {
            val firstName = it.getDisplayName()

            when (info) {
                is UtilityAccountUpdateInfo.BillsFound -> {
                    val billsFound = info.amount
                    val averageBillValue = info.averageValue
                    val hasFoundBills = billsFound > 0 && averageBillValue > 0
                    val (template, configurationKey) = if (hasFoundBills) {
                        NotificationTemplate(templatesConfiguration.whatsappTemplates.connectedFlowUtilityAccountWithBills) to "connected-flow-utility-account-with-bills"
                    } else {
                        NotificationTemplate(templatesConfiguration.whatsappTemplates.connectedFlowUtilityAccount) to "connected-flow-utility-account"
                    }
                    WhatsappNotification(
                        receiver = MobilePhone(it.mobilePhone),
                        accountId = it.accountId,
                        template = template,
                        configurationKey = configurationKey,
                        parameters = buildList {
                            add(firstName)
                            add(utilityAccount.utility.viewName)
                            if (hasFoundBills) {
                                add(billsFound.toString())
                                add(formatBrazilCurrency(averageBillValue))
                            }
                        },
                        quickReplyButtonsWhatsAppParameter = buildList {
                            add("""{"action":"ADD_NEW_CONNECTION","payload":""}""")
                        },
                    )
                }

                else -> {
                    WhatsappNotification(
                        receiver = MobilePhone(it.mobilePhone),
                        accountId = it.accountId,
                        template = NotificationTemplate(templatesConfiguration.whatsappTemplates.connectedFlowUtilityAccount),
                        configurationKey = "connected-flow-utility-account",
                        parameters = buildList {
                            add(firstName)
                            add(utilityAccount.utility.viewName)
                        },
                        quickReplyButtonsWhatsAppParameter = buildList {
                            add("""{"action":"ADD_NEW_CONNECTION","payload":""}""")
                        },
                    )
                }
            }
        }
    }

    override fun notifyUtilityAccountRequestReconnection(utilityAccount: UtilityAccount) {
        notificationService.notifyAccount(utilityAccount.addedBy, NotificationType.UTILITY_ACCOUNT_UPDATED_STATUS) { account ->
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.utilityAccountRequestReconnection),
                configurationKey = "utility-account-request-reconnection",
                parameters = buildList {
                    add(utilityAccount.utility.viewName)
                },
                buttonWhatsAppParameter = ButtonDeeplinkParameter("carteira/${utilityAccount.walletId.value}/contas-de-consumo/reconectar-conta/${utilityAccount.id.value}"),
            )
        }
    }

    override fun notifyDisconnectLegacyUtilityAccount(utilityAccount: UtilityAccount) {
        notificationService.notifyAccount(utilityAccount.addedBy, NotificationType.UTILITY_ACCOUNT_DISCONNECT_LEGACY_ACCOUNT) { account ->
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.utilityAccountDisconnectLegacyAccount),
                configurationKey = "utility-account-disconnect-legacy-account",
                parameters = buildList {
                    add(utilityAccount.utility.viewName)
                    add(utilityAccount.utility.viewName)
                },
                buttonWhatsAppParameter = buildUtilityAccountButtonPath(utilityAccount.walletId, utilityAccount.utility),
            )
        }
    }

    override fun notifyInvoicesNotFoundUtilityAccount(utilityAccount: UtilityAccount) {
        notificationService.notifyAccount(utilityAccount.addedBy, NotificationType.UTILITY_ACCOUNT_INVOICES_NOT_FOUND) { account ->
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.utilityAccountInvoiceNotFound),
                configurationKey = "utility-account-invoice-not-found",
                parameters = buildList {
                    add(utilityAccount.utility.viewName)
                },
            )
        }
        logger.info(
            Markers.append("utilityAccount", utilityAccount),
            "notifyInvoicesNotFoundUtilityAccount",
        )
    }

    override fun notifyInvoicesScanErrorUtilityAccount(utilityAccount: UtilityAccount) {
        notificationService.notifyAccount(utilityAccount.addedBy, NotificationType.UTILITY_ACCOUNT_INVOICES_SCAN_ERROR) { account ->
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.utilityAccountInvoiceScanError),
                configurationKey = "utility-account-invoice-scan-error",
                parameters = buildList {
                    add(utilityAccount.utility.viewName)
                },
            )
        }
        logger.info(
            Markers.append("utilityAccount", utilityAccount),
            "notifyInvoicesScanErrorUtilityAccount",
        )
    }

    // / TODO: criar cenario de teste
    override fun notifyCreditCardEnabled(
        accountId: AccountId,
        quota: Long,
    ) {
        notificationService.notifyAccount(accountId, NotificationType.CREDIT_CARD_ENABLED) { account ->
            val amount = (fee * 100).toLong()
            val number = (amount) / 100.0
            val value = if (number == number.toInt().toDouble()) number.toInt().toString() else String.format("%.2f", number)

            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.creditCardEnabled),
                configurationKey = "credit-card-enabled",
                parameters = buildList {
                    add(NotificationFormatter.buildFormattedAmount(quota))
                    add("por mês")
                    add("$value%")
                },
            )
        }
    }

    override fun notifySubscriptionInsufficientBalance(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        amount: Long,
    ) {
        notificationService.notifyMembers(members, NotificationType.INSUFFICIENT_BALANCE_TODAY) { account ->
            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.subscriptionInsufficientBalance),
                configurationKey = "subscription-insufficient-balance",
                parameters = buildList {
                    add(NotificationFormatter.buildFormattedAmount(amount))
                    add(walletName)
                },
                quickReplyButtonsWhatsAppParameter = buildList {
                    add("${walletId.value}#${ForecastPeriod.TODAY}")
                    add("${walletId.value}#${ForecastPeriod.SEVEN_DAYS}")
                    add("${walletId.value}#${ForecastPeriod.FIFTEEN_DAYS}")
                },
            )
        }
    }

    override fun notifyPixNotReceivedFailure(
        accountId: AccountId,
        wallet: Wallet,
        amount: Long,
        senderName: String,
        senderDocument: String,
    ) {
        notificationService.notifyAccount(accountId, NotificationType.PIX_NOT_RECEIVED_FAILURE) { account ->
            WhatsappNotification(
                accountId = accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.pixNotReceivedFailure),
                configurationKey = "pix-not-received-failure",
                parameters = buildList {
                    add(wallet.name)
                    add(NotificationFormatter.buildFormattedAmount(amount))
                    add(wallet.name)
                    add(String.format("%s (%s)", senderName, maskDocumentSpecialAsterisk(senderDocument)))
                }.filterNotNull(),
                buttonWhatsAppParameter = buildWalletButtonPath(walletId = wallet.id),
            )
        }
    }

    override fun notifyBasicSignUpReopened(accountId: AccountId, mobilePhone: MobilePhone) {
        val partialAccount = Account(
            // FIXME ACCOUNT para notificação somente. Esse usuário na verdade é um partialAccount
            accountId = accountId,
            name = "",
            emailAddress = EmailAddress(email = ""),
            document = "",
            documentType = "",
            mobilePhone = mobilePhone.msisdn,
            created = getZonedDateTime(),
            activated = null,
            status = AccountStatus.ACTIVE,
            configuration = DEFAULT_LEGACY_ACCOUNT_CONFIGURATION,
            firstLoginAsOwner = null,
            type = UserAccountType.BASIC_ACCOUNT,
            channel = null,
            imageUrlLarge = null,
            imageUrlSmall = null,
            subscriptionType = SubscriptionType.PIX,
            updated = getZonedDateTime(),
        )

        notificationService.notifyAccount(partialAccount, NotificationType.BASIC_SIGN_UP_UPDATE_DATA_NEEDED) {
            WhatsappNotification(
                accountId = accountId,
                receiver = mobilePhone,
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.signUpBasicUpdateDataNeeded),
                configurationKey = "sign-up-basic-update-data-needed",
                buttonWhatsAppParameter = ButtonDeeplinkParameter("entrar"),
            )
        }
    }

    override fun notifyPaymentIntentFailed(accountId: AccountId, paymentIntentId: PaymentIntentId) {
        notificationService.notifyAccount(accountId, NotificationType.PAYMENT_INTENT_FAILED) { account ->
            WhatsappNotification(
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.itpTransactionFailed),
                configurationKey = "itp-transaction-failed",
                buttonWhatsAppParameter = ButtonWebParameter("adicionar-saldo/${paymentIntentId.value}"), // TODO: mover para NotificationUtility
            )
        }
    }

    override fun notifyToken(accountId: AccountId, mobilePhone: MobilePhone, token: String) {
        notificationService.sendNotification(
            account = null, // FIXME - usar o AccountConfiguration novo. Será necessario migrar esse dado do AccountConfigurationLegacy
            type = NotificationType.TOKEN,
            billPaymentNotification = WhatsappNotification(
                accountId = accountId,
                receiver = mobilePhone,
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.registerToken),
                configurationKey = "register-token",
                parameters = buildList {
                    add(token)
                },
                buttonWhatsAppParameter = ButtonRawParameter(token),
            ),
        )
    }

    override fun notifyForgotPasswordToken(
        accountId: AccountId,
        document: String,
        emailAddress: EmailAddress,
        token: String,
        duration: Duration,
    ) {
        try {
            val account = accountRepository.findById(accountId)

            val emailContent = TemplateHelper.applyTemplate(
                templatesConfiguration.email.local.emailPasswordRecoveryTokenPath,
                mapOf(
                    "id" to ByteWrapper(document.toByteArray()).getBase64(),
                    "token" to token,
                    "name" to account.name,
                    "duration" to duration.toMinutes(),
                ),
            )
            billPaymentEmailSenderService.sendEmailAsync(
                EmailToBeSentWithRawBody(
                    senderAddress = EmailAddress(emailSenderConfiguration.notificationEmail),
                    displayName = emailSenderConfiguration.notificationDisplayName,
                    subject = emailPasswordRecoveryTokenSubject,
                    body = emailContent,
                    recipient = emailAddress,
                ),
            )
        } catch (e: Exception) {
            logger.error("notifyForgotPasswordToken", e)
        }
    }

    override fun notifyEmailVerificationToken(
        accountId: AccountId,
        emailAddress: EmailAddress,
        token: String,
        duration: Duration,
    ) {
        try {
            val emailContent =
                TemplateHelper.applyTemplate(
                    templatesConfiguration.email.local.emailVerificationTokenPath,
                    mapOf(
                        "token" to token,
                        "duration" to duration.toMinutes(),
                    ),
                )
            billPaymentEmailSenderService.sendEmailAsync(
                EmailToBeSentWithRawBody(
                    senderAddress = EmailAddress(emailSenderConfiguration.notificationEmail),
                    displayName = emailSenderConfiguration.notificationDisplayName,
                    subject = emailVerificationTokenSubject,
                    body = emailContent,
                    recipient = emailAddress,

                ),
            )
        } catch (e: Exception) {
            logger.error("notifyEmailVerificationToken", e)
        }
    }

    internal fun buildAddFailureNotification(
        account: Account,
        source: ActionSource,
        from: EmailAddress,
        subject: String,
        walletName: String,
        billId: BillId?,
        walletId: WalletId,
        result: CreateBillResult,
    ): WhatsappNotification {
        return when (result) {
            is CreateBillResult.FAILURE.BillAlreadyExists -> with(result.bill) {
                WhatsappNotification(
                    accountId = account.accountId,
                    receiver = MobilePhone(account.mobilePhone),
                    template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletPostalBoxAddDuplicate),
                    configurationKey = "wallet-postal-box-add-duplicate",
                    parameters = buildList {
                        add(getPayee())
                        add(NotificationFormatter.buildFormattedDueDate(result.bill.dueDate))
                        add(NotificationFormatter.buildFormattedAmount(amountTotal))
                        add(source.name())
                        add(from.value)
                        add(walletName)
                    },
                    buttonWhatsAppParameter = buildWalletBillButtonPath(walletId, billId!!),
                )
            }

            is CreateBillResult.FAILURE.BillNotPayable -> {
                if (result.billRegisterData == null) {
                    if (result.code == AddBillError.PAYMENT_NOT_AUTHORIZED.code) {
                        WhatsappNotification(
                            accountId = account.accountId,
                            receiver = MobilePhone(account.mobilePhone),
                            template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletPostalBoxAddNotAuthorized),
                            configurationKey = "wallet-postal-box-add-not-authorized",
                            parameters = buildList {
                                add(from.value)
                                add(walletName)
                                add(NotificationFormatter.buildFormattedDateTime(getZonedDateTime()))
                                add(NotificationFormatter.sanitize(subject))
                                add(source.name())
                                add(from.value)
                                add(walletName)
                            },
                        )
                    } else {
                        WhatsappNotification(
                            accountId = account.accountId,
                            receiver = MobilePhone(account.mobilePhone),
                            template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletPostalBoxAddNonPayableNoData),
                            configurationKey = "wallet-postal-box-add-non-payable-no-data",
                            parameters = buildList {
                                add(from.value)
                                add(walletName)
                                add(NotificationFormatter.buildFormattedDateTime(getZonedDateTime()))
                                add(NotificationFormatter.sanitize(subject))
                                add(source.name())
                                add(from.value)
                                add(walletName)
                            },
                        )
                    }
                } else {
                    WhatsappNotification(
                        accountId = account.accountId,
                        receiver = MobilePhone(account.mobilePhone),
                        template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletPostalBoxAddNonPayable),
                        configurationKey = "wallet-postal-box-add-non-payable",
                        parameters = buildList {
                            add(result.billRegisterData.recipient?.name ?: result.billRegisterData.assignor)
                            add(NotificationFormatter.buildFormattedDueDate(result.billRegisterData.dueDate!!))
                            add(NotificationFormatter.buildFormattedAmount(result.billRegisterData.amountTotal))
                            add(source.name())
                            add(from.value)
                            add(walletName)
                        },
                    )
                }
            }

            is CreateBillResult.FAILURE.AlreadyPaid.WithData -> {
                WhatsappNotification(
                    accountId = account.accountId,
                    receiver = MobilePhone(account.mobilePhone),
                    template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletPostalBoxAddPaidExternally),
                    configurationKey = "wallet-postal-box-add-paid-externally",
                    parameters = buildList {
                        add(result.billRegisterData.recipient?.name ?: result.billRegisterData.assignor)
                        add(NotificationFormatter.buildFormattedDueDate(result.billRegisterData.dueDate!!))
                        add(NotificationFormatter.buildFormattedAmount(result.billRegisterData.amountTotal))
                        add(source.name())
                        add(from.value)
                        add(walletName)
                    },
                )
            }

            is CreateBillResult.FAILURE.AlreadyPaid.WithoutData -> {
                WhatsappNotification(
                    accountId = account.accountId,
                    receiver = MobilePhone(account.mobilePhone),
                    template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletPostalBoxAddPaidExternallyWithoutData),
                    configurationKey = "wallet-postal-box-add-paid-externally-without-data",
                    parameters = buildList {
                        add(NotificationFormatter.buildFormattedDueDate(result.dueDate))
                        add(result.barCode.formattedDigitable())
                        add(source.name())
                        add(from.value)
                        add(walletName)
                    },
                )
            }

            else -> WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletPostalBoxAddValidationFailure),
                configurationKey = "wallet-postal-box-add-validation-failure",
                parameters = buildList {
                    add(from.value)
                    add(walletName)
                    add(NotificationFormatter.buildFormattedDateTime(getZonedDateTime()))
                    add(NotificationFormatter.sanitize(subject))
                },
            )
        }
    }

    internal fun buildInviteNotification(invite: Invite, account: Account?, email: EmailAddress): BillPaymentNotification {
        val founderFirstName = invite.founderName.split(" ")[0]
        val memberFirstName = invite.memberName.split(" ")[0]

        val notification = when (invite.memberType) {
            MemberType.ASSISTANT, MemberType.COLLABORATOR, MemberType.COFOUNDER -> {
                if (account != null) {
                    WhatsappNotification(
                        accountId = account.accountId,
                        receiver = MobilePhone(account.mobilePhone),
                        template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletInviteAssistantWithAccount),
                        configurationKey = "wallet-invite-assistant-with-account",
                        parameters = buildList {
                            add(memberFirstName)
                            add(founderFirstName)
                        },
                        buttonWhatsAppParameter = buildWalletInviteMemberWithAccountButtonPath(invite.walletId),
                    )
                } else {
                    val template = if (invite.memberType == MemberType.ASSISTANT) templatesConfiguration.email.ses.walletInviteAssistantWithoutAccount else templatesConfiguration.email.ses.walletInviteCollaboratorWithoutAccount
                    EmailNotification(
                        receiver = email,
                        template = NotificationTemplate(template),
                        parameters = mapOf(
                            "founderName" to founderFirstName,
                            "memberName" to memberFirstName,
                        ),
                    )
                }
            }

            else -> throw IllegalArgumentException("Illegal source: ${invite.memberType}")
        }

        return notification
    }

    private fun buildInviteReminderNotification(invite: Invite, account: Account?): BillPaymentNotification? {
        val founderFirstName = invite.founderName.split(" ")[0]
        val memberFirstName = invite.memberName.split(" ")[0]

        return when (invite.memberType) {
            MemberType.ASSISTANT, MemberType.COLLABORATOR, MemberType.COFOUNDER -> {
                if (account != null) {
                    WhatsappNotification(
                        accountId = account.accountId,
                        receiver = MobilePhone(account.mobilePhone),
                        buttonWhatsAppParameter = buildWalletInviteMemberWithAccountButtonPath(invite.walletId),
                        template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletInviteReminderAssistantWithAccount),
                        configurationKey = "wallet-invite-reminder-assistant-with-account",
                        parameters = buildList {
                            add(memberFirstName)
                            add(founderFirstName)
                        },
                    )
                } else if (invite.emailAddress !== null) {
                    val template = if (invite.memberType == MemberType.ASSISTANT) templatesConfiguration.email.ses.walletInviteReminderAssistantWithoutAccount else templatesConfiguration.email.ses.walletInviteReminderCollaboratorWithoutAccount
                    EmailNotification(
                        receiver = invite.emailAddress,
                        template = NotificationTemplate(template),
                        parameters = mapOf(
                            "founderName" to founderFirstName,
                            "memberName" to memberFirstName,
                        ),
                    )
                } else {
                    return null
                }
            }

            else -> throw IllegalArgumentException("Illegal source: ${invite.memberType}")
        }
    }

    /*private fun buildPushNotification(
        pushNotificationMessage: PushNotificationMessage,
        notification: Notification,
    ): PushNotificationMessage {
        var notificationMessageString = pushNotificationMessage.message
        notification.whatsAppParameters.forEachIndexed { index, parameter ->
            notificationMessageString = notificationMessageString.replace("{{${index + 1}}}", parameter)
        }

        return PushNotificationMessage(
            name = pushNotificationMessage.name,
            message = notificationMessageString,
            title = pushNotificationMessage.title,
            url = "https://use.friday.ai/${notification.buttonWhatsAppParameter}",
            template = pushNotificationMessage.template,
        )
    }*/

    override fun notifyAccountStatement(
        periodMessage: String,
        emailAddress: EmailAddress,
        name: String,
        files: List<ByteArrayWithNameAndType>,
    ) {
        try {
            billPaymentEmailSenderService.sendEmailAsync(
                EmailToBeSentWithTemplateBody(
                    senderAddress = EmailAddress(emailSenderConfiguration.notificationEmail),
                    displayName = emailSenderConfiguration.notificationDisplayName,
                    subject = "Seu extrato de $periodMessage chegou!",
                    recipient = emailAddress,
                    byteArrayWithNameAndTypes = files,
                    templateName = templatesConfiguration.email.local.accountStatementReportPath,
                    templateParams = mapOf(
                        "name" to name,
                        "periodMessage" to periodMessage,
                    ),
                ),
            )
        } catch (e: Exception) {
            logger.error("notifyReceiptEmail", e)
            throw e
        }
    }

    override fun notifyWalletSummary(
        periodMessage: String,
        emailAddress: EmailAddress,
        name: String,
        files: List<ByteArrayWithNameAndType>,
    ) {
        try {
            billPaymentEmailSenderService.sendEmailAsync(
                EmailToBeSentWithTemplateBody(
                    templateName = templatesConfiguration.email.local.walletSummaryReportPath,
                    templateParams = mapOf(
                        "name" to name,
                        "periodMessage" to periodMessage,
                    ),
                    senderAddress = EmailAddress(emailSenderConfiguration.notificationEmail),
                    displayName = emailSenderConfiguration.notificationDisplayName,
                    subject = "Seu relatório de $periodMessage chegou!",
                    byteArrayWithNameAndTypes = files,
                    recipient = emailAddress,
                ),
            )
        } catch (e: Exception) {
            logger.error("notifyWalletSummaryEmail", e)
            throw e
        }
    }

    private fun String.resolveTemplateVariation(bills: Int): String {
        return when (bills) {
            1 -> this.replace("{{i}}", "singular")
            in 2..9 -> this.replace("{{i}}", "${bills}_bills")
            else -> this.replace("{{i}}", "max")
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(CommCentreAdapter::class.java)
    }
}