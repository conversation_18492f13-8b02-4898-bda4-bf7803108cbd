package ai.friday.billpayment.adapters.settlement

import ai.friday.billpayment.adapters.celcoin.CelcoinAdapter
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.CreateBoletoRequest
import ai.friday.billpayment.app.feature.RequiresCelcoin
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.payment.BillPaymentResponse
import ai.friday.billpayment.app.payment.BillValidationResponse
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import arrow.core.Either
import io.micronaut.context.annotation.Primary
import jakarta.inject.Singleton

@Singleton
@RequiresCelcoin
@Primary
class FridaySettlementAdapter(
    private val settlementAdapter: SettlementAdapter,
    private val celcoinAdapter: CelcoinAdapter,
    private val featureConfiguration: FeatureConfiguration,
) : BoletoSettlementService {
    override fun validateBill(bill: BillView) = celcoinAdapter.validateBill(bill)

    override fun validateBill(boleto: CreateBoletoRequest) = celcoinAdapter.validateBill(boleto)

    override fun validateBill(bill: Bill) = celcoinAdapter.validateBill(bill)

    override fun settlementValidation(bill: Bill): BillValidationResponse {
        return if (featureConfiguration.asyncSettlement) {
            settlementAdapter.settlementValidation(bill)
        } else {
            celcoinAdapter.settlementValidation(bill)
        }
    }

    override fun initPayment(
        bill: Bill,
        nsu: Int,
        transactionId: String,
        payerDocument: String,
        payerName: String,
    ): BillPaymentResponse {
        return celcoinAdapter.initPayment(bill, nsu, transactionId, payerDocument, payerName)
    }

    override fun confirmPayment(externalTerminal: String, externalNsu: Int, transactionId: String) {
        celcoinAdapter.confirmPayment(externalTerminal, externalNsu, transactionId)
    }

    override fun cancelPayment(externalTerminal: String, externalNsu: Int, transactionId: String) {
        celcoinAdapter.cancelPayment(externalTerminal, externalNsu, transactionId)
    }

    override fun getBankByInstitutionCode(institutionCode: Int): String {
        return celcoinAdapter.getBankByInstitutionCode(institutionCode)
    }

    override fun queryPayment(transactionId: String): BoletoSettlementStatus {
        return celcoinAdapter.queryPayment(transactionId)
    }

    override fun getBalanceAmount(): Either<Exception, Long> {
        return celcoinAdapter.getBalanceAmount()
    }
}