package ai.friday.billpayment.adapters.settlement

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.CreateBoletoRequest
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.integrations.SettlementPaymentService
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.AmountCalculationModel.Companion.getByCode
import ai.friday.billpayment.app.payment.BillPaymentResponse
import ai.friday.billpayment.app.payment.BillRegisterData
import ai.friday.billpayment.app.payment.BillValidationResponse
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.app.payment.FichaCompensacaoType.Companion.findByCode
import ai.friday.billpayment.app.payment.RecipientChain
import ai.friday.billpayment.app.payment.checkout.RequestProtocol
import ai.friday.billpayment.app.payment.checkout.SettlementClientRequestTO
import ai.friday.billpayment.app.payment.getRecipientByPayer
import ai.friday.billpayment.markers
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.log.andAppend
import arrow.core.Either
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Secondary
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.HttpClientConfiguration
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.ApplicationConfiguration
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.net.URL
import java.time.Duration
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

private const val AlreadyPaid = 40601
private const val NotPayable = 40602
private const val BarCodeNotFound = 40400
private const val PaymentNotAuthorized = 40603
private const val CalculationGroupNotFound = 40600

@FridayMePoupe
class SettlementHttpConfiguration(
    applicationConfiguration: ApplicationConfiguration,
    val configuration: HttpClientConfiguration,
) : HttpClientConfiguration(applicationConfiguration) {
    init {
        setReadTimeout(Duration.ofMinutes(1))
    }

    override fun getConnectionPoolConfiguration(): ConnectionPoolConfiguration =
        configuration.connectionPoolConfiguration
}

@FridayMePoupe
@Secondary
@Singleton
class SettlementAdapter(
    httpConfiguration: SettlementHttpConfiguration,
    private val configuration: SettlementAdapterConfiguration,
) : BoletoSettlementService, SettlementPaymentService {

    private val logger = LoggerFactory.getLogger(SettlementAdapter::class.java)

    private val httpClient = RxHttpClient.create(URL(configuration.host), httpConfiguration)

    override fun request(request: SettlementClientRequestTO): Result<Boolean> {
        val markers = Markers.append("requisitionRequest", request)

        try {
            val httpRequest: HttpRequest<SettlementClientRequestTO> =
                HttpRequest.POST(
                    "/settlement/request",
                    request,
                )
                    .basicAuth(configuration.username, configuration.password)
                    .contentType(MediaType.APPLICATION_JSON_TYPE)
                    .accept(MediaType.APPLICATION_JSON_TYPE)

            val response = httpClient.exchange(
                httpRequest,
                Argument.of(String::class.java),
            ).firstOrError().blockingGet()

            logger.info(markers, "SettlementAdapter#request")
            return Result.success(response.status == HttpStatus.CREATED)
        } catch (e: HttpClientResponseException) {
            logger.error(e.markers().and(markers), "SettlementAdapter#request")
            return Result.failure(e)
        } catch (e: Exception) {
            logger.error(markers, "SettlementAdapter#request", e)
            return Result.failure(e)
        }
    }

    // TODO talvez seja uma boa criar uma interface de validação
    override fun validateBill(bill: BillView): BillValidationResponse {
        return validate(bill.barCode!!)
    }

    override fun validateBill(boleto: CreateBoletoRequest): BillValidationResponse {
        return validate(boleto.barcode)
    }

    override fun validateBill(bill: Bill): BillValidationResponse {
        return validate(bill.barcode!!)
    }

    override fun settlementValidation(bill: Bill): BillValidationResponse {
        return validate(bill.barcode!!)
    }

    internal fun validate(barcode: BarCode): BillValidationResponse {
        val markers = Markers.append("barcode", barcode)

        return try {
            val httpRequest: HttpRequest<ValidationRequestTO> =
                HttpRequest.PUT(
                    configuration.validatePath,
                    ValidationRequestTO(barcode.digitable, getLocalDate().format(DateTimeFormatter.ISO_DATE)),
                )
                    .basicAuth(configuration.username, configuration.password)
                    .contentType(MediaType.APPLICATION_JSON_TYPE)
                    .accept(MediaType.APPLICATION_JSON_TYPE)
            val call = httpClient.exchange(
                httpRequest,
                Argument.of(PayableValidationResponseTO::class.java),
                Argument.of(ErrorValidationResponseTO::class.java),
            )
            val response = call.firstOrError().blockingGet()

            if (response.status == HttpStatus.ACCEPTED) {
                throw IllegalStateException("Consulta não foi finalizada ainda")
            }

            val body = response.body() ?: throw IllegalStateException("Resposta inválida")

            logger.info(markers, "SettlementAdapter#validate")
            SettlementBillValidationResponse(
                billRegisterData = body.registerData.toBillRegisterData(
                    barcode = barcode,
                ),
                valid = true,
                paymentNotAuthorized = false,
                notPayable = false,
                alreadyPaid = false,
                barcodeNotFound = false,
                retryable = true,
            )
        } catch (e: HttpClientResponseException) {
            val body = e.response.getBody(ErrorValidationResponseTO::class.java)

            if (body.isPresent) {
                val validationResponse = body.get()

                logger.info(
                    markers.andAppend("validationResponseCode", validationResponse.code),
                    "SettlementAdapter#validate",
                )
                SettlementBillValidationResponse(
                    billRegisterData = validationResponse.registerData?.toBillRegisterData(
                        barcode = barcode,
                    ),
                    valid = false,
                    paymentNotAuthorized = validationResponse.code == PaymentNotAuthorized,
                    notPayable = validationResponse.code == NotPayable,
                    alreadyPaid = validationResponse.code == AlreadyPaid,
                    barcodeNotFound = validationResponse.code == BarCodeNotFound,
                    retryable = validationResponse.code !in listOf(NotPayable, AlreadyPaid, BarCodeNotFound, PaymentNotAuthorized),
                )
            } else {
                logger.warn(markers, "SettlementAdapter#validate", e)
                SettlementBillValidationResponse(
                    billRegisterData = null,
                    valid = false,
                    paymentNotAuthorized = false,
                    notPayable = false,
                    alreadyPaid = false,
                    barcodeNotFound = false,
                    retryable = true,
                )
            }
        } catch (e: Exception) {
            logger.error(markers, "SettlementAdapter#validate", e)

            SettlementBillValidationResponse(
                billRegisterData = null,
                valid = false,
                paymentNotAuthorized = false,
                notPayable = false,
                alreadyPaid = false,
                barcodeNotFound = false,
                retryable = true,
            )
        }
    }

    override fun initPayment(bill: Bill, nsu: Int, transactionId: String, payerDocument: String, payerName: String): BillPaymentResponse {
        TODO("Not yet implemented")
    }

    override fun confirmPayment(externalTerminal: String, externalNsu: Int, transactionId: String) {
        TODO("Not yet implemented")
    }

    override fun cancelPayment(externalTerminal: String, externalNsu: Int, transactionId: String) {
        TODO("Not yet implemented")
    }

    override fun getBankByInstitutionCode(institutionCode: Int): String {
        TODO("Not yet implemented")
    }

    override fun queryPayment(transactionId: String): BoletoSettlementStatus {
        TODO("Not yet implemented")
    }

    override fun getBalanceAmount(): Either<Exception, Long> {
        TODO("Not yet implemented")
        // FIXME esse cara não precisa estar nesta interface
    }
}

fun RegisterDataTO.toBillRegisterData(barcode: BarCode): BillRegisterData {
    return when (this) {
        is ConcessionariaRegisterDataTO -> BillRegisterData(
            billType = BillType.CONCESSIONARIA,
            assignor = payeeName,
            amount = calculatedAmount.original,
            discount = calculatedAmount.discount,
            interest = calculatedAmount.interest,
            fine = calculatedAmount.fine,
            amountTotal = calculatedAmount.total,
            dueDate = this.dueDate?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) },
            paymentLimitTime = paymentLimitHour,
            barCode = barcode,
            amountCalculationModel = AmountCalculationModel.UNKNOWN,
            payerDocument = null,
            settleDate = LocalDate.parse(calculatedAmount.date, DateTimeFormatter.ISO_DATE),
            divergentPayment = null,
            partialPayment = null,
        )

        is FichaRegisterDataTO -> {
            val chain = this.getRecipientChain()

            BillRegisterData(
                billType = BillType.FICHA_COMPENSACAO,
                assignor = issuer.name,
                recipient = chain.getRecipientByPayer(payer.document.number, issuer.compe.orEmpty()),
                recipientChain = chain,
                payerDocument = payer.document.number,
                amount = calculatedAmount.original,
                discount = calculatedAmount.discount,
                interest = calculatedAmount.interest,
                fine = calculatedAmount.fine,
                amountTotal = calculatedAmount.total,
                expirationDate = LocalDate.parse(paymentLimitDate, DateTimeFormatter.ISO_DATE),
                dueDate = LocalDate.parse(this.dueDate, DateTimeFormatter.ISO_DATE),
                paymentLimitTime = paymentLimitHour,
                settleDate = LocalDate.parse(calculatedAmount.date, DateTimeFormatter.ISO_DATE),
                fichaCompensacaoType = findByCode(categoryCode.toInt()),
                payerName = payer.commercialName ?: payer.name,
                amountCalculationModel = getByCode(calculationCode),
                idNumber = idNumber,
                registrationUpdateNumber = registrationUpdateNumber,
                barCode = barcode,
                abatement = calculatedAmount.abatement / 100.0,
                rebate = calculatedAmount.abatement,
                amountPaid = amountPaid,
                divergentPayment = null,
                partialPayment = null,
            )
        }
    }
}

@ConfigurationProperties("integrations.settlement")
interface SettlementAdapterConfiguration {
    val host: String
    val username: String
    val password: String
    val validatePath: String
    val requestProtocol: RequestProtocol
}

data class ValidationRequestTO(
    val barcode: String,
    val date: String,
)

data class ErrorValidationResponseTO(
    val type: String?,
    val barcode: String?,
    val financialServiceGateway: String,
    val code: Int,
    val errorMessage: String,

    @JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXTERNAL_PROPERTY,
        property = "type",
    )
    val registerData: RegisterDataTO? = null,
)

sealed interface RegisterDataTO {
    val paymentLimitHour: String
    val editableAmount: Boolean
    val calculatedAmount: CalculatedAmountTO
    val dueDate: String?
}

data class CalculatedAmountTO(
    val original: Long,
    val abatement: Long,
    val discount: Long,
    val interest: Long,
    val fine: Long,
    val total: Long,
    val date: String,
)

data class PayerTO(
    val document: DocumentTO,
    val name: String?,
    val commercialName: String?,
)

data class BeneficiaryTO(
    val document: DocumentTO?,
    val name: String?,
    val commercialName: String?,
)

@JsonTypeName("CONCESSIONARIA")
internal data class ConcessionariaRegisterDataTO(
    override val dueDate: String?,
    override val paymentLimitHour: String,
    override val editableAmount: Boolean,
    override val calculatedAmount: CalculatedAmountTO,
    val segment: String,
    val payeeName: String,
) : RegisterDataTO

data class DocumentTO(
    val type: String,
    val number: String,
)

data class IssuerTO(
    val compe: String?,
    val ispb: String?,
    val name: String,
)

@JsonTypeName("FICHA_COMPENSACAO")
internal data class FichaRegisterDataTO(
    override val paymentLimitHour: String,
    override val editableAmount: Boolean,
    override val calculatedAmount: CalculatedAmountTO,
    override val dueDate: String,
    val paymentLimitDate: String,
    val payer: PayerTO,
    val overdue: Boolean,
    val payableOverdue: Boolean,
    val sacadorAvalista: BeneficiaryTO?,
    val beneficiary: BeneficiaryTO?,
    val finalBeneficiary: BeneficiaryTO?,
    val issuer: IssuerTO,
    val categoryCode: String,
    val calculationCode: String,
    val idNumber: String?,
    val registrationUpdateNumber: Long?,
    val amountPaid: Long?,
) : RegisterDataTO

data class PayableValidationResponseTO(
    val type: String,
    val barcode: String,
    val digitableLine: String,
    val financialServiceGateway: String,
    val timestamp: String,

    @JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXTERNAL_PROPERTY,
        property = "type",
    )
    val registerData: RegisterDataTO,
)

internal fun FichaRegisterDataTO.getRecipientChain(): RecipientChain {
    return RecipientChain(
        sacadorAvalista = sacadorAvalista?.toRecipient(),
        originalBeneficiary = beneficiary?.toRecipient(),
        finalBeneficiary = finalBeneficiary?.toRecipient(),
    )
}

private fun BeneficiaryTO.toRecipient() = Recipient(
    name = commercialName ?: name.orEmpty(),
    document = document?.number,
)

data class SettlementBillValidationResponse(
    override val billRegisterData: BillRegisterData?,
    private val valid: Boolean,
    private val paymentNotAuthorized: Boolean,
    private val notPayable: Boolean,
    private val alreadyPaid: Boolean,
    private val barcodeNotFound: Boolean,
    private val retryable: Boolean,
) : BillValidationResponse(
    gateway = FinancialServiceGateway.FRIDAY,
    billRegisterData = billRegisterData,
) {
    override fun isRegistrationResponseOlderThan(registrationUpdateNumber: Long): Boolean {
        return this.billRegisterData!!.registrationUpdateNumber != null && this.billRegisterData.registrationUpdateNumber!! < registrationUpdateNumber
    }

    override fun isValid() = valid && !isPaymentLimitExpired()

    override fun paymentNotAuthorized() = paymentNotAuthorized

    override fun notPayable() = notPayable || isPaymentLimitExpired()

    override fun alreadyPaid() = alreadyPaid

    override fun isBarcodeNotFound() = barcodeNotFound

    override fun isRetryable() = retryable
}