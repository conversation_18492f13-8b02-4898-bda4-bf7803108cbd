package ai.friday.billpayment.adapters.instrumentation

import ai.friday.billpayment.app.account.AdService
import ai.friday.billpayment.app.inappsubscription.instrumentation.InAppSubscriptionInstrumentationEvent
import ai.friday.billpayment.app.inappsubscription.instrumentation.InAppSubscriptionInstrumentationService
import ai.friday.billpayment.app.instrumentation.InstrumentationRepository
import ai.friday.billpayment.app.integrations.CrmRepository
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton

@Singleton
@Requires(beans = [InAppSubscriptionInstrumentationService::class])
class InAppSubscriptionInstrumentationRepository(
    private val adService: AdService,
    private val crmRepository: CrmRepository,
) : InstrumentationRepository<InAppSubscriptionInstrumentationEvent> {
    override fun publishEvent(event: InAppSubscriptionInstrumentationEvent) {
        when (event) {
            is InAppSubscriptionInstrumentationEvent.TrialStarted -> adService.publishTrialStarted(event.accountId)
            is InAppSubscriptionInstrumentationEvent.TrialConverted -> adService.publishTrialConverted(event.accountId)
            is InAppSubscriptionInstrumentationEvent.TrialExpired -> crmRepository.publishEvent(
                accountId = event.accountId,
                eventName = "trial_expirado",
                customParams = emptyMap(),
            )
        }
    }
}