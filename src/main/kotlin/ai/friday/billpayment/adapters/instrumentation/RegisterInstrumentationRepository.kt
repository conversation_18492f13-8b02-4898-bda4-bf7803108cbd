package ai.friday.billpayment.adapters.instrumentation

import ai.friday.billpayment.adapters.firebase.FirebaseAdapter
import ai.friday.billpayment.app.account.AccountClosureDetails
import ai.friday.billpayment.app.instrumentation.InstrumentationRepository
import ai.friday.billpayment.app.integrations.CrmRepository
import ai.friday.billpayment.app.register.instrumentation.DefaultRegisterInstrumentationService
import ai.friday.billpayment.app.register.instrumentation.RegisterInstrumentationEvent
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton

@Singleton
@Requires(beans = [DefaultRegisterInstrumentationService::class])
class RegisterInstrumentationRepository(
    private val firebaseAdapter: FirebaseAdapter,
    private val intercomAdapter: CrmRepository,
) : InstrumentationRepository<RegisterInstrumentationEvent> {
    override fun publishEvent(event: RegisterInstrumentationEvent) {
        when (event) {
            is RegisterInstrumentationEvent.AccountActivated -> {
                val customParams = mapOf(
                    "registrationType" to event.registrationType.name,
                    "billsCount" to event.billsCount,
                )

                intercomAdapter.publishEvent(
                    accountId = event.accountId,
                    eventName = "cadastro_ativado",
                    customParams = customParams,
                )

                intercomAdapter.publishEvent(
                    accountId = event.accountId,
                    eventName = when (event.billsCount) {
                        0 -> "cadastro_ativado_sem_contas"
                        1 -> "cadastro_ativado_com_uma_conta"
                        else -> "cadastro_ativado_com_varias_contas"
                    },
                    customParams = customParams,
                )

                firebaseAdapter.publishEvent(
                    accountId = event.accountId,
                    eventName = "sign-up.account-activated",
                    customParams = customParams,
                )
            }

            is RegisterInstrumentationEvent.Completed -> {
                val customParams = mapOf(
                    "registrationType" to event.registrationType.name,
                )
                intercomAdapter.publishEvent(
                    accountId = event.accountId,
                    eventName = "cadastro_completo",
                    customParams = customParams,
                )
            }

            is RegisterInstrumentationEvent.ExternallyRejected -> {
                val customParams = mapOf(
                    "registrationType" to event.registrationType.name,
                )
                intercomAdapter.publishEvent(
                    accountId = event.accountId,
                    eventName = "cadastro_rejeitado_externamente",
                    customParams = customParams,
                )
            }

            is RegisterInstrumentationEvent.InternallyRejected -> {
                val customParams = mapOf(
                    "registrationType" to event.registrationType.name,
                    "deniedReason" to event.deniedReason.joinToString(separator = ", ") { it.name },
                )
                intercomAdapter.publishEvent(
                    accountId = event.accountId,
                    eventName = "cadastro_rejeitado_internamente",
                    customParams = customParams,
                )
            }

            is RegisterInstrumentationEvent.Reopened -> {
                val customParams = mapOf(
                    "registrationType" to event.registrationType.name,
                    "deniedReason" to event.deniedReason.joinToString(separator = ", ") { it.name },
                )
                intercomAdapter.publishEvent(
                    accountId = event.accountId,
                    eventName = "cadastro_reaberto",
                    customParams = customParams,
                )
            }

            is RegisterInstrumentationEvent.Upgraded -> {
                val customParams = mapOf(
                    "registrationType" to event.registrationType.name,
                    "reason" to event.reason.joinToString(separator = ", ") { it.name },
                )
                intercomAdapter.publishEvent(
                    accountId = event.accountId,
                    eventName = "cadastro_upgraded",
                    customParams = customParams,
                )
            }

            is RegisterInstrumentationEvent.AccountClosed -> {
                val customParams = mutableMapOf(
                    "accountType" to event.accountType.name,
                )

                if (event.closureDetails is AccountClosureDetails.WithReason) {
                    customParams["reason"] = event.closureDetails.reason.name
                }

                intercomAdapter.publishEvent(
                    accountId = event.accountId,
                    eventName = "cadastro_closed",
                    customParams = customParams,
                )
            }

            is RegisterInstrumentationEvent.AccountUpgraded -> {
                intercomAdapter.publishEvent(
                    accountId = event.accountId,
                    eventName = "conta_upgraded",
                )
            }
        }
    }
}