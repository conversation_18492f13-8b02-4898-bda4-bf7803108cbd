package ai.friday.billpayment.adapters.instrumentation

import ai.friday.billpayment.adapters.firebase.FirebaseAdapter
import ai.friday.billpayment.app.cashIn.instrumentation.CashInInstrumentationEvent
import ai.friday.billpayment.app.cashIn.instrumentation.DefaultCashInInstrumentationService
import ai.friday.billpayment.app.instrumentation.InstrumentationRepository
import ai.friday.billpayment.app.integrations.CashInAmountTier
import ai.friday.billpayment.app.integrations.CrmRepository
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton

@Singleton
@Requires(beans = [DefaultCashInInstrumentationService::class])
class CashInInstrumentationRepository(
    private val firebaseAdapter: FirebaseAdapter,
    private val intercomAdapter: CrmRepository,
) : InstrumentationRepository<CashInInstrumentationEvent> {

    override fun publishEvent(event: CashInInstrumentationEvent) {
        when (event) {
            is CashInInstrumentationEvent.CreditCardCashInReceived -> {
                val customParams = mapOf(
                    "type" to "CREDIT_CARD",
                    "receiverAccountId" to event.receiverAccountId.value,
                    "tier" to event.tier.name,
                    "sameOwnership" to event.sameOwnership,
                    "succeeded" to event.succeeded,
                )

                intercomAdapter.publishEvent(
                    accountId = event.receiverAccountId,
                    eventName = "cash_in_cartao",
                    customParams = customParams,
                )

                if (event.succeeded) {
                    intercomAdapter.publishEvent(
                        accountId = event.receiverAccountId,
                        eventName = when (event.tier) {
                            CashInAmountTier.BELOW_FIVE_HUNDRED -> "cash_in_cartao_abaixo_de_quinhentos"
                            CashInAmountTier.ABOVE_FIVE_HUNDRED -> "cash_in_cartao_acima_de_quinhentos"
                        },
                        customParams = customParams,
                    )
                }

                firebaseAdapter.publishEvent(
                    accountId = event.receiverAccountId,
                    eventName = if (event.succeeded) "cash-in.succeeded" else "cash-in.failed",
                    customParams,
                )
            }

            is CashInInstrumentationEvent.DepositReceived -> {
                val customParams = mapOf(
                    "type" to event.type.name,
                    "receiverAccountId" to event.receiverAccountId.value,
                    "tier" to event.tier.name,
                    "sameOwnership" to event.sameOwnership.toString(),
                )

                intercomAdapter.publishEvent(
                    accountId = event.receiverAccountId,
                    eventName = "cash_in_deposito",
                    customParams = customParams,
                )

                intercomAdapter.publishEvent(
                    accountId = event.receiverAccountId,
                    eventName = when (event.tier) {
                        CashInAmountTier.BELOW_FIVE_HUNDRED -> "cash_in_deposito_abaixo_de_quinhentos"
                        CashInAmountTier.ABOVE_FIVE_HUNDRED -> "cash_in_deposito_acima_de_quinhentos"
                    },
                    customParams = customParams,
                )

                firebaseAdapter.publishEvent(
                    accountId = event.receiverAccountId,
                    eventName = "cash-in.succeeded",
                    customParams = customParams,
                )
            }
        }
    }
}