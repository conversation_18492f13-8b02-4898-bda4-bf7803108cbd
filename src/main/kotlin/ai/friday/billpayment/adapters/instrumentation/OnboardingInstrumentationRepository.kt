package ai.friday.billpayment.adapters.instrumentation

import ai.friday.billpayment.app.instrumentation.InstrumentationRepository
import ai.friday.billpayment.app.integrations.CrmRepository
import ai.friday.billpayment.app.onboarding.instrumentation.OnboardingInstrumentationEvent
import ai.friday.billpayment.app.onboarding.instrumentation.OnboardingInstrumentationService
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton

@Singleton
@Requires(beans = [OnboardingInstrumentationService::class])
class OnboardingInstrumentationRepository(
    private val intercomAdapter: CrmRepository,
) : InstrumentationRepository<OnboardingInstrumentationEvent> {
    override fun publishEvent(event: OnboardingInstrumentationEvent) {
        when (event) {
            is OnboardingInstrumentationEvent.SinglePixCreated -> intercomAdapter.publishEvent(
                accountId = event.accountId,
                eventName = "single_pix_criado",
                customParams = emptyMap(),
            )
            is OnboardingInstrumentationEvent.SinglePixPaid -> intercomAdapter.publishEvent(
                accountId = event.accountId,
                eventName = "single_pix_pago",
                customParams = emptyMap(),
            )
        }
    }
}