package ai.friday.billpayment.adapters.instrumentation

import ai.friday.billpayment.app.bill.instrumentation.BillInstrumentationEvent
import ai.friday.billpayment.app.bill.instrumentation.DefaultBillInstrumentationService
import ai.friday.billpayment.app.instrumentation.InstrumentationRepository
import ai.friday.billpayment.app.integrations.CrmRepository
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton

@Singleton
@Requires(beans = [DefaultBillInstrumentationService::class])
class BillInstrumentationRepository(
    private val intercomAdapter: CrmRepository,
) : InstrumentationRepository<BillInstrumentationEvent> {

    override fun publishEvent(event: BillInstrumentationEvent) {
        when (event) {
            is BillInstrumentationEvent.CreatedMailbox -> {
                val intercomEventName =
                    if (event.memberAccountId != null) "conta_criada_por_email_de_usuario" else "conta_criada_por_email_desconhecido"

                intercomAdapter.publishEvent(
                    accountId = event.memberAccountId ?: event.founderAccountId,
                    eventName = intercomEventName,
                    customParams = mapOf(
                        "memberAccountId" to event.memberAccountId?.value,
                        "founderAccountId" to event.founderAccountId.value,
                        "senderEmailDomain" to event.senderEmailDomain,
                    ),
                )
            }

            is BillInstrumentationEvent.CreatedRecurring -> {
                intercomAdapter.publishEvent(
                    accountId = event.memberAccountId,
                    eventName = "conta_criada_com_recorrencia",
                    customParams = mapOf(
                        "memberAccountId" to event.memberAccountId.value,
                        "founderAccountId" to event.founderAccountId.value,
                        "billType" to event.billType.name,
                        "frequency" to event.frequency.name,
                    ),
                )
            }

            is BillInstrumentationEvent.Scheduled -> {
                val customParams = mapOf(
                    "memberAccountId" to event.memberAccountId.value,
                    "founderAccountId" to event.founderAccountId.value,
                    "billsCount" to event.billsCount,
                )

                intercomAdapter.publishEvent(
                    accountId = event.memberAccountId,
                    eventName = "conta_agendada",
                    customParams = customParams,
                )

                if (event.billsCount > 1) {
                    intercomAdapter.publishEvent(
                        accountId = event.memberAccountId,
                        eventName = "conta_agendada_multipla",
                        customParams = customParams,
                    )
                }
            }

            is BillInstrumentationEvent.Paid -> {
                intercomAdapter.publishEvent(
                    accountId = event.founderAccountId,
                    eventName = "conta_paga",
                    customParams = mapOf(
                        "founderAccountId" to event.founderAccountId.value,
                        "billType" to event.billType.name,
                    ),
                )
            }
        }
    }
}