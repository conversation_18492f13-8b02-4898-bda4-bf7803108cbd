package ai.friday.billpayment.adapters.instrumentation

import ai.friday.billpayment.app.instrumentation.InstrumentationRepository
import ai.friday.billpayment.app.integrations.CrmRepository
import ai.friday.billpayment.app.wallet.instrumentation.WalletInstrumentationEvent
import ai.friday.billpayment.app.wallet.instrumentation.WalletInstrumentationService
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton

@Singleton
@Requires(beans = [WalletInstrumentationService::class])
class WalletInstrumentationRepository(
    private val intercomAdapter: CrmRepository,
) : InstrumentationRepository<WalletInstrumentationEvent> {

    override fun publishEvent(event: WalletInstrumentationEvent) {
        when (event) {
            is WalletInstrumentationEvent.MemberRemoved -> {
                intercomAdapter.publishEvent(
                    accountId = event.memberAccountId,
                    eventName = "removido_da_carteira",
                    customParams = mapOf(
                        "walletName" to event.walletName,
                        "removedBy" to event.removedByName,
                    ),
                )

                intercomAdapter.publishEvent(
                    accountId = event.removedByAccountId,
                    eventName = "membro_removido_da_carteira",
                    customParams = mapOf(
                        "walletName" to event.walletName,
                        "removedMember" to event.memberName,
                    ),
                )
            }
        }
    }
}