package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.BillSchedulePostponed
import ai.friday.billpayment.app.bill.SchedulePostponedReason
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.payment.PaymentSchedulingService
import ai.friday.billpayment.app.payment.RescheduleError
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonProperty
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSUpdateScheduledBillHandler(
    amazonSQS: SqsClient,
    amazonSNS: SnsClient,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = billEventTopicArnPropertyName) private val topicArn: String,
    private val billEventRepository: BillEventRepository,
    private val paymentSchedulingService: PaymentSchedulingService,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = configuration.updateScheduledBillQueueName,
    amazonSNS = amazonSNS,
    topicArn = topicArn,
    filter = EventTypeFilter(eventTypes = listOf(BillEventType.SCHEDULE_POSTPONED)),
) {
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val billEvent = parseEvent(message) as BillSchedulePostponed

        when (billEvent.reason) {
            SchedulePostponedReason.INSUFFICIENT_FUNDS -> return SQSHandlerResponse(shouldDeleteMessage = true)
            SchedulePostponedReason.LIMIT_REACHED, SchedulePostponedReason.MONTHLY_LIMIT_REACHED -> {}
        }

        val markers = append("walletId", billEvent.walletId.value).andAppend("billId", billEvent.billId.value)

        val bill =
            billEventRepository.getBill(billEvent.walletId, billEvent.billId)
                .getOrElse {
                    logger.error(
                        markers.andAppend("description", "bill not found"),
                        "UpdateScheduledBill",
                    )
                    return SQSHandlerResponse(shouldDeleteMessage = true)
                }

        paymentSchedulingService.reschedule(
            billId = billEvent.billId,
            date = bill.schedule!!.date,
        )
            .mapLeft {
                when (it) {
                    RescheduleError.ScheduleNotFound ->
                        logger.error(
                            markers.andAppend(
                                "description",
                                "scheduledBill not found",
                            ),
                            "UpdateScheduledBill",
                        )

                    is RescheduleError.Unknown -> {
                        logger.error(markers, "UpdateScheduledBill", it.exception)
                        return SQSHandlerResponse(shouldDeleteMessage = false)
                    }
                }
            }

        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    override fun handleError(
        message: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(append("queue_message", message), "UpdateScheduledBill", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSUpdateScheduledBillHandler::class.java)
    }
}

data class EventTypeFilter<T>(
    @JsonProperty("eventType")
    val eventTypes: List<T>,
)