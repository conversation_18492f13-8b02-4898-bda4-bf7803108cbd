package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.ExternalBillId
import ai.friday.billpayment.app.bill.ExternalBillProvider
import ai.friday.billpayment.app.vehicledebts.LicensePlate
import ai.friday.billpayment.app.vehicledebts.VehicleDebtEnrichmentMessage
import ai.friday.billpayment.app.vehicledebts.VehicleDebtEnrichmentService
import ai.friday.billpayment.app.vehicledebts.VehicleDebts
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.log.andAppend
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@VehicleDebts
open class SQSVehicleDebtsEnrichmentHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val service: VehicleDebtEnrichmentService,
) : AbstractSQSHandler(
    amazonSQS,
    configuration,
    configuration.vehicleDebtsEnrichmentQueueName,
    consumers = 1,
) {
    private val logger = LoggerFactory.getLogger(SQSVehicleDebtsEnrichmentHandler::class.java)

    @NewSpan
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val body = message.body()
        val markers = Markers.append("request", message.markers())

        val enrichmentMessage = parseObjectFrom<VehicleDebtEnrichmentMessage>(body)

        logger.info(
            markers.andAppend("enrichmentMessage", enrichmentMessage),
            "SQSVehicleDebtsEnrichmentHandler#handleMessage",
        )

        val result = service.enrichVehicleDebt(
            accountId = AccountId(enrichmentMessage.accountId),
            walletId = WalletId(enrichmentMessage.walletId),
            licensePlate = LicensePlate(enrichmentMessage.licensePlate),
            barcode = BarCode.of(enrichmentMessage.barcode),
            externalId = enrichmentMessage.externalId?.let {
                ExternalBillId(it, ExternalBillProvider.VEHICLE_DEBTS)
            },
            details = enrichmentMessage.description,
        )

        result.onFailure { error ->
            logger.error(
                markers.andAppend("error", error.message),
                "SQSVehicleDebtsEnrichmentHandler#handleMessage",
                error,
            )
            return SQSHandlerResponse(shouldDeleteMessage = false)
        }

        return SQSHandlerResponse(shouldDeleteMessage = true)
    }
}