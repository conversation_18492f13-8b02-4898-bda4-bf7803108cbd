package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.dda.DDAFullImportResult
import ai.friday.billpayment.app.dda.DDARegisterMessage
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.dda.DDAStatus
import ai.friday.billpayment.app.integrations.DDARepository
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requires(notEnv = ["staging"])
open class SQSFullImportDDAHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val ddaService: DDAService,
    val ddaRepository: DDARepository,
) : AbstractSQSHandler(
    amazonSQS,
    configuration,
    configuration.ddaFullImportQueueName,
    consumers = 2,
) {
    @NewSpan
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val message = parseObjectFrom<DDARegisterMessage>(m.body())
        val markers =
            append("accountId", message.accountId.value)
                .andAppend("document", message.document)

        val dda = ddaRepository.find(message.accountId)

        if (dda?.status != DDAStatus.REQUESTED) {
            LOG.error(
                markers.andAppend("reason", "IllegalState: DDAStatus should be REQUESTED but is ${dda?.status}"),
                "SQSFullImportDDAHandler",
            )
            return SQSHandlerResponse(shouldDeleteMessage = true)
        }

        val result = ddaService.executeFullImport(dda)
        markers.andAppend("result", result)
            .andAppend("endState", result.endState)

        when (result) {
            is DDAFullImportResult.NotImported, is DDAFullImportResult.Imported, is DDAFullImportResult.Skipped -> {
                LOG.info(markers, "SQSFullImportDDAHandler")
            }

            is DDAFullImportResult.Expired, is DDAFullImportResult.Delayed -> {
                LOG.warn(markers, "SQSFullImportDDAHandler")
            }

            is DDAFullImportResult.Error -> {
                LOG.error(markers, "SQSFullImportDDAHandler", result.throwable)
            }
        }

        return SQSHandlerResponse(shouldDeleteMessage = result.endState)
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        LOG.error(append("event", m), "SQSFullImportDDAHandler", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SQSFullImportDDAHandler::class.java)
    }
}