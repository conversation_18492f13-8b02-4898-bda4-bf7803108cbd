package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.originalSimpleName
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import io.micronaut.context.condition.Condition
import io.micronaut.context.condition.ConditionContext
import java.lang.annotation.Inherited
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Inherited
@Requirements(Requires(condition = HandlerCondition::class), Requires(notEnv = ["test"]))
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
annotation class Handler

class HandlerCondition : Condition {
    companion object {
        private val logger = LoggerFactory.getLogger(HandlerCondition::class.java)
    }

    override fun matches(context: ConditionContext<*>): Boolean {
        val className: String = context.component.javaClass.originalSimpleName()

        val configKey = "handlers.$className.enabled"
        val property = context.getProperty(config<PERSON><PERSON>, Boolean::class.java)

        logger.debug(<PERSON>ers.append("enabled", property).andAppend("bean", className), "HandlerContition")

        return property.orElse(true)
    }
}