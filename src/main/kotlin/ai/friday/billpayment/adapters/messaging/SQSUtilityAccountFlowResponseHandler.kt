package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.ExternalBillId
import ai.friday.billpayment.app.bill.ExternalBillProvider
import ai.friday.billpayment.app.utilityaccount.Invoice
import ai.friday.billpayment.app.utilityaccount.UtilityAccountAdditionalInfoTO
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionStatus
import ai.friday.billpayment.app.utilityaccount.UtilityAccountId
import ai.friday.billpayment.app.utilityaccount.UtilityAccountInvoiceStatus
import ai.friday.billpayment.app.utilityaccount.UtilityAccountService
import ai.friday.billpayment.app.utilityaccount.UtilityAccountUpdateInfo
import ai.friday.billpayment.app.utilityaccount.UtilityFlowInvoices
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import java.time.LocalDate
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requirements(Requires(notEnv = ["test"]), Requires(env = [FRIDAY_ENV, ME_POUPE_ENV]))
open class SQSUtilityAccountFlowResponseHandler(
    sqsClient: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val utilityAccountService: UtilityAccountService,
) :
    AbstractSQSHandler(
        amazonSQS = sqsClient,
        configuration = configuration,
        queueName = configuration.utilityFlowResponseQueueName,
        consumers = 5,
    ) {
    private val logger = LoggerFactory.getLogger(SQSUtilityAccountFlowResponseHandler::class.java)

    @Trace
    @NewSpan
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val logName = "SQSUtilityAccountFlowResponseHandler#handleMessage"
        val markers = Markers.append("queue_message", m)

        val flowResponse = parseObjectFrom<UtilityAccountFlowResponseTO>(m.body())
        val utilityAccountId = UtilityAccountId(flowResponse.connectionId)

        markers.andAppend("queueUtilityAccount", flowResponse)

        return try {
            val updatedUtilityAccount = utilityAccountService.updateStatus(
                utilityAccountId,
                flowResponse.status,
                flowResponse.statusMessage,
                flowResponse.attempts,
                buildUpdateInfo(flowResponse),
            ).getOrElse {
                logger.warn(markers, logName)
                return SQSHandlerResponse(true)
            }

            markers.andAppend("utilityAccount", updatedUtilityAccount)

            logger.info(markers, logName)

            if (flowResponse.success) {
                val addInvoicesResult = utilityAccountService.handleResponseInvoices(
                    updatedUtilityAccount,
                    UtilityFlowInvoices(
                        utilityAccountId = utilityAccountId,
                        invoices = flowResponse.invoices.map { it.toInvoice() },
                        lastDueDateFound = flowResponse.parseLastDueDateFound(),
                    ),
                )

                addInvoicesResult.fold(
                    ifRight = {
                        logger.info(
                            markers
                                .andAppend("billsCreated", it.billsCreated)
                                .andAppend("paidEntriesCreated", it.paidEntriesCreated),
                            "$logName/addInvoices",
                        )
                    },

                    ifLeft = {
                        logger.error(markers.andAppend("addInvoicesError", it), "$logName/addInvoices")
                    },
                )
            }

            SQSHandlerResponse(true)
        } catch (ex: Exception) {
            logger.error(markers, "SQSUtilityAccountFlowResponseHandler#handleMessage", ex)
            SQSHandlerResponse(false)
        }
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(Markers.append("event", m), "SQSUtilityAccountFlowResponseHandler#handleError", e)
        return SQSHandlerResponse(false)
    }
}

fun buildUpdateInfo(payload: UtilityAccountFlowResponseTO): UtilityAccountUpdateInfo? {
    if (payload.success) {
        val invoicesWithValue = payload.invoices.filter { it.amount != null }

        if (invoicesWithValue.isEmpty()) {
            return null
        }

        val totalAmount = invoicesWithValue.map { it.amount ?: 0 }.reduce { acc, amount -> acc + amount }

        return UtilityAccountUpdateInfo.BillsFound(amount = payload.invoices.size, averageValue = totalAmount / invoicesWithValue.size)
    }

    return null
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class UtilityAccountFlowResponseTO(
    val connectionId: String,
    val success: Boolean,
    val status: UtilityAccountConnectionStatus,
    val statusMessage: String?,
    val attempts: Int = 0,
    val invoices: List<InvoiceTO>,
    val additionalInfo: UtilityAccountAdditionalInfoTO,
) {
    fun parseLastDueDateFound(): LocalDate? {
        return this.additionalInfo.lastDueDateFound?.let { LocalDate.parse(it, dateFormat) }
    }
}

data class InvoiceTO(
    val barcode: String,
    val dueDate: String,
    val status: UtilityAccountInvoiceStatus = UtilityAccountInvoiceStatus.ACTIVE,
    val externalId: String? = null,
    val amount: Long? = null,
) {
    fun toInvoice(): Invoice {
        return when (status) {
            UtilityAccountInvoiceStatus.ACTIVE -> {
                val barcode = BarCode.detect(barcode)
                Invoice.Active(
                    dueDate = LocalDate.parse(dueDate, dateFormat),
                    barcode = barcode,
                    externalId = externalId?.let { externalId -> ExternalBillId(externalId, ExternalBillProvider.UTILITY_ACCOUNT) },
                )
            }

            UtilityAccountInvoiceStatus.PAID -> {
                Invoice.Paid(
                    dueDate = LocalDate.parse(dueDate, dateFormat),
                    amount = amount!!,
                    externalId = externalId?.let { externalId -> ExternalBillId(externalId, ExternalBillProvider.UTILITY_ACCOUNT) },
                )
            }
        }
    }
}