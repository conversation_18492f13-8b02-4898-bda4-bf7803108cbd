package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.app.bill.AmountUpdated
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.BillPaymentScheduleUpdated
import ai.friday.billpayment.app.bill.BillPaymentScheduled
import ai.friday.billpayment.app.bill.RegisterUpdated
import ai.friday.billpayment.app.bill.UpdatedRegisterData
import ai.friday.billpayment.app.integrations.PaymentSchedulingForecastService
import ai.friday.billpayment.app.payment.PaymentSchedulingEventService
import ai.friday.billpayment.app.payment.PaymentSchedulingService
import ai.friday.billpayment.logEvent
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSSchedulingHandler(
    amazonSQS: SqsClient,
    amazonSNS: SnsClient,
    @Property(name = billEventTopicArnPropertyName) private val topicArn: String,
    @Property(name = sqsArnPrefix) private val arnPrefix: String,
    configuration: SQSMessageHandlerConfiguration,
    private val paymentSchedulingService: PaymentSchedulingService,
    private val paymentSchedulingEventService: PaymentSchedulingEventService,
    private val schedulingForecastService: PaymentSchedulingForecastService,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    amazonSNS = amazonSNS,
    topicArn = topicArn,
    filter = EventTypeFilter(
        eventTypes =
        listOf(
            BillEventType.PAYMENT_SCHEDULED,
            BillEventType.PAYMENT_SCHEDULE_CANCELED,
            BillEventType.REGISTER_UPDATED,
            BillEventType.AMOUNT_UPDATED,
            BillEventType.PAYMENT_SCHEDULE_UPDATED,
        ),
    ),
    configuration = configuration.copy(
        visibilityTimeout = 300,
        sqsWaitTime = 20,
        dlqArn = arnPrefix + configuration.billPaymentSchedulingQueueName + dlqSuffix,
    ),
    queueName = configuration.billPaymentSchedulingQueueName,
) {
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val eventType =
            m.messageAttributes()[eventTypeAttributeName]?.let { BillEventType.valueOf(it.stringValue()) }
                ?: throw Exception()
        when (eventType) {
            BillEventType.PAYMENT_SCHEDULED -> processBillPaymentScheduled(m)
            BillEventType.PAYMENT_SCHEDULE_CANCELED -> processBillPaymentScheduleCanceled(m)
            BillEventType.REGISTER_UPDATED -> processBillRegisterUpdated(m)
            BillEventType.AMOUNT_UPDATED -> processBillAmountUpdated(m)
            BillEventType.PAYMENT_SCHEDULE_UPDATED -> processBillPaymentScheduleUpdated(m)
            else -> throw IllegalStateException()
        }

        return SQSHandlerResponse(true)
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.warn(append("queue_message", m.body()), "SQSSchedulingHandler", e)
        return SQSHandlerResponse(e is IllegalStateException)
    }

    private fun processBillPaymentScheduleCanceled(message: Message) {
        val event = parseEvent(message) as BillPaymentScheduleCanceled
        logger.info(append("event", event), "processBillPaymentScheduleCanceled")
        paymentSchedulingService.cancelSchedule(
            event.walletId,
            event.billId,
            event.batchSchedulingId,
        )
        schedulingForecastService.deprovision(event)
    }

    private fun processBillPaymentScheduled(message: Message) {
        val event = parseEvent(message) as BillPaymentScheduled
        logger.info(append("event", event), "processBillPaymentScheduled")
        schedulingForecastService.provision(event)
    }

    private fun processBillRegisterUpdated(message: Message) {
        val event = parseEvent(message) as RegisterUpdated

        if (event.updatedRegisterData is UpdatedRegisterData.NewTotalAmount) {
            logger.info(append("event", event), "processBillRegisterUpdated#NewTotalAmount")
            paymentSchedulingEventService.updateAmount(
                event.walletId,
                event.billId,
                event.actionSource,
                event.updatedRegisterData.amountTotal,
                shouldProcessScheduledBills = true,
            )
        }
    }

    private fun processBillAmountUpdated(message: Message) {
        val event = parseEvent(message) as AmountUpdated

        logger.info(append("event", event), "processBillAmountUpdated")
        paymentSchedulingEventService.updateAmount(
            event.walletId,
            event.billId,
            event.actionSource,
            event.amount,
            shouldProcessScheduledBills = true,
        )
    }

    private fun processBillPaymentScheduleUpdated(message: Message) {
        val event = parseEvent(message) as BillPaymentScheduleUpdated

        val markers = logEvent(event)

        paymentSchedulingEventService.updateCalculationId(event)

        logger.info(markers, "processBillPaymentScheduleUpdated")
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSSchedulingHandler::class.java)
    }
}