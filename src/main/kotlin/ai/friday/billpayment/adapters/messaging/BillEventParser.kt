package ai.friday.billpayment.adapters.messaging // ktlint-disable filename

import ai.friday.billpayment.adapters.dynamodb.BillEventDetailsEntity
import ai.friday.billpayment.adapters.dynamodb.toBillEvent
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.bill.BillEvent
import software.amazon.awssdk.services.sqs.model.Message

inline fun <reified T : BillEvent> Message.toEventType() = parseEvent(this).let { it as T }

fun parseEvent(m: Message): BillEvent {
    return parseObjectFrom<BillEventDetailsEntity>(m.body()).toBillEvent()
}