package ai.friday.billpayment.adapters.messaging.schedule

import ai.friday.billpayment.adapters.arbi.ArbiAccountMissingPermissionsException
import ai.friday.billpayment.adapters.arbi.ArbiInvalidBalanceException
import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.bill.and
import ai.friday.billpayment.app.integrations.ScheduledBillPaymentService
import ai.friday.billpayment.log
import ai.friday.morning.date.dateFormat
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class ProcessScheduledBillsHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = "sqs.payment.process-scheduled-bills") val queue: String,
    val scheduledBillPaymentService: ScheduledBillPaymentService,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = queue,
    consumers = 3,
) {
    private val logger = LoggerFactory.getLogger(ProcessScheduledBillsHandler::class.java)

    override fun handleMessage(message: Message): SQSHandlerResponse {
        val markers = log("queue_message" to message.body(), "receive_count" to message.receiveCount())

        val command =
            runCatching { parseObjectFrom<ProcessScheduledBillsCommand>(message.body()) }
                .getOrElse { ex ->
                    logger.error(markers, "ProcessScheduledBillsHandler#error", ex)
                    return SQSHandlerResponse(true)
                }

        markers.and(
            "walletId" to command.walletId.value,
            "scheduleDate" to dateFormat.format(command.scheduleDate),
            "includeSubscription" to command.includeSubscription,
        )

        scheduledBillPaymentService.process(command.walletId, command.scheduleDate, command.includeSubscription)

        logger.debug(markers, "ProcessScheduledBillsHandler")

        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    override fun handleError(
        message: Message,
        e: Exception,
    ): SQSHandlerResponse {
        when (e) {
            is ArbiAccountMissingPermissionsException, is ArbiInvalidBalanceException -> {
                logger.warn(append("queue_message", message.body()), "ProcessScheduledBillsHandler#ArbiInvalidBalanceException", e)
                return SQSHandlerResponse(shouldDeleteMessage = false)
            }

            else -> {
                logger.error(append("queue_message", message.body()), "ProcessScheduledBillsHandler#error", e)
                return SQSHandlerResponse(shouldDeleteMessage = false)
            }
        }
    }
}