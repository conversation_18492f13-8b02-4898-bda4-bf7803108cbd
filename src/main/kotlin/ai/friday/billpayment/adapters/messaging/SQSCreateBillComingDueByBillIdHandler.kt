package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.app.bill.BillComingDue
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.integrations.BillComingDueRepository
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Property
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSCreateBillComingDueByBillIdHandler(
    amazonSQS: SqsClient,
    configuration: ParallelMessageHandlerConfiguration,
    private val billEventRepository: BillEventRepository,
    private val billComingDueRepository: BillComingDueRepository,
    @Property(name = "sqs.createBillComingDueByIdQueueName") private val queueName: String,
) : AbstractParallelSQSHandler(
    amazonSQS,
    configuration,
    queueName,
    healthIndicatorTimeInMillis = 1_000,
) {
    @NewSpan("SQSCreateBillComingDueByBillIdHandler#handleMessage")
    @Trace
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val markers = append("event.body", message.body())
        logger.info(markers, "SQSCreateBillComingDueByBillIdHandler")

        val billId = BillId(message.body())

        val bill =
            billEventRepository.getBillById(billId).getOrElse {
                logger.error(markers, "SQSCreateBillComingDueByBillIdHandler", it)
                return SQSHandlerResponse(shouldDeleteMessage = true)
            }

        if (!bill.isActive() || bill.isOverdue()) {
            logger.warn(
                markers.andAppend("billStatus", bill.status)
                    .andAppend("billIsOverdue", bill.isOverdue())
                    .andAppend("reason", "unprocessable entity"),
                "SQSCreateBillComingDueByBillIdHandler",
            )
            return SQSHandlerResponse(shouldDeleteMessage = true)
        }

        billComingDueRepository.save(
            BillComingDue(
                billId = bill.billId,
                walletId = bill.walletId,
                dueDate = bill.effectiveDueDate,
                paymentLimitTime = bill.paymentLimitTime,
                isScheduled = bill.isPaymentScheduled(),
                created = ZonedDateTime.now(),
                notificatedAt = null,
            ),
        )

        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    override fun handleError(
        message: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(append("event", message), "SQSCreateBillComingDueByBillIdHandler", e)
        val deleteMessage = (e is IllegalStateException || e is java.lang.IllegalArgumentException)
        return SQSHandlerResponse(shouldDeleteMessage = deleteMessage)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSCreateBillComingDueByBillIdHandler::class.java)
    }
}