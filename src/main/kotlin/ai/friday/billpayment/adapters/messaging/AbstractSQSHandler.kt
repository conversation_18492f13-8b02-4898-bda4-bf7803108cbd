package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.bill.and
import ai.friday.billpayment.app.calculateDurationUntilNextExecution
import ai.friday.billpayment.log
import ai.friday.billpayment.originalSimpleName
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import com.amazonaws.arn.Arn
import io.micronaut.context.event.ApplicationEventListener
import io.micronaut.discovery.event.ServiceReadyEvent
import io.micronaut.health.HealthStatus
import io.micronaut.management.health.indicator.HealthIndicator
import io.micronaut.management.health.indicator.HealthResult
import io.micronaut.runtime.event.ApplicationShutdownEvent
import io.micronaut.scheduling.cron.CronExpression
import io.micronaut.tracing.annotation.NewSpan
import io.reactivex.Flowable
import jakarta.annotation.PreDestroy
import java.lang.Integer.max
import java.lang.Integer.min
import java.time.Duration
import java.time.Instant
import java.time.format.DateTimeFormatter
import java.util.UUID
import java.util.concurrent.TimeUnit
import kotlin.concurrent.thread
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.apache.commons.lang3.time.StopWatch
import org.reactivestreams.Publisher
import org.slf4j.LoggerFactory
import parallelMap
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.ChangeMessageVisibilityRequest
import software.amazon.awssdk.services.sqs.model.CreateQueueRequest
import software.amazon.awssdk.services.sqs.model.DeleteMessageRequest
import software.amazon.awssdk.services.sqs.model.GetQueueUrlRequest
import software.amazon.awssdk.services.sqs.model.Message
import software.amazon.awssdk.services.sqs.model.MessageSystemAttributeName
import software.amazon.awssdk.services.sqs.model.QueueAttributeName
import software.amazon.awssdk.services.sqs.model.QueueDoesNotExistException
import software.amazon.awssdk.services.sqs.model.ReceiveMessageRequest
import software.amazon.awssdk.services.sqs.model.SetQueueAttributesRequest

private const val subscriptionAttributeRawMessageDelivery = "RawMessageDelivery"
private const val subscriptionAttributeFilterPolicy = "FilterPolicy"

data class Queue(val url: String, val arn: String)

@Handler
abstract class AbstractSQSHandler(
    private val amazonSQS: SqsClient,
    private val configuration: MessageHandlerConfiguration,
    private val queueName: String,
    private val amazonSNS: SnsClient? = null,
    private val topicArn: String? = null,
    private val filter: EventTypeFilter<*>? = null,
    private val consumers: Int = 1,
    private val cronExpressionString: String? = null,
    private val cronToleranceInSeconds: Long = 0,
) : ApplicationEventListener<Any>, HealthIndicator {

    var isActive = false
        private set

    private val cronExpression: CronExpression? by lazy {
        cronExpressionString?.let {
            CronExpression.create(it)
        }
    }

    private val queueUrls = mutableMapOf<String, String>()
    private val queues = mutableMapOf<String, Queue>()

    private fun getQueueURL(queueName: String) = queueUrls.getOrPut(queueName) {
        amazonSQS.getQueueUrl(GetQueueUrlRequest.builder().queueName(queueName).build()).queueUrl()
    }

    private fun getQueue(queueName: String) = queues.getOrPut(queueName) {
        val queueUrl = amazonSQS.getQueueUrl { it.queueName(queueName) }.queueUrl()
        val attrs = amazonSQS.getQueueAttributes { it.queueUrl(queueUrl).attributeNames(QueueAttributeName.QUEUE_ARN) }

        return@getOrPut Queue(url = queueUrl, arn = attrs.attributesAsStrings()["QueueArn"]!!)
    }

    open fun receiveMessages(consumerId: Int = 0) {
        val threadName = "${this.javaClass.originalSimpleName()}-$consumerId"
        logger.info(Markers.append("sqsHandlerThreadName", threadName), "SQSHandlerStarting")
        val thread = thread(start = false, isDaemon = true, name = threadName, priority = 0) {
            logger.info(Markers.append("sqsHandlerThreadName", threadName), "SQSHandlerStarted")
            receiveMessagesAsync(consumerId)
            logger.info(Markers.append("sqsHandlerThreadName", threadName), "SQSHandlerStopped")
        }

        thread.setUncaughtExceptionHandler { t, exception ->
            logger.error(Markers.append("sqsHandlerThreadName", threadName), "SQSHandlerThreadException", exception)
        }

        thread.start()
    }

    @NewSpan
    open fun receiveMessagesAsync(consumerId: Int = 0) {
        val queueUrl = getQueueURL(queueName)

        val receiveRequest =
            ReceiveMessageRequest.builder().queueUrl(queueUrl)
                .maxNumberOfMessages(configuration.maxNumberOfMessages)
                .waitTimeSeconds(configuration.sqsWaitTime)
                .messageAttributeNames(".*")
                .attributeNames(QueueAttributeName.ALL)
                .build()
        while (isActive) {
            if (waitForNexCronWindow()) continue

            val messages = amazonSQS.receiveMessage(receiveRequest).messages()

            if (messages.isNotEmpty()) {
                process(consumerId, messages, queueUrl)
            }

            if (messages.isEmpty()) {
                if (configuration.sqsCoolDownTime > 0) {
                    Thread.sleep(configuration.sqsCoolDownTime * 1000L)
                }
            }
        }
    }

    private fun Instant.elapsedSeconds() = Duration.between(this, getZonedDateTime().toInstant()).toSeconds()
    private fun Instant.elapsedMillis() = Duration.between(this, getZonedDateTime().toInstant()).toMillis()

    open fun process(consumerId: Int = 0, messages: List<Message>, queueUrl: String) {
        for (message in messages) {
            if (isActive) {
                tryProcessMessage(message, queueUrl)
            } else {
                logger.warn(
                    append("consumerId", consumerId)
                        .andAppend("messages", messages.size)
                        .andAppend("messageId", message.messageId())
                        .andAppend("queueUrl", queueUrl),
                    "AbstractSQSHandler#process",
                )
            }
        }
    }

    protected fun Message.receiveCount(): Long {
        return attributes()[MessageSystemAttributeName.APPROXIMATE_RECEIVE_COUNT]?.toLong() ?: 0
    }

    @NewSpan
    open fun tryProcessMessage(message: Message, queueUrl: String) {
        val startInstant = getZonedDateTime().toInstant()
        val markers = Markers.append("queueName", queueName)
        try {
            val response = handleMessage(message)
            markers.andAppend("handleSeconds", startInstant.elapsedSeconds())
            if (response.visibilityTimeoutInSeconds != null) {
                changeMessageVisibilityTimeout(queueUrl, message, response.visibilityTimeoutInSeconds)
            }
            if (response.shouldDeleteMessage) {
                message.delete(queueUrl, markers)
            }
        } catch (e: Throwable) {
            val response = when (e) {
                is Exception -> handleError(message, e)
                else -> handleRuntimeError(message, e, queueName)
            }
            if (response.shouldDeleteMessage) {
                message.delete(queueUrl, markers)
            }
        }
        logger.trace(markers.andAppend("elapsedSeconds", startInstant.elapsedSeconds()), "tryProcessMessage")
    }

    private fun Message.delete(queueUrl: String, markers: LogstashMarker) {
        val startInstant = getZonedDateTime().toInstant()

        amazonSQS.deleteMessage(
            DeleteMessageRequest.builder().queueUrl(queueUrl).receiptHandle(this.receiptHandle()).build(),
        )

        markers.andAppend("deleteMilliseconds", startInstant.elapsedMillis())
    }

    abstract fun handleMessage(message: Message): SQSHandlerResponse

    open fun handleError(message: Message, e: Exception): SQSHandlerResponse = handleRuntimeError(message, e, queueName)

    open fun handleRuntimeError(message: Message, e: Throwable, queueName: String): SQSHandlerResponse {
        val markers = log(
            "queueName" to queueName,
            "eventBody" to message.body(),
            "exceptionMessage" to e.message,
            "implementation" to javaClass.simpleName,
            "cause" to e.cause?.message,
        )
        logger.error(markers, "SQSHandlerRuntimeError", e)
        return SQSHandlerResponse(false)
    }

    private fun changeMessageVisibilityTimeout(queueUrl: String, message: Message, visibilityTimeoutInSeconds: Int) {
        amazonSQS.changeMessageVisibility(
            ChangeMessageVisibilityRequest.builder().queueUrl(queueUrl)
                .receiptHandle(message.receiptHandle())
                .visibilityTimeout(visibilityTimeoutInSeconds).build(),
        )
    }

    override fun onApplicationEvent(event: Any) {
        val markers = Markers
            .append("queueName", queueName)
            .andAppend("topicArn", topicArn)
            .andAppend("eventName", event.javaClass.simpleName)
        when (event) {
            is ServiceReadyEvent -> {
                logger.info(markers, "AbstractSQSHandlerOnServiceReadyEvent")
                try {
                    createDLQIfNotExits()
                    createQueueIfNotExists()
                    createSubscriptionIfNotExists()
                    isActive = true
                    repeat(consumers) { consumerId ->
                        receiveMessages(consumerId)
                    }
                } catch (e: Exception) {
                    logger.error(markers, "AbstractSQSHandlerOnServiceReadyEvent", e)
                    throw e
                }
            }

            is ApplicationShutdownEvent -> {
                isActive = false
                logger.info(
                    markers.andAppend("forceShutDown", event.source.isForceExit),
                    "AbstractSQSHandlerOnApplicationShutdownEvent",
                )
            }
        }
    }

    override fun getResult(): Publisher<HealthResult> {
        val healthStatus = if (isActive) HealthStatus.UP else HealthStatus.DOWN
        return Flowable.just(HealthResult.builder("${this::class.simpleName}HealthIndicator", healthStatus).build())
    }

    private fun createSubscriptionIfNotExists() {
        amazonSNS?.let { sns ->
            val markers = log("queue_name" to queueName)

            val queue = getQueue(queueName)
            markers.and("queue" to queue)

            val subscriptions = sns.listSubscriptionsByTopic { it.topicArn(topicArn) }.subscriptions()
            if (subscriptions.none { it.endpoint() == queue.arn }) {
                val attributes = mutableMapOf(subscriptionAttributeRawMessageDelivery to "true")
                if (filter != null) {
                    attributes[subscriptionAttributeFilterPolicy] = getObjectMapper().writeValueAsString(filter)
                }

                amazonSNS.subscribe {
                    it.topicArn(topicArn).protocol("sqs").endpoint(queue.arn).attributes(attributes)
                }.subscriptionArn()

                val policy =
                    mapOf(QueueAttributeName.POLICY to buildPolicy(queueArn = queue.arn, topicArn = topicArn!!))

                amazonSQS.setQueueAttributes { it.queueUrl(queue.url).attributes(policy) }

                logger.info(markers.and("policy" to subscriptions), "AbstractSQSHandler#createSubscriptionIfNotExists")
            }
        }
    }

    private fun createQueueIfNotExists() {
        try {
            amazonSQS.getQueueUrl { it.queueName(queueName) }.queueUrl()
        } catch (e: QueueDoesNotExistException) {
            logger.info(
                Markers.append("queueName", queueName),
                "AbstractSQSHandler#CreatingQueue",
            )
            val createQueueResult = amazonSQS.createQueue(CreateQueueRequest.builder().queueName(queueName).build())

            val dlq = createExclusiveDLQ(queueName)

            val request = SetQueueAttributesRequest.builder().queueUrl(createQueueResult.queueUrl()).attributes(
                mapOf(
                    QueueAttributeName.VISIBILITY_TIMEOUT to configuration.visibilityTimeout.toString(),
                    QueueAttributeName.REDRIVE_POLICY to "{\"maxReceiveCount\":\"${configuration.maxReceiveCount}\", \"deadLetterTargetArn\":\"${dlq.arn}\"}",
                ),
            ).build()

            amazonSQS.setQueueAttributes(request)
        }
    }

    private fun createExclusiveDLQ(queueName: String): Queue {
        val dlqQueueName = queueName + "_dlq"
        amazonSQS.createQueue(CreateQueueRequest.builder().queueName(dlqQueueName).build())
        return getQueue(dlqQueueName)
    }

    private fun createDLQIfNotExits() {
        val arn = Arn.fromString(configuration.dlqArn)
        val queueName = arn.resourceAsString
        try {
            amazonSQS.getQueueUrl { it.queueName(queueName) }.queueUrl()
        } catch (e: QueueDoesNotExistException) {
            logger.info(
                Markers.append("queueName", queueName),
                "AbstractSQSHandler#CreatingDLQQueue",
            )
            val createQueueResult = amazonSQS.createQueue(CreateQueueRequest.builder().queueName(queueName).build())
            val request = SetQueueAttributesRequest.builder().queueUrl(createQueueResult.queueUrl()).attributes(
                mapOf(
                    QueueAttributeName.VISIBILITY_TIMEOUT to configuration.visibilityTimeout.toString(),
                ),
            ).build()

            amazonSQS.setQueueAttributes(request)
        }
    }

    private fun waitForNexCronWindow(): Boolean {
        val duration = cronExpression.calculateDurationUntilNextExecution(cronToleranceInSeconds) ?: return false

        val markers =
            Markers.append("cron", cronExpressionString)
                .andAppend("cronToleranceInSeconds", cronToleranceInSeconds)
                .andAppend("duration", duration)
                .andAppend(
                    "nextExecution",
                    getZonedDateTime().plusSeconds(duration.seconds).format(DateTimeFormatter.ISO_DATE_TIME),
                )

        runBlocking {
            logger.info(markers, "SQSHandler#Stopped")
            delay(duration.toMillis())
            logger.info(markers, "SQSHandler#Restarted")
        }

        return false
    }

    @PreDestroy
    fun tearDown() {
        isActive = false
    }

    override fun supports(event: Any) = true

    private fun buildPolicy(queueArn: String, topicArn: String) = """{
          "Version": "2012-10-17",
          "Statement": [
            {
              "Sid": "${UUID.randomUUID()}",
              "Effect": "Allow",
              "Principal": {
                "AWS": "*"
              },
              "Action": "sqs:SendMessage",
              "Resource": "$queueArn",
              "Condition": {
                "ArnLike": {
                  "aws:SourceArn": "$topicArn"
                }
              }
            }
          ]
        }
    """

    companion object {
        private val logger = LoggerFactory.getLogger(AbstractSQSHandler::class.java)
    }
}

class WorkersCalculator(
    minParallelism: Int,
    maxParallelism: Int,
    private val healthIndicatorTimeInMillis: Int = 5000,
    private val minHardLimit: Int = 1,
    private val maxHardLimit: Int = 10,
    private val hardDownScale: Boolean = true,
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    init {
        if (minParallelism < 1 || maxParallelism < 1 || minParallelism > maxParallelism) {
            throw IllegalStateException("Invalid values for maxParallelism and maxParallelism")
        }
    }

    val maxWorkers: Int by lazy { applyMaxHardLimit(maxParallelism) }
    val minWorkers: Int by lazy { applyMinHardLimit(minParallelism) }

    private var currentWorkers: Int = minWorkers
    private val computedExecutions = arrayListOf<Long>()
    private val updateWorkersAfterEach = 10

    fun currentWorkers(): Int {
        val size = computedExecutions.size
        // TODO define this magic number

        if (size > 0 && size % updateWorkersAfterEach == 0) {
            val avgTime = computedExecutions.average()
            val delta = healthIndicatorTimeInMillis - avgTime
            val previousWorkers = currentWorkers

            val markers = Markers.appendEntries(
                mapOf(
                    "computedExecutions" to computedExecutions.toArray(),
                    "previousWorkers" to previousWorkers,
                    "average" to avgTime,
                    "delta" to delta,
                ),
            )

            computedExecutions.clear()

            when {
                delta > 0 -> increaseWorkers()
                else -> decreaseWorkers()
            }

            if (previousWorkers != currentWorkers) {
                logger.info(markers.andAppend("updatedWorkers", currentWorkers), "WorkersCalculator")
            }
        }

        return currentWorkers
    }

    fun computeJobStarted(): StopWatch {
        val sw = StopWatch()
        sw.start()
        return sw
    }

    fun computeJobElapsedTimeInMillis(elapsedTime: Long) {
        computedExecutions.add(elapsedTime)
    }

    // TODO avaliar aumentar/manter
    private fun increaseWorkers() {
        currentWorkers = min(applyMaxHardLimit(++currentWorkers), maxWorkers)
    }

    // TODO avaliar diminuir/manter
    private fun decreaseWorkers() {
        currentWorkers = when (hardDownScale) {
            false -> max(applyMinHardLimit(--currentWorkers), minWorkers)
            true -> minWorkers
        }
    }

    private fun applyMaxHardLimit(number: Int) = min(number, maxHardLimit)
    private fun applyMinHardLimit(number: Int) = max(applyMaxHardLimit(number), minHardLimit)
}

abstract class AbstractParallelSQSHandler(
    amazonSQS: SqsClient,
    configuration: ParallelMessageHandlerConfiguration,
    queueName: String,
    amazonSNS: SnsClient? = null,
    topicArn: String? = null,
    filter: EventTypeFilter<*>? = null,
    minParallelism: Int = 1,
    maxParallelism: Int = 10,
    healthIndicatorTimeInMillis: Int = 5000,
    consumers: Int = 1,
) : AbstractSQSHandler(amazonSQS, configuration, queueName, amazonSNS, topicArn, filter, consumers = consumers) {

    private val workersCalculator by lazy {
        val calculatorsList = mutableListOf<WorkersCalculator>()
        repeat(consumers) {
            calculatorsList.add(WorkersCalculator(minParallelism, maxParallelism, healthIndicatorTimeInMillis))
        }
        calculatorsList
    }

    private val autoScaleWorkers by lazy { configuration.autoScaleWorkersInParallel }

    override fun process(consumerId: Int, messages: List<Message>, queueUrl: String) {
        val currentWorkersCalculator = workersCalculator[consumerId]

        val workers =
            if (autoScaleWorkers) currentWorkersCalculator.currentWorkers() else currentWorkersCalculator.minWorkers

        messages.chunked(workers).forEach { chunk ->
            runBlocking(Dispatchers.IO) {
                chunk.parallelMap {
                    val watch = currentWorkersCalculator.computeJobStarted()
                    tryProcessMessage(it, queueUrl)
                    val elapsedTime = watch.getTime(TimeUnit.MILLISECONDS)
                    currentWorkersCalculator.computeJobElapsedTimeInMillis(elapsedTime)
                }
            }
        }
    }
}

data class SQSHandlerResponse(val shouldDeleteMessage: Boolean, val visibilityTimeoutInSeconds: Int? = null)