package ai.friday.billpayment.adapters.messaging.eventbus

import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.EventTypeFilter
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.messaging.billEventTopicArnPropertyName
import ai.friday.billpayment.adapters.messaging.markers
import ai.friday.billpayment.adapters.messaging.parseEvent
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.eventbus.EventBusMessageProcessor
import ai.friday.billpayment.app.feature.RequiresEventBus
import ai.friday.billpayment.plus
import io.micronaut.context.annotation.Property
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@RequiresEventBus
open class SQSEventBusHandler(
    private val messageProcessor: EventBusMessageProcessor,
    amazonSQS: SqsClient,
    amazonSNS: SnsClient,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = "sqs.eventBusQueueName") private val queueName: String,
    @Property(name = billEventTopicArnPropertyName) private val topicArn: String,
) : AbstractSQSHandler(
    amazonSQS,
    configuration,
    queueName,
    amazonSNS,
    topicArn,
    filter = EventTypeFilter(listOf(BillEventType.PAID)),
) {
    @NewSpan
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val markers = message.markers()
        val billEvent = parseEvent(message)

        val result = messageProcessor.processBillEvent(billEvent).getOrElse {
            logger.error("SQSEventBusHandler", it)
            throw it
        }

        when (result) {
            EventBusMessageProcessor.PublishResponse.Skipped ->
                logger.info(markers.plus("skipped" to 1), "SQSEventBusHandler")

            is EventBusMessageProcessor.PublishResponse.Published ->
                logger.info(markers.plus("messageId" to result), "SQSEventBusHandler")
        }

        return SQSHandlerResponse(true)
    }

    override fun handleError(message: Message, e: Exception): SQSHandlerResponse = handleRuntimeError(message, e, queueName)

    private val logger = LoggerFactory.getLogger(javaClass)
}