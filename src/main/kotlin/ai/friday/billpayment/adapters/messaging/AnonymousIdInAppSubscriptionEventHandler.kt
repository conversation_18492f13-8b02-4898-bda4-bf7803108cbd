package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.inappsubscription.AnonymousIdInAppSubscriptionQueueMessageTO
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionService
import ai.friday.billpayment.app.inappsubscription.TransferFromAnonymousIdResult
import ai.friday.morning.log.andAppend
import jakarta.inject.Singleton
import java.time.Duration
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class AnonymousIdInAppSubscriptionEventHandler(
    amazonSQS: SqsClient,
    private val configuration: SQSMessageHandlerConfiguration,
    private val inAppSubscriptionService: InAppSubscriptionService,
    private val messagePublisher: SQSMessagePublisher,
) : AbstractSQSHandler(
    amazonSQS,
    configuration,
    configuration.anonymousIdInAppSubscriptionQueueName,
    consumers = 1,
) {
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val messageData = parseObjectFrom<AnonymousIdInAppSubscriptionQueueMessageTO>(message.body())
        val markers = Markers.append("messageBody", messageData)

        if (messageData.retry > 16) {
            logger.error(
                markers
                    .andAppend("result", "MAX_RETRIES_REACHED")
                    .andAppend("ACTION", "VERIFY")
                    .andAppend("context", "evento com ID anonimo. Não será associado com nenhum usuário. Verificar no RevenueCat se o id anonimo está associado a um accountid. Se estiver, tem que atribuir a licença (que foi gerada com o idanonimo na base). Se não tiver lá, não há muito o que fazer."),
                "AnonymousIdInAppSubscriptionEventHandler#maxRetriesReached",
            )
            return SQSHandlerResponse(true)
        }

        logger.info(markers, "AnonymousIdInAppSubscriptionEventHandler#start")

        inAppSubscriptionService.transferSubscriptionFromExternalId(messageData.appUserId).fold(
            onSuccess = { result ->
                return when (result) {
                    is TransferFromAnonymousIdResult.Transferred -> {
                        logger.info(markers.andAppend("targetAccountId", result.targetAccountId), "AnonymousIdInAppSubscriptionEventHandler#success")
                        SQSHandlerResponse(true)
                    }
                    is TransferFromAnonymousIdResult.Failed,
                    is TransferFromAnonymousIdResult.InvalidExternalId,
                    is TransferFromAnonymousIdResult.InvalidTargetAccount,
                    is TransferFromAnonymousIdResult.TargetAccountIdNotFound,
                    -> {
                        logger.warn(markers.andAppend("result", result), "AnonymousIdInAppSubscriptionEventHandler#retry")

                        messagePublisher.sendMessage(
                            configuration.anonymousIdInAppSubscriptionQueueName,
                            AnonymousIdInAppSubscriptionQueueMessageTO(
                                appUserId = messageData.appUserId,
                                retry = messageData.retry + 1,
                            ),
                            delaySeconds = Duration.ofMinutes(15).toSeconds().toInt(),
                        )

                        SQSHandlerResponse(true)
                    }
                }
            },
            onFailure = { error ->
                logger.error(markers.andAppend("ACTION", "VERIFY"), "AnonymousIdInAppSubscriptionEventHandler", error)
                return SQSHandlerResponse(false)
            },
        )
    }

    override fun handleError(message: Message, e: Exception): SQSHandlerResponse {
        logger.error(Markers.append("messageBody", message.body()).andAppend("ACTION", "VERIFY"), "AnonymousIdInAppSubscriptionEventHandler", e)
        return SQSHandlerResponse(false)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(AnonymousIdInAppSubscriptionEventHandler::class.java)
    }
}