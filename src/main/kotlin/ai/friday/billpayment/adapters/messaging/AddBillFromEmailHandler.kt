package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.bill.BillValidationException
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.MailboxAddBillError
import ai.friday.billpayment.app.email.AddBoletoFromEmailMessage
import ai.friday.billpayment.app.email.MailObjectService
import ai.friday.billpayment.app.integrations.MailBoxService
import ai.friday.billpayment.app.mailbox.MailboxGlobalData
import ai.friday.billpayment.app.notification.EmailNotProcessedNotificationRequest
import ai.friday.billpayment.app.notification.InsecureBillNotificationRequest
import ai.friday.billpayment.app.notification.NotificationRouterService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.context.annotation.Property
import io.micronaut.tracing.annotation.NewSpan
import io.via1.communicationcentre.app.email.IncomingEmail
import io.via1.communicationcentre.app.integrations.EmailSenderService
import io.via1.communicationcentre.app.receipt.Receipt
import jakarta.inject.Singleton
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class AddBillFromEmailHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val mailBoxService: MailBoxService,
    private val notificationRouterService: NotificationRouterService,
    private val mailObjectService: MailObjectService,
    private val walletService: WalletService,
    private val accountService: AccountService,
    private val emailSenderService: EmailSenderService,
    @Property(name = "integrations.manual-workflow.emails") private val manualWorkflowEmail: String,
    @Property(name = "features.forwardEmailToManualWorkflow") private val forwardEmailToManualWorkflow: Boolean,
    @Property(name = "email.add-bill-email-max-receive-attempts") private val addBillEmailMaxReceiveAttempts: Int,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = configuration.addBillFromEmailQueueName,
) {

    @NewSpan
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val shouldStopRetrying =
            message.receiveCount() >= addBillEmailMaxReceiveAttempts

        val addBoletoFromEmailMessage = getObjectMapper().readValue(message.body(), AddBoletoFromEmailMessage::class.java)

        val wallet = walletService.findWallet(addBoletoFromEmailMessage.walletId)
        val member = wallet.getMember(addBoletoFromEmailMessage.accountId)

        val markers = Markers.append("event", addBoletoFromEmailMessage)

        mailBoxService.addBill(
            walletId = addBoletoFromEmailMessage.walletId,
            barCode = addBoletoFromEmailMessage.barCode,
            dueDate = addBoletoFromEmailMessage.dueDate,
            from = EmailAddress(addBoletoFromEmailMessage.from),
            subject = addBoletoFromEmailMessage.subject,
            receipt = addBoletoFromEmailMessage.receipt,
            shouldNotifyOnRetryableError = shouldStopRetrying,
        ).getOrElse {
            return when (it) {
                is MailboxAddBillError.MailboxSecurityValidationError -> {
                    notificationRouterService.routeNotification(
                        InsecureBillNotificationRequest(
                            members = listOf(member),
                            subject = addBoletoFromEmailMessage.subject,
                        ),
                    )
                    SQSHandlerResponse(true)
                }

                is MailboxAddBillError.MailboxAddInvalidBill -> {
                    when {
                        it.isRetryable && !shouldStopRetrying ->
                            SQSHandlerResponse(
                                false,
                                visibilityTimeoutInSeconds = 30 * message.receiveCount().toInt(),
                            )

                        it.isRetryable && shouldStopRetrying -> {
                            handleBillNotParsed(
                                receipt = addBoletoFromEmailMessage.receipt,
                                accountId = addBoletoFromEmailMessage.accountId,
                                emailPath = addBoletoFromEmailMessage.emailDestinationPath,
                                markers = markers,
                            )
                            SQSHandlerResponse(true)
                        }

                        it.result is CreateBillResult.FAILURE.ServerError && it.result.throwable is BillValidationException -> {
                            handleBillNotParsed(
                                receipt = addBoletoFromEmailMessage.receipt,
                                accountId = addBoletoFromEmailMessage.accountId,
                                emailPath = addBoletoFromEmailMessage.emailDestinationPath,
                                markers = markers,
                            )
                            SQSHandlerResponse(true)
                        }

                        else -> {
                            SQSHandlerResponse(true)
                        }
                    }
                }

                is MailboxAddBillError.MailboxAccountNotFoundError -> {
                    SQSHandlerResponse(true)
                }

                is MailboxAddBillError.MailBoxAddInvalidRequest -> {
                    handleBillNotParsed(
                        receipt = addBoletoFromEmailMessage.receipt,
                        accountId = addBoletoFromEmailMessage.accountId,
                        emailPath = addBoletoFromEmailMessage.emailDestinationPath,
                        markers = markers,
                    )
                    SQSHandlerResponse(true)
                }
            }.also { response ->
                markers.andAppend("shouldDelete", response.shouldDeleteMessage)
                    .andAppend("result", it::class.java.simpleName)
                    .andAppend("addBillResultStatus", "bill not added")
                logger.warn(markers, "AddBillFromEmailHandler")
            }
        }

        logger.info(
            markers.andAppend("result", "bill added"),
            "AddBillFromEmailHandler",
        )
        return SQSHandlerResponse(true)
    }

    override fun handleError(
        message: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(Markers.append("messageBody", message.body()), "AddBillFromEmailHandler", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    private fun handleBillNotParsed(
        receipt: Receipt,
        emailPath: String,
        accountId: AccountId,
        markers: LogstashMarker,
    ) {
        val incomingEmailWithMetadata =
            mailObjectService.retrieveEmailFromS3(receipt.action, emailPath)
        val incomingEmail = incomingEmailWithMetadata.incomingEmail
        val account = accountService.findAccountById(accountId)

        if (!MailboxGlobalData.blockList.contains(incomingEmail.sender.address)) {
            sendToManualWorkflow(incomingEmail, receipt, markers)
            notifyEmailNotProcessed(account, incomingEmail)
        }
    }

    private fun sendToManualWorkflow(
        incomingEmail: IncomingEmail,
        receipt: Receipt,
        markers: LogstashMarker,
    ) {
        if (forwardEmailToManualWorkflow) {
            try {
                val document = incomingEmail.recipient.substringBefore("@")
                incomingEmail.subject = "$document : ${incomingEmail.subject}"
                emailSenderService.forward(incomingEmail, manualWorkflowEmail)
            } catch (e: Exception) {
                logger.error(
                    markers.andAppend("S3Action", receipt.action).andAppend("status", "failed to notify manual workflow"),
                    "AddBillFromEmailHandler",
                    e,
                )
            }
        }
    }

    private fun notifyEmailNotProcessed(
        account: Account,
        incomingEmail: IncomingEmail,
    ) {
        val wallet = walletService.findWallet(account.configuration.defaultWalletId!!)
        notificationRouterService.routeNotification(
            EmailNotProcessedNotificationRequest(
                members = wallet.activeMembers,
                walletName = wallet.name,
                sender = EmailAddress(incomingEmail.sender.address),
                subject = incomingEmail.subject,
                dateTime = getZonedDateTime(),
            ),
        )
    }

    private val logger = LoggerFactory.getLogger(AddBillFromEmailHandler::class.java)
}