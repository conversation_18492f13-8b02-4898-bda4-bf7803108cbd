package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.PaymentFailed
import ai.friday.billpayment.app.bill.PaymentRefunded
import ai.friday.billpayment.app.bill.PaymentRefundedReason
import ai.friday.billpayment.app.contact.ContactService
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.pix.PixTransactionError
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSInvalidateBillRecipientHandler(
    amazonSQS: SqsClient,
    amazonSNS: SnsClient,
    @Property(name = billEventTopicArnPropertyName) private val topicArn: String,
    @Property(name = sqsArnPrefix) private val queueArnPrefix: String,
    private val contactService: ContactService,
    configuration: SQSMessageHandlerConfiguration,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    amazonSNS = amazonSNS,
    topicArn = topicArn,
    filter = EventTypeFilter(eventTypes = listOf(BillEventType.PAYMENT_FAIL, BillEventType.PAYMENT_REFUNDED)),
    configuration = configuration.copy(
        visibilityTimeout = 300,
        sqsWaitTime = 20,
        dlqArn = queueArnPrefix + configuration.invalidateBankAccountQueueName + dlqSuffix,
    ),
    queueName = configuration.invalidateBankAccountQueueName,
) {
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val billType =
            m.messageAttributes()[billTypeAttributeName]?.let { BillType.valueOf(it.stringValue()) }
                ?: BillType.OTHERS
        when (val billEvent = parseEvent(m)) {
            is PaymentFailed -> {
                if (shouldInvalidateSettlementInfo(billEvent, billType)) {
                    val contact =
                        contactService.invalidateSettlementInfo(
                            billEvent.walletId,
                            billEvent.billId,
                            billEvent.errorDescription,
                        )
                    logger.info(
                        append("BillEvent", billEvent)
                            .andAppend("queue_message", m.toString())
                            .andAppend("contactId", contact.id.value)
                            .andAppend("BillType", billType),
                        "InvalidateBillRecipientHandler",
                    )
                }
            }

            is PaymentRefunded -> {
                if (billEvent.reason == PaymentRefundedReason.INVALID_DATA) {
                    val contact =
                        contactService.invalidateSettlementInfo(
                            billEvent.walletId,
                            billEvent.billId,
                            billEvent.reason.name,
                        )
                    logger.info(
                        append("BillEvent", billEvent)
                            .andAppend("queue_message", m.toString())
                            .andAppend("contactId", contact.id.value)
                            .andAppend("BillType", billType),
                        "InvalidateBillRecipientHandler",
                    )
                }
            }

            else -> logger.warn(append("error_message", m), "InvalidateBillRecipientHandler")
        }
        return SQSHandlerResponse(true)
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.warn(append("error_message", m), "InvalidateBillRecipientHandler", e)
        return SQSHandlerResponse(e is ItemNotFoundException)
    }

    private fun shouldInvalidateSettlementInfo(
        billEvent: PaymentFailed,
        billType: BillType,
    ): Boolean {
        if (!billType.isBankTransfer() || billEvent.retryable) {
            return false
        }

        return billEvent.errorDescription != PixTransactionError.SettlementDestinationNotAllowedAmount.code
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSInvalidateBillRecipientHandler::class.java)
    }
}