package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.notification.BillComingDueService
import ai.friday.billpayment.app.notification.NotifyBillComingDueMessage
import ai.friday.billpayment.app.notification.NotifyBillComingDueType
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requirements(Requires(beans = [BillComingDueService::class]), Requires(notEnv = ["test"]))
open class SQSNotifyBillComingDueHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = "sqs.queues.notifyBillComingDue") private val queueName: String,
    private val billComingDueService: BillComingDueService,
) : AbstractSQSHandler(
    amazonSQS,
    configuration,
    queueName,
    consumers = 4,
) {
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val message = parseObjectFrom<NotifyBillComingDueMessage>(m.body())

        logger.debug(Markers.append("body", message), "SQSNotifyBillComingDueHandler")

        when (message.type) {
            NotifyBillComingDueType.YESTERDAY -> billComingDueService.executeOverdueYesterday(message.walletId)
            NotifyBillComingDueType.LAST_WARN -> billComingDueService.executeLastWarn(message.walletId)
            NotifyBillComingDueType.LAST_WARN_REST_OF_DAY -> billComingDueService.executeLastWarnRestOfDay(message.walletId)
            NotifyBillComingDueType.REGULAR -> billComingDueService.executeRegular(
                message.walletId,
            )

            NotifyBillComingDueType.REGULAR_CHATBOT -> billComingDueService.executeChatBotAiRegular(
                message.walletId,
            )
        }

        return SQSHandlerResponse(true)
    }

    override fun handleError(m: Message, e: Exception): SQSHandlerResponse {
        logger.error(Markers.append("body", m), "SQSNotifyBillComingDueHandler", e)
        return SQSHandlerResponse(false)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSNotifyBillComingDueHandler::class.java)
    }
}