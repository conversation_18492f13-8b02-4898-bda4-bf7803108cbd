package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.dda.BatchAddOrder
import ai.friday.billpayment.app.dda.BatchMigrationOrder
import ai.friday.billpayment.app.dda.DDARegisterMessage
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.message.QueueMessage
import ai.friday.billpayment.app.message.QueueMessageBatch
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.log
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.GetQueueUrlRequest
import software.amazon.awssdk.services.sqs.model.MessageAttributeValue
import software.amazon.awssdk.services.sqs.model.SendMessageBatchRequest
import software.amazon.awssdk.services.sqs.model.SendMessageBatchRequestEntry
import software.amazon.awssdk.services.sqs.model.SendMessageRequest

@FridayMePoupe
class SQSMessagePublisher(
    private val amazonSQS: SqsClient,
    private val configuration: SQSMessageHandlerConfiguration,
    @Property(name = "tenant.id") private val tenantName: String,
) : MessagePublisher {

    private val queueUrls = mutableMapOf<String, String>()

    private fun getQueueURL(queueName: String): String {
        return queueUrls.getOrPut(queueName) {
            amazonSQS.getQueueUrl(GetQueueUrlRequest.builder().queueName(queueName).build()).queueUrl()
        }
    }

    override fun sendMessage(queueName: String, body: Any, delaySeconds: Int?) {
        val queueUrl = getQueueURL(queueName)

        val queueMessage = getObjectMapper().writeValueAsString(body)
        val sendMessageRequestBuilder =
            SendMessageRequest.builder().queueUrl(queueUrl).messageBody(queueMessage)
                .messageAttributes(
                    mapOf(
                        "X-TENANT-ID" to MessageAttributeValue.builder()
                            .dataType("String")
                            .stringValue(tenantName)
                            .build(),
                    ),
                )
        if (delaySeconds != null && delaySeconds > 0) {
            sendMessageRequestBuilder.delaySeconds(delaySeconds)
        }

        LOG.info(log("queueName" to queueName, "queueMessage" to queueMessage), "SQSMessagePublisher")

        amazonSQS.sendMessage(sendMessageRequestBuilder.build())
    }

    override fun sendMessage(queueMessage: QueueMessage) {
        val queueUrl = getQueueURL(queueMessage.queueName)

        val sendMessageRequestBuilder =
            SendMessageRequest.builder().queueUrl(queueUrl).messageBody(queueMessage.jsonObject)
                .messageAttributes(
                    mapOf(
                        "X-TENANT-ID" to MessageAttributeValue.builder()
                            .dataType("String")
                            .stringValue(tenantName)
                            .build(),
                    ),
                )
        queueMessage.delaySeconds?.let { delaySeconds ->
            sendMessageRequestBuilder.delaySeconds(delaySeconds)
        }
        val sendMessageRequest = sendMessageRequestBuilder.build()
        LOG.info(
            append("queueMessage", queueMessage)
                .and(append("messageRequest", sendMessageRequest)),
            "SQSMessagePublisher",
        )
        amazonSQS.sendMessage(sendMessageRequest)
    }

    override fun sendGenerateBillReceipt(billPaid: BillPaid) {
        sendMessage(
            QueueMessage(
                queueName = configuration.billReceiptGeneratorQueueName,
                jsonObject = getObjectMapper().writeValueAsString(billPaid.toBillEventDetailEntity()),
            ),
        )
    }

    override fun sendMessageBatch(messageBatch: QueueMessageBatch) {
        if (messageBatch.messages.isEmpty()) return
        val queueURL = getQueueURL(messageBatch.queueName)
        val markers = append("queueName", messageBatch.queueName)
            .andAppend("totalMessagesSize", messageBatch.messages.size)

        val chunks = messageBatch.messages.mapIndexed { index, message ->
            LOG.trace(append("queueMessage", message).and(markers), "SQSMessagePublisher")
            SendMessageBatchRequestEntry.builder()
                .id(index.toString())
                .messageBody(message)
                .messageAttributes(
                    mapOf(
                        "X-TENANT-ID" to MessageAttributeValue.builder()
                            .dataType("String")
                            .stringValue(tenantName)
                            .build(),
                    ),
                )
                .build()
        }.chunked(BATCH_SIZE)

        for (chunk in chunks) {
            val batchRequest = SendMessageBatchRequest.builder().queueUrl(queueURL).entries(chunk).build()
            val response = amazonSQS.sendMessageBatch(batchRequest)

            if (response.hasFailed()) {
                LOG.error(
                    markers.andAppend("failedMessagesSize", response.failed().size).and(
                        Markers.appendArray(
                            "failedMessages",
                            response.failed(),
                        ),
                    ),
                    "SQSMessagePublisher",
                )
            }

            LOG.info(markers.andAppend("queuedMessages", response.successful().size), "SQSMessagePublisher")
        }
    }

    override fun sendRetryTransactionMessage(transactionId: TransactionId, delaySeconds: Int?) {
        sendRetryMessage(transactionId, retryTransactionEventType, delaySeconds)
    }

    override fun sendRetryCashinMessage(transactionId: TransactionId, delaySeconds: Int?) {
        sendRetryMessage(transactionId, retryCashinTransactionEventType, delaySeconds)
    }

    override fun sendBatchAddOrderMessage(batchAddOrder: BatchAddOrder) {
        sendMessage(
            QueueMessage(
                queueName = configuration.ddaBatchAddOrdersQueueName,
                getObjectMapper().writeValueAsString(batchAddOrder),
            ),
        )
    }

    override fun sendBatchDDARequestedMessage(batch: List<DDARegisterMessage>) {
        sendMessageBatch(
            QueueMessageBatch(
                configuration.ddaFullImportQueueName,
                batch.map { getObjectMapper().writeValueAsString(it) },
            ),
        )
    }

    override fun sendBatchMigrationOrderMessage(order: BatchMigrationOrder) {
        sendMessage(
            QueueMessage(
                queueName = configuration.ddaBatchMigrationOrdersQueueName,
                getObjectMapper().writeValueAsString(order),
            ),
        )
    }

    private fun sendRetryMessage(transactionId: TransactionId, eventType: String, delaySeconds: Int?) {
        sendMessage(
            queueMessage = QueueMessage(
                queueName = configuration.rollbackTransactionQueueName,
                parameters = mapOf(
                    "transactionId" to transactionId.value,
                    "eventType" to eventType,
                ),
                delaySeconds = delaySeconds,
            ),
        )
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SQSMessagePublisher::class.java)
        private const val BATCH_SIZE = 10
    }
}