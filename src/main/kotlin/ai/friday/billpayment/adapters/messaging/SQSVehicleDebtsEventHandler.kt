package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.vehicledebts.LicensePlate
import ai.friday.billpayment.app.vehicledebts.VehicleDebts
import ai.friday.billpayment.app.vehicledebts.VehicleDebtsBillRequest
import ai.friday.billpayment.app.vehicledebts.VehicleDebtsService
import ai.friday.billpayment.app.vehicledebts.VehicleDebtsType
import ai.friday.morning.log.andAppend
import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.micronaut.context.annotation.Property
import io.micronaut.tracing.annotation.NewSpan
import java.time.LocalDate
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@VehicleDebts
open class SQSVehicleDebtsEventHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val vehicleDebtsService: VehicleDebtsService,
    @Property(name = "integrations.vehicle-debts.dlqArn") private val dlqArn: String,
) : AbstractSQSHandler(
    amazonSQS,
    configuration.copy(dlqArn = dlqArn),
    configuration.vehicleDebtsQueueName,
    consumers = 1,
) {
    @NewSpan
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val body = message.body()
        val markers = Markers.append("request", body)

        val request = parseObjectFrom<VehicleDebtsCallbackRequest>(body)

        // FIXME Tratar melhor as falhas para não retentar
        val result = vehicleDebtsService.handle(request.toVehicleDebtsBillRequest()).getOrElse {
            LOG.error(markers.andAppend("request", request), "SQSVehicleDebtsEventHandler", it)
            return SQSHandlerResponse(false)
        }

        LOG.info(markers.andAppend("request", request).andAppend("result", result), "SQSVehicleDebtsEventHandler")
        return SQSHandlerResponse(true)
    }

    override fun handleError(
        message: Message,
        e: Exception,
    ): SQSHandlerResponse {
        LOG.error(Markers.append("message", message.body()), "SQSVehicleDebtsEventHandler", e)
        return SQSHandlerResponse(false)
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SQSVehicleDebtsEventHandler::class.java)
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class VehicleDebtsCallbackRequest(
    val id: String?,
    @JsonAlias("tenantUserId")
    val accountId: String,
    val document: String,
    val licensePlate: String,
    val amountTotal: Long,
    val dueDate: String?,
    val description: String,
    val barcode: String?,
    val digitableLine: String?,
    val paymentStatus: String,
    val fees: Long,
    val fine: Long,
    val externalId: String?,
    val type: String,
    val originalDueDate: String?,
    val unavailableReason: String?,
    val quota: Long?,
    val identifier: String?,
)

fun VehicleDebtsCallbackRequest.toVehicleDebtsBillRequest() = VehicleDebtsBillRequest(
    accountId = AccountId(accountId),
    barcode = barcode?.let { BarCode.of(it) },
    dueDate = dueDate?.let { LocalDate.parse(it) },
    description = description,
    document = Document(document),
    licensePlate = LicensePlate(licensePlate),
    amountTotal = amountTotal,
    paymentStatus = toPaymentStatus(),
    fees = fees,
    fine = fine,
    externalId = id ?: identifier ?: externalId,
    type = VehicleDebtsType.valueOf(type),
    originalDueDate = (originalDueDate ?: dueDate)?.let { LocalDate.parse(it) },
    unavailableReason = unavailableReason,
    quota = quota,
    identifier = identifier,
)

private fun VehicleDebtsCallbackRequest.toPaymentStatus() = when (paymentStatus) {
    "opened",
    "active",
    "ACTIVE",
    -> VehicleDebtsBillRequest.Status.ACTIVE

    "paid",
    "PAID",
    -> VehicleDebtsBillRequest.Status.PAID

    "notice",
    "NOTICE",
    -> VehicleDebtsBillRequest.Status.NOTICE

    else -> throw IllegalArgumentException("Invalid payment status: $paymentStatus")
}