package ai.friday.billpayment.adapters.messaging.pix

import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.PixKeyRepository
import ai.friday.billpayment.app.pix.PixKeyAlreadyExistsException
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.RegisterPixKeyCommand
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.log.andAppend
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class RegisterPixKeyHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val pixKeyManagement: PixKeyManagement,
    private val pixKeyRepository: PixKeyRepository,
    private val deviceFingerprintService: DeviceFingerprintService,
    private val walletService: WalletService,
) : AbstractSQSHandler(
    amazonSQS,
    configuration,
    configuration.registerPixKeyQueueName,
) {
    private val logger = LoggerFactory.getLogger(RegisterPixKeyHandler::class.java)

    private val objectMapper = getObjectMapper()

    override fun handleMessage(m: Message): SQSHandlerResponse {
        val registerPixCommand: RegisterPixKeyCommand = objectMapper.readValue(m.body(), RegisterPixKeyCommand::class.java)
        val markers = append("registerPixCommand", registerPixCommand)

        val wallet = registerPixCommand.walletId.let { walletService.findWallet(it) }
        val founderAccountId = wallet.founder.accountId

        /**
         * REFACTOR: o PixKeyService.createPixKey tem um código muito próximo deste.
         *
         * Se o deviceId fictício for salvo no banco, o PixKeyService.createPixKey
         * pegaria o deviceId fictício.
         *
         * A novidade é que começaria a ser salvo no <NAME_EMAIL>.
         * E não está claro se isso é desejado. No PixKeyService, existe um questionamento
         * se deveríamos, por exemplo, remover a chave <NAME_EMAIL>.
         *
         * Ao entrar no repository, esta chave seria removida no  encerramento de conta ou de carteira.
         */
        return try {
            val createdKey = deviceFingerprintService.withRealOrTemporaryDeviceId(accountId = founderAccountId, walletId = registerPixCommand.walletId) { deviceId ->
                pixKeyManagement.registerKey(
                    accountNo = registerPixCommand.accountNo,
                    key = registerPixCommand.key,
                    document = registerPixCommand.document,
                    name = registerPixCommand.name,
                    deviceId = deviceId,
                )
            }

            if (createdKey.type == PixKeyType.EVP) {
                pixKeyRepository.create(registerPixCommand.walletId, registerPixCommand.accountNo, createdKey)
            }

            SQSHandlerResponse(true)
        } catch (e: PixKeyAlreadyExistsException) {
            logger.warn(markers.andAppend("keyAlreadyExists", "true"), "RegisterPixKeyHandler")
            SQSHandlerResponse(true)
        } catch (e: Exception) {
            logger.error(markers, "RegisterPixKeyHandler", e)
            SQSHandlerResponse(false)
        }
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        val markers = append("eventBody", m.body()).andAppend("exceptionMessage", e.message)
        logger.error(markers, "RegisterPixKeyHandler", e)
        return SQSHandlerResponse(false)
    }
}