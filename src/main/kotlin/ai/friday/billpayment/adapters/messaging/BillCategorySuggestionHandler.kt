package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.billcategory.BillCategorySuggestionService
import ai.friday.billpayment.app.billcategory.SetBillCategorySuggestionError
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@FridayMePoupe
@Requires(notEnv = ["test"])
open class BillCategorySuggestionHandler(
    amazonSQS: SqsClient,
    amazonSNS: SnsClient,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = billEventTopicArnPropertyName) topicArn: String,
    private val billCategorySuggestionService: BillCategorySuggestionService,
    private val billRepository: BillRepository,
) : AbstractSQSHandler(
    topicArn = topicArn,
    amazonSNS = amazonSNS,
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = configuration.billCategorySuggestionQueueName,
    filter = EventTypeFilter(eventTypes = listOf(BillEventType.ADD)),
) {
    private val logger = LoggerFactory.getLogger(BillCategorySuggestionHandler::class.java)

    @NewSpan
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val billAdded = message.toEventType<BillEvent>()
        val bill = Bill.build(billAdded)
        val logName = "BillCategorySuggestionHandler#handleMessage"

        val markers =
            Markers.append("billId", bill.billId.value)
                .andAppend("walletId", bill.walletId.value)

        val categorySuggestion = billCategorySuggestionService.suggestCategories(bill.toBillView())
        val filteredCategorySuggestion = billCategorySuggestionService.filterCategories(categorySuggestion)

        billCategorySuggestionService.setBillCategorySuggestions(
            billId = bill.billId,
            walletId = bill.walletId,
            categorySuggestions = filteredCategorySuggestion,
        ).mapLeft {
            logger.error(markers.andAppend("reason", it), logName)
            return when (it) {
                SetBillCategorySuggestionError.BillNotFoundException -> {
                    SQSHandlerResponse(shouldDeleteMessage = true)
                }

                SetBillCategorySuggestionError.AlreadyLockedException -> {
                    SQSHandlerResponse(shouldDeleteMessage = false)
                }
            }
        }

        logger.info(markers, logName)
        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    override fun handleError(message: Message, e: Exception): SQSHandlerResponse {
        logger.error(Markers.append("event", message.body()), "BillCategorySuggestionHandler#handleMessage", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    private fun Bill.toBillView() = billRepository.findBill(billId, walletId)
}