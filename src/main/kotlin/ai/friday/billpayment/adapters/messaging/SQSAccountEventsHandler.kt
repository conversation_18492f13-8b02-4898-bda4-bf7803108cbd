package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountEvent
import ai.friday.billpayment.app.account.DeviceBinded
import ai.friday.billpayment.app.account.MFAEnabled
import ai.friday.billpayment.app.account.MFARequired
import ai.friday.billpayment.app.account.UserCreated
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.fingerprint.RegisterDevicesResult
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getEpochMilli
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSAccountEventsHandler(
    amazonSQS: SqsClient,
    amazonSNS: SnsClient,
    val configuration: AccountMessageHandlerConfiguration,
    @Property(name = accountEventTopicArnPropertyName) private val topicArn: String,
    private val userPoolAdapter: UserPoolAdapter,
    private val eventPublisher: EventPublisher,
    private val accountRepository: AccountRepository,
    private val deviceFingerprintService: DeviceFingerprintService,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = configuration.accountEventsQueueName,
    amazonSNS = amazonSNS,
    topicArn = topicArn,
    filter = null,
) {
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val markers = append("queue_message", message.body())
        val event = parseEvent(message)

        when (event) {
            is UserCreated -> {
                if (!event.mfaEnabled) {
                    eventPublisher.publish(MFARequired(event.accountId))
                }
                logger.info(markers, "SQSAccountEventsHandler")
            }

            is MFAEnabled -> {
                logger.info(markers, "SQSAccountEventsHandler")
            }

            is MFARequired -> {
                val visibilityTimeoutInSeconds = 60 * 60 // 1 hour
                if (event.created > getEpochMilli() - (visibilityTimeoutInSeconds * 1000)) {
                    logger.info(markers, "SQSAccountEventsHandler")
                    return SQSHandlerResponse(
                        shouldDeleteMessage = false,
                        visibilityTimeoutInSeconds = visibilityTimeoutInSeconds,
                    )
                }

                val account = accountRepository.findById(event.accountId)

                userPoolAdapter.setMfaPreference(account.document, true)

                eventPublisher.publish(MFAEnabled(event.accountId))
                logger.info(markers, "SQSAccountEventsHandler")
            }

            is DeviceBinded -> {
                return when (val registerDevicesResult = deviceFingerprintService.registerDevices(event.accountId, event.deviceFingerprint)) {
                    is RegisterDevicesResult.AddDeviceFailure -> {
                        logger.error(markers.andAppend("result", registerDevicesResult.result), "SQSAccountEventsHandler", registerDevicesResult.throwable)
                        SQSHandlerResponse(shouldDeleteMessage = false)
                    }

                    RegisterDevicesResult.RegisteredDeviceNotFound -> {
                        logger.error(markers.andAppend("result", registerDevicesResult), "SQSAccountEventsHandler")
                        SQSHandlerResponse(shouldDeleteMessage = true)
                    }

                    RegisterDevicesResult.Success -> {
                        logger.info(markers.andAppend("result", registerDevicesResult), "SQSAccountEventsHandler")
                        SQSHandlerResponse(shouldDeleteMessage = true)
                    }

                    RegisterDevicesResult.Locked -> SQSHandlerResponse(shouldDeleteMessage = false)
                }
            }
        }
        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    override fun handleError(
        message: Message,
        e: Exception,
    ): SQSHandlerResponse {
        val markers = append("event", message).and<LogstashMarker>(append("exceptionMessage", e.message))
        logger.error(markers, "SQSAccountEventsHandler", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    fun parseEvent(message: Message): AccountEvent {
        return parseObjectFrom(message.body())
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSAccountEventsHandler::class.java)
    }
}

@ConfigurationProperties("sqs")
@Requires(notEnv = ["test"])
class AccountMessageHandlerConfiguration
@ConfigurationInject
constructor(
    override val sqsWaitTime: Int,
    override val sqsCoolDownTime: Int,
    override val dlqArn: String,
    override val maxReceiveCount: Int,
    override val visibilityTimeout: Int,
    override val maxNumberOfMessages: Int,
    val accountEventsQueueName: String,
) : MessageHandlerConfiguration