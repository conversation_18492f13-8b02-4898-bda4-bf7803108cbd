package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.feature.FeaturesRepository
import ai.friday.billpayment.app.integrations.EmailService
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import io.via1.communicationcentre.app.receipt.SQSEvent
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSEmailMessageHandler(
    amazonSQS: SqsClient,
    amazonSNS: SnsClient,
    @Property(name = incomingEmailsTopicArnPropertyName) private val topicArn: String,
    @Property(name = sqsArnPrefix) private val arnPrefix: String,
    configuration: SQSMessageHandlerConfiguration,
    private val emailService: EmailService,
    private val featuresRepository: FeaturesRepository,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    amazonSNS = amazonSNS,
    topicArn = topicArn,
    configuration = configuration.copy(
        maxReceiveCount = 1,
        dlqArn = arnPrefix + configuration.emailQueueName + dlqSuffix,
    ),
    queueName = configuration.emailQueueName,
) {
    override fun handleMessage(m: Message): SQSHandlerResponse {
        if (featuresRepository.getAll().maintenanceMode) {
            LOG.info(append("jobExcecutionSkipped", "maintenanceMode"), "ReceiveSQSEmailMessage")
            return SQSHandlerResponse(shouldDeleteMessage = false)
        }

        val receipt = parseObjectFrom<SQSEvent>(m.body())
        emailService.process(receipt)

        LOG.info(
            append("messageId", m.messageId()).andAppend("messageAttributes", m.messageAttributes()),
            "SQSEmailMessageHandler",
        )
        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        LOG.error("SQSEmailMessageHandler", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SQSEmailMessageHandler::class.java)
    }
}