package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.arbi.ArbiAdapter
import ai.friday.billpayment.adapters.arbi.ArbiValidationResponse
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.PaymentFailed
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.payment.ErrorSource
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSValidateBoletoJaBaixadoHandler(
    private val arbiAdapter: ArbiAdapter,
    private val updateBillService: UpdateBillService,
    private val billEventRepository: BillEventRepository,
    amazonSQS: SqsClient,
    amazonSNS: SnsClient,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = billEventTopicArnPropertyName) private val topicArn: String,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = configuration.validateBoletoJaBaixadoQueueName,
    amazonSNS = amazonSNS,
    topicArn = topicArn,
    filter = EventTypeFilter(eventTypes = listOf(BillEventType.PAYMENT_FAIL)),
) {
    @Trace
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val billType =
            message.messageAttributes()[billTypeAttributeName]?.let { BillType.valueOf(it.stringValue()) }
                ?: BillType.OTHERS
        val billEvent = parseEvent(message)
        val markers =
            append("BillType", billType)
                .andAppend("BillEvent", billEvent)

        if (shouldValidateBoletoJaBaixado(billEvent, billType)) {
            val bill =
                billEventRepository.getBillById(billEvent.billId)
                    .getOrElse { throw it }
            val validationResponse = arbiAdapter.validate(bill) as ArbiValidationResponse
            val syncResponse = updateBillService.synchronizeBill(bill, validationResponse)
            logger.info(
                markers.andAppend("paymentStatus", validationResponse.paymentStatus)
                    .andAppend("syncResponse", syncResponse),
                "SQSValidateBoletoJaBaixadoHandler",
            )
        }
        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    override fun handleError(
        message: Message,
        e: Exception,
    ): SQSHandlerResponse {
        val markers = append("event", message).and<LogstashMarker>(append("exceptionMessage", e.message))
        return when (e) {
            is ItemNotFoundException -> {
                logger.warn(markers, "SQSValidateBoletoJaBaixadoHandler", e)
                SQSHandlerResponse(shouldDeleteMessage = true)
            }

            is ClassCastException -> {
                logger.warn(markers, "SQSValidateBoletoJaBaixadoHandler", e)
                SQSHandlerResponse(shouldDeleteMessage = true)
            }

            else -> {
                logger.error(markers, "SQSValidateBoletoJaBaixadoHandler", e)
                SQSHandlerResponse(shouldDeleteMessage = false)
            }
        }
    }

    private fun shouldValidateBoletoJaBaixado(
        billEvent: BillEvent,
        billType: BillType,
    ) =
        billEvent is PaymentFailed && billType.isBoleto() && billEvent.errorSource == ErrorSource.SETTLEMENT_CELCOIN &&
            billEvent.errorDescription == "BOLETO DE PAGAMENTO JA BAIXADO"

    companion object {
        private val logger = LoggerFactory.getLogger(SQSValidateBoletoJaBaixadoHandler::class.java)
    }
}