package ai.friday.billpayment.adapters.messaging

internal const val accountIdAttributeName = "accountId"
internal const val eventTypeAttributeName = "eventType"
internal const val billTypeAttributeName = "billType"

const val billEventTopicArnPropertyName = "sns.billEventTopicArn"
internal const val sqsArnPrefix = "sqs.arnPrefix"
internal const val dlqSuffix = "-dlq"
internal const val incomingEmailsTopicArnPropertyName = "sns.incomingEmailsTopicArn"
internal const val walletEventTopicArnPropertyName = "sns.walletEventTopicArn"
internal const val accountEventTopicArnPropertyName = "sns.accountEventTopicArn"
internal const val eventBusTopicArnPropertyName = "sns.eventBusTopicArn"

internal const val scheduledBillAlreadyPaidMessage = "Conta já paga"
internal const val scheduledBillNotPayableMessage = "Conta baixada pelo emissor"

internal const val bankTransferNotPayableErrorMessage =
    "Pagamento rejeitado pela instituição do destinatário. Por favor, tente uma nova transferência corrigindo os dados."

internal const val invalidPixKeyErrorMessage = "Chave Pix inválida ou não existe."