package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.arbi.ArbiAdapter
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.PaymentFailed
import ai.friday.billpayment.app.bill.and
import ai.friday.billpayment.app.feature.FeaturesRepository
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.PixCommand
import ai.friday.billpayment.app.integrations.PixPaymentService
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.payment.BoletoSettlementResult
import ai.friday.billpayment.app.payment.MultiplePaymentData
import ai.friday.billpayment.app.payment.NoPaymentData
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithExternalPayment
import ai.friday.billpayment.app.payment.PixPaymentStatus
import ai.friday.billpayment.app.payment.SettlementFundsTransfer
import ai.friday.billpayment.app.payment.SettlementFundsTransferType
import ai.friday.billpayment.app.payment.SinglePaymentData
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.pix.PixErrorType
import ai.friday.billpayment.app.pix.PixTransactionError
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import kotlin.contracts.ExperimentalContracts
import kotlin.contracts.contract
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@ConfigurationProperties("settlementFundsTransfer")
@Requires(beans = [SQSSettlementFundsTransferHandler::class])
data class SettlementFundsTransferConfiguration @ConfigurationInject constructor(
    val payerName: String,
    val payerDocument: String,
    val recipientName: String,
    val recipientDocument: String,
    val originSettlementBankAccount: OriginSettlementBankAccount,
    val originCashinBankAccount: OriginCashinBankAccount,
    val recipientBankAccount: RecipientBankAccount,
    val description: String,
) {

    @ConfigurationProperties("recipientBankAccount")
    data class RecipientBankAccount @ConfigurationInject constructor(
        val accountType: AccountType,
        val routingNo: Long,
        val accountNo: Long,
        val accountDv: String,
        val ispb: String,
    )

    interface OriginBankAccount {
        val accountType: AccountType
        val bankNo: Long
        val routingNo: Long
        val accountNo: Long
        val accountDv: String
    }

    @ConfigurationProperties("originSettlementBankAccount")
    data class OriginSettlementBankAccount @ConfigurationInject constructor(
        override val accountType: AccountType,
        override val bankNo: Long,
        override val routingNo: Long,
        override val accountNo: Long,
        override val accountDv: String,
    ) : OriginBankAccount

    @ConfigurationProperties("originCashinBankAccount")
    data class OriginCashinBankAccount @ConfigurationInject constructor(
        override val accountType: AccountType,
        override val bankNo: Long,
        override val routingNo: Long,
        override val accountNo: Long,
        override val accountDv: String,
        val ispb: String,
    ) : OriginBankAccount
}

@Singleton
@Requires(notEnv = ["staging"])
open class SQSSettlementFundsTransferHandler(
    amazonSQS: SqsClient,
    amazonSNS: SnsClient,
    private val pixPaymentService: PixPaymentService,
    private val arbiAdapter: ArbiAdapter,
    private val billRepository: BillRepository,
    handlerConfiguration: SQSMessageHandlerConfiguration,
    private val transferConfiguration: SettlementFundsTransferConfiguration,
    @Property(name = billEventTopicArnPropertyName) private val topicArn: String,
    private val transactionRepository: TransactionRepository,
    private val deviceFingerprintService: DeviceFingerprintService,
    private val features: FeaturesRepository,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    amazonSNS = amazonSNS,
    configuration = handlerConfiguration,
    queueName = handlerConfiguration.settlementFundsTransferQueueName,
    topicArn = topicArn,
) {
    private val settlementBankAccount = InternalBankAccount(
        accountType = transferConfiguration.originSettlementBankAccount.accountType,
        bankNo = transferConfiguration.originSettlementBankAccount.bankNo,
        routingNo = transferConfiguration.originSettlementBankAccount.routingNo,
        accountNo = transferConfiguration.originSettlementBankAccount.accountNo,
        accountDv = transferConfiguration.originSettlementBankAccount.accountDv,
        document = transferConfiguration.payerDocument,
        bankAccountMode = BankAccountMode.PHYSICAL,
    )

    private val cashInBankAccount = InternalBankAccount(
        accountType = transferConfiguration.originCashinBankAccount.accountType,
        bankNo = transferConfiguration.originCashinBankAccount.bankNo,
        routingNo = transferConfiguration.originCashinBankAccount.routingNo,
        accountNo = transferConfiguration.originCashinBankAccount.accountNo,
        accountDv = transferConfiguration.originCashinBankAccount.accountDv,
        document = transferConfiguration.payerDocument,
        bankAccountMode = BankAccountMode.PHYSICAL,
    )

    @NewSpan
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val billType =
            message.messageAttributes()[billTypeAttributeName]?.let { BillType.valueOf(it.stringValue()) } ?: BillType.OTHERS
        val billEvent = parseEvent(message)

        return when {
            isBillPaidEvent(billEvent, billType) -> processBillPaidEvent(billEvent, billType)
            isPaymentFailedEvent(billEvent, billType) -> processPaymentRefundedEvent(billEvent, billType)
            else -> SQSHandlerResponse(true)
        }
    }

    private fun processPaymentRefundedEvent(billEvent: PaymentFailed, billType: BillType): SQSHandlerResponse {
        val transaction = transactionRepository.findById(billEvent.transactionId)
        if (transaction.status != TransactionStatus.UNDONE) {
            return SQSHandlerResponse(true)
        }

        val (amountTotal, transferType) = when (transaction.paymentData) {
            is MultiplePaymentData -> throw IllegalStateException("Pagamento multiplo ainda não está disponível")
            is SinglePaymentData -> when (transaction.paymentData.details) {
                is PaymentMethodsDetailWithBalance -> return SQSHandlerResponse(true)
                is PaymentMethodsDetailWithCreditCard -> transaction.paymentData.details.netAmount to SettlementFundsTransferType.REFUND_CASHIN_ACCOUNT
                is PaymentMethodsDetailWithExternalPayment -> throw IllegalStateException("Pagamento externo não pode ser liquidado")
            }

            NoPaymentData -> throw IllegalStateException("NoPaymentData não pode ser usado aqui")
        }

        val transfers = billRepository.findSettlementFundsTransfers(billId = billEvent.billId)
            .filter { it.transactionId == billEvent.transactionId && it.sourceAccountType == SettlementFundsTransferType.REFUND_CASHIN_ACCOUNT }

        return checkAndMakeTransfer(
            billId = billEvent.billId,
            walletId = billEvent.walletId,
            billType = billType,
            eventType = billEvent.eventType,
            transactionId = billEvent.transactionId,
            transfers = transfers,
            amountTotal = amountTotal,
            transferType = transferType,
        )
    }

    private fun Transaction.usesInternalSettlementAccount() =
        paymentData is SinglePaymentData &&
            paymentData.details is PaymentMethodsDetailWithBalance &&
            settlementData.getOperation<BoletoSettlementResult>().gateway == FinancialServiceGateway.FRIDAY &&
            settlementData.getOperation<BoletoSettlementResult>().finalPartnerName == FinancialServiceGateway.ARBI

    private fun processBillPaidEvent(
        billEvent: BillPaid,
        billType: BillType,
    ): SQSHandlerResponse {
        val transaction = transactionRepository.findById(billEvent.transactionId!!)

        if (transaction.usesInternalSettlementAccount()) {
            return SQSHandlerResponse(true)
        }

        val (amountTotal, transferType) = when (transaction.paymentData) {
            is MultiplePaymentData -> throw IllegalStateException("Pagamento multiplo ainda não está disponível")
            is SinglePaymentData -> when (transaction.paymentData.details) {
                is PaymentMethodsDetailWithBalance -> transaction.paymentData.details.amount to SettlementFundsTransferType.SETTLEMENT_ACCOUNT
                is PaymentMethodsDetailWithCreditCard -> transaction.paymentData.details.netAmount to SettlementFundsTransferType.CASHIN_ACCOUNT
                is PaymentMethodsDetailWithExternalPayment -> throw IllegalStateException("Pagamento externo não pode ser liquidado")
            }

            NoPaymentData -> throw IllegalStateException("NoPaymentData não pode ser usado aqui")
        }

        val transfers = billRepository.findSettlementFundsTransfers(billId = billEvent.billId)
            .filter { it.transactionId == billEvent.transactionId && it.sourceAccountType != SettlementFundsTransferType.REFUND_CASHIN_ACCOUNT }

        return checkAndMakeTransfer(
            billEvent.billId,
            billEvent.walletId,
            billType,
            billEvent.eventType,
            billEvent.transactionId,
            transfers,
            amountTotal,
            transferType,
        )
    }

    private fun checkAndMakeTransfer(
        billId: BillId,
        walletId: WalletId,
        billType: BillType,
        eventType: BillEventType,
        transactionId: TransactionId,
        transfers: List<SettlementFundsTransfer>,
        amountTotal: Long,
        transferType: SettlementFundsTransferType,
    ): SQSHandlerResponse {
        val markers = Markers.empty()
            .and(
                "billId" to billId.value,
                "walletId" to walletId.value,
                "eventType" to eventType.name,
                "billType" to billType,
                "configuration" to transferConfiguration,
                "settlementBankAccount" to settlementBankAccount,
                "cashInBankAccount" to cashInBankAccount,
                "settlementFundsTransferAmount" to amountTotal,
                "settlementFundsTransferType" to transferType,
            )

        if (transfers.isNotEmpty() && transfers.mostRecent().status == BankOperationStatus.SUCCESS) {
            logger.warn(markers.andAppend("alreadyProcessed", "true"), "SQSSettlementFundsTransferHandler")
            return SQSHandlerResponse(true)
        }

        val originBankAccount = buildOriginBankAccount(transferType)

        val recipientBankAccount = buildRecipientBankAccount(transferType, transactionId)

        val sameBank = originBankAccount.bankNo == recipientBankAccount.bankNo

        val bankOperationId = if (transfers.isEmpty() || transfers.mostRecent().status.isError()) {
            initPayment(
                originBankAccount = originBankAccount,
                recipientBankAccount = recipientBankAccount,
                billId = billId,
                transactionId = transactionId,
                amount = amountTotal,
                transferType = transferType,
            ).getOrElse {
                markers.andAppend("settlementFundsTransferStatus", it.status)
                logger.warn(markers, "SQSSettlementFundsTransferHandler")
                billRepository.saveSettlementFundsTransfer(it)
                return SQSHandlerResponse(false)
            }
        } else {
            transfers.mostRecent().bankOperationId
        }
        markers.andAppend("bankOperationId", bankOperationId.value)

        val bankOperationStatus: BankOperationStatus = if (sameBank && features.getAll().tefAtSettlementHandler) {
            checkTEF(originBankAccount = originBankAccount, bankOperationId = bankOperationId, transferType = transferType, markers = markers)
        } else {
            checkPixStatus(transferType = transferType, bankOperationId = bankOperationId, markers = markers)
        }

        val settlementFundsTransfer = SettlementFundsTransfer(
            bankOperationId = bankOperationId,
            billId = billId,
            status = bankOperationStatus,
            amount = amountTotal,
            created = getZonedDateTime().toLocalDateTime(),
            transactionId = transactionId,
            sourceAccountType = transferType,
            sameBank = sameBank,
        )
        markers.andAppend("settlementFundsTransferStatus", settlementFundsTransfer.status)
        logger.info(markers, "SQSSettlementFundsTransferHandler")
        billRepository.saveSettlementFundsTransfer(settlementFundsTransfer)

        return SQSHandlerResponse(bankOperationStatus == BankOperationStatus.SUCCESS)
    }

    private fun checkTEF(
        transferType: SettlementFundsTransferType,
        bankOperationId: BankOperationId,
        originBankAccount: InternalBankAccount,
        markers: LogstashMarker,
    ): BankOperationStatus {
        return try {
            val localDate = getLocalDate()
            val checkResult = arbiAdapter.checkTransferStatus(
                idRequisicaoParceiroOriginal = bankOperationId.value,
                startDate = localDate.minusDays(1),
                endDate = localDate.plusDays(1),
                originAccountNo = originBankAccount.buildFullAccountNumber(),
            )

            if (checkResult) {
                BankOperationStatus.SUCCESS
            } else {
                BankOperationStatus.ERROR
            }
        } catch (e: Exception) {
            logger.error(markers, "SQSSettlementFundsTransferHandler#checkTEF", e)
            BankOperationStatus.UNKNOWN
        }
    }

    private fun checkPixStatus(
        transferType: SettlementFundsTransferType,
        bankOperationId: BankOperationId,
        markers: LogstashMarker,
    ): BankOperationStatus {
        val checkResult =
            pixPaymentService.checkPaymentStatus(buildOriginBankAccount(transferType), bankOperationId)

        val bankOperationStatus = when (checkResult.status) {
            PixPaymentStatus.ACKNOWLEDGED,
            PixPaymentStatus.UNKNOWN,
            -> BankOperationStatus.UNKNOWN

            PixPaymentStatus.SUCCESS -> BankOperationStatus.SUCCESS
            PixPaymentStatus.FAILED,
            PixPaymentStatus.REFUSED,
            -> {
                if (checkResult.error == PixTransactionError.BusinessInsufficientBalance) {
                    logger.error(markers.andAppend("ACTION", "VERIFY"), "SQSSettlementFundsTransferHandler")
                    BankOperationStatus.INSUFFICIENT_FUNDS
                } else {
                    BankOperationStatus.ERROR
                }
            }

            PixPaymentStatus.SCHEDULED -> throw IllegalStateException("invalid pix payment status ${checkResult.status}")
        }
        return bankOperationStatus
    }

    @OptIn(ExperimentalContracts::class)
    private fun isBillPaidEvent(billEvent: BillEvent, billType: BillType): Boolean {
        contract {
            returns(true) implies (billEvent is BillPaid)
        }
        return billEvent is BillPaid && billEvent.eventType == BillEventType.PAID && billType.isBoleto()
    }

    @OptIn(ExperimentalContracts::class)
    private fun isPaymentFailedEvent(billEvent: BillEvent, billType: BillType): Boolean {
        contract {
            returns(true) implies (billEvent is PaymentFailed)
        }
        return billEvent is PaymentFailed && billEvent.eventType == BillEventType.PAYMENT_FAIL && billType.isBoleto()
    }

    private fun List<SettlementFundsTransfer>.mostRecent() = this.sortedBy { it.created }.last()

    private fun initPayment(
        originBankAccount: InternalBankAccount,
        recipientBankAccount: BankAccount,
        billId: BillId,
        transactionId: TransactionId,
        amount: Long,
        transferType: SettlementFundsTransferType,
    ): Either<SettlementFundsTransfer, BankOperationId> {
        val bankOperationId = BankOperationId.build()

        val sameBank = originBankAccount.bankNo == recipientBankAccount.bankNo

        val fn = if (sameBank && features.getAll().tefAtSettlementHandler) {
            this::commandTEF
        } else {
            this::commandPix
        }

        return fn(originBankAccount, recipientBankAccount, billId, amount, bankOperationId, transactionId, transferType)
    }

    private fun commandTEF(
        originBankAccount: InternalBankAccount,
        recipientBankAccount: BankAccount,
        billId: BillId,
        amount: Long,
        bankOperationId: BankOperationId,
        transactionId: TransactionId,
        transferType: SettlementFundsTransferType,
    ): Either<SettlementFundsTransfer, BankOperationId> {
        val transfer = arbiAdapter.transfer(
            originAccountNo = originBankAccount.buildFullAccountNumber(),
            targetAccountNo = recipientBankAccount.buildFullAccountNumber(),
            amount = amount,
            operationId = bankOperationId,
        )

        return when (transfer.status) {
            BankOperationStatus.SUCCESS -> bankOperationId.right()

            BankOperationStatus.INSUFFICIENT_FUNDS,
            BankOperationStatus.ERROR,
            BankOperationStatus.INVALID_DATA,
            BankOperationStatus.UNKNOWN,
            BankOperationStatus.TIMEOUT,
            -> {
                SettlementFundsTransfer(
                    bankOperationId = bankOperationId,
                    billId = billId,
                    status = transfer.status,
                    amount = amount,
                    created = getZonedDateTime().toLocalDateTime(),
                    transactionId = transactionId,
                    sourceAccountType = transferType,
                    sameBank = originBankAccount.bankNo == recipientBankAccount.bankNo,
                ).left()
            }

            BankOperationStatus.REFUNDED -> throw IllegalStateException("Unexpected transfer status: ${transfer.status}")
        }
    }

    private fun commandPix(
        originBankAccount: InternalBankAccount,
        recipientBankAccount: BankAccount,
        billId: BillId,
        amount: Long,
        bankOperationId: BankOperationId,
        transactionId: TransactionId,
        transferType: SettlementFundsTransferType,
    ): Either<SettlementFundsTransfer, BankOperationId> {
        val pixCommand = PixCommand(
            payerName = transferConfiguration.payerName,
            originBankAccount = originBankAccount,
            recipientName = transferConfiguration.recipientName,
            recipientDocument = transferConfiguration.recipientDocument,
            recipientBankAccount = recipientBankAccount,
            recipientPixKey = null,
            description = "${transferConfiguration.description} - ${billId.value}",
            amount = amount,
            pixQrCodeId = null,
        )

        logger.info(
            Markers.append("originBankAccount", originBankAccount)
                .andAppend("pixCommand", pixCommand),
            "SQSSettlementFundsTransferHandler",
        )
        val deviceId = deviceFingerprintService.getInternalDevicesAccount()?.deviceIds?.get(AccountNumber(originBankAccount.buildFullAccountNumber()))
        val initResult = pixPaymentService.initPayment(command = pixCommand, bankOperationId = bankOperationId, deviceId = deviceId)

        if (initResult.status != PixPaymentStatus.ACKNOWLEDGED && initResult.status != PixPaymentStatus.UNKNOWN) {
            val bankOperationStatus = if (initResult.error?.type == PixErrorType.TEMPORARY) {
                BankOperationStatus.ERROR
            } else {
                BankOperationStatus.INVALID_DATA
            }

            return SettlementFundsTransfer(
                bankOperationId = bankOperationId,
                billId = billId,
                status = bankOperationStatus,
                amount = amount,
                created = getZonedDateTime().toLocalDateTime(),
                transactionId = transactionId,
                sourceAccountType = transferType,
                sameBank = originBankAccount.bankNo == recipientBankAccount.bankNo,
            ).left()
        }

        return bankOperationId.right()
    }

    private fun buildOriginBankAccount(sourceAccountType: SettlementFundsTransferType): InternalBankAccount {
        return when (sourceAccountType) {
            SettlementFundsTransferType.SETTLEMENT_ACCOUNT -> settlementBankAccount
            SettlementFundsTransferType.CASHIN_ACCOUNT -> cashInBankAccount
            SettlementFundsTransferType.REFUND_CASHIN_ACCOUNT -> settlementBankAccount
        }
    }

    private fun buildRecipientBankAccount(sourceAccountType: SettlementFundsTransferType, transactionId: TransactionId): BankAccount {
        val celcoinRecipientAccount = BankAccount(
            accountType = transferConfiguration.recipientBankAccount.accountType,
            bankNo = null,
            routingNo = transferConfiguration.recipientBankAccount.routingNo,
            accountNo = transferConfiguration.recipientBankAccount.accountNo.toBigInteger(),
            accountDv = transferConfiguration.recipientBankAccount.accountDv,
            document = transferConfiguration.recipientDocument,
            ispb = transferConfiguration.recipientBankAccount.ispb,
        )

        val cashInRecipientAccount = BankAccount(
            accountType = cashInBankAccount.accountType,
            bankNo = cashInBankAccount.bankNo,
            routingNo = cashInBankAccount.routingNo,
            accountNo = cashInBankAccount.accountNo.toBigInteger(),
            accountDv = cashInBankAccount.accountDv,
            document = cashInBankAccount.document,
            ispb = transferConfiguration.originCashinBankAccount.ispb,
        )

        val settlementRecipientAccount = BankAccount(
            accountType = transferConfiguration.originSettlementBankAccount.accountType,
            bankNo = transferConfiguration.originSettlementBankAccount.bankNo,
            routingNo = transferConfiguration.originSettlementBankAccount.routingNo,
            accountNo = transferConfiguration.originSettlementBankAccount.accountNo.toBigInteger(),
            accountDv = transferConfiguration.originSettlementBankAccount.accountDv,
            document = transferConfiguration.recipientDocument,
            ispb = transferConfiguration.originCashinBankAccount.ispb,
        )

        return when (sourceAccountType) {
            SettlementFundsTransferType.SETTLEMENT_ACCOUNT -> celcoinRecipientAccount
            SettlementFundsTransferType.CASHIN_ACCOUNT -> {
                val transaction = transactionRepository.findById(transactionId)

                if ((transaction.settlementData.settlementOperation as? BoletoSettlementResult)?.finalPartnerName == FinancialServiceGateway.ARBI) {
                    settlementRecipientAccount
                } else {
                    celcoinRecipientAccount
                }
            }

            SettlementFundsTransferType.REFUND_CASHIN_ACCOUNT -> cashInRecipientAccount
        }
    }

    override fun handleError(message: Message, e: Exception): SQSHandlerResponse {
        logger.error(Markers.append("event", message), "SQSSettlementFundsTransferHandler", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSSettlementFundsTransferHandler::class.java)
    }
}