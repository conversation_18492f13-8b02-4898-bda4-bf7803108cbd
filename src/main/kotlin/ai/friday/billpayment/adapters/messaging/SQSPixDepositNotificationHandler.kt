package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.banking.SynchronizeBankAccountTO
import ai.friday.morning.log.andAppend
import ai.friday.morning.messaging.MessageHandler
import ai.friday.morning.messaging.MessageHandlerResponse
import ai.friday.morning.messaging.receiveCount
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

@Handler
@Singleton
open class SQSPixDepositNotificationHandler(
    private val internalBankService: InternalBankService,
    @Property(name = "friday.morning.messaging.consumer.pix-deposit-notification.maxReceiveCount") private val maxRetries: Int,
) : MessageHandler {

    override val configurationName = "pix-deposit-notification"

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun handleMessage(m: Message): MessageHandlerResponse {
        val message = parseObjectFrom<SynchronizeBankAccountTO>(m.body())
        val marker = Markers.append("body", message)

        logger.info(marker, "SQSPixDepositNotificationHandler")

        val synchronizedAnyStatement =
            internalBankService.synchronizeBankAccount(
                bankNo = message.bankNo,
                routingNo = message.routingNo,
                accountNumber = AccountNumber(message.fullAccountNumber),
                withPixFallBack = true,
            )

        val receiveCount = m.receiveCount()
        marker.andAppend("synchronizedAnyStatement", synchronizedAnyStatement)
            .andAppend("receiveCount", receiveCount)

        logger.info(marker, "SQSPixDepositNotificationHandler")

        if (!synchronizedAnyStatement && receiveCount >= maxRetries) {
            logger.error(marker, "SQSPixDepositNotificationHandler#MaxRetriesExceeded")
            return MessageHandlerResponse.delete()
        }
        return MessageHandlerResponse.build(synchronizedAnyStatement)
    }

    override fun handleException(
        e: Exception,
    ): MessageHandlerResponse {
        logger.error(Markers.empty(), "SQSPixDepositNotificationHandler", e)
        return MessageHandlerResponse.keep()
    }
}