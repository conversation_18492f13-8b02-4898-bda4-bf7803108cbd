package ai.friday.billpayment.adapters.messaging

import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import software.amazon.awssdk.core.client.config.ClientOverrideConfiguration
import software.amazon.awssdk.core.retry.RetryPolicy
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.sns.SnsClient

@Factory
class SNSFactory(@Property(name = "sns.region") private val snsRegion: String) {

    @Singleton
    fun snsFactory(): SnsClient =
        SnsClient.builder()
            .region(Region.of(snsRegion))
            .overrideConfiguration(
                ClientOverrideConfiguration.builder()
                    .retryPolicy(RetryPolicy.builder().numRetries(25).build()).build(),
            )
            .build()
}