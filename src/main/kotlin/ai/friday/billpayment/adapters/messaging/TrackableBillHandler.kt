package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.and
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.tracking.TrackableBillHandlerService
import ai.friday.billpayment.app.bill.tracking.UntrackableEntity
import ai.friday.billpayment.log
import arrow.core.getOrElse
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class TrackableBillHandler(
    amazonSQS: SqsClient,
    amazonSNS: SnsClient,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = billEventTopicArnPropertyName) private val topicArn: String,
    private val service: TrackableBillHandlerService,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = configuration.trackableBillQueueName,
    amazonSNS = amazonSNS,
    topicArn = topicArn,
    filter =
    EventTypeFilter(
        eventTypes =
        listOf(
            BillEventType.ADD,
            BillEventType.PAYMENT_FAIL,
            BillEventType.PAYMENT_REFUNDED,
            BillEventType.PAID,
            BillEventType.REACTIVATED,
            BillEventType.MOVED,
            BillEventType.IGNORED,
            BillEventType.REGISTER_UPDATED,
            BillEventType.MARKED_AS_PAID,
            BillEventType.CANCELED_MARKED_AS_PAID,
        ),
    ),
) {
    private val logger = LoggerFactory.getLogger(TrackableBillHandler::class.java)

    override fun handleMessage(message: Message): SQSHandlerResponse {
        val event = parseEvent(message)

        val markers =
            with(event) {
                log("billId" to billId.value, "wallet_id" to walletId.value, "event_type" to eventType)
            }

        service.execute(event).getOrElse {
            val hasToDelete =
                when (it) {
                    is UntrackableEntity -> true
                    else -> {
                        logger.error(
                            markers.and("error_message" to it.message),
                            "TrackableBillHandler#handleMessage",
                            it.ex,
                        )
                        false
                    }
                }
            return SQSHandlerResponse(hasToDelete)
        }
        logger.info(markers, "TrackableBillHandler#handleMessage")

        return SQSHandlerResponse(true)
    }

    override fun handleError(
        message: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(log("event" to message), "TrackableBillHandler#handleError", e)

        return SQSHandlerResponse(shouldDeleteMessage = false)
    }
}