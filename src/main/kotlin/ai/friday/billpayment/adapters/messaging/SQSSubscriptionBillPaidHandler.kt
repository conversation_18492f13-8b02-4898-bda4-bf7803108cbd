package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.subscription.SubscriptionPaymentStatus
import ai.friday.billpayment.app.subscription.SubscriptionService
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.context.annotation.Property
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSSubscriptionBillPaidHandler(
    amazonSQS: SqsClient,
    amazonSNS: SnsClient,
    private val billRepository: BillRepository,
    private val subscriptionService: SubscriptionService,
    private val accountService: AccountService,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = billEventTopicArnPropertyName) private val topicArn: String,
) : AbstractSQSHandler(
    amazonSQS,
    configuration,
    configuration.subscriptionBillPaidQueueName,
    amazonSNS,
    topicArn,
    filter =
    EventTypeFilter(
        eventTypes =
        listOf(
            BillEventType.PAID,
        ),
    ),
) {
    @NewSpan
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val billEvent = parseEvent(m)
        if (billEvent !is BillPaid || billEvent.actionSource is ActionSource.OpenFinance) {
            return SQSHandlerResponse(true)
        }
        val markers =
            Markers.append("billId", billEvent.billId.value)
                .andAppend("walletId", billEvent.walletId.value)

        val paidBill =
            try {
                billRepository.findBill(billId = billEvent.billId, walletId = billEvent.walletId)
            } catch (e: IllegalStateException) {
                LOG.error(markers, "SQSSubscriptionBillPaidHandler")
                return SQSHandlerResponse(false)
            }
        markers.andAppend("subscriptionFee", paidBill.subscriptionFee)

        markers.andAppend("subscriptionBillType", paidBill.billType.name)
        if (paidBill.subscriptionFee) {
            if (paidBill.source is ActionSource.SubscriptionRecurrence) {
                subscriptionService.handleSubscriptionPayment(paidBill.source.accountId).getOrElse {
                    LOG.error(markers.andAppend("handleSubscriptionPaymentError", it), "SQSSubscriptionBillPaidHandler")
                    return SQSHandlerResponse(false)
                }
                markers.andAppend("subscriptionPaymentStatus", SubscriptionPaymentStatus.PAID)
            }
            LOG.info(markers, "SQSSubscriptionBillPaidHandler")
        }
        return SQSHandlerResponse(true)
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        val markers = append("eventBody", m.body()).andAppend("exceptionMessage", e.message)
        LOG.error(markers, "SQSSubscriptionBillPaidHandler", e)
        return SQSHandlerResponse(false)
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SQSSubscriptionBillPaidHandler::class.java)
    }
}