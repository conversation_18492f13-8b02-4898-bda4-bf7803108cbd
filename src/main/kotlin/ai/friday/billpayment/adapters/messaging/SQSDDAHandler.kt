package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.dda.DDAExecutionResult
import ai.friday.billpayment.app.dda.DDAItem
import ai.friday.billpayment.app.dda.DDAProvider
import ai.friday.billpayment.app.dda.DDARegisterMessage
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.dda.DDAStatus
import ai.friday.morning.log.andAppend
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import parallelMap
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requires(notEnv = ["staging"])
open class SQSDDAHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val ddaService: DDAService,
) : AbstractSQSHandler(amazonSQS, configuration, configuration.ddaQueueName) {
    @Trace
    @NewSpan
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val ddaRegisterMessage = parseObjectFrom<DDARegisterMessage>(m.body())

        val markers =
            append("messageId", m.messageId())
                .andAppend("messageAttributes", m.messageAttributes())
                .andAppend("accountId", ddaRegisterMessage.accountId.value)
                .andAppend("document", ddaRegisterMessage.document)

        val ddaRegister = ddaService.findDDARegister(Document(ddaRegisterMessage.document))

        markers.andAppend("ddaRegister", ddaRegister)

        if (ddaRegister?.status != DDAStatus.ACTIVE || ddaRegister.provider != DDAProvider.ARBI) {
            logger.warn(markers, "ReceiveSQSDDAHandler")
            return SQSHandlerResponse(shouldDeleteMessage = true)
        }

        val bills = ddaService.getBills(ddaRegister)

        runBlocking(Dispatchers.IO) {
            val result = bills.parallelMap { addBill(it) }.groupingBy { it.toString() }.eachCount()

            logger.info(
                markers.andAppend("billsTotal", bills.size)
                    .andAppend("result", result),
                "ReceiveSQSDDAHandler",
            )
        }

        ddaService.updateLastExecutionSuccessful(ddaRegisterMessage.accountId)

        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(append("event", m), "ReceiveSQSDDAHandler", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    @NewSpan
    open fun addBill(ddaItem: DDAItem): DDAExecutionResult {
        return try {
            when (val result: DDAExecutionResult = ddaService.executeBill(ddaItem)) {
                is DDAExecutionResult.Error,
                DDAExecutionResult.AccountNotFound,
                -> {
                    logResult(ddaItem, result)
                    result
                }

                else -> result
            }
        } catch (e: Exception) {
            logger.error(
                append("DDAExecutionResult", "Exception").andAppend("bill", ddaItem),
                "ReceiveSQSDDAHandlerAddBill",
                e,
            )
            DDAExecutionResult.Error(e)
        }
    }

    private fun logResult(
        ddaItem: DDAItem,
        result: DDAExecutionResult,
    ) {
        val resultLabel =
            when (result) {
                is DDAExecutionResult.Error -> "Error"
                DDAExecutionResult.AccountNotFound -> "AccountNotFound"
                else -> ""
            }
        logger.warn(
            append("DDAExecutionResult", resultLabel).andAppend("bill", ddaItem),
            "ReceiveSQSDDAHandlerAddBill",
        )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSDDAHandler::class.java)
    }
}