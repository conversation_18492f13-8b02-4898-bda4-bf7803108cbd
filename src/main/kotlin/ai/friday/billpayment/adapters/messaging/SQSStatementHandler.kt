package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.statement.RequestStatementMessageTO
import ai.friday.billpayment.app.statement.StatementError
import ai.friday.billpayment.app.statement.StatementService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSStatementHandler(
    sqsClient: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val statementService: StatementService,
) :
    AbstractSQSHandler(
        amazonSQS = sqsClient,
        configuration = configuration,
        queueName = configuration.statementQueueName,
    ) {
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val message = parseObjectFrom<RequestStatementMessageTO>(m.body())
        val parsedStartDate = LocalDate.parse(message.startDate, DateTimeFormatter.ISO_DATE)
        val parsedEndDate = LocalDate.parse(message.endDate, DateTimeFormatter.ISO_DATE)

        val markers =
            Markers.append("eventMessage", m.body())
                .andAppend("accountId", message.accountId)
                .andAppend("walletId", message.walletId)

        val statement =
            statementService.sendStatementByEmail(
                accountId = AccountId(message.accountId),
                walletId = WalletId(message.walletId),
                startDate = parsedStartDate,
                endDate = parsedEndDate,
            ).getOrElse {
                LOG.error(markers.andAppend("errorMessage", it.message), "SQSStatementHandler", it)

                return when (it) {
                    is StatementError.NotAllowed,
                    is StatementError.WalletNotFound,
                    -> SQSHandlerResponse(true)

                    is StatementError.FailedToSendEmail,
                    is StatementError.ServerError,
                    is StatementError.AccountMissingPermissionError,
                    -> SQSHandlerResponse(false)
                }
            }
        if (statement.statementItems.isNotEmpty()) {
            if (statement.statementItems.last().balance != statement.finalBalance) {
                LOG.warn(
                    markers
                        .andAppend("startDate", statement.startDate.format(DateTimeFormatter.ISO_DATE))
                        .andAppend("endDate", statement.endDate.format(DateTimeFormatter.ISO_DATE))
                        .andAppend("statement.statementItems.last().balance", statement.statementItems.last().balance.amount)
                        .andAppend("statement.finalBalance", statement.finalBalance.amount),
                    "SQSStatementHandler#BalanceMismatch",
                )
            }
        }
        LOG.info(markers, "SQSStatementHandler")
        return SQSHandlerResponse(true)
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        LOG.error(Markers.append("eventMessage", m.body()).andAppend("errorMessage", e.message), "SQSStatementHandler", e)
        return SQSHandlerResponse(false)
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SQSStatementHandler::class.java)
    }
}