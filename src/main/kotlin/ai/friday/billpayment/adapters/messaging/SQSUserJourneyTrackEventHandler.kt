package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.journey.UserJourneyEvent
import ai.friday.billpayment.app.journey.UserJourneyService
import ai.friday.billpayment.app.wallet.WalletService
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSUserJourneyTrackEventHandler(
    amazonSQS: SqsClient,
    amazonSNS: SnsClient,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = billEventTopicArnPropertyName) private val topicArn: String,
    private val userJourneyService: UserJourneyService,
    private val walletService: WalletService,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = configuration.userJourneyQueueName,
    amazonSNS = amazonSNS,
    topicArn = topicArn,
    filter = EventTypeFilter(eventTypes = listOf(BillEventType.ADD)),
) {
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val event = parseEvent(m)

        if (event.actionSource !is ActionSource.WalletMailBox) {
            return SQSHandlerResponse(shouldDeleteMessage = true)
        }

        val actionSource = event.actionSource as ActionSource.WalletMailBox

        val accountId = actionSource.accountId ?: walletService.findWallet(event.walletId).founder.accountId

        userJourneyService.trackEventAsync(accountId, UserJourneyEvent.SendBillByEmail)
        logger.info(append("accountId", actionSource.accountId), "UserJourneyTrackEventHandler")

        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(append("queue_message", m), "UserJourneyTrackEventHandler", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSUserJourneyTrackEventHandler::class.java)
    }
}