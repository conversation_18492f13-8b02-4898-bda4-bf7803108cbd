package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.log
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getEpochMilli
import kotlin.time.DurationUnit
import kotlin.time.toDuration
import software.amazon.awssdk.services.sqs.model.Message
import software.amazon.awssdk.services.sqs.model.MessageSystemAttributeName

fun Message.getCreatedAtInMillis(): Long? =
    attributes()[MessageSystemAttributeName.APPROXIMATE_FIRST_RECEIVE_TIMESTAMP]?.toLong()

fun Message.getAttempts(): Long? =
    attributes()[MessageSystemAttributeName.APPROXIMATE_RECEIVE_COUNT]?.toLong()

fun Message.getAgeInMinutes(): Long? = getCreatedAtInMillis()?.let { createdAt ->
    (getEpochMilli() - createdAt)
        .toDuration(DurationUnit.MILLISECONDS)
        .inWholeMinutes
}

fun Message.markers() = log(
    "eventBody" to body(),
    "receiveCount" to getAttempts(),
    "createdAt" to getCreatedAtInMillis(),
    "ageInMinutes" to getAgeInMinutes(),
)