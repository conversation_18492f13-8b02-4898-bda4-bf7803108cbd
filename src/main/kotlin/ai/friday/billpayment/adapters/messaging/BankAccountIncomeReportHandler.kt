package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.BankAccountIncomeReportService
import ai.friday.billpayment.app.banking.IncomeReportQueueMessage
import ai.friday.morning.log.andAppend
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import java.time.Year
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class BankAccountIncomeReportHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val bankAccountIncomeReportService: BankAccountIncomeReportService,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = configuration.incomeReport,
    cronExpressionString = "* * * 2-4 *", // Esse envio acontece entre Fevereiro e Março apenas.
    cronToleranceInSeconds = 60,
    consumers = 3,
) {

    @NewSpan
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val parsedMessage = getObjectMapper().readValue(message.body(), IncomeReportQueueMessage::class.java)
        bankAccountIncomeReportService.generateAndSendIncomeReport(AccountId(parsedMessage.accountId), Year.of(parsedMessage.year), parsedMessage.resend, parsedMessage.recreate)
        logger.info(Markers.append("accountId", parsedMessage.accountId).andAppend("year", parsedMessage.year).andAppend("resend", parsedMessage.resend).andAppend("recreate", parsedMessage.recreate), "BankAccountIncomeReportHandler")
        return SQSHandlerResponse(true)
    }

    override fun handleError(
        message: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(Markers.append("messageBody", message.body()), "BankAccountIncomeReportHandler", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    private val logger = LoggerFactory.getLogger(BankAccountIncomeReportHandler::class.java)
}