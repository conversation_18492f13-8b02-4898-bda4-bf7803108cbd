package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.register.SimpleSignUpPendingMessage
import ai.friday.billpayment.app.register.SimpleSignUpService
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SimpleSignUpHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val simpleSignUpService: SimpleSignUpService,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = configuration.simpleSignUpQueueName,
) {

    override fun handleMessage(message: Message): SQSHandlerResponse {
        val markers = Markers.append("message", message.body())
        val event = parseObjectFrom<SimpleSignUpPendingMessage>(message.body())

        simpleSignUpService.continueSignUp(event).getOrElse {
            throw it
        }
        logger.info(markers.andAppend("parsedMessage", event), "SimpleSignUpHandler")

        return SQSHandlerResponse(true)
    }

    override fun handleError(message: Message, e: Exception): SQSHandlerResponse {
        logger.error(Markers.append("message", message), "SimpleSignUpHandler", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SimpleSignUpHandler::class.java)
    }
}