package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.isOwnTrustedBill
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import java.time.Instant
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSSystemActivityBillPaidHandler(
    amazonSQS: SqsClient,
    amazonSNS: SnsClient,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = billEventTopicArnPropertyName) private val topicArn: String,
    @Property(name = "sqs.queues.systemActivityBillPaidHandler") private val queueName: String,
    private val systemActivityService: SystemActivityService,
    private val billRepository: BillRepository,
    private val accountService: AccountService,
) : AbstractSQSHandler(
    amazonSQS,
    configuration,
    queueName,
    amazonSNS,
    topicArn,
    filter =
    EventTypeFilter(
        eventTypes =
        listOf(
            BillEventType.PAID,
        ),
    ),
) {
    @NewSpan
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val billEvent = parseEvent(m)
        if (billEvent !is BillPaid || billEvent.actionSource is ActionSource.OpenFinance) {
            return SQSHandlerResponse(true)
        }
        val markers =
            Markers.append("billId", billEvent.billId.value)
                .andAppend("walletId", billEvent.walletId.value)

        val bill = billRepository.findBill(billEvent.billId, billEvent.walletId)

        if (isOwnTrustedBill(bill)) {
            systemActivityService.setFirstOwnTrustedBillPaid(
                bill.source.accountId()!!,
                ZonedDateTime.ofInstant(Instant.ofEpochMilli(billEvent.created), brazilTimeZone),
            )
            markers.andAppend("isOwnTrustedBill", true)
        }

        LOG.info(markers, "SQSSystemActivityBillPaidHandler")
        return SQSHandlerResponse(true)
    }

    private fun ActionSource.accountId(): AccountId? {
        return if (this is ActionSource.WalletActionSource) {
            this.accountId
        } else {
            null
        }
    }

    private fun isOwnTrustedBill(billView: BillView): Boolean {
        val accountId = billView.source.accountId() ?: return false
        val account = accountService.findAccountById(accountId)

        return billView.isOwnTrustedBill(account.document)
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        val markers = append("eventBody", m.body()).andAppend("exceptionMessage", e.message)
        LOG.error(markers, "SQSSystemActivityBillPaidHandler", e)
        return SQSHandlerResponse(false)
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SQSSystemActivityBillPaidHandler::class.java)
    }
}