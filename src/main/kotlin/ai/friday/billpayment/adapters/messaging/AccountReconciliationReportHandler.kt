package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.PaymentMethodNotFound
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.reports.AccountReconciliationReportQueueMessageTO
import ai.friday.billpayment.app.reports.BankAccountReconciliationReportService
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import ai.friday.morning.messaging.MessageHandler
import ai.friday.morning.messaging.MessageHandlerResponse
import jakarta.inject.Singleton
import java.time.LocalDate
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

@Handler
@Singleton
open class AccountReconciliationReportHandler(
    private val bankAccountReconciliationReportService: BankAccountReconciliationReportService,
    private val accountRepository: AccountRepository,
) : MessageHandler {
    private val logger = LoggerFactory.getLogger(AccountReconciliationReportHandler::class.java)

    override val configurationName = "account-reconciliation-report"

    override fun handleException(e: Exception) = MessageHandlerResponse.keep()

    override fun handleMessage(message: Message): MessageHandlerResponse {
        val markers = Markers.empty()
        val logName = "AccountReconciliationReportHandler#handleMessage"

        val messageBody = parseObjectFrom<AccountReconciliationReportQueueMessageTO>(message.body())
        markers.andAppend("messageBody", messageBody)

        val date = LocalDate.parse(messageBody.date, dateFormat)

        val accountId = AccountId(messageBody.accountId)
        val paymentMethodId = AccountPaymentMethodId(messageBody.accountPaymentMethodId)

        val accountPaymentMethod =
            try {
                accountRepository.findAccountPaymentMethodByIdAndAccountId(accountPaymentMethodId = paymentMethodId, accountId = accountId)
            } catch (e: PaymentMethodNotFound) {
                logger.error(markers, logName, e)
                return MessageHandlerResponse.keep()
            }

        val report = bankAccountReconciliationReportService.generateArbiAccountsReport(
            date = date,
            cutOffTime = messageBody.cutOffTime,
            accountPaymentMethod = accountPaymentMethod,
            previouslyCheckedTimes = messageBody.retry,
        )
        markers.andAppend("report", report)

        val hasDivergence = report.divergent
        markers.andAppend("hasDivergence", hasDivergence)

        val republished = if (hasDivergence) {
            bankAccountReconciliationReportService.retryGenerateArbiAccountsReportAsync(
                date = date,
                cutOffTime = messageBody.cutOffTime,
                accountPaymentMethod = accountPaymentMethod,
                previouslyCheckedTimes = messageBody.retry,
            )
        } else {
            false
        }
        markers.andAppend("republished", republished)
        logger.info(markers, logName)
        return MessageHandlerResponse.build(shouldDeleteMessage = true)
    }
}