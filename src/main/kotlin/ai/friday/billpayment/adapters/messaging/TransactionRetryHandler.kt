package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.and
import ai.friday.billpayment.app.cashIn.CashInExecutor
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.message.QueueMessage
import ai.friday.billpayment.app.payment.RetryForeverTransactionException
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.transaction.RetryTransaction
import ai.friday.morning.log.andAppend
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

const val retryTransactionEventType = "retryTransaction"
const val retryCashinTransactionEventType = "retryCashinTransaction"

@Singleton
open class TransactionRetryHandler(
    amazonSQS: SqsClient,
    private val configuration: SQSMessageHandlerConfiguration,
    private val retryTransaction: RetryTransaction,
    private val cashInExecutor: CashInExecutor,
    private val messagePublisher: MessagePublisher,
    private val transactionRepository: TransactionRepository,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = configuration.rollbackTransactionQueueName,
) {
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val transactionMessage = jacksonObjectMapper().readValue(message.body()) as Map<String, String>
        val transactionId =
            TransactionId(
                transactionMessage["transactionId"]
                    ?: throw IllegalArgumentException(),
            )
        val markers = append("transactionId", transactionId.value)

        val eventType = transactionMessage["eventType"] ?: throw IllegalArgumentException()
        markers.andAppend("eventType", eventType)

        val shouldDeleteMessage = retryTransaction(eventType, transactionId).fold(
            onFailure = { failure ->
                logger.error(markers, "TransactionRetryHandler", failure)
                markers.and("failed" to true, "error" to failure.message)
                if (failure is RetryForeverTransactionException) {
                    val delay = failure.retryStrategy.calculateDelay {
                        val transaction = transactionRepository.findById(transactionId)
                        transaction.created
                    }

                    messagePublisher.sendMessage(
                        QueueMessage(
                            queueName = configuration.rollbackTransactionQueueName,
                            jsonObject = getObjectMapper().writeValueAsString(transactionMessage),
                            delaySeconds = delay.toSeconds().toInt(),
                        ),
                    )
                    markers.andAppend("republished", true).andAppend("delaySeconds", delay.toSeconds().toInt())
                    true
                } else {
                    false
                }
            },
            onSuccess = {
                true
            },
        )

        markers.andAppend("shouldDeleteMessage", shouldDeleteMessage)
        logger.info(markers, "TransactionRetryHandler")
        return SQSHandlerResponse(shouldDeleteMessage = shouldDeleteMessage)
    }

    override fun handleError(
        message: Message,
        e: Exception,
    ): SQSHandlerResponse {
        val markers = append("messageBody", message.body())
        try {
            val transactionMessage = jacksonObjectMapper().readValue(message.body()) as Map<String, String>
            markers.andAppend("transactionId", transactionMessage["transactionId"])
        } catch (e: Exception) {
            markers.andAppend("context", "falha ao ler a mensagem")
        }
        logger.error(markers, "TransactionRetryHandler", e)
        return SQSHandlerResponse(false)
    }

    @NewSpan
    open fun retryTransaction(
        eventType: String,
        transactionId: TransactionId,
    ): Result<Unit> {
        return when (eventType) {
            retryTransactionEventType -> retryTransaction.retryTransaction(transactionId)

            retryCashinTransactionEventType -> cashInExecutor.retryTransaction(transactionId)

            else -> throw IllegalStateException("Transaction event type does not exist. EventType:$eventType TransactionId:$transactionId")
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(TransactionRetryHandler::class.java)
    }
}