package ai.friday.billpayment.adapters.messaging.sns

import ai.friday.billpayment.markers
import ai.friday.billpayment.plus
import io.micronaut.context.annotation.Context
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Requires
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sns.model.CreateTopicRequest

@EachProperty("sns.topics")
interface TopicConfiguration {
    val name: String
}

@Requires(notEnv = ["test"], property = "features.createTopic", value = "true", defaultValue = "false")
@Context
internal class TopicCreator(
    private val snsClient: SnsClient,
    private val topics: Map<String, TopicConfiguration>,
) {
    private val logger = LoggerFactory.getLogger(TopicCreator::class.java)

    init {
        createTopics()
    }

    private fun createTopics() {
        logger.info(markers("topicsConfiguration" to topics), "Creating SNS topics")

        topics.forEach { (_, config) ->
            val request = CreateTopicRequest.builder()
                .name(config.name)
                .build()

            val markers = markers("topicName" to config.name)

            val response = snsClient.createTopic(request)

            markers.plus(
                "response.arn" to response.topicArn(),
                "response.status" to response.sdkHttpResponse().statusCode(),
            )

            logger.info(markers, "TopicCreator")
        }
    }
}