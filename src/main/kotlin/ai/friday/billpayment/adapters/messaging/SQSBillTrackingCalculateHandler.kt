package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.ServerError
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.and
import ai.friday.billpayment.app.bill.BillTrackingCalculateOptions
import ai.friday.billpayment.app.bill.TrackableBill
import ai.friday.billpayment.app.bill.tracking.BillTrackingCalculateHandlerService
import ai.friday.billpayment.app.bill.tracking.SyncRetryable
import ai.friday.billpayment.app.bill.tracking.UnableToSync
import ai.friday.billpayment.measure
import arrow.core.getOrElse
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Property
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSBillTrackingCalculateHandler(
    @Property(name = "sqs.bill-tracking.calculate") queue: String,
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val service: BillTrackingCalculateHandlerService,
) : AbstractSQSHandler(amazonSQS, configuration, queue) {
    @Trace
    @NewSpan
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val body = message.body()
        logger.debug(Markers.append("eventBody", body), "SQSBillTrackingCalculateHandler#handleMessage")
        val markers = Markers.empty()

        val trackableBill = parseObjectFrom<TrackableBill>(body)

        markers.and("digitableNumber" to trackableBill.barCode.digitable, "billId" to trackableBill.billId.value)

        val (result, elapsed) = measure { service.execute(trackableBill, BillTrackingCalculateOptions.CALCULATE) }

        markers.and("elapsed_time" to elapsed)

        val calculationResult =
            result.getOrElse {
                val shouldDelete =
                    when (it) {
                        is UnableToSync -> true
                        is ServerError, is SyncRetryable -> false
                        else -> true
                    }

                logger.error(markers.and("error" to it), "SQSBillTrackingCalculateHandler#handleMessage")

                return SQSHandlerResponse(shouldDeleteMessage = shouldDelete)
            }

        logger.info(markers.and("result" to calculationResult), "SQSBillTrackingCalculateHandler#handleMessage")

        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    override fun handleError(
        message: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(Markers.append("event", message), "SQSBillTrackingCalculateHandler#handleError", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSBillTrackingCalculateHandler::class.java)
    }
}