package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.integrations.PixPaymentService
import ai.friday.billpayment.app.onepixpay.OnePixPayId
import ai.friday.billpayment.app.onepixpay.OnePixPayIdSource
import ai.friday.billpayment.app.onepixpay.OnePixPayIdType
import ai.friday.billpayment.app.onepixpay.OnePixPayRequestedMessage
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.onepixpay.OnePixPayStatus
import ai.friday.billpayment.app.pix.PixStatementItem
import ai.friday.morning.json.parseObjectFrom
import ai.friday.morning.log.andAppend
import ai.friday.morning.messaging.MessageHandler
import ai.friday.morning.messaging.MessageHandlerResponse
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

@Handler
@Singleton
open class SQSOnePixPayRequestedHandler(
    private val onePixPayService: OnePixPayService,
    private val pixPaymentService: PixPaymentService,
) : MessageHandler {
    private val logger = LoggerFactory.getLogger(SQSOnePixPayRequestedHandler::class.java)

    override val configurationName = "one-pix-pay-requested"

    override fun handleMessage(message: Message): MessageHandlerResponse {
        val logName = "SQSOnePixPayRequestedHandler"
        val markers = Markers.empty()

        val onePixPayRequestedMessage = parseObjectFrom<OnePixPayRequestedMessage>(message.body())
        markers.andAppend("onePixPayRequestedMessage", onePixPayRequestedMessage)

        val onePixPayId = OnePixPayIdSource.fromIdValue(onePixPayRequestedMessage.onePixPayId).toOnePixPayId()
        markers.andAppend("onePixPayId", onePixPayId)

        val onePixPay = onePixPayService.find(onePixPayId)
        markers.andAppend("onePixPay", onePixPay)

        val shouldDelete = if (onePixPay.status == OnePixPayStatus.REQUESTED) {
            val statement = pixPaymentService.getStatement(AccountNumber(onePixPayRequestedMessage.fullAccountNumber))

            val pixReceived = statement.pixReceived(onePixPayId)
            markers.andAppend("pixReceived", pixReceived)

            if (pixReceived) {
                onePixPayService.processSchedule(onePixPay)
            }

            pixReceived
        } else {
            true
        }
        markers.andAppend("shouldDeleteMessage", shouldDelete)

        logger.info(markers, logName)
        return MessageHandlerResponse.build(shouldDeleteMessage = shouldDelete)
    }

    private fun List<PixStatementItem>.pixReceived(onePixPayId: OnePixPayId<*>) = any { item ->
        item.flow == BankStatementItemFlow.CREDIT &&
            when (onePixPayId.type) {
                OnePixPayIdType.UUID -> item.transactionId == onePixPayId.source
                OnePixPayIdType.END_TO_END -> item.endToEnd == onePixPayId.source
            }
    }

    override fun handleException(e: Exception): MessageHandlerResponse {
        return MessageHandlerResponse.keep()
    }
}