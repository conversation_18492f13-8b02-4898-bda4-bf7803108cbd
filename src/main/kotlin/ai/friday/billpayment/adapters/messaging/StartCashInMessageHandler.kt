package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.cashIn.CashInExecutor
import ai.friday.billpayment.app.cashIn.StartCashInMessage
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class StartCashInMessageHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val transactionService: TransactionService,
    private val cashInExecutor: CashInExecutor,
    @Property(name = "sqs.startCashIn") val startCashInQueueName: String,
) : AbstractSQSHandler(amazonSQS, configuration, startCashInQueueName) {
    private val logger = LoggerFactory.getLogger(StartCashInMessageHandler::class.java)

    override fun handleMessage(message: Message): SQSHandlerResponse {
        val messageBody = parseObjectFrom<StartCashInMessage>(message.body())

        val markers = Markers.append("transactionId", messageBody.transactionId)

        val transaction = transactionService.findTransactionById(TransactionId(messageBody.transactionId))

        try {
            cashInExecutor.execute(transaction)
            logger.info(markers, "StartCashInMessageHandler")
        } catch (e: Exception) {
            markers.andAppend("ACTION", "VERIFY")
                .andAppend("reason", "garantir que a transacao foi processada")
            logger.error(markers, "StartCashInMessageHandler")
        }

        return SQSHandlerResponse(true)
    }

    override fun handleError(message: Message, e: Exception): SQSHandlerResponse {
        logger.error("StartCashInMessageHandler", e)
        return SQSHandlerResponse(false)
    }
}