package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.app.bill.PaymentStarted
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.payment.BillPaymentService
import ai.friday.morning.messaging.MessageHandler
import ai.friday.morning.messaging.MessageHandlerResponse
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

@Handler
@Singleton
open class SQSStartBillPaymentHandler(
    private val billPaymentService: BillPaymentService,
    private val transactionRepository: TransactionRepository,
) : MessageHandler {

    override val configurationName = "bill-payment-started"

    @NewSpan
    override fun handleMessage(message: Message): MessageHandlerResponse {
        val billEvent = parseEvent(message)

        if (billEvent !is PaymentStarted) {
            return MessageHandlerResponse.delete()
        }

        logger.info(Markers.append("event", billEvent), "SQSStartBillPaymentHandler")
        val transaction =
            try {
                transactionRepository.findById(billEvent.transactionId)
            } catch (e: Exception) {
                logger.error(Markers.append("event", message.body()), "SQSStartBillPaymentHandler", e)
                return MessageHandlerResponse.keep()
            }
        billPaymentService.process(transaction)

        return MessageHandlerResponse.delete()
    }

    override fun handleException(e: Exception): MessageHandlerResponse {
        return MessageHandlerResponse.delete()
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSStartBillPaymentHandler::class.java)
    }
}