package ai.friday.billpayment.adapters.messaging.schedule

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.BatchSchedulingId
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.integrations.BatchScheduledPublisher
import ai.friday.billpayment.app.payment.ScheduleStrategy

@FridayMePoupe
class DefaultBatchScheduledPublisher : BatchScheduledPublisher {
    override fun publish(id: BatchSchedulingId, results: List<Pair<BillId, Bill?>>, scheduleStrategy: ScheduleStrategy) {
    }
}