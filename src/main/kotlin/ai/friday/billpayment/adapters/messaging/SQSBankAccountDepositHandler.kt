package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.arbi.toUUIDTransactionId
import ai.friday.billpayment.adapters.lock.undoInvoiceLockProvider
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.banking.BankOperationExecuted
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.banking.InternalBankStatementItem
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.CashInInstrumentationService
import ai.friday.billpayment.app.integrations.InternalBankRepository
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.OnePixPayInstrumentation
import ai.friday.billpayment.app.integrations.ScheduledBillPaymentService
import ai.friday.billpayment.app.integrations.TEDService
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.payment.NotifyDepositService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.transaction.UndoTransaction
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import ai.friday.morning.messaging.MessageHandler
import ai.friday.morning.messaging.MessageHandlerResponse
import arrow.core.getOrElse
import datadog.trace.api.Trace
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.time.LocalDate
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

@Handler
@Singleton
open class SQSBankAccountDepositHandler(
    private val notifyDepositService: NotifyDepositService,
    private val scheduledBillPaymentService: ScheduledBillPaymentService,
    private val cashInInstrumentationService: CashInInstrumentationService,
    private val internalBankRepository: InternalBankRepository,
    private val accountRepository: AccountRepository,
    private val walletService: WalletService,
    private val tedService: TEDService,
    private val transactionRepository: TransactionRepository,
    private val undoTransaction: UndoTransaction,
    @Named(undoInvoiceLockProvider) private val internalLock: InternalLock,
    private val systemActivityService: SystemActivityService,
    private val onePixPayService: OnePixPayService,
    private val onePixPayInstrumentation: OnePixPayInstrumentation,
) : MessageHandler {

    override val configurationName = "bank-account-deposit"

    @Trace
    override fun handleMessage(message: Message): MessageHandlerResponse {
        val bankOperationExecuted = parseObjectFrom<BankOperationExecuted>(message.body())
        val markers =
            append("messageId", message.messageId())
                .andAppend("messageAttributes", message.messageAttributes())
                .andAppend("bankOperationExecuted", bankOperationExecuted)

        if (bankOperationExecuted.flow == BankStatementItemFlow.CREDIT) {
            val paymentMethod =
                accountRepository.findAccountPaymentMethodByIdAndAccountId(
                    accountPaymentMethodId = bankOperationExecuted.accountPaymentMethodId,
                    accountId = bankOperationExecuted.accountId,
                )

            when {
                bankOperationExecuted.verifyIsDeposit() -> return handleDeposit(
                    bankOperationExecuted,
                    paymentMethod,
                    markers,
                )

                bankOperationExecuted.verifyIsRefund() -> return handleRefund(
                    bankOperationExecuted,
                    paymentMethod,
                    markers,
                )
            }
        }
        return MessageHandlerResponse.delete()
    }

    private fun handleDeposit(
        bankOperationExecuted: BankOperationExecuted,
        paymentMethod: AccountPaymentMethod,
        markers: LogstashMarker,
    ): MessageHandlerResponse {
        logger.info(markers, "ReceiveSQSBankAccountDepositHandler")

        val wallet =
            walletService.findWallet(
                bankOperationExecuted.accountId,
                bankOperationExecuted.accountPaymentMethodId,
            )

        val bankStatement =
            internalBankRepository.findBankStatementItem(
                accountPaymentMethodId = bankOperationExecuted.accountPaymentMethodId,
                date = LocalDate.parse(bankOperationExecuted.date),
                operationNumber = bankOperationExecuted.operationNumber,
            ).getOrElse {
                logger.error(markers, "ReceiveSQSBankAccountDepositHandler", Exception(it.toString()))
                return MessageHandlerResponse.keep()
            }

        if (bankStatement.notificatedAt == null) {
            notifyDepositService.notifyDeposit(
                walletId = wallet.id,
                accountPaymentMethodId = bankOperationExecuted.accountPaymentMethodId,
                amount = bankOperationExecuted.amount,
                senderName = bankOperationExecuted.counterpartName,
                senderDocument = bankOperationExecuted.counterpartDocument,
                senderAccountNo = bankOperationExecuted.counterpartAccountNo,
                senderBankName = bankOperationExecuted.counterpartBankName,
                type = bankOperationExecuted.type,
            )
            cashInInstrumentationService.depositReceived(bankOperationExecuted)
            internalBankRepository.update(
                InternalBankStatementItem(
                    accountPaymentMethodId = bankOperationExecuted.accountPaymentMethodId,
                    bankStatementItem = bankStatement.copy(notificatedAt = getZonedDateTime()),
                ),
            )
        }

        onePixPayInstrumentation.userReceivedADeposit(bankOperationExecuted.accountId, bankStatement)
        onePixPayService.process(
            walletId = wallet.id,
            paymentMethod = paymentMethod.method as InternalBankAccount,
            depositOnePixPayId = bankStatement.toUUIDTransactionId(),
        )
        scheduledBillPaymentService.process(wallet.id)

        systemActivityService.setHasCashedIn(wallet.id)
        return MessageHandlerResponse.delete()
    }

    private fun handleRefund(
        bankOperationExecuted: BankOperationExecuted,
        paymentMethod: AccountPaymentMethod,
        markers: LogstashMarker,
    ): MessageHandlerResponse {
        val startDate = LocalDate.parse(bankOperationExecuted.date, dateFormat)

        val operations =
            tedService.checkTEDUndone(
                amount = bankOperationExecuted.amount,
                startDate = startDate,
                endDate = startDate,
                originAccountNo = (paymentMethod.method as InternalBankAccount).buildFullAccountNumber(),
            )
        markers.andAppend("operations", operations)

        if (operations.isEmpty()) {
            logger.error(markers, "ReceiveSQSBankAccountDepositHandler")
            return MessageHandlerResponse.keep()
        }

        val wallet =
            walletService.findWallet(
                bankOperationExecuted.accountId,
                bankOperationExecuted.accountPaymentMethodId,
            )

        operations.forEachIndexed { index, operation ->
            val lock = internalLock.acquireLock(operation.operationId.value)
            if (lock != null) {
                try {
                    val transaction =
                        transactionRepository.findByWalletAndStatusAndBankOperationId(
                            walletId = wallet.id,
                            status = TransactionStatus.COMPLETED,
                            bankOperationId = operation.operationId,
                        )
                    undoTransaction.undoDirectInvoice(
                        transaction = transaction,
                        status = operation.status,
                    )
                    logger.info(
                        markers.and(append("transactionId", transaction.id)),
                        "ReceiveSQSBankAccountDepositHandler",
                    )
                    return MessageHandlerResponse.delete()
                } catch (e: ItemNotFoundException) {
                    try {
                        transactionRepository.findByWalletAndStatusAndBankOperationId(
                            walletId = wallet.id,
                            status = TransactionStatus.UNDONE,
                            bankOperationId = operation.operationId,
                        )
                        if (index == operations.lastIndex) {
                            return MessageHandlerResponse.delete()
                        }
                    } catch (e: ItemNotFoundException) {
                        logger.error(markers, "ReceiveSQSBankAccountDepositHandler", e)
                        return MessageHandlerResponse.keep()
                    }
                } finally {
                    lock.unlock()
                }
            }
        }
        return MessageHandlerResponse.keep()
    }

    override fun handleException(e: Exception) = MessageHandlerResponse.keep()

    companion object {
        private val logger = LoggerFactory.getLogger(SQSBankAccountDepositHandler::class.java)
    }
}