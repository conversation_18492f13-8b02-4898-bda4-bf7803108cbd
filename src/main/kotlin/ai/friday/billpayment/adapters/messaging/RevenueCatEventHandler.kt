package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.FormatterUtils.Companion.convertToCentsOrZero
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.and
import ai.friday.billpayment.app.inappsubscription.AnonymousIdInAppSubscriptionQueueMessageTO
import ai.friday.billpayment.app.inappsubscription.InAppSubscription
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionEvent
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionEventType
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionProductId
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionReason
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionService
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStatus
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStore
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.log.andAppend
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.context.annotation.Property
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import java.time.Instant
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class RevenueCatEventHandler(
    amazonSQS: SqsClient,
    private val configuration: SQSMessageHandlerConfiguration,
    private val inAppSubscriptionService: InAppSubscriptionService,
    @Property(name = "integrations.revenueCat.dlqArn") private val dlqArn: String,
    private val messagePublisher: SQSMessagePublisher,
) : AbstractSQSHandler(
    amazonSQS,
    configuration.copy(dlqArn = dlqArn),
    configuration.revenueCatQueueName,
    consumers = 1,
) {
    private val objectMapper = jacksonObjectMapper()

    @NewSpan
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val body = message.body()
        val markers = Markers.append("request", body)
        // TRANSFER não tem app_user_id
        if (isTransferEvent(body)) {
            processTransferEvent(body)
            LOG.info(markers.andAppend("transferEvent", true), "RevenueCatCallbackHandler")
            return SQSHandlerResponse(true)
        }

        val request = parseObjectFrom<RevenueCatCallbackRequest>(body)
        markers.andAppend("accountId", request.event.appUserId).andAppend("eventType", request.event.type)

        if (request.event.environment == "SANDBOX") {
            return SQSHandlerResponse(true)
        }

        saveEvent(request.event, body)

        try {
            AccountId.create(request.event.appUserId)
        } catch (exception: Exception) {
            LOG.warn(markers.andAppend("context", "evento com ID anonimo enviado para fila de processamento"), "RevenueCatCallbackHandler")
            messagePublisher.sendMessage(
                configuration.anonymousIdInAppSubscriptionQueueName,
                AnonymousIdInAppSubscriptionQueueMessageTO(
                    appUserId = request.event.appUserId,
                    retry = 0,
                ),
            )
        }

        when (request.event.type) {
            InAppSubscriptionEventType.INITIAL_PURCHASE -> createInAppSubscription(request.event)
            InAppSubscriptionEventType.RENEWAL -> renewInAppSubscription(request.event)
            InAppSubscriptionEventType.SUBSCRIPTION_EXTENDED -> extendedInAppSubscription(request.event)
            InAppSubscriptionEventType.EXPIRATION -> expireInAppSubscription(request.event)
            InAppSubscriptionEventType.CANCELLATION -> if (request.event.cancelReason == CancellationReasonTO.CUSTOMER_SUPPORT) handleInAppSubscriptionRefund(request.event) else handleCancellation(request.event)

            InAppSubscriptionEventType.UNCANCELLATION -> handleUncancellation(request.event)

            InAppSubscriptionEventType.SUBSCRIPTION_PAUSED -> handleCancellation(request.event)

            InAppSubscriptionEventType.PRODUCT_CHANGE -> {
                LOG.error(
                    markers
                        .andAppend("ACTION", "VERIFY")
                        .andAppend("context", "evento de mudança de produto. Verificar se precisa alterar a assinatura do usuário na base. Tem que acessar o RevenueCat para investigar"),
                    "RevenueCatCallbackHandler",
                )
                null
            }

            InAppSubscriptionEventType.BILLING_ISSUE -> {
                markers.andAppend("ignorableEvent", request.event.type)
                null
            }

            InAppSubscriptionEventType.TRANSFER,
            InAppSubscriptionEventType.SUBSCRIBER_ALIAS,
            InAppSubscriptionEventType.TEST,
            InAppSubscriptionEventType.NON_RENEWING_PURCHASE,
            -> TODO("event not implemented: ${request.event.type}")
        }?.let {
            it.mapLeft {
                LOG.error(markers.andAppend("subscriptionError", it), "RevenueCatCallbackHandler")
                throw IllegalStateException("erro ao executar evento ${request.event.type}")
            }
        }

        LOG.info(markers, "RevenueCatCallbackHandler")
        return SQSHandlerResponse(true)
    }

    override fun handleError(
        message: Message,
        e: Exception,
    ): SQSHandlerResponse {
        LOG.error(Markers.append("message", message.body()), "RevenueCatCallbackHandler", e)
        return SQSHandlerResponse(false)
    }

    private fun handleCancellation(event: RevenueCatCallbackEvent) = inAppSubscriptionService.cancelSubscription(event.toInAppSubscription())
    private fun handleUncancellation(event: RevenueCatCallbackEvent) = inAppSubscriptionService.uncancelSubscription(event.toInAppSubscription())

    private fun isTransferEvent(body: String) = body.contains("TRANSFER") && !body.contains("app_user_id")

    private fun handleInAppSubscriptionRefund(event: RevenueCatCallbackEvent) = inAppSubscriptionService.handleSubscriptionRefund(event.toInAppSubscription())

    private fun createInAppSubscription(event: RevenueCatCallbackEvent) = inAppSubscriptionService.createSubscription(event.toInAppSubscription())

    private fun renewInAppSubscription(event: RevenueCatCallbackEvent) = inAppSubscriptionService.renewSubscription(event.toInAppSubscription())

    private fun extendedInAppSubscription(event: RevenueCatCallbackEvent) = inAppSubscriptionService.extendSubscription(event.toInAppSubscription())

    private fun expireInAppSubscription(event: RevenueCatCallbackEvent) = inAppSubscriptionService.expireSubscription(event.toInAppSubscription())

    private fun processTransferEvent(
        payload: String,
    ) {
        val event = parseObjectFrom<Map<String, Any>>(payload)
        val eventInfo = event["event"]?.let { parseObjectFrom<RevenueCatTransferCallbackEvent>(objectMapper.writeValueAsString(it)) } ?: throw IllegalStateException("event not found in payload $payload")

        if (eventInfo.environment == "SANDBOX") {
            return
        }

        if (eventInfo.transferredTo.size > 1 || eventInfo.transferredFrom.size > 1) {
            LOG.error(
                Markers
                    .append("ACTION", "VERIFY")
                    .and(
                        "transferedTo" to eventInfo.transferredTo,
                        "transferedFrom" to eventInfo.transferredFrom,
                    ),
                "RevenueCatCallbackHandler",
            )
        }

        inAppSubscriptionService
            .transferSubscription(
                sourceAccountId = AccountId(eventInfo.transferredFrom.single()),
                targetAccountId = AccountId(eventInfo.transferredTo.single()),
            ).mapLeft {
                LOG.error(Markers.append("subscriptionTransferError", it), "RevenueCatCallbackHandler")
            }

        inAppSubscriptionService.saveEvent(
            InAppSubscriptionEvent(
                accountId = AccountId(value = eventInfo.transferredFrom.first()),
                type = InAppSubscriptionEventType.TRANSFER,
                createdAt = ZonedDateTime.ofInstant(
                    Instant.ofEpochMilli(eventInfo.eventTimestampMs.toLong()),
                    brazilTimeZone,
                ),
                payload = payload,
            ),
        )
        inAppSubscriptionService.saveEvent(
            InAppSubscriptionEvent(
                accountId = AccountId(value = eventInfo.transferredTo.first()),
                type = InAppSubscriptionEventType.TRANSFER,
                createdAt = ZonedDateTime.ofInstant(
                    Instant.ofEpochMilli(eventInfo.eventTimestampMs.toLong()),
                    brazilTimeZone,
                ),
                payload = payload,
            ),
        )
    }

    private fun saveEvent(
        event: RevenueCatCallbackEvent,
        payload: String,
    ) =
        inAppSubscriptionService.saveEvent(
            InAppSubscriptionEvent(
                accountId = AccountId(value = event.appUserId),
                type = event.type,
                createdAt = ZonedDateTime.ofInstant(Instant.ofEpochMilli(event.eventTimestampMs), brazilTimeZone),
                payload = payload,
            ),
        )

    companion object {
        private val LOG = LoggerFactory.getLogger(RevenueCatEventHandler::class.java)
    }
}

fun RevenueCatCallbackEvent.toInAppSubscription(): InAppSubscription =
    InAppSubscription(
        accountId = AccountId(value = this.appUserId),
        status =
        when (this.type) {
            InAppSubscriptionEventType.TEST,
            InAppSubscriptionEventType.NON_RENEWING_PURCHASE,
            InAppSubscriptionEventType.PRODUCT_CHANGE,
            InAppSubscriptionEventType.BILLING_ISSUE,
            InAppSubscriptionEventType.SUBSCRIBER_ALIAS,
            InAppSubscriptionEventType.SUBSCRIPTION_PAUSED,
            InAppSubscriptionEventType.UNCANCELLATION,
            InAppSubscriptionEventType.TRANSFER,
            InAppSubscriptionEventType.RENEWAL,
            InAppSubscriptionEventType.SUBSCRIPTION_EXTENDED,
            InAppSubscriptionEventType.INITIAL_PURCHASE,
            -> InAppSubscriptionStatus.ACTIVE

            InAppSubscriptionEventType.CANCELLATION -> if (this.cancelReason == CancellationReasonTO.CUSTOMER_SUPPORT) InAppSubscriptionStatus.EXPIRED else InAppSubscriptionStatus.ACTIVE

            InAppSubscriptionEventType.EXPIRATION -> InAppSubscriptionStatus.EXPIRED
        },
        endsAt = ZonedDateTime.ofInstant(Instant.ofEpochMilli(this.expirationAtMs), brazilTimeZone),
        store = store,
        reason =
        periodType?.let {
            if (it == "TRIAL") InAppSubscriptionReason.TRIAL else InAppSubscriptionReason.SUBSCRIPTION
        } ?: InAppSubscriptionReason.SUBSCRIPTION,
        inAppSubscriptionAccessConcessionId = null,
        price = convertToCentsOrZero(priceInPurchasedCurrency),
        productId = InAppSubscriptionProductId(value = productId),
        offStoreProductId = null,
        autoRenew =
        when (this.type) {
            InAppSubscriptionEventType.RENEWAL,
            InAppSubscriptionEventType.SUBSCRIPTION_EXTENDED,
            InAppSubscriptionEventType.INITIAL_PURCHASE,
            InAppSubscriptionEventType.UNCANCELLATION,
            -> true

            InAppSubscriptionEventType.CANCELLATION,
            InAppSubscriptionEventType.EXPIRATION,
            InAppSubscriptionEventType.SUBSCRIPTION_PAUSED,
            -> false

            InAppSubscriptionEventType.TEST,
            InAppSubscriptionEventType.NON_RENEWING_PURCHASE,
            InAppSubscriptionEventType.PRODUCT_CHANGE,
            InAppSubscriptionEventType.BILLING_ISSUE,
            InAppSubscriptionEventType.SUBSCRIBER_ALIAS,
            InAppSubscriptionEventType.TRANSFER,
            -> null
        },
    )

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonIgnoreProperties(ignoreUnknown = true)
data class RevenueCatCallbackRequest(
    val apiVersion: String,
    val event: RevenueCatCallbackEvent,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonIgnoreProperties(ignoreUnknown = true)
data class RevenueCatTransferCallbackEvent(
    val appId: String,
    val eventTimestampMs: String,
    val id: String,
    val store: String,
    val transferredFrom: List<String>,
    val transferredTo: List<String>,
    val type: String,
    val environment: String,
)

/*
* https://www.revenuecat.com/docs/integrations/webhooks/sample-events
* */
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonIgnoreProperties(ignoreUnknown = true)
data class RevenueCatCallbackEvent(
    val environment: String,
    val id: String,
    val store: InAppSubscriptionStore,
    val type: InAppSubscriptionEventType,
    val appUserId: String,
    val appId: String?,
    val aliases: List<String>?,
    val commissionPercentage: String?,
    val countryCode: String?,
    val currency: String?,
    val entitlementId: String?,
    val entitlementIds: List<String>?,
    val eventTimestampMs: Long,
    val expirationAtMs: Long,
    val isFamilyShare: Boolean?,
    val offerCode: String?,
    val originalAppUserId: String?,
    val originalTransactionId: String?,
    val periodType: String?,
    val presentedOfferingId: String?,
    val price: String?,
    val priceInPurchasedCurrency: String?,
    val productId: String,
    val purchasedAtMs: Long?,
    val subscriberAttributes: Map<String, Any>?,
    val takehomePercentage: String?,
    val taxPercentage: String?,
    val transactionId: String?,
    val isTrialConversion: Boolean?,
    val cancelReason: CancellationReasonTO?,
    val expirationReason: CancellationReasonTO?,
    val renewalNumber: Long?,
    val gracePeriodExpirationAtMs: Long?,
    val autoResumeAtMs: Long?,
    val newProductId: String?,
)

enum class CancellationReasonTO {
    UNSUBSCRIBE, // Subscriber cancelled voluntarily. This event fires when a user unsubscribes, not when the subscription expires.
    BILLING_ERROR, // Apple, Amazon, or Google could not charge the subscriber using their payment method. The CANCELLATION event with cancellation reason BILLING_ERROR is fired as soon as the billing issue has been detected. The EXPIRATION event with expiration reason BILLING_ERROR is fired if the grace period (if set up) has ended without recovering the payment, and the customer should lose access to the subscription.
    DEVELOPER_INITIATED, // Developer cancelled the subscription.
    PRICE_INCREASE, // Subscriber did not agree to a price increase.
    CUSTOMER_SUPPORT, // Customer received a refund from Apple support, a Play Store subscription was refunded through RevenueCat, an Amazon subscription was refunded through Amazon support, or a web (RevenueCat Billing or Stripe Billing) subscription was refunded. Note that this does not mean that a subscription's autorenewal preference has been deactivated since refunds can be given without cancelling a subscription. You should check the current subscription status to check if the subscription is still active.
    UNKNOWN, // Apple did not provide the reason for the cancellation.
    SUBSCRIPTION_PAUSED, // The subscription expired because it was paused (only EXPIRATION event).
}