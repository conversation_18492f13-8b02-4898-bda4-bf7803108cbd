package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.jobs.SynchronizeBankAccountService
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSSynchronizeBankAccountHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = "sqs.queues.synchronizeBankAccount") private val queueName: String,
    private val synchronizeBankAccountService: SynchronizeBankAccountService,
) : AbstractSQSHandler(
    amazonSQS,
    configuration,
    queueName,
    consumers = 4,
) {
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val synchronizeBankAccountMessage = parseObjectFrom<SynchronizeBankAccountMessage>(m.body())

        logger.info(Markers.append("body", synchronizeBankAccountMessage), "SQSSynchronizeBankAccountHandler")
        synchronizeBankAccountService.synchronizePaymentMethod(
            synchronizeBankAccountMessage.paymentMethodId,
            synchronizeBankAccountMessage.accountId,
        )

        return SQSHandlerResponse(true)
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(Markers.append("body", m), "SQSSynchronizeBankAccountHandler", e)
        return SQSHandlerResponse(false)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSSynchronizeBankAccountHandler::class.java)
    }
}

data class SynchronizeBankAccountMessage(
    val accountId: AccountId,
    val paymentMethodId: AccountPaymentMethodId,
)