package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.fingerprint.AddDeviceIdResult
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.fingerprint.RemoveDeviceIdResult
import ai.friday.billpayment.app.wallet.MemberAdded
import ai.friday.billpayment.app.wallet.MemberRemoved
import ai.friday.billpayment.app.wallet.WalletEvent
import ai.friday.billpayment.app.wallet.instrumentation.WalletInstrumentationService
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSWalletEventsHandler(
    amazonSQS: SqsClient,
    amazonSNS: SnsClient,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = walletEventTopicArnPropertyName) private val topicArn: String,
    private val walletInstrumentationService: WalletInstrumentationService,
    private val deviceFingerprintService: DeviceFingerprintService,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = configuration.walletEventsQueueName,
    amazonSNS = amazonSNS,
    topicArn = topicArn,
    filter = null,
) {
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val markers = append("queue_message", message.body())
        when (val walletEvent = parseWalletEvent(message)) {
            is MemberAdded -> {
                val deviceIdAdded = addDeviceId(walletEvent)
                if (!deviceIdAdded) {
                    return SQSHandlerResponse(shouldDeleteMessage = false)
                }

                logger.info(markers.andAppend("deviceIdAdded", deviceIdAdded), "SQSWalletEventsHandler#handleMessage#MemberAdded")
            }

            is MemberRemoved -> {
                if (removeDeviceId(walletEvent)) {
                    if (walletEvent.actionSource is ActionSource.Api) {
                        walletInstrumentationService.memberRemoved(memberRemoved = walletEvent)
                    }

                    logger.info(markers, "SQSWalletEventsHandler#handleMessage#MemberRemoved")
                }
            }
        }
        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        val markers = append("event", m.body()).and<LogstashMarker>(append("exceptionMessage", e.message))
        logger.error(markers, "SQSWalletEventsHandler", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    private fun addDeviceId(event: MemberAdded): Boolean {
        val markers = append("event", event)
        return run {
            val result = deviceFingerprintService.addDeviceId(event.member, event.walletId)
                .getOrElse {
                    logger.error(markers, "SQSWalletEventsHandler#addDeviceId#Failure", it)
                    return false
                }

            when (result) {
                is AddDeviceIdResult.Success -> {
                    true
                }

                is AddDeviceIdResult.Failure -> {
                    markers.andAppend("result", result)
                    logger.error(markers, "SQSWalletEventsHandler#addDeviceId#Failure")
                    false
                }
            }
        }
    }

    private fun removeDeviceId(event: MemberRemoved): Boolean {
        val markers = append("event", event)

        return run {
            val result = deviceFingerprintService.removeDeviceId(event.member, event.walletId)

            when (result) {
                is RemoveDeviceIdResult.Success -> {
                    true
                }

                is RemoveDeviceIdResult.Unknown -> {
                    markers.andAppend("result", result)
                    logger.warn(markers, "SQSWalletEventsHandler", result.e)
                    false
                }

                is RemoveDeviceIdResult.Failure -> {
                    markers.andAppend("result", result)
                    logger.warn(markers, "SQSWalletEventsHandler#removeDeviceId#Failure")
                    false
                }
            }
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSWalletEventsHandler::class.java)
    }
}

fun parseWalletEvent(message: Message): WalletEvent {
    return parseObjectFrom(message.body())
}