package ai.friday.billpayment.adapters.staging

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.fingerprint.DeviceId
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyDetailsResult
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixKeyOwner
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Primary
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

// Esse bean sobe apenas em staging com prioridade para ser utilizado no lugar do NewArbiPixKeyManagementAdapter
// Isso é feito para podermos consultar chave pix em staging sem chamar o Arbi

@Requires(env = ["staging"])
@Singleton
@Primary
class MockNewArbiPixKeyManagementAdapter : PixKeyManagement {
    override fun registerKey(accountNo: AccountNumber, key: PixKey, document: String, name: String, deviceId: DeviceId?): PixKey {
        TODO("Not yet implemented")
    }

    override fun deleteKey(key: String, document: Document, deviceId: DeviceId?): Either<PixKeyError, Unit> {
        TODO("Not yet implemented")
    }

    override fun findKeyDetails(key: PixKey, document: String): Either<PixKeyError, PixKeyDetailsResult> {
        logger.info(
            Markers.append("pixKey", key.value)
                .andAppend("pixKeyType", key.type)
                .andAppend("document", document),
            "$logName#findKeyDetails",
        )

        var doc = document

        when (key.value) {
            "*************" -> TODO()
            "*************" -> return PixKeyError.KeyNotFound.left()
            "*************" -> doc = "invalid_document"
            "*************" -> TODO()
            "*************" -> return PixKeyError.KeyNotFound.left()
            "***********" -> return PixKeyError.KeyNotFound.left()
            "<EMAIL>" -> return PixKeyError.KeyNotFound.left()
        }

        return PixKeyDetailsResult(
            pixKeyDetails = PixKeyDetails(
                key = key,
                holder = PixKeyHolder(
                    accountNo = *********.toBigInteger(),
                    accountDv = "9",
                    ispb = "mea",
                    institutionName = "Ward Gibson",
                    accountType = AccountType.SALARY,
                    routingNo = 9088,
                ),
                owner = PixKeyOwner(name = "Leona Thomas", document = doc),
            ),
            e2e = "e2e_mock",
        ).right()
    }

    override fun findKeyDetailsCacheable(key: PixKey, document: String): PixKeyDetailsResult {
        TODO("Not yet implemented")
    }

    override fun findInternalKeys(document: String) {
        TODO("Not yet implemented")
    }

    override fun startClaim(key: String, keyType: String, type: String): String {
        TODO("Not yet implemented")
    }

    override fun confirmClaim(claimId: String, reason: String) {
        TODO("Not yet implemented")
    }

    override fun completeClaim(claimId: String) {
        TODO("Not yet implemented")
    }

    override fun cancelClaim(claimId: String, reason: String) {
        TODO("Not yet implemented")
    }

    override fun listClaims() {
        TODO("Not yet implemented")
    }

    companion object {
        private val logger = LoggerFactory.getLogger(MockNewArbiPixKeyManagementAdapter::class.java)
        private val logName = "MockNewArbiPixKeyManagementAdapter"
    }
}