package ai.friday.billpayment.adapters.whatsappflow

import ai.friday.billpayment.app.whatsapp.FlowRequest

object WhatsAppFlow {
    const val AES_KEY_SIZE = 128
    const val KEY_GENERATOR_ALGORITHM = "AES"
    const val AES_CIPHER_ALGORITHM = "AES/GCM/NoPadding"
    const val RSA_ENCRYPT_ALGORITHM = "RSA/ECB/OAEPWithSHA-256AndMGF1Padding"
    const val RSA_MD_NAME = "SHA-256"
    const val RSA_MGF = "MGF1"

    data class DecryptionInfo(val clearPayload: String, val clearAesKey: ByteArray)

    fun createTemplateRequest(flowRequest: FlowRequest, namespace: String): String {
        return """
        {
            "id": "${flowRequest.messageId}",
            "to": "${flowRequest.phoneNumber}@wa.gw.msging.net",
            "type": "application/json",
            "content": {
                "type": "template",
                "template": {
                    "namespace": "$namespace",
                    "name": "${flowRequest.templateName}",
                    "language": {
                        "code": "pt_BR",
                        "policy": "deterministic"
                    },
                    "components": [
                        {
                            "type": "button",
                            "sub_type": "flow",
                            "index": "0",
                            "parameters": [
                                {
                                    "type": "action",
                                    "action": {
                                        "flow_token": "${flowRequest.flowToken}"
                                    }
                                }
                            ]
                        }
                    ]
                }
            }
        }"""
    }
}