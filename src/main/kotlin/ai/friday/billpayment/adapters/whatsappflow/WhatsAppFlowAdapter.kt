package ai.friday.billpayment.adapters.whatsappflow

import ai.friday.billpayment.adapters.whatsappflow.WhatsAppFlow.createTemplateRequest
import ai.friday.billpayment.app.whatsapp.FlowRequest
import ai.friday.billpayment.app.whatsapp.WhatsAppClient
import ai.friday.billpayment.markers
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest.POST
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory

@Singleton
internal class WhatsAppFlowAdapter(
    @Client(id = "whatsapp-client") private val httpClient: RxHttpClient,
    private val configuration: WhatsAppConfiguration,
) : WhatsAppClient {
    override fun sendFlow(flowRequest: FlowRequest): Result<String> {
        val body = createTemplateRequest(flowRequest, configuration.namespace)
        val request = POST("/messages", body).header("Authorization", configuration.auth)

        val markers = request.markers()

        return runCatching {
            httpClient.exchange(request, Argument.STRING).blockingFirst().let { response ->
                logger.info(markers.and(response.markers()), "WhatsAppAdapter")

                if (response.status.code in 200..202) {
                    flowRequest.messageId
                } else {
                    return Result.failure(IllegalStateException("Unexpected status code: ${response.status.code}. Response: ${response.body()}"))
                }
            }
        }.onFailure { e ->
            if (e is HttpClientResponseException) {
                markers.add(e.markers())
            }
            logger.error(markers, "WhatsAppAdapter", e)
        }
    }

    private val logger = LoggerFactory.getLogger("WhatsAppAdapter")

    init {
        logger.info(
            markers(
                "host" to configuration.host,
            ),
            "WhatsAppAdapter#initialized",
        )
    }
}

@ConfigurationProperties("integrations.whatsapp")
interface WhatsAppConfiguration {
    val auth: String
    val host: String
    val namespace: String
}