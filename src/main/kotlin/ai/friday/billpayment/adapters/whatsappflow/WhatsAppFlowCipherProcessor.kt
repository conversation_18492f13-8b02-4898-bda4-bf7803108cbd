package ai.friday.billpayment.adapters.whatsappflow

import ai.friday.billpayment.adapters.whatsappflow.WhatsAppFlow.AES_CIPHER_ALGORITHM
import ai.friday.billpayment.adapters.whatsappflow.WhatsAppFlow.AES_KEY_SIZE
import ai.friday.billpayment.adapters.whatsappflow.WhatsAppFlow.DecryptionInfo
import ai.friday.billpayment.adapters.whatsappflow.WhatsAppFlow.KEY_GENERATOR_ALGORITHM
import ai.friday.billpayment.adapters.whatsappflow.WhatsAppFlow.RSA_ENCRYPT_ALGORITHM
import ai.friday.billpayment.adapters.whatsappflow.WhatsAppFlow.RSA_MD_NAME
import ai.friday.billpayment.adapters.whatsappflow.WhatsAppFlow.RSA_MGF
import ai.friday.billpayment.app.whatsapp.WhatsAppFlowConfiguration
import jakarta.inject.Singleton
import java.nio.charset.StandardCharsets
import java.security.KeyFactory
import java.security.interfaces.RSAPrivateKey
import java.security.spec.MGF1ParameterSpec
import java.security.spec.PKCS8EncodedKeySpec
import java.util.Base64
import javax.crypto.Cipher
import javax.crypto.spec.GCMParameterSpec
import javax.crypto.spec.OAEPParameterSpec
import javax.crypto.spec.PSource
import javax.crypto.spec.SecretKeySpec

@Singleton
class WhatsAppFlowCipherProcessor(private val configuration: WhatsAppFlowConfiguration) {
    fun decrypt(
        encryptedAesKey: ByteArray,
        encryptedFlowData: ByteArray,
        initialVector: ByteArray,
    ): DecryptionInfo {
        val privateKey = readPrivateKeyFromPkcs8UnencryptedPem()

        val aesKey = decryptUsingRSA(privateKey, encryptedAesKey)

        return DecryptionInfo(decryptUsingAES(encryptedFlowData, aesKey, initialVector), aesKey)
    }

    fun encrypt(
        clearResponse: String,
        aesKey: ByteArray,
        initialVector: ByteArray,
    ): String {
        val paramSpec = GCMParameterSpec(AES_KEY_SIZE, initialVector)
        val cipher = Cipher.getInstance(AES_CIPHER_ALGORITHM)
        cipher.init(Cipher.ENCRYPT_MODE, SecretKeySpec(aesKey, KEY_GENERATOR_ALGORITHM), paramSpec)
        val encryptedData = cipher.doFinal(clearResponse.toByteArray(StandardCharsets.UTF_8))

        return Base64.getEncoder().encodeToString(encryptedData)
    }

    private fun readPrivateKeyFromPkcs8UnencryptedPem(): RSAPrivateKey {
        val prefix = "-----BEGIN PRIVATE KEY-----"
        val suffix = "-----END PRIVATE KEY-----"

        check(prefix in configuration.privateKey) {
            "Expecting unencrypted private key in PKCS8 format starting with $prefix"
        }

        val privateKeyPEM = configuration.privateKey
            .replace(prefix, "")
            .replace("[\\r\\n]".toRegex(), "")
            .replace(suffix, "")

        val encoded = Base64.getDecoder().decode(privateKeyPEM)
        val keyFactory = KeyFactory.getInstance("RSA")
        val keySpec = PKCS8EncodedKeySpec(encoded)

        return keyFactory.generatePrivate(keySpec) as RSAPrivateKey
    }

    private fun decryptUsingRSA(privateKey: RSAPrivateKey, payload: ByteArray): ByteArray {
        val cipher = Cipher.getInstance(RSA_ENCRYPT_ALGORITHM)
        cipher.init(
            Cipher.DECRYPT_MODE,
            privateKey,
            OAEPParameterSpec(RSA_MD_NAME, RSA_MGF, MGF1ParameterSpec.SHA256, PSource.PSpecified.DEFAULT),
        )

        return cipher.doFinal(payload)
    }

    private fun decryptUsingAES(encryptedPayload: ByteArray, aesKey: ByteArray, iv: ByteArray): String {
        val paramSpec = GCMParameterSpec(AES_KEY_SIZE, iv)
        val cipher = Cipher.getInstance(AES_CIPHER_ALGORITHM)
        cipher.init(Cipher.DECRYPT_MODE, SecretKeySpec(aesKey, KEY_GENERATOR_ALGORITHM), paramSpec)
        val data = cipher.doFinal(encryptedPayload)
        return String(data, StandardCharsets.UTF_8)
    }
}