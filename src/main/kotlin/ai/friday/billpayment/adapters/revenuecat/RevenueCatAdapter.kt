package ai.friday.billpayment.adapters.revenuecat

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.and
import ai.friday.billpayment.app.inappsubscription.InAppSubscription
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionAdapter
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionProductId
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionProducts
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionReason
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStatus
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStore
import ai.friday.billpayment.markers
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.micronaut.cache.annotation.CacheConfig
import io.micronaut.cache.annotation.Cacheable
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpMethod
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.uri.UriBuilder
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.time.Period
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import org.slf4j.LoggerFactory

@ConfigurationProperties("integrations.revenueCat")
data class RevenueCatConfiguration
@ConfigurationInject
constructor(
    val projectId: String,
    val secretKey: String,
    val secretKeyV1: String,
)

@Singleton
@CacheConfig("revenuecat")
open class RevenueCatAdapter(
    @param:Client(
        value = "\${integrations.revenueCat.host}",
    ) private val httpClient: RxHttpClient,
    private val configuration: RevenueCatConfiguration,
) : InAppSubscriptionAdapter {
    private val logger = LoggerFactory.getLogger(RevenueCatAdapter::class.java)

    @Cacheable
    override fun getProducts(): List<InAppSubscriptionProducts> {
        val logName = "RevenueCatAdapter#getProducts"

        val uri =
            UriBuilder
                .of("/v2/projects/${configuration.projectId}/products")
                .toString()
        val request = createHttpRequest<Unit>(HttpMethod.GET, uri, configuration.secretKey)
        val markers = request.markers()
        val call =
            httpClient.exchange(
                request,
                Argument.of(InAppSubscriptionProductResponseTO::class.java),
                Argument.of(InAppSubscriptionErrorResponseTO::class.java),
            )

        try {
            val responseBody = call.firstOrError().blockingGet().body()
            markers.and("responseBody" to responseBody)
            logger.info(markers, logName)
            return responseBody
                .items
                .mapNotNull { item ->
                    item.subscription.duration?.let {
                        InAppSubscriptionProducts(
                            id = InAppSubscriptionProductId(value = item.id),
                            displayName = item.displayName,
                            storeIdentifier = item.storeIdentifier,
                            duration = Period.parse(it),
                            gracePeriodDuration = item.subscription.gracePeriodDuration?.let(Period::parse),
                            type = item.type,
                        )
                    }
                }
        } catch (ex: HttpClientResponseException) {
            logger.error(markers.and("status" to ex.response.status, "response" to ex.response.getBody(String::class.java)), logName)
            throw ex
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            throw ex
        }
    }

    override fun getSubscriptions(accountId: AccountId): Either<Exception, List<InAppSubscription>> {
        val logName = "RevenueCatAdapter#getSubscriptions"

        val requestMap =
            mutableMapOf<String, Any>("customerId" to accountId.value)
        val uri =
            UriBuilder
                .of("/v1/subscribers/{customerId}")
                .expand(requestMap)
                .toString()
        val request = createHttpRequest<Unit>(HttpMethod.GET, uri, configuration.secretKeyV1)
        val markers = request.markers().andAppend("accountId", accountId.value)

        val call =
            httpClient.exchange(
                request,
                Argument.of(InAppSubscriptionResponseTO::class.java),
                Argument.of(InAppSubscriptionErrorResponseTO::class.java),
            )

        try {
            val responseBody = call.firstOrError().blockingGet().body()
            markers.and("responseBody" to responseBody)
            logger.info(markers, logName)
            return responseBody
                .subscriber
                .subscriptions
                .entries
                .map { (_, value) ->
                    InAppSubscription(
                        accountId = accountId,
                        status = if (value.expiresDate > ZonedDateTime.now().toString()) InAppSubscriptionStatus.ACTIVE else InAppSubscriptionStatus.EXPIRED,
                        endsAt = ZonedDateTime.parse(value.expiresDate, DateTimeFormatter.ISO_DATE_TIME),
                        store = getStore(value.store),
                        reason = if (value.periodType == InAppSubscriptionResponseSubscriptionPeriodTypeTO.TRIAL) InAppSubscriptionReason.TRIAL else InAppSubscriptionReason.SUBSCRIPTION,
                        inAppSubscriptionAccessConcessionId = null,
                        autoRenew = value.unsubscribeDetectedAt != null,
                        price = 0,
                        productId = value.productPlanIdentifier?.let { InAppSubscriptionProductId(it) },
                        offStoreProductId = null,
                    )
                }.right()
        } catch (ex: HttpClientResponseException) {
            logger.error(markers.and("status" to ex.response.status, "response" to ex.response.getBody(String::class.java)), logName, ex)
            return Either.Left(ex)
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            return Either.Left(ex)
        }
    }

    override fun getAccountId(externalId: String): Result<AccountId?> = runCatching {
        val logName = "RevenueCatAdapter#getSubscriptions"

        val uri = UriBuilder.of("/v2/projects/{projectId}/customers/{customerId}")
            .expand(
                mutableMapOf(
                    "projectId" to configuration.projectId,
                    "customerId" to externalId,
                ),
            )
            .toString()
        val request = createHttpRequest<Unit>(HttpMethod.GET, uri, configuration.secretKey)
        val markers = request.markers().and("externalId" to externalId)

        val call =
            httpClient.exchange(
                request,
                Argument.of(CustomerTO::class.java),
                Argument.of(InAppSubscriptionErrorResponseTO::class.java),
            )

        try {
            val result = call.firstOrError().blockingGet().body()

            logger.info(markers.and("result" to result), logName)

            try {
                AccountId.create(result.id)
            } catch (exception: Exception) {
                null
            }
        } catch (exception: HttpClientResponseException) {
            logger.error(markers.and("status" to exception.response.status, "response" to exception.response.getBody(String::class.java)), logName, exception)
            throw exception
        } catch (exception: Exception) {
            logger.error(markers, logName, exception)
            throw exception
        }
    }

    // NOTE: Só funciona no Google. Na Apple, o usuário deve solicitar diretamente.
    override fun refund(accountId: AccountId, productId: InAppSubscriptionProductId): Result<Unit> {
        val logName = "RevenueCatAdapter#refund"

        val requestMap = mutableMapOf<String, Any>("customerId" to accountId.value, "productId" to productId.value)
        val uri =
            UriBuilder
                .of("/v1/subscribers/{customerId}/subscriptions/{productId}/revoke")
                .expand(requestMap)
                .toString()
        val request = createHttpRequest<Unit>(HttpMethod.POST, uri, configuration.secretKeyV1)
        val markers = request.markers().andAppend("accountId", accountId.value)
            .andAppend("productId", productId.value)

        val call =
            httpClient.retrieve(
                request,
                Argument.of(String::class.java),
                Argument.of(String::class.java),
            )
        return try {
            val response = call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", response), logName)
            Result.success(Unit)
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            Result.failure(ex)
        }
    }

    private fun getStore(store: InAppSubscriptionResponseItemStoreTO) =
        when (store) {
            InAppSubscriptionResponseItemStoreTO.APP_STORE -> InAppSubscriptionStore.APP_STORE
            InAppSubscriptionResponseItemStoreTO.PLAY_STORE -> InAppSubscriptionStore.PLAY_STORE
            InAppSubscriptionResponseItemStoreTO.AMAZON,
            InAppSubscriptionResponseItemStoreTO.MAC_APP_STORE,
            InAppSubscriptionResponseItemStoreTO.PROMOTIONAL,
            InAppSubscriptionResponseItemStoreTO.STRIPE,
            InAppSubscriptionResponseItemStoreTO.RC_BILLING,
            -> TODO("Store $store not supported")
        }

    private fun <T> createHttpRequest(
        httpMethod: HttpMethod,
        uri: String,
        secretKey: String,
    ) =
        HttpRequest
            .create<T>(httpMethod, uri)
            .header("Authorization", "Bearer $secretKey")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
}

/*
normal: The product is in a normal period (default)
trial: The product is in a free trial period
intro: The product is in an introductory pricing period
*/
private enum class InAppSubscriptionResponseSubscriptionPeriodTypeTO {
    @JsonProperty("normal")
    NORMAL,

    @JsonProperty("trial")
    TRIAL,

    @JsonProperty("intro")
    INTRO,
}

private enum class InAppSubscriptionResponseItemStoreTO {
    @JsonProperty("app_store")
    APP_STORE,

    @JsonProperty("mac_app_store")
    MAC_APP_STORE,

    @JsonProperty("play_store")
    PLAY_STORE,

    @JsonProperty("amazon")
    AMAZON,

    @JsonProperty("stripe")
    STRIPE,

    @JsonProperty("promotional")
    PROMOTIONAL,

    @JsonProperty("rc_billing")
    RC_BILLING,
}

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class InAppSubscriptionResponseSubscriberEntitlementsClassicTO(
    val expiresDate: String,
    val gracePeriodExpiresDate: String?,
    val productIdentifier: String,
    val purchaseDate: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class InAppSubscriptionResponseSubscriberEntitlementsTO(
    val classic: InAppSubscriptionResponseSubscriberEntitlementsClassicTO?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class InAppSubscriptionResponseSubscriptionTO(
    val autoResumeDate: String?,
    val billingIssuesDetectedAt: String?,
    val expiresDate: String,
    val gracePeriodExpiresDate: String?,
    val isSandbox: Boolean,
    val originalPurchaseDate: String,
    val ownershipType: String?,
    val productPlanIdentifier: String?,
    val periodType: InAppSubscriptionResponseSubscriptionPeriodTypeTO,
    val purchaseDate: String,
    val refundedAt: String?,
    val store: InAppSubscriptionResponseItemStoreTO,
    val storeTransactionId: String,
    val unsubscribeDetectedAt: String?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class InAppSubscriptionResponseSubscriberTO(
    val entitlements: InAppSubscriptionResponseSubscriberEntitlementsTO,
    val firstSeen: String,
    val lastSeen: String,
    val managementUrl: String?,
    val nonSubscriptions: Map<String, Any>,
    val originalAppUserId: String,
    val originalApplicationVersion: String?,
    val originalPurchaseDate: String?,
    val otherPurchases: Map<String, Any>,
    val subscriberAttributes: Map<String, Any>?,
    val subscriptions: Map<String, InAppSubscriptionResponseSubscriptionTO>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class InAppSubscriptionResponseTO(
    val requestDate: String,
    val requestDateMs: Long,
    val subscriber: InAppSubscriptionResponseSubscriberTO,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class InAppSubscriptionErrorResponseTO(
    val message: String,
    val code: Long,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class InAppSubscriptionProductResponseTO(
    val items: List<InAppSubscriptionProductResponseItemTO>,
    val nextPage: String?,
    val url: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class InAppSubscriptionProductResponseItemTO(
    val appId: String,
    val createdAt: Long,
    val displayName: String?,
    val id: String,
    val storeIdentifier: String,
    val subscription: InAppSubscriptionProductResponseItemSubscriptionTO,
    val type: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class InAppSubscriptionProductResponseItemSubscriptionTO(
    val duration: String?,
    val gracePeriodDuration: String?,
    val trialDuration: String?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class CustomerTO(
    val id: String,
)