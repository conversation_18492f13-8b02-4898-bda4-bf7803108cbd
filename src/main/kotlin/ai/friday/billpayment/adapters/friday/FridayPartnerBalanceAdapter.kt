package ai.friday.billpayment.adapters.friday

import ai.friday.billpayment.adapters.api.AmountBalanceTO
import ai.friday.billpayment.adapters.api.CreateModattaPixResponseTO
import ai.friday.billpayment.adapters.api.InternalBankAccountTO
import ai.friday.billpayment.adapters.api.PixKeyRequestTO
import ai.friday.billpayment.adapters.api.RecipientBankDetailsTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.modatta.api.CreateModattaPixTO
import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.balance.Balance
import ai.friday.billpayment.app.banking.FridayBankAccountError
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.CreateBillError
import ai.friday.billpayment.app.modatta.CreateModattaPixRequest
import ai.friday.billpayment.app.modatta.integrations.FridayBalanceService
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Requires
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
@Requires(env = [MODATTA_ENV])
class FridayPartnerBalanceAdapter(
    @param:Client("\${integrations.friday.host}") private val httpClient: RxHttpClient,
    private val configuration: FridayPartnerConfiguration,
    private val credentialConfiguration: FridayPartnerCredentialConfiguration,
) : FridayBalanceService {
    override fun getBalance(fridayExternalId: ExternalId): Either<FridayBankAccountError, Balance> {
        val url = configuration.paths.balanceAmount.replace("{accountId}", fridayExternalId.value)
        val markers = Markers.append("url", url)

        return try {
            val httpRequest: HttpRequest<*> = HttpRequest.GET<AmountBalanceTO>(url)
                .basicAuth(credentialConfiguration.identity, credentialConfiguration.secret)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.exchange(
                httpRequest,
                Argument.of(AmountBalanceTO::class.java),
                Argument.of(ResponseTO::class.java),
            )

            val response = call.firstOrError().blockingGet()

            return response.body()?.let { amountBalanceTO ->
                Balance(amountBalanceTO.amount).right().also { result ->
                    markers.andAppend("result", result.map { it })
                    LOG.info(markers, "FridayBalanceService#getBalance")
                }
            } ?: FridayBankAccountError.ServerError(IllegalStateException("NO DATA AVAILABLE")).left()
        } catch (e: HttpClientResponseException) {
            val body = e.response.getBody(ResponseTO::class.java)

            if (body.isPresent) {
                with(body.get()) {
                    when (this.code) {
                        "404" -> FridayBankAccountError.FridayAccountNotFound.left()
                        "4001" -> FridayBankAccountError.FridayAccountClosed.left()
                        "502" -> FridayBankAccountError.ServerError(e).left()
                        else -> FridayBankAccountError.ServerError(e).left()
                    }
                }
            } else {
                FridayBankAccountError.ServerError(e).left()
            }.also { result ->
                markers.andAppend("result", result.getOrElse { it })
                LOG.error(markers, "FridayBalanceService#getBalance", e)
            }
        }
    }

    override fun getCashoutBankAccount(fridayExternalId: ExternalId): Either<FridayBankAccountError, BankAccount> {
        val url = configuration.paths.balanceAmount.replace("{accountId}", fridayExternalId.value)
        val markers = Markers.append("url", url)

        return try {
            val httpRequest: HttpRequest<*> = HttpRequest.GET<InternalBankAccountTO>(url)
                .basicAuth(credentialConfiguration.identity, credentialConfiguration.secret)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.exchange(
                httpRequest,
                Argument.of(InternalBankAccountTO::class.java),
                Argument.of(ResponseTO::class.java),
            )

            val response = call.firstOrError().blockingGet()

            return response.body()?.let { responseTO ->
                BankAccount(
                    accountType = responseTO.accountType,
                    bankNo = responseTO.bankNo,
                    routingNo = responseTO.routingNo,
                    accountNo = responseTO.accountNo.toBigInteger(),
                    accountDv = responseTO.accountDv,
                    document = "",
                    ispb = null,
                ).right().also { result ->
                    markers.andAppend("result", result.map { it })
                    LOG.info(markers, "FridayBalanceService#getCashoutBankAccount")
                }
            } ?: FridayBankAccountError.ServerError(IllegalStateException("NO DATA AVAILABLE")).left()
        } catch (e: HttpClientResponseException) {
            val body = e.response.getBody(ResponseTO::class.java)

            if (body.isPresent) {
                with(body.get()) {
                    when (this.code) {
                        "404" -> FridayBankAccountError.FridayAccountNotFound.left()
                        "4001" -> FridayBankAccountError.FridayAccountClosed.left()
                        "502" -> FridayBankAccountError.ServerError(e).left()
                        else -> FridayBankAccountError.ServerError(e).left()
                    }
                }
            } else {
                FridayBankAccountError.ServerError(e).left()
            }.also { result ->
                markers.andAppend("result", result.getOrElse { it })
                LOG.error(markers, "FridayBalanceService#getCashoutBankAccount", e)
            }
        }
    }

    override fun createPix(createModattaPixRequest: CreateModattaPixRequest): Either<Pair<String, String>, BillId> {
        val url = configuration.paths.createPix
        val markers = Markers.append("url", url)

        return try {
            val httpRequest: HttpRequest<*> = HttpRequest.POST(
                url,
                CreateModattaPixTO(
                    accountId = createModattaPixRequest.walletId.value,
                    pixKey = createModattaPixRequest.recipient.pixKey?.let {
                        PixKeyRequestTO(
                            createModattaPixRequest.recipient.pixKey.value,
                            createModattaPixRequest.recipient.pixKey.type,
                        )
                    },
                    bankDetails = createModattaPixRequest.recipient.bankAccount?.let {
                        RecipientBankDetailsTO(
                            accountType = it.accountType,
                            bankNo = it.bankNo,
                            routingNo = it.routingNo,
                            accountNo = it.accountNo,
                            accountDv = it.accountDv,
                            ispb = it.ispb,
                        )
                    },
                    amount = createModattaPixRequest.amount,
                    description = createModattaPixRequest.description,
                ),
            )
                .basicAuth(credentialConfiguration.identity, credentialConfiguration.secret)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.exchange(
                httpRequest,
                Argument.of(CreateModattaPixResponseTO::class.java),
                Argument.of(ResponseTO::class.java),
            )

            val response = call.firstOrError().blockingGet()

            return response.body()?.let { responseTO ->
                BillId(responseTO.billId).right().also { result ->
                    markers.andAppend("result", result.map { it })
                    LOG.info(markers, "FridayBalanceService#createPix")
                }
            } ?: Pair(CreateBillError.SERVER_ERROR.code, CreateBillError.SERVER_ERROR.description).left()
        } catch (e: HttpClientResponseException) {
            val body = e.response.getBody(ResponseTO::class.java)

            if (body.isPresent) {
                with(body.get()) {
                    Pair(this.code, this.message).left()
                }
            } else {
                Pair(CreateBillError.SERVER_ERROR.code, CreateBillError.SERVER_ERROR.description).left()
            }.also { result ->
                markers.andAppend("result", result.getOrElse { it })
                LOG.error(markers, "FridayBalanceService#createPix", e)
            }
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(FridayPartnerBalanceAdapter::class.java)
    }
}

@Requires(env = [MODATTA_ENV])
@ConfigurationProperties("integrations.friday")
data class FridayPartnerConfiguration @ConfigurationInject constructor(
    val host: String,
    val paths: FridayPartnerPathConfiguration,
) {
    @Requires(env = [MODATTA_ENV])
    @ConfigurationProperties("paths")
    data class FridayPartnerPathConfiguration
    @ConfigurationInject
    constructor(
        val balanceAmount: String,
        val simpleSignUpValidate: String,
        val simpleSignUp: String,
        val createPix: String,
        val accountStatus: String,
    )
}

@Requires(env = [MODATTA_ENV])
@ConfigurationProperties("modatta-b2b")
class FridayPartnerCredentialConfiguration @ConfigurationInject constructor(
    val identity: String,
    val secret: String,
)