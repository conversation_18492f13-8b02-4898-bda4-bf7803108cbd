package ai.friday.billpayment.adapters.friday

import ai.friday.billpayment.adapters.api.SimpleSignUpRequestTO
import ai.friday.billpayment.adapters.api.SimpleSignUpResponseTO
import ai.friday.billpayment.adapters.api.UserDataValidationRequestTO
import ai.friday.billpayment.adapters.api.UserDataValidationResponseTO
import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.modatta.integrations.PartnerSimpleSignUpService
import ai.friday.billpayment.app.modatta.register.PartnerSimpleSignUpException
import ai.friday.billpayment.app.modatta.register.PartnerSimpleSignUpRequest
import ai.friday.billpayment.app.modatta.register.PartnerSimpleSignUpResult
import ai.friday.billpayment.app.modatta.register.PartnerSimpleSignUpValidation
import ai.friday.billpayment.app.modatta.register.UserData
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Requires
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.HttpClientConfiguration
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.ApplicationConfiguration
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.net.URL
import java.time.Duration
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
@Requires(env = [MODATTA_ENV])
class FridayPartnerSimpleSignUpHttpConfiguration(
    applicationConfiguration: ApplicationConfiguration,
    val configuration: HttpClientConfiguration,
) : HttpClientConfiguration(applicationConfiguration) {
    init {
        setReadTimeout(Duration.ofMinutes(1))
    }

    override fun getConnectionPoolConfiguration(): ConnectionPoolConfiguration =
        configuration.connectionPoolConfiguration
}

@Singleton
@Requires(env = [MODATTA_ENV])
class FridayPartnerSimpleSignUpAdapter(
    httpConfiguration: FridayPartnerSimpleSignUpHttpConfiguration,
    private val configuration: FridayPartnerConfiguration,
    private val credentialConfiguration: FridayPartnerCredentialConfiguration,
) : PartnerSimpleSignUpService {

    private val httpClient = RxHttpClient.create(URL(configuration.host), httpConfiguration)

    override fun validate(userData: UserData): PartnerSimpleSignUpValidation {
        val markers = Markers.append("userData", userData)

        return try {
            val httpRequest: HttpRequest<*> = HttpRequest.PUT(
                configuration.paths.simpleSignUpValidate,
                UserDataValidationRequestTO(
                    document = userData.document.value,
                    mobilePhone = userData.mobilePhone.msisdn,
                ),
            ).basicAuth(credentialConfiguration.identity, credentialConfiguration.secret)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.exchange(
                httpRequest,
                Argument.of(UserDataValidationResponseTO::class.java),
                Argument.of(String::class.java),
            )

            val response = call.firstOrError().blockingGet()

            PartnerSimpleSignUpValidation(
                valid = response.body().valid,
                accountId = response.body().accountId?.let { AccountId(it) },
            ).also { result ->
                markers.andAppend("result", result)
                LOG.info(markers, "PartnerSimpleSignUpService#validate")
            }
        } catch (e: HttpClientResponseException) {
            LOG.error(markers, "PartnerSimpleSignUpService#validate", e)
            throw PartnerSimpleSignUpException(e)
        }
    }

    override fun signUp(request: PartnerSimpleSignUpRequest): PartnerSimpleSignUpResult {
        val markers = Markers.append("request", request)

        return try {
            val httpRequest: HttpRequest<*> = HttpRequest.POST(
                configuration.paths.simpleSignUp,
                request.toSimpleSignUpRequestTO(),
            ).basicAuth(credentialConfiguration.identity, credentialConfiguration.secret)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.exchange(
                httpRequest,
                Argument.of(SimpleSignUpResponseTO::class.java),
                Argument.of(String::class.java),
            )

            val response = call.firstOrError().blockingGet()

            response.body().toPartnerSimpleSignUpResult().also { result ->
                markers.andAppend("result", result)
                LOG.info(markers, "PartnerSimpleSignUpService#signUp")
            }
        } catch (e: HttpClientResponseException) {
            // FIXME: tratar erro específico de validação de documento e telefone
            LOG.error(markers, "PartnerSimpleSignUpService#signUp", e)
            throw PartnerSimpleSignUpException(e)
        }
    }

    override fun getAccountStatus(fridayExternalId: ExternalId): AccountStatus {
        val markers = Markers.append("fridayExternalId", fridayExternalId.value)

        val url = configuration.paths.accountStatus.replace("{accountId}", fridayExternalId.value)

        return try {
            val httpRequest: HttpRequest<*> =
                HttpRequest.GET<Unit>(url).basicAuth(credentialConfiguration.identity, credentialConfiguration.secret)
                    .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.exchange(
                httpRequest,
                Argument.of(String::class.java),
            )

            val response = call.firstOrError().blockingGet()

            LOG.info(markers.andAppend("response", response), "PartnerSimpleSignUpService#getAccountStatus")

            AccountStatus.valueOf(response.body()!!)
        } catch (e: HttpClientResponseException) {
            LOG.error(markers, "PartnerSimpleSignUpService#getAccountStatus", e)
            throw e
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(FridayPartnerSimpleSignUpAdapter::class.java)
    }
}

private fun SimpleSignUpResponseTO.toPartnerSimpleSignUpResult() = PartnerSimpleSignUpResult(
    accountId = AccountId(accountId),
)

private fun PartnerSimpleSignUpRequest.toSimpleSignUpRequestTO() = SimpleSignUpRequestTO(
    name = userData.name,
    document = userData.document.value,
    birthDate = userData.birthDate.format(dateFormat),
    email = userData.email.value,
    mobilePhone = userData.mobilePhone.msisdn,
    livenessId = livenessId.value,
    externalId = externalId.value,
    externalIdProvider = externalId.providerName.name,
    userContractKey = userContractKey,
    userContractSignature = userContractSignature,
)