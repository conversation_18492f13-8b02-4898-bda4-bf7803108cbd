package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionStatus
import ai.friday.billpayment.app.utilityaccount.UtilityAccountService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
open class UtilityAccountDailyUpdateJob(
    private val service: UtilityAccountService,
) : AbstractJob(cron = "0 18 * * *", lockAtMostFor = "30m") {
    override fun execute() {
        val logName = "UtilityAccountDailyUpdateJob#execute"
        try {
            logger.info(Markers.append("action", "start"), logName)
            process()
            logger.info(Markers.append("action", "finish"), logName)
        } catch (ex: Exception) {
            logger.error(logName, ex)
        }
    }

    private fun process() {
        val daysOffset = 5L
        service.findAllByStatus(UtilityAccountConnectionStatus.CONNECTED).filter {
            it.lastDueDateFound?.isBefore(getLocalDate().minusDays(daysOffset)) ?: true
        }.forEach { utilityAccount ->

            service.updateToInvalidCredentials(utilityAccount).also {
                if (it.status == UtilityAccountConnectionStatus.INVALID_CREDENTIALS) {
                    return@forEach
                }
            }

            service.notifyInvoicesNotFound(utilityAccount)
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(UtilityAccountDailyUpdateJob::class.java)
    }
}