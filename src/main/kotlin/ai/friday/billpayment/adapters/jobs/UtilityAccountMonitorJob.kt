package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.utilityaccount.UtilityAccountService

@FridayMePoupe
open class UtilityAccountMonitorJob(
    private val service: UtilityAccountService,
) : AbstractJob(cron = "15 14,20 * * 1-5") {
    override fun execute() {
        service.refreshAllConnectedUtilities()
    }
}