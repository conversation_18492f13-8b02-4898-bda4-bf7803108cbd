package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.notification.BillComingDueService
import ai.friday.billpayment.app.notification.NotifyBillComingDueType
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton

@Singleton
@Requirements(Requires(beans = [BillComingDueService::class]))
open class NotifyChatBotAiBillComingDueJob(private val billComingDueService: BillComingDueService) :
    AbstractJob(
        cron = "30 11,15 * * *",
        lockAtLeastFor = "30m",
    ) {
    override fun execute() {
        billComingDueService.addWalletsOnExecutionQueue(NotifyBillComingDueType.REGULAR_CHATBOT)
    }
}