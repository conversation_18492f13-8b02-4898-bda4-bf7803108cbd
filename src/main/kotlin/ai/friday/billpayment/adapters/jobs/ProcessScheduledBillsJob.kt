package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.payment.StartProcessScheduledBillsService
import io.micronaut.context.annotation.Property

@FridayMePoupe
open class ProcessExpirableScheduledBillsJob(
    private val startProcessScheduledBillsService: StartProcessScheduledBillsService,
    @Property(name = "schedules.startProcessScheduledBills.cron") cron: String,
) : AbstractJob(cron = cron) {
    override fun execute() {
        startProcessScheduledBillsService.start(includeSubscription = false)
    }
}

@FridayMePoupe
open class ProcessAllScheduledBillsJob(
    private val startProcessScheduledBillsService: StartProcessScheduledBillsService,
) : AbstractJob(
    crons = listOf("50 10 * * *", "10 02 * * *"),
) {
    override fun execute() {
        startProcessScheduledBillsService.start(includeSubscription = true)
    }
}