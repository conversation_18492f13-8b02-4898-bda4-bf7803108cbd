package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.reports.BankAccountReconciliationReportService
import ai.friday.billpayment.app.reports.FinancialReportService
import ai.friday.billpayment.app.reports.OmnibusAccountReportService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import java.util.Calendar
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
@Requires(beans = [SendFinancialReportsService::class])
open class SendBillComingDueReportJob(private val sendFinancialReportsService: SendFinancialReportsService) :
    AbstractJob(cron = "1 19 * * *") {
    override fun execute() {
        sendFinancialReportsService.execute()
    }
}

@Singleton
@Requires(beans = [SendFinancialReportsService::class])
open class OmnibusAccountReportJob(private val sendFinancialReportsService: SendFinancialReportsService) :
    AbstractJob(cron = "0 6 * * *") {
    override fun execute() {
        sendFinancialReportsService.executeOmnibusReport()
    }
}

@Singleton
@Requires(beans = [SendFinancialReportsService::class])
open class BankAccountReconciliationReportJob(
    private val sendFinancialReportsService: SendFinancialReportsService,
    @Property(name = "email.finance-report-recipients") private val recipient: String,
) : AbstractJob(
    // o monitor "divergência de valor movimentado na conta - https://app.datadoghq.com/monitors/********" se baseia no intervalo dessa crontab
    // e fecha o alarme se não aparecer inconsistencia pra mesma conta nas duas próximas conciliações (6 horas)
    cron = "30 0/3 * * *",
) {
    override fun execute() {
        sendFinancialReportsService.executeBankAccountReconciliationReport(getZonedDateTime(), recipient)
    }
}

@FridayMePoupe
open class SendFinancialReportsService(
    private val financialReportService: FinancialReportService,
    private val omnibusAccountReportService: OmnibusAccountReportService,
    private val bankAccountReconciliationReportService: BankAccountReconciliationReportService,
) {
    open fun execute() {
        val report = financialReportService.reportBillsComingDueWithin(Calendar.DAY_OF_WEEK.toLong())
        LOG.info(append("report", report), "SendBillComingDueReport")

        val (availableQuotaSum, cashInYesterdayTotal) = financialReportService.reportCreditCardCashInUsage()
        LOG.info(
            append("availableQuotaSum", availableQuotaSum)
                .andAppend("cashInYesterdayTotal", cashInYesterdayTotal.totalAmount)
                .andAppend("cashInYesterdayNetTotal", cashInYesterdayTotal.netAmount)
                .andAppend("cashInYesterdayFeeTotal", cashInYesterdayTotal.feeAmount),
            "CreditCardCashInUsageReport",
        )
    }

    open fun executeOmnibusReport() {
        val report = omnibusAccountReportService.report()
        LOG.info(append("report", report), "OmnibusAccountReport")
    }

    open fun executeBankAccountReconciliationReport(at: ZonedDateTime, recipient: String) {
        try {
            val (date, cutOffTime) =
                if (at.hour != 0) {
                    Pair(at.toLocalDate(), at.hour)
                } else {
                    Pair(
                        at.minusDays(1).toLocalDate(),
                        24,
                    )
                }

            LOG.info(append("date", date).andAppend("cutOffTime", cutOffTime), "BankAccountReconciliationReport")

            val totalArbiAccountsReportsQueued = bankAccountReconciliationReportService.generateArbiAccountsReportAsync(date = date, cutOffTime = cutOffTime)

            val (settlementAccountReport, cashInAccountReport, allBoletosCreditCardRefunded) =
                bankAccountReconciliationReportService.generateSettlementAccountReport(
                    date = date,
                    cutOffTime = cutOffTime,
                )

            LOG.info(
                append("totalArbiAccountsReportsQueued", totalArbiAccountsReportsQueued)
                    .andAppend("settlementAccountReport", settlementAccountReport)
                    .andAppend("allBoletosCreditCardRefunded", allBoletosCreditCardRefunded)
                    .andAppend("cashInAccountReport", cashInAccountReport),
                "BankAccountReconciliationReport",
            )

            bankAccountReconciliationReportService.sendEmailReport(
                date,
                cutOffTime,
                settlementAccountReport,
                cashInAccountReport,
                allBoletosCreditCardRefunded,
                recipient,
            )
        } catch (e: Exception) {
            LOG.error("BankAccountReconciliationReport", e)
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SendFinancialReportsService::class.java)
    }
}