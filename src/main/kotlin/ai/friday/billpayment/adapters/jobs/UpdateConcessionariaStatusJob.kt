package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.feature.FeaturesRepository
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.log.andAppend
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@FridayMePoupe
open class UpdateConcessionariaStatusJob(
    private val findBillService: FindBillService,
    private val messagePublisher: MessagePublisher,
    private val featuresRepository: FeaturesRepository,
    private val configuration: SQSMessageHandlerConfiguration,
) : AbstractJob(
    cron = "9 10,19-21 * * *",
) {
    private val logger = LoggerFactory.getLogger(UpdateConcessionariaStatusJob::class.java)

    override fun execute() {
        if (featuresRepository.getAll().maintenanceMode) {
            logger.info(append("jobExcecutionSkipped", "maintenanceMode"), "UpdateConcessionariaStatusJob")
            return
        }

        findBillService.findAllWallets(
            filterAccountsThatReceiveNotification = false,
            filterActiveAccounts = true,
        ).forEach {
            it.queueOverdueBills()
        }
    }

    private fun Wallet.queueOverdueBills() {
        val markers = append("walletId", id.value)

        try {
            val overdueBills = findBillService.findOverdueBills(
                walletId = id,
            ).filter {
                it.isOverdue && it.billType == BillType.CONCESSIONARIA
            }
            markers.andAppend("overdueBills", overdueBills.map { it.billId.value })

            overdueBills.forEach {
                messagePublisher.sendMessage(
                    queueName = configuration.updateConcessionariaStatusQueueName,
                    body = UpdateConcessionariaStatusMessage(
                        billId = it.billId.value,
                        walletId = it.walletId.value,
                    ),
                )
            }

            logger.debug(markers, "UpdateConcessionariaStatusJob")
        } catch (e: Exception) {
            logger.error(markers, "UpdateConcessionariaStatusJob")
        }
    }
}

data class UpdateConcessionariaStatusMessage(
    val billId: String,
    val walletId: String,
)

@FridayMePoupe
open class UpdateConcessionariaStatusJobHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val findBillService: FindBillService,
    private val boletoSettlementService: BoletoSettlementService,
    private val updateBillService: UpdateBillService,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = configuration.updateConcessionariaStatusQueueName,
) {
    private val logger = LoggerFactory.getLogger(UpdateConcessionariaStatusJobHandler::class.java)

    override fun handleMessage(message: Message): SQSHandlerResponse {
        val updateConcessionariaStatusMessage = parseObjectFrom<UpdateConcessionariaStatusMessage>(message.body())
        val markers = append("messageBody", updateConcessionariaStatusMessage)

        val overdueBill = findBillService.find(
            walletId = WalletId(updateConcessionariaStatusMessage.walletId),
            billId = BillId(updateConcessionariaStatusMessage.billId),
        )
        markers.andAppend("originalAmountTotal", overdueBill.amountTotal)

        val billValidationResponse = boletoSettlementService.validateBill(overdueBill)
        markers.andAppend("billValidationResponse", billValidationResponse)

        val response = updateBillService.synchronizeBill(overdueBill.billId, billValidationResponse)
        markers.andAppend("inSync", response.alreadyInSync())

        logger.info(markers, "UpdateConcessionariaStatusJobHandler")

        return SQSHandlerResponse(true)
    }

    override fun handleError(message: Message, e: Exception): SQSHandlerResponse {
        logger.error(append("messageBody", message.body()), "UpdateConcessionariaStatusJobHandler", e)
        return SQSHandlerResponse(false)
    }
}