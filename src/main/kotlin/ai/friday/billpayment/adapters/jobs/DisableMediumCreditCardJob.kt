package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.DefaultCreditCardService
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.app.integrations.CreditCardScore
import ai.friday.billpayment.app.integrations.CreditCardScoreService
import ai.friday.billpayment.app.integrations.CreditCardScoreValidationRequest
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import java.time.LocalDate
import java.util.stream.Collectors
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
@Requirements(Requires(notEnv = ["staging"]), Requires(env = [FRIDAY_ENV]))
open class DisableMediumCreditCardJob(
    private val accountRepository: AccountDbRepository,
    private val creditCardScoreService: CreditCardScoreService,
    private val creditCardService: DefaultCreditCardService,
    @Property(name = "schedules.disableMediumCreditCardJob.cron") cron: String,
) : AbstractJob(
    cron = cron,
    lockAtLeastFor = "1m",
    lockAtMostFor = "10m",
) {
    override fun execute() {
        val accounts = accountRepository.findAllAccountsActivatedSince(LocalDate.EPOCH).filter { it.status == AccountStatus.ACTIVE }

        val creditCards =
            accounts.parallelStream().map {
                accountRepository.findCreditCardsByAccountIdAndStatus(
                    it.accountId,
                    AccountPaymentMethodStatus.ACTIVE,
                )
            }.collect(Collectors.toList()).flatten()

        println("#creditCards found: ${creditCards.size}")
        creditCards.parallelStream()
            .forEach { paymentMethod ->
                val markers = Markers.empty()
                try {
                    val account = accountRepository.findById(paymentMethod.accountId)
                    val creditCard =
                        creditCardService.find(
                            accountId = paymentMethod.accountId,
                            accountPaymentMethodId = paymentMethod.id,
                        )

                    if (creditCard.riskLevel == RiskLevel.LOW) {
                        LOG.info(
                            append("creditcardSkippedDueRiskLevel", paymentMethod.id.value)
                                .andAppend("riskLevel", creditCard.riskLevel),
                            "DisableMediumCreditCardJob",
                        )
                        return@forEach
                    }

                    creditCardScoreService.creditcardScore(
                        CreditCardScoreValidationRequest(
                            bin = creditCard.bin,
                            lastFourDigits = creditCard.lastFourDigits,
                            cpf = account.document,
                            totalValue = "0",
                        ),
                    ).map { score ->
                        // no NO_MATCH, no MANUAL_CHECK => MEDIUM, LOW
                        if (score != CreditCardScore.MATCH) {
                            accountRepository.deleteAccountPaymentMethod(
                                accountId = paymentMethod.accountId,
                                accountPaymentMethodId = paymentMethod.id,
                            )

                            LOG.info(
                                append("creditcardDisabled", paymentMethod.id.value),
                                "DisableMediumCreditCardJob",
                            )
                        } else {
                            accountRepository.updateCreditCardPaymentMethodRisk(account.accountId, paymentMethod.id, RiskLevel.LOW)

                            LOG.info(
                                append("creditcardRiskLow", paymentMethod.id.value),
                                "DisableMediumCreditCardJob",
                            )
                        }
                    }
                } catch (e: Exception) {
                    println(e)
                }
            }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(DDARegisterJob::class.java)
    }
}