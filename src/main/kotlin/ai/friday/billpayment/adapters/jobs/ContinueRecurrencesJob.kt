package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.recurrence.BillRecurrenceService
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
open class ContinueRecurrencesJob(
    private val recurrenceService: BillRecurrenceService,
) : AbstractJob(
    cron = "0 16 15 * *",
    lockAtLeastFor = "10m",
    lockAtMostFor = null,
    shutdownGracefully = false,
    shutdownGracefullyMaxWaitTime = 0,
) {
    override fun execute() {
        LOG.info(Markers.append("step", "begin"), "UpdateRecurrenceJob")
        recurrenceService.continueRecurrences()
        LOG.info(Markers.append("step", "end"), "UpdateRecurrenceJob")
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ContinueRecurrencesJob::class.java)
    }
}