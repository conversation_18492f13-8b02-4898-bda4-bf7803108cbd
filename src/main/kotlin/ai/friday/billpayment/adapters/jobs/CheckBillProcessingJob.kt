package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
@Requirements(Requires(notEnv = ["staging"]), Requires(env = [FRIDAY_ENV, ME_POUPE_ENV]))
open class CheckBillProcessingJob(
    private val billRepository: BillRepository,
) : AbstractJob(
    cron = "*/5 * * * *",
    lockAtLeastFor = "1m",
    lockAtMostFor = "10m",
) {
    override fun execute() {
        try {
            val stuckedProcessingBills = billRepository.getStuckedProcessingBills(
                seconds = 1200,
            ).map {
                it.billId
            }

            val markers = Markers.append("total", stuckedProcessingBills.size)
                .andAppend("billIds", stuckedProcessingBills)

            if (stuckedProcessingBills.isEmpty()) {
                LOG.info(markers, "CheckBillProcessingJob")
            } else {
                LOG.warn(markers, "CheckBillProcessingJob")
            }
        } catch (e: Exception) {
            LOG.error("CheckBillProcessingJob", e)
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(CheckBillProcessingJob::class.java)
    }
}