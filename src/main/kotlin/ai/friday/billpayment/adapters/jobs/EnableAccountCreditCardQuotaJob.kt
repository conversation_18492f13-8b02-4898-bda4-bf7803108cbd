package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.SystemActivityEntity
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.SystemActivityKeyType
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.account.SystemActivityType
import ai.friday.billpayment.app.feature.FeaturesRepository
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import java.util.stream.Stream
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

// @Requires(notEnv = ["test"])
// @Friday
open class EnableAccountCreditCardQuotaJob(
    private val featuresRepository: FeaturesRepository,
    private val systemActivityService: SystemActivityService,
    private val accountService: AccountService,
) : AbstractJob(
    cron = "0 0 30 2 *",
) {
    override fun execute() {
        if (featuresRepository.getAll().maintenanceMode) {
            LOG.info(append("jobExcecutionSkipped", "maintenanceMode"), "EnableAccountCreditCardQuotaJob")
            return
        }

        val found =
            systemActivityService.findFirstOwnTrustedBillPaidBetween(
                from = getZonedDateTime().minusDays(37).toLocalDate().atStartOfDay(brazilTimeZone),
                to =
                getZonedDateTime().minusDays(30).toLocalDate().atStartOfDay(brazilTimeZone).plusDays(1)
                    .minusSeconds(1),
            )

        val totalCreated =
            found.map { accountId ->
                val account = accountService.findAccountById(accountId)
                if (!account.hasCreditCardEnabled() && account.status == AccountStatus.ACTIVE) {
                    accountService.enableCreditCardUsage(accountId = accountId)
                        .map {
                            LOG.info(
                                append("accountId", accountId.value).andAppend("creditCardEnabled", true),
                                "EnableAccountCreditCardQuotaJob",
                            )
                            true
                        }.getOrElse {
                            LOG.error(
                                append("accountId", accountId.value).andAppend("creditCardEnabled", false),
                                "EnableAccountCreditCardQuotaJob",
                                it,
                            )
                            false
                        }
                } else {
                    false
                }
            }.count { it }

        LOG.info(
            append("totalCreated", totalCreated).andAppend(
                "totalFound",
                found.size,
            ),
            "EnableAccountCreditCardQuotaJob",
        )
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(EnableAccountCreditCardQuotaJob::class.java)
    }
}

// @Requires(notEnv = ["test"])
// @Friday
open class EnableAccountCreditCardManualJob(
    private val systemActivityService: SystemActivityService,
    private val accountService: AccountService,
    private val dynamoDbDAO: DynamoDbDAO,
) : AbstractJob(
    cron = "0 0 30 2 *",
) {
    data class AccountHelper(val value: String, val accountService: AccountService) {
        val accountId = AccountId(value)

        var account: Account? = null

        fun isEligible(): Boolean {
            return try {
                if (account == null) {
                    LOG.info(Markers.append("accountId", value), "findAccountById")
                    account = accountService.findAccountById(accountId)
                }
                account!!.status == AccountStatus.ACTIVE && !account!!.hasCreditCardEnabled()
            } catch (e: AccountNotFoundException) {
                false
            }
        }
    }

    private fun Stream<AccountHelper>.filterUntil(max: Int): Stream<AccountHelper> {
        var count = 0

        return this.takeWhile {
            if (it.isEligible()) {
                LOG.info(Markers.append("accountId", it.value).andAppend("count", count), "isEligible")
                count++
                true
            } else {
                count < max
            }
        }.filter {
            it.isEligible()
        }
    }

    private fun buildScanKey(
        keyType: SystemActivityKeyType,
        type: SystemActivityType,
    ): String {
        val scanKeyPrefix =
            when (keyType) {
                SystemActivityKeyType.Account -> "ACCOUNT_ACTIVITY"
                SystemActivityKeyType.Document -> "DOCUMENT_ACTIVITY"
                SystemActivityKeyType.Wallet -> "WALLET_ACTIVITY"
                SystemActivityKeyType.System -> "SYSTEM_ACTIVITY"
            }
        return "$scanKeyPrefix#${type.name}"
    }

    private fun findAllAccountIds(): List<AccountHelper> {
        val allActivities =
            findActivities(
                SystemActivityKeyType.Account,
                SystemActivityType.HasScheduledBill,
            ) +
                findActivities(
                    SystemActivityKeyType.Wallet,
                    SystemActivityType.HasCashedIn,
                ) +
                findActivities(
                    SystemActivityKeyType.Account,
                    SystemActivityType.ViewedBillingCarousel,
                ) +
                findActivities(
                    SystemActivityKeyType.Account,
                    SystemActivityType.AccountActivated,
                ) +
                findActivities(
                    SystemActivityKeyType.Account,
                    SystemActivityType.BillPaidOnOwnWallet,
                ) +
                findActivities(
                    SystemActivityKeyType.Account,
                    SystemActivityType.DDABillPaid,
                ) +
                findActivities(
                    SystemActivityKeyType.Account,
                    SystemActivityType.FirstOwnTrustedBillPaid,
                )

        return allActivities.sortedBy {
            it.lastUpdated
        }.reversed().distinct().map { entity ->
            AccountHelper(entity.systemId, accountService)
        }
    }

    private fun findActivities(
        keyType: SystemActivityKeyType,
        type: SystemActivityType,
    ): List<SystemActivityEntity> {
        return dynamoDbDAO.queryIndexOnHashKeyValue(
            buildScanKey(keyType, type),
            SystemActivityEntity::class.java,
        )
    }

    override fun execute() {
        val accountIds = findAllAccountIds()

        val totalNoCreditCard = accountIds.stream().filterUntil(100)

        val markers = Markers.empty()

        val totalCreated =
            totalNoCreditCard.map { accountId ->
                accountService.enableCreditCardUsage(accountId = accountId.accountId)
                    .map {
                        LOG.info(
                            Markers.append("accountId", accountId).andAppend("creditCardEnabled", true),
                            "EnableAccountCreditCardManualJob",
                        )
                        true
                    }.getOrElse {
                        LOG.error(
                            Markers.append("accountId", accountId).andAppend("creditCardEnabled", false),
                            "EnableAccountCreditCardManualJob",
                            it,
                        )
                        false
                    }
            }.filter {
                it
            }.count()

        markers.andAppend("total de cartões habilitados", totalCreated)

        LOG.info(markers, "EnableAccountCreditCardManualJob")
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(EnableAccountCreditCardQuotaJob::class.java)
    }
}