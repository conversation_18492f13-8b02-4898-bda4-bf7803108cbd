package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.adapters.arbi.ArbiAdapter
import ai.friday.billpayment.adapters.celcoin.CelcoinAdapter
import ai.friday.billpayment.app.feature.RequiresCelcoin
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.metrics.AbstractSummary
import ai.friday.billpayment.app.metrics.push
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

private const val SCHEDULE = "*/5 * * * *"

@Singleton
@RequiresCelcoin
open class CelcoinConciliationJob(private val celcoinAdapter: CelcoinAdapter) :
    AbstractJob(cron = SCHEDULE) {
    override fun execute() {
        getCelcoinConciliationJob()
    }

    private fun getCelcoinConciliationJob() {
        celcoinAdapter.getBalanceAmount().fold(
            ifLeft = {
                LOG.error("CelcoinConciliationJob#getBalance", it)
            },
            ifRight = {
                LOG.info(
                    append("conciliation.balance", it),
                    "CelcoinConciliationJob#getBalance",
                )
            },
        )
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(CelcoinConciliationJob::class.java)
    }
}

@Singleton
open class ArbiConciliationJob(
    private val arbiAdapter: ArbiAdapter,
    @Property(name = "integrations.arbi.contaCashin") private val creditCardCashinSourceAccount: String,
    @Property(name = "integrations.arbi.contaLiquidacao") private val settlementAccountNo: String,
) :
    AbstractJob(cron = SCHEDULE) {
    override fun execute() {
        val settlementBalance = getAccountBalance(settlementAccountNo, "ArbiConciliationJob#getBalance")
        ArbiSettlementBalance.push(mapOf("account" to settlementAccountNo, "settlementProvider" to "arbi"), settlementBalance)
        if (creditCardCashinSourceAccount != "0") {
            val cashInBalance = getAccountBalance(creditCardCashinSourceAccount, "ArbiConciliationJob#getCashInAccountBalance")
            ArbiCashInBalance.push(mapOf("account" to creditCardCashinSourceAccount, "cashInProvider" to "arbi"), cashInBalance)
        }
    }

    private fun getAccountBalance(
        accountNo: String,
        logName: String,
    ): Long {
        try {
            return arbiAdapter.getBalance(accountNo).also {
                LOG.info(
                    append("conciliation.balance", it),
                    logName,
                )
            }
        } catch (e: Exception) {
            LOG.error(logName, e)
            throw e
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ArbiConciliationJob::class.java)
    }
}

private object ArbiSettlementBalance : AbstractSummary()

private object ArbiCashInBalance : AbstractSummary()