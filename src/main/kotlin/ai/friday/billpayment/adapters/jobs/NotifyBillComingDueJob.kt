package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.notification.BillComingDueService
import ai.friday.billpayment.app.notification.NotifyBillComingDueType
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton

@Singleton
@Requirements(Requires(beans = [BillComingDueService::class]))
open class NotifyBillComingDueJob(private val billComingDueService: BillComingDueService) :
    AbstractJob(
        cron = "30 11-12 * * *",
        lockAtLeastFor = "30m",
    ) {
    override fun execute() {
        billComingDueService.addWalletsOnExecutionQueue(NotifyBillComingDueType.REGULAR)
    }
}

@Singleton
@Requirements(Requires(beans = [BillComingDueService::class]))
open class NotifyBillComingDueLastWarnJob(private val billComingDueService: BillComingDueService) :
    AbstractJob(
        cron = "10 17-21 * * *",
        lockAtLeastFor = "30m",
    ) {
    override fun execute() {
        billComingDueService.addWalletsOnExecutionQueue(NotifyBillComingDueType.LAST_WARN)
    }
}

@Singleton
@Requirements(Requires(beans = [BillComingDueService::class]))
open class NotifyBillComingDueLastWarnRestOfDayJob(private val billComingDueService: BillComingDueService) :
    AbstractJob(
        cron = "10 22 * * *",
        lockAtLeastFor = "30m",
    ) {
    override fun execute() {
        billComingDueService.addWalletsOnExecutionQueue(NotifyBillComingDueType.LAST_WARN_REST_OF_DAY)
    }
}

@Singleton
@Requirements(Requires(beans = [BillComingDueService::class]))
open class NotifyBillOverdueYesterdayJob(private val billComingDueService: BillComingDueService) :
    AbstractJob(
        cron = "0 11 * * *",
        lockAtLeastFor = "30m",
    ) {
    override fun execute() {
        billComingDueService.addWalletsOnExecutionQueue(NotifyBillComingDueType.YESTERDAY)
    }
}