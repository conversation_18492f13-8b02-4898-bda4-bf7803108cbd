package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.feature.FeaturesRepository
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.reports.SettlementAndCashinAccountReportService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import io.micronaut.context.annotation.Requires
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.QueryValue
import io.micronaut.security.annotation.Secured
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Requires(notEnv = ["staging"])
@Controller("/reports")
@FridayMePoupe
open class SettlementAccountReportJob(
    private val settlementAndCashinAccountReportService: SettlementAndCashinAccountReportService,
    private val featuresRepository: FeaturesRepository,
) : AbstractJob(
    cron = "00 08 * * *",
) {
    override fun execute() {
        generateReport(getZonedDateTime().minusDays(1))
    }

    @Secured(BACKOFFICE)
    @Get("/settlementaccount{?date}")
    fun getSettlementAccountReport(@QueryValue date: String?): HttpResponse<Unit> {
        val parsedDate = date?.let {
            LocalDate.parse(date, DateTimeFormatter.ISO_DATE).atStartOfDay(brazilTimeZone)
        } ?: getZonedDateTime().minusDays(1)

        generateReport(parsedDate)
        return HttpResponse.ok<Unit>()
    }

    private fun generateReport(date: ZonedDateTime) {
        if (featuresRepository.getAll().maintenanceMode) {
            LOG.info(Markers.append("jobExcecutionSkipped", "maintenanceMode"), "SettlementAccountReport")
            return
        }
        val report = settlementAndCashinAccountReportService.reportSettlementAndCashinAccountTotals(date)
        LOG.info(
            Markers.append("report", report).and<LogstashMarker>(Markers.append("date", date)),
            "SettlementAccountReport",
        )
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SettlementAccountReportJob::class.java)
    }
}