package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.statement.StatementService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
open class BiWeeklyAccountStatementJob(
    private val statementService: StatementService,
    private val walletService: WalletService,
) : AbstractJob("15 14 1,16 * *") {
    override fun execute() {
        process()
    }

    open fun process() {
        val accountList = listOf(AccountId("ACCOUNT-3dc5eb06-71c3-4442-8f9c-3ae65a862612"))

        val markers = Markers.append("accountsSize", accountList.size)
        accountList.forEach { accountId ->
            markers.andAppend("accountId", accountId.value)
            try {
                val wallet = walletService.findPrimaryWallet(accountId)
                val result = if (getLocalDate().dayOfMonth == 16) {
                    statementService.requestStatement(
                        walletId = wallet.id,
                        accountId = accountId,
                        startDate = getLocalDate().withDayOfMonth(1),
                        endDate = getLocalDate().withDayOfMonth(15),
                    )
                } else {
                    statementService.requestStatement(
                        walletId = wallet.id,
                        accountId = accountId,
                        startDate = getLocalDate().minusMonths(1).withDayOfMonth(16),
                        endDate = getLocalDate().withDayOfMonth(1).minusDays(1),
                    )
                }
                result.map {
                    logger.info(markers.andAppend("requested", true), "BiWeeklyAccountStatementJob#process")
                }.getOrElse {
                    logger.error(markers, "BiWeeklyAccountStatementJob#process", it)
                }
            } catch (e: Exception) {
                logger.error(markers, "BiWeeklyAccountStatementJob#process", e)
            }
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(BiWeeklyAccountStatementJob::class.java)
    }
}