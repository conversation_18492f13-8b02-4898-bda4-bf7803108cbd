package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.reports.NewAccountReportService
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
@Requirements(Requires(notEnv = ["staging"]), Requires(env = [FRIDAY_ENV, ME_POUPE_ENV]))
open class NewAccountReportJob(private val newAccountReportService: NewAccountReportService) : AbstractJob(
    cron = "0 12-23 * * 1-5",
) {
    override fun execute() {
        try {
            val report = newAccountReportService.build()
            LOG.info(append("size", report.size), "NewAccountReportJob")
        } catch (e: Exception) {
            LOG.error("NewAccountReportJob", e)
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(NewAccountReportJob::class.java)
    }
}