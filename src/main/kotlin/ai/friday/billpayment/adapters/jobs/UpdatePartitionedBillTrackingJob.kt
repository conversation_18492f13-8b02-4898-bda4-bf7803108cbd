package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.adapters.concurrency.scheduler
import ai.friday.billpayment.adapters.lock.billTrackingCalculate
import ai.friday.billpayment.adapters.lock.billTrackingQuery
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.and
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.bill.BillTrackingCalculateOptions
import ai.friday.billpayment.app.bill.TrackableBill
import ai.friday.billpayment.app.bill.tracking.BillTrackingPropertiesBinder
import ai.friday.billpayment.app.feature.FeaturesRepository
import ai.friday.billpayment.app.integrations.BillTrackingRepository
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.message.QueueMessageBatch
import ai.friday.billpayment.measure
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import jakarta.annotation.PreDestroy
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.time.Duration
import java.time.LocalDateTime
import java.util.concurrent.ExecutorService
import java.util.concurrent.atomic.AtomicInteger
import java.util.stream.IntStream
import kotlin.streams.toList
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.runBlocking
import net.javacrumbs.shedlock.core.SimpleLock
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import parallelMap

@FridayMePoupe
open class UpdatePartitionedBillTrackingCalculateJob(
    private val updatePartitionedBillTracking: UpdatePartitionedBillTracking,
) : AbstractJob(cron = "0/10 4-9 * * 1-5", shouldLock = false) {
    override fun execute() {
        updatePartitionedBillTracking.execute(BillTrackingCalculateOptions.CALCULATE)
    }
}

@FridayMePoupe
@Requires(notEnv = ["test"])
open class UpdatePartitionedBillTrackingQueryJob(
    private val updatePartitionedBillTracking: UpdatePartitionedBillTracking,
) : AbstractJob(cron = "0/10 3-9 * * 1-5", shouldLock = false) {
    override fun execute() {
        updatePartitionedBillTracking.execute(BillTrackingCalculateOptions.QUERY)
    }
}

@Singleton
@Requirements(Requires(env = [FRIDAY_ENV, ME_POUPE_ENV]))
class UpdatePartitionedBillTracking(
    private val properties: BillTrackingPropertiesBinder,
    private val billTrackingRepository: BillTrackingRepository,
    private val messagePublisher: MessagePublisher,
    private val featuresRepository: FeaturesRepository,
    @Named(billTrackingCalculate) private val calculateLocker: InternalLock,
    @Named(billTrackingQuery) private val queryLocker: InternalLock,
    @Named("jobExecutor") val jobExecutor: ExecutorService,
) {
    private var lock: SimpleLock? = null
    private var isInitialized = false

    fun execute(option: BillTrackingCalculateOptions) {
        val markers = Markers.empty().and("start" to LocalDateTime.now()).andAppend("type", option)

        if (featuresRepository.getAll().maintenanceMode) {
            LOG.info(markers, "UpdatePartitionedBillTrackingJob#maintenance_mode")
            return
        }

        val (possibleLock, partition) = getLockAndPartition(option, markers)

        if (possibleLock == null) {
            LOG.info(markers, "UpdatePartitionedBillTrackingJob#no_partitions_available")
            return
        }

        this.lock = possibleLock
        this.isInitialized = true

        LOG.info(markers.and("partition" to partition), "UpdatePartitionedBillTrackingJob#execute")

        val bills = findBillsToCalculate(option, partition).publishOn(jobExecutor.scheduler())
        val count = AtomicInteger(0)

        measure {
            bills.buffer(1_000)
                .doOnError { LOG.error(markers, "UpdatePartitionedBillTrackingJob#error", it) }
                .doOnNext {
                    runBlocking(jobExecutor.asCoroutineDispatcher()) {
                        it.chunked(10).parallelMap {
                            count.addAndGet(it.size)
                            process(it, option)
                        }
                    }
                }.blockLast()
        }.also { (_, elapsed) ->
            Duration.ofMillis(elapsed).let {
                markers.and(
                    "elapsedTime" to "${it.toMinutesPart()}m ${it.toSecondsPart()}s",
                    "end" to LocalDateTime.now(),
                    "query_size" to count.get(),
                )
            }
        }

        this.lock = null
        this.isInitialized = false

        LOG.info(markers, "UpdatePartitionedBillTrackingJob#execute")
    }

    internal fun process(
        trackableBills: List<TrackableBill>,
        option: BillTrackingCalculateOptions,
    ) {
        val markers = Markers.append("chunk", trackableBills).and("chunk_size" to trackableBills.size)

        try {
            val messages = trackableBills.map { getObjectMapper().writeValueAsString(it) }
            val (_, elapsed) =
                measure {
                    val queueName = getQueueName(option)

                    messagePublisher.sendMessageBatch(QueueMessageBatch(queueName, messages))
                }
            LOG.debug(markers.and("elapsed_time" to elapsed), "UpdatePartitionedBillTrackingJob#process")
        } catch (ex: Exception) {
            LOG.error(
                markers.and("error_time" to LocalDateTime.now()),
                "UpdatePartitionedBillTrackingJob#process",
                ex,
            )
        }
    }

    private fun findBillsToCalculate(
        option: BillTrackingCalculateOptions,
        partition: Int,
    ) = billTrackingRepository.findBillsByPartition(
        option,
        getLocalDate(),
        partition,
    )

    private fun getLockAndPartition(
        option: BillTrackingCalculateOptions,
        markers: LogstashMarker,
    ): Pair<SimpleLock?, Int> {
        val locker = getLocker(option)
        val partitions = getPartitions(option)
        val lockAtMostFor = getLockTimeWindow()

        var possibleLock: SimpleLock? = null
        var partition = 0

        for (part in partitions) {
            possibleLock = locker.acquireLock("$part", maxDuration = lockAtMostFor)
            if (possibleLock != null) {
                partition = part
                break
            }
        }

        markers.and("partition" to partition, "lockAtMostFor" to lockAtMostFor, "lock" to possibleLock.toString())

        return possibleLock to partition
    }

    private fun getLocker(option: BillTrackingCalculateOptions) =
        if (option == BillTrackingCalculateOptions.CALCULATE) calculateLocker else queryLocker

    private fun getPartitions(option: BillTrackingCalculateOptions): List<Int> {
        val partitionsRange =
            if (option == BillTrackingCalculateOptions.CALCULATE) properties.calculate.partitions else properties.query.partitions

        return IntStream.rangeClosed(1, partitionsRange).toList()
    }

    private fun getQueueName(option: BillTrackingCalculateOptions) =
        if (option == BillTrackingCalculateOptions.CALCULATE) properties.calculate.queue else properties.query.queue

    @PreDestroy
    fun preDestroy() {
        val markers = Markers.empty().and("pre_destroy_time" to LocalDateTime.now())

        if (!this.isInitialized) {
            LOG.info(markers, "UpdatePartitionedBillTrackingJob#preDestroy#nothing_to_do")
            return
        }
        lock?.unlock()
        LOG.info(markers, "UpdatePartitionedBillTrackingJob#pre_destroy")
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(UpdatePartitionedBillTracking::class.java)

        fun getLockTimeWindow(): Duration = Duration.ofHours(7)
    }
}