package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.conciliation.BoletoConciliationService
import ai.friday.billpayment.app.feature.RequiresCelcoin
import ai.friday.billpayment.app.job.AbstractJob
import jakarta.inject.Singleton

@Singleton
@RequiresCelcoin
open class BoletoConciliationCheckOccurrencesJob(
    private val boletoConciliationService: BoletoConciliationService,
) : AbstractJob(
    cron = "32 11-23 * * 1-5",
    lockAtLeastFor = "30m",
    lockAtMostFor = "30m",
) {
    override fun execute() {
        boletoConciliationService.checkOccurrences()
    }
}