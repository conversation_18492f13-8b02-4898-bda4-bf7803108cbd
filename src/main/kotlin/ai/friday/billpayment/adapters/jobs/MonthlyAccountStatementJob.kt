package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.statement.StatementService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
open class MonthlyAccountStatementJob(
    private val statementService: StatementService,
    private val accountService: AccountService,
    private val walletService: WalletService,
) : AbstractJob("15 13 1 * *") {
    override fun execute() {
        process()
    }

    open fun process() {
        val accountListWithStatementEnabled = accountService.findAllUsersWithMonthlyAccountStatementEnabled()
        val accountListAplha = accountService.findAllUsersWithAlphaGroup()

        val accountList = (accountListAplha + accountListWithStatementEnabled).toSet().toList()

        val markers = Markers.append("accountsSize", accountList.size)
        accountList.forEach { account ->
            markers.andAppend("accountId", account.accountId.value)
            try {
                val wallet = walletService.findPrimaryWallet(account.accountId)
                statementService.requestStatement(
                    walletId = wallet.id,
                    accountId = account.accountId,
                    startDate = getLocalDate().minusMonths(1).withDayOfMonth(1),
                    endDate = getLocalDate().withDayOfMonth(1).minusDays(1),
                ).map {
                    logger.info(markers.andAppend("requested", true), "MonthlyAccountStatementJob#process")
                }.getOrElse {
                    logger.error(markers, "MonthlyAccountStatementJob#process", it)
                }
            } catch (e: Exception) {
                logger.error(markers, "MonthlyAccountStatementJob#process", e)
            }
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(MonthlyAccountStatementJob::class.java)
    }
}