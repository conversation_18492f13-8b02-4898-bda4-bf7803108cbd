package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.onboarding.OnboardingTestPixService
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@FridayMePoupe
open class SynchronizeOnboardingTestPixJob(private val onboardingTestPixService: OnboardingTestPixService) : AbstractJob(
    cron = "0 9 * * *",
    lockAtLeastFor = "15m",
) {
    override fun execute() {
        try {
            val size = onboardingTestPixService.synchronizeAll()
            LOG.info(append("size", size), "SynchronizeOnboardingTestPixJob")
        } catch (e: Exception) {
            LOG.error("SynchronizeOnboardingTestPixJob", e)
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SynchronizeOnboardingTestPixJob::class.java)
    }
}