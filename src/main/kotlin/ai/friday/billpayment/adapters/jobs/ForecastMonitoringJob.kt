package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.integrations.PaymentSchedulingForecastService
import ai.friday.billpayment.app.integrations.ScheduleForecastResult
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.metrics.AbstractSummary
import ai.friday.billpayment.app.metrics.push
import ai.friday.billpayment.markers
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import java.time.LocalDate
import org.slf4j.LoggerFactory

@Singleton
@Requires(property = "micronaut.metrics.enabled", value = "true")
open class ForecastMonitoringJob(
    private val forecastService: PaymentSchedulingForecastService,
    @Property(name = "schedules.balanceMonitoring.cron") cron: String,
) : AbstractJob(cron = cron) {
    private val logger = LoggerFactory.getLogger(ForecastMonitoringJob::class.java)

    private fun getForecastResult(refDate: LocalDate): Long =
        when (val result = forecastService.find(refDate)) {
            is ScheduleForecastResult.Item -> result.provisionedAmount

            else -> 0
        }

    override fun execute() {
        val today = getLocalDate()
        val todayResult = getForecastResult(today)
        ScheduleProvisioning.push(value = todayResult)

        val tomorrow = today.plusDays(1)
        val tomorrowResult = getForecastResult(tomorrow)
        TomorrowScheduleProvisioning.push(value = tomorrowResult)

        logger.info(
            markers(
                "today" to todayResult,
                "tomorrow" to tomorrowResult,
            ),
            "ForecastMonitoringJob",
        )
    }
}

private object ScheduleProvisioning : AbstractSummary()
private object TomorrowScheduleProvisioning : AbstractSummary()