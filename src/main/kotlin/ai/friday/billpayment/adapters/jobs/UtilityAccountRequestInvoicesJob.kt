package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.utilityaccount.UtilityAccountService
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
open class UtilityAccountRequestInvoicesJob(
    private val service: UtilityAccountService,
) : AbstractJob(cron = "20 13 * * *", lockAtLeastFor = "30m") {

    override fun execute() {
        logger.info(Markers.append("action", "start"), "UtilityAccountRequestInvoicesJob#execute")
        process()
        logger.info(Markers.append("action", "finish"), "UtilityAccountRequestInvoicesJob#execute")
    }

    private fun process() {
        try {
            val utilityAccounts = service.findAllConnectedByFlow()
            logger.info("UtilityAccountRequestInvoicesJob#process/start")
            utilityAccounts.forEach {
                service.postRequestInvoicesMessage(it)
                logger.info(Markers.append("utilityAccount", it), "UtilityAccountRequestInvoicesJob#process/processed")
            }
            logger.info("UtilityAccountRequestInvoicesJob#process/finish")
        } catch (ex: Exception) {
            logger.error("UtilityAccountRequestInvoicesJob#process/error", ex)
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(UtilityAccountRequestInvoicesJob::class.java)
    }
}