package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.integrations.WalletConfiguration
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.wallet.InviteStatus
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
open class ExpireInviteJob(val walletRepository: WalletRepository, private val configuration: WalletConfiguration) :
    AbstractJob(
        cron = "10 3 * * *",
    ) {
    override fun execute() {
        val expiredAt = getZonedDateTime()
        walletRepository.findExpiringInvites().forEach {
            LOG.info(Markers.append("expiredInvite", it), "ExpireInviteJob")
            walletRepository.save(
                it.copy(
                    status = InviteStatus.EXPIRED,
                    updatedAt = expiredAt,
                ),
            )
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ExpireInviteJob::class.java)
    }
}