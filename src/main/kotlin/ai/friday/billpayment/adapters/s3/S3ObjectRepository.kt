package ai.friday.billpayment.adapters.s3

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.integrations.ObjectWithMetadata
import io.micronaut.context.annotation.Primary
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Secondary
import io.micronaut.http.MediaType
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.io.InputStream
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request
import software.amazon.awssdk.services.s3.model.ListObjectsV2Response
import software.amazon.awssdk.services.s3.model.PutObjectRequest

@Singleton
@Primary
@FridayMePoupe
open class S3ObjectRepository(
    private val s3Client: S3Client,
    @Property(name = "aws.region") private val awsRegion: String,
) : ObjectRepository {

    override fun listObjectKeys(bucketName: String, directoryKey: String): List<String> {
        val listing: ListObjectsV2Response = s3Client.listObjectsV2 {
            it.bucket(bucketName).prefix(directoryKey)
        }

        return listing.contents().map { it.key() }
    }

    override fun region(): String {
        return awsRegion
    }

    override fun loadObject(bucketName: String, key: String): InputStream {
        return s3Client.getObject {
            it.bucket(bucketName).key(key)
        }
    }

    override fun loadObjectWithMetadata(bucketName: String, key: String): ObjectWithMetadata {
        val file = s3Client.getObject {
            it.bucket(bucketName).key(key)
        }
        return ObjectWithMetadata(file, file.response().contentLength())
    }

    override fun moveObject(fromBucket: String, fromKey: String, toBucket: String, toKey: String) {
        s3Client.copyObject {
            it.sourceBucket(fromBucket).sourceKey(fromKey)
                .destinationBucket(toBucket).destinationKey(toKey)
        }

        s3Client.deleteObject {
            it.bucket(fromBucket).key(fromKey)
        }
    }

    override fun copyObject(fromBucket: String, fromKey: String, toBucket: String, toKey: String) {
        s3Client.copyObject {
            it.sourceBucket(fromBucket).sourceKey(fromKey)
                .destinationBucket(toBucket).destinationKey(toKey)
        }
    }

    override fun putObject(storedObject: StoredObject, fileData: ByteArray, mediaType: MediaType) {
        val putObjectRequest = PutObjectRequest.builder()
            .bucket(storedObject.bucket)
            .key(storedObject.key)
            .contentType(mediaType.toString())
            .build()

        s3Client.putObject(putObjectRequest, RequestBody.fromBytes(fileData))
    }

    override fun findKeysByPrefix(storedObject: StoredObject): List<StoredObject> {
        return listObjectsByKeyPrefix(storedObject).contents().map {
            storedObject.copy(key = it.key())
        }
    }

    override fun hasKeyPrefix(storedObject: StoredObject): Boolean {
        return listObjectsByKeyPrefix(storedObject).hasContents()
    }

    private fun listObjectsByKeyPrefix(storedObject: StoredObject): ListObjectsV2Response {
        val request = ListObjectsV2Request.builder()
            .bucket(storedObject.bucket)
            .prefix(storedObject.key)
            .maxKeys(1)
            .build()

        return s3Client.listObjectsV2(request)
    }
}

@Singleton
@Secondary
@Named("sa")
@FridayMePoupe
open class S3ObjectRepositorySa(
    s3Client: S3Client = S3Client.builder().region(Region.SA_EAST_1).build(),
) : S3ObjectRepository(s3Client, Region.SA_EAST_1.id())