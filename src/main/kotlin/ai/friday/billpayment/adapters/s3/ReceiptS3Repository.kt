package ai.friday.billpayment.adapters.s3

import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.integrations.ReceiptFileRepository
import ai.friday.billpayment.app.payment.ReceiptData
import ai.friday.billpayment.app.payment.ReceiptFiles
import ai.friday.billpayment.app.payment.ReceiptFilesData
import ai.friday.billpayment.app.utils.HtmlToPdfAndImageConversorService
import ai.friday.billpayment.app.utils.PublicHttpLinkGenerator
import ai.friday.billpayment.app.utils.PublicHttpLinkGeneratorRetryConfiguration
import ai.friday.billpayment.mediaTypeFrom
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.http.MediaType
import java.io.File
import java.io.FileOutputStream
import java.time.Duration
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@FridayMePoupe
class ReceiptS3Repository(
    private val remoteFileStorage: ObjectRepository,
    private val receiptConfiguration: ReceiptConfiguration,
    private val publicHttpLinkGenerator: PublicHttpLinkGenerator,
    private val htmlToPdfAndImageConversorService: HtmlToPdfAndImageConversorService,
) : ReceiptFileRepository {

    override fun generateReceiptFiles(receiptData: ReceiptData, receiptHtml: CompiledHtml): ReceiptFilesData {
        val pdfByteArray = htmlToPdfAndImageConversorService.convertHtml2Pdf(receiptHtml, true)
        save2S3(pdfByteArray, receiptData.getPdfS3Key(), MediaType.APPLICATION_PDF_TYPE)

        val imgByteArray = htmlToPdfAndImageConversorService.convertPdf2Image(pdfByteArray, receiptConfiguration.imageResolution, receiptConfiguration.imageFormat)
        save2S3(imgByteArray, receiptData.getImageS3Key(), mediaTypeFrom(receiptConfiguration.imageFormat))

        logger.info(
            append("pdfS3Key", receiptData.getPdfS3Key()).and(append("imageS3Key", receiptData.getImageS3Key())),
            "GenerateReceiptFiles",
        )
        val links = generateLinks(receiptData)
        return ReceiptFilesData(
            imageBytes = imgByteArray,
            imageFormat = receiptConfiguration.imageFormat,
            pdfBytes = pdfByteArray,
            imageUrl = links.imageUrl,
            pdfUrl = links.pdfUrl,
        )
    }

    override fun generateLinks(receiptData: ReceiptData): ReceiptFiles {
        val pdfUrl = generateLink(
            receiptData.getPdfS3Key(),
            receiptConfiguration.linkDuration,
            MediaType.APPLICATION_PDF,
        )
        val imageUrl = generateLink(
            receiptData.getImageS3Key(),
            receiptConfiguration.linkDuration,
            MediaType.IMAGE_PNG,
        )
        return ReceiptFiles(imageUrl = imageUrl, pdfUrl = pdfUrl)
    }

    private fun generateLink(key: String, duration: Duration, contentType: String): String? {
        val storedObject = StoredObject(receiptConfiguration.bucketRegion, receiptConfiguration.bucketName, key)
        return publicHttpLinkGenerator.generate(storedObject, duration, contentType, PublicHttpLinkGeneratorRetryConfiguration())
    }

    private fun ReceiptData.getImageS3Key() =
        "${this.walletId.value}/${this.billId.value}.${receiptConfiguration.imageFormat}"

    private fun save2S3(byteArray: ByteArray, key: String, mediaType: MediaType) {
        if (receiptConfiguration.shouldSaveReceiptFilesOnDisk) {
            val file = File("target/$key")
            file.parentFile.mkdirs()
            FileOutputStream(file).write(byteArray)
        }
        val storedObject = StoredObject(receiptConfiguration.bucketRegion, receiptConfiguration.bucketName, key)
        remoteFileStorage.putObject(storedObject, byteArray, mediaType)
    }

    override fun hasReceiptFilesStored(receiptData: ReceiptData): Boolean {
        val pdfStoredObject = StoredObject(receiptConfiguration.bucketRegion, receiptConfiguration.bucketName, receiptData.getPdfS3Key())
        val imageStoredObject = StoredObject(receiptConfiguration.bucketRegion, receiptConfiguration.bucketName, receiptData.getImageS3Key())

        return remoteFileStorage.hasKeyPrefix(pdfStoredObject) && remoteFileStorage.hasKeyPrefix(imageStoredObject)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ReceiptS3Repository::class.java)
    }
}

fun ReceiptData.getPdfS3Key() = "${this.walletId.value}/${this.billId.value}.pdf"

@FridayMePoupe
@ConfigurationProperties("receipt")
open class ReceiptConfiguration
@ConfigurationInject constructor(
    val bucketRegion: String,
    val bucketName: String,
    val imageResolution: Int,
    val imageFormat: String,
    val shouldSaveReceiptFilesOnDisk: Boolean,
    val linkDuration: Duration,
)