package ai.friday.billpayment.adapters.indicators

import ai.friday.billpayment.adapters.messaging.SQSEmailMessageHandler
import io.micronaut.context.annotation.Requires
import io.micronaut.health.HealthStatus
import io.micronaut.management.health.indicator.HealthIndicator
import io.micronaut.management.health.indicator.HealthResult
import io.reactivex.Flowable
import jakarta.inject.Singleton
import org.reactivestreams.Publisher

@Requires(beans = [SQSEmailMessageHandler::class])
@Singleton
class SQSEmailMessageHealthIndicator(private val handler: SQSEmailMessageHandler) : HealthIndicator {
    override fun getResult(): Publisher<HealthResult> {
        val healthStatus = if (handler.isActive) HealthStatus.UP else HealthStatus.DOWN
        return Flowable.just(HealthResult.builder("SQSEmailMessageHealthIndicator", healthStatus).build())
    }
}