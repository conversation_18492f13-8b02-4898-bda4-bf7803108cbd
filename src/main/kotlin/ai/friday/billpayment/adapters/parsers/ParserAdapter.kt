package ai.friday.billpayment.adapters.parsers

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.integrations.ParserService
import io.via1.communicationcentre.app.email.IncomingEmail
import io.via1.communicationcentre.app.parser.BillParseService
import io.via1.communicationcentre.app.parser.ParsedBill

@FridayMePoupe
class ParserAdapter(private val billParseService: BillParseService) : ParserService {
    override fun parseBillFrom(incomingEmail: IncomingEmail): List<ParsedBill> =
        billParseService.parseBillFrom(incomingEmail)
}