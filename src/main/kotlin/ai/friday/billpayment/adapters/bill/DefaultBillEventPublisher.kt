package ai.friday.billpayment.adapters.bill

import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillIgnored
import ai.friday.billpayment.app.bill.FichaCompensacaoAdded
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.log
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory

@Singleton
class DefaultBillEventPublisher(
    private val eventRepository: BillEventRepository,
    private val billRepository: BillRepository,
    private val eventPublisher: EventPublisher,
) : BillEventPublisher {
    private val logger = LoggerFactory.getLogger(DefaultBillEventPublisher::class.java)

    override fun publish(bill: Bill, event: BillEvent) {
        this.store(bill, event)
        try {
            eventPublisher.publish(event, bill.billType)
        } catch (e: Exception) {
            logger.error(log("event" to event), "DefaultBillEventPublisher#publish", e)
        }
    }

    // This method is used to store bill events in the database
    // Use only for past events. Ex: past paid bills
    // Events must have different created timestamp so they dont overwrite each other
    override fun storePastBill(vararg events: BillEvent) {
        val bill = Bill.build()
        // first event must be BillAdded or FichaCompensacaoAdded
        if (events.first() !is BillAdded && events.first() !is FichaCompensacaoAdded) {
            throw IllegalArgumentException("First event must be BillAdded or FichaCompensacaoAdded")
        }
        if (events.map { it.created }.distinct().size != events.size) {
            throw IllegalArgumentException("Events must have different createdAt")
        }
        events.forEach { event ->
            this.store(bill, event)
        }
    }

    private fun store(bill: Bill, event: BillEvent) {
        eventRepository.save(event)
        bill.apply(event)
        if (event is BillIgnored && event.removeFromListing) {
            billRepository.remove(bill.billId, bill.walletId)
            return
        }

        billRepository.save(bill)
    }
}