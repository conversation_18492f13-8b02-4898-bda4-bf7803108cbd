package ai.friday.billpayment.adapters.modatta.api

import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.banking.DefaultBankStatementItem
import ai.friday.billpayment.app.modatta.ModattaCashInNotification
import ai.friday.billpayment.app.modatta.ModattaCashInType
import ai.friday.billpayment.app.modatta.ModattaDepositCallback
import ai.friday.billpayment.app.modatta.UserId
import ai.friday.billpayment.app.modatta.integrations.ModattaBackendAdapter
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Requires
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.FRIDAY_CALLBACK)
@Controller("/callback")
@Requires(env = [MODATTA_ENV])
open class ModattaDepositCallbackController(
    private val modattaBackendAdapter: ModattaBackendAdapter,
    private val accountService: AccountService,
) {
    @Post("/deposit-pix")
    fun depositPixCallback(@Body bankStatementItem: DefaultBankStatementItem): HttpResponse<*> {
        val logName = "ModattaArbiDepositCallbackController#depositPixCallback"
        val markers = Markers.append("bankStatementItem", bankStatementItem)
        try {
            val account = try {
                accountService.findAccountByDocument(bankStatementItem.documentNumber)
            } catch (e: AccountNotFoundException) {
                null
            }

            val notification = ModattaCashInNotification(
                amount = bankStatementItem.amount,
                externalId = account?.let { UserId(it.accountId.value).toModattaExternalId() },
                date = bankStatementItem.date,
                counterpartName = bankStatementItem.counterpartName,
                counterpartDocument = bankStatementItem.counterpartDocument,
                counterpartAccountNo = bankStatementItem.counterpartAccountNo.orEmpty(),
                transferType = ModattaCashInType.PIX,
                transactionId = bankStatementItem.ref.orEmpty(),
            )
            markers.andAppend("notification", notification)
            val result = modattaBackendAdapter.notifyDepositReceived(notification)
            return result.fold(
                ifLeft = {
                    LOG.error(markers.andAppend("error", it), logName)
                    HttpResponse.serverError(it.toString())
                },
                ifRight = {
                    LOG.info(markers, logName)
                    HttpResponse.ok("ok")
                },
            )
        } catch (e: Exception) {
            LOG.error(markers, logName, e)
            return HttpResponse.serverError(e.message)
        }
    }

    @Post("/deposit")
    fun depositCallback(@Body depositCallback: ModattaDepositCallback): HttpResponse<*> {
        val logName = "ModattaArbiDepositCallbackController#depositCallback"
        val markers = Markers.append("depositCallback", depositCallback)
        try {
            val account = try {
                accountService.findAccountByDocument(depositCallback.counterpartDocument)
            } catch (e: AccountNotFoundException) {
                null
            }

            val notification = ModattaCashInNotification(
                amount = depositCallback.amount,
                externalId = account?.let { UserId(it.accountId.value).toModattaExternalId() },
                date = getLocalDate(),
                counterpartName = depositCallback.counterpartName,
                counterpartDocument = depositCallback.counterpartDocument,
                counterpartAccountNo = depositCallback.counterpartAccountNo,
                transferType = ModattaCashInType.TED,
            )

            LOG.info(markers.andAppend("notification", notification), logName)

            modattaBackendAdapter.notifyDepositReceived(
                notification,
            )
            return HttpResponse.noContent<Unit>()
        } catch (e: Exception) {
            LOG.error(markers, logName, e)
            return HttpResponse.serverError(e.message)
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ModattaDepositCallbackController::class.java)
    }
}