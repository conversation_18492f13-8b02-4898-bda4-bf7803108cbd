package ai.friday.billpayment.adapters.modatta.api

import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.api.StandardHttpResponses
import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.modatta.register.ModattaSimpleSignUpService
import ai.friday.billpayment.app.modatta.register.ModattaSimpleSignUpUpdateStatusRequest
import ai.friday.billpayment.app.modatta.register.SimpleSignUpApprover
import ai.friday.billpayment.app.modatta.register.SimpleSignUpCallbackError
import ai.friday.billpayment.app.register.RiskAnalysisFailedReason
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.context.annotation.Requires
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.FRIDAY_CALLBACK)
@Controller("/callback/simpleSignUp")
@Requires(env = [MODATTA_ENV])
class ModattaSimpleSignUpCallbackController(private val modattaSimpleSignUpService: ModattaSimpleSignUpService) {

    @Post("/updateStatus")
    fun updateStatus(
        authentication: Authentication,
        @Body request: ModattaSimpleSignUpUpdateStatusRequestTO,
    ): HttpResponse<*> {
        val markers = Markers.append("authentication", authentication.name)
            .andAppend("request", request)

        return try {
            modattaSimpleSignUpService.updateStatus(
                request = ModattaSimpleSignUpUpdateStatusRequest(
                    fridayAccountId = AccountId(request.accountId),
                    externalId = ExternalId(value = request.externalId, providerName = request.externalIdProviderName),
                    status = request.status,
                    riskAnalysisFailedReasons = request.riskAnalysisFailedReasons,
                    approvedBy = request.approvedBy,
                ),
            ).map {
                markers.andAppend("account", it)
                LOG.info(markers, "ModattaSimpleSignUpCallbackController#updateStatus")
                HttpResponse.accepted<Unit>()
            }.getOrElse {
                markers.andAppend("error", it)

                LOG.warn(markers, "ModattaSimpleSignUpCallbackController#updateStatus")
                when (it) {
                    is SimpleSignUpCallbackError.InvalidProviderName -> StandardHttpResponses.badRequest(
                        code = "4001",
                        message = "Invalid provider name: ${it.providerName.name}",
                    )

                    is SimpleSignUpCallbackError.AccountNotFound -> StandardHttpResponses.notFound(
                        ResponseTO(
                            code = "4041",
                            message = "Account not found: ${it.accountId.value}",
                        ),
                    )

                    is SimpleSignUpCallbackError.Conflict -> StandardHttpResponses.conflict(
                        ResponseTO(
                            code = "4091",
                            message = "Reason: ${it.reason}",
                        ),
                    )
                }
            }
        } catch (e: Exception) {
            LOG.error(markers, "ModattaSimpleSignUpCallbackController#updateStatus", e)
            StandardHttpResponses.serverError(
                ResponseTO(
                    code = "5001",
                    message = e.message.orEmpty(),
                ),
            )
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ModattaSimpleSignUpCallbackController::class.java)
    }
}

data class ModattaSimpleSignUpUpdateStatusRequestTO(
    val accountId: String,
    val externalId: String, // ID da modatta
    val externalIdProviderName: AccountProviderName,
    val status: AccountStatus,
    val riskAnalysisFailedReasons: List<RiskAnalysisFailedReason>? = null,
    val approvedBy: SimpleSignUpApprover?,
)