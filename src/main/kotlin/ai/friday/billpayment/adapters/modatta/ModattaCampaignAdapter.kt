package ai.friday.billpayment.adapters.modatta

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.campaigns.RedeemCodeResult
import ai.friday.billpayment.app.integrations.IModattaCampaignAdapter
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import java.net.URL
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
open class ModattaCampaignAdapter(
    private val configuration: ModattaCampaignAdapterConfiguration,
) : IModattaCampaignAdapter {
    private val httpClient = RxHttpClient.create(URL(configuration.host))

    override fun redeemCode(code: String): Either<Exception, RedeemCodeResult> {
        val markers = Markers.append("code", code)
        val logName = "ModattaCampaignAdapter#redeemCode"
        val url = configuration.redeemCodePath.replace("{code}", code)

        try {
            val httpRequest: HttpRequest<*> = HttpRequest.GET<ModattaPublicResponse>(url)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.exchange(
                httpRequest,
                Argument.of(ModattaPublicResponse::class.java),
                Argument.of(ModattaPublicResponse::class.java),
            )

            val response = call.firstOrError().blockingGet()
            markers.andAppend("modattaResponse", response)
            LOG.info(markers, logName)

            return RedeemCodeResult.REDEEMED.right()
        } catch (e: HttpClientResponseException) {
            val body = e.response.getBody(ModattaPublicResponse::class.java)
            markers.andAppend("modattaResponse", body.get())

            LOG.error(markers, logName, e)

            if (!body.isEmpty && body.get().message == "ER012") { // quando o código já foi resgatado
                return RedeemCodeResult.ALREADY_REDEEMED.right()
            }

            if (!body.isEmpty && body.get().message == "ER011") { // quando o código não é encontrado
                return RedeemCodeResult.INVALID_CODE.right()
            }

            return e.left()
        } catch (e: Exception) {
            LOG.error(markers, logName, e)
            return e.left()
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ModattaCampaignAdapter::class.java)
    }
}

data class ModattaPublicResponse(
    val result: String,
    val message: String,
)

@ConfigurationProperties("integrations.modattaPublic")
class ModattaCampaignAdapterConfiguration
@ConfigurationInject
constructor(
    val host: String,
    val redeemCodePath: String,
)