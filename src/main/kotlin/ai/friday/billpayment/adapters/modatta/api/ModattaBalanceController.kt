package ai.friday.billpayment.adapters.modatta.api

import ai.friday.billpayment.adapters.api.AmountBalanceTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.api.StandardHttpResponses
import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.banking.FridayBankAccountError
import ai.friday.billpayment.app.modatta.ModattaService
import ai.friday.billpayment.app.modatta.integrations.FridayBalanceService
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.context.annotation.Requires
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(value = [Role.Code.OWNER])
@Controller("/balance")
@Requires(env = [MODATTA_ENV])
class ModattaBalanceController(
    private val fridayBalanceService: FridayBalanceService,
    private val modattaService: ModattaService,
    private val accountService: AccountService,
) {
    @Get("/amount")
    fun getAmountBalance(authentication: Authentication): HttpResponse<*> {
        val userId = authentication.toUserId()

        val markers = Markers.append("userId", userId.value)

        val balance = modattaService.getBalance(userId).getOrElse {
            logger.error(markers, "ModattaBalanceController#getAmountBalance", it)
            return HttpResponse.serverError(it)
        }

        markers.andAppend("balanceAmount", balance.amount).andAppend("balanceAmount", balance.minRedeemAmount)

        logger.info(markers, "ModattaBalanceController#getAmountBalance")

        return HttpResponse.ok(
            ModattaAmountBalanceTO(
                amount = balance.amount,
                minRedeemAmount = balance.minRedeemAmount,
            ),
        )
    }

    @Post("/cashout")
    fun cashoutBalance(authentication: Authentication, @Body body: CashoutTO): HttpResponse<*> {
        val userId = authentication.toUserId()
        logger.info(
            Markers.append("userId", userId.value).andAppend("requestId", body.requestId),
            "ModattaBalanceController#cashoutBalance",
        )

        return try {
            val account = accountService.findAccountById(
                userId.toAccountId(),
            )

            val userBankAccount =
                fridayBalanceService.getCashoutBankAccount(account.configuration.externalId!!).getOrElse {
                    logger.error(
                        Markers.append("userId", userId.value),
                        "ModattaBalanceController#cashoutBalance",
                        it,
                    )
                    return HttpResponse.serverError<Unit>()
                }
            modattaService.userCashOut(userId, userBankAccount)
            HttpResponse.ok<Unit>()
        } catch (e: Exception) {
            logger.error(
                Markers.append("userId", userId.value),
                "ModattaBalanceController#cashoutBalance",
                e,
            )
            return HttpResponse.serverError<Unit>()
        }
    }

    @Get("/partnerAmount")
    fun getPartnerAmountBalance(authentication: Authentication): HttpResponse<*> {
        val userId = authentication.toUserId()
        val account = accountService.findAccountById(
            userId.toAccountId(),
        )
        if (account.status == AccountStatus.REGISTER_INCOMPLETE) {
            return HttpResponse.ok(
                AmountBalanceTO(
                    amount = 0,
                ),
            )
        }

        return fridayBalanceService.getBalance(
            fridayExternalId = account.configuration.externalId!!,
        ).map {
            HttpResponse.ok(
                AmountBalanceTO(
                    amount = it.amount,
                ),
            )
        }.getOrElse {
            when (it) {
                FridayBankAccountError.FridayAccountNotFound -> StandardHttpResponses.badRequest(
                    code = "4001",
                    message = it.toString(),
                )

                FridayBankAccountError.FridayAccountClosed -> StandardHttpResponses.badRequest(
                    code = "4002",
                    message = it.toString(),
                )

                is FridayBankAccountError.ServerError -> StandardHttpResponses.customStatusResponse(
                    httpStatus = HttpStatus.BAD_GATEWAY,
                    responseTO = ResponseTO(
                        code = "5001",
                        message = it.exception.message ?: "ServerError",
                    ),
                )
            }
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ModattaBalanceController::class.java)
    }
}

data class ModattaAmountBalanceTO(
    val amount: Long,
    val minRedeemAmount: Long,
)

data class CashoutTO(
    val requestId: String,
)