package ai.friday.billpayment.adapters.modatta

import ai.friday.billpayment.adapters.modatta.api.ModattaSimpleSignUpUpdateStatusRequestTO
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.banking.DefaultBankStatementItem
import ai.friday.billpayment.app.integrations.ModattaProvider
import ai.friday.billpayment.app.modatta.ModattaDepositCallback
import ai.friday.billpayment.app.modatta.register.SimpleSignUpApprover
import ai.friday.billpayment.app.register.RiskAnalysisFailedReason
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.HttpClientConfiguration
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.ApplicationConfiguration
import io.micronaut.rxjava2.http.client.RxHttpClient
import java.net.URL
import java.time.Duration
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
class ModattaHttpConfiguration(
    applicationConfiguration: ApplicationConfiguration,
    val configuration: HttpClientConfiguration,
) : HttpClientConfiguration(applicationConfiguration) {
    init {
        setReadTimeout(Duration.ofSeconds(30))
        setConnectionPoolIdleTimeout(Duration.ofSeconds(10))
    }

    override fun getConnectionPoolConfiguration(): ConnectionPoolConfiguration =
        configuration.connectionPoolConfiguration
}

@FridayMePoupe
class ModattaProviderAdapter(
    private val configuration: ModattaProviderAdapterConfiguration,
    httpConfiguration: ModattaHttpConfiguration,
) : ModattaProvider {

    private val httpClient = RxHttpClient.create(URL(configuration.host), httpConfiguration)

    override fun updateStatus(
        accountId: AccountId,
        externalId: ExternalId,
        status: AccountStatus,
        riskAnalysisFailedReasons: List<RiskAnalysisFailedReason>?,
        approvedBy: SimpleSignUpApprover?,
    ) {
        val marker = Markers.append("accountId", accountId)
        val logName = "ModattaSimpleSignUpUpdateStatusRequestTO#updateStatus"

        try {
            val requestBody = ModattaSimpleSignUpUpdateStatusRequestTO(
                externalId = externalId.value,
                accountId = accountId.value,
                externalIdProviderName = externalId.providerName,
                status = status,
                approvedBy = approvedBy,
                riskAnalysisFailedReasons = riskAnalysisFailedReasons,
            )
            marker.andAppend("requestBody", requestBody)

            val httpRequest =
                HttpRequest.POST(configuration.updateStatusPath, requestBody)
                    .contentType(MediaType.APPLICATION_JSON)
                    .basicAuth(
                        configuration.user,
                        configuration.password,
                    )

            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(String::class.java),
                Argument.of(String::class.java),
            )

            call.firstOrError().blockingGet()
            return
        } catch (e: HttpClientResponseException) {
            LOG.error(
                marker.andAppend("status", e.status).andAppend("response", e.response.getBody(String::class.java)),
                logName,
                e,
            )
            throw e
        } catch (e: Exception) {
            LOG.error(marker, logName, e)
            throw e
        }
    }

    override fun depositPixCallback(bankStatementItem: DefaultBankStatementItem) {
        val marker = Markers.append("bankStatementItem", bankStatementItem)
        val logName = "ModattaProviderAdapter#depositPixCallback"

        try {
            marker.andAppend("requestBody", bankStatementItem)

            val httpRequest =
                HttpRequest.POST(configuration.depositPixCallbackPath, bankStatementItem)
                    .contentType(MediaType.APPLICATION_JSON)
                    .basicAuth(
                        configuration.user,
                        configuration.password,
                    )

            val call = httpClient.retrieve(httpRequest, Argument.of(String::class.java))

            call.firstOrError().blockingGet()
            return
        } catch (e: HttpClientResponseException) {
            LOG.error(
                marker.andAppend("status", e.status).andAppend("response", e.response.getBody(String::class.java)),
                logName,
                e,
            )
            throw e
        } catch (e: Exception) {
            LOG.error(marker, logName, e)
            throw e
        }
    }

    override fun depositCallback(depositCallback: ModattaDepositCallback) {
        val markers = Markers.append("depositCallback", depositCallback)
        val logName = "ModattaProviderAdapter#depositCallback"

        try {
            val httpRequest =
                HttpRequest.POST(configuration.depositCallbackPath, depositCallback)
                    .contentType(MediaType.APPLICATION_JSON)
                    .basicAuth(
                        configuration.user,
                        configuration.password,
                    )

            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(Unit::class.java),
                Argument.of(String::class.java),
            )

            call.firstOrError().blockingGet()
            return
        } catch (e: HttpClientResponseException) {
            LOG.error(
                markers.andAppend("status", e.status),
                logName,
                e,
            )
            throw e
        } catch (e: Exception) {
            LOG.error(markers, logName, e)
            throw e
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ModattaProviderAdapter::class.java)
    }
}

@ConfigurationProperties("integrations.modatta")
class ModattaProviderAdapterConfiguration
@ConfigurationInject
constructor(
    val host: String,
    val user: String,
    val password: String,
    val updateStatusPath: String,
    val depositPixCallbackPath: String,
    val depositCallbackPath: String,
)