package ai.friday.billpayment.adapters.modatta.api

import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.modatta.UserId
import ai.friday.billpayment.app.modatta.register.ModattaSimpleSignUpService
import io.micronaut.context.annotation.Requires
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.server.util.HttpClientAddressResolver
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(BACKOFFICE)
@Controller("/backoffice")
@Requires(env = [MODATTA_ENV])
class ModattaBackofficeController(
    private val simpleSignUpService: ModattaSimpleSignUpService,
    private val addressResolver: HttpClientAddressResolver,
) {
    @Post("/simpleSignUp/agreement/{userId}")
    fun simpleSignUpPostAgreement(@PathVariable userId: String, request: HttpRequest<*>): HttpResponse<*> {
        val logMessage = "ModattaBackofficeController#simpleSignUpPostAgreement"
        val clientIp = addressResolver.resolve(request) ?: ""

        val markers = Markers.append("userId", userId)

        simpleSignUpService.acceptAgreement(
            userId = UserId(userId),
            clientIP = clientIp,
        ).mapLeft {
            return if (it is AccountNotFoundException) {
                LOG.warn(markers, logMessage)
                HttpResponse.notFound(ResponseTO(code = "4004", message = "Account Register not found"))
            } else {
                LOG.error(markers, logMessage, it)
                HttpResponse.serverError(ResponseTO(code = "5000", message = it.message.orEmpty()))
            }
        }

        return HttpResponse.ok<Unit>()
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ModattaBackofficeController::class.java)
    }
}