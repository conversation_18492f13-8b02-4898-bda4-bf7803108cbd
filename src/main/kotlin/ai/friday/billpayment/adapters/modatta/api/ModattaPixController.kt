package ai.friday.billpayment.adapters.modatta.api

import ai.friday.billpayment.adapters.api.CreateModattaPixResponseTO
import ai.friday.billpayment.adapters.api.PixKeyRequestTO
import ai.friday.billpayment.adapters.api.RecipientBankDetailsTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.api.StandardHttpResponses
import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.CreateBillError
import ai.friday.billpayment.app.bill.descriptionMaxLength
import ai.friday.billpayment.app.modatta.CreateModattaPixRequest
import ai.friday.billpayment.app.modatta.PixRecipientRequest
import ai.friday.billpayment.app.modatta.integrations.FridayBalanceService
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.context.annotation.Requires
import io.micronaut.core.annotation.Introspected
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import jakarta.validation.Valid
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Positive
import java.time.LocalDate
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(value = [Role.Code.OWNER])
@Controller("/pix")
@Requires(env = [MODATTA_ENV])
open class ModattaPixController(
    private val fridayBalanceService: FridayBalanceService,
    private val accountService: AccountService,
) {

    @Post("/createPix")
    open fun postPix(
        @Body @Valid
        createPixTO: CreateModattaPixTO,
    ): HttpResponse<*> {
        val marker = Markers.append("accountId", createPixTO.accountId)
            .and<LogstashMarker>(Markers.append("request", createPixTO))

        val account = accountService.findAccountById(
            AccountId(createPixTO.accountId),
        )
        val createBillResult = fridayBalanceService.createPix(
            CreateModattaPixRequest(
                description = "Resgate App Modatta",
                dueDate = LocalDate.now(),
                amount = createPixTO.amount,
                recipient = PixRecipientRequest(
                    accountId = AccountId(account.configuration.externalId!!.value),
                    name = null,
                    document = null,
                    bankAccount = createPixTO.bankDetails?.let {
                        BankAccount(
                            accountType = it.accountType,
                            bankNo = it.bankNo,
                            routingNo = it.routingNo,
                            accountNo = it.accountNo,
                            accountDv = it.accountDv,
                            ispb = it.ispb,
                        )
                    },
                    pixKey = createPixTO.pixKey?.let { PixKey(createPixTO.pixKey!!.value, createPixTO.pixKey.type) },
                ),
                source = ActionSource.Api(AccountId(createPixTO.accountId)),
                walletId = WalletId(),
            ),
        )
        return createBillResult.map { billId ->
            LOG.info(marker.andAppend("billId", billId), "ModattaPixController#postPix")
            HttpResponse.created(CreateModattaPixResponseTO(billId.value))
        }.getOrElse {
            if (it.first == CreateBillError.SERVER_ERROR.code) {
                LOG.error(marker, "ModattaPixController#postPix")
                StandardHttpResponses.serverError(
                    ResponseTO(
                        CreateBillError.SERVER_ERROR.code,
                        CreateBillError.SERVER_ERROR.description,
                    ),
                )
            } else {
                LOG.warn(marker.andAppend("error", it.second), "ModattaPixController#postPix")
                StandardHttpResponses.badRequest(
                    it.first,
                    it.second,
                )
            }
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ModattaPixController::class.java)
    }
}

private const val descriptionRegex = "^[^'|]{0,$descriptionMaxLength}\$"
private const val descriptionErrorMessage =
    "Description size must be up to 140 length and can't include | or ' character"

@Introspected
data class CreateModattaPixTO(
    val accountId: String,
    val pixKey: PixKeyRequestTO? = null,
    @field:Valid val bankDetails: RecipientBankDetailsTO?,
    @field:Positive(message = "Amount must be natural number") val amount: Long,
    @field:Pattern(regexp = descriptionRegex, message = descriptionErrorMessage) val description: String,
)