package ai.friday.billpayment.adapters.modatta

import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.modatta.ModattaBalance
import ai.friday.billpayment.app.modatta.ModattaCashInNotification
import ai.friday.billpayment.app.modatta.ModattaCorpError
import ai.friday.billpayment.app.modatta.ModattaExternalId
import ai.friday.billpayment.app.modatta.integrations.ModattaBackendAdapter
import ai.friday.billpayment.app.register.RiskAnalysisFailedReason
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonProperty
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Requires
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
@Requires(env = [MODATTA_ENV])
class ModattaCorpAdapter(
    @param:Client("\${integrations.modatta.host}") private val httpClient: RxHttpClient,
    private val configuration: ModattaPartnerConfiguration,
    private val credentialConfiguration: ModattaPartnerCredentialConfiguration,
) : ModattaBackendAdapter {
    override fun getBalance(externalId: ModattaExternalId): Either<ModattaCorpError, ModattaBalance> {
        val url = configuration.paths.getBalance.replace("{userId}", externalId.value)

        val markers = Markers.append("url", url)

        return try {
            val httpRequest: HttpRequest<*> = HttpRequest.GET<BalanceTO>(url)
                .basicAuth(credentialConfiguration.identity, credentialConfiguration.secret)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.exchange(
                httpRequest,
                Argument.of(BalanceTO::class.java),
                Argument.of(String::class.java),
            )

            val response = call.firstOrError().blockingGet()

            response.body()?.let { balanceTO ->
                ModattaBalance(balanceTO.amount, balanceTO.minRedeemValue).right().also { result ->
                    markers.andAppend("result", result.map { it })
                    LOG.info(markers, "ModattaCorpAdapter#getBalance")
                }
            } ?: ModattaCorpError.ServerError(IllegalStateException("NO DATA AVAILABLE")).left()
        } catch (e: HttpClientResponseException) {
            val body = e.response.getBody(String::class.java)
            markers.andAppend("modattaResponse", body.get())
            val status = e.status

            if (status == HttpStatus.UNPROCESSABLE_ENTITY) {
                ModattaCorpError.ModattaValidationError(body.get()).left()
            } else {
                ModattaCorpError.ServerError(e).left()
            }.also { result ->
                markers.andAppend("result", result.getOrElse { it })
                LOG.error(markers, "ModattaCorpAdapter#getBalance", e)
            }
        }
    }

    override fun cashout(externalId: ModattaExternalId, userBankAccount: BankAccount): Either<ModattaCorpError, Unit> {
        val url = configuration.paths.redeem

        val markers = Markers.append("url", url)

        return try {
            val httpRequest: HttpRequest<*> =
                HttpRequest.POST(url, RedeemTO(externalId.value, userBankAccount.buildFullAccountNumber()))
                    .basicAuth(credentialConfiguration.identity, credentialConfiguration.secret)
                    .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.exchange(
                httpRequest,
                Argument.of(RedeemTO::class.java),
                Argument.of(String::class.java),
            )

            val response = call.firstOrError().blockingGet()

            markers.andAppend("response", response.body())
            LOG.info(markers, "ModattaCorpAdapter#cashout")

            Unit.right()
        } catch (e: HttpClientResponseException) {
            val body = e.response.getBody(String::class.java)
            markers.andAppend("modattaResponse", body.get())
            val status = e.status

            if (status == HttpStatus.UNPROCESSABLE_ENTITY) {
                ModattaCorpError.ModattaValidationError(body.get()).left()
            } else {
                ModattaCorpError.ServerError(e).left()
            }.also { result ->
                markers.andAppend("result", result.getOrElse { it })
                LOG.error(markers, "ModattaCorpAdapter#cashout", e)
            }
        }
    }

    override fun notifyDepositReceived(
        notification: ModattaCashInNotification,
    ): Either<ModattaCorpError, Unit> {
        val url = configuration.paths.notifyCashIn

        val markers = Markers.append("url", url)

        val body = ModattaCashInNotificationTO(
            amount = notification.amount,
            externalId = notification.externalId?.value,
            date = notification.date.toString(),
            counterpartName = notification.counterpartName,
            counterpartDocument = notification.counterpartDocument,
            counterpartAccountNo = notification.counterpartAccountNo,
            transferType = notification.transferType.name,
            transactionId = notification.transactionId,
        )
        return try {
            val httpRequest: HttpRequest<*> =
                HttpRequest.POST(url, body)
                    .basicAuth(credentialConfiguration.identity, credentialConfiguration.secret)
                    .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.exchange(
                httpRequest,
                Argument.of(ModattaCashInNotificationTO::class.java),
                Argument.of(String::class.java),
            )

            val response = call.firstOrError().blockingGet()

            markers.andAppend("response", response.body())
            LOG.info(markers, "ModattaCorpAdapter#notifyDepositReceived")

            Unit.right()
        } catch (e: HttpClientResponseException) {
            val stringBody = e.response.getBody(String::class.java)
            markers.andAppend("modattaResponse", stringBody.get())
            val status = e.status

            if (status == HttpStatus.UNPROCESSABLE_ENTITY) {
                ModattaCorpError.ModattaValidationError(stringBody.get()).left()
            } else {
                ModattaCorpError.ServerError(e).left()
            }.also { result ->
                markers.andAppend("result", result.getOrElse { it })
                LOG.error(markers, "ModattaCorpAdapter#notifyDepositReceived", e)
            }
        }
    }

    override fun notifyAccountRegisterUpdates(
        externalId: ModattaExternalId,
        status: AccountStatus,
        riskAnalysisFailedReasons: List<RiskAnalysisFailedReason>?,
    ): Either<ModattaCorpError, Unit> {
        val url = configuration.paths.notifyAccountStatusChanged

        val markers = Markers.append("url", url)

        val body =
            AccountUpdateTO(
                status = status,
                deniedReasons = riskAnalysisFailedReasons?.map { it.name } ?: emptyList(),
                userId = externalId,
            )

        return try {
            val httpRequest: HttpRequest<*> =
                HttpRequest.POST(url, body)
                    .basicAuth(credentialConfiguration.identity, credentialConfiguration.secret)
                    .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.exchange(
                httpRequest,
                Argument.of(AccountUpdateTO::class.java),
                Argument.of(String::class.java),
            )

            val response = call.firstOrError().blockingGet()

            markers.andAppend("response", response.body())
            LOG.info(markers, "ModattaCorpAdapter#notifyAccountRegisterUpdates")

            Unit.right()
        } catch (e: HttpClientResponseException) {
            val body = e.response.getBody(String::class.java)
            markers.andAppend("modattaResponse", body.get())
            val httpStatus = e.status

            if (httpStatus == HttpStatus.UNPROCESSABLE_ENTITY) {
                ModattaCorpError.ModattaValidationError(body.get()).left()
            } else {
                ModattaCorpError.ServerError(e).left()
            }.also { result ->
                markers.andAppend("result", result.getOrElse { it })
                LOG.error(markers, "ModattaCorpAdapter#notifyAccountRegisterUpdates", e)
            }
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ModattaCorpAdapter::class.java)
    }
}

data class BalanceTO(
    @JsonProperty("balance") val amount: Long,
    @JsonProperty("redeem_min") val minRedeemValue: Long,
)

@Requires(env = [MODATTA_ENV])
@ConfigurationProperties("integrations.modatta")
data class ModattaPartnerConfiguration @ConfigurationInject constructor(
    val host: String,
    val paths: ModattaPartnerPathConfiguration,
) {
    @Requires(env = [MODATTA_ENV])
    @ConfigurationProperties("paths")
    data class ModattaPartnerPathConfiguration
    @ConfigurationInject
    constructor(
        val notifyCashIn: String,
        val getBalance: String,
        val redeem: String,
        val notifyAccountStatusChanged: String,
    )
}

@Requires(env = [MODATTA_ENV])
@ConfigurationProperties("modatta-corp")
class ModattaPartnerCredentialConfiguration @ConfigurationInject constructor(
    val identity: String,
    val secret: String,
)

data class RedeemTO(
    val b2cId: String,
    val accountNumber: String,
)

data class AccountUpdateTO(
    val status: AccountStatus,
    val deniedReasons: List<String> = emptyList(),
    @JsonProperty("b2cId") val userId: ModattaExternalId,
)

data class ModattaCashInNotificationTO(
    val amount: Long,
    val externalId: String?,
    val date: String,
    val counterpartName: String,
    val counterpartDocument: String,
    val counterpartAccountNo: String,
    val transferType: String,
    val transactionId: String? = null,
)