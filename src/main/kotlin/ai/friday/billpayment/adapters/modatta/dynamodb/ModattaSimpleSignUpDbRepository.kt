package ai.friday.billpayment.adapters.modatta.dynamodb

import ai.friday.billpayment.adapters.dynamodb.AbstractBillPaymentDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.CustomS3Link
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.modatta.UserId
import ai.friday.billpayment.app.modatta.integrations.ModattaSimpleSignUpRepository
import ai.friday.billpayment.app.modatta.register.Enrollment
import ai.friday.billpayment.app.modatta.register.ModattaSimpleSignUp
import ai.friday.billpayment.app.modatta.register.ModattaSimpleSignUpValidationStatus
import ai.friday.billpayment.app.modatta.register.UserContract
import ai.friday.billpayment.app.modatta.register.UserData
import ai.friday.morning.date.dateFormat
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton
import java.time.LocalDate
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

const val simpleSignUpPartitionKey = "SIMPLE_SIGN_UP"

@Singleton
class ModattaSimpleSignUpDynamoDAO(
    cli: DynamoDbEnhancedClient,
) : AbstractBillPaymentDynamoDAO<ModattaSimpleSignUpEntity>(cli, ModattaSimpleSignUpEntity::class.java)

@Singleton
class ModattaSimpleSignUpDbRepository(
    private val dynamoDAO: ModattaSimpleSignUpDynamoDAO,
) : ModattaSimpleSignUpRepository {

    override fun find(userId: UserId): Either<AccountNotFoundException, ModattaSimpleSignUp> {
        val entity = dynamoDAO.findByPrimaryKey(
            simpleSignUpPartitionKey,
            userId.value,
        ) ?: return AccountNotFoundException(userId.value).left()

        return ModattaSimpleSignUp(
            userId = UserId(entity.sortKey),
            userData = entity.userData?.toUserData(),
            enrollment = entity.enrollment?.toEnrollment(),
            userContract = entity.userContract?.toUserContract(),
            validationStatus = entity.validationStatus,
        ).right()
    }

    override fun save(simpleSignUp: ModattaSimpleSignUp) {
        val entity = ModattaSimpleSignUpEntity().apply {
            partitionKey = simpleSignUpPartitionKey
            sortKey = simpleSignUp.userId.value
            enrollment = simpleSignUp.enrollment?.toEnrollmentDocument()
            userData = simpleSignUp.userData?.toUserDataDocument()
            userContract = simpleSignUp.userContract?.toUserContractDocument()
            validationStatus = simpleSignUp.validationStatus
        }

        dynamoDAO.save(entity)
    }

    private fun UserDataDocument.toUserData() = UserData(
        name = name,
        birthDate = LocalDate.parse(birthDate, dateFormat),
        document = Document(document),
        mobilePhone = MobilePhone(msisdn = mobilePhone),
        email = EmailAddress(email = email),
    )

    private fun UserData.toUserDataDocument(): UserDataDocument {
        val domainObject = this
        return UserDataDocument().apply {
            name = domainObject.name
            birthDate = domainObject.birthDate.format(dateFormat)
            document = domainObject.document.value
            mobilePhone = domainObject.mobilePhone.msisdn
            email = domainObject.email.value
        }
    }

    private fun UserContractDocument.toUserContract() = UserContract(
        hasAccepted = hasAccepted,
        contract = contract.retrieveStoredObjectLink(),
    )

    private fun UserContract.toUserContractDocument(): UserContractDocument {
        val domainObject = this
        return UserContractDocument().apply {
            hasAccepted = domainObject.hasAccepted
            contract = domainObject.contract.generateCustomS3Link()
        }
    }

    private fun EnrollmentDocument.toEnrollment() = Enrollment(
        livenessId = LivenessId(livenessId),
        done = done,
    )

    private fun Enrollment.toEnrollmentDocument(): EnrollmentDocument {
        val domainObject = this
        return EnrollmentDocument().apply {
            livenessId = domainObject.livenessId.value
            done = domainObject.done
        }
    }

    private fun StoredObject.generateCustomS3Link() = CustomS3Link.fromStoredObject(this)

    private fun CustomS3Link.retrieveStoredObjectLink() = StoredObject(region, bucket, key)
}

@DynamoDbBean
class ModattaSimpleSignUpEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String

    @get:DynamoDbAttribute("ValidationStatus")
    lateinit var validationStatus: ModattaSimpleSignUpValidationStatus

    @get:DynamoDbAttribute("Enrollment")
    var enrollment: EnrollmentDocument? = null

    @get:DynamoDbAttribute("UserData")
    var userData: UserDataDocument? = null

    @get:DynamoDbAttribute("UserContract")
    var userContract: UserContractDocument? = null
}

@DynamoDbBean
class UserDataDocument {
    lateinit var name: String
    lateinit var birthDate: String
    lateinit var document: String
    lateinit var mobilePhone: String
    lateinit var email: String
}

@DynamoDbBean
class UserContractDocument {
    var hasAccepted: Boolean = false

    @get:DynamoDbConvertedBy(value = ai.friday.billpayment.adapters.converters.CustomS3LinkAttributeConverter::class)
    lateinit var contract: CustomS3Link
}

@DynamoDbBean
class EnrollmentDocument {
    lateinit var livenessId: String
    var done: Boolean = false
}