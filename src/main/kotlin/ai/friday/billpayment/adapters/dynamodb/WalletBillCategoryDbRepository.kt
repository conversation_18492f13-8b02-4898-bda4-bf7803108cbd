package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.pfm.WalletBillCategoryRepository
import ai.friday.billpayment.app.wallet.WalletId
import jakarta.inject.Singleton
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class WalletBillCategoryDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<WalletBillCategoryEntity>(cli, WalletBillCategoryEntity::class.java)

@Singleton
class WalletBillCategoryDbRepository(
    private val client: WalletBillCategoryDynamoDAO,
) : WalletBillCategoryRepository {
    override fun save(walletBillCategory: WalletBillCategory) {
        client.save(walletBillCategory.toBillCategoryEntity())
    }

    override fun findByWalletId(walletId: WalletId): List<WalletBillCategory> {
        return client.findByPartitionKey(billCategoryPartitionKeyPrefix + walletId.value).map {
            it.toBillCategory()
        }
    }

    override fun findByWalletIdAndBillCategoryId(walletId: WalletId, billCategoryId: PFMCategoryId) =
        client.findByPrimaryKey(billCategoryPartitionKeyPrefix + walletId.value, billCategoryId.value)?.toBillCategory()

    private fun WalletBillCategory.toBillCategoryEntity(): WalletBillCategoryEntity {
        val category = this
        return WalletBillCategoryEntity().apply {
            partitionKey = billCategoryPartitionKeyPrefix + category.walletId.value
            scanKey = category.categoryId.value
            walletId = category.walletId.value
            name = category.name
            billCategoryIcon = category.icon
            enabled = category.enabled
            default = category.default
        }
    }

    private fun WalletBillCategoryEntity.toBillCategory() = WalletBillCategory(
        walletId = WalletId(this.walletId),
        categoryId = PFMCategoryId(value = this.scanKey),
        name = this.name,
        icon = this.billCategoryIcon,
        enabled = this.enabled,
        default = this.default,
    )
}

private const val billCategoryPartitionKeyPrefix = "WALLET_BILL_CATEGORY#"

@DynamoDbBean
class WalletBillCategoryEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String // WALLET_BILL_CATEGORY # walletId

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String // CategoryId

    @get:DynamoDbAttribute(value = "WalletId")
    lateinit var walletId: String

    @get:DynamoDbAttribute(value = "Name")
    lateinit var name: String

    @get:DynamoDbAttribute(value = "BillCategoryIcon")
    lateinit var billCategoryIcon: String

    @get:DynamoDbAttribute(value = "Enabled")
    var enabled: Boolean = false

    @get:DynamoDbAttribute(value = "Default")
    var default: Boolean = false
}