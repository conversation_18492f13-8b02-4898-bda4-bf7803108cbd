package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.adapters.converters.PaymentMethodsDetailEntityAttributeConverter
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.BillTag
import ai.friday.billpayment.app.bill.BillTrackingCalculateOptions
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.PixQrCodeData
import ai.friday.billpayment.app.bill.PixQrCodeType
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.payment.PartialPaymentAmountType
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixKeyOwner
import ai.friday.billpayment.app.pix.PixKeyType
import java.math.BigDecimal
import java.math.BigInteger
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@DynamoDbBean
class BillEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1SortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    var contactId: String? = null

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    var gSIndex2SortKey: ContactIdIndexMetadata? = null

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_3])
    @get:DynamoDbAttribute(value = "GSIndex3PrimaryKey")
    var gsIndex3PartitionKey: String? = null

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_3])
    @get:DynamoDbAttribute(value = "GSIndex3ScanKey")
    var gSIndex3SortKey: String? = null

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_4])
    @get:DynamoDbAttribute(value = "GSIndex4PrimaryKey")
    var gsIndex4PartitionKey: String? = null // BillCategoryId

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_4])
    @get:DynamoDbAttribute(value = "GSIndex4ScanKey")
    var gSIndex4SortKey: String? = null // WalletId

    @get:DynamoDbAttribute(value = "Assignor")
    var assignor: String? = null

    @get:DynamoDbAttribute(value = "Description")
    var description: String? = null

    @get:DynamoDbAttribute(value = "BillRecipient")
    var billRecipient: BillRecipientDocument? = null

    @get:DynamoDbAttribute(value = "DigitableBarcode")
    var digitableBarCode: String? = null

    @get:DynamoDbAttribute(value = "DueDate")
    lateinit var dueDate: String

    @get:DynamoDbAttribute(value = "EffectiveDueDate")
    var effectiveDueDate: String? = null

    @get:DynamoDbAttribute(value = "PaidDate")
    var paidDate: String? = null

    @get:DynamoDbAttribute(value = "AmountPaid")
    var amountPaid: Long? = null

    @get:DynamoDbAttribute(value = "ExpirationDate")
    var expirationDate: String? = null

    @get:DynamoDbAttribute(value = "Amount")
    var amount: Long = 0

    @get:DynamoDbAttribute(value = "Discount")
    var discount: Long = 0

    @get:DynamoDbAttribute(value = "Interest")
    var interest: Long = 0

    @get:DynamoDbAttribute(value = "Fine")
    var fine: Long = 0

    @get:DynamoDbAttribute(value = "AmountTotal")
    var amountTotal: Long = 0

    @get:DynamoDbAttribute(value = "Type")
    lateinit var type: BillType

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: String

    @get:DynamoDbAttribute(value = "Notification")
    var notification: String? = null

    @get:DynamoDbAttribute(value = "Created")
    lateinit var created: String

    @get:DynamoDbAttribute(value = "PaymentLimitTime")
    lateinit var paymentLimitTime: String

    @get:DynamoDbAttribute(value = "LastSettleDate")
    var lastSettleDate: String? = null

    @get:DynamoDbAttribute(value = "BillComingDueMessageSent")
    var billComingDueMessageSent: Boolean = false

    @get:DynamoDbAttribute(value = "Source")
    lateinit var source: String

    @get:DynamoDbAttribute(value = "PayerDocument")
    var payerDocument: String? = null

    @get:DynamoDbAttribute(value = "PayerName")
    var payerName: String? = null

    @get:DynamoDbAttribute(value = "WarningCode")
    var warningCode: Int = 0

    @get:DynamoDbAttribute(value = "ScheduledDate")
    var scheduledDate: String? = null

    @get:DynamoDbAttribute(value = "PayerAlias")
    var payerAlias: String? = null

    @get:DynamoDbAttribute(value = "AmountCalculationModel")
    var amountCalculationModel: AmountCalculationModel? = null

    @get:DynamoDbAttribute(value = "AmountCalculationOption")
    var amountCalculationOption: BillTrackingCalculateOptions? = null

    @get:DynamoDbAttribute(value = "AmountCalculationValidUntil")
    var amountCalculationValidUntil: String? = null

    @get:DynamoDbAttribute(value = "RecurrenceRule")
    var recurrenceRule: RecurrenceRuleEntity? = null

    @get:DynamoDbAttribute(value = "WaitingFunds")
    var waitingFunds: Boolean? = null

    @get:DynamoDbAttribute(value = "WaitingRetry")
    var waitingRetry: Boolean? = null

    @get:DynamoDbAttribute(value = "BatchSchedulingId")
    var batchSchedulingId: String? = null

    @get:DynamoDbAttribute(value = "FichaCompensacaoType")
    var fichaCompensacaoType: FichaCompensacaoType? = null

    @get:DynamoDbAttribute(value = "VisibleBy")
    var visibleBy: List<String> = listOf()

    @get:DynamoDbAttribute(value = "FichaCompensacaoIdNumber")
    var fichaCompensacaoIdNumber: String? = null

    @get:DynamoDbAttribute(value = "ExternalBillId")
    var externalId: String? = null

    @get:DynamoDbAttribute(value = "ExternalBillProvider")
    var externalProvider: String? = null

    @get:DynamoDbAttribute(value = "MarkedAsPaidBy")
    var markedAsPaidBy: String? = null

    @get:DynamoDbAttribute(value = "PartnerPayment")
    var partnerPayment: PartnerPaymentDocument? = null

    @get:DynamoDbAttribute(value = "FridaySubscription")
    var subscriptionFee: Boolean? = false

    @get:DynamoDbAttribute(value = "SecurityValidationResult")
    var securityValidationResult: List<String>? = null

    @get:DynamoDbConvertedBy(value = PaymentMethodsDetailEntityAttributeConverter::class)
    @get:DynamoDbAttribute(value = "PaymentMethodsDetail")
    var paymentMethodsDetail: PaymentMethodsDetailEntity? = null

    @get:DynamoDbAttribute(value = "TransactionCorrelationId")
    var transactionCorrelationId: String? = null

    @get:DynamoDbAttribute(value = "Tags")
    var tags: Set<BillTag>? = null

    @Deprecated("Use categoryId instead")
    @get:DynamoDbAttribute(value = "Category")
    var category: BillViewCategoryDocument? = null

    @get:DynamoDbAttribute(value = "CategoryId")
    var categoryId: String? = null

    @get:DynamoDbAttribute(value = "CategorySuggestions")
    var categorySuggestions: List<BillCategorySuggestionDocument> = listOf()

    @get:DynamoDbAttribute(value = "PixQrCodeData")
    var pixQrCodeData: PixQrCodeDataDocument? = null

    @get:DynamoDbAttribute(value = "Brand")
    var brand: String? = null

    @get:DynamoDbAttribute(value = "ScheduledBy")
    var scheduledBy: String? = null

    @get:DynamoDbAttribute(value = "ScheduleSource")
    var scheduleSource: String? = null

    @get:DynamoDbAttribute(value = "PaymentWalletId")
    var paymentWalletId: String? = null

    @get:DynamoDbAttribute(value = "IgnoredAt")
    var ignoredAt: String? = null

    @get:DynamoDbAttribute(value = "GoalId")
    var goalId: String? = null

    @get:DynamoDbAttribute(value = "AutomaticPixAuthorizationMaximumAmount")
    var automaticPixAuthorizationMaximumAmount: Long? = null

    @get:DynamoDbAttribute(value = "AutomaticPixData")
    var automaticPixData: AutomaticPixDataEntity? = null

    @get:DynamoDbAttribute(value = "MarkedAsPaidAt")
    var markedAsPaidAt: String? = null

    @get:DynamoDbAttribute(value = "RefundedAt")
    var refundedAt: String? = null

    @get:DynamoDbAttribute(value = "DivergentPayment")
    var divergentPayment: DivergentPaymentDocument? = null

    @get:DynamoDbAttribute(value = "PartialPayment")
    var partialPayment: PartialPaymentDocument? = null
}

@DynamoDbBean
class PartialPaymentDocument {
    var acceptPartialPayment: Boolean = false
    var qtdPagamentoParcial: Int = 0
    var qtdPagamentoParcialRegistrado: Int = 0
    var saldoAtualPagamento: Long = 0
}

@DynamoDbBean
class DivergentPaymentDocument {
    var minimumAmount: Long? = null
    var maximumAmount: Long? = null

    var amountType: PartialPaymentAmountType? = null
    var minimumPercentage: BigDecimal? = null
    var maximumPercentage: BigDecimal? = null
}

@DynamoDbBean
class BillCategorySuggestionDocument {
    lateinit var categoryId: String
    var probability: Long = 0
}

@DynamoDbBean
class PixQrCodeDataDocument {
    lateinit var type: PixQrCodeType
    lateinit var info: String
    var additionalInfo: Map<String, String>? = null
    var pixCopyAndPaste: String? = null
    var pixId: String? = null
    var fixedAmount: Long? = null
    var expiration: String? = null
    var originalAmount: Long? = null
    var automaticPixRecurringData: String? = null
}

@DynamoDbBean
class PartnerPaymentDocument {
    lateinit var partner: AccountProviderName
    var paymentId: String? = null
}

@DynamoDbBean
class BillViewCategoryDocument {
    lateinit var billCategoryId: String
    lateinit var name: String
    lateinit var icon: String
    var default: Boolean = false
}

@DynamoDbBean
data class BillRecipientDocument(
    var name: String? = null,
    var document: String? = null,
    var alias: String? = null,
    var accountType: String? = null,
    var bankNo: Long? = null,
    var routingNo: Long? = null,
    var accountNo: BigInteger? = null,
    var accountDv: String? = null,
    var bankISPB: String? = null,
    var pixKey: String? = null,
    var pixKeyType: String? = null,
    var institutionName: String? = null,
)

fun toBillRecipientDocument(recipient: Recipient?): BillRecipientDocument? {
    return recipient?.let {
        BillRecipientDocument(
            name = recipient.name,
            document = recipient.document,
            alias = recipient.alias,
        ).apply {
            recipient.bankAccount?.let {
                accountType = it.accountType.name
                bankNo = it.bankNo
                routingNo = it.routingNo
                accountNo = it.accountNo
                accountDv = it.accountDv
                bankISPB = it.ispb
            }
            recipient.pixKeyDetails?.let {
                name = it.owner.name
                accountType = it.holder.accountType.name
                routingNo = it.holder.routingNo
                accountNo = it.holder.accountNo
                accountDv = it.holder.accountDv
                bankISPB = it.holder.ispb
                pixKey = it.key.value
                pixKeyType = it.key.type.name
                institutionName = it.holder.institutionName
            }
        }
    }
}

fun toRecipient(billRecipientDocument: BillRecipientDocument?, pixQrCodeData: PixQrCodeData?): Recipient? {
    if (billRecipientDocument == null) {
        return null
    }

    val recipient = Recipient(
        name = billRecipientDocument.name.orEmpty(),
        document = billRecipientDocument.document,
        alias = billRecipientDocument.alias.orEmpty(),
        pixQrCodeData = pixQrCodeData,
    )

    return when {
        billRecipientDocument.pixKey != null -> recipient.copy(pixKeyDetails = billRecipientDocument.toPixKeyDetails())
        billRecipientDocument.accountType != null -> recipient.copy(bankAccount = billRecipientDocument.toBankAccount())
        else -> recipient
    }
}

internal fun BillRecipientDocument.toBankAccount(): BankAccount {
    return BankAccount(
        accountType = AccountType.valueOf(accountType!!),
        bankNo = bankNo,
        routingNo = routingNo!!,
        accountNo = accountNo!!,
        accountDv = accountDv!!,
        document = document!!,
        ispb = bankISPB,
    )
}

private fun BillRecipientDocument.toPixKeyDetails(): PixKeyDetails {
    return PixKeyDetails(
        key = PixKey(
            value = pixKey!!,
            type = PixKeyType.valueOf(pixKeyType!!),
        ),
        holder = PixKeyHolder(
            accountNo = accountNo!!,
            accountDv = accountDv!!,
            ispb = bankISPB!!,
            institutionName = institutionName ?: this.toPixKeyHolderInstitutionName(),
            accountType = AccountType.valueOf(accountType!!),
            routingNo = routingNo!!,
        ),
        owner = PixKeyOwner(
            name = name!!,
            document = document!!,
        ),
    )
}

private fun BillRecipientDocument.toPixKeyHolderInstitutionName(): String {
    return when (this.bankISPB) {
        "********" -> "HYPER WALLET IP LTDA"
        else -> "Não informado"
    }
}