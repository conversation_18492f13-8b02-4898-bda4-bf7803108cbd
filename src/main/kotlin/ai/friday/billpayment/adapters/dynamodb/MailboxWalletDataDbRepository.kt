package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.integrations.MailboxWalletDataRepository
import ai.friday.billpayment.app.mailbox.MailboxListType
import ai.friday.billpayment.app.wallet.WalletId
import jakarta.inject.Singleton
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val MAILBOX_LIST_KEY = "MAILBOX_WALLET_LIST"

@Singleton
class MailboxWalletDataDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<UserEmailListDataEntity>(cli, UserEmailListDataEntity::class.java)

@Singleton
class MailboxWalletDataDbRepository(
    private val client: MailboxWalletDataDynamoDAO,
) : MailboxWalletDataRepository {

    override fun add(walletId: WalletId, type: MailboxListType, email: EmailAddress) {
        client.save(
            UserEmailListDataEntity().apply {
                this.partitionKey = buildPartitionKey(walletId, type)
                this.sortKey = email.value
                this.gSIndex1PartitionKey = keyName(type)
                this.gSIndex1SortKey = email.value
                this.walletId = walletId.value
                this.mailboxEmailFilter = email.value
            },
        )
    }

    override fun remove(walletId: WalletId, type: MailboxListType, email: EmailAddress) {
        client.delete(
            partitionKey = buildPartitionKey(walletId, type),
            sortKey = email.value,
        )
    }

    override fun load(walletId: WalletId, type: MailboxListType): List<EmailAddress> {
        return client.findByPartitionKey(
            buildPartitionKey(walletId, type),
        ).map { EmailAddress(it.mailboxEmailFilter) }
    }

    override fun has(walletId: WalletId, type: MailboxListType, email: EmailAddress): Boolean {
        return client.findByPrimaryKey(
            partitionKey = buildPartitionKey(walletId, type),
            sortKey = email.value,
        ) != null
    }

    private fun keyName(type: MailboxListType) = "$MAILBOX_LIST_KEY#${type.name}"

    private fun buildPartitionKey(walletId: WalletId, type: MailboxListType) = "${walletId.value}#${keyName(type)}"
}

@DynamoDbBean
class UserEmailListDataEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1SortKey: String

    @get:DynamoDbAttribute(value = "WalletId")
    lateinit var walletId: String

    @get:DynamoDbAttribute(value = "MailboxEmailFilter")
    lateinit var mailboxEmailFilter: String
}