package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.adapters.parsers.parseListFrom
import ai.friday.billpayment.app.banking.Bank
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.integrations.FinancialInstitutionGlobalDataRepository
import jakarta.inject.Singleton
import java.time.LocalDate

private const val HOLIDAYS = "HOLIDAYS"
private const val PIX_PARTICIPANTS = "PIX_PARTICIPANTS"
private const val BANK_LIST = "BANK_LIST"

@Singleton
class FinancialInstitutionGlobalDataDbRepository(private val globalDataDbRepository: GlobalDataDbRepository) :
    FinancialInstitutionGlobalDataRepository {

    override fun saveHolidays(holidays: List<LocalDate>) = save(HOLIDAYS, holidays)

    override fun savePixParticipants(pixParticipants: List<FinancialInstitution>) =
        save(PIX_PARTICIPANTS, pixParticipants)

    override fun saveBankList(bankList: List<Bank>) = save(BANK_LIST, bankList)

    override fun loadHolidays(): List<LocalDate> = load(HOLIDAYS)?.let { parseListFrom(it) } ?: emptyList()

    override fun loadPixParticipants(): List<FinancialInstitution> =
        load(PIX_PARTICIPANTS)?.let { parseListFrom(it) } ?: emptyList()

    override fun loadBankList(): List<Bank> = load(BANK_LIST)?.let { parseListFrom(it) } ?: emptyList()

    private fun load(key: String) = globalDataDbRepository.loadGlobalData(key, key)?.value

    private fun save(key: String, value: List<Any>) = globalDataDbRepository.saveGlobalData(key, key, value)
}