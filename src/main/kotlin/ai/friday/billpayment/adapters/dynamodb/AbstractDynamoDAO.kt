package ai.friday.billpayment.adapters.dynamodb

import io.reactivex.Flowable
import jakarta.inject.Singleton
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.Key
import software.amazon.awssdk.enhanced.dynamodb.MappedTableResource
import software.amazon.awssdk.enhanced.dynamodb.TableSchema
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest
import software.amazon.awssdk.services.dynamodb.model.AttributeValue

interface DynamoDAO<T> {
    val tableName: String
    val type: Class<T>
}

enum class TransactWriteOperation {
    DELETE, PUT, UPDATE
}

@Singleton
class TransactionDynamo(private val cli: DynamoDbEnhancedClient) {
    fun <T> transactionWrite(vararg items: Triple<TransactWriteOperation, MappedTableResource<out T>, T>) {
        cli.transactWriteItems { transaction ->

            items.forEach { item ->
                val mappedTableResource = item.second as? MappedTableResource<T> ?: throw IllegalArgumentException("MappedTableResource is null. $item")
                when (item.first) {
                    TransactWriteOperation.DELETE -> item.third?.let { transaction.addDeleteItem(mappedTableResource, it) }
                    TransactWriteOperation.PUT -> item.third?.let { transaction.addPutItem(mappedTableResource, it) }
                    TransactWriteOperation.UPDATE -> item.third?.let { transaction.addUpdateItem(mappedTableResource, it) }
                }
            }
        }
    }
}

abstract class AbstractDynamoDAO<T>(
    private val cli: DynamoDbEnhancedClient,
    override val type: Class<T>,
) : DynamoDAO<T> {
    // Lazy para dar tempo do micronaut instanciar o nome da tabela
    private val table by lazy { cli.table(tableName, TableSchema.fromBean(type)) }

    fun transactionWritePut(item: T) = Triple(TransactWriteOperation.PUT, table, item)
    fun transactionWriteDelete(item: T) = Triple(TransactWriteOperation.DELETE, table, item)
    fun transactionWriteUpdate(item: T) = Triple(TransactWriteOperation.UPDATE, table, item)

    fun save(item: T) = table.putItem(item)

    // Funciona bem para atomic counter
    fun update(item: T, ignoreNulls: Boolean = false): T = table.updateItem {
        it.item(item)
        it.ignoreNulls(ignoreNulls)
    }

    fun findByPrimaryKey(
        partitionKey: String,
        sortKey: String,
    ): T? = table.getItem(partitionKey, sortKey)

    fun findByPartitionKey(partitionKey: String): List<T> = table.getItemsByPartitionKey(partitionKey)

    fun findBeginsWithOnSortKey(
        partitionKey: String,
        sortKey: String,
        scanIndexForward: Boolean = true,
    ): List<T> =
        table.queryTableOnHashKeyAndRangeKeyBeginsWith(partitionKey, sortKey, scanIndexForward = scanIndexForward)

    fun findByPartitionKeyAndScanKeyOnIndex(
        index: GlobalSecondaryIndexNames,
        partitionKey: String,
        sortKey: String,
    ): List<T> =
        table.getItemsByIndex(index, partitionKey, sortKey)

    fun findBeginsWithOnIndex(
        index: GlobalSecondaryIndexNames,
        partitionKey: String,
        sortKey: String,
        expression: String? = null,
        expressionNames: Map<String, String> = emptyMap(),
        expressionValues: Map<String, AttributeValue> = emptyMap(),
    ): List<T> =
        table.getItemsByIndexSortBeginsWith(index, partitionKey, sortKey, expression, expressionNames, expressionValues)

    fun findByPartitionKeyAndSortKeyBetween(
        partitionKey: String,
        sortKeyFrom: String,
        sortKeyTo: String,
    ): List<T> = table.queryTableOnHashKeyAndRangeKeyBetween(partitionKey, sortKeyFrom, sortKeyTo)

    fun findByPartitionKeyAndSortKeyBetweenByIndex(
        index: GlobalSecondaryIndexNames,
        partitionKey: String,
        sortKeyFrom: String,
        sortKeyTo: String,
    ): List<T> = table.queryTableOnHashKeyAndRangeKeyBetweenByIndex(index, partitionKey, sortKeyFrom, sortKeyTo)

    fun findByPrimaryKeyGreaterThanByIndex(
        index: GlobalSecondaryIndexNames,
        partitionKey: String,
        sortKey: String,
    ): List<T> = table.queryTableOnHashKeyAndRangeKeGreaterThanByIndex(index, partitionKey, sortKey)

    fun findByPrimaryKeyLessThanByIndex(
        index: GlobalSecondaryIndexNames,
        partitionKey: String,
        sortKey: String,
    ): List<T> = table.queryTableOnHashKeyAndRangeKeLessThanByIndex(index, partitionKey, sortKey)

    fun findByPrimaryKeyGreaterThanOrEqualByIndex(
        index: GlobalSecondaryIndexNames,
        partitionKey: String,
        sortKey: String,
    ): List<T> = table.queryTableOnHashKeyAndRangeKeGreaterThanOrEqualByIndex(index, partitionKey, sortKey)

    fun findByPrimaryKeyLessThanOrEqualByIndex(
        index: GlobalSecondaryIndexNames,
        partitionKey: String,
        sortKey: String,
    ): List<T> = table.queryTableOnHashKeyAndRangeKeLessThanOrEqualByIndex(index, partitionKey, sortKey)

    fun findByPartitionKeyOnIndex(
        index: GlobalSecondaryIndexNames,
        partitionKey: String,
    ): List<T> =
        table.getItemsByIndex(index, partitionKey)

    fun delete(
        partitionKey: String,
        sortKey: String,
    ): T =
        table.deleteItem(
            Key
                .builder()
                .partitionValue(partitionKey)
                .sortValue(sortKey)
                .build(),
        )
}

abstract class AbstractAsyncDynamoDAO<T>(
    cli: DynamoDbEnhancedAsyncClient,
    override val type: Class<T>,
) : DynamoDAO<T> {
    private val table by lazy { cli.table(tableName, TableSchema.fromBean(type)) }

    fun save(
        item: T,
        ignoreNulls: Boolean? = false,
    ): Mono<T> =
        table
            .updateItem {
                it.item(item)
                it.ignoreNulls(ignoreNulls)
            }.toMono()

    fun findByPrimaryKey(
        partitionKey: String,
        sortKey: String,
    ): Mono<T> =
        table
            .getItem(
                Key
                    .builder()
                    .partitionValue(partitionKey)
                    .sortValue(sortKey)
                    .build(),
            ).toMono()

    fun listByPartitionKey(partitionKey: String): Flux<T> = table.getItemsByPartitionKey(partitionKey)

    fun findByPartitionKeyOnIndex(
        index: GlobalSecondaryIndexNames,
        partitionKey: String,
    ): Flux<T> = table.getItemsByIndex(index, partitionKey)

    fun findByPrimaryKeyOnIndex(
        index: GlobalSecondaryIndexNames,
        partitionKey: String,
        sortKey: String,
        conditionalType: ConditionalType = ConditionalType.EQUALS,
    ): Flux<T> =
        table.getItemsByIndex(index, partitionKey, sortKey, conditionalType)

    fun findByPartitionKeyAndBetweenSortKeysOnIndex(
        index: GlobalSecondaryIndexNames,
        partitionKey: String,
        sortKeyFrom: String,
        sortKeyTo: String,
    ): Flux<T> =
        table.getItemsByIndexBetween(index, partitionKey, sortKeyFrom, sortKeyTo)

    fun findByPartitionKeyAndSortKeyLessThanOrEqualTo(
        index: GlobalSecondaryIndexNames,
        partitionKey: String,
        sortKey: String,
    ): Flux<T> =
        table.getItemsByIndexAndSortKeyLessThanOrEqualTo(index, partitionKey, sortKey)

    fun findBeginsWithOnIndex(
        index: GlobalSecondaryIndexNames,
        partitionKey: String,
        sortKey: String,
    ): Flowable<T> = table.getItemsByIndexSortBeginsWith(index, partitionKey, sortKey)

    fun delete(
        partitionKey: String,
        sortKey: String,
    ): Mono<T> =
        table
            .deleteItem(
                Key
                    .builder()
                    .partitionValue(partitionKey)
                    .sortValue(sortKey)
                    .build(),
            ).toMono()
}

private fun <T> DynamoDbAsyncTable<T>.getItemsByIndexSortBeginsWith(
    index: GlobalSecondaryIndexNames,
    partitionKey: String,
    sortKey: String,
): Flowable<T> =
    Flowable.fromPublisher(
        this.index(index.name).query(
            QueryEnhancedRequest.builder().queryConditional(
                QueryConditional.sortBeginsWith(keyByPartitionKeyAndSortKey(partitionKey, sortKey)),
            ).build(),
        ),
    ).flatMapIterable { it.items() }

private fun keyByPartitionKeyAndSortKey(partitionKey: String, sortKey: String) =
    Key.builder()
        .partitionValue(partitionKey)
        .sortValue(sortKey)
        .build()