package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.contact.BankAccountId
import ai.friday.billpayment.app.contact.Contact
import ai.friday.billpayment.app.contact.ContactId
import ai.friday.billpayment.app.contact.InvalidationCode
import ai.friday.billpayment.app.contact.LastUsed
import ai.friday.billpayment.app.contact.SavedBankAccount
import ai.friday.billpayment.app.contact.SavedPixKey
import ai.friday.billpayment.app.integrations.ContactRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateTimeFormat
import arrow.core.Either
import com.amazonaws.services.dynamodbv2.model.AttributeValue
import jakarta.inject.Singleton
import java.time.LocalDateTime
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

// DO NOT CHANGE: those are serialized as map keys
const val idKey = "Id"
const val typeKey = "Type"
const val bankNoKey = "BankNo"
const val routingNoKey = "RoutingNo"
const val accountNoKey = "AccountNo"
const val accountDvKey = "AccountDv"
const val invalidatedKey = "Invalidated"
const val invalidationMessageKey = "InvalidationMessage"
const val invalidationCodeKey = "InvalidationCode"
const val recipientPrefix = "RECIPIENT#"
const val bankISPBKey = "BankISPB"

private const val billIdsAttributeName = "BillIds"
private const val recurrenceIdsAttributeName = "RecurrenceIds"

@Singleton
class ContactDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<ContactEntity>(cli, ContactEntity::class.java)

@Singleton
class ContactDbRepository(private val client: ContactDynamoDAO) : ContactRepository {

    override fun save(recipient: Contact) {
        client.save(toEntity(recipient))
    }

    @Throws(ItemNotFoundException::class)
    override fun delete(accountId: AccountId, contactId: ContactId) {
        client.delete(contactId.value, accountId.value)
    }

    @Throws(ItemNotFoundException::class)
    override fun deleteBankAccount(accountId: AccountId, contactId: ContactId, bankAccountId: BankAccountId) {
        val recipientEntity = findRecipient(contactId, accountId)
        recipientEntity.bankAccounts
            .filter { it[idKey] == bankAccountId.value }
            .ifEmpty { throw ItemNotFoundException("") }
            .also { recipientEntity.bankAccounts = recipientEntity.bankAccounts.minus(it) }

        client.save(recipientEntity)
    }

    override fun deletePixKey(accountId: AccountId, contactId: ContactId, value: String) {
        val recipientEntity = findRecipient(contactId, accountId)
        recipientEntity.pixKeys
            .filter { it[idKey] == value }
            .ifEmpty { throw ItemNotFoundException("") }
            .also { recipientEntity.pixKeys = recipientEntity.pixKeys.minus(it) }

        client.save(recipientEntity)
    }

    private fun findRecipient(contactId: ContactId, accountId: AccountId) =
        client.findByPrimaryKey(contactId.value, accountId.value) ?: throw ItemNotFoundException("")

    override fun append(accountId: AccountId, contactId: ContactId, savedBankAccount: SavedBankAccount) {
        val recipientEntity = findByIdAndAccountId(contactId, accountId)
        client.update(toEntity(recipientEntity.copy(bankAccounts = recipientEntity.bankAccounts + savedBankAccount)))
    }

    override fun append(accountId: AccountId, contactId: ContactId, savedPixKey: SavedPixKey) {
        val recipientEntity = findByIdAndAccountId(contactId, accountId)
        client.update(toEntity(recipientEntity.copy(pixKeys = recipientEntity.pixKeys + savedPixKey)))
    }

    override fun updateLastUsed(accountId: AccountId, contactId: ContactId, lastUsed: LastUsed?) {
        val contact = findByIdAndAccountId(accountId = accountId, contactId = contactId)
        save(contact.copy(lastUsed = lastUsed))
    }

    private fun fail(): Nothing {
        throw IllegalArgumentException()
    }

    override fun findRecipientByAccountIDAndDocument(
        accountId: AccountId,
        document: String,
    ): Either<Exception, Contact> {
        return try {
            val entity = client.findByPartitionKeyAndScanKeyOnIndex(
                GlobalSecondaryIndexNames.GSIndex1,
                accountId.value,
                "${recipientPrefix}$document",
            )
            Either.Right(convertToSavedRecipient(entity[0]))
        } catch (e: IndexOutOfBoundsException) {
            Either.Left(ItemNotFoundException("Recipient not found for accountId $accountId and document $document"))
        } catch (e: Exception) {
            Either.Left(e)
        }
    }

    override fun findByAccountId(accountId: AccountId): List<Contact> {
        val recipientEntityList = client.findBeginsWithOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            accountId.value,
            recipientPrefix,
        )
        return recipientEntityList.map { recipientEntity -> convertToSavedRecipient(recipientEntity) }.sorted()
    }

    override fun findById(contactId: ContactId): Contact {
        val recipientEntityList = client.findByPartitionKey(contactId.value)
        return recipientEntityList.ifEmpty { throw ItemNotFoundException("Recipient ${contactId.value} not found!") }
            .map { recipientEntity -> convertToSavedRecipient(recipientEntity) }.first()
    }

    override fun findByIdOrNull(contactId: ContactId): Contact? {
        val recipientEntityList = client.findByPartitionKey(contactId.value)
        return recipientEntityList.map { recipientEntity -> convertToSavedRecipient(recipientEntity) }.firstOrNull()
    }

    override fun findByIdAndAccountId(contactId: ContactId, accountId: AccountId): Contact {
        return client.findByPrimaryKey(contactId.value, accountId.value)
            ?.let(::convertToSavedRecipient)
            ?: throw ItemNotFoundException("Recipient ${contactId.value} not found for account ${accountId.value}!")
    }

    private fun convertToMap(bankAccounts: List<SavedBankAccount>): List<Map<String, String>> {
        return bankAccounts.map { bankAccount -> convertToMap(bankAccount) }
    }

    private fun convertToMap(bankAccount: SavedBankAccount): Map<String, String> {
        val map = mutableMapOf(
            idKey to bankAccount.id.value,
            typeKey to bankAccount.accountType.name,
            bankNoKey to (bankAccount.bankNo?.toString() ?: ""),
            routingNoKey to bankAccount.routingNo.toString(),
            accountNoKey to bankAccount.accountNo.toString(),
            accountDvKey to bankAccount.accountDv,
            invalidatedKey to bankAccount.invalidated.toString(),
            bankISPBKey to (bankAccount.ispb.orEmpty()),
        )
        if (bankAccount.invalidated && bankAccount.invalidationMessage.isNotEmpty()) {
            map[invalidationMessageKey] = bankAccount.invalidationMessage
        }
        if (bankAccount.invalidated && bankAccount.invalidationCode != null) {
            map[invalidationCodeKey] = bankAccount.invalidationCode!!.name
        }
        return map.filterValues { it.isNotEmpty() }
    }

    private fun List<SavedPixKey>.convertToEntity(): List<Map<String, String>> {
        return map {
            val map = mutableMapOf(
                idKey to it.value,
                typeKey to it.type.name,
                invalidatedKey to it.invalidated.toString(),
            )
            if (it.invalidated && it.invalidationCode != null) {
                map[invalidationCodeKey] = it.invalidationCode!!.name
            }
            map
        }
    }

    private fun LastUsed?.convertToEntity(): LastUsedEntity? = this?.let {
        LastUsedEntity(bankAccountId = it.bankAccountId?.value, pixKey = it.pixKey)
    }

    private fun convertToMutableMap(savedPixKey: SavedPixKey): MutableMap<String, AttributeValue> {
        return mutableMapOf(
            idKey to AttributeValue().withS(savedPixKey.value),
            typeKey to AttributeValue().withS(savedPixKey.type.name),
        )
    }

    private fun convertToMutableMap(bankAccount: SavedBankAccount): MutableMap<String, AttributeValue> {
        return mutableMapOf(
            idKey to AttributeValue().withS(bankAccount.id.value),
            typeKey to AttributeValue().withS(bankAccount.accountType.name),
            bankNoKey to AttributeValue().withS(bankAccount.bankNo?.toString()),
            routingNoKey to AttributeValue().withS(bankAccount.routingNo.toString()),
            accountNoKey to AttributeValue().withS(bankAccount.accountNo.toString()),
            accountDvKey to AttributeValue().withS(bankAccount.accountDv),
            bankISPBKey to AttributeValue().withS(bankAccount.ispb),
        ).filterValues {
            it.s != null && it.s.isNotEmpty()
        }.toMutableMap()
    }

    private fun convertToSavedRecipient(contactEntity: ContactEntity): Contact {
        return Contact(
            id = ContactId(contactEntity.contactId),
            accountId = AccountId(contactEntity.accountId),
            alias = contactEntity.alias,
            name = contactEntity.name,
            document = contactEntity.document,
            bankAccounts = convertToSavedBankAccount(contactEntity.bankAccounts),
            pixKeys = convertToSavedPixKey(contactEntity.pixKeys),
            created = LocalDateTime.parse(contactEntity.created, dateTimeFormat).atZone(brazilTimeZone),
            lastUsed = contactEntity.lastUsed?.let { entity ->
                LastUsed(
                    bankAccountId = entity.bankAccountId?.let {
                        BankAccountId(it)
                    },
                    pixKey = entity.pixKey,
                )
            },
        )
    }

    private fun convertToSavedBankAccount(maps: List<Map<String, String>>): List<SavedBankAccount> {
        return maps.map { map -> convertToSavedBankAccount(map) }
    }

    private fun convertToSavedBankAccount(map: Map<String, String>): SavedBankAccount {
        return SavedBankAccount(
            id = BankAccountId(map[idKey] ?: fail()),
            accountType = AccountType.valueOf(map[typeKey] ?: fail()),
            bankNo = map[bankNoKey]?.toLong(),
            routingNo = map[routingNoKey]?.toLong() ?: fail(),
            accountNo = map[accountNoKey]?.toBigInteger() ?: fail(),
            accountDv = map[accountDvKey] ?: fail(),
            ispb = map[bankISPBKey],
        ).apply {
            invalidated = map.getOrDefault(invalidatedKey, false.toString()).toBoolean()
            invalidationMessage = map.getOrDefault(invalidationMessageKey, "")
            if (invalidated) {
                invalidationCode =
                    map[invalidationCodeKey]?.let { InvalidationCode.valueOf(it) } ?: InvalidationCode.INVALID_DATA
            }
        }
    }

    private fun toEntity(recipient: Contact): ContactEntity {
        return ContactEntity().apply {
            contactId = recipient.id.value
            this.accountId = recipient.accountId.value
            gSIndex1PrimaryKey = recipient.accountId.value
            gSIndex1ScanKey = "${recipientPrefix}${recipient.document}"
            document = recipient.document
            name = recipient.name
            alias = recipient.alias?.ifEmpty { null }
            bankAccounts = convertToMap(recipient.bankAccounts)
            created = recipient.created.format(dateTimeFormat)
            pixKeys = recipient.pixKeys.convertToEntity()
            lastUsed = recipient.lastUsed.convertToEntity()
        }
    }

    private fun convertToSavedPixKey(pixKeys: List<Map<String, String>>): List<SavedPixKey> {
        return pixKeys.map { map ->
            SavedPixKey(
                value = map[idKey] ?: fail(),
                type = PixKeyType.valueOf(map[typeKey] ?: fail()),
            ).apply {
                invalidated = map.getOrDefault(invalidatedKey, "false").toBoolean()
                if (invalidated) {
                    invalidationCode =
                        map[invalidationCodeKey]?.let { InvalidationCode.valueOf(it) } ?: InvalidationCode.INVALID_DATA
                }
            }
        }
    }
}

@DynamoDbBean
class ContactEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var contactId: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var accountId: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PrimaryKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1ScanKey: String

    @get:DynamoDbAttribute(value = "Document")
    lateinit var document: String

    @get:DynamoDbAttribute(value = "Name")
    lateinit var name: String

    @get:DynamoDbAttribute(value = "Alias")
    var alias: String? = null

    @get:DynamoDbAttribute(value = "BankAccounts")
    var bankAccounts: List<Map<String, String>> = listOf()

    @get:DynamoDbAttribute(value = "PixKeys")
    var pixKeys: List<Map<String, String>> = listOf()

    @get:DynamoDbAttribute(value = "Created")
    lateinit var created: String

    @get:DynamoDbAttribute(value = billIdsAttributeName)
    var billIds: Set<String>? = null

    @get:DynamoDbAttribute(value = recurrenceIdsAttributeName)
    var recurrenceIds: Set<String>? = null

    @get:DynamoDbAttribute(value = "LastUsed")
    var lastUsed: LastUsedEntity? = null
}

@DynamoDbBean
data class LastUsedEntity(
    var bankAccountId: String? = null,
    var pixKey: String? = null,
)