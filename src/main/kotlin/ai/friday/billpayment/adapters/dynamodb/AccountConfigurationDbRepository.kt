package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountConfiguration
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.AccountConfigurationRepository
import jakarta.inject.Singleton
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

const val ACCOUNT_CONFIGURATION = "AccountConfiguration"

@Singleton
class AccountConfigurationDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<AccountConfigurationDbEntity>(cli, AccountConfigurationDbEntity::class.java)

@Singleton
class AccountConfigurationDbRepository(private val client: AccountConfigurationDynamoDAO) : AccountConfigurationRepository {

    override fun save(accountConfiguration: AccountConfiguration) {
        val accountEntity = AccountConfigurationDbEntity().apply {
            partitionKey = accountConfiguration.accountId.value
            sortKey = ACCOUNT_CONFIGURATION
            accountId = accountConfiguration.accountId.value
            receiveMonthlyStatement = accountConfiguration.receiveMonthlyStatement
            freeOfFridaySubscription = accountConfiguration.freeOfFridaySubscription
        }
        client.save(accountEntity)
    }

    override fun find(accountId: AccountId): AccountConfiguration {
        return findByIdOrNull(accountId)
            ?: buildDefaultAccountConfiguration(accountId)
    }

    private fun findByIdOrNull(accountId: AccountId): AccountConfiguration? {
        val accountConfigurationEntity = client.findByPrimaryKey(
            partitionKey = accountId.value,
            sortKey = ACCOUNT_CONFIGURATION,
        )
        return accountConfigurationEntity?.let { buildAccountConfiguration(accountConfigurationEntity) }
    }

    private fun buildAccountConfiguration(accountConfigurationEntity: AccountConfigurationDbEntity): AccountConfiguration {
        return AccountConfiguration(
            accountId = AccountId(accountConfigurationEntity.accountId),
            pushNotificationTokens = emptySet(),
            receiveMonthlyStatement = accountConfigurationEntity.receiveMonthlyStatement,
            freeOfFridaySubscription = accountConfigurationEntity.freeOfFridaySubscription,
        )
    }

    private fun buildDefaultAccountConfiguration(accountId: AccountId): AccountConfiguration {
        return AccountConfiguration(
            accountId = accountId,
            pushNotificationTokens = emptySet(),
            receiveMonthlyStatement = false,
            freeOfFridaySubscription = false,
        )
    }
}

@DynamoDbBean
class AccountConfigurationDbEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String

    @get:DynamoDbAttribute(value = "AccountId")
    lateinit var accountId: String

    @get:DynamoDbAttribute(value = "ReceiveMonthlyStatement")
    var receiveMonthlyStatement: Boolean = false

    @get:DynamoDbAttribute(value = "FreeOfFridaySubscription")
    var freeOfFridaySubscription: Boolean = false
}