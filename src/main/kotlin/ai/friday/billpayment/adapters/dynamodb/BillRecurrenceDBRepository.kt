package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.contact.ContactId
import ai.friday.billpayment.app.integrations.BillRecurrenceRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.recurrence.BillRecurrence
import ai.friday.billpayment.app.recurrence.RecurrenceFrequency
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.recurrence.RecurrenceStatus
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.dateFormat
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val INDEX_1_PRIMARY_KEY = "RECURRENCE"

@Singleton
class BillRecurrenceDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<BillRecurrenceEntity>(cli, BillRecurrenceEntity::class.java)

@Singleton
class BillRecurrenceDBRepository(
    private val client: BillRecurrenceDynamoDAO,
    @Property(name = "recurrence.lastLimitDate") private val lastLimitDateString: String,
) : BillRecurrenceRepository {

    private val mapper = jacksonObjectMapper()

    override fun findAll(status: RecurrenceStatus): List<BillRecurrence> {
        return client.findByPartitionKeyAndScanKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = INDEX_1_PRIMARY_KEY,
            sortKey = status.name,
        ).map { it.toRecurrence(lastLimitDateString) }
    }

    override fun find(recurrenceId: RecurrenceId, walletId: WalletId): BillRecurrence {
        return findOrNull(recurrenceId, walletId)
            ?: throw ItemNotFoundException("Recurrence ${recurrenceId.value} not found for wallet ${walletId.value}")
    }

    override fun findOrNull(recurrenceId: RecurrenceId, walletId: WalletId): BillRecurrence? {
        return client.findByPrimaryKey(
            partitionKey = recurrenceId.value,
            sortKey = walletId.value,
        )?.toRecurrence(lastLimitDateString)
    }

    override fun findByContactId(contactId: ContactId): List<BillRecurrence> {
        return client.findByPartitionKeyAndScanKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex2,
            partitionKey = contactId.value,
            sortKey = ContactIdIndexMetadata.RECURRENCE.name,
        ).map { it.toRecurrence(lastLimitDateString) }
    }

    override fun findByWalletId(walletId: WalletId): List<BillRecurrence> {
        return client.findByPartitionKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = INDEX_1_PRIMARY_KEY,
        )
            .filter { it.sortKey == walletId.value }
            .map { it.toRecurrence(lastLimitDateString) }
    }

    override fun save(recurrence: BillRecurrence) {
        val entity = BillRecurrenceEntity().apply {
            partitionKey = recurrence.id.value
            sortKey = recurrence.walletId.value
            description = recurrence.description
            amount = recurrence.amount
            rule = RecurrenceRuleEntity().apply {
                frequency = recurrence.rule.frequency
                startDate = recurrence.rule.startDate.format(dateFormat)
                endDate = recurrence.rule.endDate?.format(dateFormat)
                pattern = recurrence.rule.pattern
            }
            contactId = recurrence.contactId?.value
            contactAccountId = recurrence.contactAccountId.value
            gSIndex2SortKey = recurrence.contactId?.let { ContactIdIndexMetadata.RECURRENCE }
            recipient = toBillRecipientDocument(recurrence)
            actionSource = mapper.writeValueAsString(recurrence.actionSource)
            created = recurrence.created.format(DateTimeFormatter.ISO_DATE_TIME)
            billIds = recurrence.bills.map { it.value }
            status = recurrence.status
            billType = recurrence.billType
            gSIndex1SortKey = recurrence.status.name
            lastBillDueDate = recurrence.lastDueDate?.format(dateFormat)
            billCategoryId = recurrence.billCategoryId?.value
        }
        client.save(entity)
    }

    private fun toBillRecipientDocument(recurrence: BillRecurrence): BillRecipientDocument {
        return BillRecipientDocument(
            name = recurrence.recipientName,
            document = recurrence.recipientDocument,
            alias = recurrence.recipientAlias,
            accountType = recurrence.recipientBankAccount?.accountType?.name,
            bankNo = recurrence.recipientBankAccount?.bankNo,
            routingNo = recurrence.recipientBankAccount?.routingNo,
            accountNo = recurrence.recipientBankAccount?.accountNo,
            accountDv = recurrence.recipientBankAccount?.accountDv,
            bankISPB = recurrence.recipientBankAccount?.ispb,
            pixKey = recurrence.recipientPixKey?.value,
            pixKeyType = recurrence.recipientPixKey?.type?.name,
            institutionName = null,
        )
    }
}

@DynamoDbBean
class BillRecurrenceEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    var gSIndex1PartitionKey: String = INDEX_1_PRIMARY_KEY

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1SortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    var contactId: String? = null

    @get:DynamoDbAttribute(value = "ContactAccountId")
    var contactAccountId: String? = null

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    var gSIndex2SortKey: ContactIdIndexMetadata? = null

    @get:DynamoDbAttribute(value = "Description")
    var description: String? = null

    @get:DynamoDbAttribute(value = "Amount")
    var amount: Long = 0

    @get:DynamoDbAttribute(value = "Rule")
    lateinit var rule: RecurrenceRuleEntity

    @get:DynamoDbAttribute(value = "Recipient")
    lateinit var recipient: BillRecipientDocument

    @get:DynamoDbAttribute(value = "Source")
    lateinit var actionSource: String

    @get:DynamoDbAttribute(value = "Created")
    lateinit var created: String

    @get:DynamoDbAttribute(value = "BillIds")
    lateinit var billIds: List<String>

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: RecurrenceStatus

    @get:DynamoDbAttribute(value = "BillType")
    lateinit var billType: BillType

    @get:DynamoDbAttribute(value = "LastBillDueDate")
    var lastBillDueDate: String? = null

    @get:DynamoDbAttribute(value = "BillCategoryId")
    var billCategoryId: String? = null
}

@DynamoDbBean
class RecurrenceRuleEntity {
    lateinit var frequency: RecurrenceFrequency
    lateinit var startDate: String
    var endDate: String? = null
    var pattern: String? = null
}

private fun BillRecurrenceEntity.toRecurrence(lastLimitDateString: String): BillRecurrence {
    val mapper = jacksonObjectMapper()
    val actionSource: ActionSource.WalletActionSource = if (actionSource.contains("Webapp")) {
        ActionSource.Api(accountId = AccountId(sortKey))
    } else {
        mapper.readerFor(ActionSource::class.java).readValue(actionSource)
    }

    return BillRecurrence(
        id = RecurrenceId(partitionKey),
        walletId = WalletId(sortKey),
        description = description.orEmpty(),
        amount = amount,
        rule = RecurrenceRule(
            frequency = rule.frequency,
            startDate = LocalDate.parse(rule.startDate, dateFormat),
            endDate = rule.endDate?.let { LocalDate.parse(rule.endDate, dateFormat) },
            pattern = rule.pattern.orEmpty(),
        ),
        contactId = contactId?.let { ContactId(it) },
        contactAccountId = contactAccountId?.let { AccountId(it) } ?: AccountId(sortKey),
        recipientName = recipient.name.orEmpty(),
        recipientDocument = recipient.document,
        recipientAlias = recipient.alias.orEmpty(),
        recipientBankAccount = recipient.accountType?.let { recipient.toBankAccount() },
        recipientPixKey = recipient.pixKey?.let {
            PixKey(
                value = it,
                type = PixKeyType.valueOf(recipient.pixKeyType!!),
            )
        },
        actionSource = actionSource,
        created = ZonedDateTime.parse(created, DateTimeFormatter.ISO_DATE_TIME),
        status = status,
        bills = billIds.map { BillId(it) },
        billType = billType,
        lastDueDate = LocalDate.parse(lastBillDueDate ?: lastLimitDateString, dateFormat),
        billCategoryId = billCategoryId?.let { PFMCategoryId(it) },
    )
}