package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.integrations.ItemNotFoundException
import com.amazonaws.auth.DefaultAWSCredentialsProviderChain
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapper
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapperConfig
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBQueryExpression
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBSaveExpression
import com.amazonaws.services.dynamodbv2.document.DynamoDB
import com.amazonaws.services.dynamodbv2.document.PrimaryKey
import com.amazonaws.services.dynamodbv2.document.spec.DeleteItemSpec
import com.amazonaws.services.dynamodbv2.document.spec.QuerySpec
import com.amazonaws.services.dynamodbv2.document.utils.ValueMap
import com.amazonaws.services.dynamodbv2.model.AttributeValue
import com.amazonaws.services.dynamodbv2.model.QueryRequest
import com.amazonaws.services.dynamodbv2.model.QueryResult
import com.amazonaws.services.dynamodbv2.model.ReturnValue
import com.amazonaws.services.dynamodbv2.model.UpdateItemRequest
import com.amazonaws.services.dynamodbv2.model.UpdateItemResult
import jakarta.inject.Singleton

const val DYNAMODB_TABLE_NAME = "Via1-BillPayment"

@Deprecated("Usar a v2 do dynamo")
@Singleton
class DynamoDbDAO(private val amazonDynamoDB: AmazonDynamoDB) {

    private val credentialsProvider: DefaultAWSCredentialsProviderChain =
        DefaultAWSCredentialsProviderChain.getInstance()
    private val mapper: DynamoDBMapper = DynamoDBMapper(amazonDynamoDB, credentialsProvider)
    private val dynamoDB: DynamoDB = DynamoDB(amazonDynamoDB)

    @Deprecated("") // TODO refatorar
    fun findTransactionTypeByAccountIdAndNSU(walletId: String?, nsu: Long): Pair<String, String> {
        val nameMap = mapOf("#type" to "Type")
        val spec = QuerySpec()
            .withKeyConditionExpression("GSIndex1PrimaryKey = :pk and GSIndex1ScanKey = :sk")
            .withValueMap(ValueMap().withString(":pk", walletId).withString(":sk", "TRANSACTION#COMPLETED#$nsu"))
            .withProjectionExpression("PrimaryKey,#type").withNameMap(nameMap)
        val outcomes = dynamoDB.getTable(DYNAMODB_TABLE_NAME).getIndex("GSIndex1").query(spec)
        return if (outcomes.iterator().hasNext()) {
            val item = outcomes.iterator().next()
            Pair(item[BILL_PAYMENT_PARTITION_KEY] as String, item["Type"] as String)
        } else {
            Pair("", "")
        }
    }

    fun <T> load(partitionKey: String, scanKey: String, type: Class<T>): T? {
        return mapper.load(type, partitionKey, scanKey)
    }

    fun save(obj: Any) {
        mapper.save(obj)
    }

    fun save(obj: Any, saveBehavior: DynamoDBMapperConfig.SaveBehavior) {
        mapper.save(obj, DynamoDBMapperConfig.builder().withSaveBehavior(saveBehavior).build())
    }

    fun save(obj: Any, dynamoDBSaveExpression: DynamoDBSaveExpression) {
        mapper.save(obj, dynamoDBSaveExpression)
    }

    fun delete(obj: Any) {
        mapper.delete(obj)
    }

    @Throws(ItemNotFoundException::class)
    fun delete(primaryKey: String?, scanKey: String?) {
        val deleteItemSpec = DeleteItemSpec()
            .withPrimaryKey(PrimaryKey(BILL_PAYMENT_PARTITION_KEY, primaryKey, BILL_PAYMENT_RANGE_KEY, scanKey))
            .withReturnValues(ReturnValue.ALL_OLD)
        val outcome = dynamoDB.getTable(DYNAMODB_TABLE_NAME).deleteItem(deleteItemSpec)
        if (outcome.deleteItemResult.attributes == null) {
            throw ItemNotFoundException("Item not found on table $DYNAMODB_TABLE_NAME")
        }
    }

    fun <T> queryIndexOnHashKeyValue(primaryKey: String?, type: Class<T>): List<T> {
        val expressionAttributeValues = mapOf(":val1" to AttributeValue(primaryKey))
        val queryExpression = DynamoDBQueryExpression<T>()
            .withIndexName("GSIndex1")
            .withConsistentRead(false)
            .withKeyConditionExpression("GSIndex1PrimaryKey = :val1")
            .withExpressionAttributeValues(expressionAttributeValues)
        return mapper.query(type, queryExpression)
    }

    fun updateItem(updateRequest: UpdateItemRequest?): UpdateItemResult = amazonDynamoDB.updateItem(updateRequest)

    fun query(request: QueryRequest): QueryResult {
        return amazonDynamoDB.query(request)
    }

    fun <T> query(request: DynamoDBQueryExpression<T>, type: Class<T>): List<T> {
        return mapper.query(type, request)
    }
}