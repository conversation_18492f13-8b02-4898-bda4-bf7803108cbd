package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.adapters.converters.BillViewingCriteriaListAttributeConverter
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.PaymentMethodNotFound
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.BillViewingCriteria
import ai.friday.billpayment.app.wallet.CriteriaForViewingBills
import ai.friday.billpayment.app.wallet.DailyPaymentLimit
import ai.friday.billpayment.app.wallet.DailyPaymentLimitType
import ai.friday.billpayment.app.wallet.Invite
import ai.friday.billpayment.app.wallet.InviteStatus
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.StoredWalletPaymentLimits
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletStatus
import ai.friday.billpayment.app.wallet.WalletType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import io.micronaut.cache.annotation.CacheConfig
import io.micronaut.cache.annotation.CacheInvalidate
import io.micronaut.cache.annotation.Cacheable
import jakarta.inject.Singleton
import java.time.Duration
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

const val WALLET_MEMBER_PREFIX = "WALLET-MEMBER"
const val WALLET_PREFIX = "WALLET"
const val WALLET_LIMIT_PREFIX = "LIMIT"
const val INVITE_PREFIX = "INVITE"
const val INVITE_REMINDER_PREFIX = "INVITE-REMINDER"

@Singleton
class WalletDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<WalletEntity>(cli, WalletEntity::class.java)

@Singleton
class WalletMemberDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<WalletMemberEntity>(cli, WalletMemberEntity::class.java)

@Singleton
class InviteDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<InviteEntity>(cli, InviteEntity::class.java)

@Singleton
class InviteReminderDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<InviteReminderNotificationEntity>(cli, InviteReminderNotificationEntity::class.java)

@Singleton
class WalletLimitDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<WalletDailyPaymentLimitEntity>(cli, WalletDailyPaymentLimitEntity::class.java)

@Singleton
@CacheConfig("wallet")
open class WalletDbRepository(
    private val walletDAO: WalletDynamoDAO,
    private val walletMemberDAO: WalletMemberDynamoDAO,
    private val inviteDAO: InviteDynamoDAO,
    private val inviteReminderDAO: InviteReminderDynamoDAO,
    private val walletLimitDAO: WalletLimitDynamoDAO,
    private val accountRepository: AccountRepository,
) : WalletRepository {

    override fun save(invite: Invite): Invite {
        val invitePermissions = MemberPermissionsEntity().apply {
            viewBills = invite.permissions.viewBills
            scheduleBills = invite.permissions.scheduleBills
            criteriaForViewingBills = invite.permissions.criteriaForViewingBills?.let {
                CriteriaForViewingBillsEntity().apply {
                    criterias = it.criterias
                }
            }
            founderContactsEnabled = invite.permissions.founderContactsEnabled
            viewBalance = invite.permissions.viewBalance
            manageMembers = invite.permissions.manageMembers
        }

        val entity = InviteEntity().apply {
            primaryKey = invite.walletId.value
            scanKey = invite.buildScanKeyWithPrefix(INVITE_PREFIX)
            gSIndex1PrimaryKey = "$INVITE_PREFIX#${invite.memberDocument}"
            gSIndex1ScanKey = "$INVITE_PREFIX#${invite.status.name}"
            gSIndex2PrimaryKey = "$INVITE_PREFIX#${invite.status.name}"
            gSIndex2ScanKey = invite.validUntil.format(dateFormat)
            memberDocument = invite.memberDocument
            memberName = invite.memberName
            memberType = invite.memberType
            permissions = invitePermissions
            status = invite.status
            validUntil = invite.validUntil.format(dateFormat)
            walletName = invite.walletName
            founderName = invite.founderName
            founderDocument = invite.founderDocument
            created = invite.created.format(DateTimeFormatter.ISO_DATE_TIME)
            updatedAt = invite.updatedAt.format(DateTimeFormatter.ISO_DATE_TIME)
            emailAddress = invite.emailAddress?.value
        }

        inviteDAO.save(entity)

        return invite
    }

    override fun save(wallet: Wallet): Wallet {
        val entity = WalletEntity().apply {
            primaryKey = wallet.id.value
            scanKey = WALLET_PREFIX
            gSIndex1PrimaryKey = wallet.founder.accountId.value
            gSIndex1ScanKey = "$WALLET_PREFIX#${wallet.status.name}"
            name = wallet.name
            status = wallet.status
            type = wallet.type
            founderId = wallet.founder.accountId.value
            paymentMethodId = wallet.paymentMethodId.value
            maxOpenInvitations = wallet.maxOpenInvitations
        }
        walletDAO.save(entity)

        wallet.allMembers.forEach {
            upsertMember(wallet.id, it)
        }

        invalidateWalletCache(walletId = wallet.id)

        return wallet
    }

    @CacheInvalidate(parameters = ["walletId"])
    open fun invalidateWalletCache(walletId: WalletId) {
        LOG.info(append("walletId", walletId.value), "invalidateWalletCache")
    }

    override fun findInvite(walletId: WalletId, memberDocument: String): Invite {
        val entities = inviteDAO.findBeginsWithOnSortKey(
            partitionKey = walletId.value,
            sortKey = "$INVITE_PREFIX#$memberDocument",
            scanIndexForward = false,
        ).ifEmpty {
            throw ItemNotFoundException(message = "Cannot find invite for walletId $walletId and document $memberDocument")
        }

        with(entities.first()) {
            return this.toInvite()
        }
    }

    override fun findInvitesFrom(walletId: WalletId, from: Duration): List<Invite> {
        val validUntilThreshold = getZonedDateTime().minus(from).format(dateFormat)

        return inviteDAO.findBeginsWithOnSortKey(
            partitionKey = walletId.value,
            sortKey = "$INVITE_PREFIX#",
            scanIndexForward = false,
        ).filter { entity ->
            entity.validUntil >= validUntilThreshold
        }.map { it.toInvite() }
    }

    override fun findInvites(document: String, inviteStatus: InviteStatus): List<Invite> {
        return inviteDAO.findByPartitionKeyAndScanKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = "$INVITE_PREFIX#$document",
            sortKey = "$INVITE_PREFIX#${inviteStatus.name}",
        ).map { it.toInvite() }
    }

    override fun findExpiringInvites(): List<Invite> {
        val yesterday = getZonedDateTime().minusDays(1).toLocalDate().format(dateFormat)
        return inviteDAO.findByPrimaryKeyLessThanOrEqualByIndex(
            index = GlobalSecondaryIndexNames.GSIndex2,
            partitionKey = "$INVITE_PREFIX#${InviteStatus.PENDING.name}",
            sortKey = yesterday,
        ).map { it.toInvite() }
    }

    override fun markInviteReminderAsSent(invite: Invite) {
        inviteReminderDAO.save(
            InviteReminderNotificationEntity().apply {
                partitionKey = invite.walletId.value
                scanKey = invite.buildScanKeyWithPrefix(INVITE_REMINDER_PREFIX)
                reminderSent = true
            },
        )
    }

    override fun isInviteReminderSent(invite: Invite): Boolean {
        val inviteReminderNotificationEntity: InviteReminderNotificationEntity? = inviteReminderDAO.findByPrimaryKey(
            partitionKey = invite.walletId.value,
            sortKey = invite.buildScanKeyWithPrefix(INVITE_REMINDER_PREFIX),
        )

        return inviteReminderNotificationEntity?.reminderSent ?: false
    }

    fun findPendingInvites(): List<Invite> {
        return inviteDAO.findByPartitionKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex2,
            partitionKey = "$INVITE_PREFIX#${InviteStatus.PENDING.name}",
        ).map { it.toInvite() }
    }

    @Cacheable(parameters = ["walletId"])
    override fun findWalletOrNull(walletId: WalletId): Wallet? {
        LOG.trace(append("walletId", walletId.value), "findWallet")

        val walletEntity = walletDAO.findByPrimaryKey(
            partitionKey = walletId.value,
            sortKey = WALLET_PREFIX,
        ) ?: return null

        val memberEntities = walletMemberDAO.findBeginsWithOnSortKey(
            partitionKey = walletId.value,
            sortKey = "ACCOUNT-",
        )

        val members = memberEntities.map { entity ->
            Member(
                accountId = AccountId(entity.scanKey),
                document = entity.document,
                name = entity.name,
                emailAddress = EmailAddress(entity.email),
                type = entity.type,
                status = entity.status ?: MemberStatus.ACTIVE,
                permissions = MemberPermissions(
                    viewBills = entity.permissions.viewBills,
                    scheduleBills = entity.permissions.scheduleBills,
                    criteriaForViewingBills = entity.permissions.criteriaForViewingBills?.let {
                        CriteriaForViewingBills(
                            criterias = it.criterias,
                        )
                    },
                    founderContactsEnabled = entity.permissions.founderContactsEnabled,
                    manageMembers = entity.permissions.manageMembers ?: MemberPermissions.of(entity.type).manageMembers,
                    viewBalance = entity.permissions.viewBalance ?: MemberPermissions.of(entity.type).viewBalance,
                    notification = entity.permissions.notification ?: true,
                ),
                created = entity.created?.let { ZonedDateTime.parse(it, DateTimeFormatter.ISO_DATE_TIME) }
                    ?: getZonedDateTime(),
                updated = entity.updatedAt?.let { ZonedDateTime.parse(it, DateTimeFormatter.ISO_DATE_TIME) }
                    ?: getZonedDateTime(),
            )
        }
        return Wallet(
            id = walletId,
            name = walletEntity.name,
            members = members,
            maxOpenInvitations = walletEntity.maxOpenInvitations,
            status = walletEntity.status,
            type = walletEntity.type,
            paymentMethodId = AccountPaymentMethodId(walletEntity.paymentMethodId),
        )
    }

    override fun saveLimit(
        walletId: WalletId,
        type: DailyPaymentLimitType,
        currentAmount: Long,
        lastAmount: Long?,
        withTimestamp: Boolean,
    ): DailyPaymentLimit {
        val markers = append("walletId", walletId.value)
            .andAppend("type", type)
            .andAppend("currentAmount", currentAmount)
            .andAppend("lastAmount", lastAmount)
        LOG.info(markers, "WalletDbRepository#saveLimit")

        val now = getZonedDateTime()
        val timestamp = now.toInstant().toEpochMilli()
        val created = now.format(DateTimeFormatter.ISO_DATE_TIME)

        val scanKey = if (withTimestamp) "$WALLET_LIMIT_PREFIX#${type.name}#$timestamp" else "$WALLET_LIMIT_PREFIX#${type.name}"

        val entity = WalletDailyPaymentLimitEntity().apply {
            primaryKey = walletId.value
            this.scanKey = scanKey
            this.walletId = walletId.value
            amount = currentAmount
            this.lastAmount = lastAmount
            this.created = created
            this.type = type
        }

        walletLimitDAO.save(entity)
        return entity.toWalletDailyPaymentLimit()
    }

    override fun deleteMonthlyLimit(walletId: WalletId) {
        walletLimitDAO.findBeginsWithOnSortKey(
            partitionKey = walletId.value,
            sortKey = "$WALLET_LIMIT_PREFIX#${DailyPaymentLimitType.MONTHLY.name}",
            scanIndexForward = false,
        ).firstOrNull()?.let {
            walletLimitDAO.delete(it.primaryKey, it.scanKey)
        }
    }

    override fun findLimitOrNull(walletId: WalletId, type: DailyPaymentLimitType): DailyPaymentLimit? {
        return walletLimitDAO.findBeginsWithOnSortKey(
            partitionKey = walletId.value,
            sortKey = "$WALLET_LIMIT_PREFIX#${type.name}",
            scanIndexForward = false,
        ).firstOrNull()?.toWalletDailyPaymentLimit()
    }

    override fun findAllLimits(walletId: WalletId): StoredWalletPaymentLimits {
        val limits = walletLimitDAO.findBeginsWithOnSortKey(
            partitionKey = walletId.value,
            sortKey = "$WALLET_LIMIT_PREFIX#",
            scanIndexForward = false,
        ).filter { it.scanKey.contains(DailyPaymentLimitType.AUTOMATIC_PIX.name) || it.scanKey.contains(DailyPaymentLimitType.DAILY.name) || it.scanKey.contains(DailyPaymentLimitType.NIGHTTIME.name) || it.scanKey.contains(DailyPaymentLimitType.MONTHLY.name) || it.scanKey.contains(DailyPaymentLimitType.WHATSAPP_PAYMENT.name) }

        val automaticPix = limits.firstOrNull { it.type == DailyPaymentLimitType.AUTOMATIC_PIX }
        val daily = limits.firstOrNull { it.type == DailyPaymentLimitType.DAILY }
        val nighttime = limits.firstOrNull { it.type == DailyPaymentLimitType.NIGHTTIME }
        val monthly = limits.firstOrNull { it.type == DailyPaymentLimitType.MONTHLY }
        val whatsAppPayment = limits.firstOrNull { it.type == DailyPaymentLimitType.WHATSAPP_PAYMENT }

        return StoredWalletPaymentLimits(
            automaticPix = automaticPix?.toWalletDailyPaymentLimit(),
            daily = daily?.toWalletDailyPaymentLimit(),
            nighttime = nighttime?.toWalletDailyPaymentLimit(),
            monthly = monthly?.toWalletDailyPaymentLimit(),
            whatsAppPayment = whatsAppPayment?.toWalletDailyPaymentLimit(),
        )
    }

    override fun findAccountPaymentMethod(walletId: WalletId): AccountPaymentMethod {
        val wallet = findWallet(walletId)
        val foundAccountPaymentMethod = accountRepository.findAccountPaymentMethodByIdAndAccountId(
            wallet.paymentMethodId,
            wallet.founder.accountId,
        )

        if (foundAccountPaymentMethod.method !is InternalBankAccount || foundAccountPaymentMethod.method.bankAccountMode != BankAccountMode.PHYSICAL) {
            throw throw PaymentMethodNotFound(walletId)
        }

        return foundAccountPaymentMethod
    }

    override fun findWallet(walletId: WalletId): Wallet {
        return findWalletOrNull(walletId) ?: throw ItemNotFoundException(walletId)
    }

    override fun findWallets(accountId: AccountId): List<Wallet> {
        val walletMemberEntities = walletMemberDAO.findByPartitionKeyAndScanKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = accountId.value,
            sortKey = WALLET_MEMBER_PREFIX,
        )
        return walletMemberEntities
            .map { entity -> findWallet(WalletId(entity.primaryKey)) }
    }

    override fun findWallets(accountId: AccountId, memberStatus: MemberStatus?): List<Wallet> {
        val walletMemberEntities = walletMemberDAO.findByPartitionKeyAndScanKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = accountId.value,
            sortKey = WALLET_MEMBER_PREFIX,
        )

        return walletMemberEntities
            .filter { memberStatus == null || it.status == memberStatus }
            .map { entity -> findWallet(WalletId(entity.primaryKey)) }
    }

    @CacheInvalidate(parameters = ["walletId"])
    override fun createMember(walletId: WalletId, member: Member) {
        val entity = member.toWalletMemberEntity(walletId).apply {
            created = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
        }

        try {
            walletMemberDAO.save(entity)
        } catch (e: Exception) {
            LOG.warn(append("walletId", walletId).andAppend("member", member), "CreateMember")
        }
    }

    @CacheInvalidate(parameters = ["walletId"])
    override fun upsertMember(walletId: WalletId, member: Member) {
        val entity = member.toWalletMemberEntity(walletId).apply {
            updatedAt = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
        }
        walletMemberDAO.save(entity)
    }

    private fun InviteEntity.toInvite() = Invite(
        walletId = WalletId(value = this.primaryKey),
        memberDocument = this.memberDocument,
        memberName = this.memberName,
        memberType = this.memberType,
        permissions = MemberPermissions(
            viewBills = this.permissions.viewBills,
            scheduleBills = this.permissions.scheduleBills,
            criteriaForViewingBills = this.permissions.criteriaForViewingBills?.let {
                CriteriaForViewingBills(
                    criterias = it.criterias,
                )
            },
            founderContactsEnabled = this.permissions.founderContactsEnabled,
            manageMembers = MemberPermissions.of(this.memberType).manageMembers,
            viewBalance = this.permissions.viewBalance
                ?: MemberPermissions.of(this.memberType).viewBalance,
            notification = this.permissions.notification ?: true,
        ),
        status = this.status,
        validUntil = LocalDate.parse(this.validUntil, dateFormat),
        walletName = this.walletName,
        founderName = this.founderName,
        founderDocument = this.founderDocument,
        created = ZonedDateTime.parse(this.created, DateTimeFormatter.ISO_DATE_TIME),
        updatedAt = ZonedDateTime.parse(this.updatedAt, DateTimeFormatter.ISO_DATE_TIME),
        emailAddress = this.emailAddress?.let { EmailAddress(it) },
    )

    private fun Invite.buildScanKeyWithPrefix(prefix: String) =
        "$prefix#$memberDocument#${created.toInstant().toEpochMilli()}"

    private fun Member.toWalletMemberEntity(walletId: WalletId): WalletMemberEntity {
        val member = this
        val memberPermissions = MemberPermissionsEntity().apply {
            viewBills = permissions.viewBills
            scheduleBills = permissions.scheduleBills
            criteriaForViewingBills = permissions.criteriaForViewingBills?.let {
                CriteriaForViewingBillsEntity().apply { criterias = it.criterias }
            }
            founderContactsEnabled = permissions.founderContactsEnabled
            viewBalance = permissions.viewBalance
            manageMembers = permissions.manageMembers
            notification = permissions.notification
        }

        return WalletMemberEntity().apply {
            primaryKey = walletId.value
            scanKey = member.accountId.value
            gSIndex1PrimaryKey = member.accountId.value
            gSIndex1ScanKey = WALLET_MEMBER_PREFIX
            name = member.name
            document = member.document
            email = member.emailAddress.value
            type = member.type
            status = member.status
            permissions = memberPermissions
            updatedAt = member.updated.format(DateTimeFormatter.ISO_DATE_TIME)
            created = member.created.format(DateTimeFormatter.ISO_DATE_TIME)
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(WalletDbRepository::class.java)
    }
}

@DynamoDbBean
class WalletEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PrimaryKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1ScanKey: String

    @get:DynamoDbAttribute(value = "Name")
    lateinit var name: String

    @get:DynamoDbAttribute(value = "Type")
    lateinit var type: WalletType

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: WalletStatus

    @get:DynamoDbAttribute(value = "FounderId")
    lateinit var founderId: String

    @get:DynamoDbAttribute(value = "PaymentMethodId")
    lateinit var paymentMethodId: String

    @get:DynamoDbAttribute(value = "MaxOpenInvitations")
    var maxOpenInvitations: Int = 0
}

@DynamoDbBean
class WalletMemberEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PrimaryKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1ScanKey: String

    @get:DynamoDbAttribute(value = "Name")
    lateinit var name: String

    @get:DynamoDbAttribute(value = "Document")
    lateinit var document: String

    @get:DynamoDbAttribute(value = "Email")
    lateinit var email: String

    @get:DynamoDbAttribute(value = "Type")
    lateinit var type: MemberType

    @get:DynamoDbAttribute(value = "Permissions")
    lateinit var permissions: MemberPermissionsEntity

    @get:DynamoDbAttribute(value = "Status")
    var status: MemberStatus? = MemberStatus.ACTIVE

    @get:DynamoDbAttribute(value = "Created")
    var created: String? = null

    @get:DynamoDbAttribute(value = "UpdatedAt")
    var updatedAt: String? = null
}

@DynamoDbBean
class InviteReminderNotificationEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbAttribute(value = "reminderSent")
    var reminderSent: Boolean = false
}

@DynamoDbBean
class InviteEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PrimaryKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1ScanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    lateinit var gSIndex2PrimaryKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    lateinit var gSIndex2ScanKey: String

    @get:DynamoDbAttribute(value = "MemberDocument")
    lateinit var memberDocument: String

    @get:DynamoDbAttribute(value = "EmailAddress")
    var emailAddress: String? = null

    @get:DynamoDbAttribute(value = "MemberName")
    lateinit var memberName: String

    @get:DynamoDbAttribute(value = "MemberType")
    lateinit var memberType: MemberType

    @get:DynamoDbAttribute(value = "Permissions")
    lateinit var permissions: MemberPermissionsEntity

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: InviteStatus

    @get:DynamoDbAttribute(value = "ValidUntil")
    lateinit var validUntil: String

    @get:DynamoDbAttribute(value = "WalletName")
    lateinit var walletName: String

    @get:DynamoDbAttribute(value = "FounderName")
    lateinit var founderName: String

    @get:DynamoDbAttribute(value = "FounderDocument")
    lateinit var founderDocument: String

    @get:DynamoDbAttribute(value = "Created")
    lateinit var created: String

    @get:DynamoDbAttribute(value = "UpdatedAt")
    lateinit var updatedAt: String
}

@DynamoDbBean
class WalletDailyPaymentLimitEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbAttribute(value = "WalletId")
    lateinit var walletId: String

    @get:DynamoDbAttribute(value = "Amount")
    var amount: Long = 0

    @get:DynamoDbAttribute(value = "LastAmount")
    var lastAmount: Long? = null

    @get:DynamoDbAttribute(value = "Created")
    lateinit var created: String

    @get:DynamoDbAttribute(value = "Type")
    lateinit var type: DailyPaymentLimitType
}

fun WalletDailyPaymentLimitEntity.toWalletDailyPaymentLimit() =
    DailyPaymentLimit(
        updatedAt = ZonedDateTime.parse(created, DateTimeFormatter.ISO_DATE_TIME),
        lastAmount = lastAmount,
        amount = amount,
        type = type,
    )

@DynamoDbBean
class MemberPermissionsEntity {

    lateinit var viewBills: BillPermission

    lateinit var scheduleBills: BillPermission

    @get:DynamoDbAttribute(value = "CriteriaForViewingBillsEntity")
    var criteriaForViewingBills: CriteriaForViewingBillsEntity? = null

    var founderContactsEnabled: Boolean = false

    var viewBalance: Boolean? = null

    var manageMembers: Boolean? = null

    var notification: Boolean? = null
}

@DynamoDbBean
data class CriteriaForViewingBillsEntity(
    @get:DynamoDbConvertedBy(value = BillViewingCriteriaListAttributeConverter::class)
    var criterias: List<BillViewingCriteria> = emptyList(),
)