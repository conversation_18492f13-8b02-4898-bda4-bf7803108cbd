package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.SystemActivity
import ai.friday.billpayment.app.account.SystemActivityKey
import ai.friday.billpayment.app.account.SystemActivityKeyType
import ai.friday.billpayment.app.account.SystemActivityType
import ai.friday.billpayment.app.integrations.SystemActivityRepository
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val AccountActivityScanKey = "ACCOUNT_ACTIVITY"
private const val DocumentActivityScanKey = "DOCUMENT_ACTIVITY"
private const val WalletActivityScanKey = "WALLET_ACTIVITY"
private const val SystemActivityScanKey = "SYSTEM_ACTIVITY"

@Singleton
class SystemActivityDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<SystemActivityEntity>(cli, SystemActivityEntity::class.java)

@Singleton
class SystemActivityDbRepository(private val client: SystemActivityDynamoDAO) : SystemActivityRepository {

    override fun save(key: SystemActivityKey, activityType: SystemActivityType, activityValue: String) {
        val entityScanKey = buildScanKey(key.type, activityType)
        val entity = SystemActivityEntity().apply {
            primaryKey = key.value
            scanKey = entityScanKey
            index1HashKey = entityScanKey
            index1RangeKey = activityValue
            this.systemId = key.value
            this.activityValue = activityValue
            this.lastUpdated = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
        }
        client.save(entity)
    }

    override fun find(key: SystemActivityKey, activityType: SystemActivityType): SystemActivity? {
        return client
            .findByPrimaryKey(
                key.value,
                buildScanKey(key.type, activityType),
            )
            ?.let { entity ->
                SystemActivity(
                    key = key,
                    value = entity.activityValue,
                    lastUpdated = entity.lastUpdated?.let { ZonedDateTime.parse(it, DateTimeFormatter.ISO_DATE_TIME) },
                )
            }
    }

    override fun find(
        keyType: SystemActivityKeyType,
        activityType: SystemActivityType,
        activityValue: String,
    ): List<String> {
        return client.findByPartitionKeyAndScanKeyOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            buildScanKey(keyType, activityType),
            activityValue,
        ).map { entity ->
            entity.systemId
        }
    }

    override fun findLowerThan(
        keyType: SystemActivityKeyType,
        activityType: SystemActivityType,
        activityValue: String,
    ): List<String> {
        return client.findByPrimaryKeyLessThanByIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            buildScanKey(keyType, activityType),
            activityValue,
        ).map { entity ->
            entity.systemId
        }
    }

    override fun findBetween(
        keyType: SystemActivityKeyType,
        activityType: SystemActivityType,
        minValue: String,
        maxValue: String,
    ): List<String> {
        return client.findByPartitionKeyAndSortKeyBetweenByIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            buildScanKey(keyType, activityType),
            minValue,
            maxValue,
        ).map { entity ->
            entity.systemId
        }
    }

    private fun buildScanKey(keyType: SystemActivityKeyType, type: SystemActivityType): String {
        val scanKeyPrefix = when (keyType) {
            SystemActivityKeyType.Account -> AccountActivityScanKey
            SystemActivityKeyType.Wallet -> WalletActivityScanKey
            SystemActivityKeyType.System -> SystemActivityScanKey
            SystemActivityKeyType.Document -> DocumentActivityScanKey
        }
        return "$scanKeyPrefix#${type.name}"
    }
}

@DynamoDbBean
class SystemActivityEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbAttribute(value = "SystemId")
    lateinit var systemId: String

    @get:DynamoDbAttribute(value = "ActivityValue")
    lateinit var activityValue: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var index1HashKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var index1RangeKey: String

    @get:DynamoDbAttribute(value = "LastUpdated")
    var lastUpdated: String? = null
}