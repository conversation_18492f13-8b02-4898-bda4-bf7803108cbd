package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.TransactionEntity.Companion.buildIndexScanKey
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.BoletoSettlementResult
import ai.friday.billpayment.app.payment.MultiplePaymentData
import ai.friday.billpayment.app.payment.NoPaymentData
import ai.friday.billpayment.app.payment.Payer
import ai.friday.billpayment.app.payment.PaymentData
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.SettlementOperation
import ai.friday.billpayment.app.payment.SinglePaymentData
import ai.friday.billpayment.app.payment.SinglePaymentMethodsDetail
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.removePrefix
import ai.friday.billpayment.app.usage.TransactionAmount
import ai.friday.billpayment.app.usage.TransactionItemAmount
import ai.friday.billpayment.app.usage.sum
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.log.andAppend
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.services.dynamodb.model.AttributeValue

private const val CREDIT_CARD_AUTHORIZATION_KEY = "CREDIT_CARD_AUTHORIZATION"

@Singleton
class TransactionDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<TransactionEntity>(cli, TransactionEntity::class.java)

@Singleton
class CreditCardChargebackTrackDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<CreditCardChargebackTrackEntity>(cli, CreditCardChargebackTrackEntity::class.java)

@Singleton
class DynamoDbTransactionRepository(
    private val transactionDAO: TransactionDynamoDAO,
    private val creditCardChargebackTrackDAO: CreditCardChargebackTrackDynamoDAO,
    private val accountRepository: AccountRepository,
    private val transactionEntityConverter: TransactionEntityConverter,
) : TransactionRepository {

    private val mapper by lazy { jacksonObjectMapper() }

    override fun save(transaction: Transaction) {
        val markers = append("transactionId", transaction.id.value)
            .andAppend("transaction", transaction)
        LOG.info(markers, "DynamoDbTransactionRepository#save")

        val transactionSettlementTargetId = transactionEntityConverter.toSettlementTargetId(transaction.settlementData.settlementTarget)

        val indexScanKey = buildIndexScanKey(
            transactionStatus = transaction.status,
            settlementOperation = transaction.settlementData.settlementOperation,
        )

        val entity = TransactionEntity().apply {
            primaryKey = transaction.id.value
            scanKey = transaction.walletId.value
            gSIndex1PrimaryKey = transaction.walletId.value
            gSIndex1ScanKey = indexScanKey
            gSIndex2PrimaryKey = transactionSettlementTargetId
            gSIndex2ScanKey = indexScanKey
            nsu = transaction.nsu
            status = transaction.status
            created = transaction.created.format(DateTimeFormatter.ISO_DATE_TIME)
            type = transaction.type
            settlementTargetId = transactionSettlementTargetId
            totalAmount = transaction.settlementData.totalAmount
            payerAccountId = transaction.payer.accountId.value
            payerDocument = transaction.payer.document
            payerName = transaction.payer.name
            actionSource = mapper.writeValueAsString(transaction.actionSource)
            correlationId = transaction.correlationId
            bankTransactionId = transaction.settlementOperationId() ?: 0
            when (transaction.paymentData) {
                is SinglePaymentData -> {
                    paymentData = PaymentDataDocument().apply {
                        this.paymentMethodId = transaction.paymentData.accountPaymentMethod.id.value
                        this.paymentMethodOwnerAccountId = transaction.paymentData.accountPaymentMethod.accountId.value
                        this.payment = transaction.paymentData.payment?.let {
                            mapper.writeValueAsString(transactionEntityConverter.toPaymentOperationEntityType(it))
                        }
                        this.infoData = transaction.paymentData.details.toPaymentMethodsDetailEntity()
                    }
                    paymentDataList = null
                    paymentDataNoOp = false
                }

                is MultiplePaymentData -> {
                    paymentData = null
                    paymentDataList = transaction.paymentData.payments.map { currentPaymentData ->
                        PaymentDataDocument().apply {
                            paymentMethodId = currentPaymentData.accountPaymentMethod.id.value
                            this.payment = currentPaymentData.payment?.let {
                                mapper.writeValueAsString(transactionEntityConverter.toPaymentOperationEntityType(it))
                            }
                            infoData = currentPaymentData.details.toPaymentMethodsDetailEntity()
                        }
                    }
                    paymentDataNoOp = false
                }

                NoPaymentData -> {
                    paymentData = null
                    paymentDataList = null
                    paymentDataNoOp = true
                }
            }

            settlementData = SettlementDataDocument().apply {
                targetId = transactionSettlementTargetId
                serviceAmountTax = transaction.settlementData.serviceAmountTax
                totalAmount = transaction.settlementData.totalAmount
                operation =
                    buildSettlementOperation(settlementOperation = transaction.settlementData.settlementOperation)
            }
        }
        val creditCardAuthorizations = transaction.paymentData.retrieveAllCreditCardAuthorization()
        if (creditCardAuthorizations.isNotEmpty()) {
            entity.paymentMethodType = PaymentMethodType.CREDIT_CARD
        }
        transactionDAO.save(entity)

        creditCardAuthorizations.forEach { (details, creditCardAuthorization) ->
            val trackEntity = CreditCardChargebackTrackEntity().apply {
                primaryKey = "$CREDIT_CARD_AUTHORIZATION_KEY#${creditCardAuthorization.tid}"
                scanKey = "#${creditCardAuthorization.authorizationCode}"
                transactionId = transaction.id.value
                payerAccountId = transaction.payer.accountId.value
                walletId = transaction.walletId.value
                paymentMethodId = details.paymentMethodId.value
                settlementTargetId = entity.settlementTargetId
                settlementAmountTotal = entity.totalAmount
                paymentMethodNetAmount = details.netAmount
                paymentMethodFeeAmount = details.feeAmount
                created = transaction.created.format(DateTimeFormatter.ISO_DATE_TIME)
            }
            creditCardChargebackTrackDAO.save(trackEntity)
        }
    }

    override fun findCreditCardUsage(accountId: AccountId, walletId: WalletId, datePattern: String): TransactionAmount {
        val itemList = transactionDAO.findBeginsWithOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = walletId.value,
            sortKey = "TRANSACTION",
            expression = "(#status = :val1 or #status = :val2) and begins_with(#created, :val3) and #attributeName = :val4 and #payerAccountId = :val5",
            expressionNames = mapOf(
                "#status" to "Status",
                "#created" to "Created",
                "#attributeName" to "PaymentMethodType",
                "#payerAccountId" to "PayerAccountId",
            ),
            expressionValues = mapOf(
                ":val1" to AttributeValue.builder().s(TransactionStatus.COMPLETED.name).build(),
                ":val2" to AttributeValue.builder().s(TransactionStatus.PROCESSING.name).build(),
                ":val3" to AttributeValue.builder().s(datePattern).build(),
                ":val4" to AttributeValue.builder().s("CREDIT_CARD").build(),
                ":val5" to AttributeValue.builder().s(accountId.value).build(),
            ),
        )

        return itemList.map { transactionEntity: TransactionEntity ->
            transactionEntity.toCreditCardTransactionAmount()
        }.flatten().sum()
    }

    override fun findTransactions(walletId: WalletId): List<Transaction> {
        return transactionDAO.findBeginsWithOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = walletId.value,
            sortKey = "TRANSACTION",
        ).map {
            it.toTransaction()
        }
    }

    override fun findTransactions(billId: BillId, status: TransactionStatus?): List<Transaction> {
        val statusString = status?.let { "${it.name}#" } ?: ""
        return transactionDAO.findBeginsWithOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex2,
            partitionKey = billId.value,
            sortKey = "TRANSACTION#$statusString",
        ).map {
            it.toTransaction()
        }
    }

    private fun TransactionEntity.toCreditCardTransactionAmount(): List<TransactionAmount> {
        val paymentData = this.paymentData
        return if (paymentData != null) {
            val paymentMethodId = AccountPaymentMethodId(paymentData.paymentMethodId)

            val itemAmount = paymentData.infoData?.let {
                val info = it as PaymentMethodsDetailCreditCardEntity
                TransactionItemAmount(
                    paymentMethodId = AccountPaymentMethodId(it.paymentMethodId),
                    totalAmount = info.totalAmount,
                    feeAmount = info.feeAmount,
                )
            } ?: this.settlementData?.let {
                TransactionItemAmount(
                    paymentMethodId = paymentMethodId,
                    totalAmount = it.totalAmount,
                    feeAmount = it.serviceAmountTax,
                )
            } ?: TransactionItemAmount(
                paymentMethodId = paymentMethodId,
                totalAmount = totalAmount,
                feeAmount = 0,
            )

            listOf(TransactionAmount(itemAmount))
        } else {
            this.paymentDataList!!.mapNotNull {
                val info = it.infoData!!
                if (info is PaymentMethodsDetailCreditCardEntity) {
                    TransactionAmount(
                        TransactionItemAmount(
                            paymentMethodId = AccountPaymentMethodId(it.paymentMethodId),
                            totalAmount = info.totalAmount,
                            feeAmount = info.feeAmount,
                        ),
                    )
                } else {
                    null
                }
            }
        }
    }

    override fun getBankTransactionId(id: TransactionId): Long {
        val transactionEntity = transactionDAO.findByPartitionKey(id.value).single()
        return transactionEntity.toTransaction().settlementData.getOperation<BoletoSettlementResult>().bankTransactionId.toLong()
    }

    override fun getAuthentication(id: TransactionId): String {
        val transactionEntity = transactionDAO.findByPartitionKey(id.value).single()

        return transactionEntity.toTransaction().settlementData.getOperation<BankTransfer>().authentication
    }

    override fun findById(id: TransactionId): Transaction {
        val transactionEntities = transactionDAO.findByPartitionKey(id.value)
        val transactionEntity =
            transactionEntities.firstOrNull() ?: throw ItemNotFoundException("Transaction $id not found")
        return transactionEntity.toTransaction()
    }

    override fun findByIdAndWallet(
        id: TransactionId,
        walletId: WalletId,
        transactionType: TransactionType?,
    ): Transaction {
        val transactionEntity = transactionDAO.findByPrimaryKey(id.value, walletId.value)
            ?: throw ItemNotFoundException("transaction $id not found for wallet $walletId${transactionType?.let { " with type $it" } ?: ""}")

        if (transactionType != null && transactionEntity.type != transactionType) {
            throw ItemNotFoundException("transaction $id not found for wallet $walletId with type $transactionType")
        }

        return transactionEntity.toTransaction()
    }

    /*
        FIXME: remover o beginsWith da busca de transation
        Mudamos o prefixo do BankOperationId de (BANK_OPERATION-$UUID) para (OPERATION-$UUID) porque o arbi só guarda 50 caracteres. Para não gerar bug com as transações em andamento deixamos o BeginsWith por enquanto.
     */
    override fun findByWalletAndStatusAndBankOperationId(
        walletId: WalletId,
        status: TransactionStatus,
        bankOperationId: BankOperationId,
    ): Transaction {
        val transactionEntities = transactionDAO.findBeginsWithOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = walletId.value,
            sortKey = buildIndexScanKey(status, bankOperationId),
        )
        val transactionEntity = transactionEntities.firstOrNull()
            ?: throw ItemNotFoundException("transaction not found for wallet $walletId, status $status and bankOperationId ${bankOperationId.value}")
        return transactionEntity.toTransaction()
    }

    override fun findTransactionTypeByWalletAndNSU(walletId: WalletId, nsu: Long): Pair<String, String> {
        val entities = transactionDAO.findByPartitionKeyAndScanKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = walletId.value,
            sortKey = "TRANSACTION#COMPLETED#$nsu",
        )
        return if (entities.isNotEmpty()) {
            val entity = entities.first()
            Pair(entity.primaryKey, entity.type.name)
        } else {
            Pair("", "")
        }
    }

    override fun findTransactionIdByWalletAndNSU(walletId: WalletId, nsu: Long): TransactionId? {
        val result = findTransactionTypeByWalletAndNSU(walletId, nsu)
        return if (result.first.isNotEmpty()) {
            TransactionId(result.first)
        } else {
            null
        }
    }

    override fun findTransactionWalletId(id: TransactionId): WalletId? {
        return transactionDAO.findByPartitionKey(id.value).map {
            WalletId(it.scanKey)
        }.firstOrNull()
    }

    override fun findByWalletAndStatusAndType(
        walletId: WalletId,
        transactionStatus: TransactionStatus,
        transactionType: TransactionType,
    ): List<Transaction> {
        return transactionDAO.findBeginsWithOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = walletId.value,
            sortKey = buildIndexScanKey(transactionStatus),
        )
            .filter { transactionType == it.type }
            .map { it.toTransaction() }
    }

    override fun hasTransactionByWalletAndStatus(
        walletId: WalletId,
        transactionStatus: TransactionStatus,
    ): Boolean {
        return transactionDAO.findBeginsWithOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = walletId.value,
            sortKey = buildIndexScanKey(transactionStatus),
        ).isNotEmpty()
    }

    private fun buildSettlementOperation(settlementOperation: SettlementOperation?): String? {
        return settlementOperation?.let { mapper.writeValueAsString(transactionEntityConverter.toSettlementOperationEntityType(it)) }
    }

    private fun TransactionEntity.toTransaction(): Transaction {
        val payer = Payer(
            AccountId(payerAccountId ?: gSIndex1PrimaryKey),
            payerDocument.orEmpty(),
            payerName.orEmpty(),
        )

        val settlementTarget = transactionEntityConverter.toSettlementTarget(this)

        val settlementOperation = settlementData?.operation?.let {
            val settlementOperationEntityType = mapper.readValue(it, SettlementOperationEntityType::class.java)
            transactionEntityConverter.toSettlementOperation(settlementOperationEntityType, settlementData!!)
        }

        val transactionId = TransactionId(primaryKey)

        return Transaction(
            id = transactionId,
            created = ZonedDateTime.parse(created, DateTimeFormatter.ISO_DATE_TIME),
            walletId = WalletId(gSIndex1PrimaryKey),
            status = status,
            type = type,
            payer = payer,
            paymentData = this.toPaymentData(payer.accountId),
            settlementData = SettlementData(
                settlementTarget = settlementTarget,
                serviceAmountTax = settlementData?.serviceAmountTax ?: 0,
                totalAmount = settlementData?.totalAmount ?: 0,
                settlementOperation = settlementOperation,
            ),
            nsu = nsu,
            actionSource = mapper.readerFor(ActionSource::class.java).readValue(actionSource),
            correlationId = this.correlationId ?: transactionId.removePrefix(),
        )
    }

    private fun TransactionEntity.buildPaymentMethodsDetails(paymentMethodId: AccountPaymentMethodId): SinglePaymentMethodsDetail {
        return when (type) {
            TransactionType.CASH_IN -> {
                val feeAmount = settlementData!!.serviceAmountTax
                val netAmount = settlementData!!.totalAmount - feeAmount

                PaymentMethodsDetailWithCreditCard(
                    paymentMethodId = paymentMethodId,
                    netAmount = netAmount,
                    feeAmount = feeAmount,
                    installments = 1,
                    calculationId = null,
                    fee = 100.0 * feeAmount / netAmount,
                )
            }

            TransactionType.GOAL_REDEMPTION -> throw IllegalStateException("should not happen, GOAL_REDEMPTION does not have payment data")
            TransactionType.BOLETO_PAYMENT,
            TransactionType.INVOICE_PAYMENT,
            TransactionType.DIRECT_INVOICE,
            TransactionType.GOAL_INVESTMENT,
            TransactionType.AUTOMATIC_PIX,
            -> {
                PaymentMethodsDetailWithBalance(
                    amount = totalAmount,
                    paymentMethodId = paymentMethodId,
                    calculationId = null,
                )
            }
        }
    }

    private fun TransactionEntity.toPaymentData(accountId: AccountId): PaymentData {
        fun toSimplePaymentData(paymentData: PaymentDataDocument): SinglePaymentData {
            val paymentMethod = accountRepository.findAccountPaymentMethodByIdAndAccountId(
                AccountPaymentMethodId(paymentData.paymentMethodId),
                paymentData.paymentMethodOwnerAccountId?.let { AccountId(it) } ?: accountId,
            )

            val paymentOperation = paymentData.payment?.let {
                val paymentEntityType: PaymentOperationEntityType =
                    mapper.readerFor(PaymentOperationEntityType::class.java)
                        .readValue(paymentData.payment)
                transactionEntityConverter.toPaymentOperation(paymentEntityType)
            }

            val details = paymentData.infoData?.let {
                it.toPaymentMethodsDetail() as SinglePaymentMethodsDetail
            } ?: buildPaymentMethodsDetails(paymentMethod.id)

            return SinglePaymentData(
                accountPaymentMethod = paymentMethod,
                payment = paymentOperation,
                details = details,
            )
        }

        return if (paymentData != null) {
            toSimplePaymentData(paymentData!!)
        } else if (paymentDataList != null) {
            MultiplePaymentData(
                payments = paymentDataList!!.map {
                    toSimplePaymentData(it)
                },
            )
        } else if (paymentDataNoOp) {
            NoPaymentData
        } else {
            throw IllegalStateException("Transaction must have payment data or payment data list")
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(DynamoDbTransactionRepository::class.java)
    }
}