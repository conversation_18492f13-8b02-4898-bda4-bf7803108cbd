package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.ReferrerRepository
import ai.friday.billpayment.app.referrer.ReferrerRegister
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val ReferrerIndexPartitionKey = "REFERRER_REGISTER"

@Singleton
class ReferrerDynamoDAO(
    cli: DynamoDbEnhancedClient,
) : AbstractBillPaymentDynamoDAO<ReferrerEntity>(cli, ReferrerEntity::class.java)

@Singleton
class ReferrerDbRepository(private val dynamoDAO: ReferrerDynamoDAO) : ReferrerRepository {

    override fun save(referrerRegister: ReferrerRegister) {
        val entity = ReferrerEntity().apply {
            partitionKey = referrerRegister.accountId.value
            sortKey = ReferrerIndexPartitionKey
            accountId = referrerRegister.accountId.value
            created = referrerRegister.created.format(DateTimeFormatter.ISO_DATE_TIME)
            referrer = referrerRegister.referrer
            referrerUrl = referrerRegister.referrerUrl
            platform = referrerRegister.platform
            appVersion = referrerRegister.appVersion
            index1HashKey = ReferrerIndexPartitionKey
            index1RangeKey = referrerRegister.platform
        }
        dynamoDAO.save(entity)
    }

    override fun find(accountId: AccountId): ReferrerRegister? {
        return dynamoDAO.findByPrimaryKey(
            accountId.value,
            ReferrerIndexPartitionKey,
        )?.toReferrer()
    }
}

private fun ReferrerEntity.toReferrer() = ReferrerRegister(
    accountId = AccountId(this.accountId),
    created = ZonedDateTime.parse(this.created, DateTimeFormatter.ISO_DATE_TIME),
    referrer = this.referrer,
    referrerUrl = this.referrerUrl,
    platform = this.platform,
    appVersion = this.appVersion,
)

@DynamoDbBean
class ReferrerEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String // ACCOUNT-ID

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String // REFERRER_REGISTER

    @get:DynamoDbAttribute("AccountId")
    lateinit var accountId: String

    @get:DynamoDbAttribute("Created")
    lateinit var created: String

    @get:DynamoDbAttribute("Referrer")
    var referrer: String? = null

    @get:DynamoDbAttribute("ReferrerUrl")
    var referrerUrl: String? = null

    @get:DynamoDbAttribute("Platform")
    lateinit var platform: String

    @get:DynamoDbAttribute("AppVersion")
    lateinit var appVersion: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute("GSIndex1PrimaryKey")
    lateinit var index1HashKey: String // REFERRER_REGISTER

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute("GSIndex1ScanKey")
    lateinit var index1RangeKey: String
}