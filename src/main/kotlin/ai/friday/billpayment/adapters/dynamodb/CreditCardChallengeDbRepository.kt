package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.CreditCardChallenge
import ai.friday.billpayment.app.account.CreditCardChallengeStatus
import ai.friday.billpayment.app.integrations.CreditCardChallengeRepository
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val CREDIT_CARD_CHALLENGE_PREFIX = "CREDIT-CARD-CHALLENGE"

@Singleton
class CreditCardChallengeDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<CreditCardChallengeEntity>(cli, CreditCardChallengeEntity::class.java)

@Singleton
class CreditCardChallengeDbRepository(
    private val client: CreditCardChallengeDynamoDAO,
) : CreditCardChallengeRepository {

    override fun findAll(
        paymentMethodId: AccountPaymentMethodId,
    ): List<CreditCardChallenge> {
        return client.findBeginsWithOnSortKey(
            partitionKey = paymentMethodId.value,
            sortKey = CREDIT_CARD_CHALLENGE_PREFIX,
            scanIndexForward = false,
        ).map { it.toCreditCardChallenge() }
    }

    override fun find(
        paymentMethodId: AccountPaymentMethodId,
        status: CreditCardChallengeStatus,
    ): CreditCardChallenge? {
        return client.findBeginsWithOnSortKey(
            partitionKey = paymentMethodId.value,
            sortKey = CREDIT_CARD_CHALLENGE_PREFIX,
            scanIndexForward = false,
        ).firstOrNull { it.status == status }?.toCreditCardChallenge()
    }

    override fun save(creditCardChallenge: CreditCardChallenge) {
        client.save(
            CreditCardChallengeEntity().apply {
                partitionKey = creditCardChallenge.paymentMethodId.value
                sortKey = "$CREDIT_CARD_CHALLENGE_PREFIX#${creditCardChallenge.createdAt.toInstant().toEpochMilli()}"
                gSIndex1PartitionKey = CREDIT_CARD_CHALLENGE_PREFIX
                gSIndex1SortKey = creditCardChallenge.status.name
                value = creditCardChallenge.value.toString()
                alternativeValue = creditCardChallenge.alternativeValue?.toString()
                accountId = creditCardChallenge.accountId.value
                attempts = creditCardChallenge.attempts
                status = creditCardChallenge.status
                createdAt = creditCardChallenge.createdAt.format(DateTimeFormatter.ISO_DATE_TIME)
                updatedAt = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
                expiresAt = creditCardChallenge.expiresAt.format(DateTimeFormatter.ISO_DATE_TIME)
                authorizationCode = creditCardChallenge.authorizationCode
                acquirerOrderId = creditCardChallenge.acquirerOrderId
                alternativeAcquirerOrderId = creditCardChallenge.alternativeAcquirerOrderId
            },
        )
    }

    fun findAll(creditCardChallengeStatus: CreditCardChallengeStatus): List<CreditCardChallenge> =
        client.findByPartitionKeyAndScanKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = CREDIT_CARD_CHALLENGE_PREFIX,
            sortKey = creditCardChallengeStatus.name,
        ).map { it.toCreditCardChallenge() }
}

@DynamoDbBean
class CreditCardChallengeEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String // PAYMENT-METHOD-ID

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String // CREDIT-CARD-CHALLENGE#{TIMESTAMP}

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PartitionKey: String // CREDIT-CARD-CHALLENGE

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1SortKey: String // STATUS

    @get:DynamoDbAttribute(value = "RegisterType")
    var registerType: String = "CreditCardChallenge"

    @get:DynamoDbAttribute(value = "TokenValue")
    lateinit var value: String

    @get:DynamoDbAttribute(value = "AlternativeTokenValue")
    var alternativeValue: String? = null

    @get:DynamoDbAttribute(value = "AccountId")
    lateinit var accountId: String

    @get:DynamoDbAttribute(value = "Attempts")
    var attempts: Int = 0

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: CreditCardChallengeStatus

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute(value = "UpdatedAt")
    lateinit var updatedAt: String

    @get:DynamoDbAttribute(value = "ExpiresAt")
    lateinit var expiresAt: String

    @get:DynamoDbAttribute(value = "AuthorizationCode")
    lateinit var authorizationCode: String

    @get:DynamoDbAttribute(value = "AcquirerOrderId")
    lateinit var acquirerOrderId: String

    @get:DynamoDbAttribute(value = "AlternativeAcquirerOrderId")
    var alternativeAcquirerOrderId: String? = null
}

private fun CreditCardChallengeEntity.toCreditCardChallenge(): CreditCardChallenge =
    CreditCardChallenge(
        paymentMethodId = AccountPaymentMethodId(partitionKey),
        accountId = AccountId(accountId),
        value = value.toLong(),
        alternativeValue = alternativeValue?.toLong(),
        attempts = attempts,
        status = status,
        createdAt = ZonedDateTime.parse(createdAt, DateTimeFormatter.ISO_DATE_TIME),
        updatedAt = ZonedDateTime.parse(updatedAt, DateTimeFormatter.ISO_DATE_TIME),
        expiresAt = ZonedDateTime.parse(expiresAt, DateTimeFormatter.ISO_DATE_TIME),
        authorizationCode = authorizationCode,
        acquirerOrderId = acquirerOrderId,
        alternativeAcquirerOrderId = alternativeAcquirerOrderId,
    )