package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import jakarta.inject.Singleton
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class GlobalDataDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<GlobalDataEntity>(cli, GlobalDataEntity::class.java)

@Singleton
open class GlobalDataDbRepository(private val client: GlobalDataDynamoDAO) {

    fun loadGlobalData(primaryKey: String, rangeKey: String) =
        client.findByPrimaryKey(primaryKey, rangeKey)

    fun saveGlobalData(primaryKey: String, scanKey: String, list: List<Any>) {
        val entity = GlobalDataEntity().apply {
            this.primaryKey = primaryKey
            this.scanKey = scanKey
            value = getObjectMapper().writeValueAsString(list)
        }
        client.save(entity)
    }
}

@DynamoDbBean
class GlobalDataEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbAttribute(value = "GlobalData")
    lateinit var value: String
}