package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.BillComingDue
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.integrations.BillComingDueRepository
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.timeFormat
import io.reactivex.Flowable
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@FridayMePoupe
class BillComingDueDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<BillComingDueEntity>(cli, BillComingDueEntity::class.java)

@FridayMePoupe
class BillComingDueDBRepository(
    private val client: BillComingDueDynamoDAO,
    private val clientAsync: BillComingDueDynamoDAOAsync,
) : BillComingDueRepository {
    private val billComingDueRegister = "BILL_COMING_DUE_REGISTER"

    override fun save(billComingDue: BillComingDue) {
        // TODO melhorar isso
        val expiration = billComingDue.dueDate.plusDays(2).atStartOfDay().toInstant(ZoneOffset.UTC).epochSecond
        val scheduleStatus = if (billComingDue.isScheduled) "SCHEDULED" else "OPEN"
        client.save(
            BillComingDueEntity().apply {
                primaryKey = billComingDue.billId.value
                scanKey = billComingDueRegister
                created = billComingDue.created.format(DateTimeFormatter.ISO_DATE_TIME)
                lastUpdated = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
                billId = billComingDue.billId.value
                walletId = billComingDue.walletId.value
                scheduled = billComingDue.isScheduled
                expirationTtl = expiration
                notificatedAt = billComingDue.notificatedAt?.format(DateTimeFormatter.ISO_DATE_TIME)
                dueDate = billComingDue.dueDate.format(dateFormat)
                paymentLimitTime = billComingDue.paymentLimitTime.format(timeFormat)
                index1HashKey = billComingDueRegister
                index1RangeKey =
                    "${billComingDue.dueDate.format(dateFormat)}#$scheduleStatus#${billComingDue.paymentLimitTime}"
                index2HashKey = billComingDue.walletId.value
                index2RangeKey =
                    "${billComingDue.dueDate.format(dateFormat)}#$scheduleStatus"
            },
        )
    }

    // FIXME Apos rodar o script que popula a tabela com os pagamentos já existentes, deverá lançar exception quando não encontrar o registro
    override fun findById(billId: BillId): BillComingDue? {
        return client.findByPrimaryKey(billId.value, billComingDueRegister)?.toBillComingDue()
    }

    override fun findByEffectiveDueDate(effectiveDueDate: LocalDate): List<BillComingDue> {
        return client.findBeginsWithOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            billComingDueRegister,
            effectiveDueDate.format(dateFormat),
        ).map {
            it.toBillComingDue()
        }
    }

    override fun findByEffectiveDueDateAsync(effectiveDueDate: LocalDate): Flowable<BillComingDue> {
        return clientAsync.findBeginsWithOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            billComingDueRegister,
            effectiveDueDate.format(dateFormat),
        ).map {
            it.toBillComingDue()
        }
    }

    override fun findOpenBills(
        effectiveDueDate: LocalDate,
        paymentLimitTime: LocalTime,
    ): Flowable<BillComingDue> {
        return clientAsync.findBeginsWithOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            billComingDueRegister,
            "${effectiveDueDate.format(dateFormat)}#OPEN#${paymentLimitTime.hour}",
        ).map {
            it.toBillComingDue()
        }
    }

    private fun BillComingDueEntity.toBillComingDue() = this.let {
        BillComingDue(
            created = ZonedDateTime.parse(it.created, DateTimeFormatter.ISO_DATE_TIME),
            lastUpdated = ZonedDateTime.parse(it.lastUpdated, DateTimeFormatter.ISO_DATE_TIME),
            billId = BillId(it.billId),
            walletId = WalletId(it.walletId),
            dueDate = LocalDate.parse(it.dueDate, dateFormat),
            paymentLimitTime = LocalTime.parse(it.paymentLimitTime, timeFormat),
            isScheduled = it.scheduled,
            notificatedAt = it.notificatedAt?.let { dt ->
                ZonedDateTime.parse(dt, DateTimeFormatter.ISO_DATE_TIME)
            },
        )
    }
}

@FridayMePoupe
class BillComingDueDynamoDAOAsync(cli: DynamoDbEnhancedAsyncClient) : AbstractBillPaymentDynamoDAOAsync<BillComingDueEntity>(cli, BillComingDueEntity::class.java)

@DynamoDbBean
class BillComingDueEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String // BILL_ID

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String // BILL_COMING_DUE_REGISTER

    @get:DynamoDbAttribute(value = "BillId")
    lateinit var billId: String

    @get:DynamoDbAttribute(value = "WalletId")
    lateinit var walletId: String

    @get:DynamoDbAttribute(value = "Created")
    lateinit var created: String

    @get:DynamoDbAttribute(value = "LastUpdated")
    lateinit var lastUpdated: String

    @get:DynamoDbAttribute(value = "scheduled")
    var scheduled: Boolean = false

    @get:DynamoDbAttribute(value = "ExpirationTTL")
    var expirationTtl: Long = 0

    @get:DynamoDbAttribute(value = "NotificatedAt")
    var notificatedAt: String? = null

    @get:DynamoDbAttribute(value = "DueDate")
    lateinit var dueDate: String

    @get:DynamoDbAttribute(value = "PaymentLimitTime")
    lateinit var paymentLimitTime: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var index1HashKey: String // BILL_COMING_DUE_REGISTER

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var index1RangeKey: String // DUEDATE#SCHEDULED#PAYMENT_LIMIT_TIME

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    lateinit var index2HashKey: String // WALLET_ID

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    lateinit var index2RangeKey: String // DUEDATE#SCHEDULED
}