package ai.friday.billpayment.adapters.dynamodb

import io.micronaut.context.annotation.Property
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient

private const val DYNAMODB_BILL_PAYMENT_TABLE_NAME = "dynamodb.billPaymentTableName"
private const val DYNAMODB_BILL_EVENT_TABLE_NAME = "dynamodb.billEventsTableName"

abstract class AbstractBillEventDynamoDAO<T>(
    cli: DynamoDbEnhancedClient,
    type: Class<T>,
) : AbstractDynamoDAO<T>(
    cli = cli,
    type = type,
) {
    @field:Property(name = DYNAMODB_BILL_EVENT_TABLE_NAME)
    private var tName: String = BILL_EVENT_TABLE_NAME // Else legacy name

    override val tableName by lazy { tName }
}

abstract class AbstractBillPaymentDynamoDAO<T>(
    cli: DynamoDbEnhancedClient,
    type: Class<T>,
) : AbstractDynamoDAO<T>(
    cli = cli,
    type = type,
) {
    @field:Property(name = DYNAMODB_BILL_PAYMENT_TABLE_NAME)
    private var tName: String = BILL_PAYMENT_TABLE_NAME // Else legacy name

    override val tableName by lazy { tName }
}

abstract class AbstractBillPaymentDynamoDAOAsync<T>(
    cli: DynamoDbEnhancedAsyncClient,
    type: Class<T>,
) : AbstractAsyncDynamoDAO<T>(
    cli = cli,
    type = type,
) {
    @field:Property(name = DYNAMODB_BILL_PAYMENT_TABLE_NAME)
    private var tName: String = BILL_PAYMENT_TABLE_NAME // Else legacy name

    override val tableName by lazy { tName }
}