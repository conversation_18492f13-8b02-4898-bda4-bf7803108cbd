package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionEvent
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionEventRepository
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionEventType
import ai.friday.billpayment.toEpochMillis
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val PartitionKeyPrefix = "IN_APP_SUBSCRIPTION_EVENT"
private const val Index1PartitionKey = "IN_APP_SUBSCRIPTION_EVENT_TYPE"

@Singleton
class InAppSubscriptionEventDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<InAppSubscriptionEventEntity>(cli, InAppSubscriptionEventEntity::class.java)

@Singleton
class InAppSubscriptionEventDbRepository(private val client: InAppSubscriptionEventDynamoDAO) : InAppSubscriptionEventRepository {
    override fun save(inAppSubscriptionEvent: InAppSubscriptionEvent) {
        val entity =
            InAppSubscriptionEventEntity().apply {
                primaryKey = "$PartitionKeyPrefix#${inAppSubscriptionEvent.accountId.value}"
                scanKey = "${inAppSubscriptionEvent.createdAt.toEpochMillis()}"
                gSIndex1PartitionKey = Index1PartitionKey
                gSIndex1ScanKey = inAppSubscriptionEvent.type.name
                eventType = inAppSubscriptionEvent.type
                createdAt = inAppSubscriptionEvent.createdAt.format(DateTimeFormatter.ISO_DATE_TIME)
                details = inAppSubscriptionEvent.payload
                accountId = inAppSubscriptionEvent.accountId.value
            }
        client.save(entity)
    }

    fun findById(accountId: AccountId): List<InAppSubscriptionEvent> {
        return client.findByPartitionKey("$PartitionKeyPrefix#${accountId.value}")
            .map { it.toInAppSubscriptionEvent() }
    }
}

private fun InAppSubscriptionEventEntity.toInAppSubscriptionEvent() = InAppSubscriptionEvent(
    accountId = AccountId(accountId),
    createdAt = ZonedDateTime.parse(createdAt),
    type = eventType,
    payload = details,
)

@DynamoDbBean
class InAppSubscriptionEventEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1ScanKey: String

    @get:DynamoDbAttribute(value = "EventType")
    lateinit var eventType: InAppSubscriptionEventType

    @get:DynamoDbAttribute(value = "EventDetails")
    lateinit var details: String

    @get:DynamoDbAttribute(value = "AccountId")
    lateinit var accountId: String
}