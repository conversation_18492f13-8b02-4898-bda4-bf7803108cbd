package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.adapters.converters.PaymentMethodsDetailEntityAttributeConverter
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.bill.BatchSchedulingId
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.schedule.LegacyPaymentMethodsDetailParser
import ai.friday.billpayment.app.integrations.ScheduledBillRepository
import ai.friday.billpayment.app.payment.ScheduleTo
import ai.friday.billpayment.app.payment.ScheduledBill
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.dateFormat
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class ScheduledBillDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<ScheduledBillEntity>(cli, ScheduledBillEntity::class.java)

@Singleton
class ScheduledBillDBRepository(private val client: ScheduledBillDynamoDAO) : ScheduledBillRepository {

    private val SCHEDULED_BILL_PREFIX = "SCHEDULED#"

    override fun save(scheduledBill: ScheduledBill) {
        client.save(buildScheduledBillEntity(scheduledBill))
    }

    override fun findScheduledBillById(billId: BillId): List<ScheduledBill> {
        val billEntity: List<ScheduledBillEntity> = client.findByPartitionKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = SCHEDULED_BILL_PREFIX + billId.value,
        )

        return billEntity
            .map { it.toScheduledBill() }
    }

    override fun delete(scheduledBill: ScheduledBill) {
        client.delete(
            partitionKey = scheduledBill.walletId.value,
            sortKey = SCHEDULED_BILL_PREFIX + scheduledBill.scheduledDate + "#" + scheduledBill.billId.value,
        )
    }

    override fun findAllScheduledBillsByWalletId(walletId: WalletId): List<ScheduledBill> {
        val entities = client.findBeginsWithOnSortKey(
            partitionKey = walletId.value,
            sortKey = SCHEDULED_BILL_PREFIX,
        )
        return entities.map { it.toScheduledBill() }
    }

    override fun findScheduledBillsByWalletIdAndScheduledDate(
        walletId: WalletId,
        scheduledDate: LocalDate,
    ): List<ScheduledBill> {
        val entities = client.findBeginsWithOnSortKey(
            partitionKey = walletId.value,
            sortKey = "$SCHEDULED_BILL_PREFIX${scheduledDate.format(DateTimeFormatter.ISO_DATE)}",
        )
        return entities.map { it.toScheduledBill() }
    }

    override fun findScheduledBillsByWalletIdAndUntilScheduledDate(
        walletId: WalletId,
        scheduledDate: LocalDate,
    ): List<ScheduledBill> {
        val entities = client.findByPartitionKeyAndSortKeyBetween(
            partitionKey = walletId.value,
            sortKeyFrom = SCHEDULED_BILL_PREFIX,
            sortKeyTo = "$SCHEDULED_BILL_PREFIX${scheduledDate.plusDays(1).format(DateTimeFormatter.ISO_DATE)}",
        )
        return entities.map { it.toScheduledBill() }
    }

    override fun findScheduledBillsByWalletIdBetween(
        walletId: WalletId,
        beginDate: LocalDate,
        endDate: LocalDate,
    ): List<ScheduledBill> {
        val entities = client.findByPartitionKeyAndSortKeyBetween(
            partitionKey = walletId.value,
            sortKeyFrom = "$SCHEDULED_BILL_PREFIX${beginDate.format(DateTimeFormatter.ISO_DATE)}",
            sortKeyTo = "$SCHEDULED_BILL_PREFIX${endDate.plusDays(1).format(DateTimeFormatter.ISO_DATE)}",
        )
        return entities.map { it.toScheduledBill() }
    }

    private fun buildScheduledBillEntity(scheduledBill: ScheduledBill): ScheduledBillEntity {
        return ScheduledBillEntity().apply {
            walletId = scheduledBill.walletId.value
            metadata = SCHEDULED_BILL_PREFIX + scheduledBill.scheduledDate + "#" + scheduledBill.billId.value
            gSIndex1PrimaryKey = SCHEDULED_BILL_PREFIX + scheduledBill.billId.value
            gSIndex1ScanKey = scheduledBill.walletId.value
            scheduledDate = scheduledBill.scheduledDate.format(dateFormat)
            billId = scheduledBill.billId.value
            paymentMethodId =
                scheduledBill.paymentMethodsDetail.retrievePaymentMethodIds()
                    .firstOrNull()?.value // TODO - vai gerar null. Remover em um próximo deploy
            billType = scheduledBill.billType
            amount = scheduledBill.amount
            scheduleTo = scheduledBill.scheduleTo
            paymentLimitTime = scheduledBill.paymentLimitTime?.format(DateTimeFormatter.ofPattern("HH:mm"))
            expires = scheduledBill.expires
            paymentMethodsDetail = scheduledBill.paymentMethodsDetail.toPaymentMethodsDetailEntity()
            batchSchedulingId = scheduledBill.batchSchedulingId?.value
            selfTransfer = scheduledBill.isSelfTransfer
        }
    }
}

private fun ScheduledBillEntity.toScheduledBill(): ScheduledBill =
    ScheduledBill(
        WalletId(walletId),
        BillId(billId),
        LocalDate.parse(scheduledDate, dateFormat),
        billType,
        amount,
        scheduleTo ?: ScheduleTo.DUE_DATE,
        paymentLimitTime = paymentLimitTime?.let { LocalTime.parse(it) },
        expires = expires ?: true,
        paymentMethodsDetail = this.paymentMethodsDetail?.toPaymentMethodsDetail()
            ?: LegacyPaymentMethodsDetailParser.parse(
                amount = this.amount,
                paymentMethodId = this.paymentMethodId?.let { AccountPaymentMethodId(it) },
            ),
        batchSchedulingId = this.batchSchedulingId?.let { BatchSchedulingId(it) },
        isSelfTransfer = this.selfTransfer ?: false,
    )

@DynamoDbBean
class ScheduledBillEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var walletId: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var metadata: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PrimaryKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1ScanKey: String

    @get:DynamoDbAttribute(value = "ScheduledDate")
    lateinit var scheduledDate: String

    @get:DynamoDbAttribute(value = "BillId")
    lateinit var billId: String

    @get:DynamoDbAttribute(value = "PaymentMethodId")
    var paymentMethodId: String? = null

    @get:DynamoDbAttribute(value = "Amount")
    var amount: Long = 0

    @get:DynamoDbAttribute(value = "BillType")
    lateinit var billType: BillType

    @get:DynamoDbAttribute(value = "ScheduleTo")
    var scheduleTo: ScheduleTo? = null

    @get:DynamoDbAttribute(value = "PaymentLimitTime")
    var paymentLimitTime: String? = null

    @get:DynamoDbAttribute(value = "Expires")
    var expires: Boolean? = true

    @get:DynamoDbAttribute(value = "PaymentMethodsDetail")
    @get:DynamoDbConvertedBy(value = PaymentMethodsDetailEntityAttributeConverter::class)
    var paymentMethodsDetail: PaymentMethodsDetailEntity? = null

    @get:DynamoDbAttribute(value = "BatchSchedulingId")
    var batchSchedulingId: String? = null

    @get:DynamoDbAttribute(value = "selfTransfer")
    var selfTransfer: Boolean? = null
}