package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.forecast.ForecastPeriod
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.itp.ConsentId
import ai.friday.billpayment.app.itp.PaymentIntent
import ai.friday.billpayment.app.itp.PaymentIntentDetails
import ai.friday.billpayment.app.itp.PaymentIntentId
import ai.friday.billpayment.app.itp.PaymentIntentRepository
import ai.friday.billpayment.app.itp.PaymentIntentStatus
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.wallet.WalletId
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

const val PAYMENT_INTENT_PREFIX = "PAYMENT_INTENT"

@Singleton
class PaymentIntentDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<PaymentIntentEntity>(cli, PaymentIntentEntity::class.java)

@Singleton
class PaymentIntentDbRepository(private val client: PaymentIntentDynamoDAO) : PaymentIntentRepository {
    @field:Property(name = "accountRegister.pixKey.emailDomain")
    lateinit var defaultPixKeyDomain: String

    override fun save(paymentIntent: PaymentIntent): PaymentIntent {
        val entity = paymentIntent.toPaymentIntentEntity()
        client.save(entity)
        return paymentIntent
    }

    override fun find(paymentIntentId: PaymentIntentId): PaymentIntent {
        return client.findByPrimaryKey(
            partitionKey = paymentIntentId.value,
            sortKey = PAYMENT_INTENT_PREFIX,
        )?.toPaymentIntent(defaultPixKeyDomain) ?: throw ItemNotFoundException("PaymentIntent was not found")
    }

    override fun findByConsentId(consentId: ConsentId): PaymentIntent {
        return client.findByPartitionKeyAndScanKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = consentId.value,
            sortKey = "CONSENT_ID",
        )
            .ifEmpty { throw ItemNotFoundException("PaymentIntent was not found") }
            .single().toPaymentIntent(defaultPixKeyDomain)
    }
}

private fun PaymentIntent.toPaymentIntentEntity(): PaymentIntentEntity {
    return this.let { paymentIntent ->
        PaymentIntentEntity().apply {
            partitionKey = paymentIntent.paymentIntentId.value
            sortKey = PAYMENT_INTENT_PREFIX
            paymentIntentId = paymentIntent.paymentIntentId.value
            accountId = paymentIntent.accountId.value
            walletId = paymentIntent.walletId.value
            document = paymentIntent.document
            authorizationServerId = paymentIntent.authorizationServerId
            authorizationServerName = paymentIntent.authorizationServerName
            bankNo = paymentIntent.bankNo
            bankISPB = paymentIntent.bankISPB
            routingNo = paymentIntent.routingNo
            accountNo = paymentIntent.accountNo
            accountDv = paymentIntent.accountDv
            accountType = paymentIntent.accountType
            forecastPeriod = paymentIntent.forecastPeriod
            includeScheduledBillsOnly = paymentIntent.includeScheduledBillsOnly
            amount = if (paymentIntent.details is PaymentIntentDetails.WithPixKey) {
                paymentIntent.details.amount
            } else {
                null
            }
            pixKey = if (paymentIntent.details is PaymentIntentDetails.WithPixKey) {
                paymentIntent.details.pixKey.value
            } else {
                null
            }
            qrCode = if (paymentIntent.details is PaymentIntentDetails.WithQRCode) {
                paymentIntent.details.qrCode
            } else {
                null
            }
            paymentIntentStatus = paymentIntent.status
            if (paymentIntent.consentId != null) {
                gSIndex1PartitionKey = paymentIntent.consentId.value
                gSIndex1SortKey = "CONSENT_ID"
            }
        }
    }
}

private fun PaymentIntentEntity.toPaymentIntent(defaultPixKeyDomain: String): PaymentIntent {
    return this.let { paymentIntentEntity ->
        PaymentIntent(
            paymentIntentId = PaymentIntentId(paymentIntentEntity.paymentIntentId),
            accountId = AccountId(paymentIntentEntity.accountId),
            walletId = WalletId(paymentIntentEntity.walletId),
            document = paymentIntentEntity.document,
            authorizationServerId = paymentIntentEntity.authorizationServerId,
            authorizationServerName = paymentIntentEntity.authorizationServerName,
            bankNo = paymentIntentEntity.bankNo,
            bankISPB = paymentIntentEntity.bankISPB,
            routingNo = paymentIntentEntity.routingNo,
            accountNo = paymentIntentEntity.accountNo,
            accountDv = paymentIntentEntity.accountDv,
            accountType = paymentIntentEntity.accountType,
            forecastPeriod = paymentIntentEntity.forecastPeriod,
            includeScheduledBillsOnly = paymentIntentEntity.includeScheduledBillsOnly ?: (paymentIntentEntity.forecastPeriod == ForecastPeriod.TODAY),
            details = PaymentIntentDetails.build(
                pixKey = if (paymentIntentEntity.pixKey != null) {
                    PixKey(paymentIntentEntity.pixKey!!, PixKeyType.EMAIL)
                } else {
                    PixKey("${paymentIntentEntity.document}@$defaultPixKeyDomain", PixKeyType.EMAIL)
                },
                amount = paymentIntentEntity.amount,
                qrCode = paymentIntentEntity.qrCode,
            ),
            status = paymentIntentEntity.paymentIntentStatus ?: PaymentIntentStatus.CREATED,
            consentId = if (paymentIntentEntity.gSIndex1PartitionKey != null) ConsentId(paymentIntentEntity.gSIndex1PartitionKey!!) else null,
        )
    }
}

@DynamoDbBean
class PaymentIntentEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String

    @get:DynamoDbAttribute(value = "PaymentIntentId")
    lateinit var paymentIntentId: String

    @get:DynamoDbAttribute(value = "AccountId")
    lateinit var accountId: String

    @get:DynamoDbAttribute(value = "WalletId")
    lateinit var walletId: String

    @get:DynamoDbAttribute(value = "Document")
    lateinit var document: String

    @get:DynamoDbAttribute(value = "AuthorizationServerId")
    lateinit var authorizationServerId: String

    @get:DynamoDbAttribute(value = "AuthorizationServerName")
    lateinit var authorizationServerName: String

    @get:DynamoDbAttribute(value = "BankNo")
    var bankNo: Long? = null

    @get:DynamoDbAttribute(value = "BankISPB")
    var bankISPB: String? = null

    @get:DynamoDbAttribute(value = "RoutingNo")
    var routingNo: Long = 0

    @get:DynamoDbAttribute(value = "AccountNo")
    var accountNo: Long = 0

    @get:DynamoDbAttribute(value = "AccountDv")
    var accountDv: String = ""

    @get:DynamoDbAttribute(value = "AccountType")
    var accountType: AccountType? = null

    @get:DynamoDbAttribute(value = "ForecastPeriod")
    var forecastPeriod: ForecastPeriod? = null

    @get:DynamoDbAttribute(value = "IncludeScheduledBillsOnly")
    var includeScheduledBillsOnly: Boolean? = null

    @get:DynamoDbAttribute(value = "Amount")
    var amount: Long? = null

    @get:DynamoDbAttribute(value = "QRCode")
    var qrCode: String? = null

    @get:DynamoDbAttribute(value = "PIXKey")
    var pixKey: String? = null

    @get:DynamoDbAttribute(value = "PaymentIntentStatus")
    var paymentIntentStatus: PaymentIntentStatus? = null

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    var gSIndex1PartitionKey: String? = null // ConsentId

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    var gSIndex1SortKey: String? = null
}