package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.feature.Features
import ai.friday.billpayment.app.feature.FeaturesRepository
import ai.friday.billpayment.app.feature.FeaturesType
import ai.friday.billpayment.app.integrations.MaintenanceServicesEnum
import ai.friday.morning.log.andAppend
import io.micronaut.cache.annotation.CacheConfig
import io.micronaut.cache.annotation.CacheInvalidate
import io.micronaut.cache.annotation.Cacheable
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class FeaturesDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<FeaturesEntity>(cli, FeaturesEntity::class.java)

@CacheConfig("features")
@Singleton
open class FeaturesDbRepository(
    private val client: FeaturesDynamoDAO,
) : FeaturesRepository {
    private val logger = LoggerFactory.getLogger(FeaturesDbRepository::class.java)

    @Cacheable
    override fun getAll(): Features {
        logger.info("FeaturesDbRepository#getAll")

        return client.findByPartitionKey("FEATURES")
            .singleOrNull()
            ?.toDomain()
            ?: Features(maintenanceMode = false, maintenanceServices = emptyList(), proxy = false, tefAtSettlementHandler = false)
    }

    override fun update(type: FeaturesType, enabled: Boolean): Features {
        logger.info(append("type", type.name).andAppend("enabled", enabled), "FeaturesDbRepository#update")

        val features = getAll()

        return when (type) {
            FeaturesType.MAINTENANCE_MODE -> features.copy(maintenanceMode = enabled)
            FeaturesType.PROXY -> features.copy(proxy = enabled)
            FeaturesType.TEF_AT_SETTLEMENT_HANDLER -> features.copy(tefAtSettlementHandler = enabled)
        }.also { client.save(it.toEntity()) }
    }

    @CacheInvalidate
    open fun invalidate() {
        logger.info("FeaturesDbRepository#invalidate")
    }
}

private fun Features.toEntity(): FeaturesEntity {
    return this.let { features ->
        FeaturesEntity().apply {
            primaryKey = "FEATURES"
            scanKey = "ALL"
            maintenanceMode = features.maintenanceMode
            maintenanceServices = features.maintenanceServices
            proxy = features.proxy
        }
    }
}

private fun FeaturesEntity.toDomain() = Features(
    maintenanceMode = maintenanceMode,
    maintenanceServices = maintenanceServices,
    proxy = proxy,
    tefAtSettlementHandler = tefAtSettlementHandler,
)

@DynamoDbBean
class FeaturesEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String // FEATURES

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String // ALL

    @get:DynamoDbAttribute(value = "MaintenanceMode")
    var maintenanceMode: Boolean = false

    @get:DynamoDbAttribute(value = "Proxy")
    var proxy: Boolean = false

    @get:DynamoDbAttribute(value = "TefAtSettlementHandler")
    var tefAtSettlementHandler: Boolean = false

    @get:DynamoDbAttribute(value = "MaintenanceServices")
    var maintenanceServices: List<MaintenanceServicesEnum> = listOf()
}