package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.banking.ExternalBankAccount
import ai.friday.billpayment.app.integrations.ExternalBankAccountRespository
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import jakarta.inject.Singleton
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnoreNulls
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val externalBankAccountPrefix = "EXTERNAL-BANK-ACCOUNT#"

@Singleton
class ExternalBankAccountDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<ExternalBankAccountEntity>(cli, ExternalBankAccountEntity::class.java)

@Singleton
class ExternalBankAccountDbRepository(private val client: ExternalBankAccountDynamoDAO) : ExternalBankAccountRespository {
    override fun saveLastUsed(externalBankAccount: ExternalBankAccount) {
        val scanKey = "${externalBankAccountPrefix}${externalBankAccount.bankNo}#${externalBankAccount.bankISPB}#${externalBankAccount.routingNo}#${externalBankAccount.accountNo}#${externalBankAccount.accountDv}"

        val entity = ExternalBankAccountEntity().apply {
            this.primaryKey = externalBankAccount.document.value
            this.scanKey = scanKey
            this.bankNo = externalBankAccount.bankNo
            this.bankISPB = externalBankAccount.bankISPB
            this.routingNo = externalBankAccount.routingNo
            this.accountNo = externalBankAccount.accountNo
            this.accountDv = externalBankAccount.accountDv
            this.accountType = externalBankAccount.accountType
            this.lastUsed = getZonedDateTime().toInstant().toEpochMilli()
        }

        client.update(entity, true)
    }

    override fun findLastUsed(document: Document): ExternalBankAccount? {
        val entities = client.findBeginsWithOnSortKey(
            partitionKey = document.value,
            sortKey = externalBankAccountPrefix,
        )

        val entity = entities.maxByOrNull {
            it.lastUsed
        }

        return entity?.toExternalBankAccount()
    }

    override fun findAll(document: Document): List<ExternalBankAccount> {
        val entities = client.findBeginsWithOnSortKey(
            partitionKey = document.value,
            sortKey = externalBankAccountPrefix,
        )

        return entities.map(ExternalBankAccountEntity::toExternalBankAccount)
    }
}

fun ExternalBankAccountEntity.toExternalBankAccount() = ExternalBankAccount(
    document = Document(value = primaryKey),
    bankNo = bankNo,
    bankISPB = bankISPB,
    routingNo = routingNo,
    accountNo = accountNo,
    accountDv = accountDv,
    accountType = accountType,
    lastUsed = lastUsed,
)

@DynamoDbBean
class ExternalBankAccountEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbIgnoreNulls
    @get:DynamoDbAttribute(value = "BankNo")
    var bankNo: Long? = null

    @get:DynamoDbIgnoreNulls
    @get:DynamoDbAttribute(value = "BankISPB")
    var bankISPB: String? = null

    @get:DynamoDbAttribute(value = "RoutingNo")
    var routingNo: Long = 0

    @get:DynamoDbAttribute(value = "AccountNo")
    var accountNo: Long = 0

    @get:DynamoDbAttribute(value = "AccountDv")
    lateinit var accountDv: String

    @get:DynamoDbIgnoreNulls
    @get:DynamoDbAttribute(value = "AccountType")
    var accountType: String? = null

    @get:DynamoDbAttribute(value = "LastUsed")
    var lastUsed: Long = 0
}