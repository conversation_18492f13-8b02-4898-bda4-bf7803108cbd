package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.pfm.DefaultWalletBillCategory
import ai.friday.billpayment.app.pfm.DefaultWalletBillCategoryRepository
import ai.friday.billpayment.app.pfm.PFMCategoryId
import jakarta.inject.Singleton
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class DefaultWalletBillCategoryDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<DefaultWalletBillCategoryEntity>(cli, DefaultWalletBillCategoryEntity::class.java)

@Singleton
class DefaultWalletBillCategoryDbRepository(
    private val client: DefaultWalletBillCategoryDynamoDAO,
) : DefaultWalletBillCategoryRepository {
    override fun save(defaultWalletBillCategory: DefaultWalletBillCategory) {
        client.save(defaultWalletBillCategory.toDefaultBillCategoryEntity())
    }

    override fun findAll(): List<DefaultWalletBillCategory> {
        return client.findByPartitionKey(defaultBillCategoryPartitionKey).map {
            it.toDefaultBillCategory()
        }
    }

    override fun delete(defaultWalletBillCategory: DefaultWalletBillCategory) {
        client.delete(defaultBillCategoryPartitionKey, defaultWalletBillCategory.categoryId.value)
    }

    private fun DefaultWalletBillCategory.toDefaultBillCategoryEntity(): DefaultWalletBillCategoryEntity {
        val category = this
        return DefaultWalletBillCategoryEntity().apply {
            partitionKey = defaultBillCategoryPartitionKey
            scanKey = category.categoryId.value
            name = category.name
            billCategoryIcon = category.icon
        }
    }

    private fun DefaultWalletBillCategoryEntity.toDefaultBillCategory() = DefaultWalletBillCategory(
        categoryId = PFMCategoryId(value = this.scanKey),
        name = this.name,
        icon = this.billCategoryIcon,
    )
}

private const val defaultBillCategoryPartitionKey = "DEFAULT_WALLET_BILL_CATEGORY"

@DynamoDbBean
class DefaultWalletBillCategoryEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String // DEFAULT_WALLET_BILL_CATEGORY

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String // CategoryId

    @get:DynamoDbAttribute(value = "Name")
    lateinit var name: String

    @get:DynamoDbAttribute(value = "BillCategoryIcon")
    lateinit var billCategoryIcon: String
}