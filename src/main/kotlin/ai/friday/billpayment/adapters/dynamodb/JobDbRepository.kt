package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.integrations.JobRepository
import ai.friday.billpayment.app.job.Job
import ai.friday.morning.date.dateTimeFormat
import jakarta.inject.Singleton
import java.time.Duration
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class JobDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<JobEntity>(cli, JobEntity::class.java)

@Singleton
class JobDbRepository(private val client: JobDynamoDAO) : JobRepository {
    override fun findAll(): List<Job> {
        return client.findByPartitionKey("JOB").map { it.toJob() }
    }

    override fun save(job: Job) {
        val entity = JobEntity().apply {
            partitionKey = "JOB"
            sortKey = job.name
            name = job.name
            crons = job.crons
            fixedDelay = job.fixedDelay?.toString()
            running = job.running
            lastStartTime = job.lastStartTime?.format(dateTimeFormat)
            lastElapsedMinutes = job.lastElapsedMinutes
            shouldLock = job.shouldLock
            lockAtLeastFor = job.lockAtLeastFor.toString()
            lockAtMostFor = job.lockAtMostFor.toString()
            shutdownGracefully = job.shutdownGracefully
            shutdownGracefullyMaxWaitTime = job.shutdownGracefullyMaxWaitTime
        }
        client.save(entity)
    }
}

@DynamoDbBean
class JobEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String // JOB

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String // JOB-NAME

    @get:DynamoDbAttribute("RegisterType")
    var registerType: String = "Job"

    @get:DynamoDbAttribute("Name")
    lateinit var name: String

    @get:DynamoDbAttribute("Crons")
    lateinit var crons: List<String>

    @get:DynamoDbAttribute("FixedDelay")
    var fixedDelay: String? = null

    @get:DynamoDbAttribute("Running")
    var running: Boolean = false

    @get:DynamoDbAttribute("LastStartTime")
    var lastStartTime: String? = null

    @get:DynamoDbAttribute("LastElapsedMinutes")
    var lastElapsedMinutes: Long? = null

    @get:DynamoDbAttribute("ShouldLock")
    var shouldLock: Boolean = false

    @get:DynamoDbAttribute("LockAtLeastFor")
    lateinit var lockAtLeastFor: String

    @get:DynamoDbAttribute("LockAtMostFor")
    lateinit var lockAtMostFor: String

    @get:DynamoDbAttribute("ShutdownGracefully")
    var shutdownGracefully: Boolean = false

    @get:DynamoDbAttribute("ShutdownGracefullyMaxWaitTime")
    var shutdownGracefullyMaxWaitTime: Int = 0
}

fun JobEntity.toJob(): Job = Job(
    name = this.name,
    crons = this.crons,
    fixedDelay = this.fixedDelay?.let { Duration.parse(it) },
    running = this.running,
    lastStartTime = this.lastStartTime?.let {
        ZonedDateTime.parse(it, dateTimeFormat)
    },
    lastElapsedMinutes = this.lastElapsedMinutes,
    shouldLock = this.shouldLock,
    lockAtLeastFor = Duration.parse(this.lockAtLeastFor),
    lockAtMostFor = Duration.parse(this.lockAtMostFor),
    shutdownGracefully = this.shutdownGracefully,
    shutdownGracefullyMaxWaitTime = this.shutdownGracefullyMaxWaitTime,
)