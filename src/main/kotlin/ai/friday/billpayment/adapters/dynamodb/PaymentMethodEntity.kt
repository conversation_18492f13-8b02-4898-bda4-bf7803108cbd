package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.app.banking.BankAccountMode
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbFlatten
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@DynamoDbBean
class PaymentMethodEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    var typeIndex: PaymentMethodType? = null

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    var paymentIdIndex: String? = null

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    var hmac: String? = null

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    var hmacIndex: String? = null

    @get:DynamoDbAttribute(value = "PaymentMethodId")
    lateinit var paymentMethodId: String

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: AccountPaymentMethodStatus

    @get:DynamoDbAttribute(value = "Created")
    lateinit var created: String

    @get:DynamoDbAttribute(value = "ActivatedAt")
    var activatedAt: String? = null

    @get:DynamoDbAttribute(value = "Type")
    lateinit var type: PaymentMethodType

    @get:DynamoDbAttribute(value = "ProviderName")
    var providerName: AccountProviderName? = null

    @get:DynamoDbFlatten
    var creditCard: CreditCardComplexType? = null

    @get:DynamoDbFlatten
    var bankAccount: BankAccountComplexType? = null
}

@DynamoDbBean
class BankAccountComplexType {
    @get:DynamoDbAttribute(value = "BankAccountType")
    lateinit var bankAccountType: String

    @get:DynamoDbAttribute(value = "BankNo")
    var bankNo: Long = 0

    @get:DynamoDbAttribute(value = "BankRoutingNo")
    var bankRoutingNo: Long = 0

    @get:DynamoDbAttribute(value = "BankAccountNo")
    var bankAccountNo: Long = 0

    @get:DynamoDbAttribute(value = "BankAccountDv")
    lateinit var bankAccountDv: String

    @get:DynamoDbAttribute(value = "Document")
    lateinit var document: String

    @get:DynamoDbAttribute(value = "BankAccountMode")
    var bankAccountMode: BankAccountMode = BankAccountMode.PHYSICAL
}

@DynamoDbBean
class CreditCardComplexType {
    @get:DynamoDbAttribute(value = "CreditCardBrand")
    lateinit var creditCardBrand: String

    @get:DynamoDbAttribute(value = "CreditCardPAN")
    lateinit var creditCardPAN: String

    @get:DynamoDbAttribute(value = "CreditCardExpiryDate")
    lateinit var creditCardExpiryDate: String

    @get:DynamoDbAttribute(value = "CreditCardBin")
    var bin: String? = null

    @get:DynamoDbAttribute(value = "CreditCardToken")
    var token: String? = null

    @get:DynamoDbAttribute(value = "CreditCardProvider")
    var provider: String? = null

    @get:DynamoDbAttribute(value = "CreditCardCardType")
    var cardType: String? = null

    @get:DynamoDbAttribute(value = "CreditCardForeignCard")
    var foreignCard: Boolean? = null

    @get:DynamoDbAttribute(value = "CreditCardCorporateCard")
    var corporateCard: String? = null

    @get:DynamoDbAttribute(value = "CreditCardIssuer")
    var issuer: String? = null

    @get:DynamoDbAttribute(value = "CreditCardIssuerCode")
    var issuerCode: String? = null

    @get:DynamoDbAttribute(value = "CreditCardPrepaid")
    var prepaid: String? = null

    @get:DynamoDbAttribute(value = "CreditCardStatus")
    var status: String? = null

    @get:DynamoDbAttribute(value = "HMac")
    var hmac: String? = null

    @get:DynamoDbAttribute(value = "CreditCardRiskLevel")
    var riskLevel: RiskLevel? = null

    @get:DynamoDbAttribute(value = "CreditCardExternalId")
    var externalId: String? = null

    @get:DynamoDbAttribute(value = "CreditCardExternalProvider")
    var externalProvider: String? = null

    @get:DynamoDbAttribute(value = "CreditCardLastFourDigits")
    var lastFourDigits: String? = null

    @get:DynamoDbAttribute(value = "CreditCardMainCard")
    var mainCard: Boolean? = null
}