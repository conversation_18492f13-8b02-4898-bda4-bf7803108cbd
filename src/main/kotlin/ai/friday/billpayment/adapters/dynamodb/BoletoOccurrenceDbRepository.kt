package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.conciliation.BoletoOccurrence
import ai.friday.billpayment.app.integrations.BoletoOccurrenceRepository
import ai.friday.morning.date.dateFormat
import jakarta.inject.Singleton
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val PARTITION_KEY = "BOLETO_CONCILIATION_OCCURRENCE"

@Singleton
class BoletoOccurrenceDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<BoletoOccurrenceEntity>(cli, BoletoOccurrenceEntity::class.java)

@Singleton
class BoletoOccurrenceDbRepository(private val client: BoletoOccurrenceDynamoDAO) : BoletoOccurrenceRepository {

    override fun save(occurrence: BoletoOccurrence) {
        val entity = BoletoOccurrenceEntity().apply {
            partitionKey = PARTITION_KEY
            rangeKey = occurrence.toRangeKey()
            walletId = occurrence.walletId.value
            nsu = occurrence.nsu
            date = occurrence.date.format(dateFormat)
            createDate = occurrence.createDate.format(dateFormat)
            reason = occurrence.reason
            externalTransactionId = occurrence.externalTransactionId
            digitableBarCode = occurrence.barCode.digitable
            amount = occurrence.amount
            financialServiceGateway = occurrence.financialServiceGateway
        }

        client.save(entity)
    }

    override fun exists(occurrence: BoletoOccurrence): Boolean =
        client.findByPrimaryKey(PARTITION_KEY, occurrence.toRangeKey()) != null

    private fun BoletoOccurrence.toRangeKey() = "${walletId.value}#$nsu"
}

@DynamoDbBean
class BoletoOccurrenceEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var rangeKey: String

    @get:DynamoDbAttribute(value = "WalletId")
    lateinit var walletId: String

    @get:DynamoDbAttribute(value = "NSU")
    lateinit var nsu: String

    @get:DynamoDbAttribute(value = "Date")
    lateinit var date: String

    @get:DynamoDbAttribute(value = "CreateDate")
    lateinit var createDate: String

    @get:DynamoDbAttribute(value = "Reason")
    lateinit var reason: String

    @get:DynamoDbAttribute(value = "ExternalTransactionId")
    lateinit var externalTransactionId: String

    @get:DynamoDbAttribute(value = "DigitableBarcode")
    lateinit var digitableBarCode: String

    @get:DynamoDbAttribute(value = "Amount")
    var amount: Long = 0

    @get:DynamoDbAttribute(value = "FinancialInstitutionGateway")
    lateinit var financialServiceGateway: FinancialServiceGateway
}