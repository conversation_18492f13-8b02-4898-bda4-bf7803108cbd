package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.adapters.converters.PaymentMethodsDetailEntityAttributeConverter
import ai.friday.billpayment.app.Acquirer
import ai.friday.billpayment.app.account.CreditCardPaymentStatus
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.payment.ErrorSource
import ai.friday.billpayment.app.payment.SettlementOperation
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import kotlin.properties.Delegates
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val transactionIndexScanKeyPrefix = "TRANSACTION#"

@DynamoDbBean
class TransactionEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PrimaryKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1ScanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    lateinit var gSIndex2PrimaryKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    lateinit var gSIndex2ScanKey: String

    @get:DynamoDbAttribute(value = "Type")
    lateinit var type: TransactionType

    @get:DynamoDbAttribute(value = "PaymentMethodId")
    var paymentMethodId: String? = null

    @get:DynamoDbAttribute(value = "PaymentMethodType")
    var paymentMethodType: PaymentMethodType? = null

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: TransactionStatus

    @get:DynamoDbAttribute(value = "Description")
    var description: String? = null

    @get:DynamoDbAttribute(value = "TotalAmount")
    var totalAmount: Long = 0

    @get:DynamoDbAttribute(value = "Nsu")
    var nsu: Long = 0

    @get:DynamoDbAttribute(value = "BankTransactionId")
    var bankTransactionId: Long = 0

    @get:DynamoDbAttribute(value = "Created")
    lateinit var created: String

    @get:DynamoDbAttribute(value = "CreditCardAuthorization")
    var creditCardAuthorization: CreditCardAuthorizationDocument? = null

    @get:DynamoDbAttribute(value = "OpenBankingStatus")
    var openBankingStatus: String? = null

    @get:DynamoDbAttribute(value = "ErrorSource")
    var errorSource: ErrorSource? = null

    @get:DynamoDbAttribute(value = "Retryable")
    var retryable: Boolean? = null

    @get:DynamoDbAttribute(value = "PayerDocument")
    var payerDocument: String? = null

    @get:DynamoDbAttribute(value = "PayerName")
    var payerName: String? = null

    @get:DynamoDbAttribute(value = "PayerAccountId")
    var payerAccountId: String? = null

    @get:DynamoDbAttribute(value = "PaymentData")
    var paymentData: PaymentDataDocument? = null

    @get:DynamoDbAttribute(value = "PaymentDataList")
    var paymentDataList: List<PaymentDataDocument>? = null

    @get:DynamoDbAttribute(value = "PaymentDataNoOp")
    var paymentDataNoOp: Boolean = false

    @get:DynamoDbAttribute(value = "BillId")
    lateinit var settlementTargetId: String

    @get:DynamoDbAttribute(value = "SettlementData")
    var settlementData: SettlementDataDocument? = null

    @get:DynamoDbAttribute(value = "ActionSource")
    var actionSource: String =
        jacksonObjectMapper().writeValueAsString(ActionSource.Webapp(Role.OWNER)) // TODO - Remover valor default quando a base for acertada via script

    @get:DynamoDbAttribute(value = "Authentication")
    var authentication: String? = null

    @get:DynamoDbAttribute(value = "CorrelationId")
    var correlationId: String? = null

    companion object {
        fun buildIndexScanKey(transactionStatus: TransactionStatus, settlementOperation: SettlementOperation?): String {
            return settlementOperation?.let {
                buildIndexScanKey(transactionStatus, it.settlementId())
            } ?: buildIndexScanKey(transactionStatus)
        }

        fun buildIndexScanKey(transactionStatus: TransactionStatus, bankOperationId: BankOperationId) =
            "${transactionIndexScanKeyPrefix}${transactionStatus.name}#${bankOperationId.value}"

        fun buildIndexScanKey(transactionStatus: TransactionStatus, settlementOperationId: String) =
            "${transactionIndexScanKeyPrefix}${transactionStatus.name}#$settlementOperationId"

        fun buildIndexScanKey(transactionStatus: TransactionStatus) =
            "${transactionIndexScanKeyPrefix}${transactionStatus.name}"
    }
}

@DynamoDbBean
class CreditCardAuthorizationDocument {
    lateinit var acquirer: String
    lateinit var transactionId: String
    lateinit var status: String
    var acquirerTid: String? = null
    var amount: Long? = null
    var acquirerReturnCode: String? = null
    lateinit var acquirerReturnMessage: String
}

@DynamoDbBean
class PaymentDataDocument {
    @get:DynamoDbAttribute(value = "PaymentMethodId")
    lateinit var paymentMethodId: String

    @get:DynamoDbAttribute(value = "PaymentMethodOwnerAccountId")
    var paymentMethodOwnerAccountId: String? = null

    var payment: String? = null

    @get:DynamoDbConvertedBy(value = PaymentMethodsDetailEntityAttributeConverter::class)
    var infoData: PaymentMethodsDetailEntity? = null
}

@DynamoDbBean
class SettlementDataDocument {

    @get:DynamoDbAttribute(value = "BillId")
    lateinit var targetId: String
    var serviceAmountTax: Long = 0
    var totalAmount: Long by Delegates.notNull()
    var operation: String? = null
}

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME)
sealed interface SettlementOperationEntityType {
    @JsonTypeName("BOLETO")
    data class BoletoPayment(
        val gateway: String,
        val status: String,
        val bankTransactionId: String,
        val externalNsu: Long,
        val externalTerminal: String,
        val errorCode: String,
        val errorDescription: String?,
        val authentication: String?,
        val paymentPartnerName: String?,
        val finalPartnerName: FinancialServiceGateway?,
    ) : SettlementOperationEntityType

    @JsonTypeName("INVOICE")
    data class BankTransferSettlement(
        val status: BankOperationStatus,
        val operationID: String,
        val gateway: String,
        val authentication: String?,
        val errorDescription: String?,
    ) : SettlementOperationEntityType

    @JsonTypeName("GOAL_INVESTMENT")
    data class GoalInvestment(
        val operationId: String,
        val investmentRequestId: String,
        var status: String,
        val amount: Long,
        val errorDescription: String = "",
        val provider: String,
        val product: ProductEntity?,
        val positionId: String?,
        val maturityDate: String?,
    ) : SettlementOperationEntityType

    @JsonTypeName("GOAL_REDEMPTION")
    data class GoalRedemption(
        val operationId: String,
        val status: String,
        val redemptionGroupExternalId: String?,
        val redemptionExternalIds: List<String>,
        val amount: Long,
        val errorDescription: String = "",
        val provider: String?,
    ) : SettlementOperationEntityType
}

@DynamoDbBean
class ProductEntity {
    lateinit var provider: String
    lateinit var productId: String
    lateinit var name: String
    lateinit var index: String
    var indexPercentage: Int = 0
    var minTransactionValue: Long = 0
    var maxTransactionValue: Long = 0
    lateinit var tradeTimeLimit: String
}

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME)
sealed interface PaymentOperationEntityType {
    @JsonTypeName("BALANCE")
    data class Balance(
        val operationID: String,
        val status: String,
        val amount: Long,
        val timeout: Boolean,
        val errorDescription: String,
        val ongoingRefundOperationID: String?,
        val paymentGateway: FinancialServiceGateway?,
    ) : PaymentOperationEntityType

    @JsonTypeName("CREDIT_CARD")
    data class CreditCard(
        val acquirer: Acquirer,
        val originalAcquirer: String?,
        val transactionId: String,
        val status: CreditCardPaymentStatus,
        val acquirerTid: String?,
        val amount: Long?,
        val acquirerReturnCode: String?,
        val acquirerReturnMessage: String,
        val tid: String?,
        val authorizationCode: String?,
    ) : PaymentOperationEntityType

    @JsonTypeName("FRAUD_PREVENTION")
    data class FraudPrevention(
        val error: String,
    ) : PaymentOperationEntityType

    @JsonTypeName("GOAL_INVESTMENT")
    data class GoalInvestment(
        val operationId: String?,
        val status: String,
        val amount: Long,
        val errorDescription: String = "",
    ) : PaymentOperationEntityType
}

@DynamoDbBean
class CreditCardChargebackTrackEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbAttribute(value = "TransactionId")
    lateinit var transactionId: String

    @get:DynamoDbAttribute(value = "PayerAccountId")
    lateinit var payerAccountId: String

    @get:DynamoDbAttribute(value = "WalletId")
    lateinit var walletId: String

    @get:DynamoDbAttribute(value = "PaymentMethodId")
    lateinit var paymentMethodId: String

    @get:DynamoDbAttribute(value = "SettlementTargetId")
    lateinit var settlementTargetId: String

    @get:DynamoDbAttribute(value = "SettlementAmountTotal")
    var settlementAmountTotal: Long = 0

    @get:DynamoDbAttribute(value = "PaymentMethodNetAmount")
    var paymentMethodNetAmount: Long = 0

    @get:DynamoDbAttribute(value = "PaymentMethodFeeAmount")
    var paymentMethodFeeAmount: Long = 0

    @get:DynamoDbAttribute(value = "Created")
    lateinit var created: String
}