package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.adapters.quod.ValidateCreditCardScoreRequestTO
import ai.friday.billpayment.adapters.quod.ValidateCreditCardScoreResponseTO
import ai.friday.billpayment.adapters.quod.ValidateOwnershipRequestTO
import ai.friday.billpayment.adapters.quod.ValidateOwnershipResponseTO
import jakarta.inject.Singleton
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class CreditCardOwnershipValidationDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<CreditCardOwnershipValidationResponseEntity>(cli, CreditCardOwnershipValidationResponseEntity::class.java)

@Singleton
class CreditCardScoreValidationDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<CreditCardScoreResponseEntity>(cli, CreditCardScoreResponseEntity::class.java)

@Singleton
class CreditCardValidationDbRepository(
    private val creditCardOwnershipValidationDAO: CreditCardOwnershipValidationDynamoDAO,
) {
    private val prefix = "CREDIT_CARD_OWNERSHIP_VALIDATION"
    fun save(creditCardOwnershipValidation: CreditCardOwnershipValidation) {
        val maskedPan =
            "${creditCardOwnershipValidation.request.authReportRequest.searchBy.ccbin}#${creditCardOwnershipValidation.request.authReportRequest.searchBy.creditCardNumber}"
        val entity = CreditCardOwnershipValidationResponseEntity().apply {
            this.partitionKey = "$prefix#${creditCardOwnershipValidation.request.authReportRequest.searchBy.cpf}"
            this.scanKey = "$maskedPan#${creditCardOwnershipValidation.timestamp}"
            this.maskedPan = maskedPan
            this.timestamp = creditCardOwnershipValidation.timestamp.toString()
            this.requestJson = getObjectMapper().writeValueAsString(creditCardOwnershipValidation.request)
            this.responseJson = getObjectMapper().writeValueAsString(creditCardOwnershipValidation.response)
            this.version = creditCardOwnershipValidation.apiVersion
        }

        creditCardOwnershipValidationDAO.save(entity)
    }

    fun find(cpf: String, bin: String?, lastFourDigits: String?): List<CreditCardOwnershipValidation> {
        val entities = bin?.let {
            creditCardOwnershipValidationDAO.findBeginsWithOnSortKey(
                partitionKey = "$prefix#$cpf",
                sortKey = "$bin#${lastFourDigits.orEmpty()}",
            )
        } ?: creditCardOwnershipValidationDAO.findByPartitionKey("$prefix#$cpf")

        return entities.map {
            CreditCardOwnershipValidation(
                apiVersion = it.version,
                request = getObjectMapper().readValue(it.requestJson, ValidateOwnershipRequestTO::class.java),
                response = getObjectMapper().readValue(it.responseJson, ValidateOwnershipResponseTO::class.java),
                timestamp = it.timestamp.toLong(),
            )
        }
    }
}

@Singleton
class CreditCardScoreDbRepository(
    private val creditCardScoreValidationDAO: CreditCardScoreValidationDynamoDAO,
) {
    private val prefix = "CREDIT_CARD_SCORE_VALIDATION"
    fun save(creditCardScoreValidation: CreditCardScoreValidation) {
        val mask =
            "${creditCardScoreValidation.request.authCCScoreRequest.searchBy.ccbin}#${creditCardScoreValidation.request.authCCScoreRequest.searchBy.creditCardNumber}"
        val entity = CreditCardScoreResponseEntity().apply {
            this.partitionKey = "$prefix#${creditCardScoreValidation.request.authCCScoreRequest.searchBy.cpf}"
            this.scanKey = "$mask#${creditCardScoreValidation.timestamp}"
            this.maskedPan = mask
            this.timestamp = creditCardScoreValidation.timestamp.toString()
            this.requestJson = getObjectMapper().writeValueAsString(creditCardScoreValidation.request)
            this.responseJson = getObjectMapper().writeValueAsString(creditCardScoreValidation.response)
            this.version = creditCardScoreValidation.apiVersion
        }

        creditCardScoreValidationDAO.save(entity)
    }

    fun find(cpf: String, bin: String?, lastFourDigits: String?): List<CreditCardScoreValidation> {
        val entities = bin?.let {
            creditCardScoreValidationDAO.findBeginsWithOnSortKey(
                partitionKey = "$prefix#$cpf",
                sortKey = "$bin#${lastFourDigits.orEmpty()}",
                scanIndexForward = false,
            )
        } ?: creditCardScoreValidationDAO.findByPartitionKey("$prefix#$cpf")

        return entities.map {
            CreditCardScoreValidation(
                apiVersion = it.version,
                request = getObjectMapper().readValue(it.requestJson, ValidateCreditCardScoreRequestTO::class.java),
                response = getObjectMapper().readValue(it.responseJson, ValidateCreditCardScoreResponseTO::class.java),
                timestamp = it.timestamp.toLong(),
            )
        }
    }
}

@DynamoDbBean
class CreditCardOwnershipValidationResponseEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var maskedPan: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var timestamp: String

    @get:DynamoDbAttribute(value = "RequestJson")
    lateinit var requestJson: String

    @get:DynamoDbAttribute(value = "ResponseJson")
    lateinit var responseJson: String

    @get:DynamoDbAttribute(value = "Version")
    lateinit var version: String
}

data class CreditCardOwnershipValidation(
    val apiVersion: String,
    val request: ValidateOwnershipRequestTO,
    val response: ValidateOwnershipResponseTO,
    val timestamp: Long,
)

@DynamoDbBean
class CreditCardScoreResponseEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var maskedPan: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var timestamp: String

    @get:DynamoDbAttribute(value = "RequestJson")
    lateinit var requestJson: String

    @get:DynamoDbAttribute(value = "ResponseJson")
    lateinit var responseJson: String

    @get:DynamoDbAttribute(value = "Version")
    lateinit var version: String
}

data class CreditCardScoreValidation(
    val apiVersion: String,
    val request: ValidateCreditCardScoreRequestTO,
    val response: ValidateCreditCardScoreResponseTO,
    val timestamp: Long,
)