package ai.friday.billpayment.adapters.dynamodb

const val BILL_PAYMENT_TABLE_NAME = "Via1-BillPayment"
const val BILL_EVENT_TABLE_NAME = "Via1-BillEvents"
const val USER_EVENT_TABLE_NAME = "Via1-UserEvents"
const val BILLS_SEARCH_TABLE_NAME = "Via1-BillsSearch"
const val OPEN_FINANCE_DATA_TABLE_NAME = "Friday-OpenFinanceData"
const val INDEX_1 = "GSIndex1"
const val INDEX_2 = "GSIndex2"
const val INDEX_3 = "GSIndex3"
const val INDEX_4 = "GSIndex4"

const val BILL_PAYMENT_PARTITION_KEY = "PrimaryKey"
const val BILL_PAYMENT_RANGE_KEY = "ScanKey"

const val OPEN_FINANCE_DATA_PARTITION_KEY = "PartitionKey"
const val OPEN_FINANCE_DATA_RANGE_KEY = "RangeKey"