package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.integrations.UtilityAccountRepository
import ai.friday.billpayment.app.utilityaccount.Utility
import ai.friday.billpayment.app.utilityaccount.UtilityAccount
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionDetails
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionStatus
import ai.friday.billpayment.app.utilityaccount.UtilityAccountErrorFiles
import ai.friday.billpayment.app.utilityaccount.UtilityAccountId
import ai.friday.billpayment.app.utilityaccount.UtilityConnectionMethod
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

const val UTILITY_ACCOUNT_REGISTER = "UTILITY_ACCOUNT_REGISTER"

@FridayMePoupe
class UtilityAccountDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<UtilityAccountEntity>(cli, UtilityAccountEntity::class.java)

@FridayMePoupe
class UtilityAccountDbRepository(
    private val client: UtilityAccountDynamoDAO,
) : UtilityAccountRepository {
    override fun save(register: UtilityAccount) {
        val objectMapper = getObjectMapper()
        val entity = UtilityAccountEntity().apply {
            primaryKey = register.id.value
            scanKey = register.walletId.value
            walletId = register.walletId.value
            accountEmail = register.accountEmail
            attemptsToConnect = register.attempts
            scanFailureCount = register.scanFailureCount
            invalidCredentialsCount = register.invalidCredentialsCount
            createdAt = register.createdAt.format(DateTimeFormatter.ISO_DATE_TIME)
            updatedAt = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
            notificatedAt =
                if (register.notificatedAt != null) register.notificatedAt.format(DateTimeFormatter.ISO_DATE_TIME) else null
            status = register.status
            statusMessage = register.statusMessage
            utility = register.utility
            index1HashKey = "${register.walletId.value}#UA"
            index1RangeKey = register.status
            index2HashKey = "${register.status.name}#UA"
            index2RangeKey = register.createdAt.format(dateFormat)
            index3HashKey = register.utility.name
            index3RangeKey = register.connectionMethod.name
            lastBillIdFound = register.lastBillIdFound?.value
            lastBillFound = register.lastBillFound?.format(dateFormat)
            lastDueDateFound = register.lastDueDateFound?.format(dateFormat)
            lastScannedAt = register.lastScannedAt?.format(dateTimeFormat)
            lastSuccessfulScan = register.lastSuccessfulScan?.format(dateFormat)
            connectionDetails = parseObjectFrom<Map<String, String>>(objectMapper.writeValueAsString(register.connectionDetails))
            errorFiles = register.errorFiles?.let { objectMapper.writeValueAsString(it) }
            addedBy = register.addedBy.value
        }

        return client.save(entity)
    }

    override fun find(connectUtilityId: UtilityAccountId, status: UtilityAccountConnectionStatus): UtilityAccount? {
        return client.findByPrimaryKey(connectUtilityId.value, status.name)?.toConnectUtilityRegisterTO()
    }

    override fun find(connectUtilityId: UtilityAccountId): UtilityAccount? {
        return client.findByPartitionKey(connectUtilityId.value).firstOrNull()?.toConnectUtilityRegisterTO()
    }

    override fun findAll(walletId: WalletId, status: UtilityAccountConnectionStatus): List<UtilityAccount> {
        return client.findBeginsWithOnIndex(GlobalSecondaryIndexNames.GSIndex1, "${walletId.value}#UA", status.name)
            .map { it.toConnectUtilityRegisterTO() }
    }

    override fun findAllManual(): List<UtilityAccount> {
        return findByIndex(UtilityAccountConnectionStatus.CONNECTION_ERROR) +
            findByIndex(UtilityAccountConnectionStatus.DISCONNECTION_ERROR) +
            findAllManualPending()
    }

    private fun findAllManualPending(): List<UtilityAccount> {
        return (
            client.findByPartitionKeyOnIndex(GlobalSecondaryIndexNames.GSIndex2, "${UtilityAccountConnectionStatus.PENDING.name}#UA") +
                client.findByPartitionKeyOnIndex(GlobalSecondaryIndexNames.GSIndex2, "${UtilityAccountConnectionStatus.PENDING_DISCONNECTION.name}#UA")
            )
            .filter { it.index3RangeKey == UtilityConnectionMethod.MANUAL.name }
            .map { it.toConnectUtilityRegisterTO() }
    }

    override fun findByIndex(walletId: WalletId): List<UtilityAccount> {
        return client.findByPartitionKeyOnIndex(GlobalSecondaryIndexNames.GSIndex1, "${walletId.value}#UA")
            .map { it.toConnectUtilityRegisterTO() }
    }

    override fun findByIndex(status: UtilityAccountConnectionStatus): List<UtilityAccount> {
        return client.findByPartitionKeyOnIndex(GlobalSecondaryIndexNames.GSIndex2, "${status.name}#UA")
            .map { it.toConnectUtilityRegisterTO() }
    }

    override fun findByIndex(utility: Utility): List<UtilityAccount> {
        return client.findByPartitionKeyOnIndex(GlobalSecondaryIndexNames.GSIndex3, "${utility.name}")
            .map { it.toConnectUtilityRegisterTO() }
    }

    override fun findById(utilityAccountId: UtilityAccountId, walletId: WalletId): UtilityAccount? {
        return client.findByPrimaryKey(utilityAccountId.value, walletId.value)?.toConnectUtilityRegisterTO()
    }

    private fun UtilityAccountEntity.toConnectUtilityRegisterTO() = UtilityAccount(
        id = UtilityAccountId(this.primaryKey),
        status = this.status,
        statusMessage = this.statusMessage,
        walletId = WalletId(this.scanKey),
        accountEmail = this.accountEmail,
        attempts = this.attemptsToConnect,
        scanFailureCount = this.scanFailureCount ?: 0,
        invalidCredentialsCount = this.invalidCredentialsCount ?: 0,
        utility = this.utility,
        updatedAt = ZonedDateTime.parse(this.updatedAt, DateTimeFormatter.ISO_DATE_TIME),
        createdAt = ZonedDateTime.parse(this.createdAt, DateTimeFormatter.ISO_DATE_TIME),
        notificatedAt = if (this.notificatedAt != null) {
            ZonedDateTime.parse(
                this.notificatedAt,
                DateTimeFormatter.ISO_DATE_TIME,
            )
        } else {
            null
        },
        lastBillIdFound = this.lastBillIdFound?.let { BillId(it) },
        lastDueDateFound = this.lastDueDateFound?.let { LocalDate.parse(it, dateFormat) },
        lastBillFound = this.lastBillFound?.let { LocalDate.parse(it, dateFormat) },
        lastScannedAt = this.lastScannedAt?.let { ZonedDateTime.parse(it, dateTimeFormat) },
        lastSuccessfulScan = this.lastSuccessfulScan?.let { LocalDate.parse(it, dateFormat) },
        connectionMethod = this.index3RangeKey?.let { UtilityConnectionMethod.valueOf(it) } ?: UtilityConnectionMethod.SCRAPING,
        connectionDetails = UtilityAccountConnectionDetails.create(this.utility, this.connectionDetails.ifEmpty { mapOf("login" to (this.login ?: ""), "password" to (this.password ?: "")) }),
        errorFiles = this.errorFiles?.let { parseObjectFrom<UtilityAccountErrorFiles>(it) },
        addedBy = AccountId(this.addedBy ?: this.scanKey),
    )
}

@DynamoDbBean
class UtilityAccountEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String // UA-189239-********-91293-9123

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String // WALLET_ID

    @get:DynamoDbAttribute(value = "AttemptsToConnect")
    var attemptsToConnect: Int = 0

    @get:DynamoDbAttribute(value = "ScanFailureCount")
    var scanFailureCount: Int? = 0

    @get:DynamoDbAttribute(value = "InvalidCredentialsCount")
    var invalidCredentialsCount: Int? = 0

    @get:DynamoDbAttribute(value = "RegisterType")
    var registerType: String = UTILITY_ACCOUNT_REGISTER

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute(value = "AccountEmail")
    lateinit var accountEmail: String

    @get:DynamoDbAttribute(value = "Login")
    var login: String? = null

    @get:DynamoDbAttribute(value = "Password")
    var password: String? = null

    @get:DynamoDbAttribute(value = "UpdatedAt")
    lateinit var updatedAt: String

    @get:DynamoDbAttribute(value = "NotificatedAt")
    var notificatedAt: String? = null

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: UtilityAccountConnectionStatus

    @get:DynamoDbAttribute(value = "StatusMessage")
    var statusMessage: String? = null

    @get:DynamoDbAttribute(value = "Utility")
    lateinit var utility: Utility

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var index1HashKey: String // ACCOUNT_ID#UA

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var index1RangeKey: UtilityAccountConnectionStatus // STATUS

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    lateinit var index2HashKey: String // STATUS#UA

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    lateinit var index2RangeKey: String // CreatedAt (YYYY-MM-DD)

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_3])
    @get:DynamoDbAttribute(value = "GSIndex3PrimaryKey")
    var index3HashKey: String? = null // UTILITY

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_3])
    @get:DynamoDbAttribute(value = "GSIndex3ScanKey")
    var index3RangeKey: String? = null // METHOD

    @get:DynamoDbAttribute(value = "LastBillIdFound")
    var lastBillIdFound: String? = null

    @get:DynamoDbAttribute(value = "LastDueDateFound")
    var lastDueDateFound: String? = null

    @get:DynamoDbAttribute(value = "LastBillFound")
    var lastBillFound: String? = null

    @get:DynamoDbAttribute(value = "LastScannedAt")
    var lastScannedAt: String? = null

    @get:DynamoDbAttribute(value = "LastSuccessfulScan")
    var lastSuccessfulScan: String? = null

    @get:DynamoDbAttribute(value = "ConnectionDetails")
    var connectionDetails: Map<String, String> = emptyMap()

    @get:DynamoDbAttribute(value = "ErrorFiles")
    var errorFiles: String? = null

    @get:DynamoDbAttribute(value = "WalletId")
    var walletId: String? = null

    @get:DynamoDbAttribute(value = "AddedBy")
    var addedBy: String? = null
}