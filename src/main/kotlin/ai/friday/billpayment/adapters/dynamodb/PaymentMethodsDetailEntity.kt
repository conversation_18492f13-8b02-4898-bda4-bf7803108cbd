package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.bill.BillEventDependency
import ai.friday.billpayment.app.payment.MultiplePaymentMethodsDetail
import ai.friday.billpayment.app.payment.PaymentMethodsDetail
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithExternalPayment
import ai.friday.billpayment.app.payment.SinglePaymentMethodsDetail
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY)
@JsonSubTypes(
    // houve um refactor e as classes mudaram de nome. Esse mapeamento é para o parse com o nome antigo continuar funcionando
    JsonSubTypes.Type(
        value = PaymentMethodsDetailCreditCardEntity::class,
        name = "BillPaymentScheduledCreditCardInfo",
    ),
    JsonSubTypes.Type(
        value = PaymentMethodsDetailBalanceEntity::class,
        name = "BillPaymentScheduledBalanceInfo",
    ),
    JsonSubTypes.Type(
        value = PaymentMethodsDetailExternalEntity::class,
        name = "BillPaymentScheduledScheduledExternalPayment",
    ),
    JsonSubTypes.Type(
        value = PaymentMethodsDetailMultipleEntity::class,
        name = "BillPaymentScheduledMultiple",
    ),
)
@BillEventDependency
sealed interface PaymentMethodsDetailEntity {
    fun toPaymentMethodsDetail(): PaymentMethodsDetail
}

sealed interface SinglePaymentMethodsDetailEntity : PaymentMethodsDetailEntity {
    override fun toPaymentMethodsDetail(): SinglePaymentMethodsDetail
}

@JsonTypeName("PaymentMethodsDetailCreditCard")
data class PaymentMethodsDetailCreditCardEntity(
    val paymentMethodId: String,
    val netAmount: Long,
    val feeAmount: Long,
    val installments: Int,
    val calculationId: String?,
    val fee: Double = 4.0, // Valor da taxa quando ainda não era persistida no banco
) : SinglePaymentMethodsDetailEntity {
    val totalAmount = netAmount + feeAmount
    override fun toPaymentMethodsDetail() = PaymentMethodsDetailWithCreditCard(
        paymentMethodId = AccountPaymentMethodId(this.paymentMethodId),
        netAmount = this.netAmount,
        feeAmount = this.feeAmount,
        installments = this.installments,
        calculationId = this.calculationId,
        fee = this.fee,
    )
}

@JsonTypeName("PaymentMethodsDetailBalance")
data class PaymentMethodsDetailBalanceEntity(
    val amount: Long,
    val paymentMethodId: String,
    val calculationId: String?,
) : SinglePaymentMethodsDetailEntity {
    override fun toPaymentMethodsDetail() =
        PaymentMethodsDetailWithBalance(
            amount = this.amount,
            paymentMethodId = AccountPaymentMethodId(this.paymentMethodId),
            calculationId = calculationId,
        )
}

@JsonTypeName("PaymentMethodsDetailExternal")
data class PaymentMethodsDetailExternalEntity(
    val providerName: String,
) : SinglePaymentMethodsDetailEntity {
    override fun toPaymentMethodsDetail() =
        PaymentMethodsDetailWithExternalPayment(providerName = AccountProviderName.valueOf(this.providerName))
}

@JsonTypeName("PaymentMethodsDetailMultiple")
data class PaymentMethodsDetailMultipleEntity(
    val methods: List<SinglePaymentMethodsDetailEntity>,
) : PaymentMethodsDetailEntity {
    override fun toPaymentMethodsDetail() =
        MultiplePaymentMethodsDetail(methods = methods.map { it.toPaymentMethodsDetail() })
}

fun PaymentMethodsDetail.toPaymentMethodsDetailEntity(): PaymentMethodsDetailEntity {
    return when (this) {
        is SinglePaymentMethodsDetail -> this.toPaymentMethodsDetailEntitySingle()

        is MultiplePaymentMethodsDetail -> PaymentMethodsDetailMultipleEntity(
            methods = this.methods.map {
                it.toPaymentMethodsDetailEntitySingle()
            },
        )
    }
}

fun SinglePaymentMethodsDetail.toPaymentMethodsDetailEntitySingle(): SinglePaymentMethodsDetailEntity {
    return when (this) {
        is PaymentMethodsDetailWithBalance -> PaymentMethodsDetailBalanceEntity(
            amount = this.amount,
            paymentMethodId = this.paymentMethodId.value,
            calculationId = this.calculationId,
        )

        is PaymentMethodsDetailWithCreditCard -> PaymentMethodsDetailCreditCardEntity(
            netAmount = this.netAmount,
            paymentMethodId = this.paymentMethodId.value,
            feeAmount = this.feeAmount,
            installments = this.installments,
            calculationId = this.calculationId,
        )

        is PaymentMethodsDetailWithExternalPayment -> PaymentMethodsDetailExternalEntity(
            providerName = this.providerName.name,
        )
    }
}