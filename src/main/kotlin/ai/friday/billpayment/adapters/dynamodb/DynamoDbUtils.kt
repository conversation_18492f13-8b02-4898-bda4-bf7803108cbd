package ai.friday.billpayment.adapters.dynamodb

import reactor.core.publisher.Flux
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable
import software.amazon.awssdk.enhanced.dynamodb.Expression
import software.amazon.awssdk.enhanced.dynamodb.Key
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest
import software.amazon.awssdk.services.dynamodb.model.AttributeValue

enum class GlobalSecondaryIndexNames {
    GSIndex1, GSIndex2, GSIndex3, GSIndex4
}

fun <T> DynamoDbTable<T>.getItem(partitionKey: String, sortKey: String): T? =
    this.item(partitionKey, sortKey)

fun <T> DynamoDbTable<T>.getItemsByPartitionKey(
    partitionKey: String,
): List<T> =
    this.query(
        QueryEnhancedRequest.builder()
            .queryConditional(QueryConditional.keyEqualTo(keyByPartitionKey(partitionKey)))
            .build(),
    )
        .flatMap { it.items() }

fun <T> DynamoDbTable<T>.queryTableOnHashKeyAndRangeKeyBeginsWith(
    partitionKey: String?,
    scanKey: String?,
    scanIndexForward: Boolean = true,
): List<T> {
    return this.query(
        QueryEnhancedRequest.builder()
            .scanIndexForward(scanIndexForward)
            .queryConditional(
                QueryConditional.sortBeginsWith {
                    it.partitionValue(partitionKey).sortValue(scanKey)
                },
            ).build(),
    ).flatMap { it.items() }
}

fun <T> DynamoDbTable<T>.queryTableOnHashKeyAndRangeKeyBetween(
    partitionKey: String,
    sortKeyFrom: String,
    sortKeyTo: String,
): List<T> = this.query(
    QueryEnhancedRequest.builder()
        .queryConditional(
            QueryConditional.sortBetween(
                keyByPartitionKeyAndSortKey(partitionKey, sortKeyFrom),
                keyByPartitionKeyAndSortKey(partitionKey, sortKeyTo),
            ),
        ).build(),
).flatMap { it.items() }

fun <T> DynamoDbTable<T>.queryTableOnHashKeyAndRangeKeyBetweenByIndex(
    index: GlobalSecondaryIndexNames,
    partitionKey: String,
    sortKeyFrom: String,
    sortKeyTo: String,
): List<T> = this.index(index.name).query(
    QueryEnhancedRequest.builder()
        .queryConditional(
            QueryConditional.sortBetween(
                keyByPartitionKeyAndSortKey(partitionKey, sortKeyFrom),
                keyByPartitionKeyAndSortKey(partitionKey, sortKeyTo),
            ),
        ).build(),
).flatMap { it.items() }

fun <T> DynamoDbTable<T>.queryTableOnHashKeyAndRangeKeGreaterThanByIndex(
    index: GlobalSecondaryIndexNames,
    partitionKey: String,
    sortKey: String,
): List<T> = this.index(index.name).query(
    QueryEnhancedRequest.builder()
        .queryConditional(
            QueryConditional.sortGreaterThan(
                keyByPartitionKeyAndSortKey(partitionKey, sortKey),
            ),
        ).build(),
).flatMap { it.items() }

fun <T> DynamoDbTable<T>.queryTableOnHashKeyAndRangeKeGreaterThanOrEqualByIndex(
    index: GlobalSecondaryIndexNames,
    partitionKey: String,
    sortKey: String,
): List<T> = this.index(index.name).query(
    QueryEnhancedRequest.builder()
        .queryConditional(
            QueryConditional.sortGreaterThanOrEqualTo(
                keyByPartitionKeyAndSortKey(partitionKey, sortKey),
            ),
        ).build(),
).flatMap { it.items() }

fun <T> DynamoDbTable<T>.queryTableOnHashKeyAndRangeKeLessThanByIndex(
    index: GlobalSecondaryIndexNames,
    partitionKey: String,
    sortKey: String,
): List<T> = this.index(index.name).query(
    QueryEnhancedRequest.builder()
        .queryConditional(
            QueryConditional.sortLessThan(
                keyByPartitionKeyAndSortKey(partitionKey, sortKey),
            ),
        ).build(),
).flatMap { it.items() }

fun <T> DynamoDbTable<T>.queryTableOnHashKeyAndRangeKeLessThanOrEqualByIndex(
    index: GlobalSecondaryIndexNames,
    partitionKey: String,
    sortKey: String,
): List<T> = this.index(index.name).query(
    QueryEnhancedRequest.builder()
        .queryConditional(
            QueryConditional.sortLessThanOrEqualTo(
                keyByPartitionKeyAndSortKey(partitionKey, sortKey),
            ),
        ).build(),
).flatMap { it.items() }

fun <T> DynamoDbTable<T>.getItemsByIndex(
    index: GlobalSecondaryIndexNames,
    partitionKey: String,
    sortKey: String,
): List<T> =
    this.index(index.name)
        .query(partitionKey, sortKey)
        .flatMap {
            it.items()
        }

fun <T> DynamoDbTable<T>.getItemsByIndex(
    index: GlobalSecondaryIndexNames,
    partitionKey: String,
): List<T> =
    this.index(index.name)
        .query(
            QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(keyByPartitionKey(partitionKey)))
                .build(),
        )
        .flatMap { it.items() }

fun <T> DynamoDbTable<T>.getItemsByIndexSortLessThan(
    index: GlobalSecondaryIndexNames,
    partitionKey: String,
    sortKey: String,
): List<T> =
    this.index(index.name)
        .query(
            QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.sortLessThan(keyByPartitionKeyAndSortKey(partitionKey, sortKey)))
                .build(),
        )
        .flatMap { it.items() }

fun <T> DynamoDbTable<T>.getItemsByIndexSortBeginsWith(
    index: GlobalSecondaryIndexNames,
    partitionKey: String,
    sortKey: String,
    expression: String? = null,
    expressionNames: Map<String, String> = emptyMap(),
    expressionValues: Map<String, AttributeValue> = emptyMap(),
): List<T> =
    this.index(index.name)
        .query(
            QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.sortBeginsWith(keyByPartitionKeyAndSortKey(partitionKey, sortKey)))
                .let {
                    if (expression == null) return@let it

                    it.filterExpression(
                        Expression
                            .builder()
                            .expression(expression)
                            .expressionNames(expressionNames)
                            .expressionValues(expressionValues).build(),
                    )
                }
                .build(),
        )
        .flatMap { it.items() }

private fun <T> DynamoDbTable<T>.item(partitionKey: String, sortKey: String) =
    this.getItem(keyByPartitionKeyAndSortKey(partitionKey, sortKey))

private fun <T> DynamoDbIndex<T>.query(partitionKey: String, sortKey: String) =
    this.query(queryByPartitionKeyAndSortKey(partitionKey, sortKey))

fun <T> DynamoDbAsyncTable<T>.getItemsByPartitionKey(
    partitionKey: String,
): Flux<T> {
    val query = this.query(queryByPartitionKey(partitionKey))
    return Flux.from(query).flatMapIterable { page -> page.items() }
}

fun <T> DynamoDbAsyncTable<T>.getItemsByIndex(
    index: GlobalSecondaryIndexNames,
    partitionKey: String,
    sortKey: String,
    conditionalType: ConditionalType,
) = this.getItemsByIndexAndCondition(index, queryConditional(partitionKey, sortKey, conditionalType))

fun <T> DynamoDbAsyncTable<T>.getItemsByIndex(
    index: GlobalSecondaryIndexNames,
    partitionKey: String,
) = this.getItemsByIndexAndCondition(index, QueryConditional.keyEqualTo(keyByPartitionKey(partitionKey)))

fun <T> DynamoDbAsyncTable<T>.getItemsByIndexBetween(
    index: GlobalSecondaryIndexNames,
    partitionKey: String,
    sortKeyFrom: String,
    sortKeyTo: String,
) = this.getItemsByIndexAndCondition(index, queryBetweenSortKeys(partitionKey, sortKeyFrom, sortKeyTo))

fun <T> DynamoDbAsyncTable<T>.getItemsByIndexAndSortKeyLessThanOrEqualTo(
    index: GlobalSecondaryIndexNames,
    partitionKey: String,
    sortKey: String,
) = this.getItemsByIndexAndCondition(
    index,
    QueryConditional.sortLessThanOrEqualTo(
        keyByPartitionKeyAndSortKey(partitionKey, sortKey),
    ),
)

private fun <T> DynamoDbAsyncTable<T>.getItemsByIndexAndCondition(
    index: GlobalSecondaryIndexNames,
    queryConditional: QueryConditional,
): Flux<T> {
    val query = this.index(index.name).query(queryConditional)
    return Flux.from(query).flatMapIterable { page -> page.items() }
}

private fun queryConditional(
    partitionKey: String,
    sortKey: String,
    conditionalType: ConditionalType,
): QueryConditional {
    val fn: (Key) -> QueryConditional = when (conditionalType) {
        ConditionalType.EQUALS -> QueryConditional::keyEqualTo
        ConditionalType.BEGINS_WITH -> QueryConditional::sortBeginsWith
        ConditionalType.LESS_THAN -> QueryConditional::sortLessThan
        ConditionalType.LESS_THAN_OR_EQUAL_TO -> QueryConditional::sortLessThanOrEqualTo
        ConditionalType.GREATER_THAN -> QueryConditional::sortGreaterThan
        ConditionalType.GREATER_THAN_OR_EQUAL_TO -> QueryConditional::sortGreaterThanOrEqualTo
    }
    return fn(keyByPartitionKeyAndSortKey(partitionKey, sortKey))
}

enum class ConditionalType {
    EQUALS, BEGINS_WITH, LESS_THAN, LESS_THAN_OR_EQUAL_TO, GREATER_THAN, GREATER_THAN_OR_EQUAL_TO
}

private fun queryBetweenSortKeys(partitionKey: String, sortKeyFrom: String, sortKeyTo: String) =
    QueryConditional.sortBetween(
        keyByPartitionKeyAndSortKey(partitionKey, sortKeyFrom),
        keyByPartitionKeyAndSortKey(partitionKey, sortKeyTo),
    )

private fun queryByPartitionKey(partitionKey: String) =
    QueryEnhancedRequest.builder()
        .queryConditional(QueryConditional.keyEqualTo(keyByPartitionKey(partitionKey)))
        .build()

private fun queryByPartitionKeyAndSortKey(partitionKey: String, sortKey: String) =
    QueryEnhancedRequest.builder()
        .queryConditional(conditionalByPartitionKeyAndSortKey(partitionKey, sortKey))
        .build()

private fun conditionalByPartitionKeyAndSortKey(partitionKey: String, sortKey: String) =
    QueryConditional.keyEqualTo(keyByPartitionKeyAndSortKey(partitionKey, sortKey))

private fun keyByPartitionKeyAndSortKey(partitionKey: String, sortKey: String) =
    Key.builder()
        .partitionValue(partitionKey)
        .sortValue(sortKey)
        .build()

private fun keyByPartitionKey(partitionKey: String) =
    Key.builder()
        .partitionValue(partitionKey)
        .build()