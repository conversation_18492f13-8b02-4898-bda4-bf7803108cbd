package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.integrations.PixKeyRepository
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyStatus
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import jakarta.inject.Singleton
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

const val PIX_KEY_REGISTER_NAME = "PIX_KEY_REGISTER"

@Singleton
class PixKeyDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<PixKeyEntity>(cli, PixKeyEntity::class.java)

@Singleton
class PixKeyDbRepository(private val client: PixKeyDynamoDAO) : PixKeyRepository {
    override fun create(walletId: WalletId, accountNo: AccountNumber, pixKey: PixKey) {
        val currentPosition =
            client.findBeginsWithOnSortKey(walletId.value, "$PIX_KEY_REGISTER_NAME#${accountNo.fullAccountNumber}")
                .map { it.scanKey.split("#").last().toInt() }.maxOrNull() ?: 0
        val nextPosition = currentPosition + 1

        val entity = PixKeyEntity().apply {
            partitionKey = walletId.value
            scanKey = "$PIX_KEY_REGISTER_NAME#${accountNo.fullAccountNumber}#$nextPosition"
            index1HashKey = pixKey.value
            index1RangeKey = PixKeyStatus.ACTIVE
            value = pixKey.value
            type = pixKey.type
        }

        return client.save(entity)
    }

    override fun list(walletId: WalletId): List<PixKey> {
        return client.findBeginsWithOnSortKey(walletId.value, "$PIX_KEY_REGISTER_NAME#")
            .filter { it.index1RangeKey != PixKeyStatus.INACTIVE }.map { PixKey(it.value, it.type) }
    }

    override fun pixKeyExists(pixKey: PixKey): Boolean {
        return client.findByPartitionKeyAndScanKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = pixKey.value,
            sortKey = PixKeyStatus.ACTIVE.name,
        ).filter {
            it.scanKey.startsWith(PIX_KEY_REGISTER_NAME)
        }.isNotEmpty()
    }

    override fun delete(walletId: WalletId, pixKey: PixKey) {
        val entities = client.findByPartitionKeyOnIndex(GlobalSecondaryIndexNames.GSIndex1, pixKey.value)

        entities.singleOrNull {
            it.partitionKey == walletId.value && it.index1RangeKey == PixKeyStatus.ACTIVE
        }?.apply {
            index1RangeKey = PixKeyStatus.INACTIVE
        }?.let {
            client.save(it)
        }
    }
}

@DynamoDbBean
class PixKeyEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String // Account ID

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String // PIX_KEY_REGISTER#CONTA#1

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var index1HashKey: String // chave pix

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var index1RangeKey: PixKeyStatus

    @get:DynamoDbAttribute(value = "Value")
    lateinit var value: String

    @get:DynamoDbAttribute(value = "Type")
    lateinit var type: PixKeyType

    @get:DynamoDbAttribute(value = "Created")
    val created: String = getZonedDateTime().toString()
}