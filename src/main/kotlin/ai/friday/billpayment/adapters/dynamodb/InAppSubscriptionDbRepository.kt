package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.inappsubscription.InAppSubscription
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionAccessConcessionId
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionProductId
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionReason
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionRepository
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStatus
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStore
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val ScanKey = "IN_APP_SUBSCRIPTION"
private const val Index1PrimaryKey = "IN_APP_SUBSCRIPTION_STATUS"
private const val Index2PrimaryKey = "IN_APP_SUBSCRIPTION_ENDS_AT"

@Singleton
class InAppSubscriptionDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<InAppSubscriptionEntity>(cli, InAppSubscriptionEntity::class.java)

@Singleton
class InAppSubscriptionDbRepository(
    private val client: InAppSubscriptionDynamoDAO,
) : InAppSubscriptionRepository {
    override fun findOrNull(accountId: AccountId): InAppSubscription? =
        client
            .findByPrimaryKey(
                partitionKey = accountId.value,
                sortKey = ScanKey,
            )?.toInAppSubscription()

    override fun find(accountId: AccountId): InAppSubscription = findOrNull(accountId) ?: throw ItemNotFoundException("InAppSubscription not found for account $accountId")

    override fun findByStatus(status: InAppSubscriptionStatus): List<InAppSubscription> =
        client
            .findByPartitionKeyAndScanKeyOnIndex(
                index = GlobalSecondaryIndexNames.GSIndex1,
                partitionKey = Index1PrimaryKey,
                sortKey = status.name,
            ).map { it.toInAppSubscription() }

    // TODO: esse método não inclui hoje (e é o efeito desejado), mas o nome não é claro
    override fun findExpiredAfter(expiredAfter: LocalDate): List<InAppSubscription> {
        val today = getLocalDate()

        return client
            .findByPartitionKeyAndSortKeyBetweenByIndex(
                index = GlobalSecondaryIndexNames.GSIndex2,
                partitionKey = Index2PrimaryKey,
                sortKeyFrom = expiredAfter.format(DateTimeFormatter.ISO_DATE),
                sortKeyTo = today.format(DateTimeFormatter.ISO_DATE),
            )
            .filter { it.status == InAppSubscriptionStatus.EXPIRED }
            .map { it.toInAppSubscription() }
    }

    override fun findEndingBefore(date: LocalDate): List<InAppSubscription> =
        client
            .findByPrimaryKeyLessThanByIndex(
                index = GlobalSecondaryIndexNames.GSIndex2,
                partitionKey = Index2PrimaryKey,
                sortKey = date.format(DateTimeFormatter.ISO_DATE),
            ).map { it.toInAppSubscription() }

    override fun findByEndDate(endsAt: LocalDate): List<InAppSubscription> =
        client
            .findBeginsWithOnIndex(
                index = GlobalSecondaryIndexNames.GSIndex2,
                partitionKey = Index2PrimaryKey,
                sortKey = endsAt.format(DateTimeFormatter.ISO_DATE),
            ).map { it.toInAppSubscription() }

    override fun findAll(): List<InAppSubscription> =
        client
            .findByPartitionKeyOnIndex(
                index = GlobalSecondaryIndexNames.GSIndex1,
                partitionKey = Index1PrimaryKey,
            ).map { it.toInAppSubscription() }

    override fun save(inAppSubscription: InAppSubscription) {
        val entity =
            InAppSubscriptionEntity().apply {
                primaryKey = inAppSubscription.accountId.value
                scanKey = ScanKey
                gSIndex1PrimaryKey = Index1PrimaryKey
                gSIndex1ScanKey = inAppSubscription.status.name
                gSIndex2PrimaryKey = Index2PrimaryKey
                gSIndex2ScanKey = inAppSubscription.endsAt.format(DateTimeFormatter.ISO_DATE_TIME)
                status = inAppSubscription.status
                store = inAppSubscription.store?.name
                endsAt = inAppSubscription.endsAt.format(DateTimeFormatter.ISO_DATE_TIME)
                reason = inAppSubscription.reason.name
                inAppSubscriptionAccessConcessionId = inAppSubscription.inAppSubscriptionAccessConcessionId?.value
                updatedAt = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
                createdAt = inAppSubscription.createdAt.format(DateTimeFormatter.ISO_DATE_TIME)
                autoRenew = inAppSubscription.autoRenew
                price = inAppSubscription.price
                productId = inAppSubscription.productId?.value
                offStoreProductId = inAppSubscription.offStoreProductId
            }
        client.save(entity)
    }

    private fun InAppSubscriptionEntity.toInAppSubscription() =
        InAppSubscription(
            accountId = AccountId(primaryKey),
            status = status,
            endsAt = ZonedDateTime.parse(this.endsAt, DateTimeFormatter.ISO_DATE_TIME),
            store = store?.let { InAppSubscriptionStore.valueOf(it) },
            reason = InAppSubscriptionReason.valueOf(reason),
            inAppSubscriptionAccessConcessionId = inAppSubscriptionAccessConcessionId?.let { InAppSubscriptionAccessConcessionId(it) },
            createdAt = createdAt?.let { ZonedDateTime.parse(it, DateTimeFormatter.ISO_DATE_TIME) } ?: getZonedDateTime(),
            autoRenew = autoRenew,
            price = price,
            productId = productId?.let { InAppSubscriptionProductId(value = it) },
            offStoreProductId = offStoreProductId,

        )
}

@DynamoDbBean
class InAppSubscriptionEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: InAppSubscriptionStatus

    @get:DynamoDbAttribute(value = "EndsAt")
    lateinit var endsAt: String

    @get:DynamoDbAttribute(value = "Store")
    var store: String? = null

    @get:DynamoDbAttribute(value = "Reason")
    lateinit var reason: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PrimaryKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1ScanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    lateinit var gSIndex2PrimaryKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    lateinit var gSIndex2ScanKey: String

    @get:DynamoDbAttribute(value = "InAppSubscriptionaccessConcessionId")
    var inAppSubscriptionAccessConcessionId: String? = null

    @get:DynamoDbAttribute(value = "CreatedAt")
    var createdAt: String? = null

    @get:DynamoDbAttribute(value = "UpdatedAt")
    var updatedAt: String? = null

    @get:DynamoDbAttribute(value = "AutoRenew")
    var autoRenew: Boolean? = null

    @get:DynamoDbAttribute(value = "Price")
    var price: Long = 0

    @get:DynamoDbAttribute(value = "ProductId")
    var productId: String? = null

    @get:DynamoDbAttribute(value = "OffStoreProductId")
    var offStoreProductId: String? = null
}