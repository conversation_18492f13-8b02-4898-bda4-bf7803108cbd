package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.Acquirer
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.AmountUpdated
import ai.friday.billpayment.app.bill.AutomaticPixData
import ai.friday.billpayment.app.bill.AutomaticPixPayer
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BatchSchedulingId
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillApproved
import ai.friday.billpayment.app.bill.BillCategoryAdded
import ai.friday.billpayment.app.bill.BillCategoryDeleted
import ai.friday.billpayment.app.bill.BillCategorySuggestionAdded
import ai.friday.billpayment.app.bill.BillDenied
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillEventDependency
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillIgnored
import ai.friday.billpayment.app.bill.BillMarkedAsPaid
import ai.friday.billpayment.app.bill.BillMarkedAsPaidCanceled
import ai.friday.billpayment.app.bill.BillMoved
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.BillPaymentScheduleStarted
import ai.friday.billpayment.app.bill.BillPaymentScheduleUpdated
import ai.friday.billpayment.app.bill.BillPaymentScheduled
import ai.friday.billpayment.app.bill.BillPaymentScheduledBalanceInfo
import ai.friday.billpayment.app.bill.BillPaymentScheduledCreditCardInfo
import ai.friday.billpayment.app.bill.BillPaymentScheduledExternalPayment
import ai.friday.billpayment.app.bill.BillPaymentScheduledInfo
import ai.friday.billpayment.app.bill.BillPaymentScheduledWithMultiple
import ai.friday.billpayment.app.bill.BillPermissionUpdated
import ai.friday.billpayment.app.bill.BillReactivated
import ai.friday.billpayment.app.bill.BillRecipientUpdated
import ai.friday.billpayment.app.bill.BillSchedulePostponed
import ai.friday.billpayment.app.bill.BillTag
import ai.friday.billpayment.app.bill.BillTagAdded
import ai.friday.billpayment.app.bill.BillTagDeleted
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.DescriptionUpdated
import ai.friday.billpayment.app.bill.ExternalBillId
import ai.friday.billpayment.app.bill.FichaCompensacaoAdded
import ai.friday.billpayment.app.bill.PaymentFailed
import ai.friday.billpayment.app.bill.PaymentRefunded
import ai.friday.billpayment.app.bill.PaymentRefundedReason
import ai.friday.billpayment.app.bill.PaymentStarted
import ai.friday.billpayment.app.bill.PermissionUpdated
import ai.friday.billpayment.app.bill.PixCopyAndPaste
import ai.friday.billpayment.app.bill.PixQrCodeData
import ai.friday.billpayment.app.bill.PixQrCodeType
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.bill.RecurrenceUpdated
import ai.friday.billpayment.app.bill.RegisterUpdated
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.bill.SchedulePostponedReason
import ai.friday.billpayment.app.bill.UpdatedRegisterData
import ai.friday.billpayment.app.bill.UpdatedScheduleData
import ai.friday.billpayment.app.bill.schedule.LegacyPaymentMethodsDetailParser
import ai.friday.billpayment.app.billcategory.BillCategorySuggestion
import ai.friday.billpayment.app.contact.ContactId
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.DiscountData
import ai.friday.billpayment.app.payment.DiscountType
import ai.friday.billpayment.app.payment.DivergentPayment
import ai.friday.billpayment.app.payment.ErrorSource
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.payment.FineData
import ai.friday.billpayment.app.payment.FineType
import ai.friday.billpayment.app.payment.InterestData
import ai.friday.billpayment.app.payment.InterestType
import ai.friday.billpayment.app.payment.PartialPayment
import ai.friday.billpayment.app.payment.RecipientChain
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.removePrefix
import ai.friday.billpayment.app.pfm.BillCategory
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.security.Fingerprint
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.timeFormat
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.right
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBDocument
import com.amazonaws.services.dynamodbv2.model.ResourceNotFoundException
import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import jakarta.inject.Singleton
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class BillEventDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillEventDynamoDAO<BillEventEntity>(cli, BillEventEntity::class.java)

@Singleton
class UniqueConstraintDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<UniqueConstraintEntity>(cli, UniqueConstraintEntity::class.java)

@Singleton
class BillEventDBRepository(
    private val billEventDAO: BillEventDynamoDAO,
    private val uniqueConstraintDAO: UniqueConstraintDynamoDAO,
    private val featureConfiguration: FeatureConfiguration,
    private val transactionDynamo: TransactionDynamo,
) : BillEventRepository {

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val mapper = getObjectMapper()

    private val uniqueDigitablePrefix = "DIGITABLE#"
    private val uniqueIdNumberPrefix = "ID_NUMBER#"
    private val uniqueExternalIdPrefix = "EXTERNAL_BILL_ID#"

    private fun addBoletoBill(billEvent: BillEvent) {
        logger.info(append("event", billEvent).andAppend("methodName", "BillEventDBRepository#addBoletoBill"), billEvent.javaClass.simpleName)
        val (barcode, dueDate) = when (billEvent) {
            is BillAdded -> Pair(billEvent.barcode!!, billEvent.dueDate)
            is FichaCompensacaoAdded -> Pair(billEvent.barcode, billEvent.dueDate)
            else -> throw IllegalArgumentException("BillEvent should not be of type ${billEvent.javaClass.simpleName} on addBoletoBill")
        }

        val billEventEntity: BillEventEntity = BillEventEntity().apply {
            billId = billEvent.billId.value
            eventType = billEvent.eventType
            created = billEvent.created
            details = mapToEventDetails(billEvent)
            gSIndex1PrimaryKey = barcode.digitable
            gSIndex1ScanKey = billEvent.walletId.value
        }
        val digitableLineEntity = UniqueConstraintEntity().apply {
            primaryKey = barcode.digitable
            scanKey = buildUniqueDigitableScanKey(billEvent.walletId, dueDate)
            billId = billEvent.billId.value
        }

        val idNumberEntity: UniqueConstraintEntity? = if (billEvent is FichaCompensacaoAdded && billEvent.idNumber != null && featureConfiguration.blockDuplicateByIdNumber) {
            UniqueConstraintEntity().apply {
                primaryKey = billEvent.idNumber
                scanKey = buildUniqueIdNumberScanKey(billEvent.walletId, dueDate)
                billId = billEvent.billId.value
            }
        } else {
            null
        }

        val transactions = listOfNotNull(
            uniqueConstraintDAO.transactionWritePut(digitableLineEntity),
            idNumberEntity?.let { uniqueConstraintDAO.transactionWritePut(it) },
            billEventDAO.transactionWritePut(billEventEntity),
        )

        transactionDynamo.transactionWrite(*transactions.toTypedArray())
    }

    private fun mapToEventDetails(billEvent: BillEvent): String {
        return mapper.writeValueAsString(billEvent.toBillEventDetailEntity())
    }

    private fun buildUniqueDigitableScanKey(walletId: WalletId, dueDate: LocalDate) =
        "$uniqueDigitablePrefix${walletId.value}#${dueDate.format(dateFormat)}"

    private fun buildUniqueIdNumberScanKey(walletId: WalletId, dueDate: LocalDate) =
        "$uniqueIdNumberPrefix${walletId.value}#${dueDate.format(dateFormat)}"

    private fun buildExternalUniqueConstraint(billId: BillId, externalId: ExternalBillId) =
        UniqueConstraintEntity().apply {
            this.primaryKey = "$uniqueExternalIdPrefix${externalId.value}"
            this.scanKey = externalId.provider.name
            this.billId = billId.value
        }

    override fun save(billEvent: BillEvent) {
        if ((billEvent is BillAdded && billEvent.billType.isBoleto()) || billEvent is FichaCompensacaoAdded) {
            addBoletoBill(billEvent)
            return
        }

        if (billEvent is BillMoved) {
            if (billEvent.barCode != null) {
                uniqueConstraintDAO.delete(
                    billEvent.barCode.digitable,
                    buildUniqueDigitableScanKey(billEvent.walletId, billEvent.dueDate!!),
                )
            }

            if (billEvent.idNumber != null) {
                try {
                    uniqueConstraintDAO.delete(
                        billEvent.idNumber,
                        buildUniqueIdNumberScanKey(billEvent.walletId, billEvent.dueDate!!),
                    )
                } catch (e: ItemNotFoundException) {
                    // during feature flag turned off some bills with idNumber will not have UniqueConstraint saved
                }
            }
        }

        val entity: BillEventEntity = BillEventEntity().apply {
            billId = billEvent.billId.value
            eventType = billEvent.eventType
            created = billEvent.created
            details = mapToEventDetails(billEvent)
        }

        if (billEvent is BillAdded && billEvent.externalId != null) {
            uniqueConstraintDAO.save(
                buildExternalUniqueConstraint(
                    billId = billEvent.billId,
                    externalId = billEvent.externalId,
                ),
            )
        }

        billEventDAO.save(entity)
        logger.info(append("event", billEvent).andAppend("methodName", "BillEventDBRepository#save"), billEvent.javaClass.simpleName)
    }

    override fun getBillByExternalId(externalId: ExternalBillId): Either<Exception, Bill> {
        return try {
            val entity = uniqueConstraintDAO.findByPrimaryKey(
                "$uniqueExternalIdPrefix${externalId.value}",
                externalId.provider.name,
            ) ?: return Either.Left(ItemNotFoundException("ExternalID not found: ${externalId.provider}#${externalId.value}"))

            getBillById(BillId(entity.billId))
        } catch (e: Exception) {
            logger.error(append("externalId", externalId), "Failed to get bill", e)
            Either.Left(e)
        }
    }

    override fun findLastBill(barCode: BarCode, walletId: WalletId): Either<Exception, Bill> {
        return try {
            val entities = uniqueConstraintDAO.findBeginsWithOnSortKey(
                partitionKey = barCode.digitable,
                sortKey = "$uniqueDigitablePrefix${walletId.value}",
            )
            entities.takeIf { it.isNotEmpty() }
                ?: throw ItemNotFoundException("Barcode not found: ${barCode.digitable}")
            getBillById(BillId(entities.last().billId))
        } catch (e: ResourceNotFoundException) {
            Either.Left(e)
        } catch (e: ItemNotFoundException) {
            Either.Left(e)
        }
    }

    fun findBills(barCode: BarCode): Either<Exception, List<Bill>> {
        return try {
            val entities = uniqueConstraintDAO.findBeginsWithOnSortKey(
                partitionKey = barCode.digitable,
                sortKey = uniqueDigitablePrefix,
            )
            entities.takeIf { it.isNotEmpty() }
                ?: throw ItemNotFoundException("Barcode not found: ${barCode.digitable}")

            entities.mapNotNull {
                getBillById(BillId(it.billId)).getOrElse {
                    null
                }
            }.right()
        } catch (e: ResourceNotFoundException) {
            Either.Left(e)
        } catch (e: ItemNotFoundException) {
            Either.Left(e)
        }
    }

    override fun findLastBill(barCode: BarCode, walletId: WalletId, dueDate: LocalDate): Either<Exception, Bill> {
        return try {
            val entities = uniqueConstraintDAO.findBeginsWithOnSortKey(
                partitionKey = barCode.digitable,
                sortKey = "$uniqueDigitablePrefix${walletId.value}#${dueDate.format(dateFormat)}",
            )
            entities.takeIf { it.isNotEmpty() }
                ?: throw ItemNotFoundException("Barcode not found: ${barCode.digitable}")
            getBillById(BillId(entities.last().billId))
        } catch (e: ResourceNotFoundException) {
            Either.Left(e)
        } catch (e: ItemNotFoundException) {
            Either.Left(e)
        }
    }

    override fun findLastBill(idNumber: String, walletId: WalletId): Either<Exception, Bill> {
        return try {
            val entities = uniqueConstraintDAO.findBeginsWithOnSortKey(
                partitionKey = idNumber,
                sortKey = "$uniqueIdNumberPrefix${walletId.value}",
            )
            entities.takeIf { it.isNotEmpty() }
                ?: throw ItemNotFoundException("IdNumber not found: $idNumber")
            getBillById(BillId(entities.last().billId))
        } catch (e: ResourceNotFoundException) {
            Either.Left(e)
        } catch (e: ItemNotFoundException) {
            Either.Left(e)
        }
    }

    override fun getBillById(billId: BillId): Either<Exception, Bill> {
        return try {
            val entities = billEventDAO.findByPartitionKey(billId.value)
            entities.takeIf { it.isNotEmpty() } ?: return Either.Left(ItemNotFoundException(billId))
            val events = entities.map { entity -> mapToBillEvent(entity) }.toMutableList()
            Either.Right(Bill.build(events))
        } catch (e: Exception) {
            logger.error(append("billId", billId.value), "Failed to get bill", e)
            Either.Left(e)
        }
    }

    @Deprecated(
        "Use getBillById instead, as a bill being accessed only by its own wallet is no longer true",
        replaceWith = ReplaceWith(
            expression = "getBillById",
            imports = ["ai.friday.billpayment.adapters.dynamodb"],
        ),
    )
    override fun getBill(walletId: WalletId, billId: BillId): Either<Exception, Bill> {
        return getBillById(billId)
    }

    override fun listBills(barCode: BarCode, dueDate: LocalDate?): List<Bill> {
        return try {
            uniqueConstraintDAO.findBeginsWithOnSortKey(
                partitionKey = barCode.digitable,
                sortKey = uniqueDigitablePrefix,
            ).filter {
                val parts = it.scanKey.split("#")
                dueDate == null || dueDate.format(dateFormat) == parts[2]
            }.mapNotNull {
                getBillById(BillId(it.billId)).getOrElse { null }
            }
        } catch (e: ResourceNotFoundException) {
            emptyList()
        } catch (e: ItemNotFoundException) {
            emptyList()
        }
    }

    override fun deleteUniqueConstraint(barCode: BarCode, walletId: WalletId, dueDate: LocalDate) {
        uniqueConstraintDAO.delete(barCode.digitable, buildUniqueDigitableScanKey(walletId, dueDate))
    }

    private fun MutableList<BillEvent>.toBill(): Bill {
        return Bill.build(this)
    }

    private fun mapToBillEvent(entity: BillEventEntity): BillEvent {
        return mapper.readerFor(BillEventDetailsEntity::class.java)
            .readValue<BillEventDetailsEntity>(entity.details).toBillEvent()
    }
}

@DynamoDbBean
class BillEventEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var billId: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    var created: Long = 0

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    var gSIndex1PrimaryKey: String? = null

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    var gSIndex1ScanKey: String? = null

    @get:DynamoDbAttribute(value = "EventType")
    lateinit var eventType: BillEventType

    @get:DynamoDbAttribute(value = "EventDetails")
    lateinit var details: String

    override fun toString(): String {
        return "BillEventEntity(billId='$billId', created=$created, gSIndex1PrimaryKey='$gSIndex1PrimaryKey', gSIndex1ScanKey='$gSIndex1ScanKey', eventType=$eventType, details='$details')"
    }
}

@DynamoDbBean
class UniqueConstraintEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbAttribute(value = "BillId")
    lateinit var billId: String
}

fun BillEventDetailsEntity.toBillEvent(): BillEvent {
    return when (this) {
        is FichaCompensacaoAddedEntity -> {
            FichaCompensacaoAdded(
                billId = this.billId,
                brand = this.brand,
                created = this.created,
                walletId = this.walletId,
                description = this.description,
                dueDate = this.dueDate,
                amount = this.amount,
                amountTotal = this.amountTotal,
                discount = this.calculatedDiscount,
                fine = this.calculatedFine,
                interest = this.calculatedInterest,
                barcode = this.barcode,
                recipient = this.recipient,
                recipientChain = this.recipientChain,
                assignor = this.assignor,
                document = this.document,
                payerName = this.payerName,
                payerAlias = this.payerAlias,
                paymentLimitTime = LocalTime.parse(paymentLimitTime, timeFormat),
                lastSettleDate = this.lastSettleDate,
                expirationDate = this.expirationDate,
                amountCalculationModel = this.amountCalculationModel,
                actionSource = this.actionSource,
                effectiveDueDate = this.effectiveDueDate,
                fichaCompensacaoType = this.fichaCompensacaoType,
                idNumber = this.idNumber,
                registrationUpdateNumber = this.registrationUpdateNumber,
                subscriptionFee = this.subscriptionFee ?: false,
                interestData = InterestData(
                    value = this.interestValue,
                    type = this.interestType,
                    date = this.interestDate,
                ),
                fineData = FineData(
                    value = this.fineValue,
                    type = this.fineType,
                    date = this.fineDate,
                ),
                discountData = DiscountData(
                    type = this.discountType,
                    value1 = this.discount1Value,
                    date1 = this.discount1Date,
                    value2 = this.discount2Value,
                    date2 = this.discount2Date,
                    value3 = this.discount3Value,
                    date3 = this.discount3Date,
                ),
                abatement = this.abatement,
                securityValidationResult = this.securityValidationResult,
                divergentPayment = this.divergentPayment,
                partialPayment = this.partialPayment,
            )
        }

        is BillAddedEntity -> {
            BillAdded(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                description = this.description,
                dueDate = this.dueDate,
                amount = this.amount,
                amountTotal = this.amountTotal,
                discount = this.discount,
                fine = this.fine,
                interest = this.interest,
                barcode = this.barcode,
                recipient = this.recipient,
                assignor = this.assignor,
                document = this.document,
                payerName = this.payerName,
                payerAlias = this.payerAlias,
                paymentLimitTime = LocalTime.parse(paymentLimitTime, timeFormat),
                lastSettleDate = this.lastSettleDate,
                expirationDate = this.expirationDate,
                amountCalculationModel = this.amountCalculationModel,
                actionSource = this.actionSource,
                effectiveDueDate = this.effectiveDueDate,
                billType = this.billType,
                contactId = this.contactId,
                recurrenceRule = this.recurrenceRule,
                subscriptionFee = this.subscriptionFee ?: false,
                securityValidationResult = this.securityValidationResult,
                requestedDueDate = this.requestedDueDate,
                externalId = this.externalId,
                categoryId = this.categoryId,
                pixQrCodeData = this.pixQrCodeData?.let { pixQrCodeDataEntity ->
                    PixQrCodeData(
                        pixCopyAndPaste = pixQrCodeDataEntity.pixCopyAndPaste?.let { PixCopyAndPaste(it) },
                        pixId = pixQrCodeDataEntity.pixId,
                        fixedAmount = pixQrCodeDataEntity.fixedAmount,
                        type = pixQrCodeDataEntity.type,
                        info = pixQrCodeDataEntity.info,
                        additionalInfo = pixQrCodeDataEntity.additionalInfo,
                        expiration = pixQrCodeDataEntity.expiration,
                        originalAmount = pixQrCodeDataEntity.originalAmount,
                        automaticPixRecurringDataJson = pixQrCodeDataEntity.automaticPixRecurringData,
                    )
                },
                automaticPixAuthorizationMaximumAmount = this.automaticPixAuthorizationMaximumAmount,
                automaticPixData = this.automaticPixData?.toDomain(),
                goalId = this.goalId,
            )
        }

        is BillIgnoredEntity -> {
            BillIgnored(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                actionSource = this.actionSource,
                removeFromListing = this.removeFromListing,
                removeFromRecurrence = this.removeFromRecurrence,
            )
        }

        is BillMovedEntity -> {
            BillMoved(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                destinationWalletId = this.destinationWalletId,
                barCode = this.barCode,
                dueDate = this.dueDate,
                idNumber = this.idNumber,
                actionSource = this.actionSource,
            )
        }

        is BillPaidEntity -> {
            BillPaid(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                acquirer = this.acquirer,
                acquirerTid = this.acquirerTid,
                transactionId = this.transactionId,
                actionSource = this.actionSource,
                pixKeyDetails = this.pixKeyDetails,
                syncReceipt = this.syncReceipt,
            )
        }

        is BillPaymentScheduleCanceledEntity -> {
            BillPaymentScheduleCanceled(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                reason = this.reason,
                actionSource = this.actionSource,
                batchSchedulingId = this.batchSchedulingId,
            )
        }

        is BillPaymentScheduleStartedEntity -> {
            BillPaymentScheduleStarted(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                actionSource = this.actionSource,
                paymentWalletId = this.paymentWalletId ?: this.walletId,
            )
        }

        is BillPaymentScheduledEntity -> {
            BillPaymentScheduled(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                actionSource = this.actionSource,
                scheduledDate = LocalDate.parse(this.scheduledDate, dateFormat),
                amount = this.amount,
                paymentLimitTime = this.paymentLimitTime?.let { LocalTime.parse(it, timeFormat) },
                infoData = (this.infoData?.toPaymentMethodsDetail() ?: LegacyPaymentMethodsDetailParser.parse(amount = this.amount, paymentMethodId = this.paymentMethodId?.let { AccountPaymentMethodId(it) })).toBillPaymentScheduledInfo(),
                fingerprint = this.fingerprint,
                batchSchedulingId = this.batchSchedulingId,
                paymentWalletId = this.paymentWalletId ?: this.walletId,
            )
        }

        is BillPaymentScheduleUpdatedEntity -> {
            BillPaymentScheduleUpdated(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                actionSource = this.actionSource,
                updatedScheduleData = this.updatedScheduleData,
            )
        }

        is BillPermissionUpdatedEntity -> {
            BillPermissionUpdated(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                actionSource = this.actionSource,
                permissionUpdated = this.permissionUpdated,
            )
        }

        is BillReactivatedEntity -> {
            BillReactivated(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                actionSource = this.actionSource,
            )
        }

        is BillRecipientUpdatedEntity -> {
            BillRecipientUpdated(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                actionSource = this.actionSource,
                recipient = this.recipient,
            )
        }

        is BillSchedulePostponedEntity -> {
            BillSchedulePostponed(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                actionSource = this.actionSource,
                reason = this.reason,
            )
        }

        is DescriptionUpdatedEntity -> {
            DescriptionUpdated(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                description = this.description,
                actionSource = this.actionSource,
            )
        }

        is PaymentFailedEntity -> {
            PaymentFailed(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                retryable = this.retryable,
                errorSource = this.errorSource,
                errorDescription = this.errorDescription,
                transactionId = this.transactionId,
                actionSource = this.actionSource,
            )
        }

        is PaymentRefundedEntity -> {
            PaymentRefunded(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                gateway = this.gateway,
                reason = this.reason,
                transactionId = this.transactionId,
                actionSource = this.actionSource,
            )
        }

        is PaymentStartedEntity -> {
            PaymentStarted(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                transactionId = this.transactionId,
                actionSource = this.actionSource,
                correlationId = this.correlationId ?: this.transactionId.removePrefix(),
            )
        }

        is RecurrenceUpdatedEntity -> {
            RecurrenceUpdated(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                actionSource = this.actionSource,
                recurrenceRule = this.recurrenceRule,
            )
        }

        is RegisterUpdatedEntity -> {
            RegisterUpdated(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                updatedRegisterData = this.updatedRegisterData,
                actionSource = this.actionSource,
            )
        }

        is BillMarkedAsPaidEntity -> {
            BillMarkedAsPaid(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                actionSource = this.actionSource,
                amountPaid = this.amountPaid,
            )
        }

        is BillMarkedAsPaidCanceledEntity -> {
            BillMarkedAsPaidCanceled(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                actionSource = this.actionSource,
            )
        }

        is AmountUpdatedEntity -> AmountUpdated(
            billId = this.billId,
            created = this.created,
            walletId = this.walletId,
            actionSource = this.actionSource,
            amount = this.amount,
        )

        is BillApprovedEntity -> BillApproved(
            billId = this.billId,
            created = this.created,
            walletId = this.walletId,
            actionSource = this.actionSource,
        )

        is BillDeniedEntity -> BillDenied(
            billId = this.billId,
            created = this.created,
            walletId = this.walletId,
            actionSource = this.actionSource,
        )

        is BillTagAddedEntity -> BillTagAdded(
            billId = this.billId,
            created = this.created,
            walletId = this.walletId,
            actionSource = this.actionSource,
            tag = this.tag,
        )

        is BillTagDeletedEntity -> BillTagDeleted(
            billId = this.billId,
            created = this.created,
            walletId = this.walletId,
            actionSource = this.actionSource,
            tag = this.tag,
        )

        is BillCategoryAddedEntity -> BillCategoryAdded(
            billId = this.billId,
            created = this.created,
            walletId = this.walletId,
            actionSource = this.actionSource,
            categoryId = if (this.category != null) PFMCategoryId(this.category.billCategoryId) else this.categoryId,
            category = this.category?.toBillCategory(),
        )

        is BillCategoryDeletedEntity -> BillCategoryDeleted(
            billId = this.billId,
            created = this.created,
            walletId = this.walletId,
            actionSource = this.actionSource,
        )

        is BillCategorySuggestionAddedEntity -> BillCategorySuggestionAdded(
            billId = this.billId,
            created = this.created,
            walletId = this.walletId,
            actionSource = this.actionSource,
            categories = categories,
        )
    }
}

private fun BillCategoryDocument.toBillCategory() = BillCategory(
    categoryId = PFMCategoryId(value = this.billCategoryId),
    name = this.name,
    icon = this.icon,
    default = this.default,
)

private fun BillCategory.toBillCategoryDbDocument() = BillCategoryDocument(
    billCategoryId = this.categoryId.value,
    name = this.name,
    icon = this.icon,
    default = this.default,
)

fun BillEvent.toBillEventDetailEntity(): BillEventDetailsEntity {
    return when (this) {
        is BillAdded -> {
            BillAddedEntity(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                description = this.description,
                dueDate = this.dueDate,
                amount = this.amount,
                billType = this.billType,
                amountTotal = this.amountTotal,
                discount = this.discount,
                fine = this.fine,
                interest = this.interest,
                barcode = this.barcode,
                recipient = this.recipient,
                contactId = this.contactId,
                assignor = this.assignor,
                document = this.document,
                payerName = this.payerName,
                payerAlias = this.payerAlias,
                paymentLimitTime = this.paymentLimitTime.format(timeFormat),
                lastSettleDate = this.lastSettleDate,
                expirationDate = this.expirationDate,
                amountCalculationModel = this.amountCalculationModel,
                actionSource = this.actionSource,
                recurrenceRule = this.recurrenceRule,
                effectiveDueDate = this.effectiveDueDate,
                subscriptionFee = this.subscriptionFee,
                externalId = this.externalId,
                securityValidationResult = this.securityValidationResult,
                requestedDueDate = this.requestedDueDate,
                categoryId = this.categoryId,
                pixQrCodeData = this.pixQrCodeData?.let { pixQrCodeData ->
                    PixQrCodeDataEntity(
                        pixCopyAndPaste = pixQrCodeData.pixCopyAndPaste?.value,
                        pixId = pixQrCodeData.pixId,
                        fixedAmount = pixQrCodeData.fixedAmount,
                        type = pixQrCodeData.type,
                        info = pixQrCodeData.info,
                        additionalInfo = pixQrCodeData.additionalInfo,
                        expiration = pixQrCodeData.expiration,
                        originalAmount = pixQrCodeData.originalAmount,
                        automaticPixRecurringData = pixQrCodeData.automaticPixRecurringDataJson,
                    )
                },
                automaticPixAuthorizationMaximumAmount = this.automaticPixAuthorizationMaximumAmount,
                automaticPixData = this.automaticPixData?.toEntity(),
                goalId = this.goalId,
            )
        }

        is BillPaid -> {
            BillPaidEntity(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                acquirer = this.acquirer,
                acquirerTid = this.acquirerTid,
                transactionId = this.transactionId,
                actionSource = this.actionSource,
                pixKeyDetails = this.pixKeyDetails,
                syncReceipt = this.syncReceipt,
            )
        }

        is BillIgnored -> {
            BillIgnoredEntity(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                actionSource = this.actionSource,
                removeFromListing = this.removeFromListing,
                removeFromRecurrence = this.removeFromRecurrence,
            )
        }

        is BillReactivated -> {
            BillReactivatedEntity(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                actionSource = this.actionSource,
            )
        }

        is RegisterUpdated -> {
            RegisterUpdatedEntity(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                updatedRegisterData = this.updatedRegisterData,
                actionSource = this.actionSource,
            )
        }

        is PaymentStarted -> {
            PaymentStartedEntity(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                transactionId = this.transactionId,
                actionSource = this.actionSource,
                correlationId = this.correlationId,
            )
        }

        is PaymentFailed -> {
            PaymentFailedEntity(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                retryable = this.retryable,
                errorSource = this.errorSource,
                errorDescription = this.errorDescription,
                transactionId = this.transactionId,
                actionSource = this.actionSource,
            )
        }

        is PaymentRefunded -> {
            PaymentRefundedEntity(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                gateway = this.gateway,
                reason = this.reason,
                transactionId = this.transactionId,
                actionSource = this.actionSource,
            )
        }

        is BillPaymentScheduled -> {
            BillPaymentScheduledEntity(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                actionSource = this.actionSource,
                scheduledDate = this.scheduledDate.format(dateFormat),
                amount = this.amount,
                paymentLimitTime = this.paymentLimitTime?.format(timeFormat),
                paymentMethodId = this.infoData.retrievePaymentMethodIds()
                    .firstOrNull()?.value, // TODO - vai gerar null. Remover em um próximo deploy
                infoData = this.infoData.toBillEventDetailEntity(),
                fingerprint = this.fingerprint,
                batchSchedulingId = this.batchSchedulingId,
                paymentWalletId = this.paymentWalletId,
            )
        }

        is BillPaymentScheduleStarted -> {
            BillPaymentScheduleStartedEntity(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                paymentWalletId = this.paymentWalletId,
                actionSource = this.actionSource,
            )
        }

        is BillPaymentScheduleCanceled -> {
            BillPaymentScheduleCanceledEntity(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                reason = this.reason,
                actionSource = this.actionSource,
                batchSchedulingId = this.batchSchedulingId,
            )
        }

        is BillPaymentScheduleUpdated -> {
            BillPaymentScheduleUpdatedEntity(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                actionSource = this.actionSource,
                updatedScheduleData = this.updatedScheduleData,
            )
        }

        is DescriptionUpdated -> {
            DescriptionUpdatedEntity(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,

                description = this.description,
                actionSource = this.actionSource,
            )
        }

        is BillRecipientUpdated -> {
            BillRecipientUpdatedEntity(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                actionSource = this.actionSource,
                recipient = this.recipient,
            )
        }

        is BillSchedulePostponed -> {
            BillSchedulePostponedEntity(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                actionSource = this.actionSource,
                reason = this.reason,
            )
        }

        is FichaCompensacaoAdded -> {
            FichaCompensacaoAddedEntity(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                brand = this.brand,
                description = this.description,
                dueDate = this.dueDate,
                amount = this.amount,
                amountTotal = this.amountTotal,
                calculatedDiscount = this.discount,
                calculatedFine = this.fine,
                calculatedInterest = this.interest,
                barcode = this.barcode,
                recipient = this.recipient,
                recipientChain = this.recipientChain,
                assignor = this.assignor,
                document = this.document,
                payerName = this.payerName,
                payerAlias = this.payerAlias,
                paymentLimitTime = this.paymentLimitTime.format(timeFormat),
                lastSettleDate = this.lastSettleDate,
                expirationDate = this.expirationDate,
                amountCalculationModel = this.amountCalculationModel,
                actionSource = this.actionSource,
                effectiveDueDate = this.effectiveDueDate,
                fichaCompensacaoType = this.fichaCompensacaoType,
                idNumber = this.idNumber,
                registrationUpdateNumber = this.registrationUpdateNumber,
                subscriptionFee = this.subscriptionFee,
                interestValue = this.interestData?.value,
                interestType = this.interestData?.type,
                interestDate = this.interestData?.date,
                fineValue = this.fineData?.value,
                fineType = this.fineData?.type,
                fineDate = this.fineData?.date,
                discountType = this.discountData?.type,
                discount1Value = this.discountData?.value1,
                discount1Date = this.discountData?.date1,
                discount2Value = this.discountData?.value2,
                discount2Date = this.discountData?.date2,
                discount3Value = this.discountData?.value3,
                discount3Date = this.discountData?.date3,
                abatement = this.abatement,
                securityValidationResult = this.securityValidationResult,
                divergentPayment = this.divergentPayment,
                partialPayment = this.partialPayment,
            )
        }

        is BillMoved -> {
            BillMovedEntity(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                destinationWalletId = this.destinationWalletId,
                barCode = this.barCode,
                dueDate = this.dueDate,
                idNumber = this.idNumber,
                actionSource = this.actionSource,
            )
        }

        is BillPermissionUpdated -> {
            BillPermissionUpdatedEntity(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                actionSource = this.actionSource,
                permissionUpdated = this.permissionUpdated,
            )
        }

        is RecurrenceUpdated -> {
            RecurrenceUpdatedEntity(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                actionSource = this.actionSource,
                recurrenceRule = this.recurrenceRule,
            )
        }

        is BillMarkedAsPaid -> {
            BillMarkedAsPaidEntity(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                actionSource = this.actionSource,
                amountPaid = this.amountPaid,
            )
        }

        is BillMarkedAsPaidCanceled -> {
            BillMarkedAsPaidCanceledEntity(
                billId = this.billId,
                created = this.created,
                walletId = this.walletId,
                actionSource = this.actionSource,
            )
        }

        is AmountUpdated -> AmountUpdatedEntity(
            billId = this.billId,
            created = this.created,
            walletId = this.walletId,
            actionSource = this.actionSource,
            amount = this.amount,
        )

        is BillApproved -> BillApprovedEntity(
            billId = this.billId,
            created = this.created,
            walletId = this.walletId,
            actionSource = this.actionSource,
        )

        is BillDenied -> BillDeniedEntity(
            billId = this.billId,
            created = this.created,
            walletId = this.walletId,
            actionSource = this.actionSource,
        )

        is BillTagAdded -> BillTagAddedEntity(
            billId = this.billId,
            created = this.created,
            walletId = this.walletId,
            actionSource = this.actionSource,
            tag = this.tag,
        )

        is BillTagDeleted -> BillTagDeletedEntity(
            billId = this.billId,
            created = this.created,
            walletId = this.walletId,
            actionSource = this.actionSource,
            tag = this.tag,
        )

        is BillCategoryAdded -> BillCategoryAddedEntity(
            billId = this.billId,
            created = this.created,
            walletId = this.walletId,
            actionSource = this.actionSource,
            categoryId = this.categoryId, // TODO: não deve ser nulavel depois do script
            category = this.category?.toBillCategoryDbDocument(),
        )

        is BillCategoryDeleted -> BillCategoryDeletedEntity(
            billId = this.billId,
            created = this.created,
            walletId = this.walletId,
            actionSource = this.actionSource,
        )

        is BillCategorySuggestionAdded -> BillCategorySuggestionAddedEntity(
            billId = this.billId,
            created = this.created,
            walletId = this.walletId,
            actionSource = this.actionSource,
            categories = this.categories,
        )
    }
}

fun AutomaticPixData.toEntity(): AutomaticPixDataEntity {
    val domain = this
    return AutomaticPixDataEntity().apply {
        payerDocument = domain.payer?.document
        payerName = domain.payer?.name
        description = domain.description
        additionalInformation = domain.additionalInformation
        contractNumber = domain.contractNumber
    }
}

fun AutomaticPixDataEntity.toDomain(): AutomaticPixData {
    return AutomaticPixData(
        payer = AutomaticPixPayer(
            document = this.payerDocument,
            name = this.payerName,
        ),
        description = this.description,
        additionalInformation = this.additionalInformation,
        contractNumber = this.contractNumber,
    )
}

private fun BillPaymentScheduledInfo.toBillEventDetailEntity(): PaymentMethodsDetailEntity {
    return when (this) {
        is BillPaymentScheduledWithMultiple -> PaymentMethodsDetailMultipleEntity(
            methods = this.methods.map {
                it.toBillEventDetailEntitySingle()
            },
        )

        is BillPaymentScheduledInfo.SingleBillPaymentScheduledInfo -> this.toBillEventDetailEntitySingle()
    }
}

private fun BillPaymentScheduledInfo.SingleBillPaymentScheduledInfo.toBillEventDetailEntitySingle(): SinglePaymentMethodsDetailEntity {
    return when (this) {
        is BillPaymentScheduledBalanceInfo -> PaymentMethodsDetailBalanceEntity(
            amount = this.amount,
            paymentMethodId = this.paymentMethodId,
            calculationId = this.calculationId,
        )

        is BillPaymentScheduledCreditCardInfo -> PaymentMethodsDetailCreditCardEntity(
            paymentMethodId = this.paymentMethodId,
            netAmount = this.netAmount,
            feeAmount = this.feeAmount,
            installments = this.installments,
            calculationId = this.calculationId,
        )

        is BillPaymentScheduledExternalPayment -> PaymentMethodsDetailExternalEntity(
            providerName = this.providerName,
        )
    }
}

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY)
sealed class BillEventDetailsEntity {
    abstract val billId: BillId
    abstract val created: Long
    abstract val eventType: BillEventType
    abstract val walletId: WalletId
    abstract val actionSource: ActionSource
}

@JsonTypeName("AmountUpdated")
data class AmountUpdatedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val amount: Long,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.AMOUNT_UPDATED
}

@JsonTypeName("BillAdded")
@JsonIgnoreProperties(ignoreUnknown = true)
data class BillAddedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    val description: String,
    val dueDate: LocalDate,
    val amount: Long,
    val billType: BillType,
    val amountTotal: Long = amount,
    val discount: Long = 0,
    val fine: Long = 0,
    val interest: Long = 0,
    val barcode: BarCode? = null,
    val recipient: Recipient? = null,
    val contactId: ContactId? = null,
    val assignor: String? = null,
    val document: String? = null,
    val payerName: String? = null,
    val payerAlias: String? = null,
    val paymentLimitTime: String,
    val lastSettleDate: String? = null,
    val expirationDate: String? = null,
    val amountCalculationModel: AmountCalculationModel? = null,
    override val actionSource: ActionSource,
    val externalId: ExternalBillId? = null,
    val recurrenceRule: RecurrenceRule? = null,
    val effectiveDueDate: LocalDate? = null,
    @JsonAlias("fridaySubscription") val subscriptionFee: Boolean? = false,
    val securityValidationResult: List<String>? = null,
    val requestedDueDate: LocalDate? = null,
    val pixQrCodeData: PixQrCodeDataEntity? = null,
    val automaticPixAuthorizationMaximumAmount: Long? = null,
    val categoryId: PFMCategoryId? = null,
    val goalId: GoalId? = null,
    val automaticPixData: AutomaticPixDataEntity? = null,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.ADD
}

// FIXME aceitando os dois mundo de dynamo
@BillEventDependency
@DynamoDBDocument
@DynamoDbBean
class AutomaticPixDataEntity {
    var payerDocument: String? = null
    var payerName: String? = null
    var description: String? = null
    var additionalInformation: String? = null
    var contractNumber: String? = null
}

@BillEventDependency
@JsonIgnoreProperties(ignoreUnknown = true)
data class PixQrCodeDataEntity(
    val pixCopyAndPaste: String? = null,
    val pixId: String? = null,
    val fixedAmount: Long? = null,
    val type: PixQrCodeType,
    val info: String,
    val additionalInfo: Map<String, String>? = null,
    val expiration: ZonedDateTime?,
    val originalAmount: Long?,
    val automaticPixRecurringData: String?,
)

@JsonTypeName("FichaCompensacaoAdded")
data class FichaCompensacaoAddedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    val description: String,
    val dueDate: LocalDate,
    val amount: Long,
    val amountTotal: Long = amount,
    @JsonProperty("discount") val calculatedDiscount: Long = 0,
    @JsonProperty("fine") val calculatedFine: Long = 0,
    @JsonProperty("interest") val calculatedInterest: Long = 0,
    val barcode: BarCode,
    val recipient: Recipient,
    val recipientChain: RecipientChain?,
    val assignor: String,
    val document: String,
    val payerName: String? = null,
    val payerAlias: String? = null,
    val paymentLimitTime: String,
    val lastSettleDate: String? = null,
    val expirationDate: String,
    val amountCalculationModel: AmountCalculationModel,
    val interestValue: BigDecimal? = null,
    val interestType: InterestType? = null,
    val interestDate: LocalDate? = null,
    val fineValue: BigDecimal? = null,
    val fineType: FineType? = null,
    val fineDate: LocalDate? = null,
    val discountType: DiscountType? = null,
    val discount1Value: BigDecimal? = null,
    val discount1Date: LocalDate? = null,
    val discount2Value: BigDecimal? = null,
    val discount2Date: LocalDate? = null,
    val discount3Value: BigDecimal? = null,
    val discount3Date: LocalDate? = null,
    val abatement: BigDecimal? = null,
    override val actionSource: ActionSource,
    val effectiveDueDate: LocalDate,
    val fichaCompensacaoType: FichaCompensacaoType,
    val idNumber: String? = null,
    val registrationUpdateNumber: Long? = null,
    val brand: String? = null,
    @JsonAlias("fridaySubscription") val subscriptionFee: Boolean? = false,
    val securityValidationResult: List<String>? = null,
    val divergentPayment: DivergentPayment? = null,
    val partialPayment: PartialPayment? = null,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.ADD
}

@JsonTypeName("PaymentStarted")
data class PaymentStartedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    val transactionId: TransactionId,
    val correlationId: String? = null,
    override val actionSource: ActionSource,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.PAYMENT_START
}

@JsonTypeName("PaymentFailed")
data class PaymentFailedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    val retryable: Boolean,
    val errorSource: ErrorSource = ErrorSource.UNKNOWN,
    val errorDescription: String,
    val transactionId: TransactionId,
    override val actionSource: ActionSource = ActionSource.System,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.PAYMENT_FAIL
}

@JsonTypeName("PaymentRefunded")
data class PaymentRefundedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    val gateway: FinancialServiceGateway,
    val reason: PaymentRefundedReason,
    val transactionId: TransactionId,
    override val actionSource: ActionSource = ActionSource.System,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.PAYMENT_REFUNDED
}

@JsonTypeName("DescriptionUpdated")
data class DescriptionUpdatedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    val description: String,
    override val actionSource: ActionSource,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.UPDATE_DESCRIPTION
}

@JsonTypeName("BillPaid")
@JsonIgnoreProperties(ignoreUnknown = true)
data class BillPaidEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    val acquirer: Acquirer? = null,
    val acquirerTid: String? = null,
    val transactionId: TransactionId?, // TODO remove nullable after fill every event in database
    override val actionSource: ActionSource,
    val pixKeyDetails: PixKeyDetails? = null,
    val syncReceipt: Boolean = false,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.PAID
}

@JsonTypeName("BillReactivated")
data class BillReactivatedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.REACTIVATED
}

@JsonTypeName("BillMoved")
data class BillMovedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    val destinationWalletId: WalletId,
    val barCode: BarCode?,
    val dueDate: LocalDate?,
    val idNumber: String?,
    override val actionSource: ActionSource,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.MOVED
}

@JsonTypeName("BillPaymentScheduled")
data class BillPaymentScheduledEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val scheduledDate: String,
    val paymentMethodId: String? = null,
    val amount: Long,
    val paymentLimitTime: String? = null,
    val infoData: PaymentMethodsDetailEntity? = null,
    val batchSchedulingId: BatchSchedulingId? = null,
    val fingerprint: Fingerprint? = null,
    val paymentWalletId: WalletId? = null,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.PAYMENT_SCHEDULED
}

@JsonTypeName("BillPaymentScheduleCanceled")
data class BillPaymentScheduleCanceledEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    val reason: ScheduleCanceledReason,
    override val actionSource: ActionSource,
    val batchSchedulingId: BatchSchedulingId? = null,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.PAYMENT_SCHEDULE_CANCELED
}

@JsonTypeName("BillPaymentScheduleUpdated")
data class BillPaymentScheduleUpdatedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val updatedScheduleData: UpdatedScheduleData,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.PAYMENT_SCHEDULE_UPDATED
}

@JsonTypeName("BillIgnored")
data class BillIgnoredEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val removeFromListing: Boolean = false,
    val removeFromRecurrence: Boolean = false,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.IGNORED
}

@JsonTypeName("BillMarkedAsPaid")
data class BillMarkedAsPaidEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource.Api,
    val amountPaid: Long?,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.MARKED_AS_PAID
}

@JsonTypeName("BillMarkedAsPaidCanceled")
data class BillMarkedAsPaidCanceledEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.CANCELED_MARKED_AS_PAID
}

@JsonTypeName("BillPaymentScheduleStarted")
data class BillPaymentScheduleStartedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val paymentWalletId: WalletId? = null,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.PAYMENT_SCHEDULE_STARTED
}

@JsonTypeName("BillSchedulePostponed")
data class BillSchedulePostponedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val reason: SchedulePostponedReason,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.SCHEDULE_POSTPONED
}

@JsonTypeName("RegisterUpdated")
data class RegisterUpdatedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    val updatedRegisterData: UpdatedRegisterData,
    override val actionSource: ActionSource = ActionSource.System,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.REGISTER_UPDATED
}

// FIXME - nome do evento não deveria terminar com Entity. Está assim por compatibilidade com eventos que já exitiam no Dynamo
@JsonTypeName("RecurrenceUpdatedEntity")
data class RecurrenceUpdatedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val recurrenceRule: RecurrenceRule,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.RECURRENCE_UPDATED
}

@JsonTypeName("BillRecipientUpdated")
data class BillRecipientUpdatedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val recipient: Recipient,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.RECIPIENT_UPDATED
}

// FIXME - nome do evento não deveria terminar com Entity. Está assim por compatibilidade com eventos que já exitiam no Dynamo
@JsonTypeName("BillPermissionUpdatedEntity")
data class BillPermissionUpdatedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val permissionUpdated: PermissionUpdated,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.PERMISSION_UPDATED
}

@JsonTypeName("BillApproved")
data class BillApprovedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource.Api,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.APPROVED
}

@JsonTypeName("BillDenied")
data class BillDeniedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource.Api,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.DENIED
}

@JsonTypeName("BillTagAdded")
data class BillTagAddedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val tag: BillTag,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.TAG_ADDED
}

@JsonTypeName("BillTagDeleted")
data class BillTagDeletedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val tag: BillTag,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.TAG_DELETED
}

@JsonTypeName("BillCategoryAdded")
@JsonIgnoreProperties(ignoreUnknown = true)
data class BillCategoryAddedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val category: BillCategoryDocument?, // FIXME: remover o elemento depois de executar o script de correção na base.
    val categoryId: PFMCategoryId?, // FIXME: não precisa mais ser nulo após executar o script na base
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.CATEGORY_ADDED
}

@JsonTypeName("BillCategoryDeleted")
data class BillCategoryDeletedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.CATEGORY_DELETED
}

@JsonTypeName("BillCategorySuggestionAdded")
@JsonIgnoreProperties(ignoreUnknown = true)
data class BillCategorySuggestionAddedEntity(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource = ActionSource.System,
    val categories: List<BillCategorySuggestion>,
) : BillEventDetailsEntity() {
    override val eventType: BillEventType = BillEventType.CATEGORY_SUGGESTION_ADDED
}

@BillEventDependency
data class BillCategoryDocument(
    val billCategoryId: String,
    val name: String,
    val icon: String,
    val default: Boolean,
)