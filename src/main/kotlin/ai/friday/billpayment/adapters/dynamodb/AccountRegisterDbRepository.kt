package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.adapters.converters.CustomS3LinkAttributeConverter
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountClosureDetails
import ai.friday.billpayment.app.account.AccountClosureReason
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.Address
import ai.friday.billpayment.app.account.AgreementData
import ai.friday.billpayment.app.account.CustomS3Link
import ai.friday.billpayment.app.account.DocumentInfo
import ai.friday.billpayment.app.account.DocumentScan
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.account.Gender
import ai.friday.billpayment.app.account.IdentityValidationStatus
import ai.friday.billpayment.app.account.MonthlyIncome
import ai.friday.billpayment.app.account.PepQuery
import ai.friday.billpayment.app.account.PepQueryResult
import ai.friday.billpayment.app.account.PoliticallyExposed
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.account.UploadedDocumentImages
import ai.friday.billpayment.app.account.getCPF
import ai.friday.billpayment.app.documentscan.DocumentScanId
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.integrations.PersonBasicInfo
import ai.friday.billpayment.app.liveness.LivenessEnrollmentVerification
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.register.RiskAnalysisFailedReason
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import io.micronaut.http.MediaType
import jakarta.inject.Singleton
import java.io.InputStream
import java.text.DecimalFormat
import java.text.NumberFormat
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.Locale
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class AccountRegisterDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<AccountRegisterEntity>(cli, AccountRegisterEntity::class.java)

@Singleton
class OriginalOcrAndPersonDataDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<OriginalOcrAndPersonDataEntity>(cli, OriginalOcrAndPersonDataEntity::class.java)

const val accountRegisterKey = "ACCOUNT_REGISTER"
const val originalOcrKey = "ORIGINAL_OCR"
const val accountRegisterPrefix = "ACCOUNT-REGISTER#"
const val closedSuffix = "#CLOSED"

private val accountRegisterDateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy")

@Singleton
class AccountRegisterDbRepository(
    private val accountRegisterDAO: AccountRegisterDynamoDAO,
    private val originalOcrAndPersonDataDAO: OriginalOcrAndPersonDataDynamoDAO,
    private val objectRepository: ObjectRepository,
) : AccountRegisterRepository {
    override fun findByAccountId(
        accountId: AccountId,
        checkOpenAccount: Boolean,
    ): AccountRegisterData =
        findEntityByAccountId(accountId, checkOpenAccount = checkOpenAccount)
            .toAccountRegisterData()

    override fun findByDocument(document: String): AccountRegisterData =
        accountRegisterDAO
            .findByPartitionKeyOnIndex(
                GlobalSecondaryIndexNames.GSIndex1,
                "$accountRegisterPrefix$document",
            ).filter { it.checkOpenAccount() }
            .ifEmpty { throw ItemNotFoundException("AccountRegister not found for document $document") }
            .single()
            .toAccountRegisterData()

    override fun findByDocumentOrNull(document: String): AccountRegisterData? {
        return accountRegisterDAO
            .findByPartitionKeyOnIndex(
                GlobalSecondaryIndexNames.GSIndex1,
                "$accountRegisterPrefix$document",
            ).filter { it.checkOpenAccount() }
            .ifEmpty { return null }
            .single()
            .toAccountRegisterData()
    }

    override fun save(accountRegister: AccountRegisterData): AccountRegisterData {
        val accountRegisterUpdated =
            accountRegister.copy(
                lastUpdated = getZonedDateTime(),
            )
        accountRegisterDAO.save(accountRegisterUpdated.toAccountRegisterEntity())
        return accountRegisterUpdated
    }

    override fun create(
        accountId: AccountId,
        emailAddress: EmailAddress,
        emailAddressVerified: Boolean,
        username: String,
        mobilePhone: MobilePhone?,
        mobilePhoneVerified: Boolean,
        livenessId: LivenessId?,
        externalId: ExternalId?,
        documentInfo: DocumentInfo?,
        monthlyIncome: MonthlyIncome?,
        registrationType: RegistrationType,
        politicallyExposed: PoliticallyExposed?,
    ): AccountRegisterData {
        val accountRegisterData =
            AccountRegisterData(
                accountId = accountId,
                emailAddress = emailAddress,
                emailVerified = emailAddressVerified, // FIXME pode padronizar o nome
                nickname = username,
                mobilePhone = mobilePhone,
                mobilePhoneVerified = mobilePhoneVerified,
                livenessId = livenessId,
                externalId = externalId,
                documentInfo = documentInfo,
                created = getZonedDateTime(),
                lastUpdated = getZonedDateTime(),
                upgradeStarted = null,
                isDocumentEdited = false,
                openForUserReview = false,
                openedForUserReviewAt = null,
                calculatedGender = null,
                monthlyIncome = monthlyIncome,
                riskAnalysisFailedReasons = null,
                riskAnalysisFailedReasonsHistory = null,
                registrationType = registrationType,
                fraudListMatch = false,
                politicallyExposed = politicallyExposed,
            )
        this.save(accountRegisterData)
        return accountRegisterData
    }

    override fun putDocument(
        storedObject: StoredObject,
        fileData: InputStream,
        mediaType: MediaType,
    ) {
        val fileBytes = fileData.readAllBytes()
        objectRepository.putObject(storedObject, fileBytes, mediaType)
    }

    override fun getDocumentInputStream(storedObject: StoredObject): InputStream =
        objectRepository.loadObject(storedObject.bucket, storedObject.key)

    override fun deactivate(
        accountId: AccountId,
        closureDetails: AccountClosureDetails,
    ) {
        val accountRegisterDataEntity = findEntityByAccountId(accountId, checkOpenAccount = false)

        accountRegisterDataEntity.indexHashKey
            ?.takeIf { it.endsWith(closedSuffix) }
            ?.let { return }

        accountRegisterDAO.save(
            accountRegisterDataEntity.apply {
                indexHashKey = "$indexHashKey$closedSuffix"
                accountClosureDetails = closureDetails.toEntity()
            },
        )
    }

    override fun findByVerifiedMobilePhone(mobilePhone: MobilePhone): List<AccountRegisterData> =
        accountRegisterDAO
            .findByPartitionKeyOnIndex(
                GlobalSecondaryIndexNames.GSIndex2,
                mobilePhone.msisdn,
            ).filter { it.mobilePhoneVerified && it.checkOpenAccount() }
            .map {
                it.toAccountRegisterData()
            }

    private fun findEntityByAccountId(
        accountId: AccountId,
        checkOpenAccount: Boolean,
    ): AccountRegisterEntity {
        val entity = accountRegisterDAO.findByPrimaryKey(accountId.value, accountRegisterKey)
            ?: throw ItemNotFoundException("AccountRegister not found for AccountId ${accountId.value}")

        return if (checkOpenAccount && !entity.checkOpenAccount()) {
            throw ItemNotFoundException("AccountRegister not found for AccountId ${accountId.value}")
        } else {
            entity
        }
    }

    private fun AccountRegisterEntity.checkOpenAccount(): Boolean {
        if (indexHashKey == null) {
            return true
        }

        return !indexHashKey!!.endsWith(closedSuffix)
    }

    private fun AgreementData?.toAccountRegisterEntity(): AgreementDataEntity? =
        this?.let {
            AgreementDataEntity().apply {
                acceptedAt = it.acceptedAt?.format(DateTimeFormatter.ISO_DATE_TIME)
                acceptedAgreementObject =
                    CustomS3Link.create(
                        it.userContractFile.region!!,
                        it.userContractFile.bucket,
                        it.userContractFile.key,
                    )
                signatureKey = it.userContractSignature
                it.declarationOfResidencyFile?.let {
                    declarationOfResidencyObject =
                        CustomS3Link.create(
                            it.region!!,
                            it.bucket,
                            it.key,
                        )
                }
            }
        }

    private fun AccountRegisterData.toAccountRegisterEntity(): AccountRegisterEntity =
        this.let { accountRegisterData ->
            val riskAnalysisFailedReasonsHistoryVal: List<String> = accountRegisterData.riskAnalysisFailedReasonsHistory?.map { it.name } ?: emptyList()
            val riskAnalysisFailedReasonsVal: List<String> = accountRegisterData.riskAnalysisFailedReasons?.map { it.name } ?: emptyList()
            val riskSet = (riskAnalysisFailedReasonsHistoryVal + riskAnalysisFailedReasonsVal).toSet().takeIf { it.isNotEmpty() }
            AccountRegisterEntity().apply {
                primaryKey = accountRegisterData.accountId.value
                scanKey = accountRegisterKey
                indexHashKey = accountRegisterData.getCPF()?.let { "$accountRegisterPrefix$it" }
                indexRangeKey = accountRegisterData.getCPF()?.let { accountRegisterData.accountId.value }
                mobilePhone = accountRegisterData.mobilePhone?.msisdn
                mobilePhoneVerified = accountRegisterData.mobilePhoneVerified
                email = accountRegisterData.emailAddress.value
                emailVerified = accountRegisterData.emailVerified
                nickname = accountRegisterData.nickname
                accountId = accountRegisterData.accountId.value
                created = accountRegisterData.created.format(dateTimeFormat)
                lastUpdated = accountRegisterData.lastUpdated.format(dateTimeFormat)
                upgradeStarted = accountRegisterData.upgradeStarted?.format(dateTimeFormat)
                documentEdited = accountRegisterData.isDocumentEdited
                cnhObject = accountRegisterData.uploadedCNH?.generateCustomS3Link()
                documentImages =
                    accountRegisterData.uploadedDocument?.let {
                        convertUploadedDocumentImagestoEntity(it)
                    }
                documentInfo = accountRegisterData.documentInfo?.toDocumentInfoEntity()
                addressInfo = accountRegisterData.address?.toAddressInfoEntity()
                monthlyIncome =
                    accountRegisterData.monthlyIncome?.let {
                        MonthlyIncomeEntity().apply {
                            lowerBound = accountRegisterData.monthlyIncome.lowerBound
                            upperBound = accountRegisterData.monthlyIncome.upperBound
                        }
                    }
                politicallyExposed = accountRegisterData.politicallyExposed?.toPoliticallyExposedEntity()
                selfieObject = accountRegisterData.uploadedSelfie?.generateCustomS3Link()
                uploadedSelfieDateTime =
                    accountRegisterData.uploadedSelfieDateTime?.format(
                        dateTimeFormat,
                    )
                agreementData = accountRegisterData.agreementData?.toAccountRegisterEntity()
                upgradeAgreementData = accountRegisterData.upgradeAgreementData?.toAccountRegisterEntity()
                kycFile = accountRegisterData.kycFile?.generateCustomS3Link()
                openForUserReview = accountRegisterData.openForUserReview
                openedForUserReviewAt =
                    accountRegisterData.openedForUserReviewAt?.format(
                        dateTimeFormat,
                    )
                calculatedGender = accountRegisterData.calculatedGender
                index2HashKey = mobilePhone
                index2RangeKey = accountRegisterData.mobilePhoneVerified.toString()
                livenessId = accountRegisterData.livenessId?.value
                externalId = accountRegisterData.externalId?.toExternalIdEntity()
                livenessDuplicationVerification =
                    accountRegisterData.livenessEnrollmentVerification.toLivenessDuplicationVerificationEntity()
                livenessFraudVerification =
                    accountRegisterData.livenessEnrollmentVerification.toLivenessFraudVerificationEntity()
                accountDeniedReasons = accountRegisterData.riskAnalysisFailedReasons?.map { it.name }
                accountDeniedReasonsHistory = riskSet
                accountRecoverable = accountRegisterData.accountRecoverable
                birthDate = accountRegisterData.birthDate?.format(dateFormat)
                document = accountRegisterData.document?.value
                identityValidationStatus = accountRegisterData.identityValidationStatus
                registrationType = accountRegisterData.registrationType
                identityValidationPercentage =
                    accountRegisterData.identityValidationPercentage?.let {
                        val decimalFormat = NumberFormat.getNumberInstance(Locale.US) as DecimalFormat
                        decimalFormat.applyPattern("#.####")
                        decimalFormat.format(it)
                    }
                fraudListMatch = accountRegisterData.fraudListMatch
                accountClosureDetails = accountRegisterData.accountClosureDetails?.toEntity()
                clientIP = accountRegisterData.clientIP
                documentScan = accountRegisterData.documentScan?.let {
                    DocumentScanEntity().apply {
                        documentType = accountRegisterData.documentScan.documentType.value
                        documentScanId = accountRegisterData.documentScan.documentScanId.value
                    }
                }
            }
        }

    override fun saveOriginalOcrAndPersonData(
        accountId: AccountId,
        originalDocumentInfo: DocumentInfo,
        personBasicInfo: PersonBasicInfo?,
        validatedDocumentInfo: DocumentInfo,
    ) {
        originalOcrAndPersonDataDAO.save(
            OriginalOcrAndPersonDataEntity().apply {
                primaryKey = accountId.value
                scanKey = originalOcrKey
                originalOcr = originalDocumentInfo.toDocumentInfoEntity()
                this.personBasicInfo = personBasicInfo?.toPersonBasicInfoEntity()
                this.validatedDocumentInfo = validatedDocumentInfo.toDocumentInfoEntity()
            },
        )
    }

    override fun findValidatedDocumentInfo(accountId: AccountId): DocumentInfo? {
        val entity = originalOcrAndPersonDataDAO.findByPrimaryKey(accountId.value, originalOcrKey)
            ?: throw ItemNotFoundException("OriginalOcrAndPersonDataEntity not found for AccountId ${accountId.value}")

        return entity.validatedDocumentInfo?.toDocumentInfo()
    }

    private fun StoredObject.generateCustomS3Link() = CustomS3Link.fromStoredObject(this)

    private fun convertUploadedDocumentImagestoEntity(uploadedDocumentImages: UploadedDocumentImages) =
        DocumentImagesEntity().apply {
            front = uploadedDocumentImages.front.generateCustomS3Link()
            documentType = uploadedDocumentImages.documentType.value
            back = uploadedDocumentImages.back?.generateCustomS3Link()
        }
}

private fun PoliticallyExposed?.toPoliticallyExposedEntity(): PoliticallyExposedEntity? =
    this?.let {
        PoliticallyExposedEntity().apply {
            this.isExposed = it.selfDeclared
            this.selfDeclared = it.selfDeclared
            this.query =
                it.query?.let {
                    PepQueryEntity().apply {
                        this.at = it.at.format(dateTimeFormat).toString()
                        this.result = it.result.name
                    }
                }
        }
    }

private fun Address?.toAddressInfoEntity(): AddressInfoEntity? =
    this?.let { address ->
        AddressInfoEntity().apply {
            streetType = address.streetType
            streetName = address.streetName
            number = address.number
            complement = address.complement
            neighborhood = address.neighborhood
            city = address.city
            state = address.state
            zipCode = address.zipCode
        }
    }

private fun AccountClosureDetails.toEntity(): AccountClosureDetailsEntity =
    this.let {
        when (it) {
            is AccountClosureDetails.WithReason ->
                AccountClosureDetailsEntity().apply {
                    at = it.at.format(dateTimeFormat)
                    reason = it.reason.name
                    description = it.description
                }

            is AccountClosureDetails.WithoutReason ->
                AccountClosureDetailsEntity().apply {
                    at = it.at.format(dateTimeFormat)
                    reason = null
                    description = it.description
                }
        }
    }

fun PersonBasicInfo?.toPersonBasicInfoEntity(): PersonBasicInfoEntity? =
    this?.let {
        PersonBasicInfoEntity().apply {
            name = it.name
            birthDate = it.birthDate?.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"))
            motherName = it.motherName
            fatherName = it.fatherName
        }
    }

private fun DocumentInfo.toDocumentInfoEntity(): DocumentInfoEntity =
    this.let { documentInfo ->
        DocumentInfoEntity().apply {
            name = documentInfo.name
            cpf = documentInfo.cpf
            birthDate = documentInfo.birthDate?.format(accountRegisterDateFormatter)
            fatherName = documentInfo.fatherName
            motherName = documentInfo.motherName
            rg = documentInfo.rg
            docType = documentInfo.docType.value
            cnhNumber = documentInfo.cnhNumber
            orgEmission = documentInfo.orgEmission
            expeditionDate = documentInfo.expeditionDate?.format(accountRegisterDateFormatter)
            birthCity = documentInfo.birthCity
            birthState = documentInfo.birthState
        }
    }

private fun ExternalId.toExternalIdEntity(): ExternalIdEntity =
    this.let { externalId ->
        ExternalIdEntity().apply {
            value = externalId.value
            providerName = externalId.providerName
        }
    }

private fun LivenessEnrollmentVerification.toLivenessDuplicationVerificationEntity(): LivenessDuplicationVerificationEntity? =
    this.let {
        if (it.duplications is LivenessEnrollmentVerification.Result.Verified) {
            LivenessDuplicationVerificationEntity().apply {
                duplications = it.duplications.accountIds.map { accountId -> accountId.value }
            }
        } else {
            null
        }
    }

private fun LivenessEnrollmentVerification.toLivenessFraudVerificationEntity(): LivenessFraudVerificationEntity? =
    this.let {
        if (it.fraudIndications is LivenessEnrollmentVerification.Result.Verified) {
            LivenessFraudVerificationEntity().apply {
                accountIds = it.fraudIndications.accountIds.map { accountId -> accountId.value }
            }
        } else {
            null
        }
    }

private fun AccountRegisterEntity.toAccountRegisterData() =
    AccountRegisterData(
        mobilePhone = mobilePhone?.let { msisdn -> MobilePhone(msisdn) },
        mobilePhoneVerified = mobilePhoneVerified,
        emailAddress = EmailAddress(email),
        emailVerified = emailVerified,
        nickname = nickname,
        accountId = AccountId(accountId),
        created = ZonedDateTime.parse(created, dateTimeFormat),
        lastUpdated = ZonedDateTime.parse(lastUpdated, dateTimeFormat),
        upgradeStarted = upgradeStarted?.let { ZonedDateTime.parse(it, dateTimeFormat) },
        documentInfo = documentInfo?.toDocumentInfo(),
        isDocumentEdited = documentEdited,
        uploadedCNH = cnhObject?.retrieveStoredObjectLink(),
        uploadedDocument = documentImages?.toUploadedDocumentImages(),
        uploadedSelfie = selfieObject?.retrieveStoredObjectLink(),
        uploadedSelfieDateTime =
        uploadedSelfieDateTime?.let {
            ZonedDateTime.parse(
                it,
                dateTimeFormat,
            )
        },
        address = addressInfo.toAddress(),
        monthlyIncome = monthlyIncome?.let { MonthlyIncome(lowerBound = it.lowerBound!!, upperBound = it.upperBound) },
        politicallyExposed = politicallyExposed?.toPoliticallyExposed(),
        agreementData = agreementData?.toAgreementData(),
        upgradeAgreementData = upgradeAgreementData?.toAgreementData(),
        kycFile = kycFile?.retrieveStoredObjectLink(),
        openForUserReview = openForUserReview ?: false,
        openedForUserReviewAt =
        openedForUserReviewAt?.let {
            ZonedDateTime.parse(
                it,
                dateTimeFormat,
            )
        },
        calculatedGender = calculatedGender,
        livenessId = livenessId?.let { LivenessId(it) },
        externalId = externalId?.toExternalId(),
        livenessEnrollmentVerification = this.toLivenessEnrollmentVerification(),
        riskAnalysisFailedReasons = accountDeniedReasons?.map { RiskAnalysisFailedReason.valueOf(it) },
        riskAnalysisFailedReasonsHistory = accountDeniedReasonsHistory?.map { RiskAnalysisFailedReason.valueOf(it) },
        birthDate = birthDate?.let { LocalDate.parse(it, dateFormat) },
        document = document.toDocument(),
        identityValidationStatus = identityValidationStatus,
        registrationType = registrationType ?: RegistrationType.FULL,
        identityValidationPercentage = identityValidationPercentage?.toDouble(),
        fraudListMatch = this.fraudListMatch ?: false,
        accountClosureDetails = accountClosureDetails?.toDomain(),
        clientIP = clientIP,
        documentScan = documentScan?.let { DocumentScan(documentType = DocumentType.of(it.documentType), documentScanId = DocumentScanId(it.documentScanId)) },
    )

private fun AccountRegisterEntity.toLivenessEnrollmentVerification(): LivenessEnrollmentVerification =
    LivenessEnrollmentVerification(
        duplications =
        LivenessEnrollmentVerification.Result.create(
            livenessDuplicationVerification?.duplications?.map {
                AccountId(
                    it,
                )
            },
        ),
        fraudIndications =
        LivenessEnrollmentVerification.Result.create(
            livenessFraudVerification?.accountIds?.map {
                AccountId(
                    it,
                )
            },
        ),
    )

private fun AccountClosureDetailsEntity.toDomain(): AccountClosureDetails {
    this.let { entity ->
        return AccountClosureDetails.create(
            at = ZonedDateTime.parse(entity.at, dateTimeFormat),
            reason = entity.reason?.let { AccountClosureReason.valueOf(it) },
            description = entity.description,
        )
    }
}

private fun String?.toDocument() = this?.let { Document(this) }

private fun DocumentImagesEntity.toUploadedDocumentImages() =
    UploadedDocumentImages(
        front = front.retrieveStoredObjectLink(),
        documentType = DocumentType.of(documentType),
        back = back?.retrieveStoredObjectLink(),
    )

private fun CustomS3Link.retrieveStoredObjectLink() = StoredObject(region, bucket, key)

private fun AddressInfoEntity?.toAddress(): Address? =
    this?.let {
        Address(
            streetType = it.streetType,
            streetName = it.streetName,
            number = it.number,
            complement = it.complement,
            neighborhood = it.neighborhood,
            city = it.city,
            state = it.state,
            zipCode = it.zipCode,
        )
    }

private fun PoliticallyExposedEntity?.toPoliticallyExposed(): PoliticallyExposed? =
    this?.let {
        PoliticallyExposed(
            selfDeclared = selfDeclared ?: isExposed,
            query = query.toPepQuery(),
        )
    }

private fun PepQueryEntity?.toPepQuery(): PepQuery? {
    val at = this?.at
    val result = this?.result

    return if (at != null && result != null) {
        PepQuery(
            at = ZonedDateTime.parse(at, dateTimeFormat),
            result = PepQueryResult.valueOf(result),
        )
    } else {
        null
    }
}

private fun AgreementDataEntity?.toAgreementData(): AgreementData? =
    this?.let {
        AgreementData(
            acceptedAt = acceptedAt?.let { ZonedDateTime.parse(it, DateTimeFormatter.ISO_DATE_TIME) },
            userContractFile =
            StoredObject(
                acceptedAgreementObject.region,
                acceptedAgreementObject.bucket,
                acceptedAgreementObject.key,
            ),
            userContractSignature = signatureKey,
            declarationOfResidencyFile =
            declarationOfResidencyObject?.let {
                StoredObject(
                    it.region,
                    it.bucket,
                    it.key,
                )
            },
        )
    }

private fun DocumentInfoEntity.toDocumentInfo(): DocumentInfo =
    DocumentInfo(
        name = name.orEmpty(),
        cpf = cpf.orEmpty(),
        birthDate = birthDate?.let { date -> LocalDate.parse(date, accountRegisterDateFormatter) },
        docType = DocumentType.of(docType),
        cnhNumber = cnhNumber,
        fatherName = fatherName.orEmpty(),
        motherName = motherName.orEmpty(),
        rg = rg.orEmpty(),
        orgEmission = orgEmission.orEmpty(),
        expeditionDate = expeditionDate?.let { date -> LocalDate.parse(date, accountRegisterDateFormatter) },
        birthState = birthState.orEmpty(),
        birthCity = birthCity.orEmpty(),
    )

private fun ExternalIdEntity.toExternalId() =
    ExternalId(
        value = value,
        providerName = providerName,
    )

@DynamoDbBean
class OriginalOcrAndPersonDataEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbAttribute("OriginalOcr")
    lateinit var originalOcr: DocumentInfoEntity

    @get:DynamoDbAttribute("PersonBasicInfo")
    var personBasicInfo: PersonBasicInfoEntity? = null

    @get:DynamoDbAttribute("ValidatedDocumentInfo")
    var validatedDocumentInfo: DocumentInfoEntity? = null
}

@DynamoDbBean
class AccountRegisterEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    // TODO atualizar objetos antigos com a coluna nova
    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute("GSIndex1PrimaryKey")
    var indexHashKey: String? = null

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute("GSIndex1ScanKey")
    var indexRangeKey: String? = null

    @get:DynamoDbAttribute("MobilePhone")
    var mobilePhone: String? = null

    @get:DynamoDbAttribute("MobilePhoneVerified")
    var mobilePhoneVerified: Boolean = false

    @get:DynamoDbAttribute("Email")
    lateinit var email: String

    @get:DynamoDbAttribute("EmailVerified")
    var emailVerified: Boolean = false

    @get:DynamoDbAttribute("Nickname")
    var nickname: String = ""

    @get:DynamoDbAttribute("AccountId")
    lateinit var accountId: String

    @get:DynamoDbAttribute("Created")
    lateinit var created: String

    @get:DynamoDbAttribute("LastUpdated")
    lateinit var lastUpdated: String

    @get:DynamoDbAttribute("UpgradeStarted")
    var upgradeStarted: String? = null

    @get:DynamoDbAttribute("DocumentInfo")
    var documentInfo: DocumentInfoEntity? = null

    @get:DynamoDbAttribute("DocumentEdited")
    var documentEdited: Boolean = false

    @Deprecated("Com o scanner de documentos, a imagem fica salva no DocumentScantRepository")
    @get:DynamoDbAttribute("DocumentObject")
    @get:DynamoDbConvertedBy(value = CustomS3LinkAttributeConverter::class)
    var cnhObject: CustomS3Link? = null

    @Deprecated("Com o scanner de documentos, a imagem fica salva no DocumentScantRepository")
    @get:DynamoDbAttribute("DocumentImagesObject")
    var documentImages: DocumentImagesEntity? = null

    @get:DynamoDbAttribute("SelfieObject")
    @get:DynamoDbConvertedBy(value = CustomS3LinkAttributeConverter::class)
    var selfieObject: CustomS3Link? = null

    @get:DynamoDbAttribute("UploadedSelfieDateTime")
    var uploadedSelfieDateTime: String? = null

    @get:DynamoDbAttribute("AdressInfo")
    var addressInfo: AddressInfoEntity? = null

    @get:DynamoDbAttribute("MonthlyIncome")
    var monthlyIncome: MonthlyIncomeEntity? = null

    @get:DynamoDbAttribute("PoliticallyExposed")
    var politicallyExposed: PoliticallyExposedEntity? = null

    @get:DynamoDbAttribute("AggreementData")
    var agreementData: AgreementDataEntity? = null

    @get:DynamoDbAttribute("UpgradeAggreementData")
    var upgradeAgreementData: AgreementDataEntity? = null

    @get:DynamoDbAttribute("KycFile")
    @get:DynamoDbConvertedBy(CustomS3LinkAttributeConverter::class)
    var kycFile: CustomS3Link? = null

    @get:DynamoDbAttribute("OpenForUserReview")
    var openForUserReview: Boolean? = null

    @get:DynamoDbAttribute("OpenedForUserReviewAt")
    var openedForUserReviewAt: String? = null

    @get:DynamoDbAttribute("CalculatedGender")
    var calculatedGender: Gender? = null

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute("GSIndex2PrimaryKey")
    var index2HashKey: String? = null

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute("GSIndex2ScanKey")
    var index2RangeKey: String? = null

    @get:DynamoDbAttribute("LivenessId")
    var livenessId: String? = null

    @get:DynamoDbAttribute("ExternalId")
    var externalId: ExternalIdEntity? = null

    @get:DynamoDbAttribute("LivenessDuplicationVerification")
    var livenessDuplicationVerification: LivenessDuplicationVerificationEntity? = null

    @get:DynamoDbAttribute("LivenessFraudVerification")
    var livenessFraudVerification: LivenessFraudVerificationEntity? = null

    @get:DynamoDbAttribute("AccountDeniedReasons")
    var accountDeniedReasons: List<String>? = null

    @get:DynamoDbAttribute("AccountDeniedReasonsHistory")
    var accountDeniedReasonsHistory: Set<String>? = null

    @get:DynamoDbAttribute("AccountRecoverable")
    var accountRecoverable: Boolean? = null

    @get:DynamoDbAttribute("Document")
    var document: String? = null

    @get:DynamoDbAttribute("BirthDate")
    var birthDate: String? = null

    @get:DynamoDbAttribute("IdentityValidationStatus")
    var identityValidationStatus: IdentityValidationStatus? = null

    @get:DynamoDbAttribute("IdentityValidationPercentage")
    var identityValidationPercentage: String? = null

    @get:DynamoDbAttribute("FraudListMatch")
    var fraudListMatch: Boolean? = null

    @get:DynamoDbAttribute("RegistrationType")
    var registrationType: RegistrationType? = null

    @get:DynamoDbAttribute("AccountClosureDetails")
    var accountClosureDetails: AccountClosureDetailsEntity? = null

    @get:DynamoDbAttribute("ClientIP")
    var clientIP: String? = null

    @get:DynamoDbAttribute("DocumentScan")
    var documentScan: DocumentScanEntity? = null
}

@DynamoDbBean
class DocumentScanEntity {
    @get:DynamoDbAttribute("documentType")
    lateinit var documentType: String

    @get:DynamoDbAttribute("documentScanId")
    lateinit var documentScanId: String
}

@DynamoDbBean
class DocumentImagesEntity {
    @get:DynamoDbAttribute("front")
    @get:DynamoDbConvertedBy(CustomS3LinkAttributeConverter::class)
    lateinit var front: CustomS3Link

    @get:DynamoDbAttribute("documentType")
    lateinit var documentType: String

    @get:DynamoDbAttribute("back")
    @get:DynamoDbConvertedBy(CustomS3LinkAttributeConverter::class)
    var back: CustomS3Link? = null
}

@DynamoDbBean
class MonthlyIncomeEntity {
    @get:DynamoDbAttribute("lowerBound")
    var lowerBound: Long? = null

    @get:DynamoDbAttribute("upperBound")
    var upperBound: Long? = null
}

@DynamoDbBean
class AgreementDataEntity {
    @get:DynamoDbAttribute("acceptedAt")
    var acceptedAt: String? = null

    @get:DynamoDbAttribute("acceptedAgreementObject")
    @get:DynamoDbConvertedBy(CustomS3LinkAttributeConverter::class)
    lateinit var acceptedAgreementObject: CustomS3Link

    @get:DynamoDbAttribute("signatureKey")
    lateinit var signatureKey: String

    @get:DynamoDbAttribute("declarationOfResidencyObject")
    @get:DynamoDbConvertedBy(CustomS3LinkAttributeConverter::class)
    var declarationOfResidencyObject: CustomS3Link? = null
}

@DynamoDbBean
class DocumentInfoEntity {
    @get:DynamoDbAttribute("docType")
    lateinit var docType: String

    @get:DynamoDbAttribute("name")
    var name: String? = null

    @get:DynamoDbAttribute("cpf")
    var cpf: String? = null

    @get:DynamoDbAttribute("birthDate")
    var birthDate: String? = null

    @get:DynamoDbAttribute("cnhNumber")
    var cnhNumber: String? = null

    @get:DynamoDbAttribute("fatherName")
    var fatherName: String? = null

    @get:DynamoDbAttribute("motherName")
    var motherName: String? = null

    @get:DynamoDbAttribute("rg")
    var rg: String? = null

    @get:DynamoDbAttribute("orgEmission")
    var orgEmission: String? = null

    @get:DynamoDbAttribute("expeditionDate")
    var expeditionDate: String? = null

    @get:DynamoDbAttribute("birthCity")
    var birthCity: String? = null

    @get:DynamoDbAttribute("birthState")
    var birthState: String? = null
}

@DynamoDbBean
class ExternalIdEntity {
    @get:DynamoDbAttribute("value")
    lateinit var value: String

    @get:DynamoDbAttribute("providerName")
    lateinit var providerName: AccountProviderName
}

@DynamoDbBean
class PersonBasicInfoEntity {
    @get:DynamoDbAttribute("name")
    var name: String? = null

    @get:DynamoDbAttribute("birthDate")
    var birthDate: String? = null

    @get:DynamoDbAttribute("motherName")
    var motherName: String? = null

    @get:DynamoDbAttribute("fatherName")
    var fatherName: String? = null
}

@DynamoDbBean
class AddressInfoEntity {
    @get:DynamoDbAttribute("streetType")
    var streetType: String? = null

    @get:DynamoDbAttribute("streetName")
    lateinit var streetName: String

    @get:DynamoDbAttribute("number")
    var number: String? = null

    @get:DynamoDbAttribute("complement")
    var complement: String? = null

    @get:DynamoDbAttribute("neighborhood")
    lateinit var neighborhood: String

    @get:DynamoDbAttribute("city")
    lateinit var city: String

    @get:DynamoDbAttribute("state")
    lateinit var state: String

    @get:DynamoDbAttribute("zipCode")
    lateinit var zipCode: String
}

@DynamoDbBean
class PoliticallyExposedEntity {
    @Deprecated("Usar o selfDeclared, mas este valor será mantido para evitar uma migração da base de dados")
    @get:DynamoDbAttribute("isExposed")
    var isExposed: Boolean = false

    @get:DynamoDbAttribute("selfDeclared")
    var selfDeclared: Boolean? = null

    @get:DynamoDbAttribute("query")
    var query: PepQueryEntity? = null
}

@DynamoDbBean
class PepQueryEntity {
    @get:DynamoDbAttribute("at")
    var at: String? = null

    @get:DynamoDbAttribute("result")
    var result: String? = null
}

@DynamoDbBean
class LivenessDuplicationVerificationEntity {
    @Deprecated("Para saber se há duplicação, usar o podemos checar o length da lista de duplicações")
    @get:DynamoDbAttribute("hasDuplication")
    var hasDuplication: Boolean = false

    @get:DynamoDbAttribute("duplications")
    var duplications: List<String> = emptyList()
}

@DynamoDbBean
class LivenessFraudVerificationEntity {
    @get:DynamoDbAttribute("accountIds")
    var accountIds: List<String> = emptyList()
}

@DynamoDbBean
class AccountClosureDetailsEntity {
    @get:DynamoDbAttribute("at")
    var at: String? = null

    @get:DynamoDbAttribute("reason")
    var reason: String? = null

    @get:DynamoDbAttribute("description")
    var description: String? = null
}