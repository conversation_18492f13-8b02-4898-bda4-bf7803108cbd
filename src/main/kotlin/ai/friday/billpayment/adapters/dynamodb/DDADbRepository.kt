package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.dda.DDABill
import ai.friday.billpayment.app.dda.DDAProvider
import ai.friday.billpayment.app.dda.DDARegister
import ai.friday.billpayment.app.dda.DDAStatus
import ai.friday.billpayment.app.integrations.DDARepository
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val DDAIndexPartitionKey = "DDA_REGISTER"

@Singleton
class DDADynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<DDAEntity>(cli, DDAEntity::class.java)

@Singleton
class DDABillDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<DDABillEntity>(cli, DDABillEntity::class.java)

@Singleton
class DDADbRepository(
    private val ddaDAO: DDADynamoDAO,
    private val ddaBillDAO: DDABillDynamoDAO,
) : DDARepository {

    override fun save(ddaRegister: DDARegister): DDARegister {
        val entity = DDAEntity().apply {
            primaryKey = ddaRegister.accountId.value
            scanKey = DDAIndexPartitionKey
            accountId = ddaRegister.accountId.value
            document = ddaRegister.document
            created = ddaRegister.created.format(DateTimeFormatter.ISO_DATE_TIME)
            lastUpdated = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
            status = ddaRegister.status
            index1HashKey = DDAIndexPartitionKey
            index1RangeKey = ddaRegister.status.name
            lastSuccessfullExecution =
                ddaRegister.lastSuccessfullExecution?.format(DateTimeFormatter.ISO_DATE_TIME)
            provider = ddaRegister.provider
            index2HashKey = DDAIndexPartitionKey
            index2RangeKey = ddaRegister.document
            migrated = ddaRegister.migrated?.format(DateTimeFormatter.ISO_DATE_TIME)
        }
        ddaDAO.save(entity)
        return ddaRegister
    }

    override fun find(accountId: AccountId): DDARegister? {
        return ddaDAO.findByPrimaryKey(
            partitionKey = accountId.value,
            sortKey = DDAIndexPartitionKey,
        )?.toDDARegister()
    }

    override fun find(document: Document): DDARegister? {
        return ddaDAO.findByPartitionKeyAndScanKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex2,
            partitionKey = DDAIndexPartitionKey,
            sortKey = document.value,
        ).map {
            it.toDDARegister()
        }.singleOrNull()
    }

    override fun findOlderRegister(document: Document, newerAccountId: AccountId): DDARegister? {
        return ddaDAO.findByPartitionKeyAndScanKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex2,
            partitionKey = DDAIndexPartitionKey,
            sortKey = document.value,
        ).filter {
            it.accountId != newerAccountId.value
        }.map {
            it.toDDARegister()
        }.singleOrNull()
    }

    override fun findByStatus(status: DDAStatus): List<DDARegister> {
        return ddaDAO.findByPartitionKeyAndScanKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = DDAIndexPartitionKey,
            sortKey = status.name,
        ).map {
            it.toDDARegister()
        }
    }

    override fun find(barCode: BarCode, document: String, dueDate: LocalDate): DDABill? {
        return ddaBillDAO.findByPrimaryKey(
            partitionKey = barCode.digitable,
            sortKey = "DDA#$document#${dueDate.format(dateFormat)}",
        )?.let {
            DDABill(
                barcode = barCode,
                document = it.document,
                dueDate = dueDate,
                walletId = WalletId(value = it.walletId),
                billId = BillId(value = it.billId),
                lastStatus = BillStatus.fromString(it.lastStatus),
            )
        }
    }

    override fun save(ddaBill: DDABill) {
        val entity = DDABillEntity().apply {
            barcode = ddaBill.barcode.digitable
            document = ddaBill.document
            dueDate = ddaBill.dueDate.format(dateFormat)
            scanKey = "DDA#${ddaBill.document}#$dueDate"
            walletId = ddaBill.walletId.value
            billId = ddaBill.billId.value
            lastStatus = ddaBill.lastStatus.name
        }
        ddaBillDAO.save(entity)
    }
}

private fun DDAEntity.toDDARegister() = DDARegister(
    accountId = AccountId(this.accountId),
    document = this.document,
    created = ZonedDateTime.parse(this.created, DateTimeFormatter.ISO_DATE_TIME),
    lastUpdated = ZonedDateTime.parse(this.lastUpdated, DateTimeFormatter.ISO_DATE_TIME),
    lastSuccessfullExecution = this.lastSuccessfullExecution?.let {
        ZonedDateTime.parse(
            it,
            DateTimeFormatter.ISO_DATE_TIME,
        )
    },
    status = this.status,
    provider = this.provider,
    migrated = this.migrated?.let {
        ZonedDateTime.parse(
            it,
            DateTimeFormatter.ISO_DATE_TIME,
        )
    },
)

@DynamoDbBean
class DDABillEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var barcode: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbAttribute(value = "Document")
    lateinit var document: String

    @get:DynamoDbAttribute(value = "Name")
    lateinit var walletId: String

    @get:DynamoDbAttribute(value = "BillId")
    lateinit var billId: String

    @get:DynamoDbAttribute(value = "Duedate")
    lateinit var dueDate: String

    @get:DynamoDbAttribute(value = "LastStatus")
    lateinit var lastStatus: String
}

@DynamoDbBean
class DDAEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String // ACCOUNT-ID

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String // DDA

    @get:DynamoDbAttribute(value = "AccountId")
    lateinit var accountId: String

    @get:DynamoDbAttribute(value = "Document")
    lateinit var document: String

    @get:DynamoDbAttribute(value = "Created")
    lateinit var created: String

    @get:DynamoDbAttribute(value = "LastUpdated")
    lateinit var lastUpdated: String

    @get:DynamoDbAttribute(value = "LastSuccessfullExecution")
    var lastSuccessfullExecution: String? = null

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: DDAStatus

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var index1HashKey: String // DDA

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var index1RangeKey: String

    @get:DynamoDbAttribute(value = "Provider")
    var provider: DDAProvider = DDAProvider.ARBI

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    lateinit var index2HashKey: String // DDA

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    lateinit var index2RangeKey: String

    @get:DynamoDbAttribute(value = "MigratedAt")
    var migrated: String? = null
}