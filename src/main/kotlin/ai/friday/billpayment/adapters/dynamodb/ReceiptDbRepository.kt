package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.integrations.ReceiptRepository
import ai.friday.billpayment.app.payment.ReceiptData
import ai.friday.billpayment.app.wallet.WalletId
import jakarta.inject.Singleton
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class ReceiptDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<ReceiptEntity>(cli, ReceiptEntity::class.java)

@Singleton
class ReceiptDbRepository(private val client: ReceiptDynamoDAO) : ReceiptRepository {
    private val mapper = getObjectMapper()

    override fun save(receiptData: ReceiptData) {
        val entity = ReceiptEntity().apply {
            partitionKey = receiptData.billId.value
            sortKey = buildScanKey(receiptData.walletId)
            receipt = mapper.writeValueAsString(receiptData)
        }

        client.save(entity)
    }

    override fun findById(walletId: WalletId, billId: BillId): ReceiptData {
        val entity = client.findByPrimaryKey(
            partitionKey = billId.value,
            sortKey = buildScanKey(walletId),
        ) ?: throw NoSuchElementException("Receipt not found")
        return entity.toReceiptData()
    }

    private fun buildScanKey(walletId: WalletId) = "RECEIPT#${walletId.value}"
}

@DynamoDbBean
class ReceiptEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String

    @get:DynamoDbAttribute(value = "ReceiptData")
    lateinit var receipt: String
}

private fun ReceiptEntity.toReceiptData(): ReceiptData {
    return parseObjectFrom(this.receipt)
}