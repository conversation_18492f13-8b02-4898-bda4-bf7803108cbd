package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.InvalidTokenException
import ai.friday.billpayment.app.account.InvalidTokenReason
import ai.friday.billpayment.app.account.TokenKey
import ai.friday.billpayment.app.integrations.TokenData
import ai.friday.billpayment.app.integrations.TokenDataWithExpiration
import ai.friday.billpayment.app.integrations.TokenRepository
import ai.friday.billpayment.app.integrations.TokenType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.extensions.annotations.DynamoDbAtomicCounter
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class TokenDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<TokenEntity>(cli, TokenEntity::class.java)

@Singleton
open class TokenDbRepository(private val client: TokenDynamoDAO) : TokenRepository {

    private val tokenPrefix = "TOKEN#"

    override fun save(tokenKey: TokenKey, tokenData: TokenDataWithExpiration) {
        val tokenEntity = TokenEntity().apply {
            primaryKey = tokenKey.accountId.value
            scanKey = buildScanKey(tokenData)
            this.token = tokenKey.value
            accountId = tokenKey.accountId.value
            this.typeValue = tokenData.value
            this.expiration = tokenData.expiration
        }
        client.save(tokenEntity)
    }

    override fun existsToken(
        accountId: AccountId,
        tokenType: TokenType,
    ): Boolean {
        return client.findBeginsWithOnSortKey(
            partitionKey = accountId.value,
            sortKey = buildScanKeyPrefix(tokenType),
        )
            .any { it.isExpired().not() }
    }

    override fun retrieveNotExpired(
        accountId: AccountId,
        tokenType: TokenType,
    ): Either<InvalidTokenException, TokenDataWithExpiration> {
        val foundEntity = client.findBeginsWithOnSortKey(
            partitionKey = accountId.value,
            sortKey = buildScanKeyPrefix(tokenType),
        ).firstOrNull { it.isExpired().not() }
            ?: return InvalidTokenException(reason = InvalidTokenReason.NOT_FOUND).left()

        return TokenDataWithExpiration(TokenData.of(foundEntity.typeValue, tokenType), foundEntity.expiration).right()
    }

    override fun retrieveNotExpiredTokenKey(
        accountId: AccountId,
        tokenType: TokenType,
    ): Either<InvalidTokenException, TokenKey> {
        val foundEntity = client.findBeginsWithOnSortKey(
            partitionKey = accountId.value,
            sortKey = buildScanKeyPrefix(tokenType),
        ).firstOrNull { it.isExpired().not() }
            ?: return InvalidTokenException(reason = InvalidTokenReason.NOT_FOUND).left()

        return TokenKey(accountId = accountId, value = foundEntity.token).right()
    }

    override fun retrieveValidated(
        tokenKey: TokenKey,
        tokenType: TokenType,
        errorLimit: Int,
    ): Either<InvalidTokenException, TokenDataWithExpiration> {
        return client.findBeginsWithOnSortKey(
            partitionKey = tokenKey.accountId.value,
            sortKey = buildScanKeyPrefix(tokenType),
        ).check(tokenKey, errorLimit).map { foundEntity ->
            TokenDataWithExpiration(TokenData.of(foundEntity.typeValue, tokenType), foundEntity.expiration)
        }
    }

    private fun List<TokenEntity>.check(
        tokenKey: TokenKey,
        errorLimit: Int,
    ): Either<InvalidTokenException, TokenEntity> {
        return try {
            this.ifEmpty {
                throw InvalidTokenException(reason = InvalidTokenReason.NOT_FOUND)
            }.filter {
                it.isExpired().not()
            }.ifEmpty {
                throw InvalidTokenException(reason = InvalidTokenReason.EXPIRED)
            }.filter {
                it.token == tokenKey.value
            }.ifEmpty {
                throw InvalidTokenException(reason = InvalidTokenReason.MISMATCH)
            }.filter {
                it.errorCount < errorLimit
            }.ifEmpty {
                throw InvalidTokenException(reason = InvalidTokenReason.MAX_ATTEMPTS)
            }.first().right()
        } catch (e: InvalidTokenException) {
            e.left()
        }
    }

    override fun delete(tokenKey: TokenKey, tokenData: TokenData) {
        client.delete(tokenKey.accountId.value, buildScanKey(tokenData))
    }

    override fun incrementErrorCount(accountId: AccountId): Int {
        return client.findBeginsWithOnSortKey(
            partitionKey = accountId.value,
            sortKey = tokenPrefix,
        )
            .filter { it.isExpired().not() }
            .ifEmpty { return 0 }
            .map { client.update(it).errorCount }
            .maxByOrNull { it } ?: 0
    }

    fun retrieveErrorCount(accountId: AccountId): Int {
        return client.findBeginsWithOnSortKey(
            partitionKey = accountId.value,
            sortKey = tokenPrefix,
        )
            .filter { it.isExpired().not() }
            .ifEmpty { return 0 }
            .first()
            .errorCount
    }

    private fun buildScanKey(tokenData: TokenData): String {
        return "${buildScanKeyPrefix(tokenData.type)}${tokenData.value}"
    }

    private fun buildScanKeyPrefix(type: TokenType): String {
        return "${tokenPrefix}${type.name}#"
    }
}

private fun TokenEntity.isExpired(): Boolean = expiration <= getZonedDateTime().toEpochSecond()

@DynamoDbBean
class TokenEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbAttribute(value = "Token")
    lateinit var token: String

    @get:DynamoDbAttribute(value = "AccountId")
    lateinit var accountId: String

    @get:DynamoDbAttribute(value = "TypeValue")
    lateinit var typeValue: String

    @get:DynamoDbAttribute(value = "ExpirationTTL")
    var expiration: Long = 0

    @get:DynamoDbAtomicCounter(startValue = 0, delta = 1)
    @get:DynamoDbAttribute(value = "ErrorCount")
    var errorCount: Int = 0
}