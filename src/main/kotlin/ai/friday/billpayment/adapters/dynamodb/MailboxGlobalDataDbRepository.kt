package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.adapters.parsers.parseListFrom
import ai.friday.billpayment.app.integrations.MailboxGlobalDataRepository
import jakarta.inject.Singleton

@Singleton
class MailboxGlobalDataDbRepository(private val globalDataDbRepository: GlobalDataDbRepository) :
    MailboxGlobalDataRepository {

    private val allowedListKey = "MAILBOX_ALLOWED_LIST"
    private val blockListKey = "MAILBOX_BLOCK_LIST"
    private val doNotDisturbListKey = "MAILBOX_DND_LIST"

    override fun saveAllowedList(list: List<String>) = save(allowedListKey, list)
    override fun loadAllowedList() = load(allowedListKey)

    override fun saveBlockList(list: List<String>) = save(blockListKey, list)
    override fun loadBlockList() = load(blockListKey)

    override fun saveDoNotDisturbList(list: List<String>) = save(doNotDisturbListKey, list)
    override fun loadDoNotDisturbList() = load(doNotDisturbListKey)

    private fun save(key: String, list: List<String>) =
        globalDataDbRepository.saveGlobalData(key, key, list)

    private fun load(key: String): List<String> =
        globalDataDbRepository.loadGlobalData(key, key)?.let {
            parseListFrom(it.value)
        } ?: emptyList()
}