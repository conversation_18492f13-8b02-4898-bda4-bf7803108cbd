package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FindError
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus.ACTIVE
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus.CLOSED
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus.INACTIVE
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus.PENDING
import ai.friday.billpayment.app.account.AccountPaymentStatus
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardBinDetails
import ai.friday.billpayment.app.account.CreditCardBrand
import ai.friday.billpayment.app.account.CreditCardConfiguration
import ai.friday.billpayment.app.account.CreditCardExternalId
import ai.friday.billpayment.app.account.CreditCardToken
import ai.friday.billpayment.app.account.DeleteCreditCardResult
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.account.ExternalPaymentMethod
import ai.friday.billpayment.app.account.LegacyAccountConfiguration
import ai.friday.billpayment.app.account.NotificationGateways
import ai.friday.billpayment.app.account.PartialAccount
import ai.friday.billpayment.app.account.PaymentMethodNotFound
import ai.friday.billpayment.app.account.PaymentMethodType.BALANCE
import ai.friday.billpayment.app.account.PaymentMethodType.CREDIT_CARD
import ai.friday.billpayment.app.account.PaymentMethodType.EXTERNAL
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.UpdateCreditCardMethodRiskResult
import ai.friday.billpayment.app.account.UpgradeStatus
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.getOrFalse
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BackOfficeAccountRepository
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.datamodeling.TransactionWriteRequest
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.extensions.annotations.DynamoDbAtomicCounter
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class AccountDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<AccountEntity>(cli, AccountEntity::class.java)

@Singleton
class PartialAccountDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<PartialAccountEntity>(cli, PartialAccountEntity::class.java)

@Singleton
class PaymentMethodDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<PaymentMethodEntity>(cli, PaymentMethodEntity::class.java)

@Singleton
class NSUDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<NSUEntity>(cli, NSUEntity::class.java)

val DEFAULT_LEGACY_ACCOUNT_CONFIGURATION =
    LegacyAccountConfiguration(
        creditCardConfiguration = CreditCardConfiguration(),
        defaultWalletId = null,
        receiveDDANotification = true,
        receiveNotification = true,
    )

private const val accountKey = "ACCOUNT"

private val statusUpdatedDateFormatter = DateTimeFormatter.ISO_DATE_TIME
private val index2RangeKeyDateFormatter = dateFormat

@Singleton
class AccountDbRepository(
    private val accountDAO: AccountDynamoDAO,
    private val partialAccountDAO: PartialAccountDynamoDAO,
    private val paymentMethodDAO: PaymentMethodDynamoDAO,
    private val nsuDAO: NSUDynamoDAO,
    private val transactionDynamo: TransactionDynamo,
) : AccountRepository, BackOfficeAccountRepository {

    private val balanceIndexPartitionKey = "BALANCE"

    private val paymentMethodScanKeyPrefix = "PAYMENT-METHOD#"

    private val partialAccountIndexKey = "PARTIAL_ACCOUNT"

    private val externalIdHashKey = "EXTERNAL_ID"

    override fun findAccountByDocument(document: String): Account {
        return findAccountByDocumentOrNull(document) ?: throw AccountNotFoundException(document)
    }

    override fun findAccountByDocumentOrNull(document: String): Account? {
        val userList = accountDAO.findByPartitionKeyOnIndex(GlobalSecondaryIndexNames.GSIndex1, document)
        val userEntity = userList.firstOrNull { entity ->
            entity.scanKey == Role.OWNER.name && (entity.status == AccountStatus.ACTIVE || entity.status == AccountStatus.APPROVED || entity.status == AccountStatus.BLOCKED)
        }
        return userEntity?.let { buildAccount(it) }
    }

    override fun listAccountsByDocument(document: String): List<Account> {
        return accountDAO.findByPartitionKeyOnIndex(GlobalSecondaryIndexNames.GSIndex1, document)
            .map { buildAccount(it) }
    }

    override fun findById(accountId: AccountId): Account {
        return findByIdOrNull(accountId) ?: throw AccountNotFoundException(accountId.value)
    }

    override fun findByIdOrNull(accountId: AccountId): Account? {
        val accountEntity = accountDAO.findByPrimaryKey(accountId.value, Role.OWNER.name)
        return accountEntity?.let { buildAccount(accountEntity) }
    }

    override fun findByGroup(group: AccountGroup): List<Account> {
        return findAllAccountsActivatedSince(LocalDate.EPOCH)
            .filter { it.configuration.groups.contains(group) }
    }

    override fun findPartialAccountById(accountId: AccountId): PartialAccount {
        return findPartialAccountByIdOrNull(accountId) ?: throw AccountNotFoundException(accountId.value)
    }

    override fun findPartialAccountByIdOrNull(accountId: AccountId): PartialAccount? {
        return partialAccountDAO.findByPrimaryKey(accountId.value, Role.GUEST.name)?.toPartialAccount()
    }

    override fun checkAccountRole(accountId: AccountId, role: Role): Role? {
        val accountEntity = accountDAO.findByPrimaryKey(accountId.value, role.name)
        return accountEntity?.let { role }
    }

    override suspend fun findNameAndEmail(
        accountId: AccountId,
        role: Role,
    ): Either<Exception, Pair<String, EmailAddress>> {
        val accountEntity = accountDAO.findByPrimaryKey(accountId.value, role.name)
            ?: return AccountNotFoundException(accountId.value).left()

        return Pair(accountEntity.name, EmailAddress(accountEntity.email)).right()
    }

    override fun findPartialAccountByStatus(accountStatus: AccountStatus): List<PartialAccount> {
        return partialAccountDAO.findByPartitionKeyAndScanKeyOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            partialAccountIndexKey,
            accountStatus.name,
        ).map { it.toPartialAccount() }
    }

    override fun updatePartialAccountRegistrationType(accountId: AccountId, registrationType: RegistrationType) {
        val existingEntity = partialAccountDAO.findByPrimaryKey(accountId.value, Role.GUEST.name)
        if (existingEntity != null) {
            existingEntity.registrationType = registrationType
            partialAccountDAO.save(existingEntity)
        }
    }

    override fun updatePartialAccountStatus(accountId: AccountId, newStatus: AccountStatus) {
        val existingEntity = partialAccountDAO.findByPrimaryKey(accountId.value, Role.GUEST.name)
        if (existingEntity != null) {
            existingEntity.status = newStatus
            existingEntity.indexHashKey = partialAccountIndexKey
            existingEntity.indexRangeKey = newStatus
            existingEntity.statusUpdated = getZonedDateTime().format(statusUpdatedDateFormatter)
            partialAccountDAO.save(existingEntity)
        }
    }

    override fun updateAccountStatus(accountId: AccountId, newStatus: AccountStatus) {
        val existingEntity = accountDAO.findByPrimaryKey(accountId.value, Role.OWNER.name)
        if (existingEntity != null) {
            existingEntity.status = newStatus
            accountDAO.save(existingEntity)
        }
    }

    override fun updatePartialAccountName(accountId: AccountId, newName: String) {
        val existingEntity = partialAccountDAO.findByPrimaryKey(accountId.value, Role.GUEST.name)
        if (existingEntity != null) {
            existingEntity.name = newName
            partialAccountDAO.save(existingEntity)
        }
    }

    override fun updatePartialAccountEmail(accountId: AccountId, newEmail: EmailAddress) {
        val existingEntity = partialAccountDAO.findByPrimaryKey(accountId.value, Role.GUEST.name)
        if (existingEntity != null) {
            existingEntity.email = newEmail.value
            partialAccountDAO.save(existingEntity)
        }
    }

    override fun updatePartialAccountGroups(accountId: AccountId, groups: List<AccountGroup>): PartialAccount {
        val existingEntity = partialAccountDAO.findByPrimaryKey(accountId.value, Role.GUEST.name)
        if (existingEntity != null) {
            existingEntity.groups = groups.map { it.value }
            partialAccountDAO.save(existingEntity)
        }
        return findPartialAccountById(accountId)
    }

    override fun deletePartialAccount(accountId: AccountId) {
        partialAccountDAO.delete(accountId.value, Role.GUEST.name)
    }

    override fun findPhysicalAccountPaymentMethod(
        bankNo: Long,
        routingNo: Long,
        accountNo: AccountNumber,
    ): Either<FindError, AccountPaymentMethod> {
        val balanceIndexScanKey = buildBalanceIndexScanKey(bankNo, routingNo, accountNo)
        val paymentList = paymentMethodDAO.findByPartitionKeyAndScanKeyOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            balanceIndexPartitionKey,
            balanceIndexScanKey,
        )
            .ifEmpty {
                return FindError.NotFound.left()
            }
        if (paymentList.size > 1) {
            return FindError.MultipleItemsFound.left()
        }
        return paymentList.first().toBankAccountPaymentMethod().right()
    }

    override fun deleteAccountPaymentMethod(
        accountId: AccountId,
        accountPaymentMethodId: AccountPaymentMethodId,
    ): DeleteCreditCardResult {
        try {
            val foundCreditCardEntity = paymentMethodDAO
                .findBeginsWithOnSortKey(
                    partitionKey = accountId.value,
                    sortKey = paymentMethodScanKeyPrefix,
                )
                .filter { it.paymentMethodId == accountPaymentMethodId.value && it.type == CREDIT_CARD }
                .ifEmpty { return DeleteCreditCardResult.NotFound }
                .first()

            if (foundCreditCardEntity.status !in listOf(ACTIVE, PENDING)) {
                return DeleteCreditCardResult.Deleted(accountPaymentMethodId)
            }

            val (prefix, _, position) = foundCreditCardEntity.scanKey.split("#")

            val updatedPaymentMethod = PaymentMethodEntity().apply {
                primaryKey = accountId.value
                scanKey = "$prefix#${INACTIVE.name}#$position"
                this.paymentMethodId = foundCreditCardEntity.paymentMethodId
                this.status = INACTIVE
                created = foundCreditCardEntity.created
                type = CREDIT_CARD
                this.creditCard = foundCreditCardEntity.creditCard
            }

            transactionDynamo.transactionWrite(
                paymentMethodDAO.transactionWriteDelete(foundCreditCardEntity),
                paymentMethodDAO.transactionWritePut(updatedPaymentMethod),
            )

            return DeleteCreditCardResult.Deleted(accountPaymentMethodId)
        } catch (e: Exception) {
            return DeleteCreditCardResult.ServerError(e)
        }
    }

    override fun updateCreditCardPaymentMethodRisk(accountId: AccountId, accountPaymentMethodId: AccountPaymentMethodId, riskLevel: RiskLevel): UpdateCreditCardMethodRiskResult {
        val foundCreditCardEntity = paymentMethodDAO
            .findBeginsWithOnSortKey(
                partitionKey = accountId.value,
                sortKey = paymentMethodScanKeyPrefix,
            )
            .filter { it.paymentMethodId == accountPaymentMethodId.value && it.type == CREDIT_CARD }
            .ifEmpty { return UpdateCreditCardMethodRiskResult.NotFound }
            .first()

        foundCreditCardEntity.creditCard?.riskLevel = riskLevel
        paymentMethodDAO.save(foundCreditCardEntity)

        return UpdateCreditCardMethodRiskResult.Updated(accountPaymentMethodId)
    }

    override fun createAccountPaymentMethod(
        accountId: AccountId,
        bankAccount: BankAccount,
        position: Int,
        mode: BankAccountMode,
    ): AccountPaymentMethod {
        return createAccountPaymentMethod(
            accountId = accountId,
            bankAccount = bankAccount,
            position = position,
            paymentMethodId = AccountPaymentMethodId(UUID.randomUUID().toString()),
            mode = mode,
        )
    }

    override fun createAccountPaymentMethod(
        accountId: AccountId,
        paymentMethodId: AccountPaymentMethodId,
        bankAccount: BankAccount,
        position: Int,
        status: AccountPaymentMethodStatus,
        mode: BankAccountMode,
    ): AccountPaymentMethod {
        val paymentMethod = PaymentMethodEntity().apply {
            primaryKey = accountId.value
            scanKey = "$paymentMethodScanKeyPrefix${status.name}#$position"
            this.paymentMethodId = paymentMethodId.value
            this.status = status
            created = getFormattedZonedDateTime()
            type = BALANCE
            typeIndex = BALANCE
            paymentIdIndex = buildBalanceIndexScanKey(bankAccount)
            this.bankAccount = BankAccountComplexType().apply {
                bankNo = bankAccount.bankNo!!
                bankRoutingNo = bankAccount.routingNo
                bankAccountNo = bankAccount.accountNo.toLong() // TODO: revisar esse caso
                bankAccountType = bankAccount.accountType.name
                bankAccountDv = bankAccount.accountDv
                document = bankAccount.document
                bankAccountMode = mode
            }
        }

        paymentMethodDAO.save(paymentMethod)

        return paymentMethod.toBankAccountPaymentMethod()
    }

    override fun createAccountPaymentMethod(
        accountId: AccountId,
        paymentMethodId: AccountPaymentMethodId,
        creditCard: CreditCard,
        position: Int,
        status: AccountPaymentMethodStatus,
    ): AccountPaymentMethod {
        val now = getFormattedZonedDateTime()

        val activatedAt = if (status == ACTIVE) {
            now
        } else {
            null
        }

        val paymentMethod = PaymentMethodEntity().apply {
            primaryKey = accountId.value
            scanKey = "$paymentMethodScanKeyPrefix${status.name}#$position"
            hmac = creditCard.hmac
            hmacIndex = "CREDIT_CARD#HMAC"
            this.paymentMethodId = paymentMethodId.value
            this.status = status
            created = now
            this.activatedAt = activatedAt
            type = CREDIT_CARD
            this.creditCard = CreditCardComplexType().apply {
                creditCardBrand = creditCard.brand.name
                creditCardPAN = creditCard.maskedPan
                creditCardExpiryDate = creditCard.expiryDate
                token = creditCard.token?.value
                provider = creditCard.binDetails?.provider
                cardType = creditCard.binDetails?.cardType
                foreignCard = creditCard.binDetails?.foreignCard
                corporateCard = creditCard.binDetails?.corporateCard
                issuer = creditCard.binDetails?.issuer
                issuerCode = creditCard.binDetails?.issuerCode
                prepaid = creditCard.binDetails?.prepaid
                this.status = creditCard.binDetails?.status
                bin = creditCard.bin
                riskLevel = creditCard.riskLevel
                mainCard = creditCard.mainCard
                lastFourDigits = creditCard.lastFourDigits
                externalId = creditCard.externalId?.value
                externalProvider = creditCard.externalId?.provider?.name
                hmac = creditCard.hmac
            }
        }

        paymentMethodDAO.save(paymentMethod)

        return paymentMethod.toCreditCardPaymentMethod()
    }

    override fun createExternalAccountPaymentMethod(
        accountId: AccountId,
        providerName: AccountProviderName,
        position: Int,
    ): AccountPaymentMethod {
        val paymentMethodId = UUID.randomUUID().toString()

        val paymentMethod = PaymentMethodEntity().apply {
            primaryKey = accountId.value
            scanKey = "$paymentMethodScanKeyPrefix${ACTIVE.name}#$position"
            this.paymentMethodId = paymentMethodId
            this.status = ACTIVE
            created = getFormattedZonedDateTime()
            type = EXTERNAL
            this.providerName = providerName
        }

        paymentMethodDAO.save(paymentMethod)

        return paymentMethod.toExternalPaymentMethod()
    }

    override fun activateAccountPaymentMethod(
        accountId: AccountId,
        accountPaymentMethodId: AccountPaymentMethodId,
    ) {
        val paymentMethodEntity =
            findPaymentMethodEntitiesByAccountIdAndStatus(accountId = accountId, status = null)
                .ifEmpty { return }
                .single { it.paymentMethodId == accountPaymentMethodId.value }

        val (prefix, _, position) = paymentMethodEntity.scanKey.split("#")

        val updatedMethodEntity = PaymentMethodEntity().apply {
            primaryKey = paymentMethodEntity.primaryKey
            scanKey = "$prefix#${ACTIVE.name}#$position"
            paymentMethodId = paymentMethodEntity.paymentMethodId
            created = paymentMethodEntity.created
            type = paymentMethodEntity.type
            creditCard = paymentMethodEntity.creditCard
            status = ACTIVE
            activatedAt = getFormattedZonedDateTime()
        }

        val transactionWriteRequest = TransactionWriteRequest()

        transactionWriteRequest.addDelete(paymentMethodEntity)
        transactionWriteRequest.addPut(updatedMethodEntity)

        transactionDynamo.transactionWrite(
            paymentMethodDAO.transactionWriteDelete(paymentMethodEntity),
            paymentMethodDAO.transactionWritePut(updatedMethodEntity),
        )
    }

    override fun closeCreditCards(accountId: AccountId) {
        this.findPaymentMethodEntitiesByAccountIdAndStatus(accountId = accountId, status = null)
            .forEach {
                if (it.type == CREDIT_CARD && it.status != CLOSED) {
                    closeAccountPaymentMethod(it)
                }
            }
    }

    override fun closeAccountPaymentMethod(
        accountPaymentMethodId: AccountPaymentMethodId,
        accountId: AccountId,
    ) {
        this.findPaymentMethodEntitiesByAccountIdAndStatus(accountId = accountId, status = null)
            .filter {
                it.paymentMethodId == accountPaymentMethodId.value
            }
            .forEach {
                closeAccountPaymentMethod(it)
            }
    }

    override fun setAccountPaymentMethodStatus(accountPaymentMethodId: AccountPaymentMethodId, accountId: AccountId, status: AccountPaymentMethodStatus) {
        this.findPaymentMethodEntitiesByAccountIdAndStatus(accountId = accountId, status = null)
            .filter {
                it.paymentMethodId == accountPaymentMethodId.value
            }
            .forEach {
                setPaymentMethodStatus(it, status)
            }
    }

    private fun closeAccountPaymentMethod(paymentMethodEntity: PaymentMethodEntity) {
        setPaymentMethodStatus(paymentMethodEntity, CLOSED)
    }

    private fun setPaymentMethodStatus(paymentMethodEntity: PaymentMethodEntity, newStatus: AccountPaymentMethodStatus) {
        paymentMethodDAO.delete(paymentMethodEntity.primaryKey, paymentMethodEntity.scanKey)
        paymentMethodEntity.apply {
            status = newStatus
            scanKey = "$paymentMethodScanKeyPrefix${newStatus.name}#${paymentMethodEntity.scanKey.split("#").last()}"
        }
        paymentMethodDAO.save(paymentMethodEntity)
    }

    override fun create(
        username: String,
        emailAddress: EmailAddress,
        accountId: AccountId,
        registrationType: RegistrationType,
        groups: List<AccountGroup>,
        subscriptionType: SubscriptionType,
    ): PartialAccount {
        val entity = PartialAccountEntity().apply {
            primaryKey = accountId.value
            scanKey = Role.GUEST
            status = AccountStatus.REGISTER_INCOMPLETE
            statusUpdated = getZonedDateTime().format(statusUpdatedDateFormatter)
            email = emailAddress.value
            name = username
            indexHashKey = partialAccountIndexKey
            indexRangeKey = AccountStatus.REGISTER_INCOMPLETE
            this.groups = groups.map { it.value }
            this.registrationType = registrationType
            this.subscriptionType = subscriptionType
        }
        partialAccountDAO.save(entity)
        return entity.toPartialAccount()
    }

    override fun create(userAccount: Account): Account {
        val now = getZonedDateTime()
        val accountEntity = AccountEntity().apply {
            primaryKey = userAccount.accountId.value
            scanKey = Role.OWNER.name
            documentHash = userAccount.document
            index2HashKey = accountKey
            index2RangeKey = buildIndex2RangeKey(now)
            index3HashKey = userAccount.configuration.externalId?.let { buildExternalIdHashKey(it) }
            index3RangeKey = userAccount.configuration.externalId?.value
            status = AccountStatus.ACTIVE
            email = userAccount.emailAddress.value
            document = userAccount.document
            documentType = userAccount.documentType
            mobilePhone = userAccount.mobilePhone
            created = now.format(DateTimeFormatter.ISO_INSTANT)
            activatedAt = userAccount.activated?.format(DateTimeFormatter.ISO_INSTANT)
            name = userAccount.name
            userAccountType = userAccount.type
            updatedAt = now.format(DateTimeFormatter.ISO_DATE_TIME)
            configuration = userAccount.configuration.toEntity()
            imageUrlSmall = userAccount.imageUrlSmall
            imageUrlLarge = userAccount.imageUrlLarge
            subscriptionType = userAccount.subscriptionType
        }
        accountDAO.save(accountEntity)

        return buildAccount(accountEntity)
    }

    override fun save(account: Account) {
        val accountEntity = AccountEntity().apply {
            primaryKey = account.accountId.value
            scanKey = Role.OWNER.name
            documentHash = account.document
            status = account.status
            upgradeStatus = account.upgradeStatus
            paymentStatus = account.paymentStatus
            index2HashKey = accountKey
            index2RangeKey = buildIndex2RangeKey(account.created)
            index3HashKey = account.configuration.externalId?.let { buildExternalIdHashKey(it) }
            index3RangeKey = account.configuration.externalId?.let { it.value }
            index4HashKey = account.upgradeStatus?.let { "UPGRADE_STATUS" }
            index4RangeKey = account.upgradeStatus
            email = account.emailAddress.value
            document = account.document
            documentType = account.documentType
            mobilePhone = account.mobilePhone
            created = account.created.format(DateTimeFormatter.ISO_INSTANT)
            name = account.name
            userAccountType = account.type
            updatedAt = account.updated.format(DateTimeFormatter.ISO_DATE_TIME)
            activatedAt = account.activated?.format(DateTimeFormatter.ISO_DATE_TIME)
            configuration = account.configuration.toEntity()
            firstLoginAsOwner = account.firstLoginAsOwner?.format(DateTimeFormatter.ISO_DATE_TIME)
            channel = account.channel
            imageUrlSmall = account.imageUrlSmall
            imageUrlLarge = account.imageUrlLarge
            subscriptionType = account.subscriptionType
        }
        accountDAO.save(accountEntity)
    }

    override fun findAllAccountsActivatedSince(
        date: LocalDate,
        filterAccountsThatReceiveNotification: Boolean,
    ): List<Account> {
        return accountDAO.findByPrimaryKeyGreaterThanOrEqualByIndex(
            GlobalSecondaryIndexNames.GSIndex2,
            accountKey,
            date.format(index2RangeKeyDateFormatter),
        )
            .filter {
                !filterAccountsThatReceiveNotification || it.configuration?.receiveNotification == true
            }
            .map {
                buildAccount(it)
            }
    }

    override fun findAccountByUpgradeStatus(status: UpgradeStatus): List<Account> {
        return accountDAO.findByPartitionKeyAndScanKeyOnIndex(
            GlobalSecondaryIndexNames.GSIndex4,
            "UPGRADE_STATUS",
            status.name,
        ).map { buildAccount(it) }
    }

    override fun findByExternalId(externalId: ExternalId): List<Account> {
        return accountDAO.findByPartitionKeyAndScanKeyOnIndex(
            GlobalSecondaryIndexNames.GSIndex3,
            buildExternalIdHashKey(externalId),
            externalId.value,
        ).map { buildAccount(it) }
    }

    override fun findAccountPaymentMethodByIdAndAccountId(
        accountPaymentMethodId: AccountPaymentMethodId,
        accountId: AccountId,
        status: AccountPaymentMethodStatus?,
    ): AccountPaymentMethod {
        return findAccountPaymentMethodsByAccountIdAndStatus(accountId, status)
            .filter { paymentMethod ->
                paymentMethod.id == accountPaymentMethodId
            }
            .ifEmpty { throw PaymentMethodNotFound(accountPaymentMethodId, accountId) }
            .first()
    }

    override fun findAllPhysicalBalanceAccount(): List<AccountPaymentMethod> {
        return findAllBankAccountByMode(BankAccountMode.PHYSICAL)
    }

    // FIXME walletId - separar metodos de pagamento da carteira (balance) dos metodos de pagamento do usuário (cartao de credito) ???
    override fun findPhysicalBankAccountByAccountId(accountId: AccountId): List<AccountPaymentMethod> {
        return findAccountPaymentMethodsByAccountIdAndStatus(accountId, ACTIVE)
            .filter { accountPaymentMethod -> accountPaymentMethod.method is InternalBankAccount && accountPaymentMethod.method.bankAccountMode == BankAccountMode.PHYSICAL }
            .ifEmpty { throw PaymentMethodNotFound(accountId) }
    }

    override fun findAccountIdByPhysicalBankAccountNo(
        bankNo: Long,
        routingNo: Long,
        accountNo: AccountNumber,
    ): AccountId {
        val primaryKey = paymentMethodDAO.findByPartitionKeyAndScanKeyOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            balanceIndexPartitionKey,
            buildBalanceIndexScanKey(bankNo, routingNo, accountNo),
        ).single().primaryKey

        return AccountId(primaryKey)
    }

    override fun findVirtualBankAccountByDocument(document: String): Either<FindError, AccountPaymentMethod> {
        try {
            val accountPaymentMethodsEntities =
                paymentMethodDAO.findByPartitionKeyOnIndex(GlobalSecondaryIndexNames.GSIndex1, balanceIndexPartitionKey)
                    .filter { it.bankAccount?.bankAccountMode == BankAccountMode.VIRTUAL && it.bankAccount?.document == document }
                    .ifEmpty { return FindError.NotFound.left() }
            if (accountPaymentMethodsEntities.size > 1) {
                return FindError.MultipleItemsFound.left()
            }
            return accountPaymentMethodsEntities.first().toBankAccountPaymentMethod().right()
        } catch (e: Exception) {
            return FindError.ServerError(e).left()
        }
    }

    override fun findAllVirtualBankAccount(): List<AccountPaymentMethod> {
        return findAllBankAccountByMode(BankAccountMode.VIRTUAL)
    }

    override fun findAccountPaymentMethodsByAccountId(accountId: AccountId): List<AccountPaymentMethod> {
        return findAccountPaymentMethodsByAccountIdAndStatus(accountId = accountId, status = null)
    }

    override fun findAccountPaymentMethodsByHMac(hmac: String): List<AccountPaymentMethod> {
        val paymentMethodEntities = paymentMethodDAO.findByPartitionKeyAndScanKeyOnIndex(
            GlobalSecondaryIndexNames.GSIndex2,
            hmac,
            "CREDIT_CARD#HMAC",
        )
        return paymentMethodEntities.map { paymentMethodEntity ->
            try {
                when (paymentMethodEntity.type) {
                    CREDIT_CARD -> paymentMethodEntity.toCreditCardPaymentMethod()
                    else -> throw IllegalStateException("Invalid payment method type")
                }
            } catch (e: Exception) {
                logger.error(
                    Markers.append("hmac", hmac)
                        .andAppend("paymentMethodEntity", paymentMethodEntity),
                    "AccountDbRepository",
                    e,
                )
                throw e
            }
        }
    }

    override fun findCreditCardsByAccountIdAndStatus(
        accountId: AccountId,
        status: AccountPaymentMethodStatus?,
    ): List<AccountPaymentMethod> {
        return findAccountPaymentMethodsByAccountIdAndStatus(
            accountId = accountId,
            status = status,
        ).filter {
            it.method.type == CREDIT_CARD
        }
    }

    override fun incrementNSU(accountId: AccountId): Int {
        nsuDAO.findByPrimaryKey(accountId.value, "NSU")?.let {
            return nsuDAO.update(it).nsu
        }

        nsuDAO.save(
            NSUEntity().apply {
                partitionKey = accountId.value
                sortKey = "NSU"
                nsu = 1
            },
        )
        return 1
    }

    private fun buildIndex2RangeKey(creationDate: ZonedDateTime) = creationDate.format(index2RangeKeyDateFormatter)

    private fun findAllBankAccountByMode(bankAccountMode: BankAccountMode): List<AccountPaymentMethod> {
        return paymentMethodDAO.findByPartitionKeyOnIndex(GlobalSecondaryIndexNames.GSIndex1, balanceIndexPartitionKey)
            .filter { it.bankAccount?.bankAccountMode == bankAccountMode }
            .map { balanceEntity -> balanceEntity.toBankAccountPaymentMethod() }
    }

    private fun findPaymentMethodEntitiesByAccountIdAndStatus(
        accountId: AccountId,
        status: AccountPaymentMethodStatus?,
    ): List<PaymentMethodEntity> {
        val scanKey = if (status == null) {
            paymentMethodScanKeyPrefix
        } else {
            "$paymentMethodScanKeyPrefix${status.name}"
        }

        return paymentMethodDAO.findBeginsWithOnSortKey(
            partitionKey = accountId.value,
            sortKey = scanKey,
        )
    }

    // FIXME walletId - separar metodos de pagamento da carteira (balance) dos metodos de pagamento do usuário (cartao de credito) ???
    override fun findAccountPaymentMethodsByAccountIdAndStatus(
        accountId: AccountId,
        status: AccountPaymentMethodStatus?,
    ): List<AccountPaymentMethod> {
        val paymentMethodEntities = findPaymentMethodEntitiesByAccountIdAndStatus(accountId, status)
        return paymentMethodEntities.map { paymentMethodEntity ->
            try {
                when (paymentMethodEntity.type) {
                    CREDIT_CARD -> paymentMethodEntity.toCreditCardPaymentMethod()
                    BALANCE -> paymentMethodEntity.toBankAccountPaymentMethod()
                    EXTERNAL -> paymentMethodEntity.toExternalPaymentMethod()
                }
            } catch (e: Exception) {
                logger.error(
                    Markers.append("accountId", accountId.value).andAppend("status", status)
                        .andAppend("paymentMethodEntity", paymentMethodEntity),
                    "AccountDbRepository",
                    e,
                )
                throw e
            }
        }
    }

    private fun buildAccount(accountEntity: AccountEntity): Account {
        return Account(
            accountId = AccountId(accountEntity.primaryKey),
            document = accountEntity.document,
            documentType = accountEntity.documentType,
            emailAddress = EmailAddress(accountEntity.email),
            mobilePhone = accountEntity.mobilePhone,
            name = accountEntity.name,
            type = accountEntity.userAccountType ?: UserAccountType.FULL_ACCOUNT,
            created = ZonedDateTime.parse(accountEntity.created, DateTimeFormatter.ISO_DATE_TIME)
                .withZoneSameInstant(brazilTimeZone),
            activated = accountEntity.activatedAt?.let {
                ZonedDateTime.parse(it, DateTimeFormatter.ISO_DATE_TIME)
                    .withZoneSameInstant(brazilTimeZone)
            },
            status = accountEntity.status,
            upgradeStatus = accountEntity.upgradeStatus,
            paymentStatus = accountEntity.paymentStatus ?: AccountPaymentStatus.UpToDate,
            configuration = accountEntity.configuration?.let { configEntity ->
                LegacyAccountConfiguration(
                    creditCardConfiguration =
                    CreditCardConfiguration(
                        quota = configEntity.creditCardQuota,
                        allowedRisk = configEntity.creditCardRiskAllowed,
                    ),
                    defaultWalletId = configEntity.defaultWalletId?.let { WalletId(it) },
                    receiveDDANotification = configEntity.receiveDDANotification,
                    receiveNotification = configEntity.receiveNotification,
                    refreshToken = configEntity.refreshToken,
                    accessToken = configEntity.accessToken,
                    groups = configEntity.groups.mapNotNull { AccountGroup.find(it) },
                    externalId = configEntity.externalId?.let {
                        ExternalId(
                            value = it,
                            providerName = AccountProviderName.valueOf(configEntity.externalProvider!!),
                        )
                    },
                    notificationGateway = configEntity.notificationGateway?.let {
                        NotificationGateways.valueOf(it)
                    } ?: NotificationGateways.WHATSAPP,
                )
            } ?: DEFAULT_LEGACY_ACCOUNT_CONFIGURATION,
            firstLoginAsOwner = accountEntity.firstLoginAsOwner?.let { firstLoginAsOwner ->
                ZonedDateTime.parse(
                    firstLoginAsOwner,
                    DateTimeFormatter.ISO_DATE_TIME,
                ).withZoneSameInstant(brazilTimeZone)
            },
            channel = accountEntity.channel,
            imageUrlSmall = accountEntity.imageUrlSmall,
            imageUrlLarge = accountEntity.imageUrlLarge,
            subscriptionType = accountEntity.subscriptionType,
            updated = ZonedDateTime.parse(accountEntity.updatedAt, DateTimeFormatter.ISO_DATE_TIME)
                .withZoneSameInstant(brazilTimeZone),
        )
    }

    private fun buildBalanceIndexScanKey(bankNo: Long, routingNo: Long, accountNo: AccountNumber) =
        "$bankNo#$routingNo#${accountNo.fullAccountNumber}"

    private fun buildBalanceIndexScanKey(bankAccount: BankAccount) = buildBalanceIndexScanKey(
        bankAccount.bankNo!!,
        bankAccount.routingNo,
        AccountNumber(bankAccount.accountNo, bankAccount.accountDv),
    )

    private fun LegacyAccountConfiguration.toEntity(): AccountConfigurationEntity {
        return AccountConfigurationEntity().also {
            it.creditCardQuota = this.creditCardConfiguration.quota
            it.creditCardRiskAllowed = this.creditCardConfiguration.allowedRisk
            it.defaultWalletId = this.defaultWalletId?.value
            it.receiveDDANotification = this.receiveDDANotification
            it.receiveNotification = this.receiveNotification
            it.refreshToken = this.refreshToken
            it.accessToken = this.accessToken
            it.groups = this.groups.map { group -> group.value }
            it.externalId = this.externalId?.value
            it.externalProvider = this.externalId?.providerName?.name
            it.notificationGateway = this.notificationGateway.name
        }
    }

    private fun buildExternalIdHashKey(externalId: ExternalId) = "$externalIdHashKey#${externalId.providerName}"

    override fun saveCreditCardBin(accountId: AccountId, id: AccountPaymentMethodId, bin: String) {
        val paymentMethodEntity =
            findPaymentMethodEntitiesByAccountIdAndStatus(accountId = accountId, status = null)
                .ifEmpty { return }
                .single { it.paymentMethodId == id.value }

        if (paymentMethodEntity.creditCard?.bin != null) {
            return
        }

        if (paymentMethodEntity.status != ACTIVE) {
            return
        }

        val updatedMethodEntity = PaymentMethodEntity().apply {
            primaryKey = paymentMethodEntity.primaryKey
            scanKey = paymentMethodEntity.scanKey
            paymentMethodId = paymentMethodEntity.paymentMethodId
            created = paymentMethodEntity.created
            type = paymentMethodEntity.type
            creditCard = paymentMethodEntity.creditCard
            status = ACTIVE
            activatedAt = getFormattedZonedDateTime()
            this.creditCard!!.bin = bin
        }

        paymentMethodDAO.update(updatedMethodEntity)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(AccountDbRepository::class.java)
    }
}

@DynamoDbBean
class PartialAccountEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: Role

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: AccountStatus

    @get:DynamoDbAttribute(value = "Email")
    lateinit var email: String

    @get:DynamoDbAttribute(value = "Name")
    var name: String = ""

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    var indexHashKey: String? = null

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    var indexRangeKey: AccountStatus? = null

    @get:DynamoDbAttribute(value = "StatusUpdated")
    var statusUpdated: String? = null

    @get:DynamoDbAttribute(value = "Groups")
    var groups: List<String> = emptyList()

    @get:DynamoDbAttribute(value = "RegistrationType")
    var registrationType: RegistrationType = RegistrationType.FULL

    @get:DynamoDbAttribute(value = "SubscriptionType")
    var subscriptionType: SubscriptionType = SubscriptionType.PIX

    fun toPartialAccount() =
        PartialAccount(
            id = AccountId(primaryKey),
            status = status,
            emailAddress = EmailAddress(email),
            name = name,
            role = scanKey,
            statusUpdated = statusUpdated?.let { ZonedDateTime.parse(it, statusUpdatedDateFormatter) },
            groups = groups.mapNotNull { AccountGroup.find(it) },
            registrationType = registrationType,
            subscriptionType = subscriptionType,
        )
}

@DynamoDbBean
class AccountEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var documentHash: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var status: AccountStatus

    @get:DynamoDbAttribute(value = "UpgradeStatus")
    var upgradeStatus: UpgradeStatus? = null

    @get:DynamoDbAttribute(value = "PaymentStatus")
    var paymentStatus: AccountPaymentStatus? = null

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    var index2HashKey: String? = null

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    var index2RangeKey: String? = null

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_3])
    @get:DynamoDbAttribute(value = "GSIndex3PrimaryKey")
    var index3HashKey: String? = null

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_3])
    @get:DynamoDbAttribute(value = "GSIndex3ScanKey")
    var index3RangeKey: String? = null

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_4])
    @get:DynamoDbAttribute(value = "GSIndex4PrimaryKey")
    var index4HashKey: String? = null

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_4])
    @get:DynamoDbAttribute(value = "GSIndex4ScanKey")
    var index4RangeKey: UpgradeStatus? = null

    @get:DynamoDbAttribute(value = "Email")
    lateinit var email: String

    @get:DynamoDbAttribute(value = "Document")
    lateinit var document: String

    @get:DynamoDbAttribute(value = "DocumentType")
    lateinit var documentType: String

    @get:DynamoDbAttribute(value = "MobilePhone")
    lateinit var mobilePhone: String

    @get:DynamoDbAttribute(value = "Created")
    lateinit var created: String

    @get:DynamoDbAttribute(value = "Name")
    lateinit var name: String

    @get:DynamoDbAttribute(value = "UserAccountType")
    var userAccountType: UserAccountType? = null

    @get:DynamoDbAttribute(value = "UpdatedAt")
    var updatedAt: String = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)

    @get:DynamoDbAttribute(value = "ActivatedAt")
    var activatedAt: String? = null

    @get:DynamoDbAttribute(value = "Configuration")
    var configuration: AccountConfigurationEntity? = null

    @get:DynamoDbAttribute(value = "FirstLoginAsOwner")
    var firstLoginAsOwner: String? = null

    @get:DynamoDbAttribute(value = "Channel")
    var channel: String? = null

    @get:DynamoDbAttribute(value = "ImageUrlSmall")
    var imageUrlSmall: String? = null

    @get:DynamoDbAttribute(value = "ImageUrlLarge")
    var imageUrlLarge: String? = null

    @get:DynamoDbAttribute(value = "SubscriptionType")
    var subscriptionType: SubscriptionType = SubscriptionType.PIX
}

@DynamoDbBean
class AccountConfigurationEntity {
    var creditCardQuota: Long = 0
    var creditCardRiskAllowed: RiskLevel = RiskLevel.LOW
    var defaultWalletId: String? = null
    var receiveDDANotification: Boolean = true
    var receiveNotification: Boolean = true
    var refreshToken: String? = null
    var accessToken: String? = null
    var externalId: String? = null
    var externalProvider: String? = null
    var groups: List<String> = emptyList()
    var notificationGateway: String? = null
}

@DynamoDbBean
class NSUEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String // ACCOUNT_ID

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String // "NSU"

    @get:DynamoDbAtomicCounter(startValue = 1, delta = 1)
    @get:DynamoDbAttribute(value = "NSU")
    var nsu: Int = 1
}

private fun BankAccountComplexType.toInternalBankAccount() =
    InternalBankAccount(
        accountType = AccountType.valueOf(bankAccountType),
        bankNo = bankNo,
        routingNo = bankRoutingNo,
        accountNo = bankAccountNo,
        accountDv = bankAccountDv,
        document = document,
        bankAccountMode = bankAccountMode,
    )

private fun PaymentMethodEntity.toBankAccountPaymentMethod() =
    AccountPaymentMethod(
        id = AccountPaymentMethodId(paymentMethodId),
        accountId = AccountId(primaryKey),
        method = bankAccount!!.toInternalBankAccount(),
        status = status,
        created = convertToZonedDateTime(created),
    )

private fun PaymentMethodEntity.toExternalPaymentMethod() =
    AccountPaymentMethod(
        id = AccountPaymentMethodId(paymentMethodId),
        accountId = AccountId(primaryKey),
        method = ExternalPaymentMethod(providerName!!),
        status = status,
        created = convertToZonedDateTime(created),
    )

private fun PaymentMethodEntity.toCreditCardPaymentMethod() =
    AccountPaymentMethod(
        id = AccountPaymentMethodId(paymentMethodId),
        accountId = AccountId(primaryKey),
        method = creditCard!!.toCreditCard(),
        status = status,
        created = convertToZonedDateTime(created),
    )

private fun CreditCardComplexType.toCreditCard(): CreditCard {
    val binDetails = provider?.let { provider ->
        CreditCardBinDetails(
            provider = provider,
            cardType = cardType.orEmpty(),
            foreignCard = foreignCard.getOrFalse(),
            corporateCard = corporateCard.orEmpty(),
            issuer = issuer.orEmpty(),
            issuerCode = issuerCode.orEmpty(),
            prepaid = prepaid.orEmpty(),
            status = status.orEmpty(),
        )
    }

    return CreditCard(
        brand = CreditCardBrand.find(creditCardBrand),
        pan = bin?.let { it + creditCardPAN.drop(8) } ?: creditCardPAN,
        expiryDate = creditCardExpiryDate,
        token = token?.let { CreditCardToken(it) },
        binDetails = binDetails,
        riskLevel = riskLevel ?: RiskLevel.LOW,
        mainCard = mainCard,
        hmac = hmac,
        externalId = when {
            externalId.isNullOrEmpty() || !AccountProviderName.entries.any { it.name == externalProvider } -> null

            else -> CreditCardExternalId(
                value = externalId!!,
                provider = AccountProviderName.valueOf(externalProvider!!),
            )
        },
    )
}

fun convertToZonedDateTime(dateOrDateTime: String): ZonedDateTime? {
    val dateTimeFormatters =
        listOf(DateTimeFormatter.ISO_DATE_TIME, dateTimeFormat)

    val dateFormatters =
        listOf(dateFormat)
    dateTimeFormatters.forEach { formatter ->
        try {
            return ZonedDateTime.parse(dateOrDateTime, formatter)
        } catch (e: Exception) {
            // Não há o que fazer
        }
    }

    dateFormatters.forEach { formatter ->
        try {
            return LocalDate.parse(dateOrDateTime, formatter).atStartOfDay(brazilTimeZone)
        } catch (e: Exception) {
            // Não há o que fazer
        }
    }
    return null
}

private fun getFormattedZonedDateTime() = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)