package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionAccessConcession
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionAccessConcessionId
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionAccessConcessionRepository
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionReason
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStore
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getEpochMilli
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val PartitionKeyPrefix = "IN_APP_SUBSCRIPTION_ACCESS_CONCESSION"
private const val Index1PrimaryKey = "IN_APP_SUBSCRIPTION_ACCESS_CONCESSION_ENDS_AT"
private const val Index2PrimaryKeyPrefix = "DEDUPLICATION_ID#"

@Singleton
class InAppSubscriptionAccessConcessionDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<InAppSubscriptionAccessConcessionEntity>(cli, InAppSubscriptionAccessConcessionEntity::class.java)

@Singleton
class InAppSubscriptionAccessConcessionDbRepository(
    private val client: InAppSubscriptionAccessConcessionDynamoDAO,
) : InAppSubscriptionAccessConcessionRepository {
    override fun save(inAppSubscriptionAccessConcession: InAppSubscriptionAccessConcession) {
        val entity =
            InAppSubscriptionAccessConcessionEntity().apply {
                primaryKey = "$PartitionKeyPrefix#${inAppSubscriptionAccessConcession.accountId.value}"
                scanKey = "${getEpochMilli()}"
                createdAt = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
                gSIndex1PrimaryKey = Index1PrimaryKey
                gSIndex1ScanKey = inAppSubscriptionAccessConcession.endsAt.format(DateTimeFormatter.ISO_DATE_TIME)
                accountId = inAppSubscriptionAccessConcession.accountId.value
                reason = inAppSubscriptionAccessConcession.reason
                endsAt = inAppSubscriptionAccessConcession.endsAt.format(DateTimeFormatter.ISO_DATE_TIME)
                store = inAppSubscriptionAccessConcession.store?.name
                id = inAppSubscriptionAccessConcession.id.value
                transfer = inAppSubscriptionAccessConcession.isTransfer
                originConcessionId = inAppSubscriptionAccessConcession.originConcessionId?.value
                gSIndex2PrimaryKey = inAppSubscriptionAccessConcession.deduplicationId?.let { "$Index2PrimaryKeyPrefix$it" }
                gSIndex2ScanKey = inAppSubscriptionAccessConcession.deduplicationId
                deduplicationId = inAppSubscriptionAccessConcession.deduplicationId
            }
        client.save(entity)
    }

    override fun findLastByAccountId(accountId: AccountId): InAppSubscriptionAccessConcession? =
        client
            .findByPartitionKey(
                partitionKey = "$PartitionKeyPrefix#${accountId.value}",
            ).maxByOrNull { it.scanKey }
            ?.toInAppSubscriptionAccessConcession()

    override fun findAllByAccountId(accountId: AccountId): List<InAppSubscriptionAccessConcession> =
        client
            .findByPartitionKey(
                partitionKey = "$PartitionKeyPrefix#${accountId.value}",
            ).map { it.toInAppSubscriptionAccessConcession() }

    override fun findAllByDeduplicationId(deduplicationId: String): List<InAppSubscriptionAccessConcession> {
        return client
            .findByPartitionKeyAndScanKeyOnIndex(
                index = GlobalSecondaryIndexNames.GSIndex2,
                partitionKey = "$Index2PrimaryKeyPrefix$deduplicationId",
                sortKey = deduplicationId,
            ).map { it.toInAppSubscriptionAccessConcession() }
    }

    private fun InAppSubscriptionAccessConcessionEntity.toInAppSubscriptionAccessConcession() =
        InAppSubscriptionAccessConcession(
            id = InAppSubscriptionAccessConcessionId(id),
            accountId = AccountId(accountId),
            reason = reason,
            endsAt = ZonedDateTime.parse(this.endsAt, DateTimeFormatter.ISO_DATE_TIME),
            store = store?.let { InAppSubscriptionStore.valueOf(it) },
            createdAt = ZonedDateTime.parse(createdAt, DateTimeFormatter.ISO_DATE_TIME),
            isTransfer = transfer,
            originConcessionId = originConcessionId?.let { InAppSubscriptionAccessConcessionId(it) },
            deduplicationId = deduplicationId,
        )
}

@DynamoDbBean
class InAppSubscriptionAccessConcessionEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PrimaryKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1ScanKey: String

    @get:DynamoDbAttribute(value = "AccountId")
    lateinit var accountId: String

    @get:DynamoDbAttribute(value = "Reason")
    lateinit var reason: InAppSubscriptionReason

    @get:DynamoDbAttribute(value = "EndsAt")
    lateinit var endsAt: String

    @get:DynamoDbAttribute(value = "Store")
    var store: String? = null

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute(value = "Id")
    lateinit var id: String

    @get:DynamoDbAttribute(value = "transfer")
    var transfer: Boolean? = null

    @get:DynamoDbAttribute(value = "OriginConcessionId")
    var originConcessionId: String? = null

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    var gSIndex2PrimaryKey: String? = null

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    var gSIndex2ScanKey: String? = null

    @get:DynamoDbAttribute(value = "DeduplicationId")
    var deduplicationId: String? = null
}