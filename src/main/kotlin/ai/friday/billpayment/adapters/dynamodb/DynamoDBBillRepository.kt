package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BatchSchedulingId
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.BillViewSchedule
import ai.friday.billpayment.app.bill.ExternalBillId
import ai.friday.billpayment.app.bill.PartnerPayment
import ai.friday.billpayment.app.bill.PaymentStarted
import ai.friday.billpayment.app.bill.PixCopyAndPaste
import ai.friday.billpayment.app.bill.PixQrCodeData
import ai.friday.billpayment.app.bill.RefundedBill
import ai.friday.billpayment.app.bill.WarningCode
import ai.friday.billpayment.app.bill.getWarningCode
import ai.friday.billpayment.app.bill.name
import ai.friday.billpayment.app.billcategory.BillCategorySuggestion
import ai.friday.billpayment.app.contact.ContactId
import ai.friday.billpayment.app.getOrFalse
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.FindBillsCriteria
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.DivergentPayment
import ai.friday.billpayment.app.payment.PartialPayment
import ai.friday.billpayment.app.payment.SettlementFundsTransfer
import ai.friday.billpayment.app.payment.SettlementFundsTransferType
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.date.timeFormat
import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Property
import io.netty.util.internal.StringUtil
import jakarta.inject.Singleton
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.Month
import java.time.Year
import java.time.YearMonth
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

enum class ContactIdIndexMetadata {
    RECURRENT_BILL, BILL, RECURRENCE
}

@Singleton
class BillDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<BillEntity>(cli, BillEntity::class.java)

@Singleton
class RefundedBillDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<RefundedBillEntity>(cli, RefundedBillEntity::class.java)

@Singleton
class SettlementFundsTransferDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<SettlementFundsTransferEntity>(cli, SettlementFundsTransferEntity::class.java)

const val REFUNDED_BILL_ENTITY_SCAN_PREFIX = "REFUNDED"

val validStatusToBillComingDue = listOf(BillStatus.ACTIVE, BillStatus.WAITING_APPROVAL).map { it.name }

@Singleton
class DynamoDbBillRepository(
    private val billClient: BillDynamoDAO,
    private val refundedClient: RefundedBillDynamoDAO,
    private val settlementFundsTransferClient: SettlementFundsTransferDynamoDAO,
) : BillRepository {

    @field:Property(name = "bill.ted.limitTime")
    var tedLimitTime: String = "17:00"

    private val mapper = jacksonObjectMapper()

    private val settlementFundsTransferKey = "SETTLEMENT-FUNDS-TRANSFER"

    override fun save(bill: Bill) {
        val entity = BillEntity().apply {
            partitionKey = bill.billId.value
            sortKey = bill.walletId.value
            gSIndex1PartitionKey = bill.walletId.value
            gSIndex1SortKey = "BILL#" + bill.effectiveDueDate + "#" + bill.status.name
            contactId = bill.contactId?.value
            gSIndex2SortKey = bill.contactId?.let {
                bill.recurrenceRule?.let { ContactIdIndexMetadata.RECURRENT_BILL } ?: ContactIdIndexMetadata.BILL
            }
            amount = bill.amount
            discount = bill.discount
            interest = bill.interest
            fine = bill.fine
            billRecipient = toBillRecipientDocument(bill.recipient)
            amountTotal = bill.amountTotal
            paymentLimitTime = bill.paymentLimitTime.format(timeFormat)
            assignor = bill.assignor.orEmpty()
            created = Instant.ofEpochMilli(bill.created).atZone(brazilTimeZone).format(dateTimeFormat)
            description = bill.description
            status = bill.status.name
            paidDate = bill.paidDate?.let { Instant.ofEpochMilli(it).atZone(brazilTimeZone).format(dateTimeFormat) }
            amountPaid = bill.amountPaid
            dueDate = bill.dueDate.format(dateFormat)
            effectiveDueDate = bill.effectiveDueDate.format(dateFormat)
            type = bill.billType
            digitableBarCode = bill.barcode?.digitable
            lastSettleDate = bill.lastSettleDate
            expirationDate = bill.expirationDate
            payerDocument = bill.payer?.document
            payerName = bill.payer?.name
            warningCode = bill.getWarningCode().code
            scheduledDate = bill.schedule?.date?.format(dateFormat)
            waitingFunds = bill.schedule?.waitingFunds
            waitingRetry = bill.schedule?.waitingRetry
            batchSchedulingId = bill.schedule?.batchSchedulingId?.value
            payerAlias = bill.payer?.alias
            amountCalculationModel = bill.amountCalculationModel
            amountCalculationOption = bill.amountCalculationOption
            amountCalculationValidUntil = bill.amountCalculationValidUntil?.format(dateFormat)
            recurrenceRule = bill.recurrenceRule?.let {
                RecurrenceRuleEntity().apply {
                    frequency = it.frequency
                    startDate = it.startDate.format(dateFormat)
                    endDate = it.endDate?.format(dateFormat)
                    pattern = it.pattern
                }
            }
            fichaCompensacaoType = bill.fichaCompensacaoType
            visibleBy = bill.visibleBy.map {
                it.value
            }
            fichaCompensacaoIdNumber = bill.idNumber
            markedAsPaidBy = bill.markedAsPaidBy?.let { it.value }
            externalId = bill.externalId?.value
            externalProvider = bill.externalId?.provider?.name
            gsIndex3PartitionKey = bill.status.name
            gSIndex3SortKey = buildIndex3ScanKey(bill)
            gsIndex4PartitionKey = bill.categoryId?.value
            gSIndex4SortKey = bill.walletId.value
            partnerPayment = bill.partnerPayment?.let {
                PartnerPaymentDocument().apply {
                    partner = it.partner
                    paymentId = it.partnerPaymentId
                }
            }
            subscriptionFee = bill.subscriptionFee
            securityValidationResult = bill.securityValidationResult
            paymentMethodsDetail = bill.paymentMethodsDetail?.toPaymentMethodsDetailEntity()
            transactionCorrelationId = bill.transactionCorrelationId
            tags = if (bill.tags.isEmpty()) null else bill.tags
            categoryId = bill.categoryId?.value
            categorySuggestions = bill.categorySuggestions.map {
                BillCategorySuggestionDocument().apply {
                    this.categoryId = it.categoryId.value
                    this.probability = it.probability.toLong()
                }
            }
            pixQrCodeData = bill.pixQrCodeData?.let { pixQrCodeData ->
                PixQrCodeDataDocument().apply {
                    type = pixQrCodeData.type
                    info = pixQrCodeData.info
                    additionalInfo = pixQrCodeData.additionalInfo
                    pixId = pixQrCodeData.pixId
                    pixCopyAndPaste = pixQrCodeData.pixCopyAndPaste?.value
                    fixedAmount = pixQrCodeData.fixedAmount
                    expiration = pixQrCodeData.expiration?.format(DateTimeFormatter.ISO_DATE_TIME)
                    originalAmount = pixQrCodeData.originalAmount
                    automaticPixRecurringData = pixQrCodeData.automaticPixRecurringDataJson
                }
            }
            divergentPayment = bill.divergentPayment.toDivergentPaymentDocument()
            partialPayment = bill.partialPayment.toPartialPaymentDocument()
            brand = bill.brand
            scheduledBy = bill.scheduledBy?.value
            paymentWalletId = bill.paymentWalletId?.value
            ignoredAt = bill.ignoredAt?.let { Instant.ofEpochMilli(it).atZone(brazilTimeZone).format(dateTimeFormat) }
            refundedAt = bill.refundedAt?.let { Instant.ofEpochMilli(it).atZone(brazilTimeZone).format(dateTimeFormat) }
            markedAsPaidAt = bill.markedAsPaidAt?.let { Instant.ofEpochMilli(it).atZone(brazilTimeZone).format(dateTimeFormat) }
            goalId = bill.goalId?.value
            automaticPixAuthorizationMaximumAmount = bill.automaticPixAuthorizationMaximumAmount
            automaticPixData = bill.automaticPixData?.toEntity()
        }
        try {
            entity.source = mapper.writeValueAsString(bill.source)
        } catch (e: JsonProcessingException) {
            entity.source = ""
            logger.error("DynamoDbBillRepositorySave", e)
        }

        try {
            entity.scheduleSource = bill.scheduleSource?.let { mapper.writeValueAsString(it) }
        } catch (e: JsonProcessingException) {
            logger.error("DynamoDbBillRepositorySave", e)
        }

        billClient.save(entity)
    }

    @Trace
    override fun findOverdueBills(walletId: WalletId): List<BillView> {
        val today = getZonedDateTime()
        val billList = billClient.findByPartitionKeyAndSortKeyBetweenByIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = walletId.value,
            sortKeyFrom = "BILL#",
            sortKeyTo = "BILL#${today.format(dateFormat)}",
        ).filter { it.status == BillStatus.ACTIVE.name }
        try {
            return billList.map { billEntity: BillEntity -> convertToBill(billEntity) }.filter { it.billType != BillType.AUTOMATIC_PIX } // FIXME: Remover filtro de pix automático
        } catch (e: Exception) {
            logger.error(Markers.append("walletId", walletId.value), "DynamoDbBillRepository#findOverdueBills", e)
            throw e
        }
    }

    override fun findBillsWaitingFunds(walletId: WalletId): List<BillView> {
        val billEntities = billClient.findBeginsWithOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = walletId.value,
            sortKey = "BILL#",
        ).filter { it.status == BillStatus.ACTIVE.name && it.waitingFunds == true }
        return billEntities.map { billEntity: BillEntity -> convertToBill(billEntity) }.filter { it.billType != BillType.AUTOMATIC_PIX } // FIXME: Remover filtro de pix automático
    }

    override fun findBillsComingDue(walletId: WalletId): List<BillView> {
        val today = getZonedDateTime()
        val billList = billClient.findBeginsWithOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = walletId.value,
            sortKey = "BILL#${today.format(dateFormat)}#",
        )
        return billList.filter { it.status in validStatusToBillComingDue }.map { billEntity: BillEntity -> convertToBill(billEntity) }.filter { it.billType != BillType.AUTOMATIC_PIX } // FIXME: Remover filtro de pix automático
    }

    override fun findBillsComingDueWithin(walletId: WalletId, periodInDays: Long): List<BillView> {
        val today = getZonedDateTime()
        val endDate = today.plusDays(periodInDays)
        val billList = billClient.findByPartitionKeyAndSortKeyBetweenByIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = walletId.value,
            sortKeyFrom = "BILL#${today.format(dateFormat)}",
            sortKeyTo = "BILL#${endDate.format(dateFormat)}",
        )

        return billList.filter { it.status in validStatusToBillComingDue }.map { billEntity: BillEntity -> convertToBill(billEntity) }.filter { it.billType != BillType.AUTOMATIC_PIX } // FIXME: Remover filtro de pix automático
    }

    @Trace
    override fun findByWalletAndStatus(walletId: WalletId, status: BillStatus): List<BillView> {
        val billEntities = billClient.findBeginsWithOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = walletId.value,
            sortKey = "BILL#",
        ).filter { it.status == status.name }
        return billEntities.map { billEntity: BillEntity -> convertToBill(billEntity) }.filter { it.billType != BillType.AUTOMATIC_PIX } // FIXME: Remover filtro de pix automático
    }

    @Trace
    override fun findByWallet(walletId: WalletId): List<BillView> {
        val billList = billClient.findBeginsWithOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = walletId.value,
            sortKey = "BILL#",
        )
        return billList.map { billEntity: BillEntity -> convertToBill(billEntity) }.filter { it.billType != BillType.AUTOMATIC_PIX } // FIXME: Remover filtro de pix automático
    }

    @Trace
    override fun findByWallet(walletId: WalletId, criteria: FindBillsCriteria): List<BillView> {
        val from = criteria.from?.let { "BILL#${it.toIsoString()}-01#" }
        val to = criteria.to?.let { "BILL#${it.plusMonths(1).toIsoString()}" }
        val index = GlobalSecondaryIndexNames.GSIndex1

        val entities: List<BillEntity> = when {
            from != null && to != null -> billClient.findByPartitionKeyAndSortKeyBetweenByIndex(index, walletId.value, from, to)
            from != null -> billClient.findByPrimaryKeyGreaterThanOrEqualByIndex(index, walletId.value, from)
            to !== null -> billClient.findByPrimaryKeyLessThanOrEqualByIndex(index, walletId.value, to)
            else -> billClient.findBeginsWithOnIndex(index, walletId.value, "BILL#")
        }

        return entities.map { convertToBill(it) }.filter { it.billType != BillType.AUTOMATIC_PIX } // FIXME: Remover filtro de pix automático
    }

    @Trace
    override fun findByWalletAndEffectiveDueDate(walletId: WalletId, dueDate: LocalDate): List<BillView> {
        return billClient.findBeginsWithOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = walletId.value,
            sortKey = "BILL#${dueDate.format(dateFormat)}",
        ).map(::convertToBill).filter { it.billType != BillType.AUTOMATIC_PIX } // FIXME: Remover filtro de pix automático
    }

    @Trace
    override fun findByWalletAndEffectiveDueDateMonth(walletId: WalletId, year: Year, month: Month): List<BillView> {
        return billClient.findBeginsWithOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = walletId.value,
            sortKey = "BILL#${year.value}-${month.value.toString().padStart(2, '0')}",
        ).map(::convertToBill).filter { it.billType != BillType.AUTOMATIC_PIX } // FIXME: Remover filtro de pix automático
    }

    override fun findByContactId(contactId: ContactId): List<BillView> {
        return findByContactId(contactId, ContactIdIndexMetadata.BILL)
    }

    override fun findRecurrenceBillByContactId(contactId: ContactId): List<BillView> {
        return findByContactId(contactId, ContactIdIndexMetadata.RECURRENT_BILL)
    }

    private fun findByContactId(contactId: ContactId, billType: ContactIdIndexMetadata): List<BillView> {
        val billEntities = billClient.findByPartitionKeyAndScanKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex2,
            partitionKey = contactId.value,
            sortKey = billType.name,
        )
        return billEntities.map { billEntity: BillEntity -> convertToBill(billEntity) }.filter { it.billType != BillType.AUTOMATIC_PIX } // FIXME: Remover filtro de pix automático
    }

    override fun findBill(billId: BillId, walletId: WalletId): BillView {
        val billEntity = billClient.findByPrimaryKey(billId.value, walletId.value)
            ?: throw IllegalStateException("Bill ${billId.value} not found on wallet ${walletId.value}")
        return convertToBill(billEntity)
    }

    override fun remove(billId: BillId, walletId: WalletId) {
        billClient.delete(billId.value, walletId.value)
    }

    override fun setBillComingDueMessageSent(billId: BillId, walletId: WalletId, billComingDueMessageSent: Boolean) {
        val billEntity = billClient.findByPrimaryKey(billId.value, walletId.value)
            ?: throw IllegalStateException("Bill ${billId.value} not found on wallet ${walletId.value}")

        billEntity.billComingDueMessageSent = billComingDueMessageSent
        billClient.save(billEntity)
    }

    override fun saveSettlementFundsTransfer(settlementFundsTransfer: SettlementFundsTransfer) {
        val entity = SettlementFundsTransferEntity().apply {
            primaryKey = settlementFundsTransfer.billId.value
            scanKey = "$settlementFundsTransferKey#${settlementFundsTransfer.bankOperationId.value}"
            gSIndex1PrimaryKey = settlementFundsTransferKey
            gSIndex1ScanKey = settlementFundsTransfer.created.format(dateTimeFormat)
            amount = settlementFundsTransfer.amount
            status = settlementFundsTransfer.status
            created = settlementFundsTransfer.created.format(dateTimeFormat)
            bankOperationId = settlementFundsTransfer.bankOperationId.value
            transactionId = settlementFundsTransfer.transactionId.value
            sourceAccountType = settlementFundsTransfer.sourceAccountType
        }
        settlementFundsTransferClient.save(entity)
    }

    override fun findSettlementFundsTransfers(billId: BillId): List<SettlementFundsTransfer> {
        val entities = settlementFundsTransferClient.findBeginsWithOnSortKey(
            partitionKey = billId.value,
            sortKey = settlementFundsTransferKey,
        )

        return entities.map { entity ->
            SettlementFundsTransfer(
                bankOperationId = BankOperationId(value = entity.bankOperationId),
                billId = BillId(value = entity.primaryKey),
                status = entity.status,
                amount = entity.amount,
                created = LocalDateTime.parse(entity.created, dateTimeFormat),
                transactionId = TransactionId(entity.transactionId),
                sourceAccountType = entity.sourceAccountType
                    ?: SettlementFundsTransferType.SETTLEMENT_ACCOUNT,
                sameBank = entity.sameBank,
            )
        }
    }

    override fun getStuckedProcessingBills(seconds: Long): List<BillView> {
        return billClient.findByPartitionKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex3,
            partitionKey = BillStatus.PROCESSING.name,
        ).filter {
            val processingTime = readIndex3DateTime(it)
            processingTime?.plusSeconds(seconds)?.isBefore(getZonedDateTime()) ?: true
        }.map { convertToBill(it) }.filter { it.billType != BillType.AUTOMATIC_PIX } // FIXME: Remover filtro de pix automático
    }

    override fun getPaidBills(
        walletId: WalletId,
        startDate: ZonedDateTime,
        endDate: ZonedDateTime,
        types: List<BillType>?,
    ): List<BillView> {
        val readValues = billClient.findByPartitionKeyAndSortKeyBetweenByIndex(
            index = GlobalSecondaryIndexNames.GSIndex3,
            partitionKey = BillStatus.PAID.name,
            sortKeyFrom = "${walletId.value}#${startDate.format(dateTimeFormat)}",
            sortKeyTo = "${walletId.value}#${endDate.format(dateTimeFormat)}",
        ).map { convertToBill(it) }.filter { it.billType != BillType.AUTOMATIC_PIX } // FIXME: Remover filtro de pix automático

        return types?.let {
            readValues.filter { it.billType in types }
        } ?: readValues
    }

    override fun refund(
        walletId: WalletId,
        billId: BillId,
        transactionId: TransactionId,
        type: BillType,
        amount: Long,
        originalPaidDate: ZonedDateTime,
    ) {
        val refundDate = getZonedDateTime().format(dateTimeFormat)
        refundedClient.save(
            RefundedBillEntity().apply {
                this.primaryKey = walletId.value
                this.scanKey = "$REFUNDED_BILL_ENTITY_SCAN_PREFIX#$refundDate"
                this.billId = billId.value
                this.amount = amount
                this.transactionId = transactionId.value
                this.refundDate = refundDate
                this.walletId = walletId.value
                this.type = type
                this.originalPaidDate = originalPaidDate.format(dateTimeFormat)
            },
        )
    }

    override fun getRefundedBills(
        walletId: WalletId,
        startDate: ZonedDateTime,
        endDate: ZonedDateTime,
    ): List<RefundedBill> {
        return refundedClient.findByPartitionKeyAndSortKeyBetween(
            partitionKey = walletId.value,
            sortKeyFrom = "$REFUNDED_BILL_ENTITY_SCAN_PREFIX#${startDate.format(dateTimeFormat)}",
            sortKeyTo = "$REFUNDED_BILL_ENTITY_SCAN_PREFIX#${endDate.format(dateTimeFormat)}",
        ).map {
            RefundedBill(
                amount = it.amount,
                billId = BillId(it.billId),
                type = it.type,
                transactionId = TransactionId(value = it.transactionId),
                refundDate = ZonedDateTime.parse(it.refundDate, dateTimeFormat),
                originalPaidDate = ZonedDateTime.parse(it.originalPaidDate, dateTimeFormat),
            )
        }
    }

    override fun getTotalPaid(
        walletId: WalletId,
        startDate: ZonedDateTime,
        endDate: ZonedDateTime,
        types: List<BillType>?,
    ): Long {
        val paidBills = getPaidBills(walletId, startDate, endDate, types).filter { it.source !is ActionSource.OpenFinance }
        logger.trace(Markers.append("paidBills", paidBills.map { it.billId }), "getTotalPaid")
        return paidBills.sumOf { it.amountPaid ?: 0 }
    }

    override fun getTotalPaidBillsByScheduleSourceType(walletId: WalletId, startDate: ZonedDateTime, endDate: ZonedDateTime, actionSource: ActionSource): Long {
        return getPaidBills(walletId, startDate, endDate)
            .filter { it.scheduleSource?.name() == actionSource.name() }
            .sumOf { it.amountTotal }
    }

    private fun convertToBill(billEntity: BillEntity): BillView {
        val fixedPaymentLimitTime = if (billEntity.type == BillType.INVOICE) {
            tedLimitTime
        } else {
            billEntity.paymentLimitTime
        }
        val source: ActionSource = try {
            mapper.readerFor(ActionSource::class.java).readValue(billEntity.source)
        } catch (e: JsonProcessingException) {
            logger.error(Markers.append("billEntity", billEntity), "DynamoDbBillRepositoryConvertToBill", e)
            ActionSource.System
        }
        val scheduleSource = billEntity.scheduleSource?.let {
            try {
                mapper.readerFor(ActionSource::class.java).readValue<ActionSource>(it)
            } catch (e: JsonProcessingException) {
                logger.error(Markers.append("billEntity", billEntity), "DynamoDbBillRepositoryConvertToBill", e)
                null
            }
        }

        val pixQrCodeData = billEntity.pixQrCodeData?.let { document ->
            PixQrCodeData(
                type = document.type,
                info = document.info,
                additionalInfo = document.additionalInfo,
                pixCopyAndPaste = document.pixCopyAndPaste?.let { PixCopyAndPaste(it) },
                pixId = document.pixId,
                fixedAmount = document.fixedAmount,
                expiration = document.expiration?.let { ZonedDateTime.parse(it, DateTimeFormatter.ISO_DATE_TIME) },
                originalAmount = document.originalAmount,
                automaticPixRecurringDataJson = document.automaticPixRecurringData,
            )
        }

        val bill = BillView(
            walletId = WalletId(billEntity.sortKey),
            amount = billEntity.amount,
            discount = billEntity.discount,
            interest = billEntity.interest,
            fine = billEntity.fine,
            amountTotal = billEntity.amountTotal,
            paymentLimitTime = LocalTime.parse(fixedPaymentLimitTime, timeFormat),
            assignor = billEntity.assignor,
            barCode = billEntity.digitableBarCode?.let { BarCode.ofDigitable(it) },
            billId = BillId(billEntity.partitionKey),
            externalId = ExternalBillId.of(billEntity.externalId, billEntity.externalProvider),
            status = BillStatus.fromString(billEntity.status),
            billType = billEntity.type,
            notification = billEntity.notification,
            recipient = toRecipient(billEntity.billRecipient, pixQrCodeData),
            createdOn = toLocalDateTime(billEntity.created),
            isBillComingDueMessageSent = billEntity.billComingDueMessageSent,
            billDescription = billEntity.description ?: StringUtil.EMPTY_STRING,
            amountPaid = billEntity.calculateAmountPaid(),
            paidDate = billEntity.paidDate?.let { tryToLocalDateTime(it) },
            dueDate = LocalDate.parse(billEntity.dueDate, dateFormat),
            effectiveDueDate = billEntity.effectiveDueDate?.let {
                LocalDate.parse(
                    it,
                    dateFormat,
                )
            } ?: LocalDate.parse(billEntity.dueDate, dateFormat),
            lastSettleDate = billEntity.lastSettleDate?.let { LocalDate.parse(it, dateFormat) },
            expirationDate = billEntity.expirationDate?.let { LocalDate.parse(it, dateFormat) },
            payerDocument = billEntity.payerDocument,
            payerName = billEntity.payerName,
            warningCode = WarningCode.getByCode(billEntity.warningCode),
            schedule = billEntity.takeIf { it.scheduledDate != null }?.let {
                BillViewSchedule(
                    batchSchedulingId = it.batchSchedulingId?.let { id -> BatchSchedulingId(id) },
                    date = LocalDate.parse(it.scheduledDate!!, dateFormat),
                    waitingFunds = it.waitingFunds.getOrFalse(),
                    waitingRetry = it.waitingRetry.getOrFalse(),
                )
            },
            payerAlias = billEntity.payerAlias,
            amountCalculationModel = billEntity.amountCalculationModel ?: AmountCalculationModel.UNKNOWN,
            amountCalculationOption = billEntity.amountCalculationOption,
            amountCalculationValidUntil = billEntity.amountCalculationValidUntil?.let { LocalDate.parse(it, dateFormat) },
            recurrenceRule = billEntity.recurrenceRule?.let {
                RecurrenceRule(
                    frequency = it.frequency,
                    startDate = LocalDate.parse(it.startDate, dateFormat),
                    pattern = it.pattern.orEmpty(),
                    endDate = it.endDate?.let { endDate -> LocalDate.parse(endDate, dateFormat) },
                )
            },
            fichaCompensacaoType = billEntity.fichaCompensacaoType,
            visibleBy = billEntity.visibleBy.map {
                AccountId(it)
            },
            source = source,
            idNumber = billEntity.fichaCompensacaoIdNumber,
            markedAsPaidBy = billEntity.markedAsPaidBy?.let { AccountId(it) },
            partnerPayment = billEntity.partnerPayment?.let {
                PartnerPayment(partner = it.partner, partnerPaymentId = it.paymentId)
            },
            subscriptionFee = billEntity.subscriptionFee ?: false,
            securityValidationResult = billEntity.securityValidationResult,
            paymentDetails = billEntity.paymentMethodsDetail?.toPaymentMethodsDetail(),
            transactionCorrelationId = billEntity.transactionCorrelationId,
            tags = billEntity.tags ?: emptySet(),
            categoryId = billEntity.category?.let { PFMCategoryId(billEntity.category!!.billCategoryId) } ?: billEntity.categoryId?.let { PFMCategoryId(value = it) },
            categorySuggestions = billEntity.categorySuggestions.map {
                BillCategorySuggestion(categoryId = PFMCategoryId(it.categoryId), probability = it.probability.toInt())
            },
            pixQrCodeData = pixQrCodeData,
            divergentPayment = billEntity.divergentPayment.toDivergentPayment(),
            partialPayment = billEntity.partialPayment.toPartialPayment(),
            brand = billEntity.brand,
            scheduledBy = billEntity.scheduledBy?.let { AccountId(it) },
            scheduleSource = scheduleSource,
            paymentWalletId = billEntity.paymentWalletId?.let { WalletId(it) },
            refundedAt = billEntity.refundedAt?.let { tryToLocalDateTime(it) },
            markedAsPaidAt = billEntity.markedAsPaidAt?.let { tryToLocalDateTime(it) },
            ignoredAt = billEntity.ignoredAt?.let { tryToLocalDateTime(it) },
            goalId = billEntity.goalId?.let { GoalId(it) },
            automaticPixAuthorizationMaximumAmount = billEntity.automaticPixAuthorizationMaximumAmount,
            automaticPixData = billEntity.automaticPixData?.toDomain(),
        )
        return bill
    }

    private fun BillEntity.calculateAmountPaid(): Long? {
        return when (BillStatus.fromString(status)) {
            BillStatus.PAID, BillStatus.ALREADY_PAID, BillStatus.PAID_ON_PARTNER -> amountPaid ?: amountTotal
            else -> null
        }
    }

    private fun toLocalDateTime(date: String): LocalDateTime {
        val dateTime = ZonedDateTime.from(dateTimeFormat.withZone(brazilTimeZone).parse(date))
        return dateTime.toLocalDateTime()
    }

    private fun tryToLocalDateTime(date: String): LocalDateTime {
        return try {
            toLocalDateTime(date)
        } catch (e: Exception) {
            LocalDate.parse(date, dateFormat).atStartOfDay()
        }
    }

    private fun buildIndex3ScanKey(bill: Bill): String {
        val suffix = when {
            bill.isPaid() -> bill.paidDate!!
            bill.isProcessing() -> bill.history.lastOrNull { it is PaymentStarted }?.created
            else -> bill.history.lastOrNull()?.created
        }?.let {
            Instant.ofEpochMilli(it).atZone(brazilTimeZone).format(dateTimeFormat)
        } ?: ""

        return "${bill.walletId.value}#$suffix"
    }

    private fun readIndex3DateTime(billEntity: BillEntity): ZonedDateTime? {
        val split = billEntity.gSIndex3SortKey?.split("#") ?: return null

        if (split.size < 2) {
            return null
        }

        return ZonedDateTime.parse(split[1], dateTimeFormat)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(DynamoDbBillRepository::class.java)
    }
}

private fun PartialPayment?.toPartialPaymentDocument(): PartialPaymentDocument? {
    return this?.let {
        PartialPaymentDocument().apply {
            acceptPartialPayment = it.acceptPartialPayment
            qtdPagamentoParcial = it.qtdPagamentoParcial
            qtdPagamentoParcialRegistrado = it.qtdPagamentoParcialRegistrado
            saldoAtualPagamento = it.saldoAtualPagamento
        }
    }
}

private fun DivergentPayment?.toDivergentPaymentDocument(): DivergentPaymentDocument? {
    return this?.let {
        DivergentPaymentDocument().apply {
            minimumAmount = it.minimumAmount
            maximumAmount = it.maximumAmount
            amountType = it.amountType
            minimumPercentage = it.minimumPercentage
            maximumPercentage = it.maximumPercentage
        }
    }
}

private fun PartialPaymentDocument?.toPartialPayment(): PartialPayment? {
    return this?.let {
        PartialPayment(
            acceptPartialPayment = acceptPartialPayment,
            qtdPagamentoParcial = qtdPagamentoParcial,
            qtdPagamentoParcialRegistrado = qtdPagamentoParcialRegistrado,
            saldoAtualPagamento = saldoAtualPagamento,
        )
    }
}

private fun DivergentPaymentDocument?.toDivergentPayment(): DivergentPayment? {
    return this?.let {
        DivergentPayment(
            minimumAmount = minimumAmount,
            maximumAmount = maximumAmount,
            amountType = amountType,
            minimumPercentage = minimumPercentage,
            maximumPercentage = maximumPercentage,
        )
    }
}

private val YearMonthIsoFormatter = DateTimeFormatter.ofPattern("yyyy-MM")

private fun YearMonth.toIsoString(): String = this.format(YearMonthIsoFormatter)

@DynamoDbBean
class RefundedBillEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbAttribute(value = "BillId")
    lateinit var billId: String

    @get:DynamoDbAttribute(value = "Amount")
    var amount: Long = 0

    @get:DynamoDbAttribute(value = "TransactionId")
    lateinit var transactionId: String

    @get:DynamoDbAttribute(value = "RefundDate")
    lateinit var refundDate: String

    @get:DynamoDbAttribute(value = "OriginalPaidDate")
    lateinit var originalPaidDate: String

    @get:DynamoDbAttribute(value = "WalletId")
    lateinit var walletId: String

    @get:DynamoDbAttribute(value = "BillType")
    lateinit var type: BillType
}

@DynamoDbBean
class SettlementFundsTransferEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PrimaryKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1ScanKey: String

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: BankOperationStatus

    @get:DynamoDbAttribute(value = "Amount")
    var amount: Long = 0

    @get:DynamoDbAttribute(value = "Created")
    lateinit var created: String

    @get:DynamoDbAttribute(value = "BankOperationId")
    lateinit var bankOperationId: String

    @get:DynamoDbAttribute(value = "TransactionId")
    lateinit var transactionId: String

    @get:DynamoDbAttribute(value = "SourceAccountType")
    var sourceAccountType: SettlementFundsTransferType? = null

    @get:DynamoDbAttribute(value = "SameBank")
    var sameBank: Boolean? = null
}