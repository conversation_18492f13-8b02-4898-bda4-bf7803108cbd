package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.integrations.LoginRepository
import ai.friday.billpayment.app.login.Login
import ai.friday.billpayment.app.login.LoginNotFoundException
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient

@Singleton
class LoginDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<LoginEntity>(cli, LoginEntity::class.java)

@Singleton
class LoginDbRepository(
    private val dynamoDbDAO: LoginDynamoDAO,
    private val transactionDynamo: TransactionDynamo,
) : LoginRepository {

    private val scanKeyPrefix = "LOGIN#"

    override fun findUserLogin(provider: ProviderUser): Login? {
        val loginEntities = dynamoDbDAO.findBeginsWithOnSortKey(
            partitionKey = provider.id,
            sortKey = scanKeyPrefix + provider.providerName,
        )
        val loginEntity = loginEntities.firstOrNull() ?: return null
        return Login(AccountId(loginEntity.accountId), EmailAddress(loginEntity.index1PartitionKey), loginEntity.role)
    }

    override fun findUserLogin(emailAddress: EmailAddress): List<Login> {
        return findEntityByEmail(emailAddress)
            .map { Login(AccountId(it.accountId), EmailAddress(it.index1PartitionKey), it.role) }
    }

    override fun findActiveUserLoginProvider(emailAddress: EmailAddress): List<ProviderUser> {
        return findEntityByEmail(emailAddress)
            .filter { !it.scanKey.startsWith("DISABLED#") }
            .map {
                ProviderUser(
                    id = it.primaryKey,
                    providerName = ProviderName.valueOf(it.scanKey.removePrefix(scanKeyPrefix)),
                    username = "",
                    emailAddress = EmailAddress(email = it.index1PartitionKey),
                    created = it.created?.let { ZonedDateTime.parse(it, DateTimeFormatter.ISO_DATE_TIME) },
                )
            }
    }

    override fun remove(providerUser: ProviderUser) {
        val loginEntity = dynamoDbDAO.findByPrimaryKey(
            partitionKey = providerUser.id,
            sortKey = scanKeyPrefix + providerUser.providerName,
        )
        loginEntity?.let {
            dynamoDbDAO.delete(it.primaryKey, it.scanKey)
        }
    }

    override fun remove(accountId: AccountId) {
        val loginEntities = dynamoDbDAO.findBeginsWithOnSortKey(
            partitionKey = accountId.value,
            sortKey = scanKeyPrefix,
        )
        loginEntities.forEach {
            dynamoDbDAO.delete(it.primaryKey, it.scanKey)
        }
    }

    override fun createLogin(providerUser: ProviderUser, id: AccountId, role: Role) {
        val loginEntity = LoginEntity().apply {
            primaryKey = providerUser.id
            scanKey = scanKeyPrefix + providerUser.providerName
            accountId = id.value
            index1PartitionKey = providerUser.emailAddress.toString()
            this.role = role
            created = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
        }
        dynamoDbDAO.save(loginEntity)
    }

    override fun updateRole(accountId: AccountId, emailAddress: EmailAddress, role: Role) {
        findEntityByEmail(emailAddress)
            .filter { AccountId(it.accountId) == accountId }
            .ifEmpty { throw LoginNotFoundException(accountId, emailAddress) }
            .forEach { updateRole(it, role) }
    }

    private fun updateRole(loginEntity: LoginEntity, role: Role) {
        loginEntity.role = role
        dynamoDbDAO.save(loginEntity)
    }

    override fun updateEmail(accountId: AccountId, currentEmailAddress: EmailAddress, newEmailAddress: EmailAddress) {
        findEntityByEmail(currentEmailAddress)
            .filter { AccountId(it.accountId) == accountId && !it.scanKey.startsWith("DISABLED#") }
            .forEach { updateEmail(it, newEmailAddress) }
    }

    private fun updateEmail(loginEntity: LoginEntity, newEmailAddress: EmailAddress) {
        val disabledEntity = LoginEntity().apply {
            primaryKey = loginEntity.primaryKey
            scanKey = "DISABLED#${loginEntity.scanKey}"
            index1PartitionKey = newEmailAddress.value
            accountId = loginEntity.accountId
            role = loginEntity.role
            created = loginEntity.created
        }

        transactionDynamo.transactionWrite(
            dynamoDbDAO.transactionWriteDelete(loginEntity),
            dynamoDbDAO.transactionWritePut(disabledEntity),
        )
    }

    private fun findEntityByEmail(emailAddress: EmailAddress): List<LoginEntity> {
        return dynamoDbDAO.findByPartitionKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = emailAddress.value,
        )
    }
}