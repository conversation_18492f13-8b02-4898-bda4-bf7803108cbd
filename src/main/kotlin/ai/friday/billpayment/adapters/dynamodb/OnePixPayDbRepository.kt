package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.onepixpay.OnePixPay
import ai.friday.billpayment.app.onepixpay.OnePixPayId
import ai.friday.billpayment.app.onepixpay.OnePixPayIdSource
import ai.friday.billpayment.app.onepixpay.OnePixPayRepository
import ai.friday.billpayment.app.onepixpay.OnePixPayStatus
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.dateFormat
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val ONE_PIX_PAY_KEY = "ONE_PIX_PAY"
private val paymentLocalTimeFormatter = DateTimeFormatter.ofPattern("HH:mm")

@Singleton
class OnePixPayDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<OnePixPayEntity>(cli, OnePixPayEntity::class.java)

@Singleton
class OnePixPayDbRepository(private val client: OnePixPayDynamoDAO) : OnePixPayRepository {

    override fun findAll(date: LocalDate): List<OnePixPay> {
        return client.findBeginsWithOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = ONE_PIX_PAY_KEY,
            sortKey = "${date.format(dateFormat)}#",
        ).map { it.toDomain() }
    }

    override fun find(onePixPayId: OnePixPayId<*>): OnePixPay {
        return client.findByPrimaryKey(
            partitionKey = onePixPayId.value,
            sortKey = ONE_PIX_PAY_KEY,
        )?.toDomain() ?: throw ItemNotFoundException("One Pix Pay key not found")
    }

    override fun findByWalletId(walletId: WalletId, from: LocalDate, status: OnePixPayStatus): List<OnePixPay> {
        val scanKey = "${from.format(dateFormat)}#${status.name}"
        return client.findBeginsWithOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex2,
            partitionKey = walletId.value,
            sortKey = scanKey,
        ).map { it.toDomain() }
    }

    override fun save(onePixPay: OnePixPay): OnePixPay {
        client.save(onePixPay.toEntity())
        return onePixPay
    }
}

private fun OnePixPay.toEntity(): OnePixPayEntity {
    return this.let { onePixPay ->
        OnePixPayEntity().apply {
            partitionKey = onePixPay.id.value
            walletId = onePixPay.walletId.value
            billIds = onePixPay.billIds.map { it.value }
            qrCode = onePixPay.qrCode
            index1ScanKey = "${onePixPay.created.format(dateFormat)}#${onePixPay.status.name}"
            created = onePixPay.created.format(DateTimeFormatter.ISO_DATE_TIME)
            index2PartitionKey = onePixPay.walletId.value
            index2ScanKey = "${onePixPay.created.format(dateFormat)}#${onePixPay.status}"
            fundsReceived = onePixPay.fundsReceived
            fundsReceivedAt = onePixPay.fundsReceivedAt?.format(DateTimeFormatter.ISO_DATE_TIME)
            paymentLimitTime = onePixPay.paymentLimitTime.format(paymentLocalTimeFormatter)
            status = onePixPay.status
            errorDescription = onePixPay.errorDescription
        }
    }
}

private fun OnePixPayEntity.toDomain(): OnePixPay {
    return OnePixPay(
        id = OnePixPayIdSource.fromIdValue(this.partitionKey).toOnePixPayId(),
        walletId = WalletId(this.walletId),
        billIds = this.billIds.map { BillId(it) },
        qrCode = this.qrCode,
        status = this.status,
        paymentLimitTime = LocalTime.parse(this.paymentLimitTime, paymentLocalTimeFormatter),
        fundsReceived = this.fundsReceived,
        fundsReceivedAt = this.fundsReceivedAt?.let { ZonedDateTime.parse(it, DateTimeFormatter.ISO_DATE_TIME) },
        created = ZonedDateTime.parse(this.created, DateTimeFormatter.ISO_DATE_TIME),
        errorDescription = this.errorDescription,
    )
}

@DynamoDbBean
class OnePixPayEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String // OnePixPayId

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    var scanKey: String = ONE_PIX_PAY_KEY

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    var index1PartitionKey: String = ONE_PIX_PAY_KEY

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var index1ScanKey: String // (2022-10-01#OnePixPayStatus)

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    lateinit var index2PartitionKey: String // WalletId

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    lateinit var index2ScanKey: String // (2022-10-01#REQUESTED) Created#Status

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: OnePixPayStatus

    @get:DynamoDbAttribute(value = "Created")
    lateinit var created: String // 2022-06-01T15:04:56.482611Z

    @get:DynamoDbAttribute(value = "PaymentLimitTime")
    lateinit var paymentLimitTime: String // 10:00

    @get:DynamoDbAttribute(value = "FundsReceived")
    var fundsReceived: Boolean = false

    @get:DynamoDbAttribute(value = "FundsReceivedAt")
    var fundsReceivedAt: String? = null

    @get:DynamoDbAttribute(value = "WalletId")
    lateinit var walletId: String

    @get:DynamoDbAttribute(value = "BillIds")
    lateinit var billIds: List<String>

    @get:DynamoDbAttribute(value = "QrCode")
    var qrCode: String = ""

    @get:DynamoDbAttribute(value = "ErrorDescription")
    var errorDescription: String? = null
}