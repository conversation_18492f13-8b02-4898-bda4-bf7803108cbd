package ai.friday.billpayment.adapters.chatbot

import ai.friday.billpayment.adapters.api.CALLBACK_ROUTE
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.api.StandardHttpResponses
import ai.friday.billpayment.adapters.itp.mapToHttpResponse
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.bill.and
import ai.friday.billpayment.app.chatbot.ChatbotMessagePublisher
import ai.friday.billpayment.app.chatbot.ChatbotQrCodeError
import ai.friday.billpayment.app.chatbot.ChatbotService
import ai.friday.billpayment.app.chatbot.NotifyBalanceError
import ai.friday.billpayment.app.chatbot.OtherInstitutionsResponseTO
import ai.friday.billpayment.app.chatbot.PaymentOrganizationResult
import ai.friday.billpayment.app.forecast.ForecastPeriod
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.itp.CreatePaymentIntentCommand
import ai.friday.billpayment.app.itp.ITPService
import ai.friday.billpayment.app.itp.ITPServiceError
import ai.friday.billpayment.app.login.LoginService
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.metrics.ChatbotMessage
import ai.friday.billpayment.app.metrics.metricRegister
import ai.friday.billpayment.app.onepixpay.OnePixPayIdSource
import ai.friday.billpayment.app.onepixpay.OnePixPayRepository
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.onepixpay.OnePixPayStatus
import ai.friday.billpayment.app.payment.buildFormattedAmount
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.Header
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.security.rules.SecurityRule
import io.via1.communicationcentre.app.notification.NotificationFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Controller("/chatbot")
@FridayMePoupe
@Secured(SecurityRule.IS_ANONYMOUS)
open class ChatbotController(
    private val chatbotService: ChatbotService,
    private val itpService: ITPService,
    private val billRepository: BillRepository,
    private val onePixPayRepository: OnePixPayRepository,
    private val onePixPayService: OnePixPayService,
    private val walletService: WalletService,
    private val loginService: LoginService,
    private val accountService: AccountService,
    private val chatbotMessagePublisher: ChatbotMessagePublisher,
) {

    @Post(CALLBACK_ROUTE)
    fun blipWebhook(@Body rawBody: Map<String, Any>): HttpResponse<Unit> {
        val markers = Markers.append("body", rawBody)
        try {
            val receivedData = parseObjectFrom<BlipChatbotLogTO>(getObjectMapper().writeValueAsString(rawBody))

            if (receivedData.metadata != null) {
                val accountId = discoverAccountId(receivedData)?.let { accountId ->
                    markers.andAppend("accountId", accountId)
                    accountId
                }

                metricRegister(
                    ChatbotMessage(),
                    "stateName" to receivedData.metadata.stateName,
                    "previousStateName" to receivedData.metadata.previousStateName,
                    "accountId" to accountId,
                )
            }

            chatbotMessagePublisher.publishWebhook(rawBody)

            LOG.info(markers, "BlipWebHook")
            return HttpResponse.noContent()
        } catch (e: Exception) {
            LOG.warn(markers, "BlipWebHook", e)
            return HttpResponse.ok()
        }
    }

    @Get("/choose")
    fun chooseChatbot(authentication: Authentication): HttpResponse<*> {
        return try {
            val logName = "ChatbotController#ChooseChatbot"
            val markers = Markers.append("accountId", authentication.toAccountId())
            val accountId = authentication.toAccountId()
            val chatbotType = accountService.getChatbotType(accountId)
            LOG.info(markers.andAppend("chatbotType", chatbotType), logName)
            HttpResponse.ok(ChatbotChooseTO(chatbotType.value))
        } catch (e: Exception) {
            LOG.error("ChatbotController#ChooseChatbot", e)
            HttpResponse.serverError<Unit>()
        }
    }

    @Post("/reportError")
    fun reportErrorChatbot(@Body body: String, authentication: Authentication): HttpResponse<*> {
        return try {
            val logName = "ChatbotController#ErrorReport"
            val markers = Markers.append("accountId", authentication.toAccountId())
            LOG.info(markers.andAppend("errorReport", body), logName)
            HttpResponse.noContent<Unit>()
        } catch (e: Exception) {
            LOG.error("ChatbotController#ErrorReport", e)
            HttpResponse.serverError<Unit>()
        }
    }

    @Get("/pix-copy-and-paste")
    fun pixCopyAndPaste(
        @Header("X-BUTTON-PAYLOAD") buttonPayload: String?,
        authentication: Authentication,
    ): HttpResponse<*> {
        val markers = Markers.append("buttonPayload", buttonPayload)

        try {
            val accountId = authentication.toAccountId()
            markers.andAppend("accountId", accountId.value)

            val (walletId, forecastPeriod) = extractWalletAndForecast(buttonPayload)

            val result = if (walletId != null) {
                markers.andAppend("walletId", walletId.value)
                markers.andAppend("forecastPeriod", forecastPeriod)
                chatbotService.createQrCode(accountId, walletId, forecastPeriod)
            } else {
                chatbotService.createQrCode(accountId, forecastPeriod)
            }

            val paymentClassifier = if (forecastPeriod == ForecastPeriod.TODAY) {
                " agendados"
            } else {
                ""
            }

            result.map {
                markers.andAppend("walletId", it.wallet.id.value)
                markers.andAppend("forecastPeriod", it.forecastPeriod)
                LOG.info(markers.andAppend("qrCode", it.qrCode.value), "PixCopyAndPaste")

                val message =
                    "*Copie e cole o código abaixo no App do seu banco* para realizar um Pix no valor de *${
                    buildFormattedAmount(it.qrCode.amount)
                    }* referente aos pagamentos$paymentClassifier ${forecastPeriod.toDescription()}."

                val title = if (walletId == null) {
                    "Saldo será adicionado na carteira ${it.wallet.name}"
                } else {
                    null
                }

                return HttpResponse.ok(
                    PixCopyAndPasteResponseTO(
                        qrCode = it.qrCode.value,
                        message = message,
                        title = title,
                    ),
                )
            }.getOrElse {
                LOG.error(markers.andAppend("error", it), "PixCopyAndPaste")

                return when (it) {
                    is ChatbotQrCodeError.SufficientBalance -> StandardHttpResponses.badRequest(
                        4001,
                        "*Saldo atual suficiente* para os pagamentos$paymentClassifier ${forecastPeriod.toDescription()}.",
                    )

                    else -> StandardHttpResponses.badRequest(4002, "Desculpe, não consegui gerar seu código pix.")
                }
            }
        } catch (e: Exception) {
            LOG.error(markers, "PixCopyAndPaste", e)
            return HttpResponse.serverError<Unit>()
        }
    }

    @Get("/payment-organizations")
    fun listPaymentOrganizations(
        @Header("X-BUTTON-PAYLOAD") buttonPayload: String,
        authentication: Authentication,
    ): HttpResponse<*> {
        val (walletId, _) = extractWalletAndForecast(buttonPayload)

        if (walletId == null) {
            return HttpResponse.badRequest<Unit>()
        }

        val accountId = authentication.toAccountId()
        val markers = Markers.append("accountId", accountId).and("walletId" to walletId.value)

        val accountOrganization = itpService.listPaymentOrganizations(accountId).getOrElse {
            return it.mapToHttpResponse(
                onBadRequest = {
                    LOG.info(
                        markers.and("ErrorMessage" to it),
                        "ChatbotController#ListPaymentOrganizations",
                    )
                },
                onServerError = { error ->
                    LOG.error(
                        markers.and("ErrorMessage" to error.message),
                        "ChatbotController#ListPaymentOrganizations",
                        error.exception,
                    )
                },
            )
        }

        LOG.info(
            markers.and("bankISPBList" to accountOrganization.result.map { it.value.bankISPB }),
            "ChatbotController#ListPaymentOrganizations",
        )

        return HttpResponse.ok(
            ListPaymentOrganizationsResponseTO(
                walletId = walletId.value,
                document = accountOrganization.document.value,
                size = accountOrganization.result.size,
                result = accountOrganization.result,
                otherFinancialInstitutions = accountOrganization.otherFinancialInstitutions.take(9) // NOTE: o máximo suportado são 10 itens, sendo o último adicionado pelo próprio bot (item "Outra instituição").
                    .map {
                        it.copy(
                            title = it.title.take(24), // NOTE: o tamanho máximo do título é 24 caracteres
                        )
                    }.sortedBy {
                        it.title
                    },
            ),
        )
    }

    @Post("/payment-intent")
    fun paymentIntent(
        @Body body: PaymentIntentTO,
        @Header("X-BUTTON-PAYLOAD") buttonPayload: String,
        authentication: Authentication,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val markers = Markers.append("PaymentIntentTO", body).andAppend("buttonPayload", buttonPayload)
            .andAppend("accountId", accountId.value)

        val (_, forecastPeriod) = extractWalletAndForecast(buttonPayload)

        val paymentIntentId =
            itpService.createPaymentIntent(body.toPaymentIntent(accountId), forecastPeriod)
                .getOrElse {
                    LOG.error(markers.and("ErrorMessage" to it), "ChatbotController#PaymentIntent")

                    return when (it) {
                        is ITPServiceError.AccountNotFound -> HttpResponse.badRequest<Unit>()
                        is ITPServiceError.SufficientBalance -> HttpResponse.badRequest(
                            ResponseTO(code = "4002", message = "SufficientBalance"),
                        )

                        else -> HttpResponse.serverError<Unit>()
                    }
                }

        LOG.info(markers.andAppend("token", paymentIntentId.value), "ChatbotController#PaymentIntent")
        return HttpResponse.created(
            PaymentIntentResponseTO(
                token = paymentIntentId.value,
                paymentIntentId = paymentIntentId.value,
            ),
        )
    }

    @Get("/notify-balance")
    fun notifyBalance(
        authentication: Authentication,
    ): HttpResponse<*> {
        val markers = Markers.append("accountId", authentication.toAccountId())

        val response = chatbotService.notifyBalance(authentication.toAccountId())

        response.getOrElse {
            LOG.error(markers.andAppend("error", it), "ChatbotController#NotifyBalance")
            return when (it) {
                is NotifyBalanceError.WalletNotFound -> HttpResponse.badRequest<Unit>()
                is NotifyBalanceError.SufficientBalance -> HttpResponse.badRequest(
                    ResponseTO(code = "4002", message = "SufficientBalance"),
                )

                else -> HttpResponse.serverError<Unit>()
            }
        }

        LOG.info(markers, "ChatbotController#NotifyBalance")
        return HttpResponse.created(Unit)
    }

    @Get("/one-pix-pay/{onePixPayIdValue}")
    fun getOnePixPay(
        @PathVariable onePixPayIdValue: String,
        authentication: Authentication,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val markers = Markers.append("accountId", accountId).andAppend("onePixPayIdValue", onePixPayIdValue)

        return try {
            val onePixPayId = try {
                OnePixPayIdSource.fromIdValue(onePixPayIdValue).toOnePixPayId()
            } catch (e: IllegalArgumentException) {
                LOG.warn(markers, "ChatBotController#OnePixPay", e)
                return HttpResponse.badRequest<Unit>()
            }

            val onePixPay = try {
                onePixPayRepository.find(onePixPayId)
            } catch (e: ItemNotFoundException) {
                LOG.error(markers, "ChatBotController#OnePixPay", e)
                return HttpResponse.notFound<Unit>()
            }
            markers.andAppend("onePixPay", onePixPay)

            // TODO: remover validação de dia atual, pois a job vai passar para EXPIRED
            if (onePixPay.status != OnePixPayStatus.CREATED ||
                !onePixPay.created.toLocalDate().isEqual(getLocalDate())
            ) {
                LOG.error(markers, "ChatBotController#OnePixPay")
                return HttpResponse.notFound<Unit>()
            }

            val wallet = walletService.findWallet(onePixPay.walletId)
            markers.andAppend("walletFounder", wallet.founder.accountId.value)

            if (wallet.founder.accountId != accountId) {
                LOG.error(markers, "ChatBotController#OnePixPay")
                return HttpResponse.notFound<Unit>()
            }

            val bills = onePixPay.billIds.map { billRepository.findBill(it, walletId = wallet.id) }

            onePixPayService.save(onePixPay.asRequested())

            LOG.info(markers, "ChatBotController#OnePixPay")
            HttpResponse.ok(
                OnePixPayResponseTO(
                    qrCode = onePixPay.qrCode,
                    summary = bills.joinToString("\n\n") {
                        "Conta *${it.payee}* de *${
                        NotificationFormatter.buildFormattedAmount(it.amountTotal)
                        }*"
                    },
                    size = bills.size,
                ),
            )
        } catch (e: Exception) {
            LOG.error(markers, "ChatBotController#OnePixPay", e)
            HttpResponse.serverError<Unit>()
        }
    }

    data class PaymentIntentResponseTO(
        @Deprecated("use paymentIntentId instead") val token: String,
        val paymentIntentId: String,
    )

    data class ListPaymentOrganizationsResponseTO(
        val walletId: String,
        val document: String,
        val size: Int,
        @JsonInclude val otherFinancialInstitutions: List<OtherInstitutionsResponseTO>,
        @JsonInclude val result: Map<Int, PaymentOrganizationResult>,
    )

    private fun discoverAccountId(message: BlipChatbotLogTO): AccountId? {
        val fieldValues = listOf(
            message.from,
            message.to,
            message.metadata?.tunnelOwner,
            message.metadata?.tunnelOriginator,
            message.metadata?.tunnelOriginalFrom,
            message.metadata?.tunnelOriginalTo,
        ).requireNoNulls()

        val foundPhoneNumber = fieldValues.firstOrNull { value ->
            value.matches("[0-9]{12,13}@wa.gw.msging.net".toRegex())
        }

        if (foundPhoneNumber != null) {
            return loginService.findProviderUser(
                emailAddress = EmailAddress(email = foundPhoneNumber),
                providerName = ProviderName.WHATSAPP,
            )?.let {
                AccountId(it.id)
            }
        }
        return null
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ChatbotController::class.java)
    }
}

private fun extractWalletAndForecast(value: String?): Pair<WalletId?, ForecastPeriod> {
    if (value.isNullOrBlank()) {
        return Pair(null, ForecastPeriod.TODAY)
    }

    val list = value.split("#")
    val walletId = list.first()
    val forecastPeriod = if (list.size == 2) ForecastPeriod.valueOf(list.last()) else ForecastPeriod.TODAY

    return Pair(WalletId(walletId), forecastPeriod)
}

fun ForecastPeriod.toDescription() = when (this) {
    ForecastPeriod.TODAY -> "para *hoje*"
    ForecastPeriod.SEVEN_DAYS -> "para os *próximos 7 dias*"
    ForecastPeriod.FIFTEEN_DAYS -> "para os *próximos 15 dias*"
    ForecastPeriod.THIRTY_DAYS -> "para os *próximos 30 dias*"
    ForecastPeriod.FIRST_DAY_OF_NEXT_MONTH -> "até o *final do mês*"
    ForecastPeriod.FIRST_DAY_OF_TWO_MONTHS_AHEAD -> "até o *final do próximo mês*"
}

data class PixCopyAndPasteResponseTO(
    val qrCode: String,
    val message: String,
    val title: String? = null,
)

data class PaymentIntentTO(
    val walletId: String,
    val document: String,
    val authorizationServerId: String,
    val routingNo: Long,
    val accountNo: Long,
    val accountDv: String,
    val bankISPB: String?,
    val accountType: String?,
    val bankNo: Long?,
    val authorizationServerName: String,

)

data class OnePixPayResponseTO(
    val qrCode: String,
    val summary: String,
    val size: Number,
)

fun PaymentIntentTO.toPaymentIntent(accountId: AccountId) = CreatePaymentIntentCommand(
    walletId = WalletId(walletId),
    accountId = accountId,
    authorizationServerId = authorizationServerId,
)

data class ChatbotChooseTO(val name: String)

// TODO - isso deveria estar no adaper dos bancos Arbi e BTG
private fun toAccountType(accountType: String?) = when (accountType) {
    "CACC" -> AccountType.CHECKING
    "SVGS" -> AccountType.SAVINGS
    "SLRY" -> AccountType.SALARY
    "TRAN" -> AccountType.PAYMENT
    else -> null
}

@JsonIgnoreProperties(ignoreUnknown = true)
private data class BlipChatbotLogTO(
    val type: String?,
    val id: String?,
    val from: String?,
    val to: String?,
    val metadata: BlipChatbotMetadataTO?,
    val extras: BlipChatbotMetadataTO?,
    val identity: String?,
    val phoneNumber: String?,
    val source: String?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
private data class BlipChatbotMetadataTO(
    @JsonProperty("#stateName") val stateName: String?,
    @JsonProperty("#stateId") val stateId: String?,
    @JsonProperty("#messageId") val messageId: String?,
    @JsonProperty("#previousStateId") val previousStateId: String?,
    @JsonProperty("#previousStateName") val previousStateName: String?,
    @JsonProperty("#tunnel.owner")
    @JsonAlias("tunnel.owner")
    val tunnelOwner: String?,
    @JsonProperty("#tunnel.originator")
    @JsonAlias("tunnel.originator")
    val tunnelOriginator: String?,
    @JsonProperty("#tunnel.originalFrom") val tunnelOriginalFrom: String?,
    @JsonProperty("#tunnel.originalTo") val tunnelOriginalTo: String?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class PixQRCodeRequestTO(
    val amount: Long,
    val walletId: String,
    val message: String,
)