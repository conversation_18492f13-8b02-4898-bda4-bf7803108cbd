package ai.friday.billpayment.adapters.kms

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.integrations.KmsService
import com.amazonaws.services.kms.AWSKMSClient
import com.amazonaws.services.kms.model.DecryptRequest
import com.amazonaws.services.kms.model.EncryptRequest
import io.micronaut.context.annotation.Property
import java.nio.ByteBuffer
import org.apache.commons.codec.binary.Base64

@FridayMePoupe
class KmsAdapter(
    @Property(name = "application.region") private val awsRegion: String,
    @Property(name = "kms.alias") private val kmsAlias: String,
) : KmsService {
    private val base64 = Base64()

    override suspend fun encryptData(text: String): String? {
        val encryptRequest = EncryptRequest().withKeyId(kmsAlias).withPlaintext(ByteBuffer.wrap(text.toByteArray()))

        return AWSKMSClient.builder().withRegion(awsRegion).build().encrypt(encryptRequest).ciphertextBlob?.let {
            return base64.encodeToString(it.array())
        }
    }

    override suspend fun decryptData(encryptedDataVal: String): String = decryptData(encryptedDataVal, kmsAlias)

    override suspend fun decryptData(
        encryptedData: String,
        keyId: String,
    ): String {
        val decryptRequest = DecryptRequest().withKeyId(keyId).withCiphertextBlob(convert(encryptedData))

        return AWSKMSClient.builder().withRegion(awsRegion).build().decrypt(decryptRequest).plaintext?.let {
            return String(it.array())
        } ?: ""
    }

    private fun convert(value: String): ByteBuffer {
        return ByteBuffer.wrap(base64.decode(value))
    }
}