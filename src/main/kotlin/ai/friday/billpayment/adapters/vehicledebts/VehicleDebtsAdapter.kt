package ai.friday.billpayment.adapters.vehicledebts

import ai.friday.billpayment.adapters.parsers.parseListFrom
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.ScopeReader
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.vehicledebts.EnrollVehicleSource
import ai.friday.billpayment.app.vehicledebts.LicensePlate
import ai.friday.billpayment.app.vehicledebts.Vehicle
import ai.friday.billpayment.app.vehicledebts.VehicleDebtsAdapterInterface
import ai.friday.billpayment.app.vehicledebts.VehicleStatus
import ai.friday.morning.log.andAppend
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.uri.UriBuilder
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@ConfigurationProperties("integrations.vehicleDebts")
interface VehicleDebtsConfiguration {
    val username: String
    val password: String
    val retrieveVehiclesPath: String
    val enrollmentUserPath: String
    val enrollmentVehiclePath: String
    val tenant: String
}

@Singleton
open class VehicleDebtsAdapter(
    private val configuration: VehicleDebtsConfiguration,
    @Client(id = "vehicle-debts-service") private val httpClient: RxHttpClient,
    private val scopeReader: ScopeReader,
) : VehicleDebtsAdapterInterface {
    private val logger = LoggerFactory.getLogger(VehicleDebtsAdapter::class.java)

    override fun getAllVehicleByUser(accountId: AccountId, document: Document): Result<List<Vehicle>> {
        val propagatedAccountId = scopeReader.resolveAccountId()
        val markers = Markers.append("accountId", accountId.value)

        try {
            val requestMap = mutableMapOf<String, Any>(
                "tenantName" to configuration.tenant,
                "document" to document.value,
            )

            val url = UriBuilder.of(configuration.retrieveVehiclesPath)
                .expand(requestMap)

            val httpRequest =
                HttpRequest.GET<List<VehicleTO>>(url)
                    .contentType(MediaType.APPLICATION_JSON)
                    .basicAuth(
                        configuration.username,
                        configuration.password,
                    )

            val response =
                httpClient
                    .retrieve(
                        httpRequest,
                        Argument.of(String::class.java),
                        Argument.of(String::class.java),
                    )
                    .firstOrError()
                    .blockingGet()

            logger.info(
                markers.andAppend("response", response).andAppend("propagatedAccountId", propagatedAccountId),
                "VehicleDebtsAdapter#getAllVehicleByUser",
            )

            if (propagatedAccountId != accountId.value) {
                logger.error(markers, "POCScopePropagation")
            }

            val vehicles =
                parseListFrom<VehicleTO>(response).map { vehicle ->
                    Vehicle(
                        licensePlate = LicensePlate(vehicle.licensePlate),
                        description = vehicle.description,
                        status = VehicleStatus.valueOf(vehicle.status),
                        accountId = AccountId(vehicle.clientUserId),
                    )
                }

            return Result.success(vehicles)
        } catch (e: Exception) {
            logger.error(markers, "VehicleDebtsAdapter#getAllVehicleByUser", e)
            return Result.failure(e)
        }
    }

    override fun enrollByUser(accountId: AccountId, document: Document): Result<Unit> {
        val markers = Markers.empty()

        try {
            val request =
                EnrollByUserRequestTO(
                    clientUserId = accountId.value,
                    document = document.value,
                    clientId = configuration.username,
                    source = "System",
                    tenantName = configuration.tenant,

                )
            markers.andAppend("request", request)

            val httpRequest = buildRequest(configuration.enrollmentUserPath, request)

            val response =
                httpClient
                    .retrieve(
                        httpRequest,
                        Argument.of(String::class.java),
                        Argument.of(String::class.java),
                    )
                    .firstOrError()
                    .blockingGet()

            logger.info(markers.andAppend("response", response), "VehicleDebtsAdapter#enrollByUser")
            return Result.success(Unit)
        } catch (e: Exception) {
            logger.error(markers, "VehicleDebtsAdapter#enrollByUser", e)
            return Result.failure(e)
        }
    }

    override fun enrollByVehicle(
        accountId: AccountId,
        document: Document,
        licensePlate: LicensePlate,
        sync: Boolean,
        description: String?,
        source: EnrollVehicleSource,
    ): Result<Unit> {
        val markers = Markers.empty()

        try {
            val request =
                EnrollByVehicleRequestTO(
                    clientUserId = accountId.value,
                    document = document.value,
                    source = source.name,
                    licensePlate = licensePlate.value,
                    sync = true,
                    description = description ?: "",
                    tenantName = configuration.tenant.uppercase(),
                )
            markers.andAppend("request", request)

            val httpRequest = buildRequest(configuration.enrollmentVehiclePath, request)

            val response =
                httpClient
                    .retrieve(
                        httpRequest,
                        Argument.of(String::class.java),
                        Argument.of(String::class.java),
                    )
                    .firstOrError()
                    .blockingGet()

            logger.info(
                markers.andAppend("response", response),
                "VehicleDebtsAdapter#enrollByVehicle",
            )
            return Result.success(Unit)
        } catch (e: Exception) {
            logger.error(markers, "VehicleDebtsAdapter#enrollByVehicle", e)
            return Result.failure(e)
        }
    }

    override fun withdrawByVehicle(
        accountId: AccountId,
        document: Document,
        licensePlate: LicensePlate,
    ): Result<Unit> {
        val markers = Markers.empty()

        try {
            val request =
                WithdrawByVehicleRequestTO(
                    clientUserId = accountId.value,
                    tenantName = configuration.tenant,
                    document = document.value,
                    licensePlate = licensePlate.value,
                    source = "System",
                )
            markers.andAppend("request", request)

            val httpRequest =
                HttpRequest.DELETE(configuration.enrollmentVehiclePath, request)
                    .contentType(MediaType.APPLICATION_JSON)
                    .basicAuth(
                        configuration.username,
                        configuration.password,
                    )

            val response =
                httpClient
                    .retrieve(
                        httpRequest,
                        Argument.of(String::class.java),
                        Argument.of(String::class.java),
                    )
                    .firstOrError()
                    .blockingGet()

            logger.info(
                markers.andAppend("response", response),
                "VehicleDebtsAdapter#withdrawByVehicle",
            )
            return Result.success(Unit)
        } catch (e: Exception) {
            logger.error(markers, "VehicleDebtsAdapter#withdrawByVehicle", e)
            return Result.failure(e)
        }
    }

    private fun <T> buildRequest(path: String, request: T): HttpRequest<T> {
        return HttpRequest.POST(path, request)
            .contentType(MediaType.APPLICATION_JSON)
            .basicAuth(
                configuration.username,
                configuration.password,
            )
    }
}

data class EnrollByUserRequestTO(
    val clientId: String,
    val clientUserId: String,
    val document: String,
    val source: String,
    val tenantName: String,
)

data class EnrollByVehicleRequestTO(
    val clientUserId: String,
    val document: String,
    val source: String,
    val licensePlate: String,
    val sync: Boolean,
    val description: String,
    val tenantName: String,
)

data class WithdrawByVehicleRequestTO(
    val clientUserId: String,
    val tenantName: String,
    val document: String,
    val licensePlate: String,
    val source: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class VehicleTO(
    val licensePlate: String,
    val description: String? = null,
    val status: String,
    val clientUserId: String,
)