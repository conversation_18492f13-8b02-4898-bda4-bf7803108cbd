package ai.friday.billpayment.adapters.html2image

import ai.friday.billpayment.adapters.awslambda.LambdaAdapter
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.integrations.Html2ImageConverter
import com.amazonaws.util.Base64
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton

@Singleton
class Html2ImageWithLambdaConverterAdapter(
    private val lambdaAdapter: LambdaAdapter,
    @Property(name = "integrations.html2Image.lambdaName") val lambdaName: String,
) : Html2ImageConverter {

    override fun convert(compiledHtml: CompiledHtml): ByteArray {
        val lambdaRawResponse = lambdaAdapter.invokeLambdaSynchronously(lambdaName, Html2ImageRequestTO(compiledHtml.value))
        val lambdaResponse = parseObjectFrom<Html2ImageResponseTO>(lambdaRawResponse)
        return Base64.decode(lambdaResponse.imageBase64)
    }
}

private data class Html2ImageRequestTO(
    val htmlContent: String,
)

private data class Html2ImageResponseTO(
    val imageBase64: String,
)