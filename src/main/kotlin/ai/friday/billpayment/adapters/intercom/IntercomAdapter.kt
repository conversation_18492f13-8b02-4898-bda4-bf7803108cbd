package ai.friday.billpayment.adapters.intercom

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.feature.RequiresIntercom
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionReason
import ai.friday.billpayment.app.integrations.CrmContact
import ai.friday.billpayment.app.integrations.CrmRepository
import ai.friday.billpayment.app.integrations.TemporaryCrmContact
import ai.friday.billpayment.app.integrations.TemporaryCrmContactMinimal
import ai.friday.billpayment.app.register.instrumentation.ContactAlreadyExistsException
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.log.andAppend
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.time.ZoneOffset.UTC
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import kotlin.jvm.optionals.getOrNull
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

private const val INTERCOM_EXTERNAL_ID_FIELD = "external_id"
private const val INTERCOM_DOCUMENT_FIELD = "Documento"
private const val INTERCOM_EMAIL_FIELD = "email"
private const val INTERCOM_PHONE_FIELD = "phone"
private const val INTERCOM_NAME_FIELD = "name"
private const val INTERCOM_CUSTOM_ATTRIBUTES_FIELD = "custom_attributes"
private const val INTERCOM_CUSTOM_ROLE_FIELD = "Role"
private const val INTERCOM_REMOVED_FIELD = "Removed"
private const val INTERCOM_CUPOM_PROMO_FIELD = "cupom-promo"
private const val INTERCOM_CUSTOM_GROUPS_FIELD = "Groups"
private const val INTERCOM_CUSTOM_BANK_ACCOUNT_FIELD = "BankAccounts"
private const val INTERCOM_CUSTOM_ACCOUNT_TYPE_FIELD = "Tipo de Conta"
private const val INTERCOM_CUSTOM_IS_CNPJ_FIELD = "ContaCNPJ"
private const val INTERCOM_CUSTOM_CNPJ_WALLETS_FIELD = "CarteirasCNPJ"
private const val INTERCOM_CUSTOM_CPF_WALLETS_FIELD = "CarteirasCPF"
private const val INTERCOM_CUSTOM_OTHER_MEMBERS_WALLET_FIELD = "OutrosMembrosNasCarteiras"
private const val INTERCOM_CUSTOM_SUBSCRIPTION_TYPE = "Assinatura - Tipo"
private const val INTERCOM_CUSTOM_SUBSCRIPTION_ACCESS_CONCESSION_REASON = "Assinatura - Forma de concessão"
private const val INTERCOM_CUSTOM_SUBSCRIPTION_EXPIRATION_DATE = "Assinatura - Expira em"
private const val INTERCOM_CUSTOM_SUBSCRIPTION_AUTO_RENEW = "Assinatura - Renovação automática"

@Singleton
@RequiresIntercom
class IntercomAdapter(
    @param:Client(value = "\${integrations.intercom.host}") private val httpClient: RxHttpClient,
    private val configuration: IntercomConfiguration,
) : CrmRepository {

    override fun upsertContact(contact: CrmContact) {
        searchContact(
            SingleFilterQueryTO(
                QueryFieldTO(
                    field = INTERCOM_EXTERNAL_ID_FIELD,
                    operator = "=",
                    value = contact.accountId.value,
                ),
            ),
        )?.let {
            updateContact(it.id, contact, it.customAttributes)
        } ?: createContact(contact.toContactRequestTO())
    }

    override fun createContact(contact: TemporaryCrmContact) {
        val oldContact = searchContact(
            SingleFilterQueryTO(
                QueryFieldTO(
                    field = INTERCOM_EXTERNAL_ID_FIELD,
                    operator = "=",
                    value = contact.accountId.value,
                ),
            ),
        )
        if (oldContact == null) {
            createContact(contact.toContactRequestTO())
        }
    }

    override fun createContact(contact: TemporaryCrmContactMinimal) {
        val oldContact = searchContact(
            SingleFilterQueryTO(
                QueryFieldTO(
                    field = INTERCOM_EMAIL_FIELD,
                    operator = "=",
                    value = contact.emailAddress.value,
                ),
            ),
        )
        if (oldContact == null) {
            createContact(contact.toContactRequestTO())
        }
    }

    override fun findContact(accountId: AccountId): CrmContact? {
        return searchContact(
            SingleFilterQueryTO(
                QueryFieldTO(
                    field = INTERCOM_EXTERNAL_ID_FIELD,
                    operator = "=",
                    value = accountId.value,
                ),
            ),
        )?.toCrmContact()
    }

    override fun findContact(document: Document): CrmContact? {
        return searchContact(
            SingleFilterQueryTO(
                QueryFieldTO(
                    field = "$INTERCOM_CUSTOM_ATTRIBUTES_FIELD.$INTERCOM_DOCUMENT_FIELD",
                    operator = "=",
                    value = document.value,
                ),
            ),
        )?.toCrmContact()
    }

    override fun publishEvent(accountId: AccountId, eventName: String, customParams: Map<String, Any?>) {
        val contact = searchContact(
            SingleFilterQueryTO(
                QueryFieldTO(
                    field = INTERCOM_EXTERNAL_ID_FIELD,
                    operator = "=",
                    value = accountId.value,
                ),
            ),
        )
            ?: throw IntercomAdapterException("Contact not found with accountId: ${accountId.value}")

        publishContactEvent(contact.id, eventName, customParams)
    }

    override fun publishEvent(emailAddress: EmailAddress, eventName: String, customParams: Map<String, Any?>, retries: Int) {
        var sent = false
        var attempts = 0
        do {
            try {
                val contact = searchContact(
                    SingleFilterQueryTO(
                        QueryFieldTO(
                            field = INTERCOM_EMAIL_FIELD,
                            operator = "=",
                            value = emailAddress.value,
                        ),
                    ),
                )
                    ?: throw IntercomAdapterException("Contact not found with email: ${emailAddress.value}")
                publishContactEvent(contact.id, eventName, customParams)
                sent = true
            } catch (e: IntercomAdapterException) {
                if (attempts < retries) {
                    attempts++
                    Thread.sleep(1000)
                } else {
                    throw e
                }
            }
        } while (!sent)
    }

    private fun createContact(contactRequestTO: ContactRequestTO) {
        val markers = append("contact", contactRequestTO)

        val request = HttpRequest.POST("/contacts", contactRequestTO).bearerAuth(configuration.token)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        val call = httpClient.exchange(
            request,
            Argument.of(ContactResponseTO::class.java),
        )

        try {
            val contactResponseTO = call.firstOrError().blockingGet().body()
            LOG.info(markers.andAppend("contactId", contactResponseTO?.id), "IntercomCreateContact")
        } catch (e: HttpClientResponseException) {
            val response = e.response.getBody(String::class.java).get()
            markers.andAppend("response", response)
            if (response.contains("A contact matching those details already exists")) {
                LOG.error(markers, "IntercomCreateContact", e)
                throw ContactAlreadyExistsException()
            }
            handleException(e, markers, "IntercomCreateContact")
        }
    }

    private fun publishContactEvent(id: String, eventName: String, customParams: Map<String, Any?>) {
        val markers = append("id", id)
            .andAppend("eventName", eventName).andAppend("customParams", customParams)

        try {
            val metadata =
                customParams.filterValues { value ->
                    value === null || value is String || value is Boolean || value is Int || value is Long || value is Double
                }

            if (metadata.size != customParams.size) {
                throw IllegalArgumentException("Metadata values are of not supported type")
            }

            val requestTO =
                ContactEventTO(
                    id = id,
                    eventName = eventName,
                    metadata = metadata,
                )

            val request = HttpRequest.POST("/events", requestTO).bearerAuth(configuration.token)
                .accept(MediaType.APPLICATION_JSON_TYPE)
            val call = httpClient.exchange(request, Argument.STRING)

            call.firstOrError().blockingGet()
            LOG.info(markers, "IntercomPublishContactEvent")
        } catch (e: HttpClientResponseException) {
            handleException(e, markers, "IntercomPublishContactEvent")
        }
    }

    private fun updateContact(id: String, contact: CrmContact, customParams: Map<String, String>) {
        val markers = append("id", id).andAppend("contact", contact)

        val request = HttpRequest.PUT("/contacts/$id", contact.toContactRequestTO(customParams)).bearerAuth(configuration.token)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        val call = httpClient.exchange(request, Argument.STRING)

        try {
            call.firstOrError().blockingGet()
            LOG.info(markers, "IntercomUpdateContact")
        } catch (e: HttpClientResponseException) {
            handleException(e, markers, "IntercomUpdateContact")
        }
    }

    fun unarchive(id: String) {
        val markers = append("id", id)

        val request = HttpRequest.POST("/contacts/$id/unarchive", Unit).bearerAuth(configuration.token)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        val call = httpClient.exchange(request, Argument.STRING)

        try {
            call.firstOrError().blockingGet()
            LOG.info(markers, "unarchive")
        } catch (e: HttpClientResponseException) {
            handleException(e, markers, "IntercomUnarchive")
        }
    }

    fun delete(id: String) {
        val markers = append("id", id)

        val request = HttpRequest.DELETE("/contacts/$id", Unit).bearerAuth(configuration.token)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        val call = httpClient.exchange(request, Argument.STRING)

        try {
            call.firstOrError().blockingGet()
            LOG.info(markers, "IntercomDelete")
        } catch (e: HttpClientResponseException) {
            handleException(e, markers, "IntercomDelete")
        }
    }

    private fun searchContact(query: SingleFilterQueryTO): ContactResponseTO? {
        val markers = append("query", query)

        val request = HttpRequest.POST("/contacts/search", query).bearerAuth(configuration.token)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        val call = httpClient.exchange(
            request,
            Argument.of(ContactListTO::class.java),
        )

        try {
            val response = call.firstOrError().blockingGet().body()
            LOG.info(markers.andAppend("response", response), "IntercomSearchContacts")
            return response.data.singleOrNull().also { contactResponseTO ->
                if (contactResponseTO !== null && contactResponseTO.email == null) {
                    LOG.error(
                        markers.andAppend("contactResponseTO", contactResponseTO)
                            .andAppend("description", "usuario sem email cadastrado no intercom"),
                        "IntercomSearchContacts",
                    )
                }
            }
        } catch (e: HttpClientResponseException) {
            handleException(e, markers, "IntercomSearchContacts")
        }
    }

    fun calculateUserHashes(accountId: AccountId): IntercomUserHashes {
        return IntercomUserHashes(
            web = createHash(configuration.webIdVerificationSecret, accountId.value),
            ios = createHash(configuration.iosIdVerificationSecret, accountId.value),
            android = createHash(configuration.androidIdVerificationSecret, accountId.value),
        )
    }

    private fun createHash(secret: String, message: String): String {
        val algorithm = Mac.getInstance("HmacSHA256")
        val secretKey = SecretKeySpec(secret.toByteArray(), "HmacSHA256")
        algorithm.init(secretKey)

        val hash = algorithm.doFinal(message.toByteArray())
        val result = StringBuffer()
        for (b in hash) {
            result.append(String.format("%02x", b))
        }
        return result.toString()
    }

    private fun handleException(e: HttpClientResponseException, markers: LogstashMarker, message: String): Nothing {
        val errorResponse = e.response.getBody(String::class.java).getOrNull() ?: "NO BODY"
        LOG.error(
            markers.andAppend("errorResponse", errorResponse).andAppend("status", e.status),
            message,
        )
        throw IntercomAdapterException(errorResponse)
    }

    companion object {
        internal val LOG = LoggerFactory.getLogger(IntercomAdapter::class.java)
    }
}

private fun TemporaryCrmContactMinimal.toContactRequestTO(): ContactRequestTO {
    return ContactRequestTO(
        phone = this.mobilePhone?.msisdn,
        email = this.emailAddress.value,
        role = "lead",
        customAttributes = cupomPromo?.let { mapOf(INTERCOM_CUPOM_PROMO_FIELD to it) } ?: emptyMap(),
    )
}

private fun TemporaryCrmContact.toContactRequestTO(): ContactRequestTO {
    return ContactRequestTO(
        accountId = this.accountId.value,
        phone = this.mobilePhone.msisdn,
        email = this.emailAddress.value,
        role = "lead",
        customAttributes = emptyMap(),
    )
}

private fun CrmContact.toContactRequestTO(customParamsToKeep: Map<String, String> = emptyMap()): ContactRequestTO {
    val customAttributes = customParamsToKeep.toMutableMap()

    customAttributes[INTERCOM_REMOVED_FIELD] = removed.toString()
    role?.let { customAttributes[INTERCOM_CUSTOM_ROLE_FIELD] = it.name }
    document?.let { customAttributes[INTERCOM_DOCUMENT_FIELD] = it }

    customAttributes[INTERCOM_CUSTOM_ACCOUNT_TYPE_FIELD] = this.accountType.name
    isCNPJAccount?.let { customAttributes[INTERCOM_CUSTOM_IS_CNPJ_FIELD] = it.toString() }

    buildIntercomList(customAttributes, INTERCOM_CUSTOM_GROUPS_FIELD, groups.map { it.value })
    buildIntercomList(customAttributes, INTERCOM_CUSTOM_BANK_ACCOUNT_FIELD, bankAccount)
    cnpjWallets?.let { list ->
        buildIntercomList(customAttributes, INTERCOM_CUSTOM_CNPJ_WALLETS_FIELD, list.map { it.value })
    }
    cpfWallets?.let { list ->
        buildIntercomList(customAttributes, INTERCOM_CUSTOM_CPF_WALLETS_FIELD, list.map { it.value })
    }
    otherMembersOnWallets?.let { list ->
        buildIntercomList(customAttributes, INTERCOM_CUSTOM_OTHER_MEMBERS_WALLET_FIELD, list.map { it.value })
    }

    subscriptionAutoRenew?.let { customAttributes[INTERCOM_CUSTOM_SUBSCRIPTION_AUTO_RENEW] = it.toString() }

    cupomPromo?.let { customAttributes[INTERCOM_CUPOM_PROMO_FIELD] = it }

    customAttributes[INTERCOM_CUSTOM_SUBSCRIPTION_TYPE] = subscriptionType.name

    subscriptionAccessConcessionReason?.let { accessConcession ->
        customAttributes[INTERCOM_CUSTOM_SUBSCRIPTION_ACCESS_CONCESSION_REASON] = accessConcession.name
    }

    subscriptionEndsAt?.let {
        customAttributes[INTERCOM_CUSTOM_SUBSCRIPTION_EXPIRATION_DATE] = it.format(DateTimeFormatter.ISO_DATE_TIME)
    }

    return ContactRequestTO(
        email = this.emailAddress.value,
        accountId = this.accountId.value,
        role = "user",
        phone = this.mobilePhone?.msisdn,
        name = this.name,
        customAttributes = customAttributes,
        unsubscribedFromEmails = removed,
    )
}

private fun buildIntercomList(customAttributes: MutableMap<String, String>, fieldName: String, elements: List<String>?) {
    if (elements.isNullOrEmpty()) {
        customAttributes[fieldName] = "(none)"
    } else {
        customAttributes[fieldName] = elements.joinToString(separator = "") { "($it)" }
    }
}

private fun ContactResponseTO.toCrmContact() = this.email?.let { email ->
    CrmContact(
        emailAddress = EmailAddress(email),
        accountId = AccountId(accountId),
        name = this.name,
        mobilePhone = this.phone?.let { MobilePhone(it) },
        document = this.customAttributes[INTERCOM_DOCUMENT_FIELD],
        role = this.customAttributes[INTERCOM_CUSTOM_ROLE_FIELD]?.let { Role.valueOf(it) },
        removed = this.customAttributes[INTERCOM_REMOVED_FIELD]?.let { it.toBoolean() } ?: false,
        groups = this.toAccountGroupList(),
        bankAccount = this.toBankAccountList(),
        accountType = this.customAttributes[INTERCOM_CUSTOM_ACCOUNT_TYPE_FIELD]
            ?.let {
                if (it == "true") { // NOTE: Em STG esse campo foi criado como booleano por erro. Em PROD é string, e vai fazer o parse do enum corretamente
                    UserAccountType.BASIC_ACCOUNT
                } else {
                    UserAccountType.valueOf(it)
                }
            }
            ?: UserAccountType.FULL_ACCOUNT,
        isCNPJAccount = this.customAttributes[INTERCOM_CUSTOM_IS_CNPJ_FIELD]?.let { it.toBoolean() },
        cnpjWallets = this.toWalletIdList(INTERCOM_CUSTOM_CNPJ_WALLETS_FIELD),
        cpfWallets = this.toWalletIdList(INTERCOM_CUSTOM_CPF_WALLETS_FIELD),
        otherMembersOnWallets = this.toAccountIdList(INTERCOM_CUSTOM_OTHER_MEMBERS_WALLET_FIELD),
        subscriptionType = this.customAttributes[INTERCOM_CUSTOM_SUBSCRIPTION_TYPE]?.let { SubscriptionType.valueOf(it) } ?: SubscriptionType.PIX,
        subscriptionAccessConcessionReason = this.customAttributes[INTERCOM_CUSTOM_SUBSCRIPTION_ACCESS_CONCESSION_REASON]?.let { InAppSubscriptionReason.valueOf(it) },
        subscriptionEndsAt = this.customAttributes[INTERCOM_CUSTOM_SUBSCRIPTION_EXPIRATION_DATE],

    )
}

fun ContactResponseTO.toAccountIdList(fieldName: String): List<AccountId> {
    return this.customAttributes[fieldName]?.let { rawValue ->
        splitStringList(rawValue).map { walletId ->
            AccountId(walletId)
        }
    } ?: listOf()
}

fun ContactResponseTO.toWalletIdList(fieldName: String): List<WalletId> {
    return this.customAttributes[fieldName]?.let { rawValue ->
        splitStringList(rawValue).map { walletId ->
            WalletId(walletId)
        }
    } ?: listOf()
}

fun ContactResponseTO.toAccountGroupList(): List<AccountGroup> {
    return this.customAttributes[INTERCOM_CUSTOM_GROUPS_FIELD]?.let { rawValue ->
        splitStringList(rawValue).mapNotNull { group ->
            AccountGroup.find(group)
        }
    } ?: listOf()
}

fun ContactResponseTO.toBankAccountList(): List<String> {
    return this.customAttributes[INTERCOM_CUSTOM_BANK_ACCOUNT_FIELD]?.let { rawValue ->
        splitStringList(rawValue)
    } ?: listOf()
}

fun splitStringList(list: String): List<String> {
    return list.drop(1).dropLast(1).split(")(").toList()
}

@RequiresIntercom
@ConfigurationProperties("integrations.intercom")
class IntercomConfiguration {
    lateinit var host: String
    lateinit var token: String
    lateinit var webIdVerificationSecret: String
    lateinit var iosIdVerificationSecret: String
    lateinit var androidIdVerificationSecret: String
}

data class IntercomUserHashes(val web: String, val ios: String, val android: String)

data class ContactEventTO(
    val id: String,

    @JsonProperty("event_name")
    val eventName: String,

    @JsonProperty("created_at")
    val createdAt: Long = ZonedDateTime.now().withZoneSameInstant(UTC).toEpochSecond(),

    val metadata: Map<String, Any?>,
)

class IntercomAdapterException(message: String) : RuntimeException(message)

data class ContactRequestTO(
    @JsonProperty(INTERCOM_EMAIL_FIELD)
    val email: String,

    val role: String,

    @JsonProperty(INTERCOM_EXTERNAL_ID_FIELD)
    val accountId: String? = null,

    @JsonProperty(INTERCOM_PHONE_FIELD)
    val phone: String? = null,

    @JsonProperty(INTERCOM_NAME_FIELD)
    val name: String? = null,

    @JsonProperty("unsubscribed_from_emails")
    val unsubscribedFromEmails: Boolean? = null,

    @JsonProperty(INTERCOM_CUSTOM_ATTRIBUTES_FIELD)
    val customAttributes: Map<String, String>?,
)

data class SingleFilterQueryTO(
    val query: QueryFieldTO,
)

data class QueryFieldTO(
    val field: String,
    val operator: String,
    val value: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ContactListTO(
    val data: List<ContactResponseTO>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ContactResponseTO(
    val id: String,

    @JsonProperty(INTERCOM_EMAIL_FIELD)
    val email: String?,

    val role: String,

    @JsonProperty(INTERCOM_EXTERNAL_ID_FIELD)
    val accountId: String,

    @JsonProperty(INTERCOM_PHONE_FIELD)
    val phone: String? = null,

    @JsonProperty(INTERCOM_NAME_FIELD)
    val name: String? = null,

    @JsonProperty(INTERCOM_CUSTOM_ATTRIBUTES_FIELD)
    val customAttributes: Map<String, String> = mutableMapOf(),
)