package ai.friday.billpayment.adapters.intercom

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.CrmContact
import ai.friday.billpayment.app.integrations.CrmRepository
import ai.friday.billpayment.app.integrations.TemporaryCrmContact
import ai.friday.billpayment.app.integrations.TemporaryCrmContactMinimal
import ai.friday.billpayment.markers
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory

@Singleton
@Requires(missingBeans = [CrmRepository::class])
class NoopCrmRepository : CrmRepository {
    override fun upsertContact(contact: CrmContact) {}

    override fun createContact(contact: TemporaryCrmContact) {}

    override fun createContact(contact: TemporaryCrmContactMinimal) {}

    override fun findContact(accountId: AccountId) = null

    override fun findContact(document: Document) = null

    override fun publishEvent(accountId: AccountId, eventName: String, customParams: Map<String, Any?>) {
        logger.debug(
            markers(
                "accountId" to accountId.value,
                "eventName" to eventName,
                "customParams" to customParams,
            ),
            "noopCrmRepository#publishEvent",
        )
    }

    override fun publishEvent(emailAddress: EmailAddress, eventName: String, customParams: Map<String, Any?>, retries: Int) {
        logger.debug(
            markers(
                "emailAddress" to emailAddress,
                "eventName" to eventName,
                "customParams" to customParams,
            ),
            "noopCrmRepository#publishEvent",
        )
    }

    private val logger = LoggerFactory.getLogger(NoopCrmRepository::class.java)
}