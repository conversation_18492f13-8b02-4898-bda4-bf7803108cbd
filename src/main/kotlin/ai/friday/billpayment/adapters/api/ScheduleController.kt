package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.and
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.bill.schedule.ScheduleBillService
import ai.friday.billpayment.app.bill.schedule.ScheduleResult
import ai.friday.billpayment.app.integrations.CreditCardFeeCalculatorService
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.payment.MultiplePaymentMethodsDetail
import ai.friday.billpayment.app.payment.PaymentMethodsDetail
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.ScheduleStrategy
import ai.friday.billpayment.app.payment.ScheduleTo
import ai.friday.billpayment.app.payment.pinCode.PinCode
import ai.friday.billpayment.app.security.Fingerprint
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.log
import ai.friday.billpayment.plus
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonInclude
import io.micronaut.core.annotation.Introspected
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.RequestAttribute
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.validation.Validated
import jakarta.validation.Valid
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Validated
@Secured(Role.Code.OWNER)
@Controller("/")
@FridayMePoupe
@Version("2")
class ScheduleController(
    private val updateBillService: UpdateBillService,
    private val scheduleBillService: ScheduleBillService,
    private val creditCardFeeCalculatorService: CreditCardFeeCalculatorService,
) {

    @Post("/v2/schedule")
    fun scheduleBills(
        @Body @Valid
        body: BillsToScheduleTO,
        authentication: Authentication,
        @RequestAttribute("X-Fingerprint") fingerprint: Fingerprint? = null,
    ): HttpResponse<*> {
        val markers = log(
            "request" to body,
            "fingerprint" to fingerprint,
            "walletId" to authentication.getWallet().id.value,
        )

        try {
            val result = scheduleBillService.schedulePayments(
                paymentWallet = authentication.getWallet(),
                billIdsWithMethods = body.bills.map {
                    Pair(
                        BillId(it.billId),
                        buildPaymentMethodsDetailList(authentication.asWalletMember(), it.methods),
                    )
                },
                accountId = authentication.toAccountId(),
                actionSource = authentication.getActionSource(),
                scheduleStrategy = body.scheduleTo.toScheduleStrategy(),
                fingerprint = fingerprint,
                pinCode = body.pinCode?.let { PinCode(it) },
            )

            return when (result) {
                ScheduleResult.InvalidPinCode -> {
                    LOG.info(markers.plus("pinCode" to "invalid"), "ScheduleController")
                    HttpResponse.badRequest(ResponseTO("4000", "Código de segurança inválido"))
                }

                ScheduleResult.PinCodeMaxAttemptsReached -> {
                    LOG.info(markers.plus("pinCode" to "maxAttemptsReached"), "ScheduleController")
                    HttpResponse.badRequest(ResponseTO("4001", "Máximo de tentativas de código de segurança atingido"))
                }

                is ScheduleResult.Success -> {
                    val scheduleResults = result.billIds.map { (billId, isScheduled) -> ScheduleResultTO(billId.value, isScheduled) }

                    LOG.info(
                        markers.and(
                            "result" to scheduleResults,
                            "scheduled" to scheduleResults.filter { it.scheduled }.size,
                            "notScheduled" to scheduleResults.filterNot { it.scheduled }.size,
                        ),
                        "ScheduleController",
                    )
                    HttpResponse.ok(ScheduledBillsTO(scheduleResults))
                }
            }
        } catch (err: Throwable) {
            LOG.error(markers, "ScheduleController", err)

            return when (err) {
                is IllegalArgumentException -> HttpResponse.badRequest(err.message)
                else -> HttpResponse.serverError<String>()
            }
        }
    }

    private fun buildPaymentMethodsDetailList(
        member: Member,
        methods: List<ScheduleWithMethodsDetailTO>,
    ): PaymentMethodsDetail {
        val paymentMethodsDetail = methods.map {
            when (PaymentMethodType.valueOf(it.type)) {
                PaymentMethodType.CREDIT_CARD -> {
                    val fee = creditCardFeeCalculatorService.getFee(it.installments ?: 1) ?: throw IllegalArgumentException("Número de parcelas inválido")

                    PaymentMethodsDetailWithCreditCard(
                        paymentMethodId = AccountPaymentMethodId(value = it.paymentMethodId),
                        netAmount = it.amount,
                        feeAmount = creditCardFeeCalculatorService.calculateFeeAmount(
                            member.accountId,
                            it.amount,
                            it.installments ?: 1,
                            it.calculationId,
                        ).getOrElse { e ->
                            throw IllegalArgumentException(e)
                        },
                        installments = it.installments ?: 1,
                        fee = fee,
                        calculationId = it.calculationId,
                    )
                }

                PaymentMethodType.BALANCE -> PaymentMethodsDetailWithBalance(
                    paymentMethodId = AccountPaymentMethodId(value = it.paymentMethodId),
                    amount = it.amount,
                    calculationId = it.calculationId,
                )

                PaymentMethodType.EXTERNAL -> throw IllegalArgumentException("Não pode agendar com pagamento externo pela API")
            }
        }

        if (paymentMethodsDetail.isEmpty()) {
            throw IllegalArgumentException("Metodo de pagamento deve ser informado")
        }

        return if (paymentMethodsDetail.size == 1) {
            paymentMethodsDetail[0]
        } else {
            MultiplePaymentMethodsDetail(methods = paymentMethodsDetail)
        }
    }

    @Delete("/schedule/{billId}")
    fun cancelSchedule(@PathVariable billId: String, authentication: Authentication): HttpResponse<*> {
        val marker = append("accountId", authentication.name)
            .andAppend("billId", billId)
            .andAppend("walletId", authentication.getWallet().id.value)
        return try {
            val result = updateBillService.userCancelScheduledPayment(
                walletId = authentication.toWalletId(),
                billId = BillId(billId),
                member = authentication.asWalletMember(),
                actionSource = authentication.getActionSource(),
            )
            HttpResponse.ok(ScheduleResultTO(billId, result))
        } catch (e: ItemNotFoundException) {
            LOG.error(
                marker.andAppend("httpStatus", HttpStatus.NOT_FOUND.code)
                    .andAppend("error", e.message),
                "CancelSchedule",
            )
            StandardHttpResponses.billNotFound()
        } catch (e: Exception) {
            LOG.error(
                marker.andAppend("httpStatus", HttpStatus.INTERNAL_SERVER_ERROR.code),
                "CancelSchedule",
                e,
            )
            StandardHttpResponses.serverError()
        }
    }

    private fun ScheduleTo.toScheduleStrategy(): ScheduleStrategy {
        return when (this) {
            ScheduleTo.TODAY, ScheduleTo.ASAP -> ScheduleStrategy.ofAsap()
            ScheduleTo.DUE_DATE -> ScheduleStrategy.ofDueDate()
            ScheduleTo.TO_DATE -> ScheduleStrategy.ofDueDate() // FIXME - não aceitamos via api agendamentos para data especifica
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ScheduleController::class.java)
    }
}

@JsonInclude(JsonInclude.Include.ALWAYS)
data class ScheduledBillsTO(
    val billList: List<ScheduleResultTO>,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class ScheduleResultTO(
    val billId: String,
    val scheduled: Boolean,
)

@Introspected
@JsonInclude(JsonInclude.Include.ALWAYS)
data class BillsToScheduleLegacyTO(
    val billList: List<String>,
    val scheduleTo: ScheduleTo = ScheduleTo.DUE_DATE,
)

@Introspected
@JsonInclude(JsonInclude.Include.ALWAYS)
data class BillsToScheduleTO(
    val bills: List<BillsToScheduleWithMethodsDetailTO>,
    val scheduleTo: ScheduleTo = ScheduleTo.DUE_DATE,
    val pinCode: String? = null,
)

data class BillsToScheduleWithMethodsDetailTO(
    val billId: String,
    val methods: List<ScheduleWithMethodsDetailTO>,
)

data class ScheduleWithMethodsDetailTO(
    val type: String,
    val paymentMethodId: String,
    val amount: Long,
    val installments: Int? = null,
    val calculationId: String?,
)