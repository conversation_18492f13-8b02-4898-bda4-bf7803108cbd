package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.intercom.IntercomAdapter
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.feature.RequiresIntercom
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.OWNER, Role.Code.GUEST)
@Controller("/intercom")
@Version("2")
@Singleton
@RequiresIntercom
class IntercomController(private val intercomAdapter: IntercomAdapter) {

    @Get("/userHashes")
    fun getUserHashes(authentication: Authentication): HttpResponse<*> {
        val (web, ios, android) = intercomAdapter.calculateUserHashes(authentication.toAccountId())
        LOG.info(Markers.append("accountId", authentication.toAccountId()), "IntercomGetUserHashes")
        return HttpResponse.ok(IntercomUserHashesResponseTO(web = web, ios = ios, android = android))
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(IntercomAdapter::class.java)
    }
}

data class IntercomUserHashesResponseTO(val web: String, val ios: String, val android: String)