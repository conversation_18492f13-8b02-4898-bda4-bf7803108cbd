package ai.friday.billpayment.adapters.api

import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.http.HttpRequest
import io.micronaut.http.MutableHttpResponse
import io.micronaut.http.annotation.Filter
import io.micronaut.http.annotation.Filter.MATCH_ALL_PATTERN
import io.micronaut.http.filter.HttpServerFilter
import io.micronaut.http.filter.ServerFilterChain
import io.micronaut.security.authentication.Authentication
import io.opentracing.Tracer
import io.reactivex.Flowable
import io.reactivex.schedulers.Schedulers
import java.util.UUID
import net.logstash.logback.marker.Markers.append
import org.reactivestreams.Publisher
import org.slf4j.LoggerFactory

@Filter(MATCH_ALL_PATTERN)
@Requires(notEnv = ["test"])
class XRequestIdFilter(
    private val tracer: Tracer,
    @Property(name = "log.reject-paths") private val rejectPaths: List<String>,
) : HttpServerFilter {
    override fun doFilter(request: HttpRequest<*>, chain: ServerFilterChain): Publisher<MutableHttpResponse<*>> {
        val xRequestId = if (request.headers.contains("X-Request-Id")) {
            request.headers["X-Request-Id"]
        } else {
            UUID.randomUUID().toString()
        }

        val authenticationName = with(request.getUserPrincipal(Authentication::class.java)) {
            if (isPresent) {
                get().name
            } else {
                "anonymous"
            }
        }

        val span = tracer.activeSpan()
        span?.setTag("xRequestId", xRequestId)

        return Flowable.fromCallable { true }
            .subscribeOn(Schedulers.io())
            .switchMap { chain.proceed(request) }
            .doOnNext { res ->
                if (request.uri.toString() !in rejectPaths) {
                    val marker = append("method", request.methodName)
                        .andAppend("uri", request.uri)
                        .andAppend("status", res.status().code)
                        .andAppend("xRequestId", xRequestId)
                        .andAppend("authentication", authenticationName)
                    res.headers.add("X-Request-Id", xRequestId)
                    LOG.debug(marker, "XRequestIdFilter")
                }
            }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(XRequestIdFilter::class.java)
    }
}