package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.integrations.AccountRepository
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import org.slf4j.LoggerFactory

@Secured(BACKOFFICE)
@Controller("/backoffice/paymentMethod")
@FridayMePoupe
class BackofficePaymentMethodController(
    private val accountRepository: AccountRepository,
) {

    @Get("/list/account/{accountId}")
    fun getPaymentMethods(
        @PathVariable accountId: String,
    ): HttpResponse<List<PaymentMethodTO>> {
        val paymentMethods = accountRepository.findAccountPaymentMethodsByAccountId(AccountId(accountId))
            .map { convertFrom(it) }

        return HttpResponse.ok(paymentMethods)
    }

    @Put("/account/{accountId}/paymentMethod/{paymentMethodId}")
    fun updateCreditCardPaymentMethodRisk(
        @PathVariable accountId: String,
        @PathVariable paymentMethodId: String,
        @Body body: UpdateRiskTO,
    ): HttpResponse<PaymentMethodTO> {
        accountRepository.updateCreditCardPaymentMethodRisk(AccountId(accountId), AccountPaymentMethodId(paymentMethodId), RiskLevel.valueOf(body.riskLevel))

        val updatedPaymentMethod = accountRepository.findAccountPaymentMethodsByAccountId(AccountId(accountId))
            .find { it.id.value == paymentMethodId }
            ?: throw IllegalArgumentException("Payment method not found")

        return HttpResponse.ok(convertFrom(updatedPaymentMethod))
    }

    private fun convertFrom(accountPaymentMethod: AccountPaymentMethod): PaymentMethodTO {
        return when (val paymentMethodType = accountPaymentMethod.method) {
            is CreditCard -> {
                val creditCardTO = paymentMethodType.toCreditCardTO(accountPaymentMethod.status.name)
                PaymentMethodTO(
                    id = accountPaymentMethod.id.value,
                    type = PaymentMethodType.CREDIT_CARD.name,
                    creditCard = creditCardTO,
                )
            }

            is InternalBankAccount -> {
                val bankDetailsTO = paymentMethodType.toInternalBankAccountTO()
                PaymentMethodTO(
                    id = accountPaymentMethod.id.value,
                    type = PaymentMethodType.BALANCE.name,
                    bankDetails = bankDetailsTO,
                )
            }

            else -> throw IllegalArgumentException("")
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(BackofficePaymentMethodController::class.java)
    }
}

data class UpdateRiskTO(
    val riskLevel: String,
)