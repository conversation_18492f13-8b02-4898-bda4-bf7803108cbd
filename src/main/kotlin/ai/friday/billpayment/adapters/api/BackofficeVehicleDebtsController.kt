package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.vehicledebts.VehicleDebtsAdapter
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.vehicledebts.EnrollVehicleSource
import ai.friday.billpayment.app.vehicledebts.LicensePlate
import ai.friday.billpayment.app.vehicledebts.VehicleDebts
import ai.friday.billpayment.app.vehicledebts.VehicleDebtsService
import ai.friday.morning.log.andAppend
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.validation.Validated
import jakarta.validation.Valid
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Validated
@Secured(BACKOFFICE)
@Controller("/backoffice/vehicleDebts")
@VehicleDebts
class BackofficeVehicleDebtsController(private val vehicleDebtsService: VehicleDebtsService) {
    private val logger = LoggerFactory.getLogger(VehicleDebtsAdapter::class.java)

    @Post("/enrollment/user")
    fun enrollByUser(
        @Body
        @Valid
        body: VehicleDebtsEnrollmentByUserTO,
    ): HttpResponse<*> {
        val markers = Markers.append("body", body)

        val result = vehicleDebtsService.enrollByUser(
            accountId = AccountId(body.accountId),
            document = Document(body.document),
        ).getOrElse {
            logger.error(markers.andAppend("error", it), "BackofficeVehicleDebtsController#enrollByUser")
            return HttpResponse.badRequest("")
        }

        return HttpResponse.created(result)
    }

    @Post("/enrollment/vehicle")
    fun enrollByVehicle(
        @Body
        @Valid
        body: VehicleDebtsEnrollmentByVehicleTO,
    ): HttpResponse<*> {
        val markers = Markers.append("body", body)

        val result = vehicleDebtsService.enrollByVehicle(
            accountId = AccountId(body.accountId),
            licensePlate = LicensePlate(body.licensePlate),
            sync = body.sync,
            description = body.description,
            source = EnrollVehicleSource.BACKOFFICE,
        ).getOrElse {
            logger.error(markers.andAppend("error", it), "BackofficeVehicleDebtsController#enrollByVehicle")
            return HttpResponse.badRequest("")
        }

        return HttpResponse.created(result)
    }
}

data class VehicleDebtsEnrollmentByUserTO(
    val accountId: String,
    val document: String,
)

data class VehicleDebtsEnrollmentByVehicleTO(
    val accountId: String,
    val licensePlate: String,
    val sync: Boolean = false,
    val description: String?,
)