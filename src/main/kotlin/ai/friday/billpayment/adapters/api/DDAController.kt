package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.messaging.DDABillTO
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.message.QueueMessageBatch
import ai.friday.billpayment.markers
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import org.slf4j.LoggerFactory

@Controller("/dda")
@Secured(Role.Code.FRIDAY_CALLBACK)
class DDAController(
    private val messagePublisher: MessagePublisher,
    private val sqsConfiguration: SQSMessageHandlerConfiguration,
) {
    private val logger = LoggerFactory.getLogger(DDAController::class.java)

    private val mapper: ObjectMapper = jacksonObjectMapper()
        .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
        .registerModule(JavaTimeModule())

    @Post
    fun receiveDDABill(@Body ddaBillTO: List<DDABillTO>): HttpResponse<*> {
        val markers = markers("ddaBillTO" to ddaBillTO.size)
        val messages = ddaBillTO.map { mapper.writeValueAsString(it) }

        return try {
            messagePublisher.sendMessageBatch(
                QueueMessageBatch(
                    queueName = sqsConfiguration.ddaBills,
                    messages = messages,
                ),
            )

            logger.info(markers, "DDAController#receiveDDABill")

            HttpResponse.noContent<Unit>()
        } catch (e: Exception) {
            logger.error(markers, "DDAController#receiveDDABill", e)
            HttpResponse.serverError(mapOf("status" to "error", "message" to "Failed to process DDA bill"))
        }
    }
}