package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.auth.getMainRole
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.morning.log.andAppend
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.OWNER, Role.Code.GUEST)
@Controller("/activity")
@Version("2")
class SystemActivityController(
    private val systemActivityService: SystemActivityService,
) {
    @Put("/{name}")
    fun putActivity(
        authentication: Authentication,
        @PathVariable name: String,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val activity = Activity.find(name)
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("role", authentication.getMainRole())
            .andAppend("activity", activity)

        return try {
            if (activity == null) {
                LOG.warn(markers, "PutActivity")
                return HttpResponse.badRequest<Unit>()
            }

            when (activity) {
                Activity.SIGNED_IN -> systemActivityService.setLastSignedIn(accountId)
                Activity.VIEWED_CATEGORY_SUMMARY -> systemActivityService.setViewedCategorySummary(accountId)
                Activity.SUBSCRIPTION_NOT_SUPPORTED_BY_DEVICE -> systemActivityService.setSubscriptionNotSupportedByDevice(accountId)
            }

            LOG.info(markers, "PutActivity")
            HttpResponse.accepted<Unit>()
        } catch (e: Exception) {
            LOG.error(markers, "PutActivity", e)
            HttpResponse.serverError<Unit>()
        }
    }

    @Put("/survey/replied")
    fun putSurveyReplied(authentication: Authentication, @Body reply: SurveyReplyTO): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("role", authentication.getMainRole())

        return try {
            systemActivityService.updateSurveyReplied(accountId, getObjectMapper().writeValueAsString(reply))
            LOG.info(markers, "PutSurveyReplied")
            HttpResponse.accepted<Unit>()
        } catch (e: Exception) {
            LOG.error(markers, "PutSurveyReplied", e)
            HttpResponse.serverError<Unit>()
        }
    }

    @Get("/survey/has-replied")
    fun getSurveyReplied(authentication: Authentication): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("role", authentication.getMainRole())

        return try {
            val replied = systemActivityService.checkSurveyReplied(accountId)

            LOG.info(markers.andAppend("replied", replied), "GetSurveyReplied")
            HttpResponse.ok(
                object {
                    val replied = replied
                },
            )
        } catch (e: Exception) {
            LOG.error(markers, "GetSurveyReplied", e)
            HttpResponse.serverError<Unit>()
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SystemActivityController::class.java)
    }
}

enum class Activity(val value: String) {
    SIGNED_IN("signed-in"),
    VIEWED_CATEGORY_SUMMARY("viewed-category-summary"),
    SUBSCRIPTION_NOT_SUPPORTED_BY_DEVICE("subscription-not-supported-by-device"),
    ;

    companion object {
        fun find(activity: String): Activity? {
            return Activity.entries.firstOrNull {
                it.value == activity
            }
        }
    }
}

data class SurveyReplyTO(
    val expenses: List<String>?,
    val bankPreference: String?,
    val billsManagementQuantity: String?,
    val billsManagementPeople: List<String>?,
)