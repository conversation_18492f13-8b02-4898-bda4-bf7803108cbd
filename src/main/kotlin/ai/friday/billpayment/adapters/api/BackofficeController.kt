package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.arbi.ArbiAdapter
import ai.friday.billpayment.adapters.arbi.ArbiPixQRCodeAdapter
import ai.friday.billpayment.adapters.btg.BtgITPAdapter
import ai.friday.billpayment.adapters.dynamodb.FeaturesDbRepository
import ai.friday.billpayment.adapters.jobs.SendFinancialReportsService
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.ZipCode
import ai.friday.billpayment.app.account.AccountClosureDetails
import ai.friday.billpayment.app.account.AccountClosureReason
import ai.friday.billpayment.app.account.AccountGroupAddOrRemove
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.CloseAccountError
import ai.friday.billpayment.app.account.CloseAccountResult
import ai.friday.billpayment.app.account.CloseAccountService
import ai.friday.billpayment.app.account.CloseAccountStep
import ai.friday.billpayment.app.account.CloseAccountStepStatus
import ai.friday.billpayment.app.account.CloseAccountStepType
import ai.friday.billpayment.app.account.CloseAccountStepTypeCloseFounderWallet
import ai.friday.billpayment.app.account.CloseAccountStepTypeDisableNotification
import ai.friday.billpayment.app.account.CloseAccountStepTypeRemoveFromDDA
import ai.friday.billpayment.app.account.CloseAccountStepTypeRemoveFromUserPool
import ai.friday.billpayment.app.account.CloseFounderWalletCloseAccountStep
import ai.friday.billpayment.app.account.ClosePartialAccountError
import ai.friday.billpayment.app.account.CloseWalletError
import ai.friday.billpayment.app.account.CloseWalletStep
import ai.friday.billpayment.app.account.CloseWalletStepStatus
import ai.friday.billpayment.app.account.CloseWalletStepType
import ai.friday.billpayment.app.account.CloseWalletStepTypeCloseExternalAccountLater
import ai.friday.billpayment.app.account.CloseWalletStepTypeCloseExternalAccountNow
import ai.friday.billpayment.app.account.CloseWalletStepTypeDeleteWalletPixKey
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.Role.Code.ADMIN
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE_ADMIN
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.account.SystemActivityType
import ai.friday.billpayment.app.account.UpdateAccountError
import ai.friday.billpayment.app.account.UpdateAccountService
import ai.friday.billpayment.app.account.toSystemActivityKey
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.ExternalBankAccountService
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.EmailBill
import ai.friday.billpayment.app.bill.MailboxAddBillError
import ai.friday.billpayment.app.bill.PaymentFailed
import ai.friday.billpayment.app.bill.PixCopyAndPaste
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.feature.Features
import ai.friday.billpayment.app.feature.FeaturesType
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.fix
import ai.friday.billpayment.app.formatBrazilCurrency
import ai.friday.billpayment.app.inappsubscription.GrantGratuityResult
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionService
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillService
import ai.friday.billpayment.app.integrations.EmailService
import ai.friday.billpayment.app.integrations.LoginRepository
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.PixPaymentService
import ai.friday.billpayment.app.integrations.SystemActivityRepository
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.integrations.UserPoolUsernameNotFoundException
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.integrations.ZipCodeService
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.app.metrics.push
import ai.friday.billpayment.app.msisdnauth.createTemporaryEmail
import ai.friday.billpayment.app.msisdnauth.createWhatsappEmail
import ai.friday.billpayment.app.notification.PushNotificationService
import ai.friday.billpayment.app.payment.CheckoutLocator
import ai.friday.billpayment.app.payment.ErrorSource
import ai.friday.billpayment.app.payment.RefundBoletoResponse
import ai.friday.billpayment.app.payment.RefundBoletoService
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.bill
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyService
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.isValid
import ai.friday.billpayment.app.referrer.AccountReferrerResult
import ai.friday.billpayment.app.referrer.ReferrerService
import ai.friday.billpayment.app.register.instrumentation.DefaultCrmService
import ai.friday.billpayment.app.register.instrumentation.OnePixPayMetric
import ai.friday.billpayment.app.register.kyc.KycService
import ai.friday.billpayment.app.reports.CreditCardCashInUsageService
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.parseDate
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormatBR
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import arrow.core.left
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonTypeInfo
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.http.annotation.QueryValue
import io.micronaut.http.multipart.CompletedFileUpload
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.via1.communicationcentre.app.integrations.EmailSenderService
import io.via1.communicationcentre.app.receipt.Action
import io.via1.communicationcentre.app.receipt.Receipt
import io.via1.communicationcentre.app.receipt.SQSEvent
import java.io.BufferedReader
import java.io.InputStreamReader
import java.time.Duration
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(BACKOFFICE, ADMIN, BACKOFFICE_ADMIN)
@Controller("/backoffice")
@FridayMePoupe
class BackofficeController(
    private val userPoolAdapter: UserPoolAdapter,
    private val billService: BillService,
    private val closeAccountService: CloseAccountService,
    private val referrerService: ReferrerService,
    private val refundBoletoService: RefundBoletoService,
    private val updateAccountService: UpdateAccountService,
    private val zipCodeService: ZipCodeService,
    private val systemActivityRepository: SystemActivityRepository,
    private val messageProcessor: EmailService,
    private val externalBankAccountService: ExternalBankAccountService,
    private val accountRepository: AccountRepository,
    private val accountService: AccountService,
    private val accountRegisterRepository: AccountRegisterRepository,
    private val kycService: KycService,
    private val pixKeyManagement: PixKeyManagement,
    private val ddaService: DDAService,
    private val defaultCrmService: DefaultCrmService,
    private val walletRepository: WalletRepository,
    private val arbiAdapter: ArbiAdapter,
    private val arbiPixPaymentAdapter: PixPaymentService,
    private val featuresRepository: FeaturesDbRepository,
    private val pixKeyService: PixKeyService,
    private val creditCardCashInUsageService: CreditCardCashInUsageService,
    private val emailSender: EmailSenderService,
    private val qrCodeService: ArbiPixQRCodeAdapter,
    private val btgITPAdapter: BtgITPAdapter,
    private val walletService: WalletService,
    private val deviceFingerprintService: DeviceFingerprintService,
    private val sendFinancialReportsService: SendFinancialReportsService,
    private val loginRepository: LoginRepository,
    private val inAppSubscriptionService: InAppSubscriptionService,
    private val systemActivityService: SystemActivityService,
    private val pushNotificationService: PushNotificationService,
    private val billEventPublisher: BillEventPublisher,
    private val billEventRepository: BillEventRepository,
    private val transactionRepository: TransactionRepository,
    private val checkoutLocator: CheckoutLocator,

) {
    @field:Property(name = "email.notification.email")
    lateinit var from: String

    data class ArbiPayBillTO(
        val amount: Long,
        val digitableLine: String,
    )

    data class PixKeyRegisterTO(
        val pixKey: String,
    )

    @Post("/wallet/{walletId}/pix/key")
    fun createPixKey(@PathVariable walletId: String, @Body body: PixKeyRegisterTO): HttpResponse<*> {
        val markers = append("body", body).andAppend("walletId", walletId)
        val logName = "BackofficeCreatePixKey"
        return try {
            val pixKey = if (body.pixKey.isEmpty()) {
                PixKey(value = "", type = PixKeyType.EVP)
            } else {
                PixKey(value = body.pixKey, type = PixKeyType.fromString(body.pixKey) ?: throw IllegalArgumentException("Invalid pix key"))
            }

            if (pixKey.value.isNotEmpty() && !pixKey.isValid()) {
                markers.andAppend("reason", "Invalid pix key")
                throw IllegalArgumentException("Invalid pix key")
            }

            pixKeyService.createPixKey(walletId = WalletId(walletId), pixKey = pixKey).getOrElse {
                LOG.error(markers.andAppend("error", it), logName)
                return HttpResponse.serverError(it)
            }
            HttpResponse.created(pixKey.value)
        } catch (ex: Exception) {
            LOG.error(markers, logName, ex)
            HttpResponse.serverError(ex.message.orEmpty())
        }
    }

    @Post("/pix/qrcode/validate")
    fun validateQRCode(@Body body: PixQRCodeValidateTO): HttpResponse<*> {
        val response = qrCodeService.parseQRCodeCacheable(pixCopyAndPaste = PixCopyAndPaste(body.qrCodeValue), document = body.document).getOrElse {
            return HttpResponse.badRequest(it)
        }
        return HttpResponse.ok(response)
    }

    @Delete("/wallet/{walletId}/pix/key/{pixKey}")
    fun deletePixKey(@PathVariable walletId: String, @PathVariable pixKey: String): HttpResponse<*> {
        val markers = append("walletId", walletId)
        val logName = "BackofficeDeletePixKey"

        return try {
            val pixKeyType = PixKeyType.fromString(pixKey)

            if (pixKeyType == null) {
                markers.andAppend("reason", "Invalid pix key type")
                throw IllegalArgumentException("Invalid pix key type")
            }

            val parsedPixKey = PixKey(value = pixKey, type = pixKeyType)

            if (!parsedPixKey.isValid()) {
                markers.andAppend("reason", "Invalid pix key")
                throw IllegalArgumentException("Invalid pix key")
            }

            pixKeyService.deletePixKey(walletId = WalletId(walletId), pixKey = parsedPixKey)
            HttpResponse.ok<Unit>()
        } catch (ex: Exception) {
            LOG.error(markers, logName, ex)
            HttpResponse.serverError(ex.message.orEmpty())
        }
    }

    @Get("/arbiValidateBill/{digitable}")
    fun arbiValidateBill(@PathVariable digitable: String): HttpResponse<*> {
        val response = arbiAdapter.validate(BarCode.ofDigitable(digitable))
        return HttpResponse.ok(response)
    }

    @Get("/arbiEndtoEnd/{code}")
    fun arbiEndToEnd(@PathVariable code: String): HttpResponse<*> {
        val response = arbiPixPaymentAdapter.checkEndToEndStatus(code)
        return HttpResponse.ok(response)
    }

    @Requires(env = ["staging"])
    @Post("/arbiPayBill")
    fun payBill(@Body body: ArbiPayBillTO): HttpResponse<*> {
        val response = arbiAdapter.pay(BarCode.ofDigitable(body.digitableLine), body.amount)
        return HttpResponse.ok(response)
    }

    @Post("/resetPassword/{username}")
    fun resetPassword(@PathVariable username: String): HttpResponse<*> {
        return try {
            userPoolAdapter.resetPassword(username)
            HttpResponse.noContent<Unit>()
        } catch (e: UserPoolUsernameNotFoundException) {
            StandardHttpResponses.notFound(ResponseTO("USER_NOT_FOUND", e.message.orEmpty()))
        } catch (e: Exception) {
            val markers = append("username", username)
            LOG.error(markers, "ResetPassword", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Post("/login/account/{accountId}/disable")
    @Secured(Role.Code.ADMIN)
    fun disableLoginByAccountId(@PathVariable accountId: String): HttpResponse<*> {
        val markers = append("accountId", accountId)
        return try {
            val account = accountRepository.findById(AccountId(accountId))
            markers.andAppend("account", account)

            return userPoolAdapter.disableUser(account.document).map {
                HttpResponse.noContent<Unit>()
            }.getOrElse {
                LOG.error(markers.andAppend("error", it), "DisableLogin")
                StandardHttpResponses.serverError(it.toString())
            }
        } catch (e: Exception) {
            LOG.error(markers, "DisableLogin", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Post("/login/account/{accountId}/enable")
    @Secured(Role.Code.ADMIN)
    fun enableLoginByAccountId(@PathVariable accountId: String): HttpResponse<*> {
        val markers = append("accountId", accountId)
        return try {
            val account = accountRepository.findById(AccountId(accountId))
            markers.andAppend("account", account)

            return userPoolAdapter.enableUser(account.document, accountId = AccountId(accountId)).map {
                HttpResponse.noContent<Unit>()
            }.getOrElse {
                LOG.error(markers.andAppend("error", it), "EnableLogin")
                StandardHttpResponses.serverError(it.toString())
            }
        } catch (e: Exception) {
            LOG.error(markers, "EnableLogin", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Get("/login/account/{accountId}")
    @Secured(Role.Code.ADMIN)
    fun isLoginEnabled(@PathVariable accountId: String): HttpResponse<*> {
        val markers = append("accountId", accountId)
        return try {
            val account = accountRepository.findById(AccountId(accountId))
            markers.andAppend("account", account)

            HttpResponse.ok(mapOf("enabled" to userPoolAdapter.isUserEnabled(account.document)))
        } catch (e: Exception) {
            LOG.error(markers, "IsLoginEnabled", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Get("/login/document/{document}/accountId")
    @Secured(Role.Code.ADMIN)
    fun getLoginAccountId(@PathVariable document: String): HttpResponse<*> {
        val markers = append("document", document)
        return try {
            HttpResponse.ok(mapOf("accountId" to userPoolAdapter.getAccountId(document)?.value))
        } catch (e: Exception) {
            LOG.error(markers, "DisableLogin", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Put("/login/document/{document}/password/{password}")
    @Secured(Role.Code.ADMIN)
    fun setUserPassword(@PathVariable document: String, @PathVariable password: String): HttpResponse<*> {
        val markers = append("document", document)
        return try {
            userPoolAdapter.setUserPassword(document, password)
            HttpResponse.noContent<Unit>()
        } catch (e: Exception) {
            LOG.error(markers, "DisableLogin", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Post("/login/account/{accountId}/mfa")
    @Secured(Role.Code.ADMIN)
    fun enableMFA(@PathVariable accountId: String): HttpResponse<*> {
        val markers = append("accountId", accountId)
        return try {
            val account = accountRepository.findById(AccountId(accountId))
            markers.andAppend("account", account)

            return userPoolAdapter.setMfaPreference(account.document, true).map {
                HttpResponse.noContent<Unit>()
            }.getOrElse {
                LOG.error(markers.andAppend("error", it), "EnableMFA")
                StandardHttpResponses.serverError(it.toString())
            }
        } catch (e: Exception) {
            LOG.error(markers, "EnableMFA", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Delete("/login/account/{accountId}/mfa")
    @Secured(Role.Code.ADMIN)
    fun disableMFA(@PathVariable accountId: String): HttpResponse<*> {
        val markers = append("accountId", accountId)
        return try {
            val account = accountRepository.findById(AccountId(accountId))
            markers.andAppend("account", account)

            return userPoolAdapter.setMfaPreference(account.document, false).map {
                HttpResponse.noContent<Unit>()
            }.getOrElse {
                LOG.error(markers.andAppend("error", it), "DisableMFA")
                StandardHttpResponses.serverError(it.toString())
            }
        } catch (e: Exception) {
            LOG.error(markers, "DisableMFA", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Post("/login/whatsapp/fix/{msisdn}")
    fun fixWhatsappLogin(
        @PathVariable msisdn: String,
        @QueryValue("role") role: Role? = null,
    ): HttpResponse<*> {
        val markers = append("msisdn", msisdn)

        val mobilePhone = MobilePhone(msisdn).fix()

        val logins =
            loginRepository.findUserLogin(emailAddress = createTemporaryEmail(mobilePhone))

        if (logins.isEmpty()) {
            LOG.warn(markers, "fixWhatsappLogin/findLogin")
            return StandardHttpResponses.notFound("login not found")
        }

        val login = try {
            logins.single()
        } catch (e: Exception) {
            LOG.error(markers, "fixWhatsappLogin", e)
            return StandardHttpResponses.serverError(e.message.orEmpty())
        }

        val whatsappLogin = loginRepository.findUserLogin(createWhatsappEmail(mobilePhone)).singleOrNull()

        markers.andAppend("accountId", login.accountId.value)
            .andAppend("loginRole", login.role.name)
            .andAppend("whatsappLoginRole", whatsappLogin?.role?.name ?: "(none)")

        if (whatsappLogin != null && whatsappLogin.role == login.role) {
            LOG.info(markers, "fixWhatsappLogin/nothingToFix")
            return HttpResponse.noContent<Unit>()
        }

        val account = try {
            accountRepository.findById(login.accountId)
        } catch (e: Exception) {
            LOG.warn(markers, "fixWhatsappLogin/findAccount")
            return StandardHttpResponses.notFound("login not found")
        }

        try {
            loginRepository.createLogin(
                providerUser = ProviderUser(
                    id = account.accountId.value,
                    providerName = ProviderName.WHATSAPP,
                    username = "",
                    emailAddress = createWhatsappEmail(mobilePhone),
                ),
                id = account.accountId,
                role = role ?: login.role,
            )
        } catch (e: Exception) {
            LOG.error(markers, "fixWhatsappLogin", e)
            return StandardHttpResponses.serverError(e.message.orEmpty())
        }

        return HttpResponse.ok<Unit>()
    }

    @Get("/account/{accountId}/authEvents")
    @Secured(Role.Code.ADMIN)
    fun listUserAuthEvents(@PathVariable accountId: String): HttpResponse<*> {
        val markers = append("accountId", accountId)
        return try {
            val account = accountRepository.findById(AccountId(accountId))

            return userPoolAdapter.listUserEvents(account.document).map {
                HttpResponse.ok(it)
            }.getOrElse {
                LOG.error(markers.andAppend("error", it), "ListUserAuthEvents")
                StandardHttpResponses.serverError(it.toString())
            }
        } catch (e: Exception) {
            LOG.error(markers, "ListUserAuthEvents", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Post("/mailBox/bill")
    fun createBillsForUser(@Body body: CreateBillsTO): HttpResponse<*> {
        return try {
            val billsAdded = billService.addBillsFromBackoffice(
                EmailAddress(body.userEmail),
                EmailAddress(body.from),
                body.subject,
                body.bills.map { EmailBill(it.digitable, it.dueDate) },
            )

            val response = billsAdded.map {
                AddBillFromBackOfficeTO(
                    digitableLine = it.first.digitable,
                    result = it.second?.toMessage() ?: ResponseTO("2001", "Success"),
                )
            }
            LOG.info(append("body", body).andAppend("response", response), "createBillsForUser")

            HttpResponse.created(response)
        } catch (e: Exception) {
            val markers = append("body", body)
            LOG.error(markers, "createBillsForUser", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Post("/crmUpsertAccount/{accountId}")
    fun crmUpsertAccount(@PathVariable accountId: String): HttpResponse<*> {
        return try {
            val account = accountService.findAccountById(AccountId(accountId))
            val contact = defaultCrmService.upsertContact(
                account,
            )
            HttpResponse.created(contact)
        } catch (e: Exception) {
            val markers = Markers.append("accountId", accountId)
            LOG.error(markers, "crmUpsertAccount", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Post("/crmUpsertPartialAccount/{accountId}")
    fun crmUpsertPartialAccount(@PathVariable accountId: String): HttpResponse<*> {
        return try {
            val partialAccount = accountService.findPartialAccountById(accountId = AccountId(accountId))
            val contact = defaultCrmService.upsertContact(
                partialAccount,
            )
            HttpResponse.created(contact)
        } catch (e: Exception) {
            val markers = Markers.append("accountId", accountId)
            LOG.error(markers, "crmUpsertPartialAccount", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Post("/account/{accountId}/referrer")
    fun createReferrer(@PathVariable accountId: String, @Body body: AccountReferrerTO): HttpResponse<*> {
        val markers = append("accountId", accountId).andAppend("referrer", body)
        try {
            return when (referrerService.createReferrerRegister(body.toReferrerRegister(AccountId(accountId)))) {
                AccountReferrerResult.Success,
                AccountReferrerResult.AlreadyExists,
                -> HttpResponse.noContent<Unit>()

                AccountReferrerResult.AccountNotFound -> HttpResponse.notFound()
                AccountReferrerResult.Invalid -> StandardHttpResponses.badRequest(
                    ResponseTO(code = "4001", message = "invalid payload"),
                )
            }.also {
                LOG.info(
                    markers.andAppend("httpStatus", it.status),
                    "BackofficeCreateReferrer",
                )
            }
        } catch (e: Exception) {
            LOG.error(
                markers.andAppend("httpStatus", HttpStatus.INTERNAL_SERVER_ERROR),
                "BackofficeCreateReferrer",
                e,
            )
            return StandardHttpResponses.serverError()
        }
    }

    @Post("/kyc/{accountId}")
    @Secured(Role.Code.ADMIN)
    fun postKyc(@PathVariable accountId: String): HttpResponse<*> {
        val markers = append("accountId", accountId)
        return try {
            val accountRegisterData = accountRegisterRepository.findByAccountId(AccountId(accountId), checkOpenAccount = false)

            kycService.generate(accountRegisterData).getOrElse {
                throw it
            }
            HttpResponse.accepted<Unit>()
        } catch (e: Exception) {
            LOG.error(markers, "BackofficeGenerateKyc", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Get("/features")
    fun listFeatures(): HttpResponse<*> {
        val features = featuresRepository.getAll()

        return HttpResponse.ok(features.toFeaturesTO())
    }

    @Put("/features")
    fun updateFeature(@Body body: UpdateFeatureTO): HttpResponse<*> {
        val features = featuresRepository.update(type = FeaturesType.valueOf(body.type), enabled = body.enabled)
        featuresRepository.invalidate()

        return HttpResponse.ok(features.toFeaturesTO())
    }

    @Delete("/features")
    fun deleteFeatures(): HttpResponse<*> {
        featuresRepository.invalidate()

        return HttpResponse.noContent<Unit>()
    }

    @Delete("/account/{accountId}")
    @Secured(Role.Code.ADMIN, Role.Code.BACKOFFICE_ADMIN)
    fun deleteAccount(
        @PathVariable accountId: String,
        @Body body: AccountClosureDetailsTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        val markers = append("accountId", accountId).andAppend("deletedBy", authentication.toAccountId())
            .andAppend("reason", body.reason)

        val closureDetails = try {
            body.toAccountClosureDetails()
        } catch (e: Exception) {
            LOG.warn(markers, "BackOfficeCloseAccount", e)
            return StandardHttpResponses.badRequest(ResponseTO(code = "4001", message = "Invalid request"))
        }

        try {
            val result = closeAccountService.closeAccount(
                accountId = AccountId(accountId),
                closureDetails = closureDetails,
            ).getOrElse {
                markers.andAppend("error", it)
                return it.handle(markers, "BackOfficeCloseAccount")
            }

            LOG.info(markers.andAppend("result", result), "BackOfficeCloseAccount")
            return HttpResponse.ok(result.toAccountDeletionResponseTO())
        } catch (e: Exception) {
            LOG.warn(markers, "BackOfficeCloseAccount", e)
            return StandardHttpResponses.serverError(message = e.message.orEmpty())
        }
    }

    @Delete("/account/closeByFile", consumes = [MediaType.MULTIPART_FORM_DATA], produces = [MediaType.APPLICATION_JSON])
    @Secured(ADMIN, BACKOFFICE_ADMIN)
    fun deleteByFile(
        file: CompletedFileUpload,
        authentication: Authentication,
    ): HttpResponse<*> {
        val success = mutableListOf<CloseAccountResult>()
        val failed = mutableListOf<CloseAccountError>()

        BufferedReader(InputStreamReader(file.inputStream)).use { reader ->
            reader.readLine() // Pular cabeçalho
            reader.forEachLine { line ->
                val (accountIdStr, userName) = line.split(",")
                val accountId = AccountId(accountIdStr)
                val markers = append("deletedBy", authentication.toAccountId()).andAppend("accountId", accountId.value)
                try {
                    val closureDetails = AccountClosureDetails.create(
                        reason = AccountClosureReason.USER_REQUEST,
                        description = "Encerramento de conta massivo",
                    )

                    val account = accountRepository.findById(accountId)
                    val result = if (!account.name.equals(userName, ignoreCase = true)) {
                        CloseAccountError.InvalidAccountNameCheck(account.name).left()
                    } else {
                        closeAccountService.closeAccount(
                            accountId = accountId,
                            closureDetails = closureDetails,
                        )
                    }

                    result.map {
                        success.add(it)
                        LOG.info(markers.andAppend("deleteStatus", "success").andAppend("deletionData", it), "BackofficeController#deleteByFile")
                    }.getOrElse { error ->
                        LOG.info(markers.andAppend("deleteStatus", "failed").andAppend("deletionData", error), "BackofficeController#deleteByFile")
                        failed.add(error)
                    }
                } catch (e: Exception) {
                    LOG.error(markers.andAppend("deleteStatus", "failed"), "BackofficeController#deleteByFile", e)
                }
            }
        }

        return HttpResponse.ok(
            mapOf(
                "success" to success,
                "failed" to failed,
            ),
        )
    }

    @Post("/push-notification")
    fun sendPushNotification(@Body body: PushNotificationTO): HttpResponse<*> {
        val logName = "BackofficeController#sendPushNotification"
        val markers = append("request", body).andAppend("accountId", body.accountId)

        return runCatching {
            val accountId = AccountId(body.accountId)
            val notification = body.toPushNotificationContent()

            pushNotificationService.sendNotification(
                accountId = accountId,
                content = notification,
            )
        }.fold(
            onSuccess = { result ->
                LOG.info(markers.andAppend("result", result), logName)
                HttpResponse.ok(result.getOrThrow())
            },
            onFailure = {
                LOG.error(markers, logName, it)

                when (it) {
                    is IllegalArgumentException -> HttpResponse.badRequest<Unit>()
                    else -> HttpResponse.serverError(it)
                }
            },
        )
    }

    private fun CloseAccountResult.toAccountDeletionResponseTO() = AccountDeletionResponseTO(
        notificationDisabled = stepSucceeded(CloseAccountStepTypeDisableNotification),
        removedFromUserPool = stepSucceeded(CloseAccountStepTypeRemoveFromUserPool),
        removedFromDDA = stepSucceeded(CloseAccountStepTypeRemoveFromDDA),
        wallets = steps.filterIsInstance<CloseFounderWalletCloseAccountStep>().map { it.toWalletDeletionResponseTO() },
        steps = steps.map {
            it.toCloseAccountStepTO()
        },
    )

    private fun CloseAccountStep.toCloseAccountStepTO() = when (this) {
        is CloseFounderWalletCloseAccountStep -> CloseFounderWalletCloseAccountStepTO(
            walletId = walletId.value,
            steps = steps.map {
                it.toCloseWalletStepTO()
            },
            status = status,
        )

        else -> SimpleCloseAccountStepTO(
            type = type,
            status = status,
        )
    }

    private fun CloseWalletStep.toCloseWalletStepTO() = CloseWalletStepTO(
        type = type,
        status = status,
    )

    private fun CloseAccountResult.stepSucceeded(stepType: CloseAccountStepType) = steps.any { it.type == stepType && it.status == CloseAccountStepStatus.Success }

    private fun CloseFounderWalletCloseAccountStep.toWalletDeletionResponseTO() = WalletDeletionResponseTO(
        walletId = walletId.value,
        bankAccountNumber = null,
        bankAccountMode = if (steps.any { it.type in listOf(CloseWalletStepTypeCloseExternalAccountNow, CloseWalletStepTypeCloseExternalAccountLater) }) {
            BankAccountMode.PHYSICAL
        } else {
            BankAccountMode.VIRTUAL
        },
        accountPaymentMethodStatus = if (stepSucceeded(CloseWalletStepTypeCloseExternalAccountNow)) {
            AccountPaymentMethodStatus.CLOSED
        } else {
            AccountPaymentMethodStatus.PENDING_CLOSE
        },
        externalAccountClosed = stepSucceeded(CloseWalletStepTypeCloseExternalAccountNow),
        externalAccountCloseError = null,
        pixKeyDeleted = stepSucceeded(CloseWalletStepTypeDeleteWalletPixKey),
    )

    private fun CloseFounderWalletCloseAccountStep.stepSucceeded(stepType: CloseWalletStepType) = steps.any { it.type == stepType && it.status == CloseWalletStepStatus.Success }

    @Delete("/partialAccount/{accountId}")
    fun deletePartialAccount(
        @PathVariable accountId: String,
        @Body body: AccountClosureDetailsTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        val markers = append("accountId", accountId).andAppend("deletedBy", authentication.toAccountId())
        val closureDetails = try {
            body.toAccountClosureDetails()
        } catch (e: Exception) {
            LOG.warn(markers, "BackOfficeClosePartialAccount", e)
            return StandardHttpResponses.badRequest(ResponseTO(code = "4001", message = "Invalid request"))
        }

        val result = closeAccountService.closePartialAccount(
            AccountId(accountId),
            closureDetails,
        ).getOrElse {
            return it.handle(markers, "BackOfficeClosePartialAccount")
        }

        LOG.info(markers.andAppend("result", result), "BackOfficeClosePartialAccount")
        return HttpResponse.ok(
            AccountDeletionResponseTO(
                notificationDisabled = false,
                removedFromUserPool = result.removedFromUserPool,
                removedFromDDA = false,
                wallets = emptyList(),
                steps = emptyList(),
            ),
        )
    }

    @Delete("/signUpTest/{accountId}")
    fun deletePartialTestAccount(@PathVariable accountId: String): HttpResponse<*> {
        val markers = append("accountId", accountId)
        val result = closeAccountService.deletePartialTestAccount(AccountId(accountId)).getOrElse {
            return it.handle(markers, "BackOfficeDeletePartialAccount")
        }

        LOG.info(markers.andAppend("result", result), "BackOfficeDeletePartialAccount")
        return HttpResponse.ok(
            AccountDeletionResponseTO(
                notificationDisabled = false,
                removedFromUserPool = result.removedFromUserPool,
                removedFromDDA = false,
                wallets = emptyList(),
                steps = emptyList(),
            ),
        )
    }

    @Put("/wallet/{walletId}/refund")
    @Secured(Role.Code.ADMIN)
    fun refundBoletoBill(
        @PathVariable walletId: String,
        @Body body: RefundBillTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        val markers = append("walletId", walletId).andAppend("body", body)
            .andAppend("admin", authentication.toAccountId())
        val response = refundBoletoService.refund(
            walletId = WalletId(value = walletId),
            barCode = BarCode.ofDigitable(body.digitableLine),
        )
        return when (response) {
            is RefundBoletoResponse.Success -> {
                LOG.info(markers.andAppend("response", response), "RefundBoletoBill")
                HttpResponse.noContent<Unit>()
            }

            is RefundBoletoResponse.GenericError -> {
                LOG.error(markers.andAppend("response", response), "RefundBoletoBill", response.e)
                HttpResponse.serverError(response.toString())
            }

            else -> {
                LOG.error(markers.andAppend("response", response), "RefundBoletoBill")
                HttpResponse.serverError(response.toString())
            }
        }
    }

    @Put("/account/{accountId}/mobilePhone")
    @Secured(Role.Code.ADMIN, Role.Code.BACKOFFICE_ADMIN)
    fun updatePhoneNumber(
        @PathVariable accountId: String,
        @Body body: UpdatePhoneTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        val markers = append("accountId", accountId)
            .andAppend("currentMobilePhone", body.currentPhoneNumber)
            .andAppend("mobilePhone", body.newPhoneNumber)
            .andAppend("admin", authentication.toAccountId())

        return updateAccountService.updatePhoneNumber(
            accountId = AccountId(value = accountId),
            newPhoneNumber = MobilePhone(msisdn = body.newPhoneNumber),
            currentPhoneNumber = MobilePhone(msisdn = body.currentPhoneNumber),
        ).map {
            LOG.info(markers, "updatePhoneNumber")
            HttpResponse.noContent<Unit>()
        }.getOrElse {
            LOG.error(markers.andAppend("response", it), "updatePhoneNumber")
            HttpResponse.serverError(it.toString())
        }
    }

    @Put("/account/{accountId}/emailAddress")
    @Secured(Role.Code.ADMIN, Role.Code.BACKOFFICE_ADMIN)
    fun updateEmailAddress(
        @PathVariable accountId: String,
        @Body body: UpdateEmailTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        val markers = append("accountId", accountId)
            .andAppend("emailAddress", body.newEmailAddress)
            .andAppend("currentEmailAddress", body.currentEmailAddress)
            .andAppend("admin", authentication.toAccountId())

        return updateAccountService.updateEmailAddress(
            accountId = AccountId(value = accountId),
            newEmailAddress = EmailAddress(body.newEmailAddress),
            currentEmailAddress = EmailAddress(body.currentEmailAddress),
        ).map {
            LOG.info(markers, "updateEmailAddress")
            HttpResponse.noContent<Unit>()
        }.getOrElse {
            markers.andAppend("response", it)
            if (it is UpdateAccountError.Unknown) {
                LOG.error(markers, "updateEmailAddress")
                HttpResponse.serverError(it.toString())
            } else {
                LOG.warn(markers, "updateEmailAddress")
                HttpResponse.badRequest(it.toString())
            }
        }
    }

    @Put("/account/{accountId}/name")
    @Secured(Role.Code.ADMIN)
    fun updateName(
        @PathVariable accountId: String,
        @Body body: UpdateNameTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        val markers = append("accountId", accountId)
            .andAppend("name", body.newName)
            .andAppend("currentName", body.currentName)
            .andAppend("admin", authentication.toAccountId())

        return updateAccountService.updateName(
            accountId = AccountId(value = accountId),
            newName = body.newName,
            currentName = body.currentName,
        ).map {
            LOG.info(markers, "updateName")
            HttpResponse.noContent<Unit>()
        }.getOrElse {
            markers.andAppend("response", it)
            if (it is UpdateAccountError.Unknown) {
                LOG.error(markers, "updateName")
                HttpResponse.serverError(it.toString())
            } else {
                LOG.warn(markers, "updateName")
                HttpResponse.badRequest(it.toString())
            }
        }
    }

    @Post("/zipcode")
    fun registerZipCode(@Body body: RegisterZipCodeTO): HttpResponse<*> {
        val markers = append("zipCode", body.zipCode)
        return try {
            zipCodeService.register(ZipCode(body.zipCode))
            LOG.info(markers, "RegisterZipCode")
            HttpResponse.created("")
        } catch (exception: IllegalArgumentException) {
            doHandleError(
                exception = exception,
                logName = "RegisterZipCode",
                markers = markers,
                errorStatus = HttpStatus.BAD_REQUEST,
                code = "4005",
                message = "O CEP deve ter 8 dígitos.",
            )
        } catch (exception: Exception) {
            doHandleError(
                exception = exception,
                logName = "RegisterZipCode",
                markers = markers,
                errorStatus = HttpStatus.INTERNAL_SERVER_ERROR,
                code = "-1",
                message = "erro genérico",
            )
        }
    }

    @Get("/account/{accountId}/activity/{activityType}")
    fun getAccountActivity(
        @PathVariable accountId: String,
        @PathVariable activityType: SystemActivityType,
    ): HttpResponse<*> {
        return try {
            val result = systemActivityRepository.find(
                key = AccountId(accountId).toSystemActivityKey(),
                activityType = activityType,
            )

            HttpResponse.ok(result)
        } catch (error: Exception) {
            HttpResponse.badRequest(error.message)
        }
    }

    @Post("/account/{accountId}/activity/{activityType}/value/{activityValue}")
    fun setAccountActivity(
        @PathVariable accountId: String,
        @PathVariable activityType: SystemActivityType,
        @PathVariable activityValue: String,
    ): HttpResponse<*> {
        return try {
            systemActivityRepository.save(
                key = AccountId(accountId).toSystemActivityKey(),
                activityType = activityType,
                activityValue = activityValue,
            )

            val result = systemActivityRepository.find(
                key = AccountId(accountId).toSystemActivityKey(),
                activityType = activityType,
            )

            HttpResponse.ok(result)
        } catch (error: Exception) {
            HttpResponse.badRequest(error.message)
        }
    }

    @Post("/grant-gratuity")
    @Secured(Role.Code.ADMIN)
    fun grantGratuity(@Body body: String): HttpResponse<*> {
        val logName = "BackofficeController#grantGratuity"
        val parsedObject = try {
            parseObjectFrom<GratuityTO>(body)
        } catch (e: Exception) {
            LOG.error(append("body", body), logName, e)
            return HttpResponse.badRequest<Unit>()
        }

        val markers = append("accountId", parsedObject.accountId)
        val accountId = AccountId(parsedObject.accountId)

        val shouldBeGratuity = if (!parsedObject.skipParticipantVerification!!) {
            try {
                val wallets = walletService.findWallets(accountId)

                val isParticipant = wallets.any { wallet -> wallet.getMember(accountId).type !== MemberType.FOUNDER }

                isParticipant && !(systemActivityService.getBillPaidOnOwnWallet(accountId) || systemActivityService.getDDABillPaid(accountId))
            } catch (e: Exception) {
                LOG.error(markers, "shouldBeGratuity", e)
                return HttpResponse.badRequest<Unit>()
            }
        } else {
            true
        }

        if (!shouldBeGratuity) {
            LOG.error(markers.andAppend("shouldBeGratuity", false), logName)
            return HttpResponse.badRequest<Unit>()
        }

        return inAppSubscriptionService.grantGratuity(
            accountId = accountId,
            duration = Duration.parse(parsedObject.duration),
            deduplicationId = parsedObject.deduplicationId,
        ).fold(
            onFailure = {
                HttpResponse.serverError<Unit>().also { LOG.error(markers.andAppend("result", it), "$logName#Failed") }
            },
            onSuccess = { result ->
                markers.andAppend("result", result)

                when (result) {
                    is GrantGratuityResult.ValidAccountNotFound,
                    is GrantGratuityResult.ActiveStoreSubscription,
                    is GrantGratuityResult.NotInAppSubscription,
                    is GrantGratuityResult.DuplicatedGrantFound,
                    -> HttpResponse.badRequest<Unit>().also { LOG.error(markers, logName) }

                    is GrantGratuityResult.Granted -> {
                        HttpResponse.noContent<Unit>().also { LOG.info(markers, logName) }
                    }
                }
            },
        )
    }

    @Get("/wallet/{walletId}/activity/{activityType}")
    fun getWalletActivity(
        @PathVariable walletId: String,
        @PathVariable activityType: SystemActivityType,
    ): HttpResponse<*> {
        return try {
            val result = systemActivityRepository.find(
                key = WalletId(walletId).toSystemActivityKey(),
                activityType = activityType,
            )

            HttpResponse.ok(result)
        } catch (error: Exception) {
            HttpResponse.badRequest(error.message)
        }
    }

    @Post("/wallet/{walletId}/activity/{activityType}/value/{activityValue}")
    fun setWalletActivity(
        @PathVariable walletId: String,
        @PathVariable activityType: SystemActivityType,
        @PathVariable activityValue: String,
    ): HttpResponse<*> {
        return try {
            systemActivityRepository.save(
                key = WalletId(walletId).toSystemActivityKey(),
                activityType = activityType,
                activityValue = activityValue,
            )

            val result = systemActivityRepository.find(
                key = WalletId(walletId).toSystemActivityKey(),
                activityType = activityType,
            )

            HttpResponse.ok(result)
        } catch (error: Exception) {
            HttpResponse.badRequest(error.message)
        }
    }

    @Get("/external-bank-account/{document}")
    fun getExternalBankAccounts(
        @PathVariable document: String,
    ): HttpResponse<*> {
        return try {
            val result = externalBankAccountService.findAll(document = Document(value = document))

            HttpResponse.ok(result)
        } catch (ex: Exception) {
            HttpResponse.badRequest(ex.message)
        }
    }

    @Post("/email/process/{key}")
    fun processEmail(
        @PathVariable key: String,
    ): HttpResponse<*> {
        val action =
            Action("S3", "", "via1-incoming-emails", null, key)
        val receipt = Receipt(null, null, null, null, null, null, null, null, null, action)

        return try {
            messageProcessor.process(SQSEvent(null, receipt, null))
            HttpResponse.noContent<Unit>()
        } catch (error: Exception) {
            HttpResponse.serverError(error.message)
        }
    }

    @Post("/account-group/add")
    fun accountGroupsAdd(@Body request: AccountGroupAddOrRemove): HttpResponse<*> {
        val markers = append("request", request)

        return try {
            val account = accountService.addGroups(AccountId(request.accountId), request.groups)
            markers.andAppend("account", account)

            LOG.info(markers, "accountGroupsAdd")
            return HttpResponse.ok(account)
        } catch (e: Exception) {
            LOG.error(markers, "accountGroupsAdd", e)
            HttpResponse.serverError(e.message.orEmpty())
        }
    }

    @Post("/account-group/remove")
    fun accountGroupsRemove(@Body request: AccountGroupAddOrRemove): HttpResponse<*> {
        val markers = append("request", request)

        return try {
            val account = accountService.removeGroups(AccountId(request.accountId), request.groups)
            markers.andAppend("account", account)

            LOG.info(markers, "accountGroupsRemove")
            return HttpResponse.ok(account)
        } catch (e: Exception) {
            LOG.error(markers, "accountGroupsRemove", e)
            HttpResponse.serverError(e.message.orEmpty())
        }
    }

    // FIXME - suportar multiplas carteiras
    @Post("/wallet/{walletId}/registerPixKey")
    fun registerPixKey(@PathVariable walletId: String): HttpResponse<*> {
        val markers = append("walletId", walletId)

        return try {
            val wallet = walletRepository.findWallet(WalletId(walletId))

            val bankAccount =
                accountRepository.findAccountPaymentMethodByIdAndAccountId(
                    wallet.paymentMethodId,
                    wallet.founder.accountId,
                ).method as InternalBankAccount
            markers.andAppend("accountNo", bankAccount.accountNo)
            val accountNumber = AccountNumber(bankAccount.buildFullAccountNumber())

            val registeredDevice = deviceFingerprintService.getOrNull(wallet.founder.accountId)

            val deviceId = registeredDevice?.deviceIds?.get(accountNumber)

            val cpf = wallet.founder.document
            markers.andAppend("document", cpf)

            pixKeyManagement.registerKey(
                accountNo = accountNumber,
                key = walletService.walletPixKey(wallet),
                document = cpf,
                name = wallet.founder.name,
                deviceId = deviceId,
            )

            LOG.info(markers, "registerPixKey")
            HttpResponse.created(Unit)
        } catch (e: Exception) {
            LOG.error(markers, "registerPixKey", e)
            HttpResponse.serverError(e.message)
        }
    }

    @Delete("/wallet/{walletId}/pixKey")
    fun removePixKey(@PathVariable walletId: String): HttpResponse<*> {
        val markers = append("walletId", walletId)

        return try {
            val targetWalletId = WalletId(walletId)
            val wallet = walletRepository.findWallet(targetWalletId)

            val cpf = wallet.founder.document
            markers.andAppend("document", cpf)

            deviceFingerprintService.withRealOrTemporaryDeviceId(accountId = wallet.founder.accountId, walletId = wallet.id) { deviceId ->
                pixKeyManagement.deleteKey(key = walletService.walletPixKey(wallet).value, document = Document(cpf), deviceId = deviceId)
            }

            LOG.info(markers, "removePixKey")
            HttpResponse.created(Unit)
        } catch (e: Exception) {
            LOG.error(markers, "removePixKey", e)
            HttpResponse.serverError(e.message)
        }
    }

    @Post("/account/{accountId}/dda")
    fun registerDDA(@PathVariable accountId: String): HttpResponse<*> {
        val markers = append("accountId", accountId)

        return try {
            val account = accountRepository.findById(AccountId(accountId))

            val cpf = account.document
            markers.andAppend("document", cpf)

            val ddaStatus = ddaService.register(account.accountId, cpf)
            markers.andAppend("ddaStatus", ddaStatus.name)

            LOG.info(markers, "registerDDA")
            HttpResponse.ok(ddaStatus.name)
        } catch (e: Exception) {
            LOG.error(markers, "registerDDA", e)
            HttpResponse.serverError(e.message)
        }
    }

    @Delete("/account/{accountId}/dda")
    fun removerDDA(@PathVariable accountId: String): HttpResponse<*> {
        val markers = append("accountId", accountId)

        return try {
            val account = accountRepository.findById(AccountId(accountId))

            val cpf = account.document
            markers.andAppend("document", cpf)

            arbiAdapter.remove(Document(cpf))

            LOG.info(markers, "removerDDA")
            HttpResponse.ok(Unit)
        } catch (e: Exception) {
            LOG.error(markers, "removerDDA", e)
            HttpResponse.serverError(e.message)
        }
    }

    @Post("metrics/pre-populate")
    fun prepopulateMetrics(): HttpResponse<*> {
        OnePixPayMetric.RequestedVerifyBillsComingDue.push()
        OnePixPayMetric.AccountNotSupported.push()
        OnePixPayMetric.DecidedToNotifyBillsComingDue.push()
        OnePixPayMetric.DecidedNotToNotifyBillsComingDue.push()
        OnePixPayMetric.UserReceivedADeposit.ProbableOnePixPay.push()
        OnePixPayMetric.UserReceivedADeposit.ProbableCopyAndPaste.push()
        OnePixPayMetric.UserReceivedADeposit.Other.push()
        OnePixPayMetric.OnePixPayFailedToUnscheduleBills.push()
        OnePixPayMetric.OnePixPayFailed.push()
        OnePixPayMetric.OnePixPayFullyProcessed.push()
        OnePixPayMetric.OnePixPayPartiallyProcessed.push()

        return HttpResponse.accepted<Unit>()
    }

    @OptIn(DelicateCoroutinesApi::class)
    @Get("financialReport/creditCardCashIn/{date}/{recipients}")
    fun getCreditCardCashIn(@PathVariable date: String, @PathVariable recipients: String): HttpResponse<*> {
        val markers = append("date", date).andAppend("recipients", recipients)

        return try {
            val localDate = parseDate(date)
            val job = GlobalScope.launch {
                markers.andAppend("jobStarted", getZonedDateTime())
                val cashInTotal = creditCardCashInUsageService.reportCashInByDate(localDate)

                val message = buildString {
                    appendLine("total de cash-in do dia ${date.format(dateFormatBR)}:")
                    appendLine("  bruto: ${formatBrazilCurrency(cashInTotal.totalAmount)}")
                    appendLine("  líquido: ${formatBrazilCurrency(cashInTotal.netAmount)}")
                    appendLine("  taxa: ${formatBrazilCurrency(cashInTotal.feeAmount)}")
                }
                emailSender.sendRawEmail(from, "Relatório dos CashIn's por cartão de crédito referente ao dia ${date.format(dateFormatBR)}", message, recipients)
            }

            job.start()

            job.invokeOnCompletion {
                markers.andAppend("jobFinished", getZonedDateTime())
                LOG.info(markers, "getCreditCardCashInFinancialReport")
            }

            HttpResponse.noContent<Unit>()
        } catch (ex: Exception) {
            LOG.error(markers, "getCreditCardCashInFinancialReport", ex)
            HttpResponse.serverError(ex.message)
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    @Secured(Role.Code.ADMIN)
    @Get("financialReport/bankAccountReconciliation/{date}/{recipient}")
    fun bankAccountReconciliation(@PathVariable date: String, @PathVariable recipient: String): HttpResponse<*> {
        val markers = append("date", date).andAppend("recipients", recipient)

        return try {
            val at = parseDate(date).atStartOfDay(brazilTimeZone)

            val job = GlobalScope.launch {
                markers.andAppend("jobStarted", getZonedDateTime())
                sendFinancialReportsService.executeBankAccountReconciliationReport(at, recipient)
            }

            job.start()

            job.invokeOnCompletion {
                markers.andAppend("jobFinished", getZonedDateTime())
                LOG.info(markers, "bankAccountReconciliation")
            }

            HttpResponse.noContent<Unit>()
        } catch (e: Exception) {
            LOG.error(markers, "bankAccountReconciliation", e)
            HttpResponse.serverError(e.message)
        }
    }

    // Endpoint usado pelo chatbot para verificar se um token é de admin no backoffice
    @Post("validateAuth")
    @Secured(ADMIN)
    fun validateAdmin(
        authentication: Authentication,
    ): HttpResponse<*> {
        LOG.info(append("accountId", authentication.toAccountId().value), "validateAdminToken")
        return HttpResponse.ok<Unit>()
    }

    @Get("/btg/resource")
    fun btgResources(): HttpResponse<*> {
        return try {
            val response = btgITPAdapter.listResources()
            HttpResponse.ok(response)
        } catch (e: Exception) {
            LOG.error("btgResources", e)
            HttpResponse.serverError(e.message)
        }
    }

    @Get("/btg/webhook")
    fun btgWebhook(): HttpResponse<*> {
        return try {
            val response = btgITPAdapter.listWebhooks()
            HttpResponse.ok(response)
        } catch (e: Exception) {
            LOG.error("btgWebhook", e)
            HttpResponse.serverError(e.message)
        }
    }

    @Post("/btg/webhook")
    fun createBtgWebhook(@Body body: BtgWebhookTO): HttpResponse<*> {
        return try {
            val response = btgITPAdapter.createWebhook(body.url, body.resourceId, body.events)
            HttpResponse.ok(response)
        } catch (e: Exception) {
            LOG.error("createBtgWebhook", e)
            HttpResponse.serverError(e.message)
        }
    }

    @Put("/btg/webhook")
    fun updateBtgWebhook(@Body body: BtgWebhookTO): HttpResponse<*> {
        return try {
            val response = btgITPAdapter.updateWebhook(body.url, body.resourceId, body.events)
            HttpResponse.ok(response)
        } catch (e: Exception) {
            LOG.error("updateBtgWebhook", e)
            HttpResponse.serverError(e.message)
        }
    }

    @Post("/bill/fail-payment")
    @Secured(Role.Code.ADMIN)
    fun failBillPayment(
        @Body body: FailPaymentTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        val markers = append("body", body)
            .andAppend("admin", authentication.toAccountId())

        val logName = "BackofficeController#failBillPayment"

        return try {
            val walletId = WalletId(body.walletId)
            val billId = BillId(body.billId)
            val transactionId = TransactionId(body.transactionId)

            val bill = billEventRepository.getBill(walletId, billId).getOrElse {
                LOG.error(markers.andAppend("reason", "Bill not found"), logName)
                return HttpResponse.notFound<Unit>()
            }

            val transaction = transactionRepository.findById(transactionId)

            if (transaction.bill().billId != billId) {
                LOG.warn(markers.andAppend("reason", "transaction does not belong to bill"), logName)
                return HttpResponse.badRequest<Unit>()
            }

            val checkout = checkoutLocator.getCheckout(transaction)
            val failedTransaction = checkout.rollbackTransaction(transaction)
            transactionRepository.save(failedTransaction)

            val paymentFailedEvent = PaymentFailed(
                billId = billId,
                walletId = bill.walletId,
                retryable = false,
                errorSource = ErrorSource.valueOf(body.errorSource),
                errorDescription = body.errorDescription ?: "Manual payment failure from backoffice",
                transactionId = transaction.id,
                actionSource = transaction.actionSource,
            )

            billEventPublisher.publish(bill, paymentFailedEvent)

            LOG.info(markers.andAppend("status", "transaction_failed"), logName)

            HttpResponse.ok(
                mapOf(
                    "message" to "Payment failed successfully",
                    "transactionId" to transaction.id.value,
                ),
            )
        } catch (e: Exception) {
            LOG.error(markers, logName, e)
            HttpResponse.serverError(e.message.orEmpty())
        }
    }

    data class BtgWebhookTO(
        val url: String,
        val resourceId: String,
        val events: List<String>,
    )

    data class RefundBillTO(
        val digitableLine: String,
    )

    private fun ClosePartialAccountError.handle(markers: LogstashMarker, message: String) = when (this) {
        ClosePartialAccountError.AccountIsNotUnderTest -> {
            LOG.warn(markers.andAppend("error", this), message)
            StandardHttpResponses.badRequest(
                ResponseTO(
                    code = "4001",
                    message = "The accountId is not a valid test accountId",
                ),
            )
        }

        is ClosePartialAccountError.MarkAsFraud -> {
            LOG.error(markers, message, this)
            StandardHttpResponses.serverError(
                ResponseTO(
                    code = "5001",
                    message = "Unknown error",
                ),
            )
        }

        is ClosePartialAccountError.Unknown -> {
            when (this.e) {
                is AccountNotFoundException -> {
                    StandardHttpResponses.notFound(
                        ResponseTO(
                            code = "4041",
                            message = "AccountNotFound",
                        ),
                    )
                }

                else -> {
                    LOG.error(markers, message, this.e)
                    StandardHttpResponses.serverError(
                        ResponseTO(
                            code = "5001",
                            message = "Unknown error",
                        ),
                    )
                }
            }
        }
    }

    private fun CloseAccountError.handle(markers: LogstashMarker, message: String) = when (this) {
        is CloseAccountError.Unknown -> {
            when (this.exception) {
                is AccountNotFoundException -> {
                    LOG.warn(markers, message)
                    StandardHttpResponses.notFound(
                        ResponseTO(
                            code = "4041",
                            message = "AccountNotFound",
                        ),
                    )
                }

                else -> {
                    LOG.error(markers, message, this.exception)
                    StandardHttpResponses.serverError(
                        ResponseTO(
                            code = "5001",
                            message = this.exception.message.orEmpty(),
                        ),
                    )
                }
            }
        }

        is CloseAccountError.InvalidAccountStatus -> {
            LOG.warn(markers, message)
            StandardHttpResponses.badRequest(
                ResponseTO(
                    code = "4006",
                    message = this.toString(),
                ),
            )
        }

        is CloseAccountError.CloseFounderWalletError -> when (this.error) {
            is CloseWalletError.TransactionStillProcessing -> {
                LOG.warn(markers, message)
                StandardHttpResponses.conflict(
                    ResponseTO(
                        code = "4004",
                        message = getObjectMapper().writeValueAsString(
                            AccountDeletionResponseTO(
                                notificationDisabled = false,
                                removedFromUserPool = false,
                                removedFromDDA = false,
                                wallets = listOf(
                                    WalletDeletionResponseTO(
                                        walletId = this.error.walletId.value,
                                        bankAccountNumber = null,
                                        bankAccountMode = BankAccountMode.PHYSICAL,
                                        accountPaymentMethodStatus = AccountPaymentMethodStatus.ACTIVE,
                                        externalAccountClosed = false,
                                        externalAccountCloseError = "transação em processamento",
                                        pixKeyDeleted = false,
                                    ),
                                ),
                                steps = emptyList(),
                            ),
                        ),
                    ),
                )
            }

            is CloseWalletError.BalanceDifferentThanZero -> {
                LOG.warn(markers, message)
                StandardHttpResponses.conflict(
                    ResponseTO(
                        code = "4001",
                        message = getObjectMapper().writeValueAsString(
                            AccountDeletionResponseTO(
                                notificationDisabled = false,
                                removedFromUserPool = false,
                                removedFromDDA = false,
                                wallets = listOf(
                                    WalletDeletionResponseTO(
                                        walletId = this.error.walletId.value,
                                        bankAccountNumber = null,
                                        bankAccountMode = BankAccountMode.PHYSICAL,
                                        accountPaymentMethodStatus = AccountPaymentMethodStatus.ACTIVE,
                                        externalAccountClosed = false,
                                        externalAccountCloseError = "conta possui saldo: ${this.error.balance}",
                                        pixKeyDeleted = false,
                                    ),
                                ),
                                steps = emptyList(),
                            ),
                        ),
                    ),
                )
            }

            is CloseWalletError.Unknown -> {
                LOG.error(markers, message, this.error.exception)
                StandardHttpResponses.serverError(
                    ResponseTO(
                        code = "5001",
                        message = this.error.exception.message.orEmpty(),
                    ),
                )
            }
        }

        is CloseAccountError.InvalidAccountNameCheck -> {
            LOG.error(markers.andAppend("invalidAccountName", "InvalidAccountNameCheck"), message)
            StandardHttpResponses.serverError(
                ResponseTO(
                    code = "5001",
                    message = "InvalidAccountNameCheck",
                ),
            )
        }

        is CloseAccountError.CloseAccountErrorWithMessageAndAmount -> {
            LOG.warn(markers, message)
            StandardHttpResponses.serverError(
                ResponseTO(
                    code = "5001",
                    message = "${this.messageKey}: ${this.messageAmount}",
                ),
            )
        }
    }

    private fun MailboxAddBillError.toMessage(): ResponseTO {
        return when (this) {
            is MailboxAddBillError.MailboxAccountNotFoundError -> ResponseTO("4004", "AccountNotFound")
            is MailboxAddBillError.MailboxSecurityValidationError -> ResponseTO("4000", "SecurityValidation : $reason")
            is MailboxAddBillError.MailboxAddInvalidBill -> when (result) {
                is CreateBillResult.FAILURE.AlreadyPaid.WithData, is CreateBillResult.FAILURE.AlreadyPaid.WithoutData -> ResponseTO(
                    "4002",
                    "AlreadyPaid",
                )

                is CreateBillResult.FAILURE.BillAlreadyExists -> ResponseTO("4003", "BillAlreadyExists")
                is CreateBillResult.FAILURE.BillNotPayable -> ResponseTO("4005", "BillNotPayable")
                is CreateBillResult.FAILURE.BillUnableToValidate, is CreateBillResult.FAILURE.ServerError -> ResponseTO(
                    "5001",
                    "BillUnableToValidate",
                )
            }

            is MailboxAddBillError.MailBoxAddInvalidRequest -> ResponseTO("4006", "InvalidRequest: $message")
        }
    }

    private fun doHandleError(
        exception: Exception,
        logName: String,
        markers: LogstashMarker,
        errorStatus: HttpStatus,
        code: String,
        message: String,
    ): HttpResponse<ResponseTO> {
        LOG.error(
            markers.andAppend("httpStatus", errorStatus),
            logName,
            exception,
        )
        return StandardHttpResponses.customStatusResponse(
            errorStatus,
            ResponseTO(code, message),
        )
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BackofficeController::class.java)
    }
}

data class PixQRCodeValidateTO(
    val qrCodeValue: String,
    val document: String,
)

data class CreateBillsTO(
    val userEmail: String,
    val from: String,
    val subject: String,
    val bills: List<FromEmailBillTO>,
)

data class FromEmailBillTO(
    val digitable: String,
    val dueDate: String?,
)

data class AddBillFromBackOfficeTO(
    val digitableLine: String,
    val result: ResponseTO,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class AccountDeletionResponseTO(
    val notificationDisabled: Boolean,
    val removedFromUserPool: Boolean,
    val removedFromDDA: Boolean,
    val wallets: List<WalletDeletionResponseTO>,
    val steps: List<CloseAccountStepTO>,
)

@JsonTypeInfo(use = JsonTypeInfo.Id.MINIMAL_CLASS)
sealed interface CloseAccountStepTO {
    val type: CloseAccountStepType
    val status: CloseAccountStepStatus
}

data class SimpleCloseAccountStepTO(
    override val type: CloseAccountStepType,
    override val status: CloseAccountStepStatus,
) : CloseAccountStepTO

data class CloseFounderWalletCloseAccountStepTO(
    val walletId: String,
    val steps: List<CloseWalletStepTO>,
    override val status: CloseAccountStepStatus,
) : CloseAccountStepTO {
    override val type = CloseAccountStepTypeCloseFounderWallet
}

data class CloseWalletStepTO(
    val type: CloseWalletStepType,
    val status: CloseWalletStepStatus,
)

data class WalletDeletionResponseTO(
    val walletId: String,
    val bankAccountNumber: String?,
    val bankAccountMode: BankAccountMode?,
    val accountPaymentMethodStatus: AccountPaymentMethodStatus?,
    val externalAccountClosed: Boolean?,
    val externalAccountCloseError: String?,
    val pixKeyDeleted: Boolean?,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class InternalAccountTO(
    val accountId: String,
    val name: String,
    var email: String,
    var mobilePhone: String?,
    val status: String,
    val configuration: String?,
    val openForUserReview: Boolean?,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class UserAccountRegisterTO(
    val accountId: String,
    val name: String,
    val document: String,
    val email: String,
    val phone: String,
    val status: String,
    val updated: String?,
    val groups: List<String>,
    val groupsNames: List<String>,
    val userAccountType: String?,
    val subscriptionType: String,
)

data class RegisterZipCodeTO(
    val zipCode: String,
)

data class UpdateEmailTO(
    val newEmailAddress: String,
    val currentEmailAddress: String,
)

data class UpdateNameTO(
    val newName: String,
    val currentName: String,
)

data class UpdatePhoneTO(
    val newPhoneNumber: String,
    val currentPhoneNumber: String,
)

data class AccountClosureDetailsTO(
    val reason: String?,
    val description: String?,
)

fun AccountClosureDetailsTO.toAccountClosureDetails(): AccountClosureDetails {
    return AccountClosureDetails.create(
        reason = this.reason?.let { AccountClosureReason.valueOf(it) },
        description = this.description,
    )
}

data class FeaturesTO(
    val maintenanceMode: Boolean,
    val proxy: Boolean,
)

fun Features.toFeaturesTO(): FeaturesTO {
    return FeaturesTO(
        maintenanceMode = maintenanceMode,
        proxy = proxy,
    )
}

data class UpdateFeatureTO(
    val type: String,
    val enabled: Boolean,
)

data class GratuityTO(
    val accountId: String,
    val duration: String,
    val deduplicationId: String,
    val skipParticipantVerification: Boolean? = false,
)

data class FailPaymentTO(
    val billId: String,
    val walletId: String,
    val transactionId: String,
    val errorDescription: String?,
    val errorSource: String,
)