package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionError
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionService
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStatus
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Version("2")
@Controller("/in-app-subscription")
@Secured(Role.Code.OWNER)
@FridayMePoupe
class InAppSubscriptionController(
    private val inAppSubscriptionService: InAppSubscriptionService,
) {

    @Post("/sync")
    fun syncSubscription(authentication: Authentication): HttpResponse<*> {
        val accountId = authentication.toAccountId()

        val markers = Markers.append("accountId", accountId.value)

        val result = inAppSubscriptionService.getSubscriptionRealTime(accountId)

        val subscription = result.getOrElse {
            when (it) {
                is InAppSubscriptionError.GetActiveSubscription -> {
                    logger.error(markers.andAppend("error", it), "InAppSubscriptionController#syncSubscription")
                    return StandardHttpResponses.serverError("Falha ao tentar buscar a assinatura do usuário")
                }

                is InAppSubscriptionError.ExpiredSubscription -> {
                    logger.info(markers.andAppend("error", it), "InAppSubscriptionController#syncSubscription")
                    return StandardHttpResponses.badRequest("4001", "Assinatura expirada")
                }

                is InAppSubscriptionError.MultipleActiveSubscriptions -> {
                    logger.warn(markers.andAppend("error", it), "InAppSubscriptionController#syncSubscription")
                    return StandardHttpResponses.badRequest("4002", "Multiplas assinaturas encontradas")
                }
            }
        }

        return HttpResponse.ok(
            InAppSubscriptionSyncTO(
                active = subscription.status == InAppSubscriptionStatus.ACTIVE,
            ),
        )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(InAppSubscriptionController::class.java)
    }
}

data class InAppSubscriptionSyncTO(
    val active: Boolean,
)