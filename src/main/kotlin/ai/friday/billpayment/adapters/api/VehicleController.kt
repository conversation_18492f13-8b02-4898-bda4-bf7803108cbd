package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.ScopePropagator
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.vehicledebts.EnrollVehicleSource
import ai.friday.billpayment.app.vehicledebts.LicensePlate
import ai.friday.billpayment.app.vehicledebts.Vehicle
import ai.friday.billpayment.app.vehicledebts.VehicleDebts
import ai.friday.billpayment.app.vehicledebts.VehicleDebtsService
import ai.friday.morning.log.andAppend
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@VehicleDebts
@Secured(Role.Code.OWNER)
@Controller("/wallet/{walletId}/vehicles")
@Version("2")
class VehicleController(
    private val vehicleDebtsService: VehicleDebtsService,
    private val scopePropagator: ScopePropagator,
) {
    private val logger = LoggerFactory.getLogger(VehicleController::class.java)

    @Get
    fun getAllVehicles(authentication: Authentication): HttpResponse<*> {
        return scopePropagator.executeWithAccount(authentication.toAccountId()) {
            getAllVehiclesWithScope(authentication)
        }
    }

    @Post
    fun enrollVehicle(@Body body: EnrollVehicleTO): HttpResponse<*> {
        val markers = append("body", body)

        val result = vehicleDebtsService.enrollByVehicle(
            accountId = AccountId(body.accountId),
            licensePlate = LicensePlate(body.licensePlate),
            sync = true,
            description = body.description,
            source = EnrollVehicleSource.APP,
        ).getOrElse {
            logger.error(markers.andAppend("error", it), "VehicleController#enrollVehicle")
            return HttpResponse.badRequest(ResponseTO("4000", "Não foi possível cadastrar o veículo"))
        }

        logger.info(markers, "VehicleController#enrollVehicle")
        return HttpResponse.ok(result)
    }

    @Delete("/{licensePlate}")
    fun withdrawVehicle(@PathVariable licensePlate: String, authentication: Authentication): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val markers = append("accountId", accountId)

        val result = vehicleDebtsService.withdrawByVehicle(accountId, LicensePlate(licensePlate)).getOrElse {
            logger.error(markers.andAppend("error", it), "VehicleController#withdrawVehicle")
            return HttpResponse.badRequest(ResponseTO("4000", "Não foi possível remover o veículo"))
        }

        logger.info(markers, "VehicleController#withdrawVehicle")
        return HttpResponse.ok(result)
    }

    private fun getAllVehiclesWithScope(authentication: Authentication): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val markers = append("accountId", authentication.toAccountId())

        val vehicles = vehicleDebtsService.getAllVehicles(accountId).getOrElse {
            logger.error(markers.andAppend("error", it), "VehicleController#getAllVehicles")
            return HttpResponse.badRequest(ResponseTO("4000", "Não foi possível recuperar os veículos"))
        }

        logger.info(markers.andAppend("vehiclesSize", vehicles.size), "VehicleController#getAllVehicles")
        return HttpResponse.ok(vehicles.map { it.toVehicleTO() })
    }
}

data class EnrollVehicleTO(
    val licensePlate: String,
    val description: String,
    val accountId: String,
)

data class VehicleTO(
    val licensePlate: String,
    val description: String?,
    val accountId: String,
    val status: String,
)

fun Vehicle.toVehicleTO(): VehicleTO {
    return VehicleTO(
        licensePlate = licensePlate.value,
        description = description,
        accountId = accountId.value,
        status = status.name,
    )
}