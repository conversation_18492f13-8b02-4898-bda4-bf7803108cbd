package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.payment.TransactionError
import com.fasterxml.jackson.annotation.JsonInclude
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus

object StandardHttpResponses {
    fun serverError(message: String = "Internal Server Error. Please try again later."): HttpResponse<ResponseTO> =
        HttpResponse.serverError(ResponseTO("500", message))

    fun serverError(responseTO: ResponseTO): HttpResponse<ResponseTO> = HttpResponse.serverError(responseTO)

    fun badRequest(code: String, message: String, billTO: BillTO? = null): HttpResponse<*> =
        HttpResponse.badRequest(ResponseTO(code, message, billTO))

    fun badRequest(code: Int, message: String): HttpResponse<*> =
        HttpResponse.badRequest(ResponseTO(code.toString(), message))

    fun badRequest(response: ResponseTO): HttpResponse<*> = HttpResponse.badRequest(response)
    fun badRequest(transactionError: TransactionError): HttpResponse<ResponseTO> =
        HttpResponse.badRequest(ResponseTO(transactionError.code, transactionError.description))

    fun customStatusResponse(httpStatus: HttpStatus, responseTO: ResponseTO): HttpResponse<ResponseTO> =
        HttpResponse.status<ResponseTO>(httpStatus).body(responseTO)

    fun created(): HttpResponse<ResponseTO> = HttpResponse.created(ResponseTO("000", "Success"))

    fun created(billTO: BillTO): HttpResponse<BillTO> = HttpResponse.created(billTO)

    fun ok(billTO: BillTO): HttpResponse<BillTO> = HttpResponse.ok(billTO)

    fun accountNotFound(): HttpResponse<ResponseTO> = HttpResponse.notFound(ResponseTO("404", "Account not found"))

    fun conflict(responseTO: ResponseTO): HttpResponse<ResponseTO> =
        HttpResponse.status<ResponseTO>(HttpStatus.CONFLICT).body(responseTO)

    fun billNotFound(): HttpResponse<ResponseTO> = HttpResponse.notFound(ResponseTO("404", "Bill not found"))

    fun notFound(message: String): HttpResponse<ResponseTO> = HttpResponse.notFound(ResponseTO("404", message))

    fun notFound(responseTO: ResponseTO): HttpResponse<ResponseTO> = HttpResponse.notFound(responseTO)
}

@JsonInclude(JsonInclude.Include.NON_NULL)
data class ResponseTO(
    val code: String,
    val message: String,
    val bill: BillTO? = null,
)