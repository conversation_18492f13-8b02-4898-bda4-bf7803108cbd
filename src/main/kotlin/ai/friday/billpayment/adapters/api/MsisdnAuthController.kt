package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.auth.JwtValidator
import ai.friday.billpayment.adapters.auth.TO_CLAIM_ACTUAL_ROLE
import ai.friday.billpayment.adapters.auth.TO_CLAIM_EMAIL
import ai.friday.billpayment.adapters.auth.TO_CLAIM_MSISDN_MIGRATED
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.InvalidTokenException
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.TokenChannel
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.isValid
import ai.friday.billpayment.app.msisdnauth.IssueTokenError
import ai.friday.billpayment.app.msisdnauth.IssuedToken
import ai.friday.billpayment.app.msisdnauth.MsisdnAuthId
import ai.friday.billpayment.app.msisdnauth.MsisdnAuthService
import ai.friday.billpayment.app.msisdnauth.ValidateTokenRequest
import ai.friday.billpayment.app.msisdnauth.createTemporaryEmail
import ai.friday.billpayment.app.msisdnauth.extractMobilePhoneFromTemporaryEmail
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import com.nimbusds.jose.JOSEObjectType
import com.nimbusds.jose.JWSAlgorithm
import com.nimbusds.jose.JWSHeader
import com.nimbusds.jose.crypto.MACSigner
import com.nimbusds.jwt.JWTClaimsSet
import com.nimbusds.jwt.JWTParser
import com.nimbusds.jwt.SignedJWT
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Property
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.CookieValue
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.Header
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import jakarta.inject.Named
import java.time.Duration
import java.util.Date
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(SecurityRule.IS_ANONYMOUS)
@Controller("/public/msisdn")
@Version("2")
@FridayMePoupe
class MsisdnAuthController(
    private val msisdnAuthService: MsisdnAuthService,
    private val msisdnAuthConfiguration: MsisdnAuthConfiguration,
    private val cognitoAdapter: UserPoolAdapter,
    private val accountService: AccountService,
    @Property(name = "features.forceTokenViaWhatsapp", defaultValue = "false") private val forceTokenViaWhatsapp: Boolean,
    @Named("msisdn") private val jwtValidator: JwtValidator,
) {
    @Post("/issueToken")
    fun issueToken(@Body request: IssueTokenRequestTO): HttpResponse<*> {
        val markers = Markers.append("request", request)
            .andAppend("forceTokenViaWhatsapp", forceTokenViaWhatsapp)

        val mobilePhone = MobilePhone(request.number)

        if (!mobilePhone.isValid()) {
            return HttpResponse.status<Unit>(HttpStatus.FORBIDDEN)
        }

        val channel = if (forceTokenViaWhatsapp) {
            TokenChannel.WHATSAPP
        } else {
            request.channel
        }

        return msisdnAuthService.issueToken(
            mobilePhone,
            channel = channel,
        )
            .map {
                markers.andAppend("response", it.otpId)

                logger.info(markers, "MsisdnAuthController#issueToken")
                HttpResponse.created(IssueTokenResponseTO.of(it))
            }.getOrElse {
                logger.error(markers, "MsisdnAuthController#issueToken")
                when (it) {
                    is IssueTokenError.Error -> HttpResponse.serverError(it.exception.message)
                }
            }
    }

    @Put("/validateToken")
    fun validateToken(
        @Body request: ValidateTokenRequestTO,
    ): HttpResponse<*> {
        val markers = Markers.append("request", request)

        val tokenKey = request.toOtpTokenKey()
        return msisdnAuthService.validateToken(tokenKey)
            .map { (mobilePhone, accountId) ->
                markers.andAppend("response", mobilePhone)
                val jwt = createMsisdnJWT(accountId, mobilePhone)
                logger.info(markers, "MsisdnAuthController#validateToken")
                HttpResponse.ok(ValidateTokenResponseTO(jwt = jwt, otpId = request.otpId))
            }.getOrElse {
                when (it) {
                    is InvalidTokenException -> {
                        logger.warn(markers.andAppend("reason", it.reason), "MsisdnAuthController#validateToken")
                        HttpResponse.badRequest(it.message)
                    }

                    else -> {
                        logger.error(markers, "MsisdnAuthController#validateToken", it)
                        HttpResponse.serverError(it.message)
                    }
                }
            }
    }

    @Get("/refreshToken")
    @Secured(Role.Code.GUEST)
    fun refreshToken(
        @Header("Authorization") authorizationHeader: String?,
        @CookieValue("JWT") jwtCookie: String?,
    ): HttpResponse<*> {
        val logName = "MsisdnAuthController#refreshToken"
        try {
            val jwtString = (authorizationHeader ?: jwtCookie ?: "").replace("Bearer ", "")
            val token = JWTParser.parse(jwtString)
            val accountId = AccountId(token.jwtClaimsSet.subject)
            val markers = Markers.append("accountId", accountId.value)

            if (!jwtValidator.validate(token.jwtClaimsSet)) {
                logger.info(markers.andAppend("response", "invalid token"), logName)
                return HttpResponse.unauthorized<Unit>()
            }

            val mobilePhone = extractMobilePhoneFromTemporaryEmail(EmailAddress(token.jwtClaimsSet.getClaim("email").toString())) ?: run {
                logger.info(markers.andAppend("response", "invalid email"), logName)
                return HttpResponse.unauthorized<Unit>()
            }

            val jwt = createMsisdnJWT(accountId, mobilePhone)
            logger.info(markers, logName)
            return HttpResponse.ok(ValidateTokenResponseTO(jwt = jwt, otpId = "tokenRefreshed"))
        } catch (e: Exception) {
            logger.warn(logName, e)
            return HttpResponse.unauthorized<Unit>()
        }
    }

    private fun createMsisdnJWT(
        accountId: AccountId,
        mobilePhone: MobilePhone,
    ): String {
        val isGuest = accountService.isGuestAccount(accountId)
        val migrated = isMigrated(accountId, isGuest)
        val actualRole = if (isGuest) {
            Role.GUEST
        } else {
            Role.OWNER
        }

        val claimsSet = JWTClaimsSet.Builder()
            .subject(accountId.value)
            .issuer(msisdnAuthConfiguration.issuer)
            .audience(msisdnAuthConfiguration.audience)
            .claim(TO_CLAIM_EMAIL, createTemporaryEmail(mobilePhone).value)
            .claim(TO_CLAIM_MSISDN_MIGRATED, migrated)
            .claim(TO_CLAIM_ACTUAL_ROLE, actualRole.name)
            .expirationTime(
                Date.from(
                    getZonedDateTime()
                        .plus(msisdnAuthConfiguration.duration).toInstant(),
                ),
            )
            .build()

        val signer = MACSigner(msisdnAuthConfiguration.secret.toByteArray())

        val signedJWT = SignedJWT(
            JWSHeader.Builder(JWSAlgorithm.HS256)
                .type(JOSEObjectType.JWT)
                .build(),
            claimsSet,
        )

        signedJWT.sign(signer)

        return signedJWT.serialize()
    }

    private fun isMigrated(accountId: AccountId, isGuest: Boolean): Boolean? {
        try {
            if (isGuest) return null

            val account = accountService.findAccountById(accountId)
            return cognitoAdapter.doesUserExist(account.document)
        } catch (e: Exception) {
            return null
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(MsisdnAuthController::class.java)
    }
}

data class IssueTokenRequestTO(
    val number: String,
    val channel: TokenChannel,
)

data class IssueTokenResponseTO(
    val duration: String,
    val cooldown: String,
    val otpId: String,
) {
    companion object {
        fun of(issuedToken: IssuedToken): IssueTokenResponseTO {
            return IssueTokenResponseTO(
                duration = issuedToken.duration.seconds.toString(),
                cooldown = issuedToken.cooldown.seconds.toString(),
                otpId = issuedToken.otpId.value,
            )
        }
    }
}

data class ValidateTokenRequestTO(
    val token: String,
    val otpId: String,
)

fun ValidateTokenRequestTO.toOtpTokenKey(): ValidateTokenRequest {
    return ValidateTokenRequest(otpId = MsisdnAuthId(otpId), token = token)
}

data class ValidateTokenResponseTO(
    val jwt: String,
    val otpId: String,
)

@ConfigurationProperties(value = "msisdn-authentication.jwt")
data class MsisdnAuthConfiguration @ConfigurationInject constructor(
    val audience: String,
    val issuer: String,
    val duration: Duration,
    val secret: String,
)