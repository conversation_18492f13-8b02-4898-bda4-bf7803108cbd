package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.intercom.IntercomAdapterException
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountClosureDetails
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountIsNotUnderReview
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.Address
import ai.friday.billpayment.app.account.BasicRegisterService
import ai.friday.billpayment.app.account.DocumentInfo
import ai.friday.billpayment.app.account.ExternalRegisterStatus
import ai.friday.billpayment.app.account.MonthlyIncome
import ai.friday.billpayment.app.account.PartialAccount
import ai.friday.billpayment.app.account.PoliticallyExposed
import ai.friday.billpayment.app.account.RegisterService
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.backoffice.BackofficeRegisterService
import ai.friday.billpayment.app.backoffice.CreateAccountRegisterRequest
import ai.friday.billpayment.app.backoffice.UserAccountRegister
import ai.friday.billpayment.app.dda.DDANotActiveException
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.ECMProviderException
import ai.friday.billpayment.app.integrations.ExternalAccountRegisterException
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.register.instrumentation.CrmContactNotFoundException
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.http.annotation.QueryValue
import io.micronaut.http.server.types.files.StreamedFile
import io.micronaut.security.annotation.Secured
import java.time.LocalDate
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(BACKOFFICE)
@Controller("/backoffice")
@FridayMePoupe
class BackofficeRegisterController(
    private val backofficeRegisterService: BackofficeRegisterService,
    private val registerService: RegisterService,
    private val basicRegisterService: BasicRegisterService,
    private val accountRepository: AccountRepository,
    private val accountRegisterRepository: AccountRegisterRepository,
) {
    @Get("/register")
    fun getRegisters(): HttpResponse<*> {
        return try {
            val registers = backofficeRegisterService.getPending()
            HttpResponse.ok(registers.map { it.toUserAccountRegisterTO() })
        } catch (e: Exception) {
            doHandleError(e, "getRegisters", Markers.empty(), HttpStatus.INTERNAL_SERVER_ERROR, "-1", "Erro genérico")
        }
    }

    @Post("/register/accountRegister")
    fun createAccountRegister(@Body body: CreateAccountRegisterRequestTO): HttpResponse<*> {
        val createAccountRegisterRequest = CreateAccountRegisterRequest(
            address = body.address.toAddress(),
            documentInfo = body.documentInfo.toDocumentInfo(),
            monthlyIncome = MonthlyIncome(
                lowerBound = body.monthlyIncome.lowerBound,
                upperBound = body.monthlyIncome.upperBound,
            ),
            accountId = AccountId(body.accountId),
        )

        backofficeRegisterService.createAccountRegister(createAccountRegisterRequest).getOrElse {
            doHandleError(
                it,
                "createAccountRegister",
                Markers.empty(),
                HttpStatus.INTERNAL_SERVER_ERROR,
                "-1",
                "Erro genérico",
            )
        }

        return HttpResponse.noContent<Unit>()
    }

    @Get("/register/{accountId}")
    fun getRegister(@PathVariable accountId: String): HttpResponse<*> {
        return try {
            val register = backofficeRegisterService.get(AccountId(accountId))
            HttpResponse.ok(register.toUserAccountRegisterTO())
        } catch (e: Exception) {
            handleError(e, null, AccountId(accountId), "getRegister")
        }
    }

    @Put("/register/{accountId}/updateLiveness")
    fun updateLiveness(@PathVariable accountId: String): HttpResponse<*> {
        return try {
            val response = backofficeRegisterService.getUrlToUpdateLiveness(AccountId(accountId))
            LOG.info(append("accountId", accountId).andAppend("response", response), "updateLiveness")
            HttpResponse.ok(mapOf("url" to response))
        } catch (e: Exception) {
            handleError(e, null, AccountId(accountId), "updateLiveness")
        }
    }

    @Put("/register/{accountId}/updateStoredSelfie/{livenessId}")
    fun updateStoredSelfie(@PathVariable accountId: String, @PathVariable livenessId: String): HttpResponse<*> {
        return try {
            backofficeRegisterService.updateStoredSelfie(AccountId(accountId), LivenessId(livenessId))
            HttpResponse.ok(mapOf("result" to "success"))
        } catch (e: Exception) {
            handleError(e, null, AccountId(accountId), "updateStoredSelfie")
        }
    }

    @Post("/register/{accountId}/deny")
    fun postDeny(@PathVariable accountId: String, @Body body: AccountClosureDetailsTO): HttpResponse<*> {
        val markers = append("accountId", accountId).andAppend("body", body)

        val closureDetails = if (body.reason.isNullOrEmpty()) {
            AccountClosureDetails.create(
                reason = null,
                description = "Conta negada pelo backoffice sem motivo discriminado",
            )
        } else {
            try {
                body.toAccountClosureDetails()
            } catch (e: Exception) {
                LOG.error(markers, "postDeny")
                return HttpResponse.badRequest(ResponseTO(code = "4001", message = "Motivo inválido"))
            }
        }

        return backofficeRegisterService.denyAccount(
            AccountId(accountId),
            closureDetails,
        )
            .map<HttpResponse<*>> { HttpResponse.noContent<Unit>() }
            .getOrElse { handleError(it, body.toString(), AccountId(accountId), "postDeny") }
    }

    @Post("/register/{accountId}/review")
    fun postReview(@PathVariable accountId: String): HttpResponse<*> {
        return backofficeRegisterService.reviewAccount(AccountId(accountId))
            .map<HttpResponse<*>> { HttpResponse.noContent<Unit>() }
            .getOrElse { handleError(it, null, AccountId(accountId), "postReview") }
    }

    @Get("/register/{accountId}/contract")
    fun getContract(@PathVariable accountId: String): StreamedFile {
        val contract = registerService.getContract(AccountId(accountId))
        return StreamedFile(contract, MediaType.APPLICATION_PDF_TYPE)
    }

    @Post("/register/{accountId}/internalApprove{?ignoreDocument}")
    fun postInternalApprove(
        @PathVariable accountId: String,
        @QueryValue(defaultValue = "false") ignoreDocument: Boolean,
    ): HttpResponse<*> {
        val markers = append("accountId", accountId).andAppend("ignoreDocument", ignoreDocument)
        LOG.info(markers, "BackofficePostInternalApprove")

        if (ignoreDocument) {
            accountRepository.updatePartialAccountStatus(AccountId(accountId), AccountStatus.UNDER_EXTERNAL_REVIEW)
            return HttpResponse.noContent<Unit>()
        }

        return registerService.internalApproveAccount(AccountId(accountId))
            .map<HttpResponse<*>> { HttpResponse.noContent<Unit>() }
            .getOrElse { handleError(it, null, AccountId(accountId), "BackofficePostInternalApprove") }
    }

    @Post("/register/{accountId}/externalApprove/accept")
    fun externallyApproveAccept(@PathVariable accountId: String): HttpResponse<*> {
        val markers = append("accountId", accountId)

        try {
            val accountRegisterData = accountRegisterRepository.findByAccountId(AccountId(accountId))

            if (accountRegisterData.documentInfo == null) {
                throw IllegalStateException("Document was not found on user")
            }

            return registerService.updateAccountStatus(
                accountRegisterData.documentInfo.cpf,
                ExternalRegisterStatus.APPROVED,
            ).map<HttpResponse<*>> {
                LOG.info(markers.andAppend("setupAccountResult", it), "BackofficeExternallyApproveAccept")
                HttpResponse.noContent<Unit>()
            }.getOrElse {
                handleError(it, null, AccountId(accountId), "BackofficeExternallyApproveAccept")
            }
        } catch (e: Exception) {
            return handleError(e, null, AccountId(accountId), "BackofficeExternallyApproveAccept")
        }
    }

    @Post("/register/{accountId}/externalApprove/reject")
    fun externallyReject(@PathVariable accountId: String): HttpResponse<*> {
        val markers = append("accountId", accountId)

        try {
            val accountRegisterData = accountRegisterRepository.findByAccountId(AccountId(accountId))

            if (accountRegisterData.documentInfo == null) {
                throw IllegalStateException("Document was not found on user")
            }

            return registerService.updateAccountStatus(
                accountRegisterData.documentInfo.cpf,
                ExternalRegisterStatus.REJECTED,
            ).map {
                LOG.info(markers.andAppend("setupAccountResult", it), "BackofficeExternallyReject")
                HttpResponse.noContent<Unit>()
            }.getOrElse {
                handleError(it, null, AccountId(accountId), "BackofficeExternallyReject")
            }
        } catch (e: Exception) {
            return handleError(e, null, AccountId(accountId), "BackofficeExternallyApproveAccept")
        }
    }

    @Post("/register/{accountId}/activateBasic")
    fun postActivateBasic(@PathVariable accountId: String): HttpResponse<*> {
        return basicRegisterService.activateAccount(AccountId(accountId))
            .map<HttpResponse<*>> { HttpResponse.ok(it.toInternalAccountTO()) }
            .getOrElse { handleError(it, null, AccountId(accountId), "postActivate") }
    }

    @Post("/register/{document}/approveBasic")
    fun postApproveBasic(@PathVariable document: String): HttpResponse<*> {
        return basicRegisterService.updateAccountStatus(document, ExternalRegisterStatus.APPROVED)
            .map<HttpResponse<*>> { HttpResponse.ok(it) }
            .getOrElse { handleError(it, null, null, "postApproveBasic") }
    }

    @Post("/register/{accountId}/upgradeAccount")
    fun postUpgradeAccount(@PathVariable accountId: String): HttpResponse<*> {
        return registerService.upgradeAccount(AccountId(accountId))
            .map<HttpResponse<*>> { HttpResponse.noContent<Unit>() }
            .getOrElse { handleError(it, null, null, "postUpgradeAccount") }
    }

    @Post("/register/{accountId}/activate")
    fun postActivate(@PathVariable accountId: String): HttpResponse<*> {
        return registerService.activateAccount(AccountId(accountId))
            .map<HttpResponse<*>> { HttpResponse.ok(it.toInternalAccountTO()) }
            .getOrElse { handleError(it, null, AccountId(accountId), "postActivate") }
    }

    @Post("/register/{accountId}/submitInternalReview")
    fun postSubmitInternalReview(@PathVariable accountId: String): HttpResponse<*> {
        return registerService.submitRegisterForInternalReview(AccountId(accountId))
            .map<HttpResponse<*>> { HttpResponse.ok<Unit>() }
            .getOrElse { handleError(it, null, AccountId(accountId), "generateSensitiveData") }
    }

    @Get("/register/partialAccounts{?status}")
    fun getRegisterAccounts(
        @QueryValue(defaultValue = "UNDER_EXTERNAL_REVIEW") status: AccountStatus,
    ): HttpResponse<*> {
        val internalAccountList =
            registerService.findPartialAccountByStatus(status).filter { (partialAccount, _) ->
                partialAccount.registrationType != RegistrationType.BASIC
            }.map { (partialAccount, accountRegisterData) ->
                partialAccount.toInternalAccountTO(accountRegisterData)
            }
        return HttpResponse.ok(internalAccountList)
    }

    @Post("/register/{accountId}/updateDocumentOrg")
    fun updateDocumentOrg(@PathVariable accountId: String, @Body request: UpdateOrgTO): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId)
            .andAppend("request", request)

        try {
            backofficeRegisterService.updateDocumentOrgEmission(AccountId(accountId), request.newOrg, request.oldOrg)

            LOG.info(markers, "BackofficeRegisterController#updateDocumentOrg")
            return HttpResponse.ok<Unit>()
        } catch (e: IllegalArgumentException) {
            LOG.warn(markers, "BackofficeRegisterController#updateDocumentOrg")
            return HttpResponse.status<Unit>(HttpStatus.CONFLICT)
        } catch (e: Throwable) {
            LOG.error(markers, "BackofficeRegisterController#updateDocumentOrg", e)
            return HttpResponse.serverError<Unit>()
        }
    }

    @Post("/register/{accountId}/updateCity")
    fun updateCity(@PathVariable accountId: String, @Body request: UpdateCityTO): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId)
            .andAppend("request", request)

        try {
            backofficeRegisterService.updateCity(AccountId(accountId), request.newCity, request.oldCity)

            LOG.info(markers, "BackofficeRegisterController#updateDocumentOrg")
            return HttpResponse.ok<Unit>()
        } catch (e: IllegalArgumentException) {
            LOG.warn(markers, "BackofficeRegisterController#updateDocumentOrg")
            return HttpResponse.status<Unit>(HttpStatus.CONFLICT)
        } catch (e: Throwable) {
            LOG.error(markers, "BackofficeRegisterController#updateDocumentOrg", e)
            return HttpResponse.serverError<Unit>()
        }
    }

    @Put("/register/{accountId}/updatePep")
    fun updatePep(@PathVariable accountId: String, @Body request: UpdatePepTO): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId)
            .andAppend("request", request)

        try {
            val accountRegisterData = accountRegisterRepository.findByAccountId(AccountId(accountId))
            val updatedPoliticallyExposed = accountRegisterData.politicallyExposed?.copy(
                selfDeclared = request.selfDeclared,
            ) ?: PoliticallyExposed(
                selfDeclared = request.selfDeclared,
                query = null,
            )

            accountRegisterRepository.save(
                accountRegisterData.copy(
                    politicallyExposed = updatedPoliticallyExposed,
                ),
            )

            LOG.info(markers, "BackofficeRegisterController#updatePep")
            return HttpResponse.ok<Unit>()
        } catch (e: IllegalArgumentException) {
            LOG.warn(markers, "BackofficeRegisterController#updatePep")
            return HttpResponse.status<Unit>(HttpStatus.BAD_REQUEST)
        } catch (e: Exception) {
            LOG.error(markers, "BackofficeRegisterController#updatePep", e)
            return HttpResponse.serverError<Unit>()
        }
    }

    private fun doHandleError(
        exception: Exception,
        logName: String,
        markers: LogstashMarker,
        errorStatus: HttpStatus,
        code: String,
        message: String,
    ): HttpResponse<ResponseTO> {
        LOG.error(
            markers.andAppend("httpStatus", errorStatus),
            logName,
            exception,
        )
        return StandardHttpResponses.customStatusResponse(
            errorStatus,
            ResponseTO(code, message),
        )
    }

    private fun doHandleWarn(
        exception: Exception,
        logName: String,
        markers: LogstashMarker,
        errorStatus: HttpStatus,
        code: String,
        message: String,
    ): HttpResponse<ResponseTO> {
        LOG.warn(
            markers.andAppend("httpStatus", errorStatus),
            logName,
            exception,
        )
        return StandardHttpResponses.customStatusResponse(
            errorStatus,
            ResponseTO(code, message),
        )
    }

    private fun handleError(it: Exception, request: String?, accountId: AccountId?, logName: String): HttpResponse<*> {
        val markers = append("accountId", accountId)
            .andAppend("request", request)
        return when (it) {
            is AccountNotFoundException -> {
                doHandleError(it, logName, markers, HttpStatus.NOT_FOUND, "4001", "conta $accountId não encontrada")
            }

            is AccountIsNotUnderReview -> {
                doHandleError(
                    it,
                    logName,
                    markers,
                    HttpStatus.CONFLICT,
                    "4002",
                    "não é possivel alterar essa conta porque seu estado atual não permite: ${it.currentStatus}",
                )
            }

            is ECMProviderException -> {
                doHandleError(
                    it,
                    logName,
                    markers,
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    "5005",
                    "Erro enviando documentos",
                )
            }

            is ExternalAccountRegisterException -> {
                doHandleError(it, logName, markers, HttpStatus.INTERNAL_SERVER_ERROR, "5006", it.message.orEmpty())
            }

            is IntercomAdapterException -> {
                doHandleError(
                    it,
                    logName,
                    markers,
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    "5002",
                    "erro criando contato no Intercom: ${it.message}",
                )
            }

            is DDANotActiveException -> {
                doHandleWarn(
                    it,
                    logName,
                    markers,
                    HttpStatus.CONFLICT,
                    "4003",
                    "DDA completo para o usuário ainda não foi executado. Tente novamente depois",
                )
            }

            is CrmContactNotFoundException -> {
                doHandleError(
                    it,
                    logName,
                    markers,
                    HttpStatus.CONFLICT,
                    "4004",
                    "Usuário não exite no CRM. Nova tentativa de criação foi feita. Aguarde alguns segundos e tente ativar novamente",
                )
            }

            else -> {
                doHandleError(it, logName, markers, HttpStatus.INTERNAL_SERVER_ERROR, "-1", "erro genérico")
            }
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BackofficeRegisterController::class.java)
    }
}

data class CreateAccountRegisterRequestTO(
    val address: AddressTO,
    val documentInfo: DocumentDataRequestTO,
    val monthlyIncome: MonthlyIncomeRequestTO,
    val accountId: String,
)

data class UpdateOrgTO(
    val oldOrg: String,
    val newOrg: String,
)

data class UpdateCityTO(
    val oldCity: String,
    val newCity: String,
)

data class UpdatePepTO(
    val selfDeclared: Boolean,
)

private fun Account.toInternalAccountTO() = InternalAccountTO(
    accountId = accountId.value,
    name = name,
    email = emailAddress.value,
    mobilePhone = mobilePhone,
    status = status.name,
    configuration = getObjectMapper().writeValueAsString(configuration),
    openForUserReview = null,
)

private fun PartialAccount.toInternalAccountTO(accountRegisterData: AccountRegisterData?) = InternalAccountTO(
    accountId = id.value,
    name = name,
    email = emailAddress.value,
    mobilePhone = accountRegisterData?.mobilePhone?.msisdn,
    status = status.name,
    configuration = null,
    openForUserReview = accountRegisterData?.openForUserReview,
)

fun UserAccountRegister.toUserAccountRegisterTO() = UserAccountRegisterTO(
    accountId = accountId.value,
    name = name,
    document = document,
    email = email.value,
    phone = phone.toString(),
    status = status.name,
    updated = updated?.format(dateTimeFormat),
    groups = groups.map { it.value },
    groupsNames = groups.map { it.name },
    userAccountType = userAccountType?.name,
    subscriptionType = subscriptionType.name,
)

private fun AddressTO.toAddress() = Address(
    streetType = this.streetType,
    streetName = this.streetName,
    number = this.number,
    complement = this.complement,
    neighborhood = this.neighborhood,
    city = this.city,
    state = this.state,
    zipCode = this.zipCode,
)

private fun DocumentDataRequestTO.toDocumentInfo() = DocumentInfo(
    name = this.name,
    cpf = this.cpf,
    birthDate = LocalDate.parse(this.birthDate),
    fatherName = this.fatherName,
    motherName = this.motherName,
    rg = this.rgNumber ?: "",
    docType = this.cnhNumber?.let { DocumentType.CNH } ?: DocumentType.RG,
    cnhNumber = this.cnhNumber,
    orgEmission = this.orgEmission,
    expeditionDate = LocalDate.parse(this.expeditionDate),
    birthCity = this.birthCity,
    birthState = this.birthState,
)