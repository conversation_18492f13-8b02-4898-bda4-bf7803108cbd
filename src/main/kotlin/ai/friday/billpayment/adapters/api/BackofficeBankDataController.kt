package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.integrations.BankDataService
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.QueryValue
import io.micronaut.security.annotation.Secured
import jakarta.inject.Named
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.BACKOFFICE)
@Controller("/backoffice/bankdata")
@FridayMePoupe
class BackofficeBankDataController(
    @Named("arbiBankDataServiceV1") private val arbiBankDataServiceV1: BankDataService,
    @Named("arbiBankDataServiceV2") private val arbiBankDataServiceV2: BankDataService,
) {
    private val logger = LoggerFactory.getLogger(BackofficeBankDataController::class.java)

    @Get("/holidays{?v1}")
    fun getHolidays(@QueryValue(defaultValue = "true") v1: Boolean): HttpResponse<*> {
        val markers = Markers.append("v1", v1)
        return try {
            val bankDataService = if (v1) arbiBankDataServiceV1 else arbiBankDataServiceV2

            val domain = bankDataService.getHolidays()

            logger.info(markers, "BackofficeBankDataController#getHolidays")
            HttpResponse.ok(domain)
        } catch (e: Exception) {
            logger.error(markers, "BackofficeBankDataController#getHolidays", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Get("/bankcodes{?v1}")
    fun getBankCodes(@QueryValue(defaultValue = "true") v1: Boolean): HttpResponse<*> {
        val markers = Markers.append("v1", v1)
        return try {
            val bankDataService = if (v1) arbiBankDataServiceV1 else arbiBankDataServiceV2

            val domain = bankDataService.getBankCodes()

            logger.info(markers, "BackofficeBankDataController#getBankCodes")
            HttpResponse.ok(domain)
        } catch (e: Exception) {
            logger.error(markers, "BackofficeBankDataController#getBankCodes", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Get("/pixparticipants{?v1}")
    fun getPixParticipants(@QueryValue(defaultValue = "true") v1: Boolean): HttpResponse<*> {
        val markers = Markers.append("v1", v1)
        return try {
            val bankDataService = if (v1) arbiBankDataServiceV1 else arbiBankDataServiceV2

            val domain = bankDataService.getPixParticipants()

            logger.info(markers, "BackofficeBankDataController#getPixParticipants")
            HttpResponse.ok(domain)
        } catch (e: Exception) {
            logger.error(markers, "BackofficeBankDataController#getPixParticipants", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }
}