package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.statement.StatementError
import ai.friday.billpayment.app.statement.StatementService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.OWNER)
@Controller("/statement")
@Version("2")
@FridayMePoupe
class StatementController(
    private val statementService: StatementService,
) {
    private val logger = LoggerFactory.getLogger(StatementController::class.java)

    @Post("/{walletId}")
    fun generateStatement(
        authentication: Authentication,
        @PathVariable walletId: String,
        @Body body: StatementRequestTO,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()

        val marker = Markers.append("accountId", accountId.value)
            .andAppend("walletId", walletId)
            .andAppend("startDate", body.startDate)
            .andAppend("endDate", body.endDate)

        try {
            val parsedStartDate = LocalDate.parse(body.startDate, DateTimeFormatter.ISO_DATE)
            val parsedEndDate = LocalDate.parse(body.endDate, DateTimeFormatter.ISO_DATE)

            val result =
                statementService.requestStatement(accountId, WalletId(walletId), parsedStartDate, parsedEndDate)

            result.getOrElse {
                return handleError(marker, it, "StatementController#RequestStatement")
            }

            logger.info(marker, "StatementController#RequestStatement")
            return HttpResponse.accepted<Unit>()
        } catch (e: Exception) {
            return handleError(marker, e, "StatementController#RequestStatement")
        }
    }

    @Get("/{walletId}/payment-method/creation")
    fun getWalletPaymentMethodCreation(
        authentication: Authentication,
        @PathVariable walletId: String,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val marker = Markers.append("accountId", accountId.value)
            .andAppend("walletId", walletId)
        return try {
            val result = statementService.getWalletPaymentMethodCreation(WalletId(walletId))
            result.fold(
                ifLeft = { handleError(marker, it, "StatementController#getWalletPaymentMethodCreation") },
                ifRight = { HttpResponse.ok(WalletCreationDateResponseTO(it.format(DateTimeFormatter.ISO_DATE))) },
            )
        } catch (e: Exception) {
            handleError(marker, e, "StatementController#RequestStatement")
        }
    }

    private fun handleError(markers: LogstashMarker, e: Exception, message: String): HttpResponse<*> {
        val httpStatus = when (e) {
            is DateTimeParseException -> HttpStatus.BAD_REQUEST
            is StatementError.NotAllowed -> HttpStatus.METHOD_NOT_ALLOWED

            is StatementError.ServerError,
            is StatementError.WalletNotFound,
            is StatementError.FailedToSendEmail,
            -> HttpStatus.INTERNAL_SERVER_ERROR

            else -> HttpStatus.INTERNAL_SERVER_ERROR
        }

        markers.andAppend("httpStatus", httpStatus.code).also {
            logger.error(it, message, e)
        }

        return HttpResponse.status<Unit>(httpStatus)
    }
}

data class StatementRequestTO(
    val startDate: String,
    val endDate: String,
)

data class WalletCreationDateResponseTO(
    val walletCreationDate: String,
)