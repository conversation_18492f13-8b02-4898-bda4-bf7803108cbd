package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.arbi.ArbiAdapterException
import ai.friday.billpayment.adapters.modatta.api.CreateModattaPixTO
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.CreateBillError
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateBillService
import ai.friday.billpayment.app.bill.CreatePixRequest
import ai.friday.billpayment.app.bill.RecipientRequest
import ai.friday.billpayment.app.bill.schedule.ScheduleBillService
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.payment.ScheduleStrategy
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.isValid
import ai.friday.billpayment.app.register.SimpleSignUpRequest
import ai.friday.billpayment.app.register.SimpleSignUpService
import ai.friday.billpayment.app.register.UserDataValidationRequest
import ai.friday.billpayment.app.register.UserDataValidationResult
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.validation.Validated
import jakarta.validation.Valid
import java.time.LocalDate
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.MODATTA_B2B)
@Controller("/b2b/simpleSignUp")
@FridayMePoupe
class B2BSimpleSignUpController(
    private val simpleSignUpService: SimpleSignUpService,
) {

    @Put("/validate")
    fun validate(authentication: Authentication, @Body request: UserDataValidationRequestTO): HttpResponse<*> {
        val markers = Markers.append("authentication", authentication.name)
            .andAppend("request", request)

        return try {
            val response = simpleSignUpService.validate(request.toUserDataValidationRequest())
            markers.andAppend("response", response)

            LOG.info(markers, "B2BSimpleSignUpController#validate")
            HttpResponse.ok(response.toUserDataValidationResponseTO())
        } catch (e: Exception) {
            LOG.error(markers, "B2BSimpleSignUpController#validate", e)
            HttpResponse.serverError<Unit>()
        }
    }

    @Post
    fun simpleSignUp(authentication: Authentication, @Body request: SimpleSignUpRequestTO): HttpResponse<*> {
        val markers = Markers.append("authentication", authentication.name)
            .andAppend("request", request)

        return simpleSignUpService.signUp(request.toSimpleSignUpRequest()).map {
            markers.andAppend("response", it)
            LOG.info(markers, "B2BSimpleSignUpController#simpleSignUp")
            HttpResponse.ok(
                SimpleSignUpResponseTO(
                    accountId = it.accountId.value,
                ),
            )
        }.getOrElse {
            if (it is IllegalStateException) {
                LOG.warn(markers, "B2BSimpleSignUpController#simpleSignUp", it)
                StandardHttpResponses.badRequest(code = "4001", message = "Dados inválidos")
            } else {
                LOG.error(markers, "B2BSimpleSignUpController#simpleSignUp", it)
                HttpResponse.serverError<Unit>()
            }
        }
    }

    private fun UserDataValidationRequestTO.toUserDataValidationRequest() = UserDataValidationRequest(
        document = Document(value = document),
        mobilePhone = MobilePhone(msisdn = mobilePhone),
    )

    private fun SimpleSignUpRequestTO.toSimpleSignUpRequest() = SimpleSignUpRequest(
        externalId = ExternalId(value = externalId, providerName = AccountProviderName.valueOf(externalIdProvider)),
        name = name,
        document = Document(value = document),
        birthDate = LocalDate.parse(birthDate, dateFormat),
        email = EmailAddress(email = email),
        mobilePhone = MobilePhone(msisdn = mobilePhone),
        livenessId = LivenessId(value = livenessId),
        userContractKey = userContractKey,
        userContractSignature = userContractSignature,
    )

    private fun UserDataValidationResult.toUserDataValidationResponseTO() = UserDataValidationResponseTO(
        valid = valid,
        accountId = accountId?.value,
    )

    companion object {
        private val LOG = LoggerFactory.getLogger(B2BSimpleSignUpController::class.java)
    }
}

data class UserDataValidationRequestTO(
    val document: String,
    val mobilePhone: String,
)

data class UserDataValidationResponseTO(
    val valid: Boolean,
    val accountId: String?,
)

data class SimpleSignUpRequestTO(
    val name: String,
    val document: String,
    val birthDate: String,
    val email: String,
    val mobilePhone: String,
    val livenessId: String,
    val externalId: String,
    val externalIdProvider: String,
    val userContractKey: String,
    val userContractSignature: String,
)

data class SimpleSignUpResponseTO(
    val accountId: String,
)

@Secured(Role.Code.MODATTA_B2B)
@Controller("/b2b/balance")
@FridayMePoupe
class B2BBalanceController(
    private val balanceService: BalanceService,
    private val accountService: AccountService,
    private val walletService: WalletService,
) {
    private val logger = LoggerFactory.getLogger(B2BBalanceController::class.java)

    @Get("/amount/{accountId}")
    fun getAmountBalance(authentication: Authentication, @PathVariable accountId: String): HttpResponse<*> {
        val markers = Markers.append("authentication", authentication.name)
        try {
            val account = accountService.findAccountById(AccountId(accountId))

            markers.andAppend("accountId", account.accountId.value)

            if (account.status == AccountStatus.CLOSED) {
                logger.error(
                    markers.andAppend("reason", "account is closed")
                        .andAppend("httpStatus", HttpStatus.BAD_REQUEST.code),
                    "GetCashinAmount",
                )
                return StandardHttpResponses.badRequest(code = "4001", message = "Account closed")
            }

            val wallet = walletService.findPrimaryWallet(account.accountId)

            markers.andAppend("walletId", wallet.id.value)

            val walletMember = wallet.founder

            if (!walletMember.permissions.viewBalance) {
                logger.error(
                    markers.andAppend("reason", "member has viewBalance false")
                        .andAppend("httpStatus", HttpStatus.FORBIDDEN.code),
                    "GetCashinAmount",
                )
                return HttpResponse.status<Any>(HttpStatus.FORBIDDEN)
            }

            val balance = balanceService.getBalanceFrom(wallet.founder.accountId, wallet.paymentMethodId)

            return HttpResponse.ok(AmountBalanceTO(amount = balance.amount))
        } catch (e: Exception) {
            return handleError(AccountId(accountId), e, "GetCashinAmount")
        }
    }

    @Get("/cashout/{accountId}")
    fun getCashoutBankAccount(@PathVariable accountId: String): HttpResponse<*> {
        try {
            val account = accountService.findAccountById(AccountId(accountId))

            if (account.status == AccountStatus.CLOSED) {
                return StandardHttpResponses.badRequest(code = "4001", message = "Account closed")
            }

            val wallet = walletService.findPrimaryWallet(account.accountId)
            val bankAccount = accountService.findAccountPaymentMethodByIdAndAccountId(
                wallet.paymentMethodId,
                account.accountId,
            ).method as InternalBankAccount

            return HttpResponse.ok(bankAccount.toInternalBankAccountTO())
        } catch (e: AccountNotFoundException) {
            return StandardHttpResponses.accountNotFound()
        }
    }

    private fun handleError(accountId: AccountId, e: Exception, message: String): HttpResponse<ResponseTO> {
        val httpStatus = when (e) {
            is ArbiAdapterException -> HttpStatus.BAD_GATEWAY
            is AccountNotFoundException -> HttpStatus.NOT_FOUND
            else -> HttpStatus.INTERNAL_SERVER_ERROR
        }

        Markers.append("accountId", accountId.value).andAppend("httpStatus", httpStatus.code).also {
            logger.error(it, message, e)
        }

        return HttpResponse.status<ResponseTO>(httpStatus)
            .body(
                ResponseTO(
                    code = httpStatus.code.toString(),
                    message = e.message ?: "Internal Server Error. Please try again later.",
                ),
            )
    }
}

@Validated
@Secured(Role.Code.MODATTA_B2B)
@Controller("/b2b/bill")
@FridayMePoupe
open class B2BBillController(
    private val createBillService: CreateBillService,
    private val scheduleBillService: ScheduleBillService,
    private val accountService: AccountService,
    private val walletService: WalletService,
    private val walletLimitsService: WalletLimitsService,
) {

    @Post("/pix")
    fun postPix(
        @Body @Valid
        createPixTO: CreateModattaPixTO,
    ): HttpResponse<*> {
        val accountId = AccountId(createPixTO.accountId)
        val walletId = WalletId(createPixTO.accountId)
        val account = accountService.findAccountById(accountId)
        val request = CreatePixRequest(
            description = createPixTO.description,
            dueDate = LocalDate.now(),
            amount = createPixTO.amount,
            recipient = RecipientRequest(
                id = null,
                accountId = accountId,
                name = account.name,
                document = account.document,
                alias = "",
                bankAccount = createPixTO.bankDetails?.toBankAccount(),
                pixKey = createPixTO.pixKey?.toPixKey(),
                qrCode = null,
            ),
            source = ActionSource.Api(accountId),
            walletId = walletId,
            automaticPixAuthorizationMaximumAmount = null,
            automaticPixData = null,
        )

        val marker = Markers.append("accountId", accountId.value)
            .and<LogstashMarker>(Markers.append("walletId", walletId.value))
            .and<LogstashMarker>(Markers.append("request", createPixTO))

        if ((request.recipient.bankAccount == null && request.recipient.pixKey == null) ||
            (request.recipient.bankAccount != null && request.recipient.pixKey != null)
        ) {
            return StandardHttpResponses.badRequest(
                CreateBillError.PIX_MUST_HAVE_BANK_ACCOUNT_OR_PIX_KEY_OR_QRCODE.code,
                CreateBillError.PIX_MUST_HAVE_BANK_ACCOUNT_OR_PIX_KEY_OR_QRCODE.description,
            )
        }

        if (request.recipient.pixKey != null && !request.recipient.pixKey.isValid()) {
            LOG.warn(marker.and(Markers.append("error", CreateBillError.PIX_KEY_INVALID)), "AddPixModatta")
            return StandardHttpResponses.badRequest(
                CreateBillError.PIX_KEY_INVALID.code,
                CreateBillError.PIX_KEY_INVALID.description + request.recipient.pixKey.type,
            )
        }

        val wallet = walletService.findWallet(walletId)

        if (walletLimitsService.getAvailableLimit(
                wallet.id,
                getLocalDate(),
                true,
            ) < request.amount
        ) {
            LOG.warn(marker.and(Markers.append("error", CreateBillError.DAILY_LIMIT_EXCEEDED)), "AddPixModatta")
            return StandardHttpResponses.badRequest(
                CreateBillError.DAILY_LIMIT_EXCEEDED.code,
                CreateBillError.DAILY_LIMIT_EXCEEDED.description,
            )
        }

        val monthlyLimit = walletLimitsService.getAvailableMonthlyLimit(
            walletId = wallet.id,
            countScheduled = true,
        )

        if (monthlyLimit == null) {
            val context = "Wallet utilizada no B2B deveria ter um valor de limite mensal definido"
            LOG.error(
                marker.andAppend("ACTION", "VERIFY")
                    .andAppend("context", context),
                "AddPixModatta",
            )
            throw IllegalStateException(context)
        }

        if (monthlyLimit < request.amount
        ) {
            LOG.warn(marker.and(Markers.append("error", CreateBillError.MONTHLY_LIMIT_EXCEEDED)), "AddPixModatta")
            return StandardHttpResponses.badRequest(
                CreateBillError.MONTHLY_LIMIT_EXCEEDED.code,
                CreateBillError.MONTHLY_LIMIT_EXCEEDED.description,
            )
        }

        val createBillResult = createBillService.createPix(request, dryRun = false)
        if (createBillResult is CreateBillResult.FAILURE) {
            LOG.error(
                marker.and(Markers.append("error", CreateBillError.SERVER_ERROR).andAppend("step", "create")),
                "AddPixModatta",
            )
            return StandardHttpResponses.serverError(
                ResponseTO(
                    CreateBillError.SERVER_ERROR.code,
                    CreateBillError.SERVER_ERROR.description,
                ),
            )
        }

        val bill = (createBillResult as CreateBillResult.SUCCESS).bill
        scheduleBillService.schedulePayment(
            paymentWallet = wallet,
            bill = bill,
            member = wallet.founder,
            actionSource = ActionSource.Api(accountId),
            scheduleStrategy = ScheduleStrategy.ofAsap(),
        ).map { scheduledBill ->
            LOG.info(
                Markers
                    .append("walletId", walletId.value)
                    .andAppend("result", scheduledBill),
                "AddPixModatta",
            )
            scheduleBillService.processScheduleAsync(wallet.id)
            return HttpResponse.created(CreateModattaPixResponseTO(scheduledBill.billId.value))
        }.getOrElse {
            LOG.error(
                marker.and(Markers.append("error", CreateBillError.SERVER_ERROR).andAppend("step", "schedule")),
                "AddPixModatta",
                it,
            )
            return StandardHttpResponses.serverError(
                ResponseTO(
                    CreateBillError.SERVER_ERROR.code,
                    CreateBillError.SERVER_ERROR.description,
                ),
            )
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(B2BBillController::class.java)
    }
}

data class CreateModattaPixResponseTO(val billId: String)

private fun PixKeyRequestTO?.toPixKey(): PixKey? {
    if (this == null) {
        return null
    }

    return PixKey(
        type = type,
        value = value,
    )
}

@Secured(Role.Code.MODATTA_B2B)
@Controller("/b2b/accountStatus")
@FridayMePoupe
open class B2BAccountStatusController(
    private val accountService: AccountService,
) {
    private val logger = LoggerFactory.getLogger(B2BAccountStatusController::class.java)

    @Get("/{accountId}")
    fun getAccountStatus(authentication: Authentication, @PathVariable accountId: String): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId)

        return try {
            val account = accountService.findAccountById(AccountId(accountId))
            logger.info(markers.andAppend("status", account.status.name), "B2BAccountStatusController#getAccountStatus")
            HttpResponse.ok(account.status.name)
        } catch (e: Exception) {
            logger.error(markers, "B2BAccountStatusController#getAccountStatus", e)
            HttpResponse.serverError<Unit>()
        }
    }
}