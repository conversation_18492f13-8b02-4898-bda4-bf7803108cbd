package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountIsBlockedForEdition
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.Address
import ai.friday.billpayment.app.account.DocumentDetails
import ai.friday.billpayment.app.account.InvalidMonthlyIncomeException
import ai.friday.billpayment.app.account.InvalidTokenException
import ai.friday.billpayment.app.account.InvalidTokenReason
import ai.friday.billpayment.app.account.MobilePhoneAlreadyInUseException
import ai.friday.billpayment.app.account.MonthlyIncome
import ai.friday.billpayment.app.account.PoliticallyExposed
import ai.friday.billpayment.app.account.RegisterIncompleteException
import ai.friday.billpayment.app.account.RegisterService
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.account.TrialDuration
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.formatOrEmpty
import ai.friday.billpayment.app.integrations.DocumentMustBeOpenedException
import ai.friday.billpayment.app.integrations.FaceNotFoundException
import ai.friday.billpayment.app.integrations.InvalidDocumentImageException
import ai.friday.billpayment.app.liveness.LivenessErrors
import ai.friday.billpayment.app.msisdnauth.isTemporaryEmail
import ai.friday.billpayment.app.utils.PublicHttpLinkGenerator
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonInclude
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Property
import io.micronaut.core.annotation.Introspected
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.http.annotation.QueryValue
import io.micronaut.http.multipart.CompletedFileUpload
import io.micronaut.http.server.util.HttpClientAddressResolver
import io.micronaut.security.authentication.Authentication
import jakarta.validation.Valid
import jakarta.validation.constraints.Pattern
import java.time.Duration
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

private val registerDateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy")

const val targetGroupA = "z983ft"
const val targetGroupB = "mtc3bg"
const val targetGroupIncomeLimit = 1000000

abstract class RegisterController(
    protected val registerService: RegisterService,
    protected val accountService: AccountService,
    protected val systemActivityService: SystemActivityService,
    protected val configuration: AccountRegisterConfiguration,
    protected val addressResolver: HttpClientAddressResolver,
    @Property(name = "urls.termsOfUse") private val agreementFileUrl: String,
    @Property(name = "accountRegister.user_files.contractLinkDuration") private val s3LinkDuration: Duration,
    protected val publicHttpLinkGenerator: PublicHttpLinkGenerator,
    protected val logNamePrefix: String,
) {
    @Get
    protected open fun retrieveAccountRegisterData(
        authentication: Authentication,
        request: HttpRequest<*>,
    ): HttpResponse<*> {
        val markers = append("accountId", authentication.toAccountId().value)
        return registerService.findByAccountId(authentication.toAccountId()).map<HttpResponse<*>> {
            LOG.info(markers.andAppend("response", it), "${logNamePrefix}retrieveAccountRegisterData")
            val clientIp = addressResolver.resolve(request) ?: ""
            HttpResponse.ok(it.toAccountRegisterDataTO(clientIp))
        }.getOrElse {
            withLogError(
                it,
                HttpStatus.INTERNAL_SERVER_ERROR,
                authentication,
                "retrieveAccountRegisterData",
                RegisterErrors.GENERIC_ERROR,
            )
        }
    }

    @Deprecated("O documento agora é enviado via scanner")
    @Post("/document/cnh", consumes = [MediaType.MULTIPART_FORM_DATA], produces = [MediaType.APPLICATION_JSON])
    open fun uploadCnh(
        authentication: Authentication,
        document: CompletedFileUpload,
        request: HttpRequest<*>,
        @QueryValue ocr: Boolean?,
    ): HttpResponse<*> {
        return processUploadedDocument(
            authentication = authentication,
            cnhDocument = document,
            request = request,
            ocr = ocr ?: true,
        )
    }

    @Deprecated("O documento agora é enviado via scanner")
    @Post("document/rg/frontSide", consumes = [MediaType.MULTIPART_FORM_DATA], produces = [MediaType.APPLICATION_JSON])
    open fun uploadFrontRg(
        authentication: Authentication,
        @QueryValue ocr: Boolean?,
        document: CompletedFileUpload,
        request: HttpRequest<*>,
    ): HttpResponse<*> {
        return processUploadedDocument(
            authentication = authentication,
            rgFrontSide = document,
            request = request,
            ocr = ocr ?: true,
        )
    }

    @Deprecated("O documento agora é enviado via scanner")
    @Post("document/rg/backSide", consumes = [MediaType.MULTIPART_FORM_DATA], produces = [MediaType.APPLICATION_JSON])
    open fun uploadBackRg(
        authentication: Authentication,
        document: CompletedFileUpload,
        request: HttpRequest<*>,
        @QueryValue ocr: Boolean?,
    ): HttpResponse<*> {
        return processUploadedDocument(
            authentication = authentication,
            rgBackSide = document,
            request = request,
            ocr = ocr ?: true,
        )
    }

    @Put("/document/details")
    open fun upsertDocumentDetails(
        authentication: Authentication,
        @Body @Valid
        documentDetails: DocumentDetailsTO,
        request: HttpRequest<*>,
    ): HttpResponse<*> {
        val markers = append("accountId", authentication.toAccountId().value)
            .andAppend("request", documentDetails)

        return registerService.processDocumentDetails(
            authentication.toAccountId(),
            DocumentDetails(
                fatherName = documentDetails.fatherName,
                motherName = documentDetails.motherName,
                birthCity = documentDetails.birthCity,
                birthState = documentDetails.birthState.uppercase(),
                orgEmission = documentDetails.orgEmission,
                documentNumber = documentDetails.documentNumber,
                expeditionDate = LocalDate.parse(documentDetails.expeditionDate, registerDateFormatter),
            ),
        )
            .map<HttpResponse<*>> {
                LOG.info(markers.andAppend("response", it), "${logNamePrefix}upsertDocumentDetails")

                val clientIp = addressResolver.resolve(request) ?: ""
                HttpResponse.ok(it.toAccountRegisterDataTO(clientIp))
            }.getOrElse {
                handleError(it, authentication, "upsertDocumentDetails", markers)
            }
    }

    @Put("/address")
    open fun updateAddress(
        authentication: Authentication,
        @Body @Valid
        addressRequest: AddressRequestTO,
        request: HttpRequest<*>,
    ): HttpResponse<*> {
        println(addressRequest.zipCode)
        val markers = append("accountId", authentication.toAccountId().value)
        return registerService.processAddress(authentication.toAccountId(), addressRequest.toAddress())
            .map<HttpResponse<*>> {
                LOG.info(markers.andAppend("response", it), "${logNamePrefix}updateAddress")
                val clientIp = addressResolver.resolve(request) ?: ""
                HttpResponse.ok(it.toAccountRegisterDataTO(clientIp))
            }.getOrElse {
                handleError(it, authentication, "updateAddress")
            }
    }

    @Put("/monthlyIncome")
    open fun updateMonthlyIncome(
        authentication: Authentication,
        @Body monthlyIncomeRequestTO: MonthlyIncomeRequestTO,
        request: HttpRequest<*>,
    ): HttpResponse<*> {
        val markers = append("accountId", authentication.toAccountId().value)
        return registerService.processMonthlyIncome(
            authentication.toAccountId(),
            MonthlyIncome(
                lowerBound = monthlyIncomeRequestTO.lowerBound,
                upperBound = monthlyIncomeRequestTO.upperBound,
            ),
        )
            .map<HttpResponse<*>> {
                LOG.info(markers.andAppend("response", it), "${logNamePrefix}updateMonthlyIncome")
                val clientIp = addressResolver.resolve(request) ?: ""
                HttpResponse.ok(it.toAccountRegisterDataTO(clientIp))
            }.getOrElse {
                handleError(it, authentication, "updateMonthlyIncome")
            }
    }

    // TODO: deveria ir pro basic
    @Put("/liveness/enroll")
    open fun livenessEnrollment(
        authentication: Authentication,
        request: HttpRequest<*>,
    ): HttpResponse<*> {
        val loggerName = "${logNamePrefix}livenessEnrollment"
        val accountId = AccountId(authentication.name)
        val markers = append("accountId", accountId.value)
        return registerService.createLivenessId(accountId).map<HttpResponse<*>> { accountRegisterData ->
            LOG.info(markers.andAppend("response", accountRegisterData), loggerName)
            val clientIp = addressResolver.resolve(request) ?: ""
            HttpResponse.ok(accountRegisterData.toAccountRegisterDataTO(clientIp))
        }.getOrElse {
            return when (it) {
                LivenessErrors.EnrollmentUnavailable -> HttpResponse.status<Unit>(HttpStatus.CONFLICT)
                LivenessErrors.DuplicationCheckUnavailable,
                LivenessErrors.MatchUnavailable,
                LivenessErrors.AccountNotFound,
                -> handleError(
                    IllegalStateException("should not happen"),
                    authentication,
                    loggerName,
                )

                is LivenessErrors.Error -> handleError(it.e, authentication, loggerName)
            }
        }
    }

    // TODO: remover, pois o frontend vai migrar para o /basicRegister/flow/:name
    @Put("/flow/{name}")
    open fun registerFlow(
        authentication: Authentication,
        @PathVariable name: String,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val markers = append("accountId", accountId.value).andAppend("registerFlow", name)
        try {
            when (RegisterFlow.valueOf(name)) {
                RegisterFlow.BANKING_PARTNERSHIP -> {
                    systemActivityService.setAcceptedBankingPartnership(accountId)
                }

                RegisterFlow.BILLING_CAROUSEL -> {
                    accountService.addGroups(
                        accountId,
                        listOf(AccountGroup.SUBSCRIPTION_STANDARD_PLAN),
                    )
                    systemActivityService.setAcceptedBillingCarousel(accountId)
                }

                RegisterFlow.ACCEPTED_TRIAL -> {
                    accountService.addGroups(
                        accountId,
                        listOf(AccountGroup.SUBSCRIPTION_STANDARD_PLAN),
                    )
                    systemActivityService.setAcceptedTrial(
                        accountId,
                        TrialDuration.days,
                    )
                }

                RegisterFlow.ACCOUNT_CREATION_STARTED ->
                    systemActivityService.setCreateAccountStarted(accountId)

                RegisterFlow.SUBSCRIBED -> {
                    systemActivityService.setSubscribed(accountId)
                }
            }

            return HttpResponse.noContent<Unit>().also {
                LOG.info(
                    markers.andAppend("httpStatus", it.status),
                    "${logNamePrefix}registerFlow",
                )
            }
        } catch (e: IllegalArgumentException) {
            return withLogInfo(
                HttpStatus.BAD_REQUEST,
                authentication,
                "registerFlow",
                RegisterErrors.GENERIC_ERROR,
                markers,
            )
        } catch (e: Exception) {
            return withLogError(
                e,
                HttpStatus.INTERNAL_SERVER_ERROR,
                authentication,
                "registerFlow",
                RegisterErrors.GENERIC_ERROR,
                markers,
            )
        }
    }

    @Put("/agreement")
    protected abstract fun processAgreement(
        authentication: Authentication,
        @Body agreementRequestTO: AgreementRequestTO,
        request: HttpRequest<*>,
    ): HttpResponse<*>

    private fun processUploadedDocument(
        authentication: Authentication,
        cnhDocument: CompletedFileUpload? = null,
        rgFrontSide: CompletedFileUpload? = null,
        rgBackSide: CompletedFileUpload? = null,
        request: HttpRequest<*>,
        ocr: Boolean,
    ): HttpResponse<*> {
        val document = cnhDocument ?: rgFrontSide ?: rgBackSide ?: return withLogInfo(
            HttpStatus.BAD_REQUEST,
            authentication,
            "uploadDocument",
            RegisterErrors.INVALID_DOCUMENT_IMAGE,
            append("error_message", "no file found"),
        )

        val markers: LogstashMarker =
            append("accountId", authentication.name)
                .andAppend("originalDocumentSize", document.size)
                .andAppend("document", if (cnhDocument == null) "RG" else "CNH")

        if (cnhDocument != null) {
            markers.andAppend("side", if (rgFrontSide == null) "back" else "front")
        }

        if (document.size > configuration.maxDocumentSize.toLong()) {
            markers.and<LogstashMarker>(
                append(
                    "originalDocumentMaxAllowedSize",
                    configuration.maxDocumentSize.toLong(),
                ),
            )
            return withLogInfo(
                HttpStatus.REQUEST_ENTITY_TOO_LARGE,
                authentication,
                "uploadDocument",
                RegisterErrors.MAX_FILE_SIZE_EXCEEDED,
                markers,
            )
        }

        val extension = document.fileExtension()

        markers.andAppend("extension", extension)

        if (extension == null) {
            return withLogInfo(
                HttpStatus.UNSUPPORTED_MEDIA_TYPE,
                authentication,
                "uploadDocument",
                RegisterErrors.INVALID_DOCUMENT_MEDIA_TYPE,
                markers,
            )
        }

        return if (cnhDocument != null) {
            registerService.processCNHDocumentFile(
                authentication.toAccountId(),
                document.inputStream,
                extension,
                ocr,
            )
        } else {
            markers.andAppend("frontSide", rgFrontSide != null)
            registerService.processRGDocumentFile(
                authentication.toAccountId(),
                document.inputStream,
                rgFrontSide != null,
                extension,
                ocr,
            )
        }.map<HttpResponse<*>> {
            LOG.info(markers.andAppend("response", it), "${logNamePrefix}uploadDocument")
            val clientIp = addressResolver.resolve(request) ?: ""
            if (ocr) {
                HttpResponse.ok(it.toAccountRegisterDataTO(clientIp))
            } else {
                HttpResponse.noContent()
            }
        }.getOrElse {
            markers.andAppend("errorMessage", it.message)
            handleError(it, authentication, "uploadDocument", markers)
        }
    }

    protected open fun handleError(
        it: Exception,
        authentication: Authentication,
        logName: String,
        markers: LogstashMarker? = null,
    ): HttpResponse<*> {
        return when (it) {
            is RegisterIncompleteException -> {
                withLogError(
                    exception = it,
                    httpStatus = HttpStatus.BAD_REQUEST,
                    authentication = authentication,
                    logLame = logName,
                    registerErrors = RegisterErrors.REGISTER_IS_NOT_COMPLETED,
                    markers = markers,
                )
            }

            is InvalidTokenException -> {
                val registerErrors = when (it.reason) {
                    InvalidTokenReason.NOT_FOUND -> RegisterErrors.TOKEN_NOT_FOUND
                    InvalidTokenReason.EXPIRED -> RegisterErrors.TOKEN_EXPIRED
                    InvalidTokenReason.MAX_ATTEMPTS -> RegisterErrors.TOKEN_MAX_ATTEMPTS
                    InvalidTokenReason.MISMATCH -> RegisterErrors.TOKEN_MISMATCH
                }
                withLogInfo(
                    httpStatus = HttpStatus.BAD_REQUEST,
                    authentication = authentication,
                    logLame = logName,
                    registerErrors = registerErrors,
                    markers = markers,
                )
            }

            is AccountIsBlockedForEdition -> {
                withLogInfo(
                    httpStatus = HttpStatus.BAD_REQUEST,
                    authentication = authentication,
                    logLame = logName,
                    registerErrors = RegisterErrors.REGISTER_IS_BLOCKED_FOR_EDITION,
                    markers = markers,
                )
            }

            is InvalidDocumentImageException -> {
                val registerErrors = when (it) {
                    is DocumentMustBeOpenedException -> RegisterErrors.DOCUMENT_MUST_BE_OPENED
                    is FaceNotFoundException -> RegisterErrors.FACE_NOT_FOUND
                    else -> RegisterErrors.INVALID_DOCUMENT_IMAGE
                }
                withLogInfo(
                    httpStatus = HttpStatus.BAD_REQUEST,
                    authentication = authentication,
                    logLame = logName,
                    registerErrors = registerErrors,
                    markers = markers,
                )
            }

            is InvalidMonthlyIncomeException -> {
                withLogInfo(
                    httpStatus = HttpStatus.BAD_REQUEST,
                    authentication = authentication,
                    logLame = logName,
                    registerErrors = RegisterErrors.INVALID_MONTHLY_INCOME,
                    markers = markers,
                )
            }

            is MobilePhoneAlreadyInUseException -> {
                val currentMarkers = markers ?: Markers.empty()
                currentMarkers.andAppend("mobilePhoneAlreadyInUseByAccountId", it.accountId.value)

                withLogInfo(
                    httpStatus = HttpStatus.CONFLICT,
                    authentication = authentication,
                    logLame = logName,
                    registerErrors = RegisterErrors.MOBILE_PHONE_ALREADY_EXISTS,
                    markers = currentMarkers,
                )
            }

            else -> {
                withLogError(
                    exception = it,
                    httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
                    authentication = authentication,
                    logLame = logName,
                    registerErrors = RegisterErrors.GENERIC_ERROR,
                    markers = markers,
                )
            }
        }
    }

    protected open fun withLogInfo(
        httpStatus: HttpStatus,
        authentication: Authentication,
        logLame: String,
        registerErrors: RegisterErrors,
        markers: LogstashMarker? = null,
    ): HttpResponse<*> {
        val currentMarkers = markers ?: append("accountId", authentication.name)

        LOG.info(
            currentMarkers.andAppend("httpStatus", httpStatus)
                .andAppend("registerError", registerErrors.name),
            logNamePrefix + logLame,
        )
        return buildResponse(httpStatus, registerErrors)
    }

    protected fun withLogError(
        exception: Exception,
        httpStatus: HttpStatus,
        authentication: Authentication,
        logLame: String,
        registerErrors: RegisterErrors,
        markers: LogstashMarker? = null,
    ): HttpResponse<*> {
        val currentMarkers = markers ?: append("accountId", authentication.name)
        LOG.error(
            currentMarkers.andAppend("httpStatus", httpStatus).andAppend("registerError", registerErrors),
            logNamePrefix + logLame,
            exception,
        )
        return buildResponse(httpStatus, registerErrors)
    }

    private fun buildResponse(httpStatus: HttpStatus, registerErrors: RegisterErrors): HttpResponse<*> {
        return StandardHttpResponses.customStatusResponse(
            httpStatus,
            buildResponseTO(registerErrors),
        )
    }

    private fun buildResponseTO(registerErrors: RegisterErrors): ResponseTO =
        ResponseTO(registerErrors.code, registerErrors.description)

    private fun AccountRegisterData.toTargetGroup(): String? {
        return monthlyIncome?.lowerBound?.let { lowerBound ->
            if (lowerBound > targetGroupIncomeLimit) {
                targetGroupA
            } else {
                targetGroupB
            }
        }
    }

    protected open fun AccountRegisterData.toAccountRegisterDataTO(clientIP: String): AccountRegisterDataTO {
        val agreementData = registerService.updateAgreementFiles(accountId, clientIP)
        val agreementFile =
            agreementData?.let {
                publicHttpLinkGenerator.generate(
                    it.userContractFile,
                    s3LinkDuration,
                    MediaType.APPLICATION_PDF,
                )
            }
        val declarationOfResidencyFile = agreementData?.declarationOfResidencyFile?.let {
            publicHttpLinkGenerator.generate(
                it,
                s3LinkDuration,
                MediaType.APPLICATION_PDF,
            )
        }
        val agreementTO = AgreementTO(
            url = agreementFile,
            hasAccepted = this.agreementData?.acceptedAt != null,
            contract = agreementFile,
            declarationOfResidency = declarationOfResidencyFile,
        )

        val uploadedSelfie = registerService.processLiveness(this).getOrElse { throw it }.uploadedSelfie

        val isTemporaryEmail = isTemporaryEmail(emailAddress)

        return AccountRegisterDataTO(
            accountId = accountId.value,
            mobilePhone = mobilePhone?.takeIf { mobilePhoneVerified || mobilePhoneTokenExpiration != null }
                ?.toMobilePhoneResponseTO(mobilePhoneVerified, mobilePhoneTokenExpiration),
            email = if (isTemporaryEmail) null else emailAddress.value,
            emailVerification = if (isTemporaryEmail) {
                null
            } else {
                emailAddress.takeIf { emailVerified || emailTokenExpiration != null }
                    ?.toEmailResponseTO(
                        emailVerified,
                        emailTokenExpiration,
                    )
            },
            nickname = nickname,
            created = created.format(dateTimeFormat),
            lastUpdated = lastUpdated.format(dateTimeFormat),
            document = buildDocumentDataTO(),
            address = address?.toAddressTO(),
            userContract = agreementTO,
            agreement = agreementFileUrl,
            monthlyIncome = monthlyIncome?.let {
                MonthlyIncomeRequestTO(
                    lowerBound = it.lowerBound,
                    upperBound = it.upperBound,
                )
            },
            politicallyExposed = politicallyExposed?.toPoliticallyExposedTO(),
            livenessId = livenessId?.value,
            selfie = SelfieTO(hasValidUpload = uploadedSelfie != null),
            openForUserReview = openForUserReview,
            targetGroup = toTargetGroup(),
            birthDate = birthDate?.format(registerDateFormatter),
        )
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(RegisterController::class.java)
    }
}

private fun EmailAddress.toEmailResponseTO(verified: Boolean, expiration: Long?): EmailResponseTO =
    EmailResponseTO(
        email = value,
        isVerified = verified,
        tokenExpiration = expiration,
    )

fun CompletedFileUpload.fileExtension(
    valid: List<MediaType> = listOf(
        MediaType.IMAGE_JPEG_TYPE,
        MediaType.IMAGE_PNG_TYPE,
        MediaType.APPLICATION_PDF_TYPE,
    ),
): String? {
    if (contentType.isPresent && contentType.get() in valid) {
        return MediaType.of(contentType.get().name).extension
    }
    return null
}

enum class RegisterErrors(val description: String, val code: String) {
    INVALID_PHONE("Formato do número de telefone inválido", "4020"),
    GENERIC_ERROR("Não foi possível completar a operação. Por favor, tente novamente", "4022"),
    TOKEN_NOT_FOUND("Token não encontrado", "4023"),
    TOKEN_EXPIRED("Token expirado", "4023"),
    TOKEN_MISMATCH("Token inválido", "4023"),
    TOKEN_MAX_ATTEMPTS("Limite de tentativas excedido", "4023"),
    MAX_FILE_SIZE_EXCEEDED("Imagem deve ter no máximo 10 mb", "4024"),
    INVALID_DOCUMENT_MEDIA_TYPE("Documento deve ser jpeg, png ou pdf", "4025"),
    INVALID_DOCUMENT_IMAGE("Imagem deve ser da cnh ou do RG", "4026"),
    ADDRESS_NOT_FOUND("Endereço não encontrado", "4027"),
    INVALID_POLITICAL_INFO("É necessário informar detalhes se e somente se for politicamente exposto", "4028"),
    INVALID_AGREEMENT_INFO("É necessário concordar com os termos de uso", "4029"),
    REGISTER_IS_BLOCKED_FOR_EDITION("O status do registro não permite edição pelo usuário", "4030"),
    REGISTER_IS_NOT_COMPLETED("Só é possível aceitar os termos de uso após concluir o cadastro", "4031"),
    INVALID_MONTHLY_INCOME("Valor inválido para a renda mensal", "4032"),
    MOBILE_PHONE_ALREADY_EXISTS("Número de telefone já está em uso", "4033"),
    FACE_NOT_FOUND("Rosto não detectado", "4034"),
    DOCUMENT_MUST_BE_OPENED("Documento não está aberto", "4035"),
    DOCUMENT_ALREADY_REGISTERED("Documento já está cadastrado", "4036"),
}

fun PoliticallyExposed.toPoliticallyExposedTO(): PoliticallyExposedTO = PoliticallyExposedTO(
    isExposed = selfDeclared,
)

private fun Address.toAddressTO(): AddressTO = AddressTO(
    streetType = streetType,
    streetName = streetName,
    number = number.orEmpty(),
    complement = complement,
    neighborhood = neighborhood,
    city = city,
    state = state,
    zipCode = zipCode,
)

private fun AccountRegisterData.buildDocumentDataTO(): DocumentDataResponseTO {
    return documentInfo?.let {
        DocumentDataResponseTO(
            name = it.name,
            cpf = it.cpf,
            birthDate = it.birthDate.formatOrEmpty(registerDateFormatter),
            fatherName = it.fatherName,
            motherName = it.motherName,
            isEdited = isDocumentEdited,
            birthCity = it.birthCity,
            birthState = it.birthState,
            orgEmission = it.orgEmission,
            expeditionDate = it.expeditionDate.formatOrEmpty(registerDateFormatter),
            cnhNumber = it.cnhNumber,
            rgNumber = it.rg,
        )
    } ?: DocumentDataResponseTO()
}

private fun MobilePhone.toMobilePhoneResponseTO(mobilePhoneVerified: Boolean, mobilePhoneTokenExpiration: Long?) =
    MobilePhoneResponseTO(
        number = msisdn,
        isVerified = mobilePhoneVerified,
        tokenExpiration = mobilePhoneTokenExpiration?.let {
            it - getZonedDateTime().toEpochSecond()
        },
    )

@JsonInclude(JsonInclude.Include.ALWAYS)
data class AccountRegisterDataTO(
    val created: String,
    val lastUpdated: String,
    val email: String?,
    val emailVerification: EmailResponseTO? = null,
    val nickname: String,
    val accountId: String,
    val mobilePhone: MobilePhoneResponseTO? = null,
    val document: DocumentDataResponseTO,
    val address: AddressTO?,
    val selfie: SelfieTO?,
    val monthlyIncome: MonthlyIncomeRequestTO?,
    val politicallyExposed: PoliticallyExposedTO?,
    val agreement: String?,
    val userContract: AgreementTO?,
    val openForUserReview: Boolean,
    val targetGroup: String?,
    val livenessId: String?,
    val birthDate: String?,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class EmailResponseTO(
    val email: String? = null,
    val isVerified: Boolean,
    val tokenExpiration: Long? = null,
)

data class EmailRequestTO(
    val email: String,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class MobilePhoneResponseTO(
    val number: String? = null,
    val isVerified: Boolean,
    val tokenExpiration: Long? = null,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class SelfieTO(
    val hasValidUpload: Boolean,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class PoliticallyExposedTO(
    val isExposed: Boolean,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class AgreementTO(
    @Deprecated("remover após a remoção do front")
    val url: String?,
    val hasAccepted: Boolean,
    val contract: String?,
    val declarationOfResidency: String?,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class DocumentDataResponseTO(
    val cpf: String = "",
    val cnhNumber: String? = "",
    val rgNumber: String? = "",

    val name: String = "",
    val birthDate: String = "",
    val fatherName: String = "",
    val motherName: String = "",
    val birthCity: String = "",
    val birthState: String = "",
    val orgEmission: String = "",
    val expeditionDate: String = "",

    val isEdited: Boolean = false,
)

@Introspected
data class DocumentDataRequestTO(
    val name: String,
    @field:Pattern(regexp = "^\\d{11}$", message = "document must be 11 digits for CPF") val cpf: String,
    @field:Pattern(regexp = "^\\d{2}/\\d{2}/\\d{4}$", message = "birthDate should be dd/mm/yyyy") val birthDate: String,
    val fatherName: String,
    val motherName: String,
    val birthCity: String,
    @field:Pattern(
        regexp = "^(ac|AC|al|AL|am|AM|ap|AP|ba|BA|ce|CE|df|DF|es|ES|go|GO|ma|MA|mg|MG|ms|MS|mt|MT|pa|PA|pb|PB|pe|PE|pi|PI|pr|PR|rj|RJ|rn|RN|ro|RO|rr|RR|rs|RS|sc|SC|se|SE|sp|SP|to|TO)\$",
        message = "Código do estado é invalido",
    ) val birthState: String,
    val orgEmission: String,
    val cnhNumber: String?,
    val rgNumber: String?,
    @field:Pattern(
        regexp = "^\\d{2}/\\d{2}/\\d{4}$",
        message = "expeditionDate should be dd/mm/yyyy",
    ) val expeditionDate: String,
)

internal fun AddressRequestTO.toAddress() = Address(
    streetType = streetType,
    streetName = streetName,
    number = number,
    complement = complement,
    neighborhood = neighborhood,
    city = city,
    state = state,
    zipCode = zipCode,
)

@Introspected
data class AddressRequestTO(
    val streetType: String,
    val streetName: String,
    val number: String,
    val complement: String?,
    val neighborhood: String,
    val city: String,
    val state: String,
    @field:Pattern(regexp = "^\\d{8}$", message = "Zip code must be 8 digits long") val zipCode: String,
)

private fun PoliticallyExposedRequestTO.isValid(): Boolean {
    return (isExposed.not() and info.isNullOrEmpty()) or (isExposed and info.isNullOrEmpty().not())
}

data class MonthlyIncomeRequestTO(
    val lowerBound: Long,
    val upperBound: Long?,
)

data class PoliticallyExposedRequestTO(
    val isExposed: Boolean,
    val info: String? = null,
)

data class AgreementRequestTO(
    val hasAccepted: Boolean,
)

@ConfigurationProperties("accountRegister")
class AccountRegisterConfiguration {
    lateinit var maxDocumentSize: Number
}

enum class RegisterFlow {
    BANKING_PARTNERSHIP, BILLING_CAROUSEL, ACCEPTED_TRIAL, SUBSCRIBED, ACCOUNT_CREATION_STARTED
}