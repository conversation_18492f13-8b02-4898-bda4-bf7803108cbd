package ai.friday.billpayment.adapters.api

import io.micronaut.context.annotation.Property
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.cookie.CookieFactory
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule

@Controller("/logout")
@Secured(SecurityRule.IS_ANONYMOUS)
class LogoutController(
    @Property(name = "micronaut.security.token.cookie.cookie-domain") private val cookieDomain: String,
) {

    @Delete
    @Version("2")
    fun logoutV2(): HttpResponse<*>? {
        return HttpResponse.ok<Any>().cookie(
            CookieFactory.INSTANCE.create("JWT", "")
                .httpOnly(true)
                .domain(cookieDomain)
                .secure(true)
                .maxAge(0),
        )
    }
}