package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.arbi.ArbiAdapter
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.integrations.DocumentType
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import java.time.LocalDate

@Secured(Role.Code.BACKOFFICE)
@FridayMePoupe
@Controller("/backoffice/dda")
class BackofficeDDAController(private val arbiAdapter: ArbiAdapter) {

    @Put("/{document}")
    fun add(
        @PathVariable document: String,
    ): HttpResponse<*> {
        return HttpResponse.ok(
            arbiAdapter.add(
                documents = listOf(Document(document)),
                documentType = if (document.length == 11) DocumentType.CPF else DocumentType.CNPJ,
            ),
        )
    }

    @Get("/{document}/{localDate}")
    fun getDDABills(
        @PathVariable document: String,
        @PathVariable localDate: String,
    ): HttpResponse<*> {
        return HttpResponse.ok(arbiAdapter.getBills(dueDate = LocalDate.parse(localDate), document = document))
    }

    @Get("/{document}/{localDate}/withoutMicronautParser")
    fun getDDABillsWithoutMicronautParser(
        @PathVariable document: String,
        @PathVariable localDate: String,
    ): HttpResponse<*> {
        return HttpResponse.ok(arbiAdapter.getBillsWithoutMicronautParser(dueDate = LocalDate.parse(localDate), document = document))
    }
}