package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role.Code.ADMIN
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.wallet.AddOnboardingAssistantResult
import ai.friday.billpayment.app.wallet.BackOfficeWalletService
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.CreateWalletErrors
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.RemoveOnboardingAssistantResult
import ai.friday.billpayment.app.wallet.SecondaryWalletService
import ai.friday.billpayment.app.wallet.UpdateMemberPermissionErrors
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.WalletStatus
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import java.time.LocalDate
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(BACKOFFICE, ADMIN)
@Controller("/backoffice")
@FridayMePoupe
class BackofficeWalletController(
    private val accountRepository: AccountRepository,
    private val updateBillService: UpdateBillService,
    private val secondaryWalletService: SecondaryWalletService,
    private val backOfficeWalletService: BackOfficeWalletService,
    private val walletService: WalletService,
) {
    @Post("/account/{accountId}/secondaryWallet")
    fun createSecondaryWallet(
        @PathVariable accountId: String,
        @Body request: CreateSecondaryWalletRequestTO,
    ): HttpResponse<*> {
        val markers =
            Markers
                .append("accountId", accountId)
                .andAppend("request", request)
        val account = accountRepository.findById(AccountId(accountId))
        return secondaryWalletService
            .create(
                account = account,
                name = request.walletName,
                existingBankAccountNumber = request.fullAccountNumber?.let { AccountNumber(fullAccountNumber = it) },
            ).map { wallet ->
                markers.andAppend("walletId", wallet.id.value)
                logger.info(markers, "BackofficeWalletController#createSecondaryWallet")
                HttpResponse.ok(
                    CreateSecondaryAccountResponseTO(
                        accountPaymentMethodId = wallet.paymentMethodId.value,
                        walletId = wallet.id.value,
                        walletStatus = wallet.status,
                        pixKey = "", // FIXME - wallet.pixKey,
                    ),
                )
            }.getOrElse {
                handleError(
                    error = it,
                    markers = markers,
                )
            }
    }

    @Delete("/wallet/{walletId}")
    fun closeSecondaryWallet(
        @PathVariable walletId: String,
    ): HttpResponse<*> {
        val logName = "BackofficeWalletController#closeSecondaryWallet"
        val markers = Markers.append("walletId", walletId)
        return secondaryWalletService.close(WalletId(walletId)).fold(
            ifLeft = {
                logger.warn(markers, logName)
                HttpResponse.badRequest(it.toString())
            },
            ifRight = {
                logger.info(markers, logName)
                HttpResponse.noContent<Unit>()
            },
        )
    }

    @Post("/account/{accountId}/externalAccount")
    fun createExternalAccount(
        @PathVariable accountId: String,
    ): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId)
        logger.info(markers, "BackofficeWalletController#createExternalAccount")
        return secondaryWalletService.createExternalAccount(AccountId(accountId)).fold(
            ifLeft = {
                handleError(
                    error = it,
                    markers = markers,
                )
            },
            ifRight = {
                HttpResponse.created(it)
            },
        )
    }

    @Post("/wallet/{walletId}/{billId}/changeDueDate/{dueDate}")
    fun changeDueDate(
        @PathVariable billId: String,
        @PathVariable dueDate: String,
        @PathVariable walletId: String,
    ): HttpResponse<*> {
        val markers = Markers.append("billId", billId).andAppend("dueDate", dueDate).andAppend("walletId", walletId)

        updateBillService.updateDueDate(BillId(billId), WalletId(walletId), LocalDate.parse(dueDate)).getOrElse {
            logger.warn(markers, "BackofficeWalletController#changeDueDate")
            return StandardHttpResponses.badRequest(4001, "BillNotFound")
        }

        logger.info(markers, "BackofficeWalletController#changeDueDate")
        return HttpResponse.noContent<Unit>()
    }

    @Put("/wallet/{walletId}/{memberAccountId}/permissions")
    fun updateMemberPermissions(
        @PathVariable walletId: String,
        @PathVariable memberAccountId: String,
        @Body request: UpdateMemberPermissionsTO,
    ): HttpResponse<*> {
        val markers =
            Markers
                .append("walletId", walletId)
                .andAppend("memberAccountId", memberAccountId)
                .andAppend("permissions", request)

        val viewBillsPermission =
            readBillPermission(request.viewBills).getOrElse {
                logger.error(markers, "BackofficeWalletController#updateMemberPermissions")
                return HttpResponse.badRequest("Invalid viewBills")
            }

        val scheduleBillsPermission =
            readBillPermission(request.scheduleBills).getOrElse {
                logger.error(markers, "BackofficeWalletController#updateMemberPermissions")
                return HttpResponse.badRequest("Invalid scheduleBills")
            }

        val member = backOfficeWalletService.getMember(WalletId(walletId), AccountId(memberAccountId)).getOrElse { return handleError(it, markers) }

        return backOfficeWalletService
            .updateMemberPermissions(
                WalletId(walletId),
                member,
                MemberPermissions(
                    viewBills = viewBillsPermission ?: member.permissions.viewBills,
                    scheduleBills = scheduleBillsPermission ?: member.permissions.scheduleBills,
                    founderContactsEnabled = request.founderContactsEnabled ?: member.permissions.founderContactsEnabled,
                    manageMembers = request.manageMembers ?: member.permissions.manageMembers,
                    viewBalance = request.viewBalance ?: member.permissions.viewBalance,
                    notification = request.notification ?: member.permissions.notification,
                ),
            ).map {
                logger.info(markers, "BackofficeWalletController#updateMemberPermissions")
                HttpResponse.ok<Unit>()
            }.getOrElse {
                return handleError(it, markers)
            }
    }

    @Put("/account/{accountId}/updateMemberType")
    fun updateMemberType(
        @PathVariable accountId: String,
        @Body request: UpdateMemberTypeAndPermissionTO,
    ): HttpResponse<*> {
        val markers =
            Markers
                .append("accountId", accountId)
                .andAppend("request", request)

        try {
            logger.info(markers, "BackofficeWalletController#updateMemberType")

            if (request.allWallets != true && request.walletId == null) {
                logger.error(markers, "BackofficeWalletController#updateMemberType")
                return StandardHttpResponses.badRequest(ResponseTO("4001", "You should inform walletId OR allWallets=true"))
            }

            val wallets = if (request.allWallets == true) {
                walletService.findWallets(AccountId(accountId), MemberStatus.ACTIVE).filter {
                    it.founder.accountId != AccountId(accountId)
                }
            } else {
                // TODO: tentativa de uso passando walletId não funcionou.
                listOf(walletService.findWallet(WalletId(request.walletId!!)))
            }

            val result = wallets.map { wallet ->
                val member = backOfficeWalletService.getMember(wallet.id, AccountId(accountId)).getOrElse {
                    return@map UpdateWalletTypeAndPermissionResult(
                        walletId = wallet.id,
                        success = false,
                        error = it.toString(),
                    )
                }

                val permissions = MemberPermissions.of(request.memberType)

                backOfficeWalletService.updateMemberPermissions(
                    wallet.id,
                    member.copy(
                        type = request.memberType,
                    ),
                    permissions.copy(
                        viewBills = request.viewBills ?: permissions.viewBills,
                        viewBalance = request.viewBalance ?: permissions.viewBalance,
                    ),
                ).map {
                    UpdateWalletTypeAndPermissionResult(
                        walletId = wallet.id,
                        success = true,
                        error = null,
                    )
                }.getOrElse {
                    UpdateWalletTypeAndPermissionResult(
                        walletId = wallet.id,
                        success = false,
                        error = it.toString(),
                    )
                }
            }

            return HttpResponse.ok(result)
        } catch (e: Exception) {
            logger.error(markers, "BackofficeWalletController#updateMemberType", e)
            return HttpResponse.serverError(e.message)
        }
    }

    @Delete("/wallet/{walletId}/member/{accountId}")
    fun removeMemberFromWallet(
        @PathVariable walletId: String,
        @PathVariable accountId: String,
    ): HttpResponse<*> {
        val logName = "BackofficeWalletController#removeMemberFromWallet"
        val markers =
            Markers
                .append("walletId", walletId)
                .andAppend("accountId", accountId)
        try {
            walletService.removeMember(
                walletId = WalletId(walletId),
                source = ActionSource.System,
                targetAccountId = AccountId(accountId),
            ).getOrElse {
                logger.warn(markers.andAppend("error", it), logName)
                return HttpResponse.badRequest(it.toString())
            }
            logger.info(markers, logName)
            return HttpResponse.ok<Unit>()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return HttpResponse.serverError(e.message)
        }
    }

    @Put("/wallet/{walletId}/")
    fun updateWallet(
        @PathVariable walletId: String,
        @Body request: UpdateWalletRequestTO,
    ): HttpResponse<*> {
        val markers = Markers.append("walletId", walletId).andAppend("request", request)
        try {
            val updatedWallet = backOfficeWalletService.updateWallet(WalletId(walletId), request.walletName)
            logger.info(markers.andAppend("updatedWallet", updatedWallet), "BackofficeWalletController#updateWallet")
            return HttpResponse.ok(UpdateWalletResponseTO(updatedWallet.id.value, updatedWallet.name))
        } catch (e: Exception) {
            logger.error(markers, "BackofficeWalletController#updateWallet", e)
            return HttpResponse.serverError(e.message)
        }
    }

    @Secured(ADMIN)
    @Post("/wallet/{walletId}/onboarding-assistant")
    fun addOnboardingAssistant(
        @PathVariable walletId: String,
        @Body request: AddOnboardingAssistantTO,
    ): HttpResponse<*> {
        val markers = Markers.append("walletId", walletId)
            .andAppend("assistantAccountId", request.assistantAccountId)

        if (walletId.isEmpty() || request.assistantAccountId.isEmpty()) {
            return HttpResponse.badRequest<Unit>()
        }

        return backOfficeWalletService.addOnboardingAssistant(walletId = WalletId(walletId), assistantAccountId = AccountId(request.assistantAccountId))
            .fold(
                onSuccess = {
                    markers.andAppend("result", it)

                    when (it) {
                        AddOnboardingAssistantResult.Added -> HttpResponse.status(HttpStatus.ACCEPTED)
                        AddOnboardingAssistantResult.AssistantNotFound, AddOnboardingAssistantResult.WalletNotFound -> HttpResponse.notFound<Unit>()
                        AddOnboardingAssistantResult.CannotBeAssistant, AddOnboardingAssistantResult.LikelyHasOtherAssistant -> HttpResponse.status(HttpStatus.CONFLICT)
                    }.also {
                        logger.info(markers, "addOnboardingAssistant")
                    }
                },
                onFailure = {
                    logger.error(markers, "addOnboardingAssistant", it)
                    HttpResponse.serverError<Unit>()
                },
            )
    }

    @Secured(ADMIN)
    @Delete("/wallet/{walletId}/onboarding-assistant")
    fun removeOnboardingAssistant(@PathVariable walletId: String, @Body request: RemoveOnboardingAssistantTO): HttpResponse<*> {
        val markers = Markers.append("walletId", walletId)
            .andAppend("assistantAccountId", request.assistantAccountId)

        if (walletId.isEmpty() || request.assistantAccountId.isEmpty()) {
            return HttpResponse.badRequest<Unit>()
        }

        return backOfficeWalletService.removeOnboardingAssistant(walletId = WalletId(walletId), assistantAccountId = AccountId(request.assistantAccountId)).fold(
            onSuccess = {
                markers.andAppend("result", it)

                when (it) {
                    RemoveOnboardingAssistantResult.AssistantNotFound, RemoveOnboardingAssistantResult.WalletNotFound -> HttpResponse.notFound<Unit>()
                    RemoveOnboardingAssistantResult.Removed -> HttpResponse.status(HttpStatus.ACCEPTED)
                }.also {
                    logger.info(markers, "removeOnboardingAssistant")
                }
            },
            onFailure = {
                logger.error(markers, "removeOnboardingAssistant", it)
                HttpResponse.serverError<Unit>()
            },
        )
    }

    private fun readBillPermission(name: String?): Either<Unit, BillPermission?> {
        val permission: BillPermission? =
            if (name == null) {
                null
            } else {
                BillPermission.of(name) ?: return Unit.left()
            }

        return permission.right()
    }

    private fun handleError(
        error: UpdateMemberPermissionErrors,
        markers: LogstashMarker,
    ): HttpResponse<*> =
        when (error) {
            UpdateMemberPermissionErrors.WalletNotFound -> {
                logger.warn(markers, "BackofficeWalletController#updateMemberPermissions")
                HttpResponse.notFound("Wallet not found")
            }

            UpdateMemberPermissionErrors.MemberNotFound -> {
                logger.warn(markers, "BackofficeWalletController#updateMemberPermissions")
                HttpResponse.notFound("Member not found")
            }

            UpdateMemberPermissionErrors.ErrorUpdatingMember -> {
                logger.error("BackofficeWalletController#updateMemberPermissions")
                HttpResponse.serverError("Error updating member")
            }
        }

    private fun handleError(
        error: CreateWalletErrors,
        markers: LogstashMarker,
    ): HttpResponse<*> {
        markers.andAppend("createWalletError", error)
        return when (error) {
            CreateWalletErrors.MaxWalletsAllowed -> {
                markers.andAppend("errorMessage", "MaxWalletsAllowed")
                logger.warn(markers, "BackofficeWalletController#createSecondaryWallet")
                StandardHttpResponses.badRequest(4001, "MaxWalletsAllowed")
            }

            CreateWalletErrors.RegisterNaturalPersonError -> {
                markers.andAppend("errorMessage", "RegisterNaturalPersonError")
                logger.error(markers, "BackofficeWalletController#createSecondaryWallet")
                StandardHttpResponses.serverError(ResponseTO("5001", "RegisterNaturalPersonError"))
            }

            CreateWalletErrors.WalletNameAlreadyExists -> {
                markers.andAppend("errorMessage", "WalletNameAlreadyExists")
                logger.warn(markers, "BackofficeWalletController#createSecondaryWallet")
                StandardHttpResponses.badRequest(4001, "WalletNameAlreadyExists")
            }

            CreateWalletErrors.BasicAccountNotAllowed -> {
                markers.andAppend("errorMessage", "BasicAccountNotAllowed")
                logger.warn(markers, "BackofficeWalletController#createSecondaryWallet")
                StandardHttpResponses.badRequest(4001, "BasicAccountNotAllowed")
            }

            CreateWalletErrors.RegisterEvpPixKey -> {
                markers.andAppend("errorMessage", "RegisterEvpPixKey")
                logger.error(markers, "BackofficeWalletController#createSecondaryWallet")
                StandardHttpResponses.serverError(ResponseTO("4001", "RegisterEvpPixKey"))
            }
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(BackofficeWalletController::class.java)
    }
}

data class CreateSecondaryWalletRequestTO(
    val walletName: String,
    val fullAccountNumber: String?,
)

data class CreateSecondaryAccountResponseTO(
    val accountPaymentMethodId: String,
    val walletStatus: WalletStatus,
    val walletId: String,
    val pixKey: String,
)

data class UpdateMemberPermissionsTO(
    val viewBills: String?,
    val scheduleBills: String?,
    val founderContactsEnabled: Boolean?,
    val manageMembers: Boolean?,
    val viewBalance: Boolean?,
    val notification: Boolean?,
)

data class UpdateWalletRequestTO(
    val walletName: String,
)

data class UpdateMemberTypeAndPermissionTO(
    val memberType: MemberType,
    val walletId: String?,
    val allWallets: Boolean?,
    val viewBalance: Boolean?,
    val viewBills: BillPermission?,
)

data class UpdateWalletTypeAndPermissionResult(
    val walletId: WalletId,
    val success: Boolean,
    val error: String?,
)

data class UpdateDueDateResponseTO(
    val bill: Bill,
)

data class UpdateWalletResponseTO(
    val walletId: String,
    val name: String,
)

data class AddOnboardingAssistantTO(val assistantAccountId: String)
data class RemoveOnboardingAssistantTO(val assistantAccountId: String)