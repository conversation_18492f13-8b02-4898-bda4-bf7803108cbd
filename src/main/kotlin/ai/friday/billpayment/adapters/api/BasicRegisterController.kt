package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountIsBlockedForEdition
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.BasicAccountRegisterData
import ai.friday.billpayment.app.account.BasicRegisterService
import ai.friday.billpayment.app.account.DeviceAdIds
import ai.friday.billpayment.app.account.DocumentAlreadyRegistered
import ai.friday.billpayment.app.account.DocumentDetails
import ai.friday.billpayment.app.account.DocumentNumber
import ai.friday.billpayment.app.account.InvalidTokenException
import ai.friday.billpayment.app.account.InvalidTokenReason
import ai.friday.billpayment.app.account.RegisterIncompleteException
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.account.TokenKey
import ai.friday.billpayment.app.account.TokenStillValidException
import ai.friday.billpayment.app.account.TrialDuration
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.msisdnauth.isTemporaryEmail
import ai.friday.billpayment.app.utils.PublicHttpLinkGenerator
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonInclude
import io.micronaut.context.annotation.Property
import io.micronaut.core.annotation.Introspected
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.http.server.util.HttpClientAddressResolver
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.validation.Validated
import jakarta.validation.Valid
import jakarta.validation.constraints.Pattern
import java.time.Duration
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

private val registerDateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy")

@Secured(Role.Code.GUEST_OTP)
@Controller("/basicRegister")
@Version("2")
@Validated
@FridayMePoupe
class BasicRegisterController(
    private val registerService: BasicRegisterService,
    private val accountService: AccountService,
    private val systemActivityService: SystemActivityService,
    private val addressResolver: HttpClientAddressResolver,
    @Property(name = "urls.termsOfUse") private val agreementFileUrl: String,
    @Property(name = "accountRegister.user_files.contractLinkDuration") private val s3LinkDuration: Duration,
    private val publicHttpLinkGenerator: PublicHttpLinkGenerator,
) {

    @Get
    fun retrieveAccountRegisterData(authentication: Authentication, request: HttpRequest<*>): HttpResponse<*> {
        val markers = append("accountId", authentication.toAccountId().value)
        return registerService.findByAccountId(authentication.toAccountId()).map<HttpResponse<*>> {
            LOG.info(markers.andAppend("response", it), "retrieveBasicAccountRegisterData")
            val clientIp = addressResolver.resolve(request) ?: ""
            HttpResponse.ok(it.toBasicAccountRegisterDataTO(clientIp))
        }.getOrElse {
            withLogError(
                it,
                HttpStatus.INTERNAL_SERVER_ERROR,
                authentication,
                "retrieveBasicAccountRegisterData",
                RegisterErrors.GENERIC_ERROR,
            )
        }
    }

    @Put("/deviceInfo")
    fun setDeviceInfo(
        authentication: Authentication,
        @Body deviceInfo: DeviceInfoTO,
        request: HttpRequest<*>,
    ): HttpResponse<*> {
        val markers = append("accountId", authentication.name).andAppend("adjustId", deviceInfo.adjustId)
            .andAppend("adGoogleId", deviceInfo.googleId).andAppend("adAppleId", deviceInfo.appleId)
            .andAppend("isDeviceControlGroup", deviceInfo.isDeviceControlGroup)

        val accountId = authentication.toAccountId()

        deviceInfo.isDeviceControlGroup?.let {
            try {
                systemActivityService.setLastDeviceTestGroup(accountId, it)
            } catch (err: Throwable) {
                LOG.error("setDeviceInfo", err)
            }
        }

        return registerService.setDeviceInfo(
            DeviceAdIds(deviceInfo.adjustId, deviceInfo.googleId, deviceInfo.appleId),
            accountId,
        )
            .map<HttpResponse<*>> {
                LOG.info(markers, "setDeviceInfo")
                HttpResponse.noContent<Unit>()
            }.getOrElse {
                handleError(it, authentication, "setDeviceInfo", markers = markers)
            }
    }

    @Put("/document")
    fun addDocumentNumber(
        authentication: Authentication,
        @Body body: DocumentTO,
        request: HttpRequest<*>,
    ): HttpResponse<*> {
        val markers = append("accountId", authentication.name)
            .andAppend("document", body.document)
        return registerService.addDocumentNumber(authentication.toAccountId(), Document(body.document))
            .map<HttpResponse<*>> {
                LOG.info(markers.andAppend("response", it), "addDocumentNumber")
                val clientIp = addressResolver.resolve(request) ?: ""
                HttpResponse.ok(it.toBasicAccountRegisterDataTO(clientIp))
            }.getOrElse {
                handleError(it, authentication, "addDocumentNumber", markers = markers)
            }
    }

    @Put("/personalInfo")
    fun addPersonalInfo(
        authentication: Authentication,
        @Body body: PersonalInfoTO,
        request: HttpRequest<*>,
    ): HttpResponse<*> {
        val markers = append("accountId", authentication.name)
        return registerService.addPersonalInfo(
            authentication.toAccountId(),
            body.name,
            LocalDate.parse(body.birthDate, registerDateFormatter),
        )
            .map<HttpResponse<*>> {
                LOG.info(markers.andAppend("response", it), "addPersonalInfo")
                val clientIp = addressResolver.resolve(request) ?: ""
                HttpResponse.ok(it.toBasicAccountRegisterDataTO(clientIp))
            }.getOrElse {
                handleError(it, authentication, "addPersonalInfo", markers = markers)
            }
    }

    @Post("/emailAddress")
    fun registerTokenForEmailAddress(
        authentication: Authentication,
        @Body body: EmailRequestTO,
        request: HttpRequest<*>,
    ): HttpResponse<*> {
        val markers = append("accountId", authentication.toAccountId().value)
            .andAppend("emailAddress", body.email)

        val emailAddress = EmailAddress(body.email)

        val clientIp = addressResolver.resolve(request) ?: ""

        return registerService.issueToken(
            emailAddress,
            authentication.toAccountId(),
            clientIp,
        )
            .map<HttpResponse<*>> {
                LOG.info(markers, "registerTokenForEmailAddress")
                HttpResponse.created(
                    TokenIssuedTO(
                        duration = it.duration.seconds.toString(),
                        cooldown = it.cooldown.seconds.toString(),
                    ),
                )
            }.getOrElse {
                if (it is TokenStillValidException) {
                    HttpResponse.status<TokenIssuedTO>(HttpStatus.CONFLICT)
                        .body(TokenIssuedTO(duration = it.duration.seconds.toString(), cooldown = it.cooldown.seconds.toString()))
                } else {
                    handleError(it, authentication, "registerTokenForEmailAddress", markers)
                }
            }
    }

    @Put("/emailAddress/token")
    fun validateTokenForEmailAddress(
        authentication: Authentication,
        @Body body: TokenValidationTO,
        request: HttpRequest<*>,
    ): HttpResponse<*> {
        val markers = append("accountId", authentication.name)
        return registerService.updateMsisdnEmail(TokenKey(authentication.toAccountId(), body.token))
            .map<HttpResponse<*>> {
                LOG.info(markers.andAppend("response", it), "validateTokenForEmailAddress")
                val clientIp = addressResolver.resolve(request) ?: ""
                HttpResponse.ok(it.toBasicAccountRegisterDataTO(clientIp))
            }.getOrElse {
                handleError(it, authentication, "validateTokenForEmailAddress", markers = markers)
            }
    }

    @Put("/validateIdentity")
    fun postValidateIdentity(
        authentication: Authentication,
    ): HttpResponse<*> {
        val markers = append("accountId", authentication.toAccountId().value)

        val result = registerService.validateIdentity(authentication.toAccountId()).getOrElse {
            LOG.error(markers.andAppend("error", it), "postValidateIdentity")
            return HttpResponse.status<HttpResponse<*>>(
                HttpStatus.INTERNAL_SERVER_ERROR,
            )
        }

        return HttpResponse.ok(VerifyIdentityResponseTO(registrationType = result.registrationType.name))
    }

    @Put("/agreement")
    fun updateAgreement(
        authentication: Authentication,
        @Body agreementRequestTO: AgreementRequestTO,
        request: HttpRequest<*>,
    ): HttpResponse<*> {
        if (agreementRequestTO.hasAccepted.not()) {
            return withLogInfo(
                HttpStatus.BAD_REQUEST,
                authentication,
                "updateAgreement",
                RegisterErrors.INVALID_AGREEMENT_INFO,
            )
        }
        val markers = append("accountId", authentication.toAccountId().value)
        val clientIp = addressResolver.resolve(request) ?: ""
        return registerService.processAgreement(authentication.toAccountId(), clientIp).map<HttpResponse<*>> {
            val accountRegister = it.toBasicAccountRegisterDataTO(clientIp)
            LOG.info(markers.andAppend("response", it), "updateAgreement")
            registerService.cleanUpTestAccount(it.accountId, it.emailAddress)
            HttpResponse.ok(accountRegister)
        }.getOrElse {
            handleError(it, authentication, "updateAgreement")
        }
    }

    @Put("/flow/{name}")
    fun registerFlow(
        authentication: Authentication,
        @PathVariable name: String,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val markers = append("accountId", accountId.value).andAppend("registerFlow", name)
        try {
            when (RegisterFlow.valueOf(name)) {
                RegisterFlow.BANKING_PARTNERSHIP -> {
                    systemActivityService.setAcceptedBankingPartnership(accountId)
                }

                RegisterFlow.BILLING_CAROUSEL -> {
                    accountService.addGroups(
                        accountId,
                        listOf(AccountGroup.SUBSCRIPTION_STANDARD_PLAN),
                    )
                    systemActivityService.setAcceptedBillingCarousel(accountId)
                }

                RegisterFlow.ACCEPTED_TRIAL -> {
                    accountService.addGroups(
                        accountId,
                        listOf(AccountGroup.SUBSCRIPTION_STANDARD_PLAN),
                    )
                    systemActivityService.setAcceptedTrial(
                        accountId,
                        TrialDuration.days,
                    )
                }

                RegisterFlow.SUBSCRIBED -> {
                    systemActivityService.setSubscribed(accountId)
                }

                RegisterFlow.ACCOUNT_CREATION_STARTED ->
                    systemActivityService.setCreateAccountStarted(accountId)
            }

            return HttpResponse.noContent<Unit>().also {
                LOG.info(
                    markers.andAppend("httpStatus", it.status),
                    "registerFlow",
                )
            }
        } catch (e: IllegalArgumentException) {
            return withLogInfo(
                HttpStatus.BAD_REQUEST,
                authentication,
                "registerFlow",
                RegisterErrors.GENERIC_ERROR,
                markers,
            )
        } catch (e: Exception) {
            return withLogError(
                e,
                HttpStatus.INTERNAL_SERVER_ERROR,
                authentication,
                "registerFlow",
                RegisterErrors.GENERIC_ERROR,
                markers,
            )
        }
    }

    // NOTE: já existe no RegisterController, deve ser removido assim que o front for atualizado
    @Put("/document/details")
    fun upsertDocumentDetails(
        authentication: Authentication,
        @Body @Valid
        documentDetails: DocumentDetailsTO,
        request: HttpRequest<*>,
    ): HttpResponse<*> {
        val markers = append("accountId", authentication.toAccountId().value)
            .andAppend("request", documentDetails)

        return registerService.processDocumentDetails(
            authentication.toAccountId(),
            DocumentDetails(
                fatherName = documentDetails.fatherName,
                motherName = documentDetails.motherName,
                birthCity = documentDetails.birthCity,
                birthState = documentDetails.birthState.uppercase(),
                orgEmission = documentDetails.orgEmission,
                documentNumber = documentDetails.documentNumber,
                expeditionDate = LocalDate.parse(documentDetails.expeditionDate, registerDateFormatter),
            ),
        )
            .map<HttpResponse<*>> {
                LOG.info(markers.andAppend("response", it), "upsertDocumentDetails")

                val clientIp = addressResolver.resolve(request) ?: ""
                HttpResponse.ok(it.toBasicAccountRegisterDataTO(clientIp))
            }.getOrElse {
                handleError(it, authentication, "upsertDocumentDetails", markers)
            }
    }

    @Put("/politicallyExposed")
    fun updatePoliticallyExposed(
        authentication: Authentication,
        @Body politicallyExposedRequestTO: PoliticallyExposedRequestTO,
        request: HttpRequest<*>,
    ): HttpResponse<*> {
        val markers = append("accountId", authentication.toAccountId().value)
        val clientIp = addressResolver.resolve(request) ?: ""

        return registerService.processPoliticallyExposed(
            accountId = authentication.toAccountId(),
            selfDeclared = politicallyExposedRequestTO.isExposed,
            clientIp,
        )
            .map<HttpResponse<*>> {
                LOG.info(markers.andAppend("response", it), "updatePoliticallyExposed")
                HttpResponse.ok(it.toBasicAccountRegisterDataTO(clientIp))
            }.getOrElse {
                handleError(it, authentication, "updatePoliticallyExposed")
            }
    }

    private fun handleError(
        it: Exception,
        authentication: Authentication,
        logName: String,
        markers: LogstashMarker? = null,
    ): HttpResponse<*> {
        return when (it) {
            is RegisterIncompleteException -> {
                withLogError(
                    exception = it,
                    httpStatus = HttpStatus.BAD_REQUEST,
                    authentication = authentication,
                    logLame = logName,
                    registerErrors = RegisterErrors.REGISTER_IS_NOT_COMPLETED,
                    markers = markers,
                )
            }

            is InvalidTokenException -> {
                val registerErrors = when (it.reason) {
                    InvalidTokenReason.NOT_FOUND -> RegisterErrors.TOKEN_NOT_FOUND
                    InvalidTokenReason.EXPIRED -> RegisterErrors.TOKEN_EXPIRED
                    InvalidTokenReason.MAX_ATTEMPTS -> RegisterErrors.TOKEN_MAX_ATTEMPTS
                    InvalidTokenReason.MISMATCH -> RegisterErrors.TOKEN_MISMATCH
                }
                withLogInfo(
                    httpStatus = HttpStatus.BAD_REQUEST,
                    authentication = authentication,
                    logLame = logName,
                    registerErrors = registerErrors,
                    markers = markers,
                )
            }

            is AccountIsBlockedForEdition -> {
                withLogInfo(
                    httpStatus = HttpStatus.BAD_REQUEST,
                    authentication = authentication,
                    logLame = logName,
                    registerErrors = RegisterErrors.REGISTER_IS_BLOCKED_FOR_EDITION,
                    markers = markers,
                )
            }

            is DocumentAlreadyRegistered -> {
                withLogInfo(
                    httpStatus = HttpStatus.CONFLICT,
                    authentication = authentication,
                    logLame = logName,
                    registerErrors = RegisterErrors.DOCUMENT_ALREADY_REGISTERED,
                    markers = markers,
                )
            }

            else -> {
                withLogError(
                    exception = it,
                    httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
                    authentication = authentication,
                    logLame = logName,
                    registerErrors = RegisterErrors.GENERIC_ERROR,
                    markers = markers,
                )
            }
        }
    }

    private fun withLogInfo(
        httpStatus: HttpStatus,
        authentication: Authentication,
        logLame: String,
        registerErrors: RegisterErrors,
        markers: LogstashMarker? = null,
    ): HttpResponse<*> {
        val currentMarkers = markers ?: append("accountId", authentication.name)

        LOG.info(
            currentMarkers.andAppend("httpStatus", httpStatus)
                .andAppend("registerError", registerErrors.name),
            logLame,
        )
        return buildResponse(httpStatus, registerErrors)
    }

    private fun withLogError(
        exception: Exception,
        httpStatus: HttpStatus,
        authentication: Authentication,
        logLame: String,
        registerErrors: RegisterErrors,
        markers: LogstashMarker? = null,
    ): HttpResponse<*> {
        val currentMarkers = markers ?: append("accountId", authentication.name)
        LOG.error(
            currentMarkers.andAppend("httpStatus", httpStatus).andAppend("registerError", registerErrors),
            logLame,
            exception,
        )
        return buildResponse(httpStatus, registerErrors)
    }

    private fun buildResponse(httpStatus: HttpStatus, registerErrors: RegisterErrors): HttpResponse<*> {
        return StandardHttpResponses.customStatusResponse(
            httpStatus,
            buildResponseTO(registerErrors),
        )
    }

    private fun buildResponseTO(registerErrors: RegisterErrors): ResponseTO =
        ResponseTO(registerErrors.code, registerErrors.description)

    private fun BasicAccountRegisterData.toBasicAccountRegisterDataTO(clientIP: String): BasicAccountRegisterDataTO {
        val agreementData = registerService.updateAgreementFiles(accountId, clientIP).agreementData
        val agreementFile =
            agreementData?.let {
                publicHttpLinkGenerator.generate(
                    it.userContractFile,
                    s3LinkDuration,
                    MediaType.APPLICATION_PDF,
                )
            }
        val agreementTO = AgreementTO(
            url = agreementFile,
            hasAccepted = this.agreementData?.acceptedAt != null,
            contract = agreementFile,
            declarationOfResidency = null,
        )
        val uploadedSelfie = registerService.processLiveness(this).getOrElse { throw it }.uploadedSelfie
        val isTemporaryEmail = isTemporaryEmail(emailAddress)
        return BasicAccountRegisterDataTO(
            accountId = accountId.value,
            mobilePhone = mobilePhone?.takeIf { mobilePhoneVerified || mobilePhoneTokenExpiration != null }
                ?.toMobilePhoneResponseTO(mobilePhoneVerified, mobilePhoneTokenExpiration),
            email = if (isTemporaryEmail) null else emailAddress.value,
            emailVerification = if (isTemporaryEmail) {
                null
            } else {
                emailAddress.takeIf { emailVerified || emailTokenExpiration != null }
                    ?.toEmailResponseTO(
                        emailVerified,
                        emailTokenExpiration,
                    )
            },
            name = name,
            birthDate = birthDate?.format(registerDateFormatter),
            document = document?.value,
            created = created.format(dateTimeFormat),
            lastUpdated = lastUpdated.format(dateTimeFormat),
            userContract = agreementTO,
            agreement = agreementFileUrl,
            livenessId = livenessId?.value,
            openForUserReview = openForUserReview,
            selfie = SelfieTO(hasValidUpload = uploadedSelfie != null),
            identityValidationStatus = identityValidationStatus?.name,
            politicallyExposed = politicallyExposed?.toPoliticallyExposedTO(),
        )
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BasicRegisterController::class.java)
    }
}

private fun EmailAddress.toEmailResponseTO(verified: Boolean, expiration: Long?): EmailResponseTO =
    EmailResponseTO(
        email = value,
        isVerified = verified,
        tokenExpiration = expiration,
    )

private fun MobilePhone.toMobilePhoneResponseTO(mobilePhoneVerified: Boolean, mobilePhoneTokenExpiration: Long?) =
    MobilePhoneResponseTO(
        number = msisdn,
        isVerified = mobilePhoneVerified,
        tokenExpiration = mobilePhoneTokenExpiration?.let {
            it - getZonedDateTime().toEpochSecond()
        },
    )

@JsonInclude(JsonInclude.Include.ALWAYS)
data class BasicAccountRegisterDataTO(
    val created: String,
    val lastUpdated: String,
    val email: String?,
    val emailVerification: EmailResponseTO? = null,
    val name: String? = null,
    val birthDate: String? = null,
    val document: String? = null,
    val accountId: String,
    val mobilePhone: MobilePhoneResponseTO? = null,
    val selfie: SelfieTO?,
    val agreement: String?,
    val userContract: AgreementTO?,
    val openForUserReview: Boolean,
    val livenessId: String?,
    val identityValidationStatus: String?,
    val politicallyExposed: PoliticallyExposedTO?,
)

data class DeviceInfoTO(
    val adjustId: String?,
    val googleId: String?,
    val appleId: String?,
    val isDeviceControlGroup: Boolean?,
)

data class PersonalInfoTO(val name: String, val birthDate: String)

data class DocumentTO(val document: String)

data class VerifyIdentityResponseTO(
    val registrationType: String,
)

data class TokenIssuedTO(
    val duration: String,
    val cooldown: String,
)

data class TokenValidationTO(val token: String)

@Introspected
data class DocumentDetailsTO(
    val fatherName: String?,
    val motherName: String,
    val birthCity: String,
    @field:Pattern(
        regexp = "^(ac|AC|al|AL|am|AM|ap|AP|ba|BA|ce|CE|df|DF|es|ES|go|GO|ma|MA|mg|MG|ms|MS|mt|MT|pa|PA|pb|PB|pe|PE|pi|PI|pr|PR|rj|RJ|rn|RN|ro|RO|rr|RR|rs|RS|sc|SC|se|SE|sp|SP|to|TO)\$",
        message = "Código do estado é invalido",
    ) val birthState: String,
    val orgEmission: String,
    val documentNumber: DocumentNumber,
    @field:Pattern(
        regexp = "^\\d{2}/\\d{2}/\\d{4}$",
        message = "expeditionDate should be dd/mm/yyyy",
    ) val expeditionDate: String,
)