package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.and
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AddCreditCardResult
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardBinDetails
import ai.friday.billpayment.app.account.CreditCardBrand
import ai.friday.billpayment.app.account.CreditCardChallengeResponse
import ai.friday.billpayment.app.account.DefaultCreditCardService
import ai.friday.billpayment.app.account.DeleteCreditCardResult
import ai.friday.billpayment.app.account.GetChallengeResult
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.ValidateChallengeResult
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.integrations.GeneralCreditCardConfiguration
import ai.friday.morning.log.andAppend
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.http.annotation.QueryValue
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.validation.Validated
import jakarta.validation.constraints.Pattern
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(Role.Code.OWNER)
@Controller("/payment-method")
@Version("2")
@Validated
@FridayMePoupe
class CreditCardController(
    private val creditCardService: DefaultCreditCardService,
    private val generalCreditCardConfiguration: GeneralCreditCardConfiguration,
) {
    @Post("/credit-card")
    fun postCreditCard(
        authentication: Authentication,
        @Body body: CreateCreditCardTO,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val marker = append("accountId", accountId.value).and(
            "body.brand" to body.brand,
            "body.expiryDate" to body.expiryDate,
        )
        return try {
            val result = creditCardService.add(
                accountId = accountId,
                brand = CreditCardBrand.find(body.brand),
                cardNumber = body.pan,
                expirationDate = body.expiryDate,
                cvv = body.cvv,
            )
            handleAddCreditCardResult(result, marker)
        } catch (e: Exception) {
            handleError(accountId, e, "PostCreditCard")
        }
    }

    @Get("/credit-card/challenge/configuration")
    fun getCreditCardChallengeConfiguration(
        authentication: Authentication,
    ): HttpResponse<*> {
        generalCreditCardConfiguration.challenge.expiration
        generalCreditCardConfiguration.challenge.maxAmount
        generalCreditCardConfiguration.challenge.minAmount
        return HttpResponse.ok(
            CreditCardChallengeResponseTO(
                expiration = generalCreditCardConfiguration.challenge.expiration.toDays(),
                maxAmount = generalCreditCardConfiguration.challenge.maxAmount,
                minAmount = generalCreditCardConfiguration.challenge.minAmount,
            ),
        )
    }

    @Post("/credit-card/{paymentMethodId}/challenge")
    fun createCreditCardChallenge(
        authentication: Authentication,
        @QueryValue paymentMethodId: String,
    ): HttpResponse<*> {
        val result = creditCardService.createChallenge(
            accountId = authentication.toAccountId(),
            paymentMethodId = AccountPaymentMethodId(value = paymentMethodId),
        )

        val markers = append("accountId", authentication.toAccountId().value)
            .andAppend("paymentMehtodId", paymentMethodId)
            .andAppend("challenge", result)

        LOG.info(markers, "CreateCreditCardChallenge")

        when (result) {
            CreditCardChallengeResponse.Success -> return HttpResponse.noContent<Unit>()

            CreditCardChallengeResponse.ChallengeAlreadyExists -> {
                return StandardHttpResponses.badRequest(
                    ResponseTO(
                        code = "4003",
                        message = "Credit card challenge already exists",
                    ),
                )
            }

            is CreditCardChallengeResponse.InvalidCreditCardStatus -> {
                return StandardHttpResponses.badRequest(
                    ResponseTO(
                        code = "4004",
                        message = "Credit card is not pending",
                    ),
                )
            }

            is CreditCardChallengeResponse.CreditCardAutorizationError -> {
                LOG.error(
                    markers
                        .andAppend("acquirerReturnCode", result.acquirerReturnCode)
                        .andAppend("acquirerReturnMessage", result.acquirerReturnMessage)
                        .andAppend("acquirerStatus", result.status),
                    "CreateCreditCardChallenge",
                )
                return StandardHttpResponses.customStatusResponse(
                    HttpStatus.UNPROCESSABLE_ENTITY,
                    ResponseTO(
                        code = "4006",
                        message = "Error on credit card authorization",
                    ),
                )
            }

            is CreditCardChallengeResponse.InvalidChallengeStatus -> {
                return StandardHttpResponses.badRequest(
                    ResponseTO(
                        code = "4005",
                        message = "Challenge with invalid status.",
                    ),
                )
            }

            is CreditCardChallengeResponse.UnknownError -> {
                LOG.error(markers, "CreateCreditCardChallenge", result.exception)
                return StandardHttpResponses.serverError("Error creating credit card challenge")
            }

            CreditCardChallengeResponse.LimitReached -> {
                LOG.warn(markers.andAppend("limitReached", "true"), "CreateCreditCardChallenge")
                return HttpResponse.status<Unit>(HttpStatus.TOO_MANY_REQUESTS)
            }
        }
    }

    @Put("/credit-card/{paymentMethodId}/validate")
    fun validateChallenge(
        authentication: Authentication,
        @QueryValue paymentMethodId: String,
        @Body body: ValidateChallengeTO,
    ): HttpResponse<*> {
        val markers = append("accountId", authentication.toAccountId().value)
            .andAppend("paymentMethodId", paymentMethodId)

        if (body.token < generalCreditCardConfiguration.challenge.minAmount || body.token > generalCreditCardConfiguration.challenge.maxAmount) {
            return HttpResponse.badRequest(ResponseTO("4005", "Valor do token fora dos limites permitidos."))
        }

        val challengeResult = creditCardService.validateChallenge(
            accountId = authentication.toAccountId(),
            paymentMethodId = AccountPaymentMethodId(paymentMethodId),
            token = body.token,
        )

        markers.andAppend("challenge", challengeResult)

        LOG.info(markers, "ValidateCreditCardChallenge")

        when (challengeResult) {
            ValidateChallengeResult.AttemptsExceeded -> {
                return HttpResponse.badRequest(ResponseTO("4001", "Limite de tentativas máximo atingido."))
            }

            ValidateChallengeResult.Expired -> {
                return HttpResponse.badRequest(ResponseTO("4002", "Tentativa de validação expirada."))
            }

            ValidateChallengeResult.ChallengeMismatch -> {
                return HttpResponse.badRequest(ResponseTO("4003", "Valor informado não corresponde com a validação."))
            }

            ValidateChallengeResult.NotFound -> {
                return HttpResponse.notFound(ResponseTO("4004", "Tentativa de validação não encontrada."))
            }

            is ValidateChallengeResult.CreditCardInvalidStatus -> {
                return HttpResponse.badRequest(
                    ResponseTO(
                        "4005",
                        "Cartão com status inválido. Status:${challengeResult.status}",
                    ),
                )
            }

            is ValidateChallengeResult.ChallengeInvalidStatus -> {
                return StandardHttpResponses.customStatusResponse(
                    HttpStatus.UNPROCESSABLE_ENTITY,
                    ResponseTO("4006", "Validação com status inválido. Status:${challengeResult.status}"),
                )
            }

            is ValidateChallengeResult.FailedOnAcquirerVoid -> {
                LOG.error(markers, "ValidateCreditCardChallenge", challengeResult.e)
                return HttpResponse.noContent<Unit>()
            }

            is ValidateChallengeResult.UnknownError -> {
                LOG.error(markers, "ValidateCreditCardChallenge", challengeResult.e)
                return HttpResponse.serverError<Unit>()
            }

            ValidateChallengeResult.Success -> {
                return HttpResponse.noContent<Unit>()
            }
        }
    }

    @Get("/credit-card/{paymentMethodId}/challenge")
    fun getCreditCardChallenge(authentication: Authentication, @QueryValue paymentMethodId: String): HttpResponse<*> {
        try {
            val challengeResult = creditCardService.getChallenge(
                accountId = authentication.toAccountId(),
                paymentMethodId = AccountPaymentMethodId(paymentMethodId),
            )

            return when (challengeResult) {
                GetChallengeResult.Expired -> HttpResponse.notFound<Unit>()
                is GetChallengeResult.InvalidStatus -> HttpResponse.badRequest<Unit>()
                GetChallengeResult.MaxAttemptsReached -> HttpResponse.ok(GetChallengeTO(maxAttemptsReached = true))
                GetChallengeResult.NotFound -> HttpResponse.notFound<Unit>()
                is GetChallengeResult.Success -> HttpResponse.ok(GetChallengeTO(maxAttemptsReached = false))
            }
        } catch (e: Exception) {
            return handleError(authentication.toAccountId(), e, "getCreditCardChallenge")
        }
    }

    @Delete("/credit-card/{paymentMethodId}")
    fun deleteCreditCard(authentication: Authentication, @QueryValue paymentMethodId: String): HttpResponse<*> {
        val markers: LogstashMarker =
            append("paymentMethodId", paymentMethodId).and(append("accountId", authentication.toAccountId().value))
        val result = creditCardService.delete(authentication.toAccountId(), AccountPaymentMethodId(paymentMethodId))
        markers.and<LogstashMarker>(append("result", result.javaClass.simpleName))
        return when (result) {
            is DeleteCreditCardResult.Deleted -> {
                LOG.info(markers, "DeleteCreditCard")
                HttpResponse.noContent<Unit>()
            }

            DeleteCreditCardResult.NotFound -> {
                LOG.info(markers, "DeleteCreditCard")
                HttpResponse.notFound<Unit>()
            }

            is DeleteCreditCardResult.ServerError -> {
                LOG.error(markers, "DeleteCreditCard", result.e)
                StandardHttpResponses.serverError()
            }
        }
    }

    @Get("/credit-card/bin/{bin}")
    fun getCreditCardBinDetails(
        authentication: Authentication,
        @QueryValue
        @Pattern(
            regexp = "^\\d{6,9}$",
            message = "Bin must be between 6 and 9 digits long",
        )
        bin: String,
    ): HttpResponse<*> {
        return try {
            HttpResponse.ok(creditCardService.retrieveBinDetails(bin)?.toCreditCardBinDetailsTO() ?: emptyMap<String, String>())
        } catch (e: Exception) {
            handleError(authentication.toAccountId(), e, "getCreditCardBinDetails")
        }
    }

    private fun handleError(accountId: AccountId, e: Exception, logName: String): HttpResponse<ResponseTO> {
        LOG.error(
            append("accountId", accountId.value).and(append("httpStatus", HttpStatus.INTERNAL_SERVER_ERROR.code)),
            logName,
            e,
        )
        return if (e.message != null) StandardHttpResponses.serverError(e.message!!) else StandardHttpResponses.serverError()
    }

    private fun convertFrom(accountPaymentMethod: AccountPaymentMethod): PaymentMethodTO {
        return when (val paymentMethodType = accountPaymentMethod.method) {
            is CreditCard -> {
                val creditCardTO = paymentMethodType.toCreditCardTO(accountPaymentMethod.status.name)
                PaymentMethodTO(
                    id = accountPaymentMethod.id.value,
                    type = PaymentMethodType.CREDIT_CARD.name,
                    creditCard = creditCardTO,
                )
            }

            is InternalBankAccount -> {
                val bankDetailsTO = paymentMethodType.toInternalBankAccountTO()
                PaymentMethodTO(
                    id = accountPaymentMethod.id.value,
                    type = PaymentMethodType.BALANCE.name,
                    bankDetails = bankDetailsTO,
                )
            }

            else -> throw IllegalArgumentException("")
        }
    }

    internal fun handleAddCreditCardResult(
        result: AddCreditCardResult,
        marker: LogstashMarker,
    ): HttpResponse<*> {
        LOG.info(marker.andAppend("result", result.javaClass.simpleName), "PostCreditCard")
        return when (result) {
            is AddCreditCardResult.Created -> HttpResponse.created(convertFrom(result.accountPaymentMethod))
            AddCreditCardResult.MaxLimitReached -> HttpResponse.badRequest(AddCreditCardErrorResponses.MAX_LIMIT_REACHED.responseTO)
            AddCreditCardResult.InvalidCreditCard -> HttpResponse.badRequest(AddCreditCardErrorResponses.INVALID_CREDIT_CARD.responseTO)
            AddCreditCardResult.UnauthorizedCreditCard -> HttpResponse.badRequest(AddCreditCardErrorResponses.UNAUTHORIZED_CREDIT_CARD.responseTO)
            is AddCreditCardResult.ServerError -> {
                LOG.error(
                    marker.andAppend("httpStatus", HttpStatus.INTERNAL_SERVER_ERROR.code),
                    "PostCreditCard",
                    result.e,
                )
                HttpResponse.serverError(AddCreditCardErrorResponses.SERVER_ERROR.responseTO)
            }

            AddCreditCardResult.CreditCardAlreadyAddedToday -> HttpResponse.badRequest(AddCreditCardErrorResponses.CREDIT_CARD_ALREADY_ADDED_TODAY.responseTO)

            is AddCreditCardResult.HighRiskCreditCard -> HttpResponse.badRequest(AddCreditCardErrorResponses.HIGH_RISK_CREDIT_CARD.responseTO)
            AddCreditCardResult.CreditCardAlreadyInUse -> HttpResponse.badRequest(AddCreditCardErrorResponses.HIGH_RISK_CREDIT_CARD.responseTO)

            else -> HttpResponse.badRequest(AddCreditCardErrorResponses.ADD_ERROR.responseTO)
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(CreditCardController::class.java)
    }
}

private fun CreditCardBinDetails.toCreditCardBinDetailsTO() = CreditCardDetailsTO(
    provider = this.provider,
    status = this.status,
    cardType = this.cardType,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
private data class CreditCardDetailsTO(
    @JsonProperty("Provider") val provider: String?,
    @JsonProperty("Status") val status: String?,
    @JsonProperty("CardType") val cardType: String?,
)

fun InternalBankAccount.toInternalBankAccountTO() = InternalBankAccountTO(
    accountType = this.accountType,
    bankNo = this.bankNo,
    routingNo = this.routingNo,
    accountNo = this.accountNo,
    accountDv = this.accountDv,
    mode = this.bankAccountMode,
)

fun CreditCard.toCreditCardTO(status: String) = CreditCardTO(
    brand = this.brand.mainName,
    pan = this.maskedPan,
    expiryDate = this.expiryDate,
    status = status,
    externalId = this.externalId?.value,
    mainCard = this.mainCard ?: false,
    issuer = this.binDetails?.issuer,
    issuerCode = this.binDetails?.issuerCode,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class PaymentMethodTO(
    val id: String,
    val type: String,
    val creditCard: CreditCardTO? = null,
    val bankDetails: InternalBankAccountTO? = null,
)

data class CreditCardTO(
    val brand: String,
    val pan: String,
    val expiryDate: String,
    val status: String,
    val mainCard: Boolean,
    val externalId: String? = null,
    val issuer: String? = null,
    val issuerCode: String? = null,
)

data class CreateCreditCardTO(
    val brand: String,
    val pan: String,
    val expiryDate: String,
    val cvv: String,
)

data class ValidateChallengeTO(
    val token: Long,
)

enum class AddCreditCardErrorResponses(val responseTO: ResponseTO) {
    MAX_LIMIT_REACHED(ResponseTO(code = "4001", message = "Limite máximo de cartões de crédito atingido.")),
    INVALID_CREDIT_CARD(ResponseTO(code = "4002", message = "Cartão de crédito inválido.")),
    UNAUTHORIZED_CREDIT_CARD(
        ResponseTO(
            code = "4003",
            message = "Cartão de crédito não autorizado. Entre em contato com o emissor.",
        ),
    ),
    ADD_ERROR(
        ResponseTO(
            code = "4004",
            message = "Não foi possível adicionar o cartão",
        ),
    ),
    CREDIT_CARD_ALREADY_ADDED_TODAY(ResponseTO(code = "4005", message = "Cartão de crédito já adicionado hoje.")),
    HIGH_RISK_CREDIT_CARD(
        ResponseTO(
            code = "4006",
            message = "Cartão de crédito não autorizado.",
        ),
    ),
    SERVER_ERROR(ResponseTO(code = "5000", message = "Ocorreu um erro. Por favor, tente novamente")),
}

data class GetChallengeTO(
    val maxAttemptsReached: Boolean,
)

data class CreditCardChallengeResponseTO(
    val expiration: Long,
    val maxAmount: Long,
    val minAmount: Long,
)