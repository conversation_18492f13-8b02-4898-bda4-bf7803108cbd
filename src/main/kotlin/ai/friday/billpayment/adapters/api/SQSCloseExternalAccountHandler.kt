package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.CloseExternalAccountEvent
import ai.friday.billpayment.app.account.CloseExternalAccountNowStepExecutor
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.context.annotation.Property
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSCloseExternalAccountHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = "sqs.closeExternalAccountQueueName") private val queueName: String,
    private val walletService: WalletService,
    private val closeExternalAccountStepExecutor: CloseExternalAccountNowStepExecutor,
) : AbstractSQSHandler(
    amazonSQS,
    configuration,
    queueName,
) {
    @NewSpan
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val event = parseObjectFrom<CloseExternalAccountEvent>(message.body())
        val markers = append("event", event)

        val walletId = WalletId(event.walletId)

        val wallet = walletService.findWallet(walletId)
        markers.andAppend("accountId", wallet.founder.accountId.value)

        closeExternalAccountStepExecutor.executeWithoutRedrive(
            wallet = wallet,
        ).map {
            markers.andAppend("result", it)
            LOG.info(markers, "SQSCloseExternalAccountHandler")
            return SQSHandlerResponse(true)
        }.getOrElse {
            markers.andAppend("error", it)
            LOG.warn(markers, "SQSCloseExternalAccountHandler")
            return SQSHandlerResponse(false)
        }
    }

    override fun handleError(
        message: Message,
        e: Exception,
    ): SQSHandlerResponse {
        val markers = append("eventBody", message.body())

        LOG.error(markers, "SQSCloseExternalAccountHandler", e)
        return SQSHandlerResponse(false)
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SQSCloseExternalAccountHandler::class.java)
    }
}