package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.messaging.InvoiceTO
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Role.Code.ADMIN
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.utilityaccount.Utility
import ai.friday.billpayment.app.utilityaccount.UtilityAccount
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionDetails
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionStatus
import ai.friday.billpayment.app.utilityaccount.UtilityAccountError
import ai.friday.billpayment.app.utilityaccount.UtilityAccountErrorFiles
import ai.friday.billpayment.app.utilityaccount.UtilityAccountId
import ai.friday.billpayment.app.utilityaccount.UtilityAccountService
import ai.friday.billpayment.app.utilityaccount.UtilityConnectionMethod
import ai.friday.billpayment.app.utilityaccount.UtilityFlowInvoices
import ai.friday.billpayment.app.utils.PublicHttpLinkGenerator
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import java.time.Duration
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(BACKOFFICE)
@Controller("/backoffice/accountUtility")
@FridayMePoupe
class BackofficeAccountUtilityController(
    private val utilityAccountService: UtilityAccountService,
    private val linkGenerator: PublicHttpLinkGenerator,
    private val notificationAdapter: NotificationAdapter,
) {

    @Put("{walletId}/{utilityAccountId}/setStatus")
    fun utilityAccountSetStatus(
        @PathVariable walletId: String,
        @PathVariable utilityAccountId: String,
        @Body body: UtilityAccountSetStatusTO,
    ): HttpResponse<*> {
        val markers = Markers.append("walletId", walletId).andAppend("utilityAccountId", utilityAccountId)
            .andAppend("body", body)

        val currentStatusRequest = UtilityAccountConnectionStatus.of(body.currentStatus)
            ?: return HttpResponse.badRequest("Status not found")

        val newStatusRequest = UtilityAccountConnectionStatus.of(body.newStatus)
            ?: return HttpResponse.badRequest("Status not found")

        val result =
            utilityAccountService.setStatus(
                utilityAccountId = UtilityAccountId(utilityAccountId),
                walletId = WalletId(walletId),
                currentStatus = currentStatusRequest,
                newStatus = newStatusRequest,
                statusMessage = body.statusMessage,
            )

        result.mapLeft {
            LOG.error(markers, "utilityAccountSetStatus", it)
            when (it) {
                UtilityAccountError.AccountNotFound -> return HttpResponse.notFound<Unit>()
                UtilityAccountError.CannotConnectAccount -> return HttpResponse.status<Unit>(HttpStatus.CONFLICT)
                else -> return HttpResponse.serverError("Erro desconhecido ao conectar: $it")
            }
        }
        LOG.info(markers, "utilityAccountSetStatus")
        return HttpResponse.noContent<Unit>()
    }

    @Post("{walletId}/{utilityAccountId}/requestReconnection")
    fun utilityAccountRequestReconnection(
        @PathVariable walletId: String,
        @PathVariable utilityAccountId: String,
    ): HttpResponse<*> {
        val markers = Markers.append("walletId", walletId).andAppend("utilityAccountId", utilityAccountId)

        val result =
            utilityAccountService.requestReconnection(
                utilityAccountId = UtilityAccountId(utilityAccountId),
                walletId = WalletId(walletId),
            )

        result.mapLeft {
            LOG.error(markers, "utilityAccountSetStatus", it)
            when (it) {
                UtilityAccountError.AccountNotFound -> return HttpResponse.notFound<Unit>()
                UtilityAccountError.CannotConnectAccount -> return HttpResponse.status<Unit>(HttpStatus.CONFLICT)
                else -> return HttpResponse.serverError("Erro desconhecido ao pedir reconexão: $it")
            }
        }
        LOG.info(markers, "utilityAccountREquestReconnection")
        return HttpResponse.noContent<Unit>()
    }

    @Put("{walletId}/{utilityAccountId}/retry")
    fun utilityAccountRetry(
        @PathVariable walletId: String,
        @PathVariable utilityAccountId: String,
    ): HttpResponse<*> {
        val markers = Markers.append("walletId", walletId).andAppend("utilityAccountId", utilityAccountId)

        utilityAccountService.retryUtilityAccount(WalletId(walletId), UtilityAccountId(utilityAccountId)).map {
            LOG.info(markers.andAppend("utilityAccount", it), "utilityAccountRetry")
            return HttpResponse.ok(
                BackofficeUtilityAccountTO(
                    id = it.id.value,
                    status = it.status,
                    utility = it.utility,
                ),
            )
        }.getOrElse {
            LOG.error(markers, "utilityAccountRetry")
            return HttpResponse.badRequest(it::class.simpleName ?: "Unknown error")
        }
    }

    @Put("/{utility}/retryAll")
    fun utilityRetryAll(
        @PathVariable utility: Utility,
    ): HttpResponse<*> {
        val utilityAccounts = utilityAccountService.findAllConnectedByFlow().filter { it.utility == utility }

        val markers = Markers.append("utility", utility.name).andAppend("listSize", utilityAccounts.size)

        utilityAccounts.forEach {
            utilityAccountService.postRequestInvoicesMessage(it)
        }

        LOG.info(markers, "BackofficeAccountUtilityController#RetryAll")

        return HttpResponse.noContent<Unit>()
    }

    @Put("{walletId}/{utilityAccountId}/convertoToFlow")
    fun convertToFlow(
        @PathVariable walletId: String,
        @PathVariable utilityAccountId: String,
        @Body body: ConvertToFlowTO,
    ): HttpResponse<*> {
        val markers = Markers.append("walletId", walletId).andAppend("utilityAccountId", utilityAccountId)

        utilityAccountService.convertToFlow(
            WalletId(walletId),
            UtilityAccountId(utilityAccountId),
            body.connectionDetails,
        ).map {
            LOG.info(markers.andAppend("utilityAccount", it), "BackofficeAccountUtilityController#convertToFlow")
            return HttpResponse.ok("convertido e enviado para conexão")
        }.getOrElse {
            LOG.error(markers, "BackofficeAccountUtilityController#convertToFlow")
            return HttpResponse.badRequest(it::class.simpleName ?: "Unknown error")
        }
    }

    @Put("{walletId}/{utilityAccountId}/updateInvoices")
    fun updateInvoices(
        @PathVariable walletId: String,
        @PathVariable utilityAccountId: String,
    ): HttpResponse<*> {
        val markers = Markers.append("walletId", walletId).andAppend("utilityAccountId", utilityAccountId)

        val utilityAccount: UtilityAccount = utilityAccountService.find(
            walletId = WalletId(walletId),
            utilityAccountId = UtilityAccountId(utilityAccountId),
        ) ?: return HttpResponse.notFound("")

        utilityAccountService.postRequestInvoicesMessage(utilityAccount)

        LOG.info(markers, "BackofficeAccountUtilityController#updateInvoices")

        return HttpResponse.noContent<Unit>()
    }

    @Delete("{walletId}/{utilityAccountId}/disconnectLegacy")
    fun disconnectLegacy(
        @PathVariable walletId: String,
        @PathVariable utilityAccountId: String,
    ): HttpResponse<*> {
        val markers = Markers.append("walletId", walletId).andAppend("utilityAccountId", utilityAccountId)

        val utilityAccount: UtilityAccount =
            utilityAccountService.find(WalletId(walletId), UtilityAccountId(utilityAccountId))
                ?: return HttpResponse.notFound("")

        utilityAccountService.disconnectLegacy(utilityAccount)

        LOG.info(markers, "BackofficeAccountUtilityController#disconnectLegacy")

        return HttpResponse.noContent<Unit>()
    }

    @Put("listAllManual")
    @Secured(ADMIN)
    fun listAllManual(
        @Body listAllManualTO: ListAllManualTO,
    ): HttpResponse<*> {
        val minutesInADay = 24 * 60L
        val duration = if (listAllManualTO.openErrorLinkFilesForMinutes > minutesInADay) {
            Duration.ofMinutes(minutesInADay)
        } else {
            Duration.ofMinutes(listAllManualTO.openErrorLinkFilesForMinutes)
        }

        val result = utilityAccountService.listAllManual(listAllManualTO.exposePasswords).map { utilityAccount ->
            val errorFiles = utilityAccount.errorFiles
            if (duration.seconds > 0 && errorFiles != null) {
                val publicLinks = errorFiles.filesKey.mapNotNull { fileKey ->
                    linkGenerator.generate(
                        StoredObject(
                            region = errorFiles.region,
                            bucket = errorFiles.bucket,
                            key = fileKey,
                        ),
                        duration,
                        utilityAccountService.errorFileMediaType(fileKey),
                    )
                }
                utilityAccount.toBackofficeUtilityAccountAllManualTO(errorFiles.copy(filesKey = publicLinks))
            } else {
                utilityAccount.toBackofficeUtilityAccountAllManualTO()
            }
        }.sortedBy { it.createdAt }
        return HttpResponse.ok(result)
    }

    @Get("{walletId}/{utilityAccountId}/retrievePassword")
    @Secured(ADMIN)
    fun retrievePassword(
        @PathVariable walletId: String,
        @PathVariable utilityAccountId: String,
    ): HttpResponse<*> {
        val utilityAccount: UtilityAccount = utilityAccountService.findUtilityAccountWithPlainCredentials(
            WalletId(walletId),
            UtilityAccountId(utilityAccountId),
        )
            ?: return HttpResponse.notFound("Conta de consumo não encontrada ou não está conectada, ou não está a mais de 30 dias sem receber fatura")
        return HttpResponse.ok(utilityAccount.toBackofficeUtilityAccountAllManualTO())
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BackofficeAccountUtilityController::class.java)
    }

    @Post("{walletId}/{utilityAccountId}/sendInvoiceNotFoundNotification")
    fun sendInvoiceNotFoundNotification(
        @PathVariable walletId: String,
        @PathVariable utilityAccountId: String,
    ): HttpResponse<*> {
        val markers = Markers.append("walletId", walletId).andAppend("utilityAccountId", utilityAccountId)

        val utilityAccount: UtilityAccount = utilityAccountService.find(WalletId(walletId), UtilityAccountId(utilityAccountId)) ?: return HttpResponse.notFound("")

        notificationAdapter.notifyInvoicesNotFoundUtilityAccount(utilityAccount)

        LOG.info(markers, "BackofficeAccountUtilityController#sendInvoiceNotFoundNotification")

        return HttpResponse.noContent<Unit>()
    }

    @Post("{walletId}/{utilityAccountId}/sendInvoiceScanErrorNotification")
    fun sendInvoiceScanErrorNotification(
        @PathVariable walletId: String,
        @PathVariable utilityAccountId: String,
    ): HttpResponse<*> {
        val markers = Markers.append("walletId", walletId).andAppend("utilityAccountId", utilityAccountId)

        val utilityAccount: UtilityAccount = utilityAccountService.find(WalletId(walletId), UtilityAccountId(utilityAccountId)) ?: return HttpResponse.notFound("")

        notificationAdapter.notifyInvoicesScanErrorUtilityAccount(utilityAccount)

        LOG.info(markers, "BackofficeAccountUtilityController#sendInvoiceScanErrorNotification")

        return HttpResponse.noContent<Unit>()
    }

    @Post("{utilityAccountId}/sendInvoices")
    fun sendInvoices(
        @PathVariable utilityAccountId: String,
        @Body invoices: List<InvoiceTO>,
    ): HttpResponse<*> {
        val logMessage = "BackofficeAccountUtilityController#sendInvoices"
        val markers = Markers.append("utilityAccountId", utilityAccountId).andAppend("invoices", invoices)

        val parsedInvoices = invoices.map { it.toInvoice() }

        val apiInvoices = UtilityFlowInvoices(
            utilityAccountId = UtilityAccountId(utilityAccountId),
            invoices = parsedInvoices,
            lastDueDateFound = parsedInvoices.maxOfOrNull { it.dueDate },
        )

        utilityAccountService.handleResponseInvoices(apiInvoices).getOrElse {
            markers.andAppend("error", it)
            if (it is UtilityAccountError.InvalidInvoicesResponse) {
                LOG.error(markers, logMessage, it.exception)
            } else {
                LOG.warn(markers, logMessage)
            }

            return HttpResponse.serverError(it.toString())
        }

        LOG.info(logMessage)
        return HttpResponse.ok<Unit>()
    }
}

data class ListAllManualTO(
    val exposePasswords: Boolean = false,
    val openErrorLinkFilesForMinutes: Long = 0,
)

data class BackofficeUtilityAccountTO(
    val id: String,
    val status: UtilityAccountConnectionStatus,
    val utility: Utility,
)

data class BackofficeUtilityAccountAllManualTO(
    val id: String,
    val walletId: String,
    val status: UtilityAccountConnectionStatus,
    val utility: Utility,
    val accountEmail: String,
    val attempts: Int,
    val updatedAt: String,
    val createdAt: String,
    val notificatedAt: String?,
    val lastBillIdFound: String?,
    val lastBillFoundAt: String?,
    val lastDueDateFound: String?,
    val lastScannedAt: String?,
    val connectionMethod: UtilityConnectionMethod,
    val connectionDetails: UtilityAccountConnectionDetails,
    val errorFiles: UtilityAccountErrorFiles?,
)

private fun UtilityAccount.toBackofficeUtilityAccountAllManualTO(errorFiles: UtilityAccountErrorFiles? = null) = BackofficeUtilityAccountAllManualTO(
    id = id.value,
    walletId = walletId.value,
    status = status,
    utility = utility,
    accountEmail = accountEmail,
    attempts = attempts,
    updatedAt = updatedAt.format(DateTimeFormatter.ISO_LOCAL_DATE),
    createdAt = createdAt.format(DateTimeFormatter.ISO_LOCAL_DATE),
    notificatedAt = notificatedAt?.format(DateTimeFormatter.ISO_LOCAL_DATE),
    lastBillIdFound = lastBillIdFound?.value,
    lastDueDateFound = lastDueDateFound?.format(DateTimeFormatter.ISO_LOCAL_DATE),
    lastBillFoundAt = lastBillFound?.format(DateTimeFormatter.ISO_LOCAL_DATE),
    lastScannedAt = lastScannedAt?.format(DateTimeFormatter.ISO_LOCAL_DATE),
    connectionMethod = connectionMethod,
    connectionDetails = connectionDetails,
    errorFiles = errorFiles,
)

data class UtilityAccountSetStatusTO(
    val currentStatus: String,
    val newStatus: String,
    val statusMessage: String? = null,
)

data class ConvertToFlowTO(
    val connectionDetails: Map<String, String>,
)