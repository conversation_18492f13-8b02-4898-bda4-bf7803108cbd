package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.FinancialInstitutionGlobalData
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Role
import ai.friday.morning.date.dateFormat
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpResponse.ok
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.security.annotation.Secured

@Secured(Role.Code.OWNER)
@Controller("/holiday")
@Version("2")
@FridayMePoupe
class HolidayController {

    @Get
    fun getHoliday(): HttpResponse<List<String>> {
        return ok(FinancialInstitutionGlobalData.holidays.map { it.format(dateFormat) })
    }
}