package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.security.Fingerprint
import ai.friday.billpayment.app.security.FingerprintApp
import ai.friday.billpayment.app.security.FingerprintDevice
import ai.friday.billpayment.app.security.FingerprintGeolocation
import ai.friday.billpayment.app.security.FingerprintOS
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.micronaut.context.annotation.Requires
import io.micronaut.http.HttpRequest
import io.micronaut.http.MutableHttpResponse
import io.micronaut.http.annotation.Filter
import io.micronaut.http.filter.HttpServerFilter
import io.micronaut.http.filter.ServerFilterChain
import java.util.Base64
import org.reactivestreams.Publisher

@Filter(value = ["/v2/schedule/**"])
@Requires(property = "features.schedule.fingerprint.enabled", value = "true", defaultValue = "false")
class FingerprintFilter() : HttpServerFilter {

    override fun doFilter(request: HttpRequest<*>, chain: ServerFilterChain): Publisher<MutableHttpResponse<*>> {
        val fingerprint = if (request.headers.contains("fingerprint")) {
            decode(request.headers["fingerprint"])
        } else {
            null
        }

        request.setAttribute("X-Fingerprint", fingerprint)

        return chain.proceed(request)
    }

    private fun decode(headerValue: String?): Fingerprint? {
        if (headerValue.isNullOrBlank()) return null

        val json = String(Base64.getDecoder().decode(headerValue))

        val header = parseObjectFrom<FingerprintHeader>(json)

        return header.toFingerprint()
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class FingerprintHeader(
    val sessionId: String?,
    val appVersion: String?,
    val longitude: String?,
    val latitude: String?,
    val deviceManufacturer: String?,
    val deviceModel: String?,
    val installationId: String?,
    val deviceOs: String?,
    val osVersion: String?,
)

private fun FingerprintHeader.toFingerprint() = Fingerprint(
    sessionId = this.sessionId,
    app = FingerprintApp(version = this.appVersion),
    geolocation = FingerprintGeolocation(latitude = this.latitude, longitude = this.longitude),
    device = FingerprintDevice(
        manufacturer = this.deviceManufacturer,
        model = this.deviceModel,
        installationId = this.installationId,
    ),
    os = FingerprintOS(name = this.deviceOs, version = this.osVersion),

)