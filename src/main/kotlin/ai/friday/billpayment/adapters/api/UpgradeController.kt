package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.RegisterService
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.utils.PublicHttpLinkGenerator
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.context.annotation.Property
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Controller
import io.micronaut.http.server.util.HttpClientAddressResolver
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.validation.Validated
import java.time.Duration
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.OWNER)
@Controller("/upgrade")
@Version("2")
@Validated
@FridayMePoupe
class UpgradeController(
    registerService: RegisterService,
    accountService: AccountService,
    systemActivityService: SystemActivityService,
    configuration: AccountRegisterConfiguration,
    addressResolver: HttpClientAddressResolver,
    @Property(name = "urls.termsOfUse") private val agreementFileUrl: String,
    @Property(name = "accountRegister.user_files.contractLinkDuration") private val s3LinkDuration: Duration,
    publicHttpLinkGenerator: PublicHttpLinkGenerator,
) : RegisterController(registerService, accountService, systemActivityService, configuration, addressResolver, agreementFileUrl, s3LinkDuration, publicHttpLinkGenerator, logNamePrefix = "Upgrade#") {

    override fun retrieveAccountRegisterData(authentication: Authentication, request: HttpRequest<*>): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        try {
            registerService.startUpgrade(accountId)
        } catch (e: Exception) {
            return withLogError(
                e,
                HttpStatus.INTERNAL_SERVER_ERROR,
                authentication,
                "startUpgrade",
                RegisterErrors.GENERIC_ERROR,
            )
        }

        return super.retrieveAccountRegisterData(authentication, request)
    }

    override fun processAgreement(
        authentication: Authentication,
        agreementRequestTO: AgreementRequestTO,
        request: HttpRequest<*>,
    ): HttpResponse<*> {
        if (agreementRequestTO.hasAccepted.not()) {
            return withLogInfo(
                HttpStatus.BAD_REQUEST,
                authentication,
                "updateAgreement",
                RegisterErrors.INVALID_AGREEMENT_INFO,
            )
        }
        val markers = Markers.append("accountId", authentication.toAccountId().value)
        val clientIp = addressResolver.resolve(request) ?: ""

        val result = registerService.processUpgradeAgreement(authentication.toAccountId(), clientIp)

        return result.map<HttpResponse<*>> {
            val accountRegister = it.toAccountRegisterDataTO(clientIp)
            LOG.info(markers.andAppend("response", it), "${logNamePrefix}updateAgreement")
            registerService.cleanUpTestAccount(it.accountId, it.mobilePhone!!)
            HttpResponse.ok(accountRegister)
        }.getOrElse {
            handleError(it, authentication, "updateAgreement")
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(UpgradeController::class.java)
    }
}