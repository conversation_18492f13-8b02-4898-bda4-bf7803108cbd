package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.api.builders.BillTOBuilder
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.CONCESSIONARIA_DIGITABLE_LINE_SIZE
import ai.friday.billpayment.app.bill.FICHA_DIGITABLE_LINE_SIZE
import ai.friday.billpayment.app.payment.AutomaticPixReceiptData
import ai.friday.billpayment.app.payment.BoletoReceiptData
import ai.friday.billpayment.app.payment.GetReceiptErrors
import ai.friday.billpayment.app.payment.InvestmentReceiptData
import ai.friday.billpayment.app.payment.InvoiceReceiptData
import ai.friday.billpayment.app.payment.PixReceiptData
import ai.friday.billpayment.app.payment.ReceiptData
import ai.friday.billpayment.app.payment.ReceiptService
import ai.friday.billpayment.app.payment.getDocumentType
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.log.andAppend
import arrow.core.flatMap
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonInclude
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.validation.Validated
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Validated
@Secured(Role.Code.OWNER)
@Controller("/bill/id/{billId}/receipt")
@Version("2")
@FridayMePoupe
class ReceiptController(
    private val receiptService: ReceiptService,
    private val billTOBuilder: BillTOBuilder,
) {

    @Get
    fun getReceipt(@PathVariable billId: String, authentication: Authentication): HttpResponse<*> {
        val markers: LogstashMarker = Markers.append("billId", billId)
            .andAppend("accountId", authentication.toAccountId().value)

        val member = authentication.asWalletMember()
        val receiptResult = receiptService.getReceipt(
            walletId = authentication.toWalletId(),
            billId = BillId(billId),
            member = member,
        )

        return receiptResult.map { receiptData ->
            LOG.info(markers, "getReceipt")
            HttpResponse.ok(buildResponseFrom(receiptData))
        }.getOrElse { error ->
            logResponseFrom(markers, "getReceipt", error)
            buildResponseFrom(error)
        }
    }

    @Get("/files")
    fun getReceiptFiles(@PathVariable billId: String, authentication: Authentication): HttpResponse<*> {
        val markers: LogstashMarker = Markers.append("billId", billId)
            .andAppend("accountId", authentication.toAccountId().value)

        val member = authentication.asWalletMember()
        val receiptResult = receiptService.getReceipt(
            walletId = authentication.toWalletId(),
            billId = BillId(billId),
            member = member,
        )

        return receiptResult.flatMap { receiptData ->
            receiptService.resolveReceiptFiles(receiptData)
        }.map { receiptFiles ->
            LOG.info(markers, "getReceiptFiles")
            HttpResponse.ok(
                ReceiptFilesTO(
                    imageUrl = receiptFiles.imageUrl,
                    pdfUrl = receiptFiles.pdfUrl,
                ),
            )
        }.getOrElse { error ->
            logResponseFrom(markers, "getReceiptFiles", error)
            buildResponseFrom(error)
        }
    }

    private fun logResponseFrom(markers: LogstashMarker, message: String, getReceiptErrors: GetReceiptErrors) {
        markers.and<LogstashMarker>(Markers.append("GetReceiptErrors", getReceiptErrors.javaClass.simpleName))
        when (getReceiptErrors) {
            is GetReceiptErrors.ServerError -> LOG.error(markers, message, getReceiptErrors.e)
            else -> LOG.warn(markers, message)
        }
    }

    private fun buildResponseFrom(getReceiptErrors: GetReceiptErrors): HttpResponse<*> {
        return when (getReceiptErrors) {
            GetReceiptErrors.BillIsNotPaid -> StandardHttpResponses.badRequest(
                4001,
                "Cannot get receipt cause bill was not paid",
            )

            GetReceiptErrors.ReceiptNotReady -> StandardHttpResponses.badRequest(
                4002,
                "Cannot get receipt cause receipt is not ready",
            )

            GetReceiptErrors.BillNotFound -> StandardHttpResponses.notFound(
                ResponseTO(
                    "4004",
                    "Cannot get receipt cause bill was not found",
                ),
            )

            is GetReceiptErrors.ServerError -> StandardHttpResponses.serverError(
                ResponseTO(
                    "5001",
                    "Server Error. Try again later.",
                ),
            )

            GetReceiptErrors.MemberNotAllowed -> HttpResponse.status<Any>(HttpStatus.FORBIDDEN)
        }
    }

    private fun buildResponseFrom(receiptData: ReceiptData): ReceiptTO {
        return when (receiptData) {
            is BoletoReceiptData -> BoletoReceiptTO(
                totalAmount = receiptData.totalAmount,
                dateTime = receiptData.dateTime.format(dateTimeFormat),
                recipient = billTOBuilder.convertToRecipientTo(receiptData.recipient),
                assignorName = receiptData.assignor,
                payerName = receiptData.payer?.name,
                payerDocument = receiptData.payer?.document,
                payerDocumentType = getDocumentType(receiptData.payer?.document),
                typeableLine = receiptData.barcode.digitable,
                authentication = receiptData.authentication,
                dueDate = receiptData.dueDate.format(dateFormat),
                billType = receiptData.barcode.billType(),
            )

            is InvoiceReceiptData -> InvoiceReceiptTO(
                totalAmount = receiptData.totalAmount,
                dateTime = receiptData.dateTime.format(dateTimeFormat),
                recipient = billTOBuilder.convertToRecipientTo(receiptData.recipient)!!,
                purpose = receiptData.purpose,
                authentication = receiptData.authentication,
                payerName = receiptData.payer.name!!,
                payerDocument = receiptData.payer.document,
                payerDocumentType = getDocumentType(receiptData.payer.document),
                paymentPartnerName = receiptData.paymentPartnerName,
                paymentPartnerDocumentType = getDocumentType(receiptData.paymentPartnerDocument),
                paymentPartnerDocument = receiptData.paymentPartnerDocument,
                payerBankNumber = receiptData.payerBankAccount?.bankNo,
                payerRoutingNumber = receiptData.payerBankAccount?.routingNo,
                payerAccountNumber = receiptData.payerBankAccount?.accountNo,
                payerAccountDv = receiptData.payerBankAccount?.accountDv,
            )

            is PixReceiptData -> PixReceiptTO(
                totalAmount = receiptData.totalAmount,
                dateTime = receiptData.dateTime.format(dateTimeFormat),
                recipient = billTOBuilder.convertToRecipientTo(receiptData.recipient)!!,
                purpose = receiptData.purpose,
                payerName = receiptData.payer.name!!,
                payerDocument = receiptData.payer.document,
                payerDocumentType = getDocumentType(receiptData.payer.document),
                payerBankNumber = receiptData.payerBankAccount.bankNo,
                payerRoutingNumber = receiptData.payerBankAccount.routingNo,
                payerAccountNumber = receiptData.payerBankAccount.accountNo,
                payerAccountDv = receiptData.payerBankAccount.accountDv,
                transactionId = receiptData.authentication,
            )

            is AutomaticPixReceiptData -> AutomaticPixReceiptTO(
                totalAmount = receiptData.totalAmount,
                dateTime = receiptData.dateTime.format(dateTimeFormat),
                recipient = billTOBuilder.convertToRecipientTo(receiptData.pixReceiptData.recipient)!!,
                purpose = receiptData.pixReceiptData.purpose,
                payerName = receiptData.pixReceiptData.payer.name!!,
                payerDocument = receiptData.pixReceiptData.payer.document,
                payerDocumentType = getDocumentType(receiptData.pixReceiptData.payer.document),
                payerBankNumber = receiptData.pixReceiptData.payerBankAccount.bankNo,
                payerRoutingNumber = receiptData.pixReceiptData.payerBankAccount.routingNo,
                payerAccountNumber = receiptData.pixReceiptData.payerBankAccount.accountNo,
                payerAccountDv = receiptData.pixReceiptData.payerBankAccount.accountDv,
                transactionId = receiptData.pixReceiptData.authentication,
                contractNumber = receiptData.contractNumber,
                automaticPixDescription = receiptData.automaticPixDescription,
            )

            is InvestmentReceiptData -> InvestmentReceiptTO(
                totalAmount = receiptData.totalAmount,
                dateTime = receiptData.dateTime.format(dateTimeFormat),
                payerName = receiptData.payer.name!!,
                payerDocument = receiptData.payer.document,
                payerBankNumber = receiptData.payerBankAccount.bankNo,
                payerRoutingNumber = receiptData.payerBankAccount.routingNo,
                payerAccountNumber = receiptData.payerBankAccount.accountNo,
                payerAccountDv = receiptData.payerBankAccount.accountDv,
                goalName = receiptData.assignor,
                productProvider = receiptData.productProvider,
                productName = receiptData.productName,
                productIndex = receiptData.productIndex,
                productIndexPercentage = receiptData.productIndexPercentage,
                productInterestRateLabel = receiptData.productInterestRateLabel,
                positionId = receiptData.positionId,
                maturityDate = receiptData.maturityDate.format(dateFormat),
            )
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ReceiptController::class.java)
    }
}

private fun BarCode.billType() = when (digitable.length) {
    FICHA_DIGITABLE_LINE_SIZE -> BillType.FICHA_COMPENSACAO
    CONCESSIONARIA_DIGITABLE_LINE_SIZE -> BillType.CONCESSIONARIA
    else -> BillType.OTHERS
}

data class ReceiptFilesTO(
    val imageUrl: String?,
    val pdfUrl: String?,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
interface ReceiptTO {
    val billType: BillType
}

data class BoletoReceiptTO(
    val totalAmount: Long,
    val dateTime: String,
    val recipient: ResponseRecipientTO?,
    val assignorName: String,
    val payerName: String?,
    val payerDocument: String?,
    val payerDocumentType: String,
    val typeableLine: String,
    val authentication: String,
    val dueDate: String,
    override val billType: BillType,
) : ReceiptTO

data class PixReceiptTO(
    val totalAmount: Long,
    val dateTime: String,
    val recipient: ResponseRecipientTO,
    val purpose: String,
    val payerName: String,
    val payerDocument: String,
    val payerDocumentType: String,
    val payerBankNumber: Long,
    val payerRoutingNumber: Long,
    val payerAccountNumber: Long,
    val payerAccountDv: String,
    val transactionId: String,
    override val billType: BillType = BillType.PIX,
) : ReceiptTO

data class AutomaticPixReceiptTO(
    val totalAmount: Long,
    val dateTime: String,
    val recipient: ResponseRecipientTO,
    val purpose: String,
    val payerName: String,
    val payerDocument: String,
    val payerDocumentType: String,
    val payerBankNumber: Long,
    val payerRoutingNumber: Long,
    val payerAccountNumber: Long,
    val payerAccountDv: String,
    val transactionId: String,
    val contractNumber: String?,
    val automaticPixDescription: String?,
    override val billType: BillType = BillType.AUTOMATIC_PIX,
) : ReceiptTO

data class InvoiceReceiptTO(
    val totalAmount: Long,
    val dateTime: String,
    val recipient: ResponseRecipientTO,
    val purpose: String,
    val authentication: String,
    val payerName: String,
    val payerDocument: String,
    val payerDocumentType: String,
    val payerBankNumber: Long?,
    val payerRoutingNumber: Long?,
    val payerAccountNumber: Long?,
    val payerAccountDv: String?,
    val paymentPartnerName: String?,
    val paymentPartnerDocumentType: String,
    val paymentPartnerDocument: String?,
    override val billType: BillType = BillType.INVOICE,
) : ReceiptTO

data class InvestmentReceiptTO(
    val totalAmount: Long,
    val dateTime: String,
    val payerName: String,
    val payerDocument: String,
    val payerBankNumber: Long?,
    val payerRoutingNumber: Long?,
    val payerAccountNumber: Long?,
    val payerAccountDv: String?,
    val goalName: String,
    val productProvider: String,
    val productName: String,
    val productIndex: String,
    val productIndexPercentage: Int,
    val productInterestRateLabel: String,
    val positionId: String,
    val maturityDate: String,
    override val billType: BillType = BillType.INVESTMENT,
) : ReceiptTO