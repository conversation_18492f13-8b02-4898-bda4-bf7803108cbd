package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.messaging.LicensePlateReceivedRequest
import ai.friday.billpayment.adapters.messaging.SNSEventPublisher
import ai.friday.billpayment.adapters.messaging.sns.TopicConfiguration
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.adapters.whatsappflow.WhatsAppFlow.DecryptionInfo
import ai.friday.billpayment.adapters.whatsappflow.WhatsAppFlowCipherProcessor
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.whatsapp.SendFlowCommand
import ai.friday.billpayment.app.whatsapp.SendFlowResult
import ai.friday.billpayment.app.whatsapp.WhatsAppService
import ai.friday.billpayment.markers
import ai.friday.billpayment.plus
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule.IS_ANONYMOUS
import jakarta.inject.Named
import java.util.Base64
import org.slf4j.LoggerFactory

@Secured(IS_ANONYMOUS)
@Controller("/whatsappFlow")
@FridayMePoupe
class WhatsAppFlowController(
    private val cipherProcessor: WhatsAppFlowCipherProcessor,
    private val whatsAppService: WhatsAppService,
    private val snsEventPublisher: SNSEventPublisher,
    @Named("whatsapp-flow") private val topic: TopicConfiguration,
) {
    private val logger = LoggerFactory.getLogger(WhatsAppFlowController::class.java)
    private val decoder = Base64.getDecoder()

    @Post("/messages")
    @Secured(Role.Code.BACKOFFICE)
    fun sendFlow(@Body request: FlowMessageRequestTO): HttpResponse<SendFlowResult?> {
        val command = when {
            request.accounts.isNotEmpty() -> SendFlowCommand.ByAccountId(
                templateName = request.templateName,
                accounts = request.accounts.map { AccountId(it) },
            )

            request.phones.isNotEmpty() -> SendFlowCommand.ByPhoneNumber(
                templateName = request.templateName,
                phoneNumbers = request.phones,
            )

            else -> {
                return HttpResponse.badRequest(SendFlowResult(errors = listOf("No accounts or phones provided")))
            }
        }

        val result = whatsAppService.sendFlow(command)

        val markers = markers(
            "commandType" to command::class.java,
            "numberOfContacts" to request.phones.size,
            "numberOfAccounts" to request.accounts.size,
            "success" to result.isSuccess,
            "templateName" to request.templateName,
        )

        val flowResponse = result.getOrElse {
            logger.error(markers, "WhatsAppFlowController#sendFlow", it)
            return HttpResponse.serverError()
        }

        markers.plus(
            "successes" to flowResponse.successes.size,
            "errors" to flowResponse.errors.size,
        )

        logger.info(markers, "WhatsAppFlowController#sendFlow")

        return HttpResponse.ok(flowResponse)
    }

    @Post("/{flowName}")
    fun flowCallbackController(
        @Body body: String,
        @PathVariable flowName: String,
    ): HttpResponse<*> {
        val markers = markers("flowName" to flowName, "request" to body)

        runCatching {
            val map = parseObjectFrom<WhatsAppFlowRequestTO>(body)

            markers.plus("parsedRequest" to map)

            // TODO: move all these decryption steps to a service
            val encryptedAesKey = decoder.decode(map.encryptedAesKey)
            val encryptedFlowData = decoder.decode(map.encryptedFlowData)
            val initialVector = decoder.decode(map.initialVector)

            val decrypted: DecryptionInfo = cipherProcessor.decrypt(encryptedAesKey, encryptedFlowData, initialVector)
            val payload = decrypted.clearPayload

            markers.plus("payload" to payload)

            val clearResponse = when {
                "ping" in payload -> """{"data":{"status":"active"}}"""
                "INIT" in payload -> """{"screen":"HOME", "data":{}}"""

                "data_exchange" in payload -> {
                    val parsedPayload = parseObjectFrom<WhatsAppFlowCallbackTO>(payload)
                    val nextScreen = parsedPayload.data["next_screen"] ?: "SUCCESS"

                    val request = LicensePlateReceivedRequest(
                        id = parsedPayload.flowToken,
                        licensePlate = parsedPayload.data["license_plate"] as String,
                    )

                    markers.plus("licensePlate" to request.licensePlate)

                    snsEventPublisher.publish(
                        topic = topic.name,
                        message = request,
                        attributes = mapOf(
                            "eventType" to flowName,
                        ),
                    )

                    """{"screen": "$nextScreen", "data": { "extension_message_response": { "params": { "flow_token": "${parsedPayload.flowToken}" } } }}"""
                }

                else -> {
                    logger.error(markers.plus("error_message" to "Invalid Payload"), "WhatsAppFlowController#flowCallbackController")
                    return HttpResponse.serverError("Error processing request")
                }
            }

            val response = cipherProcessor.encrypt(clearResponse, decrypted.clearAesKey, flipIv(initialVector))

            logger.info(
                markers.plus("response" to clearResponse, "decrypted" to decrypted),
                "WhatsAppFlowController#flowCallbackController",
            )

            return HttpResponse.ok(response)
        }.getOrElse {
            logger.error(markers, "WhatsAppFlowController", it)
            return HttpResponse.serverError("Error processing request")
        }
    }

    private fun flipIv(iv: ByteArray): ByteArray {
        val result = ByteArray(iv.size)
        for (i in iv.indices) {
            result[i] = (iv[i].toInt() xor 0xFF).toByte()
        }
        return result
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class WhatsAppFlowRequestTO(
    val encryptedFlowData: String,
    val encryptedAesKey: String,
    val initialVector: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class WhatsAppFlowCallbackTO(
    val action: String,
    val data: Map<String, Any>,
    val flowToken: String,
    val version: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class FlowMessageRequestTO(
    val templateName: String,
    val accounts: List<String> = emptyList(),
    val phones: List<String> = emptyList(),
)