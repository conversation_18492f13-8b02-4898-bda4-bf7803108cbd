package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.api.builders.BillTOBuilder
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.banking.DefaultBankStatementItem
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillImageProvider
import ai.friday.billpayment.app.bill.ListBillsToTimeline
import ai.friday.billpayment.app.integrations.FindBillsCriteria
import ai.friday.billpayment.app.integrations.SweepingAccountServiceInterface
import ai.friday.billpayment.app.integrations.SweepingCashInStatus
import ai.friday.billpayment.app.manualentry.ManualEntry
import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.sanitizeDocumentNumber
import ai.friday.billpayment.app.statement.StatementError
import ai.friday.billpayment.app.statement.StatementService
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.measure
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.javascriptDateTimeFormat
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonTypeInfo
import io.micronaut.context.annotation.Secondary
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.QueryValue
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import jakarta.inject.Singleton
import java.time.format.DateTimeFormatter
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import parallel
import parallelMap

@Secured(Role.Code.OWNER)
@Controller("/entries")
@Version("2")
@FridayMePoupe
class EntriesController(
    private val statementService: StatementService,
    private val listBillsToTimeline: ListBillsToTimeline,
    private val walletBillCategoryService: PFMWalletCategoryService,
    private val manualEntryService: ManualEntryService,
    private val billImageProvider: BillImageProvider,
    private val bankStatementItemConverter: BankStatementItemConverter,
    private val sweepingAccountService: SweepingAccountServiceInterface,
    private val billTOBuilder: BillTOBuilder,
) {
    private val logger = LoggerFactory.getLogger(EntriesController::class.java)

    @Get
    fun tryGetEntries(authentication: Authentication): HttpResponse<*> {
        val accountId = AccountId(authentication.name)
        val logName = "EntriesController#getEntries"
        val marker = Markers.append("accountId", accountId.value)
        return try {
            val wallet = authentication.getWallet()
            val walletMember = authentication.asWalletMember()

            val (bills, elapsedTimeBills) = measure { getBillsFromWallet(wallet, accountId) }

            marker.andAppend("duplicated", bills.filterNot { it.possibleDuplicateBills.isEmpty() }.size)
                .andAppend("elapsed_time", elapsedTimeBills)
                .andAppend("size", bills.size)

            val (deposits, elapsedTimeDeposits) = measure { statementService.findAllCredits(wallet.id, walletMember).getOrElse { return handleError(marker, it, logName) } }
            marker.andAppend("deposits_size", deposits.size).andAppend("elapsed_time_deposits", elapsedTimeDeposits)

            val (manualEntries, elapsedTimeManualEntries) = measure { getManualEntriesFromWallet(wallet, walletMember) }
            marker.andAppend("manual_entries_size", manualEntries.size).andAppend("elapsed_time_manual_entries", elapsedTimeManualEntries)

            val (lastSweepingCashIns, elapsedTimeLastSweepingCashIns) = measure { getLastSweepingCashIns(wallet) }
            marker.andAppend("last_sweeping_cash_ins_size", lastSweepingCashIns.size).andAppend("elapsed_time_last_sweeping_cash_ins", elapsedTimeLastSweepingCashIns)

            logger.info(marker, logName)

            val response = EntriesTO(
                credits = deposits.toCreditTO(),
                bills = bills,
                manualEntries = manualEntries,
                lastSweepingCashIns = lastSweepingCashIns,
            )
            HttpResponse.ok(response)
        } catch (e: Exception) {
            return handleError(marker, e, logName)
        }
    }

    private fun handleError(markers: LogstashMarker, e: Exception, message: String): HttpResponse<*> {
        val httpStatus = when (e) {
            is StatementError.NotAllowed -> HttpStatus.METHOD_NOT_ALLOWED

            is StatementError.ServerError,
            is StatementError.WalletNotFound,
            is StatementError.FailedToSendEmail,
            -> HttpStatus.INTERNAL_SERVER_ERROR

            else -> HttpStatus.INTERNAL_SERVER_ERROR
        }

        markers.andAppend("httpStatus", httpStatus.code).also {
            logger.error(it, message, e)
        }

        return HttpResponse.status<Unit>(httpStatus)
    }

    private fun getBillsFromWallet(
        currentWallet: Wallet,
        accountId: AccountId,
    ): List<BillTO> {
        val bills = listBillsToTimeline.findBills(accountId = accountId, currentWalletId = currentWallet.id)
        val member = currentWallet.getActiveMember(accountId)
        val categories = walletBillCategoryService.findWalletCategories(currentWallet.id)
            .associateBy(keySelector = { it.categoryId }, valueTransform = { it.toBillCategoryTO() })

        return runBlocking {
            bills.parallelMap {
                if (it.bill.billType.isBoleto() && it.bill.assignor == null && it.bill.recipient == null) {
                    logger.warn(Markers.append("billId", it.bill.billId), "EntriesController#invalid_recipient")
                }
                val imageUrl = billImageProvider.getBillImageUrl(it.bill)
                billTOBuilder.mapFrom(it, member, categories, imageUrl = imageUrl)
            }
        }
    }

    private fun getManualEntriesFromWallet(
        currentWallet: Wallet,
        member: Member,
    ): List<ManualEntryTO> {
        val manualEntries = manualEntryService.listAllForWalletMember(currentWallet.id, member)
        val categories = walletBillCategoryService.findWalletCategories(currentWallet.id)
        return runBlocking {
            manualEntries.parallelMap { it.buildTO(categories) }
        }
    }

    private fun getLastSweepingCashIns(
        currentWallet: Wallet,
    ): List<SweepingCashInTO> {
        return sweepingAccountService.getLastActiveCashIns(currentWallet.id).map {
            SweepingCashInTO(
                id = it.id.value,
                participantId = it.participant.id,
                participantName = it.participant.name,
                amount = it.amount,
                status = it.status,
                updatedAt = it.updatedAt.format(javascriptDateTimeFormat),
            )
        }
    }

    private fun List<DefaultBankStatementItem>.toCreditTO(): List<TimelineCreditTO> {
        return this.map {
            bankStatementItemConverter.convert(it)
        }
    }
}

interface BankStatementItemConverter {
    fun convert(item: DefaultBankStatementItem): TimelineCreditTO
}

@Singleton
@Secondary
class DefaultBankStatementItemConverter : BankStatementItemConverter {
    override fun convert(item: DefaultBankStatementItem): TimelineCreditTO {
        return DefaultCreditTO(
            counterPartName = item.counterpartName,
            counterPartDocument = item.counterpartDocument.sanitizeDocumentNumber(),
            amount = item.amount,
            date = item.date.format(DateTimeFormatter.ISO_LOCAL_DATE),
        )
    }
}

@Secured(Role.Code.OWNER)
@Controller("/async-entries")
@Version("2")
@FridayMePoupe
class AsyncEntriesController(
    private val statementService: StatementService,
    private val listBillsToTimeline: ListBillsToTimeline,
    private val walletBillCategoryService: PFMWalletCategoryService,
    private val manualEntryService: ManualEntryService,
    private val billImageProvider: BillImageProvider,
    private val bankStatementItemConverter: BankStatementItemConverter,
    private val sweepingAccountService: SweepingAccountServiceInterface,
    private val billTOBuilder: BillTOBuilder,
) {
    private val logger = LoggerFactory.getLogger(AsyncEntriesController::class.java)

    @Get
    suspend fun tryGetEntries(authentication: Authentication, @QueryValue from: String?, @QueryValue to: String?): HttpResponse<*> = coroutineScope {
        val accountId = AccountId(authentication.name)
        val logName = "AsyncEntriesController#getEntries"
        val marker = Markers.append("accountId", accountId.value)

        try {
            val criteria = FindBillsCriteria.create(from = from, to = to)
            val wallet = authentication.getWallet()
            val walletMember = authentication.asWalletMember()

            val billsDeferred = async { measure { getBillsFromWallet(wallet, accountId, criteria) } }
            val depositsDeferred = async { measure { statementService.findAllCredits(wallet.id, walletMember).getOrElse { throw it } } }
            val manualEntriesDeferred = async { measure { getManualEntriesFromWallet(wallet, walletMember) } }
            val lastSweepingCashInsDeferred = async { measure { getLastSweepingCashIns(wallet) } }

            val (bills, elapsedTimeBills) = billsDeferred.await()

            marker.andAppend("duplicated", bills.filterNot { it.possibleDuplicateBills.isEmpty() }.size)
                .andAppend("elapsed_time", elapsedTimeBills)
                .andAppend("size", bills.size)

            val (deposits, elapsedTimeDeposits) = depositsDeferred.await()

            marker.andAppend("deposits_size", deposits.size).andAppend("elapsed_time_deposits", elapsedTimeDeposits)

            val (manualEntries, elapsedTimeManualEntries) = manualEntriesDeferred.await()

            marker.andAppend("manual_entries_size", manualEntries.size).andAppend("elapsed_time_manual_entries", elapsedTimeManualEntries)

            val (lastSweepingCashIns, elapsedTimeLastSweepingCashIns) = lastSweepingCashInsDeferred.await()

            marker.andAppend("last_sweeping_cash_ins_size", lastSweepingCashIns.size).andAppend("elapsed_time_last_sweeping_cash_ins", elapsedTimeLastSweepingCashIns)

            logger.info(marker, logName)

            HttpResponse.ok(
                EntriesTO(
                    credits = deposits.toCreditTO(),
                    bills = bills,
                    manualEntries = manualEntries,
                    lastSweepingCashIns = lastSweepingCashIns,
                ),
            )
        } catch (e: Exception) {
            handleError(marker, e, logName)
        }
    }

    private fun handleError(markers: LogstashMarker, e: Exception, message: String): HttpResponse<*> {
        val httpStatus = when (e) {
            is StatementError.NotAllowed -> HttpStatus.METHOD_NOT_ALLOWED

            is StatementError.ServerError,
            is StatementError.WalletNotFound,
            is StatementError.FailedToSendEmail,
            -> HttpStatus.INTERNAL_SERVER_ERROR

            else -> HttpStatus.INTERNAL_SERVER_ERROR
        }

        markers.andAppend("httpStatus", httpStatus.code).also {
            logger.error(it, message, e)
        }

        return HttpResponse.status<Unit>(httpStatus)
    }

    private suspend fun getBillsFromWallet(
        currentWallet: Wallet,
        accountId: AccountId,
        criteria: FindBillsCriteria,
    ): List<BillTO> {
        val bills = listBillsToTimeline.findBills(accountId = accountId, currentWalletId = currentWallet.id, criteria)
        val member = currentWallet.getActiveMember(accountId)
        val categories = walletBillCategoryService.findWalletCategories(currentWallet.id)
            .associateBy(keySelector = { it.categoryId }, valueTransform = { it.toBillCategoryTO() })

        return bills.parallel {
            if (it.bill.billType.isBoleto() && it.bill.assignor == null && it.bill.recipient == null) {
                logger.warn(Markers.append("billId", it.bill.billId), "EntriesController#invalid_recipient")
            }
            val imageUrl = billImageProvider.getBillImageUrl(it.bill)
            billTOBuilder.mapFrom(it, member, categories, imageUrl = imageUrl)
        }
    }

    private suspend fun getManualEntriesFromWallet(
        currentWallet: Wallet,
        member: Member,
    ): List<ManualEntryTO> {
        val manualEntries = manualEntryService.listAllForWalletMember(currentWallet.id, member)
        val categories = walletBillCategoryService.findWalletCategories(currentWallet.id)
        return manualEntries.parallel { it.buildTO(categories) }
    }

    private fun getLastSweepingCashIns(
        currentWallet: Wallet,
    ): List<SweepingCashInTO> {
        return sweepingAccountService.getLastActiveCashIns(currentWallet.id).map {
            SweepingCashInTO(
                id = it.id.value,
                participantId = it.participant.id,
                participantName = it.participant.name,
                amount = it.amount,
                status = it.status,
                updatedAt = it.updatedAt.format(javascriptDateTimeFormat),
            )
        }
    }

    private fun List<DefaultBankStatementItem>.toCreditTO(): List<TimelineCreditTO> {
        return this.map {
            bankStatementItemConverter.convert(it)
        }
    }
}

@JsonInclude(JsonInclude.Include.ALWAYS)
data class DefaultCreditTO(
    override val counterPartName: String,
    override val counterPartDocument: String,
    override val amount: Long,
    override val date: String,
) : TimelineCreditTO {
    override val timelineEntryType = "SETTLEMENT_CREDIT_ENTRY"
}

@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
interface TimelineCreditTO : TimelineIncomeEntryTO {
    val counterPartName: String
    val counterPartDocument: String
}

@JsonInclude(JsonInclude.Include.ALWAYS)
data class EntriesTO(
    val credits: List<TimelineCreditTO>,
    val bills: List<BillTO>,
    val manualEntries: List<ManualEntryTO>,
    val lastSweepingCashIns: List<SweepingCashInTO>,
)

data class SweepingCashInTO(
    val id: String,
    val participantId: String,
    val participantName: String,
    val amount: Long,
    val status: SweepingCashInStatus,
    val updatedAt: String,
)

interface ManualEntryTO : TimelineEntryTO {
    val id: String
    val type: String
    val status: String
    val title: String
    val description: String?
    val createdAt: String
    val category: BillCategoryTO?
    val categorySuggestions: List<BillCategoryTO?>
    val recurrence: RecurrenceResponseTO?
    val source: BillSourceTO?
}

fun ManualEntry.buildTO(categories: List<WalletBillCategory>): ManualEntryTO {
    val category = this.categoryId?.let { categoryId -> categories.find { walletBillCategory -> walletBillCategory.categoryId == categoryId } }?.toBillCategoryTO()
    val categorySuggestions = this.categorySuggestions.map {
        categories.find { walletBillCategory -> walletBillCategory.categoryId == it.categoryId }?.toBillCategoryTO()
    }

    val recurrence = recurrenceRule?.let {
        val recurrenceId = when (val source = this.source) {
            is ActionSource.Recurrence -> source.recurrenceId.value
            is ActionSource.WalletRecurrence -> source.recurrenceId.value
            is ActionSource.SubscriptionRecurrence -> source.recurrenceId.value
            else -> throw IllegalStateException("action source should not be $source")
        }
        RecurrenceResponseTO(
            id = recurrenceId,
            frequency = it.frequency.name,
            startDate = it.startDate.format(dateFormat),
            pattern = it.pattern,
            endDate = it.endDate?.format(dateFormat),
            occurrence = it.calculateOccurrence(dueDate),
        )
    }

    if (this.isIncome()) {
        return ManualEntryIncomeTO(
            id = this.id.value,
            amount = this.amount,
            date = this.dueDate.format(dateFormat),
            status = this.status.name,
            type = this.type.name,
            title = this.title,
            description = this.description,
            createdAt = this.createdAt.format(dateFormat),
            category = category,
            categorySuggestions = categorySuggestions,
            recurrence = recurrence,
            source = this.source.toBillSourceTO(),
        )
    } else {
        return ManualEntryExpenseTO(
            id = this.id.value,
            amount = this.amount,
            dueDate = this.dueDate.format(dateFormat),
            status = this.status.name,
            type = this.type.name,
            title = this.title,
            description = this.description,
            createdAt = this.createdAt.format(dateFormat),
            category = category,
            categorySuggestions = categorySuggestions,
            recurrence = recurrence,
            overdue = this.isOverdue,
            source = this.source.toBillSourceTO(),
        )
    }
}

data class ManualEntryExpenseTO(
    override val id: String,
    override val type: String,
    override val status: String,
    override val dueDate: String,
    override val amount: Long,
    override val title: String,
    override val description: String?,
    override val createdAt: String,
    override val category: BillCategoryTO?,
    override val categorySuggestions: List<BillCategoryTO?>,
    override val recurrence: RecurrenceResponseTO?,
    override val source: BillSourceTO?,
    val overdue: Boolean,
) : ManualEntryTO, TimelineExpenseEntryTO {
    override val timelineEntryType = "MANUAL_ENTRY_EXPENSE"
}

data class ManualEntryIncomeTO(
    override val id: String,
    override val type: String,
    override val status: String,
    override val date: String,
    override val amount: Long,
    override val title: String,
    override val description: String?,
    override val createdAt: String,
    override val category: BillCategoryTO?,
    override val categorySuggestions: List<BillCategoryTO?>,
    override val recurrence: RecurrenceResponseTO?,
    override val source: BillSourceTO?,
) : ManualEntryTO, TimelineIncomeEntryTO {
    override val timelineEntryType = "MANUAL_ENTRY_INCOME"
}