package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.messaging.settlement.SettlementClientResponseTO
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.integrations.MessagePublisher
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory

@Singleton
@Secured(Role.Code.FRIDAY_CALLBACK)
@Controller("/settlement")
class SettlementResponseController(
    private val messagePublisher: MessagePublisher,
    private val configuration: SQSMessageHandlerConfiguration,
) {
    private val logger = LoggerFactory.getLogger(SettlementResponseController::class.java)

    @Post("/response")
    fun handleSettlementResponse(@Body settlementResponse: SettlementClientResponseTO): HttpResponse<Any> {
        logger.info("Received settlement response: $settlementResponse")

        try {
            messagePublisher.sendMessage(
                queueName = configuration.settlementResponseQueueName,
                body = settlementResponse,
            )

            logger.info("Settlement response sent to queue successfully")
            return HttpResponse.ok()
        } catch (e: Exception) {
            logger.error("Error sending settlement response to queue", e)
            return HttpResponse.serverError()
        }
    }
}