package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.chatbot.ChatbotNotificationResult
import ai.friday.billpayment.app.chatbot.ChatbotNotificationService
import ai.friday.billpayment.app.chatbot.ChatbotNotificationSpec
import ai.friday.billpayment.app.inappsubscription.GrantGratuityResult
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionService
import ai.friday.billpayment.app.notification.NotificationMedia
import ai.friday.billpayment.app.notification.PushNotificationContent
import ai.friday.billpayment.app.notification.PushNotificationService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import jakarta.inject.Singleton
import java.net.URI
import java.time.Duration
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(Role.Code.INTERCOM_CALLBACK)
@Controller("/webhooks/intercom")
@Singleton
class IntercomCallbackController(
    private val pushNotificationService: PushNotificationService,
    private val subscriptionService: InAppSubscriptionService,
    private val chatbotNotificationService: ChatbotNotificationService,
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    @Post("/gratuity")
    fun postGratuity(@Body body: String): HttpResponse<*> {
        val logName = "IntercomCallbackController#postGratuity"
        val request = try {
            GratuityRequest.from(parseObjectFrom<GratuityRequestTO>(body))
        } catch (e: Exception) {
            logger.error(append("body", body), logName, e)
            return HttpResponse.badRequest<Unit>()
        }

        val markers = append("request", request)

        return subscriptionService.grantGratuity(request.accountId, request.duration, request.deduplicationId)
            .fold(
                onFailure = {
                    HttpResponse.serverError<Unit>()
                        .also { logger.error(markers.andAppend("result", it), "$logName#Failed") }
                },
                onSuccess = { result ->
                    markers.andAppend("result", result)

                    when (result) {
                        is GrantGratuityResult.ValidAccountNotFound,
                        is GrantGratuityResult.ActiveStoreSubscription,
                        is GrantGratuityResult.NotInAppSubscription,
                        is GrantGratuityResult.DuplicatedGrantFound,
                        ->
                            HttpResponse.badRequest<Unit>()
                                .also { logger.error(markers, logName) }

                        is GrantGratuityResult.Granted -> {
                            HttpResponse.noContent<Unit>()
                                .also { logger.info(markers, logName) }
                        }
                    }
                },
            )
    }

    @Post("/notification")
    fun sendNotification(@Body body: IntercomNotificationTO): HttpResponse<*> {
        val logName = "IntercomCallbackController#sendNotification"
        val markers = append("request", body).andAppend("accountId", body.accountId)

        return when (val result = chatbotNotificationService.sendNotification(AccountId(body.accountId), body.toSpec())) {
            is ChatbotNotificationResult.Success -> {
                logger.info(markers.andAppend("result", "Success"), logName)
                HttpResponse.ok<Unit>()
            }

            is ChatbotNotificationResult.NotFound -> {
                logger.warn(markers.andAppend("result", "NotFound"), logName)
                HttpResponse.notFound<Unit>()
            }

            is ChatbotNotificationResult.Error -> {
                logger.error(markers.andAppend("result", "Error").andAppend("errorMessage", result.message), logName)
                HttpResponse.serverError<Unit>()
            }
        }
    }

    @Post("/push-notification")
    fun sendPushNotification(@Body body: PushNotificationTO): HttpResponse<*> {
        val logName = "IntercomCallbackController#sendPushNotification"
        val markers = append("request", body).andAppend("accountId", body.accountId)

        return runCatching {
            val accountId = AccountId(body.accountId)
            val notification = body.toPushNotificationContent()

            pushNotificationService.sendNotification(
                accountId = accountId,
                content = notification,
            )
        }.fold(
            onSuccess = {
                logger.info(markers.andAppend("result", it), logName)
                HttpResponse.ok(it)
            },
            onFailure = {
                logger.error(markers, logName, it)

                when (it) {
                    is IllegalArgumentException -> HttpResponse.badRequest<Unit>()
                    else -> HttpResponse.serverError(it)
                }
            },
        )
    }
}

data class GratuityRequestTO(
    val accountId: String,
    val durationInDays: Int,
    val deduplicationId: String,
)

data class GratuityRequest(
    val accountId: AccountId,
    val duration: Duration,
    val deduplicationId: String,
) {
    companion object {
        fun from(request: GratuityRequestTO): GratuityRequest {
            if (request.accountId.isEmpty()) {
                throw Exception("Account ID cannot be null or empty")
            }

            if (request.durationInDays <= 0 || request.durationInDays > 90) {
                throw Exception("Duration must be between 1 and 90 days")
            }

            if (request.deduplicationId.isEmpty()) {
                throw Exception("Deduplication cannot be null or empty")
            }

            return GratuityRequest(
                accountId = AccountId(request.accountId),
                duration = Duration.ofDays(request.durationInDays.toLong()),
                deduplicationId = request.deduplicationId,
            )
        }
    }
}

data class PushNotificationTO(
    val accountId: String,
    val title: String,
    val body: String,
    val url: String,
    val imageUrl: String?,
) {
    fun toPushNotificationContent(): PushNotificationContent {
        if (accountId.isEmpty()) {
            throw IllegalArgumentException("Account ID is not valid")
        }

        if (title.isEmpty()) {
            throw IllegalArgumentException("Title cannot be empty")
        }

        if (body.isEmpty()) {
            throw IllegalArgumentException("Body cannot be empty")
        }

        return PushNotificationContent(
            title = title,
            body = body,
            url = URI.create(url),
            imageUrl = imageUrl?.let { URI.create(it) },
        )
    }
}

data class IntercomNotificationTO(
    val accountId: String,
    val template: String,
    val configurationKey: String? = null,
    val chatbotHistoryMessage: String,
    val params: String = "",
    val ctaUrl: String? = null,
    val quickReplyActions: String = "",
    val quickRepliesStartIndex: String? = null,
    val imageUrl: String? = null,
    val videoUrl: String? = null,
    val sendEvent: String? = null,
    val clickEvent: String? = null,
) {
    fun toSpec() = ChatbotNotificationSpec(
        template = template,
        configurationKey = configurationKey ?: template,
        historyMessage = chatbotHistoryMessage,
        parameters = params.split(",").filter { it.isNotBlank() },
        buttonWhatsAppParameter = ctaUrl,
        quickReplyButtonsWhatsAppParameter = quickReplyActions
            .split(",")
            .filter { it.isNotBlank() }
            .map { """{"payload": "${getLocalDate().format(dateFormat)}", "action": "$it"}""" },
        quickRepliesStartIndex = quickRepliesStartIndex?.toInt(),
        media = videoUrl?.let { NotificationMedia.Video(it, null) } ?: imageUrl?.let { NotificationMedia.Image(it, null, null, null) },
        sendEvent = sendEvent,
        clickEvent = clickEvent,
    )
}