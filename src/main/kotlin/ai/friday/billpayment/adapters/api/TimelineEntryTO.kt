package ai.friday.billpayment.adapters.api

import com.fasterxml.jackson.annotation.JsonInclude

interface TimelineEntryTO {
    val timelineEntryType: String
    val amount: Long
}

interface TimelineExpenseEntryTO : TimelineEntryTO {
    val dueDate: String
}

interface TimelineIncomeEntryTO : TimelineEntryTO {
    val date: String
}

@JsonInclude(JsonInclude.Include.ALWAYS)
data class TimelineEntriesTO(
    val entries: List<TimelineEntryTO>,
)