package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.arbi.ArbiAccountBalanceAdapter
import ai.friday.billpayment.adapters.arbi.ArbiBankDataAdapter
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.BankAccountIncomeReportService
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.AccountStatementAdapter
import ai.friday.billpayment.markers
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import io.micronaut.http.HttpResponse
import io.micronaut.http.MediaType
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.server.types.files.StreamedFile
import io.micronaut.security.annotation.Secured
import java.io.ByteArrayInputStream
import java.time.LocalDate
import java.time.Year
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.ADMIN)
@Controller("/backoffice")
@FridayMePoupe
class BackofficeBankAccountController(
    private val accountRepository: AccountRepository,
    private val balanceService: BalanceService,
    private val accountStatementAdapter: AccountStatementAdapter,
    private val arbiBankDataAdapter: ArbiBankDataAdapter,
    private val accountBalanceAdapter: ArbiAccountBalanceAdapter,
    private val bankAccountIncomeReportService: BankAccountIncomeReportService,
) {
    private val logger = LoggerFactory.getLogger(BackofficeBankAccountController::class.java)

    @field:Property(name = "email.notification.email")
    lateinit var from: String

    @Get("/account/{accountId}/balances")
    fun getBalances(@PathVariable accountId: String): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId)
        return try {
            val balances = accountRepository.findAccountPaymentMethodsByAccountId(
                accountId = AccountId(accountId),
            ).filter {
                it.method is InternalBankAccount
            }.associate {
                (it.method as InternalBankAccount).buildFullAccountNumber() to try {
                    balanceService.getBalanceFrom(
                        accountId = it.accountId,
                        accountPaymentMethodId = it.id,
                        accountPaymentMethodStatus = null,
                    ).amount
                } catch (e: Exception) {
                    e.message.orEmpty()
                }
            }
            markers.andAppend("balances", balances)

            logger.info(markers, "BackofficeBankAccountController#getBalances")
            HttpResponse.ok(balances)
        } catch (e: Exception) {
            logger.error(markers, "BackofficeBankAccountController#getBalances", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Get("/account/{accountId}/bankAccounts")
    fun getBankAccounts(@PathVariable accountId: String): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId)
        return try {
            val bankAccounts = accountRepository.findAccountPaymentMethodsByAccountId(
                accountId = AccountId(accountId),
            ).filter {
                it.status == AccountPaymentMethodStatus.ACTIVE && it.method is InternalBankAccount
            }.map {
                with(it.method as InternalBankAccount) {
                    mapOf(
                        "id" to it.id.value,
                        "number" to this.accountNo,
                        "dv" to this.accountDv,
                        "type" to this.accountType,
                        "mode" to this.bankAccountMode.name,
                    )
                }
            }
            markers.andAppend("balances", bankAccounts)

            logger.info(markers, "BackofficeBankAccountController#getBankAccounts")
            HttpResponse.ok(bankAccounts)
        } catch (e: Exception) {
            logger.error(markers, "BackofficeBankAccountController#getBankAccounts", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Get("/account/{accountId}/informe/{year}/download")
    fun getInformeDeRendimentosPDF(@PathVariable accountId: String, @PathVariable year: String): StreamedFile {
        val logName = "BackofficeBankAccountController#getInformeDeRendimentosPDF"
        val markers = Markers.append("accountId", accountId).andAppend("year", year)
        try {
            val user = accountRepository.findById(AccountId(accountId))
            val pdfFile = arbiBankDataAdapter.getIncomeReportPDF(document = Document(user.document), year = Year.of(year.toInt()))

            logger.info(markers, logName)
            return StreamedFile(ByteArrayInputStream(pdfFile), MediaType.APPLICATION_PDF_TYPE)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            throw e
        }
    }

    @Post("/account/{accountId}/informe/{year}/send")
    @Secured(Role.Code.BACKOFFICE)
    fun sendInformeDeRendimentosPDF(@PathVariable accountId: String, @PathVariable year: String, @Body body: SendInformeDeRendimentosPDFTO): HttpResponse<*> {
        val logName = "BackofficeBankAccountController#sendInformeDeRendimentosPDF"
        val markers = Markers.append("accountId", accountId).andAppend("year", year)
        try {
            bankAccountIncomeReportService.generateAndSendIncomeReport(AccountId(accountId), Year.of(year.toInt()), resend = body.shouldResend(), recreate = body.shouldRecreate())
            logger.info(markers, logName)
            return HttpResponse.created("")
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return HttpResponse.serverError(e.message)
        }
    }

    @Post("/informe/sendAll")
    fun sendIncomeReportForAllUsers(): HttpResponse<*> {
        val logName = "BackofficeBankAccountController#sendIncomeReportForAllUsers"
        val currentMonth = getLocalDate().monthValue
        if (currentMonth !in listOf(2, 4)) {
            return HttpResponse.badRequest("Informe de rendimentos só pode ser enviado de Fevereiro até Abril. Em outros meses o handler estará desligado!")
        }

        val totalEligibleUsers = bankAccountIncomeReportService.generateAndSendIncomeReportForAllUsers()
        logger.info(Markers.append("totalEligibleUsers", totalEligibleUsers), logName)
        return HttpResponse.ok("Um total de $totalEligibleUsers informes de rendimentos estão elegíveis para envio")
    }

    @Get("/account/{accountId}/paymentMethod/{paymentMethodId}/bankStatement/{startDate}/to/{endDate}")
    fun getBankStatement(@PathVariable accountId: String, @PathVariable paymentMethodId: String, @PathVariable startDate: String, @PathVariable endDate: String): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId)
            .andAppend("paymentMethodId", paymentMethodId)
            .andAppend("startDate", startDate)
            .andAppend("endDate", endDate)
        return try {
            val paymentMethod = accountRepository.findAccountPaymentMethodByIdAndAccountId(
                accountId = AccountId(accountId),
                accountPaymentMethodId = AccountPaymentMethodId(value = paymentMethodId),
            )

            val bankAccount = paymentMethod.method as InternalBankAccount
            markers.andAppend("bankAccount", bankAccount)

            val statement = accountStatementAdapter.getStatement(
                accountNo = bankAccount.buildFullAccountNumber(),
                document = Document(bankAccount.document),
                initialDate = LocalDate.parse(startDate, dateFormat),
                endDate = LocalDate.parse(endDate, dateFormat),
            )

            markers.andAppend("statement", statement)

            logger.info(markers, "BackofficeBankAccountController#getBankStatement")
            HttpResponse.ok(statement)
        } catch (e: Exception) {
            logger.error(markers, "BackofficeBankAccountController#getBankStatement", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Get("/account/{accountId}/balanceCompleted")
    fun getByAccountBalanceCompleted(@PathVariable accountId: String): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId)
        return try {
            val account = accountRepository.findPhysicalBankAccountByAccountId(
                accountId = AccountId(accountId),
            ).first().method as InternalBankAccount

            val response = accountBalanceAdapter.getBalance(accountNo = account.buildFullAccountNumber(), document = Document(account.document))

            logger.info(markers, "BackofficeBankAccountController#getBalanceCompleted")
            HttpResponse.ok(response)
        } catch (e: Exception) {
            logger.error(markers, "BackofficeBankAccountController#getBalanceCompleted", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Get("/document/{document}/fullAccountNumber/{fullAccountNumber}/balanceCompleted")
    fun getByDocumentAndAccountNumberBalanceCompleted(@PathVariable document: String, @PathVariable fullAccountNumber: String): HttpResponse<*> {
        val markers = Markers.append("document", document).andAppend("fullAccountNumber", fullAccountNumber)
        return try {
            val response = accountBalanceAdapter.getBalance(accountNo = fullAccountNumber, document = Document(document))
            logger.info(markers, "BackofficeBankAccountController#getBalanceCompleted")
            HttpResponse.ok(response)
        } catch (e: Exception) {
            logger.error(markers, "BackofficeBankAccountController#getBalanceCompleted", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }
}

data class SendInformeDeRendimentosPDFTO(private val resend: Boolean?, private val recreate: Boolean?, private val force: Boolean?) {
    fun shouldResend(): Boolean {
        return resend ?: force ?: false
    }

    fun shouldRecreate(): Boolean {
        return recreate ?: false
    }
}