package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.name.GetNameError
import ai.friday.billpayment.app.name.NameService
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpResponse.ok
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.validation.Validated
import jakarta.validation.constraints.Pattern
import org.slf4j.LoggerFactory

@Validated
@Secured(Role.Code.OWNER)
@Controller("/name")
@Version("2")
@FridayMePoupe
class NameController(private val service: NameService) {
    @Get("/{document}")
    fun getName(
        @PathVariable
        @Pattern(
            regexp = "^\\d{11}|\\d{14}$",
            message = "document must be 11 digits for CPF or 14 digits for CNPJ",
        )
        document: String,
    ) =
        service.getName(Document(document)).fold(
            ifLeft = handleError,
            ifRight = handleSuccess,
        )

    @Secured(Role.Code.GUEST)
    @Get
    fun getName(authentication: Authentication) =
        service.getName(authentication.toAccountId()).fold(
            ifLeft = handleError,
            ifRight = handleSuccess,
        )

    private val handleSuccess: (name: String) -> HttpResponse<*> = {
        ok(mapOf("name" to it))
    }

    private val handleError: (error: GetNameError) -> HttpResponse<*> = {
        when (it) {
            GetNameError.DocumentBelongsToAMinor -> StandardHttpResponses.badRequest(4001, "Document belongs to a minor")
            GetNameError.InvalidDocument -> StandardHttpResponses.badRequest(4002, "Invalid document")
            GetNameError.DocumentNotFound -> StandardHttpResponses.notFound("Document not found")
            is GetNameError.UnknownError -> {
                logger.error("NameController#getName", it.e)
                StandardHttpResponses.serverError("Error on fetch document")
            }
        }
    }

    private val logger = LoggerFactory.getLogger(NameController::class.java)
}