package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.cognito.CognitoAdapter
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountClosureDetails
import ai.friday.billpayment.app.account.AccountClosureReason
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.LoginRepository
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.micronaut.context.annotation.Requires
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured

@Secured(Role.Code.BACKOFFICE)
@Controller("/backoffice/staging")
@Requires(env = ["staging"])
class BackofficeStagingController(
    private val accountRegisterRepository: AccountRegisterRepository,
    private val accountRepository: AccountRepository,
    private val loginRepository: LoginRepository,
    private val cognitoAdapter: CognitoAdapter,
) {

    @Post("/backup/{document}")
    fun backupAccount(@PathVariable document: String): HttpResponse<*> {
        val account = accountRepository.findAccountByDocument(document)
        val accountRegisterData = accountRegisterRepository.findByAccountId(account.accountId)
        val msisdnLogin = loginRepository.findActiveUserLoginProvider(EmailAddress("+${account.mobilePhone}@friday.ai")).single()
        val whatsappLogin = loginRepository.findActiveUserLoginProvider(EmailAddress("${account.mobilePhone}@wa.gw.msging.net")).single()

        loginRepository.createLogin(
            providerUser = msisdnLogin.copy(
                emailAddress = EmailAddress("BKP#${msisdnLogin.emailAddress.value}"),
            ),
            id = AccountId(msisdnLogin.id),
            role = Role.OWNER,
        )

        loginRepository.createLogin(
            providerUser = whatsappLogin.copy(
                emailAddress = EmailAddress("BKP#${whatsappLogin.emailAddress.value}"),
            ),
            id = AccountId(whatsappLogin.id),
            role = Role.OWNER,
        )

        accountRegisterRepository.save(
            accountRegisterData.copy(
                document = Document("***********"),
                documentInfo = accountRegisterData.documentInfo?.copy(cpf = "BKP#${accountRegisterData.documentInfo.cpf}"),
                mobilePhone = MobilePhone(accountRegisterData.mobilePhone!!.msisdn.replace("+", "+BKP#")),
            ),
        )

        accountRepository.save(
            account.copy(
                document = "BKP#${account.document}",
                mobilePhone = "BKP#${account.mobilePhone}",
            ),
        )

        cognitoAdapter.disableUser(account.document)

        return HttpResponse.ok<Unit>()
    }

    @Post("/restore/{document}")
    fun restore(@PathVariable document: String): HttpResponse<*> {
        val testAccountRegisterData = accountRegisterRepository.findByDocumentOrNull(document)

        val account = accountRepository.findAccountByDocument("BKP#$document").let {
            it.copy(
                document = it.document.replace("BKP#", ""),
                mobilePhone = it.mobilePhone.replace("BKP#", ""),
            )
        }

        val accountRegisterData = accountRegisterRepository.findByAccountId(account.accountId).let {
            it.copy(
                document = Document(it.documentInfo!!.cpf.replace("BKP#", "")),
                documentInfo = it.documentInfo.copy(),
                mobilePhone = MobilePhone(it.mobilePhone!!.msisdn.replace("BKP#", "")),
            )
        }

        val msisdnLogin =
            loginRepository.findActiveUserLoginProvider(EmailAddress("bkp#+${account.mobilePhone}@friday.ai")).single()
        val whatsappLogin =
            loginRepository.findActiveUserLoginProvider(EmailAddress("bkp#${account.mobilePhone}@wa.gw.msging.net"))
                .single()

        // Remover dados de teste

        if (testAccountRegisterData != null) {
            loginRepository.remove(testAccountRegisterData.accountId)

            accountRepository.findByIdOrNull(testAccountRegisterData.accountId)?.let { testAccount ->
                accountRepository.updateAccountStatus(testAccountRegisterData.accountId, AccountStatus.CLOSED)
                cognitoAdapter.disableUser(testAccount.document)
            }

            accountRepository.findPartialAccountByIdOrNull(testAccountRegisterData.accountId)?.let {
                accountRepository.updatePartialAccountStatus(testAccountRegisterData.accountId, AccountStatus.CLOSED)
            }

            accountRegisterRepository.deactivate(
                testAccountRegisterData.accountId,
                AccountClosureDetails.WithReason(reason = AccountClosureReason.USER_REQUEST, at = getZonedDateTime(), description = "Teste"),
            )
        }

        // Restaurar dados do backup

        accountRepository.save(account)

        accountRegisterRepository.save(accountRegisterData)

        loginRepository.createLogin(
            providerUser = msisdnLogin.copy(
                emailAddress = EmailAddress(msisdnLogin.emailAddress.value.replace("bkp#", "")),
            ),
            id = AccountId(msisdnLogin.id),
            role = Role.OWNER,
        )

        loginRepository.createLogin(
            providerUser = whatsappLogin.copy(
                emailAddress = EmailAddress(whatsappLogin.emailAddress.value.replace("bkp#", "")),
            ),
            id = AccountId(whatsappLogin.id),
            role = Role.OWNER,
        )

        cognitoAdapter.enableUser(account.document, account.accountId)

        return HttpResponse.ok<Unit>()
    }
}