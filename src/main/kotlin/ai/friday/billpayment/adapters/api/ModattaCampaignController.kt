package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.campaigns.ModattaCampaignService
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.context.annotation.Requires
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Controller("/modatta/campaign")
@Secured(Role.Code.OWNER)
@Requires(env = [FRIDAY_ENV])
@Version("2")
class ModattaCampaignController(
    private val modattaCampaignService: ModattaCampaignService,
) {

    @Get("/redeem/{code}")
    fun redeemCode(authentication: Authentication, @PathVariable code: String): HttpResponse<*> {
        val markers = Markers.append("accountId", authentication.toAccountId()).andAppend("code", code)
        val logName = "ModattaCampaignController#redeemCode"

        val result = modattaCampaignService.redeemCode(authentication.toAccountId(), code).getOrElse {
            LOG.error(markers.andAppend("error", it), logName)
            return HttpResponse.serverError<Unit>()
        }

        LOG.info(markers.andAppend("codeRedeemed", result), logName)

        return HttpResponse.ok<Unit>()
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ModattaCampaignController::class.java)
    }
}