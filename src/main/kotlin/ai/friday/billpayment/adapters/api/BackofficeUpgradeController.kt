package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.ExternalRegisterStatus
import ai.friday.billpayment.app.account.RegisterService
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.account.getCPF
import ai.friday.billpayment.app.backoffice.BackofficeRegisterService
import ai.friday.billpayment.app.backoffice.BackofficeUpgradeService
import ai.friday.billpayment.app.backoffice.UserAccount
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(BACKOFFICE)
@Controller("/backoffice/upgrade")
@FridayMePoupe
class BackofficeUpgradeController(
    private val backofficeUpgradeService: BackofficeUpgradeService,
    private val registerService: RegisterService,
    private val accountRegisterRepository: AccountRegisterRepository,
    private val backofficeRegisterService: BackofficeRegisterService,
) {
    @Get
    fun getUpgradeAccounts(): HttpResponse<*> {
        return try {
            val accounts = backofficeUpgradeService.getAll()
            HttpResponse.ok(accounts.map { it.toUserAccountTO() })
        } catch (e: Exception) {
            LOG.error("BackofficeUpgradeController#getUpgradeAccounts", e)
            return HttpResponse.serverError<Unit>()
        }
    }

    @Get("/{accountId}")
    fun getAccount(@PathVariable accountId: String): HttpResponse<*> {
        return try {
            val account = backofficeUpgradeService.get(AccountId(accountId))
            HttpResponse.ok(account.toUserAccountTO())
        } catch (e: Exception) {
            LOG.error("BackofficeUpgradeController#getAccount", e)
            return HttpResponse.serverError<Unit>()
        }
    }

    @Post("/{accountId}/submitInternalReview")
    fun submitInternalReview(@PathVariable accountId: String): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId).andAppend("action", "submitInternalReview")
        LOG.info(markers, "BackofficeUpgradeReview")

        return registerService.submitUpgradeForInternalReview(AccountId(accountId))
            .map<HttpResponse<*>> { return HttpResponse.ok<Unit>() }
            .getOrElse {
                LOG.error("BackofficeUpgradeController#submitInternalReview", it)
                return HttpResponse.serverError<Unit>()
            }
    }

    @Post("/{accountId}/internalApprove")
    fun internalApprove(@PathVariable accountId: String): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId).andAppend("action", "internalApprove")
        LOG.info(markers, "BackofficeUpgradeReview")

        return registerService.internalApproveUpgrade(AccountId(accountId))
            .map<HttpResponse<*>> { return HttpResponse.ok<Unit>() }
            .getOrElse {
                LOG.error("BackofficeUpgradeController#internalApprove", it)
                return HttpResponse.serverError<Unit>()
            }
    }

    @Post("/{accountId}/reopen")
    fun reopen(@PathVariable accountId: String): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId).andAppend("action", "reopen")

        return backofficeRegisterService.reopenUpgrade(AccountId(accountId)).map<HttpResponse<*>> {
            LOG.info(markers, "BackofficeUpgradeReview")
            return HttpResponse.ok<Unit>()
        }.getOrElse {
            LOG.error("BackofficeUpgradeController#reopen", it)
            return HttpResponse.serverError<Unit>()
        }
    }

    @Post("/{accountId}/deny")
    fun deny(@PathVariable accountId: String, @Body body: DenyUpgradeTO): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId).andAppend("action", "deny")

        return backofficeRegisterService.denyUpgrade(AccountId(accountId), body.reason).map<HttpResponse<*>> {
            LOG.info(markers, "BackofficeUpgradeReview")
            return HttpResponse.ok<Unit>()
        }.getOrElse {
            LOG.error("BackofficeUpgradeController#deny", it)
            return HttpResponse.serverError<Unit>()
        }
    }

    @Post("/{accountId}/externalApprove/accept")
    fun externalAccept(@PathVariable accountId: String): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId).andAppend("action", "externalAccept")

        val accountRegisterData = accountRegisterRepository.findByAccountId(AccountId(accountId))

        if (accountRegisterData.getCPF() == null) {
            LOG.error("BackofficeUpgradeController#externalAccept", Exception("Document was not found on user"))
        }

        return registerService.updateAccountStatus(
            accountRegisterData.getCPF()!!,
            ExternalRegisterStatus.APPROVED,
        ).map<HttpResponse<*>> {
            LOG.info(markers, "BackofficeUpgradeReview")
            return HttpResponse.ok<Unit>()
        }.getOrElse {
            LOG.error("BackofficeUpgradeController#externalAccept", it)
            return HttpResponse.serverError<Unit>()
        }
    }

    @Post("/{accountId}/externalApprove/reject")
    fun externalReject(@PathVariable accountId: String): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId).andAppend("action", "externalReject")

        val accountRegisterData = accountRegisterRepository.findByAccountId(AccountId(accountId))

        if (accountRegisterData.getCPF() == null) {
            LOG.error("BackofficeUpgradeController#externalReject", Exception("Document was not found on user"))
            return HttpResponse.serverError<Unit>()
        }

        return registerService.updateAccountStatus(
            accountRegisterData.getCPF()!!,
            ExternalRegisterStatus.REJECTED,
        ).map<HttpResponse<*>> {
            LOG.info(markers, "BackofficeUpgradeReview")
            return HttpResponse.ok<Unit>()
        }.getOrElse {
            LOG.error("BackofficeUpgradeController#externalReject", it)
            return HttpResponse.serverError<Unit>()
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BackofficeUpgradeController::class.java)
    }
}

data class UserAccountTO(
    val accountId: String,
    val name: String,
    val document: String,
    val email: String,
    val phone: String,
    val status: String,
    val upgradeStatus: String?,
    val updated: String,
)

fun UserAccount.toUserAccountTO() = UserAccountTO(
    accountId = accountId.value,
    name = name,
    document = document,
    email = email.value,
    phone = phone,
    status = status.name,
    upgradeStatus = upgradeStatus?.name,
    updated = updated.format(dateTimeFormat),
)

data class DenyUpgradeTO(
    val reason: String,
)