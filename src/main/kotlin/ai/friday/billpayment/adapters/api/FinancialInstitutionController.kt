package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.FinancialInstitutionGlobalData
import ai.friday.billpayment.app.banking.FinancialInstitution
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.cache.annotation.CacheConfig
import io.micronaut.cache.annotation.Cacheable
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule.IS_AUTHENTICATED
import io.micronaut.validation.Validated
import org.slf4j.LoggerFactory

@Secured(IS_AUTHENTICATED)
@Controller("/financialInstitution")
@Version("2")
@CacheConfig("financial-institutions")
@Validated
class FinancialInstitutionController {

    @Get
    fun getAllFinancialInstitutions(): FinancialInstitutionListTO {
        return getFinancialInstitutions()
    }

    @Cacheable
    fun getFinancialInstitutions(): FinancialInstitutionListTO {
        val exclusionsStream = Thread.currentThread().contextClassLoader.getResourceAsStream("banks/exclusions.json")
        val compeExclusionList =
            jacksonObjectMapper().readerFor(List::class.java).readValue<List<String>>(exclusionsStream).map {
                it.toLong()
            }

        val fisFromBanks = FinancialInstitutionGlobalData.bankList.filter {
            it.number !in compeExclusionList
        }.map {
            FinancialInstitution(
                name = it.name,
                ispb = null,
                compe = it.number,
            )
        }
        val fis = FinancialInstitutionGlobalData.pixParticipants

        val fisWithCode = fis.filter { it.compe != null }

        val total = (fisFromBanks + fisWithCode)
            .groupBy { it.compe }
            .map { (_, financialInstitutions) ->
                if (financialInstitutions.size == 1) {
                    FinancialInstitutionTO(
                        ispb = financialInstitutions.first().ispb,
                        name = financialInstitutions.first().name,
                        code = financialInstitutions.first().compe,
                        isPixEnabled = financialInstitutions.first().ispb != null,
                        isTedEnabled = financialInstitutions.first().compe != null,
                    )
                } else {
                    FinancialInstitutionTO(
                        ispb = financialInstitutions.first().ispb ?: financialInstitutions[1].ispb,
                        name = financialInstitutions.first().name,
                        code = financialInstitutions.first().compe ?: financialInstitutions[1].compe,
                        isPixEnabled = financialInstitutions.first().ispb != null || financialInstitutions[1].ispb != null,
                        isTedEnabled = true,
                    )
                }
            }

        val fisWithoutCode = fis.filter { it.compe == null }.map {
            FinancialInstitutionTO(
                ispb = it.ispb,
                name = it.name,
                code = null,
                isPixEnabled = true,
                isTedEnabled = false,
            )
        }

        val total4 = total.enrichBankNames()
            .enrichBankLabels()
            .enrichCommonBanks()
            .enrichShortNames()

        val fullList = (total4 + fisWithoutCode).map {
            val withName = addDefaultName(it)
            addDefaultLabel(withName)
        }

        return FinancialInstitutionListTO(fullList.sorted())
    }

    private fun List<FinancialInstitutionTO>.enrichBankNames(): List<FinancialInstitutionTO> {
        val banksStream = Thread.currentThread().contextClassLoader.getResourceAsStream("banks/banks.json")
        val bankNames =
            jacksonObjectMapper().readerFor(Map::class.java).readValue<Map<String, Map<String, String>>>(banksStream)

        return this.map { fis ->
            bankNames[fis.code.toString()]?.let {
                fis.copy(name = it["name"]!!)
            } ?: fis
        }
    }

    private fun List<FinancialInstitutionTO>.enrichBankLabels(): List<FinancialInstitutionTO> {
        val labelsStream = Thread.currentThread().contextClassLoader.getResourceAsStream("banks/labels.json")
        val bankLabels =
            jacksonObjectMapper().readerFor(Map::class.java).readValue<Map<String, List<String>>>(labelsStream)

        return this.map { fis ->
            bankLabels[fis.code.toString()]?.let {
                fis.copy(labels = it)
            } ?: fis
        }
    }

    private fun List<FinancialInstitutionTO>.enrichCommonBanks(): List<FinancialInstitutionTO> {
        val mostCommonStream = Thread.currentThread().contextClassLoader.getResourceAsStream("banks/most-common.json")
        val mostCommon = jacksonObjectMapper().readerFor(List::class.java).readValue<List<Int>>(mostCommonStream)

        return this.map {
            if (mostCommon.contains(it.code!!.toInt())) {
                it.copy(isCommon = true)
            } else {
                it
            }
        }
    }

    private fun List<FinancialInstitutionTO>.enrichShortNames(): List<FinancialInstitutionTO> {
        val shortNamesStream = Thread.currentThread().contextClassLoader.getResourceAsStream("banks/short-names.json")
        val shortNames =
            jacksonObjectMapper().readerFor(Map::class.java).readValue<Map<String, Map<String, String>>>(shortNamesStream)

        return this.map { fis ->
            shortNames[fis.code.toString()]?.let {
                fis.copy(shortLabel = it["shortName"]!!)
            } ?: fis
        }
    }

    private fun addDefaultLabel(financialInstitutionTO: FinancialInstitutionTO): FinancialInstitutionTO {
        if (!financialInstitutionTO.labels.isEmpty()) {
            return financialInstitutionTO
        }

        val mainLabel = if (financialInstitutionTO.code != null) {
            "${financialInstitutionTO.code} - ${financialInstitutionTO.name.lowercase()}"
        } else {
            financialInstitutionTO.name.lowercase()
        }

        return financialInstitutionTO.copy(labels = listOf(mainLabel))
    }

    private fun addDefaultName(fis: FinancialInstitutionTO): FinancialInstitutionTO {
        if (fis.name.isEmpty()) {
            val name = when (fis.ispb) {
                "05863726" -> "Coop Cresol Planalto Serra Sul"
                "20520298" -> "AdiQ Soluções de Pagamento S.A"
                "32343119" -> "Mêntore Instituição de Pagamento S.A."
                "34678263" -> "Kredit Instituição de Pagamento S/A"
                "44373041" -> "Cooperativa de Crédito Rural - Credicana"
                "07136847" -> "Hyper Wallet IP LTDA"
                "23197081" -> "PGWEB Instituição de Pagamento LTDA"
                "92934215" -> "Banrisul Pagamentos IP"
                else -> "Não informado"
            }
            return fis.copy(name = name)
        }
        return fis
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(FinancialInstitutionController::class.java)
    }
}

data class FinancialInstitutionListTO(val financialInstitutions: List<FinancialInstitutionTO>)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class FinancialInstitutionTO(
    val code: Long?,
    val name: String,
    val ispb: String?,
    val isPixEnabled: Boolean,
    val isTedEnabled: Boolean,
    val isCommon: Boolean = false,
    val labels: List<String> = listOf(),
    val shortLabel: String? = null,
) : Comparable<FinancialInstitutionTO> {
    override fun compareTo(other: FinancialInstitutionTO): Int {
        return when {
            this.isCommon && !other.isCommon -> -1
            !this.isCommon && other.isCommon -> 1
            else -> this.name.compareTo(other.name)
        }
    }
}