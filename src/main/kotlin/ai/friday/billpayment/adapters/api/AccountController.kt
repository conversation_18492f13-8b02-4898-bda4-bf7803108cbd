package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountClosureDetails
import ai.friday.billpayment.app.account.AccountClosureReason
import ai.friday.billpayment.app.account.AccountConfigurationName
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountPaymentStatus
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.CloseAccountError
import ai.friday.billpayment.app.account.CloseAccountService
import ai.friday.billpayment.app.account.CloseWalletError
import ai.friday.billpayment.app.account.Role.Code
import ai.friday.billpayment.app.account.Role.GUEST
import ai.friday.billpayment.app.account.UpdateAccountConfigError
import ai.friday.billpayment.app.auth.getMainRole
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.fix
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionReason
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionService
import ai.friday.billpayment.app.referrer.AccountReferrerResult
import ai.friday.billpayment.app.referrer.ReferrerRegister
import ai.friday.billpayment.app.referrer.ReferrerService
import ai.friday.billpayment.app.subscription.SubscriptionService
import ai.friday.billpayment.app.subscription.SubscriptionStatus
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.usage.CreditCardUsage
import ai.friday.billpayment.app.usage.CreditCardUsageService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonInclude
import io.micronaut.core.convert.exceptions.ConversionErrorException
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpResponseFactory
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Error
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.http.annotation.QueryValue
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import java.time.Period
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Controller("/account")
@Version("2")
class GetAccountController(
    private val accountService: AccountService,
    private val walletService: WalletService,
    private val inAppSubscription: InAppSubscriptionService,
    private val subscriptionService: SubscriptionService,
) {

    @Get("/details")
    @Secured(Code.OWNER)
    fun getAccountInfo(authentication: Authentication): HttpResponse<*> {
        val accountId = AccountId(authentication.name)
        val account = accountService.findAccountByIdOrNull(accountId) ?: return HttpResponse.notFound<Unit>()

        when (account.subscriptionType) {
            SubscriptionType.IN_APP -> {
                val subscription = inAppSubscription.getSubscription(accountId)

                val simpleAccountSubscriptionTO = subscription?.let {
                    val duration = when {
                        subscription.reason == InAppSubscriptionReason.NO_STORE_COUPON -> {
                            Period.ofMonths(1).toString() // TODO: não é usado, mas quebra para algumas versões do frontend
                        }

                        subscription.productId != null -> {
                            val product = inAppSubscription.getProduct(subscription.productId).getOrElse {
                                LOG.warn(append("accountId", accountId.value).andAppend("productId", subscription.productId.value), "GetAccountController#getAccountInfo", it)
                                return HttpResponse.serverError<Unit>()
                            }
                            product.duration.toString()
                        }

                        else -> {
                            LOG.error(
                                append("accountId", accountId.value)
                                    .andAppend("subscription", subscription),
                                "GetAccountController#getAccountInfo",
                            )
                            null
                        }
                    }

                    duration?.let {
                        SimpleAccountSubscriptionTO(
                            type = account.subscriptionType,
                            autoRenew = subscription.autoRenew ?: true,
                            endsAt = dateFormat.format(subscription.endsAt.toLocalDate()),
                            status = subscription.status.name,
                            reason = subscription.reason.toReasonTO(),
                            price = subscription.price,
                            duration = duration,
                            productId = subscription.productId?.value,
                            offStoreProductId = subscription.offStoreProductId,
                        )
                    }
                }

                return HttpResponse.ok(
                    SimpleAccountTO(
                        fullName = account.name,
                        msisdn = MobilePhone(account.mobilePhone).fix().msisdn,
                        email = account.emailAddress.value,
                        subscription = simpleAccountSubscriptionTO,
                    ),
                )
            }

            SubscriptionType.PIX -> {
                val subscription = subscriptionService.findOrNull(accountId)

                if (subscription == null || subscription.status == SubscriptionStatus.INACTIVE) {
                    return HttpResponse.ok(
                        SimpleAccountTO(
                            fullName = account.name,
                            msisdn = MobilePhone(account.mobilePhone).fix().msisdn,
                            email = account.emailAddress.value,
                            subscription = null,
                        ),
                    )
                }

                return HttpResponse.ok(
                    SimpleAccountTO(
                        fullName = account.name,
                        msisdn = MobilePhone(account.mobilePhone).fix().msisdn,
                        email = account.emailAddress.value,
                        subscription = SimpleAccountSubscriptionTO(
                            type = account.subscriptionType,
                            autoRenew = true,
                            endsAt = dateFormat.format(subscription.nextEffectiveDueDate),
                            status = subscription.status.name,
                            reason = InAppSubscriptionReason.SUBSCRIPTION.toReasonTO(),
                            price = subscription.amount,
                            duration = Period.ofMonths(1).toString(),
                        ),
                    ),
                )
            }
        }
    }

    @Get
    @Secured(Code.OWNER, Code.GUEST)
    fun tryGetAccount(authentication: Authentication): HttpResponse<*> {
        return try {
            getAccount(authentication)
        } catch (e: Exception) {
            handleError(authentication, e)
        }
    }

    @Get("/invite")
    @Secured(Code.OWNER)
    fun invite(authentication: Authentication): HttpResponse<*> {
        val account: Account = accountService.findAccountById(authentication.toAccountId())

        return walletService.listPendingInvites(account.document)
            .map { invites ->
                HttpResponse.ok(invites.map { it.toInviteResponseTO() })
            }.getOrElse {
                LOG.error(append("document", account.document), "listUserInvites", it.error)
                StandardHttpResponses.serverError(ResponseTO(code = "5000", it.error.message.orEmpty()))
            }
    }

    fun InAppSubscriptionReason.toReasonTO() = when (this) {
        InAppSubscriptionReason.TRIAL -> "TRIAL"
        InAppSubscriptionReason.SUBSCRIPTION -> "PAID"
        InAppSubscriptionReason.BACKOFFICE -> "GRATUITY"
        InAppSubscriptionReason.NO_STORE_COUPON -> "NO_STORE_COUPON"
    }

    private fun getAccount(authentication: Authentication): HttpResponse<AccountTO> {
        val accountTO = if (authentication hasMainRole GUEST) {
            val account = accountService.findPartialAccountById(authentication.toAccountId())
            val status = if (account.status in listOf(
                    AccountStatus.UNDER_EXTERNAL_REVIEW,
                    AccountStatus.APPROVED,
                )
            ) {
                AccountStatus.UNDER_REVIEW
            } else {
                account.status
            }

            AccountTO(id = authentication.name, fullName = account.name, status = status, msisdn = null, paymentStatus = AccountPaymentStatus.UpToDate, subscriptionType = account.subscriptionType)
        } else {
            accountService.findAccountById(AccountId(authentication.name)).toAccountTO()
        }

        LOG.info(
            append("accountId", authentication.name)
                .and<LogstashMarker>(append("role", authentication.getMainRole().name))
                .and<LogstashMarker>(append("account", accountTO)),
            "GetAccount",
        )
        return HttpResponse.ok(accountTO)
    }

    private fun handleError(authentication: Authentication, e: Exception): HttpResponse<ResponseTO> {
        return when (e) {
            is AccountNotFoundException -> {
                LOG.warn(
                    append("accountId", authentication.name).and<LogstashMarker>(
                        append(
                            "httpStatus",
                            HttpStatus.NOT_FOUND.code,
                        ),
                    ),
                    "GetAccount",
                    e,
                )
                StandardHttpResponses.accountNotFound()
            }

            else -> {
                LOG.error(
                    append("accountId", authentication.name).and<LogstashMarker>(
                        append(
                            "httpStatus",
                            HttpStatus.INTERNAL_SERVER_ERROR.code,
                        ),
                    ),
                    "GetAccount",
                    e,
                )
                if (e.message != null) StandardHttpResponses.serverError(e.message!!) else StandardHttpResponses.serverError()
            }
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(GetAccountController::class.java)
    }
}

@Secured(Code.OWNER, Code.GUEST)
@Controller("/account")
@FridayMePoupe
@Version("2")
class AccountController(
    private val accountService: AccountService,
    private val closeAccountService: CloseAccountService,
    private val creditCardUsageService: CreditCardUsageService,
    private val referrerService: ReferrerService,
) {
    @Delete
    @Secured(Code.OWNER)
    fun closeAccount(authentication: Authentication, @QueryValue dryRun: Boolean? = false): HttpResponse<*> {
        val logName = "AccountController#closeAccount"
        val markers = append("accountId", authentication.toAccountId()).andAppend("dryRun", dryRun)

        val result = try {
            val accountId = authentication.toAccountId()
            val closureDetails = AccountClosureDetails.create(
                reason = AccountClosureReason.USER_REQUEST,
                description = null,
            )

            if (dryRun == true) {
                closeAccountService.prepareClose(accountId, closureDetails)
            } else {
                closeAccountService.closeAccount(accountId, closureDetails, delaySeconds = 1).map { it.steps }
            }
        } catch (e: Exception) {
            LOG.error(markers, logName, e)
            return HttpResponse.serverError<Unit>()
        }

        return result.fold(
            ifLeft = {
                markers.andAppend("reason", it)

                when (it) {
                    is CloseAccountError.CloseFounderWalletError -> {
                        when (it.error) {
                            is CloseWalletError.BalanceDifferentThanZero -> {
                                LOG.info(markers, logName)
                                HttpResponse.badRequest(
                                    object {
                                        val reason = "BALANCE_NOT_ZERO"
                                        val walletId = it.error.walletId.value
                                        val amount = it.error.balance
                                        val pixKey = it.error.pixKey?.let { pixKey ->
                                            val type = pixKey.type.name
                                            val value = pixKey.value
                                        }
                                        val bankAccount = it.error.externalBankAccount?.let { bankAccount ->
                                            val document = bankAccount.document.value
                                            val bankNo = bankAccount.bankNo
                                            val ispb = bankAccount.bankISPB
                                            val accountType = bankAccount.accountType
                                            val routingNo = bankAccount.routingNo
                                            val accountNo = bankAccount.accountNo
                                            val accountDv = bankAccount.accountDv
                                        }
                                    },
                                )
                            }

                            is CloseWalletError.TransactionStillProcessing,
                            -> {
                                LOG.info(markers, logName)
                                HttpResponse.badRequest<Unit>()
                            }

                            is CloseWalletError.Unknown -> {
                                LOG.error(markers, logName)
                                HttpResponse.serverError<Unit>()
                            }
                        }
                    }

                    is CloseAccountError.InvalidAccountNameCheck, is CloseAccountError.Unknown -> {
                        LOG.error(markers, logName)
                        HttpResponse.serverError<Unit>()
                    }

                    is CloseAccountError.InvalidAccountStatus -> {
                        LOG.error(markers, logName)
                        HttpResponse.status(HttpStatus.CONFLICT)
                    }

                    is CloseAccountError.CloseAccountErrorWithMessageAndAmount -> {
                        LOG.info(markers.andAppend("closeAccountError", it), logName)
                        HttpResponse.badRequest(
                            object {
                                val reason = it.messageKey // TODO - essa mensagem deveria ser gerada aqui no controller e não no service.
                                val amount = it.messageAmount
                            },
                        )
                    }
                }
            },
            ifRight = {
                LOG.info(markers.andAppend("steps", it), logName)
                HttpResponse.accepted()
            },
        )
    }

    @Get("/usage")
    @Secured(Code.OWNER)
    fun getUsage(authentication: Authentication): HttpResponse<UsageTO> {
        val usage = creditCardUsageService.calculateCreditCardUsage(AccountId(authentication.name))
        LOG.info(
            append("accountId", authentication.name)
                .and<LogstashMarker>(append("role", authentication.getMainRole().name))
                .and(append("usage", usage)),
            "getUsage",
        )
        return HttpResponse.ok(UsageTO(usage.toCreditCardUsageTO()))
    }

    @Suppress("UNUSED_PARAMETER")
    @Error
    fun handleConversionErrorException(
        request: HttpRequest<*>,
        exception: ConversionErrorException,
    ): HttpResponse<ResponseTO> {
        return HttpResponse.badRequest(
            ResponseTO(
                message = "Invalid ${exception.argument}",
                code = "4000",
            ),
        )
    }

    @Put("/config/{configName}")
    @Secured(Code.OWNER)
    fun updateConfig(
        authentication: Authentication,
        @PathVariable configName: AccountConfigurationName,
        @Body body: AccountConfigTO,
    ): HttpResponse<*> {
        val logName = "AccountController#updateConfig"

        if (configName in listOf(
                AccountConfigurationName.ACCESS_TOKEN,
                AccountConfigurationName.REFRESH_TOKEN,
            )
        ) {
            LOG.warn(append("accountId", authentication.toAccountId().value).andAppend("configName", configName), logName)
            return StandardHttpResponses.badRequest("4002", "Cannot do this operation")
        }

        val marker = append("accountId", authentication.toAccountId().value)
            .andAppend("configName", configName)
            .andAppend("value", body.value)

        return accountService.updateAccountConfig(authentication.toAccountId(), configName, body.value).map {
            LOG.info(marker, logName)
            HttpResponse.noContent<Unit>()
        }.getOrElse {
            LOG.error(marker.andAppend("error", it.javaClass.simpleName), logName)

            when (it) {
                UpdateAccountConfigError.UserNotAllowed -> HttpResponseFactory.INSTANCE.status<Unit>(HttpStatus.FORBIDDEN)
                is UpdateAccountConfigError.WalletCannotBeSetAsDefault -> StandardHttpResponses.badRequest(
                    ResponseTO(
                        "4001",
                        "wallet ${body.value} cannot be default wallet",
                    ),
                )

                UpdateAccountConfigError.WalletNotFound -> StandardHttpResponses.notFound(
                    ResponseTO(
                        "4004",
                        "wallet ${body.value} not found",
                    ),
                )
            }
        }
    }

    @Post("/referrer")
    fun createReferrer(
        authentication: Authentication,
        @Body body: AccountReferrerTO,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val markers = append("accountId", accountId.value).andAppend("referrer", body)
        try {
            return when (referrerService.createReferrerRegister(body.toReferrerRegister(accountId))) {
                AccountReferrerResult.Success,
                AccountReferrerResult.AlreadyExists,
                -> HttpResponse.noContent<Unit>()

                AccountReferrerResult.AccountNotFound -> HttpResponse.notFound()
                AccountReferrerResult.Invalid -> HttpResponse.noContent()
            }.also {
                LOG.info(
                    markers.andAppend("httpStatus", it.status),
                    "CreateReferrer",
                )
            }
        } catch (e: Exception) {
            LOG.error(
                markers.andAppend("httpStatus", HttpStatus.INTERNAL_SERVER_ERROR),
                "CreateReferrer",
                e,
            )
            return StandardHttpResponses.serverError()
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(AccountController::class.java)
    }
}

@JsonInclude(JsonInclude.Include.ALWAYS)
data class AccountTO(
    val id: String,
    val fullName: String,
    val document: String? = null,
    val documentType: String? = null,
    val status: AccountStatus,
    val msisdn: String?,
    val paymentStatus: AccountPaymentStatus,
    val subscriptionType: SubscriptionType,
    val accountGroups: List<AccountGroup> = emptyList(),
    val sweepingAccount: Boolean = false, // FIXME nao faz sentido no Account, isso é da Wallet
)

data class AccountConfigTO(val value: String)

data class AccountReferrerTO(
    val referrer: String?,
    val referrerUrl: String?,
    val platform: String,
    val appVersion: String,
)

fun AccountReferrerTO.toReferrerRegister(accountId: AccountId) = ReferrerRegister(
    accountId = accountId,
    referrer = this.referrer,
    referrerUrl = this.referrerUrl,
    platform = this.platform,
    appVersion = this.appVersion,
)

data class UsageTO(val creditCard: CreditCardUsageTO)
data class CreditCardUsageTO(
    val quota: Long,
    val usage: Long,
    val quotaType: String = "MONTHLY", // FIXME - remover quando atualizar o front
    val cashInFee: Long,
)

fun CreditCardUsage.toCreditCardUsageTO() = CreditCardUsageTO(
    quota = this.quota,
    usage = this.usage,
    cashInFee = this.cashInFee,
)

data class CreditCardRiskUsageTO(
    val quota: Long,
    val usage: Long,
)

fun Account.toAccountTO(sweepingAccount: Boolean = false) = AccountTO(
    id = this.accountId.value,
    fullName = this.name,
    document = this.document,
    documentType = this.documentType,
    accountGroups = this.configuration.groups,
    status = this.status,
    msisdn = this.mobilePhone,
    paymentStatus = this.paymentStatus,
    sweepingAccount = sweepingAccount,
    subscriptionType = this.subscriptionType,
)

data class SimpleAccountTO(
    val fullName: String,
    val msisdn: String,
    val email: String,
    val subscription: SimpleAccountSubscriptionTO?,
)

data class SimpleAccountSubscriptionTO(
    val type: SubscriptionType,
    val autoRenew: Boolean,
    val endsAt: String,
    val status: String,
    val reason: String,
    val price: Long,
    val duration: String,
    val productId: String? = null,
    val offStoreProductId: String? = null,
)