package ai.friday.billpayment.adapters.bigdatacorp

import ai.friday.billpayment.app.stripAccents
import ai.friday.billpayment.app.stripSpacesAndPunctuation

val NORMALIZED_ORG_EMISSION_MAP = mapOf(
    "Advocacia-Geral da União" to "AGU",
    "Agência Nacional de Aviação Civil" to "ANAC",
    "Clube de Aeronáutica" to "CAER",
    "Conselho de Arquitetura e Urbanismo" to "CAU",
    "Corpo de Bombeiro Militar" to "CBM",
    "Conselho Federal Administração" to "CFA",
    "Conselho Federal de Biblioteconomia" to "CFB",
    "Conselho Federal de Biologia" to "CFBIO",
    "Conselho Federal de Biomedicina" to "CFBM",
    "Conselho Federal de Contabilidade" to "CFC",
    "Conselho Federal de Serviço Social" to "CFESS",
    "Conselho Regional de Farmácia" to "CFF",
    "Conselho Federal de Fonoaudiologia" to "CFFA",
    "Conselho Federal de Medicina" to "CFM",
    "Conselho Federal de Medicina Veterinária" to "CFMV",
    "Conselho Federal de Nutrição" to "CFN",
    "Conselho Federal de Odontologia" to "CFO",
    "Conselho Federal de Psicologia" to "CFP",
    "Conselho Regional de Química" to "CFQ",
    "Conselho Federal dos Técnicos Industriais" to "CFT",
    "Conselho Federal dos Técnicos Agrícolas" to "CFTA",
    "Coordenação Geral de Privilégios e Imunidades" to "CGPI",
    "Coordenadoria Geral de Polícia Marítima, Aeronáutica e de Fronteiras" to "CGPMAF",
    "Centro de Inteligência da Polícia Civil" to "CIPC",
    "Conselho Nacional de Imigração" to "CNIG",
    "Confederação Nacional do Transporte" to "CNT",
    "Confederação Nacional de Vigilantes & Prestadores de Serviços" to "CNTV",
    "Conselho Federal de Corretores de Imóveis" to "COFECI",
    "Conselho Federal de Economia" to "COFECON",
    "Conselho Federal de Museologia" to "COFEM",
    "Conselho Federal de Enfermagem" to "COFEN",
    "Conselho Regional de Fisioterapia e Terapia Ocupacional" to "COFFITO",
    "Comando da Aeronáutica" to "COMAER",
    "Conselho Federal de Estatística" to "CONFE",
    "Conselho Federal de Engenharia e Agronomia" to "CONFEA",
    "Conselho Federal de Educação Física" to "CONFEF",
    "Conselho Federal dos Representantes Comerciais" to "CONFERE",
    "Conselho Regional de Estatística" to "CONRE",
    "Conselho Federal de Profissionais de Relações Públicas" to "CONRERP",
    "Conselho Regional dos Representantes Comerciais" to "CORE",
    "Conselho Regional de Economia" to "CORECON",
    "Conselho Regional de Museologia" to "COREM",
    "Conselho Regional de Enfermagem" to "COREN",
    "Conselho Regional de Administração" to "CRA",
    "Centro de Referência de Assistência Social" to "CRAS",
    "Conselho Regional de Biblioteconomia" to "CRB",
    "Conselho Regional de Biologia" to "CRBIO",
    "Conselho Regional de Biomedicina" to "CRBM",
    "Conselho Regional de Contabilidade" to "CRC",
    "Conselho Regional de Engenharia e Agronomia" to "CREA",
    "Conselho Regional de Corretores de Imóveis" to "CRECI",
    "Conselho Regional de Educação Física" to "CREF",
    "Conselho Regional de Fisioterapia e Terapia Ocupacional" to "CREFITO",
    "Conselho Regional de Serviço Social" to "CRESS",
    "Conselho Regional de Farmácia" to "CRF",
    "Conselho Regional de Fonoaudiologia" to "CRFA",
    "Conselho Regional de Medicina" to "CRM",
    "Conselho Regional de Medicina Veterinária" to "CRMV",
    "Conselho Regional de Nutrição" to "CRN",
    "Conselho Regional de Odontologia" to "CRO",
    "Conselho Regional de Psicologia" to "CRP",
    "Conselho Regional de Profissionais de Relações Públicas" to "CRPRE",
    "Conselho Regional de Química" to "CRQ",
    "Conselho Regional dos Técnicos Industriais" to "CRT",
    "Conselho Regional de Técnicos de Administração" to "CRTA",
    "Carteira de Trabalho e Previdência Social" to "CTPS",
    "Cartório Civil" to "CV",
    "Delegacia de Polícia de Imigração" to "DELEMIG",
    "Departamento Estadual de Trânsito" to "DETRAN",
    "Diretoria Geral da Polícia Civil" to "DGPC",
    "Diretoria de Identificação Civil" to "DIC",
    "Diretoria de Identificação Civil e Criminal" to "DICC",
    "Diretoria Executiva" to "DIREX",
    "Departamento de Polícia Federal" to "DPF",
    "Divisão de Polícia Marítima, Aérea e de Fronteiras" to "DPMAF",
    "Departamento de Polícia Técnica Geral" to "DPT",
    "Departamento de Polícia Técnico Científica" to "DPTC",
    "Delegacia Regional Executiva" to "DREX",
    "Delegacia Regional do Trabalho" to "DRT",
    "Exército Brasileiro" to "EB",
    "Força Aérea Brasileira" to "FAB",
    "Federação Nacional dos Jornalistas" to "FENAJ",
    "Fundo de Garantia do Tempo de Serviço" to "FGTS",
    "Fundação Instituto de Pesquisas Econômicas" to "FIPE",
    "Fundação Lyndolpho Silva" to "FLS",
    "Fundação Nacional do Índio" to "FUNAI",
    "Gerência de Estado de Justiça, Segurança Pública e Cidadania" to "GEJSP",
    "Gerência de Estado de Justiça, Segurança Pública e Cidadania" to "GEJSPC",
    "Gerência de Estado de Justiça, Segurança Pública e Cidadania" to "GEJUSPC",
    "Gerência de Estado de Segurança Pública" to "GESP",
    "Governo do Estado de Goiás" to "GOVGO",
    "- Carteira de Identidade Classista" to "I C",
    "Instituto de Polícia Científica" to "ICP",
    "Instituto de Identificação Dr. Aroldo Mendes Paiva" to "IDAMP",
    "Instituto Félix Pacheco" to "IFP",
    "Instituto Geral de Perícias" to "IGP",
    "Instituto de Identificação Aderson Conceição de Melo" to "IIACM",
    "Instituto de Identificação Civil e Criminal" to "IICC",
    "Instituto de Identificação Civil e Criminal Engrácia da Costa Francisco" to "IICCECF",
    "Instituto de Identificação Carlos Menezes" to "IICM",
    "Instituto de Identificação Gonçalo Pereira" to "IIGP",
    "Instituto de Identificação João de Deus Martins" to "IIJDM",
    "Instituto de Identificação da Polícia Civil" to "IIPC",
    "Instituto de Identificação Pedro Mello" to "IIPC",
    "Instituto de Identificação Ricardo Gumbleton Daunt" to "IIRGD",
    "Instituto de Identificação Raimundo Hermínio de Melo" to "IIRHM",
    "Instituto de Identificação Tavares Buril" to "IITB",
    "Instituto Médico-Legal" to "IML",
    "Instituto Nacional de Identificação" to "INI",
    "Instituto Pereira Faustino" to "IPF",
    "Instituto Técnico-Científico de Perícia" to "ITCP",
    "Instituto Técnico-Científico de Perícia" to "ITEP",
    "Ministério da Aeronáutica" to "MAER",
    "Marinha do Brasil" to "MB",
    "Ministério da Defesa" to "MD",
    "Ministério da Cidadania" to "MDS",
    "Ministério da Educação e Cultura" to "MEC",
    "Ministério do Exército" to "MEX",
    "Ministério da Defesa" to "MINDEF",
    "Ministério da Justiça" to "MJ",
    "Ministério da Marinha" to "MM",
    "Ministério da Marinha" to "MMA",
    "Ministério da Previdência e Assistência Social" to "MPAS",
    "Ministério Público Estadual" to "MPE",
    "Ministério Público Federal" to "MPF",
    "Ministério Público do Trabalho" to "MPT",
    "Ministério das Relações Exteriores" to "MRE",
    "Ministério do Trabalho" to "MT",
    "Ministério da Economia" to "MTE",
    "Ministério do Trabalho e Previdência Social" to "MTPS",
    "Núcleo de Polícia de Imigração" to "NUMIG",
    "Ordem dos Advogados do Brasil" to "OAB",
    "Ordens dos Músicos do Brasil" to "OMB",
    "Polícia Civil" to "PC",
    "Polícia Federal" to "PF",
    "Procuradoria Geral da Fazenda Nacional" to "PGFN",
    "Polícia Militar" to "PM",
    "Perícia Oficial e Identificação Técnica" to "POLITEC",
    "Polícia Rodoviária Federal" to "PRF",
    "Polícia Tecnico-Científica" to "PTC",
    "Secretaria de Estado da Casa Civil" to "SCC",
    "Secretaria Coordenadora de Justiça e Defesa Social" to "SCJDS",
    "Secretaria de Defesa Social" to "SDS",
    "Secretaria de Estado da Casa Civil" to "SECC",
    "Secretaria de Estado da Casa Civil e Desenvolvimento Econômico" to "SECCDE",
    "Secretaria de Estado da Defesa Social" to "SEDS",
    "Secretaria de Estado da Segurança Pública e da Defesa Social" to "SEGUP",
    "Secretaria de Estado de Justiça e Segurança Pública" to "SEJSP",
    "Secretaria de Estado da Justica" to "SEJUC",
    "Secretaria de Estado de Justiça e Segurança Pública" to "SEJUSP",
    "Secretaria de Estado da Polícia Civil" to "SEPC",
    "Secretaria de Estado da Segurança" to "SES",
    "Secretaria de Estado da Segurança e Cidadania" to "SESC",
    "Secretaria de Estado da Segurança, Defesa e Cidadania" to "SESDC",
    "Secretaria de Estado da Segurança, Defesa e Cidadania" to "SESDEC",
    "Secretaria Estadual de Segurança" to "SESEG",
    "Secretaria de Estado da Segurança Pública" to "SESP",
    "Secretaria de Estado da Segurança Pública e Administração Penitenciária" to "SESPAP",
    "Secretaria de Estado de Segurança Publica e Defesa do Cidadão" to "SESPDC",
    "Secretaria de Estado de Segurança Pública e Defesa Social" to "SESPDS",
    "Superintendência Geral de Polícia Civil" to "SGPC",
    "Superintendência Geral de Polícia Judiciária" to "SGPJ",
    "Serviço de Identificação da Marinha" to "SIM",
    "Secretaria da Justiça" to "SJ",
    "Secretaria da Justiça e dos Direitos Humanos" to "SJCDH",
    "Secretaria Coordenadora de Justiça e Defesa Social" to "SJDS",
    "Secretaria da Justiça e Segurança" to "SJS",
    "Secretaria da Justiça do Trabalho e Cidadania" to "SJTC",
    "Secretaria da Justiça do Trabalho e Segurança" to "SJTS",
    "Secretaria Nacional de Justiça / Departamento de Estrangeiros" to "SNJ",
    "Serviço de Polícia Marítima, Aérea e de Fronteiras" to "SPMAF",
    "Secretaria de Polícia Técnico-Científica" to "SPTC",
    "Superintendência Regional do Departamento de Polícia Federal" to "SRDPF",
    "Receita Federal" to "SRF",
    "Superintendência Regional do Trabalho" to "SRTE",
    "Secretaria da Segurança, Defesa e Cidadania" to "SSDC",
    "Secretaria da Segurança e da Defesa Social" to "SSDS",
    "Secretaria de Segurança e Informações" to "SSI",
    "Secretaria de Segurança Pública" to "SSP",
    "Secretaria de Segurança Pública e Coordenadoria Geral de Perícias" to "SSPCGP",
    "Secretaria de Segurança Pública e Defesa do Cidadão" to "SSPDC",
    "Secretaria de Segurança Pública e Defesa Social" to "SSPDS",
    "Secretaria de Segurança Pública Polícia Civil" to "SSPPC",
    "Superintendência de Seguros Privados" to "SUSEP",
    "Superintendência dos Serviços Penitenciários" to "SUSEPE",
    "Tribunal de Justiça" to "TJ",
    "Tribunal Arbitral e Mediação dos Estados Brasileiros" to "TJAEM",
    "Tribunal Regional Eleitoral" to "TRE",
    "Tribunal Regional Federal" to "TRF",
    "Tribunal Superior Eleitoral" to "TSE",
    "Detran Diretoria de Identificação Civil" to "DETRAN",
).mapKeys {
    it.key.stripAccents().stripSpacesAndPunctuation().lowercase()
}

val ORG_EMISSION_PREFIX_LIST = NORMALIZED_ORG_EMISSION_MAP.map {
    it.value.take(3).lowercase()
}