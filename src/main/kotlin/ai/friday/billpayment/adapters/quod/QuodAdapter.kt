package ai.friday.billpayment.adapters.quod

import ai.friday.billpayment.adapters.dynamodb.CreditCardOwnershipValidation
import ai.friday.billpayment.adapters.dynamodb.CreditCardScoreDbRepository
import ai.friday.billpayment.adapters.dynamodb.CreditCardScoreValidation
import ai.friday.billpayment.adapters.dynamodb.CreditCardValidationDbRepository
import ai.friday.billpayment.app.feature.RequiresQuod
import ai.friday.billpayment.app.integrations.CreditCardOwnership
import ai.friday.billpayment.app.integrations.CreditCardOwnershipValidationRequest
import ai.friday.billpayment.app.integrations.CreditCardScore
import ai.friday.billpayment.app.integrations.CreditCardScoreService
import ai.friday.billpayment.app.integrations.CreditCardScoreValidationRequest
import ai.friday.billpayment.app.integrations.CreditCardValidationService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpVersion
import io.micronaut.http.client.HttpClientConfiguration
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.retry.annotation.Retryable
import io.micronaut.runtime.ApplicationConfiguration
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.time.Duration
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
@RequiresQuod
open class QuodAdapter(
    private val repository: CreditCardValidationDbRepository,
    private val scoreRepository: CreditCardScoreDbRepository,
    private val configuration: QuodAdapterConfiguration,
    @param:Client(
        configuration = QuodHttpConfiguration::class,
        value = "\${integrations.quod.host}",
    ) private val httpClient: RxHttpClient,
) : CreditCardValidationService, CreditCardScoreService {

    @Retryable
    open fun internalValidateOwnership(validationRequest: CreditCardOwnershipValidationRequest, markers: LogstashMarker): CreditCardOwnership {
        val body = ValidateOwnershipRequestTO(
            authReportRequest = AuthReportRequestTO(
                options = AuthReportRequestOptionsTO(),
                searchBy = SearchByTO(
                    cpf = validationRequest.cpf,
                    creditCardNumber = validationRequest.lastFourDigits,
                    ccbin = validationRequest.bin,
                ),
            ),
        )

        markers.andAppend("request", body)

        val url = "${configuration.authReport}${configuration.authReportVersion}"
        val request = HttpRequest.POST(url, body)
            .header("Content-Type", "application/json; charset=UTF-8")
            .basicAuth(configuration.username, configuration.password)
        val call = httpClient.retrieve(request, Argument.of(ValidateOwnershipResponseTO::class.java))

        val response = call.firstOrError().blockingGet()

        when (val status = QuodResponseStatus.getStatus(response.authReportResponseEx.response.header.status)) {
            QuodResponseStatus.INVALID_ZIP_CODE -> throw Exception(status.message)
            QuodResponseStatus.INVALID_PHONE -> throw Exception(status.message)
            QuodResponseStatus.INVALID_CREDIT_CARD -> throw Exception(status.message)
            QuodResponseStatus.INVALID_BIN -> throw Exception(status.message)
            QuodResponseStatus.INVALID_CPF -> throw Exception(status.message)
            QuodResponseStatus.UNAUTHORIZED_CONSUMER_DATA,
            QuodResponseStatus.UNAUTHORIZED_CREDIT_CARD_SCORE,
            QuodResponseStatus.UNAUTHORIZED_CREDIT_CARD_OWNERSHIP,
            QuodResponseStatus.SUCCESS,
            -> {
                logger.warn(markers.andAppend("whenUnknownBranch", status), "WHENUNKNOWNBRANCH#QuodAdapter#validateOwnership")
            }
        }

        val creditCardOwnershipValidation = CreditCardOwnershipValidation(
            apiVersion = configuration.authReportVersion,
            request = body,
            response = response,
            timestamp = getZonedDateTime().toInstant().toEpochMilli(),
        )

        repository.save(creditCardOwnershipValidation)

        markers.andAppend("creditCardOwnershipValidation", creditCardOwnershipValidation)

        val ownershipStatus =
            QuodOwnershipStatusV15.getStatus(response.authReportResponseEx.response.records.authReportOutput.first().creditCardOwnership)

        markers.andAppend("ownerShipStatus", ownershipStatus)
        logger.info(markers, "QuodAdapter#validateOwnership")

        return when (ownershipStatus) {
            QuodOwnershipStatusV15.NO_CREDIT_CARDS_AVAILABLE -> CreditCardOwnership.NOT_OWNER
            QuodOwnershipStatusV15.NO_MATCH -> CreditCardOwnership.UNKNOWN
            QuodOwnershipStatusV15.NO_MATCH_AT_LEAST_ONE_KNOWN -> CreditCardOwnership.UNKNOWN
            QuodOwnershipStatusV15.NO_MATCH_ALL_UNKNOWN -> CreditCardOwnership.UNKNOWN
            QuodOwnershipStatusV15.DECEASED -> CreditCardOwnership.NOT_OWNER
            QuodOwnershipStatusV15.UNDERAGE -> CreditCardOwnership.UNKNOWN
            QuodOwnershipStatusV15.NOT_SHARED -> CreditCardOwnership.UNKNOWN
            QuodOwnershipStatusV15.MATCH -> CreditCardOwnership.IS_OWNER
        }
    }

    override fun validateOwnership(
        validationRequest: CreditCardOwnershipValidationRequest,
    ): Either<Exception, CreditCardOwnership> {
        val markers = Markers.append("bin", validationRequest.bin).andAppend("pan", validationRequest.lastFourDigits)
            .andAppend("document", validationRequest.cpf)

        return try {
            internalValidateOwnership(validationRequest, markers).right()
        } catch (e: HttpClientResponseException) {
            markers.andAppend("status", e.status).andAppend("response", e.response.getBody(String::class.java))
            logger.error(markers, "QuodAdapter#validateOwnership", e)
            return e.left()
        } catch (e: Exception) {
            logger.error(markers, "QuodAdapter#validateOwnership", e)
            return e.left()
        }
    }

    @Retryable
    open fun internalCreditcardScore(
        validationRequest: CreditCardScoreValidationRequest,
        markers: LogstashMarker,
    ): CreditCardScore {
        val body = ValidateCreditCardScoreRequestTO(
            authCCScoreRequest = AuthCCScoreRequestTO(
                options = CreditCardScoreRequestOptionsTO(),
                searchBy = CreditScoreSearchByTO(
                    cpf = validationRequest.cpf,
                    creditCardNumber = validationRequest.lastFourDigits,
                    ccbin = validationRequest.bin,
                ),
            ),
        )

        markers.andAppend("request", body)

        val url = "${configuration.authCCScore}${configuration.authCCScoreVersion}"
        val request = HttpRequest.POST(url, body)
            .header("Content-Type", "application/json; charset=UTF-8")
            .basicAuth(configuration.username, configuration.password)

        val call = httpClient.retrieve(request, Argument.of(ValidateCreditCardScoreResponseTO::class.java))

        val response = call.firstOrError().blockingGet()
        markers.andAppend("response", response)

        when (val status = QuodResponseStatus.getStatus(response.authCCScoreResponseEx.response.header.status)) {
            QuodResponseStatus.INVALID_ZIP_CODE -> throw Exception(status.message)
            QuodResponseStatus.INVALID_PHONE -> throw Exception(status.message)
            QuodResponseStatus.INVALID_CREDIT_CARD -> throw Exception(status.message)
            QuodResponseStatus.INVALID_BIN -> throw Exception(status.message)
            QuodResponseStatus.INVALID_CPF -> throw Exception(status.message)
            QuodResponseStatus.UNAUTHORIZED_CONSUMER_DATA,
            QuodResponseStatus.UNAUTHORIZED_CREDIT_CARD_SCORE,
            QuodResponseStatus.UNAUTHORIZED_CREDIT_CARD_OWNERSHIP,
            QuodResponseStatus.SUCCESS,
            -> {
                logger.warn(markers.andAppend("whenUnknownBranch", status), "WHENUNKNOWNBRANCH#QuodAdapter#internalCreditcardScore")
            }
        }

        val creditCardScoreValidation = CreditCardScoreValidation(
            apiVersion = configuration.authReportVersion,
            request = body,
            response = response,
            timestamp = getZonedDateTime().toInstant().toEpochMilli(),
        )

        scoreRepository.save(creditCardScoreValidation)

        markers.andAppend("creditCardScoreValidation", creditCardScoreValidation)

        var riskLevel = QuodCCScore.NO_MATCH
        response.authCCScoreResponseEx.response.records?.authccScoreOutput?.first()?.reasonCode?.reasonCode?.forEach {
            if (riskLevel == QuodCCScore.NO_MATCH && CODES_OK.contains(it.code)) {
                riskLevel = QuodCCScore.MATCH
                return@forEach
            } else if (riskLevel == QuodCCScore.NO_MATCH && CODES_NOK.contains(it.code)) {
                riskLevel = QuodCCScore.EXCECAO
                return@forEach
            }
        }

        markers.andAppend("riskLevel", riskLevel)

        logger.info(markers, "QuodAdapter#validateCreditCardScore")

        return when (riskLevel) {
            QuodCCScore.EXCECAO -> CreditCardScore.NO_MATCH
            QuodCCScore.MATCH -> CreditCardScore.MATCH
            else -> CreditCardScore.MANUAL_CHECK
        }
    }

    override fun creditcardScore(
        scoreRequest: CreditCardScoreValidationRequest,
    ): Either<Exception, CreditCardScore> {
        val markers = Markers.append("bin", scoreRequest.bin).andAppend("pan", scoreRequest.lastFourDigits)
            .andAppend("document", scoreRequest.cpf)

        return try {
            internalCreditcardScore(scoreRequest, markers).right()
        } catch (e: HttpClientResponseException) {
            markers.andAppend("status", e.status).andAppend("response", e.response.getBody(String::class.java))
            logger.error(markers, "QuodAdapter#creditcardScore", e)
            return e.left()
        } catch (e: Exception) {
            logger.error(markers, "QuodAdapter#creditcardScore", e)
            return e.left()
        }
    }

    private val logger = LoggerFactory.getLogger(QuodAdapter::class.java)
}

private val CODES_OK = arrayOf("1.1")
private val CODES_NOK = arrayOf("0.1", "0.2", "100", "200")

@JsonInclude(JsonInclude.Include.ALWAYS)
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy::class)
data class AuthReportRequestTO(
    val options: AuthReportRequestOptionsTO,
    val searchBy: SearchByTO,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy::class)
data class ValidateOwnershipRequestTO(
    val authReportRequest: AuthReportRequestTO,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy::class)
data class SearchByTO(
    @JsonProperty("CPF")
    val cpf: String,
    val creditCardNumber: String,
    @JsonProperty("CCBIN")
    val ccbin: String,
    val phone: String? = null,
    val addressLine1: String? = null,
    val addressLine2: String? = null,
    val neighborhood: String? = null,
    val city: String? = null,
    val state: String? = null,
    @JsonProperty("CEP")
    val cep: String? = null,
)

@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy::class)
data class AuthReportRequestOptionsTO(
    val includeConsumerDataValidation: Boolean = false,
    val includeAddressAuthenticity: Boolean = false,
    val includeCreditCardOwnership: Boolean = true,
    val includeCreditCardScore: Boolean = false,
)

@RequiresQuod
@ConfigurationProperties("integrations.quod")
class QuodAdapterConfiguration
@ConfigurationInject
constructor(
    val host: String,
    val username: String,
    val password: String,
    val authReport: String,
    val authReportVersion: String,
    val authCCScore: String,
    val authCCScoreVersion: String,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class ValidateOwnershipResponseTO(
    @JsonProperty("AuthReportResponseEx")
    val authReportResponseEx: AuthReportResponseExTO,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class AuthReportResponseExTO(
    val response: QuodResponseTO,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy::class)
data class QuodResponseTO(
    val header: HeaderTO,
    val records: RecordsTO,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy::class)
data class HeaderTO(
    val status: Long,
    val message: String?,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class RecordsTO(
    @JsonProperty("AuthReportOutput")
    val authReportOutput: List<AuthReportOutputTO>,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy::class)
data class AuthReportOutputTO(
    @JsonProperty("CPF")
    val cpf: String,
    val creditCardOwnership: String,
    val consumerDataValidation: ConsumerDataValidationTO?,
    val addressAuthenticity: AddressAuthenticityTO?,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy::class)
data class AddressAuthenticityTO(
    val score: String?,
    @JsonProperty("IsInactive")
    val inactive: String?,
    @JsonProperty("IsMinor")
    val minor: String?,
    @JsonProperty("IsConfirmedPhone")
    val confirmedPhone: String?,
    @JsonProperty("IsConfirmedEmail")
    val confirmedEmail: String?,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy::class)
data class ConsumerDataValidationTO(
    val personName: String?,
    @JsonProperty("CPFStatus")
    val cpfStatus: String?,
    @JsonProperty("IsDeceased")
    val deceased: String?,
    @JsonProperty("IsVip")
    val vip: String?,
    @JsonProperty("IsPep")
    val pep: String?,
)

enum class QuodOwnershipStatusV15(val code: String) {
    NO_MATCH("0"),
    MATCH("1"),
    NO_MATCH_ALL_UNKNOWN("2"),
    DECEASED("3"),
    UNDERAGE("4"),
    NOT_SHARED("5"),
    NO_MATCH_AT_LEAST_ONE_KNOWN("6"),
    NO_CREDIT_CARDS_AVAILABLE("7"),
    ;

    companion object {
        fun getStatus(code: String): QuodOwnershipStatusV15 = values().single() { it.code == code }
    }
}

/*
“0” – No match CPF x Credit Card
“1” – Match CPF x Credit Card
“2” – Unknown (não é possível dizer se match ou no match)
“3” – CPF Óbito
“4” – CPF Menor de Idade
“5” – CPF Inativo no Cadastro Positivo
“6” – (A partir da Versão 1.4+) Não houve match entre os dados informados e a base da quod; há pelo menos um cartão reportado que é desconhecido e ao menos um cartão conhecido;
“7” – (a partir da Versão 1.4+) Não há dados de cartão de crédito reportados para o CPF
 */

enum class QuodResponseStatus(val message: String, val code: Long) {
    INVALID_ZIP_CODE("CEP Inválido", 1110),
    INVALID_PHONE("Telefone Invalido", 1130),
    INVALID_CREDIT_CARD("Cartão de Crédito Inválido", 1150),
    INVALID_BIN("CCBIN Inválido", 1200),
    INVALID_CPF("CPF Inválido", 1510),
    UNAUTHORIZED_CONSUMER_DATA("Sem permissão para módulo de dados do Consumidor", 1520),
    UNAUTHORIZED_CREDIT_CARD_SCORE("Sem permissão para módulo score de autenticidade", 1530),
    UNAUTHORIZED_CREDIT_CARD_OWNERSHIP("Sem permissão para módulo autentica cartões", 1540),
    SUCCESS("", 0),
    ;

    companion object {
        fun getStatus(code: Long): QuodResponseStatus = values().find { it.code == code } ?: SUCCESS
    }
}

enum class QuodCCScore(val code: String) {

    NO_MATCH("0"),
    MATCH("1"),
    EXCECAO("-1"),
    ;

    companion object {
        fun getStatus(code: String): QuodCCScore = values().single() { it.code == code }
    }
}

@JsonInclude(JsonInclude.Include.ALWAYS)
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy::class)
data class AuthCCScoreRequestTO(
    @JsonProperty("Options")
    val options: CreditCardScoreRequestOptionsTO,
    @JsonProperty("SearchBy")
    val searchBy: CreditScoreSearchByTO,
)

@RequiresQuod
@Singleton
class QuodHttpConfiguration(
    applicationConfiguration: ApplicationConfiguration,
    val configuration: HttpClientConfiguration,
) : HttpClientConfiguration(applicationConfiguration) {

    init {
        setReadTimeout(Duration.ofMinutes(1))
        httpVersion = HttpVersion.HTTP_1_1
    }

    override fun getConnectionPoolConfiguration(): ConnectionPoolConfiguration = configuration.connectionPoolConfiguration
}

@JsonInclude(JsonInclude.Include.ALWAYS)
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy::class)
data class ValidateCreditCardScoreRequestTO(
    @JsonProperty("AuthCCScoreRequest")
    val authCCScoreRequest: AuthCCScoreRequestTO,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy::class)
data class CreditScoreSearchByTO(
    @JsonProperty("CPF")
    val cpf: String,
    @JsonProperty("CreditCardNumber")
    val creditCardNumber: String,
    @JsonProperty("CCBIN")
    val ccbin: String,
    @JsonProperty("TotalValue")
    val totalValue: String = "0",
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class ValidateCreditCardScoreResponseTO(
    @JsonProperty("AuthCCScoreResponseEx")
    val authCCScoreResponseEx: AuthCCScoreResponseExTO,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class AuthCCScoreResponseExTO(
    val response: QuodResponseCCScoreTO,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy::class)
data class QuodResponseCCScoreTO(
    val header: HeaderTO,
    val records: RecordsCCScoreTO?,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class RecordsCCScoreTO(
    @JsonProperty("AuthCCScoreOutput")
    val authccScoreOutput: List<AuthCCScoreOutputTO>,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy::class)
data class AuthCCScoreOutputTO(
    @JsonProperty("CPF")
    val cpf: String,
    val creditCardOwnership: String,
    val creditCardScore: String?,
    val reasonCode: ReasonCodeTO,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy::class)
data class ReasonCodeTO(
    @JsonProperty("ReasonCode")
    val reasonCode: List<ReasonCodesTO>,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy::class)
data class ReasonCodesTO(
    @JsonProperty("Code")
    val code: String,
    @JsonProperty("Message")
    val message: String?,
)

@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy::class)
data class CreditCardScoreRequestOptionsTO(
    val returnCount: Int = 1,
    val startingRecord: Int = 1,
    val includeCreditCardOwnership: Boolean = true,
    val includeCreditCardScore: Boolean = false,
)