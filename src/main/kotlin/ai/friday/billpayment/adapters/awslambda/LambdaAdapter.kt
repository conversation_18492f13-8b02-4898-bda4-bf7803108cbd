package ai.friday.billpayment.adapters.awslambda

import ai.friday.billpayment.MeasureResult
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.measureTimeInMillis
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.core.SdkBytes
import software.amazon.awssdk.services.lambda.LambdaClient
import software.amazon.awssdk.services.lambda.model.InvocationType
import software.amazon.awssdk.services.lambda.model.InvokeRequest

@Singleton
open class LambdaAdapter {

    private val logger = LoggerFactory.getLogger(LambdaAdapter::class.java)

    open fun invokeLambdaSynchronously(lambdaName: String, request: Any): String {
        val logName = "LambdaAdapter#invokeLambdaSynchronously"

        val measurement: MeasureResult<Either<Exception, String>> = measureTimeInMillis {
            val requestBody = getObjectMapper().writeValueAsString(request)
            val payload = getObjectMapper().writeValueAsString(LambdaRequestTO(requestBody))
            callLambdaFunction(lambdaName, payload)
        }

        val markers = append("lambdaName", lambdaName).andAppend("timeInMillis", measurement.elapsed)

        return measurement.result.fold(
            ifLeft = {
                logger.error(markers, logName, it)
                throw it
            },
            ifRight = {
                logger.info(markers, logName)
                it
            },
        )
    }

    private fun callLambdaFunction(
        lambdaName: String,
        payload: String?,
    ): Either<Exception, String> {
        val lambdaClient = LambdaClient.create()
        return try {
            val invokeRequest = InvokeRequest.builder()
                .functionName(lambdaName)
                .invocationType(InvocationType.REQUEST_RESPONSE)
                .payload(SdkBytes.fromUtf8String(payload))
                .build()
            val lambdaRawResponse = lambdaClient.invoke(invokeRequest).payload().asUtf8String()
            val lambdaResponse = parseObjectFrom<LambdaResponseTO>(lambdaRawResponse)
            lambdaResponse.body.right()
        } catch (e: Exception) {
            e.left()
        } finally {
            lambdaClient.close()
        }
    }
}

private data class LambdaRequestTO(
    val body: String,
)

private data class LambdaResponseTO(
    val body: String,
    val statusCode: Int,
)