package ai.friday.billpayment.adapters.converters

import ai.friday.billpayment.app.account.CustomS3Link
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTypeConverter
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper

/**
 * DynamoDB type converter for CustomS3Link to maintain compatibility with existing S3Link format:
 * {"s3":{"bucket":"bucket-name","key":"object-key","region":"us-east-1"}}
 */
class CustomS3LinkConverter : DynamoDBTypeConverter<String, CustomS3Link> {

    private val objectMapper = ObjectMapper()

    override fun convert(customS3Link: CustomS3Link): String {
        // Convert CustomS3Link to the expected DynamoDB JSON format
        val s3Data = mapOf(
            "s3" to mapOf(
                "bucket" to customS3Link.bucket,
                "key" to customS3Link.key,
                "region" to customS3Link.region,
            ),
        )
        return objectMapper.writeValueAsString(s3Data)
    }

    override fun unconvert(data: String): CustomS3Link {
        try {
            val jsonNode: JsonNode = objectMapper.readTree(data)

            // Handle the nested s3 structure
            val s3Node = jsonNode.get("s3") ?: throw IllegalArgumentException("Missing 's3' node in S3Link data: $data")

            val bucket = s3Node.get("bucket")?.asText() ?: throw IllegalArgumentException("Missing 'bucket' in S3Link data: $data")
            val key = s3Node.get("key")?.asText() ?: throw IllegalArgumentException("Missing 'key' in S3Link data: $data")
            val region = s3Node.get("region")?.asText() ?: "us-east-1" // default region

            return CustomS3Link(bucket = bucket, key = key, region = region)
        } catch (e: Exception) {
            throw IllegalArgumentException("Failed to convert S3Link data: $data", e)
        }
    }
}