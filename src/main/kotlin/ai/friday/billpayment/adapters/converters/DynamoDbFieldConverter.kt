package ai.friday.billpayment.adapters.converters

import ai.friday.billpayment.adapters.dynamodb.PaymentMethodsDetailEntity
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.adapters.parsers.parseListFrom
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.CustomS3Link
import ai.friday.billpayment.app.msisdnauth.MsisdnAuthId
import ai.friday.billpayment.app.wallet.BillViewingCriteria
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import software.amazon.awssdk.enhanced.dynamodb.AttributeConverter
import software.amazon.awssdk.enhanced.dynamodb.AttributeValueType
import software.amazon.awssdk.enhanced.dynamodb.EnhancedType
import software.amazon.awssdk.services.dynamodb.model.AttributeValue

class MsisdnAuthIdConverter : AttributeConverter<MsisdnAuthId> {
    override fun transformFrom(msisdnAuthId: MsisdnAuthId): AttributeValue? =
        AttributeValue.builder().s(msisdnAuthId.value).build()

    override fun transformTo(attributeValue: AttributeValue?): MsisdnAuthId =
        attributeValue?.s()?.let { MsisdnAuthId(it) }
            ?: throw IllegalArgumentException("Failed to convert to MsisdnAuthId")

    override fun type(): EnhancedType<MsisdnAuthId> = EnhancedType.of(MsisdnAuthId::class.java)

    override fun attributeValueType(): AttributeValueType = AttributeValueType.S
}

class AccountIdConverter : AttributeConverter<AccountId> {
    override fun transformFrom(accountId: AccountId): AttributeValue? =
        AttributeValue.builder().s(accountId.value).build()

    override fun transformTo(attributeValue: AttributeValue?): AccountId =
        attributeValue?.s()?.let { AccountId(it) }
            ?: throw IllegalArgumentException("Failed to convert to AccountId")

    override fun type(): EnhancedType<AccountId> = EnhancedType.of(AccountId::class.java)

    override fun attributeValueType(): AttributeValueType = AttributeValueType.S
}

class CustomS3LinkAttributeConverter : AttributeConverter<CustomS3Link> {
    private val objectMapper = ObjectMapper()

    override fun transformFrom(customS3Link: CustomS3Link): AttributeValue? {
        // Convert CustomS3Link to the expected DynamoDB JSON format
        val s3Data = mapOf(
            "s3" to mapOf(
                "bucket" to customS3Link.bucket,
                "key" to customS3Link.key,
                "region" to customS3Link.region,
            ),
        )
        val jsonString = objectMapper.writeValueAsString(s3Data)
        return AttributeValue.builder().s(jsonString).build()
    }

    override fun transformTo(attributeValue: AttributeValue?): CustomS3Link {
        val data = attributeValue?.s() ?: throw IllegalArgumentException("CustomS3Link attribute value is null")

        try {
            val jsonNode: JsonNode = objectMapper.readTree(data)

            // Handle the nested s3 structure
            val s3Node = jsonNode.get("s3")
                ?: throw IllegalArgumentException("Missing 's3' node in S3Link data: $data")

            val bucket = s3Node.get("bucket")?.asText()
                ?: throw IllegalArgumentException("Missing 'bucket' in S3Link data: $data")
            val key = s3Node.get("key")?.asText()
                ?: throw IllegalArgumentException("Missing 'key' in S3Link data: $data")
            val region = s3Node.get("region")?.asText() ?: "us-east-1" // default region

            return CustomS3Link(bucket = bucket, key = key, region = region)
        } catch (e: Exception) {
            throw IllegalArgumentException("Failed to convert S3Link data: $data", e)
        }
    }

    override fun type(): EnhancedType<CustomS3Link> = EnhancedType.of(CustomS3Link::class.java)

    override fun attributeValueType(): AttributeValueType = AttributeValueType.S
}

class PaymentMethodsDetailEntityAttributeConverter : AttributeConverter<PaymentMethodsDetailEntity> {
    override fun transformFrom(paymentMethodsDetailEntity: PaymentMethodsDetailEntity): AttributeValue? {
        val jsonString = getObjectMapper().writeValueAsString(paymentMethodsDetailEntity)
        return AttributeValue.builder().s(jsonString).build()
    }

    override fun transformTo(attributeValue: AttributeValue?): PaymentMethodsDetailEntity {
        val data = attributeValue?.s() ?: throw IllegalArgumentException("PaymentMethodsDetailEntity attribute value is null")
        return parseObjectFrom<PaymentMethodsDetailEntity>(data)
    }

    override fun type(): EnhancedType<PaymentMethodsDetailEntity> = EnhancedType.of(PaymentMethodsDetailEntity::class.java)

    override fun attributeValueType(): AttributeValueType = AttributeValueType.S
}

class BillViewingCriteriaListAttributeConverter : AttributeConverter<List<BillViewingCriteria>> {
    override fun transformFrom(criteriaList: List<BillViewingCriteria>): AttributeValue? {
        val jsonString = getObjectMapper().writeValueAsString(criteriaList)
        return AttributeValue.builder().s(jsonString).build()
    }

    override fun transformTo(attributeValue: AttributeValue?): List<BillViewingCriteria> {
        val data = attributeValue?.s() ?: throw IllegalArgumentException("BillViewingCriteria list attribute value is null")
        return parseListFrom<BillViewingCriteria>(data)
    }

    override fun type(): EnhancedType<List<BillViewingCriteria>> = EnhancedType.listOf(BillViewingCriteria::class.java)

    override fun attributeValueType(): AttributeValueType = AttributeValueType.S
}