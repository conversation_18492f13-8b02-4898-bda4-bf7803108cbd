package ai.friday.billpayment.adapters.handlebar

import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.formatBrazilCurrency
import ai.friday.billpayment.app.integrations.PDFConverter
import ai.friday.billpayment.app.integrations.StatementTemplateCompiler
import ai.friday.billpayment.app.integrations.StatementTemplateForm
import ai.friday.billpayment.app.notification.EmailTemplatesConfiguration
import ai.friday.billpayment.app.payment.formatDocument
import ai.friday.billpayment.app.utils.TemplateHelper
import java.time.format.DateTimeFormatter

@FridayMePoupe
class FridayStatementCompiler(
    private val emailTemplatesConfiguration: EmailTemplatesConfiguration,
    private val pdfConverter: PDFConverter,
) : StatementTemplateCompiler {

    override fun buildStatementHtml(form: StatementTemplateForm): CompiledHtml {
        val map = mapOf<String, Any>(
            "name" to form.name,
            "walletName" to form.walletName,
            "document" to formatDocument(form.document),
            "bankName" to "Banco Arbi S.A.",
            "bankNo" to "${form.bankAccount.bankNo}",
            "routingNo" to "${form.bankAccount.routingNo}".padStart(4, '0'),
            "accountNo" to "${form.bankAccount.accountNo}-${form.bankAccount.accountDv}",
            "startDate" to form.startDate.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")),
            "endDate" to form.endDate.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")),
            "created" to form.created.format(DateTimeFormatter.ofPattern("dd/MM/yyyy, hh:mm")),
            "statementItems" to form.statementItems.mapIndexed { index, entry ->
                mapOf<String, Any>(
                    "date" to entry.postedAt.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")),
                    "name" to entry.counterPartName,
                    "description" to entry.description,
                    "value" to (if (entry.flow == BankStatementItemFlow.DEBIT) "- " else "") + formatBrazilCurrency(entry.amount),
                    "isDebit" to (entry.flow == BankStatementItemFlow.DEBIT),
                    "isEvenEntry" to (index % 2 == 0),
                    "isOddEntry" to (index % 2 == 1),
                    "balance" to formatBrazilCurrency(entry.balance.amount),
                )
            },
            "isEvenFinalBalance" to (form.statementItems.size % 2 == 1),
            "isEmptyList" to (form.statementItems.isEmpty()),
            "initialBalance" to formatBrazilCurrency(form.initialBalance.amount),
            "finalBalance" to formatBrazilCurrency(form.finalBalance.amount),
            "initialBalanceDate" to form.initialBalanceDate.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")),
            "finalBalanceDate" to form.finalBalanceDate.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")),
        )
        return CompiledHtml(TemplateHelper.applyTemplate(emailTemplatesConfiguration.local.statementPdf, map))
    }

    override fun toPDF(html: CompiledHtml): ByteArray {
        return pdfConverter.convert(html.value, false)
    }
}