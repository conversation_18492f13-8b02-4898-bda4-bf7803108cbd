package ai.friday.billpayment.adapters.handlebar

import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.DeclarationOfResidencyForm
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.integrations.TemplateCompiler
import ai.friday.billpayment.app.integrations.TemplateForm
import ai.friday.billpayment.app.notification.EmailTemplatesConfiguration
import ai.friday.billpayment.app.register.kyc.KycDossierForm
import ai.friday.billpayment.app.utils.TemplateHelper
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton

@Singleton
@Requires(env = [FRIDAY_ENV, MODATTA_ENV, ME_POUPE_ENV])
class HandlebarTemplateCompiler(
    private val emailTemplatesConfiguration: EmailTemplatesConfiguration,
) : TemplateCompiler {

    override fun buildHtml(form: TemplateForm): CompiledHtml {
        val template = when (form) {
            is KycDossierForm -> emailTemplatesConfiguration.local.kycDossierFormPath
            is DeclarationOfResidencyForm -> emailTemplatesConfiguration.local.declarationOfResidencyFormPath
            else -> throw IllegalStateException()
        }

        return CompiledHtml(TemplateHelper.applyTemplate(template, form))
    }
}