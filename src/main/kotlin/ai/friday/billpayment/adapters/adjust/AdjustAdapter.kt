package ai.friday.billpayment.adapters.adjust

import ai.friday.billpayment.HttpClientError
import ai.friday.billpayment.and
import ai.friday.billpayment.app.integrations.AdAdapter
import ai.friday.billpayment.app.integrations.AdEventMetadata
import ai.friday.billpayment.postHttp
import ai.friday.billpayment.toEpochMillis
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Parameter
import io.micronaut.http.client.annotation.Client
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.net.URLEncoder
import java.security.MessageDigest
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@ConfigurationProperties("integrations.adjust")
data class AdjustConfiguration @ConfigurationInject constructor(
    val appToken: String,
    val events: Map<String, AdjustEventConfiguration>,
)

@EachProperty("integrations.adjust.event-tokens")
data class AdjustEventConfiguration @ConfigurationInject constructor(
    @param:Parameter val name: String,
    val token: String,
    val revenues: RevenuesConfiguration?,
) {
    @ConfigurationProperties("revenues")
    interface RevenuesConfiguration {
        val ios: IosRevenueConfiguration
        val android: AndroidRevenueConfiguration

        interface RevenueConfiguration {
            val currency: String
            val amount: Long
        }

        @ConfigurationProperties("ios")
        interface IosRevenueConfiguration : RevenueConfiguration

        @ConfigurationProperties("android")
        interface AndroidRevenueConfiguration : RevenueConfiguration
    }
}

@Singleton
open class AdjustAdapter(
    @param:Client(
        value = "\${integrations.adjust.host}",
    ) private val httpClient: RxHttpClient,
    private val configuration: AdjustConfiguration,
) : AdAdapter {
    private val logger = LoggerFactory.getLogger(AdjustAdapter::class.java)

    private val requiredParameters = mapOf(
        "s2s" to "1",
        "app_token" to configuration.appToken,
    )

    override fun trackEvent(name: String, metadata: AdEventMetadata) {
        val event = configuration.events[name] ?: throw Exception("Unknown event for Adjust: $name")

        val callbackParams = mapOf(
            "accountID" to metadata.accountId.value,
        )

        val adAppleId = metadata.trackingIds.adAppleId ?: ""
        val adGoogleId = metadata.trackingIds.adGoogleId ?: ""

        val eventParameters = mutableMapOf(
            "event_token" to event.token,
            "created_at_unix" to getZonedDateTime().toEpochMillis(),
            "idfa" to adAppleId,
            "gps_adid" to adGoogleId,
            "ip_address" to metadata.clientIP,
            "callback_params" to URLEncoder.encode(jacksonObjectMapper().writeValueAsString(callbackParams), "utf-8"),
        )

        metadata.trackingIds.adId?.let {
            eventParameters["adid"] = it
        }

        val revenue = if (adAppleId.isNotEmpty()) {
            event.revenues?.ios
        } else if (adGoogleId.isNotEmpty()) {
            event.revenues?.android
        } else {
            null
        }

        if (revenue != null) {
            eventParameters["revenue"] = revenue.amount.toDouble() / 100.0
            eventParameters["currency"] = revenue.currency
        }

        val partnerParams = metadata.toPartnerParams()
        if (partnerParams != null) {
            eventParameters["partner_params"] =
                URLEncoder.encode(jacksonObjectMapper().writeValueAsString(partnerParams), "utf-8")
        }

        val queryString = requiredParameters + eventParameters

        val markers = append("accountId", metadata.accountId)
            .andAppend("adIds", metadata.trackingIds)
            .andAppend("parameters", queryString)

        httpClient.postHttp<Unit>(
            path = "/event",
            qs = queryString,
        ).fold(
            ifLeft = {
                if (it is HttpClientError) {
                    markers.and("responseError" to it.res.getBody(String::class.java), "status" to it.res.status)
                }
                logger.error(
                    markers,
                    "AdjustAdapterTrackEvent",
                    it,
                )
            },
            ifRight = {
                logger.info(
                    markers,
                    "AdjustAdapterTrackEvent",
                )
            },
        )
    }
}

fun AdEventMetadata.toPartnerParams(): Map<String, String>? {
    val partnerParams = mutableMapOf<String, String>()

    if (this.emailAddress != null) {
        partnerParams["em"] = calculateSha256(this.emailAddress.value.trim().lowercase())
    }

    if (this.phoneNumber != null) {
        partnerParams["ph"] = calculateSha256(
            this.phoneNumber.msisdn.trim().trimStart('+').trimStart('0').replace(Regex("[-)( ]"), ""),
        )
    }

    if (this.state != null) {
        partnerParams["st"] = calculateSha256(this.state.trim().lowercase())
    }

    return partnerParams.ifEmpty { null }
}

private fun calculateSha256(string: String): String {
    val digest = MessageDigest.getInstance("SHA-256").digest(string.toByteArray())
    return digest.fold(StringBuilder()) { builder, it -> builder.append("%02x".format(it)) }.toString()
}