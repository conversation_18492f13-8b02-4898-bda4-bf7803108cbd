package ai.friday.billpayment.adapters.cognito

import ai.friday.billpayment.adapters.auth.FROM_CLAIM_ACCOUNT_ID
import ai.friday.billpayment.adapters.auth.FROM_CLAIM_EMAIL
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.UserPoolEnableUserError
import ai.friday.billpayment.app.account.UserPoolRemoveUserError
import ai.friday.billpayment.app.account.UserPoolSetMfaPreferenceError
import ai.friday.billpayment.app.feature.RequiresCognito
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.integrations.UserPoolListAuthEventsException
import ai.friday.billpayment.app.integrations.UserPoolPasswordValidationException
import ai.friday.billpayment.app.integrations.UserPoolUsernameNotFoundException
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import jakarta.inject.Singleton
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.cognitoidentityprovider.CognitoIdentityProviderClient
import software.amazon.awssdk.services.cognitoidentityprovider.model.AdminAddUserToGroupRequest
import software.amazon.awssdk.services.cognitoidentityprovider.model.AdminCreateUserRequest
import software.amazon.awssdk.services.cognitoidentityprovider.model.AdminDeleteUserAttributesRequest
import software.amazon.awssdk.services.cognitoidentityprovider.model.AdminDisableUserRequest
import software.amazon.awssdk.services.cognitoidentityprovider.model.AdminEnableUserRequest
import software.amazon.awssdk.services.cognitoidentityprovider.model.AdminGetUserRequest
import software.amazon.awssdk.services.cognitoidentityprovider.model.AdminGetUserResponse
import software.amazon.awssdk.services.cognitoidentityprovider.model.AdminListUserAuthEventsRequest
import software.amazon.awssdk.services.cognitoidentityprovider.model.AdminRemoveUserFromGroupRequest
import software.amazon.awssdk.services.cognitoidentityprovider.model.AdminResetUserPasswordRequest
import software.amazon.awssdk.services.cognitoidentityprovider.model.AdminSetUserMfaPreferenceRequest
import software.amazon.awssdk.services.cognitoidentityprovider.model.AdminSetUserPasswordRequest
import software.amazon.awssdk.services.cognitoidentityprovider.model.AdminUpdateUserAttributesRequest
import software.amazon.awssdk.services.cognitoidentityprovider.model.AdminUserGlobalSignOutRequest
import software.amazon.awssdk.services.cognitoidentityprovider.model.AttributeType
import software.amazon.awssdk.services.cognitoidentityprovider.model.InvalidParameterException
import software.amazon.awssdk.services.cognitoidentityprovider.model.InvalidPasswordException
import software.amazon.awssdk.services.cognitoidentityprovider.model.MessageActionType
import software.amazon.awssdk.services.cognitoidentityprovider.model.SMSMfaSettingsType
import software.amazon.awssdk.services.cognitoidentityprovider.model.UserNotFoundException

@Singleton
@RequiresCognito
class CognitoAdapter(private val cognitoConfiguration: CognitoConfiguration) : UserPoolAdapter {

    val client = CognitoIdentityProviderClient.builder().build()
    override fun configureUser(username: String, accountId: AccountId, groupNames: List<String>) {
        val user = getUser(username)
        if (!user.userAttributes().any { it.name() == FROM_CLAIM_ACCOUNT_ID }) {
            groupNames.forEach { groupName -> addUserToGroup(username, groupName) }
            setAccountId(username, accountId)
        }
    }

    override fun createUser(
        username: String,
        password: String,
        accountId: AccountId,
        emailAddress: EmailAddress,
        phoneNumber: MobilePhone,
        role: Role,
        name: String,
        setMFA: Boolean,
    ) {
        val markers = append("username", username)
            .and<LogstashMarker>(append("accountId", accountId.value))
            .and<LogstashMarker>(append("email", emailAddress.value))
            .and<LogstashMarker>(append("phoneNumber", phoneNumber.msisdn))

        try {
            // envia senha como temporaria para validar antes de criar a conta
            val userRequest = AdminCreateUserRequest.builder()
                .username(username)
                .userPoolId(cognitoConfiguration.userPoolId)
                .temporaryPassword(password)
                .messageAction(MessageActionType.SUPPRESS)
                .userAttributes(
                    AttributeType.builder().name(FROM_CLAIM_ACCOUNT_ID).value(accountId.value).build(),
                    AttributeType.builder().name(FROM_CLAIM_EMAIL).value(emailAddress.value).build(),
                    AttributeType.builder().name("phone_number").value(phoneNumber.msisdn).build(),
                    AttributeType.builder().name("name").value(name).build(),
                    AttributeType.builder().name("phone_number_verified").value("true").build(),
                    AttributeType.builder().name("email_verified").value("true").build(),
                ).build()

            // cria conta com status FORCE_CHANGE_PASSWORD
            val createUserResponse = client.adminCreateUser(userRequest)
            markers.and<LogstashMarker>(
                append(
                    "adminCreateUserHttpResponse",
                    createUserResponse.sdkHttpResponse().statusCode(),
                ),
            )
                .and<LogstashMarker>(append("adminCreateUserResponse", createUserResponse.user()))

            // envia senha de novo para mudar o status da conta para CONFIRMED
            val passwordRequest = AdminSetUserPasswordRequest.builder()
                .username(username)
                .userPoolId(cognitoConfiguration.userPoolId)
                .password(password)
                .permanent(true).build()

            val passwordResponse = client.adminSetUserPassword(passwordRequest)
            markers.and<LogstashMarker>(append("adminSetUserPassword", passwordResponse.sdkHttpResponse().statusCode()))

            if (setMFA) {
                setMfaPreference(username, true)
            }
            addUserToGroup(username, role.name)
        } catch (e: InvalidParameterException) {
            throw UserPoolPasswordValidationException(e.message)
        } catch (e: InvalidPasswordException) {
            throw UserPoolPasswordValidationException(e.message)
        } finally {
            logger.info(markers, "CognitoAdapterCreateUser")
        }
    }

    override fun doesUserExist(username: String): Boolean {
        return try {
            getUser(username)
            true
        } catch (e: UserNotFoundException) {
            false
        }
    }

    override fun isUserEnabled(username: String): Boolean {
        return try {
            val user = getUser(username)
            return user.enabled()
        } catch (e: UserNotFoundException) {
            false
        }
    }

    override fun addUserToGroup(username: String, groupName: String) {
        val markers = append("username", username)
            .and<LogstashMarker>(append("groupName", groupName))
        val request = AdminAddUserToGroupRequest.builder()
            .username(username)
            .userPoolId(cognitoConfiguration.userPoolId)
            .groupName(groupName).build()
        val response = client.adminAddUserToGroup(request)
        logger.info(
            markers.and(append("adminAddUserToGroup", response.sdkHttpResponse().statusCode())),
            "CognitoAddUserToGroup",
        )
    }

    override fun disableUser(username: String): Either<UserPoolRemoveUserError, Unit> {
        val markers = append("username", username)
        val request =
            AdminDisableUserRequest.builder().username(username).userPoolId(cognitoConfiguration.userPoolId).build()
        return try {
            val response = client.adminDisableUser(request)
            logger.info(
                markers.and(append("adminDisableUser", response.sdkHttpResponse().statusCode())),
                "CognitoDisableUser",
            )

            Unit.right()
        } catch (e: UserNotFoundException) {
            UserPoolRemoveUserError.UserNotFound.left()
        } catch (e: Exception) {
            UserPoolRemoveUserError.Unknown(e).left()
        }
    }

    override fun removeAccountId(username: String): Either<UserPoolRemoveUserError, Unit> {
        val markers = append("username", username).andAppend("parameter", FROM_CLAIM_ACCOUNT_ID)
        return try {
            val request = AdminDeleteUserAttributesRequest.builder()
                .username(username)
                .userPoolId(cognitoConfiguration.userPoolId).userAttributeNames(FROM_CLAIM_ACCOUNT_ID).build()
            client.adminDeleteUserAttributes(request)
            logger.info(markers, "CognitoRemoveAccountId")
            Unit.right()
        } catch (e: UserNotFoundException) {
            logger.warn(markers, "CognitoRemoveAccountId", e)
            UserPoolRemoveUserError.UserNotFound.left()
        } catch (e: Exception) {
            logger.error(markers, "CognitoRemoveAccountId", e)
            UserPoolRemoveUserError.Unknown(e).left()
        }
    }

    override fun removeUserFromGroup(username: String, groupName: String) {
        val markers = append("username", username)
            .and<LogstashMarker>(append("groupName", groupName))
        val request = AdminRemoveUserFromGroupRequest.builder()
            .username(username)
            .userPoolId(cognitoConfiguration.userPoolId)
            .groupName(groupName).build()
        val response = client.adminRemoveUserFromGroup(request)
        logger.info(
            markers.and(append("adminRemoveUserFromGroup", response.sdkHttpResponse().statusCode())),
            "CognitoRemoveUserFromGroup",
        )
    }

    override fun signOutUser(username: String) {
        try {
            val request = AdminUserGlobalSignOutRequest.builder()
                .username(username)
                .userPoolId(cognitoConfiguration.userPoolId).build()
            client.adminUserGlobalSignOut(request)
        } catch (e: UserNotFoundException) {
            logger.warn(append("username", username), "CognitoSignOutUser")
        }
    }

    override fun resetPassword(username: String) {
        try {
            val markers = append("username", username)
            val request = AdminResetUserPasswordRequest.builder()
                .username(username)
                .userPoolId(cognitoConfiguration.userPoolId).build()
            val response = client.adminResetUserPassword(request)
            logger.info(
                markers.and(append("adminResetUserPassword", response.sdkHttpResponse().statusCode())),
                "CognitoResetPassword",
            )
        } catch (e: UserNotFoundException) {
            throw UserPoolUsernameNotFoundException(e.message)
        }
    }

    override fun setUserPassword(username: String, password: String) {
        val markers = append("username", username)
        try {
            val passwordRequest = AdminSetUserPasswordRequest.builder()
                .username(username)
                .userPoolId(cognitoConfiguration.userPoolId)
                .password(password)
                .permanent(true).build()
            val response = client.adminSetUserPassword(passwordRequest)
            markers.andAppend("setUserPassword", response.sdkHttpResponse().statusCode())

            logger.info(markers, "CognitoResetPassword")
        } catch (e: UserNotFoundException) {
            logger.info(markers.andAppend("context", "userNotFound"), "CognitoResetPassword")
            throw UserPoolUsernameNotFoundException(e.message)
        } catch (e: Exception) {
            logger.error(markers, "CognitoResetPassword")
            throw e
        }
    }

    override fun updatePhoneNumber(username: String, phoneNumber: MobilePhone) {
        val markers = append("username", username)
            .and<LogstashMarker>(append("phoneNumber", phoneNumber.msisdn))

        val request = AdminUpdateUserAttributesRequest.builder()
            .username(username)
            .userPoolId(cognitoConfiguration.userPoolId)
            .userAttributes(
                AttributeType.builder().name("phone_number").value(phoneNumber.msisdn).build(),
                AttributeType.builder().name("phone_number_verified").value("true").build(),
            ).build()
        val response = client.adminUpdateUserAttributes(request)
        logger.info(
            markers.and(append("adminUpdateUserAttributes", response.sdkHttpResponse().statusCode())),
            "CognitoUpdatePhoneNumber",
        )
    }

    override fun updateEmailAddress(username: String, emailAddress: EmailAddress) {
        val markers = append("username", username)
            .and<LogstashMarker>(append("emailAddress", emailAddress.value))

        val request = AdminUpdateUserAttributesRequest.builder()
            .username(username)
            .userPoolId(cognitoConfiguration.userPoolId)
            .userAttributes(
                AttributeType.builder().name("email").value(emailAddress.value).build(),
                AttributeType.builder().name("email_verified").value("true").build(),
            ).build()
        val response = client.adminUpdateUserAttributes(request)
        logger.info(
            markers.and(append("adminUpdateUserAttributes", response.sdkHttpResponse().statusCode())),
            "CognitoUpdateEmailAddress",
        )
    }

    override fun setAccountId(username: String, accountId: AccountId) {
        val request = AdminUpdateUserAttributesRequest.builder()
            .username(username)
            .userPoolId(cognitoConfiguration.userPoolId)
            .userAttributes(
                AttributeType.builder()
                    .name(FROM_CLAIM_ACCOUNT_ID)
                    .value(accountId.value).build(),
            ).build()
        client.adminUpdateUserAttributes(request)
    }

    override fun getAccountId(username: String): AccountId? {
        val request = AdminGetUserRequest.builder()
            .username(username)
            .userPoolId(cognitoConfiguration.userPoolId).build()
        val attributeValue = client.adminGetUser(request).userAttributes().firstOrNull {
            it.name() == FROM_CLAIM_ACCOUNT_ID
        }?.value()

        return attributeValue?.let { AccountId(it) }
    }

    override fun enableUser(username: String, accountId: AccountId?): Either<UserPoolEnableUserError, Unit> {
        val markers = append("username", username)
        val request =
            AdminEnableUserRequest.builder().username(username).userPoolId(cognitoConfiguration.userPoolId).build()
        return try {
            val response = client.adminEnableUser(request)
            accountId?.let { this.setAccountId(username, it) }
            logger.info(
                markers.and(append("adminEnableUser", response.sdkHttpResponse().statusCode())),
                "CognitoEnableUser",
            )

            Unit.right()
        } catch (e: UserNotFoundException) {
            logger.error(markers, "CognitoEnableUser", e)
            UserPoolEnableUserError.UserNotFound.left()
        } catch (e: Exception) {
            logger.error(markers, "CognitoEnableUser", e)
            UserPoolEnableUserError.Unknown(e).left()
        }
    }

    override fun setMfaPreference(username: String, enabled: Boolean): Either<UserPoolSetMfaPreferenceError, Unit> {
        val markers = append("username", username)
            .andAppend("enabled", enabled)

        val smsMfaSettingsTypeBuilder = SMSMfaSettingsType.builder().enabled(enabled)

        if (enabled) {
            smsMfaSettingsTypeBuilder.preferredMfa(true)
        }

        val smsMfaSettingsType = smsMfaSettingsTypeBuilder.build()

        return try {
            val mfaRequest = AdminSetUserMfaPreferenceRequest.builder()
                .username(username)
                .userPoolId(cognitoConfiguration.userPoolId)
                .smsMfaSettings(smsMfaSettingsType).build()
            val mfaResponse = client.adminSetUserMFAPreference(mfaRequest)

            mfaResponse.sdkHttpResponse()
            val statusCode = mfaResponse.sdkHttpResponse().statusCode()
            markers.andAppend("statusCode", statusCode)

            logger.info(markers, "setMfaPreference")

            Unit.right()
        } catch (e: UserNotFoundException) {
            logger.error(markers, "setMfaPreference", e)
            UserPoolSetMfaPreferenceError.UserNotFound.left()
        } catch (e: Exception) {
            logger.error(markers, "setMfaPreference", e)
            UserPoolSetMfaPreferenceError.Unknown(e).left()
        }
    }

    override fun listUserEvents(username: String): Either<UserPoolListAuthEventsException, List<String>> {
        try {
            val request = AdminListUserAuthEventsRequest.builder()
                .username(username)
                .userPoolId(cognitoConfiguration.userPoolId).build()

            val response = client.adminListUserAuthEvents(request)

            return response.authEvents().map { getObjectMapper().writeValueAsString(it) }.also {
                logger.info("CognitoListUserEvents#$username", append("username", username).andAppend("events", it))
            }.right()
        } catch (ex: Exception) {
            logger.error("CognitoListUserEvents#$username", append("username", username).andAppend("events", ex))
            return UserPoolListAuthEventsException(ex.message).left()
        }
    }

    private fun getUser(username: String): AdminGetUserResponse {
        val request = AdminGetUserRequest.builder()
            .username(username)
            .userPoolId(cognitoConfiguration.userPoolId).build()
        return client.adminGetUser(request)
    }

    companion object {
        internal val logger = LoggerFactory.getLogger(CognitoAdapter::class.java)
    }
}

@ConfigurationProperties("integrations.cognito")
class CognitoConfiguration @ConfigurationInject constructor(
    val userPoolId: String,
)