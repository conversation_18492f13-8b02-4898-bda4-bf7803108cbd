package ai.friday.billpayment.adapters.celcoin

import ai.friday.billpayment.adapters.celcoin.CelcoinBillTypeMapper.toBillType
import ai.friday.billpayment.adapters.celcoin.CelcoinBillTypeMapper.toInteger
import ai.friday.billpayment.adapters.celcoin.Codes.CELCOIN_TRANSACTION_NOT_EXIST
import ai.friday.billpayment.adapters.celcoin.Codes.GENERIC_ERROR
import ai.friday.billpayment.adapters.celcoin.Codes.PAYMENT_NOT_AUTHORIZED_CODES
import ai.friday.billpayment.adapters.celcoin.Codes.SHOULD_VERIFY_ERROR_CODE
import ai.friday.billpayment.adapters.celcoin.Codes.VALID_CODE
import ai.friday.billpayment.adapters.celcoin.Codes.alreadyPaidCodes
import ai.friday.billpayment.adapters.celcoin.Codes.expectedErrorCodes
import ai.friday.billpayment.adapters.celcoin.Codes.expectedErrorMessages
import ai.friday.billpayment.adapters.celcoin.Codes.failureErrorCodes
import ai.friday.billpayment.adapters.celcoin.Codes.notPayableCodes
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.ConcessionariaRequest
import ai.friday.billpayment.app.bill.CreateBoletoRequest
import ai.friday.billpayment.app.bill.CreateFichaDeCompensacaoRequest
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.bill.and
import ai.friday.billpayment.app.feature.RequiresCelcoin
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.integrations.TransactionalValidationResponse
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.BillPaymentReceipt
import ai.friday.billpayment.app.payment.BillPaymentResponse
import ai.friday.billpayment.app.payment.BillRegisterData
import ai.friday.billpayment.app.payment.BillValidationResponse
import ai.friday.billpayment.app.payment.BoletoSettlementException
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.app.payment.DivergentPayment
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.payment.PartialPaymentAmountType
import ai.friday.billpayment.app.payment.TransactionError
import ai.friday.billpayment.app.unmaskDocument
import ai.friday.billpayment.deepMarkers
import ai.friday.billpayment.log
import ai.friday.billpayment.markers
import ai.friday.billpayment.parseResponseOrNull
import ai.friday.billpayment.responseBodyAsStringOrNull
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonProperty
import io.micronaut.context.annotation.Secondary
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpAttributes
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.client.exceptions.ReadTimeoutException
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.reactivex.Flowable
import jakarta.inject.Singleton
import java.text.DecimalFormat
import java.text.NumberFormat
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.Locale
import kotlin.math.roundToLong
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

const val payerBank = "Is2b Integrated Sol To Business Sa"
const val payerDocument = "**************"

private const val UNKNOWN_TRASACTION_ID = 0L

@RequiresCelcoin
@Singleton
@Secondary
open class CelcoinAdapter(
    @Client("celcoin") private val httpClient: RxHttpClient,
    private val configuration: CelcoinAdapterConfiguration,
    celcoinAuth: CelcoinAuth,
) : BoletoSettlementService {

    private val celcoinAccessTokenWrapper = CelcoinAccessTokenWrapper(celcoinAuth)

    override fun getBankByInstitutionCode(institutionCode: Int): String {
        return getBanks().banks.find { it.institutionCode == institutionCode }?.description?.trim()
            ?: institutionCode.toString()
    }

    private fun getBanks(): BankListTO {
        try {
            val httpRequest: HttpRequest<String> = HttpRequest.GET<String>(configuration.banksPath)
                .bearerAuth(celcoinAccessTokenWrapper.fetch().value)
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            httpRequest.setAttribute(HttpAttributes.URI_TEMPLATE, configuration.banksPath)

            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(BankListTO::class.java),
            )
            return call.firstOrError().blockingGet()
        } catch (e: HttpClientResponseException) {
            logger.error(e.markers(), "CelcoinGetBanks", e)

            if (e.status == HttpStatus.UNAUTHORIZED) {
                celcoinAccessTokenWrapper.invalidate()
            }
            throw e
        }
    }

    override fun validateBill(bill: Bill): CelcoinBillValidationResponse {
        return validateBill(bill.toCelcoinAuthorizeRequestTO())
    }

    override fun settlementValidation(bill: Bill): BillValidationResponse {
        return validateBill(bill.toCelcoinAuthorizeRequestTO())
    }

    override fun validateBill(boleto: CreateBoletoRequest): CelcoinBillValidationResponse {
        return validateBill(boleto.toCelcoinAuthorizeRequestTO())
    }

    override fun validateBill(bill: BillView): CelcoinBillValidationResponse {
        return validateBill(bill.toCelcoinAuthorizeRequestTO())
    }

    override fun getBalanceAmount(): Either<Exception, Long> {
        val request = HttpRequest.GET<Unit>(configuration.balancePath)
            .bearerAuth(celcoinAccessTokenWrapper.fetch().value)
            .contentType(MediaType.APPLICATION_JSON_TYPE)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        request.setAttribute(HttpAttributes.URI_TEMPLATE, configuration.balancePath)

        return try {
            val call = httpClient.exchange(request, CelcoinBalanceTO::class.java)
            val response = call.firstOrError().blockingGet()
            response.body()?.let {
                logger.info(append("response", it), "CelcoinGetBalanceAmount")
                convertToLong(it.balance).right()
            } ?: IllegalStateException("Balance response should have body").left()
        } catch (e: HttpClientResponseException) {
            if (e.status == HttpStatus.UNAUTHORIZED) {
                celcoinAccessTokenWrapper.invalidate()
            }

            logger.error(e.markers(), "CelcoinGetBalanceAmount")
            e.left()
        } catch (e: Exception) {
            logger.error("CelcoinGetBalanceAmount", e)
            e.left()
        }
    }

    override fun initPayment(
        bill: Bill,
        nsu: Int,
        transactionId: String,
        payerDocument: String,
        payerName: String,
    ): BillPaymentResponse {
        val request = buildCelcoinPaymentRequest(bill, nsu, transactionId, payerDocument, payerName)

        val url = configuration.paymentPath
        val markers = log("url" to url, "request" to request, "method" to "init_payment")

        return try {
            val httpRequest = HttpRequest.POST(url, request)
                .bearerAuth(celcoinAccessTokenWrapper.fetch().value)
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            httpRequest.setAttribute(HttpAttributes.URI_TEMPLATE, url)

            val call = httpClient.exchange(httpRequest, CelcoinTransactionReceiptV5::class.java)
            val response = call.firstOrError().blockingGet()

            markers.add(response.deepMarkers())

            if (response.status != HttpStatus.OK) {
                logger.warn(markers, "CelcoinBillPayment")

                return BillPaymentResponse(
                    BoletoSettlementStatus.UNAUTHORIZED,
                    response.body()!!.transactionId.toLong(),
                    response.body()!!.message,
                )
            }
            logger.info(markers.and("result" to "success"), "CelcoinBillPayment")
            BillPaymentResponse(BoletoSettlementStatus.AUTHORIZED, response.body()!!.transactionId.toLong())
        } catch (e: HttpClientResponseException) {
            markers.add(e.markers().and("result" to "http_error"))

            if (e.status == HttpStatus.UNAUTHORIZED) {
                celcoinAccessTokenWrapper.invalidate()
            }

            e.parseResponseOrNull<CelcoinErrorResponse>()?.let { r ->
                markers.and("errorCode" to r.errorCode)
                if (isFailureErrorCode(r.errorCode)) {
                    if (shouldVerifyErrorCode(r.errorCode)) {
                        markers.andAppend("ACTION", "VERIFY")
                    }
                    logger.error(markers, "CelcoinBillPayment", e)
                } else {
                    logger.warn(markers, "CelcoinBillPayment", e)
                }
            } ?: run { logger.error(markers, "CelcoinBillPayment", e) }

            BillPaymentResponse(BoletoSettlementStatus.UNAUTHORIZED, 0, TransactionError.GENERIC_EXCEPTION.description)
        } catch (e: Exception) {
            logger.error(markers.and("result" to "error"), "CelcoinBillPayment", e)
            BillPaymentResponse(BoletoSettlementStatus.UNKNOWN, 0, TransactionError.GENERIC_EXCEPTION.description)
        }
    }

    override fun confirmPayment(externalTerminal: String, externalNsu: Int, transactionId: String) {
        val request = CelcoinConfirmRequest(externalNSU = externalNsu, externalTerminal = externalTerminal)
        val url = configuration.capturePath.replace("{transactionId}", transactionId)

        val markers = log("url" to url, "request" to request, "method" to "confirm_payment")

        try {
            val httpRequest: HttpRequest<*> = HttpRequest.PUT(url, request)
                .bearerAuth(celcoinAccessTokenWrapper.fetch().value)
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            httpRequest.setAttribute(HttpAttributes.URI_TEMPLATE, configuration.capturePath)

            val call: Flowable<HttpResponse<String>> = httpClient.exchange(httpRequest, String::class.java)
            val response = call.firstOrError().blockingGet()
            logger.info(markers.and(response.deepMarkers().and("result" to "success")), "CelcoinPaymentCapture")
        } catch (e: Exception) {
            markers.and("result" to "error")
            if (e is HttpClientResponseException) {
                markers.add(e.markers())
                if (e.status == HttpStatus.UNAUTHORIZED) {
                    celcoinAccessTokenWrapper.invalidate()
                }
            }
            logger.error(markers, "CelcoinPaymentCapture", e)

            throw BoletoSettlementException(
                FinancialServiceGateway.CELCOIN,
                TransactionError.SETTLEMENT_CONFIRMATION.description,
                e,
            )
        }
    }

    override fun cancelPayment(externalTerminal: String, externalNsu: Int, transactionId: String) {
        val request = CelcoinConfirmRequest(externalNSU = externalNsu, externalTerminal = externalTerminal)
        val url = configuration.voidPath.replace("{transactionId}", transactionId)

        val markers = log(
            "url" to url,
            "request" to request,
            "transactionId" to transactionId,
            "method" to "cancel_payment",
        )

        if (transactionId == UNKNOWN_TRASACTION_ID.toString()) {
            markers.and("details" to "Request not sent to Celcoin", "result" to "request_not_sent")
            logger.info(markers, "CelcoinPaymentVoid")
            return
        }

        try {
            val httpRequest: HttpRequest<*> = HttpRequest.DELETE(url, request)
                .bearerAuth(celcoinAccessTokenWrapper.fetch().value)
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            httpRequest.setAttribute(HttpAttributes.URI_TEMPLATE, configuration.voidPath)

            val call = httpClient.exchange(
                httpRequest,
                Argument.of(String::class.java),
                Argument.of(CelcoinErrorResponse::class.java),
            )
            val response = call.firstOrError().blockingGet()
            markers.add(response.deepMarkers())

            if (response.status != HttpStatus.OK) {
                logger.warn(markers.and("result" to "not_ok"), "CelcoinPaymentVoid")
                throw BoletoSettlementException(FinancialServiceGateway.CELCOIN, null, null)
            }
            logger.info(markers.and("result" to "success"), "CelcoinPaymentVoid")
        } catch (e: HttpClientResponseException) {
            markers.add(e.markers().and("result" to "http_error"))

            if (e.status == HttpStatus.UNAUTHORIZED) {
                celcoinAccessTokenWrapper.invalidate()
            }
            val response = e.response.getBody(CelcoinErrorResponse::class.java)
            if ((response.isPresent && CELCOIN_TRANSACTION_NOT_EXIST == response.get().errorCode)) {
                val status = queryPayment(transactionId)

                if (status == BoletoSettlementStatus.VOIDED) {
                    logger.info(markers, "CelcoinPaymentVoid", e)
                    return
                } else {
                    logger.warn(markers, "CelcoinPaymentVoid", e)
                    throw BoletoSettlementException(
                        FinancialServiceGateway.CELCOIN,
                        "Can't cancel payment. Current status: ${status.name}",
                        e,
                    )
                }
            }
            if (response.isPresent && GENERIC_ERROR == response.get().errorCode) {
                logger.info(markers, "CelcoinPaymentVoid", e)
                return
            }
            logger.error(markers, "CelcoinPaymentVoid", e)
            throw BoletoSettlementException(FinancialServiceGateway.CELCOIN, null, e)
        } catch (e: Exception) {
            logger.error(markers.and("result" to "error"), "CelcoinPaymentVoid", e)
            throw BoletoSettlementException(FinancialServiceGateway.CELCOIN, null, e)
        }
    }

    override fun queryPayment(transactionId: String): BoletoSettlementStatus {
        val url = configuration.queryPath.replace("{transactionId}", transactionId)
        val markers = log("url" to url, "transactionId" to transactionId, "method" to "query_payment")

        if (transactionId == UNKNOWN_TRASACTION_ID.toString()) {
            logger.info(
                markers
                    .andAppend("settlementStatus", BoletoSettlementStatus.UNKNOWN)
                    .andAppend("details", "Request not sent to Celcoin"),
                "CelcoinQueryPayment",
            )
            return BoletoSettlementStatus.UNKNOWN
        }

        try {
            val httpRequest: HttpRequest<*> = HttpRequest.GET<CelcoinQueryPaymentResponseTO>(url)
                .bearerAuth(celcoinAccessTokenWrapper.fetch().value)
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            httpRequest.setAttribute(HttpAttributes.URI_TEMPLATE, configuration.queryPath)

            val call = httpClient.exchange(
                httpRequest,
                Argument.of(CelcoinQueryPaymentResponseTO::class.java),
                Argument.of(CelcoinErrorResponse::class.java),
            )
            val response = call.firstOrError().blockingGet()

            markers.add(response.deepMarkers())

            if (response.status != HttpStatus.OK) {
                logger.warn(markers.and("result" to "not_ok"), "CelcoinQueryPayment")
                throw BoletoSettlementException(FinancialServiceGateway.CELCOIN, null, null)
            }

            val settlementStatus =
                response.body()?.transaction?.toBoletoSettlementStatus() ?: BoletoSettlementStatus.UNKNOWN

            logger.info(
                markers.and("settlementStatus" to settlementStatus.name, "result" to "success"),
                "CelcoinQueryPayment",
            )
            return settlementStatus
        } catch (e: Exception) {
            when (e) {
                is HttpClientResponseException -> {
                    markers.add(e.markers().and("result" to "http_error"))
                    if (e.status == HttpStatus.UNAUTHORIZED) {
                        celcoinAccessTokenWrapper.invalidate()
                    }
                }

                else -> markers.and("result" to "error")
            }

            logger.error(markers, "CelcoinQueryPayment", e)
            throw BoletoSettlementException(FinancialServiceGateway.CELCOIN, null, e)
        }
    }

    private fun CelcoinTransactionTO.toBoletoSettlementStatus() = when {
        status == 0 && errorCode == "000" -> BoletoSettlementStatus.CONFIRMED
        status == 1 && errorCode == "000" -> BoletoSettlementStatus.VOIDED
        status == 4 && errorCode == "000" -> BoletoSettlementStatus.AUTHORIZED
        status == 1 && errorCode == "061" -> BoletoSettlementStatus.UNAUTHORIZED
        else -> BoletoSettlementStatus.UNKNOWN
    }

    fun getReceipt(bankTransactionId: String): BillPaymentReceipt {
        val url = configuration.receiptPath.replace("{transactionId}", bankTransactionId)
        val markers = log("url" to url, "transactionId" to bankTransactionId, "method" to "get_receipt")

        return try {
            val httpRequest = HttpRequest.GET<Any>(url)
                .bearerAuth(celcoinAccessTokenWrapper.fetch().value)
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            httpRequest.setAttribute(HttpAttributes.URI_TEMPLATE, configuration.receiptPath)

            val call = httpClient.exchange(httpRequest, CelcoinTransactionReceiptV5::class.java)
            val response = call.firstOrError().blockingGet()

            markers.add(response.markers())

            if (response.status != HttpStatus.OK) {
                logger.warn(
                    markers.andAppend("CelcoinTransactionReceipt", response.getBody(String::class.java)),
                    "CelcoinTransactionPendency",
                )
                throw BoletoSettlementException(FinancialServiceGateway.CELCOIN, null, null)
            }
            val receipt = response.body()

            markers.andAppend("receipt", receipt)

            if (receipt == null || receipt.authenticationAPI.blocoCompleto.isBlank()) {
                logger.error(markers, "CelcoinTransactionReceipt")
                throw IllegalStateException("Receipt cannot be blank")
            }

            logger.info(markers, "CelcoinTransactionReceipt")

            receipt.toBillPaymentReceipt()
        } catch (e: HttpClientResponseException) {
            logger.error(markers.and(e.markers()), "CelcoinTransactionReceipt", e)

            if (e.status == HttpStatus.UNAUTHORIZED) {
                celcoinAccessTokenWrapper.invalidate()
            }

            throw BoletoSettlementException(FinancialServiceGateway.CELCOIN, null, e)
        }
    }

    fun validateBill(request: CelcoinAuthorizeRequestTO): CelcoinBillValidationResponse {
        val markers = log("url" to configuration.authorizePath, "request" to request, "method" to "validate_bill")

        return try {
            val httpRequest: HttpRequest<CelcoinAuthorizeRequestTO> =
                HttpRequest.POST(configuration.authorizePath, request)
                    .bearerAuth(celcoinAccessTokenWrapper.fetch().value)
                    .contentType(MediaType.APPLICATION_JSON_TYPE)
                    .accept(MediaType.APPLICATION_JSON_TYPE)

            httpRequest.setAttribute(HttpAttributes.URI_TEMPLATE, configuration.authorizePath)

            val call = httpClient.exchange(
                httpRequest,
                Argument.of(CelcoinAuthorizeResponseTO::class.java),
            )

            val response = call.firstOrError().blockingGet().also { markers.andAppend("response", it) }
            val responseTO = response.body().also { markers.andAppend("responseTO", it) }
                ?: throw IllegalStateException("body must not be null")

            logger.info(markers, "CelcoinAuthorize")

            if (responseTO.registerData != null && responseTO.registerData.totalUpdated < 0) {
                return CelcoinBillValidationResponse("1000", "Valor negativo")
            }

            if (toBillType(responseTO.type) == BillType.CONCESSIONARIA) {
                responseTO.toConcessionariaValidationResponse()
            } else {
                responseTO.toFichaValidationResponse()
            }
        } catch (e: BoletoSettlementException) {
            logger.error(markers.andAppend("description", e.message), "CelcoinAuthorize")
            throw e
        } catch (e: Exception) {
            if (e !is HttpClientResponseException) {
                if (e is ReadTimeoutException) {
                    logger.warn(markers, "CelcoinAuthorize", e)
                } else {
                    logger.error(markers, "CelcoinAuthorize", e)
                }
                throw BoletoSettlementException(FinancialServiceGateway.CELCOIN, null, e)
            }

            markers.add(e.markers())

            if (e.status == HttpStatus.UNAUTHORIZED) {
                celcoinAccessTokenWrapper.invalidate()
            }

            try {
                val errorResponse = getObjectMapper().readValue(e.responseBodyAsStringOrNull(), CelcoinErrorResponse::class.java)

                markers.and("response" to errorResponse)

                if (isExpectedErrorCode(errorResponse.errorCode) || isExpectedErrorMessage(errorResponse.message)) {
                    logger.warn(markers, "CelcoinAuthorize", e)
                } else {
                    logger.error(markers, "CelcoinAuthorize", e)
                }

                return CelcoinBillValidationResponse(errorResponse.errorCode, errorResponse.message)
            } catch (t: Throwable) {
                logger.error(markers.andAppend("responseDecodeError", t.message), "CelcoinAuthorize", e)
                throw BoletoSettlementException(FinancialServiceGateway.CELCOIN, null, e)
            }
        }
    }

    private fun isExpectedErrorCode(errorCode: String) = expectedErrorCodes.contains(errorCode)
    private fun isExpectedErrorMessage(errorMessage: String) = expectedErrorMessages.contains(errorMessage)

    private fun CelcoinAuthorizeResponseTO.toConcessionariaValidationResponse() = CelcoinBillValidationResponse(
        transactionId = transactionId,
        gatewayResponseCode = errorCode,
        errorDescription = message,
        billRegisterData = BillRegisterData(
            toBillType(type),
            assignor = assignor,
            recipient = null,
            recipientChain = null,
            payerDocument = null,
            amount = convertToLong(value.toString()),
            discount = 0,
            interest = 0,
            fine = 0,
            amountTotal = convertToLong(value.toString()),
            expirationDate = null,
            dueDate = dueDate?.let {
                runCatching {
                    LocalDate.parse(it, DateTimeFormatter.ISO_ZONED_DATE_TIME)
                }.getOrElse {
                    logger.error(append("formattingError", dueDate), "CelcoinAuthorize", it)
                    null
                }
            },
            paymentLimitTime = endHour,
            settleDate = settleDate?.let { fromFormattedSettleDate(it) },
            fichaCompensacaoType = null,
            payerName = null,
            amountCalculationModel = AmountCalculationModel.UNKNOWN,
            interestData = null,
            fineData = null,
            discountData = null,
            abatement = null,
            amountPaid = null,
            idNumber = null,
            divergentPayment = null,
            partialPayment = null,
        ),
    )

    private fun CelcoinAuthorizeResponseTO.toFichaValidationResponse(): CelcoinBillValidationResponse {
        if (registerData == null) {
            throw BoletoSettlementException(FinancialServiceGateway.CELCOIN, "registerData is null")
        }
        val recipient = Recipient(
            name = registerData.recipient ?: "",
            document = unmaskDocument(registerData.documentRecipient),
            alias = "",
            bankAccount = null,
        )
        return CelcoinBillValidationResponse(
            transactionId = transactionId,
            gatewayResponseCode = errorCode,
            errorDescription = message,
            billRegisterData = BillRegisterData(
                toBillType(type),
                assignor = assignor,
                recipient = recipient,
                recipientChain = null,
                payerDocument = registerData.documentPayer?.let { unmaskDocument(it) },
                amount = convertToLong(registerData.originalValue.toString()),
                discount = convertToLong(registerData.discountValue.toString()),
                interest = convertToLong(registerData.interestValueCalculated.toString()),
                fine = convertToLong(registerData.fineValueCalculated.toString()),
                amountTotal = convertToLong(registerData.totalUpdated.toString()),
                expirationDate = fromFormattedDueDate(registerData.payDueDate),
                dueDate = fromFormattedDueDate(registerData.dueDateRegister),
                paymentLimitTime = endHour,
                settleDate = settleDate?.let { fromFormattedSettleDate(it) },
                fichaCompensacaoType = FichaCompensacaoType.OUTROS,
                payerName = null,
                amountCalculationModel = AmountCalculationModel.UNKNOWN,
                interestData = null,
                fineData = null,
                discountData = null,
                abatement = null,
                amountPaid = null,
                idNumber = null,
                divergentPayment = DivergentPayment(
                    minimumAmount = convertToLong(registerData.minValue.toString()),
                    maximumAmount = convertToLong(registerData.maxValue.toString()),
                    amountType = when (registerData.allowChangeValue) {
                        true -> PartialPaymentAmountType.BETWEEN_MINIMUM_AND_MAXIMUM
                        else -> PartialPaymentAmountType.NOT_ACCEPT_DIFFERENT_VALUE
                    },
                ),
                partialPayment = null,
            ),
        )
    }

    internal fun convertToLong(value: String): Long {
        if (value.isBlank()) {
            throw NumberFormatException("Value must be not empty")
        }

        val nf = NumberFormat.getNumberInstance(Locale.US) as DecimalFormat
        nf.applyPattern("#.##")
        return (nf.parse(value).toDouble() * 100.0).roundToLong()
    }

    private fun CelcoinTransactionReceiptV5.toBillPaymentReceipt() = BillPaymentReceipt(
        createdOn = createDate,
        transactionId = transactionId,
        authentication = authenticationAPI.blocoCompleto,
        paymentPartnerName = convenant,
    )

    private fun Bill.toCelcoinAuthorizeRequestTO() = CelcoinAuthorizeRequestTO(
        externalTerminal = walletId.value,
        externalNSU = null,
        barcode = CelcoinBarcodeTO(
            type = toInteger(billType),
            digitable = barcode!!.digitable,
            barcode = null,
        ),
    )

    private fun CreateBoletoRequest.toCelcoinAuthorizeRequestTO() = CelcoinAuthorizeRequestTO(
        externalTerminal = this.walletId.value,
        externalNSU = null,
        barcode = CelcoinBarcodeTO(
            type = toInteger(this),
            digitable = barcode.digitable,
            barcode = null,
        ),
    )

    private fun BillView.toCelcoinAuthorizeRequestTO() = CelcoinAuthorizeRequestTO(
        externalTerminal = walletId.value,
        externalNSU = null,
        barcode = CelcoinBarcodeTO(
            type = toInteger(billType),
            digitable = barCode!!.digitable,
            barcode = null,
        ),
    )

    private fun buildCelcoinPaymentRequest(
        bill: Bill,
        nsu: Int,
        transactionId: String,
        payerDocument: String,
        payerName: String,
    ) = CelcoinPaymentRequestV5(
        externalNSU = nsu,
        externalTerminal = bill.walletId.value,
        cpfcnpj = payerDocument,
        billData = CelcoinBillDataTO(
            value = toCurrencyFormat(bill.amountTotal),
            originalValue = toCurrencyFormat(bill.amount),
        ),
        infoBearer = CelcoinInfoBearerTO(
            nameBearer = payerName,
            documentBearer = payerDocument,
        ),
        barCode = CelcoinBarcodeTO(
            type = toInteger(bill.billType),
            digitable = bill.barcode!!.digitable,
            barcode = null,
        ),
        dueDate = toFormattedDueDate(bill.dueDate),
        transactionIdAuthorize = transactionId,
    )

    private fun logHttpClientException(e: Exception, url: String, message: String) {
        logger.error(append("url", url), message, e)
    }

    private fun toCurrencyFormat(number: Long): Float {
        return (number / 100.0).toFloat()
    }

    private fun toFormattedDueDate(dueDate: LocalDate): String {
        val zone = ZoneId.of("Brazil/East")
        val dateTime = dueDate.atTime(17, 0) // celcoin needs a time so we chose an arbitrary time
        val zonedDateTime = ZonedDateTime.of(dateTime, zone)
        return DateTimeFormatter.ISO_INSTANT.format(zonedDateTime)
    }

    private fun fromFormattedDueDate(dueDate: String): LocalDate {
        val zone = ZoneId.of("UTC") // Celcoin uses wrong Zone for due date
        val result = ZonedDateTime.parse(dueDate, DateTimeFormatter.ISO_LOCAL_DATE_TIME.withZone(zone))
        return result.toLocalDate()
    }

    private fun fromFormattedSettleDate(settleDate: String): LocalDate {
        return LocalDate.parse(settleDate, DateTimeFormatter.ofPattern("dd/MM/yyyy"))
    }

    companion object {
        private val logger = LoggerFactory.getLogger(CelcoinAdapter::class.java)
    }
}

data class CelcoinAuthenticationAPI(
    @JsonProperty("Bloco1") val bloco1: String? = null,
    @JsonProperty("Bloco2") val bloco2: String? = null,
    @JsonProperty("BlocoCompleto") val blocoCompleto: String = "",
)

data class CelcoinAuthorizeRequestTO(val externalTerminal: String, val externalNSU: Int?, val barcode: CelcoinBarcodeTO)

data class CelcoinAuthorizeResponseTO(
    val assignor: String,
    val registerData: CelcoinRegisterDataTO?,
    val settleDate: String? = null,
    val dueDate: String? = null,
    val endHour: String? = null,
    val initeHour: String? = null,
    val nextSettle: String? = null,
    val transactionId: Long = 0,
    val type: Int = 0,
    val value: Double = 0.0,
    val minValue: Double = 0.0,
    val maxValue: Double = 0.0,
    val errorCode: String,
    val message: String?,
    val status: Int = 0,
    val digitable: String? = null,
)

data class CelcoinBarcodeTO(
    val type: Int = 0, // 0 - Undefined, 1 - Concessionaria, 2 - FichaCompensacao
    val digitable: String,
    val barcode: String? = null,
)

class CelcoinRegisterDataTO(
    val documentRecipient: String,
    val documentPayer: String? = null,
    val payDueDate: String,
    val nextBusinessDay: String? = null,
    val dueDateRegister: String,
    val allowChangeValue: Boolean? = null,
    val recipient: String? = null,
    val payer: String? = null,
    val discountValue: Double = 0.0,
    val interestValueCalculated: Double = 0.0,
    val maxValue: Double = 0.0,
    val minValue: Double = 0.0,
    val fineValueCalculated: Double = 0.0,
    val originalValue: Double = 0.0,
    val totalUpdated: Double = 0.0,
    val totalWithDiscount: Double = 0.0,
    val totalWithAdditional: Double = 0.0,
)

data class CelcoinTransactionTO(
    val authentication: Long = 0,
    val errorCode: String?,
    val createDate: String?,
    val message: String?,
    val externalNSU: Int,
    val transactionId: Long = 0,
    val status: Int = 0,
    val externalTerminal: String,
)

private data class CelcoinPaymentRequestV5(
    val externalNSU: Int,
    val externalTerminal: String,
    val cpfcnpj: String,
    val billData: CelcoinBillDataTO,
    val infoBearer: CelcoinInfoBearerTO,
    val barCode: CelcoinBarcodeTO,
    val dueDate: String,
    val transactionIdAuthorize: String,
)

private data class CelcoinInfoBearerTO(
    val nameBearer: String,
    val documentBearer: String,
    val methodPaymentCode: Int = 2, // debito em conta
)

data class CelcoinPaymentResponse(
    val assignor: String? = null,
    val registerData: CelcoinRegisterDataTO? = null,
    val settleDate: String? = null,
    val dueDate: String? = null,
    val endHour: String? = null,
    val initeHour: String? = null,
    val nextSettle: String? = null,
    val digitable: String? = null,
    val transactionId: Long = 0,
    val type: Int = 0,
    val value: Float = 0f,
    val minValue: Float = 0f,
    val maxValue: Float = 0f,
    val errorCode: String? = null,
    val message: String? = null,
    val status: Int = 0,
)

data class CelcoinConfirmRequest(
    val nsu: Long = 0,
    val externalNSU: Int,
    val externalTerminal: String,
)

data class CelcoinTransactionReceipt(
    val authentication: String? = null,
    val authenticationAPI: CelcoinAuthenticationAPI,
    val receiptformatted: String,
    val createDate: String,
    val externalNSU: String? = null,
    val transactionId: String,
    val externalTerminal: String? = null,
    val errorCode: String? = null,
    val message: String? = null,
    val status: String? = null,
)

data class CelcoinErrorResponse(
    val errorCode: String,
    val message: String,
)

data class CelcoinQueryPaymentResponseTO(
    val transaction: CelcoinTransactionTO,
    val errorCode: String,
    val message: String,
    val status: Int,
)

data class CelcoinBillValidationResponse(
    override var transactionId: Long = 0,
    val gatewayResponseCode: String,
    override val errorDescription: String?,
    override val billRegisterData: BillRegisterData?,
) : TransactionalValidationResponse, BillValidationResponse(
    gateway = FinancialServiceGateway.CELCOIN,
    billRegisterData = billRegisterData,
    bankTransactionId = transactionId.toString(),
    errorDescription = errorDescription,
    errorCode = gatewayResponseCode,
) {

    constructor(gatewayResponseCode: String, errorDescription: String) : this(
        gatewayResponseCode = gatewayResponseCode,
        errorDescription = errorDescription,
        billRegisterData = null,
    )

    override fun isRegistrationResponseOlderThan(registrationUpdateNumber: Long) = false

    override fun isValid() = VALID_CODE == gatewayResponseCode && !isPaymentLimitExpired()

    override fun alreadyPaid() = alreadyPaidCodes.contains(gatewayResponseCode)

    override fun isBarcodeNotFound() = false

    override fun paymentNotAuthorized() = gatewayResponseCode in PAYMENT_NOT_AUTHORIZED_CODES

    override fun notPayable() = notPayableCodes.contains(gatewayResponseCode) || isPaymentLimitExpired()

    override fun isRetryable() = payable()

    private fun payable() = !notPayable()
}

data class CelcoinBillDataTO(
    val value: Float = 0f,
    val originalValue: Float = 0f,
    val valueWithDiscount: Float = 0f,
    val valueWithAdditional: Float = 0f,
) {
    constructor(
        value: Float,
        originalValue: Float,
    ) : this(value = value, originalValue = originalValue, valueWithDiscount = 0f, valueWithAdditional = 0f)
}

object CelcoinBillTypeMapper {

    fun toInteger(billtype: BillType?): Int {
        return when (billtype) {
            BillType.CONCESSIONARIA -> 1
            BillType.FICHA_COMPENSACAO -> 2
            else -> 0
        }
    }

    fun toInteger(boleto: CreateBoletoRequest?): Int {
        return when (boleto) {
            is ConcessionariaRequest -> 1
            is CreateFichaDeCompensacaoRequest -> 2
            else -> 0
        }
    }

    fun toBillType(num: Int): BillType {
        return when (num) {
            1 -> BillType.CONCESSIONARIA
            2 -> BillType.FICHA_COMPENSACAO
            else -> BillType.OTHERS
        }
    }
}

private object Codes {
    val expectedErrorCodes = hashSetOf(
        "183", // Pagamento ja efetuado.
        "184", // PAGAMENTO NAO PERMITIDO
        "244", // RECEBIMENTO NAO AUTORIZADO. DOCUMENTO VENCIDO
        "628", // BOLETO DE PAGAMENTO JA BAIXADO
        "642", // CODIGO DE BARRAS NAO LOCALIZADO NA BASE CENTRALIZADA
        "634", // BAIXA OPERACIONAL EM DUPLICIDADE PARA BOLETO DE PAGAMENTO QUE NAO PERMITE PAGAMENTO PARCIAL
        "639", // BOLETO NAO PERMITIDO APOS DATA LIMITE DE PAGAMENTO
        "481", // CEDENTE NÃO AUTORIZADO
        "619", // CEDENTE NÃO AUTORIZADO
        "820", // Nao e possivel pagar esse tributo. Data de liquidacao posterior a data de vencimento.
    )

    val expectedErrorMessages = hashSetOf(
        "Não é possível realizar pagamento de concessionárias com valor zerado.",
    )

    // 050 EXCEDE LIMITE
    // 171 FALHA NA COMUNIC COM A INSTITUICAO. FAVOR TENTE NOVAMENTE.
    // 701 OCORREU UM ERRO AO REALIZAR A TRANSACAO, FAVOR TENTE NOVAMENTE.
    val failureErrorCodes = hashSetOf("050", "171", "701")

    val alreadyPaidCodes = hashSetOf("183", "634")

    val notPayableCodes = hashSetOf("244", "628", "639", "642", "820", "184")

    val PAYMENT_NOT_AUTHORIZED_CODES = setOf("481", "619", "184")
    const val VALID_CODE = "000"
    const val SHOULD_VERIFY_ERROR_CODE = "050"
    const val CELCOIN_TRANSACTION_NOT_EXIST = "061" // Transação não está pendente
    const val GENERIC_ERROR = "999" // Protocolo da transacao original nao eviado
}

private fun isFailureErrorCode(errorCode: String) = failureErrorCodes.contains(errorCode)

data class BankListTO(val banks: List<BankTO>)

data class BankTO(
    val institutionCode: Int,
    val description: String,
    val institutionName: String,
)

fun shouldVerifyErrorCode(errorCode: String) = errorCode == SHOULD_VERIFY_ERROR_CODE

private data class CelcoinBalanceTO(
    val anticipated: String,
    val reconcileExecuting: String,
    val consumed: String,
    val credit: String,
    val balance: String,
    val errorCode: String,
    val message: String,
    val status: String,
)