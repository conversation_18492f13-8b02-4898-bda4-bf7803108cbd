package ai.friday.billpayment.adapters.celcoin

import ai.friday.billpayment.app.feature.RequiresCelcoin
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import com.fasterxml.jackson.annotation.JsonProperty
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpAttributes
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.client.multipart.MultipartBody
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.time.Duration
import java.time.Instant
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

class CelcoinAccessTokenWrapper(
    private val celcoinAuth: CelcoinAuth,
    private var celcoinAccessToken: CelcoinAccessToken = expiredToken,
) {

    private val offset = Duration.ofMinutes(1)

    fun fetch(): CelcoinAccessToken {
        if (celcoinAccessToken.expiration.minus(offset).isBefore(getZonedDateTime())) {
            celcoinAccessToken = celcoinAuth.fetchAccessToken()
        }

        return celcoinAccessToken
    }

    fun invalidate() {
        celcoinAccessToken = expiredToken
    }

    companion object {
        private val expiredToken = CelcoinAccessToken("EXPIRED", expiration = Instant.EPOCH.atZone(brazilTimeZone))
    }
}

data class CelcoinAccessToken(
    val value: String,
    val expiration: ZonedDateTime,
)

@Singleton
@RequiresCelcoin
open class CelcoinAuth(
    @Client(value = "celcoin") private val httpClient: RxHttpClient,
    private val configuration: CelcoinAuthConfiguration,
) {

    open fun fetchAccessToken(): CelcoinAccessToken {
        val requestBody: MultipartBody = MultipartBody.builder()
            .addPart("client_id", configuration.clientId)
            .addPart("grant_type", configuration.grantType)
            .addPart("client_secret", configuration.clientSecret)
            .build()

        val request = HttpRequest.POST(configuration.path, requestBody)
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        request.setAttribute(HttpAttributes.URI_TEMPLATE, configuration.path)

        val call = httpClient.retrieve(
            request,
            Argument.of(CelcoiAuthResponseTO::class.java),
            Argument.STRING,
        )

        try {
            val response = call.firstOrError().blockingGet()
            return CelcoinAccessToken(
                value = response.accessToken,
                expiration = getZonedDateTime().plusSeconds(response.expiresIn),
            )
        } catch (e: HttpClientResponseException) {
            logger.error(Markers.append("response", e.response.getBody(String::class.java)), "CelcoinFetchAccessToken")
            throw e
        } catch (e: Exception) {
            logger.error("CelcoinFetchAccessToken", e)
            throw e
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(CelcoinAuth::class.java)
    }
}

class CelcoiAuthResponseTO(
    @JsonProperty("access_token") val accessToken: String,
    @JsonProperty("expires_in") val expiresIn: Long,
    @JsonProperty("token_type") val tokenType: String,
)

@RequiresCelcoin
@ConfigurationProperties("integrations.celcoin.token")
class CelcoinAuthConfiguration @ConfigurationInject constructor(
    val path: String,
    val clientId: String,
    val grantType: String,
    val clientSecret: String,
)