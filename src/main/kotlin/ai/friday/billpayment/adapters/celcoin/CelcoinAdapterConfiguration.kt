package ai.friday.billpayment.adapters.celcoin

import ai.friday.billpayment.app.feature.RequiresCelcoin
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton

@Singleton
@RequiresCelcoin
open class CelcoinAdapterConfiguration {
    @field:Property(name = "integrations.celcoin.host")
    lateinit var host: String

    @field:Property(name = "integrations.celcoin.authorize.path")
    lateinit var authorizePath: String

    @field:Property(name = "integrations.celcoin.payment.path")
    lateinit var paymentPath: String

    @field:Property(name = "integrations.celcoin.balance.path")
    lateinit var balancePath: String

    @field:Property(name = "integrations.celcoin.capture.path")
    lateinit var capturePath: String

    @field:Property(name = "integrations.celcoin.void.path")
    lateinit var voidPath: String

    @field:Property(name = "integrations.celcoin.query.path")
    lateinit var queryPath: String

    @field:Property(name = "integrations.celcoin.pendency.path")
    lateinit var pendencyPath: String

    @field:Property(name = "integrations.celcoin.receipt.path")
    lateinit var receiptPath: String

    @field:Property(name = "integrations.celcoin.banktransfer.path")
    lateinit var bankTransferPath: String

    @field:Property(name = "integrations.celcoin.transferstatus.path")
    lateinit var bankTransferStatusPath: String

    @field:Property(name = "integrations.celcoin.revertboletotransaction.path")
    lateinit var revertBoletoTransactionPath: String

    @field:Property(name = "integrations.celcoin.banks.path")
    lateinit var banksPath: String

    @field:Property(name = "integrations.celcoin.username")
    lateinit var username: String

    @field:Property(name = "integrations.celcoin.password")
    lateinit var password: String
}