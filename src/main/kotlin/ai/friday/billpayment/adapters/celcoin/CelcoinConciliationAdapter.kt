package ai.friday.billpayment.adapters.celcoin

import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.conciliation.BoletoOccurrence
import ai.friday.billpayment.app.feature.RequiresCelcoin
import ai.friday.billpayment.app.integrations.BoletoOccurrenceAdapter
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.log.andAppend
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpAttributes
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlin.math.roundToLong
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@RequiresCelcoin
@Singleton
class CelcoinConciliationAdapter(
    @Client("celcoin") private val httpClient: RxHttpClient,
    private val configuration: CelcoinConciliationAdapterConfiguration,
    celcoinAuth: CelcoinAuth,
) : BoletoOccurrenceAdapter {
    private val celcoinAccessTokenWrapper = CelcoinAccessTokenWrapper(celcoinAuth)

    override fun getOccurrences(
        startDate: LocalDate,
        endDate: LocalDate,
    ): List<BoletoOccurrence> {
        val markers = Markers.append("startDate", startDate)
            .andAppend("endDate", endDate)

        try {
            val httpRequest = HttpRequest.GET<Unit>("/v5/transactions/occurrency")
                .bearerAuth(celcoinAccessTokenWrapper.fetch().value)
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            httpRequest.parameters.add("DataInicio", startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
            httpRequest.parameters.add("DataFim", endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))

            httpRequest.setAttribute(HttpAttributes.URI_TEMPLATE, "/v5/transactions/occurrency")

            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(CelcoinOccurrencesWrapperTO::class.java),
            )

            val response = call.firstOrError().blockingGet()

            markers.andAppend("response", response).andAppend("existeConta", response.occurrences.isNotEmpty())
            logger.info(markers, "CelcoinConciliationGetOccurrences")

            return response.occurrences.map { it.toBoletoConciliationOccurence() }
        } catch (e: HttpClientResponseException) {
            logger.error(
                markers.andAppend("statusCode", e.status).andAppend("response", e.response.getBody(String::class.java)),
                "CelcoinConciliationGetOccurrences",
                e,
            )

            if (e.status == HttpStatus.UNAUTHORIZED) {
                celcoinAccessTokenWrapper.invalidate()
            }
            throw e
        } catch (e: Exception) {
            logger.error(markers, "CelcoinConciliationGetOccurrences", e)
            throw e
        }
    }

    fun getFileTypes(): List<CelcoinConciliationFileType> {
        try {
            val httpRequest: HttpRequest<String> = HttpRequest.GET<String>(configuration.fileTypesPath)
                .bearerAuth(celcoinAccessTokenWrapper.fetch().value)
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.retrieve(
                httpRequest,
                Argument.listOf(CelcoinConciliationFileType::class.java),
            )
            return call.firstOrError().blockingGet()
        } catch (e: HttpClientResponseException) {
            logger.error("CelcoinConciliationGetFileTypes", e)

            if (e.status == HttpStatus.UNAUTHORIZED) {
                celcoinAccessTokenWrapper.invalidate()
            }
            throw e
        }
    }

    fun getFile(fileType: Int, accountDate: LocalDate, page: Int, quantity: Int): CelcoinConciliationFile {
        val markers = Markers.append("fileType", fileType)
            .andAppend("accountDate", accountDate)
            .andAppend("page", page)
            .andAppend("quantity", quantity)

        try {
            val httpRequest = HttpRequest.GET<String>(configuration.filePath)
                .bearerAuth(celcoinAccessTokenWrapper.fetch().value)
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            httpRequest.parameters.add("fileType", fileType.toString())
            httpRequest.parameters.add("accountDate", accountDate.format(DateTimeFormatter.ISO_DATE))
            httpRequest.parameters.add("page", page.toString())
            httpRequest.parameters.add("quantity", quantity.toString())

            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(CelcoinConciliationFile::class.java),
            )
            return call.firstOrError().blockingGet().also {
                markers.andAppend("pagination", it.pagination)
                logger.info(markers, "CelcoinConciliationGetFile")
            }
        } catch (e: HttpClientResponseException) {
            logger.error("CelcoinConciliationGetFile", e)

            if (e.status == HttpStatus.UNAUTHORIZED) {
                celcoinAccessTokenWrapper.invalidate()
            }
            throw e
        }
    }

    fun getConsolidatedStatement(
        startDate: LocalDate,
        endDate: LocalDate,
        page: Int,
        quantity: Int,
    ): CelcoinConsolidatedStatement {
        val markers = Markers.append("startDate", startDate)
            .andAppend("endDate", endDate)
            .andAppend("page", page)
            .andAppend("quantity", quantity)

        try {
            val httpRequest = HttpRequest.GET<String>(configuration.consolidatedStatement)
                .bearerAuth(celcoinAccessTokenWrapper.fetch().value)
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            httpRequest.parameters.add("startDate", startDate.format(DateTimeFormatter.ISO_DATE))
            httpRequest.parameters.add("endDate", endDate.format(DateTimeFormatter.ISO_DATE))
            httpRequest.parameters.add("page", page.toString())
            httpRequest.parameters.add("quantity", quantity.toString())

            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(CelcoinConsolidatedStatement::class.java),
            )
            return call.firstOrError().blockingGet().also {
                markers.andAppend("pagination", it.pagination)
                    .andAppend("balance", it.balance)
                logger.info(markers, "CelcoinConciliationGetConsolidatedStatement")
            }
        } catch (e: HttpClientResponseException) {
            logger.error(markers, "CelcoinConciliationGetConsolidatedStatement", e)

            if (e.status == HttpStatus.UNAUTHORIZED) {
                celcoinAccessTokenWrapper.invalidate()
            }
            throw e
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(CelcoinConciliationAdapter::class.java)
    }
}

data class CelcoinConciliationFileType(
    val fileType: Int,
    val description: String,
)

data class CelcoinConciliationFile(
    val movement: List<Map<String, Any>>,
    val pagination: CelcoinPagination,
)

data class CelcoinConsolidatedStatement(
    val statement: List<Map<String, Any>>,
    val balance: CelcoinConsolidatedStatementBalance,
    val pagination: CelcoinPagination,
)

data class CelcoinConsolidatedStatementBalance(
    val balanceStartDate: Float,
    val balanceEndDate: Float,
)

data class CelcoinPagination(
    val page: Int,
    val totalCount: Int,
    val totalPages: Int,
    val hasPrevious: Boolean,
    val hasNext: Boolean,
)

@ConfigurationProperties("integrations.celcoin.conciliation")
class CelcoinConciliationAdapterConfiguration @ConfigurationInject constructor(
    val fileTypesPath: String,
    val filePath: String,
    val consolidatedStatement: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class CelcoinOccurrencesWrapperTO(
    val occurrences: List<CelcoinOccurrenceTO>,
    val errorCode: String,
    val message: String,
    val status: Long,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class CelcoinOccurrenceTO(
    val date: String,
    val createDate: String,
    val descriptionMotivo: String,
    val externalNSU: String,
    val transactionId: Long,
    val externalTerminal: String,
    val linhaDigitavel: String,
    val value: Double,
)

fun CelcoinOccurrenceTO.toBoletoConciliationOccurence() = BoletoOccurrence(
    walletId = WalletId(externalTerminal),
    nsu = externalNSU,
    date = LocalDateTime.parse(date, DateTimeFormatter.ISO_LOCAL_DATE_TIME).atZone(brazilTimeZone),
    createDate = LocalDateTime.parse(createDate, DateTimeFormatter.ISO_LOCAL_DATE_TIME).atZone(brazilTimeZone),
    reason = descriptionMotivo,
    externalTransactionId = transactionId.toString(),
    barCode = BarCode.ofDigitable(linhaDigitavel),
    amount = (value * 100.0).roundToLong(),
    financialServiceGateway = FinancialServiceGateway.CELCOIN,
)