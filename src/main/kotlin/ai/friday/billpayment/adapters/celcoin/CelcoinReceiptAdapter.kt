package ai.friday.billpayment.adapters.celcoin

import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.feature.RequiresCelcoin
import ai.friday.billpayment.app.payment.BoletoSettlementException
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.boletoSettlementTransactionId
import ai.friday.billpayment.app.settlement.receipt.adapter.GetBoletoSettlementReceiptAdapter
import ai.friday.billpayment.app.settlement.receipt.adapter.GetBoletoSettlementReceiptResult
import ai.friday.billpayment.log
import ai.friday.billpayment.markers
import ai.friday.morning.log.andAppend
import io.micronaut.http.HttpAttributes
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Named
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory

@RequiresCelcoin
@Singleton
@Named("celcoinReceiptAdapter")
open class CelcoinReceiptAdapter(
    @Client("celcoin") private val httpClient: RxHttpClient,
    private val configuration: CelcoinAdapterConfiguration,
    celcoinAuth: CelcoinAuth,
) : GetBoletoSettlementReceiptAdapter {

    private val celcoinAccessTokenWrapper = CelcoinAccessTokenWrapper(celcoinAuth)

    override fun getReceipt(transaction: Transaction): GetBoletoSettlementReceiptResult {
        val bankTransactionId = transaction.boletoSettlementTransactionId()

        val url = configuration.receiptPath.replace("{transactionId}", bankTransactionId)
        val markers = log("url" to url, "transactionId" to bankTransactionId, "method" to "get_receipt")

        return try {
            val httpRequest = HttpRequest.GET<Any>(url)
                .bearerAuth(celcoinAccessTokenWrapper.fetch().value)
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            httpRequest.setAttribute(HttpAttributes.URI_TEMPLATE, configuration.receiptPath)

            val call = httpClient.exchange(httpRequest, CelcoinTransactionReceiptV5::class.java)
            val response = call.firstOrError().blockingGet()

            markers.add(response.markers())

            if (response.status != HttpStatus.OK) {
                logger.warn(
                    markers.andAppend("CelcoinTransactionReceipt", response.getBody(String::class.java)),
                    "CelcoinTransactionPendency",
                )
                throw BoletoSettlementException(FinancialServiceGateway.CELCOIN, null, null)
            }
            val receipt = response.body()

            markers.andAppend("receipt", receipt)

            if (receipt == null || receipt.authenticationAPI.blocoCompleto.isBlank()) {
                logger.error(markers, "CelcoinTransactionReceipt")
                return GetBoletoSettlementReceiptResult.Error(FinancialServiceGateway.CELCOIN, "Receipt cannot be blank")
            }

            logger.info(markers, "CelcoinTransactionReceipt")

            GetBoletoSettlementReceiptResult.Success(
                gateway = FinancialServiceGateway.CELCOIN,
                createdOn = receipt.createDate,
                transactionId = receipt.transactionId,
                authentication = receipt.authenticationAPI.blocoCompleto,
                paymentPartnerName = receipt.convenant,
            )
        } catch (e: HttpClientResponseException) {
            logger.error(markers.and(e.markers()), "CelcoinTransactionReceipt", e)

            if (e.status == HttpStatus.UNAUTHORIZED) {
                celcoinAccessTokenWrapper.invalidate()
            }

            return GetBoletoSettlementReceiptResult.Error(gateway = FinancialServiceGateway.CELCOIN, exception = e)
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(CelcoinReceiptAdapter::class.java)
    }
}