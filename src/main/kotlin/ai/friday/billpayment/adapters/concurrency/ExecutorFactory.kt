package ai.friday.billpayment.adapters.concurrency

import io.micronaut.context.annotation.Factory
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.util.concurrent.ExecutorService
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import reactor.core.scheduler.Scheduler
import reactor.core.scheduler.Schedulers

@Factory
class ExecutorFactory {

    @Named("jobExecutor")
    @Singleton
    fun jobExecutor(): ExecutorService = ThreadPoolExecutor(
        5,
        200,
        1,
        TimeUnit.MINUTES,
        LinkedBlockingQueue(),
    )
}

fun ExecutorService.scheduler(): Scheduler = Schedulers.fromExecutor(this)