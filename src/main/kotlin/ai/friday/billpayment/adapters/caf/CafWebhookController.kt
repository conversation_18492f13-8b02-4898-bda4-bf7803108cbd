package ai.friday.billpayment.adapters.caf

import ai.friday.billpayment.app.FridayMePoupe
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Controller("/caf")
@Secured(SecurityRule.IS_ANONYMOUS)
@FridayMePoupe
class CafWebhookController {

    @Post("/webhooks/consulta")
    fun consultaWebhook(@Body body: String): HttpResponse<*> {
        val logName = "CafWebhookController#consultaWebhook"
        val markers = Markers.append("request", body)
        logger.info(markers, logName)

        try {
            return HttpResponse.ok<Unit>()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return HttpResponse.ok<Unit>()
        }
    }

    @Post("/webhooks/perfil")
    fun perfilWebhook(@Body body: String): HttpResponse<*> {
        val logName = "CafWebhookController#perfilWebhook"
        val markers = Markers.append("request", body)
        logger.info(markers, logName)

        try {
            return HttpResponse.ok<Unit>()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return HttpResponse.ok<Unit>()
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(CafWebhookController::class.java)
    }
}