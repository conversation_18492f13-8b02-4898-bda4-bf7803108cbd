package ai.friday.billpayment.adapters.arbi

class ArbiLoginException : RuntimeException()
open class ArbiAdapterException : RuntimeException()
open class ArbiAdapterIncomeReportException(message: String? = null) : RuntimeException(message)
class ArbiDDAPayerNotFoundException : RuntimeException()
class ArbiBankAccountException : RuntimeException()
class ArbiCheckTransferException : RuntimeException()
class ArbiPixKeyAddException : RuntimeException()
class ArbiInvalidAccountException : ArbiAdapterException()
class ArbiAccountMissingPermissionsException : ArbiAdapterException()
class ArbiCloseAccountException(message: String?) : RuntimeException(message)
class ArbiSetNaturalPersonStatusException(message: String?) : RuntimeException(message)
class ArbiInvalidBalanceException(message: String?) : RuntimeException(message)