package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.integrations.TEDService
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.DirectTEDResult
import ai.friday.billpayment.app.payment.DirectTEDStatus
import ai.friday.billpayment.app.payment.DirectTEDUndoneResult
import ai.friday.billpayment.app.payment.SettlementStatus
import ai.friday.billpayment.app.payment.TEDResult
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.isCPF
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.context.annotation.Secondary
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.Optional
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

private const val ARBI_BANK_NO = "213"
private const val ARBI_ROUTING_NO = "00019"

private val arbiTEDFailuresMap = mapOf(
    "Saldo disponivel insuficiente." to DirectTEDStatus.Failure.INSUFFICIENT_FUNDS,
    "Hora/Minuto do agendamento fora do Limte para TED." to DirectTEDStatus.Failure.AFTER_HOURS,
    "Banco destino não cadastrado e/ou invalido." to DirectTEDStatus.Failure.INVALID_BANK_DATA,
)

/*
1-Conta Destinatária do Crédito Encerrada
2-Agência ou Conta Destinatária do Crédito Inválida
3-Ausência ou Divergência na Indicação do CPF/CNPJ
5-Divergência na Titularidade
31-CPF/CNPJ inapto junto à Receita Federal do Brasil
84-Conta destinatária do crédito inválida para o tipo de transação ou finalidade
 */
private val arbiTEDUndoneInvalidDataList = listOf("1", "2", "3", "5", "31", "84")

/*
61-Transferência supera limite para o tipo de conta destino
*/
private val arbiTEDUndoneNotAllowedOperationList = listOf("61")
private const val arbiUndoneStatus = "Devolvida"

@Singleton
@Secondary
@FridayMePoupe
class ArbiTedAdapter(
    @param:Client(
        id = "arbi",
    ) private val httpClient: RxHttpClient,
    private val configuration: ArbiConfiguration,
    private val authenticationManager: NewArbiAuthenticationManager,
) : TEDService {

    override fun transfer(originAccountNo: String, recipient: Recipient, totalAmount: Long): DirectTEDResult {
        val operationId = BankOperationId.build()
        val dataAgendamento = getScheduleDate()
        val request = CheckingRequestTO(
            contaCorrente = ContaCorrenteTO(
                inscricaoParceiro = configuration.inscricao,
                idRequisicao = IdRequisicao(operationId.value),
                tokenUsuario = configuration.userToken,
                idModulo = "1",
                idTransacao = "2",
                contaOrigem = originAccountNo,
                bancoDestino = recipient.bankAccount!!.bankNo!!.toString(),
                agenciaDestino = recipient.bankAccount.routingNo.toString(),
                contaDestino = recipient.bankAccount.buildFullAccountNumber(),
                tipoContaCreditada = if (recipient.bankAccount.accountType == AccountType.SAVINGS) "PP" else "CC",
                cnpjCpfCliCred = recipient.document!!,
                nomeCliCred = recipient.name,
                tipoPessoaCliCred = if (isCPF(recipient.document)) "F" else "J",
                finalidade = "10",
                dataAgendamento = dataAgendamento,
                valor = convertToString(totalAmount),
            ),
        )
        val marker = Markers.append("request", request)

        try {
            val token = authenticationManager.getToken()
            val httpRequest = HttpRequest.POST(configuration.checkingV2Path, request)
                .header("client_id", configuration.clientId)
                .header("access_token", token)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON_TYPE)
            val call = httpClient.retrieve(
                httpRequest,
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.listOf(ArbiResponseTO::class.java),
            )

            val response = call.firstOrError().blockingGet()

            logger.info(marker.and(Markers.append("response", response)), "ArbiTEDTransfer")
            return DirectTEDResult(
                gateway = FinancialServiceGateway.ARBI,
                amount = totalAmount,
                status = DirectTEDStatus.Success,
                operationId = operationId,
                authentication = response.first().resultado,
                settleDate = LocalDate.parse(getScheduleDate(), DateTimeFormatter.ISO_LOCAL_DATE),
            )
        } catch (e: HttpClientResponseException) {
            marker.and<LogstashMarker>(Markers.append("httpStatus", e.status.code))
            if (isUnauthorizedBankTransfer(e)) {
                return DirectTEDResult(
                    gateway = FinancialServiceGateway.ARBI,
                    amount = totalAmount,
                    status = DirectTEDStatus.Error,
                    operationId = operationId,
                    authentication = null,
                )
            }

            val successRange = 200..299
            if (e.status.code in successRange) {
                return unknownTEDTransferResponse(marker, e, operationId, totalAmount)
            }

            val optResponse = readArbiExceptionResponse(marker, e)
            if (optResponse.isPresent) {
                val response = optResponse.get()
                val mappedFailure = response.filter { it.descricaoStatus in arbiTEDFailuresMap.keys }
                    .map { arbiTEDFailuresMap[it.descricaoStatus] }.singleOrNull()
                return when {
                    mappedFailure != null -> {
                        logger.warn(marker.and(Markers.append("response", response)), "ArbiTEDTransfer")
                        DirectTEDResult(
                            gateway = FinancialServiceGateway.ARBI,
                            amount = totalAmount,
                            status = mappedFailure,
                            operationId = operationId,
                            authentication = null,
                        )
                    }

                    e.status.code == 504 || response.any { item -> item.resultado == "1 - Timeout alcancado 0" } -> {
                        logger.error(marker.and(Markers.append("response", response)), "ArbiTEDTransfer")
                        DirectTEDResult(
                            gateway = FinancialServiceGateway.ARBI,
                            amount = totalAmount,
                            status = DirectTEDStatus.Unknown,
                            operationId = operationId,
                            authentication = null,
                        )
                    }

                    else -> {
                        logger.error(marker.and(Markers.append("response", response)), "ArbiTEDTransfer")
                        DirectTEDResult(
                            gateway = FinancialServiceGateway.ARBI,
                            amount = totalAmount,
                            status = DirectTEDStatus.Error,
                            operationId = operationId,
                            authentication = null,
                        )
                    }
                }
            }
            return unknownTEDTransferResponse(marker, e, operationId, totalAmount)
        } catch (e: Exception) {
            return unknownTEDTransferResponse(marker, e, operationId, totalAmount)
        }
    }

    override fun checkSettlementStatus(transaction: Transaction): SettlementStatus {
        val token = authenticationManager.getToken()
        val request = ConsultaRequisicaoTEDIntegradaRequestTO(
            consultaRequisicaoTEDIntegrada = ConsultaRequisicaoTEDIntegrada(
                inscricaoParceiro = configuration.inscricao,
                tokenUsuario = configuration.userToken,
                contaOrigem = (transaction.paymentData.toSingle().accountPaymentMethod.method as InternalBankAccount).buildFullAccountNumber(),
                idRequisicaoIntegrada = transaction.settlementData.getOperation<BankTransfer>().operationId.value,
            ),
        )
        val marker = Markers.append("request", request)

        val httpRequest = HttpRequest.POST(configuration.tedStatusV2Path, request)
            .header("client_id", configuration.clientId)
            .header("access_token", token)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        val call = httpClient.exchange(
            httpRequest,
            Argument.of(ArbiResponseTO::class.java),
            Argument.listOf(ArbiResponseTO::class.java),
        )

        try {
            val response = call.firstOrError().blockingGet().body()
            val authentication = response?.resultado

            if (authentication.isNullOrEmpty()) {
                return SettlementStatus.Error
            }

            logger.info(marker.and(Markers.append("response", response)), "ArbiTEDCheckSettlementStatus")
            return SettlementStatus.Success(authentication)
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)
            val responseBody = e.response.getBody(Argument.STRING).orElse("")
            logger.error(marker.and(Markers.append("response", responseBody)), "ArbiTEDCheckSettlementStatus")
            return SettlementStatus.Failure(responseBody)
        } catch (e: Exception) {
            logger.error(marker, "ArbiTEDCheckSettlementStatus", e)
            return SettlementStatus.Error
        }
    }

    override fun checkTEDUndone(
        amount: Long,
        startDate: LocalDate,
        endDate: LocalDate,
        originAccountNo: String,
    ): List<DirectTEDUndoneResult> {
        val token = authenticationManager.getToken()
        val request = CheckingRequestTO(
            contaCorrente = ContaCorrenteTO(
                inscricaoParceiro = configuration.inscricao,
                tokenUsuario = configuration.userToken,
                idModulo = "1",
                idTransacao = "5",
                contaOrigem = originAccountNo,
                dataInicial = startDate.format(DateTimeFormatter.ISO_DATE),
                dataAgendamento = getScheduleDate(),
                dataFinal = endDate.format(DateTimeFormatter.ISO_DATE),
            ),
        )
        val marker: LogstashMarker = Markers.append("request", request).and(Markers.append("amount", amount))
        val httpRequest = HttpRequest.POST(configuration.checkingV2Path, request)
            .header("client_id", configuration.clientId)
            .header("access_token", token)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        val call = httpClient.exchange(
            httpRequest,
            Argument.listOf(ArbiResponseTO::class.java),
            Argument.listOf(String::class.java),
        )
        try {
            val httpResponse = call.firstOrError().blockingGet()
            val response = httpResponse.getBody(Argument.listOf(ArbiResponseTO::class.java)).get()
            logger.info(marker.and(Markers.append("response", response)), "ArbiCheckTransfer")
            if (httpResponse.status == HttpStatus.NO_CONTENT) { // Isso é o comportamento do gateway antigo
                return listOf()
            }
            if (response.first().resultado?.startsWith("Sem requisicões") == true) { // Isso é o comportamento do gateway novo
                return listOf()
            }
            return response.map {
                jacksonObjectMapper().readerFor(TEDUndoneTO::class.java).readValue<TEDUndoneTO>(it.resultado)
            }
                .filter { it.statusLiquidacao == arbiUndoneStatus && convertToLong(it.valor) == amount }
                .map {
                    val status = when (it.motivoDevolucao.split("-")[0]) {
                        in arbiTEDUndoneInvalidDataList -> DirectTEDStatus.Failure.INVALID_BANK_DATA
                        in arbiTEDUndoneNotAllowedOperationList -> DirectTEDStatus.Failure.NOT_ALLOWED
                        else -> DirectTEDStatus.Error
                    }
                    DirectTEDUndoneResult(
                        amount = amount,
                        status = status,
                        operationId = BankOperationId(value = it.idRequisicaoParceiroOriginal),
                    )
                }
        } catch (e: HttpClientResponseException) {
            if (e.status == HttpStatus.UNAUTHORIZED) {
                logger.warn(Markers.append("error_message", "Token is expired"), "ArbiValidate")
                authenticationManager.cleanTokens()
            } else {
                logger.error(
                    marker.and<LogstashMarker>(
                        Markers.append(
                            "response",
                            e.response.getBody(String::class.java).orElse(""),
                        ),
                    )
                        .and(Markers.append("status", e.status)),
                    "ArbiCheckTransfer",
                )
            }
            return listOf()
        } catch (e: Exception) {
            logger.error(marker, "ArbiCheckTransfer", e)
            return listOf()
        }
    }

    override fun transfer(
        recipient: Recipient,
        totalAmount: Long,
        externalTerminal: String,
        externalNsu: Long,
    ): TEDResult {
        TODO("Not yet implemented")
    }

    // duplicado
    private fun isUnauthorizedBankTransfer(e: HttpClientResponseException): Boolean {
        if (e.status == HttpStatus.UNAUTHORIZED) {
            logger.warn(Markers.append("error_message", "Token is expired"), "ArbiValidate")
            authenticationManager.cleanTokens()
            return true
        }
        return false
    }

    private fun unknownTEDTransferResponse(
        marker: LogstashMarker,
        e: Exception,
        operationId: BankOperationId,
        amount: Long,
    ): DirectTEDResult {
        logger.error(marker, "ArbiTEDTransfer", e)
        return DirectTEDResult(
            gateway = FinancialServiceGateway.ARBI,
            amount = amount,
            status = DirectTEDStatus.Unknown,
            operationId = operationId,
            authentication = null,
        )
    }

    private fun checkUnauthorized(e: HttpClientResponseException) {
        if (e.status == HttpStatus.UNAUTHORIZED) {
            logger.warn(Markers.append("error_message", "Token is expired"), "ArbiValidate")
            authenticationManager.cleanTokens()
            throw ArbiLoginException()
        }
    }

    private fun readArbiExceptionResponse(
        marker: LogstashMarker,
        httpClientResponseException: HttpClientResponseException,
    ): Optional<MutableList<ArbiResponseTO>> {
        return try {
            httpClientResponseException.response.getBody(Argument.listOf(ArbiResponseTO::class.java))
        } catch (e: Exception) {
            val body = httpClientResponseException.response.getBody(String::class.java).orElse("")
            logger.error(
                marker.and<LogstashMarker>(Markers.append("status", httpClientResponseException.status))
                    .and(Markers.append("body", body)),
                "ArbiTransfer",
                e,
            )
            throw e
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ArbiTedAdapter::class.java)
    }
}

data class ConsultaRequisicaoTEDIntegradaRequestTO(
    @JsonProperty("consultarequisicaotedintegrada") val consultaRequisicaoTEDIntegrada: ConsultaRequisicaoTEDIntegrada,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class ConsultaRequisicaoTEDIntegrada(
    @JsonProperty("inscricaoparceiro") val inscricaoParceiro: String,
    @JsonProperty("tokenusuario") val tokenUsuario: String,
    @JsonProperty("idrequisicao") val idRequisicao: IdRequisicao = IdRequisicao(),
    @JsonProperty("idmodulo") val idModulo: String = "1",
    @JsonProperty("idtransacao") val idTransacao: String = "9",
    @JsonProperty("bancoorigem") val bancoOrigem: String = ARBI_BANK_NO,
    @JsonProperty("agenciaorigem") val agenciaOrigem: String = ARBI_ROUTING_NO,
    @JsonProperty("contaorigem") val contaOrigem: String,
    @JsonProperty("idrequisicaointegrada") val idRequisicaoIntegrada: String,
)

data class TEDUndoneTO(
    @JsonProperty("dataCadastro") val dataCadastro: String,
    @JsonProperty("idrequisicaoarbioriginal") val idRequisicaoArbiOriginal: String,
    @JsonProperty("idrequisicaoparceirooriginal") val idRequisicaoParceiroOriginal: String,
    @JsonProperty("statusliquidacao") val statusLiquidacao: String,
    @JsonProperty("motivodevolucao") val motivoDevolucao: String,
    @JsonProperty("valor") val valor: String,
    @JsonProperty("historico") val historico: String,
    @JsonProperty("nroMovDeb") val numeroMovimentoDebito: String,
    @JsonProperty("nroMovCred") val numeroMovimentoCredito: String,
)