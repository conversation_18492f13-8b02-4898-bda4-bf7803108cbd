package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.adapters.parsers.parseListFrom
import ai.friday.billpayment.andAppendMasked
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.fingerprint.DeviceId
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyAlreadyExistsException
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyDetailsResult
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixKeyException
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixKeyOwner
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.cache.annotation.CacheConfig
import io.micronaut.cache.annotation.Cacheable
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpMethod
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.uri.UriBuilder
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

const val ARBI_CHAVE_NAO_ENCONTRADA = 107
const val ARBI_NOT_FOUND = 404
const val ARBI_CHAVE_REIVINDICADA = 7
const val SISTEMA_INDISPONIVEL = 16
const val DEVICE_ID_REQUIRED = 9991
const val DEVICE_ID_NOT_FOUND = 9992
const val DEVICE_ID_BLOCKED = 9993

@ConfigurationProperties("integrations.arbi")
class NewArbiPixKeyManagementConfiguration {
    lateinit var userToken: String
    lateinit var clientId: String
    lateinit var clientSecret: String
    lateinit var pixKeyV2Path: String
    lateinit var qrCodeProcessPath: String
    lateinit var findPixKeyV2Path: String
    lateinit var findInternalPixKeysPath: String
    lateinit var claimPath: String
    lateinit var cancelClaimPath: String
    lateinit var confirmClaimPath: String
    lateinit var listClaimsPath: String
    lateinit var completeClaimPath: String
    lateinit var createPixQrCodePath: String
    lateinit var inscricao: String
}

@Singleton
@CacheConfig("new-pix-keys")
@Requirements(
    Requires(notEnv = ["staging"]),
    Requires(beans = [ArbiAdapter::class]),
)
open class NewArbiPixKeyManagementAdapter(
    @param:Client(
        id = "arbi",
    ) private val httpClient: RxHttpClient,
    private val configuration: NewArbiPixKeyManagementConfiguration,
    private val authenticationManager: NewArbiAuthenticationManager,
) : PixKeyManagement {

    private fun <T> createHttpRequest(httpMethod: HttpMethod, uri: String) = HttpRequest.create<T>(httpMethod, uri)
        .header("client_id", configuration.clientId)
        .header("access_token", authenticationManager.getToken())
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON_TYPE)

    override fun registerKey(accountNo: AccountNumber, key: PixKey, document: String, name: String, deviceId: DeviceId?): PixKey {
        val request = RegisterKeyTO(
            chave = key.value,
            tipoChave = key.type.name,
            conta = accountNo.fullAccountNumber.padStart(10, '0'),
            cpfCnpj = document,
            agencia = "0001",
            instituicao = "********",
            nome = name,
            nomeFantasia = "",
            reason = "USER_REQUESTED",
            tipoConta = "CACC",
        )
        val httpRequest = createHttpRequest<RegisterKeyTO>(HttpMethod.POST, configuration.pixKeyV2Path)
            .body(request)

        deviceId?.let {
            httpRequest.header("x-device-id", it.value)
        }

        val call = httpClient.retrieve(
            httpRequest,
            Argument.listOf(ResponseTO::class.java),
            Argument.STRING,
        )
        val marker = Markers.append("request", request)
            .andAppendMasked("deviceId", deviceId?.value)

        try {
            val response = call.firstOrError().blockingGet()

            val mappedResponse =
                jacksonObjectMapper().readerFor(Map::class.java).readValue<Map<String, String>>(response[0].response)

            logger.info(marker.andAppend("response", mappedResponse), "RegisterPIXKey")

            return mappedResponse["chave"]?.let { PixKey(value = it, type = key.type) } ?: TODO()
        } catch (e: HttpClientResponseException) {
            val response = e.response.getBody(String::class.java).get()
            if (response.contains("EXISTENTE_MESMO_DONO_E_PSP")) {
                throw PixKeyAlreadyExistsException()
            }

            if (response.contains("x-device-id é Requerido") ||
                response.contains("Dispositivo não encontrado") ||
                response.contains("O dispositivo se encontra BLOQUEADO ou INATIVO")
            ) {
                logger.error(
                    marker.andAppend("status", e.response.status)
                        .andAppend("ACTION", "VERIFY")
                        .andAppend("response", response),
                    "RegisterPIXKey",
                )
                throw ArbiPixKeyAddException()
            }

            logger.error(
                marker.andAppend("status", e.response.status)
                    .andAppend("response", response),
                "RegisterPIXKey",
                e,
            )
            throw ArbiPixKeyAddException()
        }
    }

    override fun deleteKey(key: String, document: Document, deviceId: DeviceId?): Either<PixKeyError, Unit> {
        val request = DeleteKeyTO(chave = key, codUsuario = document.value)

        val httpRequest = createHttpRequest<DeleteKeyTO>(
            HttpMethod.DELETE,
            configuration.pixKeyV2Path,
        )
            .body(request)

        deviceId?.let {
            httpRequest.header("x-device-id", it.value)
        }

        val call = httpClient.exchange(
            httpRequest,
            Argument.of(Unit::class.java),
            Argument.STRING,
        )
        return try {
            val response = call.firstOrError().blockingGet()
            logger.info(
                Markers.append("request", request)
                    .andAppend("responseStatus", response.status.code)
                    .andAppendMasked("deviceId", deviceId?.value),
                "DeletePIXKey",
            )
            Unit.right()
        } catch (e: HttpClientResponseException) {
            val optBody = e.response.getBody(String::class.java)
            if (optBody.isEmpty) {
                return Either.Left(PixKeyError.UnknownError)
            }
            val response = parseListFrom<ResponseTO>(optBody.get())
            val error = jacksonObjectMapper()
                .readerFor(ErroEnderecamentoTO::class.java)
                .readValue<ErroEnderecamentoTO>(response[0].response)

            return when (error.codErro) {
                ARBI_CHAVE_NAO_ENCONTRADA, ARBI_NOT_FOUND -> {
                    logger.warn(Markers.append("request", request).andAppend("pixKeyError", error.descricaoErro), "DeletePIXKey")
                    Either.Left(PixKeyError.KeyNotFound)
                }

                DEVICE_ID_BLOCKED,
                DEVICE_ID_REQUIRED,
                DEVICE_ID_NOT_FOUND,
                -> {
                    logger.error(
                        Markers.append("request", request)
                            .andAppend("response", response)
                            .andAppend("ACTION", "verify")
                            .andAppend("pixKeyError", error.descricaoErro),
                        "DeletePIXKey",
                    )
                    Either.Left(PixKeyError.UnknownError)
                }

                ARBI_CHAVE_REIVINDICADA -> {
                    logger.error(Markers.append("request", request).andAppend("pixKeyError", error.descricaoErro), "DeletePIXKey")
                    Either.Left(PixKeyError.MalformedKey)
                }

                SISTEMA_INDISPONIVEL -> {
                    logger.error(Markers.append("request", request).andAppend("pixKeyError", error.descricaoErro), "DeletePIXKey")
                    Either.Left(PixKeyError.SystemUnavailable)
                }

                else -> {
                    logger.error(Markers.append("request", request).andAppend("pixKeyError", error.descricaoErro), "DeletePIXKey")
                    Either.Left(PixKeyError.UnknownError)
                }
            }
        }
    }

    override fun findKeyDetails(key: PixKey, document: String): Either<PixKeyError, PixKeyDetailsResult> {
        val requestMap = mutableMapOf<String, Any>("key" to key.value, "cpfCnpj" to document)
        val uri = UriBuilder.of(configuration.findPixKeyV2Path)
            .expand(requestMap)
            .toString()
        val marker = Markers.append("request", requestMap)
        val httpRequest = createHttpRequest<Unit>(HttpMethod.GET, uri)
        val call = httpClient.retrieve(
            httpRequest,
            Argument.listOf(ResponseTO::class.java),
            Argument.listOf(ResponseTO::class.java),
        )

        try {
            val response = call.firstOrError().blockingGet()
            val keyDetailsResponse = jacksonObjectMapper()
                .readerFor(KeyDetailsResponseTO::class.java)
                .readValue<KeyDetailsResponseTO>(response[0].response)

            logger.info(marker.and(Markers.append("response", keyDetailsResponse)), "findKeyDetails")
            with(keyDetailsResponse) {
                val pixKey = PixKey(
                    value = chave,
                    type = PixKeyType.valueOf(tipoChave),
                )
                val accountNumber = AccountNumber(conta)
                val pixKeyHolder = PixKeyHolder(
                    accountNo = accountNumber.number,
                    accountDv = accountNumber.dv,
                    ispb = instituicao,
                    institutionName = nomePsp.trim(),
                    accountType = getAccountType(),
                    routingNo = agencia.toLong(),
                )
                val pixKeyOwner = PixKeyOwner(
                    name = nome.trim(),
                    document = cpfCnpj,
                )

                if (!confirmado) {
                    return Either.Left(PixKeyError.KeyNotConfirmed)
                }

                return Either.Right(
                    PixKeyDetailsResult(
                        e2e = keyDetailsResponse.endToEnd,
                        pixKeyDetails = PixKeyDetails(
                            key = pixKey,
                            holder = pixKeyHolder,
                            owner = pixKeyOwner,
                        ),
                        qrCodeInfo = null,
                    ),
                )
            }
        } catch (e: HttpClientResponseException) {
            val optBody = e.response.getBody(Argument.listOf(ResponseTO::class.java))
            if (optBody.isEmpty) {
                return Either.Left(PixKeyError.UnknownError)
            }
            val response = optBody.get()
            val markerResponse = marker.andAppend("response", response)
            val erroEnderecamento = jacksonObjectMapper()
                .readerFor(ErroEnderecamentoTO::class.java)
                .readValue<ErroEnderecamentoTO>(response[0].response)
            return when (erroEnderecamento.codErro) {
                ARBI_CHAVE_NAO_ENCONTRADA, ARBI_NOT_FOUND -> {
                    logger.info(markerResponse, "findKeyDetails")
                    Either.Left(PixKeyError.KeyNotFound)
                }

                ARBI_CHAVE_REIVINDICADA -> {
                    logger.error(markerResponse, "findKeyDetails")
                    Either.Left(PixKeyError.MalformedKey)
                }

                SISTEMA_INDISPONIVEL -> {
                    logger.error(markerResponse, "findKeyDetails")
                    Either.Left(PixKeyError.SystemUnavailable)
                }

                else -> {
                    logger.error(markerResponse, "findKeyDetails")
                    Either.Left(PixKeyError.UnknownError)
                }
            }
        } catch (e: Exception) {
            logger.error(marker, "findKeyDetails", e)
            return Either.Left(PixKeyError.UnknownError)
        }
    }

    @Cacheable(cacheNames = ["find-pix-key-details"])
    override fun findKeyDetailsCacheable(key: PixKey, document: String): PixKeyDetailsResult {
        return findKeyDetails(key, document).map {
            it
        }.getOrElse {
            throw PixKeyException(it)
        }
    }

    override fun findInternalKeys(document: String) {
        val uri = UriBuilder.of(configuration.findInternalPixKeysPath)
            .expand(mutableMapOf("cpfCnpj" to document, "cpfCnpj" to document))
            .toString()
        val httpRequest = createHttpRequest<Unit>(HttpMethod.GET, uri)
        val call = httpClient.retrieve(
            httpRequest,
            Argument.listOf(ResponseTO::class.java),
            Argument.STRING,
        )
        try {
            val response = call.firstOrError().blockingGet()
            val mappedResponse = jacksonObjectMapper().readerFor(List::class.java)
                .readValue<List<Map<String, String>>>(response[0].response)

            logger.info(Markers.append("response", mappedResponse), "FindInternalPixKeys")
        } catch (e: HttpClientResponseException) {
            logger.error(
                Markers.append(
                    "response",
                    e.response.getBody(String::class.java).get(),
                ),
                "FindInternalPixKeys",
                e,
            )
        }
    }

    override fun startClaim(key: String, keyType: String, type: String): String {
        val request = ClaimTO(key = key, keyType = keyType, type = type)
        val httpRequest = createHttpRequest<ClaimTO>(HttpMethod.POST, configuration.claimPath)
            .body(request)
        val call = httpClient.retrieve(
            httpRequest,
            Argument.listOf(ResponseTO::class.java),
            Argument.STRING,
        )
        try {
            val response = call.firstOrError().blockingGet()

            val mappedResponse =
                jacksonObjectMapper().readerFor(Map::class.java).readValue<Map<String, String>>(response[0].response)

            logger.info(
                Markers.append("request", request).and<LogstashMarker>(Markers.append("response", mappedResponse)),
                "StartClaim",
            )
            return mappedResponse["id"] ?: throw IllegalStateException()
        } catch (e: HttpClientResponseException) {
            logger.error(
                Markers.append("status", e.response.status).and<LogstashMarker>(
                    Markers.append(
                        "response",
                        e.response.getBody(String::class.java).get(),
                    ),
                ),
                "StartClaim",
                e,
            )
            TODO()
        }
    }

    override fun completeClaim(claimId: String) {
        val httpRequest = createHttpRequest<CompleteClaimRequestTO>(HttpMethod.POST, configuration.completeClaimPath)
            .body(CompleteClaimRequestTO(claimId))
        val call = httpClient.retrieve(
            httpRequest,
            Argument.STRING,
            Argument.STRING,
        )

        try {
            val response = call.firstOrError().blockingGet()
            logger.info(Markers.append("response", response), "CompleteClaim")
        } catch (e: HttpClientResponseException) {
            logger.error(
                Markers.append("status", e.response.status).and<LogstashMarker>(
                    Markers.append(
                        "response",
                        e.response.getBody(String::class.java).get(),
                    ),
                ),
                "CompleteClaim",
                e,
            )
            TODO()
        }
    }

    override fun confirmClaim(claimId: String, reason: String) {
        val request = ClaimConfirmationTO(claimId = claimId, reason = reason)
        val httpRequest = createHttpRequest<ClaimConfirmationTO>(HttpMethod.POST, configuration.confirmClaimPath)
            .body(request)

        val call = httpClient.retrieve(
            httpRequest,
            Argument.listOf(ResponseTO::class.java),
            Argument.STRING,
        )

        try {
            val response = call.firstOrError().blockingGet()

            val mappedResponse =
                jacksonObjectMapper().readerFor(Map::class.java).readValue<Map<String, String>>(response[0].response)

            logger.info(
                Markers.append("request", request).and<LogstashMarker>(Markers.append("response", mappedResponse)),
                "ConfirmClaim",
            )
        } catch (e: HttpClientResponseException) {
            logger.error(
                Markers.append("status", e.response.status).and<LogstashMarker>(
                    Markers.append(
                        "response",
                        e.response.getBody(String::class.java).get(),
                    ),
                ),
                "ConfirmClaim",
                e,
            )
            TODO()
        }
    }

    override fun cancelClaim(claimId: String, reason: String) {
        val request = ClaimConfirmationTO(claimId = claimId, reason = reason)
        val httpRequest = createHttpRequest<ClaimConfirmationTO>(HttpMethod.POST, configuration.cancelClaimPath)
            .body(request)

        val call = httpClient.retrieve(
            httpRequest,
            Argument.listOf(ResponseTO::class.java),
            Argument.STRING,
        )

        try {
            val response = call.firstOrError().blockingGet()

            val mappedResponse =
                jacksonObjectMapper().readerFor(Map::class.java).readValue<Map<String, String>>(response[0].response)

            logger.info(
                Markers.append("request", request).and<LogstashMarker>(Markers.append("response", mappedResponse)),
                "CancelClaim",
            )
        } catch (e: HttpClientResponseException) {
            logger.error(
                Markers.append("status", e.response.status).and<LogstashMarker>(
                    Markers.append(
                        "response",
                        e.response.getBody(String::class.java).get(),
                    ),
                ),
                "CancelClaim",
                e,
            )
            TODO()
        }
    }

    override fun listClaims() {
        val httpRequest = createHttpRequest<ListClaimTO>(HttpMethod.POST, configuration.listClaimsPath)
            .body(ListClaimTO(cpfCnpj = configuration.inscricao))
        val call = httpClient.retrieve(
            httpRequest,
            Argument.listOf(ResponseTO::class.java),
            Argument.STRING,
        )
        try {
            val response = call.firstOrError().blockingGet()
            val mappedResponse = jacksonObjectMapper().readerFor(List::class.java)
                .readValue<List<Map<String, String>>>(response[0].response)

            logger.info(Markers.append("response", mappedResponse), "ListPixClaims")
        } catch (e: HttpClientResponseException) {
            if (e.status == HttpStatus.NOT_FOUND) {
                logger.warn(Markers.append("response", "No claim found"), "ListPixClaims")
            }
            logger.error(
                Markers.append(
                    "response",
                    e.response.getBody(String::class.java).get(),
                ),
                "ListPixClaims",
                e,
            )
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(NewArbiPixKeyManagementAdapter::class.java)
    }
}

data class ErroEnderecamentoTO(
    val erroDict: String,
    val codErro: Int,
    val descricaoErro: String?,
)

enum class PixAccountType {
    SVGS, CACC, SLRY, TRAN
}

internal fun AccountType.toTipoConta() =
    when (this) {
        AccountType.SAVINGS -> PixAccountType.SVGS.name
        AccountType.CHECKING -> PixAccountType.CACC.name
        AccountType.SALARY -> PixAccountType.SLRY.name
        AccountType.PAYMENT -> PixAccountType.TRAN.name
    }

internal fun KeyDetailsResponseTO.getAccountType() = fromArbiAccountType(tipoConta)

fun fromArbiAccountType(arbiAccountType: String): AccountType {
    return when (arbiAccountType) {
        PixAccountType.SVGS.name -> AccountType.SAVINGS
        PixAccountType.CACC.name -> AccountType.CHECKING
        PixAccountType.SLRY.name -> AccountType.SALARY
        PixAccountType.TRAN.name -> AccountType.PAYMENT
        else -> throw IllegalArgumentException("invalid account type $arbiAccountType")
    }
}

data class ClaimConfirmationTO(
    val claimId: String,
    val reason: String,
)

data class ResponseTO(
    val response: String,
    val status: Int,
)

data class ClaimTO(
    val key: String,
    val keyType: String,
    val type: String,
    val claimerAccount: ClaimerAccount = ClaimerAccount(),
    val claimer: Claimer = Claimer(),
)

data class ClaimerAccount(
    val participant: String = "********",
    val branch: String = "0001",
    val accountNumber: String = "**********",
    val accountType: String = "CACC",
    val openingDate: String = "2010-01-10T03:00:00Z",
)

data class Claimer(
    val type: String = "NATURAL_PERSON",
    val taxIdNumber: String = "***********",
    val name: String = "Nome",
    val tradeName: String = "Joaoaa Nascimento",
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class ListClaimTO(
    val claimStatus: String = "OPEN",
    val claimType: String = "PORTABILITY",
    val codAgencia: String = "",
    val cpfCnpj: String,
    val isClaimer: Boolean = true,
    val isDonor: Boolean = true,
    val limit: Int = 10,
    val modifiedAfter: String = "",
    val modifiedBefore: String = "",
    val nroConta: String = "",
)

data class CompleteClaimRequestTO(
    val claimId: String,
    val codIspb: String = "********",
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class RegisterKeyTO(
    val agencia: String,
    val chave: String,
    val conta: String,
    val cpfCnpj: String,
    val instituicao: String,
    val nome: String,
    val nomeFantasia: String,
    val reason: String,
    val tipoChave: String,
    val tipoConta: String,
) {
    val tipoPessoa = if (cpfCnpj.length == 11) "NATURAL_PERSON" else "LEGAL_PERSON"
}

@JsonInclude(JsonInclude.Include.ALWAYS)
data class DeleteKeyTO(
    val chave: String,
    val reason: String = "ACCOUNT_CLOSURE",
    val codUsuario: String,
    val codIspb: String = "********",
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class KeyDetailsResponseTO(
    val agencia: String,
    val conta: String,
    val cpfCnpj: String,
    val instituicao: String,
    val tipoConta: String,
    val confirmado: Boolean,
    val cid: String,
    val nome: String,
    val tipoPessoa: String,
    val chave: String,
    val tipoChave: String,
    val dataCriacao: String,
    val dataPosse: String,
    val endToEnd: String,
    val nomePsp: String,
    val dataAbertura: String,
    val estatisticas: Any?,
)

@ConfigurationProperties("integrations.arbi")
class ArbiPIXConfiguration {
    lateinit var host: String
    lateinit var clientId: String
    lateinit var clientSecret: String
    lateinit var pixKeyPath: String
    lateinit var findPixKeyPath: String
    lateinit var findInternalPixKeysPath: String
    lateinit var claimPath: String
    lateinit var cancelClaimPath: String
    lateinit var confirmClaimPath: String
    lateinit var findClaimPath: String
    lateinit var listClaimsPath: String
    lateinit var completeClaimPath: String
}