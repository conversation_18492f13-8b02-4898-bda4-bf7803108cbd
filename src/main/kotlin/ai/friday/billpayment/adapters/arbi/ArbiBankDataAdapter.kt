package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.banking.Bank
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.integrations.BankDataService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.cache.annotation.CacheConfig
import io.micronaut.cache.annotation.Cacheable
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.context.annotation.Secondary
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientException
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.uri.UriBuilder
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.Month
import java.time.Year
import java.util.Base64
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
@CacheConfig("arbi")
@Secondary
@Named("arbiBankDataServiceV1")
@Requires(env = [FRIDAY_ENV, MODATTA_ENV, ME_POUPE_ENV])
open class ArbiBankDataAdapter(
    @param:Client(
        id = "arbi",
    ) private val httpClient: RxHttpClient,
    private val configuration: ArbiConfiguration,
    private val authenticationManager: ArbiAuthenticationManager,
    @Property(name = "recurrence.limitDate") private val limitDate: String,
) : BankDataService {

    val mapper = jacksonObjectMapper()

    @Cacheable
    override fun getHolidays(): List<LocalDate> {
        val arbiList = fetchDomain(12)
            .mapUntilDate(LocalDate.parse(limitDate, dateFormat))

        val fridayList = listOf(LocalDate.of(2022, Month.DECEMBER, 30)).filter {
            it >= getLocalDate()
        } // TODO - remover

        val holidays = (arbiList + fridayList).sorted()

        logger.info(append("list", holidays), "GetHolidays")
        return holidays
    }

    override fun getBankCodes(): List<Bank> {
        val response = fetchDomain(2)
        logger.info(append("response", response), "ArbiGetBankCodes")
        return response.map { convertToBank(it) }
    }

    override fun getPixParticipants(): List<FinancialInstitution> {
        val response = fetchDomain(41)
        logger.info(append("response", response), "ArbiGetPixParticipants")
        return response.map { convertToFinancialInstitution(it) }
    }

    override fun getIncomeReportPDF(document: Document, year: Year): ByteArray {
        val logName = "ArbiBankDataAdapter#ArbiInforme"
        val markers = append("document", document.value).andAppend("year", year.value)
        val token = authenticationManager.getToken()
        val uri = UriBuilder.of(configuration.informeDeRendimentosPath)
            .queryParam("anoBase", year.value.toString())
            .queryParam("cpfCnpj", document.value)
            .queryParam("token_usuario", configuration.userToken)
            .queryParam("id_Modulo", "20")
            .queryParam("id_Transacao", "1").build()
        val httpRequest = HttpRequest.GET<Unit>(uri)
            .header("client_id", configuration.clientId)
            .header("access_token", token)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(String::class.java),
        )

        try {
            val rawResponse = call.firstOrError().blockingGet()
            val responseAsJsonString = parseObjectFrom<String>(rawResponse)
            val response = parseObjectFrom<ArbiInformeDeRendimentosResponseTO>(responseAsJsonString)

            logger.info(markers.andAppend("codErro", response.codErro).andAppend("descErro", response.descErro).andAppend("stringBase64Length", response.stringBase64?.length), logName)
            if (response.stringBase64 == null) {
                throw ArbiAdapterIncomeReportException("${response.codErro} - ${response.descErro}")
            }
            return Base64.getDecoder().decode(response.stringBase64)
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)
            logger.error(
                markers.andAppend("response", e.response.getBody(String::class.java).orElse(""))
                    .andAppend("status", e.status),
                logName,
                e,
            )
            throw ArbiAdapterIncomeReportException()
        } catch (e: HttpClientException) {
            logger.error(markers, logName, e)
            throw ArbiAdapterIncomeReportException()
        } catch (e: ArbiAdapterIncomeReportException) {
            logger.error(markers, logName, e)
            throw e
        } catch (e: Exception) {
            logger.error(markers.andAppend("ACTION", "VERIFY"), logName, e)
            throw ArbiAdapterIncomeReportException()
        }
    }

    private fun convertToBank(bankTO: DomainTO): Bank {
        return Bank(
            number = bankTO.codigodominio.toLong(),
            name = bankTO.descricaodominio.orEmpty(),
        )
    }

    private fun convertToFinancialInstitution(domainTO: DomainTO): FinancialInstitution {
        return FinancialInstitution(
            compe = domainTO.codigodominio.takeLast(3).toLongOrNull(),
            name = domainTO.descricaodominio.orEmpty(),
            ispb = domainTO.codigodominio.take(8),
        )
    }

    private fun fetchDomain(domainNumber: Int): List<DomainTO> {
        val token = authenticationManager.getToken()
        val httpRequest = HttpRequest.POST(configuration.domainPath, """{"dominios":{"iddominio":$domainNumber}}""")
            .header("client_id", configuration.clientId)
            .header("access_token", token)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        val call = httpClient.retrieve(
            httpRequest,
            Argument.listOf(DomainTO::class.java),
            Argument.STRING,
        )
        try {
            return call.firstOrError().blockingGet()
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)
            logger.error(append("response", e.response.getBody(String::class.java).get()), "FetchDomain", e)
            throw ArbiAdapterException()
        }
    }

    private fun checkUnauthorized(e: HttpClientResponseException) {
        if (e.status == HttpStatus.UNAUTHORIZED) {
            logger.warn(append("error_message", "Token is expired"), "ArbiAdapter")
            authenticationManager.cleanTokens()
            throw ArbiLoginException()
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ArbiBankDataAdapter::class.java)
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class ArbiInformeDeRendimentosResponseTO(
    val codErro: String?,
    val descErro: String?,
    val stringBase64: String?,
)