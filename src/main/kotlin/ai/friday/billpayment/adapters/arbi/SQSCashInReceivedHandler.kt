package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.notification.ChatbotAiMessageHandlerConfiguration
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.chatbot.CashInReceivedTO
import ai.friday.billpayment.app.chatbot.OpenFinanceIncentiveService
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import io.via1.communicationcentre.app.notification.NotificationSendException
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requirements(Requires(notEnv = ["test"]))
open class SQSCashInReceivedHandler(
    amazonSQS: SqsClient,
    configuration: ChatbotAiMessageHandlerConfiguration,
    private val openFinanceIncentiveService: OpenFinanceIncentiveService,
    @Property(name = "sqs.queues.cashInReceived") private val cashInReceivedQueueName: String,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = cashInReceivedQueueName,
    consumers = 1,
) {
    @NewSpan
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val markers = Markers.append("eventMessage", message)
        val cashInReceived = parseObjectFrom<CashInReceivedTO>(message.body())

        openFinanceIncentiveService.processCashIn(cashInReceived)

        logger.info(markers, "SQSCashInReceivedHandler")

        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    override fun handleError(
        message: Message,
        e: Exception,
    ): SQSHandlerResponse {
        if (e is NotificationSendException && e.isPermanent) {
            logger.warn("SQSCashInReceivedHandler", e)
            return SQSHandlerResponse(shouldDeleteMessage = true)
        }
        logger.error("SQSCashInReceivedHandler", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSCashInReceivedHandler::class.java)
    }
}