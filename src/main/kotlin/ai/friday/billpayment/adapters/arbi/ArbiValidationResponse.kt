package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.adapters.arbi.CIPSitTitPgto.PAGAMENTO_JA_BAIXADO
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.payment.BillRegisterData
import ai.friday.billpayment.app.payment.BillValidationResponse

private const val BARCODE_NOT_FOUND_MESSAGE = "Mensagem rejeitada pela CIP: EDDA0526"
private const val CALCULATION_GROUP_NOT_FOUND = "107: Grupo de cálculo não informado!"
private const val CALCULATION_EXPIRED = "108: Não existe Data da Validade do Cálculo na consulta do boleto para a data de hoje. O título só poderá ser pago no Banco Emissor!"

data class ArbiValidationResponse(
    override val billRegisterData: BillRegisterData?,
    val paymentStatus: Int?,
    val resultado: String? = null,
) : BillValidationResponse(
    gateway = FinancialServiceGateway.ARBI,
    billRegisterData = billRegisterData,
    errorDescription = resultado,
) {

    private val notPayableCodes = listOf(PAGAMENTO_JA_BAIXADO.code, CIPSitTitPgto.BOLETO_BLOQUEADO.code)

    constructor(resultado: String) : this(resultado = resultado, billRegisterData = null, paymentStatus = null)

    override fun isRegistrationResponseOlderThan(registrationUpdateNumber: Long): Boolean {
        return this.billRegisterData!!.registrationUpdateNumber != null && this.billRegisterData.registrationUpdateNumber!! < registrationUpdateNumber
    }

    override fun isValid(): Boolean {
        return paymentStatus == CIPSitTitPgto.SUCESSO.code && !(hasAlreadyPaid()) && !isPaymentLimitExpired()
    }

    override fun paymentNotAuthorized(): Boolean {
        return false
    }

    override fun notPayable(): Boolean {
        return paymentStatus in notPayableCodes || isPaymentLimitExpired()
    }

    override fun alreadyPaid(): Boolean {
        return when (paymentStatus) {
            CIPSitTitPgto.PAGAMENTO_JA_EFETUADO.code -> true
            PAGAMENTO_JA_BAIXADO.code, CIPSitTitPgto.SUCESSO.code -> hasAlreadyPaid()
            else -> false
        }
    }

    override fun isBarcodeNotFound(): Boolean {
        return resultado?.contains(BARCODE_NOT_FOUND_MESSAGE) ?: false
    }

    private fun hasAlreadyPaid() = billRegisterData?.amountPaid != null && billRegisterData.amountPaid > 0L

    override fun isRetryable(): Boolean {
        if (resultado in listOf(CALCULATION_GROUP_NOT_FOUND, CALCULATION_EXPIRED)) {
            return false
        }

        return true
    }

    companion object {
        fun convertStatusToErrorDescription(status: Int): String = CIPSitTitPgto.getDescription(status)
    }
}

enum class CIPSitTitPgto(val code: Int, val description: String) {
    PAGAMENTO_JA_BAIXADO(1, "BOLETO DE PAGAMENTO JA BAIXADO"),
    BOLETO_BLOQUEADO(2, "BOLETO BLOQUEADO"),
    PAGAMENTO_JA_EFETUADO(7, "PAGAMENTO JA EFETUADO"),
    SUCESSO(12, ""),
    ;

    companion object {
        fun getDescription(code: Int): String =
            values().find { it.code == code }?.description ?: "BOLETO NAO APTO PARA PAGAMENTO"
    }
}