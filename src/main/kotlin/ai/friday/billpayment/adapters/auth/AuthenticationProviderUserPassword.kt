package ai.friday.billpayment.adapters.auth

import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.Authenticator
import io.micronaut.context.annotation.Property
import io.micronaut.http.HttpRequest
import io.micronaut.security.authentication.AuthenticationFailed
import io.micronaut.security.authentication.AuthenticationRequest
import io.micronaut.security.authentication.AuthenticationResponse
import jakarta.inject.Singleton
import java.util.Optional

@Singleton
@Deprecated(
    message = "Please consider use AwsSecretBasicAuthentication",
    replaceWith = ReplaceWith(
        expression = "AwsSecretBasicAuthentication",
        imports = ["ai.friday.billpayment.adapters.auth"],
    ),
)
class AuthenticationProviderUserPassword(
    @Property(name = "celcoin-callback.identity") private val celcoinCallbackIdentity: String,
    @Property(name = "celcoin-callback.secret") private val celcoinCallbackSecret: String,
    @Property(name = "arbi-callback.identity") private val arbiCallbackIdentity: String,
    @Property(name = "arbi-callback.secret") private val arbiCallbackSecret: String,
    @Property(name = "revenue-cat-callback.identity") private val revenueCatCallbackIdentity: String,
    @Property(name = "revenue-cat-callback.secret") private val revenueCatCallbackSecret: String,
    @Property(name = "modatta-b2b.identity") private val modattaB2BIdentity: String,
    @Property(name = "modatta-b2b.secret") private val modattaB2BSecret: String,
    @Property(name = "friday-callback.identity") private val fridayIdentity: String,
    @Property(name = "friday-callback.secret") private val fridaySecret: String,
    @Property(name = "modatta-corp-callback.identity") private val modattaCorpCallbackIdentity: String,
    @Property(name = "modatta-corp-callback.secret") private val modattaCorpCallbackSecret: String,
    @Property(name = "vehicle-debts-callback.identity") private val vehicleDebtsCallbackIdentity: String,
    @Property(name = "vehicle-debts-callback.secret") private val vehicleDebtsCallbackSecret: String,
    @Property(name = "intercom-callback.identity") private val intercomCallbackIdentity: String,
    @Property(name = "intercom-callback.secret") private val intercomCallbackSecret: String,
    @Property(name = "chatbotAI-auth.secret") private val chatbotAISecret: String,
    private val chatbotAIAuthenticator: Optional<Authenticator>,
) : BillPaymentAuthenticationProvider() {

    override fun billPaymentAuthenticate(
        httpRequest: HttpRequest<*>?,
        authenticationRequest: AuthenticationRequest<*, *>,
    ): Pair<String, AuthenticationResponse> {
        return when {
            authenticationRequest.identity == revenueCatCallbackIdentity && authenticationRequest.secret == revenueCatCallbackSecret ->
                Pair(
                    "revenueCatCallbackIdentity",
                    AuthenticationResponse.success(
                        authenticationRequest.identity as String,
                        listOf(Role.REVENUE_CAT_CALLBACK.name),
                    ),
                )

            authenticationRequest.identity == celcoinCallbackIdentity && authenticationRequest.secret == celcoinCallbackSecret ->
                Pair(
                    "celcoinCallbackIdentity",
                    AuthenticationResponse.success(
                        authenticationRequest.identity as String,
                        listOf(Role.CELCOIN_CALLBACK.name),
                    ),
                )

            authenticationRequest.identity == arbiCallbackIdentity && authenticationRequest.secret == arbiCallbackSecret ->
                Pair(
                    "arbiCallbackIdentity",
                    AuthenticationResponse.success(
                        authenticationRequest.identity as String,
                        listOf(Role.ARBI_CALLBACK.name),
                    ),
                )

            authenticationRequest.identity == modattaB2BIdentity && authenticationRequest.secret == modattaB2BSecret ->
                Pair(
                    "modattaB2BIdentity",
                    AuthenticationResponse.success(
                        authenticationRequest.identity as String,
                        listOf(Role.MODATTA_B2B.name),
                    ),
                )

            authenticationRequest.identity == fridayIdentity && authenticationRequest.secret == fridaySecret ->
                Pair(
                    "fridayIdentity",
                    AuthenticationResponse.success(
                        authenticationRequest.identity as String,
                        listOf(Role.FRIDAY_CALLBACK.name),
                    ),
                )

            authenticationRequest.identity == modattaCorpCallbackIdentity && authenticationRequest.secret == modattaCorpCallbackSecret ->
                Pair(
                    "modattaCorpCallbackIdentity",
                    AuthenticationResponse.success(
                        authenticationRequest.identity as String,
                        listOf(Role.MODATTA_CORP_CALLBACK.name),
                    ),
                )

            authenticationRequest.identity == vehicleDebtsCallbackIdentity && authenticationRequest.secret == vehicleDebtsCallbackSecret ->
                Pair(
                    "vehicleDebtsCallbackIdentity",
                    AuthenticationResponse.success(
                        authenticationRequest.identity as String,
                        listOf(Role.VEHICLE_DEBTS_CALLBACK.name),
                    ),
                )

            authenticationRequest.identity == intercomCallbackIdentity && authenticationRequest.secret == intercomCallbackSecret ->
                Pair(
                    "intercomCallbackIdentity",
                    AuthenticationResponse.success(
                        authenticationRequest.identity as String,
                        listOf(Role.INTERCOM_CALLBACK.name),
                    ),
                )

            chatbotAIAuthenticator.isPresent && authenticationRequest.secret == chatbotAISecret ->
                Pair(
                    "chatbotAIAuthenticator",
                    chatbotAIAuthenticator.get().authenticate(authenticationRequest.identity as String),
                )

            else -> Pair("AuthenticationProviderUserPassword", AuthenticationFailed())
        }
    }
}