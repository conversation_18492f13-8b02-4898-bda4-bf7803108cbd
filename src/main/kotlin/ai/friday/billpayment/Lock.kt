package ai.friday.billpayment

import ai.friday.billpayment.app.integrations.InternalLock
import net.javacrumbs.shedlock.core.SimpleLock

sealed interface Lock {
    data class EphemeralLock(override val name: String) : Lock
    data class DurableLock(override val name: String, val releaseAfterError: Boolean = true) : Lock

    val name: String
}

fun <T> InternalLock.withLocks(
    locks: List<Lock>,
    toBeExecuted: () -> T,
): Result<T> {
    val lockInstances = hashMapOf<Lock, SimpleLock>()

    return runCatching {
        locks.forEach { lock ->
            val lockInstance = this.acquireLock(lock.name)
                ?: throw CouldNotAcquireLockException(lock.name)

            lockInstances[lock] = lockInstance
        }

        toBeExecuted()
    }.also { result ->
        lockInstances.forEach { entry ->
            val (lock, instance) = entry

            when (lock) {
                is Lock.EphemeralLock -> instance.unlock()
                is Lock.DurableLock -> if (result.isFailure) instance.unlock()
            }
        }
    }
}

fun <T> InternalLock.withEphemeralLock(
    lockName: String,
    toBeExecuted: () -> T,
): Result<T> = this.withLock(Lock.EphemeralLock(lockName), toBeExecuted)

fun <T> InternalLock.withLock(
    lock: Lock,
    toBeExecuted: () -> T,
): Result<T> = this.withLocks(listOf(lock), toBeExecuted)

class CouldNotAcquireLockException(lockName: String) : Exception("lock $lockName couldn't be acquired")