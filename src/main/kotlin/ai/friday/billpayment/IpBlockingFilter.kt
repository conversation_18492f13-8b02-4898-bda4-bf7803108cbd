package ai.friday.billpayment

import ai.friday.morning.log.andAppend
import io.micronaut.core.async.publisher.Publishers
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpResponse
import io.micronaut.http.filter.HttpServerFilter
import io.micronaut.http.filter.ServerFilterChain
import io.micronaut.http.server.util.HttpClientAddressResolver
import net.logstash.logback.marker.Markers
import org.reactivestreams.Publisher
import org.slf4j.LoggerFactory

abstract class IpBlockingFilter(
    private val addressResolver: HttpClientAddressResolver,
    private val allowedIps: List<String>,
) : HttpServerFilter {

    override fun doFilter(request: HttpRequest<*>, chain: ServerFilterChain): Publisher<MutableHttpResponse<*>> {
        val clientIP = addressResolver.resolve(request)
        val markers = Markers.append("clientIP", clientIP)

        if (allowedIps.isEmpty() || allowedIps.contains("*.*.*.*")) {
            logger.info(markers.andAppend("context", "AllowedIps está vazio. Tudo será liberado"), "IPBlockingFilter")
            return chain.proceed(request)
        }

        allowedIps.forEach { allowedIp ->
            if (allowedIps.contains(clientIP)) {
                logger.info(
                    markers.andAppend("context", "Request liberado por regra").andAppend("rule", allowedIp),
                    "IPBlockingFilter",
                )
                return chain.proceed(request)
            }
        }
        logger.warn(
            markers.andAppend("context", "Request bloqueado"),
            "IPBlockingFilter",
        )
        return Publishers.just(HttpResponse.status<Any>(HttpStatus.FORBIDDEN))
    }

    companion object {
        private val logger = LoggerFactory.getLogger(IpBlockingFilter::class.java)
    }
}