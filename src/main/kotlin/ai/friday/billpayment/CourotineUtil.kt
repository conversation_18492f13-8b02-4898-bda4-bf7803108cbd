import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async // ktlint-disable filename
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.withContext

suspend fun <A, B> Iterable<A>.parallelMap(f: suspend (A) -> B): List<B> = coroutineScope {
    parallel(f)
}

// to avoid linter problem
suspend fun <A, B> Iterable<A>.parallel(f: suspend (A) -> B): List<B> = withContext(Dispatchers.IO) {
    map { async { f(it) } }.awaitAll()
}