package ai.friday.billpayment

import java.security.MessageDigest
import java.security.SecureRandom
import java.util.Base64
import javax.crypto.Cipher
import javax.crypto.SecretKey
import javax.crypto.SecretKeyFactory
import javax.crypto.spec.GCMParameterSpec
import javax.crypto.spec.PBEKeySpec
import javax.crypto.spec.SecretKeySpec

object EncryptUtil {

    fun generateDeterministicSecretKey(masterKey: String, deterministicValue: String, applySaltHash: Boolean = true): SecretKeySpec {
        // use deterministic key to generate salt, e.g. userId
        val salt = when (applySaltHash) {
            true -> sha1(deterministicValue).toByteArray()
            false -> deterministicValue.toByteArray()
        }

        // Generate a key from the master key and the salt
        val keySpec = PBEKeySpec(masterKey.toCharArray(), salt, 20000, 256)
        val factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256")
        val hash = factory.generateSecret(keySpec).encoded

        return SecretKeySpec(hash, "AES")
    }

    fun encrypt(token: String, secretKey: SecretKey): String {
        val cipher = Cipher.getInstance("AES/GCM/NoPadding")
        val iv = ByteArray(12).apply { SecureRandom().nextBytes(this) }

        cipher.init(Cipher.ENCRYPT_MODE, secretKey, GCMParameterSpec(128, iv))

        val encrypted = cipher.doFinal(token.toByteArray())
        val ivAndEncrypted = iv + encrypted

        return Base64.getEncoder().encodeToString(ivAndEncrypted)
    }

    fun decrypt(encryptedToken: String, secretKey: SecretKey): String {
        val ivAndEncrypted = Base64.getDecoder().decode(encryptedToken)
        val iv = ivAndEncrypted.copyOfRange(0, 12)
        val encrypted = ivAndEncrypted.copyOfRange(12, ivAndEncrypted.size)
        val cipher = Cipher.getInstance("AES/GCM/NoPadding")

        cipher.init(Cipher.DECRYPT_MODE, secretKey, GCMParameterSpec(128, iv))

        return String(cipher.doFinal(encrypted))
    }

    fun encrypt(masterKey: String, token: String, deterministicId: String): String =
        encrypt(token, generateDeterministicSecretKey(masterKey, deterministicId))

    fun decrypt(masterKey: String, encryptedToken: String, deterministicId: String): String =
        decrypt(encryptedToken, generateDeterministicSecretKey(masterKey, deterministicId))

    private fun sha1(input: String): String {
        val digest = MessageDigest.getInstance("SHA-1")
        val hashBytes = digest.digest(input.toByteArray(Charsets.UTF_8))

        return hashBytes.joinToString("") { "%02x".format(it) }
    }
}