// ktlint-disable filename
package ai.friday.billpayment

import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.metrics.LoggerMetric
import ai.friday.billpayment.app.metrics.metricRegister
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.boletoSettlementOperation
import ai.friday.morning.log.andAppend
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.event.Level

fun markers(vararg args: Pair<String, Any?>) = Markers.appendEntries(args.toMap())

fun log(vararg args: Pair<String, Any?>) = Markers.empty().and(*args)
fun logEvent(event: BillEvent) = Markers.empty().withEvent(event)

inline fun <T> LogstashMarker.elapsedTime(fieldName: String = "elapsed_time", fn: () -> T): MeasureResult<T> =
    measureTimeInMillis { fn() }
        .also { andAppend(fieldName, it.elapsed) }

fun LogstashMarker.withEvent(event: BillEvent) =
    this.and("bill_id" to event.billId.value, "wallet_id" to event.walletId.value, "event_type" to event.eventType)

fun Transaction.markers() = Markers.append("transactionId", id.value)
    .andAppend("accountId", payer.accountId.value)
    .andAppend("barCode", settlementData.getTarget<Bill>().barcode)
    .andAppend("billId", settlementData.getTarget<Bill>().billId.value)
    .andAppend("cipId", settlementData.getTarget<Bill>().idNumber)
    .andAppend("transactionStatus", status.name)
    .andAppend("walletId", walletId.value)
    .andAppend("paymentStatus", paymentData.status())
    .andAppend("settlementProvider", boletoSettlementOperation().gateway.name)

fun LogstashMarker.elapsed(elapsedTime: Long) = this.and("elapsed_time" to elapsedTime)

fun LogstashMarker.plus(vararg args: Pair<String, Any?>) = this.and(*args)
fun LogstashMarker.merge(markers: LogstashMarker) = also { it.add(markers) }

fun LogstashMarker.and(vararg args: Pair<String, Any?>): LogstashMarker =
    args.toMap().filterKeys { it.lowercase() != "message" }.filterValues { it != null }
        .takeIf { it.isNotEmpty() }
        ?.map {
            // FIXME - tentear resolver isso diretamente na configuracao do Logstash
            val value = when (it.value) {
                is LocalDate, is LocalDateTime, is ZonedDateTime -> it.value.toString()
                else -> it.value
            }
            this.and<LogstashMarker>(Markers.append(it.key, value))
        }
        ?.reduce { first, second -> first.and(second) } ?: this

fun LogstashMarker.withSuccessResult() = this.withResult("success")
fun LogstashMarker.withFailResult(reason: String? = null) =
    this.withResult("fail").and("fail_reason" to reason)

fun LogstashMarker.withErrorResult(ex: Throwable? = null) =
    this.withResult("error").and("error_message" to ex?.message)

fun LogstashMarker.withResult(result: String) = this.and("result" to result)

fun LogstashMarker.andAppendMasked(fieldName: String, value: String?, maskChar: Char = '*', showFromStart: Int = 4, showFromEnd: Int = 4): LogstashMarker {
    if (value.isNullOrEmpty()) return this

    val start = showFromStart.coerceAtLeast(0)
    val end = showFromEnd.coerceAtLeast(0)
    val total = start + end

    return if (value.length <= total) {
        andAppend(fieldName, value)
    } else {
        val masked = StringUtils.overlay(value, StringUtils.repeat(maskChar, value.length - total), start, value.length - end)
        andAppend(fieldName, masked)
    }
}

fun Logger.i(markers: LogstashMarker, customMessage: String? = null, vararg metrics: LoggerMetric) =
    this.log(level = Level.INFO, markers = markers, message = customMessage, metrics = metrics)

fun Logger.d(markers: LogstashMarker, customMessage: String? = null, vararg metrics: LoggerMetric) =
    this.log(level = Level.DEBUG, markers = markers, message = customMessage, metrics = metrics)

fun Logger.w(markers: LogstashMarker, customMessage: String? = null, vararg metrics: LoggerMetric) =
    this.log(level = Level.WARN, markers = markers, message = customMessage, metrics = metrics)

fun Logger.e(markers: LogstashMarker, customMessage: String? = null, ex: Throwable?, vararg metrics: LoggerMetric) =
    this.log(level = Level.ERROR, markers = markers, message = customMessage, ex = ex, metrics = metrics)

private fun Logger.log(
    level: Level,
    markers: LogstashMarker,
    message: String? = null,
    ex: Throwable? = null,
    vararg metrics: LoggerMetric,
) {
    val builder = this.atLevel(level).addMarker(markers)
    if (ex != null) builder.setCause(ex)
    builder.log(message ?: this.name.split(".").last())

    register(*metrics)
}

private fun register(vararg loggerMetric: LoggerMetric) =
    loggerMetric.forEach { metricRegister(metric = it.metric, possibleTags = it.tags.toTypedArray()) }