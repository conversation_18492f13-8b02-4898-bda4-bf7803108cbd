// ktlint-disable filename
package ai.friday.billpayment

import com.fasterxml.jackson.annotation.JsonTypeInfo

@JsonTypeInfo(
    include = JsonTypeInfo.As.PROPERTY,
    use = JsonTypeInfo.Id.NAME,
    property = "@type",
    visible = true,
)
@Deprecated(
    message = "Use PrintableSealedClassV2 instead",
    replaceWith = ReplaceWith(
        "PrintableSealedClassV2",
        imports = ["ai.friday.billpayment.PrintableSealedClassV2"],
    ),
)
abstract class PrintableSealedClass {
    override fun toString(): String {
        return this::class.simpleName ?: super.toString()
    }
}

@JsonTypeInfo(
    include = JsonTypeInfo.As.PROPERTY,
    use = JsonTypeInfo.Id.NAME,
    property = "instanceType",
    visible = true,
)
abstract class PrintableSealedClassV2 {
    override fun toString(): String {
        return this::class.simpleName ?: super.toString()
    }
}