package ai.friday.billpayment

import io.micronaut.http.HttpResponse

interface Error

open class Err(val message: String, val ex: Throwable? = null, val args: Map<String, Any> = mapOf()) : Error {
    override fun toString(): String {
        return if (ex == null) message else "${this::class.simpleName}#${ex::class.simpleName}"
    }
}

class ServerError(err: Throwable? = null) : Err("Internal Server Error", err)
class HttpClientError(val res: HttpResponse<*>, err: Exception? = null) :
    Err("Http Client Error", args = mapOf("status" to res.status), ex = err)

object AccountNotFound : Err("account not found")
object AccountPaymentMethodNotFound : Err("account payment method not found")
object WalletNotFound : Err("wallet not found")
object DDARegisterNotFound : Err("dda register not found")
object BillNotFound : Err("bill not found")
object TransactionNotFound : Err("transaction not found")