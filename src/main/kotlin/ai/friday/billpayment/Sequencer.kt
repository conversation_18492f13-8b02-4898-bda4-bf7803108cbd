package ai.friday.billpayment

import java.util.concurrent.atomic.AtomicInteger

class Sequencer(sequenceSize: Int) {
    private val counter = AtomicInteger(0)
    private var array: List<Int>

    init {
        array = IntRange(1, sequenceSize).toList()
    }

    fun next(): Int {
        counter.compareAndSet(1_000_000, 0)
        val index = (counter.incrementAndGet() % array.size)
        return array[index]
    }
}