package ai.friday.billpayment

import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpAttributes
import io.micronaut.http.HttpMethod
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.MediaType
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.reactivex.Flowable
import net.logstash.logback.marker.LogstashMarker

/**
 * Add headers to current HttpRequest
 *
 * Example:
 * ```
 * request.headers("foo" to "bar", "ham" to 1)
 * ```
 *
 * @param args list of headers key - value
 */
fun <T> MutableHttpRequest<T>.headers(vararg args: Pair<String, CharSequence>) =
    args.forEach { this.header(it.first, it.second) }.let { this }

/**
 * Add bearer authentication auth to current request
 *
 * Example:
 * ```
 * request.bearer("custom_token")
 * ```
 *
 * @param token token of bearer auth
 */
fun <T> MutableHttpRequest<T>.bearer(token: String): MutableHttpRequest<T> =
    this.headers("Authorization" to "Bearer $token")

/**
 * Make current request of type json
 *
 * Example:
 * ```
 * request.json()
 * ```
 */
fun <T> MutableHttpRequest<T>.json() =
    this.accept(MediaType.APPLICATION_JSON).contentType(MediaType.APPLICATION_JSON)

/**
 * Return a [Either.Right] with success result of type [O] and [Either.Left] of type [Err] otherwise
 *
 * Example:
 * ```
 * cli.get<IResponse>("/customPath")
 * cli.get<IResponse>("/customPath", mapOf("customQueryParameter" to "queryParameterValue"))
 * cli.get<IResponse>("/customPath", mapOf("customQueryParameter" to "queryParameterValue"), mapOf("customHeader" to "headerValue")
 * cli.get<IResponse>("/customPath", mapOf("customQueryParameter" to "queryParameterValue"), mapOf("customHeader" to "headerValue") { it.headers("Content-Type" to "Another Content-Type"}
 * ```
 *
 * @param O type of response body
 * @param path path of request
 * @param qs (Optional) map of query parameters
 * @param headers (Optional) map of headers
 * @param fn (Optional) function to change default values of request
 *
 * @see ai.friday.billpayment.HttpUtilIntegrationTest
 */
inline fun <reified O> RxHttpClient.get(
    path: String,
    qs: Map<String, Any> = mapOf(),
    headers: Map<String, Any> = mapOf(),
    noinline fn: ((MutableHttpRequest<Nothing?>) -> MutableHttpRequest<Nothing?>)? = null,
): Either<Err, O> = doRequest(HttpMethod.GET, path, qs = qs, headers = headers, fn = fn)

/**
 * Return a [Either.Right] with success result of type [O] and [Either.Left] of type [Err] otherwise
 *
 * Example:
 * ```
 * cli.post<IResponse>("/customPath", IRequest())
 * cli.post<IResponse>("/customPath", IRequest(), mapOf("customHeader" to "headerValue"))
 * cli.post<IResponse>("/customPath", IRequest(), mapOf("customHeader" to "headerValue") { it.headers("Content-Type" to "Another Content-Type"}
 * ```
 *
 * @param O type of response body
 * @param path path of request
 * @param path body request
 * @param headers (Optional) map of headers
 * @param fn (Optional) function to change default values of request
 *
 * @see ai.friday.billpayment.HttpUtilIntegrationTest
 */
inline fun <reified O> RxHttpClient.post(
    path: String,
    body: Any? = null,
    headers: Map<String, Any> = mapOf(),
    noinline fn: ((MutableHttpRequest<Any?>) -> MutableHttpRequest<Any?>)? = null,
): Either<Err, O> = doRequest(HttpMethod.POST, path, body, headers = headers, fn = fn)

/**
 * Return a [Either.Right] with success result of type [O] and [Either.Left] of type [Err] otherwise
 *
 * Example:
 * ```
 * cli.put<IResponse>("/customPath", IRequest())
 * cli.put<IResponse>("/customPath", IRequest(), mapOf("customHeader" to "headerValue"))
 * cli.put<IResponse>("/customPath", IRequest(), mapOf("customHeader" to "headerValue") { it.headers("Content-Type" to "Another Content-Type"}
 * ```
 * @param O type of response body
 * @param path path of request
 * @param path body request
 * @param headers (Optional) map of headers
 * @param fn (Optional) function to change default values of request
 *
 * @see ai.friday.billpayment.HttpUtilIntegrationTest
 */
inline fun <reified O> RxHttpClient.put(
    path: String,
    body: Any? = null,
    headers: Map<String, Any> = mapOf(),
    noinline fn: ((MutableHttpRequest<Any?>) -> MutableHttpRequest<Any?>)? = null,
): Either<Err, O> = doRequest(HttpMethod.PUT, path, body, headers = headers, fn = fn)

/**
 * Return a [Either.Right] with success result of type [O] and [Either.Left] of type [Err] otherwise
 *
 * Example:
 * ```
 * cli.delete<IResponse>("/customPath", IRequest())
 * cli.delete<IResponse>("/customPath", IRequest(), mapOf("customHeader" to "headerValue"))
 * cli.delete<IResponse>("/customPath", IRequest(), mapOf("customHeader" to "headerValue") { it.headers("Content-Type" to "Another Content-Type"}
 * ```
 * @param O type of response body
 * @param path path of request
 * @param path body request
 * @param headers (Optional) map of headers
 * @param fn (Optional) function to change default values of request
 *
 * @see ai.friday.billpayment.HttpUtilIntegrationTest
 */
inline fun <reified O> RxHttpClient.delete(
    path: String,
    body: Any? = null,
    headers: Map<String, Any> = mapOf(),
    noinline fn: ((MutableHttpRequest<Any?>) -> MutableHttpRequest<Any?>)? = null,
): Either<Err, O> = doRequest(HttpMethod.DELETE, path, body, headers = headers, fn = fn)

// Exchange
/**
 * Return a [Either.Right] with [HttpResponse] with success result of type [O] and [Either.Left] of type [Err] otherwise
 *
 * Example:
 * ```
 * cli.getHttp<IResponse>("/customPath")
 * cli.getHttp<IResponse>("/customPath", mapOf("customQueryParameter" to "queryParameterValue"))
 * cli.getHttp<IResponse>("/customPath", mapOf("customQueryParameter" to "queryParameterValue"), mapOf("customHeader" to "headerValue")
 * cli.getHttp<IResponse>("/customPath", mapOf("customQueryParameter" to "queryParameterValue"), mapOf("customHeader" to "headerValue") { it.headers("Content-Type" to "Another Content-Type"}
 * ```
 *
 * @param O type of response body
 * @param path path of request
 * @param qs (Optional) map of query parameters
 * @param headers (Optional) map of headers
 * @param fn (Optional) function to change default values of request
 *
 * @see ai.friday.billpayment.HttpUtilIntegrationTest
 */
inline fun <reified O> RxHttpClient.getHttp(
    path: String,
    qs: Map<String, Any> = mapOf(),
    headers: Map<String, Any> = mapOf(),
    noinline fn: ((MutableHttpRequest<Nothing?>) -> MutableHttpRequest<Nothing?>)? = null,
) = this.http<Nothing, O>(HttpMethod.GET, path, qs = qs, headers = headers, fn = fn)

/**
 * Return a [Either.Right] with success result of type [HttpResponse] and [Either.Left] of type [Err] otherwise
 *
 * Example:
 * ```
 * cli.postHttp<IResponse>("/customPath", IRequest())
 * cli.postHttp<IResponse>("/customPath", IRequest(), mapOf("customHeader" to "headerValue"))
 * cli.postHttp<IResponse>("/customPath", IRequest(), mapOf("customHeader" to "headerValue") { it.headers("Content-Type" to "Another Content-Type"}
 * ```
 *
 * @param O type of response body
 * @param path path of request
 * @param path body request
 * @param headers (Optional) map of headers
 * @param fn (Optional) function to change default values of request
 *
 * @see ai.friday.billpayment.HttpUtilIntegrationTest
 */
inline fun <reified O> RxHttpClient.postHttp(
    path: String,
    body: Any? = null,
    qs: Map<String, Any> = mapOf(),
    headers: Map<String, Any> = mapOf(),
    noinline fn: ((MutableHttpRequest<Any?>) -> MutableHttpRequest<Any?>)? = null,
) = this.http<Any, O>(HttpMethod.POST, path, body = body, qs = qs, headers = headers, fn = fn)

inline fun <reified O> RxHttpClient.postHttpWithResult(
    path: String,
    body: Any? = null,
    qs: Map<String, Any> = mapOf(),
    headers: Map<String, Any> = mapOf(),
    noinline fn: ((MutableHttpRequest<Any?>) -> MutableHttpRequest<Any?>)? = null,
) = this.httpWithResult<Any, O>(HttpMethod.POST, path, body = body, qs = qs, headers = headers, fn = fn)

/**
 * Return a [Either.Right] with success result of type [HttpResponse] and [Either.Left] of type [Err] otherwise
 *
 * Example:
 * ```
 * cli.putHttp<IResponse>("/customPath", IRequest())
 * cli.putHttp<IResponse>("/customPath", IRequest(), mapOf("customHeader" to "headerValue"))
 * cli.putHttp<IResponse>("/customPath", IRequest(), mapOf("customHeader" to "headerValue") { it.headers("Content-Type" to "Another Content-Type"}
 * ```
 * @param O type of response body
 * @param path path of request
 * @param path body request
 * @param headers (Optional) map of headers
 * @param fn (Optional) function to change default values of request
 *
 * @see ai.friday.billpayment.HttpUtilIntegrationTest
 */
inline fun <reified O> RxHttpClient.putHttp(
    path: String,
    body: Any? = null,
    qs: Map<String, Any> = mapOf(),
    headers: Map<String, Any> = mapOf(),
    noinline fn: ((MutableHttpRequest<Any?>) -> MutableHttpRequest<Any?>)? = null,
) = this.http<Any, O>(HttpMethod.PUT, path, body = body, qs = qs, headers = headers, fn = fn)

/**
 * Return a [Either.Right] with success result of type [HttpResponse] and [Either.Left] of type [Err] otherwise
 *
 * Example:
 * ```
 * cli.deleteHttp<IResponse>("/customPath", IRequest())
 * cli.deleteHttp<IResponse>("/customPath", IRequest(), mapOf("customHeader" to "headerValue"))
 * cli.deleteHttp<IResponse>("/customPath", IRequest(), mapOf("customHeader" to "headerValue") { it.headers("Content-Type" to "Another Content-Type"}
 * ```
 * @param O type of response body
 * @param path path of request
 * @param path body request
 * @param headers (Optional) map of headers
 * @param fn (Optional) function to change default values of request
 *
 * @see ai.friday.billpayment.HttpUtilIntegrationTest
 */
inline fun <reified O> RxHttpClient.deleteHttp(
    path: String,
    body: Any? = null,
    qs: Map<String, Any> = mapOf(),
    headers: Map<String, Any> = mapOf(),
    noinline fn: ((MutableHttpRequest<Any?>) -> MutableHttpRequest<Any?>)? = null,
) = this.http<Any, O>(HttpMethod.DELETE, path, body = body, qs = qs, headers = headers, fn = fn)

inline fun <I, reified O> RxHttpClient.doRequest(
    method: HttpMethod,
    path: String,
    body: I? = null,
    qs: Map<String, Any> = mapOf(),
    headers: Map<String, Any> = mapOf(),
    noinline fn: ((MutableHttpRequest<I?>) -> MutableHttpRequest<I?>)? = null,
): Either<Err, O> {
    val req = request<I>(method, path, body, qs, headers, fn)

    val callable = this.retrieve(req, Argument.of(O::class.java))

    return handle(callable)
}

inline fun <I, reified O> RxHttpClient.http(
    method: HttpMethod,
    path: String,
    body: I? = null,
    qs: Map<String, Any> = mapOf(),
    headers: Map<String, Any> = mapOf(),
    noinline fn: ((MutableHttpRequest<I?>) -> MutableHttpRequest<I?>)? = null,
): Either<Err, HttpResponse<O>> {
    val req = request(method, path, body, qs, headers, fn)

    val callable = this.exchange(req, Argument.of(O::class.java))

    return handle(callable)
}

inline fun <I, reified O> RxHttpClient.httpWithResult(
    method: HttpMethod,
    path: String,
    body: I? = null,
    qs: Map<String, Any> = mapOf(),
    headers: Map<String, Any> = mapOf(),
    noinline fn: ((MutableHttpRequest<I?>) -> MutableHttpRequest<I?>)? = null,
): HttpResult {
    val req = request(method, path, body, qs, headers, fn)

    val callable = this.exchange(req, Argument.of(O::class.java))

    return handleWithResult(callable)
}

fun <I> request(
    method: HttpMethod,
    path: String,
    body: I? = null,
    qs: Map<String, Any> = mapOf(),
    headers: Map<String, Any> = mapOf(),
    fn: ((MutableHttpRequest<I?>) -> MutableHttpRequest<I?>)? = null,
): MutableHttpRequest<I?> {
    val uri = if (qs.isEmpty()) path else qs.entries.joinToString("&").let { "$path?$it" }

    val request = HttpRequest.create<I>(method, uri).body(body).json()

    request.setAttribute(HttpAttributes.URI_TEMPLATE, path)

    if (headers.isNotEmpty()) headers.forEach { request.header(it.key, it.value.toString()) }

    if (fn == null) return request

    return fn(request)
}

fun <O> handle(fn: Flowable<O>) = try {
    fn.blockingFirst().right()
} catch (ex: Exception) {
    when (ex) {
        is HttpClientResponseException -> HttpClientError(ex.response, err = ex)
        else -> ServerError(ex)
    }.left()
}

fun <O> handleWithResult(fn: Flowable<O>) = try {
    HttpResult.Success(fn.blockingFirst())
} catch (ex: Exception) {
    when (ex) {
        is HttpClientResponseException -> HttpResult.Error.HttpClientResponse(ex.response, ex.markers())
        else -> HttpResult.Error.UnknownError(ex)
    }
}

inline fun <reified T> HttpClientResponseException.parseResponseOrNull(): T? = runCatching {
    response.getBody(T::class.java).get()
}.getOrNull()

fun HttpClientResponseException.responseBodyAsStringOrNull(): String? = response.getBody(Argument.STRING).orElse(null)

fun <T> HttpResponse<T>.deepMarkers(): LogstashMarker = log(
    "response" to this,
    "responseBody" to getBody(Argument.STRING).orElse(null),
    "status" to status,
    "statusCode" to status.code,
)

fun <T> HttpRequest<T>.markers(includeBody: Boolean = true): LogstashMarker {
    val markers = markers(
        "requestMethod" to method,
        "requestUri" to uri,
    )

    includeBody.takeIf { it }.run { markers.andAppend("requestBody", body) }

    return markers
}

fun HttpClientResponseException.markers(): LogstashMarker = log(
    "status" to response.status,
    "statusCode" to response.status.code,
    "responseBody" to responseBodyAsStringOrNull().orEmpty(),
    "errorMessage" to message,
)

fun <T> HttpResponse<T>.markers(): LogstashMarker = log(
    "responseBody" to getBody(Argument.STRING).orElse(null),
    "status" to status,
    "statusCode" to status.code,
)

sealed class HttpResult : PrintableSealedClassV2() {
    data class Success<O>(val response: O) : HttpResult() // FIXME - O sempre será um FullNettyClientHttpResponse<Type>
    sealed class Error : HttpResult() {
        data class HttpClientResponse(val response: HttpResponse<*>, val markers: LogstashMarker) : Error()
        data class UnknownError(val ex: Exception?) : Error()
    }
}

fun mediaTypeFrom(imageFormat: String): MediaType {
    return when (imageFormat) {
        "png" -> MediaType.IMAGE_PNG_TYPE
        "jpg", "jpeg" -> MediaType.IMAGE_JPEG_TYPE
        "gif" -> MediaType.IMAGE_GIF_TYPE
        else -> MediaType.IMAGE_PNG_TYPE
    }
}