package ai.friday.billpayment

import java.math.BigDecimal
import java.text.DecimalFormat
import java.text.NumberFormat
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.Locale
import kotlin.math.roundToLong

enum class TipoPessoa {
    F, J
}

class FormatterUtils {
    companion object {
        private val numberFormat = NumberFormat.getNumberInstance(Locale.US) as DecimalFormat

        init {
            numberFormat.applyPattern("#.##")
        }

        fun formatDocument(document: String, tipoPessoa: TipoPessoa): String {
            if (document.isEmpty()) {
                return document
            }

            return when (tipoPessoa) {
                TipoPessoa.F -> document.takeLast(11).padStart(11, '0')
                TipoPessoa.J -> document.takeLast(14).padStart(14, '0')
            }
        }

        fun convertToCentsOrZero(decimal: String?): Long {
            if (decimal.isNullOrBlank()) {
                return 0
            }

            return (numberFormat.parse(decimal).toDouble() * 100.0).roundToLong()
        }
    }
}

fun String.formatDocument(tipoPessoa: TipoPessoa) = FormatterUtils.formatDocument(this, tipoPessoa)
fun Float.toLongValue() = (this * 100).toLong()
fun BigDecimal.toLongValue() = this.multiply(BigDecimal(100)).toLong()

fun Long.toAmountFormat(): String {
    val n = NumberFormat.getCurrencyInstance(Locale.forLanguageTag("pt-BR"))
    return n.format(this.toDouble() / 100.0).replace(" ", " ")
}

fun ZonedDateTime.localFormat() = format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
fun ZonedDateTime.toEpochMillis() = this.toInstant().toEpochMilli()

fun Class<*>.originalSimpleName(): String = simpleName.split("\$").first { it.isNotBlank() }