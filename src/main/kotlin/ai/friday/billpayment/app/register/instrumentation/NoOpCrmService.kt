package ai.friday.billpayment.app.register.instrumentation

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.PartialAccount
import ai.friday.billpayment.app.integrations.CrmContact
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.TemporaryCrmContact
import ai.friday.billpayment.app.integrations.TemporaryCrmContactMinimal
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton

@Singleton
@Requires(notEnv = [FRIDAY_ENV, ME_POUPE_ENV])
class NoOpCrmService : CrmService {
    override fun findContact(accountId: AccountId): CrmContact? {
        return null
    }

    override fun findContact(document: Document): CrmContact? {
        return null
    }

    override fun upsertContact(account: Account): CrmContact? {
        return null
    }

    override fun upsertContact(account: PartialAccount): CrmContact? {
        return null
    }

    override fun upsertContact(account: AccountRegisterData): CrmContact? {
        return null
    }

    override fun removeContactAsync(accountId: AccountId) {
    }

    override fun contactExists(accountId: AccountId): Boolean {
        return false
    }

    override fun createContact(contact: TemporaryCrmContact) {
    }

    override fun createContact(contact: TemporaryCrmContactMinimal) {
    }
}