package ai.friday.billpayment.app.register.instrumentation

import ai.friday.billpayment.app.account.AccountClosureDetails
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.register.RiskAnalysisFailedReason

sealed class RegisterInstrumentationEvent(val accountId: AccountId) {
    class Completed(accountId: AccountId, val registrationType: RegistrationType) :
        RegisterInstrumentationEvent(accountId)

    class InternallyRejected(
        accountId: AccountId,
        val registrationType: RegistrationType,
        val deniedReason: List<RiskAnalysisFailedReason>,
    ) :
        RegisterInstrumentationEvent(accountId)

    class Reopened(
        accountId: AccountId,
        val registrationType: RegistrationType,
        val deniedReason: List<RiskAnalysisFailedReason>,
    ) :
        RegisterInstrumentationEvent(accountId)

    class Upgraded(
        accountId: AccountId,
        val registrationType: RegistrationType,
        val reason: List<RiskAnalysisFailedReason>,
    ) :
        RegisterInstrumentationEvent(accountId)

    class ExternallyRejected(accountId: AccountId, val registrationType: RegistrationType) :
        RegisterInstrumentationEvent(accountId)

    class AccountActivated(accountId: AccountId, val registrationType: RegistrationType, val billsCount: Int) :
        RegisterInstrumentationEvent(accountId)

    class AccountClosed(
        accountId: AccountId,
        val accountType: UserAccountType,
        val closureDetails: AccountClosureDetails,
    ) :
        RegisterInstrumentationEvent(accountId)

    class AccountUpgraded(accountId: AccountId) :
        RegisterInstrumentationEvent(accountId)
}