package ai.friday.billpayment.app.register.instrumentation

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.ChatbotType
import ai.friday.billpayment.app.banking.DefaultBankStatementItem
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.integrations.OnePixPayInstrumentation
import ai.friday.billpayment.app.metrics.AbstractFridayCountMetrics
import ai.friday.billpayment.app.metrics.metricRegister
import ai.friday.billpayment.app.notification.BillsComingDueLists
import ai.friday.billpayment.app.onepixpay.OnePixPay
import ai.friday.billpayment.app.onepixpay.ProcessScheduleError
import ai.friday.billpayment.app.onepixpay.QrCodeTransactionId
import ai.friday.billpayment.app.wallet.Wallet

@FridayMePoupe
open class OnePixPayInstrumentation : OnePixPayInstrumentation {
    override fun requestedToVerifyBillsComingDueForWallet(wallet: Wallet) {
        metricRegister(OnePixPayMetric.RequestedVerifyBillsComingDue)
    }

    override fun accountIsNotSupported(wallet: Wallet, founder: Account, chatbotType: ChatbotType) {
        metricRegister(OnePixPayMetric.AccountNotSupported)
    }

    override fun decidedNotToNotifyBillsComingDue(wallet: Wallet, account: Account, bills: BillsComingDueLists) {
        metricRegister(OnePixPayMetric.DecidedNotToNotifyBillsComingDue)
    }

    override fun decidedNotToNotifyBillsComingDue(wallet: Wallet, account: Account, bills: List<BillView>) {
        metricRegister(OnePixPayMetric.DecidedNotToNotifyBillsComingDue)
    }

    override fun decidedToNotifyBillsComingDue(wallet: Wallet, account: Account, bills: List<BillView>) {
        metricRegister(OnePixPayMetric.DecidedToNotifyBillsComingDue)
    }

    override fun userReceivedADeposit(accountId: AccountId, deposit: DefaultBankStatementItem) {
        val transactionId = deposit.ref?.let { QrCodeTransactionId.from(it) }

        val metric = when {
            transactionId == null -> OnePixPayMetric.UserReceivedADeposit.Other
            transactionId.isProbableCopyAndPasteTransactionId -> OnePixPayMetric.UserReceivedADeposit.ProbableCopyAndPaste
            transactionId.isProbableOnePixPayTransactionId -> OnePixPayMetric.UserReceivedADeposit.ProbableOnePixPay
            else -> OnePixPayMetric.UserReceivedADeposit.Other
        }

        metricRegister(metric)
    }

    override fun onePixPayFailedToUnscheduleBills(wallet: Wallet, account: Account, bills: List<Bill>) {
        metricRegister(OnePixPayMetric.OnePixPayFailedToUnscheduleBills)
    }

    override fun onePixPayFailed(wallet: Wallet, account: Account, reason: ProcessScheduleError) {
        metricRegister(OnePixPayMetric.OnePixPayFailed)
    }

    override fun onePixPayFullyProcessed(wallet: Wallet, account: Account, onePixPay: OnePixPay, bills: List<Bill>) {
        metricRegister(OnePixPayMetric.OnePixPayFullyProcessed)
    }

    override fun onePixPayPartiallyProcessed(wallet: Wallet, account: Account, onePixPay: OnePixPay) {
        metricRegister(OnePixPayMetric.OnePixPayPartiallyProcessed)
    }
}

sealed class OnePixPayMetric {
    data object RequestedVerifyBillsComingDue : AbstractFridayCountMetrics("one_pix_pay.requested_to_verify_bills_coming_due")
    data object AccountNotSupported : AbstractFridayCountMetrics("one_pix_pay.account_not_supported")
    data object DecidedToNotifyBillsComingDue : AbstractFridayCountMetrics("one_pix_pay.decided_to_notify_bills_coming_due")
    data object DecidedNotToNotifyBillsComingDue : AbstractFridayCountMetrics("one_pix_pay.decided_not_to_notify_bills_coming_due")
    sealed class UserReceivedADeposit {
        data object ProbableOnePixPay : AbstractFridayCountMetrics("one_pix_pay.user_received_a_deposit.probable_one_pix_pay")
        data object ProbableCopyAndPaste : AbstractFridayCountMetrics("one_pix_pay.user_received_a_deposit.probable_copy_and_paste")
        data object Other : AbstractFridayCountMetrics("one_pix_pay.user_received_a_deposit.other")
    }

    data object OnePixPayFailedToUnscheduleBills : AbstractFridayCountMetrics("one_pix_pay.failed_to_unscheduled_bills")
    data object OnePixPayFailed : AbstractFridayCountMetrics("one_pix_pay.failed")
    data object OnePixPayFullyProcessed : AbstractFridayCountMetrics("one_pix_pay.success.full")
    data object OnePixPayPartiallyProcessed : AbstractFridayCountMetrics("one_pix_pay.success.partial")
}