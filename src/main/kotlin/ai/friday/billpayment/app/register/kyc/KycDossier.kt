package ai.friday.billpayment.app.register.kyc

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.Gender
import ai.friday.billpayment.app.account.IdentityValidationStatus
import ai.friday.billpayment.app.account.MonthlyIncome
import ai.friday.billpayment.app.account.PepQuery
import ai.friday.billpayment.app.account.PepQueryResult
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.getBirthdate
import ai.friday.billpayment.app.account.getCPF
import ai.friday.billpayment.app.account.getName
import ai.friday.billpayment.app.account.isExposed
import ai.friday.billpayment.app.documentscan.DocumentScanDigitalSpoofStatus
import ai.friday.billpayment.app.documentscan.DocumentScanFaceResult
import ai.friday.billpayment.app.documentscan.DocumentScanFaceStatus
import ai.friday.billpayment.app.documentscan.DocumentScanOcrResult
import ai.friday.billpayment.app.documentscan.DocumentScanResult
import ai.friday.billpayment.app.documentscan.DocumentScanTextResult
import ai.friday.billpayment.app.documentscan.DocumentScanTextStatus
import ai.friday.billpayment.app.integrations.TemplateForm
import ai.friday.billpayment.app.liveness.LivenessEnrollmentVerification
import ai.friday.billpayment.app.payment.formatDocument
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import java.text.Collator
import java.text.Normalizer
import java.text.NumberFormat
import java.time.Duration
import java.time.LocalDate
import java.time.Period
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.Locale

private val dateFormat = DateTimeFormatter.ofPattern("dd/MM/yyyy")

interface Dossier {
    fun toForm(accountRegisterData: AccountRegisterData, faceMatch: FaceMatchResult?, documentScan: DocumentScanResult?): TemplateForm
}

/**
 * Esta é uma representação dos dados consultados no BigDataService.
 * Os dados do arquivo gerado são baseados nesta classe em conjunto com o AccountRegisterData e o FaceMatchResult,
 * passados para o método [toForm].
 */
data class KycDossier(
    val gender: Gender?,
    val taxId: KycDossierTaxIdRegion,
    val motherName: String?,
    val fatherName: String?,
    val phones: List<KycDossierPhone>,
    val emails: List<KycDossierEmail>,
    val mep: KycDossierMepQuery,
    val pep: PepQuery,
    val officialData: KycDossierOfficialDocumentData,
    val sanctions: List<KycDossierSanctions>,
    val globalSanctionsList: List<String>,
) : Dossier {
    override fun toForm(accountRegisterData: AccountRegisterData, faceMatch: FaceMatchResult?, documentScan: DocumentScanResult?): KycDossierForm {
        return KycDossierForm(
            accountId = accountRegisterData.accountId.value,
            registrationType = when (accountRegisterData.registrationType) {
                RegistrationType.BASIC -> "Simplificado"
                RegistrationType.FULL, RegistrationType.UPGRADED -> "Completo"
            },
            createdAt = ZonedDateTime.now().toFormString(),
            risk = when (calculateRisk(accountRegisterData)) {
                RISK.HIGH -> KycDossierFormRisk(result = "Alto", warning = true)
                RISK.MEDIUM -> KycDossierFormRisk(result = "Médio", warning = true)
                RISK.LOW -> KycDossierFormRisk(result = "Baixo", warning = false)
            },
            personalData = KycDossierFormPersonalData(
                fullName = KycDossierFormPersonalData.MandatoryWithQuery(
                    declared = accountRegisterData.getName(),
                    query = checkSameName(accountRegisterData.getName(), officialData.name).let { isSameName ->
                        KycDossierFormPersonalData.Query(
                            value = officialData.name,
                            result = if (isSameName) "Sim (${officialData.provider})" else "Não (${officialData.provider})",
                            warning = !isSameName,
                        )
                    },
                ),
                birthDate = KycDossierFormPersonalData.MandatoryWithQuery(
                    declared = accountRegisterData.getBirthdate()?.format(dateFormat)
                        ?: throw IllegalStateException("Expected user birth date when creating KYC form"),
                    query = (accountRegisterData.getBirthdate() == officialData.birthDate).let { isSameBirthDate ->
                        KycDossierFormPersonalData.Query(
                            value = officialData.birthDate.format(dateFormat),
                            result = if (isSameBirthDate) "Sim (${officialData.provider})" else "Não (${officialData.provider})",
                            warning = !isSameBirthDate,
                        )
                    },
                ),
                gender = when (gender) {
                    Gender.M -> "Masculino"
                    Gender.F -> "Feminino"
                    null -> "Não disponível"
                },
                motherName = KycDossierFormPersonalData.OptionalWithQuery(
                    declared = accountRegisterData.documentInfo?.motherName,
                    query = let {
                        val match =
                            if (motherName == null || accountRegisterData.documentInfo == null) {
                                null
                            } else {
                                matchString(motherName, accountRegisterData.documentInfo.motherName)
                            }

                        KycDossierFormPersonalData.Query(
                            value = motherName ?: "Não disponível",
                            result = if (match == null) "Não disponível" else if (match) "Semelhante" else "Divergente",
                            warning = match == false,
                        )
                    },
                ),
                fatherName = KycDossierFormPersonalData.OptionalWithQuery(
                    declared = accountRegisterData.documentInfo?.fatherName,
                    query = let {
                        val match =
                            if (fatherName == null || accountRegisterData.documentInfo == null) {
                                null
                            } else {
                                matchString(fatherName, accountRegisterData.documentInfo.fatherName)
                            }

                        KycDossierFormPersonalData.Query(
                            value = fatherName ?: "Não disponível",
                            result = if (match == null) "Não disponível" else if (match) "Semelhante" else "Divergente",
                            warning = match == false,
                        )
                    },
                ),
            ),
            biometry = KycDossierFormBiometry(
                liveness = KycDossierFormBiometry.Liveness(
                    result = let {
                        if (accountRegisterData.uploadedSelfie == null) {
                            "Não realizada"
                        } else {
                            accountRegisterData.uploadedSelfieDateTime?.toFormString()?.let {
                                "Sim (feita $it)"
                            } ?: "Sim"
                        }
                    },
                    warning = accountRegisterData.uploadedSelfie == null,
                ),
                identity = let {
                    val percentage = accountRegisterData.identityValidationPercentage.toPercentageString()
                    when (accountRegisterData.identityValidationStatus) {
                        IdentityValidationStatus.CHECKED ->
                            KycDossierFormBiometry.WithPercentage(
                                result = "Coincide com base oficial",
                                percentage = percentage,
                                warning = false,
                            )

                        IdentityValidationStatus.REJECTED -> KycDossierFormBiometry.WithPercentage(
                            result = "Divergente da base oficial",
                            percentage = percentage,
                            warning = true,
                        )

                        IdentityValidationStatus.UNKNOWN -> KycDossierFormBiometry.WithPercentage(
                            result = "Não encontrado em base oficial",
                            percentage = percentage,
                            warning = false,
                        )

                        null -> KycDossierFormBiometry.WithPercentage(
                            result = "Não verificado",
                            percentage = percentage,
                            warning = true,
                        )
                    }
                },
                duplications = accountRegisterData.livenessEnrollmentVerification.duplications.toKycDossierFormBiometry(),
                fraudIndications = accountRegisterData.livenessEnrollmentVerification.fraudIndications.toKycDossierFormBiometry(),
                documentFaceMatch = KycDossierFormBiometry.WithPercentage(
                    result = faceMatch?.let { if (it.match) "Sim" else "Não" } ?: "Não verificado",
                    percentage = faceMatch?.similarity.toPercentageString(),
                    warning = let {
                        if (accountRegisterData.hasUploadedDocument) faceMatch == null || !faceMatch.match else false
                    },
                ),
            ),
            documentScan = KycDossierFormDocumentScan(
                digitalSpoof = documentScan?.digitalSpoof?.let {
                    when (it.status) {
                        DocumentScanDigitalSpoofStatus.LIKELY_PHYSICAL_ID -> KycDossierFormDocumentScan.ValueWithWarning(value = "Provavelmente", warning = false)
                        DocumentScanDigitalSpoofStatus.CANNOT_CONFIRM_PHYSICAL_ID -> KycDossierFormDocumentScan.ValueWithWarning(value = "Inconclusivo", warning = true)
                    }
                } ?: KycDossierFormDocumentScan.ValueWithWarning(value = "Não disponível", warning = true),
                face = documentScan?.face?.let {
                    when (it) {
                        DocumentScanFaceResult.NotFound -> KycDossierFormDocumentScan.ValueWithWarning(value = "Rosto não encontrado", warning = true)
                        is DocumentScanFaceResult.Found -> when (it.status) {
                            DocumentScanFaceStatus.LIKELY_ORIGINAL_FACE -> KycDossierFormDocumentScan.ValueWithWarning(value = "Provavelmente não", warning = false)
                            DocumentScanFaceStatus.CANNOT_CONFIRM_ID_IS_AUTHENTIC -> KycDossierFormDocumentScan.ValueWithWarning(value = "Inconclusivo", warning = true)
                            DocumentScanFaceStatus.TEMPLATE_DOES_NOT_SUPPORT_DETECTION -> KycDossierFormDocumentScan.ValueWithWarning(value = "Documento não permite análise", warning = true)
                        }
                    }
                } ?: KycDossierFormDocumentScan.ValueWithWarning(value = "Não disponível", warning = true),
                text = documentScan?.text?.let {
                    when (it) {
                        DocumentScanTextResult.NotPerformed -> KycDossierFormDocumentScan.ValueWithWarning(value = "Análise não realizada", warning = true)
                        is DocumentScanTextResult.Performed -> when (it.status) {
                            DocumentScanTextStatus.LIKELY_ORIGINAL_TEXT -> KycDossierFormDocumentScan.ValueWithWarning(value = "Provavelmente não", warning = false)
                            DocumentScanTextStatus.CANNOT_CONFIRM_ID_IS_AUTHENTIC -> KycDossierFormDocumentScan.ValueWithWarning(value = "Inconclusivo", warning = true)
                        }
                    }
                } ?: KycDossierFormDocumentScan.ValueWithWarning(value = "Não disponível", warning = true),
                ocr = documentScan?.ocr?.let {
                    when (it) {
                        DocumentScanOcrResult.Matched -> KycDossierFormDocumentScan.ValueWithWarning(value = "Realizada com sucesso", warning = false)
                        DocumentScanOcrResult.NotMatched -> KycDossierFormDocumentScan.ValueWithWarning(value = "Formato de documento não reconhecido", warning = true)
                    }
                } ?: KycDossierFormDocumentScan.ValueWithWarning(value = "Não disponível", warning = true),
            ),
            cpf = KycDossierFormCpfQuery(
                value = formatDocument(accountRegisterData.getCPF()),
                status = KycDossierFormCpfQuery.ValueWithWarning(
                    value = if (officialData.regular) "Regular (${officialData.provider})" else "Irregular (${officialData.status} - ${officialData.provider})",
                    warning = !officialData.regular,
                ),
                hasObitIndication = KycDossierFormCpfQuery.ValueWithWarning(
                    value = "${if (officialData.hasObitIndication) "Sim" else "Não"} (${officialData.provider})",
                    warning = officialData.hasObitIndication,
                ),
                inFraudList = KycDossierFormCpfQuery.ValueWithWarning(
                    value = if (accountRegisterData.fraudListMatch) "Sim" else "Não",
                    warning = accountRegisterData.fraudListMatch,
                ),
                region = taxId.region ?: "Não disponível",
                sanctions = sanctions.map {
                    KycDossierFormCpfQuery.Saction(
                        value = "${it.type.toFormString()} (${it.source} / ${it.matchRate.toPercentageString()} de probabilidade)",
                        warning = true,
                    )
                },
            ),
            email = emails.find { it.value == accountRegisterData.emailAddress }.let {
                KycDossierFormEmailOrPhone(
                    value = accountRegisterData.emailAddress.value,
                    createdAt = it?.createdAt?.toAgeInDaysString() ?: "Não disponível",
                    recent = if (it == null) "Não disponível" else if (it.createdAt.calculateAgeInDays() <= PHONE_AND_EMAIL_AGE_IN_DAYS_THRESHOLD) "Sim" else "Não",
                    active = it?.active?.let { active -> if (active) "Sim" else "Não" } ?: "Não disponível",
                    relatedToCpf = if (it != null) "Sim" else "Não",
                )
            },
            phone = phones.find { it.toFormString() == accountRegisterData.mobilePhone.toString() }.let {
                KycDossierFormEmailOrPhone(
                    value = accountRegisterData.mobilePhone!!.toString(),
                    createdAt = it?.createdAt?.toAgeInDaysString() ?: "Não disponível",
                    recent = if (it == null) "Não disponível" else if (it.createdAt.calculateAgeInDays() <= PHONE_AND_EMAIL_AGE_IN_DAYS_THRESHOLD) "Sim" else "Não",
                    active = it?.active?.let { active -> if (active) "Sim" else "Não" } ?: "Não disponível",
                    relatedToCpf = if (it != null) "Sim" else "Não",
                )
            },
            monthlyIncome = KycDossierFormMonthlyIncome(
                value = accountRegisterData.monthlyIncome?.toFormString() ?: "Não declarado",
            ),
            mep = KycDossierFormMep(
                exposure = KycDossierFormMep.Exposure(
                    level = mep.exposureLevel ?: "Não disponível",
                    warning = mep.exposureLevel?.uppercase() in exposedMepLevels,
                ),
                celebrity = KycDossierFormMep.Exposure(
                    level = mep.celebrityLevel ?: "Não disponível",
                    warning = mep.celebrityLevel?.uppercase() in exposedMepLevels,
                ),
                unpopularity = KycDossierFormMep.Exposure(
                    level = mep.unpopularityLevel ?: "Não disponível",
                    warning = mep.unpopularityLevel?.uppercase() in exposedMepLevels,
                ),
            ),
            pep = pep.toKycDossierFormPep(
                accountRegisterData.politicallyExposed?.selfDeclared
                    ?: throw IllegalStateException("Self-declared PEP should be avaialable when creating KYC"),
            ),
            globalSanctionsList = globalSanctionsList,
        )
    }

    private fun getAge(birthdate: LocalDate): Int {
        return Period.between(
            birthdate,
            getZonedDateTime().toLocalDate(),
        ).years
    }

    private fun calculateRisk(accountRegisterData: AccountRegisterData): RISK {
        if (!officialData.regular || officialData.hasObitIndication) {
            return RISK.HIGH
        }

        if (getAge(officialData.birthDate) < 18) {
            return RISK.HIGH
        }

        if (getAge(officialData.birthDate) >= 80) {
            return RISK.HIGH
        }

        if (sanctions.isNotEmpty()) {
            return RISK.HIGH
        }

        if (mep.unpopularityLevel?.uppercase() in listOf("A", "B", "C", "D")) {
            return RISK.HIGH
        }

        if (mep.celebrityLevel?.uppercase() in listOf("A", "B", "C", "D")) {
            return RISK.HIGH
        }

        val selfDeclaredPep = accountRegisterData.politicallyExposed?.selfDeclared
            ?: throw IllegalStateException("Self-declared PEP should be available when calculating KYC risk")

        if (selfDeclaredPep || pep.result.isExposed()) {
            return RISK.MEDIUM
        }

        return RISK.LOW
    }
}

data class KycDossierFormRisk(
    val result: String,
    val warning: Boolean,
)

data class KycDossierPhone(
    val number: String,
    val areaCode: String,
    val countryCode: String,
    val createdAt: LocalDate,
    val active: Boolean? = null,
)

data class KycDossierEmail(
    val value: EmailAddress,
    val createdAt: LocalDate,
    val active: Boolean? = null,
)

data class KycDossierTaxIdRegion(
    val region: String? = null,
)

data class KycDossierTaxIdStatus(
    val regular: Boolean,
    val description: String,
    val createdAt: LocalDate,
)

data class FaceMatchResult(
    val match: Boolean,
    val similarity: Int,
)

const val PHONE_AND_EMAIL_AGE_IN_DAYS_THRESHOLD: Long = 365

private enum class RISK { HIGH, MEDIUM, LOW }

data class KycDossierSanctions(
    val source: String,
    val type: KycDossierSanctionType,
    val matchRate: Int,
    val nameUniquenessScore: Double,
)

private val exposedMepLevels = listOf("A", "B", "C", "D")

data class KycDossierMepQuery(
    val exposureLevel: String?,
    val celebrityLevel: String?,
    val unpopularityLevel: String?,
) {
    val isExposed =
        exposureLevel?.uppercase() in exposedMepLevels || celebrityLevel?.uppercase() in exposedMepLevels || unpopularityLevel?.uppercase() in exposedMepLevels

    companion object {
        fun empty() = KycDossierMepQuery(
            exposureLevel = null,
            celebrityLevel = null,
            unpopularityLevel = null,
        )
    }
}

data class KycDossierOfficialDocumentData(
    val provider: String,
    val name: String,
    val birthDate: LocalDate,
    val socialName: String?,
    val hasObitIndication: Boolean,
    val deathYear: String?,
    val regular: Boolean,
    val status: String,
)

sealed class KycDossierSanctionType(open val value: String? = null) {
    data object ArrestWarrants : KycDossierSanctionType("ARREST WARRANTS")
    data object FinancialCrimes : KycDossierSanctionType("FINANCIAL CRIMES")
    data object Terrorism : KycDossierSanctionType("TERRORISM")
    data object FinancialInfractions : KycDossierSanctionType("FINANCIAL INFRACTIONS")
    data object EnvironmentalInfractions : KycDossierSanctionType("ENVIRONMENTAL INFRACTIONS")
    data object Corruption : KycDossierSanctionType("CORRUPTION")
    data object SlaveryCrimes : KycDossierSanctionType("SLAVERY CRIMES")
    data class Other(override val value: String) : KycDossierSanctionType(value)

    companion object {
        fun of(value: String): KycDossierSanctionType {
            return when (value.uppercase()) {
                ArrestWarrants.value -> ArrestWarrants
                FinancialCrimes.value -> FinancialCrimes
                Terrorism.value -> Terrorism
                FinancialInfractions.value -> FinancialInfractions
                EnvironmentalInfractions.value -> EnvironmentalInfractions
                Corruption.value -> Corruption
                SlaveryCrimes.value -> SlaveryCrimes
                else -> Other(value)
            }
        }
    }
}

data class KycDossierForm(
    val accountId: String,
    val registrationType: String,
    val createdAt: String,
    val risk: KycDossierFormRisk,
    val personalData: KycDossierFormPersonalData,
    val biometry: KycDossierFormBiometry,
    val documentScan: KycDossierFormDocumentScan,
    val cpf: KycDossierFormCpfQuery,
    val email: KycDossierFormEmailOrPhone,
    val phone: KycDossierFormEmailOrPhone,
    val monthlyIncome: KycDossierFormMonthlyIncome,
    val mep: KycDossierFormMep,
    val pep: KycDossierFormPep,
    val globalSanctionsList: List<String>,
) : TemplateForm

data class KycDossierFormPersonalData(
    val fullName: MandatoryWithQuery,
    val birthDate: MandatoryWithQuery,
    val gender: String,
    val motherName: OptionalWithQuery,
    val fatherName: OptionalWithQuery,
) {
    data class MandatoryWithQuery(
        val declared: String,
        val query: Query,
    )

    data class OptionalWithQuery(
        val declared: String?,
        val query: Query,
    )

    data class Query(
        val value: String,
        val result: String,
        val warning: Boolean,
    )
}

data class KycDossierFormBiometry(
    val liveness: Liveness,
    val identity: WithPercentage,
    val duplications: WithAccounts,
    val fraudIndications: WithAccounts,
    val documentFaceMatch: WithPercentage,
) {
    data class Liveness(
        val result: String,
        val warning: Boolean,
    )

    data class WithPercentage(
        val result: String,
        val percentage: String?,
        val warning: Boolean,
    )

    data class WithAccounts(
        val result: String,
        val accounts: List<String>?,
        val warning: Boolean,
    )
}

data class KycDossierFormDocumentScan(
    val digitalSpoof: ValueWithWarning,
    val face: ValueWithWarning,
    val text: ValueWithWarning,
    val ocr: ValueWithWarning,
) {
    data class ValueWithWarning(
        val value: String,
        val warning: Boolean,
    )
}

data class KycDossierFormCpfQuery(
    val value: String,
    val status: ValueWithWarning,
    val hasObitIndication: ValueWithWarning,
    val inFraudList: ValueWithWarning,
    val region: String,
    val sanctions: List<Saction>,
) {
    data class ValueWithWarning(
        val value: String,
        val warning: Boolean,
    )

    data class Saction(
        val value: String,
        val warning: Boolean,
    )
}

data class KycDossierFormEmailOrPhone(
    val value: String,
    val createdAt: String,
    val recent: String,
    val active: String,
    val relatedToCpf: String,
)

data class KycDossierFormMonthlyIncome(
    val value: String,
)

data class KycDossierFormMep(
    val exposure: Exposure,
    val celebrity: Exposure,
    val unpopularity: Exposure,
) {
    data class Exposure(
        val level: String,
        val warning: Boolean,
    )
}

data class KycDossierFormPep(
    val selfDeclared: ValueWithWarning,
    val query: Query,
) {
    data class Query(
        val value: String,
        val level: String?,
        val warning: Boolean,
    )

    data class ValueWithWarning(
        val value: String,
        val warning: Boolean,
    )
}

private fun PepQuery.toKycDossierFormPep(selfDeclared: Boolean) =
    KycDossierFormPep(
        query = KycDossierFormPep.Query(
            value = if (result.isExposed()) "Sim" else "Não",
            level = when (result) {
                PepQueryResult.DIRECT_PEP -> "Nível 1"
                PepQueryResult.RELATED_TO_PEP -> "Nível 2"
                PepQueryResult.NOT_PEP -> null
            },
            warning = result.isExposed(),
        ),
        selfDeclared = KycDossierFormPep.ValueWithWarning(
            value = if (selfDeclared) "Sim" else "Não",
            warning = selfDeclared,
        ),
    )

private fun MonthlyIncome?.toFormString(): String? {
    return when {
        this == null -> null
        lowerBound == 0L && upperBound == 2_000_00L -> "Até R$ 2.000,00"
        lowerBound == 2_000_01L && upperBound == 4_000_00L -> "Entre R$ 2.000,00 e R$ 4.000,00"
        lowerBound == 4_000_01L && upperBound == 10_000_00L -> "Entre R$ 4.000,00 e R$ 10.000,00"
        lowerBound == 10_000_01L && upperBound == 20_000_00L -> "Entre R$ 10.000,00 e R$ 20.000,00"
        lowerBound == 20_000_01L && upperBound == null -> "Acima de R$ 20.000,00"
        else -> throw IllegalArgumentException("$this is not a valid monthly income value")
    }
}

private fun KycDossierSanctionType.toFormString(): String {
    return when (this) {
        KycDossierSanctionType.ArrestWarrants -> "Mandados de prisão emitidos"
        KycDossierSanctionType.Corruption -> "Infrações e crimes no âmbito de improbidade administrativa"
        KycDossierSanctionType.EnvironmentalInfractions -> "Infrações e crimes ao meio ambiente"
        KycDossierSanctionType.FinancialCrimes -> "Crimes envolvendo lavagem de dinheiro do crime organizado e/ou tráfico de drogas"
        KycDossierSanctionType.FinancialInfractions -> "Infrações no âmbito do mercado financeiro"
        KycDossierSanctionType.SlaveryCrimes -> "Crimes relacionados ao trabalho escravo"
        KycDossierSanctionType.Terrorism -> "Associação com entidades, organizações e/ou atividades terroristas"
        is KycDossierSanctionType.Other -> this.value
    }
}

private fun LivenessEnrollmentVerification.Result.toKycDossierFormBiometry(): KycDossierFormBiometry.WithAccounts {
    return if (this is LivenessEnrollmentVerification.Result.Verified) {
        KycDossierFormBiometry.WithAccounts(
            result = if (this.accountIds.isNotEmpty()) "Sim" else "Não",
            accounts = this.accountIds.map { accountId -> accountId.value },
            warning = this.accountIds.isNotEmpty(),
        )
    } else {
        KycDossierFormBiometry.WithAccounts("Não verificado", accounts = null, warning = true)
    }
}

private fun KycDossierPhone.toFormString(): String {
    return "+$countryCode$areaCode$number"
}

fun ZonedDateTime.toFormString(): String {
    return "${
    this.format(DateTimeFormatter.ofPattern("dd/MM/yyyy").withZone(brazilTimeZone))
    } às ${
    this.format(
        DateTimeFormatter.ofPattern("HH:mm").withZone(brazilTimeZone),
    )
    }"
}

fun Double?.toPercentageString(): String? {
    return this?.let {
        NumberFormat.getPercentInstance(Locale.forLanguageTag("pt-BR")).format(it)
    }
}

private fun Int?.toPercentageString(): String? {
    return this?.let {
        NumberFormat.getPercentInstance(Locale.forLanguageTag("pt-BR")).format(it / 100.0)
    }
}

private fun LocalDate.calculateAgeInDays(): Long {
    return Duration.between(atStartOfDay(), getZonedDateTime().toLocalDate().atStartOfDay()).toDays()
}

private fun LocalDate.toAgeInDaysString(): String {
    return Duration.between(atStartOfDay(), getZonedDateTime().toLocalDate().atStartOfDay()).toDays().let {
        if (it == 1L) "$it dia" else "$it dias"
    }
}

private fun checkSameName(name1: String, name2: String): Boolean {
    val collator = Collator.getInstance()
    collator.strength = Collator.PRIMARY
    return collator.compare(name1, name2) == 0
}

private fun matchString(arg1: String, arg2: String): Boolean {
    return arg1.removeDiacritics().removeEmptySpaces()
        .equals(arg2.removeDiacritics().removeEmptySpaces(), ignoreCase = true)
}

private val RegexDiacritics = "\\p{InCombiningDiacriticalMarks}+".toRegex()

private fun String.removeDiacritics(): String {
    val temp = Normalizer.normalize(this, Normalizer.Form.NFD)
    return RegexDiacritics.replace(temp, "")
}

private val RegexEmptySpaces = "\\s+".toRegex()

private fun String.removeEmptySpaces(): String {
    return replace(RegexEmptySpaces, "")
}