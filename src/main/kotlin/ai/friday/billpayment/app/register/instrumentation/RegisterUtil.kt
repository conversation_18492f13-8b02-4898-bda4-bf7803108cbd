package ai.friday.billpayment.app.register.instrumentation

import ai.friday.billpayment.app.account.Gender
import ai.friday.billpayment.app.stripAccents
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

class RegisterUtil {
    companion object {
        fun findGenderByName(name: String): Gender {
            val namesStream = Thread.currentThread().contextClassLoader.getResourceAsStream("names/grupos.csv")
            val prefix = name.substringBefore(" ").stripAccents().uppercase() + ","

            return namesStream.bufferedReader().lines().filter {
                it.startsWith(prefix)
            }.map {
                when {
                    it.endsWith("M") -> Gender.M
                    it.endsWith("F") -> Gender.F
                    else -> {
                        logger.error(Markers.append("name", name).and(Markers.append("line", it)), "findGenderByName")
                        Gender.M
                    }
                }
            }.findFirst().or<PERSON><PERSON>e(Gender.M)
        }

        private val logger = LoggerFactory.getLogger(RegisterUtil::class.java)
    }
}