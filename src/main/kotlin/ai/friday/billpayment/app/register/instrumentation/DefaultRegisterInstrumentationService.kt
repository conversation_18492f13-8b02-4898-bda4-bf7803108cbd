package ai.friday.billpayment.app.register.instrumentation

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountClosureDetails
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.instrumentation.BaseInstrumentationService
import ai.friday.billpayment.app.instrumentation.InstrumentationRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.RegisterInstrumentationService
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.register.RiskAnalysisFailedReason
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.morning.log.andAppend
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
open class DefaultRegisterInstrumentationService(
    instrumentationRepository: InstrumentationRepository<RegisterInstrumentationEvent>,
    val billRepository: BillRepository,
    val walletRepository: WalletRepository,
) : BaseInstrumentationService<RegisterInstrumentationEvent>(instrumentationRepository),
    RegisterInstrumentationService {

    override fun completed(accountId: AccountId, registrationType: RegistrationType) {
        val markers = Markers.append("accountId", accountId).andAppend("registrationType", registrationType)
        try {
            publishEventAsync(RegisterInstrumentationEvent.Completed(accountId, registrationType))
            LOG.info(
                markers,
                "RegisterInstrumentationCompleted",
            )
        } catch (e: Exception) {
            LOG.error(
                markers,
                "RegisterInstrumentationCompleted",
                e,
            )
        }
    }

    override fun rejected(
        accountId: AccountId,
        registrationType: RegistrationType,
        rejectedReason: List<RiskAnalysisFailedReason>,
    ) {
        val markers = Markers.append("accountId", accountId).andAppend("registrationType", registrationType)
            .andAppend("rejectedReason", rejectedReason)
        try {
            publishEventAsync(
                RegisterInstrumentationEvent.InternallyRejected(
                    accountId,
                    registrationType,
                    rejectedReason,
                ),
            )
            LOG.info(
                markers,
                "RegisterInstrumentationRejected",
            )
        } catch (e: Exception) {
            LOG.error(
                markers,
                "RegisterInstrumentationRejected",
                e,
            )
        }
    }

    override fun upgraded(
        accountId: AccountId,
        registrationType: RegistrationType,
        reason: List<RiskAnalysisFailedReason>,
    ) {
        val markers = Markers.append("accountId", accountId).andAppend("registrationType", registrationType)
            .andAppend("upgradeReason", reason)
        try {
            publishEventAsync(
                RegisterInstrumentationEvent.Upgraded(
                    accountId,
                    registrationType,
                    reason,
                ),
            )
            LOG.info(
                markers,
                "RegisterInstrumentationUpgraded",
            )
        } catch (e: Exception) {
            LOG.error(
                markers,
                "RegisterInstrumentationUpgraded",
                e,
            )
        }
    }

    override fun reopened(
        accountId: AccountId,
        registrationType: RegistrationType,
        deniedReason: List<RiskAnalysisFailedReason>,
    ) {
        val markers = Markers.append("accountId", accountId).andAppend("registrationType", registrationType).andAppend(
            "deniedReason",
            deniedReason,
        )
        try {
            publishEventAsync(RegisterInstrumentationEvent.Reopened(accountId, registrationType, deniedReason))
            LOG.info(
                markers,
                "RegisterInstrumentationReopened",
            )
        } catch (e: Exception) {
            LOG.error(
                markers,
                "RegisterInstrumentationReopened",
                e,
            )
        }
    }

    override fun externallyRejected(accountId: AccountId, registrationType: RegistrationType) {
        val markers = Markers.append("accountId", accountId).andAppend("registrationType", registrationType)
        try {
            publishEventAsync(
                RegisterInstrumentationEvent.ExternallyRejected(
                    accountId,
                    registrationType = registrationType,
                ),
            )
            LOG.info(
                markers,
                "RegisterInstrumentationExternallyRejected",
            )
        } catch (e: Exception) {
            LOG.error(
                markers,
                "RegisterInstrumentationExternallyRejected",
                e,
            )
        }
    }

    override fun activated(accountId: AccountId, registrationType: RegistrationType) {
        val markers = Markers.append("accountId", accountId).andAppend("registrationType", registrationType)
        try {
            val billsCount = walletRepository.findWallets(accountId, MemberStatus.ACTIVE).fold(0) { acc, wallet ->
                acc + billRepository.findByWallet(wallet.id).size
            }

            publishEventAsync(
                RegisterInstrumentationEvent.AccountActivated(
                    accountId = accountId,
                    registrationType = registrationType,
                    billsCount = billsCount,
                ),
            )
            LOG.info(
                markers,
                "RegisterInstrumentationActivated",
            )
        } catch (e: Exception) {
            LOG.error(
                markers,
                "RegisterInstrumentationActivated",
                e,
            )
        }
    }

    override fun closed(accountId: AccountId, accountType: UserAccountType, closureDetails: AccountClosureDetails) {
        val markers = Markers.append("accountId", accountId).andAppend("closureDetails", closureDetails)
        try {
            publishEventAsync(
                RegisterInstrumentationEvent.AccountClosed(
                    accountId = accountId,
                    accountType = accountType,
                    closureDetails = closureDetails,
                ),
            )
            LOG.info(
                markers,
                "RegisterInstrumentationClosed",
            )
        } catch (e: Exception) {
            LOG.error(
                markers,
                "RegisterInstrumentationClosed",
                e,
            )
        }
    }

    override fun accountUpgraded(accountId: AccountId) {
        val markers = Markers.append("accountId", accountId)
        try {
            publishEventAsync(
                RegisterInstrumentationEvent.AccountUpgraded(
                    accountId = accountId,
                ),
            )
            LOG.info(
                markers,
                "RegisterInstrumentationActivated",
            )
        } catch (e: Exception) {
            LOG.error(
                markers,
                "RegisterInstrumentationActivated",
                e,
            )
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(DefaultRegisterInstrumentationService::class.java)
    }
}