package ai.friday.billpayment.app.message

import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.notification.NotificationHintService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.findAuthorOrNull
import ai.friday.billpayment.log
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import org.slf4j.LoggerFactory

open class BillAddedNotificationService<T : BillEvent>(
    private val isBillAddedNotificationEnabled: Boolean,
    private val walletService: WalletService,
    private val notificationAdapter: NotificationAdapter,
    private val notificationHintService: NotificationHintService,
) : EventNotificationService<T> {
    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun notify(event: T): BillEventNotificationResult =
        with(Bill.build(event)) {
            if (!isBillAddedNotificationEnabled) return BillEventNotificationResult.Skip

            val shouldNotify =
                when (event.actionSource) {
                    is ActionSource.DDA,
                    is ActionSource.Api,
                    is ActionSource.WalletMailBox,
                    is ActionSource.ConnectUtility,
                    is ActionSource.VehicleDebts,
                    -> true

                    else -> false
                }

            if (!shouldNotify) return BillEventNotificationResult.Skip

            if (dueDate.isAfter(getLocalDate().plusMonths(2))) return BillEventNotificationResult.Skip

            val wallet = walletService.findWallet(walletId)
            val author = findAuthorOrNull(wallet)

            val (founder, nonFounderMembers) =
                wallet
                    .getMembersCanView(this)
                    .filterNot { source is ActionSource.Api && it == author }
                    .partition { member -> member.accountId == wallet.founder.accountId }

            val markers =
                log(
                    "founder" to founder.firstOrNull()?.accountId,
                    "members" to nonFounderMembers.joinToString { it.accountId.value },
                )

            logger.info(markers, "BillEventNotificationHandler#notifyBillAdded")

            notificationAdapter.notifyBillCreated(
                members = nonFounderMembers,
                payee = this.getPayee(),
                amount = amountTotal,
                dueDate = dueDate,
                billType = billType,
                billId = billId,
                billStatus = status,
                walletId = wallet.id,
                walletName = wallet.name,
                author = author,
                hint = null,
                description = description.ifEmpty { null },
                actionSource = source,
            )

            notificationAdapter.notifyBillCreated(
                members = founder,
                payee = this.getPayee(),
                amount = amountTotal,
                dueDate = dueDate,
                billType = billType,
                billId = billId,
                billStatus = status,
                walletId = wallet.id,
                walletName = wallet.name,
                author = author,
                hint = notificationHintService.getBillCreated(wallet.id, wallet.founder),
                description = description.ifEmpty { null },
                actionSource = source,
            )

            return@with BillEventNotificationResult.Success
        }
}