package ai.friday.billpayment.app.message

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.FichaCompensacaoAdded
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.notification.NotificationHintService
import ai.friday.billpayment.app.wallet.WalletService
import io.micronaut.context.annotation.Property

@FridayMePoupe
class DefaultFichaAddedNotificationService(
    @Property(name = "billNotifications.notifyBillAdded") isBillAddedNotificationEnabled: Boolean,
    walletService: WalletService,
    notificationAdapter: NotificationAdapter,
    notificationHintService: NotificationHintService,
) : BillAddedNotificationService<FichaCompensacaoAdded>(
    isBillAddedNotificationEnabled,
    walletService,
    notificationAdapter,
    notificationHintService,
)