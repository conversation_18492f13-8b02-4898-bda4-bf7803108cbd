package ai.friday.billpayment.app.message

import ai.friday.billpayment.app.notification.delay
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.ObjectMapper
import io.via1.communicationcentre.app.notification.Notification

data class QueueMessage(val queueName: String, val jsonObject: String, val delaySeconds: Int? = null) {
    constructor(queueName: String, parameters: Map<String, String>) : this(
        queueName,
        ObjectMapper().writeValueAsString(parameters),
    )

    constructor(queueName: String, parameters: Map<String, String>, delaySeconds: Int?) : this(
        queueName,
        ObjectMapper().writeValueAsString(parameters),
        delaySeconds,
    )

    constructor(queueName: String, notification: QueueNotificationTO) : this(
        queueName,
        ObjectMapper().writeValueAsString(notification),
        notification.notification.delay(),
    )
}

data class QueueMessageBatch(val queueName: String, val messages: List<String>, val delaySeconds: Int? = null)

@JsonIgnoreProperties(ignoreUnknown = true)
data class QueueNotificationTO(
    val notification: Notification,
)