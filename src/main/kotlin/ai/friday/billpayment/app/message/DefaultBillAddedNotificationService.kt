package ai.friday.billpayment.app.message

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.notification.NotificationHintService
import ai.friday.billpayment.app.wallet.WalletService
import io.micronaut.context.annotation.Property

@FridayMePoupe
class DefaultBillAddedNotificationService(
    @Property(name = "billNotifications.notifyBillAdded") isBillAddedNotificationEnabled: Boolean,
    walletService: WalletService,
    notificationAdapter: NotificationAdapter,
    notificationHintService: NotificationHintService,
) : BillAddedNotificationService<BillAdded>(
    isBillAddedNotificationEnabled,
    walletService,
    notificationAdapter,
    notificationHintService,
)