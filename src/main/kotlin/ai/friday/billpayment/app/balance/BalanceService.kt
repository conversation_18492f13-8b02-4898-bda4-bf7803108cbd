package ai.friday.billpayment.app.balance

import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.banking.OmnibusBankAccountConfiguration
import ai.friday.billpayment.app.integrations.BankAccountService
import ai.friday.billpayment.app.integrations.InternalBankRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.WalletStatus
import ai.friday.morning.log.andAppend
import io.micronaut.cache.annotation.CacheConfig
import io.micronaut.cache.annotation.CacheInvalidate
import io.micronaut.cache.annotation.Cacheable
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

interface BalanceService {
    fun getBalanceFrom(accountId: AccountId, accountPaymentMethodId: AccountPaymentMethodId, accountPaymentMethodStatus: AccountPaymentMethodStatus? = AccountPaymentMethodStatus.ACTIVE): Balance
    fun invalidate(accountPaymentMethodId: AccountPaymentMethodId)

    fun getAccountBalance(accountId: AccountId, walletStatus: WalletStatus? = WalletStatus.ACTIVE): Balance
}

@Singleton
@CacheConfig("balance")
@Requires(env = [FRIDAY_ENV, MODATTA_ENV, ME_POUPE_ENV])
open class DefaultBalanceService(
    private val accountService: AccountService,
    private val internalBankRepository: InternalBankRepository,
    private val bankAccountService: BankAccountService,
    private val omnibusBankAccountConfiguration: OmnibusBankAccountConfiguration,
    private val walletService: WalletService,
) : BalanceService {

    @Cacheable(parameters = ["accountPaymentMethodId"])
    override fun getBalanceFrom(accountId: AccountId, accountPaymentMethodId: AccountPaymentMethodId, accountPaymentMethodStatus: AccountPaymentMethodStatus?): Balance {
        val markers = Markers.append("accountPaymentMethodId", accountPaymentMethodId.value)
            .andAppend("accountId", accountId.value)
            .andAppend("accountPaymentMethodStatus", accountPaymentMethodStatus)

        val accountPaymentMethod =
            accountService.findAccountPaymentMethodsByAccountId(accountId, accountPaymentMethodStatus)
                .find { it.id == accountPaymentMethodId } ?: throw ItemNotFoundException(
                accountId,
                accountPaymentMethodId,
            )

        LOG.info(markers, "getBalanceFrom")

        with(accountPaymentMethod.method as InternalBankAccount) {
            return if (bankAccountMode == BankAccountMode.VIRTUAL) {
                internalBankRepository.findAllBankStatementItem(accountPaymentMethodId = accountPaymentMethod.id).balance
            } else {
                val balanceAmount = bankAccountService.getBalance(buildFullAccountNumber())
                Balance(balanceAmount)
            }
        }
    }

    override fun invalidate(accountPaymentMethodId: AccountPaymentMethodId) {
        if (omnibusBankAccountConfiguration.isOmnibusBankAccount(accountPaymentMethodId)) {
            accountService.findAllVirtualBankAccount().forEach {
                invalidateCache(it.id)
            }
        } else {
            invalidateCache(accountPaymentMethodId)
        }
    }

    override fun getAccountBalance(accountId: AccountId, walletStatus: WalletStatus?): Balance {
        val wallet = walletService.findPrimaryWallet(accountId, walletStatus)

        val accountPaymentMethodStatus = when (walletStatus) {
            WalletStatus.ACTIVE -> AccountPaymentMethodStatus.ACTIVE
            WalletStatus.CLOSED -> null
            null -> null
        }

        return getBalanceFrom(
            accountId = accountId,
            accountPaymentMethodId = wallet.paymentMethodId,
            accountPaymentMethodStatus = accountPaymentMethodStatus,
        )
    }

    @CacheInvalidate(parameters = ["accountPaymentMethodId"])
    open fun invalidateCache(accountPaymentMethodId: AccountPaymentMethodId) {
        LOG.info(Markers.append("accountPaymentMethodId", accountPaymentMethodId.value), "InvalidateBalanceCache")
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BalanceService::class.java)
    }
}

data class Balance(val amount: Long) {
    operator fun plus(other: Balance?): Balance {
        return Balance(amount + (other?.amount ?: 0L))
    }
}