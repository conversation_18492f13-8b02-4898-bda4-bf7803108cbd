package ai.friday.billpayment.app.vehicledebts

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.ConcessionariaService
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateFichaDeCompensacaoRequest
import ai.friday.billpayment.app.bill.ExternalBillId
import ai.friday.billpayment.app.bill.ExternalBillProvider
import ai.friday.billpayment.app.bill.FichaCompensacaoService
import ai.friday.billpayment.app.bill.ForcedConcessionariaRequest
import ai.friday.billpayment.app.bill.PaidConcessionariaRequest
import ai.friday.billpayment.app.bill.isFicha
import ai.friday.billpayment.app.integrations.CrmRepository
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.app.wallet.WalletId
import io.micronaut.context.annotation.DefaultScope
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import java.time.LocalDate

@DefaultScope(Singleton::class)
@Requires(property = "features.vehicleDebts", value = "true")
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
annotation class VehicleDebts

private const val vehicleDebtsQueueName = "vehicle_debts_enrichment"

@VehicleDebts
open class VehicleDebtsService(
    private val vehicleDebtsAdapter: VehicleDebtsAdapterInterface,
    private val fichaCompensacaoService: FichaCompensacaoService,
    private val concessionariaService: ConcessionariaService,
    private val accountService: AccountService,
    private val manualEntryService: ManualEntryService,
    private val messagePublisher: MessagePublisher,
    private val crmRepository: CrmRepository,
) {
    open fun getAllVehicles(accountId: AccountId): Result<List<Vehicle>> {
        val account = accountService.findAccountById(accountId)
        return vehicleDebtsAdapter.getAllVehicleByUser(accountId, Document(account.document)).onSuccess { vehicles ->
            return Result.success(vehicles.filter { it.status != VehicleStatus.INACTIVE })
        }
    }

    open fun enrollByUser(accountId: AccountId, document: Document): Result<Unit> {
        return vehicleDebtsAdapter.enrollByUser(accountId, document)
    }

    open fun enrollByVehicle(accountId: AccountId, document: Document, licensePlate: LicensePlate, sync: Boolean, description: String?, source: EnrollVehicleSource): Result<Unit> {
        return runCatching {
            return vehicleDebtsAdapter.enrollByVehicle(accountId, document, licensePlate, sync, description, source)
                .onSuccess {
                    crmRepository.publishEvent(
                        accountId = accountId,
                        eventName = "veiculo_cadastrado",
                        customParams = mapOf(
                            "licensePlate" to licensePlate.value,
                            "description" to description,
                            "source" to source.name,
                        ),
                    )
                }
        }
    }

    open fun enrollByVehicle(accountId: AccountId, licensePlate: LicensePlate, sync: Boolean, description: String? = null, source: EnrollVehicleSource): Result<Unit> {
        return runCatching {
            val account = accountService.findAccountById(accountId)

            if (account.closed) {
                return Result.failure(IllegalStateException("Account is closed: $accountId"))
            }

            return enrollByVehicle(accountId, Document(account.document), licensePlate, sync, description, source)
        }
    }

    open fun withdrawByVehicle(accountId: AccountId, licensePlate: LicensePlate): Result<Unit> {
        val account = accountService.findAccountById(accountId)

        return vehicleDebtsAdapter.withdrawByVehicle(accountId, Document(account.document), licensePlate)
    }

    open fun handle(request: VehicleDebtsBillRequest): Result<CreateVehicleDebtsResult> = kotlin.runCatching {
        val walletId = accountService.findAccountById(request.accountId).defaultWalletId()

        val result = when (request.paymentStatus) {
            VehicleDebtsBillRequest.Status.ACTIVE -> {
                sendToEnrichmentQueue(request, walletId)
                handleActiveDebt(request, walletId)
            }

            VehicleDebtsBillRequest.Status.PAID -> handlePaidDebt(request, walletId)

            VehicleDebtsBillRequest.Status.NOTICE -> handleNoticeDebt(request, walletId)
        }

        return result
    }

    private fun sendToEnrichmentQueue(request: VehicleDebtsBillRequest, walletId: WalletId) {
        val barcode = request.barcode?.number ?: return

        val enrichmentMessage = VehicleDebtEnrichmentMessage(
            accountId = request.accountId.value,
            walletId = walletId.value,
            licensePlate = request.licensePlate.value,
            barcode = barcode,
            externalId = request.externalId,
            description = request.description,
        )

        messagePublisher.sendMessage(
            queueName = vehicleDebtsQueueName,
            body = enrichmentMessage,
        )
    }

    private fun handleNoticeDebt(
        request: VehicleDebtsBillRequest,
        walletId: WalletId,
    ): Result<CreateVehicleDebtsResult> = if (request.dueDate != null) {
        createManualEntry(request, walletId)
    } else {
        Result.success(CreateVehicleDebtsResult.InsufficientData)
    }

    private fun handlePaidDebt(
        request: VehicleDebtsBillRequest,
        walletId: WalletId,
    ): Result<CreateVehicleDebtsResult> {
        if (request.barcode == null) {
            return if (request.dueDate == null) {
                Result.success(CreateVehicleDebtsResult.InsufficientData)
            } else {
                createManualEntry(request, walletId)
            }
        } else {
            if (request.barcode.isFicha()) {
                return when (val result = fichaCompensacaoService.createFichaDeCompensacao(request.toCreateFichaDeCompensacaoRequest(walletId), null, request.getRequestDueDate(), false)) { // FIXME se um DV está ligado a outro CPF, não vai atualizar o status
                    is CreateBillResult.SUCCESS -> {
                        verifyManualEntryDuplicates(request, walletId)

                        Result.success(CreateVehicleDebtsResult.Created(result.bill.billId.value))
                    }

                    is CreateBillResult.FAILURE.ServerError -> Result.failure(IllegalStateException("Ficha de compensação não foi criada"))
                    is CreateBillResult.FAILURE.BillUnableToValidate -> {
                        if (result.isRetryable) {
                            Result.failure(IllegalStateException("Ficha de compensação não foi criada"))
                        } else {
                            Result.success(CreateVehicleDebtsResult.UnableToValidate)
                        }
                    }

                    is CreateBillResult.FAILURE.AlreadyPaid.WithData -> Result.success(CreateVehicleDebtsResult.AlreadyPaid)
                    is CreateBillResult.FAILURE.AlreadyPaid.WithoutData -> Result.success(CreateVehicleDebtsResult.AlreadyPaid)
                    is CreateBillResult.FAILURE.BillAlreadyExists -> Result.success(CreateVehicleDebtsResult.AlreadyExists)
                    is CreateBillResult.FAILURE.BillNotPayable -> Result.success(CreateVehicleDebtsResult.NotPayable)
                }
            } else {
                verifyManualEntryDuplicates(request, walletId)

                val result = concessionariaService.handlePaid(request.toPaidConcessionariaRequest(walletId, request.accountId)).getOrElse {
                    return Result.success(CreateVehicleDebtsResult.InsufficientData)
                }

                return Result.success(CreateVehicleDebtsResult.Created(result.value))
            }
        }
    }

    private fun verifyManualEntryDuplicates(request: VehicleDebtsBillRequest, walletId: WalletId) {
        request.dueDate
            ?.let { dueDate ->
                manualEntryService.findAllByWalletAndDueDate(walletId, dueDate)
                    .find { it.externalId == ExternalBillId(request.externalId!!, ExternalBillProvider.VEHICLE_DEBTS) }
                    ?.let { manualEntryService.ignore(it.id, it.walletId) }
            }
    }

    private fun createManualEntry(request: VehicleDebtsBillRequest, walletId: WalletId): Result<CreateVehicleDebtsResult> {
//        val createdManualEntry = manualEntryService.findAllByWalletAndDueDate(walletId, request.dueDate!!)
//            .any { request.externalId != null && it.externalId == ExternalBillId(request.externalId, ExternalBillProvider.VEHICLE_DEBTS) }
//
//        if (!createdManualEntry) {
//            val manualEntry = manualEntryService.create(
//                walletId = walletId,
//                title = request.description,
//                amount = request.amountTotal,
//                dueDate = request.dueDate,
//                source = ActionSource.VehicleDebts(request.accountId),
//                type = ManualEntryType.VEHICLE_DEBT,
//                status = if (request.paymentStatus == VehicleDebtsBillRequest.Status.NOTICE) ManualEntryStatus.ACTIVE else ManualEntryStatus.PAID,
//                categoryId = null,
//                externalId = request.externalId?.let { ExternalBillId(it, ExternalBillProvider.VEHICLE_DEBTS) },
//            ).getOrElse { return Result.failure(IllegalStateException("Unable to create manual entry: $it")) }
//
//            return Result.success(CreateVehicleDebtsResult.CreatedManualEntry(manualEntry.id.value))
//        }

        return Result.success(CreateVehicleDebtsResult.AlreadyExists)
    }

    private fun handleActiveDebt(
        request: VehicleDebtsBillRequest,
        walletId: WalletId,
    ): Result<CreateVehicleDebtsResult> {
        return when {
            request.barcode == null -> {
                if (request.dueDate != null) {
                    createManualEntry(request, walletId)
                } else {
                    Result.failure(IllegalStateException("Duedate and barcode should not be null"))
                }
            }

            request.barcode.isFicha() -> {
                when (val result = fichaCompensacaoService.createFichaDeCompensacao(request.toCreateFichaDeCompensacaoRequest(walletId), null, request.getRequestDueDate(), false)) {
                    is CreateBillResult.SUCCESS -> Result.success(CreateVehicleDebtsResult.Created(result.bill.billId.value))
                    is CreateBillResult.FAILURE.ServerError -> Result.failure(IllegalStateException("Ficha de compensação não foi criada: $result"))
                    is CreateBillResult.FAILURE.BillUnableToValidate -> {
                        if (result.isRetryable) {
                            Result.failure(IllegalStateException("Ficha de compensação não foi criada"))
                        } else {
                            Result.success(CreateVehicleDebtsResult.UnableToValidate)
                        }
                    }

                    is CreateBillResult.FAILURE.AlreadyPaid.WithData -> Result.success(CreateVehicleDebtsResult.AlreadyPaid)
                    is CreateBillResult.FAILURE.AlreadyPaid.WithoutData -> Result.success(CreateVehicleDebtsResult.AlreadyPaid)
                    is CreateBillResult.FAILURE.BillAlreadyExists -> Result.success(CreateVehicleDebtsResult.AlreadyExists)
                    is CreateBillResult.FAILURE.BillNotPayable -> Result.success(CreateVehicleDebtsResult.NotPayable)
                }
            }

            else -> {
                when (val result = concessionariaService.createConcessionaria(request.toCreateConcessionariaRequest(walletId, request.accountId), null, false)) {
                    is CreateBillResult.SUCCESS -> Result.success(CreateVehicleDebtsResult.Created(result.bill.billId.value))
                    is CreateBillResult.FAILURE.ServerError -> Result.failure(IllegalStateException("Concessionaria não foi criada"))
                    is CreateBillResult.FAILURE.BillUnableToValidate -> {
                        if (result.isRetryable) {
                            Result.failure(IllegalStateException("Concessionaria não foi criada: $result"))
                        } else {
                            Result.success(CreateVehicleDebtsResult.UnableToValidate)
                        }
                    }

                    is CreateBillResult.FAILURE.AlreadyPaid.WithData -> Result.success(CreateVehicleDebtsResult.AlreadyPaid)
                    is CreateBillResult.FAILURE.AlreadyPaid.WithoutData -> Result.success(CreateVehicleDebtsResult.AlreadyPaid)
                    is CreateBillResult.FAILURE.BillAlreadyExists -> Result.success(CreateVehicleDebtsResult.AlreadyExists)
                    is CreateBillResult.FAILURE.BillNotPayable -> Result.success(CreateVehicleDebtsResult.NotPayable)
                }
            }
        }.also { verifyManualEntryDuplicates(request, walletId) }
    }
}

private fun VehicleDebtsBillRequest.getRequestDueDate(): LocalDate? {
    return this.dueDate ?: this.originalDueDate
}

sealed class CreateVehicleDebtsResult {
    data class Created(val id: String) : CreateVehicleDebtsResult()
    data class CreatedManualEntry(val id: String) : CreateVehicleDebtsResult()
    data object AlreadyExists : CreateVehicleDebtsResult()
    data object AlreadyPaid : CreateVehicleDebtsResult()
    data object InsufficientData : CreateVehicleDebtsResult()
    data object UnableToValidate : CreateVehicleDebtsResult()
    data object NotPayable : CreateVehicleDebtsResult()
}

data class VehicleDebtsBillRequest(
    val accountId: AccountId,
    val barcode: BarCode?,
    val dueDate: LocalDate? = null,
    val description: String,
    val document: Document,
    val licensePlate: LicensePlate,
    val amountTotal: Long,
    val paymentStatus: Status,
    val fees: Long,
    val fine: Long,
    val externalId: String?, // ID DETRAN
    val type: VehicleDebtsType,
    val originalDueDate: LocalDate? = null,
    val unavailableReason: String?,
    val quota: Long?,
    val identifier: String?,
) {
    enum class Status {
        ACTIVE, PAID, NOTICE
    }
}

enum class VehicleDebtsType {
    Licensing, // Licenciamento
    OwnershipTax, // IPVA
    Fine, // Multa
    Multiple, // Combinação de vários tipos
    Other, // Outros
}

data class Vehicle(
    val licensePlate: LicensePlate,
    val description: String?,
    val status: VehicleStatus,
    val accountId: AccountId,
)

enum class VehicleStatus {
    ACTIVE, INACTIVE, PENDING, ERROR
}

private fun VehicleDebtsBillRequest.toCreateFichaDeCompensacaoRequest(walletId: WalletId) = CreateFichaDeCompensacaoRequest(
    description = description,
    barcode = barcode ?: throw IllegalStateException("Código de barras é obrigatório"),
    walletId = walletId,
    source = ActionSource.VehicleDebts(accountId = AccountId(walletId.value), licensePlate = licensePlate),
    payerAlias = null,
    member = null, // TODO
    securityValidationErrors = emptyList(),
    externalBillId = this.externalId?.let { ExternalBillId(it, ExternalBillProvider.VEHICLE_DEBTS) },
)

private fun VehicleDebtsBillRequest.toPaidConcessionariaRequest(walletId: WalletId, accountId: AccountId) = PaidConcessionariaRequest(
    description = description,
    dueDate = dueDate,
    barcode = barcode ?: throw IllegalStateException("Código de barras é obrigatório"),
    walletId = walletId,
    externalBillId = this.externalId?.let { ExternalBillId(it, ExternalBillProvider.VEHICLE_DEBTS) },
    accountId = accountId,
    amount = amountTotal,
    source = ActionSource.VehicleDebts(accountId = accountId, licensePlate = licensePlate),
)

private fun VehicleDebtsBillRequest.toCreateConcessionariaRequest(walletId: WalletId, accountId: AccountId) = ForcedConcessionariaRequest(
    description = description,
    dueDate = dueDate ?: throw IllegalStateException("Data de vencimento é obrigatória"),
    barcode = barcode ?: throw IllegalStateException("Código de barras é obrigatório"),
    walletId = walletId,
    externalBillId = this.externalId?.let { ExternalBillId(it, ExternalBillProvider.VEHICLE_DEBTS) },
    accountId = accountId,
    amount = amountTotal,
    source = ActionSource.VehicleDebts(accountId = accountId, licensePlate = licensePlate),
)

enum class EnrollVehicleSource {
    BACKOFFICE, FLOW, APP
}