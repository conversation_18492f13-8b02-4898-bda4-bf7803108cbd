package ai.friday.billpayment.app.vehicledebts

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.ExternalBillId
import ai.friday.billpayment.app.bill.RegisterUpdated
import ai.friday.billpayment.app.bill.UpdatedRegisterData
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.wallet.WalletId
import arrow.core.getOrElse
import jakarta.inject.Singleton

@Singleton
@VehicleDebts
open class VehicleDebtEnrichmentService(
    private val billEventRepository: BillEventRepository,
    private val billEventPublisher: BillEventPublisher,
) {
    open fun enrichVehicleDebt(
        accountId: AccountId,
        walletId: WalletId,
        barcode: BarCode,
        licensePlate: LicensePlate,
        externalId: ExternalBillId?,
        details: String?,
    ): Result<Unit> = kotlin.runCatching {
        val billResult = billEventRepository.findLastBill(barcode, walletId)
        val bill = billResult.getOrElse { return@runCatching }

        val enrichmentData = UpdatedRegisterData.VehicleDebtEnrichment(
            externalId = externalId,
            details = details,
        )

        val registerUpdatedEvent = RegisterUpdated(
            billId = bill.billId,
            walletId = walletId,
            actionSource = ActionSource.VehicleDebts(accountId, licensePlate),
            updatedRegisterData = enrichmentData,
        )

        billEventPublisher.publish(bill, registerUpdatedEvent)
    }
}