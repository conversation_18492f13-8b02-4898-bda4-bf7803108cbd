package ai.friday.billpayment.app.vehicledebts

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import com.fasterxml.jackson.annotation.JsonIgnoreProperties

interface VehicleDebtsAdapterInterface {
    fun getAllVehicleByUser(accountId: AccountId, document: Document): Result<List<Vehicle>>
    fun enrollByUser(accountId: AccountId, document: Document): Result<Unit>
    fun enrollByVehicle(accountId: AccountId, document: Document, licensePlate: LicensePlate, sync: Boolean, description: String?, source: EnrollVehicleSource): Result<Unit>
    fun withdrawByVehicle(accountId: AccountId, document: Document, licensePlate: LicensePlate): Result<Unit>
}

const val LICENSE_PLATE_SIZE = 7

data class LicensePlate(val value: String) {
    init {
        require(value.length == LICENSE_PLATE_SIZE)
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class VehicleDebtEnrichmentMessage(
    val accountId: String,
    val walletId: String,
    val licensePlate: String,
    val barcode: String,
    val externalId: String? = null,
    val description: String? = null,
)