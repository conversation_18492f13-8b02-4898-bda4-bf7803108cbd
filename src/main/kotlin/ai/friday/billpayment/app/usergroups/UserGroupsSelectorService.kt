package ai.friday.billpayment.app.usergroups

import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Parameter
import jakarta.inject.Singleton
import java.time.LocalDate

@EachProperty("account-group-selector")
data class AccountGroupSelectorProperties
@ConfigurationInject constructor(
    @param:Parameter val name: String,
    private val enabled: Boolean,
    private val beginDate: LocalDate?,
    private val endDate: LocalDate?,
    private val groups: List<String>?,
) {
    val accountGroups = groups?.map { AccountGroup.valueOf(it) } ?: emptyList()

    fun isActive(): Boolean {
        val today = getLocalDate()
        val hasBegin = beginDate == null || today == beginDate || today.isAfter(beginDate)
        val hasEnded = endDate != null && today.isAfter(endDate)
        return enabled && hasBegin && !hasEnded
    }
}

enum class AccountGroupSelectorType {
    REGISTER,
}

@Singleton
class UserGroupsSelectorService(
    private val selectorProperties: List<AccountGroupSelectorProperties>,
) {

    fun selectGroups(selectorType: AccountGroupSelectorType): List<AccountGroup> {
        val currentGroup = selectorProperties.firstOrNull { it.name.equals(selectorType.name, ignoreCase = true) } ?: return emptyList()

        if (!currentGroup.isActive()) {
            return emptyList()
        }

        return currentGroup.accountGroups
    }
}