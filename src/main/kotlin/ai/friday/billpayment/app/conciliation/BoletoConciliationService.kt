package ai.friday.billpayment.app.conciliation

import ai.friday.billpayment.and
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.feature.RequiresCelcoin
import ai.friday.billpayment.app.integrations.BoletoOccurrenceAdapter
import ai.friday.billpayment.app.integrations.BoletoOccurrenceProcessor
import ai.friday.billpayment.app.integrations.BoletoOccurrenceRepository
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.OccurrenceProcessorResult
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.integrations.UndoCaptureFundsCommand
import ai.friday.billpayment.app.payment.settlementVoid
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.log
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import java.util.concurrent.atomic.AtomicInteger
import net.logstash.logback.marker.Markers
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@Singleton
@RequiresCelcoin
class BoletoConciliationService(
    private val occurrenceAdapter: BoletoOccurrenceAdapter,
    private val occurrenceRepository: BoletoOccurrenceRepository,
    private val configuration: OccurrencesConfiguration,
    private val processor: BoletoOccurrenceProcessor,
) {
    fun checkOccurrences() {
        val range = getLocalDate().let { now ->
            now.minusDays(configuration.pastRangeInDays)..now
        }
        val markers = log("startDate" to range.start, "endDate" to range.endInclusive)

        val occurrences = try {
            occurrenceAdapter.getOccurrences(startDate = range.start, endDate = range.endInclusive)
        } catch (e: Exception) {
            LOG.error(markers, "BoletoConciliationService#checkOccurrences", e).run { return }
        }

        val newOccurrences = AtomicInteger(0)
        markers.and("totalOfOccurrences" to occurrences.size)

        occurrences.forEach { occurrence ->
            try {
                if (!occurrenceRepository.exists(occurrence)) {
                    logNewOccurrence(occurrence).run { newOccurrences.incrementAndGet() }

                    if (configuration.automation.enabled) {
                        processor.process(occurrence)
                    }

                    occurrenceRepository.save(occurrence)
                }
            } catch (e: Exception) {
                LOG.error(markers, "BoletoConciliationService#checkOccurrences", e)
            }
        }

        LOG.info(markers.and("newOccurrences" to newOccurrences), "BoletoConciliationService#checkOccurrences")
    }

    companion object {
        val LOG: Logger = LoggerFactory.getLogger(BoletoConciliationService::class.java)

        fun logNewOccurrence(boletoConciliationOccurrence: BoletoOccurrence) {
            LOG.info(
                Markers.append("occurrence", boletoConciliationOccurrence),
                "BoletoConciliationService#logNewOccurrence",
            )
        }
    }
}

@ConfigurationProperties("features.occurrences")
@Requires(beans = [BoletoConciliationService::class])
data class OccurrencesConfiguration @ConfigurationInject constructor(
    val pastRangeInDays: Long,
    val automation: Automation,
) {
    @ConfigurationProperties("automation")
    data class Automation @ConfigurationInject constructor(
        val enabled: Boolean,
        val queue: String,
    )
}

data class BoletoOccurrence(
    val walletId: WalletId,
    val nsu: String,
    val date: ZonedDateTime,
    val createDate: ZonedDateTime,
    val reason: String,
    val externalTransactionId: String,
    val barCode: BarCode,
    val amount: Long,
    val financialServiceGateway: FinancialServiceGateway,
) {
    companion object {
        const val DEFAULT_DESCRIPTION = "Boleto Devolvido"
    }
}

@Singleton
@Requires(beans = [BoletoConciliationService::class])
class DefaultOccurrenceProcessor(
    private val configuration: OccurrencesConfiguration,
    private val messagePublisher: MessagePublisher,
    private val transactionRepository: TransactionRepository,
) : BoletoOccurrenceProcessor {
    override fun process(occurrence: BoletoOccurrence): OccurrenceProcessorResult = runCatching {
        val transactionId =
            transactionRepository.findTransactionIdByWalletAndNSU(occurrence.walletId, occurrence.nsu.toLong())
                ?: return OccurrenceProcessorResult.TransactionNotfound

        val transaction = transactionRepository.findById(transactionId)

        messagePublisher.sendMessage(
            configuration.automation.queue,
            UndoCaptureFundsCommand(
                transactionId = transactionId,
                source = UndoCaptureFundsCommand.Source(
                    provider = FinancialServiceGateway.CELCOIN,
                    reason = UndoCaptureFundsCommand.Reason.SETTLEMENT_REFUND,
                    details = occurrence.reason,
                ),
            ),
        )

        transaction.settlementVoid(description = BoletoOccurrence.DEFAULT_DESCRIPTION)
            .let { voidedTransation -> transactionRepository.save(voidedTransation) }

        return OccurrenceProcessorResult.Success
    }.getOrElse { OccurrenceProcessorResult.UnknownError(it) }
}