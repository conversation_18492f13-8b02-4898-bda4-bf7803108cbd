package ai.friday.billpayment.app

import ai.friday.billpayment.app.integrations.TemplateForm

data class CompiledHtml(val value: String)

data class DeclarationOfResidencyForm(
    val fullName: String,
    val document: String,
    val fullAddress: String,
    val city: String,
    val federalUnity: String,
    val zipCode: String,
    val signature: DeclarationOfResidencySignature,
) : TemplateForm

data class DeclarationOfResidencySignature(
    val date: String,
    val shortDate: String,
    val email: String,
    val phone: String,
    val clientIP: String,
    val key: String,
)