package ai.friday.billpayment.app.modatta.register

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.AgreementFilesService
import ai.friday.billpayment.app.account.CreditCardConfiguration
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.account.LegacyAccountConfiguration
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.account.UserFilesConfiguration
import ai.friday.billpayment.app.account.generateSignatureKey
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.liveness.LivenessErrors
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.liveness.LivenessSelfieError
import ai.friday.billpayment.app.liveness.LivenessService
import ai.friday.billpayment.app.modatta.ModattaExternalId
import ai.friday.billpayment.app.modatta.UserId
import ai.friday.billpayment.app.modatta.integrations.ModattaBackendAdapter
import ai.friday.billpayment.app.modatta.integrations.ModattaSimpleSignUpRepository
import ai.friday.billpayment.app.modatta.integrations.PartnerSimpleSignUpService
import ai.friday.billpayment.app.register.RiskAnalysisFailedReason
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Requires
import io.micronaut.http.MediaType
import io.via1.communicationcentre.app.ContractForm
import io.via1.communicationcentre.app.integrations.Tenant
import jakarta.inject.Singleton
import java.io.ByteArrayOutputStream
import java.time.LocalDate
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Requires(env = [MODATTA_ENV])
@Singleton
open class ModattaSimpleSignUpService(
    private val livenessService: LivenessService,
    private val partnerSimpleSignUpService: PartnerSimpleSignUpService,
    private val simpleSignUpRepository: ModattaSimpleSignUpRepository,
    private val accountRepository: AccountRepository,
    private val agreementFilesService: AgreementFilesService,
    private val userFilesConfiguration: UserFilesConfiguration,
    private val objectRepository: ObjectRepository,
    private val walletRepository: WalletRepository,
    private val accountService: AccountService,
    private val modattaCorpAdapter: ModattaBackendAdapter,
) {
    fun find(userId: UserId): Either<Exception, ModattaSimpleSignUp> {
        val simpleSignUp = simpleSignUpRepository.find(userId).getOrElse { return it.left() }

        if (simpleSignUp.enrollment != null && !simpleSignUp.enrollment.done) {
            livenessService.retrieveEnrollmentSelfie(simpleSignUp.enrollment.livenessId).map {
                return setEnrollmentDone(simpleSignUp)
            }.getOrElse {
                when (it) {
                    is LivenessSelfieError.Error -> return it.e.left()
                    LivenessSelfieError.Unavailable -> {
                        LOG.warn(append("whenUnknownBranch", it), "WHENUNKNOWNBRANCH#ModattaSimpleSignUpService#find")
                    }
                }
            }
        }
        return simpleSignUp.right()
    }

    fun updateUserData(userId: UserId, userData: UserData): Either<Exception, ModattaSimpleSignUp> {
        val markers = Markers.append("accountId", userId.value).andAppend("userData", userData)

        try {
            val register = simpleSignUpRepository.find(userId).getOrElse {
                ModattaSimpleSignUp(
                    userId = userId,
                    userData = null,
                    enrollment = null,
                    userContract = null,
                    validationStatus = ModattaSimpleSignUpValidationStatus.STARTED,
                )
            }.initEnrollment().getOrElse {
                LOG.warn(markers, "ModattaSimpleSignUpService#updateUserData", it)
                return when (it) {
                    LivenessErrors.DuplicationCheckUnavailable, LivenessErrors.MatchUnavailable, LivenessErrors.AccountNotFound -> IllegalStateException(
                        "should not happen",
                    )

                    LivenessErrors.EnrollmentUnavailable -> TODO() // Essa falha é quando o usuário já completou um liveness check e não passou. Ele NAO pode tentar novamente. falha definitiva
                    is LivenessErrors.Error -> it.e // Algo deu errado no gateway. Pode ser uma indisponibilidade. falha provavelmente transiente.
                }.left()
            }.copy(
                userData = userData,
            )

            markers.andAppend("register", register)

            upsertAccount(userData, userId)

            simpleSignUpRepository.save(register)

            LOG.info(markers, "ModattaSimpleSignUpService#updateUserData")
            return find(userId)
        } catch (ex: Exception) {
            LOG.error(markers, "ModattaSimpleSignUpService#updateUserData", ex)
            return ex.left()
        }
    }

    private fun upsertAccount(userData: UserData, userId: UserId) {
        val account = Account(
            accountId = userId.toAccountId(),
            name = userData.name,
            emailAddress = userData.email,
            document = userData.document.value,
            documentType = "CPF",
            mobilePhone = userData.mobilePhone.msisdn,
            created = getZonedDateTime(),
            updated = getZonedDateTime(),
            status = AccountStatus.REGISTER_INCOMPLETE,
            configuration = LegacyAccountConfiguration(
                creditCardConfiguration = CreditCardConfiguration(),
                defaultWalletId = WalletId(userId.toAccountId().value),
                receiveDDANotification = false,
                receiveNotification = false,
                groups = listOf(),
            ),
            activated = null,
            type = UserAccountType.BASIC_ACCOUNT,
            imageUrlSmall = null,
            imageUrlLarge = null,
            subscriptionType = SubscriptionType.PIX,
        )

        accountRepository.save(account)
    }

    fun getAccountStatus(userId: UserId): Either<Exception, AccountStatus> {
        val markers = Markers.append("accountId", userId.value)

        return try {
            val account = accountService.findAccountById(
                userId.toAccountId(),
            )
            return partnerSimpleSignUpService.getAccountStatus(account.configuration.externalId!!).right()
        } catch (ex: Exception) {
            LOG.error(markers, "ModattaSimpleSignUpService#getAccountStatus", ex)
            ex.left()
        }
    }

    fun getSimpleSignUpStatus(userId: UserId): Either<Exception, SimpleSignUpStatus> {
        return find(userId).map {
            return try {
                getSimpleSignUpStatus(it, userId).right()
            } catch (ex: Exception) {
                ex.left()
            }
        }.mapLeft {
            if (it is AccountNotFoundException) {
                return SimpleSignUpStatus.MISSING_USER_DATA.right()
            }
            return it.left()
        }
    }

    private fun getSimpleSignUpStatus(simpleSignUp: ModattaSimpleSignUp, userId: UserId): SimpleSignUpStatus {
        with(simpleSignUp) {
            if (enrollment?.done != true) return SimpleSignUpStatus.MISSING_ENROLLMENT
            if (userContract?.hasAccepted != true) return SimpleSignUpStatus.MISSING_AGREEMENT
            if (validationStatus.isValid()) {
                val accountStatus = getAccountStatus(userId)
                    .getOrElse { AccountStatus.UNDER_REVIEW }

                return when (accountStatus) {
                    AccountStatus.ACTIVE -> SimpleSignUpStatus.ACTIVE
                    AccountStatus.CLOSED -> SimpleSignUpStatus.CLOSED
                    AccountStatus.DENIED -> SimpleSignUpStatus.DENIED

                    AccountStatus.UNDER_REVIEW, AccountStatus.UNDER_EXTERNAL_REVIEW, AccountStatus.APPROVED -> SimpleSignUpStatus.UNDER_REVIEW

                    // FIXME: precisamos informar a razão do cadastro ter sido reaberto. Hoje o cadastro só pode ser reaberto caso o nome não bata na RF, então não precisa enviar o motivo por enquanto
                    AccountStatus.REGISTER_INCOMPLETE -> SimpleSignUpStatus.REOPENED
                    AccountStatus.BLOCKED -> throw IllegalStateException("Account should not be BLOCKED")
                    AccountStatus.PENDING_CLOSE -> throw IllegalStateException("Account should not be PENDING_CLOSE")
                }
            }
            return SimpleSignUpStatus.INVALID_PHONE_AND_DOCUMENT
        }
    }

    private fun setEnrollmentDone(simpleSignUp: ModattaSimpleSignUp): Either<Exception, ModattaSimpleSignUp> {
        val markers = Markers.append("accountId", simpleSignUp.userId.value)
        try {
            val validation = partnerSimpleSignUpService.validate(simpleSignUp.userData!!)
            markers.andAppend("validation", validation)
            val validationStatus = when {
                validation.valid && validation.accountId == null -> ModattaSimpleSignUpValidationStatus.VALID_WITHOUT_PARTNER_ACCOUNT
                validation.valid && validation.accountId != null -> ModattaSimpleSignUpValidationStatus.VALID_WITH_PARTNER_ACCOUNT
                else -> ModattaSimpleSignUpValidationStatus.INVALID
            }

            val updatedRegister = simpleSignUp.copy(
                enrollment = simpleSignUp.enrollment!!.copy(done = true),
                validationStatus = validationStatus,
            )
            simpleSignUpRepository.save(updatedRegister)
            markers.andAppend("updatedRegister", updatedRegister)

            LOG.info(markers, "ModattaSimpleSignUpService#setEnrollmentDone")
            return updatedRegister.right()
        } catch (ex: Exception) {
            LOG.error(markers, "ModattaSimpleSignUpService#setEnrollmentDone", ex)
            return ex.left()
        }
    }

    private fun UserData.toContractForm() = ContractForm(
        name, // fullName
        document.value, // cpf
        "", // streetName
        "", // neighborhood
        "", // city
        "", // state
        "", // zipCode
        birthDate.format(dateFormat), // birthDate
        document.issuerRegion, // birthPlace
        document.value, // documentNumber
        document.type.value, // documentType
        "", // issuedDate
        document.issuer, // issuedBy
        "", // fathersName
        "", // mothersName
        email.value, // email
        mobilePhone.msisdn, // phone
        false, // politicallyExposed
        "", // politicallyExposedInfo
        "Até R$ 2.000,00", // monthlyIncome // FIXME - se realmente for aparecer no contrato, nao deveria ser informado pelo usuario ?
        Tenant.FRIDAY, // tenant
    )

    private fun createContractPdf(
        userId: UserId,
        contractForm: ContractForm,
        clientIP: String,
    ): StoredObject {
        val fileCreated = ByteArrayOutputStream()
        agreementFilesService.createContract(
            stream = fileCreated,
            contractForm = contractForm,
            clientIP = clientIP,
        ) // FIXME - mudar quando definirem o layout do contrato para o cadastro simplificado
        with(userFilesConfiguration) {
            return createStoredObject(
                fileContent = fileCreated.toByteArray(),
                key = "$path/${userId.value}/${contractPrefix}${getZonedDateTime().toEpochSecond()}.pdf",
                mediaType = io.micronaut.http.MediaType.APPLICATION_PDF_TYPE,
            )
        }
    }

    private fun createStoredObject(
        fileContent: ByteArray,
        key: String,
        mediaType: MediaType,
    ): StoredObject {
        with(userFilesConfiguration) {
            val storedObject = StoredObject(region, modattaUserDocumentsBucket, key)
            objectRepository.putObject(storedObject, fileContent, mediaType)

            return storedObject
        }
    }

    fun acceptAgreement(userId: UserId, clientIP: String): Either<Exception, ModattaSimpleSignUp> {
        val markers = Markers.append("accountId", userId.value)

        try {
            val simpleSignUp = simpleSignUpRepository.find(userId).getOrElse {
                return it.left()
            }
            markers.andAppend("simpleSignUp", simpleSignUp)

            val contractForm = simpleSignUp.userData!!.toContractForm()
            markers.andAppend("contractForm", contractForm)

            val userContractFile = createContractPdf(
                userId = userId,
                contractForm = contractForm,
                clientIP = clientIP,
            )

            simpleSignUpRepository.save(
                simpleSignUp = simpleSignUp.copy(
                    userContract = UserContract(
                        hasAccepted = true,
                        contract = userContractFile,
                    ),
                ),
            )

            val signUpResponse = partnerSimpleSignUpService.signUp(
                PartnerSimpleSignUpRequest(
                    userData = simpleSignUp.userData,
                    livenessId = simpleSignUp.enrollment!!.livenessId,
                    externalId = ExternalId(
                        value = simpleSignUp.userId.value,
                        providerName = AccountProviderName.MODATTA,
                    ),
                    userContractKey = userContractFile.key,
                    userContractSignature = contractForm.generateSignatureKey(),
                ),
            )

            markers.andAppend("signUpResponse", signUpResponse)

            val account = accountRepository.findById(userId.toAccountId())

            accountRepository.save(
                account.copy(
                    status = AccountStatus.UNDER_EXTERNAL_REVIEW,
                    configuration = account.configuration.copy(
                        externalId = ExternalId(
                            value = signUpResponse.accountId.value,
                            providerName = AccountProviderName.FRIDAY,
                        ),
                    ),
                ),
            )

            LOG.info(markers, "ModattaSimpleSignUpService#acceptAgreement")

            return find(userId)
        } catch (ex: Exception) {
            LOG.error(markers, "ModattaSimpleSignUpService#acceptAgreement", ex)
            return ex.left()
        }
    }

    open fun updateStatus(request: ModattaSimpleSignUpUpdateStatusRequest): Either<SimpleSignUpCallbackError, Account> {
        val markers = Markers.append("request", request)

        if (request.externalId.providerName != AccountProviderName.MODATTA) {
            LOG.warn(markers, "ModattaSimpleSignUpService#updateStatus")
            return SimpleSignUpCallbackError.InvalidProviderName(request.externalId.providerName).left()
        }

        val userId = ModattaExternalId(request.externalId.value).toUserId()
        val account = accountRepository.findByIdOrNull(userId.toAccountId())
        markers.andAppend("account", account)

        if (account == null) {
            LOG.warn(markers, "ModattaSimpleSignUpService#updateStatus")
            return SimpleSignUpCallbackError.AccountNotFound(userId.toAccountId()).left()
        }

        val simpleSignUp = simpleSignUpRepository.find(userId).getOrElse {
            LOG.error(markers, "ModattaSimpleSignUpService#updateStatus")
            return SimpleSignUpCallbackError.AccountNotFound(userId.toAccountId()).left()
        }

        val conflictReason = when {
            request.fridayAccountId.value != account.configuration.externalId?.value -> ConflictReason.INVALID_EXTERNAL_ID
            request.approvedBy != null && !request.approvedBy.isCompatibleWith(simpleSignUp.validationStatus) -> ConflictReason.INVALID_APPROVER
            !request.status.canTransitionFrom(account.status) -> ConflictReason.INVALID_STATUS
            else -> null
        }
        markers.andAppend("conflictReason", conflictReason)

        if (conflictReason != null) {
            LOG.warn(markers, "ModattaSimpleSignUpService#updateStatus")
            return SimpleSignUpCallbackError.Conflict(conflictReason).left()
        }

        if (request.status == account.status) {
            LOG.info(markers, "ModattaSimpleSignUpService#updateStatus")
            return account.right()
        }

        return if (request.status == AccountStatus.DENIED) {
            account.copy(status = AccountStatus.DENIED)
        } else {
            val wallet = walletRepository.findWallet(account.defaultWalletId())

            walletRepository.upsertMember(
                wallet.id,
                wallet.founder.copy(
                    name = simpleSignUp.userData!!.name,
                    document = simpleSignUp.userData.document.value,
                    emailAddress = simpleSignUp.userData.email,
                ),
            )

            account.copy(
                status = AccountStatus.ACTIVE,
                name = simpleSignUp.userData.name,
                document = simpleSignUp.userData.document.value,
                emailAddress = simpleSignUp.userData.email,
                mobilePhone = simpleSignUp.userData.mobilePhone.msisdn,
            )
        }.right().also {
            it.map { accountUpdated ->
                markers.andAppend("accountUpdated", accountUpdated)

                val userId = UserId(accountUpdated.accountId.value)

                LOG.info(markers, "ModattaSimpleSignUpService#updateStatus")
                accountRepository.save(accountUpdated)

                modattaCorpAdapter.notifyAccountRegisterUpdates(
                    userId.toModattaExternalId(),
                    accountUpdated.status,
                    request.riskAnalysisFailedReasons,
                )
            }
        }
    }

    private fun AccountStatus.canTransitionFrom(accountStatus: AccountStatus) = when (this) {
        AccountStatus.ACTIVE -> accountStatus == AccountStatus.UNDER_EXTERNAL_REVIEW || accountStatus == AccountStatus.ACTIVE
        AccountStatus.DENIED -> accountStatus == AccountStatus.UNDER_EXTERNAL_REVIEW || accountStatus == AccountStatus.DENIED
        else -> false
    }

    private fun SimpleSignUpApprover.isCompatibleWith(modattaSimpleSignUpValidationStatus: ModattaSimpleSignUpValidationStatus) =
        when (this) {
            SimpleSignUpApprover.USER -> modattaSimpleSignUpValidationStatus == ModattaSimpleSignUpValidationStatus.VALID_WITH_PARTNER_ACCOUNT
            SimpleSignUpApprover.EXTERNAL_ACCOUNT_REGISTER -> modattaSimpleSignUpValidationStatus == ModattaSimpleSignUpValidationStatus.VALID_WITHOUT_PARTNER_ACCOUNT
        }

    private fun ModattaSimpleSignUp.initEnrollment(): Either<LivenessErrors, ModattaSimpleSignUp> {
        if (enrollment != null) return this.right()

        val livenessId = livenessService.enroll(userId.toAccountId()).getOrElse {
            return it.left()
        }

        return copy(
            enrollment = Enrollment(
                livenessId = livenessId,
                done = false,
            ),
        ).right()
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ModattaSimpleSignUpService::class.java)
    }
}

enum class ModattaSimpleSignUpValidationStatus {
    STARTED, VALID_WITH_PARTNER_ACCOUNT, VALID_WITHOUT_PARTNER_ACCOUNT, INVALID;

    fun isValid() = (this == VALID_WITH_PARTNER_ACCOUNT) || (this == VALID_WITHOUT_PARTNER_ACCOUNT)
}

data class ModattaSimpleSignUp(
    val userId: UserId,
    val userData: UserData?,
    val enrollment: Enrollment?,
    val userContract: UserContract?,
    val validationStatus: ModattaSimpleSignUpValidationStatus,
)

data class Enrollment(
    val livenessId: LivenessId,
    val done: Boolean,
)

data class UserData(
    val name: String,
    val birthDate: LocalDate,
    val document: Document,
    val mobilePhone: MobilePhone,
    val email: EmailAddress,
)

data class UserContract(
    val hasAccepted: Boolean,
    val contract: StoredObject,
)

data class PartnerSimpleSignUpValidation(
    val valid: Boolean,
    val accountId: AccountId?,
)

data class PartnerSimpleSignUpRequest(
    val userData: UserData,
    val livenessId: LivenessId,
    val externalId: ExternalId,
    val userContractKey: String,
    val userContractSignature: String,
)

data class PartnerSimpleSignUpResult(
    val accountId: AccountId,
)

class PartnerSimpleSignUpException(override val cause: Throwable?) : Exception(cause)

data class ModattaSimpleSignUpUpdateStatusRequest(
    val fridayAccountId: AccountId,
    val externalId: ExternalId,
    val status: AccountStatus,
    val riskAnalysisFailedReasons: List<RiskAnalysisFailedReason>? = null,
    val approvedBy: SimpleSignUpApprover?,
)

enum class SimpleSignUpApprover {
    USER, EXTERNAL_ACCOUNT_REGISTER
}

sealed class SimpleSignUpCallbackError : PrintableSealedClassV2() {
    class Conflict(val reason: ConflictReason) : SimpleSignUpCallbackError()
    class InvalidProviderName(val providerName: AccountProviderName) : SimpleSignUpCallbackError()
    class AccountNotFound(val accountId: AccountId) : SimpleSignUpCallbackError()
}

enum class ConflictReason {
    INVALID_STATUS, INVALID_EXTERNAL_ID, INVALID_APPROVER
}

enum class SimpleSignUpStatus {
    MISSING_USER_DATA, MISSING_ENROLLMENT, MISSING_AGREEMENT, INVALID_PHONE_AND_DOCUMENT, UNDER_REVIEW, CLOSED, DENIED, ACTIVE, REOPENED
}