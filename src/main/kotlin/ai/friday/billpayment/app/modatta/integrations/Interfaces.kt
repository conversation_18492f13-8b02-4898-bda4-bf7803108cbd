package ai.friday.billpayment.app.modatta.integrations

import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.balance.Balance
import ai.friday.billpayment.app.banking.FridayBankAccountError
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.modatta.CreateModattaPixRequest
import ai.friday.billpayment.app.modatta.ModattaBalance
import ai.friday.billpayment.app.modatta.ModattaCashInNotification
import ai.friday.billpayment.app.modatta.ModattaCorpError
import ai.friday.billpayment.app.modatta.ModattaExternalId
import ai.friday.billpayment.app.modatta.UserId
import ai.friday.billpayment.app.modatta.register.ModattaSimpleSignUp
import ai.friday.billpayment.app.modatta.register.PartnerSimpleSignUpRequest
import ai.friday.billpayment.app.modatta.register.PartnerSimpleSignUpResult
import ai.friday.billpayment.app.modatta.register.PartnerSimpleSignUpValidation
import ai.friday.billpayment.app.modatta.register.UserData
import ai.friday.billpayment.app.register.RiskAnalysisFailedReason
import arrow.core.Either

interface FridayBalanceService {
    fun getBalance(fridayExternalId: ExternalId): Either<FridayBankAccountError, Balance>
    fun getCashoutBankAccount(fridayExternalId: ExternalId): Either<FridayBankAccountError, BankAccount>
    fun createPix(createModattaPixRequest: CreateModattaPixRequest): Either<Pair<String, String>, BillId>
}

interface PartnerSimpleSignUpService {
    fun validate(userData: UserData): PartnerSimpleSignUpValidation
    fun signUp(request: PartnerSimpleSignUpRequest): PartnerSimpleSignUpResult
    fun getAccountStatus(fridayExternalId: ExternalId): AccountStatus
}

interface ModattaSimpleSignUpRepository {
    fun find(userId: UserId): Either<AccountNotFoundException, ModattaSimpleSignUp>
    fun save(simpleSignUp: ModattaSimpleSignUp)
}

interface ModattaBackendAdapter {
    fun getBalance(externalId: ModattaExternalId): Either<ModattaCorpError, ModattaBalance>

    fun cashout(externalId: ModattaExternalId, userBankAccount: BankAccount): Either<ModattaCorpError, Unit>

    fun notifyDepositReceived(
        notification: ModattaCashInNotification,
    ): Either<ModattaCorpError, Unit>

    fun notifyAccountRegisterUpdates(
        externalId: ModattaExternalId,
        status: AccountStatus,
        riskAnalysisFailedReasons: List<RiskAnalysisFailedReason>? = null,
    ): Either<ModattaCorpError, Unit>
}

interface ModattaBackendService {
    fun getBalance(userId: UserId): Either<ModattaCorpError, ModattaBalance>

    fun userCashOut(userId: UserId, userBankAccount: BankAccount): Either<ModattaCorpError, Unit>
}