package ai.friday.billpayment.app.modatta

import ai.friday.billpayment.PrintableSealedClass
import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.modatta.integrations.ModattaBackendAdapter
import ai.friday.billpayment.app.modatta.integrations.ModattaBackendService
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.wallet.WalletId
import arrow.core.Either
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import java.time.LocalDate

@Singleton
@Requires(env = [MODATTA_ENV])
class ModattaService(val modattaBackendAdapter: ModattaBackendAdapter) : ModattaBackendService {
    override fun getBalance(userId: UserId): Either<ModattaCorpError, ModattaBalance> {
        return modattaBackendAdapter.getBalance(userId.toModattaExternalId())
    }

    override fun userCashOut(userId: UserId, userBankAccount: BankAccount): Either<ModattaCorpError, Unit> {
        return modattaBackendAdapter.cashout(userId.toModattaExternalId(), userBankAccount)
    }
}

data class ModattaBalance(
    val amount: Long,
    val minRedeemAmount: Long,
)

class CreateModattaPixRequest(
    val description: String,
    val dueDate: LocalDate,
    val amount: Long,
    val recipient: PixRecipientRequest,
    val source: ActionSource.WalletActionSource,
    val walletId: WalletId,
)

class PixRecipientRequest(
    val accountId: AccountId,
    val name: String?,
    val document: String? = null,
    val bankAccount: BankAccount? = null,
    val pixKey: PixKey? = null,
)

// Esse id é o que vem da modatta
data class ModattaExternalId(private val internalValue: String) {
    val value = internalValue.removePrefix("ACCOUNT-")

    fun toUserId() = UserId(this.value)
}

// Esse id é o "AccountId" só que pro domínio da modatta
data class UserId(private val internalValue: String) {
    val value = "ACCOUNT-${internalValue.removePrefix("ACCOUNT-")}"

    fun toAccountId() = AccountId(value)

    fun toModattaExternalId() = ModattaExternalId(internalValue)
}

sealed class ModattaCorpError : PrintableSealedClass() {
    data class ModattaValidationError(val message: String) : ModattaCorpError()
    class ServerError(val exception: Exception) : ModattaCorpError()
}

data class ModattaCashInNotification(
    val amount: Long,
    val externalId: ModattaExternalId?,
    val date: LocalDate,
    val counterpartName: String,
    val counterpartDocument: String,
    val counterpartAccountNo: String,
    val transferType: ModattaCashInType,
    val transactionId: String? = null,
)

enum class ModattaCashInType {
    PIX,
    TED,
}

data class ModattaDepositCallback(
    val amount: Long,
    val counterpartName: String,
    val counterpartDocument: String,
    val counterpartAccountNo: String,
)