package ai.friday.billpayment.app.bill.mailbox.rules

import ai.friday.billpayment.app.bill.MailboxAddBillError
import ai.friday.billpayment.app.bill.mailbox.MailboxSecurityRule
import ai.friday.billpayment.app.bill.mailbox.MailboxSecurityValidationErrorLevel
import ai.friday.billpayment.app.bill.mailbox.MailboxValidateRequest
import ai.friday.billpayment.app.bill.mailbox.WARN_PRIORITY
import ai.friday.billpayment.app.integrations.MailboxWalletDataRepository
import ai.friday.billpayment.app.mailbox.MailboxGlobalData
import ai.friday.billpayment.app.mailbox.MailboxListType
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton

@Singleton
@Requires(property = "mailbox.security.rule.email-allowed-list.enabled", value = "true", defaultValue = "false")
class EmailAllowedListMailboxRule(
    val mailboxWalletDataRepository: MailboxWalletDataRepository,
) : MailboxSecurityRule {

    override fun getPriority() = WARN_PRIORITY + 0

    override fun execute(request: MailboxValidateRequest): Either<MailboxAddBillError.MailboxSecurityValidationError, Unit> {
        if (request.fromAccountId != null) return Unit.right() // veio de um membro da wallet

        if (MailboxGlobalData.allowedList.isEmpty()) return Unit.right()

        if (MailboxGlobalData.allowedList.any { it == request.from.value || it == request.from.domain }) return Unit.right()

        if (mailboxWalletDataRepository.has(request.walletId, MailboxListType.ALLOWED, request.from)) return Unit.right()

        return MailboxAddBillError.MailboxSecurityValidationError(
            reason = "SenderNotAllowed",
            level = MailboxSecurityValidationErrorLevel.WARN,
        ).left()
    }
}