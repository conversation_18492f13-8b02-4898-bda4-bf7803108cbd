package ai.friday.billpayment.app.bill.instrumentation

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.recurrence.BillRecurrence
import ai.friday.billpayment.app.recurrence.RecurrenceFrequency
import ai.friday.billpayment.app.wallet.Wallet

sealed class BillInstrumentationEvent {
    class Scheduled private constructor(
        val founderAccountId: AccountId,
        val memberAccountId: AccountId,
        val billsCount: Int,
    ) :
        BillInstrumentationEvent() {
        companion object {
            fun create(wallet: Wallet, accountId: AccountId, billsCount: Int): Scheduled {
                return Scheduled(
                    founderAccountId = wallet.founder.accountId,
                    memberAccountId = accountId,
                    billsCount = billsCount,
                )
            }
        }
    }

    class CreatedRecurring private constructor(
        val founderAccountId: AccountId,
        val memberAccountId: AccountId,
        val frequency: RecurrenceFrequency,
        val billType: BillType,
    ) :
        BillInstrumentationEvent() {
        companion object {
            fun create(recurrence: BillRecurrence, wallet: Wallet): CreatedRecurring {
                return CreatedRecurring(
                    founderAccountId = wallet.founder.accountId,
                    memberAccountId = recurrence.actionSource.accountId!!,
                    frequency = recurrence.rule.frequency,
                    billType = recurrence.billType,
                )
            }
        }
    }

    class CreatedMailbox private constructor(
        val founderAccountId: AccountId,
        val memberAccountId: AccountId?,
        val senderEmailDomain: String,
        val billType: BillType,
    ) : BillInstrumentationEvent() {
        companion object {
            fun create(bill: Bill, wallet: Wallet): CreatedMailbox {
                val source = bill.source as ActionSource.WalletMailBox
                return CreatedMailbox(
                    founderAccountId = wallet.founder.accountId,
                    memberAccountId = source.accountId,
                    senderEmailDomain = EmailAddress(source.from).domain,
                    billType = bill.billType,
                )
            }
        }
    }

    class Paid private constructor(val founderAccountId: AccountId, val billType: BillType) :
        BillInstrumentationEvent() {
        companion object {
            fun create(bill: Bill, wallet: Wallet): Paid {
                return Paid(
                    founderAccountId = wallet.founder.accountId,
                    billType = bill.billType,
                )
            }
        }
    }
}