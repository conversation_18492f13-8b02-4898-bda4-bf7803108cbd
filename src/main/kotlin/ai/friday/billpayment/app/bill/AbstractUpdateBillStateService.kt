package ai.friday.billpayment.app.bill

import ai.friday.billpayment.ServerError
import ai.friday.billpayment.app.integrations.InternalLock
import arrow.core.Either
import arrow.core.left

interface UpdateBillStateService<T : UpdateBillStateRequest> {
    fun update(request: T): Either<ServerError, Bill>
}

abstract class AbstractUpdateBillStateService<T : UpdateBillStateRequest>(
    private val locker: InternalLock,
) : UpdateBillStateService<T> {
    override fun update(request: T): Either<ServerError, Bill> {
        val lock = locker.acquireLock(request.billId.value) ?: return ServerError(BillAlreadyLockedException(request.billId)).left()

        try {
            return this.process(request)
        } finally {
            lock.unlock()
        }
    }

    protected abstract fun process(request: T): Either<ServerError, Bill>
}