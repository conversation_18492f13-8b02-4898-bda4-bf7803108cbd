package ai.friday.billpayment.app.bill.tracking

import ai.friday.billpayment.Err
import ai.friday.billpayment.PrintableSealedClass
import ai.friday.billpayment.ServerError
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillAmountCalculator
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillSynchronizationStatus
import ai.friday.billpayment.app.bill.BillTrackingCalculateOptions
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.CalculatedAmount
import ai.friday.billpayment.app.bill.FichaCompensacaoAdded
import ai.friday.billpayment.app.bill.NotSupported
import ai.friday.billpayment.app.bill.RegisterUpdated
import ai.friday.billpayment.app.bill.TrackableBill
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.bill.UpdatedRegisterData
import ai.friday.billpayment.app.bill.and
import ai.friday.billpayment.app.bill.calculateOrQuery
import ai.friday.billpayment.app.bill.getValidUntil
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillTrackingRepository
import ai.friday.billpayment.app.integrations.BillValidationService
import ai.friday.billpayment.app.payment.BillRegisterData
import ai.friday.billpayment.app.payment.BillRegisterStatus
import ai.friday.billpayment.app.payment.BillValidationResponse
import ai.friday.billpayment.app.payment.DefaultBillValidationResponse
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.timeFormat
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import java.time.LocalDate
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
open class BillTrackingCalculateHandlerService(
    private val provider: BillValidationService,
    private val calculator: BillAmountCalculator,
    private val updateBillService: UpdateBillService,
    private val billEventRepository: BillEventRepository,
    private val billTrackingRepository: BillTrackingRepository,
    private val billEventPublisher: BillEventPublisher,
    private val configuration: BillTrackingConfiguration,
) {
    private val logger = LoggerFactory.getLogger(BillTrackingCalculateHandlerService::class.java)

    open fun execute(
        track: TrackableBill,
        origin: BillTrackingCalculateOptions,
        processingDate: LocalDate = getZonedDateTime().toLocalDate(),
    ): Either<Err, BillTrackingCalculateResult> {
        val target = track.calculateOrQuery()

        val markers = Markers.append("billId", track.billId.value)
            .and(
                "origin" to origin,
                "amount_calculation_model" to track.amountCalculationModel,
                "calculate_or_query" to target,
                "due_date" to track.dueDate,
                "is_dda" to track.addedByDda,
            )

        if (target != origin) {
            billEventRepository.getBillById(track.billId).map { bill ->
                billEventPublisher.publish(
                    bill,
                    RegisterUpdated(
                        billId = bill.billId,
                        walletId = bill.walletId,
                        updatedRegisterData = UpdatedRegisterData.NewAmountCalculationOption(
                            amountCalculationOption = target,
                        ),
                        actionSource = ActionSource.System,
                    ),
                )
            }.getOrElse {
                logger.error(markers.andAppend("barcodeNotFound", true), "BillTrackingHandlerService#execute")
            }
        }

        val isQueryCommand = target == BillTrackingCalculateOptions.QUERY

        when (val discardResult = track.shouldDiscard(configuration.doNotTrackAfterDays ?: 0, isQueryCommand)) {
            is TrackeableDiscardResult.KeepTracking -> {
                markers.andAppend("discardResult", discardResult)
            }

            else -> {
                billTrackingRepository.remove(track.billId)
                logger.info(markers.andAppend("discardResult", discardResult), "BillTrackingHandlerService#delete")
                return BillTrackingCalculateResult.REMOVED.right()
            }
        }

        val amountUpdateResult = if (isQueryCommand) {
            sendToProvider(track)
        } else {
            sendToCalculator(track, processingDate)
        }

        val amountUpdate = amountUpdateResult.getOrElse {
            if (it is NotSupported) {
                billTrackingRepository.update(
                    trackableBill = track,
                    billTrackingType = BillTrackingCalculateOptions.QUERY,
                    processingDate = getLocalDate(),
                )
            }
            logger.warn(markers.andAppend("notSupported", true), "BillTrackingHandlerService#update")
            return it.left()
        }.takeUnless { it.isBarcodeNotFound() } ?: run {
            billTrackingRepository.remove(track.billId)
            logger.error(markers.andAppend("barcodeNotFound", true), "BillTrackingHandlerService#delete")
            return BillTrackingCalculateResult.REMOVED.right()
        }

        logger.info(
            markers.and(
                "amount" to amountUpdate.billRegisterData?.amount,
                "amountTotal" to amountUpdate.billRegisterData?.amountTotal,
                "discount" to amountUpdate.billRegisterData?.discount,
                "interest" to amountUpdate.billRegisterData?.interest,
                "fine" to amountUpdate.billRegisterData?.fine,
            ),
            "BillTrackingHandlerService#execute",
        )

        val updateBillResponse = updateBillService.synchronizeBill(track.billId, amountUpdate)
        markers.andAppend("updateBillResponseStatus", updateBillResponse.status)

        val result = when (val status = updateBillResponse.status) {
            is BillSynchronizationStatus.UnableToValidate -> {
                val response = if (status.isRetryable) SyncRetryable else UnableToSync
                response.left()
            }

            is BillSynchronizationStatus.BillStatusUpdated, BillSynchronizationStatus.BillAmountTotalUpdated, BillSynchronizationStatus.BillAmountTotalUpdatedToLess, BillSynchronizationStatus.BillAmountCalculationValidUntilUpdated -> BillTrackingCalculateResult.UPDATED.right()
            is BillSynchronizationStatus.BillNotModified -> BillTrackingCalculateResult.NOT_MODIFIED.right()
            is BillSynchronizationStatus.BillIsNotActive -> {
                logger.info(markers.andAppend("billIsNotActive", true), "BillTrackingHandlerService#delete")
                billTrackingRepository.remove(track.billId)
                return BillTrackingCalculateResult.REMOVED.right()
            }
        }

        if (result.isLeft()) {
            result.mapLeft { if (it == SyncRetryable) return result }
        }

        val validUntil = amountUpdate.billRegisterData!!.amountCalculationValidUntil

        if (validUntil == null) {
            logger.info(markers, "BillTrackingHandlerService#delete")
            billTrackingRepository.remove(track.billId)
            return BillTrackingCalculateResult.REMOVED.right()
        } else {
            val nextProcessingDate = validUntil.plusDays(1)
            markers.andAppend("nextProcessingDate", nextProcessingDate)
                .andAppend("discountData", track.discountData)
            logger.info(markers, "BillTrackingHandlerService#update")
            billTrackingRepository.update(track, nextProcessingDate)
        }

        return result
    }

    fun isBillOutdated(billView: BillView) = isBillOutdated(billView.billType, billView.status, billView.dueDate)

    open fun findById(billId: BillId) = billTrackingRepository.findById(billId)

    private fun isBillOutdated(bill: Bill): Boolean = isBillOutdated(bill.billType, bill.status, bill.dueDate)

    private fun isBillOutdated(billType: BillType, billStatus: BillStatus, dueDate: LocalDate) = runCatching {
        val doNotTrackAfterDays = configuration.doNotTrackAfterDays ?: 0
        billStatus == BillStatus.ACTIVE &&
            doNotTrackAfterDays > 0 &&
            getLocalDate().isAfter(dueDate.plusDays(doNotTrackAfterDays)) &&
            billType == BillType.FICHA_COMPENSACAO
    }.getOrDefault(false)

    private sealed class TrackeableDiscardResult : PrintableSealedClass() {
        data class DiscardedByTrackingLimit(val trackingLimit: Long, val dueDate: LocalDate) : TrackeableDiscardResult()
        data class DiscardedByBillStatus(val billStatus: BillStatus) : TrackeableDiscardResult()
        data object DiscardedByBillNotFound : TrackeableDiscardResult()
        data object DiscardedQueryCommandForDDA : TrackeableDiscardResult()
        data object KeepTracking : TrackeableDiscardResult()
    }

    // #1262
    private fun TrackableBill.shouldDiscard(doNotTrackAfterDays: Long, isQueryCommand: Boolean) = when {
        isQueryCommand && addedByDda -> TrackeableDiscardResult.DiscardedQueryCommandForDDA
        else -> billEventRepository.getBillById(billId).fold(
            ifLeft = { TrackeableDiscardResult.DiscardedByBillNotFound },
            ifRight = {
                when (it.status) {
                    BillStatus.ACTIVE -> when (isBillOutdated(it)) {
                        false -> TrackeableDiscardResult.KeepTracking
                        true -> TrackeableDiscardResult.DiscardedByTrackingLimit(doNotTrackAfterDays, it.dueDate)
                    }

                    else -> TrackeableDiscardResult.DiscardedByBillStatus(it.status)
                }
            },
        )
    }

    private fun sendToProvider(trackableBill: TrackableBill): Either<Err, BillValidationResponse> = try {
        val response = provider.validate(trackableBill.barCode)
        DefaultBillValidationResponse(
            billRegisterData = response.billRegisterData?.copy(
                amountCalculationValidUntil = getValidUntil(
                    discountData = trackableBill.discountData,
                    dueDate = trackableBill.dueDate,
                    actualProcessingDate = getLocalDate(),
                    fineData = trackableBill.fineData,
                    interestData = trackableBill.interestData,
                    trackableBill.addedByDda,
                ),
            ),
            gateway = response.gateway,
            errorDescription = response.errorDescription,
            billRegisterStatus = BillRegisterStatus(notPayable = response.notPayable(), alreadyPaid = response.alreadyPaid(), barcodeNotFound = response.isBarcodeNotFound()),
        ).right()
    } catch (ex: Exception) {
        ServerError(err = ex).left()
    }

    internal fun sendToCalculator(
        trackableBill: TrackableBill,
        processingDate: LocalDate = getZonedDateTime().toLocalDate(),
    ): Either<Err, BillValidationResponse> =
        with(trackableBill) {
            val res = calculator.calculate(
                amount = amount,
                dueDate = dueDate,
                fineData = fineData,
                interestData = interestData,
                discountData = discountData,
                abatement = abatement.toDouble(),
                processingDate = processingDate,
            ).getOrElse {
                return@with it.left()
            }

            val bill = billEventRepository.getBillById(trackableBill.billId).getOrElse {
                return Either.Left(ServerError(it))
            }

            DefaultBillValidationResponse(
                billRegisterData = createBillRegisterData(bill, res, this),
                gateway = FinancialServiceGateway.FRIDAY,
                billRegisterStatus = BillRegisterStatus(
                    notPayable = bill.status == BillStatus.NOT_PAYABLE,
                    alreadyPaid = bill.isPaid(),
                ),
            ).right()
        }

    private fun createBillRegisterData(
        bill: Bill,
        calculatedAmount: CalculatedAmount,
        trackableBill: TrackableBill,
    ): BillRegisterData {
        val billAdded = bill.history.first {
            it.eventType == BillEventType.ADD
        }

        val recipientChain = if (billAdded !is FichaCompensacaoAdded) {
            null
        } else {
            billAdded.recipientChain
        }

        return BillRegisterData(
            billType = BillType.FICHA_COMPENSACAO,
            assignor = bill.assignor!!,
            recipient = bill.recipient,
            recipientChain = recipientChain,
            payerDocument = bill.payer?.document,
            amount = calculatedAmount.originalAmount,
            discount = calculatedAmount.discount,
            interest = calculatedAmount.interest,
            fine = calculatedAmount.fine,
            amountTotal = calculatedAmount.totalAmount,
            expirationDate = LocalDate.parse(bill.expirationDate),
            dueDate = bill.dueDate,
            paymentLimitTime = bill.paymentLimitTime.format(timeFormat),
            settleDate = LocalDate.parse(bill.lastSettleDate),
            fichaCompensacaoType = bill.fichaCompensacaoType,
            payerName = bill.payer?.name ?: "",
            amountCalculationModel = bill.amountCalculationModel,
            amountPaid = bill.amountPaid,
            idNumber = bill.idNumber,
            interestData = trackableBill.interestData,
            fineData = trackableBill.fineData,
            discountData = trackableBill.discountData,
            abatement = calculatedAmount.abatement.toDouble(),
            amountCalculationValidUntil = calculatedAmount.validUntil,
            divergentPayment = bill.divergentPayment,
            partialPayment = bill.partialPayment,
        )
    }
}

@ConfigurationProperties("bill-tracking")
class BillTrackingConfiguration @ConfigurationInject constructor(
    val doNotTrackAfterDays: Long?,
)

enum class BillTrackingCalculateResult {
    REMOVED, UPDATED, NOT_MODIFIED
}