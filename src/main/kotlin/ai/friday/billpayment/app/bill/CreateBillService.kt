package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.FinancialInstitutionGlobalData
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.bill.duplication.PossibleDuplicate
import ai.friday.billpayment.app.bill.duplication.PossibleDuplicateBillService
import ai.friday.billpayment.app.contact.ContactId
import ai.friday.billpayment.app.contact.ContactService
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.PixQRCodeParserService
import ai.friday.billpayment.app.integrations.TedConfiguration
import ai.friday.billpayment.app.payment.BillRegisterData
import ai.friday.billpayment.app.payment.BillValidationResponse
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixKeyException
import ai.friday.billpayment.app.recurrence.BillRecurrence
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.timeFormat
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import java.time.LocalDate
import java.time.LocalTime
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
open class CreateBillService(
    private val billRepository: BillRepository,
    private val contactService: ContactService,
    private val accountService: AccountService,
    private val pixKeyManagement: PixKeyManagement,
    private val updateBillService: UpdateBillService,
    private val tedConfiguration: TedConfiguration,
    private val walletLimitsService: WalletLimitsService,
    private val possibleDuplicateBillService: PossibleDuplicateBillService,
    private val pixQRCodeParserService: PixQRCodeParserService,
    private val categoryService: PFMWalletCategoryService,
    private val concessionariaService: ConcessionariaService,
) {
    open fun createConcessionaria(
        request: ConcessionariaRequestWithDueDate,
        preemptValidation: BillValidationResponse? = null,
        dryRun: Boolean = false,
    ): CreateBillResult {
        return concessionariaService.createConcessionaria(request, preemptValidation, dryRun)
    }

    fun findBillsByWalletAndEffectiveDueDate(walletId: WalletId, effectiveDueDate: LocalDate) =
        billRepository.findByWalletAndEffectiveDueDate(walletId, effectiveDueDate)

    fun createInvoice(
        request: CreateInvoiceRequest,
        dryRun: Boolean = false,
    ): CreateBillResult {
        if (request.recipient.bankAccount?.bankNo == 213L) {
            return CreateBillResult.FAILURE.BillNotPayable(
                description = "Número do banco de destino é o próprio Arbi",
                code = AddBillError.NOT_PAYABLE.code,
                billRegisterData = null,
            )
        }
        val invoiceAdded =
            if (dryRun) {
                buildInvoiceAdded(request, null)
            } else {
                val requestRecipient =
                    with(request) {
                        Recipient(
                            name = recipient.name.orEmpty(),
                            document = recipient.document,
                            alias = recipient.alias,
                            bankAccount = recipient.bankAccount,
                        )
                    }
                val category: WalletBillCategory? = request.categoryId?.let { categoryService.findCategory(request.walletId, it) }
                val contactId =
                    contactService.save(requestRecipient, request.recipient.accountId, request.contactId).getOrElse {
                        return CreateBillResult.FAILURE.ServerError(it)
                    }
                buildInvoiceAdded(request, contactId, category).also {
                    updateBillService.publishEvent(Bill.build(), it)
                }
            }

        val warningCode =
            calculateWarningCode(
                request.walletId,
                invoiceAdded,
            )

        val bill = Bill.build(invoiceAdded)
        val possibleDuplicateBills = possibleDuplicateBillService.check(bill)

        return CreateBillResult.SUCCESS(
            bill = bill,
            warningCode = warningCode,
            possibleDuplicateBills = possibleDuplicateBills,
        )
    }

    private fun buildInvoiceAdded(
        request: CreateInvoiceRequest,
        contactId: ContactId?,
        category: WalletBillCategory? = null,
    ) =
        with(request) {
            val requestRecipient =
                Recipient(
                    name = recipient.name.orEmpty(),
                    document = recipient.document,
                    alias = recipient.alias,
                    bankAccount = recipient.bankAccount,
                )
            BillAdded(
                billId = id,
                created = createdOn.toInstant().toEpochMilli(),
                walletId = walletId,
                description = description,
                dueDate = dueDate,
                amount = amount,
                recipient = requestRecipient,
                billType = BillType.INVOICE,
                paymentLimitTime = LocalTime.parse(tedConfiguration.limitTime, timeFormat),
                actionSource = source,
                recurrenceRule = recurrenceRule,
                effectiveDueDate = calculateEffectiveDate(dueDate = dueDate, billType = BillType.INVOICE),
                contactId = contactId,
                categoryId = category?.categoryId,
            )
        }

    fun createPixKeyFromRecurrence(
        request: CreatePixRequest,
        recurrence: BillRecurrence,
    ): CreateBillResult {
        val pixKeyDetails: PixKeyDetails? =
            request.recipient.pixKey?.let { pixKey ->
                try {
                    val account = accountService.findAccountById(request.toSourceAccountId())
                    pixKeyManagement.findKeyDetailsCacheable(pixKey, account.document).pixKeyDetails
                } catch (e: PixKeyException) {
                    if (recurrence.bills.isEmpty()) {
                        return createPix(request, false)
                    }
                    buildPixKeyDetailsFromLastBill(recurrence)
                }
            }

        val requestRecipient =
            with(request) {
                Recipient(
                    name = pixKeyDetails?.owner?.name ?: recipient.name!!,
                    document = pixKeyDetails?.owner?.document ?: recipient.document,
                    alias = recipient.alias,
                    bankAccount = recipient.bankAccount,
                    pixKeyDetails = pixKeyDetails,
                )
            }

        val contactId =
            if (request.source is ActionSource.SubscriptionRecurrence) {
                null
            } else {
                contactService.save(requestRecipient, request.recipient.accountId, request.recipient.id)
                    .getOrElse {
                        LOG.warn(
                            Markers.append("accountId", request.recipient.accountId.value)
                                .andAppend("walletId", request.walletId.value)
                                .andAppend("recipientId", request.recipient.id)
                                .andAppend("recipient", requestRecipient),
                            "failedToSaveContactOnRecurrence",
                            it,
                        )
                        null
                    }
            }

        val pixAdded =
            buildPixAdded(request, requestRecipient, contactId)
                .also { updateBillService.publishEvent(Bill.build(), it) }

        val bill = Bill.build(pixAdded)
        val warningCode = calculateWarningCode(request.walletId, pixAdded)
        val possibleDuplicateBills = possibleDuplicateBillService.check(bill)

        return CreateBillResult.SUCCESS(
            bill = bill,
            warningCode = warningCode,
            possibleDuplicateBills = possibleDuplicateBills,
        )
    }

    private fun buildPixKeyDetailsFromLastBill(
        recurrence: BillRecurrence,
    ): PixKeyDetails? {
        val lastBillId = recurrence.bills.last()
        val lastBill = updateBillService.getBill(lastBillId)

        return recurrence.recipientPixKey
            ?.takeIf { lastBill.recipient != null && lastBill.recipient!!.pixKeyDetails != null }
            ?.let {
                PixKeyDetails(
                    key = it,
                    holder = lastBill.recipient!!.pixKeyDetails!!.holder,
                    owner = lastBill.recipient!!.pixKeyDetails!!.owner,
                )
            }
    }

    fun createPix(
        request: CreatePixRequest,
        dryRun: Boolean,
    ): CreateBillResult {
        if (request.recipient.bankAccount != null) {
            if (request.recipient.bankAccount.ispb.isNullOrEmpty()) {
                return buildBillNotPayable(CreateBillError.ISPB_IS_REQUIRED)
            }
            val isPixParticipant =
                FinancialInstitutionGlobalData.pixParticipants.any { it.ispb == request.recipient.bankAccount.ispb }
            if (!isPixParticipant) {
                return buildBillNotPayable(CreateBillError.ISPB_NOT_PIX_PARTICIPANT)
            }
        }

        if (request.recipient.pspInfo != null) {
            if (request.recipient.pspInfo.code.isEmpty()) {
                return buildBillNotPayable(CreateBillError.ISPB_IS_REQUIRED)
            }
            val isPixParticipant =
                FinancialInstitutionGlobalData.pixParticipants.any { it.ispb == request.recipient.pspInfo.code }
            if (!isPixParticipant) {
                return buildBillNotPayable(CreateBillError.ISPB_NOT_PIX_PARTICIPANT)
            }
        }

        val account = accountService.findAccountById(request.toSourceAccountId())
        val pixKey = request.recipient.pixKey
        val pixKeyDetailResult =
            try {
                if (pixKey != null) {
                    pixKeyManagement.findKeyDetailsCacheable(pixKey, account.document)
                } else if (request.recipient.qrCode != null) {
                    val qrCodeResponse =
                        pixQRCodeParserService.parseQRCodeCacheable(PixCopyAndPaste(request.recipient.qrCode), account.document).getOrElse {
                            throw PixKeyException(it)
                        }
                    if (qrCodeResponse.qrCodeInfo?.fixedAmount != null && qrCodeResponse.qrCodeInfo.fixedAmount != request.amount) {
                        return buildBillNotPayable(CreateBillError.PIX_QR_CODE_INVALID_AMOUNT)
                    }
                    qrCodeResponse.asPixKeyDetailsResult()
                } else {
                    null
                }
            } catch (e: PixKeyException) {
                return when (e.pixKeyError) {
                    is PixKeyError.KeyNotFound -> {
                        pixKey?.let { invalidateRecipient(request, pixKey) }
                        buildBillNotPayable(CreateBillError.PIX_KEY_NOT_FOUND)
                    }

                    is PixKeyError.KeyNotConfirmed -> {
                        pixKey?.let { invalidateRecipient(request, pixKey) }
                        buildBillNotPayable(CreateBillError.PIX_KEY_NOT_CONFIRMED)
                    }

                    else -> CreateBillResult.FAILURE.ServerError(IllegalStateException("unexpected pixKeyError ${e.pixKeyError}"))
                }
            }

        val requestRecipient =
            with(request) {
                Recipient(
                    name = pixKeyDetailResult?.pixKeyDetails?.owner?.name ?: recipient.name!!,
                    document = pixKeyDetailResult?.pixKeyDetails?.owner?.document ?: recipient.document,
                    alias = recipient.alias,
                    bankAccount = recipient.bankAccount,
                    pixKeyDetails = pixKeyDetailResult?.pixKeyDetails,
                    pixQrCodeData = pixKeyDetailResult?.qrCodeInfo,
                    pspInformation = request.recipient.pspInfo,
                )
            }

        val category: WalletBillCategory? = request.categoryId?.let { categoryService.findCategory(request.walletId, it) }

        val pixAdded = if (dryRun) {
            buildPixAdded(request, requestRecipient, request.recipient.id, category)
        } else {
            val contactId = if (request.source is ActionSource.SubscriptionRecurrence || request.recipient.qrCode != null || request.source is ActionSource.AutomaticPix) {
                null
            } else {
                contactService.save(requestRecipient, request.recipient.accountId, request.recipient.id).getOrElse {
                    return CreateBillResult.FAILURE.ServerError(it)
                }
            }

            buildPixAdded(request, requestRecipient, contactId, category).also {
                updateBillService.publishEvent(Bill.build(), it)
            }
        }

        val bill = Bill.build(pixAdded)
        val warningCode = calculateWarningCode(request.walletId, pixAdded)
        val possibleDuplicateBills = possibleDuplicateBillService.check(bill)

        return CreateBillResult.SUCCESS(
            bill = bill,
            warningCode = warningCode,
            possibleDuplicateBills = possibleDuplicateBills,
        )
    }

    private fun calculateWarningCode(
        walletId: WalletId,
        billAdded: BillAdded,
    ): WarningCode? {
        return calculateWarningCode(walletId, billAdded.amount, billAdded.effectiveDueDate!!)
    }

    fun calculateWarningCode(
        walletId: WalletId,
        amount: Long,
        date: LocalDate,
    ): WarningCode? {
        val dailyLimit = walletLimitsService.getDailyLimit(walletId)

        val availableLimitForecast =
            walletLimitsService.getAvailableLimit(
                walletId = walletId,
                date = date,
                countScheduledBankTransfer = true,
            )

        val availableMonthlyForecast =
            walletLimitsService.getAvailableMonthlyLimit(
                walletId = walletId,
                date = date,
                countScheduled = true,
            )

        return when {
            dailyLimit < amount -> WarningCode.UNSCHEDULED_DUE_AMOUNT_HIGHER_THAN_DAILY_LIMIT
            availableLimitForecast < amount -> WarningCode.POSTPONED_DUE_LIMIT_REACHED
            availableMonthlyForecast != null && availableMonthlyForecast < amount -> WarningCode.POSTPONED_DUE_MONTHLY_LIMIT_REACHED
            else -> null
        }
    }

    private fun buildPixAdded(
        request: CreatePixRequest,
        newRecipient: Recipient,
        contactId: ContactId?,
        walletBillCategory: WalletBillCategory? = null,
    ): BillAdded {
        val pixType = if (request.source is ActionSource.AutomaticPix) {
            BillType.AUTOMATIC_PIX
        } else {
            BillType.PIX
        }

        return with(request) {
            BillAdded(
                billId = id,
                created = createdOn.toInstant().toEpochMilli(),
                walletId = walletId,
                description = description,
                dueDate = dueDate,
                amount = amount,
                recipient = newRecipient,
                contactId = contactId,
                billType = pixType,
                paymentLimitTime = LocalTime.parse("23:59", timeFormat),
                actionSource = source,
                recurrenceRule = request.recurrenceRule,
                effectiveDueDate =
                calculateEffectiveDate(
                    dueDate = dueDate,
                    billType = pixType,
                    actionSource = source,
                ),
                subscriptionFee = source is ActionSource.SubscriptionRecurrence,
                pixQrCodeData = newRecipient.pixQrCodeData,
                automaticPixData = automaticPixData,
            )
        }
    }

    private fun buildBillNotPayable(createBillError: CreateBillError) =
        CreateBillResult.FAILURE.BillNotPayable(
            description = createBillError.description,
            code = createBillError.code,
            billRegisterData = null,
        )

    private fun invalidateRecipient(
        request: CreatePixRequest,
        pixKey: PixKey,
    ) {
        request.recipient.id?.let { recipientId ->
            contactService.invalidateSettlementInfo(
                accountId = request.recipient.accountId,
                pixKey = pixKey,
                contactId = recipientId,
            )
        }
    }

    private fun CreateBoletoRequest.toBillPermissionUpdate(billId: BillId): BillPermissionUpdated {
        return BillPermissionUpdated(
            billId = billId,
            created = createdOn.toInstant().toEpochMilli(),
            walletId = walletId,
            actionSource = source,
            permissionUpdated = PermissionUpdated.VisibilityAdded(toSourceAccountId()),
        )
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(CreateBillService::class.java)
    }
}

enum class CreateBillError(val description: String, val code: String) {
    PIX_KEY_NOT_FOUND(description = "pix key not found", code = "4200"),
    PIX_KEY_NOT_CONFIRMED(description = "key is not confirmed", code = "4203"),
    ISPB_NOT_PIX_PARTICIPANT(
        description = "ispb should be of a financial institution participant in pix",
        code = "4000",
    ),
    ISPB_IS_REQUIRED(description = "ispb cannot be null", code = "4000"),
    PIX_MUST_HAVE_BANK_ACCOUNT_OR_PIX_KEY_OR_QRCODE(
        description = "Pix bill recipient should have a pix key or a bank account or a QRCode",
        code = "4201",
    ),
    PIX_KEY_INVALID(description = "Pix key value is not valid for type ", code = "4202"),
    PIX_QR_CODE_INVALID_AMOUNT(description = "qrcode has a value. it's the only amount allowed", code = "4203"),
    PIX_QR_CODE_CANT_BE_RECURRENT(description = "qrcode pix can't be used to create a recurrent payment", code = "4205"),
    DAILY_LIMIT_EXCEEDED(description = "Daily limit exceeded", code = "4300"),
    MONTHLY_LIMIT_EXCEEDED(description = "Monthly limit exceeded", code = "4301"),
    SERVER_ERROR(description = "Server error", code = "5000"),
}

sealed interface CreateBillResult {
    class SUCCESS(
        val bill: Bill,
        val warningCode: WarningCode? = null,
        val possibleDuplicateBills: List<PossibleDuplicate> = listOf(),
    ) : CreateBillResult

    sealed interface FAILURE : CreateBillResult {
        class BillAlreadyExists(val bill: Bill) : FAILURE

        class BillNotPayable(
            val description: String,
            val code: String = AddBillError.NOT_PAYABLE.code,
            val billRegisterData: BillRegisterData?,
        ) : FAILURE

        class BillUnableToValidate(val description: String, val isRetryable: Boolean = true) : FAILURE

        class ServerError(val throwable: Throwable) : FAILURE

        sealed interface AlreadyPaid : FAILURE {
            class WithData(
                val description: String,
                val billRegisterData: BillRegisterData,
            ) : AlreadyPaid

            class WithoutData(
                val description: String,
                val barCode: BarCode,
                val dueDate: LocalDate,
            ) : AlreadyPaid
        }
    }
}

enum class BillSegment(val prefix: String?) {
    PREFECTURE("81"),
    SANITATION("82"),
    ELECTRICITY_AND_GAS("83"),
    TELECOM("84"),
    GOVERNMENT("85"),
    CARNET("86"),
    TRAFFIC_TICKET("87"),
    OTHERS(null),

    ;

    companion object {
        fun getSegmentByPrefix(prefix: String): BillSegment = values().find { it.prefix == prefix } ?: OTHERS
    }
}