package ai.friday.billpayment.app.bill.mailbox.rules

import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.MailboxAddBillError
import ai.friday.billpayment.app.bill.mailbox.BLOCK_PRIORITY
import ai.friday.billpayment.app.bill.mailbox.MailboxSecurityRule
import ai.friday.billpayment.app.bill.mailbox.MailboxSecurityValidationErrorLevel
import ai.friday.billpayment.app.bill.mailbox.MailboxValidateRequest
import ai.friday.billpayment.app.integrations.BillEventRepository
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton

@Singleton
@Requires(property = "mailbox.security.rule.user-ignored-bill.enabled", value = "true", defaultValue = "false")
class UserIgnoredBillMailboxRule(
    private val billEventRepository: BillEventRepository,
) : MailboxSecurityRule {
    override fun getPriority() = BLOCK_PRIORITY + 4

    override fun execute(request: MailboxValidateRequest): Either<MailboxAddBillError.MailboxSecurityValidationError, Unit> {
        if (request.fromAccountId != null) return Unit.right()

        val lastBill = billEventRepository.findLastBill(request.barCode, request.walletId).getOrElse {
            return Unit.right()
        }

        if (lastBill.status == BillStatus.IGNORED) {
            return MailboxAddBillError.MailboxSecurityValidationError(
                reason = "BillAlreadyIgnored",
                level = MailboxSecurityValidationErrorLevel.BLOCK,
            ).left()
        }

        return Unit.right()
    }
}