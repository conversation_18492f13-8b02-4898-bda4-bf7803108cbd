package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.utilityaccount.Utility
import ai.friday.billpayment.app.utilityaccount.matches

const val FICHA_DIGITABLE_LINE_SIZE = 47
const val CONCESSIONARIA_DIGITABLE_LINE_SIZE = 48

@BillEventDependency
class BarCode(
    val number: String,
    val digitable: String,
) {
    fun formattedDigitable(): String {
        return when (digitable.length) {
            FICHA_DIGITABLE_LINE_SIZE -> "${digitable.substring(0, 5)}.${
            digitable.substring(
                5,
                10,
            )
            } ${digitable.substring(10, 15)}.${digitable.substring(15, 21)} ${
            digitable.substring(
                21,
                26,
            )
            }.${digitable.substring(26, 32)} ${digitable.substring(32, 33)} ${digitable.substring(33)}"

            CONCESSIONARIA_DIGITABLE_LINE_SIZE -> "${digitable.substring(0, 11)}-${
            digitable.substring(
                11,
                12,
            )
            } ${digitable.substring(12, 23)}-${digitable.substring(23, 24)} ${
            digitable.substring(
                24,
                35,
            )
            }-${digitable.substring(35, 36)} ${digitable.substring(36, 47)}-${digitable.substring(47)}"

            else -> throw IllegalArgumentException("digitable line should be $FICHA_DIGITABLE_LINE_SIZE or $CONCESSIONARIA_DIGITABLE_LINE_SIZE. Received $digitable")
        }
    }

    fun checkIsConcessionaria(): Boolean {
        return digitable.length == CONCESSIONARIA_DIGITABLE_LINE_SIZE
    }

    fun billType(): BillType {
        return if (this.checkIsConcessionaria()) BillType.CONCESSIONARIA else BillType.FICHA_COMPENSACAO
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as BarCode

        if (number != other.number) return false
        if (digitable != other.digitable) return false

        return true
    }

    override fun hashCode(): Int {
        var result = number.hashCode()
        result = 31 * result + digitable.hashCode()
        return result
    }

    companion object {
        const val BARCODE_SIZE = 44

        fun of(number: String): BarCode {
            return BarCode(number = number, digitable = convertToDigitable(number))
        }

        fun ofDigitable(digitable: String): BarCode {
            return BarCode(number = convertToBarCode(digitable), digitable = digitable)
        }

        fun detect(value: String): BarCode {
            return when (value.length) {
                BARCODE_SIZE -> of(value)
                FICHA_DIGITABLE_LINE_SIZE, CONCESSIONARIA_DIGITABLE_LINE_SIZE -> ofDigitable(value)
                else -> throw IllegalArgumentException("Invalid barcode")
            }
        }
    }
}

private fun convertToDigitable(number: String): String {
    return when (number.substring(0, 1)) {
        "8" -> convertConcessionariaToDigitable(number)
        else -> convertFichaToDigitable(number)
    }
}

private fun convertFichaToDigitable(number: String): String {
    val fields = listOf(
        number.substring(0, 4) + number.substring(19, 24),
        number.substring(24, 34),
        number.substring(34, 44),
        number.substring(4, 19),
    )
    return fields[0] + mod10(fields[0]) +
        fields[1] + mod10(fields[1]) +
        fields[2] + mod10(fields[2]) +
        fields[3]
}

private fun convertConcessionariaToDigitable(number: String): String {
    val fields =
        listOf(number.substring(0, 11), number.substring(11, 22), number.substring(22, 33), number.substring(33, 44))
    return when (number.substring(2, 3)) {
        "6", "7" -> fields[0] + mod10(fields[0]) +
            fields[1] + mod10(fields[1]) +
            fields[2] + mod10(fields[2]) +
            fields[3] + mod10(fields[3])

        "8", "9" -> fields[0] + mod11(fields[0]) +
            fields[1] + mod11(fields[1]) +
            fields[2] + mod11(fields[2]) +
            fields[3] + mod11(fields[3])

        else -> throw java.lang.IllegalArgumentException("Invalid barcode")
    }
}

fun checkIsConcessionaria(barCode: BarCode): Boolean {
    return try {
        convertConcessionariaToDigitable(barCode.number)
        true
    } catch (e: Exception) {
        false
    }
}

private fun convertToBarCode(digitable: String): String {
    return when (digitable.length) {
        FICHA_DIGITABLE_LINE_SIZE -> digitable.substring(0, 4) + digitable.substring(32, 47) + digitable.substring(
            4,
            9,
        ) + digitable.substring(10, 20) + digitable.substring(21, 31)

        CONCESSIONARIA_DIGITABLE_LINE_SIZE -> digitable.substring(0, 11) + digitable.substring(
            12,
            23,
        ) + digitable.substring(24, 35) + digitable.substring(36, 47)

        else -> throw IllegalArgumentException("digitable line should be $FICHA_DIGITABLE_LINE_SIZE or $CONCESSIONARIA_DIGITABLE_LINE_SIZE. Received $digitable")
    }
}

fun BarCode.isFicha(): Boolean {
    if (digitable.length != FICHA_DIGITABLE_LINE_SIZE) {
        return false
    }

    val globalCheckDigit = number[4]
    val firstCheckableBlock = digitable[9]
    val secondCheckableBlock = digitable[20]
    val thirdCheckableBlock = digitable[31]

    val firstBlock = number.substring(0, 4) + number.substring(19, 24)
    val secondBlock = number.substring(24, 34)
    val thirdBlock = number.substring(34, 44)

    val mod11 = mod11SpecialCase(number.substring(0, 4) + number.substring(5))

    return mod10(firstBlock) == firstCheckableBlock.toString() &&
        mod10(secondBlock) == secondCheckableBlock.toString() &&
        mod10(thirdBlock) == thirdCheckableBlock.toString() &&
        mod11 == globalCheckDigit.toString()
}

fun BarCode.convenantCode(): String? {
    return if (this.checkIsConcessionaria()) {
        number.substring(15, 19)
    } else {
        null
    }
}

fun BarCode.segmentAndCovenantCode(): String? {
    return if (this.checkIsConcessionaria()) {
        number.substring(1, 2) + number.substring(16, 20)
    } else {
        null
    }
}

fun BarCode.utility(): Utility {
    return if (this.checkIsConcessionaria()) {
        Utility.entries.singleOrNull { utility ->
            utility.matches(this)
        } ?: Utility.UNKNOWN
    } else {
        Utility.UNKNOWN
    }
}

private fun mod10(number: String): String {
    val sum = number.reversed().asIterable().withIndex().sumOf {
        when {
            it.index % 2 == 1 -> Character.getNumericValue(it.value)
            Character.getNumericValue(it.value) >= 5 -> (Character.getNumericValue(it.value) * 2) - 9
            else -> Character.getNumericValue(it.value) * 2
        }
    }
    return when (val sumResult = 10 - (sum % 10)) {
        10 -> "0"
        else -> sumResult.toString()
    }
}

private fun mod11(number: String): String {
    val multipliers = listOf(2, 3, 4, 5, 6, 7, 8, 9, 2, 3, 4)
    val sum = number.reversed().asIterable().withIndex().sumOf {
        Character.getNumericValue(it.value) * multipliers[it.index]
    }
    return when (val sumResult = 11 - (sum % 11)) {
        10, 11 -> "0"
        else -> sumResult.toString()
    }
}

private fun mod11SpecialCase(number: String): String {
    var sum = 0
    var i = number.length - 1

    while (i >= 0) {
        sum += (((number.length - 1 - i) % 8) + 2) * Character.getNumericValue(number[i])
        i -= 1
    }
    return if (sum % 11 > 1) (11 - sum % 11).toString() else "1"
}