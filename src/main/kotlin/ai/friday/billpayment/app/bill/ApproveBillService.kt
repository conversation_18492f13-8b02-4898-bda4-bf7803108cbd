package ai.friday.billpayment.app.bill

import ai.friday.billpayment.ServerError
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.MailboxWalletDataRepository
import ai.friday.billpayment.app.mailbox.MailboxListType
import ai.friday.billpayment.app.wallet.canSchedule
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import jakarta.inject.Named
import jakarta.inject.Singleton

@Singleton
class ApproveBillService(
    @Named(billLockProvider) locker: InternalLock,
    private val repository: BillEventRepository,
    private val eventPublisher: BillEventPublisher,
    private val mailboxWalletDataRepository: MailboxWalletDataRepository,
) : AbstractUpdateBillStateService<ApproveUpdateBillRequest>(locker) {
    override fun process(request: ApproveUpdateBillRequest): Either<ServerError, Bill> = with(request) {
        val bill = repository.getBill(walletId, billId).getOrElse { return ServerError(it).left() }

        if (!member.canSchedule(bill.source, visibleBy = bill.visibleBy)) {
            return ServerError(MemberNotAllowedException(member.accountId, bill.billId)).left()
        }

        val event = BillApproved(
            billId = bill.billId,
            walletId = walletId,
            actionSource = actionSource,
        )

        eventPublisher.publish(bill, event)

        val billSource = bill.source
        if (billSource is ActionSource.WalletMailBox) {
            if (request.alwaysAllowSender) {
                mailboxWalletDataRepository.add(
                    walletId = bill.walletId,
                    type = MailboxListType.ALLOWED,
                    email = EmailAddress(billSource.from),
                )
            }
        }

        return bill.right()
    }
}