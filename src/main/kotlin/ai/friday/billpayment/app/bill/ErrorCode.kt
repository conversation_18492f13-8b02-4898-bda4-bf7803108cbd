package ai.friday.billpayment.app.bill

enum class AddBillError(val description: String, val code: String) {
    NOT_PAYABLE("", "4001"),
    AMOUNT_ZERO("AmountTotal equals 0", "4002"),
    BILL_TYPE_NOT_YET_SUPPORTED("TIPO DE TÍTULO AINDA NÃO SUPORTADO", "4003"),
    BILL_ALREADY_INCLUDED("Bill Already included", "4004"),
    ALREADY_PAID("Bill Already paid", "4005"),
    CREDIT_CARD_OVERDUE("Cartão de crédito com data de vencimento no passado", "4006"),
    PAYMENT_NOT_AUTHORIZED("Cedente não autorizado", "4007"),
    UNABLE_TO_VALIDATE("Boleto não encontrado", "4008"),
    AMOUNT_EXCEEDED("AmountTotal greater than 250_000", "4009"),
}

enum class ScheduleError(val description: String, val code: String) {
    PAYMENT_METHOD_NOT_FOUND("No valid payment method", "5001"),
}