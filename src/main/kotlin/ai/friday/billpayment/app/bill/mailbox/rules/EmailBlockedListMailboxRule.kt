package ai.friday.billpayment.app.bill.mailbox.rules

import ai.friday.billpayment.app.bill.MailboxAddBillError
import ai.friday.billpayment.app.bill.mailbox.BLOCK_PRIORITY
import ai.friday.billpayment.app.bill.mailbox.MailboxSecurityRule
import ai.friday.billpayment.app.bill.mailbox.MailboxSecurityValidationErrorLevel
import ai.friday.billpayment.app.bill.mailbox.MailboxValidateRequest
import ai.friday.billpayment.app.mailbox.MailboxGlobalData
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton

@Singleton
@Requires(property = "mailbox.security.rule.email-blocked-list.enabled", value = "true", defaultValue = "false")
class EmailBlockedListMailboxRule : MailboxSecurityRule {

    override fun getPriority() = BLOCK_PRIORITY + 0

    override fun execute(request: MailboxValidateRequest): Either<MailboxAddBillError.MailboxSecurityValidationError, Unit> {
        if (request.fromAccountId != null) return Unit.right() // veio de um membro da wallet

        if (MailboxGlobalData.blockList.isEmpty()) return Unit.right()

        if (MailboxGlobalData.blockList.any { it == request.from.value || it == request.from.domain }) {
            return MailboxAddBillError.MailboxSecurityValidationError(
                reason = "SenderIsBlocked",
                level = MailboxSecurityValidationErrorLevel.BLOCK,
            ).left()
        }

        return Unit.right()
    }
}