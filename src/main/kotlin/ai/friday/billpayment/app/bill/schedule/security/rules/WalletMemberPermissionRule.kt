package ai.friday.billpayment.app.bill.schedule.security.rules

import ai.friday.billpayment.app.bill.schedule.security.ScheduleBillSecurityError
import ai.friday.billpayment.app.bill.schedule.security.ScheduleBillSecurityRequest
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.canSchedule
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton

@Singleton
class WalletMemberPermissionRule : ScheduleBillSecurityRule {
    override fun getPriority() = Int.MIN_VALUE

    override fun execute(request: ScheduleBillSecurityRequest): Either<ScheduleBillSecurityError, Unit> {
        if (request.member.status != MemberStatus.ACTIVE) return ScheduleBillSecurityError.PermissionDenied("WalletMemberPermission").left()

        return if (request.member.canSchedule(actionSource = request.bill.source, visibleBy = request.bill.visibleBy)) {
            Unit.right()
        } else {
            ScheduleBillSecurityError.PermissionDenied("WalletMemberPermission").left()
        }
    }
}