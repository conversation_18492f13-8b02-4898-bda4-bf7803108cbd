package ai.friday.billpayment.app.bill.tracking

import ai.friday.billpayment.Err
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.BillDenied
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillIgnored
import ai.friday.billpayment.app.bill.BillMarkedAsPaid
import ai.friday.billpayment.app.bill.BillMarkedAsPaidCanceled
import ai.friday.billpayment.app.bill.BillMoved
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillReactivated
import ai.friday.billpayment.app.bill.FichaCompensacaoAdded
import ai.friday.billpayment.app.bill.PaymentFailed
import ai.friday.billpayment.app.bill.PaymentRefunded
import ai.friday.billpayment.app.bill.RegisterUpdated
import ai.friday.billpayment.app.bill.UpdatedRegisterData
import arrow.core.Either
import arrow.core.right

interface TrackableBillHandlerService {
    fun execute(event: BillEvent): Either<Err, Unit>
}

@FridayMePoupe
class DefaultTrackableBillHandlerService(
    private val createService: CreateTrackableBillService,
    private val updateService: UpdateTrackableBillService,
    private val removeService: RemoveTrackableBillService,
    private val reactivateService: ReactivateTrackableBillService,
    private val retryService: RetryTrackableBillService,
) : TrackableBillHandlerService {

    override fun execute(event: BillEvent): Either<Err, Unit> {
        return when (event) {
            is FichaCompensacaoAdded -> this.createService.create(event)
            is BillPaid,
            is BillIgnored,
            is BillMoved,
            is BillMarkedAsPaid,
            is BillDenied,
            -> removeService.remove(event)

            is PaymentRefunded,
            is BillReactivated,
            is BillMarkedAsPaidCanceled,
            -> reactivateService.reactivate(event)

            is PaymentFailed -> if (event.retryable) retryService.retry(event) else Unit.right()
            is RegisterUpdated -> this.registerUpdated(event)
            else -> Unit.right()
        }
    }

    private fun registerUpdated(event: RegisterUpdated): Either<Err, Unit> {
        return when (event.updatedRegisterData) {
            is UpdatedRegisterData.AlreadyPaidBill, is UpdatedRegisterData.NotPayableBill -> this.removeService.remove(
                event,
            )

            is UpdatedRegisterData.NewDueDate -> this.updateService.updateDueDateByBillID(
                event.billId,
                event.updatedRegisterData.dueDate,
            )

            is UpdatedRegisterData.NewTotalAmount -> this.updateService.updateAmountByBillID(
                event.billId,
                event.updatedRegisterData.amount,
            )

            is UpdatedRegisterData.NewAmountCalculationData -> this.updateService.updatePaymentCalculationDataByBillID(
                event.billId,
                event.updatedRegisterData.fineData,
                event.updatedRegisterData.interestData,
                event.updatedRegisterData.discountData,
            )

            is UpdatedRegisterData.VehicleDebtEnrichment, is UpdatedRegisterData.NewAmountCalculationOption, is UpdatedRegisterData.NewAmountCalculationValidUntil -> Unit.right()
        }
    }
}