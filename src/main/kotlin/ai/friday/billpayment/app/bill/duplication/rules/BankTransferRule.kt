package ai.friday.billpayment.app.bill.duplication.rules

import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillTag
import ai.friday.billpayment.app.bill.duplication.PossibleDuplicationResult
import ai.friday.billpayment.app.bill.duplication.PossibleDuplicationRule
import ai.friday.billpayment.app.bill.duplication.SimilarityWrapper
import jakarta.inject.Singleton

@Singleton
class BankTransferRule : PossibleDuplicationRule {
    override fun priority(): Int = 3

    override fun accept(first: SimilarityWrapper, second: SimilarityWrapper): Boolean =
        first.billType.isBankTransfer() && second.billType.isBankTransfer()

    override fun check(first: SimilarityWrapper, second: SimilarityWrapper): PossibleDuplicationResult {
        if (first.status in ignoredStatus || second.status in ignoredStatus) {
            return PossibleDuplicationResult.NotDuplicated
        }

        if (first.status == BillStatus.PAID && second.status == BillStatus.PAID) {
            return PossibleDuplicationResult.NotDuplicated
        }

        if (first.tags?.contains(BillTag.ONBOARDING_TEST_PIX) == true) {
            return PossibleDuplicationResult.NotDuplicated
        }

        if (first.recipientDocument == second.recipientDocument &&
            first.effectiveDueDate == second.effectiveDueDate &&
            first.amountTotal == second.amountTotal
        ) {
            return PossibleDuplicationResult.BilateralDuplication(first, second)
        }

        return PossibleDuplicationResult.NotDuplicated
    }

    private val ignoredStatus = listOf(
        BillStatus.IGNORED,
        BillStatus.ALREADY_PAID,
        BillStatus.NOT_PAYABLE,
        BillStatus.MOVED,
    )
}