package ai.friday.billpayment.app.bill.mailbox.rules

import ai.friday.billpayment.app.bill.MailboxAddBillError
import ai.friday.billpayment.app.bill.mailbox.BLOCK_PRIORITY
import ai.friday.billpayment.app.bill.mailbox.MailboxSecurityRule
import ai.friday.billpayment.app.bill.mailbox.MailboxSecurityValidationErrorLevel
import ai.friday.billpayment.app.bill.mailbox.MailboxValidateRequest
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Requires
import jakarta.inject.<PERSON>ton

@Singleton
@Requires(
    property = "mailbox.security.rule.ficha-compensacao-not-allowed.enabled",
    value = "true",
    defaultValue = "false",
)
class FichaCompensacaoNotAllowedMailboxRule : MailboxSecurityRule {

    override fun getPriority() = BLOCK_PRIORITY + 3
    override fun execute(request: MailboxValidateRequest): Either<MailboxAddBillError.MailboxSecurityValidationError, Unit> {
        if (request.barCode.checkIsConcessionaria()) {
            return Unit.right()
        }
        return MailboxAddBillError.MailboxSecurityValidationError(
            reason = "FichaCompensacaoNotAllowed",
            level = MailboxSecurityValidationErrorLevel.BLOCK,
        ).left()
    }
}