package ai.friday.billpayment.app.bill.tracking

import ai.friday.billpayment.Err

data class CalculateResponseTO(
    val originalAmount: Long,
    val discount: Long,
    val interest: Long,
    val fine: Long,
    val abatement: Long?,
    val totalAmount: Long,
)

object SyncRetryable : Err("SyncRetryable")
object UnableToSync : Err("UnableToSync")

object UntrackableEntity : Err("UntrackableEntity")