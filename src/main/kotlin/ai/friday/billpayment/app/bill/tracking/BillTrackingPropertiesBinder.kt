package ai.friday.billpayment.app.bill.tracking

import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties

@ConfigurationProperties(value = "bill-tracking")
data class BillTrackingPropertiesBinder @ConfigurationInject constructor(
    val calculate: BillTrackingCalculateProperties,
    val query: BillTrackingQueryProperties,
) {
    @ConfigurationProperties(value = "calculate")
    data class BillTrackingCalculateProperties @ConfigurationInject constructor(val partitions: Int, val queue: String)

    @ConfigurationProperties(value = "query")
    data class BillTrackingQueryProperties @ConfigurationInject constructor(val partitions: Int, val queue: String)
}