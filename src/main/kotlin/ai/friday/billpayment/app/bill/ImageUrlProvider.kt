package ai.friday.billpayment.app.bill

import ai.friday.morning.log.andAppend
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
open class BillImageProvider(
    private val imageUrlProviders: List<ImageUrlProvider<*>>,
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    fun getBillImageUrl(billView: BillView): String? {
        return billView.goalId?.let { getBillImageUrl(listOf(billView.goalId)) }
    }

    fun getBillImageUrl(bill: Bill): String? {
        return getBillImageUrl(listOfNotNull(bill.goalId))
    }

    private fun getBillImageUrl(elements: List<*>): String? {
        if (elements.isEmpty()) {
            return null
        }
        val providerFound = elements.firstNotNullOfOrNull { element ->
            element?.let { findProvider(it) }?.let { found -> element to found }
        }

        return providerFound?.let { (element, provider) ->
            provider.getImageUrl(element)
        } ?: run {
            logger.error(append("elements", elements).andAppend("context", "nenhum provedor de imagem encontrado"), "BillImageProvider#getBillImageUrl")
            null
        }
    }

    private inline fun <reified T> findProvider(id: T): ImageUrlProvider<T>? {
        if (id == null) {
            return null
        }

        return imageUrlProviders.firstOrNull {
            it.urlProviderFor() == id!!::class.java
        } as ImageUrlProvider<T>?
    }
}

interface ImageUrlProvider<T> {
    fun getImageUrl(id: T): String?
    fun urlProviderFor(): Class<T>
}