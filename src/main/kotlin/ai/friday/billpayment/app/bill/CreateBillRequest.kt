package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.contact.ContactId
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.UUID

sealed class CreateBillRequest {
    abstract val id: BillId
    abstract val description: String
    abstract val createdOn: ZonedDateTime
    abstract val status: BillStatus
    abstract val source: ActionSource.WalletActionSource
}

sealed class CreateBoletoRequest : CreateBillRequest() {
    abstract val barcode: BarCode
    override val id: BillId = BillId("BILL-${UUID.randomUUID()}")
    override val createdOn: ZonedDateTime = getZonedDateTime()
    override val status: BillStatus = BillStatus.WAITING_APPROVAL
    abstract val member: Member?
    abstract val walletId: WalletId
}

class CreateInvoiceRequest(
    override val description: String,
    val dueDate: LocalDate,
    val amount: Long,
    val recipient: RecipientRequest,
    override val source: ActionSource.WalletActionSource,
    val recurrenceRule: RecurrenceRule? = null,
    val contactId: ContactId?,
    val categoryId: PFMCategoryId? = null,
    val walletId: WalletId,
) : CreateBillRequest() {
    override val id: BillId = BillId("BILL-${UUID.randomUUID()}")
    override val createdOn: ZonedDateTime = getZonedDateTime()
    override val status: BillStatus = BillStatus.WAITING_APPROVAL
}

class CreatePixRequest(
    override val description: String,
    val dueDate: LocalDate,
    val amount: Long,
    val recipient: RecipientRequest,
    override val source: ActionSource.WalletActionSource,
    val recurrenceRule: RecurrenceRule? = null,
    val categoryId: PFMCategoryId? = null,
    val walletId: WalletId,
    val automaticPixAuthorizationMaximumAmount: Long?,
    val automaticPixData: AutomaticPixData?,
) : CreateBillRequest() {
    override val id: BillId = BillId("BILL-${UUID.randomUUID()}")
    override val createdOn: ZonedDateTime = getZonedDateTime()
    override val status: BillStatus = BillStatus.WAITING_APPROVAL
}

data class AutomaticPixData(
    val payer: AutomaticPixPayer?,
    val description: String?,
    val additionalInformation: String?,
    val contractNumber: String?,
)

data class AutomaticPixPayer(
    val document: String?,
    val name: String?,
)

data class CreateInvestmentRequest(
    override val description: String,
    val dueDate: LocalDate,
    val amount: Long,
    override val source: ActionSource.WalletActionSource,
    val categoryId: PFMCategoryId,
    val walletId: WalletId,
    val goalId: GoalId,
) : CreateBillRequest() {
    override val id: BillId = BillId("BILL-${UUID.randomUUID()}")
    override val createdOn: ZonedDateTime = getZonedDateTime()
    override val status: BillStatus = BillStatus.WAITING_APPROVAL
}

fun CreateBillRequest.toSourceAccountId(): AccountId {
    return source.accountId ?: throw IllegalStateException("invalid action source $source")
}

data class CreateDirectDebitRequest(
    val id: BillId = BillId(),
    val directDebitId: String,
    val identificationCode: String,
    val account: Account,
    val beneficiary: Recipient,
    val schedulingDate: LocalDate,
    val dueDate: LocalDate,
    val amount: Long,
    val amountTotal: Long,
    val assignor: String,
    val externalProvider: ExternalBillProvider,
)

data class RecipientRequest(
    val id: ContactId? = null,
    val accountId: AccountId,
    val name: String?,
    val document: String? = null,
    val alias: String = "",
    val bankAccount: BankAccount? = null,
    val pspInfo: PspInformation? = null,
    val pixKey: PixKey? = null,
    val qrCode: String?,
)

sealed class ConcessionariaRequest(
    override val description: String,
    open val dueDate: LocalDate?,
    override val barcode: BarCode,
    override val walletId: WalletId,
    override val source: ActionSource.WalletActionSource,
    override val member: Member? = null,
    open val securityValidationErrors: List<MailboxAddBillError.MailboxSecurityValidationError> = emptyList(),
    open val externalBillId: ExternalBillId? = null,
) : CreateBoletoRequest()

sealed class ConcessionariaRequestWithDueDate(
    override val description: String,
    override val dueDate: LocalDate,
    override val barcode: BarCode,
    override val walletId: WalletId,
    override val source: ActionSource.WalletActionSource,
    override val member: Member? = null,
    override val securityValidationErrors: List<MailboxAddBillError.MailboxSecurityValidationError> = emptyList(),
    override val externalBillId: ExternalBillId? = null,
) : ConcessionariaRequest(
    description = description,
    dueDate = dueDate,
    barcode = barcode,
    walletId = walletId,
    source = source,
    member = member,
    securityValidationErrors = securityValidationErrors,
    externalBillId = externalBillId,
)

data class CreateConcessionariaRequest(
    override val description: String,
    override val dueDate: LocalDate,
    override val barcode: BarCode,
    override val walletId: WalletId,
    override val source: ActionSource.WalletActionSource,
    override val member: Member? = null,
    override val securityValidationErrors: List<MailboxAddBillError.MailboxSecurityValidationError> = emptyList(),
    override val externalBillId: ExternalBillId? = null,
) : ConcessionariaRequestWithDueDate(
    description = description,
    dueDate = dueDate,
    barcode = barcode,
    walletId = walletId,
    source = source,
    member = member,
    securityValidationErrors = securityValidationErrors,
    externalBillId = externalBillId,
)

data class ForcedConcessionariaRequest(
    override val description: String,
    override val dueDate: LocalDate,
    override val barcode: BarCode,
    override val walletId: WalletId,
    override val externalBillId: ExternalBillId? = null,
    override val source: ActionSource.WalletActionSource,
    val accountId: AccountId,
    val amount: Long,
) : ConcessionariaRequestWithDueDate(
    description = description,
    dueDate = dueDate,
    barcode = barcode,
    walletId = walletId,
    source = source,
    member = null,
    securityValidationErrors = emptyList(),
    externalBillId = externalBillId,
)

data class PaidConcessionariaRequest(
    override val description: String,
    override val dueDate: LocalDate?,
    override val barcode: BarCode,
    override val walletId: WalletId,
    override val externalBillId: ExternalBillId? = null,
    override val source: ActionSource.WalletActionSource,
    val accountId: AccountId,
    val amount: Long,
) : ConcessionariaRequest(
    description = description,
    dueDate = dueDate,
    barcode = barcode,
    walletId = walletId,
    source = source,
    member = null,
    securityValidationErrors = emptyList(),
    externalBillId = externalBillId,
)

data class CreateFichaDeCompensacaoRequest(
    override val description: String,
    override val barcode: BarCode,
    override val walletId: WalletId,
    override val source: ActionSource.WalletActionSource,
    val payerAlias: String? = null,
    override val member: Member? = null,
    val securityValidationErrors: List<MailboxAddBillError.MailboxSecurityValidationError> = emptyList(),
    val externalBillId: ExternalBillId? = null,
) : CreateBoletoRequest()

fun RecipientRequest.checkExistsOneRecipient(): Boolean {
    val possibilities = listOfNotNull(bankAccount, pixKey, qrCode)
    return possibilities.size == 1
}