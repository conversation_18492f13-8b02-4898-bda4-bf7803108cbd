package ai.friday.billpayment.app.bill

import ai.friday.billpayment.ServerError
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.wallet.canSchedule
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import jakarta.inject.Named
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
class DenyBillService(
    @Named(billLockProvider) locker: InternalLock,
    private val repository: BillEventRepository,
    private val eventPublisher: BillEventPublisher,
) : AbstractUpdateBillStateService<DenyUpdateBillRequest>(locker) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun process(request: DenyUpdateBillRequest): Either<ServerError, Bill> = with(request) {
        val bill = repository.getBill(walletId, billId).getOrElse { return ServerError(it).left() }

        if (!member.canSchedule(actionSource = bill.source, visibleBy = bill.visibleBy)) {
            return ServerError(MemberNotAllowedException(member.accountId, bill.billId)).left()
        }

        val event = BillDenied(
            billId = bill.billId,
            walletId = walletId,
            actionSource = actionSource,
        )

        logger.warn(
            append("billId", bill.billId.value)
                .andAppend("walletId", walletId)
                .andAppend("actionSource", bill.source),
            "DenyBillService",
        )

        eventPublisher.publish(bill, event)

        return bill.right()
    }
}