package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FinancialInstitutionGlobalData
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.account.hasEarlyAccess
import ai.friday.billpayment.app.isValidCpf
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.payment.NU_BANK_DOCUMENT
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.toAmountFormat
import ai.friday.morning.date.brazilTimeZone
import io.micronaut.context.annotation.Property
import io.micronaut.context.event.ApplicationEventListener
import io.micronaut.discovery.event.ServiceReadyEvent
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.Locale
import kotlin.time.Duration.Companion.seconds
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers

val BRAZIL_LOCALE: Locale = Locale.forLanguageTag("pt-BR")

private val ALL_PAYMENT_METHODS =
    listOf(PaymentMethodType.BALANCE, PaymentMethodType.CREDIT_CARD, PaymentMethodType.EXTERNAL)
private val NO_CREDIT_CARD = listOf(PaymentMethodType.BALANCE, PaymentMethodType.EXTERNAL)

fun ZonedDateTime.deltaInSeconds(dateTime: ZonedDateTime) =
    (this.toEpochSecond() - dateTime.toEpochSecond()).seconds.inWholeSeconds

@FridayMePoupe
open class BillUtilsService(
    @Property(name = "bill.fichaCompensacao.untrustedCnpjs") private val untrustedCnpjs: Set<String>,
    @Property(name = "bill.restrictPaymentMethods.enabled") private val restrictPaymentMethods: Boolean,
) : ApplicationEventListener<ServiceReadyEvent> {

    init {
        BillUtilsService.untrustedCnpjs = this.untrustedCnpjs
        BillUtilsService.restrictPaymentMethods = this.restrictPaymentMethods
    }

    override fun onApplicationEvent(event: ServiceReadyEvent?) {
        BillUtilsService.untrustedCnpjs = this.untrustedCnpjs
        BillUtilsService.restrictPaymentMethods = this.restrictPaymentMethods
    }

    companion object {
        var untrustedCnpjs: Set<String> = emptySet()
            private set

        var restrictPaymentMethods: Boolean = false
            private set
    }
}

fun LocalDate.toEpochMillis(zone: ZoneId = ZoneId.of("America/Sao_Paulo")) =
    this.atStartOfDay().atZone(zone).toInstant().toEpochMilli()

fun LocalDate.isBusinessDay(): Boolean {
    return FinancialInstitutionGlobalData.isBusinessDay(this)
}

fun LocalDate.iso(): String = format(DateTimeFormatter.ISO_LOCAL_DATE)

fun LogstashMarker.and(vararg args: Pair<String, Any?>): LogstashMarker =
    args.toMap().filterValues { it != null }.takeIf { it.isNotEmpty() }
        ?.map { this.and<LogstashMarker>(Markers.append(it.key, it.value)) }
        ?.reduce { first, second -> first.and(second) } ?: this

fun Bill.isCreditCard() = isCreditCard(this.fichaCompensacaoType, this.recipient)
fun BillView.isCreditCard() = isCreditCard(this.fichaCompensacaoType, this.recipient)
fun isCreditCard(fichaCompensacaoType: FichaCompensacaoType?, recipient: Recipient?) =
    (fichaCompensacaoType == FichaCompensacaoType.CARTAO_DE_CREDITO) || isNuBankCreditCard(
        fichaCompensacaoType,
        recipient,
    )

private fun isNuBankCreditCard(fichaCompensacaoType: FichaCompensacaoType?, recipient: Recipient?) =
    fichaCompensacaoType == FichaCompensacaoType.CH_CHEQUE && recipient?.document == NU_BANK_DOCUMENT

private fun isBoletoDeposito(fichaCompensacaoType: FichaCompensacaoType?) =
    fichaCompensacaoType == FichaCompensacaoType.DEPOSITO_E_APORTE

fun Bill.checkCanBePaidWith(type: PaymentMethodType, member: Member) = type in availablePaymentMethods(member)

fun Bill.availablePaymentMethods(member: Member) =
    availablePaymentMethods(member, billType, fichaCompensacaoType, recipient, subscriptionFee)

fun BillView.availablePaymentMethods(member: Member) =
    availablePaymentMethods(member, billType, fichaCompensacaoType, recipient, subscriptionFee)

fun Bill.isOwnTrustedBill(userPayerDocument: String) = isTrustedBoleto(
    userPayerDocument,
    billType,
    fichaCompensacaoType,
    recipient,
) && userPayerDocument == payer?.document

fun BillView.isOwnTrustedBill(userPayerDocument: String) = isTrustedBoleto(
    userPayerDocument,
    billType,
    fichaCompensacaoType,
    recipient,
) && userPayerDocument == this.payerDocument

private fun availablePaymentMethods(
    member: Member,
    billType: BillType,
    fichaCompensacaoType: FichaCompensacaoType?,
    recipient: Recipient?,
    subscriptionFee: Boolean,
): List<PaymentMethodType> {
    // TODO: remover após testes de cartão na motorola
    if (member.document in listOf("05856182758", "13397597722", "03761255004")) {
        return ALL_PAYMENT_METHODS
    }

    if (billType.isBoleto() && Document(member.document).hasEarlyAccess()) { // TODO - remover essa condição ao virar para todos os usuários
        return ALL_PAYMENT_METHODS
    }

    if (!BillUtilsService.restrictPaymentMethods) {
        return ALL_PAYMENT_METHODS
    }

    return if (isTrustedBoleto(
            member.document,
            billType,
            fichaCompensacaoType,
            recipient,
        ) && !subscriptionFee
    ) {
        ALL_PAYMENT_METHODS
    } else {
        NO_CREDIT_CARD
    }
}

private fun isTrustedBoleto(
    userPayerDocument: String,
    billType: BillType,
    fichaCompensacaoType: FichaCompensacaoType?,
    recipient: Recipient?,
): Boolean {
    if (!billType.isBoleto()) {
        return false
    }

    if (isCreditCard(fichaCompensacaoType, recipient) || isBoletoDeposito(fichaCompensacaoType)) {
        return false
    }
    return !recipient.hasUntrustedRecipient(userPayerDocument)
}

fun memberOlderThanDate(member: Member, desiredDate: String): Boolean {
    return member.created < ZonedDateTime.of(LocalDate.parse(desiredDate).atStartOfDay(), brazilTimeZone)
}

private fun Recipient?.hasUntrustedRecipient(userPayerDocument: String): Boolean {
    this?.document?.takeIf { it.isValidCpf() }?.let { return true }

    return userPayerDocument == this?.document || this?.document in BillUtilsService.untrustedCnpjs
}

fun Bill.beneficiaryName() = this.recipient?.name
fun Bill.beneficiaryDocument() = this.recipient?.document

fun Bill.payerName() = this.payer?.name
fun Bill.payerDocument() = this.payer?.document

fun Bill.amountPaid() = this.amountPaid?.toAmountFormat()