package ai.friday.billpayment.app.bill.tracking

import ai.friday.billpayment.Err
import ai.friday.billpayment.ServerError
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.FichaCompensacaoAdded
import ai.friday.billpayment.app.bill.TrackableBill
import ai.friday.billpayment.app.bill.calculateOrQuery
import ai.friday.billpayment.app.bill.getValidUntil
import ai.friday.billpayment.app.integrations.BillTrackingRepository
import ai.friday.billpayment.app.payment.DiscountData
import ai.friday.billpayment.app.payment.FineData
import ai.friday.billpayment.app.payment.InterestData
import ai.friday.billpayment.app.payment.hasDiscount
import ai.friday.billpayment.app.payment.hasFine
import ai.friday.billpayment.app.payment.hasInterest
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import java.math.BigDecimal
import java.time.Instant
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

interface CreateTrackableBillService {
    fun create(event: FichaCompensacaoAdded): Either<Err, Unit>
}

@FridayMePoupe
class DefaultCreateTrackableBillService(private val repository: BillTrackingRepository) : CreateTrackableBillService {

    override fun create(event: FichaCompensacaoAdded): Either<Err, Unit> {
        try {
            val markers = Markers.append("billId", event.billId.value)

            val untrackable = event.isUntrackable()
            markers.andAppend("untrackable", untrackable)

            if (untrackable) {
                LOG.info(markers, "DefaultCreateTrackableBillService#create")
                return UntrackableEntity.left()
            }

            val trackableBill = event.toTrackableBill()

            val nextProcessingDate = event.calcNextUpdateDate()
            markers.andAppend("nextProcessingDate", nextProcessingDate)
                .andAppend("hasDiscount", event.discountData.hasDiscount())
                .andAppend("amountCalculationModel", trackableBill.amountCalculationModel)
                .andAppend("calculateOrQuery", trackableBill.calculateOrQuery())

            if (nextProcessingDate != null) {
                LOG.info(markers, "DefaultCreateTrackableBillService#create")
                repository.create(trackableBill, nextProcessingDate)
            } else {
                LOG.error(markers, "DefaultCreateTrackableBillService#create")
            }
            return Unit.right()
        } catch (ex: Exception) {
            return ServerError(ex).left()
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(DefaultCreateTrackableBillService::class.java)
    }
}

fun FichaCompensacaoAdded.toTrackableBill() = TrackableBill(
    billId = billId,
    created = ZonedDateTime.ofInstant(Instant.ofEpochMilli(created), brazilTimeZone),
    isActive = true,
    barCode = barcode,
    addedByDda = isDDA(),
    billType = BillType.FICHA_COMPENSACAO,
    dueDate = dueDate,
    amount = amount,
    amountCalculationModel = amountCalculationModel,
    fineData = FineData(
        type = fineData?.type,
        value = fineData?.value,
        date = fineData?.date,
    ),
    interestData = InterestData(
        type = interestData?.type,
        value = interestData?.value,
        date = interestData?.date,
    ),
    discountData = DiscountData(
        type = discountData?.type,
        value1 = discountData?.value1,
        date1 = discountData?.date1,
        value2 = discountData?.value2,
        date2 = discountData?.date2,
        value3 = discountData?.value3,
        date3 = discountData?.date3,
    ),
    abatement = abatement ?: BigDecimal.valueOf(0.0),
)

private fun FichaCompensacaoAdded.isDDA() = actionSource is ActionSource.DDA

private fun FichaCompensacaoAdded.isUntrackable() = isDDA() && !discountData.hasDiscount() && !fineData.hasFine() && !interestData.hasInterest()

private fun FichaCompensacaoAdded.calcNextUpdateDate() = getValidUntil(
    discountData = discountData,
    dueDate = dueDate,
    actualProcessingDate = getLocalDate(),
    fineData = fineData,
    interestData = interestData,
    addedByDDA = isDDA(),
)?.plusDays(1)

fun FichaCompensacaoAdded.amountCalculationValidUntil() =
    if (isUntrackable()) null else calcNextUpdateDate()?.minusDays(1)

fun FichaCompensacaoAdded.amountCalculationOption() = if (isUntrackable()) null else toTrackableBill().calculateOrQuery()