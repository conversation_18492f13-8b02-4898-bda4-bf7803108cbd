package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.duplication.PossibleDuplicate
import ai.friday.billpayment.app.billcategory.BillCategorySuggestion
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.DivergentPayment
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.payment.PartialPayment
import ai.friday.billpayment.app.payment.PaymentMethodsDetail
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZonedDateTime

data class BillView(
    val billId: BillId,
    val assignor: String? = null,
    val recipient: Recipient? = null,
    val billDescription: String = "",
    val amount: Long = 0,
    val discount: Long = 0,
    val interest: Long = 0,
    val fine: Long = 0,
    val amountTotal: Long = amount + interest + fine - discount,
    val dueDate: LocalDate,
    val effectiveDueDate: LocalDate,
    val expirationDate: LocalDate? = null,
    val paidDate: LocalDateTime? = null,
    val amountPaid: Long? = null,
    val barCode: BarCode? = null,
    val billType: BillType,
    val walletId: WalletId,
    val status: BillStatus,
    val paymentLimitTime: LocalTime,
    val notification: String? = null,
    val createdOn: LocalDateTime,
    val lastSettleDate: LocalDate? = null,
    val source: ActionSource,
    val isBillComingDueMessageSent: Boolean = false,
    val payerDocument: String? = null,
    val payerName: String? = null,
    val warningCode: WarningCode = WarningCode.NONE,
    val schedule: BillViewSchedule? = null,
    val payerAlias: String? = null,
    val amountCalculationModel: AmountCalculationModel? = null,
    val amountCalculationValidUntil: LocalDate? = null,
    val amountCalculationOption: BillTrackingCalculateOptions? = null,
    val recurrenceRule: RecurrenceRule? = null,
    var fichaCompensacaoType: FichaCompensacaoType? = null,
    val visibleBy: List<AccountId> = listOf(),
    val idNumber: String? = null,
    val externalId: ExternalBillId? = null,
    val markedAsPaidBy: AccountId? = null,
    val partnerPayment: PartnerPayment? = null,
    val subscriptionFee: Boolean,
    val securityValidationResult: List<String>? = null,
    val paymentDetails: PaymentMethodsDetail? = null,
    val transactionCorrelationId: String? = null,
    val tags: Set<BillTag>,
    val categoryId: PFMCategoryId?,
    val categorySuggestions: List<BillCategorySuggestion>,
    val pixQrCodeData: PixQrCodeData? = null,
    val divergentPayment: DivergentPayment? = null,
    val partialPayment: PartialPayment? = null,
    val brand: String? = null,
    val participants: BillParticipants? = null,
    val scheduledBy: AccountId? = null,
    val scheduleSource: ActionSource? = null,
    val paymentWalletId: WalletId? = null,
    val markedAsPaidAt: LocalDateTime? = null,
    val refundedAt: LocalDateTime? = null,
    val ignoredAt: LocalDateTime? = null,
    val goalId: GoalId? = null,
    val details: String? = null,
    val automaticPixAuthorizationMaximumAmount: Long? = null,
    val automaticPixData: AutomaticPixData?,
) {

    init {
        fichaCompensacaoType =
            if (billType == BillType.FICHA_COMPENSACAO && fichaCompensacaoType == null) FichaCompensacaoType.OUTROS else fichaCompensacaoType
    }

    val expectedPaymentDate: LocalDate = schedule?.date ?: effectiveDueDate

    val isOverdue: Boolean
        get() {
            val time = paymentLimitTime
            val zonedDueDateTime = ZonedDateTime.of(effectiveDueDate.atTime(LocalTime.from(time)), brazilTimeZone)
            return (
                getZonedDateTime()
                    .isAfter(zonedDueDateTime) &&
                    BillStatus.ACTIVE == status
                )
        }
    val payee: String
        get() {
            return recipient?.name ?: assignor.orEmpty()
        }

    fun outOfSync(): Boolean {
        if (billType in listOf(BillType.CONCESSIONARIA, BillType.INVOICE, BillType.PIX)) {
            return false
        }
        if (!isOverdue) {
            return false
        }
        if (lastSettleDate == null) {
            return true
        }
        val time = paymentLimitTime
        val zonedSettleDateTime = ZonedDateTime.of(lastSettleDate.atTime(LocalTime.from(time)), brazilTimeZone)
        return zonedSettleDateTime.isBefore(getZonedDateTime())
    }

    fun isPaymentScheduled(): Boolean {
        return schedule != null
    }

    fun isOpen(): Boolean {
        return status == BillStatus.ACTIVE && !isPaymentScheduled()
    }

    fun isTriPix(): Boolean {
        return this.tags.any { it == BillTag.ONBOARDING_TEST_PIX }
    }

    fun formattedDescription(): String {
        return if (billDescription != "") {
            "$payee ($billDescription)"
        } else {
            payee
        }
    }
}

data class BillViewSchedule(
    val batchSchedulingId: BatchSchedulingId? = null,
    val date: LocalDate,
    val waitingFunds: Boolean = false,
    val waitingRetry: Boolean = false,
)

data class BillViewWrapper(
    val bill: BillView,
    val possibleDuplicateBills: List<PossibleDuplicate> = listOf(),
    val trackingOutdated: Boolean = false,
    val participants: BillParticipants? = null,
)

data class BillParticipants(
    val owner: BillParticipant,
    val scheduledBy: BillParticipant?,
    val paidBy: BillParticipant?,
)

data class BillParticipant(val walletId: WalletId, val name: String, val imageUrlSmall: String?)