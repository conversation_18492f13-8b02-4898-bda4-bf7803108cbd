package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.FinancialInstitutionGlobalData
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.hasEarlyAccess
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.bill.BillType.AUTOMATIC_PIX
import ai.friday.billpayment.app.bill.BillType.INVOICE
import ai.friday.billpayment.app.bill.BillType.PIX
import ai.friday.billpayment.app.bill.tracking.amountCalculationOption
import ai.friday.billpayment.app.bill.tracking.amountCalculationValidUntil
import ai.friday.billpayment.app.billcategory.BillCategorySuggestion
import ai.friday.billpayment.app.contact.ContactId
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.DivergentPayment
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.payment.MAX_PAYMENT_LIMIT_AMOUNT
import ai.friday.billpayment.app.payment.MultiplePaymentMethodsDetail
import ai.friday.billpayment.app.payment.PartialPayment
import ai.friday.billpayment.app.payment.PaymentMethodsDetail
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithExternalPayment
import ai.friday.billpayment.app.payment.SettlementTarget
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.security.Fingerprint
import ai.friday.billpayment.app.stripAccents
import ai.friday.billpayment.app.stripSpacesAndPunctuation
import ai.friday.billpayment.app.vehicledebts.LicensePlate
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import java.math.BigInteger
import java.time.Duration
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import java.util.UUID

@BillEventDependency
data class BillId(
    val value: String,
) {
    constructor() : this("BILL-${UUID.randomUUID()}")
}

@BillEventDependency
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME)
sealed interface ActionSource {
    sealed interface WalletActionSource : ActionSource {
        val accountId: AccountId?
    }

    @JsonTypeName("DDA")
    data class DDA(
        override val accountId: AccountId,
    ) : WalletActionSource

    @JsonTypeName("WalletMailBox")
    data class WalletMailBox(
        override val accountId: AccountId? = null,
        val from: String,
    ) : WalletActionSource

    @JsonTypeName("VehicleDebts")
    data class VehicleDebts(
        override val accountId: AccountId,
        val licensePlate: LicensePlate? = null,
    ) : WalletActionSource

    @BillEventDependency
    @JsonTypeName("Api")
    data class Api(
        override val accountId: AccountId,
        val originalBillId: BillId? = null,
    ) : WalletActionSource

    @BillEventDependency
    @JsonTypeName("AutomaticPix")
    data class AutomaticPix(
        override val accountId: AccountId,
        val endToEnd: String,
        val recurringPaymentId: String,
    ) : WalletActionSource

    @BillEventDependency
    @JsonTypeName("GoalInvestment")
    data class GoalInvestment(
        override val accountId: AccountId,
        val goalId: GoalId,
    ) : WalletActionSource

    @BillEventDependency
    @JsonTypeName("VirtualAssistant")
    data class VirtualAssistant(
        override val accountId: AccountId,
        val originalBillId: BillId? = null,
        val transactionId: String? = null,
    ) : WalletActionSource

    @JsonTypeName("WalletRecurrence")
    data class WalletRecurrence(
        override val accountId: AccountId,
        val recurrenceId: RecurrenceId,
    ) : WalletActionSource

    @JsonTypeName("Subscription")
    data class Subscription(
        override val accountId: AccountId,
    ) : WalletActionSource

    @JsonTypeName("SubscriptionRecurrence")
    data class SubscriptionRecurrence(
        override val accountId: AccountId,
        val recurrenceId: RecurrenceId,
    ) : WalletActionSource

    @JsonTypeName("ConnectUtility")
    data class ConnectUtility(
        override val accountId: AccountId,
    ) : WalletActionSource

    @JsonTypeName("OpenFinance")
    data class OpenFinance(
        override val accountId: AccountId,
        val bankNumber: Long,
    ) : WalletActionSource

    @JsonTypeName("System")
    data object System : ActionSource

    @JsonTypeName("Scheduled")
    data object Scheduled : ActionSource

    @JsonTypeName("MailBox")
    @Deprecated("Use WalletMailBox")
    data class MailBox(
        val from: String?,
        val role: Role? = null,
    ) : ActionSource

    @JsonTypeName("DirectDebit")
    data class DirectDebit(
        val identification: String,
    ) : ActionSource

    @JsonTypeName("Webapp")
    @Deprecated("Use Api")
    data class Webapp(
        val role: Role,
    ) : ActionSource

    @JsonTypeName("Recurrence")
    @Deprecated("Use WalletRecurrence")
    data class Recurrence(
        val recurrenceId: RecurrenceId,
        val role: Role,
    ) : ActionSource

    @JsonTypeName("OnePixPay")
    data object OnePixPay : ActionSource
}

@Deprecated(
    "Use name() instead, as toSourceName throws a exception",
    replaceWith =
    ReplaceWith(
        expression = "name()",
        imports = ["ai.friday.billpayment.app.bill"],
    ),
)
fun ActionSource.toSourceName() =
    when (this) {
        is ActionSource.DDA -> "DDA"
        is ActionSource.WalletMailBox,
        is ActionSource.MailBox,
        -> "Caixa Postal"

        is ActionSource.Api,
        is ActionSource.Webapp,
        is ActionSource.WalletRecurrence,
        is ActionSource.Recurrence,
        -> "App"

        is ActionSource.ConnectUtility,
        -> "Contas de consumo"

        is ActionSource.OpenFinance -> "Open finance"

        is ActionSource.DirectDebit,
        ActionSource.OnePixPay,
        ActionSource.Scheduled,
        ActionSource.System,
        is ActionSource.Subscription,
        is ActionSource.SubscriptionRecurrence,
        -> throw IllegalArgumentException("Illegal source: $this")

        is ActionSource.VirtualAssistant -> "VirtualAssistant"
        is ActionSource.GoalInvestment -> "Meta"
        is ActionSource.VehicleDebts -> "Débito veicular"
        is ActionSource.AutomaticPix -> "Pix Automático"
    }

fun ActionSource.name() =
    when (this) {
        is ActionSource.DDA -> "DDA"
        is ActionSource.WalletMailBox,
        is ActionSource.MailBox,
        -> "Caixa Postal"

        is ActionSource.Api,
        is ActionSource.Webapp,
        is ActionSource.WalletRecurrence,
        is ActionSource.Recurrence,
        -> "App"

        is ActionSource.ConnectUtility,
        -> "Contas de consumo"

        is ActionSource.OpenFinance -> "Open finance"

        is ActionSource.DirectDebit -> "Débito automático"

        ActionSource.OnePixPay -> "PIX"

        ActionSource.Scheduled,
        ActionSource.System,
        -> "Sistema"

        is ActionSource.Subscription,
        is ActionSource.SubscriptionRecurrence,
        -> "Assinatura"

        is ActionSource.VirtualAssistant -> "VirtualAssistant"
        is ActionSource.GoalInvestment -> "Meta"
        is ActionSource.VehicleDebts -> "Débito veicular"
        is ActionSource.AutomaticPix -> "Pix Automático"
    }

@BillEventDependency
data class Recipient(
    val name: String,
    val document: String? = null,
    val alias: String = "",
    val bankAccount: BankAccount? = null,
    val pixKeyDetails: PixKeyDetails? = null,
    val pixQrCodeData: PixQrCodeData? = null,
    val pspInformation: PspInformation? = null,
)

@BillEventDependency
@JsonIgnoreProperties(ignoreUnknown = true)
data class PixQrCodeData(
    val pixCopyAndPaste: PixCopyAndPaste?, // infelizmente é nulável porque no inicio não estava sendo armazenado.
    val type: PixQrCodeType,
    val info: String,
    val additionalInfo: Map<String, String>?,
    val pixId: String?,
    val fixedAmount: Long?,
    val expiration: ZonedDateTime?,
    val originalAmount: Long?,
    val automaticPixRecurringDataJson: String?,
    val compoundDataId: String? = null,
)

@BillEventDependency
data class PixCopyAndPaste(val value: String)

@BillEventDependency
enum class PixQrCodeType { DYNAMIC, STATIC, COMPOUND }

@BillEventDependency
data class BankAccount(
    val accountType: AccountType,
    val bankNo: Long?,
    val routingNo: Long,
    val accountNo: BigInteger,
    val accountDv: String,
    val document: String = "",
    val ispb: String? = null,
) {
    fun buildFullAccountNumber() = "$accountNo" + accountDv
}

@BillEventDependency
data class PspInformation(
    val code: String,
    val name: String?,
)

enum class BillStatus {
    WAITING_APPROVAL, ACTIVE, IGNORED, PROCESSING, PAID, ALREADY_PAID, NOT_PAYABLE, MOVED, PAID_ON_PARTNER, WAITING_BENEFICIARY_UPDATE;

    companion object {
        fun fromString(name: String): BillStatus {
            return if (name.uppercase() == "PENDING") {
                WAITING_APPROVAL
            } else {
                BillStatus.valueOf(name)
            }
        }
    }
}

@BillEventDependency
enum class BillType {
    CONCESSIONARIA,
    FICHA_COMPENSACAO,
    INVOICE,
    PIX,
    INVESTMENT,
    OTHERS,
    AUTOMATIC_PIX,

    ;

    fun needValidation(): Boolean = isBoleto()

    fun isBankTransfer(): Boolean = when (this) {
        INVOICE, PIX, AUTOMATIC_PIX -> true
        CONCESSIONARIA, FICHA_COMPENSACAO, INVESTMENT, OTHERS -> false
    }

    fun isBoleto(): Boolean = when (this) {
        CONCESSIONARIA, FICHA_COMPENSACAO -> true
        INVOICE, PIX, INVESTMENT, OTHERS, AUTOMATIC_PIX -> false
    }

    fun shouldCountOnTransactionalLimits(): Boolean {
        return when (this) {
            AUTOMATIC_PIX, INVESTMENT -> false
            CONCESSIONARIA,
            FICHA_COMPENSACAO,
            INVOICE,
            PIX,
            OTHERS,
            -> true
        }
    }

    fun shouldCountOnTransferLimits(): Boolean {
        return when (this) {
            AUTOMATIC_PIX, CONCESSIONARIA, FICHA_COMPENSACAO, INVESTMENT -> false
            INVOICE,
            PIX,
            OTHERS,
            -> true
        }
    }
}

const val INVESTMENT_MIN_VALUE = 50_00L
const val INVESTMENT_MAX_VALUE = 200_000_00L

data class BillPayer(
    var document: String,
    var name: String? = null,
    var alias: String? = null,
)

open class Bill private constructor() : SettlementTarget {
    lateinit var billId: BillId
        private set
    lateinit var walletId: WalletId
        private set
    lateinit var description: String
        private set
    lateinit var dueDate: LocalDate
        private set
    lateinit var billType: BillType
        private set
    var payer: BillPayer? = null
        private set
    var lastSettleDate: String? = null
        private set
    var created: Long = 0
        private set
    var barcode: BarCode? = null
        private set
    var assignor: String? = null
        private set
    lateinit var paymentLimitTime: LocalTime
        private set
    var amount: Long = 0
        private set
    var amountTotal: Long = 0
        private set
    var amountCalculationValidUntil: LocalDate? = null
        private set
    var amountCalculationOption: BillTrackingCalculateOptions? = null
        private set
    var discount: Long = 0
        private set
    var fine: Long = 0
        private set
    var interest: Long = 0
        private set
    var recipient: Recipient? = null
        private set
    var contactId: ContactId? = null
        private set
    var expirationDate: String? = null
        private set
    var amountPaid: Long? = null
        private set
    var paidDate: Long? = null
        private set
    var status: BillStatus = BillStatus.ACTIVE
        private set
    var lastRegisterUpdate: Long = 0
        private set
    var schedule: BillSchedule? = null
        private set
    var recurrenceRule: RecurrenceRule? = null
        private set
    lateinit var source: ActionSource
        private set
    lateinit var amountCalculationModel: AmountCalculationModel
        private set
    lateinit var effectiveDueDate: LocalDate
        private set
    var fichaCompensacaoType: FichaCompensacaoType? = null
        private set
    var visibleBy: MutableList<AccountId> = mutableListOf()
        private set
    var idNumber: String? = null
        private set
    var registrationUpdateNumber: Long? = null
        private set
    var markedAsPaidBy: AccountId? = null
        private set
    var externalId: ExternalBillId? = null
        private set
    var partnerPayment: PartnerPayment? = null
        private set
    var subscriptionFee: Boolean = false
        private set
    var paymentMethodsDetail: PaymentMethodsDetail? = null
        private set

    val tags: MutableSet<BillTag> = mutableSetOf()

    var categoryId: PFMCategoryId? = null

    var goalId: GoalId? = null

    var categorySuggestions: List<BillCategorySuggestion> = listOf()

    @JsonIgnore
    val history: MutableList<BillEvent> = mutableListOf()

    var securityValidationResult: List<String>? = null
        private set

    var transactionCorrelationId: String? = null
        private set

    var pixQrCodeData: PixQrCodeData? = null
        private set

    var divergentPayment: DivergentPayment? = null
        private set

    var partialPayment: PartialPayment? = null
        private set

    var brand: String? = null
        private set

    var scheduledBy: AccountId? = null
        private set

    var scheduleSource: ActionSource? = null
        private set

    var paymentWalletId: WalletId? = null
        private set

    var refundedAt: Long? = null
        private set

    var ignoredAt: Long? = null
        private set

    var markedAsPaidAt: Long? = null
        private set

    var details: String? = null
        private set

    var automaticPixAuthorizationMaximumAmount: Long? = null
        private set

    var automaticPixData: AutomaticPixData? = null
        private set

    private fun apply(descriptionUpdated: DescriptionUpdated) {
        description = descriptionUpdated.description
    }

    private fun apply(billPaid: BillPaid) {
        status = BillStatus.PAID
        paidDate = billPaid.created
        schedule = schedule.with(billPaid, billType)
        amountPaid = this.amountTotal
        recipient = billPaid.pixKeyDetails?.let {
            recipient?.copy(pixKeyDetails = it)
        } ?: recipient
    }

    private fun apply(billIgnored: BillIgnored) {
        if (isIgnorable()) {
            status = BillStatus.IGNORED
        }
        if (billIgnored.removeFromRecurrence) {
            recurrenceRule = null
        }
        ignoredAt = billIgnored.created
    }

    private fun apply(paymentStarted: PaymentStarted) {
        status = BillStatus.PROCESSING
        transactionCorrelationId = paymentStarted.correlationId
    }

    private fun apply(paymentFailed: PaymentFailed) {
        paidDate = null // sometimes a PaymentFailed is used to revert a BillPaid
        amountPaid = null
        status =
            when {
                isPaid() && (billType.isBoleto() || billType == BillType.INVESTMENT || (isInvoice() && paymentFailed.retryable)) -> BillStatus.ACTIVE // Rollback de invoice
                isPaid() && isInvoice() -> BillStatus.NOT_PAYABLE
                isProcessing() && isBankTransfer() && !paymentFailed.retryable -> {
                    schedule = schedule.with(paymentFailed, billType)
                    BillStatus.NOT_PAYABLE
                }

                isProcessing() -> {
                    schedule = schedule.with(paymentFailed, billType)
                    BillStatus.ACTIVE
                }

                else -> status
            }
    }

    private fun apply(paymentRefunded: PaymentRefunded) {
        paidDate = null
        amountPaid = null
        status =
            when (paymentRefunded.reason) {
                PaymentRefundedReason.ERROR -> BillStatus.ACTIVE
                PaymentRefundedReason.DESTINATION_NOT_ALLOWED_AMOUNT,
                PaymentRefundedReason.INVALID_DATA,
                -> BillStatus.NOT_PAYABLE
            }
        refundedAt = paymentRefunded.created
    }

    private fun apply(amountUpdated: AmountUpdated) {
        updateAmountTotal(
            newAmount = amountUpdated.amount,
            newAmountTotal = amountUpdated.amount,
        )
    }

    private fun updateAmountTotal(newAmount: Long, newAmountTotal: Long) {
        amount = newAmount
        amountTotal = newAmountTotal

        paymentMethodsDetail =
            when (this.paymentMethodsDetail) {
                is PaymentMethodsDetailWithBalance ->
                    (this.paymentMethodsDetail as PaymentMethodsDetailWithBalance).copy(amount = newAmountTotal)

                is PaymentMethodsDetailWithCreditCard ->
                    (this.paymentMethodsDetail as PaymentMethodsDetailWithCreditCard).copy(netAmount = newAmountTotal)

                is PaymentMethodsDetailWithExternalPayment -> TODO()
                is MultiplePaymentMethodsDetail -> TODO()
                null -> null
            }
    }

    private fun apply(registerUpdated: RegisterUpdated) {
        lastRegisterUpdate = registerUpdated.created
        when (val event = registerUpdated.updatedRegisterData) {
            is UpdatedRegisterData.AlreadyPaidBill -> {
                status = BillStatus.ALREADY_PAID
                amountPaid = event.amountPaid ?: this.amountTotal
                schedule = schedule.with(registerUpdated, billType)
            }

            is UpdatedRegisterData.NotPayableBill -> {
                status = BillStatus.NOT_PAYABLE
                schedule = schedule.with(registerUpdated, billType)
            }

            is UpdatedRegisterData.NewAmountCalculationData -> {}

            is UpdatedRegisterData.NewTotalAmount -> {
                lastSettleDate = event.lastSettleDate
                fine = event.fine
                interest = event.interest
                discount = event.discount + event.abatement // nao coloquei abatimento em separado porque teria que mudar o frontend, vamos tratar como um desconto
                registrationUpdateNumber = event.registrationUpdateNumber
                status = calcStatusOnNewTotalAmountUpdate(event.amountTotal, registerUpdated.createdAsZonedDateTime())
                amountCalculationValidUntil = event.amountCalculationValidUntil
                updateAmountTotal(
                    newAmount = event.amount,
                    newAmountTotal = event.amountTotal,
                )
            }

            is UpdatedRegisterData.NewDueDate -> {
                dueDate = event.dueDate
                effectiveDueDate = event.effectiveDueDate
            }

            is UpdatedRegisterData.NewAmountCalculationOption -> {
                amountCalculationOption = event.amountCalculationOption
            }

            is UpdatedRegisterData.NewAmountCalculationValidUntil -> {
                amountCalculationValidUntil = event.amountCalculationValidUntil
            }

            is UpdatedRegisterData.VehicleDebtEnrichment -> {
                source = registerUpdated.actionSource
                externalId = event.externalId
                details = event.details
            }
        }
    }

    private fun apply(billPaymentScheduled: BillPaymentScheduled) {
        schedule =
            BillSchedule(
                batchSchedulingId = billPaymentScheduled.batchSchedulingId,
                date = billPaymentScheduled.scheduledDate,
                fingerprint = billPaymentScheduled.fingerprint,
            )
        scheduledBy =
            if (billPaymentScheduled.actionSource is ActionSource.WalletActionSource) {
                billPaymentScheduled.actionSource.accountId
            } else {
                null
            }
        scheduleSource = billPaymentScheduled.actionSource
        paymentWalletId = billPaymentScheduled.paymentWalletId
        paymentMethodsDetail = billPaymentScheduled.infoData.toSchedulePaymentDetails()
    }

    @Suppress("UNUSED_PARAMETER")
    private fun apply(billPaymentScheduleCanceled: BillPaymentScheduleCanceled) {
        schedule = null
        if (!isPaid()) {
            paymentMethodsDetail = null
            transactionCorrelationId = null
            scheduledBy = null
            paymentWalletId = null
        }
    }

    private fun apply(billPaymentScheduleStarted: BillPaymentScheduleStarted) {
        schedule = schedule.with(billPaymentScheduleStarted, billType)
    }

    private fun apply(event: BillPaymentScheduleUpdated) {
        if (this.paymentMethodsDetail !is PaymentMethodsDetailWithCreditCard) return

        this.paymentMethodsDetail =
            when (val v = event.updatedScheduleData) {
                is UpdatedScheduleData.NewCalculationId ->
                    (this.paymentMethodsDetail as PaymentMethodsDetailWithCreditCard).copy(calculationId = v.calculationId)
            }
    }

    private fun apply(billAdded: BillAdded) {
        this.apply {
            status = if (billAdded.securityValidationResult.isNullOrEmpty()) BillStatus.ACTIVE else BillStatus.WAITING_APPROVAL
            billId = billAdded.billId
            created = billAdded.created
            walletId = billAdded.walletId
            description = billAdded.description
            dueDate = billAdded.dueDate
            barcode = billAdded.barcode
            assignor = billAdded.assignor
            billType = billAdded.billType
            payer =
                billAdded.document?.let {
                    BillPayer(
                        document = it,
                        alias = billAdded.payerAlias,
                        name = billAdded.payerName,
                    )
                } ?: billAdded.automaticPixData?.payer?.document?.let { automaticPixDataDocument ->
                    BillPayer(
                        document = automaticPixDataDocument,
                        alias = null,
                        name = billAdded.automaticPixData.payer.name,
                    )
                }
            paymentLimitTime = billAdded.paymentLimitTime
            amount = billAdded.amount
            amountTotal = billAdded.amountTotal
            discount = billAdded.discount
            fine = billAdded.fine
            interest = billAdded.interest
            lastSettleDate = billAdded.lastSettleDate
            recipient = billAdded.recipient
            contactId = billAdded.contactId
            expirationDate = billAdded.expirationDate
            lastRegisterUpdate = billAdded.created
            source = billAdded.actionSource
            amountCalculationModel = billAdded.amountCalculationModel ?: AmountCalculationModel.UNKNOWN
            recurrenceRule = billAdded.recurrenceRule
            effectiveDueDate = billAdded.effectiveDueDate ?: billAdded.dueDate
            subscriptionFee = billAdded.subscriptionFee
            externalId = billAdded.externalId
            securityValidationResult = billAdded.securityValidationResult
            pixQrCodeData = billAdded.pixQrCodeData
            categoryId = billAdded.categoryId
            goalId = billAdded.goalId
            automaticPixAuthorizationMaximumAmount = billAdded.automaticPixAuthorizationMaximumAmount
            automaticPixData = billAdded.automaticPixData
        }
    }

    private fun apply(fichaCompensacaoAdded: FichaCompensacaoAdded) {
        this.apply {
            status =
                when {
                    fichaCompensacaoAdded.needsBeneficiaryUpdate() -> BillStatus.WAITING_BENEFICIARY_UPDATE
                    AccountId(fichaCompensacaoAdded.walletId.value).hasEarlyAccess() && (fichaCompensacaoAdded.amountTotal == 0L || fichaCompensacaoAdded.amountTotal > MAX_PAYMENT_LIMIT_AMOUNT) -> BillStatus.NOT_PAYABLE
                    !fichaCompensacaoAdded.securityValidationResult.isNullOrEmpty() -> BillStatus.WAITING_APPROVAL
                    else -> BillStatus.ACTIVE
                }
            billId = fichaCompensacaoAdded.billId
            created = fichaCompensacaoAdded.created
            walletId = fichaCompensacaoAdded.walletId
            description = fichaCompensacaoAdded.description
            dueDate = fichaCompensacaoAdded.dueDate
            barcode = fichaCompensacaoAdded.barcode
            assignor = fichaCompensacaoAdded.assignor
            billType = BillType.FICHA_COMPENSACAO
            payer =
                BillPayer(
                    document = fichaCompensacaoAdded.document,
                    alias = fichaCompensacaoAdded.payerAlias,
                    name = fichaCompensacaoAdded.payerName,
                )
            paymentLimitTime = fichaCompensacaoAdded.paymentLimitTime
            amount = fichaCompensacaoAdded.amount
            amountTotal = fichaCompensacaoAdded.amountTotal
            discount = fichaCompensacaoAdded.discount
            fine = fichaCompensacaoAdded.fine
            interest = fichaCompensacaoAdded.interest
            lastSettleDate = fichaCompensacaoAdded.lastSettleDate
            recipient = fichaCompensacaoAdded.recipient
            expirationDate = fichaCompensacaoAdded.expirationDate
            lastRegisterUpdate = fichaCompensacaoAdded.created
            source = fichaCompensacaoAdded.actionSource
            amountCalculationModel = fichaCompensacaoAdded.amountCalculationModel
            effectiveDueDate = fichaCompensacaoAdded.effectiveDueDate
            fichaCompensacaoType = fichaCompensacaoAdded.fichaCompensacaoType
            idNumber = fichaCompensacaoAdded.idNumber
            registrationUpdateNumber = fichaCompensacaoAdded.registrationUpdateNumber
            subscriptionFee = fichaCompensacaoAdded.subscriptionFee
            securityValidationResult = fichaCompensacaoAdded.securityValidationResult
            amountCalculationOption = fichaCompensacaoAdded.amountCalculationOption()
            amountCalculationValidUntil = fichaCompensacaoAdded.amountCalculationValidUntil()
            divergentPayment = fichaCompensacaoAdded.divergentPayment
            partialPayment = fichaCompensacaoAdded.partialPayment
            brand = fichaCompensacaoAdded.brand
            externalId = fichaCompensacaoAdded.externalId
        }
    }

    @Suppress("UNUSED_PARAMETER")
    private fun apply(billReactivated: BillReactivated) {
        val billEventsBeforeIgnore = this.history.dropLastWhile { it !is BillIgnored && it !is BillDenied }.dropLast(1)
        val bill = build(*billEventsBeforeIgnore.toTypedArray())

        ignoredAt = null
        status =
            if (bill.status == BillStatus.ACTIVE) {
                if (needsBeneficaryUpdate()) {
                    BillStatus.WAITING_BENEFICIARY_UPDATE
                } else {
                    BillStatus.ACTIVE
                }
            } else {
                bill.status
            }
    }

    private fun apply(billPermissionUpdated: BillPermissionUpdated) {
        when (billPermissionUpdated.permissionUpdated) {
            is PermissionUpdated.VisibilityAdded -> visibleBy.add(billPermissionUpdated.permissionUpdated.accountId)
        }
    }

    private fun apply(event: BillMarkedAsPaid) {
        status = BillStatus.ALREADY_PAID
        amountPaid = event.amountPaid ?: this.amountTotal
        markedAsPaidBy = event.actionSource.accountId
        markedAsPaidAt = event.created
    }

    @Suppress("UNUSED_PARAMETER")
    private fun apply(event: BillMarkedAsPaidCanceled) {
        status =
            if (history.any { it is RegisterUpdated && it.updatedRegisterData is UpdatedRegisterData.NotPayableBill }) {
                BillStatus.NOT_PAYABLE
            } else {
                BillStatus.ACTIVE
            }
        amountPaid = null
        markedAsPaidBy = null
        markedAsPaidAt = null
    }

    @Suppress("UNUSED_PARAMETER")
    private fun apply(billMoved: BillMoved) {
        status = BillStatus.MOVED
    }

    private fun apply(recurrenceUpdated: RecurrenceUpdated) {
        recurrenceRule = recurrenceUpdated.recurrenceRule
    }

    private fun apply(billRecipientUpdated: BillRecipientUpdated) {
        recipient = billRecipientUpdated.recipient
    }

    private fun apply(billSchedulePostponed: BillSchedulePostponed) {
        schedule = schedule.with(billSchedulePostponed, this.billType)
    }

    @Suppress("UNUSED_PARAMETER")
    private fun apply(event: BillApproved) {
        status = BillStatus.ACTIVE
        securityValidationResult = null
    }

    @Suppress("UNUSED_PARAMETER")
    private fun apply(event: BillDenied) {
        status = BillStatus.IGNORED
    }

    private fun apply(event: BillTagAdded) {
        tags.add(event.tag)
    }

    private fun apply(event: BillTagDeleted) {
        tags.remove(event.tag)
    }

    private fun apply(event: BillCategoryAdded) {
        categoryId = if (event.category != null) event.category.categoryId else event.categoryId
    }

    private fun apply(event: BillCategoryDeleted) {
        categoryId = null
    }

    private fun apply(event: BillCategorySuggestionAdded) {
        categorySuggestions = event.categories
    }

    fun apply(
        billEvent: BillEvent,
        isNew: Boolean = true,
    ): Bill {
        when (billEvent) {
            is BillAdded -> apply(billEvent)
            is BillPaid -> apply(billEvent)
            is DescriptionUpdated -> apply(billEvent)
            is BillIgnored -> apply(billEvent)
            is RegisterUpdated -> apply(billEvent)
            is AmountUpdated -> apply(billEvent)
            is PaymentStarted -> apply(billEvent)
            is PaymentFailed -> apply(billEvent)
            is PaymentRefunded -> apply(billEvent)
            is BillReactivated -> apply(billEvent)
            is BillPaymentScheduled -> apply(billEvent)
            is BillPaymentScheduleStarted -> apply(billEvent)
            is BillPaymentScheduleCanceled -> apply(billEvent)
            is BillPaymentScheduleUpdated -> apply(billEvent)
            is RecurrenceUpdated -> apply(billEvent)
            is BillRecipientUpdated -> apply(billEvent)
            is BillSchedulePostponed -> apply(billEvent)
            is FichaCompensacaoAdded -> apply(billEvent)
            is BillMoved -> apply(billEvent)
            is BillPermissionUpdated -> apply(billEvent)
            is BillMarkedAsPaid -> apply(billEvent)
            is BillMarkedAsPaidCanceled -> apply(billEvent)
            is BillApproved -> apply(billEvent)
            is BillDenied -> apply(billEvent)
            is BillTagAdded -> apply(billEvent)
            is BillTagDeleted -> apply(billEvent)
            is BillCategoryAdded -> apply(billEvent)
            is BillCategoryDeleted -> apply(billEvent)
            is BillCategorySuggestionAdded -> apply(billEvent)
        }
        if (isNew) {
            history.add(billEvent)
        }
        return this
    }

    fun isIgnorable(): Boolean = this.status in listOf(BillStatus.NOT_PAYABLE, BillStatus.ACTIVE) && isPaymentScheduled().not()

    fun isAmountEditable(): Boolean {
        if (this.subscriptionFee) {
            return false
        }
        return when (billType) {
            BillType.INVESTMENT, INVOICE, PIX -> true
            BillType.CONCESSIONARIA,
            BillType.FICHA_COMPENSACAO,
            BillType.OTHERS,
            BillType.AUTOMATIC_PIX,
            -> false
        }
    }

    fun isMarkAsPaidCancelable() =
        this.history.none {
            it is RegisterUpdated && it.updatedRegisterData is UpdatedRegisterData.AlreadyPaidBill
        }

    fun isPaymentScheduled(): Boolean = schedule != null

    fun isRecurrent(): Boolean =
        this.source is ActionSource.Recurrence || this.source is ActionSource.WalletRecurrence

    fun isActive(): Boolean = BillStatus.ACTIVE == this.status

    fun isPaid(): Boolean = BillStatus.PAID == this.status

    fun isIgnored(): Boolean = BillStatus.IGNORED == this.status

    fun isProcessing(): Boolean = BillStatus.PROCESSING == this.status

    private fun isInvoice(): Boolean = BillType.INVOICE == this.billType

    private fun isPix(): Boolean = BillType.PIX == this.billType

    private fun isBankTransfer(): Boolean = this.billType.isBankTransfer()

    open fun getPayee(): String {
        recipient?.let {
            if (it.alias.isNotEmpty()) {
                return it.alias
            }
            return it.name
        }

        return assignor!!
    }

    fun isPaidBySchedule(): Boolean = history.filterIsInstance(BillPaid::class.java).lastOrNull()?.actionSource == ActionSource.Scheduled

    fun getLastSchedule(): BillPaymentScheduled? = history.filterIsInstance(BillPaymentScheduled::class.java).lastOrNull()

    fun isRefunded(): Boolean = history.any { it is BillPaid } && status == BillStatus.ACTIVE

    fun isOnboardingTestPix(): Boolean = isPix() && tags.contains(BillTag.ONBOARDING_TEST_PIX)

    open fun isOverdue(): Boolean {
        val time = paymentLimitTime
        val zonedDueDateTime = ZonedDateTime.of(effectiveDueDate.atTime(LocalTime.from(time)), brazilTimeZone)
        return (getZonedDateTime().isAfter(zonedDueDateTime) && BillStatus.ACTIVE == this.status)
    }

    open fun expectedPaymentDate() = schedule?.date ?: effectiveDueDate

    override fun settlementTargetId() = billId.value

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Bill

        if (billId != other.billId) return false
        return true
    }

    override fun hashCode(): Int = billId.hashCode()

    fun needsBeneficaryUpdate() = amountCalculationModel == AmountCalculationModel.BENEFICIARY_ONLY && amountTotal == 0L

    private fun waitingBeneficiaryUpdateSince() =
        this.history
            .takeLastWhile {
                it !is RegisterUpdated || it.updatedRegisterData !is UpdatedRegisterData.NewTotalAmount || it.updatedRegisterData.amountTotal == 0L
            }.firstOrNull {
                it is RegisterUpdated && it.updatedRegisterData is UpdatedRegisterData.NewTotalAmount
            }?.createdAsZonedDateTime()

    private fun waitingBeneficiaryUpdateForMoreThan5Days(date: ZonedDateTime): Boolean {
        val firstUpdatedAmountDate = waitingBeneficiaryUpdateSince()

        return firstUpdatedAmountDate != null && Duration.between(firstUpdatedAmountDate, date).toDays() > 5
    }

    private fun calcStatusOnNewTotalAmountUpdate(
        amountTotal: Long,
        created: ZonedDateTime,
    ) =
        if (amountTotal > 0) {
            if (amountCalculationModel == AmountCalculationModel.BENEFICIARY_ONLY) {
                BillStatus.ACTIVE
            } else {
                status
            }
        } else {
            if (amountCalculationModel == AmountCalculationModel.BENEFICIARY_ONLY &&
                !waitingBeneficiaryUpdateForMoreThan5Days(
                        created,
                    )
            ) {
                BillStatus.WAITING_BENEFICIARY_UPDATE
            } else {
                BillStatus.NOT_PAYABLE
            }
        }

    fun canUpdateValueTo(amount: Long): Boolean {
        if (this.billType == BillType.INVESTMENT) {
            return amount % 100 == 0L && amount >= INVESTMENT_MIN_VALUE && amount <= INVESTMENT_MAX_VALUE
        }
        return true
    }

    fun checkSelfTransfer(paymentWalletFounder: Member): Boolean {
        if (!isBankTransfer()) {
            return false
        }
        return recipient?.document == paymentWalletFounder.document
    }

    companion object {
        fun build(history: MutableList<BillEvent>): Bill = history.fold(Bill()) { aggregate, current -> aggregate.apply(current) }

        fun build(vararg history: BillEvent): Bill = build(history.toMutableList())
    }
}

fun Bill.descriptor() =
    (recipient?.name ?: assignor ?: "")
        .uppercase()
        .stripAccents()
        .replace(Regex("\\s+"), " ")
        .replace(Regex("[^A-Z0-9 ]"), "")

fun Bill.softDescriptor(size: Int = 13) = descriptor().stripSpacesAndPunctuation().take(size)

fun Bill.historySummary() =
    this.history
        .groupBy { it.eventType }
        .mapValues { (_, event) -> event.maxBy { it.created } }
        .values
        .sortedBy { it.created }

@BillEventDependency
enum class BillTag {
    ONBOARDING_TEST_PIX,
}

@BillEventDependency
enum class ExternalBillProvider { ORIGINAL, UTILITY_ACCOUNT, OPEN_FINANCE, VEHICLE_DEBTS }

@BillEventDependency
data class ExternalBillId(
    val value: String,
    val provider: ExternalBillProvider,
) {
    companion object {
        val providers = ExternalBillProvider.entries.map { it.name }

        fun of(
            value: String?,
            provider: String?,
        ) =
            when {
                value == null || provider == null || !providers.contains(provider) -> null
                else -> ExternalBillId(value, ExternalBillProvider.valueOf(provider))
            }
    }
}

data class BillSchedule(
    val batchSchedulingId: BatchSchedulingId? = null,
    val date: LocalDate,
    val ongoing: Boolean = false,
    val waitingFunds: Boolean = false,
    val waitingRetry: Boolean = false,
    val fingerprint: Fingerprint? = null,
)

fun BillSchedule.getStatus() =
    when {
        waitingFunds -> "WAITING_FUNDS"
        !ongoing && !waitingRetry && date.isAfter(LocalDate.now()) -> "SCHEDULED"
        ongoing && waitingRetry -> "ON_GOING"
        ongoing && !waitingRetry -> "ON_GOING"
        !ongoing && !waitingRetry -> "FAIL"
        else -> "UNKNOWN"
    }

private fun BillSchedule?.with(
    billEvent: BillEvent,
    billType: BillType,
): BillSchedule? =
    when (billEvent) {
        is BillSchedulePostponed -> {
            this?.let {
                val (calculatedDate, waitingFunds) =
                    when (billEvent.reason) {
                        SchedulePostponedReason.LIMIT_REACHED,
                        SchedulePostponedReason.MONTHLY_LIMIT_REACHED,
                        -> {
                            Pair(
                                calculateEffectiveDate(date.plusDays(1), billType),
                                false,
                            )
                        }

                        SchedulePostponedReason.INSUFFICIENT_FUNDS -> {
                            Pair(date, true)
                        }
                    }
                this.copy(
                    date = calculatedDate,
                    ongoing = false,
                    waitingFunds = waitingFunds,
                    waitingRetry = false,
                )
            }
        }

        is BillPaymentScheduleStarted -> this?.copy(ongoing = true, waitingFunds = false, waitingRetry = false)
        is RegisterUpdated -> this?.copy(ongoing = false)
        is BillPaid -> this?.copy(ongoing = false, waitingRetry = false)
        is PaymentFailed ->
            this?.copy(
                ongoing = billEvent.retryable,
                waitingRetry = billEvent.retryable,
                waitingFunds = false,
            )

        else -> this
    }

fun calculateEffectiveDate(
    dueDate: LocalDate,
    billType: BillType,
    barcode: BarCode? = null,
    actionSource: ActionSource? = null,
): LocalDate {
    val segmentShift = getSegmentShift(barcode, billType, actionSource)
    var currentLocalDate = dueDate

    while (!FinancialInstitutionGlobalData.isBusinessDay(currentLocalDate)) {
        currentLocalDate =
            when (segmentShift) {
                DueDateShiftMode.None -> break
                DueDateShiftMode.Increase -> currentLocalDate.plusDays(1)
                DueDateShiftMode.Decrease -> currentLocalDate.minusDays(1)
            }
    }

    return currentLocalDate
}

internal fun getSegmentShift(
    barcode: BarCode?,
    billType: BillType,
    actionSource: ActionSource? = null,
): DueDateShiftMode {
    if (barcode != null) {
        return when (getSegment(barcode)) {
            BillSegment.GOVERNMENT, BillSegment.PREFECTURE -> DueDateShiftMode.Decrease
            BillSegment.TRAFFIC_TICKET, BillSegment.CARNET, BillSegment.TELECOM, BillSegment.ELECTRICITY_AND_GAS, BillSegment.SANITATION, BillSegment.OTHERS -> DueDateShiftMode.Increase
        }
    }

    return when (billType) {
        PIX -> {
            if (actionSource is ActionSource.SubscriptionRecurrence) {
                DueDateShiftMode.Increase
            } else {
                DueDateShiftMode.None
            }
        }

        AUTOMATIC_PIX -> DueDateShiftMode.None
        BillType.CONCESSIONARIA,
        BillType.FICHA_COMPENSACAO,
        INVOICE,
        BillType.INVESTMENT,
        BillType.OTHERS,
        -> DueDateShiftMode.Increase
    }
}

internal fun getSegment(barcode: BarCode) =
    BillSegment.getSegmentByPrefix(barcode.number.take(2))

sealed class DueDateShiftMode {
    data object Decrease : DueDateShiftMode()

    data object None : DueDateShiftMode()

    data object Increase : DueDateShiftMode()
}

data class PartnerPayment(
    val partner: AccountProviderName,
    val partnerPaymentId: String?,
)