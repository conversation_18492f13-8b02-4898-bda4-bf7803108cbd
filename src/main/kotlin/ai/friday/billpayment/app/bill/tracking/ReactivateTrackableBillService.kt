package ai.friday.billpayment.app.bill.tracking

import ai.friday.billpayment.Err
import ai.friday.billpayment.ServerError
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.FichaCompensacaoAdded
import ai.friday.billpayment.app.bill.UpdateBillService
import arrow.core.Either
import arrow.core.left
import arrow.core.right

interface ReactivateTrackableBillService {
    fun reactivate(event: BillEvent): Either<Err, Unit>
}

@FridayMePoupe
class DefaultReactivateTrackableBillService(
    private val service: UpdateBillService,
    private val createService: CreateTrackableBillService,
) : ReactivateTrackableBillService {
    override fun reactivate(event: BillEvent): Either<Err, Unit> {
        return try {
            val bill = service.getBill(billId = event.billId)
            val billAdded = bill.history.first { it.eventType == BillEventType.ADD }

            if (billAdded !is FichaCompensacaoAdded) return Unit.right()

            createService.create(billAdded)
        } catch (ex: Exception) {
            ServerError(ex).left()
        }
    }
}