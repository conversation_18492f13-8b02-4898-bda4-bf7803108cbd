package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.hasEarlyAccess
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.BillValidationResponse
import jakarta.inject.Singleton

interface BlockEmptyAmountCreationService {
    fun check(billValidationResponse: BillValidationResponse, addFichaDeCompensacaoRequest: CreateFichaDeCompensacaoRequest): Boolean
}

@Singleton
open class DefaultBlockEmptyAmountCreationService() : BlockEmptyAmountCreationService {
    override fun check(billValidationResponse: BillValidationResponse, addFichaDeCompensacaoRequest: CreateFichaDeCompensacaoRequest): Boolean {
        return billValidationResponse.billRegisterData?.amountCalculationModel != AmountCalculationModel.BENEFICIARY_ONLY && (addFichaDeCompensacaoRequest.source is ActionSource.Api || !AccountId(addFichaDeCompensacaoRequest.walletId.value).hasEarlyAccess())
    }
}