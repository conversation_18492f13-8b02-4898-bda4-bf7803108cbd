package ai.friday.billpayment.app.bill

import ai.friday.billpayment.PrintableSealedClass

data class SynchronizeBillResponse(
    val status: BillSynchronizationStatus,
    val syncErrorMessages: SyncErrorMessages? = null,
) {
    fun alreadyInSync(): Boolean = when (status) {
        BillSynchronizationStatus.BillAmountCalculationValidUntilUpdated,
        BillSynchronizationStatus.BillNotModified,
        -> true

        BillSynchronizationStatus.BillAmountTotalUpdatedToLess,
        BillSynchronizationStatus.BillAmountTotalUpdated,
        is BillSynchronizationStatus.BillIsNotActive,
        is BillSynchronizationStatus.BillStatusUpdated,
        is BillSynchronizationStatus.UnableToValidate,
        -> false
    }

    fun unableToValidate(): Boolean = status is BillSynchronizationStatus.UnableToValidate
}

sealed class SyncErrorMessages(val message: String) {
    data object ZeroAmount : SyncErrorMessages("o boleto teve seu valor atualizado para zero")
    data object DivergentAmount : SyncErrorMessages("há divergência no valor de juros e multa")
    data object UnableToValidate : SyncErrorMessages("não foi possível validar a conta. Por favor, tente novamente")
    data object OldRegistrationUpdateNumber : SyncErrorMessages("A resposta da validação trouxe informações antigas do pagamento")
    class GenericSyncErrorMessage(message: String) : SyncErrorMessages(message)
}

sealed class BillSynchronizationStatus : PrintableSealedClass() {
    data object BillNotModified : BillSynchronizationStatus()
    data class BillIsNotActive(val billStatus: BillStatus) : BillSynchronizationStatus()
    data class BillStatusUpdated(val billStatus: BillStatus) : BillSynchronizationStatus()
    data object BillAmountTotalUpdated : BillSynchronizationStatus()
    data object BillAmountTotalUpdatedToLess : BillSynchronizationStatus()
    data object BillAmountCalculationValidUntilUpdated : BillSynchronizationStatus()
    data class UnableToValidate(val isRetryable: Boolean = true) : BillSynchronizationStatus()
}