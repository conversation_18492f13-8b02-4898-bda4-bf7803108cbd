package ai.friday.billpayment.app.bill.mailbox.rules

import ai.friday.billpayment.app.bill.MailboxAddBillError
import ai.friday.billpayment.app.bill.mailbox.BLOCK_PRIORITY
import ai.friday.billpayment.app.bill.mailbox.MailboxSecurityRule
import ai.friday.billpayment.app.bill.mailbox.MailboxSecurityValidationErrorLevel
import ai.friday.billpayment.app.bill.mailbox.MailboxValidateRequest
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Requires
import io.via1.communicationcentre.app.receipt.Status
import jakarta.inject.Singleton

@Singleton
@Requires(property = "mailbox.security.rule.spf.enabled", value = "true", defaultValue = "false")
class SPFMailboxRule : MailboxSecurityRule {
    override fun getPriority() = BLOCK_PRIORITY + 2

    override fun execute(request: MailboxValidateRequest): Either<MailboxAddBillError.MailboxSecurityValidationError, Unit> {
        if (request.receipt == null) return Unit.right()

        if (request.receipt.spfVerdict.status == Status.PASS) return Unit.right()

        return MailboxAddBillError.MailboxSecurityValidationError(
            reason = "SPFFail",
            level = MailboxSecurityValidationErrorLevel.BLOCK,
        ).left()
    }
}