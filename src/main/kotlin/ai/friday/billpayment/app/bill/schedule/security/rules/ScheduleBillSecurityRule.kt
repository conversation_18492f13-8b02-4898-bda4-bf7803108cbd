package ai.friday.billpayment.app.bill.schedule.security.rules

import ai.friday.billpayment.app.bill.schedule.security.ScheduleBillSecurityError
import ai.friday.billpayment.app.bill.schedule.security.ScheduleBillSecurityRequest
import arrow.core.Either

interface ScheduleBillSecurityRule {

    fun getPriority(): Int
    fun execute(request: ScheduleBillSecurityRequest): Either<ScheduleBillSecurityError, Unit>
}