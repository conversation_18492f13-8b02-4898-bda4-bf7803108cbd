package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId

sealed interface UpdateBillStateRequest {
    val billId: BillId
}

data class ApproveUpdateBillRequest(
    override val billId: BillId,
    val walletId: WalletId,
    val member: Member,
    val alwaysAllowSender: <PERSON><PERSON><PERSON>,
    val actionSource: ActionSource.Api,
) : UpdateBillStateRequest

data class DenyUpdateBillRequest(
    override val billId: BillId,
    val walletId: WalletId,
    val member: Member,
    val actionSource: ActionSource.Api,
) : UpdateBillStateRequest