package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.PaymentMethodType
import java.time.LocalDate

class InvalidBillException(val result: CreateBillResult.FAILURE) : RuntimeException()

class NewerBillExistsException(newerBillId: BillId, billId: BillId) :
    RuntimeException("A newer bill ${newerBillId.value} exists and bill ${billId.value} cannot reactivate")

class MemberNotAllowedException(accountId: AccountId, billId: BillId) :
    RuntimeException("Wallet member ${accountId.value} does not have permission to view/edit/schedule bill ${billId.value}.")

class BillAlreadyLockedException(billId: BillId) : RuntimeException("Bill ${billId.value} is already locked.")

class InvalidBillStateChangeException(billId: BillId, currentStatus: BillStatus) :
    RuntimeException("Bill ${billId.value} in status ${currentStatus.name} can't be changed.")

class InvalidSchedulePaymentAmount(billAmount: Long, scheduledNetAmount: Long) :
    RuntimeException("Scheduled bill amount is invalid. Bill total amount: $billAmount - scheduledNetAmount: $scheduledNetAmount")

class InvalidScheduleBillType(billType: BillType) :
    RuntimeException("Essa bill não pode ser agendada. billType: $billType")

class InvalidSchedulePaymentMethod(paymentMethodType: PaymentMethodType) :
    RuntimeException("Bill can't be paid with current paymentMethodType. paymentMethodType: $paymentMethodType")

class InvalidSchedulePaymentMethodDate(scheduleDate: LocalDate) :
    RuntimeException("Current payment must scheduled to be paid immediately. scheduled date: $scheduleDate")

class InvalidSchedulePaymentFee(message: String) : RuntimeException(message)

class BillValidationException(message: String) : RuntimeException(message)

class InvalidInstallmentCalculation(message: String) : RuntimeException(message)

class BillAmountIsNotEditable(billId: BillId) : RuntimeException("Bill ${billId.value} amount is not editable.")

class BillNotIgnorableException(val bill: Bill) : RuntimeException("Bill ${bill.billId.value} is not ignorable.")