package ai.friday.billpayment.app.bill.duplication

import ai.friday.billpayment.app.bill.BillView

interface PossibleDuplicationRule {
    fun accept(first: SimilarityWrapper, second: SimilarityWrapper): Boolean
    fun check(first: SimilarityWrapper, second: SimilarityWrapper): PossibleDuplicationResult
    fun priority(): Int

    fun accept(first: <PERSON><PERSON><PERSON><PERSON>, second: <PERSON><PERSON><PERSON><PERSON>): Boolean =
        accept(SimilarityWrapper(first), SimilarityWrapper(second))

    fun check(first: <PERSON><PERSON><PERSON><PERSON>, second: <PERSON><PERSON>ie<PERSON>): PossibleDuplicationResult =
        check(SimilarityWrapper(first), SimilarityWrapper(second))
}