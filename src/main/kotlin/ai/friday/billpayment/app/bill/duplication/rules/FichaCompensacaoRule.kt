package ai.friday.billpayment.app.bill.duplication.rules

import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.duplication.PossibleDuplicationResult
import ai.friday.billpayment.app.bill.duplication.PossibleDuplicationRule
import ai.friday.billpayment.app.bill.duplication.SimilarityWrapper
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import jakarta.inject.Singleton

@Singleton
class FichaCompensacaoRule : PossibleDuplicationRule {
    override fun priority(): Int = 1

    override fun accept(first: SimilarityWrapper, second: SimilarityWrapper): Boolean =
        first.billType == BillType.FICHA_COMPENSACAO &&
            second.billType == BillType.FICHA_COMPENSACAO

    override fun check(first: SimilarityWrapper, second: SimilarityWrapper): PossibleDuplicationResult {
        if (first.fichaCompensacaoType == FichaCompensacaoType.CARTAO_DE_CREDITO &&
            second.fichaCompensacaoType == FichaCompensacaoType.CARTAO_DE_CREDITO
        ) {
            if (first.effectiveDueDate != second.effectiveDueDate) {
                return PossibleDuplicationResult.NotDuplicated
            }
        }

        if (first.barCode != null && first.barCode.digitable == second.barCode?.digitable) {
            return PossibleDuplicationResult.BilateralDuplication(first, second)
        }

        if (first.idNumber != null && first.idNumber == second.idNumber) {
            return PossibleDuplicationResult.BilateralDuplication(first, second)
        }

        return PossibleDuplicationResult.NotDuplicated
    }
}