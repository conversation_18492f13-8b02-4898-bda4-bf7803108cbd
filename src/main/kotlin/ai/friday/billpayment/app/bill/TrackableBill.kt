package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.DiscountData
import ai.friday.billpayment.app.payment.FineData
import ai.friday.billpayment.app.payment.InterestData
import ai.friday.billpayment.app.payment.isSupported
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import com.fasterxml.jackson.annotation.JsonAlias
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZonedDateTime

data class TrackableBill(
    val billId: BillId,
    val created: ZonedDateTime,
    val lastUpdated: ZonedDateTime = getZonedDateTime(),
    val isActive: Boolean,
    val barCode: BarCode,
    @JsonAlias("isDda")
    val addedByDda: Boolean,
    val billType: BillType,
    val dueDate: LocalDate,
    val amount: Long,
    val amountCalculationModel: AmountCalculationModel? = null,
    val fineData: FineData,
    val interestData: InterestData,
    val discountData: DiscountData,
    val abatement: BigDecimal,
)

fun TrackableBill.calculateOrQuery() = when {
    !addedByDda -> BillTrackingCalculateOptions.QUERY
    interestData.type != null && !interestData.type.isSupported() -> BillTrackingCalculateOptions.QUERY
    fineData.type != null && !fineData.type.isSupported() -> BillTrackingCalculateOptions.QUERY
    discountData.type != null && !discountData.type.isSupported() -> BillTrackingCalculateOptions.QUERY
    amountCalculationModel == AmountCalculationModel.ANYONE -> BillTrackingCalculateOptions.CALCULATE
    amountCalculationModel == AmountCalculationModel.BENEFICIARY_AFTER_DUE_DATE && dueDate >= getZonedDateTime().toLocalDate()
    -> BillTrackingCalculateOptions.CALCULATE

    else -> BillTrackingCalculateOptions.QUERY
}

enum class BillTrackingCalculateOptions {
    CALCULATE, QUERY
}