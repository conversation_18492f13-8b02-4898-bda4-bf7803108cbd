package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.Acquirer
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.billcategory.BillCategorySuggestion
import ai.friday.billpayment.app.contact.ContactId
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.DiscountData
import ai.friday.billpayment.app.payment.DivergentPayment
import ai.friday.billpayment.app.payment.ErrorSource
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.payment.FineData
import ai.friday.billpayment.app.payment.InterestData
import ai.friday.billpayment.app.payment.MultiplePaymentMethodsDetail
import ai.friday.billpayment.app.payment.PartialPayment
import ai.friday.billpayment.app.payment.PaymentMethodsDetail
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithExternalPayment
import ai.friday.billpayment.app.payment.RecipientChain
import ai.friday.billpayment.app.payment.SinglePaymentMethodsDetail
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.pfm.BillCategory
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.security.Fingerprint
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import java.util.UUID

sealed class BillEvent {
    abstract val billId: BillId
    abstract val created: Long
    abstract val eventType: BillEventType
    abstract val walletId: WalletId
    abstract val actionSource: ActionSource

    // FIXME - o created já poderia ser ZonedDateTime no dominio
    fun createdAsZonedDateTime(): ZonedDateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(created), brazilTimeZone)
}

data class BillAdded(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    val description: String,
    val dueDate: LocalDate,
    val amount: Long,
    val billType: BillType,
    val amountTotal: Long = amount,
    val discount: Long = 0,
    val fine: Long = 0,
    val interest: Long = 0,
    val barcode: BarCode? = null,
    val recipient: Recipient? = null,
    val contactId: ContactId? = null,
    val assignor: String? = null,
    val document: String? = null,
    val payerName: String? = null,
    val payerAlias: String? = null,
    val paymentLimitTime: LocalTime,
    val lastSettleDate: String? = null,
    val expirationDate: String? = null,
    val amountCalculationModel: AmountCalculationModel? = null,
    override val actionSource: ActionSource,
    val recurrenceRule: RecurrenceRule? = null,
    val effectiveDueDate: LocalDate? = null,
    val subscriptionFee: Boolean = false,
    val securityValidationResult: List<String>? = null,
    val externalId: ExternalBillId? = null,
    val requestedDueDate: LocalDate? = null,
    val pixQrCodeData: PixQrCodeData? = null,
    val automaticPixAuthorizationMaximumAmount: Long? = null,
    val automaticPixData: AutomaticPixData? = null,
    val categoryId: PFMCategoryId? = null,
    val brand: String? = null,
    val goalId: GoalId? = null,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.ADD
}

data class FichaCompensacaoAdded(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    val description: String,
    val dueDate: LocalDate,
    val amount: Long,
    val amountTotal: Long = amount,
    val discount: Long = 0,
    val fine: Long = 0,
    val interest: Long = 0,
    val barcode: BarCode,
    val recipient: Recipient,
    val recipientChain: RecipientChain?,
    val assignor: String,
    val document: String,
    val payerName: String? = null,
    val payerAlias: String? = null,
    val paymentLimitTime: LocalTime,
    val lastSettleDate: String? = null,
    val expirationDate: String,
    val amountCalculationModel: AmountCalculationModel,
    val interestData: InterestData?,
    val fineData: FineData?,
    val discountData: DiscountData?,
    val abatement: BigDecimal?,
    override val actionSource: ActionSource,
    val effectiveDueDate: LocalDate,
    val fichaCompensacaoType: FichaCompensacaoType,
    val idNumber: String? = null,
    val registrationUpdateNumber: Long? = null,
    val subscriptionFee: Boolean = false,
    val securityValidationResult: List<String>? = null,
    val brand: String? = null,
    val divergentPayment: DivergentPayment? = null,
    val partialPayment: PartialPayment? = null,
    val externalId: ExternalBillId? = null,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.ADD

    fun needsBeneficiaryUpdate() =
        amountCalculationModel == AmountCalculationModel.BENEFICIARY_ONLY && amountTotal == 0L
}

data class PaymentStarted(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    val transactionId: TransactionId,
    val correlationId: String,
    override val actionSource: ActionSource,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.PAYMENT_START
}

data class PaymentFailed(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    val retryable: Boolean,
    val errorSource: ErrorSource = ErrorSource.UNKNOWN,
    val errorDescription: String,
    val transactionId: TransactionId,
    override val actionSource: ActionSource = ActionSource.System,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.PAYMENT_FAIL
}

data class PaymentRefunded(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    val gateway: FinancialServiceGateway,
    val reason: PaymentRefundedReason,
    val transactionId: TransactionId,
    override val actionSource: ActionSource = ActionSource.System,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.PAYMENT_REFUNDED
}

data class DescriptionUpdated(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    val description: String,
    override val actionSource: ActionSource,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.UPDATE_DESCRIPTION
}

data class BillPaid(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    val acquirer: Acquirer? = null,
    val acquirerTid: String? = null,
    val transactionId: TransactionId?, // TODO remove nullable after fill every event in database
    override val actionSource: ActionSource,
    val pixKeyDetails: PixKeyDetails? = null,
    val syncReceipt: Boolean = false,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.PAID
}

data class BillReactivated(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.REACTIVATED
}

data class BillMoved(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    val destinationWalletId: WalletId,
    val barCode: BarCode?,
    val dueDate: LocalDate?,
    val idNumber: String?,
    override val actionSource: ActionSource,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.MOVED
}

data class BillPaymentScheduled(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val scheduledDate: LocalDate,
    val amount: Long,
    val paymentLimitTime: LocalTime?,
    val infoData: BillPaymentScheduledInfo,
    val batchSchedulingId: BatchSchedulingId?,
    val fingerprint: Fingerprint? = null,
    val paymentWalletId: WalletId? = null,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.PAYMENT_SCHEDULED
}

data class BillPaymentScheduleUpdated(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource = ActionSource.System,
    val updatedScheduleData: UpdatedScheduleData,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.PAYMENT_SCHEDULE_UPDATED
}

@BillEventDependency
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME)
sealed class UpdatedScheduleData {
    @JsonTypeName("NewCalculationId")
    class NewCalculationId(val calculationId: String) : UpdatedScheduleData()
}

data class BillTagAdded(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val tag: BillTag,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.TAG_ADDED
}

data class BillTagDeleted(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val tag: BillTag,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.TAG_DELETED
}

data class BillCategoryAdded(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val categoryId: PFMCategoryId?, // TODO: não deve ser nulavel depois do script
    val category: BillCategory?, // TODO: deve ser removido depois do script
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.CATEGORY_ADDED
}

data class BillCategoryDeleted(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.CATEGORY_DELETED
}

data class BillCategorySuggestionAdded(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource = ActionSource.System,
    val categories: List<BillCategorySuggestion>,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.CATEGORY_SUGGESTION_ADDED
}

@BillEventDependency
data class BatchSchedulingId(val value: String) {
    constructor() : this("BATCH-SCHEDULING-${UUID.randomUUID()}")
}

sealed interface BillPaymentScheduledInfo {

    @Deprecated("remover quando parar de usar no repository.")
    fun retrievePaymentMethodIds(): List<AccountPaymentMethodId>

    fun toSchedulePaymentDetails(): PaymentMethodsDetail

    sealed interface SingleBillPaymentScheduledInfo : BillPaymentScheduledInfo {
        override fun toSchedulePaymentDetails(): SinglePaymentMethodsDetail
    }

    sealed interface MultipleBillPaymentScheduledInfo : BillPaymentScheduledInfo {
        override fun toSchedulePaymentDetails(): MultiplePaymentMethodsDetail
    }
}

data class BillPaymentScheduledCreditCardInfo(
    val paymentMethodId: String,
    val netAmount: Long,
    val feeAmount: Long,
    val installments: Int,
    val fee: Double,
    val calculationId: String?,
) : BillPaymentScheduledInfo.SingleBillPaymentScheduledInfo {
    override fun retrievePaymentMethodIds() = listOf(AccountPaymentMethodId(paymentMethodId))

    override fun toSchedulePaymentDetails(): SinglePaymentMethodsDetail {
        return PaymentMethodsDetailWithCreditCard(
            paymentMethodId = AccountPaymentMethodId(paymentMethodId),
            netAmount = netAmount,
            feeAmount = feeAmount,
            installments = installments,
            calculationId = calculationId,
            fee = fee,
        )
    }
}

data class BillPaymentScheduledBalanceInfo(
    val amount: Long,
    val paymentMethodId: String,
    val calculationId: String?,
) : BillPaymentScheduledInfo.SingleBillPaymentScheduledInfo {
    override fun retrievePaymentMethodIds() = listOf(AccountPaymentMethodId(paymentMethodId))
    override fun toSchedulePaymentDetails(): SinglePaymentMethodsDetail {
        return PaymentMethodsDetailWithBalance(
            paymentMethodId = AccountPaymentMethodId(value = paymentMethodId),
            amount = amount,
            calculationId = calculationId,
        )
    }
}

// FIXME - talvez fosse usado somente para o PicPay, confirmar e remover
data class BillPaymentScheduledExternalPayment(
    val providerName: String,
) : BillPaymentScheduledInfo.SingleBillPaymentScheduledInfo {
    override fun retrievePaymentMethodIds() = emptyList<AccountPaymentMethodId>()

    override fun toSchedulePaymentDetails(): SinglePaymentMethodsDetail {
        return PaymentMethodsDetailWithExternalPayment(providerName = AccountProviderName.valueOf(providerName))
    }
}

data class BillPaymentScheduledWithMultiple(
    val methods: List<BillPaymentScheduledInfo.SingleBillPaymentScheduledInfo>,
) : BillPaymentScheduledInfo.MultipleBillPaymentScheduledInfo {
    override fun retrievePaymentMethodIds() = methods.map { it.retrievePaymentMethodIds() }.flatten()
    override fun toSchedulePaymentDetails(): MultiplePaymentMethodsDetail {
        return MultiplePaymentMethodsDetail(
            methods = methods.map {
                it.toSchedulePaymentDetails()
            },
        )
    }
}

data class BillPaymentScheduleCanceled(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    val reason: ScheduleCanceledReason,
    override val actionSource: ActionSource,
    val batchSchedulingId: BatchSchedulingId?,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.PAYMENT_SCHEDULE_CANCELED
}

data class BillIgnored(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val removeFromListing: Boolean = false,
    val removeFromRecurrence: Boolean = false,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.IGNORED
}

data class BillPaymentScheduleStarted(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val paymentWalletId: WalletId,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.PAYMENT_SCHEDULE_STARTED
}

data class BillSchedulePostponed(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val reason: SchedulePostponedReason,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.SCHEDULE_POSTPONED
}

data class RegisterUpdated(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    val updatedRegisterData: UpdatedRegisterData,
    override val actionSource: ActionSource = ActionSource.System,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.REGISTER_UPDATED
}

data class RecurrenceUpdated(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val recurrenceRule: RecurrenceRule,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.RECURRENCE_UPDATED
}

data class BillRecipientUpdated(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val recipient: Recipient,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.RECIPIENT_UPDATED
}

data class BillPermissionUpdated(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val permissionUpdated: PermissionUpdated,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.PERMISSION_UPDATED
}

data class BillMarkedAsPaid(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource.Api,
    val amountPaid: Long?,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.MARKED_AS_PAID
}

data class BillApproved(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource.Api,
) : BillEvent() {
    override val eventType = BillEventType.APPROVED
}

data class BillDenied(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource.Api,
) : BillEvent() {
    override val eventType = BillEventType.DENIED
}

data class Payment(val amount: Long, val date: ZonedDateTime, val paymentMethod: String)

data class BillMarkedAsPaidCanceled(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
) : BillEvent() {
    override val eventType: BillEventType =
        BillEventType.CANCELED_MARKED_AS_PAID
}

data class AmountUpdated(
    override val billId: BillId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    override val walletId: WalletId,
    override val actionSource: ActionSource,
    val amount: Long,
) : BillEvent() {
    override val eventType: BillEventType = BillEventType.AMOUNT_UPDATED
}

@BillEventDependency
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME)
sealed class PermissionUpdated {
    @JsonTypeName("VisibilityAdded")
    class VisibilityAdded(val accountId: AccountId) : PermissionUpdated()
}

@BillEventDependency
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME)
sealed class UpdatedRegisterData {
    @JsonTypeName("AlreadyPaid")
    class AlreadyPaidBill(
        val amountPaid: Long? = null,
    ) : UpdatedRegisterData()

    @JsonTypeName("NotPayable")
    data object NotPayableBill : UpdatedRegisterData()

    @JsonTypeName("NewPaymentData")
    class NewAmountCalculationData(
        val fineData: FineData? = null,
        val interestData: InterestData? = null,
        val discountData: DiscountData? = null,
    ) : UpdatedRegisterData()

    @JsonTypeName("NewTotalAmount")
    class NewTotalAmount(
        val amount: Long,
        val abatement: Long,
        val discount: Long,
        val interest: Long,
        val fine: Long,
        val amountTotal: Long,
        val lastSettleDate: String,
        val registrationUpdateNumber: Long?,
        val source: FinancialServiceGateway?,
        val amountCalculationValidUntil: LocalDate? = null,
    ) : UpdatedRegisterData()

    @JsonTypeName("NewAmountCalculationValidUntil")
    class NewAmountCalculationValidUntil(
        val amountCalculationValidUntil: LocalDate? = null,
    ) : UpdatedRegisterData()

    @JsonTypeName("NewDueDate")
    class NewDueDate(
        val dueDate: LocalDate,
        val effectiveDueDate: LocalDate,
    ) : UpdatedRegisterData()

    @JsonTypeName("NewAmountCalculationOption")
    class NewAmountCalculationOption(
        val amountCalculationOption: BillTrackingCalculateOptions,
    ) : UpdatedRegisterData()

    @JsonTypeName("VehicleDebtEnrichment")
    class VehicleDebtEnrichment(
        val externalId: ExternalBillId?,
        val details: String?,
    ) : UpdatedRegisterData()
}

@BillEventDependency
enum class BillEventType {
    ADD,
    UPDATE_DESCRIPTION,
    AMOUNT_UPDATED,
    PAID,
    IGNORED,
    REGISTER_UPDATED,
    PAYMENT_START,
    PAYMENT_FAIL,
    PAYMENT_REFUNDED,
    REACTIVATED,
    PAYMENT_SCHEDULED,
    PAYMENT_SCHEDULE_CANCELED,
    PAYMENT_SCHEDULE_STARTED,
    PAYMENT_SCHEDULE_UPDATED,
    RECURRENCE_UPDATED,
    RECIPIENT_UPDATED,
    SCHEDULE_POSTPONED,
    TAG_ADDED,
    CREDIT_CARD_ENRICHMENT,
    TAG_DELETED,
    CATEGORY_ADDED,
    CATEGORY_DELETED,
    CATEGORY_SUGGESTION_ADDED,
    MOVED,
    PERMISSION_UPDATED,
    MARKED_AS_PAID,
    CANCELED_MARKED_AS_PAID,
    APPROVED,
    DENIED,
}

@BillEventDependency
enum class ScheduleCanceledReason {
    USER_REQUEST, EXPIRATION, STATUS_CHANGED, BILL_ALREADY_PAID, BILL_NOT_PAYABLE, EXECUTED, AMOUNT_HIGHER_THAN_DAILY_LIMIT, SUBSCRIPTION_CANCELED, BILL_AMOUNT_CHANGED, CANT_PAY_WITH_CURRENT_CREDIT_CARD, NOT_SELECTED_ON_ONE_PIX_PAY
}

@BillEventDependency
enum class SchedulePostponedReason {
    INSUFFICIENT_FUNDS, LIMIT_REACHED, MONTHLY_LIMIT_REACHED
}

@BillEventDependency
enum class PaymentRefundedReason {
    DESTINATION_NOT_ALLOWED_AMOUNT, INVALID_DATA, ERROR
}

// WARNING classes with this annotation are serialized with BillEvents. Handle with care
annotation class BillEventDependency

fun BillPaymentScheduled.internalSettlement(): Boolean =
    infoData.toSchedulePaymentDetails().internalSettlement()