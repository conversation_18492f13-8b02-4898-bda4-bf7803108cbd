package ai.friday.billpayment.app.bill.duplication

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.getOrFalse
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.recurrence.BillRecurrence
import io.micronaut.context.event.ApplicationEventListener
import io.micronaut.discovery.event.ServiceReadyEvent
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
open class PossibleDuplicateBillService(
    private val billRepository: BillRepository,
    duplicationRules: List<PossibleDuplicationRule>,
) : ApplicationEventListener<ServiceReadyEvent> {
    private val rules = duplicationRules.sortedBy { it.priority() }

    open fun check(bill: Bill): List<PossibleDuplicate> {
        val walletId = bill.walletId

        return billRepository.findByWallet(walletId)
            .filter { it.billId != bill.billId }
            .filter { it.status != BillStatus.IGNORED }
            .filter { check(SimilarityWrapper(it), SimilarityWrapper(bill)) }
            .map { PossibleDuplicate(it.billId, it.effectiveDueDate) }
    }

    open fun check(recurrence: BillRecurrence): List<PossibleDuplicate> {
        return billRepository.findByWallet(recurrence.walletId)
            .filter { check(SimilarityWrapper(it), SimilarityWrapper(recurrence)) }
            .map { PossibleDuplicate(it.billId, it.effectiveDueDate) }
    }

    open fun check(bills: List<BillView>): Map<BillView, Set<PossibleDuplicate>> {
        val resultMap = mutableMapOf<BillView, Set<PossibleDuplicate>>()

        bills.groupBy { it.effectiveDueDate }.values.forEach { billsOnDate ->
            billsOnDate.forEachIndexed { index, outterBill ->
                billsOnDate.subList(index + 1, billsOnDate.size).forEach { innerBill ->
                    val result = rules
                        .firstOrNull { it.accept(outterBill, innerBill) }
                        ?.check(outterBill, innerBill) ?: PossibleDuplicationResult.NotDuplicated

                    when (result) {
                        PossibleDuplicationResult.NotDuplicated -> {}

                        is PossibleDuplicationResult.UnilateralDuplication -> {
                            val duplication = PossibleDuplicate(
                                result.duplicationOf.billId!!,
                                result.duplicationOf.effectiveDueDate,
                            )
                            val duplicated =
                                listOf(outterBill, innerBill).first { it.billId == result.duplication.billId }
                            resultMap[duplicated] = ((resultMap[duplicated] ?: emptySet()) + duplication)
                        }

                        is PossibleDuplicationResult.BilateralDuplication -> {
                            listOf(
                                Pair(outterBill, innerBill),
                                Pair(innerBill, outterBill),
                            ).forEach { pair ->
                                val duplication = PossibleDuplicate(
                                    pair.first.billId,
                                    pair.first.effectiveDueDate,
                                )
                                resultMap[pair.second] = ((resultMap[pair.second] ?: emptySet()) + duplication)
                            }
                        }
                    }
                }
            }
        }
        return resultMap
    }

    open fun check(first: BillView, second: BillView): Boolean =
        check(SimilarityWrapper(first), SimilarityWrapper(second))

    open fun check(first: SimilarityWrapper, second: SimilarityWrapper): Boolean {
        return rules
            .firstOrNull { it.accept(first, second) }
            ?.check(first, second)
            ?.isDuplicated().getOrFalse()
    }

    override fun onApplicationEvent(event: ServiceReadyEvent) =
        LoggerFactory.getLogger(PossibleDuplicateBillService::class.java).info(
            Markers.append("rule", rules.map { it::class.simpleName }),
            "PossibleDuplicateBillServiceOnReadyEvent",
        )
}