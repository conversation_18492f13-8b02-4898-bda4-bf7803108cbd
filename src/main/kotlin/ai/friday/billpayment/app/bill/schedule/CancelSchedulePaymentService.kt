package ai.friday.billpayment.app.bill.schedule

import ai.friday.billpayment.and
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillAlreadyLockedException
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.bill.billLockProvider
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.payment.BillProcessingException
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.canView
import ai.friday.billpayment.log
import arrow.core.getOrElse
import jakarta.inject.Named
import org.slf4j.LoggerFactory

interface CancelSchedulePaymentService {
    fun cancelScheduledPayment(walletId: WalletId, billId: BillId, reason: ScheduleCanceledReason): Boolean
    fun userCancelScheduledPayment(
        walletId: WalletId,
        billId: BillId,
        member: Member,
        actionSource: ActionSource,
    ): Boolean
}

@FridayMePoupe
class DefaultCancelSchedulePaymentService(
    private val billEventRepository: BillEventRepository,
    private val billEventPublisher: BillEventPublisher,
    @Named(billLockProvider) private val lockProvider: InternalLock,
) : CancelSchedulePaymentService {
    private val logger = LoggerFactory.getLogger(DefaultCancelSchedulePaymentService::class.java)

    override fun cancelScheduledPayment(walletId: WalletId, billId: BillId, reason: ScheduleCanceledReason): Boolean {
        if (reason == ScheduleCanceledReason.USER_REQUEST) {
            throw IllegalArgumentException("Illegal reason ${reason.name}")
        }

        return doCancelScheduledPayment(
            billId = billId,
            member = null,
            actionSource = ActionSource.System,
            reason = reason,
        )
    }

    override fun userCancelScheduledPayment(
        walletId: WalletId,
        billId: BillId,
        member: Member,
        actionSource: ActionSource,
    ): Boolean {
        val result = doCancelScheduledPayment(
            billId = billId,
            member = member,
            actionSource = actionSource,
            reason = ScheduleCanceledReason.USER_REQUEST,
        )

        return result
    }

    private fun doCancelScheduledPayment(
        billId: BillId,
        member: Member?,
        actionSource: ActionSource,
        reason: ScheduleCanceledReason,
    ): Boolean {
        val markers = log("billId" to billId.value)

        val lock = lockProvider.acquireLock(billId.value) ?: throw BillAlreadyLockedException(billId)

        try {
            val result = billEventRepository.getBillById(billId)
            val bill = result.getOrElse { throw ItemNotFoundException(billId) }
            if (bill.isProcessing()) {
                throw BillProcessingException(bill.billId)
            }

            if (!bill.isPaymentScheduled()) {
                return false
            }
            if (bill.subscriptionFee) {
                logger.error(markers.and("subscription" to true), "cancelSchedulePaymentNotAllowed")
                return true
            }
            if (member != null && !member.canView(bill)) {
                logger.error(markers.and("accountId" to member.accountId.value), "cancelSchedulePaymentNotAllowed")
                return true
            }

            val event = BillPaymentScheduleCanceled(
                billId = billId,
                walletId = bill.walletId,
                reason = reason,
                actionSource = actionSource,
                batchSchedulingId = bill.schedule!!.batchSchedulingId,
            )

            billEventPublisher.publish(bill, event)

            return false
        } finally {
            lock.unlock()
        }
    }
}