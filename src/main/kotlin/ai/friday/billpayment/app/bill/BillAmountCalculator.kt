package ai.friday.billpayment.app.bill

import ai.friday.billpayment.Err
import ai.friday.billpayment.ServerError
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.BillChargesUtils
import ai.friday.billpayment.app.payment.DiscountData
import ai.friday.billpayment.app.payment.DiscountType
import ai.friday.billpayment.app.payment.FineData
import ai.friday.billpayment.app.payment.FineType
import ai.friday.billpayment.app.payment.InterestData
import ai.friday.billpayment.app.payment.InterestType
import ai.friday.billpayment.app.payment.hasDiscount
import ai.friday.billpayment.app.payment.hasFine
import ai.friday.billpayment.app.payment.hasInterest
import ai.friday.billpayment.app.payment.isSupported
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import java.time.temporal.ChronoUnit

@FridayMePoupe
class BillAmountCalculator {

    fun calculate(
        amount: Long,
        dueDate: LocalDate,
        processingDate: LocalDate = getZonedDateTime().toLocalDate(),
        fineData: FineData? = null, // multa
        interestData: InterestData? = null, // juros
        discountData: DiscountData? = null, // desconto
        abatement: Double? = null,
    ): Either<Err, CalculatedAmount> {
        try {
            val convertedAbatement = if (abatement != null) {
                (abatement * 100).toLong()
            } else {
                0L
            }
            val updatedAmount = amount - convertedAbatement

            val actualProcessingDate = BillChargesUtils.calculateClosestWorkingDay(processingDate)

            val fineAmount = calculateFine(
                fineData = fineData,
                amount = updatedAmount,
                dueDate = dueDate,
                processingDate = actualProcessingDate,
            ).getOrElse { return it.left() }
            val interestAmount = calculateInterest(
                interestData = interestData,
                amount = updatedAmount,
                dueDate = dueDate,
                processingDate = actualProcessingDate,
            ).getOrElse { return it.left() }
            val discountAmount = calculateDiscount(
                discountData = discountData,
                amount = updatedAmount,
                dueDate = dueDate,
                processingDate = actualProcessingDate,
            ).getOrElse { return it.left() }

            return CalculatedAmount(
                originalAmount = amount,
                discount = discountAmount,
                interest = interestAmount,
                fine = fineAmount,
                abatement = convertedAbatement,
                totalAmount = updatedAmount + fineAmount + interestAmount - discountAmount,
                validUntil = getValidUntil(
                    discountData = discountData,
                    dueDate = dueDate,
                    actualProcessingDate = actualProcessingDate,
                    fineData = fineData,
                    interestData = interestData,
                    true,
                ),
            ).right()
        } catch (ex: Exception) {
            return ServerError(err = ex).left()
        }
    }

    private fun calculateFine(
        fineData: FineData? = null,
        amount: Long,
        dueDate: LocalDate,
        processingDate: LocalDate,
    ): Either<Err, Long> {
        if (fineData?.date == null || fineData.value == null) return Either.Right(0)

        if (fineData.type != null && !fineData.type.isSupported()) return NotSupported("fine").left()

        val effectiveDueDate = BillChargesUtils.fineEffectiveDueDate(fineData, dueDate)

        val isAbleToCalculate = !effectiveDueDate.isAfter(processingDate)

        if (!isAbleToCalculate) return Either.Right(0)

        return when (fineData.type) {
            FineType.FREE -> Either.Right(0)
            FineType.VALUE -> fineData.value.multiply(BigDecimal(100)).toLong().right()
            FineType.PERCENT -> amount.toBigDecimal().percent(fineData.value).toLong().right()
            else -> NotSupported("fine").left()
        }
    }

    private fun calculateInterest(
        interestData: InterestData? = null,
        amount: Long,
        dueDate: LocalDate,
        processingDate: LocalDate,
    ): Either<Err, Long> {
        if (interestData?.date == null || interestData.value == null) return Either.Right(0)

        if (interestData.type != null && !interestData.type.isSupported()) return NotSupported("interest").left()

        val interestEffectiveCalculateDate = BillChargesUtils.interestEffectiveDueDate(interestData, dueDate)

        val isAbleToCalculate = !interestEffectiveCalculateDate.isAfter(processingDate)

        if (!isAbleToCalculate) return Either.Right(0)

        val daysToAdd = ChronoUnit.DAYS.between(interestData.date, processingDate)

        return when (interestData.type) {
            InterestType.FREE -> Either.Right(0)
            InterestType.VALUE -> interestData.value.multiply(BigDecimal(daysToAdd.plus(1)))
                .multiply(BigDecimal(100))
                .toLong().right()

            InterestType.PERCENT_BY_MONTH ->
                BigDecimal(amount).percentByMonth(interestData.value, (daysToAdd.plus(1))).toLong().right()

            else -> NotSupported("interest").left()
        }
    }

    private fun calculateDiscount(
        discountData: DiscountData? = null,
        amount: Long,
        dueDate: LocalDate,
        processingDate: LocalDate,
    ): Either<Err, Long> {
        if (discountData?.type != null && !discountData.type.isSupported()) return NotSupported("discount").left()

        val discounts = discountData.toDiscountListWithEffectiveDate(dueDate)

        val discount = discounts.firstOrNull {
            !it.date.isBefore(processingDate)
        } ?: return Either.Right(0)

        return when (discountData?.type) {
            DiscountType.FREE -> Either.Right(0)
            DiscountType.FIXED_UNTIL_DATE -> discount.value.multiply(BigDecimal(100)).toLong().right()
            DiscountType.PERCENT_UNTIL_DATE -> amount.toBigDecimal().percent(discount.value).round().toLong()
                .right()

            DiscountType.VALUE_BY_DAY -> {
                val daysToAdd = ChronoUnit.DAYS.between(processingDate, dueDate)

                discount.value.multiply(daysToAdd.toBigDecimal()).round().multiply(
                    BigDecimal(100),
                ).toLong()
                    .right()
            }

            else -> NotSupported("discount").left()
        }
    }

    fun isAbleToCalculateInternally(
        amountCalculationModel: AmountCalculationModel,
        processingDate: LocalDate = getZonedDateTime().toLocalDate(),
        dueDate: LocalDate,
    ): Boolean {
        return when (amountCalculationModel) {
            AmountCalculationModel.ANYONE -> true
            AmountCalculationModel.BENEFICIARY_AFTER_DUE_DATE -> processingDate.isBefore(dueDate)
            else -> false
        }
    }
}

/**
 * Busca a data de validade do processamento baseado nas informacoes de desconto.
 *
 *
 * ex:
 *  - bill.createdAt  -> 2022-12-25
 *  - vencimento      -> 2023-01-11
 *  - now             -> 2023-01-03
 *  - discount.date1  -> 2023-01-02
 *  - discount.date2  -> 2023-01-03
 *  - discount.date3  -> 2023-01-11
 *
 *  Devemos realizar o update da TrackableBill com a data de desconto 2 pois:
 *  - no dia 2022-12-25 quando a conta é criada a TrackableBill é criada com a data 2022-12-25
 *  - no dia 2022-12-26 a TrackableBill entra no seu primeiro ciclo de calculo e sera atualizada com os dados da primeira data de desconto
 *  - durante o período entre 2022-12-27 até o dia 2023-01-02 nao é necessario processar a TrackableBill pois ela foi processada
 *  no seu primeiro ciclo de calculo
 *  - no dia 2023-01-03 a TrackableBill entra no seu segundo ciclo de calculo e sera atualizada com os dados da segunda data de desconto
 *  - no dia 2023-01-04 a TrackableBill entra no seu terceiro ciclo de calculo e sera atualizada com os dados da terceira data de desconto
 *  - durante o período entre 2023-01-05 até 2023-01-11 não é necessário o processar a TrackableBill pois ela foi processada no
 *  terceiro ciclo de cálculo
 *
 * @param discountData - TrackableBill discount data
 *  @return if discount == null ? tomorrow : LocalDate
 */
fun getValidUntil(
    discountData: DiscountData?,
    dueDate: LocalDate,
    actualProcessingDate: LocalDate,
    fineData: FineData?,
    interestData: InterestData?,
    addedByDDA: Boolean,
): LocalDate? {
    if (!addedByDDA) {
        return actualProcessingDate
    } else {
        val localDate = mutableListOf<LocalDate?>()

        if (discountData.hasDiscount()) {
            val fixedDiscountTypes = listOf(
                DiscountType.FIXED_UNTIL_DATE,
                DiscountType.PERCENT_UNTIL_DATE,
                DiscountType.FREE,
            )

            if (discountData?.type in fixedDiscountTypes) {
                localDate.add(discountData?.value1?.let { BillChargesUtils.calculateClosestWorkingDay(discountData.date1 ?: dueDate) })
                localDate.add(discountData?.value2?.let { BillChargesUtils.calculateClosestWorkingDay(discountData.date2 ?: dueDate) })
                localDate.add(discountData?.value3?.let { BillChargesUtils.calculateClosestWorkingDay(discountData.date3 ?: dueDate) })
            }

            if (actualProcessingDate < dueDate) {
                if (discountData?.type !in fixedDiscountTypes) {
                    localDate.add(actualProcessingDate)
                }
            }
        }

        if (fineData.hasFine()) {
            val fineDate = BillChargesUtils.fineEffectiveDueDate(fineData!!, dueDate).minusDays(1)
            if (actualProcessingDate <= fineDate) {
                localDate.add(fineDate)
            }
        }

        if (interestData.hasInterest()) {
            val interestDate = BillChargesUtils.interestEffectiveDueDate(interestData!!, dueDate).minusDays(1)
            localDate.add(interestDate)
            if (actualProcessingDate > interestDate) {
                localDate.add(actualProcessingDate)
            }
        }

        return localDate.filterNotNull().sorted().firstOrNull() { date ->
            date >= actualProcessingDate
        }
    }
}

fun BigDecimal.round(): BigDecimal = this.setScale(2, RoundingMode.DOWN)

fun BigDecimal.percent(percentage: BigDecimal): BigDecimal = this.multiply(percentage.divide(BigDecimal(100)))

const val MONTH_IN_DAYS = 30

fun BigDecimal.percentByMonth(percentage: BigDecimal, days: Long): BigDecimal {
    val interestByDay = percentage.setScale(6, RoundingMode.DOWN).divide(BigDecimal(100), RoundingMode.DOWN).divide(BigDecimal(MONTH_IN_DAYS), RoundingMode.DOWN)
    val totalInterestOfDays = interestByDay.multiply(days.toBigDecimal())
    return this.multiply(totalInterestOfDays).round()
}

class NotSupported(message: String) : Err(message = message)

data class CalculatedAmount(
    val originalAmount: Long,
    val discount: Long,
    val interest: Long,
    val fine: Long,
    val abatement: Long,
    val totalAmount: Long,
    val validUntil: LocalDate?,
)

private data class Discount(
    val date: LocalDate,
    val value: BigDecimal,
)

private fun buildDiscountWithEffectiveDate(dueDate: LocalDate, date: LocalDate?, value: BigDecimal?) = value?.let {
    Discount(
        date = BillChargesUtils.calculateClosestWorkingDay(date ?: dueDate),
        value = it,
    )
}

private fun DiscountData?.toDiscountListWithEffectiveDate(dueDate: LocalDate) = this?.let {
    listOfNotNull(
        buildDiscountWithEffectiveDate(dueDate, date1, value1),
        buildDiscountWithEffectiveDate(dueDate, date2, value2),
        buildDiscountWithEffectiveDate(dueDate, date3, value3),
    ).sortedBy {
        it.date
    }
} ?: emptyList()