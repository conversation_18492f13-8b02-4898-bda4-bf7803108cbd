package ai.friday.billpayment.app.bill.instrumentation

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.instrumentation.BaseInstrumentationService
import ai.friday.billpayment.app.instrumentation.InstrumentationRepository
import ai.friday.billpayment.app.integrations.BillInstrumentationService
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.recurrence.BillRecurrence
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.morning.log.andAppend
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
open class DefaultBillInstrumentationService(
    instrumentationRepository: InstrumentationRepository<BillInstrumentationEvent>,
    val walletRepository: WalletRepository,
) :
    BaseInstrumentationService<BillInstrumentationEvent>(instrumentationRepository), BillInstrumentationService {

    override fun scheduled(wallet: Wallet, accountId: AccountId, billsCount: Int) {
        try {
            publishEventAsync(
                BillInstrumentationEvent.Scheduled.create(
                    wallet = wallet,
                    accountId = accountId,
                    billsCount = billsCount,
                ),
            )
        } catch (exception: Exception) {
            LOG.error(
                Markers.append("walletId", wallet.id).andAppend("member.accountId", accountId.value)
                    .andAppend("quantity", billsCount),
                "scheduled",
                exception,
            )
        }
    }

    override fun createdRecurring(recurrence: BillRecurrence) {
        try {
            val wallet = walletRepository.findWallet(recurrence.walletId)
            publishEventAsync(BillInstrumentationEvent.CreatedRecurring.create(recurrence, wallet))
        } catch (exception: Exception) {
            LOG.error(
                Markers.append("recurrence", recurrence),
                "createdRecurring",
                exception,
            )
        }
    }

    override fun createdMailbox(bill: Bill) {
        try {
            val wallet =
                walletRepository.findWallet(bill.walletId) // TODO: faz sentido aqui buscar a wallet?

            if (bill.source is ActionSource.WalletMailBox) {
                publishEventAsync(
                    BillInstrumentationEvent.CreatedMailbox.create(bill, wallet),
                )
            }
        } catch (exception: Exception) {
            LOG.error(
                Markers.append("bill", bill),
                "createdMailbox",
                exception,
            )
        }
    }

    override fun paid(bill: Bill) {
        try {
            val wallet =
                walletRepository.findWallet(bill.walletId) // TODO: faz sentido aqui buscar a wallet?

            publishEventAsync(
                BillInstrumentationEvent.Paid.create(bill, wallet),
            )
        } catch (exception: Exception) {
            LOG.error(
                Markers.append("bill", bill),
                "paid",
                exception,
            )
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(DefaultBillInstrumentationService::class.java)
    }
}