package ai.friday.billpayment.app.bill.schedule

import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.payment.PaymentMethodsDetail
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import io.micronaut.context.event.ApplicationEventListener
import io.micronaut.discovery.event.ServiceReadyEvent
import jakarta.inject.Singleton

@Singleton
class LegacyPaymentMethodsDetailParser(private val builder: LegacyPaymentMethodsDetailBuilder) :
    ApplicationEventListener<ServiceReadyEvent> {

    init {
        legacyPaymentMethodsDetailBuilder = builder
    }

    override fun onApplicationEvent(event: ServiceReadyEvent?) {
        legacyPaymentMethodsDetailBuilder = builder
    }

    companion object {
        private lateinit var legacyPaymentMethodsDetailBuilder: LegacyPaymentMethodsDetailBuilder

        fun parse(amount: Long, paymentMethodId: AccountPaymentMethodId?): PaymentMethodsDetail {
            return legacyPaymentMethodsDetailBuilder.build(amount, paymentMethodId)
        }
    }
}

interface LegacyPaymentMethodsDetailBuilder {
    fun build(amount: Long, paymentMethodId: AccountPaymentMethodId?): PaymentMethodsDetail
}

@Singleton
class FridayLegacyPaymentMethodsDetailBuilder : LegacyPaymentMethodsDetailBuilder {
    override fun build(amount: Long, paymentMethodId: AccountPaymentMethodId?): PaymentMethodsDetail {
        return PaymentMethodsDetailWithBalance(
            amount = amount,
            paymentMethodId = paymentMethodId!!,
            calculationId = null,
        )
    }
}