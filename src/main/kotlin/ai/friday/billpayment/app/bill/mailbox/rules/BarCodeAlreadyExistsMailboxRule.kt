package ai.friday.billpayment.app.bill.mailbox.rules

import ai.friday.billpayment.app.bill.MailboxAddBillError
import ai.friday.billpayment.app.bill.mailbox.BLOCK_PRIORITY
import ai.friday.billpayment.app.bill.mailbox.MailboxSecurityRule
import ai.friday.billpayment.app.bill.mailbox.MailboxSecurityValidationErrorLevel
import ai.friday.billpayment.app.bill.mailbox.MailboxValidateRequest
import ai.friday.billpayment.app.integrations.BillEventRepository
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton

@Singleton
@Requires(property = "mailbox.security.rule.barcode-already-exists.enabled", value = "true", defaultValue = "false")
class BarCodeAlreadyExistsMailboxRule(
    private val billEventRepository: BillEventRepository,
    @Property(name = "mailbox.security.rule.barcode-already-exists.max-wallets") private val maxWallets: Int,
) : MailboxSecurityRule {
    override fun getPriority() = BLOCK_PRIORITY + 1

    override fun execute(request: MailboxValidateRequest): Either<MailboxAddBillError.MailboxSecurityValidationError, Unit> {
        val wallets = billEventRepository.listBills(barCode = request.barCode, dueDate = request.dueDate)
            .map { it.walletId }
            .toSet()

        if (wallets.size >= maxWallets) {
            return MailboxAddBillError.MailboxSecurityValidationError(
                reason = "BarCodeAlreadyExists",
                level = MailboxSecurityValidationErrorLevel.BLOCK,
            ).left()
        }

        return Unit.right()
    }
}