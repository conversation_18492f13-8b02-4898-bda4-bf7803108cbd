package ai.friday.billpayment.app.bill.schedule.security

import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.schedule.security.rules.ScheduleBillSecurityRule
import ai.friday.billpayment.app.payment.PaymentMethodsDetail
import ai.friday.billpayment.app.wallet.Member
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.event.ApplicationEventListener
import io.micronaut.discovery.event.ServiceReadyEvent
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class ScheduleBillSecurityService(
    private val rules: List<ScheduleBillSecurityRule>,
) : ApplicationEventListener<ServiceReadyEvent> {

    private val logger = LoggerFactory.getLogger(ScheduleBillSecurityService::class.java)

    fun canSchedule(request: ScheduleBillSecurityRequest): Either<ScheduleBillSecurityError, Unit> {
        rules
            .sortedBy { it.getPriority() }
            .forEach {
                it.execute(request).getOrElse { result ->
                    return result.left()
                }
            }

        return Unit.right()
    }

    override fun onApplicationEvent(event: ServiceReadyEvent) {
        if (rules.isEmpty()) {
            logger.info(Markers.append("rule", "no rules"), "ScheduleBillSecurityService#OnServiceReadyEvent")
            return
        }
        rules.forEach {
            logger.info(Markers.append("rule", it.javaClass.simpleName), "ScheduleBillSecurityService#OnServiceReadyEvent")
        }
    }
}

data class ScheduleBillSecurityRequest(
    val bill: Bill,
    val member: Member,
    val paymentMethodDetails: PaymentMethodsDetail,
)