package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime

data class BillComingDue(
    val billId: BillId,
    val walletId: WalletId,
    val dueDate: LocalDate,
    val paymentLimitTime: LocalTime,
    val isScheduled: Boolean,
    val created: ZonedDateTime,
    val notificatedAt: ZonedDateTime? = null,
    val lastUpdated: ZonedDateTime = getZonedDateTime(),
)