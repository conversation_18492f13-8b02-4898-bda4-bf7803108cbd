package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.hasEarlyAccess
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.ErrorSource
import ai.friday.billpayment.app.payment.MAX_PAYMENT_LIMIT_AMOUNT
import ai.friday.billpayment.app.pix.PixTransactionError
import kotlin.reflect.full.isSubclassOf

enum class WarningCode(val code: Int) {
    NONE(0),
    PAYMENT(1),
    SETTLEMENT(2),
    REFUNDED(3),
    SERVER_ERROR(4),
    BUSINESS_INSUFFICIENT_BALANCE(5),
    BUSINESS_SINGLE_TRANSACTION_LIMIT(6),
    BUSINESS_DAILY_LIMIT(7),
    BUSINESS_SAME_VALUE_PAYMENTS_EXCEEDED(8),
    SETTLEMENT_DESTINATION_ACCOUNT_NOT_FOUND(9),
    SETTLEMENT_DESTINATION_ACCOUNT_NOT_AVAILABLE(10),
    SETTLEMENT_GENERIC_PERMANENT_ERROR(11),
    SETTLEMENT_GENERIC_TEMPORARY_ERROR(12),
    SETTLEMENT_PAYMENT_REFUSED_BY_DESTINATION(13),
    SETTLEMENT_DESTINATION_INSTITUTION_NOT_ALLOWED(14),
    UNSCHEDULED_DUE_PAYMENT_LIMIT_TIME(15),
    UNSCHEDULED_DUE_AMOUNT_HIGHER_THAN_DAILY_LIMIT(16),
    POSTPONED_DUE_LIMIT_REACHED(17),
    SETTLEMENT_DESTINATION_NOT_ALLOWED_AMOUNT(18),
    NOT_VISIBLE_BILL_ALREADY_EXISTS(19),
    REFUNDED_DESTINATION_NOT_ALLOWED_AMOUNT(20),
    SETTLEMENT_INVALID_PIX_KEY(21),
    PIX_PAYMENT(22),
    CREDIT_CARD_PAYMENT(23),
    DEPOSIT_SLIP_SAME_INSTITUTION(24),
    PAYMENT_SOURCE_ACCOUNT_LOCKED(25),
    UNSCHEDULED_DUE_AMOUNT_CHANGED(26),
    CANT_PAY_WITH_CURRENT_CREDIT_CARD(27),
    POSTPONED_DUE_MONTHLY_LIMIT_REACHED(28),
    MAX_PAYMENT_LIMIT_EXCEEDED(29),
    ZERO_AMOUNT(30),
    BENEFICIARY_UPDATE_REQUIRED(31),
    ;

    companion object {
        fun getByCode(code: Int): WarningCode {
            return values().find { it.code == code } ?: NONE
        }
    }
}

fun Bill.getWarningCode(): WarningCode {
    fun isPaidInvoice(): Boolean = this.billType == BillType.INVOICE && this.history.any { it is BillPaid }

    fun filterWarningCodeEvents(event: BillEvent): Boolean {
        return event is PaymentFailed ||
            event is PaymentRefunded ||
            event is PaymentStarted ||
            event is BillPaid ||
            event is RegisterUpdated ||
            (event is FichaCompensacaoAdded && event.amountCalculationModel == AmountCalculationModel.BENEFICIARY_ONLY) ||
            event is BillPaymentScheduled ||
            event is FichaCompensacaoAdded ||
            (
                event is BillPaymentScheduleCanceled && event.reason in listOf(
                    ScheduleCanceledReason.EXPIRATION,
                    ScheduleCanceledReason.AMOUNT_HIGHER_THAN_DAILY_LIMIT,
                    ScheduleCanceledReason.BILL_AMOUNT_CHANGED,
                    ScheduleCanceledReason.CANT_PAY_WITH_CURRENT_CREDIT_CARD,
                )
                ) ||
            (
                event is BillSchedulePostponed && event.reason in listOf(
                    SchedulePostponedReason.LIMIT_REACHED,
                    SchedulePostponedReason.MONTHLY_LIMIT_REACHED,
                )
                )
    }

    val warningCodeEvents = this.history.filter { filterWarningCodeEvents(it) }

    if (warningCodeEvents.isEmpty()) {
        return WarningCode.NONE
    }

    val lastWarningCodeEvent = warningCodeEvents.last()

    return when {
        lastWarningCodeEvent is PaymentStarted -> WarningCode.NONE
        lastWarningCodeEvent is PaymentFailed && isPaidInvoice() -> WarningCode.REFUNDED
        lastWarningCodeEvent is PaymentFailed && this.billType == BillType.PIX -> findPixTransactionErrorByCode(
            lastWarningCodeEvent.errorDescription,
        )?.toWarningCode() ?: WarningCode.NONE // TODO remover NONE?
        lastWarningCodeEvent is PaymentRefunded -> lastWarningCodeEvent.toWarningCode()
        AccountId(this.walletId.value).hasEarlyAccess() && (this.status == BillStatus.NOT_PAYABLE && lastWarningCodeEvent is FichaCompensacaoAdded && amountTotal > MAX_PAYMENT_LIMIT_AMOUNT) -> WarningCode.MAX_PAYMENT_LIMIT_EXCEEDED
        AccountId(this.walletId.value).hasEarlyAccess() && (this.status == BillStatus.NOT_PAYABLE && lastWarningCodeEvent is FichaCompensacaoAdded && amountTotal == 0L) -> WarningCode.ZERO_AMOUNT
        this.status == BillStatus.NOT_PAYABLE -> WarningCode.SETTLEMENT
        this.status == BillStatus.ALREADY_PAID -> WarningCode.NONE
        lastWarningCodeEvent is PaymentFailed && lastWarningCodeEvent.errorSource == ErrorSource.ACQUIRER -> WarningCode.CREDIT_CARD_PAYMENT
        lastWarningCodeEvent is PaymentFailed -> WarningCode.SERVER_ERROR
        lastWarningCodeEvent is BillPaymentScheduleCanceled && lastWarningCodeEvent.reason == ScheduleCanceledReason.EXPIRATION -> WarningCode.UNSCHEDULED_DUE_PAYMENT_LIMIT_TIME
        lastWarningCodeEvent is BillPaymentScheduleCanceled && lastWarningCodeEvent.reason == ScheduleCanceledReason.AMOUNT_HIGHER_THAN_DAILY_LIMIT -> WarningCode.UNSCHEDULED_DUE_AMOUNT_HIGHER_THAN_DAILY_LIMIT
        lastWarningCodeEvent is BillPaymentScheduleCanceled && lastWarningCodeEvent.reason == ScheduleCanceledReason.BILL_AMOUNT_CHANGED -> WarningCode.UNSCHEDULED_DUE_AMOUNT_CHANGED
        lastWarningCodeEvent is BillPaymentScheduleCanceled && lastWarningCodeEvent.reason == ScheduleCanceledReason.CANT_PAY_WITH_CURRENT_CREDIT_CARD -> WarningCode.CANT_PAY_WITH_CURRENT_CREDIT_CARD
        lastWarningCodeEvent is BillSchedulePostponed && lastWarningCodeEvent.reason == SchedulePostponedReason.LIMIT_REACHED -> WarningCode.POSTPONED_DUE_LIMIT_REACHED
        lastWarningCodeEvent is BillSchedulePostponed && lastWarningCodeEvent.reason == SchedulePostponedReason.MONTHLY_LIMIT_REACHED -> WarningCode.POSTPONED_DUE_MONTHLY_LIMIT_REACHED
        lastWarningCodeEvent is BillPaymentScheduled -> WarningCode.NONE
        lastWarningCodeEvent is FichaCompensacaoAdded && this.needsBeneficaryUpdate() -> WarningCode.BENEFICIARY_UPDATE_REQUIRED
        lastWarningCodeEvent is RegisterUpdated && this.needsBeneficaryUpdate() -> WarningCode.BENEFICIARY_UPDATE_REQUIRED
        else -> WarningCode.NONE
    }
}

private fun PaymentRefunded.toWarningCode() =
    when (reason) {
        PaymentRefundedReason.DESTINATION_NOT_ALLOWED_AMOUNT -> WarningCode.REFUNDED_DESTINATION_NOT_ALLOWED_AMOUNT
        PaymentRefundedReason.INVALID_DATA, PaymentRefundedReason.ERROR -> WarningCode.REFUNDED
    }

private val pixTransactionErrors: List<PixTransactionError> =
    PixTransactionError::class.nestedClasses.filter { it.isFinal && it.isSubclassOf(PixTransactionError::class) }
        .map { klass -> klass.objectInstance as PixTransactionError }

private fun findPixTransactionErrorByCode(code: String): PixTransactionError? {
    return pixTransactionErrors.find { it.code == code }
}

private fun PixTransactionError.toWarningCode(): WarningCode = when (this) {
    PixTransactionError.PaymentGenericTemporaryError -> WarningCode.PIX_PAYMENT
    PixTransactionError.BusinessInsufficientBalance -> WarningCode.BUSINESS_INSUFFICIENT_BALANCE
    PixTransactionError.BusinessSingleTransactionLimit -> WarningCode.BUSINESS_SINGLE_TRANSACTION_LIMIT
    PixTransactionError.BusinessDailyLimit -> WarningCode.BUSINESS_DAILY_LIMIT
    PixTransactionError.BusinessSameValuePaymentsExceeded -> WarningCode.BUSINESS_SAME_VALUE_PAYMENTS_EXCEEDED
    PixTransactionError.SettlementDestinationAccountNotFound -> WarningCode.SETTLEMENT_DESTINATION_ACCOUNT_NOT_FOUND
    PixTransactionError.SettlementDestinationAccountNotAvailable -> WarningCode.SETTLEMENT_DESTINATION_ACCOUNT_NOT_AVAILABLE
    PixTransactionError.SettlementGenericPermanentError -> WarningCode.SETTLEMENT_GENERIC_PERMANENT_ERROR
    PixTransactionError.UnknownTemporaryError,
    PixTransactionError.SettlementGenericTemporaryError,
    PixTransactionError.SystemUnavailable,
    PixTransactionError.AccountFundsBlocked,
    PixTransactionError.DeviceFingerprintError,
    -> WarningCode.SETTLEMENT_GENERIC_TEMPORARY_ERROR

    PixTransactionError.SettlementPaymentRefusedByDestination -> WarningCode.SETTLEMENT_PAYMENT_REFUSED_BY_DESTINATION
    PixTransactionError.SettlementDestinationInstitutionNotAllowed -> WarningCode.SETTLEMENT_DESTINATION_INSTITUTION_NOT_ALLOWED
    PixTransactionError.SettlementDestinationAccountTypeInvalid -> WarningCode.SETTLEMENT_DESTINATION_ACCOUNT_NOT_FOUND
    PixTransactionError.SettlementDestinationNotAllowedAmount -> WarningCode.SETTLEMENT_DESTINATION_NOT_ALLOWED_AMOUNT
    PixTransactionError.InvalidPixkey -> WarningCode.SETTLEMENT_INVALID_PIX_KEY
    PixTransactionError.PaymentSourceAccountLocked -> WarningCode.PAYMENT_SOURCE_ACCOUNT_LOCKED
    PixTransactionError.QrCodeRejectedByRecipient -> WarningCode.PIX_PAYMENT
}