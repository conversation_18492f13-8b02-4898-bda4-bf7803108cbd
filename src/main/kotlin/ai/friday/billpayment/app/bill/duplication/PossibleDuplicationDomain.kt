package ai.friday.billpayment.app.bill.duplication

import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillTag
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.recurrence.BillRecurrence
import java.time.LocalDate

data class PossibleDuplicate(val billId: BillId, val dueDate: LocalDate)

sealed interface PossibleDuplicationResult {
    data object NotDuplicated : PossibleDuplicationResult

    data class BilateralDuplication(val first: SimilarityWrapper, val second: SimilarityWrapper) :
        PossibleDuplicationResult

    data class UnilateralDuplication(val duplicationOf: SimilarityWrapper, val duplication: SimilarityWrapper) :
        PossibleDuplicationResult
}

data class SimilarityWrapper(
    val billType: BillType,
    val status: BillStatus,
    val effectiveDueDate: LocalDate,
    val recipientDocument: String?,
    val amountTotal: Long,
    val barCode: BarCode? = null,
    val idNumber: String? = null,
    val fichaCompensacaoType: FichaCompensacaoType? = null,
    val recipientName: String? = null,
    val assignor: String? = null,
    val description: String? = null,
    val billId: BillId? = null,
    val tags: Set<BillTag>? = null,
) {

    constructor(bill: Bill) : this(
        billType = bill.billType,
        status = bill.status,
        effectiveDueDate = bill.effectiveDueDate,
        recipientDocument = bill.recipient?.document,
        recipientName = bill.recipient?.name,
        amountTotal = bill.amountTotal,
        barCode = bill.barcode,
        idNumber = bill.idNumber,
        fichaCompensacaoType = bill.fichaCompensacaoType,
        assignor = bill.assignor,
        description = bill.description,
        billId = bill.billId,
        tags = bill.tags,
    )

    constructor(bill: BillView) : this(
        billType = bill.billType,
        status = bill.status,
        effectiveDueDate = bill.effectiveDueDate,
        recipientDocument = bill.recipient?.document,
        recipientName = bill.recipient?.name,
        amountTotal = bill.amountTotal,
        barCode = bill.barCode,
        idNumber = bill.idNumber,
        fichaCompensacaoType = bill.fichaCompensacaoType,
        assignor = bill.assignor,
        description = bill.billDescription,
        billId = bill.billId,
        tags = bill.tags,
    )

    constructor(recurrence: BillRecurrence) : this(
        billType = recurrence.billType,
        status = BillStatus.ACTIVE,
        effectiveDueDate = recurrence.rule.startDate,
        recipientDocument = recurrence.recipientDocument,
        amountTotal = recurrence.amount,
    )
}

fun PossibleDuplicationResult.isDuplicated() = when (this) {
    PossibleDuplicationResult.NotDuplicated -> false
    is PossibleDuplicationResult.BilateralDuplication, is PossibleDuplicationResult.UnilateralDuplication -> true
}