package ai.friday.billpayment.app.bill.mailbox

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.MailboxAddBillError
import ai.friday.billpayment.app.wallet.WalletId
import arrow.core.getOrElse
import io.micronaut.context.event.ApplicationEventListener
import io.micronaut.discovery.event.ServiceReadyEvent
import io.via1.communicationcentre.app.receipt.Receipt
import java.time.LocalDate
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
class MailboxSecurityService(
    private val rules: List<MailboxSecurityRule>,
) : ApplicationEventListener<ServiceReadyEvent> {

    private val logger = LoggerFactory.getLogger(MailboxSecurityService::class.java)

    fun execute(request: MailboxValidateRequest): List<MailboxAddBillError.MailboxSecurityValidationError> {
        val errors = mutableListOf<MailboxAddBillError.MailboxSecurityValidationError>()
        rules
            .sortedBy { it.getPriority() }
            .forEach {
                it.execute(request).getOrElse { result ->
                    errors.add(result)
                    if (result.level == MailboxSecurityValidationErrorLevel.BLOCK) {
                        return errors
                    }
                }
            }

        return errors
    }

    override fun onApplicationEvent(event: ServiceReadyEvent) {
        if (rules.isEmpty()) {
            logger.info(Markers.append("rule", "no rules"), "MailboxSecurityServiceOnServiceReadyEvent")
            return
        }
        rules.forEach {
            logger.info(Markers.append("rule", it.javaClass.simpleName), "MailboxSecurityServiceOnServiceReadyEvent")
        }
    }
}

enum class MailboxSecurityValidationErrorLevel {
    BLOCK, WARN
}

data class MailboxValidateRequest(
    val from: EmailAddress,
    val walletId: WalletId,
    val barCode: BarCode,
    val dueDate: LocalDate? = null,
    val receipt: Receipt? = null,
    val fromAccountId: AccountId? = null,
)