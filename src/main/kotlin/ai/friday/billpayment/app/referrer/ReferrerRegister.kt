package ai.friday.billpayment.app.referrer

import ai.friday.billpayment.app.account.AccountId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.time.ZonedDateTime

data class ReferrerRegister(
    val accountId: AccountId,
    val created: ZonedDateTime = getZonedDateTime(),
    val referrer: String?,
    val referrerUrl: String?,
    val platform: String,
    val appVersion: String,
)