package ai.friday.billpayment.app.referrer

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.ReferrerRepository

@FridayMePoupe
open class ReferrerService(
    private val accountRepository: AccountRepository,
    private val referrerRepository: ReferrerRepository,
) {

    fun createReferrerRegister(
        referrerRegister: ReferrerRegister,
    ): AccountReferrerResult {
        if (referrerRegister.referrer.isNullOrEmpty() && referrerRegister.referrerUrl.isNullOrEmpty()) {
            return AccountReferrerResult.Invalid
        }

        try {
            accountRepository.findPartialAccountById(referrerRegister.accountId)
        } catch (exception: AccountNotFoundException) {
            return AccountReferrerResult.AccountNotFound
        }

        referrerRepository.find(referrerRegister.accountId)?.let {
            return AccountReferrerResult.AlreadyExists
        }

        referrerRepository.save(referrerRegister)

        return AccountReferrerResult.Success
    }
}

sealed class AccountReferrerResult {
    data object AccountNotFound : AccountReferrerResult()
    data object Success : AccountReferrerResult()
    data object AlreadyExists : AccountReferrerResult()
    data object Invalid : AccountReferrerResult()
}