package ai.friday.billpayment.app.pix

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.integrations.PixQRCodeCreatorService
import ai.friday.billpayment.app.onepixpay.QrCodeTransactionId
import ai.friday.billpayment.app.stripAccents
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.util.Locale
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

// NOTE: valores de acordo com a documentação do BACEN https://www.bcb.gov.br/content/estabilidadefinanceira/pix/Regulamento_Pix/II_ManualdePadroesparaIniciacaodoPix.pdf
private const val GloballyUniqueIdentifier = "br.gov.bcb.pix"
private const val Version = "01"
private const val CurrencyCode = "986"; // código para Real
private const val DefaultMerchantCategoryCode = "0000"
private const val DefaultCountryCode = "BR"
private const val DefaultZipCode = "20220297"
private const val DefaultCity = "RIO DE JANEIRO"
private const val MaxAditionalInfoSize = 95 // tamanho maximo do campo é 99 - 4 caracteres que são usados para o id + tamanho da mensagem

private val amountFormatter = DecimalFormat("0.00", DecimalFormatSymbols(Locale.ENGLISH))

@FridayMePoupe
class LocalPixQRCodeCreator : PixQRCodeCreatorService {
    private val logger = LoggerFactory.getLogger(LocalPixQRCodeCreator::class.java)
    override fun createQRCode(
        key: PixKey,
        document: String,
        amount: Long,
        recipientName: String,
        transactionId: QrCodeTransactionId,
        aditionalInfo: String,
    ): String {
        val recipientNameMaxSize = 25
        val payload = listOf(
            genEMV("00", Version),
            genEMV("26", generateKey(key, aditionalInfo)),
            genEMV("52", DefaultMerchantCategoryCode),
            genEMV("53", CurrencyCode),
            genEMV("54", formatAmount(amount)),
            genEMV("58", DefaultCountryCode),
            genEMV("59", recipientName.stripAccents().take(recipientNameMaxSize)),
            genEMV("60", DefaultCity),
            genEMV("61", DefaultZipCode),
            genEMV("62", genEMV("05", transactionId.value)),
            "6304",
        ).joinToString(separator = "") { it }

        // NOTE: CRC162 (Polinômio 0x1021, C.I. 0xFFFF). De acordo com a documentação do BACEN https://www.bcb.gov.br/content/estabilidadefinanceira/pix/Regulamento_Pix/II_ManualdePadroesparaIniciacaodoPix.pdf
        val crcResult = crc16(payload.toByteArray()).toString(16).uppercase().padStart(4, '0')

        return "${payload}$crcResult".also {
            logger.info(Markers.append("qrCode", it), "LocalPixQRCodeCreator#createQRCode")
        }
    }
}

private fun formatAmount(amount: Long): String {
    return amountFormatter.format(amount / 100.0)
}

private fun generateKey(key: PixKey, message: String? = null): String {
    val fields = mutableListOf(genEMV("00", GloballyUniqueIdentifier), genEMV("01", key.value))

    val fieldsLength = fields.sumOf { it.length }

    if (!message.isNullOrBlank()) {
        fields.add(genEMV("02", message.take(MaxAditionalInfoSize - fieldsLength)))
    }

    return fields.joinToString(separator = "") {
        it
    }
}

private fun genEMV(id: String, parameter: String): String {
    val length = parameter.length.toString().padStart(2, '0')

    return "${id}${length}$parameter"
}

private fun crc16(bytes: ByteArray): Int {
    var crc = 0xFFFF
    for (j in bytes.indices) {
        crc = crc ushr 8 or (crc shl 8) and 0xffff
        crc = crc xor (bytes[j].toInt() and 0xff) // byte to int, trunc sign
        crc = crc xor (crc and 0xff shr 4)
        crc = crc xor (crc shl 12 and 0xffff)
        crc = crc xor (crc and 0xFF shl 5 and 0xffff)
    }
    crc = crc and 0xffff
    return crc
}