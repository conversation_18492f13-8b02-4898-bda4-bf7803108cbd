package ai.friday.billpayment.app.pix

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.PixKeyRepository
import ai.friday.billpayment.app.integrations.PixKeyServiceInterface
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
class PixKeyService(
    private val pixKeyRepository: PixKeyRepository,
    private val pixKeyManagementAdapter: PixKeyManagement,
    private val accountService: AccountService,
    private val walletService: WalletService,
    private val deviceFingerprintService: DeviceFingerprintService,
) : PixKeyServiceInterface {
    override fun createPixKey(walletId: WalletId, pixKey: PixKey): Either<CreatePixKeyError, Unit> {
        val markers = Markers.empty()
        val logName = "PixKeyService#createPixKey"
        return try {
            if (pixKey.type != PixKeyType.EVP && pixKeyRepository.pixKeyExists(pixKey)) {
                logger.warn(markers.andAppend("reason", "pix key already exists"), logName)
                return CreatePixKeyError.PixKeyAlreadyExists.left()
            }

            val wallet = walletService.findWallet(walletId)

            val founderAccountId = wallet.founder.accountId
            val account = accountService.findAccountById(founderAccountId)

            val paymentMethods = accountService.findAccountPaymentMethodsByAccountId(founderAccountId, AccountPaymentMethodStatus.ACTIVE)

            if (paymentMethods.isEmpty()) {
                logger.error(markers.andAppend("reason", "payment method not found"), logName)
                return CreatePixKeyError.BankAccountNotFound.left()
            }

            val accountNumber = paymentMethods
                .filter { it.method.type == PaymentMethodType.BALANCE }
                .filter { (it.method as InternalBankAccount).bankAccountMode == BankAccountMode.PHYSICAL }
                .filter { it.id == wallet.paymentMethodId }
                .map { AccountNumber((it.method as InternalBankAccount).buildFullAccountNumber()) }
                .singleOrNull()

            if (accountNumber == null) {
                logger.error(markers.andAppend("reason", "payment method not found"), logName)
                return CreatePixKeyError.BankAccountNotFound.left()
            }

            val deviceId = deviceFingerprintService.getOrNull(founderAccountId)?.deviceIds?.get(accountNumber)

            // TODO: poderia fazer o claim de alguma chave e não só cadastrar
            val createdKey = pixKeyManagementAdapter.registerKey(
                accountNo = accountNumber,
                key = pixKey,
                document = account.document,
                name = account.name,
                deviceId = deviceId,
            )

            pixKeyRepository.create(walletId, accountNumber, createdKey)

            Unit.right()
        } catch (e: AccountNotFoundException) {
            logger.error(markers, logName, e)
            return CreatePixKeyError.AccountNotFound.left()
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            return CreatePixKeyError.ServerError(ex.message.orEmpty()).left()
        }
    }

    override fun deletePixKey(walletId: WalletId, pixKey: PixKey): Either<DeletePixKeyError, Unit> {
        return try {
            val localPixKey = pixKeyRepository.list(walletId).filter { it == pixKey }.ifEmpty { return DeletePixKeyError.PixKeyNotFound.left() }.single()

            val wallet = walletService.findWallet(walletId)

            val account = accountService.findAccountById(wallet.founder.accountId)

            // TODO: deveria ser possível excluir uma chave friday (ex.: cpf@...)?
            deviceFingerprintService.withRealOrTemporaryDeviceId(accountId = wallet.founder.accountId, walletId = wallet.id) { deviceId ->
                pixKeyManagementAdapter.deleteKey(localPixKey.value, Document(account.document), deviceId = deviceId)
            }

            pixKeyRepository.delete(walletId, localPixKey)

            Unit.right()
        } catch (e: AccountNotFoundException) {
            return DeletePixKeyError.AccountNotFound.left()
        } catch (ex: Exception) {
            return DeletePixKeyError.ServerError(ex.message.orEmpty()).left()
        }
    }

    override fun listPixKeys(walletId: WalletId): List<PixKey> {
        return pixKeyRepository.list(walletId)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(PixKeyService::class.java)
    }
}

sealed class CreatePixKeyError : PrintableSealedClassV2() {
    data object PixKeyAlreadyExists : CreatePixKeyError()
    data object AccountNotFound : CreatePixKeyError()
    data object BankAccountNotFound : CreatePixKeyError()
    data class ServerError(val message: String) : CreatePixKeyError()
}

sealed class DeletePixKeyError : PrintableSealedClassV2() {
    data object PixKeyNotFound : DeletePixKeyError()
    data object AccountNotFound : DeletePixKeyError()
    data class ServerError(val message: String) : DeletePixKeyError()
}