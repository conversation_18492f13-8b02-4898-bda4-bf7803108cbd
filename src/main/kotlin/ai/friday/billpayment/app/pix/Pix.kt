package ai.friday.billpayment.app.pix

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankStatementItem
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.BankStatementMetadata
import ai.friday.billpayment.app.bill.BillEventDependency
import ai.friday.billpayment.app.bill.PixQrCodeData
import ai.friday.billpayment.app.integrations.EndToEnd
import ai.friday.billpayment.app.onepixpay.QrCodeTransactionId
import ai.friday.billpayment.app.stripAccents
import ai.friday.billpayment.app.wallet.WalletId
import java.math.BigInteger
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.regex.Pattern

const val uuidRegexMatcher = "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\$"

@BillEventDependency
enum class PixKeyType(val regex: String) {
    CNPJ("^[0-9]{14}\$"),
    PHONE("^\\+[1-9][0-9]\\d{1,14}\$"),
    EMAIL("^[a-z0-9.!#\$%&‘*+/=?^_`{|}~-]+@[a-z0-9-]+(?:\\.[a-z0-9-]+)*\$"),
    EVP(uuidRegexMatcher),
    CPF("^[0-9]{11}\$"),
    ;

    companion object {
        fun fromString(value: String): PixKeyType? {
            return entries.find { Pattern.compile(it.regex).matcher(value).matches() }
        }
    }
}

@BillEventDependency
class PixKey(value: String, val type: PixKeyType) {
    val value: String = value.lowercase()

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as PixKey

        if (type != other.type) return false
        if (value != other.value) return false

        return true
    }

    override fun hashCode(): Int {
        var result = type.hashCode()
        result = 31 * result + value.hashCode()
        return result
    }
}

enum class PixKeyStatus {
    ACTIVE, INACTIVE
}

fun PixKey.isValid() = Pattern.compile(type.regex).matcher(value).matches()

fun emailPixKeySanitize(value: String): String {
    return value.stripAccents()
        .replace(" ", ".")
        .replace(Regex("[^\\w.@]"), "")
        .lowercase()
}

@BillEventDependency
data class PixKeyHolder(
    val accountNo: BigInteger,
    val accountDv: String,
    val ispb: String,
    val institutionName: String,
    val accountType: AccountType,
    val routingNo: Long,
)

@BillEventDependency
data class PixKeyOwner(
    val name: String,
    val document: String,
)

@BillEventDependency
data class PixKeyDetails(
    val key: PixKey,
    val holder: PixKeyHolder,
    val owner: PixKeyOwner,
)

data class PixKeyDetailsResult(
    val pixKeyDetails: PixKeyDetails,
    val e2e: String,
    val qrCodeInfo: PixQrCodeData? = null,
)

data class PixQRCodeDetailsResult(
    val pixKeyDetails: PixKeyDetails?,
    val e2e: String?,
    val qrCodeInfo: PixQrCodeData? = null,
) {
    fun asPixKeyDetailsResult(): PixKeyDetailsResult {
        return PixKeyDetailsResult(
            pixKeyDetails = pixKeyDetails ?: throw IllegalStateException("pixKeyDetails cannot be null"),
            e2e = e2e ?: throw IllegalStateException("e2e cannot be null"),
            qrCodeInfo = qrCodeInfo,
        )
    }
}

sealed class PixKeyError : PrintableSealedClassV2() {
    data object MalformedKey : PixKeyError()
    data object KeyNotFound : PixKeyError()
    data object KeyNotConfirmed : PixKeyError()
    data object UnknownError : PixKeyError()
    data object SystemUnavailable : PixKeyError()
    data object InvalidQrCode : PixKeyError()
}

enum class PixErrorType {
    PERMANENT, TEMPORARY
}

sealed class PixTransactionError(val code: String, val type: PixErrorType) {
    data object PaymentGenericTemporaryError : PixTransactionError("PIXe-001", PixErrorType.TEMPORARY)
    data object BusinessInsufficientBalance : PixTransactionError("PIXe-002", PixErrorType.TEMPORARY)
    data object BusinessSingleTransactionLimit : PixTransactionError("PIXe-003", PixErrorType.TEMPORARY)
    data object BusinessDailyLimit : PixTransactionError("PIXe-004", PixErrorType.TEMPORARY)
    data object BusinessSameValuePaymentsExceeded : PixTransactionError("PIXe-005", PixErrorType.TEMPORARY)
    data object SettlementDestinationAccountNotFound : PixTransactionError("PIXe-006", PixErrorType.PERMANENT)
    data object SettlementDestinationAccountNotAvailable : PixTransactionError("PIXe-007", PixErrorType.PERMANENT)
    data object SettlementGenericPermanentError : PixTransactionError("PIXe-008", PixErrorType.PERMANENT)
    data object SettlementGenericTemporaryError : PixTransactionError("PIXe-009", PixErrorType.TEMPORARY)
    data object SettlementPaymentRefusedByDestination : PixTransactionError("PIXe-010", PixErrorType.TEMPORARY)
    data object SettlementDestinationInstitutionNotAllowed : PixTransactionError("PIXe-011", PixErrorType.PERMANENT)
    data object SettlementDestinationAccountTypeInvalid : PixTransactionError("PIXe-012", PixErrorType.PERMANENT)
    data object SettlementDestinationNotAllowedAmount : PixTransactionError("PIXe-013", PixErrorType.PERMANENT)
    data object UnknownTemporaryError : PixTransactionError("PIXe-014", PixErrorType.TEMPORARY)
    data object InvalidPixkey : PixTransactionError("PIXe-015", PixErrorType.PERMANENT)
    data object PaymentSourceAccountLocked : PixTransactionError(code = "PIXe-016", type = PixErrorType.TEMPORARY)
    data object SystemUnavailable : PixTransactionError("PIXe-017", PixErrorType.TEMPORARY)
    data object AccountFundsBlocked : PixTransactionError("PIXe-018", PixErrorType.TEMPORARY)
    data object DeviceFingerprintError : PixTransactionError("PIXe-019", PixErrorType.TEMPORARY)
    data object QrCodeRejectedByRecipient : PixTransactionError("PIXe-020", PixErrorType.PERMANENT)
}

data class PixQrCode(val value: String, val amount: Long)

data class PixStatementItem(
    override val flow: BankStatementItemFlow,
    val transactionId: QrCodeTransactionId?,
    val endToEnd: EndToEnd?,
    override val date: LocalDate,
    override val type: BankStatementItemType,
    override val description: String,
    override val operationNumber: String,
    override val isTemporaryOperationNumber: Boolean,
    override val amount: Long,
    override val counterpartName: String,
    override val counterpartDocument: String,
    override val counterpartAccountNo: String? = null,
    override val counterpartBankName: String? = null,
    override val documentNumber: String,
    override val ref: String? = null,
    override val lastUpdate: ZonedDateTime? = null,
    override val metadata: BankStatementMetadata? = null,
    override val notificatedAt: ZonedDateTime? = null,
) : BankStatementItem

data class RegisterPixKeyCommand(
    val accountNo: AccountNumber,
    val key: PixKey,
    val document: String,
    val name: String,
    val walletId: WalletId,
)

class PixKeyAlreadyExistsException : Exception()

interface AutomaticPixQrCodeRecurringDataExtractor {
    fun extractQRCodeRecurringData(rawData: Any): Result<Any?>
}