package ai.friday.billpayment.app.payment.receipt.builder // ktlint-disable filename

import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillPaymentScheduled
import ai.friday.billpayment.app.payment.MultiplePaymentData
import ai.friday.billpayment.app.payment.NoPaymentData
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.ReceiptPaymentData
import ai.friday.billpayment.app.payment.ReceiptPaymentType
import ai.friday.billpayment.app.payment.SinglePaymentData
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.Wallet
import java.math.BigInteger

fun Wallet.getScheduler(bill: Bill): Member {
    val lastScheduleEvent = bill.history.lastOrNull { event -> event is BillPaymentScheduled } ?: return this.founder
    return when (lastScheduleEvent.actionSource) {
        is ActionSource.Api -> this.getMember((lastScheduleEvent.actionSource as ActionSource.Api).accountId)
        else -> this.founder
    }
}

fun Transaction.toPaymentList(): List<ReceiptPaymentData> {
    return when (paymentData) {
        is SinglePaymentData -> paymentData.toPaymentList()
        is MultiplePaymentData -> paymentData.toPaymentList()
        NoPaymentData -> throw IllegalStateException("NoPaymentData não pode ser usado aqui")
    }
}

private fun SinglePaymentData.toReceiptPaymentData() = ReceiptPaymentData(
    amount = details.netAmountTotal(),
    type = when (details) {
        is PaymentMethodsDetailWithBalance -> ReceiptPaymentType.BALANCE
        is PaymentMethodsDetailWithCreditCard -> ReceiptPaymentType.CREDIT_CARD
        else -> ReceiptPaymentType.OTHERS
    },
    accountPaymentMethodId = accountPaymentMethod.id,
)

private fun SinglePaymentData.toPaymentList() = listOf(toReceiptPaymentData())

private fun MultiplePaymentData.toPaymentList() = payments.map {
    it.toReceiptPaymentData()
}

data class PayeeBankDetails(
    val ispb: String?,
    val routingNo: Long,
    val accountNo: BigInteger,
    val accountDv: String,
    val accountType: AccountType,
)

internal data class PayerFinancialInfo(
    val payerBankAccount: InternalBankAccount?,
    val payerFinancialInstitution: FinancialInstitution?,
    val paymentPartnerName: String?,
    val paymentPartnerDocument: String?,
)