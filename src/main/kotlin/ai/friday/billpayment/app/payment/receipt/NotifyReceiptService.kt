package ai.friday.billpayment.app.payment.receipt

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.ReceiptFileRepository
import ai.friday.billpayment.app.integrations.ReceiptRepository
import ai.friday.billpayment.app.integrations.ReceiptTemplateCompiler
import ai.friday.billpayment.app.payment.AutomaticPixReceiptData
import ai.friday.billpayment.app.payment.BoletoReceiptData
import ai.friday.billpayment.app.payment.InvestmentReceiptData
import ai.friday.billpayment.app.payment.InvoiceReceiptData
import ai.friday.billpayment.app.payment.PixReceiptData
import ai.friday.billpayment.app.payment.ReceiptData
import ai.friday.billpayment.app.payment.ReceiptFilesData
import ai.friday.billpayment.app.payment.receipt.builder.ReceiptDataBuilderService
import ai.friday.billpayment.app.payment.receipt.builder.ReceiptDataSender
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletService
import arrow.core.getOrElse

interface NotifyReceiptService {
    fun notifyWithAsyncRetry(billPaid: BillPaid)
    fun notify(billPaid: BillPaid)
}

@FridayMePoupe
open class DefaultNotifyReceiptService(
    private val billEventRepository: BillEventRepository,
    private val receiptDataBuilderService: ReceiptDataBuilderService,
    private val receiptRepository: ReceiptRepository,
    private val walletService: WalletService,
    private val receiptDataSenders: List<ReceiptDataSender>,
    private val messagePublisher: MessagePublisher,
) : NotifyReceiptService {

    override fun notifyWithAsyncRetry(billPaid: BillPaid) {
        try {
            notify(billPaid)
        } catch (e: Exception) {
            val billPaidAsync = billPaid.copy(syncReceipt = false)
            messagePublisher.sendGenerateBillReceipt(billPaidAsync)
        }
    }

    override fun notify(billPaid: BillPaid) {
        billEventRepository.getBill(walletId = billPaid.walletId, billId = billPaid.billId).map { bill ->
            val wallet = walletService.findWallet(bill.paymentWalletId ?: bill.walletId)
            val receiptData = receiptDataBuilderService.getReceiptData(bill, billPaid, wallet)
            receiptRepository.save(receiptData)
            val membersToNotify = wallet.getMembersCanView(bill)
            receiptDataSenders.firstOrNull {
                it.canSendReceiptFor(receiptData)
            }?.notifyReceipt(bill, receiptData, membersToNotify, wallet) ?: throw IllegalStateException("No sender found for ${receiptData.javaClass.simpleName}")
        }.getOrElse { throw it }
    }
}

@FridayMePoupe
open class ReceiptTemplateCompilerFinderService(
    private val receiptTemplateCompilers: List<ReceiptTemplateCompiler>,
) {
    fun findReceiptTemplateCompiler(receiptData: ReceiptData): ReceiptTemplateCompiler {
        return receiptTemplateCompilers.firstOrNull {
            it.canBuildFor(receiptData)
        } ?: throw IllegalStateException("No compiler found for ${receiptData.javaClass.simpleName}")
    }
}

@FridayMePoupe
open class DefaultReceiptDataSenderService(
    private val notificationAdapter: NotificationAdapter,
    private val receiptFileRepository: ReceiptFileRepository,
    private val receiptTemplateCompilerFinderService: ReceiptTemplateCompilerFinderService,
    private val walletService: WalletService,
) : ReceiptDataSender {

    override fun canSendReceiptFor(receiptData: ReceiptData): Boolean {
        return when (receiptData) {
            is BoletoReceiptData,
            is InvoiceReceiptData,
            is PixReceiptData,
            is AutomaticPixReceiptData,
            -> true

            is InvestmentReceiptData -> false
        }
    }

    override fun notifyReceipt(bill: Bill, receiptData: ReceiptData, membersToNotify: List<Member>, wallet: Wallet) {
        val wallet = walletService.findWallet(receiptData.walletId)
        val receiptFilesData = receiptData.generateReceiptFiles(wallet)
        val receiptMailHtml = receiptData.toMail()

        when (receiptData) {
            is InvoiceReceiptData -> notificationAdapter.notifyInvoiceReceipt(
                members = membersToNotify,
                receiptData = receiptData,
                receiptFilesData = receiptFilesData,
                mailReceiptHtml = receiptMailHtml,
            )

            is PixReceiptData -> notificationAdapter.notifyPixReceipt(
                members = membersToNotify,
                receiptData = receiptData,
                receiptFilesData = receiptFilesData,
                mailReceiptHtml = receiptMailHtml,
            )

            is BoletoReceiptData -> notificationAdapter.notifyBoletoReceipt(
                members = membersToNotify,
                receiptData = receiptData,
                receiptFilesData = receiptFilesData,
                mailReceiptHtml = receiptMailHtml,
            )

            is InvestmentReceiptData -> throw IllegalStateException("Investment receipt cannot be sent here")
            is AutomaticPixReceiptData -> notificationAdapter.notifyAutomaticPixReceipt(
                members = membersToNotify,
                receiptData = receiptData,
                receiptFilesData = receiptFilesData,
                mailReceiptHtml = receiptMailHtml,
            )
        }
    }

    private fun ReceiptData.toMail() =
        receiptTemplateCompilerFinderService.findReceiptTemplateCompiler(this).buildReceiptMailHtml(this) // TODO - adicionar campos do QRCODE

    private fun ReceiptData.generateReceiptFiles(wallet: Wallet): ReceiptFilesData { // TODO - adicionar campos do QRCODE
        val receiptHtml = receiptTemplateCompilerFinderService.findReceiptTemplateCompiler(this).buildReceiptHtml(this, wallet)
        return receiptFileRepository.generateReceiptFiles(this, receiptHtml)
    }
}