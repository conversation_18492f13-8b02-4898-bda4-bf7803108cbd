package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPayer
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.bill.checkCanBePaidWith
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.findAuthor
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@FridayMePoupe
open class TransactionService(
    private val accountRepository: AccountRepository,
    private val updateBillService: UpdateBillService,
    private val notificationAdapter: NotificationAdapter,
    private val walletService: WalletService,
    private val transactionRepository: TransactionRepository,
) {

    fun save(transaction: Transaction) {
        transactionRepository.save(transaction)
    }

    fun findTransactionsByBill(billId: BillId, status: TransactionStatus): List<Transaction> {
        return transactionRepository.findTransactions(billId, status)
    }

    open fun findTransactionById(transactionId: TransactionId): Transaction {
        return transactionRepository.findById(transactionId)
    }

    open fun findByWalletAndStatusAndType(
        walletId: WalletId,
        transactionStatus: TransactionStatus,
        transactionType: TransactionType,
    ): List<Transaction> {
        return transactionRepository.findByWalletAndStatusAndType(
            walletId = walletId,
            transactionStatus = transactionStatus,
            transactionType = transactionType,
        )
    }

    open fun findTransactionByIdAndWalletIdAndAccountId(
        transactionId: TransactionId,
        walletId: WalletId,
        accountId: AccountId,
        transactionType: TransactionType? = null,
    ): Transaction {
        val transaction = transactionRepository.findByIdAndWallet(
            transactionId,
            walletId,
            transactionType,
        )

        if (!checkTransactionOwnership(transaction, walletId, accountId)) {
            throw ItemNotFoundException("transaction ${transactionId.value} not found for wallet ${walletId.value} and accountId ${accountId.value}${transactionType.let { " with type $it" }}")
        }

        return transaction
    }

    open fun checkTransactionOwnership(
        transaction: Transaction,
        walletId: WalletId,
        accountId: AccountId,
    ): Boolean {
        return transaction.payer.accountId == accountId && transaction.walletId == walletId
    }

    fun notify(transaction: Transaction) {
        try {
            with(transaction) {
                if (this.isInvoice() && status == TransactionStatus.UNDONE) {
                    val bill = settlementData.getTarget<Bill>()
                    val wallet = walletService.findWallet(bill.walletId)
                    notificationAdapter.notifyInvoicePaymentUndone(
                        members = wallet.getMembersCanView(bill),
                        walletName = wallet.name,
                        author = bill.findAuthor(wallet),
                        recipient = bill.recipient!!,
                        amount = bill.amount,
                        settleDate = created.toLocalDate(),
                    )
                }
            }
        } catch (e: Exception) {
            LOG.error(append("transactionId", transaction.id.value), "TransactionServiceNotify", e)
        }
    }

    private fun Transaction.isInvoice() = settlementData.getTarget<Bill>().billType == BillType.INVOICE

    fun initBillPaymentTransaction(command: BillPaymentCommand): Transaction {
        val wallet = walletService.findWallet(command.walletId)
        val nsu = incrementNSU(wallet.founder.accountId)

        val bill = updateBillService.getBill(command.billId)
        val member = bill.scheduledBy?.let { wallet.getActiveMemberOrNull(it) }

        val paymentData =
            getPaymentData(founder = wallet.founder, member = member, paymentMethodDetail = command.paymentDetails, bill = bill)

        if (bill.billType != BillType.AUTOMATIC_PIX) {
            if (command.actionSource == ActionSource.Scheduled && !bill.isPaymentScheduled()) {
                throw PaymentNotScheduledException()
            }

            if (bill.amountTotal != paymentData.netAmountTotal()) {
                throw InvalidTransactionPaymentValueException("Valor líquido utilizado nos métodos de pagamento é diferente do valor total da bill.")
            }
        }

        if (bill.status == BillStatus.PROCESSING) {
            throw BillProcessingException(bill.billId)
        }

        if (bill.status != BillStatus.ACTIVE) {
            throw BillNotPayableException(bill.status)
        }

        val type = when (bill.billType) {
            BillType.CONCESSIONARIA, BillType.FICHA_COMPENSACAO -> TransactionType.BOLETO_PAYMENT
            BillType.INVOICE, BillType.PIX -> TransactionType.DIRECT_INVOICE
            BillType.INVESTMENT -> TransactionType.GOAL_INVESTMENT
            BillType.AUTOMATIC_PIX -> TransactionType.AUTOMATIC_PIX
            BillType.OTHERS -> throw IllegalArgumentException("Tipo de conta não suportado")
        }

        return Transaction(
            type = type,
            payer = wallet.founder.toPayer(),
            paymentData = paymentData,
            settlementData = SettlementData(
                settlementTarget = bill,
                serviceAmountTax = 0,
                totalAmount = bill.amountTotal,
            ),
            nsu = nsu.toLong(),
            actionSource = command.actionSource,
            walletId = command.walletId,
        ).also { transactionRepository.save(it) }
    }

    private fun incrementNSU(accountId: AccountId): Int {
        return accountRepository.incrementNSU(accountId)
    }

    private fun getPaymentData(
        founder: Member,
        member: Member?,
        paymentMethodDetail: PaymentMethodsDetail,
        bill: Bill,
    ): PaymentData {
        val methods = paymentMethodDetail.retrieveAllPaymentMethodsDetail().map {
            val paymentMethodOwner = when (it) {
                is PaymentMethodsDetailWithBalance -> founder
                is PaymentMethodsDetailWithExternalPayment -> founder
                is PaymentMethodsDetailWithCreditCard -> member ?: throw InvalidTransactionPaymentDataException("Pagamento com cartão precisa do membro que agendou, dado que o cartão é dele")
            }

            val accountPaymentMethodId =
                it.paymentMethodId()
                    ?: throw InvalidTransactionPaymentDataException("Transação não pode ser iniciada sem paymentMethodId")

            val accountPaymentMethod = accountRepository.findAccountPaymentMethodByIdAndAccountId(
                accountPaymentMethodId,
                paymentMethodOwner.accountId,
                AccountPaymentMethodStatus.ACTIVE,
            )

            if (!bill.checkCanBePaidWith(accountPaymentMethod.method.type, paymentMethodOwner)) {
                throw InvalidTransactionPaymentDataException("Esta bill não pode ser paga com o metodo de pagamento informado")
            }

            if (!it.canSettleWith(accountPaymentMethod)) {
                throw InvalidTransactionPaymentDataException("Este agendamento não pode ser liquidado com o metodo de pagamento informado")
            }

            SinglePaymentData(
                accountPaymentMethod = accountPaymentMethod,
                details = it,
            )
        }

        return if (methods.size == 1) {
            methods.first()
        } else {
            MultiplePaymentData(methods)
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(TransactionService::class.java)
    }
}

fun Account.toPayer() = Payer(accountId = accountId, document = document, name = name)

fun Member.toPayer() = Payer(accountId = accountId, document = document, name = name)

fun Member.toBillPayer() = BillPayer(document = document, name = name, alias = null)

enum class TransactionError(val description: String, val code: String) {
    SETTLEMENT_VALIDATION("Não foi possível validar a conta. Por favor, tente novamente", "4101"),
    SETTLEMENT_CONFIRMATION("Não foi possível completar a operação. Por favor, tente novamente", "4102"),
    TED("TED não realizado. Por favor, tente novamente", "4103"),
    GENERIC_EXCEPTION("Não foi possível completar a operação. Por favor, tente novamente", "4105"),
    TED_UNDONE("TED devolvido.", "4106"),
}