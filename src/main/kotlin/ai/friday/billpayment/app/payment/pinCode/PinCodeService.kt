package ai.friday.billpayment.app.payment.pinCode

import ai.friday.billpayment.app.account.AccountId
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Secondary
import jakarta.inject.Singleton

@Singleton
interface PinCodeRepository {
    fun validate(accountId: AccountId, pinCode: PinCode): ValidationResult
}

@Secondary
@Singleton
class DefaultPinCodeRepository : PinCodeRepository {
    override fun validate(accountId: AccountId, pinCode: PinCode) = ValidationResult(valid = true, maxAttemptsReached = false)
}

@Singleton
class PinCodeService(
    private val repository: PinCodeRepository,
    @Property(name = "features.pinCode.enabled", defaultValue = "false")
    private val pinCodeEnabled: Boolean,
) {
    fun validate(accountId: AccountId, pinCode: PinCode?): Result<ValidationResult> = runCatching {
        when {
            !pinCodeEnabled -> ValidationResult(valid = true, maxAttemptsReached = false) // disabled always return true
            pinCode == null -> ValidationResult(valid = false, maxAttemptsReached = false) // false for invalid pinCode
            else -> repository.validate(accountId, pinCode) // validation result
        }
    }
}

data class ValidationResult(
    val valid: Boolean,
    val maxAttemptsReached: Boolean,
)