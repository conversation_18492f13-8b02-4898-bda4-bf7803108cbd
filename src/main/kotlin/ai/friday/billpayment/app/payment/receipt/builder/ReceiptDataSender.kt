package ai.friday.billpayment.app.payment.receipt.builder

import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.payment.ReceiptData
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.Wallet

interface ReceiptDataSender {
    fun canSendReceiptFor(receiptData: ReceiptData): Boolean
    fun notifyReceipt(bill: Bill, receiptData: ReceiptData, membersToNotify: List<Member>, wallet: Wallet)
}