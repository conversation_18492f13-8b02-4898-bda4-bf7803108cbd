package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.Acquirer
import ai.friday.billpayment.app.account.CreditCardPaymentStatus
import ai.friday.billpayment.app.account.CreditCardPaymentStatus.ABORTED
import ai.friday.billpayment.app.account.CreditCardPaymentStatus.DENIED
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.BankOperationStatus.REFUNDED
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.cashIn.FraudPreventionErrors

data class FraudPreventionPaymentOperationDenied(
    val error: FraudPreventionErrors,
) : PaymentOperation {
    override fun hasError() = true
    override fun getErrorMessage() = error.name
    override fun status() = PaymentStatus.ERROR
    override fun toErrorSource() = ErrorSource.FRAUD_PREVENTION
    override fun isRetryable() = status() == PaymentStatus.ERROR
}

data class CreditCardAuthorization(
    val acquirer: Acquirer,
    val transactionId: String,
    val status: CreditCardPaymentStatus,
    val acquirerTid: String? = null,
    val amount: Long? = null,
    val acquirerReturnCode: String? = null,
    val acquirerReturnMessage: String,
    val tid: String? = null,
    val authorizationCode: String? = null,
    val originalAcquirer: String? = null,
) : PaymentOperation {
    override fun hasError(): Boolean {
        return status in listOf(DENIED, ABORTED)
    }

    override fun getErrorMessage() = acquirerReturnMessage
    override fun status(): PaymentStatus {
        return when (status) {
            CreditCardPaymentStatus.PAYMENT_CONFIRMED -> PaymentStatus.SUCCESS
            CreditCardPaymentStatus.REFUNDED -> PaymentStatus.REFUNDED
            DENIED -> PaymentStatus.ERROR
            CreditCardPaymentStatus.AUTHORIZED -> PaymentStatus.AUTHORIZED
            ABORTED, CreditCardPaymentStatus.VOIDED -> PaymentStatus.UNKNOWN
        }
    }

    override fun toErrorSource() = ErrorSource.ACQUIRER

    override fun isRetryable() = status() == PaymentStatus.ERROR
}

data class BalanceAuthorization(
    val operationId: BankOperationId = BankOperationId.build(),
    var status: BankOperationStatus,
    val timeout: Boolean = false,
    val amount: Long,
    val errorDescription: String = "",
    val ongoingRefundOperationId: BankOperationId? = null,
    val paymentGateway: FinancialServiceGateway,
) : PaymentOperation {

    override fun hasError(): Boolean {
        return status.isError() || timeout
    }

    override fun getErrorMessage(): String {
        return if (timeout) {
            PaymentError.BALANCE_TIMEOUT.description
        } else {
            errorDescription
        }
    }

    override fun status(): PaymentStatus {
        return when (status) {
            BankOperationStatus.SUCCESS -> PaymentStatus.SUCCESS
            REFUNDED -> PaymentStatus.REFUNDED

            BankOperationStatus.ERROR,
            BankOperationStatus.INSUFFICIENT_FUNDS,
            BankOperationStatus.INVALID_DATA,
            -> PaymentStatus.ERROR

            BankOperationStatus.TIMEOUT,
            BankOperationStatus.UNKNOWN,
            -> PaymentStatus.UNKNOWN
        }
    }

    override fun toErrorSource(): ErrorSource = when (paymentGateway) {
        FinancialServiceGateway.CELCOIN -> throw IllegalStateException("Invalid payment gateway: $paymentGateway")
        FinancialServiceGateway.ARBI -> ErrorSource.PAYMENT_ARBI
        FinancialServiceGateway.XP -> ErrorSource.XP
        FinancialServiceGateway.FRIDAY -> ErrorSource.FRIDAY
        FinancialServiceGateway.ORIGINAL -> ErrorSource.ORIGINAL
    }

    override fun isRetryable() = status() == PaymentStatus.ERROR

    fun refund() {
        status = REFUNDED
    }
}

enum class BankName { ARBI }

interface PaymentOperation {
    fun hasError(): Boolean
    fun getErrorMessage(): String

    fun status(): PaymentStatus

    fun toErrorSource(): ErrorSource

    fun isRetryable(): Boolean
}

enum class PaymentStatus { SUCCESS, ERROR, UNKNOWN, REFUNDED, AUTHORIZED }