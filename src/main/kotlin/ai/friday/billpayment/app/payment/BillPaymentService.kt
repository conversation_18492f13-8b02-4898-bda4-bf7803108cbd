package ai.friday.billpayment.app.payment

import ai.friday.billpayment.adapters.lock.transactionLockProvider
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.payment.transaction.CompleteTransaction
import ai.friday.billpayment.app.payment.transaction.FailTransaction
import ai.friday.billpayment.app.payment.transaction.PrepareTransaction
import ai.friday.billpayment.app.payment.transaction.RollbackTransaction
import ai.friday.billpayment.app.payment.transaction.StartTransaction
import ai.friday.billpayment.app.payment.transaction.TransactionInProcess
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.withEphemeralLock
import ai.friday.morning.log.andAppend
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Named
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
open class BillPaymentService(
    private val transactionService: TransactionService,
    private val checkoutLocator: CheckoutLocator,
    private val startTransaction: StartTransaction,
    private val prepareTransaction: PrepareTransaction,
    private val completeTransaction: CompleteTransaction,
    private val failTransaction: FailTransaction,
    private val transactionInProcess: TransactionInProcess,
    private val rollbackTransaction: RollbackTransaction,
    @Named(transactionLockProvider) private val lockProvider: InternalLock,
) {
    private val logger = LoggerFactory.getLogger(BillPaymentService::class.java)

    @NewSpan
    open fun execute(command: BillPaymentCommand): TransactionId {
        return startTransaction.execute(command).id
    }

    fun process(transaction: Transaction) {
        lockProvider.withEphemeralLock(transaction.id.value) {
            if (transaction.status == TransactionStatus.PROCESSING) {
                prepareTransaction.execute(transaction)
                transactionService.save(transaction)

                if (transaction.status == TransactionStatus.PROCESSING) {
                    val checkout = checkoutLocator.getCheckout(transaction)

                    checkout.execute(transaction)

                    if (checkout is SyncCheckout) {
                        transactionService.save(transaction)
                    }
                }

                handleResult(transaction)
            } else {
                val markers = Markers.append("accountId", transaction.payer.accountId.value)
                    .andAppend("transactionId", transaction.id.value)
                    .andAppend("status", transaction.status)
                    .andAppend("nothingToDo", true)
                logger.warn(markers, "BillPaymentService#process")
            }
        }.getOrThrow()
    }

    fun continueSettlement(transactionId: TransactionId, settlementOperation: SettlementOperation) {
        lockProvider.withEphemeralLock(transactionId.value) {
            val transaction = transactionService.findTransactionById(transactionId)

            if (transaction.status == TransactionStatus.PROCESSING) {
                when (val checkout = checkoutLocator.getCheckout(transaction)) {
                    is AsyncSettlementCheckout -> checkout.continueSettlement(transaction, settlementOperation)
                    is SyncCheckout, is AsyncCheckout -> throw IllegalStateException("Unable to proceed with this transaction")
                }

                handleResult(transaction, true)
            } else {
                val markers = Markers.append("accountId", transaction.payer.accountId.value)
                    .andAppend("transactionId", transaction.id.value)
                    .andAppend("status", transaction.status)
                    .andAppend("nothingToDo", true)
                logger.warn(markers, "BillPaymentService#continueSettlement")
            }
        }.getOrThrow()
    }

    private fun handleResult(transaction: Transaction, continuingSettlement: Boolean = false) {
        when (transaction.status) {
            TransactionStatus.FAILED -> if (continuingSettlement) {
                rollbackTransaction.rollback(transaction)
            } else {
                failTransaction.execute(transaction)
            }

            TransactionStatus.COMPLETED -> completeTransaction.execute(transaction)

            TransactionStatus.PROCESSING -> transactionInProcess.execute(transaction)

            else -> throw IllegalStateException("Transaction should not have any other status")
        }
    }
}

data class BillPaymentCommand(
    val walletId: WalletId,
    val billId: BillId,
    val actionSource: ActionSource,
    val paymentDetails: PaymentMethodsDetail,
)