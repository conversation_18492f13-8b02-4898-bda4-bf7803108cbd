package ai.friday.billpayment.app.payment.receipt

import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.integrations.ReceiptTemplateCompiler
import ai.friday.billpayment.app.payment.ReceiptData
import ai.friday.billpayment.app.wallet.Wallet

// @Singleton
// @Requires(notEnv = [FRIDAY_ENV, MODATTA_ENV])
class EmptyReceiptTemplateCompiler : ReceiptTemplateCompiler {
    override fun canBuildFor(receiptData: ReceiptData): Boolean {
        return false
    }

    override fun buildReceiptHtml(receiptData: ReceiptData, wallet: Wallet) = CompiledHtml("")

    override fun buildReceiptMailHtml(receiptData: ReceiptData) = CompiledHtml("")
}