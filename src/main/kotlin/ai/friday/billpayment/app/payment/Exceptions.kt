package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus

class BoletoSettlementException(val gateway: FinancialServiceGateway, message: String? = null, e: Exception? = null) :
    RuntimeException(message, e)

open class CreditCardPaymentException : RuntimeException {
    constructor(
        returnCode: String?,
        returnMessage: String?,
    ) : super(String.format("Acquirer returnCode: %s returnMessage: %s", returnCode, returnMessage))

    constructor(e: Exception) : super(e)
}

@Deprecated("Use RetryTransactionException instead")
class TransactionRollbackException : RuntimeException()

class RetryForeverTransactionException(val transactionId: TransactionId, val retryStrategy: TransactionRetryStrategy) : Exception()

class BillProcessingException(billId: BillId) : RuntimeException(billId.value)

class InvalidTransactionPaymentDataException(message: String) : RuntimeException(message)
class InvalidTransactionPaymentValueException(message: String) : RuntimeException(message)

class PaymentNotScheduledException : RuntimeException()

class BillNotPayableException(status: BillStatus) : RuntimeException("Bill is not payable. Actual status: $status")