package ai.friday.billpayment.app.payment.transaction

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.PaymentStarted
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.payment.BillPaymentCommand
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionService

@FridayMePoupe
class StartTransaction(
    private val transactionService: TransactionService,
    private val billEventPublisher: BillEventPublisher,
) {

    fun execute(command: BillPaymentCommand): Transaction {
        val transaction = transactionService.initBillPaymentTransaction(command)
        billEventPublisher.publish(
            transaction.settlementData.getTarget(),
            PaymentStarted(
                billId = transaction.settlementData.getTarget<Bill>().billId,
                walletId = transaction.settlementData.getTarget<Bill>().walletId,
                transactionId = transaction.id,
                correlationId = transaction.correlationId,
                actionSource = command.actionSource,
            ),
        )
        return transaction
    }
}