package ai.friday.billpayment.app.payment.receipt

import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.integrations.ReceiptFileRepository
import ai.friday.billpayment.app.payment.ReceiptData
import ai.friday.billpayment.app.payment.ReceiptFiles
import ai.friday.billpayment.app.payment.ReceiptFilesData

// @Singleton
// @Requires(notEnv = [FRIDAY_ENV, MODATTA_ENV])
class EmptyReceiptFileRepository : ReceiptFileRepository {
    override fun generateReceiptFiles(
        receiptData: ReceiptData,
        receiptHtml: CompiledHtml,
    ) = ReceiptFilesData(
        imageBytes = ByteArray(0),
        imageFormat = "png",
        pdfBytes = ByteArray(0),
        imageUrl = null,
        pdfUrl = null,
    )

    override fun generateLinks(receiptData: ReceiptData) = ReceiptFiles(null, null)

    override fun hasReceiptFilesStored(receiptData: ReceiptData): Boolean {
        return false
    }
}