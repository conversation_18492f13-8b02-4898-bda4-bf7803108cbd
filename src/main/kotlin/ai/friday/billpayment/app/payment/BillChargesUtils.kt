package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.FinancialInstitutionGlobalData
import java.time.LocalDate

object BillChargesUtils {
    fun interestEffectiveDueDate(interest: InterestData, dueDate: LocalDate): LocalDate = with(interest) {
        val effectiveDueDate = calculateClosestWorkingDay(dueDate).plusDays(1)
        val date = if (type == InterestType.VALUE) date else if (date != null) calculateClosestWorkingDay(date) else null
        return maxOf(date ?: effectiveDueDate, effectiveDueDate)
    }

    fun fineEffectiveDueDate(fine: FineData, dueDate: LocalDate): LocalDate = with(fine) {
        val effectiveDueDate = calculateClosestWorkingDay(dueDate).plusDays(1)
        return maxOf(date ?: effectiveDueDate, effectiveDueDate)
    }

    fun discountEffectiveDate(discount: DiscountData): LocalDate? = with(discount) {
        return listOfNotNull(date1, date2, date3).minByOrNull { it }
    }

    fun calculateClosestWorkingDay(date: LocalDate): LocalDate {
        var currentLocalDate = date
        while (!FinancialInstitutionGlobalData.isBusinessDay(currentLocalDate)) {
            currentLocalDate = currentLocalDate.plusDays(1)
        }
        return currentLocalDate
    }
}