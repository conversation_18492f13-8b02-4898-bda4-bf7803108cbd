package ai.friday.billpayment.app.payment

import ai.friday.billpayment.PrintableSealedClass
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillEventDependency
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.bill.isCreditCard
import ai.friday.billpayment.app.getOrFalse
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import java.math.BigDecimal
import java.time.LocalDate
import kotlin.contracts.ExperimentalContracts
import kotlin.contracts.contract
import net.logstash.logback.marker.Markers
import org.slf4j.Logger
import org.slf4j.LoggerFactory

const val NU_BANK_DOCUMENT = "**************"
const val MAX_PAYMENT_LIMIT_AMOUNT = 250_000_00L

sealed class BillValidationStatus : PrintableSealedClass() {
    class UnableToValidate(
        val reason: UnableToValidateReason = UnableToValidateReason.SERVER_ERROR,
        val shouldRetryValidation: Boolean = true,
    ) :
        BillValidationStatus()

    data object NotPayable : BillValidationStatus()
    data object AlreadyPaid : BillValidationStatus()

    data object PaymentNotAuthorized : BillValidationStatus()
    data object Payable : BillValidationStatus()
}

enum class UnableToValidateReason {
    SERVER_ERROR, BARCODE_NOT_FOUND
}

data class BillRegisterData(
    val billType: BillType,
    val assignor: String,
    val recipient: Recipient? = null,
    val recipientChain: RecipientChain? = null,
    val payerDocument: String?,
    val amount: Long,
    val discount: Long = 0,
    val interest: Long = 0,
    val fine: Long = 0,
    val amountTotal: Long,
    val expirationDate: LocalDate? = null,
    val dueDate: LocalDate? = null,
    val paymentLimitTime: String? = null,
    val settleDate: LocalDate? = null,
    val fichaCompensacaoType: FichaCompensacaoType? = null,
    val payerName: String? = null,
    val amountCalculationModel: AmountCalculationModel,
    val interestData: InterestData? = null,
    val fineData: FineData? = null,
    val discountData: DiscountData? = null,
    @Deprecated(
        replaceWith = ReplaceWith("rebate"),
        message = "use long version instead",
    )
    val abatement: Double? = null,
    val amountPaid: Long? = null,
    val paidDate: LocalDate? = null,
    val idNumber: String? = null, // Campo na CIP = NumIdentcTit
    val registrationUpdateNumber: Long? = null,
    val barCode: BarCode? = null,
    val rebate: Long? = null,
    val amountCalculationValidUntil: LocalDate? = null,
    val divergentPayment: DivergentPayment? = null,
    val partialPayment: PartialPayment? = null,
)

@BillEventDependency
data class PartialPayment(
    val acceptPartialPayment: Boolean,
    val qtdPagamentoParcial: Int,
    val qtdPagamentoParcialRegistrado: Int,
    val saldoAtualPagamento: Long,
)

@BillEventDependency
data class DivergentPayment(
    val minimumAmount: Long? = null,
    val maximumAmount: Long? = null,
    val amountType: PartialPaymentAmountType? = null,
    val minimumPercentage: BigDecimal? = null,
    val maximumPercentage: BigDecimal? = null,
)

@BillEventDependency
enum class PartialPaymentAmountType {
    ANY_VALUE, BETWEEN_MINIMUM_AND_MAXIMUM, NOT_ACCEPT_DIFFERENT_VALUE, ONLY_MINIMUM_VALUE
}

data class FineData(
    val type: FineType? = null,
    val value: BigDecimal? = null,
    val date: LocalDate? = null,
)
fun FineData?.hasFine() = this?.type?.let {
    it != FineType.FREE
}.getOrFalse()

data class InterestData(
    val type: InterestType? = null,
    val value: BigDecimal? = null,
    val date: LocalDate? = null,
)

fun InterestData?.hasInterest() = this?.type?.let {
    it != InterestType.FREE
}.getOrFalse()

data class DiscountData(
    val type: DiscountType? = null,
    val value1: BigDecimal? = null,
    val date1: LocalDate? = null,
    val value2: BigDecimal? = null,
    val date2: LocalDate? = null,
    val value3: BigDecimal? = null,
    val date3: LocalDate? = null,
)

fun DiscountData?.hasDiscount() = this?.type?.let {
    it != DiscountType.FREE
}.getOrFalse()

@BillEventDependency
data class RecipientChain(
    val sacadorAvalista: Recipient? = null,
    val originalBeneficiary: Recipient? = null,
    val finalBeneficiary: Recipient? = null,
)

fun RecipientChain.getRecipientByPayer(cpfCnpjPagador: String, defaultAssignor: String): Recipient {
    if (sacadorAvalista.canBeUsedAsBeneficiary(cpfCnpjPagador)) {
        return sacadorAvalista
    }

    if (finalBeneficiary.canBeUsedAsBeneficiary(cpfCnpjPagador)) {
        return finalBeneficiary
    }

    if (originalBeneficiary.canBeUsedAsBeneficiary(cpfCnpjPagador)) {
        return originalBeneficiary
    }

    return Recipient(defaultAssignor)
}

@OptIn(ExperimentalContracts::class)
private fun Recipient?.canBeUsedAsBeneficiary(cpfCnpjPagador: String): Boolean {
    contract {
        returns(true) implies (this@canBeUsedAsBeneficiary != null)
    }

    return this != null && documentDoesNotMatch(cpfCnpjPagador) && nameIsValid()
}

private fun Recipient.documentDoesNotMatch(anotherDocument: String): Boolean {
    return !document.isNullOrBlank() && !document!!.endsWith(anotherDocument)
}

private fun Recipient.nameIsValid(): Boolean {
    return this.name.replace(Regex("\\d"), "").isNotBlank()
}

@BillEventDependency
enum class FichaCompensacaoType(private val code: Int) {
    CH_CHEQUE(1),
    DM_DUPLICATA_MERCANTIL(2),
    DMI_DUPLICATA_MERCANTIL_INDICACAO(3),
    DS_DUPLICATA_DE_SERVICO(4),
    DSI_DUPLICATA_DE_SERVICO_INDICACAO(5),
    DR_DUPLICATA_RURAL(6),
    LC_LETRA_DE_CAMBIO(7),
    NCC_NOTA_DE_CREDITO_COMERCIAL(8),
    NCE_NOTA_DE_CREDITO_EXPORTACAO(9),
    NCI_NOTA_DE_CREDITO_INDUSTRIAL(10),
    NCR_NOTA_DE_CREDITO_RURAL(11),
    NP_NOTA_PROMISSORIA(12),
    NPR_NOTA_PROMISSORIA_RURAL(13),
    TM_TRIPLICATA_MERCANTIL(14),
    TS_TRIPLICATA_DE_SERVICO(15),
    NS_NOTA_DE_SEGURO(16),
    RC_RECIBO(17),
    FAT_BLOQUETO(18),
    ND_NOTA_DE_DEBITO(19),
    AP_APOLICE_DE_SEGURO(20),
    ME_MENSALIDADE_ESCOLAR(21),
    PC_PARCELA_DE_CONSORCIO(22),
    NF_NOTA_FISCAL(23),
    DD_DOCUMENTO_DE_DIVIDA(24),
    CEDULA_DE_PRODUTO_RURAL(25),
    WARRANT(26),
    DIVIDA_ATIVA_DE_ESTADO(27),
    DIVIDA_ATIVA_DE_MUNICIPIO(28),
    DIVIDA_ATIVA_DA_UNIAO(29),
    ENCARGOS_CONDOMINIAIS(30),
    CARTAO_DE_CREDITO(31),
    BOLETO_PROPOSTA(32),
    DEPOSITO_E_APORTE(33),
    OUTROS(99),
    ;

    fun isCreditCard() = this == CARTAO_DE_CREDITO

    companion object {
        val values = entries.associateBy { it.code }
        val logger: Logger = LoggerFactory.getLogger(FichaCompensacaoType::class.java)

        @JvmStatic
        fun findByCode(code: Int?): FichaCompensacaoType {
            code?.let { values[it]?.let { v -> return v } }

            logger.error(Markers.append("invalidCode", code), "FichaCompensacaoType")
            return OUTROS
        }
    }
}

@BillEventDependency
enum class AmountCalculationModel(val code: String) {
    UNKNOWN(""), ANYONE("01"), BENEFICIARY_AFTER_DUE_DATE("02"), BENEFICIARY_ONLY("03"), ON_DEMAND("04");

    companion object {
        fun getByCode(code: String?) = values().firstOrNull() {
            it.code == code
        } ?: UNKNOWN
    }
}

@BillEventDependency
enum class InterestType(val code: String) {
    VALUE("1"),
    PERCENT_BY_DAY("2"),
    PERCENT_BY_MONTH("3"),
    PERCENT_BY_YEAR("4"),
    FREE("5"),
    VALUE_WORKING_DAYS("6"),
    PERCENT_BY_DAY_WORKING_DAYS("7"),
    PERCENT_BY_MONTH_WORKING_DAYS("8"),
    PERCENT_BY_YEAR_WORKING_DAYS("9"),
    UNKNOWN(""),
    ;

    companion object {
        fun getByCode(code: String): InterestType {
            return values().firstOrNull() {
                it.code == code
            } ?: UNKNOWN
        }
    }
}

fun InterestType.isSupported() =
    listOf(InterestType.FREE, InterestType.VALUE, InterestType.PERCENT_BY_MONTH).contains(this)

@BillEventDependency
enum class FineType(val code: String) {
    VALUE("1"),
    PERCENT("2"),
    FREE("3"),
    UNKNOWN(""),
    ;

    companion object {
        fun getByCode(code: String): FineType {
            return values().firstOrNull() {
                it.code == code
            } ?: UNKNOWN
        }
    }
}

fun FineType.isSupported() = listOf(FineType.FREE, FineType.VALUE, FineType.PERCENT).contains(this)

@BillEventDependency
enum class DiscountType(val code: String) {
    FREE("0"),
    FIXED_UNTIL_DATE("1"),
    PERCENT_UNTIL_DATE("2"),
    VALUE_BY_DAY("3"),
    VALUE_BY_WORKING_DAY("4"),
    PERCENT_BY_DAY("5"),
    PERCENT_BY_WORKING_DAY("6"),
    UNKNOWN(""),
    ;

    companion object {
        fun getByCode(code: String): DiscountType {
            return values().firstOrNull() {
                it.code == code
            } ?: UNKNOWN
        }
    }
}

fun DiscountType.isSupported() = listOf(
    DiscountType.FREE,
    DiscountType.FIXED_UNTIL_DATE,
    DiscountType.PERCENT_UNTIL_DATE,
    DiscountType.VALUE_BY_DAY,
).contains(this)

abstract class BillValidationResponse(
    open val gateway: FinancialServiceGateway,
    open val billRegisterData: BillRegisterData?,
    open val bankTransactionId: String = "",
    open val errorDescription: String? = null,
    open val errorCode: String? = null,
) {

    fun getStatus(ignoreAmountPaid: Boolean = false): BillValidationStatus {
        return when {
            isValid() -> BillValidationStatus.Payable
            alreadyPaid() -> {
                if (ignoreAmountPaid) {
                    BillValidationStatus.Payable
                } else {
                    BillValidationStatus.AlreadyPaid
                }
            }

            paymentNotAuthorized() -> BillValidationStatus.PaymentNotAuthorized
            notPayable() -> BillValidationStatus.NotPayable
            isBarcodeNotFound() -> BillValidationStatus.UnableToValidate(
                UnableToValidateReason.BARCODE_NOT_FOUND,
            )

            else -> BillValidationStatus.UnableToValidate(shouldRetryValidation = isRetryable())
        }
    } // é válido, é pagável, nao autorizado, nao pagavel,

    fun isEmptyAmount() =
        billRegisterData?.amountTotal == 0L

    fun hasExceededAmountLimit() =
        billRegisterData?.amountTotal?.let { it > MAX_PAYMENT_LIMIT_AMOUNT } ?: false

    fun isCreditCard() = isCreditCard(billRegisterData?.fichaCompensacaoType, billRegisterData?.recipient)

    abstract fun isRegistrationResponseOlderThan(registrationUpdateNumber: Long): Boolean

    abstract fun isValid(): Boolean

    abstract fun paymentNotAuthorized(): Boolean

    fun isPaymentLimitExpired(): Boolean {
        return billRegisterData?.expirationDate?.isBefore(getLocalDate()) ?: false
    }

    abstract fun notPayable(): Boolean

    abstract fun alreadyPaid(): Boolean

    abstract fun isBarcodeNotFound(): Boolean

    abstract fun isRetryable(): Boolean
}

data class BillRegisterStatus(
    val notPayable: Boolean,
    val alreadyPaid: Boolean,
    val barcodeNotFound: Boolean = false,
)

data class DefaultBillValidationResponse(
    override val billRegisterData: BillRegisterData?,
    override val gateway: FinancialServiceGateway,
    override val errorDescription: String? = null,
    val billRegisterStatus: BillRegisterStatus,
    val retryable: Boolean = false,
) : BillValidationResponse(
    gateway = gateway,
    billRegisterData = billRegisterData,
) {
    override fun isRegistrationResponseOlderThan(registrationUpdateNumber: Long): Boolean {
        return false
    }

    private fun isError(): Boolean = !errorDescription.isNullOrBlank()

    override fun isValid(): Boolean = when {
        notPayable() -> false
        alreadyPaid() -> false
        isBarcodeNotFound() -> false
        isError() -> false
        isPaymentLimitExpired() -> false
        else -> true
    }

    override fun paymentNotAuthorized(): Boolean = false

    override fun notPayable(): Boolean = billRegisterStatus.notPayable || isPaymentLimitExpired()

    override fun alreadyPaid(): Boolean = billRegisterStatus.alreadyPaid

    override fun isBarcodeNotFound(): Boolean = billRegisterStatus.barcodeNotFound

    override fun isRetryable(): Boolean = retryable
}

enum class SettlementValidationAdapterErrorCode {
    ALREADY_PAID,
    AUTHENTICATION_FAIL,
    BARCODE_NOT_FOUND,
    NOT_AUTHORIZED,
    NOT_PAYABLE,
    UNKNOWN,
    ;

    companion object {
        private val values = entries.associateBy { it.name }

        fun of(source: String?) = values[source] ?: UNKNOWN
    }

    fun isRetryable() = when (this) {
        ALREADY_PAID,
        BARCODE_NOT_FOUND,
        NOT_AUTHORIZED,
        NOT_PAYABLE,
        -> false

        AUTHENTICATION_FAIL,
        UNKNOWN,
        -> true
    }
}

data class SettlementValidationResponse(
    val transactionId: String,
    override val billRegisterData: BillRegisterData? = null,
    override val gateway: FinancialServiceGateway,
    override val errorDescription: String? = null,
    val gatewayResponseCode: String? = null,
) : BillValidationResponse(
    billRegisterData = billRegisterData,
    bankTransactionId = transactionId,
    errorDescription = errorDescription,
    errorCode = gatewayResponseCode,
    gateway = gateway,
) {
    override fun isRegistrationResponseOlderThan(registrationUpdateNumber: Long): Boolean = false

    override fun isValid(): Boolean = billRegisterData != null && !isPaymentLimitExpired()

    override fun paymentNotAuthorized(): Boolean = gatewayResponseCode == SettlementValidationAdapterErrorCode.NOT_AUTHORIZED.name

    override fun notPayable(): Boolean = gatewayResponseCode == SettlementValidationAdapterErrorCode.NOT_PAYABLE.name || isPaymentLimitExpired()

    override fun alreadyPaid(): Boolean = gatewayResponseCode == SettlementValidationAdapterErrorCode.ALREADY_PAID.name

    override fun isBarcodeNotFound(): Boolean = gatewayResponseCode == SettlementValidationAdapterErrorCode.BARCODE_NOT_FOUND.name

    override fun isRetryable(): Boolean = gatewayResponseCode == SettlementValidationAdapterErrorCode.UNKNOWN.name
}