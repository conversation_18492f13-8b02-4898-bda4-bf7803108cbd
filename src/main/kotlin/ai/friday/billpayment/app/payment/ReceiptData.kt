package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPayer
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.wallet.WalletId
import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import java.math.BigInteger
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.regex.Pattern

enum class ReceiptPaymentType {
    CREDIT_CARD, BALANCE, OTHERS
}

data class ReceiptPaymentData(
    val amount: Long,
    val type: ReceiptPaymentType,
    val accountPaymentMethodId: AccountPaymentMethodId,
)

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME)
sealed class ReceiptData {
    abstract val billId: BillId
    abstract val walletId: WalletId
    abstract val source: ActionSource
    abstract val dateTime: ZonedDateTime
    abstract val totalAmount: Long
    abstract val walletName: String?
    abstract val scheduledBy: String?
    abstract val transactionId: TransactionId?
    abstract val payments: List<ReceiptPaymentData>?
    abstract val recipientName: String
}

@JsonTypeName("INVOICE")
data class InvoiceReceiptData(
    override val billId: BillId,
    @JsonAlias("accountId")
    override val walletId: WalletId,
    override val source: ActionSource,
    val authentication: String,
    override val dateTime: ZonedDateTime,
    val recipient: Recipient,
    override val totalAmount: Long,
    val purpose: String,
    val payer: BillPayer,
    val payeeBank: String,
    val paymentPartnerName: String?,
    val paymentPartnerDocument: String?,
    val payerBankAccount: InternalBankAccount? = null, // passar esse valor quando o TED for pago via ARBI
    val payerFinancialInstitution: FinancialInstitution? = null, // passar esse valor quando o TED for pago via ARBI
    override val walletName: String?,
    override val scheduledBy: String?,
    override val transactionId: TransactionId?,
    override val payments: List<ReceiptPaymentData>? = null,
) : ReceiptData() {
    override val recipientName = recipient.name
}

@JsonTypeName("PIX")
data class PixReceiptData(
    override val billId: BillId,
    @JsonAlias("accountId")
    override val walletId: WalletId,
    override val source: ActionSource,
    val authentication: String,
    override val dateTime: ZonedDateTime,
    val recipient: Recipient,
    override val totalAmount: Long,
    val purpose: String,
    val payer: BillPayer,
    val payeeFinancialInstitution: FinancialInstitution,
    val payerFinancialInstitution: FinancialInstitution,
    val payeeRoutingNo: Long,
    val payeeAccountNo: BigInteger,
    val payeeAccountDv: String,
    val payeeAccountType: AccountType,
    val payerBankAccount: InternalBankAccount,
    val description: String? = null,
    override val walletName: String?,
    override val scheduledBy: String?,
    override val transactionId: TransactionId?,
    override val payments: List<ReceiptPaymentData>? = null,
) : ReceiptData() {
    override val recipientName = recipient.name
}

@JsonTypeName("AUTOMATIC_PIX")
data class AutomaticPixReceiptData(
    val pixReceiptData: PixReceiptData,
    val contractNumber: String?,
    val automaticPixDescription: String?,
) : ReceiptData() {
    override val billId = pixReceiptData.billId
    override val walletId = pixReceiptData.walletId
    override val source = pixReceiptData.source
    override val dateTime = pixReceiptData.dateTime
    override val totalAmount = pixReceiptData.totalAmount
    override val walletName = pixReceiptData.walletName
    override val scheduledBy = pixReceiptData.scheduledBy
    override val transactionId = pixReceiptData.transactionId
    override val payments = pixReceiptData.payments
    override val recipientName = pixReceiptData.recipient.name
}

@JsonTypeName("BOLETO")
data class BoletoReceiptData(
    override val billId: BillId,
    @JsonAlias("accountId")
    override val walletId: WalletId,
    override val source: ActionSource,
    val authentication: String,
    override val dateTime: ZonedDateTime,
    val assignor: String,
    val recipient: Recipient?,
    override val totalAmount: Long,
    val payer: BillPayer?,
    val dueDate: LocalDate,
    val barcode: BarCode,
    override val walletName: String?,
    override val scheduledBy: String?,
    val paymentPartnerName: String?,
    override val transactionId: TransactionId?,
    override val payments: List<ReceiptPaymentData>? = null,
) : ReceiptData() {
    override val recipientName = recipient?.name ?: assignor
}

@JsonTypeName("INVESTMENT")
data class InvestmentReceiptData(
    override val billId: BillId,
    @JsonAlias("accountId")
    override val walletId: WalletId,
    override val source: ActionSource,
    override val dateTime: ZonedDateTime,
    override val totalAmount: Long,
    override val walletName: String?,
    override val scheduledBy: String?,
    override val transactionId: TransactionId?,
    override val payments: List<ReceiptPaymentData>? = null,
    val payer: BillPayer,
    val payerFinancialInstitution: FinancialInstitution,
    val payerBankAccount: InternalBankAccount,
    val goalId: String,
    val assignor: String,
    val productProvider: String,
    val productName: String,
    val productIndex: String,
    val productIndexPercentage: Int,
    val productInterestRateLabel: String,
    val positionId: String,
    val maturityDate: LocalDate,
    val goalEndDate: LocalDate?,
    val investmentGroupLabel: String?,
) : ReceiptData() {
    override val recipientName = assignor
}

class BillPaymentReceipt(
    val createdOn: String,
    val transactionId: String,
    val authentication: String,
    val paymentPartnerName: String?,
)

data class ReceiptFiles(val imageUrl: String?, val pdfUrl: String?)

class ReceiptFilesData(val imageBytes: ByteArray, val imageFormat: String, val pdfBytes: ByteArray, val imageUrl: String?, val pdfUrl: String?) {
    fun toReceiptFiles() = ReceiptFiles(imageUrl = imageUrl, pdfUrl = pdfUrl)
}

fun getAssignorDocument(receipt: String): String? {
    val regexp = "[0-9]{2}\\.[0-9]{3}\\.[0-9]{3}\\/[0-9]{4}\\-[0-9]{2}"
    val pattern = Pattern.compile(regexp)
    val matcher = pattern.matcher(receipt)
    return if (matcher.find()) matcher.group().replace("[^0-9]*".toRegex(), "") else ""
}