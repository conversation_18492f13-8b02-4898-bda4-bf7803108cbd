package ai.friday.billpayment.app.payment.transaction

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillPaymentScheduled
import ai.friday.billpayment.app.bill.PaymentFailed
import ai.friday.billpayment.app.bill.PaymentRefunded
import ai.friday.billpayment.app.bill.PaymentRefundedReason
import ai.friday.billpayment.app.bill.schedule.toScheduleBill
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.CheckoutLocator
import ai.friday.billpayment.app.payment.DirectTEDStatus
import ai.friday.billpayment.app.payment.PaymentSchedulingService
import ai.friday.billpayment.app.payment.ScheduleTo
import ai.friday.billpayment.app.payment.TEDStatus
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionRollbackException
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate

@FridayMePoupe
open class UndoTransaction(
    private val transactionService: TransactionService,
    private val billEventPublisher: BillEventPublisher,
    private val checkoutLocator: CheckoutLocator,
    private val paymentSchedulingService: PaymentSchedulingService,
    private val paymentFailedScheduleResolver: PaymentFailedScheduleResolver,
    private val walletRepository: WalletRepository,
) {

    fun undoDirectInvoice(transaction: Transaction, status: DirectTEDStatus) {
        val reason = status.toPaymentRefundedReason()
        transaction.settlementData.settlementOperation = transaction.settlementData.getOperation<BankTransfer>().copy(
            errorDescription = reason.name,
            status = when (status) {
                DirectTEDStatus.Error,
                DirectTEDStatus.Failure.INVALID_BANK_DATA,
                DirectTEDStatus.Failure.NOT_ALLOWED,
                -> BankOperationStatus.REFUNDED

                else -> throw IllegalStateException("DirectTEDStatus should not be $status")
            },
        )
        val paymentRefundedEvent = refund(transaction, reason)

        transaction.settlementData.getTarget<Bill>().let { bill ->
            if (transaction.isRetryable() && bill.isPaidBySchedule()) {
                val lastBillSchedule = bill.getLastSchedule()!!
                billEventPublisher.publish(
                    bill,
                    BillPaymentScheduled(
                        billId = lastBillSchedule.billId,
                        walletId = lastBillSchedule.walletId,
                        actionSource = lastBillSchedule.actionSource,
                        scheduledDate = lastBillSchedule.scheduledDate,
                        amount = lastBillSchedule.amount,
                        paymentLimitTime = lastBillSchedule.paymentLimitTime,
                        infoData = lastBillSchedule.infoData,
                        batchSchedulingId = lastBillSchedule.batchSchedulingId,
                        created = paymentRefundedEvent.created + 1, // NOTE: evita que este evento tenha o mesmo timestamp do evento de refund.
                    ),
                )

                val wallet = walletRepository.findWallet(lastBillSchedule.walletId)

                paymentSchedulingService.schedule(
                    bill.toScheduleBill(
                        paymentWalletId = lastBillSchedule.walletId,
                        paymentWalletFounder = wallet.founder,
                        scheduleDate = getLocalDate(),
                        scheduleTo = ScheduleTo.ASAP,
                        paymentMethodsDetail = lastBillSchedule.infoData.toSchedulePaymentDetails(),
                    ),
                )
            }
        }
    }

    @Deprecated("remover. só serve pra devolucao ted celcoin")
    @Synchronized
    fun execute(transactionId: TransactionId, tedResult: TEDStatus.Failure, retryable: Boolean) {
        val transaction = transactionService.findTransactionById(transactionId)
        transaction.settlementData.settlementOperation = transaction.settlementData.getOperation<BankTransfer>().copy(
            errorDescription = tedResult.message,
            status = if (retryable) BankOperationStatus.ERROR else BankOperationStatus.INVALID_DATA,
        )
        rollback(transaction)

        transaction.settlementData.getTarget<Bill>().let { bill ->
            if (retryable && bill.isPaidBySchedule()) {
                val lastBillSchedule = bill.getLastSchedule()!!
                billEventPublisher.publish(
                    bill,
                    BillPaymentScheduled(
                        billId = lastBillSchedule.billId,
                        walletId = lastBillSchedule.walletId,
                        actionSource = lastBillSchedule.actionSource,
                        scheduledDate = lastBillSchedule.scheduledDate,
                        amount = lastBillSchedule.amount,
                        paymentLimitTime = lastBillSchedule.paymentLimitTime,
                        infoData = lastBillSchedule.infoData,
                        batchSchedulingId = lastBillSchedule.batchSchedulingId,
                    ),
                )
            }
        }
    }

    private fun rollback(transaction: Transaction) {
        val checkout = checkoutLocator.getCheckout(transaction)
        checkout.rollbackTransaction(transaction)
        transactionService.save(transaction)
        if (transaction.status in listOf(TransactionStatus.PROCESSING, TransactionStatus.COMPLETED)) {
            throw TransactionRollbackException()
        }
        billEventPublisher.publish(
            transaction.settlementData.getTarget(),
            PaymentFailed(
                billId = transaction.settlementData.getTarget<Bill>().billId,
                walletId = transaction.settlementData.getTarget<Bill>().walletId,
                retryable = transaction.isRetryable(),
                errorDescription = transaction.getErrorDescription(),
                transactionId = transaction.id,
            ),
        )
        paymentFailedScheduleResolver.resolvePostPaymentFailedEvent(transaction)
        transactionService.notify(transaction)
    }

    private fun refund(transaction: Transaction, reason: PaymentRefundedReason): PaymentRefunded {
        transaction.status = TransactionStatus.UNDONE
        transactionService.save(transaction)
        val paymentRefundedEvent = PaymentRefunded(
            billId = transaction.settlementData.getTarget<Bill>().billId,
            walletId = transaction.settlementData.getTarget<Bill>().walletId,
            reason = reason,
            transactionId = transaction.id,
            gateway = transaction.settlementData.settlementOperation!!.gateway,
        )
        billEventPublisher.publish(
            transaction.settlementData.getTarget(),
            paymentRefundedEvent,
        )
        transactionService.notify(transaction)
        return paymentRefundedEvent
    }
}

private fun DirectTEDStatus.toPaymentRefundedReason() = when (this) {
    DirectTEDStatus.Failure.INVALID_BANK_DATA -> PaymentRefundedReason.INVALID_DATA
    DirectTEDStatus.Failure.NOT_ALLOWED -> PaymentRefundedReason.DESTINATION_NOT_ALLOWED_AMOUNT
    else -> PaymentRefundedReason.ERROR
}