package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillEventDependency
import ai.friday.billpayment.app.bill.PaymentRefundedReason
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixTransactionError
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import java.time.Duration
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.UUID
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

const val uuidSize = 36

data class Transaction(
    val id: TransactionId = TransactionId.build(),
    val created: ZonedDateTime = getZonedDateTime(),
    val walletId: WalletId,
    val type: TransactionType,
    val payer: Payer,
    val paymentData: PaymentData,
    val settlementData: SettlementData,
    val nsu: Long,
    val actionSource: ActionSource,
    var status: TransactionStatus = TransactionStatus.PROCESSING,
    val correlationId: String = id.removePrefix(),
) {

    fun getErrorDescription(): String {
        val paymentOperation = when (paymentData) {
            is SinglePaymentData -> paymentData.payment
            is MultiplePaymentData -> throw IllegalStateException("Pagamento multiplo ainda não está disponível")
            NoPaymentData -> null
        }

        val settlementOperation = settlementData.settlementOperation
        return when {
            paymentOperation != null && paymentOperation.hasError() -> paymentOperation.getErrorMessage()
            settlementOperation != null && settlementOperation.getErrorMessage()
                .isNotBlank() -> settlementOperation.getErrorMessage()

            else -> TransactionError.GENERIC_EXCEPTION.description
        }
    }

    fun getErrorSource(): ErrorSource {
        val paymentOperation = when (paymentData) {
            is SinglePaymentData -> paymentData.payment
            is MultiplePaymentData -> throw IllegalStateException("Pagamento multiplo ainda não está disponível")
            NoPaymentData -> null
        }
        val settlementOperation = settlementData.settlementOperation

        return when {
            paymentOperation != null && paymentOperation.hasError() -> paymentOperation.toErrorSource()
            settlementOperation != null && settlementOperation.getErrorMessage()
                .isNotBlank() -> settlementOperation.toErrorSource()

            else -> ErrorSource.UNKNOWN
        }
    }

    fun needValidation(): Boolean {
        return settlementData.getTarget<Bill>().billType.needValidation()
    }

    fun isRetryable(): Boolean {
        settlementData.isRetryable()?.let {
            return it || paymentData.isRetryable()
        } ?: return true
    }

    fun shouldCheckSettlementStatus(): Boolean {
        return settlementData.settlementOperation?.shouldCheckSettlementStatus() ?: false
    }

    fun rollback() {
        when (status) {
            TransactionStatus.PROCESSING, TransactionStatus.FAILED -> this.status = TransactionStatus.FAILED
            TransactionStatus.COMPLETED -> this.status = TransactionStatus.UNDONE
            TransactionStatus.UNDONE -> {
                logger.warn(append("status", status).andAppend("transactionId", id.value), "WHENUNKNOWNBRANCH#Transaction#rollback")
            }
        }
    }

    fun settlementOperationId(): Long? = runCatching {
        when (settlementData.settlementOperation) {
            is BoletoSettlementResult ->
                settlementData
                    .getOperation<BoletoSettlementResult>()
                    .bankTransactionId.toLongOrNull()

            is BankTransfer -> null
            else -> null
        }
    }.getOrNull()

    companion object {
        private val logger = LoggerFactory.getLogger(Transaction::class.java)
    }
}

fun TransactionId.removePrefix() = value.removePrefix("TRANSACTION-")

fun Transaction.settlementVoid(description: String): Transaction {
    (settlementData.settlementOperation as? BoletoSettlementResult)?.apply {
        status = BoletoSettlementStatus.VOIDED
        errorDescription = description
    }

    return this
}

enum class TransactionType {
    BOLETO_PAYMENT, INVOICE_PAYMENT, CASH_IN, DIRECT_INVOICE, GOAL_INVESTMENT, GOAL_REDEMPTION, AUTOMATIC_PIX
}

data class Payer(val accountId: AccountId, val document: String, val name: String) // TODO renomear um dos Payers

sealed interface PaymentData {
    fun retrieveAllCreditCardAuthorization(): List<Pair<PaymentMethodsDetailWithCreditCard, CreditCardAuthorization>>

    fun toSingle(): SinglePaymentData
    fun netAmountTotal(): Long

    fun status(): PaymentStatus?

    fun isRetryable(): Boolean
}

data object NoPaymentData : PaymentData {
    override fun retrieveAllCreditCardAuthorization(): List<Pair<PaymentMethodsDetailWithCreditCard, CreditCardAuthorization>> {
        return emptyList()
    }

    override fun toSingle(): SinglePaymentData {
        throw IllegalStateException("NoPaymentData não pode ser convertido para SinglePaymentData")
    }

    override fun netAmountTotal(): Long {
        return 0
    }

    override fun status(): PaymentStatus? {
        return null
    }

    override fun isRetryable(): Boolean {
        return false
    }
}

data class SinglePaymentData(
    val accountPaymentMethod: AccountPaymentMethod,
    val details: SinglePaymentMethodsDetail,
    var payment: PaymentOperation? = null,
) : PaymentData {
    fun <P : PaymentOperation> get(): P {
        @Suppress("UNCHECKED_CAST")
        return payment as P
    }

    override fun retrieveAllCreditCardAuthorization(): List<Pair<PaymentMethodsDetailWithCreditCard, CreditCardAuthorization>> {
        val payment = payment ?: return emptyList()
        return if (payment is CreditCardAuthorization && details is PaymentMethodsDetailWithCreditCard) {
            listOf(Pair(details, payment))
        } else {
            emptyList()
        }
    }

    fun paymentMethodType(): PaymentMethodType = accountPaymentMethod.method.type
    override fun toSingle() = this
    override fun netAmountTotal() = details.netAmountTotal()
    override fun status() = payment?.status()
    override fun isRetryable() = payment?.isRetryable() ?: false
}

data class MultiplePaymentData(val payments: List<SinglePaymentData>) : PaymentData {
    override fun retrieveAllCreditCardAuthorization(): List<Pair<PaymentMethodsDetailWithCreditCard, CreditCardAuthorization>> {
        return payments.map { it.retrieveAllCreditCardAuthorization() }.flatten()
    }

    override fun netAmountTotal() = payments.sumOf { it.netAmountTotal() }

    override fun toSingle() = throw IllegalStateException("AINDA NAO HA SUPORTE PARA TRANSACAO COM PAGAMENTO MULTIPLO")

    override fun status() = throw IllegalStateException("AINDA NAO HA SUPORTE PARA TRANSACAO COM PAGAMENTO MULTIPLO")

    override fun isRetryable() = throw IllegalStateException("AINDA NAO HA SUPORTE PARA TRANSACAO COM PAGAMENTO MULTIPLO")
}

interface SettlementTarget {
    fun settlementTargetId(): String
}

data class SettlementData(
    val settlementTarget: SettlementTarget,
    val serviceAmountTax: Long,
    val totalAmount: Long,
    var settlementOperation: SettlementOperation? = null,
) {
    fun <S : SettlementOperation> getOperation(): S {
        @Suppress("UNCHECKED_CAST")
        return settlementOperation as S
    }

    fun <S : SettlementTarget> getTarget(): S {
        @Suppress("UNCHECKED_CAST")
        return settlementTarget as S
    }

    fun isRetryable() = settlementOperation?.isRetryable()
}

interface SettlementOperation {
    val gateway: FinancialServiceGateway
    val authentication: String?

    fun isRetryable(): Boolean
    fun getErrorMessage(): String
    fun confirm()

    fun shouldCheckSettlementStatus(): Boolean

    fun toErrorSource(): ErrorSource {
        return when (gateway) {
            FinancialServiceGateway.CELCOIN,
            -> ErrorSource.SETTLEMENT_CELCOIN

            FinancialServiceGateway.ORIGINAL -> ErrorSource.ORIGINAL
            FinancialServiceGateway.ARBI -> ErrorSource.SETTLEMENT_ARBI
            FinancialServiceGateway.XP -> ErrorSource.XP
            FinancialServiceGateway.FRIDAY -> ErrorSource.FRIDAY
        }
    }

    fun settlementId(): String
}

data class PixPaymentResult(
    val pixKeyDetails: PixKeyDetails? = null,
    val status: PixPaymentStatus,
    val idOrdemPagamento: String,
    val endToEnd: String,
    val error: PixTransactionError? = null,
)

enum class PixPaymentStatus { ACKNOWLEDGED, UNKNOWN, SUCCESS, REFUSED, SCHEDULED, FAILED }

data class BoletoSettlementResult(
    override val gateway: FinancialServiceGateway,
    var status: BoletoSettlementStatus,
    var bankTransactionId: String,
    val externalNsu: Long,
    val externalTerminal: String,
    var errorCode: String,
    var errorDescription: String?,
    override var authentication: String? = null,
    var paymentPartnerName: String? = null,
    var finalPartnerName: FinancialServiceGateway? = null,
    var errorRetryable: Boolean = true,
) : SettlementOperation {

    fun init(status: BoletoSettlementStatus, bankTransactionId: String, errorDescription: String?) {
        this.status = status
        this.bankTransactionId = bankTransactionId
        this.errorDescription = errorDescription
        this.errorCode = if (status == BoletoSettlementStatus.AUTHORIZED) errorCode00 else errorCode01
    }

    override fun confirm() {
        status = BoletoSettlementStatus.CONFIRMED
    }

    override fun shouldCheckSettlementStatus(): Boolean {
        return gateway in listOf(
            FinancialServiceGateway.CELCOIN,
            FinancialServiceGateway.FRIDAY,
        )
    }

    override fun settlementId(): String {
        return this.externalNsu.toString()
    }

    fun void() {
        status = BoletoSettlementStatus.VOIDED
    }

    override fun getErrorMessage() = errorDescription.orEmpty()

    override fun isRetryable() = when (status) {
        BoletoSettlementStatus.VOIDED -> false
        else -> isError() && errorRetryable
    }

    private fun isError() = errorCode != errorCode00
}

fun Transaction.accountPaymentMethod() = paymentData.toSingle().accountPaymentMethod
fun Transaction.boletoSettlementOperation() = settlementData.getOperation<BoletoSettlementResult>()
fun Transaction.authentication() = settlementData.settlementOperation?.authentication
fun Transaction.bill() = settlementData.getTarget<Bill>()
fun Transaction.boletoSettlementTransactionId() = boletoSettlementOperation().bankTransactionId
fun Transaction.paymentMethodType() = accountPaymentMethod().method.type
fun Transaction.paymentDetails() = paymentData.toSingle().details

const val errorCode00 = "00" // Autorizado/validado
const val errorCode01 = "01" // Erro na validação/auth

internal fun TEDResult.toBankTransfer(): SettlementOperation {
    return when (this.status) {
        is TEDStatus.Success -> BankTransfer(
            amount = this.amount,
            gateway = this.gateway,
            status = BankOperationStatus.SUCCESS,
            authentication = this.authentication ?: "",
        )

        is TEDStatus.Failure -> BankTransfer(
            amount = this.amount,
            gateway = this.gateway,
            status = BankOperationStatus.ERROR,
            errorDescription = TransactionError.GENERIC_EXCEPTION.description,
        )

        is TEDStatus.InvalidData -> BankTransfer(
            amount = this.amount,
            gateway = this.gateway,
            status = BankOperationStatus.INVALID_DATA,
            errorDescription = this.status.message,
        )

        else -> BankTransfer(amount = this.amount, gateway = this.gateway, status = BankOperationStatus.ERROR)
    }
}

data class BankTransfer(
    val operationId: BankOperationId = BankOperationId.build(),
    override val gateway: FinancialServiceGateway,
    var status: BankOperationStatus,
    val amount: Long,
    override val authentication: String = "",
    var errorDescription: String = "",
    val debitOperationNumber: String? = null,
    val creditOperationNumber: String? = null,
    val pixKeyDetails: PixKeyDetails? = null,
) : SettlementOperation {

    override fun isRetryable() = when (status) {
        BankOperationStatus.REFUNDED -> errorDescription !in listOf(
            PaymentRefundedReason.INVALID_DATA.name,
            PaymentRefundedReason.DESTINATION_NOT_ALLOWED_AMOUNT.name,
        )

        BankOperationStatus.INVALID_DATA -> false
        else -> true
    }

    override fun getErrorMessage() = errorDescription
    override fun confirm() {
        this.status = BankOperationStatus.SUCCESS
        this.errorDescription = ""
    }

    override fun shouldCheckSettlementStatus(): Boolean {
        return status in listOf(
            BankOperationStatus.ERROR, // FIXME - nao deveria ser erro para fluxos retentaveis
            BankOperationStatus.UNKNOWN,
        )
    }

    override fun settlementId(): String {
        return this.operationId.value
    }
}

@BillEventDependency
data class TransactionId(val value: String) {
    companion object {
        fun build(uuid: String) = build(UUID.fromString(uuid))
        fun build(uuid: UUID = UUID.randomUUID()) = TransactionId("TRANSACTION-$uuid")
    }
}

@BillEventDependency
enum class ErrorSource {
    UNKNOWN, BANK, ACQUIRER, PAYMENT_CIELO, PAYMENT_ARBI, SETTLEMENT_CELCOIN, SETTLEMENT_ARBI, ORIGINAL, FRIDAY, FRAUD_PREVENTION, INVESTMENT_MANAGER, AUTOMATIC_PIX,

    @Deprecated("Mantido somente por causa do histórico no banco")
    SETTLEMENT_XP,

    @Deprecated("Mantido somente por causa do histórico no banco")
    XP,
}

fun ErrorSource.isSettlementError(): Boolean =
    this in listOf(ErrorSource.SETTLEMENT_CELCOIN, ErrorSource.SETTLEMENT_ARBI)

data class TEDResult(
    val gateway: FinancialServiceGateway,
    val amount: Long,
    val status: TEDStatus,
    val settleDate: LocalDate? = null,
    val bankTransactionId: Long? = 0,
    val receipt: String? = null,
    val authentication: String? = null,
    val refundDate: LocalDate? = null,
)

sealed class TEDStatus {
    data object Success : TEDStatus()
    class Failure(val message: String) : TEDStatus()
    class InvalidData(val message: String) : TEDStatus()
    data object Error : TEDStatus()
}

data class DirectTEDResult(
    val gateway: FinancialServiceGateway,
    val amount: Long,
    val status: DirectTEDStatus,
    val settleDate: LocalDate? = null,
    val operationId: BankOperationId,
    val authentication: String? = null,
    val refundDate: LocalDate? = null,
)

data class DirectTEDUndoneResult(
    val amount: Long,
    val status: DirectTEDStatus,
    val operationId: BankOperationId,
)

sealed interface DirectTEDStatus {
    data object Success : DirectTEDStatus
    enum class Failure : DirectTEDStatus {
        AFTER_HOURS, INSUFFICIENT_FUNDS, INVALID_BANK_DATA, NOT_ALLOWED
    }

    data object Unknown : DirectTEDStatus
    data object Error : DirectTEDStatus
}

enum class TransactionStatus {
    PROCESSING, FAILED, COMPLETED, UNDONE
}

enum class BoletoSettlementStatus {
    AUTHORIZED, UNAUTHORIZED, VOIDED, CONFIRMED, UNKNOWN, WAITING_CONFIRMATION;

    companion object {
        private val values = entries.associateBy { it.name }

        fun of(source: String?) = values[source] ?: UNKNOWN
    }
}

sealed class SettlementStatus {
    class Success(val authentication: String? = null) : SettlementStatus()
    class Failure(val message: String) : SettlementStatus()
    class InvalidData(val message: String) : SettlementStatus()
    data object AlreadyCompleted : SettlementStatus()
    data object Error : SettlementStatus()
    data object Processing : SettlementStatus()
}

sealed class TransactionRetryStrategy {

    data object Default : TransactionRetryStrategy() {
        override fun calculateDelay(sinceResolver: () -> ZonedDateTime): Duration {
            return Duration.ofMinutes(5)
        }
    }

    data class RepublishWithInterval(private val intervalConfig: List<TransactionRetryInterval>) : TransactionRetryStrategy() {
        private val sortedIntervalConfig = intervalConfig.sortedBy { it.upTo }
        override fun calculateDelay(sinceResolver: () -> ZonedDateTime): Duration {
            val durationWaiting = Duration.between(sinceResolver.invoke(), getZonedDateTime())
            return sortedIntervalConfig.dropWhile { durationWaiting > it.upTo }.firstOrNull()?.delay ?: sortedIntervalConfig.last().delay
        }
    }

    abstract fun calculateDelay(sinceResolver: () -> ZonedDateTime): Duration
}

data class TransactionRetryInterval(val upTo: Duration, val delay: Duration)

val increaseRetryInterval = TransactionRetryStrategy.RepublishWithInterval(
    listOf(
        TransactionRetryInterval(upTo = Duration.ofMinutes(1), delay = Duration.ofSeconds(10)),
        TransactionRetryInterval(upTo = Duration.ofMinutes(5), delay = Duration.ofSeconds(30)),
        TransactionRetryInterval(upTo = Duration.ofMinutes(10), delay = Duration.ofMinutes(1)),
        TransactionRetryInterval(upTo = Duration.ofHours(1), delay = Duration.ofMinutes(5)),
        TransactionRetryInterval(upTo = Duration.ofHours(2), delay = Duration.ofMinutes(10)),
        TransactionRetryInterval(upTo = Duration.ofHours(3), delay = Duration.ofMinutes(15)),
    ),
)