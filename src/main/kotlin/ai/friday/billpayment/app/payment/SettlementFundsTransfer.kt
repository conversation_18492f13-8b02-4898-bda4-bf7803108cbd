package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.bill.BillId
import java.time.LocalDateTime

data class SettlementFundsTransfer(
    val bankOperationId: BankOperationId,
    val billId: BillId,
    val status: BankOperationStatus,
    val amount: Long,
    val created: LocalDateTime,
    val transactionId: TransactionId,
    val sourceAccountType: SettlementFundsTransferType,
    val sameBank: Boolean?,
)

enum class SettlementFundsTransferType { SETTLEMENT_ACCOUNT, CASHIN_ACCOUNT, REFUND_CASHIN_ACCOUNT }