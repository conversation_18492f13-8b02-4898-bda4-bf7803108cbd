package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.ReceiptFileRepository
import ai.friday.billpayment.app.integrations.ReceiptRepository
import ai.friday.billpayment.app.payment.receipt.ReceiptTemplateCompilerFinderService
import ai.friday.billpayment.app.payment.receipt.builder.ReceiptDataBuilderService
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.canView
import arrow.core.Either
import arrow.core.left

const val INVOICE_PURPOSE = "01 - Crédito em Conta"

@FridayMePoupe
open class ReceiptService(
    private val receiptDataBuilderService: ReceiptDataBuilderService,
    private val billEventRepository: BillEventRepository,
    private val receiptFileRepository: ReceiptFileRepository,
    private val receiptTemplateCompilerFinderService: ReceiptTemplateCompilerFinderService,
    private val receiptRepository: ReceiptRepository,
    private val walletService: WalletService,
) {

    fun resolveReceiptFiles(receiptData: ReceiptData): Either<GetReceiptErrors, ReceiptFiles> {
        try {
            val receiptFiles = if (!receiptFileRepository.hasReceiptFilesStored(receiptData)) {
                val wallet = walletService.findWallet(receiptData.walletId)
                val receiptHtml = receiptTemplateCompilerFinderService.findReceiptTemplateCompiler(receiptData).buildReceiptHtml(
                    receiptData,
                    wallet,
                )
                receiptFileRepository.generateReceiptFiles(receiptData, receiptHtml).toReceiptFiles()
            } else {
                receiptFileRepository.generateLinks(receiptData)
            }
            return Either.Right(receiptFiles)
        } catch (e: Exception) {
            return Either.Left(GetReceiptErrors.ServerError(e))
        }
    }

    fun getReceipt(walletId: WalletId, billId: BillId, member: Member): Either<GetReceiptErrors, ReceiptData> {
        return billEventRepository.getBillById(billId).map { bill ->
            if (!member.canView(bill)) {
                return GetReceiptErrors.MemberNotAllowed.left()
            }
            if (bill.status != BillStatus.PAID) {
                return Either.Left(GetReceiptErrors.BillIsNotPaid)
            }
            try {
                receiptRepository.findById(bill.paymentWalletId ?: bill.walletId, billId)
            } catch (e: NoSuchElementException) {
                val wallet = walletService.findWallet(bill.paymentWalletId ?: walletId)
                val receiptData =
                    receiptDataBuilderService.getReceiptData(
                        bill = bill,
                        billPaid = bill.history.last { billEvent -> billEvent.eventType == BillEventType.PAID } as BillPaid,
                        wallet = wallet,
                    )
                receiptRepository.save(receiptData)

                return Either.Right(receiptData)
            } catch (e: Exception) {
                return Either.Left(GetReceiptErrors.ServerError(e))
            }
        }.mapLeft {
            when (it) {
                is ItemNotFoundException -> GetReceiptErrors.BillNotFound
                else -> GetReceiptErrors.ServerError(it)
            }
        }
    }
}

sealed class GetReceiptErrors {
    data object BillIsNotPaid : GetReceiptErrors()
    data object ReceiptNotReady : GetReceiptErrors()
    data object BillNotFound : GetReceiptErrors()
    data object MemberNotAllowed : GetReceiptErrors()
    class ServerError(val e: Exception) : GetReceiptErrors()
}