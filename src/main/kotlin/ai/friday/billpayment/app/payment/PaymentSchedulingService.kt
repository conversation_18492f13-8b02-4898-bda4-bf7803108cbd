package ai.friday.billpayment.app.payment

import ai.friday.billpayment.adapters.arbi.ArbiAccountMissingPermissionsException
import ai.friday.billpayment.adapters.arbi.ArbiInvalidBalanceException
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.ExternalPaymentMethod
import ai.friday.billpayment.app.account.PaymentMethod
import ai.friday.billpayment.app.account.PaymentMethodNotFound
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BatchSchedulingId
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.BillPaymentScheduleStarted
import ai.friday.billpayment.app.bill.BillPaymentScheduledBalanceInfo
import ai.friday.billpayment.app.bill.BillPaymentScheduledCreditCardInfo
import ai.friday.billpayment.app.bill.BillPaymentScheduledExternalPayment
import ai.friday.billpayment.app.bill.BillPaymentScheduledInfo
import ai.friday.billpayment.app.bill.BillPaymentScheduledWithMultiple
import ai.friday.billpayment.app.bill.BillSchedulePostponed
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.bill.SchedulePostponedReason
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.bill.billLockProvider
import ai.friday.billpayment.app.bill.walletLockProviderTag
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ScheduledBillPaymentService
import ai.friday.billpayment.app.integrations.ScheduledBillRepository
import ai.friday.billpayment.app.integrations.ScheduledWalletRepository
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.log
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Named
import java.time.LocalDate
import java.time.LocalTime
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@FridayMePoupe
class PaymentSchedulingService(
    private val scheduledBillRepository: ScheduledBillRepository,
    private val scheduledWalletRepository: ScheduledWalletRepository,
) {

    fun reschedule(billId: BillId, date: LocalDate): Either<RescheduleError, Unit> {
        try {
            val scheduledBills = scheduledBillRepository.findScheduledBillById(billId)
            scheduledBills
                .ifEmpty { return RescheduleError.ScheduleNotFound.left() }
                .forEach { scheduledBillRepository.delete(it) }

            schedule(scheduledBills.first().copy(scheduledDate = date))

            return Unit.right()
        } catch (e: Exception) {
            return RescheduleError.Unknown(e).left()
        }
    }

    fun schedule(scheduledBill: ScheduledBill) {
        tryCancelScheduledBill(scheduledBill.billId, null)
        scheduledBillRepository.save(scheduledBill)
        getMinScheduleDate(scheduledBill.walletId)?.let {
            scheduledWalletRepository.save(walletId = scheduledBill.walletId, minScheduleDate = it)
        }
    }

    fun cancelSchedule(walletId: WalletId, billId: BillId, batchSchedulingId: BatchSchedulingId?) {
        if (tryCancelScheduledBill(billId, batchSchedulingId)) {
            val minScheduleDate = getMinScheduleDate(walletId)

            LOG.info(
                log("accountId" to walletId.value, "billId" to billId.value, "minScheduleDate" to minScheduleDate),
                "cancelSchedule",
            )

            if (minScheduleDate == null) {
                scheduledWalletRepository.delete(walletId)
            } else {
                scheduledWalletRepository.save(walletId, minScheduleDate)
            }
        }
    }

    private fun getMinScheduleDate(walletId: WalletId): LocalDate? {
        return scheduledBillRepository.findAllScheduledBillsByWalletId(walletId)
            .minByOrNull { it.scheduledDate }
            ?.scheduledDate
    }

    fun cancelAllSchedules(walletId: WalletId) {
        scheduledBillRepository.findAllScheduledBillsByWalletId(walletId)
            .forEach { (_, billId) -> this.cancelSchedule(walletId, billId, null) }
    }

    private fun tryCancelScheduledBill(billId: BillId, batchSchedulingId: BatchSchedulingId?): Boolean {
        with(scheduledBillRepository) {
            val scheduledBills = findScheduledBillById(billId = billId)

            scheduledBills.filter {
                batchSchedulingId == null || it.batchSchedulingId == null || it.batchSchedulingId == batchSchedulingId
            }.ifEmpty { return false }
                .forEach { delete(it) }
            return true
        }
    }

    fun getScheduledBillsTotalAmount(
        walletId: WalletId,
        startDate: LocalDate?,
        endDate: LocalDate,
        filter: (bill: ScheduledBill) -> Boolean,
    ): Long {
        val bills = startDate?.let {
            scheduledBillRepository
                .findScheduledBillsByWalletIdBetween(walletId, startDate, endDate)
        } ?: scheduledBillRepository
            .findScheduledBillsByWalletIdAndUntilScheduledDate(walletId, endDate)

        val filtered = bills.filter { filter.invoke(it) }

        return filtered.sumAmount()
    }

    fun getOrderedScheduledBills(walletId: WalletId, scheduledDate: LocalDate): List<ScheduledBill> {
        /* Regras de ordenação:
            1- Data, mais antiga (vencida) primeiro
            2- Prioridade de tipos:
                - Impostos
                - Boletos
                - Invoice
                - PIX
            3- Valor, crescente
        */
        fun order(input: List<ScheduledBill>): List<ScheduledBill> {
            return input.sortedWith(
                compareBy(
                    { it.scheduledDate },
                    { it.billType.ordinal },
                    { it.amount },
                ),
            )
        }

        val bills = scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(
            walletId,
            scheduledDate,
        ).ifEmpty { return listOf() }

        return order(bills).also { list ->
            LOG.debug(
                append("bills", list.map { it.billId.value }).andAppend("walletId", walletId.value),
                "GetOrderedScheduledBills",
            )
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(PaymentSchedulingService::class.java)
    }
}

@FridayMePoupe
open class DefaultScheduledBillPaymentService(
    private val billPaymentService: BillPaymentService,
    private val balanceService: BalanceService,
    private val paymentSchedulingService: PaymentSchedulingService,
    private val updateBillService: UpdateBillService,
    private val walletService: WalletService,
    private val walletLimitsService: WalletLimitsService,
    private val scheduledBillPaymentServicePreProcessor: ScheduledBillPaymentServicePreProcessor,
    @Named(billLockProvider) private val lockProvider: InternalLock,
    @Named(walletLockProviderTag) private val walletLockProvider: InternalLock,
) : ScheduledBillPaymentService {

    @NewSpan
    override fun process(
        walletId: WalletId,
        scheduledDate: LocalDate,
        includeSubscription: Boolean,
    ) {
        val lock = walletLockProvider.acquireLock(walletId.value) ?: return

        try {
            val markers = append("walletId", walletId.value)
                .andAppend("scheduledDate", scheduledDate.format(dateFormat))
                .andAppend("includeSubscription", includeSubscription)

            scheduledBillPaymentServicePreProcessor.process(walletId, scheduledDate, includeSubscription)

            val orderedScheduledBills = paymentSchedulingService.getOrderedScheduledBills(walletId, scheduledDate)
                .ifEmpty { return }

            markers.andAppend("orderedScheduledBills", orderedScheduledBills.map { it.billId.value })

            val partitionByDateTime = getDateTimePartitions(orderedScheduledBills).also {
                it.expiredBills.publishUnscheduleExpired()
            }
            markers.andAppend("expiredBills", partitionByDateTime.expiredBills.map { it.billId.value })

            val partitionByLimit = getLimitPartitions(partitionByDateTime.readyBills, walletId).also {
                it.aboveLimitBills.publishUnscheduleHigherThanLimit()
                it.higherThanDailyLimitBills.publishUnscheduleHigherThanLimit()
                it.higherThanMonthlyLimitBills.publishUnscheduleHigherThanLimit()
            }
            markers.andAppend("aboveLimitBills", partitionByLimit.aboveLimitBills.map { it.billId.value })
            markers.andAppend(
                "higherThanDailyLimitBills",
                partitionByLimit.higherThanDailyLimitBills.map { it.billId.value },
            )
            markers.andAppend(
                "higherThanMonthlyLimitBills",
                partitionByLimit.higherThanMonthlyLimitBills.map { it.billId.value },
            )

            if (includeSubscription || partitionByLimit.bellowLimitBills.hasExpirableSchedule()) {
                val balancePartitions = getBalancePartitions(partitionByLimit.bellowLimitBills, walletId).also {
                    it.bellowLimitBills.startSchedulePayment()
                    it.aboveLimitBills.handleInsufficientFunds()
                }
                markers.andAppend("bellowLimitBills", balancePartitions.bellowLimitBills.map { it.billId.value })
                markers.andAppend(
                    "aboveLimitBills",
                    balancePartitions.aboveLimitBills.map { it.billId.value },
                )
            }

            LOG.info(markers, "PaymentScheduleServiceProcess")
        } finally {
            lock.unlock()
        }
    }

    private fun getBalance(accountId: AccountId, accountPaymentMethodId: AccountPaymentMethodId): Long {
        return try {
            balanceService.getBalanceFrom(
                accountId,
                accountPaymentMethodId,
            ).amount
        } catch (e: ArbiAccountMissingPermissionsException) {
            LOG.warn(
                append("accountId", accountId.value).and(append("paymentMethodId", accountPaymentMethodId.value)),
                "ScheduledBillPaymentProcess",
                e,
            )
            throw e
        } catch (e: ArbiInvalidBalanceException) {
            LOG.warn(
                append("accountId", accountId.value).and(append("paymentMethodId", accountPaymentMethodId.value)),
                "ScheduledBillPaymentProcess",
                e,
            )
            throw e
        } catch (e: Exception) {
            LOG.error(
                append("accountId", accountId.value).and(append("paymentMethodId", accountPaymentMethodId.value)),
                "ScheduledBillPaymentProcess",
                e,
            )
            throw e
        }
    }

    private fun List<ScheduledBill>.startSchedulePayment() {
        for (scheduledBill in this) {
            val command = BillPaymentCommand(
                scheduledBill.walletId,
                scheduledBill.billId,
                ActionSource.Scheduled,
                scheduledBill.paymentMethodsDetail,
            )
            val lock = lockProvider.acquireLock(scheduledBill.billId.value)
            if (lock == null) {
                LOG.warn(
                    append("command", command).and(append("error", "could not acquire lock for bill")),
                    "ScheduledBillPaymentProcess",
                )
                continue
            }
            try {
                LOG.info(append("command", command), "ScheduledBillPaymentProcess")
                val persistedBill = updateBillService.getBill(scheduledBill.billId)
                persistedBill.schedule?.let {
                    if (!it.ongoing) {
                        val event = BillPaymentScheduleStarted(
                            billId = persistedBill.billId,
                            walletId = persistedBill.walletId,
                            paymentWalletId = scheduledBill.walletId,
                            actionSource = ActionSource.System,
                        )
                        updateBillService.publishEvent(persistedBill, event)
                    }
                }
                billPaymentService.execute(command)
            } catch (e: Exception) {
                when (e) {
                    is BillProcessingException, is PaymentNotScheduledException, is InvalidTransactionPaymentValueException -> {
                        LOG.warn(
                            append("command", command).and(append("reason", e.javaClass.simpleName)),
                            "ScheduledBillPaymentProcess",
                        )
                    }

                    is BillNotPayableException -> {
                        LOG.warn(
                            append("command", command).and(append("reason", e.message)),
                            "ScheduledBillPaymentProcess",
                        )
                        paymentSchedulingService.cancelSchedule(
                            scheduledBill.walletId,
                            scheduledBill.billId,
                            scheduledBill.batchSchedulingId,
                        )
                    }

                    is PaymentMethodNotFound -> {
                        LOG.warn(
                            append("command", command).and(append("reason", e.message)),
                            "ScheduledBillPaymentProcess",
                        )
                        updateBillService.cancelScheduledPayment(
                            scheduledBill.walletId,
                            scheduledBill.billId,
                            ScheduleCanceledReason.CANT_PAY_WITH_CURRENT_CREDIT_CARD,
                        )
                    }

                    else -> LOG.error(append("command", command), "ScheduledBillPaymentProcess", e)
                }
            } finally {
                lock.unlock()
            }
        }
    }

    private fun List<ScheduledBill>.publishUnscheduleExpired() = forEach { scheduledBill ->
        val persistedBill = updateBillService.getBill(scheduledBill.billId)
        persistedBill.schedule?.let {
            if (persistedBill.status != BillStatus.PROCESSING) {
                updateBillService.publishEvent(
                    persistedBill,
                    BillPaymentScheduleCanceled(
                        billId = persistedBill.billId,
                        walletId = persistedBill.walletId,
                        actionSource = ActionSource.System,
                        reason = ScheduleCanceledReason.EXPIRATION,
                        batchSchedulingId = it.batchSchedulingId,
                    ),
                )
            }
        }
    }

    private fun List<ScheduledBill>.publishUnscheduleHigherThanLimit() = forEach { scheduledBill ->
        val persistedBill = updateBillService.getBill(scheduledBill.billId)
        persistedBill.schedule?.let {
            updateBillService.publishEvent(
                persistedBill,
                BillPaymentScheduleCanceled(
                    billId = persistedBill.billId,
                    walletId = persistedBill.walletId,
                    actionSource = ActionSource.System,
                    reason = ScheduleCanceledReason.AMOUNT_HIGHER_THAN_DAILY_LIMIT,
                    batchSchedulingId = it.batchSchedulingId,
                ),
            )
        }
    }

    private fun List<ScheduledBill>.handleInsufficientFunds() {
        asSequence().map {
            updateBillService.getBill(it.billId)
        }.filter {
            it.schedule?.waitingFunds == false
        }.forEach {
            updateBillService.publishEvent(
                it,
                BillSchedulePostponed(
                    billId = it.billId,
                    walletId = it.walletId,
                    actionSource = ActionSource.System,
                    reason = SchedulePostponedReason.INSUFFICIENT_FUNDS,
                ),
            )
        }
    }

    private fun List<ScheduledBill>.publishPostponeOutLimit(reason: SchedulePostponedReason) =
        forEach { scheduledBill ->
            val bill = updateBillService.getBill(scheduledBill.billId)
            updateBillService.publishEvent(
                bill,
                BillSchedulePostponed(
                    billId = bill.billId,
                    created = getZonedDateTime().toInstant().toEpochMilli(),
                    walletId = bill.walletId,
                    actionSource = ActionSource.System,
                    reason = reason,
                ),
            )
        }

    private fun getBalancePartitions(
        scheduledBills: List<ScheduledBill>,
        walletId: WalletId,
    ): BillsPartitionsByBalance {
        if (scheduledBills.isEmpty()) {
            return BillsPartitionsByBalance(
                bellowLimitBills = listOf(),
                aboveLimitBills = listOf(),
            )
        }

        val (checkBalanceBills, noCheckBalanceBills) = scheduledBills.partition {
            it.paymentMethodsDetail.retrieveBalancePaymentMethodIdAndAmount() != null
        }

        val (balanceEligibleScheduledBills, balanceNotEligibleScheduledBills) = if (checkBalanceBills.isNotEmpty()) {
            val wallet = walletService.findWallet(walletId)

            val balancePaymentMethodIdMap = checkBalanceBills.groupBy {
                val (accountPaymentMethodId, _) = it.paymentMethodsDetail.retrieveBalancePaymentMethodIdAndAmount()!!
                accountPaymentMethodId
            }
            getBalancePartitionsByPaymentMethodsMap(wallet, balancePaymentMethodIdMap)
        } else {
            Pair(emptyList(), emptyList())
        }

        return BillsPartitionsByBalance(
            bellowLimitBills = noCheckBalanceBills + balanceEligibleScheduledBills,
            aboveLimitBills = balanceNotEligibleScheduledBills,
        )
    }

    private fun getBalancePartitionsByPaymentMethodsMap(
        wallet: Wallet,
        balancePaymentMethodIdMap: Map<AccountPaymentMethodId, List<ScheduledBill>>,
    ): Pair<List<ScheduledBill>, List<ScheduledBill>> {
        val allPaymentMethodBills =
            balancePaymentMethodIdMap.map { (currentAccountPaymentMethodId, currentPaymentMethodScheduledBills) ->
                getBalancePartitionsByPaymentMethod(
                    wallet,
                    currentAccountPaymentMethodId,
                    currentPaymentMethodScheduledBills,
                )
            }
        return Pair(
            allPaymentMethodBills.flatMap { (balanceEligibleScheduledBills, _) -> balanceEligibleScheduledBills },
            allPaymentMethodBills.flatMap { (_, balanceNotEligibleScheduledBills) -> balanceNotEligibleScheduledBills },
        )
    }

    private fun getBalancePartitionsByPaymentMethod(
        wallet: Wallet,
        paymentMethodId: AccountPaymentMethodId,
        scheduledBills: List<ScheduledBill>,
    ): Pair<List<ScheduledBill>, List<ScheduledBill>> {
        var balance = getBalance(
            wallet.founder.accountId,
            paymentMethodId,
        )

        return scheduledBills.partition {
            val (balancePaymentMethodId, amountToCheck) = it.paymentMethodsDetail.retrieveBalancePaymentMethodIdAndAmount()!!

            if (balancePaymentMethodId != paymentMethodId) {
                throw IllegalStateException("Deveria haver apenas um metodo de pagamento com saldo na conta agendada")
            }

            if (balance >= amountToCheck) {
                balance -= amountToCheck
                true
            } else {
                false
            }
        }
    }

    private fun getDateTimePartitions(
        scheduledBills: List<ScheduledBill>,
    ): BillsPartitionsByDateTime {
        val (expiredBills, onDateBills) = scheduledBills.partition { scheduledBill ->
            scheduledBill.isExpired(tedLimitTime = LocalTime.parse(updateBillService.tedLimitTime))
        }

        val (tooEarlyToPay, onTimeToPay) = onDateBills.partition { scheduledBill ->
            scheduledBill.isTooEarlyToPay()
        }

        return BillsPartitionsByDateTime(
            notReadyYetBills = tooEarlyToPay,
            readyBills = onTimeToPay,
            expiredBills = expiredBills,
        )
    }

    private fun getLimitPartitions(
        scheduledBills: List<ScheduledBill>,
        walletId: WalletId,
    ): BillsPartitionByLimit {
        var availableLimit =
            walletLimitsService.getAvailableLimit(walletId = walletId)
        val (bellowLimitBills, outLimitScheduledBills) = scheduledBills.partition { scheduledBill ->
            if (scheduledBill.shouldCountOnTransferLimits()) {
                if (availableLimit >= scheduledBill.amount) {
                    availableLimit -= scheduledBill.amount
                    true
                } else {
                    false
                }
            } else {
                true
            }
        }

        val dailyLimit = walletLimitsService.getDailyLimit(walletId = walletId)

        val (aboveLimitBills, higherThanDailyLimitBills) = outLimitScheduledBills.partition { bill ->
            bill.amount <= dailyLimit
        }

        var availableMonthlyLimit = walletLimitsService.getAvailableMonthlyLimit(walletId = walletId)

        val (stillBelowLimitBills, aboveMonthlyLimitBills) = availableMonthlyLimit?.let {
            bellowLimitBills.partition { bill ->
                if (bill.shouldCountOnTransactionalLimits()) {
                    if (availableMonthlyLimit >= bill.amount) {
                        availableMonthlyLimit -= bill.amount
                        true
                    } else {
                        false
                    }
                } else {
                    true
                }
            }
        } ?: Pair(bellowLimitBills, emptyList())

        return BillsPartitionByLimit(
            bellowLimitBills = stillBelowLimitBills,
            aboveLimitBills = aboveLimitBills,
            higherThanDailyLimitBills = higherThanDailyLimitBills,
            higherThanMonthlyLimitBills = aboveMonthlyLimitBills,
        )
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ScheduledBillPaymentService::class.java)
    }
}

interface ScheduledBillPaymentServicePreProcessor {
    fun process(
        walletId: WalletId,
        scheduledDate: LocalDate,
        includeSubscription: Boolean,
    )
}

@FridayMePoupe
open class FridayScheduledBillPaymentServicePreProcessor(
    private val onePixPayService: OnePixPayService,
) : ScheduledBillPaymentServicePreProcessor {
    override fun process(walletId: WalletId, scheduledDate: LocalDate, includeSubscription: Boolean) {
        onePixPayService.process(walletId = walletId, paymentMethod = null, depositOnePixPayId = null)
    }
}

private fun List<ScheduledBill>.hasExpirableSchedule() = this.any { it.expires }

data class BillsPartitionsByBalance(
    val bellowLimitBills: List<ScheduledBill>,
    val aboveLimitBills: List<ScheduledBill> = emptyList(),
)

data class BillsPartitionsByDateTime(
    val notReadyYetBills: List<ScheduledBill>,
    val readyBills: List<ScheduledBill>,
    val expiredBills: List<ScheduledBill>,
)

data class BillsPartitionByLimit(
    val bellowLimitBills: List<ScheduledBill>,
    val aboveLimitBills: List<ScheduledBill> = emptyList(),
    val higherThanDailyLimitBills: List<ScheduledBill> = emptyList(),
    val higherThanMonthlyLimitBills: List<ScheduledBill> = emptyList(),
)

data class ScheduledBill(
    val walletId: WalletId,
    val billId: BillId,
    val scheduledDate: LocalDate,
    val billType: BillType,
    val amount: Long,
    val scheduleTo: ScheduleTo,
    val paymentLimitTime: LocalTime?,
    val paymentMethodsDetail: PaymentMethodsDetail,
    val expires: Boolean = true,
    val batchSchedulingId: BatchSchedulingId?,
    val isSelfTransfer: Boolean,
) {
    fun shouldCountOnTransferLimits(): Boolean {
        return !isSelfTransfer && billType.shouldCountOnTransferLimits()
    }

    fun shouldCountOnTransactionalLimits(): Boolean {
        return !isSelfTransfer && billType.shouldCountOnTransactionalLimits()
    }
}

fun ScheduledBill.isExpired(tedLimitTime: LocalTime): Boolean {
    return expires && (scheduledDate.isBefore(getZonedDateTime().toLocalDate()) || isTooLateToPay(tedLimitTime = tedLimitTime))
}

fun ScheduledBill.isTooLateToPay(tedLimitTime: LocalTime): Boolean {
    return when (billType) {
        BillType.CONCESSIONARIA, BillType.FICHA_COMPENSACAO, BillType.INVESTMENT -> paymentLimitTime?.isBefore(getZonedDateTime().toLocalTime())
            ?: false

        BillType.INVOICE -> tedLimitTime.isBefore(getZonedDateTime().toLocalTime())
        BillType.PIX -> false
        BillType.AUTOMATIC_PIX -> throw IllegalStateException("$billType não deveria estar agendado")
        BillType.OTHERS -> throw IllegalStateException("BillType.OTHERS Should never happen")
    }
}

fun ScheduledBill.isTooEarlyToPay(): Boolean {
    return when (billType) {
        BillType.INVOICE, BillType.FICHA_COMPENSACAO, BillType.CONCESSIONARIA, BillType.INVESTMENT -> getZonedDateTime().hour < 8
        BillType.PIX -> false
        BillType.AUTOMATIC_PIX -> throw IllegalStateException("$billType não deveria estar agendado")
        BillType.OTHERS -> throw IllegalStateException("BillType.OTHERS Should never happen")
    }
}

data class ScheduleStrategy private constructor(
    val schedulePriority: ScheduleTo,
    val scheduleDate: LocalDate? = null,
) {

    fun calculateScheduleDate(effectiveDueDate: LocalDate): LocalDate {
        val date = getZonedDateTime().toLocalDate()
        return when (schedulePriority) {
            ScheduleTo.ASAP, ScheduleTo.TODAY -> date
            ScheduleTo.DUE_DATE -> {
                if (effectiveDueDate.isBefore(date)) {
                    date
                } else {
                    effectiveDueDate
                }
            }

            ScheduleTo.TO_DATE -> scheduleDate!!
        }
    }

    fun canUpdateScheduleDate(paymentMethod: PaymentMethod): Boolean {
        return paymentMethod !is ExternalPaymentMethod
    }

    companion object {
        fun ofAsap(): ScheduleStrategy {
            return ScheduleStrategy(ScheduleTo.ASAP)
        }

        fun ofToday() = ScheduleStrategy(ScheduleTo.TODAY)

        fun ofDueDate(): ScheduleStrategy {
            return ScheduleStrategy(ScheduleTo.DUE_DATE)
        }

        fun ofDate(scheduleDate: LocalDate): ScheduleStrategy {
            return ScheduleStrategy(ScheduleTo.TO_DATE, scheduleDate)
        }
    }
}

sealed interface PaymentMethodsDetail {

    fun retrievePaymentMethodIds(): List<AccountPaymentMethodId>
    fun retrieveBalancePaymentMethodIdAndAmount(): Pair<AccountPaymentMethodId, Long>?
    fun toBillPaymentScheduledInfo(): BillPaymentScheduledInfo
    fun retrieveAllPaymentMethodsDetail(): List<SinglePaymentMethodsDetail>
    fun internalSettlement(): Boolean
    fun netAmountTotal(): Long
    fun mustSettlePaymentImmediately(): Boolean
}

sealed interface SinglePaymentMethodsDetail : PaymentMethodsDetail {
    override fun toBillPaymentScheduledInfo(): BillPaymentScheduledInfo.SingleBillPaymentScheduledInfo
    fun paymentMethodId(): AccountPaymentMethodId?
    override fun retrieveAllPaymentMethodsDetail() = listOf(this)
    fun canSettleWith(accountPaymentMethod: AccountPaymentMethod): Boolean
    fun methodType(): PaymentMethodType
}

data class PaymentMethodsDetailWithCreditCard(
    val paymentMethodId: AccountPaymentMethodId,
    val netAmount: Long,
    val feeAmount: Long,
    val installments: Int,
    val fee: Double,
    val calculationId: String?,
) : SinglePaymentMethodsDetail {

    val totalAmount = netAmount + feeAmount

    override fun internalSettlement(): Boolean {
        return true
    }

    override fun netAmountTotal() = netAmount
    override fun mustSettlePaymentImmediately() = true

    override fun retrievePaymentMethodIds() = listOf(paymentMethodId)

    override fun toBillPaymentScheduledInfo() =
        BillPaymentScheduledCreditCardInfo(
            paymentMethodId = paymentMethodId.value,
            feeAmount = feeAmount,
            netAmount = netAmount,
            installments = installments,
            fee = fee,
            calculationId = calculationId,
        )

    override fun paymentMethodId() = paymentMethodId
    override fun canSettleWith(accountPaymentMethod: AccountPaymentMethod) =
        PaymentMethodType.CREDIT_CARD == accountPaymentMethod.method.type

    override fun methodType() = PaymentMethodType.CREDIT_CARD

    override fun retrieveBalancePaymentMethodIdAndAmount() = null
}

data class PaymentMethodsDetailWithBalance(
    val amount: Long,
    val paymentMethodId: AccountPaymentMethodId,
    val calculationId: String?,
) : SinglePaymentMethodsDetail {

    override fun internalSettlement(): Boolean {
        return true
    }

    override fun retrievePaymentMethodIds() = listOf(paymentMethodId)

    override fun toBillPaymentScheduledInfo() = BillPaymentScheduledBalanceInfo(
        paymentMethodId = paymentMethodId.value,
        amount = amount,
        calculationId = calculationId,
    )

    override fun paymentMethodId() = paymentMethodId

    override fun retrieveBalancePaymentMethodIdAndAmount() = Pair(paymentMethodId, amount)

    override fun netAmountTotal() = amount

    override fun canSettleWith(accountPaymentMethod: AccountPaymentMethod) =
        listOf(PaymentMethodType.BALANCE, PaymentMethodType.EXTERNAL).contains(accountPaymentMethod.method.type)

    override fun mustSettlePaymentImmediately() = false

    override fun methodType() = PaymentMethodType.BALANCE
}

// FIXME - tavez fosse usado somente no PicPay, confirmar e remover
data class PaymentMethodsDetailWithExternalPayment(
    val providerName: AccountProviderName,
) : SinglePaymentMethodsDetail {
    override fun internalSettlement(): Boolean {
        return false
    }

    override fun netAmountTotal() = 0L

    override fun retrievePaymentMethodIds() = emptyList<AccountPaymentMethodId>()

    override fun paymentMethodId() = null

    override fun toBillPaymentScheduledInfo() = BillPaymentScheduledExternalPayment(
        providerName = providerName.name,
    )

    override fun retrieveBalancePaymentMethodIdAndAmount() = null

    override fun canSettleWith(accountPaymentMethod: AccountPaymentMethod) = false

    override fun mustSettlePaymentImmediately() = false

    override fun methodType() = PaymentMethodType.EXTERNAL
}

data class MultiplePaymentMethodsDetail(
    val methods: List<SinglePaymentMethodsDetail>,
) : PaymentMethodsDetail {
    override fun toBillPaymentScheduledInfo(): BillPaymentScheduledInfo.MultipleBillPaymentScheduledInfo =
        BillPaymentScheduledWithMultiple(methods = methods.map { it.toBillPaymentScheduledInfo() })

    override fun retrievePaymentMethodIds() = methods.map { it.retrievePaymentMethodIds() }.flatten()

    override fun internalSettlement() = methods.all { it.internalSettlement() }

    override fun netAmountTotal() = methods.sumOf { it.netAmountTotal() }

    override fun retrieveBalancePaymentMethodIdAndAmount() =
        methods.singleOrNull { it.retrieveBalancePaymentMethodIdAndAmount() != null }
            ?.retrieveBalancePaymentMethodIdAndAmount()

    override fun retrieveAllPaymentMethodsDetail() = methods.map { it.retrieveAllPaymentMethodsDetail() }.flatten()

    override fun mustSettlePaymentImmediately() = methods.any { it.mustSettlePaymentImmediately() }
}

enum class ScheduleTo(val priority: Int) {
    TODAY(-1),
    ASAP(0),
    DUE_DATE(1),
    TO_DATE(1),
}

fun List<ScheduledBill>.sumAmount() = this.fold(0L) { aggregate, current -> aggregate + current.amount }

sealed class RescheduleError {
    object ScheduleNotFound : RescheduleError()
    class Unknown(val exception: Exception) : RescheduleError()
}

// naive implementation extracted from Bill Controller
// TODO: check if this can result in a rounding problem
fun PaymentMethodsDetailWithCreditCard.calculateInstallmentValue(): Long = totalAmount / installments