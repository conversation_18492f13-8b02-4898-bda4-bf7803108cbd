package ai.friday.billpayment.app.payment

enum class PaymentError(val description: String) {
    INSUFFICIENT_FUNDS("Fundos insuficientes"),
    GENERIC_ERROR("Ocorreu um erro. Por favor, tente novamente"),
    BALANCE_TIMEOUT("Ocorreu um erro durante o pagamento. Por favor, tente novamente"),
}

class PaymentNotCanceled(returnCode: String?, returnMessage: String?) :
    CreditCardPaymentException(returnCode, returnMessage)

class PaymentNotEnabledToBeCanceled(returnCode: Int, returnMessage: String?) :
    CreditCardPaymentException(returnCode.toString(), returnMessage)

class PaymentNotCaptured(returnCode: String?, returnMessage: String?) :
    CreditCardPaymentException(returnCode, returnMessage)

class PaymentNotFound(message: String?) : CreditCardPaymentException(null, message)
class UnknownCreditCardPaymentException(e: Exception) : CreditCardPaymentException(e)