package ai.friday.billpayment.app.payment.transaction

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.BillSynchronizationStatus
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.payment.BillValidationResponse
import ai.friday.billpayment.app.payment.BillValidationStatus
import ai.friday.billpayment.app.payment.BoletoSettlementException
import ai.friday.billpayment.app.payment.BoletoSettlementResult
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.errorCode00
import ai.friday.billpayment.app.payment.errorCode01

@FridayMePoupe
class PrepareTransaction(
    private val updateBillService: UpdateBillService,
    private val boletoSettlementService: BoletoSettlementService,
) {

    fun execute(transaction: Transaction) {
        if (transaction.needValidation()) {
            val validationResult = validate(transaction)

            transaction.apply {
                status = if (validationResult is SettlementValidationFailed) TransactionStatus.FAILED else transaction.status
                settlementData.settlementOperation = BoletoSettlementResult(
                    gateway = validationResult.gateway,
                    status = validationResult.boletoSettlementStatus,
                    bankTransactionId = validationResult.bankTransactionId,
                    externalNsu = nsu,
                    externalTerminal = payer.accountId.value,
                    errorCode = validationResult.errorCode,
                    errorDescription = validationResult.errorDescription,
                    errorRetryable = validationResult.errorRetryable,
                )
            }
        }
    }

    private fun validate(transaction: Transaction): SettlementValidationResult {
        val billValidationResponse: BillValidationResponse = try {
            boletoSettlementService.settlementValidation(transaction.settlementData.getTarget())
        } catch (e: BoletoSettlementException) {
            return SettlementValidationFailed(
                gateway = e.gateway,
                errorCode = errorCode01,
                errorDescription = e.message.orEmpty(),
                errorRetryable = true,
            )
        }

        val errorCode = billValidationResponse.errorCode?.takeIf { it.isNotBlank() } ?: errorCode01

        if (billValidationResponse.getStatus() is BillValidationStatus.UnableToValidate) {
            return SettlementValidationFailed(
                gateway = billValidationResponse.gateway,
                errorCode = errorCode,
                errorDescription = billValidationResponse.errorDescription.orEmpty(),
                errorRetryable = billValidationResponse.isRetryable(),
            )
        }

        val response = updateBillService.synchronizeBill(transaction.settlementData.getTarget(), billValidationResponse)

        if (billValidationResponse.notPayable()) {
            return SettlementValidationFailed(
                gateway = billValidationResponse.gateway,
                errorCode = errorCode,
                errorDescription = billValidationResponse.errorDescription.orEmpty(),
                errorRetryable = billValidationResponse.isRetryable(),
            )
        }

        if (!response.alreadyInSync()) {
            return SettlementValidationFailed(
                gateway = billValidationResponse.gateway,
                errorCode = errorCode,
                errorDescription = response.syncErrorMessages?.message.orEmpty(),
                errorRetryable = (response.status as? BillSynchronizationStatus.UnableToValidate)?.isRetryable ?: billValidationResponse.isRetryable(),
            )
        }

        return SettlementValidationSuccess(
            gateway = billValidationResponse.gateway,
            bankTransactionId = billValidationResponse.bankTransactionId,
        )
    }
}

sealed class SettlementValidationResult(
    val gateway: FinancialServiceGateway,
    val boletoSettlementStatus: BoletoSettlementStatus,
    val bankTransactionId: String,
    val errorCode: String,
    val errorDescription: String,
    val errorRetryable: Boolean = true,
)

class SettlementValidationFailed(
    gateway: FinancialServiceGateway,
    errorCode: String,
    errorDescription: String,
    errorRetryable: Boolean,
) : SettlementValidationResult(
    gateway = gateway,
    boletoSettlementStatus = BoletoSettlementStatus.UNAUTHORIZED,
    bankTransactionId = "",
    errorCode = errorCode,
    errorDescription = errorDescription,
    errorRetryable = errorRetryable,
)

class SettlementValidationSuccess(
    gateway: FinancialServiceGateway,
    bankTransactionId: String,
) : SettlementValidationResult(
    gateway = gateway,
    boletoSettlementStatus = BoletoSettlementStatus.AUTHORIZED,
    bankTransactionId = bankTransactionId,
    errorCode = errorCode00,
    errorDescription = "",
)