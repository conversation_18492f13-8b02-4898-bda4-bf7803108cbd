package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.morning.date.brazilTimeZone
import io.micronaut.core.util.StringUtils
import java.math.BigInteger
import java.text.NumberFormat
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.*

fun buildFormattedHeaderDateTime(date: ZonedDateTime): String {
    val brazilDate = date.withZoneSameInstant(brazilTimeZone)
    return brazilDate.format(
        DateTimeFormatter.ofPattern(
            "dd MMM yyyy - HH:mm:ss",
            Locale.forLanguageTag("pt-BR"),
        ),
    ) // "16 nov, 2019"
}

fun buildFormattedPaymentDateTime(date: ZonedDateTime): String {
    val brazilDate = date.withZoneSameInstant(brazilTimeZone)
    return brazilDate.format(DateTimeFormatter.ofPattern("dd/MM/yyyy 'às' HH:mm"))
}

/* codigo original do commcentre */
fun getDocumentType(document: String?): String {
    return if (StringUtils.isNotEmpty(document)) {
        if (isCPF(document!!)) "CPF" else "CNPJ"
    } else {
        ""
    }
}

/* codigo original do commcentre */
fun isCPF(document: String): Boolean {
    return StringUtils.isNotEmpty(document) && document.length == 11
}

/* codigo original do commcentre */
fun formatDocument(document: String?): String {
    return if (document == null || StringUtils.isEmpty(document)) {
        ""
    } else {
        if (isCPF(document)) {
            document.replace(
                "([0-9]{3})([0-9]{3})([0-9]{3})([0-9]{2})".toRegex(),
                "$1\\.$2\\.$3-$4",
            )
        } else {
            document.replace("([0-9]{2})([0-9]{3})([0-9]{3})([0-9]{4})([0-9]{2})".toRegex(), "$1\\.$2\\.$3/$4-$5")
        }
    }
}

fun formatZipCode(zipCode: String?): String {
    return if (zipCode == null || StringUtils.isEmpty(zipCode)) {
        ""
    } else {
        zipCode.replace("([0-9]{5})([0-9]{3})".toRegex(), "$1-$2")
    }
}

fun maskDocumentSpecialAsterisk(document: String?): String {
    return if (StringUtils.isEmpty(document)) {
        ""
    } else {
        if (isCPF(document!!)) {
            document.replace(
                "([0-9]{3})([0-9]{3})([0-9]{3})([0-9]{2})".toRegex(),
                "∗∗∗\\.$2\\.$3-∗∗",
            )
        } else {
            document.replace("([0-9]{2})([0-9]{3})([0-9]{3})([0-9]{4})([0-9]{2})".toRegex(), "$1\\.$2\\.$3/$4-$5")
        }
    }
}

fun maskAccountNo(accountNo: String?): String {
    return if (accountNo.isNullOrEmpty()) {
        ""
    } else {
        val regexWithOptionalDash = Regex("""^(\d{4,})-?(\d)$""") // mínimo 4 dígitos para permitir 3 visíveis + máscara
        val match = regexWithOptionalDash.matchEntire(accountNo)
        if (match != null) {
            val number = match.groupValues[1]
            val dv = match.groupValues[2]

            if (number.length < 3) throw IllegalArgumentException("AccountNo must have at least 3 digits")

            val visibleDigits = number.takeLast(2)
            val masked = "x".repeat(number.length - 3)
            return "$masked$visibleDigits-$dv"
        }
        accountNo
    }
}

fun obfuscateCPF(document: String?): String {
    return if (StringUtils.isEmpty(document)) {
        ""
    } else {
        if (isCPF(document!!)) {
            document.drop(3).take(6)
        } else {
            document
        }
    }
}

fun getFormattedDocument(document: String?) = if (document != null && isCPF(document)) {
    maskDocument(document)
} else {
    formatDocument(document)
}

fun maskDocument(document: String?): String {
    return if (StringUtils.isEmpty(document)) {
        ""
    } else {
        if (isCPF(document!!)) {
            document.replace(
                "([0-9]{3})([0-9]{3})([0-9]{3})([0-9]{2})".toRegex(),
                "***\\.$2\\.$3-**",
            )
        } else {
            document.replace("([0-9]{2})([0-9]{3})([0-9]{3})([0-9]{4})([0-9]{2})".toRegex(), "$1\\.$2\\.$3/$4-$5")
        }
    }
}

/* codigo original do commcentre */
fun buildFormattedAmount(amount: Long): String {
    val n = NumberFormat.getCurrencyInstance(Locale.forLanguageTag("pt-BR"))
    return n.format(amount.toDouble() / 100.0).replace(" ", " ")
}

/* codigo original do commcentre */
fun formatBankData(bankNo: String?, bankIspb: String?, bankName: String): String {
    val bankPreffix = if (bankNo != null && !bankNo.isEmpty()) bankNo.padStart(4, '0') else bankIspb!!
    return "$bankPreffix - $bankName"
}

fun formatBankData(financialInstitution: FinancialInstitution): String {
    return formatBankData(financialInstitution.compe?.toString(), financialInstitution.ispb, financialInstitution.name)
}

fun formatAccountNumber(accountNo: BigInteger, dvNumber: String) =
    "${accountNo.toString().padStart(5, '0')}-$dvNumber"

fun formatAccountType(accountType: AccountType) = when (accountType) {
    AccountType.CHECKING -> "Corrente"
    AccountType.SAVINGS -> "Poupança"
    AccountType.SALARY -> "Corrente"
    AccountType.PAYMENT -> "Pagamento"
}

fun formatRoutingNumber(routingNo: String) =
    routingNo.padStart(4, '0')