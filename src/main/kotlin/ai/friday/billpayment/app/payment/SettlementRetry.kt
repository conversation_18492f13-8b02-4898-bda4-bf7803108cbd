package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.CheckableSettlementStatus
import ai.friday.billpayment.app.payment.receipt.NotifyReceiptService
import ai.friday.billpayment.app.payment.transaction.pushPaymentMetrics
import arrow.core.Either
import arrow.core.left
import arrow.core.right

@FridayMePoupe
class SettlementRetry(
    private val billEventPublisher: BillEventPublisher,
    private val transactionService: TransactionService,
    private val checkoutLocator: CheckoutLocator,
    private val notifyReceiptService: NotifyReceiptService,
) {

    fun resolve(transaction: Transaction): Either<Exception, RetrySettlementStatus> {
        return when (val status = checkStatus(transaction)) {
            is SettlementStatus.Success -> {
                confirm(transaction)
                RetrySettlementStatus.COMPLETED.right()
            }

            is SettlementStatus.Failure -> {
                if (transaction.settlementData.settlementOperation is BankTransfer) {
                    val errorDescription =
                        if (transaction.settlementData.getTarget<Bill>().billType == BillType.PIX) status.message else TransactionError.TED.description
                    transaction.settlementData.settlementOperation =
                        transaction.settlementData.getOperation<BankTransfer>()
                            .copy(errorDescription = errorDescription)
                }
                TransactionRollbackException().left()
            }

            is SettlementStatus.InvalidData -> {
                if (transaction.settlementData.settlementOperation is BankTransfer) {
                    val errorDescription =
                        if (transaction.settlementData.getTarget<Bill>().billType == BillType.PIX) status.message else TransactionError.TED.description
                    transaction.settlementData.settlementOperation =
                        transaction.settlementData.getOperation<BankTransfer>()
                            .copy(status = BankOperationStatus.INVALID_DATA, errorDescription = errorDescription)
                }
                TransactionRollbackException().left()
            }

            SettlementStatus.AlreadyCompleted -> RetrySettlementStatus.COMPLETED.right()

            SettlementStatus.Processing -> RetrySettlementStatus.PROCESSING.right()

            SettlementStatus.Error -> throw TransactionRollbackException()
        }
    }

    private fun checkStatus(transaction: Transaction): SettlementStatus {
        return when (val checkout = checkoutLocator.getCheckout(transaction)) {
            is CheckableSettlementStatus -> {
                checkout.checkSettlementStatus(transaction)
            }

            else -> throw IllegalStateException("Checkout can not check settlement status")
        }
    }

    private fun confirm(transaction: Transaction) {
        transaction.apply {
            status = TransactionStatus.COMPLETED
            settlementData.settlementOperation?.confirm()
        }
        transactionService.save(transaction)

        if (transaction.type == TransactionType.GOAL_REDEMPTION) {
            return
        }

        val bill = transaction.settlementData.getTarget<Bill>()
        val billPaid = BillPaid(
            billId = bill.billId,
            walletId = transaction.walletId,
            actionSource = ActionSource.System,
            transactionId = transaction.id,
            syncReceipt = true,
        )

        billEventPublisher.publish(
            bill,
            billPaid,
        )

        notifyReceiptService.notifyWithAsyncRetry(billPaid)

        pushPaymentMetrics(bill, billPaid)

        if (bill.isPaymentScheduled()) {
            billEventPublisher.publish(
                bill,
                BillPaymentScheduleCanceled(
                    billId = bill.billId,
                    walletId = bill.walletId,
                    actionSource = ActionSource.System,
                    reason = ScheduleCanceledReason.EXECUTED,
                    batchSchedulingId = bill.schedule!!.batchSchedulingId,
                ),
            )
        }
        transactionService.notify(transaction)
    }
}

enum class RetrySettlementStatus {
    COMPLETED, PROCESSING
}