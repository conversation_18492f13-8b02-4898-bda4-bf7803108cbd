package ai.friday.billpayment.app.auth

import ai.friday.billpayment.app.account.SignInPendingAction
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import jakarta.inject.<PERSON>ton

@Singleton
open class AuthenticationService(
    private val deviceFingerprintService: DeviceFingerprintService,
) {
    open fun getSignInPendingAction(user: UserPrincipal): SignInPendingAction {
        return when (user) {
            is UserPrincipal.Guest -> SignInPendingAction.NONE
            is UserPrincipal.Owner -> {
                when (user.method) {
                    is AuthenticationMethod.Msisdn ->
                        if (user.method.hasPassword) {
                            SignInPendingAction.PASSWORD_AUTHENTICATION_REQUIRED
                        } else {
                            SignInPendingAction.CREATE_PASSWORD_REQUIRED
                        }

                    AuthenticationMethod.Password ->
                        if (user.deviceFingerprint != null &&
                            !deviceFingerprintService.isValidFingerprint(
                                    accountId = user.accountId,
                                    deviceFingerprintPayload = user.deviceFingerprint,
                                )
                        ) {
                            SignInPendingAction.DEVICE_BINDING_REQUIRED
                        } else {
                            SignInPendingAction.NONE
                        }
                }
            }
        }
    }
}