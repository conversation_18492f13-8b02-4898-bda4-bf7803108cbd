package ai.friday.billpayment.app.auth

import ai.friday.billpayment.adapters.auth.TO_CLAIM_MSISDN_MIGRATED
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.login.ProviderName
import io.micronaut.security.authentication.Authentication

/**
 * NOTE: É sabido que não obrigar um `deviceFingerprint` é uma falha de segurança,
 * mas, como não queremos tratar o caso web agora, foi acordado que fosse feito assim.
 *
 * O motivo é, como só aceitamos um dispositivo por vez (escolha da Friday),
 * não faria sentido registrar o dispositivo web. E a forma alternativa de autenticar na web ainda não
 * foi pensada (e.g. liberar uma sessão temporária por meio de QR Code).
 *
 * Ainda, usuários com versões antigas do app/web-app (i.e. que não enviam o device fingerprint ainda)
 * não sofrerão qualquer penalidade.
 */
sealed class UserPrincipal {
    data class Guest(val accountId: AccountId) : UserPrincipal()
    data class Owner(
        val accountId: AccountId,
        val method: AuthenticationMethod,
        val deviceFingerprint: String?,
    ) : UserPrincipal()
}

sealed class AuthenticationMethod {
    data class Msisdn(val hasPassword: Boolean) : AuthenticationMethod()
    data object Password : AuthenticationMethod()
}

fun Authentication.toUserPrincipal(deviceFingerprint: String?): UserPrincipal? {
    return when (this.getMainRole()) {
        Role.OWNER -> {
            UserPrincipal.Owner(
                accountId = toAccountId(),
                method = if (attributes["providerName"].toString() == ProviderName.MSISDN.toString()) AuthenticationMethod.Msisdn(hasPassword = attributes[TO_CLAIM_MSISDN_MIGRATED].toString().toBoolean()) else AuthenticationMethod.Password,
                deviceFingerprint = deviceFingerprint,
            )
        }

        Role.GUEST -> UserPrincipal.Guest(
            accountId = toAccountId(),
        )

        else -> null
    }
}

fun Authentication.toAccountId() = AccountId(name)

private val rolesList = Role.entries.map { it.name }

fun Authentication.getMainRole(): Role {
    val isMsisdnAuthentication = attributes["providerName"].toString() == ProviderName.MSISDN.toString()
    if (isMsisdnAuthentication) {
        return Role.valueOf(attributes["actualRole"].toString())
    }

    if (roles.contains(Role.OWNER.name)) {
        return Role.OWNER
    }
    if (roles.contains(Role.GUEST.name)) {
        return Role.GUEST
    }

    return Role.valueOf(roles.first { role -> rolesList.contains(role) } as String)
}