package ai.friday.billpayment.app.instrumentation

import ai.friday.billpayment.app.AsyncUtils.callAsync
import ai.friday.billpayment.markers
import ai.friday.morning.log.andAppend
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

abstract class BaseInstrumentationService<T>(private val instrumentationRepository: InstrumentationRepository<T>) {
    internal open fun publishEventAsync(event: T) {
        try {
            callAsync {
                LOG.debug(markers("event" to event), "publishInstrumentationEvent")
                instrumentationRepository.publishEvent(event)
            }
        } catch (exception: Exception) {
            LOG.error(
                Markers.append("event", event).andAppend("className", event?.let { it::class.simpleName }),
                "publishInstrumentationEvent",
                exception,
            )
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BaseInstrumentationService::class.java)
    }
}