package ai.friday.billpayment.app.fee

import ai.friday.billpayment.Err
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.wallet.WalletId

interface BillFeesRequest

// REQUEST
data class ListFeesCommand(
    val accountId: AccountId,
    val walletId: WalletId,
    val scheduleTo: FeesCommandScheduleTo = FeesCommandScheduleTo.TODAY,
    val bills: List<BillFeesCommand>,
)

data class BillFeesCommand(val billId: String, val methods: List<FeePaymentMethodCommand>)

enum class FeesCommandScheduleTo {
    TODAY, DUE_DATE
}

sealed interface FeePaymentMethodCommand {
    val amount: Long
}

data class FeePaymentMethodBalanceCommand(override val amount: Long) : FeePaymentMethodCommand
data class FeePaymentMethodCreditCardCommand(
    val paymentMethodId: String,
    override val amount: Long,
    val quantity: Int,
) : FeePaymentMethodCommand

// RESPONSE
data class BillFeesResponse(
    val billId: BillId,
    val hash: String,
    val payerId: String,
    val baseValue: Long,
    val payerValue: Long,
    val originalPayerValue: Long,
    val payments: List<FeeResponsePaymentMethod>,
)

sealed class FeeResponsePaymentMethod {
    abstract val id: String
    abstract val method: String
    abstract val amount: Long

    data class FeeResponseBalancePayment(
        override val id: String,
        override val method: String = "BALANCE",
        override val amount: Long,
    ) : FeeResponsePaymentMethod()

    data class FeeResponseCreditCardPayment(
        override val id: String,
        override val method: String = "CREDIT_CARD",
        override val amount: Long,
        val convenience: FeePaymentCreditCardConvenience,
        val installments: List<FeeResponseCreditCardPaymentInstallment>,
    ) : FeeResponsePaymentMethod()

    data class FeePaymentCreditCardConvenience(
        val fee: Long,
        val totalValue: Long,
        val totalValueRounded: Long,
        val totalInterestValue: Long,
        val totalInterestValueRounded: Long,
    )

    data class FeeResponseCreditCardPaymentInstallment(
        val id: String,
        val fee: Long,
        val installmentValue: Long,
        val installmentValueRounded: Long,
        val totalValue: Long,
        val totalValueRounded: Long,
        val originalTotalValueRounded: Long,
        val totalInterestValue: Long,
        val totalInterestValueRounded: Long,
        val originalTotalInterestValueRounded: Long,
        val installmentQuantity: Int,
        val valueType: String,
    )
}

// RESULT
data class FeesWithSummary(
    val fees: List<BillFees>,
    var summary: FeeResultSummary? = null,
)

data class BillFees(
    val billId: String,
    val amount: Long? = null,
    val calculationId: String? = null,
    val payments: List<FeePaymentMethod> = emptyList(),
    val error: ListFeesError? = null,
)

sealed class FeePaymentMethod {
    abstract val method: String
    abstract val amount: Long
    abstract val paymentMethodId: AccountPaymentMethodId
}

data class FeePaymentMethodBalance(
    override val amount: Long,
    override val paymentMethodId: AccountPaymentMethodId,
) : FeePaymentMethod() {
    override val method: String = "BALANCE"
}

data class FeePaymentMethodCreditCard(
    override val amount: Long,
    override val paymentMethodId: AccountPaymentMethodId,
    val convenience: FeePaymentCreditCardConvenience? = null,
    val installments: List<FeePaymentCreditCardInstallment>,
) : FeePaymentMethod() {
    override val method: String = "CREDIT_CARD"
}

data class FeePaymentCreditCardConvenience(
    val fee: Long,
    val totalValue: Long,
    val totalValueRounded: Long,
    val totalInterestValue: Long,
    val totalInterestValueRounded: Long,
)

data class FeePaymentCreditCardInstallment(
    val amount: Long,
    val total: Long,
    val quantity: Int,
    val fee: Double? = null,
    val feeAmount: Long? = null,
)

data class FeeResultSummary(
    // TODO - variavel que representa se as parcelas foram agrupadas pelo maior parcela em comum. Mudar o nome da variavel
    val hasInstallmentQuantityDivergence: Boolean = false,
    val creditCardSummary: List<FeePaymentCreditCardInstallment> = emptyList(),
) {

    companion object {
        fun build(payments: List<FeePaymentMethodCreditCard>, hasInstallmentFee: Boolean = true): FeeResultSummary? {
            if (payments.isEmpty()) {
                return null
            }

            val installmentGroup = payments
                .map {
                    it.installments.groupBy { v -> v.quantity }
                        .map { v -> v.value.reduce { first, second -> if (first.amount > second.amount) first else second } }
                }
                .flatten()
                .groupBy { it.quantity }

            val commons = installmentGroup.filter { it.value.size == payments.size }

            val hasInstallmentQuantityDivergence = installmentGroup.size != commons.size

            val fn: (Map<Int, List<FeePaymentCreditCardInstallment>>) -> List<FeePaymentCreditCardInstallment> =
                { item ->
                    item.map {
                        FeePaymentCreditCardInstallment(
                            amount = it.value.sumOf { v -> v.amount },
                            total = it.value.sumOf { v -> v.total },
                            quantity = it.key,
                            fee = if (hasInstallmentFee) it.value.mapNotNull { v -> v.fee }.maxOrNull() else null,
                            feeAmount = if (hasInstallmentFee) it.value.mapNotNull { v -> v.feeAmount }.sum() else null,
                        )
                    }
                }

            return FeeResultSummary(
                hasInstallmentQuantityDivergence = hasInstallmentQuantityDivergence,
                creditCardSummary = if (hasInstallmentQuantityDivergence) fn(commons) else fn(installmentGroup),
            )
        }
    }
}

data class ListFeesError(val errorMessage: String)

data class BillFeeUnavailable(val billId: BillId) :
    Err(message = "BillFeeUnavailable", args = mapOf("bill_id" to billId.value))