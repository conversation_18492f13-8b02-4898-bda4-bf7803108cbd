package ai.friday.billpayment.app.fee

import ai.friday.billpayment.Err
import ai.friday.billpayment.ServerError
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.integrations.CreditCardFeeCalculatorService
import ai.friday.billpayment.app.integrations.ListFeesService
import ai.friday.billpayment.app.integrations.WalletRepository
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right

@FridayMePoupe
class DefaultListFeesService(
    private val walletRepository: WalletRepository,
    private val creditCardFeeCalculatorService: CreditCardFeeCalculatorService,
) : ListFeesService {
    override fun listFees(request: ListFeesCommand): Either<Err, FeesWithSummary> {
        try {
            val defaultPaymentMethod = walletRepository.findWalletOrNull(request.walletId)?.paymentMethodId
                ?: return Err(message = "Wallet not found").left()

            val ccPayments = mutableListOf<FeePaymentMethodCreditCard>()
            val fees = request.bills.map { requestBill ->
                BillFees(
                    billId = requestBill.billId,
                    calculationId = null,
                    amount = requestBill.methods.sumOf { method -> method.amount },
                    payments = requestBill.methods.map { method ->
                        when (method) {
                            is FeePaymentMethodBalanceCommand -> FeePaymentMethodBalance(
                                amount = method.amount,
                                paymentMethodId = defaultPaymentMethod,
                            )

                            is FeePaymentMethodCreditCardCommand -> {
                                val installments = (1..method.quantity).map {
                                    creditCardFeeCalculatorService.calculateInstallment(
                                        accountId = request.accountId,
                                        netAmount = method.amount,
                                        installments = it,
                                        calculationId = null,
                                    ).getOrElse { e -> throw e }
                                }

                                FeePaymentMethodCreditCard(
                                    amount = method.amount,
                                    paymentMethodId = AccountPaymentMethodId(method.paymentMethodId),
                                    installments = installments,
                                ).also {
                                    ccPayments.add(it)
                                }
                            }
                        }
                    },
                    error = null,
                )
            }

            return FeesWithSummary(
                fees = fees,
                summary = FeeResultSummary.build(ccPayments),
            ).right()
        } catch (e: Exception) {
            return ServerError(e).left()
        }
    }
}