package ai.friday.billpayment.app

import ai.friday.billpayment.FileUtils.Companion.readLocalFileAsText
import java.security.KeyFactory
import java.security.spec.X509EncodedKeySpec
import java.util.Base64
import javax.crypto.Cipher

object EncryptUtils {
    fun sign(data: String, path: String, algorithm: String = "RSA") = kotlin.runCatching {
        val keyBytes = readLocalFileAsText(path)
            ?.replace("-----BEGIN PUBLIC KEY-----", "")
            ?.replace("-----END PUBLIC KEY-----", "")
            ?.replace("\\s".toRegex(), "")
            ?.let { Base64.getDecoder().decode(it) }

        val public = KeyFactory
            .getInstance(algorithm)
            .generatePublic(X509EncodedKeySpec(keyBytes))

        val cipher = Cipher.getInstance(algorithm)

        cipher.init(Cipher.ENCRYPT_MODE, public)

        val encryptedBytes = cipher.doFinal(data.toByteArray(Charsets.UTF_8))

        Base64.getEncoder().encodeToString(encryptedBytes)
    }
}