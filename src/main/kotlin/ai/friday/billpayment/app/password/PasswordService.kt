package ai.friday.billpayment.app.password

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.InvalidTokenException
import ai.friday.billpayment.app.account.IssuedToken
import ai.friday.billpayment.app.account.TokenKey
import ai.friday.billpayment.app.account.TokenService
import ai.friday.billpayment.app.account.TokenStillValidException
import ai.friday.billpayment.app.integrations.TokenType
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.liveness.LivenessErrors
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.liveness.LivenessService
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right

@FridayMePoupe
open class PasswordService(
    private val livenessService: LivenessService,
    private val accountService: AccountService,
    private val userPoolAdapter: UserPoolAdapter,
    private val tokenService: TokenService,
) {
    fun createOTP(document: String): Either<PasswordErrors, IssuedToken> {
        val account =
            accountService.findAccountByDocumentOrNull(document) ?: return PasswordErrors.AccountNotFound.left()

        if (!userPoolAdapter.doesUserExist(account.document)) {
            return PasswordErrors.AccountWithoutPassword.left()
        }

        val hasCompletedLiveness = livenessService.hasCompletedEnrollment(account.accountId)
            .getOrElse { return PasswordErrors.Error(IllegalStateException(it.javaClass.name)).left() }

        if (!hasCompletedLiveness) {
            return PasswordErrors.CantUseLiveness.left()
        }

        return tokenService.issuePasswordRecoveryToken(
            email = account.emailAddress,
            accountId = account.accountId,
            document = account.document,
        ).map {
            it.right()
        }.getOrElse {
            if (it is TokenStillValidException) {
                PasswordErrors.TokenStillValid.left()
            } else {
                PasswordErrors.Error(it).left()
            }
        }
    }

    fun verifyOTPAndCreateMatchLivenessId(document: String, token: String): Either<PasswordErrors, LivenessId> {
        val account =
            accountService.findAccountByDocumentOrNull(document) ?: return PasswordErrors.AccountNotFound.left()
        val tokenKey = TokenKey(accountId = account.accountId, value = token)

        tokenService.validateToken(tokenKey, TokenType.EMAIL).getOrElse {
            if (it is InvalidTokenException) {
                return PasswordErrors.InvalidOTPToken.left()
            }
            return PasswordErrors.Error(it).left()
        }

        val livenessId = livenessService.match(tokenKey.accountId).getOrElse {
            if (it is LivenessErrors.Error) {
                return PasswordErrors.Error(it.e).left()
            }
            return PasswordErrors.Error(IllegalStateException(it.javaClass.simpleName)).left()
        }

        tokenService.issueToken(
            // TODO - testar esse cenário
            livenessId = livenessId,
            accountId = account.accountId,
        ).map {
            it.right()
        }.getOrElse {
            PasswordErrors.Error(it).left()
        }

        return livenessId.right()
    }

    fun resetPassword(livenessId: LivenessId, password: String): Either<PasswordErrors, Document> {
        try {
            val matchPassed = livenessService.verifyMatch(livenessId).getOrElse {
                if (it is LivenessErrors.Error) {
                    return PasswordErrors.Error(it.e).left()
                }
                return PasswordErrors.Error(IllegalStateException(it.javaClass.name)).left()
            }

            tokenService.validateToken(
                TokenKey(accountId = matchPassed.accountId, value = livenessId.value),
                TokenType.LIVENESS_ID,
            ).getOrElse {
                if (it is InvalidTokenException) {
                    return PasswordErrors.InvalidOTPToken.left()
                }
                return PasswordErrors.Error(it).left()
            }

            if (!matchPassed.match) {
                return PasswordErrors.LivenessValidationIncomplete.left()
            }

            val account = accountService.findAccountById(matchPassed.accountId)
            userPoolAdapter.setUserPassword(account.document, password)
            return Document(account.document).right()
        } catch (e: Exception) {
            return PasswordErrors.Error(e).left()
        }
    }
}

sealed class PasswordErrors {
    data object AccountWithoutPassword : PasswordErrors()
    data object CantUseLiveness : PasswordErrors()
    data object AccountNotFound : PasswordErrors()
    data object LivenessValidationIncomplete : PasswordErrors()
    data object InvalidOTPToken : PasswordErrors()
    data object TokenStillValid : PasswordErrors()
    data class Error(val e: Exception) : PasswordErrors()
}