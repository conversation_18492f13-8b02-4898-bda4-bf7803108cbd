package ai.friday.billpayment.app.settlement.receipt.adapter

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.payment.Transaction

interface GetBoletoSettlementReceiptAdapter {
    fun getReceipt(transaction: Transaction): GetBoletoSettlementReceiptResult
}

sealed class GetBoletoSettlementReceiptResult : PrintableSealedClassV2() {
    data class Success(
        val gateway: FinancialServiceGateway,
        val createdOn: String,
        val transactionId: String,
        val authentication: String,
        val paymentPartnerName: String?,
    ) : GetBoletoSettlementReceiptResult()

    data class Error(
        val gateway: FinancialServiceGateway,
        val reason: String? = null,
        val exception: Exception? = null,
    ) : GetBoletoSettlementReceiptResult()
}