package ai.friday.billpayment.app.settlement.receipt.adapter

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.payment.Transaction
import jakarta.inject.Named

interface BoletoSettlementReceiptLocator {
    fun get(transaction: Transaction): GetBoletoSettlementReceiptAdapter
}

@FridayMePoupe
class DefaultBoletoSettlementReceiptLocator(
    @Named("celcoinReceiptAdapter") private val celcoinReceiptAdapter: GetBoletoSettlementReceiptAdapter?,
) : BoletoSettlementReceiptLocator {

    override fun get(transaction: Transaction): GetBoletoSettlementReceiptAdapter {
        return celcoinReceiptAdapter ?: throw IllegalStateException("No BoletoSettlementReceiptAdapter found")
    }
}