package ai.friday.billpayment.app.billcategory

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillCategorySuggestionAdded
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.billLockProvider
import ai.friday.billpayment.app.bill.getSegment
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import jakarta.inject.Named
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

private const val CATEGORY_SUGGESTION_PERCENTAGE_FILTER = 50
private const val CATEGORY_SUGGESTION_LIMIT_FILTER = 3

@FridayMePoupe
class BillCategorySuggestionService(
    private val billRepository: BillRepository,
    private val billEventPublisher: BillEventPublisher,
    private val walletBillCategoryService: PFMWalletCategoryService,
    @Named(billLockProvider) private val lockProvider: InternalLock,
    private val billEventRepository: BillEventRepository,
) {
    private val logger = LoggerFactory.getLogger(BillCategorySuggestionService::class.java)

    fun setBillCategorySuggestions(
        billId: BillId,
        walletId: WalletId,
        categorySuggestions: List<BillCategorySuggestion>,
    ): Either<SetBillCategorySuggestionError, Unit> {
        val markers =
            Markers.append("billId", billId.value)
                .andAppend("walletId", walletId.value)
                .andAppend("categorySuggestions", categorySuggestions)
        val logName = "BillCategorySuggestionService#setBillCategorySuggestions"

        val lock = lockProvider.acquireLock(billId.value)

        if (lock == null) {
            logger.warn(markers, logName)
            return SetBillCategorySuggestionError.AlreadyLockedException.left()
        }

        if (categorySuggestions.isNotEmpty()) {
            val bill = billEventRepository.getBillById(billId = billId).getOrElse {
                logger.error(markers, logName)
                lock.unlock()
                return SetBillCategorySuggestionError.BillNotFoundException.left()
            }
            val event =
                BillCategorySuggestionAdded(
                    billId = billId,
                    walletId = walletId,
                    actionSource = ActionSource.System,
                    categories = categorySuggestions,
                )

            billEventPublisher.publish(bill, event)
            markers.andAppend("event", event)
        }

        lock.unlock()
        logger.info(markers, logName)
        return Unit.right()
    }

    // TODO: renomear
    fun suggestCategories(bill: BillView, bills: List<BillView>? = null): List<BillCategorySuggestion> {
        if (bill.categoryId != null) {
            return emptyList()
        }

        val walletBillCategories = walletBillCategoryService.findWalletCategories(bill.walletId)

        val billsList = bills ?: billRepository.findByWallet(bill.walletId)

        val categorizedBills = billsList
            .filter { billCategory ->
                billCategory.categoryId != null &&
                    walletBillCategories
                        .filter { it.enabled }
                        .any() { it.categoryId.value == billCategory.categoryId.value }
            }

        val marker = Markers.append("targetBill", bill.billId)

        return categorizedBills
            .map { it.categoryId!! to getSimilarity(bill, it) }
            .groupBy { (categoryId, _) -> categoryId }
            .map { (categoryId, similarities) ->
                val highSimilarity = similarities.groupBy { (_, similarity) -> similarity }
                    .map { (similarity, similarities) -> similarity to similarities.size }.maxBy { it.first }

                BillCategorySuggestion(
                    categoryId = categoryId,
                    probability = highSimilarity.first,
                    billsCount = highSimilarity.second,
                )
            }
            .also { logger.info(marker.andAppend("suggestions", it), "BillCategoryService#suggestCategories") }
    }

    // TODO: renomear
    fun filterCategories(value: List<BillCategorySuggestion>): List<BillCategorySuggestion> {
        val filtered =
            value.filter {
                it.probability >= CATEGORY_SUGGESTION_PERCENTAGE_FILTER
            }.sortedWith(compareByDescending<BillCategorySuggestion> { it.probability }.thenByDescending { it.billsCount }).take(CATEGORY_SUGGESTION_LIMIT_FILTER)

        return filtered
    }

    fun getSimilarity(
        bill: BillView,
        otherBill: BillView,
    ): Int {
        val billAttributes = bill.getAttributes()
        val otherBillAttributes = otherBill.getAttributes()

        val attributes =
            if (bill.billType == otherBill.billType) {
                when (bill.billType) {
                    BillType.CONCESSIONARIA -> concessionariaAttributes
                    BillType.FICHA_COMPENSACAO -> fichaAttributes
                    BillType.INVOICE -> invoiceAttributes
                    BillType.PIX, BillType.AUTOMATIC_PIX -> pixAttributes
                    BillType.INVESTMENT -> investmentAttributes
                    BillType.OTHERS -> throw IllegalStateException("should not happen: ${bill.billType}")
                }
            } else {
                differententBillTypeAttributes
            }

        val max =
            attributes.sumOf {
                it.weight
            }

        var sum = max

        attributes.forEach {
            val billAttribute = billAttributes[it.attribute]
            val otherBillAttribute = otherBillAttributes[it.attribute]

            if (billAttribute == null || billAttribute != otherBillAttribute) {
                sum -= it.weight
            }
        }

        return sum * 100 / max
    }

    data class AttributeSimilarity(
        val attribute: BillAttribute,
        val weight: Int,
    )

    enum class BillAttribute {
        DayOfMonth,
        AmountTotal,
        Description,
        Source,
        CompanyCode,
        Recipient,
        Segment,
        Payer,
        FichaCompensacaoType,
        BillType,
    }

    private val concessionariaAttributes =
        listOf(
            AttributeSimilarity(attribute = BillAttribute.CompanyCode, weight = 2),
            AttributeSimilarity(attribute = BillAttribute.Recipient, weight = 2),
            AttributeSimilarity(attribute = BillAttribute.Segment, weight = 2),
            AttributeSimilarity(attribute = BillAttribute.DayOfMonth, weight = 1),
            AttributeSimilarity(attribute = BillAttribute.AmountTotal, weight = 1),
            AttributeSimilarity(attribute = BillAttribute.Description, weight = 1),
            AttributeSimilarity(attribute = BillAttribute.Source, weight = 1),
        )

    private val fichaAttributes =
        listOf(
            AttributeSimilarity(attribute = BillAttribute.Recipient, weight = 3),
            AttributeSimilarity(attribute = BillAttribute.Payer, weight = 2),
            AttributeSimilarity(attribute = BillAttribute.FichaCompensacaoType, weight = 2),
            AttributeSimilarity(attribute = BillAttribute.DayOfMonth, weight = 1),
            AttributeSimilarity(attribute = BillAttribute.AmountTotal, weight = 1),
            AttributeSimilarity(attribute = BillAttribute.Description, weight = 1),
        )

    private val differententBillTypeAttributes =
        listOf(
            AttributeSimilarity(attribute = BillAttribute.BillType, weight = 5),
            AttributeSimilarity(attribute = BillAttribute.Recipient, weight = 1),
            AttributeSimilarity(attribute = BillAttribute.DayOfMonth, weight = 1),
            AttributeSimilarity(attribute = BillAttribute.AmountTotal, weight = 1),
            AttributeSimilarity(attribute = BillAttribute.Description, weight = 1),
            AttributeSimilarity(attribute = BillAttribute.Source, weight = 1),
        )

    private val pixAttributes =
        listOf(
            AttributeSimilarity(attribute = BillAttribute.Recipient, weight = 1),
            AttributeSimilarity(attribute = BillAttribute.DayOfMonth, weight = 1),
            AttributeSimilarity(attribute = BillAttribute.AmountTotal, weight = 1),
            AttributeSimilarity(attribute = BillAttribute.Description, weight = 1),
            AttributeSimilarity(attribute = BillAttribute.Source, weight = 1),
        )

    private val investmentAttributes =
        listOf(
            AttributeSimilarity(attribute = BillAttribute.BillType, weight = 1),
        )

    private val invoiceAttributes =
        listOf(
            AttributeSimilarity(attribute = BillAttribute.Recipient, weight = 1),
            AttributeSimilarity(attribute = BillAttribute.DayOfMonth, weight = 1),
            AttributeSimilarity(attribute = BillAttribute.AmountTotal, weight = 1),
            AttributeSimilarity(attribute = BillAttribute.Description, weight = 1),
            AttributeSimilarity(attribute = BillAttribute.Source, weight = 1),
        )

    private fun BillView.getAttributes(): Map<BillAttribute, Any?> {
        return when (billType) {
            BillType.CONCESSIONARIA -> getConcessionariaAttributes()
            BillType.FICHA_COMPENSACAO -> getFichaAttributes()
            BillType.PIX, BillType.AUTOMATIC_PIX -> getPixAttributes()
            BillType.INVOICE -> getInvoiceAttributes()
            BillType.INVESTMENT -> getInvestmentAttributes()
            BillType.OTHERS -> throw IllegalStateException("should not happen: $billType")
        } + getCommomAttributes()
    }

    private fun BillView.getCommomAttributes() =
        mapOf(
            BillAttribute.DayOfMonth to dueDate.dayOfMonth,
            BillAttribute.AmountTotal to amountTotal,
            BillAttribute.Description to billDescription,
            BillAttribute.Source to source,
        )

    private fun BillView.getPixAttributes() =
        mapOf(
            BillAttribute.Recipient to recipient?.document,
        )

    private fun BillView.getInvoiceAttributes() =
        mapOf(
            BillAttribute.Recipient to recipient?.document,
        )

    private fun BillView.getInvestmentAttributes() =
        mapOf(
            BillAttribute.BillType to billType,
        )

    private fun BillView.getFichaAttributes() =
        mapOf(
            BillAttribute.Recipient to recipient?.document,
            BillAttribute.Payer to payerDocument,
            BillAttribute.FichaCompensacaoType to fichaCompensacaoType,
        )

    private fun BillView.getConcessionariaAttributes() =
        mapOf(
            BillAttribute.CompanyCode to getCompanyCode(barCode!!),
            BillAttribute.Recipient to assignor,
            BillAttribute.Segment to getSegment(barCode),
        )

    private fun getCompanyCode(barCode: BarCode): String? {
        return if (barCode.number.startsWith("8")) {
            barCode.number.substring(15, 19)
        } else {
            null
        }
    }
}

data class BillCategorySuggestion(
    val categoryId: PFMCategoryId,
    val probability: Int,
    val billsCount: Int = 0,
)

sealed class SetBillCategorySuggestionError : PrintableSealedClassV2() {
    data object AlreadyLockedException : SetBillCategorySuggestionError()
    data object BillNotFoundException : SetBillCategorySuggestionError()
}