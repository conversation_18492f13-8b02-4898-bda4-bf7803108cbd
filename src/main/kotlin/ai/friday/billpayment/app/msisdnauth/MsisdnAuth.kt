package ai.friday.billpayment.app.msisdnauth

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.login.WHATSAPP_DOMAIN
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.time.ZonedDateTime
import java.util.UUID

private const val DOMAIN = "friday.ai"

data class MsisdnAuthId(val value: String) {
    constructor() : this("MSISDN_AUTH-${UUID.randomUUID()}")
}

data class MsisdnAuth(
    val id: MsisdnAuthId,
    val accountId: AccountId,
    val createdAt: ZonedDateTime,
) {
    val updatedAt: ZonedDateTime = getZonedDateTime()
}

fun isTemporaryEmail(emailAddress: EmailAddress) =
    emailAddress.recipient.startsWith("+55") && emailAddress.domain == DOMAIN

fun createTemporaryEmail(mobilePhone: MobilePhone): EmailAddress {
    return EmailAddress("${mobilePhone.msisdn}@$DOMAIN")
}

fun createWhatsappEmail(mobilePhone: MobilePhone): EmailAddress {
    return EmailAddress(email = "${mobilePhone.msisdn.replace("+", "")}@$WHATSAPP_DOMAIN")
}

fun extractMobilePhoneFromTemporaryEmail(emailAddress: EmailAddress): MobilePhone? {
    if (!isTemporaryEmail(emailAddress)) return null
    return MobilePhone(emailAddress.recipient)
}