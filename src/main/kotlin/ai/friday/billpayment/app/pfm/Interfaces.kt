package ai.friday.billpayment.app.pfm

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.BillEventDependency
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.recurrence.Range
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId
import arrow.core.Either
import java.time.LocalDate
import java.time.Month
import java.time.Year
import java.util.UUID

@BillEventDependency
data class PFMCategoryId(
    val value: String = "PFM_CATEGORY_${UUID.randomUUID()}",
)

data class BillCategory(
    val categoryId: PFMCategoryId,
    val name: String,
    val icon: String,
    val default: Boolean,
)

data class DefaultWalletBillCategory(
    val categoryId: PFMCategoryId,
    val name: String,
    val icon: String,
)

data class WalletBillCategory(
    val walletId: WalletId,
    val categoryId: PFMCategoryId,
    val name: String,
    val icon: String,
    val enabled: Boolean,
    val default: Boolean,
) {
    fun toBillCategory() = BillCategory(
        categoryId = categoryId,
        name = name,
        icon = icon,
        default = default,
    )
}

interface DefaultWalletBillCategoryRepository {
    fun save(defaultWalletBillCategory: DefaultWalletBillCategory)
    fun findAll(): List<DefaultWalletBillCategory>
    fun delete(defaultWalletBillCategory: DefaultWalletBillCategory)
}

interface WalletBillCategoryRepository {
    fun save(walletBillCategory: WalletBillCategory)
    fun findByWalletId(walletId: WalletId): List<WalletBillCategory>
    fun findByWalletIdAndBillCategoryId(walletId: WalletId, billCategoryId: PFMCategoryId): WalletBillCategory?
}

interface PFMWalletCategoryService {
    fun createDefaultWalletCategories(walletId: WalletId): Either<CreateDefaultCategoriesError, List<WalletBillCategory>>
    fun findWalletCategories(walletId: WalletId, createAsync: Boolean = true): List<WalletBillCategory>
    fun findCategory(walletId: WalletId, billCategoryId: PFMCategoryId): WalletBillCategory?
    fun create(
        walletId: WalletId,
        name: String,
        icon: String,
    ): Either<CreateWalletBillCategoryError, WalletBillCategory>

    fun update(walletId: WalletId, categoryId: PFMCategoryId, billCategoryName: String, billCategoryIcon: String, enabled: Boolean): Either<UpdateWalletBillCategoryError, WalletBillCategory>
}

interface PFMBillCategoryService {
    fun setBillCategory(billId: BillId, billCategoryId: PFMCategoryId, accountId: AccountId, range: Range? = Range.THIS): Either<SetBillCategoryError, Unit>
    fun removeBillCategory(billId: BillId, accountId: AccountId, range: Range? = Range.THIS): Either<RemoveBillCategoryError, Unit>
}

interface SummaryService {
    fun generateSummaryList(walletId: WalletId, walletMember: Member, year: Year, month: Month): List<SummaryEntry>
}

// FIXME: não deveriam estar aqui
sealed class CreateWalletBillCategoryError : PrintableSealedClassV2() {
    data object AlreadyExists : CreateWalletBillCategoryError()
}

sealed class CreateDefaultCategoriesError : PrintableSealedClassV2() {
    data object CouldNotAcquireLock : CreateDefaultCategoriesError()
    data object WalletAlreadyHasCategories : CreateDefaultCategoriesError()
}

sealed class UpdateWalletBillCategoryError : PrintableSealedClassV2() {
    data object NotFound : UpdateWalletBillCategoryError()
    data object NameAlreadyExists : UpdateWalletBillCategoryError()
}

sealed class SummaryError : Exception() {
    class WalletNotFound() : SummaryError()
    class ServerError() : SummaryError()
    class NotAllowed() : SummaryError()
    class FailedToSendEmail() : SummaryError()
}

data class CategorySummary(
    val category: BillCategory?,
    val totalExpenseAmount: Long,
    val totalIncomeAmount: Long,
    val billCount: Int,
    val reminderCount: Int,
    val incomeCount: Int,
)

sealed class SetBillCategoryError : PrintableSealedClassV2() {
    data object BillNotFound : SetBillCategoryError()
    data object CategoryNotFound : SetBillCategoryError()
    data object ServerError : SetBillCategoryError()
    data object AlreadyLocked : SetBillCategoryError()
}

sealed class RemoveBillCategoryError : PrintableSealedClassV2() {
    data object BillNotFound : RemoveBillCategoryError()
    data object ServerError : RemoveBillCategoryError()
}

data class SummaryEntry(
    val category: BillCategory?,
    val totalAmount: Long,
    val title: String,
    val description: String,
    val date: LocalDate,
    val type: SummaryEntryType,
    val entryType: String,
) {
    fun isIncome() = when (this.type) {
        SummaryEntryType.INCOME -> true
        SummaryEntryType.PAYMENT -> false
        SummaryEntryType.REMINDER -> false
    }
    fun isExpense() = !isIncome()
}

enum class SummaryEntryType {
    PAYMENT, REMINDER, INCOME
}