package ai.friday.billpayment.app.job

import ai.friday.billpayment.app.feature.FeaturesRepository
import ai.friday.billpayment.app.integrations.JobRepository
import ai.friday.billpayment.originalSimpleName
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import datadog.trace.api.Trace
import io.micronaut.core.convert.ConversionService
import io.micronaut.scheduling.TaskExecutors
import io.micronaut.scheduling.TaskScheduler
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Inject
import jakarta.inject.Named
import java.time.Duration
import java.time.Instant
import java.time.ZonedDateTime
import java.util.concurrent.ScheduledFuture
import net.javacrumbs.shedlock.core.LockConfiguration
import net.javacrumbs.shedlock.core.LockProvider
import net.javacrumbs.shedlock.core.SimpleLock
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@JobConfiguration
abstract class AbstractJob(
    val crons: List<String>,
    val fixedDelay: Duration? = null,
    private val lockAtLeastFor: String? = null,
    private val lockAtMostFor: String? = null,
    val shutdownGracefully: Boolean = true,
    val shutdownGracefullyMaxWaitTime: Int = 2,
    val shouldLock: Boolean = true,
) : Runnable {
    constructor(
        cron: String,
        lockAtLeastFor: String? = null,
        lockAtMostFor: String? = null,
        shutdownGracefully: Boolean = true,
        shutdownGracefullyMaxWaitTime: Int = 2,
        shouldLock: Boolean = true,
    ) : this(
        crons = listOf(cron),
        fixedDelay = null,
        lockAtLeastFor = lockAtLeastFor,
        lockAtMostFor = lockAtMostFor,
        shutdownGracefully = shutdownGracefully,
        shutdownGracefullyMaxWaitTime = shutdownGracefullyMaxWaitTime,
        shouldLock = shouldLock,
    )

    constructor(
        fixedDelay: Duration,
        lockAtLeastFor: String? = null,
        lockAtMostFor: String? = null,
        shutdownGracefully: Boolean = true,
        shutdownGracefullyMaxWaitTime: Int = 2,
        shouldLock: Boolean = true,
    ) : this(
        crons = listOf(),
        fixedDelay = fixedDelay,
        lockAtLeastFor = lockAtLeastFor,
        lockAtMostFor = lockAtMostFor,
        shutdownGracefully = shutdownGracefully,
        shutdownGracefullyMaxWaitTime = shutdownGracefullyMaxWaitTime,
        shouldLock = shouldLock,
    )

    val jobName: String = this.javaClass.originalSimpleName()
    lateinit var lockAtLeastForDuration: Duration
        private set
    lateinit var lockAtMostForDuration: Duration
        private set

    var running: Boolean = false
        private set
    var lastStartTime: ZonedDateTime? = null
        private set
    var lastElapsedMinutes: Long? = null
        private set

    private val schedules = mutableListOf<ScheduledFuture<*>>()

    private var lock: SimpleLock? = null

    @Inject
    @Named(TaskExecutors.SCHEDULED)
    private lateinit var taskScheduler: TaskScheduler

    @Inject
    private lateinit var lockProvider: LockProvider

    @Inject
    private lateinit var conversionService: ConversionService

    @Inject
    private lateinit var featuresRepository: FeaturesRepository

    @Inject
    private lateinit var jobRepository: JobRepository

    @NewSpan
    abstract fun execute()

    @Trace
    @NewSpan
    override fun run() {
        val logName = "AbstractJob#run"
        val markers = Markers.append("jobName", jobName)

        if (featuresRepository.getAll().maintenanceMode) {
            LOG.info(markers.andAppend("jobExcecutionSkipped", "maintenanceMode"), logName)
            return
        }

        if (shouldLock) {
            lock = acquireLock()

            if (lock == null) {
                LOG.info(markers.andAppend("jobExcecutionSkipped", "could not acquire lock"), logName)
                return
            }
        }

        lastStartTime = getZonedDateTime()
        LOG.info(markers.andAppend("step", "begin"), "$logName#begin")
        try {
            running = true
            jobRepository.save(this.toJob())
            execute()
        } catch (t: Throwable) {
            LOG.error(markers, logName, t)
        } finally {
            running = false
            unlock()
            val end = getZonedDateTime().toInstant()
            lastElapsedMinutes = Duration.between(lastStartTime!!.toInstant(), end).toMinutes()
            LOG.info(markers.andAppend("step", "end").andAppend("elapsedMinutes", lastElapsedMinutes), "$logName#end")

            jobRepository.save(toJob())
        }
    }

    fun toJob(): Job {
        return Job(
            name = jobName,
            crons = crons,
            fixedDelay = fixedDelay,
            running = running,
            lastStartTime = lastStartTime,
            lastElapsedMinutes = lastElapsedMinutes,
            shouldLock = shouldLock,
            lockAtLeastFor = lockAtLeastForDuration,
            lockAtMostFor = lockAtMostForDuration,
            shutdownGracefully = shutdownGracefully,
            shutdownGracefullyMaxWaitTime = shutdownGracefullyMaxWaitTime,
        )
    }

    fun initialize(defaultLockAtLeastFor: String, defaultLockAtMostFor: String) {
        val logName = "AbstractJob#initialize"
        val markers = Markers.append("jobName", jobName)
            .andAppend("crons", crons)
            .andAppend("lockAtLeastFor", lockAtLeastFor)
            .andAppend("lockAtMostFor", lockAtMostFor)
            .andAppend("defaultLockAtLeastFor", defaultLockAtLeastFor)
            .andAppend("defaultLockAtMostFor", defaultLockAtMostFor)

        try {
            lockAtLeastForDuration = parseDuration(lockAtLeastFor ?: defaultLockAtLeastFor)
            lockAtMostForDuration = parseDuration(lockAtMostFor ?: defaultLockAtMostFor)
            crons.forEach {
                schedules.add(taskScheduler.schedule(it, this))
            }
            fixedDelay?.let {
                schedules.add(taskScheduler.scheduleWithFixedDelay(null, it, this))
            }

            jobRepository.save(toJob())

            LOG.info(markers, logName)
        } catch (e: Exception) {
            LOG.error(markers, logName, e)
        }
    }

    fun beginShutdown() {
        val logName = "AbstractJob#beginShutdown"
        val markers = Markers.append("jobName", jobName)
        try {
            schedules.removeAll {
                it.cancel(false)
                true
            }
            LOG.info(markers, logName)
        } catch (e: Exception) {
            LOG.error(markers, logName, e)
        }
    }

    private fun acquireLock(): SimpleLock? {
        val logName = "AbstractJob#acquireLock"
        val markers = Markers.append("jobName", jobName)
        return try {
            lockProvider.lock(
                LockConfiguration(
                    Instant.now(),
                    jobName,
                    lockAtMostForDuration,
                    lockAtLeastForDuration,
                ),
            ).orElse(null)
        } catch (e: Exception) {
            LOG.error(markers, logName, e)
            null
        }
    }

    internal fun unlock() {
        val logName = "AbstractJob#unlock"
        val markers = Markers.append("jobName", jobName)
        LOG.info(markers, logName)
        lock?.unlock()
        lock = null
    }

    private fun parseDuration(value: String): Duration {
        return conversionService.convert(value, Duration::class.java).get()
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(AbstractJob::class.java)
    }
}