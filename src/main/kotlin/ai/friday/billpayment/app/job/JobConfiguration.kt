package ai.friday.billpayment.app.job

import ai.friday.billpayment.originalSimpleName
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import io.micronaut.context.condition.Condition
import io.micronaut.context.condition.ConditionContext
import java.lang.annotation.Inherited
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Inherited
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
@Requirements(Requires(condition = JobCondition::class), Requires(notEnv = ["test"]))
annotation class JobConfiguration

class JobCondition : Condition {
    companion object {
        private val logger = LoggerFactory.getLogger(JobCondition::class.java)
    }

    override fun matches(context: ConditionContext<*>): Boolean {
        val className: String = context.component.javaClass.originalSimpleName()

        val configKey = "jobs.$className.enabled"
        val property = context.getProperty(config<PERSON><PERSON>, Boolean::class.java)

        logger.debug(<PERSON>ers.append("enabled", property).andAppend("bean", className), "JobContition")

        return property.orElse(true)
    }
}