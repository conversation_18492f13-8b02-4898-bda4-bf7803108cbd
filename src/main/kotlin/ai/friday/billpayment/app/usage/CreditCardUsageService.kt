package ai.friday.billpayment.app.usage

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.app.account.isCreditCard
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
open class CreditCardUsageService(
    private val transactionRepository: TransactionRepository,
    private val accountService: AccountService,
    private val walletService: WalletService,
    private val featureConfiguration: FeatureConfiguration,
    @Property(name = "creditCardConfiguration.mediumRiskQuota") private val mediumRiskQuota: Long,
    @Property(name = "creditCard.installments.fees.1") private val fee: Double,
) {

    open fun calculateCreditCardUsage(accountId: AccountId): CreditCardUsage {
        if (!featureConfiguration.creditCardQuota) {
            return unlimitedCreditCardUsage
        }

        val account = accountService.findAccountById(accountId)
        val totalUsage = getCreditCardUsageInCurrentMonth(account)

        val markers = Markers.append("accountId", accountId)

        val userCreditCards =
            accountService.findAccountPaymentMethodsByAccountId(accountId, null).filter {
                it.isCreditCard()
            }

        val usages: MutableMap<RiskLevel, CreditCardRiskUsage> =
            mutableMapOf(
                RiskLevel.LOW to CreditCardRiskUsage(quota = account.creditCardConfiguration.quota, usage = 0),
                RiskLevel.MEDIUM to CreditCardRiskUsage(quota = mediumRiskQuota, usage = 0),
                RiskLevel.HIGH to CreditCardRiskUsage(quota = 0, usage = 0),
            )

        totalUsage.amounts.forEach {
            val riskLevel = (
                userCreditCards.single { accountPaymentMethod ->
                    accountPaymentMethod.id == it.key
                }.method as CreditCard
                ).riskLevel

            val oldLowValue = usages[RiskLevel.LOW]
            val newLowValue =
                CreditCardRiskUsage(oldLowValue!!.quota, oldLowValue.usage + it.value.netAmount)
            usages[RiskLevel.LOW] = newLowValue

            if (riskLevel == RiskLevel.MEDIUM || riskLevel == RiskLevel.HIGH) {
                val oldValue = usages[riskLevel]!!
                val newValue =
                    CreditCardRiskUsage(oldValue.quota, oldValue.usage + it.value.netAmount)
                usages[riskLevel] = newValue
            }
        }

        val hasActiveMediumRiskCreditCard = userCreditCards.any { (it.method as CreditCard).riskLevel == RiskLevel.MEDIUM && it.status == AccountPaymentMethodStatus.ACTIVE }
        val hasActiveHighRiskCreditCard = userCreditCards.any { (it.method as CreditCard).riskLevel == RiskLevel.HIGH && it.status == AccountPaymentMethodStatus.ACTIVE }

        markers.andAppend("hasActiveMediumRiskCreditCard", hasActiveMediumRiskCreditCard).andAppend("hasActiveHighRiskCreditCard", hasActiveHighRiskCreditCard)

        val quota =
            if (hasActiveHighRiskCreditCard && account.creditCardConfiguration.allowedRisk.level < RiskLevel.HIGH.level) {
                0
            } else if (hasActiveMediumRiskCreditCard && account.creditCardConfiguration.allowedRisk.level < RiskLevel.MEDIUM.level) {
                mediumRiskQuota
            } else {
                account.creditCardConfiguration.quota
            }

        LOG.info(markers, "CreditCardUsageService#calculateCreditCardUsage")
        return CreditCardUsage(
            quota = quota,
            usage = totalUsage.netAmount,
            cashInFee = (fee * 100).toLong(),
            riskUsages = usages,
        )
    }

    fun getCreditCardUsageOnDate(accountId: AccountId, date: LocalDate): TransactionAmount {
        val datePattern = date.format(dateFormat)
        return walletService.findWallets(accountId).map {
            transactionRepository.findCreditCardUsage(
                accountId,
                it.id,
                datePattern,
            )
        }.sum()
    }

    private fun getCreditCardUsageInCurrentMonth(account: Account): TransactionAmount {
        val yearMonth = getZonedDateTime().format(DateTimeFormatter.ofPattern("yyyy-MM"))

        return walletService.findWallets(account.accountId).map { wallet ->
            transactionRepository.findCreditCardUsage(account.accountId, wallet.id, yearMonth)
        }.sum()
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(CreditCardUsageService::class.java)
    }
}

data class CreditCardUsage(
    val quota: Long,
    val usage: Long,
    val cashInFee: Long,
    val riskUsages: Map<RiskLevel, CreditCardRiskUsage>,
) {
    val availableQuota = quota - usage
}

val unlimitedCreditCardUsage = CreditCardUsage(
    quota = Long.MAX_VALUE,
    usage = 0,
    cashInFee = 0,
    riskUsages = mapOf(
        RiskLevel.LOW to CreditCardRiskUsage(quota = Long.MAX_VALUE, usage = 0),
        RiskLevel.MEDIUM to CreditCardRiskUsage(quota = Long.MAX_VALUE, usage = 0),
        RiskLevel.HIGH to CreditCardRiskUsage(quota = Long.MAX_VALUE, usage = 0),
    ),
)

data class CreditCardRiskUsage(
    val quota: Long,
    val usage: Long,
) {
    val availableQuota = quota - usage
}

data class TransactionAmount(
    val totalAmount: Long,
    val feeAmount: Long,
    val amounts: Map<AccountPaymentMethodId, TransactionItemAmount>,
) {
    constructor(itemAmount: TransactionItemAmount) : this(
        totalAmount = itemAmount.totalAmount,
        feeAmount = itemAmount.feeAmount,
        amounts = mapOf(itemAmount.paymentMethodId to itemAmount),
    )

    val netAmount = totalAmount - feeAmount

    fun add(transactionAmount: TransactionAmount): TransactionAmount {
        return TransactionAmount(
            totalAmount = this.totalAmount + transactionAmount.totalAmount,
            feeAmount = this.feeAmount + transactionAmount.feeAmount,
            amounts = this.amounts.sum(transactionAmount.amounts),
        )
    }

    private fun Map<AccountPaymentMethodId, TransactionItemAmount>.sum(otherAmounts: Map<AccountPaymentMethodId, TransactionItemAmount>): Map<AccountPaymentMethodId, TransactionItemAmount> {
        val mutableAmounts = otherAmounts.toMutableMap()

        val sum = this.mapValues { entry ->
            mutableAmounts.remove(entry.key)?.let { otherItem ->
                otherItem.add(entry.value)
            } ?: entry.value
        }

        mutableAmounts.putAll(sum)

        return mutableAmounts
    }
}

data class TransactionItemAmount(
    val paymentMethodId: AccountPaymentMethodId,
    val totalAmount: Long,
    val feeAmount: Long,
) {
    val netAmount = totalAmount - feeAmount

    fun add(transactionItemAmount: TransactionItemAmount): TransactionItemAmount {
        return TransactionItemAmount(
            this.paymentMethodId,
            totalAmount = this.totalAmount + transactionItemAmount.totalAmount,
            feeAmount = this.feeAmount + transactionItemAmount.feeAmount,
        )
    }
}

fun List<TransactionAmount>.sum(): TransactionAmount {
    return this.fold(TransactionAmount(0L, 0L, emptyMap())) { total, current -> total.add(current) }
}