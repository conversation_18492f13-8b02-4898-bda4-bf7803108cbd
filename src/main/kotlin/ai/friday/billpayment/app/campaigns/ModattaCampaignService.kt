package ai.friday.billpayment.app.campaigns

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.integrations.IModattaCampaignAdapter
import ai.friday.billpayment.app.journey.UserJourneyEvent
import ai.friday.billpayment.app.journey.UserJourneyService
import arrow.core.Either
import arrow.core.flatMap
import arrow.core.right
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
open class ModattaCampaignService(
    private val accountService: AccountService,
    private val modattaCampaignAdapter: IModattaCampaignAdapter,
    private val userJourneyService: UserJourneyService,
) {
    open fun redeemCode(accountId: AccountId, code: String): Either<Exception, RedeemCodeResult> {
        return modattaCampaignAdapter.redeemCode(code).flatMap {
            if (it === RedeemCodeResult.REDEEMED) {
                tryUpdateCrmContactWithCampaingGroup(accountId)
                tryTrackUserJourney(accountId)
            }

            return it.right()
        }
    }

    private fun tryUpdateCrmContactWithCampaingGroup(accountId: AccountId) {
        try {
            accountService.addGroups(accountId, listOf(AccountGroup.MODATTA_MISSION_CAMPAIGN))
        } catch (e: Exception) {
            LOG.error(
                Markers.append("accountId", accountId.value),
                "ModattaCampaignService#tryUpdateCrmContactWithCampaingGroup",
                e,
            )
        }
    }

    private fun tryTrackUserJourney(accountId: AccountId) {
        try {
            userJourneyService.trackEventAsync(accountId, UserJourneyEvent.RedeemModattaCampaign)
        } catch (e: Exception) {
            LOG.error(
                Markers.append("accountId", accountId.value),
                "ModattaCampaignService#tryTrackUserJourney",
                e,
            )
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ModattaCampaignService::class.java)
    }
}

enum class RedeemCodeResult(val value: String) {
    REDEEMED("redeemed"),
    ALREADY_REDEEMED("already-redeemed"),
    INVALID_CODE("invalid-code"),
}