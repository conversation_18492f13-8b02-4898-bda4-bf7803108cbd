package ai.friday.billpayment.app.openfinance

import ai.friday.billpayment.app.integrations.ConfirmSweepingCashInError
import ai.friday.billpayment.app.integrations.EndToEnd
import ai.friday.billpayment.app.integrations.OFSweepingCashIn
import ai.friday.billpayment.app.integrations.OFSweepingConsent
import ai.friday.billpayment.app.integrations.OpenFinanceConsentService
import ai.friday.billpayment.app.integrations.RequestSweepingCashInCommand
import ai.friday.billpayment.app.integrations.RequestSweepingCashInError
import ai.friday.billpayment.app.integrations.SweepingAccountServiceInterface
import ai.friday.billpayment.app.integrations.SweepingCashInId
import ai.friday.billpayment.app.integrations.SweepingParticipant
import ai.friday.billpayment.app.wallet.WalletId
import arrow.core.Either
import io.micronaut.context.annotation.Secondary
import jakarta.inject.Singleton

@Singleton
@Secondary
class NoopSweepingAccountService : SweepingAccountServiceInterface, OpenFinanceConsentService {
    override fun requestSweepingCashIn(command: RequestSweepingCashInCommand): Either<RequestSweepingCashInError, EndToEnd> {
        TODO("OpenFinance is not enabled")
    }

    override fun confirmSweepingCashIn(endToEnd: EndToEnd): Either<ConfirmSweepingCashInError, SweepingCashInId> {
        TODO("OpenFinance is not enabled")
    }

    override fun getAuthorizedSweepingConsents(walletId: WalletId): List<OFSweepingConsent> = emptyList()

    override fun getLastActiveCashIns(walletId: WalletId): List<OFSweepingCashIn> = emptyList()

    override fun getParticipants(): List<SweepingParticipant> = emptyList()
}