package ai.friday.billpayment.app.openfinance

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.OpenFinanceBankAccountService
import ai.friday.billpayment.app.integrations.OpenFinanceParticipantId
import ai.friday.billpayment.app.wallet.WalletId
import io.micronaut.context.annotation.Secondary
import jakarta.inject.Singleton

@Singleton
@Secondary
class NoopOpenFinanceBankAccountService : OpenFinanceBankAccountService {
    override fun getBalances(accountId: AccountId, participantIds: List<OpenFinanceParticipantId>, walletId: WalletId): Map<OpenFinanceParticipantId, Long?> = emptyMap()
}