package ai.friday.billpayment.app

data class EmailAddress private constructor(
    val value: String,
    val domain: String,
    val recipient: String,
) {

    constructor(email: String) : this(
        value = email.lowercase(),
        domain = email.lowercase().substringAfter("@"),
        recipient = email.lowercase().takeWhile { it != '@' },
    )

    override fun toString(): String {
        return value
    }
}