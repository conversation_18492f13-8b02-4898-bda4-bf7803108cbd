package ai.friday.billpayment.app.notification

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.integrations.FeatureConfiguration

interface ShouldNotifyUser {
    fun shouldNotify(account: Account, type: NotificationType): Boolean
}

@FridayMePoupe
class DefaultShouldNotifyUser(private val featureConfiguration: FeatureConfiguration) : ShouldNotifyUser {
    override fun shouldNotify(account: Account, type: NotificationType): Boolean {
        if (featureConfiguration.silenceNotifications &&
            !account.configuration.groups.contains(AccountGroup.ALPHA)
        ) {
            return false
        }

        if (!account.configuration.receiveNotification) return false

        return true
    }
}