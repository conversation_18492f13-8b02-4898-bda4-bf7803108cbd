package ai.friday.billpayment.app.notification

import ai.friday.billpayment.and
import ai.friday.billpayment.app.integrations.NotifyService
import ai.friday.billpayment.log
import io.micronaut.context.ApplicationContext
import jakarta.inject.Singleton
import java.lang.reflect.ParameterizedType
import java.lang.reflect.Type
import org.slf4j.LoggerFactory

interface NotificationRouterService {
    fun <T : NotifyRequest> routeNotification(request: T)
}

@Singleton
class DefaultNotificationRouterService(context: ApplicationContext) : NotificationRouterService {
    private val logger = LoggerFactory.getLogger(DefaultNotificationRouterService::class.java)

    private var beansRef: Map<Type, NotifyService<NotifyRequest>>

    init {
        // get all NotifyService beans at micronaut context
        val beans = context.getBeansOfType(NotifyService::class.java).filterIsInstance<NotifyService<NotifyRequest>>()
        // map the bean reference as value of a map with it`s generic type as key
        // example: NotifyService<Foo> -> will create a entry in the map with key Foo::class and value the reference of service at micronaut context
        beansRef = beans
            .map { it to it.javaClass.genericInterfaces.toList() }
            .filter { it.second.filterIsInstance<ParameterizedType>().isNotEmpty() }
            .map { it.first to it.second.reduce { _, type -> type } }
            .associate { (it.second as ParameterizedType).actualTypeArguments.reduce { _, type -> type } to it.first }
    }

    override fun <T : NotifyRequest> routeNotification(request: T) {
        val markers = log("request" to request::class.simpleName)

        val bean = this.getBean(request)
        if (bean == null) {
            logger.warn(markers, "NotificationRouterService#routeNotification")
            return
        }

        logger.info(markers.and("bean" to bean::class.simpleName), "NotificationRouterService#routeNotification")

        bean.notify(request)
    }

    fun <T : NotifyRequest> getBean(request: T) = beansRef[request::class.java]
}