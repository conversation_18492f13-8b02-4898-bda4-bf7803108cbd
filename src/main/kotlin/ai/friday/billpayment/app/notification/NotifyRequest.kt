package ai.friday.billpayment.app.notification

import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.CreditCardConfiguration
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.itp.PaymentIntentId
import ai.friday.billpayment.app.onepixpay.OnePixPayId
import ai.friday.billpayment.app.payment.BoletoReceiptData
import ai.friday.billpayment.app.payment.InvoiceReceiptData
import ai.friday.billpayment.app.payment.PixReceiptData
import ai.friday.billpayment.app.utilityaccount.UtilityAccount
import ai.friday.billpayment.app.wallet.Invite
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import java.time.Duration
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime

interface NotifyRequest

data class AddFailureNotificationRequest(
    val members: List<Member>,
    val source: ActionSource,
    val from: EmailAddress,
    val subject: String,
    val walletName: String,
    val billId: BillId?,
    val walletId: WalletId,
    val result: CreateBillResult,
) : NotifyRequest

data class BillComingDueLastWarnNotificationRequest(
    val members: List<Member>,
    val wallet: Wallet,
    val dueDate: LocalDate,
    val paymentLimitTime: LocalTime,
    val hint: String?,
) : NotifyRequest

data class BillComingDueNotificationRequest(
    val members: List<Member>,
    val author: Member?,
    val walletName: String,
    val walletId: WalletId,
    val billView: BillView,
    val hint: String? = null,
) : NotifyRequest

data class BillCreatedNotificationRequest(
    val members: List<Member>,
    val payee: String,
    val amount: Long,
    val dueDate: LocalDate,
    val billType: BillType,
    val billId: BillId,
    val walletId: WalletId,
    val walletName: String,
    val author: Member?,
    val description: String?,
    val source: ActionSource,
    val hint: String?,
) : NotifyRequest

data class BillOverdueYesterdayNotificationRequest(
    val members: List<Member>,
    val walletId: WalletId,
    val walletName: String,
    val hint: String?,
) : NotifyRequest

data class BillScheduleCanceledDueAmountHigherThanDailyLimitNotificationRequest(
    val members: List<Member>,
    val walletId: WalletId,
    val walletName: String,
    val payee: String,
    val totalAmount: Long,
    val type: BillType,
    val author: Member,
) : NotifyRequest

data class BillScheduleExpiredNotificationRequest(
    val members: List<Member>,
    val walletId: WalletId,
    val walletName: String,
    val payee: String,
    val dueDate: LocalDate,
    val totalAmount: Long,
    val billId: BillId,
) : NotifyRequest

data class BillSchedulePostponedDueLimitReachedNotificationRequest(
    val members: List<Member>,
    val walletId: WalletId,
    val walletName: String,
    val payee: String,
    val totalAmount: Long,
    val type: BillType,
    val author: Member,
) : NotifyRequest

data class BoletoReceiptNotificationRequest(
    val members: List<Member>,
    val receiptData: BoletoReceiptData,
    val pdfByteArray: ByteArray,
    val mailReceiptHtml: CompiledHtml,
) : NotifyRequest {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as BoletoReceiptNotificationRequest

        if (members != other.members) return false
        if (receiptData != other.receiptData) return false
        if (!pdfByteArray.contentEquals(other.pdfByteArray)) return false
        if (mailReceiptHtml != other.mailReceiptHtml) return false

        return true
    }

    override fun hashCode(): Int {
        var result = members.hashCode()
        result = 31 * result + receiptData.hashCode()
        result = 31 * result + pdfByteArray.contentHashCode()
        result = 31 * result + mailReceiptHtml.hashCode()
        return result
    }
}

data class CashInFailureNotificationRequest(
    val members: List<Member>,
    val amount: Long,
    val errorMessage: String,
    val walletId: WalletId,
    val walletName: String,
) : NotifyRequest

data class CashInPaymentInsufficientBalanceTodayNotificationRequest(
    val members: List<Member>,
    val walletId: WalletId,
    val walletName: String,
    val pendingAmountToday: Long,
    val senderName: String,
    val senderDocument: String,
    val amount: Long,
) : NotifyRequest

data class CashInPaymentSufficientBalanceTodayNotificationRequest(
    val members: List<Member>,
    val walletId: WalletId,
    val walletName: String,
    val senderName: String,
    val senderDocument: String,
    val amount: Long,
) : NotifyRequest

data class CashInNotificationRequest(
    val members: List<Member>,
    val walletId: WalletId,
    val walletName: String,
    val senderName: String,
    val senderDocument: String,
    val amount: Long,
) : NotifyRequest

data class CreditCardEnabledNotificationRequest(
    val accountId: AccountId,
    val creditCardConfiguration: CreditCardConfiguration,
) : NotifyRequest

data class FirstBillScheduledNotificationRequest(
    val account: Account,
) : NotifyRequest

data class InsufficientBalanceTodayNotificationRequest(
    val members: List<Member>,
    val pendingScheduledAmountToday: Long,
    val pendingAmountTotal7Days: Long,
    val pendingAmountTotal15Days: Long,
    val walletId: WalletId,
    val walletName: String,
    val isAfterHours: Boolean,
) : NotifyRequest

data class InvitedMemberNotificationRequest(
    val invite: Invite,
    val receiver: EmailAddress,
) : NotifyRequest

data class InviteReminderNotificationRequest(
    val invite: Invite,
    val account: Account?,
) : NotifyRequest

data class InvoicePaymentUndoneNotificationRequest(
    val members: List<Member>,
    val walletName: String,
    val author: Member,
    val recipient: Recipient,
    val amount: Long,
    val settleDate: LocalDate,
) : NotifyRequest

data class InvoiceReceiptNotificationRequest(
    val members: List<Member>,
    val receiptData: InvoiceReceiptData,
    val pdfByteArray: ByteArray,
    val mailReceiptHtml: CompiledHtml,
) : NotifyRequest {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as InvoiceReceiptNotificationRequest

        if (members != other.members) return false
        if (receiptData != other.receiptData) return false
        if (!pdfByteArray.contentEquals(other.pdfByteArray)) return false
        if (mailReceiptHtml != other.mailReceiptHtml) return false

        return true
    }

    override fun hashCode(): Int {
        var result = members.hashCode()
        result = 31 * result + receiptData.hashCode()
        result = 31 * result + pdfByteArray.contentHashCode()
        result = 31 * result + mailReceiptHtml.hashCode()
        return result
    }
}

data class NotVisibleBillAlreadyExistsNotificationRequest(
    val member: Member,
    val walletId: WalletId,
    val billId: BillId,
    val payee: String,
    val dueDate: LocalDate,
    val totalAmount: Long,
    val walletName: String,
) : NotifyRequest

data class OnePixPayFailureNotificationRequest(
    val accountId: AccountId,
) : NotifyRequest

data class OnePixPayNotificationRequest(
    val members: List<Member>,
    val walletName: String,
    val onePixPayId: OnePixPayId<*>,
    val bills: List<BillView>,
    val delay: Duration,
) : NotifyRequest

data class OptOutNotificationRequest(
    val account: Account,
) : NotifyRequest

data class PaymentIntentFailedNotificationRequest(
    val accountId: AccountId,
    val paymentIntentId: PaymentIntentId,
) : NotifyRequest

data class PixNotReceivedFailureNotificationRequest(
    val accountId: AccountId,
    val wallet: Wallet,
    val amount: Long,
    val senderName: String,
    val senderDocument: String,
) : NotifyRequest

data class PixReceiptNotificationRequest(
    val members: List<Member>,
    val receiptData: PixReceiptData,
    val pdfByteArray: ByteArray,
    val mailReceiptHtml: CompiledHtml,
) : NotifyRequest

data class RegisterCompletedNotificationRequest(
    val accountId: AccountId,
    val mobilePhone: MobilePhone,
    val name: String,
) : NotifyRequest

data class RegisterUpdatedNotificationRequest(
    val accountId: AccountId,
    val mobilePhone: MobilePhone,
    val name: String,
) : NotifyRequest

data class ScheduledBillNotPayableNotificationRequest(
    val members: List<Member>,
    val walletId: WalletId,
    val walletName: String,
    val author: Member,
    val payee: String,
    val dueDate: LocalDate,
    val totalAmount: Long,
    val description: String,
    val billId: BillId,
) : NotifyRequest

data class SubscriptionCanceledNotificationRequest(
    val accountId: AccountId,
) : NotifyRequest

data class SubscriptionCreatedNotificationRequest(
    val accountId: AccountId,
    val dueDate: LocalDate,
    val amount: Long,
) : NotifyRequest

data class SubscriptionInsufficientBalanceNotificationRequest(
    val members: List<Member>,
    val walletId: WalletId,
    val walletName: String,
    val amount: Long,
) : NotifyRequest

data class SubscriptionOverdueNotificationRequest(
    val accountId: AccountId,
    val days: Long,
) : NotifyRequest

data class SubscriptionOverdueWithSurveyNotificationRequest(
    val accountId: AccountId,
    val days: Long,
) : NotifyRequest

data class TokenNotificationRequest(
    val accountId: AccountId,
    val mobilePhone: MobilePhone,
    val token: String,
) : NotifyRequest

data class TransferNotPayableNotificationRequest(
    val members: List<Member>,
    val walletId: WalletId,
    val walletName: String,
    val billId: BillId,
    val author: Member,
    val recipientName: String,
    val dueDate: LocalDate,
    val amount: Long,
    val errorMessage: String,
) : NotifyRequest

data class UserActivatedNotificationRequest(
    val account: Account,
) : NotifyRequest

data class UserAuthenticationRequiredNotificationRequest(
    val accountId: AccountId,
) : NotifyRequest

data class UtilityAccountUpdatedStatusNotificationRequest(
    val account: Account,
    val utilityAccount: UtilityAccount,
) : NotifyRequest

data class WalletMemberJoinedNotificationRequest(
    val founder: Member,
    val inviteeFullName: String,
    val walletName: String,
) : NotifyRequest

data class EmailNotificationRequest(
    val account: Account,
    val mailReceiptHtml: CompiledHtml,
    val pdfByteArray: ByteArray,
    val fileName: String,
) : NotifyRequest {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as EmailNotificationRequest

        if (account != other.account) return false
        if (mailReceiptHtml != other.mailReceiptHtml) return false
        if (!pdfByteArray.contentEquals(other.pdfByteArray)) return false
        if (fileName != other.fileName) return false

        return true
    }

    override fun hashCode(): Int {
        var result = account.hashCode()
        result = 31 * result + mailReceiptHtml.hashCode()
        result = 31 * result + pdfByteArray.contentHashCode()
        result = 31 * result + fileName.hashCode()
        return result
    }
}

data class EmailNotProcessedNotificationRequest(
    val members: List<Member>,
    val walletName: String,
    val sender: EmailAddress,
    val subject: String,
    val dateTime: ZonedDateTime,
) : NotifyRequest

data class InsecureBillNotificationRequest(
    val members: List<Member>,
    val subject: String,
) : NotifyRequest