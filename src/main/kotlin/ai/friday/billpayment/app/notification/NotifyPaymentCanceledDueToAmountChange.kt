package ai.friday.billpayment.app.notification

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.BillPaymentScheduled
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.NotificationService
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.via1.communicationcentre.app.notification.NotificationFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

interface NotifyPaymentCanceledDueToAmountChange {
    fun notify(scheduleCanceled: BillPaymentScheduleCanceled)
}

@FridayMePoupe
open class PaymentCanceledDueToAmountChangeNotificationService(
    private val walletService: WalletService,
    private val billEventRepository: BillEventRepository,
    private val notificationService: NotificationService,
    private val templatesConfiguration: TemplatesConfiguration,
) : NotifyPaymentCanceledDueToAmountChange {

    private val logger = LoggerFactory.getLogger(PaymentCanceledDueToAmountChangeNotificationService::class.java)
    override fun notify(scheduleCanceled: BillPaymentScheduleCanceled) {
        if (scheduleCanceled.reason != ScheduleCanceledReason.BILL_AMOUNT_CHANGED) return

        val bill = billEventRepository.getBillById(scheduleCanceled.billId).getOrElse { throw IllegalStateException() }

        with(bill) {
            try {
                val wallet = walletService.findWallet(walletId)
                val members = wallet.getMembersCanView(this)

                val lastScheduleAmount = bill.history.filterIsInstance<BillPaymentScheduled>().last().amount

                notify(members, walletId, billId, bill.getPayee(), lastScheduleAmount, bill.amountTotal)
            } catch (e: Exception) {
                logger.error(
                    Markers.append("event", scheduleCanceled).andAppend(
                        "description",
                        "notifyScheduleBillCanceledDueAmountChanged",
                    ),
                    "PaymentCanceledDueToAmountChangeNotificationService#error",
                    e,
                )
            }
        }
    }

    fun notify(members: List<Member>, walletId: WalletId, billId: BillId, payee: String, oldAmount: Long, currentAmount: Long) {
        notificationService.notifyMembers(members, NotificationType.BILL_SCHEDULE_CANCELED_DUE_AMOUNT_CHANGED) { account ->
            WhatsappNotification(
                receiver = MobilePhone(account.mobilePhone),
                accountId = account.accountId,
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletBillScheduleCanceledDueAmountChanged),
                configurationKey = "wallet-bill-schedule-canceled-due-amount-changed",
                parameters = buildList {
                    add(payee)
                    add(NotificationFormatter.buildFormattedAmount(oldAmount))
                    add(NotificationFormatter.buildFormattedAmount(currentAmount))
                },
                buttonWhatsAppParameter = buildWalletBillButtonPath(walletId = walletId, billId = billId),
            )
        }
    }
}