package ai.friday.billpayment.app.notification

import ai.friday.billpayment.app.MobilePhone
import jakarta.inject.Singleton

@Singleton
class WhatsappNotificationBuilder(private val multiChannelNotificationConfigurations: MultiChannelNotificationConfigurations) {
    fun buildFromMultiChannelNotification(multiChannelNotification: MultiChannelNotification): WhatsappNotification {
        val configuration = multiChannelNotificationConfigurations.getWhatsappConfiguration(multiChannelNotification.configurationName)
        val template = configuration.template
        return WhatsappNotification(
            receiver = MobilePhone(multiChannelNotification.receiver.mobilePhone),
            accountId = multiChannelNotification.receiver.accountId,
            template = NotificationTemplate(template),
            configurationKey = multiChannelNotification.configurationName.name,
            parameters = configuration.parameters?.mapNotNull { multiChannelNotification.parameters[it] } ?: emptyList(),
            quickReplyButtonsWhatsAppParameter = configuration.quickReplyParameters?.mapNotNull { multiChannelNotification.quickReplyButtons[it]?.value } ?: emptyList(),
            quickRepliesStartIndex = null,
            buttonWhatsAppParameter = multiChannelNotification.actionButton,
            media = multiChannelNotification.media,
        )
    }
}