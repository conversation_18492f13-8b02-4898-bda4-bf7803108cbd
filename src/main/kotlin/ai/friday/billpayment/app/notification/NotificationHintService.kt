package ai.friday.billpayment.app.notification

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.hasEarlyAccess
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.NotificationHintConfiguration
import ai.friday.billpayment.app.integrations.ScheduledBillRepository
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime

interface NotificationHintService {
    fun getBillCreated(walletId: WalletId, founder: Member): String?
    fun getBillComingDue(walletId: WalletId, founder: Member): String?
    fun getBillComingDueLastWarn(walletId: Wallet<PERSON>d, founder: Member): String?
    fun getBillOverdueYesterday(walletId: <PERSON><PERSON><PERSON><PERSON>, founder: Member): String?
}

@FridayMePoupe
class DefaultNotificationHintService(
    private val billRepository: BillRepository,
    private val scheduledBillRepository: ScheduledBillRepository,
    private val notificationHintConfiguration: NotificationHintConfiguration,
) : NotificationHintService {

    override fun getBillCreated(walletId: WalletId, founder: Member): String? {
        if (!shouldUseHint(walletId, founder)) {
            return null
        }

        return chooseHint(notificationHintConfiguration.billCreated, founder)
    }

    override fun getBillComingDue(walletId: WalletId, founder: Member): String? {
        if (!shouldUseHint(walletId, founder)) {
            return null
        }

        return chooseHint(notificationHintConfiguration.billComingDue, founder)
    }

    override fun getBillComingDueLastWarn(walletId: WalletId, founder: Member): String? {
        if (!shouldUseHint(walletId, founder)) {
            return null
        }

        return chooseHint(notificationHintConfiguration.billComingDueLastWarn, founder)
    }

    override fun getBillOverdueYesterday(walletId: WalletId, founder: Member): String? {
        if (!shouldUseHint(walletId, founder)) {
            return null
        }

        return chooseHint(notificationHintConfiguration.billOverdueYesterday, founder)
    }

    private fun shouldUseHint(walletId: WalletId, founder: Member): Boolean {
        val isActive = hasPaidBills(walletId = walletId) || hasScheduledBills(walletId = walletId)

        return !isActive || founder.accountId.hasEarlyAccess()
    }

    private fun hasPaidBills(walletId: WalletId) = billRepository.getPaidBills(
        walletId = walletId,
        startDate = getZonedDateTime().minusMonths(1),
        endDate = getZonedDateTime(),
        types = null,
    ).any { it.source !is ActionSource.OpenFinance }

    private fun hasScheduledBills(walletId: WalletId) =
        scheduledBillRepository.findAllScheduledBillsByWalletId(walletId).isNotEmpty()

    private fun chooseHint(hints: List<String>, founder: Member): String {
        val notificationsSize = hints.size

        val hint = hints[
            getZonedDateTime().toEpochSecond()
                .toInt() % notificationsSize,
        ]

        return hint.replace("seucpf", founder.document)
    }
}