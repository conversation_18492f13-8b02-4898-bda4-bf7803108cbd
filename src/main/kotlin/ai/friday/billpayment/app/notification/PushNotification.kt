package ai.friday.billpayment.app.notification

import ai.friday.billpayment.app.account.AccountId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.net.URI
import java.time.ZonedDateTime

interface PushNotificationService {
    fun sendNotificationAsync(accountId: AccountId, content: PushNotificationContent): Result<SendPushAsyncResult>
    fun sendNotificationAsync(accountId: AccountId, billPaymentNotification: BillPaymentNotification): Result<SendPushAsyncResult>
    fun canSendViaPush(billPaymentNotification: BillPaymentNotification): Boolean
    fun sendNotification(accountId: AccountId, content: PushNotificationContent): Result<SendPushNotificationToAccountIdResult>
}

interface PushNotificationPublisher {
    fun publish(accountId: AccountId, content: PushNotificationContent): Result<SendPushAsyncResult>
}

sealed class SendPushAsyncResult {
    data object Requested : SendPushAsyncResult()
    data object TemplateNotFound : SendPushAsyncResult()
    data object UnableToCreatePush : SendPushAsyncResult()
}

data class PushNotificationContent(val title: String, val body: String, val url: URI?, val imageUrl: URI?, val sentAt: ZonedDateTime = getZonedDateTime())

data class SendPushNotificationToAccountIdResult(val sent: Int, val failures: Int, val disabled: Int, val stale: Int, val purged: Int)