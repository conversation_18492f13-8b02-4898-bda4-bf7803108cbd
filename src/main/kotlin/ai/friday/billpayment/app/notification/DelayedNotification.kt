package ai.friday.billpayment.app.notification

import com.fasterxml.jackson.annotation.JsonIgnore
import io.via1.communicationcentre.app.notification.Notification
import java.time.Duration

abstract class DelayedNotification(duration: Duration) : Notification() {
    @JsonIgnore
    val delay = duration.toSeconds().toInt()

    init {
        if (duration.toSeconds() > Int.MAX_VALUE) {
            throw ArithmeticException("Delay must not exceed integer max value")
        }
    }
}

fun Notification.delay() =
    if (this is DelayedNotification) {
        this.delay
    } else {
        null
    }