package ai.friday.billpayment.app.notification

import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.onepixpay.OnePixPayId
import ai.friday.billpayment.app.utilityaccount.Utility
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import java.time.LocalDate
import java.time.LocalTime

fun buildWalletButtonPath(walletId: WalletId): ButtonParameter {
    return ButtonDeeplinkParameter("carteira/${walletId.value}/resumo")
}

fun buildWalletBillButtonPath(
    walletId: WalletId,
    billId: BillId,
): ButtonParameter {
    return ButtonDeeplinkParameter("contas/carteira/${walletId.value}/bill-timeline-entry@${billId.value}")
}

fun buildReceiptBillButtonPath(
    walletId: WalletId,
    billId: BillId,
): ButtonParameter {
    return ButtonDeeplinkParameter("comprovante/${walletId.value}/${billId.value}")
}

fun buildChangeWalletLimitsButtonPath(walletId: WalletId): ButtonParameter {
    return ButtonDeeplinkParameter("carteira/${walletId.value}/limites")
}

fun buildWalletTimelineButtonPath(walletId: WalletId): ButtonParameter {
    return ButtonDeeplinkParameter(buildWalletTimelinePath(walletId))
}

fun buildWalletTimelineQuickReply(walletId: WalletId): ButtonRawParameter {
    return ButtonRawParameter(buildWalletTimelinePath(walletId))
}

fun buildWhatsAppTrackedDeeplinkParameter(
    path: String,
    event: String,
): ButtonParameter {
    return ButtonTrackedDeeplinkParameter(path = path, event = event)
}

fun buildWalletTimelinePath(walletId: WalletId): String {
    return "contas/carteira/${walletId.value}"
}

fun buildWalletTimelineDatePath(date: LocalDate): ButtonParameter {
    return ButtonDeeplinkParameter("contas/todos/$date")
}

fun buildWalletInviteMemberWithAccountButtonPath(walletId: WalletId): ButtonParameter {
    return ButtonDeeplinkParameter("meus-convites/${walletId.value}")
}

private fun Utility.toPathName(): String {
    return name.lowercase().replace("_", "-")
}

fun buildMaskedBillName(billName: String): String {
    val billNameLength = billName.length
    return billName.replaceRange(1 until billNameLength - 1, "*".repeat(billNameLength - 1))
}

fun buildUtilityAccountButtonPath(
    walletId: WalletId,
    utility: Utility,
): ButtonParameter {
    return ButtonDeeplinkParameter("carteira/${walletId.value}/contas-de-consumo/conectar-conta/${utility.toPathName()}")
}

fun buildOnePixPayQuickReply(
    onePixPayId: OnePixPayId<*>,
    paymentLimitTime: LocalTime,
    hasBasicAccountLimitExceeded: Boolean,
): List<String> {
    val mapper = jacksonObjectMapper()
    return buildList {
        add(
            mapper.writeValueAsString(
                mapOf(
                    "onePixPayId" to onePixPayId.value,
                    "paymentLimitTimestamp" to getZonedDateTime().with(paymentLimitTime).toEpochSecond(),
                    "limitExceeded" to hasBasicAccountLimitExceeded,
                ),
            ),
        )
    }
}