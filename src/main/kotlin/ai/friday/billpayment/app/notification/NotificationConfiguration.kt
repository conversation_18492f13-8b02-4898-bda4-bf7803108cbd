package ai.friday.billpayment.app.notification

import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Parameter
import jakarta.inject.Singleton

@ConfigurationProperties("communication-centre")
data class TemplatesConfiguration @ConfigurationInject constructor(
    val landingPageUrl: String,
    val email: EmailTemplatesConfiguration,
    val whatsappTemplates: WhatsappTemplates,
)

@ConfigurationProperties("templates-html-to-image")
data class TemplatesHtmlToImageConfiguration @ConfigurationInject constructor(
    val instagrammableInvestmentReceipt: String,
    val instagrammableExtraInvestmentReceipt: String,
    val instagrammableGoalCompletedReceipt: String,
    val instagrammableGoalEndDateReachedReceipt: String,
)

data class MultiChannelConfigurationName(val name: String)

@Singleton
data class MultiChannelNotificationConfigurations(
    val configurations: List<KnownMultiChannelNotification>,
) {
    private val configurationsMap = configurations.associateBy { MultiChannelConfigurationName(it.name) }

    fun getWhatsappConfiguration(name: MultiChannelConfigurationName): KnownMultiChannelNotification.WhatsappChannelNotificationConfiguration {
        return configurationsMap[name]?.whatsapp ?: throw IllegalArgumentException("Configuration not found for ${name.name}")
    }
}

@EachProperty("multi-channel-notification")
data class KnownMultiChannelNotification @ConfigurationInject constructor(
    @param:Parameter val name: String,
    val whatsapp: WhatsappChannelNotificationConfiguration?,
) {
    @ConfigurationProperties("whatsapp")
    data class WhatsappChannelNotificationConfiguration @ConfigurationInject constructor(
        val template: String,
        val parameters: List<String>? = null,
        val quickReplyParameters: List<String>? = null,
    )
}

/*
quando migrar a notificação para o multi-channel, pode apagar daqui.
 */
@ConfigurationProperties("communication-centre.integration.blip.templates")
data class WhatsappTemplates @ConfigurationInject constructor(
    val barcodeBillCloseOverdueKnownAuthorWithDescription: String,
    val barcodeBillCloseOverdueKnownAuthorNoDescription: String,
    val barcodeBillCloseOverdueKnownAuthorNoDescriptionWithHint: String,
    val barcodeBillCloseOverdueNoAuthorWithDescription: String,
    val barcodeBillCloseOverdueNoAuthorNoDescription: String,
    val invoiceCloseOverdueWithDescription: String,
    val invoiceCloseOverdueNoDescription: String,
    val billComingDueSecondaryWallet: String,
    val walletOnePixPay: String,
    val walletOnePixPaySingular: String,
    val walletCashInWithCreditCardFailed: String,
    val walletPostalBoxAddDuplicate: String,
    val walletPostalBoxAddNotAuthorized: String,
    val walletPostalBoxAddNonPayableNoData: String,
    val walletPostalBoxAddNonPayable: String,
    val walletPostalBoxAddPaidExternally: String,
    val walletPostalBoxAddPaidExternallyWithoutData: String,
    val walletPostalBoxAddValidationFailure: String,
    val walletInvoicePaymentFailure: String,
    val walletLastAlertPaymentOverdueToday: String,
    val walletLastAlertPaymentOverdueTodayWithHint: String,
    val welcomeAccountCreatedWithoutChatbot: String,
    val upgradeCompleted: String,
    val registerDenied: String,
    val registerUpgraded: String,
    val walletMemberJoined: String,
    val userAuthenticationRequired: String,
    val registerUpdated: String,
    val walletInvoicePaymentReturned: String,
    val walletBarcodeBillReceipt: String,
    val walletInvoiceBillReceipt: String,
    val walletPixBillReceipt: String,
    val walletInvestmentBillReceiptWithBadge: String,
    val walletBillReceiptImage: String,
    val walletCashInInsufficientBalance: String,
    val walletBarcodeBillApprovedPaymentNonPayable: String,
    val walletInsufficientBalanceAfterHours: String,
    val walletInsufficientBalance: String,
    val insufficientBalanceFirstCashIn: String,
    val walletInsufficientBalanceSecondaryWallet: String,
    val walletBillSchedulePostponedDueLimitReached: String,
    val walletBillScheduleCanceledDueAmountHigherThanDailyLimit: String,
    val fredOnboardingWelcome: String,
    val triPixReminderNextDay: String,
    val triPixReminderLastDay: String,
    val triPixExpired: String,
    val walletBillScheduleCanceledDueCreditCardDenied: String,
    val postalBoxAddNotVisibleBillAlreadyExists: String,
    val walletInviteAssistantWithAccount: String,
    val walletInviteReminderAssistantWithAccount: String,
    val firstBillScheduled: String,
    val registerToken: String,
    val itpTransactionFailed: String,
    val signUpBasicUpdateDataNeeded: String,
    val pixNotReceivedFailure: String,
    val subscriptionInsufficientBalance: String,
    val subscriptionOverdue: String,
    val inAppSubscriptionOverdue: String,
    val subscriptionOverdueCloseAccount: String,
    val subscriptionOverdueWarningAccountClosure: String,
    val subscriptionOverdueDay32: String,
    val subscriptionOverdueDay04: String,
    val subscriptionOverdueDay02: String,
    val subscriptionOverdueDay01: String,
    val subscriptionGrantedByInvestment: String,
    val pixSubscriptionOverdueNotificationChannelDowngradeWarning: String,
    val inAppSubscriptionOverdueNotificationChannelDowngradeWarning: String,
    val subscriptionCreated: String,
    val walletOnePixPayFailure: String,
    val creditCardEnabled: String,
    val utilityAccountInvoiceScanError: String,
    val utilityAccountInvoiceNotFound: String,
    val utilityAccountDisconnectLegacyAccount: String,
    val utilityAccountRequestReconnection: String,
    val connectedFlowUtilityAccount: String,
    val connectedFlowUtilityAccountWithBills: String,
    val connectedUtilityAccount: String,
    val utilityAccountUpdateStatusWithDetails: String,
    val disconnectedScrapingUtilityAccount: String,
    val utilityAccountUpdateStatus: String,
    val walletFounderSelfCashInSufficientBalance: String,
    val reminderExpiredNotification: String,
    val reminderExpiredNotificationSingular: String,
    val reminderNotification: String,
    val postalBoxAddManualReview: String,
    val mailboxBillInsecure: String,
    val walletApprovedPaymentCancelled: String,
    val walletBillScheduleCanceledDueAmountChanged: String,
    val sweepingCashInError: String,
    val sweepingCashInInsufficientBalanceError: String,
    val investmentRedemptionCreated: String,
    val investmentRedemptionCompleted: String,
    val investmentRedemptionFailed: String,
    val investmentRedemptionPartialFailed: String,
    val sweepingAccountConnected: String,
    val sweepingAccountConnectedWithDataIncentive: String,
    val sweepingAccountConnectionFailed: String,
    val sweepingAccountEdit: String,
    val sweepingAccountEditFailed: String,
    val dataConsentConnected: String,
    val dataConsentConnectionFailed: String,
    val goalWithDailyLiquidityCompletedByDate: String,
    val goalWithDailyLiquidityCompletedByAmount: String,
    val goalWithMaturityLiquidityCompletedByDate: String,
    val goalWithMaturityLiquidityCompletedByAmount: String,

    val goalWithDailyLiquidityCompletedByDateWithBadge: String,
    val goalWithDailyLiquidityCompletedByAmountWithBadge: String,
    val goalWithMaturityLiquidityCompletedByDateWithBadge: String,
    val goalWithMaturityLiquidityCompletedByAmountWithBadge: String,

    val goalWithDailyLiquidityPausedByDate: String,
    val goalWithDailyLiquidityPausedByAmount: String,

    val sweepingTransferChatbotParticipantBalanceError: String,
    val sweepingTransferChatbotParticipantLimitError: String,
    val sweepingTransferChatbotConsentLimitError: String,
    val sweepingTransferChatbotInvalidConsentError: String,
    val sweepingTransferChatbotRetryableError: String,
    val sweepingTransferWebappParticipantBalanceError: String,
    val sweepingTransferWebappParticipantLimitError: String,
    val sweepingTransferWebappConsentLimitError: String,
    val sweepingTransferWebappInvalidConsentError: String,
    val sweepingTransferWebappRetryableError: String,
)

@ConfigurationProperties("communication-centre.email.templates")
data class EmailTemplatesConfiguration
@ConfigurationInject constructor(
    val local: Local,
    val ses: SES,
) {
    @ConfigurationProperties("local")
    data class Local @ConfigurationInject constructor(
        val emailVerificationTokenPath: String,
        val kycDossierFormPath: String,
        val declarationOfResidencyFormPath: String,
        val accountStatementReportPath: String,
        val walletSummaryReportPath: String,
        val emailPasswordRecoveryTokenPath: String,
        val statementPdf: String,
        val pixReceipt: String,
        val investmentReceipt: String,
        val invoiceReceipt: String,
        val barcodeBillReceipt: String,
        val mailReceipt: String,
        val inform: String,
        val investmentRedemptionFinished: String,
    )

    @ConfigurationProperties("ses")
    data class SES @ConfigurationInject constructor(
        val walletInviteAssistantWithoutAccount: String,
        val walletInviteCollaboratorWithoutAccount: String,
        val walletInviteReminderAssistantWithoutAccount: String,
        val walletInviteReminderCollaboratorWithoutAccount: String,
    )
}