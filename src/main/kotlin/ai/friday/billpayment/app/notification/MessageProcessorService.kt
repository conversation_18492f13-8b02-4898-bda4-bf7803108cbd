package ai.friday.billpayment.app.notification

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountRegisterService
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.login.LoginService
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.app.message.QueueMessage
import ai.friday.billpayment.app.message.QueueNotificationTO
import ai.friday.morning.log.andAppend
import com.fasterxml.jackson.databind.DeserializationFeature
import io.micronaut.context.annotation.Property
import io.via1.communicationcentre.app.integrations.CheckableNotificationService
import io.via1.communicationcentre.app.integrations.NotificationService
import io.via1.communicationcentre.app.notification.Button
import io.via1.communicationcentre.app.notification.Notification
import io.via1.communicationcentre.app.notification.NotificationChannel
import io.via1.communicationcentre.app.notification.NotificationStatus
import jakarta.inject.Singleton
import kotlin.contracts.ExperimentalContracts
import kotlin.contracts.contract
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message
import software.amazon.awssdk.services.sqs.model.MessageSystemAttributeName

interface MessageProcessorService {
    fun process(messageBody: String)
    fun verifyStatus(message: Message): Boolean
}

@Singleton
open class MessageProcessorServiceImpl(
    private val notificationService: NotificationService,
    private val messagePublisher: MessagePublisher,
    private val accountRegisterService: AccountRegisterService,
    private val loginService: LoginService,
    private val checkableNotificationService: CheckableNotificationService,
    private val handlebarsTempate2Html2Image: HandlebarsTempate2Html2Image,
    @Property(name = "sqs.queues.whatsappNotificationVerifier") private val whatsappNotificationVerifierQueueName: String,
) : MessageProcessorService {
    override fun process(messageBody: String) {
        val markers = Markers.empty()

        val notificationWrapper = parseNotification(messageBody)

        val userId = sendNotification(notificationWrapper)

        markers.andAppend("userId", notificationWrapper.mobilePhone)

        if (notificationWrapper.notificationChannel == NotificationChannel.WHATSAPP && notificationWrapper.notificationId != null) {
            messagePublisher.sendMessage(
                QueueMessage(
                    whatsappNotificationVerifierQueueName,
                    messageBody,
                    delaySeconds = DELAY_TO_CHECK_MESSAGE_STATUS,
                ),
            )
        }

        if (shouldCreateWhatsAppLogin(notificationWrapper.notificationChannel, notificationWrapper.externalUserId)) {
            markers.andAppend("createWhatsAppLogin", "true")
            loginService.createLogin(
                providerUser =
                ProviderUser(
                    id = notificationWrapper.externalUserId,
                    providerName = ProviderName.WHATSAPP,
                    username = "",
                    emailAddress = EmailAddress(email = userId),
                ),
                accountId = AccountId(notificationWrapper.externalUserId),
                role = Role.OWNER,
            )
        }

        logger.info(markers, "MessageProcessorService#process")
        return
    }

    override fun verifyStatus(message: Message): Boolean {
        val markers = Markers.empty()

        val notificationWrapper = parseNotification(message.body())

        val notificationStatus = checkableNotificationService.checkStatus(notificationWrapper.notificationId)
        markers.andAppend("currentStatus", notificationStatus.status).andAppend("reason", notificationStatus.reason)
            .andAppend("attributes", message.attributes())
            .andAppend("retryable", notificationStatus.retryable)
            .andAppend("accountId", notificationWrapper.externalUserId)
            .andAppend("userId", notificationWrapper.mobilePhone)
            .andAppend("notificationId", notificationWrapper.notificationId)
            .andAppend("notificationChannel", notificationWrapper.notificationChannel)
            .andAppend("template", notificationWrapper.templateNotification?.template)

        val maxRetriesReached = message.receiveCount() >= MAX_PROCESSING_RETRIES
        if (maxRetriesReached) {
            markers.andAppend("maxRetriesReached", true)
        }

        val shouldDeleteMessage =
            when (notificationStatus.status!!) {
                NotificationStatus.SUCCESS -> true

                NotificationStatus.PROCESSING, NotificationStatus.UNKNOWN -> maxRetriesReached

                NotificationStatus.FAILED -> {
                    if (notificationStatus.retryable) {
                        markers.andAppend("retrying", true)
                        sendNotification(notificationWrapper)
                    }
                    !notificationStatus.retryable || maxRetriesReached
                }
            }

        logger.info(markers, "MessageProcessorService#verifyStatus")

        return shouldDeleteMessage
    }

    private fun parseNotification(messageBody: String): NotificationWrapper {
        return if (messageBody.contains("\"template\"")) {
            val notification = try {
                val notificationTO = getObjectMapper()
                    .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                    .readerFor(QueueNotificationTO::class.java).readValue<QueueNotificationTO>(messageBody)
                notificationTO.notification
            } catch (e: Exception) {
                logger.warn(append("rawMessageBody", messageBody), "MessageProcessorService", e)
                getObjectMapper().readerFor(Notification::class.java).readValue<Notification>(messageBody)
            }

            NotificationWrapper(
                notificationId = notification.notificationId,
                userId = "",
                mobilePhone = notification.mobilePhone,
                simpleResponseNotification = null,
                notificationChannel = notification.notificationChannel,
                externalUserId = notification.externalUserId,
                templateNotification = notification,
            )
        } else {
            val responseNotification = parseObjectFrom<SimpleResponseNotification>(messageBody)

            NotificationWrapper(
                notificationId = responseNotification.notificationId,
                userId = "",
                mobilePhone = responseNotification.mobilePhone,
                simpleResponseNotification = responseNotification,
                notificationChannel = NotificationChannel.WHATSAPP,
                externalUserId = responseNotification.externalUserId,
                templateNotification = null,
            )
        }
    }

    private fun sendNotification(notificationWrapper: NotificationWrapper): String {
        val userId = if (notificationWrapper.templateNotification != null) {
            notificationService.notify(notificationWrapper.templateNotification)
        } else if (notificationWrapper.simpleResponseNotification != null) {
            sendSimpleResponse(notificationWrapper.simpleResponseNotification)
        } else {
            ""
        }

        return userId
    }

    private fun sendSimpleResponse(
        responseNotification: SimpleResponseNotification,
    ): String = if (responseNotification.link != null) {
        notificationService.sendLinkResponse(
            responseNotification.message,
            responseNotification.link.displayText,
            responseNotification.link.url,
            responseNotification.mobilePhone,
            responseNotification.notificationId,
        )
    } else if (responseNotification.buttons != null) {
        notificationService.sendInteractiveResponse(
            responseNotification.message,
            responseNotification.buttons.map { it.toButton() },
            responseNotification.mobilePhone,
            responseNotification.notificationId,
        )
    } else {
        notificationService.sendSimpleResponse(
            responseNotification.message,
            responseNotification.mobilePhone,
            responseNotification.notificationId,
        )
    }

    @OptIn(ExperimentalContracts::class)
    private fun shouldCreateWhatsAppLogin(
        notificationChannel: NotificationChannel?,
        externalUserId: String?,
    ): Boolean {
        contract {
            returns(true) implies (externalUserId != null)
        }
        return notificationChannel == NotificationChannel.WHATSAPP && !externalUserId.isNullOrBlank() && !accountRegisterService.checkIsTestAccount(
            AccountId(externalUserId),
        )
    }

    private fun Message.receiveCount(): Long {
        return attributes()[MessageSystemAttributeName.APPROXIMATE_RECEIVE_COUNT]?.toLong() ?: 0
    }

    companion object {
        private val logger = LoggerFactory.getLogger(MessageProcessorService::class.java)
    }
}

private const val DELAY_TO_CHECK_MESSAGE_STATUS = 3
private const val MAX_PROCESSING_RETRIES = 10

class SimpleResponseNotification(
    val mobilePhone: String,
    val notificationId: String,
    val message: String,
    val externalUserId: String?,
    val buttons: List<ButtonTO>? = null,
    val link: LinkTO? = null,
)

data class ButtonTO(
    val text: String,
    val payload: String,
) {
    fun toButton(): Button {
        return Button(text, payload)
    }
}

data class LinkTO(
    val displayText: String,
    val url: String,
)

data class NotificationWrapper(
    val notificationId: String?,
    val externalUserId: String?,
    val mobilePhone: String?,
    val userId: String,
    val simpleResponseNotification: SimpleResponseNotification?,
    val templateNotification: Notification?,
    val notificationChannel: NotificationChannel?,
)