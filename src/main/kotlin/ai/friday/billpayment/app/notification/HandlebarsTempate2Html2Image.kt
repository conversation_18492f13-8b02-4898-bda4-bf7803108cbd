package ai.friday.billpayment.app.notification

import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.integrations.Html2ImageConverter
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.utils.PublicHttpLinkGenerator
import ai.friday.billpayment.app.utils.TemplateHelper
import io.micronaut.http.MediaType
import jakarta.inject.Singleton
import java.io.Serializable
import java.time.Duration

@Singleton
open class HandlebarsTempate2Html2Image(
    private val html2ImageConverter: Html2ImageConverter,
    private val objectRepository: ObjectRepository,
    private val publicHttpLinkGenerator: PublicHttpLinkGenerator,
) {

    fun renderTemplateImageAndCreatePublicLink(command: HandlebarsTempate2Html2ImageCommand): String? {
        val imageBytes = renderTemplateImage(command.templatePath, command.templateData)
        objectRepository.putObject(command.placeToStore, imageBytes, command.mediaType)
        return publicHttpLinkGenerator.generate(command.placeToStore, Duration.ofHours(1), command.mediaType.toString())
    }

    private fun renderTemplateImage(templatePath: String, templateData: Map<String, Serializable>): ByteArray {
        val compiledHtml = CompiledHtml(TemplateHelper.applyTemplate(templatePath, templateData))
        return html2ImageConverter.convert(compiledHtml)
    }
}

data class HandlebarsTempate2Html2ImageCommand(
    val templatePath: String,
    val templateData: Map<String, Serializable>,
    val placeToStore: StoredObject,
    val imageFormat: String,
    val mediaType: MediaType,
)