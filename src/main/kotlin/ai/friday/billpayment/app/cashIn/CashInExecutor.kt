package ai.friday.billpayment.app.cashIn

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.ScheduledBillPaymentService
import ai.friday.billpayment.app.payment.RetryForeverTransactionException
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.increaseRetryInterval
import ai.friday.morning.log.andAppend
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

interface CashInExecutor {
    fun execute(transaction: Transaction)
    fun retryTransaction(transactionId: TransactionId): Result<Unit>
}

data class StartCashInMessage(
    val transactionId: String,
)

@FridayMePoupe
open class DefaultCashInExecutor(
    private val transactionService: TransactionService,
    private val cashInHandlerLocator: CashInHandlerLocator,
    private val messagePublisher: MessagePublisher,
    private val scheduledBillPaymentService: ScheduledBillPaymentService,
) : CashInExecutor {

    override fun execute(transaction: Transaction) {
        val markers = Markers.append("accountId", transaction.payer.accountId.value)
            .andAppend("transactionId", transaction.id)
            .andAppend("amount", transaction.settlementData.totalAmount)
            .andAppend("type", transaction.type)

        try {
            val handler = cashInHandlerLocator.getHandler(transaction)
            handler.notifyStarted(transaction)

            val processedTransaction = handler.execute(transaction)
            markers.andAppend("status", processedTransaction.status)
                .andAppend("settlementData", processedTransaction.settlementData)

            transactionService.save(processedTransaction)
            when (processedTransaction.status) {
                TransactionStatus.PROCESSING -> messagePublisher.sendRetryCashinMessage(transaction.id)
                TransactionStatus.COMPLETED -> transactionCompleted(handler, processedTransaction)
                TransactionStatus.FAILED -> handler.notifyFailed(processedTransaction)
                TransactionStatus.UNDONE -> {}
            }
            logger.info(markers, "CashInExecutor#execute")
        } catch (e: Exception) {
            logger.error(markers, "CashInExecutor#execute")
        }
    }

    override fun retryTransaction(transactionId: TransactionId): Result<Unit> {
        val logName = "CashInExecutor#retryTransaction"

        val transaction = transactionService.findTransactionById(transactionId)

        val markers = Markers.append("accountId", transaction.payer.accountId.value)
            .andAppend("transactionId", transaction.id.value)
            .andAppend("amount", transaction.settlementData.totalAmount)
            .andAppend("type", transaction.type)

        if (transaction.status != TransactionStatus.PROCESSING) {
            markers.andAppend("status", transaction.status).andAppend("nothingToDo", true)
            logger.warn(markers, logName)
            return Result.success(Unit)
        }

        val handler = cashInHandlerLocator.getHandler(transaction)

        val processedTransaction = handler.retry(transaction)
        markers.andAppend("status", processedTransaction.status)
            .andAppend("settlementData", processedTransaction.settlementData)

        transactionService.save(processedTransaction)
        when (processedTransaction.status) {
            TransactionStatus.PROCESSING -> return Result.failure(RetryForeverTransactionException(transactionId, increaseRetryInterval))
            TransactionStatus.COMPLETED -> transactionCompleted(handler, processedTransaction)
            TransactionStatus.FAILED -> handler.notifyFailed(processedTransaction)
            TransactionStatus.UNDONE -> {}
        }
        logger.info(markers, logName)
        return Result.success(Unit)
    }

    private fun transactionCompleted(handler: CashInHandler, transaction: Transaction) {
        handler.notifyCompleted(transaction)
        try {
            scheduledBillPaymentService.process(transaction.walletId)
        } catch (e: Exception) {
            logger.warn("CashInExecutor#transactionCompleted", e)
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(CashInExecutor::class.java)
    }
}

interface CashInHandler {
    val supportedTransactionType: TransactionType

    fun execute(transaction: Transaction): Transaction
    fun retry(transaction: Transaction): Transaction

    fun notifyStarted(transaction: Transaction)
    fun notifyCompleted(transaction: Transaction)
    fun notifyFailed(transaction: Transaction)
}