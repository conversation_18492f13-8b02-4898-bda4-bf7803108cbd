package ai.friday.billpayment.app.cashIn.instrumentation

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.BankOperationExecuted
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.integrations.CashInAmountTier
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionStatus

enum class DepositReceivedType {
    PIX, TED
}

sealed class CashInInstrumentationEvent {
    class DepositReceived private constructor(
        val receiverAccountId: AccountId,
        val sameOwnership: Boolean,
        val tier: CashInAmountTier,
        val type: DepositReceivedType,
    ) : CashInInstrumentationEvent() {
        companion object {
            fun create(bankOperationExecuted: BankOperationExecuted, receiverDocument: String): DepositReceived {
                return DepositReceived(
                    receiverAccountId = bankOperationExecuted.accountId,
                    type = when (bankOperationExecuted.type) {
                        BankStatementItemType.TED_DIF_TITULARIDADE, BankStatementItemType.TED_MESMA_TITULARIDADE -> DepositReceivedType.TED
                        BankStatementItemType.PIX -> DepositReceivedType.PIX
                        else -> throw IllegalArgumentException("Bank statement item type not accepted type")
                    },
                    sameOwnership = bankOperationExecuted.counterpartDocument == receiverDocument,
                    tier = CashInAmountTier.find(bankOperationExecuted.amount),
                )
            }
        }
    }

    class CreditCardCashInReceived private constructor(
        val receiverAccountId: AccountId,
        val sameOwnership: Boolean,
        val tier: CashInAmountTier,
        val succeeded: Boolean,
    ) : CashInInstrumentationEvent() {
        companion object {
            fun create(transaction: Transaction): CreditCardCashInReceived {
                val target = transaction.settlementData.getTarget<ai.friday.billpayment.app.cashIn.CreditCardCashIn>()

                return CreditCardCashInReceived(
                    receiverAccountId = target.bankAccount.accountId,
                    sameOwnership = transaction.payer.accountId == target.bankAccount.accountId,
                    tier = CashInAmountTier.find(transaction.settlementData.totalAmount),
                    succeeded = when (transaction.status) {
                        TransactionStatus.COMPLETED -> true
                        TransactionStatus.FAILED -> false
                        else -> throw IllegalArgumentException("Transaction status is not supported")
                    },
                )
            }
        }
    }
}