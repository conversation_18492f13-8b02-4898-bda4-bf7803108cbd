package ai.friday.billpayment.app.cashIn

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.PaymentMethodNotFound
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.CreditCardFeeCalculatorService
import ai.friday.billpayment.app.usage.CreditCardUsageService
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right

@FridayMePoupe
open class CreditCardFraudPreventionService(
    private val accountRepository: AccountRepository,
    private val creditCardUsageService: CreditCardUsageService,
    private val creditCardFeeCalculatorService: CreditCardFeeCalculatorService,
) {

    fun check(command: CreditCardCashInCommand): Either<FraudPreventionErrors, AccountPaymentMethod> {
        return check(
            command.actionSource.accountId,
            command.paymentMethodId,
            command.netAmount,
            command.feeAmount,
            installments = 1,
        )
    }

    fun check(
        accountId: AccountId,
        paymentMethodId: AccountPaymentMethodId,
        netAmount: Long,
        feeAmount: Long,
        installments: Int,
    ): Either<FraudPreventionErrors, AccountPaymentMethod> {
        val paymentMethod =
            try {
                accountRepository.findAccountPaymentMethodByIdAndAccountId(
                    paymentMethodId,
                    accountId,
                )
            } catch (e: PaymentMethodNotFound) {
                return FraudPreventionErrors.PAYMENT_METHOD_NOT_FOUND.left()
            }

        return check(paymentMethod, netAmount, feeAmount, installments).map {
            paymentMethod
        }
    }

    fun check(
        paymentMethod: AccountPaymentMethod,
        netAmount: Long,
        feeAmount: Long,
        installments: Int,
    ): Either<FraudPreventionErrors, Unit> {
        if (paymentMethod.status != AccountPaymentMethodStatus.ACTIVE) {
            return FraudPreventionErrors.INVALID_PAYMENT_METHOD.left()
        }

        if (paymentMethod.method.type != PaymentMethodType.CREDIT_CARD) {
            return FraudPreventionErrors.INVALID_PAYMENT_METHOD.left()
        }

        val accountId = paymentMethod.accountId
        val creditCardUsage = creditCardUsageService.calculateCreditCardUsage(accountId = accountId)

        if (creditCardUsage.availableQuota < netAmount) {
            return FraudPreventionErrors.NO_LIMIT_AVAILABLE.left()
        }

        val calculatedFeeAmount =
            creditCardFeeCalculatorService.calculateFeeAmount(accountId, netAmount, installments).getOrElse {
                return FraudPreventionErrors.INVALID_FEE_AMOUNT.left()
            }
        if (calculatedFeeAmount != feeAmount) {
            return FraudPreventionErrors.INVALID_FEE_AMOUNT.left()
        }

        return Unit.right()
    }
}

enum class FraudPreventionErrors {
    PAYMENT_METHOD_NOT_FOUND, INVALID_PAYMENT_METHOD, NO_LIMIT_AVAILABLE, INVALID_FEE_AMOUNT
}