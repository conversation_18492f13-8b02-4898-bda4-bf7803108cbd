package ai.friday.billpayment.app.cashIn

import ai.friday.billpayment.adapters.lock.cashInLockProvider
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.PaymentMethodNotFound
import ai.friday.billpayment.app.account.hasEarlyAccess
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.SettlementTarget
import ai.friday.billpayment.app.payment.SinglePaymentData
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.app.payment.TransactionStatus.COMPLETED
import ai.friday.billpayment.app.payment.TransactionStatus.PROCESSING
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.toPayer
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.getOrElse
import jakarta.inject.Named

@FridayMePoupe
class CreditCardCashInService(
    private val transactionService: TransactionService,
    private val accountRepository: AccountRepository,
    private val cashInExecutor: CashInExecutor,
    private val walletRepository: WalletRepository,
    private val creditCardFraudPreventionService: CreditCardFraudPreventionService,
    @Named(cashInLockProvider) private val lockProvider: InternalLock,
) {

    fun process(command: CreditCardCashInCommand): CashInResult {
        val lock = lockProvider.acquireLock(command.walletId.value) ?: return CashInResult.OtherCashInIsProcessing

        try {
            val accountPaymentMethod = creditCardFraudPreventionService.check(command).getOrElse {
                return when (it) {
                    FraudPreventionErrors.PAYMENT_METHOD_NOT_FOUND -> CashInResult.PaymentMethodNotFound
                    FraudPreventionErrors.INVALID_PAYMENT_METHOD -> CashInResult.InvalidPaymentMethod
                    FraudPreventionErrors.NO_LIMIT_AVAILABLE -> CashInResult.QuotaLimitReached
                    FraudPreventionErrors.INVALID_FEE_AMOUNT -> CashInResult.InvalidFeeAmount
                }
            }

            val foundTransaction = try {
                transactionService.findTransactionById(command.transactionId)
            } catch (e: ItemNotFoundException) {
                null
            }

            return when {
                foundTransaction != null -> {
                    if (transactionService.checkTransactionOwnership(
                            transaction = foundTransaction,
                            walletId = command.walletId,
                            accountId = command.actionSource.accountId,
                        )
                    ) {
                        CashInResult.DuplicatedTransactionId
                    } else {
                        CashInResult.InvalidTransactionId
                    }
                }

                hasCashInProcessing(command.walletId) -> CashInResult.OtherCashInIsProcessing
                !command.accountId.hasEarlyAccess() && hasCashedInToday(command.walletId) -> CashInResult.LimitReached

                else -> doProcess(command, accountPaymentMethod)
            }
        } catch (e: Exception) {
            return CashInResult.Unknown(e)
        } finally {
            lock.unlock()
        }
    }

    private fun doProcess(
        command: CreditCardCashInCommand,
        paymentMethod: AccountPaymentMethod,
    ): CashInResult {
        val account = accountRepository.findById(command.actionSource.accountId)

        val balanceAccount = try {
            walletRepository.findAccountPaymentMethod(command.walletId)
        } catch (e: PaymentMethodNotFound) {
            return CashInResult.MissingInternalBankAccount
        }

        val totalAmount = command.netAmount + command.feeAmount
        val transaction = Transaction(
            id = command.transactionId,
            created = getZonedDateTime(),
            type = TransactionType.CASH_IN,
            payer = account.toPayer(),
            actionSource = command.actionSource,
            nsu = 0L,
            paymentData = SinglePaymentData(
                accountPaymentMethod = paymentMethod,
                details = PaymentMethodsDetailWithCreditCard(
                    paymentMethodId = command.paymentMethodId,
                    netAmount = command.netAmount,
                    feeAmount = command.feeAmount,
                    installments = 1,
                    fee = 100.0 * command.feeAmount / command.netAmount,
                    calculationId = null,
                ),
            ),
            settlementData = SettlementData(
                settlementTarget = CreditCardCashIn(amount = command.netAmount, bankAccount = balanceAccount),
                serviceAmountTax = command.feeAmount,
                totalAmount = totalAmount,
            ),
            walletId = command.walletId,
        )

        transactionService.save(transaction)

        // FIXME publicar StartCashInMessage
        cashInExecutor.execute(transaction)
        return CashInResult.Success
    }

    private fun hasCashedInToday(walletId: WalletId): Boolean =
        transactionService.findByWalletAndStatusAndType(
            walletId = walletId,
            transactionStatus = COMPLETED,
            transactionType = TransactionType.CASH_IN,
        )
            .any { it.created.toLocalDate() == getLocalDate() }

    private fun hasCashInProcessing(walletId: WalletId) =
        transactionService.findByWalletAndStatusAndType(
            walletId = walletId,
            transactionStatus = PROCESSING,
            transactionType = TransactionType.CASH_IN,
        ).isNotEmpty()
}

sealed class CashInResult {
    data object InvalidPaymentMethod : CashInResult()
    data object PaymentMethodNotFound : CashInResult()
    data object DuplicatedTransactionId : CashInResult()
    data object InvalidTransactionId : CashInResult()
    data object OtherCashInIsProcessing : CashInResult()
    data object LimitReached : CashInResult()
    data object QuotaLimitReached : CashInResult()
    data object MissingInternalBankAccount : CashInResult()
    data object InvalidFeeAmount : CashInResult()
    class Unknown(val error: Exception) : CashInResult()
    data object Success : CashInResult()
}

data class CreditCardCashIn(
    val bankAccount: AccountPaymentMethod,
    val amount: Long,
) : SettlementTarget {
    override fun settlementTargetId() = bankAccount.id.value
}

class CreditCardCashInCommand(
    val transactionId: TransactionId,
    val netAmount: Long,
    val feeAmount: Long,
    val paymentMethodId: AccountPaymentMethodId,
    val actionSource: ActionSource.Api,
    val walletId: WalletId,
    val accountId: AccountId,
)