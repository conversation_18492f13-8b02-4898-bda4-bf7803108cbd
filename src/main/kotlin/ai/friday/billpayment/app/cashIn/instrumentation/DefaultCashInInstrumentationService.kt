package ai.friday.billpayment.app.cashIn.instrumentation

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.banking.BankOperationExecuted
import ai.friday.billpayment.app.instrumentation.BaseInstrumentationService
import ai.friday.billpayment.app.instrumentation.InstrumentationRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.CashInInstrumentationService
import ai.friday.billpayment.app.payment.Transaction
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
open class DefaultCashInInstrumentationService(
    instrumentationRepository: InstrumentationRepository<CashInInstrumentationEvent>,
    private val accountRepository: AccountRepository,
) :
    BaseInstrumentationService<CashInInstrumentationEvent>(instrumentationRepository),
    CashInInstrumentationService {
    override fun depositReceived(bankOperationExecuted: BankOperationExecuted) {
        try {
            val receiverDocument = accountRepository.findById(bankOperationExecuted.accountId).document
            publishEventAsync(CashInInstrumentationEvent.DepositReceived.create(bankOperationExecuted, receiverDocument))
        } catch (exception: Exception) {
            LOG.error(
                Markers.append("bankOperationExecuted", bankOperationExecuted),
                "CashInInstrumentationDepositReceived",
                exception,
            )
        }
    }

    override fun creditCardSucceeded(transaction: Transaction) {
        try {
            val event = CashInInstrumentationEvent.CreditCardCashInReceived.create(transaction = transaction)

            if (!event.succeeded) {
                throw Exception("Credit card transaction shoud have succeeded")
            }

            publishEventAsync(CashInInstrumentationEvent.CreditCardCashInReceived.create(transaction = transaction))
        } catch (exception: Exception) {
            LOG.error(
                Markers.append("transaction", transaction),
                "CashInInstrumentationCreditCardSucceeded",
                exception,
            )
        }
    }

    override fun creditCardFailed(transaction: Transaction) {
        try {
            val event = CashInInstrumentationEvent.CreditCardCashInReceived.create(transaction = transaction)

            if (event.succeeded) {
                throw Exception("Credit card transaction shoud have not succeeded")
            }

            publishEventAsync(event)
        } catch (exception: Exception) {
            LOG.error(
                Markers.append("transaction", transaction),
                "CashInInstrumentationCreditCardFailed",
                exception,
            )
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(DefaultCashInInstrumentationService::class.java)
    }
}