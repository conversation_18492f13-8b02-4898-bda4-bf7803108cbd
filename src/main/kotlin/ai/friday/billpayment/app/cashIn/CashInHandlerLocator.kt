package ai.friday.billpayment.app.cashIn

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.payment.Transaction
import jakarta.annotation.PostConstruct
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

interface CashInHandlerLocator {
    fun getHandler(transaction: Transaction): CashInHandler
}

@FridayMePoupe
class DefaultCashInHandlerLocator(
    private val handlers: List<CashInHandler>,
) : CashInHandlerLocator {

    private val logger = LoggerFactory.getLogger(DefaultCashInHandlerLocator::class.java)

    @PostConstruct
    fun setup() {
        logger.info(
            Markers.append(
                "handlers",
                handlers.associate { it.javaClass.simpleName to it.supportedTransactionType },
            ),
            "DefaultCashInHandlerLocator",
        )
    }

    override fun getHandler(transaction: Transaction): CashInHandler {
        return with(handlers.filter { it.supportedTransactionType == transaction.type }) {
            when (size) {
                0 -> throw IllegalStateException("no handler found for transaction type: ${transaction.type}")
                1 -> single()
                else -> throw IllegalStateException("more than one handler found for transaction type: ${transaction.type}")
            }
        }
    }
}