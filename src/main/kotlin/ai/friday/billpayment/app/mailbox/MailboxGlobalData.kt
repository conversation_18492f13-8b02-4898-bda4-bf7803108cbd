package ai.friday.billpayment.app.mailbox

import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.integrations.MailboxListsService
import ai.friday.billpayment.app.job.AbstractJob
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import io.micronaut.context.event.ApplicationEventListener
import io.micronaut.discovery.event.ServiceReadyEvent
import jakarta.inject.Singleton
import java.time.Duration
import org.slf4j.LoggerFactory

@Singleton
@Requires(beans = [MailboxGlobalData::class])
open class RefreshMailboxGlobalData(private val mailboxGlobalData: MailboxGlobalData) :
    AbstractJob(fixedDelay = Duration.ofHours(1), shouldLock = false) {
    override fun execute() {
        mailboxGlobalData.loadData()
    }
}

@Singleton
@Requirements(Requires(notEnv = ["test"]), Requires(env = [FRIDAY_ENV, ME_POUPE_ENV]))
class MailboxGlobalData(
    private val mailboxListsService: MailboxListsService,
) : ApplicationEventListener<ServiceReadyEvent> {
    private val logger = LoggerFactory.getLogger(MailboxGlobalData::class.java)

    override fun onApplicationEvent(event: ServiceReadyEvent?) {
        loadData()
    }

    fun loadData() {
        try {
            allowedList = mailboxListsService.findGlobal(MailboxListType.ALLOWED)
            blockList = mailboxListsService.findGlobal(MailboxListType.BLOCKED)
            doNotDisturbList = mailboxListsService.findGlobal(MailboxListType.DO_NOT_DISTURB)
            logger.info("MailboxGlobalData#loadData")
        } catch (ex: Exception) {
            logger.error("MailboxGlobalData#loadData", ex)
        }
    }

    companion object {
        var allowedList: List<String> = emptyList()
            private set
        var blockList: List<String> = emptyList()
            private set
        var doNotDisturbList: List<String> = emptyList()
            private set
    }
}