package ai.friday.billpayment.app.mailbox

import ai.friday.billpayment.app.integrations.MailboxGlobalDataRepository
import ai.friday.billpayment.app.integrations.MailboxListsService
import jakarta.inject.Singleton

@Singleton
class DefaultMailboxListsService(
    private val globalDataRepository: MailboxGlobalDataRepository,
) : MailboxListsService {

    override fun findGlobal(type: MailboxListType): List<String> {
        return loadData(type)
    }

    override fun putGlobal(type: MailboxListType, items: List<String>) {
        saveData(type, mergeAsSets(loadData(type), items))
    }

    override fun deleteGlobal(type: MailboxListType, email: String) {
        saveData(type, loadData(type).filter { it != email })
    }

    private fun loadData(type: MailboxListType): List<String> {
        return when (type) {
            MailboxListType.ALLOWED -> globalDataRepository.loadAllowedList()
            MailboxListType.BLOCKED -> globalDataRepository.loadBlockList()
            MailboxListType.DO_NOT_DISTURB -> globalDataRepository.loadDoNotDisturbList()
        }
    }

    private fun saveData(type: MailboxListType, elements: List<String>) {
        when (type) {
            MailboxListType.ALLOWED -> globalDataRepository.saveAllowedList(elements)
            MailboxListType.BLOCKED -> globalDataRepository.saveBlockList(elements)
            MailboxListType.DO_NOT_DISTURB -> globalDataRepository.saveDoNotDisturbList(elements)
        }
    }

    private fun mergeAsSets(list1: List<String>, list2: List<String>): List<String> {
        return (list1 + list2).toSet().toList()
    }
}

enum class MailboxListType {
    ALLOWED,
    BLOCKED,
    DO_NOT_DISTURB,
}