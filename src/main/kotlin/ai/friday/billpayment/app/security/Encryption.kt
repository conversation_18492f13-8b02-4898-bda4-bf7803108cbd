package ai.friday.billpayment.app.security

import ai.friday.billpayment.EncryptUtil
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton

@Singleton
class DefaultEncryptionService(@Property(name = "tenant.encryption.masterkey") private val masterKey: String) : EncryptionService {
    override fun encrypt(value: String, salt: String): String = EncryptUtil.encrypt(masterKey, value, salt)

    override fun decrypt(encryptedValue: String, salt: String) = EncryptUtil.decrypt(masterKey, encryptedValue, salt)
}

interface EncryptionService {
    fun encrypt(value: String, salt: String): String
    fun decrypt(encryptedValue: String, salt: String): String
}