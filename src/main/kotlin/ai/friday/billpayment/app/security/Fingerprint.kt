package ai.friday.billpayment.app.security

import ai.friday.billpayment.app.bill.BillEventDependency

@BillEventDependency
data class Fingerprint(
    val sessionId: String? = null,
    val app: FingerprintApp = FingerprintApp(),
    val geolocation: FingerprintGeolocation = FingerprintGeolocation(),
    val device: FingerprintDevice = FingerprintDevice(),
    val os: FingerprintOS = FingerprintOS(),
)

@BillEventDependency
data class FingerprintApp(
    val version: String? = null,
)

@BillEventDependency
data class FingerprintGeolocation(
    val latitude: String? = null,
    val longitude: String? = null,
)

@BillEventDependency
data class FingerprintDevice(
    val manufacturer: String? = null,
    val model: String? = null,
    val installationId: String? = null,
)

@BillEventDependency
data class FingerprintOS(
    val name: String? = null,
    val version: String? = null,
)