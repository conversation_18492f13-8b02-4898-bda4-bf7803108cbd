package ai.friday.billpayment.app

import ai.friday.billpayment.app.bill.BRAZIL_LOCALE
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.micronaut.scheduling.cron.CronExpression
import io.opentelemetry.api.baggage.Baggage
import io.opentelemetry.context.Context
import java.text.Normalizer
import java.text.NumberFormat
import java.time.Duration
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.slf4j.MDCContext
import org.slf4j.MDC

fun Boolean?.getOrFalse() = this ?: false

fun formatBrazilCurrency(long: Long): String {
    val nf = NumberFormat.getCurrencyInstance(BRAZIL_LOCALE)
    return nf.format(long.toFloat() / 100f)
}

fun LocalDate?.formatOrEmpty(formatter: DateTimeFormatter): String {
    return this?.let { it.format(formatter) } ?: ""
}

fun String.stripAccents() =
    Normalizer.normalize(this, Normalizer.Form.NFD).replace("[\\p{InCombiningDiacriticalMarks}]".toRegex(), "")

fun String.stripSpacesAndPunctuation() = this.replace(Regex("[\\p{Punct}\\s]"), "")

fun firstNotNullAndNotEmpty(vararg elements: String?): String? {
    return elements.firstOrNull {
        !it.isNullOrEmpty()
    }
}

fun String.stripEmojis() = this.replace("[^\\p{L}\\p{N}\\p{P}\\p{Z}]".toRegex(), "")

inline fun <T> List<T>.dropFirst(predicate: (current: T) -> Boolean): List<T> {
    val index = indexOfFirst { predicate(it) }
    if (index >= 0) {
        return take(index) + drop(index + 1)
    }
    return this
}

fun uuid(): String = UUID.randomUUID().toString()

fun addBaggage(vararg items: Pair<String, String?>) =
    Baggage.current().toBuilder().apply {
        items.iterator().forEach { item ->
            item.second?.let { value -> put(item.first, value) }
        }
    }.build().storeInContext(Context.current()).makeCurrent()

inline fun CronExpression?.calculateDurationUntilNextExecution(
    tolerance: Long,
): Duration? {
    this?.let { cronExpression ->
        val now = getZonedDateTime()
        val nextExecution = cronExpression.nextTimeAfter(now)

        val duration = Duration.between(now, nextExecution)
        if (duration.toSeconds() > tolerance) {
            return duration
        }
    }
    return null
}

private val asyncScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

object AsyncUtils {
    fun callAsync(block: suspend CoroutineScope.() -> Unit): Job {
        val mdc = MDC.getCopyOfContextMap() ?: emptyMap()
        return asyncScope.launch(MDCContext(mdc)) {
            block()
        }
    }
}

fun LocalDate.isBetween(start: LocalDate, end: LocalDate): Boolean = this in start..end