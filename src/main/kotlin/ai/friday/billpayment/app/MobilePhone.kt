package ai.friday.billpayment.app

data class MobilePhone(val msisdn: String) {
    override fun toString() = msisdn

    companion object {
        fun createOrNull(msisdn: String): MobilePhone? = MobilePhone(msisdn).fixOrNull()
    }
}

fun MobilePhone.isValid(): Boolean {
    return msisdn.matches(Regex("^\\+55\\d{11}$"))
}

fun MobilePhone.fix(): MobilePhone {
    val digits = msisdn.filter { it.isDigit() }
    return if (digits.length <= 11) {
        this.copy(msisdn = "+55$digits")
    } else if (!digits.startsWith("+")) {
        this.copy(msisdn = "+$digits")
    } else {
        this
    }
}

fun MobilePhone.fixOrNull(): MobilePhone? {
    return if (isValid()) {
        this
    } else {
        val maybeFixed = fix()

        if (maybeFixed.isValid()) {
            maybeFixed
        } else {
            null
        }
    }
}