package ai.friday.billpayment.app.email

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import io.via1.communicationcentre.app.email.IncomingEmail
import io.via1.communicationcentre.app.email.IncomingEmailBuilder
import io.via1.communicationcentre.app.receipt.Action
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

private const val PROCESSED_PATH = "processed"
private const val UNPROCESSED_PATH = "unprocessed"
private const val QUARANTINE_PATH = "quarantine"

@FridayMePoupe
class MailObjectService(
    private val objectRepository: ObjectRepository,
    private val incomingEmailBuilder: IncomingEmailBuilder,
) {

    fun retrieveEmailFromS3(action: Action): IncomingEmailWithMetadata {
        return retrieveEmailFromS3(action, action.objectKeyPath)
    }

    fun retrieveEmailFromS3(action: Action, objectKeyPath: String): IncomingEmailWithMetadata {
        val objectWithMetadata = objectRepository.loadObjectWithMetadata(action.bucketName, objectKeyPath)
        val incomingEmail = objectWithMetadata.data.use {
            incomingEmailBuilder.build(it)
        }

        return IncomingEmailWithMetadata(
            incomingEmail = incomingEmail,
            contentLength = objectWithMetadata.contentLength,
        )
    }

    fun moveTo(action: Action, accountId: AccountId, destinationPath: String) {
        try {
            objectRepository.moveObject(action.bucketName, action.objectKeyPath, action.bucketName, destinationPath)
        } catch (e: Exception) {
            logger.error(
                Markers.append("method", "moveTo")
                    .andAppend("accountId", accountId.value)
                    .andAppend("action", action)
                    .andAppend("destinationPath", destinationPath),
                "MailObjectService",
                e,
            )
        }
    }

    fun buildProcessedPath(action: Action, accountId: AccountId, addBillResultStatus: String): String {
        return buildNewObjectKey(
            accountId = accountId,
            destinationFolder = "$PROCESSED_PATH/$addBillResultStatus",
            fileName = action.objectKeyPath,
        )
    }

    fun moveToQuarantine(action: Action, accountId: AccountId): String {
        val destinationPath = buildNewObjectKey(
            accountId = accountId,
            destinationFolder = QUARANTINE_PATH,
            fileName = action.objectKeyPath,
        )
        try {
            objectRepository.moveObject(action.bucketName, action.objectKeyPath, action.bucketName, destinationPath)
        } catch (e: Exception) {
            logger.error(
                Markers.append("method", "moveToQuarantine")
                    .andAppend("accountId", accountId.value)
                    .andAppend("action", action)
                    .andAppend("destinationPath", destinationPath),
                "MailObjectService",
                e,
            )
        }
        return destinationPath
    }

    fun moveToUnprocessed(action: Action, accountId: AccountId, reason: String): String {
        val destinationFolder = "$UNPROCESSED_PATH/$reason"
        val destinationPath = buildNewObjectKey(
            accountId = accountId,
            destinationFolder = destinationFolder,
            fileName = action.objectKeyPath,
        )
        try {
            objectRepository.moveObject(action.bucketName, action.objectKeyPath, action.bucketName, destinationPath)
        } catch (e: Exception) {
            logger.error(
                Markers.append("method", "moveToUnprocessed")
                    .andAppend("reason", reason)
                    .andAppend("accountId", accountId.value)
                    .andAppend("action", action)
                    .andAppend("destinationPath", destinationPath),
                "MailObjectService",
                e,
            )
        }
        return destinationPath
    }

    private fun buildNewObjectKey(accountId: AccountId, destinationFolder: String, fileName: String): String {
        val datePath = getLocalDate().format(dateFormat)
        val accountPathWithSlash = "${accountId.value}/"
        return "$destinationFolder/$accountPathWithSlash$datePath/$fileName"
    }
}

data class IncomingEmailWithMetadata(
    val incomingEmail: IncomingEmail,
    val contentLength: Long,
)

private val logger = LoggerFactory.getLogger(MailObjectService::class.java)