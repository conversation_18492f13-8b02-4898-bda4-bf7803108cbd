package ai.friday.billpayment.app.email

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.integrations.EmailService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.ParserService
import ai.friday.billpayment.app.mailbox.MailboxGlobalData
import ai.friday.billpayment.app.notification.EmailNotProcessedNotificationRequest
import ai.friday.billpayment.app.notification.NotificationRouterService
import ai.friday.billpayment.app.stripSpacesAndPunctuation
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.WalletStatus
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import io.micronaut.tracing.annotation.NewSpan
import io.via1.communicationcentre.app.email.IncomingEmail
import io.via1.communicationcentre.app.integrations.EmailSenderService
import io.via1.communicationcentre.app.parser.ParsedBill
import io.via1.communicationcentre.app.receipt.Receipt
import io.via1.communicationcentre.app.receipt.SQSEvent
import io.via1.communicationcentre.app.receipt.Status
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

data class AddBoletoFromEmailMessage(
    val accountId: AccountId,
    val walletId: WalletId,
    val barCode: BarCode,
    val dueDate: LocalDate?,
    val from: String,
    val subject: String,
    val receipt: Receipt,
    val emailDestinationPath: String,
)

const val SUCCESS_PATH = "success"
const val FAILURE_PATH = "failure"
const val LOOP_DETECTED_PATH = "loop_detected"
const val ACCOUNT_CLOSED_PATH = "account_closed"
const val DOCUMENT_NOT_FOUND = "document_not_found"
const val NO_BILLS_FOUND_PATH = "no_bills_found"

@FridayMePoupe
open class DefaultEmailService(
    private val billParseService: ParserService,
    private val accountService: AccountService,
    private val emailSenderService: EmailSenderService,
    private val walletService: WalletService,
    private val mailObjectService: MailObjectService,
    private val notificationRouterService: NotificationRouterService,
    @Property(name = "integrations.manual-workflow.emails") private val manualWorkflowEmail: String,
    @Property(name = "features.forwardEmailToManualWorkflow") private val forwardEmailToManualWorkflow: Boolean,
    @Property(name = "communication-centre.forward.sender") private val forwardEmailAddress: String,
    @Property(name = "sqs.addBillFromEmailQueueName") private val addBillFromEmailQueueName: String,
    private val messagePublisher: MessagePublisher,
) : EmailService {

    private val logger = LoggerFactory.getLogger(DefaultEmailService::class.java)

    @NewSpan("process")
    override fun process(event: SQSEvent) {
        val logName = "IncomingEmailProcessor"
        val markers = event.toMarkers()

        try {
            val incomingEmail = with(event.retrieveEmailFromS3()) {
                markers.andAppend("incomingEmail.totalSize", contentLength)
                this.incomingEmail
            }
            markers.andAppend(incomingEmail)

            val account = incomingEmail.toAccount()
            markers.andAppend(account)

            if (event.hasVirus()) {
                markers.andAppend("result", "email movido para quarentena")
                logger.warn(markers, logName)
                mailObjectService.moveToQuarantine(event.receipt.action, account.accountId)
                return
            }

            if (incomingEmail.fromOurselves()) {
                markers.andAppend("result", "loop detected")
                logger.info(markers, logName)
                mailObjectService.moveToUnprocessed(event.receipt.action, account.accountId, LOOP_DETECTED_PATH)
                return
            }

            if (incomingEmail.shouldForwardToOwner(account)) {
                tryForwardMessage(incomingEmail, account.emailAddress, markers, "$logName#forwardToOwner")
            }

            if (account.closed) {
                markers.andAppend("result", "ignored: account closed")
                logger.info(markers, logName)
                mailObjectService.moveToUnprocessed(event.receipt.action, account.accountId, ACCOUNT_CLOSED_PATH)
                return
            }

            return tryToAddBillFromMailBox(incomingEmail, account, event.receipt, markers, logName)
        } catch (e: AccountNotFoundException) {
            mailObjectService.moveToUnprocessed(event.receipt.action, AccountId("ACCOUNT-DOCUMENT_NOT_FOUND"), DOCUMENT_NOT_FOUND)
            logger.error(markers, logName, e)
            return
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            throw e
        }
    }

    private fun SQSEvent.toMarkers() = mapOf(
        "mail" to mail,
        "S3Action" to receipt.action,
        "veredictStatuses" to mapOf(
            "spam" to receipt.spamVerdict.status,
            "virus" to receipt.virusVerdict.status,
            "spf" to receipt.spfVerdict.status,
            "dmarc" to receipt.dmarcVerdict.status,
            "dkim" to receipt.dkimVerdict.status,
        ),
    ).let {
        append("event", it)
    }

    private fun SQSEvent.retrieveEmailFromS3() = mailObjectService.retrieveEmailFromS3(receipt.action)

    private fun LogstashMarker.andAppend(incomingEmail: IncomingEmail) = mapOf(
        "sender" to incomingEmail.sender.address,
        "subject" to incomingEmail.subject,
        "allAttachmentsSize" to incomingEmail.attachments.size,
        "allAttachmentsNames" to incomingEmail.attachments.map { it.fileName },
        "pdfAttachmentsSize" to incomingEmail.attachments.filter { it.isPDF }.size,
    ).let {
        andAppend("incomingEmail", it)
    }

    private fun IncomingEmail.toAccount() = accountService.findLastAccountByDocument(extractDocument())

    private fun LogstashMarker.andAppend(account: Account) = andAppend("accountId", account.accountId.value)
        .andAppend("closedAccount", account.closed)

    private fun SQSEvent.hasVirus() = receipt.virusVerdict.status == Status.FAIL

    private fun IncomingEmail.fromOurselves() = sender.address == forwardEmailAddress

    private fun IncomingEmail.shouldForwardToOwner(account: Account) = if (EmailAddress(sender.address) == account.emailAddress) {
        false
    } else {
        if (!account.closed) {
            true
        } else {
            account.lastUpdate().isAfter(oneMonthAgo())
        }
    }

    private fun Account.lastUpdate(): ZonedDateTime {
        val lastUpdate = walletService.findWallets(accountId, MemberStatus.REMOVED)
            .firstOrNull { it.checkPrimary(accountId, WalletStatus.CLOSED) }
            ?.founder
            ?.updated
        return lastUpdate ?: this.created
    }

    private fun oneMonthAgo() = getZonedDateTime().minusDays(30)

    private fun tryToAddBillFromMailBox(
        incomingEmail: IncomingEmail,
        account: Account,
        receipt: Receipt,
        markers: LogstashMarker,
        logName: String,
    ) {
        try {
            val parsedBills = billParseService.parseBillFrom(incomingEmail)
            markers.andAppend("parsedBills", parsedBills)
                .andAppend("parsedBillsSize", parsedBills.size)

            if (parsedBills.isEmpty()) {
                markers.andAppend("result", "no bill found")

                val emailDestinationPath = handleBillNotParsed(incomingEmail, receipt, account, NO_BILLS_FOUND_PATH, markers, logName)
                markers.andAppend("emailDestinationPath", emailDestinationPath)
            } else {
                markers.andAppend("result", "bill added (${parsedBills.size})")

                val processPath = mailObjectService.buildProcessedPath(receipt.action, account.accountId, SUCCESS_PATH)
                markers.andAppend("emailDestinationPath", processPath)

                parsedBills.map { parsedBill ->
                    buildAddBoletoFromEmailMessage(account, parsedBill, incomingEmail, receipt, processPath)
                }.forEach { message ->
                    messagePublisher.sendMessage(addBillFromEmailQueueName, message)
                }

                mailObjectService.moveTo(receipt.action, account.accountId, processPath)
            }

            logger.info(markers, logName)
        } catch (e: Exception) {
            val emailDestinationPath = handleBillNotParsed(incomingEmail, receipt, account, FAILURE_PATH, markers, logName)
            markers.andAppend("emailDestinationPath", emailDestinationPath)
            logger.error(markers, logName, e)
        }
    }

    private fun buildAddBoletoFromEmailMessage(
        account: Account,
        parsedBill: ParsedBill,
        incomingEmail: IncomingEmail,
        receipt: Receipt,
        emailDestinationPath: String,
    ) = AddBoletoFromEmailMessage(
        walletId = account.defaultWalletId(),
        accountId = account.accountId,
        barCode = BarCode.ofDigitable(parsedBill.digitableLine),
        dueDate = getDueDate(parsedBill.dueDate),
        from = incomingEmail.sender.address,
        subject = incomingEmail.subject,
        receipt = receipt,
        emailDestinationPath = emailDestinationPath,
    )

    private fun tryForwardMessage(incomingEmail: IncomingEmail, emailAddress: EmailAddress, markers: LogstashMarker, logName: String) {
        try {
            emailSenderService.forward(incomingEmail, emailAddress.toString())
            logger.info(markers, logName)
        } catch (e: Exception) {
            logger.warn(markers, logName, e)
        }
    }

    private fun handleBillNotParsed(
        incomingEmail: IncomingEmail,
        receipt: Receipt,
        account: Account,
        reason: String,
        markers: LogstashMarker,
        logName: String,
    ): String {
        val emailDestinationPath = mailObjectService.moveToUnprocessed(receipt.action, account.accountId, reason)
        if (!MailboxGlobalData.doNotDisturbList.contains(incomingEmail.sender.address)) {
            sendToManualWorkflow(incomingEmail, receipt, markers, logName)
            notifyEmailNotProcessed(account, incomingEmail)
        }
        return emailDestinationPath
    }

    private fun notifyEmailNotProcessed(account: Account, incomingEmail: IncomingEmail) {
        val wallet = walletService.findWallet(account.configuration.defaultWalletId!!)
        notificationRouterService.routeNotification(
            EmailNotProcessedNotificationRequest(
                members = wallet.activeMembers,
                walletName = wallet.name,
                sender = EmailAddress(incomingEmail.sender.address),
                subject = incomingEmail.subject,
                dateTime = getZonedDateTime(),
            ),
        )
    }

    private fun sendToManualWorkflow(incomingEmail: IncomingEmail, receipt: Receipt, markers: LogstashMarker, logName: String) {
        if (forwardEmailToManualWorkflow) {
            val document = incomingEmail.recipient.substringBefore("@")
            incomingEmail.subject = "$document : ${incomingEmail.subject}"
            tryForwardMessage(incomingEmail, EmailAddress(manualWorkflowEmail), markers, "$logName#forwardToManualWorkflow")
        }
    }

    private fun getDueDate(dueDate: String?): LocalDate? {
        return if (!dueDate.isNullOrEmpty()) {
            LocalDate.parse(dueDate, DateTimeFormatter.ofPattern("dd/MM/yyyy"))
        } else {
            null
        }
    }
}

fun IncomingEmail.extractDocument() = EmailAddress(this.recipient).recipient.stripSpacesAndPunctuation()