package ai.friday.billpayment.app.eventbus

import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.feature.RequiresEventBus
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.metrics.AbstractSummary
import ai.friday.billpayment.app.metrics.push
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithExternalPayment
import ai.friday.billpayment.app.payment.SinglePaymentMethodsDetail
import ai.friday.billpayment.app.payment.accountPaymentMethod
import ai.friday.billpayment.app.payment.authentication
import ai.friday.billpayment.app.payment.paymentDetails
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.zonedDateTimeFromMillis
import jakarta.inject.Singleton
import java.time.ZonedDateTime

@Singleton
@RequiresEventBus
class EventBusBillPaidProcessor(
    private val accountRepository: AccountRepository,
    private val billRepository: BillRepository,
    private val transactionRepository: TransactionRepository,
    private val walletRepository: WalletRepository,
    private val eventBusPublisher: EventBus.Publisher,
) : EventBus.MessageProcessor {

    override fun accepts(event: BillEvent): Boolean = event is BillPaid

    override fun process(event: BillEvent): Result<EventBus.MessageId> = runCatching {
        if (event !is BillPaid) return Result.failure(IllegalStateException("Unsupported event type"))

        if (event.transactionId == null) {
            return Result.failure(IllegalStateException("TransactionId cannot be null"))
        }

        val wallet = walletRepository.findWallet(event.walletId)
        val account = accountRepository.findById(wallet.founder.accountId)

        val bill = billRepository.findBill(billId = event.billId, walletId = event.walletId)

        val transaction = transactionRepository.findById(event.transactionId)
        val paymentDetails = transaction.paymentDetails()
        val paymentMethod = transaction.accountPaymentMethod()

        val user = EventBusMessage.User(
            accountId = account.accountId.value,
            document = account.document,
            walletId = wallet.id.value,
            partnerId = account.externalId()?.value,
        )

        val notificationBill = EventBusMessage.Bill(
            amount = bill.amount,
            amountTotal = bill.amountTotal,
            billType = bill.billType,
            barcode = bill.barCode?.number.orEmpty(),
            digitable = bill.barCode?.digitable.orEmpty(),
            issuer = bill.assignor.orEmpty(),
            dueDate = bill.dueDate,
            effectiveDueDate = bill.effectiveDueDate,
            recipientDocument = bill.recipient?.document.orEmpty(),
            recipientName = bill.recipient?.name.orEmpty(),
            discount = bill.discount,
            fine = bill.fine,
            interest = bill.interest,
        )

        val notificationTransaction = EventBusMessage.Transaction(
            authenticationCode = transaction.authentication().orEmpty(),
            billId = bill.billId.value,
            payer = user,
            transactionId = event.transactionId.value,
        )

        val amountPaid = bill.amountPaid ?: 0
        val paidAt = zonedDateTimeFromMillis(event.created)

        val notificationPayment = createNotificationPayment(
            amountPaid = amountPaid,
            paidAt = paidAt,
            paymentMethod = paymentMethod,
            paymentDetails = paymentDetails,
        )

        BillPaidSummaryMetric.push(
            mapOf(
                "payment_method" to paymentMethod.method.type.name,
                "bill_type" to bill.billType.name,
            ),
            amountPaid,
        )

        eventBusPublisher.publish(
            EventBusMessage.PaymentEvent(
                bill = notificationBill,
                payments = listOf(notificationPayment),
                transaction = notificationTransaction,
            ),
        ).getOrThrow()
    }

    private fun createNotificationPayment(
        amountPaid: Long,
        paidAt: ZonedDateTime,
        paymentMethod: AccountPaymentMethod,
        paymentDetails: SinglePaymentMethodsDetail,
    ): EventBusMessage.Payment {
        return when (paymentDetails) {
            is PaymentMethodsDetailWithBalance -> {
                val bankAccount = paymentMethod.method as? InternalBankAccount
                    ?: throw IllegalStateException("PaymentMethod is not a CreditCard")

                EventBusMessage.Payment(
                    amountPaid = amountPaid,
                    paidAt = paidAt,
                    type = PaymentMethodType.BALANCE,
                    bankAccount = EventBusMessage.BankAccount(
                        accountNumber = bankAccount.accountNumber.number.toString(),
                        accountDv = bankAccount.accountDv,
                        routingNumber = bankAccount.routingNo.toString(),
                        bankNumber = bankAccount.bankNo.toString(),
                    ),
                )
            }

            is PaymentMethodsDetailWithCreditCard -> {
                val creditCard = paymentMethod.method as? CreditCard
                    ?: throw IllegalStateException("PaymentMethod is not a CreditCard")

                EventBusMessage.Payment(
                    amountPaid = amountPaid,
                    paidAt = paidAt,
                    type = PaymentMethodType.CREDIT_CARD,
                    installments = EventBusMessage.Installment(
                        count = paymentDetails.installments,
                        value = paymentDetails.totalAmount,
                        fee = paymentDetails.feeAmount,
                    ),
                    card = EventBusMessage.Card(
                        lastFourDigits = creditCard.lastFourDigits,
                        bin = creditCard.bin,
                        brand = creditCard.brand.mainName,
                    ),
                )
            }

            is PaymentMethodsDetailWithExternalPayment,
            -> throw IllegalStateException("PaymentDetails not supported")
        }
    }
}

object BillPaidSummaryMetric : AbstractSummary()