package ai.friday.billpayment.app.eventbus

import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.eventbus.EventBus.Message
import java.time.LocalDate
import java.time.ZonedDateTime

object EventBus {
    /**
     * This interface defines the contract for publishing messages to the event bus.
     *
     * The event bus is designed to facilitate the exchange of semantic events between services,
     * partners, and system layers.
     *
     * Since partners are expected to consume these events, messages should be as generic,
     * complete, and self-explanatory as possible.
     */

    interface Publisher {
        fun publish(message: Message): Result<MessageId>
    }

    interface MessageProcessor {
        fun process(event: BillEvent): Result<MessageId>

        fun accepts(event: BillEvent): Boolean

        fun priority(): Int = 1000
    }

    data class MessageId(val value: String)

    sealed interface Message {
        val eventType: EventType
    }

    enum class EventType { PAYMENT }
}

object EventBusMessage {
    data class PaymentEvent(
        val bill: Bill,
        val payments: List<Payment>,
        val transaction: Transaction,
    ) : Message {
        override val eventType = EventBus.EventType.PAYMENT
    }

    data class Bill(
        val amount: Long,
        val amountTotal: Long,
        val billType: BillType,
        val barcode: String,
        val digitable: String,
        val issuer: String,
        val dueDate: LocalDate,
        val effectiveDueDate: LocalDate,
        val recipientDocument: String,
        val recipientName: String,

        // optionals
        val abatement: Long = 0,
        val discount: Long = 0,
        val fine: Long = 0,
        val interest: Long = 0,
    )

    data class Transaction(
        val authenticationCode: String,
        val billId: String,
        val payer: User,
        val transactionId: String,
    )

    data class User(
        val accountId: String,
        val document: String,
        val walletId: String,
        val partnerId: String?,
    )

    data class BankAccount(
        val accountNumber: String,
        val accountDv: String,
        val routingNumber: String,
        val bankNumber: String,
    )

    data class Card(
        val lastFourDigits: String,
        val bin: String,
        val brand: String,
    )

    data class Installment(
        val count: Int,
        val value: Long,
        val fee: Long = 0,
    )

    data class Payment(
        val amountPaid: Long,
        val paidAt: ZonedDateTime,
        val type: PaymentMethodType,
        val bankAccount: BankAccount? = null,
        val installments: Installment? = null,
        val card: Card? = null,
    )
}