package ai.friday.billpayment.app.eventbus

import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.feature.RequiresEventBus
import jakarta.inject.Singleton

/*
 * This class is responsible for processing the events that are published in the event bus.
 * It delegates the processing to the specific message processor for the event.
 *
 * So it's expected that each event type must have a specific message processor.
 *
 * @param messageProcessors: List of message processors for the events.
 */
@Singleton
@RequiresEventBus
class EventBusMessageProcessor(messageProcessors: List<EventBus.MessageProcessor>) {
    private val processors = messageProcessors.sortedBy { it.priority() }

    fun processBillEvent(event: BillEvent): Result<PublishResponse> {
        val processor = processors.firstOrNull { it.accepts(event) }
            ?: return Result.success(PublishResponse.Skipped)

        val messageId = processor.process(event).getOrElse { return Result.failure(it) }

        return Result.success(PublishResponse.Published(messageId.value))
    }

    sealed interface PublishResponse {
        data object Skipped : PublishResponse
        data class Published(val messageId: String) : PublishResponse
    }
}