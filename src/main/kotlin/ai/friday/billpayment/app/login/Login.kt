package ai.friday.billpayment.app.login

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import java.time.ZonedDateTime

data class LoginResult(val login: <PERSON>gin, val created: <PERSON><PERSON><PERSON>) {
    fun shouldCreateAccountRegister() = created && login.role == Role.GUEST
}

data class Login(val accountId: AccountId, val emailAddress: EmailAddress, val role: Role)

enum class ProviderName { COGNITO, WHATSAPP, MODATTA, MSISDN, ME_POUPE, MOTOROLA }

data class ProviderUser(
    val id: String,
    val providerName: ProviderName,
    val username: String,
    val emailAddress: EmailAddress,
    val created: ZonedDateTime? = null,
)

const val ME_POUPE_USERNAME_PREFIX = "ME_POUPE"
const val WHATSAPP_USERNAME_PREFIX = "WhatsApp"
const val MODATTA_USERNAME_PREFIX = "Modatta"
const val MSISDN_USERNAME_PREFIX = "MSISDN"

const val WHATSAPP_DOMAIN = "wa.gw.msging.net"

fun ProviderUser.buildFederatedUsername(): String? {
    return when (providerName) {
        ProviderName.COGNITO -> null
        ProviderName.WHATSAPP -> "${WHATSAPP_USERNAME_PREFIX}_$id"
        ProviderName.MODATTA -> "${MODATTA_USERNAME_PREFIX}_$id"
        ProviderName.MSISDN -> "${MSISDN_USERNAME_PREFIX}_$id"
        ProviderName.ME_POUPE -> "${ME_POUPE_USERNAME_PREFIX}_$id"
        ProviderName.MOTOROLA -> "MOTOROLA_$id"
    }
}