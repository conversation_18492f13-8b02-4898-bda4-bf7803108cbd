package ai.friday.billpayment.app.account

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.adapters.lock.updateAccountStatusLockProvider
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.chatbot.ChatbotNotificationService
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.dda.optedOut
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AlreadyLockedException
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.DocumentValidationService
import ai.friday.billpayment.app.integrations.ExternalAccountRegister
import ai.friday.billpayment.app.integrations.FraudListConfiguration
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.LoginRepository
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.ModattaProvider
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.RegisterInstrumentationService
import ai.friday.billpayment.app.integrations.TokenType
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.integrations.WelcomeMessageType
import ai.friday.billpayment.app.journey.UserJourneyService
import ai.friday.billpayment.app.liveness.LivenessEnrollmentVerification
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.liveness.LivenessSelfieError
import ai.friday.billpayment.app.liveness.LivenessService
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.app.modatta.register.SimpleSignUpApprover
import ai.friday.billpayment.app.msisdnauth.TEMPORARY_NICKNAME
import ai.friday.billpayment.app.msisdnauth.createWhatsappEmail
import ai.friday.billpayment.app.onboarding.OnboardingTestPixService
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.RegisterPixKeyCommand
import ai.friday.billpayment.app.register.RiskAnalysisFailedReason
import ai.friday.billpayment.app.register.SimpleSignUpPendingMessage
import ai.friday.billpayment.app.register.instrumentation.CrmContactNotFoundException
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.DailyPaymentLimitType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.measure
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.flatMap
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Property
import io.micronaut.http.MediaType
import io.via1.communicationcentre.app.SimpleContractForm
import io.via1.communicationcentre.app.email.Attachment
import io.via1.communicationcentre.app.integrations.EmailSenderService
import io.via1.communicationcentre.app.integrations.Tenant
import jakarta.inject.Named
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.InputStream
import java.time.LocalDate
import java.time.ZonedDateTime
import kotlin.contracts.ExperimentalContracts
import kotlin.contracts.contract
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.cognitoidentityprovider.model.UserNotFoundException

@FridayMePoupe
open class BasicRegisterService(
    private val tokenService: TokenService,
    private val accountRegisterRepository: AccountRegisterRepository,
    private val accountService: AccountService,
    private val accountRegisterService: AccountRegisterService,
    private val userFilesConfiguration: UserFilesConfiguration,
    private val userAddressConfiguration: UserAddressConfiguration,
    private val agreementFilesService: AgreementFilesService,
    private val notificationSenderService: EmailSenderService,
    private val loginRepository: LoginRepository,
    private val userPoolAdapter: UserPoolAdapter,
    private val externalAccountRegister: ExternalAccountRegister,
    private val walletService: WalletService,
    private val walletLimitsService: WalletLimitsService,
    private val crmService: CrmService,
    private val registerInstrumentationService: RegisterInstrumentationService,
    private val ddaService: DDAService,
    private val userJourneyService: UserJourneyService,
    private val closeAccountService: CloseAccountService,
    private val systemActivityService: SystemActivityService,
    private val livenessService: LivenessService,
    private val modattaProvider: ModattaProvider,
    private val chatbotNotificationService: ChatbotNotificationService,
    @Named("pending-internal-review")
    private val pendingInternalReviewConfiguration: NewAccountEmailConfiguration,
    @Named("pending-activation")
    private val pendingActivationConfiguration: NewAccountEmailConfiguration,
    @Named(updateAccountStatusLockProvider) private val accountStatusLockProvider: InternalLock,
    private val messagePublisher: MessagePublisher,
    private val documentValidationService: DocumentValidationService,
    private val fraudList: FraudListConfiguration,
    private val notificationAdapter: NotificationAdapter,
    private val adService: AdService,
    private val walletBillCategoryService: PFMWalletCategoryService,
) {
    @field:Property(name = "email.notification.email")
    lateinit var from: String

    @field:Property(name = "accountRegister.pixKey.emailDomain")
    lateinit var pixKeyEmailDomain: String

    @field:Property(name = "sqs.simpleSignUpQueueName")
    lateinit var simpleSignUpQueueName: String

    @field:Property(name = "sqs.registerPixKeyQueueName")
    lateinit var registerPixKeyQueueName: String

    open fun setDeviceInfo(
        adIds: DeviceAdIds,
        accountId: AccountId,
    ): Either<Exception, Unit> {
        systemActivityService.setDeviceInfo(adIds = adIds, accountId = accountId)

        return Either.Right(Unit)
    }

    open fun issueToken(
        email: EmailAddress,
        accountId: AccountId,
        clientIP: String,
    ): Either<Exception, IssuedToken> {
        val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)

        val accountRegisterUpdated =
            accountRegisterData.copy(
                emailAddress = email,
                emailVerified = false,
                clientIP = clientIP,
            )
        accountRegisterRepository.save(accountRegisterUpdated)

        if (accountRegisterService.checkIsTestAccount(accountId)) {
            accountService.addGroupsToPartialAccount(accountId, listOf(AccountGroup.ALPHA))
        }

        updateUserOnCrm(accountRegisterUpdated)

        return tokenService.issueToken(email, accountId)
    }

    open fun updateMsisdnEmail(tokenKey: TokenKey): Either<Exception, BasicAccountRegisterData> {
        if (checkUserBlockedForEdition(tokenKey.accountId)) {
            return AccountIsBlockedForEdition().left()
        }
        return tokenService.validateToken(tokenKey, TokenType.EMAIL).flatMap {
            val emailAddress = EmailAddress(it.value)
            val accountRegisterUpdated =
                accountRegisterRepository.findByAccountId(tokenKey.accountId).copy(
                    emailAddress = emailAddress,
                    emailVerified = true,
                )
            updateUserOnCrm(accountRegisterUpdated)
            accountService.updatePartialAccountEmail(
                accountId = tokenKey.accountId,
                newEmailAddress = emailAddress,
            )
            accountRegisterRepository.save(accountRegisterUpdated).toBasicAccountRegisterData().right()
        }
    }

    open fun addDocumentNumber(
        accountId: AccountId,
        document: Document,
    ): Either<Exception, BasicAccountRegisterData> {
        try {
            val existingAccount = accountRegisterRepository.findByDocument(document.value)
            if (existingAccount.accountId != accountId) {
                return DocumentAlreadyRegistered().left()
            }
            return existingAccount.toBasicAccountRegisterData().right()
        } catch (e: ItemNotFoundException) {
            val accountRegisterData =
                accountRegisterRepository.findByAccountId(accountId)
                    .copy(document = document, fraudListMatch = document.value in fraudList.documents)
                    .let(accountRegisterRepository::save)
            return accountRegisterData.toBasicAccountRegisterData().right()
        } catch (e: Exception) {
            return e.left()
        }
    }

    fun validateIdentity(accountId: AccountId): Either<ValidateIdentityError, AccountRegisterData> {
        val logName = "validateIdentity"
        val markers = append("accountId", accountId.value)
        val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)

        if (accountRegisterData.identityAlreadyValidated()) {
            return accountRegisterData.right()
        }

        if (!canValidateIdentity(
                document = accountRegisterData.document,
                uploadedSelfie = accountRegisterData.uploadedSelfie,
            )
        ) {
            return ValidateIdentityError.UnableToValidate.left()
        }

        val uploadedSelfie =
            accountRegisterRepository.getDocumentInputStream(accountRegisterData.uploadedSelfie)

        val (result, elapsedTime) = measure {
            documentValidationService.validateWithSelfie(
                document = accountRegisterData.document,
                name = accountRegisterData.getName(),
                selfieImageStream = uploadedSelfie,
            )
        }
        markers.andAppend("elapsedTime", elapsedTime)

        val documentValidationResponse =
            result.getOrElse {
                return ValidateIdentityError.InvalidResponse(it).left()
            }

        val matchResult =
            when (documentValidationResponse.selfieMatchesDocument()) {
                true -> IdentityValidationStatus.CHECKED
                false -> IdentityValidationStatus.REJECTED
                null -> IdentityValidationStatus.UNKNOWN
            }

        markers.andAppend("matchResult", matchResult.name)

        val updatedAccountRegisterData =
            accountRegisterData.copy(
                identityValidationStatus = matchResult,
                identityValidationPercentage = documentValidationResponse.getPercentage(),
            )
        accountRegisterRepository.save(updatedAccountRegisterData)

        LOG.info(markers, logName)

        return matchResult.toRiskAnalysisFailedReason()?.let { reason ->
            upgradeRegister(updatedAccountRegisterData, reason, shouldNotify = false).right()
        } ?: updatedAccountRegisterData.right()
    }

    open fun upgradeRegister(
        accountRegisterData: AccountRegisterData,
        reason: List<RiskAnalysisFailedReason>,
        shouldNotify: Boolean,
    ): AccountRegisterData {
        val updatedAccountRegisterData =
            accountRegisterData.copy(
                registrationType = RegistrationType.UPGRADED,
                riskAnalysisFailedReasons = reason,
                agreementData = null,
            )
        accountRegisterRepository.save(updatedAccountRegisterData)

        accountService.updatePartialAccountRegistrationType(accountRegisterData.accountId, RegistrationType.UPGRADED)
        accountService.updatePartialAccountStatus(accountRegisterData.accountId, AccountStatus.REGISTER_INCOMPLETE)

        registerInstrumentationService.upgraded(
            accountRegisterData.accountId,
            accountRegisterData.registrationType,
            reason,
        )
        if (shouldNotify) {
            notificationAdapter.notifyRegisterUpgraded(accountRegisterData.accountId, accountRegisterData.mobilePhone!!)
        }
        return updatedAccountRegisterData
    }

    open fun rejectRegister(
        accountRegisterData: AccountRegisterData,
        reason: List<RiskAnalysisFailedReason>,
    ): AccountRegisterData {
        val accountId = accountRegisterData.accountId

        loginRepository.remove(accountId)

        val updatedAccountRegisterData =
            accountRegisterRepository.save(
                accountRegisterData.copy(
                    riskAnalysisFailedReasons = reason,
                ),
            )

        val accountClosureReason =
            if (reason.any { it.fraud }) {
                AccountClosureReason.FRAUD
            } else {
                AccountClosureReason.RISK_ANALYSIS
            }

        closeAccountService.deactivateAccountRegister(
            accountId,
            AccountClosureDetails.create(
                reason = accountClosureReason,
                description = "Cadastro rejeitado por análise de risco",
            ),
        ).getOrElse {
            throw it
        }
        accountService.updatePartialAccountStatus(accountId, AccountStatus.DENIED)
        registerInstrumentationService.rejected(
            accountId,
            accountRegisterData.registrationType,
            reason,
        )
        accountRegisterData.externalId?.let { externalId ->
            modattaProvider.updateStatus(
                accountId,
                externalId,
                AccountStatus.DENIED,
                reason,
            )
        }
        notificationAdapter.notifyRegisterDenied(accountId, accountRegisterData.mobilePhone!!)
        return updatedAccountRegisterData
    }

    open fun reopenRegister(
        accountRegister: AccountRegisterData,
        reason: List<RiskAnalysisFailedReason>,
    ): AccountRegisterData {
        val updatedAccountRegisterData =
            accountRegisterRepository.save(
                accountRegister.copy(
                    riskAnalysisFailedReasons = reason,
                    openForUserReview = true,
                    openedForUserReviewAt = getZonedDateTime(),
                ),
            )
        accountService.updatePartialAccountStatus(accountRegister.accountId, AccountStatus.REGISTER_INCOMPLETE)
        notificationAdapter.notifyBasicSignUpReopened(accountRegister.accountId, accountRegister.mobilePhone!!)
        registerInstrumentationService.reopened(
            accountRegister.accountId,
            accountRegister.registrationType,
            reason,
        )
        return updatedAccountRegisterData
    }

    open fun addPersonalInfo(
        accountId: AccountId,
        name: String,
        birthDate: LocalDate,
    ): Either<Exception, BasicAccountRegisterData> {
        return try {
            val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)
            if (checkUserBlockedForEdition(accountRegisterData.accountId)) {
                return Either.Left(AccountIsBlockedForEdition())
            }

            val updatedAccountRegisterData =
                accountRegisterData
                    .copy(
                        nickname = name,
                        birthDate = birthDate,
                        documentInfo = accountRegisterData.documentInfo?.copy(name = name, birthDate = birthDate),
                    )
                    .let(accountRegisterRepository::save)

            accountService.updatePartialAccountName(accountId, name)
            updatedAccountRegisterData.toBasicAccountRegisterData().right()
        } catch (e: Exception) {
            e.left()
        }
    }

    open fun processLiveness(accountRegister: BasicAccountRegisterData): Either<Exception, BasicAccountRegisterData> {
        if (accountRegister.livenessId == null || accountRegister.uploadedSelfie != null) {
            return accountRegister.right()
        }

        val selfieBytes =
            livenessService.retrieveEnrollmentSelfie(accountRegister.livenessId).getOrElse {
                return when (it) {
                    is LivenessSelfieError.Error -> it.e.left()
                    LivenessSelfieError.Unavailable -> accountRegister.right()
                }
            }

        return processSelfie(
            accountId = accountRegister.accountId,
            selfie = ByteArrayInputStream(selfieBytes),
            extension = "jpeg",
            optimize = false,
        )
    }

    private fun processSelfie(
        accountId: AccountId,
        selfie: InputStream,
        extension: String,
        optimize: Boolean = true,
    ): Either<Exception, BasicAccountRegisterData> {
        try {
            if (checkUserBlockedForEdition(accountId)) {
                return Either.Left(AccountIsBlockedForEdition())
            }
            val key = buildObjectPath(accountId, userFilesConfiguration.selfiePrefix, extension)
            val storedObject = StoredObject(userFilesConfiguration.region, userFilesConfiguration.bucket, key)

            val originalBytes = selfie.toByteArray()

            val selfieMediaType = when (extension) {
                "jpeg", "jpg" -> MediaType.IMAGE_JPEG_TYPE
                "png" -> MediaType.IMAGE_PNG_TYPE
                else -> MediaType.APPLICATION_OCTET_STREAM_TYPE
            }

            val databaseReferencedSelfie =
                if (optimize) {
                    accountRegisterRepository.putDocument(
                        storedObject.copy(
                            key =
                            buildObjectPath(
                                accountId,
                                userFilesConfiguration.selfiePrefix + "original_",
                                extension,
                            ),
                        ),
                        ByteArrayInputStream(originalBytes),
                        selfieMediaType,
                    )
                    ImageOptimizer.optimize(ByteArrayInputStream(originalBytes), extension).toByteArray()
                } else {
                    originalBytes
                }

            accountRegisterRepository.putDocument(storedObject, ByteArrayInputStream(databaseReferencedSelfie), selfieMediaType)

            val accountRegisterUpdated = accountRegisterRepository.findByAccountId(accountId)

            return Either.Right(
                accountRegisterRepository.save(
                    accountRegisterUpdated.copy(
                        uploadedSelfie = storedObject,
                    ),
                ).toBasicAccountRegisterData(),
            )
        } catch (e: Exception) {
            return Either.Left(e)
        }
    }

    // NOTE: já existe um equivalente no RegisterService, aguardando atualizar a chamada no front para ser removido
    open fun processDocumentDetails(
        accountId: AccountId,
        documentDetails: DocumentDetails,
    ): Either<Exception, BasicAccountRegisterData> {
        try {
            if (checkUserBlockedForEdition(accountId)) {
                return Either.Left(AccountIsBlockedForEdition())
            }

            val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)
            val documentInfo =
                DocumentInfo(
                    name = accountRegisterData.nickname,
                    cpf = accountRegisterData.document!!.value,
                    birthDate = accountRegisterData.birthDate,
                    fatherName = documentDetails.fatherName ?: "",
                    motherName = documentDetails.motherName,
                    rg =
                    if (documentDetails.documentNumber.type == DocumentDetailsType.IDENTITY) {
                        documentDetails.documentNumber.value
                    } else {
                        ""
                    },
                    docType =
                    when (documentDetails.documentNumber.type) {
                        DocumentDetailsType.IDENTITY -> ai.friday.billpayment.app.integrations.DocumentType.RG
                        DocumentDetailsType.DRIVERS_LICENSE -> ai.friday.billpayment.app.integrations.DocumentType.CNH
                    },
                    cnhNumber =
                    if (documentDetails.documentNumber.type == DocumentDetailsType.DRIVERS_LICENSE) {
                        documentDetails.documentNumber.value
                    } else {
                        null
                    },
                    orgEmission = documentDetails.orgEmission,
                    expeditionDate = documentDetails.expeditionDate,
                    birthCity = documentDetails.birthCity,
                    birthState = documentDetails.birthState,
                )

            val updatedAccountRegisterData = accountRegisterData.copy(documentInfo = documentInfo)

            return accountRegisterRepository.save(updatedAccountRegisterData)
                .also { updateUserOnCrm(it) }
                .toBasicAccountRegisterData()
                .right()
        } catch (e: Exception) {
            return Either.Left(e)
        }
    }

    open fun processPoliticallyExposed(
        accountId: AccountId,
        selfDeclared: Boolean,
        clientIP: String,
    ): Either<Exception, BasicAccountRegisterData> {
        try {
            if (checkUserBlockedForEdition(accountId)) {
                return Either.Left(AccountIsBlockedForEdition())
            }

            val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)
            val updatedAccountRegisterData =
                accountRegisterData.copy(
                    politicallyExposed =
                    accountRegisterData.politicallyExposed?.copy(
                        selfDeclared = selfDeclared,
                    ) ?: PoliticallyExposed(
                        selfDeclared = selfDeclared,
                        query = null,
                    ),
                )

            return accountRegisterRepository.save(updatedAccountRegisterData).toBasicAccountRegisterData().right()
        } catch (e: Exception) {
            return Either.Left(e)
        }
    }

    private fun AccountRegisterData.isBasicRegisterComplete(): Boolean {
        return document != null &&
            birthDate != null &&
            emailVerified &&
            uploadedSelfie != null &&
            nickname != TEMPORARY_NICKNAME &&
            politicallyExposed != null
    }

    open fun processAgreement(
        accountId: AccountId,
        clientIP: String,
    ): Either<Exception, BasicAccountRegisterData> {
        val accountRegister = accountRegisterRepository.findByAccountId(accountId)
        val partialAccount = accountService.findPartialAccountById(accountId)

        if (checkUserBlockedForEdition(accountId)) {
            return accountRegister.toBasicAccountRegisterData().right()
        }

        if (!accountRegister.isBasicRegisterComplete()) {
            return RegisterIncompleteException().left()
        }

        val contractForm = accountRegister.toSimpleContractForm()

        val userContractFile =
            createSimpleContractPdf(
                accountId = accountRegister.accountId,
                simpleContractForm = contractForm,
                clientIP = clientIP,
            )

        val event =
            SimpleSignUpPendingMessage(
                livenessId = accountRegister.livenessId!!.value,
                userContractKey = userContractFile.key,
                userContractSignature = contractForm.generateSignatureKey(),
                accountId = accountRegister.accountId.value,
                selfieCaptured = false,
                userContractCaptured = false,
                kycDossierGenerated = false,
                ecmDocumentsSent = false,
                hasPassedRiskAnalysis = false,
                accountActivated = false,
                deduplicationVerified = false,
                identityVerified = false,
            )
        accountService.updatePartialAccountStatus(accountId, AccountStatus.UNDER_REVIEW)
        val updatedPartialAccount = accountService.findPartialAccountById(accountId)
        messagePublisher.sendMessage(simpleSignUpQueueName, event)
        registerInstrumentationService.completed(accountRegister.accountId, accountRegister.registrationType)
        chatbotNotificationService.notifyRegisterCompleted(updatedPartialAccount)

        return accountRegister.toBasicAccountRegisterData().right()
    }

    private fun AccountRegisterData.toSimpleContractForm() =
        SimpleContractForm(
            // fullName =
            nickname,
            // cpf =
            document!!.value,
            // birthDate =
            birthDate!!.format(dateFormat),
            // email =
            emailAddress.value,
            // phone =
            mobilePhone!!.msisdn,
            // politicallyExposed =
            politicallyExposed!!.selfDeclared,
            // tenant =
            envToCommCentreTenant(),
        )

    private fun createSimpleContractPdf(
        accountId: AccountId,
        simpleContractForm: SimpleContractForm,
        clientIP: String,
    ): StoredObject {
        val fileCreated = ByteArrayOutputStream()
        agreementFilesService.createSimpleContract(
            stream = fileCreated,
            simpleContractForm = simpleContractForm,
            clientIP = clientIP,
        )
        with(userFilesConfiguration) {
            return createStoredObject(
                fileContent = fileCreated.toByteArray(),
                key = "$path/${accountId.value}/${contractPrefix}${getZonedDateTime().toEpochSecond()}.pdf",
                mediaType = MediaType.APPLICATION_PDF_TYPE,
            )
        }
    }

    open fun cleanUpTestAccount(
        accountId: AccountId,
        email: EmailAddress,
    ) {
        if (accountRegisterService.checkIsTestAccount(accountId)) {
            closeAccountService.deletePartialTestAccount(accountId)
        }
    }

    open fun findByAccountId(accountId: AccountId): Either<Exception, BasicAccountRegisterData> {
        return try {
            val accountRegisterData = accountRegisterRepository.findByAccountId(accountId, checkOpenAccount = false)
            accountRegisterData.toBasicAccountRegisterData().right()
        } catch (e: Exception) {
            e.left()
        }
    }

    /*
        callback do arbi com a criação de conta ou nao (acontece apos o internalApproveAccount)
        se a conta foi criada:
          - cria pix
          - dda
          - cria accountPaymentMethod
          - cria um Account OWNER com receiveDDANotification false (e nao apaga o account GUEST, esse é necessario para que o usuario continue logando como GUEST)
          - notifica atendimento
        se a conta nao foi criada:
          - notifica atendimento
     */
    open fun updateAccountStatus(
        document: String,
        status: ExternalRegisterStatus,
    ): Either<Exception, SetupAccountResult> {
        val markers =
            append("document", document)
                .andAppend("status", status)
        val lock = accountStatusLockProvider.acquireLock(document) ?: return AlreadyLockedException(document).left()
        return try {
            val accountRegisterData = accountRegisterRepository.findByDocument(document)
            markers.andAppend("accountId", accountRegisterData.accountId.value)

            val partialAccount = accountService.findPartialAccountById(accountRegisterData.accountId)
            markers.andAppend("accountStatus", partialAccount.status.name)

            if (partialAccount.status == AccountStatus.APPROVED && status == ExternalRegisterStatus.APPROVED) {
                LOG.info(markers, "updateAccountStatus")
                return SetupAccountResult.AccountApproved.right()
            }
            if (partialAccount.status == AccountStatus.UNDER_REVIEW && status == ExternalRegisterStatus.REJECTED) {
                LOG.info(markers, "updateAccountStatus")
                return SetupAccountResult.AccountRejected.right()
            }
            if (partialAccount.status != AccountStatus.UNDER_REVIEW) {
                LOG.warn(markers, "updateAccountStatus")
                return Either.Left(AccountIsNotUnderReview(partialAccount.status))
            }

            when (status) {
                ExternalRegisterStatus.APPROVED ->
                    handleAccountExternallyApproved(
                        accountRegisterData,
                        partialAccount,
                        markers,
                    )

                ExternalRegisterStatus.REJECTED ->
                    handleAccountExternallyRejected(
                        accountRegisterData,
                        markers,
                    ).right()
            }
        } catch (e: Exception) {
            LOG.error(markers, "updateAccountStatus", e)
            e.left()
        } finally {
            lock.unlock()
        }
    }

    /*
    ultimo passo de aprovação do cliente (acontece apos updateAccountStatus, porém tem que esperar 1hora para dar tempo do dda rodar pro usuario)
      se aprovado:
        - apaga account GUEST (partial account)
        - muda estado do account para ACTIVE
        - apaga o login de GUEST e cria o de OWNER
        - ajusta receiveDDANotification para true
        - notifica o usuario
      se rejeitado:
        - notifica o atendimento
     */
    open fun activateAccount(accountId: AccountId): Either<Exception, Account> {
        return try {
            val partialAccount = accountService.findPartialAccountById(accountId)
            if (partialAccount.status != AccountStatus.APPROVED) {
                return Either.Left(AccountIsNotUnderReview(partialAccount.status))
            }

            val accountRegister = accountRegisterRepository.findByAccountId(accountId)

            val username = accountRegister.getCPF().toString()
            if (userPoolAdapter.doesUserExist(username) && !userPoolAdapter.isUserEnabled(username)) {
                reactivateUserInCognito(username, accountRegister).getOrElse {
                    return when (it) {
                        is UserPoolEnableUserError.UserNotFound -> Either.Left(AccountNotFoundException(accountId.value))
                        is UserPoolEnableUserError.Unknown -> Either.Left(it.exception)
                    }
                }
            }

            val account =
                doActivateAccount(accountRegister).getOrElse {
                    return it.left()
                }

            if (accountRegister.shouldUpdateExternalAccountProvider()) {
                notifyExternalAccountProvider(
                    accountRegister,
                    account.status,
                    SimpleSignUpApprover.EXTERNAL_ACCOUNT_REGISTER,
                )
            }

            account.right()
        } catch (e: AccountNotFoundException) {
            try {
                val account = accountService.findAccountById(accountId)

                if (account.status == AccountStatus.ACTIVE) {
                    account.right()
                } else {
                    Either.Left(e)
                }
            } catch (e2: Exception) {
                Either.Left(e)
            }
        } catch (e: Exception) {
            Either.Left(e)
        }
    }

    private fun reactivateUserInCognito(
        document: String,
        accountRegisterData: AccountRegisterData,
    ): Either<UserPoolEnableUserError, Unit> {
        try {
            userPoolAdapter.setAccountId(document, accountRegisterData.accountId)
            userPoolAdapter.updateEmailAddress(document, accountRegisterData.emailAddress)
            userPoolAdapter.updatePhoneNumber(document, accountRegisterData.mobilePhone!!)
        } catch (e: UserNotFoundException) {
            UserPoolEnableUserError.UserNotFound.left()
        } catch (e: Exception) {
            UserPoolEnableUserError.Unknown(e).left()
        }

        userPoolAdapter.enableUser(document).mapLeft {
            return it.left()
        }

        return Unit.right()
    }

    private fun notifyExternalAccountProvider(
        accountRegisterData: AccountRegisterData,
        accountStatus: AccountStatus,
        approvedBy: SimpleSignUpApprover? = null,
        riskAnalysisFailedReasons: List<RiskAnalysisFailedReason>? = null,
    ) {
        modattaProvider.updateStatus(
            accountId = accountRegisterData.accountId,
            externalId = accountRegisterData.externalId!!,
            status = accountStatus,
            approvedBy = approvedBy,
            riskAnalysisFailedReasons = riskAnalysisFailedReasons,
        )
    }

    open fun updateAgreementFiles(
        accountId: AccountId,
        clientIP: String,
    ): AccountRegisterData {
        val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)

        if (!accountRegisterData.isBasicRegisterComplete()) {
            return accountRegisterData
        }

        if (accountRegisterData.agreementData?.acceptedAt != null) {
            return accountRegisterData
        }

        val contractForm = accountRegisterData.toSimpleContractForm()
        val signatureKey = contractForm.generateSignatureKey()

        if (signatureKey == accountRegisterData.agreementData?.userContractSignature) {
            return accountRegisterData
        }
        val contractStoredFile =
            createSimpleContractPdf(
                accountRegisterData = accountRegisterData,
                clientIP = clientIP,
            )

        val agreementData =
            AgreementData(
                acceptedAt = accountRegisterData.agreementData?.acceptedAt,
                userContractFile = contractStoredFile,
                userContractSignature = signatureKey,
            )

        return accountRegisterRepository.save(accountRegisterData.copy(agreementData = agreementData))
    }

    private fun updateUserOnCrm(accountRegisterData: AccountRegisterData) {
        try {
            crmService.upsertContact(
                accountRegisterData,
            )
        } catch (e: Exception) {
            LOG.error(append("accountId", accountRegisterData.accountId), "UpdateUserOnCrm", e)
        }
    }

    private fun createAttachment(
        documentKey: String,
        inputStream: InputStream,
    ): Attachment {
        val documentKeyFormatted = documentKey.lowercase()
        return Attachment.of(
            inputStream,
            when {
                documentKeyFormatted.endsWith("pdf") -> MediaType.APPLICATION_PDF
                documentKeyFormatted.endsWith("png") -> MediaType.IMAGE_PNG
                documentKeyFormatted.endsWith("html") -> MediaType.TEXT_HTML
                else -> MediaType.IMAGE_JPEG
            },
            documentKeyFormatted,
        )
    }

    @OptIn(ExperimentalContracts::class)
    private fun canValidateIdentity(
        document: Document?,
        uploadedSelfie: StoredObject?,
    ): Boolean {
        contract {
            returns(true) implies (document != null && uploadedSelfie != null)
        }

        return document != null &&
            uploadedSelfie != null
    }

    private fun handleAccountExternallyRejected(
        accountRegisterData: AccountRegisterData,
        markers: LogstashMarker,
    ): SetupAccountResult.AccountRejected {
        accountService.updatePartialAccountStatus(accountRegisterData.accountId, AccountStatus.UNDER_REVIEW)
        notifyAccountPendingReview(accountRegisterData, markers)
        try {
            registerInstrumentationService.externallyRejected(
                accountRegisterData.accountId,
                accountRegisterData.registrationType,
            )
        } catch (e: Exception) {
            LOG.error(markers.and(append("ACTION", "VERIFY")), "AccountExternallyRejectedCRMError", e)
        }
        return SetupAccountResult.AccountRejected
    }

    private fun handleAccountExternallyApproved(
        accountRegisterData: AccountRegisterData,
        partialAccount: PartialAccount,
        markers: LogstashMarker,
    ): Either<Exception, SetupAccountResult> {
        return try {
            val shouldSetupAccount =
                try {
                    accountService.findPhysicalBankAccountByAccountId(accountRegisterData.accountId)
                    false
                } catch (e: PaymentMethodNotFound) {
                    true
                }
            markers.andAppend("shouldSetupAccount", shouldSetupAccount)

            if (shouldSetupAccount) {
                val registerNaturalPersonResponse =
                    externalAccountRegister.simpleRegisterNaturalPerson(
                        customer = accountRegisterData.toExternalAccount(),
                    )

                if (!registerNaturalPersonResponse.success) {
                    return Either.Left(RegisterNaturalPersonException())
                }

                setupAccountInternally(
                    accountRegisterData,
                    partialAccount,
                    BankAccount(
                        accountType = AccountType.CHECKING,
                        bankNo = 213,
                        routingNo = 1,
                        accountNo = registerNaturalPersonResponse.accountNumber!!.toBigInteger(),
                        accountDv = registerNaturalPersonResponse.accountDv!!,
                        document = accountRegisterData.document!!.value,
                        ispb = null, // TODO
                    ),
                )
            }

            val setupAccountResult = registerPixKeyAndDDA(accountRegisterData)
            markers.andAppend("setupAccountResult", setupAccountResult)

            accountService.updatePartialAccountStatus(accountRegisterData.accountId, AccountStatus.APPROVED)

            return if (accountRegisterData.shouldSetupDDA()) {
                notifyAccountPendingActivation(setupAccountResult, accountRegisterData, markers)

                LOG.info(markers, "updateAccountStatus")
                setupAccountResult.right()
            } else {
                activateAccount(accountRegisterData.accountId).map {
                    LOG.info(markers, "updateAccountStatus")
                    setupAccountResult.right()
                }.getOrElse {
                    LOG.error(markers, "updateAccountStatus", it)
                    it.left()
                }
            }
        } catch (e: Exception) {
            LOG.error(markers, "updateAccountStatus", e)
            Either.Left(e)
        }
    }

    private fun AccountRegisterData.shouldSetupDDA() = externalId?.providerName != AccountProviderName.MODATTA

    private fun AccountRegisterData.shouldUpdateExternalAccountProvider() =
        externalId?.providerName == AccountProviderName.MODATTA

    private fun notifyAccountPendingActivation(
        setupAccountResult: SetupAccountResult,
        accountRegisterData: AccountRegisterData,
        markers: LogstashMarker,
    ) {
        try {
            val pixCreatedMessage =
                if (setupAccountResult != SetupAccountResult.AddPixKeyFailed) {
                    "Sim"
                } else {
                    "Não"
                }
            val ddaRegisteredMessage =
                if (setupAccountResult == SetupAccountResult.AccountApproved) {
                    "Sim"
                } else {
                    "Não"
                }

            val subject = String.format(pendingActivationConfiguration.subject, accountRegisterData.accountId.value)
            val message =
                String.format(
                    pendingActivationConfiguration.message,
                    accountRegisterData.accountId.value,
                    accountRegisterData.nickname,
                    accountRegisterData.emailAddress,
                    pixCreatedMessage,
                    ddaRegisteredMessage,
                )

            notificationSenderService.sendRawEmail(
                from,
                subject,
                message,
                pendingActivationConfiguration.recipients,
            )
            notificationSenderService.sendRawEmail(
                from,
                subject,
                message,
                pendingActivationConfiguration.sensitiveRecipients,
            )
        } catch (e: Exception) {
            LOG.error(markers, "notifyAccountPendingActivation", e)
        }
    }

    private fun AccountRegisterData.toExternalAccount(): ExternalAccount {
        return ExternalAccount(
            documentInfo =
            DocumentInfo(
                name = nickname,
                cpf = document!!.value,
                birthDate = birthDate,
                fatherName = "",
                motherName = "",
                rg = "",
                docType = document.type,
                cnhNumber = null,
                orgEmission = document.issuer,
                expeditionDate = null,
                birthCity = "",
                birthState = document.issuerRegion,
            ),
            address = userAddressConfiguration.toAddress(),
            email = emailAddress.value,
            mobilePhone = mobilePhone!!,
            politicallyExposed = PoliticallyExposed(false, null),
            monthlyIncome = MonthlyIncome(4_000_01, 10_000_00),
            calculatedGender = this.calculatedGender ?: Gender.M,
        )
    }

    private fun notifyAccountPendingReview(
        accountRegisterData: AccountRegisterData,
        markers: LogstashMarker,
    ) {
        try {
            val subject =
                String.format(pendingInternalReviewConfiguration.subject, accountRegisterData.accountId.value)
            val message =
                String.format(
                    pendingInternalReviewConfiguration.message,
                    accountRegisterData.accountId.value,
                    accountRegisterData.nickname,
                    accountRegisterData.emailAddress,
                )

            notificationSenderService.sendRawEmail(
                from,
                subject,
                message,
                pendingInternalReviewConfiguration.recipients,
            )

            sendEmailWithUserDocuments(
                accountRegisterData,
                subject,
                message,
                pendingInternalReviewConfiguration.sensitiveRecipients,
            )
        } catch (e: Exception) {
            LOG.error(markers, "notifyAccountPendingReview", e)
        }
    }

    private fun sendEmailWithUserDocuments(
        accountRegisterData: AccountRegisterData,
        subject: String,
        message: String,
        recipient: String,
    ) {
        accountRegisterData.sensitiveDocuments().attachAll { attachments ->
            notificationSenderService.sendRawEmail(
                from,
                subject,
                message,
                recipient,
                attachments,
            )
        }
    }

    private fun doActivateAccount(accountRegister: AccountRegisterData): Either<Exception, Account> {
        val account = accountService.findAccountById(accountRegister.accountId)

        val groups = listOf(OnboardingTestPixService.randomAccountGroup(), AccountGroup.CHATBOT_AI_NEW_USERS)

        val accountWithGroups = accountService.addGroupsToAccount(account.accountId, groups)

        if (!crmService.contactExists(accountWithGroups.accountId)) {
            updateUserOnCrm(accountRegister)
            throw CrmContactNotFoundException(accountWithGroups.accountId)
        }

        accountService.deletePartialAccount(accountRegister.accountId)

        val updatedAccount = updateActivatedAccount(accountWithGroups)

        registerInstrumentationService.activated(updatedAccount.accountId, accountRegister.registrationType)
        crmService.upsertContact(updatedAccount)
        userJourneyService.registerAsync(updatedAccount)
        systemActivityService.setAccountActivated(accountId = updatedAccount.accountId)
        adService.publishAccountActivated(accountId = updatedAccount.accountId)

        createWhatsappLogin(account)

        LOG.info(
            append("accountId", updatedAccount.accountId)
                .andAppend("status", updatedAccount.status)
                .andAppend("created", updatedAccount.created),
            "doActivateAccount",
        )

        return if (accountRegister.identityValidationStatus == IdentityValidationStatus.CHECKED) { // FIXME fazer um teste pra isso
            accountService.enableCreditCardUsage(
                accountId = updatedAccount.accountId,
                quota = 2_000_00,
                sendNotification = false,
            )
        } else {
            updatedAccount.right()
        }
    }

    private fun updateActivatedAccount(
        account: Account,
    ): Account {
        val tmpAccount =
            account.copy(
                status = AccountStatus.ACTIVE,
                configuration =
                account.configuration.copy(
                    receiveDDANotification = true,
                ),
                activated = getZonedDateTime(),
            )
        accountService.save(tmpAccount)
        walletLimitsService.updateLimit(
            accountId = account.accountId,
            walletId = WalletId(account.accountId.value),
            type = DailyPaymentLimitType.NIGHTTIME,
            amount = 500_00L,
            source = ActionSource.System,
        )
        walletLimitsService.updateLimit(
            accountId = account.accountId,
            walletId = WalletId(account.accountId.value),
            type = DailyPaymentLimitType.DAILY,
            amount = 500_00L,
            source = ActionSource.System,
        )

        walletLimitsService.updateLimit(
            accountId = account.accountId,
            walletId = WalletId(account.accountId.value),
            type = DailyPaymentLimitType.MONTHLY,
            amount = 10_000_00L,
            source = ActionSource.System,
        )

        val hasChatBotEnabled = accountService.getChatbotType(account).checkHasAIChatbotEnabled()
        if (hasChatBotEnabled) {
            chatbotNotificationService.notifyWelcomeMessage(
                account = account,
                chatBotWelcomeMessageType = WelcomeMessageType.WELCOME_CHATBOT_ONBOARDING_SINGLE_PIX,
            )
        } else {
            notificationAdapter.notifyUserActivated(account)
        }

        return tmpAccount
    }

    private fun setupAccountInternally(
        accountRegister: AccountRegisterData,
        partialAccount: PartialAccount,
        bankAccount: BankAccount,
    ) {
        val cpf = accountRegister.getCPF() ?: throw RegisterIncompleteException("documentInfo")
        val msisdn = accountRegister.mobilePhone?.msisdn ?: throw RegisterIncompleteException("mobilePhone")

        val newAccount =
            Account(
                accountId = accountRegister.accountId,
                name = accountRegister.nickname,
                emailAddress = accountRegister.emailAddress,
                document = cpf,
                documentType = "CPF",
                mobilePhone = msisdn,
                created = getZonedDateTime(),
                updated = getZonedDateTime(),
                status = AccountStatus.APPROVED,
                configuration =
                LegacyAccountConfiguration(
                    creditCardConfiguration = CreditCardConfiguration(),
                    defaultWalletId = null,
                    receiveDDANotification = false,
                    receiveNotification = true,
                    groups = partialAccount.groups,
                    externalId = accountRegister.externalId,
                ),
                activated = null,
                type = accountRegister.registrationType.toUserAccountType(),
                imageUrlSmall = null,
                imageUrlLarge = null,
                subscriptionType = partialAccount.subscriptionType,
            )
        accountService.create(newAccount)

        val accountPaymentMethod =
            accountService.createAccountPaymentMethod(
                accountRegister.accountId,
                bankAccount.copy(document = cpf),
                1,
            )

        val wallet = walletService.createPrimaryWallet(newAccount, accountPaymentMethod.id)

        walletBillCategoryService.createDefaultWalletCategories(wallet.id)

        val updatedNewAccount =
            newAccount.copy(configuration = newAccount.configuration.copy(defaultWalletId = wallet.id))

        accountService.save(updatedNewAccount)
    }

    private fun registerPixKeyAndDDA(
        accountRegister: AccountRegisterData,
    ): SetupAccountResult {
        val cpf = accountRegister.document!!.value
        val name = accountRegister.nickname

        val bankAccount =
            accountService.findPhysicalBankAccountByAccountId(accountRegister.accountId)
                .single().method as InternalBankAccount

        if (accountRegister.shouldSetupDDA()) {
            val ddaStatus = ddaService.register(accountRegister.accountId, cpf)
            if (ddaStatus.optedOut()) {
                return SetupAccountResult.RegisterDDAFailed
            }
        }

        messagePublisher.sendMessage(
            registerPixKeyQueueName,
            RegisterPixKeyCommand(
                accountNo = AccountNumber(bankAccount.accountNo.toBigInteger(), bankAccount.accountDv),
                key = PixKey(value = "$cpf@$pixKeyEmailDomain", type = PixKeyType.EMAIL),
                document = cpf,
                name = name,
                walletId = WalletId(accountRegister.accountId.value),
            ),
        )

        messagePublisher.sendMessage(
            registerPixKeyQueueName,
            RegisterPixKeyCommand(
                accountNo = AccountNumber(bankAccount.accountNo.toBigInteger(), bankAccount.accountDv),
                key = PixKey(value = "", type = PixKeyType.EVP),
                document = cpf,
                name = name,
                walletId = WalletId(accountRegister.accountId.value),
            ),
        )

        return SetupAccountResult.AccountApproved
    }

    private fun InputStream.toByteArray() =
        use {
            it.readAllBytes()
        }

    private fun checkUserBlockedForEdition(accountId: AccountId): Boolean {
        val partialAccount = accountService.findPartialAccountById(accountId)
        return partialAccount.status != AccountStatus.REGISTER_INCOMPLETE
    }

    private fun createSimpleContractPdf(
        accountRegisterData: AccountRegisterData,
        clientIP: String,
    ): StoredObject {
        val fileCreated = ByteArrayOutputStream()
        agreementFilesService.createSimpleContract(
            stream = fileCreated,
            simpleContractForm = accountRegisterData.toSimpleContractForm(),
            clientIP = clientIP,
        )
        with(userFilesConfiguration) {
            return createStoredObject(
                fileContent = fileCreated.toByteArray(),
                key = "$path/${accountRegisterData.accountId.value}/${contractPrefix}${getZonedDateTime().toEpochSecond()}.pdf",
                mediaType = MediaType.APPLICATION_PDF_TYPE,
            )
        }
    }

    private fun createStoredObject(
        fileContent: ByteArray,
        key: String,
        mediaType: MediaType,
    ): StoredObject {
        with(userFilesConfiguration) {
            val storedObject = StoredObject(region, bucket, key)
            accountRegisterRepository.putDocument(storedObject, ByteArrayInputStream(fileContent), mediaType)

            return storedObject
        }
    }

    fun findPartialAccountByStatus(accountStatus: AccountStatus): List<Pair<PartialAccount, AccountRegisterData?>> {
        return accountService.findPartialAccountByStatus(accountStatus = accountStatus).map {
            val accountRegister =
                try {
                    accountRegisterRepository.findByAccountId(it.id)
                } catch (e: ItemNotFoundException) {
                    null
                }
            Pair(it, accountRegister)
        }
    }

    private fun AccountRegisterData.sensitiveDocuments() =
        listOfNotNull(
            agreementData?.userContractFile,
            uploadedSelfie,
            kycFile,
            agreementData?.declarationOfResidencyFile,
        )

    private fun List<StoredObject>.attachAll(
        attachments: MutableList<Attachment> = mutableListOf(),
        block: (attachments: List<Attachment>) -> Unit,
    ) {
        val markers = append("listSize", this.size)
        if (attachments.size >= this.size) {
            LOG.info(markers.andAppend("attachmentsSize", attachments.size), "attachAll")
            return block(attachments)
        }

        val storedObject = this[attachments.size]

        accountRegisterRepository.getDocumentInputStream(storedObject).use { inputStream ->
            val attachment = createAttachment(storedObject.key, inputStream)
            markers.andAppend("fileName", attachment.fileName)
            markers.andAppend("contentType", attachment.contentType)
            attachments.add(attachment)
            LOG.info(markers, "attachAll")
            attachAll(attachments, block)
        }
    }

    private fun buildObjectPath(
        accountId: AccountId,
        prefix: String,
        extension: String,
    ) =
        "${userFilesConfiguration.path}/${accountId.value}/$prefix${getZonedDateTime().toEpochSecond()}.$extension"

    private fun createWhatsappLogin(account: Account) {
        try {
            loginRepository.createLogin(
                providerUser = ProviderUser(
                    id = account.accountId.value,
                    providerName = ProviderName.WHATSAPP,
                    username = "",
                    emailAddress = createWhatsappEmail(MobilePhone(account.mobilePhone)),
                ),
                id = account.accountId,
                role = Role.OWNER,
            )
        } catch (e: Exception) {
            LOG.warn(append("accountId", account.accountId), "BasicRegisterService#createWhatsappLogin")
        }
    }

    companion object {
        internal val LOG = LoggerFactory.getLogger(BasicRegisterService::class.java)
    }
}

private fun IdentityValidationStatus.toRiskAnalysisFailedReason(): List<RiskAnalysisFailedReason>? {
    return when (this) {
        IdentityValidationStatus.CHECKED -> null
        IdentityValidationStatus.UNKNOWN -> listOf(RiskAnalysisFailedReason.UNABLE_TO_VALIDATE_DOCUMENT)
        IdentityValidationStatus.REJECTED -> listOf(RiskAnalysisFailedReason.DOCUMENT_VALIDATION_REJECTED)
    }
}

fun AccountRegisterData.toBasicAccountRegisterData() =
    BasicAccountRegisterData(
        accountId = accountId,
        emailAddress = emailAddress,
        emailVerified = emailVerified,
        emailTokenExpiration = emailTokenExpiration,
        name = nickname,
        birthDate = birthDate,
        document = document,
        mobilePhone = mobilePhone,
        mobilePhoneVerified = mobilePhoneVerified,
        mobilePhoneTokenExpiration = mobilePhoneTokenExpiration,
        created = created,
        lastUpdated = lastUpdated,
        livenessId = livenessId,
        uploadedSelfie = uploadedSelfie,
        agreementData = agreementData,
        kycFile = kycFile,
        openForUserReview = openForUserReview,
        openedForUserReviewAt = openedForUserReviewAt,
        externalId = externalId,
        livenessEnrollmentVerification = livenessEnrollmentVerification,
        riskAnalysisFailedReasons = riskAnalysisFailedReasons,
        identityValidationStatus = identityValidationStatus,
        registrationType = registrationType,
        politicallyExposed = politicallyExposed,
    )

data class BasicAccountRegisterData(
    val accountId: AccountId,
    val emailAddress: EmailAddress,
    val emailVerified: Boolean = false,
    val emailTokenExpiration: Long? = null,
    val name: String? = null,
    val birthDate: LocalDate? = null,
    val document: Document? = null,
    val mobilePhone: MobilePhone? = null,
    val mobilePhoneVerified: Boolean = false,
    val mobilePhoneTokenExpiration: Long? = null,
    val created: ZonedDateTime,
    val lastUpdated: ZonedDateTime,
    val livenessId: LivenessId? = null,
    val uploadedSelfie: StoredObject? = null,
    val agreementData: AgreementData? = null,
    val kycFile: StoredObject? = null,
    val openForUserReview: Boolean,
    val openedForUserReviewAt: ZonedDateTime?,
    val externalId: ExternalId? = null,
    val livenessEnrollmentVerification: LivenessEnrollmentVerification = LivenessEnrollmentVerification(),
    val riskAnalysisFailedReasons: List<RiskAnalysisFailedReason>?,
    val identityValidationStatus: IdentityValidationStatus? = null,
    val registrationType: RegistrationType,
    val politicallyExposed: PoliticallyExposed?,
)

sealed class ValidateIdentityError : PrintableSealedClassV2() {
    data object UnableToValidate : ValidateIdentityError()

    data class InvalidResponse(val ex: Exception) : ValidateIdentityError()
}

data class DeviceAdIds(
    val adId: String?,
    val adGoogleId: String?,
    val adAppleId: String?,
)

fun envToCommCentreTenant(): Tenant {
    val service = (System.getenv("DD_SERVICE") ?: "missing_service_property")

    return if (service.contains("poupe", ignoreCase = true)) {
        Tenant.ME_POUPE
    } else {
        Tenant.FRIDAY
    }
}