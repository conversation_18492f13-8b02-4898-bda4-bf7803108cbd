package ai.friday.billpayment.app.account

import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AdAdapter
import ai.friday.billpayment.app.integrations.AdEventMetadata
import ai.friday.morning.log.andAppend
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

private const val accountActivatedEventName = "account-activated"
private const val onboardingTestPixCreatedEventName = "onboarding-test-pix-created"
private const val trialStartedEventName = "trial-started"
private const val trialConvertedEventName = "trial-converted"

@Singleton
open class AdService(
    private val adAdapter: AdAdapter,
    private val systemActivityService: SystemActivityService,
    private val accountRegisterRepository: AccountRegisterRepository,
) {
    private val logger = LoggerFactory.getLogger(AdService::class.java)

    open fun publishAccountActivated(accountId: AccountId) {
        publishEvent(accountId, accountActivatedEventName)
    }

    open fun publishOnboardingTestPixCreated(accountId: AccountId) {
        publishEvent(accountId, onboardingTestPixCreatedEventName)
    }

    open fun publishTrialStarted(accountId: AccountId) {
        publishEvent(accountId, trialStartedEventName)
    }

    open fun publishTrialConverted(accountId: AccountId) {
        publishEvent(accountId, trialConvertedEventName)
    }

    private fun publishEvent(accountId: AccountId, eventName: String) {
        val logName = "AdService#publishEvent"
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("eventName", eventName)
        try {
            val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)
            val clientIP = accountRegisterData.clientIP ?: ""

            val deviceAdIds = systemActivityService.getDeviceInfo(accountId)

            if (deviceAdIds == null) {
                logger.warn(markers.andAppend("context", "no device info"), logName)
                return
            }

            val metadata = AdEventMetadata(
                accountId = accountId,
                trackingIds = deviceAdIds,
                clientIP = clientIP,
                phoneNumber = if (accountRegisterData.mobilePhoneVerified) accountRegisterData.mobilePhone else null,
                emailAddress = if (accountRegisterData.emailVerified) accountRegisterData.emailAddress else null,
                state = accountRegisterData.address?.state,
            )

            adAdapter.trackEvent(eventName, metadata)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
        }
    }
}