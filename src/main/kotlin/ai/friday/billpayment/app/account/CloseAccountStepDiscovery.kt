package ai.friday.billpayment.app.account

import ai.friday.billpayment.app.wallet.Wallet
import arrow.core.Either

interface CloseAccountStepDiscovery {
    val discoveryName: String
    fun prepareClose(accountId: AccountId, closureDetails: AccountClosureDetails): Either<CloseAccountError, List<CloseAccountStep>>
}

interface CloseWalletStepDiscovery {
    val discoveryName: String
    fun prepareCloseWallet(wallet: Wallet, closureDetails: AccountClosureDetails): Either<CloseWalletError, List<CloseWalletStep>>
}