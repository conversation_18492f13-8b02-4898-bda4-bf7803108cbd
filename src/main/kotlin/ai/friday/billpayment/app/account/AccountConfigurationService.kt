package ai.friday.billpayment.app.account

import ai.friday.billpayment.app.integrations.AccountConfigurationRepository
import jakarta.inject.Singleton

@Singleton
class AccountConfigurationService(
    private val accountConfigurationRepository: AccountConfigurationRepository,
) {

    fun find(accountId: AccountId): AccountConfiguration {
        return accountConfigurationRepository.find(accountId)
    }

    fun addPushNotificationToken(pushNotificationToken: String, accountId: AccountId): AccountConfiguration {
        val accountConfiguration = accountConfigurationRepository.find(accountId)

        if (accountConfiguration.pushNotificationTokens.contains(pushNotificationToken)) {
            return accountConfiguration
        }

        val tokensSet = accountConfiguration.pushNotificationTokens + pushNotificationToken
        val accountConfigurationUpdated = accountConfiguration.copy(pushNotificationTokens = tokensSet)
        accountConfigurationRepository.save(accountConfigurationUpdated)

        return accountConfigurationUpdated
    }

    fun hasPushNotificationToken(pushNotificationToken: String, accountId: AccountId): Boolean {
        val accountConfiguration = accountConfigurationRepository.find(accountId)

        return accountConfiguration.pushNotificationTokens.contains(pushNotificationToken)
    }

    fun removePushNotificationToken(pushNotificationToken: String, accountId: AccountId): AccountConfiguration {
        val accountConfiguration = accountConfigurationRepository.find(accountId)

        if (!accountConfiguration.pushNotificationTokens.contains(pushNotificationToken)) {
            return accountConfiguration
        }

        val tokens = accountConfiguration.pushNotificationTokens - pushNotificationToken
        val accountConfigurationUpdated = accountConfiguration.copy(pushNotificationTokens = tokens)
        accountConfigurationRepository.save(accountConfigurationUpdated)

        return accountConfigurationUpdated
    }

    fun updateReceiveMonthlyStatement(accountId: AccountId, receiveMonthlyStatement: Boolean): AccountConfiguration {
        return findAndSave(accountId) { accountConfiguration ->
            accountConfiguration.copy(receiveMonthlyStatement = receiveMonthlyStatement)
        }
    }

    fun updateFreeOfFridaySubscription(accountId: AccountId, freeOfFridaySubscription: Boolean): AccountConfiguration {
        return findAndSave(accountId) { accountConfiguration ->
            accountConfiguration.copy(freeOfFridaySubscription = freeOfFridaySubscription)
        }
    }

    private fun findAndSave(accountId: AccountId, updateFields: (accountConfiguration: AccountConfiguration) -> AccountConfiguration): AccountConfiguration {
        val accountConfiguration = accountConfigurationRepository.find(accountId)
        val updated = updateFields(accountConfiguration)
        accountConfigurationRepository.save(updated)
        return updated
    }
}