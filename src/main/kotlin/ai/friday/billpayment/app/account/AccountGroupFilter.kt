package ai.friday.billpayment.app.account

import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import jakarta.inject.Singleton

@ConfigurationProperties("features.userGroupsFilter")
data class AccountGroupFilterConfiguration @ConfigurationInject constructor(
    val enabled: Boolean,
    val versionRestrictions: List<AccountGroupRestriction>,
)

data class GroupFilterContext(
    val account: Account,
    private val platform: String?,
    private val version: String?,
) {
    private val platforms = setOf("ANDROID", "IOS")

    fun groups(): List<AccountGroup> = account.configuration.groups

    fun validateAndExtract(): Pair<Version, String>? {
        if (version.isNullOrBlank() || platform == null || platform.uppercase() !in platforms) {
            return null
        }
        val semanticVersion = Version.from(version) ?: run { return null }

        return Pair(semanticVersion, platform.uppercase())
    }
}

@Singleton
open class AccountGroupFilter(val configuration: AccountGroupFilterConfiguration) {
    fun filterGroupsByVersion(context: GroupFilterContext): List<AccountGroup> {
        configuration.enabled.takeIf { it } ?: return context.groups()

        val (version, platform) = context.validateAndExtract() ?: run {
            return context.groups() - configuration.versionRestrictions.map { it.group }.toSet()
        }

        val restrictedGroups = configuration.versionRestrictions
            .filter { restriction -> restriction.strategy == AccountGroupRestrictionStrategy.EXCLUDE }
            .filter { restriction -> restriction.platform.uppercase() == platform && restriction.semanticVersion() > version }
            .map { it.group }.toSet()

        val addedGroups = configuration.versionRestrictions
            .filter { restriction -> restriction.strategy == AccountGroupRestrictionStrategy.INCLUDE }
            .filter { restriction -> restriction.platform.uppercase() == platform && restriction.semanticVersion() <= version }
            .map { it.group }.toSet()

        return context.groups() + addedGroups - restrictedGroups
    }
}

data class Version(
    val major: Int,
    val minor: Int,
    val patch: Int,
) : Comparable<Version> {

    override fun toString(): String = "$major.$minor.$patch"

    override operator fun compareTo(other: Version): Int {
        return when {
            major > other.major -> 1
            other.major > major -> -1
            minor > other.minor -> 1
            other.minor > minor -> -1
            patch > other.patch -> 1
            other.patch > patch -> -1
            else -> 0
        }
    }

    companion object {
        private val versionRegex = Regex("""^(?<prefix>v\.?)?(?<major>[0-9]+)\.(?<minor>[0-9]+)\.(?<patch>[0-9]+)(?<suffix>-RC[0-9]+)?$""")

        fun from(version: String): Version? {
            val matchResult = versionRegex.find(version)

            val major: Int = matchResult?.groups?.get("major")?.value?.toInt() ?: return null
            val minor: Int = matchResult.groups["minor"]?.value?.toInt() ?: return null
            val patch: Int = matchResult.groups["patch"]?.value?.toInt() ?: return null

            return Version(major, minor, patch)
        }
    }
}

enum class AccountGroupRestrictionStrategy { INCLUDE, EXCLUDE }

data class AccountGroupRestriction(
    val version: String,
    val platform: String,
    val strategy: AccountGroupRestrictionStrategy,
    val group: AccountGroup,
) {
    fun semanticVersion(): Version = Version.from(version) ?: throw IllegalStateException("invalid version configuration")
}