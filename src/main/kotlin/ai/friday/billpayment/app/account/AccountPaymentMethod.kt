package ai.friday.billpayment.app.account

import ai.friday.billpayment.app.account.AccountPaymentMethodStatus.ACTIVE
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory

data class AccountPaymentMethod(
    val id: AccountPaymentMethodId,
    val accountId: AccountId,
    val method: PaymentMethod,
    var status: AccountPaymentMethodStatus = ACTIVE,
    val created: ZonedDateTime? = null,
)

enum class CreditCardPaymentStatus {
    AUTHORIZED, PAYMENT_CONFIRMED, DENIED, VOIDED, REFUNDED, ABORTED
}

interface PaymentMethod {
    val type: PaymentMethodType
}

fun AccountPaymentMethod.asActive() = this.copy(status = ACTIVE)

fun AccountPaymentMethod.isCreditCard() = this.method.type == PaymentMethodType.CREDIT_CARD

class CreditCard(
    val brand: CreditCardBrand,
    val pan: String, // TODO - remover essa propriedade
    val expiryDate: String,
    val securityCode: String? = null, // TODO - remover essa propriedade
    val holderName: String? = null, // TODO - remover essa propriedade
    val token: CreditCardToken? = null,
    val riskLevel: RiskLevel,
    val binDetails: CreditCardBinDetails?,
    val externalId: CreditCardExternalId? = null,
    val mainCard: Boolean? = false,
    val hmac: String? = null,
) : PaymentMethod {
    val maskedPan: String = maskPan(pan)
    val lastFourDigits = pan.takeLast(4)
    val bin = pan.take(8)

    override val type = PaymentMethodType.CREDIT_CARD

    fun isSameCard(other: CreditCard): Boolean {
        if (this.hmac != null && other.hmac != null) {
            return this.hmac == other.hmac
        }
        return brand == other.brand &&
            expiryDate == other.expiryDate &&
            maskedPan == other.maskedPan && isSameBinDetails(other.binDetails)
    }

    private fun isSameBinDetails(otherBinDetails: CreditCardBinDetails?): Boolean {
        if (binDetails == null || otherBinDetails == null) {
            return true
        }
        return otherBinDetails == binDetails
    }
}

enum class CreditCardBrand(val mainName: String, val aliases: List<String> = emptyList()) {
    AMEX("amex"),
    AURA("aura"),
    DINERS("diners"),
    DISCOVER("discover"),
    ELO("elo"),
    JCB("jcb"),
    HIPERCARD("hipercard"),
    MASTERCARD("master", listOf("mastercard")),
    VISA("visa"),
    ;

    companion object {
        fun find(brand: String): CreditCardBrand {
            val foundBrand = entries.firstOrNull {
                it.name.equals(brand, ignoreCase = true) || it.mainName.equals(
                    brand,
                    ignoreCase = true,
                ) || it.aliases.any { alias ->
                    alias.equals(
                        brand,
                        ignoreCase = true,
                    )
                }
            }

            if (foundBrand == null) {
                logger.error(Markers.append("brand", brand).andAppend("ACTION", "VERIFY"), "CreditCardBrand")
                throw IllegalArgumentException("Unknown credit card brand: $brand")
            }
            return foundBrand
        }

        private val logger = LoggerFactory.getLogger(BalanceService::class.java)
    }
}

data class CreditCardBinDetails(
    val provider: String,
    val cardType: String,
    val foreignCard: Boolean,
    val corporateCard: String,
    val issuer: String,
    val issuerCode: String,
    val prepaid: String,
    val status: String,
)

data class CreditCardExternalId(val value: String, val provider: AccountProviderName)

data class ExternalPaymentMethod(
    val providerName: AccountProviderName,
) : PaymentMethod {
    override val type = PaymentMethodType.EXTERNAL
}

fun maskPan(pan: String) = StringUtils.overlay(pan, StringUtils.repeat("*", pan.length - 4), 0, pan.length - 4)

enum class PaymentMethodType {
    CREDIT_CARD, BALANCE, EXTERNAL
}

enum class AccountPaymentMethodStatus {
    ACTIVE, INACTIVE, BLOCKED, CLOSED, PENDING, PENDING_CLOSE
}

data class AccountPaymentMethodId(val value: String)

data class CreditCardToken(val value: String)

data class CreditCardChallenge(
    val paymentMethodId: AccountPaymentMethodId,
    val accountId: AccountId,
    val value: Long,
    val alternativeValue: Long?,
    val attempts: Int,
    val status: CreditCardChallengeStatus,
    val createdAt: ZonedDateTime,
    val updatedAt: ZonedDateTime,
    val expiresAt: ZonedDateTime,
    val authorizationCode: String,
    val acquirerOrderId: String,
    val alternativeAcquirerOrderId: String?,
)

fun CreditCardChallenge.isExpired() =
    this.status == CreditCardChallengeStatus.EXPIRED || getZonedDateTime()
        .isAfter(this.expiresAt)

fun CreditCardChallenge.increaseAttempt() = this.copy(attempts = this.attempts + 1)

fun CreditCardChallenge.asSuccess() = this.copy(status = CreditCardChallengeStatus.SUCCESS)

enum class CreditCardChallengeStatus {
    ACTIVE, EXPIRED, SUCCESS
}

enum class RiskLevel(val level: Int) {
    LOW(0), MEDIUM(1), HIGH(2)
}