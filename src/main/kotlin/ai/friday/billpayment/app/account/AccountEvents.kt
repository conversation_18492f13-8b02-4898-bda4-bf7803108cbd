package ai.friday.billpayment.app.account

import ai.friday.billpayment.app.fingerprint.DeviceFingerprint
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY)
@JsonSubTypes(
    JsonSubTypes.Type(value = MFAEnabled::class, name = "MFAEnabled"),
    JsonSubTypes.Type(value = MFARequired::class, name = "MFARequired"),
    JsonSubTypes.Type(value = UserCreated::class, name = "UserCreated"),
)
sealed class AccountEvent {
    abstract val accountId: AccountId
    abstract val created: Long
    abstract val eventType: AccountEventType
}

data class DeviceBinded(
    override val accountId: AccountId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    val deviceFingerprint: DeviceFingerprint,
) : AccountEvent() {
    override val eventType: AccountEventType = AccountEventType.DEVICE_BINDED
}

data class UserCreated(
    override val accountId: AccountId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    val username: String,
    val userPoolProvider: UserPoolProvider,
    val mfaEnabled: Boolean,
) : AccountEvent() {
    override val eventType: AccountEventType = AccountEventType.USER_CREATED
}

enum class UserPoolProvider {
    COGNITO,
}

data class MFARequired(
    override val accountId: AccountId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
) : AccountEvent() {
    override val eventType: AccountEventType = AccountEventType.MFA_REQUIRED
}

data class MFAEnabled(
    override val accountId: AccountId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
) : AccountEvent() {
    override val eventType: AccountEventType = AccountEventType.MFA_ENABLED
}

enum class AccountEventType {
    USER_CREATED, MFA_REQUIRED, MFA_ENABLED, DEVICE_BINDED
}