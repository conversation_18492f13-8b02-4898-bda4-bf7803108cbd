package ai.friday.billpayment.app.account

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.integrations.SystemActivityRepository
import ai.friday.billpayment.app.onboarding.CreateOnboardingTestPixError
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import jakarta.inject.Singleton
import java.time.LocalDateTime
import java.time.Period
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

val TrialDuration: Period = Period.ofDays(15) // TODO: o período está hardcoded

@Singleton
open class SystemActivityService(private val systemActivityRepository: SystemActivityRepository) {
    fun setCreatedPassword(accountId: AccountId, created: ZonedDateTime) {
        systemActivityRepository.save(
            accountId.toSystemActivityKey(),
            SystemActivityType.CreatedPassword,
            created.format(dateTimeFormat),
        )
    }

    fun setLastSignedIn(accountId: AccountId) {
        systemActivityRepository.save(
            accountId.toSystemActivityKey(),
            SystemActivityType.LastSignedIn,
            getZonedDateTime().format(dateTimeFormat),
        )
    }

    fun setSubscriptionNotSupportedByDevice(accountId: AccountId) {
        systemActivityRepository.save(
            accountId.toSystemActivityKey(),
            SystemActivityType.SubscriptionNotSupportedByDevice,
            getZonedDateTime().format(dateTimeFormat),
        )
    }

    fun setViewedCategorySummary(accountId: AccountId) {
        systemActivityRepository.save(
            accountId.toSystemActivityKey(),
            SystemActivityType.ViewedCategorySummary,
            getZonedDateTime().format(dateTimeFormat),
        )
    }

    fun getCreatedPassword(accountId: AccountId): LocalDateTime? =
        systemActivityRepository.find(accountId.toSystemActivityKey(), SystemActivityType.CreatedPassword)
            ?.let {
                LocalDateTime.parse(it.value, dateTimeFormat)
            }

    fun getOptOutNotificationDate(accountId: AccountId): LocalDateTime? =
        systemActivityRepository.find(accountId.toSystemActivityKey(), SystemActivityType.HasOptOutNotificationSent)
            ?.let {
                LocalDateTime.parse(it.value, dateTimeFormat)
            }

    fun setOptOutNotificationDate(accountId: AccountId) = systemActivityRepository.save(
        accountId.toSystemActivityKey(),
        SystemActivityType.HasOptOutNotificationSent,
        getZonedDateTime().format(dateTimeFormat),
    )

    fun hasScheduledBill(accountId: AccountId) =
        getFlag(accountId.toSystemActivityKey(), activityType = SystemActivityType.HasScheduledBill)

    fun setHasScheduledBill(accountId: AccountId) {
        setFlag(accountId.toSystemActivityKey(), SystemActivityType.HasScheduledBill, true)
    }

    fun hasLegacyCard(document: Document) =
        getFlag(document.toSystemActivityKey(), activityType = SystemActivityType.HasLegacyCard)

    fun setHasLegacyCard(document: Document) =
        setFlag(document.toSystemActivityKey(), SystemActivityType.HasLegacyCard, true)

    fun setReceivedDDASubscriptionBill(accountId: AccountId) {
        setFlag(accountId.toSystemActivityKey(), SystemActivityType.ReceivedDDASubscriptionBill, true)
    }

    open fun setAcceptedBillingCarousel(accountId: AccountId) {
        setFlag(accountId.toSystemActivityKey(), SystemActivityType.ViewedBillingCarousel, true)
    }

    fun setAcceptedTrial(accountId: AccountId, periodInDays: Int) {
        systemActivityRepository.save(
            accountId.toSystemActivityKey(),
            SystemActivityType.AcceptedTrial,
            "$periodInDays",
        )
    }

    fun setSubscribed(accountId: AccountId) {
        setFlag(
            accountId.toSystemActivityKey(),
            SystemActivityType.Subscribed,
            true,
        )
    }

    fun setAcceptedBankingPartnership(accountId: AccountId) {
        setFlag(
            accountId.toSystemActivityKey(),
            SystemActivityType.AcceptedBankingPartnership,
            true,
        )
    }

    fun setLastDeviceTestGroup(accountId: AccountId, isDeviceControlGroup: Boolean) {
        setFlag(
            accountId.toSystemActivityKey(),
            SystemActivityType.SetLastDeviceTestGroup,
            isDeviceControlGroup,
        )
    }

    fun hasCashedIn(walletId: WalletId) =
        getFlag(walletId.toSystemActivityKey(), activityType = SystemActivityType.HasCashedIn)

    fun setHasCashedIn(walletId: WalletId) {
        setFlag(walletId.toSystemActivityKey(), SystemActivityType.HasCashedIn, true)
    }

    fun findAccountIdsThat(activityType: SystemActivityType): List<AccountId> {
        return systemActivityRepository.find(
            keyType = SystemActivityKeyType.Account,
            activityType = activityType,
            activityValue = "1",
        ).map { accountId ->
            AccountId(accountId)
        }
    }

    fun findAccountIdsLowerThan(activityType: SystemActivityType, activityValue: String): List<AccountId> {
        return systemActivityRepository.findLowerThan(
            keyType = SystemActivityKeyType.Account,
            activityType = activityType,
            activityValue = activityValue,
        ).map { accountId ->
            AccountId(accountId)
        }
    }

    open fun setAccountActivated(
        accountId: AccountId,
        activationDate: ZonedDateTime = getZonedDateTime(),
    ) {
        systemActivityRepository.save(
            accountId.toSystemActivityKey(),
            SystemActivityType.AccountActivated,
            activationDate.format(dateTimeFormat),
        )

        val trialDuration = systemActivityRepository.find(
            accountId.toSystemActivityKey(),
            SystemActivityType.AcceptedTrial,
        )?.value?.toLongOrNull()

        if (trialDuration != null) {
            systemActivityRepository.save(
                accountId.toSystemActivityKey(),
                SystemActivityType.TrialActivated,
                activationDate.plusDays(trialDuration).minusDays(1)
                    .format(dateFormat),
            )
        }
    }

    fun getBillPaidOnOwnWallet(accountId: AccountId) =
        getFlag(accountId.toSystemActivityKey(), activityType = SystemActivityType.BillPaidOnOwnWallet)

    fun setBillPaidOnOwnWallet(accountId: AccountId) {
        setFlag(accountId.toSystemActivityKey(), SystemActivityType.BillPaidOnOwnWallet, true)
    }

    fun getDDABillPaid(accountId: AccountId) =
        getFlag(accountId.toSystemActivityKey(), activityType = SystemActivityType.DDABillPaid)

    fun setDDABillPaid(accountId: AccountId) {
        setFlag(accountId.toSystemActivityKey(), SystemActivityType.DDABillPaid, true)
    }

    enum class OnboardingTestPixSystemActivityValue {
        CREATED,
    }

    fun setOnboardingTestPixCreated(accountId: AccountId) {
        systemActivityRepository.save(
            accountId.toSystemActivityKey(),
            SystemActivityType.OnboardingTestPix,
            OnboardingTestPixSystemActivityValue.CREATED.name,
        )
    }

    fun setOnboardingTestPixError(accountId: AccountId, createOnboardingTestPixError: CreateOnboardingTestPixError) {
        systemActivityRepository.save(
            accountId.toSystemActivityKey(),
            SystemActivityType.OnboardingTestPix,
            createOnboardingTestPixError.name,
        )
    }

    fun getOnboardingTestPix(accountId: AccountId) = systemActivityRepository.find(
        key = accountId.toSystemActivityKey(),
        activityType = SystemActivityType.OnboardingTestPix,
    )?.value

    fun getFirstOwnTrustedBillPaid(accountId: AccountId): ZonedDateTime? {
        val systemActivity =
            systemActivityRepository.find(accountId.toSystemActivityKey(), SystemActivityType.FirstOwnTrustedBillPaid)
                ?: return null
        return ZonedDateTime.parse(systemActivity.value, DateTimeFormatter.ISO_LOCAL_DATE_TIME.withZone(brazilTimeZone))
    }

    fun setFirstOwnTrustedBillPaid(accountId: AccountId, timestamp: ZonedDateTime) {
        if (getFirstOwnTrustedBillPaid(accountId) != null) {
            return
        }
        systemActivityRepository.save(
            accountId.toSystemActivityKey(),
            SystemActivityType.FirstOwnTrustedBillPaid,
            timestamp.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
        )
    }

    fun findFirstOwnTrustedBillPaidBetween(from: ZonedDateTime, to: ZonedDateTime): List<AccountId> {
        return systemActivityRepository.findBetween(
            keyType = SystemActivityKeyType.Account,
            activityType = SystemActivityType.FirstOwnTrustedBillPaid,
            minValue = from.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            maxValue = to.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
        ).map { accountId ->
            AccountId(accountId)
        }
    }

    open fun getDeviceInfo(
        accountId: AccountId,
    ): DeviceAdIds? {
        val systemActivity =
            systemActivityRepository.find(accountId.toSystemActivityKey(), SystemActivityType.AddedDeviceInfo)
                ?: return null
        return getObjectMapper().readValue(systemActivity.value, DeviceAdIds::class.java)
    }

    open fun setDeviceInfo(adIds: DeviceAdIds, accountId: AccountId) {
        systemActivityRepository.save(
            key = SystemActivityKey(value = accountId.value, type = SystemActivityKeyType.Account),
            activityType = SystemActivityType.AddedDeviceInfo,
            activityValue = getObjectMapper().writeValueAsString(adIds),
        )
    }

    open fun updateSurveyReplied(accountId: AccountId, reply: String) {
        systemActivityRepository.save(
            key = accountId.toSystemActivityKey(),
            activityType = SystemActivityType.SurveyReplied,
            activityValue = reply,
        )
    }

    open fun checkSurveyReplied(accountId: AccountId): Boolean {
        val reply = systemActivityRepository.find(accountId.toSystemActivityKey(), SystemActivityType.SurveyReplied) ?: return false

        val answers = getObjectMapper().readValue(reply.value, Map::class.java)

        return answers["expenses"] != null &&
            answers["bankPreference"] != null &&
            answers["billsManagementQuantity"] != null &&
            answers["billsManagementPeople"] != null
    }

    open fun setBootstrappedUserPhoto(accountId: AccountId) {
        systemActivityRepository.save(
            accountId.toSystemActivityKey(),
            SystemActivityType.BootstrappedUserPhoto,
            "true",
        )
    }

    open fun getBootstrappedUserPhoto(accountId: AccountId): Boolean {
        val systemActivity =
            systemActivityRepository.find(accountId.toSystemActivityKey(), SystemActivityType.BootstrappedUserPhoto)
                ?: return false
        return "true" == systemActivity.value
    }

    open fun setWhatsAppPayment(walletId: WalletId, active: Boolean) {
        systemActivityRepository.save(
            walletId.toSystemActivityKey(),
            SystemActivityType.WhatsAppPayment,
            if (active) "true" else "false",
        )
    }

    open fun getWhatsAppPayment(walletId: WalletId): Boolean {
        val systemActivity =
            systemActivityRepository.find(walletId.toSystemActivityKey(), SystemActivityType.WhatsAppPayment)
                ?: return false
        return "true" == systemActivity.value
    }

    fun incrementChatbotTransactionAuthorized(walletId: WalletId): Int {
        val current =
            systemActivityRepository.find(walletId.toSystemActivityKey(), SystemActivityType.ChatbotTransactionAuthorized)
                ?.let { it.value.toIntOrNull() ?: 0 } ?: 0

        val newValue = current + 1

        systemActivityRepository.save(walletId.toSystemActivityKey(), SystemActivityType.ChatbotTransactionAuthorized, "$newValue")

        return newValue
    }

    fun setVersionMigration(versionKey: String) {
        setFlag(
            SystemActivityKey(value = versionKey, type = SystemActivityKeyType.System),
            SystemActivityType.VersionMigration,
            true,
        )
    }

    fun setCreateAccountStarted(accountId: AccountId) =
        systemActivityRepository.save(
            accountId.toSystemActivityKey(),
            SystemActivityType.AccountCreationStarted,
            "true",
        )

    fun getVersionMigration(versionKey: String) =
        getFlag(
            SystemActivityKey(value = versionKey, type = SystemActivityKeyType.System),
            SystemActivityType.VersionMigration,
        )

    fun setSystemActivityFlag(accountId: AccountId, activityType: SystemActivityType, value: Boolean) {
        setFlag(accountId.toSystemActivityKey(), activityType, value)
    }

    fun getSystemActivityFlag(accountId: AccountId, activityType: SystemActivityType): Boolean {
        return getFlag(
            accountId.toSystemActivityKey(),
            activityType,
        )
    }

    private fun setFlag(activityKey: SystemActivityKey, activityType: SystemActivityType, value: Boolean) {
        val activityValue = if (value) {
            "1"
        } else {
            "0"
        }
        systemActivityRepository.save(
            activityKey,
            activityType,
            activityValue,
        )
    }

    fun getFlag(activityKey: SystemActivityKey, activityType: SystemActivityType): Boolean {
        val systemActivity = systemActivityRepository.find(activityKey, activityType)
        val activityValue = systemActivity?.value ?: "0"
        return activityValue == "1"
    }
}

fun AccountId.toSystemActivityKey() = SystemActivityKey(value = value, type = SystemActivityKeyType.Account)
fun Document.toSystemActivityKey() = SystemActivityKey(value = value, type = SystemActivityKeyType.Document)

fun WalletId.toSystemActivityKey() = SystemActivityKey(value = value, type = SystemActivityKeyType.Wallet)

data class SystemActivity(
    val key: SystemActivityKey,
    val value: String,
    val lastUpdated: ZonedDateTime?,
)

data class SystemActivityKey(
    val value: String,
    val type: SystemActivityKeyType,
)

enum class SystemActivityType {
    HasScheduledBill,
    HasCashedIn,
    HasOptOutNotificationSent,
    HasLegacyCard,
    ReceivedDDASubscriptionBill,
    ViewedBillingCarousel,
    AccountActivated,
    TrialActivated,
    AcceptedTrial,
    BillPaidOnOwnWallet,
    DDABillPaid,
    FirstOwnTrustedBillPaid,
    VersionMigration,
    OnboardingTestPix,
    AddedDeviceInfo,
    AcceptedBankingPartnership,
    LastSignedIn,
    CreatedPassword,
    BootstrappedUserPhoto,
    WhatsAppPayment,
    SurveyReplied,
    ViewedCategorySummary,
    Subscribed,
    UserEligibleOnPartner,
    SubscriptionNotSupportedByDevice,
    AccountCreationStarted,
    ChatbotTransactionAuthorized,
    SetLastDeviceTestGroup,
    PromotedSweepingAccountOptOut,
}

enum class SystemActivityKeyType {
    System, Account, Wallet, Document
}