package ai.friday.billpayment.app.account

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.DeclarationOfResidencyForm
import ai.friday.billpayment.app.DeclarationOfResidencySignature
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.formatOrEmpty
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.PDFConverter
import ai.friday.billpayment.app.integrations.TemplateCompiler
import ai.friday.billpayment.app.payment.formatDocument
import ai.friday.billpayment.app.payment.formatZipCode
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.micronaut.context.annotation.Requires
import io.via1.communicationcentre.app.ContractForm
import io.via1.communicationcentre.app.ContractSignature
import io.via1.communicationcentre.app.SimpleContractForm
import io.via1.communicationcentre.app.integrations.ContractWriterService
import jakarta.inject.Singleton
import java.io.OutputStream
import java.math.BigInteger
import java.security.MessageDigest
import java.time.format.DateTimeFormatter
import java.util.Locale

val documentDateFormat: DateTimeFormatter = DateTimeFormatter.ofPattern("dd 'de' MMMM 'de' yyyy", Locale("pt", "BR"))
val shortDateFormat: DateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy 'às' HH:mm:ss")
val agreementDocumentDateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy")

@Singleton
@Requires(env = [FRIDAY_ENV, MODATTA_ENV, ME_POUPE_ENV])
open class AgreementFilesService(
    private val contractWriterService: ContractWriterService,
    private val handlebarTemplateCompiler: TemplateCompiler,
    private val pdfConverter: PDFConverter,
) {

    open fun getContractSignature(contractForm: ContractForm): String {
        return contractForm.generateSignatureKey()
    }

    open fun createContract(stream: OutputStream, contractForm: ContractForm, clientIP: String) {
        val agreementSignatureKey = contractForm.generateSignatureKey()
        val contractSignature = ContractSignature(
            getZonedDateTime(),
            clientIP,
            agreementSignatureKey,
        )
        contractWriterService.createContract(stream, contractForm, contractSignature)
    }

    open fun createSimpleContract(stream: OutputStream, simpleContractForm: SimpleContractForm, clientIP: String) {
        val agreementSignatureKey = simpleContractForm.generateSignatureKey()
        val contractSignature = ContractSignature(
            getZonedDateTime(),
            clientIP,
            agreementSignatureKey,
        )
        contractWriterService.createSimpleContract(stream, simpleContractForm, contractSignature)
    }

    open fun createDeclarationOfResidency(
        stream: OutputStream,
        accountRegisterData: AccountRegisterData,
        clientIP: String,
    ) {
        with(accountRegisterData) {
            val signature: DeclarationOfResidencySignature = generateDeclarationOfResidencySignature(this, clientIP)

            val declarationOfResidencyForm = DeclarationOfResidencyForm(
                fullName = getName(),
                document = formatDocument(getCPF()),
                fullAddress = address!!.toFullAddress(),
                city = address.city,
                federalUnity = address.state,
                zipCode = formatZipCode(address.zipCode),
                signature = signature,
            )

            stream.write(
                pdfConverter.convert(
                    handlebarTemplateCompiler.buildHtml(
                        declarationOfResidencyForm,
                    ).value,
                    false,
                ),
            )
        }
    }

    private fun generateDeclarationOfResidencySignature(
        accountRegisterData: AccountRegisterData,
        clientIP: String,
    ): DeclarationOfResidencySignature {
        val signatureDate = getZonedDateTime()
        return DeclarationOfResidencySignature(
            date = documentDateFormat.format(signatureDate),
            shortDate = shortDateFormat.format(signatureDate),
            email = accountRegisterData.emailAddress.value,
            phone = accountRegisterData.mobilePhone!!.msisdn,
            clientIP = clientIP,
            key = getContractSignature(accountRegisterData.toContractForm()),
        )
    }
}

fun Address.toFullAddress(): String {
    val fullStreetName = if (streetType != null) {
        "$streetType $streetName"
    } else {
        streetName
    }
    return "$fullStreetName${number?.let { ", $it" }.orEmpty()} ${complement.orEmpty()}".trim()
}

private fun DocumentType.toContractForm() = when (this) {
    DocumentType.CNH, DocumentType.CNHV2 -> "CNH"
    DocumentType.RG, DocumentType.NEWRG, is DocumentType.OTHER -> "RG"
    DocumentType.CPF -> "CPF"
    DocumentType.CNPJ -> "CNPJ"
}

fun AccountRegisterData.toContractForm() = ContractForm(
    documentInfo?.name.orEmpty(),
    documentInfo?.cpf.orEmpty(),
    address?.toFullAddress(),
    address?.neighborhood.orEmpty(),
    address?.city.orEmpty(),
    address?.state.orEmpty(),
    address?.zipCode.orEmpty(),
    documentInfo?.birthDate.formatOrEmpty(agreementDocumentDateFormatter),
    "${documentInfo?.birthCity.orEmpty()}, ${documentInfo?.birthState.orEmpty()}",
    documentInfo?.cnhNumber ?: documentInfo?.rg.orEmpty(),
    documentInfo?.docType?.toContractForm(),
    documentInfo?.expeditionDate.formatOrEmpty(agreementDocumentDateFormatter),
    documentInfo?.orgEmission.orEmpty(),
    documentInfo?.fatherName.orEmpty(),
    documentInfo?.motherName.orEmpty(),
    emailAddress.value,
    mobilePhone?.msisdn.orEmpty(),
    politicallyExposed?.selfDeclared,
    "",
    monthlyIncome.toText(),
    envToCommCentreTenant(),
)

fun ContractForm.generateSignatureKey(): String {
    val md = MessageDigest.getInstance("MD5")
    return BigInteger(1, md.digest(getObjectMapper().writeValueAsBytes(this))).toString(16).padStart(32, '0')
}

fun SimpleContractForm.generateSignatureKey(): String {
    val md = MessageDigest.getInstance("MD5")
    return BigInteger(1, md.digest(getObjectMapper().writeValueAsBytes(this))).toString(16).padStart(32, '0')
}

private fun MonthlyIncome?.toText(): String? {
    return when {
        this == null -> null
        lowerBound == 0L && upperBound == 2_000_00L -> "Até R$ 2.000,00"
        lowerBound == 2_000_01L && upperBound == 4_000_00L -> "Entre R$ 2.000,00 e R$ 4.000,00"
        lowerBound == 4_000_01L && upperBound == 10_000_00L -> "Entre R$ 4.000,00 e R$ 10.000,00"
        lowerBound == 10_000_01L && upperBound == 20_000_00L -> "Entre R$ 10.000,00 e R$ 20.000,00"
        lowerBound == 20_000_01L && upperBound == null -> "Acima de R$ 20.000,00"
        else -> throw IllegalArgumentException("$this is not a valid monthly income value")
    }
}