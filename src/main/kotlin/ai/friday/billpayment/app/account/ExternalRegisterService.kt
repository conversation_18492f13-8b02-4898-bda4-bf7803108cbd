package ai.friday.billpayment.app.account

import ai.friday.billpayment.app.bill.MoveAllBillsResult
import ai.friday.billpayment.app.bill.MoveBillService
import ai.friday.billpayment.app.dda.CreateDDARegister
import ai.friday.billpayment.app.dda.DDAStatus
import ai.friday.billpayment.app.fix
import ai.friday.billpayment.app.login.LoginService
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.app.metrics.AbstractFridayCountMetrics
import ai.friday.billpayment.app.metrics.metricRegister
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.log.andAppend
import java.util.concurrent.CompletableFuture
import java.util.concurrent.CompletionStage
import java.util.concurrent.atomic.AtomicInteger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import parallelMap

abstract class ExternalRegisterService(
    private val accountService: AccountService,
    private val walletService: WalletService,
    private val createDDARegister: CreateDDARegister?,
    private val loginService: LoginService,
    private val moveBillService: MoveBillService?,
) {
    // TODO REMOVE?
    open fun createAccountsAsync(commands: List<CreateAccountCommand>): CompletionStage<Pair<Int, Int>> {
        logger.info(Markers.append("status", "started"), "CreateAccounts")

        val successCounter = AtomicInteger()
        val errorCounter = AtomicInteger()

        val multipleSpacesRegex = "\\s+".toRegex()

        runBlocking(Dispatchers.IO) {
            commands
                .distinctBy { it.document }
                .parallelMap {
                    try {
                        createAccount(it.copy(name = it.name.trim().replace(multipleSpacesRegex, " ")))
                        successCounter.incrementAndGet()
                    } catch (ex: Exception) {
                        logger.error(Markers.append("input", it), "CreateAccounts", ex)
                        errorCounter.incrementAndGet()
                    }
                }
        }

        logger.info(
            Markers.append("status", "ended")
                .andAppend("size", commands.size)
                .andAppend("success", successCounter.get())
                .andAppend("failed", errorCounter.get()),
            "CreateAccounts",
        )

        return CompletableFuture.completedStage(Pair(successCounter.get(), errorCounter.get()))
    }

    fun createAccount(command: CreateAccountCommand): Account {
        val document = command.document.padStart(11, '0')
        val account = accountService.findAccountByDocumentOrNull(document)?.also { account ->
            command.externalId?.value?.let { externalId ->
                if (account.configuration.externalId?.value != externalId) {
                    metricRegister(CreateAccountMetric(), "result" to "FAIL", "reason" to "DOCUMENT_ALREADY_IN_USE", "channel" to command.channel)
                    throw UnassignableIdentifierException(externalId)
                }
            }
        } ?: createNewAccount(
            command.copy(
                mobilePhone = command.mobilePhone.fix(),
                document = document,
            ),
        )

        val markers = Markers.append("document", account.document)
            .andAppend("externalId", command.externalId)
            .andAppend("channel", command.channel)

        if (account.status == AccountStatus.ACTIVE) {
            logger.info(markers.andAppend("status", account.status), "CreateAccount")
            return account
        }

        val externalId = command.externalId!!

        val createdWallet = account.configuration.defaultWalletId?.let { walletService.findWallet(it) }
            ?: createPrimaryWallet(account, externalId.providerName)

        loginService.createLogin(
            providerUser = ProviderUser(
                id = externalId.value,
                providerName = command.providerName,
                username = account.name,
                emailAddress = account.emailAddress,
            ),
            accountId = account.accountId,
            role = Role.OWNER,
        )

        createDDARegister?.register(
            accountId = account.accountId,
            document = account.document,
            ddaStatus = if (command.ddaOptin) DDAStatus.PENDING else DDAStatus.CREATED,
            ddaProvider = command.ddaProvider,
        )

        if (command.importPastWallets) {
            try {
                addBillsFromPreviousWallet(createdWallet).also { markers.andAppend("addBillsFromPreviousWallet", it) }
            } catch (ex: Exception) {
                logger.error(markers, "CreateAccount#addBillsFromPreviousWallet", ex)
            }
        }

        logger.info(markers.andAppend("status", account.status), "CreateAccount")

        metricRegister(CreateAccountMetric(), "result" to "SUCCESS", "channel" to command.channel)

        return account
    }

    fun closeAccount(accountId: AccountId) { // TODO - deveria usar o CloseAccountService mas este não está pronto para clientes externos.
        accountService.attemptToUpdateAccountStatus(UpdateAccountStatusRequest.Close(accountId = accountId)).getOrThrow()
        loginService.remove(accountId)
    }

    private fun addBillsFromPreviousWallet(createdWallet: Wallet): MoveAllBillsResult? {
        val lastWalletId = accountService.findAllAccountsByDocument(createdWallet.founder.document)
            .filter { it.status == AccountStatus.CLOSED }
            .maxByOrNull { it.created }
            ?.defaultWalletId()
            ?: return null

        return moveBillService?.moveAllActiveBills(lastWalletId, createdWallet.id)
    }

    private fun createNewAccount(command: CreateAccountCommand): Account {
        if (command.externalId == null) {
            throw IllegalArgumentException("account should have an externalId")
        }
        if (accountService.checkAccountExists(command.externalId)) {
            throw IllegalStateException("user ${command.externalId} is already active")
        }

        val createdAccount = accountService.createAccount(command)
        val result = accountService.attemptToUpdateAccountStatus(UpdateAccountStatusRequest.Approve(accountId = createdAccount.accountId)).getOrThrow()

        return when (result) {
            UpdateAccountStatusResult.AccountNotFound -> throw IllegalStateException("Account id should exist")
            is UpdateAccountStatusResult.Unchanged -> result.account
            is UpdateAccountStatusResult.Updated -> result.account
        }
    }

    private fun createPrimaryWallet(account: Account, accoutProviderName: AccountProviderName): Wallet {
        val accountPaymentMethod = accountService.createExternalAccountPaymentMethod(
            account = account,
            providerName = accoutProviderName,
            position = 1,
        )

        val wallet = walletService.findWallets(account.accountId)
            .firstOrNull { it.checkPrimary(accountId = account.accountId) } ?: walletService.createPrimaryWallet(
            account,
            accountPaymentMethod.id,
        )

        accountService.updateAccountConfig(
            accountId = account.accountId,
            name = AccountConfigurationName.DEFAULT_WALLET_ID,
            value = wallet.id.value,
        )

        return wallet
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ExternalRegisterService::class.java)
    }
}

data class UnassignableIdentifierException(val id: String) : IllegalStateException("Identifier $id cannot be assigned")

class CreateAccountMetric : AbstractFridayCountMetrics()