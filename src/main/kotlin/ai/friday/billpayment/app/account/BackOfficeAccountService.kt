package ai.friday.billpayment.app.account

import ai.friday.billpayment.app.integrations.BackOfficeAccountRepository
import jakarta.inject.Singleton

@Singleton
class BackOfficeAccountService(
    private val backOfficeAccountRepository: BackOfficeAccountRepository,
) {
    fun updateStatusClosed(accountId: AccountId) {
        val account = backOfficeAccountRepository.findById(accountId)
        backOfficeAccountRepository.save(account.copy(status = AccountStatus.CLOSED))
    }

    fun closePartialAccount(accountId: AccountId) {
        backOfficeAccountRepository.updatePartialAccountStatus(accountId = accountId, newStatus = AccountStatus.CLOSED)
    }

    fun closeCreditCards(accountId: AccountId) {
        backOfficeAccountRepository.closeCreditCards(accountId)
    }

    fun closePaymentMethod(accountPaymentMethodId: AccountPaymentMethodId, accountId: AccountId) {
        backOfficeAccountRepository.closeAccountPaymentMethod(
            accountPaymentMethodId = accountPaymentMethodId,
            accountId = accountId,
        )
    }

    fun setAccountPaymentMethodStatus(accountPaymentMethodId: AccountPaymentMethodId, accountId: AccountId, status: AccountPaymentMethodStatus) {
        backOfficeAccountRepository.setAccountPaymentMethodStatus(
            accountPaymentMethodId = accountPaymentMethodId,
            accountId = accountId,
            status = status,
        )
    }
}