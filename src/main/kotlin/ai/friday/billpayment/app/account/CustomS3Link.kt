package ai.friday.billpayment.app.account

/**
 * Custom S3Link implementation to replace AWS DynamoDB S3Link.
 * Maintains compatibility with existing DynamoDB records that have the format:
 * {"s3":{"bucket":"bucket-name","key":"object-key","region":"us-east-1"}}
 */
data class CustomS3Link(
    val bucket: String,
    val key: String,
    val region: String,
) {
    /**
     * Convert to StoredObject for domain operations
     */
    fun toStoredObject(): StoredObject {
        return StoredObject(region = region, bucket = bucket, key = key)
    }

    companion object {
        /**
         * Create CustomS3Link from StoredObject
         */
        fun fromStoredObject(storedObject: StoredObject): CustomS3Link {
            return CustomS3Link(
                bucket = storedObject.bucket,
                key = storedObject.key,
                region = storedObject.region ?: "us-east-1", // default region if null
            )
        }

        /**
         * Create CustomS3Link directly from S3 components
         */
        fun create(region: String, bucket: String, key: String): CustomS3Link {
            return CustomS3Link(bucket = bucket, key = key, region = region)
        }
    }
}