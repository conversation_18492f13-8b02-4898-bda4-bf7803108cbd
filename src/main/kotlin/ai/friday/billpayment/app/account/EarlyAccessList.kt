package ai.friday.billpayment.app.account

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.wallet.WalletId

private typealias AccountNo = String

private val earlyAccess: List<Triple<AccountId, Document, AccountNo>> = listOf(
    Triple(
        AccountId("ACCOUNT-c933005c-b017-4db0-ab08-71afca18e840"),
        Document("***********"),
        "*********",
    ), // <EMAIL>
    Triple(
        AccountId("ACCOUNT-ce4d6c82-422a-4fb3-aa00-5c02110bb846"),
        Document("***********"),
        "*********",
    ), // <EMAIL>
    Triple(
        AccountId("ACCOUNT-07874a3a-140b-4083-9038-fe32377d883f"),
        Document("***********"),
        "*********",
    ), // <EMAIL>
    Triple(
        AccountId("ACCOUNT-532b91c2-a2e2-4dd7-ac93-f236bc8284fd"),
        Document("***********"),
        "*********",
    ), // <EMAIL>
    Triple(
        AccountId("ACCOUNT-ecafe3dc-eaca-42b2-8a50-f65d00bb5ea1"),
        Document("***********"),
        "*********",
    ), // <EMAIL>
    Triple(
        AccountId("ACCOUNT-e414062f-b8e4-4eb5-9af5-820f88fd1987"),
        Document("***********"),
        "*********",
    ), // <EMAIL>
    Triple(
        AccountId("ACCOUNT-8e8d8d4c-0a3f-4d4f-ac30-2025954cc9e8"),
        Document("***********"),
        "313094",
    ), // <EMAIL>,
    Triple(
        AccountId("ACCOUNT-720e08b0-6e89-4af6-b2ab-6f4e73319ce2"),
        Document("***********"),
        "*********",
    ), // <EMAIL>,
    Triple(
        AccountId("ACCOUNT-272d4f12-f389-4a9c-a13d-705745a7ad54"),
        Document("***********"),
        "*********",
    ), // <EMAIL>,
    Triple(
        AccountId("ACCOUNT-1bb050f6-556b-46b9-9761-bf39e1409519"),
        Document("***********"),
        "*********",
    ), // <EMAIL>,
    Triple(
        AccountId("ACCOUNT-55e7e44e-bdc0-4662-ac06-a23bc9612ee6"),
        Document("***********"),
        "*********",
    ), // <EMAIL>,
    Triple(
        AccountId("ACCOUNT-49099ed8-c5df-41d0-8de6-54688da369ec"),
        Document("***********"),
        "*********",
    ), // rafael - <EMAIL>
    Triple(
        AccountId("ACCOUNT-d98b2459-40e4-4acf-8e64-f6d7cfba25a3"),
        Document("***********"),
        "*********",
    ), // <EMAIL>,
    Triple(
        AccountId("ACCOUNT-2decea75-b7ce-47cf-b261-94b5b3b36cfa"),
        Document("***********"),
        "*********",
    ), // lamas
    Triple(
        AccountId("ACCOUNT-a0a9714e-649e-485a-9422-24a9ee40bcd9"),
        Document("***********"),
        "*********",
    ), // <EMAIL>,
    Triple(
        AccountId("ACCOUNT-a2e87c5c-8d84-44e2-ae3c-f5e68de20818"),
        Document("***********"),
        "313078",
    ), // Felipe Castro
    Triple(
        AccountId("ACCOUNT-8a6a55cb-e31c-45be-a220-e09779d67fe4"),
        Document("***********"),
        "*********",
    ), // Pedro Gomes

    Triple(
        AccountId("ACCOUNT-d7ea365e-2f85-4b3f-9afa-cf38c76729b3"),
        Document("***********"),
        "*********",
    ), // Zanaca

    Triple(
        AccountId("ACCOUNT-3545c39b-3c0e-416e-b661-ba67db157963"),
        Document("***********"),
        "*********",
    ), // Breno

    Triple(
        AccountId("ACCOUNT-f567096b-df3b-49c8-81c6-5063fe381fd1"),
        Document("***********"),
        "*********",
    ), // Fernando

    Triple(
        AccountId("ACCOUNT-e76d01fd-108f-43cd-a79f-8ebdfe04f1e7"),
        Document("***********"),
        "*********",
    ), // Antunes

    // developer early access soh quem esta daqui pra cima, e precisa atualizar o earlyAccessAccounts.take(X)
    Triple(
        AccountId("ACCOUNT-6f53871b-8092-4359-b1a2-4679c3f805d5"),
        Document("***********"),
        "*********",
    ), // <EMAIL>
    Triple(
        AccountId("ACCOUNT-3dc5eb06-71c3-4442-8f9c-3ae65a862612"),
        Document("***********"),
        "313060",
    ), // Clecio
    Triple(
        AccountId("ACCOUNT-199b6ebd-01b1-4ab1-8aca-9a4c8cec7c4a"),
        Document("***********"),
        "*********",
    ), // Bruno
    Triple(
        AccountId("ACCOUNT-50395c5e-8bb8-4409-a811-438aa88d94cb"),
        Document("***********"),
        "*********",
    ), // Gabriela
)

internal val earlyAccessAccounts: List<AccountId> = earlyAccess.map { (accountId, _, _) -> accountId }
internal val earlyAccessDocuments: List<Document> = earlyAccess.map { (_, document, _) -> document }
internal val earlyAccessBankAccounts: List<String> = earlyAccess.map { (_, _, accountNo) -> accountNo.trimStart('0') }

@Deprecated("Preferir utilizar o groups que está no configuration de cada usuário")
internal fun AccountId.hasEarlyAccess() = this in earlyAccessAccounts

@Deprecated("Preferir utilizar o groups que está no configuration de cada usuário")
fun AccountId.hasDeveloperEarlyAccess() = this in earlyAccessAccounts.take(21)

@Deprecated("deveria realmente ser por walletId, hoje funciona somente para as cateiras principais")
internal fun WalletId.hasEarlyAccess() = AccountId(this.value).hasEarlyAccess()

@Deprecated("deveria realmente ser por walletId, hoje funciona somente para as cateiras principais")
internal fun Document.hasEarlyAccess() = this in earlyAccessDocuments

@Deprecated("Preferir utilizar o groups que está no configuration de cada usuário")
internal fun String.hasDeveloperEarlyAccess() = this.trimStart('0') in earlyAccessBankAccounts.take(15)