package ai.friday.billpayment.app.account

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.documentscan.DocumentScanId
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.liveness.LivenessEnrollmentVerification
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.register.RiskAnalysisFailedReason
import ai.friday.billpayment.app.register.recoverable
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import com.fasterxml.jackson.annotation.JsonTypeInfo
import java.time.LocalDate
import java.time.ZonedDateTime

data class Address(
    val streetType: String?,
    val streetName: String,
    val number: String?,
    val complement: String?,
    val neighborhood: String,
    val city: String,
    val state: String,
    val zipCode: String,
)

data class DocumentInfo(
    val name: String,
    val cpf: String,
    val birthDate: LocalDate?,
    val fatherName: String,
    val motherName: String,
    val rg: String,
    val docType: DocumentType,
    val cnhNumber: String?,
    val orgEmission: String,
    val expeditionDate: LocalDate?,
    val birthCity: String,
    val birthState: String,
) {
    fun isComplete(): Boolean {
        return name.isNotEmpty() &&
            cpf.isNotEmpty() &&
            birthDate != null &&
            motherName.isNotEmpty() &&
            (!cnhNumber.isNullOrBlank() || rg.isNotEmpty()) &&
            orgEmission.isNotEmpty() &&
            birthCity.isNotEmpty() &&
            birthState.isNotEmpty() &&
            expeditionDate != null
    }
}

data class DocumentInfoUserInput(
    val name: String,
    val cpf: String,
    val birthDate: LocalDate,
    val fatherName: String,
    val motherName: String,
    val birthCity: String,
    val birthState: String,
    val orgEmission: String,
    val cnhNumber: String?,
    val rgNumber: String?,
    val expeditionDate: LocalDate,
)

data class UploadedDocumentImages(
    val front: StoredObject,
    val documentType: DocumentType,
    val back: StoredObject?,
)

data class DocumentScan(
    val documentType: DocumentType,
    val documentScanId: DocumentScanId,
)

data class AccountRegisterData(
    val accountId: AccountId,
    val emailAddress: EmailAddress,
    val emailVerified: Boolean = false,
    val emailTokenExpiration: Long? = null,
    val nickname: String,
    val mobilePhone: MobilePhone? = null,
    val mobilePhoneVerified: Boolean = false,
    val mobilePhoneTokenExpiration: Long? = null,
    val created: ZonedDateTime,
    val lastUpdated: ZonedDateTime,
    val upgradeStarted: ZonedDateTime? = null,
    val documentInfo: DocumentInfo?,
    val calculatedGender: Gender?,
    val isDocumentEdited: Boolean,
    @Deprecated("Com o scanner de documentos, a imagem fica salva no DocumentScantRepository")
    val uploadedCNH: StoredObject? = null,
    @Deprecated("Com o scanner de documentos, a imagem fica salva no DocumentScantRepository")
    val uploadedDocument: UploadedDocumentImages? = null,
    val livenessId: LivenessId? = null,
    val uploadedSelfie: StoredObject? = null,
    val uploadedSelfieDateTime: ZonedDateTime? = null,
    val address: Address? = null,
    val monthlyIncome: MonthlyIncome? = null,
    val politicallyExposed: PoliticallyExposed? = null,
    val agreementData: AgreementData? = null,
    val upgradeAgreementData: AgreementData? = null,
    val kycFile: StoredObject? = null,
    val openForUserReview: Boolean,
    val openedForUserReviewAt: ZonedDateTime?,
    val externalId: ExternalId? = null,
    val livenessEnrollmentVerification: LivenessEnrollmentVerification = LivenessEnrollmentVerification(),
    val riskAnalysisFailedReasons: List<RiskAnalysisFailedReason>?,
    val riskAnalysisFailedReasonsHistory: List<RiskAnalysisFailedReason>?,
    val document: Document? = null,
    val birthDate: LocalDate? = null,
    val identityValidationStatus: IdentityValidationStatus? = null,
    val identityValidationPercentage: Double? = null,
    val registrationType: RegistrationType,
    val fraudListMatch: Boolean,
    val accountClosureDetails: AccountClosureDetails? = null,
    val clientIP: String? = null,
    val documentScan: DocumentScan? = null,
) {
    val accountRecoverable: Boolean? = riskAnalysisFailedReasons?.recoverable()

    val hasUploadedDocument: Boolean = (uploadedCNH != null || (uploadedDocument?.front != null && uploadedDocument.back != null)) ||
        (documentScan != null && documentInfo != null && documentInfo.isComplete())

    val shouldEnableCreditCard: Boolean = uploadedCNH != null || (hasUploadedDocument && documentScan?.documentType == DocumentType.CNH)
}

enum class RegistrationType {
    BASIC, FULL, UPGRADED
}

enum class AccountClosureReason {
    /**
     * Somente por solicitação do usuário.
     */
    USER_REQUEST,

    /**
     * Somente por fraude necessariamente confirmada, e não apenas por suspeita.
     * Esse tipo de marcação cadastra o rosto do usuário na nossa lista de fraudadores.
     */
    FRAUD,

    /**
     * Por suspeita de fraude, quando não há certeza que houve uma fraude cometida.
     * O rosto do usuário não entra na nossa lista de fraudadores.
     */
    POSSIBLE_FRAUD,

    /**
     * Por inatividade do cadastro ou da conta.
     */
    INACTIVE,

    /**
     * Por não ter passado na análise de risco durante a esteira de cadastro.
     * Em alguns casos, a rejeição na esteira é por fraude confirmada. Nesses casos, é usado o [FRAUD].
     */
    RISK_ANALYSIS,

    /**
     * Por inadimplencia.
     */
    SUBSCRIPTION_OVERDUE,
}

@JsonTypeInfo(use = JsonTypeInfo.Id.MINIMAL_CLASS)
sealed class AccountClosureDetails private constructor() {
    data class WithReason(val reason: AccountClosureReason, val at: ZonedDateTime, val description: String?) :
        AccountClosureDetails()

    data class WithoutReason(val description: String, val at: ZonedDateTime) :
        AccountClosureDetails()

    companion object {
        fun create(
            reason: AccountClosureReason?,
            description: String?,
            at: ZonedDateTime = getZonedDateTime(),
        ): AccountClosureDetails {
            return if (reason != null) {
                WithReason(reason = reason, description = description, at = at)
            } else {
                WithoutReason(description = description!!, at = at)
            }
        }
    }
}

fun AccountClosureDetails.isFraud(): Boolean {
    return this is AccountClosureDetails.WithReason && this.reason == AccountClosureReason.FRAUD
}

fun AccountClosureDetails.isPossibleFraud(): Boolean {
    return this is AccountClosureDetails.WithReason && this.reason == AccountClosureReason.POSSIBLE_FRAUD
}

fun AccountClosureDetails.isSubscriptionOverdue(): Boolean {
    return this is AccountClosureDetails.WithReason && this.reason == AccountClosureReason.SUBSCRIPTION_OVERDUE
}

fun RegistrationType.toUserAccountType(): UserAccountType {
    return when (this) {
        RegistrationType.BASIC -> UserAccountType.BASIC_ACCOUNT
        RegistrationType.FULL, RegistrationType.UPGRADED -> UserAccountType.FULL_ACCOUNT
    }
}

enum class IdentityValidationStatus {
    CHECKED, REJECTED, UNKNOWN
}

enum class Gender {
    M, F
}

fun AccountRegisterData.identityAlreadyValidated(): Boolean {
    return identityValidationStatus != null
}

fun AccountRegisterData.getCPF(): String? {
    return if (this.documentInfo?.cpf != null) {
        this.documentInfo.cpf
    } else {
        this.document?.value
    }
}

fun AccountRegisterData.getName(): String {
    return if (this.documentInfo?.name != null) {
        this.documentInfo.name
    } else {
        this.nickname
    }
}

fun AccountRegisterData.getBirthdate(): LocalDate? {
    return if (this.documentInfo?.birthDate != null) {
        this.documentInfo.birthDate
    } else {
        this.birthDate
    }
}

fun AccountRegisterData.readyForAgreement(): Boolean {
    return mobilePhone != null &&
        mobilePhoneVerified &&
        documentInfo != null &&
        documentInfo.isComplete() &&
        hasUploadedDocument &&
        uploadedSelfie != null &&
        address != null &&
        monthlyIncome != null &&
        politicallyExposed != null
}

data class AgreementData(
    val acceptedAt: ZonedDateTime?,
    val userContractFile: StoredObject,
    val userContractSignature: String,
    val declarationOfResidencyFile: StoredObject? = null,
)

data class StoredObject(
    val region: String?,
    val bucket: String,
    val key: String,
) {
    companion object {
        fun fromS3Path(region: String, s3Path: String): StoredObject {
            s3Path.removePrefix("s3://").split("/", limit = 2).let { (bucket, key) ->
                return StoredObject(region = region, bucket = bucket, key = key)
            }
        }
    }
}

data class MonthlyIncome(
    val lowerBound: Long,
    val upperBound: Long?,
)

data class PoliticallyExposed(
    val selfDeclared: Boolean,
    val query: PepQuery?,
) {
    val isExposed =
        selfDeclared || (query != null && query.result.isExposed())
}

data class PepQuery(
    val at: ZonedDateTime,
    val result: PepQueryResult,
)

enum class PepQueryResult {
    /**
     * O documento (CPF) não consta em listas e não possui relacionamento com pessoas cujos documentos constem em listas oficiais.
     */
    NOT_PEP,

    /**
     * O documento (CPF) consta como PEP em listas oficiais.
     */
    DIRECT_PEP,

    /**
     * O documento (CPF) possui algum relacionamento com pessoas cujos documentos constam em listas oficiais.
     */
    RELATED_TO_PEP,
}

fun PepQueryResult.isExposed(): Boolean {
    return this == PepQueryResult.DIRECT_PEP || this == PepQueryResult.RELATED_TO_PEP
}