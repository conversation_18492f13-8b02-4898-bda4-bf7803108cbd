package ai.friday.billpayment.app.account

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.adapters.arbi.ArbiAccountMissingPermissionsException
import ai.friday.billpayment.adapters.arbi.ArbiAdapterException
import ai.friday.billpayment.adapters.arbi.ArbiInvalidAccountException
import ai.friday.billpayment.adapters.arbi.ArbiInvalidBalanceException
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.ExternalBankAccount
import ai.friday.billpayment.app.banking.ExternalBankAccountService
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.liveness.LivenessErrors
import ai.friday.billpayment.app.liveness.LivenessService
import ai.friday.billpayment.app.login.LoginService
import ai.friday.billpayment.app.onboarding.OnboardingTestPixService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.statement.StatementError
import ai.friday.billpayment.app.statement.StatementService
import ai.friday.billpayment.app.subscription.SubscriptionPaymentStatus
import ai.friday.billpayment.app.subscription.SubscriptionService
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.WalletType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnore
import io.micronaut.context.annotation.Property
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@FridayMePoupe
open class CloseAccountService(
    private val accountService: AccountService,
    private val accountRegisterService: AccountRegisterService,
    private val accountRegisterRepository: AccountRegisterRepository,
    private val externalAccountService: ExternalBankAccountService,
    private val onboardingTestPixService: OnboardingTestPixService,
    private val walletService: WalletService,
    private val backOfficeAccountService: BackOfficeAccountService,
    private val loginService: LoginService,
    private val transactionRepository: TransactionRepository,
    private val balanceService: BalanceService,
    private val crmService: CrmService,
    private val subscriptionService: SubscriptionService,
    private val livenessService: LivenessService,
    private val statementService: StatementService,
    private val closeAccountStepExecutors: List<CloseAccountStepExecutor<*>>,
    private val messagePublisher: MessagePublisher,
    private val closeAccountStepDiscovery: List<CloseAccountStepDiscovery>,
    private val closeWalletStepDiscovery: List<CloseWalletStepDiscovery>,
    @Property(name = "sqs.closeAccountQueueName") private val closeAccountQueueName: String,
) {
    open fun closeAccount(
        accountId: AccountId,
        closureDetails: AccountClosureDetails,
        delaySeconds: Int = 0,
    ): Either<CloseAccountError, CloseAccountResult> {
        val logName = "closeAccount"
        val markers = append("accountId", accountId.value)
            .andAppend("closureDetails", closureDetails)
            .andAppend("delaySeconds", delaySeconds)

        val steps = prepareClose(accountId, closureDetails).getOrElse {
            markers.andAppend("error", it)
            logger.warn(markers, logName)
            return it.left()
        }
        markers.andAppend("steps", steps)

        val result = if (delaySeconds == 0) {
            executeCloseAccountStepsWithRetry(accountId, steps, 1)
        } else {
            messagePublisher.sendMessage(
                closeAccountQueueName,
                CloseAccountMessage(
                    accountId = accountId,
                    steps = steps,
                    tryCount = 1,
                ),
                delaySeconds = delaySeconds,
            )
            CloseAccountResult(steps)
        }
        markers.andAppend("result", result)

        logger.info(markers, logName)
        return result.right()
    }

    open fun closeSecondaryWallet(wallet: Wallet): Either<CloseAccountStepError, CloseAccountResult> {
        if (wallet.type == WalletType.PRIMARY) {
            return CloseAccountStepError.GenericError("Cannot close primary wallet").left()
        }

        val steps = mutableListOf<CloseAccountStep>()

        val closeFounderWalletCloseAccountStep = wallet.prepareClose(AccountClosureDetails.create(null, "fechamento de carteira secundária"), false).getOrElse {
            return CloseAccountStepError.GenericError(it.toString()).left()
        }

        steps.add(closeFounderWalletCloseAccountStep)

        return executeCloseAccountStepsWithRetry(wallet.founder.accountId, steps, 1).right()
    }

    open fun closeWalletPJ(wallet: Wallet): Either<CloseAccountStepError, CloseAccountResult> {
        val account = accountService.findAccountById(wallet.founder.accountId)
        if (!account.isLegalPerson()) {
            return CloseAccountStepError.GenericError("Cannot close wallet for natural person").left()
        }

        val steps = mutableListOf<CloseAccountStep>()

        val closeFounderWalletCloseAccountStep = wallet.prepareClosePJ(AccountClosureDetails.create(null, "fechamento de carteira PJ"), false).getOrElse {
            return CloseAccountStepError.GenericError(it.toString()).left()
        }

        steps.add(closeFounderWalletCloseAccountStep)

        return executeCloseAccountStepsWithRetry(wallet.founder.accountId, steps, 1).right()
    }

    open fun executeCloseAccountStepsWithRetry(
        accountId: AccountId,
        steps: List<CloseAccountStep>,
        tryCount: Int,
    ): CloseAccountResult {
        val logName = "executeCloseAccountStepsWithRetry"
        val markers = append("accountId", accountId.value)
            .andAppend("steps", steps)
            .andAppend("tryCount", tryCount)

        val result = executeCloseAccountSteps(
            accountId = accountId,
            steps = steps,
        ).map {
            markers.andAppend("result", it)
            it.steps
        }.getOrElse {
            markers.andAppend("error", it)
            it.steps
        }

        val shouldRetry = result.any { it.status in listOf(CloseAccountStepStatus.Pending, CloseAccountStepStatus.Error) }
        markers.andAppend("shouldRetry", shouldRetry)

        result.firstOrNull {
            it.status == CloseAccountStepStatus.Error
        }?.let {
            markers.andAppend("errorMessage", it.errorMessage)
        }

        if (shouldRetry) {
            messagePublisher.sendMessage(
                closeAccountQueueName,
                CloseAccountMessage(
                    accountId = accountId,
                    steps = result,
                    tryCount = tryCount + 1,
                ),
                delaySeconds = 900,
            )

            logger.warn(markers, logName)
        } else {
            logger.info(markers, logName)
        }

        return CloseAccountResult(
            result,
        )
    }

    private fun executeCloseAccountSteps(
        accountId: AccountId,
        steps: List<CloseAccountStep>,
    ): Either<ExecuteCloseAccountStepsError, CloseAccountResult> {
        val logName = "executeCloseAccountSteps"
        val markers = append("accountId", accountId.value).andAppend("steps", steps)
        try {
            val account = accountService.findAccountById(accountId)

            steps.forEach { step ->
                if (step.status in listOf(CloseAccountStepStatus.Pending, CloseAccountStepStatus.Error)) {
                    val executor = closeAccountStepExecutors.singleOrNull { it.stepType == step.type } ?: throw IllegalStateException("executor not found for step ${step.type}")

                    val result = when (step) {
                        is SimpleCloseAccountStep -> (executor as SimpleCloseAccountStepExecutor).execute(account, step)
                        is CloseFounderWalletCloseAccountStep -> (executor as CloseFounderWalletStepExecutor).execute(account, step)
                        is DeactivateAccountRegisterStep -> (executor as DeactivateAccountRegisterStepExecutor).execute(account, step)
                    }

                    result.map {
                        step.status = it
                        step.errorMessage = null
                    }.getOrElse {
                        step.status = CloseAccountStepStatus.Error
                        step.errorMessage = when (it) {
                            is CloseAccountStepError.GenericError -> it.error
                            is CloseAccountStepError.GenericException -> it.message
                        }

                        markers.andAppend("error", it)
                        logger.warn(markers, logName)
                        return CloseAccountResult(steps).right()
                    }
                }
            }

            logger.info(markers, logName)
            return CloseAccountResult(steps).right()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return ExecuteCloseAccountStepsError(e, steps).left()
        }
    }

    internal fun prepareClose(
        accountId: AccountId,
        closureDetails: AccountClosureDetails,
    ): Either<CloseAccountError, List<CloseAccountStep>> {
        val logName = "prepareClose"
        val markers = append("accountId", accountId.value).andAppend("closureDetails", closureDetails)

        val account = accountService.findAccountById(accountId)
        markers.andAppend("account", account)

        if (!account.isOpen() && account.status != AccountStatus.PENDING_CLOSE) {
            logger.warn(markers, logName)
            return CloseAccountError.InvalidAccountStatus(account.status).left()
        }

        val steps = mutableListOf<CloseAccountStep>()

        val subscription = subscriptionService.findOrNull(accountId)

        if (subscription != null && account.subscriptionType == SubscriptionType.PIX) {
            steps.add(CloseAccountStepTypeUnsubscribe.toSimpleStep())
        }

        steps.add(CloseAccountStepTypeRemoveFromDDA.toSimpleStep())

        val founderWallets = walletService.findWallets(accountId).filter { it.founder.accountId == accountId }
        markers.andAppend("founderWallets", founderWallets)

        founderWallets.forEach { wallet ->
            val shouldCaptureSubscriptionFee = subscription?.walletId == wallet.id && subscription.paymentStatus == SubscriptionPaymentStatus.OVERDUE

            wallet.prepareClose(closureDetails, shouldCaptureSubscriptionFee).map {
                steps.add(it)
            }.getOrElse {
                markers.andAppend("error", it)
                logger.warn(markers, logName)
                return CloseAccountError.CloseFounderWalletError(error = it).left()
            }
        }

        val isPJ = account.document.length == 14
        markers.andAppend("PJ", isPJ)

        if (!isPJ) {
            steps.add(CloseAccountStepTypeSignOutUser.toSimpleStep())
            steps.add(CloseAccountStepTypeRemoveFromUserPool.toSimpleStep())
            steps.add(CloseAccountStepTypeRemoveLogin.toSimpleStep())
        }
        steps.add(CloseAccountStepTypeDisableNotification.toSimpleStep())
        steps.add(CloseAccountStepTypeUpdateStatusClosed.toSimpleStep())
        steps.add(CloseAccountStepTypeRemoveFromWallets.toSimpleStep())
        steps.add(CloseAccountStepTypeCloseCreditCards.toSimpleStep())
        steps.add(DeactivateAccountRegisterStep(closureDetails))
        steps.add(CloseAccountStepTypeCloseCrmContact.toSimpleStep())
        if (!isPJ && closureDetails is AccountClosureDetails.WithReason && closureDetails.reason == AccountClosureReason.FRAUD) {
            steps.add(CloseAccountStepTypeMarkAsFraud.toSimpleStep())
        }

        return withAccountDiscoverySteps(accountId, closureDetails, steps, markers).also {
            logger.info(markers, logName)
        }
    }

    open fun confirmFraud(accountRegister: AccountRegisterData): Either<LivenessErrors, Unit> {
        accountRegisterRepository.save(accountRegister.copy(accountClosureDetails = AccountClosureDetails.create(AccountClosureReason.FRAUD, "Fraude confirmada")))

        val result = livenessService.markAsFraud(accountRegister.accountId).getOrElse {
            return it.left()
        }

        return result.right()
    }

    private fun Wallet.hasDebitToday(): Either<CloseWalletError.Unknown, Boolean> {
        val statement = statementService.findAllStatementsByDate(
            walletId = id,
            startDate = getLocalDate(),
            endDate = getLocalDate(),
        ).getOrElse { statementError ->
            return when (statementError) {
                StatementError.AccountMissingPermissionError -> false.right()
                StatementError.FailedToSendEmail,
                StatementError.NotAllowed,
                StatementError.ServerError,
                StatementError.WalletNotFound,
                -> CloseWalletError.Unknown(exception = statementError).left()
            }
        }
        return statement.statementItems.any { statementItem -> statementItem.flow == BankStatementItemFlow.DEBIT }.right()
    }

    private fun Wallet.hasCredit(): Either<CloseWalletError.Unknown, Boolean> {
        val credits = statementService.findAllCredits(
            walletId = id,
            walletMember = founder,
        ).getOrElse { statementError ->
            return CloseWalletError.Unknown(exception = statementError).left()
        }

        return credits.isNotEmpty().right()
    }

    private fun Wallet.prepareClose(closureDetails: AccountClosureDetails, shouldCaptureSubscriptionFee: Boolean): Either<CloseWalletError, CloseFounderWalletCloseAccountStep> {
        val logName = "Wallet.prepareClose"
        val markers = append("walletId", id.value)
            .andAppend("closureDetails", closureDetails)
            .andAppend("shouldCaptureSubscriptionFee", shouldCaptureSubscriptionFee)

        val accountPaymentMethod =
            accountService.findAccountPaymentMethodByIdAndAccountId(
                accountPaymentMethodId = paymentMethodId,
                accountId = founder.accountId,
            )
        markers.andAppend("accountPaymentMethod", accountPaymentMethod)

        if (transactionRepository.hasTransactionByWalletAndStatus(id, TransactionStatus.PROCESSING)) {
            logger.warn(markers, logName)
            return CloseWalletError.TransactionStillProcessing(id).left()
        }

        val stepTypes = mutableListOf<CloseWalletStepType>()

        stepTypes.add(CloseWalletStepTypeIgnoreRecurrences)
        stepTypes.add(CloseWalletStepTypeCancelAllSchedules)
        stepTypes.add(CloseWalletStepTypeDeleteWalletPixKey)
        stepTypes.add(CloseWalletStepTypeDeleteCustomPixKeys)
        stepTypes.add(CloseWalletStepTypeDisconnectUtilityAccounts)
        stepTypes.add(CloseWalletStepTypeDeleteOldPixKey)

        val balance = getBalanceOrNull(accountPaymentMethod)
        markers.andAppend("balance", balance)

        val cannotCloseExternalAccountNow = when {
            balance == null || balance == 0L -> {
                hasDebitToday().getOrElse {
                    markers.andAppend("error", it)
                    logger.warn(markers, logName)
                    return it.left()
                }
            }

            closureDetails.isFraud() || closureDetails.isPossibleFraud() || shouldCaptureSubscriptionFee -> {
                stepTypes.add(CloseWalletStepTypeTransferAllFundsFromAccount)
                true
            }

            else -> {
                if (hasCredit().getOrElse { return it.left() }) {
                    return try {
                        val externalBankAccount = externalAccountService.findLastUsed(Document(this.founder.document))

                        markers.andAppend("externalBankAccount", externalBankAccount)

                        val pixKey = onboardingTestPixService.findPixKey(accountId = this.founder.accountId).getOrThrow()

                        markers.andAppend("pixKey", pixKey)

                        logger.info(markers, logName)
                        CloseWalletError.BalanceDifferentThanZero(
                            walletId = id,
                            balance = balance,
                            externalBankAccount = externalBankAccount,
                            pixKey = pixKey,
                        )
                    } catch (e: Exception) {
                        logger.error(markers, logName, e)
                        CloseWalletError.Unknown(e)
                    }.left()
                } else {
                    stepTypes.add(CloseWalletStepTypeRefundOnboardingTestPix)
                }
                true
            }
        }

        if (accountPaymentMethod.shouldCloseExternalAccount()) {
            if (cannotCloseExternalAccountNow) {
                stepTypes.add(CloseWalletStepTypeCloseExternalAccountLater)
            } else {
                stepTypes.add(CloseWalletStepTypeCloseExternalAccountNow)
            }
        }

        stepTypes.add(CloseWalletStepTypeCloseAndRemoveMembers)

        markers.andAppend("stepTypes", stepTypes)

        val walletSteps = stepTypes.map {
            it.toStep()
        }.toMutableList()

        return withWalletDiscoverySteps(closureDetails, walletSteps, markers).also {
            logger.info(markers, logName)
        }
    }

    private fun Wallet.prepareClosePJ(closureDetails: AccountClosureDetails, shouldCaptureSubscriptionFee: Boolean): Either<CloseWalletError, CloseFounderWalletCloseAccountStep> {
        val logName = "Wallet.prepareClosePj"
        val markers = append("walletId", id.value)
            .andAppend("closureDetails", closureDetails)
            .andAppend("shouldCaptureSubscriptionFee", shouldCaptureSubscriptionFee)

        if (transactionRepository.hasTransactionByWalletAndStatus(id, TransactionStatus.PROCESSING)) {
            logger.warn(markers, logName)
            return CloseWalletError.TransactionStillProcessing(id).left()
        }

        val stepTypes = mutableListOf<CloseWalletStepType>()

        stepTypes.add(CloseWalletStepTypeIgnoreRecurrences)
        stepTypes.add(CloseWalletStepTypeCancelAllSchedules)
        stepTypes.add(CloseWalletStepTypeDeleteWalletPixKey)
        stepTypes.add(CloseWalletStepTypeDeleteCustomPixKeys)
        stepTypes.add(CloseWalletStepTypeDisconnectUtilityAccounts)
        stepTypes.add(CloseWalletStepTypeDeleteOldPixKey)

        stepTypes.add(CloseWalletStepTypeCloseAndRemoveMembers)

        markers.andAppend("stepTypes", stepTypes)

        val walletSteps = stepTypes.map {
            it.toStep()
        }.toMutableList()

        return withWalletDiscoverySteps(closureDetails, walletSteps, markers).also {
            logger.info(markers, logName)
        }
    }

    private fun withAccountDiscoverySteps(
        accountId: AccountId,
        closureDetails: AccountClosureDetails,
        steps: MutableList<CloseAccountStep>,
        markers: LogstashMarker,
    ): Either<CloseAccountError, MutableList<CloseAccountStep>> {
        closeAccountStepDiscovery.forEach { current ->
            val currentSteps = current.prepareClose(accountId, closureDetails).getOrElse { closeAccountError ->
                markers.andAppend("closeAccountError", closeAccountError)
                return closeAccountError.left()
            }
            steps.addAll(currentSteps)
        }

        markers.andAppend("steps", steps)
        return steps.right()
    }

    private fun Wallet.withWalletDiscoverySteps(
        closureDetails: AccountClosureDetails,
        walletSteps: MutableList<CloseWalletStep>,
        markers: LogstashMarker,
    ): Either<CloseWalletError, CloseFounderWalletCloseAccountStep> {
        closeWalletStepDiscovery.forEach { current ->
            val currentSteps = current.prepareCloseWallet(wallet = this, closureDetails).getOrElse { closeWalletError ->
                markers.andAppend("closeWalletError", closeWalletError)
                return closeWalletError.left()
            }
            walletSteps.addAll(currentSteps)
        }

        markers.andAppend("walletSteps", walletSteps)
        return CloseFounderWalletCloseAccountStep(
            walletId = id,
            steps = walletSteps,
        ).right()
    }

    private fun AccountPaymentMethod.shouldCloseExternalAccount(): Boolean {
        return status != AccountPaymentMethodStatus.CLOSED && method is InternalBankAccount && method.bankAccountMode == BankAccountMode.PHYSICAL
    }

    open fun closePartialAccount(
        accountId: AccountId,
        closureDetails: AccountClosureDetails,
    ): Either<ClosePartialAccountError, ClosePartialAccountResult> {
        try {
            accountService.findPartialAccountById(accountId)

            loginService.remove(accountId)

            backOfficeAccountService.closePartialAccount(accountId)
            deactivateAccountRegister(accountId, closureDetails).getOrElse {
                return when (it) {
                    is DeactivateAccountError.MarkAsFraud -> ClosePartialAccountError.MarkAsFraud(it.error).left()
                    is DeactivateAccountError.Unknown -> ClosePartialAccountError.Unknown(it.e).left()
                }
            }

            return ClosePartialAccountResult(
                removedFromUserPool = false,
            ).right()
        } catch (e: Exception) {
            return ClosePartialAccountError.Unknown(e).left()
        }
    }

    fun deletePartialTestAccount(accountId: AccountId): Either<ClosePartialAccountError, ClosePartialAccountResult> {
        return try {
            if (accountRegisterService.checkIsTestAccount(accountId)) {
                closePartialAccount(
                    accountId,
                    AccountClosureDetails.create(
                        reason = null,
                        description = "Cadastro encerrado por ser conta de teste",
                    ),
                )
            } else {
                ClosePartialAccountError.AccountIsNotUnderTest.left()
            }
        } catch (e: Exception) {
            ClosePartialAccountError.Unknown(e).left()
        }
    }

    open fun deactivateAccountRegister(
        accountId: AccountId,
        closureDetails: AccountClosureDetails,
    ): Either<DeactivateAccountError, Unit> {
        return try {
            accountRegisterRepository.deactivate(accountId, closureDetails)
            crmService.removeContactAsync(accountId)

            if (closureDetails is AccountClosureDetails.WithReason && closureDetails.reason == AccountClosureReason.FRAUD) {
                livenessService.markAsFraud(accountId).getOrElse {
                    return DeactivateAccountError.MarkAsFraud(it).left()
                }
            }
            Unit.right()
        } catch (e: Exception) {
            DeactivateAccountError.Unknown(e).left()
        }
    }

    private fun getBalanceOrNull(accountPaymentMethod: AccountPaymentMethod): Long? {
        return try {
            balanceService.getBalanceFrom(
                accountId = accountPaymentMethod.accountId,
                accountPaymentMethodId = accountPaymentMethod.id,
                accountPaymentMethodStatus = null,
            ).amount
        } catch (e: ArbiInvalidAccountException) {
            null
        } catch (_: ArbiAccountMissingPermissionsException) {
            null
        } catch (_: ArbiInvalidBalanceException) {
            null
        } catch (_: ArbiAdapterException) {
            null
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(CloseAccountService::class.java)
    }
}

sealed class CloseAccountError : PrintableSealedClassV2() {
    data class InvalidAccountNameCheck(val name: String) : CloseAccountError()
    data class InvalidAccountStatus(val status: AccountStatus) : CloseAccountError()
    data class CloseFounderWalletError(val error: CloseWalletError) : CloseAccountError()
    data class CloseAccountErrorWithMessageAndAmount(val messageKey: String, val messageAmount: Long) : CloseAccountError()
    data class Unknown(
        @JsonIgnore // para nao quebrar o log
        val exception: Exception,
    ) : CloseAccountError() {
        val reason = exception.javaClass.simpleName
        val message = exception.message
    }
}

data class ExecuteCloseAccountStepsError(
    @JsonIgnore // para nao quebrar o log
    val exception: Exception,
    val steps: List<CloseAccountStep>,
) {
    val reason = exception.javaClass.simpleName
    val message = exception.message
}

sealed class CloseWalletError : PrintableSealedClassV2() {
    data class TransactionStillProcessing(val walletId: WalletId) : CloseWalletError()
    data class BalanceDifferentThanZero(val walletId: WalletId, val balance: Long, val pixKey: PixKey?, val externalBankAccount: ExternalBankAccount?) : CloseWalletError()
    data class Unknown(
        @JsonIgnore // para nao quebrar o log
        val exception: Exception,
    ) : CloseWalletError() {
        val reason = exception.javaClass.simpleName
        val message = exception.message
    }
}

sealed class ClosePartialAccountError : Exception() {
    data object AccountIsNotUnderTest : ClosePartialAccountError()

    class MarkAsFraud(val error: LivenessErrors) : ClosePartialAccountError()

    class Unknown(val e: Exception) : ClosePartialAccountError()
}

sealed class DeactivateAccountError : Exception() {
    class MarkAsFraud(val error: LivenessErrors) : DeactivateAccountError()
    class Unknown(val e: Exception) : DeactivateAccountError()
}

sealed class UserPoolRemoveUserError {
    data object UserNotFound : UserPoolRemoveUserError()

    class Unknown(
        @JsonIgnore // para nao quebrar o log
        val exception: Exception,
    ) : UserPoolRemoveUserError() {
        val reason = exception.javaClass.simpleName
        val message = exception.message
    }
}

data class CloseAccountResult(
    val steps: List<CloseAccountStep>,
)

data class ClosePartialAccountResult(
    val removedFromUserPool: Boolean,
)

data class CloseExternalAccountEvent(
    val walletId: String,
)

data class CloseAccountMessage(
    val accountId: AccountId,
    val steps: List<CloseAccountStep>,
    val tryCount: Int = 0,
)