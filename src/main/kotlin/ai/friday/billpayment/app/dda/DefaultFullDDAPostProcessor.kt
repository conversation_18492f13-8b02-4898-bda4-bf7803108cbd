package ai.friday.billpayment.app.dda

import ai.friday.billpayment.app.CNPJ_SIZE
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.BasicRegisterService
import ai.friday.billpayment.app.account.RegisterService
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Requires
import jakarta.inject.Provider
import org.slf4j.LoggerFactory

@FridayMePoupe
@Requires(missingBeans = [FullDDAPostProcessor::class])
open class DefaultFullDDAPostProcessor(
    private val registerServiceProvider: Provider<RegisterService>,
    private val basicRegisterServiceProvider: Provider<BasicRegisterService>,
    private val accountRepository: AccountRepository,
) : FullDDAPostProcessor {

    override fun process(ddaRegister: DDARegister): Either<FullDDAPostProcessorError, Unit> {
        return registerServiceProvider.get().findByAccountId(ddaRegister.accountId).map { accountRegisterData ->
            if (accountRegisterData.registrationType == RegistrationType.BASIC) {
                basicRegisterServiceProvider.get().activateAccount(accountId = ddaRegister.accountId)
            } else {
                registerServiceProvider.get().activateAccount(accountId = ddaRegister.accountId)
            }.map {
                Unit.right()
            }.getOrElse {
                LOG.error("DefaultFullDDAPostProcessor#process", it)
                FullDDAPostProcessorError.ActivateAccountError(it).left()
            }
        }.getOrElse {
            // conta PJ nao tem account register, e parece ja estar ativa nesse momento
            return if (it is ItemNotFoundException) {
                try {
                    val account = accountRepository.findById(ddaRegister.accountId)

                    if (account.status == AccountStatus.ACTIVE && account.document.length == CNPJ_SIZE) {
                        Unit.right()
                    } else {
                        FullDDAPostProcessorError.FindAccountError(it).left()
                    }
                } catch (e: Exception) {
                    FullDDAPostProcessorError.FindAccountError(e).left()
                }
            } else {
                FullDDAPostProcessorError.FindAccountError(it).left()
            }
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(DefaultFullDDAPostProcessor::class.java)
    }
}