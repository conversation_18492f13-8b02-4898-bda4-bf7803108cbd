package ai.friday.billpayment.app.inappsubscription.instrumentation

import ai.friday.billpayment.app.account.AccountId

sealed class InAppSubscriptionInstrumentationEvent {
    data class TrialExpired(val accountId: AccountId) : InAppSubscriptionInstrumentationEvent()
    data class TrialStarted(val accountId: AccountId) : InAppSubscriptionInstrumentationEvent()
    data class TrialConverted(val accountId: AccountId) : InAppSubscriptionInstrumentationEvent()
}