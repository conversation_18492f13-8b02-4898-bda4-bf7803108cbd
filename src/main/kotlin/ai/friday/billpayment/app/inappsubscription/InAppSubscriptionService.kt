package ai.friday.billpayment.app.inappsubscription

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.UpdateAccountStatusRequest
import ai.friday.billpayment.app.account.UpdateAccountStatusResult
import ai.friday.billpayment.app.account.isOpen
import ai.friday.billpayment.app.inappsubscription.instrumentation.InAppSubscriptionInstrumentationService
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton
import java.time.Duration
import java.time.LocalDate
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
@FridayMePoupe
open class InAppSubscriptionService(
    private val crmService: CrmService,
    private val notificationAdapter: NotificationAdapter,
    private val subscriptionRepository: InAppSubscriptionRepository,
    private val subscriptionAccessConcessionRepository: InAppSubscriptionAccessConcessionRepository,
    private val subscriptionEventRepository: InAppSubscriptionEventRepository,
    private val accountService: AccountService,
    private val instrumentationService: InAppSubscriptionInstrumentationService,
    private val subscriptionAdapter: InAppSubscriptionAdapter,
) {

    private val logger = LoggerFactory.getLogger(InAppSubscriptionService::class.java)

    open fun getSubscription(accountId: AccountId): InAppSubscription? = subscriptionRepository.findOrNull(accountId)

    open fun getByStatus(status: InAppSubscriptionStatus): List<InAppSubscription> = subscriptionRepository.findByStatus(status)

    open fun getSubscriptionsEndingBefore(date: LocalDate): List<InAppSubscription> = subscriptionRepository.findEndingBefore(date)

    open fun getStoreSubscriptions(accountId: AccountId): Result<List<InAppSubscription>> = subscriptionAdapter.getSubscriptions(accountId).fold({ Result.failure(it) }, { Result.success(it) })

    open fun getProduct(productId: InAppSubscriptionProductId) = Either.catch { subscriptionAdapter.getProducts().first { it.storeIdentifier == productId.value } }

    open fun createSubscription(inAppSubscription: InAppSubscription): Either<SubscriptionError, InAppSubscription> {
        val appSubscription = upsertSubscription(inAppSubscription)

        if (inAppSubscription.reason == InAppSubscriptionReason.TRIAL) {
            instrumentationService.trialStarted(inAppSubscription.accountId)
        }

        return appSubscription.right()
            .also { updateAccountWith(appSubscription) }
    }

    open fun renewSubscription(inAppSubscription: InAppSubscription): Either<SubscriptionError, InAppSubscription> {
        val currentSubscription = subscriptionRepository.findOrNull(inAppSubscription.accountId) ?: return createSubscription(inAppSubscription)

        if (currentSubscription.reason == InAppSubscriptionReason.TRIAL) {
            instrumentationService.trialConverted(inAppSubscription.accountId)
        }

        val updatedSubscription = upsertSubscription(inAppSubscription)

        return updatedSubscription.right()
            .also { updateAccountWith(updatedSubscription) }
    }

    open fun extendSubscription(inAppSubscription: InAppSubscription): Either<SubscriptionError, InAppSubscription> {
        subscriptionRepository.findOrNull(inAppSubscription.accountId) ?: run {
            logger.warn(append("inAppSubscription", inAppSubscription).andAppend("context", "criando assinatura pelo evento de extend"), "InAppSubscriptionService#extendSubscription")
            return createSubscription(inAppSubscription)
        }

        val updatedSubscription = upsertSubscription(inAppSubscription)

        return updatedSubscription.right()
            .also { updateAccountWith(updatedSubscription) }
    }

    open fun handleSubscriptionRefund(inAppSubscription: InAppSubscription): Either<SubscriptionError, InAppSubscription> {
        val updatedSubscription = inAppSubscription.copy(endsAt = getZonedDateTime())
        val concession = createConcession(updatedSubscription)
        subscriptionAccessConcessionRepository.save(concession)

        subscriptionRepository.save(updatedSubscription)
        return updatedSubscription.right()
            .also { updateAccountWith(updatedSubscription) }
    }

    open fun expireSubscription(inAppSubscription: InAppSubscription): Either<SubscriptionError, InAppSubscription> {
        val subscription = subscriptionRepository.findOrNull(inAppSubscription.accountId) ?: return SubscriptionError.SubscriptionNotFound.left()

        val updatedSubscription = if (subscription.status != InAppSubscriptionStatus.EXPIRED) {
            val expiredSubscription = subscription.copy(status = InAppSubscriptionStatus.EXPIRED)

            subscriptionRepository.save(expiredSubscription)

            expiredSubscription
        } else {
            subscription
        }

        return updatedSubscription.right()
            .also {
                updateAccountWith(updatedSubscription)
                if (subscription.reason == InAppSubscriptionReason.TRIAL) {
                    instrumentationService.trialExpired(subscription.accountId)
                }
            }
    }

    open fun cancelSubscription(toInAppSubscription: InAppSubscription): Either<SubscriptionError, InAppSubscription> {
        return toInAppSubscription.right()
            .also {
                subscriptionRepository.save(toInAppSubscription)
                updateAccountWith(toInAppSubscription)
            }
    }

    open fun uncancelSubscription(toInAppSubscription: InAppSubscription): Either<SubscriptionError, InAppSubscription> {
        return toInAppSubscription.right()
            .also {
                subscriptionRepository.save(toInAppSubscription)
                updateAccountWith(toInAppSubscription)
            }
    }

    open fun transferSubscription(
        sourceAccountId: AccountId,
        targetAccountId: AccountId,
    ): Either<SubscriptionTransferError, InAppSubscription> {
        val sourceSubscription = subscriptionRepository.findOrNull(sourceAccountId) ?: return SubscriptionTransferError.SubscriptionNotFound.left()
        val targetSubscription = subscriptionRepository.findOrNull(targetAccountId)
        val account = accountService.findAccountByIdOrNull(targetAccountId)
        val partialAccount = accountService.findPartialAccountByIdOrNull(targetAccountId)

        if (account == null && partialAccount == null) {
            return SubscriptionTransferError.TargetAccountNotFound.left()
        }

        val status = account?.status ?: partialAccount?.status

        if (status in listOf(AccountStatus.DENIED, AccountStatus.CLOSED, AccountStatus.PENDING_CLOSE)) {
            return SubscriptionTransferError.InvalidTargetAccount.left()
        }

        if (targetSubscription != null && targetSubscription.status == InAppSubscriptionStatus.ACTIVE) {
            return SubscriptionTransferError.TargetAccountAlreadyHasSubscription.left()
        }

        val updatedSubscription = sourceSubscription.copy(accountId = targetAccountId)

        upsertSubscription(updatedSubscription, isTransfer = true, originSubscriptionConcessionId = sourceSubscription.inAppSubscriptionAccessConcessionId)
        removeSubscription(sourceSubscription, isTransfer = true, originSubscriptionConcessionId = sourceSubscription.inAppSubscriptionAccessConcessionId)

        return updatedSubscription.right()
    }

    open fun transferSubscriptionFromExternalId(
        sourceExternalId: String,
    ): Result<TransferFromAnonymousIdResult> = runCatching {
        val sourceAccountId = AccountId(sourceExternalId)
        val targetAccountId = subscriptionAdapter.getAccountId(externalId = sourceExternalId).getOrThrow()

        if (targetAccountId == null) {
            TransferFromAnonymousIdResult.TargetAccountIdNotFound(sourceExternalId)
        } else if (targetAccountId == sourceAccountId) {
            TransferFromAnonymousIdResult.InvalidExternalId(sourceExternalId, targetAccountId)
        } else {
            transferSubscription(sourceAccountId = sourceAccountId, targetAccountId = targetAccountId).fold(
                ifLeft = { TransferFromAnonymousIdResult.Failed(sourceExternalId, targetAccountId, it) },
                ifRight = { TransferFromAnonymousIdResult.Transferred(sourceExternalId, targetAccountId) },
            )
        }
    }

    open fun saveEvent(inAppSubscriptionEvent: InAppSubscriptionEvent) = subscriptionEventRepository.save(inAppSubscriptionEvent)

    open fun getSubscriptionRealTime(accountId: AccountId): Either<InAppSubscriptionError, InAppSubscription> {
        val subscriptions =
            subscriptionAdapter.getSubscriptions(accountId).getOrElse {
                return Either.Left(InAppSubscriptionError.GetActiveSubscription)
            }

        val activeSubscriptions = subscriptions.filter { it.status == InAppSubscriptionStatus.ACTIVE }

        if (activeSubscriptions.isEmpty()) {
            return Either.Left(InAppSubscriptionError.ExpiredSubscription)
        }

        if (activeSubscriptions.size > 1) {
            return Either.Left(InAppSubscriptionError.MultipleActiveSubscriptions)
        }

        return Either.Right(activeSubscriptions.first())
    }

    open fun handleSubscriptionExpiration() {
        subscriptionRepository.findExpiredAfter(getLocalDate().minusDays(3)).forEach { subscription ->
            val account = accountService.findAccountByIdOrNull(subscription.accountId)
            if (account?.subscriptionType == SubscriptionType.IN_APP) {
                notificationAdapter.notifyInAppSubscriptionOverdue(
                    subscription.accountId,
                )
            }
        }

        subscriptionRepository.findByEndDate(getLocalDate().minusDays(6)).forEach { subscription ->
            val account = accountService.findAccountByIdOrNull(subscription.accountId)
            if (account?.subscriptionType == SubscriptionType.IN_APP) {
                notificationAdapter.notifySubscriptionOverdueNotificationChannelDowngradeWarning(
                    subscription.accountId,
                )
            }
        }
    }

    open fun refundAndCancelGuestSubscription(accountId: AccountId, productId: InAppSubscriptionProductId): Result<RefundResult> {
        val partialAccount = accountService.findPartialAccountByIdOrNull(accountId)
            ?: return Result.success(RefundResult.AccountNotFound(accountId))

        if (partialAccount.role != Role.GUEST) {
            return Result.success(RefundResult.AccountMustBeGuest(accountId))
        }

        return refundAndCancelSubscription(accountId = accountId, productId = productId)
    }

    open fun refundAndCancelSubscription(accountId: AccountId, productId: InAppSubscriptionProductId): Result<RefundResult> {
        if (accountService.findPartialAccountByIdOrNull(accountId) == null && accountService.findAccountByIdOrNull(accountId) == null) {
            return Result.success(RefundResult.AccountNotFound(accountId))
        }

        val subscription = subscriptionAdapter.getSubscriptions(accountId = accountId)
            .getOrElse { return Result.failure(it) }
            .firstOrNull { it.productId == productId } // NOTE: não está claro o que acontecendo havendo mais que uma assinatura

        if (subscription == null) {
            return Result.success(RefundResult.SubscriptionNotFound(accountId, productId))
        }

        if (subscription.store != InAppSubscriptionStore.PLAY_STORE) {
            return Result.success(RefundResult.NotAllowedByStore(accountId, productId, subscription.store))
        }

        return subscriptionAdapter.refund(accountId, productId)
            .map { RefundResult.Requested(accountId, productId) }
    }

    open fun grantGratuity(accountId: AccountId, duration: Duration, deduplicationId: String): Result<GrantGratuityResult> {
        return runCatching { doGrantGratuity(accountId, duration, deduplicationId) }
    }

    private fun doGrantGratuity(accountId: AccountId, duration: Duration, deduplicationId: String): GrantGratuityResult {
        val partialAccount = accountService.findPartialAccountByIdOrNull(accountId)

        if (partialAccount != null) {
            if (partialAccount.status == AccountStatus.DENIED) {
                return GrantGratuityResult.ValidAccountNotFound(accountId)
            }

            if (partialAccount.subscriptionType != SubscriptionType.IN_APP) {
                return GrantGratuityResult.NotInAppSubscription(accountId)
            }
        }

        val account = accountService.findAccountByIdOrNull(accountId = accountId)

        if (account != null) {
            if (!account.isOpen()) {
                return GrantGratuityResult.ValidAccountNotFound(accountId)
            }

            if (account.subscriptionType != SubscriptionType.IN_APP) {
                return GrantGratuityResult.NotInAppSubscription(accountId)
            }
        }

        if (account == null && partialAccount == null) {
            return GrantGratuityResult.ValidAccountNotFound(accountId)
        }

        if (subscriptionAccessConcessionRepository.findAllByDeduplicationId(deduplicationId).isNotEmpty()) {
            return GrantGratuityResult.DuplicatedGrantFound(accountId)
        }

        val subscription = getSubscription(accountId)

        if (subscription != null &&
            subscription.status == InAppSubscriptionStatus.ACTIVE &&
            subscription.reason in listOf(InAppSubscriptionReason.TRIAL, InAppSubscriptionReason.SUBSCRIPTION)
        ) {
            return GrantGratuityResult.ActiveStoreSubscription(accountId, subscription)
        }

        val endsAt = getZonedDateTime().let {
            if (subscription?.status == InAppSubscriptionStatus.ACTIVE && subscription.endsAt.isAfter(it)) {
                subscription.endsAt
            } else {
                it
            }
        }.plusDays(duration.toDays())

        val updatedSubscription = InAppSubscription(
            accountId = accountId,
            status = InAppSubscriptionStatus.ACTIVE,
            endsAt = endsAt,
            store = null,
            reason = InAppSubscriptionReason.BACKOFFICE,
            inAppSubscriptionAccessConcessionId = null,
            autoRenew = false,
            price = 0L,
            productId = null,
            offStoreProductId = null,
        )

        return extendSubscription(updatedSubscription).fold(
            { throw Exception("Failed to extend subscription during gratuity grant: $it") },
            { GrantGratuityResult.Granted(accountId) },
        )
    }

    private fun removeSubscription(
        inAppSubscription: InAppSubscription,
        isTransfer: Boolean = false,
        originSubscriptionConcessionId: InAppSubscriptionAccessConcessionId? = null,
    ) {
        val subscription =
            inAppSubscription.copy(
                status = InAppSubscriptionStatus.EXPIRED,
                endsAt = getZonedDateTime(),
                autoRenew = false,
            )
        val concession = createConcession(subscription, isTransfer, originSubscriptionConcessionId)
        subscriptionRepository.save(subscription.copy(inAppSubscriptionAccessConcessionId = concession.id))
        subscriptionAccessConcessionRepository.save(concession)

        updateAccountWith(subscription)
    }

    private fun upsertSubscription(
        inAppSubscription: InAppSubscription,
        isTransfer: Boolean = false,
        originSubscriptionConcessionId: InAppSubscriptionAccessConcessionId? = null,
    ): InAppSubscription {
        val inAppSubscriptionAccessConcession = createConcession(inAppSubscription, isTransfer, originSubscriptionConcessionId)
        val updatedAppSubscription = inAppSubscription.copy(inAppSubscriptionAccessConcessionId = inAppSubscriptionAccessConcession.id)
        subscriptionRepository.save(updatedAppSubscription)
        subscriptionAccessConcessionRepository.save(inAppSubscriptionAccessConcession)

        updateAccountWith(updatedAppSubscription)

        return updatedAppSubscription
    }

    private fun updateAccountWith(subscription: InAppSubscription) {
        val account = accountService.findAccountByIdOrNull(subscription.accountId) ?: return

        if (account.subscriptionType == SubscriptionType.IN_APP) {
            val result = accountService.attemptToUpdateAccountStatus(
                when (subscription.status) {
                    InAppSubscriptionStatus.ACTIVE -> UpdateAccountStatusRequest.Unblock(accountId = subscription.accountId)
                    InAppSubscriptionStatus.EXPIRED -> UpdateAccountStatusRequest.Block(
                        accountId = subscription.accountId,
                        expiredFor = Duration.between(subscription.endsAt.toLocalDate().atStartOfDay(), getLocalDate().atStartOfDay()),
                    )
                },
            ).getOrThrow()

            if (result is UpdateAccountStatusResult.Unchanged) {
                crmService.upsertContact(result.account)
            }
        } else {
            crmService.upsertContact(account)
        }
    }
}

sealed class RefundResult {
    data class Requested(val accountId: AccountId, val productId: InAppSubscriptionProductId) : RefundResult()
    data class AccountNotFound(val accountId: AccountId) : RefundResult()
    data class SubscriptionNotFound(val accountId: AccountId, val productId: InAppSubscriptionProductId) : RefundResult()
    data class NotAllowedByStore(val accountId: AccountId, val productId: InAppSubscriptionProductId, val store: InAppSubscriptionStore?) : RefundResult()
    data class AccountMustBeGuest(val accountId: AccountId) : RefundResult()
}

sealed class GrantGratuityResult : PrintableSealedClassV2() {
    data class ValidAccountNotFound(val accountId: AccountId) : GrantGratuityResult()
    data class NotInAppSubscription(val accountId: AccountId) : GrantGratuityResult()
    data class ActiveStoreSubscription(val accountId: AccountId, val subscription: InAppSubscription) : GrantGratuityResult()
    data class DuplicatedGrantFound(val accountId: AccountId) : GrantGratuityResult()
    data class Granted(val accountId: AccountId) : GrantGratuityResult()
}

sealed class TransferFromAnonymousIdResult : PrintableSealedClassV2() {
    data class Transferred(val externalId: String, val targetAccountId: AccountId) : TransferFromAnonymousIdResult()
    data class Failed(val externalId: String, val targetAccountId: AccountId, val reason: SubscriptionTransferError) : TransferFromAnonymousIdResult()
    data class InvalidExternalId(val externalId: String, val targetAccountId: AccountId) : TransferFromAnonymousIdResult()
    data class InvalidTargetAccount(val externalId: String, val targetAccountId: AccountId) : TransferFromAnonymousIdResult()
    data class TargetAccountIdNotFound(val externalId: String) : TransferFromAnonymousIdResult()
}

data class AnonymousIdInAppSubscriptionQueueMessageTO(
    val appUserId: String,
    val retry: Int,
)