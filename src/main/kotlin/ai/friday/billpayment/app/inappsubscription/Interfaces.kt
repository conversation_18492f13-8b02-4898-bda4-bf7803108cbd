package ai.friday.billpayment.app.inappsubscription

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.account.AccountId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import java.time.LocalDate
import java.time.Period
import java.time.ZonedDateTime
import java.util.UUID

interface InAppSubscriptionRepository {
    fun save(inAppSubscription: InAppSubscription)

    fun findOrNull(accountId: AccountId): InAppSubscription?

    fun find(accountId: AccountId): InAppSubscription

    fun findByStatus(status: InAppSubscriptionStatus): List<InAppSubscription>

    fun findExpiredAfter(expiredAfter: LocalDate): List<InAppSubscription>

    fun findEndingBefore(date: LocalDate): List<InAppSubscription>

    fun findByEndDate(endsAt: LocalDate): List<InAppSubscription>

    fun findAll(): List<InAppSubscription>
}

interface InAppSubscriptionAccessConcessionRepository {
    fun save(inAppSubscriptionAccessConcession: InAppSubscriptionAccessConcession)

    fun findLastByAccountId(accountId: AccountId): InAppSubscriptionAccessConcession?

    fun findAllByAccountId(accountId: AccountId): List<InAppSubscriptionAccessConcession>
    fun findAllByDeduplicationId(deduplicationId: String): List<InAppSubscriptionAccessConcession>
}

interface InAppSubscriptionEventRepository {
    fun save(inAppSubscriptionEvent: InAppSubscriptionEvent)
}

interface InAppSubscriptionAdapter {
    fun getSubscriptions(accountId: AccountId): Either<Exception, List<InAppSubscription>>
    fun getProducts(): List<InAppSubscriptionProducts>
    fun refund(accountId: AccountId, productId: InAppSubscriptionProductId): Result<Unit>
    fun getAccountId(externalId: String): Result<AccountId?>
}

data class InAppSubscription(
    val accountId: AccountId,
    val status: InAppSubscriptionStatus,
    val endsAt: ZonedDateTime,
    val store: InAppSubscriptionStore?,
    val reason: InAppSubscriptionReason,
    val inAppSubscriptionAccessConcessionId: InAppSubscriptionAccessConcessionId?,
    val createdAt: ZonedDateTime = getZonedDateTime(),
    val autoRenew: Boolean?,
    val price: Long,
    val productId: InAppSubscriptionProductId?,
    val offStoreProductId: String?,
) {
    fun checkActiveWithAutoRenewEnabled(): Boolean {
        return status == InAppSubscriptionStatus.ACTIVE && autoRenew == true
    }
}

enum class InAppSubscriptionStore {
    PLAY_STORE,
    APP_STORE,
}

enum class InAppSubscriptionStatus {
    ACTIVE,
    EXPIRED,
}

data class InAppSubscriptionAccessConcession(
    val id: InAppSubscriptionAccessConcessionId,
    val createdAt: ZonedDateTime = getZonedDateTime(),
    val accountId: AccountId,
    val reason: InAppSubscriptionReason,
    val store: InAppSubscriptionStore?,
    val endsAt: ZonedDateTime,
    val isTransfer: Boolean? = null,
    val originConcessionId: InAppSubscriptionAccessConcessionId? = null,
    val deduplicationId: String? = null,
)

fun createConcession(
    inAppSubscription: InAppSubscription,
    isTransfer: Boolean = false,
    originConcessionId: InAppSubscriptionAccessConcessionId? = null,
) =
    InAppSubscriptionAccessConcession(
        id = InAppSubscriptionAccessConcessionId(),
        accountId = inAppSubscription.accountId,
        reason = inAppSubscription.reason,
        store = inAppSubscription.store,
        endsAt = inAppSubscription.endsAt,
        isTransfer = isTransfer,
        originConcessionId = originConcessionId,
    )

fun InAppSubscriptionAccessConcession.isExpired() = endsAt.isBefore(getZonedDateTime())

enum class InAppSubscriptionReason {
    TRIAL,
    SUBSCRIPTION,
    BACKOFFICE,
    NO_STORE_COUPON,
}

data class InAppSubscriptionEvent(
    val accountId: AccountId,
    val type: InAppSubscriptionEventType,
    val createdAt: ZonedDateTime,
    val payload: String,
)

enum class InAppSubscriptionEventType {
    TEST, // Teste via RevenueCat dashboard
    INITIAL_PURCHASE, // Nova assinatura
    NON_RENEWING_PURCHASE, // Assinatura não renovável
    RENEWAL, // Assinatura renovada
    PRODUCT_CHANGE, // Mudança de plano, não significa que o efeito é imediato
    CANCELLATION, // Cancelamento de assinatura
    BILLING_ISSUE, // Falha no pagamento, pode ser igorado
    SUBSCRIBER_ALIAS, // Não documentado no RevenueCat
    SUBSCRIPTION_PAUSED, // Assinatura pausada no final do período. O acesso não deve ser revogado até receber o evento de EXPIRATION
    UNCANCELLATION, // Assinatura cancelada mas não expirada reativada
    TRANSFER, // Transferência de assinatura
    SUBSCRIPTION_EXTENDED, // Assinatura extentida
    EXPIRATION, // Assinatura expirada
}

data class InAppSubscriptionAccessConcessionId(
    val value: String,
) {
    constructor() : this("IN-APP-SUBSCRIPTION-ACCESS-CONCESSION-${UUID.randomUUID()}")
}

sealed class InAppSubscriptionError : PrintableSealedClassV2() {
    data object ExpiredSubscription : InAppSubscriptionError()

    data object GetActiveSubscription : InAppSubscriptionError()

    data object MultipleActiveSubscriptions : InAppSubscriptionError()
}

sealed class SubscriptionError : PrintableSealedClassV2() {
    data object ActiveConcessionNotFound : SubscriptionError()

    data object SubscriptionNotFound : SubscriptionError()

    data object SubscriptionAlreadyExists : SubscriptionError()
}

sealed class SubscriptionTransferError : PrintableSealedClassV2() {
    data object SubscriptionNotFound : SubscriptionTransferError()

    data object TargetAccountAlreadyHasSubscription : SubscriptionTransferError()

    data object TargetAccountNotFound : SubscriptionTransferError()

    data object InvalidTargetAccount : SubscriptionTransferError()

    data object SourceAccountHasNoValidSubscription : SubscriptionTransferError()
}

data class InAppSubscriptionProductId(val value: String)

val BACKOFFICE_IN_APP_SUBSCRIPTION_PRODUCT_ID = InAppSubscriptionProductId("BACKOFFICE")

data class InAppSubscriptionProducts(
    val id: InAppSubscriptionProductId,
    val displayName: String?,
    val storeIdentifier: String,
    val duration: Period,
    val gracePeriodDuration: Period?,
    val type: String,
)