package ai.friday.billpayment.app.contact

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.BillEventDependency
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.math.BigInteger
import java.time.ZonedDateTime
import java.util.UUID

data class Contact(
    val id: ContactId = ContactId("RECIPIENT-${UUID.randomUUID()}"),
    val accountId: AccountId,
    val alias: String? = null,
    val name: String,
    val document: String,
    val bankAccounts: List<SavedBankAccount>,
    val pixKeys: List<SavedPixKey>,
    val created: ZonedDateTime = getZonedDateTime(),
    val lastUsed: LastUsed?,
) : Comparable<Contact> {
    override fun compareTo(other: Contact): Int {
        return when {
            this.alias != other.alias && other.alias != null -> {
                this.alias?.compareTo(other.alias) ?: this.name.compareTo(other.alias)
            }

            this.alias != null && other.alias == null -> {
                this.alias.compareTo(other.name)
            }

            this.name != other.name -> {
                this.name.compareTo(other.name)
            }

            else -> 0
        }
    }
}

data class LastUsed(
    val bankAccountId: BankAccountId? = null,
    val pixKey: String? = null,
)

@BillEventDependency
data class ContactId(val value: String)

data class SavedPixKey(val value: String, val type: PixKeyType) {
    var invalidationCode: InvalidationCode? = null
    var invalidated: Boolean = false
}

data class SavedBankAccount(
    val id: BankAccountId = BankAccountId("BANK_ACCOUNT-${UUID.randomUUID()}"),
    val accountType: AccountType,
    val bankNo: Long?,
    val routingNo: Long,
    val accountNo: BigInteger,
    val accountDv: String,
    val ispb: String? = null,
) {
    var invalidationCode: InvalidationCode? = null
    var invalidated: Boolean = false
    var invalidationMessage: String = ""

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as SavedBankAccount

        if (accountType != other.accountType) return false
        if (bankNo != other.bankNo) return false
        if (routingNo != other.routingNo) return false
        if (accountNo != other.accountNo) return false
        if (accountDv != other.accountDv) return false
        if (ispb != other.ispb) return false

        return true
    }

    override fun hashCode(): Int {
        var result = accountType.hashCode()
        result = 31 * result + bankNo.hashCode()
        result = 31 * result + routingNo.hashCode()
        result = 31 * result + accountNo.hashCode()
        result = 31 * result + accountDv.hashCode()
        result = 31 * result + ispb.hashCode()
        return result
    }
}

data class BankAccountId(val value: String)

enum class InvalidationCode {
    INVALID_TYPE, INVALID_DATA
}

fun SavedBankAccount.toBankAccount(document: String) = BankAccount(
    accountType = accountType,
    bankNo = bankNo,
    routingNo = routingNo,
    accountNo = accountNo,
    accountDv = accountDv,
    document = document,
    ispb = ispb,
)