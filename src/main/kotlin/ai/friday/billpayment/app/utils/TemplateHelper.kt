package ai.friday.billpayment.app.utils

import com.github.jknack.handlebars.Handlebars
import com.github.jknack.handlebars.Template

object TemplateHelper {
    private val templates = mutableMapOf<String, Template>()

    fun applyTemplate(template: String, values: Any): String = getTemplate(template).apply(values)

    private fun getTemplate(name: String) = templates.getOrPut(name) {
        Handlebars().compile(name)
    }
}