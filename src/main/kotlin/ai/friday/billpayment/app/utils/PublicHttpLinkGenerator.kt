package ai.friday.billpayment.app.utils

import ai.friday.billpayment.app.account.StoredObject
import java.time.Duration

interface PublicHttpLinkGenerator {
    fun generate(storedObject: StoredObject, duration: Duration, contentType: String? = null, retryConfiguration: PublicHttpLinkGeneratorRetryConfiguration? = null): String?
}

data class PublicHttpLinkGeneratorRetryConfiguration(
    val maxRetries: Int = 6,
    val initialDelay: Duration = Duration.ofMillis(100),
    val maxDelay: Duration = Duration.ofSeconds(2),
)