package ai.friday.billpayment.app

import ai.friday.billpayment.app.account.AccountId
import io.micronaut.context.propagation.slf4j.MdcPropagationContext
import io.micronaut.core.propagation.PropagatedContext
import jakarta.inject.Singleton

@Singleton
class ScopePropagator {
    fun <T> executeWithAccount(accountId: AccountId, block: () -> T): T {
        createScope(mapOf("accountId" to accountId.value)).use {
            return block()
        }
    }

    private fun createScope(scopeValues: Map<String, String?>): PropagatedContext.Scope {
        return PropagatedContext.empty()
            .plus(MdcPropagationContext(scopeValues)).propagate()
    }
}

@Singleton
class ScopeReader {
    fun resolveAccountId(): String? {
        if (!PropagatedContext.exists()) {
            return null
        }

        val isMdcPropagationContextPresent = PropagatedContext.get().find(MdcPropagationContext::class.java).isPresent

        if (!isMdcPropagationContextPresent) {
            return null
        }

        val context = PropagatedContext.get().find(MdcPropagationContext::class.java).get()

        return context.state["accountId"]
    }
}