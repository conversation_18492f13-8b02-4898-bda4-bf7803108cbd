package ai.friday.billpayment.app.utilityaccount

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.isValidCpf
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import java.util.*
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@JsonInclude(JsonInclude.Include.NON_NULL)
data class UtilityAccount(
    val id: UtilityAccountId = UtilityAccountId(),
    val status: UtilityAccountConnectionStatus = UtilityAccountConnectionStatus.PENDING,
    val statusMessage: String? = null,
    val walletId: WalletId,
    val accountEmail: String,
    val attempts: Int = 0,
    val scanFailureCount: Int = 0,
    val invalidCredentialsCount: Int = 0,
    val utility: Utility,
    val updatedAt: ZonedDateTime = getZonedDateTime(),
    val createdAt: ZonedDateTime,
    val notificatedAt: ZonedDateTime? = null,
    val lastBillIdFound: BillId? = null,
    val lastDueDateFound: LocalDate? = null,
    val lastBillFound: LocalDate? = null,
    val lastScannedAt: ZonedDateTime? = null,
    val lastSuccessfulScan: LocalDate? = null,
    val connectionMethod: UtilityConnectionMethod,
    val connectionDetails: UtilityAccountConnectionDetails,
    val errorFiles: UtilityAccountErrorFiles? = null,
    val addedBy: AccountId,
) {
    fun daysSinceLastDueDateFound(): Long {
        val lastDate = lastDueDateFound ?: createdAt.toLocalDate()
        return lastDate.until(getLocalDate(), ChronoUnit.DAYS)
    }
}

data class UtilityAccountUpdateCommand(
    val utilityAccount: UtilityAccount,
    val errorFilesBucket: String? = null,
    val errorFilesPath: String? = null,
    val silent: Boolean = false,
    val additionalInfo: UtilityAccountUpdateInfo? = null,
)

sealed class UtilityAccountUpdateInfo {
    data class BillsFound(val amount: Int, val averageValue: Long) : UtilityAccountUpdateInfo()
}

data class UtilityAccountErrorFiles(
    val bucket: String,
    val region: String,
    val filesKey: List<String>,
)

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
)
sealed interface UtilityAccountConnectionDetails {

    fun loginInfo(): Pair<String, String>
    fun requiresEncryption(): Boolean
    fun clone(password: String): UtilityAccountConnectionDetails

    @JsonTypeName("WithLoginAndPassword")
    data class WithLoginAndPassword(val login: String, val password: String) :
        UtilityAccountConnectionDetails {
        override fun loginInfo(): Pair<String, String> = Pair(login, password)
        override fun requiresEncryption(): Boolean = true
        override fun clone(password: String) = this.copy(password = password)
    }

    @JsonTypeName("WithCpfAndPassword")
    data class WithCpfAndPassword(val cpf: String, val password: String) : UtilityAccountConnectionDetails {
        override fun loginInfo(): Pair<String, String> = Pair(cpf, password)
        override fun requiresEncryption(): Boolean = true
        override fun clone(password: String) = this.copy(password = password)
    }

    @JsonTypeName("WithCpfAndClientCodeAndInstallationCode")
    data class WithCpfAndClientCodeAndInstallationCode(
        val cpf: String,
        val clientCode: String,
        val installationCode: String,
    ) : UtilityAccountConnectionDetails {
        override fun loginInfo(): Pair<String, String> = Pair("$cpf - $installationCode", "$clientCode | $installationCode")
        override fun requiresEncryption(): Boolean = false
        override fun clone(password: String) = this.copy()
    }

    @JsonTypeName("WithCpfAndInstallationNumber")
    data class WithCpfAndInstallationNumber(val cpf: String, val installationNumber: String) : UtilityAccountConnectionDetails {
        override fun loginInfo(): Pair<String, String> = Pair("$cpf - $installationNumber", installationNumber)
        override fun requiresEncryption(): Boolean = false
        override fun clone(password: String) = this.copy()
    }

    @JsonTypeName("WithCpfInstallationNumberAndPassword")
    data class WithCpfInstallationNumberAndPassword(val cpf: String, val password: String, val installationNumber: String) : UtilityAccountConnectionDetails {
        override fun loginInfo(): Pair<String, String> = Pair("$cpf - $installationNumber", password)
        override fun requiresEncryption(): Boolean = true
        override fun clone(password: String) = this.copy(password = password)
    }

    @JsonTypeName("WithCpfPasswordAndClientNumber")
    data class WithCpfPasswordAndClientNumber(val cpf: String, val password: String, val clientNumber: String) : UtilityAccountConnectionDetails {
        override fun loginInfo(): Pair<String, String> = Pair("$cpf - $clientNumber", password)
        override fun requiresEncryption(): Boolean = true
        override fun clone(password: String) = this.copy(password = password)
    }

    @JsonTypeName("WithUsernameAndPassword")
    data class WithUsernameAndPassword(val username: String, val password: String) : UtilityAccountConnectionDetails {
        override fun loginInfo(): Pair<String, String> = Pair(username, password)
        override fun requiresEncryption(): Boolean = true
        override fun clone(password: String) = this.copy(password = password)
    }

    @JsonTypeName("WithPhoneNumberAndPassword")
    data class WithPhoneNumberAndPassword(val phoneNumber: String, val password: String) : UtilityAccountConnectionDetails {
        override fun loginInfo(): Pair<String, String> = Pair(phoneNumber, password)
        override fun requiresEncryption(): Boolean = true
        override fun clone(password: String) = this.copy(password = password)
    }

    @JsonTypeName("WithMultipleLoginAndPassword")
    data class WithMultipleLoginAndPassword(val loginType: String, val loginValue: String, val password: String) : UtilityAccountConnectionDetails {
        override fun loginInfo(): Pair<String, String> = Pair(loginValue, password)
        override fun requiresEncryption(): Boolean = true
        override fun clone(password: String) = this.copy(password = password)
    }

    @JsonTypeName("WithMultipleEmailAndPasswordAndInstallationNumber")
    data class WithMultipleEmailAndPasswordAndInstallationNumber(val email: String, val password: String, val installationNumber: String) : UtilityAccountConnectionDetails {
        override fun loginInfo(): Pair<String, String> = Pair(email, password)
        override fun requiresEncryption(): Boolean = true
        override fun clone(password: String) = this.copy(password = password)
    }

    @JsonTypeName("WithDocumentAndUserCode")
    data class WithDocumentAndUserCode(val document: String, val userCode: String) : UtilityAccountConnectionDetails {
        override fun loginInfo(): Pair<String, String> = Pair("$document - $userCode", userCode)
        override fun requiresEncryption(): Boolean = false
        override fun clone(password: String) = this.copy()
    }

    @JsonTypeName("WithCpfAndMotheFirstNameAndInstallationNumber")
    data class WithCpfAndMotheFirstNameAndInstallationNumber(val cpf: String, val installationNumber: String, val motherFirstName: String) : UtilityAccountConnectionDetails {
        override fun loginInfo(): Pair<String, String> = Pair("$cpf - $installationNumber", installationNumber)
        override fun requiresEncryption(): Boolean = false
        override fun clone(password: String) = this.copy()
    }

    @JsonTypeName("WithEmailAndPasswordAndInstallationNumber")
    data class WithEmailAndPasswordAndInstallationNumber(val email: String, val password: String, val installationNumber: String) : UtilityAccountConnectionDetails {
        override fun loginInfo(): Pair<String, String> = Pair(email, password)
        override fun requiresEncryption(): Boolean = true
        override fun clone(password: String) = this.copy(password = password)
    }

    @JsonTypeName("WithDocumentAndUserCodeOrEmailAndPassword")
    data class WithDocumentAndUserCodeOrEmailAndPassword(val document: String?, val userCode: String?, val email: String?, val password: String?) : UtilityAccountConnectionDetails {
        override fun loginInfo(): Pair<String, String> =
            if (document != null) {
                Pair("$document - $userCode", password ?: "")
            } else {
                Pair(email!!, password!!)
            }
        override fun requiresEncryption(): Boolean = password != null
        override fun clone(password: String) = this.copy(password = password)
    }

    companion object {
        private val LOGGER = LoggerFactory.getLogger(UtilityAccountConnectionDetails::class.java)

        fun create(
            utility: Utility,
            details: Map<String, String>,
        ): UtilityAccountConnectionDetails {
            val logName = "UtilityAccountConnectionDetails#create"
            val markers = Markers.append("utility", utility.name)
                .andAppend("details", details.filter { it.key != "password" })

            val login = details["login"]
            val password = details["password"]

            if (login != null && password != null) {
                return WithLoginAndPassword(
                    login = login,
                    password = password,
                )
            }

            return when (utility) {
                Utility.UNKNOWN -> {
                    LOGGER.error(markers, logName)
                    throw IllegalArgumentException("utility is required")
                }

                Utility.VIVO_MOBILE,
                Utility.VIVO_COMBO,
                -> {
                    LOGGER.error(markers.andAppend("info", "login and password are required"), logName)
                    throw IllegalArgumentException("login and password are required")
                }

                Utility.SABESP,
                Utility.OI_HOME,
                -> {
                    val cpf = validateCpf(details["cpf"], markers)
                    val validPassword = validateRequiredField(details["password"], "password", markers)

                    return WithCpfAndPassword(
                        cpf = cpf,
                        password = validPassword,
                    )
                }

                Utility.CLARO_MOBILE -> {
                    val loginType = details["loginType"]
                    val loginValue = details["loginValue"]

                    val validPassword = validateRequiredField(password, "password", markers)

                    if (loginType != null && loginValue != null) {
                        return WithMultipleLoginAndPassword(loginType, loginValue, validPassword)
                    }

                    val emailOrCpf = validateRequiredField(details["emailOrCpf"], "emailOrCpf", markers)

                    if (emailOrCpf.isValidCpf()) {
                        return WithMultipleLoginAndPassword(loginType = "document", loginValue = emailOrCpf, validPassword)
                    }

                    return WithMultipleLoginAndPassword(loginType = "email", loginValue = emailOrCpf, validPassword)
                }

                Utility.CLARO_HOME,
                -> {
                    val username = validateRequiredField(details["username"], "username", markers)
                    val validPassword = validateRequiredField(password, "password", markers)

                    return WithUsernameAndPassword(
                        username = username,
                        password = validPassword,
                    )
                }

                Utility.TIM_MOBILE -> {
                    val phoneNumber = validateRequiredField(details["phoneNumber"], "phoneNumber", markers)
                    val validPassword = validateRequiredField(password, "password", markers)

                    return WithPhoneNumberAndPassword(
                        phoneNumber = phoneNumber,
                        password = validPassword,
                    )
                }

                Utility.LIGHT_RJ,
                Utility.CEMIG,
                -> {
                    val cpf = validateCpf(details["cpf"], markers)
                    val currentPassword = details["password"] ?: ""
                    val installationNumber = details["installationNumber"] ?: ""

                    return WithCpfInstallationNumberAndPassword(
                        cpf = cpf,
                        password = currentPassword,
                        installationNumber = installationNumber,
                    )
                }

                Utility.NATURGY -> {
                    val cpf = validateCpf(details["cpf"], markers)
                    val currentPassword = details["password"] ?: ""
                    val clientNumber = validateRequiredField(details["clientNumber"], "clientNumber", markers)

                    WithCpfPasswordAndClientNumber(
                        cpf = cpf,
                        password = currentPassword,
                        clientNumber = clientNumber,
                    )
                }

                Utility.ENEL_SP -> {
                    val email = validateRequiredField(details["email"], "email", markers)
                    val currentPassword = validateRequiredField(details["password"], "password", markers)
                    val installationNumber = validateRequiredField(details["installationNumber"], "installationNumber", markers)

                    WithMultipleEmailAndPasswordAndInstallationNumber(
                        email = email,
                        password = currentPassword,
                        installationNumber = installationNumber,
                    )
                }

                Utility.COMGAS -> {
                    val document = details["document"]
                    val userCode = details["userCode"]
                    val email = details["email"]
                    val password = details["password"]

                    if (document == null && email == null) {
                        LOGGER.error(markers.andAppend("info", "either email or document are required"), "UtilityAccountConnectionDetails#validateRequired")
                        throw IllegalArgumentException("either email or document are required")
                    }

                    if (document != null && userCode == null) {
                        LOGGER.error(markers.andAppend("info", "userCode is required if document is defined"), "UtilityAccountConnectionDetails#validateRequired")
                        throw IllegalArgumentException("userCode is required if document is defined")
                    }

                    if (email != null && password == null) {
                        LOGGER.error(markers.andAppend("info", "password is required if email is defined"), "UtilityAccountConnectionDetails#validateRequired")
                        throw IllegalArgumentException("password is required if email is defined")
                    }

                    WithDocumentAndUserCodeOrEmailAndPassword(
                        document = document,
                        userCode = userCode,
                        email = email,
                        password = password,
                    )
                }

                Utility.COPEL -> {
                    val cpf = validateCpf(details["cpf"], markers)
                    val installationNumber = validateRequiredField(details["installationNumber"], "installationNumber", markers)
                    val motherFirstName = validateRequiredField(details["motherFirstName"], "motherFirstName", markers)

                    WithCpfAndMotheFirstNameAndInstallationNumber(
                        cpf = cpf,
                        installationNumber = installationNumber,
                        motherFirstName = motherFirstName,
                    )
                }

                Utility.CPFL -> {
                    val email = validateRequiredField(details["email"], "email", markers)
                    val currentPassword = validateRequiredField(details["password"], "password", markers)
                    val installationNumber = validateRequiredField(details["installationNumber"], "installationNumber", markers)

                    WithEmailAndPasswordAndInstallationNumber(email, currentPassword, installationNumber)
                }

                Utility.EDP_ES, Utility.EDP_SP -> {
                    val email = validateRequiredField(details["email"], "email", markers)
                    val currentPassword = validateRequiredField(details["password"], "password", markers)
                    val installationNumber = validateRequiredField(details["installationNumber"], "installationNumber", markers)

                    WithEmailAndPasswordAndInstallationNumber(email, currentPassword, installationNumber)
                }
            }
        }

        private fun validateCpf(cpf: String?, markers: LogstashMarker): String {
            if (cpf == null) {
                LOGGER.error(markers.andAppend("info", "cpf is required"), "UtilityAccountConnectionDetails#validateCpf")
                throw IllegalArgumentException("cpf is required")
            }

            if (!cpf.isValidCpf()) {
                LOGGER.error(markers.andAppend("info", "cpf is not valid"), "UtilityAccountConnectionDetails#validateCpf")
                throw IllegalArgumentException("cpf is not valid")
            }

            return cpf
        }

        private fun validateRequiredField(value: String?, fieldName: String, markers: LogstashMarker): String {
            if (value == null) {
                LOGGER.error(markers.andAppend("info", "$fieldName is required"), "UtilityAccountConnectionDetails#validateRequired")
                throw IllegalArgumentException("$fieldName is required")
            }

            return value
        }
    }
}

enum class UtilityAccountConnectionStatus {
    PENDING, CONNECTED, CONNECTION_ERROR, FINAL_ERROR, DISCONNECTED, PENDING_DISCONNECTION, DISCONNECTION_ERROR, INVALID_CREDENTIALS, REQUIRES_RECONNECTION, NOT_SUPPORTED;

    fun toViewName(): String {
        return when (this) {
            PENDING -> "Conectando"
            CONNECTED -> "Conectado"
            CONNECTION_ERROR -> "Erro ao conectar"
            PENDING_DISCONNECTION -> "Aguardando para desconectar"
            DISCONNECTION_ERROR -> "Erro ao desconectar"
            INVALID_CREDENTIALS -> "Credenciais Inválidas"
            DISCONNECTED -> "Desconectado"
            FINAL_ERROR -> "Não conectado"
            REQUIRES_RECONNECTION -> "Esperando reconexão"
            NOT_SUPPORTED -> "Não suportado"
        }
    }

    companion object {
        fun of(name: String): UtilityAccountConnectionStatus? = entries.find { it.name == name }
    }
}

enum class UtilityConnectionMethod {
    MANUAL,
    FLOW,
    SCRAPING,
    USER,
}

enum class Utility(
    val viewName: String,
    val method: UtilityConnectionMethod = UtilityConnectionMethod.SCRAPING,
    val codes: List<Regex> = emptyList(),
    val enabled: Boolean = true,
    val disableRequestInvoices: Boolean = false,
    val assignors: List<String> = listOf(),
    val warnings: List<UtilityWarning> = listOf(),
) {
    UNKNOWN("unknown"),
    VIVO_MOBILE(
        viewName = "Vivo Móvel",
        codes = listOf(
            "84.............0042.........................",
            "84.............0044.........................",
            "84.............0041.........................",
            "84.............0047.........................",
            "84.............0048.........................",
            "84.............0049.........................",
            "84.............0053.........................",
            "84.............0055.........................",
            "84.............0058.........................",
            "84.............0060.........................",
            "84.............0064.........................",
            "84.............0069.........................",
            "84.............0072.........................",
            "84.............0073.........................",
            "84.............0074.........................",
            "84.............0075.........................",
            "84.............0076.........................",
            "84.............0079.........................",
            "84.............0080.........................",
            "84.............0081.........................",
            "84.............0290.........................",
            "84.............0291.........................",
            "84.............0292.........................",
            "84.............0293.........................",
            "84.............0294.........................",
            "84.............0295.........................",
        ).map { it.toRegex() },
        assignors = listOf(
            "VIVO AM",
            "VIVO BA",
            "VIVO CE",
            "VIVO DF",
            "VIVO ES",
            "VIVO GO",
            "VIVO MA",
            "VIVO MG - TELEMIG",
            "VIVO MS",
            "VIVO MT",
            "VIVO PA",
            "VIVO PB",
            "VIVO PE",
            "VIVO PI",
            "VIVO PR E SC",
            "VIVO RJ",
            "VIVO RN",
            "VIVO RO",
            "VIVO RS",
            "VIVO SE",
            "VIVO SP",
        ),
        method = UtilityConnectionMethod.USER,
        disableRequestInvoices = true,
    ),
    VIVO_COMBO(
        viewName = "Vivo Internet",
        codes = listOf(
            "84.............0082.........................",
        ).map { it.toRegex() },
        assignors = listOf("VIVO"),
        method = UtilityConnectionMethod.USER,
        disableRequestInvoices = true,
    ),
    CLARO_HOME(
        viewName = "Claro Residencial",
        codes = listOf(
            "846............0158.........................",
            "846............0159.........................",
            "846............0160.........................",
            "846............0161.........................",
            "846............0162.........................",
            "846............0163.........................",
            "846............0165.........................",
            "846............0221.........................",
            "846............0305.........................",
            "846............0071.........................",
            "846............0078.........................",
            "846............0297.........................",
        ).map { it.toRegex() },
        assignors = listOf(
            "CLARO SP DDD 11",
            "CLARO TV",
            "NET SERVICOS",
        ),
        method = UtilityConnectionMethod.FLOW,
    ),
    CLARO_MOBILE(
        viewName = "Claro Móvel",
        codes = listOf(
            "848............0158.........................",
            "848............0159.........................",
            "848............0160.........................",
            "848............0161.........................",
            "848............0162.........................",
            "848............0163.........................",
            "848............0165.........................",
            "848............0221.........................",
            "848............0305.........................",
            "848............0071.........................",
            "848............0078.........................",
            "848............0297.........................",
        ).map { it.toRegex() },
        assignors = listOf("CLARO SP DDD 11"),
        method = UtilityConnectionMethod.FLOW,
    ),
    TIM_MOBILE(
        viewName = "Tim Móvel",
        codes = listOf(
            "84.............0034.........................",
            "84.............0035.........................",
            "84.............0034.........................",
            "84.............0040.........................",
            "84.............0043.........................",
            "84.............0046.........................",
            "84.............0052.........................",
            "84.............0056.........................",
            "84.............0062.........................",
            "84.............0109.........................",
            "84.............0110.........................",
            "84.............0111.........................",
            "84.............0392.........................",
        ).map { it.toRegex() },
        assignors = listOf("TIM CELULAR S.A."),
        method = UtilityConnectionMethod.FLOW,
    ),
    OI_HOME(
        viewName = "Oi Residencial",
        codes = listOf(
            "84.............0113.........................",
            "84.............0313.........................",
            "84.............0369.........................",
        ).map { it.toRegex() },
        assignors = listOf(
            "TNL PCS SA- OI MOVEL",
            "OI BRT CELULAR",
        ),
        method = UtilityConnectionMethod.FLOW,
    ),
    LIGHT_RJ(
        viewName = "Light - RJ",
        codes = listOf(
            "83.............0053.........................",
        ).map { it.toRegex() },
        assignors = listOf("LIGHT"),
        method = UtilityConnectionMethod.FLOW,
    ),
    NATURGY(
        viewName = "Naturgy",
        codes = listOf(
            "83.............0100.........................",
            "83.............0056.........................",
        ).map { it.toRegex() },
        assignors = listOf(
            "CEG-CIA DISTRIBUIDORA GAS",
            "CEG RIO",
            "GAS NATURAL",
        ),
        method = UtilityConnectionMethod.FLOW,
    ),
    ENEL_SP(
        viewName = "Enel - SP",
        codes = listOf(
            "83.............0048.........................",
        ).map { it.toRegex() },
        assignors = listOf("ELETROPAULO"),
        method = UtilityConnectionMethod.USER,
        disableRequestInvoices = true,
    ),
    SABESP(
        viewName = "SABESP",
        codes = listOf(
            "82.............0097.........................",
        ).map { it.toRegex() },
        assignors = listOf("SABESP"),
        method = UtilityConnectionMethod.FLOW,
        disableRequestInvoices = false,
    ),
    COMGAS(
        viewName = "Comgás",
        codes = listOf(
            "83.............0057.........................",
        ).map { it.toRegex() },
        assignors = listOf("COMGAS"),
        method = UtilityConnectionMethod.USER,
        disableRequestInvoices = true,
    ),
    CEMIG(
        viewName = "Cemig",
        codes = listOf(
            "83.............0138.........................",
            "83.............0139.........................",
        ).map { it.toRegex() },
        assignors = listOf("CEMIG DISTRIBUICAO"),
        method = UtilityConnectionMethod.FLOW,
    ),
    COPEL(
        viewName = "Copel",
        codes = listOf(
            "83..............0111............................",
            "84..............0106............................",
        ).map { it.toRegex() },
        assignors = listOf("COPEL DISTRIBUICAO S.A."),
        method = UtilityConnectionMethod.FLOW,
    ),
    CPFL(
        viewName = "CPFL",
        codes = listOf(
            "83..............0052.........................",
            "83..............0026.........................",
            "83..............0040.........................",
            "83..............0027.........................",
            "83..............0055.........................",
        ).map { it.toRegex() },
        assignors = listOf(
            "CPFLEnergia",
            "Cia Piratininga de Forca e Luz",
            "AES SUL DISTR GAUCHA ENER",
            "CIA JAGUARI DE ENERGIA",
        ),
        method = UtilityConnectionMethod.FLOW,
    ),
    EDP_ES(
        viewName = "EDP - ES",
        codes = listOf(
            "83..............0051.........................",
        ).map { it.toRegex() },
        assignors = listOf("Escelsa"),
        method = UtilityConnectionMethod.FLOW,
    ),
    EDP_SP(
        viewName = "EDP - SP",
        codes = listOf(
            "83..............0073.........................",
        ).map { it.toRegex() },
        assignors = listOf("BANDEIRANTE ENERGIA S A"),
        method = UtilityConnectionMethod.FLOW,
    ),
    ;

    fun extractClientNumberFrom(barcode: BarCode): String? {
        return when (this) {
            CLARO_MOBILE -> barcode.digitable.substring(29, 35) + barcode.digitable.substring(36, 39)
            UNKNOWN, VIVO_MOBILE, VIVO_COMBO, CLARO_HOME, TIM_MOBILE, OI_HOME, LIGHT_RJ, NATURGY, ENEL_SP, SABESP, COMGAS, CEMIG, COPEL, CPFL, EDP_ES, EDP_SP -> null
        }
    }

    companion object {
        fun of(name: String): Utility? =
            values().find { it.name == name }
    }
}

data class UtilityAccountId(val value: String) {
    constructor() : this("UA-${UUID.randomUUID()}")
}

data class UtilityWarning(
    val level: Level,
    val title: String,
    val description: String,
) {
    enum class Level {
        WARNING,
    }
}

data class UtilityAccountWithFormData(
    val utility: Utility,
    val category: String,
    val form: List<UtilityAccountFormData>,
)

data class UtilityAccountFormData(
    val type: String,
    val parsers: List<String> = emptyList(),
    val parserRegex: String? = null,
    val name: String,
    val label: String,
    val description: String? = null,
    val placeholder: String,
    val errorText: String,
    val customProps: Map<String, *>? = null,
)

val cpfField = UtilityAccountFormData(
    type = "numeric",
    name = "cpf",
    placeholder = "000.000.000-00",
    label = "CPF",
    parsers = listOf("cpf"),
    errorText = "Digite um CPF válido",
)
val emailField = UtilityAccountFormData(
    type = "text",
    name = "email",
    placeholder = "<EMAIL>",
    label = "E-mail",
    parsers = listOf("email"),
    errorText = "Digite um e-mail válido",
)
val passwordField = UtilityAccountFormData(
    type = "password",
    name = "password",
    placeholder = "••••••",
    label = "Senha",
    errorText = "Digite uma senha válida",
)
val documentField = UtilityAccountFormData(
    type = "numeric",
    name = "document",
    placeholder = "000.000.000-00",
    label = "CPF ou CNPJ",
    parsers = listOf("document"),
    errorText = "CPF ou CNPJ inválido",
)
val phoneNumberField = UtilityAccountFormData(
    type = "numeric",
    name = "phoneNumber",
    placeholder = "(00) 0 0000-0000",
    label = "Telefone",
    parsers = listOf("phoneNumber"),
    errorText = "Informe um telefone válido",
)

val installationNumber = UtilityAccountFormData(
    type = "numeric",
    name = "installationNumber",
    placeholder = "000000",
    label = "Número da instalação",
    errorText = "Digite um número da instalação válido",
)