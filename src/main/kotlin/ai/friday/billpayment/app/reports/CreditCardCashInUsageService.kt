package ai.friday.billpayment.app.reports

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.PaymentMethodType.CREDIT_CARD
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.usage.CreditCardUsageService
import ai.friday.billpayment.app.usage.TransactionAmount
import ai.friday.billpayment.app.usage.sum
import java.time.LocalDate

@FridayMePoupe
class CreditCardCashInUsageService(
    private val accountRepository: AccountRepository,
    private val creditCardUsageService: CreditCardUsageService,
) {

    fun reportAvailableQuotaSum(): Long {
        return accountRepository.findAllPhysicalBalanceAccount()
            .distinctBy { it.accountId }
            .fold(0L) { agg, curr -> sumCreditCardCashInUsage(agg, curr.accountId) }
    }

    private fun sumCreditCardCashInUsage(sum: Long, accountId: AccountId): Long {
        return when {
            hasCreditCard(accountId) -> sum + creditCardUsageService.calculateCreditCardUsage(accountId).availableQuota
            else -> sum
        }
    }

    private fun hasCreditCard(accountId: AccountId) =
        accountRepository.findAccountPaymentMethodsByAccountId(accountId).any { it.method.type == CREDIT_CARD }

    fun reportCashInByDate(date: LocalDate): TransactionAmount {
        return accountRepository.findAllPhysicalBalanceAccount()
            .distinctBy { it.accountId }
            .map {
                creditCardUsageService.getCreditCardUsageOnDate(
                    accountId = it.accountId,
                    date = date,
                )
            }.sum()
    }
}