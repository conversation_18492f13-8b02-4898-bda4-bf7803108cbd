package ai.friday.billpayment.app.reports

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.formatBrazilCurrency
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.usage.TransactionAmount
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.micronaut.context.annotation.Property
import io.via1.communicationcentre.app.integrations.EmailSenderService
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@FridayMePoupe
class FinancialReportService(
    private val accountRepository: AccountRepository,
    private val walletRepository: WalletRepository,
    private val billRepository: BillRepository,
    private val emailSender: EmailSenderService,
    private val creditCardCashInUsageService: CreditCardCashInUsageService,
) {

    @field:Property(name = "email.notification.email")
    lateinit var from: String

    @field:Property(name = "email.finance-report-recipients")
    lateinit var recipients: String

    private val subject = "Relatório de Contas a Vencer"

    fun reportBillsComingDueWithin(periodInDays: Long): Map<String, Long> {
        val bills = accountRepository.findAllAccountsActivatedSince(LocalDate.MIN)
            .flatMap { account ->
                walletRepository.findWallets(
                    accountId = account.accountId,
                    memberStatus = MemberStatus.ACTIVE,
                ).filter { wallet -> wallet.founder.accountId == account.accountId }
            }
            .flatMap { wallet ->
                billRepository.findBillsComingDueWithin(
                    walletId = wallet.id,
                    periodInDays = periodInDays,
                )
            }
            .filter { it.billType.isBoleto() }

        val groupedByEffectiveDueDate = bills.groupBy { it.effectiveDueDate }
        val groupedTotal = groupedByEffectiveDueDate.map { (effectiveDueDate, groupedBills) ->
            effectiveDueDate.format(DateTimeFormatter.ISO_DATE) to groupedBills.map { it.amountTotal }.sum()
        }.toMap()

        emailSender.sendRawEmail(from, subject, buildBillsComingDueWithinReportContent(groupedTotal), recipients)
        return groupedTotal
    }

    fun reportCreditCardCashInUsage(): Pair<Long, TransactionAmount> {
        val availableQuotaSum = creditCardCashInUsageService.reportAvailableQuotaSum()
        val cashInYesterdayTotal = creditCardCashInUsageService.reportCashInByDate(getZonedDateTime().toLocalDate().minusDays(1))

        val message = buildString {
            appendLine("quota disponível de hoje: ${formatBrazilCurrency(availableQuotaSum)}")
            appendLine("total de cash-in de ontem:")
            appendLine("  bruto: ${formatBrazilCurrency(cashInYesterdayTotal.totalAmount)}")
            appendLine("  líquido: ${formatBrazilCurrency(cashInYesterdayTotal.netAmount)}")
            appendLine("  taxa: ${formatBrazilCurrency(cashInYesterdayTotal.feeAmount)}")
        }
        emailSender.sendRawEmail(from, "Relatório de CashIn", message, recipients)

        return Pair(availableQuotaSum, cashInYesterdayTotal)
    }

    private fun buildBillsComingDueWithinReportContent(groupedTotal: Map<String, Long>): String {
        val sortedMap = groupedTotal.toSortedMap()
        var sum = 0L
        val accumulatedMap = sortedMap.asSequence().map { (k, v) ->
            sum += v
            k to sum / 100
        }
        return accumulatedMap.map { (effectiveDueDate, amount) -> "total até $effectiveDueDate : R$ $amount,00" }
            .joinToString(separator = "\n")
    }
}