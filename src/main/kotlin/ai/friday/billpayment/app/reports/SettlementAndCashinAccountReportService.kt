package ai.friday.billpayment.app.reports

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.DefaultBankStatementItem
import ai.friday.billpayment.app.bill.BRAZIL_LOCALE
import ai.friday.billpayment.app.integrations.BankAccountService
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import io.via1.communicationcentre.app.integrations.EmailSenderService
import java.text.NumberFormat
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter.ISO_DATE
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
class SettlementAndCashinAccountReportService(
    private val bankAccountService: BankAccountService,
    private val emailSender: EmailSenderService,
) {

    @field:Property(name = "integrations.arbi.contaLiquidacao")
    lateinit var settlementAccountNo: String

    @field:Property(name = "integrations.arbi.contaCashin")
    lateinit var cashinAccountNo: String

    @field:Property(name = "integrations.arbi.inscricao")
    lateinit var settlementAccountDocument: String

    @field:Property(name = "email.notification.email")
    lateinit var from: String

    @field:Property(name = "email.finance-report-recipients")
    lateinit var recipients: String

    internal val subject = "Relatório das Contas Liquidação e Cashin"

    internal val creditTransferKey =
        "${BankStatementItemType.TRANSFERENCIA_CC.name}_${BankStatementItemFlow.CREDIT.name}"
    internal val debitTransferKey = "${BankStatementItemType.TRANSFERENCIA_CC.name}_${BankStatementItemFlow.DEBIT.name}"
    internal val debitTED = "${BankStatementItemType.TED_MESMA_TITULARIDADE.name}_${BankStatementItemFlow.DEBIT.name}"

    fun reportSettlementAndCashinAccountTotals(date: ZonedDateTime): SettlementAndCashinAccountReport {
        val settlementAccountReport = buildAccountReport(settlementAccountNo, settlementAccountDocument, date)
        val cashinAccountReport = buildAccountReport(cashinAccountNo, settlementAccountDocument, date)
        val content = buildString {
            appendLine("---   ---   MOVIMENTAÇÕES CONTA LIQUIDAÇÃO ARBI   ---   ---")
            appendLine(buildReportContent(settlementAccountReport))
            appendLine("")
            appendLine("---   ---   MOVIMENTAÇÕES CONTA CASHIN ARBI   ---   ---")
            appendLine(buildReportContent(cashinAccountReport))
        }
        emailSender.sendRawEmail(from, "$subject ${date.format(ISO_DATE)}", content, recipients)

        return SettlementAndCashinAccountReport(
            settlement = settlementAccountReport,
            cashin = cashinAccountReport,
        )
    }

    fun buildAccountReport(accountNo: String, document: String, date: ZonedDateTime): Map<String, Long> {
        val map = mutableMapOf(creditTransferKey to 0L, debitTransferKey to 0L, debitTED to 0L)
        bankAccountService.getStatement(AccountNumber(accountNo), document, date, date).items.map {
            map.compute(it.toMapKey()) { _, value ->
                value?.plus(it.amount) ?: it.amount
            }
            it
        }.alsoCheckAnyBoletoRefund()
        return map
    }

    private fun List<DefaultBankStatementItem>.alsoCheckAnyBoletoRefund() {
        forEach {
            if (it.description.contains("BOLETO DEVOLVIDO") && it.flow == BankStatementItemFlow.CREDIT) {
                LOG.error(Markers.append("statement", this).andAppend("ACTION", "VERIFY"), "ArbiBoletoRefunded")
            }
        }
    }

    private fun buildReportContent(groupedTotal: Map<String, Long>): String {
        val nf = NumberFormat.getCurrencyInstance(BRAZIL_LOCALE)
        return groupedTotal.map { (item, amount) -> "total de $item é ${nf.format(amount.toFloat() / 100f)}" }
            .joinToString(separator = "\n")
    }

    private val LOG = LoggerFactory.getLogger(SettlementAndCashinAccountReportService::class.java)
}

internal fun DefaultBankStatementItem.toMapKey(): String = "${this.type.name}_${this.flow.name}"

data class SettlementAndCashinAccountReport(
    val settlement: Map<String, Long>,
    val cashin: Map<String, Long>,
)