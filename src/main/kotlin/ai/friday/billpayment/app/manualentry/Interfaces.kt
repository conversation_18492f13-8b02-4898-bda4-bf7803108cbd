package ai.friday.billpayment.app.manualentry

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.ExternalBillId
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.recurrence.Range
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId
import arrow.core.Either
import java.time.LocalDate
import java.time.Month
import java.time.Year

interface ManualEntryService {

    fun find(id: ManualEntryId, walletId: WalletId): Either<ManualEntryError, ManualEntry>

    fun find(id: ManualEntryId): Either<ManualEntryError, ManualEntry>

    fun findAllByWalletAndDueDateMonth(walletId: WalletId, year: Year, month: Month): List<ManualEntry>

    fun findAllByWalletAndDueDate(walletId: WalletId, date: LocalDate): List<ManualEntry>

    fun create(
        walletId: WalletId,
        title: String,
        description: String = "",
        amount: Long,
        dueDate: LocalDate,
        source: ActionSource,
        type: ManualEntryType,
        status: ManualEntryStatus,
        categoryId: PFMCategoryId?,
        externalId: ExternalBillId? = null,
    ): Either<ManualEntryError, ManualEntry>

    fun update(request: UpdateManualEntryRequest, range: Range = Range.THIS): Either<ManualEntryError, Unit>

    fun setCategory(manualEntryId: ManualEntryId, walletId: WalletId, categoryId: PFMCategoryId?, range: Range = Range.THIS): Either<ManualEntryError, ManualEntry>

    fun listAll(walletId: WalletId): List<ManualEntry>

    fun listAllForWalletMember(walletId: WalletId, member: Member): List<ManualEntry>

    fun ignore(manualEntryId: ManualEntryId, walletId: WalletId, range: Range = Range.THIS): Either<ManualEntryError, Unit>

    fun restoreIgnored(manualEntryId: ManualEntryId, walletId: WalletId): Either<ManualEntryError, ManualEntry>

    fun markAsPaid(manualEntryId: ManualEntryId, walletId: WalletId): Either<ManualEntryError, Unit>

    fun markAsPaid(manualEntryId: ManualEntryId): Either<ManualEntryError, Unit>

    fun unmarkAsPaid(manualEntryId: ManualEntryId, walletId: WalletId): Either<ManualEntryError, Unit>

    fun notifyRemindersDueToday(): Int

    fun notifyRemindersExpired(): Int

    fun notifyReminder(reminderTO: ReminderTO): Either<ManualEntryError, Unit>

    fun notifyExpiredReminderWallet(walletId: WalletId, entries: List<String>): Either<ManualEntryError, Unit>
}

data class UpdateManualEntryRequest(
    val manualEntryId: ManualEntryId,
    val walletId: WalletId,
    val title: String? = null,
    val description: String? = null,
    val amount: Long? = null,
)

data class ReminderTO(
    val id: ManualEntryId,
    val walletId: WalletId,
)

data class ExpiredReminderTO(
    val walletId: WalletId,
    val entries: List<String>,
)

sealed class ManualEntryError : PrintableSealedClassV2() {
    data object ManualEntryNotFound : ManualEntryError()
    data object CategoryNotFound : ManualEntryError()
    data object InvalidStatus : ManualEntryError()
    data object UnknownError : ManualEntryError()
}