package ai.friday.billpayment.app.manualentry

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.ExternalBillId
import ai.friday.billpayment.app.billcategory.BillCategorySuggestion
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.wallet.BillFilterCriteria
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.canView
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.UUID

data class ManualEntry(
    val id: ManualEntryId = ManualEntryId(),
    val title: String,
    val description: String = "",
    val amount: Long,
    val dueDate: LocalDate,
    val walletId: WalletId,
    val type: ManualEntryType,
    val status: ManualEntryStatus,
    val source: ActionSource,
    val recurrenceRule: RecurrenceRule? = null,
    val markedAsPaidBy: AccountId? = null,
    val categoryId: PFMCategoryId? = null,
    val categorySuggestions: List<BillCategorySuggestion> = listOf(),
    val createdAt: ZonedDateTime,
    val updatedAt: ZonedDateTime,
    val markedAsPaidAt: ZonedDateTime? = null,
    val ignoredAt: ZonedDateTime? = null,
    val externalId: ExternalBillId? = null,
) {
    fun getRecurrenceId(): RecurrenceId {
        return when (val source = source) {
            is ActionSource.Recurrence -> source.recurrenceId
            is ActionSource.WalletRecurrence -> source.recurrenceId
            is ActionSource.SubscriptionRecurrence -> source.recurrenceId
            else -> throw IllegalStateException("Manual entry is not recurring")
        }
    }

    val isOverdue: Boolean
        get() {
            return (getLocalDate().isAfter(dueDate) && status == ManualEntryStatus.ACTIVE)
        }

    fun isIncome() = when (this.type) {
        ManualEntryType.INCOME -> true
        ManualEntryType.EXTERNAL_PAYMENT,
        ManualEntryType.REMINDER,
        ManualEntryType.UTILITY_INVOICE,
        ManualEntryType.VEHICLE_DEBT,
        -> false
    }

    fun isExpense() = !isIncome()
}

data class ManualEntryId(val value: String) {
    constructor() : this("MANUAL-${UUID.randomUUID()}")
}

enum class ManualEntryType {
    EXTERNAL_PAYMENT, REMINDER, INCOME, UTILITY_INVOICE, VEHICLE_DEBT
}

enum class ManualEntryStatus {
    ACTIVE, PAID, IGNORED
}

fun Member?.canView(manualEntry: ManualEntry): Boolean {
    return this == null || canView(toBillFilterCriteria(manualEntry))
}

private fun toBillFilterCriteria(manualEntry: ManualEntry) = BillFilterCriteria(
    visibleBy = listOf(),
    payerDocument = null,
    source = manualEntry.source,
)