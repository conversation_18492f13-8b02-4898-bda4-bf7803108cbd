package ai.friday.billpayment.app.fingerprint

import ai.friday.billpayment.app.account.AccountId

interface DeviceFingerprintAdapter {
    fun createDevice(request: CreateDeviceRequest): DeviceId
    fun removeDeviceId(deviceId: DeviceId): Boolean
}

interface DeviceFingerprintRepository {
    fun save(device: RegisteredDevice, accountId: AccountId)
    fun getDeviceOrNull(deviceFingerprint: DeviceFingerprint, accountId: AccountId): RegisteredDevice?
    fun getByAccountIdOrNull(accountId: AccountId, status: DeviceStatus = DeviceStatus.ACTIVE): RegisteredDevice?
    fun getByAccountId(accountId: AccountId): List<RegisteredDevice>
}