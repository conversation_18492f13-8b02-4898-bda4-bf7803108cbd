package ai.friday.billpayment.app.fingerprint

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.liveness.LivenessId
import java.time.ZonedDateTime
import java.util.UUID

data class MobileDeviceDetails(
    val uuid: UUID,
    val alias: String,
    val fingerprint: String?,
    val screenResolution: DeviceScreenResolution,
    val type: DeviceType,
    val fresh: Boolean,
    val dpi: Double,
    val manufacturer: String,
    val model: String,
    val rooted: Boolean,
    val storageCapacity: Int,
    val osId: String,
) {
    override fun equals(other: Any?): Boolean {
        return other is MobileDeviceDetails && other.uuid == uuid &&
            other.alias == alias &&
            other.fingerprint == fingerprint &&
            other.screenResolution == screenResolution &&
            other.type == type &&
            other.dpi == dpi &&
            other.manufacturer == manufacturer &&
            other.model == model &&
            other.rooted == rooted &&
            other.storageCapacity == storageCapacity &&
            other.osId == osId
    }

    override fun hashCode(): Int {
        var result = uuid.hashCode()
        result = 31 * result + alias.hashCode()
        result = 31 * result + (fingerprint?.hashCode() ?: 0)
        result = 31 * result + screenResolution.hashCode()
        result = 31 * result + type.hashCode()
        result = 31 * result + dpi.hashCode()
        result = 31 * result + manufacturer.hashCode()
        result = 31 * result + model.hashCode()
        result = 31 * result + rooted.hashCode()
        result = 31 * result + storageCapacity
        result = 31 * result + osId.hashCode()
        return result
    }
}

data class DeviceAccountRequest(
    val psp: DevicePspInformation,
    val accountNumber: AccountNumber,
)

data class CreateDeviceRequest(
    val deviceDetails: MobileDeviceDetails,
    val person: DevicePerson,
    val account: DeviceAccountRequest,
)

data class DeviceFingerprint(
    val value: String,
)

data class DeviceId(
    val value: String,
)

data class DevicePspInformation(
    val code: String,
    val agencyCode: String,
)

data class DevicePerson(
    val phoneNumber: MobilePhone,
    val document: Document,
    val fullName: String,
    val email: EmailAddress,
)

data class DeviceScreenResolution(
    val width: Int,
    val height: Int,
)

enum class DeviceType {
    ANDROID,
    IOS,
}

enum class DeviceStatus {
    ACTIVE,
    PENDING,
    INACTIVE,
    BLOCKED,
}

data class RegisteredDevice(
    val deviceIds: Map<AccountNumber, DeviceId>,
    val fingerprint: DeviceFingerprint,
    val status: DeviceStatus,
    val creationDate: ZonedDateTime,
    val details: MobileDeviceDetails,
    val liveness: DeviceLiveness?,
) {
    val pending = status == DeviceStatus.PENDING
    val active = status == DeviceStatus.ACTIVE
}

data class DeviceLiveness(
    val id: LivenessId,
    val enrollment: Boolean = false,
)