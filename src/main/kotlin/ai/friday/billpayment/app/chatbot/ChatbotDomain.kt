package ai.friday.billpayment.app.chatbot

import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentStatus
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.notification.ChatbotNotification
import ai.friday.billpayment.app.notification.MultiChannelNotification
import ai.friday.billpayment.app.notification.NotificationMedia
import ai.friday.billpayment.app.notification.WhatsappNotification

data class ChatbotNotificationSpec(
    val template: String,
    val configurationKey: String,
    val parameters: List<String> = listOf(),
    val quickReplyButtonsWhatsAppParameter: List<String> = emptyList(),
    val quickRepliesStartIndex: Int? = null,
    val buttonWhatsAppParameter: String? = null,
    val media: NotificationMedia? = null,
    val historyMessage: String,
    val sendEvent: String? = null,
    val clickEvent: String? = null,
)

sealed class ChatbotNotificationResult {
    data class Success(val accountId: AccountId) : ChatbotNotificationResult()
    data class NotFound(val accountId: AccountId) : ChatbotNotificationResult()
    data class Error(val accountId: AccountId, val message: String) : ChatbotNotificationResult()
}

interface ChatbotMessagePublisher {
    fun publishNotification(multiChannelNotification: MultiChannelNotification)
    fun publishGenericWhatsappNotification(account: Account?, notification: WhatsappNotification)
    fun publishChatbotNotification(chatbotNotification: ChatbotNotification)
    fun publishWebhook(webhook: Map<String, Any>)
    fun publishStateUpdate(account: Account, paymentStatus: AccountPaymentStatus, status: AccountStatus)
}