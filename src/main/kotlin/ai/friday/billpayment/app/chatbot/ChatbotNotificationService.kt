package ai.friday.billpayment.app.chatbot

import ai.friday.billpayment.app.AsyncUtils.callAsync
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.PartialAccount
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.ChatBotTransactionId
import ai.friday.billpayment.app.integrations.LoginRepository
import ai.friday.billpayment.app.integrations.NotificationService
import ai.friday.billpayment.app.integrations.OpenFinanceIncentiveType
import ai.friday.billpayment.app.integrations.TestPixReminderType
import ai.friday.billpayment.app.integrations.UserEvent
import ai.friday.billpayment.app.integrations.UserEventService
import ai.friday.billpayment.app.integrations.WelcomeMessageType
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.app.msisdnauth.createWhatsappEmail
import ai.friday.billpayment.app.notification.BillComingDueLastWarnDetails
import ai.friday.billpayment.app.notification.BillComingDueRegularDetails
import ai.friday.billpayment.app.notification.ButtonDeeplinkParameter
import ai.friday.billpayment.app.notification.ButtonTrackedDeeplinkParameter
import ai.friday.billpayment.app.notification.ChatbotNotificationWithAccount
import ai.friday.billpayment.app.notification.ChatbotNotificationWithPartialAccount
import ai.friday.billpayment.app.notification.NotificationTemplate
import ai.friday.billpayment.app.notification.NotificationType
import ai.friday.billpayment.app.notification.OpenFinanceIncentiveDetails
import ai.friday.billpayment.app.notification.PixTransactionDetails
import ai.friday.billpayment.app.notification.RegisterCompleted
import ai.friday.billpayment.app.notification.TestPixreminderDetails
import ai.friday.billpayment.app.notification.WelcomeDetails
import ai.friday.billpayment.app.notification.WhatsappNotification
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.log.andAppend
import jakarta.inject.Singleton
import java.util.concurrent.CompletableFuture
import java.util.concurrent.CompletionStage
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

interface ChatbotNotificationService {
    fun notifyPixKeyFindResult(account: Account, pixDetails: PixKeyDetails, transactionId: ChatBotTransactionId)
    fun notifyOpenFinanceDDAIncentive(account: Account, wallet: Wallet, userOptedOut: Boolean, bill: BillView)
    fun notifyOpenFinanceIncentive(account: Account, type: OpenFinanceIncentiveType, userOptedOut: Boolean, bank: String?)
    fun notifyRegisterCompleted(partialAccount: PartialAccount)
    fun notifyTestPixReminder(account: Account, wallet: Wallet, bills: List<BillView>, type: TestPixReminderType)
    fun notifyWelcomeMessage(account: Account, chatBotWelcomeMessageType: WelcomeMessageType)
    fun notifyBillComingDue(account: Account, wallet: Wallet, bills: List<BillView>, lastWarn: Boolean, hint: String?)
    fun sendNotification(accountId: AccountId, spec: ChatbotNotificationSpec): ChatbotNotificationResult
    fun sendNotificationsAsync(accountIds: List<AccountId>, notificationSpec: ChatbotNotificationSpec): Any
    fun sendNotifications(accountIds: List<AccountId>, notificationSpec: ChatbotNotificationSpec): CompletionStage<List<ChatbotNotificationResult>>
}

@Singleton
open class DefaultChatbotNotificationService(
    private val accountRegisterRepository: AccountRegisterRepository,
    private val accountService: AccountService,
    private val loginRepository: LoginRepository,
    private val notificationService: NotificationService,
    private val eventService: UserEventService,
) : ChatbotNotificationService {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun notifyBillComingDue(account: Account, wallet: Wallet, bills: List<BillView>, lastWarn: Boolean, hint: String?) {
        val (details, type) = if (lastWarn) {
            BillComingDueLastWarnDetails(
                bills = bills,
                wallet = wallet,
                hint = hint,
            ) to NotificationType.BILL_COMING_DUE_LAST_WARN
        } else {
            BillComingDueRegularDetails(
                bills = bills,
                wallet = wallet,
            ) to NotificationType.BILL_COMING_DUE
        }

        notificationService.notifyAccount(
            account = account,
            type = type,
        ) {
            ChatbotNotificationWithAccount(
                walletId = wallet.id,
                account = account,
                details = details,
            )
        }
    }

    override fun notifyWelcomeMessage(account: Account, chatBotWelcomeMessageType: WelcomeMessageType) {
        notificationService.notifyAccount(
            account = account,
            type = NotificationType.WELCOME_MESSAGE,
        ) {
            ChatbotNotificationWithAccount(
                walletId = WalletId(account.accountId.value), // TODO - isso não devia ser assim
                details = WelcomeDetails(
                    type = chatBotWelcomeMessageType,
                ),
                account = account,
            )
        }
    }

    override fun notifyTestPixReminder(account: Account, wallet: Wallet, bills: List<BillView>, type: TestPixReminderType) {
        notificationService.notifyAccount(
            account = account,
            type = NotificationType.TRI_PIX_REMINDER,
        ) {
            ChatbotNotificationWithAccount(
                walletId = wallet.id,
                details = TestPixreminderDetails(
                    bills = bills,
                    wallet = wallet,
                    reminderType = type,
                ),
                account = account,
            )
        }
    }

    override fun notifyRegisterCompleted(partialAccount: PartialAccount) {
        val register = accountRegisterRepository.findByAccountId(partialAccount.id, checkOpenAccount = false)

        notificationService.sendNotification(
            account = null,
            type = NotificationType.WELCOME_MESSAGE,
            ChatbotNotificationWithPartialAccount(
                walletId = WalletId(partialAccount.id.value), // TODO - isso não devia ser assim,
                details = RegisterCompleted,
                partialAccount = partialAccount,
                register = register,
            ),
        )

        // Fix temporário para evitar problemas com usuários que não tenham passado pela criação de login GUEST adicionada em 12/2024
        loginRepository.createLogin(
            providerUser = ProviderUser(
                id = partialAccount.id.value,
                providerName = ProviderName.WHATSAPP,
                username = "",
                emailAddress = createWhatsappEmail(register.mobilePhone!!),
            ),
            id = partialAccount.id,
            role = Role.GUEST,
        )
    }

    override fun notifyOpenFinanceIncentive(account: Account, type: OpenFinanceIncentiveType, userOptedOut: Boolean, bank: String?) {
        notificationService.notifyAccount(
            account = account,
            type = NotificationType.OPEN_FINANCE_INCENTIVE,
        ) {
            ChatbotNotificationWithAccount(
                walletId = WalletId(account.accountId.value), // TODO - isso não devia ser assim
                details = OpenFinanceIncentiveDetails(
                    type = type,
                    userOptedOut = userOptedOut,
                    bank = bank,
                    billInfo = null,
                ),
                account = account,
            )
        }
    }

    override fun notifyOpenFinanceDDAIncentive(account: Account, wallet: Wallet, userOptedOut: Boolean, bill: BillView) {
        notificationService.notifyAccount(
            account = account,
            type = NotificationType.OPEN_FINANCE_DDA_INCENTIVE,
        ) {
            ChatbotNotificationWithAccount(
                walletId = wallet.id,
                details = OpenFinanceIncentiveDetails(
                    type = OpenFinanceIncentiveType.DDA,
                    userOptedOut = userOptedOut,
                    billInfo = wallet to bill,
                ),
                account = account,
            )
        }
    }

    override fun notifyPixKeyFindResult(account: Account, pixDetails: PixKeyDetails, transactionId: ChatBotTransactionId) {
        notificationService.notifyAccount(
            account = account,
            type = NotificationType.PIX_KEY_FIND_RESULT,
        ) {
            ChatbotNotificationWithAccount(
                walletId = WalletId(account.accountId.value), // TODO - isso não devia ser assim
                account = account,
                details = PixTransactionDetails(
                    transactionId = transactionId.value,
                    recipientName = pixDetails.owner.name,
                    recipientDocument = pixDetails.owner.document,
                    recipientInstitution = pixDetails.holder.institutionName,
                ),
            )
        }
    }

    override fun sendNotification(accountId: AccountId, spec: ChatbotNotificationSpec): ChatbotNotificationResult {
        val logName = "ChatbotAIService#sendNotifications"
        val account = accountService.findAccountByIdOrNull(accountId)

        if (account == null) {
            logger.warn(Markers.append("accountId", accountId), "$logName/send")

            return ChatbotNotificationResult.NotFound(accountId)
        }

        return try {
            notificationService.sendNotification(account, NotificationType.FREE_NOTIFICATION, spec.toWhatsappNotification(account))
            logger.info(Markers.append("accountId", accountId).andAppend("msisdn", account.mobilePhone), "$logName/send")

            spec.sendEvent?.let {
                eventService.save(UserEvent(accountId, it))
            }

            ChatbotNotificationResult.Success(accountId)
        } catch (e: Exception) {
            val error = e.message ?: "unknown error"

            logger.error(
                Markers.append("accountId", accountId)
                    .andAppend("msisdn", account.mobilePhone)
                    .andAppend("errorMessage", error),
                "$logName/send",
            )

            ChatbotNotificationResult.Error(accountId, error)
        }
    }

    override fun sendNotificationsAsync(accountIds: List<AccountId>, notificationSpec: ChatbotNotificationSpec) = callAsync {
        sendNotifications(accountIds, notificationSpec)
    }

    override fun sendNotifications(accountIds: List<AccountId>, notificationSpec: ChatbotNotificationSpec): CompletionStage<List<ChatbotNotificationResult>> {
        val logName = "ChatbotAIService#sendNotifications"
        val resultCount = mutableMapOf<String, Int>()
        val errorCount = mutableMapOf<String, Int>()

        fun countResult(countMap: MutableMap<String, Int>, key: String) {
            countMap[key] = (countMap[key] ?: 0) + 1
        }

        val markers = Markers.append("listSize", accountIds.size)

        logger.info(markers, "$logName/start")

        val results = accountIds.map { accountId ->
            val result = sendNotification(accountId, notificationSpec)

            when (result) {
                is ChatbotNotificationResult.Error -> {
                    countResult(resultCount, "Error")
                    countResult(errorCount, result.message)
                }

                is ChatbotNotificationResult.NotFound -> countResult(resultCount, "NotFound")
                is ChatbotNotificationResult.Success -> countResult(resultCount, "Success")
            }

            result
        }

        logger.info(markers.andAppend("result", resultCount).andAppend("errors", errorCount), "$logName/end")

        return CompletableFuture.completedFuture(results)
    }
}
private fun String.resolveParam(account: Account) =
    this.replace("{{accountId}}", account.accountId.value)
        .replace("{{mobilePhone}}", account.mobilePhone)

fun ChatbotNotificationSpec.toWhatsappNotification(account: Account): WhatsappNotification {
    return WhatsappNotification(
        account = account,
        template = NotificationTemplate(template),
        configurationKey = configurationKey,
        parameters = parameters,
        quickReplyButtonsWhatsAppParameter = quickReplyButtonsWhatsAppParameter.map { it.resolveParam(account) },
        quickRepliesStartIndex = quickRepliesStartIndex,
        buttonParameter = buttonWhatsAppParameter?.let {
            val path = it.resolveParam(account)
            clickEvent?.let { event -> ButtonTrackedDeeplinkParameter(path, event) } ?: ButtonDeeplinkParameter(path)
        },
        media = media,
    )
}