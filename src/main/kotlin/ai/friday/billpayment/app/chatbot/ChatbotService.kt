package ai.friday.billpayment.app.chatbot

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.isOpen
import ai.friday.billpayment.app.forecast.ForecastPeriod
import ai.friday.billpayment.app.forecast.ForecastService
import ai.friday.billpayment.app.forecast.forPeriod
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.PixQRCodeCreatorService
import ai.friday.billpayment.app.onepixpay.GenericTransactionId
import ai.friday.billpayment.app.pix.PixQrCode
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonInclude
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

private const val DefaultTransactionId = "CeCWhatsapp"

@FridayMePoupe
open class ChatbotService(
    private val accountService: AccountService,
    private val walletService: WalletService,
    private val forecastService: ForecastService,
    private val pixQRCodeCreatorService: PixQRCodeCreatorService,
    private val notificationAdapter: NotificationAdapter,
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    open fun createQrCode(
        accountId: AccountId,
        walletId: WalletId,
        forecastPeriod: ForecastPeriod,
    ): Either<ChatbotQrCodeError, ChatbotQrCodeResult> {
        val markers = Markers.append("accountId", accountId.value)

        try {
            val wallet = walletService.findWallet(walletId)
            val account = accountService.findAccountById(wallet.founder.accountId)

            markers.andAppend("accountStatus", account.status.name)

            if (account.status != AccountStatus.ACTIVE) {
                logger.warn(markers, "CreateQrCode")
                return ChatbotQrCodeError.AccountNotActive.left()
            }

            if (!wallet.hasActiveMember(accountId)) {
                return ChatbotQrCodeError.UserNotAllowed.left()
            }

            val walletForecast = forecastService.calculateWalletBalanceForecast(wallet)
            val forecast = walletForecast.forPeriod(period = forecastPeriod)

            markers.andAppend("forecast", forecast)
            markers.andAppend("balance", walletForecast.amount)
            if (walletForecast.amount >= forecast) {
                logger.warn(markers, "CreateQrCode")
                return ChatbotQrCodeError.SufficientBalance.left()
            }

            val amount = forecast - walletForecast.amount

            val walletPixKey = walletService.walletEvpPixKey(wallet.id) ?: walletService.walletPixKey(wallet)

            val qrCode = pixQRCodeCreatorService.createQRCode(
                key = walletPixKey,
                document = account.document,
                amount = amount,
                recipientName = account.name,
                transactionId = GenericTransactionId(DefaultTransactionId),
            )
            markers.andAppend("qrCode", qrCode)

            logger.info(markers, "CreateQrCode")
            return ChatbotQrCodeResult(
                qrCode = PixQrCode(value = qrCode, amount = amount),
                wallet = wallet,
                forecastPeriod = forecastPeriod,
                includeScheduledBillsOnly = forecastPeriod == ForecastPeriod.TODAY,
            ).right()
        } catch (exception: AccountNotFoundException) {
            logger.warn(markers, "CreateQrCode", exception)
            return ChatbotQrCodeError.AccountNotFound.left()
        } catch (exception: ItemNotFoundException) {
            logger.error(markers, "CreateQrCode", exception)
            return ChatbotQrCodeError.WalletNotFound.left()
        } catch (exception: Exception) {
            logger.error(markers, "CreateQrCode", exception)
            return ChatbotQrCodeError.ServerError.left()
        }
    }

    open fun createAmountQrCode(
        accountId: AccountId,
        walletId: WalletId,
        amount: Long,
        message: String,
    ): Either<ChatbotQrCodeError, ChatbotAmountQrCodeResult> {
        val markers = Markers.append("accountId", accountId.value)

        try {
            val wallet = walletService.findWallet(walletId)
            val account = accountService.findAccountById(wallet.founder.accountId)

            markers.andAppend("accountStatus", account.status.name)

            if (!account.isOpen()) {
                logger.warn(markers, "ChatbotService#createAmountQrCode")
                return ChatbotQrCodeError.AccountNotActive.left()
            }

            if (!wallet.hasActiveMember(accountId)) {
                return ChatbotQrCodeError.UserNotAllowed.left()
            }

            val walletPixKey = walletService.walletEvpPixKey(wallet.id) ?: walletService.walletPixKey(wallet)

            val qrCode = pixQRCodeCreatorService.createQRCode(
                key = walletPixKey,
                document = account.document,
                amount = amount,
                recipientName = account.name,
                transactionId = GenericTransactionId(DefaultTransactionId),
                aditionalInfo = message,
            )
            markers.andAppend("qrCode", qrCode)

            logger.info(markers, "ChatbotService#createAmountQrCode")
            return ChatbotAmountQrCodeResult(
                qrCode = PixQrCode(value = qrCode, amount = amount),
                wallet = wallet,
            ).right()
        } catch (exception: AccountNotFoundException) {
            logger.warn(markers, "ChatbotService#createAmountQrCode", exception)
            return ChatbotQrCodeError.AccountNotFound.left()
        } catch (exception: ItemNotFoundException) {
            logger.error(markers, "ChatbotService#createAmountQrCode", exception)
            return ChatbotQrCodeError.WalletNotFound.left()
        } catch (exception: Exception) {
            logger.error(markers, "ChatbotService#createAmountQrCode", exception)
            return ChatbotQrCodeError.ServerError.left()
        }
    }

    open fun createQrCode(
        accountId: AccountId,
        forecastPeriod: ForecastPeriod,
    ): Either<ChatbotQrCodeError, ChatbotQrCodeResult> {
        val mainWallet = walletService.findWallets(accountId).firstOrNull {
            it.checkPrimary(accountId)
        } ?: return ChatbotQrCodeError.WalletNotFound.left()

        return this.createQrCode(
            accountId = accountId,
            walletId = mainWallet.id,
            forecastPeriod = forecastPeriod,
        )
    }

    open fun notifyBalance(accountId: AccountId): Either<NotifyBalanceError, Unit> {
        try {
            val mainWallet = walletService.findWallets(accountId).firstOrNull {
                it.checkPrimary(accountId)
            } ?: return NotifyBalanceError.WalletNotFound.left()

            val walletForecast = forecastService.calculateWalletBalanceForecast(mainWallet)

            val forecastToday = walletForecast.scheduled.amountToday

            val balanceForecastToday = walletForecast.amount - forecastToday

            if (balanceForecastToday >= 0) return NotifyBalanceError.SufficientBalance.left()

            val forecast7Days = walletForecast.open.amountWeek + walletForecast.scheduled.amountWeek

            val forecast15Days = walletForecast.open.amountFifteenDays + walletForecast.scheduled.amountFifteenDays

            notificationAdapter.notifyInsufficientBalanceToday(
                members = listOf(mainWallet.founder),
                pendingScheduledAmountToday = forecastToday - walletForecast.amount,
                pendingAmountTotal7Days = forecast7Days - walletForecast.amount,
                pendingAmountTotal15Days = forecast15Days - walletForecast.amount,
                walletId = mainWallet.id,
                walletName = mainWallet.name,
                isAfterHours = false, // TODO - verificar se precisa chamar o bankservice para saber
            )

            return Unit.right()
        } catch (exception: Exception) {
            return NotifyBalanceError.ServerError().left()
        }
    }
}

sealed class NotifyBalanceError : PrintableSealedClassV2() {
    data object SufficientBalance : NotifyBalanceError()
    data object WalletNotFound : NotifyBalanceError()
    class ServerError(val message: String? = null, val exception: Exception? = null) :
        NotifyBalanceError()
}

sealed class ChatbotQrCodeError : PrintableSealedClassV2() {
    data object ServerError : ChatbotQrCodeError()
    data object AccountNotFound : ChatbotQrCodeError()
    data object AccountNotActive : ChatbotQrCodeError()
    data object SufficientBalance : ChatbotQrCodeError()
    data object WalletNotFound : ChatbotQrCodeError()
    data object UserNotAllowed : ChatbotQrCodeError()
}

data class ChatbotQrCodeResult(
    val qrCode: PixQrCode,
    val wallet: Wallet,
    val forecastPeriod: ForecastPeriod,
    val includeScheduledBillsOnly: Boolean,
)

data class ChatbotAmountQrCodeResult(
    val qrCode: PixQrCode,
    val wallet: Wallet,
)

data class PaymentOrganizationResult(
    val authorizationServerId: String,
    val bankISPB: String,
    val routingNo: Long,
    val accountNo: Long,
    val accountDv: String,
    val accountType: String?,
    val bankNo: Long?,
    val institutionName: String,
)

data class OtherInstitutionsResponseTO(
    val id: String,
    val title: String,
    val description: String? = null,
)

data class GetAccountOrganizations(
// TODO - mover para o ITP interfaces
    var size: Int,
    val document: Document,
    @JsonInclude var otherFinancialInstitutions: List<OtherInstitutionsResponseTO>, // TODO - nao deveria ter TO no dominio. isso é coisa de Adapter/controller
    @JsonInclude var result: Map<Int, PaymentOrganizationResult>,
)

data class Organization(
    val authorizationServerId: String,
    val clearingCode: String? = null,
    val ispb: String?,
    val friendlyName: String,
)