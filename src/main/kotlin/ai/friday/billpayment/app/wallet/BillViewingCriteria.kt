package ai.friday.billpayment.app.wallet

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.name
import com.fasterxml.jackson.annotation.JsonTypeInfo

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.EXISTING_PROPERTY,
    property = "criteriaType",
)
sealed class BillViewingCriteria {
    abstract fun filter(bill: BillFilterCriteria): Boolean

    /* O Jackson não consegue adicionar o criteriaType pois o objeto serializado é uma lista.
    * <PERSON><PERSON><PERSON> caso, adicionamos a propriedade na mão definindo o seu valor, visto que o @JsonTypeInfo só é utilizado para deserializar,
    * e não para serializar. */
    val criteriaType = this::class.java.simpleName
}

data class ActionSourceCriteria(val actionSourceName: String) : BillViewingCriteria() {
    override fun filter(bill: BillFilterCriteria) = actionSourceName == bill.source.name()
}

fun defaultCriteria() = listOf(ActionSourceCriteria("DDA"))

data class BillFilterCriteria(
    val visibleBy: List<AccountId>,
    val payerDocument: String?,
    val source: ActionSource,
)