package ai.friday.billpayment.app.wallet.instrumentation

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.Wallet

sealed class WalletInstrumentationEvent {

    // FIXME nao necessariamente é o titular
    class MemberRemoved private constructor(
        val removedByAccountId: AccountId,
        val removedByName: String,
        val memberAccountId: AccountId,
        val memberName: String,
        val walletName: String,
    ) : WalletInstrumentationEvent() {
        companion object {
            fun create(wallet: Wallet, removedMember: Member, removedBy: Member): MemberRemoved {
                return MemberRemoved(
                    removedByAccountId = removedBy.accountId,
                    removedByName = removedBy.name,
                    memberAccountId = removedMember.accountId,
                    memberName = removedMember.name,
                    walletName = wallet.name,
                )
            }
        }
    }
}