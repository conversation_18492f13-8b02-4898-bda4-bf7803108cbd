package ai.friday.billpayment.app.wallet

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountConfigurationName
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.PixKeyRepository
import ai.friday.billpayment.app.integrations.WalletConfiguration
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.emailPixKeySanitize
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.handleError
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import java.time.Duration
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

sealed interface ReadInviteError

@FridayMePoupe
open class BackOfficeWalletService(
    private val accountService: AccountService,
    private val walletRepository: WalletRepository,
    private val eventPublisher: EventPublisher,
    @Property(name = "tenant.supportEmail") private val supportEmail: String,
    @Property(name = "tenant.chatbotName") private val chatbotName: String,
    @Property(name = "tenant.allowedOnboardingAssistantIds") private val allowedOnboardingAssistantIds: List<String>,
) {
    fun removeFromWallets(accountId: AccountId) {
        walletRepository.findWallets(accountId, MemberStatus.ACTIVE).forEach { wallet ->
            val member = wallet.getMember(accountId)
            if (member.type != MemberType.FOUNDER) {
                walletRepository.upsertMember(wallet.id, member.copy(status = MemberStatus.REMOVED))
                eventPublisher.publish(
                    event = MemberRemoved(
                        walletId = wallet.id,
                        member = accountId,
                        actionSource = ActionSource.System,
                    ),
                )
            }
        }
    }

    fun closeAndRemoveMembers(wallet: Wallet) {
        wallet
            .unsetFromDefaultWalletId()
            .removeAllMembers()
            .close()
    }

    fun getMember(walletId: WalletId, memberAccountId: AccountId): Either<UpdateMemberPermissionErrors, Member> {
        val wallet = walletRepository.findWalletOrNull(walletId) ?: return UpdateMemberPermissionErrors.WalletNotFound.left()
        val member = wallet.getActiveMemberOrNull(memberAccountId) ?: return UpdateMemberPermissionErrors.MemberNotFound.left()

        return member.right()
    }

    fun updateMemberPermissions(walletId: WalletId, member: Member, permissions: MemberPermissions): Either<UpdateMemberPermissionErrors, Unit> {
        return try {
            walletRepository.upsertMember(walletId, member.copy(permissions = permissions))
            Unit.right()
        } catch (t: Throwable) {
            UpdateMemberPermissionErrors.ErrorUpdatingMember.left()
        }
    }

    fun updateWallet(walletId: WalletId, name: String): Wallet {
        return walletRepository.findWallet(walletId).copy(name = name).let {
            walletRepository.save(it)
        }
    }

    fun addOnboardingAssistant(walletId: WalletId, assistantAccountId: AccountId): Result<AddOnboardingAssistantResult> {
        return runCatching { doAddOnboardingAssistant(walletId = walletId, assistantAccountId = assistantAccountId) }
    }

    private fun doAddOnboardingAssistant(walletId: WalletId, assistantAccountId: AccountId): AddOnboardingAssistantResult {
        val assistant = accountService.findAccountByIdOrNull(assistantAccountId)
            ?: return AddOnboardingAssistantResult.AssistantNotFound

        val wallet = walletRepository.findWalletOrNull(walletId)
            ?: return AddOnboardingAssistantResult.WalletNotFound

        if (!allowedOnboardingAssistantIds.contains(assistantAccountId.value) || wallet.founder.accountId == assistantAccountId) {
            return AddOnboardingAssistantResult.CannotBeAssistant
        }

        if (wallet.activeMembers.any { it.type == MemberType.ASSISTANT } || wallet.hasActiveMember(assistantAccountId)) {
            return AddOnboardingAssistantResult.LikelyHasOtherAssistant
        }

        walletRepository.createMember(
            walletId = walletId,
            member = Member(
                accountId = assistant.accountId,
                document = wallet.founder.document,
                name = chatbotName,
                emailAddress = EmailAddress(supportEmail),
                type = MemberType.ASSISTANT,
                status = MemberStatus.ACTIVE,
                permissions = MemberPermissions.of(MemberType.ASSISTANT)
                    .copy(manageMembers = true, notification = false),
                created = getZonedDateTime(),
                updated = getZonedDateTime(),
            ),
        )

        eventPublisher.publish(
            event = MemberAdded(
                walletId = walletId,
                member = assistant.accountId,
                actionSource = ActionSource.Api(wallet.founder.accountId),
            ),
        )

        return AddOnboardingAssistantResult.Added
    }

    fun removeOnboardingAssistant(walletId: WalletId, assistantAccountId: AccountId): Result<RemoveOnboardingAssistantResult> {
        return runCatching { doRemoveOnboardingAssistant(walletId = walletId, assistantAccountId = assistantAccountId) }
    }

    private fun doRemoveOnboardingAssistant(walletId: WalletId, assistantAccountId: AccountId): RemoveOnboardingAssistantResult {
        val wallet = walletRepository.findWalletOrNull(walletId)
            ?: return RemoveOnboardingAssistantResult.WalletNotFound

        val member = wallet.getActiveMemberOrNull(assistantAccountId)
            ?: return RemoveOnboardingAssistantResult.AssistantNotFound

        if (member.type != MemberType.ASSISTANT) {
            return RemoveOnboardingAssistantResult.AssistantNotFound
        }

        walletRepository.upsertMember(walletId, member.copy(status = MemberStatus.REMOVED))

        eventPublisher.publish(
            event = MemberRemoved(
                walletId = walletId,
                member = assistantAccountId,
                actionSource = ActionSource.System,
            ),
        )

        return RemoveOnboardingAssistantResult.Removed
    }

    private fun Wallet.unsetFromDefaultWalletId(): Wallet {
        activeMembers.forEach { activeMember ->
            val account = accountService.findAccountById(activeMember.accountId)
            if (account.configuration.defaultWalletId == id) {
                accountService.updateAccountConfig(
                    accountId = activeMember.accountId,
                    name = AccountConfigurationName.DEFAULT_WALLET_ID,
                    value = activeMember.accountId.value,
                ).handleError {
                    LOG.error(
                        append("ACTION", "VERIFY").andAppend(
                            "activeMember",
                            activeMember.accountId,
                        ),
                        "removeFromWallets",
                    )
                }
            }
        }
        return this
    }

    private fun Wallet.removeAllMembers(): Wallet {
        val membersList = allMembers.map { member ->
            eventPublisher.publish(
                event = MemberRemoved(
                    walletId = this.id,
                    member = member.accountId,
                    actionSource = ActionSource.System,
                ),
            )
            if (member.status == MemberStatus.ACTIVE) member.copy(status = MemberStatus.REMOVED) else member
        }
        return this.copy(members = membersList)
    }

    private fun Wallet.close(): Wallet {
        return walletRepository.save(this.copy(status = WalletStatus.CLOSED))
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BackOfficeWalletService::class.java)
    }
}

sealed class AddOnboardingAssistantResult : PrintableSealedClassV2() {
    object Added : AddOnboardingAssistantResult()
    object WalletNotFound : AddOnboardingAssistantResult()
    object AssistantNotFound : AddOnboardingAssistantResult()
    object LikelyHasOtherAssistant : AddOnboardingAssistantResult()
    object CannotBeAssistant : AddOnboardingAssistantResult()
}

sealed class RemoveOnboardingAssistantResult : PrintableSealedClassV2() {
    object Removed : RemoveOnboardingAssistantResult()
    object WalletNotFound : RemoveOnboardingAssistantResult()
    object AssistantNotFound : RemoveOnboardingAssistantResult()
}

@Singleton
open class WalletService(
    private val accountService: AccountService,
    private val walletRepository: WalletRepository,
    private val configuration: WalletConfiguration,
    private val notificationAdapter: NotificationAdapter,
    private val eventPublisher: EventPublisher,
    private val crmService: CrmService,
    private val pixKeyRepository: PixKeyRepository,
) {
    @field:Property(name = "accountRegister.pixKey.emailDomain")
    lateinit var defaultPixKeyDomain: String

    open fun findWallets(accountId: AccountId, memberStatus: MemberStatus? = MemberStatus.ACTIVE): List<Wallet> {
        return walletRepository.findWallets(accountId, memberStatus)
    }

    fun findWallet(founderAccountId: AccountId, walletPaymentMethodId: AccountPaymentMethodId): Wallet {
        // TODO tratar quando não for single - lançar exceção específica ?
        return findWallets(founderAccountId)
            .single { it.founder.accountId == founderAccountId && it.paymentMethodId == walletPaymentMethodId }
    }

    fun save(wallet: Wallet): Wallet {
        return walletRepository.save(wallet)
    }

    fun createInvite(
        accountId: AccountId,
        walletId: WalletId,
        document: String,
        name: String,
        type: MemberType,
        email: EmailAddress,
        permissions: MemberPermissions,
    ): Either<InviteErrors, Invite> {
        try {
            val wallet = walletRepository.findWallet(walletId)

            if (!wallet.hasManageMembersPermission(accountId)) {
                return Either.Left(InviteErrors.UserForbidden)
            }

            if (type == MemberType.FOUNDER) {
                return InviteErrors.InviteFounderNotAllowed.left()
            }

            val invites = walletRepository.findInvitesFrom(wallet.id, configuration.inviteExpiration)

            if (invites.filter { it.status == InviteStatus.PENDING }.size >= wallet.maxOpenInvitations) {
                return Either.Left(InviteErrors.PendingInvitationsLimitReached)
            }

            invites.firstOrNull { it.memberDocument == document && it.status == InviteStatus.PENDING }?.let {
                return Either.Left(InviteErrors.UserAlreadyInvited(it))
            }

            if (wallet.hasActiveMember(document)) {
                return Either.Left(InviteErrors.UserAlreadyMember)
            }

            val invite = Invite(
                walletId = walletId,
                memberDocument = document,
                memberName = name,
                memberType = type,
                status = InviteStatus.PENDING,
                validUntil = getZonedDateTime().toLocalDate().plusDays(configuration.inviteExpiration.toDays()),
                walletName = wallet.name,
                founderName = wallet.founder.name,
                founderDocument = wallet.founder.document,
                created = getZonedDateTime(),
                emailAddress = email,
                permissions = permissions,
            )

            walletRepository.save(invite)
            notificationAdapter.notifyInvitedMember(invite, receiver = email)

            return Either.Right(invite)
        } catch (e: ItemNotFoundException) {
            return Either.Left(InviteErrors.ItemNotFound)
        } catch (e: Exception) {
            return Either.Left(InviteErrors.ServerError(e))
        }
    }

    fun listInvites(walletId: WalletId): Either<InviteErrors, List<Invite>> {
        return try {
            val cutOffDate = Duration.ofDays(7)
            val maxInviteTimeQuery: Duration = configuration.inviteExpiration + cutOffDate

            walletRepository.findInvitesFrom(walletId, maxInviteTimeQuery)
                .distinctBy { it.memberDocument }
                .filter {
                    when (it.status) {
                        InviteStatus.ACCEPTED, InviteStatus.CANCELED -> false
                        InviteStatus.EXPIRED, InviteStatus.DECLINED -> it.updatedAt.isAfter(
                            getZonedDateTime().minus(cutOffDate),
                        )

                        InviteStatus.PENDING -> it.isValid()
                    }
                }.right()
        } catch (e: ItemNotFoundException) {
            InviteErrors.ItemNotFound.left()
        }
    }

    fun listPendingInvites(document: String): Either<InviteErrors.ServerError, List<Invite>> {
        return try {
            walletRepository.findInvites(document, InviteStatus.PENDING).filter { it.isValid() }.right()
        } catch (e: Exception) {
            InviteErrors.ServerError(e).left()
        }
    }

    fun findPendingInvite(walletId: WalletId, document: String): Either<ReadInviteError, Invite> {
        return try {
            val invite = walletRepository.findInvite(walletId, document)

            if (invite.status != InviteStatus.PENDING) {
                InviteErrors.InviteNotPending.left()
            } else {
                invite.right()
            }
        } catch (e: ItemNotFoundException) {
            InviteErrors.ItemNotFound.left()
        } catch (e: Exception) {
            InviteErrors.ServerError(e).left()
        }
    }

    fun answerInvite(account: Account, walletId: WalletId, answer: InviteAnswer): Either<InviteErrors, Invite> {
        try {
            val invite = walletRepository.findInvite(walletId, account.document)

            if (invite.status != InviteStatus.PENDING) {
                return InviteErrors.InviteNotPending.left()
            }

            val wallet = walletRepository.findWallet(walletId)
            val memberAlreadyAdded = wallet.hasActiveMember(account.accountId)

            val inviteStatus = if (answer == InviteAnswer.ACCEPT) {
                walletRepository.createMember(
                    walletId = walletId,
                    member = Member(
                        accountId = account.accountId,
                        document = account.document,
                        name = account.name,
                        emailAddress = account.emailAddress,
                        type = invite.memberType,
                        status = MemberStatus.ACTIVE,
                        permissions = invite.permissions,
                        created = getZonedDateTime(),
                        updated = getZonedDateTime(),
                    ),
                )

                notificationAdapter.notifyWalletMemberJoined(
                    founder = wallet.founder,
                    inviteeFullName = account.name,
                    walletName = invite.walletName,
                )

                if (!memberAlreadyAdded) {
                    eventPublisher.publish(
                        event = MemberAdded(
                            walletId = walletId,
                            member = account.accountId,
                            actionSource = ActionSource.Api(wallet.founder.accountId),
                        ),
                    )
                }

                try {
                    crmService.upsertContact(account)
                    val founderAccount = accountService.findAccountById((wallet.founder.accountId))
                    crmService.upsertContact(founderAccount)
                } catch (e: Exception) {
                    // ignore - o crmService já faz o log
                }

                InviteStatus.ACCEPTED
            } else {
                InviteStatus.DECLINED
            }
            val now = getZonedDateTime()
            val acceptedInvite = invite.copy(
                status = inviteStatus,
                updatedAt = now,
            )
            walletRepository.save(acceptedInvite)

            return acceptedInvite.right()
        } catch (e: ItemNotFoundException) {
            return InviteErrors.ItemNotFound.left()
        } catch (e: Exception) {
            return InviteErrors.ServerError(e).left()
        }
    }

    fun cancelInvite(walletId: WalletId, document: String): Either<InviteErrors, Unit> {
        try {
            val invite = walletRepository.findInvite(walletId, document)
            if (invite.status != InviteStatus.PENDING && invite.status != InviteStatus.CANCELED) {
                return InviteErrors.InviteNotPending.left()
            } else {
                if (invite.status == InviteStatus.PENDING) {
                    walletRepository.save(invite.copy(status = InviteStatus.CANCELED))
                }
                return Unit.right()
            }
        } catch (e: ItemNotFoundException) {
            return InviteErrors.ItemNotFound.left()
        }
    }

    open fun findWallet(walletId: WalletId): Wallet {
        return walletRepository.findWallet(walletId)
    }

    open fun findWalletOrNull(walletId: WalletId): Wallet? { // TODO - acho que a nivel de serviço não deveria existir!
        return walletRepository.findWalletOrNull(walletId)
    }

    open fun findAllFounderWallets(accountId: AccountId): List<Wallet> {
        return walletRepository.findWallets(
            accountId = accountId,
            memberStatus = MemberStatus.ACTIVE,
        ).filter { wallet -> wallet.founder.accountId == accountId }
    }

    fun walletMailBox(wallet: Wallet): EmailAddress {
        return when (wallet.type) {
            WalletType.PRIMARY -> EmailAddress("${wallet.founder.document}@$defaultPixKeyDomain")
            WalletType.SECONDARY -> EmailAddress(emailPixKeySanitize("${wallet.name}.${wallet.founder.document}@$defaultPixKeyDomain"))
        }
    }

    fun walletPixKey(wallet: Wallet): PixKey {
        return PixKey(value = walletMailBox(wallet).value, type = PixKeyType.EMAIL)
    }

    fun walletEvpPixKey(walletId: WalletId): PixKey? {
        return pixKeyRepository.list(walletId).firstOrNull {
            it.type === PixKeyType.EVP
        }
    }

    fun walletPixKeys(wallet: Wallet): WalletPixKeys {
        return WalletPixKeys(
            email = this.walletPixKey(wallet),
            evp = this.walletEvpPixKey(wallet.id),
        )
    }

    fun authorizeMember(accountId: AccountId, walletId: WalletId): Either<InviteErrors, Unit> {
        try {
            val wallet = walletRepository.findWallet(walletId)
            return if (!wallet.hasActiveMember(accountId)) {
                InviteErrors.UserForbidden.left()
            } else {
                Unit.right()
            }
        } catch (e: ItemNotFoundException) {
            return InviteErrors.ItemNotFound.left()
        }
    }

    fun authorizeManageMembers(accountId: AccountId, walletId: WalletId): Either<InviteErrors, Unit> {
        return try {
            val wallet = walletRepository.findWallet(walletId)

            if (!wallet.hasManageMembersPermission(accountId)) {
                return InviteErrors.UserForbidden.left()
            }

            return Unit.right()
        } catch (e: ItemNotFoundException) {
            InviteErrors.ItemNotFound.left()
        }
    }

    fun createPrimaryWallet(account: Account, accountPaymentMethodId: AccountPaymentMethodId): Wallet {
        val founder = createFounder(account)

        val wallet = Wallet(
            id = WalletId(account.accountId.value), // TODO - já deve poder gerar outro id.
            name = founder.simpleName(),
            members = listOf(founder),
            maxOpenInvitations = configuration.maxOpenInvites,
            status = WalletStatus.ACTIVE,
            type = WalletType.PRIMARY,
            paymentMethodId = accountPaymentMethodId,
        )

        walletRepository.save(wallet)

        return wallet
    }

    open fun findPrimaryWallet(accountId: AccountId, walletStatus: WalletStatus? = WalletStatus.ACTIVE): Wallet {
        val memberStatus = when (walletStatus) {
            WalletStatus.ACTIVE -> MemberStatus.ACTIVE
            WalletStatus.CLOSED -> MemberStatus.REMOVED
            null -> null
        }

        val wallets = findWallets(accountId, memberStatus)

        return wallets.single {
            it.checkPrimary(accountId, walletStatus)
        }
    }

    fun removeMember(
        walletId: WalletId,
        source: ActionSource,
        targetAccountId: AccountId,
    ): Either<RemoveMemberErrors, Unit> {
        try {
            val wallet = findWalletOrNull(walletId)
                ?: return RemoveMemberErrors.WalletNotFound.left()

            if (source !is ActionSource.System && source !is ActionSource.Api) {
                return RemoveMemberErrors.InvalidSource.left()
            }

            if (source is ActionSource.Api) {
                if (!wallet.hasManageMembersPermission(source.accountId) && source.accountId != targetAccountId) {
                    return RemoveMemberErrors.AccountHasNotPermissionToRemoveMembers.left()
                }

                if (wallet.getPersonType() == WalletPersonType.LEGAL_PERSON && targetAccountId isCoFounder wallet) {
                    return RemoveMemberErrors.CoFounderCannotBeRemovedFromLegalPersonWallet.left()
                }
            }

            if (targetAccountId isFounder wallet) {
                return RemoveMemberErrors.FounderCannotBeRemoved.left()
            }

            val member =
                wallet.getActiveMemberOrNull(targetAccountId) ?: return RemoveMemberErrors.MemberNotFound.left()

            walletRepository.upsertMember(walletId, member.copy(status = MemberStatus.REMOVED))

            val targetAccount = accountService.findAccountById(targetAccountId)
            if (targetAccount.configuration.defaultWalletId == walletId) {
                val primaryWallet = findPrimaryWallet(targetAccountId)
                accountService.updateAccountConfig(
                    accountId = targetAccountId,
                    name = AccountConfigurationName.DEFAULT_WALLET_ID,
                    value = primaryWallet.id.value,
                )
            }

            try {
                crmService.upsertContact(targetAccount)
                val founderAccount = accountService.findAccountById((wallet.founder.accountId))
                crmService.upsertContact(founderAccount)
            } catch (e: Exception) {
                // ignore - o crmService já faz o log
            }

            eventPublisher.publish(
                event = MemberRemoved(
                    walletId = walletId,
                    member = targetAccountId,
                    actionSource = source,
                ),
            )
            return Unit.right()
        } catch (e: Exception) {
            return RemoveMemberErrors.Unknown(e).left()
        }
    }

    fun updateMember(command: UpdateMemberCommand): Either<UpdateMemberErrors, Unit> {
        return try {
            val wallet = walletRepository.findWalletOrNull(command.walletId)
                ?: return UpdateMemberErrors.WalletNotFound.left()

            val member = wallet.getActiveMemberOrNull(command.memberAccountId)
                ?: return UpdateMemberErrors.MemberNotFound.left()

            if (!wallet.hasManageMembersPermission(command.requesterAccountId)) {
                return UpdateMemberErrors.AccountHasNotPermissionToUpdateMembers.left()
            }

            if (command.requesterAccountId == command.memberAccountId) {
                return UpdateMemberErrors.MemberCantUpdateItself.left()
            }

            walletRepository.upsertMember(
                wallet.id,
                member.updatePermissions(command.options),
            )

            Unit.right()
        } catch (e: Exception) {
            return UpdateMemberErrors.Unknown(e).left()
        }
    }

    private fun createFounder(account: Account) = Member(
        accountId = account.accountId,
        document = account.document,
        name = account.name,
        emailAddress = account.emailAddress,
        type = MemberType.FOUNDER,
        status = MemberStatus.ACTIVE,
        permissions = MemberPermissions.of(MemberType.FOUNDER),
        created = getZonedDateTime(),
        updated = getZonedDateTime(),
    )

    companion object {
        private val LOG = LoggerFactory.getLogger(WalletService::class.java)
    }
}

data class UpdateMemberCommand(
    val requesterAccountId: AccountId,
    val memberAccountId: AccountId,
    val walletId: WalletId,
    val options: CustomPermissionsOptions,
)

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY)
@JsonSubTypes(
    JsonSubTypes.Type(value = MemberRemoved::class, name = "MemberRemoved"),
)
sealed interface WalletEvent {
    val walletId: WalletId
    val created: Long
    val eventType: WalletEventType
    val actionSource: ActionSource
}

enum class WalletEventType {
    CREATED, WALLET_UPDATED, MEMBER_ADDED, MEMBER_REMOVED, MEMBER_UPDATED, MEMBER_EXITED, WALLET_CLOSED
}

data class MemberRemoved(
    override val walletId: WalletId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    val member: AccountId,
    override val actionSource: ActionSource,
) : WalletEvent {
    override val eventType: WalletEventType = WalletEventType.MEMBER_REMOVED
}

data class MemberAdded(
    override val walletId: WalletId,
    override val created: Long = getZonedDateTime().toInstant().toEpochMilli(),
    val member: AccountId,
    override val actionSource: ActionSource,
) : WalletEvent {
    override val eventType: WalletEventType = WalletEventType.MEMBER_ADDED
}

data class WalletPixKeys(
    val email: PixKey,
    val evp: PixKey?,
)