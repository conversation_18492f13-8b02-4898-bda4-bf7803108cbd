package ai.friday.billpayment.app.wallet

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.time.ZonedDateTime

private const val HOURS_TO_ACTIVATE_LIMIT = 24L

data class WalletPaymentLimitsWithUsage(
    val limits: WalletPaymentLimits,
    val usage: WalletLimitsUsage,
    val editable: Boolean,
)

data class WalletLimitsUsage(
    val daily: Long,
    val nighttime: Long,
    val monthly: MonthlyLimitUsage?,
    val automaticPix: Long,
)

data class MonthlyLimitUsage(
    val used: Long,
    val forecasted: Long,
)

data class StoredWalletPaymentLimits(
    val automaticPix: DailyPaymentLimit?,
    val daily: DailyPaymentLimit?,
    val nighttime: DailyPaymentLimit?,
    val monthly: DailyPaymentLimit?,
    val whatsAppPayment: DailyPaymentLimit?,
)

data class WalletPaymentLimits(
    val daily: DailyPaymentLimit,
    val nighttime: DailyPaymentLimit,
    val monthly: DailyPaymentLimit?,
    val whatsAppPayment: DailyPaymentLimit,
    val automaticPix: DailyPaymentLimit,
) {
    fun get(type: DailyPaymentLimitType): DailyPaymentLimit? {
        return when (type) {
            DailyPaymentLimitType.AUTOMATIC_PIX -> automaticPix
            DailyPaymentLimitType.DAILY -> daily
            DailyPaymentLimitType.MONTHLY -> monthly
            DailyPaymentLimitType.NIGHTTIME -> nighttime
            DailyPaymentLimitType.WHATSAPP_PAYMENT -> whatsAppPayment
        }
    }
}

data class DailyPaymentLimit(
    val updatedAt: ZonedDateTime = getZonedDateTime(),
    private val amount: Long,
    val lastAmount: Long? = null,
    val type: DailyPaymentLimitType,
) {
    val isPending = when {
        lastAmount == null -> false
        !type.hasCooldown -> false
        amount > lastAmount && getZonedDateTime()
            .isBefore(updatedAt.plusHours(HOURS_TO_ACTIVATE_LIMIT)) -> true

        else -> false
    }

    val activeAmount: Long = when {
        lastAmount == null -> amount // smart cast purpose
        isPending -> lastAmount
        else -> amount
    }

    val nextAmount: Long? = when {
        isPending -> amount
        else -> null
    }
}

enum class DailyPaymentLimitType(val hasCooldown: Boolean = true) {
    NIGHTTIME, DAILY, MONTHLY, WHATSAPP_PAYMENT(hasCooldown = false), AUTOMATIC_PIX(hasCooldown = false)
}