package ai.friday.billpayment.app.wallet

import ai.friday.billpayment.PrintableSealedClassV2

sealed class UpdateLimitsErrors : PrintableSealedClassV2() {
    data object ItemNotFound : UpdateLimitsErrors()
    data object PendingLimitChange : UpdateLimitsErrors()
    data object UpdateLimitNotAllowed : UpdateLimitsErrors()
    data object NighttimeLimitGreaterThanDaily : UpdateLimitsErrors()
    data object InvalidAmount : UpdateLimitsErrors()
    data object LimitIsNotActive : UpdateLimitsErrors()
    data object WhatsAppPaymentLimitGreaterThanDaily : UpdateLimitsErrors()
    class UnknownError(val error: Exception) : UpdateLimitsErrors()
}

sealed class GetLimitsErrors {
    data object WalletNotFound : GetLimitsErrors()
    class UnknownError(val error: Exception) : GetLimitsErrors()
}

sealed class InviteErrors {
    data object UserForbidden : InviteErrors()
    data object PendingInvitationsLimitReached : InviteErrors()
    class UserAlreadyInvited(val invite: Invite) : InviteErrors()
    data object UserAlreadyMember : InviteErrors()
    data object InviteFounderNotAllowed : InviteErrors()
    data object ItemNotFound : InviteErrors(), ReadInviteError
    data object InviteNotPending : InviteErrors(), ReadInviteError
    class ServerError(val error: Exception) : InviteErrors(), ReadInviteError
}

sealed class CreateWalletErrors {
    data object MaxWalletsAllowed : CreateWalletErrors()
    data object WalletNameAlreadyExists : CreateWalletErrors()
    data object BasicAccountNotAllowed : CreateWalletErrors()
    data object RegisterNaturalPersonError : CreateWalletErrors()

    data object RegisterEvpPixKey : CreateWalletErrors()
}

sealed class RemoveMemberErrors() {
    data object WalletNotFound : RemoveMemberErrors()
    data object MemberNotFound : RemoveMemberErrors()
    data object AccountHasNotPermissionToRemoveMembers : RemoveMemberErrors()
    data object FounderCannotBeRemoved : RemoveMemberErrors()
    data object CoFounderCannotBeRemovedFromLegalPersonWallet : RemoveMemberErrors()
    data object InvalidSource : RemoveMemberErrors()
    class Unknown(val e: Exception) : RemoveMemberErrors()
}

sealed class UpdateMemberErrors() {
    data object WalletNotFound : UpdateMemberErrors()
    data object MemberNotFound : UpdateMemberErrors()
    data object AccountHasNotPermissionToUpdateMembers : UpdateMemberErrors()
    data object MemberCantUpdateItself : UpdateMemberErrors()
    class Unknown(val e: Exception) : UpdateMemberErrors()
}

sealed class UpdateMemberPermissionErrors {
    data object WalletNotFound : UpdateMemberPermissionErrors()
    data object MemberNotFound : UpdateMemberPermissionErrors()
    data object ErrorUpdatingMember : UpdateMemberPermissionErrors()
}