package ai.friday.billpayment.app.wallet

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillEventDependency
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.payerDocument
import ai.friday.billpayment.app.isValidCnpj
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.UUID

@BillEventDependency
data class WalletId(val value: String) {
    constructor() : this("WALLET-${UUID.randomUUID()}")
}

data class Invite(
    val walletId: WalletId,
    val memberDocument: String,
    val emailAddress: EmailAddress? = null,
    val memberName: String,
    val memberType: MemberType,
    val permissions: MemberPermissions = MemberPermissions.of(memberType),
    val status: InviteStatus,
    val validUntil: LocalDate,
    val walletName: String,
    val founderName: String,
    val founderDocument: String,
    val created: ZonedDateTime,
    val updatedAt: ZonedDateTime = created,
)

fun Invite.isValid(): Boolean {
    val localDate = getZonedDateTime().toLocalDate()
    return this.status == InviteStatus.PENDING && (localDate.isBefore(this.validUntil) || localDate.isEqual(this.validUntil))
}

enum class InviteStatus {
    PENDING, DECLINED, EXPIRED, ACCEPTED, CANCELED
}

enum class InviteAnswer {
    ACCEPT, DECLINE
}

enum class WalletStatus {
    ACTIVE, CLOSED
}

enum class WalletType {
    PRIMARY, SECONDARY
}

enum class WalletPersonType {
    LEGAL_PERSON, PHYSICAL_PERSON
}

data class MemberPermissions(
    val viewBills: BillPermission,
    val scheduleBills: BillPermission,
    val criteriaForViewingBills: CriteriaForViewingBills? = null,
    val founderContactsEnabled: Boolean = false,
    val manageMembers: Boolean = false,
    val viewBalance: Boolean,
    val notification: Boolean,
) {
    val cashinEnabled = canScheduleAny()

    companion object {

        private val DEFAULT_PERMISSIONS = mutableMapOf<MemberType, MemberPermissions>()

        fun of(memberType: MemberType): MemberPermissions {
            return DEFAULT_PERMISSIONS.getOrPut(memberType) {
                when (memberType) {
                    MemberType.ASSISTANT -> MemberPermissions(
                        viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                        scheduleBills = BillPermission.NO_BILLS,
                        founderContactsEnabled = true,
                        manageMembers = false,
                        viewBalance = false,
                        notification = true,
                    )

                    MemberType.COLLABORATOR -> MemberPermissions(
                        viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                        scheduleBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                        founderContactsEnabled = false,
                        manageMembers = false,
                        viewBalance = true,
                        notification = true,
                    )

                    MemberType.COFOUNDER -> MemberPermissions(
                        viewBills = BillPermission.ALL_BILLS,
                        scheduleBills = BillPermission.ALL_BILLS,
                        founderContactsEnabled = false,
                        manageMembers = true,
                        viewBalance = true,
                        notification = true,
                    )

                    MemberType.FOUNDER -> MemberPermissions(
                        viewBills = BillPermission.ALL_BILLS,
                        scheduleBills = BillPermission.ALL_BILLS,
                        founderContactsEnabled = false,
                        manageMembers = true,
                        viewBalance = true,
                        notification = true,
                    )
                }
            }
        }

        fun of(options: CustomPermissionsOptions): MemberPermissions {
            return of(options.memberType.toMemberType())
                .copy(
                    viewBills = when (options.viewBills) {
                        CustomViewBillPermission.ONLY_BILLS_ADDED_BY_USER -> BillPermission.ONLY_BILLS_ADDED_BY_USER
                        CustomViewBillPermission.ALL_BILLS -> BillPermission.ALL_BILLS
                    },
                )
        }
    }
}

enum class BillPermission {
    NO_BILLS, ONLY_BILLS_ADDED_BY_USER, ALL_BILLS, BY_CRITERIA;

    companion object {
        fun of(name: String): BillPermission? = entries.find { it.name == name }
    }
}

enum class CustomPermissionsMemberType {
    COFOUNDER, COLLABORATOR, ASSISTANT
}

fun CustomPermissionsMemberType.toMemberType(): MemberType {
    return when (this) {
        CustomPermissionsMemberType.COFOUNDER -> MemberType.COFOUNDER
        CustomPermissionsMemberType.COLLABORATOR -> MemberType.COLLABORATOR
        CustomPermissionsMemberType.ASSISTANT -> MemberType.ASSISTANT
    }
}

enum class CustomViewBillPermission {
    ONLY_BILLS_ADDED_BY_USER, ALL_BILLS
}

data class CustomPermissionsOptions(
    val memberType: CustomPermissionsMemberType,
    val viewBills: CustomViewBillPermission,
)

data class CriteriaForViewingBills(
    val criterias: List<BillViewingCriteria> = emptyList(),
)

data class Wallet(
    val id: WalletId = WalletId(value = "WALLET-${UUID.randomUUID()}"),
    val name: String,
    private val members: List<Member>,
    val maxOpenInvitations: Int,
    val status: WalletStatus,
    val type: WalletType,
    val paymentMethodId: AccountPaymentMethodId,
) {

    val activeMembers = members.filter { it.status == MemberStatus.ACTIVE }

    val allMembers = members

    val founder = members.single { it.type == MemberType.FOUNDER }

    fun hasManageMembersPermission(accountId: AccountId) = members.find { it.accountId == accountId }?.permissions?.manageMembers ?: false

    fun hasActiveMember(accountId: AccountId) = activeMembers.any { it.accountId == accountId }

    fun hasActiveMember(document: String) = activeMembers.any { it.document == document }

    fun getActiveMember(accountId: AccountId) = activeMembers.first { it.accountId == accountId }

    fun getActiveMemberOrNull(accountId: AccountId) = activeMembers.firstOrNull { it.accountId == accountId }

    fun getMember(accountId: AccountId) = allMembers.first { it.accountId == accountId }

    fun getMembersCanView(bill: Bill) = activeMembers.filter { it.canView(bill) }

    fun getMembersCanView(bill: BillView) = activeMembers.filter { it.canView(bill) }

    fun getMembersCanViewAll() = activeMembers.filter { it.canViewAll() }

    fun getMembersCanScheduleAny() = activeMembers.filter { it.canScheduleAny() }

    fun getMembersCanViewBalance() = activeMembers.filter { it.permissions.viewBalance }

    fun getMembersCanSchedule(bill: BillView) = activeMembers.filter { it.canSchedule(actionSource = bill.source, visibleBy = bill.visibleBy) }

    fun getPersonType() = if (founder.isLegalPerson()) WalletPersonType.LEGAL_PERSON else WalletPersonType.PHYSICAL_PERSON

    fun checkPrimary(accountId: AccountId, walletStatus: WalletStatus? = WalletStatus.ACTIVE) =
        founder.accountId == accountId &&
            type == WalletType.PRIMARY &&
            (walletStatus == null || status == walletStatus)
}

enum class MemberType {
    FOUNDER, COFOUNDER, COLLABORATOR, ASSISTANT
}

enum class MemberStatus {
    ACTIVE, REMOVED
}

data class Member(
    val accountId: AccountId,
    val document: String,
    val name: String,
    val emailAddress: EmailAddress,
    val type: MemberType,
    val permissions: MemberPermissions,
    val status: MemberStatus,
    val created: ZonedDateTime,
    val updated: ZonedDateTime,
)

fun Member.updatePermissions(options: CustomPermissionsOptions): Member {
    return copy(
        type = options.memberType.toMemberType(),
        permissions = MemberPermissions.of(options),
    )
}

fun Member.canViewAll() = permissions.viewBills == BillPermission.ALL_BILLS

fun Member?.canView(bill: BillView): Boolean {
    return this == null || canView(toBillFilterCriteria(bill))
}

fun Member?.canView(bill: Bill): Boolean {
    return this == null || canView(toBillFilterCriteria(bill))
}

fun Member.simpleName(): String =
    "${name.substringBefore(' ')} ${name.substringAfterLast(delimiter = ' ', missingDelimiterValue = "")}".trimEnd()

fun Member.canScheduleAny(): Boolean = permissions.canScheduleAny()

fun MemberPermissions.canScheduleAny(): Boolean = when (scheduleBills) {
    BillPermission.ALL_BILLS, BillPermission.ONLY_BILLS_ADDED_BY_USER -> true
    BillPermission.NO_BILLS -> false
    BillPermission.BY_CRITERIA -> false // TODO implement ????
}

fun Member.canSchedule(actionSource: ActionSource, visibleBy: List<AccountId>): Boolean = when (permissions.scheduleBills) {
    BillPermission.ALL_BILLS -> true
    BillPermission.ONLY_BILLS_ADDED_BY_USER -> (actionSource is ActionSource.WalletActionSource && accountId == actionSource.accountId) || (this.permissions.scheduleBills == BillPermission.ONLY_BILLS_ADDED_BY_USER && this.accountId in visibleBy)
    BillPermission.NO_BILLS -> false
    BillPermission.BY_CRITERIA -> permissions.criteriaForViewingBills?.canView(
        BillFilterCriteria(
            visibleBy = visibleBy,
            payerDocument = null,
            source = actionSource,
        ),
    ) ?: false
}

fun Member.isLegalPerson(): Boolean {
    return Document(this.document).isValidCnpj()
}

infix fun AccountId.isFounder(wallet: Wallet) = this == wallet.founder.accountId

infix fun AccountId.isCoFounder(wallet: Wallet): Boolean {
    val member = wallet.getActiveMemberOrNull(this)

    return member?.type == MemberType.COFOUNDER
}

fun Bill.wasAddedBy(member: Member): Boolean {
    return when (val source = this.source) {
        is ActionSource.DDA -> this.payer?.document == member.document
        is ActionSource.WalletActionSource -> source.accountId == member.accountId
        else -> false
    }
}

fun Bill.findAuthor(wallet: Wallet): Member {
    return wallet.allMembers.single { wasAddedBy(it) }
}

fun Bill.findAuthorOrNull(wallet: Wallet): Member? {
    return wallet.allMembers.singleOrNull { wasAddedBy(it) }
}

fun BillView.findAuthorOrNull(wallet: Wallet): Member? {
    return wallet.allMembers.singleOrNull { wasAddedBy(it) }
}

private fun BillView.wasAddedBy(member: Member): Boolean {
    return when (val source = this.source) {
        is ActionSource.DDA -> this.payerDocument == member.document
        is ActionSource.WalletActionSource -> source.accountId == member.accountId
        else -> false
    }
}

fun MemberPermissions.canViewStatement() = viewBalance && viewBills == BillPermission.ALL_BILLS

fun Member.canView(
    bill: BillFilterCriteria,
): Boolean {
    if (this.document == bill.payerDocument || this.accountId in bill.visibleBy) {
        return true
    }

    return when (permissions.viewBills) {
        BillPermission.ALL_BILLS -> true
        BillPermission.ONLY_BILLS_ADDED_BY_USER -> bill.source is ActionSource.WalletActionSource && accountId == bill.source.accountId
        BillPermission.NO_BILLS -> false
        BillPermission.BY_CRITERIA -> permissions.criteriaForViewingBills?.canView(bill) ?: false
    }
}

private fun toBillFilterCriteria(bill: Bill) = BillFilterCriteria(
    visibleBy = bill.visibleBy,
    payerDocument = bill.payerDocument(),
    source = bill.source,
)

private fun toBillFilterCriteria(bill: BillView) = BillFilterCriteria(
    visibleBy = bill.visibleBy,
    payerDocument = bill.payerDocument,
    source = bill.source,
)

private fun CriteriaForViewingBills.canView(bill: BillFilterCriteria) = criterias
    .groupBy { it::class }
    .all { (_, criterias) -> criterias.any { it.filter(bill) } }