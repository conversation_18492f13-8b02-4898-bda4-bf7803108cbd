package ai.friday.billpayment.app

import ai.friday.billpayment.app.banking.Bank
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.integrations.BankDataService
import ai.friday.billpayment.app.integrations.FinancialInstitutionGlobalDataRepository
import ai.friday.billpayment.app.job.AbstractJob
import io.micronaut.context.annotation.Context
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import jakarta.annotation.PostConstruct
import jakarta.inject.Singleton
import java.time.DayOfWeek
import java.time.Duration
import java.time.LocalDate
import org.slf4j.LoggerFactory

@Singleton
@Requires(beans = [FinancialInstitutionGlobalData::class])
open class RefreshFinancialInstitutionGlobalData(private val financialInstitutionGlobalData: FinancialInstitutionGlobalData) :
    AbstractJob(fixedDelay = Duration.ofHours(1), shouldLock = false) {
    override fun execute() {
        financialInstitutionGlobalData.loadData()
    }
}

@Context
@Requires(notEnv = ["test"])
class FinancialInstitutionGlobalData(private val repository: FinancialInstitutionGlobalDataRepository) {
    private val logger = LoggerFactory.getLogger(FinancialInstitutionGlobalData::class.java)

    @PostConstruct
    fun loadData() {
        try {
            pixParticipants = repository.loadPixParticipants()
            holidays = repository.loadHolidays()
            bankList = repository.loadBankList()

            val compeMap = bankList.associate { FinancialIdentifier.COMPE(it.number).id() to it.name }
            val ispbMap = pixParticipants
                .filter { it.ispb != null }
                .associate { FinancialIdentifier.ISPB(it.ispb!!.toLong()).id() to it.name }

            bankMap = compeMap + ispbMap
            logger.info("FinancialInstitutionGlobalData")
        } catch (ex: Exception) {
            logger.error("FinancialInstitutionGlobalData", ex)
        }
    }

    companion object {
        var pixParticipants: List<FinancialInstitution> = emptyList()
            private set
        var holidays: List<LocalDate> = emptyList()
            private set
        var bankList: List<Bank> = emptyList()
            private set

        private var bankMap: Map<String, String> = emptyMap()

        fun cleanData() {
            pixParticipants = emptyList()
            holidays = emptyList()
            bankList = emptyList()
            bankMap = emptyMap()
        }

        fun isBusinessDay(localDate: LocalDate): Boolean {
            return localDate !in holidays && localDate.dayOfWeek !in listOf(
                DayOfWeek.SATURDAY,
                DayOfWeek.SUNDAY,
            )
        }

        fun isHoliday(localDate: LocalDate) = holidays.contains(localDate)

        fun getInstitutionByCode(identifier: FinancialIdentifier): String? = bankMap["${identifier.id()}"]
    }
}

@Singleton
@Requirements(
    Requires(notEnv = ["test"]),
    Requires(beans = [BankDataService::class]),
)
open class UpdateFinancialInstitutionGlobalData(
    private val bankDataService: BankDataService,
    private val repository: FinancialInstitutionGlobalDataRepository,
) : AbstractJob(fixedDelay = Duration.ofDays(1)) {

    private val logger = LoggerFactory.getLogger(UpdateFinancialInstitutionGlobalData::class.java)

    override fun execute() {
        logger.info("UpdateFinancialInstitutionGlobalData#getPixParticipants")
        runCatching {
            bankDataService.getPixParticipants().takeIf { it.isNotEmpty() }?.let {
                repository.savePixParticipants(it)
            }
        }
        logger.info("UpdateFinancialInstitutionGlobalData#getHolidays")
        runCatching {
            val holidays = FinancialInstitutionGlobalData.holidays
            bankDataService.getHolidays().takeIf { it.isNotEmpty() }?.let {
                val newHolidaysList = (holidays + it).distinct()

                repository.saveHolidays(newHolidaysList)
            }
        }
        logger.info("UpdateFinancialInstitutionGlobalData#getBankCodes")
        runCatching {
            bankDataService.getBankCodes().takeIf { it.isNotEmpty() }?.let {
                repository.saveBankList(it)
            }
        }
        logger.info("UpdateFinancialInstitutionGlobalData#finished")
    }
}

sealed interface FinancialIdentifier {
    data class COMPE(var id: Long) : FinancialIdentifier
    data class ISPB(var id: Long) : FinancialIdentifier

    fun id() = when (this) {
        is COMPE -> id.toString().padStart(3, '0')
        is ISPB -> id.toString().padStart(8, '0')
    }
}