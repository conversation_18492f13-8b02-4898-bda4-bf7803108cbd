package ai.friday.billpayment.app

import io.micronaut.http.client.exceptions.HttpClientResponseException

sealed class FindError {
    data object NotFound : FindError()
    data object MultipleItemsFound : FindError()
    class ServerError(val exception: Exception) : FindError()
}

sealed class IntegrationError {
    class ClientError(val e: HttpClientResponseException? = null) : IntegrationError()
    class ServerError(val e: Exception? = null) : IntegrationError()
}