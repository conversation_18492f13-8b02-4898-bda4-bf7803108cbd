package ai.friday.billpayment.app.documentscan

import ai.friday.billpayment.app.account.AccountId
import arrow.core.Either

interface DocumentScanRepository {
    fun upsertDocumentScanId(accountId: AccountId): Either<UpsertDocumentScanIdError, DocumentScanId>

    fun getResult(id: DocumentScanId): Either<GetDocumentScanResultError, DocumentScanResult>

    fun getImage(id: DocumentScanId): Either<GetImageError, DocumentScanImage>
}