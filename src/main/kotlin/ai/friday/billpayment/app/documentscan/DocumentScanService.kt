package ai.friday.billpayment.app.documentscan

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.DocumentScan
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.account.isOpen
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right

@FridayMePoupe
class DocumentScanService(
    private val documentScanRepository: DocumentScanRepository,
    private val registerRepository: AccountRegisterRepository,
    private val accountRepository: AccountRepository,
) {
    fun upsertDocumentScanId(accountId: AccountId, documentType: DocumentType, role: Role): Either<UpsertDocumentScanIdError, DocumentScanId> {
        when (role) {
            Role.OWNER -> {
                val account = accountRepository.findByIdOrNull(accountId) ?: return UpsertDocumentScanIdError.AccountNotFound.left()
                if (account.status != AccountStatus.REGISTER_INCOMPLETE && !account.isOpen()) {
                    return UpsertDocumentScanIdError.IllegalAccountStatus.left()
                }
                if (account.type != UserAccountType.BASIC_ACCOUNT) {
                    return UpsertDocumentScanIdError.IllegalAccountType.left()
                }
            }

            Role.GUEST -> {
                val account = accountRepository.findPartialAccountByIdOrNull(accountId) ?: return UpsertDocumentScanIdError.AccountNotFound.left()
                if (account.status != AccountStatus.REGISTER_INCOMPLETE) {
                    return UpsertDocumentScanIdError.IllegalAccountStatus.left()
                }

                if (account.registrationType != RegistrationType.UPGRADED) {
                    return UpsertDocumentScanIdError.IllegalAccountType.left()
                }
            }

            Role.ASSISTANT,
            Role.INTERNAL,
            Role.BACKOFFICE,
            Role.BACKOFFICE_ADMIN,
            Role.CELCOIN_CALLBACK,
            Role.ARBI_CALLBACK,
            Role.FRIDAY_CALLBACK,
            Role.INTERCOM_CALLBACK,
            Role.MODATTA_B2B,
            Role.MODATTA_CORP_CALLBACK,
            Role.DEPENDENT,
            Role.ADMIN,
            Role.GUEST_OTP,
            Role.REVENUE_CAT_CALLBACK,
            Role.VEHICLE_DEBTS_CALLBACK,
            -> return UpsertDocumentScanIdError.IllegalAccountRole.left()
        }

        val documentScanId = documentScanRepository.upsertDocumentScanId(accountId).getOrElse { return it.left() }

        kotlin.runCatching {
            val documentScan = DocumentScan(documentType = documentType, documentScanId = documentScanId)
            registerRepository.save(registerRepository.findByAccountId(accountId).copy(documentScan = documentScan))
        }.onFailure { return UpsertDocumentScanIdError.Unexpected(it).left() }

        return documentScanId.right()
    }

    /**
     * Quando não existe um scan de documentos para o usuário, é porque não existe um document
     * scan id associado ao seu cadastro (e.g. cadastro pré-scanner, conta simplificada, cadastro
     * que não chegou na etapa ainda).
     */
    fun getResult(accountId: AccountId): Either<GetDocumentScanResultError, DocumentScanResult?> {
        val register = try {
            registerRepository.findByAccountId(accountId)
        } catch (e: ItemNotFoundException) {
            return GetDocumentScanResultError.AccountNotFound(accountId).left()
        } catch (e: Exception) {
            return GetDocumentScanResultError.Unexpected(e).left()
        }

        if (register.documentScan == null) {
            return null.right()
        }

        return documentScanRepository.getResult(register.documentScan.documentScanId)
    }

    fun getImage(accountId: AccountId): Either<GetImageError, DocumentScanImageWithType?> {
        val register = try {
            registerRepository.findByAccountId(accountId)
        } catch (e: ItemNotFoundException) {
            return GetImageError.AccountNotFound(accountId).left()
        } catch (e: Exception) {
            return GetImageError.Unexpected(e).left()
        }

        if (register.documentScan == null) {
            return null.right()
        }

        return documentScanRepository.getImage(register.documentScan.documentScanId)
            .map {
                it.withType(register.documentScan.documentType)
            }
    }
}