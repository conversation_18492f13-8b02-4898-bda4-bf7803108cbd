package ai.friday.billpayment.app.onepixpay

import ai.friday.billpayment.app.FindError
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.PaymentMethodNotFound
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.bill.schedule.ScheduleBillService
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.OnePixPayInstrumentation
import ai.friday.billpayment.app.integrations.PixPaymentService
import ai.friday.billpayment.app.integrations.PixQRCodeCreatorService
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.ScheduleStrategy
import ai.friday.billpayment.app.pix.PixStatementItem
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Property
import jakarta.inject.Named
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

const val onePixPayLockProvider = "one-pix-pay-lock-provider"
private const val ARBI_BANK_NO = 213L

@FridayMePoupe
open class OnePixPayService(
    private val accountService: AccountService,
    private val onePixPayRepository: OnePixPayRepository,
    private val onePixPayInstrumentation: OnePixPayInstrumentation,
    private val qrCodeService: PixQRCodeCreatorService,
    private val walletService: WalletService,
    private val billEventRepository: BillEventRepository,
    private val scheduleBillService: ScheduleBillService,
    private val notificationAdapter: NotificationAdapter,
    private val pixPaymentService: PixPaymentService,
    private val balanceService: BalanceService,
    private val billEventPublisher: BillEventPublisher,
    private val messagePublisher: MessagePublisher,
    private val walletRepository: WalletRepository,
    @Named(onePixPayLockProvider) private val lockProvider: InternalLock,
    @Property(name = "friday.morning.messaging.consumer.one-pix-pay-requested.queueName") val onePixPayRequestedQueueName: String,
) {

    open fun create(bills: List<BillView>, useCurrentBalance: Boolean): Either<OnePixPayErrors, OnePixPay> {
        val billsToPay = bills.toSet().toList()
        fun generateQrCode(walletId: WalletId, transactionId: QrCodeTransactionId): Either<OnePixPayErrors, String> {
            val wallet = walletService.findWallet(walletId)
            val account = accountService.findAccountById(wallet.founder.accountId)

            val billsAmount = billsToPay.sumOf { it.amountTotal }
            val amount = if (useCurrentBalance) {
                val balance = balanceService.getBalanceFrom(wallet.founder.accountId, wallet.paymentMethodId)
                billsAmount - balance.amount
            } else {
                billsAmount
            }

            if (amount <= 0) {
                return OnePixPayErrors.InvalidAmount.left()
            }

            val walletPixKey = walletService.walletEvpPixKey(wallet.id) ?: walletService.walletPixKey(wallet)

            return qrCodeService.createQRCode(
                key = walletPixKey,
                document = account.document,
                amount = amount,
                recipientName = account.name,
                transactionId = transactionId,
            ).right()
        }

        val onePixPay = OnePixPay.create(bills = billsToPay) { walletId, transactionId ->
            generateQrCode(walletId, transactionId)
        }.getOrElse { return it.left() }

        return onePixPayRepository.save(onePixPay).right()
    }

    fun processSchedule(onePixPay: OnePixPay) {
        val logName = "OnePixPayService#processSchedule"
        val lock = lockProvider.acquireLock(onePixPay.id.value) ?: throw ProcessScheduleError.OnePixPayLocked

        try {
            val wallet = walletService.findWallet(onePixPay.walletId)
            val account = accountService.findAccountById(wallet.founder.accountId)
            val markers = append("walletId", wallet.id.value)
                .andAppend("founderAccountId", account.accountId.value)
                .andAppend("onePixPayId", onePixPay.id.value)
                .andAppend("billIds", onePixPay.billIds)

            if (onePixPay.isExpired()) {
                notificationAdapter.notifyOnePixPayFailure(account.accountId)
                onePixPayRepository.save(onePixPay.asExpired())

                logger.error(markers.andAppend("reason", "expired"), logName)
                throw ProcessScheduleError.OnePixPayExpired
                    .also { onePixPayInstrumentation.onePixPayFailed(wallet, account, it) }
            }

            if (!onePixPay.isRequested()) {
                logger.error(markers.andAppend("reason", "pix not requested"), logName)
                throw ProcessScheduleError.OnePixPayNotRequested
                    .also { onePixPayInstrumentation.onePixPayFailed(wallet, account, it) }
            }

            val bills = onePixPay.billIds.map { billId ->
                billEventRepository.getBillById(billId).getOrElse {
                    notificationAdapter.notifyOnePixPayFailure(account.accountId)

                    val errorDescription = "BillId $billId not found"
                    onePixPayRepository.save(onePixPay.asError(errorDescription))

                    logger.error(markers.andAppend("reason", "Bill id not found"), logName)
                    throw ProcessScheduleError.BillNotFound(errorDescription)
                        .also { onePixPayInstrumentation.onePixPayFailed(wallet, account, it) }
                }
            }
                .filter { !it.isPaid() } // Disclaimer: fix temporário para não cancelar o OPP caso alguma das contas já ter sido paga

            bills.forEach { bill ->
                if (!bill.isActive()) {
                    notificationAdapter.notifyOnePixPayFailure(account.accountId)

                    val errorDescription = "BillId ${bill.billId} is not able to schedule"
                    onePixPayRepository.save(onePixPay.asError(errorDescription))
                    logger.error(markers.andAppend("reason", "Bill is not able to schedule"), logName)
                    throw ProcessScheduleError.BillNotAbleToSchedule(errorDescription)
                        .also { onePixPayInstrumentation.onePixPayFailed(wallet, account, it) }
                }
            }

            val (alreadyScheduledBills, notScheduledBills) = bills.partition {
                it.isPaymentScheduled()
            }

            if (alreadyScheduledBills.isNotEmpty() && alreadyScheduledBills.any { !it.subscriptionFee }) {
                logger.warn(
                    append("onePixPay", onePixPay).andAppend("alreadyScheduledBills", alreadyScheduledBills),
                    logName,
                )
            }

            cancelScheduleFromOthersBills(wallet.id, bills).map {
                markers.andAppend("canceledScheduleBills", it.map { bill -> bill.billId.value })
            }.getOrElse {
                onePixPayInstrumentation.onePixPayFailedToUnscheduleBills(wallet, account, bills)
                logger.error(markers.andAppend("reason", "Error canceling some scheduling bill"), logName, it)
            }

            val totalScheduledBills = notScheduledBills.fold(0) { total, notScheduledBill ->
                scheduleBillService.schedulePayment(
                    paymentWallet = wallet,
                    bill = notScheduledBill,
                    member = wallet.founder,
                    actionSource = ActionSource.OnePixPay,
                    scheduleStrategy = ScheduleStrategy.ofAsap(),
                ).map {
                    total + 1
                }.getOrElse {
                    total
                }
            }

            if (totalScheduledBills == notScheduledBills.size) {
                onePixPayRepository.save(onePixPay.asProcessed())
                onePixPayInstrumentation.onePixPayFullyProcessed(wallet, account, onePixPay, bills)

                logger.info(markers, logName)
            } else {
                notificationAdapter.notifyOnePixPayFailure(account.accountId)
                onePixPayRepository.save(onePixPay.asError("Some bills were not scheduled"))

                logger.error(markers.andAppend("reason", "Some bills were not scheduled"), logName)

                throw ProcessScheduleError.SomeBillsWereNotScheduled.also {
                    onePixPayInstrumentation.onePixPayPartiallyProcessed(wallet, account, onePixPay)
                }
            }
        } finally {
            lock.unlock()
        }
    }

    private fun cancelScheduleFromOthersBills(
        walletId: WalletId,
        onePixPayBills: List<Bill>,
    ): Either<Exception, List<Bill>> {
        val onePixPayBillsIds = onePixPayBills.map { it.billId }.toSet()
        val orderedScheduledBills = scheduleBillService.getOrderedScheduledBills(walletId, getLocalDate())
        val scheduleCanceledBills = orderedScheduledBills.mapNotNull { scheduledBill ->
            val bill = billEventRepository.getBillById(scheduledBill.billId).getOrElse {
                return it.left()
            }
            val shouldCancelSchedule = scheduledBill.billId !in onePixPayBillsIds && !bill.subscriptionFee
            if (shouldCancelSchedule) {
                billEventPublisher.publish(
                    bill,
                    BillPaymentScheduleCanceled(
                        billId = bill.billId,
                        walletId = bill.walletId,
                        actionSource = ActionSource.OnePixPay,
                        reason = ScheduleCanceledReason.NOT_SELECTED_ON_ONE_PIX_PAY,
                        batchSchedulingId = bill.schedule?.batchSchedulingId,
                    ),
                )
                bill
            } else {
                null
            }
        }
        return scheduleCanceledBills.right()
    }

    fun process(accountNo: AccountNumber, routingNo: Long, onePixPayId: OnePixPayId<*>) {
        val markers = append("accountNo", accountNo.fullAccountNumber)
            .andAppend("routingNo", routingNo)
            .andAppend("transactionId", onePixPayId)

        try {
            val paymentMethod = getPaymentMethod(bankNo = ARBI_BANK_NO, routingNo = routingNo, accountNumber = accountNo)
            val internalBankAccount = paymentMethod.method as InternalBankAccount

            process(WalletId(paymentMethod.accountId.value), internalBankAccount, onePixPayId)
        } catch (e: Exception) {
            logger.error(
                markers,
                "OnePixPayService#process",
                e,
            )
        }
    }

    private fun PixStatementItem.toPossibleOnePixPayIds(): List<OnePixPayId<*>> {
        return listOfNotNull(
            if (transactionId is UUIDTransactionId) {
                transactionId.toOnePixPayId()
            } else {
                null
            },
            endToEnd?.toOnePixPayId(),
        )
    }

    private fun InternalBankAccount.getPixStatement() = pixPaymentService.getStatement(
        accountNumber = AccountNumber(buildFullAccountNumber()),
    )

    fun process(walletId: WalletId, paymentMethod: InternalBankAccount?, depositOnePixPayId: OnePixPayId<*>? = null) {
        val logName = "OnePixPayService#process"
        val markers = append("walletId", walletId.value)
            .andAppend("depositOnePixPayId", depositOnePixPayId)

        fun resolveReceivedOPPIds(): List<OnePixPayId<*>> {
            val walletPaymentMethod = paymentMethod ?: getWalletPaymentMethod(walletId)
            markers.andAppend("paymentMethod", walletPaymentMethod.buildFullAccountNumber())

            val statementOppIds = walletPaymentMethod.getPixStatement().flatMap {
                it.toPossibleOnePixPayIds()
            }

            return (statementOppIds + depositOnePixPayId).filterNotNull()
        }

        try {
            val requestedOPPs = onePixPayRepository.findByWalletId(walletId, getLocalDate(), OnePixPayStatus.REQUESTED)
            markers.andAppend("requestedOPPIds", requestedOPPs.map { it.id })

            if (requestedOPPs.isNotEmpty()) {
                val receivedOPPIds = resolveReceivedOPPIds()
                markers.andAppend("receivedOPPIds", receivedOPPIds)

                val onePixPay = requestedOPPs.firstOrNull {
                    it.id in receivedOPPIds
                }
                markers.andAppend("onePixPay", onePixPay)

                onePixPay?.let { processSchedule(it) }
                logger.info(markers, logName)
            } else {
                logger.debug(markers, logName)
            }
        } catch (e: ProcessScheduleError) {
            logger.error(markers, logName, e)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            throw e
        }
    }

    open fun save(onePixPay: OnePixPay): OnePixPay {
        if (onePixPay.status == OnePixPayStatus.REQUESTED) {
            val accountPaymentMethod = walletRepository.findAccountPaymentMethod(onePixPay.walletId)

            messagePublisher.sendMessage(
                queueName = onePixPayRequestedQueueName,
                delaySeconds = 60,
                body = OnePixPayRequestedMessage(
                    onePixPayId = onePixPay.id.value,
                    fullAccountNumber = (accountPaymentMethod.method as InternalBankAccount).accountNumber.fullAccountNumber,
                ),
            )
        }

        return onePixPayRepository.save(onePixPay)
    }

    open fun find(onePixPayId: OnePixPayId<*>) = onePixPayRepository.find(onePixPayId)

    private fun getPaymentMethod(
        bankNo: Long,
        routingNo: Long,
        accountNumber: AccountNumber,
    ) = accountService.findPhysicalAccountPaymentMethod(bankNo, routingNo, accountNumber.fullAccountNumber)
        .getOrElse {
            when (it) {
                FindError.MultipleItemsFound -> throw IllegalStateException("Payment method list size must be 1")
                FindError.NotFound -> throw PaymentMethodNotFound(bankNo, routingNo, accountNumber)
                is FindError.ServerError -> throw it.exception
            }
        }

    private fun getWalletPaymentMethod(walletId: WalletId): InternalBankAccount {
        val wallet = walletService.findWallet(walletId)

        val paymentMethod = accountService.findAccountPaymentMethodByIdAndAccountId(
            accountPaymentMethodId = wallet.paymentMethodId,
            accountId = wallet.founder.accountId,
        )
        if (paymentMethod.method is InternalBankAccount) {
            return paymentMethod.method
        }
        throw IllegalStateException("Wallet payment method is not a bank account")
    }

    companion object {
        private val logger = LoggerFactory.getLogger(OnePixPayService::class.java)
    }
}

sealed class ProcessScheduleError : Exception() {
    data object OnePixPayExpired : ProcessScheduleError()
    data object OnePixPayLocked : ProcessScheduleError()
    data object OnePixPayNotRequested : ProcessScheduleError()
    data object SomeBillsWereNotScheduled : ProcessScheduleError()
    data class BillNotFound(val error: String) : ProcessScheduleError()
    data class BillNotAbleToSchedule(val error: String) : ProcessScheduleError()
}

data class OnePixPayRequestedMessage(
    val onePixPayId: String,
    val fullAccountNumber: String,
)