package ai.friday.billpayment.app.onepixpay

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.integrations.EndToEnd
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalTime
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import java.util.UUID

interface OnePixPayRepository {
    fun find(onePixPayId: OnePixPayId<*>): OnePixPay
    fun findAll(date: LocalDate): List<OnePixPay>

    fun findByWalletId(walletId: WalletId, from: LocalDate, status: OnePixPayStatus): List<OnePixPay>
    fun save(onePixPay: OnePixPay): OnePixPay
}

data class OnePixPay(
    val id: OnePixPayId<*> = UUIDOnePixPayId(),
    val walletId: WalletId,
    val billIds: List<BillId>,
    val qrCode: String,
    val status: OnePixPayStatus,
    val paymentLimitTime: LocalTime,
    val fundsReceived: Boolean,
    val fundsReceivedAt: ZonedDateTime? = null,
    val created: ZonedDateTime = getZonedDateTime(),
    val errorDescription: String? = null,
) {
    init {
        if (fundsReceived && fundsReceivedAt == null) {
            throw IllegalStateException("Funds received should have a date")
        }
    }

    fun asRequested(): OnePixPay {
        if (status != OnePixPayStatus.CREATED) {
            throw IllegalStateException("OnePixPay must have status created in order to be requested")
        }

        return copy(status = OnePixPayStatus.REQUESTED)
    }

    fun asProcessed(): OnePixPay {
        if (status != OnePixPayStatus.REQUESTED) {
            throw IllegalStateException("OnePixPay must have status requested in order to be processed")
        }

        return this.fundsReceived().copy(status = OnePixPayStatus.PROCESSED)
    }

    fun asError(errorDescription: String): OnePixPay {
        if (status != OnePixPayStatus.REQUESTED) {
            throw IllegalStateException("OnePixPay must have status requested in order to be error")
        }

        return this.fundsReceived().copy(status = OnePixPayStatus.ERROR, errorDescription = errorDescription)
    }

    fun asExpired(): OnePixPay {
        if (status != OnePixPayStatus.REQUESTED) {
            throw IllegalStateException("OnePixPay must have status requested in order to be expired")
        }

        return this.fundsReceived().copy(status = OnePixPayStatus.EXPIRED)
    }

    private fun fundsReceived(): OnePixPay {
        return copy(fundsReceived = true, fundsReceivedAt = getZonedDateTime())
    }

    fun isRequested() = this.status == OnePixPayStatus.REQUESTED

    fun isExpired(): Boolean {
        return this.status == OnePixPayStatus.EXPIRED || (
            !this.created.toLocalDate()
                .isEqual(getLocalDate()) || this.paymentLimitTime.isBefore(getLocalTime())
            )
    }

    companion object {
        fun create(
            bills: List<BillView>,
            endToEnd: EndToEnd,
        ): Either<OnePixPayErrors, OnePixPay> {
            return create(bills, endToEnd.toOnePixPayId()) { _, _ ->
                "".right()
            }
        }

        fun create(
            bills: List<BillView>,
            generateQrCode: (walletId: WalletId, transactionId: QrCodeTransactionId) -> Either<OnePixPayErrors, String>,
        ): Either<OnePixPayErrors, OnePixPay> {
            return create(bills, UUIDOnePixPayId(), generateQrCode)
        }

        private fun create(
            bills: List<BillView>,
            onePixPayId: OnePixPayId<*>,
            generateQrCode: (walletId: WalletId, transactionId: QrCodeTransactionId) -> Either<OnePixPayErrors, String>,
        ): Either<OnePixPayErrors, OnePixPay> {
            if (bills.isEmpty()) {
                return OnePixPayErrors.AtLeastOneBillRequired.left()
            }

            val walletIds = bills.map { it.walletId }.distinctBy { it.value }
            if (walletIds.size > 1) {
                return OnePixPayErrors.SingleWalletRequired(walletIds = walletIds).left()
            }

            if (!bills.all { it.status == BillStatus.ACTIVE }) {
                return OnePixPayErrors.OnlyActiveOrScheduledBillsAreAllowed.left()
            }

            val qrCode = if (onePixPayId.source is QrCodeTransactionId) {
                generateQrCode(walletIds.first(), onePixPayId.source as QrCodeTransactionId).getOrElse {
                    return it.left()
                }
            } else {
                ""
            }

            return OnePixPay(
                id = onePixPayId,
                walletId = walletIds.first(),
                billIds = bills.map { it.billId },
                qrCode = qrCode,
                status = OnePixPayStatus.CREATED,
                paymentLimitTime = bills.minOf { it.paymentLimitTime },
                fundsReceived = false,
            ).right()
        }
    }
}

enum class OnePixPayStatus {
    CREATED, REQUESTED, PROCESSED, EXPIRED, ERROR
}

private const val UUID_OPP_ID_PREFIX = "OPP-"
private const val ETOE_OPP_ID_PREFIX = "E2E-"

enum class OnePixPayIdType {
    UUID, END_TO_END
}

interface OnePixPayIdSource {
    val value: String

    fun toOnePixPayId(): OnePixPayId<*>

    companion object {
        fun fromIdValue(value: String): OnePixPayIdSource {
            return when {
                value.startsWith(UUID_OPP_ID_PREFIX) -> UUIDTransactionId(UUID.fromString(value.removePrefix(UUID_OPP_ID_PREFIX)))
                value.startsWith(ETOE_OPP_ID_PREFIX) -> EndToEnd(value.removePrefix(ETOE_OPP_ID_PREFIX))
                else -> throw IllegalArgumentException("$value is not a valid onePixPayId source")
            }
        }
    }
}

interface OnePixPayId<T : OnePixPayIdSource> {
    val value: String
    val type: OnePixPayIdType
    val source: T
}

data class UUIDOnePixPayId(override val source: UUIDTransactionId = UUIDTransactionId()) : OnePixPayId<UUIDTransactionId> {
    override val type = OnePixPayIdType.UUID

    override val value: String = "$UUID_OPP_ID_PREFIX${source.uuid}"
}

data class EndToEndOnePixPayId(override val source: EndToEnd) : OnePixPayId<EndToEnd> {
    override val type = OnePixPayIdType.END_TO_END

    override val value = "$ETOE_OPP_ID_PREFIX${source.value}"
}

sealed interface QrCodeTransactionId {
    val value: String

    val isProbableOnePixPayTransactionId: Boolean
    val isProbableCopyAndPasteTransactionId: Boolean

    companion object {
        fun from(value: String) = try {
            UUIDTransactionId(Base58.decodeToUUID(value))
        } catch (e: Exception) {
            GenericTransactionId(value)
        }
    }
}

data class GenericTransactionId(override val value: String) : QrCodeTransactionId {

    override val isProbableOnePixPayTransactionId: Boolean = false
    override val isProbableCopyAndPasteTransactionId: Boolean = value in listOf("CecApp", "CeCWhatsapp")

    init {
        if (value.length > 35) {
            throw IllegalArgumentException("QrCodeTransactionId must not be longer than 35 chars. id: $value")
        }

        if (!value.matches("^[a-zA-Z0-9*]*$".toRegex())) {
            throw IllegalArgumentException("QrCodeTransactionId must be alphanumeric. id: $value")
        }
    }
}

data class UUIDTransactionId(val uuid: UUID = UUID.randomUUID()) : QrCodeTransactionId, OnePixPayIdSource {
    override val value = Base58.encode(uuid)
    override val isProbableOnePixPayTransactionId: Boolean = true
    override val isProbableCopyAndPasteTransactionId: Boolean = false
    override fun toOnePixPayId() = UUIDOnePixPayId(this)
}

sealed class OnePixPayErrors : PrintableSealedClassV2() {
    data object AtLeastOneBillRequired : OnePixPayErrors()
    data object OnlyActiveOrScheduledBillsAreAllowed : OnePixPayErrors()
    data object InvalidAmount : OnePixPayErrors()
    data class SingleWalletRequired(val walletIds: List<WalletId>) : OnePixPayErrors()
}