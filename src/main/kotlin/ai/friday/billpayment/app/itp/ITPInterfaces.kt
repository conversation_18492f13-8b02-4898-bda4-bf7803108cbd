package ai.friday.billpayment.app.itp

import ai.friday.billpayment.PrintableSealedClass
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.ExternalBankAccount
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.chatbot.GetAccountOrganizations
import ai.friday.billpayment.app.chatbot.Organization
import ai.friday.billpayment.app.forecast.ForecastPeriod
import ai.friday.billpayment.app.onepixpay.OnePixPayErrors
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.wallet.WalletId
import arrow.core.Either
import java.util.UUID

interface ITPService {
    fun listPaymentOrganizations(accountId: AccountId): Either<ITPServiceError, GetAccountOrganizations>

    fun processPaymentIntentStatusChanged(
        paymentIntentId: PaymentIntentId,
        paymentIntentStatus: PaymentIntentStatus,
    ): Either<Exception, PaymentIntent>

    fun processPaymentIntentStatusChanged(
        consentId: ConsentId,
        paymentIntentStatus: PaymentIntentStatus,
    ): Either<Exception, PaymentIntent>

    fun listAllStaticInstitutions(): Map<String, ITPStaticInstitution>

    fun retrievePaymentRedirectUrl(paymentIntentId: PaymentIntentId): Either<ITPServiceError, PaymentIntentResponse>

    fun createPaymentIntentWithOnePixPay(
        command: CreatePaymentIntentCommand,
        bills: List<BillView>,
        useCurrentBalance: Boolean,
    ): Either<ITPServiceError, PaymentIntentId>

    fun createPaymentIntent(
        command: CreatePaymentIntentCommand,
        forecastPeriod: ForecastPeriod,
    ): Either<ITPServiceError, PaymentIntentId>

    fun callbackForPaymentInitiationConsent(
        state: String,
        code: String?,
        idToken: String?,
        error: String?,
        errorDescription: String?,
    ): Either<ITPServiceError, Unit>
}

enum class PaymentIntentStatus {
    CREATED,
    SUCCESS,
    FAILED,
    ONGOING,
}

interface ITPAdapter {
    fun createPaymentRedirectUrl(
        paymentIntent: PaymentIntent,
    ): Either<ITPServiceError, PaymentIntentResponse>

    fun listFinancialInstitutions(): Either<ITPServiceError, List<Organization>>

    fun createPaymentRedirectURL(
        url: String,
        description: String,
    ): Either<ITPServiceError, Unit>

    fun listPaymentRedirectURLs(): Either<ITPServiceError, List<Pair<String, String>>>

    fun deletePaymentRedirectURL(url: String): Either<ITPServiceError, Unit>

    fun callbackForPaymentInitiationConsent(
        state: String,
        code: String?,
        idToken: String?,
        error: String?,
        errorDescription: String?,
    ): Either<ITPServiceError, Unit>
}

interface PaymentIntentRepository {
    fun save(paymentIntent: PaymentIntent): PaymentIntent
    fun find(paymentIntentId: PaymentIntentId): PaymentIntent
    fun findByConsentId(consentId: ConsentId): PaymentIntent
}

sealed class ITPServiceError(open val message: String?) : PrintableSealedClass() {
    data class AccountNotFound(override val message: String = "AccountNotFound") : ITPServiceError(message)

    data class SufficientBalance(override val message: String = "SufficientBalanceError") : ITPServiceError(message)

    data class WalletNotFound(override val message: String = "WalletNotFound") : ITPServiceError(message)

    data class PaymentIntentNotFound(override val message: String = "PaymentIntentNotFound") : ITPServiceError(message)

    data class ISPBNotFound(override val message: String = "ISPBNotFound") : ITPServiceError(message)

    data class NestedOnePixPayError(override val message: String = "NestedOnePixPayError", val nested: OnePixPayErrors) : ITPServiceError(message)

    data class UnknownFinancialInstitution(override val message: String = "UnknownFinancialInstitution") : ITPServiceError(message)

    sealed class ITPErrorWithException(override val message: String, open val exception: Exception) : ITPServiceError(message) {
        data class ITPProviderError(override val exception: Exception) : ITPErrorWithException("ITPProviderError", exception)

        data class ITPInternalError(override val exception: Exception) : ITPErrorWithException("ITPInternalError", exception)
    }
}

data class ITPStaticInstitution(
    val ispb: String,
    val title: String,
)

data class PaymentIntent(
    val paymentIntentId: PaymentIntentId = PaymentIntentId(),
    val accountId: AccountId,
    val walletId: WalletId,
    val document: String,
    val authorizationServerId: String,
    val authorizationServerName: String,
    val routingNo: Long,
    val accountNo: Long,
    val accountDv: String,
    val bankISPB: String?,
    val accountType: AccountType?,
    val bankNo: Long?,
    val forecastPeriod: ForecastPeriod?,
    val includeScheduledBillsOnly: Boolean?,
    val details: PaymentIntentDetails,
    val status: PaymentIntentStatus,
    val consentId: ConsentId? = null,
)

data class ConsentId(val value: String)

sealed interface PaymentIntentDetails {
    val type: String

    data class WithPixKey(val pixKey: PixKey, val amount: Long) : PaymentIntentDetails {
        override val type: String = "WithPixKey"
    }

    data class WithQRCode(val qrCode: String) : PaymentIntentDetails {
        override val type: String = "WithQRCode"
    }

    companion object {
        fun build(
            pixKey: PixKey?,
            amount: Long?,
            qrCode: String?,
        ): PaymentIntentDetails {
            return if (pixKey != null && amount != null) {
                WithPixKey(pixKey, amount)
            } else if (qrCode != null) {
                WithQRCode(qrCode)
            } else {
                throw IllegalStateException("Invalid PaymentIntentDetails")
            }
        }
    }
}

fun PaymentIntent.toExternalBankAccount() =
    ExternalBankAccount(
        document = Document(value = document),
        bankNo = bankNo,
        bankISPB = bankISPB,
        routingNo = routingNo,
        accountNo = accountNo,
        accountDv = accountDv,
        accountType = accountType.toITPString(),
    )

fun AccountType?.toITPString(): String {
    return this?.let {
        when (this) {
            AccountType.CHECKING -> "CACC"
            AccountType.SAVINGS -> "SVGS"
            AccountType.SALARY -> "SLRY"
            AccountType.PAYMENT -> "TRAN"
        }
    } ?: "CACC"
}

data class PaymentIntentId(val value: String) {
    constructor() : this("PAYMENT-INTENT-${UUID.randomUUID()}")
}