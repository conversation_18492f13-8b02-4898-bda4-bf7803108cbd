package ai.friday.billpayment.app.banking

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.account.UserFilesConfiguration
import ai.friday.billpayment.app.account.getDisplayName
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BankDataService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.notification.EmailTemplatesConfiguration
import ai.friday.billpayment.app.utils.TemplateHelper
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import io.micronaut.http.MediaType
import io.via1.communicationcentre.app.email.Attachment
import io.via1.communicationcentre.app.integrations.EmailSenderService
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.io.ByteArrayInputStream
import java.time.LocalDate
import java.time.Year
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class BankAccountIncomeReportService(
    @Named("arbiBankDataServiceV1") private val bankDataService: BankDataService,
    private val accountRepository: AccountRepository,
    private val emailTemplatesConfiguration: EmailTemplatesConfiguration,
    private val emailSenderService: EmailSenderService,
    private val objectRepository: ObjectRepository,
    private val userFilesConfiguration: UserFilesConfiguration,
    private val messagePublisher: MessagePublisher,
    @Property(name = "sqs.incomeReport") val incomeReportQueueName: String,
    @Property(name = "email.notification.email") val from: String,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun generateAndSendIncomeReportForAllUsers(): Int {
        val year = Year.of(getLocalDate().year - 1)

        val eligibleUsers = accountRepository.findAllAccountsActivatedSince(LocalDate.MIN, false).filter {
            it.isEligibleForIncomeReport(year)
        }
        eligibleUsers.forEach {
            publishIncomeReportGeneration(it.accountId, year, resend = false, recreate = false)
        }
        return eligibleUsers.size
    }

    fun generateAndSendIncomeReport(accountId: AccountId, year: Year, resend: Boolean, recreate: Boolean) {
        val logName = "BankAccountIncomeReportService#generateAndSendIncomeReport"
        val markers = Markers.append("accountId", accountId.value).andAppend("year", year.value)

        if (year.value > getLocalDate().year) {
            throw IllegalArgumentException("Year cannot be in the future")
        }

        try {
            val account = accountRepository.findById(accountId)
            val pdfFilePathPrefix = generatePdfFileNamePrefix(account, year)
            val pdfRemoteFile = StoredObject(userFilesConfiguration.region, userFilesConfiguration.bucket, key = pdfFilePathPrefix)
            val existingKeys = objectRepository.findKeysByPrefix(pdfRemoteFile)
            markers.andAppend("fileAlreadyExists", existingKeys)

            if (existingKeys.isNotEmpty() && !resend && !recreate) {
                logger.info(markers.andAppend("context", "já existe um informe para esse usuário no mesmo ano").andAppend("generated", false).andAppend("emailSent", false).andAppend("s3Stored", true), logName)
                return
            }

            val pdfFile = if (existingKeys.isEmpty() || recreate) {
                val newPdf = bankDataService.getIncomeReportPDF(Document(account.document), year)
                val pdfFilePath = "${generatePdfFileNamePrefix(account, year)}${generatePdfFileNameSuffix()}"
                objectRepository.putObject(
                    StoredObject(userFilesConfiguration.region, userFilesConfiguration.bucket, key = pdfFilePath),
                    newPdf,
                    MediaType.APPLICATION_PDF_TYPE,
                )
                markers.andAppend("generated", true)
                markers.andAppend("s3Stored", true)
                newPdf
            } else {
                val lastFile = existingKeys.maxBy { it.key }
                val s3Pdf = objectRepository.loadObject(lastFile.bucket, lastFile.key).use {
                    it.readAllBytes()
                }
                markers.andAppend("generated", false)
                markers.andAppend("s3Stored", true)
                s3Pdf
            }
            sendIncomeReportEmail(account, year, pdfFile)
            markers.andAppend("emailSent", true)
            logger.info(markers, logName)
        } catch (e: Exception) {
            logger.error(markers.andAppend("context", "Erro ao gerar e enviar informe de rendimentos"), logName, e)
            throw e
        }
    }

    private fun generatePdfFileNamePrefix(account: Account, year: Year): String {
        return "${userFilesConfiguration.path}/${account.accountId.value}/informe-${account.document}-${year.value}"
    }

    private fun generatePdfFileNameSuffix(): String {
        return "-${getZonedDateTime().toEpochSecond()}.pdf"
    }

    private fun publishIncomeReportGeneration(accountId: AccountId, year: Year, resend: Boolean, recreate: Boolean) {
        messagePublisher.sendMessage(incomeReportQueueName, IncomeReportQueueMessage(accountId.value, year.value, resend = resend, recreate = recreate))
    }

    private fun sendIncomeReportEmail(account: Account, year: Year, pdfFile: ByteArray) {
        val attachment = Attachment.of(
            ByteArrayInputStream(pdfFile),
            MediaType.APPLICATION_PDF,
            "informe-${account.document}-${year.value}.pdf",
        )
        val informHtml = TemplateHelper.applyTemplate(
            emailTemplatesConfiguration.local.inform,
            mapOf(
                "name" to account.getDisplayName(),
                "year" to year.value,
                "currentYear" to getLocalDate().year,
            ),
        )
        emailSenderService.sendRawEmail(
            from,
            "Seu Informe de Rendimentos ${year.value} chegou!",
            informHtml,
            account.emailAddress.value,
            listOf(attachment),
            MediaType.TEXT_HTML,
        )
    }
}

private fun Account.isEligibleForIncomeReport(year: Year): Boolean {
    return created.toLocalDate().isBefore(LocalDate.of(year.value + 1, 1, 1)) &&
        (status == AccountStatus.ACTIVE || (status == AccountStatus.CLOSED && updated.toLocalDateTime().isAfter(LocalDate.of(year.value, 1, 1).atStartOfDay())))
}

data class IncomeReportQueueMessage(
    val accountId: String,
    val year: Int,
    val resend: Boolean,
    val recreate: Boolean,
)