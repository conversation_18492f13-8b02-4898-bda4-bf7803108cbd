package ai.friday.billpayment.app.banking

import ai.friday.billpayment.app.account.PaymentMethod
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.bill.BillEventDependency
import java.math.BigInteger
import java.util.UUID

data class InternalBankAccount(
    val accountType: AccountType,
    val bankNo: Long,
    val routingNo: Long,
    val accountNo: Long,
    val accountDv: String,
    val document: String = "",
    val bankAccountMode: BankAccountMode,
) : PaymentMethod {
    val accountNumber = AccountNumber(
        number = accountNo.toBigInteger(),
        dv = accountDv,
    )

    fun buildFullAccountNumber() = accountNumber.fullAccountNumber

    override val type = PaymentMethodType.BALANCE
}

data class AccountNumber(
    val number: BigInteger,
    val dv: String,
) {
    constructor(fullAccountNumber: String) : this(fullAccountNumber.dropLast(1).toBigInteger(), fullAccountNumber.takeLast(1))

    val fullAccountNumber = "$number" + dv
}

data class Bank(
    val number: Long,
    val name: String,
)

fun Bank.getDescription() = "$number - $name"

data class FinancialInstitution(
    val name: String,
    val ispb: String?,
    val compe: Long?,
) {
    fun toBank(): Bank? = compe?.let { Bank(number = compe, name = name) }
}

@BillEventDependency
enum class AccountType {
    CHECKING, SAVINGS, SALARY, PAYMENT
}

data class BankOperationId(val value: String) {
    companion object {
        const val PREFIX = "OPERATION-"

        fun build(uuid: String) = build(UUID.fromString(uuid))
        fun build(uuid: UUID = UUID.randomUUID()) = BankOperationId("$PREFIX$uuid")
    }
}

enum class BankOperationStatus {
    SUCCESS, REFUNDED, INSUFFICIENT_FUNDS, TIMEOUT, ERROR, INVALID_DATA, UNKNOWN;

    fun isError(): Boolean {
        return this in listOf(ERROR, INSUFFICIENT_FUNDS, INVALID_DATA)
    }
}

@BillEventDependency
enum class FinancialServiceGateway {
    CELCOIN, ARBI, FRIDAY, ORIGINAL,

    @Deprecated("")
    XP,

    ;

    companion object {
        private val values = entries.associateBy { it.name }

        fun of(source: String?) = values[source]
    }
}

enum class BankAccountMode {
    PHYSICAL, VIRTUAL
}