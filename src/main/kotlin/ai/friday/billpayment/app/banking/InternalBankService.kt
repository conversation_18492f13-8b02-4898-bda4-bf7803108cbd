package ai.friday.billpayment.app.banking

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.adapters.lock.internalBankServiceLockProvider
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.FinancialInstitutionGlobalData
import ai.friday.billpayment.app.FindError
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.PaymentMethod
import ai.friday.billpayment.app.account.PaymentMethodNotFound
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.integrations.BankAccountService
import ai.friday.billpayment.app.integrations.FundProvider
import ai.friday.billpayment.app.integrations.InternalBankRepository
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.PixPaymentService
import ai.friday.billpayment.app.isBetween
import ai.friday.billpayment.app.message.QueueMessage
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.context.annotation.Property
import jakarta.inject.Named
import java.time.DayOfWeek
import java.time.Instant
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Named("InternalBankService")
@FridayMePoupe
open class InternalBankService(
    private val accountService: AccountService,
    private val bankAccountService: BankAccountService,
    private val pixPaymentService: PixPaymentService,
    private val internalBankRepository: InternalBankRepository,
    private val messagePublisher: MessagePublisher,
    private val omnibusBankAccountConfiguration: OmnibusBankAccountConfiguration,
    private val balanceService: BalanceService,
    private val notificationAdapter: NotificationAdapter,
    private val walletService: WalletService,
    @Named(internalBankServiceLockProvider) private val lockProvider: InternalLock,
) : FundProvider {

    @field:Property(name = "integrations.arbi.contaLiquidacao")
    lateinit var internalSettlementAccountNo: String

    @field:Property(name = "integrations.arbi.contaCashin")
    lateinit var internalCashinAccountNo: String

    @field:Property(name = "sqs.bankAccountDepositQueueName")
    lateinit var bankAccountDepositQueueName: String

    @field:Property(name = "internalBankService.checkStatus.startDateMinusDays")
    var startDateMinusDays: Long = 0

    @field:Property(name = "internalBankService.checkStatus.endDatePlusDays")
    var endDatePlusDays: Long = 0

    private val mapper = getObjectMapper()

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun synchronizeBankAccount(
        accountId: AccountId,
        accountPaymentMethodId: AccountPaymentMethodId,
    ): Boolean {
        val paymentMethod = accountService.findAccountPaymentMethodByIdAndAccountId(
            accountPaymentMethodId = accountPaymentMethodId,
            accountId = accountId,
        )
        val bankAccount = paymentMethod.method as InternalBankAccount
        return synchronizeBankAccount(
            accountId = paymentMethod.accountId,
            accountPaymentMethodId = paymentMethod.id,
            accountNumber = bankAccount.accountNumber,
            document = bankAccount.document,
            withPixFallBack = false,
        )
    }

    fun synchronizeBankAccount(
        bankNo: Long,
        routingNo: Long,
        accountNumber: AccountNumber,
        withPixFallBack: Boolean = false,
    ): Boolean {
        if (omnibusBankAccountConfiguration.isOmnibusBankAccount(bankNo, routingNo, accountNumber)) {
            return synchronizeOmnibusBankAccount()
        }

        val paymentMethod = getPaymentMethod(bankNo, routingNo, accountNumber)
        val bankAccount = paymentMethod.method as InternalBankAccount

        return synchronizeBankAccount(
            accountId = paymentMethod.accountId,
            accountPaymentMethodId = paymentMethod.id,
            accountNumber = bankAccount.accountNumber,
            document = bankAccount.document,
            withPixFallBack = withPixFallBack,
        )
    }

    fun synchronizeBankAccount(
        accountId: AccountId,
        accountPaymentMethodId: AccountPaymentMethodId,
        accountNumber: AccountNumber,
        document: String,
        withPixFallBack: Boolean,
    ): Boolean {
        val logName = "InternalBankService#synchronizeBankAccount"
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("accountPaymentMethodId", accountPaymentMethodId.value)
            .andAppend("sourceFullAccountNo", accountNumber)
            .andAppend("document", document)
        try {
            val allExternalStatementItems = mutableListOf<BankStatementItem>()

            val defaultBankStatementItems = getDefaultBankStatmentItems(
                accountNumber = accountNumber,
                document = document,
            )
            markers.andAppend("defaultBankStatementItems", defaultBankStatementItems)
            allExternalStatementItems.addAll(defaultBankStatementItems)

            var synchronizedItems = defaultBankStatementItems.synchronize(accountPaymentMethodId, accountId, document)

            if (synchronizedItems.isEmpty() && withPixFallBack) {
                val pixStatementItems = pixPaymentService.getStatement(accountNumber)
                markers.andAppend("pixStatementItems", pixStatementItems)
                pixStatementItems.forEach { pixStatmentItem ->
                    if (allExternalStatementItems.none { it.operationNumber == pixStatmentItem.operationNumber }) {
                        allExternalStatementItems.add(pixStatmentItem)
                    }
                }

                synchronizedItems = pixStatementItems.synchronize(accountPaymentMethodId, accountId, document)
            }
            markers.andAppend("synchronizedAnyStatements", synchronizedItems.isNotEmpty())
                .andAppend("synchronizedItems", synchronizedItems)

            checkInternalStatementIntegrity(
                accountPaymentMethodId = accountPaymentMethodId,
                externalBankStatementItems = allExternalStatementItems,
            )

            logger.info(markers, logName)
            return synchronizedItems.isNotEmpty()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            throw SynchronizeBankAccountException(e)
        }
    }

    open fun notifyPixNotReceived(
        bankNo: Long,
        routingNo: Long,
        accountNumber: AccountNumber,
        flow: BankStatementItemFlow,
        counterpartName: String,
        counterpartDocument: String,
        amount: Long,
    ) {
        val paymentMethod =
            if (omnibusBankAccountConfiguration.isOmnibusBankAccount(bankNo, routingNo, accountNumber)) {
                val virtualAccount = findVirtualAccountPaymentMethodOrNull(flow, counterpartDocument)
                if (virtualAccount == null) {
                    LOG.error(Markers.append("context", "virtual account not found"), "notifyPixNotReceived")
                    return
                }
                virtualAccount
            } else {
                getPaymentMethod(bankNo, routingNo, accountNumber)
            }

        val wallet = walletService.findWallet(
            paymentMethod.accountId,
            paymentMethod.id,
        )

        notificationAdapter.notifyPixNotReceivedFailure(
            accountId = paymentMethod.accountId,
            wallet = wallet,
            amount = amount,
            senderName = counterpartName,
            senderDocument = counterpartDocument,
        )
    }

    open fun synchronizePixBankStatementItem(
        bankNo: Long,
        routingNo: Long,
        accountNumber: AccountNumber,
        bankStatementItem: DefaultBankStatementItem,
    ) {
        if (omnibusBankAccountConfiguration.isOmnibusBankAccount(bankNo, routingNo, accountNumber)) {
            synchronizeOmnibusBankAccount()
            return
        }

        val paymentMethod = getPaymentMethod(bankNo, routingNo, accountNumber)

        if (shouldSynchronize(paymentMethod.id, bankStatementItem)) {
            synchronize(
                accountPaymentMethodId = paymentMethod.id,
                accountId = paymentMethod.accountId,
                item = bankStatementItem,
            )
        }
    }

    fun synchronizeOmnibusBankAccount(): Boolean {
        val accountPaymentMethodId = AccountPaymentMethodId(omnibusBankAccountConfiguration.accountPaymentMethodId)
        val sourceFullAccountNo = omnibusBankAccountConfiguration.accountNo
        val document = omnibusBankAccountConfiguration.document
        try {
            val now = getZonedDateTime()
            val startDate = now.minusDays(startDateMinusDays)
            val endDate = now.plusDays(endDatePlusDays)
            val bankStatement = bankAccountService.getStatement(
                accountNumber = AccountNumber(sourceFullAccountNo),
                document = document,
                initialDate = startDate,
                endDate = endDate,
            )
            var synchronizedAnyStatements = false
            bankStatement.items.forEach { item ->
                if (synchronizeOmnibusBankStatementItem(item, accountPaymentMethodId)) {
                    synchronizedAnyStatements = true
                }
            }

            return synchronizedAnyStatements
        } catch (e: Exception) {
            throw SynchronizeBankAccountException(e)
        }
    }

    private fun getDefaultBankStatmentItems(
        accountNumber: AccountNumber,
        document: String,
    ): List<DefaultBankStatementItem> {
        val now = getZonedDateTime()
        val startDate = now.minusDays(startDateMinusDays)
        val endDate = now.plusDays(endDatePlusDays)
        val bankStatement = bankAccountService.getStatement(
            accountNumber = accountNumber,
            document = document,
            initialDate = startDate,
            endDate = endDate,
        )
        return bankStatement.items
    }

    private fun List<BankStatementItem>.synchronize(
        accountPaymentMethodId: AccountPaymentMethodId,
        accountId: AccountId,
        document: String,
    ): List<BankStatementItem> = mapNotNull { item ->
        trySynchronizeItem(accountPaymentMethodId, item, accountId, document)
    }

    private fun trySynchronizeItem(
        accountPaymentMethodId: AccountPaymentMethodId,
        item: BankStatementItem,
        accountId: AccountId,
        document: String,
    ): BankStatementItem? {
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("document", document)
            .andAppend("operationNumber", item.operationNumber)
            .andAppend("amount", item.amount)
            .andAppend("itemDate", item.date)

        val shouldSynchronize = shouldSynchronize(accountPaymentMethodId, item)
        markers.andAppend("shouldSynchronize", shouldSynchronize)

        return if (shouldSynchronize) {
            synchronize(
                accountPaymentMethodId = accountPaymentMethodId,
                accountId = accountId,
                item = item,
            )
        } else {
            null
        }.also {
            markers.andAppend("syncResult", it != null)
            logger.info(markers, "InternalBankService#synchronizeBankAccountItem")
        }
    }

    fun isAfterHour(time: ZonedDateTime): Boolean {
        val afterHoursTimeLimit = LocalTime.of(17, 0)
        val afterHoursLimit = ZonedDateTime.of(time.toLocalDate(), afterHoursTimeLimit, brazilTimeZone)

        val isHoliday = FinancialInstitutionGlobalData.isHoliday(time.toLocalDate())
        val isWeekend = time.dayOfWeek in listOf(DayOfWeek.SATURDAY, DayOfWeek.SUNDAY)
        return time.isAfter(afterHoursLimit) or isWeekend or isHoliday
    }

    override fun captureFunds(
        accountId: AccountId,
        accountPaymentMethodId: AccountPaymentMethodId,
        paymentMethod: PaymentMethod,
        amount: Long,
    ): BankTransfer {
        val costumerBankAccount = paymentMethod as InternalBankAccount
        return when (costumerBankAccount.bankAccountMode) {
            BankAccountMode.PHYSICAL -> captureFundsPhysicalAccount(costumerBankAccount, amount)
            BankAccountMode.VIRTUAL -> captureFundsVirtualAccount(accountPaymentMethodId, costumerBankAccount, amount)
        }
    }

    override fun undoCaptureFunds(
        accountId: AccountId,
        accountPaymentMethodId: AccountPaymentMethodId,
        paymentMethod: PaymentMethod,
        operationId: BankOperationId,
        amount: Long,
        ongoingRefundOperationId: BankOperationId?,
    ): BankTransfer {
        val costumerBankAccount = paymentMethod as InternalBankAccount
        val bankTransferResult = bankAccountService.transfer(
            originAccountNo = internalSettlementAccountNo,
            targetAccountNo = costumerBankAccount.buildFullAccountNumber(),
            amount = amount,
        )
        if (costumerBankAccount.bankAccountMode == BankAccountMode.VIRTUAL && bankTransferResult.status == BankOperationStatus.SUCCESS) {
            val internalBankStatementItem = InternalBankStatementItem(
                accountPaymentMethodId = accountPaymentMethodId,
                bankStatementItem = buildBankStatementItem(
                    BankStatementItemFlow.CREDIT,
                    bankTransferResult,
                    operationId,
                ),
            )
            try {
                internalBankRepository.create(internalBankStatementItem)
            } catch (error: IllegalStateException) {
                LOG.error(
                    Markers.append("error", error.message)
                        .andAppend("internalBankStatementItem", internalBankStatementItem),
                    "UndoCaptureFunds",
                )
            }
        }

        return bankTransferResult
    }

    fun prepareCashInFunds(amount: Long) = BankTransfer(
        operationId = BankOperationId.build(),
        gateway = FinancialServiceGateway.ARBI,
        status = BankOperationStatus.UNKNOWN,
        amount = amount,
    )

    fun cashInFunds(operationId: BankOperationId, userFullAccountNo: String, amount: Long): BankTransfer {
        return bankAccountService.transfer(
            originAccountNo = internalCashinAccountNo,
            targetAccountNo = userFullAccountNo,
            amount = amount,
            operationId = operationId,
        )
    }

    override fun checkCaptureFunds(
        accountId: AccountId,
        paymentMethod: PaymentMethod,
        operationId: BankOperationId,
        operationDate: LocalDate,
    ): Boolean {
        val costumerBankAccount = paymentMethod as InternalBankAccount
        return bankAccountService.checkTransferStatus(
            originAccountNo = costumerBankAccount.buildFullAccountNumber(),
            idRequisicaoParceiroOriginal = operationId.value,
            startDate = operationDate.minusDays(startDateMinusDays),
            endDate = operationDate.plusDays(endDatePlusDays),
        )
    }

    fun checkCashInFunds(operationId: BankOperationId, operationDate: LocalDate): Boolean {
        return bankAccountService.checkTransferStatus(
            originAccountNo = internalCashinAccountNo,
            idRequisicaoParceiroOriginal = operationId.value,
            startDate = operationDate.minusDays(startDateMinusDays),
            endDate = operationDate.plusDays(endDatePlusDays),
        )
    }

    fun getCashInByPeriod(
        startDate: ZonedDateTime,
        endDate: ZonedDateTime,
        accountPaymentMethodId: AccountPaymentMethodId,
    ): Long {
        val credits = internalBankRepository.findAllBankStatementCredits(
            accountPaymentMethodId = accountPaymentMethodId,
            startDate = startDate.toLocalDate(),
            endDate = endDate.toLocalDate(),
        )

        return credits.sumOf { it.amount }
    }

    open fun logStatementDivergence(accountPaymentMethodId: AccountPaymentMethodId, operationNumber: String, message: String) {
        LOG.error(
            Markers.append("accountPaymentMethodId", accountPaymentMethodId.value)
                .andAppend("operationNumber", operationNumber)
                .andAppend("error", message),
            "SynchronizeInternalStatement",
        )
    }

    private fun synchronize(
        accountPaymentMethodId: AccountPaymentMethodId,
        accountId: AccountId,
        item: BankStatementItem,
    ): BankStatementItem? {
        val internalBankStatement = InternalBankStatementItem(item, accountPaymentMethodId)
        try {
            internalBankRepository.create(internalBankStatement)
            balanceService.invalidate(accountPaymentMethodId)
            publishBankOperationExecuted(item, accountId, accountPaymentMethodId)
            return item
        } catch (error: IllegalStateException) {
            LOG.warn(
                Markers.append("error", error.message)
                    .andAppend("internalBankStatement", internalBankStatement),
                "SynchronizeInternalBankStatement",
            )
            return null
        }
    }

    private fun checkInternalStatementIntegrity(
        accountPaymentMethodId: AccountPaymentMethodId,
        externalBankStatementItems: List<BankStatementItem>,
    ) {
        val internalBankStatement = internalBankRepository.findAllBankStatementItem(
            accountPaymentMethodId = accountPaymentMethodId,
            startDate = getLocalDate().minusDays(startDateMinusDays),
            endDate = getLocalDate().plusDays(endDatePlusDays),
        )

        val internalOperationNumbers = internalBankStatement.items.map { it.operationNumber }
        val externalOperationNumbers = externalBankStatementItems.map { it.operationNumber }

        val unkownOperationNumbers = internalOperationNumbers.toSet() - externalOperationNumbers.toSet()
        unkownOperationNumbers.forEach { operationNumber ->
            logStatementDivergence(accountPaymentMethodId, operationNumber, "Statement interno não encontrado no extrato externo.")
        }

        val duplicatedItems = internalBankStatement.items.groupingBy { it.operationNumber }.eachCount()
            .filter { (_, count) -> count >= 2 }
        duplicatedItems.forEach { duplicatedItem ->
            logStatementDivergence(accountPaymentMethodId, duplicatedItem.key, "Statement duplicado no extrato interno.")
        }
    }

    private fun captureFundsPhysicalAccount(costumerBankAccount: InternalBankAccount, amount: Long): BankTransfer {
        return bankAccountService.transfer(
            originAccountNo = costumerBankAccount.buildFullAccountNumber(),
            targetAccountNo = internalSettlementAccountNo,
            amount = amount,
        )
    }

    private fun captureFundsVirtualAccount(
        accountPaymentMethodId: AccountPaymentMethodId,
        costumerBankAccount: InternalBankAccount,
        amount: Long,
    ): BankTransfer {
        val lock = lockProvider.acquireLock(accountPaymentMethodId.value)
            ?: return buildBankTransferConcurrentLockError(amount)
        try {
            val currentBalance = internalBankRepository.findAllBankStatementItem(accountPaymentMethodId).balance.amount
            if (currentBalance < amount) {
                return buildBankTransferInsufficientFunds(amount)
            }
            val bankTransferResult = bankAccountService.transfer(
                originAccountNo = costumerBankAccount.buildFullAccountNumber(),
                targetAccountNo = internalSettlementAccountNo,
                amount = amount,
            )
            if (bankTransferResult.status == BankOperationStatus.SUCCESS) {
                val internalBankStatementItem = InternalBankStatementItem(
                    accountPaymentMethodId = accountPaymentMethodId,
                    bankStatementItem = buildBankStatementItem(BankStatementItemFlow.DEBIT, bankTransferResult),
                )
                try {
                    internalBankRepository.create(internalBankStatementItem)
                } catch (error: IllegalStateException) {
                    LOG.error(
                        Markers.append("error", error.message)
                            .andAppend("internalBankStatementItem", internalBankStatementItem),
                        "CaptureFundsVirtualAccount",
                    )
                }
            }
            return bankTransferResult
        } finally {
            lock.unlock()
        }
    }

    private fun getPaymentMethod(
        bankNo: Long,
        routingNo: Long,
        accountNumber: AccountNumber,
    ) = accountService.findPhysicalAccountPaymentMethod(bankNo, routingNo, accountNumber.fullAccountNumber)
        .getOrElse {
            when (it) {
                FindError.MultipleItemsFound -> throw IllegalStateException("Payment method list size must be 1")
                FindError.NotFound -> throw PaymentMethodNotFound(bankNo, routingNo, accountNumber)
                is FindError.ServerError -> throw it.exception
            }
        }

    private fun buildBankStatementItem(
        flow: BankStatementItemFlow,
        bankTransfer: BankTransfer,
        operationId: BankOperationId? = null,
    ): DefaultBankStatementItem {
        return DefaultBankStatementItem(
            date = getZonedDateTime().toLocalDate(),
            flow = flow,
            type = BankStatementItemType.TRANSFERENCIA_CC,
            description = "",
            operationNumber = if (flow == BankStatementItemFlow.DEBIT) bankTransfer.debitOperationNumber!! else bankTransfer.creditOperationNumber!!,
            isTemporaryOperationNumber = false,
            amount = bankTransfer.amount,
            counterpartName = omnibusBankAccountConfiguration.name,
            counterpartDocument = omnibusBankAccountConfiguration.document,
            documentNumber = "",
            ref = operationId?.value,
        )
    }

    private fun buildBankTransferInsufficientFunds(amount: Long): BankTransfer {
        return BankTransfer(
            operationId = BankOperationId.build(),
            status = BankOperationStatus.INSUFFICIENT_FUNDS,
            amount = amount,
            errorDescription = "Insufficient funds on virtual account",
            gateway = FinancialServiceGateway.ARBI,
        ).also {
            LOG.warn(Markers.append("bankTransfer", it), "InternalBankService")
        }
    }

    private fun buildBankTransferConcurrentLockError(amount: Long): BankTransfer {
        return BankTransfer(
            operationId = BankOperationId.build(),
            status = BankOperationStatus.ERROR,
            amount = amount,
            errorDescription = "Concurrent transaction exception on virtual account",
            gateway = FinancialServiceGateway.ARBI,
        ).also {
            LOG.warn(Markers.append("bankTransfer", it), "InternalBankService")
        }
    }

    private fun synchronizeOmnibusBankStatementItem(
        item: DefaultBankStatementItem,
        accountPaymentMethodId: AccountPaymentMethodId,
    ): Boolean {
        val targetVirtualAccount =
            findVirtualAccountPaymentMethodOrNull(flow = item.flow, counterpartDocument = item.counterpartDocument)

        if (shouldSynchronize(accountPaymentMethodId, item)) {
            internalBankRepository.save(
                OmnibusBankStatementItem(
                    item,
                    accountPaymentMethodId,
                    targetVirtualAccount?.id,
                ),
            )
            balanceService.invalidate(accountPaymentMethodId)
        }

        targetVirtualAccount?.let {
            if (shouldSynchronize(it.id, item)) {
                val internalBankStatementItem = InternalBankStatementItem(item, it.id)
                try {
                    internalBankRepository.create(internalBankStatementItem)
                    publishBankOperationExecuted(item, it.accountId, it.id)
                    return true
                } catch (error: IllegalStateException) {
                    LOG.error(
                        Markers.append("error", error.message)
                            .andAppend("internalBankStatementItem", internalBankStatementItem),
                        "SynchronizeOmnibusBankStatement",
                    )
                }
            }
        }

        return false
    }

    private fun findVirtualAccountPaymentMethodOrNull(
        flow: BankStatementItemFlow,
        counterpartDocument: String,
    ): AccountPaymentMethod? {
        return takeIf {
            flow == BankStatementItemFlow.CREDIT
        }?.let {
            accountService.findVirtualBankAccountByDocument(counterpartDocument).getOrElse { error ->
                when (error) {
                    FindError.NotFound -> null
                    FindError.MultipleItemsFound -> throw SynchronizeBankAccountException("document $counterpartDocument has multiple AccountPaymentMethods")
                    is FindError.ServerError -> throw SynchronizeBankAccountException(
                        "error searching account for document $counterpartDocument",
                        error.exception,
                    )
                }
            }
        }
    }

    private fun shouldSynchronize(accountPaymentMethodId: AccountPaymentMethodId, item: BankStatementItem): Boolean {
        // nao sincronizar com numero temporario para evitar duplicidade quando virar definitivo
        if (item.isTemporaryOperationNumber) {
            return false
        }

        // query na base nao pode incluir a data -> arbi as vezes retorna itens com data diferente do que temos na base
        // tentavamos sincronizar equivocadamente esses itens com data diferente.
        val internalBankStatementItem = internalBankRepository.findBankStatementItem(accountPaymentMethodId, item.operationNumber)
            .getOrElse {
                when (it) {
                    FindError.NotFound -> return true
                    else -> throw SynchronizeBankAccountException("error finding bank statement item with account $accountPaymentMethodId and item $item")
                }
            }
        val minDate = item.date.minusDays(startDateMinusDays)
        val maxDate = item.date.plusDays(endDatePlusDays)
        if (internalBankStatementItem.date.isBetween(minDate, maxDate)) {
            return false
        }
        return true
    }

    private fun publishBankOperationExecuted(
        item: BankStatementItem,
        accountId: AccountId,
        accountPaymentMethodId: AccountPaymentMethodId,
    ) {
        val operation = with(item) {
            BankOperationExecuted(
                accountId = accountId,
                accountPaymentMethodId = accountPaymentMethodId,
                created = Instant.now().toEpochMilli(),
                date = date.format(dateFormat),
                flow = flow,
                type = type,
                description = description,
                operationNumber = operationNumber,
                amount = amount,
                counterpartName = counterpartName,
                counterpartDocument = counterpartDocument,
                counterpartAccountNo = counterpartAccountNo,
                counterpartBankName = counterpartBankName,
                documentNumber = documentNumber,
            )
        }
        messagePublisher.sendMessage(QueueMessage(bankAccountDepositQueueName, mapper.writeValueAsString(operation)))
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(InternalBankService::class.java)
    }
}

data class BankOperationExecuted(
    val accountId: AccountId,
    val accountPaymentMethodId: AccountPaymentMethodId,
    val date: String,
    val created: Long,
    val flow: BankStatementItemFlow,
    val type: BankStatementItemType,
    val description: String,
    val operationNumber: String,
    val amount: Long,
    val counterpartName: String,
    val counterpartDocument: String,
    val counterpartAccountNo: String? = null,
    val counterpartBankName: String? = null,
    val documentNumber: String,
) {
    fun verifyIsDeposit(): Boolean {
        return flow == BankStatementItemFlow.CREDIT && type in listOf(
            BankStatementItemType.TED_MESMA_TITULARIDADE,
            BankStatementItemType.TED_DIF_TITULARIDADE,
            BankStatementItemType.PIX,
        )
    }

    fun verifyIsRefund(): Boolean {
        return flow == BankStatementItemFlow.CREDIT && type == BankStatementItemType.DEVOLUCAO_TED
    }
}

sealed class FridayBankAccountError : PrintableSealedClassV2() {
    data object FridayAccountNotFound : FridayBankAccountError()
    data object FridayAccountClosed : FridayBankAccountError()
    class ServerError(val exception: Exception) : FridayBankAccountError()
}

data class SynchronizeBankAccountTO(
    val bankNo: Long,
    val routingNo: Long,
    val fullAccountNumber: String,
)