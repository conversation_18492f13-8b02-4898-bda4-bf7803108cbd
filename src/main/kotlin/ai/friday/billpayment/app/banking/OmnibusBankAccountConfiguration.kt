package ai.friday.billpayment.app.banking

import ai.friday.billpayment.app.account.AccountPaymentMethodId
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties

@ConfigurationProperties("internalBankService.omnibusBankAccount")
class OmnibusBankAccountConfiguration @ConfigurationInject constructor(
    val accountPaymentMethodId: String,
    val document: String,
    val bankNo: Long,
    val routingNo: Long,
    val accountNo: String,
    val name: String,
) {
    fun isOmnibusBankAccount(accountPaymentMethodId: AccountPaymentMethodId) =
        accountPaymentMethodId.value == this.accountPaymentMethodId

    fun isOmnibusBankAccount(bankNo: Long, routingNo: Long, accountNumber: AccountNumber) =
        bankNo == this.bankNo && routingNo == this.routingNo && accountNumber.fullAccountNumber == this.accountNo
}