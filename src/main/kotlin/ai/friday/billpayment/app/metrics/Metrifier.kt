// ktlint-disable filename - pulo do gato do linter
package ai.friday.billpayment.app.metrics

import ai.friday.billpayment.and
import ai.friday.billpayment.log
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.DistributionSummary
import io.micrometer.core.instrument.Gauge
import io.micrometer.core.instrument.Meter
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Timer
import io.micronaut.context.annotation.Context
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import java.time.Duration
import java.util.concurrent.TimeUnit
import java.util.function.Supplier
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Context
@Requires(property = "micronaut.metrics.enabled", value = "true")
private class Metrifier(
    @Property(name = "micronaut.application.name") applicationName: String,
    registry: MeterRegistry,
) {

    init {
        defaultPrefix = applicationName
        Companion.registry = registry
    }

    companion object {
        private val logger = LoggerFactory.getLogger(Metrifier::class.java)

        private lateinit var defaultPrefix: String
        private lateinit var registry: MeterRegistry

        fun isInitialized() = this::defaultPrefix.isInitialized && this::registry.isInitialized

        private fun validate(vararg tags: Pair<String, Any?>) =
            tags.groupingBy { it.first }.eachCount().count { it.value > 1 } == 0

        private fun getOrCreateMeter(name: String, type: MetricType, vararg possibleTags: Pair<String, Any?>, fn: Supplier<Number>): Meter? {
            if (!validate(*possibleTags)) return null

            val tagsArray = normalizeTags(possibleTags).toTypedArray()

            return when (type) {
                MetricType.GAUGE -> {
                    Gauge.builder(name, fn)
                        .tags(*tagsArray)
                        .register(registry)
                }

                MetricType.TIMED,
                MetricType.COUNTER,
                MetricType.SUMMARY,
                -> throw IllegalStateException("Invalid metric type")
            }
        }

        private fun getOrCreateMeter(name: String, type: MetricType, vararg possibleTags: Pair<String, Any?>): Meter? {
            if (!validate(*possibleTags)) return null

            val tagsArray = normalizeTags(possibleTags).toTypedArray()

            return when (type) {
                MetricType.COUNTER -> registry.counter(name, *tagsArray)
                MetricType.TIMED -> registry.timer(name, *tagsArray)
                MetricType.SUMMARY -> {
                    DistributionSummary.builder(name).tags(*tagsArray)
                        .publishPercentiles(0.95)
                        .publishPercentileHistogram(true)
                        .register(registry)
                }

                MetricType.GAUGE -> throw IllegalStateException("Invalid metric type")
            }
        }

        private fun normalizeTags(tags: Array<out Pair<String, Any?>>): ArrayList<String> {
            val tagsArray = ArrayList<String>(tags.size * 2)
            tags.forEach { tagsArray.addAll(arrayOf(it.first, it.second.toString().lowercase())) }
            return tagsArray
        }

        private fun buildMetricName(metric: Metrics): String {
            val suffix = when (metric) {
                is AbstractFridayCountMetrics -> "total"
                is AbstractTimeByElapsedMetrics -> "duration"
                is AbstractTimeByDateMetrics -> "duration"
                else -> null
            }

            return buildString {
                if (metric.shouldPrefix) {
                    append("$defaultPrefix.")
                }

                append(metric.name)

                if (metric.shouldSuffix && suffix != null) {
                    append(".$suffix")
                }
            }
        }

        private fun gauge(name: String, fn: Supplier<Number>, vararg tags: Pair<String, Any?>) {
            val markers = Markers.empty().and("metric_name" to name, "tags" to tags)

            try {
                val meter = this.getOrCreateMeter(
                    name = name,
                    type = MetricType.GAUGE,
                    possibleTags = *tags,
                    fn = fn,
                ) as? Gauge

                if (meter == null) {
                    logger.warn(markers, "gauge#duplicate_tags")
                    return
                }
            } catch (ex: Exception) {
                logger.error(markers, "gauge#error", ex)
            }
        }

        private fun summary(name: String, value: Number, vararg tags: Pair<String, Any?>) = try {
            getOrCreateMeter("$name.summary", MetricType.SUMMARY, *tags)?.let {
                (it as? DistributionSummary)?.record(value.toDouble())
            }
        } catch (ex: Exception) {
            logger.error(log("metric_name" to name, "tags" to tags), "summary#error", ex)
        }

        private fun count(name: String, value: Number, vararg tags: Pair<String, Any?>) {
            val markers = Markers.empty().and("metric_name" to name, "tags" to tags)
            try {
                val meter = this.getOrCreateMeter(name, MetricType.COUNTER, *tags) as? Counter
                if (meter == null) {
                    logger.warn(markers, "inc#duplicate_tags")
                    return
                }
                meter.increment(value.toDouble())
            } catch (ex: Exception) {
                logger.error(markers, "inc#error", ex)
            }
        }

        private fun time(
            name: String,
            elapsed: Long,
            vararg tags: Pair<String, Any?>,
        ) {
            val markers = Markers.empty().and("metric_name" to name, "elapsed" to elapsed, "tags" to tags)
            try {
                val meter = this.getOrCreateMeter(name, MetricType.TIMED, *tags) as? Timer
                if (meter == null) {
                    logger.warn(markers, "time#duplicate_tags")
                    return
                }
                meter.record(elapsed, TimeUnit.MILLISECONDS)
            } catch (ex: Exception) {
                logger.error(markers, "time#error", ex)
            }
        }

        fun push(metric: Metrics, vararg tags: Pair<String, Any?>) = push(metric, metricValue = 1, tags = tags)

        fun push(metric: Metrics, fn: Supplier<Number>, vararg tags: Pair<String, Any?>) {
            val initialized = isInitialized()

            logger.info(Markers.append("initialized", initialized), "push#fn")

            if (initialized) {
                val name = buildMetricName(metric)

                when (metric) {
                    is AbstractGaugeMetric -> this.gauge(name, fn, *tags)
                    else -> logger.warn(Markers.append("metric_name", metric.name), "push#not_implemented")
                }
            }
        }

        fun push(metric: Metrics, metricValue: Number, vararg tags: Pair<String, Any?>) {
            if (isInitialized()) {
                val name = buildMetricName(metric)

                when (metric) {
                    is AbstractFridayCountMetrics -> this.count(name, metricValue, *tags)
                    is AbstractTimeByElapsedMetrics -> this.time(name, metric.elapsed(), *tags)
                    is AbstractSummary -> summary(name, metricValue, *tags)
                    is AbstractTimeByDateMetrics -> {
                        val elapsed = Duration.between(metric.time(), getZonedDateTime())
                        val value = when (metric.baseUnit()) {
                            TimeUnit.DAYS -> elapsed::toDays
                            TimeUnit.HOURS -> elapsed::toHours
                            TimeUnit.MINUTES -> elapsed::toMinutes
                            TimeUnit.SECONDS -> elapsed::toSeconds
                            else -> elapsed::toMillis
                        }

                        this.time(name, value.invoke(), *tags)
                    }

                    else -> logger.warn(Markers.append("metric_name", metric.name), "push#not_implemented")
                }
            }
        }
    }
}

fun Metrics.push(tags: Map<String, Any?> = emptyMap(), value: Number = 1) = Metrifier.push(this, metricValue = value, tags = tags.toList().toTypedArray())
fun Metrics.push(tags: Map<String, Any?> = emptyMap(), fn: Supplier<Number>) = Metrifier.push(this, fn = fn, tags = tags.toList().toTypedArray())

fun metricRegister(metric: Metrics, vararg possibleTags: Pair<String, Any?>) = when {
    !Metrifier.isInitialized() -> Unit
    else -> Metrifier.push(metric, *possibleTags)
}