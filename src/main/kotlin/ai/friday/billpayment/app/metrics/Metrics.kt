package ai.friday.billpayment.app.metrics

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import java.time.ZonedDateTime
import java.util.concurrent.TimeUnit

interface Metrics {
    val name: String
    val shouldPrefix: Boolean
    val shouldSuffix: <PERSON>olean
}

enum class MetricType {
    COUNTER, TIMED, SUMMARY, GAUGE
}

data class LoggerMetric(val metric: Metrics, val tags: List<Pair<String, Any>>)

abstract class AbstractMetrics(name: String? = null, override val shouldPrefix: Boolean = name.isNullOrEmpty(), override val shouldSuffix: Boolean = name.isNullOrEmpty()) : Metrics {
    final override var name: String = if (!name.isNullOrEmpty()) {
        name
    } else {
        val clazzName = this::class.java.simpleName

        clazzName.fold(StringBuilder(clazzName.length)) { acc, c ->
            if (c in 'A'..'Z') {
                (if (acc.isNotEmpty()) acc.append('.') else acc).append(c + ('a' - 'A'))
            } else {
                acc.append(c)
            }
        }.toString()
    }
}

abstract class AbstractTimeByElapsedMetrics : AbstractMetrics() {
    abstract fun elapsed(): Long
}

abstract class AbstractGaugeMetric : AbstractMetrics()

abstract class AbstractTimeByDateMetrics : AbstractMetrics() {
    abstract fun time(): ZonedDateTime
    abstract fun baseUnit(): TimeUnit?
}

abstract class AbstractFridayCountMetrics(name: String? = null) : AbstractMetrics(name)

abstract class AbstractSummary(name: String? = null) : AbstractMetrics(name)

object DynamicMetric {
    fun count(metricName: String, tags: Map<String, String?>) = createCountMetric(metricName).push(tags.filter { it.value != null })

    private fun createCountMetric(metricName: String): Metrics = object : AbstractFridayCountMetrics(PropertyNamingStrategies.LowerDotCaseStrategy().translate(metricName)) {}
}