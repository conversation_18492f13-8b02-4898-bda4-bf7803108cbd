package ai.friday.billpayment.app.whatsapp

import ai.friday.billpayment.app.uuid
import io.micronaut.context.annotation.ConfigurationProperties

interface WhatsAppClient {
    fun sendFlow(flowRequest: FlowRequest): Result<String>
}

interface WhatsAppService {
    fun sendFlow(command: SendFlowCommand): Result<SendFlowResult>
}

data class FlowRequest(
    val templateName: String,
    val phoneNumber: String,
    val messageId: String = uuid(),
    val flowToken: String = uuid(),
)

@ConfigurationProperties("whatsappflow")
interface WhatsAppFlowConfiguration {
    val salt: String
    val privateKey: String
}