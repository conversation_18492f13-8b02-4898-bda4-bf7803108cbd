package ai.friday.billpayment.app.whatsapp

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.security.EncryptionService
import ai.friday.billpayment.markers
import ai.friday.billpayment.plus
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory

@Singleton
class DefaultWhatsAppService(
    private val whatsAppClient: WhatsAppClient,
    private val accountRepository: AccountRepository,
    private val encryptionService: EncryptionService,
    private val flowConfiguration: WhatsAppFlowConfiguration,
) : WhatsAppService {
    private val logger = LoggerFactory.getLogger(DefaultWhatsAppService::class.java)

    override fun sendFlow(command: SendFlowCommand): Result<SendFlowResult> = when (command) {
        is SendFlowCommand.ByPhoneNumber -> sendFlowByPhoneNumber(command)
        is SendFlowCommand.ByAccountId -> sendFlowByAccountId(command)
    }

    private fun sendFlowByAccountId(command: SendFlowCommand.ByAccountId): Result<SendFlowResult> {
        val errors = mutableListOf<String>()
        val successes = mutableListOf<SendFlowResult.Item>()

        command.accounts.forEach { id ->
            val accountId = id.value
            val markers = markers("accountId" to accountId)

            runCatching {
                val account = accountRepository.findByIdOrNull(id)
                    ?: run {
                        errors.add(accountId)
                        logger.error(markers.plus("reason" to "AccountNotFound"), "DefaultWhatsAppService#sendFlow")
                        return@forEach
                    }

                val phoneNumber = account.mobilePhone

                val flowRequest = FlowRequest(
                    command.templateName,
                    phoneNumber,
                    flowToken = encryptionService.encrypt(accountId, flowConfiguration.salt),
                )

                whatsAppClient.sendFlow(flowRequest).let { result ->
                    markers.plus("phoneNumber" to phoneNumber)
                    val messageId = result.getOrThrow()

                    logger.info(markers, "DefaultWhatsAppService#sendFlow")
                    successes.add(SendFlowResult.Item(phoneNumber, messageId, accountId))
                }
            }.onFailure {
                errors.add(accountId)
                logger.error(markers, "DefaultWhatsAppService#sendFlow", it)
            }
        }

        return Result.success(SendFlowResult(successes, errors))
    }

    private fun sendFlowByPhoneNumber(command: SendFlowCommand.ByPhoneNumber): Result<SendFlowResult> {
        val errors = mutableListOf<String>()
        val successes = mutableListOf<SendFlowResult.Item>()

        command.phoneNumbers.forEach { phoneNumber ->
            // TODO: implement a layer to find accountId by phone number here

            runCatching {
                val flowRequest = FlowRequest(
                    templateName = command.templateName,
                    phoneNumber = phoneNumber,
                    flowToken = encryptionService.encrypt(phoneNumber, flowConfiguration.salt),
                )

                whatsAppClient.sendFlow(flowRequest).let { result ->
                    val markers = markers("phoneNumber" to phoneNumber)
                    val messageId = result.getOrThrow()

                    logger.info(markers, "DefaultWhatsAppService#sendFlow")
                    successes.add(SendFlowResult.Item(phoneNumber, messageId))
                }
            }.onFailure {
                errors.add(phoneNumber)
                logger.error(markers("phoneNumber" to phoneNumber), "DefaultWhatsAppService#sendFlow", it)
            }
        }

        return Result.success(SendFlowResult(successes, errors))
    }
}

sealed interface SendFlowCommand {
    data class ByPhoneNumber(
        val templateName: String,
        val phoneNumbers: List<String>,
    ) : SendFlowCommand

    data class ByAccountId(
        val templateName: String,
        val accounts: List<AccountId>,
    ) : SendFlowCommand
}

data class SendFlowResult(
    val successes: List<Item> = emptyList(),
    val errors: List<String> = emptyList(),
) {
    data class Item(
        val phoneNumber: String,
        val messageId: String,
        val accountId: String? = null,
    )
}