package ai.friday.billpayment.app.forecast

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.integrations.BillService
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import java.time.LocalDate
import java.time.temporal.TemporalAdjusters

@FridayMePoupe
open class ForecastService(
    private val balanceService: BalanceService,
    private val billService: BillService,
    private val walletLimitsService: WalletLimitsService,
) {
    open fun forecastAmountForBills(
        wallet: Wallet,
        forecastPeriod: ForecastPeriod = ForecastPeriod.TODAY,
        includeScheduledBillsOnly: Boolean = true,
    ): AmountForBills {
        val balance = balanceService.getBalanceFrom(wallet.founder.accountId, wallet.paymentMethodId)

        val (bills) = billService.sumAmountUntilEachDate(
            wallet.id,
            listOf(calcDatePeriod(forecastPeriod)),
        ) { bill ->
            !bill.isOverdue && (!includeScheduledBillsOnly || bill.schedule != null)
        }

        // FIXME melhorar o nome
        return AmountForBills(
            balance = balance.amount,
            bills = bills,
        )
    }

    internal fun calcDatePeriod(
        forecastPeriod: ForecastPeriod,
        now: LocalDate = getZonedDateTime().toLocalDate(),
    ): LocalDate {
        return when (forecastPeriod) {
            ForecastPeriod.TODAY -> now.plusDays(1)
            ForecastPeriod.SEVEN_DAYS -> now.plusDays(7)
            ForecastPeriod.FIFTEEN_DAYS -> now.plusDays(15)
            ForecastPeriod.THIRTY_DAYS -> now.plusDays(30)
            ForecastPeriod.FIRST_DAY_OF_NEXT_MONTH -> now.with(TemporalAdjusters.firstDayOfNextMonth())
            ForecastPeriod.FIRST_DAY_OF_TWO_MONTHS_AHEAD -> now.plusMonths(2)
                .with(TemporalAdjusters.firstDayOfMonth())
        }
    }

    open fun calculateWalletBalanceForecast(wallet: Wallet): WalletBalanceForecast {
        val now = getZonedDateTime().toLocalDate()
        val tomorrow = calcDatePeriod(ForecastPeriod.TODAY, now)
        val weekAhead = calcDatePeriod(ForecastPeriod.SEVEN_DAYS, now)
        val plusFifteenDays = calcDatePeriod(ForecastPeriod.FIFTEEN_DAYS, now)
        val plusThirtyDays = calcDatePeriod(ForecastPeriod.THIRTY_DAYS, now)
        val firstDayOfNextMonth = calcDatePeriod(ForecastPeriod.FIRST_DAY_OF_NEXT_MONTH, now)
        val firstDayOfTwoMonthsAhead = calcDatePeriod(ForecastPeriod.FIRST_DAY_OF_TWO_MONTHS_AHEAD, now)

        val balance = balanceService.getBalanceFrom(wallet.founder.accountId, wallet.paymentMethodId)

        val (amountToday, amountWeek, amountFifteenDays, amountThirtyDays, amountMonth, amountNextMonth) = billService.sumAmountUntilEachDate(
            wallet.id,
            listOf(
                tomorrow,
                weekAhead,
                plusFifteenDays,
                plusThirtyDays,
                firstDayOfNextMonth,
                firstDayOfTwoMonthsAhead,
            ),
        ) { bill ->
            !bill.isOverdue && bill.schedule == null
        }

        val (amountTodayOverdue) = billService.sumAmountUntilEachDate(
            wallet.id,
            listOf(
                tomorrow,
            ),
        ) { bill ->
            bill.isOverdue && bill.schedule == null
        }

        val (amountTodayScheduled, amountWeekScheduled, amountFifteenDaysSheduled, amountThirtyDaysScheduled, amountMonthScheduled, amountNextMonthScheduled) = billService.sumAmountUntilEachDate(
            wallet.id,
            listOf(
                tomorrow,
                weekAhead,
                plusFifteenDays,
                plusThirtyDays,
                firstDayOfNextMonth,
                firstDayOfTwoMonthsAhead,
            ),
        ) { bill ->
            bill.schedule != null
        }

        return WalletBalanceForecast(
            walletId = wallet.id,
            amount = balance.amount,
            open = BalanceForecast(
                amountToday = amountToday,
                amountWeek = amountWeek,
                amountFifteenDays = amountFifteenDays,
                amountThirtyDays = amountThirtyDays,
                amountNextMonth = amountNextMonth,
                amountMonth = amountMonth,
            ),
            scheduled = BalanceForecast(
                amountToday = amountTodayScheduled,
                amountWeek = amountWeekScheduled,
                amountFifteenDays = amountFifteenDaysSheduled,
                amountThirtyDays = amountThirtyDaysScheduled,
                amountNextMonth = amountNextMonthScheduled,
                amountMonth = amountMonthScheduled,
            ),
            dates = BalanceForecastDates(
                today = tomorrow.minusDays(1).format(dateFormat),
                week = weekAhead.minusDays(1).format(dateFormat),
                fifteenDays = plusFifteenDays.minusDays(1).format(dateFormat),
                month = firstDayOfNextMonth.minusDays(1).format(dateFormat),
                thirtyDays = plusThirtyDays.minusDays(1).format(dateFormat),
                nextMonth = firstDayOfTwoMonthsAhead.minusDays(1).format(dateFormat),
            ),
            overdueAmount = amountTodayOverdue,
        )
    }
}

private operator fun List<Long>.component6() = this[5]

data class BalanceForecast(
    val amountToday: Long,
    val amountWeek: Long,
    val amountFifteenDays: Long,
    val amountThirtyDays: Long,
    val amountMonth: Long,
    val amountNextMonth: Long,
)

data class BalanceForecastDates(
    val today: String,
    val week: String,
    val fifteenDays: String,
    val month: String,
    val thirtyDays: String,
    val nextMonth: String,
)

data class WalletBalanceForecast(
    val walletId: WalletId,
    val amount: Long,
    val open: BalanceForecast,
    val scheduled: BalanceForecast,
    val dates: BalanceForecastDates,
    val overdueAmount: Long,
)

fun WalletBalanceForecast.forPeriod(period: ForecastPeriod): Long {
    return when (period) {
        ForecastPeriod.TODAY -> scheduled.amountToday
        ForecastPeriod.SEVEN_DAYS -> open.amountWeek + scheduled.amountWeek
        ForecastPeriod.FIFTEEN_DAYS -> open.amountFifteenDays + scheduled.amountFifteenDays
        ForecastPeriod.THIRTY_DAYS -> open.amountThirtyDays + scheduled.amountThirtyDays
        ForecastPeriod.FIRST_DAY_OF_NEXT_MONTH -> open.amountMonth + scheduled.amountMonth
        ForecastPeriod.FIRST_DAY_OF_TWO_MONTHS_AHEAD -> open.amountNextMonth + scheduled.amountNextMonth
    }
}

data class AmountForBills(
    val balance: Long,
    val bills: Long,
    val hasFunds: Boolean = balance - bills >= 0,
)

enum class ForecastPeriod {
    TODAY, SEVEN_DAYS, FIFTEEN_DAYS, THIRTY_DAYS, FIRST_DAY_OF_NEXT_MONTH, FIRST_DAY_OF_TWO_MONTHS_AHEAD
}