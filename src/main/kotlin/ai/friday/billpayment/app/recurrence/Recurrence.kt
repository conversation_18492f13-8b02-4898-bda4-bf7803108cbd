package ai.friday.billpayment.app.recurrence

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.BillEventDependency
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.contact.ContactId
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.wallet.WalletId
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.UUID

@BillEventDependency
enum class RecurrenceFrequency {
    WEEKLY, BIWEEKLY, MONTHLY
}

data class RecurrenceId(val value: String)

@BillEventDependency
data class RecurrenceRule(
    val frequency: RecurrenceFrequency,
    val startDate: LocalDate,
    val pattern: String = "",
    var endDate: LocalDate? = null,
) {

    init {
        if (pattern.startsWith("COUNT")) {
            val count = pattern.substringAfter("COUNT=").toLong()
            if (count < 2) {
                throw IllegalArgumentException("Count must be greater than 1")
            }
            endDate = nextDate(startDate, count - 1)
        }
    }
    fun nextDate(currentDate: LocalDate, count: Long): LocalDate {
        return when (frequency) {
            RecurrenceFrequency.WEEKLY -> currentDate.plusWeeks(count)
            RecurrenceFrequency.BIWEEKLY -> currentDate.plusWeeks(count * 2)
            RecurrenceFrequency.MONTHLY -> currentDate.plusMonths(count)
        }
    }
}

interface BaseRecurrence {
    val id: RecurrenceId
    val walletId: WalletId
    val rule: RecurrenceRule
    val actionSource: ActionSource.WalletActionSource
    val created: ZonedDateTime
    val status: RecurrenceStatus
    val lastDueDate: LocalDate?
}

data class BillRecurrence(
    override val id: RecurrenceId = RecurrenceId("RECURRENCE-${UUID.randomUUID()}"),
    override val walletId: WalletId,
    val description: String = "",
    val amount: Long,
    override val rule: RecurrenceRule,
    val contactId: ContactId? = null,
    val contactAccountId: AccountId, // FIXME nao deve mais ser necessario
    val recipientName: String,
    val recipientDocument: String? = null,
    val recipientAlias: String = "",
    val recipientBankAccount: BankAccount? = null,
    val recipientPixKey: PixKey? = null,
    override val actionSource: ActionSource.WalletActionSource,
    override val created: ZonedDateTime,
    override val status: RecurrenceStatus,
    val bills: List<BillId> = listOf(),
    val billType: BillType,
    override val lastDueDate: LocalDate? = null,
    val billCategoryId: PFMCategoryId? = null,
) : BaseRecurrence {

    fun plusBillId(billId: BillId) = copy(bills = bills + billId)

    fun ignoreBillId(billId: BillId): BillRecurrence {
        val r = copy(bills = bills - billId)
        return if (r.bills.isEmpty()) r.setIgnored() else r
    }

    fun setEndDate(localDate: LocalDate) = copy(rule = rule.copy(endDate = localDate))

    fun setLastBillDueDate(localDate: LocalDate) = copy(lastDueDate = localDate)

    private fun setIgnored() = copy(status = RecurrenceStatus.IGNORED)
}

sealed class IgnoreRecurrenceErrors {
    class ServerException(val exception: Exception) : IgnoreRecurrenceErrors()
    data object BillNotFound : IgnoreRecurrenceErrors()
    data object BillIsNotRecurring : IgnoreRecurrenceErrors()
    data object BillIsNotIgnorable : IgnoreRecurrenceErrors()
    data object MemberNotAllowed : IgnoreRecurrenceErrors()
}

enum class Range {
    THIS,
    THIS_AND_FUTURE,
}

enum class RecurrenceStatus {
    ACTIVE, IGNORED
}