package ai.friday.billpayment.app.integrations

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.wallet.WalletId

class ItemNotFoundException(message: String) : Exception(message) {
    constructor(
        walletId: WalletId,
        billId: BillId,
    ) : this("Bill ${billId.value} not found on wallet ${walletId.value}")

    constructor(
        accountId: AccountId,
        accountPaymentMethodId: AccountPaymentMethodId,
    ) : this("Payment method ${accountPaymentMethodId.value} not found on account ${accountId.value}")

    constructor(billId: BillId) : this("Bill ${billId.value} not found")

    constructor(walletId: WalletId) : this("Wallet ${walletId.value} not found")
}

class DocumentOCRParserException(e: Exception) : RuntimeException(e)
class BigDataServerException(e: Exception? = null) : Exception(e)
open class InvalidDocumentImageException(message: String, e: Exception? = null) : Exception(message, e)
class DocumentMustBeOpenedException() : InvalidDocumentImageException("CNH Document must be opened")
class FaceNotFoundException() : InvalidDocumentImageException("Face not found on document image")

class DocumentNotFoundException : RuntimeException()
class AddressNotFoundException : Exception()
class DocumentBelongsToAMinorException : Exception()

class UserPoolPasswordValidationException(message: String?) : Exception(message)
class UserPoolUsernameExistsException(message: String?) : Exception(message)
class UserPoolUsernameNotFoundException(message: String?) : Exception(message)

class UserPoolListAuthEventsException(message: String?) : Exception(message)

class ECMProviderException(message: String) : Exception(message)

class ExternalAccountRegisterException(message: String) : Exception(message)

class AlreadyLockedException(lockName: String) :
    Exception("Lock with name $lockName is currently in use", null, true, false)