package ai.friday.billpayment.app.feature

import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Parameter

@EachProperty("feature-flags")
class FeatureFlag(@param:Parameter val name: String) {
    var enabled: FeatureFlagStatus = FeatureFlagStatus.NO_ONE
    var whitelist: List<String>? = null
    var groups: List<String>? = null

    var parsedGroups: List<AccountGroup>? = null

    fun check(accountId: AccountId, userGroups: List<AccountGroup> = emptyList()): Boolean {
        return when (enabled) {
            FeatureFlagStatus.NO_ONE -> false
            FeatureFlagStatus.EVERYONE -> true
            FeatureFlagStatus.SOME -> resolveWhitelist(accountId) || resolveGroups(userGroups)
        }
    }

    fun check(account: Account) = check(account.accountId, account.configuration.groups)

    private fun resolveWhitelist(userId: AccountId): Boolean {
        return whitelist?.let {
            userId.value in it
        } ?: false
    }

    private fun resolveGroups(userGroups: List<AccountGroup>): Boolean {
        return parseGroups()?.any { it in userGroups } ?: false
    }

    private fun parseGroups(): List<AccountGroup>? {
        if (parsedGroups != null) {
            return parsedGroups
        }

        parsedGroups = groups?.mapNotNull { AccountGroup.find(it) }

        return parsedGroups
    }
}

enum class FeatureFlagStatus {
    NO_ONE, SOME, EVERYONE
}