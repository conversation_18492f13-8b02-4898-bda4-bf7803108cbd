package ai.friday.billpayment.app.feature

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.hasDeveloperEarlyAccess
import ai.friday.billpayment.app.integrations.MaintenanceServicesEnum

interface FeaturesRepository {
    fun getAll(): Features
    fun update(type: FeaturesType, enabled: Boolean): Features
}

data class Features(
    val maintenanceMode: Boolean,
    val maintenanceServices: List<MaintenanceServicesEnum>,
    val proxy: <PERSON><PERSON><PERSON>,
    val tefAtSettlementHandler: <PERSON>olean,
)

enum class FeaturesType {
    MAINTENANCE_MODE, PROXY, TEF_AT_SETTLEMENT_HANDLER
}

fun Features.isMaintenanceMode(accountId: AccountId): Boolean {
    return maintenanceMode && !accountId.hasDeveloperEarlyAccess()
}