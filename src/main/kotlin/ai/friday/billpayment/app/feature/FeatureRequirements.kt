package ai.friday.billpayment.app.feature

import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.ME_POUPE_ENV
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires

@Requirements(
    Requires(property = "disable.cognito", notEquals = "true"),
)
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
annotation class RequiresCognito

@Requirements(
    Requires(property = "disable.export-s3", notEquals = "true"),
    Requires(notEnv = ["test", "staging"]),
    Requires(env = [FRIDAY_ENV, ME_POUPE_ENV]),
)
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
annotation class RequiresExportS3

@Requirements(
    Requires(property = "disable.credit-card-risk-analisys", notEquals = "true"),
    Requires(property = "features.credit-card-risk-analisys.provider", value = "clearsale"),
)
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
annotation class RequiresClearSale

@Requirements(
    Requires(property = "disable.credit-card-risk-analisys", notEquals = "true"),
    Requires(property = "features.credit-card-risk-analisys.provider", value = "quod", defaultValue = "quod"),
)
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
annotation class RequiresQuod

@Requirements(
    Requires(property = "features.credit-card.provider", value = "cielo", defaultValue = "cielo"),
)
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
annotation class RequiresCielo

@Requirements(
    Requires(property = "features.credit-card.provider", value = "software-express"),
)
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
annotation class RequiresSoftwareExpress

@Requirements(
    Requires(property = "modules.in-app-subscription-coupon.enabled", value = "true"),
    Requires(notEnv = ["test"]),
)
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
annotation class RequiresInAppSubscriptionCoupon

@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
@Requirements(
    Requires(property = "integrations.celcoin.enabled", value = "true"),
    Requires(notEnv = ["test"]),
)
annotation class RequiresCelcoin

@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
@Requirements(
    Requires(property = "features.eventBus.enabled", value = "true", defaultValue = "false"),
)
annotation class RequiresEventBus

@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
@Requirements(
    Requires(property = "integrations.intercom.enabled", value = "true", defaultValue = "true"),
    Requires(notEnv = ["test"]),
)
annotation class RequiresIntercom