package ai.friday.billpayment.app.onboarding.instrumentation

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.instrumentation.BaseInstrumentationService
import ai.friday.billpayment.app.instrumentation.InstrumentationRepository
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
open class OnboardingInstrumentationService(
    instrumentationRepository: InstrumentationRepository<OnboardingInstrumentationEvent>,
) : BaseInstrumentationService<OnboardingInstrumentationEvent>(instrumentationRepository) {
    open fun singlePixCreated(accountId: AccountId) {
        try {
            publishEventAsync(OnboardingInstrumentationEvent.SinglePixCreated(accountId))
        } catch (exception: Exception) {
            LOG.error(
                Markers.append("accountId", accountId),
                "singlePixCreated",
                exception,
            )
        }
    }

    open fun singlePixPaid(bill: Bill) {
        try {
            publishEventAsync(OnboardingInstrumentationEvent.SinglePixPaid(AccountId(bill.walletId.value)))
        } catch (exception: Exception) {
            LOG.error(
                Markers.append("accountId", bill.walletId.value),
                "singlePixPaid",
                exception,
            )
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(OnboardingInstrumentationService::class.java)
    }
}