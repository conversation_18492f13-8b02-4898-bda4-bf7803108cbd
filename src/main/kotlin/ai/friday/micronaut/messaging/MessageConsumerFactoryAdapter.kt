package ai.friday.micronaut.messaging

import ai.friday.morning.messaging.MessageConsumerConfiguration
import ai.friday.morning.messaging.MessageConsumerFactory
import ai.friday.morning.messaging.MessageHandler
import ai.friday.morning.messaging.TopicSubscriberConfiguration
import ai.friday.morning.messaging.TracerWrapper
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Parameter
import io.micronaut.discovery.event.ServiceReadyEvent
import io.micronaut.discovery.event.ServiceStoppedEvent
import io.micronaut.runtime.event.ApplicationShutdownEvent
import io.micronaut.runtime.event.annotation.EventListener
import io.micronaut.tracing.annotation.NewSpan
import io.opentracing.Tracer
import jakarta.inject.Singleton
import java.time.Duration
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient

@Singleton
open class MessageConsumerFactoryAdapter(
    amazonSQS: SqsClient,
    amazonSNS: SnsClient,
    defaultConfiguration: MessageConsumerDefaultConfiguration,
    configurations: List<MessageConsumerConfigurationAdapter>,
    handlers: List<MessageHandler>,
    tracerWrapper: TracerWrapper,
) {

    private val logger = LoggerFactory.getLogger(MessageConsumerFactoryAdapter::class.java)

    private val service = MessageConsumerFactory(
        amazonSQS = amazonSQS,
        amazonSNS = amazonSNS,
        configurations = configurations.map { it.toMessageConsumerConfiguration(defaultConfiguration) },
        handlers = handlers,
        tracerWrapper = tracerWrapper,
    )

    @NewSpan
    @EventListener
    open fun onServiceReady(event: ServiceReadyEvent) {
        service.start()
    }

    @NewSpan
    @EventListener
    open fun onBeginShutdown(event: ApplicationShutdownEvent) {
        service.beginShutdown()
    }

    @NewSpan
    @EventListener
    open fun onServiceStopped(event: ServiceStoppedEvent) {
        service.shutdown()
    }
}

@ConfigurationProperties("friday.morning.messaging.defaults.consumer")
interface MessageConsumerDefaultConfiguration {
    val corePoolSize: Int
    val maximumPoolSize: Int
    val keepAliveTime: Duration
    val blockingDequeCapacity: Int
    val waitTime: Duration
    val awaitTerminationTime: Duration
    val visibilityTimeout: Duration
    val maxNumberOfMessages: Int
    val coolDownTime: Duration
    val maxReceiveCount: Int
}

@EachProperty("friday.morning.messaging.consumer")
data class MessageConsumerConfigurationAdapter @ConfigurationInject constructor(
    @param:Parameter val name: String,
    val queueName: String,
    val corePoolSize: Int? = null,
    val maximumPoolSize: Int? = null,
    val keepAliveTime: Duration? = null,
    val blockingDequeCapacity: Int? = null,
    val waitTime: Duration? = null,
    val awaitTerminationTime: Duration? = null,
    val visibilityTimeout: Duration? = null,
    val maxNumberOfMessages: Int? = null,
    val dlqArn: String? = null,
    val coolDownTime: Duration? = null,
    val topic: DefaultTopicSubscriberConfiguration? = null,
    val maxReceiveCount: Int? = null,

) {
    @ConfigurationProperties("topic")
    data class DefaultTopicSubscriberConfiguration @ConfigurationInject constructor(
        override val arn: String,
        override val filter: String?,
    ) : TopicSubscriberConfiguration

    fun toMessageConsumerConfiguration(defaultConfiguration: MessageConsumerDefaultConfiguration) = MessageConsumerConfiguration(
        name = name,
        queueName = queueName,
        corePoolSize = corePoolSize ?: defaultConfiguration.corePoolSize,
        maximumPoolSize = maximumPoolSize ?: defaultConfiguration.maximumPoolSize,
        keepAliveTime = keepAliveTime ?: defaultConfiguration.keepAliveTime,
        blockingDequeCapacity = blockingDequeCapacity ?: defaultConfiguration.blockingDequeCapacity,
        waitTime = waitTime ?: defaultConfiguration.waitTime,
        awaitTerminationTime = awaitTerminationTime ?: defaultConfiguration.awaitTerminationTime,
        visibilityTimeout = visibilityTimeout ?: defaultConfiguration.visibilityTimeout,
        maxNumberOfMessages = maxNumberOfMessages ?: defaultConfiguration.maxNumberOfMessages,
        coolDownTime = coolDownTime ?: defaultConfiguration.coolDownTime,
        topic = topic,
        maxReceiveCount = maxReceiveCount ?: defaultConfiguration.maxReceiveCount,
    )
}

@Singleton
open class MicronautTracerWrapper(
    private val tracer: Tracer,
) : TracerWrapper {
    @NewSpan
    override fun <T> withNewSpan(operationName: String, vararg baggageItem: Pair<String, String>, toBeExecuted: () -> T): T {
        return toBeExecuted()
    }
}