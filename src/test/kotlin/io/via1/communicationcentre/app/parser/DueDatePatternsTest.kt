package io.via1.communicationcentre.app.parser

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class DueDatePatternsTest {
    @ParameterizedTest
    @CsvSource("10/05/2020,10/05/2020", "10/05/20,10/05/2020", "10 mai 2020,10/05/2020", "10 MAI 2020,10/05/2020", "10.05.2020,10/05/2020", "10-05-2020,10/05/2020")
    fun deve_normalizar_para_o_valor_correto(input: String?, expected: String?) {
        val result = DueDatePatterns.normalize(input)
        Assertions.assertEquals(expected, result)
    }
}