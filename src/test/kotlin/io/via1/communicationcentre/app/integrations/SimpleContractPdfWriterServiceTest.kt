/*
package io.via1.communicationcentre.app.integrations

import io.mockk.every
import io.mockk.mockk
import io.via1.communicationcentre.adapters.pdfbox.PdfBoxWriter
import io.via1.communicationcentre.app.ContractForm
import io.via1.communicationcentre.app.ContractSignature
import io.via1.communicationcentre.app.SimpleContractForm
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStream
import java.time.ZonedDateTime
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
class SimpleContractPdfWriterServiceTest {

    private val simpleService = SimpleContractPdfWriterService(
        PdfBoxWriter(
            mockk() {
                every {
                    activeNames
                } returns setOf("default")
            },
        ),
    )
    private val simpleContractFormFriday = SimpleContractForm("Meu nome completo Friday", "12345678909", "31/01/1990", "<EMAIL>", "+5521987654321", false, Tenant.FRIDAY)
    private val simpleContractFormPepFriday = SimpleContractForm("Meu nome completo Friday", "12345678909", "31/01/1990", "<EMAIL>", "+5521987654321", true, Tenant.FRIDAY)
    private val simpleContractFormMP = SimpleContractForm("Meu nome completo Me Poupe!", "12345678909", "31/01/1990", "<EMAIL>", "+5521987654321", false, Tenant.ME_POUPE)
    private val simpleContractFormPepMP = SimpleContractForm("Meu nome completo Me Poupe!", "12345678909", "31/01/1990", "<EMAIL>", "+5521987654321", true, Tenant.ME_POUPE)
    private val signature = ContractSignature(ZonedDateTime.now(), "***********", "signature_key")

    private val completeService = ContractPdfWriterService(
        PdfBoxWriter(
            mockk() {
                every {
                    activeNames
                } returns setOf("default")
            },
        ),
    )

    private val completeContractFormFriday = ContractForm("Meu nome completo Friday", "12345678909", "nome da rua", "bairro", "Rio de Janeiro", "RJ", "12345-678", "31/01/1990", "Local de nascimento", "111222333444-5", "TIPO_DOCUMENTO", "31/02/2020", "BY", "nome do pai", "nome da mae", "<EMAIL>", "+5521987654321", false, null, "R$1.234,56", Tenant.FRIDAY)
    private val completeContractFormPepFriday = ContractForm("Meu nome completo Friday", "12345678909", "nome da rua", "bairro", "Rio de Janeiro", "RJ", "12345-678", "31/01/1990", "Local de nascimento", "111222333444-5", "TIPO_DOCUMENTO", "31/02/2020", "BY", "nome do pai", "nome da mae", "<EMAIL>", "+5521987654321", true, null, "R$1.234,56", Tenant.FRIDAY)
    private val completeContractFormMP = ContractForm("Meu nome completo Me Poupe!", "12345678909", "nome da rua", "bairro", "Rio de Janeiro", "RJ", "12345-678", "31/01/1990", "Local de nascimento", "111222333444-5", "TIPO_DOCUMENTO", "31/02/2020", "BY", "nome do pai", "nome da mae", "<EMAIL>", "+5521987654321", false, null, "R$1.234,56", Tenant.ME_POUPE)
    private val completeContractFormPepMP = ContractForm("Meu nome completo Me Poupe!", "12345678909", "nome da rua", "bairro", "Rio de Janeiro", "RJ", "12345-678", "31/01/1990", "Local de nascimento", "111222333444-5", "TIPO_DOCUMENTO", "31/02/2020", "BY", "nome do pai", "nome da mae", "<EMAIL>", "+5521987654321", true, null, "R$1.234,56", Tenant.ME_POUPE)

    @Test
    @Disabled("esse teste gera o pdf, mas não é pra rodar sempre")
    fun `deve gerar o relatorio simplificado Friday corretamente()`() {
        val outputStream: OutputStream = FileOutputStream(File("simple_contract_test_no_pep_friday.pdf"))
        simpleService.createContract(outputStream, simpleContractFormFriday, signature)

        val outputStream2: OutputStream = FileOutputStream(File("simple_contract_test_with_pep_friday.pdf"))
        simpleService.createContract(outputStream2, simpleContractFormPepFriday, signature)
    }

    @Test
    @Disabled("esse teste gera o pdf, mas não é pra rodar sempre")
    fun `deve gerar o relatorio simplificado Me Poupe corretamente()`() {
        val outputStream: OutputStream = FileOutputStream(File("simple_contract_test_no_pep_me_poupe.pdf"))
        simpleService.createContract(outputStream, simpleContractFormMP, signature)

        val outputStream2: OutputStream = FileOutputStream(File("simple_contract_test_with_pep_me_poupe.pdf"))
        simpleService.createContract(outputStream2, simpleContractFormPepMP, signature)
    }

    @Test
    @Disabled("esse teste gera o pdf, mas não é pra rodar sempre")
    fun `deve gerar o relatorio completo Friday corretamente()`() {
        val outputStream: OutputStream = FileOutputStream(File("complete_contract_test_no_pep_friday.pdf"))
        completeService.createContract(outputStream, completeContractFormFriday, signature)

        val outputStream2: OutputStream = FileOutputStream(File("complete_contract_test_with_pep_friday.pdf"))
        completeService.createContract(outputStream2, completeContractFormPepFriday, signature)
    }

    @Test
    @Disabled("esse teste gera o pdf, mas não é pra rodar sempre")
    fun `deve gerar o relatorio completo Me Poupe corretamente()`() {
        val outputStream: OutputStream = FileOutputStream(File("complete_contract_test_no_pep_me_poupe.pdf"))
        completeService.createContract(outputStream, completeContractFormMP, signature)

        val outputStream2: OutputStream = FileOutputStream(File("complete_contract_test_with_pep_me_poupe.pdf"))
        completeService.createContract(outputStream2, completeContractFormPepMP, signature)
    }
}*/