package ai.friday.billpayment.app.onboarding

import DynamoDBUtils
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.OnboardingTestPixDbRepository
import ai.friday.billpayment.adapters.dynamodb.OnboardingTestPixDynamoDAO
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AdService
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillIgnored
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillTag.ONBOARDING_TEST_PIX
import ai.friday.billpayment.app.bill.BillTagAdded
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateBillService
import ai.friday.billpayment.app.bill.CreatePixRequest
import ai.friday.billpayment.app.bill.RegisterUpdated
import ai.friday.billpayment.app.bill.UpdatedRegisterData
import ai.friday.billpayment.app.chatbot.ChatbotNotificationService
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BankAccountService
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.TestPixReminderType
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.journey.UserJourneyService
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyDetailsResult
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixKeyOwner
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.balance
import ai.friday.billpayment.integration.BILL_ID
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.BILL_ID_3
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.parseDateTextAsZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.date.shouldBeAfter
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.math.BigInteger
import java.time.ZonedDateTime
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource
import org.junit.jupiter.params.provider.ValueSource

class OnboardingTestPixServiceTest {
    private val walletFixture = WalletFixture(defaultWalletId = ACCOUNT.defaultWalletId())
    private val wallet = walletFixture.buildWallet()

    private val pixKeyManagement: PixKeyManagement =
        mockk {
            every {
                findKeyDetails(any(), any())
            } answers {
                createPixKeyDetailsResult(
                    pixKey = firstArg(),
                    document = secondArg(),
                ).right()
            }
        }

    private val userJourneyService: UserJourneyService =
        mockk {
            every {
                trackEventAsync(any(), any())
            } just Runs
        }

    private val accountRepository: AccountRepository =
        mockk {
            every {
                findByIdOrNull(ACCOUNT.accountId)
            } returns
                ACCOUNT.copy(
                    configuration =
                    ACCOUNT.configuration.copy(
                        groups = listOf(AccountGroup.ONBOARDING_TEST_PIX),
                    ),
                )
            every {
                findAccountPaymentMethodByIdAndAccountId(
                    accountPaymentMethodId = wallet.paymentMethodId,
                    accountId = ACCOUNT.accountId,
                )
            } returns balance
        }

    private val walletRepository: WalletRepository =
        mockk {
            every {
                findWallet(ACCOUNT.defaultWalletId())
            } returns wallet
        }

    private val createBillService: CreateBillService = mockk()

    private val bankAccountService: BankAccountService =
        mockk {
            every {
                transfer(
                    originAccountNo = any(),
                    targetAccountNo = any(),
                    amount = any(),
                    operationId = any(),
                )
            } returns
                BankTransfer(
                    operationId = BankOperationId(value = ""),
                    gateway = FinancialServiceGateway.ARBI,
                    status = BankOperationStatus.SUCCESS,
                    amount = 0,
                    authentication = "",
                    errorDescription = "",
                    debitOperationNumber = null,
                    creditOperationNumber = null,
                    pixKeyDetails = null,
                )
        }

    private val billRepository: BillRepository = mockk {
        every { findBill(any(), any()) } answers {
            createBillView(firstArg(), secondArg())
        }
    }

    private val defaultOriginAccountNo = "123456"

    private val systemActivityService: SystemActivityService =
        mockk(relaxed = true) {
            every {
                getOnboardingTestPix(ACCOUNT.accountId)
            } returns null
        }

    private val billEventPublisher: BillEventPublisher = mockk(relaxed = true)

    private val billEventRepository: BillEventRepository = mockk()

    private val notificationAdapter: NotificationAdapter = mockk(relaxed = true)

    val enhancedClient = DynamoDBUtils.setupDynamoDB()

    private val onboardingTestPixRepository =
        OnboardingTestPixDbRepository(
            client = OnboardingTestPixDynamoDAO(cli = enhancedClient),
        )

    private val adService: AdService =
        mockk {
            every {
                publishOnboardingTestPixCreated(any())
            } just Runs
        }

    val balanceService =
        mockk<BalanceService> {
            every { invalidate(any()) } just Runs
        }

    val chatBotNotificationService = mockk<ChatbotNotificationService>(relaxed = true)

    private val service =
        OnboardingTestPixService(
            pixKeyManagement = pixKeyManagement,
            accountRepository = accountRepository,
            createBillService = createBillService,
            bankAccountService = bankAccountService,
            walletRepository = walletRepository,
            configuration =
            mockk {
                every {
                    originAccountNo
                } returns defaultOriginAccountNo
            },
            systemActivityService = systemActivityService,
            billEventPublisher = billEventPublisher,
            userJourneyService = userJourneyService,
            onboardingTestPixRepository = onboardingTestPixRepository,
            billEventRepository = billEventRepository,
            notificationAdapter = notificationAdapter,
            adService = adService,
            balanceService = balanceService,
            billRepository = billRepository,
            chatBotNotificationService = chatBotNotificationService,
            onboardingInstrumentationService = mockk(relaxed = true),
        )

    @Nested
    @DisplayName("ao fazer a criação do pix de exemplo via chatbot")
    inner class CreateChatbotExamplePixTest {

        @Test
        fun `nao deve transferir fundos nem criar pix se jah marcou a atividade como concluida`() {
            every {
                systemActivityService.getOnboardingTestPix(ACCOUNT.accountId)
            } returns "CREATED"

            val result = service.createChatbotExamplePayment(accountId = ACCOUNT.accountId)

            result.isLeft() shouldBe true
            result.getOrElse {
                it shouldBe CreateOnboardingTestPixError.Conflict
            }

            verify(exactly = 0) {
                bankAccountService.transfer(any(), any(), any(), any())

                createBillService.createPix(any(), any())
                billEventPublisher.publish(any(), any<BillTagAdded>())

                userJourneyService.trackEventAsync(any(), any())
                systemActivityService.setOnboardingTestPixCreated(any())
                adService.publishOnboardingTestPixCreated(any())
            }
        }

        @Test
        fun `nao deve transferir fundos nem criar pix quando nao encontra o account`() {
            every {
                accountRepository.findByIdOrNull(ACCOUNT.accountId)
            } returns null

            val result = service.createChatbotExamplePayment(ACCOUNT.accountId)

            result.isLeft() shouldBe true
            result.getOrElse { it shouldBe CreateOnboardingTestPixError.AccountIdNotEligible }

            verify(exactly = 0) {
                bankAccountService.transfer(any(), any(), any(), any())

                createBillService.createPix(any(), any())
                billEventPublisher.publish(any(), any<BillTagAdded>())

                userJourneyService.trackEventAsync(any(), any())
                systemActivityService.setOnboardingTestPixCreated(any())
                adService.publishOnboardingTestPixCreated(any())
            }
        }

        @Test
        fun `nao deve transferir fundos nem criar pix quando o account nao esta no grupo de onboarding`() {
            every {
                accountRepository.findByIdOrNull(ACCOUNT.accountId)
            } returns ACCOUNT

            val result = service.createChatbotExamplePayment(ACCOUNT.accountId)

            result.isLeft() shouldBe true
            result.getOrElse { it shouldBe CreateOnboardingTestPixError.AccountIdNotEligible }

            verify(exactly = 0) {
                bankAccountService.transfer(any(), any(), any(), any())

                createBillService.createPix(any(), any())
                billEventPublisher.publish(any(), any<BillTagAdded>())

                userJourneyService.trackEventAsync(any(), any())
                systemActivityService.setOnboardingTestPixCreated(any())
                adService.publishOnboardingTestPixCreated(any())
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.onboarding.OnboardingTestPixServiceTest#pixKeyErrors")
        fun `nao deve transferir fundos nem criar pix quando nao encontra chave`(pixKeyError: PixKeyError) {
            every {
                pixKeyManagement.findKeyDetails(any(), any())
            } returns pixKeyError.left()

            val result = service.createChatbotExamplePayment(ACCOUNT.accountId)

            result.isLeft() shouldBe true
            result.getOrElse { it shouldBe CreateOnboardingTestPixError.NoPixKeyFound }

            verify(exactly = 0) {
                bankAccountService.transfer(any(), any(), any(), any())

                createBillService.createPix(any(), any())
                billEventPublisher.publish(any(), any<BillTagAdded>())

                userJourneyService.trackEventAsync(any(), any())
                systemActivityService.setOnboardingTestPixCreated(any())
                adService.publishOnboardingTestPixCreated(any())
            }
        }

        @Test
        fun `nao deve transferir fundos nem criar pix quando nao encontra chave do mesmo cpf`() {
            every {
                pixKeyManagement.findKeyDetails(any(), any())
            } answers {
                createPixKeyDetailsResult(
                    pixKey = firstArg(),
                    document = "",
                ).right()
            }

            val result = service.createChatbotExamplePayment(ACCOUNT.accountId)

            result.isLeft() shouldBe true
            result.getOrElse { it shouldBe CreateOnboardingTestPixError.NoPixKeyFound }

            verify(exactly = 0) {
                bankAccountService.transfer(any(), any(), any(), any())

                createBillService.createPix(any(), any())
                billEventPublisher.publish(any(), any<BillTagAdded>())

                userJourneyService.trackEventAsync(any(), any())
                systemActivityService.setOnboardingTestPixCreated(any())
                adService.publishOnboardingTestPixCreated(any())
            }
        }

        @ParameterizedTest
        @EnumSource(value = BankOperationStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["SUCCESS"])
        fun `nao deve criar pix quando nao consegue transferir fundos`(bankOperationStatus: BankOperationStatus) {
            every {
                bankAccountService.transfer(
                    originAccountNo = any(),
                    targetAccountNo = any(),
                    amount = any(),
                    operationId = any(),
                )
            } returns
                BankTransfer(
                    operationId = BankOperationId(value = ""),
                    gateway = FinancialServiceGateway.ARBI,
                    status = bankOperationStatus,
                    amount = 0,
                    authentication = "",
                    errorDescription = "",
                    debitOperationNumber = null,
                    creditOperationNumber = null,
                    pixKeyDetails = null,
                )

            val result = service.createChatbotExamplePayment(ACCOUNT.accountId)

            result.isLeft() shouldBe true
            result.getOrElse { it shouldBe CreateOnboardingTestPixError.FundsNotTransferred }

            verify(exactly = 0) {
                createBillService.createPix(any(), any())
                billEventPublisher.publish(any(), any<BillTagAdded>())

                userJourneyService.trackEventAsync(any(), any())
                systemActivityService.setOnboardingTestPixCreated(any())
                adService.publishOnboardingTestPixCreated(any())
            }
        }

        @Test
        fun `nao deve marcar a atividade quando nao conseguir criar nenhum pix`() {
            every {
                createBillService.createPix(any(), any())
            } returns CreateBillResult.FAILURE.ServerError(mockk())

            val result = service.createChatbotExamplePayment(ACCOUNT.accountId)

            result.isLeft() shouldBe true
            result.getOrElse {
                it shouldBe CreateOnboardingTestPixError.PixNotCreated
            }

            verify(exactly = 0) {
                billEventPublisher.publish(any(), any<BillTagAdded>())

                userJourneyService.trackEventAsync(any(), any())
                systemActivityService.setOnboardingTestPixCreated(any())
                adService.publishOnboardingTestPixCreated(any())
            }
        }

        @Test
        fun `deve criar 1 pix quando encontra ao menos uma chave do mesmo cpf`() {
            every {
                createBillService.createPix(any(), any())
            } returns
                CreateBillResult.SUCCESS(
                    bill =
                    mockk {
                        every { billId } returns BillId(BILL_ID)
                        every { walletId } returns WalletId(WALLET_ID)
                    },
                )

            val result = service.createChatbotExamplePayment(ACCOUNT.accountId)

            result.isRight() shouldBe true
//            result.map {
//                it shouldBe 1
//            }

            val onboardingTestPix = onboardingTestPixRepository.findByStatus(OnboardingTestPixStatus.IN_PROGRESS)

            onboardingTestPix.size shouldBe 1
            with(onboardingTestPix.first()) {
                this.accountId shouldBe ACCOUNT.accountId
                this.billIds[0].value shouldBe BILL_ID
                this.billPaidCounter shouldBe 0
            }

            val slot = mutableListOf<CreatePixRequest>()
            verify {
                createBillService.createPix(capture(slot), false)
                balanceService.invalidate(any())
            }
            slot.size shouldBe 1
            slot[0].dueDate shouldBe getLocalDate()

            slot.forEach {
                it.amount shouldBe 1
                it.description shouldBe "Bônus novo cadastro"
                it.walletId shouldBe ACCOUNT.defaultWalletId()
                it.recipient.accountId shouldBe ACCOUNT.accountId
                it.recipient.pixKey!!.value shouldBe ACCOUNT.document
                it.recipient.pixKey!!.type shouldBe PixKeyType.CPF
                it.recipient.name shouldBe ACCOUNT.name
                it.recipient.document shouldBe ACCOUNT.document
            }

            val billSlot = mutableListOf<Bill>()
            val eventSlot = mutableListOf<BillTagAdded>()

            verify {
                billEventPublisher.publish(capture(billSlot), capture(eventSlot))
            }

            billSlot.size shouldBe 1
            billSlot[0].billId.value shouldBe BILL_ID

            eventSlot.size shouldBe 1
            eventSlot[0].billId.value shouldBe BILL_ID

            eventSlot.forEach {
                it.tag shouldBe ONBOARDING_TEST_PIX
            }

            verify {
                bankAccountService.transfer(
                    originAccountNo = defaultOriginAccountNo,
                    targetAccountNo = (balance.method as InternalBankAccount).buildFullAccountNumber(),
                    amount = 1,
                    operationId = any(),
                )
                systemActivityService.setOnboardingTestPixCreated(ACCOUNT.accountId)
                adService.publishOnboardingTestPixCreated(ACCOUNT.accountId)
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.onboarding.OnboardingTestPixServiceTest#pixKeyErrors")
        fun `nao deve transferir fundos nem criar pix quando nao encontra chave customizada`(pixKeyError: PixKeyError) {
            every {
                pixKeyManagement.findKeyDetails(any(), any())
            } returns pixKeyError.left()

            val result = service.createChatbotExamplePayment(ACCOUNT.accountId, pixKey = PixKey(type = PixKeyType.PHONE, value = "***********"))

            result.isLeft() shouldBe true
            result.getOrElse { it shouldBe CreateOnboardingTestPixError.NoPixKeyFound }

            verify(exactly = 0) {
                bankAccountService.transfer(any(), any(), any(), any())

                createBillService.createPix(any(), any())
                billEventPublisher.publish(any(), any<BillTagAdded>())

                userJourneyService.trackEventAsync(any(), any())
                systemActivityService.setOnboardingTestPixCreated(any())
                adService.publishOnboardingTestPixCreated(any())
            }
        }

        @Test
        fun `nao deve transferir fundos nem criar pix quando chave customizada for de outro cpf`() {
            every {
                pixKeyManagement.findKeyDetails(any(), any())
            } answers {
                createPixKeyDetailsResult(
                    pixKey = firstArg(),
                    document = "***********",
                ).right()
            }

            val result = service.createChatbotExamplePayment(ACCOUNT.accountId, pixKey = PixKey(type = PixKeyType.PHONE, value = "***********"))

            result.isLeft() shouldBe true
            result.getOrElse { it shouldBe CreateOnboardingTestPixError.PixKeyFromDifferentOwner }

            verify(exactly = 0) {
                bankAccountService.transfer(any(), any(), any(), any())

                createBillService.createPix(any(), any())
                billEventPublisher.publish(any(), any<BillTagAdded>())

                userJourneyService.trackEventAsync(any(), any())
                systemActivityService.setOnboardingTestPixCreated(any())
                adService.publishOnboardingTestPixCreated(any())
            }
        }

        @Test
        fun `deve criar 1 pix quando a chave customizada for do mesmo cpf`() {
            every {
                pixKeyManagement.findKeyDetails(any(), any())
            } answers {
                createPixKeyDetailsResult(
                    pixKey = firstArg(),
                    document = ACCOUNT.document,
                ).right()
            }

            every {
                createBillService.createPix(any(), any())
            } returns
                CreateBillResult.SUCCESS(
                    bill =
                    mockk {
                        every { billId } returns BillId(BILL_ID)
                        every { walletId } returns WalletId(WALLET_ID)
                    },
                )

            val result = service.createChatbotExamplePayment(ACCOUNT.accountId, pixKey = PixKey(type = PixKeyType.PHONE, value = "***********"))

            result.isRight() shouldBe true

            val onboardingTestPix = onboardingTestPixRepository.findByStatus(OnboardingTestPixStatus.IN_PROGRESS)

            onboardingTestPix.size shouldBe 1
            with(onboardingTestPix.first()) {
                this.accountId shouldBe ACCOUNT.accountId
                this.billIds[0].value shouldBe BILL_ID
                this.billPaidCounter shouldBe 0
            }

            val slot = mutableListOf<CreatePixRequest>()
            verify {
                createBillService.createPix(capture(slot), false)
                balanceService.invalidate(any())
            }
            slot.size shouldBe 1
            slot[0].dueDate shouldBe getLocalDate()

            slot.forEach {
                it.amount shouldBe 1
                it.description shouldBe "Bônus novo cadastro"
                it.walletId shouldBe ACCOUNT.defaultWalletId()
                it.recipient.accountId shouldBe ACCOUNT.accountId
                it.recipient.pixKey!!.value shouldBe "***********"
                it.recipient.pixKey!!.type shouldBe PixKeyType.PHONE
                it.recipient.name shouldBe ACCOUNT.name
                it.recipient.document shouldBe ACCOUNT.document
            }

            val billSlot = mutableListOf<Bill>()
            val eventSlot = mutableListOf<BillTagAdded>()

            verify {
                billEventPublisher.publish(capture(billSlot), capture(eventSlot))
            }

            billSlot.size shouldBe 1
            billSlot[0].billId.value shouldBe BILL_ID

            eventSlot.size shouldBe 1
            eventSlot[0].billId.value shouldBe BILL_ID

            eventSlot.forEach {
                it.tag shouldBe ONBOARDING_TEST_PIX
            }

            verify {
                bankAccountService.transfer(
                    originAccountNo = defaultOriginAccountNo,
                    targetAccountNo = (balance.method as InternalBankAccount).buildFullAccountNumber(),
                    amount = 1,
                    operationId = any(),
                )
                systemActivityService.setOnboardingTestPixCreated(ACCOUNT.accountId)
                adService.publishOnboardingTestPixCreated(ACCOUNT.accountId)
            }
        }

        @Test
        fun `deve retornar pix já criado se existir e estiver ACTIVE`() {
            every {
                systemActivityService.getOnboardingTestPix(ACCOUNT.accountId)
            } returns "CREATED"

            onboardingTestPixRepository.save(
                OnboardingTestPix(
                    accountId = ACCOUNT.accountId,
                    createdAt = getZonedDateTime(),
                    updatedAt = getZonedDateTime(),
                    remindedAt = getZonedDateTime(),
                    billIds =
                    listOf(
                        BillId(BILL_ID),
                    ),
                    status = OnboardingTestPixStatus.IN_PROGRESS,
                    billPaidCounter = 0,
                ),
            )

            val result = service.createChatbotExamplePayment(ACCOUNT.accountId, pixKey = PixKey(type = PixKeyType.PHONE, value = "***********"))

            result.isLeft() shouldBe true

            result.mapLeft {
                (it is CreateOnboardingTestPixError.PixAlreadyCreated) shouldBe true
                (it as CreateOnboardingTestPixError.PixAlreadyCreated).billView.billId.value shouldBe BILL_ID
            }

            verify(exactly = 0) {
                bankAccountService.transfer(any(), any(), any(), any())

                createBillService.createPix(any(), any())
                billEventPublisher.publish(any(), any<BillTagAdded>())

                userJourneyService.trackEventAsync(any(), any())
                systemActivityService.setOnboardingTestPixCreated(any())
                adService.publishOnboardingTestPixCreated(any())
            }
        }

        @Test
        fun `deve retornar erro se o pix já tiver sido criado e não estiver ACTIVE`() {
            every {
                systemActivityService.getOnboardingTestPix(ACCOUNT.accountId)
            } returns "CREATED"

            every { billRepository.findBill(BillId(BILL_ID), any()) } answers {
                createBillView(firstArg(), secondArg()).copy(status = BillStatus.PAID)
            }

            onboardingTestPixRepository.save(
                OnboardingTestPix(
                    accountId = ACCOUNT.accountId,
                    createdAt = getZonedDateTime(),
                    updatedAt = getZonedDateTime(),
                    remindedAt = getZonedDateTime(),
                    billIds =
                    listOf(
                        BillId(BILL_ID),
                    ),
                    status = OnboardingTestPixStatus.IN_PROGRESS,
                    billPaidCounter = 0,
                ),
            )

            val result = service.createChatbotExamplePayment(ACCOUNT.accountId, pixKey = PixKey(type = PixKeyType.PHONE, value = "***********"))

            result.isLeft() shouldBe true

            result.mapLeft {
                it shouldBe CreateOnboardingTestPixError.Conflict
            }

            verify(exactly = 0) {
                bankAccountService.transfer(any(), any(), any(), any())

                createBillService.createPix(any(), any())
                billEventPublisher.publish(any(), any<BillTagAdded>())

                userJourneyService.trackEventAsync(any(), any())
                systemActivityService.setOnboardingTestPixCreated(any())
                adService.publishOnboardingTestPixCreated(any())
            }
        }
    }

    @Nested
    @DisplayName("ao tentar sincronizar o estado do pix de teste em progresso")
    inner class SynchronizeAllTest {
        @Test
        fun `quando todos os pix estiverem pagos deve atualizar o estado para COMPLETED`() {
            val onboardingTestPix = setupOnboardingTestPix()

            every {
                billEventRepository.getBillById(any())
            } answers {
                paidBill(firstArg())
            }

            service.synchronizeAll()

            val otps = onboardingTestPixRepository.findByStatus(OnboardingTestPixStatus.COMPLETED)

            otps.size shouldBe 1
            with(otps.first()) {
                this.accountId shouldBe ACCOUNT.accountId
                this.billPaidCounter shouldBe 3
                this.createdAt shouldBe onboardingTestPix.createdAt
                this.updatedAt shouldBeAfter onboardingTestPix.updatedAt
            }

            verify {
                billEventPublisher wasNot Called
            }
        }

        @Test
        fun `quando o pix estiver agendado nao deve atualizar o vencimento`() {
            val onboardingTestPix = setupOnboardingTestPix()

            every {
                billEventRepository.getBillById(any())
            } answers {
                scheduledBill(firstArg())
            }

            service.synchronizeAll()

            val otps = onboardingTestPixRepository.findByStatus(OnboardingTestPixStatus.IN_PROGRESS)

            otps.size shouldBe 1
            with(otps.first()) {
                this.accountId shouldBe ACCOUNT.accountId
                this.billPaidCounter shouldBe 0
                this.createdAt shouldBe onboardingTestPix.createdAt
                this.updatedAt shouldBeAfter onboardingTestPix.updatedAt
            }

            verify {
                billEventPublisher wasNot Called
            }
        }

        @Test
        fun `quando nenhum pix estiver pago nem agendado deve atualizar o vencimento dos pix`() {
            val onboardingTestPix = setupOnboardingTestPix()

            every {
                billEventRepository.getBillById(any())
            } answers {
                activeBill(firstArg())
            }

            service.synchronizeAll()

            val otps = onboardingTestPixRepository.findByStatus(OnboardingTestPixStatus.IN_PROGRESS)

            otps.size shouldBe 1
            with(otps.first()) {
                this.accountId shouldBe ACCOUNT.accountId
                this.billPaidCounter shouldBe 0
                this.createdAt shouldBe onboardingTestPix.createdAt
                this.updatedAt shouldBeAfter onboardingTestPix.updatedAt
            }

            val billsSlot = mutableListOf<Bill>()
            val eventsSlot = mutableListOf<RegisterUpdated>()
            verify {
                billEventPublisher.publish(capture(billsSlot), capture(eventsSlot))
            }

            billsSlot.size shouldBe 3
            eventsSlot.size shouldBe 3
            eventsSlot.forEachIndexed { index, event ->
                event.billId shouldBe billsSlot[index].billId
                event.walletId shouldBe billsSlot[index].walletId
                val updatedRegisterData = event.updatedRegisterData
                updatedRegisterData.shouldBeTypeOf<UpdatedRegisterData.NewDueDate>()
                updatedRegisterData.dueDate shouldBe updatedRegisterData.effectiveDueDate
            }

            val today = getLocalDate()
            (eventsSlot[0].updatedRegisterData as UpdatedRegisterData.NewDueDate).dueDate shouldBe today
            (eventsSlot[1].updatedRegisterData as UpdatedRegisterData.NewDueDate).dueDate shouldBe today
            (eventsSlot[2].updatedRegisterData as UpdatedRegisterData.NewDueDate).dueDate shouldBe today.plusDays(1)
        }

        @Test
        fun `quando nem todos os pix estiverem pagos deve atualizar o contador de contas pagas`() {
            val onboardingTestPix = setupOnboardingTestPix()

            every {
                billEventRepository.getBillById(any())
            } answers {
                activeBill(firstArg())
            } andThenAnswer {
                scheduledBill(firstArg())
            } andThenAnswer {
                paidBill(firstArg())
            }

            service.synchronizeAll()

            val otps = onboardingTestPixRepository.findByStatus(OnboardingTestPixStatus.IN_PROGRESS)

            otps.size shouldBe 1
            with(otps.first()) {
                this.accountId shouldBe ACCOUNT.accountId
                this.billPaidCounter shouldBe 1
                this.createdAt shouldBe onboardingTestPix.createdAt
                this.updatedAt shouldBeAfter onboardingTestPix.updatedAt
            }

            val billSlot = slot<Bill>()
            val eventSlot = slot<RegisterUpdated>()
            verify {
                billEventPublisher.publish(capture(billSlot), capture(eventSlot))
            }

            with(eventSlot.captured) {
                billId shouldBe onboardingTestPix.billIds.first()
                billId shouldBe billSlot.captured.billId
                walletId shouldBe billSlot.captured.walletId
                with(updatedRegisterData) {
                    shouldBeTypeOf<UpdatedRegisterData.NewDueDate>()
                    dueDate shouldBe getLocalDate()
                    effectiveDueDate shouldBe dueDate
                }
            }
        }

        @Test
        fun `quando expirar o prazo de pagamento e nem todos os pix estiverem pagos deve atualizar o estado para EXPIRED e remover o pix nao pago`() {
            val onboardingTestPix =
                setupOnboardingTestPix(
                    createdAt = ZonedDateTime.of(2024, 5, 1, 12, 0, 0, 0, brazilTimeZone),
                )

            every {
                billEventRepository.getBillById(any())
            } answers {
                activeBill(firstArg())
            } andThenAnswer {
                paidBill(firstArg())
            }

            withGivenDateTime(ZonedDateTime.of(2024, 5, 7, 6, 0, 0, 0, brazilTimeZone)) {
                service.synchronizeAll()
            }

            val otps = onboardingTestPixRepository.findByStatus(OnboardingTestPixStatus.EXPIRED)

            otps.size shouldBe 1
            with(otps.first()) {
                this.accountId shouldBe ACCOUNT.accountId
                this.billPaidCounter shouldBe 2
                this.createdAt shouldBe onboardingTestPix.createdAt
                this.updatedAt shouldBeAfter onboardingTestPix.updatedAt
            }

            val billsSlot = mutableListOf<Bill>()
            val eventsSlot = mutableListOf<BillIgnored>()
            verify {
                billEventPublisher.publish(capture(billsSlot), capture(eventsSlot))
            }

            billsSlot.size shouldBe 1
            eventsSlot.size shouldBe 1
            eventsSlot.forEachIndexed { index, event ->
                event.billId shouldBe billsSlot[index].billId
                event.walletId shouldBe billsSlot[index].walletId
                event.removeFromListing shouldBe false
                event.removeFromRecurrence shouldBe false
            }

            verify {
                chatBotNotificationService.notifyTestPixReminder(any(), any(), any(), TestPixReminderType.EXPIRED)
            }
        }

        @Test
        fun `nao deve expirar enquanto houver um pix agendado`() {
            val onboardingTestPix =
                setupOnboardingTestPix(
                    createdAt = getZonedDateTime().minusDays(8),
                )

            every {
                billEventRepository.getBillById(any())
            } answers {
                scheduledBill(firstArg())
            } andThenAnswer {
                paidBill(firstArg())
            }

            service.synchronizeAll()

            val otps = onboardingTestPixRepository.findByStatus(OnboardingTestPixStatus.IN_PROGRESS)

            otps.size shouldBe 1
            with(otps.first()) {
                this.accountId shouldBe ACCOUNT.accountId
                this.billPaidCounter shouldBe 2
                this.createdAt shouldBe onboardingTestPix.createdAt
                this.updatedAt shouldBeAfter onboardingTestPix.updatedAt
            }

            verify {
                billEventPublisher wasNot Called
            }

            verify(exactly = 0) {
                notificationAdapter.notifyTriPixExpired(any())
            }
        }

        @Test
        fun `nao deve expirar enquanto houver todos os pixes estiverem pagos`() {
            val onboardingTestPix =
                setupOnboardingTestPix(
                    createdAt = getZonedDateTime().minusDays(8),
                )

            every {
                billEventRepository.getBillById(any())
            } answers {
                paidBill(firstArg())
            }

            service.synchronizeAll()

            val otps = onboardingTestPixRepository.findByStatus(OnboardingTestPixStatus.COMPLETED)

            otps.size shouldBe 1
            with(otps.first()) {
                this.accountId shouldBe ACCOUNT.accountId
                this.billPaidCounter shouldBe 3
                this.createdAt shouldBe onboardingTestPix.createdAt
                this.updatedAt shouldBeAfter onboardingTestPix.updatedAt
            }

            verify {
                billEventPublisher wasNot Called
            }

            verify(exactly = 0) {
                notificationAdapter.notifyTriPixExpired(any())
            }
        }

        @ParameterizedTest
        @ValueSource(strings = ["2024-05-02", "2024-05-03", "2024-05-04", "2024-05-05"])
        fun `deve notificar o usuário em algum dia seguinte (que não seja o último) da criação do 3-pix se ainda não utilizou e não notificou`(currentDate: String) {
            val onboardingTestPix =
                setupOnboardingTestPix(
                    createdAt = ZonedDateTime.of(2024, 5, 1, 0, 0, 0, 0, brazilTimeZone),
                )

            every {
                billEventRepository.getBillById(any())
            } answers {
                activeBill(firstArg())
            }

            withGivenDateTime(parseDateTextAsZonedDateTime(currentDate)) {
                service.synchronizeAll()
            }

            val otps = onboardingTestPixRepository.findByStatus(OnboardingTestPixStatus.IN_PROGRESS)

            otps.size shouldBe 1
            with(otps.first()) {
                this.accountId shouldBe ACCOUNT.accountId
                this.billPaidCounter shouldBe 0
                this.createdAt shouldBe onboardingTestPix.createdAt
                this.updatedAt shouldBeAfter onboardingTestPix.updatedAt
                this.remindedAt.shouldNotBeNull()
                this.remindedAt!! shouldBeAfter onboardingTestPix.createdAt
            }

            verify(exactly = 1) {
                chatBotNotificationService.notifyTestPixReminder(any(), any(), any(), TestPixReminderType.NEXT_DAY)
            }

            verify {
                billEventPublisher.publish(any(), any())
            }
        }

        @Test
        fun `deve notificar o usuário no último dia de validade do 3-pix se ainda não utilizou e já foi notificado antes`() {
            val onboardingTestPix =
                setupOnboardingTestPix(
                    createdAt = ZonedDateTime.of(2024, 5, 1, 0, 0, 0, 0, brazilTimeZone),
                    remindedAt = ZonedDateTime.of(2024, 5, 2, 0, 0, 0, 0, brazilTimeZone),
                )

            every {
                billEventRepository.getBillById(any())
            } answers {
                activeBill(firstArg())
            }

            withGivenDateTime(ZonedDateTime.of(2024, 5, 6, 0, 0, 0, 0, brazilTimeZone)) {
                service.synchronizeAll()
            }

            val otps = onboardingTestPixRepository.findByStatus(OnboardingTestPixStatus.IN_PROGRESS)

            otps.size shouldBe 1
            with(otps.first()) {
                this.accountId shouldBe ACCOUNT.accountId
                this.billPaidCounter shouldBe 0
                this.createdAt shouldBe onboardingTestPix.createdAt
                this.updatedAt shouldBeAfter onboardingTestPix.updatedAt
                this.remindedAt.shouldNotBeNull()
                this.remindedAt!! shouldBeAfter onboardingTestPix.createdAt
            }

            verify(exactly = 1) {
                chatBotNotificationService.notifyTestPixReminder(any(), any(), any(), TestPixReminderType.LAST_DAY)
            }

            verify {
                billEventPublisher.publish(any(), any())
            }
        }

        @Test
        fun `não deve notificar o usuário se a criação do tri pix foi no mesmo um dia`() {
            val dateTime = getZonedDateTime().withHour(0)
            val onboardingTestPix =
                setupOnboardingTestPix(
                    createdAt = dateTime,
                )

            every {
                billEventRepository.getBillById(any())
            } answers {
                activeBill(firstArg())
            }

            withGivenDateTime(dateTime.withHour(23)) {
                service.synchronizeAll()
            }

            val otps = onboardingTestPixRepository.findByStatus(OnboardingTestPixStatus.IN_PROGRESS)

            otps.size shouldBe 1
            with(otps.first()) {
                this.accountId shouldBe ACCOUNT.accountId
                this.billPaidCounter shouldBe 0
                this.createdAt shouldBe onboardingTestPix.createdAt
                this.updatedAt shouldBeAfter onboardingTestPix.updatedAt
                this.remindedAt.shouldBeNull()
            }

            verify(exactly = 0) {
                notificationAdapter.notifyTriPixNextDayReminder(any())
            }

            verify {
                billEventPublisher.publish(any(), any())
            }
        }

        @Test
        fun `não deve notificar o usuário no dia seguinte da criação do 3-pix se tiver pix agendado`() {
            val onboardingTestPix =
                setupOnboardingTestPix(
                    createdAt = getZonedDateTime().minusDays(1),
                )

            every {
                billEventRepository.getBillById(any())
            } answers {
                scheduledBill(firstArg())
            }

            service.synchronizeAll()

            val otps = onboardingTestPixRepository.findByStatus(OnboardingTestPixStatus.IN_PROGRESS)

            otps.size shouldBe 1
            with(otps.first()) {
                this.accountId shouldBe ACCOUNT.accountId
                this.billPaidCounter shouldBe 0
                this.createdAt shouldBe onboardingTestPix.createdAt
                this.updatedAt shouldBeAfter onboardingTestPix.updatedAt
                this.remindedAt.shouldBeNull()
            }

            verify(exactly = 0) {
                notificationAdapter.notifyTriPixNextDayReminder(any())
            }

            verify(exactly = 0) {
                billEventPublisher.publish(any(), any())
            }
        }

        @Test
        fun `não deve notificar o usuário que ainda não utilizou o 3-pix se ele ja foi notificado`() {
            val onboardingTestPix =
                setupOnboardingTestPix(
                    createdAt = getZonedDateTime().minusDays(1),
                    remindedAt = getZonedDateTime().minusDays(1),
                )

            every {
                billEventRepository.getBillById(any())
            } answers {
                activeBill(firstArg())
            }

            service.synchronizeAll()

            val otps = onboardingTestPixRepository.findByStatus(OnboardingTestPixStatus.IN_PROGRESS)

            otps.size shouldBe 1
            with(otps.first()) {
                this.accountId shouldBe ACCOUNT.accountId
                this.billPaidCounter shouldBe 0
                this.createdAt shouldBe onboardingTestPix.createdAt
                this.updatedAt shouldBeAfter onboardingTestPix.updatedAt
            }

            verify(exactly = 0) {
                notificationAdapter.notifyTriPixNextDayReminder(any())
            }

            verify {
                billEventPublisher.publish(any(), any())
            }
        }

        private fun setupOnboardingTestPix(
            createdAt: ZonedDateTime = getZonedDateTime(),
            remindedAt: ZonedDateTime? = null,
        ): OnboardingTestPix {
            val onboardingTestPix =
                OnboardingTestPix(
                    accountId = ACCOUNT.accountId,
                    createdAt = createdAt,
                    updatedAt = createdAt,
                    remindedAt = remindedAt,
                    billIds =
                    listOf(
                        BillId(BILL_ID),
                        BillId(BILL_ID_2),
                        BillId(BILL_ID_3),
                    ),
                    status = OnboardingTestPixStatus.IN_PROGRESS,
                    billPaidCounter = 0,
                )

            onboardingTestPixRepository.save(onboardingTestPix)

            return onboardingTestPix
        }

        private fun activeBill(id: BillId) =
            mockk<Bill>(relaxed = true) {
                every {
                    billId
                } returns id
                every {
                    walletId
                } returns WalletId("DUMMY")
                every {
                    status
                } returns BillStatus.ACTIVE
                every {
                    schedule
                } returns null
                every {
                    dueDate
                } returns getLocalDate().minusDays(10)
                every {
                    effectiveDueDate
                } returns getLocalDate().minusDays(10)

                every {
                    isOverdue()
                } returns true
            }.right()

        private fun scheduledBill(id: BillId) =
            mockk<Bill>(relaxed = true) {
                every {
                    billId
                } returns id
                every {
                    walletId
                } returns WalletId("DUMMY")
                every {
                    status
                } returns BillStatus.ACTIVE
                every {
                    schedule
                } returns mockk()
                every {
                    dueDate
                } returns getLocalDate().minusDays(10)
                every {
                    effectiveDueDate
                } returns getLocalDate().minusDays(10)
            }.right()

        private fun paidBill(id: BillId) =
            mockk<Bill>(relaxed = true) {
                every {
                    billId
                } returns id
                every {
                    walletId
                } returns WalletId("DUMMY")
                every {
                    status
                } returns BillStatus.PAID
            }.right()
    }

    @Nested
    @DisplayName("ao fazer o refund de um tripix para a friday")
    inner class RefundFridayMePoupe {
        @Test
        fun `deve transferir o amount para a conta de origem do tripix`() {
            every {
                systemActivityService.getOnboardingTestPix(wallet.founder.accountId)
            } returns "FAKE_SYSTEM_ACTIVITY"
            every { accountRepository.findAccountPaymentMethodByIdAndAccountId(any(), any()) } returns balance
            every { accountRepository.findByIdOrNull(any()) } returns walletFixture.founderAccount.copy(configuration = ACCOUNT.configuration.copy(groups = listOf(AccountGroup.ONBOARDING_TEST_PIX)))

            val result = service.refundFriday(wallet, 10L)

            result.isRight() shouldBe true

            verify {
                bankAccountService.transfer(
                    originAccountNo = (balance.method as InternalBankAccount).buildFullAccountNumber(),
                    targetAccountNo = defaultOriginAccountNo,
                    amount = 10L,
                    operationId = any(),
                )
                userJourneyService wasNot Called
                adService wasNot Called
            }

            verify(exactly = 0) {
                systemActivityService.setOnboardingTestPixCreated(any())
            }
        }
    }

    private fun createPixKeyDetailsResult(
        pixKey: PixKey,
        document: String,
    ) =
        PixKeyDetailsResult(
            pixKeyDetails =
            PixKeyDetails(
                key = pixKey,
                holder =
                PixKeyHolder(
                    accountNo = BigInteger("0"),
                    accountDv = "",
                    ispb = "",
                    institutionName = "",
                    accountType = AccountType.CHECKING,
                    routingNo = 0,
                ),
                owner = PixKeyOwner(name = "", document = document),
            ),
            e2e = "",
        )

    private fun createBillView(billId: BillId, walletId: WalletId) = BillView(
        billId = billId,
        walletId = walletId,
        dueDate = getLocalDate(),
        effectiveDueDate = getLocalDate(),
        billType = BillType.PIX,
        status = BillStatus.ACTIVE,
        paymentLimitTime = getZonedDateTime().toLocalTime(),
        source = ActionSource.System,
        createdOn = getZonedDateTime().toLocalDateTime(),
        subscriptionFee = false,
        tags = emptySet(),
        categoryId = null,
        categorySuggestions = emptyList(),
        automaticPixData = null,
        automaticPixAuthorizationMaximumAmount = null,
    )

    companion object {
        @JvmStatic
        fun pixKeyErrors() =
            listOf(
                Arguments.of(PixKeyError.MalformedKey),
                Arguments.of(PixKeyError.KeyNotConfirmed),
                Arguments.of(PixKeyError.KeyNotFound),
                Arguments.of(PixKeyError.UnknownError),
            )
    }
}