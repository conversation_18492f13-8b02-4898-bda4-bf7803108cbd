package ai.friday.billpayment.app

import ai.friday.billpayment.adapters.dynamodb.FinancialInstitutionGlobalDataDbRepository
import ai.friday.billpayment.app.integrations.BankDataService
import ai.friday.billpayment.withHolidays
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

class UpdateFinancialInstitutionGlobalDataTest {

    val repository = mockk<FinancialInstitutionGlobalDataDbRepository>()
    val service = mockk<BankDataService>()

    private val updateFinancialInstitutionGlobalData =
        UpdateFinancialInstitutionGlobalData(bankDataService = service, repository = repository)

    @Test
    fun `ao buscar a lista de feriados, deve salvar no banco sem sobrescrever a lista atual`() {
        val now = getLocalDate()
        val tomorrow = now.plusDays(1)

        val originalHolidays = listOf(now, tomorrow)

        every { service.getHolidays() } returns listOf(tomorrow)

        withHolidays(*originalHolidays.toTypedArray()) {
            updateFinancialInstitutionGlobalData.execute()

            verify {
                repository.saveHolidays(originalHolidays)
            }
        }
    }

    @Test
    fun `ao buscar a lista de feriados, deve salvar no banco sem sobrescrever a lista atual e adicionando os novos feriados`() {
        val now = getLocalDate()
        val tomorrow = now.plusDays(1)
        val nextWeek = now.plusDays(7)

        val originalHolidays = listOf(now, tomorrow)

        every { service.getHolidays() } returns listOf(now, nextWeek)

        withHolidays(*originalHolidays.toTypedArray()) {
            updateFinancialInstitutionGlobalData.execute()

            verify {
                repository.saveHolidays(listOf(now, tomorrow, nextWeek))
            }
        }
    }
}