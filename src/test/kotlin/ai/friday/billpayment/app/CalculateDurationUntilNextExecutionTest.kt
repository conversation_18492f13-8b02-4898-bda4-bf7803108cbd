package ai.friday.billpayment.app

import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.scheduling.cron.CronExpression
import java.time.Duration
import java.time.ZoneId
import java.time.ZonedDateTime
import org.junit.jupiter.api.Test

internal class CalculateDurationUntilNextExecutionTest {

    private val cronExpression = CronExpression.create("* * 8-21 * * *")

    @Test
    fun `deve retorar a duracao do intervalo quando estiver fora da janela considerando a tolerancia`() {
        withGivenDateTime(ZonedDateTime.of(2022, 8, 10, 7, 59, 58, 0, ZoneId.of("Brazil/East"))) {
            val result = cronExpression.calculateDurationUntilNextExecution(1)
            result shouldBe Duration.ofSeconds(2)
        }
    }

    @Test
    fun `deve retornar null quando estiver no limite inferior da janela`() {
        withGivenDateTime(ZonedDateTime.of(2022, 8, 10, 7, 59, 59, 0, ZoneId.of("Brazil/East"))) {
            val result = cronExpression.calculateDurationUntilNextExecution(1)
            result.shouldBeNull()
        }
    }

    @Test
    fun `deve retornar null quando estiver no limite superior da janela`() {
        withGivenDateTime(ZonedDateTime.of(2022, 8, 10, 21, 59, 58, 0, ZoneId.of("Brazil/East"))) {
            val result = cronExpression.calculateDurationUntilNextExecution(1)
            result.shouldBeNull()
        }
    }

    @Test
    fun `deve retornar a duracao quando estiver depois do horario da janela`() {
        withGivenDateTime(ZonedDateTime.of(2022, 8, 10, 21, 59, 59, 0, ZoneId.of("Brazil/East"))) {
            val result = cronExpression.calculateDurationUntilNextExecution(1)
            result shouldBe Duration.ofHours(10).plus(Duration.ofSeconds(1))
        }
    }
}