package ai.friday.billpayment.app.pix

import ai.friday.billpayment.app.onepixpay.GenericTransactionId
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.NAME
import io.kotest.matchers.shouldBe
import java.util.stream.Stream
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class PixQRCodeCreatorTest {

    private val pixQRCodeCreator = LocalPixQRCodeCreator()

    @ParameterizedTest
    @MethodSource("createQrPix")
    fun deveCriarChavesPixValidas(amount: Long, expected: String, message: String) {
        val generatedCode = pixQRCodeCreator.createQRCode(
            key = PixKey(value = "$<EMAIL>", type = PixKeyType.EMAIL),
            document = DOCUMENT,
            amount = amount,
            recipientName = NAME,
            transactionId = GenericTransactionId("FakeTid"),
            aditionalInfo = message,
        )

        generatedCode shouldBe expected
    }

    companion object {
        @JvmStatic
        fun createQrPix(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    1,
                    "00020126430014br.gov.bcb.pix012108845856704@friday.ai52040000530398654040.015802BR5915Fulano Beltrano6014RIO DE JANEIRO61082022029762110507FakeTid6304C638",
                    "",
                ),
                Arguments.arguments(
                    12,
                    "00020126430014br.gov.bcb.pix012108845856704@friday.ai52040000530398654040.125802BR5915Fulano Beltrano6014RIO DE JANEIRO61082022029762110507FakeTid63042227",
                    "",
                ),
                Arguments.arguments(
                    123,
                    "00020126430014br.gov.bcb.pix012108845856704@friday.ai52040000530398654041.235802BR5915Fulano Beltrano6014RIO DE JANEIRO61082022029762110507FakeTid63047E24",
                    "",
                ),
                Arguments.arguments(
                    1234,
                    "00020126430014br.gov.bcb.pix012108845856704@friday.ai520400005303986540512.345802BR5915Fulano Beltrano6014RIO DE JANEIRO61082022029762110507FakeTid630449BA",
                    "",
                ),
                Arguments.arguments(
                    12345,
                    "00020126430014br.gov.bcb.pix012108845856704@friday.ai5204000053039865406123.455802BR5915Fulano Beltrano6014RIO DE JANEIRO61082022029762110507FakeTid6304849F",
                    "",
                ),
                Arguments.arguments(
                    123456,
                    "00020126430014br.gov.bcb.pix012108845856704@friday.ai52040000530398654071234.565802BR5915Fulano Beltrano6014RIO DE JANEIRO61082022029762110507FakeTid630460C2",
                    "",
                ),
                Arguments.arguments(
                    1234567,
                    "00020126430014br.gov.bcb.pix012108845856704@friday.ai520400005303986540812345.675802BR5915Fulano Beltrano6014RIO DE JANEIRO61082022029762110507FakeTid6304894A",
                    "",
                ),
                Arguments.arguments(
                    12345678,
                    "00020126430014br.gov.bcb.pix012108845856704@friday.ai5204000053039865409123456.785802BR5915Fulano Beltrano6014RIO DE JANEIRO61082022029762110507FakeTid63042545",
                    "",
                ),
                Arguments.arguments(
                    100,
                    "00020126740014br.gov.bcb.pix012108845856704@friday.ai0227pagamento assinatura friday52040000530398654041.005802BR5915Fulano Beltrano6014RIO DE JANEIRO61082022029762110507FakeTid6304EF18",
                    "pagamento assinatura friday",
                ),
                Arguments.arguments(
                    100,
                    "00020126990014br.gov.bcb.pix012108845856704@friday.ai0252pagamento assinatura friday para teste de remoção da52040000530398654041.005802BR5915Fulano Beltrano6014RIO DE JANEIRO61082022029762110507FakeTid6304E866",
                    "pagamento assinatura friday para teste de remoção daQUI PRA FRENTE",
                ),
            )
        }
    }
}