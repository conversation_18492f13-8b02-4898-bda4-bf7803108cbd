package ai.friday.billpayment.app.pix

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.adapters.dynamodb.PixKeyDbRepository
import ai.friday.billpayment.adapters.dynamodb.PixKeyDynamoDAO
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.fingerprint.DeviceId
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.fixture.AccountPaymentMethodFixture
import ai.friday.billpayment.fixture.BalanceAccountPaymentMethodFixture
import ai.friday.billpayment.fixture.CreditCardAccountPaymentMethodFixture
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.WalletFixture
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class PixKeyServiceTest {

    private val dynamoDao = PixKeyDynamoDAO(setupDynamoDB())
    private val pixKeyRepository = spyk(PixKeyDbRepository(dynamoDao))

    private val creditCardAccountPaymentMethod = CreditCardAccountPaymentMethodFixture.create()
    private val virtualBankAccountPaymentMethod = BalanceAccountPaymentMethodFixture.create().copy(bankAccountMode = BankAccountMode.VIRTUAL)
    private val physicalBankAccountPaymentMethod = BalanceAccountPaymentMethodFixture.create()
    private val walletPaymentMethod = AccountPaymentMethodFixture.create(method = physicalBankAccountPaymentMethod)

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet(accountPaymentMethodId = walletPaymentMethod.id)

    private val walletService = mockk<WalletService> {
        every {
            findWallet(wallet.id)
        } returns wallet
    }

    private val accountId = wallet.founder.accountId
    private val pixKey = PixKey(DOCUMENT, PixKeyType.CPF)
    private val accountNumber = AccountNumber(physicalBankAccountPaymentMethod.buildFullAccountNumber())

    private val accountService = mockk<AccountService> {
        every {
            findAccountById(any())
        } returns walletFixture.founderAccount

        every {
            findAccountPaymentMethodsByAccountId(any(), any())
        } returns listOf(walletPaymentMethod)
    }

    private val pixKeyManagementAdapter = mockk<PixKeyManagement>() {
        every {
            registerKey(any(), any(), any(), any(), any())
        } returns PixKey(DOCUMENT, PixKeyType.CPF)
    }

    private val deviceFingerprintServiceMock = mockk<DeviceFingerprintService>() {
        every { withRealOrTemporaryDeviceId(any(), any(), ofType<(DeviceId) -> Unit>()) } answers {
            thirdArg<(DeviceId) -> Unit>().invoke(DeviceId("123"))
        }
    }

    private val service = PixKeyService(
        pixKeyRepository = pixKeyRepository,
        pixKeyManagementAdapter = pixKeyManagementAdapter,
        accountService = accountService,
        walletService = walletService,
        deviceFingerprintService = deviceFingerprintServiceMock,
    )

    @Nested
    @DisplayName("quando criar uma chave pix")
    inner class CreatePixKey {
        @Test
        fun `deve retornar erro se a chave pix já existir`() {
            pixKeyRepository.create(wallet.id, accountNumber, pixKey)

            val response = service.createPixKey(wallet.id, pixKey)

            response.isLeft() shouldBe true
            response.mapLeft {
                it.shouldBeInstanceOf<CreatePixKeyError.PixKeyAlreadyExists>()
            }
        }

        @Test
        fun `deve retornar erro se não achar a conta do usuário`() {
            every {
                accountService.findAccountPaymentMethodsByAccountId(walletFixture.founderAccount.accountId, any())
            } returns emptyList()

            val response = service.createPixKey(wallet.id, pixKey)

            response.isLeft() shouldBe true
            response.mapLeft {
                it.shouldBeInstanceOf<CreatePixKeyError.BankAccountNotFound>()
            }
        }

        @Test
        fun `deve retornar erro se o usuário tiver apenas contas bancarias virtuais`() {
            every {
                accountService.findAccountPaymentMethodsByAccountId(walletFixture.founderAccount.accountId, any())
            } returns listOf(AccountPaymentMethodFixture.create(method = virtualBankAccountPaymentMethod))

            val response = service.createPixKey(wallet.id, pixKey)

            response.isLeft() shouldBe true
            response.mapLeft {
                it.shouldBeInstanceOf<CreatePixKeyError.BankAccountNotFound>()
            }
        }

        @Test
        fun `deve retornar erro se o usuário tiver apenas método de pagamento com cartão`() {
            every {
                accountService.findAccountPaymentMethodsByAccountId(walletFixture.founderAccount.accountId, any())
            } returns listOf(AccountPaymentMethodFixture.create(method = creditCardAccountPaymentMethod))

            val response = service.createPixKey(wallet.id, pixKey)

            response.isLeft() shouldBe true
            response.mapLeft {
                it.shouldBeInstanceOf<CreatePixKeyError.BankAccountNotFound>()
            }
        }

        @Test
        fun `deve retornar erro se não achar o usuário`() {
            every {
                accountService.findAccountById(walletFixture.founderAccount.accountId)
            } throws AccountNotFoundException("1234")

            val response = service.createPixKey(wallet.id, pixKey)

            response.isLeft() shouldBe true
            response.mapLeft {
                it.shouldBeInstanceOf<CreatePixKeyError.AccountNotFound>()
            }
        }

        @Test
        fun `não deve criar o registro caso a chamada pro arbi não funcione`() {
            every {
                pixKeyManagementAdapter.registerKey(any(), any(), any(), any(), any())
            } throws Exception("error")

            val response = service.createPixKey(wallet.id, pixKey)

            response.isLeft() shouldBe true
            response.mapLeft {
                it.shouldBeInstanceOf<CreatePixKeyError.ServerError>()
            }
        }

        @Test
        fun `deve criar o registro de chave pix`() {
            every { deviceFingerprintServiceMock.getOrNull(accountId) } returns mockk {
                every { deviceIds } returns mapOf(accountNumber to mockk())
            }

            val response = service.createPixKey(wallet.id, pixKey)

            response.isRight() shouldBe true

            val slot = slot<AccountNumber>()
            verify {
                pixKeyRepository.create(any(), capture(slot), any())
            }
            slot.captured.fullAccountNumber shouldBe physicalBankAccountPaymentMethod.buildFullAccountNumber()
        }

        @Test
        fun `deve criar o registro de chave pix EVP`() {
            val slot = slot<PixKey>()
            every {
                pixKeyManagementAdapter.registerKey(any(), capture(slot), any(), any(), any())
            } returns PixKey("evp", PixKeyType.EVP)

            every { deviceFingerprintServiceMock.getOrNull(accountId) } returns mockk {
                every { deviceIds } returns mapOf(accountNumber to mockk())
            }

            val response = service.createPixKey(wallet.id, PixKey("", PixKeyType.EVP))

            response.isRight() shouldBe true

            with(slot.captured) {
                type shouldBe PixKeyType.EVP
                value shouldBe ""
            }

            val pixKeys = pixKeyRepository.list(wallet.id)

            pixKeys.size shouldBe 1
            with(pixKeys.single()) {
                type shouldBe PixKeyType.EVP
                value shouldBe "evp"
            }

            val accountNumberSlot = slot<AccountNumber>()
            verify {
                pixKeyRepository.create(any(), capture(accountNumberSlot), any())
            }
            accountNumberSlot.captured.fullAccountNumber shouldBe physicalBankAccountPaymentMethod.buildFullAccountNumber()
        }
    }

    @Nested
    @DisplayName("quando apagar uma chave pix")
    inner class DeletePixKey {
        @BeforeEach
        fun beforeEach() {
            pixKeyRepository.create(wallet.id, accountNumber, pixKey)
        }

        @Test
        fun `deve retornar erro se não encontrar a chave pix`() {
            val response = service.deletePixKey(wallet.id, PixKey("123", PixKeyType.CPF))

            response.isLeft() shouldBe true
            response.mapLeft {
                it.shouldBeInstanceOf<DeletePixKeyError.PixKeyNotFound>()
            }
        }

        @Test
        fun `não deve apagar a chave caso a chamada para o provedor externo de erro`() {
            every {
                pixKeyManagementAdapter.deleteKey(any(), any(), any())
            } throws Exception("error")

            val response = service.deletePixKey(wallet.id, pixKey)

            response.isLeft() shouldBe true
            response.mapLeft {
                it.shouldBeInstanceOf<DeletePixKeyError.ServerError>()
            }
        }

        @Test
        fun `não deve retornar na listagem de chaves do usuário`() {
            every {
                pixKeyManagementAdapter.deleteKey(any(), any(), any())
            } returns Unit.right()

            service.deletePixKey(wallet.id, pixKey)

            val response = pixKeyRepository.list(wallet.id)

            response.size shouldBe 0
        }
    }
}