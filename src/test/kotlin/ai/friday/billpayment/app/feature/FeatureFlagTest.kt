package ai.friday.billpayment.app.feature

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class FeatureFlagTest {
    private val accountId = AccountId("TEST-ACCOUNT-ID")

    @Test
    fun `flag habilitada para todos`() {
        testFlag(enabled = FeatureFlagStatus.EVERYONE) shouldBe true
    }

    @Test
    fun `flag desabilitada`() {
        testFlag(enabled = FeatureFlagStatus.NO_ONE) shouldBe false
    }

    @Test
    fun `usuário na whitelist`() {
        testFlag(
            enabled = FeatureFlagStatus.SOME,
            whitelist = listOf("OTHER-ACCOUNT-ID", accountId.value),
        ) shouldBe true
    }

    @Test
    fun `usuário com grupo na lista`() {
        testFlag(
            enabled = FeatureFlagStatus.SOME,
            groups = listOf("alpha"),
            accountGroup = listOf(AccountGroup.ALPHA, AccountGroup.BETA),
        ) shouldBe true
    }

    @Test
    fun `usuário fora da whitelist`() {
        testFlag(
            enabled = FeatureFlagStatus.SOME,
            whitelist = listOf("OTHER-ACCOUNT-ID"),
        ) shouldBe false
    }

    @Test
    fun `usuário sem grupo na lista`() {
        testFlag(
            enabled = FeatureFlagStatus.SOME,
            groups = listOf("alpha"),
            accountGroup = listOf(AccountGroup.BETA),
        ) shouldBe false
    }

    private fun testFlag(accountGroup: List<AccountGroup> = emptyList(), enabled: FeatureFlagStatus, groups: List<String>? = null, whitelist: List<String>? = null): Boolean {
        val account = ACCOUNT.copy(accountId = accountId, configuration = ACCOUNT.configuration.copy(groups = accountGroup))

        val flag = FeatureFlag("test-flag")
        flag.enabled = enabled
        flag.groups = groups
        flag.whitelist = whitelist

        return flag.check(account)
    }
}