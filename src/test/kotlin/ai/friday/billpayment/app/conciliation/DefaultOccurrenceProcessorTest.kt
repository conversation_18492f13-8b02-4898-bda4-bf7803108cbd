package ai.friday.billpayment.app.conciliation

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.conciliation.DefaultOccurrenceProcessorTest.Declarations.configuration
import ai.friday.billpayment.app.conciliation.DefaultOccurrenceProcessorTest.Declarations.lock
import ai.friday.billpayment.app.conciliation.DefaultOccurrenceProcessorTest.Declarations.locker
import ai.friday.billpayment.app.conciliation.DefaultOccurrenceProcessorTest.Declarations.messagePublisher
import ai.friday.billpayment.app.conciliation.DefaultOccurrenceProcessorTest.Declarations.transactionRepository
import ai.friday.billpayment.app.conciliation.DefaultOccurrenceProcessorTest.Factory.NSU
import ai.friday.billpayment.app.conciliation.DefaultOccurrenceProcessorTest.Factory.occurrence
import ai.friday.billpayment.app.conciliation.DefaultOccurrenceProcessorTest.Factory.processor
import ai.friday.billpayment.app.conciliation.DefaultOccurrenceProcessorTest.Factory.transaction
import ai.friday.billpayment.app.conciliation.DefaultOccurrenceProcessorTest.Factory.wallet
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.OccurrenceProcessorResult
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.integrations.UndoCaptureFundsCommand
import ai.friday.billpayment.app.payment.BalanceAuthorization
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.app.payment.ErrorSource
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionError
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.createBoletoSettlementResult
import ai.friday.billpayment.createTransaction
import ai.friday.billpayment.fichaCompensacaoCreditCardAdded
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.transactionId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.shouldBe
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verifyOrder
import net.javacrumbs.shedlock.core.SimpleLock
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class DefaultOccurrenceProcessorTest {

    @BeforeEach
    fun beforeEach() = Setup.beforeEach()

    @Nested
    @DisplayName("WhenReceiveBoletoOccurrence")
    inner class WhenReceiveBoletoOccurrence {
        @Test
        fun `should result in error when transaction was not found`() {
            val nsu = 1010101010101L
            every { transactionRepository.findTransactionIdByWalletAndNSU(wallet.id, nsu) } returns null
            processor.process(occurrence.copy(nsu = "$nsu")) shouldBe OccurrenceProcessorResult.TransactionNotfound
        }

        @Test
        fun `should result in success when transaction exists and could be handled`() {
            val transaction = transaction().also {
                it.getErrorSource() shouldBe ErrorSource.UNKNOWN // default value
                it.getErrorDescription() shouldBe TransactionError.GENERIC_EXCEPTION.description // default result
            }

            every { transactionRepository.findTransactionIdByWalletAndNSU(wallet.id, NSU) } returns transactionId
            every { transactionRepository.findById(transactionId) } returns transaction

            processor.process(occurrence.copy(nsu = "$NSU")) shouldBe OccurrenceProcessorResult.Success

            verifyOrder {
                transactionRepository.findTransactionIdByWalletAndNSU(any(), any())
                transactionRepository.findById(any())

                messagePublisher.sendMessage(
                    configuration.automation.queue,
                    UndoCaptureFundsCommand(
                        transactionId = transactionId,
                        source = UndoCaptureFundsCommand.Source(
                            provider = FinancialServiceGateway.CELCOIN,
                            reason = UndoCaptureFundsCommand.Reason.SETTLEMENT_REFUND,
                            details = occurrence.reason,
                        ),
                    ),
                )

                transactionRepository.save(
                    withArg {
                        it.id shouldBe transaction.id
                        it.walletId shouldBe transaction.walletId
                        it.getErrorSource() shouldBe ErrorSource.SETTLEMENT_CELCOIN
                        it.getErrorDescription() shouldBe BoletoOccurrence.DEFAULT_DESCRIPTION
                    },
                )
            }
        }
    }

    private companion object Setup {
        fun beforeEach() {
            clearAllMocks()
            every { locker.acquireLock(any(), null, null) } returns lock
        }
    }

    private object Declarations {
        val transactionRepository = mockk<TransactionRepository>(relaxed = true)
        val locker = mockk<InternalLock>()
        val lock = mockk<SimpleLock>(relaxed = true)

        val messagePublisher = mockk<MessagePublisher>(relaxed = true)

        val configuration = OccurrencesConfiguration(
            pastRangeInDays = 0,
            automation = OccurrencesConfiguration.Automation(enabled = true, queue = ""),
        )
    }

    private object Factory {
        const val BILL_ID = "BILL-test"
        const val NSU = 777L

        val walletFixture = WalletFixture(founderAccountId = ACCOUNT.accountId)

        val wallet = walletFixture.buildWallet()

        val billAdded = fichaCompensacaoCreditCardAdded.copy(walletId = wallet.id, billId = BillId(BILL_ID))
        val bill = Bill.build(billAdded)

        fun transaction(id: TransactionId? = transactionId): Transaction = createTransaction(
            transactionId = id,
            status = TransactionStatus.COMPLETED,
            boletoSettlementStatus = BoletoSettlementStatus.CONFIRMED,
            settlementData = SettlementData(
                bill,
                0,
                bill.amountTotal,
                createBoletoSettlementResult(BoletoSettlementStatus.CONFIRMED),
            ),
            walletId = wallet.id,
            paymentOperation = BalanceAuthorization(
                status = BankOperationStatus.SUCCESS,
                amount = bill.amountTotal,
                paymentGateway = FinancialServiceGateway.ARBI,
            ),
            nsu = NSU,
        )

        val processor = DefaultOccurrenceProcessor(
            configuration = configuration,
            messagePublisher = messagePublisher,
            transactionRepository = transactionRepository,
        )

        val occurrence = BoletoOccurrence(
            walletId = wallet.id,
            nsu = "$NSU",
            date = getZonedDateTime(),
            createDate = getZonedDateTime(),
            reason = "reason",
            externalTransactionId = "456",
            barCode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
            amount = 100,
            financialServiceGateway = FinancialServiceGateway.CELCOIN,
        )
    }
}