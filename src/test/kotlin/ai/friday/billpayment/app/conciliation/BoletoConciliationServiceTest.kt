package ai.friday.billpayment.app.conciliation

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.dynamodb.BoletoOccurrenceDbRepository
import ai.friday.billpayment.adapters.dynamodb.BoletoOccurrenceDynamoDAO
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.integrations.BoletoOccurrenceAdapter
import ai.friday.billpayment.app.integrations.BoletoOccurrenceProcessor
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import ai.friday.billpayment.integration.LocalDbCreationRule.Companion.dynamoDB
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.mockk.Called
import io.mockk.Runs
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.spyk
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class BoletoConciliationServiceTest {

    @BeforeEach
    fun setup() = beforeEach()

    @Test
    fun `quando nao encontra nenhuma ocorrencia nao deve alarmar`() {
        every { boletoConciliationAdapter.getOccurrences(any(), any()) } returns emptyList()

        boletoConciliationService.checkOccurrences()

        verify(exactly = 0) {
            BoletoConciliationService.logNewOccurrence(any())
        }
    }

    @Test
    fun `quando encontra uma ocorrencia nova deve alarmar`() {
        every { boletoConciliationAdapter.getOccurrences(any(), any()) } returns listOf(boletoOccurrence)

        boletoConciliationService.checkOccurrences()

        verify(exactly = 1) {
            BoletoConciliationService.logNewOccurrence(boletoOccurrence)
        }
    }

    @Test
    fun `quando encontra uma ocorrencia deve despachar ou enfileirar se a automatizacao estiver ativa`() {
        every { boletoConciliationAdapter.getOccurrences(any(), any()) } returns listOf(boletoOccurrence)

        boletoConciliationService.checkOccurrences()

        verifySequence {
            BoletoConciliationService.logNewOccurrence(boletoOccurrence)
            processor.process(boletoOccurrence)
            boletoConciliationRepository.save(boletoOccurrence)
        }
    }

    @Test
    fun `quando encontra uma ocorrencia nao deve enfileirar se a automatizacao estiver inativa`() {
        every { boletoConciliationAdapter.getOccurrences(any(), any()) } returns listOf(boletoOccurrence)

        BoletoConciliationService(
            occurrenceAdapter = boletoConciliationAdapter,
            occurrenceRepository = boletoConciliationRepository,
            configuration = occurrencesConfiguration.copy(
                automation = occurrencesConfiguration.automation.copy(enabled = false),
            ),
            processor = processor,
        ).checkOccurrences()

        verify { processor wasNot Called }
    }

    @Test
    fun `quando encontra uma ocorrencia ja existente nao deve alarmar`() {
        every { boletoConciliationAdapter.getOccurrences(any(), any()) } returns listOf(boletoOccurrence)

        boletoConciliationRepository.save(boletoOccurrence)

        boletoConciliationService.checkOccurrences()

        verify(exactly = 0) {
            BoletoConciliationService.logNewOccurrence(any())
        }
    }

    private companion object Setup {
        val dynamoDbEnhancedClient = getDynamoDB()

        val processor = mockk<BoletoOccurrenceProcessor>(relaxed = true)
        val boletoConciliationAdapter: BoletoOccurrenceAdapter = mockk()
        val boletoConciliationRepository = BoletoOccurrenceDbRepository(
            client = BoletoOccurrenceDynamoDAO(dynamoDbEnhancedClient),
        )

        val occurrencesConfiguration = OccurrencesConfiguration(
            pastRangeInDays = 7,
            automation = OccurrencesConfiguration.Automation(enabled = true, queue = "foobar"),
        )

        val boletoConciliationService = spyk(
            BoletoConciliationService(
                occurrenceAdapter = boletoConciliationAdapter,
                occurrenceRepository = boletoConciliationRepository,
                configuration = occurrencesConfiguration,
                processor = processor,
            ),
        )

        val boletoOccurrence = BoletoOccurrence(
            walletId = WalletId("WALLET-ID"),
            nsu = "123",
            date = getZonedDateTime(),
            createDate = getZonedDateTime(),
            reason = "reason",
            externalTransactionId = "456",
            barCode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
            amount = 100,
            financialServiceGateway = FinancialServiceGateway.CELCOIN,
        )

        fun beforeEach() {
            clearAllMocks()
            createBillPaymentTable(dynamoDB)
            mockkObject(BoletoConciliationService)
            every { BoletoConciliationService.logNewOccurrence(any()) } just Runs
        }
    }
}