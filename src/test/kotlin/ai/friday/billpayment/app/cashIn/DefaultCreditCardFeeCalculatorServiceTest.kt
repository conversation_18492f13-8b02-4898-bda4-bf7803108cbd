package ai.friday.billpayment.app.cashIn

import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.fee.FeePaymentCreditCardInstallment
import ai.friday.billpayment.app.integrations.AccountRepository
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import java.util.stream.Stream
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class DefaultCreditCardFeeCalculatorServiceTest {

    private val accountRepository = mockk<AccountRepository>()
    private val creditCardsInstallmentsFeeConfiguration = mockk<CreditCardsInstallmentsFeeConfiguration>() {
        every { fees } returns mapOf(
            1 to 0.5,
            2 to 1.0,
            3 to 1.0,
            4 to 1.0,
            5 to 1.0,
            6 to 1.0,
            7 to 1.0,
            8 to 1.0,
            9 to 1.0,
            10 to 1.0,
            11 to 1.0,
            12 to 1.0,
        )
    }

    private val service = DefaultCreditCardFeeCalculatorService(accountRepository, creditCardsInstallmentsFeeConfiguration = creditCardsInstallmentsFeeConfiguration, useInternalCreditCardCalculator = true)

    @ParameterizedTest
    @MethodSource("installmentFeePairs")
    fun `deve calcular a taxa corretamente`(installmentFee: Pair<Int, Long>) {
        val accountMock = mockk<Account>()

        every { accountMock.hasCreditCardEnabled() } returns true
        every { accountRepository.findById(any()) } returns accountMock

        val result = service.calculateFeeAmount(
            accountId = AccountId("ACCOUNT_ID"),
            netAmount = 100000,
            installments = installmentFee.first,
        )

        result.isRight().shouldBeTrue()

        result.map { it shouldBe installmentFee.second }
    }

    @ParameterizedTest
    @MethodSource("installments")
    fun `deve calcular as parcelas corretamente`(installment: FeePaymentCreditCardInstallment) {
        val accountMock = mockk<Account>()

        every { accountMock.hasCreditCardEnabled() } returns true
        every { accountRepository.findById(any()) } returns accountMock

        val result = service.calculateInstallment(
            accountId = AccountId("ACCOUNT_ID"),
            netAmount = 100000,
            installments = installment.quantity,
        )

        result.isRight().shouldBeTrue()

        result.map { it shouldBe installment }
    }

    @Test
    fun `se não estiver usando a calculadora interna, deve dar erro ao tentar calcular as parcelas`() {
        val externalService = DefaultCreditCardFeeCalculatorService(accountRepository, creditCardsInstallmentsFeeConfiguration, useInternalCreditCardCalculator = false)

        val result = externalService.calculateInstallment(
            accountId = AccountId("ACCOUNT_ID"),
            netAmount = 100000,
            installments = 1,
        )

        result.isLeft().shouldBeTrue()
    }

    companion object {
        @JvmStatic
        fun installmentFeePairs(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(Pair<Int, Long>(1, 500)), // Taxa é diferente para uma parcela
                Arguments.arguments(Pair<Int, Long>(2, 1502)),
                Arguments.arguments(Pair<Int, Long>(3, 2007)),
                Arguments.arguments(Pair<Int, Long>(4, 2512)),
                Arguments.arguments(Pair<Int, Long>(5, 3020)),
                Arguments.arguments(Pair<Int, Long>(6, 3529)),
                Arguments.arguments(Pair<Int, Long>(7, 4040)),
                Arguments.arguments(Pair<Int, Long>(8, 4552)),
                Arguments.arguments(Pair<Int, Long>(9, 5066)),
                Arguments.arguments(Pair<Int, Long>(10, 5582)),
                Arguments.arguments(Pair<Int, Long>(11, 6099)),
                Arguments.arguments(Pair<Int, Long>(12, 6619)),
            )
        }

        @JvmStatic
        fun installments(): Stream<Arguments> {
            return listOf(
                FeePaymentCreditCardInstallment(
                    amount = 100500, // Taxa é diferente para uma parcela
                    total = 100500,
                    quantity = 1,
                    fee = 0.5,
                ),
                FeePaymentCreditCardInstallment(
                    amount = 50751,
                    total = 101502,
                    quantity = 2,
                    fee = 1.0,
                ),
                FeePaymentCreditCardInstallment(
                    amount = 34002,
                    total = 102007,
                    quantity = 3,
                    fee = 1.0,
                ),
                FeePaymentCreditCardInstallment(
                    amount = 25628,
                    total = 102512,
                    quantity = 4,
                    fee = 1.0,
                ),
                FeePaymentCreditCardInstallment(
                    amount = 20604,
                    total = 103020,
                    quantity = 5,
                    fee = 1.0,
                ),
                FeePaymentCreditCardInstallment(
                    amount = 17255,
                    total = 103529,
                    quantity = 6,
                    fee = 1.0,
                ),
                FeePaymentCreditCardInstallment(
                    amount = 14863,
                    total = 104040,
                    quantity = 7,
                    fee = 1.0,
                ),
                FeePaymentCreditCardInstallment(
                    amount = 13069,
                    total = 104552,
                    quantity = 8,
                    fee = 1.0,
                ),
                FeePaymentCreditCardInstallment(
                    amount = 11674,
                    total = 105066,
                    quantity = 9,
                    fee = 1.0,
                ),
                FeePaymentCreditCardInstallment(
                    amount = 10558,
                    total = 105582,
                    quantity = 10,
                    fee = 1.0,
                ),
                FeePaymentCreditCardInstallment(
                    amount = 9645,
                    total = 106099,
                    quantity = 11,
                    fee = 1.0,
                ),
                FeePaymentCreditCardInstallment(
                    amount = 8885,
                    total = 106619,
                    quantity = 12,
                    fee = 1.0,
                ),
            ).map { Arguments.of(it) }.stream()
        }
    }
}