package ai.friday.billpayment.app.cashIn

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.BankOperationExecuted
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.cashIn.instrumentation.CashInInstrumentationEvent
import ai.friday.billpayment.app.cashIn.instrumentation.DepositReceivedType
import ai.friday.billpayment.app.integrations.CashInAmountTier
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.creditCardCashInTransaction
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class CreditCardCashInInstrumentationEventTest {
    private val walletFixture = WalletFixture()
    private val wallet =
        walletFixture.buildWallet(otherMembers = listOf(walletFixture.participant, walletFixture.cantPayParticipant))

    private val deposit = BankOperationExecuted(
        accountId = wallet.founder.accountId,
        accountPaymentMethodId = wallet.paymentMethodId,
        date = getZonedDateTime().format(dateFormat),
        created = 0,
        flow = BankStatementItemFlow.CREDIT,
        type = BankStatementItemType.PIX,
        description = "",
        operationNumber = "000001",
        amount = 1000,
        counterpartName = wallet.founder.name,
        counterpartDocument = wallet.founder.document,
        documentNumber = "0000200",
    )

    @Nested
    @DisplayName("when a non-deposit cash-in is provided")
    inner class NonDepositCashIn {
        private val nonDepositBankOperation = deposit.copy(
            type = BankStatementItemType.DEVOLUCAO_TED,
            counterpartDocument = wallet.founder.document,
            documentNumber = wallet.founder.document,
        )

        @Test
        fun `should not be create an deposit event`() {
            assertThrows<IllegalArgumentException> {
                CashInInstrumentationEvent.DepositReceived.create(nonDepositBankOperation, wallet.founder.document)
            }
        }
    }

    @Nested
    @DisplayName("when a Pix or Ted cash-in deposit is provided")
    inner class PixOrTedCashIn {
        private val pixDepositFromMyself = deposit.copy(
            type = BankStatementItemType.PIX,
            counterpartDocument = wallet.founder.document,
            documentNumber = wallet.founder.document,
        )

        private val pixDepositFromSomeoneElse = deposit.copy(
            type = BankStatementItemType.PIX,
            counterpartDocument = wallet.founder.document,
            documentNumber = "123",
        )

        private val tedDepositFromSomeoneElse = deposit.copy(
            type = BankStatementItemType.TED_DIF_TITULARIDADE,
            counterpartDocument = wallet.founder.document,
            documentNumber = "123",
        )

        private val tedDepositFromMyself = deposit.copy(
            type = BankStatementItemType.TED_DIF_TITULARIDADE,
            counterpartDocument = wallet.founder.document,
            documentNumber = "123",
        )

        @Test
        fun `should create an event with the correct type`() {
            with(
                CashInInstrumentationEvent.DepositReceived.create(
                    pixDepositFromSomeoneElse,
                    wallet.founder.document,
                ),
            ) {
                type shouldBe DepositReceivedType.PIX
            }

            with(CashInInstrumentationEvent.DepositReceived.create(tedDepositFromMyself, wallet.founder.document)) {
                type shouldBe DepositReceivedType.TED
            }
        }

        @Test
        fun `should create an event with the correct receiver account id`() {
            with(CashInInstrumentationEvent.DepositReceived.create(pixDepositFromMyself, wallet.founder.document)) {
                receiverAccountId shouldBe pixDepositFromMyself.accountId
            }

            with(
                CashInInstrumentationEvent.DepositReceived.create(
                    pixDepositFromSomeoneElse,
                    wallet.founder.document,
                ),
            ) {
                receiverAccountId shouldBe pixDepositFromSomeoneElse.accountId
            }

            with(CashInInstrumentationEvent.DepositReceived.create(tedDepositFromMyself, wallet.founder.document)) {
                receiverAccountId shouldBe tedDepositFromMyself.accountId
            }

            with(
                CashInInstrumentationEvent.DepositReceived.create(
                    tedDepositFromSomeoneElse,
                    wallet.founder.document,
                ),
            ) {
                receiverAccountId shouldBe tedDepositFromSomeoneElse.accountId
            }
        }

        @Test
        fun `should create an event correctly identifying if the receiver is the sender`() {
            with(CashInInstrumentationEvent.DepositReceived.create(pixDepositFromMyself, wallet.founder.document)) {
                sameOwnership shouldBe true
            }

            with(CashInInstrumentationEvent.DepositReceived.create(tedDepositFromMyself, wallet.founder.document)) {
                sameOwnership shouldBe true
            }

            with(
                CashInInstrumentationEvent.DepositReceived.create(
                    pixDepositFromSomeoneElse,
                    DOCUMENT,
                ),
            ) {
                sameOwnership shouldBe false
            }

            with(
                CashInInstrumentationEvent.DepositReceived.create(
                    tedDepositFromSomeoneElse,
                    DOCUMENT,
                ),
            ) {
                sameOwnership shouldBe false
            }
        }

        @Test
        fun `should create an event with the correct tier`() {
            with(
                CashInInstrumentationEvent.DepositReceived.create(
                    pixDepositFromMyself.copy(amount = 499_99),
                    wallet.founder.document,
                ),
            ) {
                tier shouldBe CashInAmountTier.BELOW_FIVE_HUNDRED
            }

            with(
                CashInInstrumentationEvent.DepositReceived.create(
                    pixDepositFromMyself.copy(amount = 500_00),
                    wallet.founder.document,
                ),
            ) {
                tier shouldBe CashInAmountTier.ABOVE_FIVE_HUNDRED
            }
        }
    }

    @Nested
    @DisplayName("when a credit card cash-in is provided")
    inner class CreditCardCashIn {
        private val transactionFromMyself =
            creditCardCashInTransaction.copy(payer = creditCardCashInTransaction.payer.copy(accountId = creditCardCashInTransaction.paymentData.toSingle().accountPaymentMethod.accountId))
        private val transactionFromSomeoneElse =
            creditCardCashInTransaction.copy(payer = creditCardCashInTransaction.payer.copy(accountId = AccountId("someone-else")))

        @Test
        fun `should create an event with the correct receiver account id`() {
            with(CashInInstrumentationEvent.CreditCardCashInReceived.create(transactionFromMyself)) {
                val target = transactionFromMyself.settlementData.getTarget<ai.friday.billpayment.app.cashIn.CreditCardCashIn>()
                receiverAccountId shouldBe target.bankAccount.accountId
            }

            with(CashInInstrumentationEvent.CreditCardCashInReceived.create(transactionFromSomeoneElse)) {
                val target = transactionFromSomeoneElse.settlementData.getTarget<ai.friday.billpayment.app.cashIn.CreditCardCashIn>()
                receiverAccountId shouldBe target.bankAccount.accountId
            }
        }

        @Test
        fun `should create an event correctly identifying if the receiver is the sender`() {
            with(CashInInstrumentationEvent.CreditCardCashInReceived.create(transactionFromMyself)) {
                sameOwnership shouldBe true
            }

            with(CashInInstrumentationEvent.CreditCardCashInReceived.create(transactionFromSomeoneElse)) {
                sameOwnership shouldBe false
            }
        }

        @Test
        fun `should create an event with the correct tier`() {
            with(
                CashInInstrumentationEvent.CreditCardCashInReceived.create(
                    transactionFromMyself.copy(
                        settlementData = transactionFromMyself.settlementData.copy(totalAmount = 499_99),
                    ),
                ),
            ) {
                tier shouldBe CashInAmountTier.BELOW_FIVE_HUNDRED
            }

            with(
                CashInInstrumentationEvent.CreditCardCashInReceived.create(
                    transactionFromMyself.copy(
                        settlementData = transactionFromMyself.settlementData.copy(totalAmount = 500_00),
                    ),
                ),
            ) {
                tier shouldBe CashInAmountTier.ABOVE_FIVE_HUNDRED
            }

            with(
                CashInInstrumentationEvent.CreditCardCashInReceived.create(
                    transactionFromMyself.copy(
                        settlementData = transactionFromMyself.settlementData.copy(totalAmount = 5000_00),
                    ),
                ),
            ) {
                tier shouldBe CashInAmountTier.ABOVE_FIVE_HUNDRED
            }
        }

        @Test
        fun `should create an event with the correct succeeded property`() {
            val failedTransactionFromMyself = transactionFromMyself.copy(status = TransactionStatus.FAILED)

            with(
                CashInInstrumentationEvent.CreditCardCashInReceived.create(failedTransactionFromMyself),
            ) {
                succeeded shouldBe false
            }

            val succeededTransactionFromMyself = transactionFromMyself.copy(status = TransactionStatus.COMPLETED)

            with(
                CashInInstrumentationEvent.CreditCardCashInReceived.create(succeededTransactionFromMyself),
            ) {
                succeeded shouldBe true
            }
        }

        @Test
        fun `should fail if transaction status is not supported`() {
            val processingTransactionFromMyself = transactionFromMyself.copy(status = TransactionStatus.PROCESSING)

            assertThrows<IllegalArgumentException> {
                CashInInstrumentationEvent.CreditCardCashInReceived.create(processingTransactionFromMyself)
            }

            val undoneTransactionFromMyself = transactionFromMyself.copy(status = TransactionStatus.UNDONE)

            assertThrows<IllegalArgumentException> {
                CashInInstrumentationEvent.CreditCardCashInReceived.create(undoneTransactionFromMyself)
            }
        }
    }
}