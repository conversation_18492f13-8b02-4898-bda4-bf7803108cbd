package ai.friday.billpayment.app.cashIn

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.instrumentation.CashInInstrumentationRepository
import ai.friday.billpayment.app.banking.BankOperationExecuted
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.cashIn.instrumentation.CashInInstrumentationEvent
import ai.friday.billpayment.app.cashIn.instrumentation.DefaultCashInInstrumentationService
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.CashInInstrumentationService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.creditCardCashInTransaction
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow

class CashInInstrumentationServiceTest {
    private val instrumentationRepository: CashInInstrumentationRepository = mockk(relaxUnitFun = true)
    private val accountRepository: AccountRepository = mockk(relaxUnitFun = true) {
        every {
            findById(any())
        } returns ACCOUNT
    }

    private val cashInInstrumentationService: CashInInstrumentationService =
        DefaultCashInInstrumentationService(
            instrumentationRepository = instrumentationRepository,
            accountRepository = accountRepository,
        )

    private val walletFixture = WalletFixture()
    private val wallet =
        walletFixture.buildWallet(otherMembers = listOf(walletFixture.participant, walletFixture.cantPayParticipant))

    private val deposit = BankOperationExecuted(
        accountId = wallet.founder.accountId,
        accountPaymentMethodId = wallet.paymentMethodId,
        date = getZonedDateTime().format(dateFormat),
        created = 0,
        flow = BankStatementItemFlow.CREDIT,
        type = BankStatementItemType.PIX,
        description = "",
        operationNumber = "000001",
        amount = 1000,
        counterpartName = wallet.founder.name,
        counterpartDocument = wallet.founder.document,
        documentNumber = "0000200",
    )

    @Nested
    @DisplayName("when a non-deposit cash-in is made")
    inner class NonDepositCashIn {
        private val nonDepositBankOperation = deposit.copy(
            type = BankStatementItemType.DEVOLUCAO_TED,
            counterpartDocument = wallet.founder.document,
            documentNumber = wallet.founder.document,
        )

        @Test
        fun `should not send an event`() {
            assertDoesNotThrow {
                cashInInstrumentationService.depositReceived(nonDepositBankOperation)
            }

            verify(exactly = 0) {
                instrumentationRepository.publishEvent(any())
            }
        }
    }

    @Nested
    @DisplayName("when cash-in deposit is a Pix")
    inner class PixCashIn {
        private val pixDeposit = deposit.copy(
            type = BankStatementItemType.PIX,
            counterpartDocument = wallet.founder.document,
            documentNumber = wallet.founder.document,
        )

        @Test
        fun `should send event of deposit received`() {
            cashInInstrumentationService.depositReceived(pixDeposit)

            verify(exactly = 1) {
                instrumentationRepository.publishEvent(ofType<CashInInstrumentationEvent.DepositReceived>())
            }
        }

        @Test
        fun `should not throw if repository fails`() {
            every {
                instrumentationRepository.publishEvent(any())
            } throws NoStackTraceException()

            assertDoesNotThrow {
                cashInInstrumentationService.depositReceived(pixDeposit)
            }
        }
    }

    @Nested
    @DisplayName("when cash-in depoist is a Ted")
    inner class TedCashIn {
        private val tedDepositFromSomeoneElse = deposit.copy(
            type = BankStatementItemType.TED_DIF_TITULARIDADE,
            counterpartDocument = wallet.founder.document,
            documentNumber = "***********",
        )

        private val tedDepositFromMyself = deposit.copy(
            type = BankStatementItemType.TED_DIF_TITULARIDADE,
            counterpartDocument = wallet.founder.document,
            documentNumber = wallet.founder.document,
        )

        @Test
        fun `should send event of deposit received`() {
            cashInInstrumentationService.depositReceived(tedDepositFromMyself)
            cashInInstrumentationService.depositReceived(tedDepositFromSomeoneElse)

            verify(exactly = 2) {
                instrumentationRepository.publishEvent(ofType<CashInInstrumentationEvent.DepositReceived>())
            }
        }

        @Test
        fun `should not throw if repository fails`() {
            every {
                instrumentationRepository.publishEvent(any())
            } throws NoStackTraceException()

            assertDoesNotThrow {
                cashInInstrumentationService.depositReceived(tedDepositFromSomeoneElse)
                cashInInstrumentationService.depositReceived(tedDepositFromMyself)
            }
        }
    }

    @Nested
    @DisplayName("when cash-in deposit is made with credit card")
    inner class CreditCardCashIn {
        private val failedTransaction = creditCardCashInTransaction.copy(status = TransactionStatus.FAILED)

        @Test
        fun `should send event of deposit received`() {
            cashInInstrumentationService.creditCardSucceeded(creditCardCashInTransaction)

            val slot = slot<CashInInstrumentationEvent.CreditCardCashInReceived>()

            verify(exactly = 1) {
                instrumentationRepository.publishEvent(capture(slot))
            }

            with(slot.captured) {
                succeeded shouldBe true
            }
        }

        @Test
        fun `should send event when it failed`() {
            cashInInstrumentationService.creditCardFailed(failedTransaction)

            val slot = slot<CashInInstrumentationEvent.CreditCardCashInReceived>()

            verify(exactly = 1) {
                instrumentationRepository.publishEvent(capture(slot))
            }

            with(slot.captured) {
                succeeded shouldBe false
            }
        }

        @Test
        fun `should not throw if repository fails`() {
            every {
                instrumentationRepository.publishEvent(any())
            } throws NoStackTraceException()

            assertDoesNotThrow {
                cashInInstrumentationService.creditCardSucceeded(creditCardCashInTransaction)
                cashInInstrumentationService.creditCardFailed(failedTransaction)
            }
        }
    }
}