package ai.friday.billpayment.app.fingerprint

import ai.friday.billpayment.app.liveness.LivenessId
import java.time.ZonedDateTime
import java.util.UUID

object DeviceFingerprintFixture {
    val anActiveDevice = AnActiveDevice
}

object AnActiveDevice {
    const val HEADER = "eyJ1dWlkIjoiMTgxY2JkOGQtNWU5OC00OTNmLTg2MTAtZjM0M2M5ZTE3OTY3IiwiYWxpYXMiOiJpUGhvbmUiLCJmaW5nZXJwcmludCI6InVzLWVhc3QtMV9kMzVmNDdkOS1mYTZiLTQ2NzUtYjQzMC03N2JmMjllOTMyOTIiLCJzY3JlZW5SZXNvbHV0aW9uIjp7IndpZHRoIjozNzUsImhlaWdodCI6ODEyfSwidHlwZSI6IklPUyIsImZyZXNoIjpmYWxzZSwiZHBpIjozLCJtYW51ZmFjdHVyZXIiOiJBcHBsZSIsIm1vZGVsIjoiaVBob25lIDEyIG1pbmkiLCJyb290ZWQiOmZhbHNlLCJzdG9yYWdlQ2FwYWNpdHkiOjEyNDg3NDAwNCwib3NJZCI6IjczQTVBNEU0LUZEMzEtNDFDQS04Q0U3LTlBNTBENDgyMzA1QiJ9"
    val registeredDevice = RegisteredDevice(
        creationDate = ZonedDateTime.now(),
        details = MobileDeviceDetails(
            uuid = UUID.fromString("181cbd8d-5e98-493f-8610-f343c9e17967"),
            alias = "iPhone",
            screenResolution = DeviceScreenResolution(width = 375, height = 812),
            type = DeviceType.IOS,
            fresh = false,
            dpi = 3.0,
            manufacturer = "Apple",
            model = "iPhone 12 mini",
            rooted = false,
            storageCapacity = 124874004,
            osId = "73A5A4E4-FD31-41CA-8CE7-9A50D482305B",
            fingerprint = "us-east-1_d35f47d9-fa6b-4675-b430-77bf29e93292",
        ),
        deviceIds = emptyMap(),
        fingerprint = DeviceFingerprint("us-east-1_d35f47d9-fa6b-4675-b430-77bf29e93292"),
        status = DeviceStatus.ACTIVE,
        liveness = DeviceLiveness(LivenessId("liveness-id-1"), false),
    )
}