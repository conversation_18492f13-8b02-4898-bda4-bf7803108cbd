package ai.friday.billpayment.app.fingerprint

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.DeviceFingerprintDbRepository
import ai.friday.billpayment.adapters.dynamodb.DeviceFingerprintDynamoDAO
import ai.friday.billpayment.anotherBalance
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.liveness.LivenessService
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletType
import ai.friday.billpayment.balance
import ai.friday.billpayment.fixture.BalanceAccountPaymentMethodFixture
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.DOCUMENT_2
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.paymentMethodId
import ai.friday.billpayment.paymentMethodId2
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.inspectors.forOne
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.util.Base64
import java.util.UUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

private val deviceFingerprint = DeviceFingerprint("cognito-device-key")

private val uuid = UUID.fromString("0f80e143-bf76-4de8-a0de-0a6f6b3cf43c")

private val deviceFingerprintPayloadRaw = "{\"uuid\":\"$uuid\", \"fresh\":false,\"alias\":\"Galaxy A02s\"," +
    "\"dpi\":1.75,\"fingerprint\":\"${deviceFingerprint.value}\",\"rooted\":false,\"manufacturer\":\"samsung\",\"model\":\"SM-A025M\", \"screenResolution\":{\"width\":1920,\"height\":1080}," +
    "\"storageCapacity\":*********,\"type\":\"ANDROID\",\"osId\":\"7525e01ebc112d7e\"}"

private val deviceFingerprintPayloadWihtoutFingerprintRaw = "{\"uuid\":\"$uuid\", \"fresh\":false,\"alias\":\"Galaxy A02s\"," +
    "\"dpi\":1.75,\"rooted\":false,\"manufacturer\":\"samsung\",\"model\":\"SM-A025M\", \"screenResolution\":{\"width\":1920,\"height\":1080}," +
    "\"storageCapacity\":*********,\"type\":\"ANDROID\",\"osId\":\"7525e01ebc112d7e\"}"

private val deviceFingerprintPayload = Base64.getEncoder().encodeToString(deviceFingerprintPayloadRaw.toByteArray())
private val deviceFingerprintPayloadWithoutFingerprint = Base64.getEncoder().encodeToString(deviceFingerprintPayloadWihtoutFingerprintRaw.toByteArray())

private val deviceId = DeviceId("dfsapoksdpo12312323123")
private val anotherDeviceId = DeviceId("dssdofks919439i4")

private val accountId = ACCOUNT.accountId

private val deviceDetails = MobileDeviceDetails(
    alias = "Galaxy A02s",
    fingerprint = deviceFingerprint.value,
    uuid = uuid,
    screenResolution = DeviceScreenResolution(1920, 1080),
    type = DeviceType.ANDROID,
    fresh = false,
    dpi = 1.75,
    manufacturer = "samsung",
    model = "SM-A025M",
    rooted = false,
    storageCapacity = *********,
    osId = "7525e01ebc112d7e",
)

private val accountNumber = AccountNumber((balance.method as InternalBankAccount).buildFullAccountNumber())
private val anotherAccountNumber = AccountNumber((anotherBalance.method as InternalBankAccount).buildFullAccountNumber())

private val registeredDevice = RegisteredDevice(
    details = deviceDetails,
    status = DeviceStatus.ACTIVE,
    creationDate = getZonedDateTime(),
    deviceIds = mapOf(accountNumber to deviceId, anotherAccountNumber to anotherDeviceId),
    fingerprint = deviceFingerprint,
    liveness = null,
)

class DeviceFingerprintServiceTest {
    private val adapter = mockk<DeviceFingerprintAdapter>()

    private val livenessService = mockk<LivenessService>()

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val repository = DeviceFingerprintDbRepository(DeviceFingerprintDynamoDAO(dynamoDbEnhancedClient))

    private val accountService = mockk<AccountService>()

    private val mockedEventPublisher = mockk<EventPublisher>()

    private val mockedWalletRepository = mockk<WalletRepository>()

    private val walletFixture = WalletFixture()

    private val lockProvider = mockk<InternalLock> {
        every { acquireLock(any()) } returns mockk(relaxed = true)
    }

    private val mockedSystemActivityService = mockk<SystemActivityService>()

    private val service = DeviceFingerprintService(
        adapter = adapter,
        repository = repository,
        accountService = accountService,
        livenessService = livenessService,
        eventPublisher = mockedEventPublisher,
        walletRepository = mockedWalletRepository,
        lockProvider = lockProvider,
        systemActivityService = mockedSystemActivityService,
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Nested
    @DisplayName("quando for validar um fingerprint")
    inner class ValidateFingerprint {
        @Test
        fun `quando o payload não contém o fingerprint deve retornar não é válido`() {
            service.isValidFingerprint(
                deviceFingerprintPayload = deviceFingerprintPayloadWithoutFingerprint,
                accountId = accountId,
            ).shouldBeFalse()
        }

        @Test
        fun `quando o fingerprint não existe deve retornar que não é valido`() {
            service.isValidFingerprint(
                deviceFingerprintPayload = deviceFingerprintPayload,
                accountId = accountId,
            ).shouldBeFalse()
        }

        @Test
        fun `deve comparar apenas o fingerprint sem olhar para outros dados do device details`() {
            repository.save(
                device = registeredDevice.copy(details = deviceDetails.copy(storageCapacity = 0)),
                accountId = accountId,
            )

            service.isValidFingerprint(
                deviceFingerprintPayload = deviceFingerprintPayload,
                accountId = accountId,
            ).shouldBeTrue()
        }

        @Test
        fun `deve devolver erro se o fingerprint estiver incorreto`() {
            repository.save(
                device = registeredDevice.copy(details = deviceDetails.copy(fingerprint = "another-fingerprint")),
                accountId = accountId,
            )

            service.isValidFingerprint(
                deviceFingerprintPayload = deviceFingerprintPayload,
                accountId = accountId,
            ).shouldBeFalse()
        }

        @ParameterizedTest
        @EnumSource(
            value = DeviceStatus::class,
            mode = EnumSource.Mode.EXCLUDE,
            names = ["ACTIVE"],
        )
        fun `quando o fingerprint estiver desabilitado, deve retorner que não é válido`(status: DeviceStatus) {
            repository.save(
                device = registeredDevice.copy(status = status),
                accountId = accountId,
            )

            service.isValidFingerprint(
                deviceFingerprintPayload = deviceFingerprintPayload,
                accountId = accountId,
            ).shouldBeFalse()
        }

        @Test
        fun `quando o fingerprint existe e todas as informações são iguais deve retornar que é valido`() {
            repository.save(
                device = registeredDevice,
                accountId = accountId,
            )

            service.isValidFingerprint(
                deviceFingerprintPayload = deviceFingerprintPayload,
                accountId = accountId,
            ).shouldBeTrue()
        }
    }

    @Nested
    @DisplayName("quando for registrar devices")
    inner class RegisterDevices {
        @Test
        fun `deve filtrar contas virtuais e não registrar devices para elas`() {
            val accountId = walletFixture.founder.accountId
            val virtualPaymentMethod = AccountPaymentMethod(
                id = paymentMethodId,
                accountId = accountId,
                method = BalanceAccountPaymentMethodFixture.create().copy(bankAccountMode = BankAccountMode.VIRTUAL),
                status = AccountPaymentMethodStatus.ACTIVE,
                created = getZonedDateTime(),
            )

            repository.save(device = registeredDevice.copy(deviceIds = emptyMap()), accountId = accountId)

            every { mockedWalletRepository.findWallets(accountId, MemberStatus.ACTIVE) } returns listOf(
                walletFixture.buildWallet(
                    walletFounder = walletFixture.founder,
                    accountPaymentMethodId = paymentMethodId,
                ),
            )
            every { accountService.findAccountPaymentMethodByIdAndAccountId(paymentMethodId, accountId) } returns virtualPaymentMethod

            val result = service.registerDevices(accountId, deviceFingerprint)

            result shouldBe RegisterDevicesResult.Success
            verify(exactly = 0) { adapter.createDevice(any()) }
        }
    }

    @Test
    fun `deve adicionar um device para cara wallet que o usuário participa`() {
        val firstAccountId = walletFixture.founder.accountId

        val secondAccountId = walletFixture.assistant.accountId

        val firstPaymentMethod = AccountPaymentMethod(
            id = paymentMethodId,
            accountId = firstAccountId,
            method = BalanceAccountPaymentMethodFixture.create(),
            status = AccountPaymentMethodStatus.ACTIVE,
            created = getZonedDateTime(),
        )

        val secondPaymentMethod = AccountPaymentMethod(
            id = paymentMethodId2,
            accountId = secondAccountId,
            method = BalanceAccountPaymentMethodFixture.create().copy(accountNo = 1111),
            status = AccountPaymentMethodStatus.ACTIVE,
            created = getZonedDateTime(),
        )

        repository.save(device = registeredDevice.copy(deviceIds = emptyMap()), accountId = firstAccountId)

        val slot = mutableListOf<CreateDeviceRequest>()

        every { adapter.createDevice(capture(slot)) } returns deviceId andThen anotherDeviceId

        every { accountService.findAccountPaymentMethodByIdAndAccountId(paymentMethodId, firstAccountId) } returns firstPaymentMethod

        every { accountService.findAccountPaymentMethodByIdAndAccountId(paymentMethodId2, secondAccountId) } returns secondPaymentMethod

        every { accountService.findAccountById(firstAccountId) } returns ACCOUNT
        every { accountService.findAccountById(secondAccountId) } returns ACCOUNT.copy(accountId = AccountId(ACCOUNT_ID_2), document = DOCUMENT_2)

        every { mockedWalletRepository.findWallets(firstAccountId, any()) } returns listOf(
            walletFixture.buildWallet(
                walletFounder = walletFixture.founder,
                accountPaymentMethodId = paymentMethodId,
            ),
            walletFixture.buildWallet(
                walletFounder = walletFixture.assistant.copy(type = MemberType.FOUNDER),
                otherMembers = listOf(walletFixture.founder.copy(type = MemberType.ASSISTANT)),
                type = WalletType.SECONDARY,
                accountPaymentMethodId = paymentMethodId2,
            ),
        )

        service.registerDevices(
            accountId = firstAccountId,
            deviceFingerprint = deviceFingerprint,
        ).shouldBe(RegisterDevicesResult.Success)

        verify(exactly = 1) {
            accountService.findAccountById(firstAccountId)
            accountService.findAccountById(secondAccountId)
        }

        slot.forOne { it.person.document.value shouldBe DOCUMENT }
        slot.forOne { it.person.document.value shouldBe DOCUMENT_2 }

        with(repository.getByAccountId(firstAccountId).first()) {
            deviceIds shouldBe mapOf(
                AccountNumber((firstPaymentMethod.method as InternalBankAccount).buildFullAccountNumber()) to deviceId,
                AccountNumber((secondPaymentMethod.method as InternalBankAccount).buildFullAccountNumber()) to anotherDeviceId,
            )
        }
    }

    @Nested
    @DisplayName("quando for remover um deviceId")
    inner class RemoveDeviceId {

        @BeforeEach
        fun setup() {
            repository.save(registeredDevice, accountId)
            every { adapter.removeDeviceId(deviceId) } returns true
        }

        @Test
        fun `quando o deviceId existir e a chamada no adapter retornar true, deve retornar sucesso`() {
            repository.getDeviceOrNull(deviceFingerprint, accountId)
                .let { it!!.deviceIds.size shouldBe 2 }

            service.removeDeviceId(accountId, accountNumber) shouldBe RemoveDeviceIdResult.Success

            repository.getDeviceOrNull(deviceFingerprint, accountId)
                .let { it!!.deviceIds.size shouldBe 1 }
        }

        @Test
        fun `quando o deviceId não existir deve retornar DeviceIdNotFound`() {
            service.removeDeviceId(accountId, AccountNumber("*********")) shouldBe RemoveDeviceIdResult.DeviceIdNotFound
        }

        @Test
        fun `quando o Device não existir deve retornar RegisteredDeviceNotFound`() {
            service.removeDeviceId(AccountId("*********1"), accountNumber) shouldBe RemoveDeviceIdResult.RegisteredDeviceNotFound
        }

        @Test
        fun `quando o deviceId existir mas o adapter retornar falso deve retornar DeviceIdNotRemoved`() {
            every { adapter.removeDeviceId(deviceId) } returns false

            service.removeDeviceId(accountId, accountNumber) shouldBe RemoveDeviceIdResult.DeviceIdNotRemoved
            val device = repository.getDeviceOrNull(deviceFingerprint, accountId)
            device!!.deviceIds.size shouldBe 2
        }
    }
}