package ai.friday.billpayment.app.banking

import ai.friday.billpayment.adapters.arbi.ArbiAdapterException
import ai.friday.billpayment.app.FindError
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.balance.DefaultBalanceService
import ai.friday.billpayment.app.integrations.BankAccountService
import ai.friday.billpayment.app.integrations.InternalBankRepository
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.PixPaymentService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.bankStatementItem
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.omnibusBankAccountConfiguration
import ai.friday.billpayment.paymentMethodId
import ai.friday.billpayment.virtualBalance
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.model.ResourceNotFoundException
import io.kotest.matchers.shouldBe
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import io.mockk.verifyOrder
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class InternalBankServiceTest {

    private val bankAccountService: BankAccountService = mockk()
    private val pixPaymentService: PixPaymentService = mockk() {
        every {
            getStatement(any())
        } returns emptyList()
    }
    private val walletService: WalletService = mockk()
    private val messagePublisher: MessagePublisher = mockk(relaxUnitFun = true)
    private val internalBankRepository: InternalBankRepository = mockk(relaxUnitFun = true) {
        every {
            findAllBankStatementItem(any(), any(), any())
        } returns BankStatement(items = listOf())
    }

    private val accountService: AccountService = mockk {
        every {
            findAllVirtualBankAccount()
        } returns listOf(virtualBalance)
    }

    private val balanceService = spyk(
        DefaultBalanceService(
            accountService,
            internalBankRepository,
            bankAccountService,
            omnibusBankAccountConfiguration,
            walletService,
        ),
    )
    private val internalBankService = spyk(
        InternalBankService(
            accountService = accountService,
            bankAccountService = bankAccountService,
            pixPaymentService = pixPaymentService,
            internalBankRepository = internalBankRepository,
            messagePublisher = messagePublisher,
            omnibusBankAccountConfiguration = omnibusBankAccountConfiguration,
            lockProvider = mockk(),
            balanceService = balanceService,
            notificationAdapter = mockk(),
            walletService = mockk(),
        ),
    )

    private val accountId = AccountId(ACCOUNT_ID)
    private val accountNo = AccountNumber("12")
    private val document = ""
    private val date = getLocalDate()
    private val operationNumber = "1000001"
    private val operationNumber2 = "1000002"
    private val item = DefaultBankStatementItem(
        date = date,
        operationNumber = operationNumber,
        isTemporaryOperationNumber = false,
        documentNumber = document,
        counterpartName = "",
        counterpartDocument = document,
        description = "",
        flow = BankStatementItemFlow.CREDIT,
        type = BankStatementItemType.TED_MESMA_TITULARIDADE,
        amount = 100L,
    )

    private val item2 = DefaultBankStatementItem(
        date = date,
        operationNumber = operationNumber2,
        isTemporaryOperationNumber = false,
        documentNumber = document,
        counterpartName = "",
        counterpartDocument = document,
        description = "",
        flow = BankStatementItemFlow.CREDIT,
        type = BankStatementItemType.TED_MESMA_TITULARIDADE,
        amount = 200L,
    )

    private val item3 = DefaultBankStatementItem(
        date = date,
        operationNumber = operationNumber,
        isTemporaryOperationNumber = false,
        documentNumber = document,
        counterpartName = "",
        counterpartDocument = document,
        description = "",
        flow = BankStatementItemFlow.DEBIT,
        type = BankStatementItemType.TED_MESMA_TITULARIDADE,
        amount = 100L,
    )

    @BeforeEach
    fun setup() {
        internalBankService.bankAccountDepositQueueName = "test"
    }

    @Test
    fun `should throw exception on get statement error`() {
        every {
            bankAccountService.getStatement(accountNo, document, any(), any())
        } throws ArbiAdapterException()
        assertThrows(SynchronizeBankAccountException::class.java) {
            internalBankService.synchronizeBankAccount(
                accountPaymentMethodId = paymentMethodId,
                accountNumber = accountNo,
                document = document,
                accountId = accountId,
                withPixFallBack = false,
            )
        }
    }

    @Test
    fun `should do nothing on empty get statement`() {
        every { bankAccountService.getStatement(accountNo, document, any(), any()) } returns BankStatement(listOf())
        val result = internalBankService.synchronizeBankAccount(
            accountPaymentMethodId = paymentMethodId,
            accountNumber = accountNo,
            document = document,
            accountId = accountId,
            withPixFallBack = false,
        )

        result shouldBe false

        verify(exactly = 0) {
            internalBankRepository.create(any<InternalBankStatementItem>())
            messagePublisher.sendMessage(any())
        }
    }

    @Test
    fun `should do nothing on get statement with already registered items`() {
        every { bankAccountService.getStatement(accountNo, document, any(), any()) } returns BankStatement(listOf(item))
        every {
            internalBankRepository.findBankStatementItem(
                paymentMethodId,
                operationNumber,
            )
        } returns item.right()
        internalBankService.synchronizeBankAccount(
            accountPaymentMethodId = paymentMethodId,
            accountNumber = accountNo,
            document = document,
            accountId = accountId,
            withPixFallBack = false,
        )

        verify {
            internalBankRepository.findBankStatementItem(paymentMethodId, operationNumber)
        }
        coVerify(exactly = 0) {
            internalBankRepository.create(any<InternalBankStatementItem>())
            messagePublisher.sendMessage(any())
        }
    }

    @Test
    fun `should save item on get statement with new items`() {
        every { bankAccountService.getStatement(accountNo, document, any(), any()) } returns BankStatement(listOf(item))
        every {
            internalBankRepository.findBankStatementItem(
                paymentMethodId,
                operationNumber,
            )
        } returns FindError.NotFound.left()

        val result = internalBankService.synchronizeBankAccount(
            accountPaymentMethodId = paymentMethodId,
            accountNumber = accountNo,
            document = document,
            accountId = accountId,
            withPixFallBack = false,
        )

        result shouldBe true

        verify {
            internalBankRepository.findBankStatementItem(paymentMethodId, operationNumber)
            internalBankRepository.create(any<InternalBankStatementItem>())
            messagePublisher.sendMessage(any())
        }
    }

    @Test
    fun `deve alarmar quando houver registros com mesmo operationNumber e paymentMethodId`() {
        val divergentItem = item.copy(date = date.minusDays(1))

        every { bankAccountService.getStatement(accountNo, document, any(), any()) } returns BankStatement(listOf(item))

        every {
            internalBankRepository.findAllBankStatementItem(paymentMethodId, any(), any())
        } returns BankStatement(listOf(item, divergentItem))

        every {
            internalBankRepository.findBankStatementItem(paymentMethodId, operationNumber)
        } returns Either.Right(item)

        internalBankService.synchronizeBankAccount(
            accountPaymentMethodId = paymentMethodId,
            accountNumber = accountNo,
            document = document,
            accountId = accountId,
            withPixFallBack = false,
        )

        verify {
            internalBankRepository.findAllBankStatementItem(paymentMethodId, any(), any())
            internalBankService.logStatementDivergence(
                paymentMethodId,
                operationNumber,
                "Statement duplicado no extrato interno.",
            )
        }
    }

    @Test
    fun `deve alarmar quando houver itens que não possuem referencia no extrato externo`() {
        val divergentItem = item.copy(operationNumber = "12345")

        every { bankAccountService.getStatement(accountNo, document, any(), any()) } returns BankStatement(listOf(item))

        every {
            internalBankRepository.findAllBankStatementItem(paymentMethodId, any(), any())
        } returns BankStatement(listOf(divergentItem))

        every {
            internalBankRepository.findBankStatementItem(paymentMethodId, operationNumber)
        } returns Either.Right(item)

        internalBankService.synchronizeBankAccount(
            accountPaymentMethodId = paymentMethodId,
            accountNumber = accountNo,
            document = document,
            accountId = accountId,
            withPixFallBack = false,
        )

        verify {
            internalBankRepository.findAllBankStatementItem(paymentMethodId, any(), any())
            internalBankService.logStatementDivergence(
                paymentMethodId,
                divergentItem.operationNumber,
                "Statement interno não encontrado no extrato externo.",
            )
        }
    }

    @Test
    fun `should save new item on get statement with new and old items`() {
        every { bankAccountService.getStatement(accountNo, document, any(), any()) } returns BankStatement(
            listOf(
                item,
                item2,
            ),
        )
        every {
            internalBankRepository.findBankStatementItem(
                paymentMethodId,
                operationNumber,
            )
        } returns item.right()
        every {
            internalBankRepository.findBankStatementItem(
                paymentMethodId,
                operationNumber2,
            )
        } returns FindError.NotFound.left()
        internalBankService.synchronizeBankAccount(
            accountPaymentMethodId = paymentMethodId,
            accountNumber = accountNo,
            document = document,
            accountId = accountId,
            withPixFallBack = false,
        )

        verify {
            internalBankRepository.findBankStatementItem(paymentMethodId, operationNumber)
            internalBankRepository.findBankStatementItem(paymentMethodId, operationNumber2)
            internalBankRepository.create(InternalBankStatementItem(item2, paymentMethodId))
        }
        verifyOrder {
            balanceService.invalidate(paymentMethodId)
            messagePublisher.sendMessage(any())
        }
        verify(exactly = 0) {
            internalBankRepository.create(InternalBankStatementItem(item, paymentMethodId))
        }
    }

    @Test
    fun `should throw exception on error saving new item`() {
        every { bankAccountService.getStatement(accountNo, document, any(), any()) } returns BankStatement(listOf(item))
        every {
            internalBankRepository.findBankStatementItem(
                paymentMethodId,
                operationNumber,
            )
        } returns FindError.NotFound.left()
        every {
            internalBankRepository.create(any<InternalBankStatementItem>())
        } throws ResourceNotFoundException("")
        assertThrows(SynchronizeBankAccountException::class.java) {
            internalBankService.synchronizeBankAccount(
                accountPaymentMethodId = paymentMethodId,
                accountNumber = accountNo,
                document = document,
                accountId = accountId,
                withPixFallBack = false,
            )
        }
    }

    @Test
    fun `should synchronize omnibus bank account on get statement with new items`() {
        val accountPaymentMethodId = AccountPaymentMethodId(omnibusBankAccountConfiguration.accountPaymentMethodId)

        every {
            bankAccountService.getStatement(
                AccountNumber(omnibusBankAccountConfiguration.accountNo),
                omnibusBankAccountConfiguration.document,
                any(),
                any(),
            )
        } returns BankStatement(listOf(item))
        every {
            internalBankRepository.findBankStatementItem(
                accountPaymentMethodId,
                operationNumber,
            )
        } returns FindError.NotFound.left()
        every {
            internalBankRepository.findBankStatementItem(
                virtualBalance.id,
                operationNumber,
            )
        } returns FindError.NotFound.left()
        every { accountService.findVirtualBankAccountByDocument(item.counterpartDocument) } returns Either.Right(
            virtualBalance,
        )

        val result = internalBankService.synchronizeBankAccount(
            bankNo = omnibusBankAccountConfiguration.bankNo,
            routingNo = omnibusBankAccountConfiguration.routingNo,
            accountNumber = AccountNumber(omnibusBankAccountConfiguration.accountNo),
        )

        result shouldBe true

        verify {
            internalBankRepository.findBankStatementItem(accountPaymentMethodId, operationNumber)
            internalBankRepository.save(any<OmnibusBankStatementItem>())
            internalBankRepository.create(any<InternalBankStatementItem>())
        }

        verifyOrder {
            balanceService.invalidateCache(virtualBalance.id)
            messagePublisher.sendMessage(any())
        }
    }

    @Test
    fun `should not save bankStatementItem when omnibus and virtual balance already has it`() {
        val accountPaymentMethodId = AccountPaymentMethodId(omnibusBankAccountConfiguration.accountPaymentMethodId)
        every {
            bankAccountService.getStatement(
                AccountNumber(omnibusBankAccountConfiguration.accountNo),
                omnibusBankAccountConfiguration.document,
                any(),
                any(),
            )
        } returns BankStatement(listOf(item))
        every {
            internalBankRepository.findBankStatementItem(
                accountPaymentMethodId,
                operationNumber,
            )
        } returns item.right()
        every {
            internalBankRepository.findBankStatementItem(
                virtualBalance.id,
                operationNumber,
            )
        } returns item.right()
        every { accountService.findVirtualBankAccountByDocument(item.counterpartDocument) } returns Either.Right(
            virtualBalance,
        )

        val result = internalBankService.synchronizeBankAccount(
            bankNo = omnibusBankAccountConfiguration.bankNo,
            routingNo = omnibusBankAccountConfiguration.routingNo,
            accountNumber = AccountNumber(omnibusBankAccountConfiguration.accountNo),
        )

        result shouldBe false

        verify {
            internalBankRepository.findBankStatementItem(accountPaymentMethodId, operationNumber)
            internalBankRepository.findBankStatementItem(virtualBalance.id, operationNumber)
            accountService.findVirtualBankAccountByDocument(item.counterpartDocument)
        }
        verify(exactly = 0) {
            internalBankRepository.create(any<InternalBankStatementItem>())
            messagePublisher.sendMessage(any())
        }
    }

    @Test
    fun `should save omnibus bank statement item without target account payment method id when virtual account is not found`() {
        val accountPaymentMethodId = AccountPaymentMethodId(omnibusBankAccountConfiguration.accountPaymentMethodId)
        every {
            bankAccountService.getStatement(
                AccountNumber(omnibusBankAccountConfiguration.accountNo),
                omnibusBankAccountConfiguration.document,
                any(),
                any(),
            )
        } returns BankStatement(listOf(item))
        every {
            internalBankRepository.findBankStatementItem(
                accountPaymentMethodId,
                operationNumber,
            )
        } returns FindError.NotFound.left()
        every { accountService.findVirtualBankAccountByDocument(item.counterpartDocument) } returns FindError.NotFound.left()

        val result = internalBankService.synchronizeBankAccount(
            bankNo = omnibusBankAccountConfiguration.bankNo,
            routingNo = omnibusBankAccountConfiguration.routingNo,
            accountNumber = AccountNumber(omnibusBankAccountConfiguration.accountNo),
        )

        result shouldBe false

        verify {
            internalBankRepository.findBankStatementItem(accountPaymentMethodId, operationNumber)
            internalBankRepository.save(any<OmnibusBankStatementItem>())
        }
        verify(exactly = 0) {
            messagePublisher.sendMessage(any())
        }
    }

    @Test
    fun `should save omnibus bank statement item without target account payment method id when document is found but flow is DEBIT`() {
        val accountPaymentMethodId = AccountPaymentMethodId(omnibusBankAccountConfiguration.accountPaymentMethodId)

        val currentItem = item3.copy(
            counterpartDocument = (virtualBalance.method as InternalBankAccount).document,
        )

        virtualBalance
        every {
            bankAccountService.getStatement(
                AccountNumber(omnibusBankAccountConfiguration.accountNo),
                omnibusBankAccountConfiguration.document,
                any(),
                any(),
            )
        } returns BankStatement(listOf(currentItem))
        every {
            internalBankRepository.findBankStatementItem(
                accountPaymentMethodId,
                operationNumber,
            )
        } returns FindError.NotFound.left()
        every {
            internalBankRepository.findBankStatementItem(
                virtualBalance.id,
                operationNumber,
            )
        } returns FindError.NotFound.left()

        internalBankService.synchronizeBankAccount(
            bankNo = omnibusBankAccountConfiguration.bankNo,
            routingNo = omnibusBankAccountConfiguration.routingNo,
            accountNumber = AccountNumber(omnibusBankAccountConfiguration.accountNo),
        )

        verify {
            internalBankRepository.findBankStatementItem(accountPaymentMethodId, operationNumber)
            internalBankRepository.save(any<OmnibusBankStatementItem>())
        }
        verify(exactly = 0) {
            accountService.findVirtualBankAccountByDocument(currentItem.counterpartDocument)
            internalBankRepository.create(any<InternalBankStatementItem>())
            messagePublisher.sendMessage(any())
        }
    }

    @Test
    fun `should do nothing when virtual account is duplicated`() {
        every {
            bankAccountService.getStatement(
                AccountNumber(omnibusBankAccountConfiguration.accountNo),
                omnibusBankAccountConfiguration.document,
                any(),
                any(),
            )
        } returns BankStatement(listOf(item))
        every { accountService.findVirtualBankAccountByDocument(item.counterpartDocument) } returns FindError.MultipleItemsFound.left()

        assertThrows(SynchronizeBankAccountException::class.java) {
            internalBankService.synchronizeBankAccount(
                bankNo = omnibusBankAccountConfiguration.bankNo,
                routingNo = omnibusBankAccountConfiguration.routingNo,
                accountNumber = AccountNumber(omnibusBankAccountConfiguration.accountNo),
            )
        }

        verify(exactly = 0) {
            internalBankRepository.save(any<OmnibusBankStatementItem>())
            messagePublisher.sendMessage(any())
        }
    }

    @Test
    fun `should do nothing on server error searching virtual account`() {
        every {
            bankAccountService.getStatement(
                AccountNumber(omnibusBankAccountConfiguration.accountNo),
                omnibusBankAccountConfiguration.document,
                any(),
                any(),
            )
        } returns BankStatement(listOf(item))
        every { accountService.findVirtualBankAccountByDocument(item.counterpartDocument) } returns FindError.ServerError(
            RuntimeException(),
        ).left()

        assertThrows(SynchronizeBankAccountException::class.java) {
            internalBankService.synchronizeBankAccount(
                bankNo = omnibusBankAccountConfiguration.bankNo,
                routingNo = omnibusBankAccountConfiguration.routingNo,
                accountNumber = AccountNumber(omnibusBankAccountConfiguration.accountNo),
            )
        }

        verify(exactly = 0) {
            internalBankRepository.save(any<OmnibusBankStatementItem>())
            messagePublisher.sendMessage(any())
        }
    }

    @Test
    fun `deve retornar todos os créditos do período`() {
        val now = getZonedDateTime()

        every {
            internalBankRepository.findAllBankStatementCredits(any(), any(), any())
        } returns listOf(bankStatementItem.copy(amount = 1000), bankStatementItem.copy(amount = 2000))

        withGivenDateTime(now) {
            val result = internalBankService.getCashInByPeriod(
                startDate = now.minusDays(5),
                endDate = now,
                accountPaymentMethodId = AccountPaymentMethodId("mocked-account-payment-method-id"),
            )

            result shouldBe 3000
        }
    }
}