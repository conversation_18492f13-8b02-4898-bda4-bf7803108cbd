package ai.friday.billpayment.app.banking

import DynamoDBUtils
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InternalBankDBRepository
import ai.friday.billpayment.adapters.dynamodb.InternalBankDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.lock.InternalLockProvider
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.balance.DefaultBalanceService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.integrations.BankAccountService
import ai.friday.billpayment.app.integrations.TEDService
import ai.friday.billpayment.app.payment.BalanceInvoiceCheckout
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.TEDResult
import ai.friday.billpayment.app.payment.TEDStatus
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.toPayer
import ai.friday.billpayment.bankStatementItem
import ai.friday.billpayment.createSinglePaymentDataWithBalance
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID_4
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.createLockTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.integration.loadVirtualBalance
import ai.friday.billpayment.invoiceBill
import ai.friday.billpayment.omnibusBankAccountConfiguration
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import io.mockk.verifySequence
import net.javacrumbs.shedlock.core.SimpleLock
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class VirtualBankAccountTest {

    private val lockTableName = "lockTableName"

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val simpleLock = mockk<SimpleLock>(relaxed = true)
    private val lockProvider: InternalLockProvider = mockk() {
        every {
            acquireLock(any())
        } returns simpleLock
    }

    private val dynamoDbEnhancedClient = DynamoDBUtils.setupDynamoDB()
    private val internalBankDynamoDAO = InternalBankDynamoDAO(dynamoDbEnhancedClient)
    private val internalBankRepository = InternalBankDBRepository(internalBankDynamoDAO)
    private val accountDAO = AccountDynamoDAO(dynamoDbEnhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(dynamoDbEnhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(dynamoDbEnhancedClient)
    private val nsuDAO = NSUDynamoDAO(dynamoDbEnhancedClient)
    private val transactionDynamo = TransactionDynamo(dynamoDbEnhancedClient)

    private val accountDBRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )
    private val accountService = AccountService(
        accountConfigurationService = mockk(),
        accountRepository = accountDBRepository,
        chatbotMessagePublisher = mockk(),
        crmService = mockk(),
        notificationAdapter = mockk(),
        walletRepository = mockk(),
    )
    private val bankAccountService: BankAccountService = mockk() {
        every { transfer(any(), any(), any(), any()) } returns BankTransfer(
            operationId = BankOperationId(value = "1"),
            gateway = FinancialServiceGateway.CELCOIN,
            status = BankOperationStatus.SUCCESS,
            amount = invoiceBill.amountTotal,
            authentication = "",
            errorDescription = "",
        )
    }

    private val configuration = OmnibusBankAccountConfiguration(
        accountPaymentMethodId = "*********",
        document = "*********",
        bankNo = 1L,
        routingNo = 1L,
        accountNo = "11111",
        name = "VIA1",
    )

    private val internalBankService = InternalBankService(
        accountService = accountService,
        bankAccountService = bankAccountService,
        internalBankRepository = internalBankRepository,
        messagePublisher = mockk(),
        omnibusBankAccountConfiguration = configuration,
        lockProvider = lockProvider,
        balanceService = DefaultBalanceService(
            accountService,
            internalBankRepository,
            bankAccountService,
            omnibusBankAccountConfiguration,
            mockk(),
        ),
        notificationAdapter = mockk(),
        walletService = mockk(),
        pixPaymentService = mockk(),
    ).apply {
        internalSettlementAccountNo = "1"
        internalCashinAccountNo = "2"
        bankAccountDepositQueueName = "queue"
    }
    private val tedService: TEDService = mockk()

    private val balanceInvoiceCheckout = BalanceInvoiceCheckout(internalBankService, tedService)

    private val virtualBankAccount = InternalBankAccount(
        accountType = AccountType.CHECKING,
        bankNo = 342,
        routingNo = 12,
        accountNo = 1002L,
        accountDv = "01",
        document = "***********",
        bankAccountMode = BankAccountMode.VIRTUAL,
    )

    private val accountId = AccountId(ACCOUNT_ID)
    private val accountPaymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_4)

    private val debitNumber = "321"
    private val creditNumber = "456"
    private val debitOperationId = BankOperationId.build()
    private val successfulBankTransferDebit = BankTransfer(
        operationId = debitOperationId,
        status = BankOperationStatus.SUCCESS,
        amount = invoiceBill.amount,
        gateway = FinancialServiceGateway.ARBI,
        debitOperationNumber = debitNumber,
    )

    private val successfulBankTransferCredit = BankTransfer(
        operationId = BankOperationId.build(),
        status = BankOperationStatus.SUCCESS,
        amount = invoiceBill.amount,
        gateway = FinancialServiceGateway.ARBI,
        creditOperationNumber = creditNumber,
    )

    private val statementItem = InternalBankStatementItem(
        bankStatementItem = bankStatementItem.copy(amount = invoiceBill.amount),
        accountPaymentMethodId = accountPaymentMethodId,
    )

    @BeforeEach
    fun setup() {
        createLockTable(dynamoDB, lockTableName)
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        loadAccountIntoDb(dynamoDB, accountId)

        with(virtualBankAccount) {
            loadVirtualBalance(
                dynamoDB,
                bankNo = bankNo,
                bankRoutingNo = routingNo,
                bankAccountNo = accountNo,
                bankAccountDv = accountDv,
                document = document,
                paymentMethodId = accountPaymentMethodId.value,
                accountId = accountId.value,
                bankIspb = "",
            )
        }
    }

    @Test
    fun `should not capture funds when virtual account has insufficient funds`() {
        val transaction = buildTransaction(virtualBankAccount, accountPaymentMethodId)
        val processedTransaction = balanceInvoiceCheckout.execute(transaction)

        processedTransaction.status shouldBe TransactionStatus.FAILED

        val statement = internalBankRepository.findAllBankStatementItem(accountPaymentMethodId)

        statement.items.shouldBeEmpty()

        verify(exactly = 0) {
            bankAccountService.transfer(any(), any(), any(), any())
        }
    }

    @Test
    fun `should not capture funds when omnibus account has insufficient funds`() {
        val transaction = buildTransaction(virtualBankAccount, accountPaymentMethodId)
        val insufficientFundsBankTransfer = BankTransfer(
            operationId = BankOperationId.build(),
            status = BankOperationStatus.INSUFFICIENT_FUNDS,
            amount = invoiceBill.amount,
            errorDescription = "Insufficient funds",
            gateway = FinancialServiceGateway.ARBI,
        )
        internalBankRepository.create(statementItem)

        every { bankAccountService.transfer(any(), any(), any(), any()) } returns insufficientFundsBankTransfer

        val processedTransaction = balanceInvoiceCheckout.execute(transaction)

        processedTransaction.status shouldBe TransactionStatus.FAILED
        internalBankRepository.findAllBankStatementItem(accountPaymentMethodId).items shouldContainExactly listOf(
            statementItem.bankStatementItem,
        )
        verify(exactly = 1) {
            bankAccountService.transfer(any(), any(), any(), any())
        }
    }

    @Test
    fun `should capture funds when virtual account transaction works`() {
        val transaction = buildTransaction(virtualBankAccount, accountPaymentMethodId)
        val debitNumber = "321"

        internalBankRepository.create(statementItem)

        every {
            tedService.transfer(any(), any(), any(), any())
        } returns TEDResult(
            gateway = FinancialServiceGateway.CELCOIN,
            amount = invoiceBill.amount,
            status = TEDStatus.Success,
            settleDate = getLocalDate(),
        )

        every { bankAccountService.transfer(any(), any(), any(), any()) } returns successfulBankTransferDebit

        val processedTransaction = balanceInvoiceCheckout.execute(transaction)

        processedTransaction.status shouldBe TransactionStatus.COMPLETED

        val statement = internalBankRepository.findAllBankStatementItem(accountPaymentMethodId)

        statement.items shouldContain statementItem.bankStatementItem
        val debits = statement.items.filter { it.flow == BankStatementItemFlow.DEBIT }
        debits.size shouldBe 1
        debits.first().amount shouldBe invoiceBill.amount
        debits.first().operationNumber shouldBe debitNumber

        val slot = slot<String>()
        verifySequence {
            lockProvider.acquireLock(capture(slot))
            bankAccountService.transfer(any(), any(), any(), any())
            simpleLock.unlock()
        }
        slot.captured shouldBe accountPaymentMethodId.value
    }

    @Test
    fun `should fail transaction when lock is not acquired`() {
        val transaction = buildTransaction(virtualBankAccount, accountPaymentMethodId)

        internalBankRepository.create(statementItem)

        every {
            lockProvider.acquireLock(any())
        } returns null

        val processedTransaction = balanceInvoiceCheckout.execute(transaction)

        verify(exactly = 0) {
            tedService.transfer(any(), any(), any(), any())
            bankAccountService.transfer(any(), any(), any(), any())
        }

        val slot = slot<String>()

        verify {
            lockProvider.acquireLock(capture(slot))
        }

        slot.captured shouldBe accountPaymentMethodId.value

        processedTransaction.status shouldBe TransactionStatus.FAILED

        val statement = internalBankRepository.findAllBankStatementItem(accountPaymentMethodId)

        statement.items shouldContain statementItem.bankStatementItem
        val debits = statement.items.filter { it.flow == BankStatementItemFlow.DEBIT }
        debits.shouldBeEmpty()
    }

    @Test
    fun `should capture and undo capture when invoice transfer fails`() {
        val transaction = buildTransaction(virtualBankAccount, accountPaymentMethodId)

        internalBankRepository.create(statementItem)

        every {
            bankAccountService.transfer(
                any(),
                any(),
                any(),
                any(),
            )
        } returns successfulBankTransferDebit andThen successfulBankTransferCredit

        every {
            tedService.transfer(any(), any(), any(), any())
        } returns TEDResult(
            gateway = FinancialServiceGateway.CELCOIN,
            amount = invoiceBill.amount,
            status = TEDStatus.Failure("error"),
            settleDate = getLocalDate(),
        )

        balanceInvoiceCheckout.execute(transaction).status shouldBe TransactionStatus.FAILED

        val statement = internalBankRepository.findAllBankStatementItem(accountPaymentMethodId)

        val debits = statement.items.filter { it.flow == BankStatementItemFlow.DEBIT }
        debits.size shouldBe 1
        debits.first().amount shouldBe invoiceBill.amount
        debits.first().operationNumber shouldBe debitNumber

        val credit = statement.items.filter { it.flow == BankStatementItemFlow.CREDIT }
        credit.size shouldBe 2
        credit.last().amount shouldBe invoiceBill.amount
        credit.last().ref shouldBe debitOperationId.value
        credit.last().operationNumber shouldBe creditNumber

        verify(exactly = 2) {
            bankAccountService.transfer(any(), any(), any(), any())
        }
    }

    private fun buildTransaction(
        bankAccount: InternalBankAccount,
        accountPaymentMethodId: AccountPaymentMethodId,
    ): Transaction { // duplicado
        val balance = AccountPaymentMethod(
            id = accountPaymentMethodId,
            status = AccountPaymentMethodStatus.ACTIVE,
            method = bankAccount,
            accountId = AccountId(ACCOUNT_ID),
        )
        return Transaction(
            type = TransactionType.INVOICE_PAYMENT,
            payer = ACCOUNT.toPayer(),
            paymentData = createSinglePaymentDataWithBalance(balance, invoiceBill.amountTotal),
            settlementData = SettlementData(invoiceBill, 0, invoiceBill.amountTotal, null),
            nsu = 1,
            actionSource = ActionSource.System,
            walletId = invoiceBill.walletId,
        )
    }
}