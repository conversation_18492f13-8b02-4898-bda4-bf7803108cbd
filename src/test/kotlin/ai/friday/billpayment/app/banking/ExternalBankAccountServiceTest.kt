package ai.friday.billpayment.app.banking

import DynamoDBUtils
import ai.friday.billpayment.adapters.dynamodb.ExternalBankAccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.ExternalBankAccountDynamoDAO
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.withPixParticipants
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

internal class ExternalBankAccountServiceTest {

    private val dynamoDbEnhancedClient = DynamoDBUtils.setupDynamoDB()
    private val externalBankAccountDynamoDAO = ExternalBankAccountDynamoDAO(dynamoDbEnhancedClient)

    private val externalBankAccountRepository = ExternalBankAccountDbRepository(externalBankAccountDynamoDAO)
    private val externalBankAccountService = DefaultExternalBankAccountService(externalBankAccountRepository)

    private val document = Document(DOCUMENT)

    @Test
    fun `não deve encontrar conta quando o usuário não tem conta salva`() {
        val externalBankAccount = externalBankAccountService.findLastUsed(document)

        externalBankAccount shouldBe null
    }

    @Test
    fun `deve lançar exceção ao tentar salvar conta sem compe nem ispb`() {
        assertThrows<IllegalArgumentException> {
            externalBankAccountService.saveLastUsed(
                externalBankAccount = ExternalBankAccount(
                    document = document,
                    bankNo = null,
                    bankISPB = null,
                    routingNo = 0,
                    accountNo = 0,
                    accountDv = "",
                    accountType = null,
                ),
            )
        }
    }

    @Test
    fun `deve enriquecer os dados da conta com ISPB de um participante Pix conhecido`() {
        val lastUsedBankAccount = ExternalBankAccount(
            document = document,
            bankNo = 1,
            bankISPB = null,
            routingNo = 2,
            accountNo = 3,
            accountDv = "4",
            accountType = null,
        )

        val pixParticipant = FinancialInstitution(name = "payee mock institution", ispb = "********", compe = 1)

        withPixParticipants(pixParticipant) {
            externalBankAccountService.saveLastUsed(externalBankAccount = lastUsedBankAccount)
        }

        with(externalBankAccountService.findLastUsed(document)!!) {
            document shouldBe lastUsedBankAccount.document
            bankNo shouldBe pixParticipant.compe
            bankISPB shouldBe pixParticipant.ispb
            routingNo shouldBe lastUsedBankAccount.routingNo
            accountNo shouldBe lastUsedBankAccount.accountNo
            accountDv shouldBe lastUsedBankAccount.accountDv
        }
    }

    @Test
    fun `não deve enriquecer os dados da conta com ISPB de um participante Pix desconhecido`() {
        val lastUsedBankAccount = ExternalBankAccount(
            document = document,
            bankNo = 2,
            bankISPB = null,
            routingNo = 2,
            accountNo = 3,
            accountDv = "4",
            accountType = null,
        )

        withPixParticipants(FinancialInstitution(name = "payee mock institution", ispb = "********", compe = 1)) {
            externalBankAccountService.saveLastUsed(externalBankAccount = lastUsedBankAccount)
        }

        with(externalBankAccountService.findLastUsed(document)!!) {
            document shouldBe lastUsedBankAccount.document
            bankNo shouldBe lastUsedBankAccount.bankNo
            bankISPB shouldBe lastUsedBankAccount.bankISPB
            routingNo shouldBe lastUsedBankAccount.routingNo
            accountNo shouldBe lastUsedBankAccount.accountNo
            accountDv shouldBe lastUsedBankAccount.accountDv
        }
    }

    @Test
    fun `deve enriquecer os dados da conta com COMPE de um participante Pix conhecido`() {
        val lastUsedBankAccount = ExternalBankAccount(
            document = document,
            bankNo = null,
            bankISPB = "********",
            routingNo = 2,
            accountNo = 3,
            accountDv = "4",
            accountType = null,
        )

        val pixParticipant = FinancialInstitution(name = "payee mock institution", ispb = "********", compe = 1)

        withPixParticipants(pixParticipant) {
            externalBankAccountService.saveLastUsed(externalBankAccount = lastUsedBankAccount)
        }

        with(externalBankAccountService.findLastUsed(document)!!) {
            document shouldBe lastUsedBankAccount.document
            bankNo shouldBe pixParticipant.compe
            bankISPB shouldBe pixParticipant.ispb
            routingNo shouldBe lastUsedBankAccount.routingNo
            accountNo shouldBe lastUsedBankAccount.accountNo
            accountDv shouldBe lastUsedBankAccount.accountDv
        }
    }

    @Test
    fun `não deve enriquecer os dados da conta com COMPE de um participante Pix desconhecido`() {
        val lastUsedBankAccount = ExternalBankAccount(
            document = document,
            bankNo = null,
            bankISPB = "********",
            routingNo = 2,
            accountNo = 3,
            accountDv = "4",
            accountType = null,
        )

        withPixParticipants(FinancialInstitution(name = "payee mock institution", ispb = "********", compe = 1)) {
            externalBankAccountService.saveLastUsed(externalBankAccount = lastUsedBankAccount)
        }

        with(externalBankAccountService.findLastUsed(document)!!) {
            document shouldBe lastUsedBankAccount.document
            bankNo shouldBe lastUsedBankAccount.bankNo
            bankISPB shouldBe lastUsedBankAccount.bankISPB
            routingNo shouldBe lastUsedBankAccount.routingNo
            accountNo shouldBe lastUsedBankAccount.accountNo
            accountDv shouldBe lastUsedBankAccount.accountDv
        }
    }

    @Test
    fun `deve encontrar a ultima conta quando o usuário salvou mais de uma`() {
        val now = getZonedDateTime()
        val lastUsedBankAccount = ExternalBankAccount(
            document = document,
            bankNo = 9,
            bankISPB = "********",
            routingNo = 2,
            accountNo = 3,
            accountDv = "4",
            accountType = null,
        )
        val anotherBankAccount = lastUsedBankAccount.copy(bankNo = 2)
        val anotherBankAccount3 = lastUsedBankAccount.copy(bankNo = 3)

        withGivenDateTime(now) {
            externalBankAccountService.saveLastUsed(externalBankAccount = anotherBankAccount)
        }
        withGivenDateTime(now.plusSeconds(1)) {
            externalBankAccountService.saveLastUsed(externalBankAccount = lastUsedBankAccount)
        }
        withGivenDateTime(now.plusSeconds(2)) {
            externalBankAccountService.saveLastUsed(externalBankAccount = anotherBankAccount3)
        }
        withGivenDateTime(now.plusSeconds(3)) {
            externalBankAccountService.saveLastUsed(externalBankAccount = lastUsedBankAccount)
        }

        externalBankAccountService.findLastUsed(document)!!.bankNo shouldBe lastUsedBankAccount.bankNo
        externalBankAccountService.findLastUsed(document)!!.lastUsed shouldNotBe null
    }

    @Test
    fun `deve listar todas contas que o usuário fez cash in`() {
        val lastUsedBankAccount = ExternalBankAccount(
            document = document,
            bankNo = 1,
            bankISPB = "********",
            routingNo = 2,
            accountNo = 3,
            accountDv = "4",
            accountType = null,
        )
        val anotherBankAccount = lastUsedBankAccount.copy(bankNo = 2)

        externalBankAccountService.saveLastUsed(externalBankAccount = anotherBankAccount)
        externalBankAccountService.saveLastUsed(externalBankAccount = lastUsedBankAccount)

        val result = externalBankAccountService.findAll(document)

        result.size shouldBe 2
    }

    @Test
    fun `ao tentar atualizar um registro de dados da conta não deve deixar nulavel um accountType existente`() {
        val lastUsedBankAccount = ExternalBankAccount(
            document = document,
            bankNo = 1,
            bankISPB = "********",
            routingNo = 2,
            accountNo = 3,
            accountDv = "4",
            accountType = null,
        )
        val anotherBankAccount = lastUsedBankAccount.copy(accountType = "CACC")

        externalBankAccountService.saveLastUsed(externalBankAccount = anotherBankAccount)
        externalBankAccountService.saveLastUsed(externalBankAccount = lastUsedBankAccount)

        externalBankAccountService.findLastUsed(document)!!.accountType shouldBe "CACC"
    }
}