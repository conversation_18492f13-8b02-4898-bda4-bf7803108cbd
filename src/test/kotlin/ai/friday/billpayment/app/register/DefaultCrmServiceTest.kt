package ai.friday.billpayment.app.register

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.inappsubscription.InAppSubscription
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionProductId
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionReason
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionRepository
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStatus
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStore
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.CrmContact
import ai.friday.billpayment.app.integrations.CrmRepository
import ai.friday.billpayment.app.integrations.SubscriptionRepository
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.register.instrumentation.DefaultCrmService
import ai.friday.billpayment.app.subscription.Subscription
import ai.friday.billpayment.app.subscription.SubscriptionPaymentStatus
import ai.friday.billpayment.app.subscription.SubscriptionStatus
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.brazilTimeZone
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldStartWith
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate
import java.time.ZonedDateTime
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow

class DefaultCrmServiceTest {

    private val crmRepositoryMock = mockk<CrmRepository>(relaxUnitFun = true)

    private val accountRepository = mockk<AccountRepository>() {
        every {
            findPhysicalBankAccountByAccountId(any())
        } returns emptyList()
    }

    private val accountRegisterRepository = mockk<AccountRegisterRepository>()

    private val validCNPJ = "**************"
    private val walletFixture = WalletFixture()
    private val walletCPF = walletFixture.buildWallet(otherMembers = listOf(walletFixture.limitedParticipant), id = WalletId("CARTEIRA CPF"))
    private val walletCNPJ = walletFixture.buildWallet(walletFounder = walletFixture.assistant.copy(document = validCNPJ, type = MemberType.FOUNDER), otherMembers = listOf(walletFixture.founder.copy(type = MemberType.COFOUNDER), walletFixture.participant.copy(type = MemberType.COFOUNDER)), id = WalletId("CARTEIRA CNPJ"))

    private val activeInAppSubscription = InAppSubscription(
        walletFixture.founder.accountId,
        InAppSubscriptionStatus.ACTIVE,
        endsAt = ZonedDateTime.of(2025, 5, 9, 10, 15, 30, 0, brazilTimeZone),
        InAppSubscriptionStore.APP_STORE,
        reason = InAppSubscriptionReason.SUBSCRIPTION,
        inAppSubscriptionAccessConcessionId = null,
        autoRenew = true,
        price = 999,
        productId = InAppSubscriptionProductId("PRODUTO"),
        offStoreProductId = null,
    )

    private val pixSubscription = Subscription(
        accountId = walletCPF.founder.accountId,
        document = Document(walletCPF.founder.document),
        status = SubscriptionStatus.ACTIVE,
        amount = 1000L,
        dayOfMonth = 10,
        recurrenceId = RecurrenceId("any-recurrence-id"),
        paymentStatus = SubscriptionPaymentStatus.PAID,
        walletId = walletCPF.id,
        nextEffectiveDueDate = LocalDate.of(2025, 6, 10),
    )

    private val walletRepository = mockk<WalletRepository>() {
        every {
            findWallets(walletFixture.founder.accountId, MemberStatus.ACTIVE)
        } returns listOf(
            walletCPF,
            walletCNPJ,
        )
    }

    private val inAppSubscriptionRepository = mockk<InAppSubscriptionRepository>() {
        every {
            findOrNull(walletFixture.founder.accountId)
        } returns activeInAppSubscription
    }

    private val pixSubscriptionRepository = mockk<SubscriptionRepository>() {
        every {
            find(walletFixture.founder.accountId)
        } returns pixSubscription
    }

    private val crmService = DefaultCrmService(
        crmRepository = crmRepositoryMock,
        accountRepository = accountRepository,
        accountRegisterRepository = accountRegisterRepository,
        inAppSubscriptionRepository = inAppSubscriptionRepository,
        subscriptionRepository = pixSubscriptionRepository,
        walletRepository = walletRepository,
    )

    @Test
    fun `should not throw exception when repository fails when upserting contact`() {
        every {
            crmRepositoryMock.upsertContact(ofType(CrmContact::class))
        } throws NoStackTraceException("Fake error")

        assertDoesNotThrow {
            crmService.upsertContact(walletFixture.founderAccount)
        }
    }

    @Test
    fun `should not throw exception when repository fails when activating contact`() {
        every {
            crmRepositoryMock.findContact(ofType(AccountId::class))
        } throws NoStackTraceException("Fake error")

        assertDoesNotThrow {
            crmService.upsertContact(walletFixture.founderAccount)
        }
    }

    @Test
    fun `deve listar as carteiras do usuário corretamente`() {
        val result = crmService.upsertContact(walletFixture.founderAccount)
        result.cnpjWallets shouldContainExactly listOf(walletCNPJ.id)
        result.cpfWallets shouldContainExactly listOf(walletCPF.id)
    }

    @Test
    fun `deve listar usuários que compartilham carteira com o usuário onde ele é founder`() {
        val result = crmService.upsertContact(walletFixture.founderAccount)
        result.otherMembersOnWallets shouldContainExactly listOf(walletFixture.limitedParticipant.accountId)
    }

    @Test
    fun `deve buscar os dados de assinatura in-app do usuário `() {
        val result = crmService.upsertContact(walletFixture.founderAccount.copy(subscriptionType = SubscriptionType.IN_APP))

        verify { inAppSubscriptionRepository.findOrNull(any()) }
        verify(exactly = 0) { pixSubscriptionRepository.find(any<AccountId>()) }

        result.subscriptionType shouldBe SubscriptionType.IN_APP
        result.subscriptionEndsAt shouldStartWith "2025-05-09T10:15:30"
        result.subscriptionAccessConcessionReason shouldBe activeInAppSubscription.reason
        result.subscriptionAutoRenew shouldBe true
    }

    @Test
    fun `deve buscar os dados de assinatura pix do usuário`() {
        val result = crmService.upsertContact(walletFixture.founderAccount.copy(subscriptionType = SubscriptionType.PIX))

        verify(exactly = 0) { inAppSubscriptionRepository.findOrNull(any()) }
        verify { pixSubscriptionRepository.find(walletFixture.founder.accountId) }

        result.subscriptionType shouldBe SubscriptionType.PIX
        result.subscriptionEndsAt shouldStartWith "2025-06-11T00:00"
        result.subscriptionAccessConcessionReason shouldBe null
        result.subscriptionAutoRenew shouldBe null
    }
}