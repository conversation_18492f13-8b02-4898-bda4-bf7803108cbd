package ai.friday.billpayment.app.register.kyc

import ai.friday.billpayment.accountRegisterDataMissingAcceptedAt
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.PepQuery
import ai.friday.billpayment.app.account.PepQueryResult
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import java.time.LocalDate
import java.time.ZonedDateTime
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

internal class KycDossierTest {

    private val accountRegisterData = accountRegisterDataMissingAcceptedAt
    private val baseKycDossier = KycDossier(
        taxId = KycDossierTaxIdRegion(region = "RJ-SP"),
        pep = PepQuery(at = ZonedDateTime.now(), result = PepQueryResult.NOT_PEP),
        sanctions = listOf(),
        globalSanctionsList = listOf(),
        officialData = KycDossierOfficialDocumentData(
            provider = "receita",
            name = "John Doe",
            birthDate = LocalDate.of(1980, 2, 1),
            socialName = null,
            hasObitIndication = false,
            deathYear = null,
            regular = false,
            status = "",
        ),
        phones = listOf(),
        emails = listOf(),
        mep = KycDossierMepQuery(
            exposureLevel = null,
            celebrityLevel = null,
            unpopularityLevel = null,
        ),
        gender = null, motherName = null, fatherName = null,
    )

    @Test
    fun `deve que nome informado não coincide com o consultado`() {
        val kycDossier = baseKycDossier.copy(
            officialData = baseKycDossier.officialData.copy(
                name = "José Doe",
                provider = "Receita Federal",
            ),
        )

        kycDossier.toForm(
            accountRegisterData,
            faceMatch = null,
            documentScan = null,
        ).personalData.fullName shouldBe KycDossierFormPersonalData.MandatoryWithQuery(
            declared = "Luke Skywalker da Silva",
            query = KycDossierFormPersonalData.Query(
                value = "José Doe",
                result = "Não (Receita Federal)",
                warning = true,
            ),
        )
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "nome completo,Nome completo",
            " nome     completo,nome completo ",
            "aeiouç,ÁÉÍÓÚÇ",
            "joão cláudio do vovô , Joao claudio do    vovo   ",
        ],
    )
    fun `deve informar que o nome informado coincide com o consultado`(providedName: String, kycName: String) {
        val kycDossier = baseKycDossier.copy(
            officialData = baseKycDossier.officialData.copy(
                name = kycName,
                provider = "Receita Federal",
            ),
        )

        kycDossier.toForm(
            accountRegisterData.copy(documentInfo = accountRegisterData.documentInfo!!.copy(name = providedName)),
            faceMatch = null,
            documentScan = null,
        ).personalData.fullName shouldBe KycDossierFormPersonalData.MandatoryWithQuery(
            declared = providedName,
            query = KycDossierFormPersonalData.Query(
                value = kycName,
                result = "Sim (Receita Federal)",
                warning = false,
            ),
        )
    }

    @Test
    fun `deve informar que a data de nascimento informada coincide com a consultada`() {
        val kycDossier = baseKycDossier.copy(
            officialData = baseKycDossier.officialData.copy(
                birthDate = LocalDate.of(1950, 12, 25),
                provider = "Receita Federal",
            ),
        )

        kycDossier.toForm(
            accountRegisterData.copy(
                documentInfo = accountRegisterData.documentInfo!!.copy(
                    birthDate = LocalDate.of(
                        1950,
                        12,
                        25,
                    ),
                ),
            ),
            faceMatch = null,
            documentScan = null,
        ).personalData.birthDate shouldBe KycDossierFormPersonalData.MandatoryWithQuery(
            declared = "25/12/1950",
            query = KycDossierFormPersonalData.Query(
                value = "25/12/1950",
                result = "Sim (Receita Federal)",
                warning = false,
            ),
        )
    }

    @Test
    fun `deve informar que a data de nascimento informada não coincide com a consultada`() {
        val kycDossier =
            baseKycDossier.copy(
                officialData = baseKycDossier.officialData.copy(
                    birthDate = LocalDate.of(2000, 3, 5),
                    provider = "Receita Federal",
                ),
            )

        kycDossier.toForm(
            accountRegisterData.copy(
                documentInfo = accountRegisterData.documentInfo!!.copy(birthDate = LocalDate.of(1980, 2, 1)),
            ),
            faceMatch = null,
            documentScan = null,
        ).personalData.birthDate shouldBe KycDossierFormPersonalData.MandatoryWithQuery(
            declared = "01/02/1980",
            query = KycDossierFormPersonalData.Query(
                value = "05/03/2000",
                result = "Não (Receita Federal)",
                warning = true,
            ),
        )
    }

    @Test
    fun `deve informar quando o telefone não é relacionado ao CPF`() {
        val kycDossier = baseKycDossier.copy(phones = emptyList())

        kycDossier.toForm(accountRegisterData, faceMatch = null, documentScan = null).phone shouldBe
            KycDossierFormEmailOrPhone(
                value = "+*************",
                relatedToCpf = "Não",
                active = "Não disponível",
                recent = "Não disponível",
                createdAt = "Não disponível",
            )
    }

    @Test
    fun `deve informar quando o telefone é relacionado ao CPF`() {
        val now = getZonedDateTime()
        val phone = KycDossierPhone(
            number = "*********",
            areaCode = "21",
            countryCode = "55",
            createdAt = now.toLocalDate().minusDays(PHONE_AND_EMAIL_AGE_IN_DAYS_THRESHOLD + 1),
            active = true,
        )
        val expectedFullPhoneNumber = "+${phone.countryCode}${phone.areaCode}${phone.number}"

        val kycDossier = baseKycDossier.copy(phones = listOf(phone))
        val accountRegisterData = accountRegisterData.copy(
            mobilePhone = MobilePhone(expectedFullPhoneNumber),
        )

        withGivenDateTime(now) {
            kycDossier.toForm(accountRegisterData, faceMatch = null, documentScan = null).phone shouldBe
                KycDossierFormEmailOrPhone(
                    value = "+*************",
                    relatedToCpf = "Sim",
                    active = "Sim",
                    recent = "Não",
                    createdAt = "366 dias",
                )
        }
    }

    @Test
    fun `deve informar quando o telefone é recente`() {
        val now = getZonedDateTime()
        val phone = KycDossierPhone(
            number = "*********",
            areaCode = "21",
            countryCode = "55",
            createdAt = now.toLocalDate(),
            active = true,
        )
        val expectedFullPhoneNumber = "+${phone.countryCode}${phone.areaCode}${phone.number}"

        val kycDossier = baseKycDossier.copy(phones = listOf(phone))
        val accountRegisterData = accountRegisterData.copy(
            mobilePhone = MobilePhone(expectedFullPhoneNumber),
        )
        withGivenDateTime(now) {
            kycDossier.toForm(accountRegisterData, faceMatch = null, documentScan = null).phone shouldBe
                KycDossierFormEmailOrPhone(
                    value = "+*************",
                    relatedToCpf = "Sim",
                    active = "Sim",
                    recent = "Sim",
                    createdAt = "0 dias",
                )
        }
    }

    @Test
    fun `deve informar quando o e-mail não é relacionado ao CPF`() {
        val kycDossier = baseKycDossier.copy(emails = emptyList())

        kycDossier.toForm(accountRegisterData, faceMatch = null, documentScan = null).email shouldBe
            KycDossierFormEmailOrPhone(
                value = "<EMAIL>",
                relatedToCpf = "Não",
                active = "Não disponível",
                recent = "Não disponível",
                createdAt = "Não disponível",
            )
    }

    @Test
    fun `deve retornar quando o e-mail é relacionado ao CPF`() {
        val now = getZonedDateTime()
        val expectedEmailAddress = EmailAddress("<EMAIL>")
        val kycDossierEmail = KycDossierEmail(
            value = expectedEmailAddress,
            createdAt = getLocalDate().minusDays(PHONE_AND_EMAIL_AGE_IN_DAYS_THRESHOLD + 1),
            active = true,
        )

        val kycDossier = baseKycDossier.copy(emails = listOf(kycDossierEmail))
        val accountRegisterData = accountRegisterData.copy(
            emailAddress = kycDossierEmail.value,
        )
        withGivenDateTime(now) {
            kycDossier.toForm(accountRegisterData, faceMatch = null, documentScan = null).email shouldBe
                KycDossierFormEmailOrPhone(
                    value = "<EMAIL>",
                    relatedToCpf = "Sim",
                    active = "Sim",
                    recent = "Não",
                    createdAt = "366 dias",
                )
        }
    }

    @Test
    fun `deve informar quando o e-mail é recente`() {
        val now = getZonedDateTime()
        val expectedEmailAddress = EmailAddress("<EMAIL>")
        val kycDossierEmail = KycDossierEmail(
            value = expectedEmailAddress,
            createdAt = now.toLocalDate(),
            active = true,
        )

        val kycDossier = baseKycDossier.copy(emails = listOf(kycDossierEmail))
        val accountRegisterData = accountRegisterData.copy(
            emailAddress = kycDossierEmail.value,
        )
        withGivenDateTime(now) {
            kycDossier.toForm(accountRegisterData, faceMatch = null, documentScan = null).email shouldBe
                KycDossierFormEmailOrPhone(
                    value = "<EMAIL>",
                    relatedToCpf = "Sim",
                    active = "Sim",
                    recent = "Sim",
                    createdAt = "0 dias",
                )
        }
    }

    @Test
    fun `deve informar que nome da mãe é igual ao consultado`() {
        val kycMotherName = "MARIA DAS GRAÇAS XUXA MENEGUEL"
        val kycDossier = baseKycDossier.copy(motherName = kycMotherName)
        val accountRegisterData = accountRegisterData.copy(
            documentInfo = accountRegisterData.documentInfo!!.copy(
                motherName = "maria das gracas xuxa meneguel",
            ),
        )

        kycDossier.toForm(
            accountRegisterData,
            faceMatch = null,
            documentScan = null,
        ).personalData.motherName shouldBe KycDossierFormPersonalData.OptionalWithQuery(
            declared = "maria das gracas xuxa meneguel",
            query = KycDossierFormPersonalData.Query(result = "Semelhante", value = kycMotherName, warning = false),
        )
    }

    @Test
    fun `deve informar quando o nome da mãe é diferente do consultado`() {
        val kycMotherName = "${accountRegisterData.documentInfo!!.motherName} da Silva"
        val kycDossier = baseKycDossier.copy(motherName = kycMotherName)

        kycDossier.toForm(
            accountRegisterData,
            faceMatch = null,
            documentScan = null,
        ).personalData.motherName shouldBe KycDossierFormPersonalData.OptionalWithQuery(
            declared = accountRegisterData.documentInfo!!.motherName,
            query = KycDossierFormPersonalData.Query(result = "Divergente", value = kycMotherName, warning = true),
        )
    }

    @Test
    fun `deve informar que não é possível encontrar o nome da mãe`() {
        val kycDossier = baseKycDossier.copy(motherName = null)

        kycDossier.toForm(
            accountRegisterData,
            faceMatch = null,
            documentScan = null,
        ).personalData.motherName shouldBe KycDossierFormPersonalData.OptionalWithQuery(
            declared = accountRegisterData.documentInfo!!.motherName,
            query = KycDossierFormPersonalData.Query(
                result = "Não disponível",
                value = "Não disponível",
                warning = false,
            ),
        )
    }

    @Test
    fun `deve informar quando o nome do pai for igual ao consultado`() {
        val kycFatherName = "LUCIANO ZAFFIR"
        val kycDossier = baseKycDossier.copy(fatherName = kycFatherName)
        val accountRegisterData = accountRegisterData.copy(
            documentInfo = accountRegisterData.documentInfo!!.copy(
                fatherName = "luçiano záffír",
            ),
        )

        kycDossier.toForm(
            accountRegisterData,
            faceMatch = null,
            documentScan = null,
        ).personalData.fatherName shouldBe KycDossierFormPersonalData.OptionalWithQuery(
            declared = "luçiano záffír",
            query = KycDossierFormPersonalData.Query(result = "Semelhante", value = kycFatherName, warning = false),
        )
    }

    @Test
    fun `deve informar quando o nome do pai é diferente do consultado`() {
        val kycFatherName = "${accountRegisterData.documentInfo!!.fatherName} da Silva"
        val kycDossier = baseKycDossier.copy(fatherName = kycFatherName)

        kycDossier.toForm(
            accountRegisterData,
            faceMatch = null,
            documentScan = null,
        ).personalData.fatherName shouldBe KycDossierFormPersonalData.OptionalWithQuery(
            declared = accountRegisterData.documentInfo!!.fatherName,
            query = KycDossierFormPersonalData.Query(result = "Divergente", value = kycFatherName, warning = true),
        )
    }

    @Test
    fun `deve informar quando o nome do pai não é encontrado`() {
        val kycDossier = baseKycDossier.copy(fatherName = null)

        kycDossier.toForm(
            accountRegisterData,
            faceMatch = null,
            documentScan = null,
        ).personalData.fatherName shouldBe KycDossierFormPersonalData.OptionalWithQuery(
            declared = accountRegisterData.documentInfo!!.fatherName,
            query = KycDossierFormPersonalData.Query(
                result = "Não disponível",
                value = "Não disponível",
                warning = false,
            ),
        )
    }

    @Test
    fun `should not return a warning when tax id status is regular`() {
        val kycDossier = baseKycDossier.copy(
            officialData = baseKycDossier.officialData.copy(regular = true),
        )

        with(kycDossier.toForm(accountRegisterData, faceMatch = null, documentScan = null).cpf) {
            status.value shouldContain "Regular"
            status.warning shouldBe false
        }
    }

    @Test
    fun `should return a warning when tax id status is not regular`() {
        val kycDossier = baseKycDossier.copy(
            officialData = baseKycDossier.officialData.copy(regular = false),
        )

        with(kycDossier.toForm(accountRegisterData, faceMatch = null, documentScan = null).cpf) {
            status.value shouldContain "Irregular"
            status.warning shouldBe true
        }
    }

    @Test
    fun `should return a warning when kyc obit indication is true`() {
        val kycDossier = baseKycDossier.copy(officialData = baseKycDossier.officialData.copy(hasObitIndication = true))

        with(kycDossier.toForm(accountRegisterData, faceMatch = null, documentScan = null).cpf) {
            hasObitIndication.value shouldContain "Sim"
            hasObitIndication.warning shouldBe true
        }
    }

    @Test
    fun `should return ok when kyc obit indication is false`() {
        val kycDossier = baseKycDossier.copy(officialData = baseKycDossier.officialData.copy(hasObitIndication = false))

        with(kycDossier.toForm(accountRegisterData, faceMatch = null, documentScan = null).cpf) {
            hasObitIndication.value shouldContain "Não"
            hasObitIndication.warning shouldBe false
        }
    }

    @Test
    fun `deve informar que a foto do rosto não foi comparada com a do documento`() {
        with(baseKycDossier.toForm(accountRegisterData, null, documentScan = null).biometry) {
            documentFaceMatch shouldBe KycDossierFormBiometry.WithPercentage(
                result = "Não verificado",
                percentage = null,
                warning = true,
            )
        }
    }

    @Test
    fun `deve informar que a foto do rosto foi comparada com a do documento`() {
        with(baseKycDossier.toForm(accountRegisterData, faceMatch = FaceMatchResult(match = true, similarity = 97), documentScan = null).biometry) {
            documentFaceMatch shouldBe KycDossierFormBiometry.WithPercentage(
                result = "Sim",
                percentage = "97%",
                warning = false,
            )
        }
    }
}