package ai.friday.billpayment.app.register.kyc

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.accountRegisterDataMissingAcceptedAt
import ai.friday.billpayment.accountRegisterMissingOnlyAgreement
import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.DocumentInfo
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.account.MonthlyIncome
import ai.friday.billpayment.app.account.PepQuery
import ai.friday.billpayment.app.account.PepQueryResult
import ai.friday.billpayment.app.account.PoliticallyExposed
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.account.UploadedDocumentImages
import ai.friday.billpayment.app.account.UserFilesConfiguration
import ai.friday.billpayment.app.documentscan.DocumentScanService
import ai.friday.billpayment.app.integrations.BigDataService
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.FaceMatcher
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.integrations.PDFConverter
import ai.friday.billpayment.app.integrations.TemplateCompiler
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Called
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.io.InputStream
import java.time.LocalDate
import java.time.ZonedDateTime
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

internal class KycServiceTest {

    private val builtHtml = "<html></html>"

    private val handlebarTemplateCompilerMock: TemplateCompiler = mockk {
        every { buildHtml(any()) } returns CompiledHtml(builtHtml)
    }

    private val bigDataServiceMock: BigDataService = mockk {
        every { getKycDossier(any()) } returns KycDossier(
            gender = null,
            taxId = KycDossierTaxIdRegion(region = null),
            motherName = null,
            phones = listOf(),
            emails = listOf(),
            mep = KycDossierMepQuery(
                exposureLevel = null,
                celebrityLevel = null,
                unpopularityLevel = null,
            ),
            pep = PepQuery(
                at = ZonedDateTime.now(),
                result = PepQueryResult.NOT_PEP,
            ),
            officialData = KycDossierOfficialDocumentData(
                provider = "RFB",
                name = "",
                birthDate = LocalDate.EPOCH,
                socialName = null,
                hasObitIndication = false,
                deathYear = null,
                regular = false,
                status = "FAKE",
            ),
            sanctions = emptyList(),
            fatherName = null,
            globalSanctionsList = listOf("FAKE A", "FAKE B", "FAKE C"),
        ).right()
    }

    private val faceMatcherMock: FaceMatcher = mockk {
        every { match(any<InputStream>(), any()) } returns FaceMatchResult(match = true, similarity = 9).right()
    }

    private val objectRepository: ObjectRepository = mockk(relaxUnitFun = true) {
        every { loadObject(any(), any()) } returns InputStream.nullInputStream()
    }

    private val documentScanServiceMock: DocumentScanService = mockk {
        every { getResult(any()) } returns null.right()
        every { getImage(any()) } returns null.right()
    }

    private val pdfmockcontent: ByteArray = "conteudo do pdf".toByteArray()
    private val pdfConverterMock: PDFConverter = mockk() {
        every { convert(any(), false) } returns pdfmockcontent
    }

    private val userFilesConfiguration = UserFilesConfiguration().apply {
        bucket = "bucketMock"
        path = "pathMock"
        documentPrefix = "documentPrefixMock"
        selfiePrefix = "selfiePrefixMock"
        region = "regionMock"
        contractPrefix = "contractPrefixMock"
        kycPrefix = "kycPrefixMock"
    }

    private val service: KycService = KycService(
        handlebarTemplateCompiler = handlebarTemplateCompilerMock,
        bigDataService = bigDataServiceMock,
        objectRepository = objectRepository,
        userFilesConfiguration = userFilesConfiguration,
        faceMatcher = faceMatcherMock,
        pdfConverter = pdfConverterMock,
        documentScanService = documentScanServiceMock,
    )

    @Nested
    @DisplayName("on KycDossier build")
    inner class BuildKycDossier {
        private val accountRegisterData = accountRegisterDataMissingAcceptedAt

        private val accountRegisterDataMissingAcceptedAtWithRG = accountRegisterMissingOnlyAgreement.copy(
            uploadedCNH = null,
            uploadedDocument = UploadedDocumentImages(
                front = StoredObject(region = "us-east-1", bucket = "123", key = "front"),
                documentType = DocumentType.RG,
                back = StoredObject(region = "us-east-1", bucket = "123", key = "back"),
            ),
        )

        @Test
        fun `should call services and upload kyc with RG`() {
            val now = getZonedDateTime()

            withGivenDateTime(now) {
                service.generate(accountRegisterDataMissingAcceptedAtWithRG)

                val storedObject = StoredObject(
                    region = userFilesConfiguration.region,
                    bucket = userFilesConfiguration.bucket,
                    key = "${userFilesConfiguration.path}/${accountRegisterDataMissingAcceptedAtWithRG.accountId.value}/${userFilesConfiguration.kycPrefix}${now.toEpochSecond()}.pdf",
                )

                val slot = slot<ByteArray>()

                verify(exactly = 1) {
                    bigDataServiceMock.getKycDossier(accountRegisterDataMissingAcceptedAtWithRG.documentInfo!!.cpf)
                    handlebarTemplateCompilerMock.buildHtml(any())
                    pdfConverterMock.convert(builtHtml, false)
                    objectRepository.putObject(
                        storedObject,
                        capture(slot),
                        any(),
                    )
                    faceMatcherMock.match(any(), any())
                }

                String(slot.captured) shouldBe String(pdfmockcontent)
            }
        }

        @Test
        fun `should call services and upload kyc document`() {
            val now = getZonedDateTime()

            withGivenDateTime(now) {
                service.generate(accountRegisterData)

                val storedObject = StoredObject(
                    region = userFilesConfiguration.region,
                    bucket = userFilesConfiguration.bucket,
                    key = "${userFilesConfiguration.path}/${accountRegisterData.accountId.value}/${userFilesConfiguration.kycPrefix}${now.toEpochSecond()}.pdf",
                )

                val slot = slot<ByteArray>()

                verify(exactly = 1) {
                    bigDataServiceMock.getKycDossier(accountRegisterData.documentInfo!!.cpf)
                    handlebarTemplateCompilerMock.buildHtml(any())
                    pdfConverterMock.convert(builtHtml, false)
                    objectRepository.putObject(
                        storedObject,
                        capture(slot),
                        any(),
                    )
                    faceMatcherMock.match(any(), any())
                }

                String(slot.captured) shouldBe String(pdfmockcontent)
            }
        }

        @Test
        fun `should return left when big data service throws an error`() {
            every { bigDataServiceMock.getKycDossier(accountRegisterData.documentInfo!!.cpf) } returns NoStackTraceException(
                "teste kyc",
            ).left()

            val result = service.generate(accountRegisterData)
            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<KycGenerationException>()
            }

            verify {
                handlebarTemplateCompilerMock wasNot called
                objectRepository wasNot called
            }
        }

        @Test
        fun `should return right and build template when face match throws an error`() {
            every { faceMatcherMock.match(any(), any()) } returns NoStackTraceException("teste").left()

            val result = service.generate(accountRegisterData)
            result.isRight() shouldBe true

            verify(exactly = 1) {
                handlebarTemplateCompilerMock.buildHtml(any())
            }
        }

        @Test
        fun `should return left when kyc file builder throws an error`() {
            every { handlebarTemplateCompilerMock.buildHtml(any()) } throws NoStackTraceException("teste")

            val result = service.generate(accountRegisterData)
            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<KycGenerationException>()
            }

            verify(exactly = 0) {
                objectRepository.putObject(any(), any(), any())
            }
        }

        @Test
        fun `should return left when saving kyc file throws an error`() {
            every { objectRepository.putObject(any(), any(), any()) } throws NoStackTraceException("teste")

            val result = service.generate(accountRegisterData)
            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<KycGenerationException>()
            }
        }
    }

    @Nested
    @DisplayName("on SimpleSignUpKycDossier build")
    inner class BuildSimpleSignUpKycDossier {

        private val accountRegisterDataForSimpleSignUp = AccountRegisterData(
            accountId = AccountId("ACCOUNT-SIMPLE-SIGN-UP"),
            emailAddress = EmailAddress(email = "<EMAIL>"),
            nickname = "nickname",
            mobilePhone = MobilePhone("+*************"),
            mobilePhoneVerified = true,
            livenessId = LivenessId("LIVENESS-ID"),
            externalId = ExternalId("ACCOUNT-MODATTA", AccountProviderName.MODATTA),
            documentInfo = DocumentInfo(
                name = "name",
                cpf = "***********",
                birthDate = LocalDate.now(),
                fatherName = "",
                motherName = "",
                rg = "",
                docType = DocumentType.CPF,
                cnhNumber = null,
                orgEmission = "SRF",
                expeditionDate = null,
                birthCity = "Rio",
                birthState = "RJ, ES",
            ),

            created = ZonedDateTime.now(),
            lastUpdated = ZonedDateTime.now(),
            uploadedSelfie = StoredObject(region = "selfie-region", bucket = "selfie-region", key = "selfie-region"),
            monthlyIncome = MonthlyIncome(lowerBound = 0, upperBound = 2_000_00),
            calculatedGender = null,

            isDocumentEdited = false,
            openForUserReview = false,
            openedForUserReviewAt = null,
            riskAnalysisFailedReasons = null,
            riskAnalysisFailedReasonsHistory = null,
            registrationType = RegistrationType.BASIC,
            fraudListMatch = false,
            politicallyExposed = PoliticallyExposed(
                selfDeclared = false,
                query = PepQuery(at = ZonedDateTime.now(), result = PepQueryResult.NOT_PEP),
            ),
        )

        @Test
        fun `should call services and upload kyc without document`() {
            val now = getZonedDateTime()

            withGivenDateTime(now) {
                service.generate(accountRegisterDataForSimpleSignUp)

                val storedObject = StoredObject(
                    region = userFilesConfiguration.region,
                    bucket = userFilesConfiguration.bucket,
                    key = "${userFilesConfiguration.path}/${accountRegisterDataForSimpleSignUp.accountId.value}/${userFilesConfiguration.kycPrefix}${now.toEpochSecond()}.pdf",
                )

                val slot = slot<ByteArray>()

                verify(exactly = 1) {
                    bigDataServiceMock.getKycDossier(
                        accountRegisterDataForSimpleSignUp.documentInfo!!.cpf,
                    )
                    handlebarTemplateCompilerMock.buildHtml(any())
                    pdfConverterMock.convert(builtHtml, false)
                    objectRepository.putObject(
                        storedObject,
                        capture(slot),
                        any(),
                    )
                    faceMatcherMock wasNot Called
                }

                String(slot.captured) shouldBe String(pdfmockcontent)
            }
        }

        @Test
        fun `should return left when big data service throws an error fetching kyc dossier`() {
            every {
                bigDataServiceMock.getKycDossier(
                    accountRegisterDataForSimpleSignUp.documentInfo!!.cpf,
                )
            } returns NoStackTraceException(
                "teste kyc",
            ).left()

            val result = service.generate(accountRegisterDataForSimpleSignUp)
            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<KycGenerationException>()
            }

            verify {
                handlebarTemplateCompilerMock wasNot called
                objectRepository wasNot called
            }
        }

        @Test
        fun `should return left when kyc file builder throws an error`() {
            every { handlebarTemplateCompilerMock.buildHtml(any()) } throws NoStackTraceException("teste")

            val result = service.generate(accountRegisterDataForSimpleSignUp)
            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<KycGenerationException>()
            }

            verify(exactly = 0) {
                objectRepository.putObject(any(), any(), any())
            }
        }

        @Test
        fun `should return left when saving kyc file throws an error`() {
            every { objectRepository.putObject(any(), any(), any()) } throws NoStackTraceException("teste")

            val result = service.generate(accountRegisterDataForSimpleSignUp)
            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<KycGenerationException>()
            }
        }
    }
}