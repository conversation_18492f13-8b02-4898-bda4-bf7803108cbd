package ai.friday.billpayment.app.register.instrumentation

import ai.friday.billpayment.app.account.Gender
import io.kotest.matchers.shouldBe
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

internal class RegisterUtilTest {

    @ParameterizedTest
    @CsvSource(value = ["Xênia <PERSON>,F", "<PERSON>,<PERSON>", "<PERSON><PERSON><PERSON>,<PERSON>", "Graça das Dores,F", "Niña La,F", "Àvila Avido,F", "ZURMA Zelia,F"])
    fun `should return gender for name`(name: String, expectedGender: Gender) {
        val gender = RegisterUtil.findGenderByName(name)
        gender shouldBe expectedGender
    }
}