package ai.friday.billpayment.app.register

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.accountRegisterCompleted
import ai.friday.billpayment.accountRegisterData
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.OriginalOcrAndPersonDataDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountClosureDetails
import ai.friday.billpayment.app.account.AccountClosureReason
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountRegisterService
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.CloseAccountService
import ai.friday.billpayment.app.account.PartialAccount
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.UpgradeStatus
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.backoffice.BackofficeRegisterService
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.integrations.RegisterInstrumentationService
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.liveness.LivenessService
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.date.withGivenDateTime
import arrow.core.Either
import arrow.core.right
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.spyk
import io.mockk.verify
import java.io.InputStream
import java.time.ZonedDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class BackofficeRegisterServiceTest {

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val objectRepository: ObjectRepository = mockk {
        every { putObject(any(), any(), any()) } just runs
        every {
            loadObject(any(), any())
        } returns InputStream.nullInputStream()
    }

    private val dynamoDbEnhancedClient = getDynamoDB()
    private val accountRegisterDAO = AccountRegisterDynamoDAO(dynamoDbEnhancedClient)
    private val originalOcrAndPersonDataDAO = OriginalOcrAndPersonDataDynamoDAO(dynamoDbEnhancedClient)
    private val accountRegisterRepository = spyk(
        AccountRegisterDbRepository(accountRegisterDAO, originalOcrAndPersonDataDAO, objectRepository),
    )
    private val notificationAdapter: NotificationAdapter = mockk()
    private val registerInstrumentationService: RegisterInstrumentationService = mockk() {
        every { completed(any(), any()) } just Runs
    }

    private val accountDAO = AccountDynamoDAO(dynamoDbEnhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(dynamoDbEnhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(dynamoDbEnhancedClient)
    private val nsuDAO = NSUDynamoDAO(dynamoDbEnhancedClient)
    private val transactionDynamo = TransactionDynamo(dynamoDbEnhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val crmServiceMock = mockk<CrmService>(relaxUnitFun = true)

    private val accountService =
        AccountService(
            accountConfigurationService = mockk(),
            accountRepository = accountRepository,
            chatbotMessagePublisher = mockk(),
            crmService = crmServiceMock,
            notificationAdapter = notificationAdapter,
            walletRepository = mockk(),
        )

    private val livenessService: LivenessService = mockk(relaxed = true) {
        every {
            match(accountId = any())
        } returns Either.Right(LivenessId("LIVENESS_ID-45ec6992-151c-42cf-8d33-8c8d0a425429"))
    }

    private val testMobilePhone = MobilePhone("+*************")
    private val accountRegisterService = AccountRegisterService(
        accountRegisterRepository = accountRegisterRepository,
        livenessService = mockk(),
        userFilesConfiguration = mockk(),
    ).apply {
        signUpMobilePhones = listOf(testMobilePhone.msisdn)
    }

    private val closeAccountService = CloseAccountService(
        accountService = accountService,
        accountRegisterRepository = accountRegisterRepository,
        accountRegisterService = accountRegisterService,
        walletService = mockk(relaxed = true),
        backOfficeAccountService = mockk(),
        loginService = mockk(),
        transactionRepository = mockk(),
        balanceService = mockk(),
        crmService = crmServiceMock,
        subscriptionService = mockk(),
        livenessService = livenessService,
        statementService = mockk(),
        closeAccountStepExecutors = emptyList(),
        messagePublisher = mockk(),
        closeAccountQueueName = "close_Account",
        onboardingTestPixService = mockk(),
        externalAccountService = mockk(), closeAccountStepDiscovery = listOf(), closeWalletStepDiscovery = listOf(),

    )

    private val backofficeRegisterService: BackofficeRegisterService =
        BackofficeRegisterService(
            accountRegisterRepository = accountRegisterRepository,
            accountService = accountService,
            notificationAdapter = notificationAdapter,
            registerInstrumentationService = registerInstrumentationService,
            closeAccountService = closeAccountService,
            accountRepository = accountRepository,
            livenessService = livenessService,
            accountRegisterService = accountRegisterService,
            deeplinkUrl = "https://deeplinkUrl/",
        )

    private lateinit var accountId: AccountId
    private lateinit var otherAccountId: AccountId

    private lateinit var partialAccount: PartialAccount
    private lateinit var otherPartialAccount: PartialAccount

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        partialAccount = accountRepository.create(
            "fake_user",
            EmailAddress("<EMAIL>"),
            registrationType = RegistrationType.BASIC,
        )
        otherPartialAccount = accountRepository.create(
            "fake_user",
            EmailAddress("<EMAIL>"),
            registrationType = RegistrationType.BASIC,
        )

        accountId = partialAccount.id
        otherAccountId = otherPartialAccount.id
        accountRegisterRepository.save(accountRegisterData.copy(accountId = accountId))
    }

    @Test
    fun `deve reabir upgrade para revisão do usuário`() {
        val account = ACCOUNT.copy(
            accountId = AccountId("basic-account-id"),
            status = AccountStatus.ACTIVE,
            type = UserAccountType.BASIC_ACCOUNT,
            upgradeStatus = UpgradeStatus.UNDER_REVIEW,
        )

        accountRepository.save(account)

        accountRegisterRepository.save(
            accountRegisterCompleted.copy(
                accountId = account.accountId,
                upgradeStarted = getZonedDateTime(),
                upgradeAgreementData = accountRegisterCompleted.agreementData,
            ),
        )

        val timestamp = ZonedDateTime.parse("2023-01-02 03:04:05", dateTimeFormat)

        withGivenDateTime(timestamp) {
            val result = backofficeRegisterService.reopenUpgrade(account.accountId)

            result.isRight() shouldBe true
        }

        with(accountRepository.findById(account.accountId)) {
            upgradeStatus shouldBe UpgradeStatus.INCOMPLETE
        }

        with(accountRegisterRepository.findByAccountId(account.accountId)) {
            openForUserReview shouldBe true
            openedForUserReviewAt shouldBe timestamp
            upgradeAgreementData shouldBe null
        }
    }

    @Test
    fun `não deve reabir upgrade se não estiver sob revisão`() {
        val account = ACCOUNT.copy(
            accountId = AccountId("basic-account-id"),
            status = AccountStatus.ACTIVE,
            type = UserAccountType.BASIC_ACCOUNT,
            upgradeStatus = UpgradeStatus.COMPLETED,
        )

        accountRepository.save(account)

        accountRegisterRepository.save(
            accountRegisterCompleted.copy(
                accountId = account.accountId,
                upgradeStarted = getZonedDateTime(),
                upgradeAgreementData = accountRegisterCompleted.agreementData,
            ),
        )

        val result = backofficeRegisterService.reopenUpgrade(account.accountId)

        result.isLeft() shouldBe true

        with(accountRepository.findById(account.accountId)) {
            upgradeStatus shouldBe UpgradeStatus.COMPLETED
        }

        with(accountRegisterRepository.findByAccountId(account.accountId)) {
            openForUserReview shouldBe false
            upgradeAgreementData shouldBe accountRegisterCompleted.agreementData
        }
    }

    @Nested
    @DisplayName("ao negar um conta")
    inner class DenyRegister {

        lateinit var underReviewAccountId: AccountId

        @BeforeEach
        fun setup() {
            every { notificationAdapter.notifyRegisterDenied(any(), any()) } just Runs
            every { registerInstrumentationService.rejected(any(), any(), any()) } just Runs
            val underReviewAccount = accountRepository.create(
                "under_review_user",
                EmailAddress("<EMAIL>"),
                registrationType = RegistrationType.UPGRADED,
            )

            underReviewAccountId = underReviewAccount.id

            accountRegisterRepository.save(accountRegisterCompleted.copy(accountId = underReviewAccount.id))
            accountRepository.updatePartialAccountStatus(underReviewAccount.id, AccountStatus.UNDER_REVIEW)
        }

        @Nested
        @DisplayName("e o motivo é fraude")
        inner class DenyFraudRegister {

            private val fraudClosureDetails = AccountClosureDetails.create(
                reason = AccountClosureReason.FRAUD,
                description = "Roubou pão na fila do pão",
                at = ZonedDateTime.of(2023, 1, 1, 0, 0, 0, 0, brazilTimeZone),
            )

            @Test
            fun `deve fazer o encerramento de conta`() {
                every { livenessService.markAsFraud(any()) } returns Unit.right()
                backofficeRegisterService.denyAccount(underReviewAccountId, fraudClosureDetails).isRight().shouldBeTrue()

                accountRegisterRepository.findByAccountId(
                    underReviewAccountId,
                    false,
                ).accountClosureDetails shouldBe fraudClosureDetails

                accountRepository.findPartialAccountById(underReviewAccountId).status shouldBe AccountStatus.DENIED

                verify {
                    crmServiceMock.removeContactAsync(underReviewAccountId)
                }
            }

            @Test
            fun `deve marcar o rosto do usuário como fraude`() {
                every { livenessService.markAsFraud(any()) } returns Unit.right()
                backofficeRegisterService.denyAccount(underReviewAccountId, fraudClosureDetails).isRight().shouldBeTrue()

                verify {
                    livenessService.markAsFraud(underReviewAccountId)
                }
            }
        }

        @Nested
        @DisplayName("e o motivo não é fraude")
        inner class DenyNotFraudRegister {
            private val notFraudClosureDetails = AccountClosureDetails.create(
                reason = null,
                description = "Não fui com a cara dele",
                at = ZonedDateTime.of(2023, 1, 1, 0, 0, 0, 0, brazilTimeZone),
            )

            @Test
            fun `deve fazer o encerramento de conta`() {
                backofficeRegisterService.denyAccount(underReviewAccountId, notFraudClosureDetails).isRight().shouldBeTrue()

                accountRegisterRepository.findByAccountId(
                    underReviewAccountId,
                    false,
                ).accountClosureDetails shouldBe notFraudClosureDetails
                accountRepository.findPartialAccountById(underReviewAccountId).status shouldBe AccountStatus.DENIED

                verify {
                    crmServiceMock.removeContactAsync(underReviewAccountId)
                }
            }

            @Test
            fun `não deve marcar o rosto como fraude`() {
                backofficeRegisterService.denyAccount(underReviewAccountId, notFraudClosureDetails)

                verify(exactly = 0) {
                    livenessService.markAsFraud(underReviewAccountId)
                }
            }
        }

        @Test
        fun `deve gerar o link para refazer o liveness corretamente`() {
            backofficeRegisterService.getUrlToUpdateLiveness(AccountId(ACCOUNT_ID)) shouldBe "https://deeplinkUrl/liveness/cadastrar/TElWRU5FU1NfSUQtNDVlYzY5OTItMTUxYy00MmNmLThkMzMtOGM4ZDBhNDI1NDI5"
        }
    }
}