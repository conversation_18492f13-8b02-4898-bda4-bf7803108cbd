package ai.friday.billpayment.app.register

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.instrumentation.RegisterInstrumentationRepository
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.register.instrumentation.DefaultRegisterInstrumentationService
import ai.friday.billpayment.app.register.instrumentation.RegisterInstrumentationEvent
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.WalletFixture
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow

class DefaultRegisterInstrumentationServiceTest {

    private val walletFixture = WalletFixture()
    private val instrumentationRepositoryMock = mockk<RegisterInstrumentationRepository>(relaxUnitFun = true)
    private val billRepositoryMock = mockk<BillRepository>() {
        every {
            <EMAIL>(any())
        } returns listOf(
            getActiveBill(),
            getActiveBill(),
        )
    }

    private val walletRepositoryMock = mockk<WalletRepository>() {
        every {
            findWallets(any(), MemberStatus.ACTIVE)
        } returns listOf(walletFixture.buildWallet(), walletFixture.buildWallet())
    }

    private val registerInstrumentationService =
        DefaultRegisterInstrumentationService(
            instrumentationRepositoryMock,
            billRepositoryMock,
            walletRepository = walletRepositoryMock,
        )

    @Test
    fun `should not throw exception when repository fails publishing any event`() {
        every {
            instrumentationRepositoryMock.publishEvent(any())
        } throws NoStackTraceException("Fake error")

        assertDoesNotThrow {
            registerInstrumentationService.completed(
                accountId = AccountId(ACCOUNT_ID),
                registrationType = RegistrationType.BASIC,
            )
            registerInstrumentationService.rejected(
                accountId = AccountId(ACCOUNT_ID),
                registrationType = RegistrationType.BASIC,
                rejectedReason = emptyList(),
            )
            registerInstrumentationService.reopened(
                accountId = AccountId(ACCOUNT_ID),
                registrationType = RegistrationType.BASIC,
                deniedReason = emptyList(),
            )
            registerInstrumentationService.externallyRejected(
                accountId = AccountId(ACCOUNT_ID),
                registrationType = RegistrationType.BASIC,
            )
            registerInstrumentationService.activated(
                accountId = AccountId(ACCOUNT_ID),
                registrationType = RegistrationType.BASIC,
            )
        }
    }

    @Test
    fun `should send activated event`() {
        registerInstrumentationService.activated(
            ACCOUNT.accountId,
            registrationType = RegistrationType.BASIC,
        )

        val accountActivated = slot<RegisterInstrumentationEvent.AccountActivated>()

        verify {
            instrumentationRepositoryMock.publishEvent(capture(accountActivated))
        }

        with(accountActivated.captured) {
            this.accountId shouldBe ACCOUNT.accountId
            this.billsCount shouldBe 4
        }
    }
}