package ai.friday.billpayment.app.mailbox

import DynamoDBUtils
import ai.friday.billpayment.adapters.dynamodb.GlobalDataDbRepository
import ai.friday.billpayment.adapters.dynamodb.GlobalDataDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.MailboxGlobalDataDbRepository
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class DefaultMailboxGlobalBlockListServiceTest {

    private val enhancedClient = DynamoDBUtils.setupDynamoDB()
    private val globalDataDAO = GlobalDataDynamoDAO(enhancedClient)
    private val globalDataRepository = GlobalDataDbRepository(globalDataDAO)
    private val repository = MailboxGlobalDataDbRepository(globalDataRepository)
    private val service = DefaultMailboxListsService(repository)

    @BeforeEach
    fun setup() {
        repository.saveBlockList(listOf("<EMAIL>"))
    }

    @Test
    fun `deve remover o email na BlockList`() {
        service.deleteGlobal(MailboxListType.BLOCKED, "<EMAIL>")
        repository.loadBlockList().shouldBeEmpty()
    }

    @Test
    fun `deve adicionar o email na BlockList`() {
        repository.loadBlockList().shouldHaveSize(1)
        service.putGlobal(MailboxListType.BLOCKED, listOf("<EMAIL>"))
        with(repository.loadBlockList()) {
            this.shouldHaveSize(2)
            this.first().shouldBe("<EMAIL>")
            this.last().shouldBe("<EMAIL>")
        }
    }
}