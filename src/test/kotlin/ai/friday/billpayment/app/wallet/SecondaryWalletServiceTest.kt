package ai.friday.billpayment.app.wallet

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.accountRegisterData
import ai.friday.billpayment.accountRegisterDataWithAddress
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.OriginalOcrAndPersonDataDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.adapters.micronaut.WalletConfigurationMicronaut
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.MonthlyIncome
import ai.friday.billpayment.app.account.PoliticallyExposed
import ai.friday.billpayment.app.account.RegisterNaturalPersonResponse
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.ExternalAccountRegister
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.RegisterPixKeyCommand
import ai.friday.billpayment.cnhDocumentInfo
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.internalBankAccount
import ai.friday.billpayment.mobilePhone
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.Duration
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

@MicronautTest
class SecondaryWalletServiceTest {

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDAO = DynamoDbDAO(dynamoDB)
    private val enhancedClient = getDynamoDB()
    private val accountDbRepository: AccountDbRepository = mockk()

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletDbRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = accountDbRepository,
    )
    private val externalAccountRegister: ExternalAccountRegister = mockk(relaxed = true)
    private val objectRepository: ObjectRepository = mockk(relaxed = true)
    private val accountRegisterDAO = AccountRegisterDynamoDAO(enhancedClient)
    private val originalOcrAndPersonDataDAO = OriginalOcrAndPersonDataDynamoDAO(enhancedClient)
    private val accountRegisterRepository = AccountRegisterDbRepository(accountRegisterDAO, originalOcrAndPersonDataDAO, objectRepository)

    private val walletBillCategoryService = mockk<PFMWalletCategoryService>(relaxed = true)

    private val pixKeyDomain = "fake.io"

    private val messagePublisherMock = mockk<MessagePublisher>(relaxed = true)

    private val eventPublisher = mockk<EventPublisher>(relaxUnitFun = true)

    private val service = SecondaryWalletService(
        walletService = WalletService(
            accountService = mockk(),
            walletRepository = walletDbRepository,
            configuration = mockk(),
            notificationAdapter = mockk(),
            eventPublisher = mockk(),
            crmService = mockk(),
            pixKeyRepository = mockk(),
        ).apply { defaultPixKeyDomain = pixKeyDomain },
        configuration = WalletConfigurationMicronaut(
            maxOpenInvites = 10,
            inviteExpiration = Duration.ZERO,
        ),
        accountRepository = accountDbRepository,
        externalAccountRegister = externalAccountRegister,
        accountRegisterRepository = accountRegisterRepository,
        walletBillCategoryService = walletBillCategoryService,
        messagePublisher = messagePublisherMock,
        registerPixKeyQueueName = "fila",
        eventPublisher = eventPublisher,
        closeAccountService = mockk(),
    )

    private val accountPaymentMethod = AccountPaymentMethod(
        id = AccountPaymentMethodId(value = "payment-method-id"),
        status = AccountPaymentMethodStatus.ACTIVE,
        method = internalBankAccount,
        created = null,
        accountId = ACCOUNT.accountId,
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        accountRegisterRepository.save(
            accountRegisterData.copy(
                accountId = ACCOUNT.accountId,
                documentInfo = cnhDocumentInfo,
                address = accountRegisterDataWithAddress.address,
                mobilePhone = mobilePhone,
                politicallyExposed = PoliticallyExposed(false, null),
                monthlyIncome = MonthlyIncome(100000, ********),
                registrationType = RegistrationType.FULL,
            ),
        )
    }

    @Test
    fun `deve criar uma carteira secundaria`() {
        every { externalAccountRegister.registerNaturalPerson(any()) } returns RegisterNaturalPersonResponse(
            success = true,
            accountNumber = 1234,
            accountDv = "1",
        )

        every { accountDbRepository.findAccountPaymentMethodsByAccountId(any()) } returns listOf(accountPaymentMethod)

        every {
            accountDbRepository.createAccountPaymentMethod(
                accountId = any(),
                bankAccount = any(),
                position = any(),
                mode = any(),
            )
        } returns (accountPaymentMethod.copy(id = AccountPaymentMethodId("payment-method-id-new")))

        val walletName = "férias em palhoça"

        val wallet = service.create(ACCOUNT, walletName)

        wallet.isRight() shouldBe true

        wallet.map { it: Wallet ->
            it.paymentMethodId shouldBe AccountPaymentMethodId(value = "payment-method-id-new")
            it.name shouldBe walletName
        }

        verify { externalAccountRegister.registerNaturalPerson(any()) }

        verify(exactly = 1) {
            eventPublisher.publish(ofType<MemberAdded>())
            accountDbRepository.createAccountPaymentMethod(
                accountId = any(),
                bankAccount = any(),
                position = any(),
                mode = any(),
            )
        }

        val commands = mutableListOf<RegisterPixKeyCommand>()

        verify {
            messagePublisherMock.sendMessage(any(), capture(commands))
            walletBillCategoryService.createDefaultWalletCategories(any())
        }

        commands[0].key.value shouldBe "ferias.em.palhoca.${ACCOUNT.document}@$pixKeyDomain"
        commands[1].key.type shouldBe PixKeyType.EVP
    }

    @Test
    fun `deve criar a carteira secundaria sem criar uma conta no arbi se o numero ja existir`() {
        every { accountDbRepository.findAccountPaymentMethodsByAccountId(any()) } returns listOf(accountPaymentMethod)

        every {
            accountDbRepository.createAccountPaymentMethod(
                accountId = any(),
                bankAccount = any(),
                position = any(),
                mode = any(),
            )
        } returns (accountPaymentMethod.copy(id = AccountPaymentMethodId("payment-method-id-new")))

        val walletName = "férias em palhoça"

        val existingAccountNumber = AccountNumber(fullAccountNumber = "123456")
        val wallet = service.create(ACCOUNT, walletName, existingAccountNumber)

        wallet.isRight() shouldBe true

        wallet.map { it: Wallet ->
            it.paymentMethodId shouldBe AccountPaymentMethodId(value = "payment-method-id-new")
            it.name shouldBe walletName
        }

        verify(exactly = 0) { externalAccountRegister.registerNaturalPerson(any()) }

        verify(exactly = 1) {
            accountDbRepository.createAccountPaymentMethod(
                accountId = any(),
                bankAccount = any(),
                position = any(),
                mode = any(),
            )
        }

        val commands = mutableListOf<RegisterPixKeyCommand>()

        verify {
            messagePublisherMock.sendMessage(any(), capture(commands))
        }

        commands[0].key.value shouldBe "ferias.em.palhoca.${ACCOUNT.document}@$pixKeyDomain"
        commands[0].accountNo shouldBe existingAccountNumber
    }

    @Test
    fun `não deve criar carteira quando a criação de conta no arbi falhar`() {
        every { externalAccountRegister.registerNaturalPerson(any()) } returns RegisterNaturalPersonResponse(
            success = false,
            accountNumber = null,
            accountDv = null,
        )

        every { accountDbRepository.findAccountPaymentMethodsByAccountId(any()) } returns listOf(accountPaymentMethod)

        val walletName = "férias em palhoça"

        val wallet = service.create(ACCOUNT, walletName)

        wallet.isLeft() shouldBe true
        wallet.mapLeft {
            it.shouldBeTypeOf<CreateWalletErrors.RegisterNaturalPersonError>()
        }
    }

    @Test
    fun `não deve criar carteira quando o cadastro for simplificado`() {
        every { externalAccountRegister.registerNaturalPerson(any()) } returns RegisterNaturalPersonResponse(
            success = false,
            accountNumber = null,
            accountDv = null,
        )

        every { accountDbRepository.findAccountPaymentMethodsByAccountId(any()) } returns listOf(accountPaymentMethod)

        val walletName = "férias em palhoça"

        val wallet = service.create(ACCOUNT.copy(accountId = AccountId(ACCOUNT_ID_2), type = UserAccountType.BASIC_ACCOUNT), walletName)

        wallet.isLeft() shouldBe true
        wallet.mapLeft {
            it.shouldBeTypeOf<CreateWalletErrors.BasicAccountNotAllowed>()
        }
    }

    @Test
    fun `não deve criar carteira quando numero de carteiras do usuário for maior ou igual a 5`() {
        val walletFixture = WalletFixture(founderAccountId = ACCOUNT.accountId)

        repeat(5) {
            val wallet = walletFixture.buildWallet(
                walletFounder = walletFixture.founder,
                name = "Carteira $it",
                id = WalletId("WALLET-$it"),
                otherMembers = listOf(walletFixture.assistant),
            )
            walletDbRepository.save(wallet)
        }
        every { accountDbRepository.findAccountPaymentMethodsByAccountId(any()) } returns listOf(accountPaymentMethod)

        val walletName = "férias em palhoça"

        val wallet = service.create(walletFixture.founderAccount, walletName)

        wallet.isLeft() shouldBe true
        wallet.mapLeft {
            it.shouldBeTypeOf<CreateWalletErrors.MaxWalletsAllowed>()
        }
    }
}