package ai.friday.billpayment.app.wallet

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.EMAIL_ADDRESS
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.adapters.micronaut.WalletConfigurationMicronaut
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.billScheduleCanceled
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.pixKeyPaid
import ai.friday.billpayment.pixKeyPaymentStarted
import ai.friday.billpayment.pixPaymentScheduled
import ai.friday.billpayment.pixPaymentScheduledCanceled
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.mockk
import java.time.Duration
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class WalletServiceTest {

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val enhancedClient = getDynamoDB()

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletDbRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = mockk(),
    )
    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(enhancedClient),
        refundedClient = RefundedBillDynamoDAO(enhancedClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(enhancedClient),
    )

    private val walletId: WalletId = WalletId(WALLET_ID)

    private val walletFixture = WalletFixture()

    private val service = WalletService(
        accountService = mockk(),
        walletRepository = walletDbRepository,
        notificationAdapter = mockk(relaxed = true),
        eventPublisher = mockk(relaxed = true),
        configuration = WalletConfigurationMicronaut(
            maxOpenInvites = 10,
            inviteExpiration = Duration.ZERO,
        ),
        crmService = mockk(),
        pixKeyRepository = mockk(),
    )

    private val zonedDateTime = getZonedDateTime()
    private val today = zonedDateTime.toInstant().toEpochMilli()

    val bill = Bill.build(
        billAdded.copy(walletId = walletId, created = today - 4),
        billPaymentScheduled.copy(walletId = walletId, created = today - 3),
        billPaymentStart.copy(walletId = walletId, created = today - 2),
        billPaid.copy(walletId = walletId, created = today - 1, actionSource = ActionSource.Scheduled),
        billScheduleCanceled.copy(walletId = walletId, created = today),
    )

    private val pixCreated = zonedDateTime.minusDays(3).toInstant().toEpochMilli()

    private val pix = Bill.build(
        pixAdded.copy(walletId = walletId, created = pixCreated - 4),
        pixPaymentScheduled.copy(walletId = walletId, created = today - 3),
        pixKeyPaymentStarted.copy(walletId = walletId, created = today - 2),
        pixKeyPaid.copy(walletId = walletId, created = today - 1, actionSource = ActionSource.Scheduled),
        pixPaymentScheduledCanceled.copy(walletId = walletId, created = today),
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `should return 0 when there is no paid bill between dates`() {
        billRepository.getTotalPaid(
            walletId = walletId,
            startDate = getZonedDateTime().minusDays(1),
            endDate = getZonedDateTime(),
            types = listOf(BillType.INVOICE, BillType.PIX),
        ) shouldBe 0
    }

    @Test
    fun `should return bill amount when there is one paid bill between dates`() {
        billRepository.save(bill)

        billRepository.getTotalPaid(
            walletId = walletId,
            startDate = zonedDateTime.minusDays(1),
            endDate = zonedDateTime,
            types = listOf(BillType.CONCESSIONARIA),

        ) shouldBe bill.amountPaid
    }

    @Test
    fun `should return summed bill amount when there is more than one paid bill between dates`() {
        billRepository.save(bill)
        billRepository.save(pix)

        billRepository.getTotalPaid(
            walletId = walletId,
            startDate = zonedDateTime.minusDays(1),
            endDate = zonedDateTime,
            types = listOf(BillType.CONCESSIONARIA, BillType.PIX),

        ) shouldBe bill.amountPaid!!.plus(pix.amountPaid!!)
    }

    @Test
    fun `should return amount only for given types`() {
        billRepository.save(bill)
        billRepository.save(pix)

        billRepository.getTotalPaid(
            walletId = walletId,
            startDate = zonedDateTime.minusDays(1),
            endDate = zonedDateTime,
            types = listOf(BillType.PIX),

        ) shouldBe pix.amountPaid
    }

    @Test
    fun `deve salvar o e-mail no convite`() {
        val wallet = walletDbRepository.save(WalletFixture().buildWallet())

        service.createInvite(
            accountId = wallet.founder.accountId,
            walletId = wallet.id,
            document = DOCUMENT,
            name = "test",
            type = MemberType.ASSISTANT,
            email = EMAIL_ADDRESS,
            permissions = MemberPermissions.of(MemberType.ASSISTANT),
        )

        val invite = walletDbRepository.findInvite(walletId = wallet.id, memberDocument = DOCUMENT)

        invite.emailAddress shouldBe EMAIL_ADDRESS
    }

    @Test
    fun `não deve permitir criar convite um membro sem a permissão de manage members`() {
        val wallet = walletDbRepository.save(
            walletFixture.buildWallet(
                walletFounder = walletFixture.founder.copy(
                    permissions = walletFixture.founder.permissions.copy(
                        manageMembers = false,
                    ),
                ),
            ),
        )

        val result = service.createInvite(
            accountId = wallet.founder.accountId,
            walletId = wallet.id,
            document = DOCUMENT,
            name = "test",
            type = MemberType.ASSISTANT,
            email = EMAIL_ADDRESS,
            permissions = MemberPermissions.of(MemberType.ASSISTANT),
        )

        result.isLeft().shouldBeTrue()
        result.mapLeft {
            it.shouldBeTypeOf<InviteErrors.UserForbidden>()
        }
    }

    @Test
    fun `deve permitir criar convite um membro com a permissão de manage members`() {
        val wallet = walletDbRepository.save(
            WalletFixture().buildWallet(
                walletFounder = walletFixture.founder.copy(
                    permissions = walletFixture.founder.permissions.copy(
                        manageMembers = true,
                    ),
                ),
            ),
        )

        val result = service.createInvite(
            accountId = wallet.founder.accountId,
            walletId = wallet.id,
            document = DOCUMENT,
            name = "test",
            type = MemberType.ASSISTANT,
            email = EMAIL_ADDRESS,
            permissions = MemberPermissions.of(MemberType.ASSISTANT),
        )

        result.isRight().shouldBeTrue()
    }
}