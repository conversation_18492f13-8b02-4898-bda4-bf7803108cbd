package ai.friday.billpayment.app.wallet

import ai.friday.billpayment.integration.WalletFixture
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

internal class WalletTest {

    val walletFixture = WalletFixture()

    @Test
    fun `should return only active members`() {
        val wallet = walletFixture.buildWallet(
            otherMembers = listOf(
                walletFixture.participant,
                walletFixture.limitedParticipant,
                walletFixture.removedParticipant,
            ),
        )

        wallet.activeMembers shouldBe listOf(
            walletFixture.founder,
            walletFixture.participant,
            walletFixture.limitedParticipant,
        )
        wallet.allMembers shouldBe listOf(
            walletFixture.founder,
            walletFixture.participant,
            walletFixture.limitedParticipant,
            walletFixture.removedParticipant,
        )
    }
}