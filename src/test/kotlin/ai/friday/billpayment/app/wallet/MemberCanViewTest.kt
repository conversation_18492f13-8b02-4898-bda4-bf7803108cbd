package ai.friday.billpayment.app.wallet

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPermissionUpdated
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.DOCUMENT_2
import ai.friday.billpayment.integration.toMember
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class MemberCanViewTest {

    private val account = ACCOUNT.copy(
        document = DOCUMENT_2,
    )

    @Test
    fun `deve visualizar a bill quando o payer document da bill for igual ao document do member`() {
        val member = ACCOUNT.toMember(
            type = MemberType.COLLABORATOR,
            permissions = MemberPermissions(
                viewBills = BillPermission.NO_BILLS,
                scheduleBills = BillPermission.NO_BILLS,
                viewBalance = true,
                notification = true,
            ),
        )
        val bill = Bill.build(billAdded)
        member.canView(bill) shouldBe true
    }

    @Test
    fun `deve visualizar a bill quando o visibleBy da bill possuir o accountId do member`() {
        val member = account.toMember(
            type = MemberType.COLLABORATOR,
            permissions = MemberPermissions(
                viewBills = BillPermission.NO_BILLS,
                scheduleBills = BillPermission.NO_BILLS,
                viewBalance = true,
                notification = true,
            ),
        )
        val bill = Bill.build(billAdded, billPermissionUpdated)
        member.canView(bill) shouldBe true
    }

    @Test
    fun `deve visualizar a bill quando o member tiver permissao de visualizar todas as bills`() {
        val member = account.toMember(
            type = MemberType.COLLABORATOR,
            permissions = MemberPermissions(
                viewBills = BillPermission.ALL_BILLS,
                scheduleBills = BillPermission.NO_BILLS,
                viewBalance = true,
                notification = true,
            ),
        )
        val bill = Bill.build(billAdded)
        member.canView(bill) shouldBe true
    }

    @Test
    fun `deve visualizar a bill quando o member tiver permissao de visualizar apenas contas adicionadas por ele e foi`() {
        val member = account.toMember(
            type = MemberType.COLLABORATOR,
            permissions = MemberPermissions(
                viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                scheduleBills = BillPermission.NO_BILLS,
                viewBalance = true,
                notification = true,
            ),
        )
        val bill = Bill.build(
            billAdded.copy(
                actionSource = ActionSource.Api(accountId = member.accountId),
            ),
        )
        member.canView(bill) shouldBe true
    }

    @Test
    fun `nao deve visualizar a bill quando o member tiver permissao de visualizar apenas contas adicionadas por ele e mas nao foi`() {
        val member = account.toMember(
            type = MemberType.COLLABORATOR,
            permissions = MemberPermissions(
                viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                scheduleBills = BillPermission.NO_BILLS,
                viewBalance = true,
                notification = true,
            ),
        )
        val bill = Bill.build(
            billAdded.copy(
                actionSource = ActionSource.Api(accountId = AccountId(ACCOUNT_ID_2)),
            ),
        )
        member.canView(bill) shouldBe false
    }

    @Test
    fun `nao deve visualizar a bill quando o member tiver permissao de visualizar nenhuma bill`() {
        val member = account.toMember(
            type = MemberType.COLLABORATOR,
            permissions = MemberPermissions(
                viewBills = BillPermission.NO_BILLS,
                scheduleBills = BillPermission.NO_BILLS,
                viewBalance = true,
                notification = true,
            ),
        )
        val bill = Bill.build(billAdded)
        member.canView(bill) shouldBe false
    }

    @Test
    fun `deve visualizar a bill quando o member tiver permissao de visualizar bill por criterios mas os criterios estao nulos`() {
        val member = account.toMember(
            type = MemberType.COLLABORATOR,
            permissions = MemberPermissions(
                viewBills = BillPermission.BY_CRITERIA,
                criteriaForViewingBills = null,
                scheduleBills = BillPermission.NO_BILLS,
                viewBalance = true,
                notification = true,
            ),
        )
        val bill = Bill.build(
            billAdded.copy(
                actionSource = ActionSource.DDA(accountId = AccountId(ACCOUNT_ID_2)),
            ),
        )
        member.canView(bill) shouldBe false
    }

    @Test
    fun `nao deve visualizar a bill quando o member tiver permissao de visualizar bill por criterios mas os criterios estao com listas vazias`() {
        val member = account.toMember(
            type = MemberType.COLLABORATOR,
            permissions = MemberPermissions(
                viewBills = BillPermission.BY_CRITERIA,
                criteriaForViewingBills = null,
                scheduleBills = BillPermission.NO_BILLS,
                viewBalance = true,
                notification = true,
            ),
        )
        val bill = Bill.build(
            billAdded.copy(
                actionSource = ActionSource.DDA(accountId = AccountId(ACCOUNT_ID_2)),
            ),
        )
        member.canView(bill) shouldBe false
    }

    @Test
    fun `deve visualizar a bill quando o member tiver permissao de visualizar bill adicionadas por DDA e a bill for de DDA`() {
        val member = account.toMember(
            type = MemberType.COLLABORATOR,
            permissions = MemberPermissions(
                viewBills = BillPermission.BY_CRITERIA,
                criteriaForViewingBills = CriteriaForViewingBills(
                    criterias = listOf(ActionSourceCriteria("DDA")),
                ),
                scheduleBills = BillPermission.NO_BILLS,
                viewBalance = true,
                notification = true,
            ),
        )
        val bill = Bill.build(
            billAdded.copy(
                actionSource = ActionSource.DDA(accountId = AccountId(ACCOUNT_ID_2)),
            ),
        )
        member.canView(bill) shouldBe true
    }

    @ParameterizedTest
    @MethodSource("nonDDAActionSourcesThatHaveSourceName")
    fun `nao deve visualizar a bill quando o member tiver permissao de visualizar bill adicionadas por DDA e a bill nao for de DDA`(actionSource: ActionSource) {
        val member = account.toMember(
            type = MemberType.COLLABORATOR,
            permissions = MemberPermissions(
                viewBills = BillPermission.BY_CRITERIA,
                criteriaForViewingBills = CriteriaForViewingBills(
                    criterias = listOf(ActionSourceCriteria("DDA")),
                ),
                scheduleBills = BillPermission.NO_BILLS,
                viewBalance = true,
                notification = true,
            ),
        )
        val bill = Bill.build(
            billAdded.copy(
                actionSource = actionSource,
            ),
        )
        member.canView(bill) shouldBe false
    }

    companion object {

        @JvmStatic
        fun nonDDAActionSourcesThatHaveSourceName() = listOf(
            ActionSource.Api(accountId = AccountId(ACCOUNT_ID_2)),
            ActionSource.WalletMailBox(accountId = AccountId(ACCOUNT_ID_2), from = "<EMAIL>"),
            ActionSource.WalletRecurrence(AccountId(ACCOUNT_ID_2), RecurrenceId("123")),
            ActionSource.ConnectUtility(accountId = AccountId(ACCOUNT_ID_2)),
            ActionSource.VirtualAssistant(accountId = AccountId(ACCOUNT_ID_2)),
        )
    }
}