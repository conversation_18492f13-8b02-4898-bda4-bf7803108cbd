package ai.friday.billpayment.app.wallet

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

internal class DailyPaymentLimitResolverTest {
    val now = getZonedDateTime()

    @Test
    fun `when updatedAt is before 24 hours of activation should not modify dailyPaymentLimit if type does not allow`() {
        withGivenDateTime(now) {
            val dailyPaymentLimit = DailyPaymentLimit(
                updatedAt = now.minusDays(1).plusMinutes(1),
                lastAmount = 1_000_00,
                amount = 2_000_00,
                type = DailyPaymentLimitType.DAILY,
            )

            dailyPaymentLimit.activeAmount shouldBe 1_000_00
        }
    }

    @Test
    fun `when updatedAt is before 24 hours of activation should modify dailyPaymentLimit if type allows`() {
        withGivenDateTime(now) {
            val dailyPaymentLimit = DailyPaymentLimit(
                updatedAt = now.minusDays(1).plusMinutes(1),
                lastAmount = 1_000_00,
                amount = 2_000_00,
                type = DailyPaymentLimitType.WHATSAPP_PAYMENT,
            )

            dailyPaymentLimit.activeAmount shouldBe 2_000_00
        }
    }

    @ParameterizedTest
    @CsvSource(value = ["100000", "null"], nullValues = ["null"])
    fun `when updatedAt is after 24 hours of activation should modify dailyPaymentLimit`(lastAmount: Long?) {
        withGivenDateTime(now) {
            val dailyPaymentLimit = DailyPaymentLimit(
                updatedAt = now.minusDays(1).minusMinutes(1),
                lastAmount = lastAmount,
                amount = 2_000_00,
                type = DailyPaymentLimitType.DAILY,
            )

            dailyPaymentLimit.activeAmount shouldBe 2_000_00
            dailyPaymentLimit.nextAmount shouldBe null
        }
    }
}