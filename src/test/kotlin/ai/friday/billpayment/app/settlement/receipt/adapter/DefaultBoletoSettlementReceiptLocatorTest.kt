package ai.friday.billpayment.app.settlement.receipt.adapter

import ai.friday.billpayment.createTransaction
import io.kotest.matchers.shouldBe
import io.mockk.mockk
import org.junit.jupiter.api.Test

class DefaultBoletoSettlementReceiptLocatorTest {

    private val celcoinReceiptAdapter = mockk<GetBoletoSettlementReceiptAdapter>(relaxed = true)

    private val locator = DefaultBoletoSettlementReceiptLocator(
        celcoinReceiptAdapter = celcoinReceiptAdapter,
    )

    @Test
    fun `deve retornar o adapter da Celcoin`() {
        locator.get(transaction = createTransaction()) shouldBe celcoinReceiptAdapter
    }
}