package ai.friday.billpayment.app.settlement.receipt

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.payment.BoletoSettlementException
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.settlement.receipt.adapter.BoletoSettlementReceiptLocator
import ai.friday.billpayment.app.settlement.receipt.adapter.GetBoletoSettlementReceiptAdapter
import ai.friday.billpayment.app.settlement.receipt.adapter.GetBoletoSettlementReceiptResult
import ai.friday.billpayment.createTransaction
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test

class DefaultBoletoSettlementReceiptServiceTest {

    private val receiptAdapter = mockk<GetBoletoSettlementReceiptAdapter>(relaxed = true)

    private val locator = mockk<BoletoSettlementReceiptLocator>(relaxed = true) {
        every { get(any<Transaction>()) } returns receiptAdapter
    }

    private val service = DefaultBoletoSettlementReceiptService(
        boletoSettlementReceiptLocator = locator,
    )

    @Test
    fun `deve retornar o recibo em casos de sucesso`() {
        val transaction = createTransaction()

        every {
            receiptAdapter.getReceipt(transaction)
        } returns GetBoletoSettlementReceiptResult.Success(
            createdOn = "2023-12-10",
            transactionId = "123",
            authentication = "987",
            paymentPartnerName = "Original",
            gateway = FinancialServiceGateway.ORIGINAL,
        )

        val receipt = service.getReceipt(transaction = transaction)

        receipt.transactionId shouldBe "123"
        receipt.authentication shouldBe "987"
        receipt.paymentPartnerName shouldBe "Original"
        receipt.createdOn shouldBe "2023-12-10"
    }

    @Test
    fun `deve lancar exception em casos de erro`() {
        every {
            receiptAdapter.getReceipt(any())
        } returns GetBoletoSettlementReceiptResult.Error(
            gateway = FinancialServiceGateway.ORIGINAL,
            reason = "Erro",
            exception = NoStackTraceException("Erro"),
        )

        val exception = shouldThrow<BoletoSettlementException> {
            service.getReceipt(transaction = createTransaction())
        }

        exception.gateway shouldBe FinancialServiceGateway.ORIGINAL
        exception.message shouldBe "Erro"
        exception.cause.shouldBeTypeOf<NoStackTraceException>()
    }
}