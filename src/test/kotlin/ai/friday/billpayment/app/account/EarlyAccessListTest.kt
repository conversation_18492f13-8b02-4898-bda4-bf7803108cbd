package ai.friday.billpayment.app.account

import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class EarlyAccessListTest {

    @ParameterizedTest
    @MethodSource("getEarlyAccessAccounts")
    fun `should return true if account has early access`(accountId: AccountId) {
        accountId.hasEarlyAccess() shouldBe true
    }

    @Test
    fun `should return false if account has not early access`() {
        val accountId = AccountId("ACCOUNT-3dc5eb06-71c3-4442-8f9c-aaaaaaaaaaaa")
        accountId.hasEarlyAccess() shouldBe false
    }

    companion object {
        @JvmStatic
        fun getEarlyAccessAccounts() = earlyAccessAccounts.stream()
    }
}