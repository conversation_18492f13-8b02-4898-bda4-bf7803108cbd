package ai.friday.billpayment.app.account

import ai.friday.billpayment.adapters.converters.CustomS3LinkConverter
import kotlin.test.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Test

class CustomS3LinkTest {

    private val converter = CustomS3LinkConverter()

    @Test
    fun `should convert CustomS3Link to expected JSON format`() {
        // Given
        val customS3Link = CustomS3Link(
            bucket = "via1-user-documents",
            key = "user_documents/ACCOUNT-49099ed8-c5df-41d0-8de6-54688da369ec/CONTRACT_1658256500.pdf",
            region = "us-east-1",
        )

        // When
        val json = converter.convert(customS3Link)

        // Then
        val expectedJson = """{"s3":{"bucket":"via1-user-documents","key":"user_documents/ACCOUNT-49099ed8-c5df-41d0-8de6-54688da369ec/CONTRACT_1658256500.pdf","region":"us-east-1"}}"""
        assertEquals(expectedJson, json)
    }

    @Test
    fun `should unconvert JSON to CustomS3Link`() {
        // Given
        val json = """{"s3":{"bucket":"via1-user-documents","key":"user_documents/ACCOUNT-49099ed8-c5df-41d0-8de6-54688da369ec/CONTRACT_1658256500.pdf","region":"us-east-1"}}"""

        // When
        val customS3Link = converter.unconvert(json)

        // Then
        assertEquals("via1-user-documents", customS3Link.bucket)
        assertEquals("user_documents/ACCOUNT-49099ed8-c5df-41d0-8de6-54688da369ec/CONTRACT_1658256500.pdf", customS3Link.key)
        assertEquals("us-east-1", customS3Link.region)
    }

    @Test
    fun `should convert to StoredObject correctly`() {
        // Given
        val customS3Link = CustomS3Link(
            bucket = "test-bucket",
            key = "test-key",
            region = "us-west-2",
        )

        // When
        val storedObject = customS3Link.toStoredObject()

        // Then
        assertEquals("test-bucket", storedObject.bucket)
        assertEquals("test-key", storedObject.key)
        assertEquals("us-west-2", storedObject.region)
    }

    @Test
    fun `should create from StoredObject correctly`() {
        // Given
        val storedObject = StoredObject(
            region = "eu-west-1",
            bucket = "my-bucket",
            key = "my-key",
        )

        // When
        val customS3Link = CustomS3Link.fromStoredObject(storedObject)

        // Then
        assertEquals("my-bucket", customS3Link.bucket)
        assertEquals("my-key", customS3Link.key)
        assertEquals("eu-west-1", customS3Link.region)
    }

    @Test
    fun `should handle null region in StoredObject with default`() {
        // Given
        val storedObject = StoredObject(
            region = null,
            bucket = "my-bucket",
            key = "my-key",
        )

        // When
        val customS3Link = CustomS3Link.fromStoredObject(storedObject)

        // Then
        assertEquals("my-bucket", customS3Link.bucket)
        assertEquals("my-key", customS3Link.key)
        assertEquals("us-east-1", customS3Link.region) // default region
    }

    @Test
    fun `should create CustomS3Link directly`() {
        // When
        val customS3Link = CustomS3Link.create("ap-south-1", "direct-bucket", "direct-key")

        // Then
        assertEquals("direct-bucket", customS3Link.bucket)
        assertEquals("direct-key", customS3Link.key)
        assertEquals("ap-south-1", customS3Link.region)
    }

    @Test
    fun `should unconvert JSON with missing region using default`() {
        // Given - JSON without region field
        val json = """{"s3":{"bucket":"test-bucket","key":"test-key"}}"""

        // When
        val customS3Link = converter.unconvert(json)

        // Then
        assertEquals("test-bucket", customS3Link.bucket)
        assertEquals("test-key", customS3Link.key)
        assertEquals("us-east-1", customS3Link.region) // default region
    }

    @Test
    fun `should throw exception when unconverting JSON without bucket`() {
        // Given - JSON without bucket field
        val json = """{"s3":{"key":"test-key","region":"us-east-1"}}"""

        // When & Then
        assertThrows(IllegalArgumentException::class.java) {
            converter.unconvert(json)
        }
    }

    @Test
    fun `should throw exception when unconverting JSON without key`() {
        // Given - JSON without key field
        val json = """{"s3":{"bucket":"test-bucket","region":"us-east-1"}}"""

        // When & Then
        assertThrows(IllegalArgumentException::class.java) {
            converter.unconvert(json)
        }
    }
}