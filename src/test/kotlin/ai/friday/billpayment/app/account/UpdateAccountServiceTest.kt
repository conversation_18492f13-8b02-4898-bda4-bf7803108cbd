package ai.friday.billpayment.app.account

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.EMAIL_ADDRESS
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.accountRegisterCompleted
import ai.friday.billpayment.accountRegisterData
import ai.friday.billpayment.accountRegisterDataWithDocumentInfo
import ai.friday.billpayment.accountRegisterDataWithPhoneVerified
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.CrmContact
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.LoginRepository
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.journey.UserJourneyService
import ai.friday.billpayment.app.login.Login
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.toMember
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Runs
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.cognitoidentityprovider.model.InvalidParameterException

class UpdateAccountServiceTest {

    private val accountRepository: AccountRepository = mockk(relaxUnitFun = true)
    private val userPoolAdapter: UserPoolAdapter = mockk(relaxed = true)
    private val crmService: CrmService = mockk(relaxed = true)
    private val accountRegisterRepository: AccountRegisterRepository = mockk(relaxUnitFun = true) {
        every { findByVerifiedMobilePhone(any()) } returns emptyList()
    }
    private val loginRepository: LoginRepository = mockk(relaxUnitFun = true)
    private val userJourneyService: UserJourneyService = mockk(relaxUnitFun = true)
    private val walletRepository: WalletRepository = mockk(relaxUnitFun = true)

    private val updateAccountService = UpdateAccountService(
        accountRepository = accountRepository,
        userPoolAdapter = userPoolAdapter,
        crmService = crmService,
        accountRegisterRepository = accountRegisterRepository,
        loginRepository = loginRepository,
        userJourneyService = userJourneyService,
        walletRepository = walletRepository,
    )

    @Nested
    @DisplayName("ao atualizar o telefone")
    inner class UpdatePhoneNumber {
        @Test
        fun `deve retornar que telefone já existe quando um telefone estiver cadastrado`() {
            val newPhoneNumber = MobilePhone(msisdn = "+*************")
            every { accountRegisterRepository.findByVerifiedMobilePhone(newPhoneNumber) } returns
                listOf(accountRegisterDataWithPhoneVerified)

            val result = updateAccountService.updatePhoneNumber(
                accountId = AccountId(value = "USER-NOT-FOUND"),
                newPhoneNumber = newPhoneNumber,
                currentPhoneNumber = MobilePhone(msisdn = "+*************"),
            )
            result.isLeft() shouldBe true
            result.mapLeft { it.shouldBeTypeOf<UpdateAccountError.PhoneNumberAlreadyExists>() }
        }

        @Test
        fun `should return user not found when user does not exists`() {
            every {
                accountRepository.findById(any())
            } throws AccountNotFoundException(userId = "")

            val result = updateAccountService.updatePhoneNumber(
                accountId = AccountId(value = "USER-NOT-FOUND"),
                newPhoneNumber = MobilePhone(msisdn = "+*************"),
                currentPhoneNumber = MobilePhone(msisdn = "+*************"),
            )
            result.isLeft()
            result.mapLeft { it.shouldBeTypeOf<UpdateAccountError.AccountNotFound>() }
        }

        @Test
        fun `should return invalid phone number when phone number is not valid`() {
            every {
                accountRepository.findById(any())
            } returns ACCOUNT

            every {
                userPoolAdapter.updatePhoneNumber(any(), any())
            } throws InvalidParameterException.builder().build()

            val result = updateAccountService.updatePhoneNumber(
                accountId = AccountId(value = "USER-NOT-FOUND"),
                newPhoneNumber = MobilePhone(msisdn = "+*************"),
                currentPhoneNumber = MobilePhone(msisdn = "+*************"),
            )
            result.isLeft() shouldBe true
            result.mapLeft { it.shouldBeTypeOf<UpdateAccountError.InvalidPhoneNumber>() }
        }

        @Test
        fun `deve retornar sucesso ao enviar o telefone já registrado`() {
            every {
                accountRepository.findById(any())
            } returns ACCOUNT

            val result = updateAccountService.updatePhoneNumber(
                accountId = ACCOUNT.accountId,
                newPhoneNumber = MobilePhone(ACCOUNT.mobilePhone),
                currentPhoneNumber = MobilePhone(ACCOUNT.mobilePhone),
            )

            result.isRight() shouldBe true

            verify(exactly = 0) {
                userPoolAdapter.updatePhoneNumber(any(), any())
                accountRegisterRepository.save(any())
                accountRegisterRepository.findByAccountId(any())
                crmService.findContact(ofType(AccountId::class))
            }
        }

        @Test
        fun `deve retornar falha ao enviar o telefone atual errado`() {
            every {
                accountRepository.findById(any())
            } returns ACCOUNT

            val result = updateAccountService.updatePhoneNumber(
                accountId = ACCOUNT.accountId,
                newPhoneNumber = MobilePhone(msisdn = "+*************"),
                currentPhoneNumber = MobilePhone(msisdn = "+*************"),
            )

            verify(exactly = 0) {
                userPoolAdapter.updatePhoneNumber(any(), any())
                accountRegisterRepository.save(any())
                accountRegisterRepository.findByAccountId(any())
                crmService.findContact(ofType(AccountId::class))
            }

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeInstanceOf<UpdateAccountError.InvalidPhoneNumber>()
            }
        }

        @Test
        fun `should return error on error updating user pool`() {
            every {
                accountRepository.findById(any())
            } returns ACCOUNT

            every {
                userPoolAdapter.updatePhoneNumber(any(), any())
            } throws NoStackTraceException()

            val result = updateAccountService.updatePhoneNumber(
                accountId = AccountId(value = "USER-NOT-FOUND"),
                newPhoneNumber = MobilePhone(msisdn = "+*************"),
                currentPhoneNumber = MobilePhone(ACCOUNT.mobilePhone),
            )
            result.isLeft()
            result.mapLeft { it.shouldBeTypeOf<UpdateAccountError.Unknown>() }
        }

        @Test
        fun `should return success on mobile phone updated`() {
            val crmContact = CrmContact(
                accountId = AccountId(ACCOUNT_ID),
                emailAddress = EMAIL_ADDRESS,
                accountType = UserAccountType.FULL_ACCOUNT,
                subscriptionType = SubscriptionType.PIX,
            )
            val mobilePhone = MobilePhone("+*************")

            every {
                accountRepository.findById(ACCOUNT.accountId)
            } returns ACCOUNT

            every {
                accountRegisterRepository.findByAccountId(ACCOUNT.accountId)
            } returns accountRegisterCompleted

            every {
                accountRegisterRepository.save(accountRegisterCompleted.copy(mobilePhone = mobilePhone))
            } returns accountRegisterCompleted.copy(mobilePhone = mobilePhone)

            every { crmService.findContact(ACCOUNT.accountId) } returns crmContact

            every {
                userPoolAdapter.updatePhoneNumber(ACCOUNT.document, mobilePhone)
            } just Runs

            every { loginRepository.findUserLogin(ofType<ProviderUser>()) } returns Login(
                accountId = ACCOUNT.accountId,
                emailAddress = EmailAddress(email = "<EMAIL>"),
                role = Role.OWNER,
            )

            val result = updateAccountService.updatePhoneNumber(
                accountId = ACCOUNT.accountId,
                newPhoneNumber = mobilePhone,
                currentPhoneNumber = MobilePhone(ACCOUNT.mobilePhone),
            )
            result.isRight() shouldBe true

            val crmServiceSlot = slot<Account>()
            verify {
                userPoolAdapter.updatePhoneNumber(ACCOUNT.document, mobilePhone)
                accountRepository.save(ACCOUNT.copy(mobilePhone = "+*************"))
                accountRegisterRepository.save(accountRegisterCompleted.copy(mobilePhone = mobilePhone))
                crmService.upsertContact(capture(crmServiceSlot))
                loginRepository.createLogin(
                    ProviderUser(
                        id = ACCOUNT.accountId.value,
                        providerName = ProviderName.WHATSAPP,
                        username = "",
                        emailAddress = EmailAddress(email = "<EMAIL>"),
                    ),
                    ACCOUNT.accountId,
                    Role.OWNER,
                )

                loginRepository.createLogin(
                    ProviderUser(
                        id = ACCOUNT.accountId.value,
                        providerName = ProviderName.MSISDN,
                        username = "",
                        emailAddress = EmailAddress(email = "+<EMAIL>"),
                    ),
                    ACCOUNT.accountId,
                    Role.GUEST,
                )
            }
            crmServiceSlot.captured.mobilePhone shouldBe mobilePhone.msisdn
        }
    }

    @Nested
    @DisplayName("ao atualizar o email")
    inner class UpdateEmailAddress {
        @Test
        fun `deve retornar falha quando cognito falhar`() {
            every {
                accountRepository.findById(any())
            } returns ACCOUNT

            every {
                userPoolAdapter.updateEmailAddress(any(), any())
            } throws InvalidParameterException.builder().build()

            val result = updateAccountService.updateEmailAddress(
                accountId = AccountId(value = "USER-NOT-FOUND"),
                newEmailAddress = EmailAddress("invalid_email"),
                currentEmailAddress = EmailAddress("invalid_email"),
            )
            result.isLeft() shouldBe true
            result.mapLeft { it.shouldBeTypeOf<UpdateAccountError.InvalidEmailAddress>() }
        }

        @Test
        fun `deve retornar falha ao passar o email atual do usuário diferente do email dele`() {
            every {
                accountRepository.findById(any())
            } returns ACCOUNT

            val result = updateAccountService.updateEmailAddress(
                accountId = AccountId(value = "USER-NOT-FOUND"),
                newEmailAddress = EmailAddress("novo-email"),
                currentEmailAddress = EmailAddress("email-atual-errado"),
            )

            verify(exactly = 0) {
                userPoolAdapter.updateEmailAddress(any(), any())
            }

            result.isLeft() shouldBe true
            result.mapLeft { it.shouldBeTypeOf<UpdateAccountError.InvalidEmailAddress>() }
        }

        @Test
        fun `deve retornar sucesso ao passar o email novo igual ao email do usuário, sem chamar o userPool`() {
            every {
                accountRepository.findById(any())
            } returns ACCOUNT

            val result = updateAccountService.updateEmailAddress(
                accountId = AccountId(value = "USER-NOT-FOUND"),
                newEmailAddress = ACCOUNT.emailAddress,
                currentEmailAddress = ACCOUNT.emailAddress,
            )

            verify(exactly = 0) {
                userPoolAdapter.updateEmailAddress(any(), any())
            }

            result.isRight() shouldBe true
        }

        @Test
        fun `deve retornar sucesso ao passar um email novo diferente ao email do usuário`() {
            val newEmailAddress = EmailAddress("<EMAIL>")

            every {
                userPoolAdapter.doesUserExist(any())
            } returns true

            every {
                accountRepository.findById(any())
            } returns ACCOUNT

            every {
                accountRegisterRepository.findByAccountId(any())
            } returns accountRegisterData

            every {
                accountRegisterRepository.save(any())
            } returns accountRegisterData

            every {
                crmService.findContact(ofType(AccountId::class))
            } returns CrmContact(
                accountId = ACCOUNT.accountId,
                emailAddress = ACCOUNT.emailAddress,
                removed = false,
                accountType = UserAccountType.FULL_ACCOUNT,
                subscriptionType = SubscriptionType.PIX,
            )

            every {
                loginRepository.findUserLogin(any<EmailAddress>())
            } returns listOf(Login(accountId = ACCOUNT.accountId, emailAddress = ACCOUNT.emailAddress, role = Role.GUEST))

            val walletFixture = WalletFixture()
            val wallet = walletFixture.buildWallet(
                otherMembers = listOf(
                    ACCOUNT.toMember(
                        type = MemberType.COLLABORATOR,
                        permissions = MemberPermissions.of(MemberType.COLLABORATOR),
                    ),
                ),
            )

            every {
                walletRepository.findWallets(accountId = ACCOUNT.accountId, memberStatus = any())
            } returns listOf(wallet)

            val result = updateAccountService.updateEmailAddress(
                accountId = ACCOUNT.accountId,
                newEmailAddress = newEmailAddress,
                currentEmailAddress = ACCOUNT.emailAddress,
            )

            result.isRight() shouldBe true

            val userJourneyAccountSlot = slot<Account>()
            val accountSlot = slot<Account>()
            val accountRegisterSlot = slot<AccountRegisterData>()
            val crmServiceSlot = slot<Account>()
            val membersSlot = mutableListOf<Member>()

            verify {
                walletRepository.findWallets(accountId = ACCOUNT.accountId, memberStatus = MemberStatus.ACTIVE)
                walletRepository.findWallets(accountId = ACCOUNT.accountId, memberStatus = MemberStatus.REMOVED)
                userPoolAdapter.updateEmailAddress(ACCOUNT.document, newEmailAddress)
                userJourneyService.registerAsync(capture(userJourneyAccountSlot))
                accountRepository.save(capture(accountSlot))
                accountRegisterRepository.save(capture(accountRegisterSlot))
                crmService.upsertContact(capture(crmServiceSlot))
                loginRepository.updateEmail(ACCOUNT.accountId, ACCOUNT.emailAddress, newEmailAddress)
                walletRepository.upsertMember(walletId = wallet.id, member = capture(membersSlot))
            }

            userJourneyAccountSlot.captured.emailAddress shouldBe newEmailAddress
            accountSlot.captured.emailAddress shouldBe newEmailAddress
            accountRegisterSlot.captured.emailAddress shouldBe newEmailAddress
            crmServiceSlot.captured.emailAddress shouldBe newEmailAddress
            membersSlot.forEach {
                it.emailAddress shouldBe newEmailAddress
            }
        }
    }

    @Nested
    @DisplayName("ao atualizar o nome")
    inner class UpdateName {
        @Test
        fun `deve retornar falha ao passar o nome atual do usuário diferente do nome dele`() {
            every {
                accountRepository.findById(any())
            } returns ACCOUNT

            val result = updateAccountService.updateName(
                accountId = AccountId(value = "USER-NOT-FOUND"),
                newName = "novo-nome",
                currentName = "nome-atual-errado",
            )

            result.isLeft() shouldBe true
            result.mapLeft { it.shouldBeTypeOf<UpdateAccountError.InvalidName>() }
        }

        @Test
        fun `deve retornar sucesso ao passar o nome novo igual ao nome do usuário, sem chamar o userPool`() {
            every {
                accountRepository.findById(any())
            } returns ACCOUNT

            val result = updateAccountService.updateEmailAddress(
                accountId = AccountId(value = "USER-NOT-FOUND"),
                newEmailAddress = ACCOUNT.emailAddress,
                currentEmailAddress = ACCOUNT.emailAddress,
            )

            verify(exactly = 0) {
                userJourneyService wasNot called
                crmService wasNot called
            }

            result.isRight() shouldBe true
        }

        @Test
        fun `deve retornar sucesso ao passar um nome novo diferente do nome do usuário`() {
            every {
                walletRepository.save(ofType<Wallet>())
            } answers { firstArg() }
            every {
                userPoolAdapter.doesUserExist(any())
            } returns true

            every {
                accountRepository.findById(any())
            } returns ACCOUNT

            every {
                accountRegisterRepository.findByAccountId(any())
            } returns accountRegisterDataWithDocumentInfo

            every {
                accountRegisterRepository.save(any())
            } returns accountRegisterDataWithDocumentInfo

            every {
                crmService.findContact(ofType(AccountId::class))
            } returns CrmContact(
                accountId = ACCOUNT.accountId,
                emailAddress = ACCOUNT.emailAddress,
                removed = false,
                accountType = UserAccountType.FULL_ACCOUNT,
                subscriptionType = SubscriptionType.PIX,
            )

            every {
                loginRepository.findUserLogin(any<EmailAddress>())
            } returns listOf(Login(accountId = ACCOUNT.accountId, emailAddress = ACCOUNT.emailAddress, role = Role.GUEST))

            val walletFixture = WalletFixture()
            val wallet = walletFixture.buildWallet(
                walletFounder = ACCOUNT.toMember(
                    type = MemberType.FOUNDER,
                    permissions = MemberPermissions.of(MemberType.FOUNDER),
                ),
            )

            every {
                walletRepository.findWallets(accountId = ACCOUNT.accountId, memberStatus = any())
            } returns listOf(wallet)

            val newName = "new name"
            val result = updateAccountService.updateName(
                accountId = ACCOUNT.accountId,
                newName = newName,
                currentName = ACCOUNT.name,
            )

            result.isRight() shouldBe true

            val userJourneyAccountSlot = slot<Account>()
            val accountSlot = slot<Account>()
            val accountRegisterSlot = slot<AccountRegisterData>()
            val crmServiceSlot = slot<Account>()
            val membersSlot = mutableListOf<Member>()

            verify {
                walletRepository.save(withArg<Wallet> { it.name shouldBe newName })
                walletRepository.findWallets(accountId = ACCOUNT.accountId, memberStatus = MemberStatus.ACTIVE)
                walletRepository.findWallets(accountId = ACCOUNT.accountId, memberStatus = MemberStatus.REMOVED)
                userJourneyService.registerAsync(capture(userJourneyAccountSlot))
                accountRepository.save(capture(accountSlot))
                accountRegisterRepository.save(capture(accountRegisterSlot))
                crmService.upsertContact(capture(crmServiceSlot))
                walletRepository.upsertMember(walletId = wallet.id, member = capture(membersSlot))
            }

            userJourneyAccountSlot.captured.name shouldBe newName
            accountSlot.captured.name shouldBe newName
            accountRegisterSlot.captured.documentInfo?.name shouldBe newName
            crmServiceSlot.captured.name shouldBe newName
            membersSlot.forEach {
                it.name shouldBe newName
            }
        }
    }

    @Nested
    @DisplayName("ao atualizar o tipo da assinatura")
    inner class UpdateSubscriptiontType {
/*
        @Test
        fun `deve retornar falha ao passar o tipo de assinatura atual diferente da do usuario`() {
            every {
                accountRepository.findById(any())
            } returns ACCOUNT

            val result = updateAccountService.updateSubscriptionType(
                accountId = ACCOUNT.accountId,
                newSubscriptionType = SubscriptionType.IN_APP,
                currentSubscriptionType = SubscriptionType.IN_APP,
            )

            result.isLeft() shouldBe true
            result.mapLeft { it.shouldBeTypeOf<UpdateAccountError.InvalidSubscriptionType>() }

            verify(exactly = 0) {
                userJourneyService wasNot called
                crmService wasNot called
            }
        }

        @Test
        fun `deve retornar sucesso ao passar um novo tipo de assinatura diferente da do usuario`() {
            every {
                accountRepository.findById(any())
            } returns ACCOUNT

            every {
                accountRegisterRepository.findByAccountId(any())
            } returns accountRegisterDataWithDocumentInfo

            every {
                accountRegisterRepository.save(any())
            } returns accountRegisterDataWithDocumentInfo

            val result = updateAccountService.updateSubscriptionType(
                accountId = ACCOUNT.accountId,
                newSubscriptionType = SubscriptionType.IN_APP,
                currentSubscriptionType = ACCOUNT.subscriptionType,
            )

            result.isRight() shouldBe true

            val userJourneyAccountSlot = slot<Account>()
            val accountSlot = slot<Account>()
            val crmServiceSlot = slot<Account>()

            verify {
                userJourneyService.registerAsync(capture(userJourneyAccountSlot))
                accountRepository.save(capture(accountSlot))
                crmService.upsertContact(capture(crmServiceSlot))
            }

            userJourneyAccountSlot.captured.subscriptionType shouldBe SubscriptionType.IN_APP
            accountSlot.captured.subscriptionType shouldBe SubscriptionType.IN_APP
            crmServiceSlot.captured.subscriptionType shouldBe SubscriptionType.IN_APP
        }
        */
    }
}