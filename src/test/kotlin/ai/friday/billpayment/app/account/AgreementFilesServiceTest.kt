package ai.friday.billpayment.app.account

import ai.friday.billpayment.accountRegisterDataMissingAcceptedAt
import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.DeclarationOfResidencyForm
import ai.friday.billpayment.app.DeclarationOfResidencySignature
import ai.friday.billpayment.app.integrations.PDFConverter
import ai.friday.billpayment.app.integrations.TemplateCompiler
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import io.via1.communicationcentre.app.integrations.ContractWriterService
import java.io.ByteArrayOutputStream
import java.time.format.DateTimeFormatter
import java.util.Locale
import org.junit.jupiter.api.Test

internal class AgreementFilesServiceTest {

    private val clientIP = ""
    private val html = "<html></html>"
    private val pdfByteArray = "pdf com html".toByteArray()
    private val contractWriterService: ContractWriterService = mockk()
    private val handlebarTemplateCompiler: TemplateCompiler = mockk {
        every { buildHtml(any()) } returns CompiledHtml(html)
    }
    private val pdfConverter: PDFConverter = mockk {
        every { convert(any(), false) } returns pdfByteArray
    }
    private val agreementFilesService = AgreementFilesService(
        contractWriterService = contractWriterService,
        handlebarTemplateCompiler = handlebarTemplateCompiler,
        pdfConverter = pdfConverter,
    )

    @Test
    fun `should generate declaration of residency when accepted at is not present`() {
        val stream = ByteArrayOutputStream()
        val now = getZonedDateTime().minusHours(2)

        withGivenDateTime(now) {
            agreementFilesService.createDeclarationOfResidency(
                stream = stream,
                accountRegisterData = accountRegisterDataMissingAcceptedAt,
                clientIP = clientIP,
            )
        }

        val slot = slot<DeclarationOfResidencyForm>()
        verify(exactly = 1) {
            handlebarTemplateCompiler.buildHtml(capture(slot))
            pdfConverter.convert(html, false)
        }

        with(slot.captured) {
            val documentInfo = accountRegisterDataMissingAcceptedAt.documentInfo!!
            val address = accountRegisterDataMissingAcceptedAt.address!!
            val agreement = accountRegisterDataMissingAcceptedAt.agreementData!!

            val month = now.format(DateTimeFormatter.ofPattern("MMMM", Locale("pt", "BR")))
            val shortMonth = now.format(DateTimeFormatter.ofPattern("MM", Locale("pt", "BR")))
            val day = now.format(DateTimeFormatter.ofPattern("dd"))
            val time = now.format(DateTimeFormatter.ofPattern("HH:mm:ss"))

            val expectedFormattedDocument = "${documentInfo.cpf.take(3)}.${documentInfo.cpf.substring(3, 6)}.${
            documentInfo.cpf.substring(
                6,
                9,
            )
            }-${documentInfo.cpf.takeLast(2)}"
            val expectedFormattedZipCode = "${address.zipCode.take(5)}-${address.zipCode.takeLast(3)}"
            val expectedFormattedDate = "$day de $month de ${now.year}"
            val expectedFormattedShortDate = "$day/$shortMonth/${now.year} às $time"

            assertSoftly {
                fullName shouldBe documentInfo.name
                document shouldBe expectedFormattedDocument
                fullAddress shouldBe address.toFullAddress()
                city shouldBe address.city
                federalUnity shouldBe address.state
                zipCode shouldBe expectedFormattedZipCode
                signature shouldBe DeclarationOfResidencySignature(
                    date = expectedFormattedDate,
                    shortDate = expectedFormattedShortDate,
                    email = accountRegisterDataMissingAcceptedAt.emailAddress.value,
                    phone = accountRegisterDataMissingAcceptedAt.mobilePhone!!.msisdn,
                    clientIP = clientIP,
                    key = agreement.userContractSignature,
                )
            }
        }

        stream.toByteArray() shouldBe pdfByteArray
    }
}