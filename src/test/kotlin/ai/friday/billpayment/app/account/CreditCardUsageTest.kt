package ai.friday.billpayment.app.account

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.ACCOUNT_WITH_CREDIT_CARD
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.integrations.CrmContact
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.collections.shouldNotContain
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class CreditCardUsageTest {
    val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val enhancedClient = getDynamoDB()

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )
    private val crmService = mockk<CrmService>(relaxed = true) {
        every { findContact(ACCOUNT.accountId) } returns CrmContact(
            accountId = ACCOUNT.accountId,
            emailAddress = EmailAddress(email = ""),
            role = null,
            name = null,
            mobilePhone = null,
            document = null,
            removed = false,
            groups = listOf(),
            accountType = UserAccountType.FULL_ACCOUNT,
            subscriptionType = SubscriptionType.PIX,
        )
    }
    private val notificationAdapterMock = mockk<NotificationAdapter>(relaxed = true)

    private val accountService = AccountService(
        accountConfigurationService = mockk(),
        accountRepository = accountRepository,
        chatbotMessagePublisher = mockk(),
        crmService = crmService,
        notificationAdapter = notificationAdapterMock,
        walletRepository = mockk(),
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `ao habilitar com configuração repetida não deve notificar o usuário`() {
        accountRepository.save(ACCOUNT_WITH_CREDIT_CARD)

        accountService.enableCreditCardUsage(
            accountId = ACCOUNT.accountId,
            quota = 200000,
        )

        verify {
            crmService wasNot called
        }
        verify(exactly = 0) {
            notificationAdapterMock.notifyCreditCardEnabled(any(), any())
        }
    }

    @Test
    fun `ao habilitar com algumas configuracoes repetidas e outras novas, deve alterar apenas o que foi passado como parametro e não notificar o usuario`() {
        accountRepository.save(ACCOUNT_WITH_CREDIT_CARD)

        accountService.enableCreditCardUsage(
            accountId = ACCOUNT.accountId,
            quota = 500000,
        )

        val updatedAccount = accountRepository.findById(ACCOUNT.accountId)

        updatedAccount.hasCreditCardEnabled() shouldBe true
        updatedAccount.hasGroup(AccountGroup.CREDIT_CARD_STANDARD_PLAN) shouldBe true
        updatedAccount.creditCardConfiguration.quota shouldBe 500000

        verify(exactly = 0) {
            notificationAdapterMock.notifyCreditCardEnabled(any(), updatedAccount.creditCardConfiguration.quota)
        }
    }

    @Test
    fun `ao habilitar deve adicionar o usuario ao grupo, notificar o usuario e utilizar os valores padroes`() {
        accountRepository.save(ACCOUNT)

        accountService.enableCreditCardUsage(
            accountId = ACCOUNT.accountId,
            quota = null,
        )

        val updatedAccount = accountRepository.findById(ACCOUNT.accountId)

        updatedAccount.hasCreditCardEnabled() shouldBe true
        updatedAccount.hasGroup(AccountGroup.CREDIT_CARD_STANDARD_PLAN) shouldBe true
        updatedAccount.creditCardConfiguration.quota shouldBe 500_00

        verify(exactly = 1) {
            crmService.upsertContact(
                withArg<Account> {
                    it.configuration.groups shouldContain AccountGroup.CREDIT_CARD_STANDARD_PLAN
                },
            )

            notificationAdapterMock.notifyCreditCardEnabled(any(), updatedAccount.creditCardConfiguration.quota)
        }
    }

    @Test
    fun `ao habilitar o cartao com valores especificos deve notificar o usuario e utilizar os valores passados`() {
        accountRepository.save(ACCOUNT)

        accountService.enableCreditCardUsage(
            accountId = ACCOUNT.accountId,
            quota = 500000,
        )

        val updatedAccount = accountRepository.findById(ACCOUNT.accountId)

        updatedAccount.hasCreditCardEnabled() shouldBe true
        updatedAccount.hasGroup(AccountGroup.CREDIT_CARD_STANDARD_PLAN) shouldBe true
        updatedAccount.creditCardConfiguration.quota shouldBe 500000

        verify(exactly = 1) {
            crmService.upsertContact(
                withArg<Account> {
                    it.configuration.groups shouldContain AccountGroup.CREDIT_CARD_STANDARD_PLAN
                },
            )

            notificationAdapterMock.notifyCreditCardEnabled(any(), updatedAccount.creditCardConfiguration.quota)
        }
    }

    @Test
    fun `ao habilitar o cartao com cota negativa deve falhar`() {
        accountRepository.save(ACCOUNT_WITH_CREDIT_CARD)

        val result = accountService.enableCreditCardUsage(
            accountId = ACCOUNT.accountId,
            quota = -10,
        )
        result.isLeft() shouldBe true
        result.mapLeft {
            it.shouldBeInstanceOf<IllegalArgumentException>()
        }
    }

    @Test
    fun `ao habilitar o cartao de usuário não ativo deve falhar`() {
        accountRepository.save(ACCOUNT.copy(status = AccountStatus.CLOSED))

        val result = accountService.enableCreditCardUsage(
            accountId = ACCOUNT.accountId,
            quota = 100,
        )
        result.isLeft() shouldBe true
        result.mapLeft {
            it.shouldBeInstanceOf<IllegalStateException>()
        }
    }

    @Test
    fun `ao remover deve setar a cota para 0 e remover usuario do grupo`() {
        accountRepository.save(ACCOUNT_WITH_CREDIT_CARD)

        accountService.disableCreditCardUsage(ACCOUNT_WITH_CREDIT_CARD.accountId)

        val updatedAccount = accountRepository.findById(ACCOUNT_WITH_CREDIT_CARD.accountId)

        updatedAccount.hasCreditCardEnabled() shouldBe false
        updatedAccount.hasGroup(AccountGroup.CREDIT_CARD_STANDARD_PLAN) shouldBe false
        updatedAccount.creditCardConfiguration.quota shouldBe 0

        verify(exactly = 1) {
            crmService.upsertContact(
                withArg<Account> {
                    it.configuration.groups shouldNotContain AccountGroup.CREDIT_CARD_STANDARD_PLAN
                },
            )
        }

        verify(exactly = 0) {
            notificationAdapterMock.notifyCreditCardEnabled(any(), any())
        }
    }
}