package ai.friday.billpayment.app.account

import ai.friday.billpayment.accountRegisterDataWithAddress
import ai.friday.billpayment.adapters.adjust.AdjustAdapter
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AdEventMetadata
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Test

class AdServiceTest {
    private val adAdapter: AdjustAdapter = mockk(relaxed = true)
    private val accountRegisterRepository: AccountRegisterRepository = mockk()
    private val systemActivityService: SystemActivityService = mockk()

    private val adService = AdService(
        adAdapter,
        systemActivityService,
        accountRegisterRepository,
    )

    @Test
    fun `se não houver device info, não deve enviar evento`() {
        val accountId = AccountId("")

        every { systemActivityService.getDeviceInfo(accountId) } returns null

        adService.publishAccountActivated(accountId)

        verify(exactly = 0) {
            adAdapter.trackEvent(any(), any())
        }
    }

    @Test
    fun `se houver device info, deve enviar corretamente o evento`() {
        val accountId = AccountId("account-1")
        val deviceAdIds = DeviceAdIds(adId = "adjust-id", adGoogleId = "google-id", adAppleId = "apple-id")

        every { systemActivityService.getDeviceInfo(accountId) } returns deviceAdIds
        every { accountRegisterRepository.findByAccountId(accountId) } returns accountRegisterDataWithAddress
            .copy(clientIP = "clientIp", mobilePhone = MobilePhone("+*************"), emailAddress = EmailAddress("<EMAIL>"), address = accountRegisterDataWithAddress.address!!.copy(state = "RJ"))

        val metadataSlot = slot<AdEventMetadata>()

        adService.publishAccountActivated(accountId)

        verify {
            adAdapter.trackEvent(any(), capture(metadataSlot))
        }

        with(metadataSlot.captured) {
            this.accountId shouldBe AccountId("account-1")
            clientIP shouldBe "clientIp"
            trackingIds shouldBe deviceAdIds
            phoneNumber shouldBe MobilePhone("+*************")
            emailAddress shouldBe EmailAddress("<EMAIL>")
            state shouldBe "RJ"
        }
    }
}