package ai.friday.billpayment.app.account

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.AcquirerService
import ai.friday.billpayment.app.integrations.CreditCardChallengeRepository
import ai.friday.billpayment.app.metrics.push
import ai.friday.billpayment.buildCreditCardAuthorization
import ai.friday.billpayment.buildCreditCardChallenge
import ai.friday.billpayment.creditCard
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.paymentMethodId
import ai.friday.billpayment.withMockedMetric
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import io.kotest.assertions.asClue
import io.kotest.matchers.longs.shouldBeLessThanOrEqual
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import java.time.Duration
import org.junit.jupiter.api.RepeatedTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class CreditCardCreateChallengeTest {

    private val accountId = AccountId(value = ACCOUNT_ID)

    private val pendingCreditCard = creditCard.copy(status = AccountPaymentMethodStatus.PENDING)

    private val acquirerServiceMock = mockk<AcquirerService>() {
        every { authorize(any(), any(), any(), any(), any()) } returns buildCreditCardAuthorization()
        every { cancel(any()) } just runs
    }

    private val accountRepositoryMock = mockk<AccountRepository>() {
        every {
            findAccountPaymentMethodByIdAndAccountId(
                accountId = accountId,
                accountPaymentMethodId = paymentMethodId,
            )
        } returns pendingCreditCard
    }
    private val creditCardChallengeRepositoryMock = mockk<CreditCardChallengeRepository>(relaxed = true)

    private val creditCardService = DefaultCreditCardService(
        accountRepository = accountRepositoryMock,
        acquirerService = acquirerServiceMock,
        featureConfiguration = mockk(),
        creditCardChallengeRepository = creditCardChallengeRepositoryMock,
        generalCreditCardConfiguration = mockk() {
            every { challenge.expiration } returns Duration.ofDays(1)
            every { challenge.softDescriptor } returns ""
            every { challenge.minAmount } returns 50
            every { challenge.maxAmount } returns 1000
            every { challenge.maxAttempts } returns 3
        },
        limitService = mockk(relaxed = true),
        hmacService = mockk(relaxed = true),
        lockProvider = mockk(relaxed = true),
        creditCardScoreService = mockk(),
        creditCardInformationService = mockk(),
    )

    @Test
    fun `não deve criar a a validação quando um cartão não estiver pendente`() {
        every {
            accountRepositoryMock.findAccountPaymentMethodByIdAndAccountId(
                accountId = accountId,
                accountPaymentMethodId = paymentMethodId,
            )
        } returns creditCard

        val result = creditCardService.createChallenge(
            accountId = accountId,
            paymentMethodId = paymentMethodId,
        ).shouldBeTypeOf<CreditCardChallengeResponse.InvalidCreditCardStatus>()

        result.status shouldBe creditCard.status

        verify { acquirerServiceMock wasNot Called }

        verify(exactly = 0) {
            creditCardChallengeRepositoryMock.save(any())
        }
    }

    @Test
    fun `não deve criar a validação se o authorize falhar`() {
        val creditCardAuthorizationResponse = buildCreditCardAuthorization(status = CreditCardPaymentStatus.DENIED)
        every {
            acquirerServiceMock.authorize(any(), any(), any(), any(), any())
        } returns creditCardAuthorizationResponse

        val result = creditCardService.createChallenge(
            accountId = accountId,
            paymentMethodId = paymentMethodId,
        ).shouldBeTypeOf<CreditCardChallengeResponse.CreditCardAutorizationError>()

        result.status shouldBe creditCardAuthorizationResponse.status
        result.acquirerReturnCode shouldBe creditCardAuthorizationResponse.acquirerReturnCode!!
        result.acquirerReturnMessage shouldBe creditCardAuthorizationResponse.acquirerReturnMessage

        verify(exactly = 0) {
            creditCardChallengeRepositoryMock.save(any())
        }
    }

    @ParameterizedTest
    @EnumSource(value = CreditCardPaymentStatus::class, names = ["ABORTED", "DENIED"])
    fun `deve estornar quando o authorize responder ABORTED`(status: CreditCardPaymentStatus) {
        val creditCardAuthorizationResponse = buildCreditCardAuthorization(status = status)
        every {
            acquirerServiceMock.authorize(any(), any(), any(), any(), any())
        } returns creditCardAuthorizationResponse

        val result = creditCardService.createChallenge(
            accountId = accountId,
            paymentMethodId = paymentMethodId,
        ).shouldBeTypeOf<CreditCardChallengeResponse.CreditCardAutorizationError>()

        result.status shouldBe creditCardAuthorizationResponse.status
        result.acquirerReturnCode shouldBe creditCardAuthorizationResponse.acquirerReturnCode!!
        result.acquirerReturnMessage shouldBe creditCardAuthorizationResponse.acquirerReturnMessage

        verify(exactly = 0) { creditCardChallengeRepositoryMock.save(any()) }

        verify { acquirerServiceMock.cancel(any()) }
    }

    @Test
    fun `deve criar a validação`() {
        every {
            creditCardChallengeRepositoryMock.find(paymentMethodId)
        } returns null

        creditCardService.createChallenge(
            accountId = accountId,
            paymentMethodId = paymentMethodId,
        ) shouldBe CreditCardChallengeResponse.Success

        verify {
            creditCardChallengeRepositoryMock.save(any())
        }
    }

    @Test
    fun `não deve criar a validação caso já exista uma ativa`() {
        every {
            creditCardChallengeRepositoryMock.find(
                paymentMethodId = paymentMethodId,
            )
        } returns buildCreditCardChallenge()

        creditCardService.createChallenge(
            accountId = accountId,
            paymentMethodId = paymentMethodId,
        ) shouldBe CreditCardChallengeResponse.ChallengeAlreadyExists

        verify(exactly = 0) {
            creditCardChallengeRepositoryMock.save(any())
        }
    }

    @Test
    fun `não deve criar se o status da validação for 'SUCESSO'`() {
        every {
            creditCardChallengeRepositoryMock.find(paymentMethodId)
        } returns buildCreditCardChallenge(status = CreditCardChallengeStatus.SUCCESS)

        creditCardService.createChallenge(
            accountId = accountId,
            paymentMethodId = paymentMethodId,
        ).shouldBeTypeOf<CreditCardChallengeResponse.InvalidChallengeStatus>()

        verify(exactly = 0) {
            creditCardChallengeRepositoryMock.save(any())
        }
    }

    @Test
    fun `deve criar se o status da validação for 'EXPIRED'`() {
        every {
            creditCardChallengeRepositoryMock.find(paymentMethodId)
        } returns buildCreditCardChallenge(status = CreditCardChallengeStatus.EXPIRED)

        creditCardService.createChallenge(
            accountId = accountId,
            paymentMethodId = paymentMethodId,
        ).shouldBeTypeOf<CreditCardChallengeResponse.Success>()

        verify {
            creditCardChallengeRepositoryMock.save(any())
        }
    }

    @Test
    fun `deve criar uma nova validação quando a data de expiração for maior que hoje`() {
        val expirationDate = getZonedDateTime()

        every {
            creditCardChallengeRepositoryMock.find(paymentMethodId)
        } returns buildCreditCardChallenge(expirationDate = expirationDate)

        withGivenDateTime(expirationDate.plusDays(1)) {
            creditCardService.createChallenge(
                accountId = accountId,
                paymentMethodId = paymentMethodId,
            ) shouldBe CreditCardChallengeResponse.Success
        }

        verify {
            creditCardChallengeRepositoryMock.save(any())
        }
    }

    @Test
    fun `deve criar enviar metrica ao criar um novo challenge`() {
        val expirationDate = getZonedDateTime()

        every {
            creditCardChallengeRepositoryMock.find(paymentMethodId)
        } returns buildCreditCardChallenge(expirationDate = expirationDate)

        withGivenDateTime(expirationDate.plusDays(1)) {
            withMockedMetric(CreditCardChallengeMetric) {
                creditCardService.createChallenge(
                    accountId = accountId,
                    paymentMethodId = paymentMethodId,
                ) shouldBe CreditCardChallengeResponse.Success

                verify { CreditCardChallengeMetric.push(any(), any<Number>()) }
            }
        }
    }

    @Test
    fun `deve cancelar as 2 cobranças caso a segunda falhe`() {
        every {
            acquirerServiceMock.authorize(
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns buildCreditCardAuthorization() andThen buildCreditCardAuthorization(CreditCardPaymentStatus.ABORTED)

        creditCardService.createChallenge(
            accountId = accountId,
            paymentMethodId = paymentMethodId,
        ).shouldBeTypeOf<CreditCardChallengeResponse.CreditCardAutorizationError>()

        verify(exactly = 2) {
            acquirerServiceMock.cancel(any())
        }
    }

    @RepeatedTest(100)
    fun `testar valores aleatorios`() {
        creditCardService.generatePairOfRandom().asClue { (firstRandom, secondRandom) ->
            firstRandom shouldNotBe secondRandom
            firstRandom + secondRandom shouldBeLessThanOrEqual 1000
        }
    }
}

class CreditCardValidateChallengeTest {

    private val accountId = AccountId(value = ACCOUNT_ID)

    private val pendingCreditCard = creditCard.copy(status = AccountPaymentMethodStatus.PENDING)

    private val acquirerServiceMock = mockk<AcquirerService>() {
        every { authorize(any(), any(), any(), any(), any()) } returns buildCreditCardAuthorization()
        every { cancel(any()) } just runs
    }

    private val accountRepositoryMock = mockk<AccountRepository>() {
        every {
            findAccountPaymentMethodByIdAndAccountId(
                accountId = accountId,
                accountPaymentMethodId = paymentMethodId,
            )
        } returns pendingCreditCard
    }
    private val creditCardChallengeRepositoryMock = mockk<CreditCardChallengeRepository>(relaxed = true)

    private val creditCardService = DefaultCreditCardService(
        accountRepository = accountRepositoryMock,
        acquirerService = acquirerServiceMock,
        featureConfiguration = mockk(),
        creditCardChallengeRepository = creditCardChallengeRepositoryMock,
        generalCreditCardConfiguration = mockk() {
            every { challenge.expiration } returns Duration.ofDays(1)
            every { challenge.softDescriptor } returns ""
            every { challenge.minAmount } returns 1
            every { challenge.maxAmount } returns 1000
            every { challenge.maxAttempts } returns 3
        },
        limitService = mockk(relaxed = true),
        hmacService = mockk(relaxed = true),
        lockProvider = mockk(relaxed = true),
        creditCardScoreService = mockk(),
        creditCardInformationService = mockk(),
    )

    @Test
    fun `quando a validação estiver com o status expirado deve retornar erro`() {
        val token = 100L

        every { creditCardChallengeRepositoryMock.find(paymentMethodId) } returns buildCreditCardChallenge(status = CreditCardChallengeStatus.EXPIRED)

        creditCardService.validateChallenge(
            accountId,
            paymentMethodId,
            token,
        ) shouldBe ValidateChallengeResult.Expired
    }

    @Test
    fun `quando a validação estiver com o status sucesso deve retornar erro`() {
        val token = 100L

        every { creditCardChallengeRepositoryMock.find(paymentMethodId) } returns buildCreditCardChallenge(status = CreditCardChallengeStatus.SUCCESS)

        creditCardService.validateChallenge(
            accountId,
            paymentMethodId,
            token,
        ).shouldBeTypeOf<ValidateChallengeResult.ChallengeInvalidStatus>()
    }

    @Test
    fun `quando a validação estiver com a data limite expirada deve retornar erro`() {
        val token = 100L

        every { creditCardChallengeRepositoryMock.find(paymentMethodId) } returns buildCreditCardChallenge(
            expirationDate = getZonedDateTime().minusDays(1),
        )

        creditCardService.validateChallenge(
            accountId,
            paymentMethodId,
            token,
        ) shouldBe ValidateChallengeResult.Expired
    }

    @Test
    fun `quando o usuário errar a validação deve contar mais um erro`() {
        val token = 100L

        val challenge = buildCreditCardChallenge()

        every { creditCardChallengeRepositoryMock.find(paymentMethodId) } returns challenge

        creditCardService.validateChallenge(
            accountId,
            paymentMethodId,
            token,
        ) shouldBe ValidateChallengeResult.ChallengeMismatch

        verify { creditCardChallengeRepositoryMock.save(challenge.copy(attempts = challenge.attempts + 1)) }
    }

    @Test
    fun `quando não existir validação deve retornar um erro`() {
        every { creditCardChallengeRepositoryMock.find(paymentMethodId) } returns null

        creditCardService.validateChallenge(
            accountId,
            paymentMethodId = paymentMethodId,
            token = 100L,
        ) shouldBe ValidateChallengeResult.NotFound

        verify(exactly = 0) { creditCardChallengeRepositoryMock.save(any()) }
    }

    @Test
    fun `quando houver 3 tentativas de validação deve retornar um erro`() {
        val challenge = buildCreditCardChallenge(attempts = 3)

        every { creditCardChallengeRepositoryMock.find(paymentMethodId) } returns challenge

        creditCardService.validateChallenge(
            accountId,
            paymentMethodId = paymentMethodId,
            token = 100L,
        ) shouldBe ValidateChallengeResult.AttemptsExceeded

        verify(exactly = 0) { creditCardChallengeRepositoryMock.save(any()) }
    }

    @Test
    fun `quando o cartão não estiver pendente, retorna um erro`() {
        val challenge = buildCreditCardChallenge()

        every { creditCardChallengeRepositoryMock.find(paymentMethodId) } returns challenge
        every {
            accountRepositoryMock.findAccountPaymentMethodByIdAndAccountId(
                accountId = accountId,
                accountPaymentMethodId = paymentMethodId,
            )
        } returns creditCard

        creditCardService.validateChallenge(
            accountId,
            paymentMethodId = paymentMethodId,
            token = 100L,
        ).shouldBeTypeOf<ValidateChallengeResult.CreditCardInvalidStatus>()
    }

    @Test
    fun `quando houver algum erro ao fazer o extorno da validação, deve retornar FailedOnAcquirerVoid`() {
        val challenge = buildCreditCardChallenge()

        every { creditCardChallengeRepositoryMock.find(paymentMethodId) } returns challenge
        every { acquirerServiceMock.cancel(challenge.acquirerOrderId) } throws NoStackTraceException()
        every { accountRepositoryMock.activateAccountPaymentMethod(accountId, paymentMethodId) } just runs

        creditCardService.validateChallenge(
            accountId,
            paymentMethodId = paymentMethodId,
            token = 1000L,
        ).shouldBeTypeOf<ValidateChallengeResult.FailedOnAcquirerVoid>()
    }

    @Test
    fun `quando enviar o valor da validação corretamente, deve ativar o cartão e realizar o void`() {
        val challenge = buildCreditCardChallenge()

        every { creditCardChallengeRepositoryMock.find(paymentMethodId) } returns challenge
        every { accountRepositoryMock.activateAccountPaymentMethod(accountId, paymentMethodId) } just runs

        withMockedMetric(CreditCardChallengeValidationMetric) {
            creditCardService.validateChallenge(
                accountId,
                paymentMethodId = paymentMethodId,
                token = 1000L,
            ).shouldBeTypeOf<ValidateChallengeResult.Success>()
            verify { CreditCardChallengeValidationMetric.push(any(), any<Number>()) }
        }

        verify(exactly = 1) {
            creditCardChallengeRepositoryMock.save(
                challenge.copy(
                    attempts = challenge.attempts + 1,
                    status = CreditCardChallengeStatus.SUCCESS,
                ),
            )
            acquirerServiceMock.cancel(challenge.acquirerOrderId)

            accountRepositoryMock.activateAccountPaymentMethod(
                accountId = accountId,
                accountPaymentMethodId = paymentMethodId,
            )
        }
    }
}

class CreditCardGetChallengeTest {

    private val accountId = AccountId(value = ACCOUNT_ID)

    private val pendingCreditCard = creditCard.copy(status = AccountPaymentMethodStatus.PENDING)

    private val acquirerServiceMock = mockk<AcquirerService>() {
        every { authorize(any(), any(), any(), any(), any()) } returns buildCreditCardAuthorization()
        every { cancel(any()) } just runs
    }

    private val accountRepositoryMock = mockk<AccountRepository>() {
        every {
            findAccountPaymentMethodByIdAndAccountId(
                accountId = accountId,
                accountPaymentMethodId = paymentMethodId,
            )
        } returns pendingCreditCard
    }
    private val creditCardChallengeRepositoryMock = mockk<CreditCardChallengeRepository>(relaxed = true)

    private val creditCardService = DefaultCreditCardService(
        accountRepository = accountRepositoryMock,
        acquirerService = acquirerServiceMock,
        featureConfiguration = mockk(),
        creditCardChallengeRepository = creditCardChallengeRepositoryMock,
        generalCreditCardConfiguration = mockk() {
            every { challenge.expiration } returns Duration.ofDays(1)
            every { challenge.softDescriptor } returns ""
            every { challenge.minAmount } returns 1
            every { challenge.maxAmount } returns 1000
            every { challenge.maxAttempts } returns 3
        },
        limitService = mockk(relaxed = true),
        hmacService = mockk(relaxed = true),
        lockProvider = mockk(relaxed = true),
        creditCardScoreService = mockk(),
        creditCardInformationService = mockk(),
    )

    @Test
    fun `quando não houver challenge, deve retornar NotFound`() {
        every { creditCardChallengeRepositoryMock.find(paymentMethodId) } returns null

        creditCardService.getChallenge(
            accountId,
            paymentMethodId = paymentMethodId,
        ).shouldBeTypeOf<GetChallengeResult.NotFound>()
    }

    @Test
    fun `quando o challenge estiver expirado, deve retornar Expired`() {
        val challenge = buildCreditCardChallenge(status = CreditCardChallengeStatus.EXPIRED)
        every { creditCardChallengeRepositoryMock.find(paymentMethodId) } returns challenge

        creditCardService.getChallenge(
            accountId,
            paymentMethodId = paymentMethodId,
        ).shouldBeTypeOf<GetChallengeResult.Expired>()
    }

    @Test
    fun `quando o challenge estiver com sucesso, deve retornar InvalidStatus`() {
        val challenge = buildCreditCardChallenge(status = CreditCardChallengeStatus.SUCCESS)
        every { creditCardChallengeRepositoryMock.find(paymentMethodId) } returns challenge

        creditCardService.getChallenge(
            accountId,
            paymentMethodId = paymentMethodId,
        ).shouldBeTypeOf<GetChallengeResult.InvalidStatus>()
    }

    @Test
    fun `quando o challenge atingir o limite de tentativas, deve retornar MaxAttemptsReached`() {
        val challenge = buildCreditCardChallenge(attempts = 3)
        every { creditCardChallengeRepositoryMock.find(paymentMethodId) } returns challenge

        creditCardService.getChallenge(
            accountId,
            paymentMethodId = paymentMethodId,
        ).shouldBeTypeOf<GetChallengeResult.MaxAttemptsReached>()
    }

    @Test
    fun `quando o paymentMethodId for de uma conta diferente, deve retornar NotFound`() {
        val challenge = buildCreditCardChallenge(accountId = AccountId(ACCOUNT_ID_2))
        every { creditCardChallengeRepositoryMock.find(paymentMethodId) } returns challenge

        creditCardService.getChallenge(
            accountId,
            paymentMethodId = paymentMethodId,
        ).shouldBeTypeOf<GetChallengeResult.NotFound>()
    }

    @Test
    fun `quando o challenge for encontrado e possuir tentativas restantes, deve retornar Success`() {
        val challenge = buildCreditCardChallenge()
        every { creditCardChallengeRepositoryMock.find(paymentMethodId) } returns challenge

        creditCardService.getChallenge(
            accountId,
            paymentMethodId = paymentMethodId,
        ).shouldBeTypeOf<GetChallengeResult.Success>()
    }
}