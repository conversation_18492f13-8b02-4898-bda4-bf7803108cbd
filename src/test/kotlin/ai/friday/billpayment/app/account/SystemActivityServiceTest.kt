package ai.friday.billpayment.app.account

import DynamoDBUtils
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SystemActivityDbRepository
import ai.friday.billpayment.adapters.dynamodb.SystemActivityDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.date.shouldBeAfter
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import java.time.ZoneId
import java.time.ZonedDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class SystemActivityServiceTest {
    private val enhancedClient = DynamoDBUtils.setupDynamoDB()

    private val accountActivityRepository = SystemActivityDbRepository(SystemActivityDynamoDAO(enhancedClient))

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )
    private val service = SystemActivityService(systemActivityRepository = accountActivityRepository)

    private val accountId = AccountId(ACCOUNT_ID)
    private val walletId = WalletId(WALLET_ID)

    @BeforeEach
    fun setup() {
        accountRepository.save(ACCOUNT)
    }

    @Test
    fun hasScheduledBill() {
        service.hasScheduledBill(accountId) shouldBe false

        service.setHasScheduledBill(accountId)

        service.hasScheduledBill(accountId) shouldBe true
    }

    @Test
    fun hasCashedIn() {
        service.hasCashedIn(walletId) shouldBe false

        service.setHasCashedIn(walletId)

        service.hasCashedIn(walletId) shouldBe true

        val entity = accountActivityRepository.find(
            key = SystemActivityKey(
                value = walletId.value,
                type = SystemActivityKeyType.Wallet,
            ),
            activityType = SystemActivityType.HasCashedIn,
        )

        entity?.lastUpdated shouldNotBe null
    }

    @Test
    fun `deve atualizar a data da ultima atividade`() {
        val now = ZonedDateTime.now()

        val entity = withGivenDateTime(now) {
            service.setHasCashedIn(walletId)

            accountActivityRepository.find(
                key = SystemActivityKey(
                    value = walletId.value,
                    type = SystemActivityKeyType.Wallet,
                ),
                activityType = SystemActivityType.HasCashedIn,
            )
        }

        val latestEntity = withGivenDateTime(now.plusDays(1)) {
            service.setHasCashedIn(walletId)

            accountActivityRepository.find(
                key = SystemActivityKey(
                    value = walletId.value,
                    type = SystemActivityKeyType.Wallet,
                ),
                activityType = SystemActivityType.HasCashedIn,
            )
        }

        latestEntity!!.lastUpdated!!.shouldBeAfter(entity!!.lastUpdated!!)
    }

    @Test
    fun getOptOutNotificationDate() {
        service.getOptOutNotificationDate(accountId).shouldBeNull()

        val now = ZonedDateTime.of(2022, 5, 8, 10, 50, 41, 0, ZoneId.of("Brazil/East"))
        withGivenDateTime(now) {
            service.setOptOutNotificationDate(accountId)
        }

        service.getOptOutNotificationDate(accountId) shouldBe now.toLocalDateTime()
    }

    @Test
    fun `não deve encontrar as atividades cuja data são maiores ou iguais à data informada`() {
        val accountId = AccountId(ACCOUNT_ID)

        val now = getZonedDateTime()

        service.setAccountActivated(accountId, now.plusDays(1))

        service.findAccountIdsLowerThan(
            SystemActivityType.AccountActivated,
            activityValue = now.format(dateFormat),
        ).shouldBeEmpty()
    }

    @Test
    fun `deve encontrar as atividades cuja data são menores que a data informada`() {
        val accountId = AccountId(ACCOUNT_ID)

        val now = getZonedDateTime()

        service.setAccountActivated(accountId, now.minusDays(1))

        service.findAccountIdsLowerThan(
            SystemActivityType.AccountActivated,
            activityValue = now.format(dateFormat),
        ) shouldContain accountId
    }

    @Test
    fun `deve encontrar a atividade que informa se já buscamos a foto do usuário a primeira vez`() {
        val accountId = AccountId(ACCOUNT_ID)
        service.getBootstrappedUserPhoto(accountId) shouldBe false

        service.setBootstrappedUserPhoto(accountId)

        service.getBootstrappedUserPhoto(accountId) shouldBe true
    }

    @Test
    fun `deve setar a data de expiração do trial ao ativar uma conta`() {
        val accountId = AccountId(ACCOUNT_ID)
        val now = ZonedDateTime.of(2024, 2, 1, 10, 0, 0, 0, brazilTimeZone)

        withGivenDateTime(now) {
            service.setAcceptedTrial(accountId, 10)
            service.setAccountActivated(accountId, now)
        }

        with(accountActivityRepository.find(accountId.toSystemActivityKey(), SystemActivityType.TrialActivated)) {
            this?.value shouldBe "2024-02-10"
        }
    }

    @Test
    fun `não deve setar a data de expiração do trial se não tiver um período de trial`() {
        val accountId = AccountId(ACCOUNT_ID)
        val now = ZonedDateTime.of(2024, 2, 1, 10, 0, 0, 0, brazilTimeZone)

        withGivenDateTime(now) {
            service.setAccountActivated(accountId, now)
        }

        accountActivityRepository.find(accountId.toSystemActivityKey(), SystemActivityType.TrialActivated) shouldBe null
    }

    @Test
    fun `deve setar a atividade do whatsApp payment`() {
        service.setWhatsAppPayment(walletId, true)

        with(accountActivityRepository.find(walletId.toSystemActivityKey(), SystemActivityType.WhatsAppPayment)) {
            this.shouldNotBeNull()
            this.value shouldBe "true"
            this.key.type shouldBe SystemActivityKeyType.Wallet
        }
    }

    @Test
    fun `deve retornar falso se ainda não tiver setado pagamento via whatsApp`() {
        service.getWhatsAppPayment(walletId) shouldBe false
    }

    @Test
    fun `deve salvar que o realtório de categorias foi visualizado`() {
        val accountId = AccountId(ACCOUNT_ID)

        val dateTime = getZonedDateTime()

        withGivenDateTime(dateTime) {
            service.setViewedCategorySummary(accountId)
        }

        with(
            accountActivityRepository.find(
                key = accountId.toSystemActivityKey(),
                activityType = SystemActivityType.ViewedCategorySummary,
            ),
        ) {
            this?.value shouldBe dateTime.format(dateTimeFormat)
        }
    }

    @Test
    fun `deve incrementar número de vezes que autorizou transação do chatbot`() {
        val walletId = WalletId(WALLET_ID)

        service.incrementChatbotTransactionAuthorized(walletId) shouldBe 1
        service.incrementChatbotTransactionAuthorized(walletId) shouldBe 2
        service.incrementChatbotTransactionAuthorized(walletId) shouldBe 3

        with(
            accountActivityRepository.find(walletId.toSystemActivityKey(), SystemActivityType.ChatbotTransactionAuthorized),
        ) {
            this?.value shouldBe "3"
        }
    }
}