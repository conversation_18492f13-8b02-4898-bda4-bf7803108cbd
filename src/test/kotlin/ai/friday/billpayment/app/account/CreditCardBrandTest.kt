package ai.friday.billpayment.app.account

import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class CreditCardBrandTest {

    @ParameterizedTest
    @MethodSource("createKnownBrands")
    fun `deve encontar o enum correto baseado em uma string`(brand: String, expected: CreditCardBrand) {
        CreditCardBrand.find(brand) shouldBe expected
    }

    @Test
    fun `deve jogar exception ao nao encontrar uma brand conhecida`() {
        assertThrows<IllegalArgumentException> {
            CreditCardBrand.find("XFakeX")
        }
    }

    companion object {

        @JvmStatic
        fun createKnownBrands(): List<Arguments> = listOf(
            Arguments.arguments("Amex", CreditCardBrand.AMEX),
            Arguments.arguments("Aura", CreditCardBrand.AURA),
            Arguments.arguments("Diners", CreditCardBrand.DINERS),
            Arguments.arguments("Discover", CreditCardBrand.DISCOVER),
            Arguments.arguments("Elo", CreditCardBrand.ELO),
            Arguments.arguments("Jcb", CreditCardBrand.JCB),
            Arguments.arguments("Hipercard", CreditCardBrand.HIPERCARD),
            Arguments.arguments("Master", CreditCardBrand.MASTERCARD),
            Arguments.arguments("Mastercard", CreditCardBrand.MASTERCARD),
            Arguments.arguments("Visa", CreditCardBrand.VISA),
        )
    }
}