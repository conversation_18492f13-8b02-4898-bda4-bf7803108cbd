package ai.friday.billpayment.app.account

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.IntegrationError
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.AcquirerService
import ai.friday.billpayment.app.integrations.CreditCardChallengeRepository
import ai.friday.billpayment.app.integrations.CreditCardInformationService
import ai.friday.billpayment.app.integrations.CreditCardOwnership
import ai.friday.billpayment.app.integrations.CreditCardOwnershipValidationRequest
import ai.friday.billpayment.app.integrations.CreditCardScore
import ai.friday.billpayment.app.integrations.CreditCardScoreService
import ai.friday.billpayment.app.integrations.CreditCardScoreValidationRequest
import ai.friday.billpayment.app.integrations.CreditCardValidationService
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.integrations.GeneralCreditCardConfiguration
import ai.friday.billpayment.app.metrics.push
import ai.friday.billpayment.withMockedMetric
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.test.annotation.MockBean
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.Duration
import java.time.ZonedDateTime
import java.util.UUID
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource

class AddCreditCardTest {
    private val account = ACCOUNT.copy(configuration = ACCOUNT.configuration.copy(groups = listOf(AccountGroup.ALPHA)))
    private val accountId = account.accountId

    private val accountRepository: AccountRepository =
        mockk {
            every { findAccountPaymentMethodsByAccountId(any()) } returns emptyList()
            every { findAccountPaymentMethodsByHMac(any()) } returns emptyList()
        }
    private val creditCardScoreService: CreditCardScoreService = mockk()
    private val creditCardChallengeRepository: CreditCardChallengeRepository = mockk()
    private val featureConfiguration =
        mockk<FeatureConfiguration> {
            every { zeroAuthEnabled } returns true
            every { creditCardChallenge } returns false
        }

    @MockBean(CreditCardValidationService::class)
    fun creditCardValidationService(): CreditCardValidationService = creditCardValidationService

    private val creditCardValidationService =
        mockk<CreditCardValidationService> {
            every {
                validateOwnership(any())
            } returns CreditCardOwnership.IS_OWNER.right()
        }

    private val acquirerService: AcquirerService = mockk()

    private val creditCardInformationService: CreditCardInformationService =
        mockk {
            every {
                retrieveBinDetails(any())
            } returns
                CreditCardBinDetails(
                    provider = "provider",
                    cardType = "cardType",
                    foreignCard = true,
                    corporateCard = "corporateCard",
                    issuer = "issuer",
                    issuerCode = "issuerCode",
                    prepaid = "prepaid",
                    status = "status",
                )
        }
    private val generalCreditCardConfiguration =
        mockk<GeneralCreditCardConfiguration> {
            every { challenge.expiration } returns Duration.ofDays(1)
            every { maxPeriodLimit } returns 3
            every { maxLimit } returns 3
            every { periodLimit } returns Duration.ofDays(30)
        }

    private val addCreditCard =
        DefaultCreditCardService(
            accountRepository = accountRepository,
            acquirerService = acquirerService,
            featureConfiguration = featureConfiguration,
            creditCardChallengeRepository = creditCardChallengeRepository,
            generalCreditCardConfiguration = generalCreditCardConfiguration,
            limitService = mockk(relaxed = true),
            hmacService = mockk(relaxed = true),
            lockProvider = mockk(relaxed = true),
            creditCardScoreService = creditCardScoreService,
            creditCardInformationService = creditCardInformationService,
        )

    private val creditCard =
        CreditCard(
            CreditCardBrand.MASTERCARD,
            "****************",
            "01/2022",
            binDetails = null,
            riskLevel = RiskLevel.LOW,
        )
    private val anotherCreditCard =
        CreditCard(
            CreditCardBrand.MASTERCARD,
            "****************",
            "01/2021",
            binDetails = null,
            riskLevel = RiskLevel.LOW,
        )

    private val accountPaymentMethod =
        AccountPaymentMethod(
            id = AccountPaymentMethodId(UUID.randomUUID().toString()),
            status = AccountPaymentMethodStatus.ACTIVE,
            method = creditCard,
            created = getZonedDateTime().minusDays(2),
            accountId = accountId,
        )

    @BeforeEach
    fun init() {
        every {
            accountRepository.findById(account.accountId)
        } returns account
    }

    @Test
    fun `deve retornar erro quando o mesmo cartão já foi adicionado no mesmo dia`() {
        every { accountRepository.findAccountPaymentMethodsByAccountId(accountId) } returns
            listOf(
                accountPaymentMethod.copy(
                    status = AccountPaymentMethodStatus.INACTIVE,
                    created = getZonedDateTime(),
                ),
            )

        every { creditCardValidationService.validateOwnership(any()) } returns CreditCardOwnership.IS_OWNER.right()
        every { creditCardScoreService.creditcardScore(any()) } returns CreditCardScore.MATCH.right()

        addCreditCard.add(
            accountId,
            creditCard.pan,
            creditCard.expiryDate,
            creditCard.brand,
            "000",
        ) shouldBe AddCreditCardResult.CreditCardAlreadyAddedToday

        verify(exactly = 0) {
            accountRepository.createAccountPaymentMethod(
                any(),
                any(),
                ofType(CreditCard::class),
                any(),
                any(),
            )
        }
    }

    @Test
    fun `deve retornar erro quando o mesmo cartão já foi adicionado por outro usuário`() {
        every { accountRepository.findAccountPaymentMethodsByHMac(any()) } returns
            listOf(
                accountPaymentMethod.copy(
                    status = AccountPaymentMethodStatus.PENDING,
                    created = getZonedDateTime(),
                ),
            )
        addCreditCard.add(
            accountId,
            creditCard.pan,
            creditCard.expiryDate,
            creditCard.brand,
            "000",
        ) shouldBe AddCreditCardResult.CreditCardAlreadyInUse

        verify(exactly = 0) {
            accountRepository.createAccountPaymentMethod(
                any(),
                any(),
                ofType(CreditCard::class),
                any(),
                any(),
            )
        }
    }

    @Test
    fun `should return max limit reached when account has 3 credit cards`() {
        every { accountRepository.findAccountPaymentMethodsByAccountId(accountId) } returns
            listOf(
                accountPaymentMethod.copy(created = ZonedDateTime.now().minusMonths(1).minusYears(1)),
                accountPaymentMethod.copy(created = ZonedDateTime.now().minusDays(1)),
                accountPaymentMethod.copy(created = ZonedDateTime.now().minusDays(1)),
            )

        every { creditCardValidationService.validateOwnership(any()) } returns CreditCardOwnership.IS_OWNER.right()
        every { creditCardScoreService.creditcardScore(any()) } returns CreditCardScore.MATCH.right()

        addCreditCard.add(
            accountId,
            anotherCreditCard.pan,
            anotherCreditCard.expiryDate,
            anotherCreditCard.brand,
            "000",
        ) shouldBe AddCreditCardResult.MaxLimitReached

        verify(exactly = 0) {
            accountRepository.createAccountPaymentMethod(
                accountId,
                any(),
                ofType(CreditCard::class),
                any(),
                any(),
            )
        }
    }

    @Test
    fun `deve retornar limite máximo de cartões alcançado no período quando o usuário adicionar mais que 3 cartões no mês`() {
        every { accountRepository.findAccountPaymentMethodsByAccountId(accountId) } returns
            listOf(
                accountPaymentMethod.copy(
                    status = AccountPaymentMethodStatus.INACTIVE,
                    created = getZonedDateTime(),
                ),
                accountPaymentMethod.copy(
                    status = AccountPaymentMethodStatus.PENDING,
                    created = getZonedDateTime(),
                ),
                accountPaymentMethod.copy(
                    status = AccountPaymentMethodStatus.ACTIVE,
                    created = getZonedDateTime().minusDays(7),
                ),
            )

        every { creditCardValidationService.validateOwnership(any()) } returns CreditCardOwnership.IS_OWNER.right()
        every { creditCardScoreService.creditcardScore(any()) } returns CreditCardScore.MATCH.right()
        addCreditCard.add(
            accountId,
            anotherCreditCard.pan,
            anotherCreditCard.expiryDate,
            anotherCreditCard.brand,
            "000",
        ) shouldBe AddCreditCardResult.PeriodLimitReached

        verify(exactly = 0) {
            accountRepository.createAccountPaymentMethod(
                accountId,
                any(),
                ofType(CreditCard::class),
                any(),
                any(),
            )
        }
    }

    @Test
    fun `should return server error on error tokenizing credit card`() {
        every {
            acquirerService.tokenize(
                creditCard.pan,
                creditCard.expiryDate,
                creditCard.brand,
                any(),
                any(),
            )
        } returns Either.Left(IntegrationError.ServerError())

        every { creditCardValidationService.validateOwnership(any()) } returns CreditCardOwnership.IS_OWNER.right()
        every { creditCardScoreService.creditcardScore(any()) } returns CreditCardScore.MATCH.right()

        addCreditCard.add(accountId, creditCard.pan, creditCard.expiryDate, creditCard.brand, "000")
            .shouldBeTypeOf<AddCreditCardResult.ServerError>()

        verify(exactly = 0) {
            accountRepository.createAccountPaymentMethod(
                accountId,
                any(),
                ofType(CreditCard::class),
                any(),
                any(),
            )
        }
    }

    @Test
    fun `should return invalid credit card result on a client error tokenizing credit card`() {
        every {
            acquirerService.tokenize(
                creditCard.pan,
                creditCard.expiryDate,
                creditCard.brand,
                any(),
                any(),
            )
        } returns Either.Left(IntegrationError.ClientError())

        every { creditCardValidationService.validateOwnership(any()) } returns CreditCardOwnership.IS_OWNER.right()
        every { creditCardScoreService.creditcardScore(any()) } returns CreditCardScore.MATCH.right()

        addCreditCard.add(
            accountId,
            creditCard.pan,
            creditCard.expiryDate,
            creditCard.brand,
            "000",
        ) shouldBe AddCreditCardResult.InvalidCreditCard

        verify(exactly = 0) {
            accountRepository.createAccountPaymentMethod(
                accountId,
                any(),
                ofType(CreditCard::class),
                any(),
                any(),
            )
        }
    }

    @Test
    fun `should return unauthorized credit card result on a unauthorized credit card validation`() {
        val token = CreditCardToken("TOKEN")
        every {
            acquirerService.tokenize(
                creditCard.pan,
                creditCard.expiryDate,
                creditCard.brand,
                any(),
                any(),
            )
        } returns Either.Right(token)
        every { acquirerService.validate(any(), "000") } returns Either.Right(false)
        every { creditCardValidationService.validateOwnership(any()) } returns CreditCardOwnership.IS_OWNER.right()
        every { creditCardScoreService.creditcardScore(any()) } returns CreditCardScore.MATCH.right()

        addCreditCard.add(
            accountId,
            creditCard.pan,
            creditCard.expiryDate,
            creditCard.brand,
            "000",
        ) shouldBe AddCreditCardResult.UnauthorizedCreditCard

        val slot = slot<CreditCardToken>()
        verify {
            acquirerService.validate(capture(slot), "000")
        }
        slot.captured shouldBe token
        verify(exactly = 0) {
            accountRepository.createAccountPaymentMethod(
                any(),
                any(),
                ofType(CreditCard::class),
                any(),
                any(),
            )
        }
    }

    @ParameterizedTest
    @EnumSource(CreditCardScore::class, names = ["MATCH"], mode = EnumSource.Mode.EXCLUDE)
    fun `deve retornar cartao de credito com alto risco quando o cartao nao pertence ao cliente`(score: CreditCardScore) {
        every { creditCardValidationService.validateOwnership(any()) } returns CreditCardOwnership.NOT_OWNER.right()
        every { creditCardScoreService.creditcardScore(any()) } returns score.right()

        val x =
            addCreditCard.add(
                accountId,
                creditCard.pan,
                creditCard.expiryDate,
                creditCard.brand,
                "000",
            )

        x.shouldBeTypeOf<AddCreditCardResult.HighRiskCreditCard>()

        verify(exactly = 0) {
            accountRepository.createAccountPaymentMethod(
                any(),
                any(),
                ofType(CreditCard::class),
                any(),
                any(),
            )
        }
    }

    @Test
    fun `should return server error when tokenize does not work`() {
        val token = CreditCardToken("TOKEN")
        every {
            acquirerService.tokenize(
                creditCard.pan,
                creditCard.expiryDate,
                creditCard.brand,
                any(),
                any(),
            )
        } returns Either.Right(token)
        every { acquirerService.validate(any(), "000") } returns Either.Left(IntegrationError.ServerError())
        every { creditCardValidationService.validateOwnership(any()) } returns CreditCardOwnership.IS_OWNER.right()
        every { creditCardScoreService.creditcardScore(any()) } returns CreditCardScore.MATCH.right()

        addCreditCard.add(accountId, creditCard.pan, creditCard.expiryDate, creditCard.brand, "000")
            .shouldBeTypeOf<AddCreditCardResult.ServerError>()

        verify(exactly = 0) {
            accountRepository.createAccountPaymentMethod(
                any(),
                any(),
                ofType(CreditCard::class),
                any(),
                any(),
            )
        }
    }

    @ParameterizedTest
    @MethodSource("validCreditCardScore")
    fun `should return created and not call validate when feature flag is disabled`(
        creditCardScore: CreditCardScore,
        riskLevel: RiskLevel,
    ) {
        every { featureConfiguration.zeroAuthEnabled } returns false
        every { accountRepository.findById(any()) } returns account
        val scoreValidationSlot = slot<CreditCardScoreValidationRequest>()
        every { creditCardScoreService.creditcardScore(capture(scoreValidationSlot)) } returns creditCardScore.right()

        val token = CreditCardToken("TOKEN")
        every {
            acquirerService.tokenize(
                creditCard.pan,
                creditCard.expiryDate,
                creditCard.brand,
                any(),
                any(),
            )
        } returns Either.Right(token)
        every {
            accountRepository.createAccountPaymentMethod(
                any(),
                any(),
                ofType(CreditCard::class),
                any(),
                any(),
            )
        } returns accountPaymentMethod

        withMockedMetric(CreditCardAddMetric) {
            addCreditCard.add(accountId, creditCard.pan, creditCard.expiryDate, creditCard.brand, "000")
                .shouldBeTypeOf<AddCreditCardResult.Created>()

            verify { CreditCardAddMetric.push(any(), any<Number>()) }
        }

        val slot = slot<CreditCard>()
        verify { accountRepository.createAccountPaymentMethod(any(), any(), capture(slot), any(), any()) }

        with(slot.captured) {
            this.riskLevel shouldBe riskLevel
            this.pan shouldBe creditCard.pan
            this.expiryDate shouldBe creditCard.expiryDate
            this.brand shouldBe creditCard.brand
        }

        verify(exactly = 0) { acquirerService.validate(any(), "000") }
    }

    @Test
    fun `should return HighRiskCreditCard when card is not owned by user`() {
        every { featureConfiguration.zeroAuthEnabled } returns false
        every { accountRepository.findById(any()) } returns account
        val ownershipValidationSlot = slot<CreditCardOwnershipValidationRequest>()
        val ownershipScoreSlot = slot<CreditCardScoreValidationRequest>()
        every { creditCardValidationService.validateOwnership(capture(ownershipValidationSlot)) } returns CreditCardOwnership.NOT_OWNER.right()
        every { creditCardScoreService.creditcardScore(capture(ownershipScoreSlot)) } returns CreditCardScore.NO_MATCH.right()

        addCreditCard.add(accountId, creditCard.pan, creditCard.expiryDate, creditCard.brand, "000")
            .shouldBeTypeOf<AddCreditCardResult.HighRiskCreditCard>()

        verify(exactly = 0) {
            accountRepository.createAccountPaymentMethod(
                accountId,
                any(),
                ofType(CreditCard::class),
                any(),
                any(),
            )
        }

        ownershipValidationSlot.isCaptured shouldBe false
        with(ownershipScoreSlot.captured) {
            this.cpf shouldBe account.document
            this.bin shouldBe creditCard.bin
            this.lastFourDigits shouldBe creditCard.lastFourDigits
        }

        verify(exactly = 0) { acquirerService.validate(any(), "000") }
    }

    @Test
    fun `should return ok when card is not owned by user but he is in the allow list`() {
        every { featureConfiguration.zeroAuthEnabled } returns false
        every { accountRepository.findById(any()) } returns account.copy(
            configuration = account.configuration.copy(
                creditCardConfiguration = account.configuration.creditCardConfiguration.copy(
                    allowedRisk = RiskLevel.HIGH,
                ),
            ),
        )
        every { creditCardValidationService.validateOwnership(any()) } returns CreditCardOwnership.NOT_OWNER.right()
        every { creditCardScoreService.creditcardScore(any()) } returns CreditCardScore.NO_MATCH.right()
        every { acquirerService.tokenize(any(), any(), any(), any(), any()) } returns CreditCardToken("").right()
        every { accountRepository.createAccountPaymentMethod(any(), any(), any<CreditCard>(), any(), any()) } returns accountPaymentMethod

        val result = addCreditCard.add(account.accountId, creditCard.pan, creditCard.expiryDate, creditCard.brand, "000")
        result.shouldBeTypeOf<AddCreditCardResult.Created>()

        val slot = slot<CreditCard>()

        verify { accountRepository.createAccountPaymentMethod(any(), any(), capture(slot), any(), any()) }

        with(slot.captured) {
            this.riskLevel shouldBe RiskLevel.HIGH
            this.pan shouldBe creditCard.pan
            this.expiryDate shouldBe creditCard.expiryDate
            this.brand shouldBe creditCard.brand
        }
    }

    @Test
    fun `should not create credit card when it already exists`() {
        every { accountRepository.findAccountPaymentMethodsByAccountId(accountId) } returns
            listOf(
                accountPaymentMethod,
            )
        every { creditCardValidationService.validateOwnership(any()) } returns CreditCardOwnership.IS_OWNER.right()
        every { creditCardScoreService.creditcardScore(any()) } returns CreditCardScore.MATCH.right()

        val creditCardPaymentMethod =
            addCreditCard.add(accountId, creditCard.pan, creditCard.expiryDate, creditCard.brand, "000")

        creditCardPaymentMethod.shouldBeTypeOf<AddCreditCardResult.Created> {
            it.accountPaymentMethod.id shouldBe accountPaymentMethod.id
        }

        verify(exactly = 0) {
            accountRepository.createAccountPaymentMethod(
                accountId,
                any(),
                ofType(CreditCard::class),
                any(),
                any(),
            )
        }
    }

    @Test
    fun `não deve criar um cartão de crédito se ele já existir no estado pendente`() {
        every { accountRepository.findAccountPaymentMethodsByAccountId(accountId) } returns
            listOf(
                accountPaymentMethod.copy(status = AccountPaymentMethodStatus.PENDING),
            )
        every { creditCardValidationService.validateOwnership(any()) } returns CreditCardOwnership.IS_OWNER.right()
        every { creditCardScoreService.creditcardScore(any()) } returns CreditCardScore.MATCH.right()

        val creditCardPaymentMethod =
            addCreditCard.add(accountId, creditCard.pan, creditCard.expiryDate, creditCard.brand, "000")
                .shouldBeTypeOf<AddCreditCardResult.Created>()

        creditCardPaymentMethod.accountPaymentMethod.id shouldBe accountPaymentMethod.id

        verify {
            accountRepository.createAccountPaymentMethod(
                any(),
                any(),
                ofType(CreditCard::class),
                any(),
                any(),
            ) wasNot called
        }
    }

    companion object {
        @JvmStatic
        fun validCreditCardScore(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(CreditCardScore.MATCH, RiskLevel.LOW),
            )
        }
    }
}