package ai.friday.billpayment.app.account

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.PARTIAL_ACCOUNT
import ai.friday.billpayment.accountRegisterData
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.LoginDbRepository
import ai.friday.billpayment.adapters.dynamodb.LoginDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.msisdnauth.MsisdnAuthDbRepository
import ai.friday.billpayment.adapters.msisdnauth.MsisdnAuthDynamoDAO
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.TokenData
import ai.friday.billpayment.app.integrations.TokenType
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.app.msisdnauth.MsisdnAuth
import ai.friday.billpayment.app.msisdnauth.MsisdnAuthId
import ai.friday.billpayment.app.msisdnauth.MsisdnAuthService
import ai.friday.billpayment.app.msisdnauth.TEMPORARY_NICKNAME
import ai.friday.billpayment.app.msisdnauth.ValidateTokenRequest
import ai.friday.billpayment.app.msisdnauth.createTemporaryEmail
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.usergroups.AccountGroupSelectorType
import ai.friday.billpayment.app.usergroups.UserGroupsSelectorService
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.Duration
import java.time.ZonedDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class MsisdnAuthServiceTest {
    private var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val tokenServiceMock: TokenService = mockk()
    private val loginRepository = LoginDbRepository(LoginDynamoDAO(dynamoDbEnhancedClient), TransactionDynamo(dynamoDbEnhancedClient))
    private val msisdnAuthRepository =
        MsisdnAuthDbRepository(MsisdnAuthDynamoDAO(dynamoDbEnhancedClient))

    private val registerServiceMock: RegisterService = mockk(relaxed = true) {
        every { createLivenessId(any()) } returns Either.Right(mockk())
    }
    private val accountRepositoryMock: AccountDbRepository = mockk(relaxed = true)

    private val accountRegisterRepository: AccountRegisterRepository = mockk(relaxed = true)
    private val closeAccountService: CloseAccountService = mockk(relaxed = true)

    private val crmService: CrmService = mockk(relaxed = true)

    private val msisdnTest = "+*************"

    private val userGroupsSelectorService = mockk<UserGroupsSelectorService>() {
        every {
            selectGroups(AccountGroupSelectorType.REGISTER)
        } returns listOf(AccountGroup.INVESTMENT_CAMPAIGN)
    }
    private val msisdnAuthService = MsisdnAuthService(
        tokenService = tokenServiceMock,
        loginRepository = loginRepository,
        msisdnAuthRepository = msisdnAuthRepository,
        registerService = registerServiceMock,
        accountRepository = accountRepositoryMock,
        crmService = crmService,
        accountRegisterRepository = accountRegisterRepository,
        closeAccountService = closeAccountService,
        userGroupsSelectorService = userGroupsSelectorService,
        inAppSubscription = 0.1f,
        signUpMobilePhones = listOf(msisdnTest),
        appleMobilePhones = listOf(),
    )

    private val mobilePhone = MobilePhone(msisdn = "+*************")
    private val mobilePhoneTest = MobilePhone(msisdn = msisdnTest)
    private val tokenDuration = Duration.ofMinutes(1)
    private val tokenCooldown = Duration.ofSeconds(30)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `deve criar um registro de login e cadastro quando nao existe o telefone na base`() {
        every {
            tokenServiceMock.issueToken(
                mobilePhone = mobilePhone,
                accountId = any(),
                channel = TokenChannel.SMS,
            )
        } answers {
            IssuedToken(duration = tokenDuration, accountId = secondArg(), cooldown = tokenCooldown).right()
        }
        every { registerServiceMock.createLivenessId(any()) } returns Either.Right(mockk())

        val result = msisdnAuthService.issueToken(mobilePhone = mobilePhone, channel = TokenChannel.SMS)

        result.isRight()

        val emailAddress = EmailAddress("${mobilePhone.msisdn}@friday.ai")
        val logins = loginRepository.findUserLogin(emailAddress = emailAddress)
        logins.size shouldBe 1
        with(logins.single()) {
            this.role shouldBe Role.GUEST
            this.emailAddress.recipient shouldBe mobilePhone.msisdn
        }

        val whatsappLogins = loginRepository.findUserLogin(emailAddress = EmailAddress("<EMAIL>"))
        whatsappLogins.size shouldBe 1

        val msisdnAuth = msisdnAuthRepository.findByAccountId(logins.single().accountId)

        msisdnAuth!!.accountId shouldBe logins.single().accountId

        verify {
            registerServiceMock.createAccountRegister(
                accountId = logins.single().accountId,
                emailAddress = emailAddress,
                username = TEMPORARY_NICKNAME,
                mobilePhone = mobilePhone,
                registrationType = any(),
            )
            accountRepositoryMock.create(
                username = TEMPORARY_NICKNAME,
                emailAddress = emailAddress,
                accountId = logins.single().accountId,
                registrationType = RegistrationType.BASIC,
                groups = any(),
                subscriptionType = any(),
            )
            registerServiceMock.createLivenessId(logins.single().accountId)
        }

        result.map {
            it.otpId shouldBe msisdnAuth.id
            it.duration shouldBe tokenDuration
            it.cooldown shouldBe tokenCooldown
        }
    }

    @Test
    fun `deve criar um cadastro com subscription type In app para os telefones de teste`() {
        every {
            tokenServiceMock.issueToken(
                mobilePhone = mobilePhoneTest,
                accountId = any(),
                channel = TokenChannel.SMS,
            )
        } answers {
            IssuedToken(duration = tokenDuration, accountId = secondArg(), cooldown = Duration.ofSeconds(30)).right()
        }
        every { registerServiceMock.createLivenessId(any()) } returns Either.Right(mockk())

        val result = msisdnAuthService.issueToken(mobilePhone = mobilePhoneTest, channel = TokenChannel.SMS)

        result.isRight()

        val slot = slot<SubscriptionType>()
        verify {
            accountRepositoryMock.create(
                any(),
                any(),
                any(),
                any(),
                any(),
                subscriptionType = capture(slot),
            )
        }

        slot.captured shouldBe SubscriptionType.IN_APP
    }

    @Test
    fun `deve retornar o mesmo token com o duration novo em caso do token ainda ser válido`() {
        val accountId = AccountId()

        loginRepository.createLogin(
            providerUser = ProviderUser(
                id = accountId.value,
                providerName = ProviderName.MSISDN,
                username = "",
                emailAddress = EmailAddress(email = "${mobilePhone.msisdn}@friday.ai"),
            ),
            id = accountId,
            role = Role.GUEST,
        )

        every {
            tokenServiceMock.issueToken(
                mobilePhone = mobilePhone,
                accountId = any(),
                channel = TokenChannel.SMS,
            )
        } answers {
            TokenStillValidException(
                accountId = accountId,
                duration = Duration.ofSeconds(50),
                cooldown = Duration.ofSeconds(20),
            ).left()
        }

        val result = msisdnAuthService.issueToken(mobilePhone = mobilePhone, channel = TokenChannel.SMS)

        result.isRight() shouldBe true
        result.map {
            it.duration.seconds shouldBe 50
            it.cooldown.seconds shouldBe 20
        }
    }

    @Test
    fun `deve retornar o accountId existente quando o telefone é conhecido`() {
        val accountId = AccountId()

        loginRepository.createLogin(
            providerUser = ProviderUser(
                id = accountId.value,
                providerName = ProviderName.MSISDN,
                username = "",
                emailAddress = EmailAddress(email = "${mobilePhone.msisdn}@friday.ai"), // FIXME - usar o dominio a partir de uma config
            ),
            id = accountId,
            role = Role.GUEST,
        )

        every {
            tokenServiceMock.issueToken(
                mobilePhone = mobilePhone,
                accountId = any(),
                channel = TokenChannel.SMS,
            )
        } answers {
            IssuedToken(duration = tokenDuration, accountId = secondArg(), cooldown = Duration.ofSeconds(30)).right()
        }

        val result = msisdnAuthService.issueToken(mobilePhone = mobilePhone, channel = TokenChannel.SMS)

        result.isRight()

        val logins = loginRepository.findUserLogin(emailAddress = EmailAddress("${mobilePhone.msisdn}@friday.ai"))
        logins.size shouldBe 1
        with(logins.single()) {
            this.role shouldBe Role.GUEST
            this.emailAddress.recipient shouldBe mobilePhone.msisdn
            this.accountId shouldBe accountId
        }

        val msisdnAuth = msisdnAuthRepository.findByAccountId(logins.single().accountId)

        msisdnAuth!!.accountId shouldBe logins.single().accountId

        result.map {
            it.otpId shouldBe msisdnAuth.id
            it.cooldown shouldBe tokenCooldown
            it.duration shouldBe tokenDuration
        }
    }

    @Test
    fun `deve retornar um novo accountId quando o telefone é de um usuário GUEST com o registrationType FULL`() {
        val accountId = AccountId()

        loginRepository.createLogin(
            providerUser = ProviderUser(
                id = accountId.value,
                providerName = ProviderName.MSISDN,
                username = "",
                emailAddress = EmailAddress(email = "${mobilePhone.msisdn}@friday.ai"),
            ),
            id = accountId,
            role = Role.GUEST,
        )

        every { accountRegisterRepository.findByAccountId(accountId) } returns accountRegisterData.copy(
            accountId = accountId,
            registrationType = RegistrationType.FULL,
        )
        every { accountRepositoryMock.findPartialAccountByIdOrNull(accountId) } returns PARTIAL_ACCOUNT.copy(
            id = accountId,
            registrationType = RegistrationType.FULL,
        )
        every { closeAccountService.closePartialAccount(accountId, any()) } answers {
            loginRepository.remove(accountId)
            ClosePartialAccountResult(removedFromUserPool = false).right()
        }

        val now = getZonedDateTime()

        withGivenDateTime(now) {
            val result = msisdnAuthService.issueToken(mobilePhone = mobilePhone, channel = TokenChannel.SMS)
            val login = loginRepository.findUserLogin(emailAddress = createTemporaryEmail(mobilePhone))

            result.isRight()
            login.size shouldBe 1
            login.single().accountId shouldNotBe accountId

            verify {
                closeAccountService.closePartialAccount(
                    accountId,
                    AccountClosureDetails.create(
                        reason = null,
                        description = "Cadastro encerrado por ser legado",
                        at = now,
                    ),
                )
            }
        }
    }

    private val tokenKey = TokenKey(
        accountId = AccountId(),
        value = "",
    )

    @Test
    fun `deve retornar uma exececao quando o token nao for valido`() {
        val accountId = AccountId("Account-123-456")
        val msisdnAuthId = MsisdnAuthId("MSISDN_AUTH-123-456")
        msisdnAuthRepository.save(
            MsisdnAuth(
                id = msisdnAuthId,
                accountId = accountId,
                createdAt = ZonedDateTime.now(),
            ),
        )

        every {
            tokenServiceMock.validateToken(tokenKey.copy(accountId = accountId), TokenType.MOBILE_PHONE)
        } returns NoStackTraceException().left()

        every {
            tokenServiceMock.validateToken(tokenKey.copy(accountId = accountId), TokenType.WHATSAPP)
        } returns NoStackTraceException().left()

        val result = msisdnAuthService.validateToken(
            validateTokenRequest = ValidateTokenRequest(otpId = msisdnAuthId, token = ""),
        )

        result.isLeft() shouldBe true
        result.getOrElse {
            it.shouldBeTypeOf<NoStackTraceException>()
        }

        verify {
            crmService wasNot called
        }
    }

    @Test
    fun `deve retornar um JWT e atualizar o CRM a partir do account quando validar o token de um OWNER`() {
        val accountId = AccountId("Account-123-456")
        val msisdnAuthId = MsisdnAuthId("MSISDN_AUTH-123-456")
        msisdnAuthRepository.save(
            MsisdnAuth(
                id = msisdnAuthId,
                accountId = accountId,
                createdAt = ZonedDateTime.now(),
            ),
        )
        every {
            tokenServiceMock.validateToken(tokenKey.copy(accountId = accountId), TokenType.MOBILE_PHONE)
        } returns TokenData.of(mobilePhone = mobilePhone, TokenChannel.SMS).right()

        every {
            accountRepositoryMock.findByIdOrNull(accountId)
        } returns ACCOUNT.copy(accountId = accountId, mobilePhone = mobilePhone.msisdn)

        val dateTime = getZonedDateTime()
        val validateTokenRequest = ValidateTokenRequest(
            otpId = msisdnAuthId,
            token = tokenKey.value,
        )

        val result = withGivenDateTime(dateTime) {
            msisdnAuthService.validateToken(
                validateTokenRequest,
            )
        }

        result.isRight() shouldBe true
        result.map { (mobilePhone, accountId) ->
            mobilePhone shouldBe mobilePhone
            accountId shouldBe accountId
        }

        val slot = slot<Account>()
        verify {
            crmService.upsertContact(capture(slot))
        }

        with(slot.captured) {
            this.accountId shouldBe accountId
            this.mobilePhone shouldBe mobilePhone
        }
    }

    @Test
    fun `deve retornar um JWT e atualizar o CRM a partir do partial account quando validar o token de um GUEST`() {
        val accountId = AccountId("Account-123-456")
        val msisdnAuthId = MsisdnAuthId("MSISDN_AUTH-123-456")
        msisdnAuthRepository.save(
            MsisdnAuth(
                id = msisdnAuthId,
                accountId = accountId,
                createdAt = ZonedDateTime.now(),
            ),
        )
        every {
            tokenServiceMock.validateToken(tokenKey.copy(accountId = accountId), TokenType.MOBILE_PHONE)
        } returns TokenData.of(mobilePhone = mobilePhone, TokenChannel.SMS).right()

        every {
            accountRepositoryMock.findByIdOrNull(accountId)
        } returns null

        val partialAccount = PartialAccount(
            id = accountId,
            name = "",
            emailAddress = EmailAddress(email = ""),
            status = AccountStatus.ACTIVE,
            statusUpdated = null,
            role = Role.GUEST,
            groups = listOf(),
            registrationType = RegistrationType.FULL,
            subscriptionType = SubscriptionType.PIX,
        )

        every {
            accountRepositoryMock.findPartialAccountByIdOrNull(accountId)
        } returns partialAccount

        val dateTime = getZonedDateTime()
        val validateTokenRequest = ValidateTokenRequest(
            otpId = msisdnAuthId,
            token = tokenKey.value,
        )

        val result = withGivenDateTime(dateTime) {
            msisdnAuthService.validateToken(
                validateTokenRequest,
            )
        }

        result.isRight() shouldBe true
        result.map { (mobilePhone, accountId) ->
            mobilePhone shouldBe mobilePhone
            accountId shouldBe accountId
        }

        val slot = slot<PartialAccount>()
        verify {
            crmService.upsertContact(capture(slot))
        }

        slot.captured shouldBe partialAccount
    }

    @Test
    fun `deve retornar um JWT quando validar o token de whatsApp`() {
        val accountId = AccountId("Account-123-456")
        val msisdnAuthId = MsisdnAuthId("MSISDN_AUTH-123-456")
        msisdnAuthRepository.save(
            MsisdnAuth(
                id = msisdnAuthId,
                accountId = accountId,
                createdAt = ZonedDateTime.now(),
            ),
        )
        every {
            tokenServiceMock.validateToken(tokenKey.copy(accountId = accountId), TokenType.MOBILE_PHONE)
        } returns Exception().left()

        every {
            tokenServiceMock.validateToken(tokenKey.copy(accountId = accountId), TokenType.WHATSAPP)
        } returns TokenData.of(mobilePhone = mobilePhone, TokenChannel.WHATSAPP).right()

        every {
            accountRepositoryMock.findByIdOrNull(accountId)
        } returns null

        val partialAccount = PartialAccount(
            id = accountId,
            name = "",
            emailAddress = EmailAddress(email = ""),
            status = AccountStatus.ACTIVE,
            statusUpdated = null,
            role = Role.GUEST,
            groups = listOf(),
            registrationType = RegistrationType.FULL,
            subscriptionType = SubscriptionType.PIX,
        )

        every {
            accountRepositoryMock.findPartialAccountByIdOrNull(accountId)
        } returns partialAccount

        val dateTime = getZonedDateTime()
        val validateTokenRequest = ValidateTokenRequest(
            otpId = msisdnAuthId,
            token = tokenKey.value,
        )

        val result = withGivenDateTime(dateTime) {
            msisdnAuthService.validateToken(
                validateTokenRequest,
            )
        }

        result.isRight() shouldBe true
        result.map { (mobilePhone, accountId) ->
            mobilePhone shouldBe mobilePhone
            accountId shouldBe accountId
        }

        verify {
            tokenServiceMock.validateToken(tokenKey.copy(accountId = accountId), TokenType.WHATSAPP)
            tokenServiceMock.validateToken(tokenKey.copy(accountId = accountId), TokenType.MOBILE_PHONE)
        }
    }
}