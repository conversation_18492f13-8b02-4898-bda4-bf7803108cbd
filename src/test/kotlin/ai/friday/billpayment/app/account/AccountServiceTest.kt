package ai.friday.billpayment.app.account

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.accountRegisterDataWithAddress
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.CrmContact
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.WalletFixture
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import java.time.Duration
import java.util.stream.Stream
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

internal class AccountServiceTest {

    val accountRepository = spyk(
        AccountDbRepository(
            accountDAO = mockk(),
            partialAccountDAO = mockk(),
            paymentMethodDAO = mockk(),
            nsuDAO = mockk(),
            transactionDynamo = mockk(),
        ),
    ) {
        every { save(any()) } just Runs
    }
    val accountRegisterRepository = mockk<AccountRegisterRepository>()
    val crmService = mockk<CrmService>(relaxed = true)
    val deviceFingerprintService = mockk<DeviceFingerprintService>()
    val accountService =
        AccountService(
            accountConfigurationService = mockk(),
            accountRepository = accountRepository,
            crmService = crmService,
            chatbotMessagePublisher = mockk(),
            notificationAdapter = mockk(),
            walletRepository = mockk(),
        )
    val accountRegisterService = AccountRegisterService(
        accountRegisterRepository = accountRegisterRepository,
        livenessService = mockk(),
        userFilesConfiguration = mockk(),
    )
    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet(
        walletFounder = walletFixture.founder.copy(
            accountId = AccountId(ACCOUNT_ID),
        ),
    )

    @Test
    fun `deve retornar Rio de Janeiro não encontar o accountRegisterData`() {
        every {
            accountRegisterRepository.findByAccountId(any())
        } answers { throw ItemNotFoundException("Nao existe!") }

        accountRegisterService.readAccountCity(wallet.founder.accountId) shouldBe "RIO DE JANEIRO"
    }

    @Test
    fun `deve retornar Rio de Janeiro quando o endereço for null`() {
        every {
            accountRegisterRepository.findByAccountId(any())
        } returns accountRegisterDataWithAddress.copy(
            address = null,
        )

        accountRegisterService.readAccountCity(wallet.founder.accountId) shouldBe "RIO DE JANEIRO"
    }

    @Test
    fun `deve retornar Rio de Janeiro quando a cidade for uma string vazia`() {
        every {
            accountRegisterRepository.findByAccountId(any())
        } returns accountRegisterDataWithAddress.copy(
            address = accountRegisterDataWithAddress.address!!.copy(
                city = "       ",
            ),
        )
        accountRegisterService.readAccountCity(wallet.founder.accountId) shouldBe "RIO DE JANEIRO"
    }

    @Test
    fun `deve retornar o nome da ciadade quando a cidade existir`() {
        every {
            accountRegisterRepository.findByAccountId(any())
        } returns accountRegisterDataWithAddress.copy(
            address = accountRegisterDataWithAddress.address!!.copy(
                city = "São Paulo",
            ),
        )
        accountRegisterService.readAccountCity(wallet.founder.accountId) shouldBe "São Paulo"
    }

    @ParameterizedTest
    @MethodSource("addGroups")
    fun `should add groups on account`(
        initial: List<AccountGroup>,
        toBeAdded: List<AccountGroup>,
        expected: List<AccountGroup>,
        shouldSave: Boolean,
    ) {
        every { accountRepository.findById(AccountId(ACCOUNT_ID)) } returns ACCOUNT.copy(
            accountId = AccountId(ACCOUNT_ID),
            configuration = ACCOUNT.configuration.copy(groups = initial),
        )

        every {
            crmService.findContact(AccountId(ACCOUNT_ID))
        } returns CrmContact(
            accountId = AccountId(ACCOUNT_ID),
            emailAddress = EmailAddress("<EMAIL>"),
            accountType = UserAccountType.FULL_ACCOUNT,
            subscriptionType = SubscriptionType.PIX,
        )

        accountService.addGroupsToAccount(
            AccountId(ACCOUNT_ID),
            toBeAdded,
        )

        verify(exactly = if (shouldSave) 1 else 0) {
            accountRepository.save(
                withArg {
                    it.configuration.groups shouldContainExactlyInAnyOrder expected
                },
            )
            crmService.upsertContact(
                withArg<Account> {
                    it.accountId shouldBe AccountId(ACCOUNT_ID)
                    it.configuration.groups shouldContainExactlyInAnyOrder expected
                },
            )
        }
    }

    @ParameterizedTest
    @MethodSource("addGroups")
    fun `should add groups on partial account`(
        initial: List<AccountGroup>,
        toBeAdded: List<AccountGroup>,
        expected: List<AccountGroup>,
        shouldSave: Boolean,
    ) {
        val accountId = AccountId(ACCOUNT_ID)
        every {
            accountRepository.updatePartialAccountGroups(accountId, any())
        } answers {
            mockk() {
                every { id } returns accountId
                every { groups } returns expected
            }
        }
        every { accountRepository.findByIdOrNull(accountId) } returns null
        every { accountRepository.findPartialAccountById(accountId) } returns
            PartialAccount(
                id = accountId,
                name = "fake",
                emailAddress = EmailAddress(email = "<EMAIL>"),
                status = AccountStatus.REGISTER_INCOMPLETE,
                statusUpdated = null,
                role = Role.GUEST,
                groups = initial,
                registrationType = RegistrationType.FULL,
                subscriptionType = SubscriptionType.PIX,
            )

        every {
            crmService.findContact(accountId)
        } returns CrmContact(
            accountId = accountId,
            emailAddress = EmailAddress("<EMAIL>"),
            accountType = UserAccountType.FULL_ACCOUNT,
            subscriptionType = SubscriptionType.PIX,
        )

        accountService.addGroupsToPartialAccount(
            accountId,
            toBeAdded,
        )

        val slot = slot<PartialAccount>()
        verify(exactly = if (shouldSave) 1 else 0) {
            accountRepository.updatePartialAccountGroups(
                accountId,
                withArg {
                    it shouldContainExactlyInAnyOrder expected
                },
            )
            crmService.upsertContact(capture(slot))
        }
        if (shouldSave) {
            with(slot.captured) {
                id shouldBe accountId
                groups shouldContainExactlyInAnyOrder expected
            }
        }
    }

    @Test
    fun `deve retornar todos os usuários de um determinado grupo`() {
        val secondAccount = ACCOUNT.copy(
            accountId = AccountId("Account2"),
            configuration = ACCOUNT.configuration.copy(groups = listOf(AccountGroup.ALPHA)),
        )

        val firstAccount = ACCOUNT.copy(
            configuration = ACCOUNT.configuration.copy(groups = listOf(AccountGroup.ALPHA)),
        )
        every { accountRepository.findAllAccountsActivatedSince(any()) } returns listOf(
            firstAccount,
            secondAccount,
        )

        val accounts = accountService.findAllUsersWithGroup(AccountGroup.ALPHA)

        accounts[0] shouldBe firstAccount
        accounts[1] shouldBe secondAccount
    }

    @Test
    fun `deve mudar o payment de uma conta encerrada quando ela se tornar inadimplente`() {
        val closedAccount = ACCOUNT.copy(status = AccountStatus.CLOSED, paymentStatus = AccountPaymentStatus.UpToDate)

        every { accountRepository.findByIdOrNull(closedAccount.accountId) } returns closedAccount

        accountService.attemptToUpdateAccountStatus(
            UpdateAccountStatusRequest.Block(
                accountId = closedAccount.accountId,
                expiredFor = Duration.ofDays(7),
            ),
        )

        val capturedAccount = slot<Account>()

        verify(exactly = 1) {
            accountRepository.save(capture(capturedAccount))
        }

        capturedAccount.captured.status shouldBe AccountStatus.CLOSED
        capturedAccount.captured.paymentStatus shouldBe AccountPaymentStatus.Overdue
    }

    @Test
    fun `deve mudar o status de uma conta encerrada quando ela se tornar adimplente`() {
        val closedAccount = ACCOUNT.copy(status = AccountStatus.CLOSED, paymentStatus = AccountPaymentStatus.PastDue)

        every { accountRepository.findByIdOrNull(closedAccount.accountId) } returns closedAccount

        accountService.attemptToUpdateAccountStatus(UpdateAccountStatusRequest.Unblock(accountId = closedAccount.accountId))

        val capturedAccount = slot<Account>()

        verify(exactly = 1) {
            accountRepository.save(capture(capturedAccount))
        }

        capturedAccount.captured.status shouldBe AccountStatus.CLOSED
        capturedAccount.captured.paymentStatus shouldBe AccountPaymentStatus.UpToDate
    }

    @Test
    fun `deve mudar o status de uma conta ativa quando ela se tornar inadimplente há menos que 7 dias`() {
        val activeAccount = ACCOUNT.copy(status = AccountStatus.ACTIVE)

        every { accountRepository.findByIdOrNull(activeAccount.accountId) } returns activeAccount

        accountService.attemptToUpdateAccountStatus(
            UpdateAccountStatusRequest.Block(
                accountId = activeAccount.accountId,
                expiredFor = Duration.ofDays(6),
            ),
        )

        val capturedAccount = slot<Account>()

        verify(exactly = 1) {
            accountRepository.save(capture(capturedAccount))
        }

        capturedAccount.captured.paymentStatus shouldBe AccountPaymentStatus.PastDue
        capturedAccount.captured.status shouldBe AccountStatus.BLOCKED
    }

    @Test
    fun `deve mudar o status de uma conta ativa quando ela se tornar inadimplente há 7 ou mais dias`() {
        val activeAccount = ACCOUNT.copy(status = AccountStatus.ACTIVE)

        every { accountRepository.findByIdOrNull(activeAccount.accountId) } returns activeAccount

        accountService.attemptToUpdateAccountStatus(
            UpdateAccountStatusRequest.Block(
                accountId = activeAccount.accountId,
                expiredFor = Duration.ofDays(7),
            ),
        )

        val capturedAccount = slot<Account>()

        verify(exactly = 1) {
            accountRepository.save(capture(capturedAccount))
        }

        capturedAccount.captured.paymentStatus shouldBe AccountPaymentStatus.Overdue
        capturedAccount.captured.status shouldBe AccountStatus.BLOCKED
    }

    @Test
    fun `deve mudar o status de uma conta inadimplente quando ela se tornar adimplente`() {
        val blockedAccount = ACCOUNT.copy(status = AccountStatus.BLOCKED)

        every { accountRepository.findByIdOrNull(blockedAccount.accountId) } returns blockedAccount

        accountService.attemptToUpdateAccountStatus(
            UpdateAccountStatusRequest.Unblock(
                accountId = blockedAccount.accountId,
            ),
        )

        val capturedAccount = slot<Account>()

        verify(exactly = 1) {
            accountRepository.save(capture(capturedAccount))
        }

        capturedAccount.captured.status shouldBe AccountStatus.ACTIVE
    }

    @Test
    fun `deve mudar o status de pagamento de uma conta adimplente quando ele não está coerente com o status da conta`() {
        val activeAccount = ACCOUNT.copy(status = AccountStatus.ACTIVE, paymentStatus = AccountPaymentStatus.PastDue)

        every { accountRepository.findByIdOrNull(activeAccount.accountId) } returns activeAccount

        accountService.attemptToUpdateAccountStatus(
            UpdateAccountStatusRequest.Unblock(accountId = activeAccount.accountId),
        )

        val capturedAccount = slot<Account>()

        verify(exactly = 1) {
            accountRepository.save(capture(capturedAccount))
        }

        capturedAccount.captured.status shouldBe AccountStatus.ACTIVE
        capturedAccount.captured.paymentStatus shouldBe AccountPaymentStatus.UpToDate
    }

    @Test
    fun `deve mudar o status de pagamento de uma conta inadimplente quando ele não está coerente com o status da conta`() {
        val blockedAccount = ACCOUNT.copy(status = AccountStatus.BLOCKED, paymentStatus = AccountPaymentStatus.UpToDate)

        every { accountRepository.findByIdOrNull(blockedAccount.accountId) } returns blockedAccount

        accountService.attemptToUpdateAccountStatus(
            UpdateAccountStatusRequest.Block(
                accountId = blockedAccount.accountId,
                expiredFor = Duration.ofDays(3),
            ),
        )

        val capturedAccount = slot<Account>()

        verify(exactly = 1) {
            accountRepository.save(capture(capturedAccount))
        }

        capturedAccount.captured.status shouldBe AccountStatus.BLOCKED
        capturedAccount.captured.paymentStatus shouldBe AccountPaymentStatus.PastDue
    }

    companion object {
        @JvmStatic
        fun addGroups(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    listOf<String>(),
                    listOf(AccountGroup.ALPHA, AccountGroup.SUBSCRIPTION_STANDARD_PLAN),
                    listOf(AccountGroup.ALPHA, AccountGroup.SUBSCRIPTION_STANDARD_PLAN),
                    true,
                ),
                Arguments.arguments(
                    listOf(AccountGroup.ALPHA),
                    listOf(AccountGroup.BETA),
                    listOf(AccountGroup.ALPHA, AccountGroup.BETA),
                    true,
                ),
                Arguments.arguments(
                    listOf(AccountGroup.ALPHA),
                    listOf(AccountGroup.ALPHA),
                    listOf(AccountGroup.ALPHA),
                    false,
                ),
                Arguments.arguments(
                    listOf<AccountGroup>(),
                    listOf<AccountGroup>(),
                    listOf<AccountGroup>(),
                    false,
                ),
            )
        }
    }
}