package ai.friday.billpayment.app.account

import ai.friday.billpayment.ACCOUNT
import io.kotest.assertions.withClue
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldNotContainAll
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeSameInstanceAs
import io.kotest.matchers.types.shouldNotBeSameInstanceAs
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

internal class AccountGroupTimelineEntriesFilterTest {
    val filter = AccountGroupFilter(
        configuration = AccountGroupFilterConfiguration(
            enabled = true,
            versionRestrictions = listOf(),
        ),
    )

    @Nested
    @DisplayName("when filter strategy is EXCLUDE")
    inner class ExcludeScope {

        @Test
        fun `should result same account groups if filter is empty`() {
            val noFilter = AccountGroupFilter(
                configuration = AccountGroupFilterConfiguration(enabled = true, versionRestrictions = emptyList()),
            )

            listOf(ANDROID, IOS).forEach { platform ->
                noFilter.filterGroupsByVersion(filterContext(emptyList(), "1.1.1", platform)) shouldBe emptyList()
                noFilter.filterGroupsByVersion(filterContext(allGroups, "1.1.1", platform)) shouldNotBeSameInstanceAs allGroups
            }
        }

        @Test
        fun `should result same account groups if filter is disabled`() {
            // exclude nothing
            val disabledExcludeFilter = AccountGroupFilter(
                configuration = AccountGroupFilterConfiguration(enabled = false, versionRestrictions = alphaBetaExcludeRestrictions("999.999.999")),
            )

            disabledExcludeFilter.filterGroupsByVersion(filterContext(allGroups, "1.1.1", ANDROID)) shouldBeSameInstanceAs allGroups
            disabledExcludeFilter.filterGroupsByVersion(filterContext(allGroups, "1.1.1", IOS)) shouldBeSameInstanceAs allGroups
        }

        @Test
        fun `should result same account groups if platform is lowercase, camelCase or uppercase`() {
            val noFilter = AccountGroupFilter(
                configuration = AccountGroupFilterConfiguration(enabled = true, versionRestrictions = alphaBetaExcludeRestrictions("999.999.999")),
            )

            listOf("android", "ANDROID", "AnDroid", "androiD").forEach { platform ->
                noFilter.filterGroupsByVersion(filterContext(allGroups, "1.1.1", platform)) shouldNotBeSameInstanceAs allGroups
            }
        }

        @Test
        fun `should remove groups when version is not valid`() {
            val filter = AccountGroupFilter(
                configuration = AccountGroupFilterConfiguration(
                    enabled = true,
                    versionRestrictions = alphaBetaExcludeRestrictions("999.999.999"),
                ),
            )

            listOf(
                "v1.1.1",
                "1.1.1-alpha",
                "1.0",
                "1.0",
                "1-1-1",
                "1/1/1",
                "100001",
                "beta",
            ).forEach { version ->
                withClue("version $version") {
                    filter.filterGroupsByVersion(filterContext(allGroups, version, ANDROID)) shouldContainExactly allGroupsExceptAlphaBeta
                    filter.filterGroupsByVersion(filterContext(allGroups, version, IOS)) shouldContainExactly allGroupsExceptAlphaBeta
                }
            }
        }

        @Test
        fun `should remove groups when platform is not valid`() {
            val filter = AccountGroupFilter(
                configuration = AccountGroupFilterConfiguration(
                    enabled = true,
                    versionRestrictions = alphaBetaExcludeRestrictions("0.0.1"),
                ),
            )

            listOf("WINDOWS", "UBUNTU", "MACOS").forEach { platform ->
                filter.filterGroupsByVersion(filterContext(allGroups, "9.9.9", platform)) shouldContainExactly allGroupsExceptAlphaBeta
            }
        }

        @Test
        fun `should remove groups when version is less than restriction`() {
            val filter = AccountGroupFilter(
                configuration = AccountGroupFilterConfiguration(
                    enabled = true,
                    versionRestrictions = alphaBetaExcludeRestrictions("999.999.999"),
                ),
            )

            listOf(
                "1.0.0", // old version
                "1.9999.9999", // old version with high minor and patch
                "999.999.0", // old version with low patch
                "999.999.998", // previous version
            ).forEach { version ->
                withClue("version $version") {
                    filter.filterGroupsByVersion(filterContext(allGroups, version, ANDROID)) shouldContainExactly allGroupsExceptAlphaBeta
                    filter.filterGroupsByVersion(filterContext(allGroups, version, IOS)) shouldContainExactly allGroupsExceptAlphaBeta
                }
            }
        }

        @Test
        fun `should keep groups when user version is greater than or equal to restriction version`() {
            val filter = AccountGroupFilter(
                configuration = AccountGroupFilterConfiguration(
                    enabled = true,
                    versionRestrictions = alphaBetaExcludeRestrictions("5.6.7"),
                ),
            )

            listOf(
                "5.6.7", // equal
                "5.6.8", // newer patch
                "5.6.10", // newer patch testing string comparison
                "5.7.0", // newer minor
                "6.0.0", // newer major
                "v5.6.7", // equal with prefix
                "v.5.6.7", // equal with prefix
                "5.6.7-RC0", // equal with sufix
                "5.6.7-RC1", // equal with sufix
                "5.6.7-RC11", // equal with sufix
                "v5.6.7-RC1", // equal with prefix + sufix
                "v.5.6.7-RC1", // equal with prefix + sufix
            ).forEach { version ->
                withClue("version $version") {
                    filter.filterGroupsByVersion(filterContext(allGroups, version, ANDROID)) shouldContainExactly allGroups
                    filter.filterGroupsByVersion(filterContext(allGroups, version, IOS)) shouldContainExactly allGroups
                }
            }
        }
    }

    @Nested
    @DisplayName("when filter strategy is INCLUDE")
    inner class IncludeScope {
        @Test
        fun `should result same account groups if filter is empty`() {
            // include nothing
            val noFilters = AccountGroupFilter(
                configuration = AccountGroupFilterConfiguration(enabled = true, versionRestrictions = emptyList()),
            )

            listOf(ANDROID, IOS).forEach { platform ->
                noFilters.filterGroupsByVersion(filterContext(emptyList(), "1.1.1", platform)) shouldBe emptyList()
                noFilters.filterGroupsByVersion(filterContext(allGroupsExceptAlphaBeta, "1.1.1", platform)) shouldNotBeSameInstanceAs allGroupsExceptAlphaBeta
            }
        }

        @Test
        fun `should result same account groups if filter is disabled`() {
            // include nothing
            val disabledIncludeFilter = AccountGroupFilter(
                configuration = AccountGroupFilterConfiguration(enabled = false, versionRestrictions = alphaBetaIncludeRestrictions("0.0.0")),
            )

            disabledIncludeFilter.filterGroupsByVersion(filterContext(allGroupsExceptAlphaBeta, "1.1.1", ANDROID)) shouldBeSameInstanceAs allGroupsExceptAlphaBeta
            disabledIncludeFilter.filterGroupsByVersion(filterContext(allGroupsExceptAlphaBeta, "1.1.1", IOS)) shouldBeSameInstanceAs allGroupsExceptAlphaBeta
        }

        @Test
        fun `should result same account groups if platform is lowercase, camelCase or uppercase`() {
            val noFilter = AccountGroupFilter(
                configuration = AccountGroupFilterConfiguration(enabled = true, versionRestrictions = alphaBetaIncludeRestrictions("0.0.0")),
            )

            listOf("android", "ANDROID", "AnDroid", "androiD").forEach { platform ->
                noFilter.filterGroupsByVersion(filterContext(allGroupsExceptAlphaBeta, "1.1.1", platform)) shouldNotBeSameInstanceAs allGroupsExceptAlphaBeta
            }
        }

        @Test
        fun `should keep groups when version is less than restriction`() {
            val filter = AccountGroupFilter(
                configuration = AccountGroupFilterConfiguration(
                    enabled = true,
                    versionRestrictions = alphaBetaIncludeRestrictions("11.5.1"),
                ),
            )

            listOf(
                "1.0.0", // old version // dont include
                "1.9999.9999", // old version with high minor and patch // dont include
                "11.5.0", // old version with low patch  // dont include
                "11.4.999", // previous version // dont include
            ).forEach { version ->
                withClue("version $version") {
                    filter.filterGroupsByVersion(filterContext(allGroups, version, ANDROID)) shouldContainExactly allGroups
                    filter.filterGroupsByVersion(filterContext(allGroups, version, IOS)) shouldContainExactly allGroups
                }
            }
        }

        @Test
        fun `should add groups when version is greater or equal than restriction`() {
            val filter = AccountGroupFilter(
                configuration = AccountGroupFilterConfiguration(
                    enabled = true,
                    versionRestrictions = alphaBetaIncludeRestrictions("11.5.0"),
                ),
            )

            allGroupsExceptAlphaBeta.shouldNotContainAll(alphaBetaGroups)

            listOf(
                "11.5.0", // same version // include
                "11.5.1", // next patch version // include
                "11.6.0", // next minor version // include
                "12.0.0", // next major version // include
            ).forEach { version ->
                withClue("version $version") {
                    filter.filterGroupsByVersion(filterContext(allGroupsExceptAlphaBeta, version, ANDROID)) shouldContainExactlyInAnyOrder allGroups
                    filter.filterGroupsByVersion(filterContext(allGroupsExceptAlphaBeta, version, IOS)) shouldContainExactlyInAnyOrder allGroups
                }
            }
        }
    }

    companion object {
        private const val ANDROID = "ANDROID"
        private const val IOS = "IOS"

        private val alphaBetaGroups = listOf(AccountGroup.ALPHA, AccountGroup.BETA)

        val allGroups = AccountGroup.entries

        val allGroupsExceptAlphaBeta = allGroups - alphaBetaGroups

        fun alphaBetaExcludeRestrictions(version: String) = listOf(
            AccountGroupRestriction(version, ANDROID, AccountGroupRestrictionStrategy.EXCLUDE, AccountGroup.ALPHA),
            AccountGroupRestriction(version, IOS, AccountGroupRestrictionStrategy.EXCLUDE, AccountGroup.ALPHA),
            AccountGroupRestriction(version, ANDROID, AccountGroupRestrictionStrategy.EXCLUDE, AccountGroup.BETA),
            AccountGroupRestriction(version, IOS, AccountGroupRestrictionStrategy.EXCLUDE, AccountGroup.BETA),
        )

        fun alphaBetaIncludeRestrictions(version: String) = listOf(
            AccountGroupRestriction(version, ANDROID, AccountGroupRestrictionStrategy.INCLUDE, AccountGroup.ALPHA),
            AccountGroupRestriction(version, IOS, AccountGroupRestrictionStrategy.INCLUDE, AccountGroup.ALPHA),
            AccountGroupRestriction(version, ANDROID, AccountGroupRestrictionStrategy.INCLUDE, AccountGroup.BETA),
            AccountGroupRestriction(version, IOS, AccountGroupRestrictionStrategy.INCLUDE, AccountGroup.BETA),
        )

        fun filterContext(groups: List<AccountGroup>, version: String, platform: String, account: Account = ACCOUNT) =
            GroupFilterContext(
                platform = platform,
                version = version,
                account = account.copy(
                    configuration = account.configuration.copy(groups = groups),
                ),
            )
    }
}