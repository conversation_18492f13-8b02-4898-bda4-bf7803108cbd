package ai.friday.billpayment.app.account

import ai.friday.billpayment.app.integrations.AccountConfigurationRepository
import ai.friday.billpayment.integration.ACCOUNT_ID
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.collections.shouldNotContain
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

internal class AccountConfigurationServiceTest {
    val accountConfigurationRepository = mockk<AccountConfigurationRepository>()
    val accountConfigurationService = AccountConfigurationService(accountConfigurationRepository)

    val accountConfigurationMock = AccountConfiguration(
        accountId = AccountId(ACCOUNT_ID),
        pushNotificationTokens = setOf(),
        receiveMonthlyStatement = false,
        freeOfFridaySubscription = false,

    )

    val pushNotificationToken = "298fh2f27h08gu4f02847fg249fh29f8h248fy2g4f92"

    @Test
    fun `deve salvar um token`() {
        every { accountConfigurationRepository.find(any()) } returns accountConfigurationMock
        every { accountConfigurationRepository.save(any()) } just Runs

        accountConfigurationService.addPushNotificationToken(pushNotificationToken, AccountId(ACCOUNT_ID))

        verify {
            accountConfigurationRepository.save(
                withArg {
                    it.pushNotificationTokens.shouldContainExactly(pushNotificationToken)
                },
            )
        }
    }

    @Test
    fun `deve recuperar os tokens`() {
        every { accountConfigurationRepository.find(any()) } returns accountConfigurationMock.copy(
            pushNotificationTokens = setOf(
                pushNotificationToken,
            ),
        )

        accountConfigurationService.find(AccountId(ACCOUNT_ID)).pushNotificationTokens shouldContainExactly listOf(
            pushNotificationToken,
        )

        verify { accountConfigurationRepository.find(any()) }
    }

    @Test
    fun `deve remover um token da lista`() {
        every { accountConfigurationRepository.find(any()) } returns accountConfigurationMock.copy(
            pushNotificationTokens = setOf(pushNotificationToken, "r2938ru2398ru2398ru2", "f20j4f029jf09j4f029jf2"),
        )
        every { accountConfigurationRepository.save(any()) } just Runs

        val accountConfiguration =
            accountConfigurationService.removePushNotificationToken(pushNotificationToken, AccountId(ACCOUNT_ID))

        accountConfiguration.pushNotificationTokens.size shouldBe 2

        verify {
            accountConfigurationRepository.save(
                withArg {
                    it.pushNotificationTokens.shouldNotContain(pushNotificationToken)
                    it.pushNotificationTokens.size shouldBe 2
                },
            )
        }
    }

    @Test
    fun `não deve salvar um token já existente`() {
        every { accountConfigurationRepository.find(any()) } returns accountConfigurationMock.copy(
            pushNotificationTokens = setOf(pushNotificationToken),
        )

        val accountConfiguration =
            accountConfigurationService.addPushNotificationToken(pushNotificationToken, AccountId(ACCOUNT_ID))

        accountConfiguration.pushNotificationTokens.size shouldBe 1

        verify(exactly = 0) {
            accountConfigurationRepository.save(any())
        }
    }

    @Test
    fun `não deve remover um token não existente`() {
        every { accountConfigurationRepository.find(any()) } returns accountConfigurationMock

        val accountConfiguration =
            accountConfigurationService.removePushNotificationToken(pushNotificationToken, AccountId(ACCOUNT_ID))

        accountConfiguration.pushNotificationTokens.size shouldBe 0

        verify(exactly = 0) {
            accountConfigurationRepository.save(any())
        }
    }
}