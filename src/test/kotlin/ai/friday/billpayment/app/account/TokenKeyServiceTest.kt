package ai.friday.billpayment.app.account

import DynamoDBUtils
import ai.friday.billpayment.adapters.dynamodb.TokenDbRepository
import ai.friday.billpayment.adapters.dynamodb.TokenDynamoDAO
import ai.friday.billpayment.adapters.lock.InternalLockProvider
import ai.friday.billpayment.adapters.micronaut.TokenOnboardingConfigurationMicronaut
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.SmsSender
import ai.friday.billpayment.app.integrations.TokenData
import ai.friday.billpayment.app.integrations.TokenDataWithExpiration
import ai.friday.billpayment.app.integrations.TokenType
import ai.friday.billpayment.email
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.mobilePhone
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import arrow.core.getOrElse
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.slot
import io.mockk.verify
import java.time.Duration.ofMinutes
import java.time.Duration.ofSeconds
import java.time.Instant
import java.time.format.DateTimeFormatter
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class TokenKeyServiceTest {
    private val dynamoClient = DynamoDBUtils.setupDynamoDB()
    private val tokenDynamoDAO = TokenDynamoDAO(dynamoClient)
    private val tokenRepository = TokenDbRepository(client = tokenDynamoDAO)
    private val smsSender: SmsSender = mockk(relaxUnitFun = true)
    private val lockProvider = mockk<InternalLockProvider> {
        every { acquireLock(any(), simultaneousLock = 20) } returns mockk()
    }
    private val notificationAdapter: NotificationAdapter = mockk(relaxUnitFun = true)

    private val tokenService = object : TokenService(
        tokenRepository = tokenRepository,
        smsSender = smsSender,
        notificationAdapter = notificationAdapter,
        configuration = configuration,
        issuedTokenLock = lockProvider,
    ) {}

    private val accountId = AccountId(ACCOUNT_ID)

    private val expectedToken = "123455"

    private val validTokenKey = TokenKey(accountId, expectedToken)

    private val invalidTokenKey = TokenKey(accountId, "651654")

    @BeforeEach
    fun setup() {
        mockkObject(TokenGenerator)
        every { TokenGenerator.generate(any(), any()) } returns expectedToken
    }

    @ParameterizedTest
    @MethodSource("mobilePhoneTokens")
    fun `deve criar e enviar um token corretamente para um telefone`(
        tokenData: TokenData,
        duration: Long,
    ) {
        val channel = if (tokenData.type == TokenType.MOBILE_PHONE) TokenChannel.SMS else TokenChannel.WHATSAPP
        val result = tokenService.issueToken(mobilePhone, accountId, channel)

        result.isRight() shouldBe true
        result.map {
            it.duration shouldBe ofSeconds(duration)
            it.accountId shouldBe accountId
        }

        val token = slot<String>()

        if (channel == TokenChannel.SMS) {
            verify(exactly = 1) { smsSender.send(mobilePhone, capture(token)) }
            verify(exactly = 0) { notificationAdapter.notifyToken(any(), any(), any()) }
        } else {
            verify(exactly = 1) { notificationAdapter.notifyToken(accountId, any(), capture(token)) }
            verify(exactly = 0) { smsSender.send(any(), any()) }
        }

        token.captured shouldContain validTokenKey.value

        val issuedToken = tokenRepository.retrieveValidated(
            validTokenKey,
            tokenType = tokenData.type,
            errorLimit = 1,
        )

        issuedToken.isRight() shouldBe true
        issuedToken.map {
            it.value shouldBe mobilePhone.msisdn
        }
    }

    @ParameterizedTest
    @MethodSource("mobilePhoneTokens")
    fun `não deve gerar um novo token nem enviar notificação quando não encontrar um lock`(
        tokenData: TokenData,
        expiration: Long,
    ) {
        val channel = if (tokenData.type == TokenType.MOBILE_PHONE) TokenChannel.SMS else TokenChannel.WHATSAPP

        every { lockProvider.acquireLock(any(), simultaneousLock = 20) } returns null
        val result = tokenService.issueToken(mobilePhone, accountId, channel)

        result.isLeft() shouldBe true
        result.mapLeft {
            it shouldBe IssueTokenMaxLimitExceeded()
        }
        result.map {
            it.duration shouldBe ofSeconds(expiration)
            it.accountId shouldBe accountId
        }

        verify(exactly = 0) { smsSender.send(mobilePhone, any()) }
        verify(exactly = 0) { notificationAdapter.notifyToken(any(), any(), any()) }

        val issuedToken = tokenRepository.retrieveValidated(
            validTokenKey,
            tokenType = tokenData.type,
            errorLimit = 1,
        )

        issuedToken.isLeft() shouldBe true
        issuedToken.map {
            it.value shouldBe mobilePhone.msisdn
        }
    }

    @ParameterizedTest
    @MethodSource("mobilePhoneTokens")
    fun `não deve gerar um novo token e notificação com mesmo token quando tiver um lock disponível`(
        tokenData: TokenData,
        expiration: Long,
    ) {
        every { TokenGenerator.generate(any(), any()) } returns "123455" andThen "123456"
        val channel = if (tokenData.type == TokenType.MOBILE_PHONE) TokenChannel.SMS else TokenChannel.WHATSAPP
        val firstToken = tokenService.issueToken(mobilePhone, accountId, channel)
        firstToken.isRight() shouldBe true

        val result = tokenService.issueToken(mobilePhone, accountId, channel)

        result.isLeft() shouldBe true

        result.map {
            it.duration shouldBe ofSeconds(expiration)
            it.accountId shouldBe accountId
        }

        val slot = mutableListOf<String>()

        if (channel == TokenChannel.SMS) {
            verify(exactly = 2) { smsSender.send(mobilePhone, capture(slot)) }
        } else {
            verify(exactly = 2) { notificationAdapter.notifyToken(any(), any(), capture(slot)) }
        }

        slot[0] shouldBe slot[1]
    }

    @Test
    fun `deve criar um token fake quando for um telefone de teste`() {
        val now = getZonedDateTime()
        val fakeToken = now.format(DateTimeFormatter.ofPattern("ddMMyy"))

        val result = withGivenDateTime(now) {
            tokenService.issueToken(testMobilePhone, accountId, TokenChannel.SMS)
        }

        result.isRight() shouldBe true
        result.map {
            it.duration shouldBe ofMinutes(1)
            it.accountId shouldBe accountId
        }

        verify {
            smsSender wasNot called
        }

        val issuedToken = tokenRepository.retrieveValidated(
            TokenKey(accountId = accountId, value = fakeToken),
            tokenType = TokenType.MOBILE_PHONE,
            errorLimit = 1,
        )

        issuedToken.isRight() shouldBe true
        issuedToken.map { it: TokenDataWithExpiration ->
            it.value shouldBe TokenData.of(testMobilePhone, TokenChannel.SMS).value
        }
    }

    @Test
    fun `deve criar um token fixo quando for um telefone de teste da apple`() {
        val appleToken = "261213"

        val result = tokenService.issueToken(appleMobilePhone, accountId, TokenChannel.SMS)

        result.isRight() shouldBe true
        result.map {
            it.duration shouldBe ofMinutes(1)
            it.accountId shouldBe accountId
        }

        verify {
            smsSender wasNot called
        }

        val issuedToken = tokenRepository.retrieveValidated(
            TokenKey(accountId = accountId, value = appleToken),
            tokenType = TokenType.MOBILE_PHONE,
            errorLimit = 1,
        )

        issuedToken.isRight() shouldBe true
        issuedToken.map { it: TokenDataWithExpiration ->
            it.value shouldBe TokenData.of(appleMobilePhone, TokenChannel.SMS).value
        }
    }

    @Test
    fun `deve enviar um token por e-mail na recuperação de senha`() {
        val result = tokenService.issuePasswordRecoveryToken(email, accountId, DOCUMENT)

        result.isRight() shouldBe true
        result.map {
            it.duration shouldBe ofMinutes(5)
            it.accountId shouldBe accountId
        }
        val slot = slot<String>()
        verify(exactly = 1) {
            notificationAdapter.notifyForgotPasswordToken(
                accountId,
                DOCUMENT,
                email,
                capture(slot),
                ofMinutes(5),
            )
        }
        slot.captured shouldContain validTokenKey.value
        val issuedToken = tokenRepository.retrieveValidated(
            validTokenKey,
            tokenType = TokenType.EMAIL,
            errorLimit = 1,
        )

        issuedToken.isRight() shouldBe true

        issuedToken.map {
            it.value shouldBe TokenData.of(email).value
        }
    }

    @Test
    fun `deve enviar um token por e-mail`() {
        val expectedDuration = ofSeconds(configuration.emailVerificationDuration)
        val result = tokenService.issueToken(email, accountId)

        result.isRight() shouldBe true
        result.map {
            it.duration shouldBe expectedDuration
            it.accountId shouldBe accountId
        }
        val slot = slot<String>()
        verify(exactly = 1) {
            notificationAdapter.notifyEmailVerificationToken(
                accountId,
                email,
                capture(slot),
                expectedDuration,
            )
        }
        slot.captured shouldContain validTokenKey.value
        val issuedToken = tokenRepository.retrieveValidated(
            validTokenKey,
            tokenType = TokenType.EMAIL,
            errorLimit = 1,
        )

        issuedToken.isRight() shouldBe true

        issuedToken.map {
            it.value shouldBe TokenData.of(email).value
        }
    }

    @ParameterizedTest
    @MethodSource("mobilePhoneTokens")
    fun `deve enviar um novo token por telefone quando tiver passado o cooldown`(
        tokenData: TokenData,
        duration: Long,
    ) {
        val channel = if (tokenData.type == TokenType.MOBILE_PHONE) TokenChannel.SMS else TokenChannel.WHATSAPP
        val now = getZonedDateTime()

        tokenRepository.save(
            validTokenKey,
            TokenDataWithExpiration(tokenData, now.plusSeconds(duration).toEpochSecond()),
        )

        val result = withGivenDateTime(now.plusSeconds(configuration.maxCooldownDuration + 1)) {
            tokenService.issueToken(mobilePhone, accountId, channel)
        }

        result.isRight() shouldBe true
        result.map {
            it.duration shouldBe ofSeconds(duration)
            it.accountId shouldBe accountId
        }

        val token = slot<String>()

        if (channel == TokenChannel.SMS) {
            verify(exactly = 1) { smsSender.send(mobilePhone, capture(token)) }
            verify(exactly = 0) { notificationAdapter.notifyToken(any(), any(), any()) }
        } else {
            verify(exactly = 1) { notificationAdapter.notifyToken(accountId, any(), capture(token)) }
            verify(exactly = 0) { smsSender.send(any(), any()) }
        }

        token.captured shouldContain validTokenKey.value

        val issuedToken = tokenRepository.retrieveValidated(
            validTokenKey,
            tokenType = tokenData.type,
            errorLimit = 1,
        )

        issuedToken.isRight() shouldBe true
        issuedToken.map {
            it.value shouldBe mobilePhone.msisdn
        }
    }

    @Test
    fun `deve criar um novo token de recuperação de senha somente quando o atual tiver passado o cooldown`() {
        val now = getZonedDateTime()

        tokenRepository.save(
            validTokenKey,
            TokenDataWithExpiration(
                TokenData.of(email),
                expiration = now.plusSeconds(configuration.passwordRecoveryDuration).toEpochSecond(),
            ),
        )

        val result = withGivenDateTime(now.plusSeconds(configuration.maxCooldownDuration + 1)) {
            tokenService.issuePasswordRecoveryToken(email, accountId, DOCUMENT)
        }

        result.isRight() shouldBe true
        result.map {
            it.duration shouldBe ofMinutes(5)
            it.accountId shouldBe accountId
        }
        val slot = slot<String>()
        verify(exactly = 1) {
            notificationAdapter.notifyForgotPasswordToken(
                accountId,
                DOCUMENT,
                email,
                capture(slot),
                ofMinutes(5),
            )
        }
        slot.captured shouldContain validTokenKey.value
        val issuedToken = tokenRepository.retrieveValidated(
            validTokenKey,
            tokenType = TokenType.EMAIL,
            errorLimit = 1,
        )

        issuedToken.isRight() shouldBe true

        issuedToken.map {
            it.value shouldBe TokenData.of(email).value
        }
    }

    @Test
    fun `deve criar um novo token de verificação de e-mail quando o atual tiver passado o cooldown`() {
        val now = getZonedDateTime()

        tokenRepository.save(
            validTokenKey,
            TokenDataWithExpiration(
                TokenData.of(email),
                expiration = now.toEpochSecond(),
            ),
        )

        val expectedDuration = ofSeconds(configuration.emailVerificationDuration)

        val result = withGivenDateTime(now.plusSeconds(configuration.maxCooldownDuration + 1)) {
            tokenService.issueToken(email, accountId)
        }

        result.isRight() shouldBe true
        result.map {
            it.duration shouldBe expectedDuration
            it.accountId shouldBe accountId
        }
        val slot = slot<String>()
        verify(exactly = 1) {
            notificationAdapter.notifyEmailVerificationToken(
                accountId,
                email,
                capture(slot),
                expectedDuration,
            )
        }
        slot.captured shouldContain validTokenKey.value
        val issuedToken = tokenRepository.retrieveValidated(
            validTokenKey,
            tokenType = TokenType.EMAIL,
            errorLimit = 1,
        )

        issuedToken.isRight() shouldBe true

        issuedToken.map {
            it.value shouldBe TokenData.of(email).value
        }
    }

    @ParameterizedTest
    @MethodSource("mobilePhoneTokens")
    fun `deve enviar o token existente numa nova tentativa de emissão de um token ainda válido`(
        tokenData: TokenData,
        expiration: Long,
    ) {
        val now = getZonedDateTime()
        val channel = if (tokenData.type == TokenType.MOBILE_PHONE) TokenChannel.SMS else TokenChannel.WHATSAPP
        val duration = if (tokenData.type == TokenType.MOBILE_PHONE) configuration.phoneVerificationDuration else configuration.whatsappVerificationDuration
        val oldValidTokenKey = TokenKey(accountId, "111111")

        tokenRepository.save(
            oldValidTokenKey,
            TokenDataWithExpiration(
                tokenData,
                now.plusSeconds(duration).toEpochSecond(),
            ),
        )

        val elapsedSeconds = 1L

        withGivenDateTime(now.plusSeconds(elapsedSeconds)) {
            val result = tokenService.issueToken(mobilePhone, accountId, channel)

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<TokenStillValidException>()
                it.accountId shouldBe oldValidTokenKey.accountId
                it.duration.seconds shouldBe duration - elapsedSeconds
                it.cooldown.seconds shouldBe configuration.maxCooldownDuration - elapsedSeconds
            }
        }

        if (channel == TokenChannel.SMS) {
            verify(exactly = 1) { smsSender.send(mobilePhone, any()) }
            verify(exactly = 0) { notificationAdapter.notifyToken(any(), any(), any()) }
        } else {
            verify(exactly = 1) { notificationAdapter.notifyToken(accountId, any(), any()) }
            verify(exactly = 0) { smsSender.send(any(), any()) }
        }

        val invalidToken = tokenRepository.retrieveValidated(
            validTokenKey,
            tokenType = tokenData.type,
            errorLimit = 1,
        )
        invalidToken.isLeft() shouldBe true
        invalidToken.mapLeft { it.shouldBeTypeOf<InvalidTokenException>() }

        val issuedToken = tokenRepository.retrieveValidated(
            oldValidTokenKey,
            tokenType = tokenData.type,
            errorLimit = 1,
        )
        issuedToken.isRight() shouldBe true
        issuedToken.map {
            it.value shouldBe mobilePhone.msisdn
        }
    }

    @Test
    fun `should not change valid password recovery token on a new issue token`() {
        val oldValidTokenKey = TokenKey(accountId, "111111")
        tokenRepository.save(
            oldValidTokenKey,
            TokenDataWithExpiration(
                TokenData.of(email),
                Instant.now().plusSeconds(1000).epochSecond,
            ),
        )

        val result = tokenService.issuePasswordRecoveryToken(email, accountId, DOCUMENT)

        result.isLeft() shouldBe true
        result.mapLeft { it.shouldBeTypeOf<TokenStillValidException>() }
        verify(exactly = 0) { notificationAdapter wasNot called }
        val invalidToken =
            tokenRepository.retrieveValidated(
                validTokenKey,
                tokenType = TokenType.EMAIL,
                errorLimit = 1,
            )
        invalidToken.isLeft() shouldBe true
        invalidToken.mapLeft { it.shouldBeTypeOf<InvalidTokenException>() }

        val issuedToken = tokenRepository.retrieveValidated(
            oldValidTokenKey,
            tokenType = TokenType.EMAIL,
            errorLimit = 1,
        )

        issuedToken.isRight() shouldBe true

        issuedToken.map {
            it.value shouldBe TokenData.of(email).value
        }
    }

    @Test
    fun `should not change valid email verification token on a new issue token`() {
        val oldValidTokenKey = TokenKey(accountId, "111111")
        tokenRepository.save(
            oldValidTokenKey,
            TokenDataWithExpiration(
                TokenData.of(email),
                Instant.now().plusSeconds(1000).epochSecond,
            ),
        )

        val result = tokenService.issueToken(email, accountId)

        result.isLeft() shouldBe true
        result.mapLeft { it.shouldBeTypeOf<TokenStillValidException>() }
        verify(exactly = 0) { notificationAdapter wasNot called }
        val invalidToken =
            tokenRepository.retrieveValidated(
                validTokenKey,
                tokenType = TokenType.EMAIL,
                errorLimit = 1,
            )
        invalidToken.isLeft() shouldBe true
        invalidToken.mapLeft { it.shouldBeTypeOf<InvalidTokenException>() }

        val issuedToken = tokenRepository.retrieveValidated(
            oldValidTokenKey,
            tokenType = TokenType.EMAIL,
            errorLimit = 1,
        )

        issuedToken.isRight() shouldBe true

        issuedToken.map {
            it.value shouldBe TokenData.of(email).value
        }
    }

    // NOTE: Idealmente, deveria testar todos os tipos de token.
    @Test
    fun `deve apagar o token existente ao tentar criar um novo e tenha passado o cooldown`() {
        every { TokenGenerator.generate(any(), any()) } returns "new-token"

        val now = getZonedDateTime()
        val tokenData = TokenData.of(email)
        val duration = ofSeconds(configuration.emailVerificationDuration)

        tokenRepository.save(
            TokenKey(accountId = accountId, value = "old-token"),
            TokenDataWithExpiration(
                tokenData,
                expiration = now.plusSeconds(duration.seconds).toEpochSecond(),
            ),
        )

        val existingToken = tokenRepository.retrieveNotExpiredTokenKey(accountId = accountId, tokenType = tokenData.type)

        existingToken shouldBe TokenKey(accountId, value = "old-token").right()

        val recentlyIssuedToken = withGivenDateTime(now.plusSeconds(configuration.maxCooldownDuration + 1)) {
            tokenService.issueToken(email, accountId)
        }

        recentlyIssuedToken shouldBe IssuedToken(duration = duration, accountId = accountId, cooldown = ofSeconds(configuration.maxCooldownDuration)).right()

        val recentlyIssuedTokenKey = tokenRepository.retrieveNotExpiredTokenKey(accountId = accountId, tokenType = tokenData.type)

        recentlyIssuedTokenKey shouldBe TokenKey(accountId, value = "new-token").right()
    }

    // NOTE: Idealmente, deveria testar todos os tipos de token.
    @Test
    fun `deve falhar para criar um novo token caso não tenha passado o cooldown`() {
        every { TokenGenerator.generate(any(), any()) } returns "new-token"

        val now = getZonedDateTime()
        val tokenData = TokenData.of(email)
        val duration = ofSeconds(configuration.emailVerificationDuration)

        tokenRepository.save(
            TokenKey(accountId = accountId, value = "old-token"),
            TokenDataWithExpiration(
                tokenData,
                expiration = now.plusSeconds(duration.seconds).toEpochSecond(),
            ),
        )

        val existingToken = tokenRepository.retrieveNotExpiredTokenKey(accountId = accountId, tokenType = tokenData.type)

        existingToken shouldBe TokenKey(accountId, value = "old-token").right()

        val elapsedSeconds = configuration.maxCooldownDuration - 1L

        val lastIssuedToken = withGivenDateTime(now.plusSeconds(elapsedSeconds)) {
            tokenService.issueToken(email, accountId)
        }

        with(lastIssuedToken) {
            this.isLeft() shouldBe true
            this.getOrElse {
                it.shouldBeTypeOf<TokenStillValidException>()
                it.accountId shouldBe accountId
                it.duration.seconds shouldBe duration.seconds - elapsedSeconds
                it.cooldown.seconds shouldBe 1
            }
        }

        val lastIssuedTokenKey = tokenRepository.retrieveNotExpiredTokenKey(accountId = accountId, tokenType = tokenData.type)

        lastIssuedTokenKey shouldBe TokenKey(accountId, value = "old-token").right()
    }

    @Test
    fun `should return phone number when token is valid and invalidate token`() {
        tokenRepository.save(
            validTokenKey,
            TokenDataWithExpiration(
                TokenData.of(mobilePhone, TokenChannel.SMS),
                Instant.now().plusSeconds(1000).epochSecond,
            ),
        )

        val result = tokenService.validateToken(validTokenKey, TokenType.MOBILE_PHONE)

        result.isRight() shouldBe true
        result.map {
            it.value shouldBe mobilePhone.msisdn
            it.type shouldBe TokenType.MOBILE_PHONE
        }

        val error = tokenService.validateToken(validTokenKey, TokenType.MOBILE_PHONE)
        error.isLeft() shouldBe true
        error.mapLeft { it.shouldBeTypeOf<InvalidTokenException>() }
    }

    @Test
    fun `should return email when token is valid and invalidate token`() {
        tokenRepository.save(
            validTokenKey,
            TokenDataWithExpiration(TokenData.of(email), Instant.now().plusSeconds(1000).epochSecond),
        )

        val result = tokenService.validateToken(validTokenKey, TokenType.EMAIL)

        result.isRight() shouldBe true
        result.map { it.value shouldBe email.value }

        val error = tokenService.validateToken(validTokenKey, TokenType.EMAIL)
        error.isLeft() shouldBe true
        error.mapLeft { it.shouldBeTypeOf<InvalidTokenException>() }
    }

    @Test
    fun `should return 0 when msisdn is not found for validation`() {
        val result = tokenService.validateToken(invalidTokenKey, TokenType.MOBILE_PHONE)

        result.isLeft() shouldBe true
        result.mapLeft {
            it.shouldBeTypeOf<InvalidTokenException>()
            it.reason shouldBe InvalidTokenReason.NOT_FOUND
        }
        val count = tokenRepository.retrieveErrorCount(accountId)

        count shouldBe 0
    }

    @Test
    fun `deve retornar mobile token not found com reason expired token quando token estiver expirado`() {
        tokenRepository.save(
            validTokenKey,
            TokenDataWithExpiration(
                TokenData.of(mobilePhone, TokenChannel.SMS),
                Instant.now().minusSeconds(1000).epochSecond,
            ),
        )

        val result = tokenService.validateToken(validTokenKey, TokenType.MOBILE_PHONE)

        result.isLeft() shouldBe true
        result.mapLeft {
            it.shouldBeTypeOf<InvalidTokenException>()
            it.reason shouldBe InvalidTokenReason.EXPIRED
        }
    }

    @Test
    fun `deve retornar email token not found com reason expired token quando token estiver expirado`() {
        tokenRepository.save(
            validTokenKey,
            TokenDataWithExpiration(TokenData.of(email), Instant.now().minusSeconds(1000).epochSecond),
        )

        val result = tokenService.validateToken(validTokenKey, TokenType.EMAIL)

        result.isLeft() shouldBe true
        result.mapLeft {
            it.shouldBeTypeOf<InvalidTokenException>()
            it.reason shouldBe InvalidTokenReason.EXPIRED
        }
    }

    @Test
    fun `deve retornar mobile token not found com reason token mismatch quando token for inválido`() {
        tokenRepository.save(
            validTokenKey,
            TokenDataWithExpiration(
                TokenData.of(mobilePhone, TokenChannel.SMS),
                Instant.now().plusSeconds(1000).epochSecond,
            ),
        )

        val result = tokenService.validateToken(invalidTokenKey, TokenType.MOBILE_PHONE)

        result.isLeft() shouldBe true
        result.mapLeft {
            it.shouldBeTypeOf<InvalidTokenException>()
            it.reason shouldBe InvalidTokenReason.MISMATCH
        }
    }

    @Test
    fun `deve retornar email token not found com reason token mismatch quando token for inválido`() {
        tokenRepository.save(
            validTokenKey,
            TokenDataWithExpiration(TokenData.of(email), Instant.now().plusSeconds(1000).epochSecond),
        )

        val result = tokenService.validateToken(invalidTokenKey, TokenType.EMAIL)

        result.isLeft() shouldBe true
        result.mapLeft {
            it.shouldBeTypeOf<InvalidTokenException>()
            it.reason shouldBe InvalidTokenReason.MISMATCH
        }
    }

    @Test
    fun `should invalidate token when max retries is reached`() {
        tokenRepository.save(
            validTokenKey,
            TokenDataWithExpiration(
                TokenData.of(mobilePhone, TokenChannel.SMS),
                Instant.now().plusSeconds(1000).epochSecond,
            ),
        )

        repeat(configuration.maxErrorCount) {
            val result = tokenService.validateToken(invalidTokenKey, TokenType.MOBILE_PHONE)
            result.isLeft() shouldBe true
            result.mapLeft { e ->
                e.shouldBeTypeOf<InvalidTokenException>()
                e.reason shouldBe InvalidTokenReason.MISMATCH
            }
            val count = tokenRepository.retrieveErrorCount(accountId)
            count shouldBe it + 1
        }

        val result = tokenService.validateToken(validTokenKey, TokenType.MOBILE_PHONE)
        result.isLeft() shouldBe true
        result.mapLeft {
            it.shouldBeTypeOf<InvalidTokenException>()
            it.reason shouldBe InvalidTokenReason.MAX_ATTEMPTS
        }
    }

    @Test
    fun `should invalidate email token when max retries is reached`() {
        tokenRepository.save(
            validTokenKey,
            TokenDataWithExpiration(TokenData.of(email), Instant.now().plusSeconds(1000).epochSecond),
        )

        repeat(configuration.maxErrorCount) {
            val result = tokenService.validateToken(invalidTokenKey, TokenType.EMAIL)
            result.isLeft() shouldBe true
            result.mapLeft { e ->
                e.shouldBeTypeOf<InvalidTokenException>()
                e.reason shouldBe InvalidTokenReason.MISMATCH
            }
            val count = tokenRepository.retrieveErrorCount(accountId)
            count shouldBe it + 1
        }

        val result = tokenService.validateToken(validTokenKey, TokenType.EMAIL)
        result.isLeft() shouldBe true
        result.mapLeft {
            it.shouldBeTypeOf<InvalidTokenException>()
            it.reason shouldBe InvalidTokenReason.MAX_ATTEMPTS
        }
    }

    @Test
    fun `should find issued phone and expiration when it exists`() {
        val expiration = Instant.now().plusSeconds(1000).epochSecond
        tokenRepository.save(
            validTokenKey,
            TokenDataWithExpiration(TokenData.of(mobilePhone, TokenChannel.SMS), expiration),
        )

        val issuedToken = tokenService.findIssuedToken(
            validTokenKey.accountId,
            TokenType.MOBILE_PHONE,
        )

        issuedToken.isRight() shouldBe true

        issuedToken.map {
            it.value shouldBe mobilePhone.msisdn
            it.type shouldBe TokenType.MOBILE_PHONE
            it.expiration shouldBe expiration
        }
    }

    @Test
    fun `should find issued email and expiration when it exists`() {
        val expiration = Instant.now().plusSeconds(1000).epochSecond
        tokenRepository.save(validTokenKey, TokenDataWithExpiration(TokenData.of(email), expiration))

        val issuedToken = tokenService.findIssuedToken(
            validTokenKey.accountId,
            TokenType.EMAIL,
        )
        issuedToken.isRight() shouldBe true

        issuedToken.map { issuedToken ->
            issuedToken.value shouldBe email.value
            issuedToken.type shouldBe TokenType.EMAIL
            issuedToken.expiration shouldBe expiration
        }
    }

    companion object {
        private val testMobilePhone = MobilePhone("+*************")
        private val appleMobilePhone = MobilePhone("+*************")
        private val configuration = TokenOnboardingConfigurationMicronaut(
            tokenSize = 6,
            maxCooldownDuration = 5,
            phoneVerificationDuration = 60,
            emailVerificationDuration = 120,
            whatsappVerificationDuration = 10,
            passwordRecoveryDuration = 300,
            livenessVerificationDuration = 600,
            message = "Seu token é %s",
            maxErrorCount = 3,
            testMobilePhones = listOf(testMobilePhone.msisdn),
            appleMobilePhones = listOf(appleMobilePhone.msisdn),
            appleTokenVerify = "261213",
        )

        @JvmStatic
        fun mobilePhoneTokens(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    TokenData.of(mobilePhone, TokenChannel.WHATSAPP),
                    configuration.whatsappVerificationDuration,
                ),
                Arguments.arguments(
                    TokenData.of(mobilePhone, TokenChannel.SMS),
                    configuration.phoneVerificationDuration,
                ),
            )
        }
    }
}