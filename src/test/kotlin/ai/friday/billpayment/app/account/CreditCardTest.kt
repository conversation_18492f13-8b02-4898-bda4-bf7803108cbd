package ai.friday.billpayment.app.account

import io.kotest.matchers.shouldBe
import java.util.UUID
import org.junit.jupiter.api.Test

internal class CreditCardTest {

    val brand = CreditCardBrand.MASTERCARD
    val pan = "****************"
    val expiryDate = "01/2020"

    val baseCard = CreditCard(
        brand = brand,
        pan = pan,
        expiryDate = expiryDate,
        token = CreditCardToken(UUID.randomUUID().toString()),
        binDetails = null,
        riskLevel = RiskLevel.LOW,
    )

    @Test
    fun `should return same card when brand pan and expiryDate are the same`() {
        baseCard.isSameCard(
            CreditCard(
                brand = brand,
                pan = maskPan(pan),
                expiryDate = expiryDate,
                binDetails = null,
                riskLevel = RiskLevel.LOW,
            ),
        ) shouldBe true
    }

    @Test
    fun `should not return same card when brand is not the same`() {
        baseCard.isSameCard(
            CreditCard(
                brand = CreditCardBrand.VISA,
                pan = pan,
                expiryDate = expiryDate,
                binDetails = null,
                riskLevel = RiskLevel.LOW,
            ),
        ) shouldBe false
    }

    @Test
    fun `should not return same card when pan is not the same`() {
        baseCard.isSameCard(
            CreditCard(
                brand = brand,
                pan = "9999999999999999",
                expiryDate = expiryDate,
                binDetails = null,
                riskLevel = RiskLevel.LOW,
            ),
        ) shouldBe false
    }

    @Test
    fun `should not return same card when expiryDate is not the same`() {
        baseCard.isSameCard(
            CreditCard(
                brand = brand,
                pan = pan,
                expiryDate = "01/2022",
                binDetails = null,
                riskLevel = RiskLevel.LOW,
            ),
        ) shouldBe false
    }
}