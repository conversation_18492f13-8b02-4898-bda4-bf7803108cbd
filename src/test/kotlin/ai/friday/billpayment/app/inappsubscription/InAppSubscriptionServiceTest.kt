package ai.friday.billpayment.app.inappsubscription

import DynamoDBUtils
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.DEFAULT_LEGACY_ACCOUNT_CONFIGURATION
import ai.friday.billpayment.adapters.dynamodb.InAppSubscriptionDbRepository
import ai.friday.billpayment.adapters.dynamodb.InAppSubscriptionDynamoDAO
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentStatus
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.UpdateAccountStatusRequest
import ai.friday.billpayment.app.account.UpdateAccountStatusResult
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.inappsubscription.instrumentation.InAppSubscriptionInstrumentationService
import ai.friday.billpayment.app.integrations.CrmContact
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.NAME
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import io.kotest.inspectors.forOne
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import java.time.ZonedDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class InAppSubscriptionServiceTest {
    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val enhancedClient = DynamoDBUtils.setupDynamoDB()
    private val subscriptionDynamoDAO = InAppSubscriptionDynamoDAO(enhancedClient)
    private val subscriptionDbRepository = spyk(InAppSubscriptionDbRepository(client = subscriptionDynamoDAO))

    private val inAppSubscriptionAccessConcessionId = InAppSubscriptionAccessConcessionId("testeId")

    private val activeSubscription =
        InAppSubscription(
            AccountId(ACCOUNT_ID),
            InAppSubscriptionStatus.ACTIVE,
            endsAt = getZonedDateTime().plusDays(3),
            InAppSubscriptionStore.APP_STORE,
            reason = InAppSubscriptionReason.SUBSCRIPTION,
            inAppSubscriptionAccessConcessionId = inAppSubscriptionAccessConcessionId,
            autoRenew = true,
            price = 999,
            productId = InAppSubscriptionProductId("fakeProductId"),
            offStoreProductId = null,
        )

    private val trialSubscription = activeSubscription.copy(reason = InAppSubscriptionReason.TRIAL)

    private val activeSubscriptionConcession =
        InAppSubscriptionAccessConcession(
            id = inAppSubscriptionAccessConcessionId,
            accountId = activeSubscription.accountId,
            reason = InAppSubscriptionReason.SUBSCRIPTION,
            endsAt = activeSubscription.endsAt,
            store = activeSubscription.store,
        )

    private val contact =
        CrmContact(
            emailAddress = EmailAddress("<EMAIL>"),
            accountId = AccountId(ACCOUNT_ID),
            role = Role.GUEST,
            groups = listOf(AccountGroup.ALPHA, AccountGroup.BETA),
            accountType = UserAccountType.FULL_ACCOUNT,
            isCNPJAccount = true,
            cnpjWallets = listOf(WalletId("fakeWalletIdCNPJ")),
            cpfWallets = listOf(WalletId("fakeWalletIdCPF")),
            otherMembersOnWallets = listOf(AccountId("OTHER MEMBER")),
            subscriptionType = SubscriptionType.IN_APP,
        )

    private val notificationAdapter = mockk<NotificationAdapter>(relaxUnitFun = true)

    private val inAppSubscriptionAdapter =
        mockk<InAppSubscriptionAdapter> {
            every { getSubscriptions(any()) } returns Either.Right(listOf(activeSubscription))
        }

    private val inAppSubscriptionAccessConcessionRepository: InAppSubscriptionAccessConcessionRepository =
        mockk {
            every { findAllByAccountId(any()) } returns listOf(activeSubscriptionConcession)
            every { findLastByAccountId(any()) } returns activeSubscriptionConcession
            every { save(any()) } returns Unit
        }

    private val crmService: CrmService =
        mockk {
            every { upsertContact(account = any<Account>()) } returns contact
        }

    private val account = Account(
        accountId = AccountId(ACCOUNT_ID),
        name = NAME,
        emailAddress = EmailAddress("email"),
        document = DOCUMENT,
        documentType = "CPF",
        mobilePhone = "+*************",
        created = getZonedDateTime(),
        updated = getZonedDateTime(),
        status = AccountStatus.ACTIVE,
        configuration = DEFAULT_LEGACY_ACCOUNT_CONFIGURATION.copy(defaultWalletId = WalletId(WALLET_ID)),
        imageUrlSmall = "image_url_small",
        imageUrlLarge = "image_url_large",
        paymentStatus = AccountPaymentStatus.UpToDate,
        subscriptionType = SubscriptionType.IN_APP,
    )

    private val updatedAccountToActiveResult = Result.success(UpdateAccountStatusResult.Updated(account.copy(status = AccountStatus.ACTIVE)))

    private val updatedAccountToBlockedResult = Result.success(UpdateAccountStatusResult.Updated(account.copy(status = AccountStatus.BLOCKED)))

    private val accountService: AccountService =
        mockk {
            every { findAccountByIdOrNull(any()) } returns account
            every { findPartialAccountByIdOrNull(any()) } returns null
        }

    private val instrumentationServiceMock: InAppSubscriptionInstrumentationService = mockk(relaxUnitFun = true)

    private val service =
        InAppSubscriptionService(
            accountService = accountService,
            crmService = crmService,
            instrumentationService = instrumentationServiceMock,
            subscriptionAccessConcessionRepository = inAppSubscriptionAccessConcessionRepository,
            notificationAdapter = notificationAdapter,
            subscriptionAdapter = inAppSubscriptionAdapter,
            subscriptionEventRepository = mockk(),
            subscriptionRepository = subscriptionDbRepository,
        )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `ao criar uma assinatura deve criar uma entrada de concessão de acesso e tentar atualizar usuário como adimplente`() {
        every { subscriptionDbRepository.findOrNull(any()) } returns null
        every { accountService.attemptToUpdateAccountStatus(any()) } returns updatedAccountToActiveResult

        service.createSubscription(activeSubscription)

        val slotSubscription = slot<InAppSubscription>()
        val slotAccessConcession = slot<InAppSubscriptionAccessConcession>()

        verify {
            subscriptionDbRepository.save(capture(slotSubscription))
            inAppSubscriptionAccessConcessionRepository.save(capture(slotAccessConcession))
            accountService.attemptToUpdateAccountStatus(UpdateAccountStatusRequest.Unblock(accountId = activeSubscription.accountId))
        }

        slotSubscription.captured.accountId shouldBe activeSubscription.accountId
        slotSubscription.captured.status shouldBe activeSubscription.status
        slotSubscription.captured.endsAt shouldBe activeSubscription.endsAt
        slotSubscription.captured.store shouldBe activeSubscription.store
        slotSubscription.captured.reason shouldBe activeSubscription.reason
        slotSubscription.captured.inAppSubscriptionAccessConcessionId shouldBe slotAccessConcession.captured.id

        slotAccessConcession.captured.endsAt shouldBe activeSubscriptionConcession.endsAt
        slotAccessConcession.captured.accountId shouldBe activeSubscriptionConcession.accountId
        slotAccessConcession.captured.store shouldBe activeSubscriptionConcession.store
        slotAccessConcession.captured.reason shouldBe activeSubscriptionConcession.reason

        slotSubscription.captured.inAppSubscriptionAccessConcessionId shouldBe slotAccessConcession.captured.id
    }

    @Test
    fun `ao criar uma nova assinatura que é trial, deve instrumentar trial started`() {
        every { subscriptionDbRepository.findOrNull(any()) } returns null
        every { accountService.attemptToUpdateAccountStatus(any()) } returns updatedAccountToActiveResult

        service.createSubscription(trialSubscription)

        verify {
            instrumentationServiceMock.trialStarted(trialSubscription.accountId)
        }
    }

    @Test
    fun `ao criar uma nova assinatura que não é trial, não deve instrumentar trial started`() {
        every { subscriptionDbRepository.findOrNull(any()) } returns null
        every { accountService.attemptToUpdateAccountStatus(any()) } returns updatedAccountToActiveResult

        service.createSubscription(activeSubscription)

        verify(exactly = 0) {
            instrumentationServiceMock.trialStarted(activeSubscription.accountId)
        }
    }

    @Test
    fun `ao renovar uma assinatura deve atualizar a assinatura existente, deve criar uma nova concessao e deve tentar marcar usuário como adimplente`() {
        every { subscriptionDbRepository.findOrNull(activeSubscription.accountId) } returns activeSubscription
        every { accountService.attemptToUpdateAccountStatus(any()) } returns updatedAccountToActiveResult

        val subscription = service.renewSubscription(activeSubscription.copy(endsAt = activeSubscription.endsAt.plusDays(2)))

        val inAppSubscriptionAccessConcessionCapturingSlot = slot<InAppSubscriptionAccessConcession>()
        val inAppSubscriptionSlot = slot<InAppSubscription>()

        verify {
            subscriptionDbRepository.findOrNull(activeSubscription.accountId)
            subscriptionDbRepository.save(capture(inAppSubscriptionSlot))
            inAppSubscriptionAccessConcessionRepository.save(capture(inAppSubscriptionAccessConcessionCapturingSlot))
            accountService.attemptToUpdateAccountStatus(UpdateAccountStatusRequest.Unblock(accountId = activeSubscription.accountId))
        }

        inAppSubscriptionSlot.captured.endsAt shouldBe activeSubscription.endsAt.plusDays(2)
        inAppSubscriptionAccessConcessionCapturingSlot.captured.endsAt shouldBe activeSubscription.endsAt.plusDays(2)

        subscription.map {
            it.price shouldBe activeSubscription.price
            it.inAppSubscriptionAccessConcessionId shouldBe inAppSubscriptionAccessConcessionCapturingSlot.captured.id
            it.reason shouldBe InAppSubscriptionReason.SUBSCRIPTION
        }
    }

    @Test
    fun `ao renovar uma assinatura e a anterior era de trial, deve instrumentar trial converted`() {
        every { subscriptionDbRepository.findOrNull(trialSubscription.accountId) } returns trialSubscription
        every { accountService.attemptToUpdateAccountStatus(any()) } returns updatedAccountToActiveResult

        service.renewSubscription(trialSubscription)

        verify {
            instrumentationServiceMock.trialConverted(trialSubscription.accountId)
        }
    }

    @Test
    fun `ao renovar uma assinatura e a anterior não era de trial, não deve instrumentar trial converted`() {
        every { subscriptionDbRepository.findOrNull(activeSubscription.accountId) } returns activeSubscription
        every { accountService.attemptToUpdateAccountStatus(any()) } returns updatedAccountToActiveResult

        service.renewSubscription(activeSubscription.copy(endsAt = activeSubscription.endsAt.plusDays(2)))

        verify(exactly = 0) {
            instrumentationServiceMock.trialConverted(activeSubscription.accountId)
        }
    }

    @Test
    fun `quando houver estorno, deve criar uma concessao nova e deve tentar bloquear a conta`() {
        every { accountService.attemptToUpdateAccountStatus(any()) } returns updatedAccountToBlockedResult

        service.handleSubscriptionRefund(activeSubscription.copy(status = InAppSubscriptionStatus.EXPIRED))

        val slot = slot<InAppSubscriptionAccessConcession>()
        val inAppSubscriptionSlot = slot<InAppSubscription>()
        val blockRequest = slot<UpdateAccountStatusRequest.Block>()

        verify {
            inAppSubscriptionAccessConcessionRepository.save(capture(slot))
            subscriptionDbRepository.save(capture(inAppSubscriptionSlot))
            accountService.attemptToUpdateAccountStatus(capture(blockRequest))
        }

        slot.captured.endsAt.toLocalDate() shouldBe getLocalDate()
        inAppSubscriptionSlot.captured.status shouldBe InAppSubscriptionStatus.EXPIRED
        blockRequest.captured.accountId shouldBe account.accountId
    }

    @Test
    fun `quando houver uma extensão da assinatura, deve atualizar a assinatura, criar uma nova concessao e tentar marcar o usuário como adimplente`() {
        every { subscriptionDbRepository.findOrNull(activeSubscription.accountId) } returns activeSubscription
        every { accountService.attemptToUpdateAccountStatus(any()) } returns updatedAccountToActiveResult

        val inAppSubscription = activeSubscription.copy(endsAt = activeSubscription.endsAt.plusDays(2), reason = InAppSubscriptionReason.BACKOFFICE)
        val subscription = service.extendSubscription(inAppSubscription)

        val slot = slot<InAppSubscriptionAccessConcession>()
        val inAppSubscriptionSlot = slot<InAppSubscription>()

        verify {
            inAppSubscriptionAccessConcessionRepository.save(capture(slot))
            subscriptionDbRepository.findOrNull(inAppSubscription.accountId)
            subscriptionDbRepository.save(capture(inAppSubscriptionSlot))
            accountService.attemptToUpdateAccountStatus(UpdateAccountStatusRequest.Unblock(accountId = inAppSubscription.accountId))
        }
        inAppSubscriptionSlot.captured.endsAt shouldBe inAppSubscription.endsAt
        slot.captured.endsAt shouldBe inAppSubscription.endsAt
        subscription.map {
            it.inAppSubscriptionAccessConcessionId shouldBe slot.captured.id
            it.reason shouldBe inAppSubscription.reason
        }
    }

    @Test
    fun `se houver mais de uma assinatura ativa deve falhar`() {
        every { inAppSubscriptionAdapter.getSubscriptions(any()) } returns Either.Right(listOf(activeSubscription, activeSubscription))

        val result = service.getSubscriptionRealTime(activeSubscription.accountId)

        result.isLeft().shouldBeTrue()
        result.mapLeft {
            it shouldBe InAppSubscriptionError.MultipleActiveSubscriptions
        }
    }

    @Test
    fun `se não houver assinatura ativa deve falhar`() {
        val inactiveSubscription =
            InAppSubscription(
                AccountId(ACCOUNT_ID),
                InAppSubscriptionStatus.EXPIRED,
                endsAt = getZonedDateTime().plusDays(3),
                InAppSubscriptionStore.APP_STORE,
                reason = InAppSubscriptionReason.SUBSCRIPTION,
                inAppSubscriptionAccessConcessionId = inAppSubscriptionAccessConcessionId,
                autoRenew = false,
                price = 999,
                productId = InAppSubscriptionProductId("fakeProductId"),
                offStoreProductId = null,
            )

        every { inAppSubscriptionAdapter.getSubscriptions(any()) } returns Either.Right(listOf(inactiveSubscription))

        val result = service.getSubscriptionRealTime(inactiveSubscription.accountId)

        result.isLeft().shouldBeTrue()
        result.mapLeft {
            it shouldBe InAppSubscriptionError.ExpiredSubscription
        }
    }

    @Test
    fun `se a subscription já estiver expirada não deve expirar novamente`() {
        val expiredSubscription = activeSubscription.copy(status = InAppSubscriptionStatus.EXPIRED)
        every { subscriptionDbRepository.findOrNull(activeSubscription.accountId) } returns expiredSubscription
        every { accountService.attemptToUpdateAccountStatus(any()) } returns updatedAccountToBlockedResult

        val result = service.expireSubscription(expiredSubscription)

        verify(exactly = 0) {
            subscriptionDbRepository.save(any())
        }
        result.isRight().shouldBeTrue()
    }

    @Test
    fun `quando a assinatura não estiver mais válida, deve tentar marcar como inadimplente`() {
        val expiredSubscription = activeSubscription.copy(status = InAppSubscriptionStatus.EXPIRED)

        every { subscriptionDbRepository.findOrNull(activeSubscription.accountId) } returns activeSubscription
        every { accountService.attemptToUpdateAccountStatus(any()) } returns updatedAccountToBlockedResult

        service.expireSubscription(expiredSubscription)

        val capturedRequest = slot<UpdateAccountStatusRequest.Block>()

        verify {
            accountService.attemptToUpdateAccountStatus(capture(capturedRequest))
        }

        capturedRequest.captured.accountId shouldBe account.accountId
    }

    @Test
    fun `quando não precisar alterar o usuário para adimplente ou inadimplente, deve atualizar o CRM`() {
        val expiredSubscription = activeSubscription.copy(status = InAppSubscriptionStatus.EXPIRED)

        every { subscriptionDbRepository.findOrNull(activeSubscription.accountId) } returns activeSubscription
        every { accountService.attemptToUpdateAccountStatus(any()) } returns Result.success(UpdateAccountStatusResult.Unchanged(account))

        service.expireSubscription(expiredSubscription)

        verify {
            crmService.upsertContact(account)
        }
    }

    @Test
    fun `deve instrumentar se expirar um trial`() {
        val trialSubscription = activeSubscription.copy(reason = InAppSubscriptionReason.TRIAL)

        every { subscriptionDbRepository.findOrNull(trialSubscription.accountId) } returns trialSubscription
        every { accountService.findAccountByIdOrNull(account.accountId) } returns account
        every { accountService.attemptToUpdateAccountStatus(any()) } returns updatedAccountToBlockedResult

        service.expireSubscription(trialSubscription)

        verify {
            instrumentationServiceMock.trialExpired(trialSubscription.accountId)
        }
    }

    @Test
    fun `deve expirar uma subscription se estiver ativa`() {
        every { subscriptionDbRepository.findOrNull(activeSubscription.accountId) } returns activeSubscription
        every { accountService.attemptToUpdateAccountStatus(any()) } returns updatedAccountToBlockedResult

        service.expireSubscription(activeSubscription)

        verify {
            subscriptionDbRepository.findOrNull(activeSubscription.accountId)
            subscriptionDbRepository.save(activeSubscription.copy(status = InAppSubscriptionStatus.EXPIRED))
        }
    }

    @Test
    fun `deve realizar o sync com sucesso`() {
        val result = service.getSubscriptionRealTime(activeSubscription.accountId)

        result.isRight().shouldBeTrue()
        result.map {
            it shouldBe activeSubscription
        }
    }

    @Test
    fun `quando acontecer uma transferência, deve transferir uma assinatura para o outro accountId`() {
        val sourceAccountId = account.accountId
        val targetAccountId = AccountId("targetAccountId")
        val targetAccount = ACCOUNT.copy(accountId = targetAccountId, subscriptionType = SubscriptionType.IN_APP)

        every { subscriptionDbRepository.findOrNull(sourceAccountId) } returns activeSubscription.copy(accountId = sourceAccountId)
        every { subscriptionDbRepository.findOrNull(targetAccountId) } returns null
        every { accountService.findAccountByIdOrNull(targetAccountId) } returns targetAccount
        every { accountService.attemptToUpdateAccountStatus(any()) } returns updatedAccountToBlockedResult
        every { accountService.attemptToUpdateAccountStatus(any()) } returns Result.success(UpdateAccountStatusResult.Updated(targetAccount.copy(status = AccountStatus.ACTIVE)))

        val result = service.transferSubscription(sourceAccountId, targetAccountId)

        val subscriptionSaves = mutableListOf<InAppSubscription>()
        val concessionSaves = mutableListOf<InAppSubscriptionAccessConcession>()
        val updateStatusRequests = mutableListOf<UpdateAccountStatusRequest>()

        verify {
            subscriptionDbRepository.save(capture(subscriptionSaves))
            inAppSubscriptionAccessConcessionRepository.save(capture(concessionSaves))
            accountService.attemptToUpdateAccountStatus(capture(updateStatusRequests))
        }

        subscriptionSaves.forOne {
            it.accountId shouldBe targetAccountId
            it.endsAt shouldBe activeSubscription.endsAt
            it.status shouldBe InAppSubscriptionStatus.ACTIVE
        }

        subscriptionSaves.forOne {
            it.accountId shouldBe sourceAccountId
            it.endsAt.toLocalDate() shouldBe getLocalDate()
            it.status shouldBe InAppSubscriptionStatus.EXPIRED
        }

        concessionSaves.forOne {
            it.accountId shouldBe targetAccountId
            it.endsAt shouldBe activeSubscription.endsAt
            it.store shouldBe activeSubscription.store
            it.isTransfer!!.shouldBeTrue()
            it.originConcessionId!! shouldBe inAppSubscriptionAccessConcessionId
        }

        concessionSaves.forOne {
            it.accountId shouldBe sourceAccountId
            it.endsAt.toLocalDate() shouldBe getLocalDate()
            it.store shouldBe activeSubscription.store
            it.isTransfer!!.shouldBeTrue()
            it.originConcessionId!! shouldBe inAppSubscriptionAccessConcessionId
        }

        updateStatusRequests.forOne {
            val blockRequest = it as UpdateAccountStatusRequest.Block?
            blockRequest?.accountId shouldBe sourceAccountId
        }

        updateStatusRequests.forOne {
            val unblockRequest = it as UpdateAccountStatusRequest.Unblock?
            unblockRequest?.accountId shouldBe targetAccountId
        }

        result.isRight().shouldBeTrue()
    }

    @Test
    fun `quando acontecer uma transferência e o usuário destino tem assinatura, deve dar erro`() {
        val sourceAccountId = accountFromInAppSubscription(subscriptionDbRepository)
        val targetAccountId = accountFromInAppSubscription(subscriptionDbRepository)

        subscriptionDbRepository.save(activeSubscription)

        every { subscriptionDbRepository.findOrNull(targetAccountId) } returns activeSubscription

        val result = service.transferSubscription(sourceAccountId, targetAccountId)

        result.isLeft().shouldBeTrue()
        result.mapLeft { it shouldBe SubscriptionTransferError.TargetAccountAlreadyHasSubscription }
    }

    @Test
    fun `quando acontecer uma transferência e o usuário destino não existe, deve dar erro`() {
        val sourceAccountId = accountFromInAppSubscription(subscriptionDbRepository)
        val targetAccountId = accountFromInAppSubscription(subscriptionDbRepository)

        subscriptionDbRepository.save(activeSubscription)

        every { accountService.findAccountByIdOrNull(targetAccountId) } returns null

        val result = service.transferSubscription(sourceAccountId, targetAccountId)

        result.isLeft().shouldBeTrue()
        result.mapLeft { it shouldBe SubscriptionTransferError.TargetAccountNotFound }
    }

    @Test
    fun `quando acontecer uma transferência e o usuário destino está com a conta encerrada, deve dar erro`() {
        val sourceAccountId = accountFromInAppSubscription(subscriptionDbRepository)
        val targetAccountId = accountFromInAppSubscription(subscriptionDbRepository)

        subscriptionDbRepository.save(activeSubscription)

        every { accountService.findAccountByIdOrNull(targetAccountId) } returns ACCOUNT.copy(status = AccountStatus.CLOSED)

        val result = service.transferSubscription(sourceAccountId, targetAccountId)

        result.isLeft().shouldBeTrue()
        result.mapLeft { it shouldBe SubscriptionTransferError.InvalidTargetAccount }
    }

    @Test
    fun `quando acontecer uma transferência e não for encontrada a assinatura, deve dar erro`() {
        val sourceAccountId = AccountId("sourceAccountId")
        val targetAccountId = AccountId("targetAccountId")
        every { subscriptionDbRepository.findOrNull(sourceAccountId) } returns null

        val result = service.transferSubscription(sourceAccountId, targetAccountId)

        result.isLeft().shouldBeTrue()
        result.mapLeft { it shouldBe SubscriptionTransferError.SubscriptionNotFound }
    }

    @Test
    fun `quando acontecer uma transferência e o usuário destino está com a conta negada, deve dar erro`() {
        val sourceAccountId = accountFromInAppSubscription(subscriptionDbRepository)
        val targetAccountId = accountFromInAppSubscription(subscriptionDbRepository)

        subscriptionDbRepository.save(activeSubscription)

        every { accountService.findAccountByIdOrNull(targetAccountId) } returns ACCOUNT.copy(status = AccountStatus.DENIED)

        val result = service.transferSubscription(sourceAccountId, targetAccountId)

        result.isLeft().shouldBeTrue()
        result.mapLeft { it shouldBe SubscriptionTransferError.InvalidTargetAccount }
    }

    @Test
    fun `quando cancelar uma assinatura, deve atualizar no banco e deve tentar marcar o usuário como adimplente ou inadimplente`() {
        every { accountService.attemptToUpdateAccountStatus(UpdateAccountStatusRequest.Unblock(accountId = activeSubscription.accountId)) } returns updatedAccountToActiveResult

        service.cancelSubscription(activeSubscription)

        verify {
            subscriptionDbRepository.save(activeSubscription)
            accountService.attemptToUpdateAccountStatus(UpdateAccountStatusRequest.Unblock(accountId = activeSubscription.accountId))
        }
    }

    @Test
    fun `quando descancelar uma assinatura, deve atualizar no banco e deve tentar marcar o usuário como adimplente ou inadimplente`() {
        every { accountService.attemptToUpdateAccountStatus(UpdateAccountStatusRequest.Unblock(accountId = activeSubscription.accountId)) } returns updatedAccountToActiveResult

        service.uncancelSubscription(activeSubscription)

        verify {
            subscriptionDbRepository.save(activeSubscription)
            accountService.attemptToUpdateAccountStatus(UpdateAccountStatusRequest.Unblock(accountId = activeSubscription.accountId))
        }
    }

    @Test
    fun `deve notificar a assinatura expirada quando o usuário estiver expirado entre hoje e 3 dias atrás`() {
        val account1 = accountFromInAppSubscription(subscriptionDbRepository)
        val accountExpired1 = accountFromInAppSubscription(subscriptionDbRepository, status = InAppSubscriptionStatus.EXPIRED, endsAt = getZonedDateTime().minusDays(3))
        val accountExpired2 = accountFromInAppSubscription(subscriptionDbRepository, status = InAppSubscriptionStatus.EXPIRED, endsAt = getZonedDateTime().minusDays(1))
        val accountExpired3 = accountFromInAppSubscription(subscriptionDbRepository, status = InAppSubscriptionStatus.EXPIRED, endsAt = getZonedDateTime().minusDays(5))

        service.handleSubscriptionExpiration()

        verify(exactly = 1) {
            notificationAdapter.notifyInAppSubscriptionOverdue(accountExpired1)
            notificationAdapter.notifyInAppSubscriptionOverdue(accountExpired2)
        }

        verify(exactly = 0) {
            notificationAdapter.notifyInAppSubscriptionOverdue(account1)
            notificationAdapter.notifyInAppSubscriptionOverdue(accountExpired3)
        }
    }

    @Test
    fun `deve notificar o downgrade de canal de notificação 6 dias após a expiração da assinatura`() {
        val expiredFor5Days = accountFromInAppSubscription(subscriptionDbRepository, accountId = AccountId("account-1"), status = InAppSubscriptionStatus.EXPIRED, endsAt = getZonedDateTime().minusDays(5))
        val expiredFor7Days = accountFromInAppSubscription(subscriptionDbRepository, accountId = AccountId("account-2"), status = InAppSubscriptionStatus.EXPIRED, endsAt = getZonedDateTime().minusDays(7))
        val expiredFor6Days = accountFromInAppSubscription(subscriptionDbRepository, accountId = AccountId("account-3"), status = InAppSubscriptionStatus.EXPIRED, endsAt = getZonedDateTime().minusDays(6))

        service.handleSubscriptionExpiration()

        verify(exactly = 0) {
            notificationAdapter.notifySubscriptionOverdueNotificationChannelDowngradeWarning(expiredFor5Days)
            notificationAdapter.notifySubscriptionOverdueNotificationChannelDowngradeWarning(expiredFor7Days)
        }

        verify(exactly = 1) {
            notificationAdapter.notifySubscriptionOverdueNotificationChannelDowngradeWarning(expiredFor6Days)
        }
    }
}

fun accountFromInAppSubscription(
    repository: InAppSubscriptionRepository,
    accountId: AccountId = AccountId(),
    status: InAppSubscriptionStatus = InAppSubscriptionStatus.ACTIVE,
    endsAt: ZonedDateTime = getZonedDateTime(),
): AccountId {
    InAppSubscription(
        accountId,
        status,
        endsAt = endsAt,
        InAppSubscriptionStore.APP_STORE,
        reason = InAppSubscriptionReason.SUBSCRIPTION,
        inAppSubscriptionAccessConcessionId = InAppSubscriptionAccessConcessionId("fakeId"),
        autoRenew = false,
        price = 999,
        productId = InAppSubscriptionProductId("fakeProductId"),
        offStoreProductId = null,
    ).apply { repository.save(this) }

    return accountId
}