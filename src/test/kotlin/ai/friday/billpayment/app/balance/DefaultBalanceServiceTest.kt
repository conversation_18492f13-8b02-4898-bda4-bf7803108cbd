package ai.friday.billpayment.app.balance

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.BankStatement
import ai.friday.billpayment.app.integrations.BankAccountService
import ai.friday.billpayment.app.integrations.InternalBankRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.bankStatementItem
import ai.friday.billpayment.internalBankAccount
import ai.friday.billpayment.omnibusBankAccountConfiguration
import ai.friday.billpayment.paymentMethodId
import ai.friday.billpayment.paymentMethodId2
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class DefaultBalanceServiceTest {

    private val bankAccountService = mockk<BankAccountService>()

    private val internalBankRepository = mockk<InternalBankRepository>()

    private val accountService = mockk<AccountService>()

    private val service = DefaultBalanceService(
        accountService = accountService,
        internalBankRepository = internalBankRepository,
        bankAccountService = bankAccountService,
        omnibusBankAccountConfiguration = omnibusBankAccountConfiguration,
        walletService = mockk(),
    )

    @Test
    fun `deve retornar o saldo de uma conta virtual`() {
        val accountPaymentMethod = buildAccountPaymentMethod(bankAccountMode = BankAccountMode.VIRTUAL)

        every {
            accountService.findAccountPaymentMethodsByAccountId(ACCOUNT.accountId, AccountPaymentMethodStatus.ACTIVE)
        } returns listOf(accountPaymentMethod)

        every {
            internalBankRepository.findAllBankStatementItem(paymentMethodId)
        } returns BankStatement(listOf(bankStatementItem))

        service.getBalanceFrom(ACCOUNT.accountId, paymentMethodId).amount shouldBe 123L
    }

    @Test
    fun `deve retornar o saldo de uma conta fisica`() {
        val accountPaymentMethod = buildAccountPaymentMethod()
        val accountPaymentMethod2 = buildAccountPaymentMethod(id = paymentMethodId2, accountNo = 222)

        every {
            accountService.findAccountPaymentMethodsByAccountId(ACCOUNT.accountId, AccountPaymentMethodStatus.ACTIVE)
        } returns listOf(accountPaymentMethod, accountPaymentMethod2)

        every {
            bankAccountService.getBalance("22201")
        } returns 123L

        service.getBalanceFrom(ACCOUNT.accountId, paymentMethodId2).amount shouldBe 123L
    }

    @Test
    fun `deve lancar exception quando nao encontrar o AccountPaymentMethod`() {
        val accountPaymentMethod = buildAccountPaymentMethod()

        every {
            accountService.findAccountPaymentMethodsByAccountId(ACCOUNT.accountId, AccountPaymentMethodStatus.ACTIVE)
        } returns listOf(accountPaymentMethod)

        assertThrows<ItemNotFoundException> {
            service.getBalanceFrom(ACCOUNT.accountId, paymentMethodId2)
        }
    }

    private fun buildAccountPaymentMethod(
        id: AccountPaymentMethodId = paymentMethodId,
        bankAccountMode: BankAccountMode = BankAccountMode.PHYSICAL,
        accountNo: Long = internalBankAccount.accountNo,
        status: AccountPaymentMethodStatus = AccountPaymentMethodStatus.ACTIVE,
    ) = AccountPaymentMethod(
        id = id,
        status = status,
        method = internalBankAccount.copy(
            bankAccountMode = bankAccountMode,
            accountNo = accountNo,
        ),
        accountId = ACCOUNT.accountId,
    )
}