package ai.friday.billpayment.app.vehicledebts

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.ExternalBillId
import ai.friday.billpayment.app.bill.ExternalBillProvider
import ai.friday.billpayment.app.bill.RegisterUpdated
import ai.friday.billpayment.app.bill.UpdatedRegisterData
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_BARCODE
import ai.friday.billpayment.integration.WALLET_ID
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

class VehicleDebtEnrichmentServiceTest {

    private val billEventRepository = mockk<BillEventRepository>()
    private val billEventPublisher = mockk<BillEventPublisher>(relaxed = true)

    private val service = VehicleDebtEnrichmentService(
        billEventRepository = billEventRepository,
        billEventPublisher = billEventPublisher,
    )

    @Test
    fun `quando a bill com o barcode não existe, não deve publicar o evento`() {
        val accountId = AccountId("ACCOUNT-123")
        val walletId = WalletId(WALLET_ID)
        val barcode = BarCode.of(FICHA_DE_COMPENSACAO_BARCODE)
        val licensePlate = LicensePlate("ABC1234")
        val externalId = ExternalBillId("EXT-123", ExternalBillProvider.VEHICLE_DEBTS)
        val details = "Débito de IPVA 2023 para o veículo de placa ABC1234"

        every { billEventRepository.findLastBill(barcode, walletId) } returns Exception("Bill not found").left()

        val result = service.enrichVehicleDebt(
            accountId = accountId,
            walletId = walletId,
            barcode = barcode,
            licensePlate = licensePlate,
            externalId = externalId,
            details = details,
        )

        result.isSuccess shouldBe true

        verify(exactly = 0) {
            billEventPublisher.publish(any(), any())
        }
    }

    @Test
    fun `quando a bill existe, deve publicar o evento de enriquecimento`() {
        val accountId = AccountId("ACCOUNT-123")
        val walletId = WalletId(WALLET_ID)
        val mockedBillId = BillId()
        val barcode = BarCode.of(FICHA_DE_COMPENSACAO_BARCODE)
        val licensePlate = LicensePlate("ABC1234")
        val externalId = ExternalBillId("EXT-123", ExternalBillProvider.VEHICLE_DEBTS)
        val details = "Débito de IPVA 2023 para o veículo de placa ABC1234"

        val bill = mockk<Bill> {
            every { billId } returns mockedBillId
        }
        every { billEventRepository.findLastBill(barcode, walletId) } returns bill.right()

        val result = service.enrichVehicleDebt(
            accountId = accountId,
            barcode = barcode,
            licensePlate = licensePlate,
            externalId = externalId,
            details = details,
            walletId = walletId,
        )

        result.isSuccess shouldBe true

        verify(exactly = 1) {
            billEventPublisher.publish(
                any(),
                match<RegisterUpdated> {
                    it.billId == mockedBillId &&
                        it.walletId == walletId &&
                        it.updatedRegisterData is UpdatedRegisterData.VehicleDebtEnrichment &&
                        (it.updatedRegisterData as UpdatedRegisterData.VehicleDebtEnrichment).externalId == externalId &&
                        (it.updatedRegisterData as UpdatedRegisterData.VehicleDebtEnrichment).details == details
                },
            )
        }
    }
}