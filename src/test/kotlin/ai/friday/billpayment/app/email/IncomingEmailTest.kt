package ai.friday.billpayment.app.email

import io.kotest.matchers.shouldBe
import io.via1.communicationcentre.app.email.IncomingEmail
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

internal class IncomingEmailTest {

    @ParameterizedTest
    @CsvSource(value = ["131.511.970-60,13151197060", "13151197060,13151197060"])
    fun `deve retornar o documento sem formatacao`(document: String, expected: String) {
        val incomingEmail = IncomingEmail()
        incomingEmail.recipient = "$<EMAIL>"
        incomingEmail.extractDocument() shouldBe expected
    }
}