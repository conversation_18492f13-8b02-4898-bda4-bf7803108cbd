package ai.friday.billpayment.app.email

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.DEFAULT_LEGACY_ACCOUNT_CONFIGURATION
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.MailboxListsService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.ParserService
import ai.friday.billpayment.app.mailbox.MailboxGlobalData
import ai.friday.billpayment.app.mailbox.MailboxListType
import ai.friday.billpayment.app.notification.EmailNotProcessedNotificationRequest
import ai.friday.billpayment.app.notification.NotificationRouterService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WalletFixture
import io.kotest.matchers.date.shouldHaveSameDayAs
import io.kotest.matchers.shouldBe
import io.mockk.called
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.via1.communicationcentre.app.email.Attachment
import io.via1.communicationcentre.app.email.IncomingEmail
import io.via1.communicationcentre.app.integrations.EmailSenderService
import io.via1.communicationcentre.app.message.ForwardMessageException
import io.via1.communicationcentre.app.parser.ParsedBill
import io.via1.communicationcentre.app.receipt.Action
import io.via1.communicationcentre.app.receipt.Receipt
import io.via1.communicationcentre.app.receipt.SQSEvent
import io.via1.communicationcentre.app.receipt.Status
import io.via1.communicationcentre.app.receipt.Verdict
import jakarta.mail.internet.InternetAddress
import java.io.IOException
import java.time.LocalDate
import org.apache.commons.io.IOUtils
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class DefaultEmailServiceTest {

    private val forwardEmailAddress = "<EMAIL>"
    private val action = Action("", "", "", "", "")
    private val receiptVirus = Receipt(
        null,
        null,
        null,
        null,
        Verdict(Status.PASS),
        Verdict(Status.FAIL),
        Verdict(Status.PASS),
        Verdict(Status.PASS),
        Verdict(Status.PASS),
        action,
    )
    private val receiptOk = Receipt(
        null,
        null,
        null,
        null,
        Verdict(Status.PASS),
        Verdict(Status.PASS),
        Verdict(Status.PASS),
        Verdict(Status.PASS),
        Verdict(Status.PASS),
        action,
    )
    private val incomingEmail = IncomingEmail().apply {
        sender = InternetAddress(ACCOUNT.emailAddress.toString())
        recipient = "<EMAIL>"
        subject = "Nova conta"
        plainContent = ""
        attachments =
            listOf(Attachment.of(IOUtils.toInputStream("some test data for my input stream", "UTF-8"), "", ""))
    }
    private val forwadingEmail = IncomingEmail().apply {
        sender = InternetAddress("<EMAIL>")
        recipient = "<EMAIL>"
        subject = "Nova conta"
        plainContent = ""
        attachments =
            listOf(Attachment.of(IOUtils.toInputStream("some test data for my input stream", "UTF-8"), "", ""))
    }
    private val incomingEmailWithSenderConfiguration = IncomingEmail().apply {
        sender = InternetAddress(forwardEmailAddress)
        recipient = "<EMAIL>"
        subject = "Nova conta"
        plainContent = ""
        attachments =
            listOf(Attachment.of(IOUtils.toInputStream("some test data for my input stream", "UTF-8"), "", ""))
    }

    private val mockParser: ParserService = mockk {
        every {
            parseBillFrom(any())
        } returns emptyList()
    }

    private val mockEmailSenderService: EmailSenderService = mockk(relaxed = true)

    private val mockAccountRepository: AccountRepository = mockk(relaxed = true)

    private val notificationAdapter: NotificationAdapter = mockk(relaxed = true)

    private val walletService: WalletService = mockk()

    private val notificationRouterService = mockk<NotificationRouterService>(relaxed = true)

    private val mailBoxListsService = mockk<MailboxListsService>(relaxed = true)

    private val messagePublisherMock = mockk<MessagePublisher>(relaxed = true)

    private val mockMailObjectService = mockk<MailObjectService>(relaxUnitFun = true)

    private val defaultEmailService: DefaultEmailService =
        DefaultEmailService(
            billParseService = mockParser,
            accountService = AccountService(
                accountConfigurationService = mockk(),
                accountRepository = mockAccountRepository,
                chatbotMessagePublisher = mockk(),
                crmService = mockk(),
                notificationAdapter = mockk(),
                walletRepository = mockk(),
            ),
            emailSenderService = mockEmailSenderService,
            walletService = walletService,
            mailObjectService = mockMailObjectService,
            manualWorkflowEmail = "manualWorkflowEmail",
            forwardEmailToManualWorkflow = true,
            forwardEmailAddress = forwardEmailAddress,
            notificationRouterService = notificationRouterService,
            messagePublisher = messagePublisherMock,
            addBillFromEmailQueueName = "add-bill-queue",
        )

    @BeforeEach
    fun init() {
        clearMocks(mockParser, mockEmailSenderService, mockAccountRepository)
        refreshGlobalData()
    }

    private fun refreshGlobalData(blockList: List<String> = emptyList(), allowedList: List<String> = emptyList(), doNotDisturb: List<String> = emptyList()) {
        every {
            mailBoxListsService.findGlobal(MailboxListType.BLOCKED)
        } returns blockList
        every {
            mailBoxListsService.findGlobal(MailboxListType.ALLOWED)
        } returns allowedList

        every {
            mailBoxListsService.findGlobal(MailboxListType.DO_NOT_DISTURB)
        } returns doNotDisturb
        MailboxGlobalData(mailBoxListsService).loadData()
    }

    @Test
    fun `should move to quarantine when receipt show that S3 record has a virus`() {
        val account =
            ACCOUNT.copy(configuration = ACCOUNT.configuration.copy(defaultWalletId = WalletId(WALLET_ID)))
        every { mockAccountRepository.listAccountsByDocument(any()) } answers { listOf(account) }
        every {
            mockMailObjectService.retrieveEmailFromS3(any())
        } returns IncomingEmailWithMetadata(incomingEmail, 1000)
        every {
            mockMailObjectService.moveToQuarantine(any(), any())
        } returns "quarantinePath"

        defaultEmailService.process(SQSEvent(null, receiptVirus, null))
        verify {
            mockMailObjectService.moveToQuarantine(any(), any())
        }
    }

    @Test
    fun `should send to manual flow when unable to find bill and sender is not in block list`() {
        val account =
            ACCOUNT.copy(configuration = ACCOUNT.configuration.copy(defaultWalletId = WalletId(WALLET_ID)))
        every { walletService.findWallet(WalletId(WALLET_ID)) } returns wallet
        every { mockAccountRepository.listAccountsByDocument(any()) } answers { listOf(account) }
        every { mockAccountRepository.findById(any()) } answers { account }
        every {
            mockMailObjectService.retrieveEmailFromS3(any())
        } returns IncomingEmailWithMetadata(incomingEmail, 1000)
        every {
            mockParser.parseBillFrom(any())
        } returns emptyList()
        every {
            mockMailObjectService.moveToUnprocessed(any(), any(), any())
        } returns "unprocessedPath"
        refreshGlobalData()
        defaultEmailService.process(SQSEvent(null, receiptOk, null))
        verify {
            notificationRouterService.routeNotification(
                withArg<EmailNotProcessedNotificationRequest> {
                    it.members shouldBe wallet.activeMembers
                    it.walletName shouldBe wallet.name
                    it.sender shouldBe EmailAddress(incomingEmail.sender.address)
                    it.subject shouldBe incomingEmail.subject
                },
            )
            mockEmailSenderService.forward(eq(incomingEmail), eq("manualWorkflowEmail".lowercase()))
            mockMailObjectService.moveToUnprocessed(any(), account.accountId, NO_BILLS_FOUND_PATH)
        }
    }

    @Test
    fun `should not send to manual flow when unable to find bill and sender is in do not disturb list`() {
        val account =
            ACCOUNT.copy(configuration = ACCOUNT.configuration.copy(defaultWalletId = WalletId(WALLET_ID)))
        every { walletService.findWallet(WalletId(WALLET_ID)) } returns wallet
        every { mockAccountRepository.listAccountsByDocument(any()) } answers { listOf(account) }
        every { mockAccountRepository.findById(any()) } answers { account }
        every {
            mockMailObjectService.retrieveEmailFromS3(any())
        } returns IncomingEmailWithMetadata(incomingEmail, 1000)
        every {
            mockParser.parseBillFrom(any())
        } returns emptyList()
        every {
            mockMailObjectService.moveToUnprocessed(any(), any(), any())
        } returns "unprocessedPath"
        refreshGlobalData(doNotDisturb = listOf(incomingEmail.sender.address))
        defaultEmailService.process(SQSEvent(null, receiptOk, null))
        verify {
            notificationRouterService wasNot called
            mockEmailSenderService wasNot called
            mockMailObjectService.moveToUnprocessed(any(), account.accountId, NO_BILLS_FOUND_PATH)
        }
    }

    @Test
    fun `quando o email é processado com sucesso, deve enviar para fila de processamentos de contas`() {
        every {
            mockMailObjectService.retrieveEmailFromS3(any())
        } returns IncomingEmailWithMetadata(incomingEmail, 1000)
        every { mockAccountRepository.listAccountsByDocument(any()) } answers { listOf(ACCOUNT) }
        every { mockParser.parseBillFrom(eq(incomingEmail)) } answers {
            listOf(
                ParsedBill().apply {
                    digitableLine = "01234567890123456789012345678901234567890123456"; dueDate = "20/02/2020"
                },
                ParsedBill().apply {
                    digitableLine = "01234567890123456789012345678901234567890123455"; dueDate = "20/03/2020"
                },
            )
        }
        every {
            mockMailObjectService.buildProcessedPath(any(), any(), any())
        } returns "processedPath"

        defaultEmailService.process(SQSEvent(null, receiptOk, null))

        val slot = mutableListOf<AddBoletoFromEmailMessage>()

        verify {
            mockMailObjectService.moveTo(any(), any(), any())
        }
        verify(exactly = 2) {
            messagePublisherMock.sendMessage("add-bill-queue", capture(slot))
        }
        verify(exactly = 0) {
            mockMailObjectService.moveToUnprocessed(any(), any(), any())
            mockMailObjectService.moveToQuarantine(any(), any())
        }

        with(slot.first()) {
            walletId shouldBe ACCOUNT.configuration.defaultWalletId
            barCode.digitable shouldBe "01234567890123456789012345678901234567890123456"
            dueDate!! shouldHaveSameDayAs LocalDate.parse("2020-02-20")
        }
        with(slot.last()) {
            walletId shouldBe ACCOUNT.configuration.defaultWalletId
            barCode.digitable shouldBe "01234567890123456789012345678901234567890123455"
            dueDate!! shouldHaveSameDayAs LocalDate.parse("2020-03-20")
        }
    }

    @Test
    fun `should forward email when sender is not the user`() {
        every {
            mockMailObjectService.retrieveEmailFromS3(any())
        } returns IncomingEmailWithMetadata(forwadingEmail, 1000)
        every { mockAccountRepository.listAccountsByDocument(any()) } answers { listOf(ACCOUNT) }
        every { mockParser.parseBillFrom(eq(forwadingEmail)) } answers {
            listOf(
                ParsedBill().apply {
                    digitableLine = "01234567890123456789012345678901234567890123456"; dueDate = "20/02/2020"
                },
            )
        }
        every {
            mockMailObjectService.buildProcessedPath(any(), any(), any())
        } returns "processedPath"

        defaultEmailService.process(SQSEvent(null, receiptOk, null))
        verify {
            mockEmailSenderService.forward(eq(forwadingEmail), eq(ACCOUNT.emailAddress.toString()))
            mockMailObjectService.moveTo(any(), any(), "processedPath")
        }
    }

    @Test
    fun `should not interrupt when forward email throw exception`() {
        every {
            mockMailObjectService.retrieveEmailFromS3(any())
        } returns IncomingEmailWithMetadata(forwadingEmail, 1000)
        every { mockAccountRepository.listAccountsByDocument(any()) } answers { listOf(ACCOUNT) }
        every {
            mockEmailSenderService.forward(
                any(),
                any(),
            )
        } throws ForwardMessageException(ACCOUNT.emailAddress.toString(), IOException("teste"))
        every { mockParser.parseBillFrom(eq(forwadingEmail)) } answers {
            listOf(
                ParsedBill().apply {
                    digitableLine = "01234567890123456789012345678901234567890123456"; dueDate = "20/02/2020"
                },
            )
        }
        every {
            mockMailObjectService.buildProcessedPath(any(), any(), any())
        } returns "processedPath"

        defaultEmailService.process(SQSEvent(null, receiptOk, null))

        verify {
            mockEmailSenderService.forward(eq(forwadingEmail), eq(ACCOUNT.emailAddress.toString()))
            messagePublisherMock.sendMessage("add-bill-queue", any())
            mockMailObjectService.moveTo(any(), any(), "processedPath")
        }
    }

    @Test
    fun `should not add bill when account is closed`() {
        val closedAccount = ACCOUNT.copy(status = AccountStatus.CLOSED)
        every { mockAccountRepository.listAccountsByDocument(any()) } answers { listOf(closedAccount) }
        every {
            mockMailObjectService.retrieveEmailFromS3(any())
        } returns IncomingEmailWithMetadata(incomingEmail, 1000)
        every {
            mockMailObjectService.moveToUnprocessed(any(), any(), any())
        } returns "unprocessedPath"

        defaultEmailService.process(SQSEvent(null, receiptOk, null))

        verify {
            mockMailObjectService.moveToUnprocessed(any(), ACCOUNT.accountId, ACCOUNT_CLOSED_PATH)
        }

        verify(exactly = 0) {
            messagePublisherMock.sendMessage(any(), any())
        }
    }

    val walletFixture = WalletFixture()
    val wallet = walletFixture.buildWallet(
        otherMembers = listOf(
            walletFixture.participant,
            walletFixture.ultraLimitedParticipant,
        ),
    )

    @Test
    fun `se o email vier do nosso email de configuração, não deve processar`() {
        val testAccount =
            ACCOUNT.copy(configuration = DEFAULT_LEGACY_ACCOUNT_CONFIGURATION.copy(defaultWalletId = wallet.id))
        every { mockAccountRepository.listAccountsByDocument(any()) } answers { listOf(testAccount) }
        every {
            mockMailObjectService.retrieveEmailFromS3(any())
        } returns IncomingEmailWithMetadata(incomingEmailWithSenderConfiguration, 1000)
        every {
            mockMailObjectService.moveToUnprocessed(any(), any(), any())
        } returns "unprocessedPath"

        defaultEmailService.process(SQSEvent(null, receiptOk, null))

        verify {
            mockMailObjectService.moveToUnprocessed(any(), ACCOUNT.accountId, LOOP_DETECTED_PATH)
        }

        verify(exactly = 0) {
            mockEmailSenderService.forward(eq(incomingEmail), eq("manualWorkflowEmail"))
            notificationAdapter wasNot called
        }
    }
}