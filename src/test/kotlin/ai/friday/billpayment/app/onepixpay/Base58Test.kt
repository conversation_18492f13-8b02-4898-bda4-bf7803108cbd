package ai.friday.billpayment.app.onepixpay

import io.kotest.matchers.shouldBe
import java.util.UUID
import java.util.stream.Stream
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

internal class Base58Test {
    @ParameterizedTest
    @MethodSource("generateUUIDs")
    fun `encode and decode uuids`(uuid: UUID) {
        val encondedUUID = Base58.encode(uuid)

        Base58.decodeToUUID(encondedUUID) shouldBe uuid
    }

    companion object {
        @JvmStatic
        fun generateUUIDs(): Stream<Arguments> {
            val list = buildList<Arguments> {
                repeat(10) {
                    add(Arguments.of(UUID.randomUUID()))
                }
            }
            return Stream.of(*list.toTypedArray())
        }
    }
}