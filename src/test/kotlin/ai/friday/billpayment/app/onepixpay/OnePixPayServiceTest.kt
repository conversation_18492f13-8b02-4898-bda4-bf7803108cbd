package ai.friday.billpayment.app.onepixpay

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.dynamodb.OnePixPayDbRepository
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.balance.Balance
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillViewSchedule
import ai.friday.billpayment.app.bill.PaymentStarted
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.bill.schedule.ScheduleBillService
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.EndToEnd
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.OnePixPayInstrumentation
import ai.friday.billpayment.app.integrations.PixPaymentService
import ai.friday.billpayment.app.integrations.PixQRCodeCreatorService
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.PaymentMethodsDetail
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.ScheduleStrategy
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.PixStatementItem
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.fichaCompensacaoCreditCardAdded
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.BILL_ID_3
import ai.friday.billpayment.integration.BILL_ID_4
import ai.friday.billpayment.integration.BILL_ID_5
import ai.friday.billpayment.integration.CORRELATION_ID
import ai.friday.billpayment.integration.WALLET_ID_2
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.internalBankAccount
import ai.friday.billpayment.pixKeyAdded
import ai.friday.billpayment.pixKeyPaymentScheduled
import ai.friday.billpayment.scheduledBill
import ai.friday.billpayment.transactionId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Called
import io.mockk.Runs
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import java.time.LocalDate
import java.time.LocalTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource

internal class OnePixPayServiceTest {

    private val onePixPayRepository = mockk<OnePixPayDbRepository>() {
        every { save(any()) } answers { this.firstArg() }
    }
    private val qrCodeService = mockk<PixQRCodeCreatorService>() {
        every { createQRCode(any(), any(), any(), any(), any()) } returns "qrcode"
    }
    private val accountService = mockk<AccountService>() {
        every { findAccountById(wallet.founder.accountId) } returns walletFixture.founderAccount
    }
    private val pixKeyDomain = "@fake.io"
    private val walletService = mockk<WalletService>() {
        every { findWallet(walletId = wallet.id) } returns wallet
        every { walletPixKey(wallet) } returns PixKey("${wallet.founder.document}$pixKeyDomain", PixKeyType.EMAIL)
        every { walletEvpPixKey(wallet.id) } returns null
    }

    private val scheduleBillService = mockk<ScheduleBillService>() {
        every {
            processScheduleAsync(any())
        } just Runs

        every {
            schedulePayment(
                batchSchedulingId = any(),
                paymentWallet = any(),
                bill = any<Bill>(),
                member = any(),
                actionSource = any(),
                scheduleStrategy = any(),
                paymentMethodsDetail = any(),
                fingerprint = any(),
            )
        } answers { thirdArg<Bill>().right() }

        every {
            getOrderedScheduledBills(any(), any())
        } returns emptyList()
    }

    private val billEventRepository = mockk<BillEventRepository>() {
        every { getBillById(concessionaria.billId) } returns Either.Right(concessionaria)
        every { getBillById(fichaCompensasao.billId) } returns Either.Right(fichaCompensasao)
        every { getBillById(pix.billId) } returns Either.Right(pix)
        every { getBillById(pixPaid.billId) } returns Either.Right(pixPaid)
        every { getBillById(subscriptionFee.billId) } returns Either.Right(subscriptionFee)
    }

    private val notificationAdapter = mockk<NotificationAdapter>(relaxUnitFun = true)

    private val pixPaymentService = mockk<PixPaymentService>()
    private val balanceService = mockk<BalanceService>()
    private val billEventPublisher = mockk<BillEventPublisher>(relaxed = true)
    private val messagePublisher = mockk<MessagePublisher>(relaxUnitFun = true)
    private val walletRepository = mockk<WalletRepository>()

    private val onePixPayInstrumentation = mockk<OnePixPayInstrumentation>(relaxed = true)
    private val onePixPayService =
        spyk(
            OnePixPayService(
                accountService = accountService,
                onePixPayRepository = onePixPayRepository,
                qrCodeService = qrCodeService,
                walletService = walletService,
                billEventRepository = billEventRepository,
                scheduleBillService = scheduleBillService,
                notificationAdapter = notificationAdapter,
                pixPaymentService = pixPaymentService,
                balanceService = balanceService,
                billEventPublisher = billEventPublisher,
                onePixPayInstrumentation = onePixPayInstrumentation,
                messagePublisher = messagePublisher,
                lockProvider = mockk(relaxed = true),
                onePixPayRequestedQueueName = "onePixPayRequestedQueueName",
                walletRepository = walletRepository,
            ),
        )

    @Nested
    @DisplayName("criar one pix pay")
    inner class CreateOnePixPayTest {

        val billsReadyToCreateOPP = listOf(
            getActiveBill(
                billId = BillId(BILL_ID_2),
                walletId = wallet.id,
                schedule = null,
                paymentLimitTime = "18:00",
            ).copy(amountTotal = 100L),
            getActiveBill(
                billId = BillId(BILL_ID_3),
                walletId = wallet.id,
                schedule = null,
                paymentLimitTime = "19:00",
            ).copy(amountTotal = 200L),
            getActiveBill(
                billId = BillId(BILL_ID_4),
                walletId = wallet.id,
                schedule = BillViewSchedule(
                    date = getLocalDate(),
                    waitingFunds = false,
                    waitingRetry = false,
                ),
                subscriptionFee = true,
                paymentLimitTime = "19:00",
            ).copy(amountTotal = 300L),
            getActiveBill(
                billId = BillId(BILL_ID_5),
                walletId = wallet.id,
                schedule = BillViewSchedule(
                    date = getLocalDate(),
                    waitingFunds = false,
                    waitingRetry = false,
                ),
                subscriptionFee = false,
                paymentLimitTime = "23:59",
            ).copy(amountTotal = 400L),
            getActiveBill(
                billId = BillId(BILL_ID_4),
                walletId = wallet.id,
                schedule = BillViewSchedule(
                    date = getLocalDate(),
                    waitingFunds = false,
                    waitingRetry = false,
                ),
                subscriptionFee = true,
                paymentLimitTime = "19:00",
            ).copy(amountTotal = 300L),
        )

        val billsReadyToCreateOPPTotalAmount = billsReadyToCreateOPP.toSet().sumOf { it.amountTotal }

        @Test
        fun `deve retornar AtLeastOneBillRequired quando nao houver nenhuma conta`() {
            with(onePixPayService.create(listOf(), false)) {
                isLeft().shouldBeTrue()
                mapLeft {
                    it shouldBe OnePixPayErrors.AtLeastOneBillRequired
                }
            }
        }

        @Test
        fun `deve retornar SingleWalletRequired quando as contas pertencerem a mais que uma carteira`() {
            val result = onePixPayService.create(
                listOf(
                    getActiveBill(
                        billId = BillId(BILL_ID_2),
                        walletId = wallet.id,
                        schedule = null,
                    ),
                    getActiveBill(
                        billId = BillId(BILL_ID_3),
                        walletId = anotherWallet.id,
                        schedule = null,
                    ),
                ),
                false,
            )

            result.isLeft().shouldBeTrue()
            result.mapLeft {
                it.shouldBeInstanceOf<OnePixPayErrors.SingleWalletRequired>()
            }
        }

        @Test
        fun `deve retornar OnlyActiveOrScheduledBillsAreAllowed quando alguma conta nao esta em aberto`() {
            val result = onePixPayService.create(
                listOf(
                    getActiveBill(
                        billId = BillId(BILL_ID_2),
                        walletId = wallet.id,
                    ).copy(status = BillStatus.IGNORED),
                    getActiveBill(
                        billId = BillId(BILL_ID_3),
                        walletId = wallet.id,
                        schedule = null,
                    ),
                ),
                false,
            )
            result.isLeft().shouldBeTrue()
            result.mapLeft {
                it shouldBe OnePixPayErrors.OnlyActiveOrScheduledBillsAreAllowed
            }
        }

        @Test
        fun `deve gerar um pedido de 1-pix pay correto ao receber somente contas em aberto ou assinaturas ou agendadas sem considerar o saldo atual do usuário`() {
            val result = onePixPayService.create(billsReadyToCreateOPP, false)
            result.isRight().shouldBeTrue()

            result.map { onePixPay ->
                val slot = slot<OnePixPay>()
                val pixKeySlot = slot<PixKey>()
                verify(exactly = 1) {
                    qrCodeService.createQRCode(
                        key = capture(pixKeySlot),
                        document = wallet.founder.document,
                        amount = billsReadyToCreateOPPTotalAmount,
                        recipientName = wallet.founder.name,
                        transactionId = onePixPay.id.source as QrCodeTransactionId,
                    )

                    onePixPayRepository.save(capture(slot))
                }

                pixKeySlot.captured.value shouldBe "***********$pixKeyDomain"
                with(slot.captured) {
                    assertSoftly {
                        walletId shouldBe wallet.id
                        qrCode shouldBe "qrcode"
                        status shouldBe OnePixPayStatus.CREATED
                        fundsReceived.shouldBeFalse()
                        paymentLimitTime shouldBe LocalTime.parse("18:00")
                        fundsReceivedAt.shouldBeNull()
                        billIds shouldBe listOf(BillId(BILL_ID_2), BillId(BILL_ID_3), BillId(BILL_ID_4), BillId(BILL_ID_5))
                    }
                }
            }
        }

        @Test
        fun `deve gerar um pedido de 1-pix pay correto considerando o saldo do usuário e o usuário não tiver saldo suficiente`() {
            val missingAmount = 200L
            val currentBalance = billsReadyToCreateOPPTotalAmount - missingAmount
            every {
                balanceService.getBalanceFrom(wallet.founder.accountId, wallet.paymentMethodId)
            } returns Balance(currentBalance)

            val result = onePixPayService.create(billsReadyToCreateOPP, true)
            result.isRight().shouldBeTrue()

            result.map { onePixPay ->
                val slot = slot<OnePixPay>()
                val pixKeySlot = slot<PixKey>()
                verify(exactly = 1) {
                    qrCodeService.createQRCode(
                        key = capture(pixKeySlot),
                        document = wallet.founder.document,
                        amount = missingAmount,
                        recipientName = wallet.founder.name,
                        transactionId = onePixPay.id.source as QrCodeTransactionId,
                    )

                    onePixPayRepository.save(capture(slot))
                }
                pixKeySlot.captured.value shouldBe "***********$pixKeyDomain"
                pixKeySlot.captured.value shouldBe "***********$pixKeyDomain"

                with(slot.captured) {
                    assertSoftly {
                        walletId shouldBe wallet.id
                        qrCode shouldBe "qrcode"
                        status shouldBe OnePixPayStatus.CREATED
                        fundsReceived.shouldBeFalse()
                        paymentLimitTime shouldBe LocalTime.parse("18:00")
                        fundsReceivedAt.shouldBeNull()
                        billIds shouldBe listOf(BillId(BILL_ID_2), BillId(BILL_ID_3), BillId(BILL_ID_4), BillId(BILL_ID_5))
                    }
                }
            }
        }

        @Test
        fun `deve gerar um pedido de 1-pix pay correto utilizando a chave aleatória`() {
            val missingAmount = 200L
            val currentBalance = billsReadyToCreateOPPTotalAmount - missingAmount
            every {
                balanceService.getBalanceFrom(wallet.founder.accountId, wallet.paymentMethodId)
            } returns Balance(currentBalance)

            every {
                walletService.walletEvpPixKey(any())
            } returns PixKey(type = PixKeyType.EVP, value = "6a7e8ec1-b3a6-4910-8e54-5a8062eaee72")

            val result = onePixPayService.create(billsReadyToCreateOPP, true)
            result.isRight().shouldBeTrue()

            result.map { onePixPay ->
                val slot = slot<OnePixPay>()
                val pixKeySlot = slot<PixKey>()
                verify(exactly = 1) {
                    qrCodeService.createQRCode(
                        key = capture(pixKeySlot),
                        document = wallet.founder.document,
                        amount = missingAmount,
                        recipientName = wallet.founder.name,
                        transactionId = onePixPay.id.source as QrCodeTransactionId,
                    )

                    onePixPayRepository.save(capture(slot))
                }
                pixKeySlot.captured.value shouldBe "6a7e8ec1-b3a6-4910-8e54-5a8062eaee72"

                with(slot.captured) {
                    assertSoftly {
                        walletId shouldBe wallet.id
                        qrCode shouldBe "qrcode"
                        status shouldBe OnePixPayStatus.CREATED
                        fundsReceived.shouldBeFalse()
                        paymentLimitTime shouldBe LocalTime.parse("18:00")
                        fundsReceivedAt.shouldBeNull()
                        billIds shouldBe listOf(BillId(BILL_ID_2), BillId(BILL_ID_3), BillId(BILL_ID_4), BillId(BILL_ID_5))
                    }
                }
            }
        }

        @Test
        fun `deve falhar um pedido de 1-pix pay correto considerando o saldo do usuário e o usuário tiver saldo suficiente`() {
            every {
                balanceService.getBalanceFrom(wallet.founder.accountId, wallet.paymentMethodId)
            } returns Balance(billsReadyToCreateOPPTotalAmount)

            val result = onePixPayService.create(billsReadyToCreateOPP, true)
            result.isLeft().shouldBeTrue()
            result.mapLeft {
                it.shouldBeTypeOf<OnePixPayErrors.InvalidAmount>()
            }
        }
    }

    @Nested
    @DisplayName("processar agendamentos do one pix pay")
    inner class ProcessOnePixPayScheduleTest {

        @ParameterizedTest
        @EnumSource(OnePixPayStatus::class, mode = EnumSource.Mode.INCLUDE, names = ["CREATED", "PROCESSED"])
        fun `não deve agendar os pagamentos quando o One Pix Pay estiver no status`(onePixPayStatus: OnePixPayStatus) {
            val createdOnePixPay = uuidOnePixPay.copy(status = onePixPayStatus)
            every { onePixPayRepository.find(any()) } returns createdOnePixPay

            assertThrows<ProcessScheduleError.OnePixPayNotRequested> {
                onePixPayService.processSchedule(createdOnePixPay)
            }

            verify(exactly = 0) {
                scheduleBillService.schedulePayments(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                )
                onePixPayRepository.save(any())
                notificationAdapter wasNot Called
            }
        }

        @Test
        fun `não deve agendar as contas se o one pix pay estiver EXPIRED e deve notificar ao usuário`() {
            val expiredOnePixPay = uuidOnePixPay.copy(status = OnePixPayStatus.EXPIRED)
            every { onePixPayRepository.find(any()) } returns expiredOnePixPay

            assertThrows<IllegalStateException> {
                onePixPayService.processSchedule(expiredOnePixPay)
            }

            verify(exactly = 1) {
                notificationAdapter.notifyOnePixPayFailure(wallet.founder.accountId)
            }

            verify(exactly = 0) {
                onePixPayRepository.save(any())
                scheduleBillService.schedulePayments(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                )
            }
        }

        @Test
        fun `não deve agendar quando um pagamento não for encontrado no One Pix Pay`() {
            every { onePixPayRepository.find(any()) } returns uuidOnePixPay
            every { billEventRepository.getBillById(any()) } returns Either.Left(ItemNotFoundException(""))

            assertThrows<ProcessScheduleError.BillNotFound> {
                onePixPayService.processSchedule(uuidOnePixPay)
            }

            val slot = slot<OnePixPay>()
            verify(exactly = 1) {
                onePixPayRepository.save(capture(slot))
                notificationAdapter.notifyOnePixPayFailure(wallet.founder.accountId)
            }

            slot.captured verifyFundReceivedWithStatus OnePixPayStatus.ERROR

            verify(exactly = 0) {
                scheduleBillService.schedulePayments(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                )
            }
        }

        @Test
        fun `deve agendar e não processar as contas se o one pix pay estiver REQUESTED`() {
            every { onePixPayRepository.find(any()) } returns uuidOnePixPay

            onePixPayService.processSchedule(uuidOnePixPay)

            val slot = slot<OnePixPay>()
            val billSlot = mutableListOf<Bill>()
            val detailsSlot = mutableListOf<PaymentMethodsDetail>()
            verify {
                onePixPayRepository.save(capture(slot))
                scheduleBillService.schedulePayment(
                    batchSchedulingId = any(),
                    paymentWallet = wallet,
                    bill = capture(billSlot),
                    member = wallet.founder,
                    actionSource = ActionSource.OnePixPay,
                    scheduleStrategy = ScheduleStrategy.ofAsap(),
                    paymentMethodsDetail = capture(detailsSlot),
                    fingerprint = any(),
                )
            }

            verify(exactly = 0) {
                scheduleBillService.processScheduleAsync(any())
            }

            billSlot.size shouldBe 3
            billSlot[0] shouldBe concessionaria
            billSlot[1] shouldBe fichaCompensasao
            billSlot[2] shouldBe pix

            detailsSlot.size shouldBe 3
            detailsSlot[0] shouldBe PaymentMethodsDetailWithBalance(
                amount = concessionaria.amountTotal,
                paymentMethodId = wallet.paymentMethodId,
                calculationId = null,
            )
            detailsSlot[1] shouldBe PaymentMethodsDetailWithBalance(
                amount = fichaCompensasao.amountTotal,
                paymentMethodId = wallet.paymentMethodId,
                calculationId = null,
            )
            detailsSlot[2] shouldBe PaymentMethodsDetailWithBalance(
                amount = pix.amountTotal,
                paymentMethodId = wallet.paymentMethodId,
                calculationId = null,
            )

            slot.captured verifyFundReceivedWithStatus OnePixPayStatus.PROCESSED
        }

        @Test
        fun `deve agendar apenas as contas que não estão agendadas se o one pix pay estiver REQUESTED`() {
            every { onePixPayRepository.find(any()) } returns uuidOnePixPay

            every { billEventRepository.getBillById(pix.billId) } returns pix.apply(
                pixKeyPaymentScheduled,
            ).right()

            onePixPayService.processSchedule(uuidOnePixPay)

            val slot = slot<OnePixPay>()
            val billSlot = mutableListOf<Bill>()
            val detailsSlot = mutableListOf<PaymentMethodsDetail>()
            verify {
                onePixPayRepository.save(capture(slot))
                scheduleBillService.schedulePayment(
                    batchSchedulingId = any(),
                    paymentWallet = wallet,
                    bill = capture(billSlot),
                    member = wallet.founder,
                    actionSource = ActionSource.OnePixPay,
                    scheduleStrategy = ScheduleStrategy.ofAsap(),
                    paymentMethodsDetail = capture(detailsSlot),
                    fingerprint = any(),
                )
            }
            slot.captured verifyFundReceivedWithStatus OnePixPayStatus.PROCESSED

            billSlot.size shouldBe 2
            billSlot[0] shouldBe concessionaria
            billSlot[1] shouldBe fichaCompensasao

            detailsSlot.size shouldBe 2
            detailsSlot[0] shouldBe PaymentMethodsDetailWithBalance(
                amount = concessionaria.amountTotal,
                paymentMethodId = wallet.paymentMethodId,
                calculationId = null,
            )
            detailsSlot[1] shouldBe PaymentMethodsDetailWithBalance(
                amount = fichaCompensasao.amountTotal,
                paymentMethodId = wallet.paymentMethodId,
                calculationId = null,
            )
        }

        @Test
        fun `não deve agendar as contas se o one pix pay tiver passado da hora limite e deve alterar o status para EXPIRED e deve notificar ao usuário`() {
            val earlyLimitOnePixPay = uuidOnePixPay.copy(paymentLimitTime = LocalTime.NOON)
            every { onePixPayRepository.find(any()) } returns earlyLimitOnePixPay

            withGivenDateTime(getZonedDateTime().with(LocalTime.MAX)) {
                assertThrows<ProcessScheduleError.OnePixPayExpired> {
                    onePixPayService.processSchedule(
                        earlyLimitOnePixPay,
                    )
                }
            }

            verify(exactly = 0) {
                scheduleBillService.schedulePayments(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                )
            }

            val slot = slot<OnePixPay>()
            verify(exactly = 1) {
                onePixPayRepository.save(capture(slot))
                notificationAdapter.notifyOnePixPayFailure(wallet.founder.accountId)
            }
            slot.captured verifyFundReceivedWithStatus OnePixPayStatus.EXPIRED
        }

        @Test
        fun `não deve agendar as contas se o one pix pay tiver passado do dia de criacao e deve alterar o status para EXPIRED e deve notificar ao usuário`() {
            val now = getZonedDateTime()
            val outdatedOnePixPay = uuidOnePixPay.copy(created = now.minusDays(1))
            every { onePixPayRepository.find(any()) } returns outdatedOnePixPay

            withGivenDateTime(now.with(LocalTime.NOON)) {
                assertThrows<ProcessScheduleError.OnePixPayExpired> { onePixPayService.processSchedule(outdatedOnePixPay) }
            }

            verify(exactly = 0) {
                scheduleBillService.schedulePayments(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                )
            }

            val slot = slot<OnePixPay>()
            verify(exactly = 1) {
                onePixPayRepository.save(capture(slot))
                notificationAdapter.notifyOnePixPayFailure(wallet.founder.accountId)
            }
            slot.captured verifyFundReceivedWithStatus OnePixPayStatus.EXPIRED
        }

        @Test
        fun `se a conta não estiver aberta, deve alterar o status para ERROR, não deve continuar o processo e deve notificar o usuário`() {
            every { onePixPayRepository.find(any()) } returns uuidOnePixPay
            every { billEventRepository.getBillById(pix.billId) } returns pix.apply(
                pixKeyPaymentScheduled,
            ).apply(
                PaymentStarted(
                    walletId = pixKeyPaymentScheduled.walletId,
                    created = pixKeyPaymentScheduled.created + 1,
                    billId = pixKeyPaymentScheduled.billId,
                    transactionId = transactionId,
                    actionSource = ActionSource.System,
                    correlationId = CORRELATION_ID,
                ),
            ).right()

            assertThrows<ProcessScheduleError.BillNotAbleToSchedule> { onePixPayService.processSchedule(uuidOnePixPay) }

            verify(exactly = 0) {
                scheduleBillService.schedulePayments(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                )
            }

            val slot = slot<OnePixPay>()
            verify(exactly = 1) {
                onePixPayRepository.save(capture(slot))
                notificationAdapter.notifyOnePixPayFailure(wallet.founder.accountId)
            }
            slot.captured verifyFundReceivedWithStatus OnePixPayStatus.ERROR
        }

        @Test
        fun `se a conta não estiver aberta, mas estiver paga, deve continuar o processamento do one pix pay`() {
            every { onePixPayRepository.find(any()) } returns uuidOnePixPay.copy(
                billIds = listOf(concessionaria.billId, fichaCompensasao.billId, pixPaid.billId, subscriptionFee.billId),
            )

            onePixPayService.processSchedule(uuidOnePixPay)

            verify(exactly = 0) {
                scheduleBillService.schedulePayments(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                )
            }

            val slot = slot<OnePixPay>()
            verify(exactly = 1) {
                onePixPayRepository.save(capture(slot))
            }
            slot.captured verifyFundReceivedWithStatus OnePixPayStatus.PROCESSED

            verify(exactly = 0) {
                notificationAdapter.notifyOnePixPayFailure(wallet.founder.accountId)
            }
        }

        @Test
        fun `se o sistema não conseguir agendar um dos pagamentos, deve alterar o status para ERROR e deve notificar o usuário`() {
            every { onePixPayRepository.find(any()) } returns uuidOnePixPay
            every {
                scheduleBillService.schedulePayment(any(), any(), any<Bill>(), any(), any(), any(), any(), any())
            } answers {
                val bill = thirdArg<Bill>()
                if (bill.billId == fichaCompensasao.billId) {
                    NoStackTraceException("FAKE").left()
                } else {
                    bill.right()
                }
            }

            assertThrows<ProcessScheduleError.SomeBillsWereNotScheduled> { onePixPayService.processSchedule(uuidOnePixPay) }

            val slot = slot<OnePixPay>()
            verify(exactly = 1) {
                onePixPayRepository.save(capture(slot))
                notificationAdapter.notifyOnePixPayFailure(wallet.founder.accountId)
            }
            slot.captured verifyFundReceivedWithStatus OnePixPayStatus.ERROR
        }

        @Test
        fun `se alguma conta agendada não estiver no OPP a conta deverá ser desagendada`() {
            val billId = BillId("billId-que-nao-esta-no-opp")
            every {
                scheduleBillService.getOrderedScheduledBills(any(), any())
            } returns listOf(
                scheduledBill.copy(scheduledDate = getLocalDate().minusDays(1)),
                scheduledBill.copy(scheduledDate = getLocalDate().minusDays(1), billId = billId),
            )

            every { onePixPayRepository.find(any()) } returns uuidOnePixPay

            every {
                billEventRepository.getBillById(billId)
            } returns Bill.build(billAdded.copy(walletId = wallet.id, billId = billId), billPaymentScheduled).right()

            onePixPayService.processSchedule(uuidOnePixPay)

            verify(exactly = 1) {
                onePixPayRepository.save(
                    withArg {
                        it.status shouldBe OnePixPayStatus.PROCESSED
                    },
                )
                billEventPublisher.publish(
                    any(),
                    withArg {
                        it.shouldBeInstanceOf<BillPaymentScheduleCanceled>()
                        it.reason shouldBe ScheduleCanceledReason.NOT_SELECTED_ON_ONE_PIX_PAY
                        it.billId shouldBe billId
                    },
                )
            }

            verify(exactly = 0) {
                notificationAdapter.notifyOnePixPayFailure(wallet.founder.accountId)
            }
        }

        private infix fun OnePixPay.verifyFundReceivedWithStatus(onePixPayStatus: OnePixPayStatus) {
            status shouldBe onePixPayStatus
            fundsReceived.shouldBeTrue()
            fundsReceivedAt.shouldNotBeNull()
        }
    }

    @Nested
    @DisplayName("doProcess do one pix pay")
    inner class DoProccessTest {

        private val paymentMethod = internalBankAccount

        @BeforeEach
        fun setup() {
            every { onePixPayService.processSchedule(any()) } just Runs
        }

        @Test
        fun `quando não houver um one pix pay no banco, não deve processar o agendamento`() {
            setupOnePixPay()

            onePixPayService.process(wallet.id, paymentMethod)

            verify(exactly = 0) {
                onePixPayService.processSchedule(any())
            }
        }

        @Test
        fun `quando houver um erro ao pegar o extrato, não deve processar o agendamento`() {
            setupOnePixPay(uuidOnePixPay)
            every { pixPaymentService.getStatement((any())) } throws NoStackTraceException()

            assertThrows<NoStackTraceException> {
                onePixPayService.process(wallet.id, paymentMethod)
            }
            verify {
                pixPaymentService.getStatement(any())
            }
            verify(exactly = 0) {
                onePixPayService.processSchedule(any())
            }
        }

        @Test
        fun `quando houver um one pix pay no banco mas não é referente ao depósito nem ao extrato, não deve processar o agendamento`() {
            setupOnePixPay(uuidOnePixPay)
            setupPixStatement(UUIDTransactionId().toPixStatement())

            onePixPayService.process(wallet.id, paymentMethod)

            verify(exactly = 0) {
                onePixPayService.processSchedule(any())
            }
        }

        @Test
        fun `se não houver um one pix pay que bata com o transactionId, não deve processar o agendamento`() {
            setupOnePixPay(uuidOnePixPay)
            setupPixStatement()

            onePixPayService.process(wallet.id, paymentMethod, UUIDOnePixPayId())

            verify(exactly = 0) {
                onePixPayService.processSchedule(any())
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.onepixpay.OnePixPayServiceTest#validOPPs")
        fun `quando o one pix pay estiver no extrato com o mesmo uuid, deve processar o agendamento`(onePixPay: OnePixPay) {
            setupOnePixPay(onePixPay)
            setupPixStatement(onePixPay.id.toPixStatement())

            onePixPayService.process(wallet.id, paymentMethod)

            verify {
                onePixPayService.processSchedule(any())
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.onepixpay.OnePixPayServiceTest#validOPPs")
        fun `quando houver um one pix pay no banco que seja igual ao transactionId, deve processar o agendamento`(onePixPay: OnePixPay) {
            setupOnePixPay(onePixPay)
            setupPixStatement()

            onePixPayService.process(wallet.id, paymentMethod, onePixPay.id)

            verify {
                onePixPayService.processSchedule(any())
            }
        }

        @Test
        fun `quando retornar mais de um one pix pay em aberto deve processar o agendamento`() {
            setupOnePixPay(uuidOnePixPay.copy(id = UUIDOnePixPayId()), uuidOnePixPay, uuidOnePixPay.copy(id = UUIDOnePixPayId()))
            setupPixStatement(
                uuidOnePixPay.id.toPixStatement(),
                GenericTransactionId("fakeNaoVaiConsiderar").toPixStatement(),
            )

            onePixPayService.process(wallet.id, paymentMethod)

            verify {
                onePixPayService.processSchedule(any())
            }
        }

        private fun setupOnePixPay(vararg opps: OnePixPay) {
            every {
                onePixPayRepository.findByWalletId(
                    wallet.id,
                    getLocalDate(),
                    OnePixPayStatus.REQUESTED,
                )
            } returns opps.toList()
        }

        private fun OnePixPayId<*>.toPixStatement() = with(source) {
            when (this) {
                is EndToEnd -> toPixStatement()
                is UUIDTransactionId -> toPixStatement()
                else -> throw IllegalArgumentException()
            }
        }

        private fun QrCodeTransactionId.toPixStatement() = PixStatementItem(
            transactionId = this,
            flow = BankStatementItemFlow.CREDIT,
            endToEnd = EndToEnd(),
            date = LocalDate.now(),
            type = BankStatementItemType.TED_DIF_TITULARIDADE,
            description = "nonumy",
            operationNumber = "deterruisset",
            isTemporaryOperationNumber = false,
            amount = 4127,
            counterpartName = "Estelle Dean",
            counterpartDocument = "eros",
            counterpartAccountNo = null,
            counterpartBankName = null,
            documentNumber = "ultrices",
            ref = null,
            lastUpdate = null,
            metadata = null,
            notificatedAt = null,
        )

        private fun EndToEnd.toPixStatement() = PixStatementItem(
            transactionId = null,
            flow = BankStatementItemFlow.CREDIT,
            endToEnd = this,
            date = LocalDate.now(),
            type = BankStatementItemType.TED_DIF_TITULARIDADE,
            description = "nonumy",
            operationNumber = "deterruisset",
            isTemporaryOperationNumber = false,
            amount = 4127,
            counterpartName = "Estelle Dean",
            counterpartDocument = "eros",
            counterpartAccountNo = null,
            counterpartBankName = null,
            documentNumber = "ultrices",
            ref = null,
            lastUpdate = null,
            metadata = null,
            notificatedAt = null,
        )

        private fun setupPixStatement(vararg statements: PixStatementItem) {
            every {
                pixPaymentService.getStatement((any()))
            } returns statements.toList() + PixStatementItem(
                transactionId = null,
                flow = BankStatementItemFlow.DEBIT,
                endToEnd = EndToEnd(),
                date = LocalDate.now(),
                type = BankStatementItemType.TED_DIF_TITULARIDADE,
                description = "nonumy",
                operationNumber = "deterruisset",
                isTemporaryOperationNumber = false,
                amount = 4127,
                counterpartName = "Estelle Dean",
                counterpartDocument = "eros",
                counterpartAccountNo = null,
                counterpartBankName = null,
                documentNumber = "ultrices",
                ref = null,
                lastUpdate = null,
                metadata = null,
                notificatedAt = null,
            )
        }
    }

    @Nested
    @DisplayName("ao salvar um one pix pay")
    inner class SaveTest {
        private fun createOnePixPay(status: OnePixPayStatus): OnePixPay {
            val onePixPay = OnePixPay.create(
                listOf(getActiveBill()),
                EndToEnd(),
            ).getOrElse {
                throw RuntimeException(it.toString())
            }

            return when (status) {
                OnePixPayStatus.CREATED -> onePixPay
                OnePixPayStatus.REQUESTED -> onePixPay.asRequested()
                OnePixPayStatus.PROCESSED -> onePixPay.asRequested().asProcessed()
                OnePixPayStatus.EXPIRED -> onePixPay.asRequested().asExpired()
                OnePixPayStatus.ERROR -> onePixPay.asRequested().asError("mocked error description")
            }
        }

        private fun setupOnePixPay(status: OnePixPayStatus): OnePixPay {
            val onePixPay = createOnePixPay(status)

            every {
                onePixPayService.find(onePixPay.id)
            } returns onePixPay

            return onePixPay
        }

        private fun OnePixPay.setupAccountNumber(): AccountNumber {
            val fullAccountNumber = AccountNumber(1.toBigInteger(), "2")

            every {
                walletRepository.findAccountPaymentMethod(walletId)
            } returns mockk {
                every {
                    method
                } returns mockk<InternalBankAccount> {
                    every {
                        accountNumber
                    } returns fullAccountNumber
                }
            }

            return fullAccountNumber
        }

        @ParameterizedTest
        @EnumSource(value = OnePixPayStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["REQUESTED"])
        fun `não deve publicar mensagem quando o estado do one pix pay é diferente de REQUESTED`(status: OnePixPayStatus) {
            val onePixPay = setupOnePixPay(status)

            onePixPayService.save(onePixPay)

            verify {
                messagePublisher wasNot called
            }
        }

        @Test
        fun `deve publicar mensagem quando o estado do one pix pay é REQUESTED`() {
            val onePixPay = setupOnePixPay(OnePixPayStatus.REQUESTED)
            val accountNumber = onePixPay.setupAccountNumber()

            onePixPayService.save(onePixPay)

            val slot = slot<OnePixPayRequestedMessage>()
            verify {
                messagePublisher.sendMessage("onePixPayRequestedQueueName", capture(slot), 60)
            }
            with(slot.captured) {
                this.onePixPayId shouldBe onePixPay.id.value
                this.fullAccountNumber shouldBe accountNumber.fullAccountNumber
            }
        }
    }

    @Test
    fun `deve saber converter de txID para OnePixPayId`() {
        val qrCodeTransactionId = QrCodeTransactionId.from("E8JUraiUQVCtkqceeptpMY")
        qrCodeTransactionId.shouldBeTypeOf<UUIDTransactionId>()
        qrCodeTransactionId.toOnePixPayId().value shouldBe "OPP-6a4bc814-5794-49d4-bbc7-6283b454130b"
    }

    companion object {
        private val walletFixture = WalletFixture()
        private val wallet = walletFixture.buildWallet()
        private val anotherWallet = walletFixture.buildWallet(id = WalletId(WALLET_ID_2), name = "carteira2")

        private val concessionaria = Bill.build(billAdded.copy(walletId = wallet.id))
        private val fichaCompensasao = Bill.build(fichaCompensacaoCreditCardAdded.copy(walletId = wallet.id))
        private val pix = Bill.build(pixKeyAdded.copy(walletId = wallet.id))
        private val pixPaidId = BillId()
        private val pixPaid = Bill.build(
            pixKeyAdded.copy(
                billId = pixPaidId,
                walletId = wallet.id,
            ),
            BillPaid(
                billId = pixPaidId,
                created = pixKeyPaymentScheduled.created + 1,
                walletId = pixKeyPaymentScheduled.walletId,
                acquirer = null,
                acquirerTid = null,
                transactionId = null,
                actionSource = ActionSource.System,
                pixKeyDetails = null,
            ),
        )
        private val subscriptionFee = Bill.build(
            pixKeyAdded.copy(billId = BillId(BILL_ID_2), walletId = wallet.id, subscriptionFee = true),
            pixKeyPaymentScheduled.copy(billId = BillId(BILL_ID_2), walletId = wallet.id),
        )

        private val uuidOnePixPay = OnePixPay(
            walletId = wallet.id,
            billIds = listOf(concessionaria.billId, fichaCompensasao.billId, pix.billId, subscriptionFee.billId),
            qrCode = "qrcode",
            status = OnePixPayStatus.REQUESTED,
            paymentLimitTime = LocalTime.MAX,
            fundsReceived = false,
            fundsReceivedAt = null,
            created = getZonedDateTime(),
        )

        private val endToEndOnePixPay = uuidOnePixPay.copy(
            id = EndToEnd().toOnePixPayId(),
        )

        @JvmStatic
        fun validOPPs() = listOf(
            uuidOnePixPay,
            endToEndOnePixPay,
        )
    }
}