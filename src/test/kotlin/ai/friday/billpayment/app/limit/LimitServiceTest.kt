package ai.friday.billpayment.app.limit

import DynamoDBUtils
import ai.friday.billpayment.adapters.dynamodb.LimitDbRepository
import ai.friday.billpayment.adapters.dynamodb.LimitDynamoDAO
import ai.friday.billpayment.adapters.lock.InternalLockProvider
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.integration.ACCOUNT_ID
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test

class LimitServiceTest {
    private val dynamoDbEnhancedClient = DynamoDBUtils.setupDynamoDB()
    private val limitDynamoDAO = LimitDynamoDAO(dynamoDbEnhancedClient)
    private val lockProvider = mockk<InternalLockProvider>()
    private val limitDbRepository = LimitDbRepository(limitDynamoDAO)

    private val limitService = LimitService(
        limitDbRepository = limitDbRepository,
        lockProvider = lockProvider,
    )

    @Test
    fun `quando o limite estiver lockado joga exception`() {
        every { lockProvider.acquireLock(any(), any()) } returns null

        limitService.increment(AccountId(ACCOUNT_ID), "key") shouldBe LimitIncrementResult.Locked
    }

    @Test
    fun `quando estiver dentro do limite deve devolver ok e incrementar`() {
        limitService.createLimit(AccountId(ACCOUNT_ID), "key", 3)
        every { lockProvider.acquireLock(any(), any()) } returns mockk(relaxed = true)

        limitService.increment(AccountId(ACCOUNT_ID), "key") shouldBe LimitIncrementResult.Ok

        limitDbRepository.get(AccountId(ACCOUNT_ID), "key")!!.count shouldBe 1
    }

    @Test
    fun `quando fora do limite deve devolver exceeded`() {
        limitService.createLimit(AccountId(ACCOUNT_ID), "key", 1)
        every { lockProvider.acquireLock(any(), any()) } returns mockk(relaxed = true)

        limitService.increment(AccountId(ACCOUNT_ID), "key") shouldBe LimitIncrementResult.Ok
        limitService.increment(AccountId(ACCOUNT_ID), "key") shouldBe LimitIncrementResult.Exceeded
    }

    @Test
    fun `quando não existir limite deve devolver nulo`() {
        every { lockProvider.acquireLock(any(), any()) } returns mockk(relaxed = true)

        limitService.increment(AccountId(ACCOUNT_ID), "key") shouldBe LimitIncrementResult.NotFound
    }
}