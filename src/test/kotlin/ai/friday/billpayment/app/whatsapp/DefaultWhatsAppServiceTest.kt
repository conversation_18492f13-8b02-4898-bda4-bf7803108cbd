package ai.friday.billpayment.app.whatsapp

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.security.DefaultEncryptionService
import ai.friday.billpayment.app.security.EncryptionService
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.result.shouldBeSuccess
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import io.mockk.verifyOrder
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode

private const val MASTER_KEY = "testkey"

@Execution(ExecutionMode.CONCURRENT)
internal class DefaultWhatsAppServiceTest {

    @Test
    fun `should send flow successfully for account ids`() {
        val c = WhatsAppTestContext.create()
        val accountId = AccountId()
        val account = ACCOUNT.copy(accountId = accountId)

        val accountId2 = AccountId()
        val account2 = ACCOUNT.copy(accountId = accountId2)

        val accountIds: List<AccountId> = listOf(accountId, accountId2)
        val command = SendFlowCommand.ByAccountId("template", accountIds)

        every { c.accountRepository.findByIdOrNull(accountId) } returns account
        every { c.accountRepository.findByIdOrNull(accountId2) } returns account2

        val capturedFlowRequests = mutableListOf<FlowRequest>()

        every {
            c.whatsAppClient.sendFlow(capture(capturedFlowRequests))
        } returns Result.success("messageId")

        c.whatsAppService.sendFlow(command).shouldBeSuccess().let {
            it.successes.size shouldBe 2
            it.errors.size shouldBe 0
        }

        verifyOrder {
            c.accountRepository.findByIdOrNull(accountId)
            c.encryptionService.encrypt(accountId.value, "friday-salt")
            c.whatsAppClient.sendFlow(any())
        }

        capturedFlowRequests
            .map { AccountId(c.encryptionService.decrypt(it.flowToken, "friday-salt")) }
            .shouldContainExactly(accountIds)
    }

    @Test
    fun `should result fail when account does not exist`() {
        val c = WhatsAppTestContext.create()
        val accountId = AccountId()
        val command = SendFlowCommand.ByAccountId("template", listOf(accountId))

        every { c.accountRepository.findByIdOrNull(accountId) } returns null

        c.whatsAppService.sendFlow(command).shouldBeSuccess().let {
            it.successes.size shouldBe 0
            it.errors.size shouldBe 1
            it.errors[0] shouldBe accountId.value
        }

        verify { c.accountRepository.findByIdOrNull(accountId) }
    }

    @Test
    fun `should result fail for encryption failure`() {
        val c = WhatsAppTestContext.create()
        val accountId = AccountId()
        val account = ACCOUNT.copy(accountId = accountId)
        val command = SendFlowCommand.ByAccountId("template", listOf(accountId))

        every { c.accountRepository.findByIdOrNull(accountId) } returns account
        every { c.encryptionService.encrypt(accountId.value, c.flowConfiguration.salt) } throws RuntimeException("Encryption failed")

        c.whatsAppService.sendFlow(command).shouldBeSuccess().let {
            it.successes.size shouldBe 0
            it.errors.size shouldBe 1
            it.errors[0] shouldBe accountId.value
        }

        verifyOrder {
            c.accountRepository.findByIdOrNull(accountId)
            c.encryptionService.encrypt(accountId.value, "friday-salt")
        }
    }

    @Test
    fun `should result fail when Meta API fails`() {
        val c = WhatsAppTestContext.create()
        val accountId = AccountId()
        val account = ACCOUNT.copy(accountId = accountId)
        val command = SendFlowCommand.ByAccountId("template", listOf(accountId))

        every { c.accountRepository.findByIdOrNull(accountId) } returns account
        every { c.encryptionService.encrypt(accountId.value, c.flowConfiguration.salt) } returns "encryptedToken"
        every { c.whatsAppClient.sendFlow(any()) } returns Result.failure(RuntimeException("WhatsApp client failed"))

        c.whatsAppService.sendFlow(command).shouldBeSuccess().let {
            it.successes.size shouldBe 0
            it.errors.size shouldBe 1
            it.errors[0] shouldBe accountId.value
        }

        verifyOrder {
            c.accountRepository.findByIdOrNull(accountId)
            c.encryptionService.encrypt(accountId.value, "friday-salt")
            c.whatsAppClient.sendFlow(any())
        }
    }
}

internal object WhatsAppTestContext {
    fun create(
        whatsAppClient: WhatsAppClient = mockk(),
        accountRepository: AccountRepository = mockk(),
        encryptionService: EncryptionService = spyk(DefaultEncryptionService(MASTER_KEY)),
        flowConfiguration: WhatsAppFlowConfiguration = mockk(relaxed = true) {
            every { salt } returns "friday-salt"
        },
        whatsAppService: DefaultWhatsAppService = DefaultWhatsAppService(
            whatsAppClient = whatsAppClient,
            accountRepository = accountRepository,
            encryptionService = encryptionService,
            flowConfiguration = flowConfiguration,
        ),
    ): Data = Data(
        whatsAppClient = whatsAppClient,
        accountRepository = accountRepository,
        encryptionService = encryptionService,
        flowConfiguration = flowConfiguration,
        whatsAppService = whatsAppService,
    )

    data class Data(
        val whatsAppClient: WhatsAppClient,
        val accountRepository: AccountRepository,
        val encryptionService: EncryptionService,
        val flowConfiguration: WhatsAppFlowConfiguration,
        val whatsAppService: DefaultWhatsAppService,
    )
}