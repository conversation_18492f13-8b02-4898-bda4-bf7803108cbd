package ai.friday.billpayment.app.reports

import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.banking.BankStatement
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.OmnibusBankStatement
import ai.friday.billpayment.app.banking.OmnibusBankStatementItem
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.InternalBankRepository
import ai.friday.billpayment.bankStatementItem
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID_4
import ai.friday.billpayment.omnibusPaymentMethodId
import ai.friday.billpayment.virtualBalance
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test

internal class OmnibusAccountReportServiceTest {

    private val internalBankRepository: InternalBankRepository = mockk()

    private val accountRepository: AccountRepository = mockk()

    private val reportService = OmnibusAccountReportService(
        internalBankRepository = internalBankRepository,
        accountRepository = accountRepository,
        emailSender = mockk(relaxUnitFun = true),
    ).apply {
        from = "Test"
        recipients = "None"
        omnibusAccountPaymentMethodId = omnibusPaymentMethodId
    }

    private val omnibusBankStatementItem = OmnibusBankStatementItem(
        bankStatementItem = bankStatementItem,
        omnibusPaymentMethodId = AccountPaymentMethodId(value = omnibusPaymentMethodId),
        virtualPaymentMethodId = virtualBalance.id,
    )

    @Test
    fun `should return empty balance when there is no virtual account`() {
        every {
            accountRepository.findAllVirtualBankAccount()
        } returns listOf()

        every {
            internalBankRepository.findAllBankStatementItem(any())
        } returns BankStatement(listOf())

        every {
            internalBankRepository.findAllOmnibusBankStatementItem(any())
        } returns OmnibusBankStatement(listOf())

        val report = reportService.report()

        report.virtualAccountsBalanceSum shouldBe 0L
        report.omnibusAccountsBalance shouldBe 0L
        report.unresolvedDeposits.shouldBeEmpty()
    }

    @Test
    fun `should return same balance when there is a virtual account with deposit`() {
        every {
            accountRepository.findAllVirtualBankAccount()
        } returns listOf(virtualBalance)

        every {
            internalBankRepository.findAllBankStatementItem(any())
        } returns BankStatement(listOf(bankStatementItem))

        every {
            internalBankRepository.findAllOmnibusBankStatementItem(any())
        } returns OmnibusBankStatement(listOf(omnibusBankStatementItem))

        val report = reportService.report()

        report.virtualAccountsBalanceSum shouldBe bankStatementItem.amount
        report.omnibusAccountsBalance shouldBe omnibusBankStatementItem.bankStatementItem.amount
        report.unresolvedDeposits.shouldBeEmpty()
    }

    @Test
    fun `should return same balance when there is a virtual account with deposit and bill paid`() {
        every {
            accountRepository.findAllVirtualBankAccount()
        } returns listOf(virtualBalance)

        val debitItem = bankStatementItem.copy(
            flow = BankStatementItemFlow.DEBIT,
            amount = 100L,
        )

        val omnibusDebitItem =
            OmnibusBankStatementItem(
                bankStatementItem = debitItem,
                omnibusPaymentMethodId = AccountPaymentMethodId(value = omnibusPaymentMethodId),
                virtualPaymentMethodId = virtualBalance.id,
            )

        every {
            internalBankRepository.findAllBankStatementItem(virtualBalance.id)
        } returns BankStatement(listOf(bankStatementItem, debitItem))

        every {
            internalBankRepository.findAllOmnibusBankStatementItem(AccountPaymentMethodId(omnibusPaymentMethodId))
        } returns OmnibusBankStatement(listOf(omnibusBankStatementItem, omnibusDebitItem))

        val report = reportService.report()

        report.virtualAccountsBalanceSum shouldBe bankStatementItem.amount - debitItem.amount
        report.omnibusAccountsBalance shouldBe omnibusBankStatementItem.bankStatementItem.amount - omnibusDebitItem.bankStatementItem.amount
        report.unresolvedDeposits.shouldBeEmpty()
    }

    @Test
    fun `should return same balance when there is a virtual account with deposit and bill was refunded`() {
        every {
            accountRepository.findAllVirtualBankAccount()
        } returns listOf(virtualBalance)

        val debitItem = bankStatementItem.copy(
            flow = BankStatementItemFlow.DEBIT,
            amount = 100L,
        )

        val omnibusDebitItem =
            OmnibusBankStatementItem(
                bankStatementItem = debitItem,
                omnibusPaymentMethodId = AccountPaymentMethodId(value = omnibusPaymentMethodId),
                virtualPaymentMethodId = virtualBalance.id,
            )

        val refundItem = debitItem.copy(
            flow = BankStatementItemFlow.CREDIT,
        )

        val omnibusRefundItem =
            OmnibusBankStatementItem(
                bankStatementItem = refundItem,
                omnibusPaymentMethodId = AccountPaymentMethodId(value = omnibusPaymentMethodId),
                virtualPaymentMethodId = virtualBalance.id,
            )

        every {
            internalBankRepository.findAllBankStatementItem(virtualBalance.id)
        } returns BankStatement(listOf(bankStatementItem, debitItem, refundItem))

        every {
            internalBankRepository.findAllOmnibusBankStatementItem(AccountPaymentMethodId(omnibusPaymentMethodId))
        } returns OmnibusBankStatement(listOf(omnibusBankStatementItem, omnibusDebitItem, omnibusRefundItem))

        val report = reportService.report()

        report.virtualAccountsBalanceSum shouldBe bankStatementItem.amount
        report.omnibusAccountsBalance shouldBe omnibusBankStatementItem.bankStatementItem.amount
        report.unresolvedDeposits.shouldBeEmpty()
    }

    @Test
    fun `should return same total balance when there are many virtual accounts with deposit`() {
        val virtualBalance2 = virtualBalance.copy(
            id = AccountPaymentMethodId(PAYMENT_METHOD_ID_4),
        )

        every {
            accountRepository.findAllVirtualBankAccount()
        } returns listOf(virtualBalance, virtualBalance2)

        every {
            internalBankRepository.findAllBankStatementItem(virtualBalance.id)
        } returns BankStatement(listOf(bankStatementItem))

        every {
            internalBankRepository.findAllBankStatementItem(virtualBalance2.id)
        } returns BankStatement(listOf(bankStatementItem))

        every {
            internalBankRepository.findAllOmnibusBankStatementItem(AccountPaymentMethodId(omnibusPaymentMethodId))
        } returns OmnibusBankStatement(listOf(omnibusBankStatementItem, omnibusBankStatementItem))

        val report = reportService.report()

        report.virtualAccountsBalanceSum shouldBe bankStatementItem.amount * 2
        report.omnibusAccountsBalance shouldBe omnibusBankStatementItem.bankStatementItem.amount * 2
        report.unresolvedDeposits.shouldBeEmpty()
    }

    @Test
    fun `should return different total balance when there are unmapped bank statement items`() {
        val virtualBalance2 = virtualBalance.copy(
            id = AccountPaymentMethodId(PAYMENT_METHOD_ID_4),
        )

        val unmappedOmnibusBankStatementItem = OmnibusBankStatementItem(
            bankStatementItem = bankStatementItem.copy(
                documentNumber = "***********",
            ),
            omnibusPaymentMethodId = AccountPaymentMethodId(value = omnibusPaymentMethodId),
            virtualPaymentMethodId = null,
        )

        val unmappedDebitOmnibusBankStatementItem = OmnibusBankStatementItem(
            bankStatementItem = bankStatementItem.copy(
                documentNumber = "***********",
                flow = BankStatementItemFlow.DEBIT,
            ),
            omnibusPaymentMethodId = AccountPaymentMethodId(value = omnibusPaymentMethodId),
            virtualPaymentMethodId = null,
        )

        every {
            accountRepository.findAllVirtualBankAccount()
        } returns listOf(virtualBalance, virtualBalance2)

        every {
            internalBankRepository.findAllBankStatementItem(virtualBalance.id)
        } returns BankStatement(listOf(bankStatementItem))

        every {
            internalBankRepository.findAllBankStatementItem(virtualBalance2.id)
        } returns BankStatement(listOf(bankStatementItem))

        every {
            internalBankRepository.findAllOmnibusBankStatementItem(AccountPaymentMethodId(omnibusPaymentMethodId))
        } returns OmnibusBankStatement(
            listOf(
                omnibusBankStatementItem,
                unmappedOmnibusBankStatementItem,
                unmappedDebitOmnibusBankStatementItem,
                omnibusBankStatementItem,
            ),
        )

        val report = reportService.report()

        report.virtualAccountsBalanceSum shouldBe bankStatementItem.amount * 2
        report.omnibusAccountsBalance shouldBe (omnibusBankStatementItem.bankStatementItem.amount * 3) - unmappedDebitOmnibusBankStatementItem.bankStatementItem.amount
        report.unresolvedDeposits shouldContainExactly listOf(unmappedOmnibusBankStatementItem.bankStatementItem)
    }

    @Test
    fun `should return no unmapped bank statement item when it was refunded`() {
        val unmappedOmnibusBankStatementItem = OmnibusBankStatementItem(
            bankStatementItem = bankStatementItem.copy(
                documentNumber = "***********",
                ref = "refunded",
            ),
            omnibusPaymentMethodId = AccountPaymentMethodId(value = omnibusPaymentMethodId),
            virtualPaymentMethodId = null,
        )

        every {
            accountRepository.findAllVirtualBankAccount()
        } returns listOf(virtualBalance)

        every {
            internalBankRepository.findAllBankStatementItem(virtualBalance.id)
        } returns BankStatement(listOf(bankStatementItem))

        every {
            internalBankRepository.findAllOmnibusBankStatementItem(AccountPaymentMethodId(omnibusPaymentMethodId))
        } returns OmnibusBankStatement(listOf(omnibusBankStatementItem, unmappedOmnibusBankStatementItem))

        val report = reportService.report()

        report.virtualAccountsBalanceSum shouldBe bankStatementItem.amount
        report.omnibusAccountsBalance shouldBe omnibusBankStatementItem.bankStatementItem.amount + unmappedOmnibusBankStatementItem.bankStatementItem.amount
        report.unresolvedDeposits.shouldBeEmpty()
    }
}