package ai.friday.billpayment.app.reports

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.getBillWithDueDate
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import io.via1.communicationcentre.app.integrations.EmailSenderService
import java.time.format.DateTimeFormatter
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class FinancialReportServiceTest {

    private var repository: BillRepository = mockk()
    private var accountRepository: AccountRepository = mockk {
        every { findAllAccountsActivatedSince(any()) } returns listOf(ACCOUNT)
    }
    private var walletRepository: WalletRepository = mockk {
        every { findWallets(any(), any()) } returns listOf(
            WalletFixture().buildWallet(
                id = WalletId(ACCOUNT_ID_2),
                walletFounder = WalletFixture().founder.copy(accountId = ACCOUNT.accountId),
            ),
        )
    }

    private var sender: EmailSenderService = mockk()

    private val internalBillService =
        FinancialReportService(accountRepository, walletRepository, repository, sender, mockk()).apply {
            from = ""
            recipients = ""
        }

    private val singleBill =
        listOf(getBillWithDueDate(daysToSubtract = 1, effectiveDaysToSubtract = 0))
    private val pixBill = listOf(
        getBillWithDueDate(
            daysToSubtract = 1,
            effectiveDaysToSubtract = 0,
        ).copy(billType = BillType.PIX),
    )
    private val bills = listOf(
        getBillWithDueDate(daysToSubtract = 1, effectiveDaysToSubtract = 0),
        getBillWithDueDate(walletId = WalletId(ACCOUNT_ID_2), daysToSubtract = 1, effectiveDaysToSubtract = 0),
    )
    private val nextDayBills = listOf(
        getBillWithDueDate(
            daysToAdd = 1,
            daysToSubtract = 0,
            effectiveDaysToAdd = 2,
            effectiveDaysToSubtract = 0,
        ),
        getBillWithDueDate(
            walletId = WalletId(ACCOUNT_ID_2),
            daysToAdd = 3,
            daysToSubtract = 0,
            effectiveDaysToAdd = 4,
            effectiveDaysToSubtract = 0,
        ),
    )

    @Test
    fun `should report single bill coming due`() {
        every { repository.findBillsComingDueWithin(WalletId(ACCOUNT_ID_2), 7) } answers { singleBill }
        every { sender.sendRawEmail(any(), any(), any(), any()) } just Runs

        val report: Map<String, Long> = internalBillService.reportBillsComingDueWithin(7)

        assertEquals(15591, report[getLocalDate().format(DateTimeFormatter.ISO_DATE)])

        verify {
            sender.sendRawEmail(any(), any(), any(), any())
        }
    }

    @Test
    fun `should not report pix bill coming due`() {
        every { repository.findBillsComingDueWithin(WalletId(ACCOUNT_ID_2), 7) } answers { singleBill + pixBill }
        every { sender.sendRawEmail(any(), any(), any(), any()) } just Runs

        val report: Map<String, Long> = internalBillService.reportBillsComingDueWithin(7)

        assertEquals(15591, report[getLocalDate().format(DateTimeFormatter.ISO_DATE)])

        verify {
            sender.sendRawEmail(any(), any(), any(), any())
        }
    }

    @Test
    fun `should report multiple bills coming due`() {
        every { repository.findBillsComingDueWithin(WalletId(ACCOUNT_ID_2), 7) } answers { bills }
        every { sender.sendRawEmail(any(), any(), any(), any()) } just Runs

        val report: Map<String, Long> = internalBillService.reportBillsComingDueWithin(7)

        assertEquals(31182, report[getLocalDate().format(DateTimeFormatter.ISO_DATE)])

        verify {
            sender.sendRawEmail(any(), any(), any(), any())
        }
    }

    @Test
    fun `should report multiple coming due dates`() {
        every { repository.findBillsComingDueWithin(WalletId(ACCOUNT_ID_2), 7) } answers {
            listOf(
                bills,
                nextDayBills,
            ).flatten()
        }
        every { sender.sendRawEmail(any(), any(), any(), any()) } just Runs

        val report: Map<String, Long> = internalBillService.reportBillsComingDueWithin(7)

        assertEquals(31182, report[getLocalDate().format(DateTimeFormatter.ISO_DATE)])
        assertEquals(15591, report[getLocalDate().plusDays(2).format(DateTimeFormatter.ISO_DATE)])
        assertEquals(15591, report[getLocalDate().plusDays(4).format(DateTimeFormatter.ISO_DATE)])

        verify {
            sender.sendRawEmail(any(), any(), any(), any())
        }
    }
}