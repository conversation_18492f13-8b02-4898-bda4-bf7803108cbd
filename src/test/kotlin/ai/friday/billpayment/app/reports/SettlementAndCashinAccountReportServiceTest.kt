package ai.friday.billpayment.app.reports

import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankStatement
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.DefaultBankStatementItem
import ai.friday.billpayment.app.integrations.BankAccountService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import io.via1.communicationcentre.app.integrations.EmailSenderService
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

internal class SettlementAndCashinAccountReportServiceTest {

    private val bankAccountService: BankAccountService = mockk()

    private val emailSender: EmailSenderService = mockk()

    private val reportService = SettlementAndCashinAccountReportService(bankAccountService, emailSender).apply {
        from = ""
        recipients = ""
        settlementAccountNo = SettlementAndCashinAccountReportServiceTest.settlementAccountNo.fullAccountNumber
        settlementAccountDocument = "**************"
        cashinAccountNo = SettlementAndCashinAccountReportServiceTest.cashinAccountNo.fullAccountNumber
    }

    private val transferDebitItem = DefaultBankStatementItem(
        date = getLocalDate(),
        amount = 15000,
        flow = BankStatementItemFlow.DEBIT,
        type = BankStatementItemType.TRANSFERENCIA_CC,
        description = "",
        counterpartDocument = "",
        counterpartName = "",
        documentNumber = "",
        operationNumber = "",
        isTemporaryOperationNumber = false,
    )
    private val transferCreditItem = transferDebitItem.copy(flow = BankStatementItemFlow.CREDIT, amount = 5000)
    private val tedDebitItem =
        transferDebitItem.copy(type = BankStatementItemType.TED_MESMA_TITULARIDADE, amount = 10000)
    private val otherItem = transferCreditItem.copy(amount = 5001, type = BankStatementItemType.DEVOLUCAO_TED)

    @BeforeEach
    fun setup() {
        every { emailSender.sendRawEmail(reportService.from, any(), any(), reportService.recipients) } just Runs

        every {
            bankAccountService.getStatement(
                AccountNumber(reportService.cashinAccountNo),
                any(),
                any(),
                any(),
            )
        } returns BankStatement(listOf())
        every {
            bankAccountService.getStatement(
                AccountNumber(reportService.settlementAccountNo),
                any(),
                any(),
                any(),
            )
        } returns BankStatement(listOf())
    }

    @Test
    fun `report no statement items on date`() {
        val report =
            reportService.reportSettlementAndCashinAccountTotals(getZonedDateTime())

        verify {
            emailSender.sendRawEmail(
                reportService.from,
                match { it.startsWith(reportService.subject) },
                any(),
                reportService.recipients,
            )
        }
        assertSoftly {
            report.settlement.size shouldBe 3
            report.settlement[reportService.creditTransferKey] shouldBe 0
            report.settlement[reportService.debitTransferKey] shouldBe 0
            report.settlement[reportService.debitTED] shouldBe 0
            report.cashin.size shouldBe 3
            report.cashin[reportService.creditTransferKey] shouldBe 0
            report.cashin[reportService.debitTransferKey] shouldBe 0
            report.cashin[reportService.debitTED] shouldBe 0
        }
    }

    @ParameterizedTest
    @MethodSource("sourceAccounts")
    fun `report transfer credit item`(accountNo: AccountNumber) {
        val statement = BankStatement(listOf(transferCreditItem))
        every {
            bankAccountService.getStatement(
                accountNo,
                any(),
                any(),
                any(),
            )
        } returns statement
        val report = reportService.reportSettlementAndCashinAccountTotals(getZonedDateTime())
        verify {
            emailSender.sendRawEmail(
                reportService.from,
                match { it.startsWith(reportService.subject) },
                any(),
                reportService.recipients,
            )
        }
        val (testedAccount, otherAccount) = splitReportAccounts(report, accountNo)
        assertSoftly {
            testedAccount.size shouldBe 3
            testedAccount[reportService.creditTransferKey] shouldBe transferCreditItem.amount
            testedAccount[reportService.debitTransferKey] shouldBe 0
            testedAccount[reportService.debitTED] shouldBe 0
            otherAccount.size shouldBe 3
            otherAccount[reportService.creditTransferKey] shouldBe 0
            otherAccount[reportService.debitTransferKey] shouldBe 0
            otherAccount[reportService.debitTED] shouldBe 0
        }
    }

    @ParameterizedTest
    @MethodSource("sourceAccounts")
    fun `report transfer debit item`(accountNo: AccountNumber) {
        val statement = BankStatement(listOf(transferDebitItem))
        every {
            bankAccountService.getStatement(
                accountNo,
                any(),
                any(),
                any(),
            )
        } returns statement
        val report = reportService.reportSettlementAndCashinAccountTotals(getZonedDateTime())
        verify {
            emailSender.sendRawEmail(
                reportService.from,
                match { it.startsWith(reportService.subject) },
                any(),
                reportService.recipients,
            )
        }
        val (testedAccount, otherAccount) = splitReportAccounts(report, accountNo)
        assertSoftly {
            testedAccount.size shouldBe 3
            testedAccount[reportService.creditTransferKey] shouldBe 0
            testedAccount[reportService.debitTransferKey] shouldBe transferDebitItem.amount
            testedAccount[reportService.debitTED] shouldBe 0
            otherAccount.size shouldBe 3
            otherAccount[reportService.creditTransferKey] shouldBe 0
            otherAccount[reportService.debitTransferKey] shouldBe 0
            otherAccount[reportService.debitTED] shouldBe 0
        }
    }

    @ParameterizedTest
    @MethodSource("sourceAccounts")
    fun `report multiple credit and debit items`(accountNo: AccountNumber) {
        val statement = BankStatement(listOf(transferDebitItem, transferCreditItem, tedDebitItem, transferCreditItem))
        every {
            bankAccountService.getStatement(
                accountNo,
                any(),
                any(),
                any(),
            )
        } returns statement
        val report = reportService.reportSettlementAndCashinAccountTotals(getZonedDateTime())
        verify {
            emailSender.sendRawEmail(
                reportService.from,
                match { it.startsWith(reportService.subject) },
                any(),
                reportService.recipients,
            )
        }
        val (testedAccount, otherAccount) = splitReportAccounts(report, accountNo)
        assertSoftly {
            testedAccount.size shouldBe 3
            testedAccount[reportService.creditTransferKey] shouldBe transferCreditItem.amount * 2
            testedAccount[reportService.debitTransferKey] shouldBe transferDebitItem.amount
            testedAccount[reportService.debitTED] shouldBe tedDebitItem.amount
            otherAccount.size shouldBe 3
            otherAccount[reportService.creditTransferKey] shouldBe 0
            otherAccount[reportService.debitTransferKey] shouldBe 0
            otherAccount[reportService.debitTED] shouldBe 0
        }
    }

    @ParameterizedTest
    @MethodSource("sourceAccounts")
    fun `report not expected items`(accountNo: AccountNumber) {
        val statement = BankStatement(listOf(otherItem, otherItem))
        every {
            bankAccountService.getStatement(
                accountNo,
                any(),
                any(),
                any(),
            )
        } returns statement
        val report = reportService.reportSettlementAndCashinAccountTotals(getZonedDateTime())
        verify {
            emailSender.sendRawEmail(
                reportService.from,
                match { it.startsWith(reportService.subject) },
                any(),
                reportService.recipients,
            )
        }
        val (testedAccount, otherAccount) = splitReportAccounts(report, accountNo)
        assertSoftly {
            testedAccount.size shouldBe 4
            testedAccount[reportService.creditTransferKey] shouldBe 0L
            testedAccount[reportService.debitTransferKey] shouldBe 0L
            testedAccount[reportService.debitTED] shouldBe 0L
            testedAccount[otherItem.toMapKey()] shouldBe otherItem.amount * 2
            otherAccount.size shouldBe 3
            otherAccount[reportService.creditTransferKey] shouldBe 0
            otherAccount[reportService.debitTransferKey] shouldBe 0
            otherAccount[reportService.debitTED] shouldBe 0
        }
    }

    @Test
    fun `deve reportar as duas contas corretamente se ambas tiverem transações`() {
        val settlementStatment = BankStatement(listOf(transferCreditItem))
        val cashinStatment = BankStatement(listOf(tedDebitItem))
        every {
            bankAccountService.getStatement(
                settlementAccountNo,
                any(),
                any(),
                any(),
            )
        } returns settlementStatment

        every {
            bankAccountService.getStatement(
                cashinAccountNo,
                any(),
                any(),
                any(),
            )
        } returns cashinStatment
        val report = reportService.reportSettlementAndCashinAccountTotals(getZonedDateTime())
        verify {
            emailSender.sendRawEmail(
                reportService.from,
                match { it.startsWith(reportService.subject) },
                any(),
                reportService.recipients,
            )
        }

        assertSoftly {
            report.settlement.size shouldBe 3
            report.settlement[reportService.creditTransferKey] shouldBe transferCreditItem.amount
            report.settlement[reportService.debitTransferKey] shouldBe 0
            report.settlement[reportService.debitTED] shouldBe 0
            report.cashin.size shouldBe 3
            report.cashin[reportService.creditTransferKey] shouldBe 0
            report.cashin[reportService.debitTransferKey] shouldBe 0
            report.cashin[reportService.debitTED] shouldBe tedDebitItem.amount
        }
    }

    private fun splitReportAccounts(
        report: SettlementAndCashinAccountReport,
        accountNo: AccountNumber,
    ): Pair<Map<String, Long>, Map<String, Long>> {
        return if (accountNo == cashinAccountNo) {
            report.cashin to report.settlement
        } else {
            report.settlement to report.cashin
        }
    }

    companion object {
        private val settlementAccountNo = AccountNumber("1111")
        private val cashinAccountNo = AccountNumber("**************")

        @JvmStatic
        fun sourceAccounts(): List<Arguments> {
            return listOf(
                Arguments.of(settlementAccountNo),
                Arguments.of(cashinAccountNo),
            )
        }
    }
}