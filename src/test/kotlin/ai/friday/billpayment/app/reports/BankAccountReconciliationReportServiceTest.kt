package ai.friday.billpayment.app.reports

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.arbi.ArbiAdapter
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankStatement
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.DefaultBankStatementItem
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.RefundedBill
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.AccountStatementAdapter
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.OnboardingTestPixRepository
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.onboarding.OnboardingTestPixConfiguration
import ai.friday.billpayment.app.payment.MultiplePaymentData
import ai.friday.billpayment.app.payment.MultiplePaymentMethodsDetail
import ai.friday.billpayment.app.payment.Payer
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.SinglePaymentData
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.arbiBalance
import ai.friday.billpayment.getPaidBill
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID_2
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.toMember
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate
import java.time.ZoneId
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

internal class BankAccountReconciliationReportServiceTest {

    private val bankAccount = arbiBalance.method as InternalBankAccount
    private val walletFixture = WalletFixture()
    private val arbiWallet = walletFixture.buildWallet(
        id = WalletId(WALLET_ID),
        walletFounder = ACCOUNT.toMember(
            type = MemberType.FOUNDER,
            permissions = MemberPermissions(
                viewBills = BillPermission.ALL_BILLS,
                scheduleBills = BillPermission.ALL_BILLS,
                founderContactsEnabled = false,
                viewBalance = true,
                notification = true,
            ),
        ),
        accountPaymentMethodId = arbiBalance.id,
    )

    private val walletRepository: WalletRepository = mockk {
        every { findWallets(ACCOUNT.accountId, MemberStatus.ACTIVE) } returns listOf(arbiWallet)
    }
    private val accountRepository: AccountRepository = mockk {
        every { findAllPhysicalBalanceAccount() } returns listOf(arbiBalance)
        every { findAllAccountsActivatedSince(any()) } returns listOf(
            ACCOUNT.copy(
                configuration = ACCOUNT.configuration.copy(
                    defaultWalletId = WalletId(WALLET_ID),
                ),
            ),
        )
    }
    private val arbiAdapter: ArbiAdapter = mockk()
    private val billRepository: BillRepository = mockk {
        every { getRefundedBills(any(), any(), any()) } returns emptyList()
    }

    private val transactionRepository = mockk<TransactionRepository>()

    private val settlementAccountDocument = "**************"
    private val settlementAccountNo = AccountNumber("**********")
    private val cashInAccountNo = AccountNumber("**********")
    private val onboardingTestPixRepository = mockk<OnboardingTestPixRepository>()
    private val onboardingTestPixConfiguration = mockk<OnboardingTestPixConfiguration> {
        every {
            originAccountNo
        } returns cashInAccountNo.fullAccountNumber
    }
    private val accountStatementAdapter = mockk<AccountStatementAdapter>(relaxed = true)
    private val bankAccountReconciliationReportService = BankAccountReconciliationReportService(
        accountRepository = accountRepository,
        arbiAdapter = arbiAdapter,
        accountStatementAdapter = accountStatementAdapter,
        billRepository = billRepository,
        walletRepository = walletRepository,
        transactionRepository = transactionRepository,
        emailSender = mockk(relaxed = true),
        onboardingTestPixRepository = onboardingTestPixRepository,
        onboardingTestPixConfiguration = onboardingTestPixConfiguration,
        messagePublisher = mockk(relaxUnitFun = true),
        accountReconciliationReportQueueName = "accountReconciliationReportQueueName",
    ).apply {
        arbiSettlementAccountDocument = settlementAccountDocument
        arbiSettlementAccountNo = settlementAccountNo.fullAccountNumber
        arbiCashInAccountNo = cashInAccountNo.fullAccountNumber
        from = "<EMAIL>"
    }

    private val queryDate = LocalDate.of(2021, 12, 8)

    val pixDebit = DefaultBankStatementItem(
        date = queryDate,
        flow = BankStatementItemFlow.DEBIT,
        type = BankStatementItemType.PIX,
        description = "fake",
        operationNumber = "0001",
        isTemporaryOperationNumber = false,
        amount = 15591L,
        counterpartName = "fake counterpartName",
        counterpartDocument = "fake counterpartDocument",
        documentNumber = "***********",
        lastUpdate = queryDate.atStartOfDay(brazilTimeZone),
    )

    val tefDebit = DefaultBankStatementItem(
        date = getLocalDate(),
        flow = BankStatementItemFlow.DEBIT,
        type = BankStatementItemType.TRANSFERENCIA_CC,
        description = "fake",
        operationNumber = "0001",
        isTemporaryOperationNumber = false,
        amount = 100L,
        counterpartName = "fake counterpartName",
        counterpartDocument = "fake counterpartDocument",
        counterpartAccountNo = "**********",
        documentNumber = "***********",
        lastUpdate = queryDate.atStartOfDay(brazilTimeZone),
    )

    val tefCredit = DefaultBankStatementItem(
        date = getLocalDate(),
        flow = BankStatementItemFlow.CREDIT,
        type = BankStatementItemType.TRANSFERENCIA_CC,
        description = "fake",
        operationNumber = "0002",
        isTemporaryOperationNumber = false,
        amount = 100L,
        counterpartName = "fake counterpartName",
        counterpartAccountNo = "**********",
        counterpartDocument = "fake counterpartDocument",
        documentNumber = "***********",
        lastUpdate = queryDate.atStartOfDay(brazilTimeZone),
    )

    val tedDebit = DefaultBankStatementItem(
        date = getLocalDate(),
        flow = BankStatementItemFlow.DEBIT,
        type = BankStatementItemType.TED_DIF_TITULARIDADE,
        description = "fake",
        operationNumber = "0001",
        isTemporaryOperationNumber = false,
        amount = 300L,
        counterpartName = "fake counterpartName",
        counterpartDocument = "fake counterpartDocument",
        counterpartAccountNo = "**********",
        documentNumber = "***********",
        lastUpdate = queryDate.atStartOfDay(brazilTimeZone),
    )

    val tedRefunded = DefaultBankStatementItem(
        date = getLocalDate(),
        flow = BankStatementItemFlow.CREDIT,
        type = BankStatementItemType.DEVOLUCAO_TED,
        description = "fake",
        operationNumber = "0002",
        isTemporaryOperationNumber = false,
        amount = 300L,
        counterpartName = "fake counterpartName",
        counterpartAccountNo = "**********",
        counterpartDocument = "fake counterpartDocument",
        documentNumber = "***********",
        lastUpdate = queryDate.atStartOfDay(brazilTimeZone),
    )

    val tefRefunded = DefaultBankStatementItem(
        date = getLocalDate(),
        flow = BankStatementItemFlow.CREDIT,
        type = BankStatementItemType.TRANSFERENCIA_CC,
        description = "fake",
        operationNumber = "0002",
        isTemporaryOperationNumber = false,
        amount = 300L,
        counterpartName = "fake counterpartName",
        counterpartAccountNo = "**********",
        counterpartDocument = "fake counterpartDocument",
        documentNumber = "***********",
        lastUpdate = queryDate.atStartOfDay(brazilTimeZone),
    )

    val triPixCreated = DefaultBankStatementItem(
        date = getLocalDate(),
        flow = BankStatementItemFlow.CREDIT,
        type = BankStatementItemType.TRANSFERENCIA_CC,
        description = "fake",
        operationNumber = "0002",
        isTemporaryOperationNumber = false,
        amount = 3L,
        counterpartName = "fake counterpartName",
        counterpartAccountNo = settlementAccountNo.fullAccountNumber,
        counterpartDocument = settlementAccountDocument,
        documentNumber = settlementAccountDocument,
        lastUpdate = queryDate.atStartOfDay(brazilTimeZone),
    )

    val tefRefundedCreditCardReceived = DefaultBankStatementItem(
        date = getLocalDate(),
        flow = BankStatementItemFlow.CREDIT,
        type = BankStatementItemType.PIX,
        description = "fake",
        operationNumber = "0002",
        isTemporaryOperationNumber = false,
        amount = 300L,
        counterpartName = "fake counterpartName",
        counterpartAccountNo = settlementAccountNo.fullAccountNumber,
        counterpartDocument = settlementAccountDocument,
        documentNumber = settlementAccountDocument,
        lastUpdate = queryDate.atStartOfDay(brazilTimeZone),
    )

    val tefRefundedCreditCardSent = DefaultBankStatementItem(
        date = getLocalDate(),
        flow = BankStatementItemFlow.DEBIT,
        type = BankStatementItemType.PIX,
        description = "fake",
        operationNumber = "0002",
        isTemporaryOperationNumber = false,
        amount = 300L,
        counterpartName = "fake counterpartName",
        counterpartAccountNo = cashInAccountNo.fullAccountNumber,
        counterpartDocument = settlementAccountDocument,
        documentNumber = settlementAccountDocument,
        lastUpdate = queryDate.atStartOfDay(brazilTimeZone),
    )

    val pixCredit = DefaultBankStatementItem(
        date = getLocalDate(),
        flow = BankStatementItemFlow.CREDIT,
        type = BankStatementItemType.PIX,
        description = "fake",
        operationNumber = "0001",
        isTemporaryOperationNumber = false,
        amount = 100L,
        counterpartName = "fake counterpartName",
        counterpartDocument = "fake counterpartDocument",
        documentNumber = "***********",
    )

    val tefCashinCredit = DefaultBankStatementItem(
        date = getLocalDate(),
        flow = BankStatementItemFlow.CREDIT,
        type = BankStatementItemType.TRANSFERENCIA_CC,
        description = "fake",
        operationNumber = "0001",
        isTemporaryOperationNumber = false,
        amount = 100L,
        counterpartName = "fake counterpartName",
        counterpartDocument = "fake counterpartDocument",
        counterpartAccountNo = "**********",
        documentNumber = "***********",
    )

    val cutOffTime = 12
    val paidBill = getPaidBill().copy(paidDate = queryDate.atStartOfDay())

    val creditCardValue = 50L
    val paidBillWithCreditCardAndBalance =
        getPaidBill().copy(
            paidDate = queryDate.atStartOfDay(),
            amount = paidBill.amount + creditCardValue,
            paymentDetails = MultiplePaymentMethodsDetail(
                methods = listOf(
                    PaymentMethodsDetailWithBalance(
                        amount = paidBill.amount,
                        paymentMethodId = AccountPaymentMethodId(value = PAYMENT_METHOD_ID),
                        calculationId = null,
                    ),
                    PaymentMethodsDetailWithCreditCard(
                        paymentMethodId = AccountPaymentMethodId(value = PAYMENT_METHOD_ID_2),
                        netAmount = creditCardValue,
                        feeAmount = 5,
                        installments = 1,
                        calculationId = null,
                        fee = 4.0,
                    ),
                ),
            ),
        )

    val paidBillWithCreditCard =
        getPaidBill().copy(
            paidDate = queryDate.atStartOfDay(),
            paymentDetails =
            PaymentMethodsDetailWithCreditCard(
                paymentMethodId = AccountPaymentMethodId(value = PAYMENT_METHOD_ID_2),
                netAmount = paidBill.amount,
                feeAmount = 10,
                installments = 1,
                calculationId = null,
                fee = 4.0,
            ),
        )

    private val balanceAndCardTransaction = Transaction(
        walletId = arbiWallet.id,
        type = TransactionType.BOLETO_PAYMENT,
        payer = Payer(
            arbiWallet.founder.accountId,
            arbiWallet.founder.document,
            arbiWallet.founder.name,
        ),
        paymentData = MultiplePaymentData(
            payments = listOf(
                SinglePaymentData(
                    accountPaymentMethod = mockk(),
                    details = PaymentMethodsDetailWithBalance(
                        amount = tefRefunded.amount,
                        paymentMethodId = AccountPaymentMethodId(value = PAYMENT_METHOD_ID),
                        calculationId = null,
                    ),
                    payment = mockk(),
                ),
                SinglePaymentData(
                    accountPaymentMethod = mockk(),
                    details = PaymentMethodsDetailWithCreditCard(
                        paymentMethodId = AccountPaymentMethodId(value = PAYMENT_METHOD_ID),
                        netAmount = 10,
                        feeAmount = 1,
                        installments = 1,
                        fee = 4.0,
                        calculationId = null,
                    ),
                    payment = mockk(),
                ),
            ),
        ),
        settlementData = mockk() {
            every {
                settlementTarget.settlementTargetId()
            } returns "settlement-target-id"
        },
        nsu = 1,
        actionSource = ActionSource.System,
        status = TransactionStatus.UNDONE,
    )

    @Nested
    @DisplayName("given the arbi bank accounts reconciliation report")
    inner class ArbiBankAccountReconciliation {

        @Nested
        @DisplayName("should return empty list")
        inner class NoDivergencesFound {

            @ParameterizedTest
            @EnumSource(AccountPaymentMethodStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["ACTIVE"])
            fun `when there is no active bank accounts`(status: AccountPaymentMethodStatus) {
                every { walletRepository.findWallets(ACCOUNT.accountId, MemberStatus.ACTIVE) } returns listOf()
                val report = bankAccountReconciliationReportService.generateArbiAccountsReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                    accountPaymentMethod = arbiBalance.copy(status = status),
                    previouslyCheckedTimes = 0,
                )
                report.divergent shouldBe false
                report.debitsConciliation.divergent shouldBe false
                report.refundsConciliation.divergent shouldBe false
            }

            @Test
            fun `when there is no Arbi bank accounts`() {
                val xpBalance =
                    arbiBalance.copy(method = (arbiBalance.method as InternalBankAccount).copy(bankNo = 348))
                val report = bankAccountReconciliationReportService.generateArbiAccountsReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                    accountPaymentMethod = xpBalance,
                    previouslyCheckedTimes = 0,
                )
                report.divergent shouldBe false
                report.debitsConciliation.divergent shouldBe false
                report.refundsConciliation.divergent shouldBe false
            }

            @Test
            fun `when there is only credits and no bills paid`() {
                every {
                    accountStatementAdapter.getStatement(
                        accountNo = bankAccount.buildFullAccountNumber(),
                        document = Document(bankAccount.document),
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf(pixCredit, tefCashinCredit))
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf()
                val report = bankAccountReconciliationReportService.generateArbiAccountsReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                    accountPaymentMethod = arbiBalance,
                    previouslyCheckedTimes = 0,
                )
                report.divergent shouldBe false
                report.debitsConciliation.divergent shouldBe false
                report.refundsConciliation.divergent shouldBe false
            }

            @Test
            fun `when there are no debit and no bill paid`() {
                every {
                    accountStatementAdapter.getStatement(
                        accountNo = bankAccount.buildFullAccountNumber(),
                        document = Document(bankAccount.document),
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf())
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf()
                val report = bankAccountReconciliationReportService.generateArbiAccountsReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                    accountPaymentMethod = arbiBalance,
                    previouslyCheckedTimes = 0,
                )
                report.divergent shouldBe false
                report.debitsConciliation.divergent shouldBe false
                report.refundsConciliation.divergent shouldBe false
            }

            @Test
            fun `when total debits amount equals total amount of bills paid`() {
                every {
                    accountStatementAdapter.getStatement(
                        accountNo = bankAccount.buildFullAccountNumber(),
                        document = Document(bankAccount.document),
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf(pixDebit))
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf(paidBill)
                val report = bankAccountReconciliationReportService.generateArbiAccountsReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                    accountPaymentMethod = arbiBalance,
                    previouslyCheckedTimes = 0,
                )
                report.divergent shouldBe false
                report.debitsConciliation.divergent shouldBe false
                report.refundsConciliation.divergent shouldBe false
            }

            @Test
            fun `quando o total de debitos é igual ao total de contas pagas em saldo`() {
                every {
                    accountStatementAdapter.getStatement(
                        accountNo = bankAccount.buildFullAccountNumber(),
                        document = Document(bankAccount.document),
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf(pixDebit))
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf(paidBillWithCreditCardAndBalance)
                val report = bankAccountReconciliationReportService.generateArbiAccountsReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                    accountPaymentMethod = arbiBalance,
                    previouslyCheckedTimes = 0,
                )
                report.divergent shouldBe false
                report.debitsConciliation.divergent shouldBe false
                report.refundsConciliation.divergent shouldBe false
            }

            @Test
            fun `quando o total de debitos nao inclui contas pagas com cartao de credito`() {
                every {
                    accountStatementAdapter.getStatement(
                        accountNo = bankAccount.buildFullAccountNumber(),
                        document = Document(bankAccount.document),
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf())
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf(paidBillWithCreditCard)
                val report = bankAccountReconciliationReportService.generateArbiAccountsReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                    accountPaymentMethod = arbiBalance,
                    previouslyCheckedTimes = 0,
                )
                report.divergent shouldBe false
                report.debitsConciliation.divergent shouldBe false
                report.refundsConciliation.divergent shouldBe false
            }

            @Test
            fun `when there is no debit and there is a paid bill on another day`() {
                val oldPaidBill = paidBill.copy(paidDate = queryDate.minusDays(2).atStartOfDay())
                every {
                    accountStatementAdapter.getStatement(
                        accountNo = bankAccount.buildFullAccountNumber(),
                        document = Document(bankAccount.document),
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf())
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf(oldPaidBill)
                val report = bankAccountReconciliationReportService.generateArbiAccountsReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                    accountPaymentMethod = arbiBalance,
                    previouslyCheckedTimes = 0,
                )
                report.divergent shouldBe false
                report.debitsConciliation.divergent shouldBe false
                report.refundsConciliation.divergent shouldBe false
            }

            @Test
            fun `when paid bill is after cutoff time and there is no debits`() {
                val overtimePaidBill = getPaidBill().copy(paidDate = queryDate.atTime(cutOffTime, 0, 1))
                every {
                    accountStatementAdapter.getStatement(
                        accountNo = bankAccount.buildFullAccountNumber(),
                        document = Document(bankAccount.document),
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf())
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf(overtimePaidBill)
                val report = bankAccountReconciliationReportService.generateArbiAccountsReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                    accountPaymentMethod = arbiBalance,
                    previouslyCheckedTimes = 0,
                )
                report.divergent shouldBe false
                report.debitsConciliation.divergent shouldBe false
                report.refundsConciliation.divergent shouldBe false
            }

            @Test
            fun `when debit is after cutoff time and there is no paid bill`() {
                val overtimeDebit =
                    pixDebit.copy(lastUpdate = queryDate.atTime(cutOffTime, 0, 1).atZone(brazilTimeZone))
                every {
                    accountStatementAdapter.getStatement(
                        accountNo = bankAccount.buildFullAccountNumber(),
                        document = Document(bankAccount.document),
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf(overtimeDebit))
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf()
                val report = bankAccountReconciliationReportService.generateArbiAccountsReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                    accountPaymentMethod = arbiBalance,
                    previouslyCheckedTimes = 0,
                )
                report.divergent shouldBe false
                report.debitsConciliation.divergent shouldBe false
                report.refundsConciliation.divergent shouldBe false
            }

            @Test
            fun `when paid a ted but ted was refunded`() {
                every {
                    billRepository.getRefundedBills(any(), any(), any())
                } returns listOf(
                    RefundedBill(
                        amount = tedRefunded.amount,
                        billId = BillId(value = "eleifend"),
                        type = BillType.INVOICE,
                        transactionId = TransactionId(value = "sapien"),
                        refundDate = getZonedDateTime(),
                        originalPaidDate = queryDate.atTime(cutOffTime - 1, 0, 0).atZone(ZoneId.of("UTC")),
                    ),
                )
                every {
                    transactionRepository.findTransactions(any(), TransactionStatus.UNDONE)
                } returns listOf(
                    balanceAndCardTransaction,
                )

                every {
                    accountStatementAdapter.getStatement(
                        accountNo = bankAccount.buildFullAccountNumber(),
                        document = Document(bankAccount.document),
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf(tedDebit, tedRefunded))
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf()
                val report = bankAccountReconciliationReportService.generateArbiAccountsReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                    accountPaymentMethod = arbiBalance,
                    previouslyCheckedTimes = 0,
                )
                report.divergent shouldBe false
                report.debitsConciliation.divergent shouldBe false
                report.refundsConciliation.divergent shouldBe false
            }

            @Test
            fun `deve subtrair os valores do tri pix quando a conta for a mesma da conta liquidação`() {
                every {
                    onboardingTestPixConfiguration.originAccountNo
                } returns settlementAccountNo.fullAccountNumber

                every {
                    onboardingTestPixRepository.findByAccountId(any())
                } returns mockk() {
                    every {
                        billIds
                    } returns listOf(BillId("billId 1"), BillId("billId 2"), BillId("billId 3"))
                    every {
                        createdAt
                    } returns queryDate.atStartOfDay(brazilTimeZone)
                }

                every {
                    accountStatementAdapter.getStatement(
                        accountNo = bankAccount.buildFullAccountNumber(),
                        document = Document(bankAccount.document),
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf(triPixCreated))

                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf()

                every {
                    billRepository.getRefundedBills(
                        walletId = arbiWallet.id,
                        startDate = any(),
                        endDate = any(),
                    )
                } returns listOf()

                every {
                    transactionRepository.findTransactions(any(), TransactionStatus.UNDONE)
                } returns listOf()

                val report = bankAccountReconciliationReportService.generateArbiAccountsReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                    accountPaymentMethod = arbiBalance,
                    previouslyCheckedTimes = 0,
                )

                report.divergent shouldBe false
                report.debitsConciliation.divergent shouldBe false
                report.refundsConciliation.divergent shouldBe false

                verify {
                    onboardingTestPixRepository.findByAccountId(any())
                }
            }

            @Test
            fun `deve considerar registros de reembolso nos respectivos dias`() {
                val boletoPaymentDebit = tefDebit.copy(lastUpdate = tefDebit.lastUpdate!!.minusDays(1))

                every {
                    accountStatementAdapter.getStatement(
                        accountNo = bankAccount.buildFullAccountNumber(),
                        document = Document(bankAccount.document),
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf(tefRefunded))

                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf()

                every {
                    billRepository.getRefundedBills(
                        walletId = arbiWallet.id,
                        startDate = any(),
                        endDate = any(),
                    )
                } returns listOf(
                    RefundedBill(
                        amount = tefRefunded.amount,
                        billId = BillId(),
                        type = BillType.FICHA_COMPENSACAO,
                        transactionId = TransactionId.build(),
                        refundDate = tefRefunded.lastUpdate!!,
                        originalPaidDate = boletoPaymentDebit.lastUpdate!!,
                    ),
                )

                every {
                    transactionRepository.findTransactions(any(), TransactionStatus.UNDONE)
                } returns listOf(
                    balanceAndCardTransaction,
                )

                val report = bankAccountReconciliationReportService.generateArbiAccountsReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                    accountPaymentMethod = arbiBalance,
                    previouslyCheckedTimes = 0,
                )

                report.divergent shouldBe false
                report.debitsConciliation.divergent shouldBe false
                report.refundsConciliation.divergent shouldBe false
                report.refundsConciliation.currentAmount shouldBe tefRefunded.amount
                report.refundsConciliation.expectedAmount shouldBe tefRefunded.amount
            }
        }

        @Nested
        @DisplayName("should return divergent accounts")
        inner class DivergencesFound {

            @Test
            fun `when there is a debit and no bill paid`() {
                every {
                    accountStatementAdapter.getStatement(
                        accountNo = bankAccount.buildFullAccountNumber(),
                        document = Document(bankAccount.document),
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf(pixDebit))
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf()
                val report =
                    bankAccountReconciliationReportService.generateArbiAccountsReport(
                        date = queryDate,
                        cutOffTime = 12,
                        accountPaymentMethod = arbiBalance,
                        previouslyCheckedTimes = 0,
                    )
                report.divergent shouldBe true
                report.accountId shouldBe arbiBalance.accountId
                report.debitsConciliation.divergent shouldBe true
                report.debitsConciliation.currentAmount shouldBe pixDebit.amount
                report.debitsConciliation.expectedAmount shouldBe 0
                report.refundsConciliation.divergent shouldBe false
            }

            @Test
            fun `when there is a bill paid and no debit`() {
                every {
                    accountStatementAdapter.getStatement(
                        accountNo = bankAccount.buildFullAccountNumber(),
                        document = Document(bankAccount.document),
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf())
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf(paidBill)
                val report =
                    bankAccountReconciliationReportService.generateArbiAccountsReport(
                        date = queryDate,
                        cutOffTime = 12,
                        accountPaymentMethod = arbiBalance,
                        previouslyCheckedTimes = 0,
                    )
                report.divergent shouldBe true
                report.accountId shouldBe arbiBalance.accountId
                report.debitsConciliation.divergent shouldBe true
                report.debitsConciliation.currentAmount shouldBe 0
                report.debitsConciliation.expectedAmount shouldBe paidBill.amountTotal
                report.refundsConciliation.divergent shouldBe false
            }

            @Test
            fun `when there is a bill paid and no debit and also tried to pay a boleto but debit was returned`() {
                every {
                    accountStatementAdapter.getStatement(
                        accountNo = bankAccount.buildFullAccountNumber(),
                        document = Document(bankAccount.document),
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf(tefDebit, tefCredit))
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf(paidBill)
                val report = bankAccountReconciliationReportService.generateArbiAccountsReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                    accountPaymentMethod = arbiBalance,
                    previouslyCheckedTimes = 0,
                )
                report.divergent shouldBe true
                report.accountId shouldBe arbiBalance.accountId
                report.debitsConciliation.divergent shouldBe true
                report.debitsConciliation.currentAmount shouldBe tefDebit.amount
                report.debitsConciliation.expectedAmount shouldBe paidBill.amountTotal
                report.refundsConciliation.divergent shouldBe true
                report.refundsConciliation.currentAmount shouldBe tefCredit.amount
                report.refundsConciliation.expectedAmount shouldBe 0
            }

            @Test
            fun `quando uma conta for paga no cartão e houver debito na conta do usuário`() {
                every {
                    accountStatementAdapter.getStatement(
                        accountNo = bankAccount.buildFullAccountNumber(),
                        document = Document(bankAccount.document),
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf(tefDebit.copy(amount = paidBillWithCreditCard.amount)))
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf(paidBillWithCreditCard)
                val report = bankAccountReconciliationReportService.generateArbiAccountsReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                    accountPaymentMethod = arbiBalance,
                    previouslyCheckedTimes = 0,
                )
                report.divergent shouldBe true
                report.accountId shouldBe arbiBalance.accountId
                report.debitsConciliation.divergent shouldBe true
                report.debitsConciliation.currentAmount shouldBe paidBillWithCreditCard.amountTotal
                report.debitsConciliation.expectedAmount shouldBe 0
                report.refundsConciliation.divergent shouldBe false
            }
        }
    }

    @Nested
    @DisplayName("given the settlement account reconciliation report")
    inner class SettlementAccountReconciliation {

        @BeforeEach
        fun init() {
            every {
                arbiAdapter.getStatement(
                    accountNumber = cashInAccountNo,
                    document = settlementAccountDocument,
                    initialDate = any(),
                    endDate = any(),
                )
            } returns BankStatement(emptyList())
        }

        @Nested
        @DisplayName("should return no divergences")
        inner class NoDivergencesFound {
            @Test
            fun `when there are only debits and no boleto bills paid`() {
                every {
                    arbiAdapter.getStatement(
                        accountNumber = settlementAccountNo,
                        document = settlementAccountDocument,
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf(pixDebit))
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf(paidBill.copy(billType = BillType.PIX))

                val report = bankAccountReconciliationReportService.generateSettlementAccountReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                )

                report.first shouldBe SettlementAccountReport(
                    totalAmountArbi = 0L,
                    totalAmountFriday = 0L,
                    totalRefundedToCashinAccount = 0L,
                )
                report.first.hasDivergence().shouldBeFalse()
                report.second shouldBe SettlementAccountReport(
                    totalAmountArbi = 0L,
                    totalAmountFriday = 0L,
                    totalRefundedToCashinAccount = 0L,
                )
                report.second.hasDivergence().shouldBeFalse()
            }

            @Test
            fun `when there are no debit and no bill paid`() {
                every {
                    arbiAdapter.getStatement(
                        accountNumber = settlementAccountNo,
                        document = settlementAccountDocument,
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf())
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf()

                val report = bankAccountReconciliationReportService.generateSettlementAccountReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                )

                report.first shouldBe SettlementAccountReport(
                    totalAmountArbi = 0L,
                    totalAmountFriday = 0L,
                    totalRefundedToCashinAccount = 0L,
                )
                report.first.hasDivergence().shouldBeFalse()
                report.second shouldBe SettlementAccountReport(
                    totalAmountArbi = 0L,
                    totalAmountFriday = 0L,
                    totalRefundedToCashinAccount = 0L,
                )
                report.second.hasDivergence().shouldBeFalse()
            }

            @Test
            fun `when total credits amount equals total amount of boleto bills paid`() {
                val settlementCredit = tefCredit.copy(amount = paidBill.amountTotal)

                every {
                    arbiAdapter.getStatement(
                        accountNumber = settlementAccountNo,
                        document = settlementAccountDocument,
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf(settlementCredit))
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf(paidBill, paidBill.copy(billType = BillType.PIX))

                val report = bankAccountReconciliationReportService.generateSettlementAccountReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                )

                report.first shouldBe SettlementAccountReport(
                    totalAmountArbi = settlementCredit.amount,
                    totalAmountFriday = paidBill.amountTotal,
                    totalRefundedToCashinAccount = 0L,
                )
                report.first.hasDivergence().shouldBeFalse()
                report.second shouldBe SettlementAccountReport(
                    0L,
                    0L,
                    totalRefundedToCashinAccount = 0L,
                )
                report.second.hasDivergence().shouldBeFalse()
            }

            @Test
            fun `quando o valor que sai da conta cashin é igual ao valor dos boletos pagos com cartao`() {
                val settlementValueFromCashinToCelcoin =
                    tefDebit.copy(
                        amount = paidBillWithCreditCard.amountTotal,
                        type = BankStatementItemType.PIX,
                        counterpartDocument = settlementAccountDocument,
                    )

                every {
                    arbiAdapter.getStatement(
                        accountNumber = settlementAccountNo,
                        document = settlementAccountDocument,
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf())
                every {
                    arbiAdapter.getStatement(
                        accountNumber = cashInAccountNo,
                        document = settlementAccountDocument,
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf(settlementValueFromCashinToCelcoin))
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf(paidBillWithCreditCard, paidBillWithCreditCard.copy(billType = BillType.PIX))

                val report = bankAccountReconciliationReportService.generateSettlementAccountReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                )

                report.first shouldBe SettlementAccountReport(
                    totalAmountArbi = 0L,
                    totalAmountFriday = 0L,
                    totalRefundedToCashinAccount = 0L,
                )
                report.first.hasDivergence().shouldBeFalse()
                report.second shouldBe SettlementAccountReport(
                    totalAmountArbi = paidBillWithCreditCard.amountTotal,
                    totalAmountFriday = paidBillWithCreditCard.amountTotal,
                    totalRefundedToCashinAccount = 0L,
                )
                report.second.hasDivergence().shouldBeFalse()
            }

            @Test
            fun `quando o total de creditos é igual ao valor pago com saldo e o valor que sai da conta cashin é a parte paga com cartao`() {
                val settlementCredit = tefCredit.copy(amount = paidBill.amountTotal)
                val settlementValueFromCashinToCelcoin =
                    tefDebit.copy(
                        amount = creditCardValue,
                        type = BankStatementItemType.PIX,
                        counterpartDocument = settlementAccountDocument,
                    )

                every {
                    arbiAdapter.getStatement(
                        accountNumber = settlementAccountNo,
                        document = settlementAccountDocument,
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf(settlementCredit))
                every {
                    arbiAdapter.getStatement(
                        accountNumber = cashInAccountNo,
                        document = settlementAccountDocument,
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf(settlementValueFromCashinToCelcoin))
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf(
                    paidBillWithCreditCardAndBalance,
                    paidBillWithCreditCardAndBalance.copy(billType = BillType.PIX),
                )

                val report = bankAccountReconciliationReportService.generateSettlementAccountReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                )

                report.first shouldBe SettlementAccountReport(
                    totalAmountArbi = settlementCredit.amount,
                    totalAmountFriday = paidBill.amountTotal,
                    totalRefundedToCashinAccount = 0L,
                )
                report.first.hasDivergence().shouldBeFalse()
                report.second shouldBe SettlementAccountReport(
                    totalAmountArbi = creditCardValue,
                    totalAmountFriday = creditCardValue,
                    totalRefundedToCashinAccount = 0L,
                )
                report.second.hasDivergence().shouldBeFalse()
            }

            @Test
            fun `quando um boleto pago com cartão foi devolvido e o valor foi repassado a conta cashin`() {
                val tefRefundedCreditCardReceived =
                    tefRefundedCreditCardReceived.copy(
                        amount = creditCardValue,
                    )
                every {
                    arbiAdapter.getStatement(
                        accountNumber = cashInAccountNo,
                        document = settlementAccountDocument,
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf(tefRefundedCreditCardReceived))

                val tefRefundedCreditCardSent =
                    tefRefundedCreditCardSent.copy(
                        amount = creditCardValue,
                    )
                every {
                    arbiAdapter.getStatement(
                        accountNumber = settlementAccountNo,
                        document = settlementAccountDocument,
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf(tefRefundedCreditCardSent, tefCredit, tefDebit))

                every {
                    transactionRepository.findTransactions(any(), TransactionStatus.UNDONE)
                } returns listOf(
                    balanceAndCardTransaction,
                )

                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf()

                val report = bankAccountReconciliationReportService.generateSettlementAccountReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                )

                report.first.totalRefundedToCashinAccount shouldBe creditCardValue
                report.second.totalRefundedToCashinAccount shouldBe creditCardValue
            }

            @Test
            fun `when total credit amount equals total debits amount and there are no boleto bills paid`() {
                every {
                    arbiAdapter.getStatement(
                        accountNumber = settlementAccountNo,
                        document = settlementAccountDocument,
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf(tefCredit, tefDebit))
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf(paidBill.copy(billType = BillType.PIX))

                val report = bankAccountReconciliationReportService.generateSettlementAccountReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                )

                report.first shouldBe SettlementAccountReport(
                    totalAmountArbi = 0L,
                    totalAmountFriday = 0L,
                    totalRefundedToCashinAccount = 0L,
                )
                report.first.hasDivergence().shouldBeFalse()
                report.second shouldBe SettlementAccountReport(
                    totalAmountArbi = 0L,
                    totalAmountFriday = 0L,
                    totalRefundedToCashinAccount = 0L,
                )
                report.second.hasDivergence().shouldBeFalse()
            }

            @Test
            fun `when there only credits, debits and paid boleto bills overtime`() {
                val overtimeCredit =
                    tefCredit.copy(lastUpdate = queryDate.atTime(cutOffTime, 0, 1).atZone(brazilTimeZone))
                val overtimeDebit = tefDebit.copy(
                    amount = 2 * tefCredit.amount,
                    lastUpdate = queryDate.atTime(cutOffTime, 0, 1).atZone(brazilTimeZone),
                )
                val overtimePaidBill = getPaidBill().copy(paidDate = queryDate.atTime(cutOffTime, 0, 1))

                every {
                    arbiAdapter.getStatement(
                        accountNumber = settlementAccountNo,
                        document = settlementAccountDocument,
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf(overtimeCredit, overtimeDebit))
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf(overtimePaidBill)

                val report = bankAccountReconciliationReportService.generateSettlementAccountReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                )

                report.first shouldBe SettlementAccountReport(
                    totalAmountArbi = 0L,
                    totalAmountFriday = 0L,
                    totalRefundedToCashinAccount = 0L,
                )
                report.first.hasDivergence().shouldBeFalse()
                report.second shouldBe SettlementAccountReport(
                    totalAmountArbi = 0L,
                    totalAmountFriday = 0L,
                    totalRefundedToCashinAccount = 0L,
                )
                report.second.hasDivergence().shouldBeFalse()
            }

            @Test
            fun `when there is an account with multiple wallets`() {
                val secondWallet = walletFixture.buildWallet(
                    walletFounder = ACCOUNT.toMember(
                        type = MemberType.FOUNDER,
                        permissions = MemberPermissions(
                            viewBills = BillPermission.ALL_BILLS,
                            scheduleBills = BillPermission.ALL_BILLS,
                            founderContactsEnabled = false,
                            viewBalance = true,
                            notification = true,
                        ),
                    ),
                )
                every { walletRepository.findWallets(ACCOUNT.accountId, MemberStatus.ACTIVE) } returns listOf(arbiWallet, secondWallet)

                val settlementCredit = tefCredit.copy(amount = paidBill.amountTotal)

                every {
                    arbiAdapter.getStatement(
                        accountNumber = settlementAccountNo,
                        document = settlementAccountDocument,
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf(settlementCredit, settlementCredit))
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf(paidBill)
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = secondWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf(paidBill)

                val report = bankAccountReconciliationReportService.generateSettlementAccountReport(
                    date = queryDate,
                    cutOffTime = cutOffTime,
                )

                report.first shouldBe SettlementAccountReport(
                    totalAmountArbi = settlementCredit.amount * 2,
                    totalAmountFriday = paidBill.amountTotal * 2,
                    totalRefundedToCashinAccount = 0L,
                )
                report.first.hasDivergence().shouldBeFalse()
                report.second shouldBe SettlementAccountReport(
                    0L,
                    0L,
                    totalRefundedToCashinAccount = 0L,
                )
                report.second.hasDivergence().shouldBeFalse()
            }
        }

        @Nested
        @DisplayName("should return divergence")
        inner class DivergencesFound {
            @Test
            fun `when there is a credit and no boleto bill paid`() {
                every {
                    arbiAdapter.getStatement(
                        accountNumber = settlementAccountNo,
                        document = settlementAccountDocument,
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf(tefCredit))
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf(paidBill.copy(billType = BillType.PIX))

                val report = bankAccountReconciliationReportService.generateSettlementAccountReport(
                    date = queryDate,
                    cutOffTime = 12,
                )

                report.first shouldBe SettlementAccountReport(
                    totalAmountArbi = tefCredit.amount,
                    totalAmountFriday = 0L,
                    totalRefundedToCashinAccount = 0L,
                )
                report.first.hasDivergence().shouldBeTrue()
                report.second shouldBe SettlementAccountReport(
                    0L,
                    0L,
                    totalRefundedToCashinAccount = 0L,
                )
                report.second.hasDivergence().shouldBeFalse()
            }

            @Test
            fun `when there is a boleto bill paid and no credits`() {
                every {
                    arbiAdapter.getStatement(
                        accountNumber = settlementAccountNo,
                        document = settlementAccountDocument,
                        initialDate = any(),
                        endDate = any(),
                    )
                } returns BankStatement(listOf())
                every {
                    billRepository.findByWalletAndStatus(
                        walletId = arbiWallet.id,
                        status = BillStatus.PAID,
                    )
                } returns listOf(paidBill)

                val report = bankAccountReconciliationReportService.generateSettlementAccountReport(
                    date = queryDate,
                    cutOffTime = 12,
                )

                report.first shouldBe SettlementAccountReport(
                    totalAmountArbi = 0L,
                    totalAmountFriday = paidBill.amountTotal,
                    totalRefundedToCashinAccount = 0L,
                )
                report.first.hasDivergence().shouldBeTrue()
                report.second shouldBe SettlementAccountReport(
                    0L,
                    0L,
                    totalRefundedToCashinAccount = 0L,
                )
                report.second.hasDivergence().shouldBeFalse()
            }
        }
    }
}