package ai.friday.billpayment.app.reports

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.usage.CreditCardRiskUsage
import ai.friday.billpayment.app.usage.CreditCardUsage
import ai.friday.billpayment.app.usage.CreditCardUsageService
import ai.friday.billpayment.app.usage.TransactionAmount
import ai.friday.billpayment.app.usage.TransactionItemAmount
import ai.friday.billpayment.bankAccount
import ai.friday.billpayment.creditCard
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID_2
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID_4
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID_5
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class CashInCreditCardUsageServiceTest {

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val enhancedClient = getDynamoDB()

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )
    private val creditCardRiskUsage =
        CreditCardRiskUsage(quota = 20_000_00, usage = 0)
    private val accountId1CreditCardUsage = CreditCardUsage(
        quota = 20_000_00,
        usage = 0,
        cashInFee = 0,
        riskUsages = mapOf(
            RiskLevel.LOW to creditCardRiskUsage,
        ),
    )
    private val accountId2CreditCardUsage = CreditCardUsage(
        quota = 20_000_00,
        usage = 1000,
        cashInFee = 0,
        riskUsages = mapOf(RiskLevel.LOW to creditCardRiskUsage.copy(usage = 1000)),
    )

    private val accountId1cashInTotal =
        TransactionAmount(TransactionItemAmount(AccountPaymentMethodId(PAYMENT_METHOD_ID), 100L, 1L))
    private val accountId2cashInTotal =
        TransactionAmount(TransactionItemAmount(AccountPaymentMethodId(PAYMENT_METHOD_ID_2), 200L, 2L))

    private val creditCardUsageService: CreditCardUsageService = mockk {
        every {
            calculateCreditCardUsage(AccountId(ACCOUNT_ID))
        } returns accountId1CreditCardUsage
        every {
            calculateCreditCardUsage(AccountId(ACCOUNT_ID_2))
        } returns accountId2CreditCardUsage
        every {
            getCreditCardUsageOnDate(AccountId(ACCOUNT_ID), any())
        } returns accountId1cashInTotal
        every {
            getCreditCardUsageOnDate(AccountId(ACCOUNT_ID_2), any())
        } returns accountId2cashInTotal
    }

    private val creditCardCashInUsageService =
        CreditCardCashInUsageService(
            accountRepository = accountRepository,
            creditCardUsageService = creditCardUsageService,
        )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `should return empty usage when there is no account with credit card`() {
        setupAccount(
            account = ACCOUNT,
            balancePaymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_5),
            creditCardPaymentMethodId = null,
        )

        val availableQuotaSum = creditCardCashInUsageService.reportAvailableQuotaSum()

        availableQuotaSum shouldBe 0
    }

    @Test
    fun `should return single usage when there one account with credit card`() {
        setupAccount(
            account = ACCOUNT,
            balancePaymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_5),
            creditCardPaymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID),
        )

        val availableQuotaSum = creditCardCashInUsageService.reportAvailableQuotaSum()

        verify {
            creditCardUsageService.calculateCreditCardUsage(AccountId(ACCOUNT_ID))
        }

        availableQuotaSum shouldBe accountId1CreditCardUsage.availableQuota
    }

    @Test
    fun `should return both usages when there is two accounts with credit card`() {
        setupAccount(
            account = ACCOUNT,
            balancePaymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_5),
            creditCardPaymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID),
        )
        setupAccount(
            account = ACCOUNT.copy(accountId = AccountId(ACCOUNT_ID_2)),
            balancePaymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_4),
            creditCardPaymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_2),
        )

        val availableQuotaSum = creditCardCashInUsageService.reportAvailableQuotaSum()

        verify {
            creditCardUsageService.calculateCreditCardUsage(AccountId(ACCOUNT_ID))
            creditCardUsageService.calculateCreditCardUsage(AccountId(ACCOUNT_ID_2))
        }

        availableQuotaSum shouldBe accountId1CreditCardUsage.availableQuota + accountId2CreditCardUsage.availableQuota
    }

    @Test
    fun `should calculate cash-in total for multiple accounts`() {
        setupAccount(
            account = ACCOUNT,
            balancePaymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_5),
            creditCardPaymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID),
        )
        setupAccount(
            account = ACCOUNT.copy(accountId = AccountId(ACCOUNT_ID_2)),
            balancePaymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_4),
            creditCardPaymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_2),
        )

        val yesterday = getZonedDateTime().toLocalDate().minusDays(1)

        val cashInTotalResult = creditCardCashInUsageService.reportCashInByDate(
            getZonedDateTime().toLocalDate().minusDays(1),
        )

        verify {
            creditCardUsageService.getCreditCardUsageOnDate(AccountId(ACCOUNT_ID), yesterday)
            creditCardUsageService.getCreditCardUsageOnDate(AccountId(ACCOUNT_ID_2), yesterday)
        }

        cashInTotalResult.totalAmount shouldBe accountId1cashInTotal.totalAmount + accountId2cashInTotal.totalAmount
        cashInTotalResult.netAmount shouldBe accountId1cashInTotal.netAmount + accountId2cashInTotal.netAmount
        cashInTotalResult.feeAmount shouldBe accountId1cashInTotal.feeAmount + accountId2cashInTotal.feeAmount
    }

    private fun setupAccount(
        account: Account,
        balancePaymentMethodId: AccountPaymentMethodId?,
        creditCardPaymentMethodId: AccountPaymentMethodId?,
    ) {
        accountRepository.save(account)

        balancePaymentMethodId?.let {
            accountRepository.createAccountPaymentMethod(
                accountId = account.accountId,
                paymentMethodId = it,
                position = 1,
                status = AccountPaymentMethodStatus.ACTIVE,
                bankAccount = bankAccount,
                mode = BankAccountMode.PHYSICAL,
            )
        }

        creditCardPaymentMethodId?.let {
            accountRepository.createAccountPaymentMethod(
                accountId = account.accountId,
                paymentMethodId = it,
                position = 2,
                status = AccountPaymentMethodStatus.ACTIVE,
                creditCard = creditCard.method as CreditCard,
            )
        }
    }
}