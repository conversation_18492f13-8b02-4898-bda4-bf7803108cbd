package ai.friday.billpayment.app.reports

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.accountRegisterCompleted
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.OriginalOcrAndPersonDataDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.AccountStatus.ACTIVE
import ai.friday.billpayment.app.account.AccountStatus.APPROVED
import ai.friday.billpayment.app.account.AccountStatus.REGISTER_INCOMPLETE
import ai.friday.billpayment.app.account.AccountStatus.UNDER_EXTERNAL_REVIEW
import ai.friday.billpayment.app.account.AccountStatus.UNDER_REVIEW
import ai.friday.billpayment.app.account.NewAccountReportConfiguration
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.backoffice.BackofficeRegisterService
import ai.friday.billpayment.app.backoffice.UserAccountRegister
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.shouldBe
import io.micronaut.http.MediaType
import io.mockk.mockk
import io.mockk.verify
import io.via1.communicationcentre.app.integrations.EmailSenderService
import java.time.ZoneId
import java.time.ZonedDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

internal class NewAccountReportServiceTest {
    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbEnhancedClient = getDynamoDB()
    private val accountDAO = AccountDynamoDAO(dynamoDbEnhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(dynamoDbEnhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(dynamoDbEnhancedClient)
    private val nsuDAO = NSUDynamoDAO(dynamoDbEnhancedClient)
    private val transactionDynamo = TransactionDynamo(dynamoDbEnhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )
    private val objectRepository: ObjectRepository = mockk()
    private val accountRegisterDAO = AccountRegisterDynamoDAO(dynamoDbEnhancedClient)
    private val originalOcrAndPersonDataDAO = OriginalOcrAndPersonDataDynamoDAO(dynamoDbEnhancedClient)
    private val accountRegisterRepository = AccountRegisterDbRepository(accountRegisterDAO, originalOcrAndPersonDataDAO, objectRepository)
    private val backofficeRegisterService = BackofficeRegisterService(
        accountRepository = accountRepository,
        accountRegisterRepository = accountRegisterRepository,
        accountService = mockk(),
        closeAccountService = mockk(),
        notificationAdapter = mockk(),
        registerInstrumentationService = mockk(),
        livenessService = mockk(),
        accountRegisterService = mockk(),
        deeplinkUrl = "deeplinkUrl",
    )
    private val emailSender: EmailSenderService = mockk(relaxed = true)
    private val fromAddress = "<EMAIL>"
    private val configuration = NewAccountReportConfiguration(
        recipients = "<EMAIL>",
        subject = "relatório",
        tableStyle = "table",
        thStyle = "th",
        tdStyle = "td",
    )
    private val newAccountReportService = NewAccountReportService(
        backofficeRegisterService = backofficeRegisterService,
        emailSender = emailSender,
        reportConfiguration = configuration,
    ).apply {
        from = fromAddress
    }

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @ParameterizedTest
    @EnumSource(
        AccountStatus::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["UNDER_REVIEW", "UNDER_EXTERNAL_REVIEW", "APPROVED"],
    )
    fun `should return all users with given status`(status: AccountStatus) {
        val now = getZonedDateTime()

        val partialAccount = accountRepository.create(
            "Joao das Couves",
            EmailAddress("<EMAIL>"),
            registrationType = RegistrationType.BASIC,
        )

        withGivenDateTime(now) {
            accountRegisterRepository.save(
                accountRegisterCompleted.copy(
                    accountId = partialAccount.id,
                    nickname = partialAccount.name,
                    openForUserReview = (status == REGISTER_INCOMPLETE),
                ),
            )
            accountRepository.updatePartialAccountStatus(partialAccount.id, status)
        }

        val report = newAccountReportService.build()

        report.size shouldBe 1
        with(report.first()) {
            name shouldBe accountRegisterCompleted.documentInfo?.name
            updated shouldBe now
            this.status shouldBe status
            email shouldBe accountRegisterCompleted.emailAddress
            accountId shouldBe partialAccount.id
            document shouldBe accountRegisterCompleted.documentInfo?.cpf
            phone shouldBe accountRegisterCompleted.mobilePhone
        }

        verify {
            emailSender.sendRawEmail(
                fromAddress,
                configuration.subject,
                any(),
                configuration.recipients,
                listOf(),
                MediaType.TEXT_HTML,
            )
        }
    }

    @Test
    fun `should include account when it is waiting to be activated`() {
        val now = getZonedDateTime()

        val partialAccount = accountRepository.create(
            "Joao das Couves",
            EmailAddress("<EMAIL>"),
            registrationType = RegistrationType.BASIC,
        )
        accountRepository.create(ACCOUNT.copy(accountId = partialAccount.id, mobilePhone = "+*************"))

        withGivenDateTime(now) {
            accountRegisterRepository.save(
                accountRegisterCompleted.copy(
                    accountId = partialAccount.id,
                    nickname = partialAccount.name,
                    openForUserReview = false,
                ),
            )
            accountRepository.updatePartialAccountStatus(partialAccount.id, APPROVED)
        }

        val report = newAccountReportService.build()

        report.size shouldBe 1
        with(report.first()) {
            name shouldBe accountRegisterCompleted.documentInfo?.name
            updated shouldBe now
            status shouldBe AccountStatus.APPROVED
            email shouldBe accountRegisterCompleted.emailAddress
            accountId shouldBe partialAccount.id
            document shouldBe accountRegisterCompleted.documentInfo?.cpf
            phone shouldBe accountRegisterCompleted.mobilePhone
        }

        verify {
            emailSender.sendRawEmail(
                fromAddress,
                configuration.subject,
                any(),
                configuration.recipients,
                listOf(),
                MediaType.TEXT_HTML,
            )
        }
    }

    @Test
    fun `should build report content`() {
        val now = ZonedDateTime.of(2021, 10, 18, 15, 15, 0, 0, ZoneId.of("Brazil/East"))
        val oneHourAgo = now.minusHours(1)
        val twoHoursAgo = now.minusHours(2)
        val yesterday = now.minusDays(1)

        val reportEntries = listOf(
            UserAccountRegister(
                accountId = AccountId("ACCOUNT-3"),
                name = "Jose",
                document = "***********",
                email = EmailAddress("<EMAIL>"),
                phone = MobilePhone("+*************"),
                status = APPROVED,
                updated = now,
                groups = emptyList(),
                userAccountType = UserAccountType.BASIC_ACCOUNT,
                subscriptionType = SubscriptionType.PIX,
            ),
            UserAccountRegister(
                accountId = AccountId("ACCOUNT-4"),
                name = "Ana",
                document = "***********",
                email = EmailAddress("<EMAIL>"),
                phone = MobilePhone("+*************"),
                status = UNDER_EXTERNAL_REVIEW,
                updated = oneHourAgo,
                groups = emptyList(),
                userAccountType = UserAccountType.BASIC_ACCOUNT,
                subscriptionType = SubscriptionType.PIX,
            ),
            UserAccountRegister(
                accountId = AccountId("ACCOUNT-5"),
                name = "Mario",
                document = "***********",
                email = EmailAddress("<EMAIL>"),
                phone = MobilePhone("+*************"),
                status = UNDER_REVIEW,
                updated = twoHoursAgo,
                groups = emptyList(),
                userAccountType = UserAccountType.BASIC_ACCOUNT,
                subscriptionType = SubscriptionType.PIX,
            ),
            UserAccountRegister(
                accountId = AccountId("ACCOUNT-6"),
                name = "Carlos",
                document = "***********",
                email = EmailAddress("<EMAIL>"),
                phone = MobilePhone("+*************"),
                status = ACTIVE,
                updated = yesterday,
                groups = emptyList(),
                userAccountType = UserAccountType.BASIC_ACCOUNT,
                subscriptionType = SubscriptionType.PIX,
            ),
        )

        val content = withGivenDateTime(now) {
            newAccountReportService.buildReportContent(reportEntries)
        }

        content shouldBe """
            |<table style="table" class="tg">
            |<tr><td style="th">ID</td><td style="th">Nome</td><td style="th">CPF</td><td style="th">email</td><td style="th">telefone</td><td style="th">estado atual</td><td style="th">atualizado há</td></tr>
            |<tr><td style="td">ACCOUNT-3</td><td style="td">Jose</td><td style="td">333.333.333-33</td><td style="td"><EMAIL></td><td style="td">+*************</td><td style="td">Aguardando Ativação</td><td style="td">0h</td></tr>
            |<tr><td style="td">ACCOUNT-4</td><td style="td">Ana</td><td style="td">444.444.444-44</td><td style="td"><EMAIL></td><td style="td">+*************</td><td style="td">Em revisão externa</td><td style="td">1h</td></tr>
            |<tr><td style="td">ACCOUNT-5</td><td style="td">Mario</td><td style="td">555.555.555-55</td><td style="td"><EMAIL></td><td style="td">+*************</td><td style="td">Em revisão interna</td><td style="td">2h</td></tr>
            |<tr><td style="td">ACCOUNT-6</td><td style="td">Carlos</td><td style="td">666.666.666-66</td><td style="td"><EMAIL></td><td style="td">+*************</td><td style="td">Ativo</td><td style="td">24h</td></tr>
            |</table>
        """.trimMargin()
    }
}