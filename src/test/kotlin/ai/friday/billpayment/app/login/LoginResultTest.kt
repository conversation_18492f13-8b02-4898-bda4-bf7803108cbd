package ai.friday.billpayment.app.login

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

internal class LoginResultTest {

    private val accountId = AccountId("FAKE")
    private val emailAddress = EmailAddress("FAKE_EMAIL")
    private val roleGuest = Role.GUEST

    @Test
    fun `should return true when role is guest and created is true`() {
        val loginResult = LoginResult(Login(accountId, emailAddress, roleGuest), true)
        loginResult.shouldCreateAccountRegister() shouldBe true
    }

    @Test
    fun `should return false when role is guest and created is false`() {
        val loginResult = LoginResult(Login(accountId, emailAddress, roleGuest), false)
        loginResult.shouldCreateAccountRegister() shouldBe false
    }

    @ParameterizedTest
    @EnumSource(Role::class, mode = EnumSource.Mode.EXCLUDE, names = ["GUEST"])
    fun `should return false when role is not guest`(currentRole: Role) {
        LoginResult(Login(accountId, emailAddress, currentRole), false).shouldCreateAccountRegister() shouldBe false
        LoginResult(Login(accountId, emailAddress, currentRole), true).shouldCreateAccountRegister() shouldBe false
    }
}