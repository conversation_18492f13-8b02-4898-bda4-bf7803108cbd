package ai.friday.billpayment.app.bill.tracking

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.app.integrations.BillTrackingRepository
import ai.friday.billpayment.billAdded
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class DefaultRemoveTrackableBillServiceTest {
    private lateinit var test: DefaultRemoveTrackableBillService

    private lateinit var repository: BillTrackingRepository

    @BeforeEach
    fun setUp() {
        repository = mockk()

        test = DefaultRemoveTrackableBillService(repository = repository)
    }

    @Test
    fun `should remove TrackableBill`() {
        every { repository.remove(any()) } just runs

        val result = test.remove(billAdded)

        result.isRight() shouldBe true
        result.map { it shouldBe Unit }

        verify { repository.remove(billAdded.billId) }
    }

    @Test
    fun `should return error when repository throws an error`() {
        every { repository.remove(any()) } throws NoStackTraceException()

        val result = test.remove(billAdded)

        result.isLeft() shouldBe true
        result.mapLeft { it.ex.shouldBeTypeOf<NoStackTraceException>() }

        verify { repository.remove(billAdded.billId) }
    }
}