package ai.friday.billpayment.app.bill

import ai.friday.billpayment.EMAIL_ADDRESS
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.arbi.ArbiValidationResponse
import ai.friday.billpayment.adapters.arbi.CIPSitTitPgto
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.duplication.PossibleDuplicate
import ai.friday.billpayment.app.bill.duplication.PossibleDuplicateBillService
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.BillValidationService
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.BillValidationResponse
import ai.friday.billpayment.app.payment.BillValidationStatus
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.payment.NU_BANK_DOCUMENT
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.canView
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billIgnored
import ai.friday.billpayment.fichaRegisterData
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.BILL_ID_5
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.DOCUMENT_2
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_BARCODE
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2
import ai.friday.billpayment.integration.NAME
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.withEarlyAccess
import ai.friday.billpayment.withoutLogs
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import arrow.core.left
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

internal class FichaCompensacaoServiceTest {
    private val member =
        Member(
            accountId = AccountId(ACCOUNT_ID),
            document = DOCUMENT,
            name = NAME,
            emailAddress = EMAIL_ADDRESS,
            type = MemberType.COLLABORATOR,
            permissions =
            MemberPermissions(
                viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                scheduleBills = BillPermission.NO_BILLS,
                viewBalance = true,
                notification = true,
            ),
            status = MemberStatus.ACTIVE,
            created = getZonedDateTime(),
            updated = getZonedDateTime(),
        )

    private val secondMember =
        Member(
            accountId = AccountId(ACCOUNT_ID_2),
            document = DOCUMENT_2,
            name = NAME,
            emailAddress = EMAIL_ADDRESS,
            type = MemberType.COLLABORATOR,
            permissions =
            MemberPermissions(
                viewBills = BillPermission.NO_BILLS,
                scheduleBills = BillPermission.NO_BILLS,
                viewBalance = true,
                notification = true,
            ),
            status = MemberStatus.ACTIVE,
            created = getZonedDateTime(),
            updated = getZonedDateTime(),
        )

    private val createFichaDeCompensacaoRequest =
        CreateFichaDeCompensacaoRequest(
            description = "description fake",
            barcode = BarCode(number = FICHA_DE_COMPENSACAO_BARCODE, digitable = FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
            walletId = WalletId("RANDOM-WALLET-ID"),
            source = ActionSource.Api(AccountId(ACCOUNT_ID)),
            member = member,
        )

    @BeforeEach
    fun setup() {
        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns
            Either.Right(
                Bill.build(
                    billAdded.copy(
                        actionSource = ActionSource.DDA(accountId = AccountId(ACCOUNT_ID_2)),
                        document = DOCUMENT_2,
                    ),
                ),
            )

        every {
            billEventRepository.findLastBill(idNumber = any(), WalletId("RANDOM-WALLET-ID"))
        } returns Either.Left(ItemNotFoundException(""))
    }

    private val validationService = mockk<BillValidationService>()
    private val billEventRepository = mockk<BillEventRepository>()
    private val updateBillService =
        mockk<UpdateBillService>(relaxed = true) {
            every { publishEvent(any(), any()) } answers {
                firstArg<Bill>().apply(secondArg())
            }
        }

    private val billEventPublisher =
        mockk<BillEventPublisher>(relaxed = true) {
            every { publish(any(), any()) } answers {
                firstArg<Bill>().apply(secondArg())
            }
        }

    private val possibleDuplicateBillService =
        mockk<PossibleDuplicateBillService> {
            every {
                check(any<Bill>())
            } returns emptyList()
        }

    private val featureConfiguration =
        mockk<FeatureConfiguration>(relaxed = true) {
            every { blockDuplicateByIdNumber } returns true
        }

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    private val walletService: WalletService =
        mockk {
            every { findWallet(any()) } returns wallet
        }

    val billRepository: BillRepository = mockk()

    private val blockEmptyAmountCreationService: BlockEmptyAmountCreationService = mockk {
        every { check(any(), any()) } returns false
    }

    private val fichaCompensacaoService =
        FichaCompensacaoService(
            validationService = validationService,
            billEventRepository = billEventRepository,
            updateBillService = updateBillService,
            possibleDuplicateBillService = possibleDuplicateBillService,
            walletService = walletService,
            scheduleBillService = mockk(),
            billEventPublisher = billEventPublisher,
            featureConfiguration = featureConfiguration,
            blockEmptyAmountCreationService = blockEmptyAmountCreationService,
            enrichmentRules = emptyList(),
        )

    private val billValidationResponseAlreadyPaid =
        mockk<BillValidationResponse> {
            every {
                alreadyPaid()
            } returns true

            every {
                getStatus()
            } returns BillValidationStatus.AlreadyPaid

            every {
                errorDescription
            } returns "error fake"

            every {
                billRegisterData
            } returns fichaRegisterData
        }

    @Test
    fun `should not create bill when it is already paid and source is not DDA`() {
        every {
            validationService.validate(any<BarCode>())
        } returns billValidationResponseAlreadyPaid

        every {
            billEventRepository.findLastBill(barCode = any(), any())
        } returns Either.Left(ItemNotFoundException("nao tenho a conta ainda!"))

        val response =
            fichaCompensacaoService.createFichaDeCompensacao(
                request = createFichaDeCompensacaoRequest,
                dryRun = false,
            )

        response.shouldBeInstanceOf<CreateBillResult.FAILURE.AlreadyPaid.WithData>()
    }

    @Test
    fun `should not create bill already paid when source is not DDA and is Dry Run`() {
        every {
            validationService.validate(any<BarCode>())
        } returns billValidationResponseAlreadyPaid

        every {
            billEventRepository.findLastBill(barCode = any(), any())
        } returns Either.Left(ItemNotFoundException("nao tenho a conta ainda!"))

        val response =
            fichaCompensacaoService.createFichaDeCompensacao(
                request = createFichaDeCompensacaoRequest,
                dryRun = true,
            )

        response.shouldBeInstanceOf<CreateBillResult.FAILURE.AlreadyPaid.WithData>()
    }

    @Test
    fun `should not create bill already paid when source is DDA and is Dry Run`() {
        every {
            validationService.validate(any<BarCode>())
        } returns billValidationResponseAlreadyPaid

        every {
            billEventRepository.findLastBill(barCode = any(), any())
        } returns Either.Left(ItemNotFoundException("nao tenho a conta ainda!"))

        val response =
            fichaCompensacaoService.createFichaDeCompensacao(
                request = createFichaDeCompensacaoRequest,
                dryRun = true,
            )

        response.shouldBeInstanceOf<CreateBillResult.FAILURE.AlreadyPaid.WithData>()
    }

    @Test
    fun `should return server error result on generic error validating ficha de compensacao bill`() {
        every {
            validationService.validate(any<BarCode>())
        } returns ArbiValidationResponse(resultado = "1 - Timeout alcancado 0")

        every {
            billEventRepository.findLastBill(barCode = any(), any())
        } returns Either.Left(ItemNotFoundException("nao tenho a conta ainda!"))

        val response =
            fichaCompensacaoService.createFichaDeCompensacao(
                request = createFichaDeCompensacaoRequest,
                dryRun = true,
            )

        response.shouldBeInstanceOf<CreateBillResult.FAILURE.ServerError>()
    }

    @Test
    fun `should return unable to validate on barcode not found error validating ficha de compensacao bill`() {
        every {
            validationService.validate(any<BarCode>())
        } returns ArbiValidationResponse(resultado = "7 - Mensagem rejeitada pela CIP: EDDA0526 - Código de barras não localizado na base centralizada")

        every {
            billEventRepository.findLastBill(barCode = any(), any())
        } returns Either.Left(ItemNotFoundException("nao tenho a conta ainda!"))

        val response =
            fichaCompensacaoService.createFichaDeCompensacao(
                request = createFichaDeCompensacaoRequest,
                dryRun = true,
            )

        response.shouldBeInstanceOf<CreateBillResult.FAILURE.BillUnableToValidate>()
        response.isRetryable.shouldBeTrue()
    }

    @Test
    fun `should return unable to validate and non-retryable when validating ficha de compensacao unable to validate and non-retryable`() {
        every {
            validationService.validate(any<BarCode>())
        } returns ArbiValidationResponse(resultado = "107: Grupo de cálculo não informado!")

        every {
            billEventRepository.findLastBill(barCode = any(), any())
        } returns Either.Left(ItemNotFoundException("nao tenho a conta ainda!"))

        val response =
            fichaCompensacaoService.createFichaDeCompensacao(
                request = createFichaDeCompensacaoRequest,
                dryRun = true,
            )

        response.shouldBeInstanceOf<CreateBillResult.FAILURE.BillUnableToValidate>()
        response.isRetryable.shouldBeFalse()
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `should not create bill(ficha de compensacao) but make it visible when not visible bill with same digitableline already exists`(
        dryRun: Boolean,
    ) {
        every {
            validationService.validate(any<BarCode>())
        } returns
            ArbiValidationResponse(
                billRegisterData = fichaRegisterData,
                paymentStatus = CIPSitTitPgto.SUCESSO.code,
            )

        every {
            billEventRepository.findLastBill(barCode = any(), walletId = any())
        } returns
            Either.Right(
                Bill.build(
                    billAddedFicha.copy(
                        actionSource = ActionSource.DDA(accountId = AccountId(ACCOUNT_ID_2)),
                        document = DOCUMENT_2,
                        dueDate = fichaRegisterData.dueDate!!,
                    ),
                ),
            )

        val response =
            fichaCompensacaoService.createFichaDeCompensacao(
                request = createFichaDeCompensacaoRequest,
                dryRun = dryRun,
            )

        response.shouldBeInstanceOf<CreateBillResult.SUCCESS>()

        with(response) {
            warningCode shouldBe WarningCode.NOT_VISIBLE_BILL_ALREADY_EXISTS
            member.canView(bill) shouldBe true
        }

        if (dryRun) {
            verify(exactly = 0) {
                billEventPublisher.publish(any(), any())
            }
        } else {
            verify(exactly = 1) {
                billEventPublisher.publish(any(), ofType<BillPermissionUpdated>())
            }
        }
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `should create bill when ficha de compensacao type is credit card and not visible bill already exists with same idNumber and different due date`(
        dryRun: Boolean,
    ) {
        every {
            validationService.validate(any<BarCode>())
        } returns
            ArbiValidationResponse(
                billRegisterData =
                fichaRegisterData.copy(
                    dueDate = getLocalDate().plusDays(2),
                    fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO,
                ),
                paymentStatus = CIPSitTitPgto.SUCESSO.code,
            )

        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns Either.Left(ItemNotFoundException(""))

        every {
            billEventRepository.findLastBill(idNumber = any(), WalletId("RANDOM-WALLET-ID"))
        } returns
            Either.Right(
                Bill.build(
                    billAddedFicha.copy(
                        actionSource = ActionSource.DDA(accountId = AccountId(ACCOUNT_ID_2)),
                        document = DOCUMENT_2,
                        dueDate = getLocalDate(),
                        idNumber = fichaRegisterData.idNumber,
                    ),
                ),
            )

        val response =
            fichaCompensacaoService.createFichaDeCompensacao(
                request = createFichaDeCompensacaoRequest,
                dryRun = dryRun,
            )

        response.shouldBeInstanceOf<CreateBillResult.SUCCESS>()

        with((response)) {
            warningCode shouldBe WarningCode.NONE
        }

        if (dryRun) {
            verify(exactly = 0) {
                updateBillService.publishEvent(any(), any())
            }
        } else {
            verify(exactly = 0) {
                updateBillService.publishEvent(any(), ofType<BillPermissionUpdated>())
            }
        }
    }

    @Test
    fun `should not create bill when ficha de compensacao is an overdue credit card and source is DDA`() {
        every {
            validationService.validate(any<BarCode>())
        } returns
            ArbiValidationResponse(
                billRegisterData =
                fichaRegisterData.copy(
                    dueDate = getLocalDate().minusDays(2),
                    fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO,
                ),
                paymentStatus = CIPSitTitPgto.SUCESSO.code,
            )

        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns Either.Left(ItemNotFoundException(""))

        every {
            billEventRepository.findLastBill(idNumber = any(), WalletId("RANDOM-WALLET-ID"))
        } returns Either.Left(ItemNotFoundException(""))

        val response =
            fichaCompensacaoService.createFichaDeCompensacao(
                request =
                createFichaDeCompensacaoRequest.copy(
                    source =
                    ActionSource.DDA(
                        accountId =
                        AccountId(
                            ACCOUNT_ID,
                        ),
                    ),
                ),
                dryRun = false,
            )

        response.shouldBeInstanceOf<CreateBillResult.FAILURE.BillNotPayable>()
    }

    @Test
    fun `deve adicionar uma conta como ativa com valor zerado quando tipo de calculo for BENEFICIARY_ONLY e o valor total for zero`() {
        every {
            validationService.validate(any<BarCode>())
        } returns
            ArbiValidationResponse(
                billRegisterData =
                fichaRegisterData.copy(
                    dueDate = getLocalDate().plusDays(2),
                    fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO,
                    amountCalculationModel = AmountCalculationModel.BENEFICIARY_ONLY,
                    amountTotal = 0,
                    amount = 0,
                ),
                paymentStatus = CIPSitTitPgto.SUCESSO.code,
            )

        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns Either.Left(ItemNotFoundException(""))

        val response =
            fichaCompensacaoService.createFichaDeCompensacao(
                request =
                createFichaDeCompensacaoRequest.copy(
                    source =
                    ActionSource.DDA(
                        accountId =
                        AccountId(
                            ACCOUNT_ID,
                        ),
                    ),
                ),
                dryRun = false,
            )

        response.shouldBeInstanceOf<CreateBillResult.SUCCESS>()
        response.warningCode shouldBe WarningCode.BENEFICIARY_UPDATE_REQUIRED
        response.bill.amountTotal shouldBe 0
        response.bill.status shouldBe BillStatus.WAITING_BENEFICIARY_UPDATE
    }

    @Test
    fun `should not create bill when ficha de compensacao is an overdue nu bank credit card and source is DDA`() {
        every {
            validationService.validate(any<BarCode>())
        } returns
            ArbiValidationResponse(
                billRegisterData =
                fichaRegisterData.copy(
                    dueDate = getLocalDate().minusDays(2),
                    fichaCompensacaoType = FichaCompensacaoType.CH_CHEQUE,
                    recipient =
                    Recipient(
                        name = "name",
                        document = NU_BANK_DOCUMENT,
                        alias = "alias",
                    ),
                ),
                paymentStatus = CIPSitTitPgto.SUCESSO.code,
            )

        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns Either.Left(ItemNotFoundException(""))

        every {
            billEventRepository.findLastBill(idNumber = any(), WalletId("RANDOM-WALLET-ID"))
        } returns Either.Left(ItemNotFoundException(""))

        val response =
            fichaCompensacaoService.createFichaDeCompensacao(
                request =
                createFichaDeCompensacaoRequest.copy(
                    source =
                    ActionSource.DDA(
                        accountId =
                        AccountId(
                            ACCOUNT_ID,
                        ),
                    ),
                ),
                dryRun = false,
            )

        response.shouldBeInstanceOf<CreateBillResult.FAILURE.BillNotPayable>()
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `should not create bill(ficha de compensacao) but make it visible when not visible bill with same idNumber already exists`(
        dryRun: Boolean,
    ) {
        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns ItemNotFoundException("").left()

        every {
            billEventRepository.findLastBill(idNumber = any(), WalletId("RANDOM-WALLET-ID"))
        } returns
            Either.Right(
                Bill.build(
                    billAddedFicha.copy(
                        actionSource = ActionSource.DDA(accountId = AccountId(ACCOUNT_ID_2)),
                        document = DOCUMENT_2,
                        barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2),
                        idNumber = fichaRegisterData.idNumber,
                    ),
                ),
            )

        every {
            validationService.validate(any<BarCode>())
        } returns
            ArbiValidationResponse(
                billRegisterData = fichaRegisterData,
                paymentStatus = CIPSitTitPgto.SUCESSO.code,
            )

        val response =
            fichaCompensacaoService.createFichaDeCompensacao(
                request = createFichaDeCompensacaoRequest,
                dryRun = dryRun,
            )

        response.shouldBeInstanceOf<CreateBillResult.SUCCESS>()

        with((response)) {
            warningCode shouldBe WarningCode.NOT_VISIBLE_BILL_ALREADY_EXISTS
            member.canView(bill) shouldBe true
        }

        if (dryRun) {
            verify(exactly = 0) {
                billEventPublisher.publish(any(), any())
            }
        } else {
            verify(exactly = 1) {
                billEventPublisher.publish(any(), ofType<BillPermissionUpdated>())
            }
        }
    }

    @Test
    fun `quando tentar adicionar um boleto de valor zerado via api, deve retornar que não é pagável`() {
        every {
            blockEmptyAmountCreationService.check(any(), any())
        } returns true

        val expectedFichaRegisterData = fichaRegisterData.copy(amountTotal = 0)
        every {
            validationService.validate(any<BarCode>())
        } returns
            ArbiValidationResponse(
                billRegisterData = expectedFichaRegisterData,
                paymentStatus = CIPSitTitPgto.SUCESSO.code,
            )

        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns Either.Left(ItemNotFoundException(""))

        val response =
            withEarlyAccess(AccountId(createFichaDeCompensacaoRequest.walletId.value)) {
                fichaCompensacaoService.createFichaDeCompensacao(
                    request = createFichaDeCompensacaoRequest.copy(source = ActionSource.Api(AccountId(ACCOUNT_ID))),
                    dryRun = false,
                )
            }

        response.shouldBeTypeOf<CreateBillResult.FAILURE.BillNotPayable>()
    }

    @Test
    fun `quando tentar adicionar um boleto de valor maior que 250 mil via api, deve retornar que não é pagável`() {
        val expectedFichaRegisterData = fichaRegisterData.copy(amountTotal = 250_000_01L)
        every {
            validationService.validate(any<BarCode>())
        } returns
            ArbiValidationResponse(
                billRegisterData = expectedFichaRegisterData,
                paymentStatus = CIPSitTitPgto.SUCESSO.code,
            )

        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns Either.Left(ItemNotFoundException(""))

        val response =
            withEarlyAccess(AccountId(createFichaDeCompensacaoRequest.walletId.value)) {
                fichaCompensacaoService.createFichaDeCompensacao(
                    request = createFichaDeCompensacaoRequest.copy(source = ActionSource.Api(AccountId(ACCOUNT_ID))),
                    dryRun = false,
                )
            }

        response.shouldBeTypeOf<CreateBillResult.FAILURE.BillNotPayable>()
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `should not create bill(ficha de compensacao) but make it visible and reactivate it when not visible bill already exists and is ignored`(
        dryRun: Boolean,
    ) {
        every {
            validationService.validate(any<BarCode>())
        } returns
            ArbiValidationResponse(
                billRegisterData = fichaRegisterData,
                paymentStatus = CIPSitTitPgto.SUCESSO.code,
            )

        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns
            Either.Right(
                Bill.build(
                    billAdded.copy(
                        actionSource = ActionSource.DDA(accountId = AccountId(ACCOUNT_ID_2)),
                        document = DOCUMENT_2,
                        dueDate = fichaRegisterData.dueDate!!,
                    ),
                    billIgnored,
                ),
            )

        val response =
            fichaCompensacaoService.createFichaDeCompensacao(
                request = createFichaDeCompensacaoRequest,
                dryRun = dryRun,
            )

        response.shouldBeInstanceOf<CreateBillResult.SUCCESS>()
        with((response)) {
            warningCode shouldBe null
            member.canView(bill) shouldBe true
            bill.status shouldBe BillStatus.ACTIVE
        }

        if (dryRun) {
            verify(exactly = 0) {
                updateBillService.synchronizeBill(any<Bill>(), any())
                updateBillService.synchronizeBill(any<BillId>(), any())
                billEventPublisher.publish(any(), any())
            }
        } else {
            verify(exactly = 1) {
                billEventPublisher.publish(any(), ofType<BillReactivated>())
                billEventPublisher.publish(any(), ofType<BillPermissionUpdated>())
            }
        }
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `should not create bill(ficha de compensacao) but make it visible and DONT reactivate it when source is not allowed`(
        dryRun: Boolean,
    ) {
        every {
            validationService.validate(any<BarCode>())
        } returns
            ArbiValidationResponse(
                billRegisterData = fichaRegisterData,
                paymentStatus = CIPSitTitPgto.SUCESSO.code,
            )

        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns
            Either.Right(
                Bill.build(
                    billAdded.copy(
                        actionSource = ActionSource.DDA(accountId = AccountId(ACCOUNT_ID_2)),
                        document = DOCUMENT_2,
                        dueDate = fichaRegisterData.dueDate!!,
                    ),
                    billIgnored,
                ),
            )

        val response =
            fichaCompensacaoService.createFichaDeCompensacao(
                request = createFichaDeCompensacaoRequest.copy(
                    source = ActionSource.VehicleDebts(accountId = member.accountId),
                ),
                dryRun = dryRun,
            )

        response.shouldBeInstanceOf<CreateBillResult.SUCCESS>().run {
            warningCode shouldBe null
            member.canView(bill) shouldBe true
            bill.status shouldBe BillStatus.IGNORED
        }

        if (dryRun) {
            verify(exactly = 0) {
                updateBillService.synchronizeBill(any<Bill>(), any())
                updateBillService.synchronizeBill(any<BillId>(), any())
                billEventPublisher.publish(any(), any())
            }
        } else {
            verify(exactly = 1) {
                billEventPublisher.publish(any(), ofType<BillPermissionUpdated>())
            }
        }
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `should create bill and should be visible by a member without permission when this member is the payer of the bill`(
        dryRun: Boolean,
    ) {
        every {
            validationService.validate(any<BarCode>())
        } returns
            ArbiValidationResponse(
                billRegisterData = fichaRegisterData.copy(payerDocument = DOCUMENT_2),
                paymentStatus = 12,
                resultado = "SUCESSO",
            )

        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns Either.Left(ItemNotFoundException(""))

        val response =
            fichaCompensacaoService.createFichaDeCompensacao(
                request = createFichaDeCompensacaoRequest,
                dryRun = dryRun,
            )

        response.shouldBeInstanceOf<CreateBillResult.SUCCESS>()
        with((response)) {
            secondMember.canView(bill) shouldBe true
        }

        if (dryRun) {
            verify(exactly = 0) {
                billEventPublisher.publish(any(), any())
            }
        } else {
            verify(exactly = 1) {
                billEventPublisher.publish(any(), ofType<FichaCompensacaoAdded>())
            }
        }
    }

    @Test
    fun `should create already paid bill when source is DDA and is not Dry Run`() {
        every {
            validationService.validate(any<BarCode>())
        } returns billValidationResponseAlreadyPaid

        every {
            billEventRepository.findLastBill(barCode = any(), any())
        } returns Either.Left(ItemNotFoundException("nao tenho a conta ainda!"))

        every {
            updateBillService.synchronizeBill(any<Bill>(), any())
        } returns
            SynchronizeBillResponse(
                BillSynchronizationStatus.BillStatusUpdated(BillStatus.ALREADY_PAID),
                SyncErrorMessages.GenericSyncErrorMessage("erro fake. já esta sincronizado"),
            )

        val createFichaDeCompensacaoRequest =
            CreateFichaDeCompensacaoRequest(
                description = "description fake",
                barcode = BarCode(number = FICHA_DE_COMPENSACAO_BARCODE, digitable = FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
                walletId = WalletId("RANDOM-WALLET-ID"),
                source = ActionSource.DDA(accountId = AccountId(ACCOUNT_ID)),
            )

        val response =
            fichaCompensacaoService.createFichaDeCompensacao(
                request = createFichaDeCompensacaoRequest,
                dryRun = false,
            )

        verify(exactly = 1) {
            updateBillService.storeEvent(any(), any<BillAdded>())
            updateBillService.synchronizeBill(any<Bill>(), any())
        }

        verify(exactly = 0) {
            updateBillService.publishEvent(any(), any<BillAdded>())
        }

        response.shouldBeInstanceOf<CreateBillResult.SUCCESS>()
    }

    @Test
    fun `should create bill(ficha de compensacao) when blockFeatureFlag is disabled and exists bill with same idNumber`() {
        every {
            featureConfiguration.blockDuplicateByIdNumber
        } returns false

        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns ItemNotFoundException("").left()

        every {
            billEventRepository.findLastBill(idNumber = any(), WalletId("RANDOM-WALLET-ID"))
        } returns
            Either.Right(
                Bill.build(
                    billAddedFicha.copy(
                        barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2),
                        idNumber = fichaRegisterData.idNumber,
                    ),
                ),
            )

        every {
            validationService.validate(any<BarCode>())
        } returns
            ArbiValidationResponse(
                billRegisterData = fichaRegisterData,
                paymentStatus = CIPSitTitPgto.SUCESSO.code,
            )

        every {
            possibleDuplicateBillService.check(any<Bill>())
        } returns listOf(PossibleDuplicate(BillId(BILL_ID_5), LocalDate.of(2022, 2, 17)))

        val response =
            fichaCompensacaoService.createFichaDeCompensacao(
                request = createFichaDeCompensacaoRequest,
                dryRun = true,
            )

        response.shouldBeInstanceOf<CreateBillResult.SUCCESS>()

        with(response) {
            warningCode shouldBe WarningCode.NONE
            possibleDuplicateBills shouldHaveSize 1
            possibleDuplicateBills.first().billId.value shouldBe BILL_ID_5
            possibleDuplicateBills.first().dueDate shouldBe LocalDate.of(2022, 2, 17)
            bill.description shouldBe "description fake"
            bill.subscriptionFee shouldBe false
        }
    }

    @Test
    fun `deve criar uma ficha de compensação com valor maior que 250 mil reais via DDA`() {
        every {
            validationService.validate(any<BarCode>())
        } returns
            ArbiValidationResponse(
                billRegisterData = fichaRegisterData.copy(amount = 250_000_01L, amountTotal = 250_000_01L),
                paymentStatus = CIPSitTitPgto.SUCESSO.code,
            )

        every {
            billEventRepository.findLastBill(barCode = any(), any())
        } returns Either.Left(ItemNotFoundException("nao tenho a conta ainda!"))

        val response =
            withEarlyAccess(AccountId(createFichaDeCompensacaoRequest.walletId.value)) {
                fichaCompensacaoService.createFichaDeCompensacao(
                    request = createFichaDeCompensacaoRequest.copy(source = ActionSource.DDA(accountId = AccountId(value = ""))),
                    dryRun = true,
                )
            }

        response.shouldBeInstanceOf<CreateBillResult.SUCCESS>()
        with(response) {
            warningCode shouldBe WarningCode.MAX_PAYMENT_LIMIT_EXCEEDED
            bill.status shouldBe BillStatus.NOT_PAYABLE
        }
    }

    @Test
    fun `deve resincronizar uma bill quando ela for reativada`() {
        every {
            validationService.validate(any<BarCode>())
        } returns
            ArbiValidationResponse(
                billRegisterData = fichaRegisterData.copy(amount = 250_000_01L, amountTotal = 250_000_01L, dueDate = LocalDate.now()),
                paymentStatus = CIPSitTitPgto.SUCESSO.code,
            )

        every {
            billEventRepository.findLastBill(barCode = any(), any())
        } returns
            Either.Right(
                Bill.build(
                    billAdded.copy(
                        actionSource = ActionSource.DDA(accountId = AccountId(ACCOUNT_ID_2)),
                        document = DOCUMENT_2,
                        dueDate = LocalDate.now(),
                    ),
                    billIgnored,
                ),
            )

        fichaCompensacaoService.createFichaDeCompensacao(
            request = createFichaDeCompensacaoRequest.copy(source = ActionSource.DDA(accountId = AccountId(ACCOUNT_ID_2))),
            dryRun = false,
        )

        verify(exactly = 1) { updateBillService.synchronizeBill(any<Bill>(), any()) }
    }

    @Test
    fun `deve criar uma ficha de compensação com valor 0 reais se a configuração permitir esse tipo`() {
        every {
            validationService.validate(any<BarCode>())
        } returns
            ArbiValidationResponse(
                billRegisterData = fichaRegisterData.copy(amount = 0L, amountTotal = 0L),
                paymentStatus = CIPSitTitPgto.SUCESSO.code,
            )

        every {
            billEventRepository.findLastBill(barCode = any(), any())
        } returns Either.Left(ItemNotFoundException("nao tenho a conta ainda!"))

        val response =
            withEarlyAccess(AccountId(createFichaDeCompensacaoRequest.walletId.value)) {
                fichaCompensacaoService.createFichaDeCompensacao(
                    request = createFichaDeCompensacaoRequest.copy(source = ActionSource.DDA(accountId = AccountId(value = ""))),
                    dryRun = true,
                )
            }

        response.shouldBeInstanceOf<CreateBillResult.SUCCESS>()
        with(response) {
            warningCode shouldBe WarningCode.ZERO_AMOUNT
            bill.status shouldBe BillStatus.NOT_PAYABLE
        }
    }

    @Test
    fun `deve aplicar enriquecimento ao criar a bill e seguir normalmente em caso de erro na execucao do enriquecimento`() {
        every {
            validationService.validate(any<BarCode>())
        } returns
            ArbiValidationResponse(
                billRegisterData = fichaRegisterData.copy(amount = 0L, amountTotal = 0L),
                paymentStatus = CIPSitTitPgto.SUCESSO.code,
            )

        every {
            billEventRepository.findLastBill(barCode = any(), any())
        } returns Either.Left(ItemNotFoundException("nao tenho a conta ainda!"))

        val service = FichaCompensacaoService(
            validationService = validationService,
            billEventRepository = billEventRepository,
            updateBillService = updateBillService,
            possibleDuplicateBillService = possibleDuplicateBillService,
            walletService = walletService,
            scheduleBillService = mockk(),
            billEventPublisher = billEventPublisher,
            featureConfiguration = featureConfiguration,
            blockEmptyAmountCreationService = blockEmptyAmountCreationService,
            enrichmentRules = listOf(
                mockk { every { execute(any()) } throws NoStackTraceException("error") },
                mockk {
                    every { execute(any()) } answers {
                        firstArg<FichaCompensacaoAdded>().copy(
                            description = "enriched description",
                        )
                    }
                },
                mockk {
                    every { execute(any()) } answers {
                        firstArg<FichaCompensacaoAdded>().copy(
                            description = "enhanced enriched description",
                        )
                    }
                },
                mockk {
                    every { execute(any()) } answers {
                        firstArg<FichaCompensacaoAdded>().copy(
                            brand = "Visa",
                        )
                    }
                },
            ),
        )

        val response = withEarlyAccess(AccountId(createFichaDeCompensacaoRequest.walletId.value)) {
            withoutLogs {
                service.createFichaDeCompensacao(
                    request = createFichaDeCompensacaoRequest.copy(source = ActionSource.DDA(accountId = AccountId(value = ""))),
                    dryRun = true,
                )
            }
        }

        response.shouldBeInstanceOf<CreateBillResult.SUCCESS>()
        with(response) {
            warningCode shouldBe WarningCode.ZERO_AMOUNT
            bill.status shouldBe BillStatus.NOT_PAYABLE
            bill.description shouldBe "enhanced enriched description"
            bill.brand shouldBe "Visa"
        }
    }

    @Test
    fun `deve usar overriddenDueDate quando passado`() {
        val originalDueDate = getLocalDate()
        val overriddenDueDate = originalDueDate.plusDays(5)

        every {
            validationService.validate(any<BarCode>())
        } returns
            ArbiValidationResponse(
                billRegisterData = fichaRegisterData.copy(dueDate = originalDueDate),
                paymentStatus = CIPSitTitPgto.SUCESSO.code,
            )

        every {
            billEventRepository.findLastBill(barCode = any(), any())
        } returns Either.Left(ItemNotFoundException("nao tenho a conta ainda!"))

        val response = fichaCompensacaoService.createFichaDeCompensacao(
            request = createFichaDeCompensacaoRequest,
            dryRun = true,
            dueDateToOverride = overriddenDueDate,
        )

        response.shouldBeInstanceOf<CreateBillResult.SUCCESS>()
        with(response) {
            bill.dueDate shouldBe overriddenDueDate
        }
    }
}