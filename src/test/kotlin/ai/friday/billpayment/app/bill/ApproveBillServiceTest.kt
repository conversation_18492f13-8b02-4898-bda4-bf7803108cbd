package ai.friday.billpayment.app.bill

import ai.friday.billpayment.EMAIL_ADDRESS
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.MailboxWalletDataRepository
import ai.friday.billpayment.app.mailbox.MailboxListType
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.fixture.UpdateBillStateFixture
import ai.friday.billpayment.integration.WalletFixture
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ApproveBillServiceTest {
    private lateinit var service: ApproveBillService

    private lateinit var eventRepository: BillEventRepository
    private lateinit var eventPublisher: BillEventPublisher
    private val mailboxWalletDataRepository = mockk<MailboxWalletDataRepository>(relaxUnitFun = true)
    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    @BeforeEach
    fun setUp() {
        eventRepository = mockk()
        eventPublisher = mockk()

        service = ApproveBillService(mockk(relaxed = true), eventRepository, eventPublisher, mailboxWalletDataRepository)
    }

    @Test
    fun `should return left error when getBill not work`() {
        every { eventRepository.getBill(any(), any()) } returns NoStackTraceException().left()

        val result = service.update(UpdateBillStateFixture.approve())

        result.isLeft() shouldBe true
        result.mapLeft {
            it.ex.shouldBeTypeOf<NoStackTraceException>()
        }

        verify { eventRepository.getBill(WalletId("123"), BillId("123")) }
        verify { eventPublisher wasNot called }
    }

    @Test
    fun `should return left error when member can not schedule bills`() {
        val bill = Bill.build(billAdded)

        every { eventRepository.getBill(any(), any()) } returns bill.right()

        val member = WalletFixture().founder.copy(
            permissions = WalletFixture().founder.permissions.copy(
                scheduleBills = BillPermission.NO_BILLS,
            ),
        )

        val result = service.update(UpdateBillStateFixture.approve(member = member))

        result.isLeft() shouldBe true
        result.mapLeft {
            it.ex.shouldBeTypeOf<MemberNotAllowedException>()
        }

        verify { eventRepository.getBill(WalletId("123"), BillId("123")) }
        verify { eventPublisher wasNot called }
    }

    @Test
    fun `deve aprovar sem atualizar a lista de permitidos`() {
        val bill = Bill.build(billAdded)

        every { eventRepository.getBill(any(), any()) } returns bill.right()
        every { eventPublisher.publish(any(), any()) } just runs

        val result = service.update(UpdateBillStateFixture.approve(member = wallet.founder))

        result.isRight() shouldBe true
        result.map {
            it shouldBe bill
        }

        verify {
            eventRepository.getBill(WalletId("123"), BillId("123"))
            eventPublisher.publish(
                bill,
                withArg { it.shouldBeTypeOf<BillApproved>() },
            )
        }

        verify(exactly = 0) {
            mailboxWalletDataRepository.add(wallet.id, MailboxListType.ALLOWED, EMAIL_ADDRESS)
        }
    }

    @Test
    fun `deve aprovar atualizando a lista de permitidos`() {
        val bill = Bill.build(billAdded.copy(walletId = wallet.id))

        every { eventRepository.getBill(any(), any()) } returns bill.right()
        every { eventPublisher.publish(any(), any()) } just runs

        val result = service.update(UpdateBillStateFixture.approve(member = wallet.founder, alwaysApproveSender = true))

        result.isRight() shouldBe true
        result.map {
            it shouldBe bill
        }

        verify { eventRepository.getBill(WalletId("123"), BillId("123")) }
        verify {
            eventPublisher.publish(
                bill,
                withArg { it.shouldBeTypeOf<BillApproved>() },
            )
            mailboxWalletDataRepository.add(wallet.id, MailboxListType.ALLOWED, EMAIL_ADDRESS)
        }
    }
}