package ai.friday.billpayment.app.bill.mailbox.rules

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.mailbox.MailboxSecurityValidationErrorLevel
import ai.friday.billpayment.app.bill.mailbox.MailboxValidateRequestFixture.validateRequest
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billDenied
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlin.test.fail
import org.junit.jupiter.api.Test

internal class UserIgnoredBillMailboxRuleTest {

    private val sender = "<EMAIL>"

    private val billEventRepository = mockk<BillEventRepository>(relaxed = true)

    private val rule = UserIgnoredBillMailboxRule(
        billEventRepository = billEventRepository,
    )

    @Test
    fun `deve retornar right se vier de um membro de carteira`() {
        val result = rule.execute(validateRequest(sender = sender, fromAccountId = AccountId("123")))
        result.isRight().shouldBeTrue()

        verify {
            billEventRepository wasNot Called
        }
    }

    @Test
    fun `deve retornar right se o codigo de barras não existir na carteira`() {
        every {
            billEventRepository.findLastBill(any<BarCode>(), any())
        } returns NoStackTraceException("fake").left()

        val result = rule.execute(validateRequest(sender = sender, fromAccountId = null))
        result.isRight().shouldBeTrue()
    }

    @Test
    fun `deve retornar right se o codigo de barras existir na carteira sem estar ignorado`() {
        every {
            billEventRepository.findLastBill(any<BarCode>(), any())
        } returns Bill.build(billAdded.copy(securityValidationResult = listOf("fake"))).right()

        val result = rule.execute(validateRequest(sender = sender, fromAccountId = null))
        result.isRight().shouldBeTrue()
    }

    @Test
    fun `deve retornar left se o codigo de barras existir na carteira e estiver ignorado`() {
        every {
            billEventRepository.findLastBill(any<BarCode>(), any())
        } returns Bill.build(billAdded.copy(securityValidationResult = listOf("fake")), billDenied).right()

        val result = rule.execute(validateRequest(sender = sender, fromAccountId = null)).map {
            fail("Deveria ter retornado left")
        }.getOrElse { it }

        result.reason shouldBe "BillAlreadyIgnored"
        result.level shouldBe MailboxSecurityValidationErrorLevel.BLOCK
    }
}