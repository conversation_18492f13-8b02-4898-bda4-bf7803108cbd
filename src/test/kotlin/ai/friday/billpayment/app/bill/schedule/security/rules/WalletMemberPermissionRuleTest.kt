package ai.friday.billpayment.app.bill.schedule.security.rules

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.schedule.security.ScheduleBillSecurityError
import ai.friday.billpayment.app.bill.schedule.security.ScheduleBillSecurityRequest
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.createPaymentMethodsDetailWithBalance
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID
import ai.friday.billpayment.integration.WalletFixture
import arrow.core.getOrElse
import io.kotest.assertions.fail
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.types.shouldBeTypeOf
import java.util.stream.Stream
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class WalletMemberPermissionRuleTest {

    private val rule = WalletMemberPermissionRule()

    @ParameterizedTest
    @MethodSource("unauthorizedMembers")
    fun `deve retornar erro quando o membro da carteira nao tiver permissao para agendar`(member: Member) {
        val details = createPaymentMethodsDetailWithBalance(
            paymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID),
            amount = billAdded.amount,
        )

        val bill = Bill.build(billAdded)

        val result = rule.execute(
            ScheduleBillSecurityRequest(
                bill = bill,
                member = member,
                paymentMethodDetails = details,
            ),
        )

        result.isLeft().shouldBeTrue()
        result.getOrElse {
            it.shouldBeTypeOf<ScheduleBillSecurityError.PermissionDenied>()
        }
    }

    @ParameterizedTest
    @MethodSource("authorizedMembers")
    fun `nao deve retornar erro quando o membro da carteira tiver permissao para agendar`(member: Member) {
        val details = createPaymentMethodsDetailWithBalance(
            paymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID),
            amount = billAdded.amount,
        )

        val bill = Bill.build(billAdded)

        val result = rule.execute(
            ScheduleBillSecurityRequest(
                bill = bill,
                member = member,
                paymentMethodDetails = details,
            ),
        )

        result.isRight().shouldBeTrue()
        result.getOrElse {
            fail("nao deve retornar erro quando o membro da carteira tiver permissao para agendar")
        }
    }

    companion object {

        private val walletFixture = WalletFixture()

        @JvmStatic
        fun unauthorizedMembers(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(walletFixture.assistant),
                Arguments.of(walletFixture.cantPayParticipant),
                Arguments.of(walletFixture.limitedParticipant.copy(accountId = AccountId("123"))),
                Arguments.of(walletFixture.ultraLimitedParticipant),
                Arguments.of(walletFixture.removedParticipant),
            )
        }

        @JvmStatic
        fun authorizedMembers(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(walletFixture.founder),
                Arguments.of(walletFixture.participant),
                Arguments.of(walletFixture.limitedParticipant.copy(accountId = (billAdded.actionSource as ActionSource.WalletActionSource).accountId!!)),
            )
        }
    }
}