package ai.friday.billpayment.app.bill

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.fixture.UpdateBillStateFixture
import ai.friday.billpayment.integration.WalletFixture
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class DenyBillServiceTest {
    private lateinit var service: DenyBillService

    private lateinit var eventRepository: BillEventRepository
    private lateinit var eventPublisher: BillEventPublisher

    @BeforeEach
    fun setUp() {
        eventRepository = mockk()
        eventPublisher = mockk()

        service = DenyBillService(mockk(relaxed = true), eventRepository, eventPublisher)
    }

    @Test
    fun `should return left error when getBill not work`() {
        every { eventRepository.getBill(any(), any()) } returns NoStackTraceException().left()

        val result = service.update(UpdateBillStateFixture.deny())

        result.isLeft() shouldBe true
        result.mapLeft {
            it.ex.shouldBeTypeOf<NoStackTraceException>()
        }

        verify { eventRepository.getBill(WalletId("123"), BillId("123")) }
        verify { eventPublisher wasNot called }
    }

    @Test
    fun `should return left error when member can not schedule bills`() {
        val bill = Bill.build(billAdded)

        every { eventRepository.getBill(any(), any()) } returns bill.right()

        val member = WalletFixture().founder.copy(
            permissions = WalletFixture().founder.permissions.copy(
                scheduleBills = BillPermission.NO_BILLS,
            ),
        )

        val result = service.update(UpdateBillStateFixture.deny(member = member))

        result.isLeft() shouldBe true
        result.mapLeft {
            it.ex.shouldBeTypeOf<MemberNotAllowedException>()
        }

        verify { eventRepository.getBill(WalletId("123"), BillId("123")) }
        verify { eventPublisher wasNot called }
    }

    @Test
    fun `deve negar sem colocar na lista de negados`() {
        val bill = Bill.build(billAdded)

        every { eventRepository.getBill(any(), any()) } returns bill.right()
        every { eventPublisher.publish(any(), any()) } just runs

        val result = service.update(UpdateBillStateFixture.deny(member = WalletFixture().founder))

        result.isRight() shouldBe true
        result.map {
            it shouldBe bill
        }

        verify {
            eventRepository.getBill(WalletId("123"), BillId("123"))
            eventPublisher.publish(
                bill,
                withArg { it.shouldBeTypeOf<BillDenied>() },
            )
        }
    }
}