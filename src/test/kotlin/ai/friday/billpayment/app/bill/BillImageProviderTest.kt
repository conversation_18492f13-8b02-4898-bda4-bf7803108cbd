package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.billAddedInvestment
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

class BillImageProviderTest {

    private val otherProvider: ImageUrlProvider<AccountId> = mockk() {
        every { urlProviderFor() } returns AccountId::class.java
    }
    private val goalProvider: ImageUrlProvider<GoalId> = mockk() {
        every { urlProviderFor() } returns GoalId::class.java
    }
    private val billImageProvider = BillImageProvider(listOf(otherProvider, goalProvider))

    private val bill = Bill.build(billAddedInvestment)

    @Test
    fun `deve retornar null se não houver provider`() {
        val billImageProvider = BillImageProvider(listOf(otherProvider))
        val response = billImageProvider.getBillImageUrl(bill)
        response.shouldBeNull()
        verify(exactly = 0) {
            otherProvider.getImageUrl(any())
        }
    }

    @Test
    fun `deve retornar null se houver provider e o provider retornar null`() {
        every {
            goalProvider.getImageUrl(bill.goalId!!)
        } returns null

        val response = billImageProvider.getBillImageUrl(bill)
        response.shouldBeNull()

        verify(exactly = 0) {
            otherProvider.getImageUrl(any())
        }

        verify {
            goalProvider.getImageUrl(bill.goalId!!)
        }
    }

    @Test
    fun `deve retornar a url se houver provider e o provider retornar algum valor`() {
        every {
            goalProvider.getImageUrl(bill.goalId!!)
        } returns "URL"

        val response = billImageProvider.getBillImageUrl(bill)
        response shouldBe "URL"

        verify(exactly = 0) {
            otherProvider.getImageUrl(any())
        }

        verify {
            goalProvider.getImageUrl(bill.goalId!!)
        }
    }
}