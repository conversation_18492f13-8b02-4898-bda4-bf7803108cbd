package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.duplication.PossibleDuplicateBillService
import ai.friday.billpayment.app.bill.duplication.rules.BankTransferRule
import ai.friday.billpayment.app.bill.duplication.rules.FichaCompensacaoRule
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.getInvoiceBill
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.BILL_ID_3
import ai.friday.billpayment.integration.BILL_ID_4
import ai.friday.billpayment.integration.BILL_ID_5
import ai.friday.billpayment.integration.BILL_ID_6
import ai.friday.billpayment.integration.DIGITABLE_LINE
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class PossibleDuplicateBillServiceTest {

    private val billRepositoryMock: BillRepository = mockk()

    private val possibleDuplicateBillService = PossibleDuplicateBillService(
        billRepository = billRepositoryMock,
        listOf(
            FichaCompensacaoRule(),
            BankTransferRule(),
        ),
    )

    @Test
    fun `should return an empty map when there is no possible duplicate bill`() {
        val bill1 = getInvoiceBill()

        val possibleDuplicateBills = possibleDuplicateBillService.check(listOf(bill1))

        possibleDuplicateBills.isEmpty() shouldBe true
    }

    @Test
    fun `should return that both bills are possible duplicates`() {
        val bill1 = getInvoiceBill().copy(effectiveDueDate = getLocalDate())
        val bill2 = bill1.copy(billId = BillId(BILL_ID_2))
        val bill3 = bill2.copy(billId = BillId(BILL_ID_3), effectiveDueDate = getLocalDate().plusDays(1))
        val bill4 = bill3.copy(billId = BillId(BILL_ID_4))
        val bill5 = bill4.copy(billId = BillId(BILL_ID_5), effectiveDueDate = getLocalDate().plusDays(2))
        val bill6 = bill5.copy(billId = BillId(BILL_ID_6), amountTotal = 946)

        val possibleDuplicateBills =
            possibleDuplicateBillService.check(listOf(bill1, bill2, bill3, bill4, bill5, bill6))

        possibleDuplicateBills.size shouldBe 4

        with(possibleDuplicateBills[bill1]) {
            this shouldNotBe null
            this!!.shouldHaveSize(1)
            this.first().billId shouldBe bill2.billId
            this.first().dueDate shouldBe bill2.effectiveDueDate
        }

        with(possibleDuplicateBills[bill2]) {
            this shouldNotBe null
            this!!.shouldHaveSize(1)
            this.first().billId shouldBe bill1.billId
            this.first().dueDate shouldBe bill1.effectiveDueDate
        }

        with(possibleDuplicateBills[bill3]) {
            this shouldNotBe null
            this!!.shouldHaveSize(1)
            this.first().billId shouldBe bill4.billId
            this.first().dueDate shouldBe bill4.effectiveDueDate
        }

        with(possibleDuplicateBills[bill4]) {
            this shouldNotBe null
            this!!.shouldHaveSize(1)
            this.first().billId shouldBe bill3.billId
            this.first().dueDate shouldBe bill3.effectiveDueDate
        }

        possibleDuplicateBills[bill5].shouldBeNull()
        possibleDuplicateBills[bill6].shouldBeNull()
    }

    @Test
    fun `should return that two bank transfers are NOT possible duplicates when they have different documents`() {
        val bill1 = getInvoiceBill().copy(billType = BillType.PIX, recipient = Recipient(name = "fulano", document = "***********"))
        val bill2 = bill1.copy(billId = BillId(BILL_ID_2), recipient = bill1.recipient!!.copy(document = "***********"))

        possibleDuplicateBillService.check(bill1, bill2).shouldBeFalse()
        possibleDuplicateBillService.check(bill2, bill1).shouldBeFalse()
    }

    @Test
    fun `should return that two bank transfers are possible duplicates when they are not tripix`() {
        val bill1 = getInvoiceBill().copy(billType = BillType.PIX, recipient = Recipient(name = "fulano", document = "***********"))
        val bill2 = bill1.copy(billId = BillId(BILL_ID_2))

        possibleDuplicateBillService.check(bill1, bill2).shouldBeTrue()
        possibleDuplicateBillService.check(bill2, bill1).shouldBeTrue()
    }

    @Test
    fun `should return that two bank transfers are NOT possible duplicates when they tripix`() {
        val bill1 = getInvoiceBill().copy(recipient = Recipient(name = "fulano", document = "***********"), tags = setOf(BillTag.ONBOARDING_TEST_PIX))
        val bill2 = bill1.copy(billId = BillId(BILL_ID_2))

        possibleDuplicateBillService.check(bill1, bill2).shouldBeFalse()
        possibleDuplicateBillService.check(bill2, bill1).shouldBeFalse()
    }

    @Test
    fun `should return that two bank transfers are NOT possible duplicates when they have different due date`() {
        val bill1 = getInvoiceBill().copy(effectiveDueDate = getLocalDate())
        val bill2 = bill1.copy(billId = BillId(BILL_ID_2), effectiveDueDate = getLocalDate().plusDays(1))

        possibleDuplicateBillService.check(bill1, bill2).shouldBeFalse()
        possibleDuplicateBillService.check(bill2, bill1).shouldBeFalse()
    }

    @Test
    fun `should return that two bank transfers are NOT possible duplicates when they have different amount`() {
        val bill1 = getInvoiceBill().copy(amountTotal = 1L)
        val bill2 = bill1.copy(billId = BillId(BILL_ID_2), amountTotal = 2L)

        possibleDuplicateBillService.check(bill1, bill2).shouldBeFalse()
        possibleDuplicateBillService.check(bill2, bill1).shouldBeFalse()
    }

    @Test
    fun `should return that two concessionarias are NOT possible duplicates always`() {
        val bill1 = getActiveBill().copy(billType = BillType.CONCESSIONARIA)
        val bill2 = bill1.copy(billId = BillId(BILL_ID_2))

        possibleDuplicateBillService.check(bill1, bill2).shouldBeFalse()
        possibleDuplicateBillService.check(bill2, bill1).shouldBeFalse()
    }

    @Test
    fun `should return that two FICHAS DE COMPENSACAO are possible duplicates when they have same digitable lines`() {
        val bill1 = getActiveBill().copy(billType = BillType.FICHA_COMPENSACAO)
        val bill2 = bill1.copy(billId = BillId(BILL_ID_2))

        possibleDuplicateBillService.check(bill1, bill2).shouldBeTrue()
        possibleDuplicateBillService.check(bill2, bill1).shouldBeTrue()
    }

    @Test
    fun `should return that two FICHAS DE COMPENSACAO are NOT possible duplicates when they have different digitable lines and idNumber NULL`() {
        val bill1 = getActiveBill().copy(
            billType = BillType.FICHA_COMPENSACAO,
            barCode = BarCode.ofDigitable(DIGITABLE_LINE),
            idNumber = null,
        )

        val bill2 = bill1.copy(
            billId = BillId(BILL_ID_2),
            barCode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2),
        )

        possibleDuplicateBillService.check(bill1, bill2).shouldBeFalse()
        possibleDuplicateBillService.check(bill2, bill1).shouldBeFalse()
    }

    @Test
    fun `should return that two FICHAS DE COMPENSACAO are NOT possible duplicates when they have different digitable lines and different idNumber`() {
        val bill1 = getActiveBill().copy(
            billType = BillType.FICHA_COMPENSACAO,
            barCode = BarCode.ofDigitable(DIGITABLE_LINE),
            idNumber = "1",
        )

        val bill2 = bill1.copy(
            billId = BillId(BILL_ID_2),
            barCode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2),
            idNumber = "2",
        )

        possibleDuplicateBillService.check(bill1, bill2).shouldBeFalse()
        possibleDuplicateBillService.check(bill2, bill1).shouldBeFalse()
    }

    @Test
    fun `should return that two FICHAS DE COMPENSACAO are possible duplicates when they have different digitable lines and same idNumber`() {
        val bill1 = getActiveBill().copy(
            billType = BillType.FICHA_COMPENSACAO,
            barCode = BarCode.ofDigitable(DIGITABLE_LINE),
            idNumber = "1",
        )

        val bill2 = bill1.copy(
            billId = BillId(BILL_ID_2),
            barCode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2),
        )

        possibleDuplicateBillService.check(bill1, bill2).shouldBeTrue()
        possibleDuplicateBillService.check(bill2, bill1).shouldBeTrue()
    }

    @Test
    fun `should return that two FICHAS DE COMPENSACAO are NOT possible duplicates when type is CREDIT CARD with different due dates`() {
        val bill1 = getActiveBill().copy(
            billType = BillType.FICHA_COMPENSACAO,
            fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO,
            barCode = BarCode.ofDigitable(DIGITABLE_LINE),
            idNumber = "1",
            effectiveDueDate = getLocalDate(),
        )

        val bill2 = bill1.copy(
            billId = BillId(BILL_ID_2),
            effectiveDueDate = getLocalDate().plusDays(1),
        )

        possibleDuplicateBillService.check(bill1, bill2).shouldBeFalse()
        possibleDuplicateBillService.check(bill2, bill1).shouldBeFalse()
    }

    @Test
    fun `should return that two FICHAS DE COMPENSACAO are possible duplicates when type is CREDIT CARD and they have same due date and same digitable line`() {
        val bill1 = getActiveBill().copy(
            billType = BillType.FICHA_COMPENSACAO,
            fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO,
            barCode = BarCode.ofDigitable(DIGITABLE_LINE),
            idNumber = "1",
            effectiveDueDate = getLocalDate(),
        )

        val bill2 = bill1.copy(
            billId = BillId(BILL_ID_2),
            idNumber = "2",
        )

        possibleDuplicateBillService.check(bill1, bill2).shouldBeTrue()
        possibleDuplicateBillService.check(bill2, bill1).shouldBeTrue()
    }

    @Test
    fun `should return that two FICHAS DE COMPENSACAO are possible duplicates when type is CREDIT CARD and they have same due date and same idNumber`() {
        val bill1 = getActiveBill().copy(
            billType = BillType.FICHA_COMPENSACAO,
            fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO,
            barCode = BarCode.ofDigitable(DIGITABLE_LINE),
            idNumber = "1",
            effectiveDueDate = getLocalDate(),
        )

        val bill2 = bill1.copy(
            billId = BillId(BILL_ID_2),
            barCode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2),
        )

        possibleDuplicateBillService.check(bill1, bill2).shouldBeTrue()
        possibleDuplicateBillService.check(bill2, bill1).shouldBeTrue()
    }

    @Test
    fun `should return that two FICHAS DE COMPENSACAO are NOT possible duplicates when type is CREDIT CARD and they have same due date but with different digitable lines and different idNumber`() {
        val bill1 = getActiveBill().copy(
            billType = BillType.FICHA_COMPENSACAO,
            fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO,
            barCode = BarCode.ofDigitable(DIGITABLE_LINE),
            idNumber = null,
            effectiveDueDate = getLocalDate(),
        )
        val bill2 = bill1.copy(
            billId = BillId(BILL_ID_2),
            barCode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2),
        )

        possibleDuplicateBillService.check(bill1, bill2).shouldBeFalse()
        possibleDuplicateBillService.check(bill2, bill1).shouldBeFalse()
    }

    @ParameterizedTest
    @EnumSource(
        BillStatus::class,
        mode = EnumSource.Mode.EXCLUDE,
        names = ["WAITING_APPROVAL", "ACTIVE", "PROCESSING", "PAID", "PAID_ON_PARTNER", "WAITING_BENEFICIARY_UPDATE"],
    )
    fun `should return that two bank transfers are NOT possible duplicates when they have status in ignored list`(
        billStatus: BillStatus,
    ) {
        val bill1 = getInvoiceBill()
        val bill2 = bill1.copy(billId = BillId(BILL_ID_2), status = billStatus)

        possibleDuplicateBillService.check(bill1, bill2).shouldBeFalse()
        possibleDuplicateBillService.check(bill2, bill1).shouldBeFalse()
    }

    @ParameterizedTest
    @EnumSource(
        BillStatus::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["WAITING_APPROVAL", "ACTIVE", "PROCESSING", "PAID", "PAID_ON_PARTNER"],
    )
    fun `should return that two bank transfers are possible duplicates when they have a allowed status`(billStatus: BillStatus) {
        val bill1 = getInvoiceBill()
        val bill2 = bill1.copy(billId = BillId(BILL_ID_2), status = billStatus)

        possibleDuplicateBillService.check(bill1, bill2).shouldBeTrue()
        possibleDuplicateBillService.check(bill2, bill1).shouldBeTrue()
    }

    @Test
    fun `should return that two bank transfers are NOT possible duplicates when they are in PAID status`() {
        val bill1 = getInvoiceBill().copy(status = BillStatus.PAID)
        val bill2 = bill1.copy(billId = BillId(BILL_ID_2))

        possibleDuplicateBillService.check(bill1, bill2).shouldBeFalse()
        possibleDuplicateBillService.check(bill2, bill1).shouldBeFalse()
    }

    @Test
    fun `should consider bill duplicate`() {
        val bill = Bill.build(billAddedFicha.copy(recipient = Recipient("Teste")))

        val existingBill = getActiveBill(
            walletId = bill.walletId,
            accountId = AccountId(bill.walletId.value),
            billId = BillId(BILL_ID_2),
            dueDate = bill.dueDate,
            effectiveDueDate = bill.effectiveDueDate,
            fichaCompensacaoType = bill.fichaCompensacaoType,
            recipient = bill.recipient!!,
            payerDocument = bill.payerDocument()!!,
        ).copy(
            billType = bill.billType,
            barCode = bill.barcode,
        )

        every { billRepositoryMock.findByWallet(any()) } returns listOf(existingBill)

        val result = possibleDuplicateBillService.check(bill)

        result.size shouldBe 1
    }

    @Test
    fun `should not consider IGNORED bills when checking for duplicates`() {
        val bill = Bill.build(billAddedFicha.copy(recipient = Recipient("Teste")))

        val existingBill = getActiveBill(
            walletId = bill.walletId,
            accountId = AccountId(bill.walletId.value),
            billId = BillId(BILL_ID_2),
            dueDate = bill.dueDate,
            effectiveDueDate = bill.effectiveDueDate,
            fichaCompensacaoType = bill.fichaCompensacaoType,
            recipient = bill.recipient!!,
            payerDocument = bill.payerDocument()!!,
        ).copy(
            billType = bill.billType,
            barCode = bill.barcode,
            status = BillStatus.IGNORED,
        )

        every { billRepositoryMock.findByWallet(any()) } returns listOf(existingBill)

        val result = possibleDuplicateBillService.check(bill)

        result.size shouldBe 0
    }
}