package ai.friday.billpayment.app.bill.mailbox

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.DIGITABLE_LINE
import ai.friday.billpayment.integration.WALLET_ID
import io.via1.communicationcentre.app.receipt.Action
import io.via1.communicationcentre.app.receipt.Receipt
import io.via1.communicationcentre.app.receipt.Status
import io.via1.communicationcentre.app.receipt.Verdict
import java.time.LocalDate

object MailboxValidateRequestFixture {
    fun validateRequest(
        sender: String,
        walletId: WalletId = WalletId(WALLET_ID),
        barCode: BarCode = BarCode.ofDigitable(DIGITABLE_LINE),
        dueDate: LocalDate? = null,
        receipt: Receipt? = null,
        fromAccountId: AccountId? = null,
    ) = MailboxValidateRequest(
        from = EmailAddress(sender),
        walletId = walletId,
        barCode = barCode,
        dueDate = dueDate,
        receipt = receipt,
        fromAccountId = fromAccountId,
    )

    fun receipt(spfStatus: Status = Status.PASS) = Receipt(
        "", 0, "", emptyList(), verdict(), verdict(), verdict(spfStatus), verdict(), verdict(),
        Action("", "", "", "", ""),
    )

    private fun verdict(status: Status = Status.PASS) = Verdict(status)
}