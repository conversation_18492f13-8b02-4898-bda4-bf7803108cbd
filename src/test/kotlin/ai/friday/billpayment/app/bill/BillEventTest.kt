package ai.friday.billpayment.app.bill

import ai.friday.billpayment.adapters.dynamodb.BillEventDetailsEntity
import ai.friday.billpayment.adapters.dynamodb.toBillEvent
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import org.junit.jupiter.api.Test

class BillEventTest {

    @Test
    fun `deve converter o json string antigo utilizando a classe nova`() {
        val oldJsonString = """{"@type":"BillPaid","billId":{"value":"BILL-10a7620d-de10-4518-aa79-55374f869557"},"created":1741715356136,"walletId":{"value":"WALLET-ce5eec17-b164-4da2-b879-4917bb19c487"},"acquirer":null,"acquirerTid":null,"transactionId":null,"actionSource":{"@type":"System"},"pixKeyDetails":null,"eventType":"PAID"}"""
        val billEvent = parseObjectFrom<BillEventDetailsEntity>(oldJsonString).toBillEvent()

        billEvent.shouldBeTypeOf<BillPaid>()
        billEvent.syncReceipt shouldBe false
    }
}