package ai.friday.billpayment.app.bill.schedule

import DynamoDBUtils
import ai.friday.billpayment.adapters.bill.DefaultBillEventPublisher
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillPaymentScheduled
import ai.friday.billpayment.app.bill.BillPaymentScheduledBalanceInfo
import ai.friday.billpayment.app.bill.BillPaymentScheduledCreditCardInfo
import ai.friday.billpayment.app.bill.BillPaymentScheduledExternalPayment
import ai.friday.billpayment.app.bill.BillPaymentScheduledInfo
import ai.friday.billpayment.app.bill.BillPaymentScheduledWithMultiple
import ai.friday.billpayment.app.bill.InvalidSchedulePaymentAmount
import ai.friday.billpayment.app.bill.MemberNotAllowedException
import ai.friday.billpayment.app.bill.schedule.security.ScheduleBillSecurityError
import ai.friday.billpayment.app.bill.schedule.security.ScheduleBillSecurityService
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ScheduledBillPaymentService
import ai.friday.billpayment.app.payment.MultiplePaymentMethodsDetail
import ai.friday.billpayment.app.payment.PaymentMethodsDetail
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithExternalPayment
import ai.friday.billpayment.app.payment.PaymentSchedulingService
import ai.friday.billpayment.app.payment.ScheduleStrategy
import ai.friday.billpayment.app.payment.ScheduleTo
import ai.friday.billpayment.app.payment.ScheduledBill
import ai.friday.billpayment.app.payment.pinCode.PinCodeService
import ai.friday.billpayment.balance
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.creditCard
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID_2
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID_3
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDateTime
import java.time.ZonedDateTime
import kotlin.reflect.KClass
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.opentest4j.AssertionFailedError

class ScheduleBillServiceIntegrationTest {

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val enhancedClient = DynamoDBUtils.setupDynamoDB()
    private val billEventDAO = BillEventDynamoDAO(enhancedClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val billEventDbRepository = BillEventDBRepository(
        billEventDAO,
        uniqueConstraintDAO,
        mockk(),
        transactionDynamo,
    )
    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(enhancedClient),
        refundedClient = RefundedBillDynamoDAO(enhancedClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(enhancedClient),
    )

    private val billEventPublisher = DefaultBillEventPublisher(
        eventRepository = billEventDbRepository,
        billRepository = billRepository,
        eventPublisher = mockk(relaxed = true),
    )
    private val accountServiceMock: AccountService = mockk()
    private val paymentSchedulingService: PaymentSchedulingService = mockk(relaxed = true)
    private val scheduledBillPaymentService: ScheduledBillPaymentService = mockk(relaxed = true)
    private val lockProvider: InternalLock = mockk(relaxed = true)

    private val scheduleBillSecurityService = mockk<ScheduleBillSecurityService>() {
        every { canSchedule(any()) } returns Unit.right()
    }

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet(id = billAdded.walletId)

    private lateinit var scheduleBillService: ScheduleBillService

    private val mockedPinCodeService: PinCodeService = mockk(relaxed = true)

    @BeforeEach
    fun setup() {
        createBillEventTable(dynamoDB)
        createBillPaymentTable(dynamoDB)

        scheduleBillService = ScheduleBillService(
            billEventRepository = billEventDbRepository,
            accountService = accountServiceMock,
            paymentSchedulingService = paymentSchedulingService,
            scheduledBillPaymentServiceProvider = mockk() {
                every {
                    get()
                } returns scheduledBillPaymentService
            },
            lockProvider = lockProvider,
            billInstrumentationService = mockk(relaxUnitFun = true),
            billEventPublisher = billEventPublisher,
            scheduleBillSecurityService = scheduleBillSecurityService,
            batchSchedulePublisher = mockk(relaxUnitFun = true),
            walletRepository = mockk(relaxUnitFun = true),
            isBatchScheduleNotificationEnabled = false,
            pinCodeService = mockedPinCodeService,
        )

        scheduleBillService.tedLimitTime = "17:00"
    }

    //    @Disabled // TODO: Remover após fase inicial de habilitar cartoes novamente
    @ParameterizedTest
    @MethodSource("allSchedulePaymentDetails")
    fun `deve agendar uma conta com todos os tipos de detalhes disponiveis`(
        paymentMethodsDetail: PaymentMethodsDetail,
        accountPaymentMethods: List<AccountPaymentMethod>,
        expectedInfoData: BillPaymentScheduledInfo,
        shouldScheduleForPayment: Boolean,
    ) {
        val now = ZonedDateTime.of(LocalDateTime.of(2021, 9, 9, 12, 0), brazilTimeZone)
        val event = billAdded.copy(created = now.minusSeconds(10).toInstant().toEpochMilli())

        val bill = Bill.build(event)
        billEventDbRepository.save(event)
        billRepository.save(bill)

        accountPaymentMethods.forEach { accountPaymentMethods ->
            every {
                accountServiceMock.findAccountPaymentMethodByIdAndAccountId(
                    accountPaymentMethods.id,
                    wallet.founder.accountId,
                )
            } answers {
                accountPaymentMethods
            }
        }

        withGivenDateTime(now.withHour(12)) {
            val response = scheduleBillService.schedulePayment(
                paymentWallet = wallet,
                bill = bill,
                member = wallet.founder,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
                scheduleStrategy = ScheduleStrategy.ofAsap(),
                paymentMethodsDetail = paymentMethodsDetail,
            )
            response.isRight() shouldBe true
            response.map {
                it.schedule.shouldNotBeNull().date shouldBe now.toLocalDate()
            }
        }

        if (shouldScheduleForPayment) {
            val slotScheduleBill = slot<ScheduledBill>()
            verify {
                paymentSchedulingService.schedule(capture(slotScheduleBill))
            }
            with(slotScheduleBill.captured) {
                this.scheduledDate shouldBe now.toLocalDate()
                this.scheduleTo shouldBe ScheduleTo.ASAP
                this.paymentMethodsDetail shouldBe paymentMethodsDetail
            }
        } else {
            verify(exactly = 0) {
                paymentSchedulingService.schedule(any())
            }
        }

        val scheduledBill = billEventDbRepository.getBill(bill.walletId, bill.billId).getOrElse {
            TODO("DEVERIA FUNCIONAR")
        }
        scheduledBill.schedule.shouldNotBeNull().date shouldBe now.toLocalDate()
        val lastEvent = scheduledBill.history.last() as BillPaymentScheduled
        lastEvent.scheduledDate shouldBe now.toLocalDate()
        val infoData = lastEvent.infoData.shouldNotBeNull()
        infoData shouldBe expectedInfoData
    }

    @Test
    fun `nao deve agendar uma conta se o valor liquido nao for igual ao da bill`() {
        val now = ZonedDateTime.of(LocalDateTime.of(2021, 9, 9, 12, 0), brazilTimeZone)
        val event = billAdded.copy(created = now.minusSeconds(10).toInstant().toEpochMilli())

        val bill = Bill.build(event)
        billEventDbRepository.save(event)
        billRepository.save(bill)

        every {
            accountServiceMock.findAccountPaymentMethodByIdAndAccountId(
                AccountPaymentMethodId(PAYMENT_METHOD_ID_2),
                wallet.founder.accountId,
            )
        } answers {
            balance.copy(id = AccountPaymentMethodId(PAYMENT_METHOD_ID_2))
        }

        withGivenDateTime(now.withHour(12)) {
            val response = scheduleBillService.schedulePayment(
                paymentWallet = wallet,
                bill = bill,
                member = wallet.founder,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
                scheduleStrategy = ScheduleStrategy.ofAsap(),
                paymentMethodsDetail = PaymentMethodsDetailWithBalance(
                    amount = billAdded.amountTotal - 1L,
                    paymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_2),
                    calculationId = "calculationIdBalance",
                ),
            )
            response.isLeft() shouldBe true
            response.mapLeft {
                it.shouldBeTypeOf<InvalidSchedulePaymentAmount>()
            }
        }

        verify(exactly = 0) {
            paymentSchedulingService.schedule(any())
        }
        val notScheduledBill = billEventDbRepository.getBill(bill.walletId, bill.billId).getOrElse {
            TODO("DEVERIA FUNCIONAR")
        }
        notScheduledBill.schedule.shouldBeNull()
    }

    @Test
    fun `nao deve agendar uma conta quando nao passar nas regras de seguranca`() {
        val now = ZonedDateTime.of(LocalDateTime.of(2021, 9, 9, 12, 0), brazilTimeZone)
        val event = billAdded.copy(created = now.minusSeconds(10).toInstant().toEpochMilli())

        val bill = Bill.build(event)
        billEventDbRepository.save(event)
        billRepository.save(bill)

        every {
            accountServiceMock.findAccountPaymentMethodByIdAndAccountId(
                AccountPaymentMethodId(PAYMENT_METHOD_ID_2),
                wallet.founder.accountId,
            )
        } answers {
            balance.copy(id = AccountPaymentMethodId(PAYMENT_METHOD_ID_2))
        }

        every {
            scheduleBillSecurityService.canSchedule(any())
        } returns ScheduleBillSecurityError.PermissionDenied("NotAllowed").left()

        withGivenDateTime(now.withHour(12)) {
            val response = scheduleBillService.schedulePayment(
                paymentWallet = wallet,
                bill = bill,
                member = wallet.founder,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
                scheduleStrategy = ScheduleStrategy.ofAsap(),
                paymentMethodsDetail = PaymentMethodsDetailWithBalance(
                    amount = billAdded.amountTotal,
                    paymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_2),
                    calculationId = "calculationIdBalance",
                ),
            )
            response.isLeft() shouldBe true
            response.mapLeft {
                it.shouldBeTypeOf<MemberNotAllowedException>()
            }
        }

        verify(exactly = 0) {
            paymentSchedulingService.schedule(any())
        }
        val notScheduledBill = billEventDbRepository.getBill(bill.walletId, bill.billId).getOrElse {
            fail("DEVERIA FUNCIONAR")
        }
        notScheduledBill.schedule.shouldBeNull()
    }

    companion object {

        @JvmStatic
        fun allSchedulePaymentDetails(): List<Arguments> {
            val paymentMethodsDetailWithBalance = PaymentMethodsDetailWithBalance(
                amount = billAdded.amountTotal,
                paymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_2),
                calculationId = "calculationIdBalance",
            )
            val balance = balance.copy(id = AccountPaymentMethodId(PAYMENT_METHOD_ID_2))
            val billPaymentScheduledBalanceInfo = BillPaymentScheduledBalanceInfo(
                amount = billAdded.amountTotal,
                paymentMethodId = PAYMENT_METHOD_ID_2,
                calculationId = "calculationIdBalance",
            )

            val paymentMethodsDetailWithCreditCard = PaymentMethodsDetailWithCreditCard(
                netAmount = billAdded.amountTotal,
                paymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_3),
                feeAmount = 654,
                installments = 2,
                fee = 4.0,
                calculationId = "calculationId",
            )
            val creditCard = creditCard.copy(id = AccountPaymentMethodId(PAYMENT_METHOD_ID_3))
            val billPaymentScheduledCreditCardInfo = BillPaymentScheduledCreditCardInfo(
                netAmount = billAdded.amountTotal,
                paymentMethodId = PAYMENT_METHOD_ID_3,
                feeAmount = 654,
                installments = 2,
                fee = 4.0,
                calculationId = "calculationId",
            )

            val elements = listOf(
                Arguments.of(
                    paymentMethodsDetailWithBalance,
                    listOf(balance),
                    billPaymentScheduledBalanceInfo,
                    true,
                ),
                Arguments.of(
                    PaymentMethodsDetailWithExternalPayment(
                        providerName = AccountProviderName.ME_POUPE,
                    ),
                    emptyList<AccountPaymentMethod>(),
                    BillPaymentScheduledExternalPayment(
                        providerName = AccountProviderName.ME_POUPE.name,
                    ),
                    false,
                ),
                Arguments.of(
                    paymentMethodsDetailWithCreditCard,
                    listOf(creditCard),
                    billPaymentScheduledCreditCardInfo,
                    true,
                ),
                Arguments.of(
                    MultiplePaymentMethodsDetail(
                        methods = listOf(
                            paymentMethodsDetailWithBalance.copy(amount = paymentMethodsDetailWithBalance.amount - 2L),
                            paymentMethodsDetailWithCreditCard.copy(netAmount = 2L),
                        ),
                    ),
                    listOf(balance, creditCard),
                    BillPaymentScheduledWithMultiple(
                        methods = listOf(
                            billPaymentScheduledBalanceInfo.copy(amount = billPaymentScheduledBalanceInfo.amount - 2L),
                            billPaymentScheduledCreditCardInfo.copy(netAmount = 2L),
                        ),
                    ),
                    true,
                ),
            )

            val detailsUsed = elements.map {
                (it.get()[0] as PaymentMethodsDetail)
            }

            fun checkLeafSealedSubclassIsUsed(sealedSubclass: KClass<out PaymentMethodsDetail>) {
                if (sealedSubclass.sealedSubclasses.isNotEmpty()) {
                    sealedSubclass.sealedSubclasses.forEach { checkLeafSealedSubclassIsUsed(it) }
                } else {
                    val has = detailsUsed.any { it::class.simpleName == sealedSubclass.simpleName }
                    if (!has) {
                        throw AssertionFailedError("${sealedSubclass.simpleName} should be in test case")
                    }
                }
            }

            PaymentMethodsDetail::class.sealedSubclasses.forEach { checkLeafSealedSubclassIsUsed(it) }
            return elements
        }
    }
}