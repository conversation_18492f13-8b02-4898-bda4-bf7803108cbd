package ai.friday.billpayment.app.bill.mailbox

import ai.friday.billpayment.app.bill.MailboxAddBillError
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.equality.shouldBeEqualToComparingFields
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifyOrder
import org.junit.jupiter.api.Test

class MailboxSecurityServiceTest {
    @Test
    fun `should return empty when there are no rules to be validated`() {
        val service = MailboxSecurityService(rules = emptyList())

        val result = service.execute(mockk(relaxed = true))

        result shouldBe emptyList()
    }

    @Test
    fun `should return empty when rule executed passes`() {
        val rule = mockk<MailboxSecurityRule>()

        every { rule.getPriority() } returns 10
        every { rule.execute(any()) } returns Unit.right()

        val service = MailboxSecurityService(rules = listOf(rule))

        val result = service.execute(mockk(relaxed = true))

        result shouldBe emptyList()

        verify { rule.execute(any()) }
    }

    @Test
    fun `should execute multiple rules with success`() {
        val rule1 = mockk<MailboxSecurityRule>()
        val rule2 = mockk<MailboxSecurityRule>()

        every { rule1.getPriority() } returns 20
        every { rule2.getPriority() } returns 10

        every { rule1.execute(any()) } returns Unit.right()
        every { rule2.execute(any()) } returns Unit.right()

        val service = MailboxSecurityService(rules = listOf(rule1, rule2))

        val result = service.execute(mockk(relaxed = true))

        result shouldBe emptyList()

        verifyOrder {
            rule2.execute(any())
            rule1.execute(any())
        }
    }

    @Test
    fun `should execute multiple rules and return any error that happens`() {
        val rule = mockk<MailboxSecurityRule>()

        every { rule.getPriority() } returns 10 andThen 20 andThen 30

        val blockResult = MailboxAddBillError.MailboxSecurityValidationError(
            "block reason",
            MailboxSecurityValidationErrorLevel.BLOCK,
        ).left()

        val warnResult = MailboxAddBillError.MailboxSecurityValidationError(
            "warn reason",
            MailboxSecurityValidationErrorLevel.WARN,
        ).left()

        every {
            rule.execute(any())
        } returns warnResult andThen blockResult andThen Unit.right()

        val service = MailboxSecurityService(rules = listOf(rule, rule, rule))

        val result = service.execute(mockk(relaxed = true))

        result[0] shouldBeEqualToComparingFields MailboxAddBillError.MailboxSecurityValidationError(
            "warn reason",
            MailboxSecurityValidationErrorLevel.WARN,
        )

        result[1] shouldBeEqualToComparingFields MailboxAddBillError.MailboxSecurityValidationError(
            "block reason",
            MailboxSecurityValidationErrorLevel.BLOCK,
        )

        verify(exactly = 2) { rule.execute(any()) }
    }
}