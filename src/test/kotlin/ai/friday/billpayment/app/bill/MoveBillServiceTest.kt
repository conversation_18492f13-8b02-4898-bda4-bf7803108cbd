package ai.friday.billpayment.app.bill

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DDABillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DDADbRepository
import ai.friday.billpayment.adapters.dynamodb.DDADynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.integrations.DDARepository
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.billAddedInvestment
import ai.friday.billpayment.billCategoryAdded
import ai.friday.billpayment.billIgnored
import ai.friday.billpayment.billMoved
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billRegisterUpdatedAlreadyPaid
import ai.friday.billpayment.billRegisterUpdatedNotPayable
import ai.friday.billpayment.fichaCompensacaoCreditCardAdded
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.DIGITABLE_LINE
import ai.friday.billpayment.integration.ELECTRICITY_AND_GAS_BARCODE_LINE
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.PREFECTURE_BARCODE_LINE
import ai.friday.billpayment.integration.TELECOM_BARCODE_LINE
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.toMember
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.pixAdded
import arrow.core.getOrElse
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import java.util.UUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail

internal class MoveBillServiceTest {
    private val service =
        MoveBillService(
            updateBillService = updateBillService,
            walletService = walletService,
            billEventRepository = billEventRepository,
            ddaRepository = ddaRepository,
            recurrenceRepository = mockk(relaxed = true),
            billRepository = billRepository,
            lockProvider = lockProvider,
            accountRepository = accountRepository,
        )

    @BeforeEach
    fun beforeEach() = Setup.beforeEach()

    @Test
    fun `should grant visibility when try to move a bill that already exists in the destination wallet but user does not see it`() {
        val founderWallet =
            walletFixture.buildWallet(
                "carteira founder",
                id = WalletId("WALLET-FOUNDER"),
                walletFounder =
                ACCOUNT.copy(
                    accountId = AccountId("ACCOUNT-FOUNDER"),
                    document = "***********",
                ).toMember(
                    type = MemberType.FOUNDER,
                    permissions = MemberPermissions.of(MemberType.FOUNDER),
                ),
                otherMembers =
                listOf(
                    walletFixture.ultraLimitedParticipant,
                ),
            )

        val ultraLimitedAccount = walletFixture.ultraLimitedParticipantAccount

        val limitedParticipantWallet =
            walletFixture.buildWallet(
                "carteira limited",
                id = WalletId("WALLET-LIMITED"),
                walletFounder =
                ultraLimitedAccount.toMember(
                    type = MemberType.FOUNDER,
                    permissions = MemberPermissions.of(MemberType.FOUNDER),
                ),
            )

        accountRepository.save(ultraLimitedAccount)

        walletRepository.save(founderWallet)
        walletRepository.save(limitedParticipantWallet)

        val billIdToMove = BillId(BILL_ID_2)

        listOf(
            fichaCompensacaoCreditCardAdded.copy(walletId = founderWallet.id),
            fichaCompensacaoCreditCardAdded.copy(billId = billIdToMove, walletId = limitedParticipantWallet.id),
        ).forEach {
            billEventRepository.save(it)
            billRepository.save(Bill.build().apply(it))
        }

        val result =
            service.moveBills(
                ultraLimitedAccount.accountId,
                limitedParticipantWallet.id,
                founderWallet.id,
                listOf(billIdToMove),
            ).getOrElse {
                fail("should not fail")
            }

        verify(exactly = 1) {
            updateBillService.publishEvent(any(), any<BillPermissionUpdated>())
        }

        result.size shouldBe 1
        val moveBillResult = result.first()
        moveBillResult.moved shouldBe false
        moveBillResult.reason shouldBe MoveBillErrorReason.BILL_ALREADY_EXISTS
    }

    @Test
    fun `nao deve mover bills do tipo investimento`() {
        listOf(
            billAddedInvestment.copy(walletId = originWallet.id),
        ).forEach {
            billEventRepository.save(it)
            billRepository.save(Bill.build().apply(it))
        }

        billRepository.findByWallet(destinationWallet.id).size shouldBe 0
        billRepository.findByWallet(originWallet.id).also { it.size shouldBe BILLS_COUNT + 1 }

        service.moveBills(
            accountId = ACCOUNT.accountId,
            originWalletId = originWallet.id,
            destinationWalletId = destinationWallet.id,
            billIds = listOf(BillId("BILL-INVESTMENT-BILL-1")),
        ).map {
            fail("nao deveria ter movido")
        }

        billRepository.findByWallet(originWallet.id)
            .filter { bill -> bill.status == BillStatus.MOVED }.size shouldBe 0

        billRepository.findByWallet(destinationWallet.id).size shouldBe 0
    }

    @Test
    fun `não deveria mover a categoria junto com a bill para a nova wallet`() {
        val founderWallet =
            walletFixture.buildWallet(
                "carteira founder",
                id = WalletId("WALLET-FOUNDER"),
                walletFounder =
                ACCOUNT.copy(
                    accountId = AccountId("ACCOUNT-FOUNDER"),
                    document = "***********",
                ).toMember(
                    type = MemberType.FOUNDER,
                    permissions = MemberPermissions.of(MemberType.FOUNDER),
                ),
                otherMembers =
                listOf(
                    walletFixture.ultraLimitedParticipant,
                ),
            )

        val ultraLimitedAccount = walletFixture.ultraLimitedParticipantAccount

        val limitedParticipantWallet =
            walletFixture.buildWallet(
                "carteira limited",
                id = WalletId("WALLET-LIMITED"),
                walletFounder =
                ultraLimitedAccount.toMember(
                    type = MemberType.FOUNDER,
                    permissions = MemberPermissions.of(MemberType.FOUNDER),
                ),
            )

        accountRepository.save(ultraLimitedAccount)

        walletRepository.save(founderWallet)
        walletRepository.save(limitedParticipantWallet)

        val billIdToMove = BillId(BILL_ID_2)

        listOf(
            fichaCompensacaoCreditCardAdded.copy(billId = billIdToMove, walletId = limitedParticipantWallet.id),
            billCategoryAdded.copy(
                billId = billIdToMove,
                walletId = limitedParticipantWallet.id,
                categoryId = PFMCategoryId(value = "2"),
            ),
        ).also { billRepository.save(Bill.build(it.first(), it.last())) }
            .forEach { event -> billEventRepository.save(event) }

        val result =
            service.moveBills(
                ultraLimitedAccount.accountId,
                limitedParticipantWallet.id,
                founderWallet.id,
                listOf(billIdToMove),
            ).getOrElse {
                fail("should not fail")
            }

        result.size shouldBe 1
        val moveBillResult = result.first()
        moveBillResult.moved shouldBe true
        moveBillResult.billId shouldBe billIdToMove

        billRepository.findByWallet(founderWallet.id).first().also { bill ->
            bill.shouldNotBeNull()
            bill.categoryId.shouldBeNull()
        }
    }

    @Test
    fun `should move all active bills from wallet A to wallet B`() {
        billRepository.findByWallet(destinationWallet.id).size shouldBe 0
        val expectedBills = BILLS_COUNT - INACTIVE_BILLS_COUNT

        val originBills =
            billRepository.findByWalletAndStatus(originWallet.id, BillStatus.ACTIVE).also { bills ->
                bills.filter { it.status == BillStatus.ACTIVE }.size shouldBe expectedBills
            }

        service.moveAllActiveBills(originWalletId = originWallet.id, destinationWalletId = destinationWallet.id).also {
            it.shouldBeTypeOf<MoveAllBillsResult.Finished>()
            it.successes.size shouldBe expectedBills

            billRepository.findByWallet(originWallet.id)
                .filter { bill -> bill.status == BillStatus.MOVED }.size shouldBe expectedBills

            it.successes.map { success -> success.first }
                .shouldContainExactlyInAnyOrder(originBills.map { bill -> bill.billId })

            val destinationBills =
                billRepository.findByWallet(destinationWallet.id).also { bills ->
                    bills.filter { bill -> bill.status == BillStatus.ACTIVE }.size shouldBe expectedBills
                }

            it.successes.map { success -> success.second }
                .shouldContainExactlyInAnyOrder(destinationBills.map { bill -> bill.billId })
        }
    }

    @Test
    fun `should move all existent bills from wallet A to wallet B`() {
        billRepository.findByWallet(destinationWallet.id).size shouldBe 0

        billRepository.findByWallet(originWallet.id)
            .also { it.size shouldBe BILLS_COUNT }

        service.moveAllBills(originWalletId = originWallet.id, destinationWalletId = destinationWallet.id).also {
            it.shouldBeTypeOf<MoveAllBillsResult.Finished>()
            it.successes.size shouldBe BILLS_COUNT

            billRepository.findByWallet(originWallet.id)
                .filter { bill -> bill.status == BillStatus.MOVED }.size shouldBe BILLS_COUNT

            billRepository.findByWallet(destinationWallet.id)
                .filter { bill -> bill.status == BillStatus.ACTIVE }.run {
                    size shouldBe BILLS_COUNT - INACTIVE_BILLS_COUNT
                }
        }
    }

    @Test
    fun `should move all non active bills from wallet A to wallet B and keep their statuses`() {
        billRepository.findByWallet(destinationWallet.id).size shouldBe 0

        billRepository.findByWallet(originWallet.id)
            .filter { bill -> bill.status != BillStatus.ACTIVE }
            .size shouldBe INACTIVE_BILLS_COUNT

        service.moveAllBills(originWalletId = originWallet.id, destinationWalletId = destinationWallet.id).also {
            it.shouldBeTypeOf<MoveAllBillsResult.Finished>()
            it.successes.size shouldBe BILLS_COUNT

            billRepository.findByWallet(destinationWallet.id)
                .filter { bill -> bill.status != BillStatus.ACTIVE }.let { bills ->
                    bills.size shouldBe INACTIVE_BILLS_COUNT

                    bills.first { bill -> bill.barCode?.digitable == alreadyPaidBarcode.digitable }.status shouldBe BillStatus.ALREADY_PAID
                    bills.first { bill -> bill.barCode?.digitable == ignoredBarcode.digitable }.status shouldBe BillStatus.IGNORED
                    bills.first { bill -> bill.barCode?.digitable == paidBarcode.digitable }.status shouldBe BillStatus.PAID
                    bills.first { bill -> bill.barCode?.digitable == notPayableBarcode.digitable }.status shouldBe BillStatus.NOT_PAYABLE
                }
        }
    }

    @Test
    fun `should not move any bills if the documents of both wallets are different`() {
        billRepository.findByWallet(unknownWallet.id).size shouldBe 0
        billRepository.findByWallet(originWallet.id).size shouldBe BILLS_COUNT

        service.moveAllBills(originWalletId = originWallet.id, destinationWalletId = unknownWallet.id)
            .shouldBeTypeOf<MoveAllBillsResult.IncompatibleWalletOwnersError>()

        billRepository.findByWallet(unknownWallet.id).size shouldBe 0
        billRepository.findByWallet(originWallet.id).size shouldBe BILLS_COUNT
        billRepository.findByWallet(originWallet.id).filter { it.status == BillStatus.MOVED }.size shouldBe 0
    }

    private companion object Setup {
        const val BILLS_COUNT = 7
        const val INACTIVE_BILLS_COUNT = 4
        val lockProvider = mockk<InternalLock>(relaxed = true)
        private val enhancedClient = getDynamoDB()

        val paidBillId = BillId("BILL-PAID-1")
        val paidBarcode = BarCode.of(FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2)

        val ignoredBillId = BillId("BILL-IGNORED-1")
        val ignoredBarcode = BarCode.of(DIGITABLE_LINE)

        val alreadyPaidBillId = BillId("BILL-ALREADY-PAID-1")
        val alreadyPaidBarcode = BarCode.of(ELECTRICITY_AND_GAS_BARCODE_LINE)

        val notPayableBillId = BillId("BILL-NOT-PAYABLE-1")
        val notPayableBarcode = BarCode.of(TELECOM_BARCODE_LINE)

        val movedBillId = BillId("BILL-MOVED-1")
        val movedBarcode = BarCode.of(PREFECTURE_BARCODE_LINE)

        val ddaRepository: DDARepository = DDADbRepository(
            ddaDAO = DDADynamoDAO(enhancedClient),
            ddaBillDAO = DDABillDynamoDAO(enhancedClient),
        )

        val accountDAO = AccountDynamoDAO(enhancedClient)
        val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
        val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
        val nsuDAO = NSUDynamoDAO(enhancedClient)
        val accountTransactionDynamo = TransactionDynamo(enhancedClient)

        val accountRepository = spyk(
            AccountDbRepository(
                accountDAO = accountDAO,
                partialAccountDAO = partialAccountDAO,
                paymentMethodDAO = paymentMethodDAO,
                nsuDAO = nsuDAO,
                transactionDynamo = accountTransactionDynamo,
            ),
        )
        val accountService =
            spyk(
                AccountService(
                    accountConfigurationService = mockk(),
                    accountRepository = accountRepository,
                    chatbotMessagePublisher = mockk(),
                    crmService = mockk(),
                    notificationAdapter = mockk(),
                    walletRepository = mockk(relaxed = true),
                ),
            )

        val walletDAO = WalletDynamoDAO(enhancedClient)
        val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
        val inviteDAO = InviteDynamoDAO(enhancedClient)
        val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
        val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

        val walletRepository = spyk(
            WalletDbRepository(
                walletDAO = walletDAO,
                walletMemberDAO = walletMemberDAO,
                inviteDAO = inviteDAO,
                inviteReminderDAO = inviteReminderDAO,
                walletLimitDAO = walletLimitDAO,
                accountRepository = accountRepository,
            ),
        )
        val walletService: WalletService =
            spyk(
                WalletService(
                    accountService = accountService,
                    walletRepository = walletRepository,
                    configuration = mockk(relaxed = true),
                    notificationAdapter = mockk(relaxed = true),
                    eventPublisher = mockk(relaxed = true),
                    crmService = mockk(),
                    pixKeyRepository = mockk(relaxed = true),
                ),
            )
        val billRepository = spyk(
            DynamoDbBillRepository(
                billClient = BillDynamoDAO(enhancedClient),
                refundedClient = RefundedBillDynamoDAO(enhancedClient),
                settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(enhancedClient),
            ),
        )
        val billEventDAO = BillEventDynamoDAO(enhancedClient)
        val uniqueConstraintDAO = UniqueConstraintDynamoDAO(enhancedClient)
        val transactionDynamo = TransactionDynamo(enhancedClient)
        val billEventRepository = spyk(
            BillEventDBRepository(
                billEventDAO = billEventDAO,
                uniqueConstraintDAO = uniqueConstraintDAO,
                featureConfiguration = mockk(relaxed = true),
                transactionDynamo = transactionDynamo,
            ),
        )

        val eventPublisher: EventPublisher = mockk(relaxed = true)
        val updateBillService =
            spyk(
                UpdateBillService(
                    billEventRepository = billEventRepository,
                    billRepository = billRepository,
                    billTrackingRepository = mockk(relaxed = true),
                    eventPublisher = eventPublisher,
                    boletoSettlementService = mockk(relaxed = true),
                    billValidationService = mockk(relaxed = true),
                    walletLimitsService = mockk(relaxed = true),
                    walletService = walletService,
                    lockProvider = mockk(relaxed = true),
                    approveBillService = mockk(),
                    denyBillService = mockk(),
                    resolveScheduledBill = mockk(relaxed = true),
                    cancelSchedulePaymentService = mockk {
                        every { cancelScheduledPayment(any(), any(), any()) } returns true
                        every { userCancelScheduledPayment(any(), any(), any(), any()) } returns true
                    },
                    mailboxWalletDataRepository = mockk(),
                ),
            )

        private val walletFixture = WalletFixture()

        val movedWallet =
            walletFixture.buildWallet(
                id = WalletId("WALLET-MOVED-1"),
                name = "carteira movida para a origem anteriormente",
                otherMembers =
                listOf(
                    walletFixture.assistant,
                    walletFixture.limitedParticipant,
                    walletFixture.ultraLimitedParticipant,
                    walletFixture.participant,
                ),
            )

        val originWallet =
            walletFixture.buildWallet(
                "carteira origem",
                otherMembers =
                listOf(
                    walletFixture.assistant,
                    walletFixture.limitedParticipant,
                    walletFixture.ultraLimitedParticipant,
                    walletFixture.participant,
                ),
            )

        val destinationWallet =
            walletFixture.buildWallet(
                "carteira destino",
                id = WalletId("WALLET-${UUID.randomUUID()}"),
                otherMembers =
                listOf(
                    walletFixture.assistant,
                    walletFixture.limitedParticipant,
                    walletFixture.ultraLimitedParticipant,
                ),
            )

        val unknownWallet =
            walletFixture.buildWallet(
                "carteira desconhecida",
                id = WalletId("WALLET-FORBIDDEN"),
                walletFounder =
                ACCOUNT.copy(
                    accountId = AccountId(),
                    document = "***********",
                ).toMember(
                    type = MemberType.FOUNDER,
                    permissions = MemberPermissions.of(MemberType.FOUNDER),
                ),
                otherMembers =
                listOf(
                    walletFixture.assistant,
                    walletFixture.limitedParticipant,
                    walletFixture.ultraLimitedParticipant,
                ),
            )

        fun beforeEach() {
            createBillPaymentTable(LocalDbCreationRule.dynamoDB)
            createBillEventTable(LocalDbCreationRule.dynamoDB)

            every { lockProvider.acquireLock(any()) } returns mockk(relaxed = true)

            accountRepository.save(walletFixture.founderAccount)

            walletRepository.save(movedWallet)
            walletRepository.save(originWallet)
            walletRepository.save(destinationWallet)
            walletRepository.save(unknownWallet)

            listOf(
                pixAdded.copy(walletId = originWallet.id),
                invoiceAdded.copy(walletId = originWallet.id),
                fichaCompensacaoCreditCardAdded.copy(walletId = originWallet.id),
            ).forEach {
                billEventRepository.save(it)
                billRepository.save(Bill.build().apply(it))
            }

            listOf(
                fichaCompensacaoCreditCardAdded.copy(
                    walletId = originWallet.id,
                    billId = paidBillId,
                    idNumber = "321",
                    barcode = paidBarcode,
                ),
                billPaid.copy(walletId = originWallet.id, billId = paidBillId),
            ).also { billRepository.save(Bill.build(it.first(), it.last())) }
                .forEach { event -> billEventRepository.save(event) }

            listOf(
                fichaCompensacaoCreditCardAdded.copy(
                    walletId = originWallet.id,
                    billId = ignoredBillId,
                    idNumber = "3210",
                    barcode = ignoredBarcode,
                ),
                billIgnored.copy(walletId = originWallet.id, billId = ignoredBillId),
            ).also { billRepository.save(Bill.build(it.first(), it.last())) }
                .forEach { event -> billEventRepository.save(event) }

            listOf(
                fichaCompensacaoCreditCardAdded.copy(
                    walletId = originWallet.id,
                    billId = notPayableBillId,
                    idNumber = "1234567",
                    barcode = notPayableBarcode,
                ),
                billRegisterUpdatedNotPayable.copy(walletId = originWallet.id, billId = notPayableBillId),
            ).also { billRepository.save(Bill.build(it.first(), it.last())) }
                .forEach { event -> billEventRepository.save(event) }

            listOf(
                fichaCompensacaoCreditCardAdded.copy(
                    walletId = movedWallet.id,
                    billId = movedBillId,
                    idNumber = "123456789",
                    barcode = movedBarcode,
                ),
                billMoved.copy(
                    destinationWalletId = originWallet.id,
                    billId = movedBillId,
                    walletId = movedWallet.id,
                    idNumber = "123456789",
                    barCode = movedBarcode,
                ),
            ).also { billRepository.save(Bill.build(it.first(), it.last())) }
                .forEach { event -> billEventRepository.save(event) }

            listOf(
                fichaCompensacaoCreditCardAdded.copy(
                    walletId = originWallet.id,
                    billId = alreadyPaidBillId,
                    idNumber = "43210",
                    barcode = alreadyPaidBarcode,
                ),
                billRegisterUpdatedAlreadyPaid.copy(walletId = originWallet.id, billId = alreadyPaidBillId),
            ).also { billRepository.save(Bill.build(it.first(), it.last())) }
                .forEach { event -> billEventRepository.save(event) }

            clearAllMocks()
        }
    }
}