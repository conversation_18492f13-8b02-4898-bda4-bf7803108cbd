package ai.friday.billpayment.app.bill

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.hasEarlyAccess
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.BillRegisterData
import ai.friday.billpayment.app.payment.BillValidationResponse
import ai.friday.billpayment.integration.WALLET_ID
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkConstructor
import io.mockk.unmockkConstructor
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test

internal class BlockEmptyAmountCreationServiceTest {

    private val blockEmptyAmountCreationService = DefaultBlockEmptyAmountCreationService()

    @AfterEach
    fun clean() {
        unmockkConstructor(AccountId::class)
    }

    @Test
    fun `deve retornar true quando quando o calculationModel for diferente de BENEFICIARY_ONLY`() {
        val billValidationResponse = mockk<BillValidationResponse> {
            every {
                billRegisterData
            } returns mockk<BillRegisterData> {
                every {
                    amountCalculationModel
                } returns AmountCalculationModel.BENEFICIARY_AFTER_DUE_DATE
            }
        }

        val addFichaDeCompensacaoRequest = mockk<CreateFichaDeCompensacaoRequest> {
            every {
                source
            } returns ActionSource.Api(accountId = ACCOUNT.accountId)
            every {
                walletId.value
            } returns WALLET_ID
        }

        val result = blockEmptyAmountCreationService.check(billValidationResponse, addFichaDeCompensacaoRequest)

        result.shouldBeTrue()
    }

    @Test
    fun `deve retornar false quando o calculationModel for BENEFICIARY_ONLY, source não for API e o usuário for early access`() {
        val billValidationResponse = mockk<BillValidationResponse> {
            every {
                billRegisterData
            } returns mockk<BillRegisterData> {
                every {
                    amountCalculationModel
                } returns AmountCalculationModel.BENEFICIARY_ONLY
            }
        }

        val addFichaDeCompensacaoRequest = mockk<CreateFichaDeCompensacaoRequest> {
            every {
                source
            } returns ActionSource.DDA(accountId = ACCOUNT.accountId)
            every {
                walletId.value
            } returns ACCOUNT.accountId.value
        }

        mockkConstructor(AccountId::class)

        every {
            anyConstructed<AccountId>().hasEarlyAccess()
        } returns true

        val result = blockEmptyAmountCreationService.check(billValidationResponse, addFichaDeCompensacaoRequest)

        result.shouldBeFalse()
    }
}