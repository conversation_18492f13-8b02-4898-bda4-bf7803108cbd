package ai.friday.billpayment.app.bill

import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.BillValidationService
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.payment.BillValidationStatus
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billIgnored
import ai.friday.billpayment.billMoved
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentFailed
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.billRegisterUpdatedAlreadyPaid
import ai.friday.billpayment.billRegisterUpdatedNotPayable
import ai.friday.billpayment.billScheduleCanceled
import ai.friday.billpayment.fichaCompensacaoCreditCardAdded
import ai.friday.billpayment.fichaRegisterData
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.notPayableConcessionariaValidationResponse
import ai.friday.billpayment.successConcessionariaValidationResponse
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class MarkAsPaidBillServiceTest {

    private val billEventDbRepositoryMock: BillEventDBRepository = mockk(relaxed = true)
    private val billRepositoryMock: BillRepository = mockk(relaxed = true)
    private val eventPublisherMock: EventPublisher = mockk(relaxed = true)
    private val lockProvider: InternalLock = mockk(relaxed = true)
    private val billValidationService = mockk<BillValidationService>(relaxed = true)
    private val boletoSettlementService = mockk<BoletoSettlementService>(relaxed = true)

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet(otherMembers = listOf(walletFixture.participant, walletFixture.ultraLimitedParticipant))

    private val resolveScheduledBill = mockk<ResolveScheduledBill>(relaxUnitFun = true)

    private val updateBillService = UpdateBillService(
        billEventRepository = billEventDbRepositoryMock,
        billRepository = billRepositoryMock,
        billTrackingRepository = mockk(relaxed = true),
        eventPublisher = eventPublisherMock,
        boletoSettlementService = boletoSettlementService,
        billValidationService = billValidationService,
        lockProvider = lockProvider,
        walletLimitsService = mockk(),
        walletService = mockk(),
        approveBillService = mockk(),
        denyBillService = mockk(),
        resolveScheduledBill = resolveScheduledBill,
        cancelSchedulePaymentService = mockk {
            every { cancelScheduledPayment(any(), any(), any()) } returns true
            every { userCancelScheduledPayment(any(), any(), any(), any()) } returns true
        },
        mailboxWalletDataRepository = mockk(),
    )

    @Nested
    @DisplayName("given mark as paid")
    inner class MarkAsPaid {

        @BeforeEach
        fun init() {
            every {
                billEventDbRepositoryMock.getBill(any(), any())
            } returns Bill.build(billAdded).right()

            every { lockProvider.acquireLock(any()) } returns mockk(relaxed = true)
        }

        @Test
        fun `should return ItemNotFoundException when bill was not found`() {
            every {
                billEventDbRepositoryMock.getBill(any(), any())
            } returns ItemNotFoundException("").left()

            val response = updateBillService.markAsPaid(
                billId = billAdded.billId,
                member = wallet.founder,
                walletId = wallet.id,
                amountPaid = null,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            )

            response.isLeft() shouldBe true
            response.mapLeft {
                it.shouldBeTypeOf<ItemNotFoundException>()
            }
        }

        @Test
        fun `should return MemberNotAllowedException when member can't view bill`() {
            val response = updateBillService.markAsPaid(
                billId = billAdded.billId,
                member = walletFixture.ultraLimitedParticipant,
                walletId = wallet.id,
                amountPaid = null,
                actionSource = ActionSource.Api(accountId = walletFixture.ultraLimitedParticipant.accountId),
            )

            response.isLeft() shouldBe true
            response.mapLeft {
                it.shouldBeTypeOf<MemberNotAllowedException>()
            }
        }

        @Test
        fun `should return AlreadyLockedException when bill is already locked`() {
            every { lockProvider.acquireLock(any()) } returns null

            val response = updateBillService.markAsPaid(
                billId = billAdded.billId,
                member = walletFixture.founder,
                walletId = wallet.id,
                amountPaid = null,
                actionSource = ActionSource.Api(accountId = walletFixture.founder.accountId),
            )

            response.isLeft() shouldBe true
            response.mapLeft {
                it.shouldBeTypeOf<BillAlreadyLockedException>()
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.bill.MarkAsPaidBillServiceTest#invalidBillStatusToMarkAsPaid")
        fun `should not change bill and fails when bill is in state`(bill: Bill) {
            listOf(walletFixture.founder, walletFixture.participant).map { memb ->
                every {
                    billEventDbRepositoryMock.getBill(any(), any())
                } returns bill.right()

                val response = updateBillService.markAsPaid(
                    billId = billAdded.billId,
                    member = memb,
                    walletId = wallet.id,
                    amountPaid = null,
                    actionSource = ActionSource.Api(accountId = memb.accountId),
                )

                response.isLeft() shouldBe true
                response.mapLeft {
                    it.shouldBeTypeOf<InvalidBillStateChangeException>()
                }
            }
        }

        @Test
        fun `should not change bill and succeed when bill is already paid`() {
            val bill = Bill.build(billAdded, billRegisterUpdatedAlreadyPaid)
            every {
                billEventDbRepositoryMock.getBill(any(), any())
            } returns bill.right()

            val response = updateBillService.markAsPaid(
                billId = billAdded.billId,
                member = walletFixture.founder,
                walletId = wallet.id,
                amountPaid = null,
                actionSource = ActionSource.Api(accountId = walletFixture.founder.accountId),
            )

            response.isRight() shouldBe true
            response.getOrNull() shouldBe bill
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.bill.MarkAsPaidBillServiceTest#validBillStatusToMarkAsPaid")
        fun `should change bill to ALREADY_PAID and amountPaid should be bill amount total succeed when is in allowed statuses`(
            billEvents: MutableList<BillEvent>,
        ) {
            val bill = Bill.build(billEvents)
            every {
                billEventDbRepositoryMock.getBill(any(), any())
            } returns bill.right()

            val response = updateBillService.markAsPaid(
                billId = billAdded.billId,
                member = walletFixture.founder,
                walletId = wallet.id,
                amountPaid = null,
                actionSource = ActionSource.Api(accountId = walletFixture.founder.accountId),
            )

            response.isRight() shouldBe true

            response.map { bill ->
                bill.shouldBeInstanceOf<Bill>()
                bill.status shouldBe BillStatus.ALREADY_PAID
                bill.amountPaid shouldBe billAdded.amountTotal
                bill.schedule.shouldBeNull()
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.bill.MarkAsPaidBillServiceTest#validBillStatusToMarkAsPaid")
        fun `should change bill to ALREADY_PAID and amountPaid should be the same as informed value when is in allowed statuses`(
            billEvents: MutableList<BillEvent>,
        ) {
            val bill = Bill.build(billEvents)
            every {
                billEventDbRepositoryMock.getBill(any(), any())
            } returns bill.right()

            val amountPaid = billAdded.amountTotal - 1
            val response = updateBillService.markAsPaid(
                billId = billAdded.billId,
                member = walletFixture.founder,
                walletId = wallet.id,
                amountPaid = amountPaid,
                actionSource = ActionSource.Api(accountId = walletFixture.founder.accountId),
            )

            response.isRight() shouldBe true

            response.map { bill ->
                bill.shouldBeInstanceOf<Bill>()
                bill.status shouldBe BillStatus.ALREADY_PAID
                bill.amountPaid shouldBe amountPaid
                bill.schedule.shouldBeNull()
                bill.markedAsPaidBy shouldBe walletFixture.founder.accountId
                bill.markedAsPaidAt.shouldNotBeNull()
            }
        }
    }

    @Nested
    @DisplayName("given cancel marked as paid")
    inner class CancelMarkedAsPaid {

        @BeforeEach
        fun init() {
            every {
                billEventDbRepositoryMock.getBill(any(), any())
            } returns Bill.build(billAdded).right()

            every { lockProvider.acquireLock(any()) } returns mockk(relaxed = true)
        }

        @Test
        fun `should return ItemNotFoundException when bill was not found`() {
            every {
                billEventDbRepositoryMock.getBill(any(), any())
            } returns ItemNotFoundException("").left()

            val response = updateBillService.cancelMarkAsPaid(
                billId = billAdded.billId,
                member = wallet.founder,
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            )

            response.isLeft() shouldBe true
            response.mapLeft {
                it.shouldBeTypeOf<ItemNotFoundException>()
            }
        }

        @Test
        fun `should return MemberNotAllowedException when member can't view bill`() {
            val response = updateBillService.cancelMarkAsPaid(
                billId = billAdded.billId,
                member = walletFixture.ultraLimitedParticipant,
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = walletFixture.ultraLimitedParticipant.accountId),
            )

            response.isLeft() shouldBe true
            response.mapLeft {
                it.shouldBeTypeOf<MemberNotAllowedException>()
            }
        }

        @Test
        fun `should return AlreadyLockedException when bill is already locked`() {
            every { lockProvider.acquireLock(any()) } returns null

            val response = updateBillService.cancelMarkAsPaid(
                billId = billAdded.billId,
                member = walletFixture.founder,
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = walletFixture.founder.accountId),
            )

            response.isLeft() shouldBe true
            response.mapLeft {
                it.shouldBeTypeOf<BillAlreadyLockedException>()
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.bill.MarkAsPaidBillServiceTest#invalidBillStatusToCancelMarkedAsPaid")
        fun `should not change bill and fails when bill is in state`(bill: Bill) {
            listOf(walletFixture.founder, walletFixture.participant).map { memb ->
                every {
                    billEventDbRepositoryMock.getBill(any(), any())
                } returns bill.right()

                val response = updateBillService.cancelMarkAsPaid(
                    billId = billAdded.billId,
                    member = memb,
                    walletId = wallet.id,
                    actionSource = ActionSource.Api(accountId = memb.accountId),
                )

                response.isLeft() shouldBe true
                response.mapLeft {
                    it.shouldBeTypeOf<InvalidBillStateChangeException>()
                }
            }
        }

        @Test
        fun `should not change bill and succeed when last event is cancel marked as paid`() {
            val billMarkedAsPaidCanceled = BillMarkedAsPaidCanceled(
                billId = billAdded.billId,
                created = billAdded.created + 1,
                walletId = billAdded.walletId,
                actionSource = ActionSource.Api(accountId = walletFixture.founder.accountId),
            )
            val bill = Bill.build(billAdded, billMarkedAsPaidCanceled)
            every {
                billEventDbRepositoryMock.getBill(any(), any())
            } returns bill.right()

            val response = updateBillService.cancelMarkAsPaid(
                billId = billAdded.billId,
                member = walletFixture.founder,
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = walletFixture.founder.accountId),
            )

            response.isRight() shouldBe true
            response.getOrNull() shouldBe bill
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.bill.MarkAsPaidBillServiceTest#validBillStatusToMarkAsPaid")
        fun `should change bill to previous state when it is marked as paid and validation works`(
            billEvents: MutableList<BillEvent>,
        ) {
            every {
                boletoSettlementService.validateBill(any<Bill>())
            } returns successConcessionariaValidationResponse

            val billMarkedAsPaid = BillMarkedAsPaid(
                billId = billAdded.billId,
                created = billAdded.created + 1,
                walletId = billAdded.walletId,
                amountPaid = billAdded.amountTotal,
                actionSource = ActionSource.Api(accountId = walletFixture.founder.accountId),
            )
            val originalBill = Bill.build(billEvents)
            billEvents.add(billMarkedAsPaid)
            val markedAsPaidBill = Bill.build(billEvents)
            every {
                billEventDbRepositoryMock.getBill(any(), any())
            } returns markedAsPaidBill.right()

            val response = updateBillService.cancelMarkAsPaid(
                billId = billAdded.billId,
                member = walletFixture.founder,
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = walletFixture.founder.accountId),
            )

            response.isRight() shouldBe true

            response.map { bill ->
                bill.shouldBeInstanceOf<Bill>()
                bill.status shouldBe originalBill.status
                bill.amountPaid.shouldBeNull()
                bill.markedAsPaidAt.shouldBeNull()
            }
        }
    }

    @ParameterizedTest
    @MethodSource("ai.friday.billpayment.app.bill.MarkAsPaidBillServiceTest#validBillStatusToMarkAsPaid")
    fun `should change bill to not payable when it is marked as paid and validation returns not payable`(
        billEvents: MutableList<BillEvent>,
    ) {
        listOf(walletFixture.founder, walletFixture.participant).map { memb ->
            every {
                boletoSettlementService.validateBill(any<Bill>())
            } returns notPayableConcessionariaValidationResponse

            val billMarkedAsPaid = BillMarkedAsPaid(
                billId = billAdded.billId,
                created = billAdded.created + 1,
                walletId = billAdded.walletId,
                amountPaid = billAdded.amountTotal,
                actionSource = ActionSource.Api(accountId = memb.accountId),
            )
            billEvents.add(billMarkedAsPaid)
            val markedAsPaidBill = Bill.build(billEvents)
            every {
                billEventDbRepositoryMock.getBill(any(), any())
            } returns markedAsPaidBill.right()

            val response = updateBillService.cancelMarkAsPaid(
                billId = billAdded.billId,
                member = memb,
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = memb.accountId),
            )

            response.isRight() shouldBe true

            response.map { bill ->
                bill.shouldBeInstanceOf<Bill>()
                bill.status shouldBe BillStatus.NOT_PAYABLE
                bill.amountPaid.shouldBeNull()
                bill.markedAsPaidAt.shouldBeNull()
            }
        }
    }

    @Test
    fun `should not change bill state when it is marked as paid and validation returns already paid`() {
        every {
            billValidationService.validate(any<Bill>())
        } returns mockk(relaxed = true) {
            every {
                alreadyPaid()
            } returns true

            every {
                getStatus()
            } returns BillValidationStatus.AlreadyPaid

            every {
                errorDescription
            } returns "error fake"

            every {
                billRegisterData
            } returns fichaRegisterData
        }

        val billMarkedAsPaid = BillMarkedAsPaid(
            billId = fichaCompensacaoCreditCardAdded.billId,
            created = fichaCompensacaoCreditCardAdded.created + 1,
            walletId = fichaCompensacaoCreditCardAdded.walletId,
            amountPaid = fichaCompensacaoCreditCardAdded.amountTotal,
            actionSource = ActionSource.Api(accountId = walletFixture.founder.accountId),
        )
        val billEvents = mutableListOf(
            fichaCompensacaoCreditCardAdded,
            billMarkedAsPaid,
        )

        val markedAsPaidBill = Bill.build(billEvents)
        every {
            billEventDbRepositoryMock.getBill(any(), any())
        } returns markedAsPaidBill.right()

        val response = updateBillService.cancelMarkAsPaid(
            billId = billAdded.billId,
            member = walletFixture.founder,
            walletId = wallet.id,
            actionSource = ActionSource.Api(accountId = walletFixture.founder.accountId),
        )

        response.isRight() shouldBe true

        response.map { bill ->
            bill.shouldBeInstanceOf<Bill>()
            bill.status shouldBe BillStatus.ALREADY_PAID
            bill.amountPaid.shouldNotBeNull()
            val lastEvent = bill.history.last()
            lastEvent.shouldBeInstanceOf<RegisterUpdated>()
            lastEvent.updatedRegisterData.shouldBeInstanceOf<UpdatedRegisterData.AlreadyPaidBill>()
            bill.markedAsPaidAt.shouldBeNull()
        }
    }

    companion object {
        @JvmStatic
        fun invalidBillStatusToMarkAsPaid(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(Bill.build(billAdded, billIgnored)),
                Arguments.arguments(Bill.build(billAdded, billPaymentScheduled)),
                Arguments.arguments(Bill.build(billAdded, billPaymentScheduled, billPaymentStart)),
                Arguments.arguments(Bill.build(billAdded, billPaymentScheduled, billPaymentStart, billPaid)),
                Arguments.arguments(Bill.build(billAdded, billMoved)),
            )
        }

        @JvmStatic
        fun validBillStatusToMarkAsPaid(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(mutableListOf(billAdded)),
                Arguments.arguments(
                    mutableListOf(
                        billAdded,
                        billPaymentScheduled,
                        billPaymentStart,
                        billPaymentFailed,
                        billScheduleCanceled,
                    ),
                ),
                Arguments.arguments(
                    mutableListOf(
                        billAdded,
                        billPaymentScheduled,
                        billPaymentStart,
                        billPaymentFailed,
                        billScheduleCanceled,
                        billRegisterUpdatedNotPayable,
                    ),
                ),
                Arguments.arguments(mutableListOf(billAdded, billRegisterUpdatedNotPayable)),
            )
        }

        @JvmStatic
        fun invalidBillStatusToCancelMarkedAsPaid(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(Bill.build(billAdded, billIgnored)),
                Arguments.arguments(Bill.build(billAdded, billPaymentScheduled)),
                Arguments.arguments(Bill.build(billAdded, billPaymentScheduled, billPaymentStart)),
                Arguments.arguments(Bill.build(billAdded, billPaymentScheduled, billPaymentStart, billPaid)),
                Arguments.arguments(Bill.build(billAdded, billMoved)),
                Arguments.arguments(Bill.build(billAdded, billRegisterUpdatedAlreadyPaid)),
            )
        }
    }
}