package ai.friday.billpayment.app.bill.schedule.security

import ai.friday.billpayment.app.bill.schedule.security.rules.ScheduleBillSecurityRule
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifyOrder
import org.junit.jupiter.api.Test

class ScheduleBillSecurityServiceTest {

    private val rule1 = mockk<ScheduleBillSecurityRule>(relaxed = true) { every { getPriority() } returns 2 }

    private val rule2 = mockk<ScheduleBillSecurityRule>(relaxed = true) { every { getPriority() } returns 1 }

    private val scheduleBillSecurityService = ScheduleBillSecurityService(listOf(rule1, rule2))

    private val request = ScheduleBillSecurityRequest(
        bill = mockk(),
        member = mockk(),
        paymentMethodDetails = mockk(),
    )

    @Test
    fun `deve retornar sucesso quando todas as regras passarem`() {
        every {
            rule1.execute(any())
        } returns Unit.right()

        every {
            rule2.execute(any())
        } returns Unit.right()

        val result = scheduleBillSecurityService.canSchedule(request)

        result.isRight() shouldBe true

        verifyOrder {
            rule2.execute(request)
            rule1.execute(request)
        }
    }

    @Test
    fun `deve interromper a verificacao e devolver erro quando alguma regra for quebrada`() {
        every {
            rule1.execute(any())
        } returns Unit.right()

        every {
            rule2.execute(any())
        } returns ScheduleBillSecurityError.PermissionDenied("Test").left()

        val result = scheduleBillSecurityService.canSchedule(request)

        result.isLeft() shouldBe true
        result.mapLeft { it.shouldBeTypeOf<ScheduleBillSecurityError.PermissionDenied>().message shouldBe "Test" }

        verify {
            rule2.execute(request)
        }

        verify(exactly = 0) {
            rule1.execute(request)
        }
    }

    @Test
    fun `deve devolver erro quando uma regra for quebrada`() {
        every {
            rule1.execute(any())
        } returns ScheduleBillSecurityError.PermissionDenied("Test1").left()

        every {
            rule2.execute(any())
        } returns Unit.right()

        val result = scheduleBillSecurityService.canSchedule(request)

        result.isLeft() shouldBe true
        result.mapLeft { it.shouldBeTypeOf<ScheduleBillSecurityError.PermissionDenied>().message shouldBe "Test1" }

        verifyOrder {
            rule2.execute(request)
            rule1.execute(request)
        }
    }
}