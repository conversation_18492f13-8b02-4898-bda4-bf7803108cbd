package ai.friday.billpayment.app.bill.tracking

import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.payment.DiscountData
import ai.friday.billpayment.app.payment.FineData
import ai.friday.billpayment.app.payment.InterestData
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billDenied
import ai.friday.billpayment.billIgnored
import ai.friday.billpayment.billMarkedAsPaid
import ai.friday.billpayment.billMarkedAsPaidCanceled
import ai.friday.billpayment.billMoved
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentFailed
import ai.friday.billpayment.billReactivated
import ai.friday.billpayment.billRefunded
import ai.friday.billpayment.billRegisterUpdatedAlreadyPaid
import ai.friday.billpayment.billRegisterUpdatedNewAmount
import ai.friday.billpayment.billRegisterUpdatedNewAmountCalculationData
import ai.friday.billpayment.billRegisterUpdatedNewDueDate
import ai.friday.billpayment.billRegisterUpdatedNotPayable
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.math.BigDecimal
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class DefaultTrackableBillHandlerServiceTest {
    private lateinit var test: DefaultTrackableBillHandlerService

    private lateinit var retry: RetryTrackableBillService
    private lateinit var creator: CreateTrackableBillService
    private lateinit var remover: RemoveTrackableBillService
    private lateinit var updater: UpdateTrackableBillService
    private lateinit var reactivator: ReactivateTrackableBillService

    @BeforeEach
    fun setUp() {
        retry = mockk()
        creator = mockk()
        updater = mockk()
        remover = mockk()
        reactivator = mockk()

        test = DefaultTrackableBillHandlerService(
            createService = creator,
            updateService = updater,
            removeService = remover,
            reactivateService = reactivator,
            retryService = retry,
        )
    }

    @Test
    fun `should handle create trackable bill only when event is FichaCompensacaoAdded`() {
        every { creator.create(any()) } returns Unit.right()

        test.execute(billAddedFicha)

        verify {
            creator.create(
                withArg {
                    it.billId shouldBe billAddedFicha.billId
                },
            )
        }

        verify { retry wasNot called }
        verify { updater wasNot called }
        verify { remover wasNot called }
        verify { reactivator wasNot called }
    }

    @ParameterizedTest
    @MethodSource("removeEvents")
    fun `should remove trackable bill only if event does match`(event: BillEvent) {
        every { remover.remove(any()) } returns Unit.right()

        test.execute(event)

        verify {
            remover.remove(
                withArg {
                    it.billId shouldBe event.billId
                },
            )
        }

        verify { retry wasNot called }
        verify { creator wasNot called }
        verify { updater wasNot called }
        verify { reactivator wasNot called }
    }

    @ParameterizedTest
    @MethodSource("reactivateEvents")
    fun `should reactivate trackable bill only if event does match`(event: BillEvent) {
        every { reactivator.reactivate(any()) } returns Unit.right()

        test.execute(event)

        verify {
            reactivator.reactivate(
                withArg {
                    it.billId shouldBe event.billId
                },
            )
        }

        verify { retry wasNot called }
        verify { creator wasNot called }
        verify { updater wasNot called }
        verify { remover wasNot called }
    }

    @Test
    fun `should retry when event failed and retry is enabled`() {
        every { retry.retry(any()) } returns Unit.right()

        val event = billPaymentFailed.copy(retryable = true)

        test.execute(event)

        verify {
            retry.retry(event)
        }

        verify { reactivator wasNot called }
        verify { creator wasNot called }
        verify { updater wasNot called }
        verify { remover wasNot called }
    }

    @Test
    fun `should not remove trackable when event is BillFailed`() {
        every { remover.remove(any()) } returns Unit.right()

        val event = billPaymentFailed.copy(retryable = false)

        test.execute(event)

        verify(
            exactly = 0,
        ) {
            remover.remove(event)
        }

        verify { retry wasNot called }
        verify { creator wasNot called }
        verify { updater wasNot called }
        verify { reactivator wasNot called }
    }

    @Test
    fun `should update amount when event is RegisterUpdated with updatedRegisterData of the type NewTotalAmount`() {
        every { updater.updateAmountByBillID(any(), any()) } returns Unit.right()

        test.execute(billRegisterUpdatedNewAmount)

        verify {
            updater.updateAmountByBillID(billRegisterUpdatedNewAmount.billId, 10)
        }

        verify { retry wasNot called }
        verify { creator wasNot called }
        verify { reactivator wasNot called }
        verify { remover wasNot called }
    }

    @Test
    fun `should update calculation data when event is RegisterUpdated with updatedRegisterData of the type NewAmountCalculationData`() {
        every { updater.updatePaymentCalculationDataByBillID(any(), any(), any(), any()) } returns Unit.right()

        test.execute(billRegisterUpdatedNewAmountCalculationData)

        val fineDataSlot = slot<FineData>()
        val interestDataSlot = slot<InterestData>()
        val discountDataSlot = slot<DiscountData>()

        verify {
            updater.updatePaymentCalculationDataByBillID(billRegisterUpdatedNewAmount.billId, capture(fineDataSlot), capture(interestDataSlot), capture(discountDataSlot))
        }

        fineDataSlot.captured.value shouldBe BigDecimal.valueOf(1.23)
        interestDataSlot.captured.value shouldBe BigDecimal.valueOf(4.56)
        discountDataSlot.captured.value1 shouldBe BigDecimal.valueOf(7.89)

        verify { retry wasNot called }
        verify { creator wasNot called }
        verify { reactivator wasNot called }
        verify { remover wasNot called }
    }

    @Test
    fun `should update due date when event is RegisterUpdated with updatedRegisterData of the type NewDueDate`() {
        every { updater.updateDueDateByBillID(any(), any()) } returns Unit.right()

        test.execute(billRegisterUpdatedNewDueDate)

        verify {
            updater.updateDueDateByBillID(billRegisterUpdatedNewDueDate.billId, any())
        }

        verify { retry wasNot called }
        verify { creator wasNot called }
        verify { reactivator wasNot called }
        verify { remover wasNot called }
    }

    companion object {
        @JvmStatic
        fun removeEvents() = listOf(
            billPaid,
            billIgnored,
            billMoved,
            billMarkedAsPaid,
            billRegisterUpdatedAlreadyPaid,
            billRegisterUpdatedNotPayable,
            billDenied,
        ).map { Arguments.of(it) }.stream()

        @JvmStatic
        fun reactivateEvents() = listOf(
            billRefunded,
            billReactivated,
            billMarkedAsPaidCanceled,
        ).map { Arguments.of(it) }.stream()
    }
}