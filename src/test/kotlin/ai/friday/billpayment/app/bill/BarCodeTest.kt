package ai.friday.billpayment.app.bill

import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_BARCODE
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import io.kotest.matchers.shouldBe
import kotlin.test.assertEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class BarCodeTest {

    @ParameterizedTest
    @CsvSource(
        value = [
            "$FICHA_DE_COMPENSACAO_DIGITABLE_LINE,$FICHA_DE_COMPENSACAO_BARCODE",
            "34191756119437620252250451630003800000000000000,34198000000000000001756194376202525045163000",
            "23793381286002623275580000063307182460000002003,23791824600000020033381260026232758000006330",
            "00190000090286665907746231001176182590000002000,00191825900000020000000002866659074623100117",
            "924700000013367102962011909100180007002472014444,92470000001367102962019091001800000247201444",
        ],
    )
    fun `should convert digitable line to barcode number`(digitable: String, expectedBarCode: String) {
        val barcode = BarCode.ofDigitable(digitable)
        assertEquals(expectedBarCode, barcode.number)
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "23790473074000068375103001679400696820000123651,true",
            "00190000090349883302321668131176696800000036176,true",
            "23791629089009221602853000178003796770000064650,true",
            "03399543492038036470138170601017996720000017800,true",
            "34191756119437620252250451630003800000000000000,true",
            "23793381286002623275580000063307182460000002003,true",
            "00190000090286665907746231001176182590000002000,true",
        ],
    )
    fun `deve checar linha digitavel`(digitable: String, expectedAnswer: Boolean) {
        val barcode = BarCode.ofDigitable(digitable)
        assertEquals(expectedAnswer, barcode.isFicha())
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "34198000000000000001756194376202525045163000,true",
            "23791824600000020033381260026232758000006330,true",
            "00191825900000020000000002866659074623100117,true",
            "23791827400000021473381260028549782400006330,true",
            "33696827600000020000000000000010003205628243,true",
        ],
    )
    fun `deve checar o codigo de barras de linha digitavel`(barcodeLine: String, expectedAnswer: Boolean) {
        val barcode = BarCode.of(barcodeLine)
        assertEquals(expectedAnswer, barcode.isFicha())
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "$FICHA_DE_COMPENSACAO_DIGITABLE_LINE,$FICHA_DE_COMPENSACAO_BARCODE",
            "34191756119437620252250451630003800000000000000,34198000000000000001756194376202525045163000",
            "23793381286002623275580000063307182460000002003,23791824600000020033381260026232758000006330",
            "00190000090286665907746231001176182590000002000,00191825900000020000000002866659074623100117",
            "23793381286002854978424000063305182740000002147,23791827400000021473381260028549782400006330",
            "33690000090000001000932056282430682760000002000,33696827600000020000000000000010003205628243",
            "858200000066054103852009590717200512678493835452,85820000006054103852005907172005167849383545",
            "848000000014159601582027004011254192988008121223,84800000001159601582020040112541998800812122",
            "836900000008828900531074105230466119101007954817,83690000000828900531071052304661110100795481",
            "817800000215365036592027002073102002100113397317,81780000021365036592020020731020010011339731",
            "828500000000333600160624419720622014000023172061,82850000000333600160624197206220100002317206",
            "818000000020631857012205609450077731969400399142,81800000002631857012206094500777396940039914",
            "858900000247781103852213810711221373555734660951,85890000024781103852218107112213755573466095",
            "848700000009990001622024206101428440344011111225,84870000000990001622022061014284434401111122",
        ],
    )
    fun `should convert barcode number to digitable line`(expectedDigitable: String, barCode: String) {
        val barcode = BarCode.of(barCode)
        assertEquals(expectedDigitable, barcode.digitable)
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "$FICHA_DE_COMPENSACAO_BARCODE,00190.00009 02803.406004 21010.889174 5 81950000005897",
            "34198000000000000001756194376202525045163000,34191.75611 94376.202522 50451.630003 8 00000000000000",
            "23791824600000020033381260026232758000006330,23793.38128 60026.232755 80000.063307 1 82460000002003",
            "00191825900000020000000002866659074623100117,00190.00009 02866.659077 46231.001176 1 82590000002000",
            "23791827400000021473381260028549782400006330,23793.38128 60028.549784 24000.063305 1 82740000002147",
            "33696827600000020000000000000010003205628243,33690.00009 00000.010009 32056.282430 6 82760000002000",
            "85820000006054103852005907172005167849383545,85820000006-6 05410385200-9 59071720051-2 67849383545-2",
            "84800000001159601582020040112541998800812122,84800000001-4 15960158202-7 00401125419-2 98800812122-3",
            "83690000000828900531071052304661110100795481,83690000000-8 82890053107-4 10523046611-9 10100795481-7",
            "81780000021365036592020020731020010011339731,81780000021-5 36503659202-7 00207310200-2 10011339731-7",
        ],
    )
    fun `should get formatted digitableLine`(barCode: String, expectedFormattedDigitableLine: String) {
        val barcode = BarCode.of(barCode)
        barcode.formattedDigitable() shouldBe expectedFormattedDigitableLine
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "$FICHA_DE_COMPENSACAO_DIGITABLE_LINE,$FICHA_DE_COMPENSACAO_BARCODE",
            "34191756119437620252250451630003800000000000000,34198000000000000001756194376202525045163000",
            "23793381286002623275580000063307182460000002003,23791824600000020033381260026232758000006330",
            "00190000090286665907746231001176182590000002000,00191825900000020000000002866659074623100117",
            "924700000013367102962011909100180007002472014444,92470000001367102962019091001800000247201444",
            "34198000000000000001756194376202525045163000,34198000000000000001756194376202525045163000",
            "23791824600000020033381260026232758000006330,23791824600000020033381260026232758000006330",
            "00191825900000020000000002866659074623100117,00191825900000020000000002866659074623100117",
            "23791827400000021473381260028549782400006330,23791827400000021473381260028549782400006330",
            "33696827600000020000000000000010003205628243,33696827600000020000000000000010003205628243",
        ],
    )
    fun `should read both barcode and digitable`(digitable: String, expectedBarCode: String) {
        val barcode = BarCode.detect(digitable)
        assertEquals(expectedBarCode, barcode.number)
    }
}