package ai.friday.billpayment.app.bill.tracking

import ai.friday.billpayment.app.integrations.BillTrackingRepository
import ai.friday.billpayment.app.payment.DiscountType
import ai.friday.billpayment.app.payment.FineType
import ai.friday.billpayment.app.payment.InterestType
import ai.friday.billpayment.fixture.BillEventFixture
import ai.friday.billpayment.fixture.TrackableBillFixture
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.shouldBe
import io.mockk.called
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.EnumSource

class DefaultCreateTrackableBillServiceTest {
    private val repository: BillTrackingRepository = mockk(relaxUnitFun = true)

    private val test = DefaultCreateTrackableBillService(repository = repository)

    @Test
    fun `nao deve criar a TrackableBill quando a conta foi adicionada por DDA mas nao tem nem DESCONTO nem MULTA nem JUROS`() {
        val result = test.create(BillEventFixture.fichaAdded())

        result.isLeft() shouldBe true
        result.mapLeft { it shouldBe UntrackableEntity }

        verify { repository wasNot called }
    }

    @Test
    fun `nao deve criar a TrackableBill quando a conta soh tem DESCONTO mas seu tipo eh FREE`() {
        val result = test.create(
            BillEventFixture.fichaAdded(
                discount = TrackableBillFixture.discount(type = DiscountType.FREE),
                fine = null,
                interest = null,
            ),
        )

        result.isLeft() shouldBe true
        result.mapLeft { it shouldBe UntrackableEntity }

        verify { repository wasNot called }
    }

    @Test
    fun `nao deve criar a TrackableBill quando a conta soh tem MULTA mas seu tipo eh FREE`() {
        val result = test.create(
            BillEventFixture.fichaAdded(
                discount = null,
                fine = TrackableBillFixture.fine(type = FineType.FREE),
                interest = null,
            ),
        )

        result.isLeft() shouldBe true
        result.mapLeft { it shouldBe UntrackableEntity }

        verify { repository wasNot called }
    }

    @Test
    fun `nao deve criar a TrackableBill quando a conta soh tem JUROS mas seu tipo eh FREE`() {
        val result = test.create(
            BillEventFixture.fichaAdded(
                discount = null,
                fine = null,
                interest = TrackableBillFixture.interest(type = InterestType.FREE),
            ),
        )

        result.isLeft() shouldBe true
        result.mapLeft { it shouldBe UntrackableEntity }

        verify { repository wasNot called }
    }

    @ParameterizedTest
    @EnumSource(DiscountType::class, mode = EnumSource.Mode.EXCLUDE, names = ["FREE"])
    fun `deve criar a TrackableBill quando a conta tem DESCONTO e seu tipo nao eh FREE`(type: DiscountType) {
        val result = test.create(
            BillEventFixture.fichaAdded(
                discount = TrackableBillFixture.discount(type = type),
                fine = TrackableBillFixture.fine(type = FineType.FREE),
                interest = TrackableBillFixture.interest(type = InterestType.FREE),
            ),
        )

        result.isRight() shouldBe true
        result.map { it shouldBe Unit }

        verify { repository.create(any(), any<LocalDate>()) }
    }

    @ParameterizedTest
    @EnumSource(FineType::class, mode = EnumSource.Mode.EXCLUDE, names = ["FREE"])
    fun `deve criar a TrackableBill quando a conta tem MULTA e seu tipo nao eh FREE`(type: FineType) {
        val result = test.create(
            BillEventFixture.fichaAdded(
                discount = TrackableBillFixture.discount(type = DiscountType.FREE),
                fine = TrackableBillFixture.fine(type = type),
                interest = TrackableBillFixture.interest(type = InterestType.FREE),
            ),
        )

        result.isRight() shouldBe true
        result.map { it shouldBe Unit }

        verify { repository.create(any(), any<LocalDate>()) }
    }

    @ParameterizedTest
    @EnumSource(InterestType::class, mode = EnumSource.Mode.EXCLUDE, names = ["FREE"])
    fun `deve criar a TrackableBill quando a conta tem JUROS e seu tipo nao eh FREE`(type: InterestType) {
        val result = test.create(
            BillEventFixture.fichaAdded(
                discount = TrackableBillFixture.discount(type = DiscountType.FREE),
                fine = TrackableBillFixture.fine(type = FineType.FREE),
                interest = TrackableBillFixture.interest(type = type),
            ),
        )

        result.isRight() shouldBe true
        result.map { it shouldBe Unit }

        verify { repository.create(any(), any<LocalDate>()) }
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "2023-05-01, 2023-05-12",
            "2023-05-11, 2023-05-12",
            "2023-05-12, 2023-05-17",
            "2023-05-16, 2023-05-17",
            "2023-05-17, 2023-05-19",
            "2023-05-18, 2023-05-19",
            "2023-05-19, 2023-05-22",
            "2023-05-22, 2023-05-25",
            "2023-05-25, 2023-05-26",
            "2023-05-26, 2023-05-27",
        ],
    )
    fun `deve criar a TrackableBill com a proxima data de calculo correta`(today: String, expectedDate: String) {
        val event = BillEventFixture.fichaAdded(
            discount = TrackableBillFixture.discount(
                type = DiscountType.FIXED_UNTIL_DATE,
                date1 = LocalDate.of(2023, 5, 11),
                date2 = LocalDate.of(2023, 5, 16),
                date3 = LocalDate.of(2023, 5, 18),
            ),
            dueDate = LocalDate.of(2023, 5, 19),
            fine = TrackableBillFixture.fine(
                type = FineType.VALUE,
                date = LocalDate.of(2023, 5, 22),
            ),
            interest = TrackableBillFixture.interest(
                type = InterestType.VALUE,
                date = LocalDate.of(2023, 5, 25),
            ),
        )

        val result = withGivenDateTime(
            ZonedDateTime.of(
                LocalDate.parse(today, DateTimeFormatter.ISO_DATE),
                LocalTime.NOON,
                brazilTimeZone,
            ),
        ) {
            test.create(event)
        }

        result.isRight() shouldBe true
        result.map { it shouldBe Unit }

        val slot = slot<LocalDate>()
        verify { repository.create(any(), capture(slot)) }

        slot.captured shouldBe LocalDate.parse(expectedDate, DateTimeFormatter.ISO_DATE)
    }

    @Test
    fun `should work even with no data`() {
        val result = test.create(
            BillEventFixture.fichaAdded(fine = null, interest = null, discount = null, abatement = null),
        )

        result.isLeft() shouldBe true
        result.mapLeft { it shouldBe UntrackableEntity }

        verify { repository wasNot called }
    }
}