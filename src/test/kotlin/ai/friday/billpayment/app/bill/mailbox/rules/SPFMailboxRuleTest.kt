package ai.friday.billpayment.app.bill.mailbox.rules

import ai.friday.billpayment.app.bill.mailbox.MailboxSecurityValidationErrorLevel
import ai.friday.billpayment.app.bill.mailbox.MailboxValidateRequestFixture
import ai.friday.billpayment.app.bill.mailbox.MailboxValidateRequestFixture.receipt
import io.kotest.matchers.shouldBe
import io.via1.communicationcentre.app.receipt.Status
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class SPFMailboxRuleTest {
    private lateinit var rule: SPFMailboxRule

    @BeforeEach
    fun setUp() {
        rule = SPFMailboxRule()
    }

    @Test
    fun `Deve retornar sucesso caso pedido de addBill venha do backoffice`() {
        val request = MailboxValidateRequestFixture.validateRequest("abc.com.br")

        val result = rule.execute(request)

        result.isRight() shouldBe true
    }

    @Test
    fun `Deve retornar sucesso caso SPFVerdict seja igual Pass`() {
        val request = MailboxValidateRequestFixture.validateRequest("abc.com.br", receipt = receipt())

        val result = rule.execute(request)

        result.isRight() shouldBe true
    }

    @ParameterizedTest
    @EnumSource(value = Status::class, mode = EnumSource.Mode.EXCLUDE, names = ["PASS"])
    fun `Deve retornar error caso SPFVerdict seja diferente de Pass`(status: Status) {
        val request = MailboxValidateRequestFixture.validateRequest("abc.com.br", receipt = receipt(status))

        val result = rule.execute(request)

        result.isLeft() shouldBe true
        result.mapLeft {
            it.level shouldBe MailboxSecurityValidationErrorLevel.BLOCK
        }
    }
}