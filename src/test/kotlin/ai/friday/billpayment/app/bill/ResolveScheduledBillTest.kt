package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billPaymentScheduled
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate
import java.util.stream.Stream
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource

class ResolveScheduledBillTest {

    private val billEventPublisher = mockk<BillEventPublisher>(relaxed = true)

    private val resolveScheduledBill = ResolveScheduledBill(
        billEventPublisher = billEventPublisher,
    )

    @ParameterizedTest
    @MethodSource("shouldNotUnscheduleForUpdatedRegisterData")
    fun `nao deve cancelar o agendamento quando a atualizacao do registro for`(updatedRegisterData: UpdatedRegisterData) {
        val bill = Bill.build(billAddedFicha, billPaymentScheduled)

        resolveScheduledBill.resolve(updatedRegisterData, bill)

        verify {
            billEventPublisher wasNot Called
        }
    }

    @ParameterizedTest
    @MethodSource("shouldUnscheduleForUpdatedRegisterData")
    fun `nao deve cancelar o agendamento quando a bill nao estiver agendada`(updatedRegisterData: UpdatedRegisterData) {
        val bill = Bill.build(billAddedFicha)

        resolveScheduledBill.resolve(updatedRegisterData, bill)

        verify {
            billEventPublisher wasNot Called
        }
    }

    @ParameterizedTest
    @MethodSource("shouldUnscheduleForUpdatedRegisterData")
    fun `deve cancelar o agendamento quando a bill estiver agendada`(updatedRegisterData: UpdatedRegisterData, expectedReason: ScheduleCanceledReason) {
        val bill = Bill.build(billAddedFicha, billPaymentScheduled)

        resolveScheduledBill.resolve(updatedRegisterData, bill)

        verify {
            billEventPublisher.publish(
                bill,
                withArg<BillPaymentScheduleCanceled> {
                    it.billId shouldBe bill.billId
                    it.walletId shouldBe bill.walletId
                    it.actionSource shouldBe ActionSource.System
                    it.reason shouldBe expectedReason
                    it.batchSchedulingId shouldBe bill.schedule!!.batchSchedulingId
                },
            )
        }
    }

    @ParameterizedTest
    @EnumSource(ScheduleCanceledReason::class)
    fun `deve cancelar o agendamento com o motivo informado quando a bill estiver agendada`(expectedReason: ScheduleCanceledReason) {
        val bill = Bill.build(billAddedFicha, billPaymentScheduled)

        resolveScheduledBill.resolve(bill, expectedReason)

        verify {
            billEventPublisher.publish(
                bill,
                withArg<BillPaymentScheduleCanceled> {
                    it.billId shouldBe bill.billId
                    it.walletId shouldBe bill.walletId
                    it.actionSource shouldBe ActionSource.System
                    it.reason shouldBe expectedReason
                    it.batchSchedulingId shouldBe bill.schedule!!.batchSchedulingId
                },
            )
        }
    }

    companion object {
        @JvmStatic
        fun shouldNotUnscheduleForUpdatedRegisterData(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    UpdatedRegisterData.NewTotalAmount(
                        amount = 5980,
                        abatement = 0,
                        discount = 2547,
                        interest = 1794,
                        fine = 2990,
                        amountTotal = 1958,
                        lastSettleDate = "2023-03-12",
                        registrationUpdateNumber = null,
                        source = FinancialServiceGateway.CELCOIN,
                        amountCalculationValidUntil = null,
                    ),
                ),
                Arguments.arguments(
                    UpdatedRegisterData.NewAmountCalculationValidUntil(
                        amountCalculationValidUntil = null,
                    ),
                ),
                Arguments.arguments(
                    UpdatedRegisterData.NewDueDate(
                        dueDate = LocalDate.now(),
                        effectiveDueDate = LocalDate.now(),
                    ),
                ),
                Arguments.arguments(
                    UpdatedRegisterData.NewAmountCalculationOption(
                        amountCalculationOption = BillTrackingCalculateOptions.CALCULATE,
                    ),
                ),
            )
        }

        @JvmStatic
        fun shouldUnscheduleForUpdatedRegisterData(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(UpdatedRegisterData.NotPayableBill, ScheduleCanceledReason.BILL_NOT_PAYABLE),
                Arguments.arguments(UpdatedRegisterData.AlreadyPaidBill(0), ScheduleCanceledReason.BILL_ALREADY_PAID),
            )
        }
    }
}