package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.ErrorSource
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.pix.PixTransactionError
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billPaymentFailedRetryable
import ai.friday.billpayment.billPaymentSchedulePostponedByInsufficientFunds
import ai.friday.billpayment.billPaymentSchedulePostponedDueToLimitReached
import ai.friday.billpayment.billPaymentScheduleStarted
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.billRegisterUpdatedAlreadyPaid
import ai.friday.billpayment.billRegisterUpdatedNotPayable
import ai.friday.billpayment.billScheduleCanceled
import ai.friday.billpayment.billScheduleCanceledAfterHours
import ai.friday.billpayment.billScheduleCanceledAmountHigherThanDailyLimit
import ai.friday.billpayment.fichaCompensacaoCreditCardAdded
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.invoicePaid
import ai.friday.billpayment.invoicePaymentFailedRetryable
import ai.friday.billpayment.invoicePaymentStarted
import ai.friday.billpayment.pixKeyAdded
import ai.friday.billpayment.pixKeyPaymentFailed
import ai.friday.billpayment.pixKeyPaymentStarted
import ai.friday.billpayment.withEarlyAccess
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class WarningCodeTest {

    private val acquirerPaymentFailure = billPaymentFailedRetryable.copy(errorSource = ErrorSource.ACQUIRER)
    private val unknownPaymentFailure = billPaymentFailedRetryable.copy(errorSource = ErrorSource.UNKNOWN)
    private val bankPaymentFailure = billPaymentFailedRetryable.copy(errorSource = ErrorSource.BANK)
    private val invoiceRefunded = invoicePaymentFailedRetryable.copy(errorSource = ErrorSource.BANK)
    private val invoiceRefundedNotRetryable =
        invoicePaymentFailedRetryable.copy(errorSource = ErrorSource.BANK, retryable = false)

    @Test
    fun `should get no warn when bill is added`() {
        val bill = Bill.build(billAdded)
        bill.getWarningCode() shouldBe WarningCode.NONE
    }

    @Test
    fun `should get payment warning code when bill has acquirer payment failure`() {
        val bill = Bill.build(billAdded, acquirerPaymentFailure)
        bill.getWarningCode() shouldBe WarningCode.CREDIT_CARD_PAYMENT
    }

    @Test
    fun `should get server error warning code when bill has bank payment failure`() {
        val bill = Bill.build(billAdded, bankPaymentFailure)
        bill.getWarningCode() shouldBe WarningCode.SERVER_ERROR
    }

    @Test
    fun `should get last payment error warn code`() {
        val bill = Bill.build(billAdded, acquirerPaymentFailure, bankPaymentFailure)
        bill.getWarningCode() shouldBe WarningCode.SERVER_ERROR
    }

    @Test
    fun `should get no warning code when bill status changes to alreadyPaid`() {
        val bill = Bill.build(billAdded, billRegisterUpdatedAlreadyPaid)
        bill.getWarningCode() shouldBe WarningCode.NONE
    }

    @Test
    fun `should get settlement warning code when bill status changes to notPayable`() {
        val bill = Bill.build(billAdded, billRegisterUpdatedNotPayable)
        bill.getWarningCode() shouldBe WarningCode.SETTLEMENT
    }

    @Test
    fun `should get settlement warning code when bill status changes to alreadyPaid during a transaction`() {
        val bill = Bill.build(billAdded, billPaymentStart, billRegisterUpdatedAlreadyPaid, billPaymentFailedRetryable)
        bill.getWarningCode() shouldBe WarningCode.NONE
    }

    @Test
    fun `should get settlement warning code when bill status changes to notPayable during a transaction`() {
        val bill = Bill.build(billAdded, billPaymentStart, billRegisterUpdatedNotPayable, billPaymentFailedRetryable)
        bill.getWarningCode() shouldBe WarningCode.SETTLEMENT
    }

    @Test
    fun `should get no warn when a new payment has started after a payment failure`() {
        val bill = Bill.build(billAdded, acquirerPaymentFailure, billPaymentStart)
        bill.getWarningCode() shouldBe WarningCode.NONE
    }

    @Test
    fun `deve remover o warn de pagamento desagendado por falta de saldo ao reagendar a conta`() {
        val bill = Bill.build(
            billAdded,
            billPaymentScheduled,
            billScheduleCanceled.copy(reason = ScheduleCanceledReason.EXPIRATION),
            billPaymentScheduled,
        )

        bill.getWarningCode() shouldBe WarningCode.NONE
    }

    @Test
    fun `should return callback warn when invoice is refunded`() {
        val bill = Bill.build(invoiceAdded, invoicePaymentStarted, invoicePaid, invoiceRefunded)
        bill.getWarningCode() shouldBe WarningCode.REFUNDED
    }

    @Test
    fun `should return callback warn when invoice is refunded and not retryable`() {
        val bill = Bill.build(invoiceAdded, invoicePaymentStarted, invoicePaid, invoiceRefundedNotRetryable)
        bill.getWarningCode() shouldBe WarningCode.REFUNDED
    }

    @Test
    fun `should return server error when error source is unknown`() {
        val bill = Bill.build(billAdded, unknownPaymentFailure)
        bill.getWarningCode() shouldBe WarningCode.SERVER_ERROR
    }

    @Test
    fun `should return payment when payment fails then bill schedule is postponed by insufficient funds`() {
        val bill = Bill.build(
            billAdded,
            billPaymentScheduled,
            billPaymentScheduleStarted,
            acquirerPaymentFailure,
            billPaymentSchedulePostponedByInsufficientFunds,
        )
        bill.getWarningCode() shouldBe WarningCode.CREDIT_CARD_PAYMENT
    }

    @Test
    fun `should return UNSCHEDULED_DUE_PAYMENT_LIMIT_TIME when bill schedule is canceled due to after hours limit`() {
        val bill = Bill.build(billAdded, billPaymentScheduled, billScheduleCanceledAfterHours)
        bill.getWarningCode() shouldBe WarningCode.UNSCHEDULED_DUE_PAYMENT_LIMIT_TIME
    }

    @Test
    fun `should return none when bill schedule is canceled after hours then payment starts again`() {
        val bill = Bill.build(
            billAdded,
            billPaymentScheduled,
            billScheduleCanceledAfterHours,
            billPaymentScheduled,
            billPaymentScheduleStarted,
            billPaymentStart,
        )
        bill.getWarningCode() shouldBe WarningCode.NONE
    }

    @Test
    fun `should return UNSCHEDULED_DUE_AMOUNT_HIGHER_THAN_DAILY_LIMIT when bill schedule is canceled due to amount higher than daily limit`() {
        val bill = Bill.build(billAdded, billPaymentScheduled, billScheduleCanceledAmountHigherThanDailyLimit)
        bill.getWarningCode() shouldBe WarningCode.UNSCHEDULED_DUE_AMOUNT_HIGHER_THAN_DAILY_LIMIT
    }

    @Test
    fun `should return REFUNDED_DESTINATION_NOT_ALLOWED_AMOUNT when invoice is refunded and reason is DESTINATION_NOT_ALLOWED_AMOUNT`() {
        val bill = Bill.build(
            invoiceAdded,
            invoicePaymentStarted,
            invoicePaid,
            PaymentRefunded(
                billId = invoiceAdded.billId,
                walletId = invoiceAdded.walletId,
                reason = PaymentRefundedReason.DESTINATION_NOT_ALLOWED_AMOUNT,
                gateway = FinancialServiceGateway.ARBI,
                transactionId = TransactionId("12344556"),
            ),
        )
        bill.getWarningCode() shouldBe WarningCode.REFUNDED_DESTINATION_NOT_ALLOWED_AMOUNT
    }

    @Test
    fun `conta desagendada por mudanca de valor tem que ter o warning code UNSCHEDULED_DUE_AMOUNT_CHANGED`() {
        val bill = Bill.build(
            invoiceAdded,
            invoicePaymentStarted,
            invoicePaymentFailedRetryable,
            BillPaymentScheduleCanceled(
                billId = invoiceAdded.billId,
                walletId = billAdded.walletId,
                actionSource = ActionSource.Api(accountId = AccountId(ACCOUNT_ID)),
                reason = ScheduleCanceledReason.BILL_AMOUNT_CHANGED,
                batchSchedulingId = BatchSchedulingId(),
            ),
        )
        bill.getWarningCode() shouldBe WarningCode.UNSCHEDULED_DUE_AMOUNT_CHANGED
    }

    @Test
    fun `conta desagendada por falha no pagamento via cartao de credito deve ter o warning code CANT_PAY_WITH_CURRENT_CREDIT_CARD`() {
        val bill = Bill.build(
            invoiceAdded,
            invoicePaymentStarted,
            invoicePaymentFailedRetryable,
            BillPaymentScheduleCanceled(
                billId = invoiceAdded.billId,
                walletId = billAdded.walletId,
                actionSource = ActionSource.Webapp(role = Role.OWNER),
                reason = ScheduleCanceledReason.CANT_PAY_WITH_CURRENT_CREDIT_CARD,
                batchSchedulingId = BatchSchedulingId(),
            ),
        )
        bill.getWarningCode() shouldBe WarningCode.CANT_PAY_WITH_CURRENT_CREDIT_CARD
    }

    @ParameterizedTest
    @EnumSource(
        value = PaymentRefundedReason::class,
        mode = EnumSource.Mode.EXCLUDE,
        names = ["DESTINATION_NOT_ALLOWED_AMOUNT"],
    )
    fun `should return REFUNDED when invoice is refunded and reason is not DESTINATION_NOT_ALLOWED_AMOUNT`(reason: PaymentRefundedReason) {
        val bill = Bill.build(
            invoiceAdded,
            invoicePaymentStarted,
            invoicePaid,
            PaymentRefunded(
                billId = invoiceAdded.billId,
                walletId = invoiceAdded.walletId,
                reason = reason,
                gateway = FinancialServiceGateway.ARBI,
                transactionId = TransactionId("12344556"),
            ),
        )
        bill.getWarningCode() shouldBe WarningCode.REFUNDED
    }

    @Test
    fun `should return none when bill schedule is canceled due to amount higher than daily limit then payment starts again`() {
        val bill = Bill.build(
            billAdded,
            billPaymentScheduled,
            billScheduleCanceledAmountHigherThanDailyLimit,
            billPaymentScheduled,
            billPaymentScheduleStarted,
            billPaymentStart,
        )
        bill.getWarningCode() shouldBe WarningCode.NONE
    }

    @Test
    fun `should return POSTPONED_DUE_LIMIT_REACHED when bill schedule is postponed due to limit reached`() {
        val bill = Bill.build(billAdded, billPaymentScheduled, billPaymentSchedulePostponedDueToLimitReached)
        bill.getWarningCode() shouldBe WarningCode.POSTPONED_DUE_LIMIT_REACHED
    }

    @Test
    fun `should return none when bill schedule is postponed due to limit reached and then payment starts again`() {
        val bill = Bill.build(
            billAdded,
            billPaymentScheduled,
            billPaymentSchedulePostponedDueToLimitReached,
            billPaymentScheduleStarted,
            billPaymentStart,
        )
        bill.getWarningCode() shouldBe WarningCode.NONE
    }

    @Test
    fun `deve retornar chave pix invalida quando a chave pix esta invalida`() {
        val bill = Bill.build(pixKeyAdded, pixKeyPaymentStarted, pixKeyPaymentFailed)
        bill.getWarningCode() shouldBe WarningCode.SETTLEMENT_INVALID_PIX_KEY
    }

    @Test
    fun `deve retornar PIX_PAYMENT quando ocorrer falha na transacao pix`() {
        val bill = Bill.build(
            pixKeyAdded,
            pixKeyPaymentStarted,
            pixKeyPaymentFailed.copy(
                errorSource = ErrorSource.PAYMENT_ARBI,
                errorDescription = PixTransactionError.PaymentGenericTemporaryError.code,
            ),
        )
        bill.getWarningCode() shouldBe WarningCode.PIX_PAYMENT
    }

    @Test
    fun `deve retornar BENEFICIARY_UPDATE_REQUIRED quando reativar uma conta do tipo BENEFICIARY_ONLY com valor zerado`() {
        val bill = Bill.build(
            fichaCompensacaoCreditCardAdded.copy(
                amountCalculationModel = AmountCalculationModel.BENEFICIARY_ONLY,
                amount = 0,
                amountTotal = 0,
            ),
            BillIgnored(
                billId = fichaCompensacaoCreditCardAdded.billId,
                walletId = fichaCompensacaoCreditCardAdded.walletId,
                actionSource = ActionSource.System,
                removeFromListing = false,
                removeFromRecurrence = false,
            ),
            BillReactivated(
                billId = fichaCompensacaoCreditCardAdded.billId,
                walletId = fichaCompensacaoCreditCardAdded.walletId,
                actionSource = ActionSource.System,
            ),
        )

        bill.getWarningCode() shouldBe WarningCode.BENEFICIARY_UPDATE_REQUIRED
    }

    @Test
    fun `deve retornar NONE quando reativar uma conta do tipo BENEFICIARY_ONLY com valor maior que zero`() {
        val bill = Bill.build(
            fichaCompensacaoCreditCardAdded.copy(
                amountCalculationModel = AmountCalculationModel.BENEFICIARY_ONLY,
                amount = 10,
                amountTotal = 10,
            ),
            BillIgnored(
                billId = fichaCompensacaoCreditCardAdded.billId,
                walletId = fichaCompensacaoCreditCardAdded.walletId,
                actionSource = ActionSource.System,
                removeFromListing = false,
                removeFromRecurrence = false,
            ),
            BillReactivated(
                billId = fichaCompensacaoCreditCardAdded.billId,
                walletId = fichaCompensacaoCreditCardAdded.walletId,
                actionSource = ActionSource.System,
            ),
        )

        bill.getWarningCode() shouldBe WarningCode.NONE
    }

    @Test
    fun `deve retornar BENEFICIARY_UPDATE_REQUIRED quando receber uma conta do tipo BENEFICIARY_ONLY com valor zerado`() {
        val bill = Bill.build(
            fichaCompensacaoCreditCardAdded.copy(
                amountCalculationModel = AmountCalculationModel.BENEFICIARY_ONLY,
                amount = 0,
                amountTotal = 0,
            ),
        )

        bill.getWarningCode() shouldBe WarningCode.BENEFICIARY_UPDATE_REQUIRED
    }

    @Test
    fun `deve retornar NONE quando receber uma conta do tipo BENEFICIARY_ONLY com valor maior que zero`() {
        val bill = Bill.build(
            fichaCompensacaoCreditCardAdded.copy(
                amountCalculationModel = AmountCalculationModel.BENEFICIARY_ONLY,
                amount = 10,
                amountTotal = 10,
            ),
        )

        bill.getWarningCode() shouldBe WarningCode.NONE
    }

    @Test
    fun `deve retornar BENEFICIARY_UPDATE_REQUIRED quando receber uma atualização de conta do tipo BENEFICIARY_ONLY com valor zerado`() {
        val bill = Bill.build(
            fichaCompensacaoCreditCardAdded.copy(
                amountCalculationModel = AmountCalculationModel.BENEFICIARY_ONLY,
                amount = 10,
                amountTotal = 10,
            ),
            RegisterUpdated(
                billId = fichaCompensacaoCreditCardAdded.billId,
                walletId = fichaCompensacaoCreditCardAdded.walletId,
                updatedRegisterData = UpdatedRegisterData.NewTotalAmount(
                    amount = 0,
                    amountTotal = 0,
                    abatement = 0,
                    discount = 0,
                    interest = 0,
                    fine = 0,
                    lastSettleDate = "",
                    registrationUpdateNumber = null,
                    source = null,
                ),
                actionSource = ActionSource.System,
            ),
        )

        bill.getWarningCode() shouldBe WarningCode.BENEFICIARY_UPDATE_REQUIRED
    }

    @Test
    fun `deve retornar NONE quando receber uma atualização de conta do tipo BENEFICIARY_ONLY com valor maior que zero`() {
        val bill = Bill.build(
            fichaCompensacaoCreditCardAdded.copy(
                amountCalculationModel = AmountCalculationModel.BENEFICIARY_ONLY,
                amount = 0,
                amountTotal = 0,
            ),
            RegisterUpdated(
                billId = fichaCompensacaoCreditCardAdded.billId,
                walletId = fichaCompensacaoCreditCardAdded.walletId,
                updatedRegisterData = UpdatedRegisterData.NewTotalAmount(
                    amount = 10,
                    amountTotal = 10,
                    discount = 0,
                    abatement = 0,
                    interest = 0,
                    fine = 0,
                    lastSettleDate = "",
                    registrationUpdateNumber = null,
                    source = null,
                ),
                actionSource = ActionSource.System,
            ),
        )

        bill.getWarningCode() shouldBe WarningCode.NONE
    }

    @Test
    fun `deve retornar chave MAX_PAYMENT_LIMIT_EXCEEDED quando o valor for maior que 250mil`() {
        withEarlyAccess(AccountId(billAddedFicha.walletId.value)) {
            val bill = Bill.build(billAddedFicha.copy(amount = 250_000_01L, amountTotal = 250_000_01L))
            bill.getWarningCode() shouldBe WarningCode.MAX_PAYMENT_LIMIT_EXCEEDED
        }
    }

    @Test
    fun `deve retornar chave ZERO_AMOUNT quando o valor for zero`() {
        withEarlyAccess(AccountId(billAddedFicha.walletId.value)) {
            val bill = Bill.build(billAddedFicha.copy(amount = 0L, amountTotal = 0L))
            bill.getWarningCode() shouldBe WarningCode.ZERO_AMOUNT
        }
    }
}