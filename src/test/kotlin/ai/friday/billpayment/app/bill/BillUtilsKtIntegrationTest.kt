package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.WalletFixture
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.shouldBe
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.unmockkObject
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

@MicronautTest
class BillUtilsKtIntegrationTest {

    val accountId = AccountId(ACCOUNT_ID)

    val walletFixture = WalletFixture()

    val wallet = walletFixture.buildWallet()

    @ParameterizedTest
    @ValueSource(
        strings = [
            "**************",
            "**************",
            "**************",
            "**************",
            "**************",
            "**************",
            "**************",
            "**************",
            "**************",
            "**************",
            "**************",
            "**************",
            "**************",
            "**************",
            "**************",
            "**************",
            "**************",
            "**************",
        ],
    )
    fun `se o boleto for de um cnpj não conviável, não é confiável`(cnpj: String) {
        val activeBill = getActiveBill(
            accountId = accountId,
            recipient = Recipient(name = "any", document = cnpj),
        )
        activeBill.isOwnTrustedBill(activeBill.payerDocument!!).shouldBeFalse()
    }

    @Test
    fun `deve retornar todos os metodos de pagamento quando a restricao de metodos de pagamento estiver desabilitada`() {
        val activeBill = getActiveBill(
            accountId = accountId,
        )

        val availablePaymentMethods = withRestrictedPaymentMethods(false) {
            activeBill.availablePaymentMethods(wallet.founder)
        }

        availablePaymentMethods.shouldNotBeEmpty().shouldBe(
            listOf(
                PaymentMethodType.BALANCE,
                PaymentMethodType.CREDIT_CARD,
                PaymentMethodType.EXTERNAL,
            ),
        )
    }

    @Test
    fun `nao deve retornar cartao de credito como metodo de pagamento quando a restricao de metodos de pagamento estiver habilitada e a conta nao for confiavel`() {
        val activeBill = getActiveBill(
            accountId = accountId,
            recipient = Recipient(name = "any", document = "**************"),
        )

        val availablePaymentMethods = withRestrictedPaymentMethods(true) {
            activeBill.availablePaymentMethods(wallet.founder)
        }

        availablePaymentMethods.shouldNotBeEmpty().shouldBe(
            listOf(
                PaymentMethodType.BALANCE,
                PaymentMethodType.EXTERNAL,
            ),
        )
    }
}

fun <T> withRestrictedPaymentMethods(enabled: Boolean, toBeExecuted: () -> T): T {
    mockkObject(BillUtilsService)
    every { BillUtilsService.restrictPaymentMethods } returns enabled
    try {
        return toBeExecuted()
    } finally {
        unmockkObject(BillUtilsService)
    }
}