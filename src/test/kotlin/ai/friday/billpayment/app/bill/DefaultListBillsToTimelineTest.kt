package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.bill.duplication.PossibleDuplicate
import ai.friday.billpayment.app.bill.duplication.PossibleDuplicateBillService
import ai.friday.billpayment.app.bill.tracking.BillTrackingCalculateHandlerService
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.FindBillsCriteria
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.getActiveBillFicha
import ai.friday.billpayment.getActiveInvestmentBill
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

class DefaultListBillsToTimelineTest {

    private val billRepository = mockk<BillRepository>()

    private val walletRepository = mockk<WalletRepository>()

    private val walletFixture = WalletFixture(defaultWalletId = billAddedFicha.walletId)

    private val wallet = walletFixture.buildWallet(otherMembers = listOf(walletFixture.ultraLimitedParticipant))

    private val possibleDuplicateBillService: PossibleDuplicateBillService = mockk {
        every {
            check(any<List<BillView>>())
        } returns emptyMap()
    }

    private val billTrackingService: BillTrackingCalculateHandlerService = mockk {
        every {
            isBillOutdated(any())
        } returns false
    }

    private val updateAmountBill = mockk<UpdateAmountBill>() {
        every {
            updateAmountIfExpired(any())
        } returnsArgument 0
    }

    private val service = DefaultListBillsToTimeline(
        billRepository = billRepository,
        possibleDuplicateBillService = possibleDuplicateBillService,
        billTrackingService = billTrackingService,
        updateAmountBill = updateAmountBill,
        walletRepository = walletRepository,
    )

    private val activeBillFicha = getActiveBillFicha(walletId = wallet.id).copy(
        effectiveDueDate = getLocalDate().plusDays(10),
    )

    private val overdueBillFicha = getActiveBillFicha(walletId = wallet.id).copy(
        effectiveDueDate = getLocalDate().minusDays(10),
    )

    private val activeInvestmentBill = getActiveInvestmentBill(walletId = wallet.id)

    private val defaultCriteria = FindBillsCriteria(from = null, to = null)

    @BeforeEach
    fun setup() {
        every {
            walletRepository.findWallet(any())
        } returns wallet
    }

    @Test
    fun `deve retornar uma lista vazia quando nao encontra conta nenhuma`() {
        every {
            billRepository.findByWallet(wallet.id, any())
        } returns emptyList()

        val bills = service.findBills(
            currentWalletId = wallet.id,
            accountId = wallet.founder.accountId,
            criteria = defaultCriteria,
        )

        bills.shouldBeEmpty()

        verify {
            updateAmountBill wasNot Called
        }
    }

    @Test
    fun `deve buscar as bills da carteira`() {
        every {
            billRepository.findByWallet(wallet.id, any())
        } returns listOf(activeBillFicha, overdueBillFicha)

        val bills = service.findBills(
            currentWalletId = wallet.id,
            accountId = wallet.founder.accountId,
            criteria = defaultCriteria,
        )

        bills.shouldNotBeEmpty()
        bills.shouldHaveSize(2)

        with(bills.first()) {
            bill shouldBe activeBillFicha
        }

        with(bills.last()) {
            bill shouldBe overdueBillFicha
        }

        verify {
            updateAmountBill.updateAmountIfExpired(activeBillFicha)
            updateAmountBill.updateAmountIfExpired(overdueBillFicha)
        }
    }

    @Test
    fun `nao deve retornar a bill que estiver com o status MOVED`() {
        every {
            billRepository.findByWallet(wallet.id, any())
        } returns listOf(
            activeBillFicha.copy(
                status = BillStatus.MOVED,
            ),
        )

        val bills = service.findBills(
            currentWalletId = wallet.id,
            accountId = wallet.founder.accountId,
            criteria = defaultCriteria,
        )

        bills.shouldBeEmpty()

        verify {
            updateAmountBill wasNot Called
        }
    }

    @Test
    fun `nao deve retornar a subscription fee que dos meses futuros`() {
        every {
            billRepository.findByWallet(wallet.id, any())
        } returns listOf(
            activeBillFicha.copy(
                subscriptionFee = true,
                dueDate = getLocalDate().plusDays(32),
            ),
        )

        val bills = service.findBills(
            currentWalletId = wallet.id,
            accountId = wallet.founder.accountId,
            criteria = defaultCriteria,
        )

        bills.shouldBeEmpty()

        verify {
            updateAmountBill wasNot Called
        }
    }

    @Test
    fun `deve retornar a subscription fee do mes corrente`() {
        every {
            billRepository.findByWallet(wallet.id, any())
        } returns listOf(
            activeBillFicha.copy(
                subscriptionFee = true,
                dueDate = getLocalDate().plusDays(31),
            ),
        )

        val bills = service.findBills(
            currentWalletId = wallet.id,
            accountId = wallet.founder.accountId,
            criteria = defaultCriteria,
        )

        bills.shouldNotBeEmpty()
        bills.shouldHaveSize(1)
        with(bills.first()) {
            bill.subscriptionFee shouldBe true
        }
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `deve informar se a bill esta desatualizada`(isOutdated: Boolean) {
        every {
            billRepository.findByWallet(wallet.id, any())
        } returns listOf(activeBillFicha)

        every {
            billTrackingService.isBillOutdated(activeBillFicha)
        } returns isOutdated

        val bills = service.findBills(
            currentWalletId = wallet.id,
            accountId = wallet.founder.accountId,
            criteria = defaultCriteria,
        )

        bills.shouldNotBeEmpty()
        bills.shouldHaveSize(1)

        with(bills.first()) {
            bill shouldBe activeBillFicha
            trackingOutdated shouldBe isOutdated
        }
    }

    @Test
    fun `deve informar se as bills que possivelmente sao duplicadas`() {
        every {
            billRepository.findByWallet(wallet.id, any())
        } returns listOf(activeBillFicha, overdueBillFicha)

        every {
            possibleDuplicateBillService.check(listOf(activeBillFicha, overdueBillFicha))
        } returns mapOf(
            activeBillFicha to setOf(
                PossibleDuplicate(
                    billId = overdueBillFicha.billId,
                    dueDate = overdueBillFicha.effectiveDueDate,
                ),
            ),
            overdueBillFicha to setOf(
                PossibleDuplicate(
                    billId = activeBillFicha.billId,
                    dueDate = activeBillFicha.effectiveDueDate,
                ),
            ),
        )

        val bills = service.findBills(
            currentWalletId = wallet.id,
            accountId = wallet.founder.accountId,
            criteria = defaultCriteria,
        )

        bills.shouldNotBeEmpty()
        bills.shouldHaveSize(2)

        with(bills.first()) {
            bill shouldBe activeBillFicha
            with(possibleDuplicateBills.shouldHaveSize(1).first()) {
                billId shouldBe overdueBillFicha.billId
                dueDate shouldBe overdueBillFicha.effectiveDueDate
            }
        }

        with(bills.last()) {
            bill shouldBe overdueBillFicha
            with(possibleDuplicateBills.shouldHaveSize(1).first()) {
                billId shouldBe activeBillFicha.billId
                dueDate shouldBe activeBillFicha.effectiveDueDate
            }
        }
    }

    @Test
    fun `nao deve retornar a bill quando o membro não possui permissao de visualizar a bill`() {
        every {
            billRepository.findByWallet(wallet.id, any())
        } returns listOf(
            activeBillFicha,
            activeInvestmentBill,
        )

        val bills = service.findBills(
            currentWalletId = wallet.id,
            accountId = walletFixture.ultraLimitedParticipant.accountId,
            criteria = defaultCriteria,
        )

        bills.shouldBeEmpty()
    }
}