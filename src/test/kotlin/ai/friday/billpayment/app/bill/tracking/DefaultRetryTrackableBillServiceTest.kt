package ai.friday.billpayment.app.bill.tracking

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.TrackableBillFixture
import ai.friday.billpayment.app.integrations.BillTrackingRepository
import ai.friday.billpayment.billPaymentFailed
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDate
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import reactor.core.publisher.Mono

class DefaultRetryTrackableBillServiceTest {
    private lateinit var test: DefaultRetryTrackableBillService

    private lateinit var repository: BillTrackingRepository

    @BeforeEach
    fun setUp() {
        repository = mockk()

        test = DefaultRetryTrackableBillService(repository = repository)
    }

    @Test
    fun `should do nothing when there is any TrackableBill`() {
        every { repository.findById(any()) } returns Mono.empty()

        val event = billPaymentFailed

        val result = test.retry(event)

        result.isRight() shouldBe true

        verify { repository.findById(event.billId) }
        verify(exactly = 0) { repository.update(any(), any()) }
    }

    @Test
    fun `should update TrackableBill when it does exists`() {
        val trackable = TrackableBillFixture.create()

        every { repository.findById(any()) } returns Mono.just(trackable)
        every { repository.update(any(), any()) } just Runs

        val event = billPaymentFailed

        val result = test.retry(event)

        result.isRight() shouldBe true

        verify { repository.findById(event.billId) }
        val slot = slot<LocalDate>()
        verify { repository.update(any(), capture(slot)) }
        slot.captured shouldBe getLocalDate().plusDays(1)
    }

    @Test
    fun `should return error when repository throws an error`() {
        every { repository.findById(any()) } throws NoStackTraceException()

        val event = billPaymentFailed

        val result = test.retry(event)

        result.isLeft() shouldBe true
        result.mapLeft { it.ex.shouldBeTypeOf<NoStackTraceException>() }

        verify { repository.findById(event.billId) }
        verify(exactly = 0) { repository.update(any(), any()) }
    }
}