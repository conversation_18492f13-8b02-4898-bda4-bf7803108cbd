package ai.friday.billpayment.app.bill

import ai.friday.billpayment.EMAIL_ADDRESS
import ai.friday.billpayment.adapters.arbi.ArbiValidationResponse
import ai.friday.billpayment.adapters.arbi.CIPSitTitPgto
import ai.friday.billpayment.adapters.celcoin.CelcoinBillValidationResponse
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ConcessionariaService as ConcessionariaService1
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.BillValidationService
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.canView
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billDenied
import ai.friday.billpayment.billIgnored
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billRegisterData
import ai.friday.billpayment.fichaRegisterData
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.BILL_ID
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.CONCESSIONARIA_DIGITABLE_LINE
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.DOCUMENT_2
import ai.friday.billpayment.integration.NAME
import ai.friday.billpayment.integration.PREFECTURE_BARCODE_LINE
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.result.shouldBeFailure
import io.kotest.matchers.result.shouldBeSuccess
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDate
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource
import org.junit.jupiter.params.provider.ValueSource

internal class ConcessionariaServiceTest {
    private val billEventRepository = mockk<BillEventRepository>()
    private val updateBillService = mockk<UpdateBillService>(relaxed = true) {
        every { publishEvent(any(), any()) } answers {
            firstArg<Bill>().apply(secondArg())
        }
    }
    private val validationService = mockk<BillValidationService>()
    private val billRepository: BillRepository = mockk()
    private val resolveScheduledBill: ResolveScheduledBill = mockk()
    private val boletoSettlementService = mockk<BoletoSettlementService>() {
        every {
            validateBill(createConcessionariaRequest)
        } returns ArbiValidationResponse(
            billRegisterData = fichaRegisterData.copy(payerDocument = DOCUMENT_2),
            paymentStatus = 12,
            resultado = "SUCESSO",
        )
    }

    private val concessionariaService = ConcessionariaService1(
        billRepository = billRepository,
        billEventRepository = billEventRepository,
        updateBillService = updateBillService,
        boletoSettlementService = boletoSettlementService,
        resolveScheduledBill = resolveScheduledBill,
    )

    private val member = Member(
        accountId = AccountId(ACCOUNT_ID),
        document = DOCUMENT,
        name = NAME,
        emailAddress = EMAIL_ADDRESS,
        type = MemberType.COLLABORATOR,
        permissions = MemberPermissions(
            viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
            scheduleBills = BillPermission.NO_BILLS,
            viewBalance = true,
            notification = true,
        ),
        status = MemberStatus.ACTIVE,
        created = getZonedDateTime(),
        updated = getZonedDateTime(),
    )

    private val createConcessionariaRequest = CreateConcessionariaRequest(
        description = "description fake",
        barcode = BarCode.ofDigitable(digitable = CONCESSIONARIA_DIGITABLE_LINE),
        dueDate = getLocalDate(),
        walletId = WalletId("RANDOM-WALLET-ID"),
        source = ActionSource.Api(AccountId(ACCOUNT_ID)),
        member = member,
    )

    @BeforeEach
    fun setup() {
        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns Either.Right(
            Bill.build(
                billAdded.copy(
                    actionSource = ActionSource.DDA(accountId = AccountId(ACCOUNT_ID_2)),
                    document = DOCUMENT_2,
                ),
            ),
        )

        every {
            billEventRepository.findLastBill(idNumber = any(), WalletId("RANDOM-WALLET-ID"))
        } returns Either.Left(ItemNotFoundException(""))
    }

    @Test
    fun `should return server error on generic error validating concessionaria bill`() {
        every {
            boletoSettlementService.validateBill(any<ConcessionariaRequest>())
        } returns CelcoinBillValidationResponse("999", "erro XML")

        every {
            billEventRepository.findLastBill(barCode = any(), any())
        } returns Either.Left(ItemNotFoundException("nao tenho a conta ainda!"))

        val response = concessionariaService.createConcessionaria(
            request = createConcessionariaRequest,
            dryRun = true,
        )

        response.shouldBeInstanceOf<CreateBillResult.FAILURE.ServerError>()
    }

    /*
    @Disabled // Note: Desabilitado até a celcoin resolver o bug da data de vencimento.
    @Test
    fun `deve criar uma concesisonaria com a data de vencimento igual a resposta de consulta de concessionária quando ela existir e deve salvar a data imputada pelo usuário`() {
        every {
            boletoSettlementService.validateBill(createConcessionariaRequest)
        } returns successConcessionariaValidationResponse.copy(
            billRegisterData = successConcessionariaValidationResponse.billRegisterData!!.copy(
                dueDate = LocalDate.of(2023, 4, 3),
            ),
        )

        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns Either.Left(ItemNotFoundException(""))

        val response = createBillService.createConcessionaria(
            request = createConcessionariaRequest,
            dryRun = false,
        )

        response.shouldBeInstanceOf<CreateBillResult.SUCCESS>()
        response.bill.dueDate shouldHaveSameDayAs LocalDate.of(2023, 4, 3)
        response.bill.effectiveDueDate shouldHaveSameDayAs LocalDate.of(2023, 4, 3)
        (response.bill.history.first() as BillAdded).requestedDueDate!! shouldHaveSameDayAs createConcessionariaRequest.dueDate
    }
    */

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `should not create bill(concessionaria) but make it visible when not visible bill already exists`(dryRun: Boolean) {
        every {
            validationService.validate(any<BarCode>())
        } returns ArbiValidationResponse(
            billRegisterData = billRegisterData,
            paymentStatus = CIPSitTitPgto.SUCESSO.code,
        )

        val response = concessionariaService.createConcessionaria(
            request = createConcessionariaRequest,
            dryRun = dryRun,
        )

        response.shouldBeInstanceOf<CreateBillResult.SUCCESS>()
        with(response) {
            warningCode shouldBe WarningCode.NOT_VISIBLE_BILL_ALREADY_EXISTS
            member.canView(bill) shouldBe true
        }

        if (dryRun) {
            verify(exactly = 0) {
                updateBillService.publishEvent(any(), any())
            }
        } else {
            verify(exactly = 1) {
                updateBillService.publishEvent(any(), ofType<BillPermissionUpdated>())
            }
        }
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `should not create bill(concessionaria) but make it visible and reactivate it when not visible bill already exists and is ignored`(
        dryRun: Boolean,
    ) {
        every {
            validationService.validate(any<BarCode>())
        } returns ArbiValidationResponse(
            billRegisterData = billRegisterData,
            paymentStatus = CIPSitTitPgto.SUCESSO.code,
        )

        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns Either.Right(
            Bill.build(
                billAdded.copy(
                    actionSource = ActionSource.DDA(accountId = AccountId(ACCOUNT_ID_2)),
                    document = DOCUMENT_2,
                ),
                billIgnored,
            ),
        )

        val response = concessionariaService.createConcessionaria(
            request = createConcessionariaRequest,
            dryRun = dryRun,
        )

        response.shouldBeInstanceOf<CreateBillResult.SUCCESS>().run {
            warningCode shouldBe null
            member.canView(bill) shouldBe true
            bill.status shouldBe BillStatus.ACTIVE
        }

        if (dryRun) {
            verify(exactly = 0) {
                updateBillService.publishEvent(any(), any())
            }
        } else {
            verify(exactly = 1) {
                updateBillService.publishEvent(any(), ofType<BillReactivated>())
                updateBillService.publishEvent(any(), ofType<BillPermissionUpdated>())
            }
        }
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `deve reativar a concessionária como pendente quando estiver negada e for recebida novamente`(
        dryRun: Boolean,
    ) {
        every {
            validationService.validate(any<BarCode>())
        } returns ArbiValidationResponse(
            billRegisterData = billRegisterData,
            paymentStatus = CIPSitTitPgto.SUCESSO.code,
        )

        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns Either.Right(
            Bill.build(
                billAdded.copy(
                    actionSource = ActionSource.WalletMailBox(accountId = AccountId("123"), from = "<EMAIL>"),
                    securityValidationResult = listOf("teste"),
                    document = DOCUMENT_2,
                ),
                billDenied,
            ),
        )

        val response = concessionariaService.createConcessionaria(
            request = createConcessionariaRequest,
            dryRun = dryRun,
        )

        response.shouldBeInstanceOf<CreateBillResult.SUCCESS>().run {
            warningCode shouldBe null
            member.canView(bill) shouldBe true
            bill.status shouldBe BillStatus.WAITING_APPROVAL
        }

        if (dryRun) {
            verify(exactly = 0) {
                updateBillService.publishEvent(any(), any())
            }
        } else {
            verify(exactly = 1) {
                updateBillService.publishEvent(any(), ofType<BillReactivated>())
            }
        }
    }

    @Test
    fun `nao deve criar a conta quando for uma concessionaria e ja existir uma conta para o mesmo cliente da concessionaria na mesma data e mesmo valor`() {
        val existingBill = BarCode.ofDigitable("848200000000214101622026305221229884669103211239")
        val newBill = BarCode.ofDigitable("848000000006214101622026305221229884669097211236")
        val concessionariaRequest = createConcessionariaRequest.copy(barcode = newBill)
        val dueDate = getLocalDate()

        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns Either.Left(ItemNotFoundException(""))

        every {
            boletoSettlementService.validateBill(concessionariaRequest)
        } returns ArbiValidationResponse(
            billRegisterData = billRegisterData.copy(
                barCode = newBill,
                dueDate = dueDate,
                amountTotal = 100L,
            ),
            paymentStatus = CIPSitTitPgto.SUCESSO.code,
        )

        every {
            billRepository.findByWalletAndEffectiveDueDate(any(), any())
        } returns listOf(
            getActiveBill().copy(
                billId = BillId(BILL_ID),
                amountTotal = 200L,
                dueDate = dueDate,
            ),
            getActiveBill().copy(
                billId = BillId(BILL_ID_2),
                barCode = existingBill,
                amountTotal = 100L,
                dueDate = dueDate,
            ),
        )

        every {
            billEventRepository.getBill(any(), BillId(BILL_ID_2))
        } returns Either.Right(
            Bill.build(
                billAdded.copy(
                    billId = BillId(BILL_ID_2),
                    barcode = existingBill,
                    amountTotal = 100L,
                    dueDate = dueDate,
                ),
            ),
        )

        val response = concessionariaService.createConcessionaria(
            request = concessionariaRequest,
            dryRun = false,
        )

        response.shouldBeInstanceOf<CreateBillResult.FAILURE.BillAlreadyExists>().run {
            member.canView(bill) shouldBe true
            bill.status shouldBe BillStatus.ACTIVE
        }

        verify(exactly = 0) {
            updateBillService.publishEvent(any(), any())
        }
    }

    @ParameterizedTest(name = "{3}")
    @CsvSource(
        value = [
            "848000000006214101622026305221229884669097211236, 100, 1, duedate diferentes e valor igual",
            "848000000006214101622026305221229884669097211236, 101, 0, valores diferentes e duedate igual",
            "848700000009449001622020306151589435782006121224, 100, 0, 'mesma concessionária, mesmo valor, mesma data, mas identificador diferente'",
            "836600000035081600531073483486061110100540825419, 100, 0, 'outra concessionária, mesmo valor e mesma data'",
        ],
    )
    fun `deve criar a conta quando for uma concessionaria e o duedate, valor, identificador e concessionária forem diferentes`(
        newBillBarcode: String,
        amountTotal: Long,
        daysOffset: Long,
        testName: String,
    ) {
        val existingBill = BarCode.ofDigitable("848200000000214101622026305221229884669103211239")
        val newBill = BarCode.ofDigitable(newBillBarcode)
        val today = getLocalDate()
        val concessionariaRequest = createConcessionariaRequest.copy(
            barcode = newBill,
            dueDate = today.plusDays(daysOffset),
        )

        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns Either.Left(ItemNotFoundException(""))

        every {
            boletoSettlementService.validateBill(concessionariaRequest)
        } returns ArbiValidationResponse(
            billRegisterData = billRegisterData.copy(
                barCode = newBill,
                dueDate = today.plusDays(daysOffset),
                amountTotal = amountTotal,
            ),
            paymentStatus = CIPSitTitPgto.SUCESSO.code,
        )

        every {
            billRepository.findByWalletAndEffectiveDueDate(any(), any())
        } returns listOf()

        every {
            billRepository.findByWalletAndEffectiveDueDate(any(), today)
        } returns listOf(
            getActiveBill().copy(
                billId = BillId(BILL_ID_2),
                barCode = existingBill,
                amountTotal = 100L,
                dueDate = today,
            ),
        )

        val response = concessionariaService.createConcessionaria(
            request = concessionariaRequest,
            dryRun = false,
        )

        response.shouldBeInstanceOf<CreateBillResult.SUCCESS>().run {
            member.canView(bill) shouldBe true
            bill.status shouldBe BillStatus.ACTIVE
        }

        verify(exactly = 1) {
            updateBillService.publishEvent(any(), any())
        }

        verify(exactly = 0) {
            billEventRepository.getBill(any(), any())
        }
    }

    @Test
    fun `quando o existir um external id igual, deve devolver o boleto adicionado`() {
        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns ItemNotFoundException("").left()

        val expectedBill = Bill.build(billAdded)

        every { billEventRepository.getBillByExternalId(any()) } returns expectedBill.right()

        val externalBillId = ExternalBillId(value = "123", ExternalBillProvider.VEHICLE_DEBTS)

        val response = concessionariaService.createConcessionaria(
            request = createConcessionariaRequest.copy(externalBillId = externalBillId),
            dryRun = false,
        )

        verify(exactly = 0) {
            boletoSettlementService.validateBill(ofType<ConcessionariaRequest>())
        }

        response.shouldBeInstanceOf<CreateBillResult.FAILURE.BillAlreadyExists>()

        response.bill shouldBe expectedBill
    }

    @Test
    fun `deve retornar conta já paga ao tentar adicionar uma concessionaria que já está paga`() {
        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns ItemNotFoundException("").left()

        every {
            boletoSettlementService.validateBill(createConcessionariaRequest)
        } returns CelcoinBillValidationResponse("183", "Pagamento ja efetuado.")

        val response = concessionariaService.createConcessionaria(
            request = createConcessionariaRequest,
            dryRun = false,
        )

        response.shouldBeInstanceOf<CreateBillResult.FAILURE.AlreadyPaid.WithoutData>()
    }

    @Test
    fun `deve retornar server error ao tentar adicionar uma concessionária já vencida`() {
        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns ItemNotFoundException("").left()

        every {
            boletoSettlementService.validateBill(createConcessionariaRequest)
        } returns CelcoinBillValidationResponse(
            "820",
            "Nao e possivel pagar esse tributo. Data de liquidacao posterior a data de vencimento.",
        )

        val response = concessionariaService.createConcessionaria(
            request = createConcessionariaRequest,
            dryRun = false,
        )

        response.shouldBeInstanceOf<CreateBillResult.FAILURE.BillNotPayable>()
    }

    @ParameterizedTest
    @MethodSource("notPayableErrors")
    fun `quando passar o ForcedConcessionariaRequest deve sempre criar a concessionaria mesmo que não seja pagável`(response: CelcoinBillValidationResponse) {
        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns ItemNotFoundException("").left()

        every { boletoSettlementService.validateBill(ofType<ForcedConcessionariaRequest>()) } returns response

        val request = ForcedConcessionariaRequest(
            description = "description fake",
            dueDate = getLocalDate(),
            barcode = BarCode.ofDigitable(PREFECTURE_BARCODE_LINE),
            walletId = WalletId("RANDOM-WALLET-ID"),
            amount = 100L,
            accountId = AccountId(),
            source = ActionSource.VehicleDebts(accountId = AccountId(ACCOUNT_ID_2)),
        )
        val response = concessionariaService.createConcessionaria(request = request, dryRun = false)

        response.shouldBeInstanceOf<CreateBillResult.FAILURE.BillNotPayable>()

        val slot = slot<BillAdded>()

        verify(exactly = 1) {
            updateBillService.publishEvent(any(), capture(slot))

            updateBillService.publishEvent(any(), ofType<RegisterUpdated>())
        }

        slot.captured.assignor shouldBe request.description
    }

    @MethodSource("allResponses")
    @ParameterizedTest
    fun `quando for concessionaria paga e não existir a conta, deve sempre criar uma concessionaria paga`(response: CelcoinBillValidationResponse) {
        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns ItemNotFoundException("").left()

        every { boletoSettlementService.validateBill(ofType<PaidConcessionariaRequest>()) } returns response

        val request = buildPaidConcessionaria(getLocalDate())

        concessionariaService.handlePaid(request = request)
            .shouldBeSuccess()

        val slot = slot<RegisterUpdated>()

        verify(exactly = 1) {
            updateBillService.publishEvent(any(), ofType<BillAdded>())

            updateBillService.publishEvent(any(), capture(slot))
        }

        slot.captured.updatedRegisterData.shouldBeTypeOf<UpdatedRegisterData.AlreadyPaidBill>()
    }

    @Test
    fun `quando for concessionaria paga e existir conta, deve apenas publicar conta paga`() {
        every {
            billEventRepository.findLastBill(barCode = any(), billAdded.walletId)
        } returns Either.Right(
            Bill.build(
                billAdded.copy(
                    actionSource = ActionSource.VehicleDebts(accountId = AccountId(ACCOUNT_ID_2)),
                    document = DOCUMENT_2,
                ),
            ),
        )

        every { resolveScheduledBill.resolve(any(), ScheduleCanceledReason.BILL_ALREADY_PAID) } just runs

        val request = PaidConcessionariaRequest(
            description = "description fake",
            dueDate = billAdded.dueDate,
            barcode = billAdded.barcode!!,
            walletId = billAdded.walletId,
            amount = billAdded.amount,
            accountId = AccountId(ACCOUNT_ID_2),
            source = ActionSource.VehicleDebts(accountId = AccountId(ACCOUNT_ID_2)),
        )

        concessionariaService.handlePaid(request = request)
            .shouldBeSuccess()

        val slot = slot<RegisterUpdated>()

        verify(exactly = 0) {
            boletoSettlementService.validateBill(ofType<ConcessionariaRequest>())

            updateBillService.publishEvent(any(), ofType<BillAdded>())
        }

        verify(exactly = 1) {
            resolveScheduledBill.resolve(any(), ScheduleCanceledReason.BILL_ALREADY_PAID)

            updateBillService.publishEvent(any(), capture(slot))
        }

        slot.captured.updatedRegisterData.shouldBeTypeOf<UpdatedRegisterData.AlreadyPaidBill>()
    }

    @Test
    fun `quando for concessionaria paga e não existir conta, se não tiver dueDate, deve retornar erro`() {
        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns ItemNotFoundException("").left()

        val request = buildPaidConcessionaria()

        concessionariaService.handlePaid(request = request)
            .shouldBeFailure()

        verify(exactly = 0) {
            boletoSettlementService.validateBill(ofType<ConcessionariaRequest>())

            updateBillService.publishEvent(any(), any())
        }
    }

    @Test
    fun `se a conta já estiver paga no nosso sistema, não deve fazer nada`() {
        every {
            billEventRepository.findLastBill(barCode = any(), WalletId("RANDOM-WALLET-ID"))
        } returns Either.Right(
            Bill.build(
                billAdded.copy(
                    actionSource = ActionSource.DDA(accountId = AccountId(ACCOUNT_ID_2)),
                    document = DOCUMENT_2,
                ),
                billPaid,
            ),
        )

        val request = buildPaidConcessionaria(LocalDate.now())

        concessionariaService.handlePaid(request = request)
            .shouldBeSuccess()

        verify(exactly = 0) {
            boletoSettlementService.validateBill(ofType<ConcessionariaRequest>())
            updateBillService.publishEvent(any(), any())
        }
    }

    private fun buildPaidConcessionaria(dueDate: LocalDate? = null) = PaidConcessionariaRequest(
        description = "description fake",
        dueDate = dueDate,
        barcode = BarCode.ofDigitable(PREFECTURE_BARCODE_LINE),
        walletId = WalletId("RANDOM-WALLET-ID"),
        amount = 100L,
        accountId = AccountId(),
        source = ActionSource.VehicleDebts(accountId = AccountId(ACCOUNT_ID_2)),
    )

    companion object {
        @JvmStatic
        fun allResponses(): List<CelcoinBillValidationResponse> = listOf(
            CelcoinBillValidationResponse("183", "Pagamento ja efetuado."),
            CelcoinBillValidationResponse("050", "EXCEDE LIMITE"),
            okResponse(),
        ) + notPayableErrors()

        @JvmStatic
        fun notPayableErrors(): List<CelcoinBillValidationResponse> = listOf(
            CelcoinBillValidationResponse("481", "CEDENTE NAO AUTORIZADO. PAGAMENTO NAO EFETUADO."),
            CelcoinBillValidationResponse("820", "Nao e possivel pagar esse tributo. Data de liquidacao posterior a data de vencimento."),
        )
    }
}