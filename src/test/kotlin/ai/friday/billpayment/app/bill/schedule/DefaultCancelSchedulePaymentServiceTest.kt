package ai.friday.billpayment.app.bill.schedule

import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillAlreadyLockedException
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billScheduleCanceled
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.withoutLogs
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class DefaultCancelSchedulePaymentServiceTest {
    private lateinit var test: DefaultCancelSchedulePaymentService

    private lateinit var billEventRepository: BillEventRepository
    private lateinit var billEventPublisher: BillEventPublisher
    private lateinit var lockProvider: InternalLock

    @BeforeEach
    fun setUp() {
        billEventRepository = mockk(relaxed = true)
        billEventPublisher = mockk(relaxUnitFun = true)
        lockProvider = mockk(relaxed = true)

        test = DefaultCancelSchedulePaymentService(
            billEventRepository,
            billEventPublisher,
            lockProvider,
        )
    }

    @Test
    fun `on payment schedule cancel should return itemNotFound when bill not found`() {
        every {
            billEventRepository.getBillById(billAdded.billId)
        } returns ItemNotFoundException(billAdded.billId).left()

        withoutLogs {
            assertThrows<ItemNotFoundException> {
                test.cancelScheduledPayment(
                    walletId = billAdded.walletId,
                    billId = billAdded.billId,
                    reason = ScheduleCanceledReason.EXPIRATION,
                )
            }
        }
    }

    @Test
    fun `on payment schedule cancel should return error when locked`() {
        every {
            billEventRepository.getBillById(billAdded.billId)
        } answers { Bill.build(billAdded).right() }

        every { lockProvider.acquireLock(billAdded.billId.value) } returns null

        withoutLogs {
            assertThrows<BillAlreadyLockedException> {
                test.cancelScheduledPayment(
                    walletId = billAdded.walletId,
                    billId = billAdded.billId,
                    reason = ScheduleCanceledReason.EXPIRATION,
                )
            }
        }

        verify(exactly = 0) {
            billEventPublisher.publish(ofType(Bill::class), ofType(BillPaymentScheduleCanceled::class))
        }
    }

    @Test
    fun `should return scheduled false when member can view bill`() {
        val walletFixture = WalletFixture()

        val bill = Bill.build(
            billAdded.copy(
                actionSource = ActionSource.Api(
                    accountId = walletFixture.cantPayParticipant.accountId,
                    originalBillId = null,
                ),
            ),
            billPaymentScheduled,
        )

        every {
            billEventRepository.getBillById(billAdded.billId)
        } answers { bill.right() }

        val isScheduled = test.userCancelScheduledPayment(
            walletId = billAdded.walletId,
            billId = billAdded.billId,
            member = walletFixture.cantPayParticipant,
            actionSource = ActionSource.Api(
                accountId = walletFixture.cantPayParticipant.accountId,
                originalBillId = null,
            ),
        )

        isScheduled shouldBe false

        verify(exactly = 1) {
            billEventPublisher.publish(ofType(Bill::class), ofType(BillPaymentScheduleCanceled::class))
        }
    }

    @Test
    fun `should return scheduled true when bill is friday subscription`() {
        val walletFixture = WalletFixture()
        val bill = Bill.build(
            billAddedFicha.copy(
                actionSource = ActionSource.Api(
                    accountId = walletFixture.cantPayParticipant.accountId,
                    originalBillId = null,
                ),
                subscriptionFee = true,
            ),
            billPaymentScheduled.copy(billId = billAddedFicha.billId, walletId = billAddedFicha.walletId),
        )

        every {
            billEventRepository.getBillById(billAddedFicha.billId)
        } answers { bill.right() }

        val isScheduled = test.userCancelScheduledPayment(
            walletId = billAddedFicha.walletId,
            billId = billAddedFicha.billId,
            member = walletFixture.cantPayParticipant,
            actionSource = ActionSource.Api(
                accountId = walletFixture.cantPayParticipant.accountId,
                originalBillId = null,
            ),
        )

        isScheduled shouldBe true
        verify(exactly = 0) {
            billEventPublisher.publish(ofType(Bill::class), ofType(BillPaymentScheduleCanceled::class))
        }
    }

    @Test
    fun `on payment schedule cancel should return false when schedule is already canceled`() {
        every {
            billEventRepository.getBillById(billAdded.billId)
        } answers { Bill.build(billAdded, billPaymentScheduled, billScheduleCanceled).right() }

        val isScheduled = test.cancelScheduledPayment(
            walletId = billAdded.walletId,
            billId = billAdded.billId,
            reason = ScheduleCanceledReason.EXPIRATION,
        )

        isScheduled shouldBe false
        verify(exactly = 0) {
            billEventRepository.save(ofType(BillPaymentScheduleCanceled::class))
            billEventPublisher.publish(ofType(Bill::class), ofType(BillPaymentScheduleCanceled::class))
        }
    }

    @Test
    fun `on payment schedule cancel should return false when schedule is canceled`() {
        every {
            billEventRepository.getBillById(billAdded.billId)
        } answers { Bill.build(billAdded, billPaymentScheduled).right() }

        val isScheduled = test.cancelScheduledPayment(
            walletId = billAdded.walletId,
            billId = billAdded.billId,
            reason = ScheduleCanceledReason.EXPIRATION,
        )

        isScheduled shouldBe false

        verify(exactly = 1) {
            billEventPublisher.publish(ofType(Bill::class), ofType(BillPaymentScheduleCanceled::class))
        }
    }

    @Test
    fun `on payment schedule cancel should return false when bill is not scheduled`() {
        every {
            billEventRepository.getBillById(billAdded.billId)
        } answers { Bill.build(billAdded).right() }

        val isScheduled = test.cancelScheduledPayment(
            walletId = billAdded.walletId,
            billId = billAdded.billId,
            reason = ScheduleCanceledReason.EXPIRATION,
        )

        isScheduled shouldBe false
    }
}