package ai.friday.billpayment.app.bill.mailbox.rules

import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.mailbox.MailboxValidateRequestFixture
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test

internal class BarCodeAlreadyExistsMailboxRuleTest {

    private val billEventRepository: BillEventDBRepository = mockk()

    private val rule = BarCodeAlreadyExistsMailboxRule(billEventRepository = billEventRepository, maxWallets = 2)

    private val bill1 = mockk<Bill> {
        every { walletId } returns WalletId("1")
    }

    private val bill2 = mockk<Bill> {
        every { walletId } returns WalletId("2")
    }

    @Test
    fun `Deve retornar erro se o barcode ja existir em duas carteiras`() {
        every {
            billEventRepository.listBills(any(), any())
        } returns listOf(bill1, bill2)

        val result = rule.execute(
            MailboxValidateRequestFixture.validateRequest(
                sender = "<EMAIL>",
                barCode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
            ),
        )
        result.isLeft().shouldBeTrue()
        result.mapLeft {
            it.reason shouldBe "BarCodeAlreadyExists"
        }
    }

    @Test
    fun `Deve retornar sucesso se o barcode ja existir em apenas uma carteira`() {
        every {
            billEventRepository.listBills(any(), any())
        } returns listOf(bill1)

        rule.execute(
            MailboxValidateRequestFixture.validateRequest(
                sender = "<EMAIL>",
                barCode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
            ),
        ).isRight().shouldBeTrue()
    }

    @Test
    fun `Deve retornar sucesso se o barcode nao existir em outras carteiras`() {
        every {
            billEventRepository.listBills(any(), any())
        } returns emptyList()

        rule.execute(
            MailboxValidateRequestFixture.validateRequest(
                sender = "<EMAIL>",
                barCode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
            ),
        ).isRight().shouldBeTrue()
    }
}