package ai.friday.billpayment.app.bill

import DynamoDBUtils
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billTagAdded
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.getOrElse
import io.kotest.matchers.collections.shouldContain
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class TagBillTest {

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val enhancedClient = DynamoDBUtils.setupDynamoDB()
    private val billEventDAO = BillEventDynamoDAO(enhancedClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val billEventRepository = BillEventDBRepository(
        billEventDAO = billEventDAO,
        uniqueConstraintDAO = uniqueConstraintDAO,
        featureConfiguration = mockk(),
        transactionDynamo = transactionDynamo,
    )

    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(enhancedClient),
        refundedClient = RefundedBillDynamoDAO(enhancedClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(enhancedClient),
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
    }

    @Test
    fun `deve salvar uma conta com tag`() {
        billEventRepository.save(
            billAdded.copy(
                created = getZonedDateTime().minusSeconds(4).toInstant().toEpochMilli(),
            ),
        )
        billEventRepository.save(
            billTagAdded.copy(
                created = getZonedDateTime().minusSeconds(2).toInstant().toEpochMilli(),
            ),
        )

        val bill = billEventRepository.getBillById(
            billId = billAdded.billId,
        ).getOrElse {
            throw it
        }

        bill.tags shouldContain BillTag.ONBOARDING_TEST_PIX

        billRepository.save(bill)

        val billView = billRepository.findBill(
            walletId = billAdded.walletId,
            billId = billAdded.billId,
        )

        billView.tags shouldContain BillTag.ONBOARDING_TEST_PIX
    }
}