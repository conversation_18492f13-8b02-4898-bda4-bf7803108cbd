package ai.friday.billpayment.app.bill

import ai.friday.billpayment.TrackableBillFixture
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.DiscountData
import ai.friday.billpayment.app.payment.DiscountType
import ai.friday.billpayment.app.payment.FineData
import ai.friday.billpayment.app.payment.FineType
import ai.friday.billpayment.app.payment.InterestData
import ai.friday.billpayment.app.payment.InterestType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class TrackableBillTest {
    @Test
    fun `deve retornar query quando o calculo de juros não for suportado`() {
        val bill = TrackableBillFixture.create().copy(
            interestData = InterestData(
                type = InterestType.PERCENT_BY_MONTH_WORKING_DAYS,
            ),
        )

        val result = bill.calculateOrQuery()

        result shouldBe BillTrackingCalculateOptions.QUERY
    }

    @Test
    fun `deve retornar query quando o calculo de multa não for suportado`() {
        val bill = TrackableBillFixture.create().copy(
            fineData = FineData(
                type = FineType.UNKNOWN,
            ),
        )

        val result = bill.calculateOrQuery()

        result shouldBe BillTrackingCalculateOptions.QUERY
    }

    @Test
    fun `deve retornar query quando o calculo de desconto não for suportado`() {
        val bill = TrackableBillFixture.create().copy(
            discountData = DiscountData(
                type = DiscountType.UNKNOWN,
            ),
        )

        val result = bill.calculateOrQuery()

        result shouldBe BillTrackingCalculateOptions.QUERY
    }

    @Test
    fun `deve retornar query quando a bill não for DDA`() {
        val bill = TrackableBillFixture.create().copy(
            addedByDda = false,
        )

        val result = bill.calculateOrQuery()

        result shouldBe BillTrackingCalculateOptions.QUERY
    }

    @Test
    fun `deve retornar calculate quando o calculation model for ANYONE em um DDA`() {
        val bill = TrackableBillFixture.create().copy(
            addedByDda = true,
            amountCalculationModel = AmountCalculationModel.ANYONE,
        )

        val result = bill.calculateOrQuery()

        result shouldBe BillTrackingCalculateOptions.CALCULATE
    }

    @Test
    fun `deve retornar calculate quando for um DDA com calculation model BENEFICIARY_AFTER_DUE_DATE com a data de hoje`() {
        val bill = TrackableBillFixture.create().copy(
            addedByDda = true,
            amountCalculationModel = AmountCalculationModel.BENEFICIARY_AFTER_DUE_DATE,
            dueDate = getZonedDateTime().toLocalDate(),
        )

        val result = bill.calculateOrQuery()

        result shouldBe BillTrackingCalculateOptions.CALCULATE
    }

    @Test
    fun `deve retornar calculate quando for um DDA com calculation model BENEFICIARY_AFTER_DUE_DATE com a data depois de hoje`() {
        val bill = TrackableBillFixture.create().copy(
            addedByDda = true,
            amountCalculationModel = AmountCalculationModel.BENEFICIARY_AFTER_DUE_DATE,
            dueDate = getZonedDateTime().toLocalDate().plusDays(1),
        )

        val result = bill.calculateOrQuery()

        result shouldBe BillTrackingCalculateOptions.CALCULATE
    }

    @Test
    fun `deve retornar query quando for um DDA que não se encaixa em nenhuma outra regra`() {
        val bill = TrackableBillFixture.create().copy(
            addedByDda = true,
            amountCalculationModel = AmountCalculationModel.BENEFICIARY_AFTER_DUE_DATE,
            dueDate = getZonedDateTime().toLocalDate().minusDays(1),
        )

        val result = bill.calculateOrQuery()

        result shouldBe BillTrackingCalculateOptions.QUERY
    }
}