package ai.friday.billpayment.app.bill

import DynamoDBUtils
import ai.friday.billpayment.TrackableBillFixture
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.app.bill.tracking.BillTrackingCalculateHandlerService
import ai.friday.billpayment.app.bill.tracking.BillTrackingCalculateResult
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billIgnored
import ai.friday.billpayment.billMarkedAsPaid
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.billRegisterUpdatedAlreadyPaid
import ai.friday.billpayment.billRegisterUpdatedNotPayable
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.pixAdded
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import java.time.LocalDate
import java.time.ZonedDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource
import org.junit.jupiter.params.provider.ValueSource
import reactor.core.publisher.Mono

class UpdateAmountBillTest {
    private val amazonDynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val enhancedClient = DynamoDBUtils.setupDynamoDB()

    private val billRepository = spyk(
        DynamoDbBillRepository(
            billClient = BillDynamoDAO(enhancedClient),
            refundedClient = RefundedBillDynamoDAO(enhancedClient),
            settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(enhancedClient),
        ),
    )

    private val billEventRepository: BillEventRepository = mockk()

    val walletFixture = WalletFixture(defaultWalletId = billAddedFicha.walletId)
    val wallet = walletFixture.buildWallet()

    private val billTrackingService: BillTrackingCalculateHandlerService = mockk {
        every {
            isBillOutdated(any())
        } returns false
    }

    private val featureConfiguration: FeatureConfiguration = mockk {
        every {
            updateAmountAfterPaymentWindow
        } returns true
    }

    private val defaultFindBillServiceCache: DefaultFindBillServiceCache = mockk {
        every {
            storeBillId(any(), any(), any())
        } answers {
            thirdArg()
        }
    }

    private val service = UpdateAmountBill(
        billRepository = billRepository,
        billTrackingService = billTrackingService,
        featureConfiguration = featureConfiguration,
        defaultFindBillServiceCache = defaultFindBillServiceCache,
        billEventRepository = billEventRepository,
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(amazonDynamoDB)
        createBillEventTable(amazonDynamoDB)
    }

    @ParameterizedTest
    @MethodSource("getBillAddedEvents")
    fun `nao deve recalcular o valor da conta quando nao eh uma ficha de compensacao`(event: BillAdded) {
        val bill = Bill.build(event)
        billRepository.save(bill)

        val limit = bill.paymentLimitTime
        val now = ZonedDateTime.of(LocalDate.now(), limit, brazilTimeZone).plusMinutes(5)

        val viewBill = billRepository.findBill(bill.billId, bill.walletId)

        withGivenDateTime(now) {
            service.updateAmountIfExpired(viewBill)
        }

        verify(exactly = 0) {
            billTrackingService.execute(any(), any(), any())
        }
    }

    @ParameterizedTest
    @EnumSource(value = BillTrackingCalculateOptions::class)
    fun `nao deve recalcular o valor da conta quando encontra uma ficha de compensacao antes do final da janela`(billTrackingCalculateOptions: BillTrackingCalculateOptions) {
        val bill = createFichaCompensacao(billTrackingCalculateOptions)

        val limit = bill.paymentLimitTime
        val now = ZonedDateTime.of(LocalDate.now(), limit, brazilTimeZone).minusMinutes(5)

        val viewBill = billRepository.findBill(bill.billId, bill.walletId)

        withGivenDateTime(now) {
            service.updateAmountIfExpired(viewBill)
        }

        verify(exactly = 0) {
            billTrackingService.execute(any(), any(), any())
        }
    }

    @ParameterizedTest
    @ValueSource(longs = [5, -5])
    fun `nao deve recalcular o valor da conta quando encontra uma ficha de compensacao untrackable antes ou depois da janela`(
        minutes: Long,
    ) {
        val bill = createFichaCompensacao(billTrackingCalculateOptions = null, amountCalculationValidUntil = null)

        val limit = bill.paymentLimitTime
        val now = ZonedDateTime.of(LocalDate.now(), limit, brazilTimeZone).plusMinutes(minutes)

        val viewBill = billRepository.findBill(bill.billId, bill.walletId)

        withGivenDateTime(now) {
            service.updateAmountIfExpired(viewBill)
        }

        verify(exactly = 0) {
            billTrackingService.execute(any(), any(), any())
        }
    }

    @ParameterizedTest
    @ValueSource(longs = [5, -5])
    fun `nao deve recalcular o valor da conta quando encontra uma ficha de compensacao do tipo QUERY antes ou depois da janela`(
        minutes: Long,
    ) {
        val bill = createFichaCompensacao(BillTrackingCalculateOptions.QUERY)

        val limit = bill.paymentLimitTime
        val now = ZonedDateTime.of(LocalDate.now(), limit, brazilTimeZone).plusMinutes(minutes)

        val viewBill = billRepository.findBill(bill.billId, bill.walletId)

        withGivenDateTime(now) {
            service.updateAmountIfExpired(viewBill)
        }

        verify(exactly = 0) {
            billTrackingService.execute(any(), any(), any())
        }
    }

    @Test
    fun `nao deve recalcular o valor da conta quando encontra uma ficha de compensacao do tipo CALCULATE depois do final da janela com a feature flag desligada`() {
        every {
            featureConfiguration.updateAmountAfterPaymentWindow
        } returns false

        val trackable = TrackableBillFixture.create()

        every {
            billTrackingService.findById(any())
        } returns Mono.just(trackable)

        val bill = createFichaCompensacao(BillTrackingCalculateOptions.CALCULATE)

        val limit = bill.paymentLimitTime
        val now = ZonedDateTime.of(LocalDate.now(), limit, brazilTimeZone).plusMinutes(5)

        val viewBill = billRepository.findBill(bill.billId, bill.walletId)

        withGivenDateTime(now) {
            service.updateAmountIfExpired(viewBill)
        }

        verify(exactly = 0) {
            billTrackingService.execute(any(), any(), any())
        }
    }

    @Test
    fun `nao deve recalcular o valor da conta quando encontra uma ficha de compensacao que ainda esta com o calculo valido`() {
        val bill = createFichaCompensacao(BillTrackingCalculateOptions.CALCULATE, LocalDate.now().plusDays(1))

        val limit = bill.paymentLimitTime
        val now = ZonedDateTime.of(LocalDate.now(), limit, brazilTimeZone).plusMinutes(5)

        val viewBill = billRepository.findBill(bill.billId, bill.walletId)

        withGivenDateTime(now) {
            service.updateAmountIfExpired(viewBill)
        }

        verify(exactly = 0) {
            billTrackingService.execute(any(), any(), any())
        }
    }

    @Test
    fun `não deve recalcular o valor da conta quando encontra a ficha de compensacao no cache`() {
        val trackable = TrackableBillFixture.create()

        every {
            billTrackingService.findById(any())
        } returns Mono.just(trackable)

        every {
            defaultFindBillServiceCache.storeBillId(any(), any(), any())
        } returns ""

        val bill = createFichaCompensacao(BillTrackingCalculateOptions.CALCULATE)

        val limit = bill.paymentLimitTime
        val now = ZonedDateTime.of(LocalDate.now(), limit, brazilTimeZone).plusMinutes(5)

        val viewBill = billRepository.findBill(bill.billId, bill.walletId)

        withGivenDateTime(now) {
            service.updateAmountIfExpired(viewBill)
        }

        verify(exactly = 0) {
            billTrackingService.execute(any(), any(), any())
        }
    }

    @ParameterizedTest
    @MethodSource("notPayableEvents")
    fun `não deve recalcular o valor da conta quando a ficha de compensacao nao eh pagavel`(notPayableEvent: BillEvent) {
        val trackable = TrackableBillFixture.create()

        every {
            billTrackingService.findById(any())
        } returns Mono.just(trackable)

        val bill = createFichaCompensacao(
            billTrackingCalculateOptions = BillTrackingCalculateOptions.CALCULATE,
            billEvent = notPayableEvent,
        )

        val limit = bill.paymentLimitTime
        val now = ZonedDateTime.of(LocalDate.now(), limit, brazilTimeZone).plusMinutes(5)

        val viewBill = billRepository.findBill(bill.billId, bill.walletId)

        withGivenDateTime(now) {
            service.updateAmountIfExpired(viewBill)
        }

        verify(exactly = 0) {
            billTrackingService.execute(any(), any(), any())
        }
    }

    @ParameterizedTest
    @EnumSource(BillTrackingCalculateResult::class, mode = EnumSource.Mode.EXCLUDE, names = ["NOT_MODIFIED"])
    fun `deve recalcular o valor da conta quando encontra uma ficha de compensacao do tipo CALCULATE depois do final da janela`(billTrackingCalculateResult: BillTrackingCalculateResult) {
        val trackable = TrackableBillFixture.create()

        every {
            billTrackingService.findById(any())
        } returns Mono.just(trackable)

        every {
            billTrackingService.execute(any(), any(), any())
        } returns billTrackingCalculateResult.right()

        val bill = createFichaCompensacao(BillTrackingCalculateOptions.CALCULATE)

        val limit = bill.paymentLimitTime
        val now = ZonedDateTime.of(LocalDate.now(), limit, brazilTimeZone).plusMinutes(5)

        val viewBill = billRepository.findBill(bill.billId, bill.walletId)

        withGivenDateTime(now) {
            service.updateAmountIfExpired(viewBill)
        }

        val processingDateSlot = slot<LocalDate>()
        verify(exactly = 1) {
            billTrackingService.execute(trackable, any(), capture(processingDateSlot))
        }
        processingDateSlot.captured shouldBe now.toLocalDate().plusDays(1)

        verify(exactly = 0) {
            billEventRepository.getBillById(any())
            billRepository.save(any())
        }
    }

    @ParameterizedTest
    @EnumSource(BillTrackingCalculateResult::class, mode = EnumSource.Mode.EXCLUDE, names = ["NOT_MODIFIED"])
    fun `deve recalcular o valor da conta quando encontra uma ficha de compensacao do tipo CALCULATE com o calculo vencido`(billTrackingCalculateResult: BillTrackingCalculateResult) {
        val trackable = TrackableBillFixture.create()

        every {
            billTrackingService.findById(any())
        } returns Mono.just(trackable)

        every {
            billTrackingService.execute(any(), any(), any())
        } returns billTrackingCalculateResult.right()

        val today = LocalDate.now()

        val bill = createFichaCompensacao(
            billTrackingCalculateOptions = BillTrackingCalculateOptions.CALCULATE,
            amountCalculationValidUntil = today.minusDays(1),
        )

        val limit = bill.paymentLimitTime
        val now = ZonedDateTime.of(today, limit, brazilTimeZone).plusMinutes(-5)

        val viewBill = billRepository.findBill(bill.billId, bill.walletId)

        withGivenDateTime(now) {
            service.updateAmountIfExpired(viewBill)
        }

        val processingDateSlot = slot<LocalDate>()
        verify(exactly = 1) {
            billTrackingService.execute(trackable, any(), capture(processingDateSlot))
        }
        processingDateSlot.captured shouldBe now.toLocalDate()

        verify(exactly = 0) {
            billEventRepository.getBillById(any())
            billRepository.save(any())
        }
    }

    @Test // codigo temporario para corrigir conta que so tem FichaCompensacaoAdded com amountCalculationValidUntil com valor errado
    fun `deve reconstruir a bill view para atualizar a data de validade do calculo quando a conta nao foi modificada apos o calculo`() {
        val trackable = TrackableBillFixture.create()

        every {
            billTrackingService.findById(any())
        } returns Mono.just(trackable)

        every {
            billTrackingService.execute(any(), any(), any())
        } returns BillTrackingCalculateResult.NOT_MODIFIED.right()

        val today = LocalDate.now()

        val bill = createFichaCompensacao(
            billTrackingCalculateOptions = BillTrackingCalculateOptions.CALCULATE,
            amountCalculationValidUntil = today.minusDays(1),
        )

        every {
            billEventRepository.getBillById(any())
        } returns bill.right()

        val limit = bill.paymentLimitTime
        val now = ZonedDateTime.of(today, limit, brazilTimeZone).plusMinutes(-5)

        val viewBill = billRepository.findBill(bill.billId, bill.walletId)

        withGivenDateTime(now) {
            service.updateAmountIfExpired(viewBill)
        }

        verify {
            billEventRepository.getBillById(trackable.billId)
            billRepository.save(bill)
        }
    }

    private fun createFichaCompensacao(
        billTrackingCalculateOptions: BillTrackingCalculateOptions?,
        amountCalculationValidUntil: LocalDate? = LocalDate.now(),
        billEvent: BillEvent? = null,
    ): Bill {
        val bill = Bill.build(billAddedFicha)

        billTrackingCalculateOptions?.let {
            bill.apply(
                RegisterUpdated(
                    billId = billAddedFicha.billId,
                    walletId = billAddedFicha.walletId,
                    updatedRegisterData = UpdatedRegisterData.NewAmountCalculationOption(
                        amountCalculationOption = it,
                    ),
                    actionSource = ActionSource.System,
                ),
            )
        }

        amountCalculationValidUntil?.let {
            bill.apply(
                RegisterUpdated(
                    billId = billAddedFicha.billId,
                    walletId = billAddedFicha.walletId,
                    updatedRegisterData = UpdatedRegisterData.NewTotalAmount(
                        amountCalculationValidUntil = it,
                        amount = billAddedFicha.amount,
                        abatement = billAddedFicha.abatement?.toLong() ?: 0L,
                        discount = billAddedFicha.discount,
                        interest = billAddedFicha.interest,
                        fine = billAddedFicha.fine + 1,
                        amountTotal = billAddedFicha.amountTotal + 1,
                        lastSettleDate = billAddedFicha.lastSettleDate!!,
                        registrationUpdateNumber = null,
                        source = null,
                    ),
                    actionSource = ActionSource.System,
                ),
            )
        }

        billEvent?.let {
            bill.apply(it)
        }

        billRepository.save(bill)
        clearMocks(billRepository)
        return bill
    }

    companion object {
        @JvmStatic
        fun getBillAddedEvents(): List<Arguments> {
            return listOf(
                Arguments.of(billAdded),
                Arguments.of(pixAdded),
                Arguments.of(invoiceAdded),
            )
        }

        @JvmStatic
        fun notPayableEvents() = listOf(
            billIgnored,
            billPaymentStart,
            billPaid,
            billRegisterUpdatedAlreadyPaid,
            billRegisterUpdatedNotPayable,
            billMarkedAsPaid,
        )
    }
}