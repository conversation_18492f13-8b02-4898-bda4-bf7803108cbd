package ai.friday.billpayment.app.bill.tracking

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.ServerError
import ai.friday.billpayment.TrackableBillFixture
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillAmountCalculator
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillSynchronizationStatus
import ai.friday.billpayment.app.bill.BillTrackingCalculateOptions
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.NotSupported
import ai.friday.billpayment.app.bill.RegisterUpdated
import ai.friday.billpayment.app.bill.SynchronizeBillResponse
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.bill.UpdatedRegisterData
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillTrackingRepository
import ai.friday.billpayment.app.integrations.BillValidationService
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.barcodeNotFoundValidationResponse
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.fixture.BillValidationFixture
import ai.friday.billpayment.fixture.CalculatorAmountFixture
import ai.friday.billpayment.payableDefaultValidationResponse
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Called
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.stream.Stream
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.fail
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource

class BillTrackingCalculateHandlerServiceTest {

    private val bill = Bill.build(billAdded.copy(expirationDate = getLocalDate().toString()))

    private val updateBillService: UpdateBillService = mockk {
        every { synchronizeBill(any<BillId>(), any()) } returns SynchronizeBillResponse(BillSynchronizationStatus.BillAmountTotalUpdated)
    }

    private val provider: BillValidationService = mockk {
        every { validate(any<BarCode>()) } returns payableDefaultValidationResponse
    }

    private val calculator: BillAmountCalculator = mockk(relaxed = true)

    private val billEventRepository: BillEventRepository = mockk {
        every { getBillById(any()) } returns bill.right()
    }

    private val billTrackingRepository: BillTrackingRepository = mockk(relaxed = true)

    private val billTrackingConfiguration = mockk<BillTrackingConfiguration>(relaxed = true)

    private val billEventPublisher: BillEventPublisher = mockk(relaxed = true)

    private val service = BillTrackingCalculateHandlerService(
        provider = provider,
        calculator = calculator,
        updateBillService = updateBillService,
        billEventRepository = billEventRepository,
        billTrackingRepository = billTrackingRepository,
        billEventPublisher = billEventPublisher,
        configuration = billTrackingConfiguration,
    )

    @Test
    fun `deve publicar evento de alteracao do tipo de calculo`() {
        service.execute(
            TrackableBillFixture.create(AmountCalculationModel.BENEFICIARY_ONLY),
            BillTrackingCalculateOptions.CALCULATE,
        ).mapLeft {
            return fail("should not fail. $it")
        }

        val slot = slot<BillEvent>()
        verify {
            billEventPublisher.publish(bill, capture(slot))
        }

        with(slot.captured) {
            this.shouldBeTypeOf<RegisterUpdated>()

            val updatedRegisterData = this.updatedRegisterData
            updatedRegisterData.shouldBeTypeOf<UpdatedRegisterData.NewAmountCalculationOption>()
            updatedRegisterData.amountCalculationOption shouldBe BillTrackingCalculateOptions.QUERY
        }
    }

    @ParameterizedTest
    @EnumSource(
        value = AmountCalculationModel::class,
        mode = EnumSource.Mode.EXCLUDE,
        names = ["ANYONE", "BENEFICIARY_AFTER_DUE_DATE"],
    )
    fun `deve chamar provedor de calculo para bills que não podem ser calculadas`(calculationModel: AmountCalculationModel) {
        val response = BillValidationFixture.create()

        every { provider.validate(any<BarCode>()) } returns response

        every {
            updateBillService.synchronizeBill(any<BillId>(), any())
        } returns SynchronizeBillResponse(BillSynchronizationStatus.BillAmountTotalUpdated)
        every { billTrackingRepository.update(any(), any()) } just runs

        service.execute(TrackableBillFixture.create(calculationModel), BillTrackingCalculateOptions.CALCULATE).mapLeft {
            return fail("should not fail. $it")
        } shouldBe BillTrackingCalculateResult.UPDATED.right()

        verify { provider.validate(any<BarCode>()) }
        verify { calculator wasNot Called }
        verify {
            updateBillService.synchronizeBill(any<BillId>(), any())
        }
        val slot = slot<LocalDate>()
        verify { billTrackingRepository.update(any(), capture(slot)) }
        slot.captured shouldBe getLocalDate().plusDays(1)
    }

    @Test
    fun `deve chamar calculadora interna caso o boleto possa ser calculado internamente`() {
        every {
            calculator.calculate(
                amount = any(),
                dueDate = any(),
                processingDate = any(),
                fineData = any(),
                interestData = any(),
                discountData = any(),
                abatement = any(),
            )
        } returns Either.Right(CalculatorAmountFixture.create())

        every {
            updateBillService.synchronizeBill(any<BillId>(), any())
        } returns SynchronizeBillResponse(BillSynchronizationStatus.BillAmountTotalUpdated)
        every { billTrackingRepository.update(any(), any()) } just runs

        val processingDate = LocalDate.now().plusDays(1)

        service.execute(
            track = TrackableBillFixture.create(amountCalculationModel = AmountCalculationModel.ANYONE, isDda = true),
            origin = BillTrackingCalculateOptions.CALCULATE,
            processingDate = processingDate,
        ).mapLeft {
            return fail("should not fail. $it")
        } shouldBe BillTrackingCalculateResult.UPDATED.right()

        val processingDateSlot = slot<LocalDate>()
        verify {
            calculator.calculate(
                amount = any(),
                dueDate = any(),
                fineData = any(),
                interestData = any(),
                discountData = any(),
                abatement = any(),
                processingDate = capture(processingDateSlot),
            )
        }
        processingDateSlot.captured shouldBe processingDate

        verify { provider wasNot Called }
        verify { updateBillService.synchronizeBill(any<BillId>(), any()) }

        val slot = slot<LocalDate>()
        verify { billTrackingRepository.update(any(), capture(slot)) }
        slot.captured shouldBe getLocalDate().plusDays(1)
    }

    @Test
    fun `deve propagar o erro caso ocorra algum erro na calculadora`() {
        every {
            calculator.calculate(
                amount = any(),
                dueDate = any(),
                processingDate = any(),
                fineData = any(),
                interestData = any(),
                discountData = any(),
                abatement = any(),
            )
        } returns Either.Left(ServerError())

        service.execute(
            TrackableBillFixture.create(amountCalculationModel = AmountCalculationModel.ANYONE, isDda = true),
            BillTrackingCalculateOptions.CALCULATE,
        ).fold(
            {
                Assertions.assertTrue(it is ServerError)
            },
            { fail("should never success. $it") },
        )

        verify {
            updateBillService wasNot Called
        }
    }

    @Test
    fun `deve propagar o erro caso ocorra algum problema no provedor de calculo`() {
        every { provider.validate(any<BarCode>()) } throws NoStackTraceException()

        service.execute(
            TrackableBillFixture.create(AmountCalculationModel.ON_DEMAND),
            BillTrackingCalculateOptions.CALCULATE,
        ).fold(
            {
                Assertions.assertTrue(it is ServerError)
                it.ex shouldBe NoStackTraceException()
            },
            { fail("should never success. $it") },
        )

        verify {
            updateBillService wasNot Called
        }
    }

    @ParameterizedTest
    @MethodSource("syncSuccess")
    fun `deve retornar sucesso caso chame calculadora`(syncStatus: BillSynchronizationStatus, expectedResult: BillTrackingCalculateResult) {
        every {
            calculator.calculate(
                amount = any(),
                dueDate = any(),
                processingDate = any(),
                fineData = any(),
                interestData = any(),
                discountData = any(),
                abatement = any(),
            )
        } returns Either.Right(CalculatorAmountFixture.create())

        every {
            updateBillService.synchronizeBill(any<BillId>(), any())
        } returns SynchronizeBillResponse(syncStatus)
        every { billTrackingRepository.update(any(), any()) } just runs

        service.execute(
            TrackableBillFixture.create(amountCalculationModel = AmountCalculationModel.ANYONE, isDda = true),
            BillTrackingCalculateOptions.CALCULATE,
        ).mapLeft {
            return fail("should not fail. $it")
        } shouldBe expectedResult.right()

        verify {
            calculator.calculate(
                amount = any(),
                dueDate = any(),
                fineData = any(),
                interestData = any(),
                discountData = any(),
                abatement = any(),
            )
        }
        verify { provider wasNot Called }

        verify { updateBillService.synchronizeBill(any<BillId>(), any()) }
        val slot = slot<LocalDate>()
        verify { billTrackingRepository.update(any(), capture(slot)) }
        slot.captured shouldBe getLocalDate().plusDays(1)
    }

    @ParameterizedTest
    @MethodSource("syncSuccess")
    fun `deve retornar sucesso caso chame o provedor`(syncStatus: BillSynchronizationStatus, expectedResult: BillTrackingCalculateResult) {
        val response = BillValidationFixture.create()

        every { provider.validate(any<BarCode>()) } returns response

        every {
            updateBillService.synchronizeBill(any<BillId>(), any())
        } returns SynchronizeBillResponse(syncStatus)
        every { billTrackingRepository.update(any(), any()) } just runs

        service.execute(
            TrackableBillFixture.create(AmountCalculationModel.ON_DEMAND),
            BillTrackingCalculateOptions.CALCULATE,
        ).mapLeft {
            return fail("should not fail. $it")
        } shouldBe expectedResult.right()

        verify {
            provider.validate(any<BarCode>())
        }
        verify { calculator wasNot Called }

        verify { updateBillService.synchronizeBill(any<BillId>(), any()) }
        val slot = slot<LocalDate>()
        verify { billTrackingRepository.update(any(), capture(slot)) }
        slot.captured shouldBe getLocalDate().plusDays(1)
    }

    @Test
    fun `nao deve chamar o provedor se a origem for dda`() {
        every { billTrackingRepository.remove(any()) } just runs

        service.execute(
            TrackableBillFixture.create(amountCalculationModel = AmountCalculationModel.ON_DEMAND, isDda = true),
            BillTrackingCalculateOptions.CALCULATE,
        ).mapLeft {
            return fail("should not fail. $it")
        } shouldBe BillTrackingCalculateResult.REMOVED.right()

        verify {
            provider wasNot Called
            calculator wasNot Called
            updateBillService wasNot Called
            billTrackingRepository.remove(any())
        }
    }

    @Test
    fun `deve retornar erro retentável caso chame calculadora mas aconteça algum erro de sync retentável`() {
        every {
            calculator.calculate(
                amount = any(),
                dueDate = any(),
                processingDate = any(),
                fineData = any(),
                interestData = any(),
                discountData = any(),
                abatement = any(),
            )
        } returns Either.Right(CalculatorAmountFixture.create())

        every {
            updateBillService.synchronizeBill(any<BillId>(), any())
        } returns SynchronizeBillResponse(BillSynchronizationStatus.UnableToValidate(true))

        service.execute(
            TrackableBillFixture.create(amountCalculationModel = AmountCalculationModel.ANYONE, isDda = true),
            BillTrackingCalculateOptions.CALCULATE,
        ).fold(
            {
                Assertions.assertTrue(it is SyncRetryable)
            },
            { fail("should never success. $it") },
        )

        verify {
            calculator.calculate(
                amount = any(),
                dueDate = any(),
                fineData = any(),
                interestData = any(),
                discountData = any(),
                abatement = any(),
            )
        }
        verify { provider wasNot Called }
        verify { updateBillService.synchronizeBill(any<BillId>(), any()) }
        verify { billTrackingRepository wasNot called }
    }

    @Test
    fun `deve retornar erro nao retentável caso chame calculadora mas aconteça algum erro de sync não retentável`() {
        every {
            calculator.calculate(
                amount = any(),
                dueDate = any(),
                processingDate = any(),
                fineData = any(),
                interestData = any(),
                discountData = any(),
                abatement = any(),
            )
        } returns Either.Right(CalculatorAmountFixture.create())

        every {
            updateBillService.synchronizeBill(any<BillId>(), any())
        } returns SynchronizeBillResponse(BillSynchronizationStatus.UnableToValidate(false))
        every { billTrackingRepository.update(any(), any()) } just runs

        service.execute(
            TrackableBillFixture.create(amountCalculationModel = AmountCalculationModel.ANYONE, isDda = true),
            BillTrackingCalculateOptions.CALCULATE,
        ).fold(
            {
                Assertions.assertTrue(it is UnableToSync)
            },
            { fail("should never success. $it") },
        )

        verify {
            calculator.calculate(
                amount = any(),
                dueDate = any(),
                fineData = any(),
                interestData = any(),
                discountData = any(),
                abatement = any(),
            )
        }

        verify {
            provider wasNot Called
        }

        verify { updateBillService.synchronizeBill(any<BillId>(), any()) }
        val slot = slot<LocalDate>()
        verify { billTrackingRepository.update(any(), capture(slot)) }
        slot.captured shouldBe getLocalDate().plusDays(1)
    }

    @Test
    fun `deve modificar o tipo da bill tracking caso seja um calculo não suportado pela calculadora interna`() {
        every {
            calculator.calculate(
                amount = any(),
                dueDate = any(),
                processingDate = any(),
                fineData = any(),
                interestData = any(),
                discountData = any(),
                abatement = any(),
            )
        } returns Either.Left(NotSupported(""))

        every {
            billTrackingRepository.update(any(), any(), any())
        } just runs

        service.execute(
            TrackableBillFixture.create(amountCalculationModel = AmountCalculationModel.ANYONE, isDda = true),
            BillTrackingCalculateOptions.CALCULATE,
        ).fold(
            {
                Assertions.assertTrue(it is NotSupported)
            },
            { fail("should never success. $it") },
        )

        verify { provider wasNot Called }

        val slot = slot<LocalDate>()
        verify { billTrackingRepository.update(any(), BillTrackingCalculateOptions.QUERY, capture(slot)) }
        slot.captured shouldBe getLocalDate()

        verify {
            calculator.calculate(
                amount = any(),
                dueDate = any(),
                fineData = any(),
                interestData = any(),
                discountData = any(),
                abatement = any(),
            )
        }
    }

    @Test
    fun `deve ter retorno esperado de validation response do método de calculo`() {
        val calculatorResponse = CalculatorAmountFixture.create()

        every {
            calculator.calculate(
                amount = any(),
                dueDate = any(),
                processingDate = any(),
                fineData = any(),
                interestData = any(),
                discountData = any(),
                abatement = any(),
            )
        } returns Either.Right(calculatorResponse)

        val trackableBill = TrackableBillFixture.create()

        val response = service.sendToCalculator(trackableBill = trackableBill).getOrElse { fail("Não deve falhar") }

        with(response.billRegisterData!!) {
            billType shouldBe trackableBill.billType
            assignor shouldBe bill.assignor
            recipient shouldBe bill.recipient
            payerDocument shouldBe bill.payer!!.document
            amount shouldBe calculatorResponse.originalAmount
            discount shouldBe calculatorResponse.discount
            interest shouldBe calculatorResponse.interest
            fine shouldBe calculatorResponse.fine
            amountTotal shouldBe calculatorResponse.totalAmount
            expirationDate shouldBe LocalDate.parse(bill.expirationDate)
            dueDate shouldBe bill.dueDate
            amountCalculationValidUntil shouldBe calculatorResponse.validUntil
        }
    }

    @Test
    fun `deve remover o bill tracking se o título está fora do período de rastreamento`() {
        every { billTrackingRepository.remove(any()) } just runs
        every { billTrackingConfiguration.doNotTrackAfterDays } returns 30

        val date: ZonedDateTime = LocalDate.of(2022, 12, 12).atStartOfDay(brazilTimeZone)
        val dueDate: ZonedDateTime = LocalDate.of(2022, 10, 12).atStartOfDay(brazilTimeZone)

        val bill = Bill.build(billAdded.copy(dueDate = dueDate.toLocalDate(), billType = BillType.FICHA_COMPENSACAO))
        every { billEventRepository.getBillById(any()) } returns bill.right()

        withGivenDateTime(date) {
            service.execute(
                TrackableBillFixture.create(
                    amountCalculationModel = AmountCalculationModel.ANYONE,
                    isDda = true,
                    dueDate = dueDate.toLocalDate(),
                ),
                BillTrackingCalculateOptions.CALCULATE,
            ) shouldBe BillTrackingCalculateResult.REMOVED.right()
        }

        verify {
            billTrackingRepository.remove(any())
        }

        verify(exactly = 0) {
            updateBillService.synchronizeBill(any())
        }
    }

    @Test
    fun `deve remover o bill tracking se o calculo for valido indefinidamente`() {
        every { billTrackingRepository.remove(any()) } just runs

        every {
            calculator.calculate(
                amount = any(),
                dueDate = any(),
                processingDate = any(),
                fineData = any(),
                interestData = any(),
                discountData = any(),
                abatement = any(),
            )
        } returns Either.Right(CalculatorAmountFixture.create(validUntil = null))

        every {
            updateBillService.synchronizeBill(any<BillId>(), any())
        } returns SynchronizeBillResponse(BillSynchronizationStatus.BillAmountTotalUpdated)
        every { billTrackingRepository.update(any(), any()) } just runs

        service.execute(
            TrackableBillFixture.create(
                amountCalculationModel = AmountCalculationModel.ANYONE,
                isDda = true,
                dueDate = bill.dueDate,
            ),
            BillTrackingCalculateOptions.CALCULATE,
        ) shouldBe BillTrackingCalculateResult.REMOVED.right()

        verify {
            billTrackingRepository.remove(any())
        }
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "null",
            "0",
            "-1",
        ],
        nullValues = ["null"],
    )
    fun `não deve remover o bill tracking se o período de tolerância ao rastreamento for inválido`(
        doNotTrackAfterDays: Long?,
    ) {
        val date: ZonedDateTime = LocalDate.of(2022, 12, 12).atStartOfDay(brazilTimeZone)
        val dueDate: ZonedDateTime = LocalDate.of(2022, 10, 12).atStartOfDay(brazilTimeZone)

        every { billTrackingConfiguration.doNotTrackAfterDays } returns doNotTrackAfterDays
        val calculatorResponse = CalculatorAmountFixture.create()

        every {
            calculator.calculate(
                amount = any(),
                dueDate = any(),
                processingDate = any(),
                fineData = any(),
                interestData = any(),
                discountData = any(),
                abatement = any(),
            )
        } returns Either.Right(calculatorResponse)

        every { updateBillService.synchronizeBill(any<BillId>(), any()) } returns SynchronizeBillResponse(
            BillSynchronizationStatus.BillAmountTotalUpdated,
        )

        withGivenDateTime(date) {
            service.execute(
                TrackableBillFixture.create(
                    amountCalculationModel = AmountCalculationModel.ANYONE,
                    isDda = true,
                    dueDate = dueDate.toLocalDate(),
                ),
                BillTrackingCalculateOptions.CALCULATE,
            )
        }.mapLeft {
            return fail("should not fail $it")
        } shouldBe BillTrackingCalculateResult.UPDATED.right()

        verify(exactly = 0) {
            billTrackingRepository.remove(any())
        }

        verify(exactly = 1) {
            updateBillService.synchronizeBill(any<BillId>(), any())
        }
    }

    @Test
    fun `não deve remover o bill tracking se ainda está dentro do período de rastreamento`() {
        every {
            calculator.calculate(
                amount = any(),
                dueDate = any(),
                processingDate = any(),
                fineData = any(),
                interestData = any(),
                discountData = any(),
                abatement = any(),
            )
        } returns Either.Right(CalculatorAmountFixture.create())

        every { billTrackingRepository.update(any(), any()) } just runs
        every { billTrackingRepository.remove(any()) } just runs

        every {
            updateBillService.synchronizeBill(any<BillId>(), any())
        } returns SynchronizeBillResponse(BillSynchronizationStatus.BillAmountTotalUpdated)

        val date: ZonedDateTime = LocalDate.of(2022, 12, 12).atStartOfDay(brazilTimeZone)

        withGivenDateTime(date) {
            service.execute(
                TrackableBillFixture.create(
                    amountCalculationModel = AmountCalculationModel.ANYONE,
                    isDda = true,
                    dueDate = date.toLocalDate(),
                ),
                BillTrackingCalculateOptions.CALCULATE,
            )
        }.mapLeft {
            return fail("should not fail $it")
        } shouldBe BillTrackingCalculateResult.UPDATED.right()

        verify(exactly = 0) {
            billTrackingRepository.remove(any())
        }
    }

    @Test
    fun `deve parar de rastrear títulos que não estão mais ativos`() {
        every {
            calculator.calculate(
                amount = any(),
                dueDate = any(),
                processingDate = any(),
                fineData = any(),
                interestData = any(),
                discountData = any(),
                abatement = any(),
            )
        } returns Either.Right(CalculatorAmountFixture.create())

        every { billTrackingRepository.remove(any()) } just runs

        every { billEventRepository.getBillById(any()) } returns Bill.build(billAdded, billPaid).right()

        service.execute(
            TrackableBillFixture.create(
                amountCalculationModel = AmountCalculationModel.ANYONE,
                isDda = true,
            ),
            BillTrackingCalculateOptions.CALCULATE,
        ).mapLeft {
            return fail("should not fail $it")
        } shouldBe BillTrackingCalculateResult.REMOVED.right()

        verify {
            billTrackingRepository.remove(any())
        }

        verify { updateBillService wasNot Called }
    }

    @Test
    fun `deve rastrear títulos que estão ativos`() {
        every {
            calculator.calculate(
                amount = any(),
                dueDate = any(),
                processingDate = any(),
                fineData = any(),
                interestData = any(),
                discountData = any(),
                abatement = any(),
            )
        } returns Either.Right(CalculatorAmountFixture.create())

        every { billTrackingRepository.update(any(), any()) } just runs
        every { billTrackingRepository.remove(any()) } just runs

        every {
            updateBillService.synchronizeBill(any<BillId>(), any())
        } returns SynchronizeBillResponse(BillSynchronizationStatus.BillAmountTotalUpdated)

        service.execute(
            TrackableBillFixture.create(
                amountCalculationModel = AmountCalculationModel.ANYONE,
                isDda = true,
            ),
            BillTrackingCalculateOptions.CALCULATE,
        ) shouldBe BillTrackingCalculateResult.UPDATED.right()

        verify(exactly = 0) {
            billTrackingRepository.remove(any())
        }
    }

    @Test
    fun `deve remover o rastreamento caso o título não exista mais`() {
        every {
            calculator.calculate(
                amount = any(),
                dueDate = any(),
                processingDate = any(),
                fineData = any(),
                interestData = any(),
                discountData = any(),
                abatement = any(),
            )
        } returns Either.Right(CalculatorAmountFixture.create())

        every { billTrackingRepository.remove(any()) } just runs

        every { billEventRepository.getBillById(any()) } returns ItemNotFoundException(bill.billId).left()

        service.execute(
            TrackableBillFixture.create(
                amountCalculationModel = AmountCalculationModel.ANYONE,
                isDda = true,
            ),
            BillTrackingCalculateOptions.CALCULATE,
        ).mapLeft {
            return fail("should not fail $it")
        } shouldBe BillTrackingCalculateResult.REMOVED.right()

        verify {
            billTrackingRepository.remove(any())
        }

        verify { updateBillService wasNot Called }
    }

    @Test
    fun `deve remover o rastreamento caso o título não exista no provedor de DDA`() {
        every { billTrackingRepository.remove(any()) } just runs
        every { billEventRepository.getBillById(any()) } returns bill.right()

        every { provider.validate(any<BarCode>()) } returns barcodeNotFoundValidationResponse

        service.execute(TrackableBillFixture.create(), BillTrackingCalculateOptions.QUERY) shouldBe BillTrackingCalculateResult.REMOVED.right()

        verify(exactly = 1) {
            billTrackingRepository.remove(any())
        }

        verify { updateBillService wasNot Called }
    }

    companion object {
        @JvmStatic
        fun syncSuccess(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(BillSynchronizationStatus.BillNotModified, BillTrackingCalculateResult.NOT_MODIFIED),
                Arguments.of(BillSynchronizationStatus.BillStatusUpdated(billStatus = BillStatus.ACTIVE), BillTrackingCalculateResult.UPDATED),
                Arguments.of(BillSynchronizationStatus.BillAmountTotalUpdated, BillTrackingCalculateResult.UPDATED),
            )
        }
    }
}