package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.vehicledebts.LicensePlate
import ai.friday.billpayment.app.wallet.WalletId
import io.kotest.matchers.shouldBe
import java.time.LocalDate
import java.time.LocalTime
import org.junit.jupiter.api.Test

class VehicleDebtEnrichmentTest {

    @Test
    fun `deve enriquecer um debito veicular com metadata, source e externalId usando RegisterUpdated`() {
        // Arrange
        val accountId = AccountId("ACCOUNT-123")
        val licensePlate = LicensePlate("ABC1234")
        val billId = BillId()
        val walletId = WalletId(accountId.value)

        val billAdded = BillAdded(
            billId = billId,
            walletId = walletId,
            description = "Débito Veicular",
            dueDate = LocalDate.now(),
            amount = 10000,
            billType = BillType.FICHA_COMPENSACAO,
            paymentLimitTime = LocalTime.of(23, 59, 59),
            actionSource = ActionSource.VehicleDebts(accountId, null),
            amountCalculationModel = AmountCalculationModel.ANYONE,
        )

        val externalId = ExternalBillId("EXT-123", ExternalBillProvider.VEHICLE_DEBTS)
        val details = "Débito de IPVA 2023 para o veículo de placa ABC1234"

        val enrichmentData = UpdatedRegisterData.VehicleDebtEnrichment(
            externalId = externalId,
            details = details,
        )

        val registerUpdatedEvent = RegisterUpdated(
            billId = billId,
            walletId = walletId,
            actionSource = ActionSource.VehicleDebts(accountId, licensePlate),
            updatedRegisterData = enrichmentData,
        )

        val bill = Bill.build(billAdded, registerUpdatedEvent)

        bill.source shouldBe ActionSource.VehicleDebts(accountId, licensePlate)
        bill.externalId shouldBe externalId
        bill.details shouldBe details
    }
}