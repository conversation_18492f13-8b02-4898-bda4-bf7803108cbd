package ai.friday.billpayment.app.bill.tracking

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.integrations.BillTrackingRepository
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billDenied
import ai.friday.billpayment.billDescriptionUpdated
import ai.friday.billpayment.billIgnored
import ai.friday.billpayment.billMarkedAsPaid
import ai.friday.billpayment.billMoved
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentFailed
import ai.friday.billpayment.billPaymentScheduleStarted
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.billPermissionUpdated
import ai.friday.billpayment.billReactivated
import ai.friday.billpayment.billRecipientUpdated
import ai.friday.billpayment.billRecurrenceUpdated
import ai.friday.billpayment.billRefunded
import ai.friday.billpayment.billRegisterUpdatedAlreadyPaid
import ai.friday.billpayment.invoicePaymentPostponed
import ai.friday.billpayment.pixPaymentScheduledCanceled
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDate
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class DefaultReactivateTrackableBillServiceTest {
    private lateinit var test: DefaultReactivateTrackableBillService

    private lateinit var service: UpdateBillService
    private lateinit var repository: BillTrackingRepository

    @BeforeEach
    fun setUp() {
        service = mockk()
        repository = mockk()
        val createService = DefaultCreateTrackableBillService(repository = repository)

        test = DefaultReactivateTrackableBillService(service = service, createService = createService)
    }

    @ParameterizedTest
    @MethodSource("addEventsNotFichaCompensacao")
    fun `should not save when the first bill history is not a FichaCompensacaoAdded`(event: BillEvent) {
        val bill = Bill.build(event)

        every { service.getBill(any()) } returns bill

        val result = test.reactivate(billAddedFicha)

        result.isRight() shouldBe true
        result.map { it shouldBe Unit }

        verify { service.getBill(billAddedFicha.billId) }
        verify { repository wasNot Called }
    }

    @Test
    fun `should reactivate`() {
        val bill = Bill.build(billAddedFicha)

        every { service.getBill(any()) } returns bill
        every { repository.create(any(), any()) } just runs

        val result = test.reactivate(billAddedFicha)

        result.isRight() shouldBe true
        result.map { it shouldBe Unit }

        verify { service.getBill(billAddedFicha.billId) }
        val slot = slot<LocalDate>()
        verify { repository.create(any(), capture(slot)) }
        slot.captured shouldBe getLocalDate().plusDays(1)
    }

    @Test
    fun `should return error when service throws an error`() {
        every { service.getBill(any()) } throws NoStackTraceException()

        val result = test.reactivate(billAddedFicha)

        result.isLeft() shouldBe true
        result.mapLeft { it.ex.shouldBeTypeOf<NoStackTraceException>() }

        verify { service.getBill(billAddedFicha.billId) }
    }

    @Test
    fun `should return error when repository throws an error`() {
        val bill = Bill.build(billAddedFicha)

        every { service.getBill(any()) } returns bill
        every { repository.create(any(), any()) } throws NoStackTraceException()

        val result = test.reactivate(billAddedFicha)

        result.isLeft() shouldBe true
        result.mapLeft { it.ex.shouldBeTypeOf<NoStackTraceException>() }

        verify { service.getBill(billAddedFicha.billId) }
        val slot = slot<LocalDate>()
        verify { repository.create(any(), capture(slot)) }
        slot.captured shouldBe getLocalDate().plusDays(1)
    }

    companion object {
        @JvmStatic
        fun addEventsNotFichaCompensacao() = listOf(
            billAdded, billPaymentStart, billPaymentFailed, billRefunded,
            billDescriptionUpdated, billPaid, billReactivated, billMoved, billPaymentScheduled,
            pixPaymentScheduledCanceled, billIgnored, billPaymentScheduleStarted, invoicePaymentPostponed,
            billRegisterUpdatedAlreadyPaid, billRecurrenceUpdated, billRecipientUpdated, billPermissionUpdated,
            billMarkedAsPaid, billDenied,
        ).filter { it.eventType == BillEventType.ADD }.map { Arguments.of(it) }.stream()
    }
}