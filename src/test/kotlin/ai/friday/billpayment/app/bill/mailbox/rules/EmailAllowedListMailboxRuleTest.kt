package ai.friday.billpayment.app.bill.mailbox.rules

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.mailbox.MailboxValidateRequestFixture.validateRequest
import ai.friday.billpayment.app.integrations.MailboxListsService
import ai.friday.billpayment.app.integrations.MailboxWalletDataRepository
import ai.friday.billpayment.app.mailbox.MailboxGlobalData
import ai.friday.billpayment.app.mailbox.MailboxListType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.WALLET_ID
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class EmailAllowedListMailboxRuleTest {

    private val sender = "<EMAIL>"

    private val allowedEmails = mutableListOf(sender, "abc.com.br")

    private val mailboxListsService = mockk<MailboxListsService>(relaxed = true)

    private val mailboxWalletDataRepository = mockk<MailboxWalletDataRepository>()

    private val rule = EmailAllowedListMailboxRule(mailboxWalletDataRepository)

    @BeforeEach
    fun setup() {
        reloadData()
    }

    private fun reloadData(allowedEmails: List<String> = this.allowedEmails) {
        clearAllMocks()
        every {
            mailboxListsService.findGlobal(MailboxListType.ALLOWED)
        } returns allowedEmails
        MailboxGlobalData(mailboxListsService).loadData()
    }

    @Test
    fun `Deve retornar erro se o email do remetente nao estiver na allowedList global e nem na da carteira`() {
        every {
            mailboxWalletDataRepository.has(any(), any(), any())
        } returns false

        val result = rule.execute(validateRequest("<EMAIL>"))
        result.isLeft().shouldBeTrue()
        result.mapLeft {
            it.reason shouldBe "SenderNotAllowed"
        }
    }

    @Test
    fun `Deve retornar sucesso se o email do remetente nao estiver na allowedList global mas estiver na da carteira`() {
        val email = EmailAddress("<EMAIL>")

        every {
            mailboxWalletDataRepository.has(any(), any(), any())
        } returns false

        val result = rule.execute(validateRequest(email.value))
        result.isLeft().shouldBeTrue()
        result.mapLeft {
            it.reason shouldBe "SenderNotAllowed"
        }

        every {
            mailboxWalletDataRepository.has(WalletId(WALLET_ID), MailboxListType.ALLOWED, email)
        }
    }

    @Test
    fun `Deve retornar sucesso se o email do remetente estiver na allowedList`() {
        rule.execute(validateRequest(sender)).isRight().shouldBeTrue()
    }

    @Test
    fun `Deve retornar sucesso se o dominio do email do remetente estiver na allowedList`() {
        rule.execute(validateRequest("<EMAIL>")).isRight().shouldBeTrue()
    }

    @Test
    fun `Deve retornar sucesso se a allowedList estiver vazia`() {
        reloadData(emptyList())
        rule
            .execute(validateRequest("<EMAIL>"))
            .isRight()
            .shouldBeTrue()
    }

    @Test
    fun `deve retornar sucesso se o fromAccountId existir`() {
        val result = rule.execute(validateRequest(sender = "<EMAIL>", fromAccountId = AccountId("123")))
        result.isRight().shouldBeTrue()
    }
}