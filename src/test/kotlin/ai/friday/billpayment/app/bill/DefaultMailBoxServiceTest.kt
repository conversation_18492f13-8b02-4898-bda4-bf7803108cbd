package ai.friday.billpayment.app.bill

import ai.friday.billpayment.EMAIL_ADDRESS
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.bill.CreateBillResult.FAILURE.BillNotPayable
import ai.friday.billpayment.app.bill.mailbox.MailboxSecurityService
import ai.friday.billpayment.app.bill.mailbox.MailboxSecurityValidationErrorLevel
import ai.friday.billpayment.app.integrations.BillInstrumentationService
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billRegisterData
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.getActiveFichaDeCompensacao
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeSameInstanceAs
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.http.client.exceptions.ReadTimeoutException.TIMEOUT_EXCEPTION
import io.mockk.Called
import io.mockk.called
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDate
import java.util.stream.Stream
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class DefaultMailBoxServiceTest {
    private val createBillService: CreateBillService = mockk(relaxed = true)
    private val fichaCompensacaoService: FichaCompensacaoService = mockk(relaxed = true)
    private val notificationAdapter: NotificationAdapter = mockk(relaxed = true)
    private val walletService: WalletService = mockk(relaxed = true)
    private val billInstrumentationService: BillInstrumentationService = mockk(relaxUnitFun = true)
    private val mailboxSecurityService: MailboxSecurityService = mockk {
        every { execute(any()) } returns emptyList()
    }

    private val mailBoxService =
        DefaultMailBoxService(
            createBillService = createBillService,
            notificationAdapter = notificationAdapter,
            walletService = walletService,
            billInstrumentationService = billInstrumentationService,
            mailboxSecurityService = mailboxSecurityService,
            fichaCompensacaoService = fichaCompensacaoService,
            accountService = mockk {
                every {
                    findAccountById(any())
                } returns walletFixture.founderAccount.copy(
                    configuration = walletFixture.founderAccount.configuration.copy(
                        groups = listOf(AccountGroup.ALPHA),
                    ),
                )
            },
        )

    val account = walletFixture.cantPayParticipantAccount
    val accountId = account.accountId
    val actionSource = ActionSource.WalletMailBox(from = account.emailAddress.value, accountId = accountId)

    val invalidSubject =
        "'Conta nova' com descricao maior do que 140 caracteres e caracteres especiais|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||corta aqui"
    val sanitizedSubject =
        "_Conta nova_ com descricao maior do que 140 caracteres e caracteres especiais_______________________________________________________________"

    @BeforeEach
    fun setUp() {
        clearMocks(createBillService, notificationAdapter)
        every { walletService.findWallet(wallet.id) } returns wallet
    }

    @Test
    fun `should not add bill when security validation returns a BLOCK result`() {
        val member = walletFixture.participant

        every {
            mailboxSecurityService.execute(any())
        } returns listOf(
            MailboxAddBillError.MailboxSecurityValidationError(
                "teste",
                MailboxSecurityValidationErrorLevel.BLOCK,
            ),
        )

        mailBoxService.addBill(
            walletId = wallet.id,
            barCode = billAdded.barcode!!,
            dueDate = getLocalDate(),
            from = member.emailAddress,
            subject = invalidSubject,
            shouldNotifyOnRetryableError = false,
        )

        verify {
            billInstrumentationService wasNot called
            createBillService wasNot called
            notificationAdapter wasNot called
        }
    }

    @ParameterizedTest
    @MethodSource("walletMembers")
    fun `should add concessionaria for member`(member: Member) {
        every {
            mailboxSecurityService.execute(any())
        } returns emptyList()

        val actionSource = ActionSource.WalletMailBox(
            from = member.emailAddress.value,
            accountId = member.accountId,
        )
        val bill = Bill.build(billAdded.copy(actionSource = actionSource))
        val slot = slot<ConcessionariaRequestWithDueDate>()
        every { createBillService.createConcessionaria(capture(slot)) } answers {
            CreateBillResult.SUCCESS(bill)
        }

        mailBoxService.addBill(
            walletId = wallet.id,
            barCode = bill.barcode!!,
            dueDate = getLocalDate(),
            from = member.emailAddress,
            subject = invalidSubject,
            shouldNotifyOnRetryableError = false,
        )

        slot.captured.source shouldBe actionSource
        slot.captured.description shouldBe sanitizedSubject

        verify {
            billInstrumentationService.createdMailbox(bill)
            fichaCompensacaoService.createFichaDeCompensacao(any()) wasNot called
            notificationAdapter wasNot called
        }
    }

    @ParameterizedTest
    @MethodSource("walletMembers")
    fun `should add ficha de compensacao for member`(member: Member) {
        every {
            mailboxSecurityService.execute(any())
        } returns listOf(
            MailboxAddBillError.MailboxSecurityValidationError(
                "teste",
                MailboxSecurityValidationErrorLevel.WARN,
            ),
        )

        val actionSource = ActionSource.WalletMailBox(
            from = member.emailAddress.value,
            accountId = member.accountId,
        )
        val bill = Bill.build(billAddedFicha.copy(actionSource = actionSource))
        val slot = slot<CreateFichaDeCompensacaoRequest>()
        every { fichaCompensacaoService.createFichaDeCompensacao(capture(slot)) } answers {
            CreateBillResult.SUCCESS(bill)
        }

        mailBoxService.addBill(
            walletId = wallet.id,
            barCode = bill.barcode!!,
            dueDate = getLocalDate(),
            from = member.emailAddress,
            subject = invalidSubject,
            shouldNotifyOnRetryableError = false,
        )

        slot.captured.source shouldBe actionSource
        slot.captured.description shouldBe sanitizedSubject

        verify {
            billInstrumentationService.createdMailbox(bill)
            createBillService.createConcessionaria(any()) wasNot called
            notificationAdapter wasNot called
        }
    }

    @Test
    fun shouldSendNotPayableNotificationOnNotPayableCelcoinResponse() {
        val activeBill = getActiveFichaDeCompensacao(wallet.id)
        every { fichaCompensacaoService.createFichaDeCompensacao(ofType(CreateFichaDeCompensacaoRequest::class)) } answers {
            BillNotPayable(
                "",
                billRegisterData = billRegisterData,
            )
        }
        val result = mailBoxService.addBill(
            walletId = wallet.id,
            barCode = activeBill.barCode!!,
            dueDate = getLocalDate(),
            from = walletFixture.participant.emailAddress,
            subject = " ",
            shouldNotifyOnRetryableError = false,
        )

        result.isLeft().shouldBeTrue()
        result.mapLeft {
            with(it as MailboxAddBillError.MailboxAddInvalidBill) {
                this.result.shouldBeTypeOf<BillNotPayable>()
            }
        }

        val addBillResultSlot = slot<CreateBillResult>()
        verify {
            notificationAdapter.notifyAddFailure(
                members = listOf(walletFixture.participant),
                source = any(),
                from = any(),
                subject = "_(sem assunto)_",
                walletName = any(),
                billId = any(),
                walletId = any(),
                result = capture(addBillResultSlot),
            )
            fichaCompensacaoService.createFichaDeCompensacao(ofType(CreateFichaDeCompensacaoRequest::class))
            billInstrumentationService wasNot Called
        }

        assertTrue(addBillResultSlot.captured is BillNotPayable)
    }

    @Test
    fun `should send not payable notification on not payable celcoin response for concessionaria`() {
        val activeBill = getActiveBill(wallet.id).copy(dueDate = getLocalDate().minusDays(3))
        every { createBillService.createConcessionaria(any()) } answers {
            BillNotPayable(
                "",
                billRegisterData = billRegisterData,
            )
        }
        val result = mailBoxService.addBill(
            walletId = wallet.id,
            barCode = activeBill.barCode!!,
            dueDate = getLocalDate().minusDays(3),
            from = walletFixture.participant.emailAddress,
            subject = " ",
            shouldNotifyOnRetryableError = false,
        )

        result.isLeft().shouldBeTrue()
        result.mapLeft {
            with(it as MailboxAddBillError.MailboxAddInvalidBill) {
                this.result.shouldBeTypeOf<BillNotPayable>()
            }
        }

        val addBillResultSlot = slot<CreateBillResult>()
        verify {
            notificationAdapter.notifyAddFailure(
                members = listOf(walletFixture.participant),
                source = any(),
                from = any(),
                subject = "_(sem assunto)_",
                walletName = any(),
                billId = any(),
                walletId = any(),
                result = capture(addBillResultSlot),
            )
            createBillService.createConcessionaria(any())
            billInstrumentationService wasNot Called
        }

        assertTrue(addBillResultSlot.captured is BillNotPayable)
    }

    @Test
    fun `quando o 'shouldNotifyOnRetryableError' é verdadeiro e o erro não é retentavel, deve notificar`() {
        every { fichaCompensacaoService.createFichaDeCompensacao(ofType(CreateFichaDeCompensacaoRequest::class)) } answers {
            CreateBillResult.FAILURE.BillAlreadyExists(
                Bill.build(billAddedFicha),
            )
        }

        val result = mailBoxService.addBill(
            walletId = account.configuration.defaultWalletId!!,
            barCode = billAddedFicha.barcode,
            dueDate = null,
            from = account.emailAddress,
            subject = "Conta nova",
            shouldNotifyOnRetryableError = true,
        )

        result.isLeft().shouldBeTrue()
        result.mapLeft {
            with(it as MailboxAddBillError.MailboxAddInvalidBill) {
                this.result.shouldBeTypeOf<CreateBillResult.FAILURE.BillAlreadyExists>()
            }
        }

        verify {
            notificationAdapter.notifyAddFailure(
                members = listOf(walletFixture.cantPayParticipant),
                source = actionSource,
                from = account.emailAddress,
                subject = "Conta nova",
                walletName = wallet.name,
                billId = billAddedFicha.billId,
                walletId = wallet.id,
                result = any(),
            )
        }
    }

    @Test
    fun `should send not payable notification on not payable Celcoin response to known author`() {
        every { fichaCompensacaoService.createFichaDeCompensacao(ofType(CreateFichaDeCompensacaoRequest::class)) } answers {
            BillNotPayable(
                "",
                billRegisterData = billRegisterData,
            )
        }
        val result = mailBoxService.addBill(
            walletId = account.configuration.defaultWalletId!!,
            barCode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
            dueDate = getLocalDate(),
            from = account.emailAddress,
            subject = "Conta nova",
            shouldNotifyOnRetryableError = false,
        )

        result.isLeft().shouldBeTrue()
        result.mapLeft {
            with(it as MailboxAddBillError.MailboxAddInvalidBill) {
                this.result.shouldBeTypeOf<BillNotPayable>()
            }
        }

        val addBillResultSlot = slot<CreateBillResult>()
        verify {
            notificationAdapter.notifyAddFailure(
                members = listOf(walletFixture.cantPayParticipant),
                source = actionSource,
                from = account.emailAddress,
                subject = "Conta nova",
                walletName = wallet.name,
                billId = null,
                walletId = wallet.id,
                result = capture(addBillResultSlot),
            )
            fichaCompensacaoService.createFichaDeCompensacao(ofType(CreateFichaDeCompensacaoRequest::class))
            billInstrumentationService wasNot Called
        }

        addBillResultSlot.captured.shouldBeTypeOf<BillNotPayable>()
    }

    @Test
    fun `should send not payable notification to all wallet members who can view all bills when author is unknown`() {
        every { fichaCompensacaoService.createFichaDeCompensacao(ofType(CreateFichaDeCompensacaoRequest::class)) } answers {
            BillNotPayable(
                "",
                billRegisterData = billRegisterData,
            )
        }

        val result = mailBoxService.addBill(
            walletId = account.configuration.defaultWalletId!!,
            barCode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
            dueDate = getLocalDate(),
            from = EMAIL_ADDRESS,
            subject = "Conta nova",
            shouldNotifyOnRetryableError = false,
        )

        result.isLeft().shouldBeTrue()
        result.mapLeft {
            with(it as MailboxAddBillError.MailboxAddInvalidBill) {
                this.result.shouldBeTypeOf<BillNotPayable>()
            }
        }

        val addBillResultSlot = slot<CreateBillResult>()
        verify {
            notificationAdapter.notifyAddFailure(
                members = wallet.getMembersCanViewAll(),
                source = ActionSource.WalletMailBox(from = EMAIL_ADDRESS.value, accountId = null),
                from = EMAIL_ADDRESS,
                subject = "Conta nova",
                walletName = wallet.name,
                billId = null,
                walletId = wallet.id,
                result = capture(addBillResultSlot),
            )
            fichaCompensacaoService.createFichaDeCompensacao(ofType(CreateFichaDeCompensacaoRequest::class))
            billInstrumentationService wasNot Called
        }

        addBillResultSlot.captured.shouldBeTypeOf<BillNotPayable>()
    }

    @Test
    fun `should send not payable notification to all wallet members who can view all bills when author was removed from wallet`() {
        every { fichaCompensacaoService.createFichaDeCompensacao(ofType(CreateFichaDeCompensacaoRequest::class)) } answers {
            BillNotPayable(
                "",
                billRegisterData = billRegisterData,
            )
        }

        every { walletService.findWallet(wallet.id) } returns wallet.copy(members = wallet.allMembers + walletFixture.removedParticipant)

        val result = mailBoxService.addBill(
            walletId = wallet.id,
            barCode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
            dueDate = getLocalDate(),
            from = walletFixture.removedParticipant.emailAddress,
            subject = "Conta nova",
            shouldNotifyOnRetryableError = false,
        )

        result.isLeft().shouldBeTrue()
        result.mapLeft {
            with(it as MailboxAddBillError.MailboxAddInvalidBill) {
                this.result.shouldBeTypeOf<BillNotPayable>()
            }
        }

        verify {
            notificationAdapter.notifyAddFailure(
                members = wallet.getMembersCanViewAll(),
                source = ActionSource.WalletMailBox(from = walletFixture.removedParticipant.emailAddress.value, accountId = walletFixture.removedParticipant.accountId),
                from = walletFixture.removedParticipant.emailAddress,
                subject = "Conta nova",
                walletName = wallet.name,
                billId = null,
                walletId = wallet.id,
                result = any(),
            )
            fichaCompensacaoService.createFichaDeCompensacao(ofType(CreateFichaDeCompensacaoRequest::class))
            billInstrumentationService wasNot Called
        }
    }

    @Test
    fun `should send already exists notification to known author when bill was found`() {
        every { fichaCompensacaoService.createFichaDeCompensacao(ofType(CreateFichaDeCompensacaoRequest::class)) } answers {
            CreateBillResult.FAILURE.BillAlreadyExists(
                Bill.build(billAddedFicha),
            )
        }

        val result = mailBoxService.addBill(
            walletId = account.configuration.defaultWalletId!!,
            barCode = billAddedFicha.barcode,
            dueDate = null,
            from = account.emailAddress,
            subject = "Conta nova",
            shouldNotifyOnRetryableError = false,
        )

        result.isLeft().shouldBeTrue()
        result.mapLeft {
            with(it as MailboxAddBillError.MailboxAddInvalidBill) {
                this.result.shouldBeTypeOf<CreateBillResult.FAILURE.BillAlreadyExists>()
            }
        }

        val addBillResultSlot = slot<CreateBillResult>()
        verify {
            notificationAdapter.notifyAddFailure(
                members = listOf(walletFixture.cantPayParticipant),
                source = actionSource,
                from = account.emailAddress,
                subject = "Conta nova",
                walletName = wallet.name,
                billId = billAddedFicha.billId,
                walletId = wallet.id,
                result = capture(addBillResultSlot),
            )
            fichaCompensacaoService.createFichaDeCompensacao(ofType(CreateFichaDeCompensacaoRequest::class))
            billInstrumentationService wasNot Called
        }

        addBillResultSlot.captured.shouldBeTypeOf<CreateBillResult.FAILURE.BillAlreadyExists>()
    }

    @Test
    fun `should send bill paid externally notification to known author when bill was already paid`() {
        every { fichaCompensacaoService.createFichaDeCompensacao(ofType(CreateFichaDeCompensacaoRequest::class)) } answers {
            CreateBillResult.FAILURE.AlreadyPaid.WithData("", billRegisterData = billRegisterData)
        }

        val result = mailBoxService.addBill(
            walletId = account.configuration.defaultWalletId!!,
            barCode = billAddedFicha.barcode,
            dueDate = null,
            from = account.emailAddress,
            subject = "Conta nova",
            shouldNotifyOnRetryableError = false,
        )

        result.isLeft().shouldBeTrue()
        result.mapLeft {
            with(it as MailboxAddBillError.MailboxAddInvalidBill) {
                this.result.shouldBeTypeOf<CreateBillResult.FAILURE.AlreadyPaid.WithData>()
            }
        }

        val addBillResultSlot = slot<CreateBillResult>()
        verify {
            notificationAdapter.notifyAddFailure(
                members = listOf(walletFixture.cantPayParticipant),
                source = actionSource,
                from = account.emailAddress,
                subject = "Conta nova",
                walletName = wallet.name,
                billId = null,
                walletId = wallet.id,
                result = capture(addBillResultSlot),
            )
            fichaCompensacaoService.createFichaDeCompensacao(ofType(CreateFichaDeCompensacaoRequest::class))
            billInstrumentationService wasNot Called
        }

        addBillResultSlot.captured.shouldBeTypeOf<CreateBillResult.FAILURE.AlreadyPaid.WithData>()
    }

    @Test
    fun `should send validation failure notification to known author when unable to validate bill`() {
        val createBillResult =
            CreateBillResult.FAILURE.BillUnableToValidate("Codigo nao localizado na base centralizada", false)
        every { fichaCompensacaoService.createFichaDeCompensacao(ofType(CreateFichaDeCompensacaoRequest::class)) } answers {
            createBillResult
        }

        val result = mailBoxService.addBill(
            walletId = account.configuration.defaultWalletId!!,
            barCode = billAddedFicha.barcode,
            dueDate = null,
            from = account.emailAddress,
            subject = "Conta nova",
            shouldNotifyOnRetryableError = false,
        )

        result.isLeft().shouldBeTrue()
        result.mapLeft {
            with(it as MailboxAddBillError.MailboxAddInvalidBill) {
                this.result.shouldBeTypeOf<CreateBillResult.FAILURE.BillUnableToValidate>()
            }
        }

        val addBillResultSlot = slot<CreateBillResult>()
        verify {
            notificationAdapter.notifyAddFailure(
                members = listOf(walletFixture.cantPayParticipant),
                source = actionSource,
                from = account.emailAddress,
                subject = "Conta nova",
                walletName = wallet.name,
                billId = null,
                walletId = wallet.id,
                result = capture(addBillResultSlot),
            )
            fichaCompensacaoService.createFichaDeCompensacao(ofType(CreateFichaDeCompensacaoRequest::class))
            billInstrumentationService wasNot Called
        }

        addBillResultSlot.captured.shouldBeSameInstanceAs(createBillResult)
    }

    @Test
    internal fun `should not send notification when unable to validate bill error retryable`() {
        val createBillResult =
            CreateBillResult.FAILURE.BillUnableToValidate("Codigo nao localizado na base centralizada")
        every { fichaCompensacaoService.createFichaDeCompensacao(ofType(CreateFichaDeCompensacaoRequest::class)) } answers {
            createBillResult
        }

        mailBoxService.addBill(
            walletId = account.configuration.defaultWalletId!!,
            barCode = billAddedFicha.barcode,
            dueDate = null,
            from = account.emailAddress,
            subject = "Conta nova",
            shouldNotifyOnRetryableError = false,
        )

        verify {
            notificationAdapter wasNot called
        }
    }

    @Test
    fun `deve enviar notificacao quando o erro é retentavel mas 'shouldNotifyOnRetryableError' é verdadeiro`() {
        val createBillResult =
            CreateBillResult.FAILURE.BillUnableToValidate("Codigo nao localizado na base centralizada")
        every { fichaCompensacaoService.createFichaDeCompensacao(ofType(CreateFichaDeCompensacaoRequest::class)) } answers {
            createBillResult
        }

        mailBoxService.addBill(
            walletId = account.configuration.defaultWalletId!!,
            barCode = billAddedFicha.barcode,
            dueDate = null,
            from = account.emailAddress,
            subject = "Conta nova",
            shouldNotifyOnRetryableError = true,
        )

        verify {
            notificationAdapter.notifyAddFailure(
                members = any(),
                source = any(),
                from = any(),
                subject = any(),
                walletName = any(),
                billId = any(),
                walletId = any(),
                result = any(),
            )
        }
    }

    @Test
    internal fun `should consider as not retryable error when bill is overdue`() {
        val createBillResult =
            CreateBillResult.FAILURE.BillUnableToValidate("Codigo nao localizado na base centralizada", true)
        every { fichaCompensacaoService.createFichaDeCompensacao(ofType(CreateFichaDeCompensacaoRequest::class)) } answers {
            createBillResult
        }

        val result = mailBoxService.addBill(
            walletId = account.configuration.defaultWalletId!!,
            barCode = billAddedFicha.barcode,
            dueDate = LocalDate.parse("2023-01-01"),
            from = account.emailAddress,
            subject = "Conta nova",
            shouldNotifyOnRetryableError = false,
        )

        result.isLeft().shouldBeTrue()
        result.mapLeft {
            with(it as MailboxAddBillError.MailboxAddInvalidBill) {
                this.result.shouldBeTypeOf<CreateBillResult.FAILURE.BillUnableToValidate>()
                this.isRetryable shouldBe false
            }
        }

        verify {
            notificationAdapter.notifyAddFailure(any(), any(), any(), any(), any(), any(), any(), any())
        }
    }

    @Test
    fun `should throw InvalidBillException on server error result`() {
        every { fichaCompensacaoService.createFichaDeCompensacao(ofType(CreateFichaDeCompensacaoRequest::class)) } answers {
            CreateBillResult.FAILURE.ServerError(TIMEOUT_EXCEPTION)
        }

        val result = mailBoxService.addBill(
            walletId = account.configuration.defaultWalletId!!,
            barCode = billAddedFicha.barcode,
            dueDate = null,
            from = account.emailAddress,
            subject = "Conta nova",
            shouldNotifyOnRetryableError = false,
        )

        result.isLeft().shouldBeTrue()
        result.mapLeft {
            with(it as MailboxAddBillError.MailboxAddInvalidBill) {
                this.result.shouldBeTypeOf<CreateBillResult.FAILURE.ServerError>()
            }
        }

        verify {
            notificationAdapter wasNot called
            fichaCompensacaoService.createFichaDeCompensacao(ofType(CreateFichaDeCompensacaoRequest::class))
            billInstrumentationService wasNot Called
        }
    }

    @Test
    fun `should not send already exists notification and not call instrumentation when adding ficha compensacao returns not visible bill already exists`() {
        val slot = slot<CreateFichaDeCompensacaoRequest>()
        every { fichaCompensacaoService.createFichaDeCompensacao(capture(slot)) } answers {
            CreateBillResult.SUCCESS(
                Bill.build(
                    billAddedFicha.copy(
                        actionSource = ActionSource.WalletMailBox(
                            from = account.emailAddress.value,
                            accountId = accountId,
                        ),
                    ),
                ),
                WarningCode.NOT_VISIBLE_BILL_ALREADY_EXISTS,
            )
        }

        mailBoxService.addBill(
            walletId = wallet.id,
            barCode = billAddedFicha.barcode,
            dueDate = null,
            from = walletFixture.cantPayParticipant.emailAddress,
            subject = "Conta nova",
            shouldNotifyOnRetryableError = false,
        )

        slot.captured.member shouldBe walletFixture.cantPayParticipant
        verify {
            notificationAdapter wasNot Called
        }

        verify {
            billInstrumentationService wasNot Called
        }
    }

    @Test
    fun `should not send already exists notification and not call instrumentation when adding concessionaria returns not visible bill already exists`() {
        val slot = slot<ConcessionariaRequestWithDueDate>()
        every { createBillService.createConcessionaria(capture(slot)) } answers {
            CreateBillResult.SUCCESS(
                Bill.build(
                    billAdded.copy(
                        actionSource = ActionSource.WalletMailBox(
                            from = account.emailAddress.value,
                            accountId = accountId,
                        ),
                    ),
                ),
                WarningCode.NOT_VISIBLE_BILL_ALREADY_EXISTS,
            )
        }

        mailBoxService.addBill(
            walletId = wallet.id,
            barCode = billAdded.barcode!!,
            dueDate = billAdded.dueDate,
            from = walletFixture.cantPayParticipant.emailAddress,
            subject = "Conta nova",
            shouldNotifyOnRetryableError = false,
        )

        slot.captured.member shouldBe walletFixture.cantPayParticipant
        verify {
            notificationAdapter wasNot Called
        }

        verify {
            billInstrumentationService wasNot Called
        }
    }

    companion object {
        private val walletFixture = WalletFixture()
        private val wallet = walletFixture.buildWallet(
            otherMembers = listOf(
                walletFixture.participant,
                walletFixture.ultraLimitedParticipant,
                walletFixture.cantPayParticipant,
            ),
        )

        @JvmStatic
        fun walletMembers(): Stream<Arguments> {
            return wallet.allMembers.map { Arguments.arguments(it) }.stream()
        }
    }
}