package ai.friday.billpayment.app.bill.mailbox.rules

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.mailbox.MailboxValidateRequestFixture.validateRequest
import ai.friday.billpayment.app.integrations.MailboxListsService
import ai.friday.billpayment.app.mailbox.MailboxGlobalData
import ai.friday.billpayment.app.mailbox.MailboxListType
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class EmailBlockedListMailboxRuleTest {

    private val senderBlocked = "<EMAIL>"

    private val blockedEmails = mutableListOf(senderBlocked, "abc.com.br")

    private val mailboxListsService = mockk<MailboxListsService>(relaxed = true)

    private val rule = EmailBlockedListMailboxRule()

    @BeforeEach
    fun setup() {
        reloadData()
    }

    private fun reloadData(blockedEmails: List<String> = this.blockedEmails) {
        clearAllMocks()
        every {
            mailboxListsService.findGlobal(MailboxListType.BLOCKED)
        } returns blockedEmails
        MailboxGlobalData(mailboxListsService).loadData()
    }

    @Test
    fun `Deve retornar erro se o email do remetente estiver na blockedList`() {
        val result = rule.execute(validateRequest(senderBlocked))
        result.isLeft().shouldBeTrue()
        result.mapLeft {
            it.reason shouldBe "SenderIsBlocked"
        }
    }

    @Test
    fun `Deve retornar erro se o dominio do email do remetente estiver na blockedList`() {
        val result = rule.execute(validateRequest("<EMAIL>"))
        result.isLeft().shouldBeTrue()
        result.mapLeft {
            it.reason shouldBe "SenderIsBlocked"
        }
    }

    @Test
    fun `deve retornar sucesso se o fromAccountId existir`() {
        val result = rule.execute(validateRequest(sender = "<EMAIL>", fromAccountId = AccountId("123")))
        result.isRight().shouldBeTrue()
    }

    @Test
    fun `Deve retornar sucesso se nem o email nem o dominio estiverem bloqueados`() {
        rule.execute(validateRequest("<EMAIL>")).isRight().shouldBeTrue()
    }

    @Test
    fun `Deve retornar sucesso se a blocked estiver vazia`() {
        reloadData(emptyList())
        rule
            .execute(validateRequest(senderBlocked))
            .isRight()
            .shouldBeTrue()
    }
}