package ai.friday.billpayment.app.bill.mailbox.rules

import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.mailbox.MailboxValidateRequestFixture
import ai.friday.billpayment.integration.CONCESSIONARIA_DIGITABLE_LINE
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

internal class FichaCompensacaoNotAllowedMailboxRuleTest {

    private val rule = FichaCompensacaoNotAllowedMailboxRule()

    @Test
    fun `Deve retornar erro se o boleto for uma ficha de compensacao`() {
        val result = rule.execute(
            MailboxValidateRequestFixture.validateRequest(
                sender = "<EMAIL>",
                barCode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
            ),
        )
        result.isLeft().shouldBeTrue()
        result.mapLeft {
            it.reason shouldBe "FichaCompensacaoNotAllowed"
        }
    }

    @Test
    fun `Deve retornar sucesso se o boleto for uma concessionaria`() {
        rule.execute(
            MailboxValidateRequestFixture.validateRequest(
                sender = "<EMAIL>",
                barCode = BarCode.ofDigitable(CONCESSIONARIA_DIGITABLE_LINE),
            ),
        ).isRight().shouldBeTrue()
    }
}