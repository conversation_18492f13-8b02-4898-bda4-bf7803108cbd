package ai.friday.billpayment.app.bill.tracking

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.TrackableBillFixture
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.TrackableBill
import ai.friday.billpayment.app.integrations.BillTrackingRepository
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDate
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import reactor.core.publisher.Mono

class DefaultUpdateTrackableBillServiceTest {
    private lateinit var test: DefaultUpdateTrackableBillService

    private lateinit var repository: BillTrackingRepository

    @BeforeEach
    fun setUp() {
        repository = mockk()

        test = DefaultUpdateTrackableBillService(repository = repository)
    }

    @Test
    fun `should return ItemNotFoundException error when TrackableBill does not exists during updateAmountByBillID`() {
        every { repository.findById(any()) } returns Mono.empty()

        val result = test.updateAmountByBillID(BillId("123"), 1L)

        result.isLeft() shouldBe true
        result.mapLeft { it.shouldBeTypeOf<UntrackableEntity>() }

        verify { repository.findById(BillId("123")) }
        verify(exactly = 0) { repository.update(any(), any()) }
    }

    @Test
    fun `should update amount when TrackableBill exists`() {
        val trackable = TrackableBillFixture.create()

        every { repository.findById(any()) } returns Mono.just(trackable)
        every { repository.update(any(), any()) } just Runs

        val result = test.updateAmountByBillID(BillId("123"), 1L)

        result.isRight() shouldBe true
        result.map { it shouldBe Unit }

        verify { repository.findById(BillId("123")) }

        val trackableBillSlot = slot<TrackableBill>()
        val dateSlot = slot<LocalDate>()
        verify {
            repository.update(capture(trackableBillSlot), capture(dateSlot))
        }
        trackableBillSlot.captured.amount shouldBe 1L
        dateSlot.captured shouldBe getLocalDate().plusDays(1)
    }

    @Test
    fun `should update due date when TrackableBill exists`() {
        val trackable = TrackableBillFixture.create()

        every { repository.findById(any()) } returns Mono.just(trackable)
        every { repository.update(any(), any()) } just Runs

        val result = test.updateDueDateByBillID(BillId("123"), LocalDate.of(2023, 1, 1))

        result.isRight() shouldBe true
        result.map { it shouldBe Unit }

        verify { repository.findById(BillId("123")) }

        val trackableBillSlot = slot<TrackableBill>()
        val dateSlot = slot<LocalDate>()
        verify {
            repository.update(capture(trackableBillSlot), capture(dateSlot))
        }
        trackableBillSlot.captured.dueDate shouldBe LocalDate.of(2023, 1, 1)
        dateSlot.captured shouldBe getLocalDate().plusDays(1)
    }

    @Test
    fun `should return error when repository throws an error`() {
        every { repository.findById(any()) } throws NoStackTraceException()

        val result = test.updateAmountByBillID(BillId("123"), 1L)

        result.isLeft() shouldBe true
        result.mapLeft { it.ex.shouldBeTypeOf<NoStackTraceException>() }

        verify { repository.findById(BillId("123")) }
        verify(exactly = 0) { repository.update(any(), any()) }
    }
}