package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.payment.DiscountData
import ai.friday.billpayment.app.payment.DiscountType
import ai.friday.billpayment.app.payment.FineData
import ai.friday.billpayment.app.payment.FineType
import ai.friday.billpayment.app.payment.InterestData
import ai.friday.billpayment.app.payment.InterestType
import ai.friday.billpayment.withHolidays
import arrow.core.getOrElse
import io.kotest.matchers.shouldBe
import java.math.BigDecimal
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.stream.Stream
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource
import org.junit.jupiter.params.provider.ValueSource

internal class BillAmountCalculatorTest {

    private val calculator = BillAmountCalculator()

    @Test
    fun `nao deve alterar valor do boleto se nao tiver juros, multa ou desconto`() {
        val calculatedAmount = calculator.calculate(
            amount = 100,
            dueDate = LocalDate.of(2023, 10, 17),
            processingDate = LocalDate.of(2023, 10, 20),
            fineData = FineData(type = FineType.FREE),
            interestData = InterestData(type = InterestType.FREE),
            discountData = DiscountData(type = DiscountType.FREE),
            abatement = 0.0,
        ).getOrElse { fail("should not fail ${it.message}") }

        with(calculatedAmount) {
            originalAmount shouldBe 100
            fine shouldBe 0
            interest shouldBe 0
            discount shouldBe 0
            abatement shouldBe 0
            totalAmount shouldBe 100
            validUntil shouldBe null
        }
    }

    @Nested
    inner class Interest {
        /**
         * - quando nao tem desconto, quando nao tem multa, quando tem juros
         *      - juros com valor fixo -> amountTotal = amount + juros
         *      - juros com percentual mensal -> amountTotal = amount * (percentual/100)
         *
         * considerar juros a partir da data que vier do InterestData
         *
         * caso data nao esteja preenchida como devemos efetuar o calculo de juros ?
         *
         * Data de Início da cobrança de juros.
         *
         * Deve ser posterior á data de vencimento da operação
         *
         * ---
         *
         * Juros por valor fixo ????????
         */

        @ParameterizedTest
        @EnumSource(
            value = InterestType::class,
            mode = EnumSource.Mode.INCLUDE,
            names = ["PERCENT_BY_DAY", "PERCENT_BY_YEAR", "VALUE_WORKING_DAYS", "PERCENT_BY_DAY_WORKING_DAYS", "PERCENT_BY_MONTH_WORKING_DAYS", "PERCENT_BY_YEAR_WORKING_DAYS"],
        )
        fun `deve retornar erro se nao for um tipo de calculo de juros suportado pela calculadora`(type: InterestType) {
            calculator.calculate(
                amount = 100,
                dueDate = LocalDate.of(2022, 1, 3),
                fineData = null,
                interestData = InterestData(
                    type = type,
                    value = BigDecimal.valueOf(10),
                    date = LocalDate.of(2022, 1, 5),
                ),
                discountData = null,
                abatement = null,
                processingDate = LocalDate.of(2022, 1, 6),
            ).fold(
                {
                    Assertions.assertTrue(it is NotSupported)
                },
                { fail("should not work") },
            )
        }

        @ParameterizedTest
        @ValueSource(strings = ["2022-01-01", "2022-01-02", "2022-01-03", "2022-01-04"])
        fun `nao deve aplicar juros caso data de juros for apos a data corrente`(processingDate: String) {
            val calculatedAmount = calculator.calculate(
                amount = 100,
                dueDate = LocalDate.of(2022, 1, 3),
                fineData = null,
                interestData = InterestData(
                    type = InterestType.VALUE,
                    value = BigDecimal.valueOf(10),
                    date = LocalDate.of(2022, 1, 5),
                ),
                discountData = null,
                abatement = null,
                processingDate = LocalDate.parse(processingDate, DateTimeFormatter.ISO_DATE),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe 100
                fine shouldBe 0
                interest shouldBe 0
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 100
                validUntil shouldBe LocalDate.of(2022, 1, 4)
            }
        }

        @Test
        fun `nao deve aplicar juros caso data de juros for antes da data de vencimento efetiva`() {
            val calculatedAmount = calculator.calculate(
                amount = 100,
                dueDate = LocalDate.of(2022, 11, 5),
                fineData = null,
                interestData = InterestData(
                    type = InterestType.VALUE,
                    value = BigDecimal.valueOf(10),
                    date = LocalDate.of(2022, 11, 6),
                ),
                discountData = null,
                abatement = null,
                processingDate = LocalDate.of(2022, 11, 7),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe 100
                fine shouldBe 0
                interest shouldBe 0
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 100
                validUntil shouldBe LocalDate.of(2022, 11, 7)
            }
        }

        @Test
        fun `nao deve adicionar juros caso o boleto seja isento`() {
            val calculatedAmount = calculator.calculate(
                amount = 100,
                dueDate = LocalDate.of(2022, 1, 3),
                fineData = null,
                interestData = InterestData(
                    type = InterestType.FREE,
                    value = null,
                    date = null,
                ),
                discountData = null,
                abatement = null,
                processingDate = LocalDate.of(2022, 1, 6),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe 100
                fine shouldBe 0
                interest shouldBe 0
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 100
                validUntil shouldBe null
            }
        }

        @ParameterizedTest
        @CsvSource(value = ["2022-01-05,1000,2022-01-05", "2022-01-07,3000,2022-01-07", "2022-01-08,6000,2022-01-10"])
        fun `deve adicionar valor fixo de juros por dia corrido apos data prevista`(processingDate: String, expectedInterest: Long, expectedValidUntil: String) {
            val calculatedAmount = calculator.calculate(
                amount = 10000,
                dueDate = LocalDate.of(2022, 1, 3),
                fineData = null,
                interestData = InterestData(
                    type = InterestType.VALUE,
                    value = BigDecimal.valueOf(10),
                    date = LocalDate.of(2022, 1, 5),
                ),
                discountData = null,
                abatement = null,
                processingDate = LocalDate.parse(processingDate, DateTimeFormatter.ISO_DATE),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe 10000
                fine shouldBe 0
                interest shouldBe expectedInterest
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 10000 + expectedInterest
                validUntil shouldBe LocalDate.parse(expectedValidUntil, DateTimeFormatter.ISO_DATE)
            }
        }

        @Test
        fun `deve adicionar valor fixo de juros por dia corrido apos data prevista mas descontado o valor abatido do calculo`() {
            val calculatedAmount = calculator.calculate(
                amount = 10000,
                dueDate = LocalDate.of(2022, 1, 3),
                fineData = null,
                interestData = InterestData(
                    type = InterestType.VALUE,
                    value = BigDecimal.valueOf(10),
                    date = LocalDate.of(2022, 1, 5),
                ),
                discountData = null,
                abatement = 10.00,
                processingDate = LocalDate.of(2022, 1, 7),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe 10000
                fine shouldBe 0
                interest shouldBe 3000
                discount shouldBe 0
                abatement shouldBe 1000
                totalAmount shouldBe 12000
                validUntil shouldBe LocalDate.of(2022, 1, 7)
            }
        }

        @Test
        fun `deve adicionar valor fixo de juros por dia corrido apos data prevista independe de final de semana ou feriado`() {
            val calculatedAmount = calculator.calculate(
                amount = 10000,
                dueDate = LocalDate.of(2021, 12, 31),
                fineData = null,
                interestData = InterestData(
                    type = InterestType.VALUE,
                    value = BigDecimal.valueOf(10),
                    date = LocalDate.of(2022, 1, 1),
                ),
                discountData = null,
                abatement = null,
                processingDate = LocalDate.of(2022, 1, 5),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe 10000
                fine shouldBe 0
                interest shouldBe 5000
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 15000
                validUntil shouldBe LocalDate.of(2022, 1, 5)
            }
        }

        @Test
        fun `deve adicionar percentual mensal de juros divido por 30 dias por cada dia passado da data`() {
            val calculatedAmount = calculator.calculate(
                amount = 27484,
                dueDate = LocalDate.of(2022, 9, 6),
                fineData = null,
                interestData = InterestData(
                    type = InterestType.PERCENT_BY_MONTH,
                    value = BigDecimal.valueOf(0.33),
                    date = LocalDate.of(2022, 9, 7),
                ),
                discountData = null,
                abatement = null,
                processingDate = LocalDate.of(2022, 9, 20),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe 27484
                fine shouldBe 0
                interest shouldBe 42
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 27526
                validUntil shouldBe LocalDate.of(2022, 9, 20)
            }
        }

        @Test
        fun `deve adicionar percentual mensal de juros divido por 30 dias por cada dia passado da data (2)`() {
            val calculatedAmount = calculator.calculate(
                amount = 38857,
                dueDate = LocalDate.of(2022, 9, 6),
                fineData = null,
                interestData = InterestData(
                    type = InterestType.PERCENT_BY_MONTH,
                    value = BigDecimal.valueOf(8.11),
                    date = LocalDate.of(2022, 9, 21),
                ),
                discountData = DiscountData(type = DiscountType.FREE),
                abatement = null,
                processingDate = LocalDate.of(2022, 9, 22),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe 38857
                fine shouldBe 0
                interest shouldBe 210
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 39067
                validUntil shouldBe LocalDate.of(2022, 9, 22)
            }
        }

        // https://www.ibijus.com/blog/994-stj-decide-que-juros-de-mora-podem-incidir-em-sabados-domingos-ou-feriados
        @Test
        fun `deve adicionar percentual mensal de juros divido por 30 dias por cada dia passado da data contando a partir da data de juros caso tanto a data de vencimento quanto a data de juros caiam em feriado ou final de semana`() {
            val calculatedAmount = calculator.calculate(
                amount = 12990,
                dueDate = LocalDate.of(2023, 11, 25),
                fineData = FineData(
                    type = FineType.PERCENT,
                    value = BigDecimal.valueOf(2),
                    date = LocalDate.of(2023, 11, 26),
                ),
                interestData = InterestData(
                    type = InterestType.PERCENT_BY_MONTH,
                    value = BigDecimal.valueOf(9.9),
                    date = LocalDate.of(2023, 11, 26),
                ),
                discountData = null,
                abatement = null,
                processingDate = LocalDate.of(2023, 11, 28),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe 12990
                fine shouldBe 259
                interest shouldBe 128
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 13377
                validUntil shouldBe LocalDate.of(2023, 11, 28)
            }
        }

        @Test
        fun `deve adicionar percentual mensal de juros divido por 30 dias por cada dia passado da data contando a partir da propria data de juros quando a data de vencimento eh dia util`() {
            val calculatedAmount = calculator.calculate(
                amount = 9999,
                dueDate = LocalDate.of(2023, 9, 15),
                fineData = FineData(
                    type = FineType.PERCENT,
                    value = BigDecimal.valueOf(2),
                    date = LocalDate.of(2023, 9, 16),
                ),
                interestData = InterestData(
                    type = InterestType.PERCENT_BY_MONTH,
                    value = BigDecimal.valueOf(1),
                    date = LocalDate.of(2023, 9, 16),
                ),
                discountData = DiscountData(
                    type = DiscountType.FREE,
                    value1 = BigDecimal.valueOf(0),
                    date1 = null,
                    value2 = BigDecimal.valueOf(0),
                    date2 = null,
                    value3 = BigDecimal.valueOf(0),
                    date3 = null,
                ),
                abatement = 0.0,
                processingDate = LocalDate.of(2023, 9, 18),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe 9999
                fine shouldBe 199
                interest shouldBe 9
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 10207
                validUntil shouldBe LocalDate.of(2023, 9, 18)
            }
        }

        @Test
        fun `deve adicionar percentual mensal de juros divido por 30 dias por cada dia passado da data contando a partir da data de juros caso caia em feriado ou final de semana e descontando o valor abatido do calculo`() {
            val calculatedAmount = calculator.calculate(
                amount = 27484,
                dueDate = LocalDate.of(2022, 1, 1),
                fineData = null,
                interestData = InterestData(
                    type = InterestType.PERCENT_BY_MONTH,
                    value = BigDecimal.valueOf(0.33),
                    date = LocalDate.of(2022, 1, 1),
                ),
                discountData = null,
                abatement = 10.00,
                processingDate = LocalDate.of(2022, 1, 16),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe 27484
                fine shouldBe 0
                interest shouldBe 49
                discount shouldBe 0
                abatement shouldBe 1000
                totalAmount shouldBe 26533
                validUntil shouldBe LocalDate.of(2022, 1, 17)
            }
        }
    }

    @Nested
    inner class Fine {
        /**
         * - quando nao tem desconto e com multa:
         *      - isento -> amountTotal = amount
         *      - multa com valor fixo -> amountTotal = amount + multa
         *      - multa com percentual -> amountTotal = amount * (percentual/100)
         */

        @ParameterizedTest
        @ValueSource(strings = ["2022-01-01", "2022-01-02", "2022-01-03", "2022-01-04"])
        fun `nao deve aplicar multa caso data corrente for antes da data de multa`(processingDate: String) {
            val calculatedAmount = calculator.calculate(
                amount = 100,
                dueDate = LocalDate.of(2022, 1, 3),
                fineData = FineData(
                    type = FineType.VALUE,
                    value = BigDecimal("10"),
                    date = LocalDate.of(2022, 1, 5),
                ),
                interestData = null,
                discountData = null,
                abatement = null,
                processingDate = LocalDate.parse(processingDate, DateTimeFormatter.ISO_DATE),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe 100
                fine shouldBe 0
                interest shouldBe 0
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 100
                validUntil shouldBe LocalDate.of(2022, 1, 4)
            }
        }

        @Test
        fun `nao deve aplicar multa caso data de multa for antes da data de vencimento efetiva`() {
            val calculatedAmount = calculator.calculate(
                amount = 100,
                dueDate = LocalDate.of(2022, 11, 5),
                fineData = FineData(
                    type = FineType.VALUE,
                    value = BigDecimal("10"),
                    date = LocalDate.of(2022, 11, 6),
                ),
                interestData = null,
                discountData = null,
                abatement = null,
                processingDate = LocalDate.of(2022, 11, 6),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe 100
                fine shouldBe 0
                interest shouldBe 0
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 100
                validUntil shouldBe LocalDate.of(2022, 11, 7)
            }
        }

        @Test
        fun `nao deve calcular multa caso for isento`() {
            val calculatedAmount = calculator.calculate(
                amount = 100,
                dueDate = LocalDate.of(2022, 1, 3),
                fineData = FineData(
                    type = FineType.FREE,
                    value = null,
                    date = null,
                ),
                interestData = null,
                discountData = null,
                abatement = null,
                processingDate = LocalDate.of(2022, 1, 6),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe 100
                fine shouldBe 0
                interest shouldBe 0
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 100
                validUntil shouldBe null
            }
        }

        @ParameterizedTest
        @ValueSource(strings = ["2022-01-06", "2022-01-07", "2022-01-08", "2022-01-05", "2022-01-15"])
        fun `deve adicionar valor absoluto da multa ao valor total caso seja valor fixo`(processingDate: String) {
            val calculatedAmount = calculator.calculate(
                amount = 100,
                dueDate = LocalDate.of(2022, 1, 3),
                fineData = FineData(
                    type = FineType.VALUE,
                    value = BigDecimal("0.5"),
                    date = LocalDate.of(2022, 1, 5),
                ),
                interestData = null,
                discountData = null,
                abatement = null,
                processingDate = LocalDate.parse(processingDate, DateTimeFormatter.ISO_DATE),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe 100
                fine shouldBe 50
                interest shouldBe 0
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 150
                validUntil shouldBe null
            }
        }

        @ParameterizedTest
        @ValueSource(strings = ["2022-01-06", "2022-01-07", "2022-01-08", "2022-01-05", "2022-01-15"])
        fun `deve adicionar valor percentual da multa ao valor total caso seja valor percentual`(processingDate: String) {
            val calculatedAmount = calculator.calculate(
                amount = 10000,
                dueDate = LocalDate.of(2022, 1, 3),
                fineData = FineData(
                    type = FineType.PERCENT,
                    value = BigDecimal("10"),
                    date = LocalDate.of(2022, 1, 5),
                ),
                interestData = null,
                discountData = null,
                abatement = null,
                processingDate = LocalDate.parse(processingDate, DateTimeFormatter.ISO_DATE),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe 10000
                fine shouldBe 1000
                interest shouldBe 0
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 11000
                validUntil shouldBe null
            }
        }
    }

    @Nested
    inner class Discount {
        /**
         * - quando for exatamente ou anterior a data
         *      - quando for isento, amountTotal = amount
         *      - quando for valor fixo até uma data = amountTotal = amount - desconto
         * - quando a data for posterior a data do desconto
         *      - nao deve aplicar desconto
         *
         *  value2 e value3 nao sao preenchidos durante a consulta no Arbi, somente via arquivo
         */

        // FIXME verificar a lista não suportada logo no inicio do método calculateDiscount()
        @ParameterizedTest
        @EnumSource(
            value = DiscountType::class,
            mode = EnumSource.Mode.EXCLUDE,
            names = ["FREE", "FIXED_UNTIL_DATE", "PERCENT_UNTIL_DATE", "VALUE_BY_DAY"],
        )
        fun `deve retornar erro caso tipo de desconto nao for suportado`(type: DiscountType) {
            withHolidays(LocalDate.of(2022, 3, 1)) {
                calculator.calculate(
                    amount = 10000,
                    dueDate = LocalDate.of(2022, 1, 3),
                    fineData = null,
                    interestData = null,
                    discountData = DiscountData(
                        type = type,
                        value1 = BigDecimal("10"),
                        date1 = LocalDate.of(2022, 3, 1),
                    ),
                    abatement = null,
                    processingDate = LocalDate.parse("2022-03-02"),
                ).fold(
                    {
                        Assertions.assertTrue(it is NotSupported)
                    },
                    { fail("should not work") },
                )
            }
        }

        @Test
        fun `nao deve aplicar desconto quando boleto for isento`() {
            val calculatedAmount = calculator.calculate(
                amount = 100,
                dueDate = LocalDate.of(2022, 1, 3),
                fineData = null,
                interestData = null,
                discountData = DiscountData(
                    type = DiscountType.FREE,
                    value1 = null,
                    date1 = null,
                ),
                abatement = null,
                processingDate = LocalDate.of(2022, 1, 3),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe 100
                fine shouldBe 0
                interest shouldBe 0
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 100
                validUntil shouldBe null
            }
        }

        @ParameterizedTest
        @CsvSource(
            value = [
                "2022-01-03, 2022-01-03, 200",
                "2022-01-04, 2022-01-05, 100",
                "2022-01-05, 2022-01-05, 100",
                "2022-01-06,       null,   0",
            ],
            nullValues = ["null"],
        )
        fun `deve aplicar o desconto correto quando houver dois grupos de desconto`(processingDate: String, expectedValidUntil: String?, expectedDiscount: Long) {
            val amount = 1000L
            val calculatedAmount = calculator.calculate(
                amount = amount,
                dueDate = LocalDate.of(2022, 1, 6),
                fineData = null,
                interestData = null,
                discountData = DiscountData(
                    type = DiscountType.FIXED_UNTIL_DATE,
                    value1 = BigDecimal("1"),
                    date1 = LocalDate.of(2022, 1, 5),
                    value2 = BigDecimal("2"),
                    date2 = LocalDate.of(2022, 1, 3),
                ),
                abatement = null,
                processingDate = LocalDate.parse(processingDate, DateTimeFormatter.ISO_DATE),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe amount
                fine shouldBe 0
                interest shouldBe 0
                discount shouldBe expectedDiscount
                abatement shouldBe 0
                totalAmount shouldBe amount - expectedDiscount
                validUntil shouldBe expectedValidUntil?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) }
            }
        }

        @ParameterizedTest
        @CsvSource(
            value = [
                "2022-01-02, 2022-01-03, 400", // 1º desconto vigente, válido até o último dia de vigência dele
                "2022-01-03, 2022-01-03, 400", // 1º desconto vigente, válido até o último dia de vigência dele
                "2022-01-04, 2022-01-05, 200", // 2º desconto vigente, válido até o último dia de vigência dele
                "2022-01-05, 2022-01-05, 200", // 2º desconto vigente, válido até o último dia de vigência dele
                "2022-01-06, 2022-01-10, 100", // 3º desconto vigente, válido até o último dia de vigência dele
                "2022-01-07, 2022-01-10, 100", // 3º desconto vigente, válido até o último dia de vigência dele
                "2022-01-08, 2022-01-10, 100", // 3º desconto vigente, válido até o último dia de vigência dele
                "2022-01-09, 2022-01-10, 100", // 3º desconto vigente, válido até o último dia de vigência dele
                "2022-01-10, 2022-01-10, 100", // 3º desconto vigente, válido até o último dia de vigência dele
                "2022-01-11,       null,   0", // 3º desconto expirou, válido indefinidamente
            ],
            nullValues = ["null"],
        )
        fun `deve aplicar o desconto correto quando houver tres grupos de desconto`(processingDate: String, expectedValidUntil: String?, expectedDiscount: Long) {
            val amount = 1000L
            val calculatedAmount = calculator.calculate(
                amount = amount,
                dueDate = LocalDate.of(2022, 1, 8), // sábado, vencimento efetivo -> 2022-01-10
                fineData = null,
                interestData = null,
                discountData = DiscountData(
                    type = DiscountType.FIXED_UNTIL_DATE,
                    value1 = BigDecimal("2"),
                    date1 = LocalDate.of(2022, 1, 5),
                    value2 = BigDecimal(1),
                    date2 = null, // data não informada = dia do vencimento
                    value3 = BigDecimal(4),
                    date3 = LocalDate.of(2022, 1, 3),
                ),
                abatement = null,
                processingDate = LocalDate.parse(processingDate, DateTimeFormatter.ISO_DATE),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe amount
                fine shouldBe 0
                interest shouldBe 0
                discount shouldBe expectedDiscount
                abatement shouldBe 0
                totalAmount shouldBe amount - expectedDiscount
                validUntil shouldBe expectedValidUntil?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) }
            }
        }

        @ParameterizedTest
        @CsvSource(
            value = [
                "2022-01-04, 2022-01-07, 5",
                "2022-01-06, 2022-01-07, 5",
                "2022-01-07, 2022-01-07, 5",
                "2022-01-08,       null, 0",
            ],
            nullValues = ["null"],
        )
        fun `deve aplicar o desconto em porcentagem ate a data limite de desconto`(processingDate: String, expectedValidUntil: String?, expectedDiscount: Long) {
            val amount = 1000L
            val calculatedAmount = calculator.calculate(
                amount = amount,
                dueDate = LocalDate.of(2022, 1, 8),
                fineData = null,
                interestData = null,
                discountData = DiscountData(
                    type = DiscountType.PERCENT_UNTIL_DATE,
                    value1 = BigDecimal("0.5"),
                    date1 = LocalDate.of(2022, 1, 7),
                    value2 = null,
                    date2 = null,
                    value3 = null,
                    date3 = null,
                ),
                abatement = null,
                processingDate = LocalDate.parse(processingDate, DateTimeFormatter.ISO_DATE),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe amount
                fine shouldBe 0
                interest shouldBe 0
                discount shouldBe expectedDiscount
                abatement shouldBe 0
                totalAmount shouldBe amount - expectedDiscount
                validUntil shouldBe expectedValidUntil?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) }
            }
        }

        @ParameterizedTest
        @CsvSource(
            value = [
                "2022-09-28, 2022-09-28, 21650",
                "2023-10-04, 2023-10-04,  3100",
                "2023-10-05, 2023-10-05,  2440",
                "2023-11-03, 2023-11-03,  1280",
                "2023-11-05, 2023-11-06,   870", // dia 05/11 é um domingo
                "2023-12-04, 2023-12-04,    30",
                "2023-12-05,       null,     0",
            ],
            nullValues = ["null"],
        )
        fun `deve aplicar o desconto por atencipacao por dia corrido`(processingDate: String, expectedValidUntil: String?, expectedDiscount: Long) {
            val amount = 24207L
            val calculatedAmount = calculator.calculate(
                amount = amount,
                dueDate = LocalDate.of(2023, 12, 5),
                fineData = null,
                interestData = null,
                discountData = DiscountData(
                    type = DiscountType.VALUE_BY_DAY,
                    value1 = BigDecimal("0.50"),
                    date1 = LocalDate.of(2023, 10, 4),
                    value2 = BigDecimal("0.40"),
                    date2 = LocalDate.of(2023, 11, 3),
                    value3 = BigDecimal("0.30"),
                    date3 = LocalDate.of(2023, 12, 4),
                ),
                abatement = null,
                processingDate = LocalDate.parse(processingDate, DateTimeFormatter.ISO_DATE),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe amount
                fine shouldBe 0
                interest shouldBe 0
                discount shouldBe expectedDiscount
                abatement shouldBe 0
                totalAmount shouldBe amount - expectedDiscount
                validUntil shouldBe expectedValidUntil?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) }
            }
        }

        @ParameterizedTest
        @CsvSource(
            value = [
                "2021-02-27, 2022-03-02, 1000", // sábado
                "2022-03-01, 2022-03-02, 1000", // feriado de carnaval
                "2022-03-02, 2022-03-02, 1000", // dia útil
                "2022-03-03,       null,    0",
            ],
            nullValues = ["null"],
        )
        fun `deve adicionar o desconto quando a data for igual ou anterior a de processamento ou extender ate o proximo dia util caso a data limite caia em um final de semana ou feriado`(
            processingData: String,
            expectedValidUntil: String?,
            expectedDiscount: Long,
        ) {
            withHolidays(LocalDate.of(2022, 3, 1)) {
                val amount = 10000L
                val calculatedAmount = calculator.calculate(
                    amount = amount,
                    dueDate = LocalDate.of(2022, 4, 3),
                    fineData = null,
                    interestData = null,
                    discountData = DiscountData(
                        type = DiscountType.FIXED_UNTIL_DATE,
                        value1 = BigDecimal.valueOf(10),
                        date1 = LocalDate.of(2022, 3, 1),
                    ),
                    abatement = null,
                    processingDate = LocalDate.parse(processingData),
                ).getOrElse { fail("should not fail ${it.message}") }

                with(calculatedAmount) {
                    originalAmount shouldBe amount
                    fine shouldBe 0
                    interest shouldBe 0
                    discount shouldBe expectedDiscount
                    abatement shouldBe 0
                    totalAmount shouldBe amount - expectedDiscount
                    validUntil shouldBe expectedValidUntil?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) }
                }
            }
        }

        @Test
        fun `nao deve aplicar desconto caso a data do desconto tenha expirado`() {
            val calculatedAmount = calculator.calculate(
                amount = 100,
                dueDate = LocalDate.of(2022, 1, 3),
                fineData = null,
                interestData = null,
                discountData = DiscountData(
                    type = DiscountType.FIXED_UNTIL_DATE,
                    value1 = BigDecimal.valueOf(10),
                    date1 = LocalDate.of(2022, 1, 3),
                ),
                abatement = null,
                processingDate = LocalDate.of(2022, 1, 4),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe 100
                fine shouldBe 0
                interest shouldBe 0
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 100
                validUntil shouldBe null
            }
        }

        /**
         *  Descontos deverão ser postergados para o próximo dia útil assim como a data limite para  pagamento.
         *  Para casos de descontos por dia de antecipação, o efeito prático da postergação será nulo
         *  pois a quantidade de dias de antecipação será igual à zero. Neste caso, como o desconto é aplicado
         *  fazendo-se a conta de data de desconto – data de pagamento o resultado será sempre zero conforme  abaixo:
         *
         *      - Data de desconto: 08/09/18 (sábado)
         *      - Data de vencimento: 09/09/18 (domingo)
         *      - Pagamento postergado para 10/09/18 (segunda feira)
         *      – Data de desconto postergada 10/09/18
         *      - Data de vencimento postergada 10/09/18 – Data de pagamento 10/09/18 = 0
         *      - Desconto = R$ 0,00
         */
        @ParameterizedTest
        @CsvSource(
            value = [
                "2023-10-20, 2023-10-20, 4000", // sexta
                "2023-10-21, 2023-10-23, 1000", // sábado
                "2023-10-22, 2023-10-23, 1000", // domingo
                "2023-10-23, 2023-10-23, 1000", // segunda
                "2023-10-24,       null,    0", // terça
            ],
            nullValues = ["null"],
        )
        fun `deve postergar a data limite de desconto para o próximo dia útil e calcular a qtde de dias como o intervalo entre a data efetiva de processamento e data de vencimento`(
            processingData: String,
            expectedValidUntil: String?,
            expectedDiscount: Long,
        ) {
            val amount = 10000L
            val calculatedAmount = calculator.calculate(
                amount = amount,
                dueDate = LocalDate.of(2023, 10, 24),
                fineData = null,
                interestData = null,
                discountData = DiscountData(
                    type = DiscountType.VALUE_BY_DAY,
                    value1 = BigDecimal.valueOf(10),
                    date1 = LocalDate.of(2023, 10, 21),
                ),
                abatement = null,
                processingDate = LocalDate.parse(processingData),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe amount
                fine shouldBe 0
                interest shouldBe 0
                discount shouldBe expectedDiscount
                abatement shouldBe 0
                totalAmount shouldBe amount - expectedDiscount
                validUntil shouldBe expectedValidUntil?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) }
            }
        }

        @Test
        fun `deve realizar a fórmula de calculo correta 6`() {
            val calculatedAmount =
                calculator.calculate(
                    amount = 28989,
                    dueDate = LocalDate.of(2025, 9, 6),
                    fineData = FineData(
                        type = FineType.PERCENT,
                        value = BigDecimal("2"),
                        date = LocalDate.of(2025, 9, 7),
                    ),
                    interestData = InterestData(
                        type = InterestType.PERCENT_BY_MONTH,
                        value = BigDecimal("8.25"),
                        date = LocalDate.of(2025, 9, 7),
                    ),
                    discountData = DiscountData(
                        type = DiscountType.VALUE_BY_DAY,
                        value1 = BigDecimal("0.49"),
                    ),
                    abatement = null,
                    processingDate = LocalDate.of(2025, 2, 18),
                ).getOrElse {
                    fail("should not fail ${it.message}")
                }

            with(calculatedAmount) {
                originalAmount shouldBe 28989
                fine shouldBe 0
                interest shouldBe 0
                discount shouldBe 9800
                abatement shouldBe 0
                totalAmount shouldBe 19189
            }
        }
    }

    @Nested
    inner class Calculate {

        @ParameterizedTest
        @CsvSource(
            value = [
                "2022-09-01, 2022-09-01,   0,  0, 550",
                "2022-09-02, 2022-09-06,   0,  0,   0",
                "2022-09-06, 2022-09-06,   0,  0,   0",
                "2022-09-07, 2022-09-07, 550,  3,   0",
                "2022-09-09, 2022-09-09, 550,  9,   0",
                "2022-09-20, 2022-09-20, 550, 42,   0",
            ],
        )
        fun `deve calcular multa, juros e desconto quando houver todos os dados e estiver apto a fazer o calculo`(processingDate: String, expectedValidUntil: String, expectedFine: Long, expectedInterest: Long, expectedDiscount: Long) {
            val amount = 27484L
            val calculatedAmount = calculator.calculate(
                amount = amount,
                dueDate = LocalDate.of(2022, 9, 5),
                fineData = FineData(
                    type = FineType.VALUE,
                    value = BigDecimal("5.5"),
                    date = LocalDate.of(2022, 9, 7),
                ),
                interestData = InterestData(
                    type = InterestType.PERCENT_BY_MONTH,
                    value = BigDecimal("0.33"),
                    date = LocalDate.of(2022, 9, 7),
                ),
                discountData = DiscountData(
                    type = DiscountType.FIXED_UNTIL_DATE,
                    value1 = BigDecimal("5.5"),
                    date1 = LocalDate.of(2022, 9, 1),
                ),
                abatement = null,
                processingDate = LocalDate.parse(processingDate, DateTimeFormatter.ISO_DATE),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe amount
                fine shouldBe expectedFine
                interest shouldBe expectedInterest
                discount shouldBe expectedDiscount
                abatement shouldBe 0
                totalAmount shouldBe amount + expectedFine + expectedInterest - expectedDiscount
                validUntil shouldBe LocalDate.parse(expectedValidUntil, DateTimeFormatter.ISO_DATE)
            }
        }

        @Test
        fun `deve calcular multa, juros e desconto considerando o proximo dia util quando a data de processamento nao for um dia util`() {
            val calculatedAmount = withHolidays(LocalDate.of(2023, 10, 12)) {
                calculator.calculate(
                    amount = 27484,
                    dueDate = LocalDate.of(2023, 10, 10),
                    fineData = FineData(
                        type = FineType.VALUE,
                        value = BigDecimal("5.5"),
                        date = LocalDate.of(2023, 10, 10),
                    ),
                    interestData = InterestData(
                        type = InterestType.PERCENT_BY_MONTH,
                        value = BigDecimal("0.33"),
                        date = LocalDate.of(2023, 10, 10),
                    ),
                    discountData = null,
                    abatement = null,
                    processingDate = LocalDate.of(2023, 10, 12),
                ).getOrElse {
                    fail("should not fail ${it.message}")
                }
            }

            with(calculatedAmount) {
                originalAmount shouldBe 27484
                fine shouldBe 550
                interest shouldBe 12
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 28046
                validUntil shouldBe LocalDate.of(2023, 10, 13)
            }
        }

        @Test
        fun `deve realizar a fórmula de calculo correta`() {
            val calculatedAmount =
                calculator.calculate(
                    amount = 9200,
                    dueDate = LocalDate.of(2025, 2, 1),
                    fineData = FineData(
                        type = FineType.VALUE,
                        value = BigDecimal("3.99"),
                        date = LocalDate.of(2025, 2, 4),
                    ),
                    interestData = InterestData(
                        type = InterestType.PERCENT_BY_MONTH,
                        value = BigDecimal("1.99"),
                        date = LocalDate.of(2025, 2, 2),
                    ),
                    discountData = null,
                    abatement = null,
                    processingDate = LocalDate.of(2025, 2, 11),
                ).getOrElse {
                    fail("should not fail ${it.message}")
                }

            with(calculatedAmount) {
                originalAmount shouldBe 9200
                fine shouldBe 399
                interest shouldBe 60
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 9659
            }
        }

        @Test
        fun `deve realizar a fórmula de calculo correta 2`() {
            val calculatedAmount =
                calculator.calculate(
                    amount = 46945,
                    dueDate = LocalDate.of(2024, 11, 10),
                    fineData = FineData(
                        type = FineType.PERCENT,
                        value = BigDecimal("2"),
                        date = LocalDate.of(2024, 11, 11),
                    ),
                    interestData = InterestData(
                        type = InterestType.PERCENT_BY_MONTH,
                        value = BigDecimal("0.99"),
                        date = LocalDate.of(2024, 11, 11),
                    ),
                    discountData = null,
                    abatement = null,
                    processingDate = LocalDate.of(2025, 2, 12),
                ).getOrElse {
                    fail("should not fail ${it.message}")
                }

            with(calculatedAmount) {
                originalAmount shouldBe 46945
                fine shouldBe 938
                interest shouldBe 1456
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 49339
            }
        }

        @Test
        fun `deve realizar a fórmula de calculo correta 3`() {
            val calculatedAmount =
                calculator.calculate(
                    amount = 399200,
                    dueDate = LocalDate.of(2025, 2, 1),
                    fineData = FineData(
                        type = FineType.VALUE,
                        value = BigDecimal("79.84"),
                        date = LocalDate.of(2025, 2, 2),
                    ),
                    interestData = InterestData(
                        type = InterestType.PERCENT_BY_MONTH,
                        value = BigDecimal("0.95"),
                        date = LocalDate.of(2025, 2, 2),
                    ),
                    discountData = null,
                    abatement = 199.6,
                    processingDate = LocalDate.of(2025, 2, 11),
                ).getOrElse {
                    fail("should not fail ${it.message}")
                }

            with(calculatedAmount) {
                originalAmount shouldBe 399200
                fine shouldBe 7984
                interest shouldBe 1198
                discount shouldBe 0
                abatement shouldBe 19960
                totalAmount shouldBe 388422
            }
        }

        @Test
        fun `deve realizar a fórmula de calculo correta 4`() {
            val calculatedAmount =
                calculator.calculate(
                    amount = 1500,
                    dueDate = LocalDate.of(2025, 2, 11),
                    fineData = FineData(
                        type = FineType.VALUE,
                        value = BigDecimal("5"),
                        date = LocalDate.of(2025, 2, 12),
                    ),
                    interestData = InterestData(
                        type = InterestType.PERCENT_BY_MONTH,
                        value = BigDecimal("2"),
                        date = LocalDate.of(2025, 2, 12),
                    ),
                    discountData = null,
                    abatement = null,
                    processingDate = LocalDate.of(2025, 2, 12),
                ).getOrElse {
                    fail("should not fail ${it.message}")
                }

            with(calculatedAmount) {
                originalAmount shouldBe 1500
                fine shouldBe 500
                interest shouldBe 0
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 2000
            }
        }

        @Test
        fun `deve realizar a fórmula de calculo correta 5`() {
            val calculatedAmount =
                calculator.calculate(
                    amount = 8376,
                    dueDate = LocalDate.of(2024, 11, 14),
                    fineData = FineData(
                        type = FineType.VALUE,
                        value = BigDecimal("1.68"),
                        date = LocalDate.of(2024, 11, 15),
                    ),
                    interestData = InterestData(
                        type = InterestType.VALUE,
                        value = BigDecimal("0.03"),
                        date = LocalDate.of(2024, 11, 15),
                    ),
                    discountData = null,
                    abatement = null,
                    processingDate = LocalDate.of(2025, 2, 12),
                ).getOrElse {
                    fail("should not fail ${it.message}")
                }

            with(calculatedAmount) {
                originalAmount shouldBe 8376
                fine shouldBe 168
                interest shouldBe 270
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 8814
            }
        }

        @Test
        fun `deve realizar a fórmula de calculo correta 6`() {
            val calculatedAmount =
                calculator.calculate(
                    amount = 90216,
                    dueDate = LocalDate.of(2025, 2, 10),
                    fineData = FineData(
                        type = FineType.PERCENT,
                        value = BigDecimal("2"),
                        date = LocalDate.of(2025, 2, 11),
                    ),
                    interestData = InterestData(
                        type = InterestType.PERCENT_BY_MONTH,
                        value = BigDecimal("0.99475"),
                        date = LocalDate.of(2025, 2, 11),
                    ),
                    discountData = null,
                    abatement = null,
                    processingDate = LocalDate.of(2025, 2, 20),
                ).getOrElse {
                    fail("should not fail ${it.message}")
                }

            with(calculatedAmount) {
                originalAmount shouldBe 90216
                fine shouldBe 1804
                interest shouldBe 298
                discount shouldBe 0
                abatement shouldBe 0
                totalAmount shouldBe 92318
            }
        }

        @ParameterizedTest
        @CsvSource(
            value = [
                "2022-09-01, 2022-09-01,   0,  0, 550",
                "2022-09-02, 2022-09-06,   0,  0,   0",
                "2022-09-06, 2022-09-06,   0,  0,   0",
                "2022-09-07, 2022-09-07, 550,  2,   0",
                "2022-09-09, 2022-09-09, 550,  8,   0",
                "2022-09-20, 2022-09-20, 550, 40,   0",
            ],
        )
        fun `deve calcular multa, juros e desconto quando todas as modalidades de calculo forem suportadas descontando o valor abatido`(processingDate: String, expectedValidUntil: String, expectedFine: Long, expectedInterest: Long, expectedDiscount: Long) {
            val amount = 27484L
            val calculatedAmount = calculator.calculate(
                amount = amount,
                dueDate = LocalDate.of(2022, 9, 5),
                fineData = FineData(
                    type = FineType.VALUE,
                    value = BigDecimal("5.5"),
                    date = LocalDate.of(2022, 9, 7),
                ),
                interestData = InterestData(
                    type = InterestType.PERCENT_BY_MONTH,
                    value = BigDecimal("0.33"),
                    date = LocalDate.of(2022, 9, 7),
                ),
                discountData = DiscountData(
                    type = DiscountType.FIXED_UNTIL_DATE,
                    value1 = BigDecimal("5.5"),
                    date1 = LocalDate.of(2022, 9, 1),
                ),
                abatement = 10.00,
                processingDate = LocalDate.parse(processingDate, DateTimeFormatter.ISO_DATE),
            ).getOrElse { fail("should not fail ${it.message}") }

            with(calculatedAmount) {
                originalAmount shouldBe amount
                fine shouldBe expectedFine
                interest shouldBe expectedInterest
                discount shouldBe expectedDiscount
                // abatement shouldBe 1000
                // totalAmount shouldBe amount + expectedFine + expectedInterest - expectedDiscount
                validUntil shouldBe LocalDate.parse(expectedValidUntil, DateTimeFormatter.ISO_DATE)
            }
        }
    }

    @Nested
    inner class ValidUntil {
        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.bill.BillAmountCalculatorTest#discountFixedDate")
        fun `deve calcular a data de validade do processamento de acordo com a data de desconto ate antes do vencimento`(
            hoje: String,
            proximaData: String,
            type: DiscountType,
        ) {
            val dueDate = LocalDate.of(2023, 1, 12)
            val discount = DiscountData(
                type = type,
                value1 = BigDecimal.ONE,
                date1 = LocalDate.of(2023, 1, 2),
                value2 = BigDecimal.ONE,
                date2 = LocalDate.of(2023, 1, 3),
                value3 = BigDecimal.ONE,
                date3 = LocalDate.of(2023, 1, 11),
            )

            val now = LocalDate.parse(hoje, DateTimeFormatter.ISO_DATE)
            val nextProcessingDate = getValidUntil(discount, dueDate, now, null, null, true)

            nextProcessingDate shouldBe LocalDate.parse(proximaData, DateTimeFormatter.ISO_DATE)
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.bill.BillAmountCalculatorTest#discountAndTodayBeforeDueDate")
        fun `deve calcular a data de validade do processamento quando nao passou da data de vencimento, mas não tem multa ou juros`(
            hoje: String,
            type: DiscountType,
        ) {
            val dueDate = LocalDate.of(2023, 1, 12)
            val discount = DiscountData(
                type = type,
                value1 = null,
                date1 = null,
                value2 = null,
                date2 = null,
                value3 = null,
                date3 = null,
            )

            val now = LocalDate.parse(hoje, DateTimeFormatter.ISO_DATE)
            val nextProcessingDate = getValidUntil(discount, dueDate, now, null, null, true)

            nextProcessingDate shouldBe null
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.bill.BillAmountCalculatorTest#discountAndTodayAfterDueDate")
        fun `deve calcular e nao retornar a data de validade do processamento da data do vencimento em diante`(
            hoje: String,
            type: DiscountType,
        ) {
            val dueDate = LocalDate.of(2023, 1, 12)
            val discount = DiscountData(
                type = type,
                value1 = null,
                date1 = null,
                value2 = null,
                date2 = null,
                value3 = null,
                date3 = null,
            )

            val now = LocalDate.parse(hoje, DateTimeFormatter.ISO_DATE)
            val nextProcessingDate = getValidUntil(discount, dueDate, now, null, null, true)

            nextProcessingDate shouldBe null
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.bill.BillAmountCalculatorTest#discountByDay")
        fun `deve recalcular o desconto todos os dias, ate 1 dia antes do vencimento, quando o tipo de desconto nao eh fixo`(
            hoje: String,
            type: DiscountType?,
        ) {
            val dueDate = LocalDate.of(2023, 1, 12)
            val discount = DiscountData(
                type = type,
                value1 = BigDecimal.ONE,
                date1 = LocalDate.of(2023, 1, 2),
                value2 = BigDecimal.ONE,
                date2 = LocalDate.of(2023, 1, 3),
                value3 = BigDecimal.ONE,
                date3 = LocalDate.of(2023, 1, 11),
            )

            val now = LocalDate.parse(hoje, DateTimeFormatter.ISO_DATE)
            val nextProcessingDate = getValidUntil(discount, dueDate, now, null, null, true)

            nextProcessingDate shouldBe now
        }

        @ParameterizedTest
        @ValueSource(longs = [-1, 0, 1])
        fun `deve recalcular todos os dias quando a conta nao foi adicionada via DDA`(delta: Long) {
            val dueDate = LocalDate.now()

            val processingDate = dueDate.plusDays(delta)
            val nextProcessingDate = getValidUntil(null, dueDate, processingDate, null, null, false)

            nextProcessingDate shouldBe processingDate
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.bill.BillAmountCalculatorTest#discountFreeAndNull")
        fun `deve calcular e nao retornar a data de validade do processamento quando nao tiver desconto`(
            hoje: String,
            type: DiscountType?,
        ) {
            val dueDate = LocalDate.of(2023, 1, 12)
            val discount = DiscountData(
                type = type,
                value1 = null,
                date1 = null,
                value2 = null,
                date2 = null,
                value3 = null,
                date3 = null,
            )

            val now = LocalDate.parse(hoje, DateTimeFormatter.ISO_DATE)
            val nextProcessingDate = getValidUntil(discount, dueDate, now, null, null, true)

            nextProcessingDate shouldBe null
        }

        @ParameterizedTest
        @CsvSource(
            nullValues = ["null"],
            value = [
                "2023-01-03,2023-01-05", // desconto vigente, valido ate o ultimo dia de vigencia
                "2023-01-04,2023-01-05", // desconto vigente, valido ate o ultimo dia de vigencia
                "2023-01-05,2023-01-05", // desconto vigente, valido ate o ultimo dia de vigencia
                "2023-01-06,2023-01-14", // desconto expirou, valido ate a multa
                "2023-01-07,2023-01-14", // desconto expirou, valido ate a multa
                "2023-01-08,2023-01-14", // desconto expirou, valido ate a multa
                "2023-01-09,2023-01-14", // desconto expirou, valido ate a multa
                "2023-01-10,2023-01-14", // desconto expirou, valido ate a multa
                "2023-01-11,2023-01-14", // desconto expirou, valido ate a multa
                "2023-01-12,2023-01-14", // desconto expirou, valido ate a multa
                "2023-01-13,2023-01-14", // desconto expirou, valido ate a multa
                "2023-01-14,2023-01-14", // desconto expirou, valido ate a multa
                "2023-01-15,null", // multa aplicada  , valido indefinidamente
            ],
        )
        fun `deve calcular a data de validade considerando desconto e multa`(processingDate: String, expectedValidUntil: String?) {
            val discount = DiscountData(
                type = DiscountType.FIXED_UNTIL_DATE,
                value1 = BigDecimal.ONE,
                date1 = LocalDate.of(2023, 1, 5),
                value2 = null,
                date2 = null,
                value3 = null,
                date3 = null,
            )
            val dueDate = LocalDate.of(2023, 1, 12)
            val fine = FineData(
                type = FineType.VALUE,
                value = null,
                date = LocalDate.of(2023, 1, 15),
            )

            val validUntil = getValidUntil(
                discountData = discount,
                dueDate = dueDate,
                actualProcessingDate = LocalDate.parse(processingDate, DateTimeFormatter.ISO_DATE),
                fineData = fine,
                interestData = null,
                addedByDDA = true,
            )

            validUntil shouldBe expectedValidUntil?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) }
        }

        @ParameterizedTest
        @CsvSource(
            nullValues = ["null"],
            value = [
                "2023-01-03,2023-01-05", // desconto vigente, valido ate o ultimo dia de vigencia
                "2023-01-04,2023-01-05", // desconto vigente, valido ate o ultimo dia de vigencia
                "2023-01-05,2023-01-05", // desconto vigente, valido ate o ultimo dia de vigencia
                "2023-01-06,2023-01-14", // desconto expirou, valido ate o início dos juros
                "2023-01-14,2023-01-14", // desconto expirou, valido ate o início dos juros
                "2023-01-15,2023-01-15", // desconto expirou, valido diário enquanto tiver juros para calcular
                "2023-01-16,2023-01-16", // desconto expirou, valido diário enquanto tiver juros para calcular
                "2023-02-20,2023-02-20", // desconto expirou, valido diário enquanto tiver juros para calcular
            ],
        )
        fun `deve calcular a data de validade considerando desconto e juros`(processingDate: String, expectedValidUntil: String?) {
            val discount = DiscountData(
                type = DiscountType.FIXED_UNTIL_DATE,
                value1 = BigDecimal.ONE,
                date1 = LocalDate.of(2023, 1, 5),
                value2 = null,
                date2 = null,
                value3 = null,
                date3 = null,
            )
            val dueDate = LocalDate.of(2023, 1, 12)
            val interest = InterestData(
                type = InterestType.VALUE,
                value = null,
                date = LocalDate.of(2023, 1, 15),
            )

            val validUntil = getValidUntil(
                discountData = discount,
                dueDate = dueDate,
                actualProcessingDate = LocalDate.parse(processingDate, DateTimeFormatter.ISO_DATE),
                fineData = null,
                interestData = interest,
                addedByDDA = true,
            )

            validUntil shouldBe expectedValidUntil?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) }
        }

        @ParameterizedTest
        @CsvSource(
            nullValues = ["null"],
            value = [
                "2023-01-03,2023-01-05", // desconto vigente, valido ate o ultimo dia de vigencia
                "2023-01-04,2023-01-05", // desconto vigente, valido ate o ultimo dia de vigencia
                "2023-01-05,2023-01-05", // desconto vigente, valido ate o ultimo dia de vigencia
                "2023-01-06,2023-01-13", // desconto expirou, valido ate o início da multa
                "2023-01-13,2023-01-13", // desconto expirou, boleto vencido e valido ate o início da multa
                "2023-01-14,2023-01-15", // desconto expirou, multa calculada e valido ate o início dos juros
                "2023-01-15,2023-01-15", // desconto expirou, multa calculada e valido ate o início dos juros
                "2023-01-16,2023-01-16", // desconto expirou, valido diário enquanto tiver juros para calcular
                "2023-02-20,2023-02-20", // desconto expirou, valido diário enquanto tiver juros para calcular
            ],
        )
        fun `deve calcular a data de validade considerando desconto, multa e juros`(processingDate: String, expectedValidUntil: String?) {
            val discount = DiscountData(
                type = DiscountType.FIXED_UNTIL_DATE,
                value1 = BigDecimal.ONE,
                date1 = LocalDate.of(2023, 1, 5),
                value2 = null,
                date2 = null,
                value3 = null,
                date3 = null,
            )
            val dueDate = LocalDate.of(2023, 1, 12)
            val fine = FineData(
                type = FineType.VALUE,
                value = null,
                date = LocalDate.of(2023, 1, 14),
            )
            val interest = InterestData(
                type = InterestType.VALUE,
                value = null,
                date = LocalDate.of(2023, 1, 16),
            )

            val validUntil = getValidUntil(
                discountData = discount,
                dueDate = dueDate,
                actualProcessingDate = LocalDate.parse(processingDate, DateTimeFormatter.ISO_DATE),
                fineData = fine,
                interestData = interest,
                addedByDDA = true,
            )

            validUntil shouldBe expectedValidUntil?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) }
        }

        @ParameterizedTest
        @CsvSource(
            nullValues = ["null"],
            value = [
                "2023-01-03,2023-01-13", // valido ate o início da multa
                "2023-01-06,2023-01-13", // valido ate o início da multa
                "2023-01-13,2023-01-13", // boleto vencido e valido ate o início da multa
                "2023-01-14,2023-01-15", // multa calculada e valido ate o início dos juros
                "2023-01-15,2023-01-15", // multa calculada e valido ate o início dos juros
                "2023-01-16,2023-01-16", // valido diário enquanto tiver juros para calcular
                "2023-02-20,2023-02-20", // valido diário enquanto tiver juros para calcular
            ],
        )
        fun `deve calcular a data de validade considerando multa e juros`(processingDate: String, expectedValidUntil: String?) {
            val dueDate = LocalDate.of(2023, 1, 12)
            val fine = FineData(
                type = FineType.VALUE,
                value = null,
                date = LocalDate.of(2023, 1, 14),
            )
            val interest = InterestData(
                type = InterestType.VALUE,
                value = null,
                date = LocalDate.of(2023, 1, 16),
            )

            val validUntil = getValidUntil(
                discountData = null,
                dueDate = dueDate,
                actualProcessingDate = LocalDate.parse(processingDate, DateTimeFormatter.ISO_DATE),
                fineData = fine,
                interestData = interest,
                addedByDDA = true,
            )

            validUntil shouldBe expectedValidUntil?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) }
        }
    }

    companion object {
        @JvmStatic
        fun discountByDay(): Stream<Arguments> {
            val types = listOf(
                DiscountType.PERCENT_BY_DAY,
                DiscountType.PERCENT_BY_WORKING_DAY,
                DiscountType.VALUE_BY_DAY,
                DiscountType.VALUE_BY_WORKING_DAY,
                DiscountType.UNKNOWN,
            ).flatMap {
                listOf(
                    Arguments.of("2022-12-26", it),
                    Arguments.of("2023-01-02", it),
                    Arguments.of("2023-01-03", it),
                    Arguments.of("2023-01-04", it),
                    Arguments.of("2023-01-11", it),
                )
            }
            return types.stream()
        }

        @JvmStatic
        fun discountFixedDate(): Stream<Arguments> {
            val types = listOf(
                DiscountType.FIXED_UNTIL_DATE,
                DiscountType.PERCENT_UNTIL_DATE,
            ).flatMap {
                listOf(
                    Arguments.of("2022-12-26", "2023-01-02", it),
                    Arguments.of("2023-01-02", "2023-01-02", it),
                    Arguments.of("2023-01-03", "2023-01-03", it),
                    Arguments.of("2023-01-04", "2023-01-11", it),
                    Arguments.of("2023-01-11", "2023-01-11", it),
                )
            }
            return types.stream()
        }

        @JvmStatic
        fun discountAndTodayAfterDueDate(): Stream<Arguments> {
            val types = DiscountType.entries.flatMap {
                listOf(
                    Arguments.of("2023-01-12", it),
                    Arguments.of("2023-01-13", it),
                )
            }
            return types.stream()
        }

        @JvmStatic
        fun discountAndTodayBeforeDueDate(): Stream<Arguments> {
            val types = listOf(
                DiscountType.FIXED_UNTIL_DATE,
                DiscountType.PERCENT_UNTIL_DATE,
            ).flatMap {
                listOf(
                    Arguments.of("2022-12-26", it),
                    Arguments.of("2023-01-11", it),
                    Arguments.of("2023-01-10", it),
                )
            }
            return types.stream()
        }

        @JvmStatic
        fun discountFreeAndNull(): Stream<Arguments> {
            val types = listOf(
                DiscountType.FREE,
                null,
            ).flatMap {
                listOf(
                    Arguments.of("2022-12-26", it),
                    Arguments.of("2023-01-11", it),
                    Arguments.of("2023-01-12", it),
                    Arguments.of("2023-01-13", it),
                )
            }
            return types.stream()
        }
    }
}