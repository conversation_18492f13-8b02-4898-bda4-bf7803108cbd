package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.DOCUMENT_3
import ai.friday.billpayment.integration.WalletFixture
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import org.junit.jupiter.api.Test

class BillUtilsKtTest {

    val accountId = AccountId(ACCOUNT_ID)
    val walletFixture = WalletFixture()

    @Test
    fun `boleto de cartão não é confiável`() {
        val activeBill = getActiveBill(
            accountId = accountId,
            fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO,
        )
        activeBill.isOwnTrustedBill(activeBill.payerDocument!!).shouldBeFalse()
    }

    @Test
    fun `boleto de deposito não é confiável`() {
        val activeBill = getActiveBill(
            accountId = accountId,
            fichaCompensacaoType = FichaCompensacaoType.DEPOSITO_E_APORTE,
        )
        activeBill.isOwnTrustedBill(activeBill.payerDocument!!).shouldBeFalse()
    }

    @Test
    fun `boleto do nubank não é confiável`() {
        val activeBill = getActiveBill(
            accountId = accountId,
            fichaCompensacaoType = FichaCompensacaoType.CH_CHEQUE,
            recipient = Recipient(name = "NU PAGAMENTOS S.A.", document = "**************"),
        )
        activeBill.isOwnTrustedBill(activeBill.payerDocument!!).shouldBeFalse()
    }

    @Test
    fun `se o boleto não está em nome dele, não é confiável`() {
        val activeBill = getActiveBill(accountId = accountId)
        activeBill.isOwnTrustedBill("*********").shouldBeFalse()
    }

    @Test
    fun `se o boleto está em nome dele, é confiável`() {
        val activeBill = getActiveBill(accountId = accountId)
        activeBill.isOwnTrustedBill(activeBill.payerDocument!!).shouldBeTrue()
    }

    @Test
    fun `se o recipient for um cpf, não é confiável`() {
        val activeBill = getActiveBill(
            accountId = accountId,
            recipient = Recipient(name = "any", document = DOCUMENT_3),
        )
        activeBill.isOwnTrustedBill(activeBill.payerDocument!!).shouldBeFalse()
    }

    @Test
    fun `Usuário antigo é liberado`() {
        memberOlderThanDate(walletFixture.founder, "2023-10-15").shouldBeTrue()
    }

    @Test
    fun `Usuário recente é bloqueado`() {
        memberOlderThanDate(walletFixture.assistant, "2023-10-15").shouldBeFalse()
    }
}