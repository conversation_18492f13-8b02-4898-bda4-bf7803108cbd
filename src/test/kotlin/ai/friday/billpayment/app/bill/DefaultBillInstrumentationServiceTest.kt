package ai.friday.billpayment.app.bill

import ai.friday.billpayment.adapters.instrumentation.BillInstrumentationRepository
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.bill.instrumentation.BillInstrumentationEvent
import ai.friday.billpayment.app.bill.instrumentation.DefaultBillInstrumentationService
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPaymentScheduleStarted
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.weeklyWalletRecurrenceNoEndDate
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Test

class DefaultBillInstrumentationServiceTest {

    private val instrumentationRepository =
        mockk<BillInstrumentationRepository>(relaxUnitFun = true)

    private val walletFixture = WalletFixture()
    val wallet = walletFixture.buildWallet()

    private val walletRepository = mockk<WalletRepository> {
        every {
            findWallet(any())
        } returns wallet
        every {
            findWalletOrNull(any())
        } returns wallet
    }

    private val instrumentationService =
        DefaultBillInstrumentationService(
            instrumentationRepository = instrumentationRepository,
            walletRepository = walletRepository,
        )

    @Test
    fun `should send Scheduled event`() {
        val total = 3

        instrumentationService.scheduled(wallet = wallet, accountId = walletFixture.participantAccount.accountId, total)

        val eventSlot = slot<BillInstrumentationEvent.Scheduled>()
        verify {
            instrumentationRepository.publishEvent(capture(eventSlot))
        }
        with(eventSlot.captured) {
            founderAccountId shouldBe wallet.founder.accountId
            memberAccountId shouldBe walletFixture.participantAccount.accountId
            billsCount shouldBe total
        }
    }

    @Test
    fun `should sent CreatedMailbox event`() {
        val domainName = "qq.domino.serve"
        val emailAddress = EmailAddress("nome.do_usuario@$domainName")
        instrumentationService.createdMailbox(
            Bill.build(
                billAdded.copy(
                    actionSource = ActionSource.WalletMailBox(
                        from = emailAddress.value,
                        accountId = walletFixture.participant.accountId,
                    ),
                ),
            ),
        )

        val eventSlot = slot<BillInstrumentationEvent.CreatedMailbox>()
        verify {
            instrumentationRepository.publishEvent(capture(eventSlot))
        }

        with(eventSlot.captured) {
            founderAccountId shouldBe wallet.founder.accountId
            memberAccountId shouldBe walletFixture.participant.accountId
            senderEmailDomain shouldBe domainName
        }
    }

    @Test
    fun `should sent Recurrence event`() {
        instrumentationService.createdRecurring(
            weeklyWalletRecurrenceNoEndDate,
        )

        val eventSlot = slot<BillInstrumentationEvent.CreatedRecurring>()
        verify {
            instrumentationRepository.publishEvent(capture(eventSlot))
        }

        with(eventSlot.captured) {
            this.founderAccountId shouldBe wallet.founder.accountId
            this.billType shouldBe weeklyWalletRecurrenceNoEndDate.billType
            this.frequency shouldBe weeklyWalletRecurrenceNoEndDate.rule.frequency
            this.memberAccountId shouldBe weeklyWalletRecurrenceNoEndDate.actionSource.accountId
        }
    }

    @Test
    fun `should sent Paid event`() {
        val bill = Bill.build(
            billAdded.copy(
                actionSource = ActionSource.WalletMailBox(
                    from = "<EMAIL>",
                    accountId = walletFixture.participant.accountId,
                ),
            ),
            billPaymentScheduled,
            billPaymentScheduleStarted,
            billPaymentStart,
        )

        instrumentationService.paid(bill)

        val eventSlot = slot<BillInstrumentationEvent.Paid>()
        verify {
            instrumentationRepository.publishEvent(capture(eventSlot))
        }

        with(eventSlot.captured) {
            this.founderAccountId shouldBe wallet.founder.accountId
            this.billType shouldBe bill.billType
        }
    }
}