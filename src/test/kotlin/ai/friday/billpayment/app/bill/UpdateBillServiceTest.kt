package ai.friday.billpayment.app.bill

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.ServerError
import ai.friday.billpayment.adapters.arbi.ArbiValidationResponse
import ai.friday.billpayment.adapters.arbi.CIPSitTitPgto
import ai.friday.billpayment.adapters.bill.DefaultBillEventPublisher
import ai.friday.billpayment.adapters.celcoin.CelcoinBillValidationResponse
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.tracking.amountCalculationValidUntil
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.BillTrackingRepository
import ai.friday.billpayment.app.integrations.BillValidationService
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.BillRegisterData
import ai.friday.billpayment.app.payment.BillRegisterStatus
import ai.friday.billpayment.app.payment.BillValidationResponse
import ai.friday.billpayment.app.payment.DefaultBillValidationResponse
import ai.friday.billpayment.app.payment.DiscountData
import ai.friday.billpayment.app.payment.DiscountType
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.payment.FineData
import ai.friday.billpayment.app.payment.FineType
import ai.friday.billpayment.app.payment.InterestData
import ai.friday.billpayment.app.payment.InterestType
import ai.friday.billpayment.app.wallet.WalletEvent
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billIgnored
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billRegisterUpdatedNotPayable
import ai.friday.billpayment.fichaCompensacaoCreditCardAdded
import ai.friday.billpayment.fichaRegisterData
import ai.friday.billpayment.fixture.TrackableBillFixture
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Called
import io.mockk.Runs
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.verify
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.stream.Stream
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import reactor.core.publisher.Mono

class UpdateBillServiceTest {
    private val billRepositoryMock: BillRepository = mockk(relaxed = true)
    private val eventPublisherMock: EventPublisher = mockk(relaxed = true)
    private val lockProvider: InternalLock = mockk(relaxed = true)
    private val billValidationService = mockk<BillValidationService>(relaxed = true)
    private val boletoSettlementService = mockk<BoletoSettlementService>(relaxed = true)
    private val billTrackingRepositoryMock = mockk<BillTrackingRepository>(relaxed = true)

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet(id = billAdded.walletId)

    private lateinit var approveService: UpdateBillStateService<ApproveUpdateBillRequest>
    private lateinit var denyService: UpdateBillStateService<DenyUpdateBillRequest>

    private val billEventPublisher =
        DefaultBillEventPublisher(
            eventRepository = billEventDbRepositoryMock,
            billRepository = billRepositoryMock,
            eventPublisher = eventPublisherMock,
        )

    private val resolveScheduledBill =
        ResolveScheduledBill(
            billEventPublisher = billEventPublisher,
        )

    private lateinit var updateBillService: UpdateBillService

    @BeforeEach
    fun setup() {
        approveService = mockk()
        denyService = mockk()
        clearMocks(billEventDbRepositoryMock)

        every { billTrackingRepositoryMock.findById(any()) } returns Mono.empty()

        updateBillService =
            UpdateBillService(
                billEventRepository = billEventDbRepositoryMock,
                billRepository = billRepositoryMock,
                billTrackingRepository = billTrackingRepositoryMock,
                eventPublisher = eventPublisherMock,
                boletoSettlementService = boletoSettlementService,
                billValidationService = billValidationService,
                lockProvider = lockProvider,
                walletLimitsService = mockk(),
                walletService = mockk(),
                approveBillService = approveService,
                denyBillService = denyService,
                resolveScheduledBill = resolveScheduledBill,
                cancelSchedulePaymentService = mockk(),
                mailboxWalletDataRepository = mockk(),
            )

        updateBillService.tedLimitTime = "17:00"
    }

    @Test
    fun `should update bill status to ALREADY_PAID when registered amount is empty and is credit card`() {
        every { billEventDbRepositoryMock.getBillById(any()) } answers {
            Either.Right(
                Bill.build(
                    billAddedFicha.copy(
                        fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO,
                    ),
                ),
            )
        }
        val slot = slot<Bill>()
        every { billRepositoryMock.save(capture(slot)) } just Runs

        val amountResponse = newAmountResponse(0)

        val validation =
            amountResponse.copy(
                billRegisterData = amountResponse.billRegisterData!!.copy(fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO),
            )

        val response = updateBillService.synchronizeBill(billAddedFicha.billId, validation)
        response.status.shouldBeTypeOf<BillSynchronizationStatus.BillStatusUpdated>()
        slot.captured.status shouldBe BillStatus.ALREADY_PAID
        verify(exactly = 1) { billEventDbRepositoryMock.save(ofType(RegisterUpdated::class)) }
    }

    @Test
    fun `should return unable to validate when registered amount is empty and is not credit card`() {
        every { billEventDbRepositoryMock.getBillById(any()) } answers {
            Either.Right(
                Bill.build(
                    billAddedFicha.copy(
                        fichaCompensacaoType = FichaCompensacaoType.OUTROS,
                    ),
                ),
            )
        }

        val validation = newAmountResponse(0)
        val response = updateBillService.synchronizeBill(billAddedFicha.billId, validation)

        response.status.shouldBeTypeOf<BillSynchronizationStatus.UnableToValidate>()
        (response.status as BillSynchronizationStatus.UnableToValidate).isRetryable.shouldBeFalse()

        verify(exactly = 0) {
            billRepositoryMock.save(any())
        }

        verify(exactly = 0) { billEventDbRepositoryMock.save(any()) }
    }

    @Test
    fun `should update bill amount when registered amount changes to less `() {
        every { billEventDbRepositoryMock.getBillById(any()) } answers {
            Either.Right(
                Bill.build(
                    fichaCompensacaoCreditCardAdded.copy(registrationUpdateNumber = 10),
                ),
            )
        }

        val slot = slot<Bill>()
        every { billRepositoryMock.save(capture(slot)) } just Runs
        val validation = newAmountResponse(registrationUpdateNumber = 11)
        val response = updateBillService.synchronizeBill(fichaCompensacaoCreditCardAdded.billId, validation)

        Assertions.assertEquals(
            BillSynchronizationStatus.BillAmountTotalUpdatedToLess,
            response.status,
        )
        Assertions.assertEquals(BillStatus.ACTIVE, slot.captured.status)
        Assertions.assertEquals(validation.billRegisterData!!.amountTotal, slot.captured.amountTotal)

        val billEventSlot = slot<RegisterUpdated>()
        verify(exactly = 1) {
            billEventDbRepositoryMock.save(capture(billEventSlot))
            eventPublisherMock.publish(capture(billEventSlot), ofType(BillType::class))
        }
        billEventSlot.captured.updatedRegisterData.shouldBeTypeOf<UpdatedRegisterData.NewTotalAmount>()
        (billEventSlot.captured.updatedRegisterData as UpdatedRegisterData.NewTotalAmount).registrationUpdateNumber shouldBe 11
        (billEventSlot.captured.updatedRegisterData as UpdatedRegisterData.NewTotalAmount).amountCalculationValidUntil shouldBe validation.billRegisterData!!.amountCalculationValidUntil
    }

    @Test
    fun `should update bill amount when registered amount changes to more`() {
        every { billEventDbRepositoryMock.getBillById(any()) } answers {
            Either.Right(
                Bill.build(
                    fichaCompensacaoCreditCardAdded.copy(registrationUpdateNumber = 10),
                ),
            )
        }

        val slot = slot<Bill>()
        every { billRepositoryMock.save(capture(slot)) } just Runs
        val validation = newAmountResponse(amount = 20000L, registrationUpdateNumber = 11)
        val response = updateBillService.synchronizeBill(fichaCompensacaoCreditCardAdded.billId, validation)

        Assertions.assertEquals(
            BillSynchronizationStatus.BillAmountTotalUpdated,
            response.status,
        )
        Assertions.assertEquals(BillStatus.ACTIVE, slot.captured.status)
        Assertions.assertEquals(validation.billRegisterData!!.amountTotal, slot.captured.amountTotal)

        val billEventSlot = slot<RegisterUpdated>()
        verify(exactly = 1) {
            billEventDbRepositoryMock.save(capture(billEventSlot))
            eventPublisherMock.publish(capture(billEventSlot), ofType(BillType::class))
        }
        billEventSlot.captured.updatedRegisterData.shouldBeTypeOf<UpdatedRegisterData.NewTotalAmount>()
        (billEventSlot.captured.updatedRegisterData as UpdatedRegisterData.NewTotalAmount).registrationUpdateNumber shouldBe 11
        (billEventSlot.captured.updatedRegisterData as UpdatedRegisterData.NewTotalAmount).amountCalculationValidUntil shouldBe validation.billRegisterData!!.amountCalculationValidUntil
    }

    @Test
    fun `should update bill amount and calculation data when the values change`() {
        every { billEventDbRepositoryMock.getBillById(any()) } answers {
            Either.Right(
                Bill.build(
                    fichaCompensacaoCreditCardAdded.copy(registrationUpdateNumber = 10),
                ),
            )
        }

        every { billTrackingRepositoryMock.findById(any()) } returns Mono.just(TrackableBillFixture.create())
        every { billRepositoryMock.save(any()) } just Runs

        val fineData = FineData(FineType.VALUE, BigDecimal.valueOf(1.23), LocalDate.now())
        val interestData = InterestData(InterestType.VALUE, BigDecimal.valueOf(1.23), LocalDate.now())
        val discountData = DiscountData(DiscountType.UNKNOWN, BigDecimal.valueOf(1.23), LocalDate.now())
        val validation =
            newAmountResponse(
                amount = 20000L,
                registrationUpdateNumber = 11,
                fineData = fineData,
                interestData = interestData,
                discountData = discountData,
            )

        val response = updateBillService.synchronizeBill(fichaCompensacaoCreditCardAdded.billId, validation)

        Assertions.assertEquals(
            BillSynchronizationStatus.BillAmountTotalUpdated,
            response.status,
        )

        val billEvents = mutableListOf<RegisterUpdated>()
        verify(exactly = 2) {
            eventPublisherMock.publish(capture(billEvents), ofType(BillType::class))
            billEventDbRepositoryMock.save(capture(billEvents))
        }

        billEvents[0].updatedRegisterData.shouldBeTypeOf<UpdatedRegisterData.NewAmountCalculationData>()
        (billEvents[0].updatedRegisterData as UpdatedRegisterData.NewAmountCalculationData).fineData shouldBe fineData
        (billEvents[0].updatedRegisterData as UpdatedRegisterData.NewAmountCalculationData).interestData shouldBe interestData
        (billEvents[0].updatedRegisterData as UpdatedRegisterData.NewAmountCalculationData).discountData shouldBe discountData

        billEvents[1].updatedRegisterData.shouldBeTypeOf<UpdatedRegisterData.NewTotalAmount>()
        (billEvents[1].updatedRegisterData as UpdatedRegisterData.NewTotalAmount).registrationUpdateNumber shouldBe 11
        (billEvents[1].updatedRegisterData as UpdatedRegisterData.NewTotalAmount).amountCalculationValidUntil shouldBe validation.billRegisterData!!.amountCalculationValidUntil
        (billEvents[1].updatedRegisterData as UpdatedRegisterData.NewTotalAmount).amountTotal shouldBe 20000L
    }

    @Test
    fun `should not update calculation data when the values dont change`() {
        every { billEventDbRepositoryMock.getBillById(any()) } answers {
            Either.Right(
                Bill.build(
                    fichaCompensacaoCreditCardAdded.copy(registrationUpdateNumber = 10),
                ),
            )
        }

        every { billRepositoryMock.save(any()) } just Runs

        val fineData = FineData(FineType.VALUE, BigDecimal.valueOf(1.23), LocalDate.now())
        val interestData = InterestData(InterestType.VALUE, BigDecimal.valueOf(1.23), LocalDate.now())
        val discountData = DiscountData(DiscountType.UNKNOWN, BigDecimal.valueOf(1.23), LocalDate.now())

        every { billTrackingRepositoryMock.findById(any()) } returns
            Mono.just(
                TrackableBillFixture.create().copy(
                    fineData = fineData.copy(),
                    interestData = interestData.copy(),
                    discountData = discountData.copy(),
                ),
            )

        val validation =
            newAmountResponse(
                amount = 20000L,
                registrationUpdateNumber = 11,
                fineData = fineData,
                interestData = interestData,
                discountData = discountData,
            )

        val response = updateBillService.synchronizeBill(fichaCompensacaoCreditCardAdded.billId, validation)

        Assertions.assertEquals(
            BillSynchronizationStatus.BillAmountTotalUpdated,
            response.status,
        )

        val billEvents = mutableListOf<RegisterUpdated>()
        verify(exactly = 1) {
            eventPublisherMock.publish(capture(billEvents), ofType(BillType::class))
            billEventDbRepositoryMock.save(capture(billEvents))
        }

        billEvents[0].updatedRegisterData.shouldBeTypeOf<UpdatedRegisterData.NewTotalAmount>()
    }

    @Test
    fun `deve atualizar somente a data de validade do calculo quando o valor nao muda`() {
        every { billEventDbRepositoryMock.getBillById(any()) } answers {
            Either.Right(
                Bill.build(
                    fichaCompensacaoCreditCardAdded.copy(registrationUpdateNumber = 10),
                ),
            )
        }

        val slot = slot<Bill>()
        every { billRepositoryMock.save(capture(slot)) } just Runs
        val validation =
            newAmountResponse(
                registrationUpdateNumber = 11,
                amount = fichaCompensacaoCreditCardAdded.amount,
                amountCalculationValidUntil = fichaCompensacaoCreditCardAdded.amountCalculationValidUntil()!!.plusDays(1),
            )
        val response = updateBillService.synchronizeBill(fichaCompensacaoCreditCardAdded.billId, validation)

        response.status shouldBe BillSynchronizationStatus.BillAmountCalculationValidUntilUpdated

        val billEventSlot = slot<RegisterUpdated>()
        verify(exactly = 1) {
            billEventDbRepositoryMock.save(capture(billEventSlot))
            eventPublisherMock.publish(capture(billEventSlot), ofType(BillType::class))
        }
        billEventSlot.captured.updatedRegisterData.shouldBeTypeOf<UpdatedRegisterData.NewAmountCalculationValidUntil>()
        (billEventSlot.captured.updatedRegisterData as UpdatedRegisterData.NewAmountCalculationValidUntil).amountCalculationValidUntil shouldBe validation.billRegisterData!!.amountCalculationValidUntil
    }

    @Test
    fun `should not update bill amount when registered does not change`() {
        every { billEventDbRepositoryMock.getBillById(any()) } answers { Either.Right(Bill.build(billAdded)) }
        val validation = okResponse()
        val response = updateBillService.synchronizeBill(billAdded.billId, validation)
        Assertions.assertEquals(BillSynchronizationStatus.BillNotModified, response.status)
        verify(exactly = 0) {
            billRepositoryMock.save(ofType(Bill::class))
            billEventDbRepositoryMock.save(ofType(RegisterUpdated::class))
        }
    }

    @Test
    fun `deve sincronizar quando um boleto estiver esperando atualizacao externa`() {
        every {
            billEventDbRepositoryMock.getBill(
                fichaCompensacaoCreditCardAdded.walletId,
                fichaCompensacaoCreditCardAdded.billId,
            )
        } returns Either.Right(mockk(relaxed = true))

        every {
            boletoSettlementService.validateBill(any<Bill>())
        } returns okResponse()

        updateBillService.synchronizeBill(
            Bill.build(
                fichaCompensacaoCreditCardAdded.copy(amountCalculationModel = AmountCalculationModel.BENEFICIARY_ONLY),
                RegisterUpdated(
                    billId = fichaCompensacaoCreditCardAdded.billId,
                    walletId = fichaCompensacaoCreditCardAdded.walletId,
                    updatedRegisterData =
                    UpdatedRegisterData.NewTotalAmount(
                        amount = 0,
                        abatement = 0,
                        discount = 0,
                        interest = 0,
                        fine = 0,
                        amountTotal = 0,
                        lastSettleDate = "",
                        registrationUpdateNumber = null,
                        source = null,
                    ),
                    actionSource = ActionSource.System,
                ),
            ),
        ).isRight().shouldBeTrue()

        verify {
            billValidationService.validate(any<Bill>())
        }

        verify(exactly = 0) {
            boletoSettlementService wasNot Called
        }
    }

    @Test
    fun `não deve sincronizar quando um boleto não estiver ativo`() {
        updateBillService.synchronizeBill(Bill.build(billAdded, billRegisterUpdatedNotPayable)).isRight().shouldBeTrue()

        verify(exactly = 0) {
            boletoSettlementService wasNot Called
            billValidationService wasNot Called
        }
    }

    @Test
    fun `should use boletoSettlementService(CELCOIN) to validate when boleto is concessionaria`() {
        every {
            boletoSettlementService.validateBill(any<Bill>())
        } returns okResponse()

        updateBillService.synchronizeBill(Bill.build(billAdded))

        verify {
            boletoSettlementService.validateBill(any<Bill>())
        }

        verify(exactly = 0) {
            billValidationService wasNot Called
        }
    }

    @Test
    fun `should use billValidationService(ARBI) to validate when boleto is concessionaria`() {
        every {
            boletoSettlementService.validateBill(any<Bill>())
        } returns okResponse()

        updateBillService.synchronizeBill(Bill.build(billAddedFicha))

        verify {
            billValidationService.validate(any<Bill>())
        }

        verify(exactly = 0) {
            boletoSettlementService wasNot Called
        }
    }

    @Test
    fun `should update bill to status NOT_PAYABLE when CODIGO DE BARRAS NAO LOCALIZADO`() {
        every { billEventDbRepositoryMock.getBillById(any()) } answers { Either.Right(Bill.build(billAdded)) }
        val slot = slot<Bill>()
        every { billRepositoryMock.save(capture(slot)) } just Runs
        val validation = CelcoinBillValidationResponse("642", "CODIGO DE BARRAS NAO LOCALIZADO NA BASE CENTRALIZADA")
        val response = updateBillService.synchronizeBill(billAdded.billId, validation)
        response.status.shouldBeTypeOf<BillSynchronizationStatus.BillStatusUpdated>()
        Assertions.assertEquals(BillStatus.NOT_PAYABLE, slot.captured.status)
        verify(exactly = 1) { billEventDbRepositoryMock.save(ofType(RegisterUpdated::class)) }
    }

    @Test
    fun `should return NOT_PAYABLE when BOLETO JA BAIXADO`() {
        every { billEventDbRepositoryMock.getBillById(any()) } answers { Either.Right(Bill.build(billAdded)) }
        val slot = slot<Bill>()
        every { billRepositoryMock.save(capture(slot)) } just Runs
        val validation = CelcoinBillValidationResponse("628", "BOLETO DE PAGAMENTO JA BAIXADO")
        val response = updateBillService.synchronizeBill(billAdded.billId, validation)

        response.status.shouldBeTypeOf<BillSynchronizationStatus.BillStatusUpdated>().also {
            it.billStatus shouldBe BillStatus.NOT_PAYABLE
        }

        verify(exactly = 1) { billEventDbRepositoryMock.save(any()) }
    }

    @Test
    fun `should update bill to status ALREADY_PAID and paid value to bill total value when PAGAMENTO JA EFETUADO and billRegisterData is null`() {
        every { billEventDbRepositoryMock.getBillById(any()) } answers { Either.Right(Bill.build(billAdded)) }
        val slot = slot<Bill>()
        every { billRepositoryMock.save(capture(slot)) } just Runs

        val validation = alreadyPaidValidationResponse()
        val response = updateBillService.synchronizeBill(billAdded.billId, validation)

        response.status.shouldBeTypeOf<BillSynchronizationStatus.BillStatusUpdated>()
        with(slot.captured) {
            status shouldBe BillStatus.ALREADY_PAID
            amountPaid shouldBe billAdded.amountTotal
        }
        verify(exactly = 1) { billEventDbRepositoryMock.save(ofType(RegisterUpdated::class)) }
    }

    @Test
    fun `should update bill to status ALREADY_PAID and paid value to received value when billRegisterDataExists`() {
        every { billEventDbRepositoryMock.getBillById(any()) } answers { Either.Right(Bill.build(billAdded)) }
        val slot = slot<Bill>()
        every { billRepositoryMock.save(capture(slot)) } just Runs

        val paidValue = 123L
        val validation = alreadyPaidArbiValidationResponse(paidValue)
        val response = updateBillService.synchronizeBill(billAdded.billId, validation)

        response.status.shouldBeTypeOf<BillSynchronizationStatus.BillStatusUpdated>()
        with(slot.captured) {
            status shouldBe BillStatus.ALREADY_PAID
            amountPaid shouldBe paidValue
        }
        verify(exactly = 1) { billEventDbRepositoryMock.save(ofType(RegisterUpdated::class)) }
    }

    @Test
    fun `should publish BillPaymentScheduleCanceled when BOLETO JA BAIXADO and bill is scheduled`() {
        every { billEventDbRepositoryMock.getBillById(any()) } answers {
            Either.Right(
                Bill.build(
                    billAdded,
                    billPaymentScheduled,
                ),
            )
        }
        val slot = slot<Bill>()
        every { billRepositoryMock.save(capture(slot)) } just Runs
        val validation = CelcoinBillValidationResponse("642", "CODIGO DE BARRAS NAO LOCALIZADO NA BASE CENTRALIZADA")
        val response = updateBillService.synchronizeBill(billAdded.billId, validation)
        response.status.shouldBeTypeOf<BillSynchronizationStatus.BillStatusUpdated>()
        slot.captured.status shouldBe BillStatus.NOT_PAYABLE
        verify(exactly = 1) {
            billEventDbRepositoryMock.save(ofType(RegisterUpdated::class))
            billEventDbRepositoryMock.save(ofType(BillPaymentScheduleCanceled::class))
        }
    }

    @Test
    fun `should publish BillPaymentScheduleCanceled when BOLETO JA PAGO and bill is scheduled`() {
        every { billEventDbRepositoryMock.getBillById(any()) } answers {
            Either.Right(
                Bill.build(
                    billAdded,
                    billPaymentScheduled,
                ),
            )
        }
        val slot = slot<Bill>()
        every { billRepositoryMock.save(capture(slot)) } just Runs

        val validation = alreadyPaidValidationResponse()
        val response = updateBillService.synchronizeBill(billAdded.billId, validation)

        response.status.shouldBeTypeOf<BillSynchronizationStatus.BillStatusUpdated>()

        with(slot.captured) {
            status shouldBe BillStatus.ALREADY_PAID
            amountPaid shouldBe billAdded.amountTotal
        }

        verify(exactly = 1) {
            billEventDbRepositoryMock.save(ofType(RegisterUpdated::class))
            billEventDbRepositoryMock.save(ofType(BillPaymentScheduleCanceled::class))
        }
    }

    @Test
    fun `should return generic error message on unable to validate error`() {
        every { billEventDbRepositoryMock.getBillById(any()) } answers { Either.Right(Bill.build(billAdded)) }
        val validation = CelcoinBillValidationResponse("621", "mensagem errada")
        val response = updateBillService.synchronizeBill(billAdded.billId, validation)
        response.status.shouldBeTypeOf<BillSynchronizationStatus.UnableToValidate>()
        response.syncErrorMessages shouldBe SyncErrorMessages.UnableToValidate
    }

    @Test
    fun `should return Unable to Validate and non-retryable when validation response is a non-retryable unable to validate error`() {
        every { billEventDbRepositoryMock.getBillById(any()) } answers { Either.Right(Bill.build(billAdded)) }
        val validation = ArbiValidationResponse("107: Grupo de cálculo não informado!")
        val response = updateBillService.synchronizeBill(billAdded.billId, validation)
        val status = response.status
        status.shouldBeTypeOf<BillSynchronizationStatus.UnableToValidate>()
        status.isRetryable.shouldBeFalse()
        response.syncErrorMessages shouldBe SyncErrorMessages.UnableToValidate
    }

    @Test
    fun `should return Unable to Validate and retryable when validation response is a retryable unable to validate error`() {
        every { billEventDbRepositoryMock.getBillById(any()) } answers { Either.Right(Bill.build(billAdded)) }
        val validation = ArbiValidationResponse("108: Error!")
        val response = updateBillService.synchronizeBill(billAdded.billId, validation)
        val status = response.status
        status.shouldBeTypeOf<BillSynchronizationStatus.UnableToValidate>()
        status.isRetryable.shouldBeTrue()
        response.syncErrorMessages shouldBe SyncErrorMessages.UnableToValidate
    }

    @Test
    fun `should return false on empty list`() {
        val bills: List<Bill> = listOf()
        bills.hasAnyBillScheduleForTodayOrBefore() shouldBe false
    }

    @Test
    fun `should return false on list with bill with future due date`() {
        val bills: List<Bill> = listOf(Bill.build(billAdded))
        bills.hasAnyBillScheduleForTodayOrBefore() shouldBe false
    }

    @Test
    fun `should return true on list with bill overdue`() {
        val overdueBill =
            Bill.build(
                billAdded.copy(
                    dueDate = getLocalDate().plusDays(3),
                    effectiveDueDate = getLocalDate().minusDays(3),
                ),
            )
        val bills: List<Bill> = listOf(overdueBill)
        bills.hasAnyBillScheduleForTodayOrBefore() shouldBe true
    }

    @Test
    fun `should return true on list with one bill overdue and one bill with future due date`() {
        val overdueBill =
            Bill.build(
                billAdded.copy(
                    dueDate = getLocalDate().plusDays(3),
                    effectiveDueDate = getLocalDate().minusDays(3),
                ),
            )
        val bills: List<Bill> = listOf(Bill.build(billAdded), overdueBill)
        bills.hasAnyBillScheduleForTodayOrBefore() shouldBe true
    }

    @Test
    fun `should return true on list with bill with due date today`() {
        val overdueBill =
            Bill.build(
                billAdded.copy(
                    dueDate = getLocalDate().plusDays(1),
                    effectiveDueDate = getLocalDate(),
                ),
            )
        val bills: List<Bill> = listOf(overdueBill)
        bills.hasAnyBillScheduleForTodayOrBefore() shouldBe true
    }

    @Test
    fun `não deve sincronizar a conta caso a última validação seja mais antiga que a atual`() {
        val bill = Bill.build(fichaCompensacaoCreditCardAdded.copy(registrationUpdateNumber = 11L))
        val response =
            updateBillService.synchronizeBill(
                bill,
                arbiOkResponse(registrationUpdateNumber = 9L),
            )

        response.status.shouldBeTypeOf<BillSynchronizationStatus.UnableToValidate>()
        response.syncErrorMessages shouldBe SyncErrorMessages.OldRegistrationUpdateNumber
    }

    @Test
    fun `deve tornar a bill NotPayable quando o boleto nao for encontrado na integradora durante o fluxo de reativacao`() {
        val bill = Bill.build(billAddedFicha, billIgnored.copy(billId = billAddedFicha.billId))

        every { billEventDbRepositoryMock.getBill(any(), any()) } returns bill.right()
        every {
            billEventDbRepositoryMock.findLastBill(any<BarCode>(), any())
        } returns bill.right()

        every { eventPublisherMock.publish(any<WalletEvent>()) } just runs
        every { billRepositoryMock.save(any()) } just runs

        every { billValidationService.validate(any<Bill>()) } returns
            DefaultBillValidationResponse(
                billRegisterData = null,
                gateway = FinancialServiceGateway.ARBI,
                errorDescription = null,
                billRegisterStatus =
                BillRegisterStatus(
                    notPayable = false,
                    alreadyPaid = false,
                    barcodeNotFound = true,
                ),
                retryable = false,
            )

        val result =
            updateBillService.reactivateBill(bill.billId, bill.walletId, ActionSource.DDA(AccountId("123")))

        result.isRight() shouldBe true
        result.map {
            it.status shouldBe BillStatus.NOT_PAYABLE
        }

        verify { billEventDbRepositoryMock.getBill(bill.walletId, bill.billId) }
        verify { billEventDbRepositoryMock.findLastBill(bill.barcode!!, bill.walletId) }
        verify {
            eventPublisherMock.publish(
                withArg<BillEvent> {
                    it.billId shouldBe bill.billId
                    it.walletId shouldBe bill.walletId
                    it.eventType shouldBe BillEventType.REACTIVATED
                },
                BillType.FICHA_COMPENSACAO,
            )
        }

        verify { billRepositoryMock.save(bill) }
        verify { billValidationService.validate(bill) }
    }

    @MethodSource(
        "creditCardAlreadyPaidSource",
    )
    @ParameterizedTest
    fun `não deve marcar como pago um cartão de crédito quando a data de pagamento for anterior a data de criação da bill`(
        billCreatedAt: ZonedDateTime,
        billPaidAt: LocalDate,
        assertFunc: (SynchronizeBillResponse) -> Unit,
    ) {
        every { billEventDbRepositoryMock.getBillById(any()) } answers {
            Either.Right(
                Bill.build(
                    billAddedFicha.copy(
                        fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO,
                        created = billCreatedAt.toInstant().toEpochMilli(),
                    ),
                ),
            )
        }

        val validation =
            ArbiValidationResponse(
                paymentStatus = CIPSitTitPgto.SUCESSO.code,
                billRegisterData =
                fichaRegisterData.copy(
                    fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO,
                    amountTotal = billAddedFicha.amountTotal,
                    amountPaid = billAddedFicha.amountTotal,
                    paidDate = billPaidAt,
                    amountCalculationValidUntil = billAddedFicha.amountCalculationValidUntil(),
                ),
            )

        assertFunc(updateBillService.synchronizeBill(billAddedFicha.billId, validation))
    }

    @Test
    fun `should return left error when approve service throws an error`() {
        every { approveService.update(any()) } returns ServerError(NoStackTraceException()).left()

        val result =
            updateBillService.approve(BillId("123"), WalletId("123"), wallet.founder, false, ActionSource.Api(AccountId("123")))

        result.isLeft() shouldBe true
        result.mapLeft { it.ex.shouldBeTypeOf<NoStackTraceException>() }

        verify {
            approveService.update(
                withArg {
                    it.billId shouldBe BillId("123")
                    it.walletId shouldBe WalletId("123")
                    it.member shouldBe wallet.founder
                    it.actionSource shouldBe ActionSource.Api(AccountId("123"))
                    it.alwaysAllowSender shouldBe false
                },
            )
        }
    }

    @Test
    fun `should approve`() {
        val bill = Bill.build(billAdded)

        every { approveService.update(any()) } returns bill.right()

        val result =
            updateBillService.approve(BillId("123"), WalletId("123"), wallet.founder, false, ActionSource.Api(AccountId("123")))

        result.isRight() shouldBe true
        result.map { it shouldBe bill }

        verify {
            approveService.update(
                withArg {
                    it.billId shouldBe BillId("123")
                    it.walletId shouldBe WalletId("123")
                    it.member shouldBe wallet.founder
                    it.actionSource shouldBe ActionSource.Api(AccountId("123"))
                    it.alwaysAllowSender shouldBe false
                },
            )
        }
    }

    @Test
    fun `should return left error when deny service throws an error`() {
        every { denyService.update(any()) } returns ServerError(NoStackTraceException()).left()

        val result =
            updateBillService.deny(BillId("123"), WalletId("123"), wallet.founder, ActionSource.Api(AccountId("123")))

        result.isLeft() shouldBe true
        result.mapLeft { it.ex.shouldBeTypeOf<NoStackTraceException>() }

        verify {
            denyService.update(
                withArg {
                    it.billId shouldBe BillId("123")
                    it.walletId shouldBe WalletId("123")
                    it.member shouldBe wallet.founder
                    it.actionSource shouldBe ActionSource.Api(AccountId("123"))
                },
            )
        }
    }

    @Test
    fun `should deny`() {
        val bill = Bill.build(billAdded)

        every { denyService.update(any()) } returns bill.right()

        val result =
            updateBillService.deny(BillId("123"), WalletId("123"), wallet.founder, ActionSource.Api(AccountId("123")))

        result.isRight() shouldBe true
        result.map { it shouldBe bill }

        verify {
            denyService.update(
                withArg {
                    it.billId shouldBe BillId("123")
                    it.walletId shouldBe WalletId("123")
                    it.member shouldBe wallet.founder
                    it.actionSource shouldBe ActionSource.Api(AccountId("123"))
                },
            )
        }
    }

    @Test
    fun `deve atualizar uma conta ACTIVE para WAITING_BENEFICIARY_UPDATE quando receber uma atualização com valor zerado do tipo BENEFICIARY_ONLY`() {
        every { billEventDbRepositoryMock.getBillById(any()) } answers {
            Either.Right(
                Bill.build(
                    fichaCompensacaoCreditCardAdded.copy(
                        amountCalculationModel = AmountCalculationModel.BENEFICIARY_ONLY,
                        fichaCompensacaoType = FichaCompensacaoType.OUTROS,
                    ),
                ),
            )
        }
        val slot = slot<Bill>()
        every { billRepositoryMock.save(capture(slot)) } just Runs

        val validation = newAmountResponse(0, amountCalculationModel = AmountCalculationModel.BENEFICIARY_ONLY)
        val response = updateBillService.synchronizeBill(fichaCompensacaoCreditCardAdded.billId, validation)

        response.status.shouldBeTypeOf<BillSynchronizationStatus.BillAmountTotalUpdated>()
        with(slot.captured) {
            status shouldBe BillStatus.WAITING_BENEFICIARY_UPDATE
        }
        verify(exactly = 1) { billEventDbRepositoryMock.save(ofType(RegisterUpdated::class)) }
    }

    @Test
    fun `deve atualizar uma conta WAITING_BENEFICIARY_UPDATE para ACTIVE quando receber uma atualização com valor maior que zero do tipo BENEFICIARY_ONLY`() {
        every { billEventDbRepositoryMock.getBillById(any()) } answers {
            Either.Right(
                Bill.build(
                    fichaCompensacaoCreditCardAdded.copy(amountCalculationModel = AmountCalculationModel.BENEFICIARY_ONLY),
                    RegisterUpdated(
                        billId = fichaCompensacaoCreditCardAdded.billId,
                        walletId = fichaCompensacaoCreditCardAdded.walletId,
                        updatedRegisterData =
                        UpdatedRegisterData.NewTotalAmount(
                            amount = 0,
                            abatement = 0,
                            discount = 0,
                            interest = 0,
                            fine = 0,
                            amountTotal = 0,
                            lastSettleDate = "",
                            registrationUpdateNumber = null,
                            source = null,
                        ),
                        actionSource = ActionSource.System,
                    ),
                ),
            )
        }
        val slot = slot<Bill>()
        every { billRepositoryMock.save(capture(slot)) } just Runs

        val validation = newAmountResponse(10, amountCalculationModel = AmountCalculationModel.BENEFICIARY_ONLY)
        val response = updateBillService.synchronizeBill(fichaCompensacaoCreditCardAdded.billId, validation)

        response.status.shouldBeTypeOf<BillSynchronizationStatus.BillAmountTotalUpdated>()
        with(slot.captured) {
            status shouldBe BillStatus.ACTIVE
        }
        verify(exactly = 1) { billEventDbRepositoryMock.save(ofType(RegisterUpdated::class)) }
    }

    @Test
    fun `deve atualizar uma conta WAITING_BENEFICIARY_UPDATE para NOT_PAYABLE quando permanecer mais de 5 dias no mesmo status`() {
        every { billEventDbRepositoryMock.getBillById(any()) } answers {
            Either.Right(
                Bill.build(
                    fichaCompensacaoCreditCardAdded.copy(
                        amountCalculationModel = AmountCalculationModel.BENEFICIARY_ONLY,
                        fichaCompensacaoType = FichaCompensacaoType.OUTROS,
                    ),
                    RegisterUpdated(
                        created = ZonedDateTime.now().minusDays(6).toInstant().toEpochMilli(),
                        billId = fichaCompensacaoCreditCardAdded.billId,
                        walletId = fichaCompensacaoCreditCardAdded.walletId,
                        updatedRegisterData =
                        UpdatedRegisterData.NewTotalAmount(
                            amount = 0,
                            abatement = 0,
                            discount = 0,
                            interest = 0,
                            fine = 0,
                            amountTotal = 0,
                            lastSettleDate = "",
                            registrationUpdateNumber = null,
                            source = null,
                        ),
                        actionSource = ActionSource.System,
                    ),
                ),
            )
        }
        val slot = slot<Bill>()
        every { billRepositoryMock.save(capture(slot)) } just Runs

        val validation = newAmountResponse(0, amountCalculationModel = AmountCalculationModel.BENEFICIARY_ONLY)
        val response = updateBillService.synchronizeBill(fichaCompensacaoCreditCardAdded.billId, validation)

        response.status.shouldBeTypeOf<BillSynchronizationStatus.BillAmountTotalUpdated>()
        with(slot.captured) {
            status shouldBe BillStatus.NOT_PAYABLE
        }
        verify(exactly = 1) { billEventDbRepositoryMock.save(ofType(RegisterUpdated::class)) }
    }

    companion object {
        private val billEventDbRepositoryMock: BillEventDBRepository = mockk(relaxed = true)

        @JvmStatic
        fun creditCardAlreadyPaidSource(): Stream<Arguments> {
            fun assertUpdated(response: SynchronizeBillResponse) {
                response.status.shouldBeTypeOf<BillSynchronizationStatus.BillStatusUpdated>()
                verify(exactly = 1) { billEventDbRepositoryMock.save(ofType(RegisterUpdated::class)) }
            }

            fun assertNotModified(response: SynchronizeBillResponse) {
                response.status.shouldBeTypeOf<BillSynchronizationStatus.BillNotModified>()
                verify(exactly = 0) { billEventDbRepositoryMock.save(ofType(RegisterUpdated::class)) }
            }

            return Stream.of(
                Arguments.of(ZonedDateTime.now().minusDays(10), LocalDate.now().minusDays(15), ::assertNotModified),
                Arguments.of(ZonedDateTime.now().minusDays(10), LocalDate.now().minusDays(1), ::assertUpdated),
                Arguments.of(ZonedDateTime.now().minusDays(7), LocalDate.now().minusDays(7), ::assertUpdated),
            )
        }
    }
}

fun alreadyPaidValidationResponse(): CelcoinBillValidationResponse =
    CelcoinBillValidationResponse("183", "PAGAMENTO JA EFETUADO")

fun alreadyPaidArbiValidationResponse(amountPaid: Long): BillValidationResponse =
    ArbiValidationResponse(
        billRegisterData = fichaRegisterData.copy(amountPaid = amountPaid),
        paymentStatus = CIPSitTitPgto.PAGAMENTO_JA_EFETUADO.code,
    )

fun newAmountResponse(
    amount: Long = 10,
    registrationUpdateNumber: Long = 10,
    amountCalculationModel: AmountCalculationModel = AmountCalculationModel.UNKNOWN,
    amountCalculationValidUntil: LocalDate = LocalDate.now(),
    fineData: FineData? = null,
    interestData: InterestData? = null,
    discountData: DiscountData? = null,
): ArbiValidationResponse =
    ArbiValidationResponse(
        paymentStatus = CIPSitTitPgto.SUCESSO.code,
        billRegisterData =
        BillRegisterData(
            amount = amount,
            amountTotal = amount,
            settleDate = getLocalDate(),
            assignor = "",
            dueDate = getLocalDate(),
            billType = BillType.CONCESSIONARIA,
            expirationDate = getLocalDate(),
            payerDocument = "",
            amountCalculationModel = amountCalculationModel,
            recipientChain = null,
            registrationUpdateNumber = registrationUpdateNumber,
            amountCalculationValidUntil = amountCalculationValidUntil,
            divergentPayment = null,
            fineData = fineData,
            interestData = interestData,
            discountData = discountData,
            partialPayment = null,
        ),
    )

fun arbiOkResponse(
    registrationUpdateNumber: Long = 10,
    amountTotal: Long = 15591L,
) =
    ArbiValidationResponse(
        paymentStatus = CIPSitTitPgto.SUCESSO.code,
        billRegisterData =
        BillRegisterData(
            amount = 15591L,
            amountTotal = amountTotal,
            settleDate = getLocalDate(),
            assignor = "",
            dueDate = getLocalDate(),
            billType = BillType.CONCESSIONARIA,
            expirationDate = getLocalDate(),
            payerDocument = "",
            amountCalculationModel = AmountCalculationModel.UNKNOWN,
            recipientChain = null,
            registrationUpdateNumber = registrationUpdateNumber,
            divergentPayment = null,
        ),
    )

fun okResponse(registrationUpdateNumber: Long = 10): CelcoinBillValidationResponse =
    CelcoinBillValidationResponse(
        0,
        "000",
        "SUCESSO",
        BillRegisterData(
            amount = 15591L,
            amountTotal = 15591L,
            settleDate = getLocalDate(),
            assignor = "",
            dueDate = getLocalDate(),
            billType = BillType.CONCESSIONARIA,
            expirationDate = getLocalDate(),
            payerDocument = "",
            amountCalculationModel = AmountCalculationModel.UNKNOWN,
            recipientChain = null,
            registrationUpdateNumber = registrationUpdateNumber,
            divergentPayment = null,
        ),
    )

private fun List<Bill>.hasAnyBillScheduleForTodayOrBefore(): Boolean {
    return this.any { !it.effectiveDueDate.isAfter(getZonedDateTime().toLocalDate()) }
}