package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.recurrence.Range
import ai.friday.billpayment.app.recurrence.RecurrenceFrequency
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAddedInvestment
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.integration.BILL_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.pixPaymentScheduled
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class UpdateBillServiceUpdateAmountTest {

    private val walletFixture = WalletFixture()
    private val wallet =
        walletFixture.buildWallet(otherMembers = listOf(walletFixture.cantPayParticipant, walletFixture.assistant))

    private val billEventRepository = mockk<BillEventRepository>() {
        every {
            save(any())
        } just Runs
    }

    private val billRepository = mockk<BillRepository>() {
        every {
            save(any())
        } just Runs
    }

    private val walletLimitsService = mockk<WalletLimitsService>()

    private val updateBillService = UpdateBillService(
        billEventRepository = billEventRepository,
        billRepository = billRepository,
        billTrackingRepository = mockk(relaxed = true),
        eventPublisher = mockk(relaxUnitFun = true),
        boletoSettlementService = mockk(),
        billValidationService = mockk(),
        lockProvider = mockk() {
            every { acquireLock(any()) } returns mockk(relaxed = true)
        },
        walletLimitsService = walletLimitsService,
        walletService = mockk() {
            every {
                findWallet(wallet.id)
            } returns wallet
        },
        approveBillService = mockk(),
        denyBillService = mockk(),
        resolveScheduledBill = mockk(),
        cancelSchedulePaymentService = mockk {
            every { cancelScheduledPayment(any(), any(), any()) } returns true
            every { userCancelScheduledPayment(any(), any(), any(), any()) } returns true
        },
        mailboxWalletDataRepository = mockk(),
    )

    @Test
    fun `se não encontrar a conta deve retornar uma falha`() {
        every {
            billEventRepository.getBillById(any())
        } returns ItemNotFoundException(BillId(BILL_ID)).left()

        val response = updateBillService.updateAmount(
            billId = BillId(BILL_ID),
            amount = 40,
            actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
        )

        response.isLeft() shouldBe true
        response.mapLeft {
            it.shouldBeInstanceOf<ItemNotFoundException>()
        }
    }

    @Test
    fun `se o membro não tiver permissao de ver a conta deve retornar uma falha`() {
        every {
            billEventRepository.getBillById(any())
        } returns Bill.build(pixAdded.copy(walletId = wallet.id)).right()

        val response = updateBillService.updateAmount(
            billId = BillId(BILL_ID),
            amount = 40,
            actionSource = ActionSource.Api(accountId = walletFixture.ultraLimitedParticipant.accountId),
        )

        response.isLeft() shouldBe true
        response.mapLeft {
            it.shouldBeInstanceOf<MemberNotAllowedException>()
        }
    }

    @Test
    fun `se o membro não tiver permissao de adicionar contas na carteira deve retornar uma falha`() {
        every {
            billEventRepository.getBillById(any())
        } returns Bill.build(pixAdded.copy(walletId = wallet.id)).right()

        val response = updateBillService.updateAmount(
            billId = BillId(BILL_ID),
            amount = 40,
            actionSource = ActionSource.Api(accountId = walletFixture.cantPayParticipant.accountId),
        )

        response.isLeft() shouldBe true
        response.mapLeft {
            it.shouldBeInstanceOf<MemberNotAllowedException>()
        }
    }

    @ParameterizedTest
    @EnumSource(
        value = BillType::class,
        mode = EnumSource.Mode.EXCLUDE,
        names = ["INVOICE", "PIX", "INVESTMENT"],
    )
    fun `se a conta não for uma transferencia (TED ou PIX) ou um investimento, deve retornar uma falha`(billType: BillType) {
        every {
            billEventRepository.getBillById(any())
        } returns Bill.build(billAdded.copy(walletId = wallet.id, billType = billType)).right()

        val response = updateBillService.updateAmount(
            billId = BillId(BILL_ID),
            amount = 40,
            actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
        )

        response.isLeft() shouldBe true
        response.mapLeft {
            it.shouldBeInstanceOf<BillAmountIsNotEditable>()
        }
    }

    @Test
    fun `se a conta for uma assinatura deve retornar uma falha`() {
        every {
            billEventRepository.getBillById(any())
        } returns Bill.build(pixAdded.copy(walletId = wallet.id, subscriptionFee = true)).right()

        val response = updateBillService.updateAmount(
            billId = pixAdded.billId,
            amount = 40,
            actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
        )

        response.isLeft() shouldBe true
        response.mapLeft {
            it.shouldBeInstanceOf<BillAmountIsNotEditable>()
        }
    }

    @Test
    fun `se a conta for um investimento e o valor tiver centavos deve falhar`() {
        every {
            billEventRepository.getBillById(any())
        } returns Bill.build(billAddedInvestment.copy(walletId = wallet.id)).right()

        val response = updateBillService.updateAmount(
            billId = pixAdded.billId,
            amount = 1_40,
            actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
        )

        response.isLeft() shouldBe true
        response.mapLeft {
            it.shouldBeInstanceOf<BillValidationException>()
        }
    }

    @Test
    fun `se conta estiver agendada e usuário não puder agendar deverá falhar`() {
        every {
            billEventRepository.getBillById(any())
        } returns Bill.build(
            billAdded.copy(walletId = wallet.id),
            billPaymentScheduled.copy(walletId = wallet.id, billId = billAdded.billId),
        ).right()

        val response = updateBillService.updateAmount(
            billId = BillId(BILL_ID),
            amount = 40,
            actionSource = ActionSource.Api(accountId = walletFixture.assistant.accountId),
        )

        response.isLeft() shouldBe true
        response.mapLeft {
            it.shouldBeInstanceOf<MemberNotAllowedException>()
        }
    }

    @ParameterizedTest
    @EnumSource(value = Range::class)
    fun `se for uma conta recorrente e estiver agendada e usuário não puder agendar deverá falhar independentemente da escolha do usuario`(
        range: Range,
    ) {
        val recurrenceRule = RecurrenceRule(
            frequency = RecurrenceFrequency.MONTHLY,
            startDate = getLocalDate(),
            pattern = "1",
        )
        every {
            billEventRepository.getBillById(any())
        } returns Bill.build(
            billAdded.copy(walletId = wallet.id, recurrenceRule = recurrenceRule),
            billPaymentScheduled.copy(walletId = wallet.id, billId = billAdded.billId),
        ).right()

        val response = updateBillService.updateAmount(
            billId = BillId(BILL_ID),
            amount = 40,
            actionSource = ActionSource.Api(accountId = walletFixture.cantPayParticipant.accountId),
        )

        response.isLeft() shouldBe true
        response.mapLeft {
            it.shouldBeInstanceOf<MemberNotAllowedException>()
        }
    }

    @Test
    fun `se o valor foi maior que o limite diario da carteira deverá falhar se o pagamento for para hoje`() {
        val dailyLimit = 400L

        every {
            billEventRepository.getBillById(any())
        } returns Bill.build(
            pixAdded.copy(
                walletId = wallet.id,
                billId = BillId(BILL_ID),
                amount = dailyLimit,
                amountTotal = dailyLimit,
                dueDate = getLocalDate(),
                effectiveDueDate = getLocalDate(),
            ),
        ).right()

        every {
            walletLimitsService.getDailyLimit(any())
        } returns dailyLimit

        val response = updateBillService.updateAmount(
            billId = BillId(BILL_ID),
            amount = dailyLimit + 1,
            actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
        )

        response.isLeft() shouldBe true
        response.mapLeft {
            it.shouldBeInstanceOf<BillValidationException>()
        }
    }

    @Test
    fun `se o valor foi maior que o limite diario da carteira não deverá falhar se o pagamento não for para hoje`() {
        val dailyLimit = 400L

        every {
            billEventRepository.getBillById(any())
        } returns Bill.build(
            pixAdded.copy(
                walletId = wallet.id,
                billId = BillId(BILL_ID),
                amount = dailyLimit,
                amountTotal = dailyLimit,
                dueDate = getLocalDate().plusDays(2),
                effectiveDueDate = getLocalDate().plusDays(2),
            ),
        ).right()

        val response = updateBillService.updateAmount(
            billId = BillId(BILL_ID),
            amount = dailyLimit + 1,
            actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
        )

        verify(exactly = 0) {
            walletLimitsService.getDailyLimit(any())
            walletLimitsService.getAvailableLimit(
                walletId = any(),
                date = any(),
                countScheduledBankTransfer = any(),
            )
        }

        response.isRight() shouldBe true
        response.map {
            it.amount shouldBe dailyLimit + 1
        }
    }

    @Test
    fun `se o valor foi maior que o limite disponível para o dia deverá falhar se  pagamento for hoje`() {
        val dailyLimit = 400L
        val initialValue = 100L
        val availableLimit = 200L

        every {
            billEventRepository.getBillById(any())
        } returns Bill.build(
            pixAdded.copy(
                walletId = wallet.id,
                billId = BillId(BILL_ID),
                amount = initialValue,
                amountTotal = initialValue,
                dueDate = getLocalDate(),
                effectiveDueDate = getLocalDate(),
            ),
        ).right()

        every {
            walletLimitsService.getDailyLimit(any())
        } returns dailyLimit

        every {
            walletLimitsService.getAvailableLimit(
                walletId = wallet.id,
                date = any(),
                countScheduledBankTransfer = true,
            )
        } returns availableLimit

        val response = updateBillService.updateAmount(
            billId = BillId(BILL_ID),
            amount = availableLimit + 1,
            actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
        )

        response.isLeft() shouldBe true
        response.mapLeft {
            it.shouldBeInstanceOf<BillValidationException>()
        }
    }

    @Test
    fun `se o valor foi maior que o limite disponível para o dia não deverá falhar se pagamento não for hoje`() {
        val dailyLimit = 400L
        val initialValue = 100L
        val availableLimit = 200L

        every {
            billEventRepository.getBillById(any())
        } returns Bill.build(
            pixAdded.copy(
                walletId = wallet.id,
                billId = BillId(BILL_ID),
                amount = initialValue,
                amountTotal = initialValue,
                dueDate = getLocalDate().plusDays(2),
                effectiveDueDate = getLocalDate().plusDays(2),
            ),
        ).right()

        val response = updateBillService.updateAmount(
            billId = BillId(BILL_ID),
            amount = availableLimit + 1,
            actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
        )

        verify(exactly = 0) {
            walletLimitsService.getDailyLimit(any())
            walletLimitsService.getAvailableLimit(
                walletId = any(),
                date = any(),
                countScheduledBankTransfer = any(),
            )
        }

        response.isRight() shouldBe true
        response.map {
            it.amount shouldBe availableLimit + 1
        }
    }

    @Test
    fun `se a conta está agendada o valor considerado para a alteraçao de limite deve ser o delta entre o valor novo e o velho`() {
        val dailyLimit = 400L
        val initialValue = 100L
        val availableLimit = 200L

        every {
            billEventRepository.getBillById(any())
        } returns Bill.build(
            pixAdded.copy(
                walletId = wallet.id,
                billId = BillId(BILL_ID),
                amount = initialValue,
                amountTotal = initialValue,
            ),
            pixPaymentScheduled.copy(
                walletId = wallet.id,
                billId = BillId(
                    BILL_ID,
                ),
                amount = initialValue,
            ),
        ).right()

        every {
            walletLimitsService.getDailyLimit(any())
        } returns dailyLimit

        every {
            walletLimitsService.getAvailableLimit(
                walletId = wallet.id,
                date = any(),
                countScheduledBankTransfer = true,
            )
        } returns availableLimit

        val newAmount = initialValue + availableLimit
        val response = updateBillService.updateAmount(
            billId = BillId(BILL_ID),
            amount = newAmount,
            actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
        )

        response.isRight() shouldBe true

        verify {
            billEventRepository.save(
                withArg {
                    it.eventType shouldBe BillEventType.AMOUNT_UPDATED
                    it.shouldBeTypeOf<AmountUpdated>()
                    it.amount shouldBe newAmount
                },
            )
        }
        response.map {
            it.amount shouldBe newAmount
        }
    }
}