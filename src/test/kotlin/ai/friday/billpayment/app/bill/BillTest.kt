package ai.friday.billpayment.app.bill

import DynamoDBUtils
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.InterestData
import ai.friday.billpayment.app.payment.InterestType
import ai.friday.billpayment.app.payment.MAX_PAYMENT_LIMIT_AMOUNT
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billAddedInvestment
import ai.friday.billpayment.billCategoryAdded
import ai.friday.billpayment.billCategoryDeleted
import ai.friday.billpayment.billIgnored
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentFailed
import ai.friday.billpayment.billPaymentFailedRetryable
import ai.friday.billpayment.billPaymentSchedulePostponedDueToLimitReached
import ai.friday.billpayment.billPaymentScheduleStarted
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.billPostponedByInsufficientFunds
import ai.friday.billpayment.billReactivated
import ai.friday.billpayment.billRefunded
import ai.friday.billpayment.billRegisterUpdatedAlreadyPaid
import ai.friday.billpayment.billRegisterUpdatedNewAmount
import ai.friday.billpayment.billRegisterUpdatedNewDueDate
import ai.friday.billpayment.billRegisterUpdatedNotPayable
import ai.friday.billpayment.billScheduleCanceled
import ai.friday.billpayment.billTagAdded
import ai.friday.billpayment.billTagDeleted
import ai.friday.billpayment.fichaCompensacaoCreditCardAdded
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.CARNET_BARCODE_LINE
import ai.friday.billpayment.integration.ELECTRICITY_AND_GAS_BARCODE_LINE
import ai.friday.billpayment.integration.EMAIL
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_BARCODE
import ai.friday.billpayment.integration.GOVERNMENT_BARCODE_LINE
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.PREFECTURE_BARCODE_LINE
import ai.friday.billpayment.integration.SANITATION_BARCODE_LINE
import ai.friday.billpayment.integration.TELECOM_BARCODE_LINE
import ai.friday.billpayment.integration.TRAFFIC_TICKET_BARCODE_LINE
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.invoiceIgnored
import ai.friday.billpayment.invoicePaid
import ai.friday.billpayment.invoicePaymentFailedNotRetryable
import ai.friday.billpayment.invoicePaymentFailedRetryable
import ai.friday.billpayment.invoicePaymentPostponed
import ai.friday.billpayment.invoicePaymentScheduled
import ai.friday.billpayment.invoicePaymentStarted
import ai.friday.billpayment.invoicePostponedByInsufficientFunds
import ai.friday.billpayment.invoiceReactivated
import ai.friday.billpayment.invoiceScheduleStarted
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.pixPaymentPostponedDailyLimitReached
import ai.friday.billpayment.pixPaymentScheduled
import ai.friday.billpayment.withEarlyAccess
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.timeFormat
import ai.friday.morning.date.withGivenDateTime
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.collections.shouldNotContain
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.mockk
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.stream.Stream
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource

class BillTest {

    private val zoneId: ZoneId = ZoneId.of("Brazil/East")
    private val billConcessionaria = getActiveBill(accountId = AccountId(ACCOUNT_ID)).copy(
        billType = BillType.CONCESSIONARIA,
        recipient = null,
        assignor = "CEG",
    )

    private val billFichaCompensacao = getActiveBill(
        accountId = AccountId(ACCOUNT_ID),
    ).copy(
        billType = BillType.FICHA_COMPENSACAO,
        recipient = null,
        assignor = "B2W",
    )

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val enhancedClient = DynamoDBUtils.setupDynamoDB()

    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(enhancedClient),
        refundedClient = RefundedBillDynamoDAO(enhancedClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(enhancedClient),
    )

    private val billEventDAO = BillEventDynamoDAO(enhancedClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)
    private val billEventRepository = BillEventDBRepository(
        billEventDAO = billEventDAO,
        uniqueConstraintDAO = uniqueConstraintDAO,
        featureConfiguration = mockk(),
        transactionDynamo = transactionDynamo,
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
    }

    @Test
    fun `should be overdue when bill is active and with an yesterday duedate`() {
        val bill = getActiveBill(accountId = AccountId(ACCOUNT_ID)).copy(
            paymentLimitTime = LocalTime.MIDNIGHT,
            dueDate = getLocalDate(),
            effectiveDueDate = getLocalDate().minusDays(1),
            status = BillStatus.ACTIVE,
        )
        assertTrue(bill.isOverdue)
    }

    @Test
    fun `should not be overdue if bill was already paid`() {
        val bill = getActiveBill(accountId = AccountId(ACCOUNT_ID)).copy(
            paymentLimitTime = LocalDateTime.now().minusHours(1).atZone(brazilTimeZone).toLocalTime(),
            dueDate = getLocalDate(),
            effectiveDueDate = getLocalDate(),
            status = BillStatus.PAID,
        )
        assertFalse(bill.isOverdue)
    }

    @Test
    fun `should be overdue after paymentLimitTime`() {
        withGivenDateTime(ZonedDateTime.of(getLocalDate().atTime(LocalTime.of(19, 59)), zoneId)) {
            val bill = getActiveBill(accountId = AccountId(ACCOUNT_ID)).copy(
                paymentLimitTime = LocalTime.parse("20:00", timeFormat),
                dueDate = getLocalDate().plusDays(1),
                effectiveDueDate = getLocalDate(),
                status = BillStatus.ACTIVE,
            )
            assertFalse(bill.isOverdue)
        }
        withGivenDateTime(ZonedDateTime.of(getLocalDate().atTime(LocalTime.of(20, 1)), zoneId)) {
            val bill = getActiveBill(accountId = AccountId(ACCOUNT_ID)).copy(
                paymentLimitTime = LocalTime.parse("20:00", timeFormat),
                dueDate = getLocalDate().plusDays(1),
                effectiveDueDate = getLocalDate(),
                status = BillStatus.ACTIVE,
            )
            assertTrue(bill.isOverdue)
        }
    }

    @Test
    fun getPayee_onConcessionaria() {
        assertEquals("CEG", billConcessionaria.payee)
    }

    @Test
    fun getPayee_onFichaCompensacacao() {
        assertEquals("B2W", billFichaCompensacao.payee)
    }

    @Test
    fun `boleto should be not payable when register is updated to not payable on a payment transaction`() {
        val bill = Bill.build(billAdded, billPaymentStart, billRegisterUpdatedNotPayable, billPaymentFailedRetryable)
        bill.status shouldBe BillStatus.NOT_PAYABLE
    }

    @Test
    fun `invoice should be active when payment fails but is retryable`() {
        val bill = Bill.build(invoiceAdded, invoicePaymentStarted, invoicePaymentFailedRetryable)
        bill.status shouldBe BillStatus.ACTIVE
    }

    @Test
    fun `invoice should be not payable when payment fails but is not retryable`() {
        val bill =
            Bill.build(invoiceAdded, invoicePaymentStarted, invoicePaymentFailedRetryable.copy(retryable = false))
        bill.status shouldBe BillStatus.NOT_PAYABLE
    }

    @Test
    fun `boleto should be active when register is updated to new amount on a payment transaction`() {
        val bill = Bill.build(billAdded, billPaymentStart, billRegisterUpdatedNewAmount, billPaymentFailedRetryable)
        bill.status shouldBe BillStatus.ACTIVE
        with(billRegisterUpdatedNewAmount.updatedRegisterData as UpdatedRegisterData.NewTotalAmount) {
            bill.amountTotal shouldBe amountTotal
            bill.amountCalculationValidUntil shouldBe amountCalculationValidUntil
        }
    }

    @Test
    fun `boleto should be active when register is updated to new payment data on a payment transaction`() {
        val bill = Bill.build(billAdded, billPaymentStart, billRegisterUpdatedNewAmount, billPaymentFailedRetryable)
        bill.status shouldBe BillStatus.ACTIVE
    }

    @Test
    fun `paid invoice should be active on a retryable payment fail`() {
        val bill = Bill.build(invoiceAdded, invoicePaymentStarted, invoicePaid, invoicePaymentFailedRetryable)
        bill.status shouldBe BillStatus.ACTIVE
        bill.amountPaid.shouldBeNull()
    }

    @Test
    fun `paid invoice should be not payable on a not retryable payment fail`() {
        val bill = Bill.build(
            invoiceAdded,
            invoicePaymentStarted,
            invoicePaid,
            invoicePaymentFailedRetryable.copy(retryable = false),
        )
        bill.status shouldBe BillStatus.NOT_PAYABLE
        bill.amountPaid.shouldBeNull()
    }

    @Test
    fun `paid invoice should not have paid date after a payment fail`() {
        val bill = Bill.build(invoiceAdded, invoicePaymentStarted, invoicePaid, invoicePaymentFailedRetryable)
        bill.paidDate shouldBe null
        bill.amountPaid.shouldBeNull()
    }

    @Test
    fun `paid invoice should have paid value equals to its total value`() {
        val bill = Bill.build(invoiceAdded, invoiceScheduleStarted, invoicePaymentStarted, invoicePaid)
        bill.paidDate.shouldNotBeNull()
        bill.amountPaid shouldBe invoiceAdded.amountTotal
    }

    @Test
    fun `invoice should be ignored on bill ignore`() {
        val bill = Bill.build(invoiceAdded, invoiceIgnored)
        bill.status shouldBe BillStatus.IGNORED
    }

    @Test
    fun `invoice should be active on bill reactivate`() {
        val bill = Bill.build(invoiceAdded, invoiceIgnored, invoiceReactivated)
        bill.status shouldBe BillStatus.ACTIVE
    }

    @Test
    fun `should have payment scheduled flag on payment scheduled event`() {
        with(Bill.build(invoiceAdded, invoicePaymentScheduled)) {
            assertSoftly {
                status shouldBe BillStatus.ACTIVE
                isIgnorable() shouldBe false
                schedule?.ongoing shouldBe false
                schedule?.waitingFunds shouldBe false
                schedule?.waitingRetry shouldBe false
                schedule?.date shouldBe invoicePaymentScheduled.scheduledDate
            }
        }
    }

    @Test
    fun `should resolve descriptor and soft descriptor`() {
        Bill.build(
            billAddedFicha.copy(
                recipient = Recipient("""Tésté        $1\ Éxcélentè ãmígõ"""),
                assignor = "ITAÚ",
            ),
        ).run {
            descriptor() shouldBe "TESTE 1 EXCELENTE AMIGO"
            softDescriptor() shouldBe "TESTE1EXCELEN"
            softDescriptor().length shouldBe 13

            softDescriptor(10).length shouldBe 10
            softDescriptor(10) shouldBe "TESTE1EXCE"
        }

        Bill.build(billAdded.copy(recipient = null, assignor = "ITAÚ UNIBANCO LTDA")).run {
            descriptor() shouldBe "ITAU UNIBANCO LTDA"
            softDescriptor() shouldBe "ITAUUNIBANCOL"
        }
    }

    @Nested
    inner class AmountCalculation {
        @Test
        fun `deve adicionar o tipo de calculo da bill como CALCULATE`() {
            val bill = Bill.build(
                billAddedFicha.copy(
                    amountCalculationModel = AmountCalculationModel.ANYONE,
                    actionSource = ActionSource.DDA(AccountId(ACCOUNT_ID)),
                    interestData = InterestData(
                        type = InterestType.VALUE,
                        date = LocalDate.now(),
                    ),
                ),
            )

            bill.amountCalculationOption shouldBe BillTrackingCalculateOptions.CALCULATE
            bill.amountCalculationValidUntil shouldNotBe null
        }

        @Test
        fun `deve adicionar o tipo de calculo da bill como QUERY`() {
            val bill = Bill.build(
                billAddedFicha.copy(
                    amountCalculationModel = AmountCalculationModel.BENEFICIARY_ONLY,
                    actionSource = ActionSource.DDA(AccountId(ACCOUNT_ID)),
                    interestData = InterestData(
                        type = InterestType.VALUE,
                        date = LocalDate.now(),
                    ),
                ),
            )

            bill.amountCalculationOption shouldBe BillTrackingCalculateOptions.QUERY
            bill.amountCalculationValidUntil shouldNotBe null
        }

        @Test
        fun `deve alterar o tipo de calculo da bill para QUERY`() {
            val bill = Bill.build(
                billAddedFicha.copy(
                    amountCalculationModel = AmountCalculationModel.ANYONE,
                    actionSource = ActionSource.DDA(AccountId(ACCOUNT_ID)),
                ),
            )

            bill.apply(
                RegisterUpdated(
                    billId = bill.billId,
                    walletId = bill.walletId,
                    updatedRegisterData = UpdatedRegisterData.NewAmountCalculationOption(
                        amountCalculationOption = BillTrackingCalculateOptions.QUERY,
                    ),
                    actionSource = ActionSource.System,
                ),
            )

            bill.amountCalculationOption shouldBe BillTrackingCalculateOptions.QUERY
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.bill.BillTest#billsWithoutAmountCalculationOption")
        fun `não deve adicionar o tipo de calculo da bill`(billEvent: BillEvent) {
            val bill = Bill.build(billEvent)

            bill.amountCalculationOption shouldBe null
            bill.amountCalculationValidUntil shouldBe null
        }
    }

    @Test
    fun `should be active on bill added`() {
        with(Bill.build(invoiceAdded)) {
            assertSoftly {
                status shouldBe BillStatus.ACTIVE
                isPaymentScheduled() shouldBe false
                schedule.shouldBeNull()
            }
        }
    }

    @Test
    fun `should cancel payment schedule of bill`() {
        with(
            Bill.build(
                billAdded,
                billPaymentScheduled,
                billPostponedByInsufficientFunds,
                billPaymentScheduleStarted,
                billScheduleCanceled,
            ),
        ) {
            assertSoftly {
                status shouldBe BillStatus.ACTIVE
                isPaymentScheduled() shouldBe false
                schedule.shouldBeNull()
                schedule.shouldBeNull()
            }
        }
    }

    @Test
    fun `should save events to history on apply`() {
        with(Bill.build(billAdded, billPaymentStart)) {
            apply(billPaymentFailedRetryable)
            assertSoftly {
                getWarningCode() shouldBe WarningCode.SERVER_ERROR
                history.size shouldBe 3
            }
        }
    }

    @Test
    fun `should not save event to history when isNew is false`() {
        with(Bill.build(billAdded, billPaymentStart)) {
            apply(billPaymentFailedRetryable, false)
            assertSoftly {
                getWarningCode() shouldBe WarningCode.NONE
                history.size shouldBe 2
            }
        }
    }

    @Test
    fun `wasPaidBySchedule should be true when bill paid action source is scheduled`() {
        with(
            Bill.build(
                billAdded,
                billPaymentScheduled,
                billPaid.copy(actionSource = ActionSource.Scheduled),
                billScheduleCanceled,
            ),
        ) {
            isPaidBySchedule() shouldBe true
        }
    }

    @ParameterizedTest
    @MethodSource("nonSchedulesActionSources")
    fun `wasPaidBySchedule should be true when bill paid action source is not scheduled`(actionSource: ActionSource) {
        with(
            Bill.build(
                billAdded,
                billPaymentScheduled,
                billPaid.copy(actionSource = actionSource),
                billScheduleCanceled,
            ),
        ) {
            isPaidBySchedule() shouldBe false
        }
    }

    /*@Test
    @Disabled("apenas para testar retrocompatibilidade dos campos novos do evento")
    fun `should deserialize legacy event format`() {
        val oldBillEventJSON =
            "{\"@class\":\"ai.friday.billpayment.app.bill.BillIgnored\",\"billId\":{\"value\":\"$BILL_ID\"},\"created\":*************,\"accountId\":{\"value\":\"ACCOUNT-4828265b-6fee-4101-8b3a-3aa039891499\"},\"actionSource\":{\"@type\":\"Recurrence\",\"recurrenceId\":{\"value\":\"fake\"},\"role\":\"OWNER\"},\"eventType\":\"IGNORED\"}"
        val billEvent = jacksonObjectMapper().readValue(oldBillEventJSON, BillIgnored::class.java)

        billEvent.removeFromListing shouldBe false
        billEvent.removeFromRecurrence shouldBe false
        (billEvent.actionSource as ActionSource.Recurrence).role shouldBe Role.OWNER
    }*/

    @Test
    fun `bill should be ongoing when it is selected for scheduled payment`() {
        with(Bill.build(billAdded, billPaymentScheduled, billPaymentScheduleStarted)) {
            this.schedule!!.ongoing shouldBe true
            this.schedule!!.waitingRetry shouldBe false
            this.schedule?.waitingFunds shouldBe false
            this.schedule!!.date shouldBe billPaymentScheduled.scheduledDate
        }
    }

    @Test
    fun `bill waiting funds should be false after bill payment schedule started`() {
        with(
            Bill.build(
                billAdded,
                billPaymentScheduled,
                invoicePostponedByInsufficientFunds,
                billPaymentScheduleStarted,
            ),
        ) {
            this.schedule?.ongoing shouldBe true
            this.schedule!!.waitingRetry shouldBe false
            this.schedule?.waitingFunds shouldBe false
        }
    }

    @Test
    fun `bill should be ongoing when it is selected for scheduled payment and payment started`() {
        with(Bill.build(billAdded, billPaymentScheduled, billPaymentScheduleStarted, billPaymentStart)) {
            this.schedule!!.waitingRetry shouldBe false
            this.schedule!!.ongoing shouldBe true
        }
    }

    @Test
    fun `bill should not be ongoing when it is selected for scheduled payment and payment succeeds`() {
        with(Bill.build(billAdded, billPaymentScheduled, billPaymentScheduleStarted, billPaymentStart, billPaid)) {
            this.schedule!!.waitingRetry shouldBe false
            this.schedule!!.ongoing shouldBe false
        }
    }

    @Test
    fun `bill should be ongoing when it is selected for scheduled payment and payment fails`() {
        with(
            Bill.build(
                billAdded,
                billPaymentScheduled,
                billPaymentScheduleStarted,
                billPaymentStart,
                billPaymentFailedRetryable,
            ),
        ) {
            this.schedule!!.waitingRetry shouldBe true
            this.schedule!!.ongoing shouldBe true
        }
    }

    @Test
    fun `bill should be ongoing when it is selected for scheduled payment but amount changed`() {
        with(
            Bill.build(
                billAdded,
                billPaymentScheduled,
                billPaymentScheduleStarted,
                billPaymentStart,
                billRegisterUpdatedNewAmount,
                billPaymentFailedRetryable,
            ),
        ) {
            this.schedule!!.waitingRetry shouldBe true
            this.schedule!!.ongoing shouldBe true
        }
    }

    @Test
    fun `bill should not be ongoing when it is selected for scheduled payment but is not payable`() {
        with(
            Bill.build(
                billAdded,
                billPaymentScheduled,
                billPaymentScheduleStarted,
                billPaymentStart,
                billRegisterUpdatedNotPayable,
            ),
        ) {
            this.schedule!!.waitingRetry shouldBe false
            this.schedule!!.ongoing shouldBe false
        }
    }

    @Test
    fun `bill should not be ongoing when it is selected for scheduled payment but is already paid`() {
        with(
            Bill.build(
                billAdded,
                billPaymentScheduled,
                billPaymentScheduleStarted,
                billPaymentStart,
                billRegisterUpdatedAlreadyPaid,
            ),
        ) {
            this.schedule!!.waitingRetry shouldBe false
            this.schedule!!.ongoing shouldBe false
        }
    }

    @Test
    fun `invoice should be ongoing when it is selected for scheduled payment and payment fails and is retryable`() {
        with(
            Bill.build(
                invoiceAdded,
                invoicePaymentScheduled,
                invoiceScheduleStarted,
                invoicePaymentStarted,
                invoicePaymentFailedRetryable,
            ),
        ) {
            this.schedule!!.waitingRetry shouldBe true
            this.schedule!!.ongoing shouldBe true
        }
    }

    @Test
    fun `invoice should not be ongoing and waiting funds when it fails and then postponed by insufficient funds`() {
        with(
            Bill.build(
                invoiceAdded,
                invoicePaymentScheduled,
                invoiceScheduleStarted,
                invoicePaymentStarted,
                invoicePaymentFailedRetryable,
                invoicePostponedByInsufficientFunds,
            ),
        ) {
            this.schedule!!.waitingRetry shouldBe false
            this.schedule!!.ongoing shouldBe false
            this.schedule!!.waitingFunds shouldBe true
        }
    }

    @Test
    fun `invoice should not be ongoing when it is selected for scheduled payment and payment fails and is not retryable`() {
        with(
            Bill.build(
                invoiceAdded,
                invoicePaymentScheduled,
                invoiceScheduleStarted,
                invoicePaymentStarted,
                invoicePaymentFailedNotRetryable,
            ),
        ) {
            this.schedule!!.waitingRetry shouldBe false
            this.schedule!!.ongoing shouldBe false
        }
    }

    @Test
    fun `bill should not be ongoing when it is rescheduled`() {
        with(
            Bill.build(
                billAdded,
                billPaymentScheduled,
                billPaymentScheduleStarted,
                billScheduleCanceled,
                billPaymentScheduled,
            ),
        ) {
            assertSoftly {
                with(this.schedule!!) {
                    waitingRetry shouldBe false
                    ongoing shouldBe false
                    date shouldBe billPaymentScheduled.scheduledDate
                }
            }
        }
    }

    @Test
    fun `bill should be postponed on PostponedByInsufficientFunds events`() {
        with(Bill.build(billAdded, billPaymentScheduled, billPostponedByInsufficientFunds)) {
            assertSoftly {
                with(this.schedule!!) {
                    ongoing shouldBe false
                    waitingRetry shouldBe false
                    waitingFunds shouldBe true
                    date shouldBe billPaymentScheduled.scheduledDate
                }
            }
        }
    }

    @Test
    fun `should not be waiting retry after payment success`() {
        with(
            Bill.build(
                billAdded,
                billPaymentScheduled,
                billPaymentScheduleStarted,
                billPaymentStart,
                billPaymentFailedRetryable,
                billPaid,
            ),
        ) {
            this.schedule!!.waitingRetry shouldBe false
            this.schedule!!.ongoing shouldBe false
        }
    }

    @Test
    fun `se um pagamento está postponed e vem uma falha de pagamento, não deve continuar no estado de esperando saldo`() {
        val bill = Bill.build(
            billAdded,
            billPaymentScheduled,
            billPaymentScheduleStarted,
            billPaymentStart,
            billPostponedByInsufficientFunds,
            billPaymentFailedRetryable,
        )

        bill.schedule!!.waitingRetry.shouldBeTrue()
        bill.schedule!!.waitingFunds.shouldBeFalse()
    }

    @Test
    fun `should change dueDate on RegisterUpdated with NewDueDate`() {
        with(Bill.build(billAdded, billIgnored, billReactivated, billRegisterUpdatedNewDueDate)) {
            this.dueDate shouldBe (billRegisterUpdatedNewDueDate.updatedRegisterData as UpdatedRegisterData.NewDueDate).dueDate
            this.effectiveDueDate shouldBe (billRegisterUpdatedNewDueDate.updatedRegisterData as UpdatedRegisterData.NewDueDate).effectiveDueDate
        }
    }

    @Test
    fun `should update schedule of pix date on postponed by daily limit reached`() {
        val now = ZonedDateTime.of(LocalDate.of(2021, 12, 30), LocalTime.of(12, 0), brazilTimeZone)

        val bill = Bill.build(
            pixAdded.copy(created = now.toInstant().toEpochMilli()),
            pixPaymentScheduled.copy(
                created = now.toInstant().toEpochMilli() + 1,
                scheduledDate = now.toLocalDate(),
            ),
            pixPaymentPostponedDailyLimitReached.copy(created = now.toInstant().toEpochMilli() + 2),
        )

        bill.schedule!!.date shouldBe now.toLocalDate().plusDays(1)
    }

    @Test
    fun `deve adicionar como não pagável uma conta com valor maior que 250mil`() {
        val bill = withEarlyAccess(AccountId(billAddedFicha.walletId.value)) {
            Bill.build(billAddedFicha.copy(amountTotal = MAX_PAYMENT_LIMIT_AMOUNT + 1L))
        }

        bill.status shouldBe BillStatus.NOT_PAYABLE
    }

    @Test
    fun `deve adicionar como não pagável uma conta com valor zerado`() {
        val bill = withEarlyAccess(AccountId(billAddedFicha.walletId.value)) {
            Bill.build(billAddedFicha.copy(amountTotal = 0L))
        }

        bill.status shouldBe BillStatus.NOT_PAYABLE
    }

    @Test
    fun `deve registrar external id de ficha compensacao`() {
        val bill = Bill.build(billAddedFicha.copy(externalId = ExternalBillId("EXTERNAL-ID", ExternalBillProvider.UTILITY_ACCOUNT)))

        bill.externalId shouldBe ExternalBillId("EXTERNAL-ID", ExternalBillProvider.UTILITY_ACCOUNT)
        bill.billType shouldBe BillType.FICHA_COMPENSACAO
    }

    @Test
    fun `deve registrar external id de bill`() {
        val bill = Bill.build(billAdded.copy(externalId = ExternalBillId("EXTERNAL-ID", ExternalBillProvider.UTILITY_ACCOUNT)))

        bill.externalId shouldBe ExternalBillId("EXTERNAL-ID", ExternalBillProvider.UTILITY_ACCOUNT)
        bill.billType shouldNotBe BillType.FICHA_COMPENSACAO
    }

    @Test
    fun `should update schedule of invoice date on postponed by daily limit reached`() {
        val now = ZonedDateTime.of(LocalDate.of(2021, 12, 31), LocalTime.of(12, 0), brazilTimeZone)

        val bill = Bill.build(
            invoiceAdded.copy(created = now.toInstant().toEpochMilli()),
            invoicePaymentScheduled.copy(
                created = now.toInstant().toEpochMilli() + 1,
                scheduledDate = now.toLocalDate(),
            ),
            invoicePaymentPostponed.copy(created = now.toInstant().toEpochMilli() + 2),
        )

        bill.schedule!!.date shouldBe LocalDate.of(2022, 1, 3)
    }

    @ParameterizedTest
    @CsvSource(
        "$PREFECTURE_BARCODE_LINE,PREFECTURE", // Adianta
        "$SANITATION_BARCODE_LINE,SANITATION", // Atrasa
        "$ELECTRICITY_AND_GAS_BARCODE_LINE,ELECTRICITY_AND_GAS", // Atrasa
        "$TELECOM_BARCODE_LINE,TELECOM", // Atrasa
        "$GOVERNMENT_BARCODE_LINE,GOVERNMENT", // Adianta
        "$CARNET_BARCODE_LINE,CARNET", // Atrasa
        "$TRAFFIC_TICKET_BARCODE_LINE,TRAFFIC_TICKET", // Atrasa
        "$FICHA_DE_COMPENSACAO_BARCODE,OTHERS", // Atrasa
    )
    fun `should return right segment for bar code`(barCodeNumber: String, billSegment: BillSegment) {
        val barcode = BarCode.of(barCodeNumber)
        getSegment(barcode) shouldBe billSegment
    }

    @ParameterizedTest
    @MethodSource("effectiveDateExpected")
    fun `should return right segment shift for bar code and bill type`(
        barcode: BarCode?,
        billType: BillType,
        dueDateShiftMode: DueDateShiftMode,
    ) {
        getSegmentShift(barcode, billType) shouldBe dueDateShiftMode
    }

    @Test
    fun `bill deve ser serializavel para json para nao quebrar o log`() {
        val bill = Bill.build(fichaCompensacaoCreditCardAdded)

        assertDoesNotThrow {
            getObjectMapper().writeValueAsString(bill)
        }
    }

    @Test
    fun `schedule status`() {
        val tomorrow = LocalDate.now().plusDays(1)

        val assert: (List<BillEvent>, String) -> Unit = { b, s ->
            Bill.build(*b.toTypedArray()).schedule?.getStatus() shouldBe s
        }

        var events = mutableListOf(billAdded, billPaymentScheduled.copy(scheduledDate = tomorrow))
        assert(events, "SCHEDULED")

        val now = LocalDate.now()

        events = mutableListOf(billAdded, billPaymentScheduled.copy(scheduledDate = now), billPaymentScheduleStarted)
        assert(events, "ON_GOING")

        events.add(billPaymentFailedRetryable)
        assert(events, "ON_GOING")

        events = mutableListOf(
            billAdded,
            billPaymentScheduled.copy(scheduledDate = now),
            billPaymentScheduleStarted,
            billPostponedByInsufficientFunds,
        )
        assert(events, "WAITING_FUNDS")

        events.add(billPaymentSchedulePostponedDueToLimitReached)
        assert(events, "SCHEDULED")

        events.add(billPaymentScheduleStarted)
        assert(events, "ON_GOING")

        events = mutableListOf(
            billAdded,
            billPaymentScheduled.copy(scheduledDate = now),
            billPaymentScheduleStarted,
            billPaymentStart,
            billPaymentFailed,
        )
        assert(events, "FAIL")
    }

    @Test
    fun `deve aplicar a tag na bill`() {
        val bill = Bill.build(billAdded)

        bill.apply(billTagAdded)

        bill.tags shouldContain BillTag.ONBOARDING_TEST_PIX
    }

    @Test
    fun `deve remover a tag da bill`() {
        val bill = Bill.build(billAdded, billTagAdded)

        bill.apply(billTagDeleted)

        bill.tags shouldNotContain BillTag.ONBOARDING_TEST_PIX
    }

    @Test
    fun `deve aplicar a categoria na bill`() {
        val bill = Bill.build(billAdded)

        bill.apply(billCategoryAdded)

        bill.categoryId shouldBe billCategoryAdded.categoryId
    }

    @Test
    fun `deve ignorar um pagamento não pagavel`() {
        val bill = Bill.build(billAdded, billRegisterUpdatedNotPayable)

        bill.apply(billIgnored)

        bill.status shouldBe BillStatus.IGNORED
        bill.ignoredAt.shouldNotBeNull()
    }

    @Test
    fun `deve deixar um pagamento não pagavel depois de designora-lo`() {
        val bill = Bill.build(billAdded, billRegisterUpdatedNotPayable, billIgnored)

        bill.apply(billReactivated)

        bill.status shouldBe BillStatus.NOT_PAYABLE
    }

    @Test
    fun `deve remover a categoria da bill`() {
        val bill = Bill.build(billAdded, billCategoryAdded)

        bill.apply(billCategoryDeleted)

        bill.categoryId shouldBe null
    }

    @Test
    fun `deve mudar o status para ACTIVE quando ocorrer um Refund`() {
        val bill = Bill.build(billAdded, billPaid)

        bill.apply(
            billRefunded.copy(
                reason = PaymentRefundedReason.ERROR,
            ),
        )

        bill.status shouldBe BillStatus.ACTIVE
        bill.paidDate.shouldBeNull()
        bill.amountPaid.shouldBeNull()
        bill.refundedAt shouldBe billRefunded.created
    }

    @ParameterizedTest
    @EnumSource(PaymentRefundedReason::class, mode = EnumSource.Mode.EXCLUDE, names = ["ERROR"])
    fun `deve mudar o status para NOT_PAYABLE quando ocorrer um Refund`(reason: PaymentRefundedReason) {
        val bill = Bill.build(billAdded, billPaid)

        billEventRepository.save(billAdded)
        billEventRepository.save(billPaid)
        billRepository.save(bill)

        val paymentRefunded = billRefunded.copy(
            reason = reason,
        )
        billEventRepository.save(paymentRefunded)

        bill.apply(paymentRefunded)

        billRepository.save(bill)

        with(billEventRepository.getBillById(bill.billId).getOrNull()!!) {
            status shouldBe BillStatus.NOT_PAYABLE
            paidDate.shouldBeNull()
            amountPaid.shouldBeNull()
            refundedAt shouldBe billRefunded.created
        }

        with(billRepository.findBill(bill.billId, bill.walletId)) {
            status shouldBe BillStatus.NOT_PAYABLE
            paidDate.shouldBeNull()
            amountPaid.shouldBeNull()
            refundedAt shouldBe Instant.ofEpochMilli(billRefunded.created).atZone(brazilTimeZone).toLocalDateTime().withNano(0)
        }
    }

    @Test
    fun `deve gravar e ler corretamente uma bill de investimento`() {
        val bill = Bill.build(billAddedInvestment)

        billEventRepository.save(billAddedInvestment)
        billRepository.save(bill)

        with(billEventRepository.getBillById(bill.billId).getOrNull()!!) {
            status shouldBe BillStatus.ACTIVE
            paidDate.shouldBeNull()
            amountPaid.shouldBeNull()
            goalId shouldBe billAddedInvestment.goalId
            val actionSource = source
            actionSource.shouldBeTypeOf<ActionSource.GoalInvestment>()
            actionSource.goalId shouldBe billAddedInvestment.goalId
        }

        with(billRepository.findBill(bill.billId, bill.walletId)) {
            status shouldBe BillStatus.ACTIVE
            paidDate.shouldBeNull()
            amountPaid.shouldBeNull()
            goalId shouldBe billAddedInvestment.goalId
            val actionSource = source
            actionSource.shouldBeTypeOf<ActionSource.GoalInvestment>()
            actionSource.goalId shouldBe billAddedInvestment.goalId
        }
    }

    companion object {
        @JvmStatic
        fun effectiveDateExpected(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(BarCode.of(PREFECTURE_BARCODE_LINE), BillType.CONCESSIONARIA, DueDateShiftMode.Decrease),
                Arguments.of(BarCode.of(GOVERNMENT_BARCODE_LINE), BillType.CONCESSIONARIA, DueDateShiftMode.Decrease),
                Arguments.of(BarCode.of(SANITATION_BARCODE_LINE), BillType.CONCESSIONARIA, DueDateShiftMode.Increase),
                Arguments.of(
                    BarCode.of(ELECTRICITY_AND_GAS_BARCODE_LINE),
                    BillType.CONCESSIONARIA,
                    DueDateShiftMode.Increase,
                ),
                Arguments.of(BarCode.of(TELECOM_BARCODE_LINE), BillType.CONCESSIONARIA, DueDateShiftMode.Increase),
                Arguments.of(BarCode.of(CARNET_BARCODE_LINE), BillType.CONCESSIONARIA, DueDateShiftMode.Increase),
                Arguments.of(
                    BarCode.of(TRAFFIC_TICKET_BARCODE_LINE),
                    BillType.CONCESSIONARIA,
                    DueDateShiftMode.Increase,
                ),
                Arguments.of(
                    BarCode.of(FICHA_DE_COMPENSACAO_BARCODE),
                    BillType.CONCESSIONARIA,
                    DueDateShiftMode.Increase,
                ),
                Arguments.of(null, BillType.INVOICE, DueDateShiftMode.Increase),
                Arguments.of(null, BillType.PIX, DueDateShiftMode.None),
            )
        }

        @JvmStatic
        fun nonSchedulesActionSources(): Stream<ActionSource> {
            return Stream.of(
                ActionSource.DDA(accountId = AccountId(ACCOUNT_ID)),
                ActionSource.System,
                ActionSource.WalletMailBox(from = EMAIL),
                ActionSource.Api(accountId = AccountId(ACCOUNT_ID)),
            )
        }

        @JvmStatic
        fun billsWithoutAmountCalculationOption(): Stream<BillEvent> {
            return Stream.of(
                billAdded,
                pixAdded,
                invoiceAdded,
                billAddedFicha.copy(actionSource = ActionSource.DDA(accountId = AccountId(ACCOUNT_ID))),
            )
        }
    }
}