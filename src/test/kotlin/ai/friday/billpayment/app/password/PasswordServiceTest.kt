package ai.friday.billpayment.app.password

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.IssuedToken
import ai.friday.billpayment.app.account.TokenService
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.liveness.LivenessService
import ai.friday.billpayment.integration.DOCUMENT
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.Duration
import org.junit.jupiter.api.Test

class PasswordServiceTest {

    private val livenessService: LivenessService = mockk {
        every {
            hasCompletedEnrollment(any())
        } returns true.right()
    }

    private val accountService: AccountService = mockk {
        every {
            findAccountByDocumentOrNull(any())
        } returns ACCOUNT
    }

    private val userPoolAdapter: UserPoolAdapter = mockk {
        every {
            doesUserExist(ACCOUNT.document)
        } returns true
    }

    private val mockDuration = Duration.ofSeconds(2)
    private val tokenService: TokenService = mockk {
        every {
            issuePasswordRecoveryToken(
                email = ACCOUNT.emailAddress,
                accountId = ACCOUNT.accountId,
                document = ACCOUNT.document,
            )
        } answers { IssuedToken(duration = mockDuration, accountId = secondArg(), cooldown = Duration.ofSeconds(1)).right() }
    }

    private val passwordService = PasswordService(
        livenessService = livenessService,
        accountService = accountService,
        userPoolAdapter = userPoolAdapter,
        tokenService = tokenService,
    )

    @Test
    fun `deve falhar se o document não estiver associado a um account`() {
        every {
            accountService.findAccountByDocumentOrNull(any())
        } returns null

        val result = passwordService.createOTP(DOCUMENT)

        result.isLeft() shouldBe true
        result.mapLeft { it.shouldBeTypeOf<PasswordErrors.AccountNotFound>() }

        verify {
            tokenService wasNot Called
        }
    }

    @Test
    fun `deve falhar se o document for de um account que não possui senha`() {
        every {
            userPoolAdapter.doesUserExist(ACCOUNT.document)
        } returns false

        val result = passwordService.createOTP(DOCUMENT)

        result.isLeft() shouldBe true
        result.mapLeft { it.shouldBeTypeOf<PasswordErrors.AccountWithoutPassword>() }

        verify {
            tokenService wasNot Called
        }
    }

    @Test
    fun `deve falhar se o account associado não tiver um liveness`() {
        every {
            livenessService.hasCompletedEnrollment(any())
        } returns false.right()

        val result = passwordService.createOTP(DOCUMENT)

        result.isLeft() shouldBe true
        result.mapLeft { it.shouldBeTypeOf<PasswordErrors.CantUseLiveness>() }

        verify {
            tokenService wasNot Called
        }
    }

    @Test
    fun `deve gerar o token para email quando o document estiver relacionado a um account ativo`() {
        val result = passwordService.createOTP(DOCUMENT)

        result.isRight() shouldBe true
        result.map {
            it.duration shouldBe mockDuration
        }

        verify {
            tokenService.issuePasswordRecoveryToken(
                email = ACCOUNT.emailAddress,
                accountId = ACCOUNT.accountId,
                document = ACCOUNT.document,
            )
        }
    }
}