package ai.friday.billpayment.app.notification

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.NotificationService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billScheduleCanceled
import ai.friday.billpayment.billScheduleCanceledAfterHours
import ai.friday.billpayment.concessionariaBill
import ai.friday.billpayment.integration.WalletFixture
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Called
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import io.via1.communicationcentre.app.notification.NotificationFormatter
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class ExpiredBillScheduleNotificationServiceTest {

    private val walletService: WalletService = mockk(relaxed = true)

    private val billEventRepository: BillEventRepository = mockk()

    private val notificationService: NotificationService = mockk(relaxed = true)

    private val service = ExpiredBillScheduleNotificationService(
        walletService = walletService,
        billEventRepository = billEventRepository,
        notificationService = notificationService,
        templatesConfiguration = mockk() {
            every {
                whatsappTemplates
            } returns mockk() {
                every {
                    walletApprovedPaymentCancelled
                } returns "walletApprovedPaymentCancelled"
            }
        },
    )

    private val walletFixture = WalletFixture()

    private val wallet = walletFixture.buildWallet(
        otherMembers = listOf(
            walletFixture.participant,
            walletFixture.ultraLimitedParticipant,
        ),
    )

    @Test
    fun `deve lancar exception se nao encontrar a bill`() {
        every {
            billEventRepository.getBillById(any())
        } returns NoStackTraceException("NotFound").left()

        assertThrows<IllegalStateException> {
            service.notify(billScheduleCanceledAfterHours)
        }

        verify {
            walletService wasNot Called
            notificationService wasNot Called
        }
    }

    @ParameterizedTest
    @EnumSource(value = ScheduleCanceledReason::class, mode = EnumSource.Mode.EXCLUDE, names = ["EXPIRATION"])
    fun `nao deve enviar a notificacao quando o motivo for diferente de EXPIRATION`(reason: ScheduleCanceledReason) {
        val event = billScheduleCanceled.copy(
            reason = reason,
        )

        service.notify(event)

        verify {
            notificationService wasNot called
            billEventRepository wasNot called
            walletService wasNot called
        }
    }

    @Test
    fun `deve enviar a notificacao quando o motivo for EXPIRATION`() {
        val bill = Bill.build(billAdded, billPaymentScheduled, billScheduleCanceledAfterHours)

        every {
            billEventRepository.getBillById(any())
        } returns bill.right()

        every {
            walletService.findWallet(any())
        } returns wallet

        service.notify(billScheduleCanceledAfterHours)

        val slot = slot<(Account) -> BillPaymentNotification?>()

        verify {
            notificationService.notifyMembers(
                listOf(walletFixture.founder, walletFixture.participant),
                NotificationType.BILL_SCHEDULE_EXPIRED,
                capture(slot),
            )
        }

        val result = slot.captured(walletFixture.founderAccount)

        with(result.shouldBeTypeOf<WhatsappNotification>()) {
            template shouldBe NotificationTemplate("walletApprovedPaymentCancelled")
            receiver shouldBe MobilePhone("+*************")
            buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("contas/carteira/${wallet.id.value}/bill-timeline-entry@BILL-********-08ee-427c-9521-e33f68021839")
            parameters shouldBe listOf(
                "CLARO NET",
                NotificationFormatter.buildFormattedDueDate(concessionariaBill.effectiveDueDate),
                "R\$ 155,91",
                "carteira",
            )
        }
    }
}