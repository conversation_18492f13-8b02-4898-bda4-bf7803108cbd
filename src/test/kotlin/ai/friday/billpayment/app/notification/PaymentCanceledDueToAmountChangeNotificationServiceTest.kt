package ai.friday.billpayment.app.notification

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.NotificationService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billRegisterUpdatedNewAmount
import ai.friday.billpayment.billScheduleCanceled
import ai.friday.billpayment.billScheduleCanceledAmountChanged
import ai.friday.billpayment.integration.WalletFixture
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Called
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class PaymentCanceledDueToAmountChangeNotificationServiceTest {

    private val walletService: WalletService = mockk(relaxed = true)

    private val billEventRepository: BillEventRepository = mockk()

    private val notificationService: NotificationService = mockk(relaxed = true)

    private val service = PaymentCanceledDueToAmountChangeNotificationService(
        walletService = walletService,
        billEventRepository = billEventRepository,
        notificationService = notificationService,
        templatesConfiguration = mockk() {
            every {
                whatsappTemplates
            } returns mockk() {
                every {
                    walletBillScheduleCanceledDueAmountChanged
                } returns "walletBillScheduleCanceledDueAmountChanged"
            }
        },
    )

    private val walletFixture = WalletFixture()

    private val wallet = walletFixture.buildWallet(
        otherMembers = listOf(
            walletFixture.participant,
            walletFixture.ultraLimitedParticipant,
        ),
    )

    @Test
    fun `deve lancar exception se nao encontrar a bill`() {
        every {
            billEventRepository.getBillById(any())
        } returns NoStackTraceException("NotFound").left()

        assertThrows<IllegalStateException> {
            service.notify(billScheduleCanceledAmountChanged)
        }

        verify {
            walletService wasNot Called
            notificationService wasNot Called
        }
    }

    @ParameterizedTest
    @EnumSource(value = ScheduleCanceledReason::class, mode = EnumSource.Mode.EXCLUDE, names = ["BILL_AMOUNT_CHANGED"])
    fun `nao deve enviar a notificacao quando o motivo for diferente de BILL_AMOUNT_CHANGED`(reason: ScheduleCanceledReason) {
        val event = billScheduleCanceled.copy(
            reason = reason,
        )

        service.notify(event)

        verify {
            notificationService wasNot called
            billEventRepository wasNot called
            walletService wasNot called
        }
    }

    @Test
    fun `deve enviar a notificacao quando o motivo for BILL_AMOUNT_CHANGED`() {
        val bill = Bill.build(billAdded, billPaymentScheduled, billScheduleCanceledAmountChanged, billRegisterUpdatedNewAmount)

        every {
            billEventRepository.getBillById(any())
        } returns bill.right()

        every {
            walletService.findWallet(any())
        } returns wallet

        service.notify(billScheduleCanceledAmountChanged)

        val slot = slot<(Account) -> BillPaymentNotification?>()

        verify {
            notificationService.notifyMembers(
                listOf(walletFixture.founder, walletFixture.participant),
                NotificationType.BILL_SCHEDULE_CANCELED_DUE_AMOUNT_CHANGED,
                capture(slot),
            )
        }

        val result = slot.captured(walletFixture.founderAccount)

        with(result.shouldBeTypeOf<WhatsappNotification>()) {
            template shouldBe NotificationTemplate("walletBillScheduleCanceledDueAmountChanged")
            receiver shouldBe MobilePhone("+*************")
            buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("contas/carteira/${bill.walletId.value}/bill-timeline-entry@BILL-********-08ee-427c-9521-e33f68021839")
            parameters shouldBe listOf(
                "CLARO NET",
                "R\$ 155,91",
                "R\$ 0,10",
            )
        }
    }
}