package ai.friday.billpayment.app.notification

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import io.kotest.matchers.shouldBe
import io.mockk.mockk
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class DefaultShouldNotifyUserTest {
    val featureConfiguration = mockk<FeatureConfiguration>(relaxed = true)
    val service = DefaultShouldNotifyUser(featureConfiguration = featureConfiguration)
    val account = ACCOUNT

    @ParameterizedTest
    @EnumSource(NotificationType::class)
    fun `deve enviar notificações mesmo se o usuário estiver bloqueado`(type: NotificationType) {
        val result = service.shouldNotify(account.copy(status = AccountStatus.BLOCKED), type = type)

        result.shouldBe(true)
    }
}