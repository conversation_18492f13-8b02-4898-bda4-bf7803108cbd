package ai.friday.billpayment.app.notification

import ai.friday.billpayment.app.MODATTA_ENV
import io.kotest.matchers.nulls.shouldNotBeNull
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import org.junit.jupiter.api.Test

@MicronautTest(environments = [MODATTA_ENV])
class ModattaDefaultNotificationRouterServiceIntegrationTest(private val notificationRouterService: DefaultNotificationRouterService) {
    @Test
    fun `should send an email not processed notification to the right service bean`() {
        notificationRouterService.shouldNotBeNull()
    }
}