package ai.friday.billpayment.app.notification

import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.integrations.NotificationService
import ai.friday.billpayment.integration.WalletFixture
import io.kotest.matchers.collections.shouldContainInOrder
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Test

internal class InsecureBillNotificationServiceTest {

    private val notificationService = mockk<NotificationService>(relaxed = true)

    private val service = InsecureBillNotificationService(
        notificationService = notificationService,
        templatesConfiguration = mockk() {
            every {
                whatsappTemplates
            } returns mockk() {
                every {
                    mailboxBillInsecure
                } returns "mailboxBillInsecure"
            }
        },
    )

    private val walletFixture = WalletFixture()

    @Test
    fun `should notify members`() {
        val members = listOf(walletFixture.founder)
        service.notify(
            InsecureBillNotificationRequest(
                members = members,
                subject = "Insecure Mail",
            ),
        )
        val slot = slot<(account: Account) -> BillPaymentNotification?>()
        verify {
            notificationService.notifyMembers(members, NotificationType.INSECURE_BILL, capture(slot))
        }
        val notification = slot.captured(walletFixture.founderAccount).shouldBeTypeOf<WhatsappNotification>()
        notification.receiver shouldBe MobilePhone(walletFixture.founderAccount.mobilePhone)
        notification.parameters.shouldContainInOrder(
            "Insecure Mail",
        )
    }
}