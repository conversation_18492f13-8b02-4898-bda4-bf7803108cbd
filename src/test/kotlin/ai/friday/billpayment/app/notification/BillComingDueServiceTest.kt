package ai.friday.billpayment.app.notification

import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.ChatbotType
import ai.friday.billpayment.app.bill.BatchSchedulingId
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillTag
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.BillViewSchedule
import ai.friday.billpayment.app.bill.schedule.ScheduleBillService
import ai.friday.billpayment.app.chatbot.ChatbotNotificationService
import ai.friday.billpayment.app.integrations.BillService
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.OnePixPayInstrumentation
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.payment.ScheduleTo
import ai.friday.billpayment.app.payment.ScheduledBill
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.createPaymentMethodsDetailWithBalance
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.CNPJ_2
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.collections.shouldContainExactly
import io.mockk.Runs
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class BillComingDueServiceTest {

    private val findBillService = mockk<FindBillService>()
    private val billService = mockk<BillService>()
    private val walletService = mockk<WalletService>()
    private val notificationAdapter = mockk<NotificationAdapter>(relaxed = true)
    private val notificationHintService = mockk<NotificationHintService>()
    private val accountService = mockk<AccountService>()
    private val onePixPayService = mockk<OnePixPayService>()
    private val onePixPayInstrumentation = mockk<OnePixPayInstrumentation>(relaxed = true)
    private val scheduleBillService = mockk<ScheduleBillService>()
    private val onePixPayLock = mockk<InternalLock>()
    private val messagePublisher = mockk<MessagePublisher>()
    private val chatBotNotificationService = mockk<ChatbotNotificationService>()

    val service = BillComingDueService(
        findBillService = findBillService,
        billService = billService,
        walletService = walletService,
        notificationAdapter = notificationAdapter,
        notificationHintService = notificationHintService,
        accountService = accountService,
        onePixPayService = onePixPayService,
        onePixPayInstrumentation = onePixPayInstrumentation,
        scheduleBillService = scheduleBillService,
        onePixPayLock = onePixPayLock,
        messagePublisher = messagePublisher,
        chatBotNotificationService = chatBotNotificationService,
        billComingDueExecutionQueueName = "billComingDueExecutionQueueName",
    )

    val walletFixture = WalletFixture()
    val founderAccount = walletFixture.founderAccount
    val wallet = walletFixture.buildWallet()
    val today = getLocalDate()
    val pjWallet = walletFixture.buildPJWallet()
    val pjFounderAccount = walletFixture.pjFounderAccount

    val dueDateTodayBill = getActiveBill(walletId = wallet.id, billId = BillId(), dueDate = today, effectiveDueDate = today, paymentLimitTime = "23:59")

    val overdueScheduledBill = getActiveBill(walletId = wallet.id, billId = BillId(), dueDate = today.minusDays(3), effectiveDueDate = today.minusDays(3), paymentLimitTime = "23:59", schedule = BillViewSchedule(date = today.minusDays(3)))
    val overdueScheduledBillPaymentScheduledInfo = ScheduledBill(

        overdueScheduledBill.walletId,
        overdueScheduledBill.billId,
        overdueScheduledBill.dueDate,
        overdueScheduledBill.billType,
        overdueScheduledBill.amountTotal,
        ScheduleTo.ASAP,
        overdueScheduledBill.paymentLimitTime,
        expires = true,
        paymentMethodsDetail = createPaymentMethodsDetailWithBalance(wallet.paymentMethodId, billAdded.amountTotal),
        batchSchedulingId = BatchSchedulingId(),
        isSelfTransfer = false,
    )

    val overdueBill = getActiveBill(walletId = wallet.id, billId = BillId(), dueDate = today.minusDays(3), effectiveDueDate = today.minusDays(3), paymentLimitTime = "23:59")

    val futureScheduledBillForToday = getActiveBill(walletId = wallet.id, billId = BillId(), dueDate = today.plusDays(3), effectiveDueDate = today.plusDays(3), paymentLimitTime = "23:59", schedule = BillViewSchedule(date = today))
    val futureScheduledBillForTodayPaymentScheduledInfo = ScheduledBill(
        futureScheduledBillForToday.walletId,
        futureScheduledBillForToday.billId,
        futureScheduledBillForToday.dueDate,
        futureScheduledBillForToday.billType,
        futureScheduledBillForToday.amountTotal,
        ScheduleTo.ASAP,
        futureScheduledBillForToday.paymentLimitTime,
        expires = true,
        paymentMethodsDetail = createPaymentMethodsDetailWithBalance(wallet.paymentMethodId, billAdded.amountTotal),
        batchSchedulingId = BatchSchedulingId(),
        isSelfTransfer = false,
    )

    val scheduledBillForToday = getActiveBill(walletId = wallet.id, billId = BillId(), dueDate = today, effectiveDueDate = today, paymentLimitTime = "23:59", schedule = BillViewSchedule(date = today))
    val scheduledBillForTodayWaitingFunds = getActiveBill(walletId = wallet.id, billId = BillId(), dueDate = today, effectiveDueDate = today, paymentLimitTime = "23:59", schedule = BillViewSchedule(date = today, waitingFunds = true))

    @BeforeEach
    fun init() {
        clearAllMocks()
        every {
            findBillService.findBillsComingDue(any())
        } returns emptyList()

        every {
            findBillService.findOverdueBills(any())
        } returns emptyList()

        every {
            scheduleBillService.getOrderedScheduledBills(any(), today)
        } returns emptyList()

        every { chatBotNotificationService.notifyBillComingDue(any(), any(), any(), any(), any()) } just Runs
    }

    @Test
    fun `deve retornar as contas que vencem hoje`() {
        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns listOf(dueDateTodayBill)

        val foundBills = service.findBillsComingDueToNotify(wallet.id)
        foundBills.notScheduledBillsDueToday shouldContainExactly listOf(dueDateTodayBill)
        foundBills.todayBills shouldContainExactly listOf(dueDateTodayBill)
    }

    @Test
    fun `deve notificar as contas vencendo hoje quando executar o 'last-warning'`() {
        every { accountService.findAccountById(any()) } returns founderAccount
        every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHATBOT_LEGACY
        every { walletService.findWallet(any()) } returns wallet
        every { notificationHintService.getBillComingDueLastWarn(any(), any()) } returns "hint"
        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns listOf(dueDateTodayBill, dueDateTodayBill.copy(paymentLimitTime = LocalTime.parse("22:00")))

        withGivenDateTime(getZonedDateTime().withHour(20).withMinute(10)) {
            service.executeLastWarn(wallet.id)
        }

        verify {
            notificationAdapter.notifyBillComingDueLastWarn(
                members = any(),
                wallet = any(),
                dueDate = any(),
                paymentLimitTime = any(),
                hint = any(),
                bills = any(),
            )
        }
    }

    @Test
    fun `deve notificar as contas vencendo hoje quando executar o último 'last-warning' do dia`() {
        every { accountService.findAccountById(any()) } returns founderAccount
        every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHATBOT_LEGACY
        every { walletService.findWallet(any()) } returns wallet
        every { notificationHintService.getBillComingDueLastWarn(any(), any()) } returns "hint"
        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns listOf(dueDateTodayBill.copy(paymentLimitTime = LocalTime.parse("21:00")))

        withGivenDateTime(getZonedDateTime().withHour(19).withMinute(10)) {
            service.executeLastWarnRestOfDay(wallet.id)
        }

        verify {
            notificationAdapter.notifyBillComingDueLastWarn(
                members = any(),
                wallet = any(),
                dueDate = any(),
                paymentLimitTime = any(),
                hint = any(),
                bills = any(),
            )
        }
    }

    @Test
    fun `deve notificar as contas vencendo até o final do dia quando executar o último 'last-warning' do dia`() {
        every { accountService.findAccountById(any()) } returns founderAccount
        every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHATBOT_LEGACY
        every { walletService.findWallet(any()) } returns wallet
        every { notificationHintService.getBillComingDueLastWarn(any(), any()) } returns "hint"
        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns listOf(dueDateTodayBill.copy(paymentLimitTime = LocalTime.parse("23:59")))

        withGivenDateTime(getZonedDateTime().withHour(19).withMinute(10)) {
            service.executeLastWarnRestOfDay(wallet.id)
        }

        verify {
            notificationAdapter.notifyBillComingDueLastWarn(
                members = any(),
                wallet = any(),
                dueDate = any(),
                paymentLimitTime = any(),
                hint = any(),
                bills = any(),
            )
        }
    }

    @Test
    fun `não deve notificar as contas antes do offset quando executar o último 'last-warning' do dia`() {
        every { accountService.findAccountById(any()) } returns founderAccount
        every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHATBOT_LEGACY
        every { walletService.findWallet(any()) } returns wallet
        every { notificationHintService.getBillComingDueLastWarn(any(), any()) } returns "hint"
        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns listOf(dueDateTodayBill.copy(paymentLimitTime = LocalTime.parse("20:58")))

        withGivenDateTime(getZonedDateTime().withHour(19).withMinute(10)) {
            service.executeLastWarnRestOfDay(wallet.id)
        }

        verify(exactly = 0) {
            notificationAdapter.notifyBillComingDueLastWarn(
                members = any(),
                wallet = any(),
                dueDate = any(),
                paymentLimitTime = any(),
                hint = any(),
                bills = any(),
            )
        }
    }

    @Test
    fun `deve notificar as contas vencendo hoje quando founder for PJ`() {
        every { accountService.findAccountById(any()) } returns pjFounderAccount
        every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHABOT_AI
        every { walletService.findWallet(any()) } returns pjWallet
        every { notificationHintService.getBillComingDueLastWarn(any(), any()) } returns "hint"
        every {
            findBillService.findBillsComingDue(pjWallet.id)
        } returns listOf(dueDateTodayBill, dueDateTodayBill.copy(paymentLimitTime = LocalTime.parse("22:00")))

        withGivenDateTime(getZonedDateTime().withHour(20).withMinute(10)) {
            service.executeChatBotAiRegular(pjWallet.id)
        }

        verify {
            notificationAdapter.notifyBillComingDueSecondaryWallet(
                members = any(),
                wallet = any(),
                bills = any(),
            )
        }
    }

    @Test
    fun `deve notificar último aviso quando founder for PJ`() {
        every { accountService.findAccountById(any()) } returns pjFounderAccount
        every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHABOT_AI
        every { walletService.findWallet(any()) } returns pjWallet
        every { notificationHintService.getBillComingDueLastWarn(any(), any()) } returns "hint"
        every {
            findBillService.findBillsComingDue(pjWallet.id)
        } returns listOf(dueDateTodayBill, dueDateTodayBill.copy(paymentLimitTime = LocalTime.parse("22:00")))

        withGivenDateTime(getZonedDateTime().withHour(20).withMinute(10)) {
            service.executeLastWarn(pjWallet.id)
        }

        verify {
            notificationAdapter.notifyBillComingDueLastWarn(
                members = any(),
                wallet = any(),
                dueDate = any(),
                paymentLimitTime = any(),
                hint = any(),
                bills = any(),
            )
        }
    }

    @Test
    fun `deve retornar as contas vencidas que estejam agendadas`() {
        every {
            scheduleBillService.getOrderedScheduledBills(wallet.id, today)
        } returns listOf(overdueScheduledBillPaymentScheduledInfo)

        every {
            findBillService.find(wallet.id, overdueScheduledBillPaymentScheduledInfo.billId)
        } returns overdueScheduledBill

        val foundBills = service.findBillsComingDueToNotify(wallet.id)
        foundBills.userScheduledBills shouldContainExactly listOf(overdueScheduledBill)
        foundBills.todayBills shouldContainExactly listOf(overdueScheduledBill)
    }

    @Test
    fun `deve retornar as contas futuras agendadas`() {
        every {
            scheduleBillService.getOrderedScheduledBills(wallet.id, today)
        } returns listOf(futureScheduledBillForTodayPaymentScheduledInfo)

        every {
            findBillService.find(wallet.id, futureScheduledBillForTodayPaymentScheduledInfo.billId)
        } returns futureScheduledBillForToday

        val foundBills = service.findBillsComingDueToNotify(wallet.id)
        foundBills.userScheduledBills shouldContainExactly listOf(futureScheduledBillForToday)
        foundBills.todayBills shouldContainExactly listOf(futureScheduledBillForToday)
    }

    @Test
    fun `deve retornar contas vencida que não está agendada na lista overdueNotScheduledBills`() {
        every {
            findBillService.findOverdueBills(wallet.id)
        } returns listOf(overdueBill)

        val foundBills = service.findBillsComingDueToNotify(wallet.id)
        foundBills.overdueNotScheduledBills shouldContainExactly listOf(overdueBill)
    }

    @Test
    fun `chatbot deve notificar se pelo menos uma conta não estiver agendada`() {
        every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHABOT_AI
        every { accountService.findAccountById(any()) } returns founderAccount
        every { walletService.findWallet(any()) } returns wallet
        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns listOf(scheduledBillForToday, dueDateTodayBill)

        service.executeChatBotAiRegular(wallet.id)

        verify { chatBotNotificationService.notifyBillComingDue(any(), any(), any(), any(), any()) }
    }

    @Test
    fun `deve notificar contas vencendo para um usuário cofounder de carteira secundária`() {
        val founder = walletFixture.founderAccount
        val cofounder = walletFixture.cofounderAccount
        val secondaryWallet = walletFixture.buildSecondaryWallet(founderAccount = founder, coFounderAccount = cofounder)
        every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHABOT_AI
        every { accountService.findAccountById(any()) } returns cofounder
        every { walletService.findWallet(any()) } returns secondaryWallet
        every {
            findBillService.findBillsComingDue(secondaryWallet.id)
        } returns listOf(dueDateTodayBill)

        service.executeChatBotAiRegular(secondaryWallet.id)

        verify { notificationAdapter.notifyBillComingDueSecondaryWallet(secondaryWallet.activeMembers, any(), any()) }
    }

    @Test
    fun `não deve notificar contas vencendo de carteira secundária para um usuário que não possa agendar contas`() {
        val founder = walletFixture.founderAccount
        val cofounder = walletFixture.cofounderAccount
        val cantPayMember = walletFixture.cantPayParticipant
        val secondaryWallet = walletFixture.buildSecondaryWallet(founderAccount = founder, coFounderAccount = cofounder, otherMembers = listOf(cantPayMember))
        every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHABOT_AI
        every { accountService.findAccountById(any()) } returns founder
        every { walletService.findWallet(any()) } returns secondaryWallet
        every {
            findBillService.findBillsComingDue(secondaryWallet.id)
        } returns listOf(dueDateTodayBill)

        service.executeChatBotAiRegular(secondaryWallet.id)

        verify { notificationAdapter.notifyBillComingDueSecondaryWallet(secondaryWallet.activeMembers - cantPayMember, any(), any()) }
    }

    @Test
    fun `chatbot não deve notificar contas agendadas que não estejam aguardando saldo`() {
        every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHABOT_AI
        every { accountService.findAccountById(any()) } returns founderAccount
        every { walletService.findWallet(any()) } returns wallet
        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns listOf(scheduledBillForToday)

        service.executeChatBotAiRegular(wallet.id)

        verify(exactly = 0) { chatBotNotificationService.notifyBillComingDue(any(), any(), any(), any(), any()) }
    }

    @Test
    fun `chatbot não deve notificar contas PJ`() {
        every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHABOT_AI
        every { accountService.findAccountById(any()) } returns founderAccount.copy(document = CNPJ_2)
        every { walletService.findWallet(any()) } returns wallet
        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns listOf(scheduledBillForToday)

        service.executeChatBotAiRegular(wallet.id)

        verify(exactly = 0) { chatBotNotificationService.notifyBillComingDue(any(), any(), any(), any(), any()) }
    }

    @Test
    fun `chatbot deve notificar contas agendadas que estejam aguardando saldo`() {
        every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHABOT_AI
        every { accountService.findAccountById(any()) } returns founderAccount
        every { walletService.findWallet(any()) } returns wallet
        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns listOf(scheduledBillForToday, scheduledBillForTodayWaitingFunds)

        service.executeChatBotAiRegular(wallet.id)

        verify { chatBotNotificationService.notifyBillComingDue(any(), any(), any(), any(), any()) }
    }

    @Test
    fun `não deve enviar notificação de conta vencendo quando só há de pix que fazem parte do onboarding TriPix`() {
        every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHABOT_AI
        every { accountService.findAccountById(any()) } returns founderAccount
        every { walletService.findWallet(any()) } returns wallet
        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns listOf(scheduledBillForTodayWaitingFunds.copy(tags = setOf(BillTag.ONBOARDING_TEST_PIX)))

        service.executeChatBotAiRegular(wallet.id)

        verify(exactly = 0) { chatBotNotificationService.notifyBillComingDue(any(), any(), any(), any(), any()) }
    }

    @Test
    fun `não deve enviar notificação do ultimo aviso de conta vencendo quando só há de pix que fazem parte do onboarding TriPix`() {
        every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHABOT_AI
        every { accountService.findAccountById(any()) } returns founderAccount
        every { walletService.findWallet(any()) } returns wallet
        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns listOf(scheduledBillForTodayWaitingFunds.copy(tags = setOf(BillTag.ONBOARDING_TEST_PIX), billType = BillType.PIX))

        withGivenDateTime(getZonedDateTime().withHour(21).withMinute(10)) {
            service.executeLastWarn(wallet.id)
        }

        verify(exactly = 0) { chatBotNotificationService.notifyBillComingDue(any(), any(), any(), any(), any()) }
    }

    @Test
    fun `não deve enviar notificação do ultimo aviso de conta vencendo pelo fred quando founder for PJ`() {
        every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHABOT_AI
        every { accountService.findAccountById(any()) } returns pjFounderAccount
        every { notificationHintService.getBillComingDueLastWarn(any(), any()) } returns "hint"
        every { walletService.findWallet(any()) } returns pjWallet
        every {
            findBillService.findBillsComingDue(pjWallet.id)
        } returns listOf(dueDateTodayBill, dueDateTodayBill.copy(paymentLimitTime = LocalTime.parse("22:00")))

        withGivenDateTime(getZonedDateTime().withHour(20).withMinute(10)) {
            service.executeLastWarn(pjWallet.id)
        }

        verify(exactly = 0) { chatBotNotificationService.notifyBillComingDue(any(), any(), any(), any(), any()) }
        verify {
            notificationAdapter.notifyBillComingDueLastWarn(
                members = any(),
                wallet = any(),
                dueDate = any(),
                paymentLimitTime = any(),
                hint = any(),
                bills = any(),
            )
        }
    }

    @Test
    fun `deve remover da notificação os pix que fazem parte do onboarding TriPix`() {
        every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHABOT_AI
        every { accountService.findAccountById(any()) } returns founderAccount
        every { walletService.findWallet(any()) } returns wallet
        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns listOf(dueDateTodayBill, scheduledBillForTodayWaitingFunds.copy(tags = setOf(BillTag.ONBOARDING_TEST_PIX)))

        service.executeChatBotAiRegular(wallet.id)

        val billsSlot = slot<List<BillView>>()

        verify(exactly = 1) { chatBotNotificationService.notifyBillComingDue(any(), any(), capture(billsSlot), any(), any()) }

        billsSlot.captured shouldContainExactly listOf(dueDateTodayBill)
    }
}