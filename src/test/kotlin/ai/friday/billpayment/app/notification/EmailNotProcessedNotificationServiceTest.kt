package ai.friday.billpayment.app.notification

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.integrations.NotificationService
import ai.friday.billpayment.integration.WalletFixture
import io.mockk.mockk
import io.mockk.verify
import java.time.ZonedDateTime
import org.junit.jupiter.api.Test

internal class EmailNotProcessedNotificationServiceTest {

    private val notificationService = mockk<NotificationService>(relaxed = true)

    private val service = EmailNotProcessedNotificationService(
        notificationService = notificationService,
        templatesConfiguration = mockk(),
    )

    private val walletFixture = WalletFixture()

    @Test
    fun `should notify members`() {
        val members = listOf(walletFixture.founder)

        service.notify(
            EmailNotProcessedNotificationRequest(
                members = members,
                subject = "Mail Subject",
                walletName = "Test",
                sender = EmailAddress(email = "<EMAIL>"),
                dateTime = ZonedDateTime.now(),
            ),
        )
        verify {
            notificationService.notifyMembers(members, NotificationType.EMAIL_NOT_PROCESSED, any())
        }
    }
}