package ai.friday.billpayment.app.notification

import ai.friday.billpayment.app.account.AccountId
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test

class MessageProcessorServiceImplTest {

    private val messageProcessor = MessageProcessorServiceImpl(
        notificationService = mockk {
            every {
                notify(any())
            } returns "NotificationId"
        },
        messagePublisher = mockk(relaxUnitFun = true),
        accountRegisterService = mockk {
            every {
                checkIsTestAccount(any<AccountId>())
            } returns false
        },
        loginService = mockk {
            every {
                createLogin(any(), any(), any())
            } returns mockk()
        },
        checkableNotificationService = mockk(),
        handlebarsTempate2Html2Image = mockk {
            every {
                renderTemplateImageAndCreatePublicLink(any())
            } returns "ImagePulicLink"
        },
        whatsappNotificationVerifierQueueName = "whatsappNotificationVerifierQueueName",
    )

    @Test
    fun `deve funcionar com o payload`() {
        val payload =
            """{"notification":{"notificationId":"ee9bae11-6762-43c4-8090-5ddbce28e885","externalUserId":"ACCOUNT-460a346e-7e5d-4991-b001-9ae44a1df061","email":null,"mobilePhone":"+*************","template":"wallet_investment_bill_receipt_with_badge_mp__1_0_0","notificationChannel":"WHATSAPP","emailParameters":null,"whatsAppParameters":["R${'$'} 50,00","Reserva de Emergência","ARBI","Me Poupe 100","100% CDI","16/03/2035","701475","https://www.mepoupe.app","13/03/2025 às 12:05"],"buttonWhatsAppParameter":"app/comprovante/ACCOUNT-460a346e-7e5d-4991-b001-9ae44a1df061/BILL-dfa63f1c-856c-48f6-b788-551ae436f96a","quickReplyButtonsWhatsAppParameter":[],"headerImageOrDocumentUrl":null,"documentFilename":null,"media":null,"quickRepliesStartIndex":null},"asyncImageFromHtml":{"templatePath":"templates/instagrammable-investment-receipt.me-poupe","templateData":{"imageUrl":"https://notification-templates-cdn.mepoupe.app/static/emergency.png","goalName":"Reserva de Emergência","installmentNumber":6,"installmentTotal":6,"progress":100},"placeToStore":{"region":"us-east-1","bucket":"me-poupe-contas-bill-receipts","key":"ACCOUNT-460a346e-7e5d-4991-b001-9ae44a1df061/badge/GOAL-418d1363-5ff5-4a80-8cba-4ea1ce8ba940/*************.png"},"imageFormat":"png","imageType":"image/png","imageResolution":300,"imageWidth":0,"imageHeight":0}}"""

        messageProcessor.process(payload)
    }
}