package ai.friday.billpayment.app.notification

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountPaymentStatus
import ai.friday.billpayment.app.chatbot.ChatbotMessagePublisher
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.WelcomeMessageType
import ai.friday.billpayment.app.message.QueueMessage
import ai.friday.billpayment.app.message.QueueNotificationTO
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.AccountFixture.createAccount
import ai.friday.billpayment.integration.WALLET_ID
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import io.via1.communicationcentre.app.notification.NotificationChannel
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class DefaultNotificationServiceChooseChannelTest {

    private val accountRepository: AccountRepository = mockk()
    private val shouldNotifyUser: ShouldNotifyUser = mockk()
    private val messagePublisher: MessagePublisher = mockk()
    private val pushNotificationService: PushNotificationService = mockk()
    private val chatbotMessagePublisher: ChatbotMessagePublisher = mockk(relaxUnitFun = true)

    private val service = DefaultNotificationService(
        accountRepository = accountRepository,
        shouldNotifyUser = shouldNotifyUser,
        messagePublisher = messagePublisher,
        notificationQueueName = "test-queue",
        pushNotificationService = pushNotificationService,
        chatbotMessagePublisher = chatbotMessagePublisher,
    )

    @Nested
    inner class MudancaParaPush {

        @Test
        fun deveMudarParaPushQuandoContaEstaEmOverdueEPushESuportado() {
            // Given
            val account = createAccount().copy(paymentStatus = AccountPaymentStatus.Overdue)
            val notification = createWhatsappNotification()
            every { pushNotificationService.canSendViaPush(notification) } returns true

            // When
            service.sendNotification(account, NotificationType.BILL_CREATED, notification)

            verify(exactly = 1) {
                pushNotificationService.sendNotificationAsync(accountId = account.accountId, billPaymentNotification = notification)
            }
        }

        @Test
        fun deveMudarParaPushQuandoContaTemGrupoPushOverWhatsappEPushESuportado() {
            // Given
            val rawAccount = createAccount()
            val account = rawAccount.copy(configuration = rawAccount.configuration.copy(groups = listOf(AccountGroup.PUSH_OVER_WHATSAPP)))
            val notification = createWhatsappNotification()
            every { pushNotificationService.canSendViaPush(notification) } returns true

            // When
            service.sendNotification(account, NotificationType.BILL_CREATED, notification)

            verify(exactly = 1) {
                pushNotificationService.sendNotificationAsync(accountId = account.accountId, billPaymentNotification = notification)
            }
        }

        @Test
        fun deveMudarParaPushQuandoCanalPreferidoEPushEPushESuportado() {
            // Given
            val account = createAccount()
            val notification = createMultiChannelNotification().copy(preferredChannel = BillPaymentNotificationChannel.PUSH)
            every { pushNotificationService.canSendViaPush(notification) } returns true

            // When
            service.sendNotification(account, NotificationType.BILL_CREATED, notification)

            verify(exactly = 1) {
                pushNotificationService.sendNotificationAsync(accountId = account.accountId, billPaymentNotification = notification)
            }
        }

        @Test
        fun deveUsarChatbotComoFallbackQuandoCanalPreferidoEPushMasPushNaoESuportado() {
            // Given
            val account = createAccount()
            val notification = createMultiChannelNotification().copy(preferredChannel = BillPaymentNotificationChannel.PUSH)
            every { pushNotificationService.canSendViaPush(notification) } returns false

            // When
            service.sendNotification(account, NotificationType.BILL_CREATED, notification)

            verify(exactly = 0) {
                pushNotificationService.sendNotificationAsync(accountId = account.accountId, billPaymentNotification = notification)
            }
            verify {
                chatbotMessagePublisher.publishNotification(notification)
            }
        }

        @Test
        fun naoDeveMudarParaPushQuandoContaEstaEmDiaENaoTemGrupoEspecial() {
            // Given
            val account = createAccount().copy(paymentStatus = AccountPaymentStatus.UpToDate)
            val notification = createWhatsappNotification()
            every { pushNotificationService.canSendViaPush(notification) } returns true

            // When
            service.sendNotification(account, NotificationType.BILL_CREATED, notification)

            verify(exactly = 0) {
                pushNotificationService.sendNotificationAsync(accountId = account.accountId, billPaymentNotification = notification)
            }

            verify {
                chatbotMessagePublisher.publishGenericWhatsappNotification(account, notification)
            }
        }
    }

    @Nested
    inner class MudancaParaChatbot {

        @Test
        fun deveMudarParaChatbotQuandoNotificacaoEWhatsappEFeatureFlagEstaHabilitada() {
            // Given
            val account = createAccount()
            val notification = createWhatsappNotification()

            // When
            service.sendNotification(account, NotificationType.BILL_CREATED, notification)

            verify(exactly = 0) {
                messagePublisher.sendMessage(any())
            }

            verify(exactly = 1) {
                chatbotMessagePublisher.publishGenericWhatsappNotification(any(), any())
            }
        }

        @Test
        fun deveMudarParaChatbotQuandoMultiChannelNotificationTemCanalPreferidoWhatsappEFeatureFlagEstaHabilitada() {
            // Given
            val account = createAccount()
            val notification = createMultiChannelNotification(BillPaymentNotificationChannel.CHATBOT)

            service.sendNotification(account, NotificationType.BILL_CREATED, notification)

            verify(exactly = 0) {
                messagePublisher.sendMessage(any())
            }

            verify(exactly = 1) {
                chatbotMessagePublisher.publishNotification(notification)
            }
        }

        @Test
        fun deveMudarParaChatbotQuandoAccountENullENotificacaoEWhatsapp() {
            // Given
            val notification = createWhatsappNotification()

            // When
            service.sendNotification(null, NotificationType.BILL_CREATED, notification)

            verify(exactly = 0) {
                messagePublisher.sendMessage(any())
            }

            verify(exactly = 1) {
                chatbotMessagePublisher.publishGenericWhatsappNotification(any(), any())
            }
        }

        @Test
        fun naoDeveMudarParaChatbotQuandoNotificacaoEEmail() {
            // Given
            val account = createAccount()
            val notification = createEmailNotification()

            // When
            service.sendNotification(account, NotificationType.BILL_CREATED, notification)

            checkMessagePublisherNotificationSent(NotificationChannel.EMAIL)
        }
    }

    private fun checkMessagePublisherNotificationSent(channel: NotificationChannel) {
        val slot = slot<QueueMessage>()
        verify(exactly = 1) {
            messagePublisher.sendMessage(capture(slot))
        }
        val sentNotification = parseObjectFrom<QueueNotificationTO>(slot.captured.jsonObject)
        sentNotification.notification.notificationChannel shouldBe channel
    }

    @Nested
    inner class CanalPreferidoMantido {

        @Test
        fun deveManterCanalEmailQuandoNaoHaCondicoesParaMudanca() {
            // Given
            val account = createAccount()
            val notification = createEmailNotification()
            every { pushNotificationService.canSendViaPush(notification) } returns false

            // When
            service.sendNotification(account, NotificationType.BILL_CREATED, notification)

            checkMessagePublisherNotificationSent(NotificationChannel.EMAIL)
        }

        @Test
        fun deveManterCanalChatbotQuandoNotificacaoJaEChatbotNotification() {
            // Given
            val account = createAccount()
            val notification = ChatbotNotificationWithAccount(
                walletId = WalletId(WALLET_ID),
                account = account,
                details = WelcomeDetails(WelcomeMessageType.WELCOME_CHATBOT_ONBOARDING),
            )
            every { pushNotificationService.canSendViaPush(notification) } returns false

            service.sendNotification(account, NotificationType.BILL_CREATED, notification)

            verify {
                chatbotMessagePublisher.publishChatbotNotification(notification)
            }
        }
    }

    @Nested
    inner class CenariosComContaNull {

        @Test
        fun deveUsarCanalPreferidoQuandoAccountENullENaoHaCondicoesParaMudanca() {
            // Given
            val notification = createEmailNotification()

            // When
            service.sendNotification(null, NotificationType.BILL_CREATED, notification)

            checkMessagePublisherNotificationSent(NotificationChannel.EMAIL)
        }

        @Test
        fun deveMudarParaChatbotQuandoAccountENullENotificacaoEWhatsapp() {
            // Given
            val notification = createWhatsappNotification()

            // When
            service.sendNotification(null, NotificationType.BILL_CREATED, notification)

            verify {
                chatbotMessagePublisher.publishGenericWhatsappNotification(
                    any(),
                    any(),
                )
            }
        }
    }

    @Nested
    inner class CenariosDePrioridade {

        @Test
        fun pushDeveTerPrioridadeSobreChatbotQuandoAmbasCondicoesSaoAtendidas() {
            // Given
            val account = createAccount().copy(paymentStatus = AccountPaymentStatus.Overdue)
            val notification = createWhatsappNotification()
            every { pushNotificationService.canSendViaPush(notification) } returns true

            // When
            service.sendNotification(account, NotificationType.BILL_CREATED, notification)

            verify(exactly = 1) {
                pushNotificationService.sendNotificationAsync(accountId = account.accountId, billPaymentNotification = notification)
            }
        }

        @Test
        fun chatbotDeveTerPrioridadeSobreCanalPreferidoQuandoCondicoesSaoAtendidas() {
            // Given
            val account = createAccount()
            val notification = createWhatsappNotification()
            every { pushNotificationService.canSendViaPush(notification) } returns false

            // When
            service.sendNotification(account, NotificationType.BILL_CREATED, notification)

            verify(exactly = 1) {
                chatbotMessagePublisher.publishGenericWhatsappNotification(
                    any(),
                    any(),
                )
            }
        }
    }

    @Nested
    inner class CenariosDeFallback {

        @Test
        fun deveUsarChatbotComoFallbackQuandoPushEPreferidoMasNaoSuportado() {
            // Given
            val account = createAccount()
            val notification = createMultiChannelNotification().copy(preferredChannel = BillPaymentNotificationChannel.PUSH)
            every { pushNotificationService.canSendViaPush(notification) } returns false

            // When
            service.sendNotification(account, NotificationType.BILL_CREATED, notification)

            verify {
                chatbotMessagePublisher.publishNotification(notification)
            }
        }

        @Test
        fun deveUsarCanalPreferidoQuandoPushNaoESuportadoENaoHaOutrasCondicoes() {
            // Given
            val account = createAccount().copy(paymentStatus = AccountPaymentStatus.Overdue)
            val notification = createMultiChannelNotification(preferredChannel = BillPaymentNotificationChannel.CHATBOT)
            every { pushNotificationService.canSendViaPush(notification) } returns false

            // When
            service.sendNotification(account, NotificationType.BILL_CREATED, notification)

            verify {
                chatbotMessagePublisher.publishNotification(notification)
            }
        }
    }
}