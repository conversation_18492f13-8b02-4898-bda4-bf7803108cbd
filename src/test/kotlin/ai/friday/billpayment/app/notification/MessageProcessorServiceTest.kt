package ai.friday.billpayment.app.notification

import ai.friday.billpayment.adapters.messaging.SQSMessagePublisher
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountRegisterService
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.login.LoginService
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.withEarlyAccess
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import io.via1.communicationcentre.app.integrations.CheckableNotificationService
import io.via1.communicationcentre.app.integrations.NotificationService
import io.via1.communicationcentre.app.notification.Notification
import io.via1.communicationcentre.app.notification.NotificationChannel
import io.via1.communicationcentre.app.notification.NotificationFailedReason
import io.via1.communicationcentre.app.notification.NotificationStatus
import io.via1.communicationcentre.app.notification.NotificationStatusResult
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import software.amazon.awssdk.services.sqs.model.Message

class MessageProcessorServiceTest {
    private val notificationService: NotificationService = mockk(relaxed = true)
    private val loginService: LoginService = mockk(relaxed = true)
    private val accountRegisterService: AccountRegisterService = mockk(relaxed = true)
    private val messagePublisher: SQSMessagePublisher = mockk(relaxed = true)
    private val checkableNotificationService = mockk<CheckableNotificationService>()

    private val messageProcessorService = MessageProcessorServiceImpl(
        notificationService = notificationService,
        loginService = loginService,
        accountRegisterService = accountRegisterService,
        messagePublisher = messagePublisher,
        whatsappNotificationVerifierQueueName = "fake",
        checkableNotificationService = checkableNotificationService,
        handlebarsTempate2Html2Image = mockk(),
    )

    @Nested
    @DisplayName("quando processar uma mensagem")
    inner class ProcessMessageTest {

        @Test
        fun `deve atualizar o login do usuário ao enviar uma mensagem de whatsapp e não for um usuário de teste`() {
            val userId = "<EMAIL>"
            every {
                notificationService.notify(any())
            } returns userId
            every {
                accountRegisterService.checkIsTestAccount(any<AccountId>())
            } returns false

            val notification = Notification()
            notification.notificationChannel = NotificationChannel.WHATSAPP
            notification.externalUserId = ACCOUNT_ID
            notification.mobilePhone = "*************"

            withEarlyAccess(AccountId(ACCOUNT_ID)) {
                messageProcessorService.process(getObjectMapper().writeValueAsString(notification))
            }

            val slot = slot<ProviderUser>()
            verify {
                loginService.createLogin(capture(slot), AccountId(ACCOUNT_ID), Role.OWNER)
            }

            with(slot.captured) {
                this.id shouldBe ACCOUNT_ID
                this.emailAddress.value shouldBe userId
                this.providerName shouldBe ProviderName.WHATSAPP
            }
        }

        @Test
        fun `deve enviar uma mensagem simples`() {
            val userId = "<EMAIL>"
            every {
                notificationService.sendSimpleResponse(any(), any(), any())
            } returns userId

            val messageString = """{
          "mobilePhone": "*************",
          "notificationId":"99feb5dc-6d27-11ee-8146-bbb90be6d355",
          "message": "Teste via fila",
          "externalUserId":"$ACCOUNT_ID"
        }
            """.trimIndent()

            messageProcessorService.process(messageString)

            verify(exactly = 1) {
                messagePublisher.sendMessage(any())
            }

            verify {
                notificationService.sendSimpleResponse("Teste via fila", "*************", "99feb5dc-6d27-11ee-8146-bbb90be6d355")
            }
        }

        @Test
        fun `deve enviar uma mensagem com link`() {
            val userId = "<EMAIL>"
            every {
                notificationService.sendLinkResponse(any(), any(), any(), any(), any())
            } returns userId

            val messageString = """{
          "mobilePhone": "*************",
          "notificationId":"99feb5dc-6d27-11ee-8146-bbb90be6d355",
          "message": "Teste via fila",
          "externalUserId":"$ACCOUNT_ID",
          "link": {
            "displayText": "CTA",
            "url": "https://use.friday.ai/app/entrar"
          }
        }
            """.trimIndent()

            messageProcessorService.process(messageString)

            verify(exactly = 1) {
                messagePublisher.sendMessage(any())
            }

            verify {
                notificationService.sendLinkResponse("Teste via fila", "CTA", "https://use.friday.ai/app/entrar", "*************", "99feb5dc-6d27-11ee-8146-bbb90be6d355")
            }
        }

        @Test
        fun `não deve atualizar o login do usuário ao enviar uma mensagem de whatsapp e for um usuário de teste`() {
            val userId = "<EMAIL>"
            every {
                notificationService.notify(any())
            } returns userId
            every {
                accountRegisterService.checkIsTestAccount(any<AccountId>())
            } returns true

            val notification = Notification()
            notification.notificationChannel = NotificationChannel.WHATSAPP
            notification.externalUserId = ACCOUNT_ID
            notification.mobilePhone = "*************"

            withEarlyAccess(AccountId(ACCOUNT_ID)) {
                messageProcessorService.process(getObjectMapper().writeValueAsString(notification))
            }

            verify(exactly = 0) {
                loginService.createLogin(any(), any(), any())
            }
        }

        @Test
        fun `não deve atualizar o login do usuário ao enviar uma mensagem de email`() {
            val userId = "<EMAIL>"
            every {
                notificationService.notify(any())
            } returns userId

            val notification = Notification()
            notification.notificationChannel = NotificationChannel.EMAIL
            notification.externalUserId = ACCOUNT_ID

            messageProcessorService.process(getObjectMapper().writeValueAsString(notification))

            verify {
                loginService wasNot Called
            }
        }

        @ParameterizedTest
        @CsvSource(value = [",", "'',", "' ',"])
        fun `não deve atualizar o login do usuário ao enviar uma mensagem sem external user id`(externalUserId: String?) {
            val userId = "<EMAIL>"
            every {
                notificationService.notify(any())
            } returns userId

            val notification = Notification()
            notification.notificationChannel = NotificationChannel.WHATSAPP
            notification.externalUserId = externalUserId
            notification.mobilePhone = "*************"

            messageProcessorService.process(getObjectMapper().writeValueAsString(notification))

            verify {
                loginService wasNot Called
            }
        }

        @Test
        fun `deve chamar o acompanhamento de status de mensagem quando o canal for WHATSAPP`() {
            val userId = "<EMAIL>"
            every {
                notificationService.notify(any())
            } returns userId

            val notification = Notification()
            notification.notificationChannel = NotificationChannel.WHATSAPP
            notification.externalUserId = ACCOUNT_ID
            notification.mobilePhone = "*************"

            messageProcessorService.process(getObjectMapper().writeValueAsString(notification))

            verify(exactly = 1) {
                messagePublisher.sendMessage(
                    withArg {
                        it.jsonObject shouldBe getObjectMapper().writeValueAsString(notification)
                    },
                )
            }
        }
    }

    @Nested
    @DisplayName("quando verificar status de uma mensagem")
    inner class VerifyStatusMessageTest {
        private val message = mockk<Message>() {
            every { body() } returns """{"notificationId":"41f9b6f1-012d-47b0-b513-efbbe0db1eea","externalUserId":null,"email":null,"mobilePhone":"*************","template":"wallet_one_pix_pay_failure__4_16_0","notificationChannel":"WHATSAPP","emailParameters":null,"whatsAppParameters":[],"buttonWhatsAppParameter":"entrar","quickReplyButtonsWhatsAppParameter":null}"""
            every { attributes() } returns emptyMap()
        }

        @Test
        fun `deve retornar falso e deve renotificar na blip em caso de falha rententável`() {
            every { checkableNotificationService.checkStatus(any()) } returns NotificationStatusResult(
                NotificationStatus.FAILED,
                NotificationFailedReason("1", "failed"),
                true,
            )
            val response = messageProcessorService.verifyStatus(message)

            response.shouldBeFalse()

            verify(exactly = 1) {
                notificationService.notify(any())
            }
        }

        @Test
        fun `deve retornar verdadeiro em caso de falha não rententável`() {
            every { checkableNotificationService.checkStatus(any()) } returns NotificationStatusResult(
                NotificationStatus.FAILED,
                NotificationFailedReason("1", "failed"),
                false,
            )
            val response = messageProcessorService.verifyStatus(message)

            response.shouldBeTrue()

            verify {
                notificationService wasNot Called
            }
        }

        @Test
        fun `deve retornar verdadeiro em caso de sucesso`() {
            every { checkableNotificationService.checkStatus(any()) } returns NotificationStatusResult(NotificationStatus.SUCCESS)
            val response = messageProcessorService.verifyStatus(message)

            response.shouldBeTrue()

            verify { notificationService wasNot Called }
        }

        @Test
        fun `deve retornar falso caso esteja processando`() {
            every { checkableNotificationService.checkStatus(any()) } returns NotificationStatusResult(NotificationStatus.PROCESSING)
            val response = messageProcessorService.verifyStatus(message)

            response.shouldBeFalse()

            verify { notificationService wasNot Called }
        }

        @Test
        fun `deve retornar falso em caso de erro desconhecido`() {
            every { checkableNotificationService.checkStatus(any()) } returns NotificationStatusResult(
                NotificationStatus.UNKNOWN,
                NotificationFailedReason("1", "failed"),
                true,
            )
            val response = messageProcessorService.verifyStatus(message)

            response.shouldBeFalse()

            verify { notificationService wasNot Called }
        }
    }
}