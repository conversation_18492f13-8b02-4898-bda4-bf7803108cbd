package ai.friday.billpayment.app.usergroups

import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.shouldBe
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

@MicronautTest(environments = [FRIDAY_ENV])
class FridayUserGroupsSelectorServiceTest(private val userGroupsSelectorService: UserGroupsSelectorService) {
    @ParameterizedTest
    @CsvSource(
        "2024-01-01",
        "2026-01-01",
    )
    fun `deve retornar vazio quando não está na data correta`(date: String) {
        withGivenDateTime(ZonedDateTime.of(LocalDate.parse(date, DateTimeFormatter.ISO_LOCAL_DATE), LocalTime.NOON, brazilTimeZone)) {
            val groups = userGroupsSelectorService.selectGroups(AccountGroupSelectorType.REGISTER)
            groups.size shouldBe 0
        }
    }

    @Test
    fun `deve retorar vazio quando esta na data ativa`() {
        withGivenDateTime(ZonedDateTime.of(LocalDate.of(2025, 5, 1), LocalTime.NOON, brazilTimeZone)) {
            val groups = userGroupsSelectorService.selectGroups(AccountGroupSelectorType.REGISTER)
            groups.size shouldBe 0
        }
    }
}