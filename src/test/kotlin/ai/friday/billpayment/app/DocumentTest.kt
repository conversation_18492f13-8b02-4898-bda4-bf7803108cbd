package ai.friday.billpayment.app

import io.kotest.matchers.shouldBe
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource

class DocumentTest {
    @ParameterizedTest
    @ValueSource(strings = ["03761255004", "30202752020", "74877264019", "05584772089", "94240437083", "08954368093", "86853631013"])
    fun `deve retornar true para cpfs válidos`(cpf: String) {
        cpf.isValidCpf() shouldBe true
    }

    @ParameterizedTest
    @ValueSource(strings = ["", "53424543233", "23312435332", "11111111111", "22222222222", "33333333333", "44444444444", "55555555555", "66666666666", "77777777777", "88888888888", "99999999999", "12345678901", "123456789"])
    fun `deve retornar false para cpfs invalidos`(cpf: String) {
        cpf.isValidCpf() shouldBe false
    }

    @ParameterizedTest
    @ValueSource(strings = ["98744762000105", "92311426000184", "13366396000137", "65890793000107", "53933965000101", "24994004000109", "95273983000146"])
    fun `deve retornar true para cnpj válidos`(cnpj: String) {
        Document(cnpj).isValidCnpj() shouldBe true
    }

    @ParameterizedTest
    @ValueSource(strings = ["02343029430432", "11111111111111", "22222222222222", "33333333333333", "44444444444444", "55555555555555", "66666666666666", "77777777777777", "88888888888888", "99999999999999", "12345678901"])
    fun `deve retornar false para cnpj invalidos`(cnpj: String) {
        Document(cnpj).isValidCnpj() shouldBe false
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            ",", // empty
            "62863943030,62863943030", // valid CPF
            "00062863943030,62863943030", // valid CPF com CNPJ_SIZE
            "02006062042,02006062042", // valid CPF iniciado com 0
            "2006062042,02006062042", // valid CPF sem o 0 inicial
            "34701685000115,34701685000115", // valid CNPJ
            "00000000000191,00000000000191", // valid BB CNPJ
            "09708509000168,09708509000168", // valid CNPJ iniciado com 0
            "9708509000168,09708509000168", // valid CNPJ sem o 0 inicial
        ],
    )
    fun `deve gerar CPFs ou CNPJs corretamente`(original: String?, expected: String?) {
        original.orEmpty().sanitizeDocumentNumber() shouldBe expected.orEmpty()
    }
}