package ai.friday.billpayment.app.usage

import ai.friday.billpayment.app.account.AccountPaymentMethodId
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class TransactionTotalAmountTest {

    private val paymentMethodId1 = AccountPaymentMethodId("ID-1")
    private val paymentMethodId2 = AccountPaymentMethodId("ID-2")
    private val paymentMethodId3 = AccountPaymentMethodId("ID-3")

    @Test
    fun `deve somar o valor com o mesmo paymentMethodId`() {
        val foo = TransactionAmount(
            totalAmount = 1,
            feeAmount = 2,
            amounts = mapOf(
                paymentMethodId1 to TransactionItemAmount(
                    totalAmount = 1,
                    feeAmount = 2,
                    paymentMethodId = paymentMethodId1,
                ),
            ),
        )

        val bar = foo.add(foo)

        bar.totalAmount shouldBe 2
        bar.feeAmount shouldBe 4
        bar.amounts.size shouldBe 1
        bar.amounts[paymentMethodId1]?.totalAmount shouldBe 2
        bar.amounts[paymentMethodId1]?.feeAmount shouldBe 4
        bar.amounts[paymentMethodId1]?.paymentMethodId shouldBe paymentMethodId1
    }

    @Test
    fun `deve somar o valor com o paymentMethodId diferente`() {
        val foo1 = TransactionAmount(
            totalAmount = 1,
            feeAmount = 2,
            amounts = mapOf(
                paymentMethodId1 to TransactionItemAmount(
                    totalAmount = 1,
                    feeAmount = 2,
                    paymentMethodId = paymentMethodId1,
                ),
            ),
        )

        val foo2 = TransactionAmount(
            totalAmount = 1,
            feeAmount = 2,
            amounts = mapOf(
                paymentMethodId2 to TransactionItemAmount(
                    totalAmount = 1,
                    feeAmount = 2,
                    paymentMethodId = paymentMethodId2,
                ),
            ),
        )

        val bar = foo1.add(foo2)

        bar.totalAmount shouldBe 2
        bar.feeAmount shouldBe 4
        bar.amounts.size shouldBe 2
        bar.amounts[paymentMethodId1]?.totalAmount shouldBe 1
        bar.amounts[paymentMethodId1]?.feeAmount shouldBe 2
        bar.amounts[paymentMethodId1]?.paymentMethodId shouldBe paymentMethodId1
        bar.amounts[paymentMethodId2]?.totalAmount shouldBe 1
        bar.amounts[paymentMethodId2]?.feeAmount shouldBe 2
        bar.amounts[paymentMethodId2]?.paymentMethodId shouldBe paymentMethodId2
    }

    @Test
    fun `deve somar o valor com o paymentMethodId iguais e diferentes`() {
        val foo1 = TransactionAmount(
            totalAmount = 32,
            feeAmount = 34,
            amounts = mapOf(
                paymentMethodId1 to TransactionItemAmount(
                    totalAmount = 1,
                    feeAmount = 2,
                    paymentMethodId = paymentMethodId1,
                ),
                paymentMethodId3 to TransactionItemAmount(
                    totalAmount = 31,
                    feeAmount = 32,
                    paymentMethodId = paymentMethodId3,
                ),
            ),
        )

        val foo2 = TransactionAmount(
            totalAmount = 32,
            feeAmount = 34,
            amounts = mapOf(
                paymentMethodId2 to TransactionItemAmount(
                    totalAmount = 1,
                    feeAmount = 2,
                    paymentMethodId = paymentMethodId2,
                ),
                paymentMethodId3 to TransactionItemAmount(
                    totalAmount = 31,
                    feeAmount = 32,
                    paymentMethodId = paymentMethodId3,
                ),
            ),
        )

        val bar = foo1.add(foo2)

        bar.totalAmount shouldBe 64
        bar.feeAmount shouldBe 68
        bar.amounts.size shouldBe 3
        bar.amounts[paymentMethodId1]?.totalAmount shouldBe 1
        bar.amounts[paymentMethodId1]?.feeAmount shouldBe 2
        bar.amounts[paymentMethodId1]?.paymentMethodId shouldBe paymentMethodId1
        bar.amounts[paymentMethodId2]?.totalAmount shouldBe 1
        bar.amounts[paymentMethodId2]?.feeAmount shouldBe 2
        bar.amounts[paymentMethodId2]?.paymentMethodId shouldBe paymentMethodId2
        bar.amounts[paymentMethodId3]?.totalAmount shouldBe 62
        bar.amounts[paymentMethodId3]?.feeAmount shouldBe 64
        bar.amounts[paymentMethodId3]?.paymentMethodId shouldBe paymentMethodId3
    }
}