package ai.friday.billpayment.app.usage

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.payment.PaymentSchedulingService
import ai.friday.billpayment.app.wallet.DailyPaymentLimitType
import ai.friday.billpayment.app.wallet.UpdateLimitsErrors
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.withGivenDateTime
import arrow.core.getOrElse
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import java.time.ZonedDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail

class WalletLimitsServiceIntegrationTest {
    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val enhancedClient = getDynamoDB()

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    val walletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = mockk(),
    )

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    private val billRepository = mockk<BillRepository> {
        every {
            getTotalPaid(any(), any(), any())
        } returns 0L

        every {
            getTotalPaid(any(), any(), any(), any())
        } returns 0L
    }

    private val paymentSchedulingService = mockk<PaymentSchedulingService> {
        every {
            getScheduledBillsTotalAmount(
                any(),
                any(),
                any(),
                any(),
            )
        } returns 0L
    }

    private val accountService: AccountService = mockk() {
        every { findAccountById(any()) }.returns(walletFixture.founderAccount)
    }

    private val walletLimitsService = WalletLimitsService(
        billRepository = billRepository,
        walletRepository = walletRepository,
        accountService = accountService,
        paymentSchedulingService = paymentSchedulingService,
        internalLock = mockk(relaxed = true),
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        walletRepository.save(wallet)
    }

    @Test
    fun `ao chamar os limites deve retornar os valores iniciais`() {
        createLimits()
        val limits = walletLimitsService.findWalletPaymentLimits(wallet.id)

        limits.isRight() shouldBe true

        limits.map {
            it.daily.activeAmount shouldBe 500_00L
            it.nighttime.activeAmount shouldBe 500_00L
            it.monthly?.activeAmount shouldBe 10_000_00L
        }
    }

    @Test
    fun `quando um usuário básico não tem limite configurado deve retornar o limite default`() {
        every { accountService.findAccountById(any()) } returns walletFixture.founderAccount.copy(type = UserAccountType.BASIC_ACCOUNT)

        val limits = walletLimitsService.findWalletPaymentLimits(wallet.id)

        limits.isRight() shouldBe true

        limits.map {
            it.daily.activeAmount shouldBe 500_00L
            it.nighttime.activeAmount shouldBe 500_00L
            it.monthly?.activeAmount shouldBe 10_000_00L
        }
    }

    @Test
    fun `quando um usuário full não tem limite configurado deve retornar o limite default`() {
        every { accountService.findAccountById(any()) } returns walletFixture.founderAccount.copy(type = UserAccountType.FULL_ACCOUNT)

        val limits = walletLimitsService.findWalletPaymentLimits(wallet.id)

        limits.isRight() shouldBe true

        limits.map {
            it.daily.activeAmount shouldBe 25_000_00L
            it.nighttime.activeAmount shouldBe 1_000_00L
            it.monthly.shouldBeNull()
        }
    }

    @Test
    fun `ao resetar os limites do usuário deve deixar as configurações corretas`() {
        walletLimitsService.setDefaultFullAccountLimits(accountId = walletFixture.founder.accountId)

        val limits = walletLimitsService.findWalletPaymentLimits(wallet.id)

        limits.isRight() shouldBe true

        limits.map {
            it.daily.activeAmount shouldBe 25_000_00L
            it.nighttime.activeAmount shouldBe 1_000_00L
            it.monthly.shouldBeNull()
        }
    }

    @Test
    fun `quando setar novo limite diário deve garantir que o limite de pagamento via whatsapp seja menor ou igual`() {
        createLimits()
        walletLimitsService.updateLimit(
            accountId = walletFixture.founder.accountId,
            walletId = wallet.id,
            type = DailyPaymentLimitType.WHATSAPP_PAYMENT,
            amount = 500_00L,
            source = ActionSource.System,
        )

        withGivenDateTime(ZonedDateTime.now().plusDays(2)) {
            val result = walletLimitsService.updateLimit(accountId = walletFixture.founder.accountId, walletId = wallet.id, type = DailyPaymentLimitType.DAILY, amount = 100_00L, source = ActionSource.System).getOrElse {
                fail("não deveria falhar")
            }

            result.activeAmount shouldBe 100_00L

            val limits = walletLimitsService.getAllWalletLimitsWithUsage(walletId = wallet.id, accountId = walletFixture.founder.accountId).getOrElse {
                fail("não deveria falhar")
            }

            limits.limits.daily.activeAmount shouldBe 100_00L
            limits.limits.whatsAppPayment.activeAmount shouldBe 100_00L
        }
    }

    @Test
    fun `quando setar novo limite diário deve garantir que o limite pendente de pagamento via whatsapp seja menor ou igual`() {
        createLimits()
        walletLimitsService.updateLimit(
            accountId = walletFixture.founder.accountId,
            walletId = wallet.id,
            type = DailyPaymentLimitType.WHATSAPP_PAYMENT,
            amount = 500_00L,
            source = ActionSource.System,
        )

        val result = walletLimitsService.updateLimit(accountId = walletFixture.founder.accountId, walletId = wallet.id, type = DailyPaymentLimitType.DAILY, amount = 100_00L, source = ActionSource.System).getOrElse {
            fail("não deveria falhar")
        }

        result.activeAmount shouldBe 100_00L

        withGivenDateTime(ZonedDateTime.now().plusDays(2)) {
            val limits = walletLimitsService.getAllWalletLimitsWithUsage(walletId = wallet.id, accountId = walletFixture.founder.accountId).getOrElse {
                fail("não deveria falhar")
            }

            limits.limits.daily.activeAmount shouldBe 100_00L
            limits.limits.whatsAppPayment.activeAmount shouldBe 100_00L
        }
    }

    @Nested
    @DisplayName("ao tentar atualizar o limite de pagamento via whatsapp")
    inner class WhatsAppPaymentUpdateLimitTest {

        @Test
        fun `não deve deixar o limite ultrapassarr o limite diário`() {
            createLimits()

            val result = walletLimitsService.updateLimit(accountId = walletFixture.founder.accountId, walletId = wallet.id, type = DailyPaymentLimitType.WHATSAPP_PAYMENT, amount = 1_000_00L, source = ActionSource.System)

            result.isLeft() shouldBe true
            result.mapLeft {
                it shouldBe UpdateLimitsErrors.WhatsAppPaymentLimitGreaterThanDaily
            }
        }

        @Test
        fun `deve modificar o limite se a feature tiver ligada`() {
            createLimits()
            val result = walletLimitsService.updateLimit(accountId = walletFixture.founder.accountId, walletId = wallet.id, type = DailyPaymentLimitType.WHATSAPP_PAYMENT, amount = 100_00L, source = ActionSource.System).getOrElse {
                fail("não deveria falhar")
            }

            withGivenDateTime(ZonedDateTime.now().plusDays(2)) {
                val walletLimits = walletLimitsService.getAllWalletLimitsWithUsage(walletId = wallet.id, accountId = walletFixture.founder.accountId).getOrElse {
                    fail("não deveria falhar")
                }
                walletLimits.limits.whatsAppPayment.activeAmount shouldBe 100_00L
            }
        }
    }

    @Nested
    @DisplayName("ao buscar o limite de pagamento via whatsapp")
    inner class WhatsAppPaymentGetLimitTest {
        @Test
        fun `deve retornar o limite default se não tiver limite setado no banco`() {
            createLimits()
            val limits = walletLimitsService.getAllWalletLimitsWithUsage(walletId = wallet.id, accountId = walletFixture.founder.accountId).getOrElse {
                fail("não deveria falhar")
            }

            limits.limits.whatsAppPayment.activeAmount shouldBe 50_00
        }

        @Test
        fun `deve retornar o configurado se houver limite setado no banco`() {
            createLimits()

            val result = walletLimitsService.updateLimit(
                accountId = walletFixture.founder.accountId,
                walletId = wallet.id,
                type = DailyPaymentLimitType.WHATSAPP_PAYMENT,
                amount = 123_45L,
                source = ActionSource.System,
            )

            val limits = walletLimitsService.getAllWalletLimitsWithUsage(walletId = wallet.id, accountId = walletFixture.founder.accountId).getOrElse {
                fail("não deveria falhar")
            }

            limits.limits.whatsAppPayment.activeAmount shouldBe 123_45
        }
    }

    private fun createLimits() {
        walletLimitsService.updateLimit(
            accountId = walletFixture.founder.accountId,
            walletId = wallet.id,
            type = DailyPaymentLimitType.NIGHTTIME,
            amount = 500_00L,
            source = ActionSource.System,
        )
        walletLimitsService.updateLimit(
            accountId = walletFixture.founder.accountId,
            walletId = wallet.id,
            type = DailyPaymentLimitType.DAILY,
            amount = 500_00L,
            source = ActionSource.System,
        )

        walletLimitsService.updateLimit(
            accountId = walletFixture.founder.accountId,
            walletId = wallet.id,
            type = DailyPaymentLimitType.MONTHLY,
            amount = 10_000_00L,
            source = ActionSource.System,
        )
    }
}