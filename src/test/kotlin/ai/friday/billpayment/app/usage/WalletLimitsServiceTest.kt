package ai.friday.billpayment.app.usage

import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.PaymentSchedulingService
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.DailyPaymentLimit
import ai.friday.billpayment.app.wallet.DailyPaymentLimitType
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.MonthlyLimitUsage
import ai.friday.billpayment.app.wallet.StoredWalletPaymentLimits
import ai.friday.billpayment.app.wallet.UpdateLimitsErrors
import ai.friday.billpayment.app.wallet.WalletLimitsUsage
import ai.friday.billpayment.app.wallet.WalletPaymentLimits
import ai.friday.billpayment.app.wallet.WalletPaymentLimitsWithUsage
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.toMember
import ai.friday.morning.date.BrazilZonedDateTimeSupplier
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import arrow.core.getOrElse
import arrow.core.right
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import io.mockk.verify
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZonedDateTime
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.fail
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class WalletLimitsServiceTest {
    val walletFixture = WalletFixture()

    val account = walletFixture.founderAccount.copy(document = "***********")
    val coFounderAccount = walletFixture.participantAccount.toMember(
        type = MemberType.COFOUNDER,
        permissions = MemberPermissions(
            viewBills = BillPermission.ALL_BILLS,
            scheduleBills = BillPermission.ALL_BILLS,
            founderContactsEnabled = false,
            manageMembers = false,
            viewBalance = true,
            notification = true,
        ),
        status = MemberStatus.ACTIVE,
    )

    val wallet = walletFixture.buildPrimaryWallet(
        founderAccount = account,
        otherMembers = listOf(coFounderAccount),
    )

    val legalPersonWallet = walletFixture.buildPrimaryWallet(
        founderAccount = account.copy(document = "**************"),
        otherMembers = listOf(coFounderAccount),
    )

    val billRepository = mockk<BillRepository>()
    val walletRepository = mockk<WalletRepository>() {
        every { findWallet(wallet.id) } returns wallet
    }
    val accountService = mockk<AccountService>() {
        every { findAccountById(any()) } returns walletFixture.participantAccount
    }

    val paymentSchedulingService = mockk<PaymentSchedulingService>()
    val internalLock = mockk<InternalLock>()

    val service =
        WalletLimitsService(billRepository, walletRepository, accountService, paymentSchedulingService, internalLock)

    private val timestamp = ZonedDateTime.of(2020, 1, 2, 23, 20, 21, 123, ZoneId.systemDefault())
    private val monthBegin = timestamp.toLocalDate().withDayOfMonth(1).atStartOfDay(brazilTimeZone)
    private val monthEnd = monthBegin.plusMonths(1).minusNanos(1)
    private val nightTimestamp = timestamp.with(LocalTime.of(20, 0, 0, 0))
    private val endNightTimestamp = timestamp.plusDays(1).with(LocalTime.of(5, 59, 59, 999))

    @BeforeEach
    fun setup() {
        mockkObject(BrazilZonedDateTimeSupplier)
        every { getZonedDateTime() }.returns(timestamp)
        every { getLocalDate() }.returns(timestamp.toLocalDate())

        every { walletRepository.findWalletOrNull(any()) }.returns(wallet)
        every { accountService.findAccountById(any()) }.returns(account)

        every { walletRepository.findLimitOrNull(any(), any()) }.returns(buildLimit(0, DailyPaymentLimitType.DAILY))

        every { billRepository.getTotalPaid(walletId = any(), date = any(), types = any()) }.returns(0)
        every {
            billRepository.getTotalPaid(
                walletId = any(),
                startDate = any(),
                endDate = any(),
                types = any(),
            )
        }.returns(0)

        every {
            paymentSchedulingService.getScheduledBillsTotalAmount(
                walletId = any(),
                startDate = any(),
                endDate = any(),
                filter = any(),
            )
        }.returns(0)

        every {
            internalLock.acquireLock(any())
        } returns mockk(relaxed = true)
    }

    @AfterEach
    fun tearDown() {
        unmockkObject(BrazilZonedDateTimeSupplier)
    }

    @Test
    fun `ao buscar limites com uso, deve retornar todos os limites gastos e deve ser editavel`() {
        every { walletRepository.findAllLimits(any()) } returns StoredWalletPaymentLimits(automaticPix = buildLimit(1_000, DailyPaymentLimitType.AUTOMATIC_PIX), daily = buildLimit(2_000, DailyPaymentLimitType.DAILY), nighttime = buildLimit(1_000, DailyPaymentLimitType.NIGHTTIME), monthly = buildLimit(10_000, DailyPaymentLimitType.MONTHLY), whatsAppPayment = buildLimit(1_000, DailyPaymentLimitType.WHATSAPP_PAYMENT))

        every { billRepository.getTotalPaid(walletId = any(), date = any(), types = any()) }.returns(500)
        every {
            billRepository.getTotalPaid(
                walletId = any(),
                startDate = eq(nightTimestamp),
                endDate = eq(endNightTimestamp),
                types = any(),
            )
        }.returns(200)
        every {
            billRepository.getTotalPaid(
                walletId = any(),
                startDate = monthBegin,
                endDate = monthEnd,
                types = any(),
            )
        }.returns(2_000)

        every {
            paymentSchedulingService.getScheduledBillsTotalAmount(
                walletId = any(),
                startDate = eq(monthBegin.toLocalDate()),
                endDate = eq(monthEnd.toLocalDate()),
                filter = any(),
            )
        }.returns(4_000)

        val result = service.getAllWalletLimitsWithUsage(wallet.id, account.accountId)

        result shouldBe WalletPaymentLimitsWithUsage(
            limits = WalletPaymentLimits(
                automaticPix = buildLimit(1_000, DailyPaymentLimitType.AUTOMATIC_PIX),
                daily = buildLimit(2_000, DailyPaymentLimitType.DAILY),
                nighttime = buildLimit(1_000, DailyPaymentLimitType.NIGHTTIME),
                monthly = buildLimit(10_000, DailyPaymentLimitType.MONTHLY),
                whatsAppPayment = buildLimit(1_000, DailyPaymentLimitType.WHATSAPP_PAYMENT),
            ),
            usage = WalletLimitsUsage(
                automaticPix = 0,
                daily = 500,
                nighttime = 200,
                monthly = MonthlyLimitUsage(used = 2_000, forecasted = 6_000),
            ),
            editable = true,
        ).right()
    }

    @Test
    fun `ao buscar limites com uso de uma carteira PJ o COFOUNDER deve poder editar o valor`() {
        every { walletRepository.findAllLimits(any()) } returns StoredWalletPaymentLimits(automaticPix = buildLimit(1_000, DailyPaymentLimitType.AUTOMATIC_PIX), daily = buildLimit(2_000, DailyPaymentLimitType.DAILY), nighttime = buildLimit(1_000, DailyPaymentLimitType.NIGHTTIME), monthly = buildLimit(10_000, DailyPaymentLimitType.MONTHLY), whatsAppPayment = buildLimit(1_000, DailyPaymentLimitType.WHATSAPP_PAYMENT))

        every { billRepository.getTotalPaid(walletId = any(), date = any(), types = any()) }.returns(500)
        every {
            billRepository.getTotalPaid(
                walletId = any(),
                startDate = eq(nightTimestamp),
                endDate = eq(endNightTimestamp),
                types = any(),
            )
        }.returns(200)
        every {
            billRepository.getTotalPaid(
                walletId = any(),
                startDate = monthBegin,
                endDate = monthEnd,
                types = any(),
            )
        }.returns(2_000)

        every {
            paymentSchedulingService.getScheduledBillsTotalAmount(
                walletId = any(),
                startDate = eq(monthBegin.toLocalDate()),
                endDate = eq(monthEnd.toLocalDate()),
                filter = any(),
            )
        }.returns(4_000)

        every { walletRepository.findWalletOrNull(any()) }.returns(legalPersonWallet)

        val result = service.getAllWalletLimitsWithUsage(wallet.id, coFounderAccount.accountId)

        result shouldBe WalletPaymentLimitsWithUsage(
            limits = WalletPaymentLimits(
                automaticPix = buildLimit(1_000, DailyPaymentLimitType.AUTOMATIC_PIX),
                daily = buildLimit(2_000, DailyPaymentLimitType.DAILY),
                nighttime = buildLimit(1_000, DailyPaymentLimitType.NIGHTTIME),
                monthly = buildLimit(10_000, DailyPaymentLimitType.MONTHLY),
                whatsAppPayment = buildLimit(1_000, DailyPaymentLimitType.WHATSAPP_PAYMENT),
            ),
            usage = WalletLimitsUsage(
                automaticPix = 0L,
                daily = 500,
                nighttime = 200,
                monthly = MonthlyLimitUsage(used = 2_000, forecasted = 6_000),
            ),
            editable = true,
        ).right()
    }

    @Test
    fun `ao buscar limites com uso, durante o dia, não deve retornar o limite noturno gasto`() {
        val timestampDuringDay = timestamp.withHour(12)
        every { getZonedDateTime() }.returns(timestampDuringDay)

        every { walletRepository.findAllLimits(any()) } returns StoredWalletPaymentLimits(
            automaticPix = buildLimit(1_000, DailyPaymentLimitType.AUTOMATIC_PIX),
            daily = buildLimit(1_000, DailyPaymentLimitType.DAILY),
            nighttime = buildLimit(1_000, DailyPaymentLimitType.NIGHTTIME),
            monthly = null,
            whatsAppPayment = buildLimit(100, DailyPaymentLimitType.WHATSAPP_PAYMENT),
        )

        every {
            billRepository.getTotalPaid(
                walletId = any(),
                startDate = eq(nightTimestamp),
                endDate = eq(endNightTimestamp),
                types = any(),
            )
        }.returns(200)

        val result = service.getAllWalletLimitsWithUsage(wallet.id, account.accountId).getOrElse { return }

        result.limits.nighttime.activeAmount shouldBe 1_000
        result.usage.nighttime shouldBe 0
    }

    @Test
    fun `ao buscar limites com uso, usuario sem limite mensal, não deve retornar o limite mensal`() {
        every { walletRepository.findAllLimits(any()) }.returns(
            StoredWalletPaymentLimits(
                automaticPix = buildLimit(1_000, DailyPaymentLimitType.AUTOMATIC_PIX),
                daily = buildLimit(1_000, DailyPaymentLimitType.DAILY),
                nighttime = buildLimit(1_000, DailyPaymentLimitType.NIGHTTIME),
                monthly = null,
                whatsAppPayment = buildLimit(100, DailyPaymentLimitType.WHATSAPP_PAYMENT),
            ),
        )

        val result = service.getAllWalletLimitsWithUsage(wallet.id, account.accountId).getOrElse { return }

        result.limits.monthly shouldBe null
        result.usage.monthly shouldBe null
    }

    @Test
    fun `ao resetar os limites do usuário deve deixar as configurações corretas`() {
        every { walletRepository.saveLimit(any(), any(), any(), any()) } returns mockk()
        every { walletRepository.deleteMonthlyLimit(any()) } just Runs
        every { walletRepository.findWallets(any()) }.returns(listOf(wallet))

        service.setDefaultFullAccountLimits(accountId = account.accountId)

        verify {
            walletRepository.saveLimit(
                walletId = wallet.id,
                type = DailyPaymentLimitType.DAILY,
                currentAmount = 25_000_00L,
                lastAmount = null,
            )

            walletRepository.saveLimit(
                walletId = wallet.id,
                type = DailyPaymentLimitType.NIGHTTIME,
                currentAmount = 1_000_00L,
                lastAmount = null,
            )

            walletRepository.deleteMonthlyLimit(
                walletId = wallet.id,
            )
        }
    }

    @Test
    fun `membro founder deve poder atualizar o limite da carteira`() {
        val nextAmount = 40_000_00L

        every { walletRepository.saveLimit(any(), any(), any(), any()) } returns DailyPaymentLimit(
            updatedAt = getZonedDateTime(),
            amount = nextAmount,
            lastAmount = 30_000_00,
            type = DailyPaymentLimitType.DAILY,
        )

        val result = service.updateLimit(
            accountId = account.accountId,
            walletId = wallet.id,
            type = DailyPaymentLimitType.DAILY,
            amount = nextAmount,
            source = ActionSource.Api(accountId = account.accountId),
        ).getOrElse { fail("") }

        result.isPending.shouldBeTrue()
        result.nextAmount shouldBe nextAmount
    }

    @Test
    fun `membro co-founder não deve poder atualizar o limite da carteira de PF`() {
        val nextAmount = 40_000_00L

        every { accountService.findAccountById(any()) }.returns(walletFixture.participantAccount)

        val result = service.updateLimit(
            accountId = coFounderAccount.accountId,
            walletId = wallet.id,
            type = DailyPaymentLimitType.DAILY,
            amount = nextAmount,
            source = ActionSource.Api(accountId = coFounderAccount.accountId),
        )

        result.isLeft().shouldBeTrue()
        result.mapLeft {
            it.shouldBeTypeOf<UpdateLimitsErrors.UpdateLimitNotAllowed>()
        }
    }

    @Test
    fun `membro co-founder deve poder atualizar o limite da carteira de PJ`() {
        val nextAmount = 40_000_00L

        every { walletRepository.findWalletOrNull(any()) }.returns(legalPersonWallet)
        every { accountService.findAccountById(any()) }.returns(walletFixture.participantAccount)
        every { walletRepository.findWallet(any()) } returns legalPersonWallet
        every { walletRepository.saveLimit(any(), any(), any(), any()) } returns DailyPaymentLimit(
            updatedAt = getZonedDateTime(),
            amount = nextAmount,
            lastAmount = 30_000_00,
            type = DailyPaymentLimitType.DAILY,
        )

        val result = service.updateLimit(
            accountId = coFounderAccount.accountId,
            walletId = legalPersonWallet.id,
            type = DailyPaymentLimitType.DAILY,
            amount = nextAmount,
            source = ActionSource.Api(accountId = coFounderAccount.accountId),
        ).getOrElse { fail("") }

        result.isPending.shouldBeTrue()
        result.nextAmount shouldBe nextAmount
    }

    @Test
    fun `deve dar erro ao tentar alterar o limite de uma conta básica via api`() {
        val nextAmount = 40_000_00L

        every { accountService.findAccountById(any()) }.returns(walletFixture.founderAccount.copy(type = UserAccountType.BASIC_ACCOUNT))

        val result = service.updateLimit(
            accountId = walletFixture.founderAccount.accountId,
            walletId = wallet.id,
            type = DailyPaymentLimitType.DAILY,
            amount = nextAmount,
            source = ActionSource.Api(accountId = coFounderAccount.accountId),
        )

        result.isLeft().shouldBeTrue()
        result.mapLeft {
            it.shouldBeTypeOf<UpdateLimitsErrors.UpdateLimitNotAllowed>()
        }
    }

    @Test
    fun `deve dar sucesso ao tentar alterar o limite de uma conta básica via sistema`() {
        val nextAmount = 40_000_00L

        every { accountService.findAccountById(any()) }.returns(walletFixture.founderAccount.copy(type = UserAccountType.BASIC_ACCOUNT))
        every { walletRepository.saveLimit(any(), any(), any(), any()) } returns DailyPaymentLimit(
            updatedAt = getZonedDateTime(),
            amount = nextAmount,
            lastAmount = 30_000_00,
            type = DailyPaymentLimitType.DAILY,
        )

        val result = service.updateLimit(
            accountId = walletFixture.founderAccount.accountId,
            walletId = wallet.id,
            type = DailyPaymentLimitType.DAILY,
            amount = nextAmount,
            source = ActionSource.System,
        )

        result.isRight().shouldBeTrue()
        result.map {
            it.isPending.shouldBeTrue()
            it.nextAmount shouldBe nextAmount
        }
    }

    @ParameterizedTest
    @CsvSource("10,90", "50,50", "13,87", "100,0", "101,-1", "150,-50", "0,100")
    fun `deve retornar o limite disponível que pode ser usado pelo assistente virtual`(totalPaid: Long, totalAvailable: Long) {
        every { billRepository.getTotalPaidBillsByScheduleSourceType(any(), any(), any(), any()) }.returns(totalPaid)
        every { walletRepository.findLimitOrNull(any(), any()) }.returns(buildLimit(100L, DailyPaymentLimitType.WHATSAPP_PAYMENT))

        service.getAvailableAssistantLimit(wallet.id, account.accountId) shouldBe totalAvailable
    }

    @Test
    fun `deve retornar limite máximo de assistente se o configurado for maior que o máximo`() {
        every { billRepository.getTotalPaidBillsByScheduleSourceType(any(), any(), any(), any()) }.returns(0L)
        every { walletRepository.findLimitOrNull(any(), any()) }.returns(buildLimit(MAX_WHATSAPP_PAYMENT_LIMIT_AMOUNT + 1L, DailyPaymentLimitType.WHATSAPP_PAYMENT))

        service.getAvailableAssistantLimit(wallet.id, account.accountId) shouldBe MAX_WHATSAPP_PAYMENT_LIMIT_AMOUNT
    }

    private fun buildLimit(amount: Long, type: DailyPaymentLimitType): DailyPaymentLimit {
        return DailyPaymentLimit(updatedAt = timestamp, amount = amount, type = type)
    }
}