package ai.friday.billpayment.app.usage

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.CreditCardConfiguration
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.fixture.AccountPaymentMethodFixture
import ai.friday.billpayment.fixture.CreditCardAccountPaymentMethodFixture
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.WalletFixture
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

internal class CreditCardUsageServiceTest {

    val transactionRepository: TransactionRepository = mockk()
    val accountService: AccountService = mockk()
    val walletService: WalletService = mockk()
    private val featureConfiguration: FeatureConfiguration = mockk(relaxed = true)

    private val creditCardUsageService = CreditCardUsageService(
        transactionRepository = transactionRepository,
        accountService = accountService,
        walletService = walletService,
        featureConfiguration = featureConfiguration,
        mediumRiskQuota = 100,
        fee = 4.0,
    )

    @Test
    fun `should return default value when the calculation it's disabled`() {
        every { featureConfiguration.creditCardQuota } returns false

        creditCardUsageService.calculateCreditCardUsage(AccountId(ACCOUNT_ID)) shouldBe unlimitedCreditCardUsage
    }

    @ParameterizedTest
    @EnumSource(RiskLevel::class)
    fun `deveria retornar a quota do usuário quando estiver na allowList`(risk: RiskLevel) {
        every { featureConfiguration.creditCardQuota } returns true
        every { accountService.findAccountById(AccountId(ACCOUNT_ID)) } returns ACCOUNT.copy(
            accountId = AccountId(ACCOUNT_ID),
            configuration = ACCOUNT.configuration.copy(
                creditCardConfiguration = CreditCardConfiguration(
                    quota = 1000,
                    allowedRisk = risk,
                ),
            ),
        )
        val walletFixture = WalletFixture(founderAccountId = AccountId(ACCOUNT_ID))
        every { walletService.findWallets(any()) } returns listOf(walletFixture.buildPrimaryWallet(walletFixture.founderAccount))
        every { accountService.findAccountPaymentMethodsByAccountId(AccountId(ACCOUNT_ID), any()) } returns listOf(AccountPaymentMethodFixture.create(id = "consequat", accountId = ACCOUNT_ID, method = CreditCardAccountPaymentMethodFixture.create(riskLevel = risk)))
        every { transactionRepository.findCreditCardUsage(any(), any(), any()) } returns TransactionAmount(
            itemAmount = TransactionItemAmount(
                paymentMethodId = AccountPaymentMethodId(value = "consequat"),
                totalAmount = 2064,
                feeAmount = 9175,
            ),
        )

        creditCardUsageService.calculateCreditCardUsage(AccountId(ACCOUNT_ID)).quota shouldBe 1000
    }

    @Test
    fun `deveria retornar a quota de risco médio quando o usuário não estiver na allowList e o risco do cartão for médio`() {
        every { featureConfiguration.creditCardQuota } returns true
        every { accountService.findAccountById(AccountId(ACCOUNT_ID_2)) } returns ACCOUNT.copy(
            accountId = AccountId(ACCOUNT_ID_2),
            configuration = ACCOUNT.configuration.copy(
                creditCardConfiguration = CreditCardConfiguration(
                    quota = 1000,
                ),
            ),
        )
        val walletFixture = WalletFixture(founderAccountId = AccountId(ACCOUNT_ID_2))
        every { walletService.findWallets(any()) } returns listOf(walletFixture.buildPrimaryWallet(walletFixture.founderAccount))
        every { accountService.findAccountPaymentMethodsByAccountId(AccountId(ACCOUNT_ID_2), any()) } returns listOf(AccountPaymentMethodFixture.create(id = "consequat", accountId = ACCOUNT_ID_2, method = CreditCardAccountPaymentMethodFixture.create(riskLevel = RiskLevel.MEDIUM)))
        every { transactionRepository.findCreditCardUsage(any(), any(), any()) } returns TransactionAmount(
            itemAmount = TransactionItemAmount(
                paymentMethodId = AccountPaymentMethodId(value = "consequat"),
                totalAmount = 2064,
                feeAmount = 9175,
            ),
        )

        creditCardUsageService.calculateCreditCardUsage(AccountId(ACCOUNT_ID_2)).quota shouldBe 100
    }

    @Test
    fun `deveria retornar a quota de risco alto quando o usuário não estiver na allowList e o risco do cartão for alto`() {
        every { featureConfiguration.creditCardQuota } returns true
        every { accountService.findAccountById(AccountId(ACCOUNT_ID_2)) } returns ACCOUNT.copy(
            accountId = AccountId(ACCOUNT_ID_2),
            configuration = ACCOUNT.configuration.copy(
                creditCardConfiguration = CreditCardConfiguration(
                    quota = 1000,
                ),
            ),
        )
        val walletFixture = WalletFixture(founderAccountId = AccountId(ACCOUNT_ID_2))
        every { walletService.findWallets(any()) } returns listOf(walletFixture.buildPrimaryWallet(walletFixture.founderAccount))
        every { accountService.findAccountPaymentMethodsByAccountId(AccountId(ACCOUNT_ID_2), any()) } returns listOf(AccountPaymentMethodFixture.create(id = "consequat", accountId = ACCOUNT_ID_2, method = CreditCardAccountPaymentMethodFixture.create(riskLevel = RiskLevel.HIGH)))
        every { transactionRepository.findCreditCardUsage(any(), any(), any()) } returns TransactionAmount(
            itemAmount = TransactionItemAmount(
                paymentMethodId = AccountPaymentMethodId(value = "consequat"),
                totalAmount = 2064,
                feeAmount = 9175,
            ),
        )

        creditCardUsageService.calculateCreditCardUsage(AccountId(ACCOUNT_ID_2)).quota shouldBe 0
    }
}