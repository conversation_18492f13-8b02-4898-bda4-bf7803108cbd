package ai.friday.billpayment.app.chatbot

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillViewSchedule
import ai.friday.billpayment.app.bill.CreateBillService
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.bill.schedule.ScheduleBillService
import ai.friday.billpayment.app.integrations.BillRecurrenceRepository
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.integrations.NotificationService
import ai.friday.billpayment.app.integrations.UserEventService
import ai.friday.billpayment.app.itp.ITPService
import ai.friday.billpayment.app.notification.ButtonDeeplinkParameter
import ai.friday.billpayment.app.notification.NotificationMedia
import ai.friday.billpayment.app.notification.WhatsappNotification
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.BILL_ID
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.BILL_ID_3
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.common.runBlocking
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.http.MediaType
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.future.await
import org.junit.jupiter.api.Test

class ChatbotNotificationServiceTest {

    private val dynamoDB: AmazonDynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val itpService: ITPService = mockk(relaxed = true)
    private val onePixPayService: OnePixPayService = mockk(relaxed = true)
    private val accountService: AccountService = mockk(relaxed = true)

    val billIds = listOf(BillId(BILL_ID), BillId(BILL_ID_2), BillId(BILL_ID_3))
    val authorizationServerId = "1234-4567-8901-2345"
    val organizationsList = listOf(OtherInstitutionsResponseTO(id = authorizationServerId, title = "Banco", description = null))

    val walletFixture = WalletFixture()
    val wallet = walletFixture.buildWallet()
    val accountId = wallet.founder.accountId
    val walletId = wallet.id
    val findBillService = mockk<FindBillService>(relaxed = true) {
        every {
            find(walletId, any())
        } answers {
            getActiveBill(billId = secondArg())
        }

        every {
            findActiveAndWaitingApprovalBills(any())
        } returns listOf(
            getActiveBill(effectiveDueDate = getLocalDate().minusDays(4)),
            getActiveBill(effectiveDueDate = getLocalDate().minusDays(3)),
            getActiveBill(effectiveDueDate = getLocalDate().minusDays(1)),
            getActiveBill(effectiveDueDate = getLocalDate()),
            getActiveBill(effectiveDueDate = getLocalDate().plusDays(1)),
            getActiveBill(effectiveDueDate = getLocalDate().plusDays(10)),
            getActiveBill(
                effectiveDueDate = getLocalDate().plusDays(20),
                schedule = BillViewSchedule(date = getLocalDate()),
            ),
        )

        every { findBillsWaitingFunds(any()) } returns emptyList()
    }
    val updateBillService = mockk<UpdateBillService>()
    val walletService = mockk<WalletService>() {
        every {
            findAllFounderWallets(accountId)
        } returns listOf(wallet)
        every {
            findWallets(any())
        } returns listOf(wallet)
        every {
            findWallet(any())
        } returns wallet
    }

    val createBillService = mockk<CreateBillService>()
    val scheduleBillService = mockk<ScheduleBillService>()
    val recurrenceRepository = mockk<BillRecurrenceRepository>()
    val walletLimitsService = mockk<WalletLimitsService>()
    val systemActivityService = mockk<SystemActivityService>(relaxed = true)
    private val eventService: UserEventService = mockk(relaxed = true)
    private val notificationService: NotificationService = mockk(relaxed = true)

    private val chatbotNotificationService = DefaultChatbotNotificationService(
        accountRegisterRepository = mockk(),
        accountService = accountService,
        loginRepository = mockk(),
        notificationService = notificationService,
        eventService = eventService,
    )

    @Test
    fun `deve enviar notificacoes para todos os account ids da lista`() {
        val accountIds = listOf(AccountId("ACCOUNT-ID-1"), AccountId("ACCOUNT-ID-2"), AccountId("ACCOUNT-ID-3"))

        every { accountService.findAccountByIdOrNull(accountIds[0]) } returns ACCOUNT.copy(accountId = accountIds[0])
        every { notificationService.sendNotification(match { it.accountId == accountIds[0] }, any(), any()) } just Runs

        every { accountService.findAccountByIdOrNull(accountIds[1]) } returns null

        every { accountService.findAccountByIdOrNull(accountIds[2]) } returns ACCOUNT.copy(accountId = accountIds[2])
        every { notificationService.sendNotification(match { it.accountId == accountIds[2] }, any(), any()) } throws RuntimeException("error sending message")

        val result = chatbotNotificationService.sendNotifications(
            accountIds,
            ChatbotNotificationSpec(
                template = "test_template",
                configurationKey = "test_configuration_key",
                parameters = listOf("param1", "param2"),
                quickReplyButtonsWhatsAppParameter = listOf("payload1", "payload2"),
                buttonWhatsAppParameter = "test/deeplink",
                media = NotificationMedia.Image("image_url", MediaType.IMAGE_PNG),
                historyMessage = "history message",

            ),
        )

        val results = runBlocking { result.await() }

        results.size shouldBe 3
        results[0] shouldBe ChatbotNotificationResult.Success(accountIds[0])
        results[1] shouldBe ChatbotNotificationResult.NotFound(accountIds[1])
        results[2] shouldBe ChatbotNotificationResult.Error(accountIds[2], "error sending message")

        verify {
            notificationService.sendNotification(
                match { it.accountId == accountIds[0] },
                any(),
                withArg {
                    val notification = it as? WhatsappNotification
                    notification.shouldNotBeNull()

                    notification.accountId shouldBe accountIds[0]
                    notification.receiver shouldBe MobilePhone(ACCOUNT.mobilePhone)
                    notification.template.value shouldBe "test_template"
                    notification.parameters shouldBe listOf("param1", "param2")
                    notification.quickReplyButtonsWhatsAppParameter shouldBe listOf("payload1", "payload2")
                    notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("test/deeplink")
                    notification.media shouldBe NotificationMedia.Image("image_url", MediaType.IMAGE_PNG)
                },
            )
        }
    }
}