package ai.friday.billpayment.app.chatbot

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.forecast.BalanceForecast
import ai.friday.billpayment.app.forecast.BalanceForecastDates
import ai.friday.billpayment.app.forecast.ForecastPeriod
import ai.friday.billpayment.app.forecast.ForecastService
import ai.friday.billpayment.app.forecast.WalletBalanceForecast
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.PixQRCodeCreatorService
import ai.friday.billpayment.app.onepixpay.GenericTransactionId
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Called
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.util.UUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class ChatbotServiceTest {

    private val dynamoDB: AmazonDynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val enhancedClient = getDynamoDB()

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = accountRepository,
    )

    private val EXPECTED_TID = "CeCWhatsapp"

    private val accountService = AccountService(
        accountConfigurationService = mockk(),
        accountRepository = accountRepository,
        crmService = mockk(),
        chatbotMessagePublisher = mockk(),
        notificationAdapter = mockk(),
        walletRepository = walletRepository,
    )

    private val forecastService: ForecastService = mockk()
    private val pixQRCodeCreatorService: PixQRCodeCreatorService = mockk()
    private val walletService: WalletService = mockk()
    private val notificationAdapter: NotificationAdapter = mockk()

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet(
        walletFounder = walletFixture.founder.copy(
            accountId = AccountId(ACCOUNT_ID),
        ),
    )
    private val accountId = wallet.founder.accountId
    private val emailDomain = "fake.mail.com"

    private val chatbotService = ChatbotService(
        accountService = accountService,
        walletService = walletService,
        forecastService = forecastService,
        pixQRCodeCreatorService = pixQRCodeCreatorService,
        notificationAdapter = notificationAdapter,
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        every { walletService.walletEvpPixKey(any()) } returns null
        every { walletService.walletPixKey(any()) } returns PixKey(value = "${wallet.founder.document}@$emailDomain", type = PixKeyType.EMAIL)
    }

    @Test
    fun `deve retornar erro quando nao encontrar o account`() {
        every {
            walletService.findWallet(wallet.id)
        } returns wallet

        val createQrCode = chatbotService.createQrCode(accountId, wallet.id, ForecastPeriod.TODAY)

        createQrCode.isLeft() shouldBe true
        createQrCode.mapLeft {
            it shouldBe ChatbotQrCodeError.AccountNotFound
        }
    }

    @ParameterizedTest
    @EnumSource(value = AccountStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["ACTIVE"])
    fun `deve retornar erro quando o account nao estiver ativo`(status: AccountStatus) {
        loadAccountIntoDb(dynamoDB, accountId = wallet.founder.accountId, status = status.name)

        every {
            walletService.findWallet(wallet.id)
        } returns wallet

        val createQrCode = chatbotService.createQrCode(accountId, wallet.id, ForecastPeriod.TODAY)

        createQrCode.isLeft() shouldBe true
        createQrCode.mapLeft {
            it shouldBe ChatbotQrCodeError.AccountNotActive
        }
    }

    @ParameterizedTest
    @EnumSource(value = ForecastPeriod::class)
    fun `deve retornar o código pix copia e cola com o valor necessário para cobrir as contas do periodo selecionado`(
        forecastPeriod: ForecastPeriod,
    ) {
        loadAccountIntoDb(
            dynamoDB,
            accountId = accountId,
            defaultWalletId = wallet.id,
            name = wallet.founder.name,
            document = wallet.founder.document,
        )

        every {
            walletService.findWallet(wallet.id)
        } returns wallet

        every {
            forecastService.calculateWalletBalanceForecast(wallet)
        } returns WalletBalanceForecast(
            walletId = wallet.id,
            amount = 0,
            open = BalanceForecast(
                amountToday = 10,
                amountWeek = 10,
                amountFifteenDays = 10,
                amountThirtyDays = 10,
                amountMonth = 10,
                amountNextMonth = 10,
            ),
            scheduled = BalanceForecast(
                amountToday = 10,
                amountWeek = 10,
                amountFifteenDays = 10,
                amountThirtyDays = 10,
                amountMonth = 10,
                amountNextMonth = 10,
            ),
            dates = BalanceForecastDates(
                today = "",
                week = "",
                fifteenDays = "",
                month = "",
                thirtyDays = "",
                nextMonth = "",
            ),
            overdueAmount = 0,
        )

        val qrCodeValue = "QR-CODE-VALUE"

        every {
            pixQRCodeCreatorService.createQRCode(
                key = PixKey(value = "${wallet.founder.document}@$emailDomain", type = PixKeyType.EMAIL),
                document = wallet.founder.document,
                amount = any(),
                recipientName = wallet.founder.name,
                transactionId = GenericTransactionId(EXPECTED_TID),
            )
        } returns qrCodeValue

        val createQrCode =
            chatbotService.createQrCode(accountId, wallet.id, forecastPeriod)

        createQrCode.isRight() shouldBe true
        createQrCode.map {
            it.shouldBeTypeOf<ChatbotQrCodeResult>()
            it.qrCode.value shouldBe qrCodeValue
        }

        verify {
            forecastService.calculateWalletBalanceForecast(
                wallet = wallet,
            )
            ForecastPeriod.TODAY
        }
    }

    @Test
    fun `deve retornar o código pix copia e cola com a chave aleatória quando existir`() {
        loadAccountIntoDb(
            dynamoDB,
            accountId = accountId,
            defaultWalletId = wallet.id,
            name = wallet.founder.name,
            document = wallet.founder.document,
        )

        every {
            walletService.findWallet(wallet.id)
        } returns wallet

        val chaveAleatoria = UUID.randomUUID().toString()
        val qrCodeValue = "QR-CODE-VALUE"

        every {
            walletService.walletEvpPixKey(wallet.id)
        } returns PixKey(value = chaveAleatoria, type = PixKeyType.EVP)

        every {
            pixQRCodeCreatorService.createQRCode(
                key = PixKey(value = chaveAleatoria, type = PixKeyType.EVP),
                document = wallet.founder.document,
                amount = 990,
                recipientName = wallet.founder.name,
                transactionId = GenericTransactionId(EXPECTED_TID),
            )
        } returns qrCodeValue

        val createQrCode = chatbotService.createAmountQrCode(accountId, wallet.id, 990, "")

        verify {
            walletService.walletEvpPixKey(wallet.id)
        }

        createQrCode.isRight() shouldBe true
        createQrCode.map {
            it.shouldBeTypeOf<ChatbotAmountQrCodeResult>()
            it.qrCode.value shouldBe qrCodeValue
        }
    }

    @Test
    fun `deve retornar o código pix copia e cola`() {
        loadAccountIntoDb(
            dynamoDB,
            accountId = accountId,
            defaultWalletId = wallet.id,
            name = wallet.founder.name,
            document = wallet.founder.document,
        )

        every {
            walletService.findWallet(wallet.id)
        } returns wallet

        val qrCodeValue = "QR-CODE-VALUE"

        every {
            pixQRCodeCreatorService.createQRCode(
                key = PixKey(value = "${wallet.founder.document}@$emailDomain", type = PixKeyType.EMAIL),
                document = wallet.founder.document,
                amount = 990,
                recipientName = wallet.founder.name,
                transactionId = GenericTransactionId(EXPECTED_TID),
            )
        } returns qrCodeValue

        val createQrCode = chatbotService.createAmountQrCode(accountId, wallet.id, 990, "")

        createQrCode.isRight() shouldBe true
        createQrCode.map {
            it.shouldBeTypeOf<ChatbotAmountQrCodeResult>()
            it.qrCode.value shouldBe qrCodeValue
        }
    }

    @Test
    fun `deve retornar o código pix mesmo quando não for passado a carteira`() {
        loadAccountIntoDb(
            dynamoDB,
            accountId = accountId,
            defaultWalletId = wallet.id,
            name = wallet.founder.name,
            document = wallet.founder.document,
        )

        every {
            walletService.findWallets(accountId)
        } returns listOf(wallet)

        every {
            walletService.findWallet(wallet.id)
        } returns wallet

        every {
            forecastService.calculateWalletBalanceForecast(wallet)
        } returns WalletBalanceForecast(
            walletId = wallet.id,
            amount = 0,
            open = BalanceForecast(
                amountToday = 0,
                amountWeek = 0,
                amountFifteenDays = 0,
                amountThirtyDays = 0,
                amountMonth = 0,
                amountNextMonth = 0,
            ),
            scheduled = BalanceForecast(
                amountToday = 10,
                amountWeek = 0,
                amountFifteenDays = 0,
                amountThirtyDays = 0,
                amountMonth = 0,
                amountNextMonth = 0,
            ),
            dates = BalanceForecastDates(
                today = "",
                week = "",
                fifteenDays = "",
                month = "",
                thirtyDays = "",
                nextMonth = "",
            ),
            overdueAmount = 0,
        )

        every {
            pixQRCodeCreatorService.createQRCode(
                key = PixKey(value = "${wallet.founder.document}@$emailDomain", type = PixKeyType.EMAIL),
                document = wallet.founder.document,
                amount = 10,
                recipientName = wallet.founder.name,
                transactionId = GenericTransactionId(EXPECTED_TID),
            )
        } returns "qrcodevalue"

        val createQrCode =
            chatbotService.createQrCode(accountId = accountId, forecastPeriod = ForecastPeriod.TODAY)

        createQrCode.isRight() shouldBe true
        createQrCode.map {
            it.shouldBeTypeOf<ChatbotQrCodeResult>()
            it.qrCode.value shouldBe "qrcodevalue"
        }
    }

    @Test
    fun `deve retornar erro quando o saldo for suficiente para pagar as contas`() {
        loadAccountIntoDb(
            dynamoDB,
            accountId = accountId,
            defaultWalletId = wallet.id,
            name = wallet.founder.name,
            document = wallet.founder.document,
        )

        every {
            walletService.findWallet(wallet.id)
        } returns wallet

        every {
            forecastService.calculateWalletBalanceForecast(wallet)
        } returns WalletBalanceForecast(
            walletId = wallet.id,
            amount = 10,
            open = BalanceForecast(
                amountToday = 5,
                amountWeek = 0,
                amountFifteenDays = 0,
                amountThirtyDays = 0,
                amountMonth = 0,
                amountNextMonth = 0,
            ),
            scheduled = BalanceForecast(
                amountToday = 5,
                amountWeek = 0,
                amountFifteenDays = 0,
                amountThirtyDays = 0,
                amountMonth = 0,
                amountNextMonth = 0,
            ),
            dates = BalanceForecastDates(
                today = "",
                week = "",
                fifteenDays = "",
                month = "",
                thirtyDays = "",
                nextMonth = "",
            ),
            overdueAmount = 0,
        )

        val createQrCode = chatbotService.createQrCode(accountId, wallet.id, ForecastPeriod.TODAY)

        createQrCode.isLeft() shouldBe true
        createQrCode.mapLeft {
            it shouldBe ChatbotQrCodeError.SufficientBalance
        }

        verify {
            pixQRCodeCreatorService wasNot called
        }
    }

    @Test
    fun `deve retornar erro quando não encontrar a carteira`() {
        loadAccountIntoDb(
            dynamoDB,
            accountId = accountId,
            defaultWalletId = wallet.id,
            name = wallet.founder.name,
            document = wallet.founder.document,
        )

        every {
            walletService.findWallet(wallet.id)
        } throws ItemNotFoundException(wallet.id)

        val createQrCode = chatbotService.createQrCode(accountId, wallet.id, ForecastPeriod.TODAY)

        createQrCode.isLeft() shouldBe true
        createQrCode.mapLeft {
            it shouldBe ChatbotQrCodeError.WalletNotFound
        }

        verify {
            pixQRCodeCreatorService wasNot called
        }
    }

    @Test
    fun `deve retornar erro quando o usuário não for membro da carteira`() {
        loadAccountIntoDb(
            dynamoDB,
            accountId = AccountId("mocked-account-id"),
            defaultWalletId = WalletId("mocked-account-id"),
        )

        loadAccountIntoDb(
            dynamoDB,
            accountId = wallet.founder.accountId,
            defaultWalletId = wallet.id,
        )

        every {
            walletService.findWallet(wallet.id)
        } returns wallet

        val createQrCode =
            chatbotService.createQrCode(AccountId("mocked-account-id"), wallet.id, ForecastPeriod.TODAY)

        createQrCode.isLeft() shouldBe true
        createQrCode.mapLeft {
            it shouldBe ChatbotQrCodeError.UserNotAllowed
        }

        verify {
            pixQRCodeCreatorService wasNot called
        }
    }

    @Test
    fun `deve enviar a notificacão quando o saldo for insuficiente`() {
        every {
            forecastService.calculateWalletBalanceForecast(any())
        } returns WalletBalanceForecast(
            walletId = wallet.id,
            amount = 100,
            open = BalanceForecast(
                amountToday = 250,
                amountWeek = 0,
                amountFifteenDays = 0,
                amountThirtyDays = 0,
                amountMonth = 0,
                amountNextMonth = 0,
            ),
            scheduled = BalanceForecast(
                amountToday = 250,
                amountWeek = 0,
                amountFifteenDays = 0,
                amountThirtyDays = 0,
                amountMonth = 0,
                amountNextMonth = 0,
            ),
            dates = BalanceForecastDates(
                today = "",
                week = "",
                fifteenDays = "",
                month = "",
                thirtyDays = "",
                nextMonth = "",
            ),
            overdueAmount = 0,
        )

        every {
            walletService.findWallets(any())
        } returns listOf(wallet)

        chatbotService.notifyBalance(AccountId(ACCOUNT_ID))

        verify {
            notificationAdapter.notifyInsufficientBalanceToday(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        }
    }

    @Test
    fun `não deve enviar a notificacao quando o saldo for suficiente`() {
        every {
            forecastService.calculateWalletBalanceForecast(any())
        } returns WalletBalanceForecast(
            walletId = wallet.id,
            amount = 500,
            open = BalanceForecast(
                amountToday = 500,
                amountWeek = 0,
                amountFifteenDays = 0,
                amountThirtyDays = 0,
                amountMonth = 0,
                amountNextMonth = 0,
            ),
            scheduled = BalanceForecast(
                amountToday = 0,
                amountWeek = 0,
                amountFifteenDays = 0,
                amountThirtyDays = 0,
                amountMonth = 0,
                amountNextMonth = 0,
            ),
            dates = BalanceForecastDates(
                today = "",
                week = "",
                fifteenDays = "",
                month = "",
                thirtyDays = "",
                nextMonth = "",
            ),
            overdueAmount = 0,
        )

        every {
            walletService.findWallets(any())
        } returns listOf(wallet)

        chatbotService.notifyBalance(AccountId(ACCOUNT_ID))

        verify {
            notificationAdapter wasNot Called
        }
    }
}