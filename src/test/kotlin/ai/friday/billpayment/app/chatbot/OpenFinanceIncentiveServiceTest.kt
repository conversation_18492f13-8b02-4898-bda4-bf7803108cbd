package ai.friday.billpayment.app.chatbot

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.FindError
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.account.SystemActivityType
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.OpenFinanceConsentService
import ai.friday.billpayment.app.integrations.OpenFinanceIncentiveType
import ai.friday.billpayment.app.integrations.SweepingParticipant
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.fixture.AccountPaymentMethodFixture
import ai.friday.billpayment.getPaidBill
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.internalBankAccount
import arrow.core.left
import arrow.core.right
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

class OpenFinanceIncentiveServiceTest {
    private val accountService: AccountService = mockk(relaxed = true)
    private val walletService: WalletService = mockk(relaxed = true)
    private val chatBotNotificationService: ChatbotNotificationService = mockk(relaxed = true)

    private val openFinanceConsentService: OpenFinanceConsentService = mockk(relaxed = true)

    private val systemActivityService: SystemActivityService = mockk {
        every { getSystemActivityFlag(any(), SystemActivityType.PromotedSweepingAccountOptOut) } returns false
    }

    private val billEventRepository: BillEventRepository = mockk(relaxed = true)

    private val billRepository: BillRepository = mockk(relaxed = true)

    private val service = OpenFinanceIncentiveService(accountService, walletService, chatBotNotificationService, openFinanceConsentService, systemActivityService, billEventRepository, billRepository)

    private val internalBankService = InternalBankAccount(
        accountType = AccountType.CHECKING,
        bankNo = 123L,
        routingNo = 123L,
        accountNo = 123L,
        accountDv = "3",
        bankAccountMode = BankAccountMode.PHYSICAL,
    )

    private val wallet = WalletFixture().buildWallet()

    private val bank = SweepingParticipant(
        id = "participant-id",
        name = "Banco",
        shortName = "Banco",
        compe = "987",
        ispb = "123",
    )

    @Test
    fun `deve enviar incentivo do open finance no cash in`() {
        every { accountService.findPhysicalAccountPaymentMethod(any(), any(), any()) } returns AccountPaymentMethodFixture.create(method = internalBankAccount).right()
        every { accountService.findAccountById(any()) } returns ACCOUNT
        every { openFinanceConsentService.getParticipants() } returns listOf(bank)

        service.processCashIn(CashInReceivedTO(AccountNumber("1233"), 123L, 123L, 123L))

        verify { chatBotNotificationService.notifyOpenFinanceIncentive(any(), OpenFinanceIncentiveType.CASH_IN, false, bank.name) }
    }

    @Test
    fun `não deve enviar se não oferecermos conexão pra esse banco`() {
        every { accountService.findPhysicalAccountPaymentMethod(any(), any(), any()) } returns AccountPaymentMethodFixture.create(method = internalBankAccount).right()
        every { accountService.findAccountById(any()) } returns ACCOUNT
        every { openFinanceConsentService.getParticipants() } returns listOf(bank.copy(ispb = "456"))

        service.processCashIn(CashInReceivedTO(AccountNumber("1233"), 123L, 123L, 123L))

        verify(exactly = 0) { chatBotNotificationService.notifyOpenFinanceIncentive(any(), OpenFinanceIncentiveType.CASH_IN, false, null) }
    }

    @Test
    fun `não deve quebrar se não encontrar a conta`() {
        every { accountService.findPhysicalAccountPaymentMethod(any(), any(), any()) } returns FindError.NotFound.left()

        service.processCashIn(CashInReceivedTO(AccountNumber("1233"), 123L, 123L, 123L))

        verify(exactly = 0) { chatBotNotificationService.notifyOpenFinanceIncentive(any(), OpenFinanceIncentiveType.CASH_IN, false, null) }
    }

    @Test
    fun `deve enviar incentivo ao processar DDA`() {
        val bill = Bill.build(billAdded.copy(walletId = wallet.id), billPaid.copy(walletId = wallet.id))
        val billView = getPaidBill(walletId = wallet.id, billId = bill.billId)

        every { walletService.findWallet(any()) } returns wallet
        every { accountService.findAccountById(any()) } returns ACCOUNT
        every { billEventRepository.getBillById(bill.billId) } returns bill.right()
        every { billRepository.findBill(bill.billId, wallet.id) } returns billView

        service.processDDA(bill.billId)

        verify { chatBotNotificationService.notifyOpenFinanceDDAIncentive(any(), wallet, false, bill = billView) }
    }
}