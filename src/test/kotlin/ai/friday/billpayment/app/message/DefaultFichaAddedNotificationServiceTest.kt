package ai.friday.billpayment.app.message

import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.notification.NotificationHintService
import ai.friday.billpayment.app.payment.DiscountData
import ai.friday.billpayment.app.payment.FineData
import ai.friday.billpayment.app.payment.InterestData
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.integration.WalletFixture
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class DefaultFichaAddedNotificationServiceTest {
    private lateinit var test: DefaultFichaAddedNotificationService

    private val isBillAddedNotificationEnabled: Boolean = true
    private lateinit var walletService: WalletService
    private lateinit var notificationAdapter: NotificationAdapter
    private lateinit var notificationHintService: NotificationHintService

    @BeforeEach
    fun setUp() {
        walletService = mockk()
        notificationAdapter = mockk(relaxUnitFun = true)
        notificationHintService = mockk {
            every { getBillCreated(any(), any()) } returns "Dica 1"
        }

        test = DefaultFichaAddedNotificationService(
            isBillAddedNotificationEnabled,
            walletService,
            notificationAdapter,
            notificationHintService,
        )
    }

    @Test
    fun `should call notifyBillCreated with DDA source on a wallet`() {
        every { walletService.findWallet(any()) } returns wallet

        val billEvent = billAddedFicha.copy(
            actionSource = ActionSource.DDA(accountId = wallet.founder.accountId),
            document = wallet.founder.document,
            fineData = FineData(),
            interestData = InterestData(),
            discountData = DiscountData(),
        )

        val result = test.notify(billEvent)

        result shouldBe BillEventNotificationResult.Success

        verify {
            notificationAdapter.notifyBillCreated(
                members = listOf(wallet.founder),
                payee = any(),
                amount = any(),
                dueDate = any(),
                billType = BillType.FICHA_COMPENSACAO,
                billId = billEvent.billId,
                billStatus = any(),
                walletId = wallet.id,
                walletName = wallet.name,
                author = wallet.founder,
                description = billAddedFicha.description,
                actionSource = ActionSource.DDA(wallet.founder.accountId),
                hint = "Dica 1",
            )
        }
//
        verify {
            notificationAdapter.notifyBillCreated(
                members = listOf(walletFixture.participant),
                payee = any(),
                amount = any(),
                dueDate = any(),
                billType = BillType.FICHA_COMPENSACAO,
                billStatus = any(),
                billId = billEvent.billId,
                walletId = wallet.id,
                walletName = wallet.name,
                author = wallet.founder,
                description = billAddedFicha.description,
                actionSource = ActionSource.DDA(wallet.founder.accountId),
                hint = null,
            )
        }
    }

    companion object {
        private val walletFixture = WalletFixture()
        private val wallet = walletFixture.buildWallet(
            otherMembers = listOf(
                walletFixture.participant,
                walletFixture.ultraLimitedParticipant,
            ),
        )
    }
}