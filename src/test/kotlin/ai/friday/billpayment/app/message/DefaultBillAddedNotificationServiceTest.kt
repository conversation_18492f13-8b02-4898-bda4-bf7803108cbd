package ai.friday.billpayment.app.message

import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.toEpochMillis
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.notification.NotificationHintService
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.canViewAll
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.integration.BillEventFixture
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.toArgumentsStream
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class DefaultBillAddedNotificationServiceTest {
    private lateinit var test: DefaultBillAddedNotificationService

    private val isBillAddedNotificationEnabled: Boolean = true
    private lateinit var walletService: WalletService
    private lateinit var notificationAdapter: NotificationAdapter
    private lateinit var notificationHintService: NotificationHintService

    @BeforeEach
    fun setUp() {
        walletService = mockk()
        notificationAdapter = mockk(relaxUnitFun = true)
        notificationHintService = mockk {
            every { getBillCreated(any(), any()) } returns "Dica 1"
        }

        test = DefaultBillAddedNotificationService(
            isBillAddedNotificationEnabled,
            walletService,
            notificationAdapter,
            notificationHintService,
        )
    }

    @Test
    fun `should skip notification when configuration is disabled`() {
        val result = DefaultBillAddedNotificationService(
            false,
            walletService,
            notificationAdapter,
            notificationHintService,
        ).notify(BillEventFixture.added())

        result shouldBe BillEventNotificationResult.Skip

        verify {
            walletService wasNot Called
            notificationAdapter wasNot Called
            notificationHintService wasNot Called
        }
    }

    @Test
    fun `não deve notificar quando a data de vencimento está a mais de 2 meses de distância`() {
        val billEvent = BillEventFixture.added(dueDate = getLocalDate().plusMonths(3).toEpochMillis())

        val result = test.notify(billEvent)

        result shouldBe BillEventNotificationResult.Skip

        verify {
            walletService wasNot Called
            notificationAdapter wasNot Called
            notificationHintService wasNot Called
        }
    }

    @ParameterizedTest
    @MethodSource("invalidActionSources")
    fun `should skip notification when action_source is`(source: ActionSource) {
        val result = test.notify(BillEventFixture.added(source = source))

        result shouldBe BillEventNotificationResult.Skip

        verify {
            walletService wasNot Called
            notificationAdapter wasNot Called
            notificationHintService wasNot Called
        }
    }

    @ParameterizedTest
    @MethodSource("apiSourceBillAdded")
    fun `should notify collaborator when bill has API source`(billAdded: BillAdded) {
        every { walletService.findWallet(any()) } returns wallet

        val actionSource = ActionSource.Api(accountId = wallet.founder.accountId)

        val billEvent = billAdded.copy(actionSource = actionSource)

        val result = test.notify(billEvent)

        result shouldBe BillEventNotificationResult.Success

        verify {
            notificationAdapter.notifyBillCreated(
                members = listOf(walletFixture.participant),
                payee = any(),
                amount = any(),
                dueDate = any(),
                billType = billEvent.billType,
                billId = billEvent.billId,
                any(),
                walletId = wallet.id,
                walletName = wallet.name,
                author = wallet.founder,
                description = billEvent.description,
                actionSource = actionSource,
            )
        }
    }

    @ParameterizedTest
    @MethodSource("apiSourceBillAdded")
    fun `should notify all members when bill has ConnectUtility source`(billAdded: BillAdded) {
        every { walletService.findWallet(any()) } returns wallet

        val actionSource = ActionSource.ConnectUtility(accountId = wallet.founder.accountId)
        val billEvent = billAdded.copy(actionSource = actionSource)

        val result = test.notify(billEvent)

        result shouldBe BillEventNotificationResult.Success

        verify {
            notificationAdapter.notifyBillCreated(
                members = wallet.allMembers.filter { it.type != MemberType.FOUNDER && it.canViewAll() },
                payee = any(),
                amount = any(),
                dueDate = any(),
                billType = billEvent.billType,
                billId = billEvent.billId,
                any(),
                walletId = wallet.id,
                walletName = wallet.name,
                author = wallet.founder,
                description = billEvent.description,
                actionSource = actionSource,
            )
            notificationAdapter.notifyBillCreated(
                members = listOf(wallet.founder),
                payee = any(),
                amount = any(),
                dueDate = any(),
                billType = billEvent.billType,
                billId = billEvent.billId,
                any(),
                walletId = wallet.id,
                walletName = wallet.name,
                author = wallet.founder,
                description = billEvent.description,
                hint = any(),
                actionSource = actionSource,
            )
        }
    }

    @Test
    fun `should call notifyBillCreated with WalletMailbox source with unknown author`() {
        every { walletService.findWallet(any()) } returns wallet

        val actionSource = ActionSource.WalletMailBox(from = "<EMAIL>", accountId = null)
        val billEvent = billAdded.copy(actionSource = actionSource, description = "")

        val result = test.notify(billEvent)

        result shouldBe BillEventNotificationResult.Success

        verify {
            notificationAdapter.notifyBillCreated(
                members = listOf(walletFixture.founder),
                payee = any(),
                amount = any(),
                dueDate = any(),
                billType = BillType.CONCESSIONARIA,
                billId = billEvent.billId,
                any(),
                walletId = wallet.id,
                walletName = wallet.name,
                author = null,
                description = null,
                actionSource = actionSource,
                hint = "Dica 1",
            )

            notificationAdapter.notifyBillCreated(
                members = listOf(walletFixture.participant),
                payee = any(),
                amount = any(),
                dueDate = any(),
                billType = BillType.CONCESSIONARIA,
                billId = billEvent.billId,
                any(),
                walletId = wallet.id,
                walletName = wallet.name,
                author = null,
                description = null,
                actionSource = actionSource,
                hint = null,
            )
        }
    }

    @Test
    fun `should call notifyBillCreated with WalletMailbox source with known author`() {
        every { walletService.findWallet(any()) } returns wallet

        val actionSource =
            ActionSource.WalletMailBox(from = "<EMAIL>", accountId = walletFixture.participant.accountId)
        val billEvent = billAdded.copy(actionSource = actionSource, description = "")

        val result = test.notify(billEvent)

        result shouldBe BillEventNotificationResult.Success

        verify {
            notificationAdapter.notifyBillCreated(
                members = listOf(walletFixture.founder),
                payee = any(),
                amount = any(),
                dueDate = any(),
                billType = BillType.CONCESSIONARIA,
                billId = billEvent.billId,
                any(),
                walletId = wallet.id,
                walletName = wallet.name,
                author = walletFixture.participant,
                description = null,
                actionSource = actionSource,
                hint = "Dica 1",
            )

            notificationAdapter.notifyBillCreated(
                members = listOf(walletFixture.participant),
                payee = any(),
                amount = any(),
                dueDate = any(),
                billType = BillType.CONCESSIONARIA,
                billId = billEvent.billId,
                any(),
                walletId = wallet.id,
                walletName = wallet.name,
                author = walletFixture.participant,
                description = null,
                actionSource = actionSource,
                hint = null,
            )
        }
    }

    companion object {
        private val walletFixture = WalletFixture()
        private val wallet = walletFixture.buildWallet(
            otherMembers = listOf(
                walletFixture.participant,
                walletFixture.ultraLimitedParticipant,
            ),
        )

        @JvmStatic
        fun invalidActionSources() = listOf(
            mockk<ActionSource.VirtualAssistant>(relaxed = true),
            mockk<ActionSource.WalletRecurrence>(relaxed = true),
            mockk<ActionSource.Subscription>(relaxed = true),
            mockk<ActionSource.SubscriptionRecurrence>(relaxed = true),
            mockk<ActionSource.System>(relaxed = true),
            mockk<ActionSource.Scheduled>(relaxed = true),
            mockk<ActionSource.DirectDebit>(relaxed = true),
        ).toArgumentsStream()

        @JvmStatic
        fun apiSourceBillAdded() = listOf(billAdded, invoiceAdded, pixAdded).toArgumentsStream()
    }
}