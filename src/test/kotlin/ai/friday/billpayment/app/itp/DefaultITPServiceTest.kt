package ai.friday.billpayment.app.itp

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.btg.BtgITPAdapter
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.ExternalBankAccount
import ai.friday.billpayment.app.banking.ExternalBankAccountService
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.forecast.BalanceForecast
import ai.friday.billpayment.app.forecast.BalanceForecastDates
import ai.friday.billpayment.app.forecast.ForecastPeriod
import ai.friday.billpayment.app.forecast.ForecastService
import ai.friday.billpayment.app.forecast.WalletBalanceForecast
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.onepixpay.OnePixPay
import ai.friday.billpayment.app.onepixpay.OnePixPayErrors
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.onepixpay.OnePixPayStatus
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.BILL_ID
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.ItpFixture
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalTime
import java.util.UUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.ValueSource

internal class DefaultITPServiceTest {

    private val externalBankAccount = ExternalBankAccount(
        document = Document(value = DOCUMENT),
        bankNo = 341,
        bankISPB = "********",
        routingNo = 8888,
        accountNo = 1111,
        accountDv = "1",
        accountType = "CACC",
    )

    private val btgAdapter: BtgITPAdapter = mockk()
    private val forecastService: ForecastService = mockk()
    private val accountService: AccountService = mockk() {
        every {
            findAccountById(any())
        } returns ACCOUNT.copy(
            configuration = ACCOUNT.configuration.copy(groups = listOf(AccountGroup.BETA)),
        )
        every {
            findAccountByDocumentOrNull(any())
        } returns ACCOUNT.copy(
            configuration = ACCOUNT.configuration.copy(groups = listOf(AccountGroup.BETA)),
        )
    }
    private val externalBankAccountService = mockk<ExternalBankAccountService>() {
        every {
            findAll(Document(ACCOUNT.document))
        } returns listOf(externalBankAccount)
        every {
            findLastUsed(Document(ACCOUNT.document))
        } returns externalBankAccount
    }

    private val pixKeyDomain = "@fake.ai"
    private val walletService: WalletService = mockk() {
        every {
            walletPixKey(any())
        } returns PixKey(value = "$DOCUMENT$pixKeyDomain", type = PixKeyType.EMAIL)
    }
    private val paymentIntentRepository: PaymentIntentRepository = mockk(relaxed = true)
    private val notificationAdapter: NotificationAdapter = mockk()
    private val onePixPayService: OnePixPayService = mockk()

    private val itpStaticInstitutions = mutableMapOf<String, ITPStaticInstitution>()

    private val itpService = DefaultITPService(
        itpAdapter = btgAdapter,
        accountService = accountService,
        walletService = walletService,
        forecastService = forecastService,
        externalBankAccountService = externalBankAccountService,
        paymentIntentRepository = paymentIntentRepository,
        notificationAdapter = notificationAdapter,
        onePixPayService = onePixPayService,
        itpStaticInstitutions = itpStaticInstitutions,
        itpStaticInstitutionsAlpha = emptyMap(),
    )

    private val wallet = WalletFixture().buildWallet()

    val externalOrganizations = listOf(
        ItpFixture.organizationTO(ispb = "1111", authorizationServerId = "1"),
        ItpFixture.organizationTO(ispb = "2222", authorizationServerId = "2"),
        ItpFixture.organizationTO(ispb = "3333", authorizationServerId = "3"),
        ItpFixture.organizationTO(ispb = "4444", authorizationServerId = "4"),
    )

    @Nested
    @DisplayName("listPaymentOrganizations")
    inner class ListPaymentOrganizations {

        @BeforeEach
        fun init() {
            itpStaticInstitutions.clear()
        }

        @Test
        fun `deve retornar a lista de instituições do btg adapter se a list de instituições estática estiver nula or não preenchida`() {
            every {
                btgAdapter.listFinancialInstitutions()
            } returns externalOrganizations.right()

            val financialInstitutions =
                itpService.listPaymentOrganizations(AccountId(ACCOUNT_ID)).getOrElse { fail(it.toString()) }

            financialInstitutions.otherFinancialInstitutions shouldHaveSize externalOrganizations.size
            financialInstitutions.otherFinancialInstitutions.map { it.id } shouldContainExactlyInAnyOrder externalOrganizations.map { it.authorizationServerId }
        }

        @Test
        fun `deve retornar somente as instituições da lista estática quando estiver preenchida`() {
            every {
                btgAdapter.listFinancialInstitutions()
            } returns externalOrganizations.right()

            itpStaticInstitutions.put(
                externalOrganizations[0].authorizationServerId,
                ITPStaticInstitution(ispb = externalOrganizations[0].ispb!!, title = "instituicao 1"),
            )

            itpStaticInstitutions.put(
                externalOrganizations[2].authorizationServerId,
                ITPStaticInstitution(ispb = externalOrganizations[2].ispb!!, title = "instituicao 3"),
            )

            itpStaticInstitutions.put(
                "outro-id",
                ITPStaticInstitution(ispb = "0000", title = "instituicao 4"),
            )

            val financialInstitutions =
                itpService.listPaymentOrganizations(AccountId(ACCOUNT_ID)).getOrElse { fail(it.toString()) }

            financialInstitutions.otherFinancialInstitutions shouldHaveSize 2
            financialInstitutions.otherFinancialInstitutions[0].id shouldBe externalOrganizations[0].authorizationServerId
            financialInstitutions.otherFinancialInstitutions[1].id shouldBe externalOrganizations[2].authorizationServerId
        }

        @Test
        fun `deve retornar a última instituição financeira válida para o fluxo de ITP`() {
            val now = getZonedDateTime()
            val today = now.toInstant().toEpochMilli()
            val yesterday = now.minusDays(1).toInstant().toEpochMilli()

            every { btgAdapter.listFinancialInstitutions() } returns externalOrganizations.right()
            every { externalBankAccountService.findAll(any()) } returns listOf(
                externalBankAccount.copy(bankISPB = "1234", lastUsed = today),
                externalBankAccount.copy(bankISPB = "1111", lastUsed = yesterday),
            )

            val financialInstitutions =
                itpService.listPaymentOrganizations((AccountId(ACCOUNT_ID))).getOrElse { fail(it.toString()) }

            financialInstitutions.size shouldBe 1
            financialInstitutions.result.map {
                it.value.bankISPB shouldBe "1111"
            }
        }

        @Test
        fun `deve retornar lista vazia quando a instituição do usuário não estiver apta ao ITP`() {
            every { btgAdapter.listFinancialInstitutions() } returns externalOrganizations.right()
            every { externalBankAccountService.findAll(any()) } returns listOf(externalBankAccount)

            val financialInstitutions =
                itpService.listPaymentOrganizations((AccountId(ACCOUNT_ID))).getOrElse { fail(it.toString()) }

            financialInstitutions.size shouldBe 0
        }
    }

    @Nested
    @DisplayName("createPaymentIntent")
    inner class CreatePaymentIntent {

        private val createPaymentIntentCommand = CreatePaymentIntentCommand(
            accountId = AccountId(ACCOUNT_ID),
            walletId = WalletId(WALLET_ID),
            authorizationServerId = "Authorization-server-fake",
        )
        private val forecastPeriod = ForecastPeriod.FIFTEEN_DAYS

        @ParameterizedTest
        @ValueSource(longs = [100, 200])
        fun `nao deve gerar o token caso o saldo for suficiente`(debit: Long) {
            every {
                walletService.findWallet(any())
            } returns wallet

            every {
                forecastService.calculateWalletBalanceForecast(any())
            } returns WalletBalanceForecast(
                walletId = wallet.id,
                amount = 200,
                open = BalanceForecast(
                    amountToday = debit,
                    amountWeek = 0,
                    amountFifteenDays = 0,
                    amountThirtyDays = 0,
                    amountMonth = 0,
                    amountNextMonth = 0,
                ),
                scheduled = BalanceForecast(
                    amountToday = 0,
                    amountWeek = 0,
                    amountFifteenDays = 0,
                    amountThirtyDays = 0,
                    amountMonth = 0,
                    amountNextMonth = 0,
                ),
                dates = BalanceForecastDates(
                    today = "",
                    week = "",
                    fifteenDays = "",
                    month = "",
                    thirtyDays = "",
                    nextMonth = "",
                ),
                overdueAmount = 0,
            )
            verify {
                paymentIntentRepository wasNot Called
            }

            val result = itpService.createPaymentIntent(createPaymentIntentCommand, forecastPeriod)
            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<ITPServiceError.SufficientBalance>()
            }
        }

        @Test
        fun `deve retornar sucesso e consultar o ispb no btg e armazenar o intent quando o saldo for insuficiente`() {
            val externalOrganization = externalOrganizations[1]

            every {
                btgAdapter.listFinancialInstitutions()
            } returns externalOrganizations.right()

            every {
                walletService.findWallet(any())
            } returns wallet

            every {
                forecastService.calculateWalletBalanceForecast(any())
            } returns WalletBalanceForecast(
                walletId = wallet.id,
                amount = 200,
                open = BalanceForecast(
                    amountToday = 0,
                    amountWeek = 0,
                    amountFifteenDays = 0,
                    amountThirtyDays = 0,
                    amountMonth = 0,
                    amountNextMonth = 0,
                ),
                scheduled = BalanceForecast(
                    amountToday = 0,
                    amountWeek = 0,
                    amountFifteenDays = 250,
                    amountThirtyDays = 0,
                    amountMonth = 0,
                    amountNextMonth = 0,
                ),
                dates = BalanceForecastDates(
                    today = "",
                    week = "",
                    fifteenDays = "",
                    month = "",
                    thirtyDays = "",
                    nextMonth = "",
                ),
                overdueAmount = 0,
            )

            every {
                paymentIntentRepository.save(any())
            } returnsArgument 0

            every {
                btgAdapter.listFinancialInstitutions()
            } returns externalOrganizations.right()

            val result = itpService.createPaymentIntent(
                createPaymentIntentCommand.copy(
                    authorizationServerId = externalOrganization.authorizationServerId,
                ),
                forecastPeriod,

            )
            verify {
                paymentIntentRepository.save(
                    withArg {
                        it.accountNo shouldBe externalBankAccount.accountNo
                        it.accountDv shouldBe externalBankAccount.accountDv
                        it.bankNo shouldBe externalBankAccount.bankNo
                        it.bankISPB shouldBe externalOrganization.ispb
                        it.status shouldBe PaymentIntentStatus.CREATED
                        it.details shouldBe PaymentIntentDetails.WithPixKey(pixKey = PixKey(value = "$<EMAIL>", type = PixKeyType.EMAIL), amount = 50)
                    },
                )
            }
            result.isRight() shouldBe true
        }

        @Test
        fun `deve gerar o link mesmo se o usuário ainda não tiver feito cash in com alguma conta de mesma titularidade`() {
            val externalOrganization = externalOrganizations[1]
            every {
                externalBankAccountService.findLastUsed(Document(ACCOUNT.document))
            } returns null

            every {
                btgAdapter.listFinancialInstitutions()
            } returns externalOrganizations.right()

            every {
                walletService.findWallet(any())
            } returns wallet

            every {
                forecastService.calculateWalletBalanceForecast(any())
            } returns WalletBalanceForecast(
                walletId = wallet.id,
                amount = 200,
                open = BalanceForecast(
                    amountToday = 0,
                    amountWeek = 0,
                    amountFifteenDays = 0,
                    amountThirtyDays = 0,
                    amountMonth = 0,
                    amountNextMonth = 0,
                ),
                scheduled = BalanceForecast(
                    amountToday = 0,
                    amountWeek = 0,
                    amountFifteenDays = 250,
                    amountThirtyDays = 0,
                    amountMonth = 0,
                    amountNextMonth = 0,
                ),
                dates = BalanceForecastDates(
                    today = "",
                    week = "",
                    fifteenDays = "",
                    month = "",
                    thirtyDays = "",
                    nextMonth = "",
                ),
                overdueAmount = 0,
            )

            every {
                paymentIntentRepository.save(any())
            } returnsArgument 0

            every {
                btgAdapter.listFinancialInstitutions()
            } returns externalOrganizations.right()

            val result = itpService.createPaymentIntent(
                createPaymentIntentCommand.copy(
                    authorizationServerId = externalOrganization.authorizationServerId,
                ),
                forecastPeriod,

            )
            verify {
                paymentIntentRepository.save(
                    withArg {
                        it.routingNo shouldBe 0
                        it.accountNo shouldBe 0
                        it.accountDv shouldBe ""
                        it.bankNo shouldBe null
                        it.bankISPB shouldBe externalOrganization.ispb
                        it.status shouldBe PaymentIntentStatus.CREATED
                        it.details shouldBe PaymentIntentDetails.WithPixKey(pixKey = PixKey(value = "$<EMAIL>", type = PixKeyType.EMAIL), amount = 50)
                    },
                )
            }
            result.isRight() shouldBe true
        }
    }

    @Nested
    @DisplayName("createPaymentIntentWithOnePixPay")
    inner class CreatePaymentIntentWithOnePixPay {

        private val createPaymentIntentCommand = CreatePaymentIntentCommand(
            accountId = AccountId(ACCOUNT_ID),
            walletId = WalletId(WALLET_ID),
            authorizationServerId = "Authorization-server-fake",
        )

        val bills = listOf(getActiveBill(billId = BillId(BILL_ID)))

        private val onePixPay = OnePixPay(
            walletId = wallet.id,
            billIds = bills.map { it.billId },
            qrCode = "qrcode",
            status = OnePixPayStatus.CREATED,
            paymentLimitTime = LocalTime.MAX,
            fundsReceived = false,
            fundsReceivedAt = null,
            created = getZonedDateTime(),
        )

        @Test
        fun `deve falhar se o OPP falhar`() {
            every {
                onePixPayService.create(any(), any())
            } returns OnePixPayErrors.InvalidAmount.left()

            val response = itpService.createPaymentIntentWithOnePixPay(createPaymentIntentCommand, listOf(getActiveBill(billId = BillId(BILL_ID))), false)
            response.isLeft().shouldBeTrue()
            response.mapLeft {
                it.shouldBeTypeOf<ITPServiceError.SufficientBalance>()
            }
        }

        @Test
        fun `deve gerar o PaymentIntent com o QRCode retornardo do OPP`() {
            val externalOrganization = externalOrganizations[3]
            every {
                onePixPayService.create(bills, false)
            } returns onePixPay.right()

            every {
                onePixPayService.save(any())
            } answers { firstArg<OnePixPay>() }

            every {
                btgAdapter.listFinancialInstitutions()
            } returns externalOrganizations.right()

            val response = itpService.createPaymentIntentWithOnePixPay(createPaymentIntentCommand.copy(authorizationServerId = externalOrganization.authorizationServerId), bills, false)
            response.isRight().shouldBeTrue()

            verify {
                paymentIntentRepository.save(
                    withArg {
                        it.routingNo shouldBe externalBankAccount.routingNo
                        it.accountNo shouldBe externalBankAccount.accountNo
                        it.accountDv shouldBe externalBankAccount.accountDv
                        it.bankNo shouldBe externalBankAccount.bankNo
                        it.bankISPB shouldBe externalOrganization.ispb
                        it.bankISPB shouldBe externalOrganization.ispb
                        it.details shouldBe PaymentIntentDetails.WithQRCode(qrCode = onePixPay.qrCode)
                    },
                )
                onePixPayService.save(
                    withArg {
                        it.id shouldBe onePixPay.id
                        it.status shouldBe OnePixPayStatus.REQUESTED
                    },
                )
            }
        }

        @Test
        fun `deve gerar o PaymentIntent com o QRCode retornardo do OPP mesmo que o usuário não tenha feito deposito anteriormente`() {
            val externalOrganization = externalOrganizations[3]
            every {
                externalBankAccountService.findLastUsed(Document(ACCOUNT.document))
            } returns null

            every {
                onePixPayService.create(bills, false)
            } returns onePixPay.right()

            every {
                onePixPayService.save(any())
            } answers { firstArg<OnePixPay>() }

            every {
                btgAdapter.listFinancialInstitutions()
            } returns externalOrganizations.right()

            val response = itpService.createPaymentIntentWithOnePixPay(createPaymentIntentCommand.copy(authorizationServerId = externalOrganization.authorizationServerId), bills, false)
            response.isRight().shouldBeTrue()

            verify {
                paymentIntentRepository.save(
                    withArg {
                        it.routingNo shouldBe 0
                        it.accountNo shouldBe 0
                        it.accountDv shouldBe ""
                        it.bankNo shouldBe null
                        it.bankISPB shouldBe externalOrganization.ispb
                        it.details shouldBe PaymentIntentDetails.WithQRCode(qrCode = onePixPay.qrCode)
                    },
                )
            }
        }
    }

    @Nested
    @DisplayName("retrievePaymentRedirectUrl")
    inner class RetrievePaymentRedirectUrl {

        private val forecastPeriod = ForecastPeriod.TODAY
        private val includeScheduledBillsOnly = false
        private val paymentIntent = PaymentIntent(
            accountId = wallet.founder.accountId,
            walletId = wallet.id,
            document = externalBankAccount.document.value,
            authorizationServerId = UUID.randomUUID().toString(),
            authorizationServerName = "nome-do-banco-qualquer",
            routingNo = externalBankAccount.routingNo,
            accountNo = externalBankAccount.accountNo,
            accountDv = externalBankAccount.accountDv,
            bankISPB = externalBankAccount.bankISPB,
            accountType = AccountType.CHECKING,
            bankNo = externalBankAccount.bankNo,
            forecastPeriod = forecastPeriod,
            includeScheduledBillsOnly = includeScheduledBillsOnly,
            details = PaymentIntentDetails.WithPixKey(pixKey = PixKey(value = "$<EMAIL>", type = PixKeyType.EMAIL), amount = 1),
            status = PaymentIntentStatus.CREATED,
        )

        @BeforeEach
        fun init() {
            every { paymentIntentRepository.find(any()) } returns paymentIntent
            every { walletService.findWalletOrNull(any()) } returns wallet
            every { forecastService.calculateWalletBalanceForecast(any()) } returns WalletBalanceForecast(
                walletId = wallet.id,
                amount = 500,
                open = BalanceForecast(
                    amountToday = 1000,
                    amountWeek = 0,
                    amountFifteenDays = 0,
                    amountThirtyDays = 0,
                    amountMonth = 0,
                    amountNextMonth = 0,
                ),
                scheduled = BalanceForecast(
                    amountToday = 0,
                    amountWeek = 0,
                    amountFifteenDays = 0,
                    amountThirtyDays = 0,
                    amountMonth = 0,
                    amountNextMonth = 0,
                ),
                dates = BalanceForecastDates(
                    today = "",
                    week = "",
                    fifteenDays = "",
                    month = "",
                    thirtyDays = "",
                    nextMonth = "",
                ),
                overdueAmount = 0,
            )
        }

        @Test
        fun `deve retornar PaymentIntentNotFound se o payment intent não for encontrado`() {
            every { paymentIntentRepository.find(any()) } throws ItemNotFoundException("FAKE")
            val result = itpService.retrievePaymentRedirectUrl(
                PaymentIntentId("token"),
            )
            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<ITPServiceError.PaymentIntentNotFound>()
            }
        }

        @Test
        fun `deve retornar PaymentIntentNotFound se o payment intent estiver concluido`() {
            every { paymentIntentRepository.find(any()) } returns paymentIntent.copy(status = PaymentIntentStatus.SUCCESS)
            every { walletService.findWalletOrNull(any()) } returns null
            val result = itpService.retrievePaymentRedirectUrl(
                PaymentIntentId("token"),
            )

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<ITPServiceError.PaymentIntentNotFound>()
            }
        }

        @Test
        fun `deve retornar AccountNotFound se o usuário não for encontrado`() {
            every {
                accountService.findAccountByDocumentOrNull(any())
            } returns null

            val result = itpService.retrievePaymentRedirectUrl(
                PaymentIntentId("token"),
            )
            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<ITPServiceError.AccountNotFound>()
            }
        }

        @Test
        fun `deve retornar ServerError se falhar ao criar o redirect url`() {
            every { btgAdapter.createPaymentRedirectUrl(any()) } returns ITPServiceError.ITPErrorWithException.ITPProviderError(NoStackTraceException()).left()
            val result = itpService.retrievePaymentRedirectUrl(
                PaymentIntentId("token"),
            )
            result.isLeft() shouldBe true
            result.mapLeft { it.shouldBeTypeOf<ITPServiceError.ITPErrorWithException.ITPProviderError>() }
        }

        @Test
        fun `deve retornar not found se o payment intent já houver sido concluido`() {
            every { paymentIntentRepository.find(any()) } returns paymentIntent.copy(status = PaymentIntentStatus.SUCCESS)
            val result = itpService.retrievePaymentRedirectUrl(
                PaymentIntentId("token"),
            )
            result.isLeft().shouldBeTrue()
            result.mapLeft {
                it.shouldBeTypeOf<ITPServiceError.PaymentIntentNotFound>()
            }
        }

        @Test
        fun `deve retornar a url de redirecionamento e salvar consentId`() {
            every { btgAdapter.createPaymentRedirectUrl(any()) } returns PaymentIntentResponse(PaymentIntentId(), "url-de-redirecionamento", ConsentId("consent-id")).right()
            val result = itpService.retrievePaymentRedirectUrl(
                PaymentIntentId("token"),
            )
            result.isRight() shouldBe true
            result.map {
                it.url shouldBe "url-de-redirecionamento"
                it.consentId shouldBe ConsentId("consent-id")
            }
            verify {
                paymentIntentRepository.save(
                    withArg {
                        it.consentId shouldBe ConsentId("consent-id")
                    },
                )
            }
        }
    }

    @Nested
    @DisplayName("paymentIntentUpdated")
    inner class PaymentIntentUpdated {

        private val paymentIntentId = PaymentIntentId("ID-FAKE")

        @BeforeEach
        fun init() {
            every { paymentIntentRepository.find(any()) } returns PaymentIntent(
                paymentIntentId = paymentIntentId,
                accountId = AccountId(ACCOUNT_ID),
                walletId = WalletId(WALLET_ID),
                document = DOCUMENT,
                authorizationServerId = "Authorization-server-fake",
                authorizationServerName = "nome-do-banco",
                routingNo = 1234,
                accountNo = 4321,
                accountDv = "0",
                bankISPB = "1234",
                accountType = AccountType.CHECKING,
                bankNo = null,
                forecastPeriod = ForecastPeriod.FIFTEEN_DAYS,
                includeScheduledBillsOnly = false,
                details = PaymentIntentDetails.WithPixKey(pixKey = PixKey(value = "$<EMAIL>", type = PixKeyType.EMAIL), amount = 1),
                status = PaymentIntentStatus.CREATED,
            )
            every { notificationAdapter.notifyPaymentIntentFailed(any(), any()) } just Runs
        }

        @ParameterizedTest
        @EnumSource(PaymentIntentStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["FAILED"])
        fun `nao deve notificar quando nao for falha`(status: PaymentIntentStatus) {
            itpService.processPaymentIntentStatusChanged(paymentIntentId, status)

            verify {
                notificationAdapter wasNot Called
                paymentIntentRepository.save(
                    withArg {
                        it.status shouldBe status
                    },
                )
            }
        }

        @Test
        fun `deve notificar quando for falha`() {
            itpService.processPaymentIntentStatusChanged(paymentIntentId, PaymentIntentStatus.FAILED)

            verify {
                notificationAdapter.notifyPaymentIntentFailed(any(), paymentIntentId = paymentIntentId)
                paymentIntentRepository.save(
                    withArg {
                        it.status shouldBe PaymentIntentStatus.FAILED
                    },
                )
            }
        }
    }
}