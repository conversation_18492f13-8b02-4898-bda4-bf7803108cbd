package ai.friday.billpayment.app.payment.receipt

import ai.friday.billpayment.app.CompiledHtml
import io.kotest.matchers.equality.shouldBeEqualToComparingFields
import io.mockk.mockk
import org.junit.jupiter.api.Test

class EmptyReceiptTemplateCompilerTest {

    private val compiler = EmptyReceiptTemplateCompiler()

    @Test
    fun `deve retornar um objeto vazio ao solicitar o html de recibo por email`() {
        compiler.buildReceiptMailHtml(mockk()) shouldBeEqualToComparingFields CompiledHtml("")
    }

    @Test
    fun `deve retornar um objeto vazio ao solicitar o html de recibo`() {
        compiler.buildReceiptHtml(mockk(), mockk()) shouldBeEqualToComparingFields CompiledHtml("")
    }
}