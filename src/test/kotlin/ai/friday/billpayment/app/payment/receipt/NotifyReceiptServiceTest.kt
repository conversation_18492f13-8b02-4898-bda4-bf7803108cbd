package ai.friday.billpayment.app.payment.receipt

import DynamoDBUtils
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.celcoin.payerBank
import ai.friday.billpayment.adapters.celcoin.payerDocument
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.ReceiptDbRepository
import ai.friday.billpayment.adapters.dynamodb.ReceiptDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.handlebar.ReceiptHandlebarCompiler
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPayer
import ai.friday.billpayment.app.bill.PixCopyAndPaste
import ai.friday.billpayment.app.bill.PixQrCodeData
import ai.friday.billpayment.app.bill.PixQrCodeType
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.ReceiptFileRepository
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.BillPaymentReceipt
import ai.friday.billpayment.app.payment.BoletoReceiptData
import ai.friday.billpayment.app.payment.INVOICE_PURPOSE
import ai.friday.billpayment.app.payment.InvoiceReceiptData
import ai.friday.billpayment.app.payment.MultiplePaymentData
import ai.friday.billpayment.app.payment.PaymentData
import ai.friday.billpayment.app.payment.PixReceiptData
import ai.friday.billpayment.app.payment.ReceiptData
import ai.friday.billpayment.app.payment.ReceiptFilesData
import ai.friday.billpayment.app.payment.ReceiptPaymentData
import ai.friday.billpayment.app.payment.ReceiptPaymentType
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.SettlementOperation
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.receipt.builder.BoletoReceiptDataBuilder
import ai.friday.billpayment.app.payment.receipt.builder.InvoiceReceiptDataBuilder
import ai.friday.billpayment.app.payment.receipt.builder.PixReceiptDataBuilder
import ai.friday.billpayment.app.payment.receipt.builder.ReceiptDataBuilderService
import ai.friday.billpayment.app.payment.toPayer
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixKeyOwner
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.simpleName
import ai.friday.billpayment.balance
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.concessionariaBill
import ai.friday.billpayment.createBoletoSettlementResult
import ai.friday.billpayment.createEmailTemplatesConfiguration
import ai.friday.billpayment.createSinglePaymentDataWithBalance
import ai.friday.billpayment.createSinglePaymentDataWithCreditCard
import ai.friday.billpayment.creditCard
import ai.friday.billpayment.externalCreditCard
import ai.friday.billpayment.integration.CNPJ_1
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.DOCUMENT_2
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.NAME
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.integration.loadBalancePaymentMethod
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.invoiceBill
import ai.friday.billpayment.invoicePaid
import ai.friday.billpayment.invoicePaymentScheduled
import ai.friday.billpayment.invoicePaymentStarted
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.pixBill
import ai.friday.billpayment.pixKeyDetails
import ai.friday.billpayment.pixPaid
import ai.friday.billpayment.pixPaymentScheduled
import ai.friday.billpayment.pixPaymentStarted
import ai.friday.billpayment.withPixParticipants
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import arrow.core.Either
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.equality.shouldBeEqualToComparingFields
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.kotest.matchers.string.shouldNotContain
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.math.BigInteger
import java.time.Instant
import java.time.ZonedDateTime
import java.util.UUID
import java.util.stream.Stream
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

internal class NotifyReceiptServiceTest {

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer().also {
        createBillPaymentTable(it)
    }
    private val dynamoDbEnhancedClient = DynamoDBUtils.setupDynamoDB()
    private val receiptDynamoDAO = ReceiptDynamoDAO(dynamoDbEnhancedClient)
    private val accountDAO = AccountDynamoDAO(dynamoDbEnhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(dynamoDbEnhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(dynamoDbEnhancedClient)
    private val nsuDAO = NSUDynamoDAO(dynamoDbEnhancedClient)
    private val transactionDynamo = TransactionDynamo(dynamoDbEnhancedClient)

    private val accountDbRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )
    private val receiptFileRepository: ReceiptFileRepository = mockk {
        every { generateReceiptFiles(any(), any()) } returns ReceiptFilesData(imageBytes = "BYTEARRAY".toByteArray(), imageFormat = "png", pdfBytes = "BYTEARRAY".toByteArray(), imageUrl = "imageUrl", pdfUrl = "pdfUrl")
    }
    private val notificationAdapter: NotificationAdapter = mockk(relaxed = true)
    private val receiptRepository = ReceiptDbRepository(receiptDynamoDAO)

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet(
        otherMembers = listOf(
            walletFixture.participant,
            walletFixture.ultraLimitedParticipant,
        ),
    )
    private val walletService: WalletService = mockk() {
        every {
            findWallet(any())
        } returns wallet
    }

    private val concessionariaPaidBill = Bill.build(
        billAdded,
        billPaymentScheduled.copy(actionSource = ActionSource.Api(accountId = walletFixture.participant.accountId)),
        billPaymentStart,
        billPaid,
    )
    private val invoicePaidBill = Bill.build(
        invoiceAdded,
        invoicePaymentScheduled.copy(actionSource = ActionSource.Api(accountId = walletFixture.participant.accountId)),
        invoicePaymentStarted,
        invoicePaid,
    )
    private val pixPaidBill = Bill.build(
        pixAdded,
        pixPaymentScheduled.copy(actionSource = ActionSource.Api(accountId = walletFixture.participant.accountId)),
        pixPaymentStarted,
        pixPaid.copy(
            pixKeyDetails = pixKeyDetails.copy(
                holder = PixKeyHolder(
                    accountNo = BigInteger("1231234"),
                    accountDv = "0",
                    ispb = "1234",
                    institutionName = "Teste",
                    accountType = AccountType.CHECKING,
                    routingNo = 12,
                ),
            ),
        ),
    )
    private val authentication = "AAAAA"

    private val boletoTransaction = buildTransaction(
        transactionType = TransactionType.BOLETO_PAYMENT,
        amountTotal = concessionariaBill.amountTotal,
        bill = concessionariaBill,
        walletId = concessionariaBill.walletId,
        settlementOperation = createBoletoSettlementResult(),
    )

    private val creditCardTransaction = buildTransaction(
        transactionType = TransactionType.BOLETO_PAYMENT,
        amountTotal = concessionariaBill.amountTotal,
        bill = concessionariaBill,
        walletId = concessionariaBill.walletId,
        settlementOperation = createBoletoSettlementResult(),
        paymentData = createSinglePaymentDataWithCreditCard(
            accountPaymentMethod = externalCreditCard,
            netAmount = concessionariaBill.amountTotal,
            feeAmount = 0,
            installments = 1,
        ),
    )

    private val multiplePaymentTransaction = buildTransaction(
        transactionType = TransactionType.BOLETO_PAYMENT,
        amountTotal = concessionariaBill.amountTotal,
        bill = concessionariaBill,
        walletId = concessionariaBill.walletId,
        settlementOperation = createBoletoSettlementResult(),
        paymentData = MultiplePaymentData(
            listOf(
                createSinglePaymentDataWithCreditCard(
                    accountPaymentMethod = externalCreditCard,
                    netAmount = concessionariaBill.amountTotal - 1L,
                    feeAmount = 0,
                    installments = 1,
                ),
                createSinglePaymentDataWithBalance(balance, 1L),
            ),
        ),
    )

    private val pixTransaction = buildTransaction(
        transactionType = TransactionType.DIRECT_INVOICE,
        amountTotal = pixBill.amountTotal,
        bill = pixBill,
        walletId = pixBill.walletId,
    )

    private val invoiceTransaction = buildTransaction(
        transactionType = TransactionType.DIRECT_INVOICE,
        amountTotal = invoiceBill.amountTotal,
        bill = invoiceBill,
        walletId = invoiceBill.walletId,
    )

    private fun buildTransaction(
        transactionType: TransactionType,
        amountTotal: Long,
        bill: Bill,
        walletId: WalletId,
        settlementOperation: SettlementOperation? = null,
        paymentData: PaymentData? = null,
    ) = Transaction(
        type = transactionType,
        payer = ACCOUNT.toPayer(),
        paymentData = paymentData ?: createSinglePaymentDataWithBalance(balance, amountTotal),
        settlementData = SettlementData(
            bill,
            0,
            amountTotal,
            settlementOperation = settlementOperation ?: BankTransfer(
                operationId = BankOperationId(value = UUID.randomUUID().toString()),
                gateway = FinancialServiceGateway.ARBI,
                status = BankOperationStatus.SUCCESS,
                amount = amountTotal,
                authentication = "AUTH",
                errorDescription = "",
                debitOperationNumber = null,
                creditOperationNumber = null,
            ),
        ),
        nsu = 1,
        actionSource = ActionSource.System,
        status = TransactionStatus.COMPLETED,
        walletId = walletId,
    )

    private fun buildReceiptService(repository: TransactionRepository): DefaultNotifyReceiptService {
        val pixReceiptDataBuilder = PixReceiptDataBuilder(
            accountRepository = accountDbRepository,
            transactionRepository = repository,
        )

        val boletoReceiptDataBuilder = BoletoReceiptDataBuilder(
            transactionRepository = repository,
            boletoSettlementReceiptService = mockk {
                every { getReceipt(any()) } returns BillPaymentReceipt("", "1", authentication, "CELCOIN")
            },

        )

        val invoiceReceiptDataBuilder = InvoiceReceiptDataBuilder(
            accountRepository = accountDbRepository,
            transactionRepository = repository,
        )

        val receiptDataBuilderService = ReceiptDataBuilderService(
            receiptDataBuilder = listOf(
                pixReceiptDataBuilder,
                boletoReceiptDataBuilder,
                invoiceReceiptDataBuilder,
            ),
        )
        return DefaultNotifyReceiptService(
            billEventRepository = mockk {
                every { getBill(any(), billPaid.billId) } returns Either.Right(concessionariaPaidBill)
                every { getBill(any(), invoicePaid.billId) } returns Either.Right(invoicePaidBill)
                every { getBill(any(), pixPaid.billId) } returns Either.Right(pixPaidBill)
            },
            receiptDataBuilderService = receiptDataBuilderService,
            receiptRepository = receiptRepository,
            walletService = walletService,
            receiptDataSenders = listOf(
                DefaultReceiptDataSenderService(
                    notificationAdapter,
                    receiptFileRepository,
                    ReceiptTemplateCompilerFinderService(
                        receiptTemplateCompilers = listOf(
                            ReceiptHandlebarCompiler(createEmailTemplatesConfiguration()),
                        ),
                    ),
                    walletService,
                ),
            ),
            messagePublisher = mockk(),
        )
    }

    @Test
    fun `should process receipt on BillPaid with credit card`() {
        val receiptService = buildReceiptService(
            repository = mockk {
                every { findById(any()) } returns creditCardTransaction
            },
        )
        receiptService.notify(billPaid = billPaid)

        val receiptData =
            receiptRepository.findById(walletId = billPaid.walletId, billId = billPaid.billId)
        with(receiptData as BoletoReceiptData) {
            billId shouldBe concessionariaPaidBill.billId
            walletId shouldBe concessionariaPaidBill.walletId
            source shouldBe concessionariaPaidBill.source
            authentication shouldBe authentication
            dateTime shouldBe ZonedDateTime.ofInstant(
                Instant.ofEpochMilli(concessionariaPaidBill.paidDate!!),
                brazilTimeZone,
            )
            assignor shouldBe concessionariaPaidBill.assignor
            recipient shouldBe concessionariaPaidBill.recipient
            totalAmount shouldBe concessionariaPaidBill.amountTotal
            payer shouldBe concessionariaPaidBill.payer
            dueDate shouldBe concessionariaPaidBill.dueDate
            barcode shouldBe concessionariaPaidBill.barcode
            walletName shouldBe wallet.name
            scheduledBy shouldBe walletFixture.participant.simpleName()
            paymentPartnerName shouldBe "CELCOIN"
            transactionId shouldBe creditCardTransaction.id
            payments.shouldNotBeNull().first() shouldBeEqualToComparingFields ReceiptPaymentData(
                amount = concessionariaPaidBill.amountTotal,
                type = ReceiptPaymentType.CREDIT_CARD,
                accountPaymentMethodId = externalCreditCard.id,
            )
        }
        verify(exactly = 1) {
            receiptFileRepository.generateReceiptFiles(any(), any())
            notificationAdapter.notifyBoletoReceipt(any(), any(), any(), any())
        }
    }

    @Test
    fun `should process receipt on BillPaid with multiple payments`() {
        val receiptService = buildReceiptService(
            repository = mockk {
                every { findById(any()) } returns multiplePaymentTransaction
            },
        )
        receiptService.notify(billPaid = billPaid)

        val receiptData =
            receiptRepository.findById(walletId = billPaid.walletId, billId = billPaid.billId)
        with(receiptData as BoletoReceiptData) {
            billId shouldBe concessionariaPaidBill.billId
            walletId shouldBe concessionariaPaidBill.walletId
            source shouldBe concessionariaPaidBill.source
            authentication shouldBe authentication
            dateTime shouldBe ZonedDateTime.ofInstant(
                Instant.ofEpochMilli(concessionariaPaidBill.paidDate!!),
                brazilTimeZone,
            )
            assignor shouldBe concessionariaPaidBill.assignor
            recipient shouldBe concessionariaPaidBill.recipient
            totalAmount shouldBe concessionariaPaidBill.amountTotal
            payer shouldBe concessionariaPaidBill.payer
            dueDate shouldBe concessionariaPaidBill.dueDate
            barcode shouldBe concessionariaPaidBill.barcode
            walletName shouldBe wallet.name
            scheduledBy shouldBe walletFixture.participant.simpleName()
            paymentPartnerName shouldBe "CELCOIN"
            transactionId shouldBe multiplePaymentTransaction.id
            with(payments.shouldNotBeNull().shouldHaveSize(2)) {
                first() shouldBeEqualToComparingFields ReceiptPaymentData(
                    amount = concessionariaPaidBill.amountTotal - 1L,
                    type = ReceiptPaymentType.CREDIT_CARD,
                    accountPaymentMethodId = externalCreditCard.id,
                )
                last() shouldBeEqualToComparingFields ReceiptPaymentData(
                    amount = 1L,
                    type = ReceiptPaymentType.BALANCE,
                    accountPaymentMethodId = balance.id,
                )
            }
        }
        verify(exactly = 1) {
            receiptFileRepository.generateReceiptFiles(any(), any())
            notificationAdapter.notifyBoletoReceipt(any(), any(), any(), any())
        }
    }

    @Test
    fun `should process receipt and notify members when boleto bill in a wallet is paid`() {
        every {
            walletService.findWallet(billPaid.walletId)
        } returns wallet

        val receiptService = buildReceiptService(
            repository = mockk {
                every { findById(any()) } returns boletoTransaction
            },
        )
        receiptService.notify(billPaid = billPaid)

        val receiptData =
            receiptRepository.findById(walletId = billPaid.walletId, billId = billPaid.billId)
        with(receiptData as BoletoReceiptData) {
            billId shouldBe concessionariaPaidBill.billId
            walletId shouldBe concessionariaPaidBill.walletId
            source shouldBe concessionariaPaidBill.source
            authentication shouldBe authentication
            dateTime shouldBe ZonedDateTime.ofInstant(
                Instant.ofEpochMilli(concessionariaPaidBill.paidDate!!),
                brazilTimeZone,
            )
            assignor shouldBe concessionariaPaidBill.assignor
            recipient shouldBe concessionariaPaidBill.recipient
            totalAmount shouldBe concessionariaPaidBill.amountTotal
            payer shouldBe concessionariaPaidBill.payer
            dueDate shouldBe concessionariaPaidBill.dueDate
            barcode shouldBe concessionariaPaidBill.barcode
            walletName shouldBe wallet.name
            scheduledBy shouldBe walletFixture.participant.simpleName()
            paymentPartnerName shouldBe "CELCOIN"
            transactionId shouldBe boletoTransaction.id
            payments.shouldNotBeNull().first() shouldBeEqualToComparingFields ReceiptPaymentData(
                amount = concessionariaPaidBill.amountTotal,
                type = ReceiptPaymentType.BALANCE,
                accountPaymentMethodId = balance.id,
            )
        }
        verify(exactly = 1) {
            receiptFileRepository.generateReceiptFiles(any(), any())
            notificationAdapter.notifyBoletoReceipt(
                members = listOf(wallet.founder, walletFixture.participant),
                receiptData = receiptData,
                receiptFilesData = any(),
                mailReceiptHtml = any(),
            )
        }
    }

    @Test
    fun `should process receipt and notify members when direct invoice bill in a wallet is paid`() {
        every {
            walletService.findWallet(billPaid.walletId)
        } returns wallet

        loadAccountIntoDb(dynamoDB, accountId = wallet.founder.accountId)

        val receiptService = buildReceiptService(
            repository = mockk {
                every { findById(any()) } returns invoiceTransaction
            },
        )
        receiptService.notify(billPaid = invoicePaid)

        val receiptData =
            receiptRepository.findById(walletId = invoicePaid.walletId, billId = invoicePaid.billId)
        with(receiptData as InvoiceReceiptData) {
            billId shouldBe invoicePaidBill.billId
            walletId shouldBe invoicePaidBill.walletId
            source shouldBe invoicePaidBill.source
            authentication shouldBe "AUTH"
            dateTime shouldBe ZonedDateTime.ofInstant(Instant.ofEpochMilli(invoicePaidBill.paidDate!!), brazilTimeZone)
            recipient shouldBe invoicePaidBill.recipient
            totalAmount shouldBe invoicePaidBill.amountTotal
            payerBankAccount shouldBe balance.method
            payerFinancialInstitution shouldBe FinancialInstitution(name = "Banco Arbi S.A.", ispb = null, compe = 213)
            paymentPartnerName shouldBe null
            paymentPartnerDocument shouldBe null
            walletName shouldBe wallet.name
            scheduledBy shouldBe walletFixture.participant.simpleName()
            transactionId shouldBe invoiceTransaction.id
            payments.shouldNotBeNull().first() shouldBeEqualToComparingFields ReceiptPaymentData(
                amount = invoicePaidBill.amountTotal,
                type = ReceiptPaymentType.BALANCE,
                accountPaymentMethodId = balance.id,
            )
        }
        verify(exactly = 1) {
            receiptFileRepository.generateReceiptFiles(any(), any())
            notificationAdapter.notifyInvoiceReceipt(
                members = listOf(wallet.founder, walletFixture.participant),
                receiptData = receiptData,
                receiptFilesData = any(),
                mailReceiptHtml = any(),
            )
        }
    }

    @Test
    fun `should process direct invoice receipt on BillPaid on wallet`() {
        loadAccountIntoDb(dynamoDB, accountId = wallet.founder.accountId)

        val receiptService = buildReceiptService(
            repository = mockk {
                every { findById(any()) } returns invoiceTransaction
            },
        )
        receiptService.notify(billPaid = invoicePaid)

        val receiptData =
            receiptRepository.findById(walletId = invoicePaid.walletId, billId = invoicePaid.billId)
        with(receiptData as InvoiceReceiptData) {
            billId shouldBe invoicePaidBill.billId
            walletId shouldBe invoicePaidBill.walletId
            source shouldBe invoicePaidBill.source
            authentication shouldBe "AUTH"
            dateTime shouldBe ZonedDateTime.ofInstant(Instant.ofEpochMilli(invoicePaidBill.paidDate!!), brazilTimeZone)
            recipient shouldBe invoicePaidBill.recipient
            totalAmount shouldBe invoicePaidBill.amountTotal
            payerBankAccount shouldBe balance.method
            payerFinancialInstitution shouldBe FinancialInstitution(name = "Banco Arbi S.A.", ispb = null, compe = 213)
            paymentPartnerName shouldBe null
            paymentPartnerDocument shouldBe null
            walletName shouldBe wallet.name
            scheduledBy shouldBe walletFixture.participant.simpleName()
            transactionId shouldBe invoiceTransaction.id
            payments.shouldNotBeNull().first() shouldBeEqualToComparingFields ReceiptPaymentData(
                amount = invoicePaidBill.amountTotal,
                type = ReceiptPaymentType.BALANCE,
                accountPaymentMethodId = balance.id,
            )
        }
        verify(exactly = 1) {
            receiptFileRepository.generateReceiptFiles(any(), any())
            notificationAdapter.notifyInvoiceReceipt(any(), any(), any(), any())
        }
    }

    @Test
    fun `should process pix receipt on BillPaid`() {
        loadAccountIntoDb(dynamoDB, accountId = wallet.founder.accountId)
        val internalBankAccount = balance.method as InternalBankAccount
        loadBalancePaymentMethod(
            accountRepository = accountDbRepository,
            document = internalBankAccount.document,
            accountId = wallet.founder.accountId.value,
            paymentMethodId = balance.id.value,
            bankNo = internalBankAccount.bankNo,
            bankRoutingNo = internalBankAccount.routingNo,
            bankAccountNo = internalBankAccount.accountNo.toBigInteger(),
            bankAccountDv = internalBankAccount.accountDv,
        )
        val receiptService = buildReceiptService(
            repository = mockk {
                every { findById(any()) } returns pixTransaction
            },
        )

        val payerFinancialInstitution =
            FinancialInstitution(name = "Banco Arbi S.A.", ispb = null, compe = internalBankAccount.bankNo)
        val payeeFinancialInstitution = FinancialInstitution(name = "Itau S.A.", ispb = "********", compe = 341)
        withPixParticipants(
            payeeFinancialInstitution,
            payerFinancialInstitution,
        ) {
            receiptService.notify(billPaid = pixPaid)

            val receiptData =
                receiptRepository.findById(walletId = pixPaid.walletId, billId = pixPaid.billId)
            with(receiptData as PixReceiptData) {
                billId shouldBe pixPaidBill.billId
                walletId shouldBe pixPaidBill.walletId
                source shouldBe pixPaidBill.source
                authentication shouldBe "AUTH"
                dateTime shouldBe ZonedDateTime.ofInstant(Instant.ofEpochMilli(pixPaidBill.paidDate!!), brazilTimeZone)
                recipient shouldBe pixPaidBill.recipient
                totalAmount shouldBe pixPaidBill.amountTotal
                payerBankAccount shouldBe balance.method
                this.payerFinancialInstitution shouldBe payerFinancialInstitution
                payer shouldBe BillPayer(document = DOCUMENT, name = NAME)
                this.payeeFinancialInstitution shouldBe payeeFinancialInstitution
                walletName shouldBe wallet.name
                scheduledBy shouldBe walletFixture.participant.simpleName()
                transactionId shouldBe pixTransaction.id
                payments.shouldNotBeNull().first() shouldBeEqualToComparingFields ReceiptPaymentData(
                    amount = pixPaidBill.amountTotal,
                    type = ReceiptPaymentType.BALANCE,
                    accountPaymentMethodId = balance.id,
                )
            }
            verify(exactly = 1) {
                receiptFileRepository.generateReceiptFiles(any(), any())
                notificationAdapter.notifyPixReceipt(any(), any(), any(), any())
            }
        }
    }

    @Test
    fun `should process receipt and notify members when pix bill in a wallet is paid`() {
        every {
            walletService.findWallet(billPaid.walletId)
        } returns wallet

        loadAccountIntoDb(dynamoDB, accountId = wallet.founder.accountId)
        val internalBankAccount = balance.method as InternalBankAccount
        loadBalancePaymentMethod(
            accountRepository = accountDbRepository,
            document = internalBankAccount.document,
            paymentMethodId = balance.id.value,
            accountId = wallet.founder.accountId.value,
            bankNo = internalBankAccount.bankNo,
            bankRoutingNo = internalBankAccount.routingNo,
            bankAccountNo = internalBankAccount.accountNo.toBigInteger(),
            bankAccountDv = internalBankAccount.accountDv,
        )

        val receiptService = buildReceiptService(
            repository = mockk {
                every { findById(any()) } returns pixTransaction
            },
        )

        val payerFinancialInstitution =
            FinancialInstitution(name = "Banco Arbi S.A.", ispb = null, compe = internalBankAccount.bankNo)
        val payeeFinancialInstitution = FinancialInstitution(name = "Itau S.A.", ispb = "********", compe = 341)
        withPixParticipants(
            payeeFinancialInstitution,
            payerFinancialInstitution,
        ) {
            receiptService.notify(billPaid = pixPaid)

            val receiptData =
                receiptRepository.findById(walletId = pixPaid.walletId, billId = pixPaid.billId)
            with(receiptData as PixReceiptData) {
                billId shouldBe pixPaidBill.billId
                walletId shouldBe pixPaidBill.walletId
                source shouldBe pixPaidBill.source
                authentication shouldBe "AUTH"
                dateTime shouldBe ZonedDateTime.ofInstant(Instant.ofEpochMilli(pixPaidBill.paidDate!!), brazilTimeZone)
                recipient shouldBe pixPaidBill.recipient
                totalAmount shouldBe pixPaidBill.amountTotal
                payerBankAccount shouldBe balance.method
                this.payerFinancialInstitution shouldBe payerFinancialInstitution
                payer shouldBe BillPayer(document = DOCUMENT, name = NAME)
                this.payeeFinancialInstitution shouldBe payeeFinancialInstitution
                walletName shouldBe wallet.name
                scheduledBy shouldBe walletFixture.participant.simpleName()
                transactionId shouldBe pixTransaction.id
                payments.shouldNotBeNull().first() shouldBeEqualToComparingFields ReceiptPaymentData(
                    amount = pixPaidBill.amountTotal,
                    type = ReceiptPaymentType.BALANCE,
                    accountPaymentMethodId = balance.id,
                )
            }
            verify(exactly = 1) {
                receiptFileRepository.generateReceiptFiles(any(), any())
                notificationAdapter.notifyPixReceipt(
                    members = listOf(wallet.founder, walletFixture.participant),
                    receiptData = receiptData,
                    receiptFilesData = any(),
                    mailReceiptHtml = any(),
                )
            }
        }
    }

    @ParameterizedTest
    @MethodSource("receiptDatas")
    fun `should deserialize receipt data with accountId`(receiptData: ReceiptData) {
        val serializedWithWalletId = getObjectMapper().writeValueAsString(receiptData)
        serializedWithWalletId shouldContain "walletId"

        val serializedWithAccountId = serializedWithWalletId.replace("walletId", "accountId")
        serializedWithAccountId shouldNotContain "walletId"

        val deserializedReceipt = getObjectMapper().readerFor(ReceiptData::class.java)
            .readValue<ReceiptData>(serializedWithAccountId)

        deserializedReceipt shouldBe receiptData
    }

    @ParameterizedTest
    @MethodSource("receiptDatas")
    fun `should deserialize receipt data with walletId`(receiptData: ReceiptData) {
        val serializedWithWalletId = getObjectMapper().writeValueAsString(receiptData)
        serializedWithWalletId shouldContain "walletId"

        val deserializedReceipt = getObjectMapper().readerFor(ReceiptData::class.java)
            .readValue<ReceiptData>(serializedWithWalletId)

        deserializedReceipt shouldBe receiptData
    }

    companion object {
        val boletoReceiptData = BoletoReceiptData(
            authentication = "***********.***********.88.99.AA.BB.CC.DD.EE.FF",
            dateTime = getZonedDateTime(),
            recipient = Recipient(
                name = "receipient",
                document = CNPJ_1,
                bankAccount = null,
                pixKeyDetails = null,
            ),
            totalAmount = 1042799,
            billId = BillId("FICHA_RECEIPT"),
            walletId = WalletId(WALLET_ID),
            payer = BillPayer(document = DOCUMENT, name = "receipient", alias = null),
            dueDate = getLocalDate(),
            assignor = "receipient",
            barcode = BarCode.ofDigitable("34191090080116587030201035380003586090000311015"),
            source = ActionSource.Webapp(role = Role.OWNER),
            walletName = null,
            scheduledBy = null,
            paymentPartnerName = "BANCO 756 / AGENCIA 3038",
            transactionId = TransactionId("transactionId"),
            payments = listOf(
                ReceiptPaymentData(
                    amount = 1042799,
                    type = ReceiptPaymentType.BALANCE,
                    accountPaymentMethodId = balance.id,
                ),
            ),
        )

        val pixReceiptData = PixReceiptData(
            authentication = "***********.***********.88.99.AA.BB.CC.DD.EE.FF",
            dateTime = getZonedDateTime(),
            recipient = Recipient(
                name = "recipient",
                document = DOCUMENT_2,
                alias = "alias",
                bankAccount = null,
                pixKeyDetails = PixKeyDetails(
                    key = PixKey(value = "+*************", type = PixKeyType.PHONE),
                    holder = PixKeyHolder(
                        accountNo = BigInteger("23468"),
                        accountDv = "0",
                        ispb = "********",
                        institutionName = "Banco do Brasil",
                        accountType = AccountType.PAYMENT,
                        routingNo = 123,
                    ),
                    owner = PixKeyOwner(
                        name = "recipient",
                        document = DOCUMENT_2,
                    ),
                ),
                pixQrCodeData = PixQrCodeData(
                    type = PixQrCodeType.STATIC,
                    info = "info",
                    pixCopyAndPaste = PixCopyAndPaste("pixCopyAndPaste"),
                    pixId = "pixId",
                    fixedAmount = 1042799,
                    additionalInfo = mapOf("additionalInfoKey" to "additionalInfoValue"),
                    expiration = getZonedDateTime().plusHours(1),
                    originalAmount = 1042799,
                    automaticPixRecurringDataJson = null,
                ),
            ),
            totalAmount = 1042799,
            purpose = INVOICE_PURPOSE,
            billId = BillId("PIX_KEY_RECEIPT"),
            walletId = WalletId(WALLET_ID),
            payer = BillPayer(document = DOCUMENT, name = "recipient", alias = null),
            source = ActionSource.Webapp(role = Role.OWNER),
            payeeFinancialInstitution = FinancialInstitution(name = "payeeBank", ispb = null, compe = 1234),
            payerFinancialInstitution = FinancialInstitution(name = "payerBank", ispb = null, compe = 4321),
            payeeRoutingNo = 1L,
            payeeAccountNo = BigInteger("12345"),
            payeeAccountDv = "1",
            payeeAccountType = AccountType.PAYMENT,
            payerBankAccount = InternalBankAccount(
                accountType = AccountType.CHECKING,
                bankNo = 0,
                routingNo = 0,
                accountNo = 0,
                accountDv = "1",
                document = "**********",
                bankAccountMode = BankAccountMode.PHYSICAL,
            ),
            walletName = null,
            scheduledBy = null,
            transactionId = null,
            payments = listOf(
                ReceiptPaymentData(
                    amount = 1042799,
                    type = ReceiptPaymentType.CREDIT_CARD,
                    accountPaymentMethodId = creditCard.id,
                ),
            ),
        )

        val invoiceReceiptData = InvoiceReceiptData(
            authentication = "***********.***********.88.99.AA.BB.CC.DD.EE.FF",
            dateTime = getZonedDateTime(),
            recipient = Recipient(
                name = "recipient",
                document = DOCUMENT_2,
                alias = "alias",
                bankAccount = BankAccount(
                    accountType = AccountType.PAYMENT,
                    bankNo = 1L,
                    routingNo = 123,
                    accountNo = BigInteger("12345"),
                    accountDv = "0",
                    document = DOCUMENT_2,
                    ispb = null,
                ),
                pixKeyDetails = null,
            ),
            totalAmount = 1042799,
            purpose = "CC",
            billId = BillId("INVOICE_RECEIPT"),
            walletId = WalletId(WALLET_ID),
            payer = BillPayer(document = DOCUMENT, name = "recipient", alias = null),
            source = ActionSource.Webapp(role = Role.OWNER),
            payeeBank = "10 - Banco XYZ",
            paymentPartnerName = payerBank,
            paymentPartnerDocument = payerDocument,
            walletName = null,
            scheduledBy = null,
            transactionId = TransactionId("transactionId"),
            payments = listOf(
                ReceiptPaymentData(
                    amount = 1042799,
                    type = ReceiptPaymentType.BALANCE,
                    accountPaymentMethodId = balance.id,
                ),
                ReceiptPaymentData(
                    amount = 1042799,
                    type = ReceiptPaymentType.CREDIT_CARD,
                    accountPaymentMethodId = creditCard.id,
                ),
            ),
        )

        @JvmStatic
        fun receiptDatas(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(boletoReceiptData),
                Arguments.of(invoiceReceiptData),
                Arguments.of(pixReceiptData),
            )
        }
    }
}