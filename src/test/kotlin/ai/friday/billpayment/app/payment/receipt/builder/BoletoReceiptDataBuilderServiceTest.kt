package ai.friday.billpayment.app.payment.receipt.builder

import DynamoDBUtils
import ai.friday.billpayment.adapters.dynamodb.CreditCardChargebackTrackDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbTransactionRepository
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamoDAO
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.payment.BillPaymentReceipt
import ai.friday.billpayment.app.payment.BoletoSettlementResult
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.boletoSettlementTransactionId
import ai.friday.billpayment.app.settlement.receipt.BoletoSettlementReceiptService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.billScheduleCanceled
import ai.friday.billpayment.createTransaction
import ai.friday.billpayment.defaultTransactionEntityConverter
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class BoletoReceiptDataBuilderServiceTest {

    val bill = Bill.build(billAdded, billPaymentScheduled, billPaymentStart, billPaid, billScheduleCanceled)

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val billEventRepository: BillEventRepository = mockk {
        every {
            getBillById(bill.billId)
        } returns bill.right()
    }

    private val accountRepository: AccountRepository = mockk() {
        every {
            findAccountPaymentMethodByIdAndAccountId(any(), any())
        } returns mockk(relaxed = true)
    }

    private val enhancedClient = DynamoDBUtils.setupDynamoDB()
    private val transactionDynamo = TransactionDynamo(enhancedClient)
    private val transactionDAO = TransactionDynamoDAO(enhancedClient)
    private val creditCardChargebackTrackDAO = CreditCardChargebackTrackDynamoDAO(enhancedClient)

    private val transactionRepository = DynamoDbTransactionRepository(
        transactionDAO = transactionDAO,
        creditCardChargebackTrackDAO = creditCardChargebackTrackDAO,
        accountRepository = accountRepository,
        transactionEntityConverter = defaultTransactionEntityConverter(billEventRepository, mockk(), transactionDynamo),
    )

    private val boletoSettlementReceiptService: BoletoSettlementReceiptService = mockk()

    private val boletoReceiptDataBuilder = BoletoReceiptDataBuilder(
        transactionRepository = transactionRepository,
        boletoSettlementReceiptService = boletoSettlementReceiptService,
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `quando a transacao tem a informacao de authorization e settlementFinancialInstitution não deve consultar o servição de liquidação`() {
        val boletoSettlementResult = BoletoSettlementResult(
            gateway = FinancialServiceGateway.FRIDAY,
            status = BoletoSettlementStatus.CONFIRMED,
            bankTransactionId = "123",
            externalNsu = 0,
            externalTerminal = "",
            errorCode = "",
            errorDescription = null,
            authentication = "authorization",
            paymentPartnerName = "settlementFinancialInstitution",
        )

        val transaction = createTransaction(
            transactionId = billPaid.transactionId,
            settlementData = SettlementData(
                settlementTarget = bill,
                serviceAmountTax = 0,
                totalAmount = 0,
                settlementOperation = boletoSettlementResult,
            ),
        )
        transactionRepository.save(transaction)

        val walletFixture = WalletFixture(founderAccountId = (bill.source as ActionSource.WalletActionSource).accountId!!, defaultWalletId = bill.walletId)
        val wallet = walletFixture.buildWallet()

        val receiptData = boletoReceiptDataBuilder.build(
            bill = bill,
            billPaid = billPaid,
            wallet = wallet,
        )

        receiptData.authentication shouldBe boletoSettlementResult.authentication
        receiptData.paymentPartnerName shouldBe boletoSettlementResult.paymentPartnerName

        verify {
            boletoSettlementReceiptService wasNot Called
        }
    }

    @Test
    fun `quando a transacao não tem a informacao de authorization e settlementFinancialInstitution deve consultar o servição de liquidação`() {
        val boletoSettlementResult = BoletoSettlementResult(
            gateway = FinancialServiceGateway.FRIDAY,
            status = BoletoSettlementStatus.CONFIRMED,
            bankTransactionId = "123",
            externalNsu = 0,
            externalTerminal = "",
            errorCode = "",
            errorDescription = null,
            authentication = null,
            paymentPartnerName = null,
        )

        val transaction = createTransaction(
            transactionId = billPaid.transactionId,
            settlementData = SettlementData(
                settlementTarget = bill,
                serviceAmountTax = 0,
                totalAmount = 0,
                settlementOperation = boletoSettlementResult,
            ),
        )
        transactionRepository.save(transaction)

        val walletFixture = WalletFixture(founderAccountId = (bill.source as ActionSource.WalletActionSource).accountId!!, defaultWalletId = bill.walletId)
        val wallet = walletFixture.buildWallet()

        val billPaymentReceipt = BillPaymentReceipt(
            createdOn = "",
            transactionId = boletoSettlementResult.bankTransactionId,
            authentication = "authentication",
            paymentPartnerName = "paymentPartnerName",
        )

        every {
            boletoSettlementReceiptService.getReceipt(any())
        } returns billPaymentReceipt

        val receiptData = boletoReceiptDataBuilder.build(
            bill = bill,
            billPaid = billPaid,
            wallet = wallet,
        )

        receiptData.authentication shouldBe billPaymentReceipt.authentication
        receiptData.paymentPartnerName shouldBe billPaymentReceipt.paymentPartnerName

        verify {
            boletoSettlementReceiptService.getReceipt(
                withArg {
                    it.id shouldBe transaction.id
                    it.boletoSettlementTransactionId() shouldBe boletoSettlementResult.bankTransactionId
                },
            )
        }
    }
}