package ai.friday.billpayment.app.payment.transaction

import ai.friday.billpayment.adapters.celcoin.CelcoinBillValidationResponse
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillSynchronizationStatus
import ai.friday.billpayment.app.bill.SynchronizeBillResponse
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.payment.BoletoSettlementResult
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.concessionariaBill
import ai.friday.billpayment.createBoletoSettlementResult
import ai.friday.billpayment.createTransaction
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.BILL_ID_3
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

internal class PrepareTransactionTest {

    private val bill1 = Bill.build(billAdded, billPaymentScheduled)

    private val bill2 = Bill.build(
        billAdded.copy(billId = BillId(BILL_ID_2)),
        billPaymentScheduled.copy(billId = BillId(BILL_ID_2)),
    )

    private val bill3 = Bill.build(
        billAdded.copy(billId = BillId(BILL_ID_3)),
        billPaymentScheduled.copy(billId = BillId(BILL_ID_3)),
    )

    private val updateBillService = mockk<UpdateBillService>()

    private val settlementService = mockk<BoletoSettlementService> {
        every { settlementValidation(bill1) } returns mockk(relaxed = true) {
            every { bankTransactionId } returns "123"
        }

        every { settlementValidation(bill2) } returns CelcoinBillValidationResponse(
            "628",
            "BOLETO DE PAGAMENTO JA BAIXADO",
        )

        every { settlementValidation(bill3) } returns CelcoinBillValidationResponse(
            "99999",
            "UNKNOWN ERROR",
        )
    }

    private val prepareTransaction = PrepareTransaction(
        updateBillService = updateBillService,
        boletoSettlementService = settlementService,
    )

    @Test
    fun `não deve prosseguir com uma transação caso a validação retorne um valor menor que o valor atual`() {
        val transaction = createTransaction()
        every { updateBillService.synchronizeBill(any<Bill>(), any()) } returns SynchronizeBillResponse(
            status = BillSynchronizationStatus.BillAmountTotalUpdated,
        )
        prepareTransaction.execute(transaction)

        transaction.status shouldBe TransactionStatus.FAILED
        transaction.isRetryable().shouldBeFalse()

        with(transaction.settlementData.settlementOperation) {
            shouldBeTypeOf<BoletoSettlementResult>()
            errorCode shouldBe "01"
            bankTransactionId shouldBe ""
            isRetryable().shouldBeFalse()
        }
    }

    @Test
    fun `deve falhar uma transação caso a validação retorne um valor maior que o valor atual`() {
        val transaction = createTransaction()
        every { updateBillService.synchronizeBill(any<Bill>(), any()) } returns SynchronizeBillResponse(
            status = BillSynchronizationStatus.BillAmountTotalUpdated,
        )
        prepareTransaction.execute(transaction)

        transaction.status shouldBe TransactionStatus.FAILED
        transaction.isRetryable().shouldBeFalse()

        with(transaction.settlementData.settlementOperation) {
            shouldBeTypeOf<BoletoSettlementResult>()
            errorCode shouldBe "01"
            bankTransactionId shouldBe ""
            isRetryable().shouldBeFalse()
        }
    }

    @Test
    fun `deve falhar uma transação caso a validação retorne que o boleto ja foi baixado`() {
        every { updateBillService.synchronizeBill(any<Bill>(), any()) } returns SynchronizeBillResponse(
            status = BillSynchronizationStatus.BillAmountTotalUpdated,
        )

        val transaction = createTransaction(
            settlementData = SettlementData(
                bill2,
                0,
                concessionariaBill.amountTotal,
                createBoletoSettlementResult(BoletoSettlementStatus.UNKNOWN),
            ),
        )

        prepareTransaction.execute(transaction)

        transaction.status shouldBe TransactionStatus.FAILED
        transaction.isRetryable().shouldBeFalse()

        verify { updateBillService.synchronizeBill(any<Bill>(), any()) }

        with(transaction.settlementData.settlementOperation) {
            shouldBeTypeOf<BoletoSettlementResult>()
            errorCode shouldBe "628"
            bankTransactionId shouldBe ""
            isRetryable().shouldBeFalse()
        }
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `deve falhar uma transação caso a validação retorne que nao foi possivel validar o boleto`(isRetryable: Boolean) {
        every { settlementService.settlementValidation(bill2) } returns CelcoinBillValidationResponse(
            "000",
            "o boleto teve seu valor atualizado para zero",
        )

        every { updateBillService.synchronizeBill(any<Bill>(), any()) } returns SynchronizeBillResponse(
            status = BillSynchronizationStatus.UnableToValidate(isRetryable),
        )

        val transaction = createTransaction(
            settlementData = SettlementData(
                bill2,
                0,
                concessionariaBill.amountTotal,
                createBoletoSettlementResult(BoletoSettlementStatus.UNKNOWN),
            ),
        )

        prepareTransaction.execute(transaction)

        transaction.status shouldBe TransactionStatus.FAILED
        transaction.isRetryable() shouldBe isRetryable

        verify { updateBillService.synchronizeBill(any<Bill>(), any()) }

        with(transaction.settlementData.settlementOperation) {
            shouldBeTypeOf<BoletoSettlementResult>()
            errorCode shouldBe "000"
            bankTransactionId shouldBe ""
            isRetryable() shouldBe isRetryable
        }
    }

    @Test
    fun `deve falhar uma transação e salva-la como retryable se o erro for desconhecido`() {
        val transaction = createTransaction(
            settlementData = SettlementData(
                bill3,
                0,
                concessionariaBill.amountTotal,
                createBoletoSettlementResult(BoletoSettlementStatus.UNKNOWN),
            ),
        )

        prepareTransaction.execute(transaction)

        transaction.status shouldBe TransactionStatus.FAILED
        transaction.isRetryable().shouldBeTrue()

        with(transaction.settlementData.settlementOperation) {
            shouldBeTypeOf<BoletoSettlementResult>()
            errorCode shouldBe "99999"
            bankTransactionId shouldBe ""
            isRetryable().shouldBeTrue()
        }
    }
}