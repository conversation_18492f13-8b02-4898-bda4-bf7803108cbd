package ai.friday.billpayment.app.payment

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.CreditCardPaymentStatus
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.cashIn.CreditCardCashIn
import ai.friday.billpayment.app.cashIn.CreditCardCashInHandler
import ai.friday.billpayment.app.integrations.AcquirerService
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.authorizedCreditCardAuthorization
import ai.friday.billpayment.creditCardCashInTransaction
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.DOCUMENT
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

internal class CreditCardCashInHandlerTest {

    val transactionRepository: TransactionRepository = mockk(relaxed = true)
    val internalBankService: InternalBankService = mockk() {
        every {
            prepareCashInFunds(any())
        } answers { callOriginal() }
    }

    val acquirerService: AcquirerService = mockk()

    val checkout = CreditCardCashInHandler(
        acquirerService = acquirerService,
        internalBankService = internalBankService,
        transactionRepository = transactionRepository,
        balanceService = mockk(),
        cashInInstrumentationService = mockk(),
        systemActivityService = mockk(),
        creditCardService = mockk(),
        walletService = mockk(),
        notificationAdapter = mockk(),
    )

    @Test
    fun `should save transaction before cashin`() {
        every {
            acquirerService.checkStatus(any())
        } returns authorizedCreditCardAuthorization.copy(status = CreditCardPaymentStatus.PAYMENT_CONFIRMED)

        val bankAccount = AccountPaymentMethod(
            accountId = AccountId(ACCOUNT_ID),
            method = InternalBankAccount(
                accountType = AccountType.CHECKING,
                bankNo = 1L,
                routingNo = 2L,
                accountNo = 3L,
                accountDv = "X",
                document = DOCUMENT,
                bankAccountMode = BankAccountMode.PHYSICAL,
            ),
            id = AccountPaymentMethodId("123"),
        )

        val retryingTransaction = creditCardCashInTransaction.copy(
            id = TransactionId.build(),
            status = TransactionStatus.PROCESSING,
            settlementData = SettlementData(
                settlementTarget = CreditCardCashIn(amount = 1, bankAccount = bankAccount),
                serviceAmountTax = 0,
                totalAmount = 1,
            ),
            paymentData = creditCardCashInTransaction.paymentData.toSingle().copy(
                payment = authorizedCreditCardAuthorization,
            ),
        )

        every {
            internalBankService.cashInFunds(any(), any(), any())
        } throws NoStackTraceException("teste")

        assertThrows<NoStackTraceException> {
            checkout.retry(retryingTransaction)
        }

        val slot = slot<Transaction>()

        verifySequence {
            internalBankService.prepareCashInFunds(any())
            transactionRepository.save(capture(slot))
            internalBankService.cashInFunds(any(), any(), any())
        }

        slot.captured.settlementData.getOperation<BankTransfer>().status shouldBe BankOperationStatus.UNKNOWN
        slot.captured.id shouldBe retryingTransaction.id
    }

    @Test
    fun `should return transaction failed when credit card authorization is denied`() {
        val failedAuthorization = authorizedCreditCardAuthorization.copy(
            status = CreditCardPaymentStatus.DENIED,
            acquirerReturnMessage = "O pagamento foi negado. Por favor, entre em contato com o seu banco.",
        )

        every {
            acquirerService.checkStatus(any())
        } returns failedAuthorization

        val bankAccount = AccountPaymentMethod(
            accountId = AccountId(ACCOUNT_ID),
            method = InternalBankAccount(
                accountType = AccountType.CHECKING,
                bankNo = 1L,
                routingNo = 2L,
                accountNo = 3L,
                accountDv = "X",
                document = DOCUMENT,
                bankAccountMode = BankAccountMode.PHYSICAL,
            ),
            id = AccountPaymentMethodId("123"),
        )

        val retryingTransaction = creditCardCashInTransaction.copy(
            id = TransactionId.build(),
            status = TransactionStatus.PROCESSING,
            settlementData = SettlementData(
                settlementTarget = CreditCardCashIn(amount = 1, bankAccount = bankAccount),
                serviceAmountTax = 0,
                totalAmount = 1,
            ),
            paymentData = creditCardCashInTransaction.paymentData.toSingle().copy(
                payment = failedAuthorization,
            ),
        )

        checkout.retry(retryingTransaction).status shouldBe TransactionStatus.FAILED
    }
}