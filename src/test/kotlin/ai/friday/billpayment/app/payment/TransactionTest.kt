package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.banking.FinancialServiceGateway
import io.kotest.matchers.booleans.shouldBeTrue
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test

class TransactionTest {

    val transaction = mockk<Transaction> {
        every { shouldCheckSettlementStatus() } answers { callOriginal() }
        every { isRetryable() } answers { callOriginal() }
    }

    @Test
    fun `transações de liquidação boletos feitas no gateway da Friday devem checar o status da liquidação em uma retentativa`() {
        val settlementResult = mockk<BoletoSettlementResult>() {
            every { shouldCheckSettlementStatus() } answers { callOriginal() }
            every { gateway } returns FinancialServiceGateway.FRIDAY
        }
        every { transaction.settlementData.settlementOperation } returns settlementResult

        transaction.shouldCheckSettlementStatus().shouldBeTrue()
    }

    @Test
    fun `quando o pagamento falha a transacao deve ser retentavel`() {
        val paymentData = mockk<SinglePaymentData>()
        every { transaction.paymentData } returns paymentData
        every { paymentData.status() } returns PaymentStatus.ERROR
        every { transaction.settlementData.settlementOperation } returns mockk<BoletoSettlementResult> {
            every { status } returns BoletoSettlementStatus.VOIDED
            every { isRetryable() } answers { callOriginal() }
        }
    }
}