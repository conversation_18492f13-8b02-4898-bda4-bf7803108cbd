package ai.friday.billpayment.app.payment.receipt

import ai.friday.billpayment.app.payment.ReceiptFiles
import ai.friday.billpayment.app.payment.ReceiptFilesData
import io.kotest.matchers.equality.shouldBeEqualToComparingFields
import io.mockk.mockk
import org.junit.jupiter.api.Test

class EmptyReceiptFileRepositoryTest {

    private val emptyReceiptFileRepository = EmptyReceiptFileRepository()

    @Test
    fun `deve retornar um byte array vazio`() {
        emptyReceiptFileRepository.generateReceiptFiles(
            receiptData = mockk(),
            receiptHtml = mockk(),
        ) shouldBeEqualToComparingFields ReceiptFilesData(
            imageBytes = ByteArray(0),
            imageFormat = "png",
            pdfBytes = ByteArray(0),
            imageUrl = null,
            pdfUrl = null,
        )
    }

    @Test
    fun `deve retornar um receipt file vazio`() {
        emptyReceiptFileRepository.generateLinks(mockk()) shouldBeEqualToComparingFields ReceiptFiles(null, null)
    }
}