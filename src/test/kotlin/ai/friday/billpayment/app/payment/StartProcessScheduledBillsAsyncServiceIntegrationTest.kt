/*
package ai.friday.billpayment.app.payment

import DynamoDBUtils
import ai.friday.billpayment.adapters.dynamodb.ScheduledWalletDBRepository
import ai.friday.billpayment.adapters.dynamodb.ScheduledWalletDynamoDAO
import ai.friday.billpayment.app.integrations.ProcessScheduledBillsPublisher
import ai.friday.billpayment.app.wallet.WalletId
import io.kotest.common.runBlocking
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import parallelMap

@Disabled("Only for local tests")
class StartProcessScheduledBillsAsyncServiceIntegrationTest {

    private val dynamoDbEnhancedAsyncClient = DynamoDBUtils.getDynamoDBAsync()

    private val scheduledWalletDynamoDAO = ScheduledWalletDynamoDAO(dynamoDbEnhancedAsyncClient)
    private val scheduledWalletRepository = ScheduledWalletDBRepository(scheduledWalletDynamoDAO)

    private val processScheduledBillsPublisher = mockk<ProcessScheduledBillsPublisher>(relaxUnitFun = true)

    private val service = StartProcessScheduledBillsAsyncService(
        scheduledWalletRepository = scheduledWalletRepository,
        processScheduledBillsPublisher = processScheduledBillsPublisher,
        jobExecutor = ThreadPoolExecutor(
            5,
            200,
            1,
            TimeUnit.MINUTES,
            LinkedBlockingQueue(),
        ),
    )

    private val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")

    @BeforeEach
    fun setup() {
        DynamoDBUtils.setupDynamoDBAsync()

        log("preparando a base")

        save(LocalDate.now(), 1_000_000)
        save(LocalDate.now().plusDays(1), 10)

        log("base preparada")
    }

    @Test
    fun `deve enviar as carteiras para a fila`() {
        log("start")
        service.start(true)
        log("end")

        verify(exactly = 1_000_000) {
            processScheduledBillsPublisher.publish(any<List<WalletId>>(), any(), any())
        }
    }

    fun save(localDate: LocalDate, numWallets: Int) {
        val numThreads = 10

        runBlocking {
            (1..numWallets)
                .chunked(numWallets / numThreads)
                .parallelMap {
                    it.forEach { _ ->
                        scheduledWalletRepository.save(walletId = WalletId(), minScheduleDate = localDate)
                    }
                }
        }
    }

    private fun log(message: String) {
        val now = LocalDateTime.now().format(formatter)
        println("$now : $message")
    }
}*/