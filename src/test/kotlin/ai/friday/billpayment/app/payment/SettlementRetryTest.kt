package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.payment.checkout.AsyncSettlementBoletoCheckout
import ai.friday.billpayment.app.payment.receipt.NotifyReceiptService
import ai.friday.billpayment.createTransaction
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class SettlementRetryTest {
    private val checkoutMock: AsyncSettlementBoletoCheckout = mockk()

    private val checkoutLocatorMock: CheckoutLocator = mockk {
        every {
            getCheckout(any())
        } returns checkoutMock
    }

    private val transactionServiceMock: TransactionService = mockk {
        every {
            save(any())
        } just Runs
        every {
            notify(any())
        } just Runs
    }

    private val billEventPublisherMock: BillEventPublisher = mockk {
        every {
            publish(any(), any())
        } just Runs
    }

    private val notifyReceiptService = mockk<NotifyReceiptService>()

    private val settlementRetry = SettlementRetry(
        billEventPublisher = billEventPublisherMock,
        transactionService = transactionServiceMock,
        checkoutLocator = checkoutLocatorMock,
        notifyReceiptService = notifyReceiptService,
    )

    private val settlementOperationSpy = spyk(
        BankTransfer(
            operationId = BankOperationId("123"),
            gateway = FinancialServiceGateway.FRIDAY,
            status = BankOperationStatus.SUCCESS,
            amount = 1,
        ),
    )

    private val billMock: Bill = mockk(relaxed = true)

    private val settlementDataMock = SettlementData(
        settlementTarget = billMock,
        serviceAmountTax = 1,
        totalAmount = 100,
        settlementOperation = settlementOperationSpy,
    )

    private val transaction = createTransaction(
        settlementData = settlementDataMock,
    )

    @Test
    fun `quando der erro verificando o estado da liquidacao deve manter a transacao processando e lancar TransactionRollbackException`() {
        every {
            checkoutMock.checkSettlementStatus(transaction)
        } returns SettlementStatus.Error

        assertThrows<TransactionRollbackException> {
            settlementRetry.resolve(transaction)
        }

        transaction.status shouldBe TransactionStatus.PROCESSING

        verify(exactly = 0) {
            settlementOperationSpy.confirm()
            transactionServiceMock.save(transaction)
            billEventPublisherMock.publish(billMock, any<BillPaid>())
            transactionServiceMock.notify(transaction)
        }
    }

    @ParameterizedTest
    @CsvSource("failure;PIX", "TED não realizado. Por favor, tente novamente;INVOICE", delimiter = ';')
    fun `quando a liquidacao estiver com falha deve manter a transacao processando e devolver TransactionRollbackException`(
        expectedMessage: String,
        billType: BillType,
    ) {
        every {
            checkoutMock.checkSettlementStatus(transaction)
        } returns SettlementStatus.Failure("failure")

        every { billMock.billType } returns billType

        val result = settlementRetry.resolve(transaction)

        result.isLeft().shouldBeTrue()
        result.mapLeft {
            it.shouldBeTypeOf<TransactionRollbackException>()
        }

        transaction.status shouldBe TransactionStatus.PROCESSING
        transaction.settlementData.getOperation<BankTransfer>().errorDescription shouldBe expectedMessage

        verify(exactly = 0) {
            settlementOperationSpy.confirm()
            transactionServiceMock.save(transaction)
            billEventPublisherMock.publish(billMock, any<BillPaid>())
            transactionServiceMock.notify(transaction)
        }
    }

    @ParameterizedTest
    @CsvSource("failure;PIX", "TED não realizado. Por favor, tente novamente;INVOICE", delimiter = ';')
    fun `quando a liquidacao estiver com dados invalidos deve manter a transacao processando e devolver TransactionRollbackException`(
        expectedMessage: String,
        billType: BillType,
    ) {
        every {
            checkoutMock.checkSettlementStatus(transaction)
        } returns SettlementStatus.InvalidData("failure")

        every { billMock.billType } returns billType

        val result = settlementRetry.resolve(transaction)

        result.isLeft().shouldBeTrue()
        result.mapLeft {
            it.shouldBeTypeOf<TransactionRollbackException>()
        }

        transaction.status shouldBe TransactionStatus.PROCESSING
        transaction.settlementData.getOperation<BankTransfer>().errorDescription shouldBe expectedMessage
        transaction.settlementData.getOperation<BankTransfer>().status shouldBe BankOperationStatus.INVALID_DATA

        verify(exactly = 0) {
            settlementOperationSpy.confirm()
            transactionServiceMock.save(transaction)
            billEventPublisherMock.publish(billMock, any<BillPaid>())
            transactionServiceMock.notify(transaction)
        }
    }

    @Test
    fun `quando estiver already completed, deve manter a transação mas e não deve salvar`() {
        every {
            checkoutMock.checkSettlementStatus(transaction)
        } returns SettlementStatus.AlreadyCompleted

        val result = settlementRetry.resolve(transaction)

        result.isRight().shouldBeTrue()
        result.map {
            it shouldBe RetrySettlementStatus.COMPLETED
        }

        verify(exactly = 0) {
            settlementOperationSpy.confirm()
            transactionServiceMock.save(any())
            billEventPublisherMock.publish(any(), any())
            transactionServiceMock.notify(any())
        }
    }

    @Test
    fun `quando a liquidacao ainda estiver processando deve manter a transacao processando`() {
        every {
            checkoutMock.checkSettlementStatus(transaction)
        } returns SettlementStatus.Processing

        val result = settlementRetry.resolve(transaction)

        result.isRight().shouldBeTrue()
        result.map {
            it shouldBe RetrySettlementStatus.PROCESSING
        }

        transaction.status shouldBe TransactionStatus.PROCESSING

        verify(exactly = 0) {
            settlementOperationSpy.confirm()
            transactionServiceMock.save(transaction)
            billEventPublisherMock.publish(billMock, any<BillPaid>())
            transactionServiceMock.notify(transaction)
        }
    }

    @Test
    fun `quando a liquidacao estiver com sucesso deve completar a transacao`() {
        every {
            checkoutMock.checkSettlementStatus(transaction)
        } returns SettlementStatus.Success("authentication")

        every {
            notifyReceiptService.notifyWithAsyncRetry(any())
        } just Runs

        val result = settlementRetry.resolve(transaction)

        result.isRight().shouldBeTrue()
        result.map {
            it shouldBe RetrySettlementStatus.COMPLETED
        }

        transaction.status shouldBe TransactionStatus.COMPLETED

        verify {
            settlementOperationSpy.confirm()
            transactionServiceMock.save(transaction)
            billEventPublisherMock.publish(billMock, any<BillPaid>())
            notifyReceiptService.notifyWithAsyncRetry(any())
            transactionServiceMock.notify(transaction)
        }
    }
}