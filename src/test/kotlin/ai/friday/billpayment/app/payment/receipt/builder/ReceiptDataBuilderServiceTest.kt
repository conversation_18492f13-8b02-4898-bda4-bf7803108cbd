package ai.friday.billpayment.app.payment.receipt.builder

import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.payment.AutomaticPixReceiptData
import ai.friday.billpayment.app.payment.BoletoReceiptData
import ai.friday.billpayment.app.payment.InvoiceReceiptData
import ai.friday.billpayment.app.payment.PixReceiptData
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.concessionariaBill
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.pixAdded
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.called
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ReceiptDataBuilderServiceTest {

    private val pixReceiptDataBuilder: PixReceiptDataBuilder = mockk(relaxed = true) { every { buildFor } returns listOf(BillType.PIX) }
    private val boletoReceiptDataBuilder: BoletoReceiptDataBuilder = mockk(relaxed = true) { every { buildFor } returns listOf(BillType.FICHA_COMPENSACAO, BillType.CONCESSIONARIA) }
    private val invoiceReceiptDataBuilder: InvoiceReceiptDataBuilder = mockk(relaxed = true) { every { buildFor } returns listOf(BillType.INVOICE) }
    private val automaticPixReceiptDataBuilder: ReceiptDataBuilder = mockk(relaxed = true) { every { buildFor } returns listOf(BillType.AUTOMATIC_PIX) }

    private val walletFixture = WalletFixture()

    private val wallet = walletFixture.buildWallet()

    private val receiptDataBuilderService = ReceiptDataBuilderService(
        receiptDataBuilder = listOf(
            pixReceiptDataBuilder,
            boletoReceiptDataBuilder,
            invoiceReceiptDataBuilder,
            automaticPixReceiptDataBuilder,
        ),
    )

    @BeforeEach
    fun init() {
        clearMocks(
            pixReceiptDataBuilder,
            boletoReceiptDataBuilder,
            invoiceReceiptDataBuilder,
        )
    }

    @Test
    fun `deve retornar um recibo de boleto quando a bill for CONCESSIONARIA`() {
        every {
            boletoReceiptDataBuilder.build(any(), any(), any())
        } returns mockk<BoletoReceiptData>()

        val result = receiptDataBuilderService.getReceiptData(bill = concessionariaBill, billPaid = billPaid, wallet = wallet)

        result.shouldBeTypeOf<BoletoReceiptData>()

        verify {
            boletoReceiptDataBuilder.build(concessionariaBill, billPaid, wallet)
            pixReceiptDataBuilder wasNot called
            invoiceReceiptDataBuilder wasNot called
        }
    }

    @Test
    fun `deve retornar um recibo de boleto quando a bill for FICHA_COMPENSACAO`() {
        every {
            boletoReceiptDataBuilder.build(any(), any(), any())
        } returns mockk<BoletoReceiptData>()

        val bill = Bill.build(billAddedFicha)

        val result = receiptDataBuilderService.getReceiptData(bill = bill, billPaid = billPaid, wallet = wallet)

        result.shouldBeTypeOf<BoletoReceiptData>()

        verify {
            boletoReceiptDataBuilder.build(bill, billPaid, wallet)
            pixReceiptDataBuilder wasNot called
            invoiceReceiptDataBuilder wasNot called
        }
    }

    @Test
    fun `deve retornar um recibo de pix quando a bill for PIX`() {
        every {
            pixReceiptDataBuilder.build(any(), any(), any())
        } returns mockk<PixReceiptData>()

        val bill = Bill.build(pixAdded)

        val result = receiptDataBuilderService.getReceiptData(bill = bill, billPaid = billPaid, wallet = wallet)

        result.shouldBeTypeOf<PixReceiptData>()

        verify {
            pixReceiptDataBuilder.build(bill, billPaid, wallet)
            boletoReceiptDataBuilder wasNot called
            invoiceReceiptDataBuilder wasNot called
        }
    }

    @Test
    fun `deve retornar um recibo de invoice quando a bill for INVOICE`() {
        every {
            invoiceReceiptDataBuilder.build(any(), any(), any())
        } returns mockk<InvoiceReceiptData>()

        val bill = Bill.build(invoiceAdded)

        val result = receiptDataBuilderService.getReceiptData(bill = bill, billPaid = billPaid, wallet = wallet)

        result.shouldBeTypeOf<InvoiceReceiptData>()

        verify {
            invoiceReceiptDataBuilder.build(bill, billPaid, wallet)
            boletoReceiptDataBuilder wasNot called
            pixReceiptDataBuilder wasNot called
        }
    }

    @Test
    fun `deve retornar um recibo de automatic pix quando a bill for AUTOMATIC_PIX`() {
        val bill = Bill.build(pixAdded.copy(billType = BillType.AUTOMATIC_PIX))

        every {
            automaticPixReceiptDataBuilder.build(any(), any(), any())
        } returns mockk<AutomaticPixReceiptData>()

        val result = receiptDataBuilderService.getReceiptData(bill = bill, billPaid = billPaid, wallet = wallet)

        result.shouldBeTypeOf<AutomaticPixReceiptData>()

        verify {
            boletoReceiptDataBuilder wasNot called
            pixReceiptDataBuilder wasNot called
            invoiceReceiptDataBuilder wasNot called
        }
    }
}