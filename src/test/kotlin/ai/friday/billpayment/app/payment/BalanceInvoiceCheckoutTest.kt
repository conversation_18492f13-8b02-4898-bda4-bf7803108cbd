package ai.friday.billpayment.app.payment

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.integrations.TEDService
import ai.friday.billpayment.concessionariaBill
import ai.friday.billpayment.createSinglePaymentDataWithBalance
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.internalBankAccount
import ai.friday.billpayment.invoiceBill
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class BalanceInvoiceCheckoutTest {

    private val bankAccount = internalBankAccount.copy(accountNo = 1001L)
    private val bankAccountZeroBalance = internalBankAccount

    private val internalBankService: InternalBankService = mockk()
    private val tedService: TEDService = mockk()
    private val balanceInvoiceCheckout = BalanceInvoiceCheckout(internalBankService, tedService)

    @BeforeEach
    fun setup() {
        clearMocks(internalBankService, tedService)
        every {
            internalBankService.captureFunds(
                any(),
                any(),
                bankAccount,
                any(),
            )
        } answers {
            BankTransfer(
                status = BankOperationStatus.SUCCESS,
                amount = invoiceBill.amountTotal,
                gateway = FinancialServiceGateway.ARBI,
            )
        }
        every { internalBankService.captureFunds(any(), any(), bankAccountZeroBalance, any()) } answers {
            BankTransfer(
                status = BankOperationStatus.INSUFFICIENT_FUNDS,
                amount = invoiceBill.amountTotal,
                gateway = FinancialServiceGateway.ARBI,
                errorDescription = "Insufficient funds",
            )
        }
        every {
            internalBankService.undoCaptureFunds(
                any(),
                any(),
                bankAccount,
                any(),
                concessionariaBill.amountTotal,
            )
        } returns BankTransfer(
            status = BankOperationStatus.SUCCESS,
            amount = concessionariaBill.amountTotal,
            gateway = FinancialServiceGateway.ARBI,
        )
        every { tedService.transfer(any(), any(), any(), any()) } answers {
            TEDResult(
                status = TEDStatus.Success,
                settleDate = getLocalDate(),
                authentication = "AUTH",
                bankTransactionId = 0,
                receipt = "receipt",
                gateway = FinancialServiceGateway.CELCOIN,
                amount = invoiceBill.amountTotal,
            )
        }
    }

    @Test
    fun `should pay invoice with balance`() {
        val transaction = buildTransaction(bankAccount)
        balanceInvoiceCheckout.execute(transaction = transaction)
        verify {
            internalBankService.captureFunds(any(), any(), bankAccount, invoiceBill.amountTotal)
            tedService.transfer(invoiceBill.recipient!!, invoiceBill.amountTotal, ACCOUNT_ID, 1)
        }
        assertEquals(TransactionStatus.COMPLETED, transaction.status)
        assertEquals(BankOperationStatus.SUCCESS, transaction.paymentData.toSingle().get<BalanceAuthorization>().status)
        assertEquals(BankOperationStatus.SUCCESS, transaction.settlementData.getOperation<BankTransfer>().status)
    }

    @Test
    fun `should fail payment when balance is lower than bill amount`() {
        val transaction = buildTransaction(bankAccountZeroBalance)
        balanceInvoiceCheckout.execute(transaction = transaction)
        verify {
            internalBankService.captureFunds(any(), any(), bankAccountZeroBalance, invoiceBill.amountTotal)
            tedService wasNot Called
        }
        assertEquals(TransactionStatus.FAILED, transaction.status)
        assertEquals(
            BankOperationStatus.INSUFFICIENT_FUNDS,
            transaction.paymentData.toSingle().get<BalanceAuthorization>().status,
        )
        assertNull(transaction.settlementData.settlementOperation)
    }

    @Test
    fun `should rollback payment when transfer funds fails`() {
        val errorDescription = "EXCEDE LIMITE"
        every {
            tedService.transfer(
                any(),
                any(),
                any(),
                any(),
            )
        } answers {
            TEDResult(
                status = TEDStatus.Failure(message = errorDescription),
                settleDate = getLocalDate(),
                authentication = "AUTH",
                bankTransactionId = 0,
                receipt = "receipt",
                gateway = FinancialServiceGateway.CELCOIN,
                amount = invoiceBill.amountTotal,
            )
        }
        val transaction = buildTransaction(bankAccount)
        balanceInvoiceCheckout.execute(transaction = transaction)
        verify {
            internalBankService.captureFunds(any(), any(), bankAccount, invoiceBill.amountTotal)
            tedService.transfer(invoiceBill.recipient!!, invoiceBill.amountTotal, ACCOUNT_ID, 1)
        }
        assertEquals(TransactionStatus.FAILED, transaction.status)
        assertEquals(
            BankOperationStatus.REFUNDED,
            transaction.paymentData.toSingle().get<BalanceAuthorization>().status,
        )
        assertEquals(BankOperationStatus.ERROR, transaction.settlementData.getOperation<BankTransfer>().status)
        assertEquals(
            TransactionError.GENERIC_EXCEPTION.description,
            transaction.settlementData.getOperation<BankTransfer>().errorDescription,
        )
    }

    private fun buildTransaction(bankAccount: InternalBankAccount): Transaction {
        val balance = AccountPaymentMethod(
            id = AccountPaymentMethodId(UUID.randomUUID().toString()),
            status = AccountPaymentMethodStatus.ACTIVE,
            method = bankAccount,
            accountId = AccountId(ACCOUNT_ID),
        )
        return Transaction(
            type = TransactionType.INVOICE_PAYMENT,
            payer = ACCOUNT.toPayer(),
            paymentData = createSinglePaymentDataWithBalance(balance, invoiceBill.amountTotal),
            settlementData = SettlementData(invoiceBill, 0, invoiceBill.amountTotal, null),
            nsu = 1,
            actionSource = ActionSource.System,
            walletId = invoiceBill.walletId,
        )
    }

    @Test
    fun `transaction should be processing when ted transfer returns error`() {
        every { tedService.transfer(any(), any(), any(), any()) } answers {
            TEDResult(
                status = TEDStatus.Error,
                settleDate = getLocalDate(),
                authentication = "AUTH",
                bankTransactionId = 0,
                receipt = "receipt",
                gateway = FinancialServiceGateway.CELCOIN,
                amount = invoiceBill.amountTotal,
            )
        }
        val transaction = buildTransaction(bankAccount)

        balanceInvoiceCheckout.execute(transaction = transaction)

        verify {
            internalBankService.captureFunds(any(), any(), bankAccount, invoiceBill.amountTotal)
            tedService.transfer(invoiceBill.recipient!!, invoiceBill.amountTotal, ACCOUNT_ID, 1)
        }
        verify(exactly = 0) { internalBankService.undoCaptureFunds(any(), any(), any(), any(), any()) }
        transaction.status shouldBe TransactionStatus.PROCESSING
        transaction.paymentData.toSingle().get<BalanceAuthorization>().status shouldBe BankOperationStatus.SUCCESS
        transaction.settlementData.getOperation<BankTransfer>().status shouldBe BankOperationStatus.ERROR
    }

    @Test
    fun `transaction should be processing when payment transfer returns unknown`() {
        every {
            internalBankService.captureFunds(
                any(),
                any(),
                bankAccount,
                any(),
            )
        } answers {
            BankTransfer(
                status = BankOperationStatus.UNKNOWN,
                amount = invoiceBill.amountTotal,
                gateway = FinancialServiceGateway.ARBI,
            )
        }

        val transaction = buildTransaction(bankAccount)

        balanceInvoiceCheckout.execute(transaction = transaction)

        verify {
            internalBankService.captureFunds(any(), any(), bankAccount, invoiceBill.amountTotal)
        }
        verify(exactly = 0) {
            tedService.transfer(invoiceBill.recipient!!, invoiceBill.amountTotal, ACCOUNT_ID, any())
            internalBankService.undoCaptureFunds(any(), any(), any(), any(), any())
        }
        transaction.status shouldBe TransactionStatus.PROCESSING
        transaction.paymentData.toSingle().get<BalanceAuthorization>().status shouldBe BankOperationStatus.UNKNOWN
        transaction.settlementData.settlementOperation shouldBe null
    }

    @Test
    fun `transaction should be processing when undoCaptureFunds returns error`() {
        every {
            tedService.transfer(
                any(),
                any(),
                any(),
                any(),
            )
        } answers {
            TEDResult(
                status = TEDStatus.Failure(message = "Error"),
                settleDate = getLocalDate(),
                authentication = "AUTH",
                bankTransactionId = 0,
                receipt = "receipt",
                gateway = FinancialServiceGateway.CELCOIN,
                amount = invoiceBill.amountTotal,
            )
        }
        every {
            internalBankService.undoCaptureFunds(
                any(),
                any(),
                bankAccount,
                any(),
                concessionariaBill.amountTotal,
            )
        } returns BankTransfer(
            status = BankOperationStatus.ERROR,
            amount = concessionariaBill.amountTotal,
            gateway = FinancialServiceGateway.ARBI,
        )
        val transaction = buildTransaction(bankAccount)

        balanceInvoiceCheckout.execute(transaction = transaction)

        verify {
            internalBankService.captureFunds(any(), any(), bankAccount, invoiceBill.amountTotal)
            tedService.transfer(invoiceBill.recipient!!, invoiceBill.amountTotal, ACCOUNT_ID, 1)
            internalBankService.undoCaptureFunds(any(), any(), bankAccount, any(), invoiceBill.amountTotal)
        }
        transaction.status shouldBe TransactionStatus.PROCESSING
        transaction.paymentData.toSingle().get<BalanceAuthorization>().status shouldBe BankOperationStatus.SUCCESS
        transaction.settlementData.getOperation<BankTransfer>().status shouldBe BankOperationStatus.ERROR
    }
}