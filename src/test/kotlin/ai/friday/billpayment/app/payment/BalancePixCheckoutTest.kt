package ai.friday.billpayment.app.payment

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.fingerprint.DeviceId
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.NewPixCommand
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.PixPaymentService
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyDetailsResult
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.PixTransactionError
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.balance
import ai.friday.billpayment.createSettlementDataFromBill
import ai.friday.billpayment.createSinglePaymentDataWithBalance
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.pixKeyAdded
import ai.friday.billpayment.pixKeyPaymentScheduled
import arrow.core.right
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.util.UUID
import java.util.stream.Stream
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.junit.jupiter.params.provider.ValueSource

internal class BalancePixCheckoutTest {

    private val pixPaymentService: PixPaymentService = mockk(relaxed = true)

    val pixKey = PixKey(
        type = PixKeyType.CPF,
        value = "***********",
    )
    private val pixKeyManagement: PixKeyManagement = mockk {
        every { findKeyDetails(any(), any()) } returns PixKeyDetailsResult(pixKeyDetails = PixKeyDetails(key = pixKey, holder = mockk(), owner = mockk()), e2e = "").right()
    }
    private val transactionService: TransactionService = mockk(relaxed = true)

    private val accountRepository: AccountRepository = mockk(relaxed = true)

    private val deviceFingerprintServiceMock: DeviceFingerprintService = mockk(relaxed = true)

    private val mockerWalletService = mockk<WalletService>()

    private val balancePixCheckout = BalancePixCheckout(pixPaymentService, 1L, 1, pixKeyManagement, transactionService, accountRepository, mockerWalletService, deviceFingerprintServiceMock)

    private val pix = Bill.build(pixKeyAdded, pixKeyPaymentScheduled)

    private val transaction = Transaction(
        type = TransactionType.INVOICE_PAYMENT,
        payer = ACCOUNT.toPayer(),
        paymentData = createSinglePaymentDataWithBalance(
            balance,
            pix.amountTotal,
            payment = null,
        ),
        settlementData = SettlementData(settlementTarget = pix, serviceAmountTax = 0, totalAmount = pix.amountTotal),
        nsu = 0,
        actionSource = ActionSource.System,
        walletId = pix.walletId,
    )

    @Test
    fun `should return payment failed on missing bank account`() {
        every {
            pixPaymentService.transfer(
                command = any(),
                deviceId = null,
            )
        } returns PixPaymentResult(
            status = PixPaymentStatus.REFUSED,
            idOrdemPagamento = "",
            endToEnd = "",
            error = PixTransactionError.PaymentGenericTemporaryError,
        )
        with(balancePixCheckout.execute(transaction)) {
            status shouldBe TransactionStatus.FAILED
            val payment = paymentData.toSingle().payment as BalanceAuthorization
            payment.status shouldBe BankOperationStatus.INVALID_DATA
            settlementData.settlementOperation.shouldNotBeNull()
            isRetryable() shouldBe true
        }
    }

    @ParameterizedTest
    @MethodSource("temporaryErrorsProvider")
    fun `should return ERRO on temporary error`(error: PixTransactionError) {
        val e2e = UUID.randomUUID().toString()
        val command = mockk<NewPixCommand> {
            every { bankOperationId } returns BankOperationId("*********")
        }
        every { pixPaymentService.transfer(command = command, deviceId = null) } answers {
            PixPaymentResult(
                status = PixPaymentStatus.ACKNOWLEDGED,
                idOrdemPagamento = command.bankOperationId.value,
                endToEnd = e2e,
            )
        }
        every {
            pixPaymentService.checkPaymentStatus(any(), ofType(BankOperationId::class))
        } answers {
            PixPaymentResult(
                status = PixPaymentStatus.FAILED,
                idOrdemPagamento = command.bankOperationId.value,
                endToEnd = e2e,
                error = error,
            )
        }
        with(balancePixCheckout.execute(transaction)) {
            status shouldBe TransactionStatus.FAILED
            val payment = paymentData.toSingle().payment as BalanceAuthorization
            payment.status shouldBe BankOperationStatus.SUCCESS
            verify { pixPaymentService.checkPaymentStatus(any(), any()) }
            val settlement = settlementData.settlementOperation as BankTransfer
            settlement.status shouldBe BankOperationStatus.ERROR
            settlement.errorDescription shouldBe error.code
            isRetryable() shouldBe true
        }
    }

    @ParameterizedTest
    @ValueSource(strings = ["ACKNOWLEDGED", "UNKNOWN"])
    fun `should return processing when status checked is not final`(checkPaymentStatus: PixPaymentStatus) {
        val e2e = UUID.randomUUID().toString()
        val idOrdemPagamento = "1"
        every { pixPaymentService.transfer(command = any(), deviceId = null) } returns PixPaymentResult(
            status = PixPaymentStatus.ACKNOWLEDGED,
            idOrdemPagamento = idOrdemPagamento,
            endToEnd = e2e,
        )
        every {
            pixPaymentService.checkPaymentStatus(any(), ofType(BankOperationId::class))
        } returns PixPaymentResult(
            status = checkPaymentStatus,
            idOrdemPagamento = idOrdemPagamento,
            endToEnd = e2e,
        )
        with(balancePixCheckout.execute(transaction)) {
            status shouldBe TransactionStatus.PROCESSING
            val payment = paymentData.toSingle().payment as BalanceAuthorization
            payment.status shouldBe BankOperationStatus.SUCCESS

            verify { pixPaymentService.checkPaymentStatus(any(), payment.operationId) }

            val settlement = settlementData.settlementOperation as BankTransfer
            settlement.status shouldBe BankOperationStatus.UNKNOWN
            settlement.authentication shouldBe e2e
        }
    }

    @ParameterizedTest
    @MethodSource("permanentErrorsProvider")
    fun `should return INVALID_DATA on permanent pix errors`(error: PixTransactionError) {
        val e2e = UUID.randomUUID().toString()
        val command = mockk<NewPixCommand>() {
            every { bankOperationId } returns BankOperationId("*********")
        }
        every { pixPaymentService.transfer(command = any(), deviceId = null) } answers {
            PixPaymentResult(
                status = PixPaymentStatus.ACKNOWLEDGED,
                idOrdemPagamento = command.bankOperationId.value,
                endToEnd = e2e,
            )
        }
        every {
            pixPaymentService.checkPaymentStatus(any(), ofType(BankOperationId::class))
        } answers {
            PixPaymentResult(
                status = PixPaymentStatus.REFUSED,
                idOrdemPagamento = command.bankOperationId.value,
                endToEnd = e2e,
                error = error,
            )
        }
        with(balancePixCheckout.execute(transaction)) {
            status shouldBe TransactionStatus.FAILED
            val payment = paymentData.toSingle().payment as BalanceAuthorization
            payment.status shouldBe BankOperationStatus.SUCCESS
            verify { pixPaymentService.checkPaymentStatus(any(), any()) }
            val settlement = settlementData.settlementOperation as BankTransfer
            settlement.status shouldBe BankOperationStatus.INVALID_DATA
            settlement.errorDescription shouldBe error.code
            isRetryable() shouldBe false
        }
    }

    @Test
    fun `should return settlement failed on permanent pix key error`() {
        every { pixPaymentService.transfer(command = any(), deviceId = null) } returns PixPaymentResult(
            status = PixPaymentStatus.REFUSED,
            idOrdemPagamento = "",
            endToEnd = "",
            error = PixTransactionError.SettlementGenericPermanentError,
        )

        with(balancePixCheckout.execute(transaction)) {
            status shouldBe TransactionStatus.FAILED
            val payment = paymentData.toSingle().payment as BalanceAuthorization
            payment.status shouldBe BankOperationStatus.SUCCESS
            val settlement = settlementData.settlementOperation as BankTransfer
            settlement.status shouldBe BankOperationStatus.INVALID_DATA
            isRetryable() shouldBe false
        }
    }

    @Test
    fun `should return settlement failed on error while init operation`() {
        every { pixPaymentService.transfer(command = any(), deviceId = null) } returns PixPaymentResult(
            status = PixPaymentStatus.FAILED,
            idOrdemPagamento = "",
            endToEnd = "",
            error = PixTransactionError.PaymentGenericTemporaryError,
        )

        with(balancePixCheckout.execute(transaction)) {
            status shouldBe TransactionStatus.FAILED
            val payment = paymentData.toSingle().payment as BalanceAuthorization
            payment.status shouldBe BankOperationStatus.SUCCESS
            val settlement = settlementData.settlementOperation as BankTransfer
            settlement.status shouldBe BankOperationStatus.ERROR
            isRetryable() shouldBe true
        }
    }

    @Test
    fun `quando houver um bank operation id salvo, deve retornar a transacao`() {
        val tran = transaction.copy(
            paymentData = createSinglePaymentDataWithBalance(
                balance,
                pix.amountTotal,
                payment = BalanceAuthorization(
                    operationId = BankOperationId("123"),
                    status = BankOperationStatus.ERROR,
                    timeout = false,
                    amount = pix.amountTotal,
                    errorDescription = "",
                    paymentGateway = FinancialServiceGateway.ARBI,
                ),
            ),
        )
        with(balancePixCheckout.execute(tran)) {
            status shouldBe TransactionStatus.PROCESSING
            this.isRetryable().shouldBeTrue()
        }

        verify { pixPaymentService wasNot called }
    }

    @ParameterizedTest
    @ValueSource(strings = ["ACKNOWLEDGED", "SCHEDULED", "SUCCESS"])
    fun `quando for pix por conta bancária deve salvar a transação no status do pix`(paymentStatus: PixPaymentStatus) {
        every {
            pixPaymentService.transfer(command = any(), deviceId = null)
        } returns PixPaymentResult(
            status = paymentStatus,
            idOrdemPagamento = "",
            endToEnd = "",
        )

        val accountId = transaction.payer.accountId
        val updatedTransaction = transaction.copy(type = TransactionType.DIRECT_INVOICE, settlementData = createSettlementDataFromBill(Bill.build(pixAdded.copy(walletId = WalletId(accountId.value)), pixKeyPaymentScheduled)))

        balancePixCheckout.execute(updatedTransaction)
        verify { transactionService.save(updatedTransaction) }
    }

    @Test
    fun `should return settlement failed on temporary pix key error`() {
        every { pixPaymentService.transfer(command = any(), deviceId = null) } returns PixPaymentResult(
            status = PixPaymentStatus.REFUSED,
            idOrdemPagamento = "",
            endToEnd = "",
            error = PixTransactionError.SettlementGenericTemporaryError,
        )

        with(balancePixCheckout.execute(transaction)) {
            status shouldBe TransactionStatus.FAILED
            val payment = paymentData.toSingle().payment as BalanceAuthorization
            payment.status shouldBe BankOperationStatus.SUCCESS
            val settlement = settlementData.settlementOperation as BankTransfer
            settlement.status shouldBe BankOperationStatus.ERROR
            isRetryable() shouldBe true
        }
    }

    @Test
    fun `should return success when initPayment fails but checkPaymentStatus is successful`() {
        every { pixPaymentService.transfer(command = any(), deviceId = null) } returns PixPaymentResult(
            status = PixPaymentStatus.UNKNOWN,
            idOrdemPagamento = "",
            endToEnd = "",
            error = PixTransactionError.UnknownTemporaryError,
        )
        val expectedE2E = "123abc"
        every {
            pixPaymentService.checkPaymentStatus(any(), ofType(BankOperationId::class))
        } answers {
            PixPaymentResult(
                status = PixPaymentStatus.SUCCESS,
                idOrdemPagamento = secondArg<BankOperationId>().value,
                endToEnd = expectedE2E,
                error = null,
            )
        }

        with(balancePixCheckout.execute(transaction)) {
            status shouldBe TransactionStatus.COMPLETED
            val payment = paymentData.toSingle().payment as BalanceAuthorization
            payment.status shouldBe BankOperationStatus.SUCCESS
            val settlement = settlementData.settlementOperation as BankTransfer
            settlement.status shouldBe BankOperationStatus.SUCCESS
            settlement.authentication shouldBe expectedE2E
        }
    }

    @Test
    fun `quando tiver um deviceId, deve passar na chamada`() {
        val expectedDeviceId = DeviceId("456")
        every {
            pixPaymentService.transfer(command = any(), deviceId = null)
        } returns PixPaymentResult(
            status = PixPaymentStatus.SUCCESS,
            idOrdemPagamento = "",
            endToEnd = "",
        )

        every { deviceFingerprintServiceMock.getOrNull(transaction.payer.accountId) } returns mockk {
            every { deviceIds } returns mapOf(AccountNumber("1235551") to expectedDeviceId)
        }

        balancePixCheckout.execute(transaction)

        verify { pixPaymentService.transfer(command = any(), deviceId = expectedDeviceId) }
    }

    companion object {
        @JvmStatic
        fun permanentErrorsProvider(): Stream<Arguments> {
            return Stream.of(

                Arguments.arguments(PixTransactionError.SettlementDestinationAccountNotFound),
                Arguments.arguments(PixTransactionError.SettlementDestinationAccountNotAvailable),
                Arguments.arguments(PixTransactionError.SettlementGenericPermanentError),
                Arguments.arguments(PixTransactionError.SettlementDestinationInstitutionNotAllowed),
            )
        }

        @JvmStatic
        fun temporaryErrorsProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(PixTransactionError.BusinessSingleTransactionLimit),
                Arguments.arguments(PixTransactionError.BusinessSameValuePaymentsExceeded),
                Arguments.arguments(PixTransactionError.PaymentGenericTemporaryError),
                Arguments.arguments(PixTransactionError.BusinessDailyLimit),
                Arguments.arguments(PixTransactionError.SettlementGenericTemporaryError),
                Arguments.arguments(PixTransactionError.SettlementPaymentRefusedByDestination),
            )
        }
    }
}