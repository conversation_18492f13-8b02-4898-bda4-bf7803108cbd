package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.integrations.ProcessScheduledBillsPublisher
import ai.friday.billpayment.app.integrations.ScheduledWalletRepository
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.ZonedDateTime
import java.util.concurrent.Executors
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import reactor.core.publisher.Flux

class StartProcessScheduledBillsAsyncServiceTest {

    private val scheduledWalletRepository = mockk<ScheduledWalletRepository>()
    private val processScheduledBillsPublisher = mockk<ProcessScheduledBillsPublisher>(relaxed = true)

    private val service = StartProcessScheduledBillsAsyncService(
        scheduledWalletRepository = scheduledWalletRepository,
        processScheduledBillsPublisher = processScheduledBillsPublisher,
        jobExecutor = Executors.newCachedThreadPool(),
    )

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `should send wallets for processing`(includeSubscription: Boolean) {
        val now = ZonedDateTime.of(2023, 8, 10, 0, 0, 0, 0, brazilTimeZone)
        every {
            scheduledWalletRepository.findWalletsWithScheduledBillsBetween(now.toLocalDate().minusMonths(3), now.toLocalDate())
        } returns Flux.just(WalletId("123"), WalletId("456"))

        withGivenDateTime(now) {
            service.start(includeSubscription)
        }

        verify {
            processScheduledBillsPublisher.publish(
                walletIdList = listOf(WalletId("123"), WalletId("456")),
                scheduleDate = now.toLocalDate(),
                includeSubscription = includeSubscription,
            )
        }
    }
}