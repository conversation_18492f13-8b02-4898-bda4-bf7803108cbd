package ai.friday.billpayment.app.payment.transaction

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.PaymentStarted
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.payment.BillPaymentCommand
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.boletoTransaction
import ai.friday.billpayment.createPaymentMethodsDetailWithBalance
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.BILL_ID
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID_3
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.scheduledBill
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Test

internal class StartTransactionTest {

    private val transactionService: TransactionService = mockk(relaxUnitFun = true)

    private val billEventPublisher: BillEventPublisher = mockk(relaxUnitFun = true)

    private val startTransaction = StartTransaction(
        transactionService = transactionService,
        billEventPublisher = billEventPublisher,
    )

    @Test
    fun `deve criar a transacao e publicar o evento de PaymentStarted`() {
        val command = BillPaymentCommand(
            walletId = WalletId(WALLET_ID),
            billId = BillId(BILL_ID),
            actionSource = ActionSource.Api(accountId = AccountId(ACCOUNT_ID)),
            paymentDetails = createPaymentMethodsDetailWithBalance(
                paymentMethodId = AccountPaymentMethodId(
                    PAYMENT_METHOD_ID_3,
                ),
                amount = scheduledBill.amount,
            ),
        )

        every {
            transactionService.initBillPaymentTransaction(any())
        } returns boletoTransaction

        startTransaction.execute(command) shouldBe boletoTransaction

        val slot = slot<PaymentStarted>()

        verify {
            billEventPublisher.publish(boletoTransaction.settlementData.getTarget(), capture(slot))
        }

        with(slot.captured) {
            billId shouldBe boletoTransaction.settlementData.getTarget<Bill>().billId
            walletId shouldBe boletoTransaction.settlementData.getTarget<Bill>().walletId
            transactionId shouldBe boletoTransaction.id
            correlationId shouldBe boletoTransaction.correlationId
            actionSource shouldBe command.actionSource
        }
    }
}