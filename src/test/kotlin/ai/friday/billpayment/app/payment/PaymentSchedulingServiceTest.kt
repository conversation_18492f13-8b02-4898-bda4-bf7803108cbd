package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.bill.BatchSchedulingId
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.schedule.toScheduleBill
import ai.friday.billpayment.app.integrations.ScheduledBillRepository
import ai.friday.billpayment.app.integrations.ScheduledWalletRepository
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.createPaymentMethodsDetailWithBalance
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.paymentMethodId2
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

internal class PaymentSchedulingServiceTest {

    private val walletFixture = WalletFixture()

    @Test
    fun `deve criar um agendamento que não expira quando for uma assinatura friday`() {
        val subscriptionBill = Bill.build(billAddedFicha.copy(subscriptionFee = true))

        val scheduleBill = subscriptionBill.toScheduleBill(
            paymentWalletId = billAddedFicha.walletId,
            paymentWalletFounder = walletFixture.founder,
            scheduleDate = LocalDate.now(),
            scheduleTo = ScheduleTo.ASAP,
            paymentMethodsDetail = PaymentMethodsDetailWithBalance(
                amount = billAddedFicha.amountTotal,
                paymentMethodId = AccountPaymentMethodId(value = "foo"),
                calculationId = null,
            ),
        )

        scheduleBill.expires shouldBe false
    }

    @Test
    fun `deve criar um agendamento que expira quando não for uma aasinatura friday`() {
        val regularBill = Bill.build(billAddedFicha.copy(subscriptionFee = false))

        val scheduleBill = regularBill.toScheduleBill(
            paymentWalletId = billAddedFicha.walletId,
            paymentWalletFounder = walletFixture.founder,
            scheduleDate = LocalDate.now(),
            scheduleTo = ScheduleTo.ASAP,
            paymentMethodsDetail = PaymentMethodsDetailWithBalance(
                amount = billAddedFicha.amountTotal,
                paymentMethodId = AccountPaymentMethodId(value = "foo"),
                calculationId = null,
            ),
        )

        scheduleBill.expires shouldBe true
    }

    @Nested
    @DisplayName("ordenação de bills")
    inner class GetOrderedScheduledBills {
        private val smallAmount = 1L
        private val amount = 100L
        private val bigAmount = 100000L
        private val earlyScheduleDate = getLocalDate().minusDays(2)
        private val scheduleDate = getLocalDate().minusDays(1)
        private val lateScheduleDate = getLocalDate()

        private val expectedList = listOf(
            ScheduledBill(
                WalletId(WALLET_ID),
                BillId(UUID.randomUUID().toString()),
                earlyScheduleDate,
                BillType.CONCESSIONARIA,
                amount,
                ScheduleTo.DUE_DATE,
                paymentLimitTime = LocalTime.parse("10:00"),
                createPaymentMethodsDetailWithBalance(paymentMethodId2, amount),
                batchSchedulingId = BatchSchedulingId(),
                isSelfTransfer = false,
            ),
            ScheduledBill(
                WalletId(WALLET_ID),
                BillId(UUID.randomUUID().toString()),
                earlyScheduleDate,
                BillType.FICHA_COMPENSACAO,
                amount,
                ScheduleTo.DUE_DATE,
                paymentLimitTime = LocalTime.parse("10:00"),
                createPaymentMethodsDetailWithBalance(paymentMethodId2, amount),
                batchSchedulingId = BatchSchedulingId(),
                isSelfTransfer = false,
            ),
            ScheduledBill(
                WalletId(WALLET_ID),
                BillId(UUID.randomUUID().toString()),
                earlyScheduleDate,
                BillType.INVOICE,
                smallAmount,
                ScheduleTo.DUE_DATE,
                paymentLimitTime = null,
                createPaymentMethodsDetailWithBalance(paymentMethodId2, smallAmount),
                batchSchedulingId = BatchSchedulingId(),
                isSelfTransfer = false,
            ),
            ScheduledBill(
                WalletId(WALLET_ID),
                BillId(UUID.randomUUID().toString()),
                earlyScheduleDate,
                BillType.INVOICE,
                amount,
                ScheduleTo.DUE_DATE,
                paymentLimitTime = null,
                createPaymentMethodsDetailWithBalance(paymentMethodId2, amount),
                batchSchedulingId = BatchSchedulingId(),
                isSelfTransfer = false,
            ),
            ScheduledBill(
                WalletId(WALLET_ID),
                BillId(UUID.randomUUID().toString()),
                earlyScheduleDate,
                BillType.INVOICE,
                bigAmount,
                ScheduleTo.ASAP,
                null,
                createPaymentMethodsDetailWithBalance(paymentMethodId2, bigAmount),
                batchSchedulingId = BatchSchedulingId(),
                isSelfTransfer = false,
            ),
            ScheduledBill(
                WalletId(WALLET_ID),
                BillId(UUID.randomUUID().toString()),
                scheduleDate,
                BillType.CONCESSIONARIA,
                amount,
                ScheduleTo.DUE_DATE,
                paymentLimitTime = LocalTime.parse("10:00"),
                createPaymentMethodsDetailWithBalance(paymentMethodId2, amount),
                batchSchedulingId = BatchSchedulingId(),
                isSelfTransfer = false,
            ),
            ScheduledBill(
                WalletId(WALLET_ID),
                BillId(UUID.randomUUID().toString()),
                scheduleDate,
                BillType.FICHA_COMPENSACAO,
                smallAmount,
                ScheduleTo.DUE_DATE,
                paymentLimitTime = LocalTime.parse("10:00"),
                createPaymentMethodsDetailWithBalance(paymentMethodId2, smallAmount),
                batchSchedulingId = BatchSchedulingId(),
                isSelfTransfer = false,
            ),
            ScheduledBill(
                WalletId(WALLET_ID),
                BillId(UUID.randomUUID().toString()),
                scheduleDate,
                BillType.FICHA_COMPENSACAO,
                amount,
                ScheduleTo.DUE_DATE,
                paymentLimitTime = LocalTime.parse("10:00"),
                createPaymentMethodsDetailWithBalance(paymentMethodId2, amount),
                batchSchedulingId = BatchSchedulingId(),
                isSelfTransfer = false,
            ),
            ScheduledBill(
                WalletId(WALLET_ID),
                BillId(UUID.randomUUID().toString()),
                scheduleDate,
                BillType.FICHA_COMPENSACAO,
                bigAmount,
                ScheduleTo.DUE_DATE,
                paymentLimitTime = LocalTime.parse("10:00"),
                createPaymentMethodsDetailWithBalance(paymentMethodId2, bigAmount),
                batchSchedulingId = BatchSchedulingId(),
                isSelfTransfer = false,
            ),
            ScheduledBill(
                WalletId(WALLET_ID),
                BillId(UUID.randomUUID().toString()),
                scheduleDate,
                BillType.INVOICE,
                amount,
                ScheduleTo.DUE_DATE,
                paymentLimitTime = null,
                createPaymentMethodsDetailWithBalance(paymentMethodId2, amount),
                batchSchedulingId = BatchSchedulingId(),
                isSelfTransfer = false,
            ),
            ScheduledBill(
                WalletId(WALLET_ID),
                BillId(UUID.randomUUID().toString()),
                lateScheduleDate,
                BillType.INVOICE,
                amount,
                ScheduleTo.DUE_DATE,
                paymentLimitTime = null,
                createPaymentMethodsDetailWithBalance(paymentMethodId2, amount),
                batchSchedulingId = BatchSchedulingId(),
                isSelfTransfer = false,
            ),
            ScheduledBill(
                WalletId(WALLET_ID),
                BillId(UUID.randomUUID().toString()),
                lateScheduleDate,
                BillType.PIX,
                amount,
                ScheduleTo.ASAP,
                paymentLimitTime = null,
                createPaymentMethodsDetailWithBalance(paymentMethodId2, amount),
                batchSchedulingId = BatchSchedulingId(),
                isSelfTransfer = false,
            ),
        )

        @Test
        fun `should order list of scheduled bills on scheduled data, billType and amount`() {
            val scheduledBillRepository = mockk<ScheduledBillRepository>()
            val scheduledWalletRepository = mockk<ScheduledWalletRepository>()
            val paymentSchedulingService = PaymentSchedulingService(scheduledBillRepository, scheduledWalletRepository)
            (0..20).forEach { _ ->
                every {
                    scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(any(), any())
                } returns expectedList.shuffled()

                val sorted = paymentSchedulingService.getOrderedScheduledBills(
                    WalletId(WALLET_ID),
                    getLocalDate(),
                )
                sorted shouldBe expectedList
            }
        }
    }
}