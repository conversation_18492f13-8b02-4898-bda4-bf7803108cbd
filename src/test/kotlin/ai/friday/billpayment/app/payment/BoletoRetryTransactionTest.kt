package ai.friday.billpayment.app.payment

import DynamoDBUtils
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.CouldNotAcquireLockException
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.CreditCardChargebackTrackDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbTransactionRepository
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillSynchronizationStatus
import ai.friday.billpayment.app.bill.PaymentFailed
import ai.friday.billpayment.app.bill.SynchronizeBillResponse
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.payment.captureFunds.FridayCaptureFundsLocator
import ai.friday.billpayment.app.payment.checkout.SyncBoletoCheckout
import ai.friday.billpayment.app.payment.transaction.RetryTransaction
import ai.friday.billpayment.app.payment.transaction.RollbackTransaction
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.createSinglePaymentDataWithBalance
import ai.friday.billpayment.defaultTransactionEntityConverter
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadBalancePaymentMethod
import ai.friday.billpayment.internalBankAccount
import ai.friday.billpayment.paymentMethodId2
import ai.friday.billpayment.successConcessionariaValidationResponse
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.result.shouldBeFailure
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import net.javacrumbs.shedlock.core.SimpleLock
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

internal class BoletoRetryTransactionTest {

    private val notificationMock: NotificationAdapter = mockk(relaxed = true)

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val enhancedClient = DynamoDBUtils.setupDynamoDB()
    private val billEventDAO = BillEventDynamoDAO(enhancedClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)
    private val transactionDAO = TransactionDynamoDAO(enhancedClient)
    private val creditCardChargebackTrackDAO = CreditCardChargebackTrackDynamoDAO(enhancedClient)
    private val billEventRepository = BillEventDBRepository(
        billEventDAO = billEventDAO,
        uniqueConstraintDAO = uniqueConstraintDAO,
        featureConfiguration = allFalseFeatureConfiguration,
        transactionDynamo = transactionDynamo,
    )

    private val boletoSettlementService: BoletoSettlementService = mockk()

    private val updateBillService: UpdateBillService = mockk(relaxed = true)

    private val internalBankService: InternalBankService = spyk(
        InternalBankService(
            accountService = mockk(),
            bankAccountService = mockk(),
            internalBankRepository = mockk(),
            messagePublisher = mockk(),
            omnibusBankAccountConfiguration = mockk(),
            balanceService = mockk(),
            notificationAdapter = mockk(),
            walletService = mockk(),
            lockProvider = mockk(),
            pixPaymentService = mockk(),
        ),
    )

    private val checkoutLocator = DefaultCheckoutLocator(
        balanceInvoiceCheckout = mockk(),
        balancePixCheckout = mockk(),
        balanceDirectInvoiceCheckout = mockk(),
        boletoCheckout = SyncBoletoCheckout(
            captureFundsLocator = FridayCaptureFundsLocator(
                fundProvider = internalBankService,
                acquirerService = mockk(),
                creditCardFraudPreventionService = mockk(),
            ),
            boletoSettlementService = boletoSettlementService,
        ),
        asyncSettlementBoletoCheckout = mockk(),
        featureConfiguration = mockk(relaxed = true),
        investmentCheckout = mockk(),
    )

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)

    val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )
    val walletService: WalletService = mockk() {
        every {
            findWallet(any())
        } returns mockk() {
            every {
                founder
            } returns mockk() {
                every {
                    accountId
                } returns ACCOUNT.accountId
            }
        }
    }

    private val transactionService = TransactionService(
        accountRepository = accountRepository,
        updateBillService = updateBillService,
        notificationAdapter = notificationMock,
        walletService = walletService,
        transactionRepository = DynamoDbTransactionRepository(
            transactionDAO = transactionDAO,
            creditCardChargebackTrackDAO = creditCardChargebackTrackDAO,
            accountRepository = accountRepository,
            transactionEntityConverter = defaultTransactionEntityConverter(billEventRepository, mockk(), transactionDynamo),
        ),
    )

    private val settlementRetry: SettlementRetry = mockk()

    val billEventPublisher = mockk<BillEventPublisher>(relaxed = true)

    private val internalLock: InternalLock = mockk(relaxed = true)

    private val rollbackTransaction = RollbackTransaction(
        transactionService = transactionService,
        billEventPublisher = billEventPublisher,
        checkoutLocator = checkoutLocator,
        paymentFailedScheduleResolver = mockk(relaxed = true),
    )

    private val service = RetryTransaction(
        transactionService = transactionService,
        checkoutLocator = checkoutLocator,
        settlementRetry = settlementRetry,
        rollbackTransaction = rollbackTransaction,
        lockProvider = internalLock,
    )

    private val bankAccount = internalBankAccount

    private val concessionariaBill = Bill.build(billAdded)

    private val successBankTransfer = BankTransfer(
        status = BankOperationStatus.SUCCESS,
        amount = billAdded.amountTotal,
        gateway = FinancialServiceGateway.ARBI,
    )

    private val errorDescription = "Generic error"

    private val transactionId = 12L

    @Test
    fun `should return failure when lock cannot be acquired`() {
        every { internalLock.acquireLock(any()) } returns null

        service.retryTransaction(boletoTransaction.id)
            .shouldBeFailure<CouldNotAcquireLockException>()
    }

    @Test
    fun `should release lock even on error`() {
        val mockLock = mockk<SimpleLock>(relaxed = true)
        every { internalLock.acquireLock(any()) } returns mockLock
        every {
            settlementRetry.resolve(any())
        } throws RuntimeException("Test exception")

        transactionService.save(boletoTransaction)

        service.retryTransaction(boletoTransaction.id).shouldBeFailure<RuntimeException>()

        verify(exactly = 1) { mockLock.unlock() }
    }

    @BeforeEach
    fun setUp() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        billEventRepository.save(billAdded)
        billEventRepository.save(billPaymentStart)
        with(bankAccount) { loadBalancePaymentMethod(accountRepository, bankNo, routingNo, accountNo.toBigInteger(), accountDv) }
        every {
            updateBillService.synchronizeBill(
                ofType(Bill::class),
                any(),
            )
        } answers { SynchronizeBillResponse(status = BillSynchronizationStatus.BillNotModified) }
        every { boletoSettlementService.settlementValidation(ofType(Bill::class)) } answers { successConcessionariaValidationResponse }
        every {
            boletoSettlementService.initPayment(
                ofType(Bill::class),
                ofType(Int::class),
                ofType(String::class),
                ofType(String::class),
                any(),
            )
        } answers { BillPaymentResponse(status = BoletoSettlementStatus.AUTHORIZED, transactionId = transactionId) }
        every {
            boletoSettlementService.confirmPayment(
                ofType(String::class),
                ofType(Int::class),
                ofType(String::class),
            )
        } just runs
        every {
            boletoSettlementService.cancelPayment(
                ofType(String::class),
                ofType(Int::class),
                ofType(String::class),
            )
        } just runs
        every {
            boletoSettlementService.queryPayment(any())
        } returns BoletoSettlementStatus.AUTHORIZED
        every {
            settlementRetry.resolve(any())
        } returns TransactionRollbackException().left()
    }

    private val balance = AccountPaymentMethod(
        id = paymentMethodId2,
        accountId = AccountId(ACCOUNT_ID),
        status = AccountPaymentMethodStatus.ACTIVE,
        method = bankAccount,
        created = getZonedDateTime(),
    )
    private val boletoTransaction = Transaction(
        type = TransactionType.BOLETO_PAYMENT,
        payer = Payer(AccountId(ACCOUNT_ID), DOCUMENT, ACCOUNT.name),
        paymentData = createSinglePaymentDataWithBalance(
            balance,
            concessionariaBill.amountTotal,
        ),
        settlementData = SettlementData(concessionariaBill, 0, concessionariaBill.amountTotal),
        nsu = 1,
        actionSource = ActionSource.Scheduled,
        walletId = concessionariaBill.walletId,
        created = getZonedDateTime().minusMinutes(60),
    )

    @Test
    fun `should throw exception when settlement rollback fails`() {
        val balanceAuthorization = BalanceAuthorization(
            operationId = BankOperationId("1"),
            status = BankOperationStatus.ERROR,
            amount = 10,
            errorDescription = "Error",
            paymentGateway = FinancialServiceGateway.ARBI,
        )
        val boletoSettlementResult = BoletoSettlementResult(
            FinancialServiceGateway.CELCOIN,
            BoletoSettlementStatus.AUTHORIZED,
            "1",
            1,
            ACCOUNT_ID,
            "",
            "",
        )
        savePaymentAndSettlement(balanceAuthorization, boletoSettlementResult)
        every {
            boletoSettlementService.cancelPayment(
                ofType(String::class),
                ofType(Int::class),
                ofType(String::class),
            )
        } throws BoletoSettlementException(FinancialServiceGateway.CELCOIN, "")

        service.retryTransaction(boletoTransaction.id).shouldBeFailure<TransactionRollbackException>()

        val transactionDB = transactionService.findTransactionById(boletoTransaction.id)
        assertTransactionStatuses(
            transactionDB,
            TransactionStatus.PROCESSING,
            BankOperationStatus.ERROR,
            BoletoSettlementStatus.AUTHORIZED,
        )
        verify(exactly = 0) {
            updateBillService.publishEvent(boletoTransaction.settlementData.getTarget(), any())
        }
    }

    @Test
    fun `should throw exception when payment rollback fails`() {
        every {
            internalBankService.undoCaptureFunds(
                any(),
                any(),
                bankAccount,
                any(),
                billAdded.amountTotal,
            )
        } answers {
            BankTransfer(
                status = BankOperationStatus.ERROR,
                amount = billAdded.amountTotal,
                errorDescription = "",
                gateway = FinancialServiceGateway.ARBI,
            )
        }
        val balanceAuthorization =
            BalanceAuthorization(
                operationId = BankOperationId("1"),
                status = BankOperationStatus.SUCCESS,
                amount = 10,
                paymentGateway = FinancialServiceGateway.ARBI,
            )
        val boletoSettlementResult =
            BoletoSettlementResult(
                FinancialServiceGateway.CELCOIN,
                BoletoSettlementStatus.VOIDED,
                "1",
                1,
                ACCOUNT_ID,
                "",
                "",
            )
        savePaymentAndSettlement(balanceAuthorization, boletoSettlementResult)

        service.retryTransaction(boletoTransaction.id).shouldBeFailure<TransactionRollbackException>()

        val transactionDB = transactionService.findTransactionById(boletoTransaction.id)
        assertTransactionStatuses(
            transactionDB,
            TransactionStatus.PROCESSING,
            BankOperationStatus.SUCCESS,
            BoletoSettlementStatus.VOIDED,
        )
        verify(exactly = 0) {
            updateBillService.publishEvent(boletoTransaction.settlementData.getTarget(), any())
        }
    }

    @Test
    fun `should fail transaction on settlement cancel retry`() {
        val balanceAuthorization = BalanceAuthorization(
            operationId = BankOperationId("1"),
            status = BankOperationStatus.ERROR,
            amount = 10,
            errorDescription = errorDescription,
            paymentGateway = FinancialServiceGateway.ARBI,
        )
        val boletoSettlementResult = BoletoSettlementResult(
            FinancialServiceGateway.CELCOIN,
            BoletoSettlementStatus.AUTHORIZED,
            "1",
            1,
            ACCOUNT_ID,
            "",
            "",
        )
        savePaymentAndSettlement(balanceAuthorization, boletoSettlementResult)

        service.retryTransaction(boletoTransaction.id)

        val transactionDB = transactionService.findTransactionById(boletoTransaction.id)
        assertTransactionStatuses(
            transactionDB,
            TransactionStatus.FAILED,
            BankOperationStatus.ERROR,
            BoletoSettlementStatus.VOIDED,
        )
        val slot = slot<PaymentFailed>()
        verify {
            billEventPublisher.publish(boletoTransaction.settlementData.getTarget(), capture(slot))
        }
        slot.captured.errorDescription shouldBe errorDescription
    }

    @Test
    fun `should fail transaction on payment cancel retry`() {
        every {
            internalBankService.undoCaptureFunds(
                any(),
                any(),
                bankAccount,
                any(),
                billAdded.amountTotal,
            )
        } answers { successBankTransfer }
        val balanceAuthorization =
            BalanceAuthorization(
                operationId = BankOperationId("1"),
                status = BankOperationStatus.SUCCESS,
                amount = billAdded.amountTotal,
                paymentGateway = FinancialServiceGateway.ARBI,
            )
        val boletoSettlementResult = BoletoSettlementResult(
            FinancialServiceGateway.CELCOIN,
            BoletoSettlementStatus.AUTHORIZED,
            "1",
            1,
            ACCOUNT_ID,
            errorCode00,
            errorDescription,
        )
        savePaymentAndSettlement(balanceAuthorization, boletoSettlementResult)

        service.retryTransaction(boletoTransaction.id)

        val transactionDB = transactionService.findTransactionById(boletoTransaction.id)
        assertTransactionStatuses(
            transactionDB,
            TransactionStatus.FAILED,
            BankOperationStatus.REFUNDED,
            BoletoSettlementStatus.VOIDED,
        )
        val slot = slot<PaymentFailed>()
        verify {
            billEventPublisher.publish(boletoTransaction.settlementData.getTarget(), capture(slot))
        }
        slot.captured.errorDescription shouldBe errorDescription
    }

    @Test
    fun `should throw exception when settlement is voided but payment fails to undo`() {
        every {
            internalBankService.undoCaptureFunds(
                any(),
                any(),
                bankAccount,
                any(),
                billAdded.amountTotal,
            )
        } answers {
            BankTransfer(
                status = BankOperationStatus.ERROR,
                amount = billAdded.amountTotal,
                errorDescription = "",
                gateway = FinancialServiceGateway.ARBI,
            )
        }
        val balanceAuthorization =
            BalanceAuthorization(
                operationId = BankOperationId("1"),
                status = BankOperationStatus.SUCCESS,
                amount = 10,
                paymentGateway = FinancialServiceGateway.ARBI,
            )
        val boletoSettlementResult = BoletoSettlementResult(
            FinancialServiceGateway.CELCOIN,
            BoletoSettlementStatus.AUTHORIZED,
            "1",
            1,
            ACCOUNT_ID,
            errorCode00,
            "ERROR",
        )
        savePaymentAndSettlement(balanceAuthorization, boletoSettlementResult)

        service.retryTransaction(boletoTransaction.id).shouldBeFailure<TransactionRollbackException>()

        val transactionDB = transactionService.findTransactionById(boletoTransaction.id)
        assertTransactionStatuses(
            transactionDB,
            TransactionStatus.PROCESSING,
            BankOperationStatus.SUCCESS,
            BoletoSettlementStatus.VOIDED,
        )

        verify(exactly = 0) {
            updateBillService.publishEvent(boletoTransaction.settlementData.getTarget(), any())
        }
    }

    @ParameterizedTest
    @EnumSource(
        FinancialServiceGateway::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["CELCOIN", "FRIDAY"],
    )
    fun `should check payment status when settlement gateway is CELCOIN or FRIDAY`(settlementGateway: FinancialServiceGateway) {
        every {
            settlementRetry.resolve(any())
        } returns RetrySettlementStatus.COMPLETED.right()

        val balanceAuthorization =
            BalanceAuthorization(
                operationId = BankOperationId("1"),
                status = BankOperationStatus.SUCCESS,
                amount = 10,
                paymentGateway = FinancialServiceGateway.ARBI,
            )
        val boletoSettlementResult = BoletoSettlementResult(
            gateway = settlementGateway,
            status = BoletoSettlementStatus.AUTHORIZED,
            bankTransactionId = "1",
            externalNsu = 1,
            externalTerminal = ACCOUNT_ID,
            errorCode = errorCode00,
            errorDescription = "ERROR",
        )
        savePaymentAndSettlement(balanceAuthorization, boletoSettlementResult)

        service.retryTransaction(boletoTransaction.id)

        verify {
            settlementRetry.resolve(any())
        }
    }

    @Test
    fun `should fail transaction on capture funds times out retry`() {
        val timeoutDescription = "Timeout error"
        val operationid = BankOperationId("1")
        every {
            internalBankService.checkCaptureFunds(
                any(),
                bankAccount,
                operationid,
                any(),
            )
        } answers { false }
        val balanceAuthorization = BalanceAuthorization(
            operationId = operationid,
            status = BankOperationStatus.TIMEOUT,
            amount = 10,
            errorDescription = timeoutDescription,
            timeout = true,
            paymentGateway = FinancialServiceGateway.ARBI,
        )
        val boletoSettlementResult = BoletoSettlementResult(
            FinancialServiceGateway.CELCOIN,
            BoletoSettlementStatus.AUTHORIZED,
            "1",
            1,
            ACCOUNT_ID,
            "",
            "",
        )
        savePaymentAndSettlement(balanceAuthorization, boletoSettlementResult)

        service.retryTransaction(boletoTransaction.id)

        val transactionDB = transactionService.findTransactionById(boletoTransaction.id)
        assertTransactionStatuses(
            transactionDB,
            TransactionStatus.FAILED,
            BankOperationStatus.ERROR,
            BoletoSettlementStatus.VOIDED,
        )
        val slot = slot<PaymentFailed>()
        verify {
            internalBankService.checkCaptureFunds(any(), bankAccount, operationid, any())
            boletoSettlementService.cancelPayment(ofType(String::class), ofType(Int::class), ofType(String::class))
            billEventPublisher.publish(any(), capture(slot))
        }
        verify(exactly = 0) {
            internalBankService.undoCaptureFunds(any(), any(), any(), any(), any())
        }

        slot.captured.errorDescription shouldBe PaymentError.BALANCE_TIMEOUT.description
    }

    @Test
    fun `should rollback transaction on capture funds times out retry`() {
        val timeoutDescription = "Timeout error"
        val operationid = BankOperationId("1")
        every {
            internalBankService.checkCaptureFunds(
                any(),
                bankAccount,
                operationid,
                any(),
            )
        } answers { true }
        every {
            internalBankService.undoCaptureFunds(
                any(),
                any(),
                bankAccount,
                operationid,
                boletoTransaction.settlementData.totalAmount,
            )
        } answers {
            BankTransfer(
                status = BankOperationStatus.SUCCESS,
                amount = boletoTransaction.settlementData.totalAmount,
                errorDescription = "",
                gateway = FinancialServiceGateway.ARBI,
            )
        }
        val balanceAuthorization = BalanceAuthorization(
            operationId = operationid,
            status = BankOperationStatus.TIMEOUT,
            amount = boletoTransaction.settlementData.totalAmount,
            errorDescription = timeoutDescription,
            timeout = true,
            paymentGateway = FinancialServiceGateway.ARBI,
        )
        val boletoSettlementResult = BoletoSettlementResult(
            FinancialServiceGateway.CELCOIN,
            BoletoSettlementStatus.AUTHORIZED,
            "1",
            1,
            ACCOUNT_ID,
            "",
            "",
        )
        savePaymentAndSettlement(balanceAuthorization, boletoSettlementResult)

        service.retryTransaction(boletoTransaction.id)

        val transactionDB = transactionService.findTransactionById(boletoTransaction.id)
        assertTransactionStatuses(
            transactionDB,
            TransactionStatus.FAILED,
            BankOperationStatus.REFUNDED,
            BoletoSettlementStatus.VOIDED,
        )
        val slot = slot<PaymentFailed>()
        verify {
            internalBankService.checkCaptureFunds(any(), bankAccount, operationid, any())
            internalBankService.undoCaptureFunds(any(), any(), any(), any(), any())
            boletoSettlementService.cancelPayment(ofType(String::class), ofType(Int::class), ofType(String::class))
            billEventPublisher.publish(any(), capture(slot))
        }

        slot.captured.errorDescription shouldBe PaymentError.BALANCE_TIMEOUT.description
    }

    private fun savePaymentAndSettlement(
        balanceAuthorization: BalanceAuthorization,
        boletoSettlementResult: BoletoSettlementResult,
    ) {
        boletoTransaction.paymentData.toSingle().payment = balanceAuthorization
        boletoTransaction.settlementData.settlementOperation = boletoSettlementResult
        transactionService.save(boletoTransaction)
    }

    private fun assertTransactionStatuses(
        transaction: Transaction,
        transactionStatus: TransactionStatus,
        bankOperationStatus: BankOperationStatus,
        boletoSettlementStatus: BoletoSettlementStatus,
    ) {
        transaction.status shouldBe transactionStatus
        transaction.paymentData.toSingle().get<BalanceAuthorization>().status shouldBe bankOperationStatus
        transaction.settlementData.getOperation<BoletoSettlementResult>().status shouldBe boletoSettlementStatus
    }
}