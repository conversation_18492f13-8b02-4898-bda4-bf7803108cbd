package ai.friday.billpayment.app.payment

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.CouldNotAcquireLockException
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.CreditCardChargebackTrackDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbTransactionRepository
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.adapters.lock.transactionLockProvider
import ai.friday.billpayment.adapters.lock.walletDailyPaymentLimitLockProvider
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillSchedulePostponed
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.WarningCode
import ai.friday.billpayment.app.bill.getWarningCode
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.PixPaymentService
import ai.friday.billpayment.app.payment.transaction.PIX_DELAY_TO_RETRY
import ai.friday.billpayment.app.payment.transaction.RetryTransaction
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyDetailsResult
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixTransactionError
import ai.friday.billpayment.balance
import ai.friday.billpayment.createSinglePaymentDataWithBalance
import ai.friday.billpayment.defaultTransactionEntityConverter
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.integration.loadBalancePaymentMethod
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.pixKeyAdded
import ai.friday.billpayment.pixKeyDetails
import ai.friday.billpayment.pixKeyPaymentScheduled
import ai.friday.billpayment.pixKeyPaymentStarted
import ai.friday.billpayment.pixPaymentScheduled
import ai.friday.billpayment.pixPaymentStarted
import ai.friday.billpayment.transactionId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.result.shouldBeFailure
import io.kotest.matchers.shouldBe
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import jakarta.inject.Named
import java.math.BigInteger
import java.util.stream.Stream
import net.javacrumbs.shedlock.core.SimpleLock
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.junit.jupiter.params.provider.ValueSource

@MicronautTest(environments = [FRIDAY_ENV])
class PixPaymentServiceTest(
    private val billPaymentService: BillPaymentService,
    private val retryTransaction: RetryTransaction,
    private val dynamoDB: AmazonDynamoDB,
) {

    private val enhancedClient = getDynamoDB()

    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(enhancedClient),
        refundedClient = RefundedBillDynamoDAO(enhancedClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(enhancedClient),
    )

    private val billEventDAO = BillEventDynamoDAO(enhancedClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)
    private val billEventRepository = BillEventDBRepository(
        billEventDAO = billEventDAO,
        uniqueConstraintDAO = uniqueConstraintDAO,
        featureConfiguration = allFalseFeatureConfiguration,
        transactionDynamo = transactionDynamo,
    )

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val transactionDAO = TransactionDynamoDAO(enhancedClient)
    private val creditCardChargebackTrackDAO = CreditCardChargebackTrackDynamoDAO(enhancedClient)

    private val transactionRepository = DynamoDbTransactionRepository(
        transactionDAO = transactionDAO,
        creditCardChargebackTrackDAO = creditCardChargebackTrackDAO,
        accountRepository = accountRepository,
        transactionEntityConverter = defaultTransactionEntityConverter(billEventRepository, mockk(), transactionDynamo),
    )

    private val bill = Bill.build(pixAdded, pixPaymentScheduled, pixPaymentStarted)

    @MockBean(MessagePublisher::class)
    fun messagePublisherMock() = messagePublisher

    private val messagePublisher: MessagePublisher = mockk(relaxed = true)

    lateinit var transaction: Transaction

    private val cleanTransactionId = TransactionId("*************")
    lateinit var cleanTransaction: Transaction

    private val pixPaymentServiceMock: PixPaymentService = mockk()

    @MockBean(PixPaymentService::class)
    fun getArbiPixPaymentService(): PixPaymentService = pixPaymentServiceMock

    private val pixKeyManagement: PixKeyManagement = mockk(relaxed = true)

    @MockBean(PixKeyManagement::class)
    fun getArbiPixKeyManagement(): PixKeyManagement = pixKeyManagement

    private val notificationAdapterMock: NotificationAdapter = mockk(relaxed = true)

    @MockBean(NotificationAdapter::class)
    fun getNotificationAdapter(): NotificationAdapter = notificationAdapterMock

    private val simpleLock: SimpleLock = mockk(relaxed = true)

    private val transactionLockProviderMock: InternalLock = mockk {
        every { acquireLock(any()) } returns simpleLock
    }

    @MockBean(value = InternalLock::class, named = transactionLockProvider)
    @Named(transactionLockProvider)
    fun getTransactionLockProvider(): InternalLock = transactionLockProviderMock

    @MockBean(value = InternalLock::class, named = walletDailyPaymentLimitLockProvider)
    @Named(walletDailyPaymentLimitLockProvider)
    fun getWalletDailyPaymentLimitLockProvider(): InternalLock = mockk(relaxed = true)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        loadAccountIntoDb(dynamoDB, accountId = ACCOUNT.accountId)
        loadBalancePaymentMethod(accountRepository, paymentMethodId = balance.id.value)
        billRepository.save(bill)
        billEventRepository.save(pixAdded)
        billEventRepository.save(pixPaymentStarted)
        billEventRepository.save(pixPaymentScheduled)

        transaction = Transaction(
            id = transactionId,
            type = TransactionType.INVOICE_PAYMENT,
            payer = ACCOUNT.toPayer(),
            paymentData = createSinglePaymentDataWithBalance(
                balance,
                bill.amountTotal,
                calculationId = null,
                BalanceAuthorization(
                    status = BankOperationStatus.ERROR,
                    timeout = false,
                    amount = 100,
                    paymentGateway = FinancialServiceGateway.ARBI,
                ),
            ),
            settlementData = SettlementData(
                settlementTarget = bill,
                serviceAmountTax = 0L,
                bill.amountTotal,
                settlementOperation = BankTransfer(
                    gateway = FinancialServiceGateway.ARBI,
                    status = BankOperationStatus.UNKNOWN,
                    amount = bill.amountTotal,
                ),
            ),
            nsu = 0L,
            actionSource = ActionSource.Scheduled,
            walletId = bill.walletId,
        )

        cleanTransaction = Transaction(
            id = cleanTransactionId,
            type = TransactionType.DIRECT_INVOICE,
            payer = ACCOUNT.toPayer(),
            paymentData = createSinglePaymentDataWithBalance(
                balance,
                bill.amountTotal,
                calculationId = null,
                null,
            ),
            settlementData = SettlementData(
                settlementTarget = bill,
                serviceAmountTax = 0L,
                bill.amountTotal,
                settlementOperation = BankTransfer(
                    gateway = FinancialServiceGateway.ARBI,
                    status = BankOperationStatus.UNKNOWN,
                    amount = bill.amountTotal,
                ),
            ),
            nsu = 0L,
            actionSource = ActionSource.Scheduled,
            walletId = bill.walletId,
        )
        transactionRepository.save(transaction)
        transactionRepository.save(cleanTransaction)
    }

    @Test
    fun `transaction status should be processing when payment is still being validated`() {
        every { pixPaymentServiceMock.checkPaymentStatus(any(), any<BankOperationId>()) } returns PixPaymentResult(
            status = PixPaymentStatus.ACKNOWLEDGED,
            idOrdemPagamento = "10",
            endToEnd = "ABC",
        )

        retryTransaction.retryTransaction(transaction.id).shouldBeFailure<TransactionRollbackException>()

        val transaction = transactionRepository.findById(transaction.id)
        transaction.status shouldBe TransactionStatus.PROCESSING
        val result = billEventRepository.getBillById(bill.billId)
        result.isRight() shouldBe true
        result.map { it.status shouldBe BillStatus.PROCESSING }
    }

    @Test
    fun `transaction status should be completed with paid bill when payment is validated`() {
        every { pixPaymentServiceMock.checkPaymentStatus(any(), any<BankOperationId>()) } returns PixPaymentResult(
            status = PixPaymentStatus.SUCCESS,
            idOrdemPagamento = "10",
            endToEnd = "ABC",
        )

        retryTransaction.retryTransaction(transaction.id)

        val transaction = transactionRepository.findById(transaction.id)
        transaction.status shouldBe TransactionStatus.COMPLETED
        val result = billEventRepository.getBillById(bill.billId)
        result.isRight() shouldBe true
        result.map { it.status shouldBe BillStatus.PAID }
    }

    @Test
    fun `should send to retry with delay when pix transaction is not on final state`() {
        every { pixPaymentServiceMock.transfer(command = any(), deviceId = null) } returns PixPaymentResult(
            status = PixPaymentStatus.ACKNOWLEDGED,
            idOrdemPagamento = "10",
            endToEnd = "ABC",
        )

        every { pixPaymentServiceMock.checkPaymentStatus(any(), any<BankOperationId>()) } returns PixPaymentResult(
            status = PixPaymentStatus.ACKNOWLEDGED,
            idOrdemPagamento = "10",
            endToEnd = "ABC",
        )

        billPaymentService.process(transaction)

        verify {
            messagePublisher.sendRetryTransactionMessage(transaction.id, PIX_DELAY_TO_RETRY)
        }
    }

    @ParameterizedTest
    @ValueSource(strings = ["REFUSED", "FAILED"])
    fun `should remove scheduling from not payable pix`(paymentStatus: PixPaymentStatus) {
        every { pixPaymentServiceMock.transfer(command = any(), deviceId = null) } returns PixPaymentResult(
            status = PixPaymentStatus.ACKNOWLEDGED,
            idOrdemPagamento = "10",
            endToEnd = "ABC",
        )

        every { pixPaymentServiceMock.checkPaymentStatus(any(), any<BankOperationId>()) } returns PixPaymentResult(
            status = paymentStatus,
            idOrdemPagamento = "10",
            endToEnd = "ABC",
            error = PixTransactionError.SettlementDestinationAccountTypeInvalid,
        )

        billPaymentService.process(cleanTransaction)

        verify(exactly = 0) {
            messagePublisher.sendRetryTransactionMessage(cleanTransaction.id, any())
        }

        val result = billEventRepository.getBillById(bill.billId)
        result.isRight() shouldBe true
        result.map {
            it.status shouldBe BillStatus.NOT_PAYABLE
            it.isPaymentScheduled() shouldBe false
        }
    }

    @Test
    fun `deve atualizar os dados do recipient na bill caso mudem após a adição`() {
        every { pixKeyManagement.findKeyDetails(any(), any()) } returns PixKeyDetailsResult(pixKeyDetails = PixKeyDetails(key = pixKeyDetails.key, holder = pixKeyDetails.holder, owner = pixKeyDetails.owner), e2e = "").right()
        billEventRepository.save(pixKeyAdded)
        billEventRepository.save(pixKeyPaymentScheduled)
        billEventRepository.save(pixKeyPaymentStarted)

        val bill = Bill.build(pixKeyAdded, pixKeyPaymentScheduled, pixKeyPaymentStarted)
        val transaction = Transaction(
            type = TransactionType.DIRECT_INVOICE,
            payer = ACCOUNT.toPayer(),
            paymentData = createSinglePaymentDataWithBalance(balance, bill.amountTotal),
            settlementData = SettlementData(
                settlementTarget = bill,
                serviceAmountTax = 0,
                totalAmount = bill.amountTotal,
            ),
            nsu = 0,
            actionSource = ActionSource.System,
            walletId = bill.walletId,
        )

        val expectedPixKeyDetails = pixKeyDetails.copy(
            holder = PixKeyHolder(
                accountNo = BigInteger("1231234"),
                accountDv = "0",
                ispb = "123",
                institutionName = "Teste",
                accountType = AccountType.CHECKING,
                routingNo = 12,
            ),
        )

        every { pixPaymentServiceMock.transfer(command = any(), deviceId = null) } returns PixPaymentResult(
            pixKeyDetails = expectedPixKeyDetails,
            status = PixPaymentStatus.ACKNOWLEDGED,
            idOrdemPagamento = "10",
            endToEnd = "ABC",
        )

        every { pixPaymentServiceMock.checkPaymentStatus(any(), any()) } returns PixPaymentResult(
            status = PixPaymentStatus.SUCCESS,
            idOrdemPagamento = "10",
            endToEnd = "ABC",
        )

        billPaymentService.process(transaction)

        transaction.status shouldBe TransactionStatus.COMPLETED

        val billResult = billEventRepository.getBill(walletId = bill.walletId, billId = bill.billId)

        billResult.isRight() shouldBe true
        billResult.map { it: Bill ->
            it.recipient!!.pixKeyDetails shouldBe expectedPixKeyDetails
        }
    }

    @Test
    fun `bill should be waiting funds when pix transaction returns BusinessInsufficientBalance`() {
        every { pixPaymentServiceMock.transfer(command = any(), deviceId = null) } returns PixPaymentResult(
            status = PixPaymentStatus.ACKNOWLEDGED,
            idOrdemPagamento = "10",
            endToEnd = "ABC",
        )

        every { pixPaymentServiceMock.checkPaymentStatus(any(), any()) } returns PixPaymentResult(
            status = PixPaymentStatus.FAILED,
            idOrdemPagamento = "10",
            endToEnd = "ABC",
            error = PixTransactionError.BusinessInsufficientBalance,
        )

        billPaymentService.process(cleanTransaction)

        verify(exactly = 0) {
            messagePublisher.sendRetryTransactionMessage(cleanTransaction.id, any())
        }

        val result = billEventRepository.getBillById(bill.billId)
        result.isRight() shouldBe true
        result.map {
            it.status shouldBe BillStatus.ACTIVE
            it.schedule?.waitingFunds shouldBe true
        }
    }

    @Test
    fun `bill should be postponed when pix transaction returns BusinessDailyLimit`() {
        every { pixPaymentServiceMock.transfer(command = any(), deviceId = null) } returns PixPaymentResult(
            status = PixPaymentStatus.ACKNOWLEDGED,
            idOrdemPagamento = "10",
            endToEnd = "ABC",
        )

        every { pixPaymentServiceMock.checkPaymentStatus(any(), any()) } returns PixPaymentResult(
            status = PixPaymentStatus.FAILED,
            idOrdemPagamento = "10",
            endToEnd = "ABC",
            error = PixTransactionError.BusinessDailyLimit,
        )

        billPaymentService.process(cleanTransaction)

        verify(exactly = 0) {
            messagePublisher.sendRetryTransactionMessage(cleanTransaction.id, any())
        }

        val result = billEventRepository.getBillById(bill.billId)
        result.isRight() shouldBe true
        result.map {
            it.status shouldBe BillStatus.ACTIVE
            it.schedule?.date shouldBe getLocalDate().plusDays(1)
            it.history.filterIsInstance<BillSchedulePostponed>() shouldHaveSize 1
            it.history.last() shouldBe io.kotest.matchers.types.beOfType(BillSchedulePostponed::class)
        }
    }

    @ParameterizedTest
    @MethodSource("temporaryErrorsProvider")
    fun `transaction status should be failed with retryable bill when payment is not validated`(
        error: PixTransactionError,
        warningCode: WarningCode,
    ) {
        every { pixPaymentServiceMock.checkPaymentStatus(any(), any()) } returns PixPaymentResult(
            status = PixPaymentStatus.FAILED,
            idOrdemPagamento = "10",
            endToEnd = "ABC",
            error = error,
        )

        retryTransaction.retryTransaction(transaction.id)

        val transaction = transactionRepository.findById(transaction.id)
        transaction.status shouldBe TransactionStatus.FAILED
        val result = billEventRepository.getBillById(bill.billId)
        result.isRight() shouldBe true
        result.map {
            it.status shouldBe BillStatus.ACTIVE
            it.getWarningCode() shouldBe warningCode
        }
    }

    @ParameterizedTest
    @MethodSource("permanentErrorsProvider")
    fun `transaction status should be failed with non retryable bill when settlement destination is not validated`(
        error: PixTransactionError,
        warningCode: WarningCode,
    ) {
        every { pixPaymentServiceMock.checkPaymentStatus(any(), any()) } returns PixPaymentResult(
            status = PixPaymentStatus.FAILED,
            idOrdemPagamento = "10",
            endToEnd = "ABC",
            error = error,
        )

        retryTransaction.retryTransaction(transaction.id)

        val transaction = transactionRepository.findById(transaction.id)
        transaction.status shouldBe TransactionStatus.FAILED
        val result = billEventRepository.getBillById(bill.billId)
        result.isRight() shouldBe true
        result.map {
            it.status shouldBe BillStatus.NOT_PAYABLE
            it.getWarningCode() shouldBe warningCode
        }
    }

    @Test
    fun `deve adquirir e liberar o lock corretamente durante o processamento`() {
        every { pixPaymentServiceMock.transfer(command = any(), deviceId = null) } returns PixPaymentResult(
            status = PixPaymentStatus.ACKNOWLEDGED,
            idOrdemPagamento = "10",
            endToEnd = "ABC",
        )

        every { pixPaymentServiceMock.checkPaymentStatus(any(), any<BankOperationId>()) } returns PixPaymentResult(
            status = PixPaymentStatus.SUCCESS,
            idOrdemPagamento = "10",
            endToEnd = "ABC",
        )

        billPaymentService.process(transaction)

        verify(exactly = 1) { transactionLockProviderMock.acquireLock(transaction.id.value) }
        verify(exactly = 1) { simpleLock.unlock() }
    }

    @Test
    fun `não deve executar o processamento quando não consegue adquirir o lock`() {
        every { transactionLockProviderMock.acquireLock(transaction.id.value) } returns null

        assertThrows<CouldNotAcquireLockException> { billPaymentService.process(transaction) }

        verify(exactly = 1) { transactionLockProviderMock.acquireLock(transaction.id.value) }
        verify(exactly = 0) { pixPaymentServiceMock.transfer(any(), any()) }
        verify(exactly = 0) { simpleLock.unlock() }
    }

    companion object {
        @JvmStatic
        fun permanentErrorsProvider(): Stream<Arguments> {
            return Stream.of(

                Arguments.arguments(
                    PixTransactionError.SettlementDestinationAccountNotFound,
                    WarningCode.SETTLEMENT_DESTINATION_ACCOUNT_NOT_FOUND,
                ),
                Arguments.arguments(
                    PixTransactionError.SettlementDestinationAccountNotAvailable,
                    WarningCode.SETTLEMENT_DESTINATION_ACCOUNT_NOT_AVAILABLE,
                ),
                Arguments.arguments(
                    PixTransactionError.SettlementGenericPermanentError,
                    WarningCode.SETTLEMENT_GENERIC_PERMANENT_ERROR,
                ),
                Arguments.arguments(
                    PixTransactionError.SettlementDestinationInstitutionNotAllowed,
                    WarningCode.SETTLEMENT_DESTINATION_INSTITUTION_NOT_ALLOWED,
                ),
            )
        }

        @JvmStatic
        fun temporaryErrorsProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    PixTransactionError.BusinessSingleTransactionLimit,
                    WarningCode.BUSINESS_SINGLE_TRANSACTION_LIMIT,
                ),
                Arguments.arguments(
                    PixTransactionError.BusinessSameValuePaymentsExceeded,
                    WarningCode.BUSINESS_SAME_VALUE_PAYMENTS_EXCEEDED,
                ),
                Arguments.arguments(PixTransactionError.PaymentGenericTemporaryError, WarningCode.PIX_PAYMENT),
                Arguments.arguments(
                    PixTransactionError.BusinessDailyLimit,
                    WarningCode.POSTPONED_DUE_LIMIT_REACHED,
                ),
                Arguments.arguments(
                    PixTransactionError.SettlementGenericTemporaryError,
                    WarningCode.SETTLEMENT_GENERIC_TEMPORARY_ERROR,
                ),
                Arguments.arguments(
                    PixTransactionError.BusinessInsufficientBalance,
                    WarningCode.BUSINESS_INSUFFICIENT_BALANCE,
                ),
                Arguments.arguments(
                    PixTransactionError.SettlementPaymentRefusedByDestination,
                    WarningCode.SETTLEMENT_PAYMENT_REFUSED_BY_DESTINATION,
                ),
            )
        }
    }
}