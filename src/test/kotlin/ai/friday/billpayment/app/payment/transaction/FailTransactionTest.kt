package ai.friday.billpayment.app.payment.transaction

import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.PaymentFailed
import ai.friday.billpayment.app.conciliation.BoletoOccurrence
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.payment.ErrorSource
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.settlementVoid
import ai.friday.billpayment.createTransaction
import io.kotest.matchers.shouldBe
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Test

internal class FailTransactionTest {

    private val transactionService: TransactionService = mockk(relaxUnitFun = true)

    private val billEventPublisher: BillEventPublisher = mockk(relaxUnitFun = true)

    private val paymentFailedScheduleResolver: PaymentFailedScheduleResolver = mockk(relaxUnitFun = true)

    private val failTransaction = FailTransaction(
        transactionService = transactionService,
        billEventPublisher = billEventPublisher,
        paymentFailedScheduleResolver = paymentFailedScheduleResolver,
    )

    private val transaction = createTransaction(status = TransactionStatus.FAILED)

    private val voidedTransaction = createTransaction(status = TransactionStatus.COMPLETED)
        .settlementVoid(BoletoOccurrence.DEFAULT_DESCRIPTION)

    @Test
    fun `deve executar os passos de falha para uma transacao`() {
        failTransaction.execute(transaction)

        val slot = slot<PaymentFailed>()

        verify {
            transactionService.notify(transaction)
            paymentFailedScheduleResolver.resolvePostPaymentFailedEvent(transaction)
            billEventPublisher.publish(transaction.settlementData.getTarget(), capture(slot))
        }

        with(slot.captured) {
            billId shouldBe transaction.settlementData.getTarget<Bill>().billId
            walletId shouldBe transaction.settlementData.getTarget<Bill>().walletId
            retryable shouldBe transaction.isRetryable()
            errorDescription shouldBe transaction.getErrorDescription()
            actionSource shouldBe transaction.actionSource
            errorSource shouldBe transaction.getErrorSource()
            transactionId shouldBe transaction.id
        }
    }

    @Test
    fun `deve marcar a origem do evento de falha de pagamento com CELCOIN se a transacao foi estornada a partir do liquidante`() {
        failTransaction.execute(voidedTransaction)

        val slot = slot<PaymentFailed>()

        verify {
            billEventPublisher.publish(voidedTransaction.settlementData.getTarget(), capture(slot))
        }

        with(slot.captured) {
            transactionId shouldBe voidedTransaction.id

            errorDescription shouldBe BoletoOccurrence.DEFAULT_DESCRIPTION
            errorSource shouldBe ErrorSource.SETTLEMENT_CELCOIN
            retryable shouldBe false

            billId shouldBe voidedTransaction.settlementData.getTarget<Bill>().billId
            walletId shouldBe voidedTransaction.settlementData.getTarget<Bill>().walletId
        }
    }
}