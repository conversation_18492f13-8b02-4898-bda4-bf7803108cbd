package ai.friday.billpayment.app.payment

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.Acquirer
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.CreditCardPaymentStatus
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.BillSchedulePostponed
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.bill.SchedulePostponedReason
import ai.friday.billpayment.app.cashIn.FraudPreventionErrors
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.payment.transaction.PaymentFailedScheduleResolver
import ai.friday.billpayment.app.pix.PixTransactionError
import ai.friday.billpayment.createSinglePaymentDataWithBalance
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.internalBankAccount
import ai.friday.billpayment.invoiceBill
import ai.friday.billpayment.invoicePaymentScheduled
import ai.friday.billpayment.invoiceScheduleCanceled
import ai.friday.billpayment.paymentMethodId2
import ai.friday.billpayment.pixBill
import ai.friday.billpayment.pixPaymentScheduled
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.called
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class PaymentFailedScheduleResolverTest {

    private val billEventPublisher = mockk<BillEventPublisher>(relaxed = true)

    private val paymentFailedScheduleResolver = PaymentFailedScheduleResolver(
        billEventPublisher = billEventPublisher,
    )

    private val balance = AccountPaymentMethod(
        id = paymentMethodId2,
        accountId = AccountId(ACCOUNT_ID),
        status = AccountPaymentMethodStatus.ACTIVE,
        method = internalBankAccount,
        created = getZonedDateTime(),
    )

    private lateinit var invoiceTransaction: Transaction

    private lateinit var pixTransaction: Transaction

    @BeforeEach
    fun init() {
        invoiceTransaction = Transaction(
            type = TransactionType.INVOICE_PAYMENT,
            payer = ACCOUNT.toPayer(),
            paymentData = createSinglePaymentDataWithBalance(balance, invoiceBill.amountTotal),
            settlementData = SettlementData(invoiceBill, 0, invoiceBill.amountTotal),
            nsu = 1,
            actionSource = ActionSource.Scheduled,
            walletId = invoiceBill.walletId,
        )

        pixTransaction = Transaction(
            type = TransactionType.DIRECT_INVOICE,
            payer = ACCOUNT.toPayer(),
            paymentData = createSinglePaymentDataWithBalance(balance, invoiceBill.amountTotal),
            settlementData = SettlementData(pixBill, 0, pixBill.amountTotal),
            nsu = 1,
            actionSource = ActionSource.Scheduled,
            walletId = pixBill.walletId,
        )
    }

    @Test
    fun `não deve publicar nenhum evento se a conta não estiver agendada`() {
        invoiceTransaction.settlementData.getTarget<Bill>().apply(invoicePaymentScheduled)
        invoiceTransaction.settlementData.getTarget<Bill>().apply(invoiceScheduleCanceled)
        paymentFailedScheduleResolver.resolvePostPaymentFailedEvent(
            transaction = invoiceTransaction,
        )
        verify {
            billEventPublisher wasNot called
        }
    }

    @Test
    fun `deve publicar agendamento cancelado com reason BILL_NOT_PAYABLE se a liquidação for não retentavel`() {
        pixTransaction.settlementData.getTarget<Bill>().apply(pixPaymentScheduled)
        pixTransaction.settlementData.settlementOperation = BankTransfer(
            gateway = FinancialServiceGateway.ARBI,
            status = BankOperationStatus.INVALID_DATA,
            amount = 1,
        )

        paymentFailedScheduleResolver.resolvePostPaymentFailedEvent(
            transaction = pixTransaction,
        )
        val slot = slot<BillEvent>()
        verify {
            billEventPublisher.publish(any(), capture(slot))
        }
        with(slot.captured) {
            this.shouldBeTypeOf<BillPaymentScheduleCanceled>()
                .reason shouldBe ScheduleCanceledReason.BILL_NOT_PAYABLE
        }
    }

    @Test
    fun `deve publicar agendamento adiado com reason LIMIT_REACHED for um erro de PIX por falta de limite`() {
        pixTransaction.settlementData.getTarget<Bill>().apply(pixPaymentScheduled)
        pixTransaction.settlementData.settlementOperation = BankTransfer(
            gateway = FinancialServiceGateway.ARBI,
            status = BankOperationStatus.ERROR,
            amount = 1,
            errorDescription = PixTransactionError.BusinessDailyLimit.code,
        )

        paymentFailedScheduleResolver.resolvePostPaymentFailedEvent(
            transaction = pixTransaction,
        )
        val slot = slot<BillEvent>()
        verify {
            billEventPublisher.publish(any(), capture(slot))
        }
        with(slot.captured) {
            this.shouldBeTypeOf<BillSchedulePostponed>()
                .reason shouldBe SchedulePostponedReason.LIMIT_REACHED
        }
    }

    @Test
    fun `deve publicar agendamento adiado com reason INSUFFICIENT_FUNDS for um erro de PIX por falta de saldo`() {
        pixTransaction.settlementData.getTarget<Bill>().apply(pixPaymentScheduled)
        pixTransaction.settlementData.settlementOperation = BankTransfer(
            gateway = FinancialServiceGateway.ARBI,
            status = BankOperationStatus.INSUFFICIENT_FUNDS,
            amount = 1,
            errorDescription = PixTransactionError.BusinessInsufficientBalance.code,
        )

        paymentFailedScheduleResolver.resolvePostPaymentFailedEvent(
            transaction = pixTransaction,
        )
        val slot = slot<BillEvent>()
        verify {
            billEventPublisher.publish(any(), capture(slot))
        }
        with(slot.captured) {
            this.shouldBeTypeOf<BillSchedulePostponed>()
                .reason shouldBe SchedulePostponedReason.INSUFFICIENT_FUNDS
        }
    }

    @Test
    fun `deve publicar agendamento cancelado com reason CANT_PAY_WITH_CURRENT_CREDIT_CARD for cartao negado no anti fraude`() {
        invoiceTransaction.settlementData.getTarget<Bill>().apply(invoicePaymentScheduled)
        invoiceTransaction.paymentData.toSingle().payment = FraudPreventionPaymentOperationDenied(
            error = FraudPreventionErrors.NO_LIMIT_AVAILABLE,
        )

        paymentFailedScheduleResolver.resolvePostPaymentFailedEvent(
            transaction = invoiceTransaction,
        )
        val slot = slot<BillEvent>()
        verify {
            billEventPublisher.publish(any(), capture(slot))
        }
        with(slot.captured) {
            this.shouldBeTypeOf<BillPaymentScheduleCanceled>()
                .reason shouldBe ScheduleCanceledReason.CANT_PAY_WITH_CURRENT_CREDIT_CARD
        }
    }

    @Test
    fun `deve publicar agendamento cancelado com reason CANT_PAY_WITH_CURRENT_CREDIT_CARD for cartao negado na adiquirente`() {
        invoiceTransaction.settlementData.getTarget<Bill>().apply(invoicePaymentScheduled)
        invoiceTransaction.paymentData.toSingle().payment = CreditCardAuthorization(
            acquirer = Acquirer.CIELO,
            transactionId = "",
            status = CreditCardPaymentStatus.DENIED,
            acquirerTid = null,
            amount = null,
            acquirerReturnCode = null,
            acquirerReturnMessage = "",
            tid = null,
            authorizationCode = null,
        )

        paymentFailedScheduleResolver.resolvePostPaymentFailedEvent(
            transaction = invoiceTransaction,
        )
        val slot = slot<BillEvent>()
        verify {
            billEventPublisher.publish(any(), capture(slot))
        }
        with(slot.captured) {
            this.shouldBeTypeOf<BillPaymentScheduleCanceled>()
                .reason shouldBe ScheduleCanceledReason.CANT_PAY_WITH_CURRENT_CREDIT_CARD
        }
    }
}