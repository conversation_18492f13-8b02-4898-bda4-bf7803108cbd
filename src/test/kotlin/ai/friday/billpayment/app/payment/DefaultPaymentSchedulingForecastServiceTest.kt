package ai.friday.billpayment.app.payment

import DynamoDBUtils
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.FORECAST_PREFIX
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.ScheduleForecastDbRepository
import ai.friday.billpayment.adapters.dynamodb.ScheduleForecastDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.ScheduleForecastEntity
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPaymentScheduledExternalPayment
import ai.friday.billpayment.app.bill.iso
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ScheduleForecastResult
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billScheduleCanceled
import ai.friday.billpayment.fichaCompensacaoCreditCardAdded
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillEventTable
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import java.time.LocalDate
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test

class DefaultPaymentSchedulingForecastServiceTest {

    @Test
    fun `should provision bill amount for settlement`() {
        val scheduleDate = LocalDate.now().plusDays(10)

        forecastService.provision(paymentScheduledEvent.copy(scheduledDate = scheduleDate))
        scheduleForecastRepository.find(scheduleDate)
            .shouldBeTypeOf<ScheduleForecastResult.Item>().run {
                scheduledBills shouldBe 1
                paidAmount shouldBe 0
                paidBills shouldBe 0
                provisionedAmount shouldBe paymentScheduledEvent.amount
            }
    }

    @Test
    fun `should deprovision bill amount for internal settlement`() {
        val baseAmount = 500000L

        scheduleForecastRepository.save(
            scheduleForecast = ScheduleForecastEntity().apply {
                primaryKey = FORECAST_PREFIX
                scanKey = paymentScheduledEvent.scheduledDate.iso()
                provisionedAmount = baseAmount
                paidAmount = 200
                scheduledBills = 5
                paidBills = 1
            },
        )

        scheduleForecastRepository.find(paymentScheduledEvent.scheduledDate)
            .shouldBeTypeOf<ScheduleForecastResult.Item>().run {
                provisionedAmount shouldBe baseAmount
                paidAmount shouldBe 200
            }

        forecastService.deprovision(event = paymentScheduledCancelEvent)

        verify {
            scheduleForecastRepository.processPayment(
                paymentScheduledEvent.scheduledDate,
                paymentScheduledEvent.amount,
            )
        }

        scheduleForecastRepository.find(paymentScheduledEvent.scheduledDate)
            .shouldBeTypeOf<ScheduleForecastResult.Item>().run {
                provisionedAmount shouldBe baseAmount - BILL_AMOUNT
                paidAmount shouldBe 200 + BILL_AMOUNT
                paidBills shouldBe 2
                scheduledBills shouldBe 4
            }
    }

    @Test
    fun `should not provision bill amount for bills with external settlement`() {
        forecastService.provision(paymentScheduledEventWithExternalSettlement)

        scheduleForecastRepository
            .find(paymentScheduledEventWithExternalSettlement.scheduledDate)
            .shouldBeTypeOf<ScheduleForecastResult.NotFound>()
    }

    private companion object {
        const val BILL_AMOUNT: Long = 4000L
        val walletFixture = WalletFixture()

        val lockProvider = mockk<InternalLock>(relaxed = true)
        val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

        val enhancedClient = DynamoDBUtils.setupDynamoDB()

        val billDynamoDAO = BillDynamoDAO(enhancedClient)
        val refundedBillDynamoDAO = RefundedBillDynamoDAO(enhancedClient)
        val settlementFundsTransferDynamoDAO = SettlementFundsTransferDynamoDAO(enhancedClient)
        private val billEventDAO = BillEventDynamoDAO(enhancedClient)
        private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(enhancedClient)
        private val transactionDynamo = TransactionDynamo(enhancedClient)

        val billRepository = DynamoDbBillRepository(billDynamoDAO, refundedBillDynamoDAO, settlementFundsTransferDynamoDAO)
        val billEventRepository = BillEventDBRepository(
            billEventDAO,
            uniqueConstraintDAO,
            mockk(relaxed = true),
            transactionDynamo,
        )

        val billId = BillId()
        val barcode = BarCode.of(FICHA_DE_COMPENSACAO_DIGITABLE_LINE)

        val originWallet = walletFixture.buildWallet(
            "carteira origem",
            otherMembers = listOf(
                walletFixture.assistant,
                walletFixture.limitedParticipant,
                walletFixture.ultraLimitedParticipant,
                walletFixture.participant,
            ),
        )

        val paymentScheduledEvent = billPaymentScheduled.copy(
            walletId = originWallet.id,
            billId = billId,
            amount = BILL_AMOUNT,
            scheduledDate = LocalDate.now().plusDays(16),
        )

        val externalPaymentInfo = BillPaymentScheduledExternalPayment(AccountProviderName.ME_POUPE.name)

        val paymentScheduledEventWithExternalSettlement = paymentScheduledEvent.copy(
            amount = BILL_AMOUNT,
            infoData = externalPaymentInfo,
            scheduledDate = LocalDate.now().plusDays(15),
        )

        val paymentScheduledCancelEvent = billScheduleCanceled.copy(walletId = originWallet.id, billId = billId)

        val scheduleForecastDAO = ScheduleForecastDynamoDAO(enhancedClient)
        val scheduleForecastRepository = spyk(ScheduleForecastDbRepository(db = DynamoDbDAO(amazonDynamoDB = dynamoDB), scheduleForecastDAO = scheduleForecastDAO))

        val accountDAO = AccountDynamoDAO(enhancedClient)
        val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
        val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
        val nsuDAO = NSUDynamoDAO(enhancedClient)

        val accountRepository = AccountDbRepository(
            accountDAO = accountDAO,
            partialAccountDAO = partialAccountDAO,
            paymentMethodDAO = paymentMethodDAO,
            nsuDAO = nsuDAO,
            transactionDynamo = transactionDynamo,
        )

        val walletDAO = WalletDynamoDAO(enhancedClient)
        val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
        val inviteDAO = InviteDynamoDAO(enhancedClient)
        val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
        val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

        val walletRepository = WalletDbRepository(
            walletDAO = walletDAO,
            walletMemberDAO = walletMemberDAO,
            inviteDAO = inviteDAO,
            inviteReminderDAO = inviteReminderDAO,
            walletLimitDAO = walletLimitDAO,
            accountRepository = accountRepository,
        )

        val forecastService = DefaultPaymentSchedulingForecastService(
            billEventRepository = billEventRepository,
            schedulingForecastRepository = scheduleForecastRepository,
        )

        @BeforeAll
        @JvmStatic
        fun beforeAll() {
            createBillEventTable(dynamoDB)
            every { lockProvider.acquireLock(any()) } returns mockk(relaxed = true)

            accountRepository.save(walletFixture.founderAccount)
            walletRepository.save(originWallet)

            listOf(
                fichaCompensacaoCreditCardAdded.copy(
                    walletId = originWallet.id,
                    billId = billId,
                    idNumber = "321",
                    barcode = barcode,
                    amount = BILL_AMOUNT,
                    amountTotal = BILL_AMOUNT,
                ),
                paymentScheduledEvent.copy(amount = BILL_AMOUNT, billId = billId),
                billPaid.copy(billId = billId),
                paymentScheduledCancelEvent.copy(billId = billId),
            ).also { billRepository.save(Bill.build(it.first(), it.last())) }
                .forEach { event -> billEventRepository.save(event) }
        }
    }
}