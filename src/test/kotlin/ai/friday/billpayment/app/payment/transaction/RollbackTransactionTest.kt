package ai.friday.billpayment.app.payment.transaction

import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.PaymentFailed
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.payment.Checkout
import ai.friday.billpayment.app.payment.CheckoutLocator
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionRollbackException
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.boletoTransaction
import io.kotest.matchers.shouldBe
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

internal class RollbackTransactionTest {

    private val transactionService: TransactionService = mockk(relaxUnitFun = true)
    private val billEventPublisher: BillEventPublisher = mockk(relaxUnitFun = true)
    private val checkoutLocator: CheckoutLocator = mockk(relaxUnitFun = true)
    private val paymentFailedScheduleResolver: PaymentFailedScheduleResolver = mockk(relaxUnitFun = true)

    private val rollbackTransaction = RollbackTransaction(
        transactionService = transactionService,
        billEventPublisher = billEventPublisher,
        checkoutLocator = checkoutLocator,
        paymentFailedScheduleResolver = paymentFailedScheduleResolver,
    )

    private val checkout = mockk<Checkout>(relaxUnitFun = true)

    private val transaction = boletoTransaction.copy(
        status = TransactionStatus.PROCESSING,
    )

    @ParameterizedTest
    @EnumSource(value = TransactionStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["PROCESSING", "COMPLETED"])
    fun `deve realizar o rollback no checkout e publicar o evento de PaymentFailed`(returnedStatus: TransactionStatus) {
        every {
            checkoutLocator.getCheckout(any())
        } returns checkout

        every {
            checkout.rollbackTransaction(any())
        } answers {
            arg<Transaction>(0).apply { status = returnedStatus }
        }

        rollbackTransaction.rollback(transaction)

        val slot = slot<PaymentFailed>()

        verify {
            transactionService.save(transaction)
            paymentFailedScheduleResolver.resolvePostPaymentFailedEvent(transaction)
            transactionService.notify(transaction)
            billEventPublisher.publish(
                transaction.settlementData.getTarget(),
                capture(slot),
            )
        }

        with(slot.captured) {
            billId shouldBe transaction.settlementData.getTarget<Bill>().billId
            walletId shouldBe transaction.settlementData.getTarget<Bill>().walletId
            retryable shouldBe transaction.isRetryable()
            errorDescription shouldBe transaction.getErrorDescription()
            transactionId shouldBe transaction.id
        }
    }

    @ParameterizedTest
    @EnumSource(value = TransactionStatus::class, mode = EnumSource.Mode.INCLUDE, names = ["PROCESSING", "COMPLETED"])
    fun `deve lancar excecao TransactionRollbackException quando o checkout nao conseguir fazer o rollback`(
        returnedStatus: TransactionStatus,
    ) {
        every {
            checkoutLocator.getCheckout(any())
        } returns checkout

        every {
            checkout.rollbackTransaction(any())
        } answers {
            arg<Transaction>(0).apply { status = returnedStatus }
        }

        assertThrows<TransactionRollbackException> {
            rollbackTransaction.rollback(transaction)
        }

        verify {
            transactionService.save(transaction)
            paymentFailedScheduleResolver wasNot called
            billEventPublisher wasNot called
        }

        verify(exactly = 0) {
            transactionService.notify(any())
        }
    }
}