package ai.friday.billpayment.app.payment

import DynamoDBUtils
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.CouldNotAcquireLockException
import ai.friday.billpayment.adapters.celcoin.CelcoinBillValidationResponse
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.CreditCardChargebackTrackDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbTransactionRepository
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillSynchronizationStatus
import ai.friday.billpayment.app.bill.PaymentFailed
import ai.friday.billpayment.app.bill.SyncErrorMessages
import ai.friday.billpayment.app.bill.SynchronizeBillResponse
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.cashIn.CreditCardFraudPreventionService
import ai.friday.billpayment.app.cashIn.FraudPreventionErrors
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.payment.captureFunds.CaptureFundsLocator
import ai.friday.billpayment.app.payment.captureFunds.FridayCaptureFundsLocator
import ai.friday.billpayment.app.payment.checkout.SyncBoletoCheckout
import ai.friday.billpayment.app.payment.transaction.BOLETO_DELAY_TO_RETRY
import ai.friday.billpayment.app.payment.transaction.CompleteTransaction
import ai.friday.billpayment.app.payment.transaction.FailTransaction
import ai.friday.billpayment.app.payment.transaction.PrepareTransaction
import ai.friday.billpayment.app.payment.transaction.StartTransaction
import ai.friday.billpayment.app.payment.transaction.TransactionInProcess
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.createSinglePaymentDataWithBalance
import ai.friday.billpayment.defaultTransactionEntityConverter
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID_4
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadBalancePaymentMethod
import ai.friday.billpayment.integration.loadCreditCard
import ai.friday.billpayment.internalBankAccount
import ai.friday.billpayment.paymentMethodId2
import ai.friday.billpayment.successConcessionariaValidationResponse
import ai.friday.billpayment.withEarlyAccess
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.left
import io.kotest.matchers.shouldBe
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.verify
import java.util.stream.Stream
import net.javacrumbs.shedlock.core.SimpleLock
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

internal class BoletoPaymentServiceTest {

    private val notificationMock: NotificationAdapter = mockk(relaxed = true)

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)

    private val enhancedClient = DynamoDBUtils.setupDynamoDB()
    private val billEventDAO = BillEventDynamoDAO(enhancedClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)
    private val billEventRepository = BillEventDBRepository(
        billEventDAO = billEventDAO,
        uniqueConstraintDAO = uniqueConstraintDAO,
        featureConfiguration = allFalseFeatureConfiguration,
        transactionDynamo = transactionDynamo,
    )

    private val boletoSettlementService: BoletoSettlementService = mockk()

    private val updateBillService: UpdateBillService = mockk(relaxed = true) {
        every { getBill(any()) } answers { Bill.build(billAdded) }
    }

    private val internalBankService: InternalBankService = mockk()

    private val mockMessagePublisher: MessagePublisher = mockk()
    private val creditCardFraudPreventionService = mockk<CreditCardFraudPreventionService>()
    private val captureFundsLocator: CaptureFundsLocator = FridayCaptureFundsLocator(
        fundProvider = internalBankService,
        acquirerService = mockk(),
        creditCardFraudPreventionService = creditCardFraudPreventionService,
    )

    private val checkoutLocator = DefaultCheckoutLocator(
        balanceInvoiceCheckout = mockk(),
        balancePixCheckout = mockk(),
        balanceDirectInvoiceCheckout = mockk(),
        boletoCheckout = SyncBoletoCheckout(
            captureFundsLocator = captureFundsLocator,
            boletoSettlementService = boletoSettlementService,
        ),
        asyncSettlementBoletoCheckout = mockk(),
        featureConfiguration = mockk(relaxed = true),
        investmentCheckout = mockk(),
    )

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDAO = TransactionDynamoDAO(enhancedClient)
    private val creditCardChargebackTrackDAO = CreditCardChargebackTrackDynamoDAO(enhancedClient)

    val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )
    val walletFixture = WalletFixture()
    val wallet = walletFixture.buildWallet()
    val walletService: WalletService = mockk() {
        every { findWallet(any()) } returns wallet
    }

    private val billAdded = ai.friday.billpayment.billAdded.copy(walletId = wallet.id)
    private val billPaymentFailedRetryable = ai.friday.billpayment.billPaymentFailedRetryable.copy(walletId = wallet.id)

    val transactionRepository = DynamoDbTransactionRepository(
        transactionDAO = transactionDAO,
        creditCardChargebackTrackDAO = creditCardChargebackTrackDAO,
        accountRepository = accountRepository,
        transactionEntityConverter = defaultTransactionEntityConverter(billEventRepository, mockk(), transactionDynamo),
    )
    private val transactionService = TransactionService(
        accountRepository = accountRepository,
        updateBillService = updateBillService,
        notificationAdapter = notificationMock,
        walletService = walletService,
        transactionRepository = transactionRepository,
    )

    private val settlementRetry: SettlementRetry = mockk()

    val billEventPublisher = mockk<BillEventPublisher>(relaxed = true)

    private val completeTransaction = CompleteTransaction(
        billInstrumentationService = mockk(relaxUnitFun = true),
        balanceService = mockk(relaxed = true),
        billEventPublisher = billEventPublisher,
        notifyReceiptService = mockk(relaxed = true),
        onboardingInstrumentationService = mockk(relaxed = true),
    )

    private val failTransaction = FailTransaction(
        transactionService = transactionService,
        paymentFailedScheduleResolver = mockk(relaxed = true),
        billEventPublisher = billEventPublisher,
    )

    private val transactionInProcess = TransactionInProcess(
        messagePublisher = mockMessagePublisher,
        checkoutLocator = checkoutLocator,
    )

    private val prepareTransaction = PrepareTransaction(
        updateBillService = updateBillService,
        boletoSettlementService = boletoSettlementService,
    )

    private val startTransaction = StartTransaction(
        transactionService = transactionService,
        billEventPublisher = billEventPublisher,
    )

    private val simpleLock: SimpleLock = mockk(relaxed = true)

    private val lockProvider: InternalLock = mockk {
        every { acquireLock(any()) } returns simpleLock
    }

    private val service = BillPaymentService(
        transactionService = transactionService,
        checkoutLocator = checkoutLocator,
        startTransaction = startTransaction,
        prepareTransaction = prepareTransaction,
        completeTransaction = completeTransaction,
        failTransaction = failTransaction,
        transactionInProcess = transactionInProcess,
        lockProvider = lockProvider,
        rollbackTransaction = mockk(),
    )

    private val bankAccount = internalBankAccount

    private val concessionariaBill = Bill.build(billAdded)

    private val successBankTransfer = BankTransfer(
        status = BankOperationStatus.SUCCESS,
        amount = billAdded.amountTotal,
        gateway = FinancialServiceGateway.ARBI,
    )

    private val errorDescription = "Generic error"

    private val transactionId = 12L

    val paymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_4)
    val creditCard = ai.friday.billpayment.creditCard.copy(id = paymentMethodId)

    @BeforeEach
    fun setUp() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        billEventRepository.save(billAdded)
        billEventRepository.save(billPaymentStart)
        with(bankAccount) { loadBalancePaymentMethod(accountRepository, bankNo, routingNo, accountNo.toBigInteger(), accountDv) }
        loadCreditCard(amazonDynamoDB = dynamoDB)
        every {
            updateBillService.synchronizeBill(
                ofType(Bill::class),
                any(),
            )
        } answers { SynchronizeBillResponse(status = BillSynchronizationStatus.BillNotModified) }
        every { boletoSettlementService.settlementValidation(ofType(Bill::class)) } answers { successConcessionariaValidationResponse }
        every {
            boletoSettlementService.initPayment(
                ofType(Bill::class),
                ofType(Int::class),
                ofType(String::class),
                ofType(String::class),
                any(),
            )
        } answers { BillPaymentResponse(status = BoletoSettlementStatus.AUTHORIZED, transactionId = transactionId) }
        every {
            boletoSettlementService.confirmPayment(
                ofType(String::class),
                ofType(Int::class),
                ofType(String::class),
            )
        } just runs
        every {
            boletoSettlementService.cancelPayment(
                ofType(String::class),
                ofType(Int::class),
                ofType(String::class),
            )
        } just runs
        every {
            boletoSettlementService.queryPayment(any())
        } returns BoletoSettlementStatus.AUTHORIZED
        every {
            settlementRetry.resolve(any())
        } returns TransactionRollbackException().left()
    }

    private val balance = AccountPaymentMethod(
        id = paymentMethodId2,
        accountId = AccountId(ACCOUNT_ID),
        status = AccountPaymentMethodStatus.ACTIVE,
        method = bankAccount,
        created = getZonedDateTime(),
    )
    private val boletoTransaction = Transaction(
        type = TransactionType.BOLETO_PAYMENT,
        payer = Payer(AccountId(ACCOUNT_ID), DOCUMENT, ACCOUNT.name),
        paymentData = createSinglePaymentDataWithBalance(
            balance,
            concessionariaBill.amountTotal,
        ),
        settlementData = SettlementData(concessionariaBill, 0, concessionariaBill.amountTotal),
        nsu = 1,
        actionSource = ActionSource.Scheduled,
        walletId = concessionariaBill.walletId,
    )

    @Test
    fun `should fail transaction on celcoin validate error`() {
        every {
            updateBillService.synchronizeBill(
                ofType(Bill::class),
                any(),
            )
        } answers {
            SynchronizeBillResponse(
                status = BillSynchronizationStatus.BillStatusUpdated(BillStatus.NOT_PAYABLE),
                syncErrorMessages = SyncErrorMessages.GenericSyncErrorMessage("CODIGO DE BARRAS NAO LOCALIZADO NA BASE CENTRALIZADA"),
            )
        }
        every { boletoSettlementService.validateBill(ofType(Bill::class)) } returns CelcoinBillValidationResponse(
            "642",
            "CODIGO DE BARRAS NAO LOCALIZADO NA BASE CENTRALIZADA",
        )
        service.process(transaction = boletoTransaction)
        val transactionDB = transactionService.findTransactionById(boletoTransaction.id)
        boletoTransaction.status shouldBe TransactionStatus.FAILED
        boletoTransaction.settlementData.getOperation<BoletoSettlementResult>().status shouldBe BoletoSettlementStatus.UNAUTHORIZED
        // transactionDB shouldBe boletoTransaction
        val slot = slot<PaymentFailed>()
        verify {
            billEventPublisher.publish(boletoTransaction.settlementData.getTarget(), capture(slot))
        }
        slot.captured.errorDescription shouldBe "CODIGO DE BARRAS NAO LOCALIZADO NA BASE CENTRALIZADA"
        slot.captured.errorSource shouldBe ErrorSource.SETTLEMENT_CELCOIN
    }

    @Test
    fun `should fail transaction on celcoin validate exception`() {
        every { boletoSettlementService.settlementValidation(ofType(Bill::class)) } throws BoletoSettlementException(
            FinancialServiceGateway.CELCOIN,
            errorDescription,
        )
        service.process(transaction = boletoTransaction)
        val transactionDB = transactionService.findTransactionById(boletoTransaction.id)
        boletoTransaction.status shouldBe TransactionStatus.FAILED
        boletoTransaction.settlementData.getOperation<BoletoSettlementResult>().status shouldBe BoletoSettlementStatus.UNAUTHORIZED
        // transactionDB shouldBe boletoTransaction
        val slot = slot<PaymentFailed>()
        verify {
            billEventPublisher.publish(boletoTransaction.settlementData.getTarget(), capture(slot))
        }
        slot.captured.errorDescription shouldBe errorDescription
        slot.captured.errorSource shouldBe ErrorSource.SETTLEMENT_CELCOIN
    }

    @Test
    fun `should fail transaction on celcoin unable to validate`() {
        val celcoinBillValidationResponse = CelcoinBillValidationResponse("999", "Error message")
        every { boletoSettlementService.settlementValidation(ofType(Bill::class)) } returns celcoinBillValidationResponse
        service.process(transaction = boletoTransaction)
        val transactionDB = transactionService.findTransactionById(boletoTransaction.id)
        boletoTransaction.status shouldBe TransactionStatus.FAILED
        boletoTransaction.settlementData.getOperation<BoletoSettlementResult>().status shouldBe BoletoSettlementStatus.UNAUTHORIZED
        // transactionDB shouldBe boletoTransaction
        val slot = slot<PaymentFailed>()
        verify {
            billEventPublisher.publish(boletoTransaction.settlementData.getTarget(), capture(slot))
        }
        slot.captured.errorDescription shouldBe celcoinBillValidationResponse.errorDescription
    }

    @Test
    fun `should fail transaction on celcoin excede limite`() {
        val errorDescription = TransactionError.GENERIC_EXCEPTION.description
        every { boletoSettlementService.initPayment(any(), any(), any(), any(), any()) } answers {
            BillPaymentResponse(
                BoletoSettlementStatus.UNAUTHORIZED,
                0,
                errorDescription,
            )
        }
        service.process(transaction = boletoTransaction)
        val transactionDB = transactionService.findTransactionById(boletoTransaction.id)
        boletoTransaction.status shouldBe TransactionStatus.FAILED
        boletoTransaction.settlementData.getOperation<BoletoSettlementResult>().status shouldBe BoletoSettlementStatus.UNAUTHORIZED
        boletoTransaction.settlementData.getOperation<BoletoSettlementResult>().errorDescription shouldBe errorDescription
        // transactionDB shouldBe boletoTransaction
        val slot = slot<PaymentFailed>()
        verify {
            billEventPublisher.publish(boletoTransaction.settlementData.getTarget(), capture(slot))
        }
        slot.captured.errorDescription shouldBe errorDescription
    }

    @Test
    fun `should rollback and fail transaction on celcoin confirm failure`() {
        every {
            boletoSettlementService.confirmPayment(
                ofType(String::class),
                ofType(Int::class),
                ofType(String::class),
            )
        } throws BoletoSettlementException(FinancialServiceGateway.CELCOIN, errorDescription)
        every { internalBankService.captureFunds(any(), any(), bankAccount, any()) } answers { successBankTransfer }
        every {
            internalBankService.undoCaptureFunds(
                any(),
                any(),
                bankAccount,
                any(),
                billAdded.amountTotal,
            )
        } answers { successBankTransfer }

        service.process(transaction = boletoTransaction)

        val transactionDB = transactionService.findTransactionById(boletoTransaction.id)
        // transactionDB shouldBe boletoTransaction
        assertTransactionStatuses(
            boletoTransaction,
            TransactionStatus.FAILED,
            BankOperationStatus.REFUNDED,
            BoletoSettlementStatus.VOIDED,
        )

        val slot = slot<PaymentFailed>()
        verify {
            boletoSettlementService.cancelPayment(ofType(String::class), ofType(Int::class), ofType(String::class))
            internalBankService.undoCaptureFunds(any(), any(), bankAccount, any(), billAdded.amountTotal)
            billEventPublisher.publish(boletoTransaction.settlementData.getTarget(), capture(slot))
        }
        slot.captured.errorDescription shouldBe errorDescription
    }

    @Test
    fun `should rollback and fail transaction on insufficient funds and cancel failure`() {
        every {
            internalBankService.captureFunds(
                any(),
                any(),
                bankAccount,
                any(),
            )
        } answers {
            BankTransfer(
                status = BankOperationStatus.INSUFFICIENT_FUNDS,
                amount = billAdded.amountTotal,
                errorDescription = errorDescription,
                gateway = FinancialServiceGateway.ARBI,
            )
        }
        every {
            boletoSettlementService.cancelPayment(
                ofType(String::class),
                ofType(Int::class),
                ofType(String::class),
            )
        } throws BoletoSettlementException(
            gateway = FinancialServiceGateway.ARBI,
            message = "Timeout na celcoin",
        ) andThenAnswer { nothing }

        service.process(transaction = boletoTransaction)

        val transactionDB = transactionService.findTransactionById(boletoTransaction.id)
        assertTransactionStatuses(
            boletoTransaction,
            TransactionStatus.FAILED,
            BankOperationStatus.INSUFFICIENT_FUNDS,
            BoletoSettlementStatus.VOIDED,
        )
        // transactionDB shouldBe boletoTransaction
        val slot = slot<PaymentFailed>()
        verify {
            billEventPublisher.publish(boletoTransaction.settlementData.getTarget(), capture(slot))
        }
        verify(exactly = 0) {
            internalBankService.undoCaptureFunds(any(), any(), bankAccount, any(), billAdded.amountTotal)
        }
        slot.captured.errorDescription shouldBe errorDescription
    }

    @ParameterizedTest
    @MethodSource("valuesProvider")
    fun `should fail transaction on Arbi failure`(
        status: BankOperationStatus,
        errorDescription: String,
        expectedNotification: String,
    ) {
        every {
            internalBankService.captureFunds(
                any(),
                any(),
                bankAccount,
                any(),
            )
        } answers {
            BankTransfer(
                status = status,
                amount = concessionariaBill.amountTotal,
                errorDescription = errorDescription,
                gateway = FinancialServiceGateway.ARBI,
            )
        }
        service.process(transaction = boletoTransaction)
        val transactionDB = transactionService.findTransactionById(boletoTransaction.id)
        boletoTransaction.status shouldBe TransactionStatus.FAILED
        // transactionDB shouldBe boletoTransaction
        val slot = slot<PaymentFailed>()
        verify {
            billEventPublisher.publish(boletoTransaction.settlementData.getTarget(), capture(slot))
        }
        slot.captured.errorDescription shouldBe errorDescription
        slot.captured.errorSource shouldBe ErrorSource.PAYMENT_ARBI
    }

    @Test
    fun `should send to retry transaction with delay when celcoin cancel fails on rollback`() {
        every {
            internalBankService.captureFunds(
                any(),
                any(),
                bankAccount,
                any(),
            )
        } answers {
            BankTransfer(
                status = BankOperationStatus.INSUFFICIENT_FUNDS,
                amount = billAdded.amountTotal,
                errorDescription = errorDescription,
                gateway = FinancialServiceGateway.ARBI,
            )
        }
        every {
            boletoSettlementService.cancelPayment(
                ofType(String::class),
                ofType(Int::class),
                ofType(String::class),
            )
        } throws BoletoSettlementException(FinancialServiceGateway.CELCOIN, "Timeout na celcoin")
        every { mockMessagePublisher.sendRetryTransactionMessage(ofType(TransactionId::class), any()) } just runs

        service.process(transaction = boletoTransaction)

        val transactionDB = transactionService.findTransactionById(boletoTransaction.id)
        // transactionDB shouldBe boletoTransaction
        assertTransactionStatuses(
            boletoTransaction,
            TransactionStatus.PROCESSING,
            BankOperationStatus.INSUFFICIENT_FUNDS,
            BoletoSettlementStatus.AUTHORIZED,
        )

        verify(exactly = 0) {
            updateBillService.publishEvent(boletoTransaction.settlementData.getTarget(), any())
        }

        verify {
            mockMessagePublisher.sendRetryTransactionMessage(boletoTransaction.id, BOLETO_DELAY_TO_RETRY)
        }
    }

    private fun assertTransactionStatuses(
        transaction: Transaction,
        transactionStatus: TransactionStatus,
        bankOperationStatus: BankOperationStatus,
        boletoSettlementStatus: BoletoSettlementStatus,
    ) {
        transaction.status shouldBe transactionStatus
        transaction.paymentData.toSingle().get<BalanceAuthorization>().status shouldBe bankOperationStatus
        transaction.settlementData.getOperation<BoletoSettlementResult>().status shouldBe boletoSettlementStatus
    }

    @Test
    fun `should send to retry transaction when undo transfer fails on rollback`() {
        every { internalBankService.captureFunds(any(), any(), bankAccount, any()) } answers { successBankTransfer }
        every {
            boletoSettlementService.confirmPayment(
                ofType(String::class),
                ofType(Int::class),
                ofType(String::class),
            )
        } throws BoletoSettlementException(FinancialServiceGateway.CELCOIN, errorDescription)
        every {
            internalBankService.undoCaptureFunds(
                any(),
                any(),
                bankAccount,
                any(),
                billAdded.amountTotal,
            )
        } answers {
            BankTransfer(
                status = BankOperationStatus.ERROR,
                amount = billAdded.amountTotal,
                errorDescription = "",
                gateway = FinancialServiceGateway.ARBI,
            )
        }
        every { mockMessagePublisher.sendRetryTransactionMessage(ofType(TransactionId::class), any()) } just runs

        service.process(transaction = boletoTransaction)

        val transactionDB = transactionService.findTransactionById(boletoTransaction.id)
        // transactionDB shouldBe boletoTransaction
        assertTransactionStatuses(
            boletoTransaction,
            TransactionStatus.PROCESSING,
            BankOperationStatus.SUCCESS,
            BoletoSettlementStatus.VOIDED,
        )
        boletoTransaction.settlementData.getOperation<BoletoSettlementResult>().errorDescription shouldBe errorDescription

        verify(exactly = 0) {
            updateBillService.publishEvent(boletoTransaction.settlementData.getTarget(), any())
        }

        verify {
            mockMessagePublisher.sendRetryTransactionMessage(boletoTransaction.id, BOLETO_DELAY_TO_RETRY)
        }
    }

    @Test
    fun `should send to retry transaction when capture funds times out`() {
        every {
            internalBankService.captureFunds(
                any(),
                any(),
                bankAccount,
                any(),
            )
        } answers {
            BankTransfer(
                status = BankOperationStatus.TIMEOUT,
                gateway = FinancialServiceGateway.ARBI,
                amount = boletoTransaction.settlementData.getTarget<Bill>().amountTotal,
            )
        }
        every { mockMessagePublisher.sendRetryTransactionMessage(ofType(TransactionId::class), any()) } just runs

        service.process(transaction = boletoTransaction)

        val transactionDB = transactionService.findTransactionById(boletoTransaction.id)
        assertTransactionStatuses(
            transactionDB,
            TransactionStatus.PROCESSING,
            BankOperationStatus.TIMEOUT,
            BoletoSettlementStatus.AUTHORIZED,
        )

        verify {
            mockMessagePublisher.sendRetryTransactionMessage(boletoTransaction.id, BOLETO_DELAY_TO_RETRY)
        }

        verify(exactly = 0) {
            updateBillService.publishEvent(any(), any())
        }
    }

    @Test
    fun `should throw PaymentNotScheduledException when scheduled bill is not scheduled`() {
        billEventRepository.save(billPaymentFailedRetryable)
        with(bankAccount) {
            loadBalancePaymentMethod(
                accountRepository = accountRepository,
                bankNo = bankNo,
                bankRoutingNo = routingNo,
                bankAccountNo = accountNo.toBigInteger(),
                bankAccountDv = accountDv,
                accountId = wallet.founder.accountId.value,
                paymentMethodId = wallet.paymentMethodId.value,
            )
        }
        assertThrows<PaymentNotScheduledException> {
            service.execute(
                BillPaymentCommand(
                    walletId = billAdded.walletId,
                    billId = billAdded.billId,
                    paymentDetails = PaymentMethodsDetailWithBalance(
                        paymentMethodId = wallet.paymentMethodId,
                        amount = billAdded.amountTotal,
                        calculationId = null,
                    ),
                    actionSource = ActionSource.Scheduled,
                ),
            )
        }
    }

    @Test
    fun `should save action source from bill payment command on bill events`() {
        val expectedActionSource = ActionSource.Scheduled
        every {
            updateBillService.synchronizeBill(
                ofType(Bill::class),
                any(),
            )
        } answers {
            SynchronizeBillResponse(
                status = BillSynchronizationStatus.BillStatusUpdated(BillStatus.ALREADY_PAID),
                syncErrorMessages = SyncErrorMessages.GenericSyncErrorMessage("BOLETO DE PAGAMENTO JA BAIXADO"),
            )
        }
        every { boletoSettlementService.validateBill(ofType(Bill::class)) } returns CelcoinBillValidationResponse(
            "628",
            "BOLETO DE PAGAMENTO JA BAIXADO",
        )

        service.process(transaction = boletoTransaction.copy(actionSource = expectedActionSource))

        val slot = slot<PaymentFailed>()
        verify {
            billEventPublisher.publish(boletoTransaction.settlementData.getTarget(), capture(slot))
            notificationMock wasNot called
        }
        slot.captured.actionSource shouldBe expectedActionSource
    }

    @Test
    fun `deve falhar quando a transação não passar no antifraude`() {
        val error = FraudPreventionPaymentOperationDenied(
            error = FraudPreventionErrors.NO_LIMIT_AVAILABLE,
        )
        every {
            creditCardFraudPreventionService.check(any(), any(), any(), any())
        } returns FraudPreventionErrors.NO_LIMIT_AVAILABLE.left()

        withEarlyAccess(boletoTransaction.payer.accountId) {
            service.process(
                transaction = boletoTransaction.copy(
                    paymentData = SinglePaymentData(
                        accountPaymentMethod = creditCard,
                        details = PaymentMethodsDetailWithCreditCard(
                            paymentMethodId = creditCard.id,
                            netAmount = concessionariaBill.amountTotal,
                            feeAmount = 10,
                            installments = 1,
                            fee = 4.0,
                            calculationId = null,
                        ),
                    ),
                ),
            )
        }

        val transaction = transactionRepository.findById(boletoTransaction.id)
        transaction.status shouldBe TransactionStatus.FAILED
        transaction.paymentData.toSingle().payment shouldBe error
    }

    @Test
    fun `should not process transaction when lock cannot be acquired`() {
        every { lockProvider.acquireLock(any()) } returns null

        assertThrows<CouldNotAcquireLockException> {
            service.process(transaction = boletoTransaction)
        }

        verify(exactly = 1) { lockProvider.acquireLock(any()) }
        verify(exactly = 0) {
            simpleLock.unlock()
        }
    }

    @Test
    fun `should acquire and release lock during successful transaction processing`() {
        every { internalBankService.captureFunds(any(), any(), bankAccount, any()) } answers { successBankTransfer }

        service.process(transaction = boletoTransaction)

        verify(exactly = 1) { lockProvider.acquireLock(any()) }
        verify(exactly = 1) { simpleLock.unlock() }
    }

    @Test
    fun `should acquire and release lock during failed transaction processing`() {
        every { boletoSettlementService.settlementValidation(ofType(Bill::class)) } throws BoletoSettlementException(
            FinancialServiceGateway.CELCOIN,
            errorDescription,
        )

        service.process(transaction = boletoTransaction)

        verify(exactly = 1) { lockProvider.acquireLock(any()) }
        verify(exactly = 1) { simpleLock.unlock() }
    }

    companion object {
        @JvmStatic
        fun valuesProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    BankOperationStatus.INSUFFICIENT_FUNDS,
                    "Insufficient funds",
                    PaymentError.INSUFFICIENT_FUNDS.description,
                ),
                Arguments.of(
                    BankOperationStatus.ERROR,
                    "Hora/Minuto do agendamento fora do Limte para TRANSFERENCIA/TED.",
                    PaymentError.GENERIC_ERROR.description,
                ),
                Arguments.of(BankOperationStatus.ERROR, "Arbi auth exception", PaymentError.GENERIC_ERROR.description),
            )
        }
    }
}