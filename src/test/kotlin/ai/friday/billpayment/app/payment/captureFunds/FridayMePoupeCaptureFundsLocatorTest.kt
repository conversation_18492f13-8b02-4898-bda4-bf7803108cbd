package ai.friday.billpayment.app.payment.captureFunds

import ai.friday.billpayment.app.Acquirer
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardPaymentStatus
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.cashIn.CreditCardFraudPreventionService
import ai.friday.billpayment.app.cashIn.FraudPreventionErrors
import ai.friday.billpayment.app.integrations.AcquirerService
import ai.friday.billpayment.app.integrations.FundProvider
import ai.friday.billpayment.app.metrics.push
import ai.friday.billpayment.app.payment.BalanceAuthorization
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.CreditCardAuthorization
import ai.friday.billpayment.app.payment.MultiplePaymentData
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithExternalPayment
import ai.friday.billpayment.app.payment.PaymentNotCanceled
import ai.friday.billpayment.app.payment.PaymentStatus
import ai.friday.billpayment.app.payment.SinglePaymentData
import ai.friday.billpayment.balance
import ai.friday.billpayment.createSinglePaymentDataWithBalance
import ai.friday.billpayment.createSinglePaymentDataWithCreditCard
import ai.friday.billpayment.createTransaction
import ai.friday.billpayment.externalCreditCard
import ai.friday.billpayment.withMockedMetric
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.EnumSource

class FridayMePoupeCaptureFundsLocatorTest {

    private val fundProvider: FundProvider = mockk()
    private val acquirerService: AcquirerService = mockk()
    private val creditCardFraudPreventionService: CreditCardFraudPreventionService = mockk()

    private val locator = FridayCaptureFundsLocator(
        fundProvider = fundProvider,
        acquirerService = acquirerService,
        creditCardFraudPreventionService = creditCardFraudPreventionService,
    )

    @Nested
    @DisplayName("ao tentar vericar uma fraude")
    inner class CheckOnFraudPrevention {
        @Test
        fun `deve lancar excecao para uma transacao com pagamento multiplo`() {
            val transaction = createTransaction(
                paymentData = MultiplePaymentData(
                    listOf(
                        createSinglePaymentDataWithBalance(
                            accountPaymentMethod = balance,
                            amount = 1L,
                            payment = null,
                            calculationId = null,
                        ),
                    ),
                ),
            )

            assertThrows<IllegalStateException> { locator.checkOnFraudPrevention(transaction) }
        }

        @Test
        fun `deve lancar excecao para uma transacao com pagamento externo`() {
            val transaction = createTransaction(
                paymentData = SinglePaymentData(
                    accountPaymentMethod = balance,
                    details = PaymentMethodsDetailWithExternalPayment(providerName = AccountProviderName.ME_POUPE),
                    payment = null,
                ),
            )

            assertThrows<IllegalStateException> { locator.checkOnFraudPrevention(transaction) }
        }

        @Test
        fun `deve retornar null para uma transacao com pagamento usando saldo`() {
            val transaction = createTransaction(
                paymentData = createSinglePaymentDataWithBalance(
                    accountPaymentMethod = balance,
                    amount = 1L,
                    payment = null,
                    calculationId = null,
                ),
            )

            locator.checkOnFraudPrevention(transaction) shouldBe null
        }

        @Test
        fun `deve retornar null para uma transacao com pagamento usando um cartao de credito legitimo`() {
            val transaction = createTransaction(
                paymentData = createSinglePaymentDataWithCreditCard(
                    accountPaymentMethod = externalCreditCard,
                    netAmount = 1L,
                    feeAmount = 0,
                    installments = 1,
                ),
            )

            every {
                creditCardFraudPreventionService.check(any(), any(), any(), any())
            } returns Unit.right()

            locator.checkOnFraudPrevention(transaction) shouldBe null

            verify {
                creditCardFraudPreventionService.check(
                    paymentMethod = externalCreditCard,
                    netAmount = 1L,
                    feeAmount = 0,
                    installments = 1,
                )
            }
        }

        @ParameterizedTest
        @EnumSource(FraudPreventionErrors::class)
        fun `deve retornar recusado para uma transacao com pagamento usando um cartao de credito fraudado`(
            fraudPreventionError: FraudPreventionErrors,
        ) {
            val transaction = createTransaction(
                paymentData = createSinglePaymentDataWithCreditCard(
                    accountPaymentMethod = externalCreditCard,
                    netAmount = 1L,
                    feeAmount = 0,
                    installments = 1,
                ),
            )

            every {
                creditCardFraudPreventionService.check(
                    paymentMethod = any(),
                    netAmount = any(),
                    feeAmount = any(),
                    installments = 1,
                )
            } returns fraudPreventionError.left()

            val result = locator.checkOnFraudPrevention(transaction)

            result.shouldNotBeNull()
            result.error shouldBe fraudPreventionError

            val paymentData = transaction.paymentData
            paymentData.shouldBeTypeOf<SinglePaymentData>()
            paymentData.payment shouldBe result

            verify {
                creditCardFraudPreventionService.check(
                    paymentMethod = externalCreditCard,
                    netAmount = 1L,
                    feeAmount = 0,
                    installments = 1,
                )
            }
        }
    }

    @Nested
    @DisplayName("ao tentar capturar fundos")
    inner class CaptureFunds {
        @Test
        fun `deve lancar excecao para uma transacao com pagamento multiplo`() {
            val transaction = createTransaction(
                paymentData = MultiplePaymentData(
                    listOf(
                        createSinglePaymentDataWithBalance(
                            accountPaymentMethod = balance,
                            amount = 1L,
                            payment = null,
                            calculationId = null,
                        ),
                    ),
                ),
            )

            assertThrows<IllegalStateException> { locator.captureFunds(transaction) }
        }

        @Test
        fun `deve lancar excecao para uma transacao com pagamento externo`() {
            val transaction = createTransaction(
                paymentData = SinglePaymentData(
                    accountPaymentMethod = balance,
                    details = PaymentMethodsDetailWithExternalPayment(providerName = AccountProviderName.ME_POUPE),
                    payment = null,
                ),
            )

            assertThrows<IllegalStateException> { locator.captureFunds(transaction) }
        }

        @ParameterizedTest
        @CsvSource(
            value = [
                "SUCCESS,false,''",
                "REFUNDED,false,refunded",
                "INSUFFICIENT_FUNDS,false,insufficient funds",
                "ERROR,false,error",
                "INVALID_DATA,false,invalid data",
                "TIMEOUT,true,timeout",
                "UNKNOWN,true,unknown",
            ],
        )
        fun `deve retornar BalanceAuthorization para uma transacao com pagamento usando saldo`(
            bankOperationStatus: BankOperationStatus,
            expectedTimeout: Boolean,
            errorDescription: String,
        ) {
            val transaction = createTransaction(
                paymentData = createSinglePaymentDataWithBalance(
                    accountPaymentMethod = balance,
                    amount = 1L,
                    payment = null,
                    calculationId = null,
                ),
            )

            val bankTransfer = BankTransfer(
                operationId = BankOperationId(value = "BankOperationId"),
                gateway = FinancialServiceGateway.FRIDAY,
                status = bankOperationStatus,
                amount = 1L,
                authentication = "authentication",
                errorDescription = errorDescription,
            )

            every {
                fundProvider.captureFunds(any(), any(), any(), any())
            } returns bankTransfer

            val result = locator.captureFunds(transaction)

            result.shouldBeTypeOf<BalanceAuthorization>()
            result.operationId shouldBe bankTransfer.operationId
            result.status shouldBe bankTransfer.status
            result.amount shouldBe bankTransfer.amount
            result.paymentGateway shouldBe bankTransfer.gateway
            result.errorDescription shouldBe bankTransfer.errorDescription
            result.timeout shouldBe expectedTimeout

            val paymentData = transaction.paymentData
            paymentData.shouldBeTypeOf<SinglePaymentData>()
            paymentData.payment shouldBe result

            verify {
                fundProvider.captureFunds(
                    accountId = balance.accountId,
                    accountPaymentMethodId = balance.id,
                    paymentMethod = balance.method,
                    amount = 1L,
                )
            }
        }

        @Test
        fun `deve retornar CreditCardAuthorization para uma transacao com pagamento usando cartao de credito`() {
            val transaction = createTransaction(
                paymentData = createSinglePaymentDataWithCreditCard(
                    accountPaymentMethod = externalCreditCard,
                    netAmount = 1L,
                    feeAmount = 0,
                    installments = 1,
                ),
            )

            val creditCardAuthoriation = CreditCardAuthorization(
                acquirer = Acquirer.CIELO,
                transactionId = "",
                status = CreditCardPaymentStatus.AUTHORIZED,
                acquirerTid = null,
                amount = null,
                acquirerReturnCode = null,
                acquirerReturnMessage = "",
                tid = null,
                authorizationCode = null,
            )

            every {
                acquirerService.authorizeAndCapture(any(), any(), any(), any(), any())
            } returns creditCardAuthoriation

            val result = withMockedMetric(CaptureFundsSummary) {
                val result = locator.captureFunds(transaction)
                verify { CaptureFundsSummary.push(any(), any<Number>()) }
                result
            }

            result.shouldBeTypeOf<CreditCardAuthorization>()
            result shouldBe creditCardAuthoriation

            val paymentData = transaction.paymentData
            paymentData.shouldBeTypeOf<SinglePaymentData>()
            paymentData.payment shouldBe result

            verify {
                acquirerService.authorizeAndCapture(
                    accountId = externalCreditCard.accountId,
                    orderId = transaction.id.value,
                    amount = 1L,
                    creditCard = externalCreditCard.method as CreditCard,
                    softDescriptor = transaction.settlementData.getTarget<Bill>().getPayee(),
                )
            }
        }
    }

    @Nested
    @DisplayName("ao tentar devolver fundos")
    inner class UndoCaptureFunds {
        @Test
        fun `deve lancar excecao para uma transacao com pagamento multiplo`() {
            val transaction = createTransaction(
                paymentData = MultiplePaymentData(
                    listOf(
                        createSinglePaymentDataWithBalance(
                            accountPaymentMethod = balance,
                            amount = 1L,
                            payment = null,
                            calculationId = null,
                        ),
                    ),
                ),
            )

            assertThrows<IllegalStateException> { locator.undoCaptureFunds(transaction) }
        }

        @ParameterizedTest
        @CsvSource(
            value = [
                "SUCCESS,REFUNDED,ongoingRefundOperationId,false,''",
                "REFUNDED,SUCCESS,BankOperationId,false,refunded",
                "INSUFFICIENT_FUNDS,SUCCESS,BankOperationId,false,insufficient funds",
                "ERROR,SUCCESS,BankOperationId,false,error",
                "INVALID_DATA,SUCCESS,BankOperationId,false,invalid data",
                "TIMEOUT,SUCCESS,BankOperationId,true,timeout",
                "UNKNOWN,SUCCESS,BankOperationId,true,unknown",
            ],
        )
        fun `deve retornar BalanceAuthorization para uma transacao com pagamento usando saldo`(
            bankOperationStatus: BankOperationStatus,
            expectedBankOperationStatus: BankOperationStatus,
            expectedBankOperationId: String,
            expectedTimeout: Boolean,
            errorDescription: String,
        ) {
            val balanceAuthorization = BalanceAuthorization(
                operationId = BankOperationId(value = "operationId"),
                status = BankOperationStatus.SUCCESS,
                timeout = false,
                amount = 1L,
                ongoingRefundOperationId = BankOperationId(value = "ongoingRefundOperationId"),
                paymentGateway = FinancialServiceGateway.CELCOIN,
            )

            val transaction = createTransaction(
                paymentData = createSinglePaymentDataWithBalance(
                    accountPaymentMethod = balance,
                    amount = balanceAuthorization.amount,
                    payment = balanceAuthorization,
                    calculationId = null,
                ),
            )

            val bankTransfer = BankTransfer(
                operationId = BankOperationId(value = "BankOperationId"),
                gateway = FinancialServiceGateway.FRIDAY,
                status = bankOperationStatus,
                amount = 1L,
                authentication = "authentication",
                errorDescription = errorDescription,
            )

            every {
                fundProvider.undoCaptureFunds(any(), any(), any(), any(), any(), any())
            } returns bankTransfer

            val result = locator.undoCaptureFunds(transaction)

            result.shouldBeTypeOf<BalanceAuthorization>()
            result.operationId shouldBe bankTransfer.operationId
            result.status shouldBe bankTransfer.status
            result.amount shouldBe bankTransfer.amount
            result.paymentGateway shouldBe bankTransfer.gateway
            result.errorDescription shouldBe bankTransfer.errorDescription
            result.timeout shouldBe expectedTimeout

            val paymentData = transaction.paymentData
            paymentData.shouldBeTypeOf<SinglePaymentData>()
            paymentData.payment shouldBe balanceAuthorization.copy(
                status = expectedBankOperationStatus,
                ongoingRefundOperationId = BankOperationId(value = expectedBankOperationId),
            )

            verify {
                fundProvider.undoCaptureFunds(
                    accountId = balance.accountId,
                    accountPaymentMethodId = balance.id,
                    paymentMethod = balance.method,
                    amount = 1L,
                    operationId = balanceAuthorization.operationId,
                    ongoingRefundOperationId = balanceAuthorization.ongoingRefundOperationId,
                )
            }
        }

        @Test
        fun `deve retornar CreditCardAuthorization para uma transacao com pagamento usando cartao de credito`() {
            val creditCardAuthoriation = CreditCardAuthorization(
                acquirer = Acquirer.CIELO,
                transactionId = "",
                status = CreditCardPaymentStatus.PAYMENT_CONFIRMED,
                acquirerTid = null,
                amount = null,
                acquirerReturnCode = null,
                acquirerReturnMessage = "",
                tid = null,
                authorizationCode = null,
            )

            val transaction = createTransaction(
                paymentData = createSinglePaymentDataWithCreditCard(
                    accountPaymentMethod = externalCreditCard,
                    netAmount = 1L,
                    feeAmount = 0,
                    installments = 1,
                    payment = creditCardAuthoriation,
                ),
            )

            every {
                acquirerService.cancel(any())
            } just Runs

            val result = withMockedMetric(UndoCaptureFundsSummary) {
                val result = locator.undoCaptureFunds(transaction)
                verify { UndoCaptureFundsSummary.push(any(), any<Number>()) }
                result
            }

            result.shouldBeTypeOf<CreditCardAuthorization>()
            result shouldBe creditCardAuthoriation.copy(status = CreditCardPaymentStatus.REFUNDED)

            val paymentData = transaction.paymentData
            paymentData.shouldBeTypeOf<SinglePaymentData>()
            paymentData.payment shouldBe result

            verify {
                acquirerService.cancel(
                    orderId = transaction.id.value,
                )
            }
        }

        @Test
        fun `deve lancar execao quando falha o estorno para uma transacao com pagamento usando cartao de credito`() {
            val creditCardAuthoriation = CreditCardAuthorization(
                acquirer = Acquirer.CIELO,
                transactionId = "",
                status = CreditCardPaymentStatus.PAYMENT_CONFIRMED,
                acquirerTid = null,
                amount = null,
                acquirerReturnCode = null,
                acquirerReturnMessage = "",
                tid = null,
                authorizationCode = null,
            )

            val transaction = createTransaction(
                paymentData = createSinglePaymentDataWithCreditCard(
                    accountPaymentMethod = externalCreditCard,
                    netAmount = 1L,
                    feeAmount = 0,
                    installments = 1,
                    payment = creditCardAuthoriation,
                ),
            )

            every {
                acquirerService.cancel(any())
            } throws PaymentNotCanceled("", "")

            assertThrows<PaymentNotCanceled> { locator.undoCaptureFunds(transaction) }

            verify {
                acquirerService.cancel(
                    orderId = transaction.id.value,
                )
            }
        }
    }

    @Nested
    @DisplayName("ao tentar atualizar um status de pagamento")
    inner class UpdatePaymentStatus {
        @Test
        fun `deve lancar excecao para uma transacao com pagamento multiplo`() {
            val transaction = createTransaction(
                paymentData = MultiplePaymentData(
                    listOf(
                        createSinglePaymentDataWithBalance(
                            accountPaymentMethod = balance,
                            amount = 1L,
                            payment = null,
                            calculationId = null,
                        ),
                    ),
                ),
            )

            assertThrows<IllegalStateException> { locator.updatePaymentStatus(transaction) }
        }

        @Test
        fun `deve lancar excecao para uma transacao com pagamento externo`() {
            val transaction = createTransaction(
                paymentData = SinglePaymentData(
                    accountPaymentMethod = balance,
                    details = PaymentMethodsDetailWithExternalPayment(providerName = AccountProviderName.ME_POUPE),
                    payment = null,
                ),
            )

            assertThrows<IllegalStateException> { locator.updatePaymentStatus(transaction) }
        }

        @ParameterizedTest
        @CsvSource(value = ["true,SUCCESS", "false,ERROR"])
        fun `deve atualizar a transação com status do pagamento usando saldo`(expectedCaptureFundsCheck: Boolean, expectedPaymentStatus: PaymentStatus) {
            val balanceAuthorization = BalanceAuthorization(
                operationId = BankOperationId(value = "operationId"),
                status = BankOperationStatus.UNKNOWN,
                timeout = true,
                amount = 1L,
                ongoingRefundOperationId = BankOperationId(value = "ongoingRefundOperationId"),
                paymentGateway = FinancialServiceGateway.ARBI,
            )

            val transaction = createTransaction(
                createdAt = getZonedDateTime().minusMinutes(500),
                paymentData = createSinglePaymentDataWithBalance(
                    accountPaymentMethod = balance,
                    amount = balanceAuthorization.amount,
                    payment = balanceAuthorization,
                    calculationId = null,
                ),
            )

            every { fundProvider.checkCaptureFunds(any(), any(), any(), any()) } returns expectedCaptureFundsCheck

            locator.updatePaymentStatus(transaction)

            transaction.paymentData.status() shouldBe expectedPaymentStatus
        }

        @Test
        fun `deve retornar UNKNOWN caso a reposta da captura de fundos não encontre saldo debitado e a transação esteja dentro de uma janela de 15 minutos`() {
            // Caso onde o Arbi demora a consolidar o débito no extrato
            val balanceAuthorization = BalanceAuthorization(
                operationId = BankOperationId(value = "operationId"),
                status = BankOperationStatus.UNKNOWN,
                timeout = true,
                amount = 1L,
                ongoingRefundOperationId = BankOperationId(value = "ongoingRefundOperationId"),
                paymentGateway = FinancialServiceGateway.ARBI,
            )

            val transaction = createTransaction(
                createdAt = getZonedDateTime(),
                paymentData = createSinglePaymentDataWithBalance(
                    accountPaymentMethod = balance,
                    amount = balanceAuthorization.amount,
                    payment = balanceAuthorization,
                    calculationId = null,
                ),
            )

            every { fundProvider.checkCaptureFunds(any(), any(), any(), any()) } returns false

            locator.updatePaymentStatus(transaction)

            transaction.paymentData.status() shouldBe PaymentStatus.UNKNOWN
        }

        @Test
        fun `deve atualizar a transação com status do pagamento usando cartão de crédito`() {
            val creditCardAuthoriation = CreditCardAuthorization(
                acquirer = Acquirer.CIELO,
                transactionId = "",
                status = CreditCardPaymentStatus.PAYMENT_CONFIRMED,
                acquirerTid = null,
                amount = null,
                acquirerReturnCode = null,
                acquirerReturnMessage = "",
                tid = null,
                authorizationCode = null,
            )

            val transaction = createTransaction(
                paymentData = createSinglePaymentDataWithCreditCard(
                    accountPaymentMethod = externalCreditCard,
                    netAmount = 1L,
                    feeAmount = 0,
                    installments = 1,
                    payment = null,
                ),
            )

            every {
                acquirerService.checkStatus(transaction.id.value)
            } returns creditCardAuthoriation

            locator.updatePaymentStatus(transaction)

            transaction.paymentData.status() shouldBe PaymentStatus.SUCCESS
        }
    }
}