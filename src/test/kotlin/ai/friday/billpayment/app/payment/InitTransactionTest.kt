package ai.friday.billpayment.app.payment

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.PaymentMethodNotFound
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.balance
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billIgnored
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billRegisterUpdatedAlreadyPaid
import ai.friday.billpayment.billRegisterUpdatedNotPayable
import ai.friday.billpayment.concessionariaBill
import ai.friday.billpayment.createPaymentMethodsDetailWithBalance
import ai.friday.billpayment.integration.CREATED
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.NAME
import ai.friday.billpayment.invoiceBill
import ai.friday.billpayment.paymentMethodId
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.ZonedDateTime
import java.util.stream.Stream
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

internal class InitTransactionTest {

    private val paymentMethod = balance.copy(id = paymentMethodId)
    private val accountRepository: AccountRepository = mockk(relaxed = true) {
        every {
            findById(any())
        } returns ACCOUNT

        every {
            findAccountPaymentMethodByIdAndAccountId(
                paymentMethodId,
                ACCOUNT.accountId,
                AccountPaymentMethodStatus.ACTIVE,
            )
        } returns paymentMethod
    }

    val walletService: WalletService = mockk() {
        every {
            findWallet(any())
        } returns mockk {
            every {
                founder
            } returns mockk {
                every {
                    accountId
                } returns ACCOUNT.accountId
                every {
                    document
                } returns DOCUMENT
                every {
                    name
                } returns NAME
                every {
                    created
                } returns ZonedDateTime.parse(CREATED)
            }
        }
    }

    private val transactionRepository: TransactionRepository = mockk(relaxed = true)

    private val updateBillService = mockk<UpdateBillService>()
    private val transactionService = TransactionService(
        accountRepository = accountRepository,
        notificationAdapter = mockk(),
        transactionRepository = transactionRepository,
        walletService = walletService,
        updateBillService = updateBillService,
    )

    @ParameterizedTest
    @MethodSource("valuesProvider")
    fun `should throw BillNotPayableException when bill is not payable`(bill: Bill) {
        every { updateBillService.getBill(bill.billId) } returns bill

        assertThrows<BillNotPayableException> {
            transactionService.initBillPaymentTransaction(
                BillPaymentCommand(
                    bill.walletId,
                    bill.billId,
                    ActionSource.System,
                    createPaymentMethodsDetailWithBalance(paymentMethodId, bill.amountTotal),
                ),
            )
        }
    }

    @Test
    fun `should init transaction with BOLETO_PAYMENT type`() {
        every { updateBillService.getBill(concessionariaBill.billId) } returns concessionariaBill
        val bill = concessionariaBill
        val transaction = transactionService.initBillPaymentTransaction(
            BillPaymentCommand(
                bill.walletId,
                bill.billId,
                ActionSource.System,
                createPaymentMethodsDetailWithBalance(paymentMethodId, bill.amountTotal, "FAKE_CALCULATION_ID"),
            ),
        )

        transaction.type shouldBe TransactionType.BOLETO_PAYMENT
        transaction.payer shouldBe Payer(
            accountId = ACCOUNT.accountId,
            document = ACCOUNT.document,
            name = ACCOUNT.name,
        )
        transaction.paymentData shouldBe SinglePaymentData(
            accountPaymentMethod = paymentMethod,
            details = PaymentMethodsDetailWithBalance(
                amount = bill.amountTotal,
                paymentMethodId = paymentMethod.id,
                calculationId = "FAKE_CALCULATION_ID",
            ),
            payment = null,
        )
    }

    @Test
    fun `should init transaction with DIRECT_INVOICE type`() {
        every { updateBillService.getBill(invoiceBill.billId) } returns invoiceBill

        val bill = invoiceBill
        val transaction = transactionService.initBillPaymentTransaction(
            BillPaymentCommand(
                bill.walletId,
                bill.billId,
                ActionSource.System,
                createPaymentMethodsDetailWithBalance(paymentMethodId, bill.amountTotal),
            ),
        )

        transaction.type shouldBe TransactionType.DIRECT_INVOICE
        transaction.payer shouldBe Payer(
            accountId = ACCOUNT.accountId,
            document = ACCOUNT.document,
            name = ACCOUNT.name,
        )
    }

    @Test
    fun `deve falhar se o metodo de pagamento nao estiver ativo`() {
        every { updateBillService.getBill(invoiceBill.billId) } returns invoiceBill

        every {
            accountRepository.findAccountPaymentMethodByIdAndAccountId(
                paymentMethodId,
                ACCOUNT.accountId,
                AccountPaymentMethodStatus.ACTIVE,
            )
        } throws PaymentMethodNotFound("FAKE")

        val bill = invoiceBill
        assertThrows<PaymentMethodNotFound> {
            transactionService.initBillPaymentTransaction(
                BillPaymentCommand(
                    bill.walletId,
                    bill.billId,
                    ActionSource.System,
                    createPaymentMethodsDetailWithBalance(paymentMethodId, bill.amountTotal),
                ),
            )
        }

        verify {
            transactionRepository wasNot Called
        }
    }

    @Test
    fun `deve falhar ao tentar iniciar uma transação onde o método de pagamento é externo`() {
        every { updateBillService.getBill(invoiceBill.billId) } returns invoiceBill

        val bill = invoiceBill
        assertThrows<InvalidTransactionPaymentDataException> {
            transactionService.initBillPaymentTransaction(
                BillPaymentCommand(
                    bill.walletId,
                    bill.billId,
                    ActionSource.System,
                    PaymentMethodsDetailWithExternalPayment(
                        providerName = AccountProviderName.ME_POUPE,
                    ),
                ),
            )
        }

        verify {
            transactionRepository wasNot Called
        }
    }

    @Test
    fun `deve falhar ao tentar iniciar uma transação onde o método de pagamento não é encontrado`() {
        every { updateBillService.getBill(invoiceBill.billId) } returns invoiceBill
        every {
            accountRepository.findAccountPaymentMethodByIdAndAccountId(
                paymentMethodId,
                ACCOUNT.accountId,
                AccountPaymentMethodStatus.ACTIVE,
            )
        } throws PaymentMethodNotFound("FAKE")

        val bill = invoiceBill
        assertThrows<PaymentMethodNotFound> {
            transactionService.initBillPaymentTransaction(
                BillPaymentCommand(
                    bill.walletId,
                    bill.billId,
                    ActionSource.System,
                    createPaymentMethodsDetailWithBalance(paymentMethodId, bill.amountTotal),
                ),
            )
        }

        verify {
            transactionRepository wasNot Called
        }
    }

    @Test
    fun `deve falhar ao tentar iniciar uma transação contendo método de pagamento é externo`() {
        every { updateBillService.getBill(invoiceBill.billId) } returns invoiceBill

        val bill = invoiceBill
        assertThrows<InvalidTransactionPaymentDataException> {
            transactionService.initBillPaymentTransaction(
                BillPaymentCommand(
                    bill.walletId,
                    bill.billId,
                    ActionSource.System,

                    MultiplePaymentMethodsDetail(
                        methods = listOf(
                            createPaymentMethodsDetailWithBalance(paymentMethodId, bill.amountTotal),
                            PaymentMethodsDetailWithExternalPayment(
                                providerName = AccountProviderName.ME_POUPE,
                            ),
                        ),
                    ),
                ),
            )
        }

        verify {
            transactionRepository wasNot Called
        }
    }

    @Test
    fun `deve falhar ao tentar iniciar uma transação utilizando um detalhe de um tipo com o accountPaymentMethodId sendo referente a outro tipo`() {
        every { updateBillService.getBill(invoiceBill.billId) } returns invoiceBill

        val bill = invoiceBill
        assertThrows<InvalidTransactionPaymentDataException> {
            transactionService.initBillPaymentTransaction(
                BillPaymentCommand(
                    bill.walletId,
                    bill.billId,
                    ActionSource.System,
                    PaymentMethodsDetailWithCreditCard(
                        paymentMethodId = paymentMethodId,
                        netAmount = bill.amountTotal,
                        feeAmount = 1L,
                        installments = 1,
                        calculationId = null,
                        fee = 4.0,
                    ),
                ),
            )
        }

        verify {
            transactionRepository wasNot Called
        }
    }

    @Test
    fun `deve falhar ao tentar iniciar uma transação se o valor pago nos métodos de pagamento não forem igual ao da bill`() {
        every { updateBillService.getBill(invoiceBill.billId) } returns invoiceBill

        val bill = invoiceBill
        assertThrows<InvalidTransactionPaymentValueException> {
            transactionService.initBillPaymentTransaction(
                BillPaymentCommand(
                    bill.walletId,
                    bill.billId,
                    ActionSource.System,
                    createPaymentMethodsDetailWithBalance(paymentMethodId, bill.amountTotal - 1L),
                ),
            )
        }

        verify {
            transactionRepository wasNot Called
        }
    }

    companion object {
        @JvmStatic
        fun valuesProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(Bill.build(billAdded, billRegisterUpdatedAlreadyPaid)),
                Arguments.of(Bill.build(billAdded, billIgnored)),
                Arguments.of(Bill.build(billAdded, billRegisterUpdatedNotPayable)),
                Arguments.of(Bill.build(billAdded, billPaid)),
            )
        }
    }
}