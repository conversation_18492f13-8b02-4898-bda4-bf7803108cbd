package ai.friday.billpayment.app.payment

import ai.friday.billpayment.adapters.celcoin.payerBank
import ai.friday.billpayment.adapters.celcoin.payerDocument
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.ReceiptDbRepository
import ai.friday.billpayment.adapters.dynamodb.ReceiptDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.ReceiptEntity
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.adapters.handlebar.ReceiptHandlebarCompiler
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPayer
import ai.friday.billpayment.app.bill.PixCopyAndPaste
import ai.friday.billpayment.app.bill.PixQrCodeData
import ai.friday.billpayment.app.bill.PixQrCodeType
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.integrations.ReceiptFileRepository
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.payment.receipt.builder.BoletoReceiptDataBuilder
import ai.friday.billpayment.app.payment.receipt.builder.InvoiceReceiptDataBuilder
import ai.friday.billpayment.app.payment.receipt.builder.PixReceiptDataBuilder
import ai.friday.billpayment.app.payment.receipt.builder.ReceiptDataBuilderService
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixKeyOwner
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.createEmailTemplatesConfiguration
import ai.friday.billpayment.createTransaction
import ai.friday.billpayment.integration.CNPJ_1
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.DOCUMENT_2
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WALLET_ID_2
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.invoicePaid
import ai.friday.billpayment.invoicePaymentScheduled
import ai.friday.billpayment.invoicePaymentStarted
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.pixKeyDetails
import ai.friday.billpayment.pixPaid
import ai.friday.billpayment.pixPaymentScheduled
import ai.friday.billpayment.pixPaymentStarted
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import arrow.core.Either
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.kotest.matchers.string.shouldNotContain
import io.kotest.matchers.types.shouldBeInstanceOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import java.math.BigInteger
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.stream.Stream
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

internal class ReceiptServiceTest {

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbEnhancedClient = DynamoDBUtils.setupDynamoDB()
    private val receiptDynamoDAO = ReceiptDynamoDAO(dynamoDbEnhancedClient)
    private val accountDAO = AccountDynamoDAO(dynamoDbEnhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(dynamoDbEnhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(dynamoDbEnhancedClient)
    private val nsuDAO = NSUDynamoDAO(dynamoDbEnhancedClient)
    private val transactionDynamo = TransactionDynamo(dynamoDbEnhancedClient)

    private val accountDbRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )
    private val receiptFileRepository: ReceiptFileRepository = mockk {
        every { generateReceiptFiles(any(), any()) } returns ReceiptFilesData(imageBytes = "BYTEARRAY".toByteArray(), imageFormat = "png", pdfBytes = "BYTEARRAY".toByteArray(), imageUrl = "imageUrl", pdfUrl = "pdfUrl")
    }
    private val receiptRepository = ReceiptDbRepository(receiptDynamoDAO)
    private val billEventDAO = BillEventDynamoDAO(dynamoDbEnhancedClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(dynamoDbEnhancedClient)
    private val billEventRepository = spyk(
        BillEventDBRepository(
            billEventDAO,
            uniqueConstraintDAO,
            mockk(),
            transactionDynamo,
        ),
    )

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet(
        otherMembers = listOf(
            walletFixture.participant,
            walletFixture.ultraLimitedParticipant,
        ),
    )
    private val participantWallet = walletFixture.buildPrimaryWallet(
        walletFixture.participantAccount,
        otherMembers = listOf(
            walletFixture.founder.copy(type = MemberType.COLLABORATOR),
            walletFixture.ultraLimitedParticipant,
        ),
    )
    private val walletService: WalletService = mockk() {
        every {
            findWallet(any())
        } returns wallet
    }

    private val concessionariaPaidBill = Bill.build(
        billAdded,
        billPaymentScheduled.copy(actionSource = ActionSource.Api(accountId = walletFixture.participant.accountId)),
        billPaymentStart,
        billPaid,
    )

    private val participantPaidFounderBillId = BillId()
    private val participantPaidFounderBill = Bill.build(
        billAdded.copy(billId = participantPaidFounderBillId, actionSource = ActionSource.DDA(accountId = walletFixture.participant.accountId)), // bill é de outra pessoa
        billPaymentScheduled.copy(
            billId = participantPaidFounderBillId,
            paymentWalletId = participantWallet.id, // quem pagou foi esse participante
            actionSource = ActionSource.Api(accountId = walletFixture.participant.accountId),
        ),
        billPaymentStart.copy(billId = participantPaidFounderBillId),
        billPaid.copy(billId = participantPaidFounderBillId),
    )
    private val invoicePaidBill = Bill.build(
        invoiceAdded,
        invoicePaymentScheduled.copy(actionSource = ActionSource.Api(accountId = walletFixture.participant.accountId)),
        invoicePaymentStarted,
        invoicePaid,
    )
    private val pixPaidBill = Bill.build(
        pixAdded,
        pixPaymentScheduled.copy(actionSource = ActionSource.Api(accountId = walletFixture.participant.accountId)),
        pixPaymentStarted,
        pixPaid.copy(
            pixKeyDetails = pixKeyDetails.copy(
                holder = PixKeyHolder(
                    accountNo = BigInteger("1231234"),
                    accountDv = "0",
                    ispb = "1234",
                    institutionName = "Teste",
                    accountType = AccountType.CHECKING,
                    routingNo = 12,
                ),
            ),
        ),
    )
    private val authentication = "AAAAA"

    private fun buildReceiptService(repository: TransactionRepository): ReceiptService {
        val pixReceiptDataBuilder = PixReceiptDataBuilder(
            accountRepository = accountDbRepository,
            transactionRepository = repository,
        )

        val boletoReceiptDataBuilder = BoletoReceiptDataBuilder(
            transactionRepository = repository,
            boletoSettlementReceiptService = mockk {
                every { getReceipt(any()) } returns BillPaymentReceipt("", "1", authentication, "CELCOIN")
            },

        )

        val invoiceReceiptDataBuilder = InvoiceReceiptDataBuilder(
            accountRepository = accountDbRepository,
            transactionRepository = repository,
        )

        val receiptDataBuilderService = ReceiptDataBuilderService(
            receiptDataBuilder = listOf(
                pixReceiptDataBuilder,
                boletoReceiptDataBuilder,
                invoiceReceiptDataBuilder,
            ),
        )

        return ReceiptService(
            receiptDataBuilderService = receiptDataBuilderService,
            billEventRepository = mockk {
                every { getBillById(billPaid.billId) } returns Either.Right(concessionariaPaidBill)
                every { getBillById(invoicePaid.billId) } returns Either.Right(invoicePaidBill)
                every { getBillById(pixPaid.billId) } returns Either.Right(pixPaidBill)
                every { getBillById(participantPaidFounderBillId) } returns Either.Right(participantPaidFounderBill)
            },
            receiptFileRepository = receiptFileRepository,
            receiptTemplateCompilerFinderService = mockk {
                every { findReceiptTemplateCompiler(any()) } returns ReceiptHandlebarCompiler(createEmailTemplatesConfiguration())
            },
            receiptRepository = receiptRepository,
            walletService = walletService,
        )
    }

    @ParameterizedTest
    @MethodSource("receiptDatas")
    fun `should deserialize receipt data with accountId`(receiptData: ReceiptData) {
        val serializedWithWalletId = getObjectMapper().writeValueAsString(receiptData)
        serializedWithWalletId shouldContain "walletId"

        val serializedWithAccountId = serializedWithWalletId.replace("walletId", "accountId")
        serializedWithAccountId shouldNotContain "walletId"

        val deserializedReceipt = getObjectMapper().readerFor(ReceiptData::class.java)
            .readValue<ReceiptData>(serializedWithAccountId)

        deserializedReceipt shouldBe receiptData
    }

    @ParameterizedTest
    @MethodSource("receiptDatas")
    fun `should deserialize receipt data with walletId`(receiptData: ReceiptData) {
        val serializedWithWalletId = getObjectMapper().writeValueAsString(receiptData)
        serializedWithWalletId shouldContain "walletId"

        val deserializedReceipt = getObjectMapper().readerFor(ReceiptData::class.java)
            .readValue<ReceiptData>(serializedWithWalletId)

        deserializedReceipt shouldBe receiptData
    }

    @Test
    fun `deve retornar erro ao tentar fazer o parse de um recibo sem accountId no source`() {
        val receiptService = buildReceiptService(
            repository = mockk {
                every { getBankTransactionId(any()) } returns 1L
            },
        )

        val billPaidTest = billPaid.copy(
            billId = concessionariaPaidBill.billId,
            walletId = concessionariaPaidBill.walletId,
        )
        every {
            billEventRepository.getBillById(
                billPaidTest.billId,
            )
        } returns concessionariaPaidBill.right()

        every {
            walletService.findWallet(billPaidTest.walletId)
        } returns wallet
        val entity = ReceiptEntity().apply {
            partitionKey = billPaidTest.billId.value
            sortKey = "RECEIPT#${billPaidTest.walletId.value}"
            receipt =
                "{\"@type\":\"BOLETO\",\"billId\":{\"value\":\"${billPaidTest.billId.value}\"},\"walletId\":{\"value\":\"${billPaidTest.walletId.value}\"},\"source\":{\"@type\":\"DDA\"},\"authentication\":\"B8.5F.7A.5B.2C.D3.D0.F6.74.98.F1.3C.75.13.A0.9E\",\"dateTime\":\"2022-10-03T13:39:14.747-03:00[Brazil/East]\",\"assignor\":\"BCO DO ESTADO DO RS S.A.\",\"recipient\":{\"name\":\"CLUBE BRILHANTE\",\"document\":\"**************\",\"alias\":\"\",\"bankAccount\":null,\"pixKeyDetails\":null},\"totalAmount\":27600,\"payer\":{\"document\":\"***********\",\"name\":\"JANE DOE\",\"alias\":null},\"dueDate\":\"2022-10-31\",\"barcode\":{\"number\":\"************00307002103200931810000244664054\",\"digitable\":\"04192103230093181000602446640548191550000030700\"},\"walletName\":\"JOHN DOE\",\"scheduledBy\":\"JANE DOE\",\"paymentPartnerName\":\"BANCO VOTORANTIM\"}"
        }

        receiptDynamoDAO.save(entity)

        receiptService.getReceipt(
            billPaidTest.walletId,
            billPaidTest.billId,
            wallet.founder,
        ).shouldBeInstanceOf<Either.Left<GetReceiptErrors.ServerError>>()
    }

    @Test
    fun `deve retornar sucesso quando o founder que pagou a conta`() {
        val receiptService = buildReceiptService(
            repository = mockk {
                every { getBankTransactionId(any()) } returns 1L
            },
        )

        val billPaidTest = billPaid.copy(
            billId = concessionariaPaidBill.billId,
            walletId = concessionariaPaidBill.walletId,
        )
        every {
            billEventRepository.getBillById(
                billPaidTest.billId,
            )
        } returns concessionariaPaidBill.right()

        every {
            walletService.findWallet(billPaidTest.walletId)
        } returns wallet
        val entity = ReceiptEntity().apply {
            partitionKey = billPaidTest.billId.value
            sortKey = "RECEIPT#${billPaidTest.walletId.value}"
            receipt =
                "{\"@type\":\"BOLETO\",\"billId\":{\"value\":\"${billPaidTest.billId.value}\"},\"walletId\":{\"value\":\"${billPaidTest.walletId.value}\"},\"source\":{\"@type\":\"DDA\"},\"authentication\":\"B8.5F.7A.5B.2C.D3.D0.F6.74.98.F1.3C.75.13.A0.9E\",\"dateTime\":\"2022-10-03T13:39:14.747-03:00[Brazil/East]\",\"assignor\":\"BCO DO ESTADO DO RS S.A.\",\"recipient\":{\"name\":\"CLUBE BRILHANTE\",\"document\":\"**************\",\"alias\":\"\",\"bankAccount\":null,\"pixKeyDetails\":null},\"totalAmount\":27600,\"payer\":{\"document\":\"***********\",\"name\":\"JANE DOE\",\"alias\":null},\"dueDate\":\"2022-10-31\",\"barcode\":{\"number\":\"************00307002103200931810000244664054\",\"digitable\":\"04192103230093181000602446640548191550000030700\"},\"walletName\":\"JOHN DOE\",\"scheduledBy\":\"JANE DOE\",\"paymentPartnerName\":\"BANCO VOTORANTIM\"}"
        }

        receiptDynamoDAO.save(entity)

        receiptService.getReceipt(
            billPaidTest.walletId,
            billPaidTest.billId,
            wallet.founder,
        ).shouldBeInstanceOf<Either.Left<GetReceiptErrors.ServerError>>()
    }

    @Test
    fun `deve salvar o recibo com o walletId do participante com sucesso quando o participante que pagou a conta`() {
        val startedAt = ZonedDateTime.now().withZoneSameLocal(ZoneId.of("Brazil/East"))
        withGivenDateTime(startedAt) {
            val receiptService = buildReceiptService(
                repository = mockk {
                    every { getBankTransactionId(any()) } returns 1L
                    every { findById(any()) } returns createTransaction()
                },
            )

            every { walletService.findWallet(participantPaidFounderBill.paymentWalletId!!) } returns participantWallet

            val gotReceipt = receiptService.getReceipt(
                participantPaidFounderBill.paymentWalletId!!,
                participantPaidFounderBill.billId,
                walletFixture.participant,
            ).getOrNull()

            gotReceipt!!.scheduledBy shouldBe "Ciclano Tal"
            gotReceipt.walletName shouldBe participantWallet.name
            gotReceipt.billId shouldBe participantPaidFounderBill.billId
            gotReceipt.walletId shouldBe participantPaidFounderBill.paymentWalletId
            gotReceipt.source shouldBe ActionSource.DDA(accountId = walletFixture.participantAccount.accountId)
        }
    }

    companion object {
        private val boletoReceiptData = BoletoReceiptData(
            authentication = "***********.***********.88.99.AA.BB.CC.DD.EE.FF",
            dateTime = getZonedDateTime(),
            recipient = Recipient(
                name = "receipient",
                document = CNPJ_1,
                bankAccount = null,
                pixKeyDetails = null,
            ),
            totalAmount = 1042799,
            billId = BillId("FICHA_RECEIPT"),
            walletId = WalletId(WALLET_ID),
            payer = BillPayer(document = DOCUMENT, name = "receipient", alias = null),
            dueDate = getLocalDate(),
            assignor = "receipient",
            barcode = BarCode.ofDigitable("34191090080116587030201035380003586090000311015"),
            source = ActionSource.Webapp(role = Role.OWNER),
            walletName = null,
            scheduledBy = null,
            paymentPartnerName = "BANCO 756 / AGENCIA 3038",
            transactionId = TransactionId("transactionId"),
        )

        private val participantPaidWithHisWalletBoletoReceiptData = BoletoReceiptData(
            authentication = "***********.***********.88.99.AA.BB.CC.DD.EE.FF",
            dateTime = getZonedDateTime(),
            recipient = Recipient(
                name = "receipient",
                document = CNPJ_1,
                bankAccount = null,
                pixKeyDetails = null,
            ),
            totalAmount = 1042799,
            billId = BillId("FICHA_RECEIPT"),
            walletId = WalletId(WALLET_ID_2),
            payer = BillPayer(document = DOCUMENT, name = "receipient", alias = null),
            dueDate = getLocalDate(),
            assignor = "receipient",
            barcode = BarCode.ofDigitable("34191090080116587030201035380003586090000311015"),
            source = ActionSource.Webapp(role = Role.OWNER),
            walletName = null,
            scheduledBy = null,
            paymentPartnerName = "BANCO 756 / AGENCIA 3038",
            transactionId = TransactionId("transactionId"),
        )

        private val pixReceiptData = PixReceiptData(
            authentication = "***********.***********.88.99.AA.BB.CC.DD.EE.FF",
            dateTime = getZonedDateTime(),
            recipient = Recipient(
                name = "recipient",
                document = DOCUMENT_2,
                alias = "alias",
                bankAccount = null,
                pixKeyDetails = PixKeyDetails(
                    key = PixKey(value = "+*************", type = PixKeyType.PHONE),
                    holder = PixKeyHolder(
                        accountNo = BigInteger("23468"),
                        accountDv = "0",
                        ispb = "********",
                        institutionName = "Banco do Brasil",
                        accountType = AccountType.PAYMENT,
                        routingNo = 123,
                    ),
                    owner = PixKeyOwner(
                        name = "recipient",
                        document = DOCUMENT_2,
                    ),
                ),
                pixQrCodeData = PixQrCodeData(
                    type = PixQrCodeType.STATIC,
                    info = "info",
                    pixCopyAndPaste = PixCopyAndPaste("pixCopyAndPaste"),
                    pixId = "pixId",
                    fixedAmount = 1042799,
                    additionalInfo = mapOf("chave" to "valor"),
                    expiration = getZonedDateTime().plusHours(1),
                    originalAmount = 1042799,
                    automaticPixRecurringDataJson = null,
                ),
            ),
            totalAmount = 1042799,
            purpose = INVOICE_PURPOSE,
            billId = BillId("PIX_KEY_RECEIPT"),
            walletId = WalletId(WALLET_ID),
            payer = BillPayer(document = DOCUMENT, name = "recipient", alias = null),
            source = ActionSource.Webapp(role = Role.OWNER),
            payeeFinancialInstitution = FinancialInstitution(name = "payeeBank", ispb = null, compe = 1234),
            payerFinancialInstitution = FinancialInstitution(name = "payerBank", ispb = null, compe = 4321),
            payeeRoutingNo = 1L,
            payeeAccountNo = BigInteger("12345"),
            payeeAccountDv = "1",
            payeeAccountType = AccountType.PAYMENT,
            payerBankAccount = InternalBankAccount(
                accountType = AccountType.CHECKING,
                bankNo = 0,
                routingNo = 0,
                accountNo = 0,
                accountDv = "1",
                document = "**********",
                bankAccountMode = BankAccountMode.PHYSICAL,
            ),
            walletName = null,
            scheduledBy = null,
            transactionId = TransactionId("transactionId"),
        )

        private val invoiceReceiptData = InvoiceReceiptData(
            authentication = "***********.***********.88.99.AA.BB.CC.DD.EE.FF",
            dateTime = getZonedDateTime(),
            recipient = Recipient(
                name = "recipient",
                document = DOCUMENT_2,
                alias = "alias",
                bankAccount = BankAccount(
                    accountType = AccountType.PAYMENT,
                    bankNo = 1L,
                    routingNo = 123,
                    accountNo = BigInteger("12345"),
                    accountDv = "0",
                    document = DOCUMENT_2,
                    ispb = null,
                ),
                pixKeyDetails = null,
            ),
            totalAmount = 1042799,
            purpose = "CC",
            billId = BillId("INVOICE_RECEIPT"),
            walletId = WalletId(WALLET_ID),
            payer = BillPayer(document = DOCUMENT, name = "recipient", alias = null),
            source = ActionSource.Webapp(role = Role.OWNER),
            payeeBank = "10 - Banco XYZ",
            paymentPartnerName = payerBank,
            paymentPartnerDocument = payerDocument,
            walletName = null,
            scheduledBy = null,
            transactionId = TransactionId("transactionId"),
        )

        @JvmStatic
        fun receiptDatas(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(boletoReceiptData),
                Arguments.of(invoiceReceiptData),
                Arguments.of(pixReceiptData),
                Arguments.of(participantPaidWithHisWalletBoletoReceiptData),
            )
        }
    }
}