package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.payment.checkout.AsyncSettlementBoletoCheckout
import ai.friday.billpayment.app.payment.checkout.SyncBoletoCheckout
import ai.friday.billpayment.app.wallet.WalletId
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class CheckoutLocatorTest {

    private val featureConfiguration = mockk<FeatureConfiguration> {
        every { asyncSettlement } returns false
    }

    private val defaultCheckoutLocator = DefaultCheckoutLocator(
        balanceInvoiceCheckout = mockk(),
        balancePixCheckout = mockk(),
        balanceDirectInvoiceCheckout = mockk(),
        boletoCheckout = mockk(),
        asyncSettlementBoletoCheckout = mockk(),
        featureConfiguration = featureConfiguration,
        investmentCheckout = mockk(),
    )

    private val transaction = mockk<Transaction> {
        every { walletId } returns WalletId("123")
    }

    @Test
    fun `quando a conta for um ted deve devolver o BalanceDirectInvoiceCheckout`() {
        every { transaction.settlementData.getTarget<Bill>().billType } returns BillType.INVOICE
        every { transaction.type } returns TransactionType.DIRECT_INVOICE

        val checkout = defaultCheckoutLocator.getCheckout(transaction)

        checkout.shouldBeTypeOf<BalanceDirectInvoiceCheckout>()
    }

    @Test
    fun `quando a conta for um PIX deve devolver o BalancePixCheckout`() {
        every { transaction.settlementData.getTarget<Bill>().billType } returns BillType.PIX
        every { transaction.type } returns TransactionType.DIRECT_INVOICE

        val checkout = defaultCheckoutLocator.getCheckout(transaction)

        checkout.shouldBeTypeOf<BalancePixCheckout>()
    }

    @ParameterizedTest
    @EnumSource(value = BillType::class, names = ["FICHA_COMPENSACAO", "CONCESSIONARIA"])
    fun `quando a conta for um boleto e a feature flag estiver desligada, deve devolver o SyncSettlementBoletoCheckout`(
        billType: BillType,
    ) {
        every { transaction.settlementData.getTarget<Bill>().billType } returns billType
        every { transaction.type } returns TransactionType.BOLETO_PAYMENT

        val checkout = defaultCheckoutLocator.getCheckout(transaction)

        checkout.shouldBeTypeOf<SyncBoletoCheckout>()
    }

    @ParameterizedTest
    @EnumSource(value = BillType::class, names = ["FICHA_COMPENSACAO", "CONCESSIONARIA"])
    fun `quando a conta for um boleto e a feature flag estiver ligada, deve devolver o AsyncSettlementBoletoCheckout`(
        billType: BillType,
    ) {
        every { transaction.settlementData.getTarget<Bill>().billType } returns billType
        every { transaction.type } returns TransactionType.BOLETO_PAYMENT
        every { featureConfiguration.asyncSettlement } returns true

        val checkout = defaultCheckoutLocator.getCheckout(transaction)

        checkout.shouldBeTypeOf<AsyncSettlementBoletoCheckout>()
    }

    @ParameterizedTest
    @EnumSource(value = BillType::class, names = ["OTHERS"])
    fun `quando a conta for um tipo desconhecido, deve jogar exception`(billType: BillType) {
        every { transaction.settlementData.getTarget<Bill>().billType } returns billType
        every { transaction.type } returns TransactionType.DIRECT_INVOICE

        assertThrows<IllegalStateException> { defaultCheckoutLocator.getCheckout(transaction) }
    }
}