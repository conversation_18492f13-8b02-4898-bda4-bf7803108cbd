package ai.friday.billpayment.app.payment.receipt

import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.ReceiptFileRepository
import ai.friday.billpayment.app.integrations.ReceiptRepository
import ai.friday.billpayment.app.integrations.ReceiptTemplateCompiler
import ai.friday.billpayment.app.payment.BoletoReceiptData
import ai.friday.billpayment.app.payment.InvoiceReceiptData
import ai.friday.billpayment.app.payment.PixReceiptData
import ai.friday.billpayment.app.payment.ReceiptFilesData
import ai.friday.billpayment.app.payment.receipt.builder.ReceiptDataBuilderService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.pixAdded
import arrow.core.right
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class DefaultNotifyReceiptServiceTest {

    private val billEventRepository: BillEventRepository = mockk()
    private val notificationAdapter: NotificationAdapter = mockk()
    private val receiptDataBuilderService: ReceiptDataBuilderService = mockk()
    private val receiptFileRepository: ReceiptFileRepository = mockk()
    private val receiptTemplateCompiler: ReceiptTemplateCompiler = mockk {
        every {
            canBuildFor(any())
        } returns true
    }
    private val receiptRepository: ReceiptRepository = mockk()
    private val walletService: WalletService = mockk()

    private val defaultNotifyReceiptService = DefaultNotifyReceiptService(
        billEventRepository,
        receiptDataBuilderService,
        receiptRepository,
        walletService,
        receiptDataSenders = listOf(
            DefaultReceiptDataSenderService(
                notificationAdapter,
                receiptFileRepository,
                ReceiptTemplateCompilerFinderService(
                    receiptTemplateCompilers = listOf(receiptTemplateCompiler),
                ),
                walletService,
            ),
        ),
        messagePublisher = mockk(),
    )

    private val walletFixture = WalletFixture()

    private val wallet = walletFixture.buildWallet()

    private val boletoReceiptData = mockk<BoletoReceiptData>() {
        every {
            walletId
        } returns wallet.id
    }
    private val pixReceiptData = mockk<PixReceiptData>() {
        every {
            walletId
        } returns wallet.id
    }

    private val invoiceReceiptData = mockk<InvoiceReceiptData>() {
        every {
            walletId
        } returns wallet.id
    }
    private val compiledHtml = mockk<CompiledHtml>()
    private val compiledMailHtml = mockk<CompiledHtml>()
    private val pdfByteArray = ByteArray(0)

    private val receiptFilesData = ReceiptFilesData(imageBytes = ByteArray(0), imageFormat = "png", pdfBytes = pdfByteArray, imageUrl = "imageUrl", pdfUrl = "pdfUrl")

    @BeforeEach
    fun setup() {
        every {
            walletService.findWallet(any())
        } returns wallet

        every {
            receiptRepository.save(any())
        } just runs

        every {
            receiptTemplateCompiler.buildReceiptHtml(any(), any())
        } returns compiledHtml

        every {
            receiptTemplateCompiler.buildReceiptMailHtml(any())
        } returns compiledMailHtml

        every {
            receiptFileRepository.generateReceiptFiles(any(), any())
        } returns receiptFilesData
    }

    @ParameterizedTest
    @MethodSource("boletoBillAddEvent")
    fun `should notify boleto receipt when bill type is boleto`(billAddedEvent: BillEvent) {
        val bill = Bill.build(billAddedEvent, billPaid)

        every {
            billEventRepository.getBill(any(), any())
        } returns bill.right()

        every {
            receiptDataBuilderService.getReceiptData(any(), any(), any())
        } returns boletoReceiptData

        every { notificationAdapter.notifyBoletoReceipt(any(), any(), any(), any()) } just runs

        defaultNotifyReceiptService.notify(billPaid)

        verify {
            notificationAdapter.notifyBoletoReceipt(
                members = wallet.activeMembers,
                receiptData = boletoReceiptData,
                receiptFilesData = receiptFilesData,
                mailReceiptHtml = compiledMailHtml,
            )
            receiptTemplateCompiler.buildReceiptMailHtml(boletoReceiptData)
            receiptTemplateCompiler.buildReceiptHtml(boletoReceiptData, any())
            receiptFileRepository.generateReceiptFiles(boletoReceiptData, compiledHtml)
        }
    }

    @Test
    fun `should notify pix receipt when bill type is pix`() {
        val bill = Bill.build(pixAdded, billPaid)

        every {
            billEventRepository.getBill(any(), any())
        } returns bill.right()

        every {
            receiptDataBuilderService.getReceiptData(any(), any(), any())
        } returns pixReceiptData

        every { notificationAdapter.notifyPixReceipt(any(), any(), any(), any()) } just runs

        defaultNotifyReceiptService.notify(billPaid)

        verify {
            notificationAdapter.notifyPixReceipt(
                members = wallet.activeMembers,
                receiptData = pixReceiptData,
                receiptFilesData = receiptFilesData,
                mailReceiptHtml = compiledMailHtml,
            )
            receiptTemplateCompiler.buildReceiptMailHtml(pixReceiptData)
            receiptTemplateCompiler.buildReceiptHtml(pixReceiptData, any())
            receiptFileRepository.generateReceiptFiles(pixReceiptData, compiledHtml)
        }
    }

    @Test
    fun `should notify invoice receipt when bill type is invoice`() {
        val bill = Bill.build(invoiceAdded, billPaid)

        every {
            billEventRepository.getBill(any(), any())
        } returns bill.right()

        every {
            receiptDataBuilderService.getReceiptData(any(), any(), any())
        } returns invoiceReceiptData

        every { notificationAdapter.notifyInvoiceReceipt(any(), any(), any(), any()) } just runs

        defaultNotifyReceiptService.notify(billPaid)

        verify {
            notificationAdapter.notifyInvoiceReceipt(
                members = wallet.activeMembers,
                receiptData = invoiceReceiptData,
                receiptFilesData = receiptFilesData,
                mailReceiptHtml = compiledMailHtml,
            )
            receiptTemplateCompiler.buildReceiptMailHtml(invoiceReceiptData)
            receiptTemplateCompiler.buildReceiptHtml(invoiceReceiptData, any())
            receiptFileRepository.generateReceiptFiles(invoiceReceiptData, compiledHtml)
        }
    }

    companion object {

        @JvmStatic
        fun boletoBillAddEvent() = Stream.of(
            Arguments.of(billAdded),
            Arguments.of(billAddedFicha),
        )
    }
}