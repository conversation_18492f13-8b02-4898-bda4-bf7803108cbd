package ai.friday.billpayment.app.payment.transaction

import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.payment.Checkout
import ai.friday.billpayment.app.payment.CheckoutLocator
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.util.stream.Stream
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

internal class TransactionInProcessTest {
    private val messagePublisher = mockk<MessagePublisher>(relaxUnitFun = true)
    private val checkoutLocator = mockk<CheckoutLocator>()
    private val transactionInProcess = TransactionInProcess(messagePublisher, checkoutLocator)

    @ParameterizedTest
    @MethodSource("shouldSendToRetryQueue")
    fun `deve enviar para a fila de retentativa com o correto delay quando o checkout da transacao suportar retentativa`(
        billType: BillType,
        delay: Int?,
    ) {
        val checkout = mockk<Checkout>() {
            every {
                supportsRetryTransaction()
            } returns true
        }
        every { checkoutLocator.getCheckout(any()) } returns checkout

        val bill = mockk<Bill>()
        every { bill.billType } returns billType

        val transactionId = TransactionId("123")

        val transaction = mockk<Transaction>() {
            every { settlementData.getTarget<Bill>() } returns bill
            every { id } returns transactionId
        }

        transactionInProcess.execute(transaction)

        verify { messagePublisher.sendRetryTransactionMessage(transactionId, delay) }
    }

    @Test
    fun `nao deve enviar para a fila de retentativa quando o checkout da transacao não suportar retentativa`() {
        val checkout = mockk<Checkout>() {
            every {
                supportsRetryTransaction()
            } returns false
        }
        every { checkoutLocator.getCheckout(any()) } returns checkout

        val bill = mockk<Bill>()
        every { bill.billType } returns BillType.CONCESSIONARIA

        val transaction = mockk<Transaction>()
        every { transaction.settlementData.getTarget<Bill>() } returns bill

        every { messagePublisher.sendRetryTransactionMessage(any(), any()) } throws IllegalStateException()

        transactionInProcess.execute(transaction)

        verify(exactly = 0) { messagePublisher.sendRetryTransactionMessage(any(), any()) }
    }

    companion object {
        @JvmStatic
        fun shouldSendToRetryQueue(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(BillType.PIX, PIX_DELAY_TO_RETRY),
                Arguments.arguments(BillType.CONCESSIONARIA, BOLETO_DELAY_TO_RETRY),
                Arguments.arguments(BillType.FICHA_COMPENSACAO, BOLETO_DELAY_TO_RETRY),
                Arguments.arguments(BillType.INVOICE, null),
                Arguments.arguments(BillType.OTHERS, null),
            )
        }
    }
}