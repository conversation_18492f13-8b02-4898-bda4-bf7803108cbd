package ai.friday.billpayment.app.payment

import ai.friday.billpayment.adapters.celcoin.payerBank
import ai.friday.billpayment.adapters.celcoin.payerDocument
import ai.friday.billpayment.adapters.handlebar.ReceiptHandlebarCompiler
import ai.friday.billpayment.adapters.s3.ReceiptS3Repository
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPayer
import ai.friday.billpayment.app.bill.PixCopyAndPaste
import ai.friday.billpayment.app.bill.PixQrCodeData
import ai.friday.billpayment.app.bill.PixQrCodeType
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixKeyOwner
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.utils.PublicHttpLinkGenerator
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.balance
import ai.friday.billpayment.createEmailTemplatesConfiguration
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.CNPJ_1
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.DOCUMENT_2
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.withPixParticipants
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import java.math.BigInteger
import org.junit.jupiter.api.Test

@MicronautTest(environments = [FRIDAY_ENV])
internal class ReceiptS3RepositoryTest(private val receiptRepository: ReceiptS3Repository) {

    private val objectRepository: ObjectRepository = mockk()

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    private val LARGE_ASSIGNOR = "BRADESCO S.A muito grande para não caber em uma linha"

    @MockBean(ObjectRepository::class)
    fun getObjectRepository() = objectRepository

    @MockBean(PublicHttpLinkGenerator::class)
    fun getPublicHttpLinkGenerator() = mockk<PublicHttpLinkGenerator>() {
        every { generate(any(), any(), any(), any()) } returns ""
    }

    private val LARGE_PAYER_NAME = "Payer Name muito grande com mais de uma linha certamente"
    private val LARGE_RECIPIENT_NAME = "Recipient Name muito grande com mais de uma linha certamente"
    private val receiptHandlebarCompiler = ReceiptHandlebarCompiler(createEmailTemplatesConfiguration())

    @Test
    fun `should generate invoice receipt`() {
        every {
            objectRepository.putObject(any(), any(), any())
        } just Runs

        val receiptData = InvoiceReceiptData(
            authentication = "***********.***********.88.99.AA.BB.CC.DD.EE.FF",
            dateTime = getZonedDateTime(),
            recipient = Recipient(
                name = LARGE_RECIPIENT_NAME,
                document = DOCUMENT_2,
                alias = "alias",
                bankAccount = BankAccount(
                    accountType = AccountType.PAYMENT,
                    bankNo = 1L,
                    routingNo = 123,
                    accountNo = BigInteger("12345"),
                    accountDv = "0",
                    document = DOCUMENT_2,
                    ispb = null,
                ),
                pixKeyDetails = null,
            ),
            totalAmount = 1042799,
            purpose = "CC",
            billId = BillId("INVOICE_RECEIPT"),
            walletId = WalletId(WALLET_ID),
            payer = BillPayer(document = DOCUMENT, name = LARGE_PAYER_NAME, alias = null),
            source = ActionSource.Webapp(role = Role.OWNER),
            payeeBank = "10 - Banco XYZ",
            paymentPartnerName = payerBank,
            paymentPartnerDocument = payerDocument,
            walletName = null,
            scheduledBy = null,
            transactionId = TransactionId("transactionId"),
        )
        receiptRepository.generateReceiptFiles(
            receiptData = receiptData,
            receiptHtml = receiptHandlebarCompiler.buildReceiptHtml(receiptData, wallet),
        )
    }

    @Test
    fun `should generate pix receipt with pix key`() {
        val accountId = AccountId(ACCOUNT_ID)
        val payerBankAccount = (balance.method as InternalBankAccount)

        every {
            objectRepository.putObject(any(), any(), any())
        } just Runs

        val receiptData = PixReceiptData(
            authentication = "***********.***********.88.99.AA.BB.CC.DD.EE.FF",
            dateTime = getZonedDateTime(),
            recipient = Recipient(
                name = LARGE_RECIPIENT_NAME,
                document = DOCUMENT_2,
                alias = "alias",
                bankAccount = null,
                pixKeyDetails = PixKeyDetails(
                    key = PixKey(value = "+*************", type = PixKeyType.PHONE),
                    holder = PixKeyHolder(
                        accountNo = BigInteger("23468"),
                        accountDv = "0",
                        ispb = "********",
                        institutionName = "Banco do Brasil",
                        accountType = AccountType.PAYMENT,
                        routingNo = 123,
                    ),
                    owner = PixKeyOwner(
                        name = LARGE_RECIPIENT_NAME,
                        document = DOCUMENT_2,
                    ),
                ),
                pixQrCodeData = PixQrCodeData(
                    type = PixQrCodeType.STATIC,
                    info = "info",
                    pixCopyAndPaste = PixCopyAndPaste("pixCopyAndPaste"),
                    pixId = "pixId",
                    fixedAmount = 1042799,
                    additionalInfo = mapOf("additionalInfoKey" to "additionalInfoValue"),
                    expiration = getZonedDateTime().plusHours(1),
                    originalAmount = 1042799,
                    automaticPixRecurringDataJson = null,
                ),
            ),
            totalAmount = 1042799,
            purpose = INVOICE_PURPOSE,
            billId = BillId("PIX_KEY_RECEIPT"),
            walletId = WalletId(WALLET_ID),
            payer = BillPayer(document = DOCUMENT, name = LARGE_PAYER_NAME, alias = null),
            source = ActionSource.Webapp(role = Role.OWNER),
            payeeFinancialInstitution = FinancialInstitution(name = "payeeBank", ispb = null, compe = 1234),
            payerFinancialInstitution = FinancialInstitution(name = "payerBank", ispb = null, compe = 4321),
            payeeRoutingNo = 1L,
            payeeAccountNo = BigInteger("12345"),
            payeeAccountDv = "1",
            payeeAccountType = AccountType.PAYMENT,
            payerBankAccount = InternalBankAccount(
                accountType = AccountType.CHECKING,
                bankNo = 0,
                routingNo = 0,
                accountNo = 0,
                accountDv = "1",
                document = "**********",
                bankAccountMode = BankAccountMode.PHYSICAL,
            ),
            walletName = null,
            scheduledBy = null,
            transactionId = TransactionId("transactionId"),
        )

        withPixParticipants(
            FinancialInstitution(name = "payee mock institution", ispb = "********", compe = null),
            FinancialInstitution(name = "payer mock institution", ispb = null, compe = payerBankAccount.bankNo),
        ) {
            receiptRepository.generateReceiptFiles(
                receiptData = receiptData,
                receiptHtml = receiptHandlebarCompiler.buildReceiptHtml(receiptData, wallet),
            )
        }
    }

    @Test
    fun `should generate pix receipt without pix key`() {
        val accountId = AccountId(ACCOUNT_ID)
        val payerBankAccount = (balance.method as InternalBankAccount)

        every {
            objectRepository.putObject(any(), any(), any())
        } just Runs

        val receiptData = PixReceiptData(
            authentication = "***********.***********.88.99.AA.BB.CC.DD.EE.FF",
            dateTime = getZonedDateTime(),
            recipient = Recipient(
                name = LARGE_RECIPIENT_NAME,
                document = DOCUMENT_2,
                alias = "alias",
                bankAccount = BankAccount(
                    accountNo = BigInteger("23468"),
                    bankNo = 1,
                    accountDv = "0",
                    ispb = "********",
                    accountType = AccountType.PAYMENT,
                    routingNo = 123,
                ),
                pixKeyDetails = null,
            ),
            purpose = INVOICE_PURPOSE,
            totalAmount = 1042799,
            billId = BillId("PIX_BANK_ACCOUNT_RECEIPT"),
            walletId = WalletId(WALLET_ID),
            payer = BillPayer(document = DOCUMENT, name = LARGE_PAYER_NAME, alias = null),
            source = ActionSource.Webapp(role = Role.OWNER),
            payeeFinancialInstitution = FinancialInstitution(name = "payeeBank", ispb = "********", compe = 1234),
            payerFinancialInstitution = FinancialInstitution(name = "payerBank", ispb = null, compe = 4321),
            payeeRoutingNo = 1L,
            payeeAccountNo = BigInteger("12345"),
            payeeAccountDv = "1",
            payeeAccountType = AccountType.PAYMENT,
            payerBankAccount = InternalBankAccount(
                accountType = AccountType.CHECKING,
                bankNo = 0,
                routingNo = 0,
                accountNo = 0,
                accountDv = "1",
                document = "**********",
                bankAccountMode = BankAccountMode.PHYSICAL,
            ),
            walletName = null,
            scheduledBy = null,
            transactionId = TransactionId("transactionId"),
        )

        withPixParticipants(
            FinancialInstitution(name = "payee mock institution", ispb = "********", compe = null),
            FinancialInstitution(name = "payer mock institution", ispb = null, compe = payerBankAccount.bankNo),
        ) {
            receiptRepository.generateReceiptFiles(
                receiptData = receiptData,
                receiptHtml = receiptHandlebarCompiler.buildReceiptHtml(receiptData, wallet),
            )
        }
    }

    @Test
    fun `should generate boleto receipt`() {
        every {
            objectRepository.putObject(any(), any(), any())
        } just Runs

        val receiptData = BoletoReceiptData(
            authentication = "***********.***********.88.99.AA.BB.CC.DD.EE.FF",
            dateTime = getZonedDateTime(),
            recipient = Recipient(
                name = LARGE_RECIPIENT_NAME,
                document = CNPJ_1,
                bankAccount = null,
                pixKeyDetails = null,
            ),
            totalAmount = 1042799,
            billId = BillId("FICHA_RECEIPT"),
            walletId = WalletId(WALLET_ID),
            payer = BillPayer(document = DOCUMENT, name = LARGE_PAYER_NAME, alias = null),
            dueDate = getLocalDate(),
            assignor = LARGE_ASSIGNOR,
            barcode = BarCode.ofDigitable("34191090080116587030201035380003586090000311015"),
            source = ActionSource.Webapp(role = Role.OWNER),
            walletName = null,
            scheduledBy = null,
            paymentPartnerName = "BANCO 756 / AGENCIA 3038",
            transactionId = TransactionId("transactionId"),
        )
        receiptRepository.generateReceiptFiles(
            receiptData = receiptData,
            receiptHtml = receiptHandlebarCompiler.buildReceiptHtml(receiptData, wallet),
        )
    }

    @Test
    fun `should generate boleto no recipient and payer data receipt`() {
        every {
            objectRepository.putObject(any(), any(), any())
        } just Runs

        val receiptData = BoletoReceiptData(
            authentication = "***********.***********.88.99.AA.BB.CC.DD.EE.FF",
            dateTime = getZonedDateTime(),
            recipient = null,
            totalAmount = 1042799,
            billId = BillId("FICHA_RECEIPT_NO_DATA"),
            walletId = WalletId(WALLET_ID),
            payer = WalletFixture().founder.toBillPayer(),
            dueDate = getLocalDate(),
            assignor = LARGE_ASSIGNOR,
            barcode = BarCode.ofDigitable("34191090080116587030201035380003586090000311015"),
            source = ActionSource.Webapp(role = Role.OWNER),
            walletName = null,
            scheduledBy = null,
            paymentPartnerName = null,
            transactionId = TransactionId("transactionId"),
        )
        receiptRepository.generateReceiptFiles(
            receiptData = receiptData,
            receiptHtml = receiptHandlebarCompiler.buildReceiptHtml(receiptData, wallet),
        )
    }

    @Test
    fun `should generate boleto only payer document receipt`() {
        every {
            objectRepository.putObject(any(), any(), any())
        } just Runs

        val receiptData = BoletoReceiptData(
            authentication = "***********.***********.88.99.AA.BB.CC.DD.EE.FF",
            dateTime = getZonedDateTime(),
            recipient = null,
            totalAmount = 1042799,
            billId = BillId("FICHA_RECEIPT_WITH_PAYER_DOCUMENT"),
            walletId = WalletId(WALLET_ID),
            payer = BillPayer(document = DOCUMENT, name = null, alias = null),
            dueDate = getLocalDate(),
            assignor = LARGE_ASSIGNOR,
            barcode = BarCode.ofDigitable("34191090080116587030201035380003586090000311015"),
            source = ActionSource.Webapp(role = Role.OWNER),
            walletName = null,
            scheduledBy = null,
            paymentPartnerName = null,
            transactionId = TransactionId("transactionId"),
        )
        receiptRepository.generateReceiptFiles(
            receiptData = receiptData,
            receiptHtml = receiptHandlebarCompiler.buildReceiptHtml(receiptData, wallet),
        )
    }

    @Test
    fun `should generate boleto concessionaria receipt`() {
        every {
            objectRepository.putObject(any(), any(), any())
        } just Runs

        val receiptData = BoletoReceiptData(
            authentication = "***********.***********.88.99.AA.BB.CC.DD.EE.FF",
            dateTime = getZonedDateTime(),
            recipient = null,
            totalAmount = 1042799,
            billId = BillId("CONCESSIONARIA_RECEIPT"),
            walletId = WalletId(WALLET_ID),
            payer = BillPayer(document = DOCUMENT, name = LARGE_PAYER_NAME, alias = null),
            dueDate = getLocalDate(),
            assignor = "CEG-CIA DISTRIBUIDORA GAS DEIXANDO GRANDE PARA OCUPAR DUAS LINHAS",
            barcode = BarCode.ofDigitable("836700000018373100560003000082975632291020200205"),
            source = ActionSource.Webapp(role = Role.OWNER),
            walletName = null,
            scheduledBy = null,
            paymentPartnerName = "BANCO 756 / AGENCIA 3038",
            transactionId = TransactionId("transactionId"),
        )

        receiptRepository.generateReceiptFiles(
            receiptData = receiptData,
            receiptHtml = receiptHandlebarCompiler.buildReceiptHtml(receiptData, wallet),
        )
    }

    @Test
    fun `should generate boleto concessionaria no recipient data receipt`() {
        every {
            objectRepository.putObject(any(), any(), any())
        } just Runs

        val receiptData = BoletoReceiptData(
            authentication = "***********.***********.88.99.AA.BB.CC.DD.EE.FF",
            dateTime = getZonedDateTime(),
            recipient = null,
            totalAmount = 1042799,
            billId = BillId("CONCESSIONARIA_RECEIPT_NO_DATA"),
            walletId = WalletId(WALLET_ID),
            payer = WalletFixture().founder.toBillPayer(),
            dueDate = getLocalDate(),
            assignor = "CEG-CIA DISTRIBUIDORA GAS DEIXANDO GRANDE PARA OCUPAR DUAS LINHAS",
            barcode = BarCode.ofDigitable("836700000018373100560003000082975632291020200205"),
            source = ActionSource.Webapp(role = Role.OWNER),
            walletName = null,
            scheduledBy = null,
            paymentPartnerName = null,
            transactionId = TransactionId("transactionId"),
        )

        receiptRepository.generateReceiptFiles(
            receiptData = receiptData,
            receiptHtml = receiptHandlebarCompiler.buildReceiptHtml(receiptData, wallet),
        )
    }
}