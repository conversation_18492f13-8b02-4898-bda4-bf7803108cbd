package ai.friday.billpayment.app.payment

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.ScheduledBillDBRepository
import ai.friday.billpayment.adapters.dynamodb.ScheduledBillDynamoDAO
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.integrations.ScheduledBillPaymentService
import ai.friday.billpayment.app.integrations.ScheduledBillRepository
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.createPaymentMethodsDetailWithBalance
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WALLET_ID_2
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.paymentMethodId
import ai.friday.billpayment.paymentMethodId2
import ai.friday.billpayment.scheduledBill
import ai.friday.billpayment.withEarlyAccess
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import java.time.LocalDate
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

internal class PaymentSchedulingEventServiceTest {
    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val enhancedClient = getDynamoDB()

    private val scheduledBillRepository: ScheduledBillRepository = spyk(ScheduledBillDBRepository(client = ScheduledBillDynamoDAO(cli = enhancedClient)))
    private val scheduledBillPaymentService: ScheduledBillPaymentService = mockk(relaxed = true)
    private val billEventPublisher: BillEventPublisher = mockk(relaxed = true)
    private val paymentSchedulingEventService =
        PaymentSchedulingEventService(
            scheduledBillRepository = scheduledBillRepository,
            scheduledBillPaymentService = scheduledBillPaymentService,
            billEventPublisher = billEventPublisher,
            updateBillService = mockk {
                every {
                    getBill(any(), any())
                } returns Bill.build(billAdded, billPaymentScheduled).right()
            },
            featureConfiguration = mockk<FeatureConfiguration> {
                every { updateScheduleOnAmountLowered } returns true
            },
            lockProvider = mockk(relaxed = true),

        )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
    }

    @Test
    fun `se não estiver agendado, não faz nada`() {
        val billId = BillId(BILL_ID_2)
        val walletId = WalletId(WALLET_ID_2)

        paymentSchedulingEventService.updateAmount(
            billId = billId,
            walletId = walletId,
            actionSource = ActionSource.System,
            updatedAmount = 200L,
            shouldProcessScheduledBills = true,
        )

        verify(exactly = 1) {
            scheduledBillRepository.findScheduledBillById(billId = billId)
        }

        verify(exactly = 0) {
            scheduledBillRepository.save(any<ScheduledBill>())
            scheduledBillPaymentService.process(any(), any())
            billEventPublisher.publish(any(), any())
        }
    }

    @ParameterizedTest
    @MethodSource("datesTodayOrBefore")
    fun `se estiver agendado e a data esperada de pagamento é hoje ou antes, deve atualizar o valor e reprocessar os agendamentos`(
        scheduledDate: LocalDate,
    ) {
        scheduledBillRepository.save(
            scheduledBill.copy(
                amount = 100L,
                scheduledDate = scheduledDate,
                paymentMethodsDetail = createPaymentMethodsDetailWithBalance(paymentMethodId2, 100L),
            ),
        )

        paymentSchedulingEventService.updateAmount(
            billId = scheduledBill.billId,
            walletId = scheduledBill.walletId,
            updatedAmount = 200L,
            actionSource = ActionSource.Api(accountId = ACCOUNT.accountId),
            shouldProcessScheduledBills = true,
        )

        val scheduledBills = scheduledBillRepository.findScheduledBillById(
            billId = scheduledBill.billId,
        )

        scheduledBills.size shouldBe 1

        with(scheduledBills.single()) {
            amount shouldBe 200L
            paymentMethodsDetail.netAmountTotal() shouldBe 200L
        }

        verify(exactly = 1) {
            scheduledBillPaymentService.process(
                walletId = scheduledBill.walletId,
                scheduledDate = scheduledDate,
            )
        }

        verify(exactly = 0) {
            billEventPublisher.publish(any(), any())
        }
    }

    @Test
    fun `se estiver agendado e a data esperado de pagamento é futura, deve atualizar o valor e não deve reprocessar os agendamentos`() {
        val scheduledDate = getLocalDate().plusDays(1)

        scheduledBillRepository.save(
            scheduledBill.copy(
                amount = 100L,
                scheduledDate = scheduledDate,
                paymentMethodsDetail = createPaymentMethodsDetailWithBalance(paymentMethodId2, 100L),
            ),
        )

        paymentSchedulingEventService.updateAmount(
            billId = scheduledBill.billId,
            walletId = scheduledBill.walletId,
            actionSource = ActionSource.Api(accountId = ACCOUNT.accountId),
            updatedAmount = 200L,
            shouldProcessScheduledBills = true,
        )

        val scheduledBills = scheduledBillRepository.findScheduledBillById(
            billId = scheduledBill.billId,
        )

        scheduledBills.size shouldBe 1

        with(scheduledBills.single()) {
            amount shouldBe 200L
            paymentMethodsDetail.netAmountTotal() shouldBe 200L
        }

        verify(exactly = 0) {
            scheduledBillPaymentService.process(
                walletId = any(),
                scheduledDate = any(),
            )
            billEventPublisher.publish(any(), any())
        }
    }

    @Test
    fun `se o valor foi alterado pelo sistema deve desagendar a conta e notificar o usuário`() {
        val scheduledDate = getLocalDate()

        scheduledBillRepository.save(
            scheduledBill.copy(
                amount = 100L,
                scheduledDate = scheduledDate,
                paymentMethodsDetail = createPaymentMethodsDetailWithBalance(paymentMethodId2, 100L),
            ),
        )

        paymentSchedulingEventService.updateAmount(
            billId = scheduledBill.billId,
            walletId = scheduledBill.walletId,
            actionSource = ActionSource.System,
            updatedAmount = 200L,
            shouldProcessScheduledBills = true,
        )

        val scheduledBills = scheduledBillRepository.findScheduledBillById(
            billId = scheduledBill.billId,
        )

        scheduledBills.size shouldBe 1

        with(scheduledBills.single()) {
            amount shouldBe 100L
            paymentMethodsDetail.netAmountTotal() shouldBe 100L
        }

        verify(exactly = 0) {
            scheduledBillPaymentService.process(
                walletId = any(),
                scheduledDate = any(),
            )
        }

        verify {
            billEventPublisher.publish(
                any(),
                withArg {
                    it.shouldBeTypeOf<BillPaymentScheduleCanceled>()
                    it.reason shouldBe ScheduleCanceledReason.BILL_AMOUNT_CHANGED
                },
            )
        }
    }

    @Test
    fun `se o valor foi alterado pelo usuário e o pagamento não é BALANCE deve desagendar a conta e notificar o usuário`() {
        val scheduledDate = getLocalDate()

        scheduledBillRepository.save(
            scheduledBill.copy(
                amount = 100L,
                scheduledDate = scheduledDate,
                paymentMethodsDetail = PaymentMethodsDetailWithCreditCard(
                    paymentMethodId = paymentMethodId,
                    netAmount = 100L,
                    feeAmount = 10L,
                    installments = 1,
                    calculationId = null,
                    fee = 4.0,
                ),
            ),
        )

        paymentSchedulingEventService.updateAmount(
            billId = scheduledBill.billId,
            walletId = scheduledBill.walletId,
            actionSource = ActionSource.Api(ACCOUNT.accountId),
            updatedAmount = 200L,
            shouldProcessScheduledBills = true,
        )

        val scheduledBills = scheduledBillRepository.findScheduledBillById(
            billId = scheduledBill.billId,
        )

        scheduledBills.size shouldBe 1

        with(scheduledBills.single()) {
            amount shouldBe 100L
            paymentMethodsDetail.netAmountTotal() shouldBe 100L
        }

        verify(exactly = 0) {
            scheduledBillPaymentService.process(
                walletId = any(),
                scheduledDate = any(),
            )
        }

        verify {
            billEventPublisher.publish(
                any(),
                withArg {
                    it.shouldBeTypeOf<BillPaymentScheduleCanceled>()
                    it.reason shouldBe ScheduleCanceledReason.BILL_AMOUNT_CHANGED
                },
            )
        }
    }

    @Test
    fun `se o valor foi alterado para baixo deve atualizar o agendamento`() {
        val scheduledDate = getLocalDate()

        scheduledBillRepository.save(
            scheduledBill.copy(
                amount = 200L,
                scheduledDate = scheduledDate,
                paymentMethodsDetail = createPaymentMethodsDetailWithBalance(paymentMethodId2, 200L),
            ),
        )

        withEarlyAccess(accountId = AccountId(scheduledBill.walletId.value)) {
            paymentSchedulingEventService.updateAmount(
                billId = scheduledBill.billId,
                walletId = scheduledBill.walletId,
                actionSource = ActionSource.System,
                updatedAmount = 100L,
                shouldProcessScheduledBills = true,
            )
        }

        val scheduledBills = scheduledBillRepository.findScheduledBillById(
            billId = scheduledBill.billId,
        )

        scheduledBills.size shouldBe 1

        with(scheduledBills.single()) {
            amount shouldBe 100L
            paymentMethodsDetail.netAmountTotal() shouldBe 100L
        }
        verify(exactly = 1) {
            scheduledBillPaymentService.process(
                walletId = scheduledBill.walletId,
                scheduledDate = scheduledDate,
            )
        }

        verify(exactly = 0) {
            billEventPublisher.publish(any(), any())
        }
    }

    @Test
    fun `se o valor foi alterado para zero deve cancelar o agendamento`() {
        val scheduledDate = getLocalDate()

        scheduledBillRepository.save(
            scheduledBill.copy(
                amount = 200L,
                scheduledDate = scheduledDate,
                paymentMethodsDetail = createPaymentMethodsDetailWithBalance(paymentMethodId2, 200L),
            ),
        )

        withEarlyAccess(accountId = AccountId(scheduledBill.walletId.value)) {
            paymentSchedulingEventService.updateAmount(
                billId = scheduledBill.billId,
                walletId = scheduledBill.walletId,
                actionSource = ActionSource.System,
                updatedAmount = 0L,
                shouldProcessScheduledBills = true,
            )
        }

        val scheduledBills = scheduledBillRepository.findScheduledBillById(
            billId = scheduledBill.billId,
        )

        scheduledBills.size shouldBe 1
        verify(exactly = 0) {
            scheduledBillPaymentService.process(
                walletId = any(),
                scheduledDate = any(),
            )
        }

        verify {
            billEventPublisher.publish(
                any(),
                withArg {
                    it.shouldBeTypeOf<BillPaymentScheduleCanceled>()
                    it.reason shouldBe ScheduleCanceledReason.BILL_AMOUNT_CHANGED
                },
            )
        }
    }

    @Test
    fun `se o valor foi alterado pelo usuário e o pagamento é BALANCE não deve desagendar nem notificar o usuário`() {
        val scheduledDate = getLocalDate()

        scheduledBillRepository.save(
            scheduledBill.copy(
                amount = 100L,
                scheduledDate = scheduledDate,
                paymentMethodsDetail = PaymentMethodsDetailWithBalance(
                    paymentMethodId = paymentMethodId,
                    amount = 100L,
                    calculationId = null,
                ),
            ),
        )

        paymentSchedulingEventService.updateAmount(
            billId = scheduledBill.billId,
            walletId = scheduledBill.walletId,
            actionSource = ActionSource.Api(ACCOUNT.accountId),
            updatedAmount = 200L,
            shouldProcessScheduledBills = true,
        )

        val scheduledBills = scheduledBillRepository.findScheduledBillById(
            billId = scheduledBill.billId,
        )

        scheduledBills.size shouldBe 1

        with(scheduledBills.single()) {
            amount shouldBe 200L
            paymentMethodsDetail.netAmountTotal() shouldBe 200L
        }

        verify(exactly = 1) {
            scheduledBillPaymentService.process(
                walletId = scheduledBill.walletId,
                scheduledDate = scheduledDate,
            )
        }

        verify(exactly = 0) {
            billEventPublisher.publish(
                any(),
                any(),
            )
        }
    }

    companion object {
        @JvmStatic
        fun datesTodayOrBefore(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(getLocalDate()),
                Arguments.of(getLocalDate().minusDays(1)),
                Arguments.of(getLocalDate().minusMonths(3)),
            )
        }
    }
}