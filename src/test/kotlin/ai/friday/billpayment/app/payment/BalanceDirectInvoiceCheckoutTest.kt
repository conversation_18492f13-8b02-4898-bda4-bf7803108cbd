package ai.friday.billpayment.app.payment

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.integrations.TEDService
import ai.friday.billpayment.createSinglePaymentDataWithBalance
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.internalBankAccount
import ai.friday.billpayment.invoiceBill
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.mockk.every
import io.mockk.mockk
import java.util.UUID
import java.util.stream.Stream
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class BalanceDirectInvoiceCheckoutTest {
    private val tedServiceMock: TEDService = mockk()
    private val balanceDirectInvoiceCheckout = BalanceDirectInvoiceCheckout(tedServiceMock)

    @Test
    fun `should fail transaction on insufficient funds`() {
        every {
            tedServiceMock.transfer(any(), any(), any())
        } returns DirectTEDResult(
            gateway = FinancialServiceGateway.ARBI,
            amount = 10,
            status = DirectTEDStatus.Failure.INSUFFICIENT_FUNDS,
            settleDate = null,
            operationId = BankOperationId("1"),
            authentication = null,
        )

        val transaction = buildTransaction()
        val result = balanceDirectInvoiceCheckout.execute(transaction)

        assertSoftly {
            result.status shouldBe TransactionStatus.FAILED
            transaction.settlementData.getOperation<BankTransfer>().status shouldBe BankOperationStatus.INSUFFICIENT_FUNDS
        }
    }

    @Test
    fun `should fail transaction on after hours`() {
        every {
            tedServiceMock.transfer(any(), any(), any())
        } returns DirectTEDResult(
            gateway = FinancialServiceGateway.ARBI,
            amount = 10,
            status = DirectTEDStatus.Failure.AFTER_HOURS,
            settleDate = null,
            operationId = BankOperationId("1"),
            authentication = null,
        )

        val transaction = buildTransaction()
        val result = balanceDirectInvoiceCheckout.execute(transaction)

        assertSoftly {
            result.status shouldBe TransactionStatus.FAILED
            transaction.settlementData.getOperation<BankTransfer>().status shouldBe BankOperationStatus.ERROR
        }
    }

    @Test
    fun `should fail transaction on invalid bank data`() {
        every {
            tedServiceMock.transfer(any(), any(), any())
        } returns DirectTEDResult(
            gateway = FinancialServiceGateway.ARBI,
            amount = 10,
            status = DirectTEDStatus.Failure.INVALID_BANK_DATA,
            settleDate = null,
            operationId = BankOperationId("1"),
            authentication = null,
        )

        val transaction = buildTransaction()
        val result = balanceDirectInvoiceCheckout.execute(transaction)

        assertSoftly {
            result.status shouldBe TransactionStatus.FAILED
            transaction.settlementData.getOperation<BankTransfer>().status shouldBe BankOperationStatus.INVALID_DATA
        }
    }

    @Test
    fun `transaction should be processing on unknown error`() {
        every {
            tedServiceMock.transfer(any(), any(), any())
        } returns DirectTEDResult(
            gateway = FinancialServiceGateway.ARBI,
            amount = 10,
            status = DirectTEDStatus.Error,
            settleDate = null,
            operationId = BankOperationId("1"),
            authentication = null,
        )

        val transaction = buildTransaction()
        val result = balanceDirectInvoiceCheckout.execute(transaction)

        assertSoftly {
            result.status shouldBe TransactionStatus.PROCESSING
            transaction.settlementData.getOperation<BankTransfer>().status shouldBe BankOperationStatus.ERROR
        }
    }

    @Test
    fun `transaction should be processing on timeout`() {
        every {
            tedServiceMock.transfer(any(), any(), any())
        } returns DirectTEDResult(
            gateway = FinancialServiceGateway.ARBI,
            amount = 10,
            status = DirectTEDStatus.Unknown,
            settleDate = null,
            operationId = BankOperationId("1"),
            authentication = null,
        )

        val transaction = buildTransaction()
        val result = balanceDirectInvoiceCheckout.execute(transaction)

        assertSoftly {
            result.status shouldBe TransactionStatus.PROCESSING
            transaction.settlementData.getOperation<BankTransfer>().status shouldBe BankOperationStatus.UNKNOWN
        }
    }

    @Test
    fun `transaction status should be completed when transfer operation is a success`() {
        every {
            tedServiceMock.transfer(any(), any(), any())
        } returns DirectTEDResult(
            gateway = FinancialServiceGateway.ARBI,
            amount = 10,
            status = DirectTEDStatus.Success,
            settleDate = getLocalDate(),
            operationId = BankOperationId("1"),
            authentication = "123",
        )

        val transaction = buildTransaction()
        val result = balanceDirectInvoiceCheckout.execute(transaction)

        assertSoftly {
            result.status shouldBe TransactionStatus.COMPLETED
            transaction.settlementData.getOperation<BankTransfer>().status shouldBe BankOperationStatus.SUCCESS
        }
    }

    @ParameterizedTest
    @MethodSource("settlemnetStatusWithoutSuccess")
    fun `should change checkSettlementStatus result to Error when it is different of Success and transaction was created less than one hour ago`(
        settlementStatus: SettlementStatus,
    ) {
        every {
            tedServiceMock.checkSettlementStatus(any())
        } returns settlementStatus

        val transaction = buildTransaction()

        withGivenDateTime(getZonedDateTime().plusMinutes(60)) {
            balanceDirectInvoiceCheckout.checkSettlementStatus(transaction) shouldBe SettlementStatus.Error
        }
    }

    @ParameterizedTest
    @MethodSource("settlemnetStatusWithoutSuccess")
    fun `should change checkSettlementStatus result when it is different of Success and transaction was created over one hour ago`(
        settlementStatus: SettlementStatus,
    ) {
        every {
            tedServiceMock.checkSettlementStatus(any())
        } returns settlementStatus

        val transaction = buildTransaction()

        withGivenDateTime(getZonedDateTime().plusMinutes(61)) {
            balanceDirectInvoiceCheckout.checkSettlementStatus(transaction) shouldBe SettlementStatus.Error
        }
    }

    @Test
    fun `should return Success when checkSettlementStatus is Success and transaction was created less than one hour ago`() {
        every {
            tedServiceMock.checkSettlementStatus(any())
        } returns SettlementStatus.Success("")

        val transaction = buildTransaction()

        withGivenDateTime(getZonedDateTime().plusMinutes(60)) {
            balanceDirectInvoiceCheckout.checkSettlementStatus(transaction)
                .shouldBeInstanceOf<SettlementStatus.Success>()
        }
    }

    @Test
    fun `should return Success when checkSettlementStatus is Success and transaction was created over one hour ago`() {
        every {
            tedServiceMock.checkSettlementStatus(any())
        } returns SettlementStatus.Success("")

        val transaction = buildTransaction()

        withGivenDateTime(getZonedDateTime().plusMinutes(61)) {
            balanceDirectInvoiceCheckout.checkSettlementStatus(transaction)
                .shouldBeInstanceOf<SettlementStatus.Success>()
        }
    }

    private fun buildTransaction(): Transaction {
        val balance = AccountPaymentMethod(
            id = AccountPaymentMethodId(UUID.randomUUID().toString()),
            status = AccountPaymentMethodStatus.ACTIVE,
            method = internalBankAccount,
            accountId = AccountId(ACCOUNT_ID),
        )
        return Transaction(
            type = TransactionType.INVOICE_PAYMENT,
            payer = ACCOUNT.toPayer(),
            paymentData = createSinglePaymentDataWithBalance(balance, invoiceBill.amountTotal),
            settlementData = SettlementData(invoiceBill, 0, invoiceBill.amountTotal, null),
            nsu = 1,
            actionSource = ActionSource.System,
            walletId = invoiceBill.walletId,
        )
    }

    companion object {
        @JvmStatic
        fun settlemnetStatusWithoutSuccess(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(SettlementStatus.Failure("")),
                Arguments.of(SettlementStatus.InvalidData("")),
                Arguments.of(SettlementStatus.Error),
            )
        }
    }
}