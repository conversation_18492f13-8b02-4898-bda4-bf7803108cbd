package ai.friday.billpayment.app.payment.checkout

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.CreditCardPaymentStatus
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.cashIn.FraudPreventionErrors
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.integrations.CheckableSettlementStatus
import ai.friday.billpayment.app.payment.BalanceAuthorization
import ai.friday.billpayment.app.payment.BillPaymentResponse
import ai.friday.billpayment.app.payment.BoletoSettlementException
import ai.friday.billpayment.app.payment.BoletoSettlementResult
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.app.payment.FraudPreventionPaymentOperationDenied
import ai.friday.billpayment.app.payment.PaymentNotCanceled
import ai.friday.billpayment.app.payment.PaymentOperation
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.SettlementStatus
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.captureFunds.CaptureFundsLocator
import ai.friday.billpayment.app.payment.toPayer
import ai.friday.billpayment.boletoSettlementResult
import ai.friday.billpayment.boletoTransaction
import ai.friday.billpayment.buildCreditCardAuthorization
import ai.friday.billpayment.concessionariaBill
import ai.friday.billpayment.createSinglePaymentDataWithBalance
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.internalBankAccount
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.mockk.called
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class SyncBoletoCheckoutTest {

    private val boletoSettlementService: BoletoSettlementService = mockk()

    private val captureFundsLocator = mockk<CaptureFundsLocator>()
    private val checkout = SyncBoletoCheckout(
        captureFundsLocator = captureFundsLocator,
        boletoSettlementService = boletoSettlementService,
    )

    private val firstBankTransactionId = "1"
    private val secondBankTransactionId = "11"

    fun paymentData(amount: Long) = createSinglePaymentDataWithBalance(
        AccountPaymentMethod(
            id = AccountPaymentMethodId(UUID.randomUUID().toString()),
            status = AccountPaymentMethodStatus.ACTIVE,
            method = internalBankAccount,
            accountId = AccountId(ACCOUNT_ID),
        ),
        amount,
    )

    lateinit var transaction: Transaction

    @BeforeEach
    fun setup() {
        clearMocks(captureFundsLocator, boletoSettlementService)
        every { captureFundsLocator.checkOnFraudPrevention(any()) } returns null
        every {
            boletoSettlementService.initPayment(
                ofType(Bill::class),
                ofType(Int::class),
                ofType(String::class),
                ofType(String::class),
                any(),
            )
        } answers {
            BillPaymentResponse(
                status = BoletoSettlementStatus.AUTHORIZED,
                transactionId = secondBankTransactionId.toLong(),
            )
        }
        every {
            boletoSettlementService.confirmPayment(
                ofType(String::class),
                ofType(Int::class),
                ofType(String::class),
            )
        } just runs
        every {
            boletoSettlementService.cancelPayment(
                ofType(String::class),
                ofType(Int::class),
                ofType(String::class),
            )
        } just runs
        every {
            captureFundsLocator.undoCaptureFunds(any())
        } answers {
            BalanceAuthorization(
                status = BankOperationStatus.SUCCESS,
                amount = concessionariaBill.amountTotal,
                paymentGateway = FinancialServiceGateway.ARBI,
            )
        }
        every {
            boletoSettlementService.queryPayment(any())
        } returns BoletoSettlementStatus.AUTHORIZED

        every {
            boletoSettlementService.queryPayment("0")
        } throws BoletoSettlementException(FinancialServiceGateway.CELCOIN, null, NoStackTraceException())

        transaction = Transaction(
            type = TransactionType.BOLETO_PAYMENT,
            payer = ACCOUNT.toPayer(),
            paymentData = paymentData(concessionariaBill.amountTotal),
            settlementData = SettlementData(
                concessionariaBill,
                0,
                concessionariaBill.amountTotal,
                BoletoSettlementResult(
                    FinancialServiceGateway.CELCOIN,
                    BoletoSettlementStatus.AUTHORIZED,
                    "1",
                    1,
                    ACCOUNT_ID,
                    "",
                    "",
                ),
            ),
            nsu = 1,
            actionSource = ActionSource.System,
            walletId = concessionariaBill.walletId,
        )
        transaction.settlementData.getOperation<BoletoSettlementResult>().bankTransactionId = firstBankTransactionId
    }

    @ParameterizedTest
    @MethodSource("errorPaymentOperations")
    fun `deve falhar o pagamento quando não consegue capturar o pagamento`(paymentOperation: PaymentOperation) {
        every { captureFundsLocator.captureFunds(transaction) } returns paymentOperation

        checkout.execute(transaction = transaction)

        verify {
            boletoSettlementService.cancelPayment(ofType(String::class), ofType(Int::class), ofType(String::class))
        }
        assertEquals(TransactionStatus.FAILED, transaction.status)
        assertEquals(
            BoletoSettlementStatus.VOIDED,
            (transaction.settlementData.settlementOperation as BoletoSettlementResult).status,
        )
    }

    @ParameterizedTest
    @MethodSource("successPaymentOperations")
    fun `should pay bill`(paymentOperation: PaymentOperation) {
        every {
            captureFundsLocator.captureFunds(any())
        } returns paymentOperation

        checkout.execute(transaction = transaction)
        verify {
            boletoSettlementService.initPayment(
                bill = ofType(Bill::class),
                nsu = ofType(Int::class),
                transactionId = firstBankTransactionId,
                payerDocument = transaction.payer.document,
                payerName = boletoTransaction.payer.name,
            )
            captureFundsLocator.captureFunds(transaction)
            boletoSettlementService.confirmPayment(ofType(String::class), ofType(Int::class), "11")
        }
        assertEquals(TransactionStatus.COMPLETED, transaction.status)
        assertEquals(
            BoletoSettlementStatus.CONFIRMED,
            (transaction.settlementData.settlementOperation as BoletoSettlementResult).status,
        )
    }

    @Test
    fun `should cancel and void when settlement response is unknown and responseTransactionId is known`() {
        transaction.settlementData.getOperation<BoletoSettlementResult>().bankTransactionId = firstBankTransactionId

        every {
            boletoSettlementService.initPayment(
                ofType(Bill::class),
                ofType(Int::class),
                ofType(String::class),
                ofType(String::class),
                any(),
            )
        } answers {
            BillPaymentResponse(
                status = BoletoSettlementStatus.UNKNOWN,
                transactionId = 2,
                message = "Não foi possível completar a operação. Por favor, tente novamente",
            )
        }

        checkout.execute(transaction = transaction)

        verify {
            boletoSettlementService.initPayment(
                ofType(Bill::class),
                ofType(Int::class),
                firstBankTransactionId,
                ofType(String::class),
                transaction.payer.name,
            )
            boletoSettlementService.cancelPayment(any(), any(), any())
        }

        verify(exactly = 0) {
            captureFundsLocator.captureFunds(transaction)
            boletoSettlementService.confirmPayment(ofType(String::class), ofType(Int::class), any())
        }

        assertEquals(TransactionStatus.FAILED, transaction.status)
        transaction.paymentData.toSingle().payment.shouldBeNull()
        assertEquals(
            BoletoSettlementStatus.VOIDED,
            (transaction.settlementData.settlementOperation as BoletoSettlementResult).status,
        )
    }

    @Test
    fun `should cancel and not void when settlement response and responseTransactionId are unknown`() {
        transaction.settlementData.getOperation<BoletoSettlementResult>().bankTransactionId = firstBankTransactionId

        every {
            boletoSettlementService.initPayment(
                ofType(Bill::class),
                ofType(Int::class),
                ofType(String::class),
                ofType(String::class),
                any(),
            )
        } answers {
            BillPaymentResponse(
                status = BoletoSettlementStatus.UNKNOWN,
                transactionId = 0,
                message = "Não foi possível completar a operação. Por favor, tente novamente",
            )
        }

        every {
            boletoSettlementService.queryPayment("0")
        } returns BoletoSettlementStatus.UNKNOWN

        checkout.execute(transaction = transaction)

        verify {
            boletoSettlementService.initPayment(
                ofType(Bill::class),
                ofType(Int::class),
                firstBankTransactionId,
                ofType(String::class),
                transaction.payer.name,
            )
            boletoSettlementService.queryPayment(any())
            boletoSettlementService.cancelPayment(any(), any(), any())
        }

        verify(exactly = 0) {
            captureFundsLocator.captureFunds(transaction)
            boletoSettlementService.confirmPayment(ofType(String::class), ofType(Int::class), any())
        }

        transaction.status shouldBe TransactionStatus.FAILED
        transaction.paymentData.toSingle().payment.shouldBeNull()
        (transaction.settlementData.settlementOperation as BoletoSettlementResult).status shouldBe BoletoSettlementStatus.VOIDED
    }

    @ParameterizedTest
    @MethodSource("unknownPaymentOperations")
    fun `should retry when payment response is unknown`(paymentOperation: PaymentOperation) {
        transaction.settlementData.getOperation<BoletoSettlementResult>().bankTransactionId = firstBankTransactionId

        every {
            captureFundsLocator.captureFunds(transaction)
        } returns paymentOperation

        checkout.execute(transaction = transaction)

        verify {
            boletoSettlementService.initPayment(
                ofType(Bill::class),
                ofType(Int::class),
                firstBankTransactionId,
                ofType(String::class),
                transaction.payer.name,
            )
        }

        verify(exactly = 0) {
            boletoSettlementService.confirmPayment(ofType(String::class), ofType(Int::class), any())
        }

        assertEquals(TransactionStatus.PROCESSING, transaction.status)
        assertEquals(
            BoletoSettlementStatus.AUTHORIZED,
            (transaction.settlementData.settlementOperation as BoletoSettlementResult).status,
        )
    }

    @ParameterizedTest
    @MethodSource("errorPaymentOperations")
    fun `should rollback transaction when transfer funds fails and cancel payment fails`(paymentOperation: PaymentOperation) {
        every {
            captureFundsLocator.captureFunds(transaction)
        } returns paymentOperation

        every {
            boletoSettlementService.cancelPayment(
                ofType(String::class),
                ofType(Int::class),
                ofType(String::class),
            )
        } throws BoletoSettlementException(FinancialServiceGateway.CELCOIN) andThenAnswer { nothing }

        checkout.execute(transaction = transaction)

        verify {
            captureFundsLocator.captureFunds(transaction)
        }
        verify(exactly = 2) {
            boletoSettlementService.cancelPayment(ofType(String::class), ofType(Int::class), ofType(String::class))
        }
        assertEquals(TransactionStatus.FAILED, transaction.status)
        assertEquals(
            BoletoSettlementStatus.VOIDED,
            (transaction.settlementData.settlementOperation as BoletoSettlementResult).status,
        )
    }

    @ParameterizedTest
    @MethodSource("successPaymentOperations")
    fun `should rollback transaction when transfer funds fails and capture payment fails`(paymentOperation: PaymentOperation) {
        every {
            captureFundsLocator.captureFunds(any())
        } returns paymentOperation
        every {
            boletoSettlementService.confirmPayment(
                ofType(String::class),
                ofType(Int::class),
                ofType(String::class),
            )
        } throws BoletoSettlementException(FinancialServiceGateway.CELCOIN)
        every {
            boletoSettlementService.cancelPayment(
                ofType(String::class),
                ofType(Int::class),
                ofType(String::class),
            )
        } just runs

        checkout.execute(transaction = transaction)
        verify {
            captureFundsLocator.captureFunds(transaction)
            boletoSettlementService.confirmPayment(
                transaction.payer.accountId.value,
                transaction.nsu.toInt(),
                secondBankTransactionId,
            )
            boletoSettlementService.cancelPayment(
                transaction.payer.accountId.value,
                transaction.nsu.toInt(),
                secondBankTransactionId,
            )
            captureFundsLocator.undoCaptureFunds(transaction)
        }
        assertEquals(TransactionStatus.FAILED, transaction.status)
        assertEquals(
            BoletoSettlementStatus.VOIDED,
            (transaction.settlementData.settlementOperation as BoletoSettlementResult).status,
        )
    }

    @Test
    fun `should not rollback a transaction with settlement status CONFIRMED`() {
        every {
            boletoSettlementService.queryPayment(any())
        } returns BoletoSettlementStatus.CONFIRMED

        val originalTransaction = Transaction(
            type = TransactionType.BOLETO_PAYMENT,
            payer = ACCOUNT.toPayer(),
            paymentData = paymentData(
                concessionariaBill.amountTotal,
            ),
            settlementData = SettlementData(
                concessionariaBill,
                0,
                concessionariaBill.amountTotal,
                boletoSettlementResult.copy(status = BoletoSettlementStatus.AUTHORIZED),
            ),
            nsu = 1,
            actionSource = ActionSource.System,
            walletId = concessionariaBill.walletId,
            status = TransactionStatus.PROCESSING,
        )

        val transaction =
            checkout.rollbackTransaction(transaction = originalTransaction)

        transaction.status shouldBe TransactionStatus.PROCESSING

        verify {
            captureFundsLocator wasNot called
        }
    }

    @ParameterizedTest
    @MethodSource("successPaymentOperations")
    fun `should not rollback transaction when transfer refund fails`(paymentOperation: PaymentOperation) {
        every {
            captureFundsLocator.captureFunds(any())
        } returns paymentOperation
        every {
            boletoSettlementService.confirmPayment(
                ofType(String::class),
                ofType(Int::class),
                ofType(String::class),
            )
        } throws BoletoSettlementException(FinancialServiceGateway.CELCOIN)
        every {
            boletoSettlementService.cancelPayment(
                ofType(String::class),
                ofType(Int::class),
                ofType(String::class),
            )
        } just runs

        every {
            captureFundsLocator.undoCaptureFunds(any())
        } throws PaymentNotCanceled("", "")

        val transaction = transaction.copy(
            paymentData = paymentData(
                transaction.settlementData.getTarget<Bill>().amountTotal,
            ),
        )

        checkout.execute(transaction = transaction)
        verify {
            captureFundsLocator.captureFunds(transaction)
            boletoSettlementService.confirmPayment(
                transaction.payer.accountId.value,
                transaction.nsu.toInt(),
                secondBankTransactionId,
            )
            boletoSettlementService.cancelPayment(
                transaction.payer.accountId.value,
                transaction.nsu.toInt(),
                secondBankTransactionId,
            )
        }

        assertEquals(TransactionStatus.PROCESSING, transaction.status)
        assertEquals(
            BoletoSettlementStatus.VOIDED,
            (transaction.settlementData.settlementOperation as BoletoSettlementResult).status,
        )
    }

    @Test
    fun `deve falhar a transação se não passar no antifraude`() {
        val transaction = transaction.copy(
            paymentData = paymentData(
                transaction.settlementData.getTarget<Bill>().amountTotal,
            ),
        )

        every {
            captureFundsLocator.checkOnFraudPrevention(transaction)
        } returns FraudPreventionPaymentOperationDenied(FraudPreventionErrors.NO_LIMIT_AVAILABLE)

        checkout.execute(transaction = transaction)

        verify(exactly = 0) {
            boletoSettlementService.initPayment(
                bill = any(),
                nsu = any(),
                transactionId = any(),
                payerDocument = any(),
                payerName = any(),
            )
            captureFundsLocator.captureFunds(transaction)
            boletoSettlementService.cancelPayment(any(), any(), any())
            boletoSettlementService.confirmPayment(any(), any(), any())
        }

        transaction.status shouldBe TransactionStatus.FAILED
        (transaction.settlementData.settlementOperation as BoletoSettlementResult).status shouldBe BoletoSettlementStatus.AUTHORIZED
    }

    @ParameterizedTest
    @MethodSource("settlementStatusMap")
    fun `should map boleto settlement statuses`(
        boletoSettlementStatus: BoletoSettlementStatus,
        settlementStatus: SettlementStatus,
    ) {
        every {
            boletoSettlementService.queryPayment(any())
        } returns boletoSettlementStatus

        val status = (checkout as CheckableSettlementStatus).checkSettlementStatus(transaction)

        status::class shouldBe settlementStatus::class
    }

    companion object {
        @JvmStatic
        fun settlementStatusMap(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(BoletoSettlementStatus.CONFIRMED, SettlementStatus.Success()),
                Arguments.arguments(BoletoSettlementStatus.VOIDED, SettlementStatus.Failure("voided")),
                Arguments.arguments(BoletoSettlementStatus.UNAUTHORIZED, SettlementStatus.Failure("unauthorized")),
                Arguments.arguments(BoletoSettlementStatus.AUTHORIZED, SettlementStatus.Failure("authorized")),
                Arguments.arguments(BoletoSettlementStatus.UNKNOWN, SettlementStatus.Failure("unkown")),
            )
        }

        @JvmStatic
        fun errorPaymentOperations(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    BalanceAuthorization(
                        status = BankOperationStatus.ERROR,
                        amount = concessionariaBill.amountTotal,
                        paymentGateway = FinancialServiceGateway.ARBI,
                        operationId = BankOperationId("1"),
                        errorDescription = "error",
                        timeout = false,
                    ),
                ),
                Arguments.arguments(
                    BalanceAuthorization(
                        operationId = BankOperationId("asd"),
                        status = BankOperationStatus.INSUFFICIENT_FUNDS,
                        amount = concessionariaBill.amountTotal,
                        paymentGateway = FinancialServiceGateway.ARBI,
                    ),
                ),
                Arguments.arguments(
                    buildCreditCardAuthorization(
                        status = CreditCardPaymentStatus.DENIED,
                        amount = 1L,
                    ),
                ),
            )
        }

        @JvmStatic
        fun successPaymentOperations(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    BalanceAuthorization(
                        status = BankOperationStatus.SUCCESS,
                        amount = concessionariaBill.amountTotal,
                        paymentGateway = FinancialServiceGateway.ARBI,
                    ),
                ),
                Arguments.arguments(
                    buildCreditCardAuthorization(
                        status = CreditCardPaymentStatus.PAYMENT_CONFIRMED,
                        amount = 1L,
                    ),
                ),
            )
        }

        @JvmStatic
        fun unknownPaymentOperations(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    BalanceAuthorization(
                        status = BankOperationStatus.UNKNOWN,
                        amount = concessionariaBill.amountTotal,
                        paymentGateway = FinancialServiceGateway.ARBI,
                        operationId = BankOperationId("1"),
                        errorDescription = "error",
                        timeout = true,
                    ),
                ),
                Arguments.arguments(
                    buildCreditCardAuthorization(
                        status = CreditCardPaymentStatus.ABORTED,
                        amount = 1L,
                    ),
                ),
            )
        }
    }
}