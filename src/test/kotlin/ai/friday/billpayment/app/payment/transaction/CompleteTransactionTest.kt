package ai.friday.billpayment.app.payment.transaction

import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillInstrumentationService
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.receipt.NotifyReceiptService
import ai.friday.billpayment.boletoTransaction
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

internal class CompleteTransactionTest {

    val balanceService: BalanceService = mockk(relaxUnitFun = true)

    val billInstrumentationService = mockk<BillInstrumentationService>(relaxUnitFun = true)

    val billEventPublisher = mockk<BillEventPublisher>(relaxUnitFun = true)

    private val notifyReceiptService = mockk<NotifyReceiptService>()
    private val completeTransaction = CompleteTransaction(
        balanceService = balanceService,
        billInstrumentationService = billInstrumentationService,
        billEventPublisher = billEventPublisher,
        notifyReceiptService = notifyReceiptService,
        onboardingInstrumentationService = mockk(relaxed = true),
    )

    val transaction = boletoTransaction.copy(status = TransactionStatus.COMPLETED)

    @Test
    fun `deve executar os passos apos a transacao ser concluida`() {
        every {
            notifyReceiptService.notifyWithAsyncRetry(any())
        } just Runs

        completeTransaction.execute(transaction = transaction)

        val slot = mutableListOf<BillEvent>()
        verify {
            balanceService.invalidate(transaction.paymentData.toSingle().accountPaymentMethod.id)
            billInstrumentationService.paid(transaction.settlementData.getTarget())
            billEventPublisher.publish(transaction.settlementData.getTarget(), capture(slot))
            notifyReceiptService.notifyWithAsyncRetry(any())
        }

        with(slot[0]) {
            shouldBeTypeOf<BillPaid>()
            billId shouldBe transaction.settlementData.getTarget<Bill>().billId
            walletId shouldBe transaction.settlementData.getTarget<Bill>().walletId
            actionSource shouldBe transaction.actionSource
            transactionId shouldBe transaction.id
            pixKeyDetails.shouldBeNull()
        }

        with(slot[1]) {
            shouldBeTypeOf<BillPaymentScheduleCanceled>()
            billId shouldBe transaction.settlementData.getTarget<Bill>().billId
            walletId shouldBe transaction.settlementData.getTarget<Bill>().walletId
            actionSource shouldBe transaction.actionSource
            reason shouldBe ScheduleCanceledReason.EXECUTED
        }
    }

    @Test
    fun `deve executar os passos apos a transacao PIX ser concluida`() {
        every {
            notifyReceiptService.notifyWithAsyncRetry(any())
        } just Runs

        transaction.apply {
            settlementData.settlementOperation = BankTransfer(
                operationId = BankOperationId("abc"),
                gateway = FinancialServiceGateway.ARBI,
                amount = transaction.settlementData.totalAmount,
                status = BankOperationStatus.SUCCESS,
                errorDescription = "000",
                authentication = "123",
            )
        }

        completeTransaction.execute(transaction = transaction)

        val slot = mutableListOf<BillEvent>()
        verify {
            balanceService.invalidate(transaction.paymentData.toSingle().accountPaymentMethod.id)
            billInstrumentationService.paid(transaction.settlementData.getTarget())
            billEventPublisher.publish(transaction.settlementData.getTarget(), capture(slot))
            notifyReceiptService.notifyWithAsyncRetry(any())
        }

        with(slot[0]) {
            shouldBeTypeOf<BillPaid>()
            billId shouldBe transaction.settlementData.getTarget<Bill>().billId
            walletId shouldBe transaction.settlementData.getTarget<Bill>().walletId
            actionSource shouldBe transaction.actionSource
            transactionId shouldBe transaction.id
            pixKeyDetails.shouldBeNull()
        }

        with(slot[1]) {
            shouldBeTypeOf<BillPaymentScheduleCanceled>()
            billId shouldBe transaction.settlementData.getTarget<Bill>().billId
            walletId shouldBe transaction.settlementData.getTarget<Bill>().walletId
            actionSource shouldBe transaction.actionSource
            reason shouldBe ScheduleCanceledReason.EXECUTED
        }
    }
}