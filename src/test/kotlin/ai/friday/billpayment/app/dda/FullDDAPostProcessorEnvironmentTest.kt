package ai.friday.billpayment.app.dda

import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.mockk
import org.junit.jupiter.api.Test

@MicronautTest(environments = [FRIDAY_ENV])
class FullDDAPostProcessorEnvironmentTest(private val postProcessor: FullDDAPostProcessor) {
    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @Test
    fun `should not throw exception`() {
        postProcessor.process(
            DDARegister(
                accountId = AccountId(ACCOUNT_ID),
                document = "***********",
                created = getZonedDateTime(),
                status = DDAStatus.ACTIVE,
                lastUpdated = getZonedDateTime(),
                lastSuccessfullExecution = getZonedDateTime(),
            ),
        )
    }
}