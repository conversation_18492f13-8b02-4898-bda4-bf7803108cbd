package ai.friday.billpayment.app.dda

import DynamoDBUtils
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.arbi.ArbiDDAPayerNotFoundException
import ai.friday.billpayment.adapters.arbi.ArbiValidationResponse
import ai.friday.billpayment.adapters.dynamodb.DDABillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DDADbRepository
import ai.friday.billpayment.adapters.dynamodb.DDADynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.CreditCardConfiguration
import ai.friday.billpayment.app.account.LegacyAccountConfiguration
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillSynchronizationStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateFichaDeCompensacaoRequest
import ai.friday.billpayment.app.bill.FichaCompensacaoService
import ai.friday.billpayment.app.bill.SynchronizeBillResponse
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.chatbot.OpenFinanceIncentiveService
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BillValidationService
import ai.friday.billpayment.app.integrations.DDABatchResult
import ai.friday.billpayment.app.integrations.DDAProviderService
import ai.friday.billpayment.app.integrations.DDARepository
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.WalletStatus
import ai.friday.billpayment.billRegisterData
import ai.friday.billpayment.createDDAConfig
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.BILL_ID_7
import ai.friday.billpayment.integration.CNPJ_1
import ai.friday.billpayment.integration.DIGITABLE_LINE
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.DOCUMENT_2
import ai.friday.billpayment.integration.DOCUMENT_3
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.timeFormat
import ai.friday.morning.date.withGivenDateTime
import arrow.core.right
import io.kotest.matchers.collections.shouldContainAll
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.http.HttpResponse
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.mockk.Called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.verify
import java.time.Instant
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.UUID
import java.util.stream.Stream
import kotlin.math.min
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource

class DDAServiceTest {

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbDAO = DynamoDbDAO(amazonDynamoDB = dynamoDB)

    private val ddaProviderService: DDAProviderService = mockk(relaxed = true)
    private val fichaCompensacaoService: FichaCompensacaoService = mockk()
    private val findBillService: FindBillService = mockk()
    private val featureConfiguration: FeatureConfiguration = mockk(relaxed = true)
    private val accountRepository: AccountRepository = mockk(relaxUnitFun = true)
    private val billValidationService: BillValidationService = mockk()
    private val updateBillService: UpdateBillService = mockk()
    private val ddaRepository: DDARepository = mockk(relaxUnitFun = true, relaxed = true) {
        every { save(any<DDARegister>()) } answers { firstArg() }
    }
    private val messagePublisher: MessagePublisher = mockk(relaxed = true)
    private val fullDDAPostProcessor: FullDDAPostProcessor = mockk(relaxed = true)

    val mockDdaConfig = mockk<DDAConfig>(relaxed = true) {
        every { maxAddUser } returns MAX_ADD_USER
        every { maxAddUserAsync } returns MAX_ADD_USER_ASYNC
        every { activateWaitMinutes } returns 90
    }

    val createDDARegister = CreateDDARegister(
        ddaRepository = ddaRepository,
        ddaConfig = mockDdaConfig,
    )

    val walletService = mockk<WalletService>() {
        every { findWallet(any()) } returns mockk(relaxed = true) {
            every { status } returns WalletStatus.ACTIVE
        }
    }

    private val openFinanceIncentiveService: OpenFinanceIncentiveService = mockk(relaxed = true)

    private val ddaService = DDAService(
        ddaProviderService = ddaProviderService,
        findBillService = findBillService,
        featureConfiguration = featureConfiguration,
        accountRepository = accountRepository,
        billValidationService = billValidationService,
        updateBillService = updateBillService,
        ddaRepository = ddaRepository,
        fullDDAPostProcessor = fullDDAPostProcessor,
        messagePublisher = messagePublisher,
        ddaConfig = mockDdaConfig,
        createDDARegister = createDDARegister,
        fichaCompensacaoService = fichaCompensacaoService,
        walletService = walletService,
        openFinanceIncentiveService = openFinanceIncentiveService,
        ddaFullImportQueueName = "queue",
    )

    @Nested
    @DisplayName("ao executar o registro de DDA em grandes lotes assincronamente ")
    inner class DDABatchRegisterAsyncScope {

        @Test
        fun `deve listar registros de DDA que estão com status PENDING`() {
            ddaService.batchRegisterAsync()
            verify(exactly = 1) { ddaRepository.findByStatus(DDAStatus.PENDING) }
        }

        @Test
        fun `deve adicionar a lista de documentos no DDA e atualizar registro de DDA para REQUESTING e retornar o identificador da requisição`() {
            every {
                ddaRepository.findByStatus(DDAStatus.PENDING)
            } returns ddaRegisterPendingList

            every {
                ddaProviderService.batchAdd(any())
            } returns DDABatchResult.Accepted(BATCH_REQUEST_ID)

            ddaService.batchRegisterAsync()

            val slots = mutableListOf<DDARegister>()

            val expectedSaves = min(ddaRegisterPendingList.size, MAX_ADD_USER_ASYNC)
            verify(exactly = expectedSaves) { ddaRepository.save(capture(slots)) }

            slots.forEach {
                it.status shouldBe DDAStatus.REQUESTING
            }

            ddaRegisterPendingList.map { it.document } shouldContainAll slots.map { it.document }
        }

        @Test
        fun `deve enfileirar o lote que foi solicitado para verificação posterior`() {
            every {
                ddaRepository.findByStatus(DDAStatus.PENDING)
            } returns listOf(ddaRegisterPending)

            every {
                ddaProviderService.batchAdd(any())
            } returns DDABatchResult.Accepted(BATCH_REQUEST_ID)

            ddaService.batchRegisterAsync()

            val slot = slot<BatchAddOrder>()

            verify(exactly = 1) { messagePublisher.sendBatchAddOrderMessage(capture(slot)) }

            slot.captured.requestId shouldBe BATCH_REQUEST_ID
            slot.captured.documents shouldBe listOf(ddaRegisterPending.document)
        }

        @Test
        fun `não deve chamar mais documentos que a configuração(maxAddUserAsync) permite`() {
            every {
                ddaRepository.findByStatus(any())
            } returns ddaRegisterPendingList

            val slotDDARegisterList = slot<List<Document>>()
            ddaService.batchRegisterAsync()
            verify(exactly = 1) { ddaProviderService.batchAdd(capture(slotDDARegisterList)) }

            slotDDARegisterList.captured.size shouldBe MAX_ADD_USER_ASYNC
        }

        @Test
        fun `ao receber erro da integração, não deve alterar o status do registro de DDA`() {
            every { ddaProviderService.batchAdd(any()) } throws HttpClientResponseException(
                "error",
                HttpResponse.badRequest("body"),
            )

            ddaService.batchRegisterAsync()
            verify(exactly = 0) { messagePublisher.sendMessage(any()) }
            verify(exactly = 0) { ddaRepository.save(any<DDARegister>()) }
        }

        @Test
        fun `deve remover cadastro de usuários a descadastrar se não houver usuários pendentes de cadastro`() {
            every {
                ddaRepository.findByStatus(DDAStatus.PENDING)
            } returns emptyList()

            every {
                ddaRepository.findByStatus(DDAStatus.PENDING_CLOSE)
            } returns ddaRegisterClosingList

            withGivenDateTime(ZonedDateTime.of(LocalDate.of(2022, 8, 10), LocalTime.NOON, brazilTimeZone)) {
                ddaService.batchRegisterAsync()
            }
            verify(exactly = 0) { ddaProviderService.batchAdd(any()) }

            verify(exactly = 1) { ddaProviderService.remove(any()) }
        }
    }

    @Nested
    @DisplayName("given wallet user")
    inner class WalletUser {

        private val defaultWalletId = WalletId("WALLET-ID ${UUID.randomUUID()}")
        val account = Account(
            accountId = AccountId(ACCOUNT_ID),
            name = "Name",
            emailAddress = EmailAddress("email"),
            document = DOCUMENT,
            documentType = "documentType",
            mobilePhone = "mobilePhone",
            created = getZonedDateTime(),
            updated = getZonedDateTime(),
            status = AccountStatus.APPROVED,
            configuration = LegacyAccountConfiguration(
                creditCardConfiguration = CreditCardConfiguration(
                    quota = 20_000_00,
                ),
                defaultWalletId = defaultWalletId,
                receiveDDANotification = false,
            ),
            imageUrlSmall = null,
            imageUrlLarge = null,
            subscriptionType = SubscriptionType.PIX,
        )
        private val billAdded = BillAdded(
            billId = BillId(BILL_ID_7),
            created = Instant.now().toEpochMilli(),
            walletId = defaultWalletId,
            description = "ACTIVE BILL",
            dueDate = getLocalDate().plusDays(3),
            amount = 100L,
            billType = BillType.FICHA_COMPENSACAO,
            barcode = BarCode.ofDigitable(DIGITABLE_LINE),
            assignor = "AMERICANAS",
            document = DOCUMENT,
            paymentLimitTime = LocalTime.parse("20:00", timeFormat),
            lastSettleDate = getLocalDate().plusDays(1).format(dateFormat),
            actionSource = ActionSource.DDA(accountId = account.accountId),
        )

        val bill = Bill.build(billAdded)

        private val ddaRegisterActive = DDARegister(
            accountId = account.accountId,
            document = account.document,
            created = getZonedDateTime(),
            lastUpdated = getZonedDateTime(),
            status = DDAStatus.ACTIVE,
        )
        private val ddaRegisterRequested = DDARegister(
            accountId = account.accountId,
            document = account.document,
            created = getZonedDateTime(),
            status = DDAStatus.REQUESTED,
            lastUpdated = getZonedDateTime(),
        )

        private val ddaRegisterPending = DDARegister(
            accountId = account.accountId,
            document = account.document,
            created = getZonedDateTime(),
            status = DDAStatus.PENDING,
        )

        @BeforeEach
        fun setUp() {
            every { accountRepository.findAccountByDocument(DOCUMENT) } returns account
            every { accountRepository.findById(account.accountId) } returns account

            every { billValidationService.validate(ofType(BarCode::class)) } answers {
                ArbiValidationResponse(
                    null,
                    12,
                    null,
                )
            }
            every { ddaRepository.find(accountId = any()) } answers { ddaRegisterActive }

            every { fichaCompensacaoService.createFichaDeCompensacao(any()) } answers {
                CreateBillResult.FAILURE.BillAlreadyExists(
                    bill,
                )
            }
        }

        @Test
        fun `should not call full import when skipFullImportWhenAccountHasBills is true and account already has bills`() {
            val ddaRegister = DDARegister(
                accountId = AccountId(ACCOUNT_ID),
                document = DOCUMENT,
                created = getZonedDateTime(),
                lastUpdated = getZonedDateTime(),
                status = DDAStatus.REQUESTED,
            )

            every {
                featureConfiguration.skipFullImportWhenAccountHasBills
            } returns true

            every {
                findBillService.findByWalletId(any())
            } returns listOf(getActiveBill())

            every { fullDDAPostProcessor.process(any()) } returns Unit.right()

            val result = ddaService.executeFullImport(ddaRegister)

            verify(exactly = 0) {
                fichaCompensacaoService.createFichaDeCompensacao(any(), any())
                ddaRepository.find(any(), any(), any())
            }

            verify {
                fullDDAPostProcessor.process(ddaRegister)
                ddaRepository.save(
                    withArg<DDARegister> {
                        it.status shouldBe DDAStatus.ACTIVE
                    },
                )
            }

            result.shouldBeTypeOf<DDAFullImportResult.Skipped>()
        }

        @Test
        fun `should add bill on default wallet`() {
            val slot = slot<CreateFichaDeCompensacaoRequest>()

            every { ddaRepository.find(any(), any(), any()) } returns null
            every { fichaCompensacaoService.createFichaDeCompensacao(capture(slot)) } answers {
                CreateBillResult.SUCCESS(
                    bill,
                )
            }

            val ddaItem = DDAItem(
                ddaProvider = DDAProvider.ARBI,
                amount = 100.0,
                barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
                dueDate = getLocalDate(),
                document = DOCUMENT,
            )
            val result = ddaService.executeBill(ddaItem = ddaItem)
            result shouldBe DDAExecutionResult.Added
            with(slot.captured) {
                walletId shouldBe account.configuration.defaultWalletId
            }

            val ddaBill = DDABill(
                barcode = ddaItem.barcode,
                document = ddaItem.document,
                dueDate = ddaItem.dueDate,
                walletId = account.configuration.defaultWalletId!!,
                billId = bill.billId,
                lastStatus = bill.status,
            )
            verify { ddaRepository.save(ddaBill) }
        }

        @ParameterizedTest
        @EnumSource(
            DDAStatus::class,
            mode = EnumSource.Mode.EXCLUDE,
            names = ["REQUESTED", "ACTIVE", "PENDING_MIGRATION_OPTOUT", "MIGRATING_OPTOUT"],
        )
        fun `should not add bill when user DDA is not ACTIVE or REQUESTED`(ddaStatus: DDAStatus) {
            every { ddaRepository.find(accountId = any()) } answers { ddaRegisterActive.copy(status = ddaStatus) }

            val ddaItem = DDAItem(
                ddaProvider = DDAProvider.ARBI,
                amount = 100.0,
                barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
                dueDate = getLocalDate(),
                document = DOCUMENT,
            )

            val result = ddaService.executeBill(ddaItem = ddaItem)

            result.shouldBeTypeOf<DDAExecutionResult.NotAdded>()

            verify {
                accountRepository.findById(any()) wasNot Called
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.dda.DDAServiceTest#synchronizeStatuses")
        fun `should synchronize bill when it already exists`(
            billSynchronizationStatus: BillSynchronizationStatus,
            ddaExecutionResult: DDAExecutionResult,
        ) {
            val ddaBill = DDABill(
                barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
                dueDate = getLocalDate(),
                document = DOCUMENT,
                walletId = defaultWalletId,
                lastStatus = BillStatus.ACTIVE,
                billId = bill.billId,
            )
            every { ddaRepository.find(any(), any(), any()) } returns ddaBill
            every {
                updateBillService.synchronizeBill(bill.billId, any())
            } returns SynchronizeBillResponse(status = billSynchronizationStatus)

            val ddaItem = DDAItem(
                ddaProvider = DDAProvider.ARBI,
                amount = 100.0,
                barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
                dueDate = getLocalDate(),
                document = DOCUMENT,
            )
            val result = ddaService.executeBill(ddaItem = ddaItem)

            when (billSynchronizationStatus) {
                is BillSynchronizationStatus.BillStatusUpdated -> {
                    verify {
                        ddaRepository.save(ddaBill.copy(lastStatus = billSynchronizationStatus.billStatus))
                    }
                    result.shouldBeTypeOf<DDAExecutionResult.Updated>()

                    when (billSynchronizationStatus.billStatus) {
                        BillStatus.ALREADY_PAID -> {
                            verify { openFinanceIncentiveService.processDDA(any()) }
                        }
                        else -> {
                            verify(exactly = 0) { openFinanceIncentiveService.processDDA(any()) }
                        }
                    }
                }

                is BillSynchronizationStatus.BillAmountTotalUpdated -> {
                    verify(exactly = 0) {
                        ddaRepository.save(any<DDABill>())
                    }
                    result.shouldBeTypeOf<DDAExecutionResult.Updated>()
                }

                is BillSynchronizationStatus.UnableToValidate -> {
                    verify(exactly = 0) {
                        ddaRepository.save(any<DDABill>())
                    }
                    result.shouldBeTypeOf<DDAExecutionResult.Error>()
                    result.isRetryable shouldBe billSynchronizationStatus.isRetryable
                }

                else -> {
                    verify(exactly = 0) {
                        ddaRepository.save(any<DDABill>())
                    }
                    result::class shouldBe ddaExecutionResult::class
                }
            }
        }

        @Test
        fun `deve criar uma nova bill se a bill já existir numa carteira inativa`() {
            val ddaBill = DDABill(
                barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
                dueDate = getLocalDate(),
                document = DOCUMENT,
                walletId = WalletId("test-wallet"),
                lastStatus = BillStatus.ACTIVE,
                billId = bill.billId,
            )
            val slot = slot<CreateFichaDeCompensacaoRequest>()
            every { ddaRepository.find(any(), any(), any()) } returns ddaBill
            every {
                fichaCompensacaoService.createFichaDeCompensacao(capture(slot), any())
            } returns CreateBillResult.SUCCESS(bill)

            every { walletService.findWallet(ddaBill.walletId) } returns mockk(relaxed = true) {
                every { status } returns WalletStatus.CLOSED
            }

            val ddaItem = DDAItem(
                ddaProvider = DDAProvider.ARBI,
                amount = 100.0,
                barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
                dueDate = getLocalDate(),
                document = DOCUMENT,
            )
            ddaService.executeBill(ddaItem = ddaItem)

            slot.captured.walletId shouldBe defaultWalletId
            verify {
                updateBillService wasNot Called
            }
        }

        @ParameterizedTest
        @EnumSource(BillStatus::class, mode = EnumSource.Mode.INCLUDE, names = ["PAID", "ALREADY_PAID", "NOT_PAYABLE"])
        fun `should not update bill when it is not ACTIVE on dda control`(billStatus: BillStatus) {
            every { ddaRepository.find(any(), any(), any()) } answers {
                DDABill(
                    barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
                    dueDate = getLocalDate(),
                    document = DOCUMENT,
                    walletId = defaultWalletId,
                    lastStatus = billStatus,
                    billId = bill.billId,
                )
            }

            val ddaItem = DDAItem(
                ddaProvider = DDAProvider.ARBI,
                amount = 100.0,
                barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
                dueDate = getLocalDate(),
                document = DOCUMENT,
            )
            val result = ddaService.executeBill(ddaItem = ddaItem)

            result.shouldBeTypeOf<DDAExecutionResult.NotUpdated>()

            verify(exactly = 0) {
                updateBillService.synchronizeBill(bill.billId, any())
            }
        }

        @Test
        fun `should not update bill and return Added when it exists on bill payment but not on dda control`() {
            every { ddaRepository.find(any(), any(), any()) } returns null
            every { fichaCompensacaoService.createFichaDeCompensacao(any()) } answers {
                CreateBillResult.FAILURE.BillAlreadyExists(
                    bill,
                )
            }

            val ddaItem = DDAItem(
                ddaProvider = DDAProvider.ARBI,
                amount = 100.0,
                barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
                dueDate = getLocalDate(),
                document = DOCUMENT,
            )
            val result = ddaService.executeBill(ddaItem = ddaItem)
            result shouldBe DDAExecutionResult.Added

            verify(exactly = 0) {
                updateBillService.synchronizeBill(bill.billId, any())
            }
            val ddaBill = DDABill(
                barcode = ddaItem.barcode,
                document = ddaItem.document,
                dueDate = ddaItem.dueDate,
                walletId = bill.walletId,
                billId = bill.billId,
                lastStatus = bill.status,
            )
            verify { ddaRepository.save(ddaBill) }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.dda.DDAServiceTest#createBillResultWithError")
        fun `should return error when it does not exists and CreateBillResult is with error`(
            result: CreateBillResult,
            isRetryable: Boolean,
        ) {
            every { ddaRepository.find(any(), any(), any()) } returns null
            every { fichaCompensacaoService.createFichaDeCompensacao(any()) } returns result

            val ddaItem = DDAItem(
                ddaProvider = DDAProvider.ARBI,
                amount = 100.0,
                barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
                dueDate = getLocalDate(),
                document = DOCUMENT,
            )
            val ddaResult = ddaService.executeBill(ddaItem = ddaItem)
            ddaResult.shouldBeTypeOf<DDAExecutionResult.Error>()
            ddaResult.isRetryable shouldBe isRetryable

            verify(exactly = 0) {
                ddaRepository.save(any<DDABill>())
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.dda.DDAServiceTest#createBillResultWithFinalStatus")
        fun `should return not added when it does not exists and CreateBillResult is with final status`(
            result: CreateBillResult,
        ) {
            every { ddaRepository.find(any(), any(), any()) } returns null
            every { fichaCompensacaoService.createFichaDeCompensacao(any()) } returns result

            val ddaItem = DDAItem(
                ddaProvider = DDAProvider.ARBI,
                amount = 100.0,
                barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
                dueDate = getLocalDate(),
                document = DOCUMENT,
            )
            val ddaResult = ddaService.executeBill(ddaItem = ddaItem)
            ddaResult.shouldBeTypeOf<DDAExecutionResult.NotAdded>()

            verify(exactly = 0) {
                ddaRepository.save(any<DDABill>())
            }
        }

        @Test
        fun `should register DDA when get bills returns user not found`() {
            val date = ZonedDateTime.of(2022, 5, 20, 12, 30, 0, 0, ZoneId.systemDefault())

            val dueDate = date.toLocalDate().minusMonths(1)

            withGivenDateTime(date) {
                every {
                    ddaProviderService.getBills(dueDate = dueDate, document = DOCUMENT)
                } throws ArbiDDAPayerNotFoundException()

                every {
                    ddaRepository.find(accountId = any())
                } returns ddaRegisterPending

                every {
                    ddaProviderService.add(any(), DocumentType.CPF)
                } returns listOf(Document(DOCUMENT))

                val result = ddaService.executeFullImport(ddaRegisterRequested)

                verify {
                    ddaProviderService.getBills(dueDate, DOCUMENT)
                    ddaRepository.find(ddaRegisterPending.accountId)
                }

                result.shouldBeTypeOf<DDAFullImportResult.NotImported>()
            }
        }

        @Test
        fun `should continue processing bills even when some throws exception`() {
            every {
                ddaProviderService.getBills(dueDate = any(), document = DOCUMENT)
            } returns listOf(
                DDAItem(
                    ddaProvider = DDAProvider.ARBI,
                    amount = 10.0,
                    barcode = BarCode.ofDigitable(DIGITABLE_LINE),
                    document = DOCUMENT,
                    dueDate = getLocalDate(),
                ),
                DDAItem(
                    ddaProvider = DDAProvider.ARBI,
                    amount = 11.0,
                    barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
                    document = DOCUMENT,
                    dueDate = getLocalDate(),
                ),
            )

            every { ddaRepository.find(any(), any(), any()) } throws NoStackTraceException("test") andThen null
            every { fichaCompensacaoService.createFichaDeCompensacao(any()) } answers {
                CreateBillResult.SUCCESS(Bill.build(ai.friday.billpayment.billAdded))
            }

            every { fullDDAPostProcessor.process(any()) } returns Unit.right()

            val result = ddaService.executeFullImport(ddaRegisterRequested)

            val slot = slot<DDARegister>()
            verify {
                ddaRepository.find(BarCode.ofDigitable(DIGITABLE_LINE), any(), any())
                ddaRepository.find(BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE), any(), any())
                ddaRepository.save(capture(slot))
            }

            slot.captured.status shouldBe DDAStatus.ACTIVE
            result shouldBe DDAFullImportResult.Imported
        }

        @Test
        fun `should change dda register to active when all bills are processed successfully`() {
            val date = ZonedDateTime.of(2022, 5, 20, 12, 30, 0, 0, ZoneId.systemDefault())

            val dueDate = date.toLocalDate().minusMonths(1)

            withGivenDateTime(date) {
                every {
                    ddaProviderService.getBills(dueDate = dueDate, document = DOCUMENT)
                } returns listOf(
                    DDAItem(
                        ddaProvider = DDAProvider.ARBI,
                        amount = 10.0,
                        barcode = BarCode.ofDigitable(DIGITABLE_LINE),
                        document = DOCUMENT,
                        dueDate = date.toLocalDate(),
                    ),
                    DDAItem(
                        ddaProvider = DDAProvider.ARBI,
                        amount = 11.0,
                        barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
                        document = DOCUMENT,
                        dueDate = date.toLocalDate(),
                    ),
                )

                every { ddaRepository.find(any(), any(), any()) } returns null
                every { fichaCompensacaoService.createFichaDeCompensacao(any()) } answers {
                    CreateBillResult.SUCCESS(Bill.build(ai.friday.billpayment.billAdded))
                }

                every { fullDDAPostProcessor.process(any()) } returns Unit.right()

                val result = ddaService.executeFullImport(ddaRegisterRequested)

                val slot = slot<DDARegister>()
                verify {
                    ddaRepository.save(capture(slot))
                }

                slot.captured.status shouldBe DDAStatus.ACTIVE
                result shouldBe DDAFullImportResult.Imported

                verify {
                    ddaProviderService.getBills(dueDate, DOCUMENT)
                }
            }
        }

        @Test
        fun `should not activate user if no bills were added and it can retry`() {
            every {
                ddaProviderService.getBills(dueDate = any(), document = DOCUMENT)
            } returns listOf(
                DDAItem(
                    ddaProvider = DDAProvider.ARBI,
                    amount = 10.0,
                    barcode = BarCode.ofDigitable(DIGITABLE_LINE),
                    document = DOCUMENT,
                    dueDate = getLocalDate(),
                ),
                DDAItem(
                    ddaProvider = DDAProvider.ARBI,
                    amount = 11.0,
                    barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
                    document = DOCUMENT,
                    dueDate = getLocalDate(),
                ),
            )
            every { ddaRepository.find(any(), any(), any()) } throws NoStackTraceException("test") andThen null
            every { fichaCompensacaoService.createFichaDeCompensacao(any()) } answers {
                CreateBillResult.FAILURE.BillNotPayable(description = "fake", billRegisterData = null)
            }

            val result = ddaService.executeFullImport(ddaRegisterRequested)

            verify {
                ddaRepository.find(BarCode.ofDigitable(DIGITABLE_LINE), any(), any())
                ddaRepository.find(BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE), any(), any())
            }

            verify(exactly = 0) {
                ddaRepository.save(any<DDARegister>())
            }

            result.shouldBeTypeOf<DDAFullImportResult.NotImported>()
        }

        @Test
        fun `should activate user if no bills were added and it can't retry`() {
            every {
                ddaProviderService.getBills(dueDate = any(), document = DOCUMENT)
            } returns listOf(
                DDAItem(
                    ddaProvider = DDAProvider.ARBI,
                    amount = 10.0,
                    barcode = BarCode.ofDigitable(DIGITABLE_LINE),
                    document = DOCUMENT,
                    dueDate = getLocalDate(),
                ),
                DDAItem(
                    ddaProvider = DDAProvider.ARBI,
                    amount = 11.0,
                    barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
                    document = DOCUMENT,
                    dueDate = getLocalDate(),
                ),
            )
            every { ddaRepository.find(any(), any(), any()) } throws NoStackTraceException("test") andThen null
            every { fichaCompensacaoService.createFichaDeCompensacao(any()) } answers {
                CreateBillResult.FAILURE.BillNotPayable(description = "fake", billRegisterData = null)
            }

            every { fullDDAPostProcessor.process(any()) } returns Unit.right()

            val result =
                ddaService.executeFullImport(ddaRegisterRequested.copy(lastUpdated = getZonedDateTime().minusMinutes(91)))

            val registerSlot = slot<DDARegister>()

            verify {
                ddaRepository.find(BarCode.ofDigitable(DIGITABLE_LINE), any(), any())
                ddaRepository.find(BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE), any(), any())
                ddaRepository.save(capture(registerSlot))
            }

            registerSlot.captured.status shouldBe DDAStatus.ACTIVE
            result.shouldBeTypeOf<DDAFullImportResult.Expired>()
        }

        @Test
        fun `should not activate user and return error when generic exception happens`() {
            every {
                ddaProviderService.getBills(dueDate = any(), document = DOCUMENT)
            } throws NoStackTraceException("test")

            val result = ddaService.executeFullImport(ddaRegisterRequested)

            verify(exactly = 0) {
                ddaRepository wasNot Called
            }

            result.shouldBeTypeOf<DDAFullImportResult.Error>()
        }
    }

    @Nested
    @DisplayName("when execute DDA register ")
    inner class DDARegisterScope {
        @BeforeEach
        fun setUp() {
            createBillPaymentTable(dynamoDB)
        }

        val ddaConfig = createDDAConfig(
            maxAddUser = MAX_ADD_USER,
            maxAddUserAsync = MAX_ADD_USER_ASYNC,
            ddaProvider = DDAProvider.ARBI,
        )

        val enhancedClient = DynamoDBUtils.setupDynamoDB()
        val ddaRepository = DDADbRepository(
            ddaDAO = DDADynamoDAO(enhancedClient),
            ddaBillDAO = DDABillDynamoDAO(enhancedClient),
        )

        val createDDARegister = CreateDDARegister(
            ddaRepository = ddaRepository,
            ddaConfig = ddaConfig,
        )

        val walletService = mockk<WalletService>() {
            every { findWallet(any()) } returns mockk(relaxed = true) {
                every { status } returns WalletStatus.ACTIVE
            }
        }

        val ddaService = DDAService(
            ddaProviderService = ddaProviderService,
            accountRepository = accountRepository,
            billValidationService = billValidationService,
            updateBillService = updateBillService,
            ddaRepository = ddaRepository,
            findBillService = mockk(),
            featureConfiguration = mockk(),
            fullDDAPostProcessor = mockk(relaxed = true),
            messagePublisher = messagePublisher,
            ddaConfig = ddaConfig,
            createDDARegister = createDDARegister,
            fichaCompensacaoService = fichaCompensacaoService,
            walletService = walletService,
            openFinanceIncentiveService = openFinanceIncentiveService,
            ddaFullImportQueueName = "queue",
        )

        @Test
        fun `should change DDARegister status to PENDING when it does not exist`() {
            every {
                ddaProviderService.add(any(), DocumentType.CPF)
            } throws CallDDAAgregadoException(NoStackTraceException("fake error"))

            val result = ddaService.register(accountId = AccountId(ACCOUNT_ID), document = DOCUMENT)

            result shouldBe DDAStatus.PENDING

            val register = ddaRepository.find(AccountId(ACCOUNT_ID))

            register.shouldNotBeNull()

            with(register) {
                status shouldBe DDAStatus.PENDING
                accountId shouldBe AccountId(ACCOUNT_ID)
                document shouldBe DOCUMENT
                provider shouldBe DDAProvider.ARBI
            }
        }

        @Test
        fun `ao tentar ativar um CPF e jah existir um pedido de encerramento para este CPF, deve cancelar o encerramento`() {
            every {
                ddaProviderService.add(any(), DocumentType.CPF)
            } throws CallDDAAgregadoException(NoStackTraceException("fake error"))

            ddaService.register(accountId = AccountId(ACCOUNT_ID_2), document = DOCUMENT) shouldBe DDAStatus.PENDING
            ddaService.remove(accountId = AccountId(ACCOUNT_ID_2), document = Document(DOCUMENT)).isRight() shouldBe true

            val result = ddaService.register(accountId = AccountId(ACCOUNT_ID), document = DOCUMENT)

            result shouldBe DDAStatus.PENDING

            val register = ddaRepository.find(AccountId(ACCOUNT_ID))

            register.shouldNotBeNull()

            with(register) {
                status shouldBe DDAStatus.PENDING
                accountId shouldBe AccountId(ACCOUNT_ID)
                document shouldBe DOCUMENT
                provider shouldBe DDAProvider.ARBI
            }

            ddaRepository.find(AccountId(ACCOUNT_ID_2))?.status shouldBe DDAStatus.CLOSED
        }
    }

    @Nested
    @DisplayName("ao executar o registro de DDA em lote sincronamente ")
    inner class DDABatchRegisterScope {
        @Test
        fun `deve adicionar a lista de documentos no dda e salvar como REQUESTED`() {
            val expectedDdaRegister = DDARegister(
                accountId = AccountId(ACCOUNT_ID),
                document = DOCUMENT,
                created = getZonedDateTime(),
                status = DDAStatus.PENDING,
                lastUpdated = getZonedDateTime(),
                lastSuccessfullExecution = null,
            )
            val expectedDdaRegister2 = DDARegister(
                accountId = AccountId(ACCOUNT_ID_2),
                document = CNPJ_1,
                created = getZonedDateTime(),
                status = DDAStatus.PENDING,
                lastUpdated = getZonedDateTime(),
                lastSuccessfullExecution = null,
            )
            every { ddaRepository.findByStatus(DDAStatus.PENDING) } returns listOf(
                expectedDdaRegister,
                expectedDdaRegister2,
            )
            every { ddaProviderService.add(any(), DocumentType.CPF) } returns listOf(
                Document(expectedDdaRegister.document),
            )

            every { ddaProviderService.add(any(), DocumentType.CNPJ) } returns listOf(
                Document(expectedDdaRegister2.document),
            )

            ddaService.batchRegister()

            val slot = slot<List<DDARegisterMessage>>()
            verify(exactly = 1) {
                ddaRepository.save(expectedDdaRegister.copy(status = DDAStatus.REQUESTED))
                ddaRepository.save(expectedDdaRegister2.copy(status = DDAStatus.REQUESTED))
                messagePublisher.sendBatchDDARequestedMessage(capture(slot))
            }
            slot.captured.shouldContainExactlyInAnyOrder(
                listOf(
                    DDARegisterMessage(expectedDdaRegister.accountId, expectedDdaRegister.document),
                    DDARegisterMessage(expectedDdaRegister2.accountId, expectedDdaRegister2.document),
                ),
            )
        }

        @Test
        fun `deve adicionar a lista de documentos no dda e salvar como REQUESTED apenas os que funcionaram`() {
            val expectedDdaRegister = DDARegister(
                accountId = AccountId(ACCOUNT_ID),
                document = DOCUMENT,
                created = getZonedDateTime(),
                status = DDAStatus.PENDING,
                lastUpdated = getZonedDateTime(),
                lastSuccessfullExecution = null,
            )
            val expectedDdaRegister2 = DDARegister(
                accountId = AccountId(ACCOUNT_ID_2),
                document = DOCUMENT_2,
                created = getZonedDateTime(),
                status = DDAStatus.PENDING,
                lastUpdated = getZonedDateTime(),
                lastSuccessfullExecution = null,
            )
            every { ddaRepository.findByStatus(DDAStatus.PENDING) } returns listOf(
                expectedDdaRegister,
                expectedDdaRegister2,
            )
            every { ddaProviderService.add(any(), DocumentType.CPF) } returns listOf(Document(expectedDdaRegister2.document))

            ddaService.batchRegister()

            verify(exactly = 0) {
                ddaRepository.save(expectedDdaRegister.copy(status = DDAStatus.REQUESTED))
            }
            val slot = slot<List<DDARegisterMessage>>()
            verify(exactly = 1) {
                ddaRepository.save(expectedDdaRegister2.copy(status = DDAStatus.REQUESTED))
                messagePublisher.sendBatchDDARequestedMessage(capture(slot))
            }
            slot.captured shouldBe listOf(
                DDARegisterMessage(
                    expectedDdaRegister2.accountId,
                    expectedDdaRegister2.document,
                ),
            )
        }

        @Test
        fun `nao deve salvar como REQUESTED quando falha a adição de toda a lista de documentos no dda`() {
            val expectedDdaRegister = DDARegister(
                accountId = AccountId(ACCOUNT_ID),
                document = DOCUMENT,
                created = getZonedDateTime(),
                status = DDAStatus.PENDING,
                lastUpdated = getZonedDateTime(),
                lastSuccessfullExecution = null,
            )
            val expectedDdaRegister2 = DDARegister(
                accountId = AccountId(ACCOUNT_ID_2),
                document = DOCUMENT_2,
                created = getZonedDateTime(),
                status = DDAStatus.PENDING,
                lastUpdated = getZonedDateTime(),
                lastSuccessfullExecution = null,
            )
            every { ddaRepository.findByStatus(DDAStatus.PENDING) } returns listOf(
                expectedDdaRegister,
                expectedDdaRegister2,
            )
            every { ddaProviderService.add(any(), DocumentType.CPF) } returns listOf()

            ddaService.batchRegister()

            verify(exactly = 0) {
                ddaRepository.save(expectedDdaRegister.copy(status = DDAStatus.REQUESTED))
                ddaRepository.save(expectedDdaRegister2.copy(status = DDAStatus.REQUESTED))
                messagePublisher.sendBatchDDARequestedMessage(any())
            }
        }

        @Test
        fun `não deve chamar mais documentos que a configuração(maxAddUser) do dda permite`() {
            val ddaRegister = DDARegister(
                accountId = AccountId(ACCOUNT_ID),
                document = DOCUMENT,
                created = getZonedDateTime(),
                status = DDAStatus.PENDING,
                lastUpdated = getZonedDateTime(),
                lastSuccessfullExecution = null,
            )
            val expectedDdaRegisters =
                buildList {
                    repeat(3) {
                        add(
                            ddaRegister,
                        )
                    }
                }

            every { ddaRepository.findByStatus(DDAStatus.PENDING) } returns expectedDdaRegisters
            every { ddaProviderService.add(any(), DocumentType.CPF) } returns listOf(Document(DOCUMENT))

            ddaService.batchRegister()

            verify(exactly = 2) { ddaRepository.save(ddaRegister.copy(status = DDAStatus.REQUESTED)) }
        }

        @Test
        fun `Não deve chamar a adição de usuários no DDA quando não há usuários para serem adicionados`() {
            every { ddaRepository.findByStatus(DDAStatus.PENDING) } returns emptyList()

            ddaService.batchRegister()

            verify(exactly = 0) { ddaProviderService.add(any(), DocumentType.CPF) }
        }
    }

    @Nested
    @DisplayName("ao remover o DDA")
    inner class DDARemoveScope {
        @Test
        fun `deve marcar o registro de DDA como pendente de remoção`() {
            val expectedDdaRegister = DDARegister(
                accountId = AccountId(ACCOUNT_ID),
                document = DOCUMENT,
                created = getZonedDateTime(),
                status = DDAStatus.ACTIVE,
                lastUpdated = getZonedDateTime(),
                lastSuccessfullExecution = null,
            )

            every { ddaRepository.find(AccountId(ACCOUNT_ID)) } returns expectedDdaRegister

            ddaService.remove(accountId = AccountId(ACCOUNT_ID), document = Document(DOCUMENT)).isRight() shouldBe true

            verify(exactly = 1) {
                ddaRepository.save(
                    withArg<DDARegister> {
                        it.document shouldBe expectedDdaRegister.document
                        it.accountId shouldBe expectedDdaRegister.accountId
                        it.status shouldBe DDAStatus.PENDING_CLOSE
                    },
                )
            }
        }
    }

    @Nested
    @DisplayName("ao executar o descadastramento em lote")
    inner class DDABatchDeregisterScope {

        @Test
        fun `deve solicitar a remoção do documento no dda e salvar com o status CLOSED somente para provider ARBI`() {
            withGivenDateTime(ZonedDateTime.of(LocalDate.of(2023, 1, 9), LocalTime.NOON, brazilTimeZone)) {
                val expectedDdaRegister = DDARegister(
                    accountId = AccountId(ACCOUNT_ID),
                    document = DOCUMENT,
                    created = getZonedDateTime(),
                    status = DDAStatus.PENDING_CLOSE,
                    lastUpdated = getZonedDateTime(),
                    lastSuccessfullExecution = null,
                    provider = DDAProvider.ARBI,
                )
                every { ddaRepository.findByStatus(DDAStatus.PENDING_CLOSE) } returns listOf(
                    expectedDdaRegister,
                )

                every {
                    ddaProviderService.remove(any())
                } just runs

                ddaService.batchDeregister()

                verify(exactly = 1) {
                    ddaRepository.save(
                        withArg<DDARegister> {
                            it.document shouldBe expectedDdaRegister.document
                            it.accountId shouldBe expectedDdaRegister.accountId
                            it.status shouldBe DDAStatus.CLOSED
                        },
                    )
                }
            }
        }
    }

    @Nested
    @DisplayName("ao executar o migração em lote")
    inner class DDABatchMigrationScope {

        @Test
        fun `deve solicitar a remoção do documento no dda e salvar com o status MIGRATING_OPTOUT`() {
            val expectedDdaRegister = DDARegister(
                accountId = AccountId(ACCOUNT_ID),
                document = DOCUMENT,
                created = getZonedDateTime(),
                status = DDAStatus.PENDING_MIGRATION_OPTOUT,
                lastUpdated = getZonedDateTime(),
                lastSuccessfullExecution = null,
            )
            val expectedDdaRegister2 = DDARegister(
                accountId = AccountId(ACCOUNT_ID_2),
                document = DOCUMENT_2,
                created = getZonedDateTime(),
                status = DDAStatus.PENDING_MIGRATION_OPTOUT,
                lastUpdated = getZonedDateTime(),
                lastSuccessfullExecution = null,
            )
            every { ddaRepository.findByStatus(DDAStatus.PENDING_MIGRATION_OPTOUT) } returns listOf(
                expectedDdaRegister,
                expectedDdaRegister2,
            )

            every {
                ddaProviderService.batchRemove(any())
            } returns DDABatchResult.Accepted("abc")

            ddaService.batchMigration()

            verify(exactly = 1) {
                ddaRepository.save(
                    withArg<DDARegister> {
                        it.document shouldBe expectedDdaRegister.document
                        it.accountId shouldBe expectedDdaRegister.accountId
                        it.status shouldBe DDAStatus.MIGRATING_OPTOUT
                    },
                )
                ddaRepository.save(
                    withArg<DDARegister> {
                        it.document shouldBe expectedDdaRegister2.document
                        it.accountId shouldBe expectedDdaRegister2.accountId
                        it.status shouldBe DDAStatus.MIGRATING_OPTOUT
                    },
                )
            }
        }
    }

    companion object {
        private const val MAX_ADD_USER = 2
        private const val MAX_ADD_USER_ASYNC = 2
        private const val BATCH_REQUEST_ID = "requisicao#1"

        @JvmStatic
        fun createBillResultWithError(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    CreateBillResult.FAILURE.ServerError(throwable = NoStackTraceException("teste")),
                    true,
                ),
                Arguments.arguments(CreateBillResult.FAILURE.BillUnableToValidate(description = "failed"), true),
                Arguments.arguments(
                    CreateBillResult.FAILURE.BillUnableToValidate(
                        description = "failed",
                        isRetryable = false,
                    ),
                    false,
                ),
            )
        }

        @JvmStatic
        fun createBillResultWithFinalStatus(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    CreateBillResult.FAILURE.AlreadyPaid.WithData(
                        "BillAlreadyPaid",
                        billRegisterData = billRegisterData,
                    ),
                ),
                Arguments.arguments(
                    CreateBillResult.FAILURE.BillNotPayable(
                        "BillNotPayable",
                        billRegisterData = billRegisterData,
                    ),
                ),
            )
        }

        @JvmStatic
        fun synchronizeStatuses(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(BillSynchronizationStatus.BillNotModified, DDAExecutionResult.NotUpdated()),
                Arguments.arguments(
                    BillSynchronizationStatus.BillAmountTotalUpdated,
                    DDAExecutionResult.Updated(BillSynchronizationStatus.BillAmountTotalUpdated),
                ),
                Arguments.arguments(
                    BillSynchronizationStatus.BillStatusUpdated(BillStatus.ALREADY_PAID),
                    DDAExecutionResult.Updated(BillSynchronizationStatus.BillStatusUpdated(BillStatus.ALREADY_PAID)),
                ),
                Arguments.arguments(
                    BillSynchronizationStatus.BillStatusUpdated(BillStatus.NOT_PAYABLE),
                    DDAExecutionResult.Updated(BillSynchronizationStatus.BillStatusUpdated(BillStatus.NOT_PAYABLE)),
                ),
                Arguments.arguments(BillSynchronizationStatus.UnableToValidate(), DDAExecutionResult.Error()),
                Arguments.arguments(
                    BillSynchronizationStatus.UnableToValidate(false),
                    DDAExecutionResult.Error(isRetryable = false),
                ),
            )
        }

        val ddaRegisterPending = DDARegister(
            accountId = ACCOUNT.accountId,
            document = ACCOUNT.document,
            created = getZonedDateTime(),
            status = DDAStatus.PENDING,
        )

        private val ddaRegisterPendingList = listOf(
            ddaRegisterPending,
            ddaRegisterPending.copy(accountId = AccountId(ACCOUNT_ID_2), document = DOCUMENT_2),
            ddaRegisterPending.copy(accountId = AccountId(UUID.randomUUID().toString()), document = DOCUMENT_3),
        )

        private val ddaRegisterClosingList = listOf(
            ddaRegisterPending.copy(status = DDAStatus.CLOSING),
            ddaRegisterPending.copy(accountId = AccountId(ACCOUNT_ID_2), status = DDAStatus.CLOSING),
        )
    }
}