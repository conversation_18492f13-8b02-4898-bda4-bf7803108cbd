package ai.friday.billpayment.app

import io.kotest.matchers.collections.shouldContainExactly
import org.junit.jupiter.api.Test

internal class UtilsKtTest {

    @Test
    fun `deve remover o primeiro elemento da lista`() {
        val original = listOf(0, 1, 2, 3)
        val expected = listOf(1, 2, 3)

        original.dropFirst { it == 0 }.shouldContainExactly(expected)
    }

    @Test
    fun `deve remover o ultimo elemento da lista`() {
        val original = listOf(1, 2, 3, 0)
        val expected = listOf(1, 2, 3)

        original.dropFirst { it == 0 }.shouldContainExactly(expected)
    }

    @Test
    fun `deve remover o terceiro elemento da lista`() {
        val original = listOf(1, 2, 0, 3)
        val expected = listOf(1, 2, 3)

        original.dropFirst { it == 0 }.shouldContainExactly(expected)
    }

    @Test
    fun `deve remover o elemento apenas uma vez da lista`() {
        val original = listOf(1, 0, 2, 0, 3)
        val expected = listOf(1, 2, 0, 3)

        original.dropFirst { it == 0 }.shouldContainExactly(expected)
    }

    @Test
    fun `não deve remover nenhum elemento da lista`() {
        val original = listOf(1, 0, 2, 0, 3)
        val expected = listOf(1, 0, 2, 0, 3)

        original.dropFirst { it == 4 }.shouldContainExactly(expected)
    }

    @Test
    fun `deve funcionar com uma lista vazia`() {
        val original = listOf<Int>()
        val expected = listOf<Int>()

        original.dropFirst { it == 4 }.shouldContainExactly(expected)
    }
}