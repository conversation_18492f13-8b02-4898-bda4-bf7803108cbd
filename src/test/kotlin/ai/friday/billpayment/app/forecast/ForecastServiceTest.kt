package ai.friday.billpayment.app.forecast

import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.integrations.BillService
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.shouldBe
import io.mockk.mockk
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.util.stream.Stream
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

internal class ForecastServiceTest {
    private val balanceService: BalanceService = mockk()
    private val billService: BillService = mockk()
    private val walletLimitsService: WalletLimitsService = mockk()

    private val forecastService = ForecastService(
        balanceService = balanceService,
        billService = billService,
        walletLimitsService = walletLimitsService,
    )

    @ParameterizedTest
    @MethodSource("datePeriods")
    fun `deve retornar a data de acordo com o período`(
        now: ZonedDateTime,
        forecastPeriod: ForecastPeriod,
        expectedDate: LocalDate,
    ) {
        val result = withGivenDateTime(now) {
            forecastService.calcDatePeriod(forecastPeriod)
        }
        result shouldBe expectedDate
    }

    companion object {
        private val now = LocalDateTime.of(2022, 7, 30, 12, 0, 0).atZone(brazilTimeZone)

        @JvmStatic
        fun datePeriods(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(now, ForecastPeriod.TODAY, LocalDate.of(2022, 7, 31)),
                Arguments.of(now, ForecastPeriod.SEVEN_DAYS, LocalDate.of(2022, 8, 6)),
                Arguments.of(now, ForecastPeriod.FIFTEEN_DAYS, LocalDate.of(2022, 8, 14)),
                Arguments.of(now, ForecastPeriod.THIRTY_DAYS, LocalDate.of(2022, 8, 29)),
                Arguments.of(now, ForecastPeriod.FIRST_DAY_OF_NEXT_MONTH, LocalDate.of(2022, 8, 1)),
                Arguments.of(now, ForecastPeriod.FIRST_DAY_OF_TWO_MONTHS_AHEAD, LocalDate.of(2022, 9, 1)),
            )
        }
    }
}