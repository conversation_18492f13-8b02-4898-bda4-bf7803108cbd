package ai.friday.billpayment.app.statement

import ai.friday.billpayment.app.balance.Balance
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import io.kotest.matchers.shouldBe
import io.mockk.mockk
import java.time.LocalDateTime
import org.junit.jupiter.api.Test

class FridayMePoupeStatementItemConverterTest {
    private val statementItemConverter = FridayStatementItemConverter(
        statementTemplateCompiler = mockk(),
    )

    @Test
    fun `deve gerar o csv com header e com as colunas na ordem certa`() {
        val statements = listOf(
            StatementItem(
                id = "id-1",
                amount = 100,
                counterPartName = "Item 1",
                postedAt = LocalDateTime.of(2023, 7, 15, 9, 0, 0, 0),
                description = "descrição do item 1",
                flow = BankStatementItemFlow.CREDIT,
                Balance(0),
                category = null,
            ),
            StatementItem(
                id = "id-2",
                amount = 200,
                counterPartName = "Item 2",
                postedAt = LocalDateTime.of(2023, 7, 12, 10, 0, 0, 0),
                description = "descrição do item 2",
                flow = BankStatementItemFlow.CREDIT,
                Balance(0),
                category = null,
            ),
            StatementItem(
                id = "id-3",
                amount = 300,
                counterPartName = "Item 3",
                postedAt = LocalDateTime.of(2023, 7, 10, 11, 0, 0, 0),
                description = "descrição do item 3",
                flow = BankStatementItemFlow.CREDIT,
                Balance(0),
                category = null,
            ),
            StatementItem(
                id = "id-4",
                amount = 400,
                counterPartName = "Item 4",
                postedAt = LocalDateTime.of(2023, 7, 8, 12, 0, 0, 0),
                description = "descrição do item 4",
                flow = BankStatementItemFlow.DEBIT,
                Balance(0),
                category = "Diversos",
            ),
        )

        val csvData = statementItemConverter.convertToCsv(statements)
        csvData shouldBe "data,nome,descricao,valor,saldo,categoria\n" +
            "15/07/2023,\"Item 1\",\"descrição do item 1\",1.0,0.0,\n" +
            "12/07/2023,\"Item 2\",\"descrição do item 2\",2.0,0.0,\n" +
            "10/07/2023,\"Item 3\",\"descrição do item 3\",3.0,0.0,\n" +
            "08/07/2023,\"Item 4\",\"descrição do item 4\",-4.0,0.0,Diversos\n"
    }
}