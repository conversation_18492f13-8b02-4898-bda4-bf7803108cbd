package ai.friday.billpayment.app.journey

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.UserJourneyRegisterException
import ai.friday.billpayment.app.account.UserJourneyTrackEventException
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.integrations.UserJourneyAdapter
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.withBlockingAsyncCalls
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import java.time.Duration
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.testcontainers.shaded.org.awaitility.Awaitility.await

class UserJourneyServiceTest {

    private val userJourneyAdapter: UserJourneyAdapter = mockk()
    private val featureConfiguration: FeatureConfiguration = mockk() {
        every {
            userPilotEnabled
        } returns true
    }

    private val userJourneyService =
        UserJourneyService(userJourneyAdapter = userJourneyAdapter, featureConfiguration = featureConfiguration)

    @Test
    fun `deve registrar o novo usuario`() {
        every {
            userJourneyAdapter.register(any(), any(), any(), any(), any())
        } just Runs
        userJourneyService.registerAsync(ACCOUNT)

        val timeout = Duration.ofSeconds(10)
        val interval = Duration.ofMillis(100)

        await("await async code").atMost(timeout).pollInterval(interval).untilAsserted {
            verify {
                userJourneyAdapter.register(ACCOUNT.accountId, ACCOUNT.name, ACCOUNT.emailAddress, ACCOUNT.type, ACCOUNT.created)
            }
        }
    }

    @Test
    fun `deve lançar excecao quando ocorrer um erro ao registrar novo usuario`() {
        every {
            userJourneyAdapter.register(any(), any(), any(), any(), any())
        } throws IllegalStateException()

        assertThrows<UserJourneyRegisterException> {
            withBlockingAsyncCalls {
                userJourneyService.registerAsync(ACCOUNT)
            }
        }
    }

    @Test
    fun `deve enviar evento`() {
        every {
            userJourneyAdapter.trackEvent(any(), any())
        } just Runs
        withBlockingAsyncCalls {
            userJourneyService.trackEventAsync(AccountId(ACCOUNT_ID), UserJourneyEvent.SendBillByEmail)
        }
        verify {
            userJourneyAdapter.trackEvent(AccountId(ACCOUNT_ID), UserJourneyEvent.SendBillByEmail.eventName)
        }
    }

    @Test
    fun `deve lançar excecao quando ocorrer um erro ao enviar evento`() {
        every {
            userJourneyAdapter.trackEvent(any(), any())
        } throws IllegalStateException()

        assertThrows<UserJourneyTrackEventException> {
            withBlockingAsyncCalls {
                userJourneyService.trackEventAsync(AccountId(ACCOUNT_ID), UserJourneyEvent.SendBillByEmail)
            }
        }
    }
}