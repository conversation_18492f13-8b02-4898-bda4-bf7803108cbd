package ai.friday.billpayment.app.utilityaccount

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.UtilityAccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.UtilityAccountDynamoDAO
import ai.friday.billpayment.adapters.jobs.UtilityAccountDailyUpdateJob
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.parseDateTextAsZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.date.withGivenDateTime
import io.kotest.assertions.assertSoftly
import io.kotest.common.runBlocking
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.collections.shouldContainOnly
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Called
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import io.via1.communicationcentre.app.integrations.EmailSenderService
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZonedDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.ValueSource

class UtilityAccountServiceTest {

    val wallet = WalletFixture().buildWallet()
    val walletService = mockk<WalletService> {
        every { findWalletOrNull(any()) } returns wallet
    }

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val enhancedClient = setupDynamoDB()
    private val utilityAccountRepository =
        UtilityAccountDbRepository(client = UtilityAccountDynamoDAO(cli = enhancedClient))

    private val accountRepository: AccountDbRepository = mockk()
    private val accountService: AccountService = mockk()
    private val billRepository: BillRepository = mockk()

    private val emailSenderService = mockk<EmailSenderService>(relaxed = true)

    private val notificationAdapter: NotificationAdapter = mockk()

    private val utilityAccountService = spyk(
        UtilityAccountService(
            utilityAccountRepository = utilityAccountRepository,
            accountRepository = accountRepository,
            sqsMessagePublisher = mockk {
                every { sendMessage(any()) } returns Unit
            },
            notificationAdapter = notificationAdapter,
            intercomAdapter = mockk(),
            kmsAdapter = mockk {
                every { runBlocking { decryptData(any()) } } returns "DECRIPTED_DATA"
                every { runBlocking { encryptData(any()) } } returns "ENCRYPTED_DATA"
            },
            emailSenderService = emailSenderService,
            utilityAccountMonitorService = UtilityAccountMonitorService(
                accountService = accountService,
                walletService = walletService,
                billRepository = billRepository,
            ),
            externalFilesRepositorySA = mockk(),
            utilityAccountActivationQueueName = "",
            sender = "",
            emailDomain = "",
            manualWorkFlowEmail = "",
            utilityFlowRequestInvoicesQueueName = "",
            createBillService = mockk(relaxed = true),
            fichaCompensacaoService = mockk(relaxed = true),
            walletService = walletService,
            userJourneyService = mockk(relaxed = true),
            manualEntryService = mockk(relaxed = true),
            userEventService = mockk(relaxed = true),
        ),
    )

    private val utilityAccountDailyUpdateJob = UtilityAccountDailyUpdateJob(
        service = utilityAccountService,
    )

    private val utilityAccount = UtilityAccount(
        id = UtilityAccountId("test"),
        status = UtilityAccountConnectionStatus.CONNECTED,
        walletId = wallet.id,
        accountEmail = "<EMAIL>",
        attempts = 0,
        utility = Utility.CLARO_HOME,
        updatedAt = getZonedDateTime(),
        createdAt = getZonedDateTime().minusMonths(1).minusDays(1),
        lastBillIdFound = BillId("old-bill-id"),
        lastScannedAt = getZonedDateTime().minusMonths(1).minusDays(1),
        connectionMethod = UtilityConnectionMethod.SCRAPING,
        connectionDetails = UtilityAccountConnectionDetails.create(Utility.CLARO_HOME, mapOf("login" to "test", "password" to "1234")),
        addedBy = wallet.founder.accountId,
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        utilityAccountRepository.save(utilityAccount)
        every { accountRepository.findById(any()) } returns ACCOUNT
        every { accountService.findAccountByIdOrNull(any()) } returns ACCOUNT
        every { billRepository.findByWallet(any()) } returns emptyList()
    }

    @Test
    fun `utilities que sao flow nao devem ser atualizadas`() {
        utilityAccountRepository.save(utilityAccount.copy(connectionMethod = UtilityConnectionMethod.FLOW))

        val validBill = getActiveBill(dueDate = getLocalDate().plusDays(15)).copy(
            barCode = BarCode.of("84611111111111101582222222222222222222222222"),
            createdOn = getZonedDateTime().minusDays(15).toLocalDateTime(),
        )

        every { billRepository.findByWallet(any()) } returns listOf(validBill)

        utilityAccountService.refreshAllConnectedUtilities()

        with(utilityAccountRepository.findById(utilityAccount.id, utilityAccount.walletId)!!) {
            lastBillIdFound shouldBe utilityAccount.lastBillIdFound
            lastDueDateFound shouldBe utilityAccount.lastDueDateFound
        }
    }

    @Test
    fun `deve salvar utility account com lastBillId e dueDate caso encontre uma conta criada nos últimos 30 dias`() {
        val validBill = getActiveBill(
            dueDate = getLocalDate().plusDays(15),
        ).copy(
            barCode = BarCode.of("84611111111111101582222222222222222222222222"),
            createdOn = getZonedDateTime().minusDays(15).toLocalDateTime(),
        )

        every { billRepository.findByWallet(any()) } returns listOf(
            validBill,
        )

        val executeTime = getZonedDateTime()

        withGivenDateTime(time = executeTime) {
            utilityAccountService.refreshAllConnectedUtilities()
        }

        with(utilityAccountRepository.findById(utilityAccount.id, utilityAccount.walletId)!!) {
            lastBillIdFound!! shouldBe validBill.billId
            lastDueDateFound shouldBe validBill.dueDate
            lastScannedAt!!.format(dateTimeFormat) shouldBe executeTime.format(
                dateTimeFormat,
            )
        }

        verify {
            emailSenderService wasNot Called
        }
    }

    @Test
    fun `nao deve atualizar a lastDueDateFound quando a data de vencimento da conta encontrada for menor que a ja salva`() {
        utilityAccountRepository.save(utilityAccount.copy(lastDueDateFound = getLocalDate().minusMonths(1)))

        val validBill = getActiveBill(
            dueDate = getLocalDate().minusYears(2),
        ).copy(
            barCode = BarCode.of("84611111111111101582222222222222222222222222"),
            createdOn = getZonedDateTime().minusDays(15).toLocalDateTime(),
        )

        every { billRepository.findByWallet(any()) } returns listOf(
            validBill,
        )

        val executeTime = getZonedDateTime()

        withGivenDateTime(time = executeTime) {
            utilityAccountService.refreshAllConnectedUtilities()
        }

        assertSoftly {
            with(utilityAccountRepository.findById(utilityAccount.id, utilityAccount.walletId)!!) {
                lastBillIdFound!! shouldBe utilityAccount.lastBillIdFound
                lastDueDateFound shouldBe getLocalDate().minusMonths(1)
            }
        }
    }

    @Test
    fun `deve salvar utility account com scannedAt caso não encontre uma conta nos últimos 30 dias`() {
        val executeTime = getZonedDateTime()

        withGivenDateTime(time = executeTime) {
            utilityAccountService.refreshAllConnectedUtilities()
        }

        with(utilityAccountRepository.findById(utilityAccount.id, utilityAccount.walletId)!!) {
            lastBillIdFound shouldBe utilityAccount.lastBillIdFound
            lastDueDateFound shouldBe utilityAccount.lastDueDateFound
            lastScannedAt!!.format(dateTimeFormat) shouldBe executeTime.format(
                dateTimeFormat,
            )
        }
    }

    @Test
    fun `deve escanear as contas de um utility account criado há mais de 30 dias`() {
        utilityAccountRepository.save(
            utilityAccount.copy(
                lastDueDateFound = null,
                lastBillIdFound = null,
                lastScannedAt = getZonedDateTime(),
            ),
        )

        utilityAccountService.refreshAllConnectedUtilities()

        verify {
            billRepository.findByWallet(any())
        }
    }

    @Test
    fun `deve escanear as contas de um utility account com data de vencimento há mais de 25 dias`() {
        utilityAccountRepository.save(
            utilityAccount.copy(
                lastDueDateFound = getLocalDate().minusDays(26),
                lastBillIdFound = null,
            ),
        )

        utilityAccountService.refreshAllConnectedUtilities()

        verify {
            billRepository.findByWallet(any())
        }
    }

    @Test
    fun `deve escanear as contas de um utility account com data de vencimento há mais de 25 dias mesmo que tenha sido escaneada no último mês`() {
        utilityAccountRepository.save(
            utilityAccount.copy(
                lastDueDateFound = getLocalDate().minusDays(26),
                lastScannedAt = getZonedDateTime().minusMonths(1),
            ),
        )

        utilityAccountService.refreshAllConnectedUtilities()

        verify {
            billRepository.findByWallet(any())
        }
    }

    @Test
    fun `não deve escanear as contas de um utility account que não recebe conta há mais de 3 meses`() {
        utilityAccountRepository.save(
            utilityAccount.copy(
                lastDueDateFound = getLocalDate().minusMonths(3).minusDays(1),
            ),
        )

        utilityAccountService.refreshAllConnectedUtilities()

        verify {
            billRepository wasNot called
        }
    }

    @Test
    fun `deve retornar somente as contas de consumo conectadas via FLOW e que não possuem fatura para o mes atual`() {
        val today = getLocalDate()

        val utilityAccountLight = utilityAccount.copy(
            utility = Utility.LIGHT_RJ,
            connectionDetails = UtilityAccountConnectionDetails.create(Utility.LIGHT_RJ, mapOf("cpf" to DOCUMENT, "password" to "password", "installationNumber" to "1234")),
        )

        utilityAccountRepository.save(
            utilityAccountLight.copy(
                id = UtilityAccountId("1"),
                status = UtilityAccountConnectionStatus.DISCONNECTED,
                connectionMethod = UtilityConnectionMethod.SCRAPING,
            ),
        )
        utilityAccountRepository.save(
            utilityAccount.copy(
                id = UtilityAccountId("2"),
                status = UtilityAccountConnectionStatus.CONNECTED,
                utility = Utility.CLARO_HOME,
                connectionMethod = UtilityConnectionMethod.SCRAPING,
            ),
        )
        utilityAccountRepository.save(
            utilityAccountLight.copy(
                id = UtilityAccountId("3"),
                status = UtilityAccountConnectionStatus.CONNECTED,
                lastDueDateFound = null,
                connectionMethod = UtilityConnectionMethod.FLOW,
            ),
        )
        utilityAccountRepository.save(
            utilityAccountLight.copy(
                id = UtilityAccountId("4"),
                status = UtilityAccountConnectionStatus.CONNECTED,
                lastDueDateFound = today.minusDays(5),
                connectionMethod = UtilityConnectionMethod.FLOW,
            ),
        )
        utilityAccountRepository.save(
            utilityAccountLight.copy(
                id = UtilityAccountId("5"),
                status = UtilityAccountConnectionStatus.CONNECTED,
                lastDueDateFound = today.minusDays(16),
                connectionMethod = UtilityConnectionMethod.FLOW,
            ),
        )

        utilityAccountRepository.save(
            utilityAccountLight.copy(
                id = UtilityAccountId("6"),
                status = UtilityAccountConnectionStatus.CONNECTED,
                lastDueDateFound = today.plusDays(90),
                lastBillFound = today.minusMonths(1),
                connectionMethod = UtilityConnectionMethod.FLOW,
            ),
        )

        utilityAccountRepository.save(
            utilityAccountLight.copy(
                id = UtilityAccountId("7"),
                status = UtilityAccountConnectionStatus.CONNECTED,
                lastDueDateFound = today.minusDays(5),
                lastBillFound = today.minusDays(20),
                connectionMethod = UtilityConnectionMethod.FLOW,
            ),
        )

        val utilityAccounts = utilityAccountService.findAllConnectedByFlow()

        utilityAccounts
            .map { it.id }
            .shouldContainOnly(UtilityAccountId("5"), UtilityAccountId("3"), UtilityAccountId("6"), UtilityAccountId("7"))
    }

    @Test
    fun `deve retornar somente as contas de consumo habilitadas pra buscar contas`() {
        val today = getLocalDate()

        val utilityAccountLight = utilityAccount.copy(
            utility = Utility.LIGHT_RJ,
            connectionDetails = UtilityAccountConnectionDetails.create(Utility.LIGHT_RJ, mapOf("cpf" to DOCUMENT, "password" to "password", "installationNumber" to "1234")),
        )
        val utilityAccountSabesp = utilityAccount.copy(
            utility = Utility.SABESP,
            connectionDetails = UtilityAccountConnectionDetails.create(Utility.SABESP, mapOf("login" to "login", "password" to "password")),
        )

        utilityAccountRepository.save(
            utilityAccountSabesp.copy(
                id = UtilityAccountId("1"),
                status = UtilityAccountConnectionStatus.CONNECTED,
                lastDueDateFound = today.minusDays(16),
                connectionMethod = UtilityConnectionMethod.FLOW,
            ),
        )
        utilityAccountRepository.save(
            utilityAccount.copy(
                id = UtilityAccountId("2"),
                status = UtilityAccountConnectionStatus.CONNECTED,
                utility = Utility.CLARO_HOME,
                lastDueDateFound = null,
                connectionMethod = UtilityConnectionMethod.FLOW,
            ),
        )
        utilityAccountRepository.save(
            utilityAccountLight.copy(
                id = UtilityAccountId("3"),
                status = UtilityAccountConnectionStatus.CONNECTED,
                lastDueDateFound = null,
                connectionMethod = UtilityConnectionMethod.FLOW,
            ),
        )
        utilityAccountRepository.save(
            utilityAccountSabesp.copy(
                id = UtilityAccountId("4"),
                status = UtilityAccountConnectionStatus.CONNECTED,
                lastDueDateFound = null,
                connectionMethod = UtilityConnectionMethod.FLOW,
            ),
        )
        utilityAccountRepository.save(
            utilityAccountLight.copy(
                id = UtilityAccountId("5"),
                status = UtilityAccountConnectionStatus.CONNECTED,
                lastDueDateFound = today.minusDays(16),
                connectionMethod = UtilityConnectionMethod.FLOW,
            ),
        )

        utilityAccountRepository.save(
            utilityAccountLight.copy(
                id = UtilityAccountId("6"),
                status = UtilityAccountConnectionStatus.CONNECTED,
                lastDueDateFound = today.plusDays(90),
                lastBillFound = today.minusMonths(1),
                connectionMethod = UtilityConnectionMethod.FLOW,
            ),
        )

        utilityAccountRepository.save(
            utilityAccountLight.copy(
                id = UtilityAccountId("7"),
                status = UtilityAccountConnectionStatus.CONNECTED,
                lastDueDateFound = today.minusDays(5),
                lastBillFound = today.minusDays(20),
                connectionMethod = UtilityConnectionMethod.FLOW,
            ),
        )

        every { utilityAccountService.shouldDisableRequestInvoices(Utility.SABESP) } returns true

        val utilityAccounts = utilityAccountService.findAllConnectedByFlow()

        utilityAccounts
            .map { it.id }
            .shouldContainOnly(UtilityAccountId("5"), UtilityAccountId("2"), UtilityAccountId("3"), UtilityAccountId("6"), UtilityAccountId("7"))
    }

    @Test
    fun `não deve permitiar a criação de uma nova conexão caso os dados de conexão sejam iguais`() {
        utilityAccountRepository.save(
            utilityAccount.copy(
                id = UtilityAccountId("2"),
                status = UtilityAccountConnectionStatus.CONNECTED,
                utility = Utility.CLARO_HOME,
                connectionMethod = UtilityConnectionMethod.SCRAPING,
            ),
        )

        val response = utilityAccountService.create(
            UtilityAccountConnectionDetails.create(
                utility = Utility.CLARO_HOME,
                details = mapOf("login" to "test", "password" to "1234"),
            ),
            walletFounderAccountId = wallet.founder.accountId,
            utility = Utility.CLARO_HOME,
            connectionMethod = UtilityConnectionMethod.SCRAPING,
            walletId = wallet.id,
            currentAccountId = wallet.founder.accountId,
        )

        response.isLeft().shouldBeTrue()
        response.mapLeft {
            it.shouldBeTypeOf<UtilityAccountError.AccountAlreadyCreated>()
        }
    }

    @ParameterizedTest
    @EnumSource(value = UtilityAccountConnectionStatus::class, mode = EnumSource.Mode.INCLUDE, names = ["CONNECTED", "PENDING"])
    fun `ao requisitar reconexão deve notificar usuário`(status: UtilityAccountConnectionStatus) {
        every { notificationAdapter.notifyUtilityAccountRequestReconnection(any()) } returns Unit

        utilityAccountRepository.save(utilityAccount.copy(status = status))

        val result = utilityAccountService.requestReconnection(utilityAccount.id, utilityAccount.walletId)

        result.isRight() shouldBe true

        verify {
            notificationAdapter.notifyUtilityAccountRequestReconnection(any())
        }

        val utilityAccountFromDb = utilityAccountRepository.findById(utilityAccount.id, utilityAccount.walletId)

        (utilityAccountFromDb == null) shouldBe false
        utilityAccountFromDb!!.status shouldBe UtilityAccountConnectionStatus.INVALID_CREDENTIALS
    }

    @ParameterizedTest
    @EnumSource(value = UtilityAccountConnectionStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["CONNECTED", "PENDING"])
    fun `não deve requisitar reconexão de uma UA com status diferente dos esperados`(status: UtilityAccountConnectionStatus) {
        utilityAccountRepository.save(utilityAccount.copy(status = status))

        val result = utilityAccountService.requestReconnection(utilityAccount.id, utilityAccount.walletId)

        result.isLeft() shouldBe true
        result.leftOrNull() shouldBe UtilityAccountError.CannotConnectAccount

        verify(exactly = 0) {
            notificationAdapter.notifyUtilityAccountRequestReconnection(any())
        }
    }

    @Test
    fun `não deve requisitar reconexão se a UA não existe`() {
        val result = utilityAccountService.requestReconnection(UtilityAccountId("utility account id that doesnt exist"), utilityAccount.walletId)

        result.isLeft() shouldBe true
        result.leftOrNull() shouldBe UtilityAccountError.AccountNotFound

        verify(exactly = 0) {
            notificationAdapter.notifyUtilityAccountRequestReconnection(any())
        }
    }

    @Test
    fun `ao reconectar deve criar uma nova conexão e desconectar a antiga`() {
        utilityAccountRepository.save(utilityAccount.copy(utility = Utility.LIGHT_RJ, status = UtilityAccountConnectionStatus.INVALID_CREDENTIALS))

        val result = utilityAccountService.reconnect(
            utilityAccount.id,
            wallet.founder.accountId,
            wallet,
            mapOf(
                "cpf" to DOCUMENT,
                "password" to "meu_novo_password",
                "installationNumber" to "42069",
            ),
        )

        result.isRight() shouldBe true

        val newUa = result.getOrNull()!!

        newUa.id shouldNotBe utilityAccount.id

        val uaFromDb = utilityAccountRepository.find(newUa.id)

        uaFromDb.shouldNotBeNull()
        uaFromDb.utility shouldBe Utility.LIGHT_RJ
        uaFromDb.status shouldBe UtilityAccountConnectionStatus.PENDING

        (uaFromDb.connectionDetails is UtilityAccountConnectionDetails.WithCpfInstallationNumberAndPassword) shouldBe true
        (uaFromDb.connectionDetails as UtilityAccountConnectionDetails.WithCpfInstallationNumberAndPassword).let {
            it.cpf shouldBe DOCUMENT
            it.password shouldBe "ENCRYPTED_DATA"
            it.installationNumber shouldBe "42069"
        }

        val oldUa = utilityAccountRepository.find(utilityAccount.id)!!

        oldUa.status shouldBe UtilityAccountConnectionStatus.DISCONNECTED
    }

    @Test
    fun `ao tentar reconectar uma UA inexistente deve dar erro`() {
        val result = utilityAccountService.reconnect(
            UtilityAccountId("ua-que-nao-existe"),
            wallet.founder.accountId,
            wallet,
            mapOf(
                "cpf" to DOCUMENT,
                "password" to "meu_novo_password",
                "installationNumber" to "42069",
            ),
        )

        result.isLeft() shouldBe true

        result.leftOrNull()!! shouldBe UtilityAccountError.AccountNotFound
    }

    @Nested
    inner class NotifyInvoicesNotFound {
        @ParameterizedTest
        @ValueSource(ints = [10, 40, 70, 100, 310])
        fun `usuário que tenha exatamente (10, 40, 70, 100, 310) dias de erros consecutivos deve ser notificado com a mensagem de erro`(scanCount: Int) {
            every { notificationAdapter.notifyInvoicesScanErrorUtilityAccount(any()) } returns Unit

            val utilityAccount = utilityAccount.copy(
                utility = Utility.LIGHT_RJ,
                status = UtilityAccountConnectionStatus.CONNECTED,
                lastDueDateFound = getZonedDateTime().minusDays(30).toLocalDate(),
                lastBillFound = getZonedDateTime().minusDays(25).toLocalDate(),
                scanFailureCount = scanCount,
            )

            utilityAccountRepository.save(utilityAccount)

            utilityAccountDailyUpdateJob.execute()

            val slot = slot<UtilityAccount>()

            verify {
                notificationAdapter.notifyInvoicesScanErrorUtilityAccount(capture(slot))
            }

            slot.captured.id shouldBe utilityAccount.id

            verify(exactly = 0) { notificationAdapter.notifyInvoicesNotFoundUtilityAccount(any()) }
        }

        @ParameterizedTest
        @ValueSource(ints = [4, 9, 11, 41, 69, 71, 99, 101, 309, 311])
        fun `usuário que não tenha exatamente (10, 40, 70, 100, 310) dias de erros consecutivos não deve ser notificado com a mensagem de erro`(scanCount: Int) {
            every { notificationAdapter.notifyInvoicesNotFoundUtilityAccount(any()) } returns Unit

            val utilityAccount = utilityAccount.copy(
                utility = Utility.LIGHT_RJ,
                status = UtilityAccountConnectionStatus.CONNECTED,
                lastDueDateFound = getZonedDateTime().minusDays(25).toLocalDate(),
                lastBillFound = getZonedDateTime().minusDays(25).toLocalDate(),
                scanFailureCount = scanCount,
            )

            utilityAccountRepository.save(utilityAccount)

            utilityAccountDailyUpdateJob.execute()

            verify(exactly = 0) {
                notificationAdapter.notifyInvoicesScanErrorUtilityAccount(any())
            }

            if (scanCount > 10) {
                verify(exactly = 0) {
                    notificationAdapter.notifyInvoicesNotFoundUtilityAccount(any())
                }
            }
        }

        @ParameterizedTest
        @ValueSource(strings = ["2024-01-30", "2024-02-04", "2024-03-04", "2024-03-30", "2024-04-04", "2024-04-30", "2024-05-04"])
        fun `uauário que já achamos contas com 25 e 30 dias depois do últimmo vencimento deve ser notificado com a mensagem de conta não encontrada`(today: String) {
            every { notificationAdapter.notifyInvoicesNotFoundUtilityAccount(any()) } returns Unit

            val date = LocalDateTime.of(2024, 1, 5, 6, 0, 0)

            val utilityAccount = utilityAccount.copy(
                utility = Utility.LIGHT_RJ,
                status = UtilityAccountConnectionStatus.CONNECTED,
                lastDueDateFound = date.toLocalDate(),
                lastBillFound = date.toLocalDate(),
                scanFailureCount = 0,
            )

            utilityAccountRepository.save(utilityAccount)

            withGivenDateTime(time = parseDateTextAsZonedDateTime(today)) {
                utilityAccountDailyUpdateJob.execute()
            }

            val slot = slot<UtilityAccount>()

            verify {
                notificationAdapter.notifyInvoicesNotFoundUtilityAccount(capture(slot))
            }

            slot.captured.id shouldBe utilityAccount.id

            verify(exactly = 0) {
                notificationAdapter.notifyInvoicesScanErrorUtilityAccount(any())
            }
        }

        @ParameterizedTest
        @CsvSource(value = ["-6,0", "24,0", "29,0", "26,1", "22,1", "10,2", "11,2", "18,3", "15,3"])
        fun `uauário que já achamos contas com qualquer dia diferente de 25 e 30 depois do últimmo vencimento não deve ser notificado com a mensagem de conta não encontrada`(day: Long, month: Long) {
            val date = LocalDateTime.of(2024, 1, 5, 6, 0, 0)

            val utilityAccount = utilityAccount.copy(
                utility = Utility.LIGHT_RJ,
                status = UtilityAccountConnectionStatus.CONNECTED,
                lastDueDateFound = date.toLocalDate(),
                lastBillFound = date.toLocalDate(),
                scanFailureCount = 0,
            )

            utilityAccountRepository.save(utilityAccount)

            withGivenDateTime(time = ZonedDateTime.of(date.plusDays(day).plusMonths(month), ZoneId.of("Brazil/East"))) {
                utilityAccountDailyUpdateJob.execute()
            }

            verify(exactly = 0) {
                notificationAdapter.notifyInvoicesNotFoundUtilityAccount(any())
            }

            verify(exactly = 0) {
                notificationAdapter.notifyInvoicesScanErrorUtilityAccount(any())
            }
        }

        @Test
        fun `quando o ultimo pagamento adicionado tem menos de 5 dias em relacao a data de vencimento, nao deve notificar`() {
            val utilityAccount = utilityAccount.copy(
                utility = Utility.LIGHT_RJ,
                status = UtilityAccountConnectionStatus.CONNECTED,
                lastDueDateFound = LocalDate.of(2024, 2, 2),
                lastBillFound = LocalDate.of(2024, 1, 18),
                scanFailureCount = 0,
            )

            utilityAccountRepository.save(utilityAccount)

            withGivenDateTime(ZonedDateTime.of(LocalDate.of(2024, 2, 3), LocalTime.NOON, ZoneId.of("Brazil/East"))) {
                utilityAccountDailyUpdateJob.execute()
            }

            verify(exactly = 0) {
                notificationAdapter.notifyInvoicesNotFoundUtilityAccount(any())
            }
        }

        @ParameterizedTest
        @CsvSource(value = ["15,0", "10,0", "15,1", "10,1", "15,2", "10,2", "15,3", "10,3"])
        fun `usuário que nunca achamos conta com 10 e 15 dias depois de conectar deve ser notificado com a mensagem de conta não encontrada`(day: Long, month: Long) {
            every { notificationAdapter.notifyInvoicesNotFoundUtilityAccount(any()) } returns Unit

            val date = LocalDateTime.of(2024, 1, 5, 6, 0, 0)

            val utilityAccount = utilityAccount.copy(
                utility = Utility.LIGHT_RJ,
                status = UtilityAccountConnectionStatus.CONNECTED,
                createdAt = ZonedDateTime.of(date, ZoneId.of("Brazil/East")),
                lastDueDateFound = null,
                lastBillFound = null,
                scanFailureCount = 0,
            )

            utilityAccountRepository.save(utilityAccount)

            withGivenDateTime(time = ZonedDateTime.of(date.plusDays(day).plusMonths(month), ZoneId.of("Brazil/East"))) {
                utilityAccountDailyUpdateJob.execute()
            }

            val slot = slot<UtilityAccount>()

            verify {
                notificationAdapter.notifyInvoicesNotFoundUtilityAccount(capture(slot))
            }

            slot.captured.id shouldBe utilityAccount.id

            verify(exactly = 0) {
                notificationAdapter.notifyInvoicesScanErrorUtilityAccount(any())
            }
        }

        @ParameterizedTest
        @CsvSource(value = ["9,0", "29,0", "14,1", "22,1", "3,2", "11,2", "18,3", "1,3"])
        fun `usuário que nunca achamos conta com qualquer dia diferente de 10 e 15 depois de conectar não deve ser notificado com a mensagem de conta não encontrada`(day: Long, month: Long) {
            val date = LocalDateTime.of(2024, 1, 5, 6, 0, 0)

            val utilityAccount = utilityAccount.copy(
                utility = Utility.LIGHT_RJ,
                status = UtilityAccountConnectionStatus.CONNECTED,
                createdAt = ZonedDateTime.of(date, ZoneId.of("Brazil/East")),
                lastDueDateFound = null,
                lastBillFound = null,
                scanFailureCount = 0,
            )

            utilityAccountRepository.save(utilityAccount)

            withGivenDateTime(time = ZonedDateTime.of(date.plusDays(day).plusMonths(month), ZoneId.of("Brazil/East"))) {
                utilityAccountDailyUpdateJob.execute()
            }

            verify(exactly = 0) {
                notificationAdapter.notifyInvoicesNotFoundUtilityAccount(any())
            }

            verify(exactly = 0) {
                notificationAdapter.notifyInvoicesScanErrorUtilityAccount(any())
            }
        }

        @ParameterizedTest
        @EnumSource(
            value = Utility::class,
            mode = EnumSource.Mode.MATCH_ALL,
        )
        fun `usuário que teve mais de 5 invalid credentials em tentativas de achar conta deve ter seu status alterado para INVALID_CREDENTIALS`(
            utility: Utility,
        ) {
            every { notificationAdapter.notifyUtilityAccountUpdatedStatus(any()) } returns Unit
            val date = LocalDateTime.of(2024, 1, 5, 6, 0, 0)

            val utilityAccount = utilityAccount.copy(
                utility = utility,
                status = UtilityAccountConnectionStatus.CONNECTED,
                createdAt = ZonedDateTime.of(date, ZoneId.of("Brazil/East")),
                lastDueDateFound = null,
                lastBillFound = null,
                scanFailureCount = 10,
                invalidCredentialsCount = 6,
            )

            utilityAccountRepository.save(utilityAccount)

            utilityAccountDailyUpdateJob.execute()

            verify(exactly = 1) {
                notificationAdapter.notifyUtilityAccountUpdatedStatus(any())
            }

            val uaFromDb = utilityAccountRepository.find(utilityAccount.id)
            uaFromDb.shouldNotBeNull()
            uaFromDb.status shouldBe UtilityAccountConnectionStatus.INVALID_CREDENTIALS

            verify(exactly = 0) {
                notificationAdapter.notifyInvoicesNotFoundUtilityAccount(any())
            }

            verify(exactly = 0) {
                notificationAdapter.notifyInvoicesScanErrorUtilityAccount(any())
            }
        }

        @ParameterizedTest
        @EnumSource(
            value = Utility::class,
            mode = EnumSource.Mode.MATCH_ALL,
        )
        fun `usuário que não teve mais de 5 invalid credentials em tentativas de achar conta não deve ter seu status alterado para INVALID_CREDENTIALS`(
            utility: Utility,
        ) {
            every { notificationAdapter.notifyUtilityAccountUpdatedStatus(any()) } returns Unit
            val date = LocalDateTime.of(2024, 1, 5, 6, 0, 0)

            val utilityAccount = utilityAccount.copy(
                utility = utility,
                status = UtilityAccountConnectionStatus.CONNECTED,
                createdAt = ZonedDateTime.of(date, ZoneId.of("Brazil/East")),
                lastDueDateFound = null,
                lastBillFound = null,
                scanFailureCount = 0,
                invalidCredentialsCount = 4,
            )

            utilityAccountRepository.save(utilityAccount)

            utilityAccountDailyUpdateJob.execute()

            verify(exactly = 0) {
                notificationAdapter.notifyUtilityAccountUpdatedStatus(any())
            }

            val uaFromDb = utilityAccountRepository.find(utilityAccount.id)
            uaFromDb.shouldNotBeNull()
            uaFromDb.status shouldBe UtilityAccountConnectionStatus.CONNECTED
        }
    }
}