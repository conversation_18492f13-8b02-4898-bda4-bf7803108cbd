package ai.friday.billpayment.app.utilityaccount

import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.utility
import io.kotest.matchers.shouldBe
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class UtilityTest {

    @ParameterizedTest
    @CsvSource(
        value = [
            "848500000005498901622024310151636687630004121228, *********, CLARO_MOBILE",
            "848700000009449001622020306151589435782006121224, *********, CLARO_MOBILE",
            "848200000000214101622026305221229884669103211239, *********, CLARO_MOBILE",
            "36490000270001926180900000858100400000000009000, null, UNKNOWN",
            "836600000035081600531073483486061110100540825419, null, LIGHT_RJ",
        ],
        nullValues = ["null"],
    )
    fun `extract client number from barcode`(barcodeText: String, clientNumber: String?, utilityName: String) {
        val barcode = BarCode.ofDigitable(barcodeText)
        val utility = barcode.utility()
        utility.name shouldBe utilityName
        utility.extractClientNumberFrom(barcode) shouldBe clientNumber
    }
}