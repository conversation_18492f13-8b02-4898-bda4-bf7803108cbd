package ai.friday.billpayment.app.utilityaccount

import ai.friday.billpayment.integration.DOCUMENT
import io.kotest.matchers.types.shouldBeInstanceOf
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class UtilityAccountConnectionDetailsTest {
    @ParameterizedTest
    @EnumSource(Utility::class, mode = EnumSource.Mode.INCLUDE, names = ["VIVO_MOBILE", "CLARO_HOME", "CLARO_MOBILE", "TIM_MOBILE", "ENEL_SP", "SABESP", "COMGAS", "OI_HOME"])
    fun `deve criar o connection details como Login e Password`(utility: Utility) {
        val connectionDetails = UtilityAccountConnectionDetails.create(utility, mapOf("login" to "login", "password" to "password"))
        connectionDetails.shouldBeInstanceOf<UtilityAccountConnectionDetails.WithLoginAndPassword>()
    }

    @ParameterizedTest
    @EnumSource(Utility::class, mode = EnumSource.Mode.INCLUDE, names = ["LIGHT_RJ", "CEMIG"])
    fun `deve criar como CPF, Password e Installation Number`(utility: Utility) {
        val connectionDetails = UtilityAccountConnectionDetails.create(utility, mapOf("cpf" to DOCUMENT, "password" to "password", "installationNumber" to "installationNumber"))
        connectionDetails.shouldBeInstanceOf<UtilityAccountConnectionDetails.WithCpfInstallationNumberAndPassword>()
    }

    @ParameterizedTest
    @EnumSource(Utility::class, mode = EnumSource.Mode.INCLUDE, names = ["NATURGY"])
    fun `deve criar como CPF e Client Number`(utility: Utility) {
        val connectionDetails = UtilityAccountConnectionDetails.create(utility, mapOf("cpf" to DOCUMENT, "password" to "password", "clientNumber" to "clientNumber"))
        connectionDetails.shouldBeInstanceOf<UtilityAccountConnectionDetails.WithCpfPasswordAndClientNumber>()
    }

    @ParameterizedTest
    @EnumSource(Utility::class, mode = EnumSource.Mode.INCLUDE, names = ["LIGHT_RJ", "CEMIG"])
    fun `deve criar o connection details também como Login e Password para concessionarias com suporte a conexão legada`(utility: Utility) {
        val connectionDetails = UtilityAccountConnectionDetails.create(utility, mapOf("login" to "login", "password" to "password"))
        connectionDetails.shouldBeInstanceOf<UtilityAccountConnectionDetails.WithLoginAndPassword>()
    }

    @Test
    fun `deve criar a claro móvel com multiplos valores`() {
        val connectionDetails = UtilityAccountConnectionDetails.create(Utility.CLARO_MOBILE, mapOf("emailOrCpf" to "login", "password" to "password"))
        connectionDetails.shouldBeInstanceOf<UtilityAccountConnectionDetails.WithMultipleLoginAndPassword>()

        val connectionDetails2 = UtilityAccountConnectionDetails.create(Utility.CLARO_MOBILE, mapOf("loginType" to "loginType", "loginValue" to "loginValue", "password" to "password"))
        connectionDetails2.shouldBeInstanceOf<UtilityAccountConnectionDetails.WithMultipleLoginAndPassword>()
    }

    @Test
    fun `deve retornar erro caso o cpf passado seja invalido`() {
        assertThrows<IllegalArgumentException> { UtilityAccountConnectionDetails.create(Utility.LIGHT_RJ, mapOf("cpf" to "cpf", "installationCode" to "1234")) }
    }
}