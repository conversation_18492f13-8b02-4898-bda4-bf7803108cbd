package ai.friday.billpayment.app.subscription

import io.kotest.matchers.shouldBe
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class InactivationFlowDatesTest {
    @ParameterizedTest
    @CsvSource(
        value = [
            "2024-01-10, 2024-01-10, 2024-01-11, 2024-01-12, 2024-01-15, 2024-01-17, 2024-02-12, 2024-02-13, 2024-02-14, 2024-02-19",
            "2024-02-10, 2024-02-12, 2024-02-13, 2024-02-14, 2024-02-16, 2024-02-20, 2024-03-11, 2024-03-12, 2024-03-13, 2024-03-18",
            "2024-03-10, 2024-03-11, 2024-03-12, 2024-03-13, 2024-03-15, 2024-03-19, 2024-04-10, 2024-04-11, 2024-04-12, 2024-04-16",
            "2024-04-10, 2024-04-10, 2024-04-11, 2024-04-12, 2024-04-15, 2024-04-17, 2024-05-10, 2024-05-13, 2024-05-14, 2024-05-16",
            "2024-05-10, 2024-05-10, 2024-05-13, 2024-05-14, 2024-05-15, 2024-05-17, 2024-06-10, 2024-06-11, 2024-06-12, 2024-06-17",
            "2024-06-10, 2024-06-10, 2024-06-11, 2024-06-12, 2024-06-14, 2024-06-18, 2024-07-10, 2024-07-11, 2024-07-12, 2024-07-16",
            "2024-07-10, 2024-07-10, 2024-07-11, 2024-07-12, 2024-07-15, 2024-07-17, 2024-08-12, 2024-08-13, 2024-08-14, 2024-08-19",
            "2024-08-10, 2024-08-12, 2024-08-13, 2024-08-14, 2024-08-16, 2024-08-20, 2024-09-10, 2024-09-11, 2024-09-12, 2024-09-16",
            "2024-09-10, 2024-09-10, 2024-09-11, 2024-09-12, 2024-09-16, 2024-09-18, 2024-10-10, 2024-10-11, 2024-10-14, 2024-10-16",
            "2024-10-10, 2024-10-10, 2024-10-11, 2024-10-14, 2024-10-15, 2024-10-17, 2024-11-11, 2024-11-12, 2024-11-13, 2024-11-18",
            "2024-11-10, 2024-11-11, 2024-11-12, 2024-11-13, 2024-11-15, 2024-11-19, 2024-12-10, 2024-12-11, 2024-12-12, 2024-12-16",
            "2024-12-10, 2024-12-10, 2024-12-11, 2024-12-12, 2024-12-16, 2024-12-18, 2025-01-10, 2025-01-13, 2025-01-14, 2025-01-16",
            "2025-01-10, 2025-01-10, 2025-01-13, 2025-01-14, 2025-01-15, 2025-01-17, 2025-02-10, 2025-02-11, 2025-02-12, 2025-02-17",
            "2025-02-10, 2025-02-10, 2025-02-11, 2025-02-12, 2025-02-14, 2025-02-18, 2025-03-10, 2025-03-11, 2025-03-12, 2025-03-17",
            "2025-03-10, 2025-03-10, 2025-03-11, 2025-03-12, 2025-03-14, 2025-03-18, 2025-04-10, 2025-04-11, 2025-04-14, 2025-04-16",
            "2025-04-10, 2025-04-10, 2025-04-11, 2025-04-14, 2025-04-15, 2025-04-17, 2025-05-12, 2025-05-13, 2025-05-14, 2025-05-19",
            "2025-05-10, 2025-05-12, 2025-05-13, 2025-05-14, 2025-05-16, 2025-05-20, 2025-06-10, 2025-06-11, 2025-06-12, 2025-06-16",
            "2025-06-10, 2025-06-10, 2025-06-11, 2025-06-12, 2025-06-16, 2025-06-18, 2025-07-10, 2025-07-11, 2025-07-14, 2025-07-16",
            "2025-07-10, 2025-07-10, 2025-07-11, 2025-07-14, 2025-07-15, 2025-07-17, 2025-08-11, 2025-08-12, 2025-08-13, 2025-08-18",
            "2025-08-10, 2025-08-11, 2025-08-12, 2025-08-13, 2025-08-15, 2025-08-19, 2025-09-10, 2025-09-11, 2025-09-12, 2025-09-16",
            "2025-09-10, 2025-09-10, 2025-09-11, 2025-09-12, 2025-09-15, 2025-09-17, 2025-10-10, 2025-10-13, 2025-10-14, 2025-10-16",
            "2025-10-10, 2025-10-10, 2025-10-13, 2025-10-14, 2025-10-15, 2025-10-17, 2025-11-10, 2025-11-11, 2025-11-12, 2025-11-17",
            "2025-11-10, 2025-11-10, 2025-11-11, 2025-11-12, 2025-11-14, 2025-11-18, 2025-12-10, 2025-12-11, 2025-12-12, 2025-12-16",
            "2025-12-10, 2025-12-10, 2025-12-11, 2025-12-12, 2025-12-15, 2025-12-17, 2026-01-12, 2026-01-13, 2026-01-14, 2026-01-19",
        ],
    )
    fun calcIncativationFlowDates(dueDate: String, m0d0: String, m0d1: String, m0d2: String, m0d4: String, m0d7: String, m1d0: String, m1d1: String, m1d2: String, m1d6: String) {
        val dateFormat: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")

        val dates = InactivationFlowDates.from(LocalDate.parse(dueDate, dateFormat))

        dates.month0Day0.format(dateFormat) shouldBe m0d0
        dates.month0Day1.format(dateFormat) shouldBe m0d1
        dates.month0Day2.format(dateFormat) shouldBe m0d2
        dates.month0Day4.format(dateFormat) shouldBe m0d4
        dates.month0Day7.format(dateFormat) shouldBe m0d7

        dates.month1Day0.format(dateFormat) shouldBe m1d0
        dates.month1Day1.format(dateFormat) shouldBe m1d1
        dates.month1Day2.format(dateFormat) shouldBe m1d2
        dates.month1Day6.format(dateFormat) shouldBe m1d6
    }
}