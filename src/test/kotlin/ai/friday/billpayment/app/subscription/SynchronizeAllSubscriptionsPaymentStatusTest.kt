package ai.friday.billpayment.app.subscription

import DynamoDBUtils
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InAppSubscriptionDbRepository
import ai.friday.billpayment.adapters.dynamodb.InAppSubscriptionDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SubscriptionDbRepository
import ai.friday.billpayment.adapters.dynamodb.SubscriptionDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.app.account.AccountConfigurationService
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentStatus
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.chatbot.ChatbotMessagePublisher
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.inappsubscription.InAppSubscription
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionAccessConcessionId
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionAccessConcessionRepository
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionAdapter
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionEventRepository
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionProductId
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionReason
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionService
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStatus
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStore
import ai.friday.billpayment.app.inappsubscription.instrumentation.InAppSubscriptionInstrumentationService
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.recurrence.BillRecurrenceService
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.shouldBe
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class SynchronizeAllSubscriptionsPaymentStatusTest {
    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val enhancedClient = DynamoDBUtils.setupDynamoDB()

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )
    private val subscriptionDynamoDAO = SubscriptionDynamoDAO(enhancedClient)
    private val subscriptionDbRepository = SubscriptionDbRepository(client = subscriptionDynamoDAO)
    private val inAppSubscriptionDynamoDAO = InAppSubscriptionDynamoDAO(enhancedClient)
    private val inAppSubscriptionRepository = InAppSubscriptionDbRepository(client = inAppSubscriptionDynamoDAO)
    private val inAppSubscriptionAdapter: InAppSubscriptionAdapter = mockk(relaxed = true)
    private val recurrenceService: BillRecurrenceService = mockk(relaxed = true)
    private val notificationAdapter: NotificationAdapter = mockk(relaxed = true)
    private val subscriptionAccessConcessionRepository: InAppSubscriptionAccessConcessionRepository = mockk(relaxed = true)
    private val inAppSubscriptionEventRepository: InAppSubscriptionEventRepository = mockk(relaxed = true)
    private val inAppSubscriptionAccessConcessionId = InAppSubscriptionAccessConcessionId("testeId")
    private val instrumentationService: InAppSubscriptionInstrumentationService = mockk(relaxed = true)
    private val subscriptionConfiguration: SubscriptionConfiguration = mockk(relaxed = true)
    private val billRepository: BillRepository = mockk()
    private val accountConfigurationService: AccountConfigurationService = mockk(relaxed = true)
    private val ddaService: DDAService = mockk(relaxed = true)
    private val chatbotMessagePublisher: ChatbotMessagePublisher = mockk(relaxed = true)

    private val accountService = AccountService(
        accountConfigurationService = mockk(relaxed = true),
        accountRepository = accountRepository,
        chatbotMessagePublisher = chatbotMessagePublisher,
        crmService = mockk(relaxed = true),
        notificationAdapter = mockk(relaxed = true),
        walletRepository = mockk(relaxed = true),
    )

    private val inAppSubscriptionService = InAppSubscriptionService(
        crmService = mockk(relaxed = true),
        notificationAdapter = notificationAdapter,
        subscriptionRepository = inAppSubscriptionRepository,
        subscriptionAccessConcessionRepository = subscriptionAccessConcessionRepository,
        subscriptionEventRepository = inAppSubscriptionEventRepository,
        accountService = accountService,
        instrumentationService = instrumentationService,
        subscriptionAdapter = inAppSubscriptionAdapter,
    )

    private val subscriptionService = SubscriptionService(
        accountRepository = accountRepository,
        subscriptionRepository = subscriptionDbRepository,
        recurrenceService = recurrenceService,
        billRepository = billRepository,
        subscriptionConfiguration = subscriptionConfiguration,
        notificationAdapter = notificationAdapter,
        accountConfigurationService = accountConfigurationService,
        ddaService = ddaService,
        closeAccountServiceProvider = mockk(relaxed = true),
        disableOverdueAccountDDA = true,
        closeOverdueAccount = true,
        intercomAdapter = mockk(relaxed = true),
        inAppSubscriptionService = inAppSubscriptionService,
        chatbotMessagePublisher = chatbotMessagePublisher,
        subscritionDiscountInterfaceProvider = mockk(),
        recurrenceRepository = mockk(),
        billEventRepository = mockk(),
    )

    @BeforeEach
    fun setup() {
        loadAccountIntoDb(dynamoDB, subscriptionType = SubscriptionType.IN_APP)
    }

    @Test
    fun `altera status da subscription expirada há 2 dias ou mais caso não tenha passado no dia anterior`() {
        inAppSubscriptionRepository.save(
            InAppSubscription(
                accountId = AccountId(ACCOUNT_ID),
                status = InAppSubscriptionStatus.ACTIVE,
                endsAt = getZonedDateTime().minusDays(2),
                store = null,
                reason = InAppSubscriptionReason.NO_STORE_COUPON,
                inAppSubscriptionAccessConcessionId = inAppSubscriptionAccessConcessionId,
                autoRenew = false,
                price = 0,
                productId = InAppSubscriptionProductId("fakeProductId"),
                offStoreProductId = null,
            ),
        )

        subscriptionService.synchronizeAllInAppSubscriptionStatusActive() shouldBe 1

        val account = accountRepository.findByIdOrNull(AccountId(ACCOUNT_ID))
        val inAppSubscription = account?.let { inAppSubscriptionRepository.find(it.accountId) }

        verify { chatbotMessagePublisher.publishStateUpdate(any(), any(), any()) }

        inAppSubscription?.status shouldBe InAppSubscriptionStatus.EXPIRED
        account?.status shouldBe AccountStatus.BLOCKED
        account?.paymentStatus shouldBe AccountPaymentStatus.PastDue
    }

    @Test
    fun `não altera status para subscription expirada há 1 dia com status já expirada`() {
        inAppSubscriptionRepository.save(
            InAppSubscription(
                accountId = AccountId(ACCOUNT_ID),
                status = InAppSubscriptionStatus.EXPIRED,
                endsAt = getZonedDateTime().minusDays(1),
                store = null,
                reason = InAppSubscriptionReason.NO_STORE_COUPON,
                inAppSubscriptionAccessConcessionId = inAppSubscriptionAccessConcessionId,
                autoRenew = false,
                price = 0,
                productId = InAppSubscriptionProductId("fakeProductId"),
                offStoreProductId = null,
            ),
        )

        subscriptionService.synchronizeAllInAppSubscriptionStatusActive() shouldBe 0

        val account = accountRepository.findByIdOrNull(AccountId(ACCOUNT_ID))
        val inAppSubscription = account?.let { inAppSubscriptionRepository.find(it.accountId) }

        inAppSubscription?.status shouldBe InAppSubscriptionStatus.EXPIRED
        account?.status shouldBe AccountStatus.ACTIVE
        account?.paymentStatus shouldBe AccountPaymentStatus.UpToDate
    }

    @Test
    fun `não altera status para subscription expirada há 1 dia que foi criada pela loja`() {
        inAppSubscriptionRepository.save(
            InAppSubscription(
                accountId = AccountId(ACCOUNT_ID),
                status = InAppSubscriptionStatus.ACTIVE,
                endsAt = getZonedDateTime().minusDays(1),
                store = InAppSubscriptionStore.PLAY_STORE,
                reason = InAppSubscriptionReason.SUBSCRIPTION,
                inAppSubscriptionAccessConcessionId = inAppSubscriptionAccessConcessionId,
                autoRenew = false,
                price = 0,
                productId = InAppSubscriptionProductId("fakeProductId"),
                offStoreProductId = null,
            ),
        )

        subscriptionService.synchronizeAllInAppSubscriptionStatusActive() shouldBe 1

        val account = accountRepository.findByIdOrNull(AccountId(ACCOUNT_ID))
        val inAppSubscription = account?.let { inAppSubscriptionRepository.find(it.accountId) }

        inAppSubscription?.status shouldBe InAppSubscriptionStatus.ACTIVE
        account?.status shouldBe AccountStatus.ACTIVE
        account?.paymentStatus shouldBe AccountPaymentStatus.UpToDate
    }

    @Test
    fun `altera status para subscription expirada há 1 dia e altera o status da conta bara bloqueada`() {
        inAppSubscriptionRepository.save(
            InAppSubscription(
                accountId = AccountId(ACCOUNT_ID),
                status = InAppSubscriptionStatus.ACTIVE,
                endsAt = getZonedDateTime().minusDays(1),
                store = null,
                reason = InAppSubscriptionReason.NO_STORE_COUPON,
                inAppSubscriptionAccessConcessionId = inAppSubscriptionAccessConcessionId,
                autoRenew = false,
                price = 0,
                productId = InAppSubscriptionProductId("fakeProductId"),
                offStoreProductId = null,
            ),
        )

        subscriptionService.synchronizeAllInAppSubscriptionStatusActive() shouldBe 1

        val account = accountRepository.findByIdOrNull(AccountId(ACCOUNT_ID))
        val inAppSubscription = account?.let { inAppSubscriptionRepository.find(it.accountId) }

        verify { chatbotMessagePublisher.publishStateUpdate(any(), any(), any()) }

        inAppSubscription?.status shouldBe InAppSubscriptionStatus.EXPIRED
        account?.status shouldBe AccountStatus.BLOCKED
        account?.paymentStatus shouldBe AccountPaymentStatus.PastDue
    }

    @Test
    fun `altera account de uma subscription já expirada`() {
        inAppSubscriptionRepository.save(
            InAppSubscription(
                accountId = AccountId(ACCOUNT_ID),
                status = InAppSubscriptionStatus.EXPIRED,
                endsAt = getZonedDateTime().minusDays(1),
                store = null,
                reason = InAppSubscriptionReason.NO_STORE_COUPON,
                inAppSubscriptionAccessConcessionId = inAppSubscriptionAccessConcessionId,
                autoRenew = false,
                price = 0,
                productId = InAppSubscriptionProductId("fakeProductId"),
                offStoreProductId = null,
            ),
        )

        subscriptionService.synchronizeAllInAppSubscriptionStatusExpired() shouldBe 1

        verify { chatbotMessagePublisher.publishStateUpdate(any(), any(), any()) }

        val account = accountRepository.findByIdOrNull(AccountId(ACCOUNT_ID))
        val inAppSubscription = account?.let { inAppSubscriptionRepository.find(it.accountId) }

        inAppSubscription?.status shouldBe InAppSubscriptionStatus.EXPIRED
        account?.status shouldBe AccountStatus.BLOCKED
        account?.paymentStatus shouldBe AccountPaymentStatus.PastDue
    }

    @Test
    fun `altera account de uma subscription já expirada mesmo quando feita pela loja`() {
        inAppSubscriptionRepository.save(
            InAppSubscription(
                accountId = AccountId(ACCOUNT_ID),
                status = InAppSubscriptionStatus.EXPIRED,
                endsAt = getZonedDateTime().minusDays(1),
                store = null,
                reason = InAppSubscriptionReason.SUBSCRIPTION,
                inAppSubscriptionAccessConcessionId = inAppSubscriptionAccessConcessionId,
                autoRenew = false,
                price = 0,
                productId = InAppSubscriptionProductId("fakeProductId"),
                offStoreProductId = null,
            ),
        )

        subscriptionService.synchronizeAllInAppSubscriptionStatusExpired() shouldBe 1

        val account = accountRepository.findByIdOrNull(AccountId(ACCOUNT_ID))
        val inAppSubscription = account?.let { inAppSubscriptionRepository.find(it.accountId) }

        verify { chatbotMessagePublisher.publishStateUpdate(any(), any(), any()) }

        inAppSubscription?.status shouldBe InAppSubscriptionStatus.EXPIRED
        account?.status shouldBe AccountStatus.BLOCKED
        account?.paymentStatus shouldBe AccountPaymentStatus.PastDue
    }

    @Test
    fun `não altera account de uma subscription ainda ativa`() {
        inAppSubscriptionRepository.save(
            InAppSubscription(
                accountId = AccountId(ACCOUNT_ID),
                status = InAppSubscriptionStatus.ACTIVE,
                endsAt = getZonedDateTime().plusDays(1),
                store = null,
                reason = InAppSubscriptionReason.NO_STORE_COUPON,
                inAppSubscriptionAccessConcessionId = inAppSubscriptionAccessConcessionId,
                autoRenew = false,
                price = 0,
                productId = InAppSubscriptionProductId("fakeProductId"),
                offStoreProductId = null,
            ),
        )

        subscriptionService.synchronizeAllInAppSubscriptionStatusExpired() shouldBe 0

        val account = accountRepository.findByIdOrNull(AccountId(ACCOUNT_ID))
        val inAppSubscription = account?.let { inAppSubscriptionRepository.find(it.accountId) }

        inAppSubscription?.status shouldBe InAppSubscriptionStatus.ACTIVE
        account?.status shouldBe AccountStatus.ACTIVE
        account?.paymentStatus shouldBe AccountPaymentStatus.UpToDate
    }
}