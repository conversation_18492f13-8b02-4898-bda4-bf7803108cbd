package ai.friday.billpayment.app.subscription

import DynamoDBUtils
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SubscriptionDbRepository
import ai.friday.billpayment.adapters.dynamodb.SubscriptionDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountClosureDetails
import ai.friday.billpayment.app.account.AccountClosureReason
import ai.friday.billpayment.app.account.AccountConfiguration
import ai.friday.billpayment.app.account.AccountConfigurationService
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentStatus
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.CloseAccountResult
import ai.friday.billpayment.app.account.CloseAccountService
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.chatbot.ChatbotMessagePublisher
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.dda.DDAStatus
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.recurrence.BillRecurrence
import ai.friday.billpayment.app.recurrence.BillRecurrenceService
import ai.friday.billpayment.app.recurrence.CancelSubscriptionResult
import ai.friday.billpayment.app.recurrence.RecurrenceCreationError
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.recurrence.RecurrenceResult
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.getPaidBill
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.weeklyWalletRecurrenceNoEndDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.comparables.shouldBeGreaterThanOrEqualTo
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource
import org.junit.jupiter.params.provider.ValueSource

class SubscriptionServiceTest {

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val enhancedClient = DynamoDBUtils.setupDynamoDB()

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )
    private val subscriptionDynamoDAO = SubscriptionDynamoDAO(enhancedClient)
    private val subscriptionDbRepository = SubscriptionDbRepository(client = subscriptionDynamoDAO)
    private val recurrenceService: BillRecurrenceService = mockk(relaxed = true)

    private val defaultAmount = 990L
    private val defaultDay = 10

    private val subscriptionConfiguration = SubscriptionConfiguration(
        amount = defaultAmount,
        dayOfMonth = defaultDay,
        description = "Assinatura Friday",
        recipientName = "Friday",
        recipientDocument = "123456",
        bankAccount = SubscriptionConfiguration.BankAccount(
            accountType = AccountType.CHECKING,
            bankNo = 213,
            routingNo = 1,
            accountNo = 1234,
            accountDv = "2",
            ispb = "********",
        ),
    )

    private val notificationAdapter: NotificationAdapter = mockk(relaxed = true)
    private val billRepository: BillRepository = mockk()
    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()
    private val accountConfigurationService = mockk<AccountConfigurationService> {
        every {
            find(any())
        } answers {
            AccountConfiguration(
                accountId = firstArg(),
                pushNotificationTokens = emptySet(),
                receiveMonthlyStatement = false,
                freeOfFridaySubscription = false,
            )
        }
    }

    private val ddaService = mockk<DDAService>(relaxed = true)
    private val closeAccountService = mockk<CloseAccountService> {
        every {
            closeAccount(any(), any(), any())
        } returns mockk<CloseAccountResult>(relaxed = true).right()
    }

    private val chatBotMessagePublisher = mockk<ChatbotMessagePublisher> {
        every { publishStateUpdate(any(), any(), any()) } just runs
    }

    private val subscriptionService = SubscriptionService(
        accountRepository = accountRepository,
        subscriptionRepository = subscriptionDbRepository,
        recurrenceService = recurrenceService,
        billRepository = billRepository,
        subscriptionConfiguration = subscriptionConfiguration,
        notificationAdapter = notificationAdapter,
        accountConfigurationService = accountConfigurationService,
        ddaService = ddaService,
        closeAccountServiceProvider = mockk {
            every {
                get()
            } returns closeAccountService
        },
        disableOverdueAccountDDA = true,
        closeOverdueAccount = true,
        intercomAdapter = mockk(),
        inAppSubscriptionService = mockk(),
        chatbotMessagePublisher = chatBotMessagePublisher,
        subscritionDiscountInterfaceProvider = null,
        recurrenceRepository = mockk(),
        billEventRepository = mockk(),
    )

    @BeforeEach
    fun setup() {
        loadAccountIntoDb(dynamoDB, defaultWalletId = wallet.id)
    }

    @Nested
    @DisplayName("subscription")
    inner class Subscribe {
        @Test
        fun `deve retornar AccountNotFound quando nao encontra a conta ao tentar registrar o usuario como pagante`() {
            val result = subscriptionService.subscribe(
                accountId = AccountId(ACCOUNT_ID_2),
                amount = defaultAmount,
                dayOfMonth = defaultDay,
            )

            result shouldBe SubscribeResult.AccountNotFound
        }

        @ParameterizedTest
        @EnumSource(AccountStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["ACTIVE", "BLOCKED"])
        fun `deve retornar AccountNotActive quando nao encontra a conta ATIVA ao tentar registrar o usuario como pagante`(
            accountStatus: AccountStatus,
        ) {
            loadAccountIntoDb(dynamoDB, defaultWalletId = wallet.id, status = accountStatus.name)

            val result = subscriptionService.subscribe(
                accountId = AccountId(ACCOUNT_ID),
                amount = defaultAmount,
                dayOfMonth = defaultDay,
            )

            result shouldBe SubscribeResult.AccountNotActive
        }

        @Test
        fun `deve retornar AccountIsFreeOfFridaySubscription quando a conta esta ATIVA mas eh GRATUITA`() {
            every {
                accountConfigurationService.find(any())
            } answers {
                AccountConfiguration(
                    accountId = firstArg(),
                    pushNotificationTokens = emptySet(),
                    receiveMonthlyStatement = false,
                    freeOfFridaySubscription = true,
                )
            }

            val result = subscriptionService.subscribe(
                accountId = AccountId(ACCOUNT_ID),
                amount = defaultAmount,
                dayOfMonth = defaultDay,
            )

            result shouldBe SubscribeResult.AccountIsFreeOfFridaySubscription
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.subscription.SubscriptionServiceTest#recurrenceCreationErrors")
        fun `deve retornar Error ao falhar criando o Pix recorrente`(recurrenceCreationError: RecurrenceCreationError) {
            every {
                recurrenceService.create(any(), false)
            } returns recurrenceCreationError.left()

            val result = subscriptionService.subscribe(
                accountId = ACCOUNT.accountId,
                amount = defaultAmount,
                dayOfMonth = defaultDay,
            )

            result.shouldBeTypeOf<SubscribeResult.Error>()
            result.message shouldBe recurrenceCreationError.message
        }

        @ParameterizedTest
        @CsvSource(value = ["1,1", "1,0", "0,1"])
        fun `deve retornar Conflict ao tentar registrar com dados divergentes um usuario que ja eh pagante`(
            amountDelta: Long,
            dayOfMonthDelta: Int,
        ) {
            val subscription = setupSubscription()

            val result = subscriptionService.subscribe(
                accountId = subscription.accountId,
                amount = subscription.amount + amountDelta,
                dayOfMonth = subscription.dayOfMonth + dayOfMonthDelta,
            )

            result.shouldBeTypeOf<SubscribeResult.Conflict>()
            result.subscription shouldBe subscription

            verify(exactly = 0) {
                notificationAdapter.notifySubscriptionCreated(any(), any(), any())
            }
        }

        @Test
        fun `deve retornar Success ao tentar registrar com os mesmos dados um usuario que ja eh pagante`() {
            val subscription = setupSubscription()

            val result = subscriptionService.subscribe(
                accountId = subscription.accountId,
                amount = subscription.amount,
                dayOfMonth = subscription.dayOfMonth,
            )

            result shouldBe SubscribeResult.Success

            verify(exactly = 0) {
                notificationAdapter.notifySubscriptionCreated(any(), any(), any())
            }
        }

        @ParameterizedTest
        @CsvSource(
            value = [
                "true, 1, 30, 2022-11-20, 2022-11-30",
                "false, 2, 10, 2022-11-20, 2022-12-12",
            ],
            nullValues = ["null"],
        )
        fun `deve retornar Success ao tentar registrar como pagante um usuario que nao eh pagante`(
            shouldSaveInactiveSubscription: Boolean,
            customSubscriptionAmount: Long,
            customSubscriptionDayOfMonth: Int,
            currentDate: LocalDate,
            nextEffectiveDueDate: LocalDate,
        ) {
            if (shouldSaveInactiveSubscription) {
                setupSubscription(status = SubscriptionStatus.INACTIVE)
            }

            val bill = getActiveBill(
                walletId = billPaid.walletId,
                accountId = ACCOUNT.accountId,
                subscriptionFee = true,
                dueDate = nextEffectiveDueDate,
                effectiveDueDate = nextEffectiveDueDate,
            )

            every {
                recurrenceService.create(any(), false)
            } answers {
                RecurrenceResult(recurrence = firstArg<BillRecurrence>().copy(bills = listOf(bill.billId))).right()
            }

            every {
                billRepository.findBill(any(), any())
            } returns bill

            val fakeNow = ZonedDateTime.of(
                currentDate,
                LocalTime.now(),
                brazilTimeZone,
            )
            withGivenDateTime(fakeNow) {
                val result =
                    subscriptionService.subscribe(
                        ACCOUNT.accountId,
                        customSubscriptionAmount,
                        customSubscriptionDayOfMonth,
                    )
                result shouldBe SubscribeResult.Success
            }

            val savedSubscription = subscriptionDbRepository.find(ACCOUNT.accountId)!!

            savedSubscription.status shouldBe SubscriptionStatus.ACTIVE
            savedSubscription.document.value shouldBe ACCOUNT.document
            savedSubscription.amount shouldBe customSubscriptionAmount
            savedSubscription.dayOfMonth shouldBe customSubscriptionDayOfMonth
            savedSubscription.walletId shouldBe wallet.id
            savedSubscription.nextEffectiveDueDate shouldBe nextEffectiveDueDate

            val slot = slot<BillRecurrence>()
            verify {
                recurrenceService.create(capture(slot), false)
            }

            with(slot.captured) {
                this.id shouldBe savedSubscription.recurrenceId
                this.amount shouldBe customSubscriptionAmount
                this.rule.startDate.dayOfMonth shouldBe customSubscriptionDayOfMonth
                this.description shouldBe subscriptionConfiguration.description
                this.recipientName shouldBe subscriptionConfiguration.recipientName
                this.recipientDocument shouldBe subscriptionConfiguration.recipientDocument
                this.recipientBankAccount?.accountType shouldBe subscriptionConfiguration.bankAccount.accountType
                this.recipientBankAccount?.bankNo shouldBe subscriptionConfiguration.bankAccount.bankNo
                this.recipientBankAccount?.accountNo shouldBe subscriptionConfiguration.bankAccount.accountNo.toBigInteger()
                this.recipientBankAccount?.accountDv shouldBe subscriptionConfiguration.bankAccount.accountDv
                this.recipientBankAccount?.routingNo shouldBe subscriptionConfiguration.bankAccount.routingNo
                this.recipientBankAccount?.ispb shouldBe subscriptionConfiguration.bankAccount.ispb
                this.recipientBankAccount?.document shouldBe subscriptionConfiguration.recipientDocument
                this.actionSource.shouldBeTypeOf<ActionSource.Subscription>()
                this.actionSource.accountId shouldBe ACCOUNT.accountId
                this.contactId shouldBe null
            }

            verify {
                notificationAdapter.notifySubscriptionCreated(
                    accountId = ACCOUNT.accountId,
                    dueDate = slot.captured.rule.startDate,
                    amount = customSubscriptionAmount,
                )
            }
        }

        @ParameterizedTest
        @CsvSource(value = ["2022-10-14,2022-11-10,10", "2022-10-09,2022-10-10,10", "2022-10-10,2022-10-10,10", "2022-12-11,2023-01-10, 10", "2022-10-24,2022-10-30,30", "2022-10-31,2022-11-30,30"])
        fun `deve calcular a proxima data de vencimento da assinatura`(
            now: LocalDate,
            expectedNextDueDate: String,
            expectedDayOfMonth: Int,
        ) {
            // FIXME variar o dia do mes
            val nextDueDate =
                subscriptionService.calcNextSubscriptionDueDate(
                    now,
                    expectedDayOfMonth,
                )

            nextDueDate.format(DateTimeFormatter.ISO_DATE) shouldBe expectedNextDueDate
        }
    }

    @Nested
    @DisplayName("unsubscribe")
    inner class Unsubscribe {
        @Test
        fun `deve retornar AccountNotFound quando nao encontra a conta ao tentar desinscrever o usuario como pagante`() {
            val result = subscriptionService.unsubscribe(AccountId(ACCOUNT_ID_2))

            result.isLeft() shouldBe true
            result.mapLeft {
                it shouldBe UnsubscribeError.AccountNotFound
            }
        }

        @ParameterizedTest
        @ValueSource(booleans = [true, false])
        fun `deve retornar Success ao tentar remover o registro como pagante um usuario que nao eh pagante`(shouldSave: Boolean) {
            if (shouldSave) {
                setupSubscription(status = SubscriptionStatus.INACTIVE)
            }

            val result = subscriptionService.unsubscribe(ACCOUNT.accountId)

            result.isRight() shouldBe true
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.subscription.SubscriptionServiceTest#cancelSubscriptionErrors")
        fun `deve retornar ServerError ao falhar tentando cancelar a assinatura`(cancelSubscriptionResult: CancelSubscriptionResult) {
            val subscription = setupSubscription()

            every {
                recurrenceService.cancelSubscription(subscription.recurrenceId, subscription.walletId)
            } returns cancelSubscriptionResult

            val result = subscriptionService.unsubscribe(ACCOUNT.accountId)

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<UnsubscribeError.ServerError>()
                it.message shouldBe cancelSubscriptionResult.toString()
            }

            val updatedSubscription = subscriptionDbRepository.find(ACCOUNT.accountId)

            updatedSubscription?.status shouldBe SubscriptionStatus.ACTIVE
        }

        @Test
        fun `deve retornar BillProcessing ao tentar cancelar uma assinatura em que a cobranca esta sendo processada`() {
            val subscription = setupSubscription()

            every {
                recurrenceService.cancelSubscription(subscription.recurrenceId, subscription.walletId)
            } returns CancelSubscriptionResult.BillProcessing

            val result = subscriptionService.unsubscribe(ACCOUNT.accountId)

            result.isLeft() shouldBe true
            result.mapLeft {
                it shouldBe UnsubscribeError.BillProcessing
            }

            val updatedSubscription = subscriptionDbRepository.find(ACCOUNT.accountId)

            updatedSubscription?.status shouldBe SubscriptionStatus.ACTIVE
        }

        @Test
        fun `deve retornar Success ao tentar remover o registro como pagante um usuario que eh pagante`() {
            val subscription = setupSubscription()

            every {
                recurrenceService.cancelSubscription(subscription.recurrenceId, subscription.walletId)
            } returns CancelSubscriptionResult.Success

            val result = subscriptionService.unsubscribe(ACCOUNT.accountId)

            val updatedSubscription = subscriptionDbRepository.find(ACCOUNT.accountId)

            updatedSubscription?.status shouldBe SubscriptionStatus.INACTIVE

            result.isRight() shouldBe true

            verify {
                recurrenceService.cancelSubscription(subscription.recurrenceId, subscription.walletId)
            }
        }
    }

    @Nested
    @DisplayName("ignore subscription fee")
    inner class IgnoreSubscriptionFee {

        @Test
        fun `deve retornar AccountNotFound quando nao encontra a conta do usuario`() {
            val result = subscriptionService.ignoreSubscriptionFee(AccountId("FOO"), LocalDate.now())

            result shouldBe IgnoreSubscriptionFeeResult.AccountNotFound
        }

        @Test
        fun `deve retornar SubscriptionNotFound quando o usuario nunca foi um assinante`() {
            val result = subscriptionService.ignoreSubscriptionFee(ACCOUNT.accountId, LocalDate.now())

            result shouldBe IgnoreSubscriptionFeeResult.SubscriptionNotFound
        }

        @ParameterizedTest
        @EnumSource(SubscriptionStatus::class)
        fun `deve retornar SubscriptionFeeNotFound quando nao encontra uma cobranca na data informada`(
            subscriptionStatus: SubscriptionStatus,
        ) {
            val subscription = setupSubscription(status = subscriptionStatus)

            every {
                recurrenceService.ignoreSubscriptionFee(subscription.recurrenceId, subscription.walletId, any())
            } returns BillRecurrenceService.IgnoreSubscriptionRecurrenceResult.BillNotFound

            val result = subscriptionService.ignoreSubscriptionFee(ACCOUNT.accountId, LocalDate.now())

            result shouldBe IgnoreSubscriptionFeeResult.SubscriptionFeeNotFound
        }

        @Test
        fun `deve retornar SubscriptionFeeProcessing quando esta processando o pagamento da cobranca na data informada`() {
            val dueDate = LocalDate.now()

            val subscription = setupSubscription()

            every {
                recurrenceService.ignoreSubscriptionFee(subscription.recurrenceId, subscription.walletId, any())
            } returns BillRecurrenceService.IgnoreSubscriptionRecurrenceResult.BillProcessing

            val result = subscriptionService.ignoreSubscriptionFee(ACCOUNT.accountId, dueDate)

            result shouldBe IgnoreSubscriptionFeeResult.SubscriptionFeeProcessing
        }

        @Test
        fun `deve retornar SubscriptionFeeAlreadyPaid quando a cobranca da data informada ja esta paga`() {
            val dueDate = LocalDate.now()

            val subscription = setupSubscription()

            every {
                recurrenceService.ignoreSubscriptionFee(subscription.recurrenceId, subscription.walletId, any())
            } returns BillRecurrenceService.IgnoreSubscriptionRecurrenceResult.BillAlreadyPaid

            val result = subscriptionService.ignoreSubscriptionFee(ACCOUNT.accountId, dueDate)

            result shouldBe IgnoreSubscriptionFeeResult.SubscriptionFeeAlreadyPaid
        }

        @Test
        fun `deve retornar Success quando consegue ignorar a cobranca da data informada`() {
            val dueDate = LocalDate.now()
            val nextActiveBill = getActiveBill(dueDate = dueDate)
            val nextActiveBillId = nextActiveBill.billId
            val overdueBillId = BillId("BILL-OVERDUE")

            val subscription = setupSubscription(paymentStatus = SubscriptionPaymentStatus.OVERDUE)

            every {
                recurrenceService.ignoreSubscriptionFee(subscription.recurrenceId, subscription.walletId, any())
            } returns BillRecurrenceService.IgnoreSubscriptionRecurrenceResult.Success

            every {
                recurrenceService.find(subscription.recurrenceId, subscription.walletId)
            } returns weeklyWalletRecurrenceNoEndDate.copy(
                bills = listOf(overdueBillId, nextActiveBillId),
                walletId = subscription.walletId,
            )

            every {
                billRepository.findBill(overdueBillId, subscription.walletId)
            } throws IllegalStateException()

            every {
                billRepository.findBill(nextActiveBillId, subscription.walletId)
            } returns nextActiveBill

            val result = subscriptionService.ignoreSubscriptionFee(ACCOUNT.accountId, dueDate)

            result shouldBe IgnoreSubscriptionFeeResult.Success

            val updateSubscription = subscriptionDbRepository.find(subscription.accountId)!!

            updateSubscription.paymentStatus shouldBe SubscriptionPaymentStatus.PAID
        }
    }

    @DisplayName("synchronize subscription")
    @Nested
    inner class SynchronizeSubscriptionTest {
        private val accountId = ACCOUNT.accountId
        val bill = getPaidBill(
            walletId = billPaid.walletId,
            accountId = accountId,
            subscriptionFee = true,
            source = ActionSource.SubscriptionRecurrence(
                accountId = accountId,
                recurrenceId = RecurrenceId("FOO"),
            ),
        )

        @BeforeEach
        fun setup() {
            createBillPaymentTable(dynamoDB)
            setupAccount()
            every {
                billRepository.findBill(billPaid.billId, billPaid.walletId)
            } returns bill
        }

        @ParameterizedTest
        @CsvSource(
            value = [
                "2024-01-10, 34",
                "2024-02-12, 29",
                "2024-03-11, 31",
                "2024-04-10, 33",
                "2024-05-10, 32",
                "2024-06-10, 31",
                "2024-07-10, 34",
                "2024-08-12, 30",
                "2024-09-10, 31",
                "2024-10-10, 33",
                "2024-11-11, 30",
                "2024-12-10, 34",
                "2025-01-10, 32",
                "2025-02-10, 29",
                "2025-03-10, 32",
                "2025-04-10, 33",
                "2025-05-12, 30",
                "2025-06-10, 31",
                "2025-07-10, 33",
                "2025-08-11, 31",
                "2025-09-10, 33",
                "2025-10-10, 32",
                "2025-11-10, 31",
                "2025-12-10, 34",
            ],
        )
        fun `deve tentar notificar usuario sobre remocao das notificacoes se ja tiver inadimplente`(dueDate: LocalDate, days: Long) {
            setupAccount(paymentStatus = AccountPaymentStatus.Overdue)
            val subscription = setupSubscription(paymentStatus = SubscriptionPaymentStatus.OVERDUE)
            setupRecurrence(subscription, billIds = listOf(billPaid.billId, BillId("ACTIVE_BILL")))
            setupActiveBill(dueDate)

            withGivenDateTime(dueDate.plusDays(days).atStartOfDay(brazilTimeZone).plusHours(12)) {
                subscriptionService.synchronizePixSubscriptionPaymentStatus(accountId = accountId)
            }

            verify {
                notificationAdapter.notifySubscriptionOverdueWarningBillNotificationsAndDDAShutdown(any(), any())
            }
        }

        @Test
        fun `deve marcar o usuario como adimplente quando nao tem conta em aberto antes da conta paga e proxima conta em aberto nao esta vencida`() {
            val firstActiveBill = setupActiveBill()
            val subscription = setupSubscription()
            setupRecurrence(subscription, billIds = listOf(billPaid.billId, BillId("ACTIVE_BILL")))

            subscriptionService.synchronizePixSubscriptionPaymentStatus(accountId = accountId)

            with(subscriptionDbRepository.find(subscription.accountId)!!) {
                paymentStatus shouldBe SubscriptionPaymentStatus.PAID
                nextEffectiveDueDate shouldBe firstActiveBill.effectiveDueDate
            }

            accountRepository.findById(accountId).paymentStatus shouldBe AccountPaymentStatus.UpToDate
        }

        @Test
        fun `deve marcar o usuario como inadimplente quando nao tem conta em aberto antes da conta paga e proxima conta em aberto esta vencida`() {
            val firstActiveBill = setupActiveBill(3L)
            val subscription = setupSubscription()
            setupRecurrence(subscription, billIds = listOf(billPaid.billId, BillId("ACTIVE_BILL")))

            subscriptionService.synchronizePixSubscriptionPaymentStatus(accountId = accountId)

            with(subscriptionDbRepository.find(subscription.accountId)!!) {
                paymentStatus shouldBe SubscriptionPaymentStatus.OVERDUE
                nextEffectiveDueDate shouldBe firstActiveBill.effectiveDueDate
            }

            accountRepository.findById(accountId).paymentStatus shouldBe AccountPaymentStatus.PastDue
        }

        @Test
        fun `deve marcar o usuario como inadimplente mantendo Overdue quando nao tem conta em aberto antes da conta paga e proxima conta em aberto esta vencida mas ja estava Overdue`() {
            setupAccount(paymentStatus = AccountPaymentStatus.Overdue)
            val firstActiveBill = setupActiveBill(30L)
            val subscription = setupSubscription()
            setupRecurrence(subscription, billIds = listOf(billPaid.billId, BillId("ACTIVE_BILL")))

            subscriptionService.synchronizePixSubscriptionPaymentStatus(accountId = accountId)

            with(subscriptionDbRepository.find(subscription.accountId)!!) {
                paymentStatus shouldBe SubscriptionPaymentStatus.OVERDUE
                nextEffectiveDueDate shouldBe firstActiveBill.effectiveDueDate
            }

            accountRepository.findById(accountId).paymentStatus shouldBe AccountPaymentStatus.Overdue
        }

        @ParameterizedTest
        @ValueSource(longs = [1, 2, 3, 4, 5, 6])
        fun `deve manter o usuario como inadimplente marcando como PastDue quando nao tem conta em aberto antes da conta paga e proxima conta em aberto esta vencida a menos de 7 dias`(daysOverdue: Long) {
            setupAccount()
            setupAccount(paymentStatus = AccountPaymentStatus.Overdue)
            val firstActiveBill = setupActiveBill(daysOverdue)
            val subscription = setupSubscription()
            setupRecurrence(subscription, billIds = listOf(billPaid.billId, BillId("ACTIVE_BILL")))

            subscriptionService.synchronizePixSubscriptionPaymentStatus(accountId = accountId)

            with(subscriptionDbRepository.find(subscription.accountId)!!) {
                paymentStatus shouldBe SubscriptionPaymentStatus.OVERDUE
                nextEffectiveDueDate shouldBe firstActiveBill.effectiveDueDate
            }

            accountRepository.findById(accountId).paymentStatus shouldBe AccountPaymentStatus.PastDue
        }

        @Test
        fun `deve marcar o usuario como adimplente quando tem conta em aberto antes da conta paga e a conta em aberto nao esta vencida`() {
            val firstActiveBill = setupActiveBill()
            val subscription = setupSubscription()
            setupRecurrence(subscription, billIds = listOf(BillId("ACTIVE_BILL"), billPaid.billId))

            subscriptionService.synchronizePixSubscriptionPaymentStatus(accountId = accountId)

            with(subscriptionDbRepository.find(subscription.accountId)!!) {
                paymentStatus shouldBe SubscriptionPaymentStatus.PAID
                nextEffectiveDueDate shouldBe firstActiveBill.effectiveDueDate
            }

            accountRepository.findById(accountId).paymentStatus shouldBe AccountPaymentStatus.UpToDate
        }

        @Test
        fun `deve marcar o usuario como inadimplente quando tem conta em aberto antes da conta paga e a conta em aberto esta vencida`() {
            val firstActiveBill = setupActiveBill(6L)
            val subscription = setupSubscription()
            setupRecurrence(subscription, billIds = listOf(BillId("ACTIVE_BILL"), billPaid.billId))

            subscriptionService.synchronizePixSubscriptionPaymentStatus(accountId = accountId)

            with(subscriptionDbRepository.find(subscription.accountId)!!) {
                paymentStatus shouldBe SubscriptionPaymentStatus.OVERDUE
                nextEffectiveDueDate shouldBe firstActiveBill.effectiveDueDate
            }

            accountRepository.findById(accountId).paymentStatus shouldBe AccountPaymentStatus.PastDue
        }

        @Test
        @Disabled("teste intermitente no dia 5")
        fun `deve marcar o usuario como inadimplente mantendo Overdue quando tem conta em aberto antes da conta paga e a conta em aberto esta vencida mas ja estava OVERDUE`() {
            setupAccount(status = AccountStatus.BLOCKED, paymentStatus = AccountPaymentStatus.Overdue)
            val firstActiveBill = setupActiveBill(40L)
            val subscription = setupSubscription()
            setupRecurrence(subscription, billIds = listOf(BillId("ACTIVE_BILL"), billPaid.billId))

            subscriptionService.synchronizePixSubscriptionPaymentStatus(accountId = accountId)

            with(subscriptionDbRepository.find(subscription.accountId)!!) {
                paymentStatus shouldBe SubscriptionPaymentStatus.OVERDUE
                nextEffectiveDueDate shouldBe firstActiveBill.effectiveDueDate
            }

            val account = accountRepository.findById(accountId)
            account.paymentStatus shouldBe AccountPaymentStatus.Overdue
            account.status shouldBe AccountStatus.BLOCKED
        }

        @Test
        fun `deve colocar a conta do usuario para ACTIVE caso nao haja conta em aberto`() {
            setupAccount(paymentStatus = AccountPaymentStatus.Overdue, status = AccountStatus.BLOCKED)
            val firstActiveBill = setupActiveBill()
            val subscription = setupSubscription()
            setupRecurrence(subscription, billIds = listOf(BillId("ACTIVE_BILL"), billPaid.billId))

            subscriptionService.synchronizePixSubscriptionPaymentStatus(accountId = accountId)

            with(subscriptionDbRepository.find(subscription.accountId)!!) {
                paymentStatus shouldBe SubscriptionPaymentStatus.PAID
                nextEffectiveDueDate shouldBe firstActiveBill.effectiveDueDate
            }

            val account = accountRepository.findById(accountId)
            account.paymentStatus shouldBe AccountPaymentStatus.UpToDate
            account.status shouldBe AccountStatus.ACTIVE
        }

        @Nested
        @DisplayName("check payment status - inactivation flow")
        inner class CheckPaymentStatusInactivationFlow {
            @ParameterizedTest
            @CsvSource(
                value = [
                    // D1
                    "2024-01-10, 1",
                    "2024-02-12, 1",
                    "2024-03-11, 1",
                    "2024-04-10, 1",
                    "2024-05-10, 3",
                    "2024-06-10, 1",
                    "2024-07-10, 1",
                    "2024-08-12, 1",
                    "2024-09-10, 1",
                    "2024-10-10, 1",
                    "2024-11-11, 1",
                    "2024-12-10, 1",
                    "2025-01-10, 3",
                    "2025-02-10, 1",
                    "2025-03-10, 1",
                    "2025-04-10, 1",
                    "2025-05-12, 1",
                    "2025-06-10, 1",
                    "2025-07-10, 1",
                    "2025-08-11, 1",
                    "2025-09-10, 1",
                    "2025-10-10, 3",
                    "2025-11-10, 1",
                    "2025-12-10, 1",
                    // D2
                    "2024-01-10, 2",
                    "2024-02-12, 2",
                    "2024-03-11, 2",
                    "2024-04-10, 2",
                    "2024-05-10, 4",
                    "2024-06-10, 2",
                    "2024-07-10, 2",
                    "2024-08-12, 2",
                    "2024-09-10, 2",
                    "2024-10-10, 4",
                    "2024-11-11, 2",
                    "2024-12-10, 2",
                    "2025-01-10, 4",
                    "2025-02-10, 2",
                    "2025-03-10, 2",
                    "2025-04-10, 4",
                    "2025-05-12, 2",
                    "2025-06-10, 2",
                    "2025-07-10, 4",
                    "2025-08-11, 2",
                    "2025-09-10, 2",
                    "2025-10-10, 4",
                    "2025-11-10, 2",
                    "2025-12-10, 2",
                ],
            )
            fun `deve enviar notificacao de debito nao efetuado`(dueDate: LocalDate, days: Long) {
                val subscription = setupSubscription()
                setupRecurrence(subscription, billIds = listOf(BillId("ACTIVE_BILL"), billPaid.billId))
                val bill = setupActiveBill(dueDate)

                withGivenDateTime(dueDate.plusDays(days).atStartOfDay(brazilTimeZone).plusHours(12)) {
                    subscriptionService.synchronizePixSubscriptionPaymentStatus(accountId = accountId)
                }

                verify {
                    notificationAdapter.notifySubscriptionOverdue(
                        accountId,
                        bill.effectiveDueDate,
                        subscription.amount,
                        days != 1L,
                    )
                }
                verify(exactly = 0) {
                    notificationAdapter.notifySubscriptionOverdueWarningDetailedNotificationsShutdown(any(), any())
                    notificationAdapter.notifySubscriptionOverdueWarningBillNotificationsAndDDAShutdown(any(), any())
                    notificationAdapter.notifySubscriptionOverdueWarningAccountClosure(any(), any(), any())
                }

                val updateSubscription = subscriptionDbRepository.find(subscription.accountId)!!

                updateSubscription.status shouldBe SubscriptionStatus.ACTIVE
            }

            @ParameterizedTest
            @CsvSource(
                value = [
                    "2024-01-10, 5",
                    "2024-02-12, 4",
                    "2024-03-11, 4",
                    "2024-04-10, 5",
                    "2024-05-10, 5",
                    "2024-06-10, 4",
                    "2024-07-10, 5",
                    "2024-08-12, 4",
                    "2024-09-10, 6",
                    "2024-10-10, 5",
                    "2024-11-11, 4",
                    "2024-12-10, 6",
                    "2025-01-10, 5",
                    "2025-02-10, 4",
                    "2025-03-10, 4",
                    "2025-04-10, 5",
                    "2025-05-12, 4",
                    "2025-06-10, 6",
                    "2025-07-10, 5",
                    "2025-08-11, 4",
                    "2025-09-10, 5",
                    "2025-10-10, 5",
                    "2025-11-10, 4",
                    "2025-12-10, 5",
                ],
            )
            fun `deve enviar notificacao avisando possivel desligamento de notificacao detalhada`(dueDate: LocalDate, days: Long) {
                val subscription = setupSubscription()
                setupRecurrence(subscription, billIds = listOf(BillId("ACTIVE_BILL"), billPaid.billId))
                val bill = setupActiveBill(dueDate)

                withGivenDateTime(dueDate.plusDays(days).atStartOfDay(brazilTimeZone).plusHours(12)) {
                    subscriptionService.synchronizePixSubscriptionPaymentStatus(accountId = accountId)
                }

                verify {
                    notificationAdapter.notifySubscriptionOverdueWarningDetailedNotificationsShutdown(accountId, bill.effectiveDueDate)
                }
                verify(exactly = 0) {
                    notificationAdapter.notifySubscriptionOverdue(any(), any(), any(), any())
                    notificationAdapter.notifySubscriptionOverdueWarningBillNotificationsAndDDAShutdown(any(), any())
                    notificationAdapter.notifySubscriptionOverdueWarningAccountClosure(any(), any(), any())
                }

                val updateSubscription = subscriptionDbRepository.find(subscription.accountId)!!

                updateSubscription.status shouldBe SubscriptionStatus.ACTIVE
            }

            @ParameterizedTest
            @ValueSource(longs = [7, 8])
            fun `deve desabilitar o envio de notificacao detalhada`(days: Long) {
                withGivenDateTime(LocalDate.of(2025, 4, 10).atStartOfDay(brazilTimeZone)) {
                    val subscription = setupSubscription()
                    setupRecurrence(subscription, billIds = listOf(BillId("ACTIVE_BILL"), billPaid.billId))
                    setupActiveBill(days)

                    subscriptionService.synchronizePixSubscriptionPaymentStatus(accountId = accountId)
                }

                val account = accountRepository.findById(accountId)

                account.paymentStatus shouldBe AccountPaymentStatus.Overdue

                verify(exactly = 0) {
                    notificationAdapter wasNot called
                }
            }

            @ParameterizedTest
            @ValueSource(longs = [8, 9])
            fun `nao deve desabilitar o envio de notificacao detalhada se ja foi desabilitado`(days: Long) {
                setupAccount(paymentStatus = AccountPaymentStatus.Overdue)
                val subscription = setupSubscription()
                setupRecurrence(subscription, billIds = listOf(BillId("ACTIVE_BILL"), billPaid.billId))
                setupActiveBill(days)

                subscriptionService.synchronizePixSubscriptionPaymentStatus(accountId = accountId)

                verify(exactly = 0) {
                    notificationAdapter wasNot called
                }
            }

            @ParameterizedTest
            @CsvSource(
                value = [
                    "2024-01-10, 34, 2024-02-14",
                    "2024-02-12, 29, 2024-03-13",
                    "2024-03-11, 31, 2024-04-12",
                    "2024-04-10, 33, 2024-05-14",
                    "2024-05-10, 32, 2024-06-12",
                    "2024-06-10, 31, 2024-07-12",
                    "2024-07-10, 34, 2024-08-14",
                    "2024-08-12, 30, 2024-09-12",
                    "2024-09-10, 31, 2024-10-14",
                    "2024-10-10, 33, 2024-11-13",
                    "2024-11-11, 30, 2024-12-12",
                    "2024-12-10, 34, 2025-01-14",
                    "2025-01-10, 32, 2025-02-12",
                    "2025-02-10, 29, 2025-03-12",
                    "2025-03-10, 32, 2025-04-14",
                    "2025-04-10, 33, 2025-05-14",
                    "2025-05-12, 30, 2025-06-12",
                    "2025-06-10, 31, 2025-07-14",
                    "2025-07-10, 33, 2025-08-13",
                    "2025-08-11, 31, 2025-09-12",
                    "2025-09-10, 33, 2025-10-14",
                    "2025-10-10, 32, 2025-11-12",
                    "2025-11-10, 31, 2025-12-12",
                    "2025-12-10, 34, 2026-01-14",
                ],
            )
            fun `deve enviar notificacao avisando possivel desligamento de todas as notificacoes e DDA`(dueDate: LocalDate, days: Long, dayToBlock: LocalDate) {
                val subscription = setupSubscription()
                setupRecurrence(subscription, billIds = listOf(BillId("ACTIVE_BILL"), billPaid.billId))
                setupActiveBill(dueDate)

                withGivenDateTime(dueDate.plusDays(days).atStartOfDay(brazilTimeZone).plusHours(12)) {
                    subscriptionService.synchronizePixSubscriptionPaymentStatus(accountId = accountId)
                }

                val slot = slot<LocalDate>()
                verify {
                    notificationAdapter.notifySubscriptionOverdueWarningBillNotificationsAndDDAShutdown(accountId, capture(slot))
                }
                with(slot.captured) {
                    this.format(DateTimeFormatter.ISO_DATE) shouldBe dayToBlock.minusDays(1).format(DateTimeFormatter.ISO_DATE)
                }

                verify(exactly = 0) {
                    notificationAdapter.notifySubscriptionOverdue(any(), any(), any(), any())
                    notificationAdapter.notifySubscriptionOverdueWarningDetailedNotificationsShutdown(any(), any())
                    notificationAdapter.notifySubscriptionOverdueWarningAccountClosure(any(), any(), any())
                }

                val updateSubscription = subscriptionDbRepository.find(subscription.accountId)!!

                updateSubscription.status shouldBe SubscriptionStatus.ACTIVE
            }

            @ParameterizedTest
            @CsvSource(
                value = [
                    "2024-01-10, 35",
                    "2024-02-12, 30",
                    "2024-03-11, 32",
                    "2024-04-10, 34",
                    "2024-05-10, 33",
                    "2024-06-10, 32",
                    "2024-07-10, 35",
                    "2024-08-12, 31",
                    "2024-09-10, 34",
                    "2024-10-10, 34",
                    "2024-11-11, 31",
                    "2024-12-10, 35",
                    "2025-01-10, 33",
                    "2025-02-10, 30",
                    "2025-03-10, 35",
                    "2025-04-10, 34",
                    "2025-05-12, 31",
                    "2025-06-10, 34",
                    "2025-07-10, 34",
                    "2025-08-11, 32",
                    "2025-09-10, 34",
                    "2025-10-10, 33",
                    "2025-11-10, 32",
                    "2025-12-10, 35",
                ],
            )
            fun `deve bloquear a conta`(dueDate: LocalDate, days: Long) {
                val subscription = setupSubscription()
                setupRecurrence(subscription, billIds = listOf(BillId("ACTIVE_BILL"), billPaid.billId))
                setupActiveBill(dueDate)

                withGivenDateTime(dueDate.plusDays(days).atStartOfDay(brazilTimeZone).plusHours(12)) {
                    subscriptionService.synchronizePixSubscriptionPaymentStatus(accountId = accountId)
                }

                val documentSlot = slot<Document>()
                verify {
                    ddaService.remove(accountId, capture(documentSlot))
                }
                with(documentSlot.captured) {
                    this.value shouldBe ACCOUNT.document
                }

                with(accountRepository.findById(accountId)) {
                    status shouldBe AccountStatus.BLOCKED
                }

                verify {
                    notificationAdapter.notifySubscriptionOverdueWarningAccountClosure(accountId, any(), any())
                }
                verify(exactly = 0) {
                    notificationAdapter.notifySubscriptionOverdue(any(), any(), any(), any())
                    notificationAdapter.notifySubscriptionOverdueWarningDetailedNotificationsShutdown(any(), any())
                    notificationAdapter.notifySubscriptionOverdueWarningBillNotificationsAndDDAShutdown(any(), any())
                }
            }

            @ParameterizedTest
            @CsvSource(
                value = [
                    "2024-01-10, 35",
                    "2024-02-12, 30",
                    "2024-03-11, 32",
                    "2024-04-10, 34",
                    "2024-05-10, 33",
                    "2024-06-10, 32",
                    "2024-07-10, 35",
                    "2024-08-12, 31",
                    "2024-09-10, 34",
                    "2024-10-10, 34",
                    "2024-11-11, 31",
                    "2024-12-10, 35",
                    "2025-01-10, 33",
                    "2025-02-10, 30",
                    "2025-03-10, 35",
                    "2025-04-10, 34",
                    "2025-05-12, 31",
                    "2025-06-10, 34",
                    "2025-07-10, 34",
                    "2025-08-11, 32",
                    "2025-09-10, 34",
                    "2025-10-10, 33",
                    "2025-11-10, 32",
                    "2025-12-10, 35",
                ],
            )
            fun `nao deve desativar o DDA ao bloquear a conta se a flag estiver desligada`(dueDate: LocalDate, days: Long) {
                val subscription = setupSubscription()
                setupRecurrence(subscription, billIds = listOf(BillId("ACTIVE_BILL"), billPaid.billId))
                setupActiveBill(dueDate)

                val subscriptionServiceWithFlagDisabled = SubscriptionService(
                    accountRepository = accountRepository,
                    subscriptionRepository = subscriptionDbRepository,
                    recurrenceService = recurrenceService,
                    billRepository = billRepository,
                    subscriptionConfiguration = subscriptionConfiguration,
                    notificationAdapter = notificationAdapter,
                    accountConfigurationService = accountConfigurationService,
                    ddaService = ddaService,
                    closeAccountServiceProvider = mockk {
                        every {
                            get()
                        } returns closeAccountService
                    },
                    intercomAdapter = mockk(),
                    closeOverdueAccount = true,
                    disableOverdueAccountDDA = false,
                    inAppSubscriptionService = mockk(),
                    chatbotMessagePublisher = chatBotMessagePublisher,
                    subscritionDiscountInterfaceProvider = mockk(),
                    recurrenceRepository = mockk(),
                    billEventRepository = mockk(),
                )

                withGivenDateTime(dueDate.plusDays(days).atStartOfDay(brazilTimeZone).plusHours(12)) {
                    subscriptionServiceWithFlagDisabled.synchronizePixSubscriptionPaymentStatus(accountId = accountId)
                }

                with(accountRepository.findById(accountId)) {
                    status shouldBe AccountStatus.BLOCKED
                }

                verify {
                    notificationAdapter.notifySubscriptionOverdueWarningAccountClosure(accountId, any(), any())
                }
                verify(exactly = 0) {
                    ddaService.remove(any(), any())
                    notificationAdapter.notifySubscriptionOverdue(any(), any(), any(), any())
                    notificationAdapter.notifySubscriptionOverdueWarningDetailedNotificationsShutdown(any(), any())
                    notificationAdapter.notifySubscriptionOverdueWarningBillNotificationsAndDDAShutdown(any(), any())
                }
            }

            @ParameterizedTest
            @ValueSource(longs = [35])
            @Disabled("habilitar novamente. deu ruim em 5/ago/2024. Segunda-feira")
            fun `nao deve bloquear uma conta ja bloqueada`(days: Long) {
                val subscription = setupSubscription()
                setupRecurrence(subscription, billIds = listOf(BillId("ACTIVE_BILL"), billPaid.billId))
                setupActiveBill(days)
                setupAccount(status = AccountStatus.BLOCKED)

                subscriptionService.synchronizePixSubscriptionPaymentStatus(accountId = accountId)

                verify(exactly = 0) {
                    ddaService.remove(any(), any())
                    notificationAdapter wasNot called
                }
            }

            @ParameterizedTest
            @CsvSource(
                value = [
                    "2024-01-10, 35, 5, 2024-02-18",
                    "2024-02-12, 30, 5, 2024-03-17",
                    "2024-03-11, 32, 4, 2024-04-15",
                    "2024-04-10, 34, 2, 2024-05-15",
                    "2024-05-10, 33, 5, 2024-06-16",
                    "2024-06-10, 32, 4, 2024-07-15",
                    "2024-07-10, 35, 5, 2024-08-18",
                    "2024-08-12, 31, 4, 2024-09-15",
                    "2024-09-10, 34, 2, 2024-10-15",
                    "2024-10-10, 34, 5, 2024-11-17",
                    "2024-11-11, 31, 4, 2024-12-15",
                    "2024-12-10, 35, 2, 2025-01-15",
                    "2025-01-10, 33, 5, 2025-02-16",
                    "2025-02-10, 30, 5, 2025-03-16",
                    "2025-03-10, 35, 2, 2025-04-15",
                    "2025-04-10, 34, 5, 2025-05-18",
                    "2025-05-12, 31, 4, 2025-06-15",
                    "2025-06-10, 34, 2, 2025-07-15",
                    "2025-07-10, 34, 5, 2025-08-17",
                    "2025-08-11, 32, 4, 2025-09-15",
                    "2025-09-10, 34, 2, 2025-10-15",
                    "2025-10-10, 33, 5, 2025-11-16",
                    "2025-11-10, 32, 4, 2025-12-15",
                    "2025-12-10, 35, 5, 2026-01-18",
                ],
            )
            fun `deve enviar notificacao avisando possivel encerramento da conta`(dueDate: LocalDate, days: Long, daysUntilClosure: Long, dayBeforeClose: LocalDate) {
                val subscription = setupSubscription()
                setupRecurrence(subscription, billIds = listOf(BillId("ACTIVE_BILL"), billPaid.billId))
                setupActiveBill(dueDate)

                val now = dueDate.plusDays(days).atStartOfDay(brazilTimeZone).plusHours(12)

                withGivenDateTime(now) {
                    subscriptionService.synchronizePixSubscriptionPaymentStatus(accountId = accountId)
                }

                verify {
                    notificationAdapter.notifySubscriptionOverdueWarningAccountClosure(accountId, daysUntilClosure, dayBeforeClose)
                }
                verify(exactly = 0) {
                    notificationAdapter.notifySubscriptionOverdue(any(), any(), any(), any())
                    notificationAdapter.notifySubscriptionOverdueWarningDetailedNotificationsShutdown(any(), any())
                    notificationAdapter.notifySubscriptionOverdueWarningBillNotificationsAndDDAShutdown(any(), any())
                    notificationAdapter.notifySubscriptionOverdueCloseAccount(any())
                }

                val updateSubscription = subscriptionDbRepository.find(subscription.accountId)!!

                updateSubscription.status shouldBe SubscriptionStatus.ACTIVE // FIXME
            }

            @ParameterizedTest
            @CsvSource(
                value = [
                    // um dia depois
                    "2024-01-10, 41",
                    // no dia exato
                    "2024-01-10, 40",
                    "2024-02-12, 35",
                    "2024-03-11, 36",
                    "2024-04-10, 36",
                    "2024-05-10, 38",
                    "2024-06-10, 36",
                    "2024-07-10, 40",
                    "2024-08-12, 35",
                    "2024-09-10, 36",
                    "2024-10-10, 39",
                    "2024-11-11, 35",
                    "2024-12-10, 37",
                    "2025-01-10, 38",
                    "2025-02-10, 35",
                    "2025-03-10, 37",
                    "2025-04-10, 39",
                    "2025-05-12, 35",
                    "2025-06-10, 36",
                    "2025-07-10, 39",
                    "2025-08-11, 36",
                    "2025-09-10, 36",
                    "2025-10-10, 38",
                    "2025-11-10, 36",
                    "2025-12-10, 40",
                ],
            )
            fun `deve encerrar a conta`(dueDate: LocalDate, days: Long) {
                val subscription = setupSubscription()
                setupRecurrence(subscription, billIds = listOf(BillId("ACTIVE_BILL"), billPaid.billId))
                setupActiveBill(dueDate)

                withGivenDateTime(dueDate.plusDays(days).atStartOfDay(brazilTimeZone).plusHours(12)) {
                    subscriptionService.synchronizePixSubscriptionPaymentStatus(accountId = accountId)
                }

                val closureDetailsSlot = slot<AccountClosureDetails.WithReason>()
                val delaySecondsSlot = slot<Int>()
                verify {
                    closeAccountService.closeAccount(accountId, capture(closureDetailsSlot), capture(delaySecondsSlot))
                }

                with(closureDetailsSlot.captured) {
                    this.reason shouldBe AccountClosureReason.SUBSCRIPTION_OVERDUE
                }

                with(delaySecondsSlot.captured) {
                    this shouldBeGreaterThanOrEqualTo 300 // deve executar com delay para garantir que o save(updatedAccount) rodou antes
                }

                verify {
                    notificationAdapter.notifySubscriptionOverdueCloseAccount(accountId)
                }
            }

            @ParameterizedTest
            @EnumSource(AccountStatus::class, mode = EnumSource.Mode.INCLUDE, names = ["CLOSED", "PENDING_CLOSE"])
            fun `nao deve encerrar uma conta pendente de encerramento ou encerrada`(status: AccountStatus) {
                val subscription = setupSubscription()
                setupRecurrence(subscription, billIds = listOf(BillId("ACTIVE_BILL"), billPaid.billId))
                setupActiveBill(62L)
                setupAccount(status = status)

                subscriptionService.synchronizePixSubscriptionPaymentStatus(accountId = accountId)

                verify(exactly = 0) {
                    closeAccountService.closeAccount(any(), any())
                    notificationAdapter wasNot called
                }
            }

            @ParameterizedTest
            @EnumSource(SubscriptionStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["ACTIVE"])
            fun `nao deve enviar notificacao quando a assinatura nao esta ativa`(status: SubscriptionStatus) {
                setupActiveBill()
                val subscription = setupSubscription(status = status)
                setupRecurrence(subscription, billIds = listOf(BillId("ACTIVE_BILL"), billPaid.billId))

                subscriptionService.synchronizePixSubscriptionPaymentStatus(accountId = accountId)

                verify {
                    notificationAdapter wasNot called
                }

                val updateSubscription = subscriptionDbRepository.find(subscription.accountId)!!

                updateSubscription shouldBe subscription
            }

            @Test
            fun `nao deve enviar notificacao quando a assinatura nao esta vencida`() {
                val subscription = setupSubscription()
                setupRecurrence(subscription, billIds = listOf(billPaid.billId))
                setupActiveBill()

                subscriptionService.synchronizePixSubscriptionPaymentStatus(accountId = accountId)

                verify {
                    notificationAdapter wasNot called
                }
            }
        }

        @DisplayName("quando um usuário pagar a assinatura")
        @Nested
        inner class SubscriptionPaymentTest {
            @Test
            fun `deve mudar status de pagamento do usuario para UpToDate, status de assinatura para PAID e status do usuario para ACTIVE`() {
                setupAccount(paymentStatus = AccountPaymentStatus.Overdue, status = AccountStatus.BLOCKED)
                val subscription = setupSubscription()
                setupRecurrence(subscription, billIds = listOf(billPaid.billId))

                subscriptionService.handleSubscriptionPayment(accountId = accountId)

                with(subscriptionDbRepository.find(subscription.accountId)!!) {
                    paymentStatus shouldBe SubscriptionPaymentStatus.PAID
                }

                val account = accountRepository.findById(accountId)
                account.paymentStatus shouldBe AccountPaymentStatus.UpToDate
                account.status shouldBe AccountStatus.ACTIVE
            }

            @Test
            fun `nao deve mudar nenhum status do usuario e da assinatura caso ele seja adimplente`() {
                setupAccount(paymentStatus = AccountPaymentStatus.UpToDate, status = AccountStatus.ACTIVE)
                val subscription = setupSubscription()
                setupRecurrence(subscription, billIds = listOf(billPaid.billId))

                subscriptionService.handleSubscriptionPayment(accountId = accountId)

                verify { recurrenceService wasNot called }

                val account = accountRepository.findById(accountId)
                account.paymentStatus shouldBe AccountPaymentStatus.UpToDate
                account.status shouldBe AccountStatus.ACTIVE
            }

            @ParameterizedTest
            @EnumSource(DDAStatus::class, mode = EnumSource.Mode.INCLUDE, names = ["CLOSED", "PENDING_CLOSE", "CLOSING"])
            fun `deve ativar o dda do usuario caso ele esteja em algum status de fechamento`(ddaStatus: DDAStatus) {
                every {
                    ddaService.findDDARegister(any())
                } returns mockk {
                    every { status } returns ddaStatus
                }

                setupAccount(paymentStatus = AccountPaymentStatus.Overdue, status = AccountStatus.BLOCKED)
                val subscription = setupSubscription()
                setupRecurrence(subscription, billIds = listOf(billPaid.billId))

                subscriptionService.handleSubscriptionPayment(accountId = accountId)

                verify {
                    ddaService.register(accountId, any())
                }
            }

            @Test
            fun `deve ativar o dda do usuario caso ele nao tenha o registro`() {
                every {
                    ddaService.findDDARegister(any())
                } returns null

                setupAccount(paymentStatus = AccountPaymentStatus.Overdue, status = AccountStatus.BLOCKED)
                val subscription = setupSubscription()
                setupRecurrence(subscription, billIds = listOf(billPaid.billId))

                subscriptionService.handleSubscriptionPayment(accountId = accountId)

                verify {
                    ddaService.register(accountId, any())
                }
            }

            @ParameterizedTest
            @EnumSource(DDAStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["CLOSED", "PENDING_CLOSE", "CLOSING"])
            fun `nao deve tentar ativar o dda do usuario se estiver em estado nao permitido de ativacao`(ddaStatus: DDAStatus) {
                every {
                    ddaService.findDDARegister(any())
                } returns mockk {
                    every { status } returns ddaStatus
                }

                setupAccount(paymentStatus = AccountPaymentStatus.Overdue, status = AccountStatus.BLOCKED)
                val subscription = setupSubscription()
                setupRecurrence(subscription, billIds = listOf(billPaid.billId))

                subscriptionService.handleSubscriptionPayment(accountId = accountId)

                verify(exactly = 0) {
                    ddaService.register(accountId, any())
                }
            }
        }

        @Nested
        @DisplayName("quando o usuário já tiver uma conta de assinatura em aberto e vencida")
        inner class UnscheduleCurrentSubscriptionTest {
            @Test
            fun `deve desagendar a nova conta em aberto e deixar agendada apenas a mais antiga`() {
                setupAccount(paymentStatus = AccountPaymentStatus.Overdue, status = AccountStatus.BLOCKED)
                val firstActiveBill = setupActiveBill(35L)
                val subscription = setupSubscription(paymentStatus = SubscriptionPaymentStatus.OVERDUE, nextEffectiveDueDate = firstActiveBill.effectiveDueDate)
                setupRecurrence(subscription, billIds = listOf(BillId("ACTIVE_BILL"), pixAdded.billId))

                subscriptionService.unscheduleCurrentSubscription()

                verify {
                    recurrenceService.ignoreSubscriptionFee(any(), any(), getLocalDate().withDayOfMonth(10))
                }

                val account = accountRepository.findById(accountId)
                account.paymentStatus shouldBe AccountPaymentStatus.Overdue
                account.status shouldBe AccountStatus.BLOCKED
            }
        }

        private fun setupAccount(status: AccountStatus = AccountStatus.ACTIVE, paymentStatus: AccountPaymentStatus = AccountPaymentStatus.UpToDate) {
            accountRepository.save(
                ACCOUNT.copy(
                    status = status,
                    paymentStatus = paymentStatus,
                ),
            )
        }

        private fun setupRecurrence(subscription: Subscription, billIds: List<BillId>) {
            every {
                recurrenceService.find(subscription.recurrenceId, subscription.walletId)
            } returns mockk {
                every {
                    walletId
                } returns subscription.walletId
                every {
                    bills
                } returns billIds
            }
        }
    }

    private fun setupSubscription(
        status: SubscriptionStatus = SubscriptionStatus.ACTIVE,
        paymentStatus: SubscriptionPaymentStatus = SubscriptionPaymentStatus.PAID,
        nextEffectiveDueDate: LocalDate = getLocalDate().withDayOfMonth(10),
    ): Subscription {
        val subscription = Subscription(
            accountId = ACCOUNT.accountId,
            document = Document(ACCOUNT.document),
            status = status,
            amount = 990,
            dayOfMonth = 10,
            recurrenceId = RecurrenceId("FOO"),
            paymentStatus = paymentStatus,
            walletId = billPaid.walletId,
            nextEffectiveDueDate = nextEffectiveDueDate,
        )

        subscriptionDbRepository.save(subscription)

        return subscription
    }

    private fun setupActiveBill(overDueDays: Long = 0): BillView {
        val dueDate = if (overDueDays > 0) {
            getLocalDate().minusDays(overDueDays)
        } else {
            getLocalDate().plusMonths(1)
        }

        return setupActiveBill(dueDate)
    }

    private fun setupActiveBill(dueDate: LocalDate): BillView {
        val bill = getActiveBill(dueDate = dueDate, effectiveDueDate = dueDate)

        every {
            billRepository.findBill(BillId("ACTIVE_BILL"), billPaid.walletId)
        } returns bill

        return bill
    }

    companion object {
        @JvmStatic
        fun recurrenceCreationErrors(): Stream<Arguments> = Stream.of(
            Arguments.arguments(RecurrenceCreationError.ServerError(NoStackTraceException("message"))),
            Arguments.arguments(RecurrenceCreationError.DueDateAfterLimit),
            Arguments.arguments(RecurrenceCreationError.DueDateInThePast),
        )

        @JvmStatic
        fun cancelSubscriptionErrors(): Stream<Arguments> = Stream.of(
            Arguments.arguments(CancelSubscriptionResult.RecurrenceInvalid),
            Arguments.arguments(CancelSubscriptionResult.RecurrenceNotFound),
        )
    }
}