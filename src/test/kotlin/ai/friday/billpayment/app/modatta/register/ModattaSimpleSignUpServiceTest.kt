// package ai.friday.billpayment.app.modatta.register
//
// import DynamoDBUtils.setupDynamoDB
// import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
// import ai.friday.billpayment.adapters.dynamodb.DEFAULT_LEGACY_ACCOUNT_CONFIGURATION
// import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
// import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
// import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
// import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
// import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
// import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
// import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
// import ai.friday.billpayment.adapters.modatta.ModattaCorpAdapter
// import ai.friday.billpayment.adapters.modatta.dynamodb.ModattaSimpleSignUpDbRepository
// import ai.friday.billpayment.adapters.modatta.dynamodb.ModattaSimpleSignUpDynamoDAO
// import ai.friday.billpayment.app.Document
// import ai.friday.billpayment.app.EmailAddress
// import ai.friday.billpayment.app.MobilePhone
// import ai.friday.billpayment.app.account.Account
// import ai.friday.billpayment.app.account.AccountId
// import ai.friday.billpayment.app.account.AccountProviderName
// import ai.friday.billpayment.app.account.AccountStatus
// import ai.friday.billpayment.app.account.ExternalId
// import ai.friday.billpayment.app.account.StoredObject
// import ai.friday.billpayment.app.liveness.LivenessId
// import ai.friday.billpayment.app.modatta.UserId
// import ai.friday.billpayment.app.modatta.integrations.PartnerSimpleSignUpService
// import ai.friday.billpayment.app.subscription.SubscriptionType
// import ai.friday.billpayment.app.wallet.WalletId
// import ai.friday.billpayment.integration.LocalDbCreationRule
// import ai.friday.billpayment.integration.WALLET_ID
// import ai.friday.billpayment.integration.WalletFixture
// import ai.friday.billpayment.integration.createBillPaymentTable
// import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
// import io.kotest.matchers.shouldBe
// import io.kotest.matchers.types.shouldBeTypeOf
// import io.mockk.every
// import io.mockk.mockk
// import io.mockk.verify
// import java.time.LocalDate
// import java.util.stream.Stream
// import org.junit.jupiter.api.BeforeEach
// import org.junit.jupiter.api.Disabled
// import org.junit.jupiter.api.Test
// import org.junit.jupiter.params.ParameterizedTest
// import org.junit.jupiter.params.provider.Arguments
// import org.junit.jupiter.params.provider.EnumSource
// import org.junit.jupiter.params.provider.MethodSource
//
// @Disabled
// class ModattaSimpleSignUpServiceTest {
//
//    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
//    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
//    private val enhancedClient = setupDynamoDB()
//    private val accountRepository = AccountDbRepository(dynamoDbDAO)
//
//    private val walletDAO = WalletDynamoDAO(enhancedClient)
//    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
//    private val inviteDAO = InviteDynamoDAO(enhancedClient)
//    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
//    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)
//
//    private val walletRepository = WalletDbRepository(
//        walletDAO = walletDAO,
//        walletMemberDAO = walletMemberDAO,
//        inviteDAO = inviteDAO,
//        inviteReminderDAO = inviteReminderDAO,
//        walletLimitDAO = walletLimitDAO,
//        accountRepository = accountRepository,
//    )
//    private val modattaSimpleSignUpDynamoDAO = ModattaSimpleSignUpDynamoDAO(enhancedClient)
//    private val simpleSignUpRepository = ModattaSimpleSignUpDbRepository(modattaSimpleSignUpDynamoDAO)
//    private val modattaCorpAdapter = mockk<ModattaCorpAdapter>(relaxed = true)
//    private val partnerSimpleSignUpService: PartnerSimpleSignUpService = mockk {
//        every {
//            getAccountStatus(any())
//        } returns AccountStatus.ACTIVE
//    }
//
//    private val modattaSimpleSignUpService = ModattaSimpleSignUpService(
//        livenessService = mockk(),
//        partnerSimpleSignUpService = partnerSimpleSignUpService,
//        simpleSignUpRepository = simpleSignUpRepository,
//        accountRepository = accountRepository,
//        agreementFilesService = mockk(),
//        userFilesConfiguration = mockk(),
//        objectRepository = mockk(),
//        walletRepository = walletRepository,
//        modattaCorpAdapter = modattaCorpAdapter,
//        accountService = mockk(relaxed = true),
//    )
//
//    private val modattaAccountId = AccountId("ACCOUNT-ID-MODATTA")
//    private val fridayAccountId = AccountId("ACCOUNT-ID-FRIDAY")
//
//    private val modattaAccount = Account(
//        accountId = modattaAccountId,
//        name = "Nome na Conta",
//        emailAddress = EmailAddress("email@conta"),
//        document = "***********",
//        documentType = "CPF",
//        mobilePhone = "+*************",
//        created = getZonedDateTime(),
//        updated = getZonedDateTime(),
//        status = AccountStatus.UNDER_EXTERNAL_REVIEW,
//        configuration = DEFAULT_LEGACY_ACCOUNT_CONFIGURATION.copy(
//            defaultWalletId = WalletId(WALLET_ID),
//            externalId = ExternalId(
//                value = fridayAccountId.value,
//                providerName = AccountProviderName.FRIDAY,
//            ),
//        ),
//        imageUrlSmall = null,
//        imageUrlLarge = null,
//        subscriptionType = SubscriptionType.PIX,
//    )
//
//    private val modattaSimpleSignUp = ModattaSimpleSignUp(
//        userId = UserId(modattaAccount.accountId.value),
//        userData = UserData(
//            name = "Nome do Cadastro",
//            birthDate = LocalDate.now(),
//            document = Document("***********"),
//            mobilePhone = MobilePhone(msisdn = "+*************"),
//            email = EmailAddress(email = "email@cadastro"),
//        ),
//        enrollment = null,
//        userContract = null,
//        validationStatus = ModattaSimpleSignUpValidationStatus.VALID_WITHOUT_PARTNER_ACCOUNT,
//    )
//
//    private val callbackSimpleSignUpRequest = ModattaSimpleSignUpUpdateStatusRequest(
//        fridayAccountId = fridayAccountId,
//        status = AccountStatus.APPROVED,
//        approvedBy = SimpleSignUpApprover.EXTERNAL_ACCOUNT_REGISTER,
//        externalId = ExternalId(
//            value = modattaAccountId.value,
//            providerName = AccountProviderName.MODATTA,
//        ),
//    )
//
//    @BeforeEach
//    fun setup() {
//        createBillPaymentTable(dynamoDB)
//    }
//
//    @ParameterizedTest
//    @EnumSource(AccountProviderName::class, mode = EnumSource.Mode.EXCLUDE, names = ["MODATTA"])
//    fun `deve retornar InvalidProviderName ao receber um externalId que nao seja da Modatta`(accountProviderName: AccountProviderName) {
//        val request = callbackSimpleSignUpRequest.copy(
//            externalId = ExternalId(
//                value = "mock",
//                providerName = accountProviderName,
//            ),
//        )
//
//        val result = modattaSimpleSignUpService.updateStatus(request)
//
//        result.isLeft() shouldBe true
//
//        result.mapLeft {
//            it.shouldBeTypeOf<SimpleSignUpCallbackError.InvalidProviderName>()
//            it.providerName shouldBe accountProviderName
//        }
//    }
//
//    @Test
//    fun `deve retornar AccountNotFound ao receber um externalId inexistente`() {
//        val result = modattaSimpleSignUpService.updateStatus(callbackSimpleSignUpRequest)
//
//        result.isLeft() shouldBe true
//
//        result.mapLeft {
//            it.shouldBeTypeOf<SimpleSignUpCallbackError.AccountNotFound>()
//            it.accountId.value shouldBe callbackSimpleSignUpRequest.externalId.value
//        }
//    }
//
//    @Test
//    fun `deve retornar conflito ao receber o status REPROVADO para uma conta ativa`() {
//        accountRepository.save(modattaAccount.copy(status = AccountStatus.ACTIVE))
//        simpleSignUpRepository.save(modattaSimpleSignUp)
//
//        val request = callbackSimpleSignUpRequest.copy(status = AccountStatus.DENIED)
//
//        val result = modattaSimpleSignUpService.updateStatus(request)
//
//        result.isLeft() shouldBe true
//
//        result.mapLeft {
//            it.shouldBeTypeOf<SimpleSignUpCallbackError.Conflict>()
//            it.reason shouldBe ConflictReason.INVALID_STATUS
//        }
//    }
//
//    @Test
//    fun `deve retornar conflito ao receber o status REPROVADO para uma conta reprovada com externalId diferente`() {
//        accountRepository.save(modattaAccount.copy(status = AccountStatus.DENIED))
//        simpleSignUpRepository.save(modattaSimpleSignUp)
//
//        val request = callbackSimpleSignUpRequest.copy(status = AccountStatus.DENIED)
//
//        val result = modattaSimpleSignUpService.updateStatus(
//            request.copy(
//                fridayAccountId = AccountId("INVALID-FRIDAY-ACCOUNT-ID"),
//            ),
//        )
//
//        result.isLeft() shouldBe true
//
//        result.mapLeft {
//            it.shouldBeTypeOf<SimpleSignUpCallbackError.Conflict>()
//            it.reason shouldBe ConflictReason.INVALID_EXTERNAL_ID
//        }
//    }
//
//    @Test
//    fun `deve retornar conflito ao receber o status REPROVADO para uma conta reprovada com origem de aprovacao incompativel com o estado da validacao de telefone e documento`() {
//        accountRepository.save(modattaAccount.copy(status = AccountStatus.UNDER_EXTERNAL_REVIEW))
//        simpleSignUpRepository.save(modattaSimpleSignUp)
//
//        simpleSignUpRepository.save(
//            simpleSignUp = ModattaSimpleSignUp(
//                userId = UserId(modattaAccount.accountId.value),
//                userData = null,
//                enrollment = null,
//                userContract = null,
//                validationStatus = ModattaSimpleSignUpValidationStatus.VALID_WITHOUT_PARTNER_ACCOUNT,
//            ),
//        )
//
//        val request = callbackSimpleSignUpRequest.copy(
//            status = AccountStatus.DENIED,
//            approvedBy = SimpleSignUpApprover.USER,
//        )
//
//        val result = modattaSimpleSignUpService.updateStatus(
//            request,
//        )
//
//        result.isLeft() shouldBe true
//
//        result.mapLeft {
//            it.shouldBeTypeOf<SimpleSignUpCallbackError.Conflict>()
//            it.reason shouldBe ConflictReason.INVALID_APPROVER
//        }
//    }
//
//    @ParameterizedTest
//    @MethodSource("invalidApprovers")
//    fun `deve retornar conflito ao receber uma requisicao com origem de aprovacao incompativel com o estado da validacao de telefone e documento`(
//        modattaSimpleSignUpValidationStatus: ModattaSimpleSignUpValidationStatus,
//        simnpleSignUpApprover: SimpleSignUpApprover,
//        accountStatus: AccountStatus,
//    ) {
//        accountRepository.save(modattaAccount.copy(status = AccountStatus.UNDER_EXTERNAL_REVIEW))
//        simpleSignUpRepository.save(modattaSimpleSignUp.copy(validationStatus = modattaSimpleSignUpValidationStatus))
//
//        val request = callbackSimpleSignUpRequest.copy(
//            status = accountStatus,
//            approvedBy = simnpleSignUpApprover,
//        )
//
//        val result = modattaSimpleSignUpService.updateStatus(
//            request,
//        )
//
//        result.isLeft() shouldBe true
//
//        result.mapLeft {
//            it.shouldBeTypeOf<SimpleSignUpCallbackError.Conflict>()
//            it.reason shouldBe ConflictReason.INVALID_APPROVER
//        }
//    }
//
//    @Test
//    fun `deve retornar sucesso ao receber o status REPROVADO para uma conta reprovada com o mesmo externalId`() {
//        accountRepository.save(modattaAccount.copy(status = AccountStatus.DENIED))
//        simpleSignUpRepository.save(modattaSimpleSignUp)
//
//        val request = callbackSimpleSignUpRequest.copy(status = AccountStatus.DENIED)
//
//        val result = modattaSimpleSignUpService.updateStatus(
//            request,
//        )
//
//        result.isRight() shouldBe true
//
//        result.map { it: Account ->
//            it.accountId shouldBe modattaAccount.accountId
//            it.status shouldBe AccountStatus.DENIED
//        }
//    }
//
//    @Test
//    fun `deve retornar sucesso ao receber o status REPROVADO para uma conta UNDER_EXTERNAL_REVIEW`() {
//        accountRepository.save(modattaAccount.copy(status = AccountStatus.UNDER_EXTERNAL_REVIEW))
//        simpleSignUpRepository.save(modattaSimpleSignUp)
//
//        val request = callbackSimpleSignUpRequest.copy(status = AccountStatus.DENIED)
//
//        val result = modattaSimpleSignUpService.updateStatus(
//            request,
//        )
//
//        result.isRight() shouldBe true
//
//        result.map { it: Account ->
//            it.accountId shouldBe modattaAccount.accountId
//            it.status shouldBe AccountStatus.DENIED
//        }
//
//        val account = accountRepository.findById(modattaAccount.accountId)
//
//        account.status shouldBe AccountStatus.DENIED
//    }
//
//    @Test
//    fun `deve retornar conflito ao receber o status ATIVO para uma conta reprovada`() {
//        accountRepository.save(modattaAccount.copy(status = AccountStatus.DENIED))
//        simpleSignUpRepository.save(modattaSimpleSignUp)
//
//        val request = callbackSimpleSignUpRequest.copy(status = AccountStatus.ACTIVE)
//
//        val result = modattaSimpleSignUpService.updateStatus(request)
//
//        result.isLeft() shouldBe true
//
//        result.mapLeft {
//            it.shouldBeTypeOf<SimpleSignUpCallbackError.Conflict>()
//            it.reason shouldBe ConflictReason.INVALID_STATUS
//        }
//    }
//
//    @Test
//    fun `deve retornar conflito ao receber o status ATIVO para uma conta ativa com externalId diferente`() {
//        accountRepository.save(modattaAccount.copy(status = AccountStatus.ACTIVE))
//        simpleSignUpRepository.save(modattaSimpleSignUp)
//
//        val request = callbackSimpleSignUpRequest.copy(status = AccountStatus.ACTIVE)
//
//        val result = modattaSimpleSignUpService.updateStatus(
//            request.copy(
//                fridayAccountId = AccountId("INVALID-FRIDAY-ACCOUNT-ID"),
//            ),
//        )
//
//        result.isLeft() shouldBe true
//
//        result.mapLeft {
//            it.shouldBeTypeOf<SimpleSignUpCallbackError.Conflict>()
//            it.reason shouldBe ConflictReason.INVALID_EXTERNAL_ID
//        }
//    }
//
//    @Test
//    fun `deve retornar sucesso ao receber o status ATIVO para uma conta ativa com o mesmo externalId`() {
//        accountRepository.save(modattaAccount.copy(status = AccountStatus.ACTIVE))
//        simpleSignUpRepository.save(modattaSimpleSignUp)
//
//        val request = callbackSimpleSignUpRequest.copy(status = AccountStatus.ACTIVE)
//
//        val result = modattaSimpleSignUpService.updateStatus(
//            request,
//        )
//
//        result.isRight() shouldBe true
//
//        result.map { it: Account ->
//            it.accountId shouldBe modattaAccount.accountId
//            it.status shouldBe AccountStatus.ACTIVE
//        }
//    }
//
//    @Test
//    fun `deve retornar sucesso ao receber o status ATIVO para uma conta UNDER_EXTERNAL_REVIEW`() {
//        val walletFixture = WalletFixture(founderAccountId = modattaAccount.accountId)
//        val wallet = walletFixture.buildWallet()
//        walletRepository.save(wallet)
//
//        accountRepository.save(
//            modattaAccount.copy(
//                status = AccountStatus.UNDER_EXTERNAL_REVIEW,
//                configuration = modattaAccount.configuration.copy(defaultWalletId = wallet.id),
//            ),
//        )
//        simpleSignUpRepository.save(modattaSimpleSignUp)
//
//        val request = callbackSimpleSignUpRequest.copy(status = AccountStatus.ACTIVE)
//
//        val result = modattaSimpleSignUpService.updateStatus(
//            request,
//        )
//
//        result.isRight() shouldBe true
//
//        result.map { it: Account ->
//            it.accountId shouldBe modattaAccount.accountId
//            it.status shouldBe AccountStatus.ACTIVE
//        }
//
//        val account = accountRepository.findById(modattaAccount.accountId)
//
//        account.status shouldBe AccountStatus.ACTIVE
//        account.name shouldBe modattaSimpleSignUp.userData?.name
//        account.document shouldBe modattaSimpleSignUp.userData?.document?.value
//        account.emailAddress shouldBe modattaSimpleSignUp.userData?.email
//        account.mobilePhone shouldBe modattaSimpleSignUp.userData?.mobilePhone?.msisdn
//
//        val updatedWallet = walletRepository.findWallet(account.defaultWalletId())
//
//        with(updatedWallet.founder) {
//            this.name shouldBe modattaSimpleSignUp.userData?.name
//            this.document shouldBe modattaSimpleSignUp.userData?.document?.value
//            this.emailAddress shouldBe modattaSimpleSignUp.userData?.email
//        }
//    }
//
//    @ParameterizedTest
//    @EnumSource(AccountStatus::class, mode = EnumSource.Mode.INCLUDE, names = ["ACTIVE", "DENIED"])
//    fun `deve notificar a modatta quando o status do cadastro for modificado`(status: AccountStatus) {
//        val walletFixture = WalletFixture(founderAccountId = modattaAccount.accountId)
//        val wallet = walletFixture.buildWallet()
//        walletRepository.save(wallet)
//
//        accountRepository.save(
//            modattaAccount.copy(
//                status = AccountStatus.UNDER_EXTERNAL_REVIEW,
//                configuration = modattaAccount.configuration.copy(defaultWalletId = wallet.id),
//            ),
//        )
//
//        simpleSignUpRepository.save(
//            modattaSimpleSignUp.copy(
//                userContract = UserContract(
//                    hasAccepted = true,
//                    contract = StoredObject(
//                        region = "us-east-1",
//                        bucket = "test-bucket",
//                        key = "test-contract.pdf",
//                    ),
//                ),
//                enrollment = Enrollment(livenessId = LivenessId(value = "OLD-LIVENESS-ID"), done = true),
//            ),
//        )
//
//        val request = callbackSimpleSignUpRequest.copy(status = status)
//
//        modattaSimpleSignUpService.updateStatus(
//            request,
//        )
//
//        val userId = UserId(request.externalId.value)
//
//        verify(exactly = 1) {
//            modattaCorpAdapter.notifyAccountRegisterUpdates(
//                userId.toModattaExternalId(),
//                status,
//            )
//        }
//    }
//
//    companion object {
//        @JvmStatic
//        fun invalidApprovers(): Stream<Arguments> = Stream.of(
//            Arguments.arguments(
//                ModattaSimpleSignUpValidationStatus.VALID_WITHOUT_PARTNER_ACCOUNT,
//                SimpleSignUpApprover.USER,
//                AccountStatus.DENIED,
//            ),
//            Arguments.arguments(
//                ModattaSimpleSignUpValidationStatus.VALID_WITHOUT_PARTNER_ACCOUNT,
//                SimpleSignUpApprover.USER,
//                AccountStatus.ACTIVE,
//            ),
//            Arguments.arguments(
//                ModattaSimpleSignUpValidationStatus.VALID_WITH_PARTNER_ACCOUNT,
//                SimpleSignUpApprover.EXTERNAL_ACCOUNT_REGISTER,
//                AccountStatus.DENIED,
//            ),
//            Arguments.arguments(
//                ModattaSimpleSignUpValidationStatus.VALID_WITH_PARTNER_ACCOUNT,
//                SimpleSignUpApprover.EXTERNAL_ACCOUNT_REGISTER,
//                AccountStatus.ACTIVE,
//            ),
//        )
//    }
// }