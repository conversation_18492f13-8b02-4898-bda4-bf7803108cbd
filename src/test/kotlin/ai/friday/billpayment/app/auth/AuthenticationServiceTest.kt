package ai.friday.billpayment.app.auth

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.SignInPendingAction
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.integration.ACCOUNT_ID
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

internal class AuthenticationServiceTest {
    val deviceFingerprintService = mockk<DeviceFingerprintService>()
    val authenticationService = AuthenticationService(deviceFingerprintService = deviceFingerprintService)

    @Nested
    inner class GetSignInPendingAction {
        val accountId = AccountId(ACCOUNT_ID)
        val deviceFingerprint = "base64-device-fingerprint-payload"

        @Nested
        @DisplayName("quando o usuário é guest")
        inner class GetGuestUserSignInPendingAction {
            private val user = UserPrincipal.Guest(accountId = accountId)

            @Test
            fun `deve retornar NONE`() {
                authenticationService.getSignInPendingAction(user) shouldBe SignInPendingAction.NONE
            }

            @Test
            fun `não deve verificar se o dispositivo é vinculado`() {
                authenticationService.getSignInPendingAction(user)

                verify(exactly = 0) {
                    deviceFingerprintService.isValidFingerprint(any(), any())
                }
            }
        }

        @Nested
        @DisplayName("quando o usuário é OWNER, mas ainda não foi criado no Cognito")
        inner class OwnerWithoutCognitoUser {
            private val user = UserPrincipal.Owner(
                accountId = accountId,
                method = AuthenticationMethod.Msisdn(hasPassword = false),
                deviceFingerprint = deviceFingerprint,
            )

            @Test
            fun `deve retornar CREATE_PASSWORD_REQUIRED`() {
                authenticationService.getSignInPendingAction(user) shouldBe SignInPendingAction.CREATE_PASSWORD_REQUIRED
            }

            @Test
            fun `não deve verificar se o dispositivo é vinculado`() {
                authenticationService.getSignInPendingAction(user)

                verify(exactly = 0) {
                    deviceFingerprintService.isValidFingerprint(any(), any())
                }
            }
        }

        @Nested
        @DisplayName("quando o usuário é OWNER, já foi criado no Cognito, mas se autenticou com o MSISDN")
        inner class OwnerWithCognitoUserUsingMsisdnAuthentication {
            private val user = UserPrincipal.Owner(
                accountId = accountId,
                AuthenticationMethod.Msisdn(hasPassword = true),
                deviceFingerprint = deviceFingerprint,
            )

            @Test
            fun `deve retornar PASSWORD_AUTHENTICATION_REQUIRED`() {
                authenticationService.getSignInPendingAction(user) shouldBe SignInPendingAction.PASSWORD_AUTHENTICATION_REQUIRED
            }

            @Test
            fun `não deve verificar se o dispositivo é vinculado`() {
                authenticationService.getSignInPendingAction(user)

                verify(exactly = 0) {
                    deviceFingerprintService.isValidFingerprint(any(), any())
                }
            }
        }

        @Nested
        @DisplayName("quando o usuário é OWNER, já foi criado no Cognito e se autenticou com senha")
        inner class OwnerWithCognitoUserUsingPasswordAuthentication {

            @Nested
            @DisplayName("e é passado um device")
            inner class ProvidingDeviceFingerprint {
                private val user = UserPrincipal.Owner(
                    accountId = accountId,
                    method = AuthenticationMethod.Password,
                    deviceFingerprint = deviceFingerprint,
                )

                @Test
                fun `deve retornar DEVICE_BINDING_REQUIRED se passar um device for desconhecido`() {
                    every { deviceFingerprintService.isValidFingerprint(any(), any()) } returns false

                    authenticationService.getSignInPendingAction(user) shouldBe SignInPendingAction.DEVICE_BINDING_REQUIRED
                }

                @Test
                fun `deve retornar NONE se passar um device conhecido`() {
                    every { deviceFingerprintService.isValidFingerprint(any(), any()) } returns true

                    authenticationService.getSignInPendingAction(user) shouldBe SignInPendingAction.NONE
                }

                @Test
                fun `deve verificar se o dispositivo está vinculado ao usuário`() {
                    every { deviceFingerprintService.isValidFingerprint(any(), any()) } returns true

                    authenticationService.getSignInPendingAction(user)

                    verify {
                        deviceFingerprintService.isValidFingerprint(
                            accountId = accountId,
                            deviceFingerprintPayload = deviceFingerprint,
                        )
                    }
                }
            }

            @Nested
            @DisplayName("e não é passado um device")
            inner class NotProvidingADeviceFingerprint {
                private val user = UserPrincipal.Owner(
                    accountId = accountId,
                    method = AuthenticationMethod.Password,
                    deviceFingerprint = null,
                )

                @Test
                fun `deve retornar NONE`() {
                    authenticationService.getSignInPendingAction(user) shouldBe SignInPendingAction.NONE
                }

                @Test
                fun `não deve validar o dispositivo`() {
                    authenticationService.getSignInPendingAction(user)

                    verify(exactly = 0) {
                        deviceFingerprintService.isValidFingerprint(any(), any())
                    }
                }
            }
        }
    }
}