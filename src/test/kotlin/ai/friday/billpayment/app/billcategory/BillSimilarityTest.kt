package ai.friday.billpayment.app.billcategory

import DynamoDBUtils
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.integration.SANITATION_BARCODE_LINE
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.pixAdded
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import java.util.stream.Stream
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class BillSimilarityTest {
    private val enhancedClient = DynamoDBUtils.setupDynamoDB()

    private val billDynamoDAO = BillDynamoDAO(enhancedClient)
    private val refundedBillDynamoDAO = RefundedBillDynamoDAO(enhancedClient)
    private val settlementFundsTransferDynamoDAO = SettlementFundsTransferDynamoDAO(enhancedClient)
    private val billEventDAO = BillEventDynamoDAO(enhancedClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val billRepository = DynamoDbBillRepository(billDynamoDAO, refundedBillDynamoDAO, settlementFundsTransferDynamoDAO)

    private val billEventRepository = BillEventDBRepository(
        billEventDAO = billEventDAO,
        uniqueConstraintDAO = uniqueConstraintDAO,
        featureConfiguration = mockk(),
        transactionDynamo = transactionDynamo,
    )

    private val billEventPublisher: BillEventPublisher = mockk() {
        every {
            publish(any(), any())
        } returns Unit
    }

    private val walletBillCategoryService: PFMWalletCategoryService = mockk()

    private val billCategorySuggestionService = BillCategorySuggestionService(billRepository, billEventPublisher, walletBillCategoryService, mockk(relaxed = true), billEventRepository)

    private fun Bill.toBillView(): BillView {
        billRepository.save(this)
        return billRepository.findBill(billId, walletId)
    }

    @ParameterizedTest
    @MethodSource("pixSimilarity")
    fun `quando o tipo das duas contas for PIX as contas devem apresentar as seguintes similaridades`(similarity: Int, otherBill: Bill) {
        val bill = Bill.build(pixAdded)

        billCategorySuggestionService.getSimilarity(bill.toBillView(), otherBill.toBillView()) shouldBe similarity
    }

    @ParameterizedTest
    @MethodSource("concessionariaSimilarity")
    fun `quando o tipo das duas contas for CONCESSIONARIA as contas devem apresentar as seguintes similaridades`(attribute: String, similarity: Int, otherBill: Bill) {
        val bill = Bill.build(billAdded)
        billCategorySuggestionService.getSimilarity(bill.toBillView(), otherBill.toBillView()) shouldBe similarity
    }

    @ParameterizedTest
    @MethodSource("fichaSimilarity")
    fun `quando o tipo das duas contas for FICHA DE COMPENSACAO as contas devem apresentar as seguintes similaridades`(attribute: String, similarity: Int, otherBill: Bill) {
        val bill = Bill.build(billAddedFicha)
        billCategorySuggestionService.getSimilarity(bill.toBillView(), otherBill.toBillView()) shouldBe similarity
    }

    @ParameterizedTest
    @MethodSource("invoiceSimilarity")
    fun `quando o tipo das duas contas for TED as contas devem apresentar as seguintes similaridades`(attribute: String, similarity: Int, otherBill: Bill) {
        val bill = Bill.build(invoiceAdded)
        billCategorySuggestionService.getSimilarity(bill.toBillView(), otherBill.toBillView()) shouldBe similarity
    }

    @ParameterizedTest
    @MethodSource("pixAndInvoiceSimilarity")
    fun `quando for um PIX e um INVOICE as contas devem apresentar as seguintes similaridades`(attribute: String, similarity: Int, otherBill: Bill) {
        val bill = Bill.build(pixAdded)
        billCategorySuggestionService.getSimilarity(bill.toBillView(), otherBill.toBillView()) shouldBe similarity
    }

    @Test
    fun `quando for as contas forem totalmente diferentes deve apresentar 0 similaridade`() {
        val bill = Bill.build(billAddedFicha)
        val otherBill = Bill.build(
            billAdded.copy(
                dueDate = billAdded.dueDate.plusDays(45),
                description = "otherDescription",
                amountTotal = 1,
            ),
        )
        billCategorySuggestionService.getSimilarity(bill.toBillView(), otherBill.toBillView()) shouldBe 0
    }

    companion object {
        private val otherRecipient = Recipient(name = "", document = null, alias = "", bankAccount = null, pixKeyDetails = null, pixQrCodeData = null)
        private val invoiceAddedAsPix = invoiceAdded.copy(
            recipient = pixAdded.recipient,
            amountTotal = pixAdded.amountTotal,
            actionSource = pixAdded.actionSource,
            dueDate = pixAdded.dueDate,
            description = pixAdded.description,
        )

        @JvmStatic
        fun fichaSimilarity(): Stream<Arguments> = Stream.of(
            Arguments.arguments("none", 100, Bill.build(billAddedFicha)),
            Arguments.arguments("recipient", 70, Bill.build(billAddedFicha.copy(recipient = otherRecipient))),
            Arguments.arguments("document", 80, Bill.build(billAddedFicha.copy(document = "***********"))),
            Arguments.arguments("fichaCompensacaoType", 80, Bill.build(billAddedFicha.copy(fichaCompensacaoType = FichaCompensacaoType.WARRANT))),
            Arguments.arguments("dueDate", 90, Bill.build(billAddedFicha.copy(dueDate = billAddedFicha.dueDate.plusDays(45)))),
            Arguments.arguments("amountTotal", 90, Bill.build(billAddedFicha.copy(amountTotal = 1))),
            Arguments.arguments("description", 90, Bill.build(billAddedFicha.copy(description = "other description"))),
        )

        @JvmStatic
        fun invoiceSimilarity(): Stream<Arguments> = Stream.of(
            Arguments.arguments("none", 100, Bill.build(invoiceAdded)),
            Arguments.arguments("recipient", 80, Bill.build(invoiceAdded.copy(recipient = otherRecipient))),
            Arguments.arguments("amountTotal", 80, Bill.build(invoiceAdded.copy(amountTotal = 1))),
            Arguments.arguments("actionSource", 80, Bill.build(invoiceAdded.copy(actionSource = ActionSource.System))),
            Arguments.arguments("dueDate", 80, Bill.build(invoiceAdded.copy(dueDate = invoiceAdded.dueDate.plusDays(40)))),
            Arguments.arguments("description", 80, Bill.build(invoiceAdded.copy(description = "nova descricao"))),
        )

        @JvmStatic
        fun pixAndInvoiceSimilarity(): Stream<Arguments> = Stream.of(
            Arguments.arguments("none", 50, Bill.build(invoiceAddedAsPix)),
            Arguments.arguments("recipient", 40, Bill.build(invoiceAddedAsPix.copy(recipient = otherRecipient))),
            Arguments.arguments("amountTotal", 40, Bill.build(invoiceAddedAsPix.copy(amountTotal = 1))),
            Arguments.arguments("actionSource", 40, Bill.build(invoiceAddedAsPix.copy(actionSource = ActionSource.System))),
            Arguments.arguments("dueDate", 40, Bill.build(invoiceAddedAsPix.copy(dueDate = invoiceAddedAsPix.dueDate.plusDays(45)))),
            Arguments.arguments("description", 40, Bill.build(invoiceAddedAsPix.copy(description = "nova descricao"))),
        )

        @JvmStatic
        fun concessionariaSimilarity(): Stream<Arguments> = Stream.of(
            Arguments.arguments("none", 100, Bill.build(billAdded)),
            Arguments.arguments("assignor", 80, Bill.build(billAdded.copy(assignor = ""))),
            Arguments.arguments("segment", 80, Bill.build(billAdded.copy(barcode = BarCode.ofDigitable(SANITATION_BARCODE_LINE)))),
            Arguments.arguments("companyCode", 80, Bill.build(billAdded.copy(barcode = BarCode.ofDigitable("816800000009857444431073096493867111101007954817")))),
            Arguments.arguments("companyCode and amountTotal", 70, Bill.build(billAdded.copy(barcode = BarCode.ofDigitable("816800000009857444431073096493867111101007954817"), amountTotal = 1))),
            Arguments.arguments("amountTotal", 90, Bill.build(billAdded.copy(amountTotal = 1))),
            Arguments.arguments("actionSource", 90, Bill.build(billAdded.copy(actionSource = ActionSource.System))),
            Arguments.arguments("dueDate", 90, Bill.build(billAdded.copy(dueDate = billAdded.dueDate.plusDays(45)))),
            Arguments.arguments("description", 90, Bill.build(billAdded.copy(description = "nova descricao"))),
        )

        @JvmStatic
        fun pixSimilarity(): Stream<Arguments> = Stream.of(
            Arguments.arguments(100, Bill.build(pixAdded)),
            Arguments.arguments(80, Bill.build(pixAdded.copy(recipient = otherRecipient))),
            Arguments.arguments(80, Bill.build(pixAdded.copy(amountTotal = 1))),
            Arguments.arguments(80, Bill.build(pixAdded.copy(actionSource = ActionSource.System))),
            Arguments.arguments(80, Bill.build(pixAdded.copy(dueDate = pixAdded.dueDate.plusDays(45)))),
            Arguments.arguments(80, Bill.build(pixAdded.copy(description = "nova descricao"))),
        )
    }
}

/*
*
*
* ## Como categorizar
- É um pagamento?
  - É o mesmo dia do mês?
  - É o mesmo valor?
  - É a mesma descricao?
  - É o mesmo source?
- É um PIX?
  - É o mesmo destinatario?

- É uma concessionária?
  - Veio de uma connect utility?
    - mesma UA?
  - É o mesmo código cliente?
  - É o mesmo destinatario?
  - Qual é o segmento?
    - ** Olhar segmento e inferir qual categoria deveria ser


- É uma ficha?
  - Qual é o tipo da ficha?
  - É o mesmo destinatario?
  - É o mesmo sacado?
  - Qual é o tipo?
    - `
    "1": 	 "CH Cheque",
    "2": 	 "DM Duplicata Mercantil",
    "3": 	 "DMI Duplicata Mercantil Indicação",
    "4": 	 "DS Duplicata de Serviço",
    "5": 	 "DSI Duplicata de Serviço Indicação",
    "6": 	 "DR Duplicata Rural",
    "7": 	 "LC Letra de Câmbio",
    "8": 	 "NCC Nota de Crédito Comercial",
    "9": 	 "NCE Nota de Crédito Exportação",
    "10": 	 "NCI Nota de Crédito Industrial",
    "11": 	 "NCR Nota de Crédito Rural",
    "12": 	 "NP Nota Promissória",
    "13": 	 "NPR Nota Promissória Rural",
    "14": 	 "TM Triplicata Mercantil",
    "15": 	 "TS Triplicata de Serviço",
    "16": 	 "NS Nota de Seguro",
    "17": 	 "RC Recibo",
    "18": 	 "FAT Bloqueto",
    "19": 	 "ND Nota de Débito",
    "20": 	 "AP Apólice de Seguro",
    "21": 	 "ME Mensalidade Escolar",
    "22": 	 "PC Parcela de Consórcio",
    "23": 	"NF Nota Fiscal",
    "24": 	"DD Documento de Dívida",
    "25": 	"Cédula de Produto Rural",
    "26": 	"Warrant",
    "27": 	"Dívida Ativa de Estado",
    "28": 	"Dívida Ativa de Município",
    "29": 	"Dívida Ativa da União",
    "30": 	"Encargos condominiais",
    "31": 	"Cartão de Crédito",
    "32": 	"Boleto proposta",
    "99": 	"Outros",
*
* */

// * TED