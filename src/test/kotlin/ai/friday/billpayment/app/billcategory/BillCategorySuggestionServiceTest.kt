package ai.friday.billpayment.app.billcategory

import DynamoDBUtils
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillCategorySuggestionAdded
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.FichaCompensacaoAdded
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.pfm.BillCategory
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billAddedInvestment
import ai.friday.billpayment.billCategoryAdded
import arrow.core.right
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class BillCategorySuggestionServiceTest {
    private val enhancedClient = DynamoDBUtils.setupDynamoDB()

    private val billDynamoDAO = BillDynamoDAO(enhancedClient)
    private val refundedBillDynamoDAO = RefundedBillDynamoDAO(enhancedClient)
    private val settlementFundsTransferDynamoDAO = SettlementFundsTransferDynamoDAO(enhancedClient)

    private val billRepository = DynamoDbBillRepository(billDynamoDAO, refundedBillDynamoDAO, settlementFundsTransferDynamoDAO)

    private val billEventRepository = mockk<BillEventRepository> {
        every { getBillById(any()) } returns Bill.build(billAdded).right()
    }

    private val billEventPublisher: BillEventPublisher =
        mockk {
            every {
                publish(any(), any())
            } returns Unit
        }

    private val walletId = WalletId("wallet")
    private val walletBillCategoryService: PFMWalletCategoryService =
        mockk {
            every {
                findWalletCategories(walletId)
            } returns listOf(
                "IMPOSTOS".toWalletBillCategory(),
                "SAUDE".toWalletBillCategory(),
                "EDUCACAO".toWalletBillCategory(),
                "LAZER".toWalletBillCategory(),
                "TRANSPORTE".toWalletBillCategory(enabled = false),
                "INVESTIMENTO".toWalletBillCategory(),
            )
        }

    private val billCategorySuggestionService = BillCategorySuggestionService(billRepository, billEventPublisher, walletBillCategoryService, mockk(relaxed = true), billEventRepository)

    @Test
    fun `quando a conta ja esta categorizada deve retornar uma lista vazia`() {
        val bill = createBill(category = tax).toBillView()
        createBill(category = tax)

        val result = billCategorySuggestionService.suggestCategories(bill)

        result.shouldBeEmpty()
    }

    @Test
    fun `quando nao há contas categorizadas deve retornar uma lista vazia`() {
        val bill = createBill().toBillView()

        val result = billCategorySuggestionService.suggestCategories(bill)

        result.shouldBeEmpty()
    }

    @Test
    fun `quando encontra uma conta similar categorizada deve sugerir a mesma categoria`() {
        val bill = createBill().toBillView()
        createBill(category = tax)

        val result = billCategorySuggestionService.suggestCategories(bill)

        result shouldHaveSize 1
        with(result.first()) {
            categoryId shouldBe tax.categoryId
            probability shouldBe 100
        }
    }

    @Test
    fun `uma conta similar sem categoria nao deve influenciar a probabilidade`() {
        val bill = createBill().toBillView()
        createBill()
        createBill(category = tax)

        val result = billCategorySuggestionService.suggestCategories(bill)

        result shouldHaveSize 1
        with(result.first()) {
            categoryId shouldBe tax.categoryId
            probability shouldBe 100
        }
    }

    @Test
    fun `uma conta similar com categoria arquivada nao deve influenciar a probabilidade`() {
        val bill = createBill().toBillView()
        createBill(category = categoriaArquivada)
        createBill(category = tax)

        val result = billCategorySuggestionService.suggestCategories(bill)

        result shouldHaveSize 1
        with(result.first()) {
            categoryId shouldBe tax.categoryId
            probability shouldBe 100
        }
    }

    @Test
    fun `quando encontra mais de uma conta similar com a mesma categoria deve sugerir a categoria ponderando a probabilidade`() {
        val bill = createBill().toBillView()
        createBill(category = tax) // 100%
        createBill(category = health, billEvent = billAddedFicha, description = "qualquer coisa")
        createBill(category = health, billEvent = billAddedFicha, description = "qualquer coisa")
        createBill(category = tax, assignor = "otherAssignor") // 80%
        createBill(category = tax, description = "otherDescription") // 90%

        val result = billCategorySuggestionService.suggestCategories(bill)

        result.shouldContainExactlyInAnyOrder(
            BillCategorySuggestion(tax.categoryId, 100, 1),
            BillCategorySuggestion(health.categoryId, 20, 2),
        )
    }

    @Test
    fun `quando encontra duas contas similares com categorias diferentes deve sugerir as duas categorias`() {
        val bill = createBill().toBillView()
        createBill(category = tax)
        createBill(category = health)

        val result = billCategorySuggestionService.suggestCategories(bill)

        result.shouldContainExactlyInAnyOrder(
            BillCategorySuggestion(tax.categoryId, 100, 1),
            BillCategorySuggestion(health.categoryId, 100, 1),
        )
    }

    @MethodSource("suggestions")
    @ParameterizedTest
    fun `quando encontra diversas contas similares categorizadas deve sugerir as categorias ordenadas por probabilidade`(billType: BillType, suggestions: List<BillCategorySuggestion>) {
        val bill = createBill(billType = billType).toBillView()
        repeat(4) { createBill(category = tax) }
        repeat(3) { createBill(category = health) }
        repeat(2) { createBill(category = health, billEvent = billAddedFicha, description = "qualquer coisa") }
        repeat(2) { createBill(category = education) }
        createBill(category = education, billEvent = billAddedFicha, description = "qualquer outra coisa")
        createBill(category = entertainment, billEvent = billAddedFicha, description = "entertainment")
        createBill(category = investment, billEvent = billAddedInvestment, description = "investimento")

        val result = billCategorySuggestionService.suggestCategories(bill)

        result.shouldContainExactlyInAnyOrder(suggestions)
    }

    @Test
    fun `quando recebe uma lista de sugestão de categorias deve adicionar o evento de sugestão`() {
        val bill = createBill()
        val suggestions = listOf(BillCategorySuggestion(PFMCategoryId("1"), 80), BillCategorySuggestion(PFMCategoryId("2"), 70))

        billCategorySuggestionService.setBillCategorySuggestions(
            billId = bill.billId,
            walletId = bill.walletId,
            categorySuggestions = suggestions,
        )

        val slot = slot<BillCategorySuggestionAdded>()

        verify(exactly = 1) { billEventPublisher.publish(any(), capture(slot)) }

        slot.captured.billId shouldBe bill.billId
        slot.captured.walletId shouldBe bill.walletId
        slot.captured.actionSource shouldBe ActionSource.System
        slot.captured.categories shouldBe suggestions
    }

    @Test
    fun `quando recebe uma lista de sugestão de categorias vazia não deve adicionar o evento de sugestão`() {
        val bill = createBill()
        val suggestions = emptyList<BillCategorySuggestion>()

        billCategorySuggestionService.setBillCategorySuggestions(
            billId = bill.billId,
            walletId = bill.walletId,
            categorySuggestions = suggestions,
        )

        verify(exactly = 0) { billEventPublisher.publish(any(), any()) }
    }

    @Test
    fun `quando recebe uma lista de sugestão de categorias deve ordenar e filtrar`() {
        val suggestions = listOf(
            BillCategorySuggestion(PFMCategoryId("1"), 100, 3),
            BillCategorySuggestion(PFMCategoryId("2"), 70, 2),
            BillCategorySuggestion(PFMCategoryId("4"), 100, 2),
            BillCategorySuggestion(PFMCategoryId("3"), 50, 10),
        )

        val result = billCategorySuggestionService.filterCategories(suggestions)

        result.shouldContainExactly(
            BillCategorySuggestion(PFMCategoryId("1"), 100, 3),
            BillCategorySuggestion(PFMCategoryId("4"), 100, 2),
            BillCategorySuggestion(PFMCategoryId("2"), 70, 2),
        )
    }

    @Test
    fun `quando recebe uma lista de sugestão de categorias com similaridade baixa deve ordenar e filtrar`() {
        val suggestions = listOf(
            BillCategorySuggestion(PFMCategoryId("1"), 50, 3),
            BillCategorySuggestion(PFMCategoryId("2"), 30, 2),
            BillCategorySuggestion(PFMCategoryId("4"), 60, 2),
            BillCategorySuggestion(PFMCategoryId("3"), 10, 10),
            BillCategorySuggestion(PFMCategoryId("5"), 49, 8),
        )

        val result = billCategorySuggestionService.filterCategories(suggestions)

        result.shouldContainExactly(
            BillCategorySuggestion(PFMCategoryId("4"), 60, 2),
            BillCategorySuggestion(PFMCategoryId("1"), 50, 3),
        )
    }

    private fun createBill(
        category: BillCategory? = null,
        description: String? = null,
        assignor: String? = null,
        billEvent: BillEvent = billAdded,
        billType: BillType? = null,
    ): Bill {
        return Bill.build(
            when (billEvent) {
                is BillAdded -> billEvent.copy(
                    walletId = walletId,
                    billId = BillId(),
                    description = description ?: billEvent.description,
                    assignor = assignor ?: billEvent.assignor,
                    billType = billType ?: billEvent.billType,
                )

                is FichaCompensacaoAdded -> billEvent.copy(
                    walletId = walletId,
                    billId = BillId(),
                    description = description ?: billEvent.description,
                    assignor = assignor ?: billEvent.assignor,
                )

                else -> billEvent
            },
        ).also { bill -> category?.let { bill.apply(billCategoryAdded.copy(billId = bill.billId, categoryId = it.categoryId)) } }
            .also { billRepository.save(it) }
    }

    private fun Bill.toBillView() = billRepository.findBill(billId, walletId)

    private fun String.toWalletBillCategory(enabled: Boolean = true) = WalletBillCategory(
        walletId = walletId,
        categoryId = toBillCategoryId(),
        name = this,
        icon = this,
        enabled = enabled,
        default = true,
    )

    companion object {
        private val tax = "IMPOSTOS".toBillCategory()
        private val health = "SAUDE".toBillCategory()
        private val education = "EDUCACAO".toBillCategory()
        private val entertainment = "LAZER".toBillCategory()
        private val categoriaArquivada = "TRANSPORTE".toBillCategory()
        private val investment = "INVESTIMENTO".toBillCategory()

        private fun String.toBillCategory() =
            BillCategory(
                categoryId = toBillCategoryId(),
                name = this,
                icon = this,
                default = false,
            )

        private fun String.toBillCategoryId() = PFMCategoryId(value = "BILL_CATEGORY_$this")

        @JvmStatic
        fun suggestions(): List<Arguments> {
            return BillType.entries.mapNotNull { billType ->
                val suggestions: List<BillCategorySuggestion>? = when (billType) {
                    BillType.CONCESSIONARIA -> listOf(
                        BillCategorySuggestion(tax.categoryId, 100, 4),
                        BillCategorySuggestion(health.categoryId, 100, 3),
                        BillCategorySuggestion(education.categoryId, 100, 2),
                        BillCategorySuggestion(entertainment.categoryId, 20, 1),
                        BillCategorySuggestion(investment.categoryId, 20, 1),
                    )

                    BillType.FICHA_COMPENSACAO -> listOf(
                        BillCategorySuggestion(health.categoryId, 40, 5),
                        BillCategorySuggestion(tax.categoryId, 40, 4),
                        BillCategorySuggestion(education.categoryId, 40, 3),
                        BillCategorySuggestion(entertainment.categoryId, 40, 1),
                        BillCategorySuggestion(investment.categoryId, 20, 1),
                    )

                    BillType.INVOICE -> listOf(
                        BillCategorySuggestion(tax.categoryId, 40, 4),
                        BillCategorySuggestion(education.categoryId, 40, 2),
                        BillCategorySuggestion(health.categoryId, 40, 3),
                        BillCategorySuggestion(entertainment.categoryId, 20, 1),
                        BillCategorySuggestion(investment.categoryId, 20, 1),
                    )

                    BillType.PIX -> listOf(
                        BillCategorySuggestion(tax.categoryId, 40, 4),
                        BillCategorySuggestion(education.categoryId, 40, 2),
                        BillCategorySuggestion(health.categoryId, 40, 3),
                        BillCategorySuggestion(entertainment.categoryId, 20, 1),
                        BillCategorySuggestion(investment.categoryId, 20, 1),
                    )

                    BillType.INVESTMENT -> listOf(
                        BillCategorySuggestion(education.categoryId, 40, 2),
                        BillCategorySuggestion(health.categoryId, 40, 3),
                        BillCategorySuggestion(tax.categoryId, 40, 4),
                        BillCategorySuggestion(entertainment.categoryId, 20, 1),
                        BillCategorySuggestion(investment.categoryId, 100, 1),
                    )

                    BillType.OTHERS -> null
                    BillType.AUTOMATIC_PIX -> listOf(
                        BillCategorySuggestion(tax.categoryId, 40, 4),
                        BillCategorySuggestion(education.categoryId, 40, 2),
                        BillCategorySuggestion(health.categoryId, 40, 3),
                        BillCategorySuggestion(entertainment.categoryId, 20, 1),
                        BillCategorySuggestion(investment.categoryId, 20, 1),
                    )
                }

                if (suggestions == null) {
                    null
                } else {
                    Arguments.of(billType, suggestions)
                }
            }
        }
    }
}