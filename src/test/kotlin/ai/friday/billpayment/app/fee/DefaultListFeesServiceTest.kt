package ai.friday.billpayment.app.fee

import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.integrations.CreditCardFeeCalculatorService
import ai.friday.billpayment.app.integrations.ListFeesService
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.fixture.ListFeeRequestFixture
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID_2
import arrow.core.right
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

class DefaultListFeesServiceTest {

    private val walletRepository: WalletRepository = mockk {
        every {
            findWalletOrNull(any())
        } returns mockk {
            every {
                paymentMethodId
            } returns AccountPaymentMethodId(PAYMENT_METHOD_ID)
        }
    }

    private val creditCardFeeCalculatorService = mockk<CreditCardFeeCalculatorService>()

    private val service: ListFeesService =
        DefaultListFeesService(
            walletRepository = walletRepository,
            creditCardFeeCalculatorService = creditCardFeeCalculatorService,
        )

    @Test
    fun `deve retrornar sucesso com os valores das bills e sem resumo quando a bill for paga em saldo`() {
        val request = ListFeeRequestFixture.create()

        mockCalculateInstallment(netAmount = 1000, installments = 1, amount = 1010, total = 1010)

        val response = service.listFees(request)

        response.isRight() shouldBe true

        response.map {
            it.summary shouldBe null
            it.fees.size shouldBe 1
            it.fees[0] shouldBe BillFees(
                billId = "123",
                amount = 1000,
                calculationId = null,
                payments = listOf(
                    FeePaymentMethodBalance(
                        amount = 1000,
                        paymentMethodId = AccountPaymentMethodId(value = PAYMENT_METHOD_ID),
                    ),
                ),
                error = null,
            )
        }
    }

    @Test
    fun `deve retornar os valores quando a bill for totalmente paga em cartao`() {
        val bill = ListFeeRequestFixture.bill(
            id = "456",
            methods = arrayOf(
                ListFeeRequestFixture.cc(paymentMethodId = PAYMENT_METHOD_ID_2, quantity = 1),
            ),
        )
        val request = ListFeeRequestFixture.create(bills = arrayOf(bill))

        mockCalculateInstallment(netAmount = 1000, installments = 1, amount = 1010, total = 1010)

        val response = service.listFees(request)

        response.isRight() shouldBe true

        response.map {
            it.summary shouldBe FeeResultSummary(
                hasInstallmentQuantityDivergence = false,
                creditCardSummary = listOf(
                    FeePaymentCreditCardInstallment(
                        amount = 1010,
                        total = 1010,
                        quantity = 1,
                        fee = 1.0,
                        feeAmount = 0,
                    ),
                ),
            )
            it.fees.size shouldBe 1
            it.fees[0] shouldBe BillFees(
                billId = "456",
                amount = 1000,
                calculationId = null,
                payments = listOf(
                    FeePaymentMethodCreditCard(
                        amount = 1000,
                        paymentMethodId = AccountPaymentMethodId(value = PAYMENT_METHOD_ID_2),
                        installments = listOf(
                            FeePaymentCreditCardInstallment(
                                amount = 1010,
                                total = 1010,
                                quantity = 1,
                                fee = 1.0,
                                feeAmount = 0,
                            ),
                        ),
                    ),
                ),
                error = null,
            )
        }
    }

    @Test
    fun `deve retornar os valores quando uma bill for paga com saldo e cartão`() {
        val bill = ListFeeRequestFixture.bill(
            id = "456",
            methods = arrayOf(
                ListFeeRequestFixture.cc(paymentMethodId = PAYMENT_METHOD_ID_2, quantity = 1, value = 800),
                ListFeeRequestFixture.balance(value = 200),
            ),
        )
        val request = ListFeeRequestFixture.create(bills = arrayOf(bill))

        mockCalculateInstallment(netAmount = 800, installments = 1, amount = 808, total = 808)

        val response = service.listFees(request)

        response.isRight() shouldBe true

        response.map {
            it.summary shouldBe FeeResultSummary(
                hasInstallmentQuantityDivergence = false,
                creditCardSummary = listOf(
                    FeePaymentCreditCardInstallment(
                        amount = 808,
                        total = 808,
                        quantity = 1,
                        fee = 1.0,
                        feeAmount = 0,
                    ),
                ),
            )
            it.fees.size shouldBe 1
            with(it.fees[0]) {
                billId shouldBe "456"
                amount shouldBe 1000
                calculationId shouldBe null
                payments.shouldContainExactlyInAnyOrder(
                    listOf(
                        FeePaymentMethodCreditCard(
                            amount = 800,
                            paymentMethodId = AccountPaymentMethodId(value = PAYMENT_METHOD_ID_2),
                            installments = listOf(
                                FeePaymentCreditCardInstallment(
                                    amount = 808,
                                    total = 808,
                                    quantity = 1,
                                    fee = 1.0,
                                    feeAmount = 0,
                                ),
                            ),
                        ),
                        FeePaymentMethodBalance(
                            amount = 200,
                            paymentMethodId = AccountPaymentMethodId(value = PAYMENT_METHOD_ID),
                        ),
                    ),
                )
                error shouldBe null
            }
        }
    }

    @Test
    fun `deve retornar os valores corretamente ao receber vários boletos`() {
        val bill = ListFeeRequestFixture.bill(
            id = "123",
            methods = arrayOf(
                ListFeeRequestFixture.balance(value = 1000),
            ),
        )
        val bill2 = ListFeeRequestFixture.bill(
            id = "456",
            methods = arrayOf(
                ListFeeRequestFixture.cc(paymentMethodId = PAYMENT_METHOD_ID_2, quantity = 1, value = 900),
            ),
        )
        val request = ListFeeRequestFixture.create(bills = arrayOf(bill, bill2))

        mockCalculateInstallment(netAmount = 900, installments = 1, amount = 909, total = 909)

        val response = service.listFees(request)

        response.isRight() shouldBe true

        response.map {
            it.summary shouldBe FeeResultSummary(
                hasInstallmentQuantityDivergence = false,
                creditCardSummary = listOf(
                    FeePaymentCreditCardInstallment(
                        amount = 909,
                        total = 909,
                        quantity = 1,
                        fee = 1.0,
                        feeAmount = 0,
                    ),
                ),
            )

            it.fees.size shouldBe 2
            with(it.fees[0]) {
                billId shouldBe "123"
                amount shouldBe 1000
                calculationId shouldBe null
                payments shouldBe (
                    listOf(
                        FeePaymentMethodBalance(
                            amount = 1000,
                            paymentMethodId = AccountPaymentMethodId(value = PAYMENT_METHOD_ID),
                        ),
                    )
                    )
                error shouldBe null
            }
            with(it.fees[1]) {
                billId shouldBe "456"
                amount shouldBe 900
                calculationId shouldBe null
                payments shouldBe
                    listOf(
                        FeePaymentMethodCreditCard(
                            amount = 900,
                            paymentMethodId = AccountPaymentMethodId(value = PAYMENT_METHOD_ID_2),
                            installments = listOf(
                                FeePaymentCreditCardInstallment(
                                    amount = 909,
                                    total = 909,
                                    quantity = 1,
                                    fee = 1.0,
                                    feeAmount = 0,
                                ),
                            ),
                        ),
                    )
                error shouldBe null
            }
        }
    }

    @Test
    fun `deve juntar o valor das parcelas dos boletos pagos com cartão no resumo`() {
        val bill = ListFeeRequestFixture.bill(
            id = "123",
            methods = arrayOf(
                ListFeeRequestFixture.balance(value = 1000),
            ),
        )
        val bill2 = ListFeeRequestFixture.bill(
            id = "456",
            methods = arrayOf(
                ListFeeRequestFixture.cc(paymentMethodId = PAYMENT_METHOD_ID_2, quantity = 1, value = 900),
            ),
        )
        val bill3 = ListFeeRequestFixture.bill(
            id = "789",
            methods = arrayOf(
                ListFeeRequestFixture.cc(paymentMethodId = PAYMENT_METHOD_ID_2, quantity = 1, value = 500),
            ),
        )
        val request = ListFeeRequestFixture.create(bills = arrayOf(bill3, bill, bill2))

        mockCalculateInstallment(netAmount = 900, installments = 1, amount = 909, total = 909)
        mockCalculateInstallment(netAmount = 500, installments = 1, amount = 505, total = 505)

        val response = service.listFees(request)

        response.isRight() shouldBe true

        response.map {
            it.summary shouldBe FeeResultSummary(
                hasInstallmentQuantityDivergence = false,
                creditCardSummary = listOf(
                    FeePaymentCreditCardInstallment(
                        amount = 505 + 909,
                        total = 505 + 909,
                        quantity = 1,
                        fee = 1.0,
                        feeAmount = 0,
                    ),
                ),
            )
            it.fees.size shouldBe 3
        }
    }

    @Test
    fun `deve retornar o valor correto das parcelas do boleto`() {
        val bill = ListFeeRequestFixture.bill(
            id = "123",
            methods = arrayOf(
                ListFeeRequestFixture.cc(paymentMethodId = PAYMENT_METHOD_ID_2, quantity = 2, value = 100000),
            ),
        )

        mockCalculateInstallment(netAmount = 100000, installments = 1, amount = 101000, total = 101000)
        mockCalculateInstallment(netAmount = 100000, installments = 2, amount = 50751, total = 101502)

        val request = ListFeeRequestFixture.create(
            accountId = "ACCOUNT-1bb050f6-556b-46b9-9761-bf39e1409519",
            bills = arrayOf(bill),
        )

        val response = service.listFees(request)

        response.isRight() shouldBe true

        response.map {
            it.summary shouldBe FeeResultSummary(
                hasInstallmentQuantityDivergence = false,
                creditCardSummary = listOf(
                    FeePaymentCreditCardInstallment(
                        amount = 101000,
                        total = 101000,
                        quantity = 1,
                        fee = 1.0,
                        feeAmount = 0,
                    ),
                    FeePaymentCreditCardInstallment(
                        amount = 50751,
                        total = 101502,
                        quantity = 2,
                        fee = 1.0,
                        feeAmount = 0,
                    ),
                ),
            )
        }

        verify { creditCardFeeCalculatorService.calculateInstallment(any(), any(), 1, any()) }
        verify { creditCardFeeCalculatorService.calculateInstallment(any(), any(), 2, any()) }
    }

    private fun mockCalculateInstallment(netAmount: Long, installments: Int, amount: Long, total: Long) {
        every {
            creditCardFeeCalculatorService.calculateInstallment(
                any(),
                netAmount = netAmount,
                installments,
                any(),
            )
        } returns
            FeePaymentCreditCardInstallment(
                amount = amount,
                total = total,
                quantity = installments,
                fee = 1.0,
                feeAmount = 0,
            ).right()
    }
}