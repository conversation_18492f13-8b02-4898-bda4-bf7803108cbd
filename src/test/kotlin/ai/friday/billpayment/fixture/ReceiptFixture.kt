package ai.friday.billpayment.fixture

import ai.friday.billpayment.adapters.celcoin.payerBank
import ai.friday.billpayment.adapters.celcoin.payerDocument
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPayer
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.payment.BoletoReceiptData
import ai.friday.billpayment.app.payment.INVOICE_PURPOSE
import ai.friday.billpayment.app.payment.InvoiceReceiptData
import ai.friday.billpayment.app.payment.PixReceiptData
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixKeyOwner
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.CNPJ_1
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.DOCUMENT_2
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.math.BigInteger

object ReceiptFixture {
    fun boleto() = BoletoReceiptData(
        authentication = "***********.***********.88.99.AA.BB.CC.DD.EE.FF",
        dateTime = getZonedDateTime(),
        recipient = Recipient(
            name = "receipient",
            document = CNPJ_1,
            bankAccount = null,
            pixKeyDetails = null,
        ),
        totalAmount = 1042799,
        billId = BillId("FICHA_RECEIPT"),
        walletId = WalletId(WALLET_ID),
        payer = BillPayer(document = DOCUMENT, name = "receipient", alias = null),
        dueDate = getLocalDate(),
        assignor = "receipient",
        barcode = BarCode.ofDigitable("34191090080116587030201035380003586090000311015"),
        source = ActionSource.Webapp(role = Role.OWNER),
        walletName = null,
        scheduledBy = null,
        paymentPartnerName = "BANCO 756 / AGENCIA 3038",
        transactionId = TransactionId("transactionId"),
    )

    fun invoice() = InvoiceReceiptData(
        authentication = "***********.***********.88.99.AA.BB.CC.DD.EE.FF",
        dateTime = getZonedDateTime(),
        recipient = Recipient(
            name = "recipient",
            document = DOCUMENT_2,
            alias = "alias",
            bankAccount = BankAccount(
                accountType = AccountType.PAYMENT,
                bankNo = 1L,
                routingNo = 123,
                accountNo = BigInteger("12345"),
                accountDv = "0",
                document = DOCUMENT_2,
                ispb = null,
            ),
            pixKeyDetails = null,
        ),
        totalAmount = 1042799,
        purpose = "CC",
        billId = BillId("INVOICE_RECEIPT"),
        walletId = WalletId(WALLET_ID),
        payer = BillPayer(document = DOCUMENT, name = "recipient", alias = null),
        source = ActionSource.Webapp(role = Role.OWNER),
        payeeBank = "10 - Banco XYZ",
        paymentPartnerName = payerBank,
        paymentPartnerDocument = payerDocument,
        walletName = null,
        scheduledBy = null,
        transactionId = TransactionId("transactionId"),
    )

    fun pix() = PixReceiptData(
        authentication = "***********.***********.88.99.AA.BB.CC.DD.EE.FF",
        dateTime = getZonedDateTime(),
        recipient = Recipient(
            name = "recipient",
            document = DOCUMENT_2,
            alias = "alias",
            bankAccount = null,
            pixKeyDetails = PixKeyDetails(
                key = PixKey(value = "+*************", type = PixKeyType.PHONE),
                holder = PixKeyHolder(
                    accountNo = BigInteger("23468"),
                    accountDv = "0",
                    ispb = "********",
                    institutionName = "Banco do Brasil",
                    accountType = AccountType.PAYMENT,
                    routingNo = 123,
                ),
                owner = PixKeyOwner(
                    name = "recipient",
                    document = DOCUMENT_2,
                ),
            ),
            pixQrCodeData = null,
        ),
        totalAmount = 1042799,
        purpose = INVOICE_PURPOSE,
        billId = BillId("PIX_KEY_RECEIPT"),
        walletId = WalletId(WALLET_ID),
        payer = BillPayer(document = DOCUMENT, name = "recipient", alias = null),
        source = ActionSource.Webapp(role = Role.OWNER),
        payeeFinancialInstitution = FinancialInstitution(name = "payeeBank", ispb = null, compe = 1234),
        payerFinancialInstitution = FinancialInstitution(name = "payerBank", ispb = null, compe = 4321),
        payeeRoutingNo = 1L,
        payeeAccountNo = BigInteger("12345"),
        payeeAccountDv = "1",
        payeeAccountType = AccountType.PAYMENT,
        payerBankAccount = InternalBankAccount(
            accountType = AccountType.CHECKING,
            bankNo = 0,
            routingNo = 0,
            accountNo = 0,
            accountDv = "1",
            document = "**********",
            bankAccountMode = BankAccountMode.PHYSICAL,
        ),
        walletName = null,
        scheduledBy = null,
        transactionId = TransactionId("transactionId"),
    )
}