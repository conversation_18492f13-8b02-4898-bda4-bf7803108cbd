package ai.friday.billpayment.fixture

import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.TrackableBill
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.DiscountData
import ai.friday.billpayment.app.payment.DiscountType
import ai.friday.billpayment.app.payment.FineData
import ai.friday.billpayment.app.payment.FineType
import ai.friday.billpayment.app.payment.InterestData
import ai.friday.billpayment.app.payment.InterestType
import ai.friday.billpayment.billAddedFicha
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.math.BigDecimal
import java.time.LocalDate

object TrackableBillFixture {
    fun create(
        amountCalculationModel: AmountCalculationModel = AmountCalculationModel.ANYONE,
        dueDate: LocalDate? = null,
        active: Boolean = true,
        billId: String? = null,
        isDda: Boolean = false,
        discount: DiscountData = discount(),
    ): TrackableBill {
        val newBill = billAddedFicha.copy(amountCalculationModel = amountCalculationModel)
        return TrackableBill(
            billId = if (billId != null) BillId(billId) else newBill.billId,
            created = getZonedDateTime(),
            isActive = active,
            barCode = newBill.barcode,
            addedByDda = isDda,
            billType = BillType.FICHA_COMPENSACAO,
            dueDate = dueDate ?: newBill.dueDate,
            amount = newBill.amount,
            amountCalculationModel = newBill.amountCalculationModel,
            fineData = newBill.fineData ?: FineData(),
            interestData = newBill.interestData ?: InterestData(),
            discountData = discount,
            abatement = BigDecimal.ZERO,
        )
    }

    fun discount(
        type: DiscountType = DiscountType.FREE,
        value1: BigDecimal? = BigDecimal(1),
        date1: LocalDate? = LocalDate.now(),
        value2: BigDecimal? = BigDecimal(2),
        date2: LocalDate? = LocalDate.now().minusDays(1),
        value3: BigDecimal? = BigDecimal(3),
        date3: LocalDate? = LocalDate.now().minusDays(2),
    ) = DiscountData(
        type = type,
        value1 = value1,
        date1 = date1,
        value2 = value2,
        date2 = date2,
        value3 = value3,
        date3 = date3,
    )

    fun fine(
        type: FineType = FineType.FREE,
        value: BigDecimal? = BigDecimal(1),
        date: LocalDate? = LocalDate.of(2022, 1, 1),
    ) = FineData(
        type = type,
        value = value,
        date = date,
    )

    fun interest(
        type: InterestType = InterestType.FREE,
        value: BigDecimal? = BigDecimal(1),
        date: LocalDate? = LocalDate.of(2022, 1, 1),
    ) = InterestData(
        type = type,
        value = value,
        date = date,
    )
}