package ai.friday.billpayment.fixture

import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.payment.BillValidationResponse
import ai.friday.billpayment.successConcessionariaValidationResponse

object BillValidationFixture {
    fun create(
        billType: BillType = BillType.CONCESSIONARIA,
        amount: Long = 1_000,
        amountTotal: Long = 1_000,
        fine: Long? = null,
        interest: Long? = null,
        discount: Long? = null,
        abatement: Long? = null,
    ): BillValidationResponse {
        val registerData = successConcessionariaValidationResponse.billRegisterData
        return successConcessionariaValidationResponse.copy(
            billRegisterData = registerData?.copy(
                billType = billType,
                amount = amount,
                amountTotal = amountTotal,
                fine = fine ?: 0,
                interest = interest ?: 0,
                discount = discount ?: 0,
                abatement = abatement?.toDouble(),
            ),
        )
    }
}