package ai.friday.billpayment.fixture

import ai.friday.billpayment.adapters.api.BillFeesRequestTO
import ai.friday.billpayment.adapters.api.FeePaymentMethodBalanceRequestTO
import ai.friday.billpayment.adapters.api.FeePaymentMethodCreditCardRequestTO
import ai.friday.billpayment.adapters.api.FeePaymentMethodRequestTO
import ai.friday.billpayment.adapters.api.FeesRequestTO
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.fee.BillFeesCommand
import ai.friday.billpayment.app.fee.FeePaymentMethodBalanceCommand
import ai.friday.billpayment.app.fee.FeePaymentMethodCommand
import ai.friday.billpayment.app.fee.FeePaymentMethodCreditCardCommand
import ai.friday.billpayment.app.fee.FeesCommandScheduleTo
import ai.friday.billpayment.app.fee.ListFeesCommand
import ai.friday.billpayment.app.wallet.WalletId
import java.util.UUID

object ListFeeRequestTOFixture {
    fun create(
        scheduleTO: FeesCommandScheduleTo = FeesCommandScheduleTo.TODAY,
        vararg bills: BillFeesRequestTO = arrayOf(bill()),
    ) = FeesRequestTO(
        scheduleTo = scheduleTO.name,
        bills = bills.toList(),
    )

    fun bill(methods: List<FeePaymentMethodRequestTO> = listOf(balance())) =
        BillFeesRequestTO(billId = UUID.randomUUID().toString(), methods = methods)

    fun balance() = FeePaymentMethodBalanceRequestTO(amount = 100)
    fun cc(
        paymentMethodId: String = "payment_method_id",
        amount: Long = 100,
        quantity: Int = 12,
    ) = FeePaymentMethodCreditCardRequestTO(
        paymentMethodId = paymentMethodId,
        amount = amount,
        quantity = quantity,
    )
}

object ListFeeRequestFixture {
    fun create(
        accountId: String = "123",
        walletId: String = "WALLET_ID_123",
        scheduleTO: FeesCommandScheduleTo = FeesCommandScheduleTo.TODAY,
        vararg bills: BillFeesCommand = arrayOf(bill()),
    ) = ListFeesCommand(
        accountId = AccountId(accountId),
        walletId = WalletId(walletId),
        scheduleTo = scheduleTO,
        bills = bills.toList(),
    )

    fun bill(id: String = "123", vararg methods: FeePaymentMethodCommand = arrayOf(balance())) =
        BillFeesCommand(billId = id, methods = methods.toList())

    fun balance(value: Long = 1000) = FeePaymentMethodBalanceCommand(amount = value)
    fun cc(
        paymentMethodId: String = "card_id",
        value: Long = 1000,
        quantity: Int = 12,
    ) = FeePaymentMethodCreditCardCommand(
        paymentMethodId = paymentMethodId,
        amount = value,
        quantity = quantity,
    )
}