package ai.friday.billpayment.fixture

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardBinDetails
import ai.friday.billpayment.app.account.CreditCardBrand
import ai.friday.billpayment.app.account.CreditCardExternalId
import ai.friday.billpayment.app.account.CreditCardToken
import ai.friday.billpayment.app.account.PaymentMethod
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.InternalBankAccount
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.UUID

object AccountPaymentMethodFixture {
    fun <T : PaymentMethod> create(
        id: String = UUID.randomUUID().toString(),
        accountId: String = "123",
        status: AccountPaymentMethodStatus = AccountPaymentMethodStatus.ACTIVE,
        method: T,
    ) = AccountPaymentMethod(
        id = AccountPaymentMethodId(id),
        accountId = AccountId(accountId),
        status = status,
        method = method,
        created = ZonedDateTime.of(2023, 1, 1, 10, 0, 0, 0, ZoneId.systemDefault()),
    )
}

object BalanceAccountPaymentMethodFixture {
    fun create() = InternalBankAccount(
        accountType = AccountType.PAYMENT,
        bankNo = 1,
        routingNo = 2,
        accountNo = 3,
        accountDv = "123",
        document = "123456",
        bankAccountMode = BankAccountMode.PHYSICAL,
    )
}

object CreditCardAccountPaymentMethodFixture {
    fun create(
        brand: CreditCardBrand = CreditCardBrand.AMEX,
        pan: String = "pan",
        expiryDate: String = "2023-01-01",
        securityCode: String = "security_code",
        holderName: String = "holder_name",
        token: String = "token",
        binDetails: CreditCardBinDetails? = null,
        externalId: String = "123",
        accountProvider: AccountProviderName = AccountProviderName.FRIDAY,
        mainCard: Boolean = false,
        riskLevel: RiskLevel = RiskLevel.LOW,
    ) = CreditCard(
        brand = brand,
        pan = pan,
        expiryDate = expiryDate,
        securityCode = securityCode,
        holderName = holderName,
        token = CreditCardToken(token),
        riskLevel = riskLevel,
        binDetails = binDetails ?: binDetails(),
        externalId = CreditCardExternalId(externalId, accountProvider),
        mainCard = mainCard,
    )

    fun binDetails(
        provider: String = "provider",
        cardType: String = "card_type",
        foreignCard: Boolean = false,
        corporateCard: String = "corporate_card",
        issuer: String = "issuer",
        issuerCode: String = "issuer_code",
        prepaid: String = "prepaid",
        status: String = "status",
    ) = CreditCardBinDetails(
        provider = provider,
        cardType = cardType,
        foreignCard = foreignCard,
        corporateCard = corporateCard,
        issuer = issuer,
        issuerCode = issuerCode,
        prepaid = prepaid,
        status = status,
    )
}