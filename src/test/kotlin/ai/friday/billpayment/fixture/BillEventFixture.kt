package ai.friday.billpayment.fixture

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.payment.DiscountData
import ai.friday.billpayment.app.payment.FineData
import ai.friday.billpayment.app.payment.InterestData
import ai.friday.billpayment.billAddedFicha
import java.math.BigDecimal
import java.time.LocalDate

object BillEventFixture {
    fun fichaAdded(
        dueDate: LocalDate = LocalDate.now().plusDays(1),
        discount: DiscountData? = TrackableBillFixture.discount(),
        fine: FineData? = TrackableBillFixture.fine(),
        interest: InterestData? = TrackableBillFixture.interest(),
        abatement: Int? = 0,
    ) = billAddedFicha.copy(
        dueDate = dueDate,
        discountData = discount,
        fineData = fine,
        interestData = interest,
        actionSource = ActionSource.DDA(AccountId("123")),
        abatement = if (abatement == null) null else BigDecimal(abatement),
    )
}