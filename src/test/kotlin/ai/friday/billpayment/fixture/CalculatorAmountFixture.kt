package ai.friday.billpayment.fixture

import ai.friday.billpayment.app.bill.CalculatedAmount
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import java.time.LocalDate

object CalculatorAmountFixture {
    fun create(
        originalAmount: Long = 1_000,
        totalAmount: Long = 1_000,
        discount: Long? = null,
        interest: Long? = null,
        fine: Long? = null,
        abatement: Long? = null,
        validUntil: LocalDate? = getLocalDate(),
    ) = CalculatedAmount(
        originalAmount = originalAmount,
        totalAmount = totalAmount,
        discount = discount ?: 0,
        interest = interest ?: 0,
        fine = fine ?: 0,
        abatement = abatement ?: 0,
        validUntil = validUntil,
    )
}