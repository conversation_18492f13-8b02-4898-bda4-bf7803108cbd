package ai.friday.billpayment.fixture

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.utilityaccount.Utility
import ai.friday.billpayment.app.utilityaccount.UtilityAccount
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionDetails
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionStatus
import ai.friday.billpayment.app.utilityaccount.UtilityAccountId
import ai.friday.billpayment.app.utilityaccount.UtilityConnectionMethod
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime

object UtilityAccountFixture {
    fun create(
        id: UtilityAccountId = UtilityAccountId(),
        status: UtilityAccountConnectionStatus = UtilityAccountConnectionStatus.PENDING,
        accountId: AccountId = AccountId(ACCOUNT_ID),
        walletId: WalletId = WalletId(ACCOUNT_ID),
        accountEmail: String = "$<EMAIL>",
        attempts: Int = 0,
        utility: Utility = Utility.CLARO_MOBILE,
        connectionDetails: UtilityAccountConnectionDetails = UtilityAccountConnectionDetails.create(utility, mapOf("login" to "test", "password" to "1234")),
        connectionMethod: UtilityConnectionMethod = UtilityConnectionMethod.SCRAPING,
    ): UtilityAccount {
        return UtilityAccount(
            id = id,
            status = status,
            walletId = walletId,
            accountEmail = accountEmail,
            attempts = attempts,
            utility = utility,
            createdAt = getZonedDateTime(),
            connectionMethod = connectionMethod,
            connectionDetails = connectionDetails,
            addedBy = accountId,
        )
    }
}