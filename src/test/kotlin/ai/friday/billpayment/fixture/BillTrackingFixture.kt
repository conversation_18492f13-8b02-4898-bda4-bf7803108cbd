package ai.friday.billpayment.fixture

import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.FichaCompensacaoAdded
import ai.friday.billpayment.app.bill.TrackableBill
import ai.friday.billpayment.app.bill.tracking.BillTrackingPropertiesBinder
import ai.friday.billpayment.app.payment.DiscountData
import ai.friday.billpayment.app.payment.FineData
import ai.friday.billpayment.app.payment.InterestData
import ai.friday.billpayment.billAddedFicha
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.math.BigDecimal
import java.util.UUID

object BillTrackingFixture {
    fun trackableBill(
        id: UUID? = null,
        fichaCompensacaoAdded: FichaCompensacaoAdded = billAddedFicha,
        active: Boolean = true,
    ) = TrackableBill(
        billId = if (id == null) fichaCompensacaoAdded.billId else BillId(id.toString()),
        created = getZonedDateTime(),
        isActive = active,
        barCode = fichaCompensacaoAdded.barcode,
        addedByDda = fichaCompensacaoAdded.actionSource is ActionSource.DDA,
        billType = BillType.FICHA_COMPENSACAO,
        dueDate = fichaCompensacaoAdded.dueDate,
        amount = fichaCompensacaoAdded.amount,
        amountCalculationModel = fichaCompensacaoAdded.amountCalculationModel,
        fineData = fichaCompensacaoAdded.fineData ?: FineData(),
        interestData = fichaCompensacaoAdded.interestData ?: InterestData(),
        discountData = fichaCompensacaoAdded.discountData ?: DiscountData(),
        abatement = BigDecimal.ZERO,
    )

    fun properties(calculatePartitions: Int = 10, queryPartitions: Int = 5) = BillTrackingPropertiesBinder(
        calculate = BillTrackingPropertiesBinder.BillTrackingCalculateProperties(
            calculatePartitions,
            "queue-calculate",
        ),
        query = BillTrackingPropertiesBinder.BillTrackingQueryProperties(queryPartitions, "queue-query"),
    )
}