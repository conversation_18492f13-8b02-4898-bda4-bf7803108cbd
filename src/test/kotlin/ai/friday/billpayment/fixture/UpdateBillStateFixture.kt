package ai.friday.billpayment.fixture

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.ApproveUpdateBillRequest
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.DenyUpdateBillRequest
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId
import io.mockk.mockk

object UpdateBillStateFixture {
    fun approve(
        billID: String = "123",
        walletID: String = "123",
        member: Member? = null,
        alwaysApproveSender: Boolean = false,
        actionSource: ActionSource.Api = ActionSource.Api(AccountId("123")),
    ) = ApproveUpdateBillRequest(
        billId = BillId(billID),
        walletId = WalletId(walletID),
        member = member ?: mockk(relaxed = true),
        alwaysAllowSender = alwaysApproveSender,
        actionSource = actionSource,
    )

    fun deny(
        billID: String = "123",
        walletID: String = "123",
        member: Member? = null,
        actionSource: ActionSource.Api = ActionSource.Api(AccountId("123")),
    ) = DenyUpdateBillRequest(
        billId = BillId(billID),
        walletId = WalletId(walletID),
        member = member ?: mockk(relaxed = true),
        actionSource = actionSource,
    )
}