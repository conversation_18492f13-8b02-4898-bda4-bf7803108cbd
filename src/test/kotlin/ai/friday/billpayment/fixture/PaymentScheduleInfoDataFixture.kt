package ai.friday.billpayment.fixture

import ai.friday.billpayment.app.bill.BillPaymentScheduledBalanceInfo
import ai.friday.billpayment.app.bill.BillPaymentScheduledCreditCardInfo
import ai.friday.billpayment.app.bill.BillPaymentScheduledExternalPayment

object PaymentScheduleInfoDataFixture {
    fun balance() = BillPaymentScheduledBalanceInfo(
        paymentMethodId = "leo",
        amount = 3464,
        calculationId = null,
    )

    fun cc() = BillPaymentScheduledCreditCardInfo(
        paymentMethodId = "iuvaret",
        netAmount = 2440,
        feeAmount = 4775,
        installments = 9966,
        fee = 6.7,
        calculationId = null,
    )

    fun external() = BillPaymentScheduledExternalPayment(providerName = "PIC_PAY")
}