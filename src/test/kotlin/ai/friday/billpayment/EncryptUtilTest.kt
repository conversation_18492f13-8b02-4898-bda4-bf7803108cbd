package ai.friday.billpayment

import io.kotest.matchers.shouldBe
import java.util.concurrent.atomic.AtomicInteger
import org.junit.jupiter.api.Test

class EncryptUtilTest {
    @Test
    fun `generate deterministic secret key`() {
        val masterKey = "masterKey"
        val deterministicValue = "123"

        // same deterministic value should generate the same key
        generateSequence {
            EncryptUtil.generateDeterministicSecretKey(masterKey, deterministicValue)
        }
            .take(3)
            .toSet().size shouldBe 1

        val i = AtomicInteger()

        // different deterministic values should generate different keys
        generateSequence {
            EncryptUtil.generateDeterministicSecretKey(masterKey, i.incrementAndGet().toString())
        }
            .take(3)
            .toSet().size shouldBe 3
    }

    @Test
    fun `should encrypt and decrypt token`() {
        val encrypted = EncryptUtil.encrypt(masterKey = "masterKey", token = "friday+motorola", deterministicId = "123")

        val decrypted = EncryptUtil.decrypt(masterKey = "masterKey", encryptedToken = encrypted, deterministicId = "123")

        decrypted shouldBe "friday+motorola"
    }

    /*
    // Average: 24.993333333333332
    // Max: 94
    // Min: 24

    @Test
    fun `should encrypt and decrypt token with different secret key`() {
        val elapsedTimes = mutableListOf<Long>()

        for (i in 1..300) {
            measureTimeInMillis {
                `should encrypt and decrypt token`()
            }.also { elapsedTimes.add(it.elapsed) }
        }

        val average = elapsedTimes.average()
        val max = elapsedTimes.max()
        val min = elapsedTimes.min()

        println("Average: $average")
        println("Max: $max")
        println("Min: $min")
    }
    */
}