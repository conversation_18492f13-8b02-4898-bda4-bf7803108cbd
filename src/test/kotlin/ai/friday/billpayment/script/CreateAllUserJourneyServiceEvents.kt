/*
package ai.friday.billpayment.script

import ai.friday.billpayment.adapters.userpilot.UserPilotAdapter
import ai.friday.billpayment.adapters.userpilot.UserPilotConfiguration
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.journey.UserJourneyEvent
import ai.friday.billpayment.app.journey.UserJourneyService
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.mockk.every
import io.mockk.mockk
import java.net.URL
import java.time.ZonedDateTime
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
class CreateAllUserJourneyServiceEvents {

    private val hostValue: String = "https://analytex.userpilot.io"
    private val tokenValue: String = ""
    private val apiVersionValue: String = "2020-09-22"
    private val identifyPathValue: String = "/v1/identify"
    private val trackEventPathValue: String = "/v1/track"

    private val userJourneyAdapter = UserPilotAdapter(
        httpClient = RxHttpClient.create(URL(hostValue)),
        configuration = UserPilotConfiguration().apply {
            host = hostValue
            token = tokenValue
            apiVersion = apiVersionValue
            identifyPath = identifyPathValue
            trackEventPath = trackEventPathValue
        },
    )
    private val userJourneyService = UserJourneyService(
        userJourneyAdapter = userJourneyAdapter,
        featureConfiguration = mockk() {
            every { userPilotEnabled } returns true
        },
    )

    @Test
    fun `criar todos os eventos para uma instalacao nova`() {
        val accountIdValue = AccountId("USER-PILOT-EVENTS")
        val nameValue = "user pilot events"
        val emailValue = EmailAddress("<EMAIL>")
        val accountTypeValue = UserAccountType.FULL_ACCOUNT
        val createdAtValue = ZonedDateTime.now()

        val account: Account = mockk {
            every { accountId } returns accountIdValue
            every { name } returns nameValue
            every { emailAddress } returns emailValue
            every { type } returns accountTypeValue
            every { created } returns createdAtValue
        }

        userJourneyService.register(account)

        UserJourneyEvent.values().forEach {
            userJourneyService.trackEvent(accountIdValue, it)
        }

        val frontendEvents = listOf(
            "recurrent_bill_added",
            "wallet_shared",
            "checkout_completed",
            "multiple_bills_scheduled",
            "utility_connection_requested",
        )

        frontendEvents.forEach {
            userJourneyAdapter.trackEvent(accountIdValue, it)
        }
    }
}*/