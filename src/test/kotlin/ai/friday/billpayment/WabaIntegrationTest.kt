/*
package ai.friday.billpayment

import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import java.net.URL
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
class WabaIntegrationTest {

    private val httpClient = RxHttpClient.create(URL("https://graph.facebook.com/v20.0/"))

    // private val wabaId = "***************"
    private val wabaId = "***************"
    private val token = ""

    @Test
    fun test() {
        listarTemplates(wabaId, token, "pt_br")
    }

    // 'https://graph.facebook.com/v20.0/<WHATSAPP_BUSINESS_ACCOUNT_ID>?fields=id,name,message_templates,phone_numbers' \

    // "https://graph.facebook.com/LATEST-VERSION/WHATS-APP-MESSAGE-TEMPLATE-ID?access_token=USER-ACCESS-TOKEN"
    fun listarTemplates(wabaId: String, accessToken: String, language: String) {
        val uri = "$wabaId/message_templates?limit=3"

        val httpRequest = HttpRequest.GET<String>(uri).bearer(
            accessToken,
        ).contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        val call = httpClient.exchange(
            httpRequest,
            Argument.of(String::class.java),
            Argument.STRING,
        )

        try {
            val response = call.firstOrError().blockingGet()
            val body = response.body()
            println(body)
        } catch (e: HttpClientResponseException) {
            throw e
        }
    }
}*/