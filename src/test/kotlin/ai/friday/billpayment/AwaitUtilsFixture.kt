package ai.friday.billpayment

import java.time.Duration
import org.testcontainers.shaded.org.awaitility.Awaitility.await

private val TIMEOUT = Duration.ofSeconds(10)
private val INTERVAL = Duration.ofMillis(100)

fun awaitUntilAssertedWithPollingAtMost(
    timeout: Duration = TIMEOUT,
    interval: Duration = INTERVAL,
    block: () -> Unit,
) {
    await("await async code").atMost(timeout).pollInterval(interval).untilAsserted {
        block()
    }
}