/*
package ai.friday.billpayment.integration

import ai.friday.billpayment.adapters.firebase.FirebaseAdapter
import ai.friday.billpayment.app.account.AccountId
import io.micronaut.rxjava2.http.client.RxHttpClient
import java.net.URL
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
class FirebaseAdapterIntegrationTest() {

    private val firebaseAdapter: FirebaseAdapter =
        FirebaseAdapter(RxHttpClient.create(URL("https://www.google-analytics.com")))

    @BeforeEach
    fun init() {
        firebaseAdapter.measurementId = "G-R6VDVTMRXD"
    }

    @Test
    fun `should send event to Firebase`() {
        firebaseAdapter.publishEvent(
            AccountId(ACCOUNT_ID),
            "event.test.marlon",
            mapOf(
                "param1" to "abc",
                "param2" to "123",
                "a" to null,
            ),
        )
    }
}*/