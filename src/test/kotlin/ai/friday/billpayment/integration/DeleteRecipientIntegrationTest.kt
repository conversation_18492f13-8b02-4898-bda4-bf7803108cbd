package ai.friday.billpayment.integration

import DynamoDBUtils
import ai.friday.billpayment.BANK_ACCOUNT_ID
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.RECIPIENT_ID
import ai.friday.billpayment.RECIPIENT_PIX_EMAIL_KEY
import ai.friday.billpayment.adapters.dynamodb.ContactDbRepository
import ai.friday.billpayment.adapters.dynamodb.ContactDynamoDAO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.createSavedRecipient
import arrow.core.getOrElse
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldNotContain
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
class DeleteRecipientIntegrationTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {

    private val wallet = WalletFixture().buildWallet()
    private val accountId = wallet.founder.accountId

    private val deleteRecipientRequest =
        HttpRequest.DELETE<Any>("/recipient/$RECIPIENT_ID").onWallet(wallet)
    private val deleteBankAccountRequest =
        HttpRequest.DELETE<Any>("/recipient/$RECIPIENT_ID/bankAccount/$BANK_ACCOUNT_ID")
            .onWallet(wallet)
    private val deletePixKeyRequest =
        HttpRequest.DELETE<Any>("/recipient/$RECIPIENT_ID/pixKey/$RECIPIENT_PIX_EMAIL_KEY")
            .onWallet(wallet)

    private val enhancedClient = DynamoDBUtils.getDynamoDB()

    private val recipientDbRepository = ContactDbRepository(ContactDynamoDAO(enhancedClient))

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val savedRecipient = createSavedRecipient(accountId = accountId)

    @MockBean(WalletRepository::class)
    fun walletRepository(): WalletRepository = walletRepository
    private val walletRepository: WalletRepository = mockk() {
        every {
            findWalletOrNull(wallet.id)
        } returns wallet
    }

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
    }

    @Test
    fun `should return accepted when recipient exists`() {
        recipientDbRepository.save(recipient = savedRecipient)
        val response = client.toBlocking().exchange(deleteRecipientRequest, Void::class.java)
        response.status shouldBe HttpStatus.ACCEPTED
    }

    @Test
    fun `should return accepted when bank account is deleted`() {
        recipientDbRepository.save(recipient = savedRecipient)

        val response = client.toBlocking().exchange(deleteBankAccountRequest, Void::class.java)

        response.status shouldBe HttpStatus.ACCEPTED
        val recipients = recipientDbRepository.findRecipientByAccountIDAndDocument(accountId, savedRecipient.document)
        recipients.getOrElse { throw NoStackTraceException("teste") }.bankAccounts shouldNotContain savedRecipient.bankAccounts[0]
    }

    @Test
    fun `should return not found when bank account does not exist but contact does`() {
        val request = HttpRequest.DELETE<Any>("/recipient/id/$RECIPIENT_ID/bankAccounts/id/ID-NOT-FOUND")
            .onWallet(wallet)
        recipientDbRepository.save(recipient = savedRecipient)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Void::class.java)
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        val recipients = recipientDbRepository.findRecipientByAccountIDAndDocument(accountId, savedRecipient.document)
            .getOrElse { throw NoStackTraceException("teste") }
        recipients.bankAccounts shouldContainExactlyInAnyOrder savedRecipient.bankAccounts
        recipients.pixKeys shouldContainExactlyInAnyOrder savedRecipient.pixKeys
    }

    @Test
    fun `should return not found when recipient and pix key do not exist`() {
        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(deletePixKeyRequest, Void::class.java)
        }
        thrown.status shouldBe HttpStatus.NOT_FOUND
    }

    @Test
    fun `should return accepted when pix key is deleted`() {
        recipientDbRepository.save(recipient = savedRecipient)

        val response = client.toBlocking().exchange(deletePixKeyRequest, Void::class.java)

        response.status shouldBe HttpStatus.ACCEPTED
        val recipients = recipientDbRepository.findRecipientByAccountIDAndDocument(accountId, savedRecipient.document)
        recipients.getOrElse { throw NoStackTraceException("teste") }.pixKeys shouldContainExactly listOf(
            savedRecipient.pixKeys[1],
        )
    }

    @Test
    fun `should return not found when pix key does not exist but recipient does`() {
        val request = HttpRequest.DELETE<Any>("/recipient/$RECIPIENT_ID/pixKey/<EMAIL>")
            .onWallet(wallet)
        recipientDbRepository.save(recipient = savedRecipient)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Void::class.java)
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        val recipients = recipientDbRepository.findRecipientByAccountIDAndDocument(accountId, savedRecipient.document)
            .getOrElse { throw NoStackTraceException("teste") }
        recipients.bankAccounts shouldContainExactlyInAnyOrder savedRecipient.bankAccounts
        recipients.pixKeys shouldContainExactlyInAnyOrder savedRecipient.pixKeys
    }
}