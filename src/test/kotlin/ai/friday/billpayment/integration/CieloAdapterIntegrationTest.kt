/*
package ai.friday.billpayment.integration

import ai.friday.billpayment.adapters.cielo.CieloAdapter
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardBrand
import ai.friday.billpayment.app.account.CreditCardToken
import ai.friday.billpayment.app.account.RiskLevel
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import java.util.UUID
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@MicronautTest(environments = [FRIDAY_ENV])
@Disabled
class CieloAdapterIntegrationTest(private val cieloAdapter: CieloAdapter) {

    @Test
    fun `should authorize and capture`() {
        val orderId = "TEST_${UUID.randomUUID()}"
        println(orderId)
        val creditCard =
            CreditCard(
                CreditCardBrand.MASTER,
                "****************",
                "01/2022",
                "000",
                binDetails = null,
                riskLevel = RiskLevel.LOW,
            )
        val auth = cieloAdapter.authorizeAndCapture(AccountId(ACCOUNT_ID), orderId, 1, creditCard, "test-123")
        println(auth)
        println(cieloAdapter.checkStatus(orderId))
    }

    @Test
    fun `test authorize with tokenization`() {
        val creditCard =
            CreditCard(
                CreditCardBrand.MASTER,
                "****************",
                "01/2022",
                "000",
                "nome do portador",
                binDetails = null,
                riskLevel = RiskLevel.LOW,
            )
        val response =
            cieloAdapter.tokenize(creditCard.pan, creditCard.expiryDate, creditCard.brand, creditCard.holderName!!)
        val newCreditCard = CreditCard(
            brand = creditCard.brand,
            pan = creditCard.pan,
            expiryDate = creditCard.expiryDate,
            securityCode = creditCard.securityCode,
            holderName = creditCard.holderName,
            token = response.getOrNull()!!,
            binDetails = null,
            riskLevel = RiskLevel.LOW,
        )
        println(cieloAdapter.authorizeAndCapture(AccountId(ACCOUNT_ID), "***********", 15700, newCreditCard, "TESTE"))
    }

    @Test
    fun `should tokenize credit card`() {
        val creditCard =
            CreditCard(
                CreditCardBrand.MASTER,
                "****************",
                "01/2022",
                "000",
                "nome do portador",
                binDetails = null,
                riskLevel = RiskLevel.LOW,
            )
        val response =
            cieloAdapter.tokenize(creditCard.pan, creditCard.expiryDate, creditCard.brand, creditCard.holderName!!)

        response.isRight() shouldBe true
        response.map {
            println(response)
        }
    }

    @Test
    fun `should return card details on valid bin`() {
        val bin = "001040"
        val response = cieloAdapter.retrieveBinDetails(bin)

        response shouldNotBe null
        println(response)
    }

    @Test
    fun `should zero auth credit card`() {
        val creditCard = CreditCard(
            CreditCardBrand.MASTER,
            "****************",
            "01/2022",
            "000",
            "nome do portador",
            CreditCardToken("6c30cc15-8b70-49ec-92bc-45e5d169660f"),
            binDetails = null,
            riskLevel = RiskLevel.LOW,
        )

        val response = cieloAdapter.validate(creditCard.token!!, "000")

        response shouldBe true
    }
}*/