/*
package ai.friday.billpayment.integration

import ai.friday.billpayment.accountRegisterDataMissingAcceptedAt
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AgreementFilesService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import java.io.File
import java.io.FileOutputStream
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@MicronautTest(environments = [FRIDAY_ENV])
@Disabled
class AgreementFilesIntegrationTest(
    private val agreementFilesService: AgreementFilesService,
) {

    @Test
    fun `generate residency file`() {
        val file = File("target/register/declaration_of_residency.pdf")
        file.parentFile.mkdirs()

        val accountRegisterData = accountRegisterDataMissingAcceptedAt
            .copy(
                agreementData = accountRegisterDataMissingAcceptedAt.agreementData!!
                    .copy(
                        acceptedAt = getZonedDateTime(),
                    ),
            )

        agreementFilesService.createDeclarationOfResidency(
            stream = FileOutputStream(file),
            accountRegisterData = accountRegisterData,
            clientIP = "***********",
        )
    }
}*/