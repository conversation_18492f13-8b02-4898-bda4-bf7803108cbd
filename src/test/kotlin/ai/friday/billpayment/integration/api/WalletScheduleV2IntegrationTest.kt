package ai.friday.billpayment.integration.api

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.api.BillsToScheduleTO
import ai.friday.billpayment.adapters.api.BillsToScheduleWithMethodsDetailTO
import ai.friday.billpayment.adapters.api.ScheduleResultTO
import ai.friday.billpayment.adapters.api.ScheduleWithMethodsDetailTO
import ai.friday.billpayment.adapters.api.ScheduledBillsTO
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardBrand
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.FichaCompensacaoAdded
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.bill.billLockProvider
import ai.friday.billpayment.app.cashIn.CreditCardsInstallmentsFeeConfiguration
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.payment.ScheduleTo
import ai.friday.billpayment.app.security.Fingerprint
import ai.friday.morning.date.withGivenDateTime
import arrow.core.getOrElse
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.date.shouldHaveSameDayAs
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import jakarta.inject.Named
import java.time.ZonedDateTime
import java.util.Base64
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

@MicronautTest(environments = [FRIDAY_ENV])
open class WalletScheduleV2IntegrationTest(
    embeddedServer: EmbeddedServer,
    dynamoDB: AmazonDynamoDB,
    @Named(billLockProvider) lockProvider: InternalLock,
    updateBillService: UpdateBillService,
) : WalletScheduleIntegrationTest(
    embeddedServer = embeddedServer,
    dynamoDB = dynamoDB,
    lockProvider = lockProvider,
    updateBillService = updateBillService,
) {

    open fun calculationId(): String? = null

    @MockBean(CreditCardsInstallmentsFeeConfiguration::class)
    fun creditCardsInstallmentsFeeConfiguration() = creditCardsInstallmentsFeeConfiguration
    private val creditCardsInstallmentsFeeConfiguration: CreditCardsInstallmentsFeeConfiguration = mockk(relaxUnitFun = true) {
        every { fees } returns (1..12).associateWith { 4.0 }
    }

    private val creditCard = CreditCard(
        brand = CreditCardBrand.VISA,
        pan = "****************",
        expiryDate = "10/2020",
        binDetails = null,
        riskLevel = RiskLevel.LOW,
    )

    override fun getScheduleResponse(
        billsAdded: List<BillEvent>,
        account: Account,
        scheduleTo: ScheduleTo,
    ): ScheduledBillsTO {
        return getScheduleResponse(
            billsAdded = billsAdded,
            account = account,
            scheduleTo = scheduleTo,
            type = PaymentMethodType.BALANCE,
            paymentMethodId = bankAccount.id,
        )
    }

    private fun getScheduleResponse(
        billsAdded: List<BillEvent>,
        account: Account,
        billsToSchedule: BillsToScheduleTO,
        fingerprint: Fingerprint? = null,
    ): ScheduledBillsTO {
        billsAdded.forEach { billEventRepository.save(it) }

        val request = buildScheduleBillsHttpRequest(billsToSchedule, account, fingerprint)
        val result = client.toBlocking().exchange(request, Argument.of(ScheduledBillsTO::class.java))

        result.status shouldBe HttpStatus.OK

        return result.getBody(ScheduledBillsTO::class.java).get()
    }

    private fun getScheduleResponse(
        billsAdded: List<BillEvent>,
        account: Account,
        scheduleTo: ScheduleTo,
        type: PaymentMethodType,
        paymentMethodId: AccountPaymentMethodId,
        fingerprint: Fingerprint? = null,
    ): ScheduledBillsTO {
        billsAdded.forEach { billEventRepository.save(it) }

        val billsToSchedule =
            BillsToScheduleTO(
                bills = billsAdded.map {
                    val amount = if (it is BillAdded) {
                        it.amountTotal
                    } else if (it is FichaCompensacaoAdded) {
                        it.amountTotal
                    } else {
                        0
                    }

                    val method = when (type) {
                        PaymentMethodType.CREDIT_CARD -> ScheduleWithMethodsDetailTO(
                            type = type.name,
                            paymentMethodId = paymentMethodId.value,
                            amount = amount,
                            installments = 1,
                            calculationId = calculationId(),
                        )

                        PaymentMethodType.BALANCE -> ScheduleWithMethodsDetailTO(
                            type = type.name,
                            paymentMethodId = paymentMethodId.value,
                            amount = amount,
                            calculationId = null,
                        )

                        PaymentMethodType.EXTERNAL -> TODO("NAO PODE AGENDAR COM EXTERNAL PELO CONTROLLER")
                    }
                    BillsToScheduleWithMethodsDetailTO(
                        it.billId.value,
                        listOf(method),
                    )
                },
                scheduleTo = scheduleTo,
            )
        val request = buildScheduleBillsHttpRequest(billsToSchedule, account, fingerprint)
        val result = client.toBlocking().exchange(request, Argument.of(ScheduledBillsTO::class.java))

        result.status shouldBe HttpStatus.OK

        return result.getBody(ScheduledBillsTO::class.java).get()
    }

    @ParameterizedTest
    @MethodSource("allCreditCardSchedulableBills")
    fun `deve agendar a conta com cartao de credito`(
        billEvent: BillEvent,
        now: ZonedDateTime,
        expectedFingerprint: Fingerprint?,
    ) {
        val creditCardPaymentMethodId = addCreditCard(walletFixture.founder.accountId, creditCard)

        withGivenDateTime(now) {
            val billsAdded = listOf(billEvent)

            val responseBody =
                getScheduleResponse(
                    billsAdded = billsAdded,
                    account = walletFixture.founderAccount,
                    scheduleTo = ScheduleTo.TODAY,
                    type = PaymentMethodType.CREDIT_CARD,
                    paymentMethodId = creditCardPaymentMethodId,
                    fingerprint = expectedFingerprint,
                )

            responseBody.billList shouldContainExactlyInAnyOrder billsAdded.map {
                ScheduleResultTO(
                    billId = it.billId.value,
                    scheduled = true,
                )
            }

            val bill = billEventRepository.getBillById(billsAdded.single().billId)
                .getOrElse { throw NoStackTraceException("teste") }

            with(bill.schedule!!) {
                date shouldHaveSameDayAs now.toLocalDate()
                fingerprint shouldBe expectedFingerprint
                batchSchedulingId.shouldNotBeNull()
            }
        }
    }

    @ParameterizedTest
    @MethodSource("allCreditCardSchedulableBills")
    fun `deve agendar a conta com cartao de credito e saldo`(
        billEvent: BillEvent,
        now: ZonedDateTime,
        expectedFingerprint: Fingerprint?,
    ) {
        val creditCardPaymentMethodId = addCreditCard(walletFixture.founder.accountId, creditCard)

        withGivenDateTime(now) {
            val billsAdded = listOf(billEvent)

            val billsToSchedule = BillsToScheduleTO(
                bills = billsAdded.map {
                    val amount = if (it is BillAdded) {
                        it.amountTotal
                    } else if (it is FichaCompensacaoAdded) {
                        it.amountTotal
                    } else {
                        0
                    }

                    val methods = listOf(
                        ScheduleWithMethodsDetailTO(
                            type = PaymentMethodType.CREDIT_CARD.name,
                            paymentMethodId = creditCardPaymentMethodId.value,
                            amount = amount - 2L,
                            installments = 1,
                            calculationId = calculationId(),
                        ),
                        ScheduleWithMethodsDetailTO(
                            type = PaymentMethodType.BALANCE.name,
                            paymentMethodId = bankAccount.id.value,
                            amount = 2L,
                            calculationId = null,
                        ),
                    )

                    BillsToScheduleWithMethodsDetailTO(
                        it.billId.value,
                        methods,
                    )
                },
                scheduleTo = ScheduleTo.TODAY,
            )

            val responseBody =
                getScheduleResponse(
                    billsAdded = billsAdded,
                    billsToSchedule = billsToSchedule,
                    account = walletFixture.founderAccount,
                    fingerprint = expectedFingerprint,
                )

            responseBody.billList shouldContainExactlyInAnyOrder billsAdded.map {
                ScheduleResultTO(
                    billId = it.billId.value,
                    scheduled = true,
                )
            }

            val bill = billEventRepository.getBillById(billsAdded.single().billId)
                .getOrElse { throw NoStackTraceException("teste") }
            bill.schedule!!.date shouldHaveSameDayAs now.toLocalDate()
        }
    }

    @ParameterizedTest
    @MethodSource("allCreditCardSchedulableBills")
    fun `uma conta agendavel com cartão não pode ser agendada se a data do agendamento não for o mesmo dia`(
        billEvent: BillEvent,
        now: ZonedDateTime,
        expectedFingerprint: Fingerprint?,
    ) {
        val creditCardPaymentMethodId = addCreditCard(walletFixture.founder.accountId, creditCard)

        withGivenDateTime(now.plusMinutes(2)) {
            val billsAdded = listOf(billEvent)

            val billsToSchedule = BillsToScheduleTO(
                bills = billsAdded.map {
                    val amount = if (it is BillAdded) {
                        it.amountTotal
                    } else if (it is FichaCompensacaoAdded) {
                        it.amountTotal
                    } else {
                        0
                    }

                    val methods = listOf(
                        ScheduleWithMethodsDetailTO(
                            type = PaymentMethodType.CREDIT_CARD.name,
                            paymentMethodId = creditCardPaymentMethodId.value,
                            amount = amount - 2L,
                            installments = 1,
                            calculationId = calculationId(),
                        ),
                        ScheduleWithMethodsDetailTO(
                            type = PaymentMethodType.BALANCE.name,
                            paymentMethodId = bankAccount.id.value,
                            amount = 2L,
                            calculationId = null,
                        ),
                    )

                    BillsToScheduleWithMethodsDetailTO(
                        it.billId.value,
                        methods,
                    )
                },
                scheduleTo = ScheduleTo.TODAY,
            )

            val responseBody =
                getScheduleResponse(
                    billsAdded = billsAdded,
                    billsToSchedule = billsToSchedule,
                    account = walletFixture.founderAccount,
                    fingerprint = expectedFingerprint,
                )

            responseBody.billList shouldContainExactlyInAnyOrder billsAdded.map {
                ScheduleResultTO(
                    billId = it.billId.value,
                    scheduled = false,
                )
            }

            val bill = billEventRepository.getBillById(billsAdded.single().billId)
                .getOrElse { throw NoStackTraceException("teste") }
            bill.schedule.shouldBeNull()
        }
    }

//    @Disabled // TODO: Remover após fase inicial de habilitar cartoes novamente
    @ParameterizedTest
    @MethodSource("allCreditCardNonSchedulableBills")
    fun `nao deve agendar a conta com cartao de credito`(billEvent: BillEvent, now: ZonedDateTime) {
        val creditCardPaymentMethodId = addCreditCard(walletFixture.founder.accountId, creditCard)

        withGivenDateTime(now) {
            val billsAdded = listOf(billEvent)

            val billsToSchedule = BillsToScheduleTO(
                bills = billsAdded.map {
                    val amount = if (it is BillAdded) {
                        it.amountTotal
                    } else if (it is FichaCompensacaoAdded) {
                        it.amountTotal
                    } else {
                        0
                    }

                    val methods = listOf(
                        ScheduleWithMethodsDetailTO(
                            type = PaymentMethodType.CREDIT_CARD.name,
                            paymentMethodId = creditCardPaymentMethodId.value,
                            amount = amount,
                            installments = 1,
                            calculationId = calculationId(),
                        ),
                    )

                    BillsToScheduleWithMethodsDetailTO(
                        it.billId.value,
                        methods,
                    )
                },
                scheduleTo = ScheduleTo.TODAY,
            )

            val responseBody =
                getScheduleResponse(
                    billsAdded = billsAdded,
                    billsToSchedule = billsToSchedule,
                    account = walletFixture.founderAccount,
                )

            responseBody.billList shouldContainExactlyInAnyOrder billsAdded.map {
                ScheduleResultTO(
                    billId = it.billId.value,
                    scheduled = false,
                )
            }

            val bill = billEventRepository.getBillById(billsAdded.single().billId)
                .getOrElse { throw NoStackTraceException("teste") }
            bill.schedule.shouldBeNull()
        }
    }

//    @Disabled // TODO: Remover após fase inicial de habilitar cartoes novamente
    @ParameterizedTest
    @MethodSource("allSchedulableBills")
    fun `nao deve agendar a conta com cartao de credito e saldo se o somatorio dos valores estiver errado`(
        billEvent: BillEvent,
        now: ZonedDateTime,
    ) {
        val creditCardPaymentMethodId = addCreditCard(walletFixture.founder.accountId, creditCard)

        withGivenDateTime(now) {
            val billsAdded = listOf(billEvent)

            val billsToSchedule = BillsToScheduleTO(
                bills = billsAdded.map {
                    val amount = if (it is BillAdded) {
                        it.amountTotal
                    } else if (it is FichaCompensacaoAdded) {
                        it.amountTotal
                    } else {
                        0
                    }

                    val methods = listOf(
                        ScheduleWithMethodsDetailTO(
                            type = PaymentMethodType.CREDIT_CARD.name,
                            paymentMethodId = creditCardPaymentMethodId.value,
                            amount = amount - 2L,
                            installments = 1,
                            calculationId = calculationId(),
                        ),
                        ScheduleWithMethodsDetailTO(
                            type = PaymentMethodType.BALANCE.name,
                            paymentMethodId = bankAccount.id.value,
                            amount = 1L,
                            calculationId = null,
                        ),
                    )

                    BillsToScheduleWithMethodsDetailTO(
                        it.billId.value,
                        methods,
                    )
                },
                scheduleTo = ScheduleTo.TODAY,
            )

            val responseBody =
                getScheduleResponse(
                    billsAdded = billsAdded,
                    billsToSchedule = billsToSchedule,
                    account = walletFixture.founderAccount,
                )

            responseBody.billList shouldContainExactlyInAnyOrder billsAdded.map {
                ScheduleResultTO(
                    billId = it.billId.value,
                    scheduled = false,
                )
            }

            val bill = billEventRepository.getBillById(billsAdded.single().billId)
                .getOrElse { throw NoStackTraceException("teste") }
            val expectedDate = now.toLocalDate().plusDays(1)
            bill.schedule.shouldBeNull()
        }
    }

    private fun buildScheduleBillsHttpRequest(
        billsToScheduleTO: BillsToScheduleTO,
        account: Account,
        fingerprint: Fingerprint?,
    ): HttpRequest<BillsToScheduleTO> {
        return HttpRequest.POST("/v2/schedule", billsToScheduleTO)
            .cookie(buildCookie(account))
            .header("X-API-VERSION", "2")
            .header("X-WALLET-ID", wallet.id.value)
            .header("fingerprint", fingerprint?.toHeader() ?: "")
    }

    private fun Fingerprint.toHeader(): String {
        val params = mutableMapOf(
            "session_id" to this.sessionId,
            "app_version" to this.app.version,
            "longitude" to this.geolocation.longitude,
            "latitude" to this.geolocation.latitude,
            "device_manufacturer" to this.device.manufacturer,
            "device_model" to this.device.model,
            "installation_id" to this.device.installationId,
            "device_os" to this.os.name,
            "os_version" to this.os.version,
        )
        return Base64.getEncoder().encodeToString(getObjectMapper().writeValueAsString(params).toByteArray())
    }
}