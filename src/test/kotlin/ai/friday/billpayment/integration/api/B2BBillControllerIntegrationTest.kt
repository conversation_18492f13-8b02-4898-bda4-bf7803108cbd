package ai.friday.billpayment.integration.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.api.CreateModattaPixResponseTO
import ai.friday.billpayment.adapters.api.PixKeyRequestTO
import ai.friday.billpayment.adapters.api.RecipientBankDetailsTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.adapters.modatta.api.CreateModattaPixTO
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyDetailsResult
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixKeyOwner
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.DailyPaymentLimitType
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletStatus
import ai.friday.billpayment.app.wallet.WalletType
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.EMAIL
import ai.friday.billpayment.integration.INVALID_SHORT_DESCRIPTION
import ai.friday.billpayment.integration.INVALID_SHORT_DESCRIPTION2
import ai.friday.billpayment.integration.LONG_DESCRIPTION
import ai.friday.billpayment.integration.configureDailyLimits
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.createLockTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.integration.loadBalancePaymentMethod
import ai.friday.billpayment.paymentMethodId
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.pixPaid
import ai.friday.billpayment.withPixParticipants
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.micronaut.context.annotation.Property
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import java.math.BigInteger
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

@MicronautTest(environments = [FRIDAY_ENV])
class B2BBillControllerIntegrationTest(
    embeddedServer: EmbeddedServer,
    val dynamoDB: AmazonDynamoDB,
) {

    @field:Property(name = "lock.tableName")
    var lockTableName: String = "Shedlock"

    @MockBean(PixKeyManagement::class)
    fun pixKeyManagementFun(): PixKeyManagement = pixKeyManagement

    private val pixKeyManagement: PixKeyManagement = mockk()

    private val dynamoDAO = DynamoDbDAO(dynamoDB)
    private val dynamoClient = getDynamoDB()

    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(dynamoClient),
        refundedClient = RefundedBillDynamoDAO(dynamoClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(dynamoClient),
    )
    private val billEventDAO = BillEventDynamoDAO(dynamoClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(dynamoClient)
    private val transactionDynamo = TransactionDynamo(dynamoClient)
    private val billEventRepository = BillEventDBRepository(
        billEventDAO,
        uniqueConstraintDAO,
        allFalseFeatureConfiguration,
        transactionDynamo,
    )

    private val walletDAO = WalletDynamoDAO(dynamoClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(dynamoClient)
    private val inviteDAO = InviteDynamoDAO(dynamoClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(dynamoClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(dynamoClient)

    private val walletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = mockk(),
    )

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val lockProvider: InternalLock = mockk(relaxed = true)

    private val accountId: AccountId = AccountId(ACCOUNT_ID)

    private val bankAccountRequestTO = CreateModattaPixTO(
        amount = 10,
        description = "description",
        bankDetails = RecipientBankDetailsTO(
            accountType = AccountType.CHECKING,
            routingNo = 1,
            accountNo = BigInteger("*********"),
            accountDv = "2",
            ispb = "********",
        ),
        accountId = accountId.value,
    )

    private val pixKeyRequestTO = CreateModattaPixTO(
        amount = 10,
        description = "description",
        bankDetails = null,
        pixKey = PixKeyRequestTO(
            type = PixKeyType.CPF,
            value = "***********",
        ),
        accountId = accountId.value,
    )

    private val pixKeyDocument = DOCUMENT
    private val pixKeyDetails = PixKeyDetails(
        key = PixKey(value = pixKeyDocument, type = PixKeyType.CPF),
        holder = PixKeyHolder(
            accountNo = BigInteger("12345"),
            accountDv = "X",
            ispb = "********",
            institutionName = "banco fake",
            accountType = AccountType.CHECKING,
            routingNo = 1L,
        ),
        owner = PixKeyOwner("Ze", pixKeyDocument),
    )

    private val founder = Member(
        accountId,
        document = DOCUMENT,
        name = "Membro Fundador",
        emailAddress = EmailAddress(EMAIL),
        type = MemberType.FOUNDER,
        status = MemberStatus.ACTIVE,
        permissions = MemberPermissions(
            viewBills = BillPermission.ALL_BILLS,
            scheduleBills = BillPermission.ALL_BILLS,
            viewBalance = true,
            notification = true,
        ),
        created = getZonedDateTime(),
        updated = getZonedDateTime(),
    )

    private val primaryWalletId = WalletId(ACCOUNT_ID)

    private val primaryWallet = Wallet(
        primaryWalletId,
        "carteira principal",
        listOf(founder),
        10,
        WalletStatus.ACTIVE,
        WalletType.PRIMARY,
        paymentMethodId,
    )

    private val walletLimitsService = WalletLimitsService(
        accountService = mockk<AccountService>() {
            every {
                findAccountById(accountId)
            } returns mockk() {
                every {
                    type
                } returns UserAccountType.FULL_ACCOUNT
            }
        },
        walletRepository = walletRepository,
        billRepository = mockk(),
        paymentSchedulingService = mockk(),
        internalLock = lockProvider,
    )

    private val walletLimit: Long = 500_00

    private val accountDAO = AccountDynamoDAO(dynamoClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(dynamoClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(dynamoClient)
    private val nsuDAO = NSUDynamoDAO(dynamoClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        createLockTable(dynamoDB, lockTableName)
        loadAccountIntoDb(dynamoDB, hasCreditCard = false)

        loadBalancePaymentMethod(accountRepository, paymentMethodId = primaryWallet.paymentMethodId.value)

        walletRepository.save(primaryWallet)
        configureDailyLimits(
            walletLimitsService = walletLimitsService,
            accountId = accountId,
            walletId = primaryWalletId,
            dailyLimit = walletLimit,
            nighttimeLimit = walletLimit,
        )
        walletLimitsService.updateLimit(
            accountId = accountId,
            walletId = primaryWalletId,
            type = DailyPaymentLimitType.MONTHLY,
            amount = 10_000_00,
            source = ActionSource.System,
        )
    }

    private fun buildRequest(request: Any) =
        HttpRequest.POST("/b2b/bill/pix", request)
            .basicAuth("8da2b063-bff2-43d2-8377-07b00a458c31", "8da2b063-bff2-43d2-8377-07b00a458c31")

    @ParameterizedTest
    @CsvSource(
        "-10,description,1,********,2,********,Amount",
        "10,$LONG_DESCRIPTION,1,********,2,********,Description",
        "10,$INVALID_SHORT_DESCRIPTION,1,********,2,********,Description",
        "10,$INVALID_SHORT_DESCRIPTION2,1,********,2,********,Description",
        "10,description,-1,********,2,********,Routing Number",
        "10,description,1,-********,2,********,Account Number",
        "10,description,1,********,112,********,Account check",
        "10,description,1,********,2,********9,ispb",
    )
    fun `should return bad request on invalid pix request`(
        amount: Long,
        description: String,
        routingNo: Long,
        accountNo: Long,
        accountDv: String,
        ispb: String?,
        expectedWrongField: String,
    ) {
        val to = CreateModattaPixTO(
            amount = amount,
            description = description,
            bankDetails = RecipientBankDetailsTO(
                accountType = AccountType.CHECKING,
                routingNo = routingNo,
                accountNo = accountNo.toBigInteger(),
                accountDv = accountDv,
                ispb = if (ispb.isNullOrEmpty()) {
                    null
                } else {
                    ispb
                },
            ),
            accountId = accountId.value,
            pixKey = null,
        )

        val response = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.exchange(buildRequest(to), Unit::class.java).firstOrError().blockingGet()
        }

        response.status shouldBe HttpStatus.BAD_REQUEST
        response.response.getBody(String::class.java).get() shouldContain expectedWrongField
    }

    @Test
    fun `should return bad request with 4300 code when reached the daily limit`() {
        withPixParticipants(FinancialInstitution(name = "Banco do Brasil SA", compe = 1L, ispb = "********")) {
            val exception = Assertions.assertThrows(HttpClientResponseException::class.java) {
                client.exchange(
                    buildRequest(bankAccountRequestTO.copy(amount = walletLimit + 1)),
                    CreateModattaPixResponseTO::class.java,
                ).firstOrError()
                    .blockingGet()
            }

            exception.status shouldBe HttpStatus.BAD_REQUEST

            with(exception.response.getBody(ResponseTO::class.java).get()) {
                this.code shouldBe "4300"
            }
        }
    }

    @Test
    fun `should return bad request with 4301 code when reached the monthly limit`() {
        withGivenDateTime(getZonedDateTime().withDayOfMonth(10)) {
            billRepository.save(
                Bill.build(
                    pixAdded.copy(walletId = primaryWalletId, amount = 9_900_00, amountTotal = 9_900_00),
                    pixPaid.copy(
                        walletId = primaryWalletId,
                        created = getZonedDateTime().withDayOfMonth(5).toInstant()
                            .toEpochMilli(),
                    ),
                ),
            )
            withPixParticipants(FinancialInstitution(name = "Banco do Brasil SA", compe = 1L, ispb = "********")) {
                val exception = Assertions.assertThrows(HttpClientResponseException::class.java) {
                    client.exchange(
                        buildRequest(bankAccountRequestTO.copy(amount = 400_00)),
                        CreateModattaPixResponseTO::class.java,
                    ).firstOrError()
                        .blockingGet()
                }

                exception.status shouldBe HttpStatus.BAD_REQUEST

                with(exception.response.getBody(ResponseTO::class.java).get()) {
                    this.code shouldBe "4301"
                }
            }
        }
    }

    @Test
    fun `should return 201 on successful create pix`() {
        withPixParticipants(FinancialInstitution(name = "Banco do Brasil SA", compe = 1L, ispb = "********")) {
            val response = client.exchange(buildRequest(bankAccountRequestTO), CreateModattaPixResponseTO::class.java)
                .firstOrError()
                .blockingGet()

            response.status shouldBe HttpStatus.CREATED
            val getBillResponse = billEventRepository.getBillById(BillId(response.body()!!.billId))
            getBillResponse.isRight() shouldBe true
            getBillResponse.map {
                it.amount shouldBe bankAccountRequestTO.amount
                it.recipient!!.document shouldBe DOCUMENT
                it.status shouldBe BillStatus.ACTIVE
                it.isPaymentScheduled() shouldBe true
            }
        }
    }

    @Test
    fun `should return 201 on successful create pix by key`() {
        every {
            pixKeyManagement.findKeyDetailsCacheable(any(), any())
        } returns PixKeyDetailsResult(pixKeyDetails, "")

        val response =
            client.exchange(buildRequest(pixKeyRequestTO), CreateModattaPixResponseTO::class.java).firstOrError()
                .blockingGet()

        response.status shouldBe HttpStatus.CREATED
        val billId = BillId(response.body()!!.billId)
        val getBillResponse = billEventRepository.getBillById(billId)
        io.mockk.verify {
            pixKeyManagement.findKeyDetailsCacheable(
                PixKey(
                    pixKeyRequestTO.pixKey!!.value,
                    pixKeyRequestTO.pixKey!!.type,
                ),
                DOCUMENT,
            )
        }
        getBillResponse.isRight() shouldBe true
        getBillResponse.map {
            it.recipient?.pixKeyDetails shouldBe pixKeyDetails
        }
        with(billRepository.findBill(billId, primaryWalletId)) {
            this.recipient?.pixKeyDetails shouldBe pixKeyDetails
            this.amount shouldBe bankAccountRequestTO.amount
            this.recipient!!.document shouldBe DOCUMENT
            this.status shouldBe BillStatus.ACTIVE
            this.isPaymentScheduled() shouldBe true
        }
    }
}