package ai.friday.billpayment.integration.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.accountRegisterDataWithDocumentInfo
import ai.friday.billpayment.adapters.api.BasicAccountRegisterDataTO
import ai.friday.billpayment.adapters.api.PoliticallyExposedRequestTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.OriginalOcrAndPersonDataDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.notification.PushNotificationService
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.SecurityFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.date.withGivenDateTime
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import io.via1.communicationcentre.app.integrations.ContractPdfWriterService
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

@MicronautTest(environments = [FRIDAY_ENV])
class PutPoliticallyExposedIntegrationTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {
    val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val enhancedClient = getDynamoDB()
    private val accountRegisterDAO = AccountRegisterDynamoDAO(enhancedClient)
    private val originalOcrAndPersonDataDAO = OriginalOcrAndPersonDataDynamoDAO(enhancedClient)

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val securityFixture = SecurityFixture()

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @MockBean(ContractPdfWriterService::class)
    fun contractPdfWriterService() = contractPdfWriterService
    private val contractPdfWriterService: ContractPdfWriterService = mockk(relaxUnitFun = true)

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @MockBean(PushNotificationService::class)
    fun pushNotificationSenderService() = pushNotificationService
    private val pushNotificationService: PushNotificationService = mockk(relaxed = true)

    @MockBean(ObjectRepository::class)
    fun objectRepository() = objectRepository
    private val objectRepository: ObjectRepository = mockk {
        every { putObject(any(), any(), any()) } just runs
    }

    private val accountRegisterRepository =
        AccountRegisterDbRepository(accountRegisterDAO, originalOcrAndPersonDataDAO, objectRepository)

    private lateinit var accountId: AccountId

    private fun buildUpdatePoliticallyExposedRequest(
        cookie: Cookie,
        politicallyExposedRequestTO: PoliticallyExposedRequestTO,
    ): MutableHttpRequest<PoliticallyExposedRequestTO> {
        return HttpRequest.PUT("/basicRegister/politicallyExposed", politicallyExposedRequestTO)
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        val partialAccount = accountRepository.create(
            username = "fake_user",
            emailAddress = EmailAddress("<EMAIL>"),
            accountId = AccountId(ACCOUNT_ID_2),
            registrationType = RegistrationType.BASIC,
        )
        accountId = partialAccount.id
        accountRegisterRepository.save(accountRegisterDataWithDocumentInfo.copy(accountId = accountId))
    }

    /*
        @Disabled("Info is not important for now")
        @ParameterizedTest
        @MethodSource("getPoliticallyExposedWrong")
        fun `should return BAD_REQUEST when is politically exposed and no info passed`(
            currentTO: PoliticallyExposedRequestTO,
        ) {
            val request = buildUpdatePoliticallyExposedRequest(securityFixture.cookieMsisdnAuth, currentTO)

            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(
                        request,
                        Argument.of(BasicAccountRegisterDataTO::class.java),
                        Argument.of(ResponseTO::class.java),
                    )
            }

            thrown.status shouldBe HttpStatus.BAD_REQUEST
        }
    */

    @ParameterizedTest
    @MethodSource("getPoliticallyExposed")
    fun `should return account register when info is correct`(currentTO: PoliticallyExposedRequestTO) {
        val updatedDate = getZonedDateTime()
        val request = buildUpdatePoliticallyExposedRequest(securityFixture.cookieMsisdnAuth, currentTO)

        withGivenDateTime(updatedDate) {
            val response = client.toBlocking()
                .exchange(request, Argument.of(BasicAccountRegisterDataTO::class.java), Argument.of(String::class.java))
            response.status shouldBe HttpStatus.OK

            val politicallyExposed = response.body.get().politicallyExposed

            with(currentTO) {
                isExposed shouldBe politicallyExposed!!.isExposed
            }

            val account = accountRegisterRepository.findByAccountId(accountId)

            with(account.politicallyExposed!!) {
                selfDeclared shouldBe politicallyExposed!!.isExposed
            }
            response.body.get().lastUpdated shouldBe updatedDate.format(dateTimeFormat)
        }

        verify(exactly = 0) {
            contractPdfWriterService.createContract(any(), any(), any())
        }
    }

    @ParameterizedTest
    @MethodSource("getPoliticallyExposed")
    fun `should return BAD_REQUEST when account register when info is correct but account is not editable`(
        currentTO: PoliticallyExposedRequestTO,
    ) {
        val request = buildUpdatePoliticallyExposedRequest(securityFixture.cookieMsisdnAuth, currentTO)
        accountRepository.updatePartialAccountStatus(accountId, AccountStatus.UNDER_REVIEW)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(
                    request,
                    Argument.of(BasicAccountRegisterDataTO::class.java),
                    Argument.of(ResponseTO::class.java),
                )
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    companion object {
        @JvmStatic
        fun getPoliticallyExposed(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(PoliticallyExposedRequestTO(false)),
                Arguments.of(PoliticallyExposedRequestTO(true, "bla bla bla")),
            )
        }

        /*
                @JvmStatic
                fun getPoliticallyExposedWrong(): Stream<Arguments> {
                    return Stream.of(
                        Arguments.of(PoliticallyExposedRequestTO(true)),
                        Arguments.of(PoliticallyExposedRequestTO(false, "bla bla bla")),
                    )
                }
        */
    }
}