package ai.friday.billpayment.integration.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.accountRegisterDataMissingUpgradeAgreement
import ai.friday.billpayment.accountRegisterDataWithDocumentInfo
import ai.friday.billpayment.adapters.api.AccountRegisterDataTO
import ai.friday.billpayment.adapters.api.AgreementRequestTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.OriginalOcrAndPersonDataDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.s3.S3LinkGenerator
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.AgreementFilesService
import ai.friday.billpayment.app.account.PepQuery
import ai.friday.billpayment.app.account.PepQueryResult
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.account.UpgradeStatus
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.integrations.RegisterInstrumentationService
import ai.friday.billpayment.app.journey.UserJourneyEvent
import ai.friday.billpayment.app.journey.UserJourneyService
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.register.kyc.KycDossier
import ai.friday.billpayment.app.register.kyc.KycDossierMepQuery
import ai.friday.billpayment.app.register.kyc.KycDossierOfficialDocumentData
import ai.friday.billpayment.app.register.kyc.KycDossierTaxIdRegion
import ai.friday.billpayment.app.register.kyc.KycService
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.generateCookie
import ai.friday.billpayment.integration.loadPartialAccountIntoDb
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.string.shouldContain
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.PropertySource
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import io.via1.communicationcentre.app.ContractForm
import io.via1.communicationcentre.app.ContractSignature
import io.via1.communicationcentre.app.integrations.ContractPdfWriterService
import io.via1.communicationcentre.app.integrations.ContractWriterService
import io.via1.communicationcentre.app.integrations.NotificationSenderService
import jakarta.inject.Named
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.UUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

private const val contractFileKey = "contractKey"
private const val declarationOfResidencyKey = "residencyKey"
private const val signUpTestMobilePhone = "+*************"

@PropertySource(
    value = [
        Property(name = "accountRegister.user_files.contractPrefix", value = contractFileKey),
        Property(name = "accountRegister.user_files.declarationOfResidencyPrefix", value = declarationOfResidencyKey),
        Property(name = "token.onboarding.testMobilePhones", value = signUpTestMobilePhone),
    ],
)
@MicronautTest(environments = [FRIDAY_ENV])
class UpgradeAgreementIntegrationTest(
    embeddedServer: EmbeddedServer,
    val dynamoDB: AmazonDynamoDB,
) {

    private val enhancedClient = getDynamoDB()
    private val accountRegisterDAO = AccountRegisterDynamoDAO(enhancedClient)
    private val originalOcrAndPersonDataDAO = OriginalOcrAndPersonDataDynamoDAO(enhancedClient)

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @MockBean(ContractPdfWriterService::class)
    fun contractPdfWriterService() = contractWriterService
    private val contractWriterService: ContractWriterService = mockk(relaxUnitFun = true)
    private val clientIP = "***********"

    @MockBean(AgreementFilesService::class)
    fun agreementFilesService() = agreementFilesService
    private val agreementFilesService: AgreementFilesService =
        spyk(
            AgreementFilesService(
                contractWriterService = contractWriterService,
                handlebarTemplateCompiler = mockk(),
                pdfConverter = mockk(),
            ),
        ) {
            every {
                createDeclarationOfResidency(any(), any(), any())
            } returns Unit
        }

    @Named("email")
    @MockBean(NotificationSenderService::class)
    fun notificationSenderService() = notificationSenderService
    private val notificationSenderService: NotificationSenderService = mockk(relaxUnitFun = true)

    @MockBean(NotificationAdapter::class)
    fun notificationAdapter() = notificationAdapter
    private val notificationAdapter: NotificationAdapter = mockk(relaxUnitFun = true)

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @MockBean(ObjectRepository::class)
    fun objectRepository() = objectRepository
    private val objectRepository = mockk<ObjectRepository>(relaxUnitFun = true) {
        every {
            loadObject(any(), any())
        } returns java.io.InputStream.nullInputStream()
    }

    @MockBean(UserJourneyService::class)
    fun userJourneyService() = userJourneyService
    private val userJourneyService: UserJourneyService = mockk()

    @MockBean(S3LinkGenerator::class)
    fun getS3LinkGenerator() = s3LinkGenerator
    private val s3LinkGenerator: S3LinkGenerator = mockk()

    @MockBean(KycService::class)
    fun getKycService() = kycService
    private val kycService: KycService = mockk()
    private val kycFileStoredObject = StoredObject("", "", "kyc-report.html")

    val contactId = UUID.randomUUID().toString()

    @MockBean(CrmService::class)
    fun getCRMService() = crmService
    private val crmService: CrmService = mockk()

    @MockBean(RegisterInstrumentationService::class)
    fun registerInstrumentationService(): RegisterInstrumentationService = registerInstrumentationService
    private val registerInstrumentationService: RegisterInstrumentationService = mockk(relaxUnitFun = true)

    private val accountRegisterRepository =
        AccountRegisterDbRepository(accountRegisterDAO, originalOcrAndPersonDataDAO, objectRepository)

    private lateinit var accountId: AccountId

    private fun buildPutAgreement(cookie: Cookie, agreed: Boolean) =
        HttpRequest.PUT("/upgrade/agreement", AgreementRequestTO(agreed))
            .cookie(cookie)
            .header("X-API-VERSION", "2")
            .header("Forwarded", "by=***********;for=$clientIP;host=127.0.0.1;proto=http")

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        loadPartialAccountIntoDb(dynamoDB)
        val account = accountRepository.create(
            ACCOUNT.copy(
                type = UserAccountType.BASIC_ACCOUNT,
                upgradeStatus = UpgradeStatus.INCOMPLETE,
            ),
        )
        accountId = account.accountId
        accountRegisterRepository.save(
            accountRegisterDataMissingUpgradeAgreement.copy(
                accountId = accountId,
                openForUserReview = false,
                openedForUserReviewAt = getZonedDateTime(),
            ),
        )
        every {
            crmService.upsertContact(any<AccountRegisterData>())
        } returns mockk()

        every { userJourneyService.trackEventAsync(any(), any()) } returns Unit

        every { s3LinkGenerator.generate(any(), any(), any()) } returns "FAKE_LINK"
        every { kycService.generate(any()) } returns Pair(
            kycFileStoredObject,
            KycDossier(
                taxId = KycDossierTaxIdRegion(region = "SP-RJ"),
                sanctions = listOf(),
                globalSanctionsList = listOf(),
                pep = PepQuery(at = ZonedDateTime.now(), result = PepQueryResult.NOT_PEP),
                phones = listOf(),
                emails = listOf(), gender = null, motherName = null, fatherName = null,
                mep = KycDossierMepQuery(
                    exposureLevel = null,
                    celebrityLevel = null,
                    unpopularityLevel = null,
                ),
                officialData = KycDossierOfficialDocumentData(
                    provider = "",
                    name = "",
                    birthDate = LocalDate.of(1990, 1, 1),
                    socialName = null,
                    hasObitIndication = false,
                    deathYear = null,
                    regular = false,
                    status = "",
                ),
            ),
        ).right()
    }

    @Test
    fun `should return ok when upgrade agreement is settled`() {
        val updatedDate = getZonedDateTime().plusDays(2)

        withGivenDateTime(updatedDate) {
            val response = client.toBlocking().exchange(
                buildPutAgreement(generateCookie(accountId, Role.OWNER), true),
                AccountRegisterDataTO::class.java,
            )
            response.status shouldBe HttpStatus.OK
            response.body.get().userContract!!.hasAccepted shouldBe true
            response.body.get().openForUserReview shouldBe false
        }

        with(accountRegisterRepository.findByAccountId(accountId)) {
            agreementData shouldBe accountRegisterDataMissingUpgradeAgreement.agreementData
            upgradeAgreementData?.acceptedAt shouldBe updatedDate
            upgradeAgreementData?.userContractSignature.shouldNotBeNull()
            upgradeAgreementData?.userContractFile?.key shouldContain contractFileKey
            upgradeAgreementData?.declarationOfResidencyFile?.key shouldContain declarationOfResidencyKey
            openForUserReview shouldBe false
            openedForUserReviewAt shouldNotBe null
        }

        with(accountRepository.findById(accountId)) {
            upgradeStatus shouldBe UpgradeStatus.UNDER_REVIEW
        }

        val slot = slot<ContractSignature>()
        val formSlot = slot<ContractForm>()
        verify(exactly = 1) {
            contractWriterService.createContract(any(), capture(formSlot), capture(slot))
            agreementFilesService.createContract(any(), any(), any())
            agreementFilesService.createDeclarationOfResidency(any(), any(), any())
        }

        slot.captured.ip shouldBe clientIP
        with(accountRegisterDataWithDocumentInfo.documentInfo!!) {
            formSlot.captured.fullName shouldBe name
            formSlot.captured.birthPlace shouldBe "$birthCity, $birthState"
        }

        verify { userJourneyService.trackEventAsync(accountId, UserJourneyEvent.UpgradeRequested) }
    }
}