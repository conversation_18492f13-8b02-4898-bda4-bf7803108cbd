package ai.friday.billpayment.integration.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.accountRegisterDataWithDocumentInfo
import ai.friday.billpayment.adapters.api.AccountRegisterDataTO
import ai.friday.billpayment.adapters.api.AddressRequestTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.OriginalOcrAndPersonDataDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.UpgradeStatus
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.notification.PushNotificationService
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.generateCookie
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.date.withGivenDateTime
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
class PutAddressIntegrationTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {

    val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val enhancedClient = getDynamoDB()
    private val accountRegisterDAO = AccountRegisterDynamoDAO(enhancedClient)
    private val originalOcrAndPersonDataDAO = OriginalOcrAndPersonDataDynamoDAO(enhancedClient)

    private val accountRegisterRepository =
        AccountRegisterDbRepository(accountRegisterDAO, originalOcrAndPersonDataDAO, mockk())

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    @MockBean(PushNotificationService::class)
    fun pushNotificationSenderService() = pushNotificationService
    private val pushNotificationService: PushNotificationService = mockk(relaxed = true)

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val validAddressRequestTO =
        AddressRequestTO("tipo de rua", "nome da rua", "0", "ap 100", "bairro", "cidade", "uf", "********")

    private lateinit var accountId: AccountId

    private fun buildUpdateAddressRequest(
        cookie: Cookie,
        addressRequestTO: AddressRequestTO,
    ): MutableHttpRequest<AddressRequestTO> {
        return HttpRequest.PUT("/register/address", addressRequestTO)
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    private fun buildUpgradeUpdateAddressRequest(
        cookie: Cookie,
        addressRequestTO: AddressRequestTO,
    ): MutableHttpRequest<AddressRequestTO> {
        return HttpRequest.PUT("/upgrade/address", addressRequestTO)
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)

        val partialAccount = accountRepository.create(
            "fake_user",
            EmailAddress("<EMAIL>"),
            registrationType = RegistrationType.BASIC,
        )

        accountId = partialAccount.id

        accountRegisterRepository.save(accountRegisterDataWithDocumentInfo.copy(accountId = accountId))
    }

    @Test
    fun `should return BAD_REQUEST when update zip code is invalid`() {
        val request = buildUpdateAddressRequest(
            generateCookie(accountId, Role.GUEST),
            validAddressRequestTO.copy(zipCode = "12345"),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    @Test
    fun `should return BAD_REQUEST when update zip code is valid but account is not editable`() {
        val request = buildUpdateAddressRequest(generateCookie(accountId, Role.GUEST), validAddressRequestTO)
        accountRepository.updatePartialAccountStatus(accountId, AccountStatus.UNDER_REVIEW)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    @Test
    fun `deve retornar sem targetGroup quando monthlyIncome for inexistente`() {
        val request = buildUpdateAddressRequest(generateCookie(accountId, Role.GUEST), validAddressRequestTO)

        val response = client.toBlocking()
            .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(String::class.java))

        response.status shouldBe HttpStatus.OK
        response.getBody(AccountRegisterDataTO::class.java).get().targetGroup shouldBe null
    }

    @Test
    fun `should return account register when update address is successfull and update database`() {
        val updatedDate = getZonedDateTime()

        val request = buildUpdateAddressRequest(generateCookie(accountId, Role.GUEST), validAddressRequestTO)

        withGivenDateTime(updatedDate) {
            val response = client.toBlocking()
                .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(String::class.java))

            response.status shouldBe HttpStatus.OK

            val responseAddress = response.body.get().address!!

            with(validAddressRequestTO) {
                streetType shouldBe responseAddress.streetType
                streetName shouldBe responseAddress.streetName
                number shouldBe responseAddress.number
                complement shouldBe responseAddress.complement
                neighborhood shouldBe responseAddress.neighborhood
                city shouldBe responseAddress.city
                state shouldBe responseAddress.state
            }

            with(accountRegisterRepository.findByAccountId(accountId).address!!) {
                streetType shouldBe responseAddress.streetType
                streetName shouldBe responseAddress.streetName
                number shouldBe responseAddress.number
                complement shouldBe responseAddress.complement
                neighborhood shouldBe responseAddress.neighborhood
                city shouldBe responseAddress.city
                state shouldBe responseAddress.state
            }

            response.body.get().lastUpdated shouldBe updatedDate.format(dateTimeFormat)
        }
    }

    @Test
    fun `deve retornar OK quando for chamado por uma conta do tipo basic`() {
        val basicAccount = accountRepository.create(ACCOUNT.copy(type = UserAccountType.BASIC_ACCOUNT, upgradeStatus = UpgradeStatus.INCOMPLETE))
        accountRegisterRepository.save(accountRegisterDataWithDocumentInfo.copy(accountId = basicAccount.accountId))

        val request = buildUpgradeUpdateAddressRequest(generateCookie(basicAccount.accountId, Role.OWNER), validAddressRequestTO)

        val response = client.toBlocking()
            .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(String::class.java))

        response.status shouldBe HttpStatus.OK

        with(accountRegisterRepository.findByAccountId(basicAccount.accountId).address!!) {
            streetType shouldBe validAddressRequestTO.streetType
            streetName shouldBe validAddressRequestTO.streetName
            number shouldBe validAddressRequestTO.number
            complement shouldBe validAddressRequestTO.complement
            neighborhood shouldBe validAddressRequestTO.neighborhood
            city shouldBe validAddressRequestTO.city
            state shouldBe validAddressRequestTO.state
        }
    }
}