package ai.friday.billpayment.integration.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.accountRegisterDataWithDocumentInfo
import ai.friday.billpayment.adapters.api.AccountRegisterDataTO
import ai.friday.billpayment.adapters.api.MonthlyIncomeRequestTO
import ai.friday.billpayment.adapters.api.targetGroupA
import ai.friday.billpayment.adapters.api.targetGroupB
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.OriginalOcrAndPersonDataDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.generateCookie
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.EnumSource

@MicronautTest(environments = [FRIDAY_ENV])
class PutMonthlyIncomeIntegrationTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {
    val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val enhancedClient = getDynamoDB()
    private val accountRegisterDAO = AccountRegisterDynamoDAO(enhancedClient)
    private val originalOcrAndPersonDataDAO = OriginalOcrAndPersonDataDynamoDAO(enhancedClient)

    private val accountRegisterRepository =
        AccountRegisterDbRepository(accountRegisterDAO, originalOcrAndPersonDataDAO, mockk())

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private lateinit var accountId: AccountId

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    private fun buildUpdateMonthlyIncomeRequest(
        monthlyIncomeRequestTO: MonthlyIncomeRequestTO,
        cookie: Cookie = generateCookie(accountId, Role.GUEST),
    ): MutableHttpRequest<MonthlyIncomeRequestTO> {
        return HttpRequest.PUT("/register/monthlyIncome", monthlyIncomeRequestTO)
            .cookie(cookie).header("X-API-VERSION", "2")
    }
    private fun buildUpgradeUpdateMonthlyIncomeRequest(
        monthlyIncomeRequestTO: MonthlyIncomeRequestTO,
        cookie: Cookie = generateCookie(accountId, Role.OWNER),
    ): MutableHttpRequest<MonthlyIncomeRequestTO> {
        return HttpRequest.PUT("/upgrade/monthlyIncome", monthlyIncomeRequestTO)
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)

        val partialAccount = accountRepository.create(
            "fake_user",
            EmailAddress("<EMAIL>"),
            registrationType = RegistrationType.BASIC,
        )
        accountId = partialAccount.id
        accountRegisterRepository.save(accountRegisterDataWithDocumentInfo.copy(accountId = accountId))
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "-1,10",
            "20,10",
            "20,-10",
            "200000,400000",
            "200000,400001",
            "200001,400001",
            "2000000,null",
        ],
        nullValues = ["null"],
    )
    fun `should return a bad request when monthly income values are invalid`(lowerBound: Long, upperBound: Long?) {
        val request = buildUpdateMonthlyIncomeRequest(
            MonthlyIncomeRequestTO(
                lowerBound = lowerBound,
                upperBound = upperBound,
            ),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "0,200000",
            "200001,400000",
            "400001,1000000",
            "1000001,2000000",
            "2000001,null",
        ],
        nullValues = ["null"],
    )
    fun `should return success when monthly income values are valid`(lowerBound: Long, upperBound: Long?) {
        val monthlyIncomeExpectedTO = MonthlyIncomeRequestTO(
            lowerBound = lowerBound,
            upperBound = upperBound,
        )

        val request = buildUpdateMonthlyIncomeRequest(
            monthlyIncomeExpectedTO,
        )

        val response = client.toBlocking()
            .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(String::class.java))

        response.status shouldBe HttpStatus.OK
        response.getBody(AccountRegisterDataTO::class.java).get().monthlyIncome shouldBe monthlyIncomeExpectedTO
        val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)

        with(accountRegisterData.monthlyIncome!!) {
            this.upperBound shouldBe monthlyIncomeExpectedTO.upperBound
            this.lowerBound shouldBe monthlyIncomeExpectedTO.lowerBound
        }
    }

    @Test
    fun `deve retornar targetGroupB quando lowerBound for menor ou igual que 1000000`() {
        val monthlyIncomeExpectedTO = MonthlyIncomeRequestTO(
            lowerBound = 400001,
            upperBound = 1000000,
        )

        val request = buildUpdateMonthlyIncomeRequest(
            monthlyIncomeExpectedTO,
        )

        val response = client.toBlocking()
            .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(String::class.java))

        response.status shouldBe HttpStatus.OK
        response.getBody(AccountRegisterDataTO::class.java).get().targetGroup shouldBe targetGroupB
    }

    @Test
    fun `deve retornar targetGroupA quando lowerBound for maior que 1000000`() {
        val monthlyIncomeExpectedTO = MonthlyIncomeRequestTO(
            lowerBound = 1000001,
            upperBound = 2000000,
        )

        val request = buildUpdateMonthlyIncomeRequest(
            monthlyIncomeExpectedTO,
        )

        val response = client.toBlocking()
            .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(String::class.java))

        response.status shouldBe HttpStatus.OK
        response.getBody(AccountRegisterDataTO::class.java).get().targetGroup shouldBe targetGroupA
    }

    @ParameterizedTest
    @EnumSource(AccountStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["REGISTER_INCOMPLETE"])
    fun `should return BAD_REQUEST when account register when info is correct but account is not editable`(
        accountStatus: AccountStatus,
    ) {
        accountRepository.updatePartialAccountStatus(accountId, accountStatus)

        val request = buildUpdateMonthlyIncomeRequest(
            MonthlyIncomeRequestTO(
                lowerBound = 0,
                upperBound = 2_000_00,
            ),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    @Test
    fun `deve retornar OK quando for chamado por uma conta do tipo basic`() {
        accountRepository.save(ACCOUNT.copy(accountId = accountId, type = UserAccountType.BASIC_ACCOUNT))

        val monthlyIncomeExpectedTO = MonthlyIncomeRequestTO(
            lowerBound = 1000001,
            upperBound = 2000000,
        )

        val request = buildUpgradeUpdateMonthlyIncomeRequest(
            monthlyIncomeExpectedTO,
            generateCookie(accountId, Role.OWNER),
        )

        val response = client.toBlocking()
            .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(String::class.java))

        response.status shouldBe HttpStatus.OK
        response.getBody(AccountRegisterDataTO::class.java).get().targetGroup shouldBe targetGroupA
    }
}