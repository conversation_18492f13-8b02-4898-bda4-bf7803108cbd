/*
package ai.friday.billpayment.integration.api

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.api.BillsToScheduleLegacyTO
import ai.friday.billpayment.adapters.api.ScheduledBillsTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.billLockProvider
import ai.friday.billpayment.app.integrations.BillInstrumentationService
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ScheduledBillPaymentService
import ai.friday.billpayment.app.payment.ScheduleTo
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billIgnored
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.createLockTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.integration.loadBalancePaymentMethod
import ai.friday.billpayment.integration.onWallet
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import arrow.core.getOrElse
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.shouldBe
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.Called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.verify
import jakarta.inject.Named
import java.time.LocalDateTime
import java.time.ZonedDateTime
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
@MicronautTest(environments = [FRIDAY_ENV])
class ScheduleBillsIntegrationTest(
    embeddedServer: EmbeddedServer,
    val dynamoDB: AmazonDynamoDB,
    @Named(billLockProvider) val lockProvider: InternalLock,
) {

    @field:Property(name = "lock.tableName")
    lateinit var lockTableName: String

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)

    private val billEventRepository = BillEventDBRepository(
        dynamoDbDAO,
        allFalseFeatureConfiguration,
    )
    private val billRepository = DynamoDbBillRepository(dynamoDbDAO)
    private val accountRepository = AccountDbRepository(dynamoDbDAO)
    private val walletRepository = WalletDbRepository(dynamoDbDAO, mockk())

    private val schedulePath = "/schedule"

    val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @MockBean(ScheduledBillPaymentService::class)
    fun getBankAccountService(): ScheduledBillPaymentService = mockk() {
        every { process(any(), any()) } just runs
    }

    @MockBean(BillInstrumentationService::class)
    fun billInstrumentationService() = billInstrumentationService
    private val billInstrumentationService = mockk<BillInstrumentationService>(relaxUnitFun = true)

    private val walletFixture = WalletFixture()
    private lateinit var wallet: Wallet

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        createLockTable(dynamoDB, lockTableName)

        wallet = walletFixture.buildWallet(
            otherMembers = listOf(
                walletFixture.participant,
                walletFixture.limitedParticipant,
                walletFixture.ultraLimitedParticipant,
                walletFixture.cantPayParticipant,
            ),
        )
        walletRepository.save(wallet)
    }

    @Test
    fun `should return 401 if authorization token is invalid or not present`() {
        val request = HttpRequest.POST(schedulePath, "{}").header("X-API-VERSION", "2")
        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(request, Unit::class.java)
        }
        thrown.status shouldBe HttpStatus.UNAUTHORIZED
    }

    @Test
    fun `should return ok when bill id list is empty`() {
        val request = buildScheduleBillsHttpRequest(BillsToScheduleLegacyTO(emptyList()))
        val result = client.toBlocking().exchange(request, Argument.of(ScheduledBillsTO::class.java))

        result.status shouldBe HttpStatus.OK
        result.getBody(ScheduledBillsTO::class.java).get().billList.isEmpty() shouldBe true

        verify {
            billInstrumentationService wasNot Called
        }
    }

    @Test
    fun `should return ok with bill not scheduled when bill is not found`() {
        val result = scheduleBill()

        val responseBody = result.getBody(ScheduledBillsTO::class.java).get()

        assertSoftly {
            result.status shouldBe HttpStatus.OK
            with(responseBody.billList) {
                isNotEmpty() shouldBe true
                first().billId shouldBe billAdded.billId.value
                first().scheduled shouldBe false
            }
        }

        verify {
            billInstrumentationService wasNot Called
        }
    }

    @Test
    fun `should return ok with bill not scheduled when cannot acquire lock`() {
        billEventRepository.save(billAdded)
        loadBalancePaymentMethod(accountRepository)
        loadAccountIntoDb(dynamoDB)
        val lock = lockProvider.acquireLock(billAdded.billId.value)

        val result = scheduleBill()

        val responseBody = result.getBody(ScheduledBillsTO::class.java).get()
        assertSoftly {
            result.status shouldBe HttpStatus.OK
            with(responseBody.billList) {
                isNotEmpty() shouldBe true
                first().billId shouldBe billAdded.billId.value
                first().scheduled shouldBe false
            }
        }

        verify {
            billInstrumentationService wasNot Called
        }

        lock!!.unlock()
    }

    @Test
    fun `should return ok with bill scheduled`() {
        val now = ZonedDateTime.of(LocalDateTime.of(2021, 9, 9, 12, 0), brazilTimeZone)
        val walletBillAdded = billAdded.copy(walletId = wallet.id)
        billEventRepository.save(walletBillAdded)
        loadBalancePaymentMethod(
            accountRepository = accountRepository,
            paymentMethodId = wallet.paymentMethodId.value,
            accountId = wallet.founder.accountId.value,
        )
        loadAccountIntoDb(dynamoDB)

        withGivenDateTime(now.withHour(12)) {
            val result = scheduleBill()

            val responseBody = result.getBody(ScheduledBillsTO::class.java).get()
            val bill =
                billEventRepository.getBillById(walletBillAdded.billId)
                    .getOrElse { throw NoStackTraceException("teste") }
            billRepository.findBill(walletBillAdded.billId, walletBillAdded.walletId)

            val expectedDate = now.toLocalDate()
            assertSoftly {
                result.status shouldBe HttpStatus.OK
                with(responseBody.billList) {
                    isNotEmpty() shouldBe true
                    first().billId shouldBe walletBillAdded.billId.value
                    first().scheduled shouldBe true
                }
                bill.isPaymentScheduled() shouldBe true
                bill.schedule?.date shouldBe expectedDate

                bill.getLastSchedule()!!.paymentLimitTime shouldBe billAdded.paymentLimitTime
            }

            val walletSlot = slot<Wallet>()
            val accountIdSlot = slot<AccountId>()
            verify(exactly = 1) {
                billInstrumentationService.scheduled(capture(walletSlot), capture(accountIdSlot), 1)
            }
            walletSlot.captured.id shouldBe wallet.id
            accountIdSlot.captured shouldBe walletFixture.participant.accountId
        }
    }

    @Test
    fun `should return ok with bill not scheduled when bill cannot be scheduled`() {
        billEventRepository.save(billAdded)
        billEventRepository.save(billIgnored)
        val result = scheduleBill()

        val responseBody = result.getBody(ScheduledBillsTO::class.java).get()
        val bill =
            billEventRepository.getBillById(billAdded.billId).getOrElse { throw NoStackTraceException("teste") }

        assertSoftly {
            result.status shouldBe HttpStatus.OK
            with(responseBody.billList) {
                isNotEmpty() shouldBe true
                first().billId shouldBe billAdded.billId.value
                first().scheduled shouldBe false
            }
            bill.isPaymentScheduled() shouldBe false
        }

        verify {
            billInstrumentationService wasNot Called
        }
    }

    @Test
    fun `should change scheduled date when scheduled bill is rescheduled for today`() {
        val now = ZonedDateTime.of(LocalDateTime.of(2021, 9, 9, 12, 0), brazilTimeZone)
        loadBalancePaymentMethod(
            accountRepository = accountRepository,
            paymentMethodId = wallet.paymentMethodId.value,
            accountId = wallet.founder.accountId.value,
        )
        loadAccountIntoDb(dynamoDB)
        billEventRepository.save(
            billAdded.copy(
                walletId = wallet.id,
                created = now.minusSeconds(2).toInstant().toEpochMilli(),
            ),
        )
        billEventRepository.save(
            billPaymentScheduled.copy(
                walletId = wallet.id,
                created = now.minusSeconds(1).toInstant().toEpochMilli(),
            ),
        )

        withGivenDateTime(now.withHour(12)) {
            val result = scheduleBill()

            val responseBody = result.getBody(ScheduledBillsTO::class.java).get()
            val bill =
                billEventRepository.getBillById(billAdded.billId).getOrElse { throw NoStackTraceException("teste") }

            assertSoftly {
                result.status shouldBe HttpStatus.OK
                with(responseBody.billList) {
                    isNotEmpty() shouldBe true
                    first().billId shouldBe billAdded.billId.value
                    first().scheduled shouldBe true
                }
                bill.isPaymentScheduled() shouldBe true
                bill.schedule?.date shouldBe now.toLocalDate()
            }

            val walletSlot = slot<Wallet>()
            val accountIdSlot = slot<AccountId>()
            verify(exactly = 1) {
                billInstrumentationService.scheduled(capture(walletSlot), capture(accountIdSlot), 1)
            }
            walletSlot.captured.id shouldBe wallet.id
            accountIdSlot.captured shouldBe walletFixture.participant.accountId
        }
    }

    private fun scheduleBill(): HttpResponse<ScheduledBillsTO> {
        val billsToSchedule =
            BillsToScheduleLegacyTO(billList = listOf(billAdded.billId.value), scheduleTo = ScheduleTo.TODAY)
        val request = buildScheduleBillsHttpRequest(billsToSchedule)
        return client.toBlocking().exchange(request, Argument.of(ScheduledBillsTO::class.java))
    }

    private fun buildScheduleBillsHttpRequest(billsToScheduleTO: BillsToScheduleLegacyTO): HttpRequest<BillsToScheduleLegacyTO> {
        return HttpRequest.POST(schedulePath, billsToScheduleTO)
            .onWallet(wallet, walletFixture.participantAccount)
    }
}*/