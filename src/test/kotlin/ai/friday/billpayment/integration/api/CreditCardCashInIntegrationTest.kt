package ai.friday.billpayment.integration.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.CASHIN_TRANSACTIONID
import ai.friday.billpayment.adapters.api.CashInErrors
import ai.friday.billpayment.adapters.api.CashInRequestTO
import ai.friday.billpayment.adapters.api.CashInResponseTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.api.TransactionPaymentMethodTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.CreditCardChargebackTrackDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbTransactionRepository
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.adapters.lock.InternalLockProvider
import ai.friday.billpayment.adapters.lock.cashInLockProvider
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.cashIn.CashInExecutor
import ai.friday.billpayment.app.cashIn.CreditCardCashIn
import ai.friday.billpayment.app.cashIn.CreditCardsInstallmentsFeeConfiguration
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.toPayer
import ai.friday.billpayment.app.payment.uuidSize
import ai.friday.billpayment.app.usage.CreditCardRiskUsage
import ai.friday.billpayment.app.usage.CreditCardUsage
import ai.friday.billpayment.app.usage.CreditCardUsageService
import ai.friday.billpayment.boletoTransaction
import ai.friday.billpayment.createSinglePaymentDataWithBalance
import ai.friday.billpayment.creditCard
import ai.friday.billpayment.creditCardCashInTransaction
import ai.friday.billpayment.defaultTransactionEntityConverter
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID_3
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.createLockTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.integration.loadBalancePaymentMethod
import ai.friday.billpayment.integration.loadCreditCard
import ai.friday.billpayment.integration.onWallet
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.micronaut.context.annotation.Property
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import jakarta.inject.Named
import java.math.BigInteger
import java.util.UUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

@MicronautTest(environments = [FRIDAY_ENV])
class CreditCardCashInIntegrationTest(
    embeddedServer: EmbeddedServer,
    @Named(cashInLockProvider) private val internalLockProvider: InternalLockProvider,
    val dynamoDB: AmazonDynamoDB,
) {
    @field:Property(name = "lock.tableName")
    lateinit var lockTableName: String

    private val walletFixture = WalletFixture()
    private val wallet =
        walletFixture.buildWallet(
            otherMembers = listOf(
                walletFixture.assistant,
                walletFixture.participant,
            ),
            accountPaymentMethodId = AccountPaymentMethodId(UUID.randomUUID().toString()),
        )

    private val creditCardCreditCardUsageServiceMock: CreditCardUsageService = mockk {
        every { calculateCreditCardUsage(any()) } returns CreditCardUsage(
            quota = 1000,
            usage = 0,
            cashInFee = 400,
            mapOf(RiskLevel.LOW to CreditCardRiskUsage(quota = 1000, usage = 0)),
        )
    }

    @MockBean(CreditCardUsageService::class)
    fun getUsageService(): CreditCardUsageService = creditCardCreditCardUsageServiceMock

    @MockBean(CreditCardsInstallmentsFeeConfiguration::class)
    fun creditCardsInstallmentsFeeConfiguration() = creditCardsInstallmentsFeeConfiguration
    private val creditCardsInstallmentsFeeConfiguration: CreditCardsInstallmentsFeeConfiguration = mockk(relaxUnitFun = true) {
        every { fees } returns (1..12).associateWith { 4.0 }
    }

    @MockBean(WalletRepository::class)
    fun walletRepository(): WalletRepository = walletRepository

    private val enhancedClient = getDynamoDB()
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletRepository: WalletRepository =
        spyk(
            WalletDbRepository(
                walletDAO = walletDAO,
                walletMemberDAO = walletMemberDAO,
                inviteDAO = inviteDAO,
                inviteReminderDAO = inviteReminderDAO,
                walletLimitDAO = walletLimitDAO,
                accountRepository = accountRepository,
            ),
        ) {
            every {
                findWalletOrNull(wallet.id)
            } returns wallet
        }

    private val transactionDAO = TransactionDynamoDAO(enhancedClient)
    private val creditCardChargebackTrackDAO = CreditCardChargebackTrackDynamoDAO(enhancedClient)

    private val transactionRepository = DynamoDbTransactionRepository(
        transactionDAO = transactionDAO,
        creditCardChargebackTrackDAO = creditCardChargebackTrackDAO,
        accountRepository = accountRepository,
        transactionEntityConverter = defaultTransactionEntityConverter(
            billEventRepository = mockk(),
            walletRepository = walletRepository,
            transactionDynamo = transactionDynamo,
        ),
    )
    private val cashInExecutor: CashInExecutor = mockk(relaxUnitFun = true)

    @MockBean(CashInExecutor::class)
    fun getCashinExecutorMock() = cashInExecutor

    private val accountId = wallet.founder.accountId

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val transaction = creditCardCashInTransaction.copy(
        payer = walletFixture.founderAccount.toPayer(),
        actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
        walletId = wallet.id,
        paymentData = createSinglePaymentDataWithBalance(
            accountPaymentMethod = creditCard.copy(
                accountId = walletFixture.founderAccount.accountId,
                id = AccountPaymentMethodId(PAYMENT_METHOD_ID),
            ),
            1L,
        ),
    )

    private var walletBankAccount: AccountPaymentMethod? = null

    private fun createCashInTransaction(transactionId: TransactionId = TransactionId.build()): Transaction {
        return transaction.copy(
            id = transactionId,
            settlementData = SettlementData(
                settlementTarget = CreditCardCashIn(amount = 1, bankAccount = walletBankAccount!!),
                serviceAmountTax = 0,
                totalAmount = 1,
            ),
        )
    }

    private fun buildRequest(cashInRequestTO: CashInRequestTO, account: Account): MutableHttpRequest<CashInRequestTO> {
        return HttpRequest.POST("/transaction/cash-in", cashInRequestTO)
            .onWallet(wallet = wallet, account = account)
    }

    private fun buildGetRequest(uuid: UUID, account: Account): HttpRequest<Unit> {
        return HttpRequest.GET<Unit>("/transaction/cash-in/$uuid")
            .onWallet(wallet = wallet, account = account)
    }

    private val cashInRequestTO = CashInRequestTO(
        id = CASHIN_TRANSACTIONID.toString(),
        paymentMethod = TransactionPaymentMethodTO(
            id = PAYMENT_METHOD_ID,
            type = PaymentMethodType.CREDIT_CARD,
        ),
        netAmount = 100,
        feeAmount = 4,
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        loadAccountIntoDb(
            dynamoDB,
            accountId = wallet.founder.accountId,
            document = wallet.founder.document,
            name = wallet.founder.name,
            groups = listOf(AccountGroup.ALPHA.value),
        )
        createLockTable(dynamoDB, lockTableName)
        val tmpAccount = walletFixture.buildBankAccount(wallet)
        with(tmpAccount.method as InternalBankAccount) {
            walletBankAccount = loadBalancePaymentMethod(
                accountRepository,
                document = document,
                accountId = wallet.founder.accountId.value,
                paymentMethodId = tmpAccount.id.value,
                bankNo = 111,
                bankRoutingNo = 4,
                bankAccountNo = BigInteger("123555"),
                bankAccountDv = "1",
                mode = BankAccountMode.PHYSICAL,
            )
        }
    }

    @Test
    fun `should return bad request when net amount is 0 or less`() {
        val thrown = assertThrows<HttpClientResponseException> {
            client.exchange(
                buildRequest(
                    cashInRequestTO.copy(netAmount = 0),
                    walletFixture.founderAccount,
                ),
            )
                .blockingSingle()
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get() shouldBe CashInErrors.INVALID_AMOUNT.responseTO
    }

    @Test
    fun `should return bad request when net amount is less than 1,00 BRL`() {
        val thrown = assertThrows<HttpClientResponseException> {
            client.exchange(
                buildRequest(
                    cashInRequestTO.copy(netAmount = 99),
                    walletFixture.founderAccount,
                ),
            )
                .blockingSingle()
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get() shouldBe CashInErrors.INVALID_AMOUNT.responseTO
    }

    @Test
    fun `should return bad request when fee amount is less than 0`() {
        val thrown = assertThrows<HttpClientResponseException> {
            client.exchange(
                buildRequest(
                    cashInRequestTO.copy(netAmount = 1, feeAmount = -1),
                    walletFixture.founderAccount,
                ),
            )
                .blockingSingle()
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get() shouldBe CashInErrors.INVALID_AMOUNT.responseTO
    }

    @Test
    fun `should return bad request when fee amount differs from expected`() {
        val thrown = assertThrows<HttpClientResponseException> {
            client.exchange(
                buildRequest(
                    cashInRequestTO.copy(netAmount = 102, feeAmount = 102),
                    walletFixture.founderAccount,
                ),
            )
                .blockingSingle()
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get() shouldBe CashInErrors.INVALID_FEE.responseTO
    }

    @Test
    fun `should return bad request when id is not an uuid`() {
        val thrown = assertThrows<HttpClientResponseException> {
            client.exchange(buildRequest(cashInRequestTO.copy(id = "random string"), walletFixture.founderAccount))
                .blockingSingle()
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get() shouldBe CashInErrors.INVALID_UUID.responseTO
    }

    @Test
    fun `should return bad request when payment method type is not creditcard`() {
        val thrown = assertThrows<HttpClientResponseException> {
            client.exchange(
                buildRequest(
                    cashInRequestTO.copy(
                        paymentMethod = TransactionPaymentMethodTO(
                            id = UUID.randomUUID().toString(),
                            type = PaymentMethodType.BALANCE,
                        ),
                    ),
                    walletFixture.founderAccount,
                ),
            ).blockingSingle()
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java)
            .get() shouldBe CashInErrors.PAYMENT_METHOD_NOT_CREDITCARD.responseTO
    }

    @Test
    fun `should return ACCEPTED on successful request on regular account`() {
        val response = client.exchange(buildRequest(cashInRequestTO, walletFixture.founderAccount)).blockingSingle()
        val expectedTransactionId = TransactionId.build(cashInRequestTO.id)

        response.status shouldBe HttpStatus.ACCEPTED

        val transaction = transactionRepository.findById(expectedTransactionId)

        with(transaction) {
            status shouldBe TransactionStatus.PROCESSING
            type shouldBe TransactionType.CASH_IN
            actionSource shouldBe ActionSource.Api(accountId = walletFixture.founderAccount.accountId)
            id shouldBe expectedTransactionId
            nsu shouldBe 0L
            payer shouldBe walletFixture.founderAccount.toPayer()
            paymentData.toSingle().accountPaymentMethod.id.value shouldBe cashInRequestTO.paymentMethod.id
            paymentData.toSingle().accountPaymentMethod.method.type shouldBe PaymentMethodType.CREDIT_CARD
            paymentData.toSingle().payment shouldBe null
            paymentData.toSingle().details shouldBe PaymentMethodsDetailWithCreditCard(
                paymentMethodId = AccountPaymentMethodId(cashInRequestTO.paymentMethod.id),
                netAmount = cashInRequestTO.netAmount,
                feeAmount = cashInRequestTO.feeAmount,
                installments = 1,
                calculationId = null,
                fee = 4.0,
            )
            settlementData.totalAmount shouldBe cashInRequestTO.netAmount + cashInRequestTO.feeAmount
            settlementData.getTarget<CreditCardCashIn>().amount shouldBe cashInRequestTO.netAmount
            settlementData.getTarget<CreditCardCashIn>().bankAccount shouldBe walletBankAccount
            settlementData.settlementOperation shouldBe null
            walletId shouldBe wallet.id
        }
        verify { cashInExecutor.execute(any()) }

        internalLockProvider.acquireLock(ACCOUNT_ID) shouldNotBe null
    }

    @Test
    fun `should return ACCEPTED on successful request on cashin with credit card`() {
        val creditCardEnabledAccount = walletFixture.founderAccount.copy(
            configuration = walletFixture.founderAccount.configuration.copy(
                creditCardConfiguration = walletFixture.founderAccount.creditCardConfiguration.copy(
                    quota = 1_000_00,
                ),
                groups = listOf(AccountGroup.CREDIT_CARD_STANDARD_PLAN, AccountGroup.ALPHA),
            ),
        )

        accountRepository.save(creditCardEnabledAccount)
        val response = client.exchange(
            buildRequest(
                cashInRequestTO.copy(netAmount = 100, feeAmount = 4),
                walletFixture.founderAccount,
            ),
        ).blockingSingle()
        val expectedTransactionId = TransactionId.build(cashInRequestTO.id)

        response.status shouldBe HttpStatus.ACCEPTED

        val transaction = transactionRepository.findById(expectedTransactionId)

        with(transaction) {
            status shouldBe TransactionStatus.PROCESSING
            type shouldBe TransactionType.CASH_IN
            actionSource shouldBe ActionSource.Api(accountId = walletFixture.founderAccount.accountId)
            id shouldBe expectedTransactionId
            nsu shouldBe 0L
            payer shouldBe walletFixture.founderAccount.toPayer()
            paymentData.toSingle().accountPaymentMethod.id.value shouldBe cashInRequestTO.paymentMethod.id
            paymentData.toSingle().accountPaymentMethod.method.type shouldBe PaymentMethodType.CREDIT_CARD
            paymentData.toSingle().payment shouldBe null
            settlementData.totalAmount shouldBe 104L
            settlementData.getTarget<CreditCardCashIn>().amount shouldBe cashInRequestTO.netAmount
            settlementData.getTarget<CreditCardCashIn>().bankAccount shouldBe walletBankAccount
            settlementData.settlementOperation shouldBe null
            walletId shouldBe wallet.id
        }
        verify { cashInExecutor.execute(any()) }

        internalLockProvider.acquireLock(ACCOUNT_ID) shouldNotBe null
    }

    @Test
    fun `should return BAD_REQUEST when payment method is not found`() {
        val thrown = assertThrows<HttpClientResponseException> {
            client.exchange(
                buildRequest(
                    cashInRequestTO.copy(
                        paymentMethod = cashInRequestTO.paymentMethod.copy(
                            id = UUID.randomUUID().toString(),
                        ),
                    ),
                    walletFixture.founderAccount,
                ),
            ).blockingSingle()
        }
        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get() shouldBe CashInErrors.PAYMENT_METHOD_NOT_FOUND.responseTO
    }

    @Test
    fun `should return BAD_REQUEST when payment method is not a credit card`() {
        val accountPaymentMethod = accountRepository.createAccountPaymentMethod(
            accountId = accountId,
            bankAccount = BankAccount(
                accountType = AccountType.CHECKING,
                bankNo = 1L,
                routingNo = 2L,
                accountNo = BigInteger("3"),
                accountDv = "X",
                document = DOCUMENT,
            ),
            position = 1,
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.exchange(
                buildRequest(
                    cashInRequestTO.copy(
                        paymentMethod = cashInRequestTO.paymentMethod.copy(
                            id = accountPaymentMethod.id.value,
                        ),
                    ),
                    walletFixture.founderAccount,
                ),
            ).blockingSingle()
        }
        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java)
            .get() shouldBe CashInErrors.PAYMENT_METHOD_NOT_CREDITCARD.responseTO
    }

    @Test
    fun `should return BAD_REQUEST when there is a transaction in the same day`() {
        transactionRepository.save(createCashInTransaction())

        val thrown = assertThrows<HttpClientResponseException> {
            client.exchange(buildRequest(cashInRequestTO, walletFixture.founderAccount)).blockingSingle()
        }
        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get() shouldBe CashInErrors.CASH_IN_LIMIT_REACHED.responseTO
    }

    @Test
    fun `deve retornar bad request quando o usuário não possui mais quota de cartão`() {
        every { creditCardCreditCardUsageServiceMock.calculateCreditCardUsage(any()) } returns CreditCardUsage(
            quota = 500,
            usage = 401,
            cashInFee = 0,
            mapOf(RiskLevel.LOW to CreditCardRiskUsage(quota = 500, usage = 401)),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.exchange(buildRequest(cashInRequestTO, walletFixture.founderAccount)).blockingSingle()
        }
        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get() shouldBe CashInErrors.QUOTA_LIMIT_REACHED.responseTO
    }

    @Test
    fun `should return BAD_REQUEST when transaction id belongs to another user`() {
        loadCreditCard(
            amazonDynamoDB = dynamoDB,
            accountId = AccountId(ACCOUNT_ID_2),
            paymentMethodId = PAYMENT_METHOD_ID_3,
        )

        val otherTransacion = createCashInTransaction(transactionId = TransactionId.build(CASHIN_TRANSACTIONID))

        transactionRepository.save(
            otherTransacion.copy(
                payer = otherTransacion.payer.copy(
                    accountId = AccountId(ACCOUNT_ID_2),
                    document = "***********",
                    name = "name",
                ),
                paymentData = createSinglePaymentDataWithBalance(
                    accountPaymentMethod = creditCard.copy(
                        accountId = AccountId(ACCOUNT_ID_2),
                        id = AccountPaymentMethodId(PAYMENT_METHOD_ID_3),
                    ),
                    1L,
                ),
                actionSource = ActionSource.Api(accountId = AccountId(ACCOUNT_ID_2)),
            ),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.exchange(buildRequest(cashInRequestTO, walletFixture.founderAccount)).blockingSingle()
        }
        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get() shouldBe CashInErrors.INVALID_TRANSACTION_ID.responseTO
    }

    @Test
    fun `should return BAD_REQUEST when other transaction is processing`() {
        transactionRepository.save(
            transaction.copy(
                id = TransactionId.build(),
                status = TransactionStatus.PROCESSING,
                settlementData = SettlementData(
                    settlementTarget = CreditCardCashIn(amount = 1, bankAccount = walletBankAccount!!),
                    serviceAmountTax = 0,
                    totalAmount = 1,
                ),
                walletId = wallet.id,
            ),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.exchange(buildRequest(cashInRequestTO, walletFixture.founderAccount)).blockingSingle()
        }
        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get() shouldBe CashInErrors.CASH_IN_IN_PROGRESS.responseTO
    }

    @Test
    fun `should return BAD_REQUEST when lock can't be acquired`() {
        accountRepository.createAccountPaymentMethod(
            accountId = AccountId(ACCOUNT_ID),
            bankAccount = BankAccount(
                accountType = AccountType.CHECKING,
                bankNo = 1L,
                routingNo = 2L,
                accountNo = BigInteger("3"),
                accountDv = "X",
                document = DOCUMENT,
            ),
            position = 2,
        )

        internalLockProvider.acquireLock(wallet.id.value)

        val thrown = assertThrows<HttpClientResponseException> {
            client.exchange(buildRequest(cashInRequestTO, walletFixture.founderAccount)).blockingSingle()
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST

        val expectedTransactionId = TransactionId.build(cashInRequestTO.id)
        assertThrows<ItemNotFoundException> {
            transactionRepository.findById(expectedTransactionId)
        }

        verify(exactly = 0) { cashInExecutor.execute(any()) }
    }

    @Test
    fun `should return NOT FOUND when transaction does not exists`() {
        val thrown = assertThrows<HttpClientResponseException> {
            client.exchange(buildGetRequest(UUID.randomUUID(), walletFixture.founderAccount)).blockingSingle()
        }
        thrown.status shouldBe HttpStatus.NOT_FOUND
        thrown.response.getBody(ResponseTO::class.java).get() shouldBe CashInErrors.TRANSACTION_NOT_FOUND.responseTO
    }

    @ParameterizedTest
    @EnumSource(TransactionStatus::class)
    fun `should return persisted transaction`(transactionStatus: TransactionStatus) {
        val completedTransaction = createCashInTransaction()
        transactionRepository.save(completedTransaction.copy(status = transactionStatus))

        val transactionUuid = UUID.fromString(completedTransaction.id.value.takeLast(uuidSize))
        val response = client.retrieve(
            buildGetRequest(transactionUuid, walletFixture.founderAccount),
            CashInResponseTO::class.java,
        ).blockingSingle()

        response.status shouldBe transactionStatus
        response.amount shouldBe completedTransaction.settlementData.getTarget<CreditCardCashIn>().amount
        response.serviceAmountTax shouldBe completedTransaction.settlementData.serviceAmountTax
        response.totalAmount shouldBe completedTransaction.settlementData.totalAmount
        response.paymentMethod.id shouldBe completedTransaction.paymentData.toSingle().accountPaymentMethod.id.value
    }

    @Test
    fun `should return NOT FOUND when transaction belongs to another user`() {
        loadCreditCard(
            amazonDynamoDB = dynamoDB,
            accountId = AccountId(ACCOUNT_ID_2),
            paymentMethodId = PAYMENT_METHOD_ID_3,
        )

        val completedTransaction = createCashInTransaction()
        transactionRepository.save(
            completedTransaction.copy(
                payer = completedTransaction.payer.copy(
                    accountId = AccountId(ACCOUNT_ID_2),
                ),
                paymentData = createSinglePaymentDataWithBalance(
                    accountPaymentMethod = creditCard.copy(
                        accountId = AccountId(ACCOUNT_ID_2),
                        id = AccountPaymentMethodId(PAYMENT_METHOD_ID_3),
                    ),
                    1L,
                ),
                actionSource = ActionSource.Api(accountId = AccountId(ACCOUNT_ID_2)),
            ),
        )

        val transactionUuid = UUID.fromString(completedTransaction.id.value.takeLast(uuidSize))
        val thrown = assertThrows<HttpClientResponseException> {
            client.exchange(buildGetRequest(transactionUuid, walletFixture.founderAccount)).blockingSingle()
        }
        thrown.status shouldBe HttpStatus.NOT_FOUND
        thrown.response.getBody(ResponseTO::class.java).get() shouldBe CashInErrors.TRANSACTION_NOT_FOUND.responseTO
    }

    @Test
    fun `should return NOT FOUND when transaction is not cash-in`() {
        loadBalancePaymentMethod(
            accountRepository,
            document = walletFixture.founderAccount.document,
            accountId = accountId.value,
        )
        transactionRepository.save(boletoTransaction)

        val transactionUuid = UUID.fromString(boletoTransaction.id.value.takeLast(uuidSize))
        val thrown = assertThrows<HttpClientResponseException> {
            client.exchange(buildGetRequest(transactionUuid, walletFixture.founderAccount)).blockingSingle()
        }
        thrown.status shouldBe HttpStatus.NOT_FOUND
        thrown.response.getBody(ResponseTO::class.java).get() shouldBe CashInErrors.TRANSACTION_NOT_FOUND.responseTO
    }

    @Test
    fun `should return bad request with invalid target account when bank account is virtual`() {
        loadBalancePaymentMethod(
            accountRepository,
            document = walletFixture.founder.document,
            accountId = walletFixture.founder.accountId.value,
            paymentMethodId = wallet.paymentMethodId.value,
            mode = BankAccountMode.VIRTUAL,
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.exchange(buildRequest(cashInRequestTO, walletFixture.founderAccount)).blockingSingle()
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get() shouldBe CashInErrors.INVALID_TARGET_ACCOUNT.responseTO
    }
}