package ai.friday.billpayment.integration.api

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.api.AccountClosureDetailsTO
import ai.friday.billpayment.adapters.api.AccountDeletionResponseTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountClosureReason
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.CloseAccountError
import ai.friday.billpayment.app.account.CloseAccountService
import ai.friday.billpayment.app.account.CloseWalletError
import ai.friday.billpayment.app.notification.PushNotificationService
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.SecurityFixture
import arrow.core.left
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
class UserDeletionTest(embeddedServer: EmbeddedServer) {

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val closeAccountServiceMock: CloseAccountService = mockk()

    @MockBean(CloseAccountService::class)
    fun getCloseAccountServiceMock() = closeAccountServiceMock

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @MockBean(PushNotificationService::class)
    fun pushNotificationSenderService() = pushNotificationService
    private val pushNotificationService: PushNotificationService = mockk(relaxUnitFun = true)

    @Test
    fun `should return forbidden when caller is not an backoffice user`() {
        val request = buildRequest(AccountId("any"), SecurityFixture().cookieAuthOwner)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(Unit::class.java))
        }

        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    /*
        @Test
        fun `deve retornar ok quando remover com sucesso`() {
            every { closeAccountServiceMock.closeAccount(any(), any()) } returns CloseAccountResult(
                steps = listOf(
                    CloseAccountStepTypeUnsubscribe.toSimpleStep(CloseAccountStepStatus.Success),
                    CloseFounderWalletCloseAccountStep(
                        walletId = WalletId(value = "walletId"),
                        steps = CloseWalletStepType.entries.map { it.toStep(CloseWalletStepStatus.Success) },
                        status = CloseAccountStepStatus.Success,
                    ),
                    CloseFounderWalletCloseAccountStep(
                        walletId = WalletId(value = "secondaryWalletId"),
                        steps = CloseWalletStepType.entries.filter { it !in listOf(CloseWalletStepTypeCloseExternalAccountNow, CloseWalletStepTypeCloseExternalAccountLater) }.map { it.toStep() },
                        status = CloseAccountStepStatus.Success,
                    ),
                    CloseAccountStepTypeSignOutUser.toSimpleStep(CloseAccountStepStatus.Success),
                    CloseAccountStepTypeRemoveLogin.toSimpleStep(CloseAccountStepStatus.Success),
                    CloseAccountStepTypeUpdateStatusClosed.toSimpleStep(CloseAccountStepStatus.Success),
                    CloseAccountStepTypeRemoveFromWallets.toSimpleStep(CloseAccountStepStatus.Success),
                    CloseAccountStepTypeCloseCreditCards.toSimpleStep(CloseAccountStepStatus.Success),
                    CloseAccountStepTypeCloseCrmContact.toSimpleStep(CloseAccountStepStatus.Success),
                    DeactivateAccountRegisterStep(
                        closureDetails = AccountClosureDetails.create(AccountClosureReason.SUBSCRIPTION_OVERDUE, description = "10 days overdue"),
                        status = CloseAccountStepStatus.Success,

                    ),
                    CloseAccountStepTypeMarkAsFraud.toSimpleStep(CloseAccountStepStatus.Success),

                    CloseAccountStepTypeRemoveFromDDA.toSimpleStep(CloseAccountStepStatus.Warning),
                    CloseAccountStepTypeDisableNotification.toSimpleStep(CloseAccountStepStatus.Error),
                    CloseAccountStepTypeRemoveFromUserPool.toSimpleStep(CloseAccountStepStatus.Pending),
                ),
            ).right()

            val accountId = AccountId("any")
            val request = buildRequest(accountId, SecurityFixture().cookieBackoffice)

            val response = client.toBlocking().exchange(request, AccountDeletionResponseTO::class.java)

            response.status shouldBe HttpStatus.OK
            verify { closeAccountServiceMock.closeAccount(accountId, any()) }

            with(response.body()!!) {
                notificationDisabled shouldBe false
                removedFromUserPool shouldBe false
                removedFromDDA shouldBe false

                wallets.size shouldBe 2
                with(wallets.single { it.walletId == "walletId" }) {
                    bankAccountNumber shouldBe null
                    bankAccountMode shouldBe BankAccountMode.PHYSICAL
                    accountPaymentMethodStatus shouldBe AccountPaymentMethodStatus.CLOSED
                    externalAccountClosed shouldBe true
                    externalAccountCloseError shouldBe null
                    pixKeyDeleted shouldBe true
                }
                with(wallets.single { it.walletId == "secondaryWalletId" }) {
                    bankAccountNumber shouldBe null
                    bankAccountMode shouldBe BankAccountMode.VIRTUAL
                    accountPaymentMethodStatus shouldBe AccountPaymentMethodStatus.PENDING_CLOSE
                    externalAccountClosed shouldBe false
                    externalAccountCloseError shouldBe null
                    pixKeyDeleted shouldBe false
                }
            }
        }
    */
    @Test
    fun `deve retornar CONFLICT e codigo 4001 quando a conta tiver saldo`() {
        val accountId = AccountId("any")
        val request = buildRequest(accountId, SecurityFixture().cookieBackoffice)

        every { closeAccountServiceMock.closeAccount(any(), any()) } returns CloseAccountError.CloseFounderWalletError(CloseWalletError.BalanceDifferentThanZero(WalletId("walletId"), 100L, externalBankAccount = null, pixKey = null)).left()

        val thrown = assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, AccountDeletionResponseTO::class.java) }

        thrown.status shouldBe HttpStatus.CONFLICT

        verify { closeAccountServiceMock.closeAccount(accountId, any()) }

        val response = thrown.response.getBody(ResponseTO::class.java).get()
        response.code shouldBe "4001"
        with(parseObjectFrom<AccountDeletionResponseTO>(response.message)) {
            notificationDisabled shouldBe false
            removedFromUserPool shouldBe false
            removedFromDDA shouldBe false

            wallets.size shouldBe 1
            with(wallets.single()) {
                walletId shouldBe "walletId"
                externalAccountClosed shouldBe false
                externalAccountCloseError shouldBe "conta possui saldo: 100"
                pixKeyDeleted shouldBe false
            }
        }
    }

    @Test
    fun `deve retornar CONFLICT e codigo 4004 se uma transacao estiver em processamento`() {
        val accountId = AccountId("any")
        val request = buildRequest(accountId, SecurityFixture().cookieBackoffice)

        every { closeAccountServiceMock.closeAccount(any(), any()) } returns CloseAccountError.CloseFounderWalletError(CloseWalletError.TransactionStillProcessing(WalletId("walletId"))).left()

        val thrown = assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, AccountDeletionResponseTO::class.java) }

        thrown.status shouldBe HttpStatus.CONFLICT

        verify { closeAccountServiceMock.closeAccount(accountId, any()) }

        val response = thrown.response.getBody(ResponseTO::class.java).get()
        response.code shouldBe "4004"
        with(parseObjectFrom<AccountDeletionResponseTO>(response.message)) {
            notificationDisabled shouldBe false
            removedFromUserPool shouldBe false
            removedFromDDA shouldBe false

            wallets.size shouldBe 1
            with(wallets.single()) {
                walletId shouldBe "walletId"
                externalAccountClosed shouldBe false
                externalAccountCloseError shouldBe "transação em processamento"
                pixKeyDeleted shouldBe false
            }
        }
    }

    @Test
    fun `deve retornar INTERNAL_SERVER_ERROR se for um erro não mapeado`() {
        val accountId = AccountId("any")
        val request = buildRequest(accountId, SecurityFixture().cookieBackoffice)

        every { closeAccountServiceMock.closeAccount(any(), any()) } returns CloseAccountError.Unknown(NoStackTraceException("exception message")).left()

        val thrown = assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, AccountDeletionResponseTO::class.java) }

        thrown.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR

        verify { closeAccountServiceMock.closeAccount(accountId, any()) }

        val response = thrown.response.getBody(ResponseTO::class.java).get()
        response.code shouldBe "5001"
        response.message shouldBe "exception message"
    }

    @Test
    fun `deve retornar BAD_REQUEST quando o reason for inválido`() {
        val accountId = AccountId("any")
        val request = buildRequest(accountId, SecurityFixture().cookieBackoffice)
            .body(AccountClosureDetailsTO(reason = "INVALID", description = null))

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, AccountDeletionResponseTO::class.java)
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST

        with(thrown.response.getBody(ResponseTO::class.java).get()) {
            code shouldBe "4001"
        }
    }

    @Test
    fun `deve retornar BAD_REQUEST quando não tiver motivo e a descrição não for informada`() {
        val accountId = AccountId("any")
        val request = buildRequest(accountId, SecurityFixture().cookieBackoffice)
            .body(AccountClosureDetailsTO(reason = null, description = null))

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, AccountDeletionResponseTO::class.java)
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST

        with(thrown.response.getBody(ResponseTO::class.java).get()) {
            code shouldBe "4001"
        }
    }

    private fun buildRequest(accountId: AccountId, cookie: Cookie) =
        HttpRequest.DELETE<AccountClosureDetailsTO>("/backoffice/account/${accountId.value}")
            .body(AccountClosureDetailsTO(reason = AccountClosureReason.USER_REQUEST.name, description = null))
            .cookie(cookie)
}