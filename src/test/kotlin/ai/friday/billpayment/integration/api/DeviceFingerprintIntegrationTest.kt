package ai.friday.billpayment.integration.api

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.api.DeviceRequestTypeTO
import ai.friday.billpayment.adapters.api.PostDeviceResponseTO
import ai.friday.billpayment.adapters.api.PutDeviceRequestTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DeviceFingerprintDbRepository
import ai.friday.billpayment.adapters.dynamodb.DeviceFingerprintDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SystemActivityDbRepository
import ai.friday.billpayment.adapters.dynamodb.SystemActivityDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.DeviceBinded
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.fingerprint.DeviceFingerprint
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintAdapter
import ai.friday.billpayment.app.fingerprint.DeviceId
import ai.friday.billpayment.app.fingerprint.DeviceLiveness
import ai.friday.billpayment.app.fingerprint.DeviceScreenResolution
import ai.friday.billpayment.app.fingerprint.DeviceStatus
import ai.friday.billpayment.app.fingerprint.DeviceType
import ai.friday.billpayment.app.fingerprint.MobileDeviceDetails
import ai.friday.billpayment.app.fingerprint.RegisteredDevice
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.liveness.LivenessErrors
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.liveness.LivenessMatchVerify
import ai.friday.billpayment.app.liveness.LivenessService
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.SecurityFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.toArgumentsStream
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.left
import arrow.core.right
import com.itextpdf.xmp.impl.Base64
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.maps.shouldBeEmpty
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.util.UUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource

private const val livenessErrors = "ai.friday.billpayment.integration.api.DeviceFingerprintIntegrationTest#livenessErrors"

@MicronautTest(environments = [FRIDAY_ENV])
class DeviceFingerprintIntegrationTest(embeddedServer: EmbeddedServer) {
    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val enhancedDynamoDB = setupDynamoDB()

    private val repository = DeviceFingerprintDbRepository(DeviceFingerprintDynamoDAO(enhancedDynamoDB))

    private val accountDAO = AccountDynamoDAO(enhancedDynamoDB)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedDynamoDB)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedDynamoDB)
    private val nsuDAO = NSUDynamoDAO(enhancedDynamoDB)
    private val transactionDynamo = TransactionDynamo(enhancedDynamoDB)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val mockedLivenessService = mockk<LivenessService> {
        every { verifyMatch(any()) } returns mockk<LivenessMatchVerify> {
            every { match } returns true
        }.right()
    }

    private val systemActivityService = SystemActivityService(SystemActivityDbRepository(SystemActivityDynamoDAO(enhancedDynamoDB)))

    private val securityFixture = SecurityFixture()

    private val accountId = AccountId(ACCOUNT_ID)

    private val eventPublisher: EventPublisher = mockk(relaxUnitFun = true)

    private val mockedDeviceAdapter = mockk<DeviceFingerprintAdapter>()

    @MockBean(EventPublisher::class)
    fun eventPublisher(): EventPublisher = eventPublisher

    @MockBean(LivenessService::class)
    fun getLiveness(): LivenessService = mockedLivenessService

    @MockBean(DeviceFingerprintAdapter::class)
    fun getArbiDeviceAdapter(): DeviceFingerprintAdapter = mockedDeviceAdapter

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        accountRepository.save(ACCOUNT)
        systemActivityService.setCreatedPassword(ACCOUNT.accountId, getZonedDateTime().minusDays(2))
    }

    private val deviceFingerprint = "cognito-device-key"

    private val mobileDeviceDetails = MobileDeviceDetails(
        uuid = UUID.randomUUID(),
        fresh = false,
        alias = "Galaxy A02s",
        dpi = 1.75,
        fingerprint = deviceFingerprint,
        rooted = false,
        manufacturer = "samsung",
        model = "SM-A025M",
        screenResolution = DeviceScreenResolution(1920, 1080),
        storageCapacity = *********,
        type = DeviceType.ANDROID,
        osId = "7525e01ebc112d7e",
    )

    val device = RegisteredDevice(
        deviceIds = mapOf(),
        fingerprint = DeviceFingerprint(value = deviceFingerprint),
        status = DeviceStatus.PENDING,
        creationDate = getZonedDateTime(),
        details = mobileDeviceDetails,
        liveness = null,
    )

    @Nested
    @DisplayName("Create Device")
    inner class PostDevice {
        @Test
        fun `quando é um primeiro cadastro recente, deve criar direto um registered device`() {
            systemActivityService.setCreatedPassword(ACCOUNT.accountId, getZonedDateTime())

            val response = client.exchange(buildPostRequest(), PostDeviceResponseTO::class.java)
                .blockingFirst()

            response.status shouldBe HttpStatus.CREATED

            with(response.getBody(PostDeviceResponseTO::class.java).get()) {
                livenessId shouldBe null
                type shouldBe DeviceRequestTypeTO.NO_CHALLENGE
            }
            verify(exactly = 0) { mockedLivenessService wasNot Called }
        }

        @Test
        fun `quando não tem um liveness cadastrado, cria um liveness de enrollment e salva`() {
            every { mockedLivenessService.hasCompletedEnrollment(any()) } returns false.right()
            every { mockedLivenessService.enroll(any()) } returns LivenessId("livenessIdEnrollment").right()

            val response = client.exchange(buildPostRequest(), PostDeviceResponseTO::class.java)
                .blockingFirst()

            response.status shouldBe HttpStatus.CREATED

            with(response.getBody(PostDeviceResponseTO::class.java).get()) {
                livenessId shouldBe "livenessIdEnrollment"
                type shouldBe DeviceRequestTypeTO.LIVENESS
            }

            verify { mockedLivenessService.enroll(accountId) }
            verify(exactly = 0) { mockedLivenessService.match(any()) }
        }

        @Test
        fun `quando tem um liveness cadastrado, cria um liveness match e salva`() {
            every { mockedLivenessService.hasCompletedEnrollment(any()) } returns true.right()
            every { mockedLivenessService.match(any()) } returns LivenessId("livenessIdMatch").right()

            val response = client.exchange(buildPostRequest(), PostDeviceResponseTO::class.java)
                .blockingFirst()

            response.status shouldBe HttpStatus.CREATED

            with(response.getBody(PostDeviceResponseTO::class.java).get()) {
                livenessId shouldBe "livenessIdMatch"
                type shouldBe DeviceRequestTypeTO.LIVENESS
            }

            verify(exactly = 0) { mockedLivenessService.enroll(accountId) }
            verify { mockedLivenessService.match(any()) }
        }

        @Test
        fun `se a conta não estiver ativa, deve dar conflito`() {
            accountRepository.save(ACCOUNT.copy(status = AccountStatus.CLOSED))

            val response = assertThrows<HttpClientResponseException> {
                client.exchange(buildPostRequest(), PostDeviceResponseTO::class.java)
                    .blockingFirst()
            }

            response.status shouldBe HttpStatus.CONFLICT

            verify { mockedLivenessService wasNot Called }
        }

        @Test
        fun `se o fingerprint não estiver na chamada, deve dar conflito`() {
            val response = assertThrows<HttpClientResponseException> {
                client.exchange(buildPostRequest(mobileDeviceDetails.copy(fingerprint = null)), PostDeviceResponseTO::class.java)
                    .blockingFirst()
            }

            response.status shouldBe HttpStatus.CONFLICT

            verify { mockedLivenessService wasNot Called }
        }

        @Test
        fun `deve salvar as informações do device no banco corretamente`() {
            every { mockedLivenessService.hasCompletedEnrollment(any()) } returns false.right()
            every { mockedLivenessService.enroll(any()) } returns LivenessId("livenessIdEnrollment").right()

            client.exchange(buildPostRequest(), PostDeviceResponseTO::class.java)
                .blockingFirst()

            with(repository.getDeviceOrNull(DeviceFingerprint(deviceFingerprint), accountId = accountId)) {
                shouldNotBeNull()
                status shouldBe DeviceStatus.PENDING
                details shouldBe mobileDeviceDetails
                liveness!!.id shouldBe LivenessId("livenessIdEnrollment")
                liveness!!.enrollment.shouldBeTrue()
                fingerprint shouldBe DeviceFingerprint(deviceFingerprint)
                deviceIds.shouldBeEmpty()
                creationDate.toLocalDate() shouldBe getLocalDate()
            }
        }

        private fun buildPostRequest(details: MobileDeviceDetails = mobileDeviceDetails): HttpRequest<*> = HttpRequest.POST("/device", Unit)
            .header("x-device-fingerprint", Base64.encode(getObjectMapper().writeValueAsString(details)))
            .cookie(securityFixture.cookieAuthOwner)
    }

    @Nested
    @DisplayName("Confirm Device")
    inner class PutDevice {
        private val putRequest = PutDeviceRequestTO(type = DeviceRequestTypeTO.LIVENESS, livenessId = "liveness")

        private val livenessId = LivenessId("liveness")

        @Test
        fun `quando o registro não é encontrado, deve retornar erro`() {
            val response = assertThrows<HttpClientResponseException> {
                client.exchange(buildPutRequest(), PostDeviceResponseTO::class.java)
                    .blockingFirst()
            }

            response.status shouldBe HttpStatus.CONFLICT
        }

        @ParameterizedTest
        @EnumSource(value = DeviceStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["PENDING"])
        fun `quando o registered device não está no estado pendente, deve dar erro`(status: DeviceStatus) {
            repository.save(device.copy(status = status, liveness = DeviceLiveness(id = livenessId)), accountId)

            val response = assertThrows<HttpClientResponseException> {
                client.exchange(buildPutRequest(), PostDeviceResponseTO::class.java)
                    .blockingFirst()
            }

            response.status shouldBe HttpStatus.CONFLICT
        }

        @Test
        fun `quando o liveness não bate com a conta, deve dar erro`() {
            repository.save(device.copy(liveness = DeviceLiveness(id = LivenessId("another-liveness"))), accountId)

            val response = assertThrows<HttpClientResponseException> {
                client.exchange(buildPutRequest(), PostDeviceResponseTO::class.java)
                    .blockingFirst()
            }

            response.status shouldBe HttpStatus.CONFLICT
        }

        @Test
        fun `quando o fingerprint estiver correto, mesmo com outros detalhes divergentes, deve dar sucesso`() {
            repository.save(device.copy(details = device.details.copy(dpi = 4.0, screenResolution = DeviceScreenResolution(480, 640)), liveness = DeviceLiveness(id = livenessId)), accountId)

            val response = assertThrows<HttpClientResponseException> {
                client.exchange(buildPutRequest(), PostDeviceResponseTO::class.java)
                    .blockingFirst()
            }

            response.status shouldBe HttpStatus.CONFLICT
        }

        @MethodSource(livenessErrors)
        @ParameterizedTest
        fun `quando o liveness não da match, deve dar erro`(livenessErrors: LivenessErrors) {
            repository.save(device.copy(liveness = DeviceLiveness(id = livenessId, enrollment = false)), accountId)
            every { mockedLivenessService.verifyMatch(any()) } returns livenessErrors.left()

            val response = assertThrows<HttpClientResponseException> {
                client.exchange(buildPutRequest(), PostDeviceResponseTO::class.java)
                    .blockingFirst()
            }

            response.status shouldBe HttpStatus.CONFLICT

            verify { mockedLivenessService.verifyMatch(livenessId) }
            verify(exactly = 0) { mockedLivenessService.hasCompletedEnrollment(any()) }
        }

        @MethodSource(livenessErrors)
        @ParameterizedTest
        fun `quando o liveness não resolve o enrollment, deve dar erro`(livenessErrors: LivenessErrors) {
            repository.save(device.copy(liveness = DeviceLiveness(id = livenessId, enrollment = true)), accountId)
            every { mockedLivenessService.hasCompletedEnrollment(any()) } returns livenessErrors.left()

            val response = assertThrows<HttpClientResponseException> {
                client.exchange(buildPutRequest(), PostDeviceResponseTO::class.java)
                    .blockingFirst()
            }

            response.status shouldBe HttpStatus.CONFLICT

            verify(exactly = 0) { mockedLivenessService.verifyMatch(any()) }
            verify { mockedLivenessService.hasCompletedEnrollment(accountId) }
        }

        @Test
        fun `quando o liveness funcionar corretamente, deve confirmar e salvar as informações do registered device`() {
            repository.save(device.copy(liveness = DeviceLiveness(id = livenessId, enrollment = false)), accountId)
            every { mockedLivenessService.verifyMatch(any()) } returns mockk<LivenessMatchVerify> {
                every { match } returns true
            }.right()

            val response = client.exchange(buildPutRequest(), PostDeviceResponseTO::class.java)
                .blockingFirst()

            response.status shouldBe HttpStatus.NO_CONTENT

            verify(exactly = 0) { mockedLivenessService.hasCompletedEnrollment(any()) }

            val deviceBindSlot = slot<DeviceBinded>()
            verify {
                mockedLivenessService.verifyMatch(livenessId)
                eventPublisher.publish(capture(deviceBindSlot))
            }

            with(deviceBindSlot.captured) {
                accountId shouldBe accountId
                deviceFingerprint shouldBe deviceFingerprint
            }

            with(repository.getDeviceOrNull(DeviceFingerprint(deviceFingerprint), accountId)) {
                shouldNotBeNull()
                status shouldBe DeviceStatus.ACTIVE
            }
        }

        @Test
        fun `deve desativar outros dispositivos ativos`() {
            val firstFingerprint = DeviceFingerprint("device-fingerprint-1")
            val secondFingerprint = DeviceFingerprint("device-fingerprint-2")
            val firstActiveRegisteredDevice = DeviceId("123123")
            val secondActiveRegisteredDevice = DeviceId("sdofjdsiojo")
            repository.save(
                device.copy(
                    deviceIds = mapOf(AccountNumber("111") to firstActiveRegisteredDevice),
                    fingerprint = firstFingerprint,
                    status = DeviceStatus.ACTIVE,
                ),
                accountId,
            )
            repository.save(
                device.copy(
                    deviceIds = mapOf(AccountNumber("222") to secondActiveRegisteredDevice),
                    fingerprint = secondFingerprint,
                    status = DeviceStatus.ACTIVE,
                ),
                accountId,
            )
            repository.save(
                device.copy(
                    liveness = DeviceLiveness(id = livenessId, enrollment = false),
                ),
                accountId,
            )
            every { mockedLivenessService.verifyMatch(any()) } returns mockk<LivenessMatchVerify> {
                every { match } returns true
            }.right()

            every { mockedDeviceAdapter.removeDeviceId(any()) } returns true

            val response = client.exchange(buildPutRequest(), PostDeviceResponseTO::class.java)
                .blockingFirst()

            response.status shouldBe HttpStatus.NO_CONTENT

            verify {
                mockedDeviceAdapter.removeDeviceId(firstActiveRegisteredDevice)
                mockedDeviceAdapter.removeDeviceId(secondActiveRegisteredDevice)
            }

            with(repository.getDeviceOrNull(DeviceFingerprint(deviceFingerprint), accountId)) {
                shouldNotBeNull()
                status shouldBe DeviceStatus.ACTIVE
            }

            with(repository.getDeviceOrNull(firstFingerprint, accountId)) {
                shouldNotBeNull()
                status shouldBe DeviceStatus.INACTIVE
            }

            with(repository.getDeviceOrNull(secondFingerprint, accountId)) {
                shouldNotBeNull()
                status shouldBe DeviceStatus.INACTIVE
            }
        }

        private fun buildPutRequest(details: MobileDeviceDetails = mobileDeviceDetails, request: PutDeviceRequestTO = putRequest): HttpRequest<*> = HttpRequest.PUT("/device", request)
            .header("x-device-fingerprint", Base64.encode(getObjectMapper().writeValueAsString(details)))
            .cookie(securityFixture.cookieAuthOwner)
    }

    companion object {
        @JvmStatic
        @Suppress("unused")
        fun livenessErrors() = listOf(
            LivenessErrors.EnrollmentUnavailable,
            LivenessErrors.MatchUnavailable,
            LivenessErrors.AccountNotFound,
            LivenessErrors.DuplicationCheckUnavailable,
            LivenessErrors.Error(Exception()),
        ).toArgumentsStream()
    }
}