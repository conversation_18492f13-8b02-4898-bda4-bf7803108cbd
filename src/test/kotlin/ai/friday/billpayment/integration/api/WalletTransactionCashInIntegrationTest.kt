package ai.friday.billpayment.integration.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.CASHIN_TRANSACTIONID
import ai.friday.billpayment.adapters.api.CashInRequestTO
import ai.friday.billpayment.adapters.api.TransactionPaymentMethodTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardBrand
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.bill.billLockProvider
import ai.friday.billpayment.app.cashIn.CashInExecutor
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.buildCookie
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.createLockTable
import ai.friday.billpayment.integration.toMember
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.context.annotation.Property
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.mockk
import jakarta.inject.Named
import java.math.BigInteger
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

@MicronautTest(environments = [FRIDAY_ENV])
class WalletTransactionCashInIntegrationTest(
    embeddedServer: EmbeddedServer,
    val dynamoDB: AmazonDynamoDB,
    @Named(billLockProvider) val lockProvider: InternalLock,
    val updateBillService: UpdateBillService,
) {

    private val client: RxHttpClient =
        embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val enhancedClient = getDynamoDB()

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletRepository: WalletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = accountRepository,
    )

    @MockBean(CashInExecutor::class)
    fun getCashInExecutorMock() = cashInExecutor
    private val cashInExecutor: CashInExecutor = mockk(relaxUnitFun = true)

    private fun buildCashInRequest(
        cashInRequestTO: CashInRequestTO,
        member: Member,
    ): MutableHttpRequest<CashInRequestTO> {
        return HttpRequest.POST("/transaction/cash-in", cashInRequestTO)
            .cookie(buildCookie(member))
            .header("X-API-VERSION", "2")
            .header("X-WALLET-ID", wallet.id.value)
    }

    private lateinit var wallet: Wallet

    @field:Property(name = "lock.tableName")
    lateinit var lockTableName: String

    lateinit var bankAccount: AccountPaymentMethod

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        createLockTable(dynamoDB, lockTableName)
        accountRepository.create(walletFixture.founderAccount)
        accountRepository.create(walletFixture.participantAccount)
        accountRepository.create(walletFixture.limitedParticipantAccount)
        accountRepository.create(walletFixture.ultraLimitedParticipantAccount)
        accountRepository.create(walletFixture.cantPayParticipantAccount)

        bankAccount = accountRepository.createAccountPaymentMethod(
            accountId = walletFixture.founderAccount.accountId,
            bankAccount = BankAccount(
                accountType = AccountType.CHECKING,
                bankNo = 1L,
                routingNo = 2L,
                accountNo = BigInteger("3"),
                accountDv = "X",
                document = DOCUMENT,
            ),
            position = 2,
        )
        wallet = walletFixture.buildWallet(
            otherMembers = listOf(
                walletFixture.participant,
                walletFixture.limitedParticipant,
                walletFixture.ultraLimitedParticipant,
                walletFixture.cantPayParticipant,
            ),
            accountPaymentMethodId = bankAccount.id,
        )
        walletRepository.save(wallet)
    }

    /*
        @ParameterizedTest
        @MethodSource("walletMembers")
        @Disabled
        fun `should return ACCEPTED on member cash-in request`(member: Member) {
            val creditCard = CreditCard(
                brand = CreditCardBrand.VISA,
                pan = "****************",
                expiryDate = "10/2020",
                binDetails = null,
                riskLevel = RiskLevel.LOW,
            )
            val creditCardPaymentMethodId = addCreditCard(member.accountId, creditCard)

            val cashInRequestTO = CashInRequestTO(
                id = CASHIN_TRANSACTIONID.toString(),
                paymentMethod = TransactionPaymentMethodTO(
                    id = creditCardPaymentMethodId.value,
                    type = PaymentMethodType.CREDIT_CARD,
                ),
                netAmount = 101,
                feeAmount = 0,
            )

            val response = client.exchange(buildCashInRequest(cashInRequestTO, member)).blockingSingle()
            val expectedTransactionId = TransactionId.build(cashInRequestTO.id)

            response.status shouldBe HttpStatus.ACCEPTED

            val transaction = transactionRepository.findById(expectedTransactionId)

            with(transaction) {
                status shouldBe TransactionStatus.PROCESSING
                type shouldBe TransactionType.CASH_IN
                actionSource shouldBe ActionSource.Api(accountId = member.accountId)
                id shouldBe expectedTransactionId
                nsu shouldBe 0L
                payer shouldBe Payer(accountId = member.accountId, document = member.document, name = member.name)
                paymentData.toSingle().accountPaymentMethod.id.value shouldBe cashInRequestTO.paymentMethod.id
                paymentData.toSingle().accountPaymentMethod.method.type shouldBe PaymentMethodType.CREDIT_CARD
                (paymentData.toSingle().accountPaymentMethod.method as CreditCard).brand shouldBe "Visa"
                paymentData.toSingle().payment shouldBe null
                settlementData.totalAmount shouldBe cashInRequestTO.netAmount + cashInRequestTO.feeAmount
                settlementData.getTarget<CreditCardCashIn>().bankAccount shouldBe bankAccount
                settlementData.settlementOperation shouldBe null
            }
            verify { cashInExecutor.execute(any()) }
        }
    */

    @Test
    fun `should return FORBIDDEN on non participant cash-in request`() {
        val creditCard = CreditCard(
            brand = CreditCardBrand.VISA,
            pan = "****************",
            expiryDate = "10/2020",
            binDetails = null,
            riskLevel = RiskLevel.LOW,
        )
        val creditCardPaymentMethodId = addCreditCard(AccountId(ACCOUNT_ID_2), creditCard)
        val cashInRequestTO = CashInRequestTO(
            id = CASHIN_TRANSACTIONID.toString(),
            paymentMethod = TransactionPaymentMethodTO(
                id = creditCardPaymentMethodId.value,
                type = PaymentMethodType.CREDIT_CARD,
            ),
            netAmount = 1,
            feeAmount = 0,
        )

        val response = assertThrows<HttpClientResponseException> {
            client.exchange(
                buildCashInRequest(
                    cashInRequestTO,
                    ACCOUNT.copy(accountId = AccountId(ACCOUNT_ID_2))
                        .toMember(MemberType.COLLABORATOR, MemberPermissions.of(MemberType.COLLABORATOR)),
                ),
            ).blockingSingle()
        }

        response.status shouldBe HttpStatus.FORBIDDEN
    }

    @ParameterizedTest
    @MethodSource("walletMembers")
    open fun `should return bad request when credit card is not active`(member: Member) {
        val creditCard = CreditCard(
            brand = CreditCardBrand.VISA,
            pan = "****************",
            expiryDate = "10/2020",
            binDetails = null,
            riskLevel = RiskLevel.LOW,
        )
        val creditCardPaymentMethodId =
            addCreditCard(member.accountId, creditCard, status = AccountPaymentMethodStatus.PENDING)

        val cashInRequestTO = CashInRequestTO(
            id = CASHIN_TRANSACTIONID.toString(),
            paymentMethod = TransactionPaymentMethodTO(
                id = creditCardPaymentMethodId.value,
                type = PaymentMethodType.CREDIT_CARD,
            ),
            netAmount = 101,
            feeAmount = 0,
        )

        val response = assertThrows<HttpClientResponseException> {
            client.exchange(buildCashInRequest(cashInRequestTO, member)).blockingSingle()
        }

        response.status shouldBe HttpStatus.BAD_REQUEST
    }

    private fun addCreditCard(
        accountId: AccountId,
        creditCard: CreditCard,
        status: AccountPaymentMethodStatus = AccountPaymentMethodStatus.ACTIVE,
    ) =
        accountRepository.createAccountPaymentMethod(
            accountId = accountId,
            creditCard = creditCard,
            position = 1,
            status = status,
        ).id

    companion object {

        val walletFixture = WalletFixture()

        @JvmStatic
        fun walletMembers(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(walletFixture.founder),
                Arguments.arguments(walletFixture.participant),
                Arguments.arguments(walletFixture.limitedParticipant),
                Arguments.arguments(walletFixture.ultraLimitedParticipant),
                Arguments.arguments(walletFixture.cantPayParticipant),
            )
        }
    }
}