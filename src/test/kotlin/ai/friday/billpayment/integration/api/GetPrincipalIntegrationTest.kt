package ai.friday.billpayment.integration.api

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.adapters.api.GuestPrincipalTO
import ai.friday.billpayment.adapters.api.OwnerPrincipalTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DeviceFingerprintDbRepository
import ai.friday.billpayment.adapters.dynamodb.DeviceFingerprintDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.FeaturesDbRepository
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.SignInPendingAction
import ai.friday.billpayment.app.account.UpgradeStatus
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.feature.FeaturesRepository
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintAdapter
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintFixture
import ai.friday.billpayment.app.integrations.MaintenanceServicesEnum
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.NAME
import ai.friday.billpayment.integration.SecurityFixture
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.integration.loadBalancePaymentMethod
import ai.friday.billpayment.integration.loadLoginIntoDb
import ai.friday.billpayment.integration.loadPartialAccountIntoDb
import ai.friday.billpayment.integration.toSignedCookie
import ai.friday.billpayment.integration.withAudience
import ai.friday.billpayment.integration.withClaim
import ai.friday.billpayment.integration.withExpiresAt
import ai.friday.billpayment.integration.withIssuer
import ai.friday.billpayment.integration.withSubject
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.nimbusds.jwt.JWTClaimsSet
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpRequest
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import java.time.Duration
import java.util.Date
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

@MicronautTest(environments = [FRIDAY_ENV])
class GetPrincipalIntegrationTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {
    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val accountDAO = AccountDynamoDAO(dynamoDbEnhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(dynamoDbEnhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(dynamoDbEnhancedClient)
    private val nsuDAO = NSUDynamoDAO(dynamoDbEnhancedClient)
    private val transactionDynamo = TransactionDynamo(dynamoDbEnhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )
    private val deviceFingerprintRepository = DeviceFingerprintDbRepository(DeviceFingerprintDynamoDAO(dynamoDbEnhancedClient))

    private val securityFixture = SecurityFixture()
    private val walletFixture = WalletFixture()

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val featuresRepository = mockk<FeaturesRepository>(relaxed = true)

    @MockBean(FeaturesDbRepository::class)
    fun featureRepositoryMock() = featuresRepository

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService

    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @MockBean(DeviceFingerprintAdapter::class)
    fun deviceFingerprintAdapter() = deviceFingerprintAdapter

    private val deviceFingerprintAdapter: DeviceFingerprintAdapter = mockk(relaxed = true)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `should return principal on guest principal`() {
        val expectedName = "Fulano2"
        loadPartialAccountIntoDb(dynamoDB, name = expectedName, email = "<EMAIL>")
        fetchGuestPrincipal(securityFixture.cookieGuest).run {
            assertSoftly {
                accountId shouldBe ACCOUNT_ID
                role shouldBe Role.GUEST.name
                nickname shouldBe expectedName
                registrationType shouldBe "FULL"
                subscriptionType shouldBe SubscriptionType.PIX
            }
        }
    }

    @Test
    fun `should return principal on guest from msisdn authentication when user is guest`() {
        val expectedName = "Fulano2"
        val issuer = "https://friday.ai"
        val audience = "friday.ai"
        val cookie =
            JWTClaimsSet.Builder().withSubject(ACCOUNT_ID)
                .withIssuer(issuer)
                .withAudience(audience)
                .withClaim("email", "<EMAIL>")
                .withClaim("actualRole", Role.GUEST.name)
                .withExpiresAt(
                    Date.from(
                        getZonedDateTime()
                            .plus(Duration.ofMinutes(5)).toInstant(),
                    ),
                ).toSignedCookie()
        loadPartialAccountIntoDb(dynamoDB, name = expectedName, email = "<EMAIL>")
        fetchGuestPrincipal(cookie).run {
            assertSoftly {
                accountId shouldBe ACCOUNT_ID
                role shouldBe Role.GUEST.name
                nickname shouldBe expectedName
                subscriptionType shouldBe SubscriptionType.PIX
            }
        }
    }

    @Test
    fun `deve retornar principal como OWNER e PASSWORD_AUTHENTICATION_REQUIRED no login com msisdn para usuário com senha criada`() {
        val expectedEmail = "<EMAIL>"
        val expectedName = "Fulano2"
        val issuer = "https://friday.ai"
        val audience = "friday.ai"
        val cookie =
            JWTClaimsSet.Builder().withSubject(ACCOUNT_ID)
                .withIssuer(issuer)
                .withAudience(audience)
                .withClaim("email", "<EMAIL>")
                .withClaim("actualRole", Role.OWNER.name)
                .withClaim("msisdn:migrated", true)
                .withExpiresAt(
                    Date.from(
                        getZonedDateTime()
                            .plus(Duration.ofMinutes(5)).toInstant(),
                    ),
                ).toSignedCookie()
        loadAccountIntoDb(dynamoDB, name = expectedName, email = expectedEmail, defaultWalletId = WalletId(WALLET_ID))
        fetchOwnerPrincipal(cookie).run {
            assertSoftly {
                accountId shouldBe ACCOUNT_ID
                role shouldBe Role.OWNER.name
                nickname shouldBe expectedName
                migrated shouldBe true
                pendingAction shouldBe SignInPendingAction.PASSWORD_AUTHENTICATION_REQUIRED.name
                subscriptionType shouldBe SubscriptionType.PIX
            }
        }
    }

    @Test
    fun `deve retornar principal como OWNER e CREATE_PASSWORD_REQUIRED no login com msisdn para usuário sem senha criada`() {
        val expectedEmail = "<EMAIL>"
        val expectedName = "Fulano2"
        val issuer = "https://friday.ai"
        val audience = "friday.ai"
        val cookie =
            JWTClaimsSet.Builder().withSubject(ACCOUNT_ID)
                .withIssuer(issuer)
                .withAudience(audience)
                .withClaim("email", "<EMAIL>")
                .withClaim("actualRole", Role.OWNER.name)
                .withClaim("msisdn:migrated", false)
                .withExpiresAt(
                    Date.from(
                        getZonedDateTime()
                            .plus(Duration.ofMinutes(5)).toInstant(),
                    ),
                ).toSignedCookie()
        loadAccountIntoDb(dynamoDB, name = expectedName, email = expectedEmail, defaultWalletId = WalletId(WALLET_ID))
        fetchOwnerPrincipal(cookie).run {
            assertSoftly {
                accountId shouldBe ACCOUNT_ID
                role shouldBe Role.OWNER.name
                nickname shouldBe expectedName
                migrated shouldBe false
                pendingAction shouldBe SignInPendingAction.CREATE_PASSWORD_REQUIRED.name
                subscriptionType shouldBe SubscriptionType.PIX
            }
        }
    }

    @Test
    fun `deve retornar principal como OWNER e sem pendingAction no login com senha em device conhecido`() {
        val currentName = "name on database"
        val currentEmail = "email on database"
        val role = Role.OWNER

        loadLoginIntoDb(dynamoDB)
        loadBalancePaymentMethod(accountRepository)
        loadAccountIntoDb(dynamoDB, name = currentName, email = currentEmail, defaultWalletId = WalletId(WALLET_ID))

        deviceFingerprintRepository.save(accountId = AccountId(ACCOUNT_ID), device = DeviceFingerprintFixture.anActiveDevice.registeredDevice)

        fetchOwnerPrincipal(
            securityFixture.cookieAuthOwner,
            deviceFingerprint = DeviceFingerprintFixture.anActiveDevice.HEADER,
        ).run {
            accountId shouldBe ACCOUNT_ID
            role shouldBe role
            nickname shouldBe currentName
            pendingAction shouldBe null
            subscriptionType shouldBe SubscriptionType.PIX
        }
    }

    @Test
    fun `deve retornar principal como OWNER e com pendingAction no login com senha em device desconhecido`() {
        val currentName = "name on database"
        val currentEmail = "email on database"
        val role = Role.OWNER

        loadLoginIntoDb(dynamoDB)
        loadBalancePaymentMethod(accountRepository)
        loadAccountIntoDb(dynamoDB, name = currentName, email = currentEmail, defaultWalletId = WalletId(WALLET_ID))

        fetchOwnerPrincipal(
            securityFixture.cookieAuthOwner,
            deviceFingerprint = DeviceFingerprintFixture.anActiveDevice.HEADER,
        ).run {
            accountId shouldBe ACCOUNT_ID
            role shouldBe role
            nickname shouldBe currentName
            pendingAction shouldBe SignInPendingAction.DEVICE_BINDING_REQUIRED.name
            subscriptionType shouldBe SubscriptionType.PIX
        }
    }

    @Test
    fun `deve retornar o principal como OWNER e sem pendingAction no login com senha sem informar o device`() {
        val currentName = "name on database"
        val currentEmail = "email on database"
        val role = Role.OWNER

        loadLoginIntoDb(dynamoDB)
        loadBalancePaymentMethod(accountRepository)
        loadAccountIntoDb(dynamoDB, name = currentName, email = currentEmail, defaultWalletId = WalletId(WALLET_ID))

        fetchOwnerPrincipal(
            securityFixture.cookieAuthOwner,
            deviceFingerprint = null,
        ).run {
            accountId shouldBe ACCOUNT_ID
            role shouldBe role
            nickname shouldBe currentName
            pendingAction shouldBe null
            subscriptionType shouldBe SubscriptionType.PIX
        }
    }

    private val wallet = walletFixture.buildWallet()

    @Test
    fun `should return principal for owner with only 1 wallet`() {
        loadAccountIntoDb(dynamoDB, defaultWalletId = wallet.id)

        fetchOwnerPrincipal(securityFixture.cookieAuthOwner).run {
            accountId shouldBe ACCOUNT_ID
            role shouldBe Role.OWNER.name
            nickname shouldBe NAME
            migrated shouldBe null
            subscriptionType shouldBe SubscriptionType.PIX
        }
    }

    @Test
    fun `should return principal with empty groups for account owner without groups`() {
        loadLoginIntoDb(amazonDynamoDB = dynamoDB)
        loadAccountIntoDb(dynamoDB, defaultWalletId = wallet.id)

        fetchOwnerPrincipal(securityFixture.cookieAuthOwner).run {
            groups shouldHaveSize 0
        }
    }

    @Test
    fun `deve devolver uma lista de servicos em manutencao`() {
        loadLoginIntoDb(amazonDynamoDB = dynamoDB)
        loadAccountIntoDb(dynamoDB, defaultWalletId = wallet.id)
        every { featuresRepository.getAll().maintenanceServices } returns listOf(MaintenanceServicesEnum.BOLETO)

        fetchOwnerPrincipal(securityFixture.cookieAuthOwner).run {
            maintenanceServices.shouldContain("BOLETO")
        }
    }

    @Test
    fun `should return principal groups for account owner`() {
        loadLoginIntoDb(amazonDynamoDB = dynamoDB)
        loadAccountIntoDb(dynamoDB, defaultWalletId = wallet.id, groups = listOf("alpha", "beta"))

        fetchOwnerPrincipal(securityFixture.cookieAuthOwner).run {
            groups shouldContainExactly listOf("alpha", "beta")
        }
    }

    @Test
    fun `should return principal groups without version restrictions for account owner`() {
        loadLoginIntoDb(amazonDynamoDB = dynamoDB)
        loadAccountIntoDb(dynamoDB, defaultWalletId = wallet.id, groups = listOf("alpha", "beta", "multi-select-bills"))

        /* fetchOwnerPrincipal(securityFixture.cookieAuthOwner, "11.0.0", "android").run {
             groups shouldContainExactly listOf("alpha", "beta")
         }
         */
        fetchOwnerPrincipal(securityFixture.cookieAuthOwner, "11.0.158", "android").run {
            groups shouldContainExactly listOf("alpha", "beta", "multi-select-bills")
        }
    }

    @Test
    fun `deve retornar o status do upgrade completo para um full account`() {
        loadLoginIntoDb(amazonDynamoDB = dynamoDB)
        loadAccountIntoDb(dynamoDB, defaultWalletId = wallet.id)

        fetchOwnerPrincipal(securityFixture.cookieAuthOwner).run {
            upgradeStatus shouldBe UpgradeStatus.COMPLETED.name
        }
    }

    @Test
    fun `deve retornar o status do upgrade incompleto para um basic account com upgrade em andamento`() {
        loadLoginIntoDb(amazonDynamoDB = dynamoDB)
        loadAccountIntoDb(
            dynamoDB,
            defaultWalletId = wallet.id,
            type = UserAccountType.BASIC_ACCOUNT,
            upgradeStatus = UpgradeStatus.INCOMPLETE.name,
        )

        fetchOwnerPrincipal(securityFixture.cookieAuthOwner).run {
            upgradeStatus shouldBe UpgradeStatus.INCOMPLETE.name
        }
    }

    @Test
    fun `deve retornar o status do upgrade em revisao para um basic account com upgrade em revisao`() {
        loadLoginIntoDb(amazonDynamoDB = dynamoDB)
        loadAccountIntoDb(dynamoDB, defaultWalletId = wallet.id, upgradeStatus = UpgradeStatus.UNDER_REVIEW.name)

        fetchOwnerPrincipal(securityFixture.cookieAuthOwner).run {
            upgradeStatus shouldBe UpgradeStatus.UNDER_REVIEW.name
        }
    }

    private fun fetchGuestPrincipal(cookie: Cookie): GuestPrincipalTO {
        return client.toBlocking().retrieve(
            HttpRequest.GET<GuestPrincipalTO>("/principal")
                .cookie(cookie)
                .header("X-API-VERSION", "2"),
            GuestPrincipalTO::class.java,
        )
    }

    private fun fetchOwnerPrincipal(
        cookie: Cookie,
        appVersion: String? = null,
        appPlatform: String? = null,
        deviceFingerprint: String? = null,
    ): OwnerPrincipalTO {
        val headers = mutableMapOf<CharSequence, CharSequence>("X-API-VERSION" to "2")

        appVersion?.let { headers.put("X-FRIDAY-VERSION", it) }
        appPlatform?.let { headers.put("X-FRIDAY-PLATFORM", it) }
        deviceFingerprint?.let { headers.put("x-device-fingerprint", it) }

        return client.toBlocking().retrieve(
            HttpRequest.GET<OwnerPrincipalTO>("/principal")
                .cookie(cookie)
                .headers(headers),
            OwnerPrincipalTO::class.java,
        )
    }
}