/*
package ai.friday.billpayment.integration.api

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.api.AmountBalanceTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.LoginDbRepository
import ai.friday.billpayment.adapters.modatta.api.CashoutTO
import ai.friday.billpayment.adapters.modatta.api.ModattaAmountBalanceTO
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.CreditCardConfiguration
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.account.LegacyAccountConfiguration
import ai.friday.billpayment.app.account.NotificationGateways
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.balance.Balance
import ai.friday.billpayment.app.banking.FridayBankAccountError
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.app.modatta.ModattaBalance
import ai.friday.billpayment.app.modatta.ModattaCorpError
import ai.friday.billpayment.app.modatta.UserId
import ai.friday.billpayment.app.modatta.integrations.FridayBalanceService
import ai.friday.billpayment.app.modatta.integrations.ModattaBackendAdapter
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.bankAccount
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.buildModattaCookie
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@Disabled
@MicronautTest(environments = [MODATTA_ENV, "stgmodatta"])
class ModattaGetPartnerBalanceIntegrationTest(
    embeddedServer: EmbeddedServer,
    val dynamoDB: AmazonDynamoDB,
) {
    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val accountRepository = AccountDbRepository(dynamoDbDAO)
    private val loginRepository = LoginDbRepository(dynamoDbDAO)

    private val userId = UserId("MODATTA-USER-1")

    private val externalId = ExternalId(ACCOUNT_ID_2, AccountProviderName.FRIDAY)

    private val modattaAdapterMock = mockk<ModattaBackendAdapter>(relaxed = true)

    private val fridayBalanceAdapter = mockk<FridayBalanceService>(relaxed = true)

    @MockBean(FridayBalanceService::class)
    fun partnerBalanceService() = partnerBalanceService
    private val partnerBalanceService: FridayBalanceService = fridayBalanceAdapter

    @MockBean(ModattaBackendAdapter::class)
    fun modattaCorpAdapter() = modattaCorpAdapter
    private val modattaCorpAdapter: ModattaBackendAdapter = modattaAdapterMock

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Nested
    @DisplayName("ao consultar o saldo modatta")
    inner class ModattaBalanceTest {

        @Test
        fun `deve retornar o saldo e o valor mínimo de resgate`() {
            every {
                modattaAdapterMock.getBalance(any())
            } returns ModattaBalance(amount = 500L, minRedeemAmount = 10L).right()

            val cookie = buildModattaCookie(userId)

            val response = fetchModattaBalance(cookie)

            val body = response.body.get()

            body.amount shouldBe 500L
            body.minRedeemAmount shouldBe 10L
        }

        @Test
        fun `deve retornar erro caso haja algum erro na resposta da api modatta`() {
            every {
                modattaAdapterMock.getBalance(any())
            } returns ModattaCorpError.ServerError(Exception("")).left()

            val cookie = buildModattaCookie(userId)

            val response = assertThrows<HttpClientResponseException> { fetchModattaBalance(cookie) }

            response.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        }

        private fun fetchModattaBalance(cookie: Cookie) =
            client
                .exchange(
                    buildModattaBalanceHttpRequest(cookie = cookie),
                    Argument.of(ModattaAmountBalanceTO::class.java),
                    Argument.of(ResponseTO::class.java),
                ).firstOrError().blockingGet()

        private fun buildModattaBalanceHttpRequest(cookie: Cookie) =
            HttpRequest.GET<ModattaAmountBalanceTO>("/balance/amount")
                .cookie(cookie)
    }

    @Nested
    @DisplayName("ao fazer o resgate do saldo")
    inner class CashOutTest {

        @Test
        fun `deve retornar ok se o resgate for feito com sucesso`() {
            createOwner()

            every {
                fridayBalanceAdapter.getCashoutBankAccount(any())
            } returns bankAccount.right()

            val cookie = buildModattaCookie(userId)

            val body = CashoutTO(requestId = "123")
            val response = cashOut(cookie, body)

            response.status shouldBe HttpStatus.OK
        }

        @Test
        fun `deve retornar erro caso não consiga fazer o resgate`() {
            createOwner()

            every {
                fridayBalanceAdapter.getCashoutBankAccount(any())
            } returns FridayBankAccountError.FridayAccountNotFound.left()

            val cookie = buildModattaCookie(userId)

            val body = CashoutTO(requestId = "123")
            val response = assertThrows<HttpClientResponseException> { cashOut(cookie, body) }

            response.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        }

        private fun cashOut(cookie: Cookie, body: CashoutTO) =
            client
                .exchange(
                    buildCashOutHttpRequest(cookie = cookie, body),
                    Argument.of(CashoutTO::class.java),
                    Argument.of(ResponseTO::class.java),
                ).firstOrError().blockingGet()

        private fun buildCashOutHttpRequest(cookie: Cookie, body: CashoutTO) =
            HttpRequest.POST("/balance/cashout", body)
                .cookie(cookie)
    }

    @Nested
    @DisplayName("ao consultar o saldo na Friday")
    inner class FridayMePoupeAmountTest {

        @Test
        fun `deve retornar saldo 0 quando o cadastro do usuário estiver incompleto`() {
            loadAccountIntoDb(
                accountId = userId.toAccountId(),
                amazonDynamoDB = dynamoDB,
                status = "REGISTER_INCOMPLETE",
            )
            val cookie = buildModattaCookie(userId)

            val response = fetchPartnerAmountBalance(cookie)

            response.status shouldBe HttpStatus.OK
            response.body().amount shouldBe 0L
            verify {
                partnerBalanceService wasNot called
            }
        }

        @Test
        fun `deve retornar o saldo externo quando usuário for OWNER e existir`() {
            createOwner()

            val partnerBalance = Balance(100L)
            every {
                partnerBalanceService.getBalance(externalId)
            } returns partnerBalance.right()

            val cookie = buildModattaCookie(userId)

            val response = fetchPartnerAmountBalance(cookie)

            response.status shouldBe HttpStatus.OK
            response.body().amount shouldBe partnerBalance.amount
        }

        @Test
        fun `deve retornar BAD_REQUEST quando o usuario for OWNER e não existir, `() {
            createOwner()

            every {
                partnerBalanceService.getBalance(externalId)
            } returns FridayBankAccountError.FridayAccountNotFound.left()

            val cookie = buildModattaCookie(userId)

            val result = assertThrows<HttpClientResponseException> {
                fetchPartnerAmountBalance(cookie)
            }

            result.status shouldBe HttpStatus.BAD_REQUEST
            with(result.response.getBody(ResponseTO::class.java).get()) {
                this.code shouldBe "4001"
                this.message shouldBe "FridayAccountNotFound"
            }
        }

        @Test
        fun `deve retornar BAD_REQUEST quando o usuario for OWNER e a conta estiver encerrada`() {
            createOwner()

            every {
                partnerBalanceService.getBalance(externalId)
            } returns FridayBankAccountError.FridayAccountClosed.left()

            val cookie = buildModattaCookie(userId)

            val result = assertThrows<HttpClientResponseException> {
                fetchPartnerAmountBalance(cookie)
            }

            result.status shouldBe HttpStatus.BAD_REQUEST
            with(result.response.getBody(ResponseTO::class.java).get()) {
                this.code shouldBe "4002"
                this.message shouldBe "FridayAccountClosed"
            }
        }

        @Test
        fun `deve retornar BAD_GATEWAY quando o usuario for OWNER e nao conseguir consultar o saldo`() {
            createOwner()

            every {
                partnerBalanceService.getBalance(externalId)
            } returns FridayBankAccountError.ServerError(NoStackTraceException("message")).left()

            val cookie = buildModattaCookie(userId)

            val result = assertThrows<HttpClientResponseException> {
                fetchPartnerAmountBalance(cookie)
            }

            result.status shouldBe HttpStatus.BAD_GATEWAY
            with(result.response.getBody(ResponseTO::class.java).get()) {
                this.code shouldBe "5001"
                this.message shouldBe "message"
            }
        }

        private fun fetchPartnerAmountBalance(cookie: Cookie) =
            client
                .exchange(
                    buildPartnerAmountBalanceHttpRequest(cookie = cookie),
                    Argument.of(AmountBalanceTO::class.java),
                    Argument.of(ResponseTO::class.java),
                ).firstOrError().blockingGet()

        private fun buildPartnerAmountBalanceHttpRequest(cookie: Cookie) =
            HttpRequest.GET<AmountBalanceTO>("/balance/partnerAmount")
                .cookie(cookie)
    }

    private fun createOwner() {
        val accountId = userId.toAccountId()
        loginRepository.createLogin(
            providerUser = ProviderUser(
                id = userId.value.removePrefix("ACCOUNT-"),
                providerName = ProviderName.MODATTA,
                username = "Modata user name",
                emailAddress = EmailAddress("<EMAIL>"),
            ),
            id = accountId,
            role = Role.OWNER,
        )
        accountRepository.create(
            Account(
                accountId = accountId,
                name = "Modata user name",
                emailAddress = EmailAddress("<EMAIL>"),
                document = DOCUMENT,
                documentType = "CPF",
                mobilePhone = "*************",
                created = getZonedDateTime(),
                activated = null,
                status = AccountStatus.ACTIVE,
                configuration = LegacyAccountConfiguration(
                    accountId = null,
                    creditCardConfiguration = CreditCardConfiguration(
                        quota = 0,
                    ),
                    defaultWalletId = null,
                    receiveDDANotification = false,
                    receiveNotification = false,
                    accessToken = null,
                    refreshToken = null,
                    externalId = externalId,
                    groups = listOf(),
                    notificationGateway = NotificationGateways.WHATSAPP,
                ),
                firstLoginAsOwner = null,
                channel = null,
                imageUrlSmall = null,
                imageUrlLarge = null,
                subscriptionType = SubscriptionType.PIX,
            ),
        )
    }
}*/