package ai.friday.billpayment.integration.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.api.BillTO
import ai.friday.billpayment.adapters.api.EntriesTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.addBillIntoDb
import ai.friday.billpayment.integration.buildCookie
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import java.time.LocalDate
import java.time.ZonedDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

@MicronautTest(environments = [FRIDAY_ENV])
class GetSubscriptionIntegrationTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val enhancedClient = getDynamoDB()

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = mockk(),
    )

    private val walletFixture = WalletFixture()

    private val wallet = walletFixture.buildPrimaryWallet(
        founderAccount = walletFixture.founderAccount,
    )

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk {
        every {
            findWalletCategories(any())
        } returns emptyList()
    }

    @MockBean(ManualEntryService::class)
    fun manualEntryService() = manualEntryService
    private val manualEntryService: ManualEntryService = mockk {
        every { listAllForWalletMember(any(), any()) } returns emptyList()
    }

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        accountRepository.create(walletFixture.founderAccount)
        walletRepository.save(wallet)
    }

    @Test
    fun `não deve exibir assinatura Friday a partir de 31 dias`() {
        addBillIntoDb(enhancedClient, getActiveBill(walletId = wallet.id, billId = BillId(), subscriptionFee = true, dueDate = LocalDate.of(2022, 1, 10)))
        addBillIntoDb(enhancedClient, getActiveBill(walletId = wallet.id, billId = BillId(), dueDate = LocalDate.of(2022, 1, 15)))
        addBillIntoDb(enhancedClient, getActiveBill(walletId = wallet.id, billId = BillId(), dueDate = LocalDate.of(2022, 2, 15)))
        addBillIntoDb(enhancedClient, getActiveBill(walletId = wallet.id, billId = BillId(), subscriptionFee = true, dueDate = LocalDate.of(2022, 2, 10)))

        val bills = withGivenDateTime(ZonedDateTime.of(2022, 1, 9, 0, 0, 0, 0, brazilTimeZone)) {
            retrieveBills(walletFixture.founderAccount)
        }

        bills.first { it.subscriptionFee }.dueDate shouldBe "2022-01-10"
        bills.size shouldBe 3
    }

    @Test
    fun `deve exibir as assinaturas Friday até o dia de hoje + 31 dias`() {
        addBillIntoDb(enhancedClient, getActiveBill(walletId = wallet.id, billId = BillId(), subscriptionFee = true, dueDate = LocalDate.of(2022, 1, 10)))
        addBillIntoDb(enhancedClient, getActiveBill(walletId = wallet.id, billId = BillId(), dueDate = LocalDate.of(2022, 1, 15)))
        addBillIntoDb(enhancedClient, getActiveBill(walletId = wallet.id, billId = BillId(), dueDate = LocalDate.of(2022, 2, 15)))
        addBillIntoDb(enhancedClient, getActiveBill(walletId = wallet.id, billId = BillId(), subscriptionFee = true, dueDate = LocalDate.of(2022, 2, 10)))

        val bills = withGivenDateTime(ZonedDateTime.of(2022, 1, 10, 0, 0, 0, 0, brazilTimeZone)) {
            retrieveBills(walletFixture.founderAccount)
        }

        bills.size shouldBe 4
    }

    private fun retrieveBills(account: Account): List<BillTO> {
        val cookie = buildCookie(account)

        val request = HttpRequest
            .GET<EntriesTO>("/entries")
            .cookie(cookie)
            .header("X-API-VERSION", "2")
            .header("X-WALLET-ID", wallet.id.value)

        return client.toBlocking()
            .retrieve(
                request,
                Argument.of(EntriesTO::class.java),
            ).bills
    }
}