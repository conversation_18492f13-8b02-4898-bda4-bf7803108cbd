package ai.friday.billpayment.integration.api

import ai.friday.billpayment.adapters.api.AmountBalanceTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.arbi.ArbiAdapterException
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.balance.Balance
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.integration.WalletFixture
import io.kotest.matchers.shouldBe
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
class GetB2BBalanceIntegrationTest(
    embeddedServer: EmbeddedServer,
) {

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @field:Property(name = "modatta-b2b.identity")
    lateinit var modattaB2BIdentity: String

    @field:Property(name = "modatta-b2b.secret")
    lateinit var modattaB2BSecret: String

    @MockBean(AccountService::class)
    fun accountService() = accountService
    private val accountService: AccountService = mockk()

    @MockBean(WalletService::class)
    fun walletService() = walletService
    private val walletService: WalletService = mockk()

    @MockBean(BalanceService::class)
    fun balanceService() = balanceService
    private val balanceService: BalanceService = mockk()

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    @Test
    fun `quando a credencial for invalida deve retornar UNAUTHORIZED`() {
        val request = HttpRequest.GET<AmountBalanceTO>(
            "/b2b/balance/amount/ACCOUNT-123",
        ).basicAuth(modattaB2BIdentity, "wrong-password")

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.UNAUTHORIZED
    }

    @Test
    fun `quando o accountId nao existe deve retornar NOT_FOUND`() {
        val accountId = AccountId("ACCOUNT-NOT-FOUND")

        every {
            accountService.findAccountById(accountId)
        } throws AccountNotFoundException(accountId.value)

        val request = HttpRequest.GET<AmountBalanceTO>(
            "/b2b/balance/amount/${accountId.value}",
        ).basicAuth(modattaB2BIdentity, modattaB2BSecret)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND

        with(thrown.response.getBody(ResponseTO::class.java).get()) {
            code shouldBe "404"
            message shouldBe "Account ACCOUNT-NOT-FOUND not found"
        }
    }

    @Test
    fun `quando o accountId pertence a uma conta encerrada deve retornar BAD_REQUEST`() {
        val accountId = walletFixture.founderAccount.accountId

        every {
            accountService.findAccountById(accountId)
        } returns walletFixture.founderAccount.copy(status = AccountStatus.CLOSED)

        val request = HttpRequest.GET<AmountBalanceTO>(
            "/b2b/balance/amount/${accountId.value}",
        ).basicAuth(modattaB2BIdentity, modattaB2BSecret)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST

        with(thrown.response.getBody(ResponseTO::class.java).get()) {
            code shouldBe "4001"
            message shouldBe "Account closed"
        }
    }

    @Test
    fun `quando o accountId pertence a uma conta ativa mas nao consegue consultar o saldo no arbi deve retornar BAD_GATEWAY`() {
        val accountId = walletFixture.founderAccount.accountId

        every {
            accountService.findAccountById(accountId)
        } returns walletFixture.founderAccount

        every {
            walletService.findPrimaryWallet(accountId)
        } returns wallet

        every {
            balanceService.getBalanceFrom(wallet.founder.accountId, wallet.paymentMethodId)
        } throws ArbiAdapterException()

        val request = HttpRequest.GET<AmountBalanceTO>(
            "/b2b/balance/amount/${accountId.value}",
        ).basicAuth(modattaB2BIdentity, modattaB2BSecret)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_GATEWAY

        with(thrown.response.getBody(ResponseTO::class.java).get()) {
            code shouldBe "502"
        }
    }

    @Test
    fun `quando o accountId pertence a uma conta ativa deve retornar OK`() {
        val accountId = walletFixture.founderAccount.accountId

        every {
            accountService.findAccountById(accountId)
        } returns walletFixture.founderAccount

        every {
            walletService.findPrimaryWallet(accountId)
        } returns wallet

        val balance = Balance(amount = 100)
        every {
            balanceService.getBalanceFrom(wallet.founder.accountId, wallet.paymentMethodId)
        } returns balance

        val request = HttpRequest.GET<AmountBalanceTO>(
            "/b2b/balance/amount/${accountId.value}",
        ).basicAuth(modattaB2BIdentity, modattaB2BSecret)

        val response = client.toBlocking().exchange(request, Argument.of(AmountBalanceTO::class.java))

        response.status shouldBe HttpStatus.OK

        with(response.body.get()) {
            this.amount shouldBe balance.amount
        }
    }
}