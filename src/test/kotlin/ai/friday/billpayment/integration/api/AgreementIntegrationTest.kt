package ai.friday.billpayment.integration.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.accountRegisterDataMissingAcceptedAt
import ai.friday.billpayment.accountRegisterDataWithDocumentInfo
import ai.friday.billpayment.accountRegisterDataWithoutMonthlyIncomeAndAgreement
import ai.friday.billpayment.accountRegisterMissingOnlyAgreement
import ai.friday.billpayment.adapters.api.AccountRegisterDataTO
import ai.friday.billpayment.adapters.api.AgreementRequestTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.OriginalOcrAndPersonDataDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.intercom.IntercomAdapterException
import ai.friday.billpayment.adapters.s3.S3LinkGenerator
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.AgreementData
import ai.friday.billpayment.app.account.AgreementFilesService
import ai.friday.billpayment.app.account.PartialAccount
import ai.friday.billpayment.app.account.PepQuery
import ai.friday.billpayment.app.account.PepQueryResult
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.account.generateSignatureKey
import ai.friday.billpayment.app.account.toContractForm
import ai.friday.billpayment.app.chatbot.ChatbotNotificationService
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.integrations.RegisterInstrumentationService
import ai.friday.billpayment.app.liveness.LivenessEnrollmentVerification
import ai.friday.billpayment.app.liveness.LivenessService
import ai.friday.billpayment.app.notification.PushNotificationService
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.register.kyc.KycDossier
import ai.friday.billpayment.app.register.kyc.KycDossierMepQuery
import ai.friday.billpayment.app.register.kyc.KycDossierOfficialDocumentData
import ai.friday.billpayment.app.register.kyc.KycDossierTaxIdRegion
import ai.friday.billpayment.app.register.kyc.KycService
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.generateCookie
import ai.friday.billpayment.integration.loadPartialAccountIntoDb
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.equality.shouldBeEqualToIgnoringFields
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.string.shouldContain
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.PropertySource
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest.GET
import io.micronaut.http.HttpRequest.PUT
import io.micronaut.http.HttpStatus.BAD_REQUEST
import io.micronaut.http.HttpStatus.CONFLICT
import io.micronaut.http.HttpStatus.OK
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import io.via1.communicationcentre.app.ContractForm
import io.via1.communicationcentre.app.ContractSignature
import io.via1.communicationcentre.app.integrations.ContractPdfWriterService
import io.via1.communicationcentre.app.integrations.ContractWriterService
import io.via1.communicationcentre.app.integrations.NotificationSenderService
import jakarta.inject.Named
import java.time.Duration
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.UUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.ValueSource
import org.testcontainers.shaded.org.awaitility.Awaitility.await

private const val contractFileKey = "contractKey"
private const val declarationOfResidencyKey = "residencyKey"
private const val signUpTestMobilePhone = "+*************"

@PropertySource(
    value = [
        Property(name = "accountRegister.user_files.contractPrefix", value = contractFileKey),
        Property(name = "accountRegister.user_files.declarationOfResidencyPrefix", value = declarationOfResidencyKey),
        Property(name = "token.onboarding.testMobilePhones", value = signUpTestMobilePhone),
    ],
)
@MicronautTest(environments = [FRIDAY_ENV])
class AgreementIntegrationTest(
    embeddedServer: EmbeddedServer,
    val dynamoDB: AmazonDynamoDB,
) {
    private val enhancedClient = getDynamoDB()

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val accountRegisterDAO = AccountRegisterDynamoDAO(enhancedClient)
    private val originalOcrAndPersonDataDAO = OriginalOcrAndPersonDataDynamoDAO(enhancedClient)

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @MockBean(ContractPdfWriterService::class)
    fun contractPdfWriterService() = contractWriterService
    private val contractWriterService: ContractWriterService = mockk(relaxUnitFun = true)
    private val clientIP = "***********"

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @MockBean(AgreementFilesService::class)
    fun agreementFilesService() = agreementFilesService
    private val agreementFilesService: AgreementFilesService =
        spyk(
            AgreementFilesService(
                contractWriterService = contractWriterService,
                handlebarTemplateCompiler = mockk(),
                pdfConverter = mockk(),
            ),
        ) {
            every {
                createDeclarationOfResidency(any(), any(), any())
            } returns Unit
        }

    @Named("email")
    @MockBean(NotificationSenderService::class)
    fun notificationSenderService() = notificationSenderService
    private val notificationSenderService: NotificationSenderService = mockk(relaxUnitFun = true)

    @MockBean(NotificationAdapter::class)
    fun notificationAdapter() = notificationAdapter
    private val notificationAdapter: NotificationAdapter = mockk(relaxUnitFun = true)

    @MockBean(LivenessService::class)
    fun livenessAdapter() = livenessService
    private val livenessService: LivenessService = mockk {
        every { verifyDuplication(any()) } returns LivenessEnrollmentVerification().right()
    }

    @MockBean(ObjectRepository::class)
    fun objectRepository(): ObjectRepository = objectRepository
    private val objectRepository = mockk<ObjectRepository>(relaxUnitFun = true) {
        every {
            loadObject(any(), any())
        } returns java.io.InputStream.nullInputStream()
    }

    @MockBean(S3LinkGenerator::class)
    fun getS3LinkGenerator() = s3LinkGenerator
    private val s3LinkGenerator: S3LinkGenerator = mockk()

    @MockBean(KycService::class)
    fun getKycService() = kycService
    private val kycService: KycService = mockk()
    private val kycFileStoredObject = StoredObject("", "", "kyc-report.html")

    val contactId = UUID.randomUUID().toString()

    @MockBean(CrmService::class)
    fun getCRMService() = crmService
    private val crmService: CrmService = mockk()

    @MockBean(RegisterInstrumentationService::class)
    fun registerInstrumentationService(): RegisterInstrumentationService = registerInstrumentationService
    private val registerInstrumentationService: RegisterInstrumentationService = mockk(relaxUnitFun = true)

    @MockBean(ChatbotNotificationService::class)
    fun chatBotMessagePublisher(): ChatbotNotificationService = chatBotNotificationService
    private val chatBotNotificationService: ChatbotNotificationService = mockk(relaxUnitFun = true)

    @MockBean(PushNotificationService::class)
    fun pushNotificationSenderService() = pushNotificationService
    private val pushNotificationService: PushNotificationService = mockk(relaxUnitFun = true)

    private val accountRegisterRepository =
        AccountRegisterDbRepository(accountRegisterDAO, originalOcrAndPersonDataDAO, objectRepository)

    private lateinit var accountId: AccountId

    private fun buildPutAgreement(cookie: Cookie, agreed: Boolean) =
        PUT("/register/agreement", AgreementRequestTO(agreed))
            .cookie(cookie)
            .header("X-API-VERSION", "2")
            .header("Forwarded", "by=***********;for=$clientIP;host=127.0.0.1;proto=http")

    private fun buildGetRegister(cookie: Cookie) = GET<Unit>("/register")
        .cookie(cookie)
        .header("X-API-VERSION", "2")
        .header("Forwarded", "by=***********;for=$clientIP;host=127.0.0.1;proto=http")

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        loadPartialAccountIntoDb(dynamoDB)
        val partialAccount = accountRepository.create(
            "fake_user",
            EmailAddress("<EMAIL>"),
            registrationType = RegistrationType.BASIC,
        )
        accountId = partialAccount.id
        accountRegisterRepository.save(
            accountRegisterMissingOnlyAgreement.copy(
                accountId = accountId,
                openForUserReview = false,
                openedForUserReviewAt = getZonedDateTime(),
            ),
        )
        every {
            crmService.upsertContact(any<AccountRegisterData>())
        } returns mockk()

        every { s3LinkGenerator.generate(any(), any(), any()) } returns "FAKE_LINK"
        every { kycService.generate(any()) } returns Pair(
            kycFileStoredObject,
            KycDossier(
                taxId = KycDossierTaxIdRegion(region = "SP-RJ"),
                sanctions = listOf(),
                globalSanctionsList = listOf(),
                pep = PepQuery(at = ZonedDateTime.now(), result = PepQueryResult.RELATED_TO_PEP),
                phones = listOf(),
                emails = listOf(),
                gender = null,
                motherName = null,
                fatherName = null,
                mep = KycDossierMepQuery(
                    exposureLevel = null,
                    celebrityLevel = null,
                    unpopularityLevel = null,
                ),
                officialData = KycDossierOfficialDocumentData(
                    provider = "",
                    name = "",
                    birthDate = LocalDate.of(1980, 1, 1),
                    socialName = null,
                    hasObitIndication = false,
                    deathYear = null,
                    regular = false,
                    status = "",
                ),

            ),
        ).right()
    }

    @Test
    fun `should return BAD_REQUEST when agreement is not settled`() {
        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(
                buildPutAgreement(generateCookie(accountId, Role.GUEST), false),
                AccountRegisterDataTO::class.java,
            )
        }
        thrown.status shouldBe BAD_REQUEST
    }

    @Test
    fun `should return BAD_REQUEST when agreement is settled but register data is not completed`() {
        accountRegisterRepository.save(accountRegisterDataWithoutMonthlyIncomeAndAgreement.copy(accountId = accountId))
        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(
                buildPutAgreement(generateCookie(accountId, Role.GUEST), true),
                AccountRegisterDataTO::class.java,
            )
        }
        thrown.status shouldBe BAD_REQUEST
    }

    @Test
    fun `should return BAD_REQUEST when agreement is settled but document info data is not completed`() {
        accountRegisterRepository.save(
            accountRegisterMissingOnlyAgreement.copy(
                accountId = accountId,
                documentInfo = accountRegisterDataWithDocumentInfo.documentInfo!!.copy(cpf = ""),
            ),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(
                buildPutAgreement(generateCookie(accountId, Role.GUEST), true),
                AccountRegisterDataTO::class.java,
            )
        }
        thrown.status shouldBe BAD_REQUEST
    }

    @Test
    fun `should return files when agreement files are ready and user has not accepted agreement`() {
        val response = client.toBlocking()
            .exchange(buildGetRegister(generateCookie(accountId, Role.GUEST)), AccountRegisterDataTO::class.java)
        response.status shouldBe OK
        with(response.getBody(AccountRegisterDataTO::class.java).get()) {
            userContract?.contract shouldNotBe null
            userContract?.declarationOfResidency shouldNotBe null
        }

        val slot = slot<ContractSignature>()
        val formSlot = slot<ContractForm>()
        verify(exactly = 1) {
            agreementFilesService.createContract(any(), any(), any())
            contractWriterService.createContract(any(), capture(formSlot), capture(slot))
            agreementFilesService.createDeclarationOfResidency(any(), any(), any())
        }
        slot.captured.ip shouldBe clientIP
        with(accountRegisterDataWithDocumentInfo.documentInfo!!) {
            formSlot.captured.fullName shouldBe name
            formSlot.captured.birthPlace shouldBe "$birthCity, $birthState"
        }

        with(accountRegisterRepository.findByAccountId(accountId)) {
            agreementData?.acceptedAt shouldBe null
            agreementData?.userContractFile?.key shouldContain contractFileKey
            agreementData?.declarationOfResidencyFile?.key shouldContain declarationOfResidencyKey
        }
    }

    @Test
    fun `should return both files when agreement files are ready and user has accepted agreement`() {
        val accountRegister = accountRegisterDataMissingAcceptedAt.copy(
            agreementData = accountRegisterDataMissingAcceptedAt.agreementData!!.copy(acceptedAt = getZonedDateTime()),
            accountId = accountId,
        )
        accountRegisterRepository.save(accountRegister)

        val response = client.toBlocking()
            .exchange(buildGetRegister(generateCookie(accountId, Role.GUEST)), AccountRegisterDataTO::class.java)
        response.status shouldBe OK
        response.getBody(AccountRegisterDataTO::class.java).get().userContract?.contract shouldNotBe null

        verify(exactly = 0) {
            agreementFilesService.createContract(any(), any(), any())
            agreementFilesService.createDeclarationOfResidency(any(), any(), any())
        }

        with(accountRegisterRepository.findByAccountId(accountId)) {
            agreementData?.acceptedAt.shouldNotBeNull()
            agreementData shouldBe accountRegister.agreementData
        }
    }

    @Test
    fun `should return files when agreement files are ready and user has not accepted neither edited any info`() {
        accountRegisterRepository.save(accountRegisterDataMissingAcceptedAt.copy(accountId = accountId))
        val response = client.toBlocking()
            .exchange(buildGetRegister(generateCookie(accountId, Role.GUEST)), AccountRegisterDataTO::class.java)
        response.status shouldBe OK
        response.getBody(AccountRegisterDataTO::class.java).get().userContract?.contract shouldNotBe null

        verify(exactly = 0) {
            agreementFilesService.createContract(any(), any(), any())
            agreementFilesService.createDeclarationOfResidency(any(), any(), any())
        }

        with(accountRegisterRepository.findByAccountId(accountId)) {
            agreementData shouldBe accountRegisterDataMissingAcceptedAt.agreementData
        }
    }

    @Test
    fun `should generate new contract and declaration of residency when signature key does not match`() {
        val signatureKey = "FAKE"
        val accountRegister = accountRegisterDataMissingAcceptedAt.copy(
            agreementData = accountRegisterDataMissingAcceptedAt.agreementData!!.copy(userContractSignature = signatureKey),
            accountId = accountId,
        )
        accountRegisterRepository.save(accountRegister)
        val response = client.toBlocking()
            .exchange(buildGetRegister(generateCookie(accountId, Role.GUEST)), AccountRegisterDataTO::class.java)
        response.status shouldBe OK
        response.getBody(AccountRegisterDataTO::class.java).get().userContract?.contract shouldNotBe null

        verify(exactly = 1) {
            agreementFilesService.createContract(any(), any(), any())
            agreementFilesService.createDeclarationOfResidency(any(), any(), any())
        }

        with(accountRegisterRepository.findByAccountId(accountId)) {
            agreementData?.acceptedAt.shouldBeNull()
            agreementData?.userContractSignature shouldBe accountRegister.toContractForm().generateSignatureKey()
            agreementData?.userContractFile shouldNotBe accountRegister.agreementData!!.userContractFile
            agreementData?.declarationOfResidencyFile shouldNotBe accountRegister.agreementData!!.declarationOfResidencyFile
        }
    }

    @Test
    fun `should return ok when agreement is settled`() {
        val updatedDate = getZonedDateTime().plusDays(2)

        withGivenDateTime(updatedDate) {
            val response = client.toBlocking().exchange(
                buildPutAgreement(generateCookie(accountId, Role.GUEST), true),
                AccountRegisterDataTO::class.java,
            )
            response.status shouldBe OK
            response.body.get().userContract!!.hasAccepted shouldBe true
            response.body.get().openForUserReview shouldBe false
        }

        with(accountRegisterRepository.findByAccountId(accountId)) {
//            kycFile shouldBe kycFileStoredObject //FIXME: https://gitlab.com/via1/bill-payment-service/-/issues/804
            agreementData?.acceptedAt shouldBe updatedDate
            agreementData?.userContractSignature.shouldNotBeNull()
            agreementData?.userContractFile?.key shouldContain contractFileKey
            agreementData?.declarationOfResidencyFile?.key shouldContain declarationOfResidencyKey
            openForUserReview shouldBe false
            openedForUserReviewAt shouldNotBe null
        }

        with(accountRepository.findPartialAccountById(accountId)) {
            status shouldBe AccountStatus.UNDER_REVIEW
        }

        val slot = slot<ContractSignature>()
        val formSlot = slot<ContractForm>()
        verify(exactly = 1) {
            contractWriterService.createContract(any(), capture(formSlot), capture(slot))
            agreementFilesService.createContract(any(), any(), any())
            agreementFilesService.createDeclarationOfResidency(any(), any(), any())
            chatBotNotificationService.notifyRegisterCompleted(any<PartialAccount>())

//            kycService.generate(any()) //FIXME: https://gitlab.com/via1/bill-payment-service/-/issues/804
        }
        verify(exactly = 0) {
            notificationAdapter.notifyRegisterUpdated(any(), any(), any())
        }

        slot.captured.ip shouldBe clientIP
        with(accountRegisterDataWithDocumentInfo.documentInfo!!) {
            formSlot.captured.fullName shouldBe name
            formSlot.captured.birthPlace shouldBe "$birthCity, $birthState"
        }

//        val attachmentSlot = slot<List<Attachment>>() //FIXME: https://gitlab.com/via1/bill-payment-service/-/issues/804
//        verify(exactly = 1) {
//            notificationSenderService.sendRawEmail(any(), any(), any(), any())
//            notificationSenderService.sendRawEmail(any(), any(), any(), any(), capture(attachmentSlot))
//        }
//
//        attachmentSlot.captured.size shouldBe 5
//        attachmentSlot.captured.forExactly(1) { attachment ->
//            attachment.fileName shouldBe kycFileStoredObject.key
//            attachment.contentType shouldBe MediaType.TEXT_HTML
//        }
//
//        attachmentSlot.captured.forExactly(1) { attachment ->
//            attachment.fileName shouldContainIgnoringCase declarationOfResidencyKey
//            attachment.contentType shouldBe MediaType.APPLICATION_PDF
//        }
//
//        attachmentSlot.captured.forExactly(1) { attachment ->
//            attachment.fileName shouldContainIgnoringCase contractFileKey
//            attachment.contentType shouldBe MediaType.APPLICATION_PDF
//        }
    }

    @Test
    fun `deve armazenar o PEP consultado quando der aceitei nos termos`() {
        val updatedDate = getZonedDateTime().plusDays(2)

        with(accountRegisterRepository.findByAccountId(accountId)) {
            politicallyExposed?.selfDeclared shouldBe false
        }

        withGivenDateTime(updatedDate) {
            val result = client.toBlocking().exchange(
                buildPutAgreement(generateCookie(accountId, Role.GUEST), true),
                AccountRegisterDataTO::class.java,
            )
            result.status shouldBe OK

            val timeout = Duration.ofSeconds(10)
            val interval = Duration.ofMillis(100)

            await("await async code").atMost(timeout).pollInterval(interval).until {
                val found = accountRegisterRepository.findByAccountId(accountId)
                found.politicallyExposed?.selfDeclared == false &&
                    found.politicallyExposed?.query?.result == PepQueryResult.RELATED_TO_PEP
            }

            with(accountRegisterRepository.findByAccountId(accountId)) {
                politicallyExposed?.selfDeclared shouldBe false
                politicallyExposed?.query?.result shouldBe PepQueryResult.RELATED_TO_PEP
            }
        }
    }

    @Test
    fun `should return ok when agreement is settled and disable account register when email is signup test account`() {
        val partialAccount = accountRepository.create(
            "fake_user",
            accountRegisterMissingOnlyAgreement.emailAddress,
            registrationType = RegistrationType.BASIC,
        )
        accountRegisterRepository.save(
            accountRegisterMissingOnlyAgreement.copy(
                accountId = partialAccount.id,
                openForUserReview = false,
                openedForUserReviewAt = getZonedDateTime(),
                mobilePhone = MobilePhone(signUpTestMobilePhone),
            ),
        )
        every { crmService.removeContactAsync(partialAccount.id) } just Runs

        val updatedDate = getZonedDateTime().plusDays(2)

        withGivenDateTime(updatedDate) {
            val response = client.toBlocking().exchange(
                buildPutAgreement(generateCookie(partialAccount.id, Role.GUEST), true),
                AccountRegisterDataTO::class.java,
            )
            response.status shouldBe OK
        }

        shouldThrow<ItemNotFoundException> { accountRegisterRepository.findByAccountId(partialAccount.id) }

        with(accountRepository.findPartialAccountById(partialAccount.id)) {
            status shouldBe AccountStatus.CLOSED
        }

        verify {
            notificationSenderService wasNot Called
        }
    }

    @Test
    fun `should notify register updated and return ok when agreement is settled`() {
        accountRegisterRepository.save(
            accountRegisterMissingOnlyAgreement.copy(
                accountId = accountId,
                openForUserReview = true,
            ),
        )

        val response = client.toBlocking().exchange(
            buildPutAgreement(generateCookie(accountId, Role.GUEST), true),
            AccountRegisterDataTO::class.java,
        )
        response.status shouldBe OK
        response.body.get().userContract!!.hasAccepted shouldBe true
        response.body.get().openForUserReview shouldBe false

        verify(exactly = 1) {
            notificationAdapter.notifyRegisterUpdated(
                accountId,
                accountRegisterMissingOnlyAgreement.mobilePhone!!,
                accountRegisterMissingOnlyAgreement.documentInfo!!.name,
            )
        }

        verify(exactly = 0) {
            chatBotNotificationService.notifyRegisterCompleted(any<PartialAccount>())
        }
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `should publish CADASTRO_CONCLUIDO event with contact ID when agreement is settled`(openForUserReview: Boolean) {
        accountRegisterRepository.save(
            accountRegisterMissingOnlyAgreement.copy(
                accountId = accountId,
                openForUserReview = openForUserReview,
            ),
        )

        val updatedDate = getZonedDateTime().plusDays(2)

        withGivenDateTime(updatedDate) {
            val response = client.toBlocking().exchange(
                buildPutAgreement(generateCookie(accountId, Role.GUEST), true),
                AccountRegisterDataTO::class.java,
            )
            response.status shouldBe OK
            response.body.get().userContract!!.hasAccepted shouldBe true
            response.body.get().openForUserReview shouldBe false
        }

        verify(exactly = 1) {
            registerInstrumentationService.completed(
                accountId,
                accountRegisterMissingOnlyAgreement.registrationType,
            )
        }
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `should return success when agreement is settled and CRM event publish fails`(openForUserReview: Boolean) {
        every {
            registerInstrumentationService.completed(any(), any())
        } throws IntercomAdapterException("mock exception")

        accountRegisterRepository.save(
            accountRegisterMissingOnlyAgreement.copy(
                accountId = accountId,
                openForUserReview = openForUserReview,
            ),
        )

        val updatedDate = getZonedDateTime().plusDays(2)

        withGivenDateTime(updatedDate) {
            val response = client.toBlocking().exchange(
                buildPutAgreement(generateCookie(accountId, Role.GUEST), true),
                AccountRegisterDataTO::class.java,
            )
            response.status shouldBe OK
        }
    }

    @Test
    fun `should not generate another contract neither declaration of residency on put agreement when signature is the same`() {
        accountRegisterRepository.save(accountRegisterDataMissingAcceptedAt.copy(accountId = accountId))
        val updatedDate = getZonedDateTime().plusDays(2)

        withGivenDateTime(updatedDate) {
            val response = client.toBlocking().exchange(
                buildPutAgreement(generateCookie(accountId, Role.GUEST), true),
                AccountRegisterDataTO::class.java,
            )
            response.status shouldBe OK
            response.body.get().userContract!!.hasAccepted shouldBe true
        }

        verify(exactly = 0) {
            agreementFilesService.createContract(any(), any(), any())
            agreementFilesService.createDeclarationOfResidency(any(), any(), any())
        }

        with(accountRegisterRepository.findByAccountId(accountId)) {
            agreementData!!.shouldBeEqualToIgnoringFields(
                accountRegisterDataMissingAcceptedAt.agreementData!!,
                AgreementData::acceptedAt,
            )
        }
    }

    @Test
    fun `should generate another contract and declaration of residency on put agreement when signature is different`() {
        val accountRegister = accountRegisterDataMissingAcceptedAt.copy(
            agreementData = accountRegisterDataMissingAcceptedAt.agreementData?.copy(userContractSignature = "FAKE"),
            accountId = accountId,
        )
        accountRegisterRepository.save(accountRegister)
        val updatedDate = getZonedDateTime().plusDays(2)

        withGivenDateTime(updatedDate) {
            val response = client.toBlocking().exchange(
                buildPutAgreement(generateCookie(accountId, Role.GUEST), true),
                AccountRegisterDataTO::class.java,
            )
            response.status shouldBe OK
            response.body.get().userContract!!.hasAccepted shouldBe true
        }

        verify(exactly = 1) {
            agreementFilesService.createContract(any(), any(), any())
            agreementFilesService.createDeclarationOfResidency(any(), any(), any())
        }
    }

    @Test
    fun `should return BAD_REQUEST when agreement is valid but account is not editable`() {
        val request = buildPutAgreement(generateCookie(accountId, Role.GUEST), true)
        accountRepository.updatePartialAccountStatus(accountId, AccountStatus.UNDER_REVIEW)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe BAD_REQUEST
    }

    @Test
    fun `should return Bad Request when monthly income is not provided`() {
        accountRegisterRepository.save(
            accountRegisterMissingOnlyAgreement.copy(
                monthlyIncome = null,
                accountId = accountId,
            ),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(
                buildPutAgreement(generateCookie(accountId, Role.GUEST), true),
                AccountRegisterDataTO::class.java,
            )
        }

        thrown.status shouldBe BAD_REQUEST
    }

    @Test
    fun `should return ok when kyc call throws exception`() {
        every { kycService.generate(any()) } returns Exception().left()

        val accountRegister = accountRegisterDataMissingAcceptedAt.copy(
            agreementData = accountRegisterDataMissingAcceptedAt.agreementData!!,
            accountId = accountId,
        )

        accountRegisterRepository.save(accountRegister)

        val response = client.toBlocking().exchange(
            buildPutAgreement(generateCookie(accountId, Role.GUEST), true),
            AccountRegisterDataTO::class.java,
        )

        accountRegisterRepository.findByAccountId(accountRegister.accountId).kycFile shouldBe null

        response.status shouldBe OK
    }

    @Test
    fun `deve retornar OK quando o telefone for duplicado em alguma outra conta que não está ativa`() {
        accountRegisterRepository.save(accountRegisterDataMissingAcceptedAt.copy(accountId = accountId))
        accountRegisterRepository.save(accountRegisterDataMissingAcceptedAt.copy(accountId = ACCOUNT.accountId))

        accountRepository.save(
            ACCOUNT.copy(
                mobilePhone = accountRegisterDataMissingAcceptedAt.mobilePhone!!.msisdn,
                status = AccountStatus.CLOSED,
            ),
        )

        val response = client.toBlocking().exchange(
            buildPutAgreement(generateCookie(accountId, Role.GUEST), true),
            AccountRegisterDataTO::class.java,
        )

        response.status shouldBe OK
    }

    @Test
    fun `deve retornar BAD_REQUEST quando o telefone for duplicado em alguma outra conta ativa`() {
        accountRegisterRepository.save(accountRegisterDataMissingAcceptedAt.copy(accountId = accountId))
        accountRegisterRepository.save(accountRegisterDataMissingAcceptedAt.copy(accountId = ACCOUNT.accountId))

        accountRepository.save(
            ACCOUNT.copy(
                mobilePhone = accountRegisterDataMissingAcceptedAt.mobilePhone!!.msisdn,
                status = AccountStatus.ACTIVE,
            ),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(
                buildPutAgreement(generateCookie(accountId, Role.GUEST), true),
                AccountRegisterDataTO::class.java,
            )
        }

        thrown.status shouldBe CONFLICT
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4033"
    }

    @ParameterizedTest
    @EnumSource(
        AccountStatus::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["UNDER_REVIEW", "UNDER_EXTERNAL_REVIEW", "APPROVED"],
    )
    fun `deve retornar BAD_REQUEST quando o telefone for duplicado em outro cadastro finalizado`(status: AccountStatus) {
        val account =
            accountRepository.create(
                username = "fake username",
                emailAddress = EmailAddress("<EMAIL>"),
                registrationType = RegistrationType.BASIC,
            )

        accountRegisterRepository.save(accountRegisterDataMissingAcceptedAt.copy(accountId = accountId))
        accountRegisterRepository.save(accountRegisterDataMissingAcceptedAt.copy(accountId = account.id))

        accountRepository.updatePartialAccountStatus(accountId = account.id, newStatus = status)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(
                buildPutAgreement(generateCookie(accountId, Role.GUEST), true),
                AccountRegisterDataTO::class.java,
            )
        }

        thrown.status shouldBe CONFLICT
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4033"
    }

    @ParameterizedTest
    @EnumSource(
        AccountStatus::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["DENIED", "REGISTER_INCOMPLETE"],
    )
    fun `deve retonar OK quando o telefone for duplicado em alguma conta com o cadastro ainda em andamento`(status: AccountStatus) {
        val account =
            accountRepository.create(
                username = "fake username",
                emailAddress = EmailAddress("<EMAIL>"),
                registrationType = RegistrationType.BASIC,
            )

        accountRegisterRepository.save(accountRegisterDataMissingAcceptedAt.copy(accountId = accountId))
        accountRegisterRepository.save(accountRegisterDataMissingAcceptedAt.copy(accountId = account.id))

        accountRepository.updatePartialAccountStatus(accountId = account.id, newStatus = status)

        val response = client.toBlocking().exchange(
            buildPutAgreement(generateCookie(accountId, Role.GUEST), true),
            AccountRegisterDataTO::class.java,
        )

        response.status shouldBe OK
    }
}