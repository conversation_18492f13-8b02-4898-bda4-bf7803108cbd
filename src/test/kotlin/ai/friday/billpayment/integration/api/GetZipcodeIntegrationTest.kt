package ai.friday.billpayment.integration.api

import ai.friday.billpayment.adapters.api.AddressTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.bigdatacorp.BigBoostAdapter
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.Address
import ai.friday.billpayment.app.integrations.AddressNotFoundException
import ai.friday.billpayment.app.integrations.BigDataServerException
import ai.friday.billpayment.app.integrations.BigDataService
import ai.friday.billpayment.integration.SecurityFixture
import arrow.core.Either
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus.BAD_REQUEST
import io.micronaut.http.HttpStatus.INTERNAL_SERVER_ERROR
import io.micronaut.http.HttpStatus.NOT_FOUND
import io.micronaut.http.HttpStatus.OK
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

@MicronautTest(environments = [FRIDAY_ENV])
class GetZipcodeIntegrationTest(embeddedServer: EmbeddedServer) {

    private val securityFixture = SecurityFixture()

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val bigDataServiceMock: BigDataService = mockk()

    @MockBean(BigBoostAdapter::class)
    fun getBigDataService() = bigDataServiceMock

    private val validZipcode = "12345678"

    private val address = Address(
        streetType = "test",
        streetName = "test",
        number = "",
        complement = "",
        neighborhood = "test",
        city = "test",
        state = "test",
        zipCode = "test",
    )

    private val genericAddress = Address(
        streetType = "",
        streetName = "",
        number = "",
        complement = "",
        neighborhood = "",
        city = "test",
        state = "test",
        zipCode = "test",
    )

    @ParameterizedTest
    @ValueSource(strings = ["123", "1234567a", "123456789", "12345-678"])
    fun `should return bad request on invalid zipcode`(zipcode: String) {
        val request = buildGetZipcodeRequest(zipcode)
        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(AddressTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe BAD_REQUEST
    }

    @Test
    fun `should return internal server error on zipcode service failure`() {
        val request = buildGetZipcodeRequest(validZipcode)
        every { bigDataServiceMock.getAddress(validZipcode) } returns Either.Left(BigDataServerException())

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(AddressTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe INTERNAL_SERVER_ERROR
    }

    @Test
    fun `should return not found on missing zipcode`() {
        val request = buildGetZipcodeRequest(validZipcode)
        every { bigDataServiceMock.getAddress(validZipcode) } returns Either.Left(AddressNotFoundException())

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(AddressTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe NOT_FOUND
    }

    @Test
    fun `should return ok on existing zipcode`() {
        val request = buildGetZipcodeRequest(validZipcode)
        every { bigDataServiceMock.getAddress(validZipcode) } returns Either.Right(address)

        val response = client.toBlocking()
            .exchange(request, Argument.of(AddressTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe OK
        assertSoftly {
            with(response.body.get()) {
                streetType shouldBe address.streetType
                streetName shouldBe address.streetName
                number shouldBe address.number
                complement shouldBe address.complement
                neighborhood shouldBe address.neighborhood
                city shouldBe address.city
                state shouldBe address.state
                zipCode shouldBe address.zipCode
            }
        }
    }

    @Test
    fun `should return ok on existing generic zipcode`() {
        val request = buildGetZipcodeRequest(validZipcode)
        every { bigDataServiceMock.getAddress(validZipcode) } returns Either.Right(genericAddress)

        val response = client.toBlocking()
            .exchange(request, Argument.of(AddressTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe OK
        assertSoftly {
            with(response.body.get()) {
                streetType shouldBe genericAddress.streetType
                streetName shouldBe genericAddress.streetName
                number shouldBe genericAddress.number
                complement shouldBe genericAddress.complement
                neighborhood shouldBe genericAddress.neighborhood
                city shouldBe genericAddress.city
                state shouldBe genericAddress.state
                zipCode shouldBe genericAddress.zipCode
            }
        }
    }

    private fun buildGetZipcodeRequest(zipcode: String): MutableHttpRequest<Unit> {
        return HttpRequest.GET<Unit>("/zipcode/$zipcode")
            .cookie(securityFixture.cookieGuest).header("X-API-VERSION", "2")
    }
}