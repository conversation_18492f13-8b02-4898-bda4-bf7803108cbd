package ai.friday.billpayment.integration.api

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.api.CreditCardResponseTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.CreditCardConfiguration
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.bankAccount
import ai.friday.billpayment.integration.SecurityFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

@MicronautTest(environments = [FRIDAY_ENV])
class BackofficeConfigurationIntegrationTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {
    private val securityFixture = SecurityFixture()

    private val enhancedClient = DynamoDBUtils.getDynamoDB()
    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )
    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `deve retornar NOT_FOUND quando não encontra o ACCOUNT_ID ao tentar configurar cartão de crédito`() {
        val request = HttpRequest.POST(
            "/backoffice/configuration/***********/creditCard/quota/0",
            "",
        )
            .cookie(securityFixture.cookieBackoffice)

        val thrown =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

        thrown.status shouldBe HttpStatus.NOT_FOUND
    }

    @ParameterizedTest
    @ValueSource(longs = [200000, 500000])
    fun `deve retornar OK quando encontra o ACCOUNT_ID e a requisição é válida`(quota: Long) {
        accountRepository.save(ACCOUNT)

        accountRepository.createAccountPaymentMethod(
            accountId = ACCOUNT.accountId,
            bankAccount = bankAccount,
            position = 0,
            mode = BankAccountMode.PHYSICAL,
        )

        val request = HttpRequest.POST(
            "/backoffice/configuration/${ACCOUNT.accountId.value}/creditCard/quota/$quota",
            "",
        )
            .cookie(securityFixture.cookieBackoffice)

        val response = client.toBlocking().exchange(request, String::class.java)

        response.status shouldBe HttpStatus.OK

        val account = accountRepository.findById(ACCOUNT.accountId)

        account.hasCreditCardEnabled() shouldBe true
        account.creditCardConfiguration.quota shouldBe quota
    }

    @ParameterizedTest
    @ValueSource(longs = [200000, 500000])
    fun `deve atualizar o valor da taxa quando encontra a configuração de cartão de crédito de uma conta`(
        quota: Long,
    ) {
        accountRepository.save(
            ACCOUNT.copy(
                configuration = ACCOUNT.configuration.copy(
                    creditCardConfiguration = ACCOUNT.creditCardConfiguration.copy(),
                ),
            ),
        )

        accountRepository.createAccountPaymentMethod(
            accountId = ACCOUNT.accountId,
            bankAccount = bankAccount,
            position = 0,
            mode = BankAccountMode.PHYSICAL,
        )

        val request = HttpRequest.POST(
            "/backoffice/configuration/${ACCOUNT.accountId.value}/creditCard/quota/$quota",
            "",
        )
            .cookie(securityFixture.cookieBackoffice)

        val response = client.toBlocking().exchange(request, String::class.java)

        response.status shouldBe HttpStatus.OK

        val account = accountRepository.findById(ACCOUNT.accountId)

        account.hasCreditCardEnabled() shouldBe true
        account.creditCardConfiguration.quota shouldBe quota
    }

    @Test
    fun `não deve atualizar a quota se o valor não for informado e o account já tinha quota`() {
        accountRepository.save(
            ACCOUNT.copy(
                configuration = ACCOUNT.configuration.copy(
                    creditCardConfiguration = CreditCardConfiguration(
                        quota = 50_000_00L,
                    ),
                ),
            ),
        )

        val request = HttpRequest.POST(
            "/backoffice/configuration/${ACCOUNT.accountId.value}/creditCard/quota",
            "",
        )
            .cookie(securityFixture.cookieBackoffice)

        val response = client.toBlocking().exchange(request, String::class.java)

        response.status shouldBe HttpStatus.OK

        val account = accountRepository.findById(ACCOUNT.accountId)

        account.hasCreditCardEnabled() shouldBe true
        account.creditCardConfiguration.quota shouldBe 50_000_00L
    }

    @Test
    fun `deve usar a quota padrão se o valor não for informado e o account não tinha quota`() {
        accountRepository.save(
            ACCOUNT.copy(
                configuration = ACCOUNT.configuration.copy(
                    creditCardConfiguration = CreditCardConfiguration(
                        quota = 0,
                    ),
                ),
            ),
        )

        accountRepository.createAccountPaymentMethod(
            accountId = ACCOUNT.accountId,
            bankAccount = bankAccount,
            position = 0,
            mode = BankAccountMode.PHYSICAL,
        )

        val request = HttpRequest.POST(
            "/backoffice/configuration/${ACCOUNT.accountId.value}/creditCard/quota",
            "",
        )
            .cookie(securityFixture.cookieBackoffice)

        val response = client.toBlocking().exchange(request, String::class.java)

        response.status shouldBe HttpStatus.OK

        val account = accountRepository.findById(ACCOUNT.accountId)

        account.hasCreditCardEnabled() shouldBe true
        account.creditCardConfiguration.quota shouldBe 500_00L
    }

    @Test
    fun `deve retornar NOT_FOUND quando não encontra o ACCOUNT_ID ao tentar desabilitar o cartão de crédito`() {
        val request = HttpRequest.DELETE("/backoffice/configuration/***********/creditCard", null)
            .cookie(securityFixture.cookieBackoffice)

        val thrown =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

        thrown.status shouldBe HttpStatus.NOT_FOUND
    }

    @Test
    fun `deve atualizar o valor da taxa para zero quando encontra o ACCOUNT_ID e desabilitar o cartão de crédito`() {
        accountRepository.save(
            ACCOUNT.copy(
                configuration = ACCOUNT.configuration.copy(
                    creditCardConfiguration = ACCOUNT.creditCardConfiguration.copy(
                        quota = 1,
                    ),
                    groups = listOf(AccountGroup.CREDIT_CARD_STANDARD_PLAN),
                ),
            ),
        )

        accountRepository.createAccountPaymentMethod(
            accountId = ACCOUNT.accountId,
            bankAccount = bankAccount,
            position = 0,
            mode = BankAccountMode.PHYSICAL,
        )
        val request = HttpRequest.DELETE("/backoffice/configuration/${ACCOUNT.accountId.value}/creditCard", null)
            .cookie(securityFixture.cookieBackoffice)

        val response = client.toBlocking().exchange(request, String::class.java)

        response.status shouldBe HttpStatus.OK

        val account = accountRepository.findById(ACCOUNT.accountId)

        account.hasCreditCardEnabled() shouldBe false
        account.creditCardConfiguration.quota shouldBe 0L
    }

    @Test
    fun `deve retornar NOT_FOUND quando não encontra o ACCOUNT_ID ao tentar recuperar a configuração de cartão de crédito`() {
        val request = HttpRequest.GET<Unit>("/backoffice/configuration/***********/creditCard")
            .cookie(securityFixture.cookieBackoffice)

        val thrown =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

        thrown.status shouldBe HttpStatus.NOT_FOUND
    }

    @Test
    fun `deve retornar a configuração do core user ao tentar recuperar a configuração de cartão de crédito`() {
        val account = ACCOUNT.copy(
            configuration = ACCOUNT.configuration.copy(
                creditCardConfiguration = ACCOUNT.creditCardConfiguration.copy(
                    quota = 2_000_00,
                ),
                groups = listOf(AccountGroup.CREDIT_CARD_STANDARD_PLAN),
            ),
        )
        accountRepository.save(
            account,
        )
        val request =
            HttpRequest.GET<CreditCardResponseTO>("/backoffice/configuration/${account.accountId.value}/creditCard")
                .cookie(securityFixture.cookieBackoffice)

        val response = client.toBlocking().exchange(request, CreditCardResponseTO::class.java)

        response.status shouldBe HttpStatus.OK
        response.body()?.quota shouldBe account.creditCardConfiguration.quota
        response.body()?.accountId shouldBe account.accountId.value
    }

    @Test
    fun `deve retornar NOT_FOUND quando account não tiver uma configuração de cartão de crédito`() {
        val account = ACCOUNT
        accountRepository.save(
            account,
        )
        val request =
            HttpRequest.GET<CreditCardResponseTO>("/backoffice/configuration/${account.accountId.value}/creditCard")
                .cookie(securityFixture.cookieBackoffice)

        val thrown =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

        thrown.status shouldBe HttpStatus.NOT_FOUND
    }
}