package ai.friday.billpayment.integration.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDetailsEntity
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillIgnoredEntity
import ai.friday.billpayment.adapters.dynamodb.BillRecurrenceDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillRecurrenceDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.billLockProvider
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billIgnored
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.addBillEventIntoDb
import ai.friday.billpayment.integration.addBillIntoDb
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.createLockTable
import ai.friday.billpayment.integration.onWallet
import ai.friday.billpayment.weeklyRecurrenceNoEndDate
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.kotest.matchers.shouldBe
import io.micronaut.context.annotation.Property
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.mockk
import jakarta.inject.Named
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

@MicronautTest(environments = [FRIDAY_ENV])
class IgnoreBillIntegrationTest(
    embeddedServer: EmbeddedServer,
    val dynamoDB: AmazonDynamoDB,
    @Named(billLockProvider) val lockProvider: InternalLock,
) {

    @field:Property(name = "lock.tableName")
    lateinit var lockTableName: String

    private val dynamoDbEnhancedClient = getDynamoDB()
    private val billRecurrenceDynamoDAO = BillRecurrenceDynamoDAO(dynamoDbEnhancedClient)
    private val billEventDAO = BillEventDynamoDAO(dynamoDbEnhancedClient)
    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(dynamoDbEnhancedClient),
        refundedClient = RefundedBillDynamoDAO(dynamoDbEnhancedClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(dynamoDbEnhancedClient),
    )
    private val walletDAO = WalletDynamoDAO(dynamoDbEnhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(dynamoDbEnhancedClient)
    private val inviteDAO = InviteDynamoDAO(dynamoDbEnhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(dynamoDbEnhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(dynamoDbEnhancedClient)

    private val walletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = mockk(),
    )

    private val recurrenceDBRepository = BillRecurrenceDBRepository(billRecurrenceDynamoDAO, "2021-12-31")

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    private val activeBill = getActiveBill(walletId = wallet.id)

    private fun validIgnoreRequest(billId: BillId = activeBill.billId) =
        HttpRequest.PUT("/bill/id/${billId.value}/ignore", "{}")
            .onWallet(wallet)

    private val ignoreThisAndFutureRequest =
        HttpRequest.PUT("/bill/id/${activeBill.billId.value}/ignore?range=THIS_AND_FUTURE", "{}")
            .onWallet(wallet)

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        createLockTable(dynamoDB, lockTableName)
        walletRepository.save(wallet)
    }

    @Test
    fun `should return 404 when bill is not found`() {
        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(validIgnoreRequest(), Unit::class.java)
        }
        Assertions.assertEquals(HttpStatus.NOT_FOUND, thrown.status)
    }

    @Test
    fun `should return conflict when bill is in a not ignorable state`() {
        addBillIntoDb(dynamoDbEnhancedClient, activeBill.copy(status = BillStatus.PAID))
        addBillEventIntoDb(dynamoDB, billAdded.copy(walletId = wallet.id))
        addBillEventIntoDb(dynamoDB, billPaid.copy(walletId = wallet.id))
        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(validIgnoreRequest(), Unit::class.java)
        }
        Assertions.assertEquals(HttpStatus.CONFLICT, thrown.status)
    }

    @Test
    fun `should return locked when cannot acquire lock`() {
        addBillIntoDb(dynamoDbEnhancedClient, activeBill)
        addBillEventIntoDb(dynamoDB, billAdded.copy(walletId = wallet.id))
        val lock = lockProvider.acquireLock(billAdded.billId.value)
        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(validIgnoreRequest(), Unit::class.java)
        }
        Assertions.assertEquals(HttpStatus.LOCKED, thrown.status)
        lock!!.unlock()
    }

    @Test
    fun `should return ok when ignore bill`() {
        addBillIntoDb(dynamoDbEnhancedClient, activeBill)
        addBillEventIntoDb(dynamoDB, billAdded.copy(walletId = wallet.id))
        val response = client.toBlocking().exchange(validIgnoreRequest(), Unit::class.java)
        Assertions.assertEquals(HttpStatus.OK, response.status)

        val itemList = billEventDAO.findByPartitionKey(activeBill.billId.value)
        val billEntity = itemList[1]
        val event =
            jacksonObjectMapper().readerFor(BillEventDetailsEntity::class.java)
                .readValue<BillEventDetailsEntity>(billEntity.details)
        assertTrue(event is BillIgnoredEntity)

        val bill: BillView = billRepository.findBill(activeBill.billId, wallet.id)
        assertEquals(BillStatus.IGNORED, bill.status)
    }

    @Test
    fun `should return ok on ignored bill`() {
        val ignoredBillId = BillId("IGNORED_BILL_ID")
        val ignoredBill =
            activeBill.copy(status = BillStatus.IGNORED, billId = ignoredBillId, walletId = wallet.id)

        addBillIntoDb(dynamoDbEnhancedClient, ignoredBill)
        addBillEventIntoDb(dynamoDB, billAdded.copy(billId = ignoredBillId, walletId = wallet.id))
        addBillEventIntoDb(dynamoDB, billIgnored.copy(billId = ignoredBillId, walletId = wallet.id))

        val response = client.toBlocking().exchange(validIgnoreRequest(ignoredBill.billId), Unit::class.java)
        Assertions.assertEquals(HttpStatus.OK, response.status)

        val bill: BillView = billRepository.findBill(ignoredBill.billId, wallet.id)
        assertEquals(BillStatus.IGNORED, bill.status)

        val itemList = billEventDAO.findByPartitionKey(ignoredBill.billId.value)
        assertEquals(2, itemList.size)
    }

    @Test
    fun `should return conflict when bill is valid but without recurrence`() {
        addBillIntoDb(dynamoDbEnhancedClient, activeBill.copy(status = BillStatus.ACTIVE))
        addBillEventIntoDb(dynamoDB, billAdded.copy(walletId = wallet.id))

        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(ignoreThisAndFutureRequest, Unit::class.java)
        }

        thrown.status shouldBe HttpStatus.CONFLICT
    }

    @Test
    fun `should return ok when bill recurrence is ignored`() {
        val recurrence = ActionSource.Recurrence(weeklyRecurrenceNoEndDate.id, role = Role.OWNER)
        activeBill.copy(
            status = BillStatus.ACTIVE,
            source = recurrence,
        )
        addBillIntoDb(dynamoDbEnhancedClient, activeBill)
        addBillEventIntoDb(dynamoDB, billAdded.copy(actionSource = recurrence, walletId = wallet.id))
        recurrenceDBRepository.save(weeklyRecurrenceNoEndDate.plusBillId(billAdded.billId))

        val response = client.toBlocking().exchange(ignoreThisAndFutureRequest, Unit::class.java)

        response.status shouldBe HttpStatus.OK
    }

    @Test
    fun `should return not found when bill is valid but without recurrence`() {
        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(ignoreThisAndFutureRequest, Unit::class.java)
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
    }
}