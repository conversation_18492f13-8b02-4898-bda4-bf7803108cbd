package ai.friday.billpayment.integration.api

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.accountRegisterDataWithCNHUploaded
import ai.friday.billpayment.accountRegisterDataWithDocumentInfo
import ai.friday.billpayment.accountRegisterDataWithPhoneVerified
import ai.friday.billpayment.accountRegisterDataWithRGUploaded
import ai.friday.billpayment.adapters.api.AccountRegisterDataTO
import ai.friday.billpayment.adapters.api.DocumentDataResponseTO
import ai.friday.billpayment.adapters.api.RegisterErrors
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.RegisterService
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.account.UploadedDocumentImages
import ai.friday.billpayment.app.integrations.DocumentMustBeOpenedException
import ai.friday.billpayment.app.integrations.DocumentOCRParserException
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.InvalidDocumentImageException
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.integration.SecurityFixture
import ai.friday.billpayment.rgDocumentInfo
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.client.multipart.MultipartBody
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import java.io.File
import java.time.format.DateTimeFormatter
import java.util.stream.Stream
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

@MicronautTest(environments = [FRIDAY_ENV])
class PostDocumentIntegrationTest(embeddedServer: EmbeddedServer) {

    private val securityFixture = SecurityFixture()

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val registerServiceMock: RegisterService = mockk() {
        every {
            processLiveness(any())
        } answers {
            firstArg<AccountRegisterData>().right()
        }
    }

    @MockBean(RegisterService::class)
    fun getRegisterService(): RegisterService = registerServiceMock

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @Nested
    @DisplayName("chn uploaded document")
    inner class CnhUploaded {
        private fun buildCNHRequest(
            cookie: Cookie,
            filePath: String,
            mediaType: MediaType = MediaType.IMAGE_PNG_TYPE,
            ocr: Boolean? = null,
        ): MutableHttpRequest<MultipartBody> {
            val url = Thread.currentThread().contextClassLoader.getResource(filePath)
            val file = File(url.path)
            val requestBody: MultipartBody = MultipartBody.builder()
                .addPart(
                    "document",
                    file.name,
                    mediaType,
                    file,
                ).build()

            val requestString = if (ocr != null) {
                "/register/document/cnh?ocr=$ocr"
            } else {
                "/register/document/cnh"
            }

            return HttpRequest.POST(requestString, requestBody)
                .cookie(cookie).header("X-API-VERSION", "2")
                .contentType(MediaType.MULTIPART_FORM_DATA_TYPE)
        }

        @Test
        fun `should return forbidden for non guest user`() {
            val request = buildCNHRequest(securityFixture.cookieAuthOwner, "images/cnh_fake.jpeg")
            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))
            }
            thrown.status shouldBe HttpStatus.FORBIDDEN
        }

        @Test
        fun `should return REQUEST_ENTITY_TOO_LARGE for file uploaded bigger than limit`() {
            val request = buildCNHRequest(securityFixture.cookieGuest, "images/large_cnh_fake.jpeg")
            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))
            }
            thrown.status shouldBe HttpStatus.REQUEST_ENTITY_TOO_LARGE
        }

        @Test
        fun `should return UNSUPPORTED_MEDIA_TYPE when uploading an invalid file extension`() {
            val request = buildCNHRequest(
                securityFixture.cookieGuest,
                "images/invalid_document.odt",
                MediaType.APPLICATION_OCTET_STREAM_TYPE,
            )

            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))
            }
            thrown.status shouldBe HttpStatus.UNSUPPORTED_MEDIA_TYPE
        }

        @Test
        fun `should return OK when registerService works for image`() {
            runOKTest("images/cnh_fake.jpeg", MediaType.IMAGE_PNG_TYPE)
        }

        @Test
        fun `should return OK when registerService works for pdf document`() {
            runOKTest("images/cnh_em_pdf.pdf", MediaType.APPLICATION_PDF_TYPE)
        }

        private fun runOKTest(filePath: String, mediaType: MediaType): AccountRegisterDataTO {
            val request =
                buildCNHRequest(cookie = securityFixture.cookieGuest, filePath = filePath, mediaType = mediaType)

            every { registerServiceMock.processCNHDocumentFile(any(), any(), any(), any()) } returns Either.Right(
                accountRegisterDataWithDocumentInfo,
            )
            every { registerServiceMock.updateAgreementFiles(any(), any()) } returns accountRegisterDataWithDocumentInfo.agreementData
            val response = client.toBlocking()
                .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))
            val accountRegisterDataTO = response.body.get()
            response.status shouldBe HttpStatus.OK
            with(accountRegisterDataWithDocumentInfo.documentInfo!!) {
                accountRegisterDataTO.document shouldBe DocumentDataResponseTO(
                    name = name,
                    cpf = cpf,
                    birthDate = birthDate!!.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")),
                    fatherName = fatherName,
                    motherName = motherName,
                    isEdited = false,
                    birthCity = birthCity,
                    birthState = birthState,
                    orgEmission = orgEmission,
                    expeditionDate = expeditionDate!!.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")),
                    cnhNumber = cnhNumber,
                    rgNumber = rg,
                )
            }

            return accountRegisterDataTO
        }

        @Test
        fun `should return INTERNAL_SERVER_ERROR when registerService return any exception`() {
            val request = buildCNHRequest(securityFixture.cookieGuest, "images/cnh_fake.jpeg")

            every { registerServiceMock.processCNHDocumentFile(any(), any(), any()) } returns Either.Left(
                DocumentOCRParserException(
                    NoStackTraceException("teste"),
                ),
            )

            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))
            }
            thrown.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        }

        @Test
        fun `deve retornar no content ao enviar um CNH sem checagem de ocr`() {
            val request = buildCNHRequest(
                securityFixture.cookieGuest,
                "images/cnh_fake.jpeg",
                mediaType = MediaType.IMAGE_PNG_TYPE,
                ocr = false,
            )

            every { registerServiceMock.processCNHDocumentFile(any(), any(), any(), any()) } returns Either.Right(
                accountRegisterDataWithCNHUploaded,
            )

            every { registerServiceMock.updateAgreementFiles(any(), any()) } returns accountRegisterDataWithCNHUploaded.agreementData

            val response = client.toBlocking()
                .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))

            response.status shouldBe HttpStatus.NO_CONTENT
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.integration.api.PostDocumentIntegrationTest#invalidDocumentErrors")
        fun `deve retornar BAD_REQUEST quando o documento enviado não for válido`(
            registerError: RegisterErrors,
            exception: InvalidDocumentImageException,
        ) {
            val request = buildCNHRequest(securityFixture.cookieGuest, "images/cnh_fake.jpeg")

            every {
                registerServiceMock.processCNHDocumentFile(any(), any(), any(), any())
            } returns exception.left()

            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(
                        request,
                        Argument.of(AccountRegisterDataTO::class.java),
                        Argument.of(ResponseTO::class.java),
                    )
            }

            thrown.status shouldBe HttpStatus.BAD_REQUEST
            thrown.response.getBody(ResponseTO::class.java)
                .get().code shouldBe registerError.code
        }
    }

    companion object {
        @JvmStatic
        fun invalidDocumentErrors(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    RegisterErrors.INVALID_DOCUMENT_IMAGE,
                    InvalidDocumentImageException("FAKE_EXCEPTION"),
                ),
                Arguments.arguments(RegisterErrors.DOCUMENT_MUST_BE_OPENED, DocumentMustBeOpenedException()),
//                Arguments.arguments(RegisterErrors.FACE_NOT_FOUND, FaceNotFoundException()),
            )
        }
    }

    @Nested
    @DisplayName("rg uploaded document")
    inner class RgUploaded {
        private fun buildRGRequest(
            cookie: Cookie,
            filePath: String,
            frontFace: Boolean,
            mediaType: MediaType = MediaType.IMAGE_PNG_TYPE,
            ocr: Boolean? = null,
        ): MutableHttpRequest<MultipartBody> {
            val url = Thread.currentThread().contextClassLoader.getResource(filePath)
            val file = File(url.path)
            val path = if (frontFace) {
                "frontSide"
            } else {
                "backSide"
            }
            val requestBody: MultipartBody = MultipartBody.builder()
                .addPart(
                    "document",
                    file.name,
                    mediaType,
                    file,
                ).build()

            val requestString = if (ocr != null) {
                "/register/document/rg/$path?ocr=$ocr"
            } else {
                "/register/document/rg/$path"
            }

            return HttpRequest.POST(requestString, requestBody)
                .cookie(cookie).header("X-API-VERSION", "2")
                .contentType(MediaType.MULTIPART_FORM_DATA_TYPE)
        }

        @Test
        fun `should return OK when registerService works for front document image`() {
            runOKTest(
                "images/cnh_fake.jpeg",
                MediaType.IMAGE_PNG_TYPE,
                true,
                accountRegisterDataWithPhoneVerified.copy(
                    documentInfo = null,
                    isDocumentEdited = false,
                    uploadedCNH = null,
                    uploadedDocument = UploadedDocumentImages(
                        front = StoredObject(region = null, bucket = "", key = ""),
                        documentType = DocumentType.RG,
                        back = null,
                    ),
                ),
            )
        }

        @Test
        fun `deve retornar no content ao enviar um RG sem checagem de ocr`() {
            val request =
                buildRGRequest(
                    cookie = securityFixture.cookieGuest,
                    filePath = "images/cnh_em_pdf.pdf",
                    frontFace = true,
                    ocr = false,
                    mediaType = MediaType.APPLICATION_PDF_TYPE,
                )

            val slot = slot<Boolean>()
            every {
                registerServiceMock.processRGDocumentFile(
                    any(),
                    any(),
                    capture(slot),
                    any(),
                    any(),
                )
            } returns Either.Right(
                accountRegisterDataWithRGUploaded,
            )
            every { registerServiceMock.updateAgreementFiles(any(), any()) } returns accountRegisterDataWithRGUploaded.agreementData

            val response = client.toBlocking()
                .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))

            response.status shouldBe HttpStatus.NO_CONTENT
        }

        @Test
        fun `should return OK when registerService works for front pdf document`() {
            runOKTest(
                "images/cnh_em_pdf.pdf",
                MediaType.APPLICATION_PDF_TYPE,
                true,
                accountRegisterDataWithPhoneVerified.copy(
                    documentInfo = null,
                    isDocumentEdited = false,
                    uploadedCNH = null,
                    uploadedDocument = UploadedDocumentImages(
                        front = StoredObject(region = null, bucket = "", key = ""),
                        documentType = DocumentType.RG,
                        back = null,
                    ),
                ),
            )
        }

        @Test
        fun `should return OK when registerService works for back document image`() {
            runOKTest(
                "images/cnh_fake.jpeg",
                MediaType.IMAGE_PNG_TYPE,
                false,
                accountRegisterDataWithPhoneVerified.copy(
                    documentInfo = rgDocumentInfo,
                    isDocumentEdited = false,
                    uploadedCNH = null,
                    uploadedDocument = UploadedDocumentImages(
                        front = StoredObject(region = null, bucket = "", key = ""),
                        documentType = DocumentType.RG,
                        back = StoredObject(region = null, bucket = "", key = ""),
                    ),
                ),
            )
        }

        @Test
        fun `should return OK when registerService works for back pdf document`() {
            runOKTest(
                "images/cnh_em_pdf.pdf",
                MediaType.APPLICATION_PDF_TYPE,
                false,
                accountRegisterDataWithPhoneVerified.copy(
                    documentInfo = rgDocumentInfo,
                    isDocumentEdited = false,
                    uploadedCNH = null,
                    uploadedDocument = UploadedDocumentImages(
                        front = StoredObject(region = null, bucket = "", key = ""),
                        documentType = DocumentType.RG,
                        back = StoredObject(region = null, bucket = "", key = ""),
                    ),
                ),
            )
        }

        private fun runOKTest(
            filePath: String,
            mediaType: MediaType,
            frontFace: Boolean,
            accountRegisterData: AccountRegisterData,
        ) {
            val request =
                buildRGRequest(
                    cookie = securityFixture.cookieGuest,
                    filePath = filePath,
                    mediaType = mediaType,
                    frontFace = frontFace,
                )

            val slot = slot<Boolean>()
            every {
                registerServiceMock.processRGDocumentFile(
                    any(),
                    any(),
                    capture(slot),
                    any(),
                    any(),
                )
            } returns Either.Right(
                accountRegisterData,
            )
            every { registerServiceMock.updateAgreementFiles(any(), any()) } returns accountRegisterData.agreementData
            val response = client.toBlocking()
                .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))
            val accountRegisterDataTO = response.body.get()
            response.status shouldBe HttpStatus.OK

            if (frontFace) {
                accountRegisterData.documentInfo.shouldBeNull()
                accountRegisterDataTO.document shouldBe DocumentDataResponseTO()
            } else {
                with(accountRegisterData.documentInfo!!) {
                    accountRegisterDataTO.document shouldBe DocumentDataResponseTO(
                        name = name,
                        cpf = cpf,
                        birthDate = birthDate!!.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")),
                        fatherName = fatherName,
                        motherName = motherName,
                        isEdited = false,
                        birthCity = birthCity,
                        birthState = birthState,
                        orgEmission = orgEmission,
                        expeditionDate = expeditionDate!!.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")),
                        cnhNumber = cnhNumber.orEmpty(),
                        rgNumber = rg,
                    )
                }
            }

            slot.captured shouldBe frontFace
        }
    }
}