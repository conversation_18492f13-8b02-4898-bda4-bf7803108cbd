package ai.friday.billpayment.integration.api

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.adapters.api.ActionSourceType
import ai.friday.billpayment.adapters.api.BillSourceTO
import ai.friday.billpayment.adapters.api.BillTO
import ai.friday.billpayment.adapters.api.EntriesTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.WalletBillCategoryDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletBillCategoryDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BatchSchedulingId
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillMarkedAsPaid
import ai.friday.billpayment.app.bill.BillPaymentScheduledCreditCardInfo
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillTag
import ai.friday.billpayment.app.bill.BillViewSchedule
import ai.friday.billpayment.app.bill.ExternalBillId
import ai.friday.billpayment.app.bill.ExternalBillProvider
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.bill.WarningCode
import ai.friday.billpayment.app.billcategory.BillCategorySuggestion
import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.app.payment.ErrorSource
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.payment.NU_BANK_DOCUMENT
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.statement.StatementService
import ai.friday.billpayment.bankStatementItem
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentFailedRetryable
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.getActiveBillFicha
import ai.friday.billpayment.getBillWithDueDate
import ai.friday.billpayment.getIgnoredBill
import ai.friday.billpayment.getInvoiceBill
import ai.friday.billpayment.getPaidBill
import ai.friday.billpayment.integration.EMAIL
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.addBillIntoDb
import ai.friday.billpayment.integration.buildCookie
import ai.friday.billpayment.integration.cleanupDb
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.pixKeyAdded
import ai.friday.billpayment.weeklyRecurrenceNoEndDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.dateFormat
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.date.shouldHaveSameDayAs
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import java.time.Instant
import java.time.LocalDate
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

@MicronautTest(environments = [FRIDAY_ENV])
class GetBillsIntegrationTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet(
        otherMembers = listOf(walletFixture.ultraLimitedParticipant),
    )

    private val dynamoClient = setupDynamoDB()

    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(dynamoClient),
        refundedClient = RefundedBillDynamoDAO(dynamoClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(dynamoClient),
    )

    private val accountDAO = AccountDynamoDAO(dynamoClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(dynamoClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(dynamoClient)
    private val nsuDAO = NSUDynamoDAO(dynamoClient)
    private val transactionDynamo = TransactionDynamo(dynamoClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val walletDAO = WalletDynamoDAO(dynamoClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(dynamoClient)
    private val inviteDAO = InviteDynamoDAO(dynamoClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(dynamoClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(dynamoClient)

    private val walletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = mockk(),
    )
    private val walletBillCategoryDbRepository = WalletBillCategoryDbRepository(client = WalletBillCategoryDynamoDAO(cli = setupDynamoDB()))

    private val overdueBill =
        getBillWithDueDate(wallet.id, daysToSubtract = 3, effectiveDaysToSubtract = 2)
    private val activeBill = getActiveBill(wallet.id, wallet.founder.accountId)
    private val paidBill =
        getPaidBill(walletId = wallet.id).copy(source = ActionSource.WalletMailBox(from = EMAIL))
    private val scheduledBillView =
        getActiveBill(
            wallet.id,
            wallet.founder.accountId,
        ).copy(schedule = BillViewSchedule(date = activeBill.effectiveDueDate))
    private val scheduledOngoingBillView =
        getActiveBill(
            wallet.id,
            wallet.founder.accountId,
        ).copy(schedule = BillViewSchedule(date = activeBill.effectiveDueDate))
    private val scheduledPostponedByMissingFundsBillView = getActiveBill(wallet.id, wallet.founder.accountId).copy(
        schedule = BillViewSchedule(
            date = activeBill.effectiveDueDate,
            waitingFunds = true,
            waitingRetry = false,
        ),
    )
    private val scheduledWaitingRetryBillView = getActiveBill(wallet.id, wallet.founder.accountId).copy(
        schedule = BillViewSchedule(
            date = activeBill.effectiveDueDate,
            waitingFunds = false,
            waitingRetry = true,
        ),
    )

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @MockBean(StatementService::class)
    fun statementService() = statementService
    private val statementService: StatementService = mockk() {
        every { findAllCredits(any(), any()) } returns listOf(bankStatementItem).right()
    }

    @MockBean(ManualEntryService::class)
    fun manualEntryService() = manualEntryService
    private val manualEntryService: ManualEntryService = mockk() {
        every { listAllForWalletMember(any(), any()) } returns emptyList()
    }

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        accountRepository.create(walletFixture.founderAccount)
        walletRepository.save(wallet)
        val category = WalletBillCategory(walletId = wallet.id, categoryId = PFMCategoryId("category-id"), name = "Teste", icon = "EDUCACAO", default = false, enabled = true)
        walletBillCategoryDbRepository.save(category)

        every {
            pfmWalletCategoryService.findWalletCategories(any())
        } returns listOf(category)
    }

    @Test
    fun `should return no active bills on empty database`() {
        cleanupDb(dynamoDB)
        val bills = retrieveBills()
        Assertions.assertTrue(bills.isEmpty())
    }

    @Test
    fun `deve retornar todas as bills independente da data de vencimento efetiva`() {
        loadAccountIntoDb(dynamoDB)

        listOf(
            LocalDate.now(),
            LocalDate.now().plusMonths(10),
            LocalDate.now().minusMonths(10),
            LocalDate.now().plusYears(3),
            LocalDate.now().minusDays(20),
        )
            .forEach {
                addBillIntoDb(dynamoClient, activeBill.copy(billId = BillId(), dueDate = it, effectiveDueDate = it))
            }

        retrieveBills() shouldHaveSize 5
    }

    @Test
    fun `should return active scheduled bill`() {
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoClient, scheduledBillView.copy(tags = setOf(BillTag.ONBOARDING_TEST_PIX), categoryId = PFMCategoryId(value = "category-id"), categorySuggestions = listOf(BillCategorySuggestion(categoryId = PFMCategoryId(value = "category-id"), probability = 60))))
        val bills = retrieveBills()
        bills.size shouldBe 1
        val actual = bills[0]
        assertEquals(scheduledBillView.billId.value, actual.id)
        actual.warningCode shouldBe WarningCode.NONE.name
        assertEquals(scheduledBillView.payerAlias, actual.payer?.alias)

        actual.schedule?.date shouldBe scheduledBillView.schedule?.date?.format(dateFormat)
        actual.schedule?.waitingFunds shouldBe false
        actual.schedule?.waitingRetry shouldBe false
        actual.fichaCompensacaoType.shouldBeNull()
        actual.tags shouldBe setOf(BillTag.ONBOARDING_TEST_PIX)
        actual.categorySuggestions.size shouldBe 1

        with(actual.categorySuggestions.first()!!) {
            name shouldBe "Teste"
            icon shouldBe "EDUCACAO"
            billCategoryId shouldBe "category-id"
        }

        with(actual.category!!) {
            name shouldBe "Teste"
            icon shouldBe "EDUCACAO"
            billCategoryId shouldBe "category-id"
        }

        val expected = BillSourceTO(type = "Webapp", accountId = wallet.founder.accountId.value)
        assertEquals(expected, actual.source)

        LocalDate.parse(actual.effectiveDueDate, dateFormat) shouldHaveSameDayAs scheduledBillView.effectiveDueDate
    }

    @Test
    fun `should return active scheduled bill with batchSchedulingId`() {
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(
            dynamoClient,
            scheduledBillView.copy(schedule = scheduledBillView.schedule?.copy(batchSchedulingId = BatchSchedulingId("ABC-123"))),
        )
        val bills = retrieveBills()
        bills.size shouldBe 1
        with(bills[0]) {
            this.batchSchedulingId shouldBe "ABC-123"
        }
    }

    @Test
    fun `should return externalId for bills with external reference`() {
        loadAccountIntoDb(dynamoDB)

        scheduledBillView.copy(
            externalId = ExternalBillId("123", ExternalBillProvider.ORIGINAL),
            source = ActionSource.DirectDebit("********"),
        ).also {
            addBillIntoDb(dynamoClient, it)
        }

        retrieveBills().first().shouldNotBeNull().also {
            it.externalId.shouldNotBeNull().value shouldBe "123"
            it.externalId.shouldNotBeNull().provider shouldBe ExternalBillProvider.ORIGINAL
            it.source.externalId.shouldNotBeNull() shouldBe "********"
        }
    }

    @Test
    fun `should return paid amount for a legacy paid bill which does not have amountPaid stored`() {
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoClient, paidBill.copy(amountPaid = null))

        val bills = retrieveBills()

        bills.size shouldBe 1
        with(bills[0]) {
            this.amountPaid shouldBe this.amountTotal
            this.status shouldBe BillStatus.PAID.name
        }
    }

    @Test
    fun `should return bill paid with its paid amount`() {
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoClient, paidBill)

        val bills = retrieveBills()

        bills.size shouldBe 1
        with(bills[0]) {
            this.amountPaid shouldBe paidBill.amountPaid
            this.status shouldBe BillStatus.PAID.name
            this.subscriptionFee shouldBe false
            this.transactionCorrelationId.shouldNotBeNull() shouldBe paidBill.transactionCorrelationId
        }
    }

    @Test
    fun `deve retornar conta de assinatura da Friday`() {
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoClient, paidBill.copy(subscriptionFee = true))

        val bills = retrieveBills()

        bills.size shouldBe 1
        with(bills[0]) {
            this.subscriptionFee shouldBe true
        }
    }

    @Test
    fun `should return CARTAO_DE_CREDITO when bill is ficha_compensacao CARTAO_DE_CREDITO`() {
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoClient, getActiveBillFicha(walletId = wallet.id))
        val bills = retrieveBills()
        bills.size shouldBe 1
        val actual = bills[0]

        actual.fichaCompensacaoType shouldBe FichaCompensacaoType.CARTAO_DE_CREDITO
    }

    @Test
    fun `should return CARTAO_DE_CREDITO when bill is ficha_compensacao CH_CHEQUE but issued by nu bank`() {
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(
            dynamoClient,
            getActiveBillFicha(
                walletId = wallet.id,
                type = FichaCompensacaoType.CH_CHEQUE,
                recipient = Recipient(name = "NU PAGAMENTOS S/A", document = NU_BANK_DOCUMENT),
            ),
        )
        val bills = retrieveBills()
        bills.size shouldBe 1
        val actual = bills[0]

        actual.fichaCompensacaoType shouldBe FichaCompensacaoType.CARTAO_DE_CREDITO
    }

    @Test
    fun `should return OUTROS when bill is ficha_compensacao and fichaCompensacaoType is null`() {
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoClient, getActiveBillFicha(walletId = wallet.id, null))
        val bills = retrieveBills()
        bills.size shouldBe 1
        val actual = bills[0]

        actual.fichaCompensacaoType shouldBe FichaCompensacaoType.OUTROS
    }

    @Test
    fun `should return OUTROS when bill is ficha_compensacao CH_CHEQUE`() {
        loadAccountIntoDb(dynamoDB)
        val ficha =
            getActiveBillFicha(walletId = wallet.id).copy(fichaCompensacaoType = FichaCompensacaoType.CH_CHEQUE)
        addBillIntoDb(dynamoClient, ficha)
        val bills = retrieveBills()
        bills.size shouldBe 1
        val actual = bills[0]

        actual.fichaCompensacaoType shouldBe FichaCompensacaoType.OUTROS
    }

    @Test
    fun `should return overdue bills`() {
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoClient, activeBill)
        addBillIntoDb(dynamoClient, overdueBill)
        val bills = retrieveBills()
        assertEquals(2, bills.size)
        val actual = bills.first { it.id == overdueBill.billId.value }
        assertEquals(overdueBill.billId.value, actual.id)
        assertEquals(overdueBill.payerName, actual.payer!!.name)
        assertEquals(overdueBill.payerDocument, actual.payer!!.document)
        assertEquals(overdueBill.payerAlias, actual.payer?.alias)
    }

    @Test
    fun `should return invoice bills`() {
        loadAccountIntoDb(dynamoDB)
        val invoice = getInvoiceBill(wallet.id)
        addBillIntoDb(dynamoClient, invoice)
        val bills = retrieveBills()
        assertEquals(1, bills.size)
        val actual = bills[0]
        assertEquals(invoice.billId.value, actual.id)

        actual.schedule shouldBe null
    }

    @Test
    fun `should return ongoing scheduled when scheduling has started`() {
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoClient, scheduledOngoingBillView)

        val bills = retrieveBills()
        bills.size shouldBe 1
        val actual = bills[0]
        actual.id shouldBe scheduledOngoingBillView.billId.value

        actual.schedule?.waitingRetry shouldBe false
        actual.schedule?.waitingFunds shouldBe false
        actual.schedule?.date shouldBe scheduledOngoingBillView.schedule?.date?.format(dateFormat)
    }

    @Test
    fun `should return waitingFunds when bill is postponed by insufficient funds`() {
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoClient, scheduledPostponedByMissingFundsBillView)

        val bills = retrieveBills()
        bills.size shouldBe 1
        val actual = bills[0]
        actual.id shouldBe scheduledOngoingBillView.billId.value

        actual.schedule?.waitingFunds shouldBe true
        actual.schedule?.waitingRetry shouldBe false
        actual.schedule?.date shouldBe scheduledOngoingBillView.schedule?.date?.format(dateFormat)
    }

    @Test
    fun `should return waitingRetry when scheduling has started and bill payment failed`() {
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoClient, scheduledWaitingRetryBillView)

        val bills = retrieveBills()
        bills.size shouldBe 1
        val actual = bills.first()
        actual.id shouldBe scheduledWaitingRetryBillView.billId.value

        actual.schedule?.waitingFunds shouldBe false
        actual.schedule?.waitingRetry shouldBe true
        actual.schedule?.date shouldBe scheduledWaitingRetryBillView.schedule?.date?.format(dateFormat)
    }

    @Test
    fun `should return pix by key`() {
        loadAccountIntoDb(dynamoDB)
        val pix = Bill.build(pixKeyAdded.copy(walletId = wallet.id))
        billRepository.save(pix)

        val bills = retrieveBills()

        bills.size shouldBe 1
        with(bills[0]) {
            id shouldBe pix.billId.value
            with(billRecipient!!) {
                pixKey!!.value shouldBe pix.recipient?.pixKeyDetails?.key?.value
                pixKey!!.type shouldBe pix.recipient?.pixKeyDetails?.key?.type
                bankDetails shouldBe null
            }
            fichaCompensacaoType.shouldBeNull()
        }
    }

    @Test
    fun `should return pix by account`() {
        loadAccountIntoDb(dynamoDB)
        val pix = Bill.build(pixAdded.copy(walletId = wallet.id))
        billRepository.save(pix)

        val bills = retrieveBills()

        bills.size shouldBe 1
        with(bills[0]) {
            id shouldBe pix.billId.value
            billRecipient!!.pixKey shouldBe null
            with(billRecipient!!.bankDetails!!) {
                accountNo shouldBe pix.recipient?.bankAccount?.accountNo
                accountDv shouldBe pix.recipient?.bankAccount?.accountDv
                ispb shouldBe pix.recipient?.bankAccount?.ispb
            }
        }
    }

    @Test
    fun `should return all bills ordered by effectiveduedate`() {
        val ignoredBill = getIgnoredBill(walletId = wallet.id)

        val activeBill = getActiveBill(wallet.id, accountId = wallet.founder.accountId).copy(
            dueDate = getLocalDate().plusDays(3),
            effectiveDueDate = getLocalDate().plusDays(5),
        )

        listOf(activeBill, ignoredBill, overdueBill, paidBill).forEach { addBillIntoDb(dynamoClient, it) }

        val bills = retrieveBills()
        val billsInOrder = listOf(overdueBill, paidBill, ignoredBill, activeBill)
        assertEquals(billsInOrder.size, bills.size)
        assertEquals("2020-10-10", bills[1].paidDate)

        billsInOrder.forEachIndexed { index, currentBill ->
            assertEquals(currentBill.billId.value, bills[index].id)
            assertEquals(currentBill.payerAlias, bills[index].payer?.alias)
        }
    }

    @Test
    fun `should return ignored bills`() {
        val expectedBill = getIgnoredBill(walletId = wallet.id)
        addBillIntoDb(dynamoClient, expectedBill)
        val bills = retrieveBills()
        assertEquals(1, bills.size)
        val actual = bills[0]
        assertEquals(expectedBill.billId.value, actual.id)
    }

    @Test
    fun `should return bills when a new bill is created`() {
        val expectedBill = Bill.build(billAddedFicha.copy(walletId = wallet.id, brand = "Visa"))
        billRepository.save(expectedBill)
        val bills = retrieveBills()
        assertEquals(1, bills.size)

        bills[0].run {
            id shouldBe expectedBill.billId.value
            billRecipient!!.document shouldBe expectedBill.recipient!!.document
            payer!!.name shouldBe billAddedFicha.payerName
            payer!!.document shouldBe billAddedFicha.document
            brand shouldBe "Visa"
        }
    }

    @Test
    fun `should return warningCode on payment error`() {
        val expectedBill = Bill.build(
            billAdded.copy(walletId = wallet.id),
            billPaymentFailedRetryable.copy(errorSource = ErrorSource.ACQUIRER),
        )
        billRepository.save(expectedBill)

        val bills = retrieveBills()

        bills.size shouldBe 1
        bills[0].warningCode shouldBe WarningCode.CREDIT_CARD_PAYMENT.name
    }

    @Test
    fun `should return paid externally informed by when bill was marked as externally paid`() {
        val billMarkedAsPaid = BillMarkedAsPaid(
            billId = billAdded.billId,
            created = Instant.now().toEpochMilli(),
            walletId = wallet.id,
            actionSource = ActionSource.Api(accountId = walletFixture.assistantAccount.accountId),
            amountPaid = billAdded.amountTotal,
        )

        val expectedBill = Bill.build(
            billAdded.copy(walletId = wallet.id),
            billMarkedAsPaid,
        )
        billRepository.save(expectedBill)

        val bills = retrieveBills()

        bills.size shouldBe 1
        bills[0].markedAsPaidBy shouldBe walletFixture.assistantAccount.accountId.value
        bills[0].amountPaid shouldBe billAdded.amountTotal
    }

    @Test
    fun `should return recurrence information when bill is recurrent`() {
        val recurrence = weeklyRecurrenceNoEndDate
        val rule = recurrence.rule
        val recurrentBillAddedEvent = invoiceAdded.copy(
            recurrenceRule = rule,
            actionSource = ActionSource.Recurrence(recurrence.id, role = Role.OWNER),
        )
        billRepository.save(Bill.build(recurrentBillAddedEvent.copy(walletId = wallet.id)))

        val bills = retrieveBills()

        bills.size shouldNotBe 0
        var count = 1
        bills.forEach {
            with(it.recurrence!!) {
                id shouldBe recurrence.id.value
                startDate shouldBe rule.startDate.format(dateFormat)
                frequency shouldBe rule.frequency.name
                pattern shouldBe rule.pattern
                endDate shouldBe rule.endDate?.format(dateFormat)
                occurrence shouldBe count++
            }
        }
    }

    @Test
    fun `should return recurrence information when bill is recurrent and user has wallet`() {
        val recurrence = weeklyRecurrenceNoEndDate
        val rule = recurrence.rule
        val recurrentBillAddedEvent = invoiceAdded.copy(
            recurrenceRule = rule,
            actionSource = ActionSource.WalletRecurrence(accountId = wallet.founder.accountId, recurrence.id),
        )
        billRepository.save(Bill.build(recurrentBillAddedEvent.copy(walletId = wallet.id)))
        val bills = retrieveBills()

        bills.size shouldNotBe 0
        val bill = bills.first()
        with(bill.recurrence!!) {
            id shouldBe recurrence.id.value
            startDate shouldBe rule.startDate.format(dateFormat)
            frequency shouldBe rule.frequency.name
            pattern shouldBe rule.pattern
            endDate shouldBe rule.endDate?.format(dateFormat)
            occurrence shouldBe 1
        }
        bill.source shouldBe BillSourceTO(
            type = ActionSourceType.Recurrence.name,
            accountId = wallet.founder.accountId.value,
        )
    }

    @Test
    fun `should return source with accountId on bill added by mailbox`() {
        val billAdded = billAdded.copy(
            walletId = wallet.id,
            actionSource = ActionSource.WalletMailBox(
                from = "<EMAIL>",
                accountId = wallet.founder.accountId,
            ),
        )
        billRepository.save(Bill.build(billAdded))
        val bill = retrieveBills().single()
        bill.source shouldBe BillSourceTO(
            type = ActionSourceType.MailBox.name,
            accountId = wallet.founder.accountId.value,
            email = "<EMAIL>",
        )
    }

    @Test
    fun `should return source with accountId on bill added by api`() {
        val billAdded = billAdded.copy(
            walletId = wallet.id,
            actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
        )
        billRepository.save(Bill.build(billAdded))
        val bill = retrieveBills().single()
        bill.source shouldBe BillSourceTO(
            type = ActionSourceType.Webapp.name,
            accountId = wallet.founder.accountId.value,
        )
    }

    @Test
    fun `should return bill when source is System`() {
        val billAdded = billAdded.copy(
            walletId = wallet.id,
            actionSource = ActionSource.System,
        )
        billRepository.save(Bill.build(billAdded))
        val bill = retrieveBills().single()
        bill.source shouldBe BillSourceTO(
            type = ActionSourceType.Unknown.name,
        )
    }

    @Test
    fun `should return bill with its similar bill when there is a duplicated transfer`() {
        val expectedBill = Bill.build(pixAdded.copy(walletId = wallet.id))
        billRepository.save(expectedBill)

        val duplicatedBill =
            Bill.build(pixAdded.copy(billId = BillId("DUPLICADO"), walletId = wallet.id))
        billRepository.save(duplicatedBill)

        val bills = retrieveBills()
        assertEquals(2, bills.size)

        bills.forEach {
            it.possibleDuplicateBills shouldNotBe null
            it.possibleDuplicateBills shouldHaveSize 1
            it.possibleDuplicateBills.first().billId shouldNotBe it.id
            it.possibleDuplicateBills.first().dueDate shouldBe it.effectiveDueDate
        }
    }

    @Test
    fun `should return bill with similar bill when there is a duplicated transfer but user can not view the other payment`() {
        val expectedBill = Bill.build(pixAdded.copy(walletId = wallet.id))
        billRepository.save(expectedBill)

        val duplicatedBill =
            Bill.build(
                pixAdded.copy(
                    billId = BillId("DUPLICADO"),
                    walletId = wallet.id,
                    actionSource = ActionSource.Api(accountId = walletFixture.ultraLimitedParticipant.accountId),
                ),
            )
        billRepository.save(duplicatedBill)

        val bills = retrieveBills(account = walletFixture.ultraLimitedParticipantAccount)
        assertEquals(1, bills.size)

        bills.forEach {
            it.possibleDuplicateBills.shouldNotBeEmpty()
            it.possibleDuplicateBills.first().billId shouldBe expectedBill.billId.value
            it.possibleDuplicateBills.first().dueDate shouldBe getLocalDate().format(dateFormat)
        }
    }

    @Test
    fun `should return bill without similar bill when there isn't a duplicated transfer `() {
        val expectedBill = Bill.build(pixAdded.copy(walletId = wallet.id))
        billRepository.save(expectedBill)

        val duplicatedBill =
            Bill.build(
                pixAdded.copy(
                    billId = BillId("NAO ESTÁ DUPLICADO"),
                    amountTotal = pixAdded.amount + 1,
                    walletId = wallet.id,
                ),
            )
        billRepository.save(duplicatedBill)

        val bills = retrieveBills()
        assertEquals(2, bills.size)

        bills.forEach {
            it.possibleDuplicateBills.shouldBeEmpty()
        }
    }

    @Test
    fun `deve retornar detalhes do pagamento com cartão de crédito quando a bill for paga parcelada`() {
        val billAdded = billAdded.copy(walletId = wallet.id)
        val billPaymentScheduled = billPaymentScheduled.copy(
            infoData = BillPaymentScheduledCreditCardInfo(
                paymentMethodId = PAYMENT_METHOD_ID,
                netAmount = 1000,
                feeAmount = 40,
                installments = 10,
                fee = 4.0,
                calculationId = null,
            ),
        )

        billRepository.save(Bill.build(billAdded, billPaymentScheduled, billPaid))

        val bills = retrieveBills()

        assertEquals(1, bills.size)

        with(bills.single()) {
            with(this.paymentDetails) {
                this?.installments shouldBe 10
                this?.installmentAmount shouldBe 104
                this?.fee shouldBe 400
                this?.feeAmount shouldBe 40
            }
        }
    }

    private fun retrieveBills(account: Account = walletFixture.founderAccount): List<BillTO> {
        val cookie = buildCookie(account)

        val request = HttpRequest
            .GET<EntriesTO>("/entries")
            .cookie(cookie)
            .header("X-API-VERSION", "2")
            .header("X-WALLET-ID", wallet.id.value)

        return client.toBlocking()
            .retrieve(
                request,
                Argument.of(EntriesTO::class.java),
            ).bills
    }
}