package ai.friday.billpayment.integration.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.accountRegisterDataWithDocumentInfo
import ai.friday.billpayment.adapters.api.AccountRegisterDataTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.OriginalOcrAndPersonDataDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.liveness.LivenessErrors
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.liveness.LivenessSelfieError
import ai.friday.billpayment.app.liveness.LivenessService
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.generateCookie
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
class LivenessEnrollmentIntegrationTest(
    embeddedServer: EmbeddedServer,
    private val dynamoDB: AmazonDynamoDB,
) {

    val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val enhancedClient = getDynamoDB()
    private val accountRegisterDAO = AccountRegisterDynamoDAO(enhancedClient)
    private val originalOcrAndPersonDataDAO = OriginalOcrAndPersonDataDynamoDAO(enhancedClient)

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val accountRegisterRepository =
        AccountRegisterDbRepository(accountRegisterDAO, originalOcrAndPersonDataDAO, mockk())

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private lateinit var accountId: AccountId

    @MockBean(LivenessService::class)
    fun getLivenessService() = livenessService
    private val livenessService = mockk<LivenessService> {
        every {
            enroll(any())
        } returns LivenessId("FAKE-LID").right()
        every {
            retrieveEnrollmentSelfie(any())
        } returns LivenessSelfieError.Unavailable.left()
    }

    private fun buildRequest(
        cookie: Cookie,
    ): MutableHttpRequest<String> {
        return HttpRequest.PUT("/register/liveness/enroll", "")
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        val partialAccount = accountRepository.create(
            "fake_user",
            EmailAddress("<EMAIL>"),
            registrationType = RegistrationType.BASIC,
        )
        accountId = partialAccount.id
        accountRegisterRepository.save(accountRegisterDataWithDocumentInfo.copy(accountId = accountId))
    }

    @Test
    fun `should return livenessId`() {
        val response = client.toBlocking().exchange(
            buildRequest(
                cookie = generateCookie(accountId, Role.GUEST),
            ),
            Argument.of(AccountRegisterDataTO::class.java),
            Argument.of(ResponseTO::class.java),
        )

        response.status shouldBe HttpStatus.OK
        val receivedLivenessId = response.body()?.livenessId
        receivedLivenessId.shouldNotBeNull()
        with(accountRegisterRepository.findByAccountId(accountId)) {
            livenessId?.value shouldBe receivedLivenessId
        }
    }

    @Test
    fun `deve retornar CONFLICT quando a criacao do enrollment retornar EnrolmentUnavailable`() {
        every {
            livenessService.enroll(any())
        } returns LivenessErrors.EnrollmentUnavailable.left()

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(
                buildRequest(
                    cookie = generateCookie(accountId, Role.GUEST),
                ),
                Argument.of(AccountRegisterDataTO::class.java),
                Argument.of(ResponseTO::class.java),
            )
        }

        thrown.status shouldBe HttpStatus.CONFLICT
    }
}