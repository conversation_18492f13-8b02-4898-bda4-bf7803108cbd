package ai.friday.billpayment.integration.api

import DynamoDB<PERSON>tils.setupDynamoDB
import DynamoDB<PERSON>tils.setupDynamoDBAsync
import ai.friday.billpayment.adapters.api.AddConcessionariaTO
import ai.friday.billpayment.adapters.api.AddFichaDeCompensacaoTO
import ai.friday.billpayment.adapters.api.BillSourceTO
import ai.friday.billpayment.adapters.api.BillTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.arbi.ArbiValidationResponse
import ai.friday.billpayment.adapters.celcoin.CelcoinBillValidationResponse
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_TABLE_NAME
import ai.friday.billpayment.adapters.dynamodb.BillAddedEntity
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.FeaturesDbRepository
import ai.friday.billpayment.adapters.dynamodb.FeaturesDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.FichaCompensacaoAddedEntity
import ai.friday.billpayment.adapters.dynamodb.GlobalSecondaryIndexNames
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.micronaut.FeaturesConfigurationMicronaut
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.AddBillError
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.CreateBoletoRequest
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.bill.toEpochMillis
import ai.friday.billpayment.app.integrations.BillValidationService
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.BillValidationResponse
import ai.friday.billpayment.app.payment.DivergentPayment
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.payment.NU_BANK_DOCUMENT
import ai.friday.billpayment.app.payment.PartialPayment
import ai.friday.billpayment.app.payment.PartialPaymentAmountType
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.fichaRegisterData
import ai.friday.billpayment.integration.CONCESSIONARIA_DIGITABLE_LINE
import ai.friday.billpayment.integration.DIGITABLE_LINE
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2
import ai.friday.billpayment.integration.LONG_DESCRIPTION
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.cleanupDb
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.createLockTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.integration.loadBalancePaymentMethod
import ai.friday.billpayment.integration.onWallet
import ai.friday.billpayment.successConcessionariaValidationResponse
import ai.friday.billpayment.sundayDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.timeFormat
import ai.friday.morning.date.withGivenDateTime
import arrow.core.getOrElse
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.amazonaws.services.dynamodbv2.document.DynamoDB
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.date.shouldHaveSameDayAs
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.PropertySource
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import java.math.BigInteger
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import java.util.stream.Stream
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource
import org.junit.jupiter.params.provider.ValueSource

private const val FRIDAY_DOCUMENT = "34701685000115"

@MicronautTest(environments = [FRIDAY_ENV])
@PropertySource(
    value = [
        Property(name = "createBillService.idSubscriptionBy.recipientDocument", value = "34701685000115"),
    ],
)
class AddBillIntegrationTest(
    embeddedServer: EmbeddedServer,
    val dynamoDB: AmazonDynamoDB,
) {

    private val featureConfigurationMock = mockk<FeatureConfiguration>(relaxed = true) {
        every {
            blockDuplicateByIdNumber
        } returns true
    }

    @field:Property(name = "lock.tableName")
    lateinit var lockTableName: String
    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val dynamoClient = setupDynamoDB()
    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(dynamoClient),
        refundedClient = RefundedBillDynamoDAO(dynamoClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(dynamoClient),
    )
    private val billEventDAO = BillEventDynamoDAO(dynamoClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(dynamoClient)
    private val transactionDynamo = TransactionDynamo(dynamoClient)
    private val billEventRepository = BillEventDBRepository(
        billEventDAO,
        uniqueConstraintDAO,
        featureConfigurationMock,
        transactionDynamo,
    )

    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val accountDAO = AccountDynamoDAO(dynamoClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(dynamoClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(dynamoClient)
    private val nsuDAO = NSUDynamoDAO(dynamoClient)

    private val featuresDbRepository = FeaturesDbRepository(client = FeaturesDynamoDAO(cli = dynamoDbEnhancedClient))

    @MockBean(FeaturesDbRepository::class)
    fun featuresDbRepository() = featuresDbRepository

    @MockBean(FeaturesConfigurationMicronaut::class)
    fun featureConfiguration(): FeatureConfiguration = featureConfigurationMock

    @MockBean(WalletDbRepository::class)
    fun walletRepository(): WalletRepository = walletRepository
    private val walletRepository: WalletRepository = mockk() {
        every {
            findWalletOrNull(wallet.id)
        } returns wallet

        every {
            findWallet(wallet.id)
        } returns wallet
    }

    private val validationService: BillValidationService = mockk(relaxed = true)

    @MockBean(BillValidationService::class)
    fun getBillValidationService(): BillValidationService = validationService

    @MockBean(BoletoSettlementService::class)
    fun openBankingMock(): BoletoSettlementService = boletoSettlementMock
    private val boletoSettlementMock: BoletoSettlementService = mockk(relaxed = true)

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @BeforeEach
    fun setup() {
        setupDynamoDBAsync()
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        createLockTable(dynamoDB, lockTableName)
    }

    private val validConcessionariaTo = AddConcessionariaTO(
        digitableLine = CONCESSIONARIA_DIGITABLE_LINE,
        description = "description",
        dueDate = sundayDate.format(dateFormat),
    )

    private val validFichaCompensacaoTO =
        AddFichaDeCompensacaoTO(digitableLine = FICHA_DE_COMPENSACAO_DIGITABLE_LINE, description = "description")

    private val validAddConcessionariaRequest =
        HttpRequest.POST("/bill/concessionaria", validConcessionariaTo).onWallet(wallet)

    private val validAddFichaCompensacaoRequest =
        HttpRequest.POST("/bill/ficha-compensacao", validFichaCompensacaoTO).onWallet(wallet)

    private val arbiValidationResponse =
        ArbiValidationResponse(billRegisterData = fichaRegisterData, paymentStatus = 12, resultado = "SUCESSO")

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @ParameterizedTest
    @CsvSource(
        "Invalid,description,2020-04-20,Digitable line",
        "$DIGITABLE_LINE,$LONG_DESCRIPTION,2020-04-20,Description",
        "$DIGITABLE_LINE,description,2020-20-1,Due date",
    )
    fun `should return bad request on invalid add bill request`(
        digitableLine: String,
        description: String,
        dueDate: String,
        expectedMessage: String,
    ) {
        val to = AddConcessionariaTO(digitableLine = digitableLine, description = description, dueDate = dueDate)
        val request = HttpRequest.POST("/bill/concessionaria", to).onWallet(wallet)
        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(request, Unit::class.java)
        }
        assertEquals(HttpStatus.BAD_REQUEST, thrown.status)
        thrown.message!! shouldContain expectedMessage
    }

    @Test
    fun `should return server error on validation failure at open banking service`() {
        every { boletoSettlementMock.validateBill(any<CreateBoletoRequest>()) } answers {
            CelcoinBillValidationResponse(
                "171",
                "FALHA NA COMUNICACAO COM A INSTITUICAO",
            )
        }
        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(
                validAddConcessionariaRequest,
                Argument.of(BillTO::class.java),
                Argument.of(ResponseTO::class.java),
            )
        }
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, thrown.status)
    }

    @Test
    fun `should return bad request on barcode not found on central bank`() {
        every { boletoSettlementMock.validateBill(any<CreateBoletoRequest>()) } answers {
            CelcoinBillValidationResponse(
                "642",
                "CODIGO DE BARRAS NAO LOCALIZADO NA BASE CENTRALIZADA",
            )
        }
        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(validAddConcessionariaRequest, Unit::class.java)
        }
        assertEquals(HttpStatus.BAD_REQUEST, thrown.status)
        assertEquals(thrown.message, "CODIGO DE BARRAS NAO LOCALIZADO NA BASE CENTRALIZADA")
    }

    @Test
    fun `should return bad request on payment not authorized`() {
        every { boletoSettlementMock.validateBill(any<CreateBoletoRequest>()) } answers {
            CelcoinBillValidationResponse(
                "481",
                "CEDENTE NÃO AUTORIZADO (481)",
            )
        }

        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(
                validAddConcessionariaRequest,
                Argument.of(BillTO::class.java),
                Argument.of(ResponseTO::class.java),
            )
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        with(thrown.response.getBody(ResponseTO::class.java).get()) {
            this.code shouldBe "4007"
            this.message shouldBe "Cedente não autorizado"
        }
    }

    @Test
    fun `should return bad request on bill already settled`() {
        every { boletoSettlementMock.validateBill(any<CreateBoletoRequest>()) } answers {
            CelcoinBillValidationResponse(
                "642",
                "CODIGO DE BARRAS NAO LOCALIZADO NA BASE CENTRALIZADA",
            )
        }
        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(validAddConcessionariaRequest, Unit::class.java)
        }
        assertEquals(HttpStatus.BAD_REQUEST, thrown.status)
        assertEquals(thrown.message, "CODIGO DE BARRAS NAO LOCALIZADO NA BASE CENTRALIZADA")
    }

    @Test
    fun `should create concessionaria bill on valid owner request`() {
        every { boletoSettlementMock.validateBill(any<CreateBoletoRequest>()) } answers { successConcessionariaValidationResponse }

        val result = client.toBlocking().exchange(validAddConcessionariaRequest, BillTO::class.java)

        val billTO = result.getBody(BillTO::class.java).get()

        assertEquals(HttpStatus.CREATED, result.status)
        assertConcessionaria(validConcessionariaTo.digitableLine, successConcessionariaValidationResponse)
        assertConcessionariaBillTO(billTO, validConcessionariaTo, successConcessionariaValidationResponse)
        LocalDate.parse(billTO.effectiveDueDate, dateFormat) shouldHaveSameDayAs sundayDate.plusDays(1)
        billTO.availablePaymentMethods.shouldContainExactlyInAnyOrder(
            PaymentMethodType.BALANCE,
            PaymentMethodType.CREDIT_CARD,
            PaymentMethodType.EXTERNAL,
        )
    }

    @Test
    fun `should create ficha de compensacao bill on valid owner request`() {
        loadAccountIntoDb(accountId = wallet.founder.accountId, amazonDynamoDB = dynamoDB)
        val internalAccountPaymentMethod = loadBalancePaymentMethod(
            accountRepository = AccountDbRepository(
                accountDAO = accountDAO,
                partialAccountDAO = partialAccountDAO,
                paymentMethodDAO = paymentMethodDAO,
                nsuDAO = nsuDAO,
                transactionDynamo = transactionDynamo,
            ),
            bankNo = 213L,
            bankAccountNo = BigInteger("12345"),
            bankAccountDv = "6",
            document = wallet.founder.document,
            accountId = wallet.founder.accountId.value,
        )
        every {
            walletRepository.findWallet(wallet.id)
        } returns wallet.copy(paymentMethodId = internalAccountPaymentMethod.id)
        val customResponse =
            arbiValidationResponse.copy(
                billRegisterData = fichaRegisterData.copy(
                    fichaCompensacaoType = FichaCompensacaoType.CH_CHEQUE,
                ),
            )
        every { validationService.validate(ofType(BarCode::class)) } answers { customResponse }

        val result = client.toBlocking().exchange(validAddFichaCompensacaoRequest, BillTO::class.java)

        val billTO = result.getBody(BillTO::class.java).get()

        val bill = billEventRepository.getBillById(BillId(billTO.id)).getOrElse { throw it }

        bill.idNumber shouldBe customResponse.billRegisterData!!.idNumber
        bill.subscriptionFee shouldBe false

        var billResult =
            billEventRepository.findLastBill(idNumber = bill.idNumber!!, walletId = bill.walletId)

        billResult.isRight() shouldBe true
        billResult.map { it.billId shouldBe bill.billId }

        billResult = billEventRepository.findLastBill(barCode = bill.barcode!!, walletId = bill.walletId)

        billResult.isRight() shouldBe true
        billResult.map { it.billId shouldBe bill.billId }

        assertEquals(HttpStatus.CREATED, result.status)
        assertFichaCompensacao(validFichaCompensacaoTO.digitableLine, customResponse)
        assertFichaDeCompensacaoBillTO(
            billTO,
            validFichaCompensacaoTO,
            customResponse,
            FichaCompensacaoType.OUTROS,
        )
        LocalDate.parse(billTO.effectiveDueDate, dateFormat) shouldHaveSameDayAs sundayDate.plusDays(1)
        bill.schedule shouldBe null
        billTO.availablePaymentMethods.shouldContainExactlyInAnyOrder(
            PaymentMethodType.BALANCE,
            PaymentMethodType.CREDIT_CARD,
            PaymentMethodType.EXTERNAL,
        )
    }

    @Test
    fun `should not create friday subscription on dry run`() {
        loadAccountIntoDb(accountId = wallet.founder.accountId, amazonDynamoDB = dynamoDB)
        val internalAccountPaymentMethod = loadBalancePaymentMethod(
            accountRepository = AccountDbRepository(
                accountDAO = accountDAO,
                partialAccountDAO = partialAccountDAO,
                paymentMethodDAO = paymentMethodDAO,
                nsuDAO = nsuDAO,
                transactionDynamo = transactionDynamo,
            ),
            bankNo = 213L,
            bankAccountNo = BigInteger("12345"),
            bankAccountDv = "6",
            document = wallet.founder.document,
            accountId = wallet.founder.accountId.value,
        )
        every {
            walletRepository.findWallet(wallet.id)
        } returns wallet.copy(paymentMethodId = internalAccountPaymentMethod.id)

        val customResponse =
            arbiValidationResponse.copy(
                billRegisterData = fichaRegisterData.copy(
                    fichaCompensacaoType = FichaCompensacaoType.CH_CHEQUE,
                    recipient = fichaRegisterData.recipient?.copy(
                        document = FRIDAY_DOCUMENT,
                    ),
                ),
            )
        every { validationService.validate(ofType(BarCode::class)) } answers { customResponse }

        val validDryRunAddFichaCompensacaoRequest =
            HttpRequest.POST("/bill/ficha-compensacao?dryRun=true", validFichaCompensacaoTO).onWallet(wallet)

        val result = client.toBlocking().exchange(validDryRunAddFichaCompensacaoRequest, BillTO::class.java)

        val billTO = result.getBody(BillTO::class.java).get()

        Assertions.assertThrows(IllegalStateException::class.java) {
            billRepository.findBill(
                BillId(billTO.id),
                wallet.id,
            )
        }

        assertEquals(HttpStatus.OK, result.status)
    }

    @Test
    fun `não deve marcar como assinatura friday se não for para o fundador da carteira`() {
        loadAccountIntoDb(accountId = wallet.founder.accountId, amazonDynamoDB = dynamoDB)
        val internalAccountPaymentMethod = loadBalancePaymentMethod(
            accountRepository = AccountDbRepository(
                accountDAO = accountDAO,
                partialAccountDAO = partialAccountDAO,
                paymentMethodDAO = paymentMethodDAO,
                nsuDAO = nsuDAO,
                transactionDynamo = transactionDynamo,
            ),
            bankNo = 213L,
            bankAccountNo = BigInteger("12345"),
            bankAccountDv = "6",
            document = wallet.founder.document,
            accountId = wallet.founder.accountId.value,
        )
        every {
            walletRepository.findWallet(wallet.id)
        } returns wallet.copy(paymentMethodId = internalAccountPaymentMethod.id)
        val customResponse =
            arbiValidationResponse.copy(
                billRegisterData = fichaRegisterData.copy(
                    fichaCompensacaoType = FichaCompensacaoType.CH_CHEQUE,
                    recipient = fichaRegisterData.recipient?.copy(
                        document = FRIDAY_DOCUMENT,
                    ),
                ),
            )
        every { validationService.validate(ofType(BarCode::class)) } answers { customResponse }

        val result = client.toBlocking().exchange(validAddFichaCompensacaoRequest, BillTO::class.java)

        val billTO = result.getBody(BillTO::class.java).get()

        val bill = billEventRepository.getBillById(BillId(billTO.id)).getOrElse { throw it }

        bill.idNumber shouldBe customResponse.billRegisterData!!.idNumber
        bill.subscriptionFee shouldBe false

        var billResult =
            billEventRepository.findLastBill(idNumber = bill.idNumber!!, walletId = bill.walletId)

        billResult.isRight() shouldBe true
        billResult.map { it.billId shouldBe bill.billId }

        billResult = billEventRepository.findLastBill(barCode = bill.barcode!!, walletId = bill.walletId)

        billResult.isRight() shouldBe true
        billResult.map { it.billId shouldBe bill.billId }

        assertEquals(HttpStatus.CREATED, result.status)
        assertFichaCompensacao(validFichaCompensacaoTO.digitableLine, customResponse)
        assertFichaDeCompensacaoBillTO(
            billTO,
            validFichaCompensacaoTO,
            customResponse,
            FichaCompensacaoType.OUTROS,
        )
        LocalDate.parse(billTO.effectiveDueDate, dateFormat) shouldHaveSameDayAs sundayDate.plusDays(1)
        bill.schedule?.date shouldBe null
    }

    @Test
    fun `should not create ficha de compensacao when exists bill with same idNumber`() {
        every { validationService.validate(ofType(BarCode::class)) } answers { arbiValidationResponse }

        billEventRepository.save(billAddedFichaEvent.copy(idNumber = fichaRegisterData.idNumber))
        billEventRepository.save(billPaymentScheduled)

        val request = HttpRequest.POST(
            "/bill/ficha-compensacao",
            validFichaCompensacaoTO.copy(digitableLine = FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2),
        ).onWallet(wallet)

        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(
                request,
                Argument.of(BillTO::class.java),
                Argument.of(ResponseTO::class.java),
            )
        }
        thrown.status shouldBe HttpStatus.BAD_REQUEST
        with(thrown.response.getBody(ResponseTO::class.java).get()) {
            message shouldBe AddBillError.BILL_ALREADY_INCLUDED.description
            BillId(bill!!.id) shouldBe billAddedFichaEvent.billId
        }
    }

    @Test
    fun `should create bill when ficha de compensacao type is credit card and due date is not in the past`() {
        val customResponse =
            arbiValidationResponse.copy(billRegisterData = fichaRegisterData.copy(fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO))
        every { validationService.validate(ofType(BarCode::class)) } answers { customResponse }

        val result = client.toBlocking().exchange(
            validAddFichaCompensacaoRequest,
            Argument.of(BillTO::class.java),
            Argument.of(ResponseTO::class.java),
        )

        val billTO = result.getBody(BillTO::class.java).get()

        assertEquals(HttpStatus.CREATED, result.status)
        assertFichaCompensacao(validFichaCompensacaoTO.digitableLine, customResponse)
        assertFichaDeCompensacaoBillTO(billTO, validFichaCompensacaoTO, customResponse)
        assertNotNull(
            DynamoDB(dynamoDB).getTable(BILL_PAYMENT_TABLE_NAME).getItem(
                BILL_PAYMENT_PARTITION_KEY,
                validFichaCompensacaoTO.digitableLine,
                BILL_PAYMENT_RANGE_KEY,
                "DIGITABLE#${wallet.id.value}#${
                arbiValidationResponse.billRegisterData?.dueDate?.format(
                    dateFormat,
                )
                }",
            ),
        )
        billTO.availablePaymentMethods.shouldContainExactlyInAnyOrder(
            PaymentMethodType.BALANCE,
            PaymentMethodType.EXTERNAL,
        )
    }

    @Test
    fun `should create bill when ficha de compensacao with divergent payment info`() {
        val customResponse =
            arbiValidationResponse.copy(
                billRegisterData = fichaRegisterData.copy(
                    divergentPayment = DivergentPayment(
                        minimumAmount = 10,
                        maximumAmount = 1000,
                        amountType = PartialPaymentAmountType.ANY_VALUE,
                        minimumPercentage = null,
                        maximumPercentage = null,
                    ),
                    partialPayment = PartialPayment(
                        acceptPartialPayment = true,
                        qtdPagamentoParcial = 99,
                        qtdPagamentoParcialRegistrado = 1,
                        saldoAtualPagamento = 100,
                    ),
                ),
            )
        every { validationService.validate(ofType(BarCode::class)) } answers { customResponse }

        val result = client.toBlocking().exchange(
            validAddFichaCompensacaoRequest,
            Argument.of(BillTO::class.java),
            Argument.of(ResponseTO::class.java),
        )

        val billTO = result.getBody(BillTO::class.java).get()

        assertEquals(HttpStatus.CREATED, result.status)
        assertFichaCompensacao(validFichaCompensacaoTO.digitableLine, customResponse)

        val billView = billRepository.findBill(BillId(billTO.id), wallet.id)
        billView.divergentPayment shouldBe customResponse.billRegisterData?.divergentPayment
        billView.partialPayment shouldBe customResponse.billRegisterData?.partialPayment

        assertFichaDeCompensacaoBillTO(billTO, validFichaCompensacaoTO, customResponse)
        assertNotNull(
            DynamoDB(dynamoDB).getTable(BILL_PAYMENT_TABLE_NAME).getItem(
                BILL_PAYMENT_PARTITION_KEY,
                validFichaCompensacaoTO.digitableLine,
                BILL_PAYMENT_RANGE_KEY,
                "DIGITABLE#${wallet.id.value}#${
                arbiValidationResponse.billRegisterData?.dueDate?.format(
                    dateFormat,
                )
                }",
            ),
        )
        billTO.availablePaymentMethods.shouldContainExactlyInAnyOrder(
            PaymentMethodType.BALANCE,
            PaymentMethodType.EXTERNAL,
            PaymentMethodType.CREDIT_CARD,
        )
    }

    @Test
    fun `should create bill when ficha de compensacao type is nu bank credit card`() {
        val customResponse =
            arbiValidationResponse.copy(
                billRegisterData = fichaRegisterData.copy(
                    fichaCompensacaoType = FichaCompensacaoType.CH_CHEQUE,
                    recipient = Recipient(
                        name = "NU",
                        document = NU_BANK_DOCUMENT,
                        alias = "NU",
                    ),
                ),
            )
        every { validationService.validate(ofType(BarCode::class)) } answers { customResponse }

        val result = client.toBlocking().exchange(
            validAddFichaCompensacaoRequest,
            Argument.of(BillTO::class.java),
            Argument.of(ResponseTO::class.java),
        )

        val billTO = result.getBody(BillTO::class.java).get()

        assertEquals(HttpStatus.CREATED, result.status)
        billTO.fichaCompensacaoType shouldBe FichaCompensacaoType.CARTAO_DE_CREDITO
        billTO.availablePaymentMethods.shouldContainExactlyInAnyOrder(
            PaymentMethodType.BALANCE,
            PaymentMethodType.EXTERNAL,
        )
    }

    @Test
    fun `should create bill when ficha de compensacao type is credit card and due date in the past`() {
        val overdueDueDate = getLocalDate().minusDays(1)
        every { validationService.validate(ofType(BarCode::class)) } answers {
            arbiValidationResponse.copy(
                billRegisterData = fichaRegisterData.copy(
                    fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO,
                    dueDate = overdueDueDate,
                ),
            )
        }

        val result = client.toBlocking().exchange(
            validAddFichaCompensacaoRequest,
            Argument.of(BillTO::class.java),
            Argument.of(ResponseTO::class.java),
        )
        val billTO = result.getBody(BillTO::class.java).get()

        result.status shouldBe HttpStatus.CREATED
        billTO.dueDate shouldBe overdueDueDate.format(dateFormat)
        billTO.availablePaymentMethods.shouldContainExactlyInAnyOrder(
            PaymentMethodType.BALANCE,
            PaymentMethodType.EXTERNAL,
        )
    }

    @Test
    fun `deve retornar que a conta já foi incluida caso retorne um número de cadastro antigo na validação`() {
        every { validationService.validate(ofType(BarCode::class)) } answers {
            arbiValidationResponse.copy(
                billRegisterData = fichaRegisterData.copy(
                    registrationUpdateNumber = 10L,
                ),
            )
        }
        billEventRepository.save(billAddedFichaEvent.copy(registrationUpdateNumber = 11L))

        val result = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(
                validAddFichaCompensacaoRequest,
                Argument.of(BillTO::class.java),
                Argument.of(ResponseTO::class.java),
            )
        }

        result.status shouldBe HttpStatus.BAD_REQUEST
        result.response.getBody(ResponseTO::class.java).get().code shouldBe "4004"
    }

    @Test
    fun `should create bill and invalidate existing bill when digitableline per account register exist with another due date`() {
        every { validationService.validate(ofType(BarCode::class)) } answers {
            arbiValidationResponse.copy(
                billRegisterData = fichaRegisterData,
            )
        }

        billEventRepository.save(billAddedFichaEvent)

        val result = client.toBlocking().exchange(
            validAddFichaCompensacaoRequest,
            Argument.of(BillTO::class.java),
            Argument.of(ResponseTO::class.java),
        )

        val billTO = result.getBody(BillTO::class.java).get()

        result.status shouldBe HttpStatus.CREATED
        billTO.possibleDuplicateBills shouldHaveSize 1
        billTO.possibleDuplicateBills.first().billId shouldBe billAddedFichaEvent.billId.value
        billTO.possibleDuplicateBills.first().dueDate shouldBe billAddedFichaEvent.effectiveDueDate.format(
            dateFormat,
        )

        assertNotNull(
            DynamoDB(dynamoDB).getTable(BILL_PAYMENT_TABLE_NAME).getItem(
                BILL_PAYMENT_PARTITION_KEY,
                validFichaCompensacaoTO.digitableLine,
                BILL_PAYMENT_RANGE_KEY,
                "DIGITABLE#${wallet.id.value}#${
                arbiValidationResponse.billRegisterData?.dueDate?.format(
                    dateFormat,
                )
                }",
            ),
        )
        assertNotNull(
            DynamoDB(dynamoDB).getTable(BILL_PAYMENT_TABLE_NAME).getItem(
                BILL_PAYMENT_PARTITION_KEY,
                validFichaCompensacaoTO.digitableLine,
                BILL_PAYMENT_RANGE_KEY,
                "DIGITABLE#${wallet.id.value}#${billAddedFichaEvent.dueDate.format(dateFormat)}",
            ),
        )

        var billResult = billEventRepository.getBillById(billAddedFichaEvent.billId)
        billResult.isRight() shouldBe true
        billResult.map {
            it.status shouldBe BillStatus.NOT_PAYABLE
            it.dueDate shouldBe billAddedFichaEvent.dueDate
        }

        billResult = billEventRepository.getBillById(BillId(billTO.id))
        billResult.isRight() shouldBe true
        billResult.map {
            it.status shouldBe BillStatus.ACTIVE
            it.dueDate shouldBe arbiValidationResponse.billRegisterData!!.dueDate
        }
    }

    @Test
    fun `should create bill and invalidate existing bill and remove schedule when digitableline per account register exist with another due date`() {
        every { validationService.validate(ofType(BarCode::class)) } answers {
            arbiValidationResponse.copy(
                billRegisterData = fichaRegisterData,
            )
        }

        billEventRepository.save(billAddedFichaEvent)
        billEventRepository.save(billPaymentScheduled)

        val result = client.toBlocking().exchange(
            validAddFichaCompensacaoRequest,
            Argument.of(BillTO::class.java),
            Argument.of(ResponseTO::class.java),
        )

        val billTO = result.getBody(BillTO::class.java).get()

        assertEquals(HttpStatus.CREATED, result.status)

        var billResult = billEventRepository.getBillById(billAddedFichaEvent.billId)
        billResult.isRight() shouldBe true
        billResult.map {
            it.status shouldBe BillStatus.NOT_PAYABLE
            it.dueDate shouldBe billAddedFichaEvent.dueDate
            it.isPaymentScheduled() shouldBe false
            it.history.filterIsInstance<BillPaymentScheduleCanceled>().size shouldBe 1
        }

        billResult = billEventRepository.getBillById(BillId(billTO.id))
        billResult.isRight() shouldBe true
        billResult.map {
            it.status shouldBe BillStatus.ACTIVE
            it.dueDate shouldBe arbiValidationResponse.billRegisterData!!.dueDate
        }
    }

    @Test
    fun `should not create bill neither invalidate existing bill neither remove schedule when digitableline per account register exist with another due date but dry run is true`() {
        every { validationService.validate(ofType(BarCode::class)) } answers {
            arbiValidationResponse.copy(
                billRegisterData = fichaRegisterData,
            )
        }
        billEventRepository.save(billAddedFichaEvent)
        billEventRepository.save(billPaymentScheduled)

        val request = HttpRequest.POST("/bill/ficha-compensacao?dryRun=true", validFichaCompensacaoTO).onWallet(wallet)
        val result =
            client.toBlocking().exchange(request, Argument.of(BillTO::class.java), Argument.of(ResponseTO::class.java))

        assertEquals(HttpStatus.OK, result.status)

        var billResult = billEventRepository.getBillById(billAddedFichaEvent.billId)
        billResult.isRight() shouldBe true
        billResult.map {
            it.status shouldBe BillStatus.ACTIVE
            it.schedule?.date shouldBe billPaymentScheduled.scheduledDate
            it.history.size shouldBe 2
        }
    }

    @Test
    fun `should create ficha de compensacao with possible duplicated when blockFeatureFlag is disabled and exists bill with same idNumber`() {
        every { featureConfigurationMock.blockDuplicateByIdNumber } returns false
        every { validationService.validate(ofType(BarCode::class)) } answers { arbiValidationResponse }

        val billEvent = billAddedFichaEvent.copy(
            idNumber = fichaRegisterData.idNumber,
            effectiveDueDate = getLocalDate(),
        )

        billEventRepository.save(billEvent)
        billRepository.save(Bill.build(billEvent))

        val request = HttpRequest.POST(
            "/bill/ficha-compensacao",
            validFichaCompensacaoTO.copy(digitableLine = FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2),
        ).onWallet(wallet)

        val response =
            client.toBlocking().exchange(request, Argument.of(BillTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.CREATED
        with(response.getBody(BillTO::class.java).get()) {
            possibleDuplicateBills shouldHaveSize 1
            possibleDuplicateBills.first().billId shouldBe billEvent.billId.value
            possibleDuplicateBills.first().dueDate shouldBe billEvent.effectiveDueDate.format(
                dateFormat,
            )
        }
    }

    @Test
    fun `should create ficha de compensacao credit card with possible duplicated when blockFeatureFlag is disabled and exists bill with same idNumber`() {
        withGivenDateTime(ZonedDateTime.of(2022, 2, 25, 15, 0, 0, 0, brazilTimeZone)) {
            every { featureConfigurationMock.blockDuplicateByIdNumber } returns false
            every { validationService.validate(ofType(BarCode::class)) } answers {
                arbiValidationResponse.copy(
                    billRegisterData = fichaRegisterData.copy(
                        fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO,
                        dueDate = getLocalDate(),
                    ),
                )
            }

            val billEvent = billAddedFichaEvent.copy(
                fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO,
                idNumber = fichaRegisterData.idNumber,
                effectiveDueDate = getLocalDate(),
            )
            billEventRepository.save(billEvent)
            billEventRepository.save(billPaymentScheduled)
            billRepository.save(Bill.build(billEvent, billPaymentScheduled))

            val request = HttpRequest.POST(
                "/bill/ficha-compensacao",
                validFichaCompensacaoTO.copy(digitableLine = FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2),
            ).onWallet(wallet)

            val response =
                client.toBlocking()
                    .exchange(request, Argument.of(BillTO::class.java), Argument.of(ResponseTO::class.java))

            response.status shouldBe HttpStatus.CREATED
            with(response.getBody(BillTO::class.java).get()) {
                possibleDuplicateBills shouldHaveSize 1
                possibleDuplicateBills.first().billId shouldBe billEvent.billId.value
                possibleDuplicateBills.first().dueDate shouldBe billEvent.effectiveDueDate.format(
                    dateFormat,
                )
            }
        }
    }

    @ParameterizedTest
    @MethodSource("shouldNotMarkAsNotPayableStatus")
    fun `should create bill and not change previous bill when it is in current state`(billEvent: BillEvent) {
        every { validationService.validate(ofType(BarCode::class)) } answers {
            arbiValidationResponse.copy(
                billRegisterData = fichaRegisterData,
            )
        }

        billEventRepository.save(billAddedFichaEvent)
        billEventRepository.save(billEvent)

        val result = client.toBlocking().exchange(
            validAddFichaCompensacaoRequest,
            Argument.of(BillTO::class.java),
            Argument.of(ResponseTO::class.java),
        )

        val billTO = result.getBody(BillTO::class.java).get()

        assertEquals(HttpStatus.CREATED, result.status)

        assertNotNull(
            DynamoDB(dynamoDB).getTable(BILL_PAYMENT_TABLE_NAME).getItem(
                BILL_PAYMENT_PARTITION_KEY,
                validFichaCompensacaoTO.digitableLine,
                BILL_PAYMENT_RANGE_KEY,
                "DIGITABLE#${wallet.id.value}#${
                arbiValidationResponse.billRegisterData?.dueDate?.format(
                    dateFormat,
                )
                }",
            ),
        )
        assertNotNull(
            DynamoDB(dynamoDB).getTable(BILL_PAYMENT_TABLE_NAME).getItem(
                BILL_PAYMENT_PARTITION_KEY,
                validFichaCompensacaoTO.digitableLine,
                BILL_PAYMENT_RANGE_KEY,
                "DIGITABLE#${wallet.id.value}#${billAddedFichaEvent.dueDate.format(dateFormat)}",
            ),
        )

        var billResult = billEventRepository.getBillById(billAddedFichaEvent.billId)
        billResult.isRight() shouldBe true
        billResult.map {
            it.history.size shouldBe 2
        }

        billResult = billEventRepository.getBillById(BillId(billTO.id))
        billResult.isRight() shouldBe true
        billResult.map {
            it.status shouldBe BillStatus.ACTIVE
            it.dueDate shouldBe arbiValidationResponse.billRegisterData!!.dueDate
        }
    }

    @Test
    fun `should not create bill when existing digitableline and existing bill is processing`() {
        every { validationService.validate(ofType(BarCode::class)) } answers {
            arbiValidationResponse.copy(
                billRegisterData = fichaRegisterData,
            )
        }

        billEventRepository.save(billAddedFichaEvent)
        billEventRepository.save(billPaymentStart)

        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(
                validAddFichaCompensacaoRequest,
                Argument.of(BillTO::class.java),
                Argument.of(ResponseTO::class.java),
            )
        }
        thrown.status shouldBe HttpStatus.BAD_REQUEST
        with(thrown.response.getBody(ResponseTO::class.java).get()) {
            message shouldBe AddBillError.BILL_ALREADY_INCLUDED.description
            BillId(bill!!.id) shouldBe billAddedFichaEvent.billId
        }
    }

    @Test
    fun `should not create bill when existing digitableline and existing bill has same due date`() {
        every { validationService.validate(ofType(BarCode::class)) } answers {
            arbiValidationResponse.copy(
                billRegisterData = fichaRegisterData.copy(
                    dueDate = billAddedFichaEvent.dueDate,
                ),
            )
        }

        billEventRepository.save(billAddedFichaEvent)

        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(
                validAddFichaCompensacaoRequest,
                Argument.of(BillTO::class.java),
                Argument.of(ResponseTO::class.java),
            )
        }
        thrown.status shouldBe HttpStatus.BAD_REQUEST
        with(thrown.response.getBody(ResponseTO::class.java).get()) {
            message shouldBe AddBillError.BILL_ALREADY_INCLUDED.description
            BillId(bill!!.id) shouldBe billAddedFichaEvent.billId
        }
    }

    @Test
    fun `should not create bill when ficha de compensacao type is credit card and already exists bill with same idNumber and due date`() {
        every { validationService.validate(ofType(BarCode::class)) } answers {
            arbiValidationResponse.copy(
                billRegisterData = fichaRegisterData.copy(
                    dueDate = billAddedFichaEvent.dueDate,
                    fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO,
                ),
            )
        }

        billEventRepository.save(
            billAddedFichaEvent.copy(
                barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2),
                idNumber = fichaRegisterData.idNumber,
            ),
        )

        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(
                validAddFichaCompensacaoRequest,
                Argument.of(BillTO::class.java),
                Argument.of(ResponseTO::class.java),
            )
        }
        thrown.status shouldBe HttpStatus.BAD_REQUEST
        with(thrown.response.getBody(ResponseTO::class.java).get()) {
            message shouldBe AddBillError.BILL_ALREADY_INCLUDED.description
            BillId(bill!!.id) shouldBe billAddedFichaEvent.billId
        }
    }

    @Test
    fun `should create bill when ficha de compensacao type is credit card and already exists bill with same idNumber and different due date`() {
        every { validationService.validate(ofType(BarCode::class)) } answers {
            arbiValidationResponse.copy(
                billRegisterData = fichaRegisterData.copy(
                    fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO,
                ),
            )
        }

        billEventRepository.save(
            billAddedFichaEvent.copy(
                barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2),
                idNumber = fichaRegisterData.idNumber,
                dueDate = getLocalDate(),
            ),
        )

        val result = client.toBlocking().exchange(
            validAddFichaCompensacaoRequest,
            Argument.of(BillTO::class.java),
            Argument.of(ResponseTO::class.java),
        )

        assertEquals(HttpStatus.CREATED, result.status)

        val billTO = result.getBody(BillTO::class.java).get()

        var billResult = billEventRepository.findLastBill(
            barCode = BarCode.ofDigitable(validFichaCompensacaoTO.digitableLine),
            walletId = wallet.id,
        )
        billResult.isRight() shouldBe true
        billResult.map {
            it.billId.value shouldBe billTO.id
            it.idNumber shouldBe fichaRegisterData.idNumber
        }

        billResult = billEventRepository.findLastBill(
            barCode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2),
            walletId = wallet.id,
        )
        billResult.isRight() shouldBe true
        billResult.map {
            it.billId shouldBe billAddedFichaEvent.billId
            it.idNumber shouldBe fichaRegisterData.idNumber
        }
    }

    @Test
    fun `deve criar uma nova conta do tipo cartão de crédito quando vir uma nova data de vencimento, mesmo que existam baixas`() {
        val now = getLocalDate()
        val dueDate = now.minusMonths(1)
        val created = dueDate.minusDays(10).toEpochMillis()
        val newDueDate = now.plusMonths(1)

        every { featureConfigurationMock.blockDuplicateByIdNumber } returns false
        billEventRepository.save(
            billAddedFichaEvent.copy(
                dueDate = dueDate,
                created = created,
            ),
        )

        every { validationService.validate(ofType(BarCode::class)) } answers {
            arbiValidationResponse.copy(
                billRegisterData = fichaRegisterData.copy(
                    dueDate = newDueDate,
                    barCode = billAddedFichaEvent.barcode,
                    fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO,
                    amountPaid = fichaRegisterData.amount,

                ),
            )
        }

        val result = client.toBlocking().exchange(
            validAddFichaCompensacaoRequest,
            Argument.of(BillTO::class.java),
            Argument.of(ResponseTO::class.java),
        )

        assertEquals(HttpStatus.CREATED, result.status)

        val billTO = result.getBody(BillTO::class.java).get()

        var billResult = billEventRepository.findLastBill(
            barCode = billAddedFichaEvent.barcode,
            walletId = wallet.id,
        )
        billResult.isRight() shouldBe true
        billResult.map {
            it.billId.value shouldBe billTO.id
        }
    }

    @Test
    fun `should not create bill neither update existing bill when new amount is zero`() {
        every { validationService.validate(ofType(BarCode::class)) } answers {
            arbiValidationResponse.copy(
                billRegisterData = fichaRegisterData.copy(
                    amountTotal = 0L,
                ),
            )
        }

        billEventRepository.save(billAddedFichaEvent)

        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(
                validAddFichaCompensacaoRequest,
                Argument.of(BillTO::class.java),
                Argument.of(ResponseTO::class.java),
            )
        }
        thrown.status shouldBe HttpStatus.BAD_REQUEST
        with(thrown.response.getBody(ResponseTO::class.java).get()) {
            message shouldBe AddBillError.BILL_ALREADY_INCLUDED.description
            BillId(bill!!.id) shouldBe billAddedFichaEvent.billId
        }

        val billResult = billEventRepository.getBillById(billAddedFichaEvent.billId)
        billResult.isRight() shouldBe true
        billResult.map {
            it.status shouldBe BillStatus.ACTIVE
        }
    }

    @Test
    fun `should not create concessionaria when dryRun is true`() {
        every { boletoSettlementMock.validateBill(any<CreateBoletoRequest>()) } answers { successConcessionariaValidationResponse }
        val request = HttpRequest.POST("/bill/concessionaria?dryRun=true", validConcessionariaTo).onWallet(wallet)

        val result = client.toBlocking().exchange(request, BillTO::class.java)

        val billTO = result.getBody(BillTO::class.java).get()

        assertEquals(HttpStatus.OK, result.status)
        Assertions.assertThrows(IllegalStateException::class.java) {
            billRepository.findBill(
                BillId(billTO.id),
                wallet.id,
            )
        }
        assertConcessionariaBillTO(billTO, validConcessionariaTo, successConcessionariaValidationResponse)
    }

    @Test
    fun `should not create ficha when dryRun is true`() {
        every { validationService.validate(ofType(BarCode::class)) } answers { arbiValidationResponse }
        val request = HttpRequest.POST("/bill/ficha-compensacao?dryRun=true", validFichaCompensacaoTO).onWallet(wallet)

        val result = client.toBlocking().exchange(request, BillTO::class.java)

        val billTO = result.getBody(BillTO::class.java).get()

        assertEquals(HttpStatus.OK, result.status)
        Assertions.assertThrows(IllegalStateException::class.java) {
            billRepository.findBill(
                BillId(billTO.id),
                wallet.id,
            )
        }
        assertFichaDeCompensacaoBillTO(billTO, validFichaCompensacaoTO, arbiValidationResponse)
    }

    @Test
    fun `should return unprocessable entity on ficha compensacao barcode not found on central bank`() {
        every { validationService.validate(ofType(BarCode::class)) } answers {
            ArbiValidationResponse(resultado = "7 - Mensagem rejeitada pela CIP: EDDA0526 - ")
        }

        val request = HttpRequest.POST("/bill/ficha-compensacao?dryRun=true", validFichaCompensacaoTO).onWallet(wallet)

        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(request, Unit::class.java)
        }
        assertEquals(HttpStatus.UNPROCESSABLE_ENTITY, thrown.status)
        assertEquals(thrown.message, "Boleto não encontrado")
    }

    @Test
    fun `should return bill already added when digitableline per account register exist`() {
        every { validationService.validate(ofType(BarCode::class)) } answers {
            arbiValidationResponse.copy(
                billRegisterData = arbiValidationResponse.billRegisterData!!.copy(
                    dueDate = billAddedFichaEvent.dueDate,
                ),
            )
        }

        billEventRepository.save(billAddedFichaEvent)

        val result = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(
                validAddFichaCompensacaoRequest,
                Argument.of(BillTO::class.java),
                Argument.of(ResponseTO::class.java),
            )
        }

        assertEquals(HttpStatus.BAD_REQUEST, result.status)
        val responseTO = result.response.getBody(ResponseTO::class.java).get()
        assertEquals(AddBillError.BILL_ALREADY_INCLUDED.code, responseTO.code)
        assertEquals(billAddedFichaEvent.billId.value, responseTO.bill!!.id)
    }

    @Test
    fun `should add bill ficha de compensação when digitableline per account register exist was removed`() {
        every { validationService.validate(ofType(BarCode::class)) } answers {
            arbiValidationResponse.copy(
                billRegisterData = arbiValidationResponse.billRegisterData!!.copy(
                    dueDate = billAddedFichaEvent.dueDate,
                ),
            )
        }

        billEventRepository.save(billAddedFichaEvent)
        billEventRepository.save(billIgnored)

        val result =
            client.toBlocking().exchange(
                validAddFichaCompensacaoRequest,
                Argument.of(BillTO::class.java),
                Argument.of(ResponseTO::class.java),
            )

        assertEquals(HttpStatus.CREATED, result.status)
    }

    @Test
    fun `should add bill concessionaria when digitableline per account register exist was ignored`() {
        every { boletoSettlementMock.validateBill(any<CreateBoletoRequest>()) } answers { successConcessionariaValidationResponse }
        setupIgnoredConcessionaria()

        val result = client.toBlocking().exchange(validAddConcessionariaRequest, BillTO::class.java)

        val billTO = result.getBody(BillTO::class.java).get()

        assertEquals(HttpStatus.CREATED, result.status)
        assertConcessionaria(validConcessionariaTo.digitableLine, successConcessionariaValidationResponse)
        assertConcessionariaBillTO(billTO, validConcessionariaTo, successConcessionariaValidationResponse)
        LocalDate.parse(billTO.effectiveDueDate, dateFormat) shouldHaveSameDayAs sundayDate.plusDays(1)
    }

    private fun setupIgnoredConcessionaria() {
        billEventRepository.save(
            billAdded.copy(
                barcode = BarCode.ofDigitable(CONCESSIONARIA_DIGITABLE_LINE),
                amount = successConcessionariaValidationResponse.billRegisterData!!.amount,
                amountTotal = successConcessionariaValidationResponse.billRegisterData!!.amountTotal,
                discount = successConcessionariaValidationResponse.billRegisterData!!.discount,
                fine = successConcessionariaValidationResponse.billRegisterData!!.fine,
                interest = successConcessionariaValidationResponse.billRegisterData!!.interest,
                assignor = successConcessionariaValidationResponse.billRegisterData!!.assignor,
                document = successConcessionariaValidationResponse.billRegisterData!!.payerDocument,
                paymentLimitTime = LocalTime.parse(successConcessionariaValidationResponse.billRegisterData!!.paymentLimitTime!!, timeFormat),
                lastSettleDate = successConcessionariaValidationResponse.billRegisterData!!.settleDate!!.format(dateFormat),
                description = validConcessionariaTo.description,
            ),
        )
        billEventRepository.save(billIgnored)
    }

    @Test
    fun `should not reactivate and update existing ignored concessionaria bill on new bill with another due date when dryRun is true`() {
        setupIgnoredConcessionaria()
        every { boletoSettlementMock.validateBill(any<CreateBoletoRequest>()) } answers { successConcessionariaValidationResponse }
        val request = HttpRequest.POST("/bill/concessionaria?dryRun=true", validConcessionariaTo).onWallet(wallet)

        val result = client.toBlocking().exchange(request, BillTO::class.java)
        val billTO = result.getBody(BillTO::class.java).get()

        assertEquals(HttpStatus.OK, result.status)
        assertConcessionariaBillTO(billTO, validConcessionariaTo, successConcessionariaValidationResponse)

        val billResult = billEventRepository.getBillById(BillId(billTO.id))
        billResult.isRight() shouldBe true
        billResult.map {
            it.status shouldBe BillStatus.IGNORED
            it.dueDate shouldBe billAdded.dueDate
            it.effectiveDueDate shouldBe billAdded.effectiveDueDate
        }
    }

    @Test
    fun `should reactivate and update existing ignored concessionaria bill on new bill with another due date`() {
        setupIgnoredConcessionaria()
        every { boletoSettlementMock.validateBill(any<CreateBoletoRequest>()) } answers { successConcessionariaValidationResponse }

        val result = client.toBlocking().exchange(validAddConcessionariaRequest, BillTO::class.java)
        val billTO = result.getBody(BillTO::class.java).get()

        billTO.id shouldBe billAdded.billId.value
        assertEquals(HttpStatus.CREATED, result.status) // response deveria ser created?
        assertConcessionaria(validConcessionariaTo.digitableLine, successConcessionariaValidationResponse)
        assertConcessionariaBillTO(billTO, validConcessionariaTo, successConcessionariaValidationResponse)
        LocalDate.parse(billTO.effectiveDueDate, dateFormat) shouldHaveSameDayAs sundayDate.plusDays(1)
    }

    @ParameterizedTest
    @ValueSource(strings = ["true", "false"])
    fun `should return bill already added when concessionaria bill is not ignored and duedate do not change`(
        dryRun: Boolean,
    ) {
        billEventRepository.save(
            billAdded.copy(
                barcode = BarCode.ofDigitable(CONCESSIONARIA_DIGITABLE_LINE),
                dueDate = successConcessionariaValidationResponse.billRegisterData!!.dueDate!!,
            ),
        )

        every { boletoSettlementMock.validateBill(any<CreateBoletoRequest>()) } answers { successConcessionariaValidationResponse }

        addConcessionariaRequestShouldReturnBillAlreadyAdded(dryRun)
    }

    @ParameterizedTest
    @ValueSource(strings = ["true", "false"])
    fun `should return bill already added when concessionaria bill duedate changes but bill is not ignored`(
        dryRun: Boolean,
    ) {
        billEventRepository.save(
            billAdded.copy(
                barcode = BarCode.ofDigitable(CONCESSIONARIA_DIGITABLE_LINE),
                dueDate = billAdded.dueDate,
            ),
        )

        every { boletoSettlementMock.validateBill(any<CreateBoletoRequest>()) } answers { successConcessionariaValidationResponse }

        addConcessionariaRequestShouldReturnBillAlreadyAdded(dryRun)
    }

    private fun addConcessionariaRequestShouldReturnBillAlreadyAdded(dryRun: Boolean) {
        val request = if (dryRun) {
            HttpRequest.POST("/bill/concessionaria?dryRun=true", validConcessionariaTo).onWallet(wallet)
        } else {
            validAddConcessionariaRequest
        }
        val result = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(
                request,
                Argument.of(BillTO::class.java),
                Argument.of(ResponseTO::class.java),
            )
        }

        assertEquals(HttpStatus.BAD_REQUEST, result.status)
        val responseTO = result.response.getBody(ResponseTO::class.java).get()
        assertEquals(AddBillError.BILL_ALREADY_INCLUDED.code, responseTO.code)
        assertEquals(billAdded.billId.value, responseTO.bill!!.id)
    }

    @AfterEach
    fun cleanUp() {
        clearMocks(boletoSettlementMock)
        cleanupDb(dynamoDB)
    }

    private fun assertConcessionaria(digitableLine: String, validationResponse: CelcoinBillValidationResponse) {
        val billAdded = getBillAddEvent(digitableLine)
        assertEquals(BillEventType.ADD, billAdded.eventType)
        assertEquals(validationResponse.billRegisterData!!.amount, billAdded.amount)
        assertEquals(BillType.CONCESSIONARIA, billAdded.billType)
    }

    private fun assertFichaCompensacao(digitableLine: String, validationResponse: BillValidationResponse) {
        val billAdded = getFichaCompensacaoAddedEvent(digitableLine)
        assertEquals(BillEventType.ADD, billAdded.eventType)
        assertEquals(validationResponse.billRegisterData!!.amount, billAdded.amount)
        assertEquals(
            validationResponse.billRegisterData!!.expirationDate!!.format(dateFormat),
            billAdded.expirationDate,
        )
        assertEquals(validationResponse.billRegisterData!!.recipient, billAdded.recipient)
        assertEquals(validationResponse.billRegisterData!!.amountCalculationModel, billAdded.amountCalculationModel)
        assertEquals(validationResponse.billRegisterData!!.divergentPayment, billAdded.divergentPayment)
        assertEquals(validationResponse.billRegisterData!!.partialPayment, billAdded.partialPayment)
    }

    private fun assertConcessionariaBillTO(
        billTO: BillTO,
        addConcessionariaTO: AddConcessionariaTO,
        validationResponse: CelcoinBillValidationResponse,
    ) {
        assertEquals(validationResponse.billRegisterData!!.amount, billTO.amount)
        assertEquals(validationResponse.billRegisterData!!.amountTotal, billTO.amountTotal)
        assertEquals(validationResponse.billRegisterData!!.assignor, billTO.assignor)
        assertEquals(addConcessionariaTO.dueDate, billTO.dueDate)
        assertEquals(addConcessionariaTO.digitableLine, addConcessionariaTO.digitableLine)
        assertEquals(addConcessionariaTO.description, billTO.description)
        assertNull(billTO.billRecipient)
        assertEquals(BillSourceTO(type = "Webapp", accountId = wallet.founder.accountId.value), billTO.source)
    }

    private fun assertFichaDeCompensacaoBillTO(
        billTO: BillTO,
        addFichaDeCompensacaoTO: AddFichaDeCompensacaoTO,
        validationResponse: BillValidationResponse,
        fichaCompensacaoType: FichaCompensacaoType? = null,
    ) {
        assertEquals(validationResponse.billRegisterData!!.amount, billTO.amount)
        assertEquals(validationResponse.billRegisterData!!.amountTotal, billTO.amountTotal)
        assertEquals(validationResponse.billRegisterData!!.dueDate!!.format(dateFormat), billTO.dueDate)
        assertEquals(addFichaDeCompensacaoTO.digitableLine, billTO.barcode)
        assertEquals(addFichaDeCompensacaoTO.description, billTO.description)
        assertEquals(validationResponse.billRegisterData!!.recipient!!.name, billTO.recipient?.name)
        assertEquals(validationResponse.billRegisterData!!.recipient!!.document, billTO.recipient?.document)
        assertEquals(validationResponse.billRegisterData!!.recipient!!.name, billTO.billRecipient?.name)
        assertEquals(validationResponse.billRegisterData!!.recipient!!.document, billTO.billRecipient?.document)
        assertEquals(validationResponse.billRegisterData!!.payerDocument, billTO.payer!!.document)
        assertEquals(validationResponse.billRegisterData!!.payerName, billTO.payer!!.name)
        assertEquals(validationResponse.billRegisterData!!.amountCalculationModel.name, billTO.amountCalculationModel)
        assertEquals(BillSourceTO(type = "Webapp", accountId = wallet.founder.accountId.value), billTO.source)

        val currentFichaCompensacaoType =
            fichaCompensacaoType ?: validationResponse.billRegisterData!!.fichaCompensacaoType
        billTO.fichaCompensacaoType shouldBe currentFichaCompensacaoType
    }

    private fun getBillAddEvent(digitableLine: String): BillAddedEntity {
        val entities = billEventDAO.findBeginsWithOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            digitableLine,
            wallet.id.value,
        )
        return entities.filter { entity -> entity.eventType == BillEventType.ADD }
            .map { entity -> parseObjectFrom<BillAddedEntity>(entity.details) }.single()
    }

    private fun getFichaCompensacaoAddedEvent(digitableLine: String): FichaCompensacaoAddedEntity {
        val entities = billEventDAO.findBeginsWithOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            digitableLine,
            wallet.id.value,
        )
        return entities.filter { entity -> entity.eventType == BillEventType.ADD }
            .map { entity -> parseObjectFrom<FichaCompensacaoAddedEntity>(entity.details) }.single()
    }

    companion object {
        private val walletFixture = WalletFixture()
        private val wallet = walletFixture.buildWallet()
        private val billAddedFichaEvent = billAddedFicha.copy(
            walletId = wallet.id,
            actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
        )
        private val billAdded = ai.friday.billpayment.billAdded.copy(
            walletId = wallet.id,
            barcode = BarCode.ofDigitable(CONCESSIONARIA_DIGITABLE_LINE),
            actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
        )
        private val billIgnored = ai.friday.billpayment.billIgnored.copy(walletId = wallet.id)

        private val billPaid = ai.friday.billpayment.billPaid.copy(walletId = wallet.id)
        private val billPaymentScheduled =
            ai.friday.billpayment.billPaymentScheduled.copy(walletId = wallet.id)
        private val billPaymentStart = ai.friday.billpayment.billPaymentStart.copy(walletId = wallet.id)
        private val billRegisterUpdatedAlreadyPaid =
            ai.friday.billpayment.billRegisterUpdatedAlreadyPaid.copy(walletId = wallet.id)
        private val billRegisterUpdatedNotPayable =
            ai.friday.billpayment.billRegisterUpdatedNotPayable.copy(walletId = wallet.id)

        @JvmStatic
        fun shouldNotMarkAsNotPayableStatus(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(billIgnored),
                Arguments.of(billPaid),
                Arguments.of(billRegisterUpdatedNotPayable),
                Arguments.of(billRegisterUpdatedAlreadyPaid),
            )
        }
    }
}