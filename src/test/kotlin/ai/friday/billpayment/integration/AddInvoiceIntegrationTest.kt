package ai.friday.billpayment.integration

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.api.BillSourceTO
import ai.friday.billpayment.adapters.api.BillTO
import ai.friday.billpayment.adapters.api.CreateInvoiceTO
import ai.friday.billpayment.adapters.api.FAKE_RECURRENCE_BILL_ID
import ai.friday.billpayment.adapters.api.RecipientBankDetailsTO
import ai.friday.billpayment.adapters.api.RecurrenceRequestTO
import ai.friday.billpayment.adapters.api.RequestInvoiceRecipientTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.api.toBankAccount
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillRecurrenceDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillRecurrenceDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.ContactDbRepository
import ai.friday.billpayment.adapters.dynamodb.ContactDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.WarningCode
import ai.friday.billpayment.app.contact.LastUsed
import ai.friday.billpayment.app.contact.toSavedBankAccount
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.recurrence.RecurrenceFrequency
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.bankDetailsTO
import ai.friday.billpayment.createInvoiceTO
import ai.friday.billpayment.createSavedRecipient
import ai.friday.billpayment.recipientTO
import ai.friday.billpayment.sundayDate
import ai.friday.billpayment.verify
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.withGivenDateTime
import arrow.core.getOrElse
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.fasterxml.jackson.databind.ObjectMapper
import io.kotest.assertions.assertSoftly
import io.kotest.assertions.fail
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.date.shouldHaveSameDayAs
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.string.shouldContain
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import java.math.BigInteger
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

private const val limitDate = "2025-12-31"

@Property(name = "recurrence.limitDate", value = limitDate)
@MicronautTest(environments = [FRIDAY_ENV])
class AddInvoiceIntegrationTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {

    private val walletLimitsServiceMock: WalletLimitsService = mockk {
        every { getAvailableMonthlyLimit(any(), any(), any()) } returns null
    }

    @MockBean(WalletLimitsService::class)
    fun getTransactionLimitService(): WalletLimitsService = walletLimitsServiceMock

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val dynamoDbEnhancedClient = getDynamoDB()
    private val billRecurrenceDynamoDAO = BillRecurrenceDynamoDAO(dynamoDbEnhancedClient)
    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(dynamoDbEnhancedClient),
        refundedClient = RefundedBillDynamoDAO(dynamoDbEnhancedClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(dynamoDbEnhancedClient),
    )
    private val recurrenceRepository = BillRecurrenceDBRepository(billRecurrenceDynamoDAO, limitDate)
    private val recipientDbRepository = ContactDbRepository(ContactDynamoDAO(dynamoDbEnhancedClient))

    private val walletDAO = WalletDynamoDAO(dynamoDbEnhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(dynamoDbEnhancedClient)
    private val inviteDAO = InviteDynamoDAO(dynamoDbEnhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(dynamoDbEnhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(dynamoDbEnhancedClient)

    private val walletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = mockk(),
    )

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        walletRepository.save(wallet)

        every {
            walletLimitsServiceMock.getDailyLimit(any())
        } returns 100_000_00

        every {
            walletLimitsServiceMock.getAvailableLimit(any(), any(), any())
        } returns 100_000_00
    }

    @ParameterizedTest
    @CsvSource(
        "$LONG_DESCRIPTION,2020-04-20,10,Eduardo,***********,CPF,CHECKING,111111,02,123,1,Description",
        "description,20/04/2020,10,Eduardo,***********,CPF,CHECKING,111111,02,123,1,Due date",
        "description,2020-04-20,-1,Eduardo,***********,CPF,CHECKING,111111,02,123,1,Amount",
        "description,2020-04-20,10,Eduardo,***********2,CPF,CHECKING,111111,02,123,1,document",
        "description,2020-04-20,10,Eduardo,***********,CNH,CHECKING,111111,02,123,1,Document type",
        "description,2020-04-20,10,Eduardo,***********,CPF,CHECKING,-11111,02,123,1,Account Number",
        "description,2020-04-20,10,Eduardo,***********,CPF,CHECKING,111111,-22,123,1,Routing Number",
        "description,2020-04-20,10,Eduardo,***********,CPF,CHECKING,111111,02,-12,1,Bank Number",
        "description,2020-04-20,10,Eduardo,***********,CPF,CHECKING,111111,02,,1,BankNo can't be null",
        "description,2020-04-20,10,Eduardo,***********,CPF,CHECKING,111111,02,123,11,Account check",
    )
    fun `should return bad request on invalid invoice request`(
        description: String,
        dueDate: String,
        amount: Long,
        name: String,
        document: String,
        documentType: String,
        accountType: AccountType,
        accountNo: BigInteger,
        routingNo: Long,
        bankNo: Long?,
        accountDv: String,
        expectedMessage: String,
    ) {
        val bankDetailsTO = RecipientBankDetailsTO(
            accountType = accountType,
            bankNo = bankNo,
            routingNo = routingNo,
            accountNo = accountNo,
            accountDv = accountDv,
        )
        val recipientTO = RequestInvoiceRecipientTO(
            name = name,
            document = document,
            documentType = documentType,
            bankDetails = bankDetailsTO,
        )
        val addInvoiceTO =
            CreateInvoiceTO(description = description, dueDate = dueDate, amount = amount, recipient = recipientTO)
        val request = HttpRequest.POST("/bill/invoice", addInvoiceTO).onWallet(wallet)
        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(request, Unit::class.java)
        }
        assertEquals(HttpStatus.BAD_REQUEST, thrown.status)
        thrown.message!!.shouldContain(expectedMessage)
    }

    @ParameterizedTest
    @CsvSource(
        "description,2020-04-20,10,Eduardo,***********,CNPJ,CHECKING,111111,02,123,Document does not match required pattern",
        "description,2020-04-20,10,Eduardo,**************,CPF,CHECKING,111111,02,123,Document does not match required pattern",
    )
    fun `should return bad request when cpf or cpnj does not match pattern`(
        description: String,
        dueDate: String,
        amount: Long,
        name: String,
        document: String,
        documentType: String,
        accountType: AccountType,
        accountoNo: BigInteger,
        routingNo: Long,
        bankNo: Long,
        expectedMessage: String,
    ) {
        val bankDetailsTO = RecipientBankDetailsTO(
            accountType = accountType,
            bankNo = bankNo,
            routingNo = routingNo,
            accountNo = accountoNo,
            accountDv = "X",
        )
        val recipientTO = RequestInvoiceRecipientTO(
            name = name,
            document = document,
            documentType = documentType,
            bankDetails = bankDetailsTO,
        )
        val addInvoiceTO =
            CreateInvoiceTO(description = description, dueDate = dueDate, amount = amount, recipient = recipientTO)
        val request = HttpRequest.POST("/bill/invoice", addInvoiceTO).onWallet(wallet)
        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking()
                .exchange(request, Argument.of(Unit::class.java), Argument.of(ResponseTO::class.java))
        }
        val responseTO = thrown.response.getBody(ResponseTO::class.java)
        assertEquals(HttpStatus.BAD_REQUEST, thrown.status)
        assertTrue(
            expectedMessage == responseTO.get().message,
            "Expected '$expectedMessage' but found '${responseTO.get().message}'",
        )
    }

    /*
        @Disabled // FIXME esta dando Account not Found - nao sei porque
        @Test
        fun `should return bad request when account type does not exists`() {
            val request = HttpRequest.POST(
                "/bill/invoice",
                """{"recipient": {"name": "string","document": "133.975.977-22","documentType": "CPF","bankDetails": {"accountType": "BOLETO","bankNo": "1111","routingNo": "11","accountNo": "11"}},"amount": 0,"dueDate": "2020-04-12","description": "string"}""",
            )
                .onWallet(wallet)
            val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
                client.toBlocking().exchange(request, Unit::class.java)
            }
            assertEquals(HttpStatus.BAD_REQUEST, thrown.status)
            assertTrue(thrown.message!!.contains("AccountType"), "Expected 'AccountType' but found '${thrown.message}'")
        }
    */

    @Test
    fun `should return UNSCHEDULED_DUE_AMOUNT_HIGHER_THAN_DAILY_LIMIT warning code on available limit reached`() {
        every {
            walletLimitsServiceMock.getDailyLimit(any())
        } returns 200

        every {
            walletLimitsServiceMock.getAvailableLimit(any(), any(), any())
        } returns 100

        withGivenDateTime(ZonedDateTime.of(LocalDate.of(2022, 1, 14), LocalTime.NOON, brazilTimeZone)) {
            val request =
                HttpRequest.POST(
                    "/bill/invoice?dryRun=true",
                    createInvoiceTO.copy(
                        amount = 250,
                        dueDate = getZonedDateTime().toLocalDate().format(dateFormat),
                    ),
                )
                    .onWallet(wallet)
            val response =
                client.toBlocking()
                    .exchange(request, Argument.of(BillTO::class.java), Argument.of(ResponseTO::class.java))

            response.status shouldBe HttpStatus.CREATED
            val billTO = response.getBody(BillTO::class.java).get()

            billTO.warningCode shouldBe WarningCode.UNSCHEDULED_DUE_AMOUNT_HIGHER_THAN_DAILY_LIMIT.name
        }
    }

    @Test
    fun `should return POSTPONED_DUE_LIMIT_REACHED warning code on available limit reached`() {
        every {
            walletLimitsServiceMock.getDailyLimit(any())
        } returns 200

        every {
            walletLimitsServiceMock.getAvailableLimit(any(), any(), any())
        } returns 100

        withGivenDateTime(ZonedDateTime.of(LocalDate.of(2022, 1, 14), LocalTime.NOON, brazilTimeZone)) {
            val request =
                HttpRequest.POST(
                    "/bill/invoice?dryRun=true",
                    createInvoiceTO.copy(
                        amount = 150,
                        dueDate = getZonedDateTime().toLocalDate().format(dateFormat),
                    ),
                )
                    .onWallet(wallet)
            val response =
                client.toBlocking()
                    .exchange(request, Argument.of(BillTO::class.java), Argument.of(ResponseTO::class.java))

            response.status shouldBe HttpStatus.CREATED
            val billTO = response.getBody(BillTO::class.java).get()

            billTO.warningCode shouldBe WarningCode.POSTPONED_DUE_LIMIT_REACHED.name
        }
    }

    @Test
    fun `should return created when invoice parameters are valid`() {
        val request = HttpRequest.POST("/bill/invoice", createInvoiceTO).onWallet(wallet)

        assertEquals(0, billRepository.findByWallet(wallet.id).size)

        withGivenDateTime(ZonedDateTime.of(sundayDate.plusDays(1), LocalTime.NOON, brazilTimeZone)) {
            val response = client.toBlocking()
                .exchange(request, Argument.of(BillTO::class.java), Argument.of(ResponseTO::class.java))

            val billTO = response.getBody(BillTO::class.java).get()
            val bills = billRepository.findByWallet(wallet.id)
            assertEquals(HttpStatus.CREATED, response.status)
            assertEquals(1, getEventsAddedCountByBillId(BillId(billTO.id)))
            assertEquals(1, bills.size)
            assertEquals(ActionSource.Api(accountId = wallet.founder.accountId), bills[0].source)
            bills[0].effectiveDueDate shouldHaveSameDayAs bills[0].dueDate.plusDays(1)
            bills[0].isOverdue shouldBe false
            billTO.overdue shouldBe false

            assertBillTO(billTO, createInvoiceTO, 1)
            val recipient = recipientDbRepository.findRecipientByAccountIDAndDocument(
                wallet.founder.accountId,
                createInvoiceTO.recipient.document,
            ).getOrElse { fail("should find recipient") }
            recipient.bankAccounts.size shouldNotBe 0
            recipient.bankAccounts[0].bankNo shouldBe createInvoiceTO.recipient.bankDetails.bankNo
            with(billRepository.findByContactId(recipient.id)) {
                size shouldBe 1
                first().walletId shouldBe wallet.id
                first().billId.value shouldBe billTO.id
            }
        }
    }

    @Test
    fun `should append bank account on recipient already saved by document`() {
        val createdRecipient =
            createSavedRecipient(lastUsed = LastUsed(pixKey = "<EMAIL>"), accountId = wallet.founder.accountId)
        val newBankAccountInvoice =
            createInvoiceTO.copy(recipient = recipientTO.copy(bankDetails = bankDetailsTO.copy(accountType = AccountType.SAVINGS)))
        recipientDbRepository.save(recipient = createdRecipient)
        val request =
            HttpRequest.POST("/bill/invoice", newBankAccountInvoice).onWallet(wallet)

        val response =
            client.toBlocking().exchange(request, Argument.of(BillTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.CREATED
        with(recipientDbRepository.findById(createdRecipient.id)) {
            bankAccounts.size shouldBe 3
            bankAccounts[0].accountType shouldBe AccountType.CHECKING
            bankAccounts[1].accountType shouldBe AccountType.CHECKING
            bankAccounts[2].accountType shouldBe AccountType.SAVINGS

            lastUsed.verify(bankAccount = bankAccounts.last())
        }
    }

    @Test
    fun `should not append bank account on duplicate bank account`() {
        val createdRecipient =
            createSavedRecipient(lastUsed = LastUsed(pixKey = "<EMAIL>"), accountId = wallet.founder.accountId)
        recipientDbRepository.save(createdRecipient)
        val request = HttpRequest.POST("/bill/invoice", createInvoiceTO).onWallet(wallet)

        val response =
            client.toBlocking().exchange(request, Argument.of(BillTO::class.java), Argument.of(ResponseTO::class.java))

        assertEquals(HttpStatus.CREATED, response.status)
        val recipient = recipientDbRepository.findById(createdRecipient.id)

        recipient.lastUsed.verify(
            bankAccount = createdRecipient.bankAccounts.first {
                createInvoiceTO.recipient.bankDetails.toBankAccount().toSavedBankAccount() == it
            },
        )
        assertEquals(2, recipient.bankAccounts.size)
    }

    @Test
    fun `should save recipient on add invoice with new recipient`() {
        val request = HttpRequest.POST("/bill/invoice", createInvoiceTO).onWallet(wallet)

        val response =
            client.toBlocking().exchange(request, Argument.of(BillTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.CREATED

        val result = recipientDbRepository.findRecipientByAccountIDAndDocument(
            wallet.founder.accountId,
            recipientTO.document,
        )
        result.isRight() shouldBe true
        result.map {
            it.name shouldBe recipientTO.name
            it.document shouldBe recipientTO.document
            it.alias shouldBe recipientTO.alias
            with(it.bankAccounts.first()) {
                accountType shouldBe bankDetailsTO.accountType
                bankNo shouldBe bankDetailsTO.bankNo
                routingNo shouldBe bankDetailsTO.routingNo
                accountNo shouldBe bankDetailsTO.accountNo
                accountDv shouldBe bankDetailsTO.accountDv

                it.lastUsed.verify(bankAccount = this)
            }
        }
    }

    @Test
    fun `should add invoice without alias`() {
        val addInvoiceWithoutAlias = createInvoiceTO.copy(recipient = recipientTO.copy(alias = null))
        val request =
            HttpRequest.POST("/bill/invoice", addInvoiceWithoutAlias).onWallet(wallet)

        assertEquals(0, billRepository.findByWallet(wallet.id).size)

        val response =
            client.toBlocking().exchange(request, Argument.of(BillTO::class.java), Argument.of(ResponseTO::class.java))

        val billTO = response.getBody(BillTO::class.java).get()

        assertEquals(HttpStatus.CREATED, response.status)
        assertEquals("", billTO.billRecipient?.alias)
    }

    @Test
    fun `should not create invoice when dryRun is true`() {
        val request =
            HttpRequest.POST("/bill/invoice?dryRun=true", createInvoiceTO).onWallet(wallet)
        val response =
            client.toBlocking().exchange(request, Argument.of(BillTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.CREATED
        val billTO = response.getBody(BillTO::class.java).get()
        val bills = billRepository.findByWallet(wallet.id)
        getEventsAddedCountByBillId(BillId(billTO.id)) shouldBe 0
        bills.size shouldBe 0
        assertBillTO(billTO, createInvoiceTO, 1)
    }

    @Test
    fun `should return bad request when end date format is invalid`() {
        val createInvoiceTO = createInvoiceTO.copy(
            recurrence = RecurrenceRequestTO(
                endDate = "10/10/2020",
                frequency = RecurrenceFrequency.WEEKLY,
            ),
        )
        val request = buildRequest(createInvoiceTO, false)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(BillTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    @Test
    fun `should return bad request when frequency is invalid`() {
        val jsonString =
            ObjectMapper().writeValueAsString(createInvoiceTO.copy(recurrence = RecurrenceRequestTO(frequency = RecurrenceFrequency.WEEKLY)))
                .replace("WEEKLY", "INVALIDO")
        val request = buildRequest(jsonString, false)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(BillTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    @Test
    fun `should return created on valid dryrun invoice with recurrence but should not create recurrence`() {
        val createInvoiceTO =
            createInvoiceTO.copy(recurrence = RecurrenceRequestTO(frequency = RecurrenceFrequency.WEEKLY))
        val request = buildRequest(createInvoiceTO, true)

        val response = client.toBlocking().exchange(request, BillTO::class.java)

        response.status shouldBe HttpStatus.CREATED
        with(response.body()!!) {
            assertSoftly {
                source.type shouldBe "Recurrence"
                source.accountId shouldBe wallet.founder.accountId.value
                recurrence!!.startDate shouldBe createInvoiceTO.dueDate
                recurrence!!.frequency shouldBe RecurrenceFrequency.WEEKLY.name
                id shouldBe FAKE_RECURRENCE_BILL_ID
            }
            assertThrows<ItemNotFoundException> {
                recurrenceRepository.find(
                    recurrenceId = RecurrenceId(recurrence!!.id),
                    walletId = wallet.id,
                )
            }
        }

        val bills = billRepository.findByWallet(wallet.id)
        bills.size shouldBe 0
    }

    @Test
    fun `should return created with warning code POSTPONED_DUE_LIMIT_REACHED`() {
        every {
            walletLimitsServiceMock.getDailyLimit(any())
        } returns 200

        every {
            walletLimitsServiceMock.getAvailableLimit(any(), any(), any())
        } returns 100

        withGivenDateTime(ZonedDateTime.of(2022, 2, 25, 15, 0, 0, 0, brazilTimeZone)) {
            val createInvoiceTO =
                createInvoiceTO.copy(
                    dueDate = getLocalDate().format(dateFormat),
                    amount = 150,
                    recurrence = RecurrenceRequestTO(frequency = RecurrenceFrequency.WEEKLY),
                )
            val request = buildRequest(createInvoiceTO, true)

            val response = client.toBlocking().exchange(request, BillTO::class.java)

            response.status shouldBe HttpStatus.CREATED
            response.body()!!.warningCode shouldBe WarningCode.POSTPONED_DUE_LIMIT_REACHED.name
        }
    }

    @Test
    fun `should return created with warning code UNSCHEDULED_DUE_AMOUNT_HIGHER_THAN_DAILY_LIMIT`() {
        every {
            walletLimitsServiceMock.getDailyLimit(any())
        } returns 200

        withGivenDateTime(ZonedDateTime.of(2022, 2, 25, 15, 0, 0, 0, brazilTimeZone)) {
            val createInvoiceTO =
                createInvoiceTO.copy(
                    amount = 250,
                    recurrence = RecurrenceRequestTO(frequency = RecurrenceFrequency.WEEKLY),
                )
            val request = buildRequest(createInvoiceTO, true)

            val response = client.toBlocking().exchange(request, BillTO::class.java)

            response.status shouldBe HttpStatus.CREATED
            response.body()!!.warningCode shouldBe WarningCode.UNSCHEDULED_DUE_AMOUNT_HIGHER_THAN_DAILY_LIMIT.name
        }
    }

    @Test
    fun `should return created on valid invoice with recurrence and also create recurrence and invoice`() {
        val createInvoiceTO = createInvoiceTO.copy(
            dueDate = limitDate,
            recurrence = RecurrenceRequestTO(frequency = RecurrenceFrequency.WEEKLY),
        )
        val request = buildRequest(createInvoiceTO, false)

        val response = client.toBlocking().exchange(request, BillTO::class.java)

        response.status shouldBe HttpStatus.CREATED
        val recurrence =
            recurrenceRepository.find(RecurrenceId(response.body()!!.recurrence!!.id), wallet.id)
        assertSoftly {
            val responseBody = response.body()!!
            with(responseBody) {
                id shouldBe recurrence.bills.first().value
                source.type shouldBe "Recurrence"
                this.recurrence!!.startDate shouldBe createInvoiceTO.dueDate
                this.recurrence!!.frequency shouldBe RecurrenceFrequency.WEEKLY.name
            }
            with(recurrence) {
                this.actionSource shouldBe ActionSource.Api(accountId = wallet.founder.accountId)
                this.rule.frequency shouldBe RecurrenceFrequency.WEEKLY
                this.contactId shouldNotBe null
            }
        }
    }

    @Test
    fun `should return bad request when start date is before today`() {
        val createInvoiceTO = createInvoiceTO.copy(
            dueDate = getZonedDateTime().toLocalDate().minusDays(1).format(dateFormat),
            recurrence = RecurrenceRequestTO(frequency = RecurrenceFrequency.WEEKLY),
        )
        val request = buildRequest(createInvoiceTO, false)

        val response = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, BillTO::class.java)
        }

        response.status shouldBe HttpStatus.BAD_REQUEST
    }

    @Test
    fun `deve retornar bad request quando o dueDate for invalido`() {
        val createInvoiceTO = createInvoiceTO.copy(
            dueDate = getZonedDateTime().toLocalDate().format(dateFormat).dropLast(2) + "32",
            recurrence = RecurrenceRequestTO(frequency = RecurrenceFrequency.WEEKLY),
        )
        val request = buildRequest(createInvoiceTO, false)

        val response = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, BillTO::class.java)
        }

        response.status shouldBe HttpStatus.BAD_REQUEST
    }

    private fun buildRequest(createInvoiceTO: Any, dryRun: Boolean) =
        HttpRequest.POST("/bill/invoice?dryRun=$dryRun", createInvoiceTO)
            .onWallet(wallet)

    private fun assertBillTO(billTO: BillTO, createInvoiceTO: CreateInvoiceTO, daysToAdd: Long) {
        val billRecipient = billTO.billRecipient!!
        assertEquals(createInvoiceTO.amount, billTO.amount)
        assertEquals(createInvoiceTO.amount, billTO.amountTotal)
        assertEquals(createInvoiceTO.dueDate, billTO.dueDate)
        assertEquals(createInvoiceTO.description, billTO.description)
        assertEquals(createInvoiceTO.recipient.alias, billTO.recipient?.alias)
        assertEquals(createInvoiceTO.recipient.document, billTO.recipient?.document)
        assertEquals(createInvoiceTO.recipient.name, billTO.recipient?.name)
        assertEquals(createInvoiceTO.recipient.alias, billTO.recipient?.alias)
        assertEquals(createInvoiceTO.recipient.bankDetails, billTO.recipient?.bankDetails)
        assertEquals(createInvoiceTO.recipient.alias, billRecipient.alias)
        assertEquals(createInvoiceTO.recipient.document, billRecipient.document)
        assertEquals(createInvoiceTO.recipient.name, billRecipient.name)
        assertEquals(createInvoiceTO.recipient.alias, billRecipient.alias)
        assertEquals(createInvoiceTO.recipient.bankDetails, billRecipient.bankDetails)
        assertEquals(BillSourceTO(type = "Webapp", accountId = wallet.founder.accountId.value), billTO.source)
        billTO.availablePaymentMethods.shouldContainExactlyInAnyOrder(
            PaymentMethodType.BALANCE,
            PaymentMethodType.EXTERNAL,
        )

        LocalDate.parse(billTO.effectiveDueDate, dateFormat) shouldHaveSameDayAs LocalDate.parse(
            billTO.dueDate,
            dateFormat,
        ).plusDays(daysToAdd)
    }

    private fun getEventsAddedCountByBillId(billId: BillId): Int =
        BillEventDynamoDAO(dynamoDbEnhancedClient).findByPartitionKey(billId.value).size
}