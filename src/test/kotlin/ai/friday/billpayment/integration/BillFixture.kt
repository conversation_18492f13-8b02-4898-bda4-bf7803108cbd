package ai.friday.billpayment.integration

import ai.friday.billpayment.EMAIL_ADDRESS
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BatchSchedulingId
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPaymentScheduledInfo
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.calculateEffectiveDate
import ai.friday.billpayment.app.payment.ScheduleTo
import ai.friday.billpayment.app.payment.ScheduledBill
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentFailed
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billScheduleUpdated
import ai.friday.billpayment.createPaymentMethodsDetailWithBalance
import ai.friday.billpayment.fixture.PaymentScheduleInfoDataFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.timeFormat
import java.time.Instant
import java.time.Instant.ofEpochMilli
import java.time.LocalTime
import java.time.ZoneId.systemDefault

class BillFixture(wallet: Wallet) {

    val billAdded = BillAdded(
        billId = BillId(BILL_ID_4),
        created = Instant.now().toEpochMilli(),
        walletId = wallet.id,
        description = "ACTIVE BILL",
        dueDate = getLocalDate().plusDays(3),
        amount = 15591L,
        billType = BillType.CONCESSIONARIA,
        barcode = BarCode.ofDigitable(DIGITABLE_LINE),
        assignor = "CLARO NET",
        document = DOCUMENT,
        paymentLimitTime = LocalTime.parse("17:00", timeFormat),
        lastSettleDate = getLocalDate().plusDays(1).format(dateFormat),
        actionSource = ActionSource.WalletMailBox(
            from = EMAIL_ADDRESS.toString(),
            accountId = wallet.founder.accountId,
        ),
        effectiveDueDate = calculateEffectiveDate(
            getLocalDate().plusDays(3),
            BillType.FICHA_COMPENSACAO,
            BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
        ),
    )

    val scheduledBill = ScheduledBill(
        walletId = billAdded.walletId,
        billId = billAdded.billId,
        scheduledDate = billAdded.dueDate,
        billType = billAdded.billType,
        amount = billAdded.amountTotal,
        scheduleTo = ScheduleTo.ASAP,
        paymentLimitTime = LocalTime.parse("10:00"),
        createPaymentMethodsDetailWithBalance(wallet.paymentMethodId, billAdded.amountTotal),
        batchSchedulingId = BatchSchedulingId(),
        isSelfTransfer = false,
    )
}

object BillEventFixture {
    private fun date() = Instant.now().plusMillis(5_000).toEpochMilli()

    fun added() = this.added(date())
    fun added(created: Long = 0, billId: String? = null, dueDate: Long = 0, source: ActionSource? = null) =
        billAdded.copy(
            billId = if (billId != null) BillId(billId) else billAdded.billId,
            created = if (created > 0) created else billAdded.created,
            dueDate = if (dueDate > 0) {
                ofEpochMilli(dueDate).atZone(systemDefault()).toLocalDate()
            } else {
                billAdded.dueDate
            },
            effectiveDueDate = if (dueDate > 0) {
                calculateEffectiveDate(
                    ofEpochMilli(dueDate).atZone(systemDefault()).toLocalDate(),
                    BillType.CONCESSIONARIA,
                    BarCode.ofDigitable(PREFECTURE_BARCODE_LINE),
                )
            } else {
                billAdded.effectiveDueDate
            },
            actionSource = source ?: billAdded.actionSource,
        )

    fun scheduled() = this.scheduled(date())
    fun scheduled(
        created: Long = billPaymentScheduled.created,
        billId: String = billPaymentScheduled.billId.value,
        paymentMethod: BillPaymentScheduledInfo = PaymentScheduleInfoDataFixture.balance(),
    ) = billPaymentScheduled.copy(
        billId = BillId(billId),
        created = created,
        infoData = paymentMethod,
        batchSchedulingId = BatchSchedulingId("batch_scheduling_id"),
    )

    fun paid() = this.paid(date())
    fun paid(created: Long = 0, billId: String? = null) = billPaid.copy(
        billId = if (billId != null) BillId(billId) else billAdded.billId,
        created = if (created > 0) created else billPaid.created,
    )

    fun failed() = this.failed(date())
    fun failed(created: Long = 0, billId: String? = null) = billPaymentFailed.copy(
        billId = if (billId != null) BillId(billId) else billAdded.billId,
        created = if (created > 0) created else billPaymentFailed.created,
    )

    fun scheduleUpdated(created: Long = 0, billId: String? = null) = billScheduleUpdated.copy(
        billId = if (billId != null) BillId(billId) else billAdded.billId,
        created = if (created > 0) created else billScheduleUpdated.created,
    )
}