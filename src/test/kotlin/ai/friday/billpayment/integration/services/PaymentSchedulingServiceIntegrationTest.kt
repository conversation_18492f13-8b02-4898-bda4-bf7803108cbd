package ai.friday.billpayment.integration.services

import DynamoDBUtils
import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.dynamodb.ScheduledBillDBRepository
import ai.friday.billpayment.adapters.dynamodb.ScheduledBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.ScheduledWalletDBRepository
import ai.friday.billpayment.adapters.dynamodb.ScheduledWalletDynamoDAO
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.payment.PaymentSchedulingService
import ai.friday.billpayment.app.payment.RescheduleError
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.scheduledBill
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.spyk
import io.mockk.verify
import java.time.LocalDate
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class PaymentSchedulingServiceIntegrationTest {

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val dynamoDbEnhancedAsyncClient = DynamoDBUtils.getDynamoDBAsync()

    private lateinit var scheduledBillRepository: ScheduledBillDBRepository

    private lateinit var scheduledWalletDynamoDAO: ScheduledWalletDynamoDAO

    private lateinit var scheduledWalletRepository: ScheduledWalletDBRepository

    private lateinit var paymentSchedulingService: PaymentSchedulingService

    @BeforeEach
    fun setup() {
        DynamoDBUtils.setupDynamoDBAsync()

        scheduledBillRepository = spyk(ScheduledBillDBRepository(ScheduledBillDynamoDAO(getDynamoDB())))
        scheduledWalletDynamoDAO = ScheduledWalletDynamoDAO(dynamoDbEnhancedAsyncClient)
        scheduledWalletRepository = ScheduledWalletDBRepository(scheduledWalletDynamoDAO)

        paymentSchedulingService =
            PaymentSchedulingService(scheduledBillRepository, scheduledWalletRepository)

        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
    }

    @Test
    fun `should create entries for scheduled bill and its owner account`() {
        getAllWalletsWithScheduledBill().size shouldBe 0

        paymentSchedulingService.schedule(scheduledBill)

        scheduledBillRepository.findScheduledBillById(scheduledBill.billId)
            .first()
            .shouldBe(scheduledBill)

        getAllWalletsWithScheduledBill()
            .shouldHaveSize(1)
            .first()
            .shouldBe(billAdded.walletId)

        getAllWalletsWithScheduledBillsToDate()
            .shouldHaveSize(1)
            .first()
            .shouldBe(billAdded.walletId)
    }

    @Test
    fun `should create only one schedule entry for each wallet and update min schedule date`() {
        scheduledWalletRepository.save(billAdded.walletId, scheduledBill.scheduledDate.plusDays(3))

        getAllWalletsWithScheduledBillsToDate().shouldBeEmpty()

        getAllWalletsWithScheduledBillsToDate(scheduledBill.scheduledDate.plusDays(3))
            .shouldHaveSize(1)
            .first()
            .shouldBe(billAdded.walletId)

        paymentSchedulingService.schedule(scheduledBill)

        scheduledBillRepository.findScheduledBillById(scheduledBill.billId)
            .first()
            .shouldBe(scheduledBill)

        getAllWalletsWithScheduledBill()
            .shouldHaveSize(1)
            .first()
            .shouldBe(billAdded.walletId)

        getAllWalletsWithScheduledBillsToDate()
            .shouldHaveSize(1)
            .first()
            .shouldBe(billAdded.walletId)

        getAllWalletsWithScheduledBillsToDate(scheduledBill.scheduledDate.plusDays(3))
            .shouldBeEmpty()
    }

    @Test
    fun `should create only one schedule entry for each wallet and should not update min schedule date when schedule date is after min schedule date`() {
        scheduledWalletRepository.save(billAdded.walletId, scheduledBill.scheduledDate.minusDays(3))
        scheduledBillRepository.save(
            scheduledBill.copy(
                scheduledDate = scheduledBill.scheduledDate.minusDays(3),
                billId = BillId(BILL_ID_2),
            ),
        )

        getAllWalletsWithScheduledBill().shouldHaveSize(1)

        getAllWalletsWithScheduledBillsToDate().shouldBeEmpty()

        getAllWalletsWithScheduledBillsToDate(scheduledBill.scheduledDate.minusDays(3))
            .shouldHaveSize(1)
            .first()
            .shouldBe(billAdded.walletId)

        paymentSchedulingService.schedule(scheduledBill)

        scheduledBillRepository.findScheduledBillById(scheduledBill.billId)
            .first()
            .shouldBe(scheduledBill)

        getAllWalletsWithScheduledBill()
            .shouldHaveSize(1)
            .first()
            .shouldBe(billAdded.walletId)

        getAllWalletsWithScheduledBillsToDate(scheduledBill.scheduledDate.minusDays(3))
            .shouldHaveSize(1)
            .first()
            .shouldBe(billAdded.walletId)

        getAllWalletsWithScheduledBillsToDate()
            .shouldBeEmpty()
    }

    @Test
    fun `schedule an already scheduled bill should not change anything`() {
        scheduledWalletRepository.save(billAdded.walletId, scheduledBill.scheduledDate)
        scheduledBillRepository.save(scheduledBill)

        paymentSchedulingService.schedule(scheduledBill)

        scheduledBillRepository.findScheduledBillById(scheduledBill.billId)
            .first()
            .shouldBe(scheduledBill)

        getAllWalletsWithScheduledBill().shouldHaveSize(1)
            .first()
            .shouldBe(billAdded.walletId)

        getAllWalletsWithScheduledBillsToDate()
            .shouldHaveSize(1)
            .first()
            .shouldBe(billAdded.walletId)
    }

    @Test
    fun `should reschedule bill on a new schedule date`() {
        scheduledWalletRepository.save(billAdded.walletId, scheduledBill.scheduledDate)
        scheduledBillRepository.save(scheduledBill)

        getAllWalletsWithScheduledBill().shouldHaveSize(1)

        getAllWalletsWithScheduledBillsToDate()
            .shouldHaveSize(1)
            .first()
            .shouldBe(billAdded.walletId)

        val newDate = scheduledBill.scheduledDate.minusDays(1)

        getAllWalletsWithScheduledBillsToDate(newDate).shouldBeEmpty()

        val scheduledBillDifferentScheduleDate = scheduledBill.copy(scheduledDate = newDate)

        paymentSchedulingService.schedule(scheduledBillDifferentScheduleDate)

        scheduledBillRepository.findScheduledBillById(scheduledBill.billId)
            .first()
            .shouldBe(scheduledBillDifferentScheduleDate)

        getAllWalletsWithScheduledBill().shouldHaveSize(1)
            .first()
            .shouldBe(billAdded.walletId)

        getAllWalletsWithScheduledBillsToDate(newDate)
            .shouldHaveSize(1)
            .first()
            .shouldBe(billAdded.walletId)

        getAllWalletsWithScheduledBillsToDate()
            .shouldBeEmpty()
    }

    @Test
    fun `should delete entry for scheduled bill on cancel scheduled with specific batchSchedulingId`() {
        scheduledWalletRepository.save(billAdded.walletId, scheduledBill.scheduledDate)
        scheduledBillRepository.save(scheduledBill)

        getAllWalletsWithScheduledBill().shouldHaveSize(1)

        getAllWalletsWithScheduledBillsToDate()
            .shouldHaveSize(1)
            .first()
            .shouldBe(billAdded.walletId)

        paymentSchedulingService.cancelSchedule(scheduledBill.walletId, billAdded.billId, scheduledBill.batchSchedulingId)

        scheduledBillRepository.findScheduledBillById(
            scheduledBill.billId,
        ).shouldBeEmpty()

        verify { scheduledBillRepository.delete(scheduledBill) }

        getAllWalletsWithScheduledBill().shouldBeEmpty()

        getAllWalletsWithScheduledBillsToDate()
            .shouldBeEmpty()
    }

    @Test
    fun `should delete all entries for scheduled bill on cancel scheduled if no batchSchedulingId was passed`() {
        scheduledWalletRepository.save(billAdded.walletId, scheduledBill.scheduledDate)
        scheduledBillRepository.save(scheduledBill)
        scheduledBillRepository.save(
            scheduledBill.copy(
                scheduledDate = scheduledBill.scheduledDate.plusDays(4),
            ),
        )

        getAllWalletsWithScheduledBill()
            .shouldHaveSize(1)
            .first()
            .shouldBe(billAdded.walletId)

        scheduledBillRepository.findScheduledBillById(
            scheduledBill.billId,
        ).shouldHaveSize(2)

        paymentSchedulingService.cancelSchedule(scheduledBill.walletId, billAdded.billId, null)

        scheduledBillRepository.findScheduledBillById(
            scheduledBill.billId,
        ).shouldBeEmpty()

        getAllWalletsWithScheduledBill()
            .shouldBeEmpty()

        getAllWalletsWithScheduledBillsToDate()
            .shouldBeEmpty()

        getAllWalletsWithScheduledBillsToDate(scheduledBill.scheduledDate.plusDays(4))
            .shouldBeEmpty()
    }

    @Test
    fun `should delete entries for scheduled bill on cancel scheduled with more than one scheduled bill`() {
        scheduledWalletRepository.save(billAdded.walletId, scheduledBill.scheduledDate)
        scheduledBillRepository.save(scheduledBill)

        val scheduledBill2 = scheduledBill.copy(
            billId = BillId(BILL_ID_2),
            scheduledDate = scheduledBill.scheduledDate.plusDays(4),
        )
        scheduledBillRepository.save(scheduledBill2)

        getAllWalletsWithScheduledBill()
            .shouldHaveSize(1)
            .first()
            .shouldBe(billAdded.walletId)

        scheduledBillRepository.findScheduledBillById(
            scheduledBill.billId,
        ).shouldHaveSize(1)
            .first()
            .shouldBe(scheduledBill)

        scheduledBillRepository.findScheduledBillById(
            scheduledBill2.billId,
        ).shouldHaveSize(1)
            .first()
            .shouldBe(scheduledBill2)

        getAllWalletsWithScheduledBillsToDate()
            .shouldHaveSize(1)
            .first()
            .shouldBe(billAdded.walletId)

        getAllWalletsWithScheduledBillsToDate(scheduledBill2.scheduledDate)
            .shouldBeEmpty()

        paymentSchedulingService.cancelSchedule(scheduledBill.walletId, billAdded.billId, scheduledBill.batchSchedulingId)

        scheduledBillRepository.findScheduledBillById(
            scheduledBill.billId,
        ).shouldBeEmpty()

        scheduledBillRepository.findScheduledBillById(scheduledBill2.billId)
            .first()
            .shouldBe(scheduledBill2)

        getAllWalletsWithScheduledBill()
            .shouldHaveSize(1)
            .first()
            .shouldBe(billAdded.walletId)

        getAllWalletsWithScheduledBillsToDate()
            .shouldBeEmpty()

        getAllWalletsWithScheduledBillsToDate(scheduledBill2.scheduledDate)
            .shouldHaveSize(1)
            .first()
            .shouldBe(billAdded.walletId)
    }

    @Test
    fun `should do nothing on cancel non existent schedule`() {
        paymentSchedulingService.cancelSchedule(scheduledBill.walletId, billAdded.billId, null)
        scheduledBillRepository.findScheduledBillById(
            billAdded.billId,
        ).size shouldBe 0
        getAllWalletsWithScheduledBill().isEmpty() shouldBe true
    }

    @Test
    fun `should return ScheduleNotFound when schedule is not found`() {
        val result = paymentSchedulingService.reschedule(
            billId = billAdded.billId,
            date = getLocalDate(),
        )
        result.isLeft() shouldBe true
        result.mapLeft {
            it.shouldBeTypeOf<RescheduleError.ScheduleNotFound>()
        }
    }

    @Test
    fun `should return Unknown on unknown error`() {
        every {
            scheduledBillRepository.findScheduledBillById(any())
        } throws IllegalStateException()

        val result = paymentSchedulingService.reschedule(
            billId = billAdded.billId,
            date = getLocalDate(),
        )
        result.isLeft() shouldBe true
        result.mapLeft {
            it.shouldBeTypeOf<RescheduleError.Unknown>()
        }
    }

    @Test
    fun `should reschedule bill`() {
        scheduledWalletRepository.save(billAdded.walletId, scheduledBill.scheduledDate)
        scheduledBillRepository.save(scheduledBill)

        getAllWalletsWithScheduledBill()
            .shouldHaveSize(1)
            .first()
            .shouldBe(billAdded.walletId)

        getAllWalletsWithScheduledBillsToDate()
            .shouldHaveSize(1)
            .first()
            .shouldBe(billAdded.walletId)

        val expectedScheduleDate = scheduledBill.scheduledDate.plusDays(1)

        getAllWalletsWithScheduledBillsToDate(expectedScheduleDate)
            .shouldBeEmpty()

        val result = paymentSchedulingService.reschedule(
            billId = billAdded.billId,
            date = expectedScheduleDate,
        )
        result.isRight() shouldBe true
        result.map {
            scheduledBillRepository.findScheduledBillById(billAdded.billId)
                .shouldHaveSize(1)
                .first()
                .scheduledDate shouldBe expectedScheduleDate

            getAllWalletsWithScheduledBill()
                .shouldHaveSize(1)
                .first()
                .shouldBe(billAdded.walletId)

            getAllWalletsWithScheduledBillsToDate()
                .shouldBeEmpty()

            getAllWalletsWithScheduledBillsToDate(expectedScheduleDate)
                .shouldHaveSize(1)
                .first()
                .shouldBe(billAdded.walletId)
        }
    }

    private fun getAllWalletsWithScheduledBillsToDate(scheduledDate: LocalDate = scheduledBill.scheduledDate) =
        scheduledWalletRepository
            .findAllWalletsWithScheduledBillsToDate(scheduledDate)
            .buffer(100).blockLast() ?: emptyList()

    private fun getAllWalletsWithScheduledBill() =
        scheduledWalletRepository.findAllWalletsWithScheduledBills()
            .buffer(100).blockLast() ?: emptyList()
}