package ai.friday.billpayment.integration.services

import DynamoDBUtils
import ai.friday.billpayment.adapters.arbi.ArbiCheckTransferException
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.CreditCardChargebackTrackDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbTransactionRepository
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamoDAO
import ai.friday.billpayment.app.account.CreditCardPaymentStatus
import ai.friday.billpayment.app.account.DefaultCreditCardService
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.cashIn.CreditCardCashIn
import ai.friday.billpayment.app.cashIn.CreditCardCashInHandler
import ai.friday.billpayment.app.cashIn.DefaultCashInExecutor
import ai.friday.billpayment.app.cashIn.DefaultCashInHandlerLocator
import ai.friday.billpayment.app.integrations.AcquirerService
import ai.friday.billpayment.app.integrations.CashInInstrumentationService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.ScheduledBillPaymentService
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.CreditCardAuthorization
import ai.friday.billpayment.app.payment.RetryForeverTransactionException
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.SettlementOperation
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.toPayer
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.authorizedCreditCardAuthorization
import ai.friday.billpayment.createSinglePaymentDataWithBalance
import ai.friday.billpayment.creditCard
import ai.friday.billpayment.creditCardCashInTransaction
import ai.friday.billpayment.defaultTransactionEntityConverter
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.mockk.Called
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.verify
import io.mockk.verifyOrder
import java.math.BigInteger
import java.util.UUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class CreditCardCashInServiceIntegrationTest {

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer().also {
        createBillPaymentTable(it)
    }

    private val walletRepository = mockk<WalletRepository>()
    private val enhancedClient = DynamoDBUtils.setupDynamoDB()
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val transactionDAO = TransactionDynamoDAO(enhancedClient)
    private val creditCardChargebackTrackDAO = CreditCardChargebackTrackDynamoDAO(enhancedClient)

    private val transactionRepository = DynamoDbTransactionRepository(
        transactionDAO = transactionDAO,
        creditCardChargebackTrackDAO = creditCardChargebackTrackDAO,
        accountRepository = accountRepository,
        transactionEntityConverter = defaultTransactionEntityConverter(
            billEventRepository = mockk(),
            walletRepository = walletRepository,
            transactionDynamo = transactionDynamo,
        ),
    )

    private val transactionService = TransactionService(
        accountRepository = accountRepository,
        updateBillService = mockk(),
        notificationAdapter = mockk(),
        walletService = mockk(),
        transactionRepository = transactionRepository,
    )

    private val acquirerServiceMock = mockk<AcquirerService>() {
        every {
            checkStatus(any())
        } returns authorizedCreditCardAuthorization.copy(
            status = CreditCardPaymentStatus.PAYMENT_CONFIRMED,
        )
    }
    private val internalBankServiceMock = mockk<InternalBankService>() {
        every {
            prepareCashInFunds(any())
        } answers { callOriginal() }
    }

    private val balanceService = mockk<BalanceService>() {
        every {
            invalidate(any())
        } just runs
    }

    private val messagePublisher: MessagePublisher = mockk(relaxUnitFun = true)
    private val scheduledBillPaymentService: ScheduledBillPaymentService = mockk(relaxUnitFun = true)
    private val notificationAdapterMock: NotificationAdapter = mockk(relaxUnitFun = true)
    private val cashInInstrumentationService: CashInInstrumentationService = mockk(relaxUnitFun = true)
    private val systemActivityService: SystemActivityService = mockk(relaxUnitFun = true)

    private val walletFixture = WalletFixture()

    private val bankAccount = accountRepository.createAccountPaymentMethod(
        accountId = walletFixture.founderAccount.accountId,
        bankAccount = BankAccount(
            accountType = AccountType.CHECKING,
            bankNo = 1L,
            routingNo = 2L,
            accountNo = BigInteger("3"),
            accountDv = "X",
            document = walletFixture.founderAccount.document,
        ),
        position = 2,
    )
    private val wallet = walletFixture.buildWallet(accountPaymentMethodId = bankAccount.id)

    private val retryingTransaction = creditCardCashInTransaction.copy(
        id = TransactionId.build(),
        payer = walletFixture.founderAccount.toPayer(),
        status = TransactionStatus.PROCESSING,
        paymentData = createSinglePaymentDataWithBalance(
            accountPaymentMethod = creditCard.copy(accountId = wallet.founder.accountId),
            1L,
        ),
        settlementData = SettlementData(
            settlementTarget = CreditCardCashIn(amount = 1, bankAccount = bankAccount),
            serviceAmountTax = 0,
            totalAmount = 1,
        ),
        walletId = wallet.id,
        actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
    )

    private val successAuthorization =
        authorizedCreditCardAuthorization.copy(status = CreditCardPaymentStatus.PAYMENT_CONFIRMED)

    private val bankTransfer = BankTransfer(
        operationId = BankOperationId(UUID.randomUUID().toString()),
        gateway = FinancialServiceGateway.ARBI,
        status = BankOperationStatus.SUCCESS,
        amount = 1,
    )
    private val anotherRetryingTransaction = retryingTransaction.copy(
        paymentData = retryingTransaction.paymentData.toSingle().copy(
            payment = successAuthorization,
        ),
        settlementData = retryingTransaction.settlementData.copy(
            settlementOperation = bankTransfer.copy(
                status = BankOperationStatus.ERROR,
            ),
        ),
    )

    private val creditCardService: DefaultCreditCardService = mockk(relaxUnitFun = true)

    private val walletService: WalletService = mockk(relaxUnitFun = true) {
        every { findWallets(any()) } returns listOf(wallet)
    }

    private val creditCardCashinCheckout = CreditCardCashInHandler(
        acquirerService = acquirerServiceMock,
        internalBankService = internalBankServiceMock,
        transactionRepository = transactionRepository,
        balanceService = balanceService,
        notificationAdapter = notificationAdapterMock,
        walletService = walletService,
        cashInInstrumentationService = cashInInstrumentationService,
        systemActivityService = systemActivityService,
        creditCardService = creditCardService,
    ).apply {
        softDescriptor = "Test"
    }

    private val cashInExecutor = DefaultCashInExecutor(
        transactionService = transactionService,
        cashInHandlerLocator = DefaultCashInHandlerLocator(listOf(creditCardCashinCheckout)),
        messagePublisher = messagePublisher,
        scheduledBillPaymentService = scheduledBillPaymentService,
    )

    private val unknownStatusAuthorization =
        authorizedCreditCardAuthorization.copy(status = CreditCardPaymentStatus.ABORTED)

    private val retryingPaymentUnknownErrorTransacion = retryingTransaction.copy(
        paymentData = retryingTransaction.paymentData.toSingle().copy(
            payment = unknownStatusAuthorization,
        ),
        settlementData = retryingTransaction.settlementData.copy(
            settlementOperation = null,
        ),
    )

    @BeforeEach
    fun setup() {
        loadAccountIntoDb(dynamoDB, accountId = wallet.founder.accountId, document = wallet.founder.document)
        transactionRepository.save(retryingTransaction)

        every {
            walletRepository.findAccountPaymentMethod(wallet.id)
        } returns bankAccount
    }

    @Test
    fun `should FAIL transaction when payment is not authorized`() {
        val failAuthorization = authorizedCreditCardAuthorization.copy(
            status = CreditCardPaymentStatus.DENIED,
            acquirerReturnMessage = "O pagamento foi negado. Por favor, entre em contato com o seu banco.",
        )

        every {
            acquirerServiceMock.authorizeAndCapture(any(), any(), any(), any(), any())
        } returns failAuthorization

        cashInExecutor.execute(transaction = retryingTransaction)

        val transaction = transactionRepository.findById(retryingTransaction.id)

        verify {
            internalBankServiceMock wasNot called
            notificationAdapterMock.notifyCashInFailure(
                members = any(),
                amount = transaction.settlementData.totalAmount,
                errorMessage = failAuthorization.getErrorMessage(),
                walletId = wallet.id,
                walletName = any(),
            )
            cashInInstrumentationService.creditCardFailed(any())
        }

        verify(exactly = 0) {
            acquirerServiceMock.capture(any())
            acquirerServiceMock.cancel(any())
        }

        transaction.status shouldBe TransactionStatus.FAILED
        transaction.settlementData.settlementOperation shouldBe null
        transaction.paymentData.toSingle().payment?.hasError() shouldBe true
    }

    @Test
    fun `should FAIL wallet cash-in when payment is not authorized`() {
        val failAuthorization = authorizedCreditCardAuthorization.copy(
            status = CreditCardPaymentStatus.DENIED,
            acquirerReturnMessage = "O pagamento foi negado. Por favor, entre em contato com o seu banco.",
        )

        every {
            acquirerServiceMock.authorizeAndCapture(any(), any(), any(), any(), any())
        } returns failAuthorization

        every { walletService.findWallets(retryingTransaction.payer.accountId) } returns listOf(wallet)

        cashInExecutor.execute(transaction = retryingTransaction)

        val transaction = transactionRepository.findById(retryingTransaction.id)

        verify {
            internalBankServiceMock wasNot called
            notificationAdapterMock.notifyCashInFailure(
                listOf(wallet.founder),
                transaction.settlementData.totalAmount,
                failAuthorization.getErrorMessage(),
                wallet.id,
                wallet.name,
            )
            cashInInstrumentationService.creditCardFailed(any())
        }

        verify(exactly = 0) {
            acquirerServiceMock.capture(any())
            acquirerServiceMock.cancel(any())
        }

        transaction.status shouldBe TransactionStatus.FAILED
        transaction.settlementData.settlementOperation shouldBe null
        transaction.paymentData.toSingle().payment?.hasError() shouldBe true
    }

    @Test
    fun `should retry transaction on timeout authorizing payment`() {
        val failAuthorization = authorizedCreditCardAuthorization.copy(status = CreditCardPaymentStatus.ABORTED)

        every {
            acquirerServiceMock.authorizeAndCapture(any(), any(), any(), any(), any())
        } returns failAuthorization

        cashInExecutor.execute(transaction = retryingTransaction)

        verify {
            internalBankServiceMock wasNot called
            notificationAdapterMock wasNot called
            messagePublisher.sendRetryCashinMessage(transactionId = retryingTransaction.id, any())
        }

        verify(exactly = 0) {
            acquirerServiceMock.capture(any())
            acquirerServiceMock.cancel(any())
        }

        retryingTransaction.id.shouldBePersistedWith(
            transactionStatus = TransactionStatus.PROCESSING,
            settlementOperation = null,
            creditCardPaymentStatus = CreditCardPaymentStatus.ABORTED,
        )
    }

    private fun TransactionId.shouldBePersistedWith(
        transactionStatus: TransactionStatus,
        settlementOperation: SettlementOperation?,
        creditCardPaymentStatus: CreditCardPaymentStatus,
    ) {
        val transaction = transactionRepository.findById(this)
        val paymentData = transaction.paymentData.toSingle().get<CreditCardAuthorization>()

        transaction.status shouldBe transactionStatus
        transaction.settlementData.settlementOperation shouldBe settlementOperation
        paymentData.status shouldBe creditCardPaymentStatus
        paymentData.tid shouldBe "tid"
        paymentData.authorizationCode shouldBe "authorizationCode"
    }

    @ParameterizedTest
    @EnumSource(BankOperationStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["SUCCESS"])
    fun `should retry transaction when payment is authorized but transfer fails`(
        bankOperationStatus: BankOperationStatus,
    ) {
        every {
            acquirerServiceMock.authorizeAndCapture(any(), any(), any(), any(), any())
        } returns successAuthorization

        val bankTransfer = BankTransfer(
            operationId = BankOperationId(UUID.randomUUID().toString()),
            gateway = FinancialServiceGateway.ARBI,
            status = bankOperationStatus,
            amount = 1,
        )
        every {
            internalBankServiceMock.cashInFunds(any(), any(), any())
        } returns bankTransfer

        cashInExecutor.execute(transaction = retryingTransaction)

        verify {
            internalBankServiceMock.cashInFunds(any(), any(), any())
            messagePublisher.sendRetryCashinMessage(transactionId = retryingTransaction.id, any())
        }

        verify(exactly = 0) {
            cashInInstrumentationService wasNot Called
            scheduledBillPaymentService.process(any())
            acquirerServiceMock.cancel(any())
            acquirerServiceMock.capture(any())
        }

        retryingTransaction.id.shouldBePersistedWith(
            transactionStatus = TransactionStatus.PROCESSING,
            settlementOperation = bankTransfer,
            creditCardPaymentStatus = CreditCardPaymentStatus.PAYMENT_CONFIRMED,
        )
    }

    @Test
    fun `should COMPLETE transaction when payment is authorized and captured`() {
        every {
            acquirerServiceMock.authorizeAndCapture(any(), any(), any(), any(), any())
        } returns successAuthorization

        every {
            internalBankServiceMock.cashInFunds(any(), any(), any())
        } returns bankTransfer

        cashInExecutor.execute(transaction = retryingTransaction)

        verify {
            acquirerServiceMock.authorizeAndCapture(any(), any(), any(), any(), any())
            internalBankServiceMock.cashInFunds(any(), any(), any())
        }

        val creditCardCashIn = anotherRetryingTransaction.settlementData.getTarget<CreditCardCashIn>()
        verifyOrder {
            balanceService.invalidate(creditCardCashIn.bankAccount.id)
            systemActivityService.setHasCashedIn(anotherRetryingTransaction.walletId)
            cashInInstrumentationService.creditCardSucceeded(retryingTransaction)
            scheduledBillPaymentService.process(anotherRetryingTransaction.walletId)
        }

        verify(exactly = 0) { acquirerServiceMock.cancel(any()) }

        retryingTransaction.id.shouldBePersistedWith(
            transactionStatus = TransactionStatus.COMPLETED,
            settlementOperation = bankTransfer,
            creditCardPaymentStatus = CreditCardPaymentStatus.PAYMENT_CONFIRMED,
        )
    }

    @Test
    fun `should complete transaction when retrying a failed bank operation and query finds successful transfer`() {
        transactionRepository.save(anotherRetryingTransaction)

        every {
            internalBankServiceMock.checkCashInFunds(
                operationId = anotherRetryingTransaction.settlementData.getOperation<BankTransfer>().operationId,
                operationDate = any(),
            )
        } returns true

        cashInExecutor.retryTransaction(anotherRetryingTransaction.id)

        retryingTransaction.id.shouldBePersistedWith(
            transactionStatus = TransactionStatus.COMPLETED,
            settlementOperation = bankTransfer,
            creditCardPaymentStatus = CreditCardPaymentStatus.PAYMENT_CONFIRMED,
        )

        verify(exactly = 0) {
            internalBankServiceMock.cashInFunds(userFullAccountNo = any(), amount = any(), operationId = any())
        }
    }

    @Test
    fun `should retry transaction when retrying a failed bank operation and the error remains`() {
        transactionRepository.save(anotherRetryingTransaction)

        every {
            internalBankServiceMock.checkCashInFunds(
                operationId = anotherRetryingTransaction.settlementData.getOperation<BankTransfer>().operationId,
                operationDate = any(),
            )
        } returns false

        val retryBankTransfer = bankTransfer.copy(
            operationId = BankOperationId(UUID.randomUUID().toString()),
            status = BankOperationStatus.ERROR,
        )
        every {
            internalBankServiceMock.cashInFunds(userFullAccountNo = any(), amount = any(), operationId = any())
        } returns retryBankTransfer

        val result = cashInExecutor.retryTransaction(anotherRetryingTransaction.id)
        result.isFailure shouldBe true
        result.getOrElse {
            it.shouldBeInstanceOf<RetryForeverTransactionException>()
        }

        anotherRetryingTransaction.id.shouldBePersistedWith(
            transactionStatus = TransactionStatus.PROCESSING,
            settlementOperation = retryBankTransfer,
            creditCardPaymentStatus = CreditCardPaymentStatus.PAYMENT_CONFIRMED,
        )
    }

    @Test
    fun `should COMPLETE transaction when retrying a failed bank operation and it succeeds`() {
        transactionRepository.save(anotherRetryingTransaction)

        every {
            internalBankServiceMock.checkCashInFunds(
                operationId = anotherRetryingTransaction.settlementData.getOperation<BankTransfer>().operationId,
                operationDate = any(),
            )
        } returns false

        val retryBankTransfer = bankTransfer.copy(operationId = BankOperationId(UUID.randomUUID().toString()))
        every {
            internalBankServiceMock.cashInFunds(userFullAccountNo = any(), amount = any(), operationId = any())
        } returns retryBankTransfer

        cashInExecutor.retryTransaction(anotherRetryingTransaction.id)

        anotherRetryingTransaction.id.shouldBePersistedWith(
            transactionStatus = TransactionStatus.COMPLETED,
            settlementOperation = retryBankTransfer,
            creditCardPaymentStatus = CreditCardPaymentStatus.PAYMENT_CONFIRMED,
        )

        verify {
            with(anotherRetryingTransaction.settlementData.getTarget<CreditCardCashIn>()) {
                internalBankServiceMock.cashInFunds(
                    userFullAccountNo = (bankAccount.method as InternalBankAccount).buildFullAccountNumber(),
                    amount = amount,
                    operationId = any(),
                )
            }
        }
    }

    @Test
    fun `should retry transaction when retrying a failed bank operation and the check cash in fails`() {
        transactionRepository.save(anotherRetryingTransaction)

        every {
            internalBankServiceMock.checkCashInFunds(
                operationId = anotherRetryingTransaction.settlementData.getOperation<BankTransfer>().operationId,
                operationDate = any(),
            )
        } throws ArbiCheckTransferException()

        assertThrows<ArbiCheckTransferException> {
            cashInExecutor.retryTransaction(anotherRetryingTransaction.id)
        }

        anotherRetryingTransaction.id.shouldBePersistedWith(
            transactionStatus = TransactionStatus.PROCESSING,
            settlementOperation = anotherRetryingTransaction.settlementData.settlementOperation,
            creditCardPaymentStatus = CreditCardPaymentStatus.PAYMENT_CONFIRMED,
        )

        verify(exactly = 0) {
            internalBankServiceMock.cashInFunds(userFullAccountNo = any(), amount = any(), operationId = any())
        }
    }

    @Test
    fun `should retry transaction when capture is unknown error and the error remains`() {
        transactionRepository.save(retryingPaymentUnknownErrorTransacion)

        every {
            acquirerServiceMock.checkStatus(retryingPaymentUnknownErrorTransacion.id.value)
        } returns unknownStatusAuthorization

        val result = cashInExecutor.retryTransaction(anotherRetryingTransaction.id)
        result.isFailure shouldBe true
        result.getOrElse {
            it.shouldBeInstanceOf<RetryForeverTransactionException>()
        }

        anotherRetryingTransaction.id.shouldBePersistedWith(
            transactionStatus = TransactionStatus.PROCESSING,
            settlementOperation = null,
            creditCardPaymentStatus = CreditCardPaymentStatus.ABORTED,
        )

        verify(exactly = 0) {
            internalBankServiceMock.cashInFunds(userFullAccountNo = any(), amount = any(), operationId = any())
        }
    }

    @Test
    fun `should FAIL transaction when retrying and capture failed`() {
        transactionRepository.save(retryingPaymentUnknownErrorTransacion)

        val currentAuthorization = unknownStatusAuthorization.copy(
            status = CreditCardPaymentStatus.DENIED,
        )
        every {
            acquirerServiceMock.checkStatus(retryingPaymentUnknownErrorTransacion.id.value)
        } returns currentAuthorization

        cashInExecutor.retryTransaction(anotherRetryingTransaction.id)

        anotherRetryingTransaction.id.shouldBePersistedWith(
            transactionStatus = TransactionStatus.FAILED,
            settlementOperation = null,
            creditCardPaymentStatus = currentAuthorization.status,
        )

        verify {
            notificationAdapterMock.notifyCashInFailure(
                members = any(),
                amount = anotherRetryingTransaction.settlementData.totalAmount,
                errorMessage = currentAuthorization.getErrorMessage(),
                walletId = wallet.id,
                walletName = any(),
            )
            cashInInstrumentationService.creditCardFailed(any())
        }
        verify(exactly = 0) {
            internalBankServiceMock.cashInFunds(userFullAccountNo = any(), amount = any(), operationId = any())
        }
    }

    @Test
    fun `should retry transaction when retrying and capture worked but transfer fails`() {
        transactionRepository.save(retryingPaymentUnknownErrorTransacion)

        val currentAuthorization = unknownStatusAuthorization.copy(
            status = CreditCardPaymentStatus.PAYMENT_CONFIRMED,
        )
        every {
            acquirerServiceMock.checkStatus(retryingPaymentUnknownErrorTransacion.id.value)
        } returns currentAuthorization

        val retryBankTransfer = bankTransfer.copy(
            operationId = BankOperationId(UUID.randomUUID().toString()),
            status = BankOperationStatus.ERROR,
        )
        every {
            internalBankServiceMock.cashInFunds(userFullAccountNo = any(), amount = any(), operationId = any())
        } returns retryBankTransfer

        val result = cashInExecutor.retryTransaction(anotherRetryingTransaction.id)
        result.isFailure shouldBe true
        result.getOrElse {
            it.shouldBeInstanceOf<RetryForeverTransactionException>()
        }

        anotherRetryingTransaction.id.shouldBePersistedWith(
            transactionStatus = TransactionStatus.PROCESSING,
            settlementOperation = retryBankTransfer,
            creditCardPaymentStatus = currentAuthorization.status,
        )

        verify {
            notificationAdapterMock wasNot called
        }
        verify(exactly = 0) {
            internalBankServiceMock.checkCashInFunds(operationId = any(), operationDate = any())
        }
        verify {
            scheduledBillPaymentService wasNot called
        }
    }

    @Test
    fun `should complete transaction when retrying and capture worked and transfer works`() {
        transactionRepository.save(retryingPaymentUnknownErrorTransacion)

        val currentAuthorization = unknownStatusAuthorization.copy(
            status = CreditCardPaymentStatus.PAYMENT_CONFIRMED,
        )
        every {
            acquirerServiceMock.checkStatus(retryingPaymentUnknownErrorTransacion.id.value)
        } returns currentAuthorization

        val retryBankTransfer = bankTransfer.copy(operationId = BankOperationId(UUID.randomUUID().toString()))
        every {
            internalBankServiceMock.cashInFunds(userFullAccountNo = any(), amount = any(), operationId = any())
        } returns retryBankTransfer

        cashInExecutor.retryTransaction(anotherRetryingTransaction.id)

        anotherRetryingTransaction.id.shouldBePersistedWith(
            transactionStatus = TransactionStatus.COMPLETED,
            settlementOperation = retryBankTransfer,
            creditCardPaymentStatus = currentAuthorization.status,
        )

        val transactionSlot = slot<Transaction>()

        verify {
            scheduledBillPaymentService.process(anotherRetryingTransaction.walletId)
            systemActivityService.setHasCashedIn(anotherRetryingTransaction.walletId)
            cashInInstrumentationService.creditCardSucceeded(capture(transactionSlot))
        }

        with(transactionSlot.captured) {
            this.id shouldBe anotherRetryingTransaction.id
            this.type shouldBe TransactionType.CASH_IN
            this.payer shouldBe anotherRetryingTransaction.payer
            this.paymentData.toSingle().accountPaymentMethod.accountId shouldBe anotherRetryingTransaction.paymentData.toSingle().accountPaymentMethod.accountId
            this.paymentData.toSingle().accountPaymentMethod.method.type shouldBe anotherRetryingTransaction.paymentData.toSingle().accountPaymentMethod.method.type
        }

        verify(exactly = 0) {
            internalBankServiceMock.checkCashInFunds(operationId = any(), operationDate = any())
        }
    }
}