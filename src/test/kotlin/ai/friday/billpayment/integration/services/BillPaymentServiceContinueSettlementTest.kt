package ai.friday.billpayment.integration.services

import ai.friday.billpayment.CouldNotAcquireLockException
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.payment.AsyncCheckout
import ai.friday.billpayment.app.payment.AsyncSettlementCheckout
import ai.friday.billpayment.app.payment.BillPaymentService
import ai.friday.billpayment.app.payment.BoletoSettlementResult
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.app.payment.Checkout
import ai.friday.billpayment.app.payment.CheckoutLocator
import ai.friday.billpayment.app.payment.SyncCheckout
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.transaction.CompleteTransaction
import ai.friday.billpayment.app.payment.transaction.FailTransaction
import ai.friday.billpayment.app.payment.transaction.PrepareTransaction
import ai.friday.billpayment.app.payment.transaction.RollbackTransaction
import ai.friday.billpayment.app.payment.transaction.StartTransaction
import ai.friday.billpayment.app.payment.transaction.TransactionInProcess
import ai.friday.billpayment.boletoTransaction
import ai.friday.billpayment.createBoletoSettlementResult
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import net.javacrumbs.shedlock.core.SimpleLock
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class BillPaymentServiceContinueSettlementTest {

    private val checkout: AsyncSettlementCheckout = mockk(relaxed = true)

    private val checkoutLocator: CheckoutLocator = mockk()

    private val transactionService: TransactionService = mockk()

    private val completeTransaction: CompleteTransaction = mockk(relaxed = true)

    private val prepareTransaction: PrepareTransaction = mockk(relaxed = true)

    private val startTransaction: StartTransaction = mockk()

    private val failTransaction: FailTransaction = mockk(relaxed = true)

    private val rollbackTransaction: RollbackTransaction = mockk(relaxed = true)

    private val transactionInProcess: TransactionInProcess = mockk(relaxed = true)

    private val simpleLock: SimpleLock = mockk(relaxed = true)

    private val lockProvider: InternalLock = mockk {
        every { acquireLock(any()) } returns simpleLock
    }

    val service = BillPaymentService(
        transactionService = transactionService,
        checkoutLocator = checkoutLocator,
        startTransaction = startTransaction,
        prepareTransaction = prepareTransaction,
        completeTransaction = completeTransaction,
        failTransaction = failTransaction,
        transactionInProcess = transactionInProcess,
        lockProvider = lockProvider,
        rollbackTransaction = rollbackTransaction,
    )

    private lateinit var transaction: Transaction

    @BeforeEach
    fun setup() {
        transaction = boletoTransaction.copy(status = TransactionStatus.PROCESSING)

        every { transactionService.findTransactionById(transaction.id) } returns transaction
        every { checkoutLocator.getCheckout(transaction) } returns checkout
    }

    @ParameterizedTest
    @MethodSource("getCheckout")
    fun `deve lancar exception quando o checkout não for um AsyncSettlementCheckout`(checkout: Checkout) {
        every { checkoutLocator.getCheckout(transaction) } returns checkout
        assertThrows<IllegalStateException> {
            service.continueSettlement(transaction.id, createBoletoSettlementResult(BoletoSettlementStatus.CONFIRMED, transaction.id.value))
        }

        verify {
            failTransaction wasNot called
            completeTransaction wasNot called
            transactionInProcess wasNot called
        }
    }

    @Test
    fun `deve falhar a transacao quando ocorrer um erro no checkout`() {
        val settlementResult = mockk<BoletoSettlementResult>(relaxed = true)

        every {
            checkout.continueSettlement(transaction, settlementResult)
        } answers {
            arg<Transaction>(0).apply { status = TransactionStatus.FAILED }
        }

        service.continueSettlement(transaction.id, settlementResult)

        verify {
            rollbackTransaction.rollback(transaction)
            checkout.continueSettlement(transaction, settlementResult)
        }

        verify(exactly = 0) {
            transactionService.save(any())
            failTransaction.execute(any())
            completeTransaction.execute(any())
            transactionInProcess.execute(any())
        }
    }

    @Test
    fun `deve completar a transacao quando o checkout responder que a transacao esta finalizada`() {
        val settlementResult = mockk<BoletoSettlementResult>(relaxed = true)

        every {
            checkout.continueSettlement(transaction, settlementResult)
        } answers {
            arg<Transaction>(0).apply { status = TransactionStatus.COMPLETED }
        }

        service.continueSettlement(transaction.id, settlementResult)

        verify {
            completeTransaction.execute(transaction)
            checkout.continueSettlement(transaction, settlementResult)
        }

        verify(exactly = 0) {
            transactionService.save(transaction)
            failTransaction.execute(any())
            transactionInProcess.execute(any())
        }
    }

    @Test
    fun `should not process transaction when lock cannot be acquired`() {
        val settlementResult = mockk<BoletoSettlementResult>(relaxed = true)
        every { lockProvider.acquireLock(any()) } returns null

        val exception = assertThrows<CouldNotAcquireLockException> {
            service.continueSettlement(transaction.id, settlementResult)
        }

        verify(exactly = 1) { lockProvider.acquireLock(any()) }
        verify(exactly = 0) {
            checkout.continueSettlement(any(), any())
            completeTransaction.execute(any())
            failTransaction.execute(any())
            transactionInProcess.execute(any())
            simpleLock.unlock()
        }
    }

    @Test
    fun `should acquire and release lock during successful transaction processing`() {
        val settlementResult = mockk<BoletoSettlementResult>(relaxed = true)

        every {
            checkout.continueSettlement(transaction, settlementResult)
        } answers {
            arg<Transaction>(0).apply { status = TransactionStatus.COMPLETED }
        }

        service.continueSettlement(transaction.id, settlementResult)

        verify(exactly = 1) { lockProvider.acquireLock(any()) }
        verify(exactly = 1) { simpleLock.unlock() }
    }

    @Test
    fun `should acquire and release lock during failed transaction processing`() {
        val settlementResult = mockk<BoletoSettlementResult>(relaxed = true)

        every {
            checkout.continueSettlement(transaction, settlementResult)
        } answers {
            arg<Transaction>(0).apply { status = TransactionStatus.FAILED }
        }

        service.continueSettlement(transaction.id, settlementResult)

        verify(exactly = 1) { lockProvider.acquireLock(any()) }
        verify(exactly = 1) { simpleLock.unlock() }
    }

    companion object {
        @JvmStatic
        fun getCheckout() = listOf(
            mockk<SyncCheckout>(),
            mockk<AsyncCheckout>(),
        )
    }
}