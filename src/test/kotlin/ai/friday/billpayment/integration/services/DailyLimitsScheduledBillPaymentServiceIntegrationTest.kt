package ai.friday.billpayment.integration.services

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.InternalBankDBRepository
import ai.friday.billpayment.adapters.dynamodb.InternalBankDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.ScheduledBillDBRepository
import ai.friday.billpayment.adapters.dynamodb.ScheduledBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.ScheduledWalletDBRepository
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.adapters.micronaut.WalletConfigurationMicronaut
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.balance.DefaultBalanceService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.BillPaymentScheduleStarted
import ai.friday.billpayment.app.bill.BillPaymentScheduledBalanceInfo
import ai.friday.billpayment.app.bill.BillSchedulePostponed
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.integrations.BankAccountService
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.payment.BillPaymentService
import ai.friday.billpayment.app.payment.DefaultScheduledBillPaymentService
import ai.friday.billpayment.app.payment.PaymentSchedulingService
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.DailyPaymentLimitType
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.WalletStatus
import ai.friday.billpayment.app.wallet.WalletType
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.createPaymentMethodsDetailWithBalance
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.BILL_ID
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.BILL_ID_3
import ai.friday.billpayment.integration.BILL_ID_4
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.EMAIL
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.createPaidInvoice
import ai.friday.billpayment.integration.createPaidPix
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.integration.loadBalancePaymentMethod
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.invoicePaymentScheduled
import ai.friday.billpayment.omnibusBankAccountConfiguration
import ai.friday.billpayment.paymentMethodId
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.pixPaymentScheduled
import ai.friday.billpayment.scheduledBill
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNot
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.types.beOfType
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import java.time.Duration
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient

class DailyLimitsScheduledBillPaymentServiceIntegrationTest {
    private val walletPaymentMethodId = primaryWallet.paymentMethodId

    @BeforeEach
    fun setup() {
        clearAllMocks()

        createBillPaymentTable(dynamoDB)

        createBillEventTable(dynamoDB)

        loadAccountIntoDb(dynamoDB, accountId, hasCreditCard = false)

        every { billPaymentServiceMock.execute(any()) } returns TransactionId("X")

        every { bankAccountServiceMock.getBalance(any()) } returns 100000L

        walletRepository.save(primaryWallet)

        configureDailyLimits(dailyLimit = walletLimit, nighttimeLimit = walletLimit)

        loadBalancePaymentMethod(
            accountRepository,
            accountId = primaryWallet.founder.accountId.value,
            paymentMethodId = walletPaymentMethodId.value,
        )
    }

    @Test
    fun `should not postpone BOLETO bills when limit is reached`() {
        withGivenDateTime(getZonedDateTime().withHour(6)) {
            createPaidPix(client = enhancedClient, walletId = primaryWalletId, amount = 499)
            createPaidInvoice(client = enhancedClient, walletId = primaryWalletId, amount = 599)
        }

        withGivenDateTime(getZonedDateTime().withHour(12)) {
            createConcessionariaScheduledBill(
                billId = BILL_ID,
                walletId = primaryWalletId,
                amount = 4990,
                walletPaymentMethodId,
            )
            createFichaCompensacaoScheduledBill(
                billId = BILL_ID_2,
                walletId = primaryWalletId,
                amount = 3990,
                walletPaymentMethodId,
            )

            scheduledBillPaymentService.process(primaryWallet.id, getZonedDateTime().toLocalDate())
        }

        verify(exactly = 2) { billPaymentServiceMock.execute(any()) }

        var result = billEventRepository.getBillById(BillId(BILL_ID))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldNotBePostponed()
        }
        result = billEventRepository.getBillById(BillId(BILL_ID_2))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldNotBePostponed()
        }
    }

    @Test
    fun `should unschedule PIX and INVOICE bills when limit is reached`() {
        withGivenDateTime(getZonedDateTime().withHour(6)) {
            createPaidPix(client = enhancedClient, walletId = primaryWalletId, amount = walletLimit)
        }

        withGivenDateTime(getZonedDateTime().withHour(12)) {
            createPixScheduledBill(
                billId = BILL_ID_3,
                walletId = primaryWalletId,
                amount = 1,
                walletPaymentMethodId,
            )
            createInvoiceScheduledBill(billId = BILL_ID_4, walletId = primaryWalletId, amount = 1)

            scheduledBillPaymentService.process(primaryWallet.id, getZonedDateTime().toLocalDate())
        }

        verify(exactly = 0) { billPaymentServiceMock.execute(any()) }

        var result = billEventRepository.getBillById(BillId(BILL_ID_3))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldBeUnscheduled()
        }
        result = billEventRepository.getBillById(BillId(BILL_ID_4))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldBeUnscheduled()
        }
    }

    @Test
    fun `should include paid bills before daytime payment start hour`() {
        configureDailyLimits(dailyLimit = 10_000, nighttimeLimit = 1_000)

        withGivenDateTime(getZonedDateTime().withHour(5)) {
            createPaidPix(client = enhancedClient, walletId = primaryWalletId, amount = 1_000)
        }

        withGivenDateTime(getZonedDateTime().withHour(12)) {
            createInvoiceScheduledBill(billId = BILL_ID_3, walletId = primaryWalletId, amount = 9_000)
            createPixScheduledBill(
                billId = BILL_ID_4,
                walletId = primaryWalletId,
                amount = 100,
                walletPaymentMethodId,
            )

            scheduledBillPaymentService.process(primaryWallet.id, getZonedDateTime().toLocalDate())
        }

        verify(exactly = 1) { billPaymentServiceMock.execute(any()) }

        var result = billEventRepository.getBillById(BillId(BILL_ID_3))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldNotBePostponed()
        }
        result = billEventRepository.getBillById(BillId(BILL_ID_4))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldBeUnscheduled()
        }
    }

    @Test
    fun `should consider daily limit is from 0AM to 23_59PM`() {
        configureDailyLimits(dailyLimit = 10_000, nighttimeLimit = 1_000)

        withGivenDateTime(getZonedDateTime().withHour(23).withMinute(59)) {
            createPaidPix(client = enhancedClient, walletId = primaryWalletId, amount = 500)
        }

        withGivenDateTime(getZonedDateTime().plusDays(1).withHour(0).withMinute(0)) {
            createPaidPix(client = enhancedClient, walletId = primaryWalletId, amount = 500)
        }

        withGivenDateTime(getZonedDateTime().plusDays(1).withHour(8)) {
            createInvoiceScheduledBill(billId = BILL_ID_3, walletId = primaryWalletId, amount = 9_500)
            createPixScheduledBill(
                billId = BILL_ID_4,
                walletId = primaryWalletId,
                amount = 100,
                walletPaymentMethodId,
            )

            scheduledBillPaymentService.process(primaryWallet.id, getZonedDateTime().toLocalDate())
        }

        verify(exactly = 1) { billPaymentServiceMock.execute(any()) }

        var result = billEventRepository.getBillById(BillId(BILL_ID_3))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldNotBePostponed()
        }
        result = billEventRepository.getBillById(BillId(BILL_ID_4))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldBeUnscheduled()
        }
    }

    @Test
    fun `should allow first bill to be paid and postpone second one`() {
        withGivenDateTime(getZonedDateTime().withHour(6)) {
            createPaidPix(client = enhancedClient, walletId = primaryWalletId, amount = 400)
        }

        withGivenDateTime(getZonedDateTime().withHour(12)) {
            createInvoiceScheduledBill(billId = BILL_ID_3, walletId = primaryWalletId, amount = 500)
            createPixScheduledBill(
                billId = BILL_ID_4,
                walletId = primaryWalletId,
                amount = 300,
                walletPaymentMethodId,
            )

            scheduledBillPaymentService.process(primaryWallet.id, getZonedDateTime().toLocalDate())
        }

        verify(exactly = 1) { billPaymentServiceMock.execute(any()) }

        var result = billEventRepository.getBillById(BillId(BILL_ID_3))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldNotBePostponed()
        }
        result = billEventRepository.getBillById(BillId(BILL_ID_4))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldBeUnscheduled()
        }
    }

    @Test
    fun `should unschedule bill when nighttime limit is reached`() {
        configureDailyLimits(10000L, 400L)

        withGivenDateTime(getZonedDateTime().withHour(6)) {
            createPaidPix(client = enhancedClient, walletId = primaryWalletId, amount = 9500)
        }

        withGivenDateTime(getZonedDateTime().withHour(20)) {
            createPixScheduledBill(
                billId = BILL_ID_3,
                walletId = primaryWalletId,
                amount = 500,
                walletPaymentMethodId,
            )
            createPixScheduledBill(
                billId = BILL_ID_4,
                walletId = primaryWalletId,
                amount = 300,
                walletPaymentMethodId,
            )

            scheduledBillPaymentService.process(primaryWallet.id, getZonedDateTime().toLocalDate())
        }

        verify(exactly = 1) { billPaymentServiceMock.execute(any()) }

        var result = billEventRepository.getBillById(BillId(BILL_ID_3))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldBeUnscheduled()
        }

        result = billEventRepository.getBillById(BillId(BILL_ID_4))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldNotBePostponed()
        }
    }

    @Test
    fun `should unschedule bill when daily limit is reached after 0am`() {
        configureDailyLimits(10_000L, 1_000L)

        withGivenDateTime(getZonedDateTime().withHour(12)) {
            createPaidPix(client = enhancedClient, walletId = primaryWalletId, amount = 9500)
        }

        withGivenDateTime(getZonedDateTime().withHour(23)) {
            createPaidPix(client = enhancedClient, walletId = primaryWalletId, amount = 400)
        }

        withGivenDateTime(getZonedDateTime().plusDays(1).withHour(2)) {
            createPixScheduledBill(
                billId = BILL_ID_3,
                walletId = primaryWalletId,
                amount = 200,
                walletPaymentMethodId,
            )

            scheduledBillPaymentService.process(primaryWallet.id, getZonedDateTime().toLocalDate())
        }

        verify(exactly = 0) { billPaymentServiceMock.execute(any()) }

        val result = billEventRepository.getBillById(BillId(BILL_ID_3))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldBeUnscheduled()
        }
    }

    @Test
    fun `não deve considerar limite para pagamento de transferencia para titular da carteira`() {
        configureDailyLimits(10_000L, 1_000L)

        withGivenDateTime(getZonedDateTime().withHour(12)) {
            createPaidPix(client = enhancedClient, walletId = primaryWalletId, amount = 9500)
        }

        withGivenDateTime(getZonedDateTime().withHour(23)) {
            createPaidPix(client = enhancedClient, walletId = primaryWalletId, amount = 400)
        }

        val billId = BillId()
        withGivenDateTime(getZonedDateTime().plusDays(1).withHour(2)) {
            createPixScheduledBill(
                billId = billId.value,
                walletId = primaryWalletId,
                amount = 200,
                walletPaymentMethodId,
                isSelfTransfer = true,
            )

            scheduledBillRepository.findScheduledBillById(billId)
            scheduledBillPaymentService.process(primaryWallet.id, getZonedDateTime().toLocalDate())
        }

        verify(exactly = 1) { billPaymentServiceMock.execute(any()) }

        val result = billEventRepository.getBillById(billId)
        result.isRight() shouldBe true
        result.map { bill ->
            bill.paymentShouldBeStarted()
        }
    }

    @Test
    fun `deve cancelar o agendamento a conta quando o limite mensal for atingido para todos os débitos`() {
        configureDailyLimits(10000L, 400L, 10000L)

        withGivenDateTime(getZonedDateTime().withDayOfMonth(1).withHour(6)) {
            createPaidPix(client = enhancedClient, walletId = primaryWalletId, amount = 8000)
            createPaidInvoice(client = enhancedClient, walletId = primaryWalletId, amount = 1800)
        }

        withGivenDateTime(getZonedDateTime().withDayOfMonth(4).withHour(12)) {
            createPixScheduledBill(
                billId = BILL_ID_2,
                walletId = primaryWalletId,
                amount = 200,
                walletPaymentMethodId,
            )
            createFichaCompensacaoScheduledBill(
                billId = BILL_ID_3,
                walletId = primaryWalletId,
                amount = 500,
                walletPaymentMethodId,
            )
            createConcessionariaScheduledBill(
                billId = BILL_ID_4,
                walletId = primaryWalletId,
                amount = 300,
                walletPaymentMethodId,
            )

            scheduledBillPaymentService.process(primaryWallet.id, getZonedDateTime().toLocalDate())
        }

        verify { billPaymentServiceMock.execute(any()) }

        var result = billEventRepository.getBillById(BillId(BILL_ID_3))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldBeUnscheduled()
        }

        result = billEventRepository.getBillById(BillId(BILL_ID_4))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldBeUnscheduled()
        }

        result = billEventRepository.getBillById(BillId(BILL_ID_2))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.paymentShouldBeStarted()
        }
    }

    @Test
    fun `deve pagar a conta quando o mudar o mês de calendário`() {
        configureDailyLimits(10000L, 400L, 10000L)

        withGivenDateTime(getZonedDateTime().withDayOfMonth(1).minusDays(1).withHour(6)) {
            createPaidPix(client = enhancedClient, walletId = primaryWalletId, amount = 8000)
            createPaidInvoice(client = enhancedClient, walletId = primaryWalletId, amount = 1800)
        }

        withGivenDateTime(getZonedDateTime().withDayOfMonth(4).withHour(12)) {
            createFichaCompensacaoScheduledBill(
                billId = BILL_ID_3,
                walletId = primaryWalletId,
                amount = 500,
                walletPaymentMethodId,
            )

            scheduledBillPaymentService.process(primaryWallet.id, getZonedDateTime().toLocalDate())
        }

        verify { billPaymentServiceMock.execute(any()) }

        val result = billEventRepository.getBillById(BillId(BILL_ID_3))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldNotBePostponed()
        }
    }

    @Test
    fun `should unschedule bills when daily limit is reached before nighttime`() {
        configureDailyLimits(10000L, 400L)

        withGivenDateTime(getZonedDateTime().withHour(6)) {
            createPaidPix(client = enhancedClient, walletId = primaryWalletId, amount = 10000)
        }

        withGivenDateTime(getZonedDateTime().plusDays(1).withHour(2)) {
            createPixScheduledBill(
                billId = BILL_ID_3,
                walletId = primaryWalletId,
                amount = 500,
                walletPaymentMethodId,
            )
            createPixScheduledBill(
                billId = BILL_ID_4,
                walletId = primaryWalletId,
                amount = 300,
                walletPaymentMethodId,
            )

            scheduledBillPaymentService.process(primaryWallet.id, getZonedDateTime().toLocalDate())
        }

        verify(exactly = 0) { billPaymentServiceMock.execute(any()) }

        var result = billEventRepository.getBillById(BillId(BILL_ID_3))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldBeUnscheduled()
        }

        result = billEventRepository.getBillById(BillId(BILL_ID_4))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldBeUnscheduled()
        }
    }

    @Test
    fun `should unschedule bill when there is daily limit but nighttime limit is reached`() {
        configureDailyLimits(10000L, 400L)

        withGivenDateTime(getZonedDateTime().withHour(21)) {
            createPaidPix(client = enhancedClient, walletId = primaryWalletId, amount = 400)
        }

        withGivenDateTime(getZonedDateTime().plusDays(1).withHour(2)) {
            createPixScheduledBill(
                billId = BILL_ID_3,
                walletId = primaryWalletId,
                amount = 500,
                walletPaymentMethodId,
            )
            createPixScheduledBill(
                billId = BILL_ID_4,
                walletId = primaryWalletId,
                amount = 300,
                walletPaymentMethodId,
            )
            scheduledBillPaymentService.process(primaryWallet.id, getZonedDateTime().toLocalDate())
        }

        verify(exactly = 0) { billPaymentServiceMock.execute(any()) }

        var result = billEventRepository.getBillById(BillId(BILL_ID_3))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldBeUnscheduled()
        }

        result = billEventRepository.getBillById(BillId(BILL_ID_4))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldBeUnscheduled()
        }
    }

    @Test
    fun `should unschedule bill when amount is higher than daily limit`() {
        configureDailyLimits(100_00L, 400L)

        withGivenDateTime(getZonedDateTime().withHour(12)) {
            createPixScheduledBill(
                billId = BILL_ID_3,
                walletId = primaryWalletId,
                amount = 100_01L,
                walletPaymentMethodId,
            )
            scheduledBillPaymentService.process(primaryWallet.id, getZonedDateTime().toLocalDate())
        }

        verify(exactly = 0) { billPaymentServiceMock.execute(any()) }

        val result = billEventRepository.getBillById(BillId(BILL_ID_3))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldBeUnscheduled()
        }
    }

    @Test
    fun `should unschedule bill when it is over due`() {
        configureDailyLimits(100_00L, 400L)

        withGivenDateTime(getZonedDateTime().withHour(12)) {
            createPixScheduledBill(
                billId = BILL_ID_3,
                walletId = primaryWalletId,
                amount = 100_01L,
                walletPaymentMethodId,
            )
        }

        withGivenDateTime(getZonedDateTime().plusDays(1).withHour(12)) {
            scheduledBillPaymentService.process(primaryWallet.id, getZonedDateTime().toLocalDate())
        }

        verify(exactly = 0) { billPaymentServiceMock.execute(any()) }

        val result = billEventRepository.getBillById(BillId(BILL_ID_3))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldBeUnscheduled()
        }
    }

    @Test
    fun `should try to pay bill when it is over due but does not expire`() {
        configureDailyLimits(100_00L, 400L)

        withGivenDateTime(getZonedDateTime().withHour(12)) {
            createPixScheduledBill(
                billId = BILL_ID_3,
                walletId = primaryWalletId,
                amount = 100_00L,
                walletPaymentMethodId,
                expires = false,
            )
        }

        withGivenDateTime(getZonedDateTime().plusDays(1).withHour(12)) {
            scheduledBillPaymentService.process(primaryWallet.id, getZonedDateTime().toLocalDate())
        }

        verify(exactly = 1) { billPaymentServiceMock.execute(any()) }

        val result = billEventRepository.getBillById(BillId(BILL_ID_3))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.schedule shouldNotBe null
            bill.shouldNotBePostponed()
            bill.paymentShouldBeStarted()
        }
    }

    @Test
    fun `deve iniciar o pagamento e consultar o saldo quando a unica conta agendada expira e inclui assinaturas`() {
        configureDailyLimits(100_00L, 400L)

        withGivenDateTime(getZonedDateTime().withHour(12)) {
            createPixScheduledBill(
                billId = BILL_ID_3,
                walletId = primaryWalletId,
                amount = 100_00L,
                walletPaymentMethodId,
                expires = false,
            )
        }

        withGivenDateTime(getZonedDateTime().withHour(12)) {
            scheduledBillPaymentService.process(
                walletId = primaryWallet.id,
                scheduledDate = getZonedDateTime().toLocalDate(),
                includeSubscription = true,
            )
        }

        verify(exactly = 1) { billPaymentServiceMock.execute(any()) }
        verify(exactly = 1) { bankAccountServiceMock.getBalance(any()) }

        val result = billEventRepository.getBillById(BillId(BILL_ID_3))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.schedule shouldNotBe null
            bill.shouldNotBePostponed()
            bill.paymentShouldBeStarted()
        }
    }

    @Test
    fun `nao deve iniciar o pagamento nem consukltar o saldo quando a unica conta agendada expira e nao inclui assinaturas`() {
        configureDailyLimits(100_00L, 400L)

        withGivenDateTime(getZonedDateTime().withHour(12)) {
            createPixScheduledBill(
                billId = BILL_ID_3,
                walletId = primaryWalletId,
                amount = 100_00L,
                walletPaymentMethodId,
                expires = false,
            )
        }

        withGivenDateTime(getZonedDateTime().withHour(12)) {
            scheduledBillPaymentService.process(
                walletId = primaryWallet.id,
                scheduledDate = getZonedDateTime().toLocalDate(),
                includeSubscription = false,
            )
        }

        verify(exactly = 0) { billPaymentServiceMock.execute(any()) }
        verify(exactly = 0) { bankAccountServiceMock.getBalance(any()) }

        val result = billEventRepository.getBillById(BillId(BILL_ID_3))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.schedule shouldNotBe null
            bill.paymentShouldNotBeStarted()
        }
    }

    @Test
    fun `should unschedule bill when amount is higher than nightime limit and lower than daily limit`() {
        configureDailyLimits(100_00L, 4_00L)

        withGivenDateTime(getZonedDateTime().withHour(23)) {
            createPixScheduledBill(
                billId = BILL_ID_3,
                walletId = primaryWalletId,
                amount = 4_01L,
                walletPaymentMethodId,
            )
            scheduledBillPaymentService.process(primaryWallet.id, getZonedDateTime().toLocalDate())
        }

        verify(exactly = 0) { billPaymentServiceMock.execute(any()) }

        val result = billEventRepository.getBillById(BillId(BILL_ID_3))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldBeUnscheduled()
        }
    }

    @Test
    fun `should allow bill to be paid when there are daily and nighttime limits`() {
        configureDailyLimits(10000L, 900L)

        withGivenDateTime(getZonedDateTime().withHour(21)) {
            createPaidPix(client = enhancedClient, walletId = primaryWalletId, amount = 100)
        }

        withGivenDateTime(getZonedDateTime().withHour(22)) {
            createPixScheduledBill(
                billId = BILL_ID_3,
                walletId = primaryWalletId,
                amount = 500,
                walletPaymentMethodId,
            )
            createPixScheduledBill(
                billId = BILL_ID_4,
                walletId = primaryWalletId,
                amount = 300,
                walletPaymentMethodId,
            )

            scheduledBillPaymentService.process(primaryWallet.id, getZonedDateTime().toLocalDate())
        }

        verify(exactly = 2) { billPaymentServiceMock.execute(any()) }

        var result = billEventRepository.getBillById(BillId(BILL_ID_3))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldNotBePostponed()
        }

        result = billEventRepository.getBillById(BillId(BILL_ID_4))
        result.isRight() shouldBe true
        result.map { bill ->
            bill.shouldNotBePostponed()
        }
    }

    private fun createInvoiceScheduledBill(billId: String, walletId: WalletId, amount: Long) {
        val billInvoiceAdded = invoiceAdded.copy(
            billId = BillId(billId),
            walletId = walletId,
            amount = amount,
            amountTotal = amount,
            created = getZonedDateTime().toInstant().toEpochMilli() - 10,
        )
        val billInvoicePaymentScheduled = invoicePaymentScheduled.copy(
            billId = BillId(billId),
            walletId = walletId,
            amount = amount,
            created = getZonedDateTime().toInstant().toEpochMilli() - 5,
        )

        billEventRepository.save(billInvoiceAdded)
        billEventRepository.save(billInvoicePaymentScheduled)

        billRepository.save(Bill.build(billInvoiceAdded, billInvoicePaymentScheduled))

        scheduledBillRepository.save(
            scheduledBill.copy(
                billId = BillId(billId),
                walletId = walletId,
                scheduledDate = getLocalDate(),
                amount = amount,
                billType = BillType.INVOICE,
                paymentMethodsDetail = createPaymentMethodsDetailWithBalance(
                    amount = amount,
                    paymentMethodId = paymentMethodId,
                ),
            ),
        )
    }

    private fun createPixScheduledBill(
        billId: String,
        walletId: WalletId,
        amount: Long,
        paymentMethodId: AccountPaymentMethodId,
        expires: Boolean = true,
        isSelfTransfer: Boolean = false,
    ) {
        val billPixAdded = pixAdded.copy(
            billId = BillId(billId),
            walletId = walletId,
            amount = amount,
            amountTotal = amount,
            created = getZonedDateTime().toInstant().toEpochMilli() - 10,
        )
        val billPixPaymentScheduled = pixPaymentScheduled.copy(
            billId = BillId(billId),
            walletId = walletId,
            amount = amount,
            created = getZonedDateTime().toInstant().toEpochMilli() - 5,
            infoData = BillPaymentScheduledBalanceInfo(
                amount = amount,
                paymentMethodId = paymentMethodId.value,
                calculationId = null,
            ),
        )

        billEventRepository.save(billPixAdded)
        billEventRepository.save(billPixPaymentScheduled)

        billRepository.save(Bill.build(billPixAdded, billPixPaymentScheduled))

        scheduledBillRepository.save(
            scheduledBill.copy(
                billId = BillId(billId),
                walletId = walletId,
                scheduledDate = getLocalDate(),
                amount = amount,
                billType = BillType.PIX,
                expires = expires,
                paymentMethodsDetail = createPaymentMethodsDetailWithBalance(
                    amount = amount,
                    paymentMethodId = paymentMethodId,
                ),
                isSelfTransfer = isSelfTransfer,
            ),
        )
    }

    private fun createFichaCompensacaoScheduledBill(
        billId: String,
        walletId: WalletId,
        amount: Long,
        paymentMethodId: AccountPaymentMethodId,
    ) {
        val billFichaCompensacaoAdded = billAdded.copy(
            billId = BillId(billId),
            walletId = walletId,
            barcode = BarCode.of(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
            amount = amount,
            amountTotal = amount,
            created = getZonedDateTime().toInstant().toEpochMilli() - 10,
        )
        val billFichaCompensacaoPaymentScheduled = billPaymentScheduled.copy(
            billId = BillId(billId),
            walletId = walletId,
            created = getZonedDateTime().toInstant().toEpochMilli() - 5,
            amount = amount,
            infoData = BillPaymentScheduledBalanceInfo(
                amount = amount,
                paymentMethodId = paymentMethodId.value,
                calculationId = null,
            ),
        )

        billEventRepository.save(billFichaCompensacaoAdded)
        billEventRepository.save(billFichaCompensacaoPaymentScheduled)

        billRepository.save(Bill.build(billFichaCompensacaoAdded, billFichaCompensacaoPaymentScheduled))

        scheduledBillRepository.save(
            scheduledBill.copy(
                billId = BillId(billId),
                walletId = walletId,
                scheduledDate = getLocalDate(),
                amount = amount,
                billType = BillType.FICHA_COMPENSACAO,
                paymentMethodsDetail = createPaymentMethodsDetailWithBalance(
                    amount = amount,
                    paymentMethodId = paymentMethodId,
                ),
            ),
        )
    }

    private fun createConcessionariaScheduledBill(
        billId: String,
        walletId: WalletId,
        amount: Long,
        paymentMethodId: AccountPaymentMethodId,
    ) {
        val billConcessionariaAdded = billAdded.copy(
            billId = BillId(billId),
            walletId = walletId,
            amount = amount,
            created = getZonedDateTime().toInstant().toEpochMilli() - 10,
        )
        val billConcessionariaPaymentScheduled = billPaymentScheduled.copy(
            billId = BillId(billId),
            walletId = walletId,
            amount = amount,
            created = getZonedDateTime().toInstant().toEpochMilli() - 5,
            infoData = BillPaymentScheduledBalanceInfo(
                amount = amount,
                paymentMethodId = paymentMethodId.value,
                calculationId = null,
            ),
        )

        billEventRepository.save(billConcessionariaAdded)
        billEventRepository.save(billConcessionariaPaymentScheduled)

        billRepository.save(Bill.build(billConcessionariaAdded, billConcessionariaPaymentScheduled))

        scheduledBillRepository.save(
            scheduledBill.copy(
                billId = BillId(billId),
                walletId = walletId,
                scheduledDate = getLocalDate(),
                amount = amount,
                paymentMethodsDetail = createPaymentMethodsDetailWithBalance(
                    amount = amount,
                    paymentMethodId = paymentMethodId,
                ),
            ),
        )
    }

    private fun configureDailyLimits(dailyLimit: Long, nighttimeLimit: Long, monthlyLimit: Long? = null) {
        withGivenDateTime(getZonedDateTime().minusDays(3)) {
            walletLimitsService.updateLimit(
                accountId = accountId,
                walletId = primaryWalletId,
                type = DailyPaymentLimitType.DAILY,
                amount = dailyLimit,
                source = ActionSource.Api(accountId = accountId),
            )
            walletLimitsService.updateLimit(
                accountId = accountId,
                walletId = primaryWalletId,
                type = DailyPaymentLimitType.NIGHTTIME,
                amount = nighttimeLimit,
                source = ActionSource.Api(accountId = accountId),
            )
            monthlyLimit?.let {
                walletLimitsService.updateLimit(
                    accountId = accountId,
                    walletId = primaryWalletId,
                    type = DailyPaymentLimitType.MONTHLY,
                    amount = it,
                    source = ActionSource.Api(accountId = accountId),
                )
            }
        }
    }

    private companion object Setup {
        private val enhancedClient: DynamoDbEnhancedClient = getDynamoDB()
        private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
        private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
        private val scheduledBillRepository = ScheduledBillDBRepository(ScheduledBillDynamoDAO(enhancedClient))
        private val scheduledWalletRepository = mockk<ScheduledWalletDBRepository>()

        private val billPaymentServiceMock: BillPaymentService = mockk()
        private val bankAccountServiceMock: BankAccountService = mockk()
        private val paymentSchedulingService =
            spyk(PaymentSchedulingService(scheduledBillRepository, scheduledWalletRepository))
        private val notificationAdapter: NotificationAdapter = mockk(relaxUnitFun = true)
        private val billEventDAO = BillEventDynamoDAO(enhancedClient)
        private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(enhancedClient)
        private val transactionDynamo = TransactionDynamo(enhancedClient)
        private val billEventRepository = BillEventDBRepository(
            billEventDAO,
            uniqueConstraintDAO,
            allFalseFeatureConfiguration,
            transactionDynamo,
        )
        private val billRepository = DynamoDbBillRepository(
            billClient = BillDynamoDAO(enhancedClient),
            refundedClient = RefundedBillDynamoDAO(enhancedClient),
            settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(enhancedClient),
        )

        private val accountDAO = AccountDynamoDAO(enhancedClient)
        private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
        private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
        private val nsuDAO = NSUDynamoDAO(enhancedClient)

        private val accountRepository = AccountDbRepository(
            accountDAO = accountDAO,
            partialAccountDAO = partialAccountDAO,
            paymentMethodDAO = paymentMethodDAO,
            nsuDAO = nsuDAO,
            transactionDynamo = transactionDynamo,
        )
        private val lockProvider: InternalLock = mockk(relaxed = true)

        private val walletDAO = WalletDynamoDAO(enhancedClient)
        private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
        private val inviteDAO = InviteDynamoDAO(enhancedClient)
        private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
        private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

        private val walletRepository = WalletDbRepository(
            walletDAO = walletDAO,
            walletMemberDAO = walletMemberDAO,
            inviteDAO = inviteDAO,
            inviteReminderDAO = inviteReminderDAO,
            walletLimitDAO = walletLimitDAO,
            accountRepository = mockk(),
        )

        private val updateBillService = UpdateBillService(
            billEventRepository = billEventRepository,
            billRepository = billRepository,
            billTrackingRepository = mockk(relaxed = true),
            eventPublisher = mockk(relaxed = true),
            boletoSettlementService = mockk(),
            billValidationService = mockk(relaxed = true),
            lockProvider = mockk(),
            walletLimitsService = mockk(),
            walletService = mockk(),
            approveBillService = mockk(),
            denyBillService = mockk(),
            resolveScheduledBill = mockk(),
            cancelSchedulePaymentService = mockk {
                every { cancelScheduledPayment(any(), any(), any()) } returns true
                every { userCancelScheduledPayment(any(), any(), any(), any()) } returns true
            },
            mailboxWalletDataRepository = mockk(),
        ).apply {
            this.tedLimitTime = "17:00"
        }
        private val internalBankRepository = InternalBankDBRepository(InternalBankDynamoDAO(enhancedClient))
        private val accountService = AccountService(
            accountConfigurationService = mockk(),
            accountRepository = accountRepository,
            chatbotMessagePublisher = mockk(),
            crmService = mockk(),
            notificationAdapter = mockk(),
            walletRepository = mockk(),
        )
        private val balanceService = DefaultBalanceService(
            accountService,
            internalBankRepository,
            bankAccountServiceMock,
            omnibusBankAccountConfiguration,
            walletService = mockk(),
        )
        private val walletService = WalletService(
            accountService = accountService,
            walletRepository = walletRepository,
            configuration = WalletConfigurationMicronaut(
                maxOpenInvites = 1,
                inviteExpiration = Duration.ZERO,
            ),
            notificationAdapter = notificationAdapter,
            eventPublisher = mockk(relaxed = true),
            crmService = mockk(),
            pixKeyRepository = mockk(),
        )

        private val walletLimitsService =
            WalletLimitsService(
                billRepository = billRepository,
                walletRepository = walletRepository,
                accountService = accountService,
                paymentSchedulingService = mockk(),
                internalLock = lockProvider,
            )

        private val accountId: AccountId = AccountId(ACCOUNT_ID)

        private val founder = Member(
            accountId,
            document = DOCUMENT,
            name = "Membro Fundador",
            emailAddress = EmailAddress(EMAIL),
            type = MemberType.FOUNDER,
            status = MemberStatus.ACTIVE,
            permissions = MemberPermissions(
                viewBills = BillPermission.ALL_BILLS,
                scheduleBills = BillPermission.ALL_BILLS,
                viewBalance = true,
                notification = true,
            ),
            created = getZonedDateTime(),
            updated = getZonedDateTime(),
        )

        private val primaryWalletId = WalletId(WALLET_ID)

        private val primaryWallet = Wallet(
            primaryWalletId,
            "carteira principal",
            listOf(founder),
            10,
            WalletStatus.ACTIVE,
            WalletType.PRIMARY,
            paymentMethodId,
        )

        private val scheduledBillPaymentService = DefaultScheduledBillPaymentService(
            billPaymentService = billPaymentServiceMock,
            balanceService = balanceService,
            paymentSchedulingService = paymentSchedulingService,
            updateBillService = updateBillService,
            walletService = walletService,
            walletLimitsService = walletLimitsService,
            lockProvider = lockProvider,
            walletLockProvider = mockk(relaxed = true),
            scheduledBillPaymentServicePreProcessor = mockk(relaxed = true),
        )
        private const val walletLimit: Long = 999

        private fun Bill.shouldNotBePostponed() {
            history.filterIsInstance<BillSchedulePostponed>() shouldHaveSize 0
            history.last() shouldNot beOfType(BillSchedulePostponed::class)
        }

        private fun Bill.shouldBeUnscheduled() {
            history.filterIsInstance<BillPaymentScheduleCanceled>() shouldHaveSize 1
            history.last() shouldBe beOfType(BillPaymentScheduleCanceled::class)
        }

        private fun Bill.paymentShouldBeStarted() {
            history.filterIsInstance<BillPaymentScheduleStarted>() shouldHaveSize 1
            history.last() shouldBe beOfType(BillPaymentScheduleStarted::class)
        }

        private fun Bill.paymentShouldNotBeStarted() {
            history.filterIsInstance<BillPaymentScheduleStarted>() shouldHaveSize 0
        }
    }
}