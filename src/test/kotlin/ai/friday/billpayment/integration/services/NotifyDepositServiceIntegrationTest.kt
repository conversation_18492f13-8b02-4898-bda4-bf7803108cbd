package ai.friday.billpayment.integration.services

import DynamoDBUtils
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.InternalBankDBRepository
import ai.friday.billpayment.adapters.dynamodb.InternalBankDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.ScheduledBillDBRepository
import ai.friday.billpayment.adapters.dynamodb.ScheduledBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.balance.DefaultBalanceService
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.integrations.BankAccountService
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.NotifyDepositService
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.DailyPaymentLimit
import ai.friday.billpayment.app.wallet.DailyPaymentLimitType
import ai.friday.billpayment.app.wallet.WalletPaymentLimits
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.BILL_ID_3
import ai.friday.billpayment.integration.CONCESSIONARIA_DIGITABLE_LINE
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadBalancePaymentMethod
import ai.friday.billpayment.omnibusBankAccountConfiguration
import ai.friday.billpayment.paymentMethodId2
import ai.friday.billpayment.scheduledBill
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import arrow.core.right
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class NotifyDepositServiceIntegrationTest {

    private val bankAccountServiceMock: BankAccountService = mockk()
    private val notificationAdapter: NotificationAdapter = mockk(relaxUnitFun = true)

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoClient = DynamoDBUtils.setupDynamoDB()

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val scheduledBillRepository = ScheduledBillDBRepository(ScheduledBillDynamoDAO(dynamoClient))
    private val billEventDAO = BillEventDynamoDAO(dynamoClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(dynamoClient)
    private val transactionDynamo = TransactionDynamo(dynamoClient)
    private val billEventRepository = BillEventDBRepository(
        billEventDAO,
        uniqueConstraintDAO,
        allFalseFeatureConfiguration,
        transactionDynamo,
    )
    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(dynamoClient),
        refundedClient = RefundedBillDynamoDAO(dynamoClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(dynamoClient),
    )

    private val accountDAO = AccountDynamoDAO(dynamoClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(dynamoClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(dynamoClient)
    private val nsuDAO = NSUDynamoDAO(dynamoClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )
    private val lockProvider: InternalLock = mockk(relaxed = true)

    private val internalBankRepository = InternalBankDBRepository(InternalBankDynamoDAO(dynamoClient))
    val accountService = AccountService(
        accountConfigurationService = mockk(),
        accountRepository = accountRepository,
        chatbotMessagePublisher = mockk(),
        crmService = mockk(),
        notificationAdapter = mockk(),
        walletRepository = mockk(),
    )

    val balanceService = DefaultBalanceService(
        accountService,
        internalBankRepository,
        bankAccountServiceMock,
        omnibusBankAccountConfiguration,
        walletService = mockk(),
    )

    private val walletRepository: WalletRepository = mockk()
    private val walletService: WalletService = mockk()

    private val walletLimitsService =
        spyk(
            WalletLimitsService(
                walletRepository = walletRepository,
                paymentSchedulingService = mockk(),
                billRepository = billRepository,
                accountService = mockk(),
                internalLock = lockProvider,
            ),
        )

    private val walletFixture = WalletFixture()

    private val wallet = walletFixture.buildWallet(accountPaymentMethodId = paymentMethodId2)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        loadBalancePaymentMethod(
            accountRepository,
            accountId = wallet.founder.accountId.value,
            paymentMethodId = wallet.paymentMethodId.value,
        )
        billEventRepository.save(billAdded)
        billEventRepository.save(billPaymentScheduled)
        billEventRepository.save(
            billAdded.copy(
                billId = BillId(BILL_ID_2),
                barcode = BarCode.of(CONCESSIONARIA_DIGITABLE_LINE),
            ),
        )
        billEventRepository.save(billPaymentScheduled.copy(billId = BillId(BILL_ID_2)))
        billEventRepository.save(
            billAdded.copy(
                billId = BillId(BILL_ID_3),
                barcode = BarCode.of(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
            ),
        )
        billEventRepository.save(billPaymentScheduled.copy(billId = BillId(BILL_ID_3)))

        every { bankAccountServiceMock.getBalance(any()) } returns 15591L

        every { walletService.findWallet(any()) } returns wallet
        every { walletRepository.findWallet(any()) } returns wallet

        every { walletLimitsService.findWalletPaymentLimits(any()) } returns WalletPaymentLimits(
            daily = DailyPaymentLimit(amount = 100_000_00L, lastAmount = null, updatedAt = getZonedDateTime(), type = DailyPaymentLimitType.DAILY),
            nighttime = DailyPaymentLimit(amount = 1_000_00L, lastAmount = null, updatedAt = getZonedDateTime(), type = DailyPaymentLimitType.NIGHTTIME),
            monthly = null,
            whatsAppPayment = DailyPaymentLimit(amount = 1_000_00L, lastAmount = null, updatedAt = getZonedDateTime(), type = DailyPaymentLimitType.WHATSAPP_PAYMENT),
            automaticPix = DailyPaymentLimit(amount = 100_000_00L, lastAmount = null, updatedAt = getZonedDateTime(), type = DailyPaymentLimitType.AUTOMATIC_PIX),
        ).right()
    }

    private val scheduledBillPaymentService = NotifyDepositService(
        scheduledBillRepository = scheduledBillRepository,
        balanceService = balanceService,
        notificationAdapter = notificationAdapter,
        walletService = walletService,
    )

    private val walletId = wallet.id
    private val amount = 100L
    private val pendingAmount = 100L
    private val senderName = "Fulano"
    private val senderDocument = DOCUMENT
    private val senderAccountNo = "0000-1"
    private val senderBankName = "Bank"

    @Test
    fun `should notify cash in when there is no bill scheduled`() {
        scheduledBillPaymentService.notifyDeposit(walletId, paymentMethodId2, amount, senderName, senderDocument, senderAccountNo, senderBankName, BankStatementItemType.PIX)
        verify {
            notificationAdapter.notifyCashIn(
                members = any(),
                walletId = wallet.id,
                walletName = any(),
                senderName = any(),
                senderDocument = any(),
                amount = amount,
            )
        }
    }

    @Test
    fun `should notify cash in when there is only bill scheduled for the future`() {
        scheduledBillRepository.save(
            scheduledBill.copy(
                scheduledDate = getLocalDate().plusDays(2),
            ),
        )
        scheduledBillPaymentService.notifyDeposit(walletId, paymentMethodId2, amount, senderName, senderDocument, senderAccountNo, senderBankName, BankStatementItemType.PIX)
        verify {
            notificationAdapter.notifyCashIn(
                members = any(),
                walletId = wallet.id,
                walletName = any(),
                senderName = any(),
                senderDocument = any(),
                amount = amount,
            )
        }
    }

    @Test
    fun `should notify cash in when there is a bill scheduled for today and have enough balance`() {
        val currentDateMock = LocalDate.of(2020, 10, 29)
        scheduledBillRepository.save(
            scheduledBill.copy(
                walletId = wallet.id,
                scheduledDate = currentDateMock,
            ),
        )

        withGivenDateTime(ZonedDateTime.of(currentDateMock, LocalTime.NOON, brazilTimeZone)) {
            scheduledBillPaymentService.notifyDeposit(walletId, paymentMethodId2, amount, senderName, senderDocument, senderAccountNo, senderBankName, BankStatementItemType.PIX)
        }
        verify {
            notificationAdapter.notifyCashInPaymentSufficientBalanceToday(
                members = any(),
                walletId = wallet.id,
                walletName = any(),
                senderName = any(),
                senderDocument = any(),
                amount = amount,
            )
        }
    }

    @Test
    fun `should notify cash in when there is a bill scheduled for today, have enough balance and deposit is after hour`() {
        val currentDateMock = LocalDate.of(2020, 10, 29)
        scheduledBillRepository.save(
            scheduledBill.copy(
                walletId = wallet.id,
                scheduledDate = currentDateMock,
            ),
        )

        withGivenDateTime(ZonedDateTime.of(currentDateMock, LocalTime.of(23, 50), brazilTimeZone)) {
            scheduledBillPaymentService.notifyDeposit(walletId, paymentMethodId2, amount, senderName, senderDocument, senderAccountNo, senderBankName, BankStatementItemType.PIX)
        }

        verify {
            notificationAdapter.notifyCashInPaymentSufficientBalanceToday(
                members = any(),
                walletId = wallet.id,
                walletName = any(),
                senderName = any(),
                senderDocument = any(),
                amount = amount,
            )
        }
    }

    @Test
    fun `should notify cash in when there is a bill scheduled for today and have not enough balance`() {
        every { bankAccountServiceMock.getBalance(any()) } returns scheduledBill.amount - pendingAmount
        val currentDateMock = LocalDate.of(2020, 10, 29)
        scheduledBillRepository.save(
            scheduledBill.copy(
                walletId = wallet.id,
                scheduledDate = currentDateMock,
            ),
        )

        withGivenDateTime(ZonedDateTime.of(currentDateMock, LocalTime.NOON, brazilTimeZone)) {
            scheduledBillPaymentService.notifyDeposit(walletId, paymentMethodId2, amount, senderName, senderDocument, senderAccountNo, senderBankName, BankStatementItemType.PIX)
        }

        verify {
            notificationAdapter.notifyCashInPaymentInsufficientBalanceToday(
                members = any(),
                walletId = wallet.id,
                walletName = any(),
                pendingAmountToday = pendingAmount,
                senderName = any(),
                senderDocument = any(),
                amount = amount,
            )
        }
    }

    @Test
    fun `should notify cash in when there is a bill scheduled for today, don't have enough balance and deposit is after hour`() {
        val currentDateMock = LocalDate.of(2020, 10, 29)
        every { bankAccountServiceMock.getBalance(any()) } returns scheduledBill.amount - pendingAmount
        scheduledBillRepository.save(
            scheduledBill.copy(
                walletId = wallet.id,
                scheduledDate = currentDateMock,
            ),
        )
        scheduledBillRepository.save(
            scheduledBill.copy(
                walletId = wallet.id,
                billId = BillId(BILL_ID_2),
                scheduledDate = currentDateMock.plusDays(1),
            ),
        )

        withGivenDateTime(ZonedDateTime.of(currentDateMock, LocalTime.of(23, 50), brazilTimeZone)) {
            scheduledBillPaymentService.notifyDeposit(walletId, paymentMethodId2, amount, senderName, senderDocument, senderAccountNo, senderBankName, BankStatementItemType.PIX)
        }
        verify {
            notificationAdapter.notifyCashInPaymentInsufficientBalanceToday(
                members = any(),
                walletId = wallet.id,
                walletName = any(),
                pendingAmountToday = pendingAmount,
                senderName = any(),
                senderDocument = any(),
                amount = amount,
            )
        }
    }
}