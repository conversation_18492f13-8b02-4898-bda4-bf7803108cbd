package ai.friday.billpayment.integration

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.RECIPIENT_ID
import ai.friday.billpayment.adapters.api.BillTO
import ai.friday.billpayment.adapters.api.CreatePixTO
import ai.friday.billpayment.adapters.api.PixKeyRequestTO
import ai.friday.billpayment.adapters.api.RecipientBankDetailsTO
import ai.friday.billpayment.adapters.api.RecurrenceRequestTO
import ai.friday.billpayment.adapters.api.RequestPixRecipientTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.ContactDbRepository
import ai.friday.billpayment.adapters.dynamodb.ContactDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.ScheduledBillDBRepository
import ai.friday.billpayment.adapters.dynamodb.ScheduledBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.CreateBillError
import ai.friday.billpayment.app.bill.PixCopyAndPaste
import ai.friday.billpayment.app.bill.PixQrCodeData
import ai.friday.billpayment.app.bill.PixQrCodeType
import ai.friday.billpayment.app.bill.WarningCode
import ai.friday.billpayment.app.contact.BankAccountId
import ai.friday.billpayment.app.contact.InvalidationCode
import ai.friday.billpayment.app.contact.LastUsed
import ai.friday.billpayment.app.contact.SavedPixKey
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.PixQRCodeParserService
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyDetailsResult
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixKeyException
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixKeyOwner
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.PixQRCodeDetailsResult
import ai.friday.billpayment.app.recurrence.RecurrenceFrequency
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.DailyPaymentLimitType
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletStatus
import ai.friday.billpayment.app.wallet.WalletType
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.createSavedRecipient
import ai.friday.billpayment.paymentMethodId
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.pixPaymentScheduled
import ai.friday.billpayment.scheduledBill
import ai.friday.billpayment.sundayDate
import ai.friday.billpayment.verify
import ai.friday.billpayment.withPixParticipants
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import arrow.core.getOrElse
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.date.shouldHaveSameDayAs
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.string.shouldContain
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus.BAD_REQUEST
import io.micronaut.http.HttpStatus.CREATED
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.math.BigInteger
import java.time.LocalDate
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.junit.JUnitAsserter.fail
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource

@MicronautTest(environments = [FRIDAY_ENV])
class AddPixIntegrationTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {

    @MockBean(PixKeyManagement::class)
    fun pixKeyManagementFun(): PixKeyManagement = pixKeyManagement

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @MockBean(PixQRCodeParserService::class)
    fun pixQRCodeParserService(): PixQRCodeParserService = pixQRCodeParserService
    private val pixQRCodeParserService: PixQRCodeParserService = mockk()

    private val pixKeyManagement: PixKeyManagement = mockk()

    private val dynamoClient = getDynamoDB()

    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(dynamoClient),
        refundedClient = RefundedBillDynamoDAO(dynamoClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(dynamoClient),
    )
    private val billEventDAO = BillEventDynamoDAO(dynamoClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(dynamoClient)
    private val transactionDynamo = TransactionDynamo(dynamoClient)
    private val billEventRepository = BillEventDBRepository(
        billEventDAO,
        uniqueConstraintDAO,
        allFalseFeatureConfiguration,
        transactionDynamo,
    )
    private val walletDAO = WalletDynamoDAO(dynamoClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(dynamoClient)
    private val inviteDAO = InviteDynamoDAO(dynamoClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(dynamoClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(dynamoClient)

    private val walletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = mockk(),
    )

    private val recipientDbRepository = ContactDbRepository(ContactDynamoDAO(dynamoClient))

    private val scheduledBillRepository = ScheduledBillDBRepository(client = ScheduledBillDynamoDAO(cli = dynamoClient))

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val lockProvider: InternalLock = mockk(relaxed = true)

    private val bankAccountRequestTO = CreatePixTO(
        amount = 10,
        description = "description",
        dueDate = sundayDate.format(dateFormat),
        recipient = RequestPixRecipientTO(
            name = "Ze",
            document = "**********2",
            documentType = "CPF",
            bankDetails = RecipientBankDetailsTO(
                accountType = AccountType.CHECKING,
                routingNo = 1,
                accountNo = BigInteger("*********"),
                accountDv = "2",
                ispb = "********",
            ),
            alias = "Ze",
        ),
    )

    private val pixKeyRequestTO = CreatePixTO(
        amount = 10,
        description = "description",
        dueDate = getZonedDateTime().plusDays(1).format(dateFormat),
        recipient = RequestPixRecipientTO(
            name = null,
            document = null,
            documentType = null,
            bankDetails = null,
            pixKey = PixKeyRequestTO(
                type = PixKeyType.CPF,
                value = "**********2",
            ),
            alias = "Ze",
        ),
    )

    private val qrCode = "QRCODE_PIX_VALIDO"
    private val qrCodePixId = "*********"

    private val pixQrCodeRequestTO = CreatePixTO(
        amount = 50,
        description = "description",
        dueDate = getZonedDateTime().plusDays(1).format(dateFormat),
        recipient = RequestPixRecipientTO(
            name = null,
            document = null,
            documentType = null,
            bankDetails = null,
            pixKey = null,
            alias = "Ze",
            qrCode = qrCode,
        ),
    )

    private val pixKeyDocument = "**********2"
    private val pixKeyDetails = PixKeyDetails(
        key = PixKey(value = pixKeyDocument, type = PixKeyType.CPF),
        holder = PixKeyHolder(
            accountNo = BigInteger("12345"),
            accountDv = "X",
            ispb = "********",
            institutionName = "banco fake",
            accountType = AccountType.CHECKING,
            routingNo = 1L,
        ),
        owner = PixKeyOwner("Ze", pixKeyDocument),
    )

    private val accountId: AccountId = AccountId(ACCOUNT_ID)

    private val founder = Member(
        accountId,
        document = DOCUMENT,
        name = "Membro Fundador",
        emailAddress = EmailAddress(EMAIL),
        type = MemberType.FOUNDER,
        status = MemberStatus.ACTIVE,
        permissions = MemberPermissions(
            viewBills = BillPermission.ALL_BILLS,
            scheduleBills = BillPermission.ALL_BILLS,
            viewBalance = true,
            notification = true,
        ),
        created = getZonedDateTime(),
        updated = getZonedDateTime(),
    )

    private val primaryWalletId = WalletId(WALLET_ID)

    private val primaryWallet = Wallet(
        primaryWalletId,
        "carteira principal",
        listOf(founder),
        10,
        WalletStatus.ACTIVE,
        WalletType.PRIMARY,
        paymentMethodId,
    )

    private val walletLimitsService = WalletLimitsService(
        billRepository = mockk(),
        accountService = mockk<AccountService>() {
            every {
                findAccountById(accountId)
            } returns mockk() {
                every {
                    type
                } returns UserAccountType.FULL_ACCOUNT
            }
        },
        walletRepository = walletRepository,
        paymentSchedulingService = mockk(),
        internalLock = lockProvider,
    )

    private val walletLimit: Long = 999

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        loadAccountIntoDb(dynamoDB)

        walletRepository.save(primaryWallet)
        configureDailyLimits(
            walletLimitsService = walletLimitsService,
            accountId = accountId,
            walletId = primaryWalletId,
            dailyLimit = walletLimit,
            nighttimeLimit = walletLimit,
        )
    }

    private fun buildRequest(request: Any, dryRun: Boolean) =
        HttpRequest.POST("/bill/pix?dryRun=$dryRun", request)
            .onWallet(primaryWallet)

    @ParameterizedTest
    @CsvSource(
        "-10,description,2020-12-25,**********2,CPF,1,********,2,********,Amount",
        "10,$LONG_DESCRIPTION,2020-12-25,**********2,CPF,1,********,2,********,Description",
        "10,$INVALID_SHORT_DESCRIPTION,2020-12-25,**********2,CPF,1,********,2,********,Description",
        "10,$INVALID_SHORT_DESCRIPTION2,2020-12-25,**********2,CPF,1,********,2,********,Description",
        "10,description,25/12/2020,**********2,CPF,1,********,2,********,Due date",
        "10,description,2020-12-25,**********22,CPF,1,********,2,********,document",
        "10,description,2020-12-25,**********22222,CNPJ,1,********,2,********,document",
        "10,description,2020-12-25,**********2,CNH,1,********,2,********,Document type",
        "10,description,2020-12-25,**********2,CPF,-1,********,2,********,Routing Number",
        "10,description,2020-12-25,**********2,CPF,1,-********,2,********,Account Number",
        "10,description,2020-12-25,**********2,CPF,1,********,112,********,Account check",
        "10,description,2020-12-25,**********2,CPF,1,********,2,*********,ispb",
        "10,description,2020-12-25,**********2,CPF,1,********,2,,ispb",
        "10,description,2020-12-25,**********2,CPF,1,********,2,********,ispb",
    )
    fun `should return bad request on invalid pix request`(
        amount: Long,
        description: String,
        dueDate: String,
        document: String,
        documentType: String,
        routingNo: Long,
        accountNo: BigInteger,
        accountDv: String,
        ispb: String?,
        expectedWrongField: String,
    ) {
        val to = CreatePixTO(
            amount = amount,
            description = description,
            dueDate = dueDate,
            recipient = RequestPixRecipientTO(
                name = "Ze",
                document = document,
                documentType = documentType,
                bankDetails = RecipientBankDetailsTO(
                    accountType = AccountType.CHECKING,
                    routingNo = routingNo,
                    accountNo = accountNo,
                    accountDv = accountDv,
                    ispb = if (ispb.isNullOrEmpty()) {
                        null
                    } else {
                        ispb
                    },
                ),
                alias = "Ze",
            ),
        )

        val response = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.exchange(buildRequest(to, false), Unit::class.java).firstOrError().blockingGet()
        }

        response.status shouldBe BAD_REQUEST
        response.message shouldContain expectedWrongField
    }

    @Test
    fun `should return 201 with warningCode UNSCHEDULED_DUE_AMOUNT_HIGHER_THAN_DAILY_LIMIT when dueDate today and unavailable limit`() {
        withPixParticipants(FinancialInstitution(name = "Banco do Brasil SA", compe = 1L, ispb = "********")) {
            val now = getZonedDateTime()
            val response = client.exchange(
                buildRequest(
                    bankAccountRequestTO.copy(
                        amount = walletLimit + 1,
                        dueDate = now.toLocalDate().format(dateFormat),
                    ),
                    true,
                ),
                BillTO::class.java,
            ).firstOrError()
                .blockingGet()

            response.status shouldBe CREATED

            with(response.body()!!) {
                warningCode shouldBe WarningCode.UNSCHEDULED_DUE_AMOUNT_HIGHER_THAN_DAILY_LIMIT.name
            }

            val getBillResponse = billEventRepository.getBillById(BillId(response.body()!!.id))
            getBillResponse.isLeft() shouldBe true
            getBillResponse.mapLeft { it.shouldBeTypeOf<ItemNotFoundException>() }
        }
    }

    @Test
    fun `should return 201 with warningCode POSTPONED_DUE_LIMIT_REACHED when dueDate today and unavailable limit`() {
        withPixParticipants(FinancialInstitution(name = "Banco do Brasil SA", compe = 1L, ispb = "********")) {
            createPaidPix(client = dynamoClient, walletId = primaryWalletId, amount = walletLimit)

            val now = getZonedDateTime()
            val response = client.exchange(
                buildRequest(
                    bankAccountRequestTO.copy(
                        amount = 1,
                        dueDate = now.toLocalDate().format(dateFormat),
                    ),
                    true,
                ),
                BillTO::class.java,
            ).firstOrError()
                .blockingGet()

            response.status shouldBe CREATED

            with(response.body()!!) {
                warningCode shouldBe WarningCode.POSTPONED_DUE_LIMIT_REACHED.name
            }

            val getBillResponse = billEventRepository.getBillById(BillId(response.body()!!.id))
            getBillResponse.isLeft() shouldBe true
            getBillResponse.mapLeft { it.shouldBeTypeOf<ItemNotFoundException>() }
        }
    }

    @Test
    fun `should return 201 with warningCode POSTPONED_DUE_MONTHLY_LIMIT_REACHED when dueDate today and unavailable limit`() {
        configureDailyLimits(
            walletLimitsService = walletLimitsService,
            accountId = accountId,
            walletId = primaryWalletId,
            dailyLimit = walletLimit * 2,
            nighttimeLimit = walletLimit * 2,
        )
        walletLimitsService.updateLimit(
            accountId = accountId,
            walletId = primaryWalletId,
            type = DailyPaymentLimitType.MONTHLY,
            amount = walletLimit,
            source = ActionSource.System,
        )

        withPixParticipants(FinancialInstitution(name = "Banco do Brasil SA", compe = 1L, ispb = "********")) {
            createPaidPix(client = dynamoClient, walletId = primaryWalletId, amount = walletLimit)

            val now = getZonedDateTime()
            val response = client.exchange(
                buildRequest(
                    bankAccountRequestTO.copy(
                        amount = 1,
                        dueDate = now.toLocalDate().format(dateFormat),
                    ),
                    true,
                ),
                BillTO::class.java,
            ).firstOrError()
                .blockingGet()

            response.status shouldBe CREATED

            with(response.body()!!) {
                warningCode shouldBe WarningCode.POSTPONED_DUE_MONTHLY_LIMIT_REACHED.name
            }

            val getBillResponse = billEventRepository.getBillById(BillId(response.body()!!.id))
            getBillResponse.isLeft() shouldBe true
            getBillResponse.mapLeft { it.shouldBeTypeOf<ItemNotFoundException>() }
        }
    }

    @Test
    fun `should return 201 quando não tiver limite para o mes atual mas o agendamento for para mes futuro`() {
        configureDailyLimits(
            walletLimitsService = walletLimitsService,
            accountId = accountId,
            walletId = primaryWalletId,
            dailyLimit = walletLimit * 2,
            nighttimeLimit = walletLimit * 2,
        )
        walletLimitsService.updateLimit(
            accountId = accountId,
            walletId = primaryWalletId,
            type = DailyPaymentLimitType.MONTHLY,
            amount = walletLimit,
            source = ActionSource.System,
        )

        withPixParticipants(FinancialInstitution(name = "Banco do Brasil SA", compe = 1L, ispb = "********")) {
            createScheduledPix(amount = walletLimit, scheduleDate = getZonedDateTime().withDayOfMonth(10).toLocalDate())

            val now = getZonedDateTime().plusMonths(1).withDayOfMonth(2)
            val response = client.exchange(
                buildRequest(
                    bankAccountRequestTO.copy(
                        amount = 1,
                        dueDate = now.toLocalDate().format(dateFormat),
                    ),
                    true,
                ),
                BillTO::class.java,
            ).firstOrError()
                .blockingGet()

            response.status shouldBe CREATED

            with(response.body()!!) {
                warningCode shouldBe WarningCode.NONE.name
            }

            val getBillResponse = billEventRepository.getBill(WalletId(WALLET_ID), BillId(response.body()!!.id))
            getBillResponse.isLeft() shouldBe true
            getBillResponse.mapLeft { it.shouldBeTypeOf<ItemNotFoundException>() }
        }
    }

    @Test
    fun `should return 201 with warningCode NONE when dueDate is to the future and amount is higher than daily limit`() {
        withPixParticipants(FinancialInstitution(name = "Banco do Brasil SA", compe = 1L, ispb = "********")) {
            val now = getZonedDateTime()
            val response = client.exchange(
                buildRequest(
                    bankAccountRequestTO.copy(
                        amount = walletLimit + 1,
                        dueDate = now.toLocalDate().plusDays(1).format(dateFormat),
                    ),
                    true,
                ),
                BillTO::class.java,
            ).firstOrError()
                .blockingGet()

            response.status shouldBe CREATED

            with(response.body()!!) {
                warningCode shouldBe WarningCode.UNSCHEDULED_DUE_AMOUNT_HIGHER_THAN_DAILY_LIMIT.name
            }

            val getBillResponse = billEventRepository.getBillById(BillId(response.body()!!.id))
            getBillResponse.isLeft() shouldBe true
            getBillResponse.mapLeft { it.shouldBeTypeOf<ItemNotFoundException>() }
        }
    }

    @Test
    fun `should return 201 with warningCode NONE when dueDate is today and exists a boleto scheduled`() {
        withPixParticipants(FinancialInstitution(name = "Banco do Brasil SA", compe = 1L, ispb = "********")) {
            val now = getZonedDateTime()

            val scheduleDate = now.toLocalDate()

            createScheduledBoleto(walletLimit, scheduleDate)

            val response = client.exchange(
                buildRequest(
                    bankAccountRequestTO.copy(
                        amount = 1,
                        dueDate = scheduleDate.format(dateFormat),
                    ),
                    true,
                ),
                BillTO::class.java,
            ).firstOrError()
                .blockingGet()

            response.status shouldBe CREATED

            with(response.body()!!) {
                warningCode shouldBe WarningCode.NONE.name
            }

            val getBillResponse = billEventRepository.getBillById(BillId(response.body()!!.id))
            getBillResponse.isLeft() shouldBe true
            getBillResponse.mapLeft { it.shouldBeTypeOf<ItemNotFoundException>() }
        }
    }

    @Test
    fun `should return 201 with warningCode POSTPONED_DUE_LIMIT_REACHED when dueDate is today and reached the daily limit`() {
        withPixParticipants(FinancialInstitution(name = "Banco do Brasil SA", compe = 1L, ispb = "********")) {
            val now = getZonedDateTime()

            val scheduleDate = now.toLocalDate()

            createScheduledPix(walletLimit, scheduleDate)

            val response = client.exchange(
                buildRequest(
                    bankAccountRequestTO.copy(
                        amount = 1,
                        dueDate = scheduleDate.format(dateFormat),
                    ),
                    true,
                ),
                BillTO::class.java,
            ).firstOrError()
                .blockingGet()

            response.status shouldBe CREATED

            with(response.body()!!) {
                warningCode shouldBe WarningCode.POSTPONED_DUE_LIMIT_REACHED.name
            }

            val getBillResponse = billEventRepository.getBillById(BillId(response.body()!!.id))
            getBillResponse.isLeft() shouldBe true
            getBillResponse.mapLeft { it.shouldBeTypeOf<ItemNotFoundException>() }
        }
    }

    @Test
    fun `should return 201 with warningCode POSTPONED_DUE_LIMIT_REACHED when dueDate is to the future and reached the daily limit`() {
        withPixParticipants(FinancialInstitution(name = "Banco do Brasil SA", compe = 1L, ispb = "********")) {
            val now = getZonedDateTime()

            val scheduleDate = now.toLocalDate().plusDays(1)

            createScheduledPix(walletLimit, scheduleDate)

            val response = client.exchange(
                buildRequest(
                    bankAccountRequestTO.copy(
                        amount = 1,
                        dueDate = scheduleDate.format(dateFormat),
                    ),
                    true,
                ),
                BillTO::class.java,
            ).firstOrError()
                .blockingGet()

            response.status shouldBe CREATED

            with(response.body()!!) {
                warningCode shouldBe WarningCode.POSTPONED_DUE_LIMIT_REACHED.name
            }

            val getBillResponse = billEventRepository.getBillById(BillId(response.body()!!.id))
            getBillResponse.isLeft() shouldBe true
            getBillResponse.mapLeft { it.shouldBeTypeOf<ItemNotFoundException>() }
        }
    }

    @Test
    fun `should return 201 on successful create pix with dry run`() {
        withPixParticipants(FinancialInstitution(name = "Banco do Brasil SA", compe = 1L, ispb = "********")) {
            val response = client.exchange(buildRequest(bankAccountRequestTO, true), BillTO::class.java).firstOrError()
                .blockingGet()

            response.status shouldBe CREATED
            validatePixByAccountResponse(response)
            val getBillResponse = billEventRepository.getBillById(BillId(response.body()!!.id))
            getBillResponse.isLeft() shouldBe true
            getBillResponse.mapLeft { it.shouldBeTypeOf<ItemNotFoundException>() }
        }
    }

    @Test
    fun `should return 201 on successful create pix without dry run`() {
        withPixParticipants(FinancialInstitution(name = "Banco do Brasil SA", compe = 1L, ispb = "********")) {
            val response = client.exchange(buildRequest(bankAccountRequestTO, false), BillTO::class.java).firstOrError()
                .blockingGet()

            response.status shouldBe CREATED
            validatePixByAccountResponse(response)
            val billId = BillId(response.body()!!.id)

            with(billRepository.findBill(BillId(response.body()!!.id), primaryWalletId)) {
                this.recipient!!.bankAccount!!.ispb shouldBe bankAccountRequestTO.recipient.bankDetails!!.ispb
            }

            val recipient = recipientDbRepository.findRecipientByAccountIDAndDocument(
                AccountId(ACCOUNT_ID),
                bankAccountRequestTO.recipient.document!!,
            ).getOrElse { fail("should find recipient") }
            recipient.bankAccounts.size shouldNotBe 0
            recipient.bankAccounts[0].ispb shouldBe bankAccountRequestTO.recipient.bankDetails!!.ispb
            with(billRepository.findByContactId(recipient.id)) {
                size shouldBe 1
                first().walletId shouldBe primaryWalletId
                first().billId shouldBe billId
            }
        }
    }

    @Test
    fun `should return bad request on neither pix key nor bank account present`() {
        val response = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.exchange(
                buildRequest(pixKeyRequestTO.copy(recipient = pixKeyRequestTO.recipient.copy(pixKey = null)), false),
                Argument.of(BillTO::class.java),
                Argument.of(ResponseTO::class.java),
            ).firstOrError().blockingGet()
        }

        response.status shouldBe BAD_REQUEST
        with(response.response.getBody(ResponseTO::class.java).get()) {
            this.code shouldBe "4201" // FIXME
        }
    }

    @Test
    fun `should return bad request on pix key and bank account present`() {
        val response = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.exchange(
                buildRequest(
                    bankAccountRequestTO.copy(
                        recipient = bankAccountRequestTO.recipient.copy(
                            pixKey = pixKeyRequestTO.recipient.pixKey,
                        ),
                    ),
                    false,
                ),
                Argument.of(BillTO::class.java),
                Argument.of(ResponseTO::class.java),
            ).firstOrError().blockingGet()
        }

        response.status shouldBe BAD_REQUEST
        with(response.response.getBody(ResponseTO::class.java).get()) {
            this.code shouldBe "4201" // FIXME
        }
    }

    @ParameterizedTest
    @CsvSource(
        "CNPJ,***********",
        "PHONE,*************",
        "EMAIL,teste@@com",
        "EVP,XXXXX-XXXXX-XXXX",
        "CPF,1339759XXXX",
        "CPF,********000199",
    )
    fun `should return bad request on invalid pix key format`(type: String, key: String) {
        val response = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.exchange(
                buildRequest(
                    pixKeyRequestTO.copy(
                        recipient = pixKeyRequestTO.recipient.copy(
                            pixKey = PixKeyRequestTO(
                                key,
                                PixKeyType.valueOf(type),
                            ),
                        ),
                    ),
                    false,
                ),
                Argument.of(BillTO::class.java),
                Argument.of(ResponseTO::class.java),
            ).firstOrError().blockingGet()
        }

        response.status shouldBe BAD_REQUEST
        with(response.response.getBody(ResponseTO::class.java).get()) {
            this.code shouldBe "4202" // FIXME
            this.message shouldContain type
        }
    }

    @MethodSource("failPix")
    @ParameterizedTest
    fun `should return bad request on pix key error and saved recipient does not exists`(
        keyError: PixKeyError,
        expectedCode: String,
    ) {
        every {
            pixKeyManagement.findKeyDetailsCacheable(any(), any())
        } throws PixKeyException(keyError)

        val response = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.exchange(
                buildRequest(
                    pixKeyRequestTO.copy(
                        recipient = pixKeyRequestTO.recipient.copy(
                            id = RECIPIENT_ID,
                        ),
                    ),
                    false,
                ),
                Argument.of(BillTO::class.java),
                Argument.of(ResponseTO::class.java),
            ).firstOrError().blockingGet()
        }

        response.status shouldBe BAD_REQUEST
        with(response.response.getBody(ResponseTO::class.java).get()) {
            this.code shouldBe expectedCode
        }
    }

    @MethodSource("failPix")
    @ParameterizedTest
    fun `should return bad request and invalidate recipient key on pix key error and saved recipient exists`(
        keyError: PixKeyError,
        expectedCode: String,
    ) {
        val savedRecipient = createSavedRecipient()
        recipientDbRepository.save(savedRecipient)

        val newPixKeyRequestTO = pixKeyRequestTO.copy(
            recipient = pixKeyRequestTO.recipient.copy(
                id = savedRecipient.id.value,
                pixKey = pixKeyRequestTO.recipient.pixKey!!.copy(
                    value = savedRecipient.pixKeys[0].value,
                    type = savedRecipient.pixKeys[0].type,
                ),
            ),
        )

        every {
            pixKeyManagement.findKeyDetailsCacheable(any(), any())
        } throws PixKeyException(keyError)

        val response = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.exchange(
                buildRequest(newPixKeyRequestTO, false),
                Argument.of(BillTO::class.java),
                Argument.of(ResponseTO::class.java),
            ).firstOrError().blockingGet()
        }

        response.status shouldBe BAD_REQUEST
        with(response.response.getBody(ResponseTO::class.java).get()) {
            this.code shouldBe expectedCode
        }

        val recipient = recipientDbRepository.findRecipientByAccountIDAndDocument(
            accountId = AccountId(ACCOUNT_ID),
            savedRecipient.document,
        )
        recipient.isRight() shouldBe true
        recipient.map {
            it.pixKeys[0].invalidated shouldBe true
            it.pixKeys[1].invalidated shouldBe false
        }
    }

    @Test
    fun `should return 201 on successful create pix by key with dry run`() {
        every {
            pixKeyManagement.findKeyDetailsCacheable(any(), any())
        } returns PixKeyDetailsResult(pixKeyDetails, "")

        val response =
            client.exchange(buildRequest(pixKeyRequestTO, true), BillTO::class.java).firstOrError().blockingGet()

        response.status shouldBe CREATED
        validatePixByKeyResponse(response, pixKeyRequestTO.amount)
        val getBillResponse = billEventRepository.getBillById(BillId(response.body()!!.id))
        getBillResponse.isLeft() shouldBe true
        getBillResponse.mapLeft { it.shouldBeTypeOf<ItemNotFoundException>() }

        val recipient = recipientDbRepository.findRecipientByAccountIDAndDocument(
            AccountId(ACCOUNT_ID),
            bankAccountRequestTO.recipient.document!!,
        )
        recipient.isLeft() shouldBe true
        recipient.mapLeft { it.shouldBeTypeOf<ItemNotFoundException>() }
    }

    @Test
    fun `should return 201 on successful create pix by key`() {
        every {
            pixKeyManagement.findKeyDetailsCacheable(any(), any())
        } returns PixKeyDetailsResult(pixKeyDetails, "")

        val response =
            client.exchange(buildRequest(pixKeyRequestTO, false), BillTO::class.java).firstOrError().blockingGet()

        response.status shouldBe CREATED
        validatePixByKeyResponse(response, pixKeyRequestTO.amount)
        val billId = BillId(response.body()!!.id)
        val getBillResponse = billEventRepository.getBillById(billId)
        verify {
            pixKeyManagement.findKeyDetailsCacheable(
                PixKey(
                    pixKeyRequestTO.recipient.pixKey!!.value,
                    pixKeyRequestTO.recipient.pixKey!!.type,
                ),
                DOCUMENT,
            )
        }
        getBillResponse.isRight() shouldBe true
        getBillResponse.map {
            it.recipient?.pixKeyDetails shouldBe pixKeyDetails
        }

        val recipient = recipientDbRepository.findRecipientByAccountIDAndDocument(
            AccountId(ACCOUNT_ID),
            bankAccountRequestTO.recipient.document!!,
        )
        recipient.isRight() shouldBe true
        recipient.map {
            it.pixKeys.size shouldNotBe 0
            it.pixKeys[0].value shouldBe pixKeyDetails.key.value
            it.pixKeys[0].type shouldBe pixKeyDetails.key.type
            it.lastUsed.verify(pixKey = pixKeyRequestTO.recipient.pixKey!!.value)
        }

        with(billRepository.findBill(billId, primaryWalletId)) {
            this.recipient?.pixKeyDetails shouldBe pixKeyDetails
        }
    }

    @Test
    fun `should revalidate pix key when invalidated key is successfully added`() {
        val savedPixKey = SavedPixKey(
            value = pixKeyRequestTO.recipient.pixKey!!.value,
            type = pixKeyRequestTO.recipient.pixKey!!.type,
        ).apply {
            invalidationCode = InvalidationCode.INVALID_DATA
            invalidated = true
        }
        val savedRecipient = createSavedRecipient(savedPixKey = savedPixKey)
        recipientDbRepository.save(savedRecipient)
        every {
            pixKeyManagement.findKeyDetailsCacheable(any(), any())
        } returns PixKeyDetailsResult(pixKeyDetails, "")

        val response =
            client.exchange(buildRequest(pixKeyRequestTO, false), BillTO::class.java).firstOrError().blockingGet()

        response.status shouldBe CREATED
        verify {
            pixKeyManagement.findKeyDetailsCacheable(
                PixKey(
                    pixKeyRequestTO.recipient.pixKey!!.value,
                    pixKeyRequestTO.recipient.pixKey!!.type,
                ),
                DOCUMENT,
            )
        }

        val recipient = recipientDbRepository.findRecipientByAccountIDAndDocument(
            AccountId(ACCOUNT_ID),
            bankAccountRequestTO.recipient.document!!,
        )
        recipient.isRight() shouldBe true
        recipient.map {
            it.pixKeys.filter { it == savedPixKey && !it.invalidated }.shouldNotBeEmpty()
        }
    }

    @Test
    fun `should add first pix key on recipient who has only bank account property`() {
        loadRecipientWithoutPixKeys(amazonDynamoDB = dynamoDB, document = pixKeyDocument)

        every {
            pixKeyManagement.findKeyDetailsCacheable(any(), any())
        } returns PixKeyDetailsResult(pixKeyDetails, "")

        client.exchange(buildRequest(pixKeyRequestTO, false), BillTO::class.java).firstOrError().blockingGet()

        val recipient = recipientDbRepository.findRecipientByAccountIDAndDocument(
            AccountId(ACCOUNT_ID),
            bankAccountRequestTO.recipient.document!!,
        )
        recipient.isRight() shouldBe true
        recipient.map {
            it.pixKeys shouldHaveSize 1
            it.pixKeys[0].value shouldBe pixKeyDetails.key.value
            it.pixKeys[0].type shouldBe pixKeyDetails.key.type

            it.lastUsed.verify(pixKey = pixKeyRequestTO.recipient.pixKey!!.value)
        }
    }

    @Test
    fun `should append pix key on add pix without recipientId but recipient exists`() {
        val createdRecipient = createSavedRecipient(lastUsed = LastUsed(BankAccountId("ANY_ID")))
        recipientDbRepository.save(createdRecipient)

        every {
            pixKeyManagement.findKeyDetailsCacheable(any(), any())
        } returns PixKeyDetailsResult(pixKeyDetails, "")

        val response =
            client.exchange(buildRequest(pixKeyRequestTO, false), BillTO::class.java).firstOrError().blockingGet()

        response.status shouldBe CREATED

        val recipient = recipientDbRepository.findRecipientByAccountIDAndDocument(
            AccountId(ACCOUNT_ID),
            bankAccountRequestTO.recipient.document!!,
        )
        recipient.isRight() shouldBe true
        recipient.map {
            it.pixKeys shouldHaveSize 3
            it.pixKeys[2].value shouldBe pixKeyDetails.key.value
            it.pixKeys[2].type shouldBe pixKeyDetails.key.type

            it.lastUsed.verify(pixKey = pixKeyRequestTO.recipient.pixKey!!.value)
        }
    }

    @Test
    fun `should append pix key on add pix with recipientId and recipient exists`() {
        val createdRecipient = createSavedRecipient(lastUsed = LastUsed(BankAccountId("ANY_ID")))
        val accountId = AccountId(ACCOUNT_ID)
        recipientDbRepository.save(createdRecipient)

        every {
            pixKeyManagement.findKeyDetailsCacheable(any(), any())
        } returns PixKeyDetailsResult(pixKeyDetails, "")

        val response = client.exchange(
            buildRequest(
                pixKeyRequestTO.copy(recipient = pixKeyRequestTO.recipient.copy(id = createdRecipient.id.value)),
                false,
            ),
            BillTO::class.java,
        ).firstOrError().blockingGet()

        response.status shouldBe CREATED

        with(recipientDbRepository.findByAccountId(accountId)) {
            size shouldBe 1
            first().pixKeys shouldHaveSize createdRecipient.pixKeys.size + 1
            first().lastUsed.verify(pixKey = pixKeyRequestTO.recipient.pixKey!!.value)
        }
    }

    @Test
    fun `should not append pix key on add pix when key already exists`() {
        val createdRecipient = createSavedRecipient(lastUsed = LastUsed(BankAccountId("ANY_ID")))
        recipientDbRepository.save(createdRecipient)

        val recipientKeyTO = PixKeyRequestTO(
            type = PixKeyType.EMAIL,
            value = "<EMAIL>",
        )
        val recipientKey = PixKey(recipientKeyTO.value, recipientKeyTO.type)

        every {
            pixKeyManagement.findKeyDetailsCacheable(any(), any())
        } returns PixKeyDetailsResult(pixKeyDetails.copy(key = recipientKey), "")

        val response = client.exchange(
            buildRequest(
                pixKeyRequestTO.copy(
                    recipient = pixKeyRequestTO.recipient.copy(
                        pixKey = recipientKeyTO,
                    ),
                ),
                false,
            ),
            BillTO::class.java,
        ).firstOrError().blockingGet()

        response.status shouldBe CREATED

        val recipient = recipientDbRepository.findRecipientByAccountIDAndDocument(
            AccountId(ACCOUNT_ID),
            bankAccountRequestTO.recipient.document!!,
        )
        recipient.isRight() shouldBe true
        recipient.map {
            it.pixKeys shouldHaveSize 2
            it.lastUsed.verify(pixKey = recipientKeyTO.value)
        }
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "true, null, null",
            "true, 100, null",
            "false, null, null",
            "false, 100, null",

            "true, null, *********",
            "true, 100, *********",
            "false, null, *********",
            "false, 100, *********",
        ],
        nullValues = ["null"],
    )
    fun `deve retornar 201 quando criar um pix com sucesso por QRCODE, não deve retornar os detalhes da chave, não deve salvar o contato, e deve voltar o id do QRCode e o fixed amount se houverem`(dryRun: Boolean, fixedAmount: Long?, qrCodePixId: String?) {
        every {
            pixQRCodeParserService.parseQRCodeCacheable(PixCopyAndPaste(qrCode), DOCUMENT)
        } returns PixQRCodeDetailsResult(pixKeyDetails, "", qrCodeInfo = PixQrCodeData(pixCopyAndPaste = PixCopyAndPaste(qrCode), pixId = qrCodePixId, fixedAmount = fixedAmount, info = "info", type = PixQrCodeType.STATIC, additionalInfo = mapOf("chave" to "valor"), expiration = null, originalAmount = 100, automaticPixRecurringDataJson = null)).right()

        val response =
            client.exchange(buildRequest(pixQrCodeRequestTO.copy(amount = fixedAmount ?: pixQrCodeRequestTO.amount), dryRun), BillTO::class.java).firstOrError().blockingGet()

        response.status shouldBe CREATED

        val body = response.body()
        val pixQrCodeData = body.pixQrCodeData
        pixQrCodeData?.pixId shouldBe qrCodePixId
        pixQrCodeData?.fixedAmount shouldBe fixedAmount
        pixQrCodeData?.info shouldBe "info"
        pixQrCodeData?.type shouldBe PixQrCodeType.STATIC
        pixQrCodeData?.additionalInfo shouldBe mapOf("chave" to "valor")

        body.recipient!!.pixKey shouldBe null
        body.recipient!!.bankDetails shouldBe null

        with(response.body()!!) {
            amount shouldBe (fixedAmount ?: pixQrCodeRequestTO.amount)
            description shouldBe pixKeyRequestTO.description
            dueDate shouldBe pixKeyRequestTO.dueDate
            billType shouldBe BillType.PIX.name
            billRecipient!!.document shouldBe pixKeyDetails.owner.document
            billRecipient!!.alias shouldBe pixKeyRequestTO.recipient.alias
            billRecipient!!.name shouldBe pixKeyDetails.owner.name
            availablePaymentMethods.shouldContainExactlyInAnyOrder(
                PaymentMethodType.BALANCE,
                PaymentMethodType.EXTERNAL,
            )
        }

        val billId = BillId(response.body()!!.id)
        val getBillResponse = billEventRepository.getBill(primaryWalletId, billId)

        verify {
            pixKeyManagement.findKeyDetailsCacheable(any(), any()) wasNot Called
            pixQRCodeParserService.parseQRCodeCacheable(PixCopyAndPaste(qrCode), DOCUMENT)
        }

        if (!dryRun) {
            getBillResponse.isRight() shouldBe true
            getBillResponse.map {
                it.recipient?.pixKeyDetails shouldBe pixKeyDetails
                it.pixQrCodeData?.pixId shouldBe qrCodePixId
                it.pixQrCodeData?.fixedAmount shouldBe fixedAmount
                it.pixQrCodeData?.info shouldBe "info"
                it.pixQrCodeData?.type shouldBe PixQrCodeType.STATIC
                it.pixQrCodeData?.additionalInfo shouldBe mapOf("chave" to "valor")
            }
            with(billRepository.findBill(billId, primaryWalletId)) {
                this.recipient?.pixKeyDetails shouldBe pixKeyDetails
                this.pixQrCodeData?.pixId shouldBe qrCodePixId
                this.pixQrCodeData?.fixedAmount shouldBe fixedAmount
                pixQrCodeData?.info shouldBe "info"
                pixQrCodeData?.type shouldBe PixQrCodeType.STATIC
                pixQrCodeData?.additionalInfo shouldBe mapOf("chave" to "valor")
            }
        } else {
            getBillResponse.isLeft() shouldBe true
        }

        val recipient = recipientDbRepository.findRecipientByAccountIDAndDocument(
            AccountId(ACCOUNT_ID),
            bankAccountRequestTO.recipient.document!!,
        )
        recipient.isLeft() shouldBe true
    }

    @Test
    fun `deve falhar quando criar um pix por QRCODE com dryrun e com fixedAmount e valor for diferente`() {
        every {
            pixQRCodeParserService.parseQRCodeCacheable(PixCopyAndPaste(qrCode), DOCUMENT)
        } returns PixQRCodeDetailsResult(pixKeyDetails, "", qrCodeInfo = PixQrCodeData(pixCopyAndPaste = PixCopyAndPaste(qrCode), pixId = qrCodePixId, fixedAmount = 99L, info = "info", type = PixQrCodeType.STATIC, additionalInfo = null, expiration = null, originalAmount = null, automaticPixRecurringDataJson = null)).right()

        val response = assertThrows<HttpClientResponseException> {
            client.exchange(buildRequest(pixQrCodeRequestTO.copy(amount = 100L), dryRun = true), BillTO::class.java).firstOrError().blockingGet()
        }

        response.status shouldBe BAD_REQUEST
    }

    @Test
    fun `deve falhar quando criar um pix por QRCODE sem dryrun e com fixedAmount e valor for diferente`() {
        every {
            pixQRCodeParserService.parseQRCodeCacheable(PixCopyAndPaste(qrCode), DOCUMENT)
        } returns PixQRCodeDetailsResult(pixKeyDetails, "", qrCodeInfo = PixQrCodeData(pixCopyAndPaste = PixCopyAndPaste(qrCode), pixId = qrCodePixId, fixedAmount = 99L, info = "info", type = PixQrCodeType.STATIC, additionalInfo = null, originalAmount = null, expiration = null, automaticPixRecurringDataJson = null)).right()

        val response = assertThrows<HttpClientResponseException> {
            client.exchange(buildRequest(pixQrCodeRequestTO.copy(amount = 100L), dryRun = false), BillTO::class.java).firstOrError().blockingGet()
        }

        response.status shouldBe BAD_REQUEST
    }

    @Test
    fun `deve falhar quando criar um pix por QRCODE recorrente`() {
        val response = assertThrows<HttpClientResponseException> {
            client.exchange(
                buildRequest(
                    pixQrCodeRequestTO.copy(
                        recurrence = RecurrenceRequestTO(
                            frequency = RecurrenceFrequency.MONTHLY,
                            endDate = getZonedDateTime().plusMonths(1).format(dateFormat),
                        ),
                    ),
                    dryRun = true,
                ),
                Argument.of(BillTO::class.java),
                Argument.of(ResponseTO::class.java),
            ).firstOrError().blockingGet()
        }

        verify {
            pixQRCodeParserService.parseQRCodeCacheable(any(), any()) wasNot Called
        }

        response.status shouldBe BAD_REQUEST
        response.response.getBody(ResponseTO::class.java).get().code shouldBe "4205"
    }

    private fun validatePixByAccountResponse(response: HttpResponse<BillTO>) {
        with(response.body()!!) {
            assertSoftly {
                amount shouldBe bankAccountRequestTO.amount
                description shouldBe bankAccountRequestTO.description
                dueDate shouldBe bankAccountRequestTO.dueDate
                billType shouldBe BillType.PIX.name
                billRecipient!!.document shouldBe bankAccountRequestTO.recipient.document
                billRecipient!!.alias shouldBe bankAccountRequestTO.recipient.alias
                billRecipient!!.name shouldBe bankAccountRequestTO.recipient.name
                billRecipient!!.bankDetails shouldBe bankAccountRequestTO.recipient.bankDetails
                LocalDate.parse(effectiveDueDate, dateFormat) shouldHaveSameDayAs LocalDate.parse(dueDate, dateFormat)
            }
        }
    }

    private fun validatePixByKeyResponse(response: HttpResponse<BillTO>, expectedAmount: Long) {
        with(response.body()!!) {
            assertSoftly {
                amount shouldBe expectedAmount
                description shouldBe pixKeyRequestTO.description
                dueDate shouldBe pixKeyRequestTO.dueDate
                billType shouldBe BillType.PIX.name
                billRecipient!!.document shouldBe pixKeyDetails.owner.document
                billRecipient!!.alias shouldBe pixKeyRequestTO.recipient.alias
                billRecipient!!.name shouldBe pixKeyDetails.owner.name
                billRecipient!!.pixKey!!.type shouldBe pixKeyDetails.key.type
                billRecipient!!.pixKey!!.value shouldBe pixKeyDetails.key.value
                billRecipient!!.pixKey!!.accountNo shouldBe pixKeyDetails.holder.accountNo
                billRecipient!!.pixKey!!.accountDv shouldBe pixKeyDetails.holder.accountDv
                billRecipient!!.pixKey!!.accountType shouldBe pixKeyDetails.holder.accountType
                billRecipient!!.pixKey!!.routingNo shouldBe pixKeyDetails.holder.routingNo
                billRecipient!!.pixKey!!.ispb shouldBe pixKeyDetails.holder.ispb
                billRecipient!!.pixKey!!.institutionName shouldBe pixKeyDetails.holder.institutionName
                billRecipient!!.pixKey!!.document shouldBe pixKeyDetails.owner.document
                billRecipient!!.pixKey!!.name shouldBe pixKeyDetails.owner.name
                availablePaymentMethods.shouldContainExactlyInAnyOrder(
                    PaymentMethodType.BALANCE,
                    PaymentMethodType.EXTERNAL,
                )
            }
        }
    }

    private fun createScheduledPix(amount: Long, scheduleDate: LocalDate) {
        val billId = BillId("BILL-${UUID.randomUUID()}")
        billRepository.save(
            Bill.build(
                pixAdded.copy(
                    walletId = primaryWalletId,
                    billId = billId,
                    amount = amount,
                    amountTotal = amount,
                    created = getZonedDateTime().toInstant().toEpochMilli() - 20,
                ),
                pixPaymentScheduled.copy(
                    walletId = primaryWalletId,
                    billId = billId,
                    created = getZonedDateTime().toInstant().toEpochMilli() - 10,
                ),
            ),
        )

        scheduledBillRepository.save(
            scheduledBill.copy(
                billId = billId,
                walletId = primaryWalletId,
                scheduledDate = scheduleDate,
                amount = amount,
                billType = BillType.PIX,
            ),
        )
    }

    private fun createScheduledBoleto(amount: Long, scheduleDate: LocalDate) {
        val billId = BillId("BILL-${UUID.randomUUID()}")
        billRepository.save(
            Bill.build(
                billAddedFicha.copy(
                    walletId = primaryWalletId,
                    billId = billId,
                    amount = amount,
                    amountTotal = amount,
                    created = getZonedDateTime().toInstant().toEpochMilli() - 20,
                ),
                billPaymentScheduled.copy(
                    walletId = primaryWalletId,
                    billId = billId,
                    amount = billAddedFicha.amountTotal,
                    created = getZonedDateTime().toInstant().toEpochMilli() - 10,
                ),
            ),
        )

        scheduledBillRepository.save(
            scheduledBill.copy(
                billId = billId,
                scheduledDate = scheduleDate,
                amount = amount,
                billType = BillType.FICHA_COMPENSACAO,
            ),
        )
    }

    companion object {
        @JvmStatic
        fun failPix(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(PixKeyError.KeyNotConfirmed, CreateBillError.PIX_KEY_NOT_CONFIRMED.code),
                Arguments.of(PixKeyError.KeyNotFound, CreateBillError.PIX_KEY_NOT_FOUND.code),
            )
        }
    }
}