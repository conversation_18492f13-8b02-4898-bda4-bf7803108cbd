package ai.friday.billpayment.integration

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.arbi.AccessTokenTO
import ai.friday.billpayment.adapters.arbi.ArbiAdapterException
import ai.friday.billpayment.adapters.arbi.ArbiResponseTO
import ai.friday.billpayment.adapters.arbi.BillValidateRequestTO
import ai.friday.billpayment.adapters.arbi.CIPSitTitPgto
import ai.friday.billpayment.adapters.arbi.DDATO
import ai.friday.billpayment.adapters.arbi.DomainTO
import ai.friday.billpayment.adapters.arbi.GrantCodeResponseTO
import ai.friday.billpayment.adapters.arbi.GrantCodeTO
import ai.friday.billpayment.adapters.arbi.convertToLongOrZero
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DDABillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DDADbRepository
import ai.friday.billpayment.adapters.dynamodb.DDADynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.adapters.messaging.SQSDDAHandler
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.CreateBoletoRequest
import ai.friday.billpayment.app.dda.DDABill
import ai.friday.billpayment.app.dda.DDARegister
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.dda.DDAStatus
import ai.friday.billpayment.app.dda.toDDARegisterMessage
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.fichaCompensacaoCreditCardAdded
import ai.friday.billpayment.getActiveBillFicha
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.withGivenDateTime
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.should
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.CapturingSlot
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import io.reactivex.Flowable
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.UUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@MicronautTest(environments = [FRIDAY_ENV])
class SQSDDAHandlerIntegrationTest(ddaService: DDAService, val dynamoDB: AmazonDynamoDB) {

    private val mockBoletoSettlementService: BoletoSettlementService = mockk()

    private val mockHttpClient: RxHttpClient = mockk(relaxed = true) {
        every {
            stop()
        } returns this
    }

    @MockBean(RxHttpClient::class)
    fun httpClient(): RxHttpClient = mockHttpClient

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk {
        every {
            findWalletCategories(any())
        } returns emptyList()
    }

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val enhancedClient = getDynamoDB()
    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(enhancedClient),
        refundedClient = RefundedBillDynamoDAO(enhancedClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(enhancedClient),
    )
    private val billEventDAO = BillEventDynamoDAO(enhancedClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)
    private val billEventRepository = BillEventDBRepository(
        billEventDAO,
        uniqueConstraintDAO,
        allFalseFeatureConfiguration,
        transactionDynamo,
    )

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = mockk(),
    )
    private val ddaRepository = DDADbRepository(
        ddaDAO = DDADynamoDAO(enhancedClient),
        ddaBillDAO = DDABillDynamoDAO(enhancedClient),
    )

    private val amazonSQS: SqsClient = mockk()

    private val sqsMessageHandlerConfiguration = mockk<SQSMessageHandlerConfiguration>() {
        every { ddaQueueName } returns "dda-queue-tests"
    }

    private val handler = SQSDDAHandler(amazonSQS, sqsMessageHandlerConfiguration, ddaService)

    private val ddaQueryDateFormat = DateTimeFormatter.ofPattern("dd/MM/yyyy")

    private val ddaRegister = DDARegister(
        accountId = AccountId(value = ACCOUNT_ID),
        document = DOCUMENT,
        created = getZonedDateTime(),
        status = DDAStatus.ACTIVE,
        lastUpdated = getZonedDateTime(),
    )

    private val ddaRegister2 = DDARegister(
        accountId = AccountId(value = ACCOUNT_ID_2),
        document = DOCUMENT_2,
        created = getZonedDateTime(),
        status = DDAStatus.ACTIVE,
        lastUpdated = getZonedDateTime(),
    )

    private val wallet = WalletFixture(defaultWalletId = WalletId(WALLET_ID), founderAccountId = AccountId(ACCOUNT_ID)).buildWallet()

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        loadAccountIntoDb(dynamoDB, defaultWalletId = wallet.id)

        walletRepository.save(wallet)
        every {
            mockHttpClient.retrieve(
                any<HttpRequest<GrantCodeTO>>(),
                Argument.of(GrantCodeResponseTO::class.java),
            )
        } answers {
            Flowable.just(GrantCodeResponseTO("http://localhost/?code=XXXX_GRANT_CODE_XXXX"))
        }

        every {
            mockHttpClient.retrieve(
                any<HttpRequest<Map<String, String>>>(),
                Argument.of(AccessTokenTO::class.java),
            )
        } answers {
            Flowable.just(
                AccessTokenTO(
                    accessToken = "XXXX_ACCESS_TOKEN_XXXX",
                    refreshToken = "XXXX_REFRESH_TOKEN_XXXX",
                    expiresIn = 365,
                    tokenType = "ACCESS_TOKEN",
                ),
            )
        }

        every {
            mockHttpClient.retrieve(
                any<HttpRequest<Map<String, String>>>(),
                Argument.listOf(DomainTO::class.java),
                Argument.STRING,
            )
        } answers {
            Flowable.just(listOf())
        }

        ddaRepository.save(ddaRegister)
    }

    @Test
    fun `should skip execution if dda register is not found and delete message`() {
        loadAccountIntoDb(
            dynamoDB,
            defaultWalletId = WalletId(WALLET_ID_2),
            accountId = AccountId(ACCOUNT_ID_2),
            document = DOCUMENT_2,
        )

        val response = handler.handleMessage(buildMessage(ddaRegister2))
        response.shouldDeleteMessage.shouldBeTrue()

        verify {
            mockHttpClient wasNot called
        }
    }

    @ParameterizedTest
    @EnumSource(value = DDAStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["ACTIVE"])
    fun `should skip execution if dda register is not active and delete message`(ddaStatus: DDAStatus) {
        loadAccountIntoDb(
            dynamoDB,
            defaultWalletId = WalletId(WALLET_ID_2),
            accountId = AccountId(ACCOUNT_ID_2),
            document = DOCUMENT_2,
        )

        ddaRepository.save(ddaRegister2.copy(status = ddaStatus))

        val response = handler.handleMessage(buildMessage(ddaRegister2))
        response.shouldDeleteMessage.shouldBeTrue()

        verify {
            mockHttpClient wasNot called
        }
    }

    @Test
    fun `should add bill on new bill`() {
        checkDDABillAdded(DOCUMENT)
    }

    private fun buildMessage(ddaRegister: DDARegister) = Message.builder()
        .body(jacksonObjectMapper().writeValueAsString(ddaRegister.toDDARegisterMessage()))
        .build()

    @Test
    fun `should not add bill on bill already exists`() {
        addBillIntoDb(enhancedClient, getActiveBillFicha(WalletId(WALLET_ID)))

        billEventRepository.save(billAddedFicha)

        setupDDAQuery(valor = defaultValue)

        val response = handler.handleMessage(buildMessage(ddaRegister))
        response.shouldDeleteMessage.shouldBeTrue()

        val bills = billRepository.findByWallet(WalletId(WALLET_ID))
        assertEquals(1, bills.size)
        assertEquals(billAddedFicha.billId, bills[0].billId)
    }

    @Test
    fun `should not add bill on user not found`() {
        val invalidDocument = "12345678909"
        setupDDAQuery(invalidDocument, valor = defaultValue)
        val response = handler.handleMessage(buildMessage(ddaRegister))
        response.shouldDeleteMessage.shouldBeTrue()
    }

    @Test
    fun `should not update bill when is not ACTIVE and already exists`() {
        addBillIntoDb(enhancedClient, getActiveBillFicha(WalletId(WALLET_ID)).copy(status = BillStatus.PAID))
        billEventRepository.save(billAddedFicha)
        addBillEventIntoDb(dynamoDB, billPaid)

        setupDDAQuery(valor = defaultValue)

        val response = handler.handleMessage(buildMessage(ddaRegister))
        response.shouldDeleteMessage.shouldBeTrue()

        verify(exactly = 0) {
            mockBoletoSettlementService.validateBill(ofType(CreateBoletoRequest::class))
            mockBoletoSettlementService.validateBill(ofType(Bill::class))
        }
        val bills = billRepository.findByWallet(WalletId(WALLET_ID))
        assertEquals(1, bills.size)
        assertEquals(billAddedFicha.billId, bills[0].billId)
        assertEquals(BillStatus.PAID, bills[0].status)
    }

    @Test
    fun `should update bill when is ACTIVE and is outdated`() {
        val changedAmountTotal = "2.00"
        addBillIntoDb(enhancedClient, getActiveBillFicha(WalletId(WALLET_ID)))
        billEventRepository.save(billAddedFicha)

        setupDDAQuery(dueDate = billAddedFicha.dueDate, valor = defaultValue)
        setupBillValidate(dueDate = billAddedFicha.dueDate, valorTotalCobrar = changedAmountTotal)

        ddaRepository.save(
            DDABill(
                barcode = billAddedFicha.barcode,
                document = billAddedFicha.document,
                dueDate = billAddedFicha.dueDate,
                walletId = WalletId(WALLET_ID),
                billId = billAddedFicha.billId,
                lastStatus = BillStatus.ACTIVE,
            ),
        )

        val response = handler.handleMessage(buildMessage(ddaRegister))
        response.shouldDeleteMessage.shouldBeTrue()

        val bills = billRepository.findByWallet(WalletId(WALLET_ID))
        assertEquals(1, bills.size)
        assertEquals(billAddedFicha.billId, bills[0].billId)
        assertEquals(BillStatus.ACTIVE, bills[0].status)
        assertEquals(convertToLongOrZero(changedAmountTotal), bills[0].amountTotal)
        val itemList = billEventDAO.findByPartitionKey(billAddedFicha.billId.value)
        assertEquals(2, itemList.size) // Bill added + Updated
    }

    @Test
    fun `should not update bill on EDDA0526 error`() {
        addBillIntoDb(enhancedClient, getActiveBillFicha(WalletId(WALLET_ID)))
        billEventRepository.save(billAddedFicha)

        setupDDAQuery(valor = defaultValue)
        every {
            mockHttpClient.retrieve(
                any<HttpRequest<BillValidateRequestTO>>(),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.listOf(ArbiResponseTO::class.java),
            )
        } answers {
            val resultado =
                "7 - Mensagem rejeitada pela CIP: EDDA0526 - Código de barras não localizado na base centralizada"
            val response = listOf(
                ArbiResponseTO(
                    idTransacao = 1,
                    resultado = resultado,
                    idModulo = 2,
                    idRequisicaoArbi = "1252020183179483@2469098973966",
                    descricaoStatus = "Erro ConsultaTitulo",
                    idStatus = 422,
                    idRequisicaoParceiro = "2573821d-75e0-4fb5-80f2-5d81550cb1f8",
                ),
            )
            Flowable.error(HttpClientResponseException("erro", HttpResponse.badRequest(response)))
        }

        val response = handler.handleMessage(buildMessage(ddaRegister))
        response.shouldDeleteMessage.shouldBeTrue()

        val bills = billRepository.findByWallet(WalletId(WALLET_ID))
        assertEquals(1, bills.size)
        assertEquals(billAddedFicha.billId, bills[0].billId)
        assertEquals(BillStatus.ACTIVE, bills[0].status)
    }

    @Test
    fun `should update bill to already paid when payment status is 7`() {
        addBillIntoDb(enhancedClient, getActiveBillFicha(WalletId(WALLET_ID)))
        billEventRepository.save(billAddedFicha)

        setupDDAQuery(dueDate = billAddedFicha.dueDate, valor = defaultValue)
        setupBillValidate(dueDate = billAddedFicha.dueDate, situacaoTitulo = CIPSitTitPgto.PAGAMENTO_JA_EFETUADO)

        ddaRepository.save(
            DDABill(
                barcode = billAddedFicha.barcode,
                document = billAddedFicha.document,
                dueDate = billAddedFicha.dueDate,
                walletId = WalletId(WALLET_ID),
                billId = billAddedFicha.billId,
                lastStatus = BillStatus.ACTIVE,
            ),
        )

        val response = handler.handleMessage(buildMessage(ddaRegister))
        response.shouldDeleteMessage.shouldBeTrue()

        val bills = billRepository.findByWallet(WalletId(WALLET_ID))
        assertEquals(1, bills.size)
        assertEquals(billAddedFicha.billId, bills[0].billId)
        assertEquals(BillStatus.ALREADY_PAID, bills[0].status)
    }

    @Test
    fun `should update bill to not payable when bill has effective settle date time but has no payment`() {
        addBillIntoDb(enhancedClient, getActiveBillFicha(WalletId(WALLET_ID)))
        billEventRepository.save(billAddedFicha)

        setupDDAQuery(dueDate = billAddedFicha.dueDate, valor = defaultValue)
        setupBillValidate(
            dueDate = billAddedFicha.dueDate,
            situacaoTitulo = CIPSitTitPgto.PAGAMENTO_JA_BAIXADO,
            valorTituloBaixaEfetiva = "",
        )

        ddaRepository.save(
            DDABill(
                barcode = billAddedFicha.barcode,
                document = billAddedFicha.document,
                dueDate = billAddedFicha.dueDate,
                walletId = WalletId(WALLET_ID),
                billId = billAddedFicha.billId,
                lastStatus = BillStatus.ACTIVE,
            ),
        )

        val response = handler.handleMessage(buildMessage(ddaRegister))
        response.shouldDeleteMessage.shouldBeTrue()

        val bills = billRepository.findByWallet(WalletId(WALLET_ID))
        assertEquals(1, bills.size)
        assertEquals(billAddedFicha.billId, bills[0].billId)
        assertEquals(BillStatus.NOT_PAYABLE, bills[0].status)
    }

    @Test
    fun `should update bill to already paid when bill has effective settle with amount`() {
        addBillIntoDb(enhancedClient, getActiveBillFicha(WalletId(WALLET_ID)))
        billEventRepository.save(billAddedFicha)

        setupDDAQuery(dueDate = billAddedFicha.dueDate, barCode = billAddedFicha.barcode, valor = defaultValue)
        setupBillValidate(
            barCode = billAddedFicha.barcode,
            dueDate = billAddedFicha.dueDate,
            situacaoTitulo = CIPSitTitPgto.PAGAMENTO_JA_BAIXADO,
            valorTituloBaixaEfetiva = "10.00",
        )

        ddaRepository.save(
            DDABill(
                barcode = billAddedFicha.barcode,
                document = billAddedFicha.document,
                dueDate = billAddedFicha.dueDate,
                walletId = WalletId(WALLET_ID),
                billId = billAddedFicha.billId,
                lastStatus = BillStatus.ACTIVE,
            ),
        )

        val response = handler.handleMessage(buildMessage(ddaRegister))
        response.shouldDeleteMessage.shouldBeTrue()

        val bills = billRepository.findByWallet(WalletId(WALLET_ID))
        assertEquals(1, bills.size)
        assertEquals(billAddedFicha.billId, bills[0].billId)
        assertEquals(BillStatus.ALREADY_PAID, bills[0].status)
    }

    @Test
    fun `should add bill on default wallet`() {
        val defaultWalletId = WalletId("WALLET-${UUID.randomUUID()}")
        loadAccountIntoDb(dynamoDB, defaultWalletId = defaultWalletId)

        val dueDate = "2020-12-30"
        val amount = "1759.24"
        val expectedRecipient = "BANCO INTER"
        setupDDAQuery(valor = defaultValue)
        val slot = setupBillValidate(valorTotalCobrar = amount, razaoSocialSacadorAvalista = expectedRecipient)

        val response = handler.handleMessage(buildMessage(ddaRegister))
        response.shouldDeleteMessage.shouldBeTrue()

        val bills = billRepository.findByWallet(defaultWalletId)
        bills.size shouldBe 1
        assertSoftly {
            slot.captured.body.get().pagamentos.codigoBarras shouldBe FICHA_DE_COMPENSACAO_BARCODE
            with(bills[0]) {
                this.barCode?.digitable shouldBe FICHA_DE_COMPENSACAO_DIGITABLE_LINE
                this.source shouldBe ActionSource.DDA(AccountId(ACCOUNT_ID))
                this.payerAlias shouldBe payerAlias
                this.recipient?.name shouldBe expectedRecipient
            }
        }
        val ddaBill = ddaRepository.find(
            barCode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
            document = DOCUMENT,
            dueDate = LocalDate.parse(dueDate, DateTimeFormatter.ISO_DATE),
        )
        ddaBill shouldNotBe null
        with(ddaBill!!) {
            walletId shouldBe defaultWalletId
            billId shouldBe bills[0].billId
            barcode.digitable shouldBe FICHA_DE_COMPENSACAO_DIGITABLE_LINE
            document shouldBe DOCUMENT
            lastStatus shouldBe bills[0].status
        }
    }

    @Test
    fun `should add bill on wallet with same barcode but different duedate and update older bill to not_payable`() {
        val defaultWalletId = WalletId("WALLET-${UUID.randomUUID()}")
        loadAccountIntoDb(dynamoDB, defaultWalletId = defaultWalletId)

        val olderBillAdded = fichaCompensacaoCreditCardAdded.copy(
            dueDate = getLocalDate().minusMonths(1),
            walletId = defaultWalletId,
        )
        billEventRepository.save(olderBillAdded)
        val bill = Bill.build(olderBillAdded)
        billRepository.save(bill)

        val amount = 500.0
        val newDuedate = getLocalDate()

        setupDDAQuery(dueDate = newDuedate, valor = amount, barCode = olderBillAdded.barcode)
        setupBillValidate(
            barCode = olderBillAdded.barcode,
            dueDate = newDuedate,
            valorTitulo = amount.toString(),
        )

        val response = handler.handleMessage(buildMessage(ddaRegister))
        response.shouldDeleteMessage.shouldBeTrue()

        val bills = billRepository.findByWallet(defaultWalletId)
        bills.size shouldBe 2
        bills.find { billView -> billView.dueDate.format(dateFormat) == newDuedate.format(dateFormat) }
            .should { billView ->
                billView shouldNotBe null
                billView?.barCode?.digitable shouldBe olderBillAdded.barcode.digitable
                billView?.amount shouldBe (amount * 100).toLong()
                billView?.status shouldBe BillStatus.ACTIVE
            }
        bills.find { billView -> billView.dueDate == olderBillAdded.dueDate }.should { billView ->
            billView shouldNotBe null
            billView?.barCode?.digitable shouldBe olderBillAdded.barcode.digitable
            billView?.amount shouldBe olderBillAdded.amount
            billView?.status shouldBe BillStatus.NOT_PAYABLE
        }

        val newDDABill = ddaRepository.find(barCode = olderBillAdded.barcode, document = DOCUMENT, dueDate = newDuedate)
        newDDABill shouldNotBe null
        with(newDDABill!!) {
            lastStatus shouldBe BillStatus.ACTIVE
        }
    }

    @Test
    fun `should update bill on wallet to already paid when payment status is 7`() {
        val defaultWalletId = wallet.id
        loadAccountIntoDb(dynamoDB, defaultWalletId = defaultWalletId)

        val billAdded = fichaCompensacaoCreditCardAdded.copy(walletId = defaultWalletId)
        billEventRepository.save(billAdded)
        val bill = Bill.build(billAdded)
        val dueDate = fichaCompensacaoCreditCardAdded.dueDate
        val amount = fichaCompensacaoCreditCardAdded.amount.toDouble() / 100
        billRepository.save(bill)
        ddaRepository.save(
            DDABill(
                barcode = billAdded.barcode,
                document = billAdded.document,
                dueDate,
                defaultWalletId,
                billId = billAdded.billId,
                lastStatus = BillStatus.ACTIVE,
            ),
        )

        setupDDAQuery(dueDate = dueDate, valor = amount, barCode = billAdded.barcode)
        setupBillValidate(
            barCode = billAdded.barcode,
            dueDate = dueDate,
            valorTitulo = amount.toString(),
            situacaoTitulo = CIPSitTitPgto.PAGAMENTO_JA_EFETUADO,
        )

        val response = handler.handleMessage(buildMessage(ddaRegister))
        response.shouldDeleteMessage.shouldBeTrue()

        val bills = billRepository.findByWallet(defaultWalletId)
        bills.size shouldBe 1
        assertEquals(billAdded.billId, bills[0].billId)
        assertEquals(BillStatus.ALREADY_PAID, bills[0].status)
        val newDDABill = ddaRepository.find(barCode = billAdded.barcode, document = DOCUMENT, dueDate = dueDate)
        newDDABill shouldNotBe null
        with(newDDABill!!) {
            lastStatus shouldBe BillStatus.ALREADY_PAID
        }
    }

    @Test
    fun `should update last successfull dda execution`() {
        val defaultWalletId = WalletId("WALLET-${UUID.randomUUID()}")
        loadAccountIntoDb(dynamoDB, defaultWalletId = defaultWalletId)

        setupDDAQuery(valor = defaultValue)
        val now = getZonedDateTime().plusDays(10)
        withGivenDateTime(now) {
            val response = handler.handleMessage(buildMessage(ddaRegister))
            response.shouldDeleteMessage.shouldBeTrue()

            val ddaRegister = ddaRepository.find(AccountId(ACCOUNT_ID))
            ddaRegister!!.lastSuccessfullExecution shouldBe now
        }
    }

    @Test
    fun `should not update last successfull when dda execution fails`() {
        val defaultWalletId = WalletId("WALLET-${UUID.randomUUID()}")
        loadAccountIntoDb(dynamoDB, defaultWalletId = defaultWalletId)

        every {
            mockHttpClient.retrieve(
                any<HttpRequest<DDATO>>(),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.STRING,
            )
        } answers {
            Flowable.error(
                HttpClientResponseException(
                    "erro",
                    HttpResponse.badRequest("[]"),
                ),
            )
        }

        val now = getZonedDateTime().plusDays(10)
        withGivenDateTime(now) {
            assertThrows<ArbiAdapterException> {
                handler.handleMessage(buildMessage(ddaRegister))
            }
            val ddaRegister = ddaRepository.find(AccountId(ACCOUNT_ID))
            ddaRegister?.lastSuccessfullExecution.shouldBeNull()
        }
    }

    @Test
    fun `should update dda bill when bill on wallet is paid and dda bill is active`() {
        val defaultWalletId = wallet.id
        loadAccountIntoDb(dynamoDB, defaultWalletId = defaultWalletId)

        val billAdded = fichaCompensacaoCreditCardAdded.copy(walletId = defaultWalletId)
        billEventRepository.save(billAdded)
        billEventRepository.save(billPaid.copy(billId = billAdded.billId, walletId = billAdded.walletId))
        val bill = Bill.build(billAdded, billPaid.copy(billId = billAdded.billId, walletId = billAdded.walletId))
        val dueDate = fichaCompensacaoCreditCardAdded.dueDate
        val amount = fichaCompensacaoCreditCardAdded.amount.toDouble() / 100
        billRepository.save(bill)
        ddaRepository.save(
            DDABill(
                barcode = billAdded.barcode,
                document = billAdded.document,
                dueDate,
                defaultWalletId,
                billId = billAdded.billId,
                lastStatus = BillStatus.ACTIVE,
            ),
        )

        setupDDAQuery(dueDate = dueDate, valor = amount, barCode = billAdded.barcode)
        setupBillValidate(
            barCode = billAdded.barcode,
            dueDate = dueDate,
            valorTitulo = amount.toString(),
        )

        val response = handler.handleMessage(buildMessage(ddaRegister))
        response.shouldDeleteMessage.shouldBeTrue()

        val newDDABill = ddaRepository.find(barCode = billAdded.barcode, document = DOCUMENT, dueDate = dueDate)
        newDDABill shouldNotBe null
        with(newDDABill!!) {
            lastStatus shouldBe BillStatus.PAID
        }
    }

    val defaultValue = 150.34000
    fun setupDDAQuery(
        document: String = DOCUMENT,
        dueDate: LocalDate = LocalDate.parse("30/12/2020", ddaQueryDateFormat),
        valor: Double,
        barCode: BarCode = BarCode.of(FICHA_DE_COMPENSACAO_BARCODE),
    ) {
        val resultado =
            """{"inscricaosacado":"$document","nomesacado":"CONTRIBUINTE","banco":"237","inscricaocedente":"30295513000138","nomecedente":"ERJ - DETRAN","datavctotitulo":"${
            dueDate.format(ddaQueryDateFormat)
            }","valor":"$valor","codbarras":"${barCode.number}","dataemissaotitulo":"16/03/2020","linhadigitavel":"${barCode.digitable}","inscricaocedenteori":"30295513000138","nomecedenteori":"DETRAN - ESTADO DO RIO DE JANEIRO","dthrsittitulo":"16/03/2020 11:37:26"}"""
        every {
            mockHttpClient.retrieve(
                any<HttpRequest<DDATO>>(),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.STRING,
            )
        } answers {
            Flowable.just(
                listOf(
                    ArbiResponseTO(
                        idTransacao = 8,
                        resultado = resultado,
                        idModulo = 2,
                        idRequisicaoArbi = "1252020183179483@2469098973966",
                        descricaoStatus = "sucesso",
                        idStatus = 201,
                        idRequisicaoParceiro = "2573821d-75e0-4fb5-80f2-5d81550cb1f8",
                    ),
                ),
            )
        }
    }

    private fun checkDDABillAdded(document: String, payerAlias: String? = null) {
        setupDDAQuery(document, valor = defaultValue)

        val amount = "1759.24"
        val dueDate = LocalDate.parse("2020-02-10", dateFormat)
        val expectedRecipient = "BANCO INTER"
        val expectedAmount = convertToLongOrZero(amount)
        slot<HttpRequest<BillValidateRequestTO>>()
        val slot = setupBillValidate(
            document = document,
            valorTotalCobrar = amount,
            razaoSocialSacadorAvalista = expectedRecipient,
            dueDate = dueDate,
        )

        val response = handler.handleMessage(buildMessage(ddaRegister))
        response.shouldDeleteMessage.shouldBeTrue()

        val bills = billRepository.findByWallet(WalletId(WALLET_ID))
        bills.size shouldBe 1
        assertSoftly {
            slot.captured.body.get().pagamentos.codigoBarras shouldBe FICHA_DE_COMPENSACAO_BARCODE
            with(bills[0]) {
                this.barCode?.digitable shouldBe FICHA_DE_COMPENSACAO_DIGITABLE_LINE
                this.amount shouldBe expectedAmount
                this.dueDate shouldBe dueDate
                this.source shouldBe ActionSource.DDA(AccountId(ACCOUNT_ID))
                this.payerDocument shouldBe document
                this.payerAlias shouldBe payerAlias
            }
        }
    }

    private fun setupBillValidate(
        document: String = DOCUMENT,
        barCode: BarCode = BarCode.of("07798000000000000000001101000001331705421683"),
        dueDate: LocalDate = LocalDate.parse("2020-12-30", dateFormat),
        valorTitulo: String = "1759.24",
        valorTituloOriginal: String = valorTitulo,
        valorTotalCobrar: String = valorTitulo,
        valorTituloBaixaEfetiva: String? = null,
        razaoSocialSacadorAvalista: String = "BANCO INTER",
        situacaoTitulo: CIPSitTitPgto = CIPSitTitPgto.SUCESSO,
    ): CapturingSlot<HttpRequest<BillValidateRequestTO>> {
        val valorTituloBaixaEfetivaStr = if (valorTituloBaixaEfetiva == null) "null" else "\"$valorTituloBaixaEfetiva\""
        val resultado =
            "[{\"DDA0110R1\": {\"NumCtrlPart\":\"DDA20200526000343976\",\"ISPBPartRecbdrPrincipal\":\"54403563\",\"ISPBPartRecbdrAdmtd\":\"54403563\",\"NumCtrlDDA\":\"20200526000063419613\",\"NumIdentcTit\":\"2018122805315728701\",\"NumRefAtlCadTit\":\"1581911314149000216\",\"NumSeqAtlzCadTit\":\"32\",\"DtHrSitTit\":\"2020-02-17T00:48:34\",\"ISPBPartDestinatario\":\"00416968\",\"CodPartDestinatario\":\"077\",\"TpPessoaBenfcrioOr\":\"J\",\"CNPJ_CPFBenfcrioOr\":\"416968000101\",\"Nom_RzSocBenfcrioOr\":\"BANCO INTER\",\"NomFantsBenfcrioOr\":\"BRADESCO\",\"LogradBenfcrioOr\":\"AV CONTORNO 7777\",\"CidBenfcrioOr\":\"Belo Horizonte\",\"UFBenfcrioOr\":\"MG\",\"CEPBenfcrioOr\":\"30110051\",\"TpPessoaBenfcrioFinl\":\"J\",\"CNPJ_CPFBenfcrioFinl\":\"416968000101\",\"Nom_RzSocBenfcrioFinl\":\"BANCO INTER\",\"NomFantsBenfcrioFinl\":\"BANCO INTER\",\"TpPessoaPagdr\":\"F\"" +
                ",\"CNPJ_CPFPagdr\":\"$document\",\"Nom_RzSocPagdr\":\"FELIPE MENDES DE OLIVEIRA CASTRO        \",\"NomFantsPagdr\":\"\",\"TpIdentcSacdrAvalst\":\"\",\"IdentcSacdrAvalst\":\"\",\"Nom_RzSocSacdrAvalst\":\"\",\"CodMoedaCNAB\":\"09\"" +
                ",\"NumCodBarras\":\"${barCode.number}\"" +
                ",\"NumLinhaDigtvl\":\"${barCode.digitable}\"" +
                ",\"DtVencTit\":\"${dueDate.format(dateFormat)}\"" +
                ",\"VlrTit\":\"$valorTitulo\",\"CodEspTit\":\"99\",\"QtdDiaPrott\":\"\",\"DtLimPgtoTit\":\"2028-04-28\",\"IndrBloqPgto\":\"N\",\"IndrPgtoParcl\":\"S\",\"QtdPgtoParcl\":\"99\",\"VlrAbattTit\":\"0.00\",\"DtJurosTit\":\"\",\"CodJurosTit\":\"5\",\"Vlr_PercJurosTit\":\"0.00\",\"CodMultaTit\":\"3\",\"DtMultaTit\":\"\",\"Vlr_PercMultaTit\":\"0.00\",\"DtDesctTit\":\"\",\"CodDesctTit\":\"0\",\"Vlr_PercDesctTit\":\"0.00\",\"TpVlr_PercMinTit\":\"V\",\"Vlr_PercMinTit\":\"0.01\",\"TpVlr_PercMaxTit\":\"V\",\"Vlr_PercMaxTit\":\"9999999999.99\",\"TpModlCalc\":\"01\",\"TpAutcRecbtVlrDivgte\":\"1\",\"QtdPgtoParclRegtd\":\"2\",\"VlrSldTotAtlPgtoTit\":\"\",\"NumIdentcBaixaEft\":\"2020021600335586620\",\"NumRefAtlBaixaEft\":\"1581911313693000216\",\"NumSeqAtlzBaixaEft\":\"15\",\"DtProcBaixaEft\":\"2020-02-14\",\"DtHrProcBaixaEft\":\"2020-02-14T00:00:00\",\"VlrBaixaEftTit\":$valorTituloBaixaEfetivaStr,\"NumCodBarrasBaixaEft\":\"07798000000000000000001101000001331705421683\",\"CanPgto\":\"\",\"MeioPgto\":\"\",\"DtHrSitBaixaEft\":\"2020-02-17T00:48:33\",\"SitTitPgto\":\"${situacaoTitulo.code}\",\"DtHrDDA\":\"2020-05-26T17:02:06\",\"DtMovto\":\"2020-05-26\"}},{\"ValorCalculado\": " +
                "{\"valorTituloOriginal\":\"$valorTituloOriginal\"" +
                ",\"valorTotalCobrar\":\"$valorTotalCobrar\",\"valorDescontoCalculado\":\"0\",\"valorJurosCalculado\":\"0\",\"valorMultaCalculado\":\"0\",\"valorAbatimento\":\"0.00\",\"dataOperacao\":\"26/05/2020\"" +
                ", \"Nom_RzSocSacdrAvalst\": \"$razaoSocialSacadorAvalista\"}},{\"Emissor\":{\"ISPBPartRecbdr\":\"54403563\",\"Codigo\":\"213\",\"razaosocialemissor\":\"BANCO ARBI S.A.\"}}]"

        val slot = slot<HttpRequest<BillValidateRequestTO>>()

        every {
            mockHttpClient.retrieve(
                capture(slot),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.listOf(ArbiResponseTO::class.java),
            )
        } answers {
            Flowable.just(
                listOf(
                    ArbiResponseTO(
                        idTransacao = 8,
                        resultado = resultado,
                        idModulo = 2,
                        idRequisicaoArbi = "1252020183179483@2469098973966",
                        descricaoStatus = "sucesso",
                        idStatus = 201,
                        idRequisicaoParceiro = "2573821d-75e0-4fb5-80f2-5d81550cb1f8",
                    ),
                ),
            )
        }

        return slot
    }
}