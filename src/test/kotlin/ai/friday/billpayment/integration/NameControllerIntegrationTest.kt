package ai.friday.billpayment.integration

import ai.friday.billpayment.accountRegisterDataMissingAcceptedAt
import ai.friday.billpayment.accountRegisterDataWithPhoneVerified
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.BigDataService
import arrow.core.Either
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

@MicronautTest(environments = [FRIDAY_ENV])
class NameControllerIntegrationTest(embeddedServer: EmbeddedServer) {

    private val securityFixture = SecurityFixture()

    private val bigDataServiceMock: BigDataService = mockk(relaxed = true)

    private val accountRegisterRepositoryMock: AccountRegisterRepository = mockk(relaxed = true)

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @MockBean(AccountRegisterRepository::class)
    fun getAccountRegisterRepository(): AccountRegisterRepository = accountRegisterRepositoryMock

    @MockBean(BigDataService::class)
    fun getBigDataService(): BigDataService = bigDataServiceMock

    @ParameterizedTest
    @ValueSource(strings = ["**********", "**********11", "**********111", "**********11111", "**********1a"])
    fun `should return bad request when document is not valid`(document: String) {
        val request = HttpRequest.GET<Unit>("/name/$document")
            .cookie(securityFixture.cookieAuthOwner)
            .header("X-API-VERSION", "2")
        val exception = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Unit::class.java)
        }
        assertTrue(exception.status == HttpStatus.BAD_REQUEST)
    }

    @ParameterizedTest
    @ValueSource(strings = ["***********", "**************"])
    fun `should return ok when document is valid`(document: String) {
        every { bigDataServiceMock.getPersonName(document) } answers { Either.Right("test") }
        every { bigDataServiceMock.getCompanyName(document) } answers { Either.Right("test") }
        val request = HttpRequest.GET<Unit>("/name/$document")
            .cookie(securityFixture.cookieAuthOwner)
            .header("X-API-VERSION", "2")
        val response = client.toBlocking().exchange(request, Unit::class.java)
        assertTrue(response.status == HttpStatus.OK)
    }

    @Test
    fun `deve retornar o nome do proprio usuário`() {
        val accountRegisterData = accountRegisterDataMissingAcceptedAt.copy(document = Document(accountRegisterDataMissingAcceptedAt.documentInfo!!.cpf))
        every { accountRegisterRepositoryMock.findByAccountId(accountRegisterData.accountId) } answers { accountRegisterData }
        every { bigDataServiceMock.getPersonName(accountRegisterData.document!!.value) } answers { Either.Right("test") }
        val request = HttpRequest.GET<Unit>("/name")
            .cookie(securityFixture.cookieGuest)
            .header("X-API-VERSION", "2")
        val response = client.toBlocking().exchange(request, Map::class.java)
        response.status shouldBe HttpStatus.OK
        response.body()["name"] shouldBe "test"
    }

    @Test
    fun `deve retornar conflito se o usuário ainda não tiver documento e tentar retornar o proprio nome`() {
        every { accountRegisterRepositoryMock.findByAccountId(AccountId(ACCOUNT_ID)) } answers { accountRegisterDataWithPhoneVerified }
        val request = HttpRequest.GET<Unit>("/name")
            .cookie(securityFixture.cookieGuest)
            .header("X-API-VERSION", "2")
        assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, Unit::class.java) }
            .status shouldBe HttpStatus.NOT_FOUND
    }
}