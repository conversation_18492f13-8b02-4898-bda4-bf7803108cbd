package ai.friday.billpayment.integration

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.CreditCardChargebackTrackDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbTransactionRepository
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardBrand
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.cashIn.CreditCardCashIn
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.payment.MultiplePaymentData
import ai.friday.billpayment.app.payment.Payer
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.SinglePaymentData
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.toPayer
import ai.friday.billpayment.app.usage.CreditCardRiskUsage
import ai.friday.billpayment.app.usage.CreditCardUsage
import ai.friday.billpayment.app.usage.CreditCardUsageService
import ai.friday.billpayment.app.usage.TransactionAmount
import ai.friday.billpayment.app.usage.TransactionItemAmount
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.authorizedCreditCardAuthorization
import ai.friday.billpayment.balance
import ai.friday.billpayment.bankAccount
import ai.friday.billpayment.boletoSettlementResult
import ai.friday.billpayment.creditCard
import ai.friday.billpayment.creditCardCashInTransaction
import ai.friday.billpayment.defaultTransactionEntityConverter
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.unmockkAll
import java.time.LocalTime
import java.util.UUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class CreditCardUsageIntegrationTest {

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val enhancedClient = getDynamoDB()

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)
    private val billEventDAO = BillEventDynamoDAO(enhancedClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val walletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = accountRepository,
    )

    private val transactionDAO = TransactionDynamoDAO(enhancedClient)
    private val creditCardChargebackTrackDAO = CreditCardChargebackTrackDynamoDAO(enhancedClient)

    private val transactionRepository =
        DynamoDbTransactionRepository(
            transactionDAO = transactionDAO,
            creditCardChargebackTrackDAO = creditCardChargebackTrackDAO,
            accountRepository = accountRepository,
            transactionEntityConverter = defaultTransactionEntityConverter(
                billEventRepository = BillEventDBRepository(
                    billEventDAO = billEventDAO,
                    uniqueConstraintDAO = uniqueConstraintDAO,
                    featureConfiguration = allFalseFeatureConfiguration,
                    transactionDynamo = transactionDynamo,
                ),
                walletRepository = walletRepository,
                transactionDynamo = transactionDynamo,
            ),
        )

    private val featureConfiguration: FeatureConfiguration = mockk()

    private val mediumRiskQuota: Long = 500_00

    private val creditCardUsageService = CreditCardUsageService(
        transactionRepository = transactionRepository,
        accountService = AccountService(
            accountConfigurationService = mockk(),
            accountRepository = accountRepository,
            chatbotMessagePublisher = mockk(),
            crmService = mockk(),
            notificationAdapter = mockk(),
            walletRepository = walletRepository,
        ),
        walletService = WalletService(
            accountService = mockk(),
            walletRepository = walletRepository,
            configuration = mockk(),
            notificationAdapter = mockk(),
            eventPublisher = mockk(relaxUnitFun = true),
            crmService = mockk(),
            pixKeyRepository = mockk(),
        ),
        mediumRiskQuota = mediumRiskQuota,
        featureConfiguration = featureConfiguration,
        fee = 4.0,
    )

    private val walletFixture = WalletFixture()

    private val walletAsAssistant =
        walletFixture.buildWallet(
            accountPaymentMethodId = AccountPaymentMethodId(("AccountPaymentMethodId - ${UUID.randomUUID()}")),
            otherMembers = listOf(walletFixture.assistant),
        )

    private val walletAsFounder = walletFixture.buildWallet(
        id = WalletId("WALLET-${UUID.randomUUID()}"),
        walletFounder = walletFixture.assistant.copy(type = MemberType.FOUNDER),
        accountPaymentMethodId = AccountPaymentMethodId(UUID.randomUUID().toString()),
    )

    val billAdded = BillAdded(
        billId = BillId(BILL_ID),
        walletId = walletAsFounder.id,
        description = "",
        dueDate = getLocalDate(),
        amount = 0,
        billType = BillType.CONCESSIONARIA,
        amountTotal = 0,
        paymentLimitTime = LocalTime.MIDNIGHT,
        actionSource = ActionSource.Api(accountId = walletFixture.assistantAccount.accountId),
    )

    private val lowRiskCreditCard = creditCard.copy(
        id = AccountPaymentMethodId("LOW_RISK"),
        method = CreditCard(
            brand = CreditCardBrand.VISA,
            pan = "1234",
            expiryDate = "10/23",
            securityCode = null,
            holderName = null,
            token = null,
            riskLevel = RiskLevel.LOW,
            binDetails = null,
        ),
    )

    private val mediumRiskCreditCard = creditCard.copy(
        id = AccountPaymentMethodId("MEDIUM_RISK"),
        method = CreditCard(
            brand = CreditCardBrand.VISA,
            pan = "1234",
            expiryDate = "10/23",
            securityCode = null,
            holderName = null,
            token = null,
            riskLevel = RiskLevel.MEDIUM,
            binDetails = null,
        ),
    )

    private val deactivatedCreditCard = creditCard.copy(
        id = AccountPaymentMethodId("DEACTIVATED_RISK"),
        method = CreditCard(
            brand = CreditCardBrand.VISA,
            pan = "1234",
            expiryDate = "10/23",
            securityCode = null,
            holderName = null,
            token = null,
            riskLevel = RiskLevel.MEDIUM,
            binDetails = null,
        ),
        status = AccountPaymentMethodStatus.INACTIVE,
    )

    private val balanceMethod = balance.copy(
        id = AccountPaymentMethodId("BALANCE_PAYMENT"),
    )

    private val serviceAmountTax = 10L
    private val netAmount = 100L

    private val walletAsFounderCreditCardLowRiskTransaction = Transaction(
        id = TransactionId("123"),
        created = getZonedDateTime(),
        type = TransactionType.BOLETO_PAYMENT,
        payer = Payer(
            accountId = walletFixture.assistantAccount.accountId,
            document = DOCUMENT,
            name = "Fulano",
        ),
        paymentData = SinglePaymentData(
            accountPaymentMethod = lowRiskCreditCard,
            details = PaymentMethodsDetailWithCreditCard(
                paymentMethodId = lowRiskCreditCard.id,
                netAmount = netAmount,
                feeAmount = serviceAmountTax,
                installments = 1,
                calculationId = null,
                fee = 4.0,
            ),
            payment = authorizedCreditCardAuthorization,
        ),
        settlementData = SettlementData(
            settlementTarget = Bill.build(billAdded.copy(amount = netAmount, amountTotal = netAmount)),
            serviceAmountTax = 0,
            totalAmount = netAmount,
            settlementOperation = boletoSettlementResult,
        ),
        nsu = 1,
        actionSource = ActionSource.Api(accountId = walletFixture.assistantAccount.accountId),
        status = TransactionStatus.COMPLETED,
        walletId = walletAsFounder.id,
    )

    private val walletAsFounderCashInMediumRiskTransaction = creditCardCashInTransaction.copy(
        payer = walletFixture.assistantAccount.toPayer(),
        actionSource = ActionSource.Api(accountId = walletFixture.assistantAccount.accountId),
        walletId = walletAsFounder.id,
        paymentData = SinglePaymentData(
            accountPaymentMethod = mediumRiskCreditCard,
            details = PaymentMethodsDetailWithCreditCard(
                paymentMethodId = mediumRiskCreditCard.id,
                netAmount = netAmount,
                feeAmount = serviceAmountTax,
                installments = 1,
                calculationId = null,
                fee = 4.0,
            ),
            payment = authorizedCreditCardAuthorization,
        ),
        settlementData = SettlementData(
            settlementTarget = CreditCardCashIn(amount = netAmount, bankAccount = creditCard),
            serviceAmountTax = serviceAmountTax,
            totalAmount = netAmount + serviceAmountTax,
        ),
    )

    private val walletAsFounderCashInDeactivatedCardTransaction = creditCardCashInTransaction.copy(
        payer = walletFixture.assistantAccount.toPayer(),
        actionSource = ActionSource.Api(accountId = walletFixture.assistantAccount.accountId),
        walletId = walletAsFounder.id,
        paymentData = SinglePaymentData(
            accountPaymentMethod = deactivatedCreditCard,
            details = PaymentMethodsDetailWithCreditCard(
                paymentMethodId = deactivatedCreditCard.id,
                netAmount = netAmount,
                feeAmount = serviceAmountTax,
                installments = 1,
                calculationId = null,
                fee = 4.0,
            ),
            payment = authorizedCreditCardAuthorization,
        ),
        settlementData = SettlementData(
            settlementTarget = CreditCardCashIn(amount = netAmount, bankAccount = creditCard),
            serviceAmountTax = serviceAmountTax,
            totalAmount = netAmount + serviceAmountTax,
        ),
    )

    @BeforeEach
    fun setup() {
        unmockkAll()
        createBillPaymentTable(dynamoDB)

        every { featureConfiguration.creditCardQuota } returns true

        accountRepository.save(walletFixture.assistantAccount)
        accountRepository.createAccountPaymentMethod(
            accountId = walletFixture.assistantAccount.accountId,
            paymentMethodId = balanceMethod.id,
            bankAccount = bankAccount,
            position = 0,
            status = balanceMethod.status,
            mode = BankAccountMode.PHYSICAL,
        )
        accountRepository.createAccountPaymentMethod(
            accountId = walletFixture.assistantAccount.accountId,
            paymentMethodId = lowRiskCreditCard.id,
            creditCard = lowRiskCreditCard.method as CreditCard,
            position = 1,
            status = lowRiskCreditCard.status,
        )
        accountRepository.createAccountPaymentMethod(
            accountId = walletFixture.assistantAccount.accountId,
            paymentMethodId = mediumRiskCreditCard.id,
            creditCard = mediumRiskCreditCard.method as CreditCard,
            position = 2,
            status = mediumRiskCreditCard.status,
        )
        accountRepository.createAccountPaymentMethod(
            accountId = walletFixture.assistantAccount.accountId,
            paymentMethodId = deactivatedCreditCard.id,
            creditCard = deactivatedCreditCard.method as CreditCard,
            position = 3,
            status = deactivatedCreditCard.status,
        )

        walletRepository.save(walletAsFounder)
        walletRepository.save(walletAsAssistant)
    }

    @Test
    fun `should return 0 usage when there is no transaction`() {
        assertEquals(
            CreditCardUsage(
                mediumRiskQuota,
                0,
                400,
                mapOf(
                    RiskLevel.LOW to CreditCardRiskUsage(quota = 5_000_00, usage = 0),
                    RiskLevel.MEDIUM to CreditCardRiskUsage(
                        quota = 500_00,
                        usage = 0,
                    ),
                    RiskLevel.HIGH to CreditCardRiskUsage(
                        quota = 0,
                        usage = 0,
                    ),
                ),
            ),
            creditCardUsageService.calculateCreditCardUsage(walletFixture.assistantAccount.accountId),
            message = null,
        )
    }

    @Test
    fun `should return usage when a processing transaction exists`() {
        val transaction = walletAsFounderCreditCardLowRiskTransaction.copy(
            status = TransactionStatus.PROCESSING,
        )
        transactionRepository.save(transaction)
        assertEquals(
            CreditCardUsage(
                mediumRiskQuota,
                netAmount,
                400,
                mapOf(
                    RiskLevel.LOW to CreditCardRiskUsage(quota = 5_000_00, usage = netAmount),
                    RiskLevel.MEDIUM to CreditCardRiskUsage(quota = 500_00, usage = 0),
                    RiskLevel.HIGH to CreditCardRiskUsage(quota = 0, usage = 0),
                ),
            ),
            creditCardUsageService.calculateCreditCardUsage(walletFixture.assistantAccount.accountId),
        )
    }

    @Test
    fun `should return 0 usage when there is only failed transactions`() {
        val transaction = walletAsFounderCreditCardLowRiskTransaction.copy(
            status = TransactionStatus.FAILED,
        )
        transactionRepository.save(transaction)
        assertEquals(
            CreditCardUsage(
                mediumRiskQuota,
                0,
                400,
                mapOf(
                    RiskLevel.LOW to CreditCardRiskUsage(quota = 5_000_00, usage = 0),
                    RiskLevel.MEDIUM to CreditCardRiskUsage(quota = 500_00, usage = 0),
                    RiskLevel.HIGH to CreditCardRiskUsage(quota = 0, usage = 0),
                ),
            ),
            creditCardUsageService.calculateCreditCardUsage(walletFixture.assistantAccount.accountId),
        )
    }

    @Test
    fun `should return usage when there is a completed transaction`() {
        val transaction = walletAsFounderCreditCardLowRiskTransaction.copy(
            status = TransactionStatus.COMPLETED,
        )
        transactionRepository.save(transaction)
        assertEquals(
            CreditCardUsage(
                mediumRiskQuota,
                netAmount,
                400,
                mapOf(
                    RiskLevel.LOW to CreditCardRiskUsage(quota = 5_000_00, usage = netAmount),
                    RiskLevel.MEDIUM to CreditCardRiskUsage(quota = 500_00, usage = 0),
                    RiskLevel.HIGH to CreditCardRiskUsage(quota = 0, usage = 0),
                ),
            ),
            creditCardUsageService.calculateCreditCardUsage(walletFixture.assistantAccount.accountId),
        )
    }

    @Test
    fun `should return usage when there is a completed transaction with multiple payments`() {
        val lowRiskNetAmount = 80L
        val mediumRiskNetAmount = 20L
        val expectedAmount = lowRiskNetAmount + mediumRiskNetAmount
        val transaction = walletAsFounderCreditCardLowRiskTransaction.copy(
            status = TransactionStatus.COMPLETED,
            paymentData = MultiplePaymentData(
                payments =
                listOf(
                    SinglePaymentData(
                        accountPaymentMethod = lowRiskCreditCard,
                        details = PaymentMethodsDetailWithCreditCard(
                            paymentMethodId = lowRiskCreditCard.id,
                            netAmount = lowRiskNetAmount,
                            feeAmount = 3,
                            installments = 1,
                            calculationId = null,
                            fee = 4.0,
                        ),
                        payment = authorizedCreditCardAuthorization,
                    ),
                    SinglePaymentData(
                        accountPaymentMethod = mediumRiskCreditCard,
                        details = PaymentMethodsDetailWithCreditCard(
                            paymentMethodId = mediumRiskCreditCard.id,
                            netAmount = mediumRiskNetAmount,
                            feeAmount = 1,
                            installments = 1,
                            calculationId = null,
                            fee = 4.0,
                        ),
                        payment = authorizedCreditCardAuthorization,
                    ),
                ),
            ),
            settlementData = walletAsFounderCreditCardLowRiskTransaction.settlementData,
        )
        transactionRepository.save(transaction)
        assertEquals(
            CreditCardUsage(
                mediumRiskQuota,
                expectedAmount,
                400,
                mapOf(
                    RiskLevel.LOW to CreditCardRiskUsage(
                        quota = 5_000_00,
                        usage = lowRiskNetAmount + mediumRiskNetAmount,
                    ),
                    RiskLevel.MEDIUM to CreditCardRiskUsage(quota = 500_00, usage = mediumRiskNetAmount),
                    RiskLevel.HIGH to CreditCardRiskUsage(quota = 0, usage = 0),
                ),
            ),
            creditCardUsageService.calculateCreditCardUsage(walletFixture.assistantAccount.accountId),
        )
    }

    @Test
    fun `should return usage when there is a completed transaction with credit card and balance`() {
        val lowRiskNetAmount = 80L
        val balanceAmount = 20L
        val transaction = walletAsFounderCreditCardLowRiskTransaction.copy(
            status = TransactionStatus.COMPLETED,
            paymentData = MultiplePaymentData(
                payments =
                listOf(
                    SinglePaymentData(
                        accountPaymentMethod = lowRiskCreditCard,
                        details = PaymentMethodsDetailWithCreditCard(
                            paymentMethodId = lowRiskCreditCard.id,
                            netAmount = lowRiskNetAmount,
                            feeAmount = 8,
                            installments = 1,
                            calculationId = null,
                            fee = 4.0,
                        ),
                        payment = authorizedCreditCardAuthorization,
                    ),
                    SinglePaymentData(
                        accountPaymentMethod = balanceMethod,
                        details = PaymentMethodsDetailWithBalance(
                            paymentMethodId = mediumRiskCreditCard.id,
                            amount = balanceAmount,
                            calculationId = null,
                        ),
                        payment = authorizedCreditCardAuthorization,
                    ),
                ),
            ),
            settlementData = SettlementData(
                settlementTarget = Bill.build(billAdded.copy(amount = netAmount, amountTotal = netAmount)),
                serviceAmountTax = 0,
                totalAmount = netAmount,
                settlementOperation = boletoSettlementResult,
            ),
        )
        transactionRepository.save(transaction)
        assertEquals(
            CreditCardUsage(
                mediumRiskQuota,
                lowRiskNetAmount,
                400,
                mapOf(
                    RiskLevel.LOW to CreditCardRiskUsage(
                        quota = 5_000_00,
                        usage = lowRiskNetAmount,
                    ),
                    RiskLevel.MEDIUM to CreditCardRiskUsage(quota = 500_00, usage = 0),
                    RiskLevel.HIGH to CreditCardRiskUsage(quota = 0, usage = 0),
                ),
            ),
            creditCardUsageService.calculateCreditCardUsage(walletFixture.assistantAccount.accountId),
        )
    }

    @Test
    fun `should return usage sum`() {
        val totalAmount1 = 200L
        val totalAmount2 = 500L
        val mediumRiskTotalAmount = 600L
        val serviceAmountTax1 = 2L
        val serviceAmountTax2 = 5L
        val mediumRiskServiceAmountTax = 6L
        val now = getZonedDateTime().withDayOfMonth(5)

        val transaction = walletAsFounderCreditCardLowRiskTransaction.copy(
            status = TransactionStatus.COMPLETED,
            created = now,
            paymentData = SinglePaymentData(
                accountPaymentMethod = lowRiskCreditCard,
                details = PaymentMethodsDetailWithCreditCard(
                    paymentMethodId = lowRiskCreditCard.id,
                    netAmount = totalAmount1 - serviceAmountTax1,
                    feeAmount = serviceAmountTax1,
                    installments = 1,
                    calculationId = null,
                    fee = 4.0,
                ),
                payment = authorizedCreditCardAuthorization,
            ),
            settlementData = walletAsFounderCreditCardLowRiskTransaction.settlementData.copy(
                totalAmount = totalAmount1,
                serviceAmountTax = serviceAmountTax1,
            ),
        )
        transactionRepository.save(transaction)

        val transactionFromyesterday = walletAsFounderCreditCardLowRiskTransaction.copy(
            id = TransactionId("4564"),
            status = TransactionStatus.COMPLETED,
            created = now.minusDays(1),
            paymentData = SinglePaymentData(
                accountPaymentMethod = lowRiskCreditCard,
                details = PaymentMethodsDetailWithCreditCard(
                    paymentMethodId = lowRiskCreditCard.id,
                    netAmount = totalAmount2 - serviceAmountTax2,
                    feeAmount = serviceAmountTax2,
                    installments = 1,
                    calculationId = null,
                    fee = 4.0,
                ),
                payment = authorizedCreditCardAuthorization,
            ),
            settlementData = walletAsFounderCreditCardLowRiskTransaction.settlementData.copy(
                totalAmount = totalAmount2,
                serviceAmountTax = serviceAmountTax2,
            ),
            walletId = walletAsAssistant.id,
        )
        transactionRepository.save(transactionFromyesterday)

        val cashinTransaction = walletAsFounderCashInMediumRiskTransaction.copy(
            id = TransactionId("7895"),
            status = TransactionStatus.COMPLETED,
            created = now,
            paymentData = SinglePaymentData(
                accountPaymentMethod = mediumRiskCreditCard,
                details = PaymentMethodsDetailWithCreditCard(
                    paymentMethodId = mediumRiskCreditCard.id,
                    netAmount = mediumRiskTotalAmount - mediumRiskServiceAmountTax,
                    feeAmount = mediumRiskServiceAmountTax,
                    installments = 1,
                    calculationId = null,
                    fee = 4.0,
                ),
                payment = authorizedCreditCardAuthorization,
            ),
            settlementData = walletAsFounderCashInMediumRiskTransaction.settlementData.copy(
                totalAmount = mediumRiskTotalAmount,
                serviceAmountTax = mediumRiskServiceAmountTax,
            ),
            payer = walletFixture.assistantAccount.toPayer(),
        )
        transactionRepository.save(cashinTransaction)

        val anotherUserTransaction = walletAsFounderCashInMediumRiskTransaction.copy(
            id = TransactionId("7890"),
            status = TransactionStatus.COMPLETED,
            payer = walletFixture.founderAccount.toPayer(),
        )
        transactionRepository.save(anotherUserTransaction)

        val anotherUserLegacyTransaction = walletAsFounderCreditCardLowRiskTransaction.copy(
            id = TransactionId("0987"),
            status = TransactionStatus.COMPLETED,
            payer = walletFixture.founderAccount.toPayer(),
        )
        transactionRepository.save(anotherUserLegacyTransaction)

        val lowRiskExpectedAmount = totalAmount1 + totalAmount2 + mediumRiskTotalAmount
        val lowRiskServiceAmountTax = serviceAmountTax1 + serviceAmountTax2 + mediumRiskServiceAmountTax
        assertEquals(
            CreditCardUsage(
                mediumRiskQuota,
                lowRiskExpectedAmount - lowRiskServiceAmountTax,
                400,
                mapOf(
                    RiskLevel.LOW to CreditCardRiskUsage(
                        quota = 5_000_00,
                        usage = lowRiskExpectedAmount - lowRiskServiceAmountTax,
                    ),
                    RiskLevel.MEDIUM to CreditCardRiskUsage(
                        quota = 500_00,
                        usage = mediumRiskTotalAmount - mediumRiskServiceAmountTax,
                    ),
                    RiskLevel.HIGH to CreditCardRiskUsage(quota = 0, usage = 0),
                ),
            ),
            creditCardUsageService.calculateCreditCardUsage(walletFixture.assistantAccount.accountId),
        )

        assertEquals(
            TransactionAmount(
                mediumRiskTotalAmount + totalAmount1,
                mediumRiskServiceAmountTax + serviceAmountTax1,
                mapOf(
                    lowRiskCreditCard.id to
                        TransactionItemAmount(
                            paymentMethodId = lowRiskCreditCard.id,
                            totalAmount = totalAmount1,
                            feeAmount = serviceAmountTax1,
                        ),
                    mediumRiskCreditCard.id to
                        TransactionItemAmount(
                            paymentMethodId = mediumRiskCreditCard.id,
                            totalAmount = mediumRiskTotalAmount,
                            feeAmount = mediumRiskServiceAmountTax,
                        ),
                ),
            ),
            creditCardUsageService.getCreditCardUsageOnDate(
                walletFixture.assistantAccount.accountId,
                now.toLocalDate(),
            ),
        )
    }

    @Test
    fun `should return database quota for account with monthly usage`() {
        assertEquals(
            CreditCardUsage(
                mediumRiskQuota,
                0L,
                400,
                mapOf(
                    RiskLevel.LOW to CreditCardRiskUsage(quota = 5000_00, usage = 0),
                    RiskLevel.MEDIUM to CreditCardRiskUsage(
                        quota = 500_00,
                        usage = 0,
                    ),
                    RiskLevel.HIGH to CreditCardRiskUsage(quota = 0, usage = 0),
                ),
            ),
            creditCardUsageService.calculateCreditCardUsage(walletFixture.assistantAccount.accountId),
        )
    }

    @Test
    fun `não deve considerar cartões de risco médio não ativos no calculo para a quota`() {
        accountRepository.closeAccountPaymentMethod(mediumRiskCreditCard.id, walletFixture.assistantAccount.accountId)

        assertEquals(
            CreditCardUsage(
                walletFixture.assistantAccount.creditCardConfiguration.quota,
                0L,
                400,
                mapOf(
                    RiskLevel.LOW to CreditCardRiskUsage(quota = 5000_00, usage = 0),
                    RiskLevel.MEDIUM to CreditCardRiskUsage(
                        quota = 500_00,
                        usage = 0,
                    ),
                    RiskLevel.HIGH to CreditCardRiskUsage(quota = 0, usage = 0),
                ),
            ),
            creditCardUsageService.calculateCreditCardUsage(walletFixture.assistantAccount.accountId),
        )
    }

    @Test
    fun `should return usage with cash in total amount`() {
        val transactionFromLastMonth =
            walletAsFounderCashInMediumRiskTransaction.copy(
                id = TransactionId.build(),
                created = walletAsFounderCashInMediumRiskTransaction.created.minusMonths(1),
            )
        transactionRepository.save(walletAsFounderCashInMediumRiskTransaction)
        transactionRepository.save(transactionFromLastMonth)

        val usage = creditCardUsageService.calculateCreditCardUsage(walletFixture.assistantAccount.accountId)

        usage shouldBe CreditCardUsage(
            mediumRiskQuota,
            netAmount,
            400,
            mapOf(
                RiskLevel.LOW to CreditCardRiskUsage(
                    quota = 5_000_00,
                    usage = netAmount,
                ),
                RiskLevel.MEDIUM to CreditCardRiskUsage(
                    quota = 500_00,
                    usage = netAmount,
                ),
                RiskLevel.HIGH to CreditCardRiskUsage(quota = 0, usage = 0),
            ),
        )
    }

    @Test
    fun `deve considerar o valor transacionado em cartao que ja foi removido`() {
        transactionRepository.save(walletAsFounderCashInDeactivatedCardTransaction)

        val usage = creditCardUsageService.calculateCreditCardUsage(walletFixture.assistantAccount.accountId)

        usage shouldBe CreditCardUsage(
            mediumRiskQuota,
            netAmount,
            400,
            mapOf(
                RiskLevel.LOW to CreditCardRiskUsage(
                    quota = 5_000_00,
                    usage = netAmount,
                ),
                RiskLevel.MEDIUM to CreditCardRiskUsage(
                    quota = 500_00,
                    usage = netAmount,
                ),
                RiskLevel.HIGH to CreditCardRiskUsage(quota = 0, usage = 0),
            ),
        )
    }

    @Test
    fun `should return zero when there is no cash-in`() {
        val today = getZonedDateTime()
        val transactionFromToday =
            walletAsFounderCashInMediumRiskTransaction.copy(id = TransactionId.build(), created = today)
        val transactionFromLastTwoDays =
            walletAsFounderCashInMediumRiskTransaction.copy(id = TransactionId.build(), created = today.minusDays(2))
        transactionRepository.save(transactionFromToday)
        transactionRepository.save(transactionFromLastTwoDays)

        val cashInSum =
            creditCardUsageService.getCreditCardUsageOnDate(
                walletFixture.assistantAccount.accountId,
                today.toLocalDate().minusDays(1),
            )

        cashInSum.totalAmount shouldBe 0L
        cashInSum.feeAmount shouldBe 0L
        cashInSum.amounts.size shouldBe 0
    }

    @Test
    fun `should return total amount when there is a cash-in yesterday`() {
        val yesterday = getZonedDateTime().minusDays(1)
        val transactionFromLastMonth = walletAsFounderCashInMediumRiskTransaction.copy(created = yesterday)
        transactionRepository.save(transactionFromLastMonth)
        val anotherWalletTransaction =
            walletAsFounderCashInMediumRiskTransaction.copy(created = yesterday, walletId = walletAsAssistant.id)
        transactionRepository.save(anotherWalletTransaction)

        val cashInSum =
            creditCardUsageService.getCreditCardUsageOnDate(
                walletFixture.assistantAccount.accountId,
                yesterday.toLocalDate(),
            )

        val expectedTotalAmount =
            walletAsFounderCashInMediumRiskTransaction.settlementData.totalAmount + anotherWalletTransaction.settlementData.totalAmount
        val expectedFeeAmount =
            walletAsFounderCashInMediumRiskTransaction.settlementData.serviceAmountTax + anotherWalletTransaction.settlementData.serviceAmountTax

        cashInSum.totalAmount shouldBe expectedTotalAmount
        cashInSum.feeAmount shouldBe expectedFeeAmount
        cashInSum.netAmount shouldBe expectedTotalAmount - expectedFeeAmount
    }
}