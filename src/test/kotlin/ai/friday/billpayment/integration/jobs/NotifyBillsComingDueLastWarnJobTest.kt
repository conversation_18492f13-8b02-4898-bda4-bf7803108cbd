package ai.friday.billpayment.integration.jobs

import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.ChatbotType
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.chatbot.ChatbotNotificationService
import ai.friday.billpayment.app.integrations.BillService
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.notification.BillComingDueService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.BILL_ID_3
import ai.friday.billpayment.integration.BILL_ID_4
import ai.friday.billpayment.integration.BILL_ID_5
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.timeFormat
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.shouldBe
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalTime
import net.javacrumbs.shedlock.core.LockAssert
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class NotifyBillsComingDueLastWarnJobTest {

    private val notificationAdapter: NotificationAdapter = mockk(relaxed = true)
    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet(
        otherMembers = listOf(walletFixture.participant, walletFixture.cantPayParticipant),
    )
    private val anotherWallet = walletFixture.buildWallet(
        otherMembers = listOf(
            walletFixture.participant,
            walletFixture.ultraLimitedParticipant,
        ),
        id = WalletId("ANOTHER-WALLET-ID"),
    )
    private val findBillService: FindBillService = mockk() {
        every {
            findAllWallets(any(), any())
        } returns listOf(wallet, anotherWallet)
    }
    private val billService: BillService = mockk()
    private val walletService: WalletService = mockk {
        every { findWalletOrNull(any()) } returns null
        every { findWallet(wallet.id) } returns wallet
        every { findWallet(anotherWallet.id) } returns anotherWallet
    }
    private val messagePublisher: MessagePublisher = mockk()
    private val accountService: AccountService = mockk()

    private val chatBotNotificationService: ChatbotNotificationService = mockk()
    private val notifyBillComingDueService =
        BillComingDueService(
            findBillService = findBillService,
            billService = billService,
            walletService = walletService,
            notificationAdapter = notificationAdapter,
            notificationHintService = mockk() {
                every {
                    getBillComingDueLastWarn(any(), any())
                } returns "DICA FAKE"
            },
            accountService = accountService,
            onePixPayService = mockk(),
            scheduleBillService = mockk(),
            onePixPayLock = mockk(),
            messagePublisher = messagePublisher,
            chatBotNotificationService = chatBotNotificationService,
            billComingDueExecutionQueueName = "queueName",
            onePixPayInstrumentation = mockk(relaxed = true),
        )

    private val billComingDue = getActiveBill().copy(
        billId = BillId(BILL_ID_2),
        dueDate = getLocalDate().minusDays(1),
        effectiveDueDate = getLocalDate(),
        paymentLimitTime = LocalTime.parse("16:45", timeFormat),
        walletId = wallet.id,
    )
    private val billComingDue2 = getActiveBill().copy(
        billId = BillId(BILL_ID_3),
        dueDate = getLocalDate().minusDays(1),
        effectiveDueDate = getLocalDate(),
        paymentLimitTime = LocalTime.parse("16:30", timeFormat),
        walletId = wallet.id,
    )

    private val anotherUserBillComingDue = getActiveBill().copy(
        billId = BillId(BILL_ID_4),
        dueDate = getLocalDate().minusDays(1),
        effectiveDueDate = getLocalDate(),
        paymentLimitTime = LocalTime.parse("16:30", timeFormat),
        walletId = anotherWallet.id,
    )
    private val anotherUserBillComingDue2 = getActiveBill().copy(
        billId = BillId(BILL_ID_5),
        dueDate = getLocalDate().minusDays(1),
        effectiveDueDate = getLocalDate(),
        paymentLimitTime = LocalTime.parse("16:45", timeFormat),
        walletId = anotherWallet.id,
    )

    @BeforeEach
    fun setUp() {
        LockAssert.TestHelper.makeAllAssertsPass(true)
    }

    @Test
    fun `should not notify last bill coming due warn when there is no bill coming due`() {
        every { walletService.findWallet(any()) } returns wallet
        every { accountService.findAccountById(any()) } returns walletFixture.founderAccount
        every { findBillService.findBillsComingDue(any()) } returns emptyList()

        notifyBillComingDueService.executeLastWarn(wallet.id)

        verify {
            findBillService.findBillsComingDue(any())
            notificationAdapter wasNot called
        }
    }

    @Test
    fun `should notify last bill coming due warn when there is bill time to due on a wallet`() {
        every { walletService.findWallet(any()) } returns wallet
        withGivenDateTime(getZonedDateTime().withHour(14)) {
            every { findBillService.findBillsComingDue(any()) } returns listOf(billComingDue)
            every { accountService.findAccountById(any()) } returns walletFixture.founderAccount

            every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHATBOT_LEGACY

            notifyBillComingDueService.executeLastWarn(wallet.id)

            verify {
                findBillService.findBillsComingDue(any())
                notificationAdapter.notifyBillComingDueLastWarn(
                    members = listOf(walletFixture.founder, walletFixture.participant),
                    wallet = wallet,
                    dueDate = billComingDue.effectiveDueDate,
                    paymentLimitTime = billComingDue.paymentLimitTime,
                    hint = "DICA FAKE",
                    bills = any(),
                )
            }
        }
    }

    @Test
    fun `deve enviar para a fila de notificação do chatbot caso o usuário tenha o chatbot habilitado`() {
        every { walletService.findWallet(any()) } returns wallet
        withGivenDateTime(getZonedDateTime().withHour(14)) {
            every { findBillService.findBillsComingDue(any()) } returns listOf(billComingDue)
            every { accountService.findAccountById(any()) } returns walletFixture.founderAccount

            every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHABOT_AI

            notifyBillComingDueService.executeLastWarn(wallet.id)

            verify {
                chatBotNotificationService.notifyBillComingDue(
                    account = any(),
                    wallet = any(),
                    bills = any(),
                    lastWarn = any(),
                    hint = any(),
                )
            }
        }
    }

    @Test
    fun `should not notify last bill coming due warn when there is a bill hours ahead to due`() {
        withGivenDateTime(getZonedDateTime().withHour(14)) {
            every { findBillService.findBillsComingDue(any()) } returns listOf(billComingDue)

            notifyBillComingDueService.executeLastWarn(wallet.id)

            verify {
                findBillService.findBillsComingDue(any())
                notificationAdapter wasNot called
            }
        }
    }

    @Test
    fun `should not notify last bill coming due warn when there is a bill one hour overdue`() {
        withGivenDateTime(getZonedDateTime().withHour(16)) {
            every { walletService.findWallet(any()) } returns wallet
            every { accountService.findAccountById(any()) } returns walletFixture.founderAccount
            every { findBillService.findBillsComingDue(any()) } returns listOf(billComingDue)

            notifyBillComingDueService.executeLastWarn(wallet.id)

            verify {
                findBillService.findBillsComingDue(any())
                notificationAdapter wasNot called
            }
        }
    }

    @Test
    fun `should notify once per user when there are more than one bill using most recent time to due`() {
        every { findBillService.findBillsComingDue(wallet.id) } returns listOf(
            billComingDue,
            billComingDue2,
        )
        every { findBillService.findBillsComingDue(anotherWallet.id) } returns listOf(
            anotherUserBillComingDue,
            anotherUserBillComingDue2,
        )

        every { accountService.findAccountById(any()) } returns walletFixture.founderAccount
        every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHATBOT_LEGACY

        withGivenDateTime(getZonedDateTime().withHour(14)) {
            notifyBillComingDueService.executeLastWarn(wallet.id)
            notifyBillComingDueService.executeLastWarn(anotherWallet.id)
        }

        verify(exactly = 1) {
            findBillService.findBillsComingDue(wallet.id)
            findBillService.findBillsComingDue(anotherWallet.id)
            notificationAdapter.notifyBillComingDueLastWarn(
                members = listOf(wallet.founder, walletFixture.participant),
                wallet = withArg { it.id shouldBe billComingDue2.walletId },
                dueDate = billComingDue2.effectiveDueDate,
                paymentLimitTime = billComingDue2.paymentLimitTime,
                hint = "DICA FAKE",
                bills = any(),
            )

            notificationAdapter.notifyBillComingDueLastWarn(
                members = listOf(wallet.founder, walletFixture.participant),
                wallet = withArg { it.id shouldBe anotherUserBillComingDue.walletId },
                dueDate = anotherUserBillComingDue.effectiveDueDate,
                paymentLimitTime = anotherUserBillComingDue.paymentLimitTime,
                hint = "DICA FAKE",
                bills = any(),
            )
        }
    }
}