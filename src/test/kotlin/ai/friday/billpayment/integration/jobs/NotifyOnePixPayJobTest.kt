package ai.friday.billpayment.integration.jobs

import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.ChatbotType
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.bill.BatchSchedulingId
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.BillViewSchedule
import ai.friday.billpayment.app.bill.schedule.ScheduleBillService
import ai.friday.billpayment.app.integrations.BillService
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.notification.BillComingDueService
import ai.friday.billpayment.app.onepixpay.OnePixPay
import ai.friday.billpayment.app.onepixpay.OnePixPayErrors
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.onepixpay.OnePixPayStatus
import ai.friday.billpayment.app.payment.ScheduleTo
import ai.friday.billpayment.app.payment.ScheduledBill
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.createPaymentMethodsDetailWithBalance
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.getNotScheduledBillsEnoughToNotifyOnePixPay
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.BILL_ID_3
import ai.friday.billpayment.integration.BILL_ID_4
import ai.friday.billpayment.integration.WALLET_ID_2
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.withEarlyAccess
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.timeFormat
import ai.friday.morning.date.withGivenDateTime
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.Duration
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import net.javacrumbs.shedlock.core.LockAssert
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow

class NotifyOnePixPayJobTest {
    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet(otherMembers = listOf(walletFixture.participant))
    private val anotherWallet = walletFixture.buildWallet(id = WalletId(WALLET_ID_2), name = "carteira2")

    private val notificationAdapter: NotificationAdapter = mockk(relaxed = true)
    private val findBillService: FindBillService = mockk() {
        every {
            findAllWallets(any(), any())
        } returns listOf(wallet)

        every { findOverdueBills(any()) } returns emptyList()
    }
    private val scheduleBillService: ScheduleBillService = mockk() {
        every {
            getOrderedScheduledBills(any(), any())
        } returns emptyList()
    }
    private val walletService: WalletService = mockk {
        every {
            findWallet(wallet.id)
        } returns wallet
        every {
            findWallet(anotherWallet.id)
        } returns anotherWallet
    }

    private val accountService: AccountService = mockk() {
        every { findAccountById(any()) } returns walletFixture.founderAccount
        every { getChatbotType(any<Account>()) } returns ChatbotType.CHATBOT_LEGACY
    }

    private val billService: BillService = mockk(relaxed = true) {
        every { getPaidBills(any(), any(), any()) } returns listOf()
    }

    private val onePixPayLock = mockk<InternalLock> {
        every {
            acquireLock(any())
        } returns mockk(relaxed = true)
    }

    private val onePixPayService = mockk<OnePixPayService> {
        every { create(any(), any()) } answers {
            val bills = firstArg<List<BillView>>()

            OnePixPay(
                walletId = bills.first().walletId,
                billIds = bills.map { it.billId },
                qrCode = "valor-do-qr-code",
                status = OnePixPayStatus.CREATED,
                paymentLimitTime = LocalTime.NOON,
                fundsReceived = false,
            ).right()
        }
    }

    private val messagePublisher: MessagePublisher = mockk()
    private val notifyOnePixPayJob =
        BillComingDueService(
            findBillService = findBillService,
            billService = billService,
            walletService = walletService,
            notificationAdapter = notificationAdapter,
            notificationHintService = mockk(relaxed = true),
            onePixPayLock = onePixPayLock,
            accountService = accountService,
            onePixPayService = onePixPayService,
            onePixPayInstrumentation = mockk(relaxed = true),
            scheduleBillService = scheduleBillService,
            messagePublisher = messagePublisher,
            chatBotNotificationService = mockk(),
            billComingDueExecutionQueueName = "queueName",
        )

    private val noon = ZonedDateTime.of(LocalDate.of(2022, 10, 10), LocalTime.NOON, brazilTimeZone)

    @BeforeEach
    fun setUp() {
        LockAssert.TestHelper.makeAllAssertsPass(true)
    }

    @Test
    fun `nao deve notificar se nao houver contas para vencer no dia`() {
        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns listOf()

        withGivenDateTime(noon) {
            notifyOnePixPayJob.executeRegular(wallet.id)
        }

        verify {
            notificationAdapter wasNot Called
        }
    }

    @Test
    fun `nao deve notificar se houver alguma conta agendada para o dia`() {
        val bills = withGivenDateTime(noon) {
            getNotScheduledBillsEnoughToNotifyOnePixPay(wallet) + listOf(
                getActiveBill(
                    walletId = wallet.id,
                    accountId = wallet.founder.accountId,
                    schedule = BillViewSchedule(
                        date = getLocalDate(),
                        waitingFunds = false,
                        waitingRetry = false,
                    ),
                ),
            )
        }

        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns bills

        withGivenDateTime(noon) {
            notifyOnePixPayJob.executeRegular(wallet.id)
        }

        verify(exactly = 0) {
            notificationAdapter.notifyOnePixPay(any(), any(), any(), any(), any())
        }
    }

    @Test
    fun `nao deve notificar se houver alguma conta vencida e agendada para o dia`() {
        val bills = withGivenDateTime(noon) {
            getNotScheduledBillsEnoughToNotifyOnePixPay(wallet)
        }

        val today = noon.toLocalDate()

        every {
            scheduleBillService.getOrderedScheduledBills(wallet.id, today)
        } returns listOf(
            ScheduledBill(
                walletId = wallet.id,
                billId = BillId(BILL_ID_2),
                scheduledDate = today,
                billType = BillType.PIX,
                amount = 30,
                scheduleTo = ScheduleTo.DUE_DATE,
                paymentLimitTime = LocalTime.MIDNIGHT,
                paymentMethodsDetail = createPaymentMethodsDetailWithBalance(wallet.paymentMethodId, 30),
                expires = true,
                batchSchedulingId = BatchSchedulingId(),
                isSelfTransfer = false,
            ),
        )

        every {
            findBillService.find(wallet.id, BillId(BILL_ID_2))
        } returns getActiveBill(
            billId = BillId(BILL_ID_2),
            schedule = BillViewSchedule(date = today, waitingFunds = false, waitingRetry = false),
            subscriptionFee = false,
            dueDate = today.minusDays(2),
            effectiveDueDate = today.minusDays(2),
        )

        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns bills

        withGivenDateTime(noon) {
            notifyOnePixPayJob.executeRegular(wallet.id)
        }

        verify(exactly = 0) {
            notificationAdapter.notifyOnePixPay(any(), any(), any(), any(), any())
        }
    }

    @Test
    fun `deve notificar se houver apenas uma conta em aberto para o dia`() {
        val bills = withGivenDateTime(noon) {
            listOf(
                getActiveBill(
                    walletId = wallet.id,
                    accountId = wallet.founder.accountId,
                ).copy(
                    billId = BillId(BILL_ID_2),
                    dueDate = getLocalDate(),
                    effectiveDueDate = getLocalDate(),
                    paymentLimitTime = LocalTime.parse("18:00", timeFormat),
                ),
            )
        }

        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns bills

        withGivenDateTime(noon) {
            notifyOnePixPayJob.executeRegular(wallet.id)
        }

        verify(exactly = 1) {
            notificationAdapter.notifyOnePixPay(any(), any(), any(), any(), any())
        }
    }

    @Test
    fun `deve notificar se for uma conta básica e não ultrapassar o limite`() {
        val bills = withGivenDateTime(noon) {
            listOf(
                getActiveBill(
                    walletId = wallet.id,
                    accountId = wallet.founder.accountId,
                ).copy(
                    billId = BillId(BILL_ID_2),
                    dueDate = getLocalDate(),
                    effectiveDueDate = getLocalDate(),
                    paymentLimitTime = LocalTime.parse("18:00", timeFormat),
                ),
            )
        }

        every { accountService.findAccountById(any()) } returns walletFixture.founderAccount.copy(type = UserAccountType.BASIC_ACCOUNT)

        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns bills

        withGivenDateTime(noon) {
            notifyOnePixPayJob.executeRegular(wallet.id)
        }

        verify(exactly = 1) {
            notificationAdapter.notifyOnePixPay(any(), any(), any(), any(), any(), false)
        }
    }

    @Test
    fun `deve notificar se for uma conta básica e ultrapassar o limite`() {
        val bills = withGivenDateTime(noon) {
            listOf(
                getActiveBill(
                    walletId = wallet.id,
                    accountId = wallet.founder.accountId,
                ).copy(
                    billId = BillId(BILL_ID_2),
                    dueDate = getLocalDate(),
                    effectiveDueDate = getLocalDate(),
                    paymentLimitTime = LocalTime.parse("18:00", timeFormat),
                    amount = 100_000_00,
                    amountTotal = 100_000_00,
                ),
            )
        }

        every { accountService.findAccountById(any()) } returns walletFixture.founderAccount.copy(type = UserAccountType.BASIC_ACCOUNT)

        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns bills

        withGivenDateTime(noon) {
            notifyOnePixPayJob.executeRegular(wallet.id)
        }

        verify(exactly = 1) {
            notificationAdapter.notifyOnePixPay(any(), any(), any(), any(), any(), true)
        }
    }

    @Test
    fun `não deve falhar a job de notificação de 1-pix pay se algo jogar uma exceção`() {
        val bills = withGivenDateTime(noon) {
            listOf(
                getActiveBill(
                    walletId = wallet.id,
                    accountId = wallet.founder.accountId,
                ).copy(
                    billId = BillId(BILL_ID_2),
                    dueDate = getLocalDate(),
                    effectiveDueDate = getLocalDate(),
                    paymentLimitTime = LocalTime.parse("18:00", timeFormat),
                ),
            )
        }

        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns bills

        every {
            onePixPayService.create(any(), any())
        } returns OnePixPayErrors.AtLeastOneBillRequired.left()

        assertDoesNotThrow {
            withGivenDateTime(noon) {
                notifyOnePixPayJob.executeRegular(wallet.id)
            }
        }
    }

    @Test
    fun `deve notificar se houver uma conta em aberta para o dia e uma conta de assinatura enviando a conta de assinatura para o one pix pay`() {
        val bills = withGivenDateTime(noon) {
            listOf(
                getActiveBill(
                    walletId = wallet.id,
                    accountId = wallet.founder.accountId,
                ).copy(
                    billId = BillId(BILL_ID_2),
                    dueDate = getLocalDate(),
                    effectiveDueDate = getLocalDate(),
                    paymentLimitTime = LocalTime.parse("18:00", timeFormat),
                ),
                getActiveBill(
                    walletId = wallet.id,
                    accountId = wallet.founder.accountId,
                    schedule = BillViewSchedule(
                        date = getLocalDate(),
                        waitingFunds = false,
                        waitingRetry = false,
                    ),
                    subscriptionFee = true,
                ).copy(
                    billId = BillId(BILL_ID_3),
                ),
            )
        }

        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns bills

        withGivenDateTime(noon) {
            notifyOnePixPayJob.executeRegular(wallet.id)
        }

        val slot = slot<List<BillView>>()
        verify(exactly = 1) {
            notificationAdapter.notifyOnePixPay(
                any(),
                any(),
                any(),
                capture(slot),
                any(),
            )
        }

        with(slot.captured) {
            size shouldBe 2
            map { it.billId.value } shouldContainExactlyInAnyOrder listOf(BILL_ID_2, BILL_ID_3)
        }
    }

    @Test
    fun `não deve notificar se houver somente assinatura para o dia`() {
        val bills = withGivenDateTime(noon) {
            listOf(
                getActiveBill(
                    walletId = wallet.id,
                    accountId = wallet.founder.accountId,
                    schedule = BillViewSchedule(
                        date = getLocalDate(),
                        waitingFunds = false,
                        waitingRetry = false,
                    ),
                    subscriptionFee = true,
                ).copy(
                    billId = BillId(BILL_ID_3),
                ),
            )
        }

        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns bills

        withGivenDateTime(noon) {
            notifyOnePixPayJob.executeRegular(wallet.id)
        }

        verify(exactly = 0) {
            notificationAdapter.notifyOnePixPay(
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        }
    }

    @Test
    fun `deve notificar se houver uma conta em aberto e adicionar assinaturas vencidas`() {
        val today = noon.toLocalDate()
        val yesterday = today.minusDays(1)
        val bills = withGivenDateTime(noon) {
            listOf(
                getActiveBill(
                    walletId = wallet.id,
                    accountId = wallet.founder.accountId,
                ).copy(
                    billId = BillId(BILL_ID_3),
                ),
            )
        }

        every {
            scheduleBillService.getOrderedScheduledBills(wallet.id, today)
        } returns listOf(
            ScheduledBill(
                walletId = wallet.id,
                billId = BillId(BILL_ID_2),
                scheduledDate = yesterday,
                billType = BillType.PIX,
                amount = 30,
                scheduleTo = ScheduleTo.DUE_DATE,
                paymentLimitTime = LocalTime.MIDNIGHT,
                paymentMethodsDetail = createPaymentMethodsDetailWithBalance(wallet.paymentMethodId, 30),
                expires = false,
                batchSchedulingId = BatchSchedulingId(),
                isSelfTransfer = false,
            ),
        )

        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns bills

        every {
            findBillService.find(wallet.id, BillId(BILL_ID_2))
        } returns getActiveBill(
            billId = BillId(BILL_ID_2),
            schedule = BillViewSchedule(date = yesterday, waitingFunds = false, waitingRetry = false),
            subscriptionFee = true,
            dueDate = yesterday,
            effectiveDueDate = yesterday,
        )

        withGivenDateTime(noon) {
            notifyOnePixPayJob.executeRegular(wallet.id)
        }

        val slot = slot<List<BillView>>()
        verify(exactly = 1) {
            notificationAdapter.notifyOnePixPay(
                any(),
                any(),
                any(),
                capture(slot),
                any(),
            )
        }

        with(slot.captured) {
            // size shouldBe 2
            map { it.billId.value } shouldContainExactlyInAnyOrder listOf(BILL_ID_2, BILL_ID_3)
        }
    }

    @Test
    fun `deve notificar se houver mais de uma conta em aberto para o dia`() {
        val bills = withGivenDateTime(noon) { getNotScheduledBillsEnoughToNotifyOnePixPay(wallet) }

        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns bills

        withGivenDateTime(noon) {
            withEarlyAccess(accountId = wallet.founder.accountId) {
                notifyOnePixPayJob.executeRegular(wallet.id)
            }
        }

        verify(exactly = 1) {
            notificationAdapter.notifyOnePixPay(
                members = any(),
                walletName = wallet.name,
                onePixPayId = any(),
                delay = any(),
                bills = bills,
            )
        }
    }

    @Test
    fun `deve notificar somente o dono da carteira`() {
        val bills = withGivenDateTime(noon) { getNotScheduledBillsEnoughToNotifyOnePixPay(wallet) }

        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns bills

        withGivenDateTime(noon) {
            withEarlyAccess(accountId = wallet.founder.accountId) {
                notifyOnePixPayJob.executeRegular(wallet.id)
            }
        }

        verify(exactly = 1) {
            notificationAdapter.notifyOnePixPay(
                members = listOf(wallet.founder),
                walletName = any(),
                onePixPayId = any(),
                delay = any(),
                bills = any(),
            )
        }
    }

    @Test
    fun `deve notificar apenas para as carteiras com conta em aberto vencendo hoje`() {
        every {
            findBillService.findAllWallets(any(), any())
        } returns listOf(wallet, anotherWallet)

        val walletBills = withGivenDateTime(noon) {
            getNotScheduledBillsEnoughToNotifyOnePixPay(wallet)
        }

        val anotherWalletBills = withGivenDateTime(noon) {
            listOf(
                getActiveBill(
                    walletId = anotherWallet.id,
                    accountId = anotherWallet.founder.accountId,
                ).copy(
                    billId = BillId(BILL_ID_4),
                    dueDate = getLocalDate(),
                    effectiveDueDate = getLocalDate(),
                    paymentLimitTime = LocalTime.parse("18:00", timeFormat),
                    schedule = BillViewSchedule(
                        date = getLocalDate(),
                        waitingFunds = false,
                        waitingRetry = false,
                    ),
                ),
            )
        }

        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns walletBills

        every {
            findBillService.findBillsComingDue(anotherWallet.id)
        } returns anotherWalletBills

        every { findBillService.findOverdueBills(any()) } returns emptyList()

        withGivenDateTime(noon) {
            notifyOnePixPayJob.executeRegular(wallet.id)
            notifyOnePixPayJob.executeRegular(anotherWallet.id)
        }

        verify(exactly = 1) {
            notificationAdapter.notifyOnePixPay(any(), wallet.name, any(), any(), any())
        }

        verify(exactly = 0) {
            notificationAdapter.notifyOnePixPay(any(), anotherWallet.name, any(), any(), any())
        }
    }

    @Test
    fun `deve notificar com um atraso de 60 segundos`() {
        val bills = withGivenDateTime(noon) { getNotScheduledBillsEnoughToNotifyOnePixPay(wallet) }

        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns bills

        withGivenDateTime(noon) {
            withEarlyAccess(accountId = wallet.founder.accountId) {
                notifyOnePixPayJob.executeRegular(wallet.id)
            }
        }

        verify(exactly = 1) {
            notificationAdapter.notifyOnePixPay(
                members = any(),
                walletName = any(),
                onePixPayId = any(),
                delay = Duration.ofSeconds(60),
                bills = any(),
            )
        }
    }

    @Test
    fun `nao deve notificar mais de uma vez no dia para a mesma carteira`() {
        every {
            onePixPayLock.acquireLock(any())
        } returns mockk(relaxed = true) andThen null

        val bills = withGivenDateTime(noon) { getNotScheduledBillsEnoughToNotifyOnePixPay(wallet) }

        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns bills

        withGivenDateTime(noon) {
            notifyOnePixPayJob.executeRegular(wallet.id)
            notifyOnePixPayJob.executeRegular(wallet.id)
        }

        verify(exactly = 1) {
            notificationAdapter.notifyOnePixPay(any(), wallet.name, any(), any(), any())
        }
    }

    @Test
    fun `nao deve considerar os boletos vencidos`() {
        val enoughBillsToNotify = withGivenDateTime(noon) { getNotScheduledBillsEnoughToNotifyOnePixPay(wallet) }

        val bills = withGivenDateTime(noon) {
            enoughBillsToNotify + listOf(
                getActiveBill(
                    walletId = wallet.id,
                    accountId = wallet.founder.accountId,
                ).copy(
                    billId = BillId(BILL_ID_4),
                    dueDate = getLocalDate(),
                    effectiveDueDate = getLocalDate(),
                    paymentLimitTime = LocalTime.parse("05:00", timeFormat),
                ),
            )
        }

        every {
            findBillService.findBillsComingDue(wallet.id)
        } returns bills

        withGivenDateTime(noon) {
            notifyOnePixPayJob.executeRegular(wallet.id)
        }

        verify(exactly = 1) {
            notificationAdapter.notifyOnePixPay(
                members = any(),
                walletName = wallet.name,
                onePixPayId = any(),
                delay = any(),
                bills = enoughBillsToNotify,
            )
        }
    }
}