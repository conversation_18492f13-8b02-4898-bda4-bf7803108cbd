package ai.friday.billpayment.integration.jobs

import ai.friday.billpayment.adapters.jobs.DDARegisterJob
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.dda.CreateDDARegister
import ai.friday.billpayment.app.dda.DDAConfig
import ai.friday.billpayment.app.dda.DDAProvider
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.integrations.DDARepository
import ai.friday.billpayment.withUnlockedLock
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.junit.jupiter.api.Test

class DDARegisterJobTest {

    private val ddaRepository: DDARepository = mockk {
        every {
            findByStatus(any())
        } returns emptyList()
    }

    private val ddaConfig = DDAConfig(
        maxAddUser = 0,
        maxAddUserAsync = 0,
        provider = DDAProvider.ARBI,
        activateWaitMinutes = 0,
    )

    private val createDDARegister = CreateDDARegister(
        ddaRepository = ddaRepository,
        ddaConfig = ddaConfig,
    )

    private val ddaService: DDAService = spyk(
        DDAService(
            ddaProviderService = mockk(),
            accountRepository = mockk(),
            billValidationService = mockk(),
            findBillService = mockk(),
            updateBillService = mockk(),
            ddaRepository = ddaRepository,
            fullDDAPostProcessor = mockk(),
            messagePublisher = mockk(),
            ddaConfig = ddaConfig,
            createDDARegister = createDDARegister,
            featureConfiguration = allFalseFeatureConfiguration,
            fichaCompensacaoService = mockk(),
            walletService = mockk(),
            openFinanceIncentiveService = mockk(relaxed = true),
            ddaFullImportQueueName = "queue",
        ),
    )

    private val ddaRegisterJob = DDARegisterJob(
        ddaService = ddaService,
        featureConfiguration = allFalseFeatureConfiguration,
        cron = "",
        featuresRepository = mockk() {
            every {
                getAll().maintenanceMode
            } returns false
        },
    )

    @Test
    fun `should not throw exception if list is empty`() {
        withUnlockedLock {
            ddaRegisterJob.execute()
        }

        verify(exactly = 0) {
            ddaService.register(any(), any())
        }
    }
}