package ai.friday.billpayment.integration.jobs

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.ChatbotType
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillViewSchedule
import ai.friday.billpayment.app.bill.DefaultBillService
import ai.friday.billpayment.app.bill.DefaultFindBillService
import ai.friday.billpayment.app.bill.schedule.ScheduleBillService
import ai.friday.billpayment.app.chatbot.ChatbotNotificationService
import ai.friday.billpayment.app.integrations.BillService
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.OnePixPayInstrumentation
import ai.friday.billpayment.app.message.QueueMessageBatch
import ai.friday.billpayment.app.notification.BillComingDueService
import ai.friday.billpayment.app.notification.NotificationHintService
import ai.friday.billpayment.app.notification.NotifyBillComingDueMessage
import ai.friday.billpayment.app.notification.NotifyBillComingDueType
import ai.friday.billpayment.app.onepixpay.OnePixPay
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.getInvoiceBill
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.BILL_ID_3
import ai.friday.billpayment.integration.BILL_ID_4
import ai.friday.billpayment.integration.BILL_ID_5
import ai.friday.billpayment.integration.BILL_ID_6
import ai.friday.billpayment.integration.BILL_ID_7
import ai.friday.billpayment.integration.CNPJ_2
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.addBillIntoDb
import ai.friday.billpayment.integration.cleanupDb
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadUserIntoDb
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.timeFormat
import ai.friday.morning.date.withGivenDateTime
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.collections.shouldNotContain
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalTime
import java.time.ZonedDateTime
import net.javacrumbs.shedlock.core.LockAssert
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class NotifyBillsComingDueJobIntegrationTest {

    private val dynamoDB: AmazonDynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val walletService: WalletService = mockk {
        every { findWalletOrNull(any()) } returns null
    }

    private val notificationAdapter: NotificationAdapter = mockk(relaxed = true)

    private val dynamoClient = getDynamoDB()

    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(dynamoClient),
        refundedClient = RefundedBillDynamoDAO(dynamoClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(dynamoClient),
    )

    private val accountDAO = AccountDynamoDAO(dynamoClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(dynamoClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(dynamoClient)
    private val nsuDAO = NSUDynamoDAO(dynamoClient)
    private val transactionDynamo = TransactionDynamo(dynamoClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val walletDAO = WalletDynamoDAO(dynamoClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(dynamoClient)
    private val inviteDAO = InviteDynamoDAO(dynamoClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(dynamoClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(dynamoClient)

    private val walletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = accountRepository,
    )

    private val findBillService: FindBillService = DefaultFindBillService(
        accountRepository = accountRepository,
        walletService = walletService,
        repository = billRepository,
        billsComingDueRepository = mockk(),
    )

    private val billService: BillService = DefaultBillService(
        repository = billRepository,
        mailBoxService = mockk(),
        accountRepository = mockk(),
    )

    private val notificationHintService: NotificationHintService = mockk {
        every { getBillComingDue(any(), any()) } returns "Dica 1"
    }

    private val scheduleBillService: ScheduleBillService = mockk {
        every {
            getOrderedScheduledBills(any(), any())
        } returns emptyList()
    }

    private val accountService: AccountService = mockk() {
        every {
            getChatbotType(any<Account>())
        } returns ChatbotType.CHATBOT_LEGACY
    }
    private val onePixPayService: OnePixPayService = mockk()
    private val onePixPayInstrumentation = mockk<OnePixPayInstrumentation>(relaxed = true)

    private val messagePublisher: MessagePublisher = mockk()
    private val chatBotNotificationService: ChatbotNotificationService = mockk()

    private val notifyBillComingDueService =
        BillComingDueService(
            findBillService = findBillService,
            billService = billService,
            walletService = walletService,
            notificationAdapter = notificationAdapter,
            notificationHintService = notificationHintService,
            accountService = accountService,
            onePixPayService = onePixPayService,
            onePixPayInstrumentation = onePixPayInstrumentation,
            scheduleBillService = scheduleBillService,
            onePixPayLock = mockk(relaxed = true),
            messagePublisher = messagePublisher,
            chatBotNotificationService = chatBotNotificationService,
            billComingDueExecutionQueueName = "queueName",
        ).apply { filterAccountsThatReceiveNotification = true }

    private val billComingDue = getActiveBill().copy(
        billId = BillId(BILL_ID_2),
        dueDate = getLocalDate().minusDays(1),
        effectiveDueDate = getLocalDate(),
        paymentLimitTime = LocalTime.parse("18:00", timeFormat),
    )
    private val billComingDue2 = getActiveBill().copy(
        billId = BillId(BILL_ID_3),
        dueDate = getLocalDate().minusDays(1),
        effectiveDueDate = getLocalDate(),
        paymentLimitTime = LocalTime.parse("18:00", timeFormat),
    )

    private val billMessageAlreadySent = getActiveBill().copy(
        billId = BillId(BILL_ID_4),
        dueDate = getLocalDate().minusDays(1),
        effectiveDueDate = getLocalDate(),
        paymentLimitTime = LocalTime.parse("18:00", timeFormat),
        isBillComingDueMessageSent = true,
    )

    private val scheduledBill = getActiveBill().copy(
        billId = BillId(BILL_ID_5),
        dueDate = getLocalDate().minusDays(1),
        effectiveDueDate = getLocalDate(),
        schedule = BillViewSchedule(date = getLocalDate()),
        paymentLimitTime = LocalTime.parse("18:00", timeFormat),
        isBillComingDueMessageSent = false,
    )

    private val overdueFichaDeCompensacao = getActiveBill().copy(
        billId = BillId(BILL_ID_6),
        dueDate = getLocalDate().minusDays(1),
        effectiveDueDate = getLocalDate(),
        paymentLimitTime = LocalTime.MIDNIGHT,
    )

    private val overdueInvoice = getInvoiceBill().copy(
        billId = BillId(BILL_ID_7),
        dueDate = getLocalDate().minusDays(1),
        effectiveDueDate = getLocalDate(),
        paymentLimitTime = LocalTime.MIDNIGHT,
    )

    @BeforeEach
    fun setUp() {
        LockAssert.TestHelper.makeAllAssertsPass(true)
        createBillPaymentTable(dynamoDB)
        loadUserIntoDb(dynamoDB)
    }

    @AfterEach
    fun tearDown() {
        cleanupDb(dynamoDB)
    }

    @Test
    fun `should not send message to bills twice neither send to scheduled bills or overdue boletos`() {
        val walletFixture = WalletFixture()
        val wallet = walletFixture.buildWallet(
            walletFounder = walletFixture.founder.copy(accountId = AccountId(ACCOUNT_ID)),
            otherMembers = listOf(
                walletFixture.participant,
                walletFixture.ultraLimitedParticipant,
            ),
        )
        every {
            accountService.findAccountById(wallet.founder.accountId)
        } returns walletFixture.founderAccount.copy(accountId = AccountId(ACCOUNT_ID))

        walletRepository.save(wallet)
        every { walletService.findWallet(any()) } returns wallet

        withGivenDateTime(ZonedDateTime.of(getLocalDate(), LocalTime.of(13, 0), brazilTimeZone)) {
            val billsToNotNotify = listOf(scheduledBill, billMessageAlreadySent, overdueFichaDeCompensacao)
            val billsToNotify = listOf(billComingDue, billComingDue2, overdueInvoice)

            billsToNotify.plus(billsToNotNotify).forEach { addBillIntoDb(dynamoClient, it.copy(walletId = wallet.id)) }

            notifyBillComingDueService.executeRegular(wallet.id)

            verify(exactly = billsToNotify.size) {
                notificationAdapter.notifyBillComingDue(
                    members = any(),
                    author = any(),
                    walletName = any(),
                    walletId = any(),
                    billView = any(),
                )
            }
            billsToNotify.forEach {
                val bill = billRepository.findBill(it.billId, wallet.id)
                assertTrue(bill.isBillComingDueMessageSent)
            }
        }
    }

    @Test
    fun `should notify bill coming due on wallet`() {
        val walletFixture = WalletFixture()
        val wallet = walletFixture.buildWallet(
            walletFounder = walletFixture.founder.copy(accountId = AccountId(ACCOUNT_ID)),
            otherMembers = listOf(
                walletFixture.participant,
                walletFixture.ultraLimitedParticipant,
            ),
        )
        walletRepository.save(wallet)
        every { walletService.findWallet(wallet.id) } returns wallet
        every { accountService.findAccountById(wallet.founder.accountId) } returns walletFixture.founderAccount
        every { onePixPayService.create(any(), any()) } returns mockk<OnePixPay>(relaxed = true).right()

        val billComingDueOnWallet = getActiveBill(
            walletId = wallet.id,
            accountId = wallet.founder.accountId,
        ).copy(
            billId = BillId(BILL_ID_2),
            dueDate = getLocalDate().minusDays(1),
            effectiveDueDate = getLocalDate(),
            paymentLimitTime = LocalTime.parse("18:00", timeFormat),
        )

        withGivenDateTime(ZonedDateTime.of(getLocalDate(), LocalTime.of(13, 0), brazilTimeZone)) {
            addBillIntoDb(dynamoClient, billComingDueOnWallet)

            notifyBillComingDueService.executeRegular(wallet.id)

            verify(exactly = 1) {
                notificationAdapter.notifyBillComingDue(
                    members = listOf(wallet.founder),
                    author = wallet.founder,
                    walletName = wallet.name,
                    walletId = wallet.id,
                    billView = withArg { it.billId shouldBe billComingDueOnWallet.billId },
                    hint = "Dica 1",
                )
            }

            verify(exactly = 1) {
                notificationAdapter.notifyBillComingDue(
                    members = listOf(walletFixture.participant),
                    author = wallet.founder,
                    walletName = wallet.name,
                    walletId = wallet.id,
                    billView = withArg { it.billId shouldBe billComingDueOnWallet.billId },
                )
            }

            val bill =
                billRepository.findBill(billComingDueOnWallet.billId, billComingDueOnWallet.walletId)
            bill.isBillComingDueMessageSent shouldBe true
        }
    }

    @Test
    fun `should not notify bill coming due on wallet when already notified`() {
        val walletFixture = WalletFixture()
        val wallet = walletFixture.buildWallet(
            otherMembers = listOf(
                walletFixture.participant,
                walletFixture.ultraLimitedParticipant,
            ),
        )

        every { walletService.findWallet(wallet.id) } returns wallet
        every {
            accountService.findAccountById(wallet.founder.accountId)
        } returns walletFixture.founderAccount.copy(accountId = AccountId(ACCOUNT_ID))

        val billComingDueOnWallet = getActiveBill(
            walletId = wallet.id,
            accountId = wallet.founder.accountId,
        ).copy(
            billId = BillId(BILL_ID_2),
            dueDate = getLocalDate().minusDays(1),
            effectiveDueDate = getLocalDate(),
            paymentLimitTime = LocalTime.parse("18:00", timeFormat),
            isBillComingDueMessageSent = true,
        )

        withGivenDateTime(ZonedDateTime.of(getLocalDate(), LocalTime.of(13, 0), brazilTimeZone)) {
            addBillIntoDb(dynamoClient, billComingDueOnWallet)

            notifyBillComingDueService.executeRegular(wallet.id)

            verify(exactly = 1) {
                notificationAdapter wasNot called
            }
            val bill =
                billRepository.findBill(billComingDueOnWallet.billId, billComingDueOnWallet.walletId)
            bill.isBillComingDueMessageSent shouldBe true
        }
    }

    @Test
    fun `should not notify bill coming due on wallet when bill was scheduled`() {
        val walletFixture = WalletFixture()
        val wallet = walletFixture.buildWallet(
            otherMembers = listOf(
                walletFixture.participant,
                walletFixture.ultraLimitedParticipant,
            ),
        )

        every { walletService.findWallet(wallet.id) } returns wallet
        every {
            accountService.findAccountById(wallet.founder.accountId)
        } returns walletFixture.founderAccount.copy(accountId = AccountId(ACCOUNT_ID))

        val billComingDueOnWallet = scheduledBill.copy(
            walletId = wallet.id,
            source = ActionSource.Api(wallet.founder.accountId),
        )

        withGivenDateTime(ZonedDateTime.of(getLocalDate(), LocalTime.of(13, 0), brazilTimeZone)) {
            addBillIntoDb(dynamoClient, billComingDueOnWallet)

            notifyBillComingDueService.executeRegular(wallet.id)

            verify(exactly = 1) {
                notificationAdapter wasNot called
            }
            val bill =
                billRepository.findBill(billComingDueOnWallet.billId, billComingDueOnWallet.walletId)
            bill.isBillComingDueMessageSent shouldBe false
        }
    }

    @Test
    fun `should not notify bill coming due on wallet when bill is already overdue`() {
        val walletFixture = WalletFixture()
        val wallet = walletFixture.buildWallet(
            otherMembers = listOf(
                walletFixture.participant,
                walletFixture.ultraLimitedParticipant,
            ),
        )
        every {
            accountService.findAccountById(wallet.founder.accountId)
        } returns walletFixture.founderAccount.copy(accountId = AccountId(ACCOUNT_ID))

        every { walletService.findWallet(wallet.id) } returns wallet

        val billComingDueOnWallet = overdueFichaDeCompensacao.copy(
            walletId = wallet.id,
            source = ActionSource.Api(wallet.founder.accountId),
        )

        withGivenDateTime(ZonedDateTime.of(getLocalDate(), LocalTime.of(13, 0), brazilTimeZone)) {
            addBillIntoDb(dynamoClient, billComingDueOnWallet)

            notifyBillComingDueService.executeRegular(wallet.id)

            verify(exactly = 1) {
                notificationAdapter wasNot called
            }
            val bill =
                billRepository.findBill(billComingDueOnWallet.billId, billComingDueOnWallet.walletId)
            bill.isBillComingDueMessageSent shouldBe false
        }
    }

    @Test
    fun `não deve enviar notificacao na fila quando o usuário estiver no fluxo do chatbotAI`() {
        every {
            accountService.getChatbotType(any<Account>())
        } returns ChatbotType.CHABOT_AI

        val walletFixture = WalletFixture(founderAccountId = AccountId(ACCOUNT_ID))
        val founderAccount = walletFixture.founderAccount

        val wallet = walletFixture.buildWallet(
            otherMembers = listOf(
                walletFixture.participant,
                walletFixture.ultraLimitedParticipant,
            ),
        )
        walletRepository.save(wallet)
        every { walletService.findWallet(wallet.id) } returns wallet
        every { accountService.findAccountById(wallet.founder.accountId) } returns founderAccount

        every {
            chatBotNotificationService.notifyBillComingDue(any(), any(), any(), any(), any())
        } just Runs

        val billComingDueOnWallet = getActiveBill(
            walletId = wallet.id,
            accountId = wallet.founder.accountId,
        ).copy(
            billId = BillId(BILL_ID_2),
            dueDate = getLocalDate().minusDays(1),
            effectiveDueDate = getLocalDate(),
            paymentLimitTime = LocalTime.parse("18:00", timeFormat),
        )

        withGivenDateTime(ZonedDateTime.of(getLocalDate(), LocalTime.of(13, 0), brazilTimeZone)) {
            addBillIntoDb(dynamoClient, billComingDueOnWallet)

            notifyBillComingDueService.executeRegular(wallet.id)

            verify(exactly = 0) {
                notificationAdapter.notifyBillComingDue(
                    members = any(),
                    author = any(),
                    walletId = any(),
                    billView = any(),
                    hint = any(),
                    walletName = any(),
                )
                onePixPayService.create(any(), any())
            }

            verify(exactly = 0) {
                chatBotNotificationService.notifyBillComingDue(
                    founderAccount,
                    wallet,
                    withArg {
                        it.size shouldBe 1
                        it[0].billId shouldBe billComingDueOnWallet.billId
                    },
                    any(),
                    any(),
                )
            }

            val bill =
                billRepository.findBill(billComingDueOnWallet.billId, billComingDueOnWallet.walletId)
            bill.isBillComingDueMessageSent shouldBe false
        }
    }

    @Test
    fun `não deve enviar onepixpay quando o founder for uma conta PJ`() {
        every {
            accountService.getChatbotType(any<Account>())
        } returns ChatbotType.CHATBOT_LEGACY

        every { onePixPayService.create(any(), any()) } returns mockk<OnePixPay>(relaxed = true).right()

        val walletFixture = WalletFixture(founderAccountId = AccountId(ACCOUNT_ID), founderDocument = CNPJ_2)
        val founderAccount = walletFixture.founderAccount

        val wallet = walletFixture.buildWallet(
            otherMembers = listOf(
                walletFixture.participant,
                walletFixture.ultraLimitedParticipant,
            ),
        )
        walletRepository.save(wallet)
        every { walletService.findWallet(wallet.id) } returns wallet
        every { accountService.findAccountById(wallet.founder.accountId) } returns founderAccount

        every {
            chatBotNotificationService.notifyBillComingDue(any(), any(), any(), any(), any())
        } just Runs

        val billComingDueOnWallet = getActiveBill(
            walletId = wallet.id,
            accountId = wallet.founder.accountId,
        ).copy(
            billId = BillId(BILL_ID_2),
            dueDate = getLocalDate().minusDays(1),
            effectiveDueDate = getLocalDate(),
            paymentLimitTime = LocalTime.parse("18:00", timeFormat),
        )

        withGivenDateTime(ZonedDateTime.of(getLocalDate(), LocalTime.of(13, 0), brazilTimeZone)) {
            addBillIntoDb(dynamoClient, billComingDueOnWallet)

            notifyBillComingDueService.executeRegular(wallet.id)

            verify {
                notificationAdapter.notifyBillComingDue(
                    members = any(),
                    author = any(),
                    walletId = any(),
                    billView = any(),
                    hint = any(),
                    walletName = any(),
                )
            }

            verify(exactly = 0) {
                onePixPayService.create(any(), any())
                notificationAdapter.notifyOnePixPay(any(), any(), any(), any(), any())
            }
        }
    }

    @Test
    fun `should not notify bill coming due on wallet when account dont receive notifications`() {
        val account = accountRepository.findById(AccountId(ACCOUNT_ID))
        accountRepository.save(account.copy(configuration = account.configuration.copy(receiveNotification = false)))
        val walletFixture = WalletFixture()
        val wallet = walletFixture.buildWallet(
            walletFounder = walletFixture.founder.copy(accountId = AccountId(ACCOUNT_ID)),
        )
        walletRepository.save(wallet)
        every { walletService.findWallet(wallet.id) } returns wallet

        val billComingDueOnWallet = getActiveBill(
            walletId = wallet.id,
            accountId = wallet.founder.accountId,
        ).copy(
            billId = BillId(BILL_ID_2),
            dueDate = getLocalDate().minusDays(1),
            effectiveDueDate = getLocalDate(),
            paymentLimitTime = LocalTime.parse("18:00", timeFormat),
        )

        val slot = slot<QueueMessageBatch>()
        every {
            messagePublisher.sendMessageBatch(capture(slot))
        } just Runs

        withGivenDateTime(ZonedDateTime.of(getLocalDate(), LocalTime.of(13, 0), brazilTimeZone)) {
            addBillIntoDb(dynamoClient, billComingDueOnWallet)

            notifyBillComingDueService.addWalletsOnExecutionQueue(NotifyBillComingDueType.REGULAR)

            slot.captured.messages.map {
                parseObjectFrom<NotifyBillComingDueMessage>(it)
            } shouldNotContain NotifyBillComingDueMessage(wallet.id, NotifyBillComingDueType.REGULAR)
        }
    }

    @Test
    fun `deve notificar bill coming due numa carteira que não receba notificações se ela for PJ`() {
        val account = accountRepository.findById(AccountId(ACCOUNT_ID))
        accountRepository.save(account.copy(document = "**************", documentType = "CNPJ", configuration = account.configuration.copy(receiveNotification = false)))
        val walletFixture = WalletFixture()
        val wallet = walletFixture.buildWallet(
            walletFounder = walletFixture.founder.copy(accountId = AccountId(ACCOUNT_ID)),
        )
        walletRepository.save(wallet)
        every { walletService.findWallet(wallet.id) } returns wallet
        every { walletService.findAllFounderWallets(any()) } returns listOf(wallet)

        val billComingDueOnWallet = getActiveBill(
            walletId = wallet.id,
            accountId = wallet.founder.accountId,
        ).copy(
            billId = BillId(BILL_ID_2),
            dueDate = getLocalDate().minusDays(1),
            effectiveDueDate = getLocalDate(),
            paymentLimitTime = LocalTime.parse("18:00", timeFormat),
        )

        val slot = slot<QueueMessageBatch>()
        every {
            messagePublisher.sendMessageBatch(capture(slot))
        } just Runs

        withGivenDateTime(ZonedDateTime.of(getLocalDate(), LocalTime.of(13, 0), brazilTimeZone)) {
            addBillIntoDb(dynamoClient, billComingDueOnWallet)

            notifyBillComingDueService.addWalletsOnExecutionQueue(NotifyBillComingDueType.REGULAR_CHATBOT)

            slot.captured.messages.map {
                parseObjectFrom<NotifyBillComingDueMessage>(it)
            } shouldContain NotifyBillComingDueMessage(wallet.id, NotifyBillComingDueType.REGULAR_CHATBOT)
        }
    }
}