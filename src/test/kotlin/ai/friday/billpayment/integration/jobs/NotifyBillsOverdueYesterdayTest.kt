package ai.friday.billpayment.integration.jobs

import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillViewSchedule
import ai.friday.billpayment.app.integrations.BillService
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.notification.BillComingDueService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.BILL_ID_3
import ai.friday.billpayment.integration.BILL_ID_4
import ai.friday.billpayment.integration.BILL_ID_5
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WALLET_ID_2
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.timeFormat
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalTime
import net.javacrumbs.shedlock.core.LockAssert
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class NotifyBillsOverdueYesterdayTest {
    private val notificationAdapter: NotificationAdapter = mockk(relaxed = true)
    private val findBillService: FindBillService = mockk()
    private val billService: BillService = mockk()
    private val walletFixture = WalletFixture()
    private val wallet =
        walletFixture.buildWallet(otherMembers = listOf(walletFixture.participant, walletFixture.cantPayParticipant))

    private val walletService: WalletService = mockk() {
        every { findWallet(any()) } answers {
            walletFixture.buildWallet(
                id = firstArg(),
                otherMembers = listOf(walletFixture.participant, walletFixture.cantPayParticipant),
            )
        }
    }

    private val messagePublisher: MessagePublisher = mockk()
    private val notifyBillComingDueService =
        BillComingDueService(
            findBillService = findBillService,
            billService = billService,
            walletService = walletService,
            notificationAdapter = notificationAdapter,
            notificationHintService = mockk() {
                every {
                    getBillOverdueYesterday(any(), any())
                } returns "DICA FAKE"
            },
            accountService = mockk(),
            onePixPayService = mockk(),
            onePixPayInstrumentation = mockk(relaxed = true),
            scheduleBillService = mockk(),
            onePixPayLock = mockk(),
            messagePublisher = messagePublisher,
            chatBotNotificationService = mockk(),
            billComingDueExecutionQueueName = "queueName",
        )

    private val billOverdueToday = getActiveBill().copy(
        billId = BillId(BILL_ID_2),
        dueDate = getLocalDate().minusDays(1),
        effectiveDueDate = getLocalDate(),
        paymentLimitTime = LocalTime.MIDNIGHT,
    )

    private val billOverdueYesterday = getActiveBill().copy(
        billId = BillId(BILL_ID_3),
        dueDate = getLocalDate().minusDays(2),
        effectiveDueDate = getLocalDate().minusDays(1),
        paymentLimitTime = LocalTime.parse("18:00", timeFormat),
    )

    private val billScheduledOverdueYesterday = getActiveBill().copy(
        billId = BillId(BILL_ID_3),
        dueDate = getLocalDate().minusDays(2),
        effectiveDueDate = getLocalDate().minusDays(1),
        paymentLimitTime = LocalTime.parse("18:00", timeFormat),
        schedule = BillViewSchedule(
            date = getLocalDate().minusDays(2),
            waitingFunds = false,
            waitingRetry = false,
        ),
    )

    private val billOverdueYesterday2 = getActiveBill().copy(
        billId = BillId(BILL_ID_4),
        dueDate = getLocalDate().minusDays(2),
        effectiveDueDate = getLocalDate().minusDays(1),
        paymentLimitTime = LocalTime.parse("18:00", timeFormat),
    )

    private val billOverdueBeforeYesterday = getActiveBill().copy(
        billId = BillId(BILL_ID_2),
        dueDate = getLocalDate().minusDays(1),
        effectiveDueDate = getLocalDate().minusDays(2),
        paymentLimitTime = LocalTime.parse("18:00", timeFormat),
    )

    private val billOverdueYesterdayOtherUser = getActiveBill().copy(
        billId = BillId(BILL_ID_4),
        dueDate = getLocalDate().minusDays(2),
        effectiveDueDate = getLocalDate().minusDays(1),
        paymentLimitTime = LocalTime.parse("18:00", timeFormat),
        walletId = WalletId(WALLET_ID_2),
    )

    private val billOverdueYesterdayOtherUser2 = getActiveBill().copy(
        billId = BillId(BILL_ID_5),
        dueDate = getLocalDate().minusDays(2),
        effectiveDueDate = getLocalDate().minusDays(1),
        paymentLimitTime = LocalTime.parse("18:00", timeFormat),
        walletId = WalletId(WALLET_ID_2),
    )

    @BeforeEach
    fun setUp() {
        LockAssert.TestHelper.makeAllAssertsPass(true)
    }

    @Test
    fun `should not notify when there is no overdue bill`() {
        every { findBillService.findOverdueBills(any()) } returns listOf()

        notifyBillComingDueService.executeOverdueYesterday(wallet.id)

        verify(exactly = 1) {
            findBillService.findOverdueBills(any())
        }

        verify(exactly = 0) {
            notificationAdapter.notifyBillOverdueYesterday(any(), any(), any(), any())
        }
    }

    @Test
    fun `should not notify when bill is schedule`() {
        every { findBillService.findOverdueBills(any()) } returns listOf(billScheduledOverdueYesterday)

        notifyBillComingDueService.executeOverdueYesterday(wallet.id)

        verify(exactly = 1) {
            findBillService.findOverdueBills(any())
        }

        verify(exactly = 0) {
            notificationAdapter.notifyBillOverdueYesterday(any(), any(), any(), any())
        }
    }

    @Test
    fun `should notify when bill is overdue since yesterday`() {
        every { findBillService.findOverdueBills(any()) } returns listOf(billOverdueYesterday)

        notifyBillComingDueService.executeOverdueYesterday(wallet.id)

        verify(exactly = 1) {
            findBillService.findOverdueBills(any())
            notificationAdapter.notifyBillOverdueYesterday(any(), any(), any(), "DICA FAKE")
            notificationAdapter.notifyBillOverdueYesterday(any(), any(), any(), null)
        }
    }

    @Test
    fun `should notify when bill is overdue since yesterday on a wallet`() {
        every { findBillService.findOverdueBills(any()) } returns listOf(billOverdueYesterday)

        notifyBillComingDueService.executeOverdueYesterday(wallet.id)

        verify(exactly = 1) {
            findBillService.findOverdueBills(any())
            notificationAdapter.notifyBillOverdueYesterday(
                listOf(walletFixture.founder),
                any(),
                any(),
                "DICA FAKE",
            )

            notificationAdapter.notifyBillOverdueYesterday(
                listOf(walletFixture.participant),
                any(),
                any(),
                null,
            )
        }
    }

    @Test
    fun `should not notify when bill is overdue by more than one day`() {
        every { findBillService.findOverdueBills(any()) } returns listOf(billOverdueBeforeYesterday)

        notifyBillComingDueService.executeOverdueYesterday(wallet.id)

        verify(exactly = 1) {
            findBillService.findOverdueBills(any())
        }

        verify(exactly = 0) {
            notificationAdapter.notifyBillOverdueYesterday(any(), any(), any(), any())
        }
    }

    @Test
    fun `should not notify when bill is overdue today`() {
        every { findBillService.findOverdueBills(any()) } returns listOf(billOverdueToday)

        notifyBillComingDueService.executeOverdueYesterday(wallet.id)

        verify(exactly = 1) {
            findBillService.findOverdueBills(any())
        }

        verify(exactly = 0) {
            notificationAdapter.notifyBillOverdueYesterday(any(), any(), any(), any())
        }
    }

    @Test
    fun `should notify only once when there are more than one bill overdue since yesterday`() {
        every { findBillService.findOverdueBills(any()) } returns listOf(billOverdueYesterday, billOverdueYesterday2)

        notifyBillComingDueService.executeOverdueYesterday(wallet.id)

        verify(exactly = 1) {
            findBillService.findOverdueBills(any())
            notificationAdapter.notifyBillOverdueYesterday(any(), any(), any(), "DICA FAKE")
            notificationAdapter.notifyBillOverdueYesterday(any(), any(), any(), null)
        }
    }

    @Test
    fun `should notify only once per user when there are more than one bill overdue since yesterday`() {
        every { findBillService.findOverdueBills(any()) } returns listOf(
            billOverdueYesterday,
            billOverdueYesterdayOtherUser,
            billOverdueYesterday2,
            billOverdueYesterdayOtherUser2,
        )

        notifyBillComingDueService.executeOverdueYesterday(wallet.id)

        verify(exactly = 1) {
            findBillService.findOverdueBills(any())
            notificationAdapter.notifyBillOverdueYesterday(
                members = listOf(wallet.founder),
                walletId = WalletId(WALLET_ID),
                walletName = any(),
                hint = "DICA FAKE",
            )
            notificationAdapter.notifyBillOverdueYesterday(
                members = listOf(walletFixture.participant),
                walletId = WalletId(WALLET_ID),
                walletName = any(),
                hint = null,
            )

            notificationAdapter.notifyBillOverdueYesterday(
                members = listOf(wallet.founder),
                walletId = WalletId(WALLET_ID_2),
                walletName = any(),
                hint = "DICA FAKE",
            )
            notificationAdapter.notifyBillOverdueYesterday(
                members = listOf(walletFixture.participant),
                walletId = WalletId(WALLET_ID_2),
                walletName = any(),
                hint = null,
            )
        }
    }

    @Test
    fun `should not notify bills overdue more than 1 year`() {
        every { findBillService.findOverdueBills(any()) } returns listOf(
            billOverdueYesterday.copy(
                dueDate = billOverdueYesterday.dueDate.minusYears(1),
                effectiveDueDate = billOverdueYesterday.dueDate.minusYears(1),
            ),
        )

        notifyBillComingDueService.executeOverdueYesterday(wallet.id)

        verify(exactly = 1) {
            findBillService.findOverdueBills(any())
            notificationAdapter wasNot called
        }
    }
}