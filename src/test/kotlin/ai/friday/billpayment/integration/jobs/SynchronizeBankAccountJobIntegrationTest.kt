package ai.friday.billpayment.integration.jobs

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InternalBankDBRepository
import ai.friday.billpayment.adapters.dynamodb.InternalBankDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.jobs.SynchronizeBankAccountService
import ai.friday.billpayment.adapters.messaging.SynchronizeBankAccountMessage
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.balance.DefaultBalanceService
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankStatement
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.DefaultBankStatementItem
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.integrations.BankAccountService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.message.QueueMessageBatch
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID_2
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadBalancePaymentMethod
import ai.friday.billpayment.integration.loadVirtualBalance
import ai.friday.billpayment.omnibusBankAccountConfiguration
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import arrow.core.getOrElse
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

internal class SynchronizeBankAccountJobIntegrationTest {

    private val bankAccountService: BankAccountService = mockk()

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val dynamoDbEnhancedClient = getDynamoDB()

    private val accountDAO = AccountDynamoDAO(dynamoDbEnhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(dynamoDbEnhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(dynamoDbEnhancedClient)
    private val nsuDAO = NSUDynamoDAO(dynamoDbEnhancedClient)
    private val transactionDynamo = TransactionDynamo(dynamoDbEnhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )
    private val internalBankDynamoDAO = InternalBankDynamoDAO(dynamoDbEnhancedClient)
    private val internalBankRepository = InternalBankDBRepository(internalBankDynamoDAO)
    val accountService = AccountService(
        accountConfigurationService = mockk(),
        accountRepository = accountRepository,
        chatbotMessagePublisher = mockk(),
        crmService = mockk(),
        notificationAdapter = mockk(),
        walletRepository = mockk(),
    )
    private val internalBankService = InternalBankService(
        accountService = accountService,
        bankAccountService = bankAccountService,
        internalBankRepository = internalBankRepository,
        messagePublisher = mockk(relaxed = true),
        omnibusBankAccountConfiguration = omnibusBankAccountConfiguration,
        lockProvider = mockk(),
        balanceService = DefaultBalanceService(
            accountService,
            internalBankRepository,
            bankAccountService,
            omnibusBankAccountConfiguration,
            walletService = mockk(),
        ),
        notificationAdapter = mockk(),
        walletService = mockk(),
        pixPaymentService = mockk(),
    ).apply {
        bankAccountDepositQueueName = ""
        internalSettlementAccountNo = ""
    }
    private val messagePublisher: MessagePublisher = mockk(relaxUnitFun = true)

    private val synchronizeBankAccountJob =
        SynchronizeBankAccountService(
            accountRepository,
            internalBankService,
            omnibusBankAccountConfiguration,
            messagePublisher = messagePublisher,
            queueName = "",
        )

    private val paymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_2)
    private val accountId = AccountId(ACCOUNT_ID)
    private val accountNo = 1000L
    private val accountDv = "1"
    private val document = "***********"
    private val date = getLocalDate()
    private val number = "1000001"
    private val item = DefaultBankStatementItem(
        date = date,
        operationNumber = number,
        isTemporaryOperationNumber = false,
        documentNumber = document,
        counterpartName = "",
        counterpartDocument = document,
        description = "",
        flow = BankStatementItemFlow.CREDIT,
        type = BankStatementItemType.TED_MESMA_TITULARIDADE,
        amount = 100L,
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        loadBalancePaymentMethod(
            accountRepository = accountRepository,
            bankAccountNo = accountNo.toBigInteger(),
            bankAccountDv = accountDv,
            document = document,
            paymentMethodId = paymentMethodId.value,
            accountId = accountId.value,
        )
        every { bankAccountService.getStatement(AccountNumber(accountNo.toBigInteger(), accountDv), document, any(), any()) } returns BankStatement(
            listOf(item),
        )
    }

    @Test
    fun `should save statement items on synchronize bank account with new items`() {
        synchronizeBankAccountJob.synchronizePaymentMethod(paymentMethodId = paymentMethodId, accountId = accountId)
        val bankStatementItem =
            internalBankRepository.findBankStatementItem(paymentMethodId, item.date, item.operationNumber)
                .getOrElse { throw Exception() }
        bankStatementItem shouldBe item
    }

    @Test
    fun `should not get balances with VIRTUAL bank account mode`() {
        val bankAccountNo: Long = 808080
        val bankAccountDv = "x"
        val document1 = "***********"
        loadVirtualBalance(
            amazonDynamoDB = dynamoDB,
            bankAccountNo = bankAccountNo,
            bankAccountDv = bankAccountDv,
            document = document1,
        )
        every { bankAccountService.getStatement(any(), any(), any(), any()) } answers { BankStatement(listOf()) }

        synchronizeBankAccountJob.executePhysical()

        val slot = slot<QueueMessageBatch>()

        verify {
            messagePublisher.sendMessageBatch(capture(slot))
        }

        slot.captured.messages.size shouldBe 1
        parseObjectFrom<SynchronizeBankAccountMessage>(slot.captured.messages.first()).accountId shouldBe accountId
        parseObjectFrom<SynchronizeBankAccountMessage>(slot.captured.messages.first()).paymentMethodId shouldBe paymentMethodId
    }

    @ParameterizedTest
    @EnumSource(AccountPaymentMethodStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["ACTIVE"])
    fun `should not get balances with CLOSED status`(paymentMethodStatus: AccountPaymentMethodStatus) {
        val bankAccountNo: Long = 808080
        val bankAccountDv = "x"
        val document1 = "***********"
        loadBalancePaymentMethod(
            accountRepository = accountRepository,
            bankAccountNo = bankAccountNo.toBigInteger(),
            bankAccountDv = bankAccountDv,
            document = document1,
            paymentMethodStatus = paymentMethodStatus,
        )

        synchronizeBankAccountJob.executePhysical()

        val slot = slot<QueueMessageBatch>()
        verify {
            messagePublisher.sendMessageBatch(capture(slot))
        }

        slot.captured.messages.size shouldBe 1
        parseObjectFrom<SynchronizeBankAccountMessage>(slot.captured.messages.first()).accountId shouldBe accountId
        parseObjectFrom<SynchronizeBankAccountMessage>(slot.captured.messages.first()).paymentMethodId shouldBe paymentMethodId
    }
}