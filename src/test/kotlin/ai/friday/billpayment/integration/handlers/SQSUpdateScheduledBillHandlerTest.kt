package ai.friday.billpayment.integration.handlers

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.messaging.SQSUpdateScheduledBillHandler
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillSchedulePostponed
import ai.friday.billpayment.app.bill.SchedulePostponedReason
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.payment.PaymentSchedulingService
import ai.friday.billpayment.app.payment.RescheduleError
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.pixPaymentPostponedDailyLimitReached
import ai.friday.billpayment.pixPaymentScheduled
import ai.friday.billpayment.scheduledBill
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

class SQSUpdateScheduledBillHandlerTest {

    private val amazonSQS: SqsClient = mockk()

    private val configuration: SQSMessageHandlerConfiguration = mockk(relaxed = true)

    private val paymentSchedulingService: PaymentSchedulingService = mockk()

    private val billEventRepository: BillEventRepository = mockk()

    private val sqsUpdateScheduledBillHandler = SQSUpdateScheduledBillHandler(
        amazonSQS = amazonSQS,
        amazonSNS = mockk(),
        configuration = configuration,
        topicArn = "",
        billEventRepository = billEventRepository,
        paymentSchedulingService = paymentSchedulingService,
    )

    private val bill = Bill.build(
        pixAdded,
        pixPaymentScheduled,
        pixPaymentPostponedDailyLimitReached,
    )

    @BeforeEach
    fun setup() {
        every {
            billEventRepository.getBill(any(), any())
        } returns bill.right()
    }

    @Test
    fun `should not update ScheduledBill when BillSchedulePostponed reason is not LIMIT_REACHED`() {
        val billEvent = createBillSchedulePostponed(SchedulePostponedReason.INSUFFICIENT_FUNDS)
        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(billEvent.toBillEventDetailEntity())).build()

        val result = sqsUpdateScheduledBillHandler.handleMessage(message)

        verify {
            paymentSchedulingService wasNot called
        }

        result.shouldDeleteMessage.shouldBeTrue()
    }

    @Test
    fun `should not update ScheduledBill when schedule not found`() {
        val billEvent = createBillSchedulePostponed(SchedulePostponedReason.LIMIT_REACHED)
        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(billEvent.toBillEventDetailEntity())).build()

        every {
            paymentSchedulingService.reschedule(any(), any())
        } returns RescheduleError.ScheduleNotFound.left()

        val result = sqsUpdateScheduledBillHandler.handleMessage(message)

        verify {
            paymentSchedulingService.reschedule(
                scheduledBill.billId,
                bill.schedule!!.date,
            )
        }

        result.shouldDeleteMessage.shouldBeTrue()
    }

    @Test
    fun `should not delete message on unknown reschedule error`() {
        val billEvent = createBillSchedulePostponed(SchedulePostponedReason.LIMIT_REACHED)
        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(billEvent.toBillEventDetailEntity())).build()

        every {
            paymentSchedulingService.reschedule(any(), any())
        } returns RescheduleError.Unknown(NoStackTraceException(null)).left()

        val result = sqsUpdateScheduledBillHandler.handleMessage(message)

        verify {
            paymentSchedulingService.reschedule(
                scheduledBill.billId,
                bill.schedule!!.date,
            )
        }

        result.shouldDeleteMessage.shouldBeFalse()
    }

    @Test
    fun `should not update ScheduledBill when BILL not found`() {
        val billEvent = createBillSchedulePostponed(SchedulePostponedReason.LIMIT_REACHED)
        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(billEvent.toBillEventDetailEntity())).build()

        every {
            billEventRepository.getBill(any(), any())
        } returns Either.Left(ItemNotFoundException(""))

        val result = sqsUpdateScheduledBillHandler.handleMessage(message)

        verify {
            billEventRepository.getBill(scheduledBill.walletId, scheduledBill.billId)
        }

        result.shouldDeleteMessage.shouldBeTrue()
    }

    @Test
    fun `should update ScheduledBill when BillSchedulePostponed reason is not LIMIT_REACHED`() {
        val billEvent = createBillSchedulePostponed(SchedulePostponedReason.LIMIT_REACHED)
        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(billEvent.toBillEventDetailEntity())).build()

        every {
            paymentSchedulingService.reschedule(any(), any())
        } returns Unit.right()

        val result = sqsUpdateScheduledBillHandler.handleMessage(message)

        verify {
            billEventRepository.getBill(scheduledBill.walletId, scheduledBill.billId)
            paymentSchedulingService.reschedule(
                scheduledBill.billId,
                bill.schedule!!.date,
            )
        }

        result.shouldDeleteMessage.shouldBeTrue()
    }

    private fun createBillSchedulePostponed(reason: SchedulePostponedReason): BillSchedulePostponed {
        return BillSchedulePostponed(
            billId = scheduledBill.billId,
            created = getZonedDateTime().toInstant().toEpochMilli(),
            walletId = scheduledBill.walletId,
            actionSource = ActionSource.System,
            reason = reason,
        )
    }
}