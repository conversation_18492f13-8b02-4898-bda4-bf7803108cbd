package ai.friday.billpayment.integration.handlers

import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.messaging.SQSInvalidateBillRecipientHandler
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.PaymentRefunded
import ai.friday.billpayment.app.bill.PaymentRefundedReason
import ai.friday.billpayment.app.contact.ContactService
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.pix.PixTransactionError
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.billPaymentFailedRetryable
import ai.friday.billpayment.createSavedRecipient
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.BILL_ID_4
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.invoicePaymentFailedNotRetryable
import ai.friday.billpayment.invoicePaymentStarted
import ai.friday.billpayment.transactionId
import com.fasterxml.jackson.databind.ObjectMapper
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message
import software.amazon.awssdk.services.sqs.model.MessageAttributeValue

class SQSInvalidateBillRecipientHandlerIntegrationTest {

    private val contactService: ContactService = mockk()

    private val amazonSQS: SqsClient = mockk()

    private val amazonSNS: SnsClient = mockk()

    private val configuration: SQSMessageHandlerConfiguration = mockk(relaxed = true)

    private val invalidateHandler = SQSInvalidateBillRecipientHandler(
        amazonSQS = amazonSQS,
        amazonSNS = amazonSNS,
        topicArn = "arn",
        queueArnPrefix = "arn",
        contactService,
        configuration,
    )

    private val receipt = "**********"

    private val mapper by lazy { ObjectMapper() }

    @Test
    fun `should discard event on any event different from PaymentFailed`() {
        val messageContent = mapper.writeValueAsString(invoicePaymentStarted.toBillEventDetailEntity())
        val message = buildReceiveMessageResult(
            messageContent,
            BillType.INVOICE,
        )

        val result = invalidateHandler.handleMessage(message)

        assertInvalidateBankAccountNotCalled(result)
    }

    @Test
    fun `should discard event on a boleto PaymentFailed`() {
        val messageContent = mapper.writeValueAsString(billPaymentFailedRetryable.toBillEventDetailEntity())
        val message = buildReceiveMessageResult(
            messageContent,
            BillType.CONCESSIONARIA,
        )

        val result = invalidateHandler.handleMessage(message)

        assertInvalidateBankAccountNotCalled(result)
    }

    @Test
    fun `should discard event on retryable PaymentFailed event`() {
        val messageContent = mapper.writeValueAsString(billPaymentFailedRetryable.toBillEventDetailEntity())
        val message = buildReceiveMessageResult(
            messageContent,
            BillType.INVOICE,
        )

        val result = invalidateHandler.handleMessage(message)

        assertInvalidateBankAccountNotCalled(result)
    }

    @Test
    fun `should discard event on SettlementDestinationNotAllowedAmount error on PaymentFailed event`() {
        val messageContent = mapper.writeValueAsString(
            billPaymentFailedRetryable.copy(
                retryable = false,
                errorDescription = PixTransactionError.SettlementDestinationNotAllowedAmount.code,
            ).toBillEventDetailEntity(),
        )
        val message = buildReceiveMessageResult(
            messageContent,
            BillType.INVOICE,
        )

        val result = invalidateHandler.handleMessage(message)

        assertInvalidateBankAccountNotCalled(result)
    }

    @Test
    fun `should invalidate bank account on PaymentRefunded with PaymentRefundedReason INVALID_DATA`() {
        val messageContent = mapper.writeValueAsString(
            PaymentRefunded(
                billId = BillId(BILL_ID_4),
                walletId = WalletId(WALLET_ID),
                reason = PaymentRefundedReason.INVALID_DATA,
                transactionId = transactionId,
                gateway = FinancialServiceGateway.ARBI,
                actionSource = ActionSource.Api(accountId = AccountId(ACCOUNT_ID)),
            ).toBillEventDetailEntity(),
        )
        every {
            contactService.invalidateSettlementInfo(
                any(),
                eq(BillId(BILL_ID_4)),
                any(),
            )
        } returns createSavedRecipient()
        val message = buildReceiveMessageResult(
            messageContent,
            BillType.INVOICE,
        )

        val result = invalidateHandler.handleMessage(message)

        verify(exactly = 1) {
            contactService.invalidateSettlementInfo(
                WalletId(WALLET_ID),
                BillId(BILL_ID_4),
                PaymentRefundedReason.INVALID_DATA.name,
            )
        }

        result.shouldDeleteMessage shouldBe true
    }

    @EnumSource(PaymentRefundedReason::class, mode = EnumSource.Mode.EXCLUDE, names = ["INVALID_DATA"])
    @ParameterizedTest
    fun `should discard event with PaymentRefundedReason different from INVALID_DATA on PaymentRefunded event`(reason: PaymentRefundedReason) {
        val messageContent = mapper.writeValueAsString(
            PaymentRefunded(
                billId = BillId(BILL_ID_4),
                walletId = WalletId(WALLET_ID),
                reason = reason,
                transactionId = transactionId,
                gateway = FinancialServiceGateway.ARBI,
                actionSource = ActionSource.Api(accountId = AccountId(ACCOUNT_ID)),
            ).toBillEventDetailEntity(),
        )
        val message = buildReceiveMessageResult(
            messageContent,
            BillType.INVOICE,
        )

        val result = invalidateHandler.handleMessage(message)

        assertInvalidateBankAccountNotCalled(result)
    }

    @Test
    fun `should invalidate account on non retryable payment failure`() {
        val messageContent = mapper.writeValueAsString(invoicePaymentFailedNotRetryable.toBillEventDetailEntity())
        every {
            contactService.invalidateSettlementInfo(
                invoicePaymentFailedNotRetryable.walletId,
                invoicePaymentFailedNotRetryable.billId,
                invoicePaymentFailedNotRetryable.errorDescription,
            )
        } returns createSavedRecipient()
        val message = buildReceiveMessageResult(
            messageContent,
            BillType.INVOICE,
        )

        val result = invalidateHandler.handleMessage(message)

        verify(exactly = 1) {
            contactService.invalidateSettlementInfo(
                invoicePaymentFailedNotRetryable.walletId,
                invoicePaymentFailedNotRetryable.billId,
                invoicePaymentFailedNotRetryable.errorDescription,
            )
        }

        result.shouldDeleteMessage shouldBe true
    }

    @Test
    fun `should keep event on invalidateBankAccount npe`() {
        val messageContent = mapper.writeValueAsString(invoicePaymentFailedNotRetryable.toBillEventDetailEntity())
        every {
            contactService.invalidateSettlementInfo(
                invoicePaymentFailedNotRetryable.walletId,
                invoicePaymentFailedNotRetryable.billId,
                invoicePaymentFailedNotRetryable.errorDescription,
            )
        } throws NullPointerException()

        val message = buildReceiveMessageResult(
            messageContent,
            BillType.INVOICE,
        )

        assertThrows<NullPointerException> { invalidateHandler.handleMessage(message) }

        verify(exactly = 1) {
            contactService.invalidateSettlementInfo(
                invoicePaymentFailedNotRetryable.walletId,
                invoicePaymentFailedNotRetryable.billId,
                invoicePaymentFailedNotRetryable.errorDescription,
            )
        }

        val result = invalidateHandler.handleError(message, NullPointerException())

        result.shouldDeleteMessage shouldBe false
    }

    @Test
    fun `should discard event on ItemNotFoundException`() {
        val messageContent = mapper.writeValueAsString(invoicePaymentFailedNotRetryable.toBillEventDetailEntity())
        every {
            contactService.invalidateSettlementInfo(
                invoicePaymentFailedNotRetryable.walletId,
                invoicePaymentFailedNotRetryable.billId,
                invoicePaymentFailedNotRetryable.errorDescription,
            )
        } throws ItemNotFoundException("")

        val message = buildReceiveMessageResult(
            messageContent,
            BillType.INVOICE,
        )

        assertThrows<ItemNotFoundException> { invalidateHandler.handleMessage(message) }

        verify(exactly = 1) {
            contactService.invalidateSettlementInfo(
                invoicePaymentFailedNotRetryable.walletId,
                invoicePaymentFailedNotRetryable.billId,
                invoicePaymentFailedNotRetryable.errorDescription,
            )
        }

        val result = invalidateHandler.handleError(message, ItemNotFoundException(""))

        result.shouldDeleteMessage shouldBe true
    }

    private fun assertInvalidateBankAccountNotCalled(result: SQSHandlerResponse) {
        result.shouldDeleteMessage shouldBe true

        verify(exactly = 0) {
            contactService.invalidateSettlementInfo(
                billPaymentFailedRetryable.walletId,
                billPaymentFailedRetryable.billId,
                billPaymentFailedRetryable.errorDescription,
            )
        }
    }

    private fun buildReceiveMessageResult(message: String, billType: BillType) =
        Message.builder()
            .body(message)
            .receiptHandle(receipt)
            .messageAttributes(
                mapOf(
                    "billType" to MessageAttributeValue.builder().stringValue(billType.name).build(),
                ),
            ).build()
}