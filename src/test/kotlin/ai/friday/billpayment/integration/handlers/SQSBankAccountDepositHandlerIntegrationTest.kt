package ai.friday.billpayment.integration.handlers

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.bill.DefaultBillEventPublisher
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.CreditCardChargebackTrackDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbTransactionRepository
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.PaymentRefunded
import ai.friday.billpayment.app.bill.PaymentRefundedReason
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.TEDService
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.BalanceAuthorization
import ai.friday.billpayment.app.payment.BalanceDirectInvoiceCheckout
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.DirectTEDStatus
import ai.friday.billpayment.app.payment.PaymentSchedulingService
import ai.friday.billpayment.app.payment.ScheduleTo
import ai.friday.billpayment.app.payment.ScheduledBill
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.transaction.PaymentFailedScheduleResolver
import ai.friday.billpayment.app.payment.transaction.UndoTransaction
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.balance
import ai.friday.billpayment.createSinglePaymentDataWithBalance
import ai.friday.billpayment.createTransaction
import ai.friday.billpayment.defaultTransactionEntityConverter
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadBalancePaymentMethod
import ai.friday.billpayment.internalBankAccount
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.invoiceBill
import ai.friday.billpayment.invoicePaid
import ai.friday.billpayment.invoicePaymentScheduled
import ai.friday.billpayment.invoicePaymentStarted
import ai.friday.billpayment.invoiceScheduleCanceled
import ai.friday.billpayment.invoiceScheduleStarted
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import arrow.core.getOrElse
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

@MicronautTest(environments = [FRIDAY_ENV])
class SQSBankAccountDepositHandlerIntegrationTest(val dynamoDB: AmazonDynamoDB) {

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val walletService: WalletService = mockk()
    private val enhancedClient = getDynamoDB()
    private val billEventDAO = BillEventDynamoDAO(enhancedClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)
    val billEventRepository = BillEventDBRepository(
        billEventDAO,
        uniqueConstraintDAO,
        allFalseFeatureConfiguration,
        transactionDynamo,
    )

    private val tedService: TEDService = mockk()

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)

    private val accountRepository: AccountDbRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val transactionDAO = TransactionDynamoDAO(enhancedClient)
    private val creditCardChargebackTrackDAO = CreditCardChargebackTrackDynamoDAO(enhancedClient)

    private val transactionRepository: TransactionRepository = DynamoDbTransactionRepository(
        transactionDAO = transactionDAO,
        creditCardChargebackTrackDAO = creditCardChargebackTrackDAO,
        accountRepository = accountRepository,
        transactionEntityConverter = defaultTransactionEntityConverter(billEventRepository, mockk(), transactionDynamo),
    )
    private val balanceDirectInvoiceCheckout = BalanceDirectInvoiceCheckout(tedService)
    private val notificationMock: NotificationAdapter = mockk(relaxed = true)
    val paymentSchedulingService: PaymentSchedulingService = mockk(relaxed = true)
    private val updateBillService = UpdateBillService(
        billEventRepository = billEventRepository,
        billRepository = mockk(relaxed = true),
        billTrackingRepository = mockk(relaxed = true),
        eventPublisher = mockk(relaxed = true),
        boletoSettlementService = mockk(relaxed = true),
        billValidationService = mockk(relaxed = true),
        lockProvider = mockk(relaxed = true),
        walletLimitsService = mockk(),
        walletService = mockk(),
        approveBillService = mockk(),
        denyBillService = mockk(),
        resolveScheduledBill = mockk(),
        cancelSchedulePaymentService = mockk {
            every { cancelScheduledPayment(any(), any(), any()) } returns true
            every { userCancelScheduledPayment(any(), any(), any(), any()) } returns true
        },
        mailboxWalletDataRepository = mockk(),
    )

    private val transactionService = TransactionService(
        accountRepository = accountRepository,
        updateBillService = updateBillService,
        notificationAdapter = notificationMock,
        walletService = walletService,
        transactionRepository = transactionRepository,
    )

    private val billEventPublisher = DefaultBillEventPublisher(
        eventRepository = billEventRepository,
        billRepository = mockk(relaxed = true),
        eventPublisher = mockk(relaxed = true),
    )

    private val paymentFailedScheduleResolver = PaymentFailedScheduleResolver(
        billEventPublisher = billEventPublisher,
    )

    private val walletRepository = mockk<WalletRepository>()

    private val undoTransaction = UndoTransaction(
        transactionService = transactionService,
        billEventPublisher = billEventPublisher,
        checkoutLocator = mockk(relaxed = true) {
            every { getCheckout(any()) } returns balanceDirectInvoiceCheckout
        },
        paymentSchedulingService = paymentSchedulingService,
        paymentFailedScheduleResolver = paymentFailedScheduleResolver,
        walletRepository = walletRepository,
    )

    private val walletFixture = WalletFixture()
    private val wallet =
        walletFixture.buildWallet(
            id = invoiceBill.walletId,
            walletFounder = walletFixture.founder.copy(accountId = ACCOUNT.accountId),
        )

    private lateinit var transaction: Transaction

    @BeforeEach
    fun setup() {
        clearAllMocks()
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        billEventRepository.save(invoiceAdded.copy(actionSource = ActionSource.Api(accountId = wallet.founder.accountId)))
        billEventRepository.save(invoicePaymentScheduled)
        billEventRepository.save(invoiceScheduleStarted)
        billEventRepository.save(invoicePaymentStarted)
        billEventRepository.save(invoicePaid.copy(actionSource = ActionSource.Scheduled))
        billEventRepository.save(invoiceScheduleCanceled)
        with(internalBankAccount) {
            loadBalancePaymentMethod(
                accountRepository = accountRepository,
                bankNo = bankNo,
                bankRoutingNo = routingNo,
                bankAccountNo = accountNo.toBigInteger(),
                bankAccountDv = accountDv,
                paymentMethodId = balance.id.value,
                accountId = balance.accountId.value,
            )
        }
        every { walletService.findWallet(invoiceBill.walletId) } returns wallet

        every { walletRepository.findWallet(any()) } returns wallet

        transaction = createTransaction(
            status = TransactionStatus.COMPLETED,
            type = TransactionType.INVOICE_PAYMENT,
            account = ACCOUNT,
            paymentData = createSinglePaymentDataWithBalance(
                balance,
                invoiceAdded.amountTotal,
                calculationId = null,
                BalanceAuthorization(
                    operationId = BankOperationId(""),
                    status = BankOperationStatus.SUCCESS,
                    amount = invoiceBill.amountTotal,
                    paymentGateway = FinancialServiceGateway.ARBI,
                ),
            ),
            settlementData = SettlementData(
                Bill.build(
                    invoiceAdded.copy(
                        actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
                    ),
                    invoicePaymentScheduled,
                    invoiceScheduleStarted,
                    invoicePaymentStarted,
                    invoicePaid.copy(actionSource = ActionSource.Scheduled),
                    invoiceScheduleCanceled,
                ),
                0,
                invoiceBill.amountTotal,
                BankTransfer(
                    operationId = BankOperationId("123"),
                    gateway = FinancialServiceGateway.ARBI,
                    status = BankOperationStatus.SUCCESS,
                    amount = invoiceBill.amountTotal,
                    authentication = "",
                    errorDescription = "",
                ),
            ),

            nsu = 1,
            walletId = invoiceAdded.walletId,
        )

        transactionRepository.save(transaction)
    }

    @Test
    fun `should undo transaction when invoice refunded on non retryable error`() {
        undoTransaction.undoDirectInvoice(
            transaction = transaction,
            status = DirectTEDStatus.Failure.INVALID_BANK_DATA,
        )

        transactionRepository.findById(transaction.id).status shouldBe TransactionStatus.UNDONE
        val bill =
            billEventRepository.getBillById(invoiceAdded.billId).getOrElse { throw NoStackTraceException("teste") }
        bill.status shouldBe BillStatus.NOT_PAYABLE
        with(bill.history.filterIsInstance<PaymentRefunded>().first()) {
            reason shouldBe PaymentRefundedReason.INVALID_DATA
            gateway shouldBe FinancialServiceGateway.ARBI
        }
        verify {
            notificationMock.notifyInvoicePaymentUndone(
                members = wallet.activeMembers,
                walletName = wallet.name,
                author = any(),
                any(),
                any(),
                any(),
            )
        }

        verify(exactly = 0) {
            paymentSchedulingService.schedule(any())
        }
    }

    @Test
    fun `should undo transaction when invoice refunded on not allowed error`() {
        undoTransaction.undoDirectInvoice(
            transaction = transaction,
            status = DirectTEDStatus.Failure.NOT_ALLOWED,
        )

        transactionRepository.findById(transaction.id).status shouldBe TransactionStatus.UNDONE
        val bill =
            billEventRepository.getBillById(invoiceAdded.billId).getOrElse { throw NoStackTraceException("teste") }
        bill.status shouldBe BillStatus.NOT_PAYABLE
        with(bill.history.filterIsInstance<PaymentRefunded>().first()) {
            reason shouldBe PaymentRefundedReason.DESTINATION_NOT_ALLOWED_AMOUNT
            gateway shouldBe FinancialServiceGateway.ARBI
        }
        verify {
            notificationMock.notifyInvoicePaymentUndone(
                members = wallet.activeMembers,
                walletName = wallet.name,
                author = any(),
                any(),
                any(),
                any(),
            )
        }

        verify(exactly = 0) {
            paymentSchedulingService.schedule(any())
        }
    }

    @Test
    fun `should undo transaction when invoice refunded on retryable error`() {
        undoTransaction.undoDirectInvoice(
            transaction = transaction,
            status = DirectTEDStatus.Error,
        )

        transactionRepository.findById(transaction.id).status shouldBe TransactionStatus.UNDONE
        val bill =
            billEventRepository.getBillById(invoiceAdded.billId).getOrElse { throw NoStackTraceException("teste") }
        bill.status shouldBe BillStatus.ACTIVE
        with(bill.history.filterIsInstance<PaymentRefunded>().first()) {
            reason shouldBe PaymentRefundedReason.ERROR
            gateway shouldBe FinancialServiceGateway.ARBI
        }
        verify {
            notificationMock.notifyInvoicePaymentUndone(
                members = wallet.activeMembers,
                walletName = wallet.name,
                author = any(),
                any(),
                any(),
                any(),
            )
        }

        val now = getLocalDate()
        val slotScheduleBill = slot<ScheduledBill>()
        verify {
            paymentSchedulingService.schedule(capture(slotScheduleBill))
        }
        with(slotScheduleBill.captured) {
            scheduledDate shouldBe now
            scheduleTo shouldBe ScheduleTo.ASAP
        }
    }
}