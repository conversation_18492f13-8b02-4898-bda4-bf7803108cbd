package ai.friday.billpayment.integration

import DynamoDBUtils
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.adapters.messaging.DDABillTO
import ai.friday.billpayment.adapters.messaging.SQSDDABillsHandler
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.bill.FichaCompensacaoAdded
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.payment.DiscountType
import ai.friday.billpayment.app.payment.FineType
import ai.friday.billpayment.app.payment.InterestType
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.getOrElse
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.kotest.matchers.shouldBe
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import java.io.FileInputStream
import java.nio.charset.StandardCharsets
import java.time.format.DateTimeFormatter
import kotlin.test.assertEquals
import kotlin.test.fail
import org.apache.commons.io.IOUtils
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@MicronautTest(environments = [FRIDAY_ENV])
class SQSDDABillsHandlerIntegrationTest(
    ddaService: DDAService,
    val dynamoDB: AmazonDynamoDB,
) {

    private val dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")

    private val enhancedClient = DynamoDBUtils.setupDynamoDB()

    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(enhancedClient),
        refundedClient = RefundedBillDynamoDAO(enhancedClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(enhancedClient),
    )
    private val billEventDAO = BillEventDynamoDAO(enhancedClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)
    private val billEventRepository = BillEventDBRepository(
        billEventDAO,
        uniqueConstraintDAO,
        allFalseFeatureConfiguration,
        transactionDynamo,
    )
    private val amazonSQS: SqsClient = mockk()

    private val sqsMessageHandlerConfiguration = mockk<SQSMessageHandlerConfiguration>() {
        every { ddaBills } returns "dda-queue-tests"
    }

    private val resource = Thread.currentThread().contextClassLoader.getResource("arbi/DDAInformation.json")
    private val json = IOUtils.toString(FileInputStream(resource!!.path), StandardCharsets.UTF_8.name())

    private val handler = SQSDDABillsHandler(
        amazonSQS = amazonSQS,
        configuration = sqsMessageHandlerConfiguration,
        ddaService = ddaService,
        calculator = mockk {
            every { calculate(any(), any(), any(), any(), any(), any(), any()) } returns mockk(relaxed = true)
        },
        ddaRepository = mockk(relaxed = true),
    ).apply {
        paymentTimeLimit = "20:00"
    }

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        loadAccountIntoDb(dynamoDB, defaultWalletId = WalletId(WALLET_ID))
    }

    @Test
    fun `deve retornar os valores corretos do evento após criar o pagamento`() {
        val ddaBillTO: DDABillTO = parseObjectFrom<DDABillTO>(json).copy(
            cnpj_cpf_sacado = DOCUMENT,
            datavencimento = getZonedDateTime().plusDays(10).format(dateTimeFormatter),
            codmulta = FineType.FREE.code,
            vlrpercmulta = 23.87,
            datamulta = getZonedDateTime().plusDays(11).format(dateTimeFormatter),
            codmora = InterestType.PERCENT_BY_MONTH.code,
            vlrpercmora = 1.7,
            datamora = getZonedDateTime().plusDays(12).format(dateTimeFormatter),
            coddesconto01 = DiscountType.FIXED_UNTIL_DATE.code,
            vlrpercdesconto01 = 5.0,
            datadesconto01 = getZonedDateTime().minusDays(1).format(dateTimeFormatter),
        )

        handler.handleMessage(buildMessage(ddaBillTO))

        val bills = billRepository.findByWallet(WalletId(WALLET_ID))
        assertEquals(1, bills.size)

        val bill = billEventRepository.getBillById(bills[0].billId).getOrElse { fail() }

        with(bill.history.first { it is FichaCompensacaoAdded } as FichaCompensacaoAdded) {
            fineData!!.type shouldBe FineType.FREE
            fineData!!.value shouldBe 23.87.toBigDecimal()
            fineData!!.date shouldBe getLocalDate().plusDays(11)
            interestData!!.type shouldBe InterestType.PERCENT_BY_MONTH
            interestData!!.value shouldBe 1.7.toBigDecimal()
            interestData!!.date shouldBe getLocalDate().plusDays(12)
            discountData!!.type shouldBe DiscountType.FIXED_UNTIL_DATE
            discountData!!.value1 shouldBe 5.0.toBigDecimal()
            discountData!!.date1 shouldBe getLocalDate().minusDays(1)
        }
    }

    @Test
    fun `não deve adicionar o pagamento quando o vencimento passou de 1 mês para trás`() {
        val ddaBillTO: DDABillTO = parseObjectFrom<DDABillTO>(json).copy(
            cnpj_cpf_sacado = DOCUMENT,
            datavencimento = getZonedDateTime().minusMonths(2).format(dateTimeFormatter),
            codmulta = FineType.FREE.code,
            vlrpercmulta = 23.87,
            datamulta = getZonedDateTime().plusDays(11).format(dateTimeFormatter),
            codmora = InterestType.PERCENT_BY_MONTH.code,
            vlrpercmora = 1.7,
            datamora = getZonedDateTime().plusDays(12).format(dateTimeFormatter),
            coddesconto01 = DiscountType.FIXED_UNTIL_DATE.code,
            vlrpercdesconto01 = 5.0,
            datadesconto01 = getZonedDateTime().minusDays(1).format(dateTimeFormatter),
        )

        val result = handler.handleMessage(buildMessage(ddaBillTO))
        result.shouldDeleteMessage shouldBe true
    }

    private fun buildMessage(ddaBillTO: DDABillTO) = Message.builder()
        .body(jacksonObjectMapper().writeValueAsString(ddaBillTO))
        .build()
}