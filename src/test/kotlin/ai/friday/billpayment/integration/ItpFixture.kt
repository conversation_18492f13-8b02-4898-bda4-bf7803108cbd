package ai.friday.billpayment.integration

import ai.friday.billpayment.adapters.chatbot.PaymentIntentTO
import ai.friday.billpayment.app.chatbot.Organization

object ItpFixture {
    fun organizationTO(
        authorizationServerId: String = "1",
        ispb: String = "ispb",
        clearingCode: String = "clearingCode",
        friendlyName: String = "banco",
    ) = Organization(
        authorizationServerId = authorizationServerId,
        ispb = ispb,
        clearingCode = clearingCode,
        friendlyName = friendlyName,
    )

    fun payment(
        walletId: String = "wallet_id",
        document: String = DOCUMENT,
        authorizationServerId: String = "1",
        authorizationServerName: String = "Nome do Banco",
        routingNo: Long = 0,
        accountNo: Long = 0,
        accountDv: String = "1",
        accountType: String = "CACC",
        bankNo: Long? = null,
        bankISPB: String? = "000000",
    ) = PaymentIntentTO(
        walletId = walletId,
        document = document,
        authorizationServerId = authorizationServerId,
        authorizationServerName = authorizationServerName,
        routingNo = routingNo,
        accountNo = accountNo,
        accountDv = accountDv,
        accountType = accountType,
        bankNo = bankNo,
        bankISPB = bankISPB,
    )
}