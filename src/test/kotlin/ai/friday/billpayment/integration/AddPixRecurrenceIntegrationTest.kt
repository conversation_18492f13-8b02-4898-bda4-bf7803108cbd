package ai.friday.billpayment.integration

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.RECIPIENT_ID
import ai.friday.billpayment.adapters.api.BillTO
import ai.friday.billpayment.adapters.api.CreatePixTO
import ai.friday.billpayment.adapters.api.FAKE_RECURRENCE_BILL_ID
import ai.friday.billpayment.adapters.api.PixKeyRequestTO
import ai.friday.billpayment.adapters.api.RecipientBankDetailsTO
import ai.friday.billpayment.adapters.api.RecurrenceRequestTO
import ai.friday.billpayment.adapters.api.RequestPixRecipientTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.dynamodb.BillRecurrenceDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillRecurrenceDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.ContactDbRepository
import ai.friday.billpayment.adapters.dynamodb.ContactDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.WarningCode
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyDetailsResult
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixKeyOwner
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.recurrence.RecurrenceFrequency
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletStatus
import ai.friday.billpayment.app.wallet.WalletType
import ai.friday.billpayment.createSavedRecipient
import ai.friday.billpayment.paymentMethodId
import ai.friday.billpayment.withPixParticipants
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.fasterxml.jackson.databind.ObjectMapper
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.math.BigInteger
import java.time.temporal.TemporalAdjusters
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
class AddPixRecurrenceIntegrationTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {

    @MockBean(PixKeyManagement::class)
    fun pixKeyManagementFun(): PixKeyManagement = pixKeyManagement

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    private val pixKeyDetails = PixKeyDetails(
        key = PixKey(value = "***********", type = PixKeyType.CPF),
        holder = PixKeyHolder(
            accountNo = BigInteger("12345"),
            accountDv = "X",
            ispb = "********",
            institutionName = "banco fake",
            accountType = AccountType.CHECKING,
            routingNo = 1L,
        ),
        owner = PixKeyOwner("Ze", "***********"),
    )

    private val pixKeyManagement: PixKeyManagement = mockk {
        every { findKeyDetailsCacheable(any(), any()) } returns PixKeyDetailsResult(pixKeyDetails, "")
    }

    private val dynamoDbEnhancedClient = getDynamoDB()
    private val billRecurrenceDynamoDAO = BillRecurrenceDynamoDAO(dynamoDbEnhancedClient)
    private val recurrenceRepository = BillRecurrenceDBRepository(billRecurrenceDynamoDAO, "2020-12-31")

    private val recipientDbRepository = ContactDbRepository(ContactDynamoDAO(dynamoDbEnhancedClient))

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val walletDAO = WalletDynamoDAO(dynamoDbEnhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(dynamoDbEnhancedClient)
    private val inviteDAO = InviteDynamoDAO(dynamoDbEnhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(dynamoDbEnhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(dynamoDbEnhancedClient)

    private val walletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = mockk(),
    )

    private val lockProvider: InternalLock = mockk(relaxed = true)

    private val bankAccountRequestTO = CreatePixTO(
        amount = 10,
        description = "description",
        dueDate = getLocalDate().plusDays(1).format(dateFormat),
        recipient = RequestPixRecipientTO(
            id = RECIPIENT_ID,
            name = "Ze",
            document = "***********",
            documentType = "CPF",
            bankDetails = RecipientBankDetailsTO(
                accountType = AccountType.CHECKING,
                routingNo = 1,
                accountNo = BigInteger("*********"),
                accountDv = "2",
                ispb = "********",
            ),
            alias = "Ze",
        ),
    )

    private val pixKeyRequestTO = CreatePixTO(
        amount = 10,
        description = "description",
        dueDate = getLocalDate().plusDays(1).format(dateFormat),
        recipient = RequestPixRecipientTO(
            name = null,
            document = null,
            documentType = null,
            bankDetails = null,
            pixKey = PixKeyRequestTO(
                type = PixKeyType.CPF,
                value = "***********",
            ),
            alias = "Ze",
        ),
    )

    private val accountId: AccountId = AccountId(ACCOUNT_ID)

    private val founder = Member(
        accountId,
        document = DOCUMENT,
        name = "Membro Fundador",
        emailAddress = EmailAddress(EMAIL),
        type = MemberType.FOUNDER,
        status = MemberStatus.ACTIVE,
        permissions = MemberPermissions(
            viewBills = BillPermission.ALL_BILLS,
            scheduleBills = BillPermission.ALL_BILLS,
            viewBalance = true,
            notification = true,
        ),
        created = getZonedDateTime(),
        updated = getZonedDateTime(),
    )

    private val primaryWalletId = WalletId(WALLET_ID)

    private val primaryWallet = Wallet(
        primaryWalletId,
        "carteira principal",
        listOf(founder),
        10,
        WalletStatus.ACTIVE,
        WalletType.PRIMARY,
        paymentMethodId,
    )

    private val walletLimitsService = WalletLimitsService(
        billRepository = mockk(),
        accountService = mockk<AccountService>() {
            every {
                findAccountById(accountId)
            } returns mockk() {
                every {
                    type
                } returns UserAccountType.FULL_ACCOUNT
            }
        },
        walletRepository = walletRepository,
        paymentSchedulingService = mockk(),
        internalLock = lockProvider,
    )

    private val walletLimit: Long = 999

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        loadAccountIntoDb(dynamoDB)

        walletRepository.save(primaryWallet)
        configureDailyLimits(
            walletLimitsService = walletLimitsService,
            accountId = accountId,
            walletId = primaryWalletId,
            dailyLimit = walletLimit,
            nighttimeLimit = walletLimit,
        )
    }

    private fun buildRequest(request: Any, dryRun: Boolean) = HttpRequest.POST("/bill/pix?dryRun=$dryRun", request)
        .onWallet(primaryWallet)

    @Test
    fun `should return created on valid dry run pix with recurrence but should not create recurrence`() {
        val response = client.exchange(
            buildRequest(
                pixKeyRequestTO.copy(recurrence = RecurrenceRequestTO(frequency = RecurrenceFrequency.WEEKLY)),
                true,
            ),
            BillTO::class.java,
        ).firstOrError().blockingGet()

        response.status shouldBe HttpStatus.CREATED
        response.body()?.id shouldBe FAKE_RECURRENCE_BILL_ID

        validatePixRecurrenceResponse(response.body()!!, pixKeyRequestTO)
        val recipient = recipientDbRepository.findRecipientByAccountIDAndDocument(
            AccountId(ACCOUNT_ID),
            bankAccountRequestTO.recipient.document!!,
        )
        recipient.isLeft() shouldBe true
        recipient.mapLeft { it.shouldBeTypeOf<ItemNotFoundException>() }
        verify {
            pixKeyManagement.findKeyDetailsCacheable(any(), any())
        }
    }

    @Test
    fun `should return created with warning code POSTPONED_DUE_LIMIT_REACHED`() {
        createPaidPix(client = dynamoDbEnhancedClient, walletId = primaryWalletId, amount = walletLimit - 1)
        val response = client.exchange(
            buildRequest(
                pixKeyRequestTO.copy(
                    dueDate = getLocalDate().format(dateFormat),
                    recurrence = RecurrenceRequestTO(frequency = RecurrenceFrequency.WEEKLY),
                    amount = walletLimit,
                ),
                true,
            ),
            BillTO::class.java,
        ).firstOrError().blockingGet()

        response.status shouldBe HttpStatus.CREATED
        response.body()?.warningCode shouldBe WarningCode.POSTPONED_DUE_LIMIT_REACHED.name
        response.body()?.availablePaymentMethods.shouldContainExactlyInAnyOrder(
            PaymentMethodType.BALANCE,
        )
    }

    @Test
    fun `should return created with warning code UNSCHEDULED_DUE_AMOUNT_HIGHER_THAN_DAILY_LIMIT`() {
        val response = client.exchange(
            buildRequest(
                pixKeyRequestTO.copy(
                    recurrence = RecurrenceRequestTO(frequency = RecurrenceFrequency.WEEKLY),
                    amount = walletLimit + 1,
                ),
                true,
            ),
            BillTO::class.java,
        ).firstOrError().blockingGet()

        response.status shouldBe HttpStatus.CREATED
        response.body()?.warningCode shouldBe WarningCode.UNSCHEDULED_DUE_AMOUNT_HIGHER_THAN_DAILY_LIMIT.name
        response.body()?.availablePaymentMethods.shouldContainExactlyInAnyOrder(
            PaymentMethodType.BALANCE,
        )
    }

    @Test
    fun `should return created on valid pix with bank account and recurrence and save entities`() {
        recipientDbRepository.save(createSavedRecipient())

        withPixParticipants(FinancialInstitution(name = "Banco do Brasil SA", compe = 1L, ispb = "********")) {
            val dueDate = getLocalDate().with(TemporalAdjusters.firstDayOfNextMonth())
            val requestTO = bankAccountRequestTO.copy(
                dueDate = dueDate.format(dateFormat),
                recurrence = RecurrenceRequestTO(
                    frequency = RecurrenceFrequency.MONTHLY,
                    endDate = dueDate.plusMonths(1).format(dateFormat),
                ),
            )
            val response = client.exchange(
                buildRequest(requestTO, false),
                BillTO::class.java,
            ).firstOrError().blockingGet()

            response.status shouldBe HttpStatus.CREATED
            validatePixRecurrenceResponse(response.body()!!, requestTO)
            val recurrenceId = RecurrenceId(response.body()!!.recurrence!!.id)
            val recurrence = recurrenceRepository.find(recurrenceId, primaryWalletId)
            assertSoftly {
                val responseBody = response.body()!!
                with(responseBody) {
                    id shouldBe recurrence.bills.first().value
                    source.type shouldBe "Recurrence"
                    this.recurrence!!.startDate shouldBe requestTO.dueDate
                    this.recurrence!!.frequency shouldBe RecurrenceFrequency.MONTHLY.name
                }
                with(recurrence) {
                    this.actionSource shouldBe ActionSource.Api(accountId = primaryWallet.founder.accountId)
                    this.rule.frequency shouldBe RecurrenceFrequency.MONTHLY
                    this.contactId!!.value shouldBe bankAccountRequestTO.recipient.id
                }
            }
        }
    }

    @Test
    fun `should return bad request when end date format is invalid`() {
        val request = buildRequest(
            bankAccountRequestTO.copy(
                recurrence = RecurrenceRequestTO(
                    frequency = RecurrenceFrequency.WEEKLY,
                    endDate = "10/10/2020",
                ),
            ),
            false,
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(BillTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    @Test
    fun `should return bad request when frequency is invalid`() {
        val jsonString =
            ObjectMapper().writeValueAsString(bankAccountRequestTO.copy(recurrence = RecurrenceRequestTO(frequency = RecurrenceFrequency.WEEKLY)))
                .replace("WEEKLY", "INVALIDO")
        val request = buildRequest(jsonString, false)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(BillTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    @Test
    fun `should return bad request when start date is before today`() {
        val requestTO = bankAccountRequestTO.copy(
            recurrence = RecurrenceRequestTO(frequency = RecurrenceFrequency.WEEKLY),
            dueDate = getZonedDateTime().toLocalDate().minusDays(1).format(dateFormat),
        )

        val request = buildRequest(requestTO, false)

        val response = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, BillTO::class.java)
        }

        response.status shouldBe HttpStatus.BAD_REQUEST
    }

    private fun validatePixRecurrenceResponse(billTO: BillTO, createRequest: CreatePixTO) {
        assertSoftly {
            billTO.amount shouldBe createRequest.amount
            billTO.description shouldBe createRequest.description
            billTO.dueDate shouldBe createRequest.dueDate
            billTO.billType shouldBe BillType.PIX.name
            with(billTO.billRecipient!!) {
                alias shouldBe createRequest.recipient.alias
                if (pixKey != null) {
                    document shouldBe pixKeyDetails.owner.document
                    name shouldBe pixKeyDetails.owner.name
                    pixKey!!.type shouldBe pixKeyDetails.key.type
                    pixKey!!.value shouldBe pixKeyDetails.key.value
                    pixKey!!.name shouldBe pixKeyDetails.owner.name
                    pixKey!!.document shouldBe pixKeyDetails.owner.document
                    pixKey!!.institutionName shouldBe pixKeyDetails.holder.institutionName
                    pixKey!!.ispb shouldBe pixKeyDetails.holder.ispb
                    pixKey!!.routingNo shouldBe pixKeyDetails.holder.routingNo
                    pixKey!!.accountNo shouldBe pixKeyDetails.holder.accountNo
                    pixKey!!.accountDv shouldBe pixKeyDetails.holder.accountDv
                    pixKey!!.accountType shouldBe pixKeyDetails.holder.accountType
                } else {
                    document shouldBe createRequest.recipient.document
                    name shouldBe createRequest.recipient.name
                    bankDetails!!.ispb shouldBe createRequest.recipient.bankDetails!!.ispb
                    bankDetails!!.accountNo shouldBe createRequest.recipient.bankDetails!!.accountNo
                    bankDetails!!.accountDv shouldBe createRequest.recipient.bankDetails!!.accountDv
                    bankDetails!!.routingNo shouldBe createRequest.recipient.bankDetails!!.routingNo
                    bankDetails!!.accountType shouldBe createRequest.recipient.bankDetails!!.accountType
                }
            }
            billTO.availablePaymentMethods.shouldContainExactlyInAnyOrder(
                PaymentMethodType.BALANCE,
            )
        }
    }
}