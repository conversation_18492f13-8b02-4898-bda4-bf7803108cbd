package ai.friday.billpayment.integration

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.api.CreditCardUsageTO
import ai.friday.billpayment.adapters.api.WalletPaymentBalanceResponseTO
import ai.friday.billpayment.adapters.api.WalletPaymentMethodBankAccountTO
import ai.friday.billpayment.adapters.api.WalletPaymentMethodCreditCardTO
import ai.friday.billpayment.adapters.api.WalletPaymentMethodsResponseTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.adapters.lock.limitLockProvider
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardBrand
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.app.account.hmacLockTag
import ai.friday.billpayment.app.balance.Balance
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.integrations.HMacService
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.emailPixKeySanitize
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletType
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.inspectors.forExactly
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import jakarta.inject.Named
import java.math.BigInteger
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
class WalletPaymentMethodIntegrationTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {
    private val securityFixture = SecurityFixture()
    private val walletFixture = WalletFixture()

    private val enhancedClient = getDynamoDB()

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val participantPrimaryWallet =
        walletFixture.buildPrimaryWallet(founderAccount = walletFixture.participantAccount)

    private val founderPrimaryWallet = walletFixture.buildWallet(
        otherMembers = listOf(walletFixture.assistant, walletFixture.participant),
        accountPaymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_2),
    )
    private val founderSecondaryWallet = walletFixture.buildWallet(
        id = WalletId("FOUNDER-SECONDARY-WALLET"),
        type = WalletType.SECONDARY,
        accountPaymentMethodId = AccountPaymentMethodId("APM-FOUNDER-SECONDARY-WALLET"),
    ).copy(name = "Carteira")

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletRepository: WalletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = mockk(),
    )

    val client: RxHttpClient =
        embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val participantCreditCardWithID3 = CreditCard(
        brand = CreditCardBrand.MASTERCARD,
        pan = "****************",
        expiryDate = "10/2021",
        riskLevel = RiskLevel.LOW,
        binDetails = null,
        externalId = null,
        mainCard = null,
    )
    private val participantCreditCardWithID5 = CreditCard(
        brand = CreditCardBrand.MASTERCARD,
        pan = "****************",
        expiryDate = "10/2021",
        riskLevel = RiskLevel.MEDIUM,
        binDetails = null,
        externalId = null,
        mainCard = null,
    )

    private val externalCreditCard = CreditCard(
        brand = CreditCardBrand.MASTERCARD,
        pan = "****************",
        expiryDate = "10/2021",
        riskLevel = RiskLevel.MEDIUM,
        binDetails = null,
        mainCard = true,
    )

    @field:Property(name = "creditCardConfiguration.mediumRiskQuota")
    var mediumRiskQuota: Long = 500_00

    @MockBean(BalanceService::class)
    fun balanceService() = balanceService
    val balanceService: BalanceService = mockk {
        every {
            getBalanceFrom(any(), any())
        } returns Balance(10L)
    }

    @MockBean(InternalLock::class, named = limitLockProvider)
    @Named(limitLockProvider)
    fun lockService(): InternalLock = lockService
    private val lockService = mockk<InternalLock>()

    @MockBean(InternalLock::class, named = hmacLockTag)
    @Named(hmacLockTag)
    fun lockService2(): InternalLock = lockService2
    private val lockService2 = mockk<InternalLock>()

    @MockBean(HMacService::class)
    fun hmacService(): HMacService = hmacService
    private val hmacService = mockk<HMacService> {
        every { sign(any(), any()) } returns "signature"
    }

    private val participantQuota = 20_000_00L

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        loadAccountIntoDb(dynamoDB, position = 1)

        accountRepository.save(
            walletFixture.participantAccount.copy(
                configuration = walletFixture.participantAccount.configuration.copy(
                    creditCardConfiguration = walletFixture.participantAccount.configuration.creditCardConfiguration.copy(
                        quota = participantQuota,
                    ),
                ),
            ),
        )
        walletRepository.save(participantPrimaryWallet)

        accountRepository.createAccountPaymentMethod(
            accountId = walletFixture.participantAccount.accountId,
            bankAccount = BankAccount(
                accountType = AccountType.CHECKING,
                bankNo = 341,
                routingNo = 1,
                accountNo = BigInteger("1"),
                accountDv = "1",
                document = walletFixture.participantAccount.document,
                ispb = "********",
            ),
            position = 1,
            paymentMethodId = participantPrimaryWallet.paymentMethodId,
            status = AccountPaymentMethodStatus.ACTIVE,
            mode = BankAccountMode.PHYSICAL,
        )
        accountRepository.createAccountPaymentMethod(
            accountId = walletFixture.participantAccount.accountId,
            paymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_4),
            creditCard = CreditCard(
                brand = CreditCardBrand.MASTERCARD,
                pan = "****************",
                expiryDate = "10/2021",
                riskLevel = RiskLevel.LOW,
                binDetails = null,
                externalId = null,
                mainCard = null,
            ),
            position = 2,
            status = AccountPaymentMethodStatus.BLOCKED,
        )
        accountRepository.createAccountPaymentMethod(
            accountId = walletFixture.participantAccount.accountId,
            paymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_3),
            creditCard = participantCreditCardWithID3,
            position = 3,
            status = AccountPaymentMethodStatus.ACTIVE,
        )
        accountRepository.createAccountPaymentMethod(
            accountId = walletFixture.participantAccount.accountId,
            paymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_5),
            creditCard = participantCreditCardWithID5,
            position = 4,
            status = AccountPaymentMethodStatus.ACTIVE,
        )
        accountRepository.createAccountPaymentMethod(
            accountId = walletFixture.participantAccount.accountId,
            paymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_EXTERNAL_ID),
            creditCard = externalCreditCard,
            position = 5,
            status = AccountPaymentMethodStatus.ACTIVE,
        )

        accountRepository.save(walletFixture.founderAccount)
        walletRepository.save(founderPrimaryWallet)
        walletRepository.save(founderSecondaryWallet)

        accountRepository.createAccountPaymentMethod(
            accountId = walletFixture.founderAccount.accountId,
            bankAccount = BankAccount(
                accountType = AccountType.CHECKING,
                bankNo = 999,
                routingNo = 7,
                accountNo = BigInteger("8"),
                accountDv = "9",
                document = walletFixture.founderAccount.document,
                ispb = "********",
            ),
            position = 1,
            paymentMethodId = founderPrimaryWallet.paymentMethodId,
            status = AccountPaymentMethodStatus.ACTIVE,
            mode = BankAccountMode.PHYSICAL,
        )

        accountRepository.createAccountPaymentMethod(
            accountId = walletFixture.founderAccount.accountId,
            bankAccount = BankAccount(
                accountType = AccountType.CHECKING,
                bankNo = 341,
                routingNo = 1,
                accountNo = BigInteger("1"),
                accountDv = "1",
                document = walletFixture.founderAccount.document,
                ispb = "********",
            ),
            position = 3,
            paymentMethodId = founderSecondaryWallet.paymentMethodId,
            status = AccountPaymentMethodStatus.ACTIVE,
            mode = BankAccountMode.PHYSICAL,
        )
    }

    @Test
    fun `should return forbidden when user is not a wallet member`() {
        val cookie = securityFixture.cookieAuthOwner

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().retrieve(
                buildGetPaymentMethodsRequest(cookie, participantPrimaryWallet.id),
                Argument.listOf(
                    WalletPaymentMethodsResponseTO::class.java,
                ),
            )
        }
        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `should return wallet payment methods when user request payment methods of his primary wallet`() {
        val cookie = buildCookie(walletFixture.participantAccount)

        val expectedBankAccount = WalletPaymentMethodBankAccountTO(
            paymentMethodId = participantPrimaryWallet.paymentMethodId.value,
            bankNo = 341,
            routingNo = 1,
            accountNo = 1,
            accountDv = "1",
            accountType = AccountType.CHECKING,
            mode = BankAccountMode.PHYSICAL,
            name = participantPrimaryWallet.founder.name,
            document = participantPrimaryWallet.founder.document,
        )

        val expectedUsage = CreditCardUsageTO(
            quota = mediumRiskQuota,
            usage = 0,
            cashInFee = 400,
        )

        val response = client.toBlocking().retrieve(
            buildGetPaymentMethodsRequest(cookie, participantPrimaryWallet.id),
            Argument.of(
                WalletPaymentMethodsResponseTO::class.java,
            ),
        )

        response.usage shouldBe expectedUsage

        with(response.pixKey!!) {
            type shouldBe PixKeyType.EMAIL
            value shouldBe "${participantPrimaryWallet.founder.document}@friday.ai"
        }
        response.bankDetails shouldBe expectedBankAccount
        response.creditCards.size shouldBe 3
        with(response.creditCards.first { it.id == PAYMENT_METHOD_ID_3 }) {
            CreditCardBrand.find(creditCard.brand) shouldBe participantCreditCardWithID3.brand
            creditCard.pan shouldBe participantCreditCardWithID3.maskedPan
            creditCard.expiryDate shouldBe participantCreditCardWithID3.expiryDate
            creditCard.status shouldBe AccountPaymentMethodStatus.ACTIVE.name
            usage.usage shouldBe 0
            usage.quota shouldBe participantQuota
        }
        with(response.creditCards.first { it.id == PAYMENT_METHOD_ID_5 }) {
            CreditCardBrand.find(creditCard.brand) shouldBe participantCreditCardWithID5.brand
            creditCard.pan shouldBe participantCreditCardWithID5.maskedPan
            creditCard.expiryDate shouldBe participantCreditCardWithID5.expiryDate
            creditCard.status shouldBe AccountPaymentMethodStatus.ACTIVE.name
            usage.usage shouldBe 0
            usage.quota shouldBe mediumRiskQuota
        }
        with(response.creditCards.first { it.id == PAYMENT_METHOD_EXTERNAL_ID }) {
            CreditCardBrand.find(creditCard.brand) shouldBe externalCreditCard.brand
            creditCard.pan shouldBe externalCreditCard.maskedPan
            creditCard.expiryDate shouldBe externalCreditCard.expiryDate
            creditCard.status shouldBe AccountPaymentMethodStatus.ACTIVE.name
            creditCard.externalId shouldBe externalCreditCard.externalId?.value
            creditCard.mainCard.shouldBeTrue()
        }
        response.balance shouldBe WalletPaymentBalanceResponseTO(
            paymentMethodId = participantPrimaryWallet.paymentMethodId.value,
            amount = 10L,
        )
    }

    @Test
    fun `should return wallet payment methods when user request payment methods of other wallet`() {
        val cookie = buildCookie(walletFixture.participantAccount)

        val expectedBankAccount = WalletPaymentMethodBankAccountTO(
            paymentMethodId = founderPrimaryWallet.paymentMethodId.value,
            bankNo = 999,
            routingNo = 7,
            accountNo = 8,
            accountDv = "9",
            accountType = AccountType.CHECKING,
            mode = BankAccountMode.PHYSICAL,
            name = founderPrimaryWallet.founder.name,
            document = founderPrimaryWallet.founder.document,
        )

        val response = client.toBlocking().retrieve(
            buildGetPaymentMethodsRequest(cookie, founderPrimaryWallet.id),
            Argument.of(
                WalletPaymentMethodsResponseTO::class.java,
            ),
        )

        val expectedUsage = CreditCardUsageTO(
            quota = mediumRiskQuota,
            usage = 0,
            cashInFee = 400,
        )

        response.usage shouldBe expectedUsage
        with(response.pixKey!!) {
            type shouldBe PixKeyType.EMAIL
            value shouldBe "${founderPrimaryWallet.founder.document}@friday.ai"
        }
        response.bankDetails shouldBe expectedBankAccount
        response.creditCards.size shouldBe 3
        with(response.creditCards.first { it.id == PAYMENT_METHOD_ID_3 }) {
            CreditCardBrand.find(creditCard.brand) shouldBe participantCreditCardWithID3.brand
            creditCard.pan shouldBe participantCreditCardWithID3.maskedPan
            creditCard.expiryDate shouldBe participantCreditCardWithID3.expiryDate
            creditCard.status shouldBe AccountPaymentMethodStatus.ACTIVE.name
            usage.usage shouldBe 0
            usage.quota shouldBe participantQuota
        }
        with(response.creditCards.first { it.id == PAYMENT_METHOD_ID_5 }) {
            CreditCardBrand.find(creditCard.brand) shouldBe participantCreditCardWithID5.brand
            creditCard.pan shouldBe participantCreditCardWithID5.maskedPan
            creditCard.expiryDate shouldBe participantCreditCardWithID5.expiryDate
            creditCard.status shouldBe AccountPaymentMethodStatus.ACTIVE.name
            usage.usage shouldBe 0
            usage.quota shouldBe mediumRiskQuota
        }
        response.balance shouldBe WalletPaymentBalanceResponseTO(
            paymentMethodId = founderPrimaryWallet.paymentMethodId.value,
            amount = 10L,
        )
    }

    @Test
    fun `should return PIX as an wallet payment method when user request payment methods of a secondary wallet`() {
        val cookie = buildCookie(walletFixture.founderAccount)

        val response = client.toBlocking().retrieve(
            buildGetPaymentMethodsRequest(cookie, founderSecondaryWallet.id),
            Argument.of(
                WalletPaymentMethodsResponseTO::class.java,
            ),
        )

        with(response.pixKey!!) {
            type shouldBe PixKeyType.EMAIL
            value shouldBe emailPixKeySanitize("${founderSecondaryWallet.name}.${founderSecondaryWallet.founder.document}@friday.ai")
        }

        response.balance shouldBe WalletPaymentBalanceResponseTO(
            paymentMethodId = founderSecondaryWallet.paymentMethodId.value,
            amount = 10L,
        )
    }

    @Test
    fun `deve retornar os cartões ativos e os cartões pendentes para o usuário`() {
        val cookie = buildCookie(walletFixture.participantAccount)

        loadCreditCard(
            amazonDynamoDB = dynamoDB,
            creditCardPan = "****************",
            accountId = walletFixture.participantAccount.accountId,
            paymentMethodId = PAYMENT_METHOD_ID_4,
            paymentMethodStatus = AccountPaymentMethodStatus.PENDING,
        )

        val response = client.toBlocking().retrieve(
            buildGetPaymentMethodsRequest(cookie, founderPrimaryWallet.id),
            Argument.of(
                WalletPaymentMethodsResponseTO::class.java,
            ),
        )

        response.creditCards.size shouldBe 4
        response.creditCards.forExactly(1) { it.creditCard.status shouldBe AccountPaymentMethodStatus.PENDING.name }
    }

    private fun buildGetPaymentMethodsRequest(cookie: Cookie, walletId: WalletId) =
        HttpRequest.GET<WalletPaymentMethodCreditCardTO>("/wallet/${walletId.value}/payment-method")
            .cookie(cookie)
            .header("X-API-VERSION", "2")
}