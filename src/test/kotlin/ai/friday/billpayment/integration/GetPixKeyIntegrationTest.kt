package ai.friday.billpayment.integration

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.api.AccountTO
import ai.friday.billpayment.adapters.api.PixKeyResponseTO
import ai.friday.billpayment.adapters.api.QrCodeTO
import ai.friday.billpayment.adapters.api.builders.PixQRCodeResponseTO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.bill.PixCopyAndPaste
import ai.friday.billpayment.app.bill.PixQrCodeData
import ai.friday.billpayment.app.bill.PixQrCodeType
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.PixQRCodeParserService
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyDetailsResult
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixKeyException
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixKeyOwner
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.PixQRCodeDetailsResult
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import java.math.BigInteger
import java.util.UUID
import java.util.stream.Stream
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource

@MicronautTest(environments = [FRIDAY_ENV])
class GetPixKeyIntegrationTest(embeddedServer: EmbeddedServer) {

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)
    private val securityFixture = SecurityFixture()

    @MockBean(PixKeyManagement::class)
    fun pixKeyManagementFun(): PixKeyManagement = pixKeyManagement
    private val pixKeyManagement: PixKeyManagement = mockk()

    @MockBean(AccountService::class)
    fun accountService(): AccountService = accountService
    private val accountService: AccountService = mockk() {
        every { findAccountById(any()) } returns ACCOUNT
    }

    @MockBean(PixQRCodeParserService::class)
    fun pixQRCodeParserService(): PixQRCodeParserService = pixQRCodeParserService
    private val pixQRCodeParserService: PixQRCodeParserService = mockk()

    private fun buildRequest(pixKeyType: String, pixKey: String, cookie: Cookie) =
        client.toBlocking().retrieve(
            HttpRequest.GET<AccountTO>("/pix/$pixKeyType/$pixKey")
                .cookie(cookie)
                .header("X-API-VERSION", "2"),
            PixKeyResponseTO::class.java,
        )

    private fun buildRequestQrCode(qrCode: String, cookie: Cookie) =
        client.toBlocking().retrieve(
            HttpRequest.POST("/pix/QRCODE", QrCodeTO(qrCode = qrCode))
                .cookie(cookie)
                .header("X-API-VERSION", "2")
                .header("X-WALLET-ID", WALLET_ID),
            PixQRCodeResponseTO::class.java,
        )

    @Test
    fun `should fail if user is guest`() {
        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            buildRequest("EMAIL", "any", securityFixture.cookieGuest)
        }

        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @ParameterizedTest
    @CsvSource(
        "INVALID,ANY",
        "CNPJ,***********",
        "PHONE,*************",
        "EMAIL,teste@@com",
        "EVP,XXXXX-XXXXX-XXXX",
        "CPF,1339759XXXX",
        "CPF,********000199",
    )
    fun `should return bad request on invalid pix key format`(type: String, key: String) {
        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            buildRequest(type, key, securityFixture.cookieAuthOwner)
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    @ParameterizedTest
    @MethodSource("errorsProvider")
    fun `should return specified error when recieves error finding key`(keyError: PixKeyError, httpStatus: HttpStatus) {
        every {
            pixKeyManagement.findKeyDetailsCacheable(any(), any())
        } throws PixKeyException(keyError)

        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            buildRequest("EMAIL", "<EMAIL>", securityFixture.cookieAuthOwner)
        }

        thrown.status shouldBe httpStatus
    }

    @Test
    fun `should return key if it is found`() {
        val pixKey = UUID.randomUUID().toString()

        every {
            pixKeyManagement.findKeyDetailsCacheable(PixKey(pixKey, PixKeyType.EVP), any())
        } returns
            PixKeyDetailsResult(
                pixKeyDetails = PixKeyDetails(
                    key = PixKey(pixKey, PixKeyType.EVP),
                    holder = PixKeyHolder(
                        accountNo = BigInteger("0"),
                        accountDv = "1",
                        ispb = "********",
                        institutionName = "fake name",
                        accountType = AccountType.CHECKING,
                        routingNo = 0,
                    ),
                    owner = PixKeyOwner(
                        name = "owner fake",
                        document = "**********",
                    ),
                ),
                e2e = UUID.randomUUID().toString(),
            )

        val result = buildRequest("EVP", pixKey, securityFixture.cookieAuthOwner)

        result.type shouldBe PixKeyType.EVP
    }

    @Test
    fun `deve retornar o QRCode com os detalhes e apenas dados básicos da chave`() {
        val pixKey = UUID.randomUUID().toString()

        val expiration = getZonedDateTime().plusMonths(2)

        every {
            pixQRCodeParserService.parseQRCodeCacheable(PixCopyAndPaste("QR_CODE"), any())
        } returns Either.Right(
            PixQRCodeDetailsResult(
                pixKeyDetails = PixKeyDetails(
                    key = PixKey(pixKey, PixKeyType.EVP),
                    holder = PixKeyHolder(
                        accountNo = BigInteger("0"),
                        accountDv = "1",
                        ispb = "********",
                        institutionName = "fake name",
                        accountType = AccountType.CHECKING,
                        routingNo = 0,
                    ),
                    owner = PixKeyOwner(
                        name = "owner fake",
                        document = "**********",
                    ),
                ),
                e2e = UUID.randomUUID().toString(),
                qrCodeInfo = PixQrCodeData(
                    type = PixQrCodeType.STATIC,
                    info = "info",
                    additionalInfo = mapOf("chave" to "valor"),
                    pixCopyAndPaste = PixCopyAndPaste("QR_CODE"),
                    pixId = "PIX_ID",
                    fixedAmount = 123,
                    originalAmount = 100,
                    expiration = expiration,
                    automaticPixRecurringDataJson = null,
                ),
            ),
        )

        val result = buildRequestQrCode("QR_CODE", securityFixture.cookieAuthOwner)

        result.ispb shouldBe "********"
        result.institutionName shouldBe "fake name"
        result.document shouldBe "**********"
        result.name shouldBe "owner fake"

        result.pixQrCodeData.type shouldBe PixQrCodeType.STATIC
        result.pixQrCodeData.info shouldBe "info"
        result.pixQrCodeData.additionalInfo shouldBe mapOf("chave" to "valor")
        result.pixQrCodeData.pixId shouldBe "PIX_ID"
        result.pixQrCodeData.fixedAmount shouldBe 123
        result.pixQrCodeData.expiration?.isEqual(expiration) shouldBe true
        result.pixQrCodeData.originalAmount shouldBe 100
    }

    @Test
    fun `should return valid pix Key when email is used with upper case characters`() {
        val pixKey = "<EMAIL>"

        every {
            pixKeyManagement.findKeyDetailsCacheable(PixKey(pixKey.lowercase(), PixKeyType.EMAIL), any())
        } returns PixKeyDetailsResult(
            pixKeyDetails = PixKeyDetails(
                key = PixKey(pixKey.lowercase(), PixKeyType.EMAIL),
                holder = PixKeyHolder(
                    accountNo = BigInteger("0"),
                    accountDv = "1",
                    ispb = "********",
                    institutionName = "fake name",
                    accountType = AccountType.CHECKING,
                    routingNo = 0,
                ),
                owner = PixKeyOwner(
                    name = "owner fake",
                    document = "**********",
                ),
            ),
            e2e = UUID.randomUUID().toString(),
        )

        val result = buildRequest(PixKeyType.EMAIL.name, pixKey, securityFixture.cookieAuthOwner)

        result.type shouldBe PixKeyType.EMAIL
        result.value shouldBe pixKey.lowercase()
    }

    companion object {
        @JvmStatic
        fun errorsProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(PixKeyError.KeyNotFound, HttpStatus.NOT_FOUND),
                Arguments.of(PixKeyError.KeyNotConfirmed, HttpStatus.NOT_FOUND),
                Arguments.of(PixKeyError.MalformedKey, HttpStatus.BAD_REQUEST),
                Arguments.of(PixKeyError.UnknownError, HttpStatus.INTERNAL_SERVER_ERROR),
            )
        }
    }
}