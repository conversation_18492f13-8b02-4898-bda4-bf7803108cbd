package ai.friday.billpayment.eventbus

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillApproved
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillPaymentScheduled
import ai.friday.billpayment.app.eventbus.EventBus
import ai.friday.billpayment.app.eventbus.EventBusMessageProcessor
import ai.friday.billpayment.eventbus.EventBusMessageProcessorTest.Processors.billAddedProcessor
import ai.friday.billpayment.eventbus.EventBusMessageProcessorTest.Processors.billApprovedBadProcessor
import ai.friday.billpayment.eventbus.EventBusMessageProcessorTest.Processors.billPaidProcessor
import io.kotest.matchers.result.shouldBeFailure
import io.kotest.matchers.result.shouldBeSuccess
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.mockk.mockk
import org.junit.jupiter.api.Test

class EventBusMessageProcessorTest {

    @Test
    fun `should process bill paid with BillPaidProcessor`() {
        // given
        val eventBusMessageProcessor = EventBusMessageProcessor(
            messageProcessors = listOf(billPaidProcessor, billAddedProcessor, billApprovedBadProcessor),
        )

        // there is no BillSchedule processor
        val billScheduledEvent = mockk<BillPaymentScheduled>()

        // bad processor that throws an exception
        val billApprovedEvent = mockk<BillApproved>()

        val billAddedEvent = mockk<BillAdded>()
        val billPaidEvent = mockk<BillPaid>()

        // when
        eventBusMessageProcessor.processBillEvent(billAddedEvent)
            // then
            .shouldBeSuccess()
            .shouldBeInstanceOf<EventBusMessageProcessor.PublishResponse.Published>()
            .messageId shouldBe "added"

        eventBusMessageProcessor.processBillEvent(billPaidEvent)
            // then
            .shouldBeSuccess()
            .shouldBeInstanceOf<EventBusMessageProcessor.PublishResponse.Published>()
            .messageId shouldBe "paid"

        eventBusMessageProcessor.processBillEvent(billScheduledEvent)
            // then
            .shouldBeSuccess()
            .shouldBeInstanceOf<EventBusMessageProcessor.PublishResponse.Skipped>()

        eventBusMessageProcessor.processBillEvent(billApprovedEvent)
            // then
            .shouldBeFailure() shouldBe NoStackTraceException("Bad processor")
    }

    private object Processors {
        val billPaidProcessor = object : EventBus.MessageProcessor {
            override fun process(event: BillEvent): Result<EventBus.MessageId> {
                return Result.success(EventBus.MessageId("paid"))
            }

            override fun accepts(event: BillEvent): Boolean = event is BillPaid
        }

        val billAddedProcessor = object : EventBus.MessageProcessor {
            override fun process(event: BillEvent): Result<EventBus.MessageId> {
                return Result.success(EventBus.MessageId("added"))
            }

            override fun accepts(event: BillEvent): Boolean = event is BillAdded
        }

        val billApprovedBadProcessor = object : EventBus.MessageProcessor {
            override fun process(event: BillEvent): Result<EventBus.MessageId> {
                return Result.failure(NoStackTraceException("Bad processor"))
            }

            override fun accepts(event: BillEvent): Boolean = event is BillApproved
        }
    }
}