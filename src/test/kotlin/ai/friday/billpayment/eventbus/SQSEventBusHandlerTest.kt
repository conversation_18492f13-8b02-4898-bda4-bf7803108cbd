package ai.friday.billpayment.eventbus

import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.adapters.messaging.eventbus.SQSEventBusHandler
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.eventbus.EventBusMessageProcessor
import ai.friday.billpayment.billPaid
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import software.amazon.awssdk.services.sqs.model.Message

class SQSEventBusHandlerTest {

    private val processor = mockk<EventBusMessageProcessor>()

    private val handler = SQSEventBusHandler(
        messageProcessor = processor,
        amazonSQS = mockk(),
        amazonSNS = mockk(),
        configuration = mockk(),
        queueName = "queueName",
        topicArn = "topicArn",
    )

    private val messageBody = getObjectMapper().writeValueAsString(billPaid.toBillEventDetailEntity())

    private val message = Message.builder().body(messageBody).build()

    @Test
    fun `should delete message when processor result success`() {
        // when
        every {
            processor.processBillEvent(any())
        } returns Result.success(EventBusMessageProcessor.PublishResponse.Published("idvalue"))

        val response = handler.handleMessage(message)
        response.shouldDeleteMessage shouldBe true

        verify {
            processor.processBillEvent(any())
        }
    }

    @Test
    fun `should delete message when there is no processor for the message`() {
        // when
        every {
            processor.processBillEvent(any())
        } returns Result.success(EventBusMessageProcessor.PublishResponse.Skipped)

        val response = handler.handleMessage(message)
        response.shouldDeleteMessage shouldBe true

        verify {
            processor.processBillEvent(any())
        }
    }

    @Test
    fun `should NOT delete message when processor fails`() {
        // when
        every {
            processor.processBillEvent(any())
        } returns Result.failure(RuntimeException("testerror"))

        assertThrows<RuntimeException> { handler.handleMessage(message) }
            .shouldNotBeNull()
            .message shouldBe "testerror"

        verify {
            processor.processBillEvent(any())
        }
    }
}