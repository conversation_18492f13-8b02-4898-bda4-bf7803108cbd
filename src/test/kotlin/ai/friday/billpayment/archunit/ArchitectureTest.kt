package ai.friday.billpayment.archunit

import ai.friday.billpayment.adapters.dynamodb.BillEventDetailsEntity
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillEventDependency
import ai.friday.billpayment.app.job.AbstractJob
import com.fasterxml.jackson.annotation.JsonTypeName
import com.tngtech.archunit.core.domain.JavaClass
import com.tngtech.archunit.core.domain.JavaClasses
import com.tngtech.archunit.core.domain.JavaModifier
import com.tngtech.archunit.core.importer.ImportOption
import com.tngtech.archunit.core.importer.Location
import com.tngtech.archunit.junit.AnalyzeClasses
import com.tngtech.archunit.junit.ArchIgnore
import com.tngtech.archunit.junit.ArchTest
import com.tngtech.archunit.lang.ArchCondition
import com.tngtech.archunit.lang.ConditionEvents
import com.tngtech.archunit.lang.SimpleConditionEvent
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.classes
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.methods
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.noClasses
import io.micronaut.core.annotation.Generated
import io.micronaut.scheduling.annotation.Async
import io.micronaut.scheduling.annotation.Scheduled
import io.micronaut.tracing.annotation.NewSpan
import java.util.regex.Pattern
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode

@AnalyzeClasses(packages = ["ai.friday.billpayment"], importOptions = [ImportOption.DoNotIncludeTests::class, ExcludeBuildTmp::class])
@Execution(ExecutionMode.CONCURRENT)
internal class ArchitectureTest {

    @ArchTest
    fun `classes that extends BillEvent should reside in a package ai_friday_billpayment_app_bill`(
        importedClasses: JavaClasses,
    ) {
        classes().that()
            .areAssignableTo(BillEvent::class.java)
            .should().resideInAPackage("ai.friday.billpayment.app.bill")
            .check(importedClasses)
    }

    @ArchTest
    fun `classes that extends BillEventDetailsEntity should have fields annotated with @BillEventDependency`(
        importedClasses: JavaClasses,
    ) {
        classes().that()
            .areAssignableTo(BillEventDetailsEntity::class.java)
            .should(billEventDependencyCondition)
            .check(importedClasses)
    }

    @ArchTest
    fun `classes that extends BillEventDetailsEntity should be annotated with @JsonTypeName`(
        importedClasses: JavaClasses,
    ) {
        classes().that().areAssignableTo(BillEventDetailsEntity::class.java)
            .and()
            .areNotAssignableFrom(BillEventDetailsEntity::class.java)
            .should().beAnnotatedWith(JsonTypeName::class.java).check(importedClasses)
    }

    @ArchTest
    fun `nenhum método deve usar a anotação Scheduled`(
        importedClasses: JavaClasses,
    ) {
        methods()
            .should().notBeAnnotatedWith(Scheduled::class.java)
            .check(importedClasses)
    }

    @ArchTest
    fun `classes que extendem AbstractJob não podem ser final`(
        importedClasses: JavaClasses,
    ) {
        classes()
            .that().areAssignableTo(AbstractJob::class.java)
            .should().notHaveModifier(JavaModifier.FINAL)
            .check(importedClasses)
    }

    @ArchTest
    fun `classes com métodos anotadas com NewSpan não podem ser final`(
        importedClasses: JavaClasses,
    ) {
        methods()
            .that().areAnnotatedWith(NewSpan::class.java)
            .should().beDeclaredInClassesThat().doNotHaveModifier(JavaModifier.FINAL)
            .check(importedClasses)
    }

    @ArchTest
    fun `classes that are annotated with @BillEventDependency should have fields annotated with @BillEventDependency`(
        importedClasses: JavaClasses,
    ) {
        classes().that()
            .areAnnotatedWith(BillEventDependency::class.java)
            .should(billEventDependencyCondition)
            .check(importedClasses)
    }

    @ArchTest
    @ArchIgnore
    fun `micronaut dependencies should not reside inside domain files`(importedClasses: JavaClasses) {
        noClasses()
            .that().resideInAPackage("ai.friday.billpayment.app.(*)")
            .and().areNotAnnotatedWith(Generated::class.java)
            .should()
            .dependOnClassesThat().resideInAnyPackage("..micronaut..")
            .check(importedClasses)
    }

    @ArchTest
    fun `nenhum método deve usar a anotação Async`(
        importedClasses: JavaClasses,
    ) {
        methods()
            .should().notBeAnnotatedWith(Async::class.java)
            .check(importedClasses)
    }

    @ArchTest
    fun testHexagonal(importedClasses: JavaClasses) {
        noClasses().that()
            .resideInAnyPackage("ai.friday.billpayment.app", "ai.friday.billpayment.app.(**)")
            .should().dependOnClassesThat()
            .resideInAnyPackage("ai.friday.billpayment.adapters", "ai.friday.billpayment.adapters.(**)")
            .check(importedClasses)
    }

    private val billEventDependencyCondition = BillEventDependencyCondition()
}

private class BillEventDependencyCondition : ArchCondition<JavaClass>("only have fields annotated with @BillEventDependency") {
    override fun check(item: JavaClass, events: ConditionEvents) {
        item.fields.filter { field ->
            field.rawType.fullName.startsWith("ai.friday.billpayment") &&
                !field.rawType.isAnnotatedWith(BillEventDependency::class.java) &&
                field.name != "Companion"
        }.forEach { field ->
            val message = String.format(
                "Field %s is not annotated with @BillEventDependency",
                field.fullName,
            )
            events.add(SimpleConditionEvent.violated(field, message))
        }
    }
}

class ExcludeBuildTmp : ImportOption {
    private val pattern = Pattern.compile(".*/build/tmp/kapt./.*")
    override fun includes(location: Location?): Boolean {
        return !pattern.matcher(location.toString()).matches()
    }
}