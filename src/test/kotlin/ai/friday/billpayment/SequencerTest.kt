package ai.friday.billpayment

import io.kotest.matchers.ints.shouldBeInRange
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

internal class SequencerTest {
    private val sequencer = Sequencer(10)

    @OptIn(DelicateCoroutinesApi::class)
    @Test
    internal fun `should run with multiple coroutines`() {
        runBlocking {
            repeat(50_000) {
                GlobalScope.launch {
                    val iterator: Int = sequencer.next()
                    iterator.shouldBeInRange(1..10)
                }
            }
        }
    }
}