package ai.friday.billpayment

import io.kotest.matchers.ints.shouldBeGreaterThan
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpMethod
import io.micronaut.http.netty.NettyHttpHeaders
import java.net.URI
import java.util.Optional
import org.junit.jupiter.api.Test

internal class HttpUtilTest {
    @Test
    internal fun `should create basic get request`() {
        with(request<Unit>(HttpMethod.GET, "/foo")) {
            method shouldBe HttpMethod.GET

            uri shouldBe URI("/foo")

            (headers as NettyHttpHeaders).nettyHeaders.size() shouldBeGreaterThan 0
        }
    }

    @Test
    internal fun `should create get request with query parameters`() {
        with(request<Unit>(HttpMethod.GET, "/foo", qs = mapOf("ham" to 1))) {
            method shouldBe HttpMethod.GET
            uri shouldBe URI("/foo?ham=1")

            with(headers as NettyHttpHeaders) {
                nettyHeaders.size() shouldBeGreaterThan 0
            }
        }
    }

    @Test
    internal fun `should create get request with multiple query parameters`() {
        with(request<Unit>(HttpMethod.GET, "/foo", qs = mapOf("ham" to 1, "baz" to false, "qux" to "fu"))) {
            method shouldBe HttpMethod.GET

            uri shouldBe URI("/foo?ham=1&baz=false&qux=fu")

            with(headers as NettyHttpHeaders) {
                nettyHeaders.size() shouldBeGreaterThan 0
            }
        }
    }

    @Test
    internal fun `should create get request with headers parameters`() {
        with(request<Unit>(HttpMethod.GET, "/foo", headers = mapOf("ham" to 1))) {
            method shouldBe HttpMethod.GET

            uri shouldBe URI("/foo")

            with(headers as NettyHttpHeaders) {
                nettyHeaders.size() shouldBeGreaterThan 0
                nettyHeaders["ham"] shouldBe "1"
            }
        }
    }

    @Test
    internal fun `should create get request with queries and headers parameters`() {
        with(request<Unit>(HttpMethod.GET, "/foo", qs = mapOf("baz" to "qux"), headers = mapOf("ham" to 1))) {
            method shouldBe HttpMethod.GET
            uri shouldBe URI("/foo?baz=qux")

            with(headers as NettyHttpHeaders) {
                nettyHeaders.size() shouldBeGreaterThan 0
                nettyHeaders["ham"] shouldBe "1"
            }
        }
    }

    @Test
    internal fun `should create basic post request`() {
        with(request(HttpMethod.POST, "/foo", body = FooRequest(foo = "bar"))) {
            method shouldBe HttpMethod.POST
            uri shouldBe URI("/foo")
            body shouldBe Optional.of(FooRequest(foo = "bar"))

            with(headers as NettyHttpHeaders) {
                nettyHeaders.size() shouldBeGreaterThan 0
            }
        }
    }

    @Test
    internal fun `should modify request created with custom functions`() {
        val req = request<Nothing>(HttpMethod.GET, "/foo") {
            return@request it.run { bearer("abc"); this.contentType("qwe") }
        }

        with(req) {
            method shouldBe HttpMethod.GET
            uri shouldBe URI("/foo")

            with(headers as NettyHttpHeaders) {
                nettyHeaders.size() shouldBe 3
                nettyHeaders["Authorization"] shouldBe "Bearer abc"
                nettyHeaders["Content-Type"] shouldBe "qwe"
            }
        }
    }
}