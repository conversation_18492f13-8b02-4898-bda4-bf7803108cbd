package ai.friday.billpayment

import com.github.tomakehurst.wiremock.WireMockServer
import com.github.tomakehurst.wiremock.client.ResponseDefinitionBuilder
import com.github.tomakehurst.wiremock.client.WireMock
import com.github.tomakehurst.wiremock.core.WireMockConfiguration
import com.github.tomakehurst.wiremock.matching.RequestPatternBuilder
import io.micronaut.http.MediaType

fun mockServer() = WireMockServer(WireMockConfiguration.wireMockConfig().dynamicPort()).also { it.start() }

fun aJsonResponse(str: String? = null, status: Int = 200) = WireMock.aResponse().json().withStatus(status).withBody(str)
fun aJsonErrorResponse(str: String? = null, status: Int = 500) =
    WireMock.aResponse().json().withStatus(status).withBody(str)

fun ResponseDefinitionBuilder.json() = this.headers("Content-Type" to MediaType.APPLICATION_JSON)

fun ResponseDefinitionBuilder.headers(vararg args: Pair<String, Any>) =
    args.forEach { withHeader(it.first, "${it.second}") }.let { this }

fun RequestPatternBuilder.assertHeaders(vararg args: Pair<String, Any>) =
    args.forEach { withHeader(it.first, WireMock.equalTo("${it.second}")) }.let { this }

fun RequestPatternBuilder.assertJsonBody(vararg args: Pair<String, Any?>) = args.forEach {
    withRequestBody(WireMock.matchingJsonPath("$.${it.first}", WireMock.equalTo("${it.second}")))
}.let { this }