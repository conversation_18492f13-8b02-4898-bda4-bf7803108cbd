package ai.friday.billpayment

import kotlin.test.expect
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

internal class LoggerUtilTest {

    @Nested
    @DisplayName("When using andAppendMasked")
    inner class AndAppendMasked {

        private lateinit var marker: LogstashMarker
        private val value = "123e4567-e89b-12d3-a456-************"

        @BeforeEach
        fun setUp() {
            marker = Markers.empty()
        }

        @Test
        fun `should show the exact amount of characters if fromStart and fromEnd are positive`() {
            marker.andAppendMasked("uuid", value, '*', 5, 10)

            expect("uuid=123e4*********************6614174000") {
                marker.toString()
            }
        }

        @Test
        fun `should show the entire string if fromStart plus fromEnd are greater than the string length`() {
            marker.andAppendMasked("uuid", value, '*', 100, 100)

            expect("uuid=123e4567-e89b-12d3-a456-************") {
                marker.toString()
            }
        }

        @Test
        fun `should mask the entire string if both fromStart and fromEnd are 0`() {
            marker.andAppendMasked("uuid", value, '*', 0, 0)

            expect("uuid=************************************") {
                marker.toString()
            }
        }

        @Test
        fun `should use 0 if fromStart or fromEnd are negative`() {
            marker.andAppendMasked("uuid", value, '*', -10, -20)

            expect("uuid=************************************") {
                marker.toString()
            }
        }

        @Test
        fun `should not append anything if the value is null or empty`() {
            marker.andAppendMasked("uuid", null, '*', 4, 4)
            marker.andAppendMasked("uuid", "", '*', 4, 4)

            expect("") {
                marker.toString()
            }
        }

        @Test
        fun `should use provided mask parameters if none are provided`() {
            marker.andAppendMasked("uuid", value, '#', 5, 5)

            expect("uuid=123e4##########################74000") {
                marker.toString()
            }
        }

        @Test
        fun `should use default parameters if none are provided`() {
            marker.andAppendMasked("uuid", value)

            expect("uuid=123e****************************4000") {
                marker.toString()
            }
        }
    }
}