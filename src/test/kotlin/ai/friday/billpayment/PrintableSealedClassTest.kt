import ai.friday.billpayment.PrintableSealedClassV2

/*package ai.friday.billpayment

import ai.friday.billpayment.app.bill.BillSynchronizationStatus
import ai.friday.billpayment.app.dda.DDAExecutionResult
import ch.qos.logback.classic.Level
import ch.qos.logback.classic.Logger
import ch.qos.logback.classic.LoggerContext
import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.core.OutputStreamAppender
import io.kotest.extensions.system.OverrideMode
import io.kotest.extensions.system.withEnvironment
import io.kotest.matchers.ints.shouldBeGreaterThan
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import java.io.ByteArrayOutputStream
import java.nio.charset.StandardCharsets
import java.util.stream.Stream
import net.logstash.logback.encoder.LogstashEncoder
import net.logstash.logback.marker.Markers.append
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.Arguments.arguments
import org.junit.jupiter.params.provider.MethodSource
import org.slf4j.LoggerFactory


@Disabled
internal class PrintableSealedClassTest {
    @ParameterizedTest
    @MethodSource("sealedInstanceInputs")
    fun `ao invocar o logger deve formatar as sealed instances adequadamente`(
        instance: Any?,
        expectation: String,
    ) {
        withEnvironment("LOGGER_LEVELS_ROOT", "INFO", OverrideMode.SetOrOverride) {
            System.getenv("LOGGER_LEVELS_ROOT") shouldBe "INFO" // System environment overridden!
            // setup do appender, logger, etc - start
            val outContent = ByteArrayOutputStream()
            val context = LoggerFactory.getILoggerFactory() as LoggerContext

            val encoder = LogstashEncoder()
            encoder.context = context
            encoder.start()

            val appender = OutputStreamAppender<ILoggingEvent>()
            appender.name = "TestCustomAppender"
            appender.context = context
            appender.encoder = encoder // <-- must be set before outputstream
            appender.outputStream = outContent
            appender.start()

            val logger = LoggerFactory.getLogger("TestCustomAppender") as Logger
            logger.isAdditive = false
            logger.level = Level.INFO
            logger.detachAndStopAllAppenders()
            logger.addAppender(appender)
            // setup - end

            logger.info(append("result", instance), "test")

            outContent.flush()
            outContent.toByteArray().size shouldBeGreaterThan 0
            outContent.toString(StandardCharsets.UTF_8) shouldContain expectation
            outContent.close()
        }
    }

    companion object {
        @JvmStatic
        fun sealedInstanceInputs(): Stream<Arguments> = Stream.of(
            arguments(DDAExecutionResult.NotAdded(), "DDAExecutionResult\$NotAdded"),
            arguments(DDAExecutionResult.Added, "DDAExecutionResult\$Added"),
            arguments(DDAExecutionResult.NotUpdated(), "DDAExecutionResult\$NotUpdated"),
            arguments(DDAExecutionResult.AccountNotFound, "DDAExecutionResult\$AccountNotFound"),
            arguments(DDAExecutionResult.Error(), "DDAExecutionResult\$Error"),
            arguments(DDAExecutionResult.Error(NoStackTraceException()), "Error"),
            arguments(
                DDAExecutionResult.Updated(BillSynchronizationStatus.BillNotModified),
                "BillSynchronizationStatus\$BillNotModified",
            ),
            arguments(null, "null"),
            arguments(TestData.Test1("value1"), "TestData\$Test1"),
            arguments(TestData.Test2("value2"), "TestData\$Test2"),
        )
    }
}
*/

private sealed class TestData() : PrintableSealedClassV2() {
    data class Test1(val arg1: String) : TestData()
    data class Test2(val arg2: String) : TestData()
}