package ai.friday.billpayment

import DynamoDBUtils
import java.net.URI
import java.util.UUID
import net.javacrumbs.shedlock.core.LockProvider
import net.javacrumbs.shedlock.provider.dynamodb2.DynamoDBLockProvider
import org.junit.jupiter.api.extension.AfterEachCallback
import org.junit.jupiter.api.extension.BeforeAllCallback
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.extension.ExtensionContext
import org.junit.jupiter.api.extension.ParameterContext
import org.junit.jupiter.api.extension.ParameterResolutionException
import org.junit.jupiter.api.extension.ParameterResolver
import org.junit.platform.commons.support.AnnotationSupport
import org.testcontainers.containers.GenericContainer
import org.testcontainers.utility.DockerImageName
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.ProvisionedThroughput

@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
@ExtendWith(DynamoContainerExtension::class)
annotation class DynamoContainerTest(
    /**
     * Returns: initialize db with default tables BeforeAll
     */
    val initializeDB: Boolean = true,
    /**
     * Returns: refresh all tables from database - AfterEach
     */
    val refreshDB: Boolean = true,
    /**
     * Returns: support LockProvider parameter resolution - BeforeAll
     */
    val locker: Boolean = false,
    /**
     * Returns: version of DynamoLocal image used - BeforeAll
     */
    val version: String = "1.16.0",
)

@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.VALUE_PARAMETER)
annotation class DynamoContainerPort

data class DynamoContainerTestData(
    val initializeDB: Boolean,
    val refreshDB: Boolean,
    val locker: Boolean,
    val version: String,
)

private class DynamoContainerExtension : BeforeAllCallback, ParameterResolver, AfterEachCallback {
    private var dynamoPort: Int = 0

    private lateinit var basicSync: DynamoDbClient
    private lateinit var basicAsync: DynamoDbAsyncClient

    private lateinit var enhancedSync: DynamoDbEnhancedClient
    private lateinit var enhancedAsync: DynamoDbEnhancedAsyncClient

    private var locker: LockProvider? = null

    private lateinit var data: DynamoContainerTestData

    override fun beforeAll(context: ExtensionContext) {
        this.data = this.bindParameters(context.requiredTestClass) ?: return

        initializeDB()
    }

    override fun supportsParameter(
        parameterContext: ParameterContext,
        extensionContext: ExtensionContext,
    ): Boolean {
        val isValid =
            when (parameterContext.parameter.type) {
                DynamoDbClient::class.java,
                DynamoDbAsyncClient::class.java,
                DynamoDbEnhancedClient::class.java,
                DynamoDbEnhancedAsyncClient::class.java,
                -> true

                LockProvider::class.java -> if (locker != null) true else throw ParameterResolutionException("lock parameter must be set as true")

                else -> false
            }

        if (isValid) return true

        if (parameterContext.isAnnotated(DynamoContainerPort::class.java)) return true

        return false
    }

    @Suppress("IMPLICIT_CAST_TO_ANY")
    override fun resolveParameter(
        parameterContext: ParameterContext,
        extensionContext: ExtensionContext,
    ): Any? {
        val instance =
            when (parameterContext.parameter.type) {
                DynamoDbClient::class.java -> this.basicSync
                DynamoDbAsyncClient::class.java -> this.basicAsync
                DynamoDbEnhancedClient::class.java -> this.enhancedSync
                DynamoDbEnhancedAsyncClient::class.java -> this.enhancedAsync
                LockProvider::class.java -> this.locker
                else -> null
            }

        if (instance != null) return instance

        if (parameterContext.isAnnotated(DynamoContainerPort::class.java)) return dynamoPort

        return null
    }

    override fun afterEach(context: ExtensionContext) {
        if (!this.data.refreshDB) return

        val tables = this.basicSync.listTables()
        tables.tableNames().forEach { tableName ->
            this.basicSync.deleteTable { it.tableName(tableName) }
        }

        initializeDatabase()
    }

    private fun initializeDB() {
        val image = DockerImageName.parse("amazon/dynamodb-local:${data.version}")
        val container = GenericContainer(image).withExposedPorts(8000)
        container.start()

        val port = container.firstMappedPort
        this.dynamoPort = port

        val (basicSync, basicAsync) = createBasicDynamoCli(port)
        val (enhancedSync, enhancedAsync) = createEnhancedCli(basicSync, basicAsync)

        this.basicSync = basicSync
        this.basicAsync = basicAsync
        this.enhancedSync = enhancedSync
        this.enhancedAsync = enhancedAsync

        with(this.data) {
            if (initializeDB) initializeDatabase()
            if (locker) initializeLocker()
        }
    }

    private fun createBasicDynamoCli(port: Int): Pair<DynamoDbClient, DynamoDbAsyncClient> {
        val endpoint = URI.create("http://127.0.0.1:$port")
        val basicCredentials = AwsBasicCredentials.create(UUID.randomUUID().toString(), UUID.randomUUID().toString())
        val credentials = StaticCredentialsProvider.create(basicCredentials)
        val region = Region.US_EAST_1

        val sync =
            DynamoDbClient.builder()
                .endpointOverride(endpoint)
                .credentialsProvider(credentials)
                .region(region)
                .build()

        val async =
            DynamoDbAsyncClient.builder()
                .endpointOverride(endpoint)
                .credentialsProvider(credentials)
                .region(region)
                .build()

        return sync to async
    }

    private fun createEnhancedCli(
        basicSync: DynamoDbClient,
        basicAsync: DynamoDbAsyncClient,
    ): Pair<DynamoDbEnhancedClient, DynamoDbEnhancedAsyncClient> {
        val enhancedSync =
            DynamoDbEnhancedClient.builder()
                .dynamoDbClient(basicSync)
                .build()

        val enhancedAsync =
            DynamoDbEnhancedAsyncClient.builder()
                .dynamoDbClient(basicAsync)
                .build()

        return enhancedSync to enhancedAsync
    }

    private fun bindParameters(clazz: Class<*>) =
        AnnotationSupport.findAnnotation(clazz, DynamoContainerTest::class.java)
            .map {
                DynamoContainerTestData(
                    initializeDB = it.initializeDB,
                    refreshDB = it.refreshDB,
                    locker = it.locker,
                    version = it.version,
                )
            }.orElse(null)

    private fun initializeDatabase() =
        with(basicSync) {
            DynamoDBUtils.createBillPaymentTable(this)
            DynamoDBUtils.createBillEventTable(this)
            DynamoDBUtils.createBillsSearchTable(this)
            DynamoDBUtils.createOpenFinanceDataTable(this)
        }

    private fun initializeLocker() {
        locker = DynamoDBLockProvider(basicSync, "Shedlock")

        val throughput = ProvisionedThroughput.builder().readCapacityUnits(1_000).writeCapacityUnits(1_000).build()
        net.javacrumbs.shedlock.provider.dynamodb2.DynamoDBUtils.createLockTable(basicSync, "Shedlock", throughput)
    }
}