/*
package ai.friday.billpayment.adapters.intercom

import ai.friday.billpayment.adapters.instrumentation.RegisterInstrumentationRepository
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.integrations.CrmContact
import ai.friday.billpayment.app.integrations.TemporaryCrmContact
import ai.friday.billpayment.app.integrations.TemporaryCrmContactMinimal
import ai.friday.billpayment.app.register.instrumentation.DefaultCrmService
import ai.friday.billpayment.app.register.instrumentation.RegisterInstrumentationEvent
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateFormatBR
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.mockk
import java.time.Duration
import java.time.Period
import java.util.UUID
import java.util.concurrent.TimeUnit
import org.awaitility.Awaitility.await
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
@MicronautTest(environments = [FRIDAY_ENV])
class IntercomAdapterIntegrationTest(private val intercomAdapter: IntercomAdapter) {

    private val randomName: String = UUID.randomUUID().toString()
    private val currentAccountId = AccountId("ACCOUNT-TEST-$randomName")
    private val currentEmailAddress = EmailAddress("emailteste-$<EMAIL>")
    private val registerInstrumentationRepository = RegisterInstrumentationRepository(intercomAdapter)

    val timeout: Duration = Duration.ofSeconds(60)
    private val interval: Duration = Duration.ofSeconds(3)

    private val crmService = DefaultCrmService(
        crmRepository = intercomAdapter,
        accountRepository = mockk(),
        accountRegisterRepository = mockk(),
        walletRepository = mockk(),
        inAppSubscriptionRepository = mockk(),
    )

    @Test
    fun testEvent() {
        registerInstrumentationRepository.publishEvent(
            RegisterInstrumentationEvent.AccountActivated(
                accountId = AccountId("ACCOUNT-ce4d6c82-422a-4fb3-aa00-5c02110bb846"),
                registrationType = RegistrationType.FULL,
                billsCount = 1,
            ),
        )
    }

    @Test
    fun testAll() {
        println("currentAccountId: $currentAccountId")
        println("currentEmailAddress: $currentEmailAddress")

        var contact = CrmContact(
            emailAddress = currentEmailAddress,
            accountId = currentAccountId,
            role = Role.GUEST,
            groups = listOf(AccountGroup.ALPHA, AccountGroup.BETA),
            accountType = UserAccountType.FULL_ACCOUNT,
            isCNPJAccount = true,
            cnpjWallets = listOf(WalletId("fakeWalletIdCNPJ")),
            cpfWallets = listOf(WalletId("fakeWalletIdCPF")),
            otherMembersOnWallets = listOf(AccountId("OTHER MEMBER")),
            subscriptionType = SubscriptionType.PIX,
        )

        intercomAdapter.upsertContact(contact)

        awaitForIntercom(currentAccountId, "Encontrou o contato no Intercom") { true }
        awaitForIntercom(currentAccountId, "Esperou encontrar novamete no Intercom, pois às vezes desaparece") { true }
        awaitForIntercom(
            currentAccountId,
            "Esperou encontrar novamete no Intercom, pois às vezes desaparece de novo",
        ) { true }

        // Publica um evento

        val customParams = mapOf<String, Any?>(
            "long" to 1L,
            "integer" to 1,
            "double" to 2 / 3,
            "boolean" to true,
            "boolean2" to false,
            "string" to "string",
            "null" to null,
        )

        intercomAdapter.publishEvent(
            currentAccountId,
            "novo_evento_de_teste",
            customParams,
        )

        println("Publicou o evento")

        // Atualiza os grupos
        contact = contact.copy(groups = listOf(AccountGroup.BETA))
        intercomAdapter.upsertContact(contact)
        awaitForIntercom(currentAccountId, "Usuario com 1 grupo") {
            it.groups.size == 1
        }

        // Atualiza os grupos
        contact = contact.copy(groups = listOf())
        intercomAdapter.upsertContact(contact)
        awaitForIntercom(currentAccountId, "Remove todos os grupos do usuário") {
            it.groups.isEmpty()
        }

        // Atualiza o nome

        contact = contact.copy(name = "agora tem nome")

        intercomAdapter.upsertContact(contact)

        awaitForIntercom(currentAccountId, "Atualizou o nome para o novo valor") {
            it.name == contact.name
        }

        // Atualiza o telefone

        contact = contact.copy(mobilePhone = MobilePhone("*************"))

        intercomAdapter.upsertContact(contact)

        awaitForIntercom(currentAccountId, "Atualizou o telefone para o novo valor") {
            it.mobilePhone?.msisdn == contact.mobilePhone!!.msisdn
        }

        // Atualiza o documento

        contact = contact.copy(document = "***********")

        intercomAdapter.upsertContact(contact)

        awaitForIntercom(currentAccountId, "Atualizou o documento para o novo valor") {
            it.document == contact.document
        }

        // Atualiza a role

        contact = contact.copy(role = Role.OWNER)

        intercomAdapter.upsertContact(contact)

        awaitForIntercom(currentAccountId, "Atualizou a role") {
            it.role == contact.role
        }

        contact = contact.copy(
            otherMembersOnWallets = listOf(AccountId("OTHER MEMBER 2"), AccountId("OTHER MEMBER 3")),
            cnpjWallets = listOf(WalletId("fakeWalletIdCNPJ 2"), WalletId("fakeWalletIdCNPJ 3")),
            cpfWallets = listOf(WalletId("fakeWalletIdCPF 2"), WalletId("fakeWalletIdCPF 3")),
            isCNPJAccount = true,
        )
        awaitForIntercom(currentAccountId, "atualizou carteiras, membros e virou cnpj") {
            it.otherMembersOnWallets?.size == 2 &&
                it.cnpjWallets?.size == 2 && it.cpfWallets?.size == 2 && it.isCNPJAccount == true
        }

        // 'Remove' o usuário
        crmService.removeContact(currentAccountId)
        awaitForIntercom(currentAccountId, "Marcou usuário como removido") {
            it.removed
        }
    }

    @Test
    fun `evento para usuario sem accountId`() {
        println("currentAccountId: $currentAccountId")
        println("currentEmailAddress: $currentEmailAddress")

        intercomAdapter.createContact(
            TemporaryCrmContactMinimal(
                emailAddress = currentEmailAddress,
                mobilePhone = null,
            ),
        )
        intercomAdapter.publishEvent(
            currentEmailAddress,
            eventName = "external_coupon_created",
            customParams = mapOf(
                "coupon_code" to "ABCDEF",
                "external_id" to "external_id_1",
                "end_date" to getLocalDate().plusMonths(6).format(dateFormatBR),
                "end_date_iso" to getLocalDate().plusMonths(6).format(dateFormat),
                "external_offer_id" to "BLACK_DESENROLATION_2_ANOS",
                "internal_offer_id" to Period.ofMonths(6).toString(),
            ),
        )
    }

    private fun awaitForIntercom(
        accountId: AccountId,
        message: String,
        condition: (it: CrmContact) -> Boolean,
    ) {
        val beforeWait = getZonedDateTime()

        await(message).atMost(timeout).pollInterval(interval).until {
            val contact = intercomAdapter.findContact(accountId)
            contact != null && condition(contact)
        }.also {
            val timeDiff = getZonedDateTime().toInstant().toEpochMilli() - beforeWait.toInstant().toEpochMilli()

            println("Tempo de espera: ${TimeUnit.SECONDS.convert(timeDiff, TimeUnit.MILLISECONDS)} segundos")
            println(message)
        }
    }
    */
/*
        @Test
        fun fixUsers() {
            val jsonString = Thread.currentThread().contextClassLoader.getResourceAsStream("users-to-fix.json")
            val usersToFix =
                jacksonObjectMapper().readerForListOf(UserAccountRegisterTO::class.java)
                    .readValue<List<UserAccountRegisterTO>>(jsonString)

            usersToFix.forEach { accountRegisterTO ->
                val crmContact = CrmContact(
                    accountId = AccountId(accountRegisterTO.accountId),
                    emailAddress = EmailAddress(accountRegisterTO.email),
                    role = Role.OWNER,
                    name = accountRegisterTO.name,
                    mobilePhone = MobilePhone(accountRegisterTO.phone),
                    document = accountRegisterTO.document,
                )

                println(crmContact)
                // intercomAdapter.upsertContact(crmContact)
            }
        }*//*


    @Test
    fun unarchive() {
        intercomAdapter.unarchive("61994517fc64e828244392f5")
    }

    @Test
    fun delete() {
        intercomAdapter.delete("62f560527313be662db132ef")
    }

    @Test
    fun createContact() {
        val temporaryCrmContact = TemporaryCrmContact(
            emailAddress = EmailAddress("<EMAIL>"),
            mobilePhone = MobilePhone("+*************"),
            accountId = AccountId(ACCOUNT_ID),
        )
        crmService.createContact(
            temporaryCrmContact,
        )

        val contact = crmService.findContact(temporaryCrmContact.accountId)

        contact.shouldNotBeNull()
        assertSoftly {
            contact.emailAddress shouldBe temporaryCrmContact.emailAddress // FIXME nao esta voltando o email ?
            contact.mobilePhone shouldBe temporaryCrmContact.mobilePhone // FIXME nao esta voltando o telefone ?
            contact.accountId shouldBe temporaryCrmContact.accountId
        }
    }
}*/