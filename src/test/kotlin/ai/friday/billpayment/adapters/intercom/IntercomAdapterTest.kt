package ai.friday.billpayment.adapters.intercom

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.integration.ACCOUNT_ID
import io.kotest.matchers.shouldBe
import io.mockk.mockk
import org.junit.jupiter.api.Test

internal class IntercomAdapterTest {
    private val configuration =
        IntercomConfiguration().apply {
            webIdVerificationSecret = "uEQeg-W3ddKuYirm3LMXpOh-ePWjbSdjXCw0SdcR"
            iosIdVerificationSecret = "QCKelP1l1XhDIN49NT6X3ju7ECD1xwH8ORfs6FGk"
            androidIdVerificationSecret = "Q7sS4G6eVmbdYJO42Op-bWcrvV7xW4pRSwp6019Y"
        }
    private val intercomAdapter = IntercomAdapter(httpClient = mockk(), configuration = configuration)

    @Test
    fun calculateUserHashes() {
        val (web, ios, android) = intercomAdapter.calculateUserHashes(AccountId((ACCOUNT_ID)))
        web shouldBe "d4b592f8d8bbe272e95ff1bd3444e602999c87bca5f8197842529be0c4d824f9"
        ios shouldBe "ce5a3cd97332c6c60b5411d7cf025e1027b5b95623e4e66e3122c398d7f723bd"
        android shouldBe "f2aaf3291543c77ac14b99625cc4fd4c6bd060568152b8d864a250d1bddf0e8e"
    }
}