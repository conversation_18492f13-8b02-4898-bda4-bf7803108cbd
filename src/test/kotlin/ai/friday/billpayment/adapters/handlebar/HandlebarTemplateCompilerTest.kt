package ai.friday.billpayment.adapters.handlebar

import ai.friday.billpayment.ByteWrapper
import ai.friday.billpayment.PDF_CONVERTER
import ai.friday.billpayment.accountRegisterDataMissingAcceptedAt
import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.DeclarationOfResidencyForm
import ai.friday.billpayment.app.DeclarationOfResidencySignature
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.GIGU_ENV
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.PepQuery
import ai.friday.billpayment.app.account.PepQueryResult
import ai.friday.billpayment.app.account.PoliticallyExposed
import ai.friday.billpayment.app.account.documentDateFormat
import ai.friday.billpayment.app.account.generateSignatureKey
import ai.friday.billpayment.app.account.getCPF
import ai.friday.billpayment.app.account.getName
import ai.friday.billpayment.app.account.shortDateFormat
import ai.friday.billpayment.app.account.toContractForm
import ai.friday.billpayment.app.account.toFullAddress
import ai.friday.billpayment.app.balance.Balance
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.documentscan.DocumentScanDigitalSpoofResult
import ai.friday.billpayment.app.documentscan.DocumentScanDigitalSpoofStatus
import ai.friday.billpayment.app.documentscan.DocumentScanFaceResult
import ai.friday.billpayment.app.documentscan.DocumentScanFaceStatus
import ai.friday.billpayment.app.documentscan.DocumentScanOcrResult
import ai.friday.billpayment.app.documentscan.DocumentScanResult
import ai.friday.billpayment.app.documentscan.DocumentScanTextResult
import ai.friday.billpayment.app.documentscan.DocumentScanTextStatus
import ai.friday.billpayment.app.integrations.StatementTemplateForm
import ai.friday.billpayment.app.liveness.LivenessEnrollmentVerification
import ai.friday.billpayment.app.payment.buildFormattedAmount
import ai.friday.billpayment.app.payment.buildFormattedHeaderDateTime
import ai.friday.billpayment.app.payment.formatDocument
import ai.friday.billpayment.app.payment.formatZipCode
import ai.friday.billpayment.app.payment.maskDocument
import ai.friday.billpayment.app.register.kyc.KycDossier
import ai.friday.billpayment.app.register.kyc.KycDossierEmail
import ai.friday.billpayment.app.register.kyc.KycDossierMepQuery
import ai.friday.billpayment.app.register.kyc.KycDossierOfficialDocumentData
import ai.friday.billpayment.app.register.kyc.KycDossierPhone
import ai.friday.billpayment.app.register.kyc.KycDossierSanctionType
import ai.friday.billpayment.app.register.kyc.KycDossierSanctions
import ai.friday.billpayment.app.register.kyc.KycDossierTaxIdRegion
import ai.friday.billpayment.app.statement.StatementItem
import ai.friday.billpayment.app.utils.TemplateHelper
import ai.friday.billpayment.createEmailTemplatesConfiguration
import ai.friday.billpayment.internalBankAccount
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.io.File
import java.io.FileOutputStream
import java.time.Duration
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime
import kotlin.time.DurationUnit
import kotlin.time.toDuration
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

internal class HandlebarTemplateCompilerTest {

    private val knownEnvironments = listOf(FRIDAY_ENV, ME_POUPE_ENV, GIGU_ENV)
    private val templatesConfiguration = createEmailTemplatesConfiguration(environment = knownEnvironments[2])

    private val handlebarTemplateCompiler = HandlebarTemplateCompiler(templatesConfiguration)

    private val accountRegisterData = accountRegisterDataMissingAcceptedAt

    private val statementTemplateCompiler = FridayStatementCompiler(templatesConfiguration, PDF_CONVERTER)

    private val userBirthDate = accountRegisterData.documentInfo!!.birthDate

    private val creationDate =
        getZonedDateTime().minusDays(60).toLocalDateTime()

    private val recentCreationDate =
        getZonedDateTime().minusDays(10).toLocalDateTime()

    @Test
    fun emptyReport() {
        val kycDossier = KycDossier(
            taxId = KycDossierTaxIdRegion(region = "SP-RJ"),
            sanctions = listOf(),
            globalSanctionsList = listOf(),
            pep = PepQuery(at = ZonedDateTime.now(), result = PepQueryResult.NOT_PEP),
            gender = null,
            motherName = null,
            fatherName = null,
            phones = listOf(),
            emails = listOf(),
            mep = KycDossierMepQuery.empty(),
            officialData = KycDossierOfficialDocumentData(
                provider = "Receita Federal",
                name = "Nome na receita",
                birthDate = LocalDate.now(),
                hasObitIndication = false,
                regular = true,
                socialName = null,
                deathYear = null,
                status = "Regular",
            ),
        )

        buildKycReport(kycDossier, accountRegisterData, "kyc_empty")
    }

    @Test
    fun declarationOfResidency() {
        knownEnvironments.forEach {
            buildDeclarationOfResidency(accountRegisterData, "declaration-of-residency-$it")
        }
    }

    @Test
    fun fullMatchReport() {
        val accountRegisterData = accountRegisterData.copy(
            livenessEnrollmentVerification = LivenessEnrollmentVerification(
                duplications = LivenessEnrollmentVerification.Result.Verified(
                    accountIds = listOf(
                        AccountId(
                            "AccountId-1231241-**********-1",
                        ),
                    ),
                ),
                fraudIndications = LivenessEnrollmentVerification.Result.Verified(
                    accountIds = listOf(
                        AccountId("AccountId-1231241-**********-1"),
                    ),
                ),
            ),
        )
        val kycDossier = KycDossier(
            taxId = KycDossierTaxIdRegion(region = "SP-RJ"),
            motherName = accountRegisterData.documentInfo!!.motherName,
            fatherName = accountRegisterData.documentInfo!!.fatherName,
            phones = listOf(
                KycDossierPhone(
                    number = accountRegisterData.mobilePhone!!.msisdn.takeLast(9),
                    areaCode = accountRegisterData.mobilePhone!!.msisdn.takeLast(11).take(2),
                    countryCode = accountRegisterData.mobilePhone!!.msisdn.takeLast(13).take(2),
                    createdAt = creationDate.toLocalDate(),
                ),
            ),
            emails = listOf(
                KycDossierEmail(value = accountRegisterData.emailAddress, createdAt = creationDate.toLocalDate()),
            ),
            sanctions = listOf(
                KycDossierSanctions(
                    source = "interpol",
                    type = KycDossierSanctionType.ArrestWarrants,
                    matchRate = 100,
                    nameUniquenessScore = 1.0,
                ),
                KycDossierSanctions(
                    source = "Conselho Nacional de Justiça",
                    type = KycDossierSanctionType.Corruption,
                    matchRate = 98,
                    nameUniquenessScore = 1.0,
                ),
            ),
            globalSanctionsList = listOf("FAKE A", "FAKE B", "FAKE C"),
            pep = PepQuery(at = ZonedDateTime.now(), result = PepQueryResult.DIRECT_PEP),
            gender = null,
            mep = KycDossierMepQuery.empty(),
            officialData = KycDossierOfficialDocumentData(
                provider = "Receita Federal",
                name = "Nome na receita",
                birthDate = LocalDate.now(),
                hasObitIndication = false,
                regular = true,
                socialName = null,
                deathYear = null,
                status = "Regular",
            ),
        )

        buildKycReport(kycDossier, accountRegisterData.copy(fraudListMatch = true), "kyc_full_match")
    }

    @Test
    fun noMatchReport() {
        val kycDossier = KycDossier(
            taxId = KycDossierTaxIdRegion(region = "SP-RJ"),
            motherName = "nome da minha mãe",
            fatherName = "nome do meu pai",
            phones = listOf(
                KycDossierPhone(
                    number = "*********",
                    areaCode = "00",
                    countryCode = "11",
                    createdAt = recentCreationDate.toLocalDate(),
                ),
            ),
            emails = listOf(
                KycDossierEmail(value = accountRegisterData.emailAddress, createdAt = recentCreationDate.toLocalDate()),
            ),
            sanctions = listOf(
                KycDossierSanctions(
                    source = "interpol",
                    type = KycDossierSanctionType.ArrestWarrants,
                    matchRate = 100,
                    nameUniquenessScore = 1.0,
                ),

                KycDossierSanctions(
                    source = "Conselho Nacional de Justiça",
                    type = KycDossierSanctionType.Corruption,
                    matchRate = 98,
                    nameUniquenessScore = 1.0,
                ),
            ),
            globalSanctionsList = listOf("FAKE A", "FAKE B", "FAKE C"),
            pep = PepQuery(at = ZonedDateTime.now(), result = PepQueryResult.DIRECT_PEP),
            gender = null,
            mep = KycDossierMepQuery.empty(),
            officialData = KycDossierOfficialDocumentData(
                provider = "Receita Federal",
                name = "Nome na receita",
                birthDate = LocalDate.now(),
                hasObitIndication = false,
                regular = true,
                socialName = null,
                deathYear = null,
                status = "Regular",
            ),
        )

        buildKycReport(kycDossier, accountRegisterData, "kyc_no_match")
    }

    @Test
    fun simpleMatchReport() {
        val accountRegisterData = accountRegisterData.copy(
            livenessEnrollmentVerification = LivenessEnrollmentVerification(
                duplications = LivenessEnrollmentVerification.Result.Verified(
                    accountIds = listOf(
                        AccountId(
                            "AccountId-1231241-**********-1",
                        ),
                    ),
                ),
                fraudIndications = LivenessEnrollmentVerification.Result.Verified(
                    accountIds = listOf(
                        AccountId("AccountId-1231241-**********-1"),
                    ),
                ),
            ),
        )
        val simpleSignUpKycDossier = KycDossier(
            motherName = "Joana da Vida",
            gender = accountRegisterData.calculatedGender,
            taxId = KycDossierTaxIdRegion(region = "AL-PB-PE-RN"),
            phones = listOf(
                KycDossierPhone(
                    number = accountRegisterData.mobilePhone!!.msisdn.takeLast(9),
                    areaCode = accountRegisterData.mobilePhone!!.msisdn.takeLast(11).take(2),
                    countryCode = accountRegisterData.mobilePhone!!.msisdn.takeLast(13).take(2),
                    createdAt = creationDate.toLocalDate(),
                    active = true,
                ),
            ),
            emails = listOf(
                KycDossierEmail(
                    value = accountRegisterData.emailAddress,
                    createdAt = LocalDate.EPOCH,
                    active = false,
                ),
            ),
            mep = KycDossierMepQuery(exposureLevel = "A", celebrityLevel = "B", unpopularityLevel = "H"),
            pep = PepQuery(at = ZonedDateTime.now(), result = PepQueryResult.RELATED_TO_PEP),
            officialData = KycDossierOfficialDocumentData(
                provider = "Receita Federal",
                name = accountRegisterData.documentInfo!!.name + " dif",
                birthDate = userBirthDate!!.plusDays(1),
                socialName = null,
                hasObitIndication = false,
                deathYear = null,
                regular = false,
                status = "FAKE",
            ),
            sanctions = listOf(
                KycDossierSanctions(
                    source = "interpol",
                    type = KycDossierSanctionType.ArrestWarrants,
                    matchRate = 100,
                    nameUniquenessScore = 1.0,

                ),
                KycDossierSanctions(
                    source = "Conselho Nacional de Justiça",
                    type = KycDossierSanctionType.Corruption,
                    matchRate = 98,
                    nameUniquenessScore = 1.0,
                ),
            ),
            fatherName = null,
            globalSanctionsList = listOf("FAKE A", "FAKE B", "FAKE C"),
        )

        buildSimpleKycReport(
            kycDossier = simpleSignUpKycDossier,
            "simple_kyc_match",
            accountRegisterData.copy(
                politicallyExposed = PoliticallyExposed(
                    false,
                    PepQuery(at = ZonedDateTime.now(), result = PepQueryResult.NOT_PEP),
                ),
            ),
        )
    }

//    @Disabled
//    @Test
//    fun statementEmail() {
//        val emailContent = HandlebarHelper.applyTemplate(
//            "templates/statement-report",
//            mapOf(
//                "name" to "FULANO DE TAL",
//                "periodMessage" to "01/02/2000 a 12/10/2022",
//            ),
//        )
//
//        val fileHtml = File("target/statement-report.html")
//        fileHtml.parentFile.mkdirs()
//        FileOutputStream(fileHtml).write(emailContent.toByteArray())
//
//        emailSender.sendRawEmail(
//            buildSource(
//                emailSenderConfiguration.notificationDisplayName,
//                emailSenderConfiguration.notificationEmail,
//            ),
//            "Friday - Extrato",
//            emailContent,
//            "<EMAIL>",
//            emptyList(),
//            MediaType.TEXT_HTML,
//        )
//    }

//    @Test
//    fun forgotPasswordEmail() {
//        val emailContent = HandlebarHelper.applyTemplate(
//            "templates/password-recovery-token",
//            mapOf(
//                "id" to ByteWrapper("*********09".toByteArray()).getBase64(),
//                "token" to "789012",
//                "name" to "FULANO DE TAL",
//                "duration" to Duration.ofSeconds(300).toMinutes(),
//            ),
//        )
//
//        val fileHtml = File("target/forgot-password/email-with-token.html")
//        fileHtml.parentFile.mkdirs()
//        FileOutputStream(fileHtml).write(emailContent.toByteArray())
//
//        emailSender.sendRawEmail(
//            buildSource(
//                emailSenderConfiguration.notificationDisplayName,
//                emailSenderConfiguration.notificationEmail,
//            ),
//            "Friday - Recuperação de Senha",
//            emailContent,
//            "<EMAIL>",
//            emptyList(),
//            MediaType.TEXT_HTML,
//        )
//    }

    @Test
    @Disabled
    fun buildStatementTemplate() {
        val form = StatementTemplateForm(
            name = "Test User",
            walletName = "Test Wallet",
            document = "**************",
            bankAccount = internalBankAccount.copy(routingNo = 1L, bankNo = 213),
            startDate = LocalDate.now(),
            endDate = LocalDate.now(),
            initialBalanceDate = LocalDate.now().minusDays(1),
            finalBalanceDate = LocalDate.now(),
            initialBalance = Balance(9_100_000_00L),
            finalBalance = Balance(9_100_000_00L),
            created = LocalDateTime.now(),
            statementItems = listOf(
                StatementItem(id = "1", amount = 100_000_00, postedAt = LocalDateTime.now(), counterPartName = "Nome ", description = "Descrição", flow = BankStatementItemFlow.DEBIT, balance = Balance(1000), category = null),
                StatementItem(id = "2", amount = 1_000_000_00, postedAt = LocalDateTime.now(), counterPartName = "Teste 2", description = "Descrição teste", flow = BankStatementItemFlow.DEBIT, balance = Balance(20000), category = null),
                StatementItem(id = "3", amount = 10_500_00, postedAt = LocalDateTime.now(), counterPartName = "Teste 3", description = "", flow = BankStatementItemFlow.CREDIT, balance = Balance(5000), category = null),
                StatementItem(id = "4", amount = 10_500_00, postedAt = LocalDateTime.now(), counterPartName = "Teste 3", description = "", flow = BankStatementItemFlow.DEBIT, balance = Balance(100000), category = null),
                StatementItem(id = "5", amount = 10_500_00, postedAt = LocalDateTime.now(), counterPartName = "Teste 3", description = "", flow = BankStatementItemFlow.CREDIT, balance = Balance(30000), category = null),
                StatementItem(id = "6", amount = 10_500_00, postedAt = LocalDateTime.now(), counterPartName = "Teste 3", description = " de teste bem grande para quebrar linha linha linha Descrição de teste bem grande para quebrar linha linha linha", flow = BankStatementItemFlow.CREDIT, balance = Balance(450), category = null),
                StatementItem(id = "7", amount = 10_500_00, postedAt = LocalDateTime.now(), counterPartName = "Teste 3", description = "Descrição de teste bem grande para quebrar linha linha linha Descrição de teste bem grande para quebrar linha linha linha", flow = BankStatementItemFlow.CREDIT, balance = Balance(50000), category = null),
                StatementItem(id = "8", amount = 10_500_00, postedAt = LocalDateTime.now(), counterPartName = "Teste 3", description = "Descrição de teste bem grande para quebrar linha linha linha Descrição de teste bem grande para quebrar linha linha linha", flow = BankStatementItemFlow.CREDIT, balance = Balance(7760), category = null),
                StatementItem(id = "9", amount = 10_500_00, postedAt = LocalDateTime.now(), counterPartName = "Teste 3", description = "Descrição de teste bem grande para quebrar linha linha linha Descrição de teste bem grande para quebrar linha linha linha", flow = BankStatementItemFlow.CREDIT, balance = Balance(7760), category = null),
                StatementItem(id = "10", amount = 10_500_00, postedAt = LocalDateTime.now(), counterPartName = "Teste 3", description = "Descrição de teste bem grande para quebrar linha linha linha Descrição de teste bem grande para quebrar linha linha linha", flow = BankStatementItemFlow.CREDIT, balance = Balance(7760), category = null),
                StatementItem(id = "11", amount = 10_500_00, postedAt = LocalDateTime.now(), counterPartName = "Teste 30", description = "Teste com descrição normal", flow = BankStatementItemFlow.CREDIT, balance = Balance(7760), category = null),
                StatementItem(id = "12", amount = 10_500_00, postedAt = LocalDateTime.now(), counterPartName = "Teste 30", description = "Teste com descrição normal", flow = BankStatementItemFlow.CREDIT, balance = Balance(7760), category = null),
                StatementItem(id = "13", amount = 10_500_00, postedAt = LocalDateTime.now(), counterPartName = "Teste 30", description = "Teste com descrição normal", flow = BankStatementItemFlow.DEBIT, balance = Balance(7760), category = null),
                StatementItem(id = "14", amount = 10_500_00, postedAt = LocalDateTime.now(), counterPartName = "Teste 30", description = "Teste com descrição normal", flow = BankStatementItemFlow.DEBIT, balance = Balance(7760), category = null),
                StatementItem(id = "15", amount = 10_500_00, postedAt = LocalDateTime.now(), counterPartName = "Teste 30", description = "Teste com descrição normal", flow = BankStatementItemFlow.CREDIT, balance = Balance(7760), category = null),
                StatementItem(id = "16", amount = 10_500_00, postedAt = LocalDateTime.now(), counterPartName = "Teste 30", description = "Teste com descrição normal", flow = BankStatementItemFlow.CREDIT, balance = Balance(7760), category = null),
                StatementItem(id = "17", amount = 10_500_00, postedAt = LocalDateTime.now(), counterPartName = "Teste 30", description = "Teste com descrição normal", flow = BankStatementItemFlow.CREDIT, balance = Balance(7760), category = null),
                StatementItem(id = "18", amount = 10_500_00, postedAt = LocalDateTime.now(), counterPartName = "Teste 30", description = "Teste com descrição normal", flow = BankStatementItemFlow.CREDIT, balance = Balance(7760), category = null),
                StatementItem(id = "19", amount = 10_500_00, postedAt = LocalDateTime.now(), counterPartName = "Teste 30", description = "Teste com descrição normal", flow = BankStatementItemFlow.CREDIT, balance = Balance(7760), category = null),
                StatementItem(id = "20", amount = 10_500_00, postedAt = LocalDateTime.now(), counterPartName = "Teste 30", description = "Teste com descrição normal", flow = BankStatementItemFlow.CREDIT, balance = Balance(7760), category = null),
                StatementItem(id = "21", amount = 10_500_00, postedAt = LocalDateTime.now(), counterPartName = "Teste 30", description = "Teste com descrição normal", flow = BankStatementItemFlow.CREDIT, balance = Balance(7760), category = null),
                StatementItem(id = "22", amount = 10_500_00, postedAt = LocalDateTime.now(), counterPartName = "Teste 30", description = "Teste com descrição normal", flow = BankStatementItemFlow.CREDIT, balance = Balance(7760), category = null),
            ),
        )

        val statementHtml = statementTemplateCompiler.buildStatementHtml(form)
        val html = File("target/statement/user-statement.html")
        html.parentFile.mkdirs()
        FileOutputStream(html).write(statementHtml.value.toByteArray())
        val pdf = File("target/statement/user-statement.pdf")
        pdf.parentFile.mkdirs()
        FileOutputStream(pdf).write(PDF_CONVERTER.convert(statementHtml.value, false))

        val statementWithBalance = statementTemplateCompiler.buildStatementHtml(form)
        val htmlWithBalance = File("target/statement/user-statement-with-balance.html")
        htmlWithBalance.parentFile.mkdirs()
        FileOutputStream(htmlWithBalance).write(statementWithBalance.value.toByteArray())
        val pdfWithBalance = File("target/statement/user-statement-with-balance.pdf")
        pdfWithBalance.parentFile.mkdirs()
        FileOutputStream(pdfWithBalance).write(PDF_CONVERTER.convert(statementWithBalance.value, false))
    }

    @Test
    fun buildEmailVerificationTokenTemplate() {
        forAllEnvironments { environment, templatesConfiguration ->
            val map = mapOf("token" to "123456", "duration" to Duration.ofMinutes(5).toMinutes())
            val result = CompiledHtml(TemplateHelper.applyTemplate(templatesConfiguration.local.emailVerificationTokenPath, map))
            val fileHtml = File("target/email-verification/email-verification-token-$environment.html")
            fileHtml.parentFile.mkdirs()
            FileOutputStream(fileHtml).write(result.value.toByteArray())
        }
    }

    @Test
    fun buildStatementReport() {
        forAllEnvironments { environment, templatesConfiguration ->
            val map = mapOf(
                "name" to "Test User",
                "periodMessage" to "7 dias",
            )
            val result = CompiledHtml(TemplateHelper.applyTemplate(templatesConfiguration.local.accountStatementReportPath, map))
            val fileHtml = File("target/statement/statement-report-$environment.html")
            fileHtml.parentFile.mkdirs()
            FileOutputStream(fileHtml).write(result.value.toByteArray())
        }
    }

    @Test
    fun buildWalletSummaryReport() {
        forAllEnvironments { environment, templatesConfiguration ->
            val map = mapOf(
                "name" to "Test User",
                "periodMessage" to "7 dias",
            )
            val result = CompiledHtml(TemplateHelper.applyTemplate(templatesConfiguration.local.walletSummaryReportPath, map))
            val fileHtml = File("target/wallet/wallet-summary-report-$environment.html")
            fileHtml.parentFile.mkdirs()
            FileOutputStream(fileHtml).write(result.value.toByteArray())
        }
    }

    @Test
    fun buildPasswordRecoveryToken() {
        forAllEnvironments { environment, templatesConfiguration ->
            val map = mapOf(
                "id" to ByteWrapper("document".toByteArray()).getBase64(),
                "token" to "token",
                "name" to "Account Name",
                "duration" to 100.toDuration(DurationUnit.MINUTES),
            )
            val result = CompiledHtml(TemplateHelper.applyTemplate(templatesConfiguration.local.emailPasswordRecoveryTokenPath, map))
            val fileHtml = File("target/password/password-recovery-token-$environment.html")
            fileHtml.parentFile.mkdirs()
            FileOutputStream(fileHtml).write(result.value.toByteArray())
        }
    }

    @Test
    fun buildInformTemplate() {
        forAllEnvironments { environment, templatesConfiguration ->
            val result = CompiledHtml(TemplateHelper.applyTemplate(templatesConfiguration.local.inform, ""))
            val fileHtml = File("target/inform/inform-$environment.html")
            fileHtml.parentFile.mkdirs()
            FileOutputStream(fileHtml).write(result.value.toByteArray())
        }
    }

    @Test
    fun buildMailReceipt() {
        forAllEnvironments { environment, templatesConfiguration ->
            val map = mapOf(
                "totalAmount" to buildFormattedAmount(1000),
                "date" to buildFormattedHeaderDateTime(ZonedDateTime.now()),
                "recipientName" to "Recipient Name",
                "recipientDocument" to maskDocument("53922503004"),
                "billTypeLabel" to "PIX",
                "authentication" to "authentication",
            )
            val result = CompiledHtml(TemplateHelper.applyTemplate(templatesConfiguration.local.mailReceipt, map))
            val fileHtml = File("target/receipt/mail-receipt-$environment.html")
            fileHtml.parentFile.mkdirs()
            FileOutputStream(fileHtml).write(result.value.toByteArray())
        }
    }

    private fun buildSimpleKycReport(
        kycDossier: KycDossier,
        fileName: String,
        accountRegisterData: AccountRegisterData,
    ) {
        val result = handlebarTemplateCompiler.buildHtml(kycDossier.toForm(accountRegisterData, faceMatch = null, documentScan = null))
        val fileHtml = File("target/kyc/$fileName.html")
        val filePdf = File("target/kyc/$fileName.pdf")

        fileHtml.parentFile.mkdirs()

        FileOutputStream(filePdf).write(PDF_CONVERTER.convert(result.value, false))
        FileOutputStream(fileHtml).write(result.value.toByteArray())
    }

    private fun buildKycReport(kycDossier: KycDossier, accountRegisterData: AccountRegisterData, fileName: String) {
        val result = handlebarTemplateCompiler.buildHtml(
            kycDossier.toForm(
                accountRegisterData,
                faceMatch = null,
                documentScan = DocumentScanResult(
                    digitalSpoof = DocumentScanDigitalSpoofResult(status = DocumentScanDigitalSpoofStatus.LIKELY_PHYSICAL_ID),
                    face = DocumentScanFaceResult.Found(status = DocumentScanFaceStatus.TEMPLATE_DOES_NOT_SUPPORT_DETECTION),
                    text = DocumentScanTextResult.Performed(status = DocumentScanTextStatus.LIKELY_ORIGINAL_TEXT),
                    ocr = DocumentScanOcrResult.Matched,
                ),
            ),
        )
        val file = File("target/kyc/$fileName.pdf")
        file.parentFile.mkdirs()
        FileOutputStream(file).write(PDF_CONVERTER.convert(result.value, false))
    }

    private fun buildDeclarationOfResidency(accountRegisterData: AccountRegisterData, fileName: String) {
        with(accountRegisterData) {
            val signature: DeclarationOfResidencySignature = generateDeclarationOfResidencySignature(this, "1234")
            val declarationOfResidencyForm = DeclarationOfResidencyForm(
                fullName = getName(),
                document = formatDocument(getCPF()),
                fullAddress = address!!.toFullAddress(),
                city = address!!.city,
                federalUnity = address!!.state,
                zipCode = formatZipCode(address!!.zipCode),
                signature = signature,
            )

            val result = handlebarTemplateCompiler.buildHtml(declarationOfResidencyForm)
            val file = File("target/register/$fileName.pdf")
            file.parentFile.mkdirs()
            FileOutputStream(file).write(PDF_CONVERTER.convert(result.value, false))
        }
    }

    private fun generateDeclarationOfResidencySignature(
        accountRegisterData: AccountRegisterData,
        clientIP: String,
    ): DeclarationOfResidencySignature {
        val signatureDate = getZonedDateTime()
        return DeclarationOfResidencySignature(
            date = documentDateFormat.format(signatureDate),
            shortDate = shortDateFormat.format(signatureDate),
            email = accountRegisterData.emailAddress.value,
            phone = accountRegisterData.mobilePhone!!.msisdn,
            clientIP = clientIP,
            key = accountRegisterData.toContractForm().generateSignatureKey(),
        )
    }
}