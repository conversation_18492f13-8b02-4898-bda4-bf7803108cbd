package ai.friday.billpayment.adapters.handlebar

import ai.friday.billpayment.PDF_CONVERTER
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.GIGU_ENV
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.bill.BillPayer
import ai.friday.billpayment.app.bill.PspInformation
import ai.friday.billpayment.app.notification.EmailTemplatesConfiguration
import ai.friday.billpayment.app.payment.AutomaticPixReceiptData
import ai.friday.billpayment.app.payment.BoletoReceiptData
import ai.friday.billpayment.app.payment.InvoiceReceiptData
import ai.friday.billpayment.app.payment.PixReceiptData
import ai.friday.billpayment.app.utils.HtmlToPdfAndImageConversorService
import ai.friday.billpayment.createEmailTemplatesConfiguration
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.json.getObjectMapper
import java.io.File
import java.io.FileOutputStream
import java.util.stream.Stream
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

private val knownEnvironments = listOf(FRIDAY_ENV, ME_POUPE_ENV, GIGU_ENV)

internal class HandlebarReceiptTemplateCompilerTest {
    private val htmlToPdfAndImageConversorService = HtmlToPdfAndImageConversorService(PDF_CONVERTER)

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    private val imageResolution = 400
    private val imageFormat = "png"

    @ParameterizedTest
    @MethodSource("buildPixReceiptTemplateData")
    fun buildPixReceiptTemplate(name: String, data: String) {
        forAllEnvironments { environment, templatesConfiguration ->
            val receiptTemplateCompiler = ReceiptHandlebarCompiler(templatesConfiguration)
            val receiptData = parseObjectFrom<PixReceiptData>(data)
            val receiptHtml = receiptTemplateCompiler.buildReceiptHtml(receiptData, wallet)
            val fileName = "target/receipt/pix-receipt-$name.$environment"

            val html = File("$fileName.html")
            html.parentFile.mkdirs()
            FileOutputStream(html).write(receiptHtml.value.toByteArray())

            val file = File("$fileName.pdf")
            file.parentFile.mkdirs()
            val pdfData = PDF_CONVERTER.convert(receiptHtml.value, true)
            FileOutputStream(file).write(pdfData)

            val imageData = htmlToPdfAndImageConversorService.convertPdf2Image(pdfData, imageResolution, imageFormat)
            val imgFile = File("$fileName.png")
            FileOutputStream(imgFile).write(imageData)

            val receiptMailHtml = receiptTemplateCompiler.buildReceiptMailHtml(receiptData)
            val mailHtml = File("$fileName-mail.html")
            mailHtml.parentFile.mkdirs()
            FileOutputStream(mailHtml).write(receiptMailHtml.value.toByteArray())
        }
    }

    @ParameterizedTest
    @MethodSource("buildAutomaticPixReceiptTemplateData")
    fun buildAutomaticPixReceiptTemplate(name: String, data: String) {
        forAllEnvironments { environment, templatesConfiguration ->
            val receiptTemplateCompiler = ReceiptHandlebarCompiler(templatesConfiguration)
            val receiptData = parseObjectFrom<AutomaticPixReceiptData>(data)

            val x = receiptData.copy(
                pixReceiptData = receiptData.pixReceiptData.copy(
                    recipient = receiptData.pixReceiptData.recipient.copy(
                        bankAccount = null,
                        pixKeyDetails = null,
                        pixQrCodeData = null,
                        pspInformation = PspInformation("1234", null),
                    ),
                ),
            )

            println(getObjectMapper().writeValueAsString(x))

            val receiptHtml = receiptTemplateCompiler.buildReceiptHtml(receiptData, wallet)
            val fileName = "target/receipt/automatic-pix-receipt-$name.$environment"

            val html = File("$fileName.html")
            html.parentFile.mkdirs()
            FileOutputStream(html).write(receiptHtml.value.toByteArray())

            val file = File("$fileName.pdf")
            file.parentFile.mkdirs()
            val pdfData = PDF_CONVERTER.convert(receiptHtml.value, true)
            FileOutputStream(file).write(pdfData)

            val imageData = htmlToPdfAndImageConversorService.convertPdf2Image(pdfData, imageResolution, imageFormat)
            val imgFile = File("$fileName.png")
            FileOutputStream(imgFile).write(imageData)

            val receiptMailHtml = receiptTemplateCompiler.buildReceiptMailHtml(receiptData)
            val mailHtml = File("$fileName-mail.html")
            mailHtml.parentFile.mkdirs()
            FileOutputStream(mailHtml).write(receiptMailHtml.value.toByteArray())
        }
    }

    @Test
    fun buildInvoiceReceiptTemplate() {
        forAllEnvironments { environment, templatesConfiguration ->
            val receiptTemplateCompiler = ReceiptHandlebarCompiler(templatesConfiguration)
            val receiptDataStr = """{"@type":"INVOICE","billId":{"value":"BILL-49e60ecf-cfe0-4306-810d-06f8f54d9451"},"walletId":{"value":"ACCOUNT-04db57f9-3d4a-4f95-bc30-3946263e15b0"},"source":{"@type":"Api","accountId":{"value":"ACCOUNT-85f03ac4-4d7c-4200-acf0-1cb14b2609a2"},"originalBillId":null},"authentication":"STR20250128008626817","dateTime":"2025-01-28T12:05:02.848-03:00[Brazil/East]","recipient":{"name":"ALEXANDRE TEIXEIRA DE ASSUMPCAO SAIGH","document":"***********","alias":"Alexandre Teixeira","bankAccount":{"accountType":"CHECKING","bankNo":341,"routingNo":3741,"accountNo":14393,"accountDv":"1","document":"***********","ispb":"********"},"pixKeyDetails":null,"pixQrCodeData":null},"totalAmount":18310,"purpose":"01 - Crédito em Conta","payer":{"document":"***********","name":"ANGELA SAIGH SUCAR","alias":null},"payeeBank":"341 - ITAU UNIBANCO S.A.","paymentPartnerName":null,"paymentPartnerDocument":null,"payerBankAccount":{"accountType":"CHECKING","bankNo":213,"routingNo":1,"accountNo":********,"accountDv":"1","document":"***********","bankAccountMode":"PHYSICAL","type":"BALANCE"},"payerFinancialInstitution":{"name":"Banco Arbi S.A.","ispb":null,"compe":213},"walletName":"ANGELA SUCAR","scheduledBy":"MARIZE SILVA","transactionId":{"value":"TRANSACTION-03fdea99-fca2-4d3c-91ea-e17e0c003fc2"},"payments":[{"amount":18310,"type":"BALANCE","accountPaymentMethodId":{"value":"7ce27e6b-d769-4706-bf63-11f057a1f231"}}],"recipientName":"ALEXANDRE TEIXEIRA DE ASSUMPCAO SAIGH"}"""
            val receiptData = parseObjectFrom<InvoiceReceiptData>(receiptDataStr)
            val fileName = "target/receipt/invoice-receipt.$environment"

            val receiptHtml = receiptTemplateCompiler.buildReceiptHtml(receiptData, wallet)
            val html = File("$fileName.html")
            html.parentFile.mkdirs()
            FileOutputStream(html).write(receiptHtml.value.toByteArray())

            val file = File("$fileName.pdf")
            file.parentFile.mkdirs()
            val pdfData = PDF_CONVERTER.convert(receiptHtml.value, true)
            FileOutputStream(file).write(pdfData)

            val imageData = htmlToPdfAndImageConversorService.convertPdf2Image(pdfData, imageResolution, imageFormat)
            val imgFile = File("$fileName.png")
            FileOutputStream(imgFile).write(imageData)

            val receiptMailHtml = receiptTemplateCompiler.buildReceiptMailHtml(receiptData)
            val mailHtml = File("$fileName-mail.$environment.html")
            mailHtml.parentFile.mkdirs()
            FileOutputStream(mailHtml).write(receiptMailHtml.value.toByteArray())
        }
    }

    @ParameterizedTest
    @MethodSource("buildBarcodeReceiptTemplateData")
    fun buildBarcodeReceiptTemplate(name: String, data: String) {
        forAllEnvironments { environment, templatesConfiguration ->
            val receiptTemplateCompiler = ReceiptHandlebarCompiler(templatesConfiguration)
            val receiptData = with(parseObjectFrom<BoletoReceiptData>(data)) {
                if (this.payer != null) {
                    this
                } else {
                    this.copy(
                        payer = BillPayer(
                            document = "***********",
                            name = "Fulano de Tal",
                            alias = null,
                        ),
                    )
                }
            }
            val receiptHtml = receiptTemplateCompiler.buildReceiptHtml(receiptData, wallet)
            val fileName = "target/receipt/barcodeReceipt-$name.$environment"

            val html = File("$fileName.html")
            html.parentFile.mkdirs()
            FileOutputStream(html).write(receiptHtml.value.toByteArray())

            val file = File("$fileName.pdf")
            file.parentFile.mkdirs()
            val pdfData = PDF_CONVERTER.convert(receiptHtml.value, true)
            FileOutputStream(file).write(pdfData)

            val imageData = htmlToPdfAndImageConversorService.convertPdf2Image(pdfData, imageResolution, imageFormat)
            val imgFile = File("$fileName.png")
            FileOutputStream(imgFile).write(imageData)

            val receiptMailHtml = receiptTemplateCompiler.buildReceiptMailHtml(receiptData)
            val mailHtml = File("$fileName-mail.html")
            mailHtml.parentFile.mkdirs()
            FileOutputStream(mailHtml).write(receiptMailHtml.value.toByteArray())
        }
    }

    companion object {
        private val pixWithQRCode = """{"@type":"PIX","billId":{"value":"BILL-887944cc-a5e8-4f8f-82bb-2189417bba35"},"walletId":{"value":"ACCOUNT-460a346e-7e5d-4991-b001-9ae44a1df061"},"source":{"@type":"Api","accountId":{"value":"ACCOUNT-460a346e-7e5d-4991-b001-9ae44a1df061"},"originalBillId":null},"authentication":"E********202502131248toK2Tz9W2B2","dateTime":"2025-02-13T09:48:30.684-03:00[Brazil/East]","recipient":{"name":"COORD ADM FINANCEIRA CAF","document":"**************","alias":"","bankAccount":null,"pixKeyDetails":{"key":{"value":"706d7b01-07d4-4db2-82f1-c584e49e5afe","type":"EVP"},"holder":{"accountNo":1300001,"accountDv":"2","ispb":"********","institutionName":"BCO DO BRASIL S.A.","accountType":"CHECKING","routingNo":1897},"owner":{"name":"COORD ADM FINANCEIRA CAF","document":"**************"}},"pixQrCodeData":{"type":"DYNAMIC","info":"Dados recuperados do link 'https://qrcodepix.bb.com.br/pix/v2/7f475ed8-a321-495c-92b0-95c385bbac9a'","additionalInfo":{"Campo 1":"Valor campo1", "Campo 2":"Valor campo2"},"pixId":"RCBMUL20250213API25044094705803637","fixedAmount":182559,"expiration":"2025-02-13T13:02:04.95Z","originalAmount":182559}},"totalAmount":182559,"purpose":"01 - Crédito em Conta","payer":{"document":"***********","name":"Marlon da Costa moncores","alias":null},"payeeFinancialInstitution":{"name":"BCO DO BRASIL S.A.","ispb":"********","compe":1},"description":"Teste", "payerFinancialInstitution":{"name":"BCO ARBI S.A.","ispb":"********","compe":213},"payeeRoutingNo":1897,"payeeAccountNo":1300001,"payeeAccountDv":"2","payeeAccountType":"CHECKING","payerBankAccount":{"accountType":"CHECKING","bankNo":213,"routingNo":1,"accountNo":********,"accountDv":"3","document":"***********","bankAccountMode":"PHYSICAL","accountNumber":{"number":********,"dv":"3","fullAccountNumber":"********3"},"type":"BALANCE"},"description":"","walletName":"Marlon moncores","scheduledBy":"Marlon moncores","transactionId":{"value":"TRANSACTION-f3780047-a22c-492f-aadc-a43d106d0510"},"payments":[{"amount":182559,"type":"BALANCE","accountPaymentMethodId":{"value":"37d19d31-2d9e-46ef-af75-93d10245eecf"}}],"recipientName":"COORD ADM FINANCEIRA CAF"}"""

        @JvmStatic
        fun buildPixReceiptTemplateData(): Stream<Arguments> {
            val pixWithKey = """{"@type":"PIX","billId":{"value":"BILL-4a860df7-4c86-4e8c-a61e-c70e40f59951"},"walletId":{"value":"ACCOUNT-460a346e-7e5d-4991-b001-9ae44a1df061"},"source":{"@type":"WalletRecurrence","accountId":{"value":"ACCOUNT-460a346e-7e5d-4991-b001-9ae44a1df061"},"recurrenceId":{"value":"RECURRENCE-5f43342b-97c1-4fcd-818d-4ef47604b337"}},"authentication":"E********202503181624tP2oUIas3KC","dateTime":"2025-03-18T13:24:21.111-03:00[Brazil/East]","recipient":{"name":"RIO ABRACE","document":"**************","alias":"","bankAccount":null,"pixKeyDetails":{"key":{"value":"**************","type":"CNPJ"},"holder":{"accountNo":58249,"accountDv":"3","ispb":"********","institutionName":"ITAÚ UNIBANCO S.A.","accountType":"CHECKING","routingNo":530},"owner":{"name":"RIO ABRACE","document":"**************"}},"pixQrCodeData":null},"totalAmount":2500,"purpose":"01 - Crédito em Conta","payer":{"document":"***********","name":"Marlon da Costa moncores","alias":null},"payeeFinancialInstitution":{"name":"ITAÚ UNIBANCO S.A.","ispb":"********","compe":341},"payerFinancialInstitution":{"name":"BCO ARBI S.A.","ispb":"********","compe":213},"payeeRoutingNo":530,"payeeAccountNo":58249,"payeeAccountDv":"3","payeeAccountType":"CHECKING","payerBankAccount":{"accountType":"CHECKING","bankNo":213,"routingNo":1,"accountNo":********,"accountDv":"3","document":"***********","bankAccountMode":"PHYSICAL","description":"Teste", "accountNumber":{"number":********,"dv":"3","fullAccountNumber":"********3"},"type":"BALANCE"},"description":"","walletName":"Marlon moncores","scheduledBy":"Marlon moncores","transactionId":{"value":"TRANSACTION-9c2291a1-0558-4e84-815b-2e9b9a3b5fdf"},"payments":[{"amount":2500,"type":"BALANCE","accountPaymentMethodId":{"value":"37d19d31-2d9e-46ef-af75-93d10245eecf"}}],"recipientName":"RIO ABRACE"}"""

            val pixWithAccountData = """{"@type":"PIX","billId":{"value":"BILL-********-8ae5-47fe-acb2-8545f9665a11"},"walletId":{"value":"ACCOUNT-********-b017-4db0-ab08-71afca18e840"},"source":{"@type":"Api","accountId":{"value":"ACCOUNT-********-b017-4db0-ab08-71afca18e840"},"originalBillId":null},"authentication":"E********202504011726LqT6hOfsxpt","dateTime":"2025-04-01T14:26:04.8-03:00[Brazil/East]","recipient":{"name":"Larissa","document":"***********","alias":"","bankAccount":{"accountType":"CHECKING","bankNo":1,"routingNo":7123,"accountNo":21234,"accountDv":"2","document":"***********","ispb":"********"},"pixKeyDetails":null,"pixQrCodeData":null},"totalAmount":1,"purpose":"01 - Crédito em Conta","payer":{"document":"***********","name":"Walter Santtos Com o nome bem Grande","alias":null},"payeeFinancialInstitution":{"name":"BCO DO BRASIL S.A.","ispb":"********","compe":1},"payerFinancialInstitution":{"name":"BCO ARBI S.A.","ispb":"********","compe":213},"payeeRoutingNo":7123,"payeeAccountNo":21234,"payeeAccountDv":"2","payeeAccountType":"CHECKING","payerBankAccount":{"accountType":"CHECKING","bankNo":213,"routingNo":1,"accountNo":********,"accountDv":"0","document":"***********","bankAccountMode":"PHYSICAL","accountNumber":{"number":********,"dv":"0","fullAccountNumber":"********0"},"type":"BALANCE"},"description":"Teste","walletName":"WALTER MACAMBIRA","scheduledBy":"Walter Santtos","transactionId":{"value":"TRANSACTION-ac33f5e5-ef3f-409e-abd6-128473a00000"},"payments":[{"amount":1,"type":"BALANCE","accountPaymentMethodId":{"value":"f800f045-1bdb-4b6c-849d-7538fad00000"}}],"recipientName":"Larissa Barros"}"""

            return Stream.of(
                Arguments.of("pixWithKey", pixWithKey),
                Arguments.of("pixWithQRCode", pixWithQRCode),
                Arguments.of("pixWithAccountData", pixWithAccountData),
            )
        }

        @JvmStatic
        fun buildAutomaticPixReceiptTemplateData(): Stream<Arguments> {
            val automaticPixWithContract = """{"@type":"AUTOMATIC_PIX","pixReceiptData":{"@type":"PIX","billId":{"value":"BILL-887944cc-a5e8-4f8f-82bb-2189417bba35"},"walletId":{"value":"ACCOUNT-460a346e-7e5d-4991-b001-9ae44a1df061"},"source":{"@type":"Api","accountId":{"value":"ACCOUNT-460a346e-7e5d-4991-b001-9ae44a1df061"},"originalBillId":null},"authentication":"E********202502131248toK2Tz9W2B2","dateTime":"2025-02-13T09:48:30.684-03:00[Brazil/East]","recipient":{"name":"COORD ADM FINANCEIRA CAF","document":"**************","alias":"","bankAccount":null,"pixKeyDetails":null,"pixQrCodeData":null,"pspInformation":{"code":"1234","name":"Banco com nome grande"}},"totalAmount":182559,"purpose":"01 - Crédito em Conta","payer":{"document":"***********","name":"Marlon da Costa moncores","alias":null},"payeeFinancialInstitution":{"name":"BCO DO BRASIL S.A.","ispb":"********","compe":1},"payerFinancialInstitution":{"name":"BCO ARBI S.A.","ispb":"********","compe":213},"payeeRoutingNo":1897,"payeeAccountNo":1300001,"payeeAccountDv":"2","payeeAccountType":"CHECKING","payerBankAccount":{"accountType":"CHECKING","bankNo":213,"routingNo":1,"accountNo":********,"accountDv":"3","document":"***********","bankAccountMode":"PHYSICAL","accountNumber":{"number":********,"dv":"3","fullAccountNumber":"********3"},"type":"BALANCE"},"description":"","walletName":"Marlon moncores","scheduledBy":"Marlon moncores","transactionId":{"value":"TRANSACTION-f3780047-a22c-492f-aadc-a43d106d0510"},"payments":[{"amount":182559,"type":"BALANCE","accountPaymentMethodId":{"value":"37d19d31-2d9e-46ef-af75-93d10245eecf"}}],"recipientName":"COORD ADM FINANCEIRA CAF"},"contractNumber":"CONTRATO-123","automaticPixDescription":"Pagamento automático mensal","billId":{"value":"BILL-887944cc-a5e8-4f8f-82bb-2189417bba35"},"walletId":{"value":"ACCOUNT-460a346e-7e5d-4991-b001-9ae44a1df061"},"source":{"@type":"Api","accountId":{"value":"ACCOUNT-460a346e-7e5d-4991-b001-9ae44a1df061"},"originalBillId":null},"dateTime":"2025-02-13T09:48:30.684-03:00[Brazil/East]","totalAmount":182559,"walletName":"Marlon moncores","scheduledBy":"Marlon moncores","transactionId":{"value":"TRANSACTION-f3780047-a22c-492f-aadc-a43d106d0510"},"payments":[{"amount":182559,"type":"BALANCE","accountPaymentMethodId":{"value":"37d19d31-2d9e-46ef-af75-93d10245eecf"}}],"recipientName":"COORD ADM FINANCEIRA CAF"}"""
            return Stream.of(
                Arguments.of("automaticPixWithContract", automaticPixWithContract),
            )
        }

        @JvmStatic
        fun buildBarcodeReceiptTemplateData(): Stream<Arguments> {
            val receiptDataStrFicha = """{"@type":"BOLETO","billId":{"value":"BILL-********-57a9-405c-8a28-50997fb88112"},"walletId":{"value":"ACCOUNT-ce4d6c82-422a-4fb3-aa00-5c02110bb846"},"source":{"@type":"DDA","accountId":{"value":"ACCOUNT-e31dd3cf-7abf-41a9-b91a-9954d6db5a52"}},"authentication":"549951","dateTime":"2024-12-26T15:54:33.612-03:00[Brazil/East]","assignor":"BCO SANTANDER 0BRASIL0 S.A.","recipient":{"name":"FUNDACAO UNIVERSITARIA PARA O VESTIBULARFUVEST","document":"**************","alias":"","bankAccount":null,"pixKeyDetails":null,"pixQrCodeData":null},"totalAmount":16500,"payer":{"document":"***********","name":"Aline Teste","alias":null},"dueDate":"2025-01-06","barcode":{"number":"03399995300000165009036729301242374695300101","digitable":"03399036762930124237546953001016999530000016500"},"walletName":"Marlon da Costa Moncores","scheduledBy":"Marlon Moncores","paymentPartnerName":"BCO ARBI S.A.","transactionId":{"value":"TRANSACTION-113bb9fb-70e1-4c13-aaf4-30d1b95d200f"},"payments":[{"amount":16500,"type":"BALANCE","accountPaymentMethodId":{"value":"BANK-ACCOUNT-96fc10f9-a141-487d-8a9f-77ff1cd3a524"}}],"recipientName":"RECEBEDOR DO BOLETO"}"""
            val receiptDataStrConcessionaria = """{"@type":"BOLETO","billId":{"value":"BILL-2b59dbc1-32d5-4bf4-9bc8-bf2830472202"},"walletId":{"value":"ACCOUNT-ce4d6c82-422a-4fb3-aa00-5c02110bb846"},"source":{"@type":"WalletMailBox","accountId":null,"from":"<EMAIL>"},"authentication":"1F.75.5D.3E.07.06.C0.93.A8.***********.C2.20.B2","dateTime":"2024-07-22T09:56:33.932-03:00[Brazil/East]","assignor":"CEG RIO","recipient":null,"totalAmount":15932,"payer":null,"dueDate":"2024-07-20","barcode":{"number":"83660000001593201********0518463769062024170","digitable":"836600000019593201000001000051846376690620241701"},"walletName":"Marlon da Costa Moncores","scheduledBy":"Marlon Moncores","paymentPartnerName":"BRADESCO","transactionId":{"value":"TRANSACTION-31b94f43-d26c-4239-a78e-aa5b88c6b512"},"payments":[{"amount":15932,"type":"BALANCE","accountPaymentMethodId":{"value":"BANK-ACCOUNT-96fc10f9-a141-487d-8a9f-77ff1cd3a524"}}]}"""

            return Stream.of(
                Arguments.of("fichaCIP", receiptDataStrFicha),
                Arguments.of("concessionaria", receiptDataStrConcessionaria),
            )
        }
    }
}

fun forAllEnvironments(
    block: (environment: String, templatesConfiguration: EmailTemplatesConfiguration) -> Unit,
) {
    knownEnvironments.forEach { environment ->
        val templatesConfiguration = createEmailTemplatesConfiguration(environment)
        block(environment, templatesConfiguration)
    }
}