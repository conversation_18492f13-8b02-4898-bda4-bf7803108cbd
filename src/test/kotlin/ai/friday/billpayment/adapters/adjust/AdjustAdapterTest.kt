package ai.friday.billpayment.adapters.adjust

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.DeviceAdIds
import ai.friday.billpayment.app.integrations.AdEventMetadata
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.micronaut.rxjava2.http.client.RxHttpClient
import java.net.URL
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

private const val adjustUrl = "https://s2s.adjust.com"

class AdjustAdapterTest {

    private val httpClient = RxHttpClient.create(URL(adjustUrl))

    private val adjustAdapter =
        AdjustAdapter(
            httpClient = httpClient,
            configuration = AdjustConfiguration(
                appToken = "",
                events = mapOf(
                    "account-activated" to AdjustEventConfiguration(
                        name = "123",
                        token = "adjust-id",
                        revenues = object : AdjustEventConfiguration.RevenuesConfiguration {
                            override val ios = object : AdjustEventConfiguration.RevenuesConfiguration.IosRevenueConfiguration {
                                override val currency = "BRL"
                                override val amount = 123L
                            }
                            override val android = object : AdjustEventConfiguration.RevenuesConfiguration.AndroidRevenueConfiguration {
                                override val currency = "USD"
                                override val amount = 456L
                            }
                        },
                    ),
                ),
            ),
        )

    @Disabled
    @Test
    fun `enviar evento para adjust`() {
        adjustAdapter.trackEvent(
            name = "foo",
            metadata = AdEventMetadata(
                accountId = AccountId(value = "account-id"),
                trackingIds = DeviceAdIds(
                    adId = "53570f539909145d2a6909302cd61a08",
                    adGoogleId = null,
                    adAppleId = null,
                ),
                clientIP = "clientip",
                phoneNumber = MobilePhone("+*************"),
                emailAddress = EmailAddress("<EMAIL>"),
                state = "RJ",
            ),
        )
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "     <EMAIL>    ,62a14e44f765419d10fea99367361a727c12365e2520f32218d505ed9aa0f62f",
            "<EMAIL>,508a7286f88555648990c08a1002657a28ba330379e8b47f3291120e6c80580d",
            "    <EMAIL>  ,508a7286f88555648990c08a1002657a28ba330379e8b47f3291120e6c80580d",
        ],
    )
    fun `deve enviar o e-mail no formato correto`(email: String, expected: String) {
        with(
            AdEventMetadata(
                accountId = AccountId("123"),
                trackingIds = DeviceAdIds(adId = "adjust-id", adGoogleId = null, adAppleId = null),
                clientIP = "clientIp",
                emailAddress = EmailAddress(email),
                state = null,
                phoneNumber = null,
            ).toPartnerParams(),
        ) {
            this shouldNotBe null
            this?.get("em") shouldBe expected
        }
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "+1 (616) 954-78 88,bb2fa2e6d2cea4746530c99686d0c7886ab2019875dca2d7e9e171c0e6ada00d",
            "1(650)123-4567,1f41a6876308cc581e7c096db4b342524d51f06f1df35537e30859e3956b5e89",
            "+001(616) 954-78 88,bb2fa2e6d2cea4746530c99686d0c7886ab2019875dca2d7e9e171c0e6ada00d",
            "01(650)123-4567,1f41a6876308cc581e7c096db4b342524d51f06f1df35537e30859e3956b5e89",
            "**********,f42306983821e408cada89d0f1840682989439e2b0c5e169b8e0808e807930c9",
            "**********,31b551ffbd7262694020f6e2d7a7e3fc3ad327d024862e4dfba552688c35ea95",
            "+001(616) 954-78 88,bb2fa2e6d2cea4746530c99686d0c7886ab2019875dca2d7e9e171c0e6ada00d",
            "+55(11) 91023-1232,b07612655c9f1567f55867196440daf66a797b8cffe44d4eabc50447266ad890",
            "*************,b07612655c9f1567f55867196440daf66a797b8cffe44d4eabc50447266ad890",
        ],
    )
    fun `deve enviar o telefone no formato correto`(phone: String, expected: String) {
        with(
            AdEventMetadata(
                accountId = AccountId("123"),
                trackingIds = DeviceAdIds(adId = "adjust-id", adGoogleId = null, adAppleId = null),
                clientIP = "clientIp",
                emailAddress = null,
                state = null,
                phoneNumber = MobilePhone(phone),
            ).toPartnerParams(),
        ) {
            this shouldNotBe null
            this?.get("ph") shouldBe expected
        }
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "AC,f45de51cdef30991551e41e882dd7b5404799648a0a00753f44fc966e6153fc1",
            "CE,e64c826b6b33f307cecf54ff843c5f9797c1056eb33f9dd60bc632f519712cb9",
            "ac,f45de51cdef30991551e41e882dd7b5404799648a0a00753f44fc966e6153fc1",
        ],
    )
    fun `deve enviar o estado no formato correto`(state: String, expected: String) {
        with(
            AdEventMetadata(
                accountId = AccountId("123"),
                trackingIds = DeviceAdIds(adId = "adjust-id", adGoogleId = null, adAppleId = null),
                clientIP = "clientIp",
                emailAddress = null,
                state = state,
                phoneNumber = null,
            ).toPartnerParams(),
        ) {
            this shouldNotBe null
            this?.get("st") shouldBe expected
        }
    }
}