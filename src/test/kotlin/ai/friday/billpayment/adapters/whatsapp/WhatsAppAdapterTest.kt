package ai.friday.billpayment.adapters.whatsapp

import ai.friday.billpayment.Wm
import ai.friday.billpayment.Wm.aJsonResponse
import ai.friday.billpayment.adapters.whatsappflow.WhatsAppConfiguration
import ai.friday.billpayment.adapters.whatsappflow.WhatsAppFlowAdapter
import ai.friday.billpayment.app.uuid
import ai.friday.billpayment.app.whatsapp.FlowRequest
import ai.friday.billpayment.rxClient
import com.github.tomakehurst.wiremock.WireMockServer
import com.github.tomakehurst.wiremock.client.WireMock.containing
import com.github.tomakehurst.wiremock.client.WireMock.equalTo
import com.github.tomakehurst.wiremock.client.WireMock.matchingJsonPath
import com.github.tomakehurst.wiremock.client.WireMock.post
import io.kotest.matchers.result.shouldBeFailure
import io.kotest.matchers.result.shouldBeSuccess
import io.kotest.matchers.shouldBe
import io.micronaut.http.client.HttpClient
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode

@Execution(ExecutionMode.CONCURRENT)
class WhatsAppAdapterTest {
    @Test
    fun `should return success when meta accepts the request`() {
        val c = Setup.createTestContext()

        val flowRequest = FlowRequest(
            messageId = "test-messageId",
            templateName = "test-templateName",
            phoneNumber = "5510912345678",
            flowToken = uuid(),
        )

        c.wm.stubFor(
            post("/messages")
                .withRequestBody(
                    matchingJsonPath("$.id", equalTo(flowRequest.messageId))
                        .and(matchingJsonPath("$.to", equalTo("${flowRequest.phoneNumber}@wa.gw.msging.net")))
                        .and(matchingJsonPath("$.content.template.name", equalTo(flowRequest.templateName)))
                        .and(matchingJsonPath("$.content.template.name", equalTo(flowRequest.templateName)))
                        .and(containing(""""flow_token": "${flowRequest.flowToken}"""")),
                )
                .willReturn(aJsonResponse(status = 202)),
        )

        c.adapter.sendFlow(flowRequest)
            .shouldBeSuccess().let { messageId ->
                messageId shouldBe flowRequest.messageId
            }
    }

    @Test
    fun `should return failure when request fails`() {
        listOf(400, 401, 404, 409, 500, 504).forEach { statusCode ->
            val c = Setup.createTestContext()

            val flowRequest = FlowRequest(
                messageId = "test-messageId",
                templateName = "test-templateName",
                phoneNumber = "5510912345678",
                flowToken = uuid(),
            )

            c.wm.stubFor(
                post("/messages")
                    .withRequestBody(containing(""""flow_token": "${flowRequest.flowToken}""""))
                    .willReturn(aJsonResponse(status = statusCode)),
            )

            c.adapter.sendFlow(flowRequest).shouldBeFailure<HttpClientResponseException>()
        }
    }

    @Test
    fun `should return failure when Meta responds some unexpected code`() {
        listOf(208, 204).forEach { statusCode ->
            val c = Setup.createTestContext()

            val flowRequest = FlowRequest(
                messageId = "test-messageId",
                templateName = "test-templateName",
                phoneNumber = "5510912345678",
                flowToken = uuid(),
            )

            c.wm.stubFor(
                post("/messages")
                    .withRequestBody(containing(""""flow_token": "${flowRequest.flowToken}""""))
                    .willReturn(aJsonResponse(status = statusCode)),
            )

            c.adapter.sendFlow(flowRequest).shouldBeFailure<IllegalStateException>()
        }
    }

    private object Setup {
        data class TestContext(
            val wm: WireMockServer,
            val httpClient: HttpClient,
            val adapter: WhatsAppFlowAdapter,
        )

        fun createTestContext(): TestContext {
            val wm = Wm.mockServer()
            val httpClient = wm.rxClient()

            val adapter = WhatsAppFlowAdapter(
                httpClient = httpClient,
                configuration = mockk<WhatsAppConfiguration> {
                    every { auth } returns "test-auth"
                    every { host } returns wm.baseUrl()
                    every { namespace } returns "test-namespace"
                },
            )
            return TestContext(
                wm = wm,
                httpClient = httpClient,
                adapter = adapter,
            )
        }
    }
}