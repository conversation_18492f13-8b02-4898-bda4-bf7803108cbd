/*
package ai.friday.billpayment.adapters.kms

import io.kotest.common.runBlocking
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
internal class KmsAdapterTest {
    private val kmsAdapter = KmsAdapter(awsRegion = "us-east-1", kmsAlias = "alias/utility-connect-key")

    @Test
    internal fun `deve criptografar e depois decriptografar o dado`() {
        val data = "frase a ser criptografada"

        val encrypted = runBlocking { kmsAdapter.encryptData(data) }

        val decrypted = runBlocking { kmsAdapter.decryptData(encrypted!!) }

        data shouldBe decrypted
    }
}*/