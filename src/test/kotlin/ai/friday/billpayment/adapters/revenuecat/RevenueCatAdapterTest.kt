/*
package ai.friday.billpayment.adapters.revenuecat

import ai.friday.billpayment.app.account.AccountId
import io.kotest.matchers.shouldNotBe
import io.micronaut.rxjava2.http.client.RxHttpClient
import java.net.URI
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

private val projectId = "d4b62f03"
private val host = "https://api.revenuecat.com"
private val secret = "secret"

class RevenueCatAdapterTest {
    private val httpClient = RxHttpClient.create(URI(host).toURL())

    private val revenueCatAdapter =
        RevenueCatAdapter(
            httpClient = httpClient,
            configuration =
            RevenueCatConfiguration(
                projectId = projectId,
                secretKey = secret,
                secretKeyV1 = secret,
            ),
        )

    @Disabled
    @Test
    fun `buscar assinaturas do usuário`() {
        val response = revenueCatAdapter.getSubscriptions(AccountId("ACCOUNT-2cf66764-443d-44de-99c9-0a2c66c0e469"))

        println(response)

        response shouldNotBe null
    }

    @Disabled
    @Test
    fun `buscar produto`() {
        val response = revenueCatAdapter.getProducts()

        println(response)

        response shouldNotBe null
    }
}*/