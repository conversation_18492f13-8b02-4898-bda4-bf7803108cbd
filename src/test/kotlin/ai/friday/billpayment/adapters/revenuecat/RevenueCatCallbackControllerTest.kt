package ai.friday.billpayment.adapters.revenuecat

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.integrations.MessagePublisher
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpStatus
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Test

class RevenueCatCallbackControllerTest {
    private val messagePublisherMock: MessagePublisher = mockk(relaxed = true)

    private val revenueCatCallbackController = RevenueCatCallbackController(messagePublisherMock, mockk(relaxed = true))

    @Test
    fun `deve enviar a mensagem do evento do revenuecat para a fila`() {
        val body =
            Thread
                .currentThread()
                .contextClassLoader
                .getResource("revenuecat/initial-purchase.json")!!

        val json = body.readText()
        val httpResponse = revenueCatCallbackController.revenueCatCallbackController(json)

        httpResponse.status shouldBe HttpStatus.OK

        val capturingSlot = slot<Any>()
        verify {
            messagePublisherMock.sendMessage(any(), capture(capturingSlot))
        }

        capturingSlot.captured shouldBe parseObjectFrom<Map<String, Any>>(json)
    }
}