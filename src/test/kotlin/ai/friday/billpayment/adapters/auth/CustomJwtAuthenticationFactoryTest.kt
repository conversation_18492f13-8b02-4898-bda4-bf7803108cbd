package ai.friday.billpayment.adapters.auth

import ai.friday.billpayment.adapters.api.MsisdnAuthConfiguration
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.login.Login
import ai.friday.billpayment.app.login.LoginResult
import ai.friday.billpayment.app.login.LoginService
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.integration.SecurityFixture
import ai.friday.billpayment.integration.asJWT
import ai.friday.billpayment.integration.toSignedCookie
import ai.friday.billpayment.integration.withAudience
import ai.friday.billpayment.integration.withClaim
import ai.friday.billpayment.integration.withExpiresAt
import ai.friday.billpayment.integration.withIssuer
import ai.friday.billpayment.integration.withSubject
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import com.nimbusds.jwt.JWTClaimsSet
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.Date
import java.util.Optional
import org.junit.jupiter.api.Test

internal class CustomJwtAuthenticationFactoryTest {

    private val loginService: LoginService = mockk()
    private val userPoolAdapter: UserPoolAdapter = mockk()
    private val securityFixture = SecurityFixture()

    private val cognitoJwtAuthenticationFactory = CognitoJwtAuthenticationFactory(
        loginService = loginService,
        userPoolAdapter = userPoolAdapter,
    )

    private val msisdnJwtAuthenticationFactory = MsisdnJwtAuthenticationFactory(
        jwtValidator = JwtValidator(
            JwtValidationConfiguration(
                audience = listOf("friday.ai"),
                issuer = "https://friday.ai",
            ),
        ),
    )

    private val customJwtAuthenticationFactory = CustomJwtAuthenticationFactory(
        cognitoJwtAuthenticationFactory = cognitoJwtAuthenticationFactory,
        msisdnJwtAuthenticationFactory = msisdnJwtAuthenticationFactory,
    )

    @Test
    fun `should login on userPassword token`() {
        val jwt = securityFixture.cookieAuthOwner.asJWT()

        val accountId = jwt.jwtClaimsSet.getStringClaim(FROM_CLAIM_ACCOUNT_ID)
        val email = jwt.jwtClaimsSet.getStringClaim(FROM_CLAIM_EMAIL)
        val role = jwt.jwtClaimsSet.getStringListClaim(FROM_CLAIM_ROLES).first()

        every {
            loginService.resolveLogin(any())
        } returns LoginResult(
            login = Login(
                accountId = AccountId(accountId),
                emailAddress = EmailAddress(email),
                role = Role.valueOf(role),
            ),
            created = false,
        )

        val response = customJwtAuthenticationFactory.createAuthentication(jwt).get()

        response.attributes["sub"] shouldBe accountId
        response.attributes[TO_CLAIM_ACCOUNT_ID] shouldBe accountId
        response.attributes[TO_CLAIM_EMAIL] shouldBe email
        response.attributes[TO_CLAIM_NAME] shouldBe jwt.jwtClaimsSet.getClaim(FROM_CLAIM_NAME)
        response.roles shouldBe jwt.jwtClaimsSet.getClaim(FROM_CLAIM_ROLES)
        response.attributes[TO_CLAIM_PROVIDER_NAME] shouldBe ProviderName.COGNITO
    }

    @Test
    fun `should login as GUEST when mobilePhone is verified`() {
        val msisdnAuthConfiguration = MsisdnAuthConfiguration(
            audience = "friday.ai",
            issuer = "https://friday.ai",
            duration = Duration.ofMinutes(5),
            secret = "PleaseChangeForANewOne",
        )

        val accountId = "accountId"
        val mobilePhone = MobilePhone("+*************")

        val jwt = JWTClaimsSet.Builder().withSubject(accountId)
            .withIssuer(msisdnAuthConfiguration.issuer)
            .withAudience(msisdnAuthConfiguration.audience)
            .withClaim("email", "${mobilePhone.msisdn}@friday.ai")
            .withExpiresAt(
                Date.from(
                    getZonedDateTime()
                        .plus(msisdnAuthConfiguration.duration).toInstant(),
                ),
            ).toSignedCookie()
            .asJWT()

        val email = jwt.jwtClaimsSet.getStringClaim(FROM_CLAIM_EMAIL)

        every {
            loginService.resolveLogin(any())
        } returns LoginResult(
            login = Login(
                accountId = AccountId(accountId),
                emailAddress = EmailAddress(email),
                role = Role.GUEST,
            ),
            created = false,
        )

        val response = customJwtAuthenticationFactory.createAuthentication(jwt).get()
        response.attributes["sub"] shouldBe accountId
        response.attributes[TO_CLAIM_ACCOUNT_ID] shouldBe accountId
        response.attributes[TO_CLAIM_EMAIL] shouldBe email
        response.attributes[TO_CLAIM_NAME] shouldBe accountId
        response.roles shouldBe listOf(Role.GUEST.name, Role.GUEST_OTP.name)
        response.attributes[TO_CLAIM_PROVIDER_NAME] shouldBe ProviderName.MSISDN
    }

    @Test
    fun `should return optional empty when issuer does not match`() {
        val idToken = JWTClaimsSet.Builder()
            .withSubject("auth0|11111")
            .withAudience("another_audience")
            .withIssuer("https://another.issuer.com/")
            .withClaim("email", "")
            .withClaim("name", "Cliente 11111")
            .withExpiresAt(Date(Instant.now().plus(1, ChronoUnit.DAYS).toEpochMilli()))
            .toSignedCookie()

        val jwt = idToken.asJWT()

        customJwtAuthenticationFactory.createAuthentication(jwt) shouldBe Optional.empty()
    }
}