package ai.friday.billpayment.adapters.instrument

import ai.friday.morning.log.andAppend
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.config.NamingConvention
import jakarta.annotation.PostConstruct
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class MeterRegistryDecorator(
    private val meterRegistry: MeterRegistry,
) {
    private val logger = LoggerFactory.getLogger(MeterRegistryDecorator::class.java)

    @PostConstruct
    fun initialize() {
        val commonTags = mapOf(
            "env" to (System.getenv("DD_ENV") ?: "missing_environment_property"),
            "service" to (System.getenv("DD_SERVICE") ?: "missing_service_property"),
        )

        val common = buildList(commonTags.size * 2) {
            commonTags.forEach { (key, value) ->
                this.add(key); this.add(value.toString())
            }
        }

        meterRegistry.config()
            .namingConvention(NamingConvention.dot)
            .commonTags(*common.toTypedArray())

        val markers = Markers.append("commonTags", commonTags)
            .andAppend("config", meterRegistry.config())

        logger.info(markers, "MeterRegistryDecorator#initialize")
    }
}