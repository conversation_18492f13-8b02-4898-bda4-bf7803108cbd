/*
package ai.friday.billpayment.adapters.userpilot

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
@MicronautTest(environments = [FRIDAY_ENV])
class UserPilotAdapterIntegrationTest(private val userPilotAdapter: UserPilotAdapter) {

    private val accountId = AccountId()

    @Test
    fun `register`() {
        val emailAddress = EmailAddress("<EMAIL>")
        userPilotAdapter.register(
            accountId,
            "Teste Integrado",
            emailAddress,
            UserAccountType.FULL_ACCOUNT,
            getZonedDateTime(),
        )
    }

    @Test
    fun `trackEvent`() {
        userPilotAdapter.trackEvent(accountId, "send_bill_by_email")
    }
}*/