/*
package ai.friday.billpayment.adapters.settlement

import ai.friday.billpayment.app.bill.BarCode
import io.mockk.mockk
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
class SettlementAdapterTest {
    private val configuration = TestSettlementAdapaterConfiguration(
        host = "https://liquidacao.meupagador.com.br",
        username = "77cfdc76-111e-4e8c-82e8-d3acdc013fb5",
        password = "\$2y\$19\$npO3lIz0ToTa.YZ/xWx07eymYxtSw.Y8QigUc9HaT4uJsewvsLKL2",
        validatePath = "/validate",
    )

    private val settlementAdapter = SettlementAdapter(configuration = configuration, httpConfiguration = mockk(relaxed = true))

    @Test
    fun validate() {
        // val result = settlementAdapter.validate(BarCode.of("34195711800005000001570000582111500052061000"))
        val result = settlementAdapter.validate(BarCode.of("33690000090000001000975961722131600000000000000"))

        println(result)
    }
}

data class TestSettlementAdapaterConfiguration(
    override val host: String,
    override val username: String,
    override val password: String,
    override val validatePath: String,
) : SettlementAdapaterConfiguration*/