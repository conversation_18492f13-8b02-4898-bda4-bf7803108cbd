package ai.friday.billpayment.adapters.quod

import DynamoDBUtils
import ai.friday.billpayment.adapters.dynamodb.CreditCardOwnershipValidationDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.CreditCardScoreDbRepository
import ai.friday.billpayment.adapters.dynamodb.CreditCardScoreValidationDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.CreditCardValidationDbRepository
import ai.friday.billpayment.app.integrations.CreditCardOwnership
import ai.friday.billpayment.app.integrations.CreditCardOwnershipValidationRequest
import ai.friday.billpayment.app.integrations.CreditCardScore
import ai.friday.billpayment.app.integrations.CreditCardScoreValidationRequest
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.client.exceptions.HttpClientException
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.reactivex.Flowable
import java.net.URL
import java.util.stream.Stream
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class QuodAdapterTest {

    private val configuration = QuodAdapterConfiguration(
        host = "http://quodHost",
        username = "quodUsername",
        password = "quodPassword",
        authReport = "quodAuthReportPath",
        authReportVersion = "2.0",
        authCCScore = "quodAuthCCScorePath",
        authCCScoreVersion = "2.1",
    )

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbEnhancedClient = DynamoDBUtils.setupDynamoDB()

    private val creditCardOwnershipValidationDAO = CreditCardOwnershipValidationDynamoDAO(dynamoDbEnhancedClient)
    private val creditCardScoreValidationDAO = CreditCardScoreValidationDynamoDAO(dynamoDbEnhancedClient)

    val repository = CreditCardValidationDbRepository(creditCardOwnershipValidationDAO)
    val scoreRepository = CreditCardScoreDbRepository(creditCardScoreValidationDAO)

    lateinit var quodAdapter: QuodAdapter

    private val rxHttpClient: RxHttpClient = mockk()

    private val request = CreditCardOwnershipValidationRequest(
        bin = "123456",
        lastFourDigits = "1234",
        cpf = "12345678901",
    )

    private val requestScore = CreditCardScoreValidationRequest(
        bin = "123456",
        lastFourDigits = "1234",
        cpf = "12345678901",
        totalValue = "0",
    )

    private fun buildAuthReportResponseTO(state: Long, creditCardOwnership: String): ValidateOwnershipResponseTO {
        return ValidateOwnershipResponseTO(
            authReportResponseEx = AuthReportResponseExTO(
                response = QuodResponseTO(
                    header = HeaderTO(
                        status = state,
                        message = "",
                    ),
                    records = RecordsTO(
                        authReportOutput = listOf(
                            AuthReportOutputTO(
                                cpf = "",
                                creditCardOwnership = creditCardOwnership,
                                consumerDataValidation = ConsumerDataValidationTO(
                                    personName = "",
                                    cpfStatus = "",
                                    deceased = "",
                                    vip = "",
                                    pep = "",
                                ),
                                addressAuthenticity = AddressAuthenticityTO(
                                    score = "",
                                    inactive = "",
                                    minor = "",
                                    confirmedPhone = "",
                                    confirmedEmail = "",
                                ),
                            ),
                        ),
                    ),
                ),
            ),
        )
    }

    private fun buildScoreResponseTO(state: Long, code: String): ValidateCreditCardScoreResponseTO {
        return ValidateCreditCardScoreResponseTO(
            authCCScoreResponseEx = AuthCCScoreResponseExTO(
                response = QuodResponseCCScoreTO(
                    header = HeaderTO(
                        status = state,
                        message = "",
                    ),
                    records = RecordsCCScoreTO(
                        authccScoreOutput = listOf(
                            AuthCCScoreOutputTO(
                                cpf = "",
                                creditCardOwnership = "",
                                creditCardScore = "",
                                reasonCode = ReasonCodeTO(
                                    listOf(
                                        ReasonCodesTO(
                                            code = code,
                                            message = "",
                                        ),
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),
            ),
        )
    }

    @BeforeEach
    fun setup() {
        mockkStatic("io.micronaut.rxjava2.http.client.RxHttpClient")
        every {
            RxHttpClient.create(URL(configuration.host))
        } returns rxHttpClient

        quodAdapter = QuodAdapter(
            repository = repository,
            scoreRepository = scoreRepository,
            configuration = configuration,
            httpClient = rxHttpClient,
        )

        createBillPaymentTable(dynamoDB)
    }

    @AfterEach
    fun teardown() {
        unmockkStatic("io.micronaut.rxjava2.http.client.RxHttpClient")
    }

    @Test
    fun `deve salvar o resultado ao receber uma resposta OK no modo Ownership`() {
        val authReportResponseTO = buildAuthReportResponseTO(0, QuodOwnershipStatusV15.MATCH.code)
        every {
            rxHttpClient.retrieve(
                any<HttpRequest<ValidateOwnershipRequestTO>>(),
                any<Argument<ValidateOwnershipResponseTO>>(),
            )
        } returns Flowable.just(authReportResponseTO)

        val resultOwnership = quodAdapter.validateOwnership(
            validationRequest = request,
        )

        resultOwnership.isRight() shouldBe true
        resultOwnership.map {
            it shouldBe CreditCardOwnership.IS_OWNER
        }
        val response = repository.find(request.cpf, request.bin, request.lastFourDigits).single()

        response.response shouldBe authReportResponseTO
        response.apiVersion shouldBe configuration.authReportVersion
    }

    @Test
    fun `deve salvar o resultado ao receber uma resposta OK no modo Score`() {
        val scoreResponseTO = buildScoreResponseTO(0, "1.1")
        every {
            rxHttpClient.retrieve(
                any<HttpRequest<ValidateCreditCardScoreRequestTO>>(),
                any<Argument<ValidateCreditCardScoreResponseTO>>(),
            )
        } returns Flowable.just(scoreResponseTO)

        val resultScore = quodAdapter.creditcardScore(
            scoreRequest = requestScore,
        )

        resultScore.isRight() shouldBe true
        resultScore.map {
            it shouldBe CreditCardScore.MATCH
        }
        val response = scoreRepository.find(request.cpf, request.bin, request.lastFourDigits).single()

        response.response shouldBe scoreResponseTO
        response.apiVersion shouldBe configuration.authReportVersion
    }

    @Test
    fun `nao deve salvar o resultado ao receber uma resposta diferente de OK no modo Score`() {
        val scoreResponseTO = buildScoreResponseTO(0, "1.2")
        every {
            rxHttpClient.retrieve(
                any<HttpRequest<ValidateCreditCardScoreRequestTO>>(),
                any<Argument<ValidateCreditCardScoreResponseTO>>(),
            )
        } returns Flowable.just(scoreResponseTO)

        val resultScore = quodAdapter.creditcardScore(
            scoreRequest = requestScore,
        )

        resultScore.isRight() shouldBe true
        resultScore.map {
            it shouldBe CreditCardScore.MANUAL_CHECK
        }

        val response = repository.find(request.cpf, request.bin, request.lastFourDigits)

        response shouldBe emptyList()
    }

    @Test
    fun `nao deve salvar o resultao ao receber uma resposta diferente de OK`() {
        every {
            rxHttpClient.retrieve(
                any<HttpRequest<ValidateOwnershipRequestTO>>(),
                any<Argument<ValidateOwnershipResponseTO>>(),
            )
        } throws HttpClientException("")

        val result = quodAdapter.validateOwnership(
            validationRequest = request,
        )

        result.isLeft() shouldBe true

        val response = repository.find(request.cpf, request.bin, request.lastFourDigits)

        response shouldBe emptyList()
    }

    @Test
    fun `nao deve salvar o resultado ao receber uma resposta e for um erro mapeado`() {
        val errorResponse = buildAuthReportResponseTO(1200, "7")

        every {
            rxHttpClient.retrieve(
                any<HttpRequest<ValidateOwnershipRequestTO>>(),
                any<Argument<ValidateOwnershipResponseTO>>(),
            )
        } returns Flowable.just(errorResponse)

        val result = quodAdapter.validateOwnership(
            validationRequest = request,
        )

        result.isLeft() shouldBe true

        val response = repository.find(request.cpf, request.bin, request.lastFourDigits)

        response shouldBe emptyList()
    }

    @ParameterizedTest
    @MethodSource("ownerCheck")
    fun `deve retornar tipo de owner baseado na resposta de ownership`(
        quodOwnershipStatus: QuodOwnershipStatusV15,
        creditCardOwnership: CreditCardOwnership,
    ) {
        val successResponse = buildAuthReportResponseTO(0, quodOwnershipStatus.code)
        every {
            rxHttpClient.retrieve(
                any<HttpRequest<ValidateOwnershipRequestTO>>(),
                any<Argument<ValidateOwnershipResponseTO>>(),
            )
        } returns Flowable.just(successResponse)

        val result = quodAdapter.validateOwnership(
            validationRequest = request,
        )

        result.isRight() shouldBe true
        result.map {
            it shouldBe creditCardOwnership
        }
    }

    companion object {
        @JvmStatic
        fun ownerCheck(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    QuodOwnershipStatusV15.NO_MATCH,
                    CreditCardOwnership.UNKNOWN,
                ),
                Arguments.arguments(QuodOwnershipStatusV15.NO_CREDIT_CARDS_AVAILABLE, CreditCardOwnership.NOT_OWNER),
                Arguments.arguments(QuodOwnershipStatusV15.NO_MATCH_AT_LEAST_ONE_KNOWN, CreditCardOwnership.UNKNOWN),
                Arguments.arguments(QuodOwnershipStatusV15.NO_MATCH_ALL_UNKNOWN, CreditCardOwnership.UNKNOWN),
                Arguments.arguments(QuodOwnershipStatusV15.DECEASED, CreditCardOwnership.NOT_OWNER),
                Arguments.arguments(QuodOwnershipStatusV15.UNDERAGE, CreditCardOwnership.UNKNOWN),
                Arguments.arguments(QuodOwnershipStatusV15.NOT_SHARED, CreditCardOwnership.UNKNOWN),
                Arguments.arguments(QuodOwnershipStatusV15.MATCH, CreditCardOwnership.IS_OWNER),
            )
        }
    }
}