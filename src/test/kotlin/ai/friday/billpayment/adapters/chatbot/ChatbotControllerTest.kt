package ai.friday.billpayment.adapters.chatbot

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.chatbot.ChatbotService
import ai.friday.billpayment.app.chatbot.GetAccountOrganizations
import ai.friday.billpayment.app.chatbot.NotifyBalanceError
import ai.friday.billpayment.app.chatbot.OtherInstitutionsResponseTO
import ai.friday.billpayment.app.chatbot.PaymentOrganizationResult
import ai.friday.billpayment.app.forecast.ForecastPeriod
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.itp.ITPService
import ai.friday.billpayment.app.itp.ITPServiceError
import ai.friday.billpayment.app.itp.PaymentIntentId
import ai.friday.billpayment.app.onepixpay.OnePixPay
import ai.friday.billpayment.app.onepixpay.OnePixPayRepository
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.onepixpay.OnePixPayStatus
import ai.friday.billpayment.app.onepixpay.UUIDOnePixPayId
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.BILL_ID_3
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.WALLET_ID_2
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpStatus
import io.micronaut.security.authentication.Authentication
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalTime
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

internal class ChatbotControllerTest {
    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()
    private val anotherWallet = walletFixture.buildWallet(
        id = WalletId(WALLET_ID_2),
        name = "carteira2",
        walletFounder = walletFixture.founder.copy(accountId = walletFixture.assistantAccount.accountId),
    )

    private val billRepository: BillRepository = mockk()
    private val onePixPayRepository: OnePixPayRepository = mockk(relaxed = true)
    private val onePixPayService: OnePixPayService = mockk(relaxed = true)
    private val chatbotService: ChatbotService = mockk()
    private val itpService: ITPService = mockk()
    private val walletService: WalletService = mockk()
    private val accountService: AccountService = mockk()

    private val chatbotController =
        ChatbotController(
            chatbotService = chatbotService,
            itpService = itpService,
            billRepository = billRepository,
            onePixPayRepository = onePixPayRepository,
            onePixPayService = onePixPayService,
            walletService = walletService,
            loginService = mockk(),
            accountService = accountService,
            chatbotMessagePublisher = mockk(relaxed = true),
        )

    private val buttonPayload = "${wallet.id.value}#${ForecastPeriod.TODAY.name}"

    private val authentication: Authentication = mockk {
        every {
            name
        } returns wallet.founder.accountId.value
    }

    @Nested
    @DisplayName("listPaymentOrganizations")
    inner class ListPaymentOrganizations {
        @Test
        fun `deve retornar Internal Server Error ao tentar listar as organizações e falhar`() {
            every {
                itpService.listPaymentOrganizations(any())
            } returns ITPServiceError.ITPErrorWithException.ITPProviderError(NoStackTraceException()).left()

            val response =
                chatbotController.listPaymentOrganizations(
                    buttonPayload = buttonPayload,
                    authentication = authentication,
                )

            response.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        }

        @Test
        fun `deve retornar bad request se o accountid não existir`() {
            every {
                itpService.listPaymentOrganizations(any())
            } returns ITPServiceError.AccountNotFound().left()

            val response =
                chatbotController.listPaymentOrganizations(
                    buttonPayload = buttonPayload,
                    authentication = authentication,
                )

            response.status shouldBe HttpStatus.BAD_REQUEST
        }

        @Test
        fun `deve retornar a lista de organizações quando a consulta funcionar`() {
            val paymentOrganizationResultMock = mockk<PaymentOrganizationResult>() {
                every {
                    bankISPB
                } returns "1234"
            }
            every {
                itpService.listPaymentOrganizations(any())
            } returns GetAccountOrganizations(
                size = 1,
                document = Document(DOCUMENT),
                otherFinancialInstitutions = listOf(
                    OtherInstitutionsResponseTO(id = "id-other-institution", title = "title"),
                ),
                result = mapOf(0 to paymentOrganizationResultMock),
            ).right()

            val response =
                chatbotController.listPaymentOrganizations(
                    buttonPayload = buttonPayload,
                    authentication = authentication,
                )

            response.status shouldBe HttpStatus.OK
            with(response.getBody(ChatbotController.ListPaymentOrganizationsResponseTO::class.java).get()) {
                this.size shouldBe 1
                this.document shouldBe DOCUMENT
                this.walletId shouldBe wallet.id.value
                this.otherFinancialInstitutions shouldHaveSize 1
                this.otherFinancialInstitutions[0].id shouldBe "id-other-institution"
                this.result[0] shouldBe paymentOrganizationResultMock
            }
        }
    }

    @Nested
    @DisplayName("paymentIntent")
    inner class PaymentIntent {

        private val paymentIntentTO = PaymentIntentTO(
            walletId = wallet.id.value,
            document = DOCUMENT,
            authorizationServerId = "authorizationServerId",
            authorizationServerName = "authorizationServerName",
            routingNo = 1234,
            accountNo = 2222,
            accountDv = "0",
            bankISPB = null,
            bankNo = null,
            accountType = "CACC",
        )

        @Test
        fun `deve retornar BAD_REQUEST quando não econtrar o account`() {
            every {
                itpService.createPaymentIntent(any(), any())
            } returns ITPServiceError.AccountNotFound(message = "FAKE").left()

            val response =
                chatbotController.paymentIntent(
                    buttonPayload = buttonPayload,
                    body = paymentIntentTO,
                    authentication = authentication,
                )

            response.status shouldBe HttpStatus.BAD_REQUEST
        }

        @Test
        fun `deve retornar BAD_REQUEST quando o saldo for suficiente`() {
            every {
                itpService.createPaymentIntent(any(), any())
            } returns ITPServiceError.SufficientBalance(message = "FAKE").left()

            val response =
                chatbotController.paymentIntent(
                    buttonPayload = buttonPayload,
                    body = paymentIntentTO,
                    authentication = authentication,
                )

            response.status shouldBe HttpStatus.BAD_REQUEST
            response.getBody(ResponseTO::class.java).get().code shouldBe "4002"
        }

        @Test
        fun `deve retornar INTERNAL_SERVER_ERROR quando ocorrer alguma falha`() {
            every {
                itpService.createPaymentIntent(any(), any())
            } returns ITPServiceError.ITPErrorWithException.ITPProviderError(NoStackTraceException()).left()

            val response =
                chatbotController.paymentIntent(
                    buttonPayload = buttonPayload,
                    body = paymentIntentTO,
                    authentication = authentication,
                )

            response.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        }

        @Test
        fun `deve retornar CREATED com o id gerado quando gerar o payment intent`() {
            every {
                itpService.createPaymentIntent(any(), any())
            } returns PaymentIntentId("id-gerado").right()

            val response =
                chatbotController.paymentIntent(
                    buttonPayload = buttonPayload,
                    body = paymentIntentTO,
                    authentication = authentication,
                )

            response.status shouldBe HttpStatus.CREATED
            response.getBody(ChatbotController.PaymentIntentResponseTO::class.java)
                .get().paymentIntentId shouldBe "id-gerado"
            response.getBody(ChatbotController.PaymentIntentResponseTO::class.java)
                .get().token shouldBe "id-gerado"
        }
    }

    @Nested
    @DisplayName("notifyBalance")
    inner class NotifyBalance {
        @Test
        fun `deve retornar CREATED quando notificar`() {
            every {
                chatbotService.notifyBalance(any())
            } returns Unit.right()

            val response = chatbotController.notifyBalance(authentication = authentication)

            response.status shouldBe HttpStatus.CREATED
        }

        @Test
        fun `deve retornar BAD_REQUEST quando não encontrar a carteira`() {
            every {
                chatbotService.notifyBalance(any())
            } returns NotifyBalanceError.WalletNotFound.left()

            val response = chatbotController.notifyBalance(authentication = authentication)

            response.status shouldBe HttpStatus.BAD_REQUEST
        }

        @Test
        fun `deve retornar BAD_REQUEST quando o saldo é suficiente`() {
            every {
                chatbotService.notifyBalance(any())
            } returns NotifyBalanceError.SufficientBalance.left()

            val response = chatbotController.notifyBalance(authentication = authentication)

            response.status shouldBe HttpStatus.BAD_REQUEST
            response.getBody(Argument.of(ResponseTO::class.java)).get().code shouldBe "4002"
        }
    }

    @Nested
    @DisplayName("onePixPay")
    inner class OnePixPayTest {
        @Test
        fun `deve retornar BAD_REQUEST ao enviar um id com formato inválido`() {
            val response =
                chatbotController.getOnePixPay(authentication = authentication, onePixPayIdValue = "valor-invalido")

            response.status shouldBe HttpStatus.BAD_REQUEST
            response.getBody(Argument.of(String::class.java)).isEmpty shouldBe true
        }

        @Test
        fun `deve retornar NOT_FOUND ao enviar um id que nao existe`() {
            every { onePixPayRepository.find(any()) } answers {
                throw ItemNotFoundException("")
            }

            val response =
                chatbotController.getOnePixPay(
                    authentication = authentication,
                    onePixPayIdValue = "OPP-ba33df39-ae5a-41ac-af92-cca25e0606e7",
                )

            response.status shouldBe HttpStatus.NOT_FOUND
            response.getBody(Argument.of(String::class.java)).isEmpty shouldBe true
        }

        @Test
        fun `deve retornar NOT_FOUND quando o 1-pix-pay nao pertencer ao usuario fazendo a requisicao`() {
            every { walletService.findWallet(wallet.id) } returns wallet
            every { walletService.findWallet(anotherWallet.id) } returns anotherWallet

            val onePixKeyId = UUIDOnePixPayId()

            every { onePixPayRepository.find(onePixKeyId) } returns OnePixPay(
                id = onePixKeyId,
                walletId = anotherWallet.id,
                billIds = listOf(),
                qrCode = "qr-code",
                status = OnePixPayStatus.CREATED,
                paymentLimitTime = LocalTime.NOON,
                fundsReceived = false,
            )

            val response =
                chatbotController.getOnePixPay(
                    authentication = authentication,
                    onePixPayIdValue = onePixKeyId.value,
                )

            response.status shouldBe HttpStatus.NOT_FOUND
            response.getBody(Argument.of(String::class.java)).isEmpty shouldBe true
        }

        @Test
        fun `deve retornar NOT_FOUND quando o 1-pix pay nao está no estado de CREATED`() {
            val onePixKeyId = UUIDOnePixPayId()

            every { onePixPayRepository.find(onePixKeyId) } returns OnePixPay(
                id = onePixKeyId,
                walletId = wallet.id,
                billIds = listOf(),
                qrCode = "qr-code",
                status = OnePixPayStatus.EXPIRED,
                paymentLimitTime = LocalTime.NOON,
                fundsReceived = false,
            )

            val response =
                chatbotController.getOnePixPay(
                    authentication = authentication,
                    onePixPayIdValue = onePixKeyId.value,
                )

            response.status shouldBe HttpStatus.NOT_FOUND
            response.getBody(Argument.of(String::class.java)).isEmpty shouldBe true
        }

        @Test
        fun `deve retornar NOT_FOUND quando nao encontrar um 1-pix-pay para o dia atual`() {
            every { walletService.findWallet(wallet.id) } returns wallet

            val aBill = getActiveBill(walletId = wallet.id, billId = BillId(BILL_ID_2)).copy(
                recipient = Recipient("Colégio"),
                amountTotal = 100L,
            )
            val anotherBill =
                getActiveBill(walletId = wallet.id, billId = BillId(BILL_ID_3)).copy(
                    recipient = Recipient("Cartão"),
                    amountTotal = 200L,
                )

            every { billRepository.findBill(aBill.billId, wallet.id) } returns aBill
            every { billRepository.findBill(anotherBill.billId, wallet.id) } returns anotherBill

            val onePixKeyId = UUIDOnePixPayId()

            every { onePixPayRepository.find(onePixKeyId) } returns OnePixPay(
                id = onePixKeyId,
                walletId = wallet.id,
                billIds = listOf(aBill.billId, anotherBill.billId),
                qrCode = "qr-code",
                status = OnePixPayStatus.CREATED,
                paymentLimitTime = LocalTime.NOON,
                fundsReceived = false,
                created = getZonedDateTime().minusDays(1),
            )

            val response =
                chatbotController.getOnePixPay(
                    authentication = authentication,
                    onePixPayIdValue = onePixKeyId.value,
                )

            response.status shouldBe HttpStatus.NOT_FOUND
            response.getBody(Argument.of(String::class.java)).isEmpty shouldBe true
        }

        @Test
        fun `deve retornar o resumo das contas, o QRCode e alterar o status do 1-pix-pay para REQUESTED`() {
            every { walletService.findWallet(wallet.id) } returns wallet

            val aBill = getActiveBill(walletId = wallet.id, billId = BillId(BILL_ID_2)).copy(
                recipient = Recipient("Colégio"),
                amountTotal = 100L,
            )
            val anotherBill =
                getActiveBill(walletId = wallet.id, billId = BillId(BILL_ID_3)).copy(
                    recipient = Recipient("Cartão"),
                    amountTotal = 200L,
                )

            every { billRepository.findBill(aBill.billId, wallet.id) } returns aBill
            every { billRepository.findBill(anotherBill.billId, wallet.id) } returns anotherBill

            val onePixKeyId = UUIDOnePixPayId()

            every { onePixPayRepository.find(onePixKeyId) } returns OnePixPay(
                id = onePixKeyId,
                walletId = wallet.id,
                billIds = listOf(aBill.billId, anotherBill.billId),
                qrCode = "qr-code",
                status = OnePixPayStatus.CREATED,
                paymentLimitTime = LocalTime.NOON,
                fundsReceived = false,
            )

            val response =
                chatbotController.getOnePixPay(
                    authentication = authentication,
                    onePixPayIdValue = onePixKeyId.value,
                )

            response.status shouldBe HttpStatus.OK

            with(response.getBody(Argument.of(OnePixPayResponseTO::class.java)).get()) {
                qrCode shouldBe "qr-code"
                summary shouldBe "Conta *Colégio* de *R$ 1,00*\n\nConta *Cartão* de *R$ 2,00*"
                size shouldBe 2
            }

            val slot = slot<OnePixPay>()

            verify(exactly = 1) {
                onePixPayService.save(capture(slot))
            }

            with(slot.captured) {
                status shouldBe OnePixPayStatus.REQUESTED
            }
        }
    }
}