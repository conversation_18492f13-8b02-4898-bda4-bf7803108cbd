package ai.friday.billpayment.adapters.chatbot

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.LoginDbRepository
import ai.friday.billpayment.adapters.dynamodb.LoginDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.chatbot.ChatbotQrCodeError
import ai.friday.billpayment.app.chatbot.ChatbotQrCodeResult
import ai.friday.billpayment.app.chatbot.ChatbotService
import ai.friday.billpayment.app.forecast.ForecastPeriod
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pix.PixQrCode
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource

@MicronautTest(environments = [FRIDAY_ENV])
class ChatbotPixCopyAndPasteTest(
    embeddedServer: EmbeddedServer,
    val dynamoDB: AmazonDynamoDB,
) {
    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    val dynamoDbDAO = DynamoDbDAO(amazonDynamoDB = dynamoDB)
    val dynamoClient = getDynamoDB()

    private val loginRepository = LoginDbRepository(dynamoDbDAO = LoginDynamoDAO(dynamoClient), TransactionDynamo(dynamoClient))

    private val walletDAO = WalletDynamoDAO(dynamoClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(dynamoClient)
    private val inviteDAO = InviteDynamoDAO(dynamoClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(dynamoClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(dynamoClient)

    private val walletDBRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = mockk(),
    )

    private val accountId = AccountId(ACCOUNT_ID)
    private val walletId = WalletId("ACCOUNT-4828265b-6fee-4101-8b3a-3aa039891499")
    private val externalUserId = "<EMAIL>"

    private val walletFixture = WalletFixture()

    private val wallet = walletFixture.buildWallet()

    @MockBean(ChatbotService::class)
    fun chatbotService(): ChatbotService = chatbotService
    private val chatbotService: ChatbotService = mockk()

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)

        loginRepository.createLogin(
            providerUser = ProviderUser(
                id = accountId.value,
                providerName = ProviderName.WHATSAPP,
                username = "",
                emailAddress = EmailAddress(email = externalUserId),
            ),
            id = accountId,
            role = Role.OWNER,
        )

        walletDBRepository.save(wallet)
    }

    @Test
    fun `deve retornar o código pix para o dia de hoje e a mensaem explicativa identificando a carteira principal quando o não for informado carteira e período mas o header existir`() {
        every {
            chatbotService.createQrCode(any(), any())
        } returns ChatbotQrCodeResult(
            PixQrCode(value = "pixcopiaecola", amount = 2000),
            wallet = wallet,
            forecastPeriod = ForecastPeriod.TODAY,
            includeScheduledBillsOnly = false,
        ).right()

        val request = buildRequestWithoutWalletAndPeriod(externalUserId, buttonPayload = "")

        val response = client.toBlocking()
            .exchange(request, Argument.of(PixCopyAndPasteResponseTO::class.java), Argument.of(String::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get()) {
            qrCode shouldBe "pixcopiaecola"
            message shouldBe "*Copie e cole o código abaixo no App do seu banco* para realizar um Pix no valor de *R\$ 20,00* referente aos pagamentos agendados para *hoje*."
            title shouldBe "Saldo será adicionado na carteira carteira"
        }
    }

    @Test
    fun `deve retornar o código pix para o dia de hoje e a mensaem explicativa identificando a carteira principal quando o não for informado carteira e período`() {
        every {
            chatbotService.createQrCode(any(), any())
        } returns ChatbotQrCodeResult(
            PixQrCode(value = "pixcopiaecola", amount = 2000),
            wallet = wallet,
            forecastPeriod = ForecastPeriod.TODAY,
            includeScheduledBillsOnly = false,
        ).right()

        val request = buildRequestWithoutWalletAndPeriod(externalUserId, buttonPayload = null)

        val response = client.toBlocking()
            .exchange(request, Argument.of(PixCopyAndPasteResponseTO::class.java), Argument.of(String::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get()) {
            qrCode shouldBe "pixcopiaecola"
            message shouldBe "*Copie e cole o código abaixo no App do seu banco* para realizar um Pix no valor de *R\$ 20,00* referente aos pagamentos agendados para *hoje*."
            title shouldBe "Saldo será adicionado na carteira carteira"
        }
    }

    @Test
    fun `deve retornar o codigo pix e a mensagem explicativa para contas agendadas quando o período for TODAY`() {
        every {
            chatbotService.createQrCode(
                accountId,
                walletId,
                ForecastPeriod.TODAY,
            )
        } returns ChatbotQrCodeResult(
            PixQrCode(value = "pixcopiaecola", amount = 2000),
            wallet = wallet,
            forecastPeriod = ForecastPeriod.TODAY,
            includeScheduledBillsOnly = false,
        ).right()

        val request = buildRequest(externalUserId, ForecastPeriod.TODAY)

        val response = client.toBlocking()
            .exchange(request, Argument.of(PixCopyAndPasteResponseTO::class.java), Argument.of(String::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get()) {
            qrCode shouldBe "pixcopiaecola"
            message shouldBe "*Copie e cole o código abaixo no App do seu banco* para realizar um Pix no valor de *R\$ 20,00* referente aos pagamentos agendados para *hoje*."
            title.shouldBeNull()
        }
    }

    @ParameterizedTest
    @EnumSource(ForecastPeriod::class, mode = EnumSource.Mode.EXCLUDE, names = ["TODAY"])
    fun `deve retornar o codigo pix e a mensagem explicativa para todas as contas quando o período for diferente de TODAY`(
        forecastPeriod: ForecastPeriod,
    ) {
        val forecastPeriodCapturingSlot = slot<ForecastPeriod>()
        every {
            chatbotService.createQrCode(
                accountId,
                walletId,
                capture(forecastPeriodCapturingSlot),
            )
        } returns ChatbotQrCodeResult(
            PixQrCode(value = "pixcopiaecola", amount = 2000),
            wallet = wallet,
            forecastPeriod = forecastPeriod,
            includeScheduledBillsOnly = false,
        ).right()

        val request = buildRequest(externalUserId, forecastPeriod)

        val response = client.toBlocking()
            .exchange(request, Argument.of(PixCopyAndPasteResponseTO::class.java), Argument.of(String::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get()) {
            qrCode shouldBe "pixcopiaecola"
            message shouldBe "*Copie e cole o código abaixo no App do seu banco* para realizar um Pix no valor de *R\$ 20,00* referente aos pagamentos ${forecastPeriod.toDescription()}."
        }

        forecastPeriodCapturingSlot.captured shouldBe forecastPeriod
    }

    @Test
    fun `deve retornar erro quando o saldo for suficiente para as contas agendadas para hoje`() {
        every {
            chatbotService.createQrCode(accountId, walletId, any())
        } returns ChatbotQrCodeError.SufficientBalance.left()

        val request = buildRequest(externalUserId, ForecastPeriod.TODAY)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(PixCopyAndPasteResponseTO::class.java), Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java)
            .get().message shouldBe "*Saldo atual suficiente* para os pagamentos agendados para *hoje*."
    }

    @ParameterizedTest
    @EnumSource(ForecastPeriod::class, mode = EnumSource.Mode.EXCLUDE, names = ["TODAY"])
    fun `deve retornar erro quando o saldo for suficiente e período diferente de TODAY`(forecastPeriod: ForecastPeriod) {
        every {
            chatbotService.createQrCode(accountId, walletId, any())
        } returns ChatbotQrCodeError.SufficientBalance.left()

        val request = buildRequest(externalUserId, forecastPeriod)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(PixCopyAndPasteResponseTO::class.java), Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java)
            .get().message shouldBe "*Saldo atual suficiente* para os pagamentos ${forecastPeriod.toDescription()}."
    }

    private fun buildRequest(externalUserId: String?, forecastPeriod: ForecastPeriod? = null): MutableHttpRequest<*> {
        return HttpRequest.GET<Any>("/chatbot/pix-copy-and-paste")
            .also { request ->
                externalUserId?.let { request.header("X-EXTERNAL-USER-ID", it) }
                forecastPeriod?.let { request.header("X-BUTTON-PAYLOAD", "${walletId.value}#$it") }
                    ?: request.header("X-BUTTON-PAYLOAD", walletId.value)
            }
    }

    private fun buildRequestWithoutWalletAndPeriod(
        externalUserId: String?,
        buttonPayload: String? = null,
    ): MutableHttpRequest<*> {
        return HttpRequest.GET<Any>("/chatbot/pix-copy-and-paste")
            .also { request ->
                externalUserId?.let { request.header("X-EXTERNAL-USER-ID", it) }
                buttonPayload?.let { buttonPayload ->
                    request.header("X-BUTTON-PAYLOAD", buttonPayload)
                }
            }
    }

    @ParameterizedTest
    @MethodSource("chatbotQrCodeErrors")
    fun `deve retornar erro quando nao for possivel criar o codigo pix`(result: ChatbotQrCodeError) {
        every {
            chatbotService.createQrCode(accountId, walletId, any())
        } returns result.left()

        val request = buildRequest(externalUserId)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(PixCopyAndPasteResponseTO::class.java), Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java)
            .get().message shouldBe "Desculpe, não consegui gerar seu código pix."
    }

    companion object {
        @JvmStatic
        fun chatbotQrCodeErrors(): Stream<ChatbotQrCodeError> {
            return Stream.of(
                ChatbotQrCodeError.AccountNotFound,
                ChatbotQrCodeError.AccountNotActive,
                ChatbotQrCodeError.ServerError,
                ChatbotQrCodeError.UserNotAllowed,
                ChatbotQrCodeError.WalletNotFound,
            )
        }
    }
}