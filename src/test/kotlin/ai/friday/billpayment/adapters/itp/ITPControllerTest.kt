package ai.friday.billpayment.adapters.itp

import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.app.itp.ConsentId
import ai.friday.billpayment.app.itp.ITPService
import ai.friday.billpayment.app.itp.ITPServiceError
import ai.friday.billpayment.app.itp.PaymentIntentId
import ai.friday.billpayment.app.itp.PaymentIntentResponse
import arrow.core.Either
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpStatus
import io.mockk.every
import io.mockk.mockk
import java.util.stream.Stream
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

internal class ITPControllerTest() {

    private val itpService: ITPService = mockk()
    private val itpController = ITPController(itpService = itpService)

    @Test
    fun `deve retornar o redirectUrl a partir do paymentIntentId fornecido`() {
        every {
            itpService.retrievePaymentRedirectUrl(any())
        } returns Either.Right(PaymentIntentResponse(PaymentIntentId(), "https://use.friday.ai", ConsentId("")))

        val response = itpController.getPaymentByToken("token")

        response.status shouldBe HttpStatus.OK
        response.body.get() shouldBe mapOf("consentPage" to "https://use.friday.ai")
    }

    @Test
    fun `deve retornar Internal Server Error quando não conseguir gerar a URL`() {
        every {
            itpService.retrievePaymentRedirectUrl(any())
        } returns Either.Left(ITPServiceError.ITPErrorWithException.ITPProviderError(exception = Exception()))

        val response = itpController.getPaymentByToken("token")

        response.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
    }

    @ParameterizedTest
    @MethodSource("notFoundExceptions")
    fun `deve retornar Bad Request quando não encontrar o usuário`(ex: ITPServiceError) {
        every {
            itpService.retrievePaymentRedirectUrl(any())
        } returns Either.Left(ex)

        val response = itpController.getPaymentByToken("token")

        response.status shouldBe HttpStatus.BAD_REQUEST
        response.getBody(ResponseTO::class.java).get().code shouldBe "4001"
    }

    @Test
    fun `deve retornar Bad Request com code 4002 quando saldo não for suficiente`() {
        every {
            itpService.retrievePaymentRedirectUrl(any())
        } returns Either.Left(ITPServiceError.SufficientBalance())

        val response = itpController.getPaymentByToken("token")

        response.status shouldBe HttpStatus.BAD_REQUEST
        response.getBody(ResponseTO::class.java).get().code shouldBe "4002"
    }

    companion object {
        @JvmStatic
        fun notFoundExceptions(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(ITPServiceError.AccountNotFound()),
                Arguments.arguments(ITPServiceError.ISPBNotFound()),
                Arguments.arguments(ITPServiceError.WalletNotFound()),
                Arguments.arguments(ITPServiceError.PaymentIntentNotFound()),
            )
        }
    }
}