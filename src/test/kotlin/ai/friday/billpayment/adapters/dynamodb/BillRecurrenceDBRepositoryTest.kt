package ai.friday.billpayment.adapters.dynamodb

import DynamoDBUtils
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.weeklyWalletRecurrenceNoEndDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.dateFormat
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

internal class BillRecurrenceDBRepositoryTest {
    private val dynamoDbEnhancedClient = DynamoDBUtils.setupDynamoDB()
    private val billRecurrenceDynamoDAO = BillRecurrenceDynamoDAO(dynamoDbEnhancedClient)

    private val billRecurrenceRepository = BillRecurrenceDBRepository(
        client = billRecurrenceDynamoDAO,
        lastLimitDateString = getLocalDate()
            .plusWeeks(2).format(dateFormat),
    )

    @Test
    fun findByWalletId() {
        val walletId = WalletId(WALLET_ID)

        billRecurrenceRepository.save(weeklyWalletRecurrenceNoEndDate.copy(walletId = walletId))
        billRecurrenceRepository.save(weeklyWalletRecurrenceNoEndDate)

        with(billRecurrenceRepository.findByWalletId(walletId)) {
            this shouldHaveSize 1
            this.first().walletId shouldBe walletId
        }
    }
}