package ai.friday.billpayment.adapters.dynamodb

import DynamoDBUtils
import io.kotest.matchers.collections.shouldContainExactly
import org.junit.jupiter.api.Test

internal class MailboxAllowedListDbRepositoryTest {

    private val enhancedClient = DynamoDBUtils.setupDynamoDB()
    private val globalDataDAO = GlobalDataDynamoDAO(enhancedClient)

    val repository = MailboxGlobalDataDbRepository(GlobalDataDbRepository(globalDataDAO))

    @Test
    fun `should save and load`() {
        val allowedList = listOf("<EMAIL>", "<EMAIL>")
        repository.saveAllowedList(allowedList)

        val loadedAllowedList = repository.loadAllowedList()

        loadedAllowedList shouldContainExactly allowedList
    }
}