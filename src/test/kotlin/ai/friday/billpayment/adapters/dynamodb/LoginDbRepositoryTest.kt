package ai.friday.billpayment.adapters.dynamodb

import DynamoDB<PERSON>tils
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow

internal class LoginDbRepositoryTest {
    private val enhancedClient = DynamoDBUtils.setupDynamoDB()
    private val dynamoDbDAO = LoginDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val loginDbRepository = LoginDbRepository(dynamoDbDAO, transactionDynamo)

    private val originalEmail = EmailAddress("<EMAIL>")
    private val providerUser = ProviderUser(
        id = "ID_FAKE_DO_PROVIDER",
        providerName = ProviderName.MSISDN,
        username = "",
        emailAddress = originalEmail,
    )

    @BeforeEach
    fun setup() {
        loginDbRepository.createLogin(providerUser, ACCOUNT.accountId, Role.GUEST)
    }

    @Test
    fun `deve trocar o email do usuário na base e desativar o login com o email antigo em todos os providers`() {
        val newEmail = EmailAddress("<EMAIL>")

        loginDbRepository.updateEmail(ACCOUNT.accountId, originalEmail, newEmail)

        val resultByNewEmail = loginDbRepository.findUserLogin(newEmail)
        resultByNewEmail shouldHaveSize 1
        resultByNewEmail.forEach {
            it.role shouldBe Role.GUEST
        }

        val resultByOldEmail = loginDbRepository.findUserLogin(originalEmail)
        resultByOldEmail shouldHaveSize 0

        loginDbRepository.findUserLogin(providerUser) shouldBe null
    }

    @Test
    fun `nao deve retornar erro caso ocorra uma remocao sem registros no banco`() {
        assertDoesNotThrow {
            loginDbRepository.remove(
                ProviderUser(
                    id = "xpto",
                    providerName = ProviderName.WHATSAPP,
                    username = "",
                    emailAddress = EmailAddress(""),
                ),
            )
        }
    }
}