package ai.friday.billpayment.adapters.dynamodb

import DynamoDB<PERSON>tils.getDynamoDB
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.bill.AutomaticPixData
import ai.friday.billpayment.app.bill.AutomaticPixPayer
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.LocalDbCreationRule.Companion.dynamoDB
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.timeFormat
import io.kotest.matchers.shouldBe
import java.time.LocalTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class BillEventDBRepositoryAutomaticPixIntegrationTest {

    private val dynamoDbEnhancedClient = getDynamoDB()
    private val billEventDAO = BillEventDynamoDAO(dynamoDbEnhancedClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(dynamoDbEnhancedClient)
    private val transactionDynamo = TransactionDynamo(dynamoDbEnhancedClient)
    private val billEventDBRepository = BillEventDBRepository(
        billEventDAO = billEventDAO,
        uniqueConstraintDAO = uniqueConstraintDAO,
        featureConfiguration = allFalseFeatureConfiguration,
        transactionDynamo = transactionDynamo,
    )

    @BeforeEach
    fun init() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
    }

    @Test
    fun `deve salvar e ler corretamente BillAdded com automaticPixAuthorizationMaximumAmount e automaticPixData`() {
        val billId = BillId("BILL-TEST-AUTOMATIC-PIX")
        val walletId = WalletId("WALLET-TEST-AUTOMATIC-PIX")
        val automaticPixAuthorizationMaximumAmount = 5000L
        val automaticPixData = AutomaticPixData(
            payer = AutomaticPixPayer(
                document = "***********",
                name = "João Silva",
            ),
            description = "Pagamento automático de conta",
            additionalInformation = "Informação adicional do PIX automático",
            contractNumber = "CONTRATO-123",
        )

        val billAdded = BillAdded(
            billId = billId,
            created = getZonedDateTime().toInstant().toEpochMilli(),
            walletId = walletId,
            description = "Conta com PIX automático",
            dueDate = getLocalDate().plusDays(5),
            amount = 10000L,
            billType = BillType.PIX,
            paymentLimitTime = LocalTime.parse("23:59", timeFormat),
            actionSource = ai.friday.billpayment.app.bill.ActionSource.Api(
                accountId = ai.friday.billpayment.app.account.AccountId("ACCOUNT-TEST"),
            ),
            effectiveDueDate = getLocalDate().plusDays(5),
            automaticPixAuthorizationMaximumAmount = automaticPixAuthorizationMaximumAmount,
            automaticPixData = automaticPixData,
        )

        billEventDBRepository.save(billAdded)

        val retrievedBill = billEventDBRepository.getBillById(billId)

        retrievedBill.isRight() shouldBe true

        val bill = retrievedBill.getOrNull()!!
        with(bill) {
            this.billId shouldBe billId
            this.walletId shouldBe walletId
            this.automaticPixAuthorizationMaximumAmount shouldBe automaticPixAuthorizationMaximumAmount
            this.automaticPixData shouldBe automaticPixData
        }

        with(bill.automaticPixData!!) {
            this.payer!!.document shouldBe "***********"
            this.payer!!.name shouldBe "João Silva"
            this.description shouldBe "Pagamento automático de conta"
            this.additionalInformation shouldBe "Informação adicional do PIX automático"
            this.contractNumber shouldBe "CONTRATO-123"
        }
    }

    @Test
    fun `deve salvar e ler corretamente BillAdded sem campos de PIX automático`() {
        val billId = BillId("BILL-TEST-NO-AUTOMATIC-PIX")
        val walletId = WalletId("WALLET-TEST-NO-AUTOMATIC-PIX")

        val billAdded = BillAdded(
            billId = billId,
            created = getZonedDateTime().toInstant().toEpochMilli(),
            walletId = walletId,
            description = "Conta sem PIX automático",
            dueDate = getLocalDate().plusDays(2),
            amount = 5000L,
            billType = BillType.PIX,
            paymentLimitTime = LocalTime.parse("23:59", timeFormat),
            actionSource = ai.friday.billpayment.app.bill.ActionSource.Api(
                accountId = ai.friday.billpayment.app.account.AccountId("ACCOUNT-TEST"),
            ),
            effectiveDueDate = getLocalDate().plusDays(2),
            automaticPixAuthorizationMaximumAmount = null,
            automaticPixData = null,
        )

        billEventDBRepository.save(billAdded)

        val retrievedBill = billEventDBRepository.getBillById(billId)

        retrievedBill.isRight() shouldBe true

        val bill = retrievedBill.getOrNull()!!
        with(bill) {
            this.billId shouldBe billId
            this.walletId shouldBe walletId
            this.automaticPixAuthorizationMaximumAmount shouldBe null
            this.automaticPixData shouldBe null
        }
    }
}