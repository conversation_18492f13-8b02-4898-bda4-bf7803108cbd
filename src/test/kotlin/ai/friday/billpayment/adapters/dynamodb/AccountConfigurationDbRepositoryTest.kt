package ai.friday.billpayment.adapters.dynamodb

import DynamoDB<PERSON>tils
import ai.friday.billpayment.app.account.AccountConfiguration
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.integration.ACCOUNT_ID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

internal class AccountConfigurationDbRepositoryTest {
    private val dynamoDbEnhancedClient = DynamoDBUtils.setupDynamoDB()
    private val accountConfigurationDbRepository = AccountConfigurationDbRepository(
        client = AccountConfigurationDynamoDAO(dynamoDbEnhancedClient),
    )

    @Test
    fun `deve salvar um accountConfiguration sem tokens`() {
        val legacyAccountConfiguration = AccountConfiguration(
            accountId = AccountId(ACCOUNT_ID),
            pushNotificationTokens = setOf(),
            receiveMonthlyStatement = false,
            freeOfFridaySubscription = false,
        )

        accountConfigurationDbRepository.save(legacyAccountConfiguration)

        val savedConfiguration = accountConfigurationDbRepository.find(AccountId(ACCOUNT_ID))

        savedConfiguration shouldBe legacyAccountConfiguration
    }
}