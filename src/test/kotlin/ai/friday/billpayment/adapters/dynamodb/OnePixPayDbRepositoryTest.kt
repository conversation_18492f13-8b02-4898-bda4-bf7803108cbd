package ai.friday.billpayment.adapters.dynamodb

import DynamoDBUtils
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.onepixpay.OnePixPay
import ai.friday.billpayment.app.onepixpay.OnePixPayStatus
import ai.friday.billpayment.app.onepixpay.UUIDOnePixPayId
import ai.friday.billpayment.getNotScheduledBillsEnoughToNotifyOnePixPay
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import arrow.core.getOrElse
import arrow.core.right
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import java.time.LocalTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

internal class OnePixPayDbRepositoryTest {
    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()
    private val enhancedClient = DynamoDBUtils.setupDynamoDB()
    private val onePixPayDynamoDAO = OnePixPayDynamoDAO(enhancedClient)
    private val onePixPayDbRepository = OnePixPayDbRepository(onePixPayDynamoDAO)

    private val createdOnePixPay = OnePixPay(
        walletId = wallet.id,
        billIds = listOf(BillId()),
        qrCode = "qrcode",
        status = OnePixPayStatus.CREATED,
        paymentLimitTime = LocalTime.NOON,
        fundsReceived = false,
    )

    @Test
    fun `deve salvar e ler um 1 pix pay`() {
        val bills = getNotScheduledBillsEnoughToNotifyOnePixPay(wallet)
        val billIds = bills.map { it.billId }

        val now = getZonedDateTime()
        val onePixPay = withGivenDateTime(now) {
            onePixPayDbRepository.save(
                OnePixPay.create(bills = bills) { _, _ -> "qrcode".right() }
                    .getOrElse { throw Exception("NÃO DEVERIA FALHAR AQUI") },
            )
        }

        onePixPayDbRepository.find(onePixPay.id) shouldBe OnePixPay(
            id = onePixPay.id,
            walletId = wallet.id,
            billIds = billIds,
            qrCode = "qrcode",
            status = OnePixPayStatus.CREATED,
            paymentLimitTime = LocalTime.of(14, 0, 0),
            fundsReceived = false,
            fundsReceivedAt = null,
            created = now,
        )
    }

    @Test
    fun `deve salvar no formato correspondente`() {
        val bills = getNotScheduledBillsEnoughToNotifyOnePixPay(wallet)
        val now = ZonedDateTime.parse("2022-06-01T15:04:56.482611Z", DateTimeFormatter.ISO_DATE_TIME)

        val onePixPay = withGivenDateTime(now) {
            onePixPayDbRepository.save(
                OnePixPay.create(bills = bills) { _, _ -> "qrcode".right() }
                    .getOrElse { throw Exception("NÃO DEVERIA FALHAR AQUI") },
            )
        }

        val onePixPayEntity = onePixPayDynamoDAO.findByPrimaryKey(onePixPay.id.value, "ONE_PIX_PAY")

        onePixPayEntity.shouldNotBeNull()

        with(onePixPayEntity) {
            created shouldBe "2022-06-01T15:04:56.482611Z"
            paymentLimitTime shouldBe "14:00"
            index2ScanKey shouldBe "2022-06-01#CREATED"
        }
    }

    @Test
    fun `deve encontrar todas as one pix pay por walletId`() {
        val requestedOnePixPay = createdOnePixPay.copy(id = UUIDOnePixPayId(), status = OnePixPayStatus.REQUESTED)
        val outdatedOnePixPay = requestedOnePixPay.copy(id = UUIDOnePixPayId(), created = getZonedDateTime().minusDays(1))
        onePixPayDbRepository.save(createdOnePixPay)
        onePixPayDbRepository.save(requestedOnePixPay)
        onePixPayDbRepository.save(outdatedOnePixPay)

        onePixPayDbRepository.findByWalletId(
            walletId = wallet.id,
            from = getLocalDate(),
            status = OnePixPayStatus.REQUESTED,
        ) shouldBe listOf(
            requestedOnePixPay,
        )
    }

    @Test
    fun `deve encontrar todas as one pix pay por data`() {
        val yesterday = getZonedDateTime().minusDays(1)

        val outdatedRequestedOnePixPay = createdOnePixPay.copy(
            id = UUIDOnePixPayId(),
            created = yesterday,
            status = OnePixPayStatus.REQUESTED,
        )
        val outdatedCreatedOnePixPay = outdatedRequestedOnePixPay.copy(id = UUIDOnePixPayId(), created = yesterday)

        onePixPayDbRepository.save(createdOnePixPay)
        onePixPayDbRepository.save(outdatedRequestedOnePixPay)
        onePixPayDbRepository.save(outdatedCreatedOnePixPay)

        onePixPayDbRepository.findAll(
            date = yesterday.toLocalDate(),
        ) shouldContainExactlyInAnyOrder listOf(outdatedRequestedOnePixPay, outdatedCreatedOnePixPay)
    }

    @Test
    fun `deve retornar uma excecao se nao achar nenhum 1 pix pay`() {
        assertThrows<ItemNotFoundException> { onePixPayDbRepository.find(onePixPayId = UUIDOnePixPayId()) }
    }
}