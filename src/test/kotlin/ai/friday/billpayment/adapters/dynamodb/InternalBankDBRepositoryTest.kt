package ai.friday.billpayment.adapters.dynamodb

import DynamoDBUtils
import ai.friday.billpayment.app.FindError
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.DefaultBankStatementItem
import ai.friday.billpayment.app.banking.InternalBankStatementItem
import ai.friday.billpayment.bankStatementItem
import ai.friday.billpayment.paymentMethodId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.dateTimeFormat
import arrow.core.left
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.shouldBe
import java.time.ZonedDateTime
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

internal class InternalBankDBRepositoryTest {

    private val dynamoDbEnhancedClient = DynamoDBUtils.setupDynamoDB()
    private val internalBankDynamoDAO = InternalBankDynamoDAO(dynamoDbEnhancedClient)
    private val internalBankRepository = InternalBankDBRepository(internalBankDynamoDAO)

    private val date = getLocalDate()
    private val operationNumber = "1000001"
    private val document = ""

    private val item = DefaultBankStatementItem(
        date = date,
        operationNumber = operationNumber,
        isTemporaryOperationNumber = false,
        documentNumber = document,
        counterpartName = "",
        counterpartDocument = document,
        description = "description",
        flow = BankStatementItemFlow.CREDIT,
        type = BankStatementItemType.TED_MESMA_TITULARIDADE,
        amount = 100L,
    )

    private val internalBankStatementItem = InternalBankStatementItem(
        bankStatementItem = item,
        accountPaymentMethodId = paymentMethodId,
    )

    @Test
    fun `não deve salvar novo StatementItem quando já existir um item com o mesmo operationNumber e paymentMethodId`() {
        internalBankRepository.create(internalBankStatementItem)

        val bankStatement = item.copy(date = date.plusDays(3))

        assertThrows<IllegalStateException> {
            internalBankRepository.create(internalBankStatementItem.copy(bankStatementItem = bankStatement))
        }

        internalBankRepository.findBankStatementItem(
            accountPaymentMethodId = paymentMethodId,
            date = date.plusDays(3),
            operationNumber,
        ) shouldBe FindError.NotFound.left()
    }

    @Test
    fun `findBankStatementItem deve ser inclusivo na data inicial e na data final`() {
        internalBankRepository.create(internalBankStatementItem)

        with(
            internalBankRepository.findAllBankStatementCredits(
                accountPaymentMethodId = paymentMethodId,
                startDate = date,
                endDate = date,
            ),
        ) {
            this.shouldNotBeEmpty()
            this.size shouldBe 1
        }
    }

    @Test
    fun `deve salvar um bankStatement com notificatedAt válido`() {
        val dateTime = ZonedDateTime.now()
        internalBankRepository.create(
            internalBankStatementItem.copy(
                bankStatementItem = bankStatementItem.copy(
                    notificatedAt = dateTime,
                ),
            ),
        )

        val bankStatement =
            internalBankRepository.findAllBankStatementItem(internalBankStatementItem.accountPaymentMethodId)

        bankStatement.items.first().notificatedAt!!.format(dateTimeFormat) shouldBe dateTime.format(
            dateTimeFormat,
        )
    }
}