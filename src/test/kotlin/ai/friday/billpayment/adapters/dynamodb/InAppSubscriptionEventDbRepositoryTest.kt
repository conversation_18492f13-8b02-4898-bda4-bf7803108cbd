package ai.friday.billpayment.adapters.dynamodb

import DynamoD<PERSON><PERSON>tils
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionEvent
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionEventType
import ai.friday.morning.date.brazilTimeZone
import java.time.Instant
import java.time.ZonedDateTime
import org.junit.jupiter.api.Test

class InAppSubscriptionEventDbRepositoryTest {
    private val dynamoDbEnhancedClient = DynamoDBUtils.setupDynamoDB()
    private val dynamoDAO = InAppSubscriptionEventDynamoDAO(dynamoDbEnhancedClient)

    private val inAppSubscriptionEventRepository = InAppSubscriptionEventDbRepository(client = dynamoDAO)

    @Test
    fun `deve conseguir criar um evento de assinatura`() {
        val inAppSubscriptionEvent = InAppSubscriptionEvent(
            accountId = AccountId(value = "account-id"),
            type = InAppSubscriptionEventType.INITIAL_PURCHASE,
            createdAt = ZonedDateTime.ofInstant(Instant.ofEpochMilli(*************), brazilTimeZone),
            payload = """
                '{
                    "event": {
                        "app_user_id": "ACCOUNT-ecafe3dc-eaca-42b2-8a50-f65d00bb5ea1",
                        "type": "TEST",
                        "store": "APP_STORE",
                        "expiration_at_ms": *************,
                        "event_timestamp_ms": *************
                    }
                }'
            """,
        )

        inAppSubscriptionEventRepository.save(inAppSubscriptionEvent)
    }
}