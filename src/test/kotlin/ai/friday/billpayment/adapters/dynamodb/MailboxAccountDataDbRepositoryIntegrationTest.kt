package ai.friday.billpayment.adapters.dynamodb

import DynamoD<PERSON><PERSON>tils.getDynamoDB
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.mailbox.MailboxListType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class MailboxAccountDataDbRepositoryIntegrationTest {

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbEnhancedClient = getDynamoDB()
    private val dynamoDbDAO = MailboxWalletDataDynamoDAO(dynamoDbEnhancedClient)

    private val walletId = WalletId("123")
    private val walletId2 = WalletId("1234")

    private val emailAdded = EmailAddress("<EMAIL>")
    private val emailAdded2 = EmailAddress("<EMAIL>")
    private val emailAdded3 = EmailAddress("<EMAIL>")

    private val repository = MailboxWalletDataDbRepository(
        client = dynamoDbDAO,
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @ParameterizedTest
    @EnumSource(MailboxListType::class)
    fun `deve salvar um email na lista corretamente`(type: MailboxListType) {
        repository.add(walletId, type, emailAdded)

        repository.has(walletId, type, emailAdded).shouldBeTrue()
        repository.has(walletId, type, emailAdded2).shouldBeFalse()
        repository.load(walletId, type).size shouldBe 1
    }

    @ParameterizedTest
    @EnumSource(MailboxListType::class)
    fun `deve salvar dois emails distintos na lista corretamente e não duplicar um save repetido`(type: MailboxListType) {
        repository.add(walletId, type, emailAdded)
        repository.add(walletId, type, emailAdded2)
        repository.add(walletId, type, emailAdded)

        repository.has(walletId, type, emailAdded).shouldBeTrue()
        repository.has(walletId, type, emailAdded2).shouldBeTrue()
        repository.load(walletId, type).size shouldBe 2
    }

    @ParameterizedTest
    @EnumSource(MailboxListType::class)
    fun `não deve encontrar emails salvos de outra carteira`(type: MailboxListType) {
        repository.add(walletId, type, emailAdded)
        repository.add(walletId, type, emailAdded2)
        repository.add(walletId2, type, emailAdded3)

        repository.has(walletId2, type, emailAdded).shouldBeFalse()
        repository.has(walletId2, type, emailAdded2).shouldBeFalse()
        repository.has(walletId2, type, emailAdded3).shouldBeTrue()
        repository.load(walletId2, type).size shouldBe 1
    }

    @ParameterizedTest
    @EnumSource(MailboxListType::class)
    fun `não deve encontrar emails salvos de outro tipo`(type: MailboxListType) {
        repository.add(walletId, type, emailAdded)
        repository.add(walletId, type, emailAdded2)

        MailboxListType.entries.forEach {
            if (it != type) {
                repository.has(walletId, it, emailAdded).shouldBeFalse()
                repository.has(walletId, it, emailAdded2).shouldBeFalse()
                repository.load(walletId, it).size shouldBe 0
            }
        }
    }

    @ParameterizedTest
    @EnumSource(MailboxListType::class)
    fun `deve funcionar e não alterar nada ao remover um item que não existe na base`(type: MailboxListType) {
        repository.add(walletId, type, emailAdded)
        repository.add(walletId, type, emailAdded2)
        repository.add(walletId2, type, emailAdded)
        repository.add(walletId2, type, emailAdded3)

        repository.remove(walletId, type, emailAdded3)

        repository.load(walletId, type).size shouldBe 2
        repository.load(walletId2, type).size shouldBe 2
        repository.has(walletId2, type, emailAdded3).shouldBeTrue()
    }
}