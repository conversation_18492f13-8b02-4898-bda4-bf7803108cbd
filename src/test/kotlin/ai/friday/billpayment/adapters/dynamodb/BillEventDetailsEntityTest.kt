package ai.friday.billpayment.adapters.dynamodb

import DynamoDBUtils
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billMarkedAsPaid
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.kotest.matchers.string.shouldNotContain
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class BillEventDetailsEntityTest {

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val dynamoDbEnhancedClient = DynamoDBUtils.setupDynamoDB()
    private val billEventDAO = BillEventDynamoDAO(dynamoDbEnhancedClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(dynamoDbEnhancedClient)
    private val transactionDynamo = TransactionDynamo(dynamoDbEnhancedClient)

    private val billEventDBRepository = BillEventDBRepository(
        billEventDAO = billEventDAO,
        uniqueConstraintDAO = uniqueConstraintDAO,
        featureConfiguration = mockk(),
        transactionDynamo = transactionDynamo,
    )

    @BeforeEach
    fun init() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
    }

    @Test
    fun `should save new event format of BillAddedEvent`() {
        val billEventDBRepository = BillEventDBRepository(
            billEventDAO = billEventDAO,
            uniqueConstraintDAO = uniqueConstraintDAO,
            featureConfiguration = allFalseFeatureConfiguration,
            transactionDynamo = transactionDynamo,
        )
        val billEvent = billAdded
        billEventDBRepository.save(billEvent)

        val billEventEntity = billEventDAO.findByPartitionKey(billAdded.billId.value).single()

        with(billEventEntity) {
            this.details shouldContain "walletId"
            this.details shouldContain """"@type":"BillAdded""""
            this.details shouldNotContain "@class"
            this.details shouldNotContain "ai.friday.billpayment.app.bill"
        }
    }

    @Test
    fun `should save new event format of BillMarkedAsPaidEntity`() {
        val billEventDBRepository = BillEventDBRepository(
            billEventDAO = billEventDAO,
            uniqueConstraintDAO = uniqueConstraintDAO,
            featureConfiguration = allFalseFeatureConfiguration,
            transactionDynamo = transactionDynamo,
        )
        val billEvent = billMarkedAsPaid

        billEventDBRepository.save(billEvent)

        val billEventEntity = billEventDAO.findByPartitionKey(billAdded.billId.value).single()

        with(billEventEntity) {
            this.details shouldContain "walletId"
            this.details shouldContain """"@type":"BillMarkedAsPaid""""
            this.details shouldNotContain "@class"
            this.details shouldNotContain "ai.friday.billpayment.app.bill"
        }
    }

    @Test
    fun `should read new event format of FichaCompensacaoAdded event`() {
        val currentBillId = BillId("BILL-4ece335c-d6ef-492e-8ef0-ba7ec71177ee")
        val walletId = WalletId("ACCOUNT-0b0a06a8-03ae-4d47-be53-79e4f1d49631")
        val event =
            "{\"@type\":\"FichaCompensacaoAdded\",\"billId\":{\"value\":\"${currentBillId.value}\"},\"created\":*************,\"walletId\":{\"value\":\"${walletId.value}\"},\"description\":\"\",\"dueDate\":\"2022-02-10\",\"amount\":546844,\"amountTotal\":546844,\"discount\":0,\"fine\":0,\"interest\":0,\"barcode\":{\"number\":\"07798000000000000000001101001305200500913319\",\"digitable\":\"07790001160100130520805009133199800000000000000\"},\"recipient\":{\"name\":\"BANCO INTER SA\",\"document\":\"**************\",\"alias\":\"\",\"bankAccount\":null,\"pixKeyDetails\":null},\"assignor\":\"BANCO INTER\",\"document\":\"***********\",\"payerName\":\"PAULO MAURICIO LEVY                     \",\"payerAlias\":null,\"paymentLimitTime\":\"20:00\",\"lastSettleDate\":\"2022-02-02\",\"expirationDate\":\"2030-04-29\",\"amountCalculationModel\":\"ANYONE\",\"actionSource\":{\"@type\":\"DDA\",\"accountId\":{\"value\":\"ACCOUNT-0b0a06a8-03ae-4d47-be53-79e4f1d49631\"}},\"effectiveDueDate\":\"2022-02-10\",\"fichaCompensacaoType\":\"CARTAO_DE_CREDITO\",\"eventType\":\"ADD\"}"

        val billEventEntity = BillEventEntity().apply {
            billId = currentBillId.value
            created = *************
            gSIndex1PrimaryKey = "07790001160100130520805009133199800000000000000"
            gSIndex1ScanKey = walletId.value
            eventType = BillEventType.ADD
            details = event
        }
        billEventDAO.save(billEventEntity)

        val response = billEventDBRepository.getBillById(currentBillId)

        response.isRight() shouldBe true
        with(response.getOrNull()!!) {
            billId shouldBe currentBillId
            this.walletId.value shouldBe walletId.value
        }
    }
}