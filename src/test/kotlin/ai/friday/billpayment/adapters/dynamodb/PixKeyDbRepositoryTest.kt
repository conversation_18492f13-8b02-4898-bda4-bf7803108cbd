package ai.friday.billpayment.adapters.dynamodb

import DynamoDB<PERSON>tils.setupDynamoDB
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyStatus
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.DOCUMENT_2
import ai.friday.billpayment.integration.WALLET_ID
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class PixKeyDbRepositoryTest {

    private lateinit var repository: PixKeyDbRepository

    private val walletId = WalletId(WALLET_ID)
    private val accountNumber = AccountNumber("*********")
    private val pixKey = PixKey(DOCUMENT, PixKeyType.CPF)
    private lateinit var dynamoDao: PixKeyDynamoDAO

    @BeforeEach
    fun beforeEach() {
        dynamoDao = PixKeyDynamoDAO(setupDynamoDB())
        repository = PixKeyDbRepository(dynamoDao)
        repository.create(walletId, accountNumber, pixKey)
    }

    @Test
    fun `deve salvar uma chave pix no banco`() {
        val pixKeys = dynamoDao.findByPartitionKey(walletId.value)

        pixKeys.size shouldBe 1

        val pixKey = pixKeys.first()

        pixKey.partitionKey shouldBe walletId.value
        pixKey.scanKey shouldBe "$PIX_KEY_REGISTER_NAME#${accountNumber.fullAccountNumber}#1"
        pixKey.value shouldBe DOCUMENT
        pixKey.type shouldBe PixKeyType.CPF
        pixKey.index1HashKey shouldBe DOCUMENT
        pixKey.index1RangeKey shouldBe PixKeyStatus.ACTIVE
        pixKey.created.shouldNotBeNull()
    }

    @Test
    fun `deve inativar uma chave pix sem excluir do banco`() {
        repository.delete(walletId, pixKey)

        val pixKeys = dynamoDao.findByPartitionKey(walletId.value)

        val pixKey = pixKeys.single()

        pixKey.index1RangeKey shouldBe PixKeyStatus.INACTIVE

        repository.list(walletId).size shouldBe 0
    }

    @Test
    fun `deve retornar apenas as chaves ativas na listagem`() {
        repository.create(walletId, accountNumber, PixKey(DOCUMENT_2, PixKeyType.CPF))
        repository.delete(walletId, PixKey(DOCUMENT_2, PixKeyType.CPF))
        val response = repository.list(walletId)

        val pixKeys = dynamoDao.findByPartitionKey(walletId.value)

        val isSamePixKey = pixKeys.any {
            it.value == response.single().value
        }

        isSamePixKey shouldBe true

        response.size shouldBe 1
    }
}