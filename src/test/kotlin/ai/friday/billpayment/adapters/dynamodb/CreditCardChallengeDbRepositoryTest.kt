package ai.friday.billpayment.adapters.dynamodb

import DynamoDBUtils
import ai.friday.billpayment.app.account.CreditCardChallengeStatus
import ai.friday.billpayment.buildCreditCardChallenge
import ai.friday.billpayment.paymentMethodId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

internal class CreditCardChallengeDbRepositoryTest {
    private val dynamoDbEnhancedClient = DynamoDBUtils.setupDynamoDB()

    private val creditCardChallengeRepository = CreditCardChallengeDbRepository(
        client = CreditCardChallengeDynamoDAO(dynamoDbEnhancedClient),
    )

    @Test
    fun `deve retornar a entidade mais recente`() {
        val now = getZonedDateTime()
        val challengeCreatedToday = buildCreditCardChallenge()
        val challengeCreatedYesterday = buildCreditCardChallenge(createdAt = now.minusDays(1), attempts = 5)
        creditCardChallengeRepository.save(challengeCreatedToday)
        creditCardChallengeRepository.save(challengeCreatedYesterday)

        val challenge = creditCardChallengeRepository.find(
            paymentMethodId = paymentMethodId,
            status = CreditCardChallengeStatus.ACTIVE,
        ).shouldNotBeNull()

        with(challenge) {
            createdAt shouldBe challengeCreatedToday.createdAt
            expiresAt shouldBe challengeCreatedToday.expiresAt
            attempts shouldBe challengeCreatedToday.attempts
        }
    }
}