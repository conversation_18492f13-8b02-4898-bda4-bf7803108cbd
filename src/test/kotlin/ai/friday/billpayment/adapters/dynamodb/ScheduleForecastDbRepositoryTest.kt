package ai.friday.billpayment.adapters.dynamodb

import DynamoDBUtils
import ai.friday.billpayment.app.bill.iso
import ai.friday.billpayment.app.integrations.ScheduleForecastResult
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import java.time.LocalDate
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test

class ScheduleForecastDbRepositoryTest {

    @Test
    fun `should save and find item`() {
        repository.find(dateWithZero).shouldBeTypeOf<ScheduleForecastResult.Item>().provisionedAmount shouldBe 0

        repository.save(
            ScheduleForecastEntity().apply {
                primaryKey = FORECAST_PREFIX
                scanKey = dateWithZero.iso()
                provisionedAmount = 1L
                paidAmount = 2L
                paidBills = 3L
                scheduledBills = 4L
            },
        )

        repository.find(dateWithoutRecord).shouldBeTypeOf<ScheduleForecastResult.NotFound>()
        repository.find(dateWithZero).shouldBeTypeOf<ScheduleForecastResult.Item>().run {
            provisionedAmount shouldBe 1L
            paidAmount shouldBe 2L
            paidBills shouldBe 3L
            scheduledBills shouldBe 4L
        }
    }

    @Test
    fun `should increment value by 100 when process a schedulement`() {
        val date = dateWithZero.plusDays(10)

        repository.find(date).shouldBeTypeOf<ScheduleForecastResult.NotFound>()

        repository.save(
            ScheduleForecastEntity().apply {
                primaryKey = FORECAST_PREFIX
                scanKey = date.iso()
                provisionedAmount = 1000L
                scheduledBills = 1L
                paidBills = 2L
                paidAmount = 200L
            },
        )

        repository.find(date).shouldBeTypeOf<ScheduleForecastResult.Item>().run {
            provisionedAmount shouldBe 1000
            scheduledBills shouldBe 1L
            paidBills shouldBe 2L
            paidAmount shouldBe 200L
        }

        repository.processSchedule(date, 100)

        repository.find(date).shouldBeTypeOf<ScheduleForecastResult.Item>().run {
            provisionedAmount shouldBe 1100
            scheduledBills shouldBe 2L
            paidBills shouldBe 2L
            paidAmount shouldBe 200L
        }
    }

    @Test
    fun `should increment value by 100 from absolute zero if there is no previous when process a schedulement`() {
        val date = dateWithZero.plusDays(12)

        repository.find(date).shouldBeTypeOf<ScheduleForecastResult.NotFound>()
        repository.processSchedule(date, 300)

        repository.find(date).shouldBeTypeOf<ScheduleForecastResult.Item>().run {
            provisionedAmount shouldBe 300
            scheduledBills shouldBe 1
            paidBills shouldBe 0
            paidAmount shouldBe 0
        }
    }

    @Test
    fun `should increment value by 100 from absolute zero if there is no previous record when process a payment`() {
        val date = dateWithZero.plusDays(30)

        repository.find(date).shouldBeTypeOf<ScheduleForecastResult.NotFound>()
        repository.processPayment(date, 300)

        repository.find(date).shouldBeTypeOf<ScheduleForecastResult.Item>().run {
            paidBills shouldBe 1
            paidAmount shouldBe 300
        }
    }

    @Test
    fun `should decrement value by 100 and keep amount paid and paid bills value when process a cancelment`() {
        val date = dateWithZero.plusDays(13)

        repository.find(date).shouldBeTypeOf<ScheduleForecastResult.NotFound>()

        repository.save(
            ScheduleForecastEntity().apply {
                primaryKey = FORECAST_PREFIX
                scanKey = date.iso()
                provisionedAmount = 1000L
                scheduledBills = 10
                paidBills = 1
                paidAmount = 200
            },
        )

        repository.find(date).shouldBeTypeOf<ScheduleForecastResult.Item>().run {
            provisionedAmount shouldBe 1000
            scheduledBills shouldBe 10
            paidBills shouldBe 1
        }

        repository.processCancelment(date, 100)

        repository.find(date).shouldBeTypeOf<ScheduleForecastResult.Item>().run {
            // updated values
            provisionedAmount shouldBe 900
            scheduledBills shouldBe 9

            // original values
            paidBills shouldBe 1
            paidAmount shouldBe 200
        }
    }

    @Test
    fun `should decrement value by 100 and increment amount paid when process payments`() {
        val date = dateWithZero.plusDays(16)

        repository.find(date).shouldBeTypeOf<ScheduleForecastResult.NotFound>()

        repository.save(
            ScheduleForecastEntity().apply {
                primaryKey = FORECAST_PREFIX
                scanKey = date.iso()
                provisionedAmount = 1000L
                scheduledBills = 10L
                paidBills = 1L
                paidAmount = 500L
            },
        )

        repository.find(date).shouldBeTypeOf<ScheduleForecastResult.Item>().run {
            provisionedAmount shouldBe 1000L
            scheduledBills shouldBe 10L
            paidBills shouldBe 1L
            paidAmount shouldBe 500
        }

        repository.processPayment(date, 100L)

        repository.find(date).shouldBeTypeOf<ScheduleForecastResult.Item>().run {
            provisionedAmount shouldBe 900
            scheduledBills shouldBe 9
            paidBills shouldBe 2
            paidAmount shouldBe 600
        }
    }

    @Test
    fun `should result a forecast item when there is a previous record`() {
        val date = dateWithZero.plusDays(14)

        repository.save(
            ScheduleForecastEntity().apply {
                primaryKey = FORECAST_PREFIX
                scanKey = date.iso()
                provisionedAmount = 8000L
            },
        )

        repository.find(date).shouldBeTypeOf<ScheduleForecastResult.Item>().provisionedAmount shouldBe 8000
    }

    @Test
    fun `should result NOT FOUND when there is no record`() {
        dateWithZero.plusDays(15).let { date ->
            repository.find(date).shouldBeTypeOf<ScheduleForecastResult.NotFound>()
        }
    }

    // setup

    private companion object {
        var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
        val dynamoDAO = DynamoDbDAO(dynamoDB)
        val enhancedClient = DynamoDBUtils.setupDynamoDB()
        val scheduleForecastDAO = ScheduleForecastDynamoDAO(enhancedClient)
        val repository = ScheduleForecastDbRepository(dynamoDAO, scheduleForecastDAO)

        val dateWithZero: LocalDate = LocalDate.of(2023, 11, 22)
        val dateWithoutRecord: LocalDate = dateWithZero.plusYears(50)

        @JvmStatic
        @BeforeAll
        fun beforeAll() {
            createBillPaymentTable(dynamoDB)

            repository.save(
                ScheduleForecastEntity().apply {
                    primaryKey = FORECAST_PREFIX
                    scanKey = dateWithZero.iso()
                    provisionedAmount = 0L
                    scheduledBills = 0L
                    paidBills = 0L
                },
            )
        }
    }
}