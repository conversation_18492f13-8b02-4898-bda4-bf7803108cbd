package ai.friday.billpayment.adapters.dynamodb

import DynamoD<PERSON><PERSON>tils.getDynamoDB
import ai.friday.billpayment.app.wallet.Invite
import ai.friday.billpayment.app.wallet.InviteStatus
import ai.friday.billpayment.buildInvite
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.equality.shouldBeEqualToComparingFields
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class WalletDbRepositoryTest {

    private var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val enhancedClient = getDynamoDB()

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletDbRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = mockk(),
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `deve trazer o convite mais recente`() {
        val expiredInvite = withGivenDateTime(getZonedDateTime().minusDays(1)) {
            buildInvite(inviteStatus = InviteStatus.EXPIRED)
        }
        val pendingInvite: Invite = buildInvite(inviteStatus = InviteStatus.PENDING)
        walletDbRepository.save(expiredInvite)
        walletDbRepository.save(pendingInvite)

        val expectedInvite = walletDbRepository.findInvite(
            walletId = pendingInvite.walletId,
            memberDocument = pendingInvite.memberDocument,
        )

        expectedInvite shouldBeEqualToComparingFields pendingInvite
    }

    @Test
    fun `quando o convite não existe deve devolver falso`() {
        walletDbRepository.isInviteReminderSent(
            buildInvite(inviteStatus = InviteStatus.EXPIRED),
        ).shouldBeFalse()
    }
}