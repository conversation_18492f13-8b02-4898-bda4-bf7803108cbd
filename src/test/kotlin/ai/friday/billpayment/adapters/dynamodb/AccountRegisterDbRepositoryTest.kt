package ai.friday.billpayment.adapters.dynamodb

import DynamoDB<PERSON>tils.getDynamoDB
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import io.kotest.matchers.shouldBe
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class AccountRegisterDbRepositoryTest {
    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbEnhancedClient = getDynamoDB()
    private val accountRegisterDAO = AccountRegisterDynamoDAO(dynamoDbEnhancedClient)
    private val originalOcrAndPersonDataDAO = OriginalOcrAndPersonDataDynamoDAO(dynamoDbEnhancedClient)
    private val accountRegisterDbRepository = AccountRegisterDbRepository(accountRegisterDAO, originalOcrAndPersonDataDAO, mockk())

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `deve conseguir criar e ler um usuário sem apelido`() {
        accountRegisterDbRepository.create(
            AccountId(ACCOUNT_ID),
            EmailAddress("<EMAIL>"),
            emailAddressVerified = false,
            username = "",
            registrationType = RegistrationType.BASIC,
        )

        val accountRegister = accountRegisterDbRepository.findByAccountId(AccountId(ACCOUNT_ID))

        accountRegister.nickname shouldBe ""
    }
}