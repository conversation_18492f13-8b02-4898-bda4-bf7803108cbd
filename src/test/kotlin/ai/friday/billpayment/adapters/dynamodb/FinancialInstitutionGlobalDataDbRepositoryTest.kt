package ai.friday.billpayment.adapters.dynamodb

import DynamoDBUtils
import ai.friday.billpayment.FinancialInstitutionFixture.Companion.BB
import ai.friday.billpayment.FinancialInstitutionFixture.Companion.BRADESCO
import ai.friday.billpayment.FinancialInstitutionFixture.Companion.ITAU
import ai.friday.billpayment.app.FinancialIdentifier
import ai.friday.billpayment.app.FinancialInstitutionGlobalData
import ai.friday.billpayment.app.FinancialInstitutionGlobalData.Companion.getInstitutionByCode
import ai.friday.billpayment.app.banking.Bank
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.banking.getDescription
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test

internal class FinancialInstitutionGlobalDataDbRepositoryTest {

    private val enhancedClient = DynamoDBUtils.setupDynamoDB()
    private val globalDataDAO = GlobalDataDynamoDAO(enhancedClient)

    val repository = FinancialInstitutionGlobalDataDbRepository(GlobalDataDbRepository(globalDataDAO))

    @AfterEach
    fun cleanData() {
        FinancialInstitutionGlobalData.cleanData()
    }

    @Test
    fun `should save and load holidays`() {
        val now = getLocalDate()
        val tomorrow = now.plusDays(1)

        val originalHolidays = listOf(now, tomorrow)
        repository.saveHolidays(originalHolidays)

        val holidays = repository.loadHolidays()

        holidays shouldContainExactly originalHolidays
    }

    @Test
    fun `should save and load pix participants`() {
        val originalParticipants = listOf(
            FinancialInstitution(name = "FI1", ispb = "**************", compe = 1111),
            FinancialInstitution(name = "FI2", ispb = "**************", compe = 1112),
            FinancialInstitution(name = "FI3", ispb = "**************", compe = 1113),
        )
        repository.savePixParticipants(originalParticipants)

        val participants = repository.loadPixParticipants()

        participants shouldContainExactly originalParticipants
    }

    @Test
    fun `should save and load bank list`() {
        val originalBankList = listOf(
            Bank(number = 1, name = "BANK A"),
            Bank(number = 2, name = "BANK B"),
            Bank(number = 3, name = "BANK C"),
        )

        repository.saveBankList(originalBankList)

        val bankList = repository.loadBankList()

        bankList shouldContainExactly originalBankList

        bankList.first().getDescription() shouldBe "1 - BANK A"
    }

    @Test
    fun `should save and recover by code financial institutions and banks`() {
        repository.saveHolidays(listOf(getLocalDate()))

        val institutions = listOf(ITAU, BRADESCO, BB)
        repository.savePixParticipants(institutions)
        repository.saveBankList(institutions.mapNotNull { it.toBank() })

        val globalData = FinancialInstitutionGlobalData(repository)
        globalData.loadData()

        val namesByCOMPE = mutableListOf<String>()
        val namesByISPB = mutableListOf<String>()

        institutions.forEach {
            namesByCOMPE.add(getInstitutionByCode(FinancialIdentifier.COMPE(it.compe!!)).shouldNotBeNull())
            namesByISPB.add(getInstitutionByCode(FinancialIdentifier.ISPB(it.ispb!!.toLong())).shouldNotBeNull())
        }
        institutions.map { it.name }.also { names ->
            namesByCOMPE shouldContainExactlyInAnyOrder names
            namesByISPB shouldContainExactlyInAnyOrder names
        }
    }
}