package ai.friday.billpayment.adapters.dynamodb

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardBrand
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.creditCard
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.DynamoDBUtils as IntegrationDynamoDBUtils
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class AccountDbRepositoryTest {

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val enhancedClient = getDynamoDB()

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountDbRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val walletFixture = WalletFixture()

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `should create NSU register when there is no NSU register neither BillCount`() {
        loadAccountIntoDb(amazonDynamoDB = dynamoDB, billCount = null)
        accountDbRepository.incrementNSU(AccountId(ACCOUNT_ID)) shouldBe 1

        val item = IntegrationDynamoDBUtils.getItem(dynamoDB, DYNAMODB_TABLE_NAME, ACCOUNT_ID, "NSU")
        item shouldNotBe null
        item.getInt("NSU") shouldBe 1
    }

    @Test
    fun `should increment NSU when register exists`() {
        val originalNsu = 2
        loadAccountIntoDb(amazonDynamoDB = dynamoDB, billCount = null, nsu = originalNsu)
        accountDbRepository.incrementNSU(AccountId(ACCOUNT_ID)) shouldBe originalNsu + 1

        val item = IntegrationDynamoDBUtils.getItem(dynamoDB, DYNAMODB_TABLE_NAME, ACCOUNT_ID, "NSU")
        item shouldNotBe null
        item.getInt("NSU") shouldBe originalNsu + 1
    }

    @Test
    fun `should create NSU register when there is BillCount but no NSU`() {
        val count = 15
        loadAccountIntoDb(amazonDynamoDB = dynamoDB, billCount = null)
        repeat(count) {
            nsuDAO.update(
                NSUEntity().apply {
                    partitionKey = ACCOUNT_ID
                    sortKey = "NSU"
                    nsu = count
                },
            )
        }
        accountDbRepository.incrementNSU(AccountId(ACCOUNT_ID)) shouldBe count + 1

        val item = IntegrationDynamoDBUtils.getItem(dynamoDB, DYNAMODB_TABLE_NAME, ACCOUNT_ID, "NSU")
        item shouldNotBe null
        item.getInt("NSU") shouldBe count + 1
    }

    @Test
    fun `should create partial account`() {
        val name = "Name Fake"
        val email = "<EMAIL>"
        val now = getZonedDateTime()

        val partialAccount = withGivenDateTime(now) {
            accountDbRepository.create(name, EmailAddress(email), registrationType = RegistrationType.BASIC)
        }

        with(partialAccount) {
            status shouldBe AccountStatus.REGISTER_INCOMPLETE
            statusUpdated shouldBe now
            emailAddress.value shouldBe email
            this.name shouldBe name
        }
    }

    @Test
    fun `should create partial account and update it status`() {
        val name = "Name Fake"
        val email = "<EMAIL>"
        val now = getZonedDateTime().plusDays(1)
        val partialAccount =
            accountDbRepository.create(name, EmailAddress(email), registrationType = RegistrationType.BASIC)

        withGivenDateTime(now) {
            accountDbRepository.updatePartialAccountStatus(partialAccount.id, AccountStatus.APPROVED)
        }

        with(accountDbRepository.findPartialAccountById(partialAccount.id)) {
            status shouldBe AccountStatus.APPROVED
            statusUpdated shouldBe now
            emailAddress.value shouldBe email
            this.name shouldBe name
        }
    }

    @Test
    fun `deve atualizar o account status corretamente`() {
        val name = "Name Fake"
        val email = "<EMAIL>"

        val account =
            accountDbRepository.create(ACCOUNT.copy(name = name, emailAddress = EmailAddress(email), status = AccountStatus.UNDER_EXTERNAL_REVIEW))

        accountDbRepository.updateAccountStatus(account.accountId, AccountStatus.ACTIVE)

        with(accountDbRepository.findById(account.accountId)) {
            status shouldBe AccountStatus.ACTIVE
            emailAddress.value shouldBe email
            this.name shouldBe name
        }
    }

    @Test
    fun `should update partial account groups`() {
        val name = "Name Fake"
        val email = "<EMAIL>"
        val now = getZonedDateTime().plusDays(1)
        val partialAccount =
            accountDbRepository.create(name, EmailAddress(email), registrationType = RegistrationType.BASIC)

        withGivenDateTime(now) {
            accountDbRepository.updatePartialAccountGroups(
                partialAccount.id,
                listOf(AccountGroup.ALPHA, AccountGroup.BETA),
            )
        }

        with(accountDbRepository.findPartialAccountById(partialAccount.id)) {
            status shouldBe AccountStatus.REGISTER_INCOMPLETE
            statusUpdated shouldBe partialAccount.statusUpdated
            emailAddress.value shouldBe email
            this.name shouldBe name
            groups shouldContainExactlyInAnyOrder listOf(AccountGroup.ALPHA, AccountGroup.BETA)
        }
    }

    @Test
    fun `should create and find partial account by its status `() {
        val name = "Name Fake"
        val email = "<EMAIL>"

        val partialAccount =
            accountDbRepository.create(name, EmailAddress(email), registrationType = RegistrationType.BASIC)
        accountDbRepository.updatePartialAccountStatus(partialAccount.id, AccountStatus.UNDER_EXTERNAL_REVIEW)

        accountDbRepository.findPartialAccountByStatus(AccountStatus.REGISTER_INCOMPLETE).shouldBeEmpty()

        val partialAccounts = accountDbRepository.findPartialAccountByStatus(AccountStatus.UNDER_EXTERNAL_REVIEW)
        partialAccounts shouldHaveSize 1

        with(partialAccounts.single()) {
            status shouldBe AccountStatus.UNDER_EXTERNAL_REVIEW
            email shouldBe email
            name shouldBe name
        }
    }

    @Test
    fun `deve conseguir criar e ler um usuário sem nome`() {
        val accountRegister = accountDbRepository.create(
            "",
            EmailAddress("<EMAIL>"),
            registrationType = RegistrationType.BASIC,
        )

        val partialAccounts = accountDbRepository.findPartialAccountById(accountRegister.id)

        partialAccounts.name shouldBe ""
    }

    @Test
    fun `deve conseguir salvar o usuário corretamente`() {
        val account = ACCOUNT.copy(firstLoginAsOwner = getZonedDateTime())

        accountDbRepository.save(account)

        val databaseAccount = accountDbRepository.findById(account.accountId)
        databaseAccount shouldBe account
    }

    @Test
    fun `deve atualizar o risco do cartão de crédito`() {
        val method = CreditCard(
            brand = CreditCardBrand.VISA,
            pan = "1234",
            expiryDate = "10/23",
            securityCode = null,
            holderName = null,
            token = null,
            riskLevel = RiskLevel.MEDIUM,
            binDetails = null,
        )

        val mediumRiskCreditCard = creditCard.copy(
            id = AccountPaymentMethodId("MEDIUM_RISK"),
            method = method,
        )

        accountDbRepository.createAccountPaymentMethod(
            accountId = walletFixture.assistantAccount.accountId,
            paymentMethodId = mediumRiskCreditCard.id,
            creditCard = mediumRiskCreditCard.method as CreditCard,
            position = 1,
            status = mediumRiskCreditCard.status,
        )

        accountDbRepository.updateCreditCardPaymentMethodRisk(
            accountId = walletFixture.assistantAccount.accountId,
            accountPaymentMethodId = mediumRiskCreditCard.id,
            riskLevel = RiskLevel.LOW,
        )

        val findPaymentMethod = accountDbRepository.findAccountPaymentMethodsByAccountId(
            accountId = walletFixture.assistantAccount.accountId,
        ).first {
            it.id == mediumRiskCreditCard.id && it.method.type == PaymentMethodType.CREDIT_CARD
        }.method as CreditCard

        findPaymentMethod.riskLevel shouldBe RiskLevel.LOW
        findPaymentMethod.token shouldBe method.token
    }
}