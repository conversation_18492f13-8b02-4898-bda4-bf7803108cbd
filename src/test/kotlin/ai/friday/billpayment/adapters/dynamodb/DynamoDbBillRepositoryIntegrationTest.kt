package ai.friday.billpayment.adapters.dynamodb

import DynamoDBUtils
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.AutomaticPixData
import ai.friday.billpayment.app.bill.AutomaticPixPayer
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.integrations.FindBillsCriteria
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.BILL_ID_5
import ai.friday.billpayment.integration.BILL_ID_6
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.invoicePaid
import ai.friday.billpayment.invoicePaymentScheduled
import ai.friday.billpayment.invoicePaymentStarted
import ai.friday.billpayment.invoiceScheduleStarted
import ai.friday.billpayment.pixAdded
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.shouldBe
import java.time.LocalDate
import java.time.LocalTime
import java.time.YearMonth
import org.junit.jupiter.api.Test

class DynamoDbBillRepositoryIntegrationTest {
    private val enhancedClient = DynamoDBUtils.setupDynamoDB()

    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(enhancedClient),
        refundedClient = RefundedBillDynamoDAO(enhancedClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(enhancedClient),
    )

    @Test
    fun `nao deve retornar uma conta processando se o tempo for menor do que o informado`() {
        val now = getZonedDateTime()

        val bill = Bill.build(
            invoiceAdded,
            invoicePaymentScheduled,
            invoicePaymentStarted.copy(
                created = now.minusSeconds(59).toInstant().toEpochMilli(),
            ),
        )
        billRepository.save(bill)

        withGivenDateTime(now) {
            val bills = billRepository.getStuckedProcessingBills(60)
            bills.size shouldBe 0
        }
    }

    @Test
    fun `deve retornar uma conta processando se o tempo for maior do que o informado`() {
        val now = getZonedDateTime()

        val bill = Bill.build(
            invoiceAdded,
            invoicePaymentScheduled,
            invoicePaymentStarted.copy(
                created = now.minusSeconds(60).toInstant().toEpochMilli(),
            ),
        )
        billRepository.save(bill)

        withGivenDateTime(now) {
            val bills = billRepository.getStuckedProcessingBills(60)
            bills.size shouldBe 1
            bills.single().billId shouldBe invoiceAdded.billId
        }
    }

    @Test
    fun `deve retornar o total das contas pagas filtradas pelo actionSource`() {
        val bill = Bill.build(
            invoiceAdded,
            invoicePaymentScheduled.copy(actionSource = ActionSource.VirtualAssistant(ACCOUNT.accountId, transactionId = "TRANSACTION-1")),
            invoiceScheduleStarted,
            invoicePaymentStarted,
            invoicePaid,
        )
        billRepository.save(bill)

        val bill2 = Bill.build(
            invoiceAdded.copy(billId = BillId(BILL_ID_6)),
            invoicePaymentScheduled.copy(billId = BillId(BILL_ID_6), actionSource = ActionSource.VirtualAssistant(ACCOUNT.accountId, transactionId = "TRANSACTION-2")),
            invoiceScheduleStarted.copy(billId = BillId(BILL_ID_6)),
            invoicePaymentStarted.copy(billId = BillId(BILL_ID_6)),
            invoicePaid.copy(billId = BillId(BILL_ID_6)),
        )
        billRepository.save(bill2)

        val bill3 = Bill.build(
            invoiceAdded.copy(billId = BillId(BILL_ID_5)),
            invoicePaymentScheduled.copy(billId = BillId(BILL_ID_5)),
            invoiceScheduleStarted.copy(billId = BillId(BILL_ID_5)),
            invoicePaymentStarted.copy(billId = BillId(BILL_ID_5)),
            invoicePaid.copy(billId = BillId(BILL_ID_5)),
        )
        billRepository.save(bill3)

        billRepository.getTotalPaidBillsByScheduleSourceType(
            WalletId(WALLET_ID),
            startDate = getLocalDate().atStartOfDay(brazilTimeZone),
            endDate = getLocalDate().atTime(LocalTime.MAX).atZone(brazilTimeZone),
            actionSource = ActionSource.VirtualAssistant(ACCOUNT.accountId),
        ) shouldBe 31182L
    }

    @Test
    fun `deve retornar contas ativas e aguardando aprovação na lista de billscomingdue`() {
        val today = getLocalDate()
        val bill = Bill.build(invoiceAdded.copy(dueDate = today, effectiveDueDate = today, created = getZonedDateTime().minusSeconds(3).toInstant().toEpochMilli()))
        billRepository.save(bill)

        val bill2 = Bill.build(invoiceAdded.copy(billId = BillId(), dueDate = today, effectiveDueDate = today, created = getZonedDateTime().minusSeconds(2).toInstant().toEpochMilli(), securityValidationResult = listOf("FAKE_VALIDATION")))
        billRepository.save(bill2)

        val result = billRepository.findBillsComingDue(invoiceAdded.walletId)
        result.size shouldBe 2
        result.single { it.billId == bill.billId }.status shouldBe BillStatus.ACTIVE
        result.single { it.billId == bill2.billId }.status shouldBe BillStatus.WAITING_APPROVAL
    }

    @Test
    fun `deve retornar contas a partir de um mês-ano`() {
        val thisMonth = LocalDate.of(2025, 4, 29)
        val nextMonth = LocalDate.of(2025, 5, 1)
        val walletId = invoiceAdded.walletId

        val thisMonthBill = Bill.build(invoiceAdded.copy(dueDate = thisMonth, effectiveDueDate = thisMonth))
        billRepository.save(thisMonthBill)

        val nextMonthBill = Bill.build(invoiceAdded.copy(billId = BillId(), dueDate = nextMonth, effectiveDueDate = nextMonth))
        billRepository.save(nextMonthBill)

        val bills = billRepository.findByWallet(walletId, FindBillsCriteria(from = YearMonth.of(nextMonth.year, nextMonth.month)))

        bills.size shouldBe 1
        bills[0].billId shouldBe nextMonthBill.billId
    }

    @Test
    fun `deve retornar contas antes de um mês-ano`() {
        val thisMonth = LocalDate.of(2025, 4, 29)
        val nextMonth = LocalDate.of(2025, 5, 1)
        val walletId = invoiceAdded.walletId

        val thisMonthBill = Bill.build(invoiceAdded.copy(dueDate = thisMonth, effectiveDueDate = thisMonth))
        billRepository.save(thisMonthBill)

        val nextMonthBill = Bill.build(invoiceAdded.copy(billId = BillId(), dueDate = nextMonth, effectiveDueDate = nextMonth))
        billRepository.save(nextMonthBill)

        val bills = billRepository.findByWallet(walletId, FindBillsCriteria(to = YearMonth.of(thisMonth.year, thisMonth.month)))

        bills.size shouldBe 1
        bills[0].billId shouldBe thisMonthBill.billId
    }

    @Test
    fun `deve retornar contas dentro de um intervalo de mês-ano`() {
        val previousMonth = LocalDate.of(2025, 3, 31)
        val thisMonth = LocalDate.of(2025, 4, 1)
        val nextMonth = LocalDate.of(2025, 5, 31)
        val walletId = invoiceAdded.walletId

        val previousMonthBill = Bill.build(invoiceAdded.copy(dueDate = previousMonth, effectiveDueDate = previousMonth))
        billRepository.save(previousMonthBill)

        val thisMonthBill = Bill.build(invoiceAdded.copy(billId = BillId(), dueDate = thisMonth, effectiveDueDate = thisMonth))
        billRepository.save(thisMonthBill)

        val nextMonthBill = Bill.build(invoiceAdded.copy(billId = BillId(), dueDate = nextMonth, effectiveDueDate = nextMonth))
        billRepository.save(nextMonthBill)

        val bills = billRepository.findByWallet(
            walletId,
            FindBillsCriteria(
                from = YearMonth.of(thisMonth.year, thisMonth.month),
                to = YearMonth.of(nextMonth.year, nextMonth.month),
            ),
        )

        bills.size shouldBe 2
        bills[0].billId shouldBe thisMonthBill.billId
        bills[1].billId shouldBe nextMonthBill.billId
    }

    @Test
    fun `deve salvar e ler uma bill com dados de pix automatico`() {
        // Cria dados de PIX automático
        val automaticPixData = AutomaticPixData(
            payer = AutomaticPixPayer(
                document = "98765432100",
                name = "Pagador Pix Automático",
            ),
            description = "Pagamento recorrente PIX automático",
            additionalInformation = "Informação adicional PIX automático",
            contractNumber = "contrato-PIX-123",
        )

        val bill = Bill.build(
            pixAdded.copy(
                billType = BillType.AUTOMATIC_PIX,
                automaticPixData = automaticPixData,
                description = "Conta com PIX automático",
                actionSource = ActionSource.AutomaticPix(
                    accountId = AccountId(ACCOUNT_ID),
                    endToEnd = "endToEnd",
                    recurringPaymentId = "recurringPaymentId",
                ),
            ),
        )

        billRepository.save(bill)

        val foundBill = billRepository.findBill(bill.billId, bill.walletId)

        foundBill.billType shouldBe BillType.AUTOMATIC_PIX
        foundBill.automaticPixData shouldBe automaticPixData
    }
}