package ai.friday.billpayment.adapters.dynamodb

import DynamoDBUtils
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.forecast.ForecastPeriod
import ai.friday.billpayment.app.itp.PaymentIntent
import ai.friday.billpayment.app.itp.PaymentIntentDetails
import ai.friday.billpayment.app.itp.PaymentIntentStatus
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.WALLET_ID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

internal class PaymentIntentDbRepositoryTest {

    private val dynamoDbEnhancedClient = DynamoDBUtils.setupDynamoDB()

    private val paymentIntentRepository = PaymentIntentDbRepository(
        client = PaymentIntentDynamoDAO(dynamoDbEnhancedClient),
    ).apply {
        defaultPixKeyDomain = "@fake.ai"
    }

    private val paymentIntent = PaymentIntent(
        accountId = AccountId(ACCOUNT_ID),
        walletId = WalletId(WALLET_ID),
        document = DOCUMENT,
        authorizationServerId = "AUTHORIZATION_ID",
        authorizationServerName = "AUTHORIZATION_NAME",
        routingNo = 123,
        accountNo = 321,
        accountDv = "",
        bankISPB = "1234",
        accountType = AccountType.CHECKING,
        bankNo = null,
        forecastPeriod = ForecastPeriod.SEVEN_DAYS,
        includeScheduledBillsOnly = false,
        details = PaymentIntentDetails.WithPixKey(pixKey = PixKey(value = "$<EMAIL>", type = PixKeyType.EMAIL), amount = 1),
        status = PaymentIntentStatus.CREATED,
    )

    @Test
    fun `deve salvar e buscar o item corretamente quando for pagamento com PixKey`() {
        val savedItem = paymentIntentRepository.save(paymentIntent)

        val readPaymentIntent = paymentIntentRepository.find(savedItem.paymentIntentId)

        readPaymentIntent shouldBe paymentIntent
    }

    @Test
    fun `deve salvar e buscar o item corretamente quando for pagamento com QRCode`() {
        val paymentIntent = paymentIntent.copy(details = PaymentIntentDetails.WithQRCode(qrCode = "MEU_CODIGO_QR"))

        val savedItem = paymentIntentRepository.save(paymentIntent)

        val readPaymentIntent = paymentIntentRepository.find(savedItem.paymentIntentId)

        readPaymentIntent shouldBe paymentIntent
    }

    @Test
    fun `deve atualizar o status do pagamento`() {
        val savedItem = paymentIntentRepository.save(paymentIntent)

        paymentIntentRepository.save(savedItem.copy(status = PaymentIntentStatus.ONGOING))

        val readPaymentIntent = paymentIntentRepository.find(savedItem.paymentIntentId)

        readPaymentIntent shouldBe paymentIntent.copy(status = PaymentIntentStatus.ONGOING)
    }
}