/*
package ai.friday.billpayment.adapters.cognito

import ai.friday.billpayment.adapters.auth.FROM_CLAIM_NAME
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.integration.ACCOUNT_ID
import io.kotest.matchers.shouldBe
import java.nio.charset.StandardCharsets
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import software.amazon.awssdk.services.cognitoidentityprovider.CognitoIdentityProviderClient
import software.amazon.awssdk.services.cognitoidentityprovider.model.AdminGetUserRequest
import software.amazon.awssdk.services.cognitoidentityprovider.model.AdminListGroupsForUserRequest
import software.amazon.awssdk.services.cognitoidentityprovider.model.AdminResetUserPasswordRequest
import software.amazon.awssdk.services.cognitoidentityprovider.model.AdminSetUserPasswordRequest
import software.amazon.awssdk.services.cognitoidentityprovider.model.AdminUpdateUserAttributesRequest
import software.amazon.awssdk.services.cognitoidentityprovider.model.AttributeType
import software.amazon.awssdk.services.cognitoidentityprovider.model.AuthFlowType
import software.amazon.awssdk.services.cognitoidentityprovider.model.ConfirmForgotPasswordRequest
import software.amazon.awssdk.services.cognitoidentityprovider.model.InitiateAuthRequest
import software.amazon.awssdk.services.cognitoidentityprovider.model.InvalidPasswordException
import software.amazon.awssdk.services.cognitoidentityprovider.model.RespondToAuthChallengeRequest

@Disabled
class CognitoAdapterIntegrationTest {

    val userPoolId = "us-east-1_S0DGZEW5J"
    val userPoolClientId = "3og5ib4dn9av4vdv3u8rqthvtd"

    private val configuration = CognitoConfiguration(userPoolId)

    val cognitoAdapter = CognitoAdapter(configuration)

    @Test
    fun `should configure user without accountId attribute`() {
        cognitoAdapter.configureUser(username = "testelink", accountId = AccountId(ACCOUNT_ID), groupNames = listOf("OWNER"))
    }

    @Test
    fun `should not create user with invalid password`() {
        val thrown = assertThrows<InvalidPasswordException> {
            cognitoAdapter.createUser(
                username = "***********",
                password = "aA34567",
                accountId = AccountId("ACCOUNT-FAKE"),
                emailAddress = EmailAddress("<EMAIL>"),
                phoneNumber = MobilePhone("+*************"),
                role = Role.OWNER,
                name = "teste",
            )
        }

        thrown.awsErrorDetails().errorCode() shouldBe "InvalidPasswordException"
    }

    val username = "***********"

    @Test
    fun `should create user with permanent password`() {
        cognitoAdapter.createUser(
            username = username,
            password = "aA!45678",
            accountId = AccountId("ACCOUNT-16c17754-a07d-4047-937d-4f5d72c5dde8"),
            emailAddress = EmailAddress("<EMAIL>"),
            phoneNumber = MobilePhone("+*************"),
            role = Role.OWNER,
            name = "Alexandre",
        )
    }

    @Test
    fun `should signout user`() {
        cognitoAdapter.signOutUser("testecrypto")
    }

    @Test
    fun setUserPassword() {
        cognitoAdapter.setUserPassword("testecrypto", "Temporary1!")
    }

    @Test
    fun foo() {
        val baseString = "valueToEncode"
        val keyString = "gUkXp2s5v8y/B?E("
        val key = SecretKeySpec(keyString.toByteArray(StandardCharsets.UTF_8), "HmacSHA256")
        val mac = Mac.getInstance("HmacSHA256")
        mac.init(key)
        val bytes = mac.doFinal(baseString.toByteArray(StandardCharsets.UTF_8))
        println(bytes)
    }

    @Test
    fun login() {
        val client = CognitoIdentityProviderClient.builder().build()

        val password = "a9182633"
        val userName = username
        val loginRequest = InitiateAuthRequest.builder()
            .clientId(userPoolClientId)
            .authFlow(AuthFlowType.USER_PASSWORD_AUTH)
            .authParameters(mapOf("USERNAME" to userName, "PASSWORD" to password)).build()
        val response = client.initiateAuth(loginRequest)
        println(response.authenticationResult())
        println("session: ${response.session()}")
        println("challengeName: ${response.challengeName()}")
    }

    @Test
    fun authChallenge() {
        val client = CognitoIdentityProviderClient.builder().build()

        val userName = username
        val session =
            "AYABeNlERienbCDO6BsS67WhxU0AHQABAAdTZXJ2aWNlABBDb2duaXRvVXNlclBvb2xzAAEAB2F3cy1rbXMAS2Fybjphd3M6a21zOnVzLWVhc3QtMTo3NDU2MjM0Njc1NTU6a2V5L2IxNTVhZmNhLWJmMjktNGVlZC1hZmQ4LWE5ZTA5MzY1M2RiZQC4AQIBAHiAcAt7Ei832QLLvv5tnR-fAKEzaf-OMDg-j1aLh6qMVAHv03WP7kaIwZqp2VrPA_O2AAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMwdXeyqcf2rxpfb3cAgEQgDtxTbiMShi3F9Z4ym2S8fVsM-VS3R99Uzmm38SR5_IqaZcV-b5qVESeZ9_DnETIQlobMm1b1xURLBiByQIAAAAADAAAEAAAAAAAAAAAAAAAAAB93VMxdo0pKzxUp6wvu_eA_____wAAAAEAAAAAAAAAAAAAAAEAAAHCoAWk_0HWHme80SaaF7bAQvxjdPkGyNXAGphXU5QtIov0kHHT6tacuqxpF5ELW_jFs2K6sU0QnaCQRl1B_Lxs2lU9y4agXsjiJsyoNEbbsiJhXH0oV43YU9HYpoqZK0kQzdt7wwPV3tiE-lZPqS8KV8UgmL6fcVe3kyzMA3DdF_rwbYHiuCVZk-xrob9DnxmoOje68hvg8s6fN3wxxTeTaB2pJWV9RLZNYiY-Lidrsz7FstDBvIQkh7ZSgeXXZCqPFXzohsTLaLnIvQCu5ssmYD9Cez63v2KGBhPXs3HuQmeorCHDtAwlPyZ8azGYLIpHPQgTlqdfEXQXGwBVTbTXArHG7G2QjrEwZNLCB7j0e6tqbmSRRAQfoRG6cIBlKO9Ebu6jOL2qrdvkBZTUTTVjg_OgJawDmk1shfwiCskWpIFtFtfKJStRJ917xlz13JNAt52NNwS1hUHNZN3OfxqwHI04wAnWc0y79c_IQpIKsgNpPSGQ3HDIURQbAHr7JnNtVtsJzlqCvlN-iJF9IBk92HI5wAcptlJXRFPBa2CSVviFueenuBEmFR8v_lwpqWaJiegri-Vz-N1Rr-HcKeY107VC-Za9kd0zNpeo8EBsk5maUA"
        val mfaCode = "887027"
        val request = RespondToAuthChallengeRequest.builder()
            .challengeName("SMS_MFA")
            .session(session)
            .clientId(userPoolClientId)
            .challengeResponses(mapOf("SMS_MFA_CODE" to mfaCode, "USERNAME" to userName)).build()

        val response = client.respondToAuthChallenge(request)
        println(response)
        println("accessToken: ${response.authenticationResult().accessToken()}")
        println("idToken: ${response.authenticationResult().idToken()}")
    }

    @Test
    fun changeUserPassword() {
        val client = CognitoIdentityProviderClient.builder().build()

        val setRequest = AdminSetUserPasswordRequest.builder().username("testecrypto")
            .userPoolId(userPoolId)
            .password("Temporary1!")
            .permanent(true).build()

        val response = client.adminSetUserPassword(setRequest)
        println(response)
    }

    @Test
    fun listGroupsForUser() {
        val client = CognitoIdentityProviderClient.builder().build()

        val request = AdminListGroupsForUserRequest.builder()
            .username("testecrypto")
            .userPoolId(userPoolId).build()

        val response = client.adminListGroupsForUser(request)
        println(response)
    }

    @Test
    fun getUser() {
        val client = CognitoIdentityProviderClient.builder().build()

        val request = AdminGetUserRequest.builder()
            .username(username)
            .userPoolId(userPoolId).build()
        val response = client.adminGetUser(request)
        println(response)
    }

    @Test
    fun resetPassword() {
        val client = CognitoIdentityProviderClient.builder().build()

        val request = AdminResetUserPasswordRequest.builder()
            .username(username)
            .userPoolId(userPoolId).build()
        val response = client.adminResetUserPassword(request)
        println(response)
    }

    @Test
    fun setUserAttribute() {
        val client = CognitoIdentityProviderClient.builder().build()

        val request = AdminUpdateUserAttributesRequest.builder()
            .username("testecrypto")
            .userPoolId(userPoolId)
            .userAttributes(
                AttributeType.builder()
                    .name(FROM_CLAIM_NAME)
                    .value("meunome").build(),
            ).build()
        client.adminUpdateUserAttributes(request)
    }

    @Test
    fun confirmForgotPassword() {
        val client = CognitoIdentityProviderClient.builder().build()

        val request = ConfirmForgotPasswordRequest.builder()
            .username(username)
            .clientId(userPoolClientId)
            .confirmationCode("235771")
            .password("a9182633").build()
        client.confirmForgotPassword(request)
    }

    @Test
    fun changeUserPhone() {
        val username = "64642410104"
        val phoneNumber = "+5511950336492"

        val client = CognitoIdentityProviderClient.builder().build()

        val phoneRequest = AdminUpdateUserAttributesRequest.builder()
            .username(username)
            .userPoolId(userPoolId)
            .userAttributes(
                AttributeType.builder()
                    .name("phone_number")
                    .value(phoneNumber).build(),
            ).build()
        client.adminUpdateUserAttributes(phoneRequest)

        val verifyRequest = AdminUpdateUserAttributesRequest.builder()
            .username(username)
            .userPoolId(userPoolId)
            .userAttributes(
                AttributeType.builder()
                    .name("phone_number_verified")
                    .value("true").build(),
            ).build()
        client.adminUpdateUserAttributes(verifyRequest)
    }
}*/