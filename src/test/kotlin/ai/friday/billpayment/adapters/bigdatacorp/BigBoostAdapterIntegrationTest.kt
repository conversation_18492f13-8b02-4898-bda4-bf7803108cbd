/*
package ai.friday.billpayment.adapters.bigdatacorp

import ai.friday.billpayment.adapters.handlebar.HandlebarTemplateCompiler
import ai.friday.billpayment.adapters.pdf.FlyingSaucerPDFConverter
import ai.friday.billpayment.adapters.pdf.PDFConfiguration
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.DocumentInfo
import ai.friday.billpayment.app.account.Gender
import ai.friday.billpayment.app.account.MonthlyIncome
import ai.friday.billpayment.app.account.PoliticallyExposed
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.createEmailTemplatesConfiguration
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.shouldBe
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import java.io.File
import java.io.FileOutputStream
import java.time.LocalDate
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@MicronautTest(environments = [FRIDAY_ENV, "test"])
@Disabled
internal class BigBoostAdapterIntegrationTest(private val bigBoostAdapter: BigBoostAdapter) {

    private val handlebarTemplateCompiler = HandlebarTemplateCompiler(createEmailTemplatesConfiguration())
    private val pdfConverter = FlyingSaucerPDFConverter(
        configuration = PDFConfiguration(
            fontFiles = listOf("templates/fonts/poppins-medium.ttf"),
            folderName = "target",
            dotsPerPoint = 35f,
            dotsPerPixel = 20,
        ),
    )

    @Test
    @Disabled
    fun `should read personal user data for KYC with provided document`() {
        val response = bigBoostAdapter.getKycDossier("***********")
        response.map {
            println(it)
        }
    }

    @Test
    @Disabled
    fun `should read personal user data for SimpleSignUp KYC with provided document`() {
        val response = bigBoostAdapter.getKycDossierRawData("***********")
        response.map {
            println(it)
        }
    }

    @Test
    @Disabled
    fun `deve pegar os dados e gerar o relatorio kyc simplificado para o cpf informado`() {
        val accountRegisterData = AccountRegisterData(
            accountId = AccountId(),
            emailAddress = EmailAddress(email = "<EMAIL>"),
            nickname = "",
            mobilePhone = MobilePhone("+*************"),
            mobilePhoneVerified = true,
            mobilePhoneTokenExpiration = null,
            created = getZonedDateTime(),
            lastUpdated = getZonedDateTime(),
            documentInfo = DocumentInfo(
                name = "LEILA REGINA DOS SANTOS",
                cpf = "***********",
                birthDate = LocalDate.of(1986, 1, 1),
                fatherName = "",
                motherName = "",
                rg = "",
                docType = DocumentType.CPF,
                cnhNumber = null,
                orgEmission = "DETRAN",
                expeditionDate = null,
                birthCity = "",
                birthState = "",
            ),
            calculatedGender = Gender.M,
            isDocumentEdited = false,
            uploadedCNH = null,
            uploadedDocument = null,
            livenessId = null,
            uploadedSelfie = null,
            address = null,
            monthlyIncome = MonthlyIncome(0, 2_000_00),
            politicallyExposed = PoliticallyExposed(false, null),
            agreementData = null,
            kycFile = null,
            openForUserReview = false,
            openedForUserReviewAt = null,
            externalId = null,
            riskAnalysisFailedReasons = null,
            riskAnalysisFailedReasonsHistory = null,
            registrationType = RegistrationType.BASIC,
            fraudListMatch = false,
        )
        getSimpleSignUpKycDossier(accountRegisterData)
    }

    private fun getSimpleSignUpKycDossier(accountRegisterData: AccountRegisterData) {
        val result = bigBoostAdapter.getKycDossier(accountRegisterData.document!!.value)

        result.isRight() shouldBe true

        result.map { kycDossier ->
            val html = handlebarTemplateCompiler.buildHtml(kycDossier.toForm(accountRegisterData, faceMatch = null, documentScan = null))
            val pdfContent = pdfConverter.convert(html.value)

            val fileName = "integration_test_kyc_$accountRegisterData.document!!.value"
            val fileHtml = File("target/kyc/$fileName.html")
            val filePdf = File("target/kyc/$fileName.pdf")

            fileHtml.parentFile.mkdirs()

            FileOutputStream(filePdf).write(pdfContent)
            FileOutputStream(fileHtml).write(html.value.toByteArray())
        }
    }
}*/