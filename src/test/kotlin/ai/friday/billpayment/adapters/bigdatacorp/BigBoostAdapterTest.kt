package ai.friday.billpayment.adapters.bigdatacorp

import ai.friday.billpayment.app.integrations.DocumentBelongsToAMinorException
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import java.nio.charset.Charset
import java.time.LocalDate
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

internal class BigBoostAdapterTest {

    private val bigBoostAdapter = BigBoostAdapter(
        BigBoostHttpConfiguration(
            mockk() {
                every {
                    defaultCharset
                } returns Charset.defaultCharset()
            },
            mockk(),
        ),
        BigBoostConfiguration().apply {
            bigBoostHost = "http://fakehost"
            accessToken = ""
            peoplePath = ""
            companyPath = ""
            addressPath = ""
        },
    )

    @Test
    fun `should concatenate title+space+street main when street has title field`() {
        val title = "teste"
        val main = "nome da rua"
        val addressBasicDataTO = AddressBasicDataTO(
            ectStandardizedTitle = title,
            ectStandardizedAddressMain = main,
            zipCode = "",
            zipCodeType = "",
            city = "",
            ibgeCityCode = "",
            state = "",
            country = "",
        )

        with(addressBasicDataTO.toAddress()) {
            streetName shouldBe "$title $main"
        }
    }

    @Test
    fun `should not add space before main name when street doesn't have title`() {
        val main = "nome da rua"
        val addressBasicDataTO = AddressBasicDataTO(
            ectStandardizedTitle = null,
            ectStandardizedAddressMain = main,
            zipCode = "",
            zipCodeType = "",
            city = "",
            ibgeCityCode = "",
            state = "",
            country = "",
        )

        with(addressBasicDataTO.toAddress()) {
            streetName shouldBe main
        }
    }

    @Test
    fun `should not add trailing whitespace when ectStandardizedAddressMain is not available`() {
        val addressBasicDataTO = AddressBasicDataTO(
            ectStandardizedTitle = "nome da rua",
            ectStandardizedAddressMain = "",
            zipCode = "",
            zipCodeType = "",
            country = "",
        )

        with(addressBasicDataTO.toAddress()) {
            streetName shouldBe "nome da rua"
        }
    }

    @Test
    fun `should remove unnecessary whitespaces from all fields`() {
        val addressBasicDataTO = AddressBasicDataTO(
            ectStandardizedTitle = " nome da rua ",
            ectStandardizedAddressMain = " principal ",
            typology = " avenida ",
            zipCode = "",
            zipCodeType = "",
            city = " Fortaleza ",
            ibgeCityCode = "",
            ectStandardizedNeighborhood = " Bairro ",
            state = "RJ ",
            country = "",
        )

        with(addressBasicDataTO.toAddress()) {
            streetType shouldBe "avenida"
            streetName shouldBe "nome da rua principal"
            neighborhood shouldBe "Bairro"
            city shouldBe "Fortaleza"
            state shouldBe "RJ"
        }
    }

    @ParameterizedTest
    @ValueSource(
        strings = [
            """{"Result":[{"MatchKeys":"doc{12345678909}","BasicData":{"TaxIdNumber":"12345678909","TaxIdStatus":"CPF DOES NOT EXIST IN RECEITA FEDERAL DATABASE","TaxIdStatusDate":"2023-12-18T21:07:40.8007377Z"},"KycData":{"PEPHistory":[],"IsCurrentlyPEP":false,"SanctionsHistory":[],"IsCurrentlySanctioned":false,"WasPreviouslySanctioned":false,"Last30DaysSanctions":0,"Last90DaysSanctions":0,"Last180DaysSanctions":0,"Last365DaysSanctions":0,"LastYearPEPOccurence":0,"Last3YearsPEPOccurence":0,"Last5YearsPEPOccurence":0,"Last5PlusYearsPEPOccurence":0,"IsCurrentlyElectoralDonor":false,"IsHistoricalElectoralDonor":false,"TotalElectoralDonations":0,"TotalElectoralDonationAmount":0,"ElectoralDonations":{}},"MediaProfileAndExposure":{"TotalPages":0},"OnlineCertificates":[{"Origin":"Receita-Federal Status","InputParameters":"doc{12345678909}null},dateformat{yyyy-mm-dd}"}],"Emails":[],"Phones":[]}],"QueryId":"7de7b016-b658-49df-b372-1d3c2a825a80","ElapsedMilliseconds":166,"QueryDate":"2023-12-18T21:07:40.731988Z","Status":{"phones":[{"Code":0,"Message":"OK"}],"emails":[{"Code":0,"Message":"OK"}],"media_profile_and_exposure":[{"Code":-2008,"Message":"NOT ENOUGH DATA TO GENERATE OUTPUT"}],"basic_data":[{"Code":0,"Message":"OK"}],"kyc":[{"Code":0,"Message":"OK"}],"ondemand_rf_status":[{"Code":-123,"Message":"BIRTH DATE PARAMETER MUST BE FILLED OUT"}]},"Evidences":{}}""",
        ],
    )
    fun `deve fazer o parse corretamente do json de resposta do bigdatacorp`(json: String) {
        val parsedData = bigBoostAdapter.parseBigBoostTO(json, "12345678909")
        parsedData.isRight() shouldBe true
    }

    @Test
    fun `deve fazer o parse corretamente do json de um menor de idade no extractPersonName`() {
        val json = """{"Result":[],"QueryId":"7867f740-a29e-4e40-ae0c-3f1e9df80fd7","ElapsedMilliseconds":37,"QueryDate":"2023-12-18T21:03:34.1273134Z","Status":{"date_of_birth_validation":[{"Code":-200,"Message":"THIS CPF BELONGS TO A MINOR. DATE OF BIRTH IS NEEDED TO PROCESS REQUEST."}]}}"""
        val parsedData = bigBoostAdapter.extractPersonName(json.right())
        parsedData.isLeft() shouldBe true
        parsedData.mapLeft {
            it.shouldBeTypeOf<DocumentBelongsToAMinorException>()
        }
    }

    @Test
    fun `deve fazer o parse corretamente do json de um menor de idade no parseBigBoost`() {
        val json = """{"Result":[],"QueryId":"7867f740-a29e-4e40-ae0c-3f1e9df80fd7","ElapsedMilliseconds":37,"QueryDate":"2023-12-18T21:03:34.1273134Z","Status":{"date_of_birth_validation":[{"Code":-200,"Message":"THIS CPF BELONGS TO A MINOR. DATE OF BIRTH IS NEEDED TO PROCESS REQUEST."}]}}"""
        val parsed = bigBoostAdapter.parseBigBoostTO(json, "")
        parsed.isLeft() shouldBe true
        parsed.mapLeft {
            it.shouldBeTypeOf<DocumentBelongsToAMinorException>()
        }
    }

    @Test
    fun `should parse birth date with yyyy-mm-dd format`() {
        parseOutputDataBirthDate("2023-12-18") shouldBe LocalDate.of(2023, 12, 18)
    }

    @Test
    fun `should parse birth date with ddmmyyyy format`() {
        parseOutputDataBirthDate("1902-19-90") shouldBe LocalDate.of(1990, 2, 19)
    }

    @Test
    fun `should parse birth date with dmmyyyyy format`() {
        parseOutputDataBirthDate("6082000") shouldBe LocalDate.of(2000, 8, 6)
    }
}