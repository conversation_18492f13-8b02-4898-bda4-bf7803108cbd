/*
package ai.friday.billpayment.adapters.bigdatacorp

import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.InvalidDocumentImageException
import arrow.core.getOrElse
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import java.io.File
import java.io.FileInputStream
import java.util.Base64
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
@MicronautTest(environments = [FRIDAY_ENV])
class BigIdAdapterIntegrationTest(private val bigIdAdapter: BigIdAdapter) {

    @Test
    fun `should match facematch`() {
        val sourceUrl = Thread.currentThread().contextClassLoader.getResource("images/gringo-cnh.png")
        val targetUrl = Thread.currentThread().contextClassLoader.getResource("images/gringo-foto.png")
        val sourceFile = FileInputStream(sourceUrl!!.path)
        val targetFile = FileInputStream(targetUrl!!.path)

        val result = bigIdAdapter.match(sourceFile, targetFile)
        println(result)
        result.shouldNotBeNull()
    }

    @Test
    fun `should not match facematch`() {
        val sourceUrl = Thread.currentThread().contextClassLoader.getResource("images/sorvete-1.jpg")
        val targetUrl = Thread.currentThread().contextClassLoader.getResource("images/sorvete-1.jpg")
        val sourceFile = FileInputStream(sourceUrl!!.path)
        val targetFile = FileInputStream(targetUrl!!.path)

        val result = bigIdAdapter.match(sourceFile, targetFile)
        println(result)
        result.shouldNotBeNull()
    }

    @Test
    fun `should return InvalidDocumentImageException when cnh image is frontside`() {
        val base64Encoder = Base64.getEncoder()

        val sourceUrl = Thread.currentThread().contextClassLoader.getResource("images/RG_FULL.jpg")
        val sourceFile = File(sourceUrl!!.path)

        val result = bigIdAdapter.parseDocument(base64Encoder.encodeToString(sourceFile.readBytes()))

        result.isLeft() shouldBe true
        result.mapLeft { it.shouldBeTypeOf<InvalidDocumentImageException>() }
    }

    @Test
    fun `should read document with two images`() {
        val base64Encoder = Base64.getEncoder()

        val sourceUrlFront = Thread.currentThread().contextClassLoader.getResource("images/RG_FRENTE.jpeg")
        val sourceUrlBack = Thread.currentThread().contextClassLoader.getResource("images/RG_VERSO.jpeg")

        val frontDocumentFile = File(sourceUrlFront!!.path)
        val backDocumentFile = File(sourceUrlBack!!.path)

        val documentQuality =
            bigIdAdapter.fetchDocumentQuality(base64Encoder.encodeToString(frontDocumentFile.readBytes()))
                .getOrElse { throw it }

        val result = bigIdAdapter.parseDocument(
            base64Encoder.encodeToString(frontDocumentFile.readBytes()),
            base64Encoder.encodeToString(backDocumentFile.readBytes()),
            documentQuality.typeOfDocument,
        )

        result.isRight() shouldBe true
        result.map { document ->
            document.docType.value shouldBe "RG"
            document.cpf shouldBe "13397597722"
        }
    }

    @Test
    fun `check RG verso quality`() {
        val base64Encoder = Base64.getEncoder()

        val sourceUrl = Thread.currentThread().contextClassLoader.getResource("images/RG_VERSO.jpeg")
        val sourceFile = File(sourceUrl!!.path)

        val result = bigIdAdapter.fetchDocumentQuality(base64Encoder.encodeToString(sourceFile.readBytes()))

        result.isRight() shouldBe true
        result.map {
            it.containsFace shouldBe false
            it.typeOfDocument shouldBe DocumentType.RG
        }
    }

    @Test
    fun `check RG frente quality`() {
        val base64Encoder = Base64.getEncoder()

        val sourceUrl = Thread.currentThread().contextClassLoader.getResource("images/RG_FRENTE.jpeg")
        val sourceFile = File(sourceUrl!!.path)

        val result = bigIdAdapter.fetchDocumentQuality(base64Encoder.encodeToString(sourceFile.readBytes()))

        result.isRight() shouldBe true
        result.map {
            it.containsFace shouldBe true
            it.typeOfDocument shouldBe DocumentType.RG
        }
    }
}*/