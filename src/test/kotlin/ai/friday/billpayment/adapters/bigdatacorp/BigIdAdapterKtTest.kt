package ai.friday.billpayment.adapters.bigdatacorp

import ai.friday.billpayment.app.integrations.DocumentMustBeOpenedException
import ai.friday.billpayment.app.integrations.DocumentType
import io.kotest.matchers.shouldBe
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource

internal class BigIdAdapterKtTest {
    @ParameterizedTest
    @CsvSource(
        value = [
            "RIO DE JANEIRO, RJ\tRIO DE JANEIRO\tRJ",
            "SÃO PAULO/SP\tSÃO PAULO\tSP",
            "   cidade-xpto  /  DF   \tCIDADE-XPTO\tDF",
        ],
        delimiter = '\t',
    )
    fun `should be able to parse city and state when provided value is valid`(
        cityAndState: String,
        expectedCity: String,
        expectedState: String,
    ) {
        splitCityAndState(cityAndState) shouldBe Pair(expectedCity, expectedState)
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "SÃO PAULO SP",
            "MACEIO, ALAGOAS",
        ],
        delimiter = '\t',
    )
    fun `should return null when format is not valid`(cityAndState: String) {
        splitCityAndState(cityAndState) shouldBe null
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "RIO DE JANEIRO RJ\tRIO DE JANEIRO\tRJ",
            "SÃO PAULO SP\tSÃO PAULO\tSP",
            "   cidade-xpto DF   \tCIDADE-XPTO\tDF",
            "TO \t''\tTO",
        ],
        delimiter = '\t',
    )
    fun `should be able to parse city and state from RG when provided value is valid`(
        cityAndState: String,
        expectedCity: String,
        expectedState: String,
    ) {
        splitCityAndStateFromRG(cityAndState) shouldBe Pair(expectedCity, expectedState)
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "SÃO PAULO",
            "MACEIOXX",
        ],
        delimiter = '\t',
    )
    fun `should return null when format is not valid from RG`(cityAndState: String) {
        splitCityAndStateFromRG(cityAndState) shouldBe null
    }

    @Test
    fun `should ignore orgEmission when name is unknown`() {
        processOrgEmission("Unknown Name") shouldBe ""
    }

    @Test
    fun `should return achronym when orgEmission name is known`() {
        processOrgEmission("Advocacia-Geral da União") shouldBe "AGU"
    }

    @ParameterizedTest
    @ValueSource(strings = ["CNH", "CNHV2"])
    fun `deve retornar tipo de documento CNH`(documentType: String) {
        val bigIdAdapter = BigIdAdapter(mockk(), mockk())
        val cnhMap = mapOf(
            "DOCTYPE" to documentType,
            "SIDE" to "C",
            "IDENTIFICATION_NUMBER" to "000000",
            "BIRTHDATE" to "23/08/1988",
            "CNHNUMBER" to "000000",
            "EXPEDITIONDATE" to "23/08/2008",
        )
        bigIdAdapter.buildDocumentInfo(cnhMap).map {
            it.docType shouldBe DocumentType.CNH
        }
    }

    @ParameterizedTest
    @ValueSource(strings = ["UNDEFINED", "RG", "NEWRG"])
    fun `deve retornar tipo de documento RG`(documentType: String) {
        val bigIdAdapter = BigIdAdapter(mockk(), mockk())
        val cnhMap = mapOf(
            "DOCTYPE" to documentType,
            "DOCEMISSIONPLACE" to "SP",
            "IDENTIFICATIONNUMBER" to "000000",
            "BIRTHDATE" to "23/08/1988",
            "EXPEDITIONDATE" to "23/08/2008",
        )
        bigIdAdapter.buildDocumentInfo(cnhMap).map {
            it.docType shouldBe DocumentType.RG
        }
    }

    @Test
    fun `should ignore case when orgEmission name is known`() {
        processOrgEmission("advocacia-geral da união") shouldBe "AGU"
    }

    @Test
    fun `should ignore accented characters when orgEmission name is known`() {
        processOrgEmission("Advocacia-Geral da Uniao") shouldBe "AGU"
    }

    @Test
    fun `should ignore punctuation characters when orgEmission name is known`() {
        processOrgEmission("Advocacia Geral da União") shouldBe "AGU"
    }

    @Test
    fun `should ignore white space characters when orgEmission name is known`() {
        processOrgEmission("Advocacia-GeraldaUnião") shouldBe "AGU"
    }

    @Test
    fun `deve permitir sigla conhecida`() {
        processOrgEmission("CREA") shouldBe "CREA"
    }

    @Test
    fun `deve permitir sigla conhecida com UF`() {
        processOrgEmission("CREA-RJ") shouldBe "CREA-RJ"
    }

    @Test
    fun `não deve permitir campo desconhecido com mais de 7 caracteres`() {
        processOrgEmission("DIRETORIA") shouldBe ""
    }

    @ParameterizedTest
    @ValueSource(strings = ["", ONLY_BACK, ONLY_FRONT])
    fun `deve retornar erro quando identificar uma cnh fechada`(value: String) {
        val bigIdAdapter = BigIdAdapter(mockk(), mockk())

        val map: Map<String, String> = if (value.isEmpty()) mapOf() else mapOf("SIDE" to value)

        assertThrows<DocumentMustBeOpenedException> {
            bigIdAdapter.buildCNHDocumentInfo(map)
        }
    }

    @Test
    fun `deve retornar sucesso quando identificar uma cnh aberta`() {
        val bigIdAdapter = BigIdAdapter(mockk(), mockk())

        assertDoesNotThrow {
            bigIdAdapter.buildCNHDocumentInfo(mapOf("SIDE" to FULL_DOCUMENT))
        }
    }
}