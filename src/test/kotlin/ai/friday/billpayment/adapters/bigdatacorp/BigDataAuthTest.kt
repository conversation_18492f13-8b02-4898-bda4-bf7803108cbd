package ai.friday.billpayment.adapters.bigdatacorp

import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

internal class BigDataAuthTest {

    private val expectedBigDataAccessToken = BigDataAccessToken("TOKEN", expiration = getZonedDateTime().plusMinutes(3))

    private val bigDataAuthMock: BigDataAuth = mockk() {
        every { fetchAccessToken() } returns expectedBigDataAccessToken
    }
    private val bigDataToken = BigDataAccessTokenWrapper(
        bigDataAuth = bigDataAuthMock,
    )

    @Test
    fun `should set access token when there is no token set`() {
        bigDataToken.fetch() shouldBe expectedBigDataAccessToken

        verify(exactly = 1) { bigDataAuthMock.fetchAccessToken() }
        bigDataToken.bigDataAccessToken shouldBe expectedBigDataAccessToken
    }

    @Test
    fun `should return existent access token when it is already set`() {
        val bigDataToken = BigDataAccessTokenWrapper(
            bigDataAuth = bigDataAuthMock,
            bigDataAccessToken = expectedBigDataAccessToken,
        )

        bigDataToken.fetch() shouldBe expectedBigDataAccessToken

        verify(exactly = 0) { bigDataAuthMock.fetchAccessToken() }
    }

    @Test
    fun `should return new access token when time has expired`() {
        val bigDataToken = BigDataAccessTokenWrapper(
            bigDataAuth = bigDataAuthMock,
            bigDataAccessToken = BigDataAccessToken(
                expiration = getZonedDateTime().plusSeconds(59),
                value = "EXPIRED_TOKEN",
            ),
        )

        bigDataToken.fetch() shouldBe expectedBigDataAccessToken

        verify(exactly = 1) { bigDataAuthMock.fetchAccessToken() }
    }
}