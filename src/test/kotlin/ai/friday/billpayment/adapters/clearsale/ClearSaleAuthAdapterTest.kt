package ai.friday.billpayment.adapters.clearsale

import com.github.tomakehurst.wiremock.client.WireMock
import com.github.tomakehurst.wiremock.client.WireMock.aResponse
import com.github.tomakehurst.wiremock.client.WireMock.equalTo
import com.github.tomakehurst.wiremock.client.WireMock.post
import com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor
import com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo
import io.kotest.matchers.result.shouldBeSuccess
import io.kotest.matchers.result.shouldNotBeSuccess
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode

@Execution(ExecutionMode.CONCURRENT)
internal class ClearSaleAuthAdapterTest {

    @Test
    fun `should fetch new token when request is made with success`() {
        val c = ClearSaleTestContext.create()

        c.wm.stubFor(
            post("/v1/authentication")
                .withHeader("Content-Type", equalTo("application/json"))
                .withRequestBody(
                    equalTo("""{"Username":"friday_user","Password":"friday_password"}"""),
                )
                .willReturn(
                    aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("""{"token":"mockedToken","expiresInSeconds":7200}"""),
                ),
        )

        val result = c.authAdapter.acquireToken()

        c.wm.verify(
            1,
            postRequestedFor(urlEqualTo("/v1/authentication"))
                .withRequestBody(WireMock.containing("friday_user")),
        )

        result.shouldBeSuccess() shouldBe "mockedToken"
    }

    @Test
    fun `should fail when token cannot be acquired`() {
        val c = ClearSaleTestContext.create(
            configuration = ClearSaleTestContext.configuration(username = "fake_user", password = "wrong_password"),
        )

        c.wm.stubFor(
            post("/v1/authentication")
                .withHeader("Content-Type", equalTo("application/json"))
                .withRequestBody(
                    equalTo("""{"Username":"fake_user","Password":"wrong_password"}"""),
                )
                .willReturn(
                    aResponse()
                        .withStatus(401)
                        .withHeader("Content-Type", "application/json")
                        .withBody(
                            """{
                                "statusCode": 401,
                                "message": "Unauthorized. Access token is missing or invalid."
                            }
                            """.trimIndent(),
                        ),
                ),
        )

        c.authAdapter.acquireToken().let {
            it.shouldNotBeSuccess()
            it.exceptionOrNull().shouldBeTypeOf<HttpClientResponseException>().status shouldBe HttpStatus.UNAUTHORIZED
        }

        c.wm.verify(
            1,
            postRequestedFor(urlEqualTo("/v1/authentication"))
                .withRequestBody(WireMock.containing("fake_user")),
        )
    }

    // TODO add test cases for token expiration
}