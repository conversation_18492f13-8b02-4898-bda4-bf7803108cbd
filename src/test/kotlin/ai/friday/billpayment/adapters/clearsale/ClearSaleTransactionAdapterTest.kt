package ai.friday.billpayment.adapters.clearsale

import ai.friday.billpayment.FileUtils
import ai.friday.billpayment.aJsonResponse
import ai.friday.billpayment.adapters.clearsale.ClearSaleTestContext.transactionResponseBody
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.integration.DOCUMENT
import com.github.tomakehurst.wiremock.client.WireMock.equalTo
import com.github.tomakehurst.wiremock.client.WireMock.equalToJson
import com.github.tomakehurst.wiremock.client.WireMock.get
import com.github.tomakehurst.wiremock.client.WireMock.post
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.result.shouldBeSuccess
import io.kotest.matchers.shouldBe
import io.mockk.every
import java.util.UUID
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode

@Execution(ExecutionMode.CONCURRENT)
class ClearSaleTransactionAdapterTest {
    @Test
    fun `should create a transaction with success`() {
        val c = ClearSaleTestContext.create()
        val document = Document(DOCUMENT)

        val token = UUID.randomUUID().toString()
        every { c.authAdapter.acquireToken() } returns Result.success(token)

        val tid = UUID.randomUUID().toString()

        c.wm.stubFor(
            post("/v1/transaction")
                .withHeader("Content-Type", equalTo("application/json"))
                .withHeader("Authorization", equalTo("Bearer $token"))
                .withRequestBody(
                    equalToJson(
                        """{
                        |"documentType":"CPF",
                        |"document":"$DOCUMENT"
                        |}
                        """.trimMargin(),
                    ),
                )
                .willReturn(
                    aJsonResponse(transactionResponseBody(id = tid)),
                ),
        )

        c.transactionAdapter
            .createTransaction(document)
            .shouldBeSuccess().run { id shouldBe tid }
    }

    @Test
    fun `should fetch transaction insights with success`() {
        val c = ClearSaleTestContext.create()

        val tid = UUID.randomUUID().toString()
        val bin = "123456"
        val last4 = "7890"

        val token = UUID.randomUUID().toString()
        every { c.authAdapter.acquireToken() } returns Result.success(token)

        c.wm.stubFor(
            post("/v1/transaction/$tid/trustcard")
                .withHeader("Content-Type", equalTo("application/json"))
                .withHeader("Authorization", equalTo("Bearer $token"))
                .withRequestBody(
                    equalToJson(
                        """{
                        |"bin":"$bin",
                        |"last4":"$last4"
                        |}
                        """.trimMargin(),
                    ),
                )
                .willReturn(
                    aJsonResponse(FileUtils.readLocalFileAsText("clearsale/trust-card-insights-response.json")),
                ),
        )

        c.transactionAdapter.transactionInsights(
            transactionId = tid,
            bin = bin,
            lastFourDigits = last4,
        ).shouldBeSuccess().let { insights ->
            insights.map { it.code }.let {
                it.size shouldBe 5
                it shouldContainExactlyInAnyOrder listOf(
                    InsightCode.CRT0004,
                    InsightCode.CRT0005,
                    InsightCode.CRT0006,
                    InsightCode.CRT0007,
                    InsightCode.CRT0008,
                )
            }
        }
    }

    @Test
    fun `should fetch a transaction with success`() {
        val c = ClearSaleTestContext.create()

        val token = UUID.randomUUID().toString()
        every { c.authAdapter.acquireToken() } returns Result.success(token)

        val tid = UUID.randomUUID().toString()

        c.wm.stubFor(
            get("/v1/transaction/$tid")
                .withHeader("Authorization", equalTo("Bearer $token"))
                .willReturn(aJsonResponse(transactionResponseBody(id = tid))),
        )

        c.transactionAdapter.findTransaction(tid)
            .shouldBeSuccess().run { id shouldBe tid }
    }
}