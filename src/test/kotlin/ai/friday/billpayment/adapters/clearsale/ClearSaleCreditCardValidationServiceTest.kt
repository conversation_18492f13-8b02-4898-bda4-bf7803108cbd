package ai.friday.billpayment.adapters.clearsale

import ai.friday.billpayment.adapters.clearsale.ClearSaleTestContext.negativeInsights
import ai.friday.billpayment.adapters.clearsale.ClearSaleTestContext.neutralInsights
import ai.friday.billpayment.adapters.clearsale.ClearSaleTestContext.ownershipInsights
import ai.friday.billpayment.adapters.clearsale.ClearSaleTestContext.transaction
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.integrations.CreditCardOwnership
import ai.friday.billpayment.app.integrations.CreditCardOwnershipValidationRequest
import ai.friday.billpayment.app.integrations.CreditCardScore
import ai.friday.billpayment.app.integrations.CreditCardScoreValidationRequest
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.DOCUMENT_2
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.mockk.every
import java.util.UUID
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode

@Execution(ExecutionMode.CONCURRENT)
class ClearSaleCreditCardValidationServiceTest {

    @Nested
    @DisplayName("when validating credit card score")
    inner class CreditCardScoreContext {
        @Test
        fun `should return MATCH when insight is positive`() {
            val c = ClearSaleTestContext.create()

            val request = CreditCardScoreValidationRequest(
                bin = "123456",
                lastFourDigits = "7890",
                cpf = DOCUMENT,
                totalValue = "100.00",
            )

            val transactionId = UUID.randomUUID().toString()

            every { c.transactionAdapter.createTransaction(Document(request.cpf)) } returns Result.success(transaction(transactionId))

            ownershipInsights.forEach { ownershipInsight ->
                every {
                    c.transactionAdapter.transactionInsights(
                        transactionId = transactionId,
                        bin = request.bin,
                        lastFourDigits = request.lastFourDigits,
                    )
                } returns Result.success(listOf(ownershipInsight))

                c.service.creditcardScore(request).let {
                    it.isRight().shouldBeTrue()
                    it.getOrNull()?.shouldBe(CreditCardScore.MATCH)
                }
            }
        }

        @Test
        fun `should return NOT_MATCH when insight is negative`() {
            val c = ClearSaleTestContext.create()

            val request = CreditCardScoreValidationRequest(
                bin = "123456",
                lastFourDigits = "7890",
                cpf = DOCUMENT,
                totalValue = "100.00",
            )

            val transactionId = UUID.randomUUID().toString()

            every { c.transactionAdapter.createTransaction(Document(request.cpf)) } returns Result.success(transaction(transactionId))

            negativeInsights.forEach { negativeInsight ->
                every {
                    c.transactionAdapter.transactionInsights(
                        transactionId = transactionId,
                        bin = request.bin,
                        lastFourDigits = request.lastFourDigits,
                    )
                } returns Result.success(listOf(negativeInsight))

                c.service.creditcardScore(request).let {
                    it.isRight().shouldBeTrue()
                    it.getOrNull()?.shouldBe(CreditCardScore.NO_MATCH)
                }
            }
        }

        @Test
        fun `should return MANUAL_CHECK when insight is neutral`() {
            val c = ClearSaleTestContext.create()

            val request = CreditCardScoreValidationRequest(
                bin = "123456",
                lastFourDigits = "7890",
                cpf = DOCUMENT,
                totalValue = "100.00",
            )

            val transactionId = UUID.randomUUID().toString()

            every { c.transactionAdapter.createTransaction(Document(request.cpf)) } returns Result.success(transaction(transactionId))

            neutralInsights.forEach { neutralInsight ->
                every {
                    c.transactionAdapter.transactionInsights(
                        transactionId = transactionId,
                        bin = request.bin,
                        lastFourDigits = request.lastFourDigits,
                    )
                } returns Result.success(listOf(neutralInsight))

                c.service.creditcardScore(request).let {
                    it.isRight().shouldBeTrue()
                    it.getOrNull()?.shouldBe(CreditCardScore.MANUAL_CHECK)
                }
            }
        }
    }

    @Nested
    @DisplayName("when validating ownership")
    inner class CreditCardOwnershipContext {
        @Test
        fun `should return OWNER when insight is positive`() {
            val c = ClearSaleTestContext.create()

            val request = CreditCardOwnershipValidationRequest(
                bin = "123456",
                lastFourDigits = "7890",
                cpf = DOCUMENT_2,
            )

            val transactionId = UUID.randomUUID().toString()

            every { c.transactionAdapter.createTransaction(Document(request.cpf)) } returns Result.success(transaction(transactionId))

            ownershipInsights.forEach { ownershipInsight ->
                every {
                    c.transactionAdapter.transactionInsights(
                        transactionId = transactionId,
                        bin = request.bin,
                        lastFourDigits = request.lastFourDigits,
                    )
                } returns Result.success(listOf(ownershipInsight))

                c.service.validateOwnership(request).let {
                    it.isRight().shouldBeTrue()
                    it.getOrNull()?.shouldBe(CreditCardOwnership.IS_OWNER)
                }
            }
        }

        @Test
        fun `should return NOT_OWNER when insight is negative or neutral`() {
            val c = ClearSaleTestContext.create()

            val request = CreditCardOwnershipValidationRequest(
                bin = "123456",
                lastFourDigits = "7890",
                cpf = DOCUMENT_2,
            )

            val transactionId = UUID.randomUUID().toString()

            every { c.transactionAdapter.createTransaction(Document(request.cpf)) } returns Result.success(transaction(transactionId))

            (negativeInsights + neutralInsights).forEach { insight ->
                every {
                    c.transactionAdapter.transactionInsights(
                        transactionId = transactionId,
                        bin = request.bin,
                        lastFourDigits = request.lastFourDigits,
                    )
                } returns Result.success(listOf(insight))

                c.service.validateOwnership(request).let {
                    it.isRight().shouldBeTrue()
                    it.getOrNull()?.shouldBe(CreditCardOwnership.NOT_OWNER)
                }
            }
        }
    }
}