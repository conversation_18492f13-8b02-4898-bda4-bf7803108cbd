package ai.friday.billpayment.adapters.clearsale

import ai.friday.billpayment.FileUtils
import ai.friday.billpayment.adapters.clearsale.ClearSaleInsights.cpfFraudCodes
import ai.friday.billpayment.adapters.clearsale.ClearSaleInsights.ownershipCodes
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.mockServer
import ai.friday.billpayment.rxClient
import com.github.tomakehurst.wiremock.WireMockServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.mockk.spyk
import java.util.UUID

internal object ClearSaleTestContext {
    private val wm: WireMockServer = mockServer()

    private fun httpClient(): RxHttpClient = wm.rxClient()

    fun configuration(
        username: String = "friday_user",
        password: String = "friday_password",
    ) = object : ClearSaleConfiguration {
        override val username = username
        override val password = password
        override val host = wm.baseUrl()
        override val trustedCodes = setOf(InsightCode.CRT0004)
    }

    private fun authAdapter(httpClient: RxHttpClient, configuration: ClearSaleConfiguration) =
        ClearSaleAuthAdapter(
            httpClient = httpClient,
            configuration = configuration,
        )

    private fun transactionAdapter(httpClient: RxHttpClient, authAdapter: ClearSaleAuthAdapter) =
        ClearSaleTransactionAdapter(
            httpClient = httpClient,
            auth = authAdapter,
        )

    fun create(
        configuration: ClearSaleConfiguration = configuration(),
        httpClient: RxHttpClient = httpClient(),
        authAdapter: ClearSaleAuthAdapter = spyk(authAdapter(httpClient, configuration)),
        transactionAdapter: ClearSaleTransactionAdapter = spyk(transactionAdapter(httpClient, authAdapter)),
        service: ClearSaleCreditCardValidationService = ClearSaleCreditCardValidationService(
            transactionAdapter = transactionAdapter,
            authAdapter = authAdapter,
            configuration = configuration,
        ),
    ): Data = Data(
        httpClient = httpClient,
        configuration = configuration,
        authAdapter = authAdapter,
        transactionAdapter = transactionAdapter,
        wm = wm, // shared
        service = service,
    )

    data class Data(
        val httpClient: RxHttpClient,
        val configuration: ClearSaleConfiguration,
        val authAdapter: ClearSaleAuthAdapter,
        val transactionAdapter: ClearSaleTransactionAdapter,
        val wm: WireMockServer,
        val service: ClearSaleCreditCardValidationService,
    )

    private val sampleInsight = TrustCardResponse.Insight(
        code = InsightCode.CRT0001,
        description = "",
        relevance = "",
        type = "",
        category = "",
        relatedTo = listOf(),
    )

    private val transactionResponseBody = FileUtils.readLocalFile("clearsale/transaction-response.json")
    private val sampleTransaction: ClearSaleTransaction = parseObjectFrom<ClearSaleTransaction>(transactionResponseBody())

    fun transactionResponseBody(id: String = UUID.randomUUID().toString()) = transactionResponseBody.replace("\$transactionId", id)
    fun transaction(id: String = UUID.randomUUID().toString()) = sampleTransaction.copy(id = id)

    val ownershipInsights: List<TrustCardResponse.Insight> = ownershipCodes.map { sampleInsight.copy(code = it) }

    val negativeInsights: List<TrustCardResponse.Insight> = cpfFraudCodes.map { sampleInsight.copy(code = it) }

    val neutralInsights: List<TrustCardResponse.Insight> =
        (InsightCode.entries - (ownershipCodes + cpfFraudCodes)).map { sampleInsight.copy(code = it) }
}