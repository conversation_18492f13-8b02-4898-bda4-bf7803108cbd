package ai.friday.billpayment.adapters.circuitbreaker

import ai.friday.billpayment.adapters.arbi.ArbiAdapterException
import io.github.resilience4j.circuitbreaker.CallNotPermittedException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import jakarta.inject.Singleton
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest
class CircuitBreakerTest(embeddedServer: EmbeddedServer) {

    private val circuitBreakerServiceTest = embeddedServer.applicationContext.getBean(CircuitBreakerServiceTest::class.java)

    @Test
    fun `deve recusar a segunda chamada`() {
        assertThrows<ArbiAdapterException> { circuitBreakerServiceTest.load() }
        assertThrows<CallNotPermittedException> { circuitBreakerServiceTest.load() }

        Thread.sleep(3000)

        assertThrows<ArbiAdapterException> { circuitBreakerServiceTest.load() }
    }
}

@Singleton
open class CircuitBreakerServiceTest {
    @CircuitBreakerHoF(name = "myService", minimumNumberOfCalls = 1, failureRateThreshold = 1.00f, waitDurationInOpenState = 2)
    open fun load(): String {
        return getData()
    }

    private fun getData(): String {
        throw ArbiAdapterException()
    }
}