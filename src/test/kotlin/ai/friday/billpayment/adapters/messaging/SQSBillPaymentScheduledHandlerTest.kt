package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.billPaymentScheduled
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message

internal class SQSBillPaymentScheduledHandlerTest {

    private val systemActivityService: SystemActivityService = mockk(relaxUnitFun = true)

    private val configurationMock: SQSMessageHandlerConfiguration = mockk() {
        every { billPaymentScheduledQueueName } returns "queue-name"
    }

    private val notificationAdapter: NotificationAdapter = mockk(relaxed = true)

    private val accountService: AccountService = mockk()

    private val handler = SQSBillPaymentScheduledHandler(
        amazonSQS = mockk(),
        amazonSNS = mockk(),
        configuration = configurationMock,
        topicArn = "topic-name",
        notificationAdapter = notificationAdapter,
        systemActivityService = systemActivityService,
        accountService = accountService,
    )

    @Test
    fun `deve enviar notificacao e registrar atividade de agendamento de conta quando for o primeiro agendamento de conta`() {
        every {
            accountService.findAccountById(any())
        } returns ACCOUNT

        every {
            systemActivityService.hasScheduledBill(ACCOUNT.accountId)
        } returns false

        val event = billPaymentScheduled
            .copy(actionSource = ActionSource.Api(accountId = ACCOUNT.accountId))
            .toBillEventDetailEntity()

        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(event))
                .build()

        handler.handleMessage(message).shouldDeleteMessage shouldBe true

        val slot = slot<Account>()
        verify {
            notificationAdapter.notifyFirstBillScheduled(capture(slot))
            systemActivityService.setHasScheduledBill(ACCOUNT.accountId)
        }

        slot.captured.accountId shouldBe ACCOUNT.accountId
    }

    @Test
    fun `nao deve enviar notificacao quando nao for o primeiro agendamento de conta`() {
        every {
            accountService.findAccountById(any())
        } returns ACCOUNT

        every {
            systemActivityService.hasScheduledBill(ACCOUNT.accountId)
        } returns true

        val event = billPaymentScheduled
            .copy(actionSource = ActionSource.Api(accountId = ACCOUNT.accountId))
            .toBillEventDetailEntity()

        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(event))
                .build()

        handler.handleMessage(message).shouldDeleteMessage shouldBe true

        verify {
            notificationAdapter wasNot Called
        }
    }

    @Test
    fun `nao deve enviar notificacao quando o agendamento não foi feito pelo usuário`() {
        every {
            accountService.findAccountById(any())
        } returns ACCOUNT

        every {
            systemActivityService.hasScheduledBill(ACCOUNT.accountId)
        } returns false

        val event = billPaymentScheduled
            .copy(actionSource = ActionSource.Subscription(accountId = ACCOUNT.accountId))
            .toBillEventDetailEntity()

        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(event))
                .build()

        handler.handleMessage(message).shouldDeleteMessage shouldBe true

        verify {
            notificationAdapter wasNot Called
        }
    }
}