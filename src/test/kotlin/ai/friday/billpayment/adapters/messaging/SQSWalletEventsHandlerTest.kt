package ai.friday.billpayment.adapters.messaging

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.adapters.instrumentation.WalletInstrumentationRepository
import ai.friday.billpayment.adapters.intercom.IntercomAdapter
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.fingerprint.AddDeviceIdResult
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.fingerprint.RemoveDeviceIdResult
import ai.friday.billpayment.app.wallet.MemberAdded
import ai.friday.billpayment.app.wallet.MemberRemoved
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.WalletEvent
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.instrumentation.WalletInstrumentationService
import ai.friday.billpayment.awaitUntilAssertedWithPollingAtMost
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import java.time.Instant
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message

internal class SQSWalletEventsHandlerTest() {

    private val intercomAdapter: IntercomAdapter = mockk {
        every { publishEvent(any<AccountId>(), any(), any()) } just Runs
    }

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val enhancedClient = getDynamoDB()

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletRepository = spyk(
        WalletDbRepository(
            walletDAO = walletDAO,
            walletMemberDAO = walletMemberDAO,
            inviteDAO = inviteDAO,
            inviteReminderDAO = inviteReminderDAO,
            walletLimitDAO = walletLimitDAO,
            accountRepository = mockk(),
        ),
    )

    private val deviceFingerprintService: DeviceFingerprintService = mockk {
        every { removeDeviceId(any(AccountId::class), any(WalletId::class)) } returns RemoveDeviceIdResult.Success
        every { addDeviceId(any(AccountId::class), any(WalletId::class)) } returns Result.success(AddDeviceIdResult.Success(mockk()))
    }

    private val handler = SQSWalletEventsHandler(
        amazonSQS = mockk(),
        amazonSNS = mockk(),
        configuration = mockk(relaxed = true),
        topicArn = "mockArn",
        walletInstrumentationService = WalletInstrumentationService(
            instrumentationRepository = WalletInstrumentationRepository(intercomAdapter),
            walletRepository = walletRepository,
        ),
        deviceFingerprintService = deviceFingerprintService,
    )

    private val walletFixture = WalletFixture()
    private val founder = walletFixture.founder
    private val cofounder = walletFixture.limitedParticipant.copy(
        permissions = walletFixture.limitedParticipant.permissions.copy(manageMembers = true),
    )
    private val member = walletFixture.assistant.copy(status = MemberStatus.REMOVED)
    private val wallet =
        walletFixture.buildPrimaryWallet(
            founderAccount = walletFixture.founderAccount,
            otherMembers = listOf(member, cofounder),
        )

    private val internalBankAccount = walletFixture.buildBankAccount(wallet)

    private val memberAdded = MemberAdded(
        walletId = wallet.id,
        created = Instant.now().toEpochMilli(),
        member = member.accountId,
        actionSource = ActionSource.Api(accountId = founder.accountId),
    )

    private val memberRemoved = MemberRemoved(
        walletId = wallet.id,
        created = Instant.now().toEpochMilli(),
        member = member.accountId,
        actionSource = ActionSource.Api(accountId = founder.accountId),
    )

    private val memberRemovedByCoFounder = MemberRemoved(
        walletId = wallet.id,
        created = Instant.now().toEpochMilli(),
        member = member.accountId,
        actionSource = ActionSource.Api(accountId = cofounder.accountId),
    )

    fun buildMessage(event: WalletEvent) = Message.builder()
        .body(getObjectMapper().writeValueAsString(event))
        .build()

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        walletRepository.save(wallet)
        every { walletRepository.findAccountPaymentMethod(wallet.id) } returns internalBankAccount
    }

    @Test
    fun `should remove device id when member is removed by founder and publish to Intercom`() {
        val response = handler.handleMessage(buildMessage(memberRemoved))

        response.shouldDeleteMessage shouldBe true

        awaitUntilAssertedWithPollingAtMost {
            verify(exactly = 1) {
                deviceFingerprintService.removeDeviceId(memberRemoved.member, memberRemoved.walletId)

                intercomAdapter.publishEvent(
                    accountId = memberRemoved.member,
                    eventName = "removido_da_carteira",
                    customParams = mapOf("walletName" to wallet.name, "removedBy" to founder.name),
                )
                intercomAdapter.publishEvent(
                    accountId = founder.accountId,
                    eventName = "membro_removido_da_carteira",
                    customParams = mapOf("walletName" to wallet.name, "removedMember" to member.name),
                )
            }
        }
    }

    @Test
    fun `should remove device id when member is removed by another member and publish to Intercom`() {
        val response = handler.handleMessage(buildMessage(memberRemovedByCoFounder))

        response.shouldDeleteMessage shouldBe true

        verify(exactly = 1) {
            deviceFingerprintService.removeDeviceId(memberRemovedByCoFounder.member, memberRemovedByCoFounder.walletId)

            intercomAdapter.publishEvent(
                accountId = memberRemoved.member,
                eventName = "removido_da_carteira",
                customParams = mapOf("walletName" to wallet.name, "removedBy" to cofounder.name),
            )
            intercomAdapter.publishEvent(
                accountId = cofounder.accountId,
                eventName = "membro_removido_da_carteira",
                customParams = mapOf("walletName" to wallet.name, "removedMember" to member.name),
            )
        }
    }

    @Test
    fun `should not publish events to Intercom when device id is not removed`() {
        every {
            deviceFingerprintService.removeDeviceId(any(AccountId::class), any(WalletId::class))
        } returns RemoveDeviceIdResult.DeviceIdNotRemoved

        val response = handler.handleMessage(buildMessage(memberRemoved))

        response.shouldDeleteMessage shouldBe true

        verify(exactly = 0) {
            intercomAdapter.publishEvent(any<AccountId>(), any(), any())
        }
    }

    @Test
    fun `should not publish events to Intercom when ActionSource is not Api`() {
        val response = handler.handleMessage(buildMessage(memberRemoved.copy(actionSource = ActionSource.System)))

        response.shouldDeleteMessage shouldBe true

        verify(exactly = 1) {
            deviceFingerprintService.removeDeviceId(memberRemoved.member, memberRemoved.walletId)
        }

        verify(exactly = 0) {
            intercomAdapter.publishEvent(any<AccountId>(), any(), any())
        }
    }

    @Test
    fun `should add deviceId when member is added to wallet and should not publish to intercom`() {
        val response = handler.handleMessage(buildMessage(memberAdded))

        response.shouldDeleteMessage shouldBe true

        verify(exactly = 1) {
            deviceFingerprintService.addDeviceId(memberAdded.member, memberAdded.walletId)
        }

        verify(exactly = 0) {
            intercomAdapter.publishEvent(any<AccountId>(), any(), any())
        }
    }

    @Test
    fun `should not delete message when device id fails to be added`() {
        every {
            deviceFingerprintService.addDeviceId(any(AccountId::class), any(WalletId::class))
        } returns Result.success(AddDeviceIdResult.AccountNotFound)

        val response = handler.handleMessage(buildMessage(memberAdded))

        response.shouldDeleteMessage shouldBe false

        verify(exactly = 1) {
            deviceFingerprintService.addDeviceId(memberAdded.member, memberAdded.walletId)
        }

        verify(exactly = 0) {
            intercomAdapter.publishEvent(any<AccountId>(), any(), any())
        }
    }
}