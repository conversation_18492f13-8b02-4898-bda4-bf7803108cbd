package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.app.vehicledebts.CreateVehicleDebtsResult
import ai.friday.billpayment.app.vehicledebts.VehicleDebtsBillRequest
import ai.friday.billpayment.app.vehicledebts.VehicleDebtsService
import ai.friday.billpayment.billAdded2
import ai.friday.morning.date.dateFormat
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import software.amazon.awssdk.services.sqs.model.Message

class SQSVehicleDebtsEventHandlerTest {
    private val vehicleDebtsService: VehicleDebtsService = mockk(relaxed = true) {
        every { handle(any()) } returns Result.success(CreateVehicleDebtsResult.Created(billAdded2.billId.value))
    }

    private val vehicleDebtsEventHandler = SQSVehicleDebtsEventHandler(
        amazonSQS = mockk(relaxed = true),
        configuration = mockk(relaxed = true),
        vehicleDebtsService = vehicleDebtsService,
        dlqArn = "vehicle-debts-dlq-arn",
    )

    @ParameterizedTest
    @MethodSource("payloads")
    fun `ao receber uma multa de uma placa cadastrada`(payload: String) {
        val expectedAccountId = billAdded2.walletId.value
        val message = Message.builder().body(payload).build()

        val response = vehicleDebtsEventHandler.handleMessage(message)

        val slot = slot<VehicleDebtsBillRequest>()
        verify { vehicleDebtsService.handle(capture(slot)) }

        response.shouldDeleteMessage.shouldBeTrue()
        slot.captured.accountId.value shouldBe expectedAccountId
        slot.captured.barcode!!.number shouldBe billAdded2.barcode!!.number
        slot.captured.dueDate shouldBe billAdded2.dueDate
        slot.captured.description shouldBe "MULTA XPTO"
    }

    companion object {
        @JvmStatic
        fun payloads() = listOf(Payload.PAYLOAD_ORIGINAL, Payload.PAYLOAD_ALIAS)
    }

    private object Payload {
        val PAYLOAD_ORIGINAL = """
            {
                "accountId": "${billAdded2.walletId.value}",
                "id": null,
                "document": "***********",
                "licensePlate": "JBQ6B98",
                "amountTotal": 13000,
                "dueDate": "${billAdded2.dueDate.format(dateFormat)}",
                "description": "MULTA XPTO",
                "barcode": "${billAdded2.barcode!!.number}",
                "digitableLine": "3123123",
                "paymentStatus": "PAID",
                "fees": 0,
                "fine": 0,
                "externalId": "",
                "type": "Fine",
                "originalDueDate": "2025-04-15",
                "unavailableReason": null,
                "quota": null,
                "identifier": null
            }
            """

        val PAYLOAD_ALIAS = """
            {
                "tenantUserId": "${billAdded2.walletId.value}",
                "id": null,
                "document": "***********",
                "licensePlate": "JBQ6B98",
                "amountTotal": 13000,
                "dueDate": "${billAdded2.dueDate.format(dateFormat)}",
                "description": "MULTA XPTO",
                "barcode": "${billAdded2.barcode!!.number}",
                "digitableLine": "3123123",
                "paymentStatus": "PAID",
                "fees": 0,
                "fine": 0,
                "externalId": "",
                "type": "Fine",
                "originalDueDate": "2025-04-15",
                "unavailableReason": null,
                "quota": null,
                "identifier": null
            }
            """
    }
}