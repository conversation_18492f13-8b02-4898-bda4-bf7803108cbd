package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.app.bill.BillSynchronizationStatus
import ai.friday.billpayment.app.dda.DDAExecutionResult
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.integrations.DDACallbackService
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.mockk.Called
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

internal class SQSDDABillsInternalHandlerTest {

    private val sqsClientMock = mockk<SqsClient>(relaxed = true)
    private val ddaServiceMock = mockk<DDAService>(relaxed = true)
    private val ddaCallbackServiceMock = mockk<DDACallbackService>(relaxed = true)

    private val configuration = mockk<SQSMessageHandlerConfiguration>(relaxed = true) {
        every { maxNumberOfMessages } returns 1
    }

    val handler = SQSDDABillsInternalHandler(
        amazonSQS = sqsClientMock,
        configuration = configuration,
        ddaService = ddaServiceMock,
        queueName = "test",
        ddaCallbackService = ddaCallbackServiceMock,
    )

    @Nested
    @DisplayName("when receive an invalid message")
    inner class ExceptionContext {
        @Test
        fun `that is invalid should not call execute bill and delete the message`() {
            assertThrows<IllegalArgumentException> {
                handler.handleMessage(mockk { every { body() } returns MESSAGE_BILL_WITHOUT_BARCODE })
            }

            assertThrows<IllegalArgumentException> {
                handler.handleMessage(mockk { every { body() } returns MESSAGE_BILL_WITHOUT_PAYER })
            }

            assertThrows<IllegalArgumentException> {
                handler.handleMessage(mockk { every { body() } returns MESSAGE_BILL_WITHOUT_DUEDATE })
            }

            val response = handler.handleError(mockk(), IllegalArgumentException())
            response.shouldDeleteMessage.shouldBeTrue()

            verify(exactly = 0) { ddaServiceMock.executeBill(any(), any()) }
        }

        @Test
        fun `and throws an Exception, should keep message to retry`() {
            val response = handler.handleError(mockk(), NoStackTraceException())
            response.shouldDeleteMessage.shouldBeFalse()
        }
    }

    @Nested
    @DisplayName("when receive a message from queue")
    inner class ReceivingMessageContext {
        @Test
        fun `that is valid should call execute bill and delete the message`() {
            val message = mockk<Message> { every { body() } returns MESSAGE_BILL_1 }
            every { ddaServiceMock.executeBill(any(), any()) } returns DDAExecutionResult.Added

            val response = handler.handleMessage(message)

            verify(exactly = 1) { ddaServiceMock.executeBill(any(), any()) }
            response.shouldDeleteMessage.shouldBeTrue()
        }

        @Test
        fun `call execute bill and result is Error, should NOT remove message from queue`() {
            val message = mockk<Message> { every { body() } returns MESSAGE_BILL_1 }
            every { ddaServiceMock.executeBill(any(), any()) } returns DDAExecutionResult.Error()

            val response = handler.handleMessage(message)

            verify(exactly = 1) { ddaServiceMock.executeBill(any(), any()) }
            response.shouldDeleteMessage.shouldBeFalse()
        }

        @Test
        fun `call execute bill and result is not an Error, should remove message from queue`() {
            val results = listOf(DDAExecutionResult.Added, DDAExecutionResult.NotAdded())

            results.forEach { mockedResult ->
                val message = mockk<Message> { every { body() } returns MESSAGE_BILL_1 }
                every { ddaServiceMock.executeBill(any(), any()) } returns mockedResult

                val response = handler.handleMessage(message)
                response.shouldDeleteMessage.shouldBeTrue()
            }

            verify(exactly = results.size) { ddaServiceMock.executeBill(any(), any()) }
        }
    }

    @Nested
    @DisplayName("when process a notification")
    inner class CallbackNotificationContext {
        @Test
        fun `and callback service is available, should notify the respective callback`() {
            mapOf(
                DDAExecutionResult.Added to ddaCallbackServiceMock::notifyBillAdded,
                DDAExecutionResult.Updated(BillSynchronizationStatus.BillAmountTotalUpdated) to ddaCallbackServiceMock::notifyBillUpdated,
                DDAExecutionResult.Error() to ddaCallbackServiceMock::notifyError,
                DDAExecutionResult.NotAdded() to ddaCallbackServiceMock::notifyEventIgnored,
                DDAExecutionResult.AccountNotFound to ddaCallbackServiceMock::notifyEventIgnored,
                DDAExecutionResult.NotUpdated() to ddaCallbackServiceMock::notifyEventIgnored,
            ).forEach { (result, notificationCallback) ->
                clearAllMocks()

                val message = mockk<Message> { every { body() } returns MESSAGE_BILL_1 }
                every { ddaServiceMock.executeBill(any(), any()) } returns result

                handler.handleMessage(message)

                verify(exactly = 1) {
                    ddaServiceMock.executeBill(any(), any())
                    notificationCallback(any())
                }
            }
        }

        @Test
        fun `and callback service is unavailable, should not notify callbacks`() {
            val testHandler = SQSDDABillsInternalHandler(
                amazonSQS = sqsClientMock,
                configuration = configuration,
                ddaService = ddaServiceMock,
                queueName = "test",
                ddaCallbackService = null,
            )

            listOf(
                DDAExecutionResult.Added,
                DDAExecutionResult.Updated(BillSynchronizationStatus.BillAmountTotalUpdated),
                DDAExecutionResult.Error(),
                DDAExecutionResult.NotAdded(),
                DDAExecutionResult.AccountNotFound,
                DDAExecutionResult.NotUpdated(),
            ).forEach { result ->
                clearAllMocks()

                val message = mockk<Message> { every { body() } returns MESSAGE_BILL_1 }
                every { ddaServiceMock.executeBill(any(), any()) } returns result

                testHandler.handleMessage(message)

                verify {
                    ddaServiceMock.executeBill(any(), any())
                    ddaCallbackServiceMock wasNot Called
                }
            }
        }
    }

    private companion object {
        val mapper = jacksonObjectMapper()
            .registerModule(JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .disable(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE)
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)

        val MESSAGE_BILL_1 = """{
	"ddaProvider": "ARBI",
	"status": {
        "notPayable": false,
        "alreadyPaid": false
    },
	"bill": {
		"barCode": {
			"number": "836800000009857900531073096493867111101007954817",
			"digitable": "836800000009985790053104730964938676111101007958"
		},
		"billType": "CONCESSIONARIA",
		"assignor": "BCO BRADESCO S.A.",
		"recipient": {
			"name": "Galvão",
			"document": "11111111111",
			"alias": ""
		},
		"recipientChain": {
			"sacadorAvalista": {
				"name": "Galvão",
				"document": "11111111111",
				"alias": ""
			},
			"originalBeneficiary": {
				"name": "Galvão Beneficiary",
				"document": "00000000001",
				"alias": ""
			},
			"finalBeneficiary": {
				"name": "Galvão Final",
				"document": "00000000002",
				"alias": ""
			}
		},
		"payerDocument": "08845856704",
		"amount": 10050,
		"discount": 202,
		"interest": 1099,
		"fine": 199,
		"amountTotal": 1500,
		"expirationDate": "2022-10-01",
		"dueDate": "${LocalDate.now().minusWeeks(1)}",
		"paymentLimitTime": "20:00",
		"settleDate": "2022-09-05",
		"fichaCompensacaoType": "CH_CHEQUE",
		"payerName": "Globo",
		"amountCalculationModel": "ANYONE",
		"interestData": {
			"type": "VALUE",
			"value": 0.04,
			"date": "2022-09-02"
		},
		"fineData": {
			"type": "PERCENT",
			"value": 15,
			"date": "2022-09-03"
		},
		"discountData": {
			"type": "FIXED_UNTIL_DATE",
			"value1": 15,
			"date1": "2022-08-15"
		},
		"discounts": [{
			"type": "FIXED_UNTIL_DATE",
			"value": 1500,
			"date": "2022-08-15"
		}],
		"amountPaid": 10050,
		"idNumber": "f0caa841-18fc-4585-8797-5c9b0495a386",
		"abatement": 100.5,
		"rebate": 10050
	}
}"""

        private val PARSED_MESSAGE_BILL_1: DDABillMessageTO =
            mapper.readValue(MESSAGE_BILL_1, DDABillMessageTO::class.java)

        val MESSAGE_BILL_WITHOUT_BARCODE: String = mapper.writeValueAsString(
            PARSED_MESSAGE_BILL_1.copy(
                bill = PARSED_MESSAGE_BILL_1.bill.copy(barCode = null),
            ),
        )

        val MESSAGE_BILL_WITHOUT_PAYER: String = mapper.writeValueAsString(
            PARSED_MESSAGE_BILL_1.copy(
                bill = PARSED_MESSAGE_BILL_1.bill.copy(payerDocument = null),
            ),
        )

        val MESSAGE_BILL_WITHOUT_DUEDATE: String = mapper.writeValueAsString(
            PARSED_MESSAGE_BILL_1.copy(
                bill = PARSED_MESSAGE_BILL_1.bill.copy(dueDate = null),
            ),
        )
    }
}