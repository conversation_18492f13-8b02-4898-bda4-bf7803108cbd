package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.getPaidBill
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.WalletFixture
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message

class SQSBillPaidActivityHandlerTest {

    private val systemActivityService: SystemActivityService = mockk(relaxed = true)
    private val billRepository: BillRepository = mockk()
    private val walletService: WalletService = mockk()

    val accountId = AccountId(ACCOUNT_ID)
    val bill = getPaidBill(walletId = billPaid.walletId, accountId = accountId)

    private val walletFixture = WalletFixture(
        founderAccountId = accountId,
        defaultWalletId = billPaid.walletId,
    )
    private val wallet = walletFixture.buildWallet(id = billPaid.walletId)

    private val configurationMock: SQSMessageHandlerConfiguration = mockk() {
        every { billPaidActivityQueueName } returns "queue-name"
    }

    private val handler = SQSBillPaidActivityHandler(
        amazonSQS = mockk(),
        amazonSNS = mockk(),
        configuration = configurationMock,
        topicArn = "topic-name",
        systemActivityService = systemActivityService,
        walletService = walletService,
        billRepository = billRepository,
        userJourneyService = mockk(relaxed = true),
    )

    private val message =
        Message.builder().body(getObjectMapper().writeValueAsString(billPaid.toBillEventDetailEntity()))
            .build()

    @BeforeEach
    fun setup() {
        every {
            walletService.findWallet(billPaid.walletId)
        } returns wallet

        every {
            billRepository.findBill(billPaid.billId, billPaid.walletId)
        } returns bill.copy(source = ActionSource.DDA(accountId))
    }

    @Test
    fun `quando receber uma mensagem de conta paga na propria carteira, deve registar a atividade de BillPaidOnOwnWallet`() {
        handler.handleMessage(message).shouldDeleteMessage shouldBe true

        verify {
            systemActivityService.setBillPaidOnOwnWallet(accountId)
        }
    }

    @Test
    fun `quando receber uma mensagem de conta paga recebida via DDA, deve registrar a atividade de DDABillPaid`() {
        handler.handleMessage(message).shouldDeleteMessage shouldBe true

        verify {
            systemActivityService.setDDABillPaid(accountId)
        }
    }
}