package ai.friday.billpayment.adapters.messaging.pix

import ai.friday.billpayment.adapters.arbi.ArbiPixKeyAddException
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.fingerprint.DeviceId
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.PixKeyRepository
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyAlreadyExistsException
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.RegisterPixKeyCommand
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.integration.WalletFixture
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message

class RegisterPixKeyHandlerTest {

    private val pixKeyManagementMock = mockk<PixKeyManagement>()

    private val pixKeyRepository: PixKeyRepository = mockk {
        every {
            create(any(), any(), any())
        } returns Unit
    }

    private val wallet = WalletFixture().buildWallet()

    private val mockedDeviceId = DeviceId("131231")

    private val walletId = WalletId("123456")

    private val registerPixCommand = RegisterPixKeyCommand(
        accountNo = AccountNumber("000000"),
        key = PixKey(value = "***********", type = PixKeyType.CPF),
        document = "***********",
        name = "fulano",
        walletId = walletId,
    )

    private val deviceFingerprintServiceMock = mockk<DeviceFingerprintService> {
        every { getOrNull(wallet.founder.accountId) } returns null
        every { withRealOrTemporaryDeviceId(any(), any(), ofType<(DeviceId) -> Any>()) } answers {
            thirdArg<(DeviceId) -> Any>().invoke(mockedDeviceId)
        }
    }

    private val walletServiceMock = mockk<WalletService> {
        every { findWallet(any()) } returns wallet
    }

    private val registerPixKeyHandler = RegisterPixKeyHandler(
        amazonSQS = mockk(),
        configuration = mockk(relaxed = true),
        pixKeyManagement = pixKeyManagementMock,
        pixKeyRepository = pixKeyRepository,
        walletService = walletServiceMock,
        deviceFingerprintService = deviceFingerprintServiceMock,
    )

    private val message = Message.builder()
        .body(getObjectMapper().writeValueAsString(registerPixCommand))
        .build()

    private val messageEvpPixKey = Message.builder()
        .body(getObjectMapper().writeValueAsString(registerPixCommand.copy(walletId = walletId, key = PixKey(value = "", type = PixKeyType.EVP))))
        .build()

    @Test
    fun `deve cadastrar a chave pix e deletar da fila`() {
        every { pixKeyManagementMock.registerKey(any(), any(), any(), any(), any()) } returns PixKey(value = "***********", type = PixKeyType.CPF)

        val result = registerPixKeyHandler.handleMessage(message)

        verify(exactly = 0) {
            pixKeyRepository.create(any(), any(), any())
        }

        result shouldBe SQSHandlerResponse(true)
    }

    @Test
    fun `deve cadastrar a chave aleatória e deletar da fila`() {
        val expectedDeviceId = DeviceId("131231")

        every { pixKeyManagementMock.registerKey(any(), any(), any(), any(), any()) } returns PixKey(value = "***********", type = PixKeyType.EVP)
        every { walletServiceMock.findWallet(any()) } returns wallet
        every { deviceFingerprintServiceMock.getOrNull(wallet.founder.accountId) } returns mockk {
            every { deviceIds } returns mapOf(registerPixCommand.accountNo to expectedDeviceId)
        }

        val result = registerPixKeyHandler.handleMessage(messageEvpPixKey)

        verify(exactly = 1) {
            pixKeyManagementMock.registerKey(any(), any(), any(), any(), expectedDeviceId)
            pixKeyRepository.create(any(), any(), any())
        }

        result shouldBe SQSHandlerResponse(true)
    }

    @Test
    fun `se a chave já estiver cadastrada, deve remover da fila`() {
        every { pixKeyManagementMock.registerKey(any(), any(), any(), any(), any()) } throws PixKeyAlreadyExistsException()

        val result = registerPixKeyHandler.handleMessage(message)

        result shouldBe SQSHandlerResponse(true)
    }

    @Test
    fun `se der um erro, não deve remover da fila`() {
        every { pixKeyManagementMock.registerKey(any(), any(), any(), any(), any()) } throws ArbiPixKeyAddException()

        val result = registerPixKeyHandler.handleMessage(message)

        result shouldBe SQSHandlerResponse(false)
    }

    @Test
    fun `quando não há um deviceId cadastrado, deve criar um temporario para cadastrar a chave pix`() {
        every { pixKeyManagementMock.registerKey(any(), any(), any(), any(), any()) } returns PixKey(value = "***********", type = PixKeyType.EVP)

        registerPixKeyHandler.handleMessage(messageEvpPixKey).shouldDeleteMessage.shouldBeTrue()

        verify {
            deviceFingerprintServiceMock.withRealOrTemporaryDeviceId(wallet.founder.accountId, walletId, ofType<(DeviceId) -> Any>())
        }
    }
}