package ai.friday.billpayment.adapters.messaging

import DynamoDBUtils
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.SystemActivityDbRepository
import ai.friday.billpayment.adapters.dynamodb.SystemActivityDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.getPaidBill
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.DOCUMENT_2
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_BARCODE
import arrow.core.right
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message

internal class SQSSystemActivityBillPaidHandlerTest {

    private val enhancedClient = DynamoDBUtils.setupDynamoDB()

    val systemActivityService = SystemActivityService(systemActivityRepository = SystemActivityDbRepository(SystemActivityDynamoDAO(enhancedClient)))

    private val billRepository: BillRepository = mockk()

    val accountId = AccountId(ACCOUNT_ID)
    val bill = getPaidBill(
        billId = billPaid.billId,
        walletId = billPaid.walletId,
        accountId = accountId,
        subscriptionFee = true,
        source = ActionSource.SubscriptionRecurrence(
            accountId = accountId,
            recurrenceId = RecurrenceId("FOO"),
        ),
    )

    private val accountService: AccountService = mockk {
        every {
            findAccountById(AccountId(ACCOUNT_ID))
        } returns ACCOUNT
        every {
            enableCreditCardUsage(any(), any(), any())
        } returns ACCOUNT.right()
    }

    private val message = mockk<Message> {
        every {
            body()
        } returns getObjectMapper().writeValueAsString(billPaid.toBillEventDetailEntity())
    }

    private val handler = SQSSystemActivityBillPaidHandler(
        amazonSQS = mockk(),
        amazonSNS = mockk(),
        configuration = mockk(),
        topicArn = "topic",
        queueName = "queue",
        systemActivityService = systemActivityService,
        billRepository = billRepository,
        accountService = accountService,
    )

    @Test
    fun `deve salvar o system activity HasPaidOwnTrustedBill de conta do proprio CPF que não seja cartão de crédito nem boleto de deposito`() {
        every {
            billRepository.findBill(billPaid.billId, billPaid.walletId)
        } returns bill.copy(
            billType = BillType.FICHA_COMPENSACAO,
            barCode = BarCode.of(FICHA_DE_COMPENSACAO_BARCODE),
            source = ActionSource.Api(accountId = AccountId(ACCOUNT_ID)),
            fichaCompensacaoType = FichaCompensacaoType.BOLETO_PROPOSTA,
            payerDocument = DOCUMENT,
        )

        val response = handler.handleMessage(message)

        systemActivityService.getFirstOwnTrustedBillPaid(AccountId(ACCOUNT_ID))
            .shouldNotBeNull() // TODO - validar o timestamp
        response.shouldDeleteMessage shouldBe true
    }

    @Test
    fun `nao deve salvar o system activity HasPaidOwnTrustedBill do proprio CPF que seja boleto de deposito`() {
        every {
            billRepository.findBill(billPaid.billId, billPaid.walletId)
        } returns bill.copy(
            billType = BillType.FICHA_COMPENSACAO,
            barCode = BarCode.of(FICHA_DE_COMPENSACAO_BARCODE),
            source = ActionSource.Api(accountId = AccountId(ACCOUNT_ID)),
            fichaCompensacaoType = FichaCompensacaoType.DEPOSITO_E_APORTE,
            payerDocument = DOCUMENT,
        )

        val response = handler.handleMessage(message)

        systemActivityService.getFirstOwnTrustedBillPaid(AccountId(ACCOUNT_ID)).shouldBeNull()
        response.shouldDeleteMessage shouldBe true
    }

    @Test
    fun `nao deve salvar o system activity HasPaidOwnTrustedBill de conta do proprio CPF que seja cartão de crédito`() {
        every {
            billRepository.findBill(billPaid.billId, billPaid.walletId)
        } returns bill.copy(
            billType = BillType.FICHA_COMPENSACAO,
            barCode = BarCode.of(FICHA_DE_COMPENSACAO_BARCODE),
            source = ActionSource.Api(accountId = AccountId(ACCOUNT_ID)),
            fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO,
            payerDocument = DOCUMENT,
        )

        val response = handler.handleMessage(message)

        systemActivityService.getFirstOwnTrustedBillPaid(AccountId(ACCOUNT_ID)).shouldBeNull()
        response.shouldDeleteMessage shouldBe true
    }

    @Test
    fun `nao deve salvar o system activity HasPaidOwnTrustedBill de conta de outro CPF`() {
        every {
            billRepository.findBill(billPaid.billId, billPaid.walletId)
        } returns bill.copy(
            billType = BillType.FICHA_COMPENSACAO,
            barCode = BarCode.of(FICHA_DE_COMPENSACAO_BARCODE),
            source = ActionSource.Api(accountId = AccountId(ACCOUNT_ID)),
            fichaCompensacaoType = FichaCompensacaoType.BOLETO_PROPOSTA,
            payerDocument = DOCUMENT_2,
        )

        val response = handler.handleMessage(message)

        systemActivityService.getFirstOwnTrustedBillPaid(AccountId(ACCOUNT_ID)).shouldBeNull()
        response.shouldDeleteMessage shouldBe true
    }
}