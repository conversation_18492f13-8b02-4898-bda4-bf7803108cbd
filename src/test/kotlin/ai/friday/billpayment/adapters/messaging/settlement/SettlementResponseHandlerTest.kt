package ai.friday.billpayment.adapters.messaging.settlement

import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.payment.BillPaymentService
import ai.friday.billpayment.app.payment.BoletoSettlementResult
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.integration.DIGITABLE_LINE
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.shouldBe
import io.mockk.mockk
import io.mockk.verify
import java.time.format.DateTimeFormatter
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import software.amazon.awssdk.services.sqs.model.Message

class SettlementResponseHandlerTest {

    private val billPaymentService: BillPaymentService = mockk(relaxUnitFun = true)

    private val settlementResponseHandler = SettlementResponseHandler(
        amazonSQS = mockk(),
        configuration = mockk(relaxed = true),
        billPaymentService = billPaymentService,
    )

    private val paidSettlementClientResponse = """
        {
          "transactionId": "123",
          "barcode": "23610000001559102962019091003800000237307261",
          "digitableLine": "236100000013559102962011909100380001002373072615",
          "authorization": "1234",
          "timestamp": "2023-08-10T18:52:10.168133-03:00",
          "financialServiceGateway": "ARBI",
          "settlementFinancialInstitution": "Banco Bradesco S/A",
          "status": "PAID"
        }
    """.trimIndent()

    @Test
    fun `quando chegar uma resposta de liquidacao com sucesso deve continuar a liquidacao como CONFIRMED`() {
        val message = Message.builder().body(paidSettlementClientResponse).build()

        val response = settlementResponseHandler.handleMessage(message)

        verify {
            billPaymentService.continueSettlement(
                TransactionId("123"),
                BoletoSettlementResult(
                    gateway = FinancialServiceGateway.FRIDAY,
                    status = BoletoSettlementStatus.CONFIRMED,
                    bankTransactionId = "123",
                    externalNsu = 0,
                    externalTerminal = "",
                    errorCode = "",
                    errorDescription = null,
                    authentication = "1234",
                    paymentPartnerName = "Banco Bradesco S/A",
                    finalPartnerName = FinancialServiceGateway.ARBI,
                ),
            )
        }

        response shouldBe SQSHandlerResponse(true)
    }

    @ParameterizedTest
    @EnumSource(SettlementErrorCode::class, mode = EnumSource.Mode.EXCLUDE, names = ["BEFORE_HOURS"])
    fun `quando chegar uma resposta de liquidacao com falha deve continuar a liquidacao como VOIDED`(errorCode: SettlementErrorCode) {
        val barCode = BarCode.ofDigitable(DIGITABLE_LINE)

        val responseTO = FailedSettlementClientResponseTO(
            transactionId = "123",
            barcode = barCode.number,
            digitableLine = barCode.digitable,
            timestamp = getZonedDateTime().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME),
            financialServiceGateway = FinancialServiceGateway.CELCOIN,
            errorMessage = "Falhou",
            errorCode = errorCode,
        )

        val message = Message.builder().body(getObjectMapper().writeValueAsString(responseTO)).build()

        val response = settlementResponseHandler.handleMessage(message)

        verify {
            billPaymentService.continueSettlement(
                TransactionId(responseTO.transactionId),
                BoletoSettlementResult(
                    gateway = FinancialServiceGateway.FRIDAY,
                    status = BoletoSettlementStatus.VOIDED,
                    bankTransactionId = responseTO.transactionId,
                    externalNsu = 0,
                    externalTerminal = "",
                    errorCode = BoletoSettlementStatus.VOIDED.name,
                    errorDescription = responseTO.errorMessage,
                    finalPartnerName = responseTO.financialServiceGateway,
                ),
            )
        }

        response shouldBe SQSHandlerResponse(true)
    }

    @ParameterizedTest
    @EnumSource(SettlementErrorCode::class, mode = EnumSource.Mode.EXCLUDE, names = ["AFTER_HOURS", "OTHER"])
    fun `quando chegar uma resposta de requisicao com falha retentável deve retornar UNAUTHORIZED`(errorCode: SettlementErrorCode) {
        val barCode = BarCode.ofDigitable(DIGITABLE_LINE)

        val responseTO = FailedSettlementClientResponseTO(
            transactionId = "123",
            barcode = barCode.number,
            digitableLine = barCode.digitable,
            timestamp = getZonedDateTime().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME),
            financialServiceGateway = FinancialServiceGateway.CELCOIN,
            errorMessage = "Falhou",
            errorCode = errorCode,
        )

        val message = Message.builder().body(getObjectMapper().writeValueAsString(responseTO)).build()

        val response = settlementResponseHandler.handleMessage(message)

        verify {
            billPaymentService.continueSettlement(
                TransactionId(responseTO.transactionId),
                BoletoSettlementResult(
                    gateway = FinancialServiceGateway.FRIDAY,
                    status = BoletoSettlementStatus.UNAUTHORIZED,
                    bankTransactionId = responseTO.transactionId,
                    externalNsu = 0,
                    externalTerminal = "",
                    errorCode = BoletoSettlementStatus.UNAUTHORIZED.name,
                    errorDescription = responseTO.errorMessage,
                    finalPartnerName = responseTO.financialServiceGateway,
                ),
            )
        }

        response shouldBe SQSHandlerResponse(true)
    }

    @Test
    fun `quando chegar uma resposta de requisicao invalida deve continuar a liquidacao como UNAUTHORIZED`() {
        val responseTO = BadRequestSettlementClientResponseTO(
            transactionId = "123456",
            timestamp = getZonedDateTime().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME),
            errorMessage = "Requisição inválida",
        )

        val message = Message.builder().body(getObjectMapper().writeValueAsString(responseTO)).build()

        val response = settlementResponseHandler.handleMessage(message)

        verify {
            billPaymentService.continueSettlement(
                TransactionId(responseTO.transactionId),
                BoletoSettlementResult(
                    gateway = FinancialServiceGateway.FRIDAY,
                    status = BoletoSettlementStatus.UNAUTHORIZED,
                    bankTransactionId = responseTO.transactionId,
                    externalNsu = 0,
                    externalTerminal = "",
                    errorCode = BoletoSettlementStatus.UNAUTHORIZED.name,
                    errorDescription = responseTO.errorMessage,
                    finalPartnerName = null,
                ),
            )
        }

        response shouldBe SQSHandlerResponse(true)
    }
}