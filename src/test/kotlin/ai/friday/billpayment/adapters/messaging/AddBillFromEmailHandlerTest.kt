package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.adapters.s3.S3ObjectRepository
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillValidationException
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.MailboxAddBillError
import ai.friday.billpayment.app.bill.mailbox.MailboxSecurityValidationErrorLevel
import ai.friday.billpayment.app.email.AddBoletoFromEmailMessage
import ai.friday.billpayment.app.email.MailObjectService
import ai.friday.billpayment.app.integrations.MailBoxService
import ai.friday.billpayment.app.notification.EmailNotProcessedNotificationRequest
import ai.friday.billpayment.app.notification.InsecureBillNotificationRequest
import ai.friday.billpayment.app.notification.NotificationRouterService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.integration.DIGITABLE_LINE
import ai.friday.billpayment.integration.WalletFixture
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import io.via1.communicationcentre.app.integrations.EmailSenderService
import io.via1.communicationcentre.app.receipt.Action
import io.via1.communicationcentre.app.receipt.Receipt
import java.time.LocalDate
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message
import software.amazon.awssdk.services.sqs.model.MessageSystemAttributeName

class AddBillFromEmailHandlerTest {

    private val mailBoxServiceMock = mockk<MailBoxService>()

    private val s3ObjectRepositoryMock = mockk<S3ObjectRepository>(relaxed = true)

    private val notificationRouterServiceMock = mockk<NotificationRouterService>(relaxUnitFun = true)

    private val mailObjectServiceMock =
        MailObjectService(objectRepository = s3ObjectRepositoryMock, incomingEmailBuilder = mockk(relaxed = true))

    private val emailSenderServiceMock = mockk<EmailSenderService>(relaxed = true)

    val walletFixture =
        WalletFixture(founderAccountId = ACCOUNT.accountId, defaultWalletId = ACCOUNT.defaultWalletId())
    val wallet = walletFixture.buildWallet(
        otherMembers = listOf(
            walletFixture.participant,
            walletFixture.ultraLimitedParticipant,
        ),
    )

    private val accountServiceMock = mockk<AccountService>(relaxed = true) {
        every { findAccountById(any()) } returns ACCOUNT
    }

    private val walletServiceMock = mockk<WalletService>() {
        every { findWallet(wallet.id) } returns wallet
    }

    private val handler = AddBillFromEmailHandler(
        amazonSQS = mockk(),
        configuration = mockk(relaxed = true),
        mailBoxService = mailBoxServiceMock,
        notificationRouterService = notificationRouterServiceMock,
        mailObjectService = mailObjectServiceMock,
        walletService = walletServiceMock,
        emailSenderService = emailSenderServiceMock,
        manualWorkflowEmail = "teste",
        forwardEmailToManualWorkflow = true,
        accountService = accountServiceMock,
        addBillEmailMaxReceiveAttempts = 10,
    )

    val action =
        Action("S3", "", "via1-incoming-emails", null, "somekey")
    val receipt = Receipt(null, null, null, null, null, null, null, null, null, action)

    @Test
    fun `quando a validação de segurança do email falha, não deve adicionar e deve notificar o usuário`() {
        val message = buildMessage()

        every {
            mailBoxServiceMock.addBill(any(), any(), any(), any(), any(), any(), any())
        } answers {
            MailboxAddBillError.MailboxSecurityValidationError(
                reason = "Test",
                level = MailboxSecurityValidationErrorLevel.BLOCK,
            ).left()
        }

        handler.handleMessage(message).shouldDeleteMessage.shouldBeTrue()

        verify {
            notificationRouterServiceMock.routeNotification(
                withArg<InsecureBillNotificationRequest> {
                    it.members shouldBe listOf(walletFixture.founder)
                },
            )
        }
    }

    @Test
    fun `quando a validação de boleto retorna um erro final, não deve adicionar, não deve retentar e não deve jogar para o fluxo manual`() {
        val message = buildMessage()

        every {
            mailBoxServiceMock.addBill(any(), any(), any(), any(), any(), any(), shouldNotifyOnRetryableError = false)
        } answers {
            MailboxAddBillError.MailboxAddInvalidBill(
                result = CreateBillResult.FAILURE.BillAlreadyExists(mockk()),
                isRetryable = false,
            ).left()
        }

        handler.handleMessage(message).shouldDeleteMessage.shouldBeTrue()
    }

    @Test
    fun `quando a validação de boleto retorna um erro de conta não encontrada, não deve adicionar, não deve retentar e não deve jogar para o fluxo manual`() {
        val message = buildMessage()

        every {
            mailBoxServiceMock.addBill(any(), any(), any(), any(), any(), any(), shouldNotifyOnRetryableError = false)
        } answers {
            MailboxAddBillError.MailboxAccountNotFoundError().left()
        }

        handler.handleMessage(message).shouldDeleteMessage.shouldBeTrue()
    }

    @Test
    fun `se o pagamento for adicionado com sucesso deve mover para processado e remover da fila`() {
        val message = buildMessage()

        every {
            mailBoxServiceMock.addBill(any(), any(), any(), any(), any(), any(), shouldNotifyOnRetryableError = false)
        } answers {
            Unit.right()
        }

        handler.handleMessage(message).shouldDeleteMessage.shouldBeTrue()
    }

    @Test
    fun `quando a validação de boleto retorna um erro retentável e tem mais tentativas, não deve adicionar, não deve remover da fila, não deve notificar e não deve jogar para o fluxo manual`() {
        val message = buildMessage()

        every {
            mailBoxServiceMock.addBill(any(), any(), any(), any(), any(), any(), shouldNotifyOnRetryableError = false)
        } answers {
            MailboxAddBillError.MailboxAddInvalidBill(result = mockk(relaxed = true), isRetryable = true).left()
        }

        handler.handleMessage(message).shouldDeleteMessage.shouldBeFalse()

        verify(exactly = 0) {
            s3ObjectRepositoryMock.moveObject(any(), any(), any(), any())
            s3ObjectRepositoryMock.copyObject(any(), any(), any(), any())
        }
        verify {
            notificationRouterServiceMock wasNot called
        }
    }

    @Test
    fun `quando a validação do boleto retorna um erro retentável e não possui mais tentativas, não deve adicionar, deve remover da fila, deve notificar e jogar para o fluxo manual`() {
        val message = buildMessage(count = "10")

        every {
            mailBoxServiceMock.addBill(any(), any(), any(), any(), any(), any(), shouldNotifyOnRetryableError = true)
        } answers {
            MailboxAddBillError.MailboxAddInvalidBill(result = mockk(relaxed = true), isRetryable = true).left()
        }

        handler.handleMessage(message).shouldDeleteMessage.shouldBeTrue()

        verify {
            emailSenderServiceMock.forward(any(), any())
        }

        val slot = slot<EmailNotProcessedNotificationRequest>()

        verify {
            notificationRouterServiceMock.routeNotification(
                capture(slot),
            )
        }

        slot.captured.members shouldBe wallet.activeMembers
    }

    @Test
    fun `quando a validação do boleto retorna um server error indicando que nao conseguiu validar, não deve adicionar, deve remover da fila, deve notificar e jogar para o fluxo manual`() {
        val message = buildMessage(count = "10")

        every {
            mailBoxServiceMock.addBill(any(), any(), any(), any(), any(), any(), shouldNotifyOnRetryableError = true)
        } answers {
            MailboxAddBillError.MailboxAddInvalidBill(result = CreateBillResult.FAILURE.ServerError(BillValidationException("")), isRetryable = false).left()
        }

        handler.handleMessage(message).shouldDeleteMessage.shouldBeTrue()

        verify {
            emailSenderServiceMock.forward(any(), any())
        }

        val slot = slot<EmailNotProcessedNotificationRequest>()

        verify {
            notificationRouterServiceMock.routeNotification(
                capture(slot),
            )
        }

        slot.captured.members shouldBe wallet.activeMembers
    }

    @Test
    fun `quando é uma conta de concessionaria sem data de vencimento não deve adicionar, deve remover da fila, deve notificar e jogar para o fluxo manual`() {
        val message = buildMessage(count = "10")

        every {
            mailBoxServiceMock.addBill(any(), any(), any(), any(), any(), any(), shouldNotifyOnRetryableError = true)
        } answers {
            MailboxAddBillError.MailBoxAddInvalidRequest(message = "some message").left()
        }

        handler.handleMessage(message).shouldDeleteMessage.shouldBeTrue()

        verify {
            emailSenderServiceMock.forward(any(), any())
        }

        val slot = slot<EmailNotProcessedNotificationRequest>()

        verify {
            notificationRouterServiceMock.routeNotification(
                capture(slot),
            )
        }

        slot.captured.members shouldBe wallet.activeMembers
    }

    private fun buildMessage(count: String? = "1"): Message {
        val body = AddBoletoFromEmailMessage(
            walletId = wallet.id,
            barCode = BarCode.ofDigitable(DIGITABLE_LINE),
            dueDate = LocalDate.parse("2023-04-14"),
            from = "",
            subject = "subject",
            receipt = receipt,
            accountId = ACCOUNT.accountId,
            emailDestinationPath = "",
        )

        return Message.builder()
            .attributes(mapOf(MessageSystemAttributeName.APPROXIMATE_RECEIVE_COUNT to count))
            .body(getObjectMapper().writeValueAsString(body)).build()
    }
}