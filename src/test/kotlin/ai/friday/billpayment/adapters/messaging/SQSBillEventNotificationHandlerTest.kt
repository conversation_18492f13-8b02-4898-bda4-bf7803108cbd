package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.AmountUpdated
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillPermissionUpdated
import ai.friday.billpayment.app.bill.FichaCompensacaoAdded
import ai.friday.billpayment.app.bill.PermissionUpdated
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.bill.SchedulePostponedReason
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.message.BillEventNotificationResult
import ai.friday.billpayment.app.message.EventNotificationService
import ai.friday.billpayment.app.notification.NotifyExpiredPaymentSchedule
import ai.friday.billpayment.app.notification.NotifyPaymentCanceledDueToAmountChange
import ai.friday.billpayment.app.payment.DiscountData
import ai.friday.billpayment.app.payment.FineData
import ai.friday.billpayment.app.payment.InterestData
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentFailedRetryable
import ai.friday.billpayment.billPaymentSchedulePostponedByInsufficientFunds
import ai.friday.billpayment.billPaymentSchedulePostponedDueToLimitReached
import ai.friday.billpayment.billPaymentScheduleStarted
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.billScheduleCanceled
import ai.friday.billpayment.billScheduleCanceledAmountChanged
import ai.friday.billpayment.billScheduleCanceledAmountHigherThanDailyLimit
import ai.friday.billpayment.billScheduleCanceledCantPayWithCurrentCreditCard
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.invoicePaymentScheduled
import ai.friday.billpayment.invoicePaymentStarted
import ai.friday.billpayment.invoiceScheduleCanceled
import ai.friday.billpayment.invoiceScheduleStarted
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.pixKeyAdded
import ai.friday.billpayment.pixKeyPaymentCanceled
import ai.friday.billpayment.pixKeyPaymentFailed
import ai.friday.billpayment.pixKeyPaymentScheduled
import ai.friday.billpayment.pixPaymentScheduled
import ai.friday.billpayment.pixPaymentScheduledCanceled
import ai.friday.billpayment.pixPaymentStarted
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.util.stream.Stream
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import software.amazon.awssdk.services.sqs.model.Message

class SQSBillEventNotificationHandlerTest {

    private val bill = Bill.build(billAdded, billPaymentScheduled, billScheduleCanceled)

    private val notificationAdapter: NotificationAdapter = mockk(relaxUnitFun = true)
    private val billEventRepository: BillEventRepository = mockk {
        every { getBillById(billAdded.billId) } returns Either.Right(bill)
    }
    private val walletService: WalletService = mockk {
        every { findWalletOrNull(any()) } returns null
    }

    private val billAddedNotificationService = mockk<EventNotificationService<BillAdded>> {
        every { notify(any()) } returns BillEventNotificationResult.Success
    }
    private val fichaAddedNotificationService = mockk<EventNotificationService<FichaCompensacaoAdded>> {
        every { notify(any()) } returns BillEventNotificationResult.Success
    }

    private val defaultNotificationConfiguration = BillEventNotificationConfiguration().apply {
        notifyBillAdded = true
        notifyScheduleBillNotPayable = true
        notifyScheduledBillExpired = true
        notifyScheduleBillPostponedDueLimitReached = true
        notifyScheduleBillCanceledDueAmountHigherThanDailyLimit = true
        notifyScheduleBillCanceledDueAmountChanged = true
        notifyScheduleBillCanceledDueCreditCardDenied = true
        notifyNotVisibleBillAlreadyExists = true
    }

    private val restrictedNotificationConfiguration = BillEventNotificationConfiguration().apply {
        notifyBillAdded = false
        notifyScheduleBillNotPayable = false
        notifyScheduledBillExpired = false
        notifyScheduleBillPostponedDueLimitReached = false
        notifyScheduleBillCanceledDueAmountHigherThanDailyLimit = false
        notifyScheduleBillCanceledDueAmountChanged = false
        notifyScheduleBillCanceledDueCreditCardDenied = false
        notifyNotVisibleBillAlreadyExists = false
    }

    private val notifyExpiredPaymentSchedule = mockk<NotifyExpiredPaymentSchedule>(relaxed = true)

    private val notifyPaymentCanceledDueToAmountChange = mockk<NotifyPaymentCanceledDueToAmountChange>(relaxed = true)

    private val billEventNotificationHandler = SQSBillEventNotificationHandler(
        amazonSQS = mockk(relaxed = true),
        amazonSNS = mockk(relaxed = true),
        configuration = mockk(relaxed = true),
        notificationAdapter = notificationAdapter,
        billEventRepository = billEventRepository,
        walletService = walletService,
        topicArn = "",
        notifyExpiredPaymentSchedule = notifyExpiredPaymentSchedule,
        notificationConfiguration = defaultNotificationConfiguration,
        notifyPaymentCanceledDueToAmountChange = notifyPaymentCanceledDueToAmountChange,
        billAddedNotificationService = billAddedNotificationService,
        fichaAddedNotificationService = fichaAddedNotificationService,
    )

    @Test
    fun `should not notify billPaid`() {
        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(billPaid.toBillEventDetailEntity())).build()

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true
        verify {
            notificationAdapter wasNot called
        }
    }

    @ParameterizedTest
    @MethodSource("notifiableBillEvents")
    fun `should not notify event when notify configuration is false`(event: BillEvent) {
        val restrictedNotificationHandler = SQSBillEventNotificationHandler(
            amazonSQS = mockk(relaxed = true),
            amazonSNS = mockk(relaxed = true),
            configuration = mockk(relaxed = true),
            notificationAdapter = notificationAdapter,
            billEventRepository = billEventRepository,
            walletService = walletService,
            topicArn = "",
            notificationConfiguration = restrictedNotificationConfiguration,
            notifyExpiredPaymentSchedule = notifyExpiredPaymentSchedule,
            notifyPaymentCanceledDueToAmountChange = notifyPaymentCanceledDueToAmountChange,
            billAddedNotificationService = billAddedNotificationService,
            fichaAddedNotificationService = fichaAddedNotificationService,
        )
        every { billEventRepository.getBillById(any()) } returns Either.Right(
            Bill.build(
                billAdded.copy(
                    actionSource = ActionSource.DDA(
                        accountId = wallet.founder.accountId,
                    ),
                    document = wallet.founder.document,
                ),
            ),
        )
        every { walletService.findWallet(any()) } returns wallet
        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(event.toBillEventDetailEntity())).build()

        val response = restrictedNotificationHandler.handleMessage(message)
        response.shouldDeleteMessage shouldBe true
        verify {
            notificationAdapter wasNot called
        }
    }

    @Test
    fun `should notify transfer not payable when bill is a PIX on wallet`() {
        every { walletService.findWallet(any()) } returns wallet
        val message = Message.builder()
            .body(
                getObjectMapper().writeValueAsString(
                    pixPaymentScheduledCanceled.copy(walletId = wallet.id).toBillEventDetailEntity(),
                ),
            )
            .build()

        val pix = Bill.build(
            pixAdded.copy(
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            ),
            pixPaymentScheduled.copy(walletId = wallet.id),
            pixPaymentStarted.copy(walletId = wallet.id),
            pixPaymentScheduledCanceled.copy(walletId = wallet.id),
        )
        every { billEventRepository.getBillById(pixAdded.billId) } returns Either.Right(pix)

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true
        verify {
            notificationAdapter.notifyTransferNotPayable(
                members = listOf(walletFixture.founder, walletFixture.participant),
                recipientName = pix.recipient!!.name,
                dueDate = pix.effectiveDueDate,
                amount = pix.amountTotal,
                errorMessage = bankTransferNotPayableErrorMessage,
                author = walletFixture.founder,
                walletId = wallet.id,
                walletName = wallet.name,
                billId = pixAdded.billId,
            )
        }
    }

    @Test
    fun `deve notificar uma transferência com chave pix inválida`() {
        every { walletService.findWallet(any()) } returns wallet
        val message = Message.builder()
            .body(
                getObjectMapper().writeValueAsString(
                    pixKeyPaymentCanceled.copy(walletId = wallet.id).toBillEventDetailEntity(),
                ),
            )
            .build()

        val pix = Bill.build(
            pixKeyAdded.copy(
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            ),
            pixKeyPaymentScheduled.copy(walletId = wallet.id),
            pixKeyPaymentFailed.copy(walletId = wallet.id),
            pixKeyPaymentCanceled.copy(walletId = wallet.id),
        )
        every { billEventRepository.getBillById(pixKeyAdded.billId) } returns Either.Right(pix)

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true
        verify {
            notificationAdapter.notifyTransferNotPayable(
                members = listOf(walletFixture.founder, walletFixture.participant),
                recipientName = pix.recipient!!.name,
                dueDate = pix.effectiveDueDate,
                amount = pix.amountTotal,
                errorMessage = invalidPixKeyErrorMessage,
                author = walletFixture.founder,
                walletId = wallet.id,
                walletName = wallet.name,
                billId = pixKeyAdded.billId,
            )
        }
    }

    @Test
    fun `should notify transfer not payable when bill is an INVOICE on wallet`() {
        every { walletService.findWallet(any()) } returns wallet
        val message = Message.builder()
            .body(
                getObjectMapper().writeValueAsString(
                    invoiceScheduleCanceled.copy(reason = ScheduleCanceledReason.BILL_NOT_PAYABLE)
                        .toBillEventDetailEntity(),
                ),
            )
            .build()

        val invoice = Bill.build(
            invoiceAdded.copy(
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            ),
            invoicePaymentScheduled.copy(walletId = wallet.id),
            invoiceScheduleStarted.copy(walletId = wallet.id),
            invoicePaymentStarted.copy(walletId = wallet.id),
            invoiceScheduleCanceled.copy(
                walletId = wallet.id,
                reason = ScheduleCanceledReason.BILL_NOT_PAYABLE,
            ),
        )
        every { billEventRepository.getBillById(invoice.billId) } returns Either.Right(invoice)

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true
        verify {
            notificationAdapter.notifyTransferNotPayable(
                members = listOf(walletFixture.founder, walletFixture.participant),
                recipientName = invoice.recipient!!.name,
                dueDate = invoice.effectiveDueDate,
                amount = invoice.amountTotal,
                errorMessage = bankTransferNotPayableErrorMessage,
                author = walletFixture.founder,
                walletId = wallet.id,
                walletName = wallet.name,
                billId = invoice.billId,
            )
        }
    }

    @Test
    fun `should notify transfer not payable when bill is a scheduled BOLETO on wallet`() {
        every { walletService.findWallet(any()) } returns wallet
        val message = Message.builder()
            .body(
                getObjectMapper().writeValueAsString(
                    billScheduleCanceled.copy(reason = ScheduleCanceledReason.BILL_NOT_PAYABLE)
                        .toBillEventDetailEntity(),
                ),
            )
            .build()

        val bill = Bill.build(
            billAdded.copy(
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            ),
            billPaymentScheduled.copy(walletId = wallet.id),
            billPaymentScheduleStarted.copy(walletId = wallet.id),
            billPaymentStart.copy(walletId = wallet.id),
            billScheduleCanceled.copy(
                walletId = wallet.id,
                reason = ScheduleCanceledReason.BILL_NOT_PAYABLE,
            ),
        )
        every { billEventRepository.getBillById(bill.billId) } returns Either.Right(bill)

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true
        verify {
            notificationAdapter.notifyScheduledBillNotPayable(
                members = listOf(walletFixture.founder, walletFixture.participant),
                walletId = wallet.id,
                walletName = wallet.name,
                author = wallet.founder,
                payee = bill.getPayee(),
                dueDate = bill.effectiveDueDate,
                totalAmount = bill.amountTotal,
                description = scheduledBillNotPayableMessage,
                billId = bill.billId,
            )
        }
    }

    @Test
    fun `should not notify when bill payment cancel is user_canceled`() {
        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(billScheduleCanceled.toBillEventDetailEntity()))
                .build()

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true
        verify {
            notificationAdapter wasNot called
        }
    }

    @Test
    fun `should notify expiring scheduledBill on bill schedule canceled event with EXPIRED reason on wallet`() {
        every { walletService.findWallet(any()) } returns wallet
        val event = billScheduleCanceled.copy(reason = ScheduleCanceledReason.EXPIRATION)
        val message = Message.builder()
            .body(getObjectMapper().writeValueAsString(event.toBillEventDetailEntity())).build()

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true
        verify {
            notifyExpiredPaymentSchedule.notify(event)
        }
    }

    @Test
    fun `should call notifyBillCreated with DDA source on a wallet`() {
        every { walletService.findWallet(any()) } returns wallet
        val billEvent = billAddedFicha.copy(
            actionSource = ActionSource.DDA(accountId = wallet.founder.accountId),
            document = wallet.founder.document,
            fineData = FineData(),
            interestData = InterestData(),
            discountData = DiscountData(),
        )
        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(billEvent.toBillEventDetailEntity())).build()

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true

        verify { fichaAddedNotificationService.notify(billEvent) }
    }

    @ParameterizedTest
    @MethodSource("apiSourceBillAdded")
    fun `should notify collaborator when bill has API source`(billAdded: BillAdded) {
        every { walletService.findWallet(any()) } returns wallet
        val actionSource = ActionSource.Api(accountId = wallet.founder.accountId)
        val billEvent = billAdded.copy(actionSource = actionSource)
        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(billEvent.toBillEventDetailEntity())).build()

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true
        verify { billAddedNotificationService.notify(billEvent) }
    }

    @ParameterizedTest
    @MethodSource("apiSourceBillAdded")
    fun `should notify all members when bill has ConnectUtility source`(billAdded: BillAdded) {
        every { walletService.findWallet(any()) } returns wallet
        val actionSource = ActionSource.ConnectUtility(accountId = wallet.founder.accountId)
        val billEvent = billAdded.copy(actionSource = actionSource)
        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(billEvent.toBillEventDetailEntity())).build()

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true

        verify { billAddedNotificationService.notify(billEvent) }
    }

    @Test
    fun `should call notifyBillCreated with WalletMailbox source with unknown author`() {
        every { walletService.findWallet(any()) } returns wallet
        val actionSource = ActionSource.WalletMailBox(from = "<EMAIL>", accountId = null)
        val billEvent = billAdded.copy(actionSource = actionSource, description = "")
        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(billEvent.toBillEventDetailEntity())).build()

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true
        verify { billAddedNotificationService.notify(billEvent) }
    }

    @Test
    fun `should call notifyBillCreated with WalletMailbox source with known author`() {
        every { walletService.findWallet(any()) } returns wallet
        val actionSource =
            ActionSource.WalletMailBox(from = "<EMAIL>", accountId = walletFixture.participant.accountId)
        val billEvent = billAdded.copy(actionSource = actionSource, description = "")
        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(billEvent.toBillEventDetailEntity())).build()

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true

        verify { billAddedNotificationService.notify(billEvent) }
    }

    @ParameterizedTest
    @MethodSource("actionSourcesWithoutNotify")
    fun `should not call notifyBillCreated on action sources without notify`(actionSource: ActionSource) {
        val message =
            Message.builder().body(
                getObjectMapper().writeValueAsString(
                    billAdded.copy(actionSource = actionSource).toBillEventDetailEntity(),
                ),
            )
                .build()

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true
        verify {
            notificationAdapter wasNot Called
        }
    }

    @Test
    fun `should notify when postponed with LIMIT_REACHED reason`() {
        every { walletService.findWallet(any()) } returns wallet

        val message =
            Message.builder()
                .body(getObjectMapper().writeValueAsString(billPaymentSchedulePostponedDueToLimitReached.toBillEventDetailEntity()))
                .build()

        val bill = Bill.build(
            billAdded.copy(
                billId = billPaymentSchedulePostponedDueToLimitReached.billId,
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            ),
            billPaymentScheduled.copy(
                billId = billPaymentSchedulePostponedDueToLimitReached.billId,
                walletId = wallet.id,
            ),
            billPaymentSchedulePostponedDueToLimitReached,
        )

        every { billEventRepository.getBillById(bill.billId) } returns Either.Right(bill)

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true
        verify {
            notificationAdapter.notifyBillSchedulePostponedDueLimitReached(
                members = listOf(walletFixture.founder, walletFixture.participant),
                walletId = wallet.id,
                walletName = wallet.name,
                payee = bill.getPayee(),
                totalAmount = bill.amountTotal,
                type = bill.billType,
                author = any(),
            )
        }
    }

    @Test
    fun `should notify when postponed with MONTHLY_LIMIT_REACHED reason`() {
        every { walletService.findWallet(any()) } returns wallet

        val event =
            billPaymentSchedulePostponedDueToLimitReached.copy(reason = SchedulePostponedReason.MONTHLY_LIMIT_REACHED)
        val message =
            Message.builder()
                .body(
                    getObjectMapper().writeValueAsString(
                        event
                            .toBillEventDetailEntity(),
                    ),
                )
                .build()

        val bill = Bill.build(
            billAdded.copy(
                billId = event.billId,
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            ),
            billPaymentScheduled.copy(
                billId = event.billId,
                walletId = wallet.id,
            ),
            event,
        )

        every { billEventRepository.getBillById(bill.billId) } returns Either.Right(bill)

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true
        verify {
            notificationAdapter.notifyBillSchedulePostponedDueLimitReached(
                members = listOf(walletFixture.founder, walletFixture.participant),
                walletId = wallet.id,
                walletName = wallet.name,
                payee = bill.getPayee(),
                totalAmount = bill.amountTotal,
                type = bill.billType,
                author = any(),
            )
        }
    }

    @Test
    fun `should not notify when postponed with INSUFFICIENT_FUNDS reason`() {
        every { walletService.findWalletOrNull(any()) } returns wallet

        val message = Message.builder().body(
            getObjectMapper().writeValueAsString(
                billPaymentSchedulePostponedByInsufficientFunds.toBillEventDetailEntity(),
            ),
        ).build()

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true
        verify(exactly = 0) {
            notificationAdapter.notifyBillSchedulePostponedDueLimitReached(
                members = any(),
                walletId = any(),
                walletName = any(),
                payee = any(),
                totalAmount = any(),
                type = any(),
                author = any(),
            )
        }
    }

    @Test
    fun `should notify when schedule canceled with AMOUNT_HIGHER_THAN_DAILY_LIMIT reason`() {
        every { walletService.findWallet(any()) } returns wallet

        val message = Message.builder().body(
            getObjectMapper().writeValueAsString(
                billScheduleCanceledAmountHigherThanDailyLimit.toBillEventDetailEntity(),
            ),
        ).build()

        val bill = Bill.build(
            billAdded.copy(
                billId = billScheduleCanceledAmountHigherThanDailyLimit.billId,
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            ),
            billPaymentScheduled.copy(
                billId = billScheduleCanceledAmountHigherThanDailyLimit.billId,
                walletId = wallet.id,
            ),
            billScheduleCanceledAmountHigherThanDailyLimit,
        )

        every { billEventRepository.getBillById(bill.billId) } returns Either.Right(bill)

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true
        verify {
            notificationAdapter.notifyBillScheduleCanceledDueAmountHigherThanDailyLimit(
                members = listOf(walletFixture.founder, walletFixture.participant),
                walletId = wallet.id,
                walletName = wallet.name,
                payee = bill.getPayee(),
                totalAmount = bill.amountTotal,
                type = bill.billType,
                author = any(),
            )

            notifyExpiredPaymentSchedule wasNot called
        }
    }

    @Test
    fun `deve notificar que uma bill foi desagendada por mudança de valor`() {
        every { walletService.findWallet(any()) } returns wallet

        val message = Message.builder().body(
            getObjectMapper().writeValueAsString(
                billScheduleCanceledAmountChanged.toBillEventDetailEntity(),
            ),
        ).build()

        val now = getZonedDateTime().toInstant().toEpochMilli()
        val bill = Bill.build(
            billAdded.copy(
                billId = billScheduleCanceledAmountChanged.billId,
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
                created = now,
            ),
            billPaymentScheduled.copy(
                billId = billScheduleCanceledAmountChanged.billId,
                walletId = wallet.id,
                created = now + 1,
            ),
            AmountUpdated(
                billId = billScheduleCanceledAmountChanged.billId,
                created = now + 2,
                walletId = wallet.id,
                actionSource = ActionSource.System,
                amount = 100L,
            ),
            billScheduleCanceledAmountChanged,
        )

        every { billEventRepository.getBillById(bill.billId) } returns Either.Right(bill)

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true
        verify {
            notifyPaymentCanceledDueToAmountChange.notify(billScheduleCanceledAmountChanged)
            notifyExpiredPaymentSchedule wasNot called
        }
    }

    @Test
    fun `deve notificar que uma bill foi desagendada por falha no pagamento via cartao de credito`() {
        every { walletService.findWallet(any()) } returns wallet

        val message = Message.builder().body(
            getObjectMapper().writeValueAsString(
                billScheduleCanceledCantPayWithCurrentCreditCard.toBillEventDetailEntity(),
            ),
        ).build()

        val now = getZonedDateTime().toInstant().toEpochMilli()
        val bill = Bill.build(
            billAdded.copy(
                billId = billScheduleCanceledAmountChanged.billId,
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
                created = now,
            ),
            billPaymentScheduled.copy(
                billId = billScheduleCanceledAmountChanged.billId,
                walletId = wallet.id,
                created = now + 1,
            ),
            billPaymentStart.copy(
                billId = billScheduleCanceledAmountChanged.billId,
                walletId = wallet.id,
                created = now + 2,
            ),
            billPaymentFailedRetryable.copy(
                billId = billScheduleCanceledAmountChanged.billId,
                walletId = wallet.id,
                created = now + 3,
            ),
            billScheduleCanceledCantPayWithCurrentCreditCard.copy(
                billId = billScheduleCanceledAmountChanged.billId,
                walletId = wallet.id,
                created = now + 4,
            ),
        )

        every { billEventRepository.getBillById(bill.billId) } returns Either.Right(bill)

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true
        verify {
            notificationAdapter.notifyScheduleBillCanceledDueCreditCardDenied(
                members = listOf(walletFixture.founder, walletFixture.participant),
                walletId = wallet.id,
                billId = bill.billId,
                payee = bill.getPayee(),
                amount = bill.amountTotal,
            )
            notifyExpiredPaymentSchedule wasNot called
        }
    }

    @Test
    fun `should notify not visible bill already exists when bill permission updated with visibility added and source WalletMailBox`() {
        every { walletService.findWallet(any()) } returns wallet

        val billPermissionUpdated = BillPermissionUpdated(
            billId = billAdded.billId,
            created = billAdded.created + 10,
            walletId = wallet.id,
            actionSource = ActionSource.WalletMailBox(
                from = walletFixture.ultraLimitedParticipant.emailAddress.value,
                accountId = walletFixture.ultraLimitedParticipant.accountId,
            ),
            permissionUpdated = PermissionUpdated.VisibilityAdded(accountId = walletFixture.ultraLimitedParticipant.accountId),
        )

        val message = Message.builder()
            .body(getObjectMapper().writeValueAsString(billPermissionUpdated.toBillEventDetailEntity())).build()

        val bill = Bill.build(
            billAdded.copy(
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            ),
            billPermissionUpdated,
        )

        every { billEventRepository.getBillById(bill.billId) } returns Either.Right(bill)

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true

        verify {
            notificationAdapter.notifyNotVisibleBillAlreadyExists(
                member = walletFixture.ultraLimitedParticipant,
                walletId = wallet.id,
                billId = bill.billId,
                payee = bill.getPayee(),
                dueDate = bill.dueDate,
                totalAmount = bill.amountTotal,
                walletName = wallet.name,
            )
        }
    }

    @Test
    fun `should not notify not visible bill already exists when bill permission updated with visibility added and source Api`() {
        every { walletService.findWallet(any()) } returns wallet

        val billPermissionUpdated = BillPermissionUpdated(
            billId = billAdded.billId,
            created = billAdded.created + 10,
            walletId = wallet.id,
            actionSource = ActionSource.Api(
                accountId = walletFixture.ultraLimitedParticipant.accountId,
            ),
            permissionUpdated = PermissionUpdated.VisibilityAdded(accountId = walletFixture.ultraLimitedParticipant.accountId),
        )

        val message = Message.builder()
            .body(getObjectMapper().writeValueAsString(billPermissionUpdated.toBillEventDetailEntity())).build()

        val bill = Bill.build(
            billAdded.copy(
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            ),
            billPermissionUpdated,
        )

        every { billEventRepository.getBillById(bill.billId) } returns Either.Right(bill)

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true

        verify {
            notificationAdapter wasNot Called
        }
    }

    @Test
    fun `deve notificar o dono da carteira quando uma conta se tornar não pagável e o autor estiver sido removido da carteira`() {
        every { walletService.findWallet(any()) } returns wallet
        val message = Message.builder()
            .body(
                getObjectMapper().writeValueAsString(
                    pixPaymentScheduledCanceled.copy(walletId = wallet.id).toBillEventDetailEntity(),
                ),
            )
            .build()

        val pix = Bill.build(
            pixAdded.copy(
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = AccountId("ACCOUNT-membro-nao-existente-na-carteira")),
            ),
            pixPaymentScheduled.copy(walletId = wallet.id),
            pixPaymentStarted.copy(walletId = wallet.id),
            pixPaymentScheduledCanceled.copy(walletId = wallet.id),
        )
        every { billEventRepository.getBillById(pixAdded.billId) } returns Either.Right(pix)

        val response = billEventNotificationHandler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true
        verify {
            notificationAdapter.notifyTransferNotPayable(
                members = listOf(walletFixture.founder, walletFixture.participant),
                recipientName = pix.recipient!!.name,
                dueDate = pix.effectiveDueDate,
                amount = pix.amountTotal,
                errorMessage = bankTransferNotPayableErrorMessage,
                author = walletFixture.founder,
                walletId = wallet.id,
                walletName = wallet.name,
                billId = pixAdded.billId,
            )
        }
    }

    companion object {

        private val walletFixture = WalletFixture()
        private val wallet = walletFixture.buildWallet(
            otherMembers = listOf(
                walletFixture.participant,
                walletFixture.ultraLimitedParticipant,
            ),
        )

        @JvmStatic
        fun actionSourcesWithoutNotify(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(ActionSource.Webapp(Role.OWNER)),
                Arguments.arguments(ActionSource.System),
                Arguments.arguments(ActionSource.Scheduled),
                Arguments.arguments(
                    ActionSource.Recurrence(
                        recurrenceId = RecurrenceId(value = "fakeId"),
                        role = Role.OWNER,
                    ),
                ),
                Arguments.arguments(ActionSource.MailBox(from = "<EMAIL>", role = Role.OWNER)),
            )
        }

        @JvmStatic
        fun notifiableBillEvents(): Stream<Arguments> {
            val billAdded = billAddedFicha.copy(
                actionSource = ActionSource.DDA(accountId = wallet.founder.accountId),
                document = wallet.founder.document,
            )
            return Stream.of(
                Arguments.arguments(billAdded),
                Arguments.arguments(pixPaymentScheduledCanceled.copy(walletId = wallet.id)),
                Arguments.arguments(billScheduleCanceled.copy(reason = ScheduleCanceledReason.EXPIRATION)),
                Arguments.arguments(billPaymentSchedulePostponedDueToLimitReached),
                Arguments.arguments(billScheduleCanceledAmountHigherThanDailyLimit),
                Arguments.arguments(
                    BillPermissionUpdated(
                        billId = ai.friday.billpayment.billAdded.billId,
                        created = ai.friday.billpayment.billAdded.created + 10,
                        walletId = wallet.id,
                        actionSource = ActionSource.WalletMailBox(
                            from = walletFixture.ultraLimitedParticipant.emailAddress.value,
                            accountId = walletFixture.ultraLimitedParticipant.accountId,
                        ),
                        permissionUpdated = PermissionUpdated.VisibilityAdded(accountId = walletFixture.ultraLimitedParticipant.accountId),
                    ),
                ),
            )
        }

        @JvmStatic
        fun apiSourceBillAdded(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(billAdded),
                Arguments.arguments(invoiceAdded),
                Arguments.arguments(pixAdded),
            )
        }
    }
}