package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.journey.UserJourneyEvent
import ai.friday.billpayment.app.journey.UserJourneyService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.integration.WalletFixture
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message

internal class SQSUserJourneyTrackEventHandlerTest {

    private val configurationMock: SQSMessageHandlerConfiguration = mockk() {
        every { userJourneyQueueName } returns "queue-name"
    }

    private val walletFixture = WalletFixture()

    private val userJourneyService: UserJourneyService = mockk()

    private val walletService: WalletService = mockk()

    private val handler = SQSUserJourneyTrackEventHandler(
        amazonSQS = mockk(),
        amazonSNS = mockk(),
        configuration = configurationMock,
        topicArn = "topic-name",
        userJourneyService = userJourneyService,
        walletService = walletService,
    )

    @Test
    fun `deve enviar evento para o User Pilot quando uma concessionaria for adicionada via email`() {
        every {
            userJourneyService.trackEventAsync(any(), any())
        } just Runs

        val event = billAdded
            .copy(actionSource = ActionSource.WalletMailBox(accountId = ACCOUNT.accountId, from = "<EMAIL>"))
            .toBillEventDetailEntity()

        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(event))
                .build()

        handler.handleMessage(message).shouldDeleteMessage shouldBe true

        verify {
            userJourneyService.trackEventAsync(accountId = ACCOUNT.accountId, UserJourneyEvent.SendBillByEmail)
        }
    }

    @Test
    fun `deve enviar evento para o User Pilot quando uma ficha de compensacao for adicionada via email`() {
        every {
            userJourneyService.trackEventAsync(any(), any())
        } just Runs

        val event = billAddedFicha
            .copy(actionSource = ActionSource.WalletMailBox(accountId = ACCOUNT.accountId, from = "<EMAIL>"))
            .toBillEventDetailEntity()

        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(event))
                .build()

        handler.handleMessage(message).shouldDeleteMessage shouldBe true

        verify {
            userJourneyService.trackEventAsync(accountId = ACCOUNT.accountId, UserJourneyEvent.SendBillByEmail)
        }
    }

    @Test
    fun `deve enviar evento para o User Pilot quando a conta for adicionada via email por um usuario desconhecido`() {
        every {
            userJourneyService.trackEventAsync(any(), any())
        } just Runs

        every {
            walletService.findWallet(billAdded.walletId)
        } returns walletFixture.buildWallet()

        val event = billAdded
            .copy(actionSource = ActionSource.WalletMailBox(accountId = null, from = "<EMAIL>"))
            .toBillEventDetailEntity()

        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(event))
                .build()

        handler.handleMessage(message).shouldDeleteMessage shouldBe true

        verify {
            userJourneyService.trackEventAsync(
                accountId = walletFixture.founderAccount.accountId,
                UserJourneyEvent.SendBillByEmail,
            )
        }
    }

    @Test
    fun `nao deve enviar evento para o User Pilot quando a conta nao for adicionada via email`() {
        every {
            userJourneyService.trackEventAsync(any(), any())
        } just Runs

        val event = billAdded
            .copy(actionSource = ActionSource.Api(accountId = ACCOUNT.accountId))
            .toBillEventDetailEntity()

        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(event))
                .build()

        handler.handleMessage(message).shouldDeleteMessage shouldBe true

        verify {
            userJourneyService wasNot called
        }
    }
}