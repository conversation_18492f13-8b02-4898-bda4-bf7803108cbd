package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.BillPaymentScheduledCreditCardInfo
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.BoletoSettlementResult
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.PixPaymentStatus
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.SettlementFundsTransfer
import ai.friday.billpayment.app.payment.SettlementFundsTransferType
import ai.friday.billpayment.app.payment.SinglePaymentData
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentFailed
import ai.friday.billpayment.billPaymentScheduleStarted
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billScheduleCanceled
import ai.friday.billpayment.concessionariaBill
import ai.friday.billpayment.createTransaction
import ai.friday.billpayment.creditCard
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDateTime
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import software.amazon.awssdk.services.sqs.model.Message
import software.amazon.awssdk.services.sqs.model.MessageAttributeValue

class SQSSettlementFundsTransferHandlerWithCreditCardTest : SQSSettlementFundsTransferHandlerTest() {

    override fun createEvents() =
        mutableListOf(
            billAdded,
            billPaymentScheduled.copy(
                infoData = BillPaymentScheduledCreditCardInfo(
                    paymentMethodId = PAYMENT_METHOD_ID,
                    netAmount = billAdded.amountTotal,
                    feeAmount = 1L,
                    installments = 1,
                    fee = 4.0,
                    calculationId = null,
                ),
            ),
            billPaymentScheduleStarted,
            billPaid,
            billScheduleCanceled,
        )

    override fun fundsTransferSourceAccountType(): SettlementFundsTransferType =
        SettlementFundsTransferType.CASHIN_ACCOUNT

    override fun transaction() = createTransaction(
        transactionId = transactionId,
        paymentData = SinglePaymentData(
            accountPaymentMethod = creditCard,
            details = PaymentMethodsDetailWithCreditCard(
                paymentMethodId = creditCard.id,
                netAmount = concessionariaBill.amountTotal,
                feeAmount = 1L,
                installments = 1,
                fee = 4.0,
                calculationId = null,
            ),
            payment = null,
        ),
        settlementData = SettlementData(
            settlementTarget = concessionariaBill,
            serviceAmountTax = 0,
            totalAmount = concessionariaBill.amountTotal,
            settlementOperation = BoletoSettlementResult(
                gateway = FinancialServiceGateway.FRIDAY,
                status = BoletoSettlementStatus.AUTHORIZED,
                bankTransactionId = "312312",
                externalNsu = 1,
                externalTerminal = "********",
                errorCode = "",
                errorDescription = null,
                authentication = "11.111.1111.1",
                paymentPartnerName = "ARBI",
                finalPartnerName = FinancialServiceGateway.CELCOIN,
                errorRetryable = false,
            ),
        ),
        nsu = 1,
        walletId = concessionariaBill.walletId,
    )

    override fun originBankAccount() = settlementFundsTransferConfiguration.originCashinBankAccount

    @Test
    fun `não deve transferir o valor do pagamento se a transação não for UNDONE`() {
        val message = Message.builder()
            .body(
                getObjectMapper().writeValueAsString(
                    billPaymentFailed.copy(transactionId = transactionId).toBillEventDetailEntity(),
                ),
            )
            .messageAttributes(
                mapOf(
                    billTypeAttributeName to MessageAttributeValue.builder().dataType("String")
                        .stringValue(BillType.CONCESSIONARIA.name).build(),
                ),
            )
            .build()

        val response = handler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true

        verify(exactly = 0) {
            pixPaymentService.checkPaymentStatus(any(), bankOperationId = any())
            pixPaymentService.initPayment(
                command = any(),
                bankOperationId = any(),
                deviceId = null,
            )
        }

        val transfers = billRepository.findSettlementFundsTransfers(bill.billId)
        transfers.size shouldBe 0
    }

    @Test
    fun `quando for uma operacao de crédito e o pagamento for do arbi, tem que ir para a conta liquidação`() {
        val transaction = transactionRepository.findById(transactionId)
        val settlementOperation = (transaction.settlementData.settlementOperation as BoletoSettlementResult).copy(finalPartnerName = FinancialServiceGateway.ARBI)
        transactionRepository.save(transaction.copy(settlementData = transaction.settlementData.copy(settlementOperation = settlementOperation)))

        val operationId = BankOperationId.build()

        every { arbiAdapterMock.transfer(any(), any(), any(), any()) } returns BankTransfer(operationId = operationId, gateway = FinancialServiceGateway.FRIDAY, status = BankOperationStatus.SUCCESS, amount = bill.amountTotal)
        every { arbiAdapterMock.checkTransferStatus(any(), any(), any(), any()) } returns true

        val response = handler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true

        verify {
            arbiAdapterMock.transfer(
                originAccountNo = any(),
                targetAccountNo = "${settlementFundsTransferConfiguration.originSettlementBankAccount.accountNo}${settlementFundsTransferConfiguration.originSettlementBankAccount.accountDv}",
                amount = any(),
                operationId = any(),
            )
        }
    }

    @ParameterizedTest
    @EnumSource(BankOperationStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["SUCCESS", "REFUNDED"])
    fun `quando falhar a tef não deve deletar a mensagem`(status: BankOperationStatus) {
        val transaction = transactionRepository.findById(transactionId)
        val settlementOperation = (transaction.settlementData.settlementOperation as BoletoSettlementResult).copy(finalPartnerName = FinancialServiceGateway.ARBI)
        transactionRepository.save(transaction.copy(settlementData = transaction.settlementData.copy(settlementOperation = settlementOperation)))

        val operationId = BankOperationId.build()
        val slot = slot<BankOperationId>()
        every { arbiAdapterMock.transfer(any(), any(), any(), capture(slot)) } returns BankTransfer(operationId = operationId, gateway = FinancialServiceGateway.FRIDAY, status = status, amount = bill.amountTotal)
        every { arbiAdapterMock.checkTransferStatus(any(), any(), any(), any()) } returns false

        val response = handler.handleMessage(message)

        response.shouldDeleteMessage shouldBe false

        val transfers = billRepository.findSettlementFundsTransfers(bill.billId)

        transfers.size shouldBe 1
        with(transfers.first()) {
            bankOperationId shouldBe slot.captured
            amount shouldBe bill.amountTotal
            status shouldBe status
        }
    }

    @ParameterizedTest
    @EnumSource(PixPaymentStatus::class, mode = EnumSource.Mode.INCLUDE, names = ["ACKNOWLEDGED", "UNKNOWN"])
    fun `deve transferir o valor do pagamento da conta liquidação para conta cashin quando um pagamento com cartão for estornado`(
        pixPaymentStatus: PixPaymentStatus,
    ) {
        val slot = slot<BankOperationId>()
        every { arbiAdapterMock.transfer(any(), any(), any(), capture(slot)) } returns BankTransfer(operationId = BankOperationId.build(), gateway = FinancialServiceGateway.FRIDAY, status = BankOperationStatus.SUCCESS, amount = bill.amountTotal)
        every { arbiAdapterMock.checkTransferStatus(any(), any(), any(), any()) } returns true

        val message = Message.builder()
            .body(
                getObjectMapper().writeValueAsString(
                    billPaymentFailed.copy(transactionId = transactionId).toBillEventDetailEntity(),
                ),
            )
            .messageAttributes(
                mapOf(
                    billTypeAttributeName to MessageAttributeValue.builder().dataType("String")
                        .stringValue(BillType.CONCESSIONARIA.name).build(),
                ),
            )
            .build()

        transactionRepository.save(transaction().copy(status = TransactionStatus.UNDONE))

        billRepository.saveSettlementFundsTransfer(
            settlementFundsTransfer = SettlementFundsTransfer(
                bankOperationId = BankOperationId(value = "mocked-bank-operation-id"),
                billId = bill.billId,
                status = BankOperationStatus.SUCCESS,
                amount = 0L,
                created = LocalDateTime.now(),
                transactionId = billPaid.transactionId!!,
                sourceAccountType = SettlementFundsTransferType.CASHIN_ACCOUNT,
                sameBank = true,
            ),
        )

        billRepository.saveSettlementFundsTransfer(
            settlementFundsTransfer = SettlementFundsTransfer(
                bankOperationId = BankOperationId(value = "mocked-bank-operation-id-2"),
                billId = bill.billId,
                status = BankOperationStatus.SUCCESS,
                amount = 0L,
                created = LocalDateTime.now(),
                transactionId = billPaid.transactionId!!,
                sourceAccountType = SettlementFundsTransferType.SETTLEMENT_ACCOUNT,
                sameBank = true,
            ),
        )

        val response = handler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true

        verify {
            arbiAdapterMock.transfer(
                originAccountNo = settlementFundsTransferConfiguration.originSettlementBankAccount.accountNo.toString() + settlementFundsTransferConfiguration.originSettlementBankAccount.accountDv,
                targetAccountNo = settlementFundsTransferConfiguration.originCashinBankAccount.accountNo.toString() + settlementFundsTransferConfiguration.originCashinBankAccount.accountDv,
                amount = any(),
                operationId = any(),
            )
            arbiAdapterMock.checkTransferStatus(
                idRequisicaoParceiroOriginal = slot.captured.value,
                startDate = any(),
                endDate = any(),
                originAccountNo = settlementFundsTransferConfiguration.originSettlementBankAccount.accountNo.toString() + settlementFundsTransferConfiguration.originSettlementBankAccount.accountDv,
            )
        }

        val transfers = billRepository.findSettlementFundsTransfers(bill.billId)

        transfers.size shouldBe 3
        with(transfers.first()) {
            bankOperationId shouldBe slot.captured
            amount shouldBe bill.amountTotal
            status shouldBe BankOperationStatus.SUCCESS
            sourceAccountType shouldBe SettlementFundsTransferType.REFUND_CASHIN_ACCOUNT
        }
    }

    @Test
    fun `não deve transferir o valor do pagamento da conta liquidação para conta cashin quando o estorno já foi feito`() {
        val message = Message.builder()
            .body(
                getObjectMapper().writeValueAsString(
                    billPaymentFailed.copy(transactionId = transactionId).toBillEventDetailEntity(),
                ),
            )
            .messageAttributes(
                mapOf(
                    billTypeAttributeName to MessageAttributeValue.builder().dataType("String")
                        .stringValue(BillType.CONCESSIONARIA.name).build(),
                ),
            )
            .build()

        val mockedValue = 10L
        billRepository.saveSettlementFundsTransfer(
            settlementFundsTransfer = SettlementFundsTransfer(
                bankOperationId = BankOperationId(value = "mocked-bank-operation-id"),
                billId = bill.billId,
                status = BankOperationStatus.SUCCESS,
                amount = mockedValue,
                created = LocalDateTime.now(),
                transactionId = billPaid.transactionId!!,
                sourceAccountType = SettlementFundsTransferType.REFUND_CASHIN_ACCOUNT,
                sameBank = true,
            ),
        )

        transactionRepository.save(transaction().copy(status = TransactionStatus.UNDONE))

        val response = handler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true

        verify(exactly = 0) {
            pixPaymentService.checkPaymentStatus(any(), bankOperationId = any())
            pixPaymentService.initPayment(
                command = any(),
                bankOperationId = any(),
                deviceId = null,
            )
        }

        val transfers = billRepository.findSettlementFundsTransfers(bill.billId)
        transfers.size shouldBe 1
    }
}