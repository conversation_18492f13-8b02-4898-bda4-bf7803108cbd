package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.ExternalBillId
import ai.friday.billpayment.app.bill.ExternalBillProvider
import ai.friday.billpayment.app.vehicledebts.LicensePlate
import ai.friday.billpayment.app.vehicledebts.VehicleDebtEnrichmentMessage
import ai.friday.billpayment.app.vehicledebts.VehicleDebtEnrichmentService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_BARCODE
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import ai.friday.billpayment.integration.WALLET_ID
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

class SQSVehicleDebtsEnrichmentHandlerTest {

    private val sqsClient: SqsClient = mockk(relaxed = true)

    private val configuration: SQSMessageHandlerConfiguration = mockk {
        every { vehicleDebtsEnrichmentQueueName } returns "vehicle_debts_enrichment"
    }

    private val vehicleDebtEnrichmentService = mockk<VehicleDebtEnrichmentService>()

    private val handler = SQSVehicleDebtsEnrichmentHandler(
        amazonSQS = sqsClient,
        configuration = configuration,
        service = vehicleDebtEnrichmentService,
    )

    @Test
    fun `deve chamar o serviço de enriquecimento quando receber uma mensagem válida com barcode`() {
        val accountId = "ACCOUNT-123"
        val licensePlate = "ABC1234"
        val barcode = FICHA_DE_COMPENSACAO_BARCODE
        val externalId = "EXT-123"

        val description = "Multa de trânsito por excesso de velocidade"
        val enrichmentMessage = VehicleDebtEnrichmentMessage(
            accountId = accountId,
            licensePlate = licensePlate,
            barcode = barcode,
            externalId = externalId,
            walletId = WALLET_ID,
            description = description,
        )

        val message = Message.builder()
            .body(getObjectMapper().writeValueAsString(enrichmentMessage))
            .build()

        every {
            vehicleDebtEnrichmentService.enrichVehicleDebt(
                accountId = AccountId(accountId),
                barcode = BarCode.of(barcode),
                licensePlate = LicensePlate(licensePlate),
                externalId = ExternalBillId(externalId, ExternalBillProvider.VEHICLE_DEBTS),
                details = description,
                walletId = WalletId(WALLET_ID),
            )
        } returns Result.success(Unit)

        val response = handler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true

        verify(exactly = 1) {
            vehicleDebtEnrichmentService.enrichVehicleDebt(
                accountId = AccountId(accountId),
                barcode = BarCode.of(barcode),
                licensePlate = LicensePlate(licensePlate),
                externalId = any(),
                details = description,
                walletId = WalletId(WALLET_ID),
            )
        }
    }

    @Test
    fun `deve processar corretamente quando a descrição for nula`() {
        val accountId = "ACCOUNT-123"
        val licensePlate = "ABC1234"
        val barcode = FICHA_DE_COMPENSACAO_DIGITABLE_LINE
        val externalId = "EXT-123"

        val enrichmentMessage = VehicleDebtEnrichmentMessage(
            accountId = accountId,
            licensePlate = licensePlate,
            barcode = barcode,
            externalId = externalId,
            walletId = WALLET_ID,
            description = null,
        )

        val message = Message.builder()
            .body(getObjectMapper().writeValueAsString(enrichmentMessage))
            .build()

        every {
            vehicleDebtEnrichmentService.enrichVehicleDebt(
                accountId = AccountId(accountId),
                barcode = BarCode.of(barcode),
                licensePlate = LicensePlate(licensePlate),
                externalId = ExternalBillId(externalId, ExternalBillProvider.VEHICLE_DEBTS),
                details = null,
                walletId = WalletId(WALLET_ID),
            )
        } returns Result.success(Unit)

        val response = handler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true

        verify(exactly = 1) {
            vehicleDebtEnrichmentService.enrichVehicleDebt(
                accountId = AccountId(accountId),
                barcode = BarCode.of(barcode),
                licensePlate = LicensePlate(licensePlate),
                externalId = any(),
                details = null,
                walletId = WalletId(WALLET_ID),
            )
        }
    }
}