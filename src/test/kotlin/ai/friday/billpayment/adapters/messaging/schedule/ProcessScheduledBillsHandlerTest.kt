package ai.friday.billpayment.adapters.messaging.schedule

import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.app.integrations.ScheduledBillPaymentService
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.message
import io.kotest.matchers.booleans.shouldBeTrue
import io.mockk.called
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import software.amazon.awssdk.services.sqs.SqsClient

class ProcessScheduledBillsHandlerTest {

    private val sqsClient: SqsClient = mockk()

    private val configuration: SQSMessageHandlerConfiguration = mockk()

    private val scheduledBillPaymentService: ScheduledBillPaymentService = mockk(relaxed = true)

    private val handler = ProcessScheduledBillsHandler(
        amazonSQS = sqsClient,
        configuration = configuration,
        queue = "sqs.payment.process-scheduled-bills",
        scheduledBillPaymentService = scheduledBillPaymentService,
    )

    private val walletFixture = WalletFixture()

    private val walletId = walletFixture.defaultWalletId

    @Test
    fun `deve apagar a mensagem da fila quando falhar a conversao`() {
        val message = message("hello")
        handler.handleMessage(message).shouldDeleteMessage.shouldBeTrue()
        verify {
            scheduledBillPaymentService wasNot called
        }
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `deve enviar a wallet para processamento`(includeSubscription: Boolean) {
        val scheduleDate = LocalDate.of(2023, 8, 10)

        val message = message(
            ProcessScheduledBillsCommand(
                walletId = walletId,
                scheduleDate = scheduleDate,
                includeSubscription = includeSubscription,
            ),
        )

        handler.handleMessage(message).shouldDeleteMessage.shouldBeTrue()

        verify {
            scheduledBillPaymentService.process(walletId, scheduleDate, includeSubscription)
        }
    }
}