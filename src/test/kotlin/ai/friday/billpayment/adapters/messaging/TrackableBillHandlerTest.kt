package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.ServerError
import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.bill.tracking.TrackableBillHandlerService
import ai.friday.billpayment.app.bill.tracking.UntrackableEntity
import ai.friday.billpayment.billPaid
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message

internal class TrackableBillHandlerTest {
    private lateinit var handler: TrackableBillHandler

    private lateinit var service: TrackableBillHandlerService

    @BeforeEach
    fun setUp() {
        service = mockk()

        handler = TrackableBillHandler(mockk(), mockk(), mockk(relaxed = true), "", service)
    }

    private val event = getObjectMapper().writeValueAsString(billPaid.toBillEventDetailEntity())

    @Test
    internal fun `should delete message from broker when service does not throw any error`() {
        val message = Message.builder().body(event).build()

        every { service.execute(any()) } returns Unit.right()

        val result = handler.handleMessage(message)

        result shouldBe SQSHandlerResponse(true)

        verify { service.execute(any()) }
    }

    @Test
    fun `should delete message from broker when service returns UntrackableEntity error`() {
        val message = Message.builder().body(event).build()

        every { service.execute(any()) } returns UntrackableEntity.left()

        val result = handler.handleMessage(message)

        result shouldBe SQSHandlerResponse(true)

        verify { service.execute(any()) }
    }

    @Test
    fun `should not delete message from broker when service returns an Error`() {
        val message = Message.builder().body(event).build()

        every { service.execute(any()) } returns ServerError(NoStackTraceException()).left()

        val result = handler.handleMessage(message)

        result shouldBe SQSHandlerResponse(false)

        verify { service.execute(any()) }
    }
}