package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.payment.receipt.NotifyReceiptService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.invoicePaid
import ai.friday.billpayment.pixPaid
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message

internal class SQSBillReceiptGeneratorHandlerTest {

    private val notifyReceiptService: NotifyReceiptService = mockk {
        every { notify(any()) } just Runs
    }

    private val sQSBillReceiptGeneratorHandler = SQSBillReceiptGeneratorHandler(
        amazonSQS = mockk(),
        configuration = mockk(relaxed = true),
        notifyReceiptService = notifyReceiptService,
    )

    @Test
    fun `should notify invoice receipt on invoice paid event`() {
        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(invoicePaid.toBillEventDetailEntity())).build()

        sQSBillReceiptGeneratorHandler.handleMessage(message) shouldBe SQSHandlerResponse(true)

        verify {
            notifyReceiptService.notify(any())
        }
    }

    @Test
    fun `should notify pix receipt on pix paid event`() {
        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(pixPaid.toBillEventDetailEntity())).build()

        sQSBillReceiptGeneratorHandler.handleMessage(message) shouldBe SQSHandlerResponse(true)

        verify {
            notifyReceiptService.notify(any())
        }
    }

    @Test
    fun `should notify boleto receipt on boleto paid event`() {
        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(billPaid.toBillEventDetailEntity())).build()

        every { notifyReceiptService.notify(any()) } just Runs

        sQSBillReceiptGeneratorHandler.handleMessage(message) shouldBe SQSHandlerResponse(true)

        verify {
            notifyReceiptService.notify(any())
        }
    }

    @Test
    fun `should not notify on bill event different than paid`() {
        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(billAdded.toBillEventDetailEntity())).build()

        sQSBillReceiptGeneratorHandler.handleMessage(message) shouldBe SQSHandlerResponse(true)

        verify {
            notifyReceiptService wasNot called
        }
    }

    @Test
    fun `should not delete message on error`() {
        val message = Message.builder().body(getObjectMapper().writeValueAsString(billPaid)).build()

        sQSBillReceiptGeneratorHandler.handleError(message, NoStackTraceException("teste")) shouldBe SQSHandlerResponse(
            false,
        )
    }
}