package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.bill.BillEvent
import software.amazon.awssdk.services.sqs.model.Message
import software.amazon.awssdk.services.sqs.model.MessageAttributeValue

object SQSMessageFixture {
    fun create(content: Any): Message = Message.builder().body(getObjectMapper().writeValueAsString(content)).build()
    fun create(content: String) = Message.builder().body(content).build()

    fun createByEvent(event: BillEvent) =
        Message.builder().body(getObjectMapper().writeValueAsString(event.toBillEventDetailEntity())).messageAttributes(
            mapOf(
                eventTypeAttributeName to MessageAttributeValue.builder().dataType("String")
                    .stringValue(event.eventType.name).build(),
            ),
        ).build()
}