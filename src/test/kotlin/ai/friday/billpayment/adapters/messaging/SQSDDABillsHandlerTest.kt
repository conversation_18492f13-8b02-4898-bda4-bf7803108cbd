package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.ServerError
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillAmountCalculator
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillSynchronizationStatus
import ai.friday.billpayment.app.bill.CalculatedAmount
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.FichaCompensacaoService
import ai.friday.billpayment.app.bill.SynchronizeBillResponse
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.dda.CreateDDARegister
import ai.friday.billpayment.app.dda.DDABill
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BillValidationService
import ai.friday.billpayment.app.integrations.DDARepository
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.BillValidationResponse
import ai.friday.billpayment.app.payment.DiscountType
import ai.friday.billpayment.app.payment.FineType
import ai.friday.billpayment.app.payment.InterestType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.WalletStatus
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.createDDAConfig
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.BILL_ID
import ai.friday.billpayment.integration.DIGITABLE_LINE
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.Runs
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import java.io.FileInputStream
import java.nio.charset.StandardCharsets
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import org.apache.commons.io.IOUtils
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import software.amazon.awssdk.services.sqs.model.Message

internal class SQSDDABillsHandlerTest {

    private val fichaCompensacaoService: FichaCompensacaoService = mockk()

    private val updateBillService = mockk<UpdateBillService>()

    private val findBillService: FindBillService = mockk()

    private val featureConfiguration: FeatureConfiguration = mockk()

    private val billValidationService = mockk<BillValidationService>()

    private val ddaRepository = mockk<DDARepository> {
        every { find(accountId = any()) } returns null
        every { find(document = any()) } returns null
        every { find(barCode = any(), document = any(), dueDate = any()) } returns null
        every { save(ddaBill = any()) } just Runs
    }

    private val accountRepository = mockk<AccountRepository> {
        every { findAccountByDocument(any()) } returns mockk(relaxed = true)
    }

    private val dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")

    private val baixa = BaixaTO(
        agebcobaixa = null,
        codbcobaixa = "213",
        canal = null,
        cnpj_cpf_port = "",
        nome_port = "",
        dataprocbaixa = getZonedDateTime().format(dateTimeFormatter),
        meio = null,
        numidentbaixa = 2022070600352635748,
        seqbaixa = 1,
        tpbaixa = 2,
        vlrbaixa = 0.0,
    )

    val calculator = spyk<BillAmountCalculator> {
        every { calculate(any(), any(), any(), any(), any(), any(), any()) } returns CalculatedAmount(
            0,
            0,
            0,
            0,
            0,
            0,
            validUntil = LocalDate.now(),
        ).right()
    }

    private val ddaConfig = createDDAConfig(
        maxAddUser = 0,
        maxAddUserAsync = 0,
    )

    private val createDDARegister = CreateDDARegister(
        ddaRepository = ddaRepository,
        ddaConfig = ddaConfig,
    )

    private val walletService = mockk<WalletService> {
        every { findWallet(any()) } returns mockk(relaxed = true) {
            every { status } returns WalletStatus.ACTIVE
        }
    }

    private val paymentTimeLimitConfig = "22:00"

    private val sqsDDABillsHandler = SQSDDABillsHandler(
        amazonSQS = mockk(),
        configuration = mockk(relaxed = true),
        ddaService = DDAService(
            ddaProviderService = mockk(),
            accountRepository = accountRepository,
            billValidationService = billValidationService,
            findBillService = findBillService,
            featureConfiguration = featureConfiguration,
            updateBillService = updateBillService,
            ddaRepository = ddaRepository,
            fullDDAPostProcessor = mockk(),
            messagePublisher = mockk(),
            ddaConfig = ddaConfig,
            createDDARegister = createDDARegister,
            fichaCompensacaoService = fichaCompensacaoService,
            walletService = walletService,
            openFinanceIncentiveService = mockk(relaxed = true),
            ddaFullImportQueueName = "queue",
        ),
        calculator = calculator,
        ddaRepository = ddaRepository,
    ).apply {
        paymentTimeLimit = paymentTimeLimitConfig
    }

    private val resource = Thread.currentThread().contextClassLoader.getResource("arbi/DDAInformation.json")
    private val json = IOUtils.toString(FileInputStream(resource!!.path), StandardCharsets.UTF_8.name())

    @Nested
    @DisplayName("caso nao tenha desconto nem esteja vencido")
    inner class BoletoPadrao {

        private val ddaBillTO: DDABillTO = parseObjectFrom<DDABillTO>(json).copy(
            coddesconto01 = DiscountType.FREE.code,
            datavencimento = getZonedDateTime().plusDays(10).format(dateTimeFormatter),
        )

        @Test
        fun `pode adicionar a conta e remover da fila`() {
            val slot = slot<BillValidationResponse>()
            every { fichaCompensacaoService.createFichaDeCompensacao(any(), any(), any()) } returns CreateBillResult.SUCCESS(
                Bill.build(
                    billAdded,
                ),
            )

            val message = Message.builder()
                .body(
                    getObjectMapper().writeValueAsString(
                        ddaBillTO,
                    ),
                ).build()

            val result = sqsDDABillsHandler.handleMessage(message)

            result.shouldDeleteMessage shouldBe true

            verify {
                fichaCompensacaoService.createFichaDeCompensacao(any(), capture(slot), any())
            }

            slot.captured shouldNotBe null
            slot.captured.billRegisterData?.amountTotal shouldBe 304255L
            slot.captured.billRegisterData?.amountPaid.shouldBeNull()
            slot.captured.billRegisterData?.recipient?.name shouldBe "Fatura Cartao BTG Pactual"
            slot.captured.billRegisterData?.recipient?.document shouldBe "04413729000140"
        }

        @ParameterizedTest
        @EnumSource(
            AmountCalculationModel::class,
            mode = EnumSource.Mode.EXCLUDE,
            names = ["UNKNOWN", "BENEFICIARY_ONLY"],
        )
        fun `não deve calcular o valor`(amountCalculationModel: AmountCalculationModel) {
            sqsDDABillsHandler.buildBillValidationResponse(
                ddaBillTO.copy(
                    tipocalculo = amountCalculationModel.code,
                ),
            ).shouldNotBeNull()
            verify(exactly = 0) {
                calculator.calculate(any(), any(), any(), any(), any(), any())
            }
        }

        @ParameterizedTest
        @EnumSource(
            AmountCalculationModel::class,
            mode = EnumSource.Mode.INCLUDE,
            names = ["UNKNOWN", "BENEFICIARY_ONLY"],
        )
        fun `deve validar o boleto`(amountCalculationModel: AmountCalculationModel) {
            sqsDDABillsHandler.buildBillValidationResponse(
                ddaBillTO.copy(tipocalculo = amountCalculationModel.code),
            ).shouldBeNull()
            verify(exactly = 0) {
                calculator.calculate(any(), any(), any(), any(), any(), any())
            }
        }
    }

    @Nested
    @DisplayName("caso o titulo não tenha multa")
    inner class BoletoSemMulta {

        private val ddaBillTO: DDABillTO = parseObjectFrom<DDABillTO>(json).copy(
            codmulta = FineType.FREE.code,
            codmora = InterestType.FREE.code,
            coddesconto01 = DiscountType.FREE.code,
        )

        @Nested
        @DisplayName("e esteja vencido")
        inner class Vencido {
            private val dueDate = getLocalDate().atStartOfDay().minusDays(3)

            @ParameterizedTest
            @EnumSource(
                AmountCalculationModel::class,
                mode = EnumSource.Mode.EXCLUDE,
                names = ["UNKNOWN", "BENEFICIARY_ONLY", "BENEFICIARY_AFTER_DUE_DATE"],
            )
            fun `nao deve calcular o valor`(amountCalculationModel: AmountCalculationModel) {
                sqsDDABillsHandler.buildBillValidationResponse(
                    ddaBillTO.copy(
                        datavencimento = dueDate.format(dateTimeFormatter),
                        datamulta = dueDate.plusDays(1).format(dateTimeFormatter),
                        tipocalculo = amountCalculationModel.code,
                    ),
                ).shouldNotBeNull()
                verify(exactly = 0) {
                    calculator.calculate(any(), any(), any(), any(), any(), any())
                }
            }

            @ParameterizedTest
            @EnumSource(
                AmountCalculationModel::class,
                mode = EnumSource.Mode.INCLUDE,
                names = ["UNKNOWN", "BENEFICIARY_ONLY", "BENEFICIARY_AFTER_DUE_DATE"],
            )
            fun `deve validar o boleto quando o tipo de calculo for`(amountCalculationModel: AmountCalculationModel) {
                sqsDDABillsHandler.buildBillValidationResponse(
                    ddaBillTO.copy(
                        datavencimento = dueDate.format(dateTimeFormatter),
                        datamulta = dueDate.plusDays(1).format(dateTimeFormatter),
                        tipocalculo = amountCalculationModel.code,
                    ),
                ).shouldBeNull()
                verify(exactly = 0) {
                    calculator.calculate(any(), any(), any(), any(), any(), any())
                }
            }
        }
    }

    @Nested
    @DisplayName("caso o titulo tenha multa")
    inner class BoletoComMulta {

        private val ddaBillTO: DDABillTO = parseObjectFrom<DDABillTO>(json).copy(
            codmulta = FineType.FREE.code,
            codmora = InterestType.VALUE.code,
            coddesconto01 = DiscountType.FREE.code,
        )

        @Nested
        @DisplayName("e nao esteja vencido")
        inner class NaoVencido {

            private val dueDate = getLocalDate().atStartOfDay().plusDays(1)

            @Test
            fun `pode adicionar a conta e remover da fila sem validar`() {
                val slot = slot<BillValidationResponse>()
                every {
                    fichaCompensacaoService.createFichaDeCompensacao(
                        any(),
                        any(),
                        any(),
                    )
                } returns CreateBillResult.SUCCESS(
                    Bill.build(
                        billAdded,
                    ),
                )

                val message = Message.builder().body(
                    getObjectMapper().writeValueAsString(
                        ddaBillTO.copy(
                            datavencimento = dueDate.format(dateTimeFormatter),
                            datamulta = dueDate.plusDays(1).format(dateTimeFormatter),
                        ),
                    ),
                ).build()

                val result = sqsDDABillsHandler.handleMessage(message)

                result.shouldDeleteMessage shouldBe true

                verify {
                    fichaCompensacaoService.createFichaDeCompensacao(any(), capture(slot), any())
                }
                slot.captured shouldNotBe null
                slot.captured.isValid()
            }

            @ParameterizedTest
            @EnumSource(
                AmountCalculationModel::class,
                mode = EnumSource.Mode.EXCLUDE,
                names = ["UNKNOWN", "BENEFICIARY_ONLY"],
            )
            fun `nao deve calcular o valor`(amountCalculationModel: AmountCalculationModel) {
                sqsDDABillsHandler.buildBillValidationResponse(
                    ddaBillTO.copy(
                        datavencimento = dueDate.format(dateTimeFormatter),
                        datamulta = dueDate.plusDays(1).format(dateTimeFormatter),
                        tipocalculo = amountCalculationModel.code,
                    ),
                ).shouldNotBeNull()
                verify(exactly = 0) {
                    calculator.calculate(any(), any(), any(), any(), any(), any())
                }
            }

            @ParameterizedTest
            @EnumSource(
                AmountCalculationModel::class,
                mode = EnumSource.Mode.INCLUDE,
                names = ["UNKNOWN", "BENEFICIARY_ONLY"],
            )
            fun `deve validar o boleto quando o tipo de calculo for`(amountCalculationModel: AmountCalculationModel) {
                sqsDDABillsHandler.buildBillValidationResponse(
                    ddaBillTO.copy(
                        datavencimento = dueDate.format(dateTimeFormatter),
                        datamulta = dueDate.plusDays(1).format(dateTimeFormatter),
                        tipocalculo = amountCalculationModel.code,
                    ),
                ).shouldBeNull()
                verify(exactly = 0) {
                    calculator.calculate(any(), any(), any(), any(), any(), any())
                }
            }
        }

        @Nested
        @DisplayName("e esteja vencido")
        inner class Vencido {

            private val dueDate = getLocalDate().atStartOfDay().minusDays(1)

            @Test
            fun `deve validar a conta antes de adicionar e remover da fila`() {
                every {
                    fichaCompensacaoService.createFichaDeCompensacao(
                        any(),
                        any(),
                        any(),
                    )
                } returns CreateBillResult.SUCCESS(
                    Bill.build(
                        billAdded,
                    ),
                )

                val message = Message.builder().body(
                    getObjectMapper().writeValueAsString(
                        ddaBillTO.copy(
                            tipocalculo = AmountCalculationModel.BENEFICIARY_ONLY.code,
                            datavencimento = dueDate.format(dateTimeFormatter),
                            datamulta = dueDate.plusDays(1).format(dateTimeFormatter),
                            codmulta = FineType.VALUE.code,
                        ),
                    ),
                ).build()

                val result = sqsDDABillsHandler.handleMessage(message)

                result.shouldDeleteMessage shouldBe true

                verify {
                    fichaCompensacaoService.createFichaDeCompensacao(any(), null, any())
                }
            }

            @ParameterizedTest
            @EnumSource(AmountCalculationModel::class, mode = EnumSource.Mode.INCLUDE, names = ["ANYONE"])
            fun `deve calcular o valor quando o modelo de calculo for`(amountCalculationModel: AmountCalculationModel) {
                val actualDdaBillTO = ddaBillTO.copy(
                    datavencimento = dueDate.format(dateTimeFormatter),
                    datamulta = dueDate.plusDays(1).format(dateTimeFormatter),
                    tipocalculo = amountCalculationModel.code,
                )
                sqsDDABillsHandler.buildBillValidationResponse(actualDdaBillTO).shouldNotBeNull()

                verify {
                    calculator.calculate(any(), any(), any(), any(), any(), any(), any())
                }
            }

            @ParameterizedTest
            @EnumSource(
                AmountCalculationModel::class,
                mode = EnumSource.Mode.INCLUDE,
                names = ["BENEFICIARY_AFTER_DUE_DATE", "BENEFICIARY_ONLY", "UNKNOWN"],
            )
            fun `deve validar o boleto quando modelo de calculo for`(amountCalculationModel: AmountCalculationModel) {
                sqsDDABillsHandler.buildBillValidationResponse(
                    ddaBillTO.copy(
                        datavencimento = dueDate.format(dateTimeFormatter),
                        datamulta = dueDate.plusDays(1).format(dateTimeFormatter),
                        tipocalculo = amountCalculationModel.code,
                    ),
                ).shouldBeNull()
                verify(exactly = 0) {
                    calculator.calculate(any(), any(), any(), any(), any(), any())
                }
            }
        }
    }

    @Nested
    @DisplayName("caso o titulo tenha juros")
    inner class BoletoComJuros {

        val ddaBillTO: DDABillTO = parseObjectFrom<DDABillTO>(json).copy(
            coddesconto01 = DiscountType.FREE.code,
            codmulta = FineType.FREE.code,
            codmora = InterestType.VALUE.code,
        )

        @Nested
        @DisplayName("e não esteja vencido")
        inner class NaoVencido {

            private val dueDate = getLocalDate().plusDays(9).atStartOfDay()

            @Test
            fun `deve adicionar a conta e remover da fila sem validar`() {
                val slot = slot<BillValidationResponse>()
                every {
                    fichaCompensacaoService.createFichaDeCompensacao(
                        any(),
                        any(),
                        any(),
                    )
                } returns CreateBillResult.SUCCESS(
                    Bill.build(
                        billAdded,
                    ),
                )

                val message = Message.builder()
                    .body(
                        getObjectMapper().writeValueAsString(
                            ddaBillTO.copy(
                                datavencimento = dueDate.format(
                                    dateTimeFormatter,
                                ),
                                datamora = dueDate.plusDays(1).format(dateTimeFormatter),
                            ),
                        ),
                    ).build()

                val result = sqsDDABillsHandler.handleMessage(message)

                result.shouldDeleteMessage shouldBe true

                verify {
                    fichaCompensacaoService.createFichaDeCompensacao(any(), capture(slot), any())
                }
                slot.captured shouldNotBe null
                slot.captured.isValid()
            }

            @ParameterizedTest
            @EnumSource(
                AmountCalculationModel::class,
                mode = EnumSource.Mode.EXCLUDE,
                names = ["UNKNOWN", "BENEFICIARY_ONLY"],
            )
            fun `nao deve calcular o valor`(amountCalculationModel: AmountCalculationModel) {
                sqsDDABillsHandler.buildBillValidationResponse(
                    ddaBillTO.copy(
                        datavencimento = dueDate.format(dateTimeFormatter),
                        datamora = dueDate.plusDays(1).format(dateTimeFormatter),
                        tipocalculo = amountCalculationModel.code,
                    ),
                ).shouldNotBeNull()
                verify(exactly = 0) {
                    calculator.calculate(any(), any(), any(), any(), any(), any())
                }
            }

            @ParameterizedTest
            @EnumSource(
                AmountCalculationModel::class,
                mode = EnumSource.Mode.INCLUDE,
                names = ["BENEFICIARY_ONLY", "UNKNOWN"],
            )
            fun `deve validar o boleto quando modelo de calculo for`(amountCalculationModel: AmountCalculationModel) {
                sqsDDABillsHandler.buildBillValidationResponse(
                    ddaBillTO.copy(
                        datavencimento = dueDate.format(dateTimeFormatter),
                        datamulta = dueDate.plusDays(1).format(dateTimeFormatter),
                        tipocalculo = amountCalculationModel.code,
                    ),
                ).shouldBeNull()
                verify(exactly = 0) {
                    calculator.calculate(any(), any(), any(), any(), any(), any())
                }
            }
        }

        @Nested
        @DisplayName("e esteja vencido")
        inner class Vencido {

            private val dueDate = getLocalDate().minusDays(1).atStartOfDay()

            @Test
            fun `deve validar a conta antes de adicionar e remover da fila`() {
                every {
                    fichaCompensacaoService.createFichaDeCompensacao(
                        any(),
                        any(),
                        any(),
                    )
                } returns CreateBillResult.SUCCESS(
                    Bill.build(
                        billAdded,
                    ),
                )
                val message = Message.builder().body(
                    getObjectMapper().writeValueAsString(
                        ddaBillTO.copy(
                            tipocalculo = AmountCalculationModel.BENEFICIARY_ONLY.code,
                            datavencimento = dueDate.format(
                                dateTimeFormatter,
                            ),
                            datamora = dueDate.plusDays(1).format(dateTimeFormatter),
                            codmora = InterestType.VALUE.code,
                        ),
                    ),
                ).build()

                val result = sqsDDABillsHandler.handleMessage(message)

                result.shouldDeleteMessage shouldBe true

                verify {
                    fichaCompensacaoService.createFichaDeCompensacao(any(), null, any())
                }
            }

            @ParameterizedTest
            @EnumSource(AmountCalculationModel::class, mode = EnumSource.Mode.INCLUDE, names = ["ANYONE"])
            fun `deve calcular o valor quando o modelo de calculo for`(amountCalculationModel: AmountCalculationModel) {
                val actualDdaBillTO = ddaBillTO.copy(
                    datavencimento = dueDate.format(dateTimeFormatter),
                    datamora = dueDate.plusDays(1).format(dateTimeFormatter),
                    tipocalculo = amountCalculationModel.code,
                )
                sqsDDABillsHandler.buildBillValidationResponse(actualDdaBillTO).shouldNotBeNull()

                verify {
                    calculator.calculate(any(), any(), any(), any(), any(), any(), any())
                }
            }

            @ParameterizedTest
            @EnumSource(
                AmountCalculationModel::class,
                mode = EnumSource.Mode.INCLUDE,
                names = ["BENEFICIARY_AFTER_DUE_DATE", "BENEFICIARY_ONLY", "UNKNOWN"],
            )
            fun `deve validar o boleto quando modelo de calculo for`(amountCalculationModel: AmountCalculationModel) {
                sqsDDABillsHandler.buildBillValidationResponse(
                    ddaBillTO.copy(
                        datavencimento = dueDate.format(dateTimeFormatter),
                        datamora = dueDate.plusDays(1).format(dateTimeFormatter),
                        tipocalculo = amountCalculationModel.code,
                    ),
                ).shouldBeNull()
                verify(exactly = 0) {
                    calculator.calculate(any(), any(), any(), any(), any(), any())
                }
            }
        }
    }

    @Nested
    @DisplayName("caso o titulo tenha desconto")
    inner class BoletoComDesconto {

        private val dueDate = getLocalDate().atStartOfDay()

        private val ddaBillTO: DDABillTO = parseObjectFrom<DDABillTO>(json).copy(
            coddesconto01 = DiscountType.FIXED_UNTIL_DATE.code,
            codmora = InterestType.FREE.code,
            codmulta = FineType.FREE.code,
        )

        @Nested
        @DisplayName("e já tenha passado da data de desconto")
        inner class JaPassouDataDesconto {

            @Test
            fun `pode adicionar a conta e remover da fila sem validar`() {
                val slot = slot<BillValidationResponse>()
                every {
                    fichaCompensacaoService.createFichaDeCompensacao(
                        any(),
                        any(),
                        any(),
                    )
                } returns CreateBillResult.SUCCESS(
                    Bill.build(
                        billAdded,
                    ),
                )

                val message = Message.builder().body(
                    getObjectMapper().writeValueAsString(
                        ddaBillTO.copy(
                            datadesconto01 = getLocalDate().minusDays(1).atStartOfDay().format(dateTimeFormatter),
                        ),
                    ),
                ).build()

                val result = sqsDDABillsHandler.handleMessage(message)

                result.shouldDeleteMessage shouldBe true

                verify {
                    fichaCompensacaoService.createFichaDeCompensacao(any(), capture(slot), any())
                }
                slot.captured shouldNotBe null
                slot.captured.isValid()
            }

            @ParameterizedTest
            @EnumSource(
                AmountCalculationModel::class,
                mode = EnumSource.Mode.EXCLUDE,
                names = ["UNKNOWN", "BENEFICIARY_ONLY"],
            )
            fun `nao deve calcular o valor`(amountCalculationModel: AmountCalculationModel) {
                sqsDDABillsHandler.buildBillValidationResponse(
                    ddaBillTO.copy(
                        datavencimento = dueDate.format(dateTimeFormatter),
                        datadesconto01 = getLocalDate().minusDays(1).atStartOfDay().format(dateTimeFormatter),
                        tipocalculo = amountCalculationModel.code,
                    ),
                ).shouldNotBeNull()
                verify(exactly = 0) {
                    calculator.calculate(any(), any(), any(), any(), any(), any())
                }
            }

            @ParameterizedTest
            @EnumSource(
                AmountCalculationModel::class,
                mode = EnumSource.Mode.INCLUDE,
                names = ["UNKNOWN", "BENEFICIARY_ONLY"],
            )
            fun `deve validar o boleto`(amountCalculationModel: AmountCalculationModel) {
                sqsDDABillsHandler.buildBillValidationResponse(
                    ddaBillTO.copy(
                        datavencimento = dueDate.format(dateTimeFormatter),
                        datadesconto01 = getLocalDate().minusDays(1).atStartOfDay().format(dateTimeFormatter),
                        tipocalculo = amountCalculationModel.code,
                    ),
                ).shouldBeNull()
                verify(exactly = 0) {
                    calculator.calculate(any(), any(), any(), any(), any(), any())
                }
            }
        }

        @Nested
        @DisplayName("e a data de desconto é o vencimento e ainda não passou do vencimento ")
        inner class DataVencimentoComoDataDesconto {

            private val dueDate = getLocalDate().plusDays(1).atStartOfDay()

            private val ddaBillTO: DDABillTO = parseObjectFrom<DDABillTO>(json).copy(
                coddesconto01 = DiscountType.FIXED_UNTIL_DATE.code,
                datadesconto01 = null,
                datavencimento = dueDate.format(dateTimeFormatter),
                codmora = InterestType.FREE.code,
                codmulta = FineType.FREE.code,
            )

            @Test
            fun `deve validar a conta antes de adicionar e remover da fila`() {
                every {
                    fichaCompensacaoService.createFichaDeCompensacao(
                        any(),
                        any(),
                        any(),
                    )
                } returns CreateBillResult.SUCCESS(
                    Bill.build(
                        billAdded,
                    ),
                )

                val message = Message.builder().body(
                    getObjectMapper().writeValueAsString(
                        ddaBillTO.copy(
                            tipocalculo = AmountCalculationModel.BENEFICIARY_ONLY.code,
                            coddesconto01 = DiscountType.PERCENT_UNTIL_DATE.code,
                        ),
                    ),
                ).build()

                val result = sqsDDABillsHandler.handleMessage(message)

                result.shouldDeleteMessage shouldBe true

                verify {
                    fichaCompensacaoService.createFichaDeCompensacao(any(), null, any())
                }
            }

            @ParameterizedTest
            @EnumSource(
                AmountCalculationModel::class,
                mode = EnumSource.Mode.INCLUDE,
                names = ["ANYONE", "BENEFICIARY_AFTER_DUE_DATE"],
            )
            fun `deve calcular o valor quando o modelo de calculo for`(amountCalculationModel: AmountCalculationModel) {
                val actualDdaBillTO = ddaBillTO.copy(
                    tipocalculo = amountCalculationModel.code,
                )
                sqsDDABillsHandler.buildBillValidationResponse(actualDdaBillTO).shouldNotBeNull()
                verify {
                    calculator.calculate(any(), any(), any(), any(), any(), any(), any())
                }
            }

            @ParameterizedTest
            @EnumSource(
                AmountCalculationModel::class,
                mode = EnumSource.Mode.INCLUDE,
                names = ["BENEFICIARY_ONLY", "UNKNOWN"],
            )
            fun `deve validar o boleto quando o modelo de calculo for `(amountCalculationModel: AmountCalculationModel) {
                sqsDDABillsHandler.buildBillValidationResponse(
                    ddaBillTO.copy(
                        tipocalculo = amountCalculationModel.code,
                    ),
                ).shouldBeNull()
                verify(exactly = 0) {
                    calculator.calculate(any(), any(), any(), any(), any(), any())
                }
            }

            @Nested
            @DisplayName("e tiver mais de uma data de desconto")
            inner class ComMaisDeUmaDataDeDesconto {
                @ParameterizedTest
                @EnumSource(
                    AmountCalculationModel::class,
                    mode = EnumSource.Mode.INCLUDE,
                    names = ["ANYONE", "BENEFICIARY_AFTER_DUE_DATE"],
                )
                fun `deve validar o boleto com 2 datas de desconto quando o modelo de calculo for `(
                    amountCalculationModel: AmountCalculationModel,
                ) {
                    val dataDesconto01 = dueDate.minusDays(1).format(dateTimeFormatter)
                    val actualDdaBillTO = ddaBillTO.copy(
                        tipocalculo = amountCalculationModel.code,
                        coddesconto01 = DiscountType.FIXED_UNTIL_DATE.code,
                        datadesconto01 = dataDesconto01,
                        vlrpercdesconto01 = 20.0,
                        coddesconto02 = DiscountType.FIXED_UNTIL_DATE.code,
                        datadesconto02 = null,
                        vlrpercdesconto02 = 10.0,
                    )
                    sqsDDABillsHandler.buildBillValidationResponse(
                        actualDdaBillTO,
                    ).shouldNotBeNull()
                    verify {
                        calculator.calculate(any(), any(), any(), any(), any(), any(), any())
                    }
                }

                @ParameterizedTest
                @EnumSource(
                    AmountCalculationModel::class,
                    mode = EnumSource.Mode.INCLUDE,
                    names = ["ANYONE", "BENEFICIARY_AFTER_DUE_DATE"],
                )
                fun `deve validar o boleto com 3 datas de desconto quando o modelo de calculo for `(
                    amountCalculationModel: AmountCalculationModel,
                ) {
                    val dataDesconto01 = dueDate.minusDays(2).format(dateTimeFormatter)
                    val dataDesconto02 = dueDate.minusDays(1).format(dateTimeFormatter)
                    val actualDdaBillTO = ddaBillTO.copy(
                        tipocalculo = amountCalculationModel.code,
                        coddesconto01 = DiscountType.FIXED_UNTIL_DATE.code,
                        datadesconto01 = dataDesconto01,
                        vlrpercdesconto01 = 20.0,
                        coddesconto02 = DiscountType.FIXED_UNTIL_DATE.code,
                        datadesconto02 = dataDesconto02,
                        vlrpercdesconto02 = 10.0,
                        coddesconto03 = DiscountType.FIXED_UNTIL_DATE.code,
                        datadesconto03 = null,
                        vlrpercdesconto03 = 5.0,

                    )
                    sqsDDABillsHandler.buildBillValidationResponse(
                        actualDdaBillTO,
                    ).shouldNotBeNull()

                    verify {
                        calculator.calculate(any(), any(), any(), any(), any(), any(), any())
                    }
                }
            }
        }

        @Nested
        @DisplayName("e não passou a data de desconto ")
        inner class TemDescontoAtivo {

            private val dueDate = getLocalDate().plusDays(5).atStartOfDay()

            private val ddaBillTO: DDABillTO = parseObjectFrom<DDABillTO>(json).copy(
                coddesconto01 = DiscountType.FIXED_UNTIL_DATE.code,
                datavencimento = dueDate.format(dateTimeFormatter),
                datadesconto01 = getLocalDate().plusDays(2).atStartOfDay().format(dateTimeFormatter),
                codmora = InterestType.FREE.code,
                codmulta = FineType.FREE.code,
            )

            @Test
            fun `deve validar a conta antes de adicionar e remover da fila`() {
                every {
                    fichaCompensacaoService.createFichaDeCompensacao(
                        any(),
                        any(),
                        any(),
                    )
                } returns CreateBillResult.SUCCESS(
                    Bill.build(
                        billAdded,
                    ),
                )

                val message = Message.builder().body(
                    getObjectMapper().writeValueAsString(
                        ddaBillTO.copy(
                            tipocalculo = AmountCalculationModel.BENEFICIARY_ONLY.code,
                            coddesconto01 = DiscountType.PERCENT_UNTIL_DATE.code,
                        ),
                    ),
                ).build()

                val result = sqsDDABillsHandler.handleMessage(message)

                result.shouldDeleteMessage shouldBe true

                verify {
                    fichaCompensacaoService.createFichaDeCompensacao(any(), null, any())
                }
            }

            @ParameterizedTest
            @EnumSource(
                AmountCalculationModel::class,
                mode = EnumSource.Mode.INCLUDE,
                names = ["ANYONE", "BENEFICIARY_AFTER_DUE_DATE"],
            )
            fun `deve calcular o valor quando o modelo de calculo for`(amountCalculationModel: AmountCalculationModel) {
                val actualDdaBillTO = ddaBillTO.copy(
                    tipocalculo = amountCalculationModel.code,
                )
                sqsDDABillsHandler.buildBillValidationResponse(actualDdaBillTO).shouldNotBeNull()

                verify {
                    calculator.calculate(any(), any(), any(), any(), any(), any(), any())
                }
            }

            @ParameterizedTest
            @EnumSource(
                AmountCalculationModel::class,
                mode = EnumSource.Mode.INCLUDE,
                names = ["BENEFICIARY_ONLY", "UNKNOWN"],
            )
            fun `deve validar o boleto quando o modelo de calculo for `(amountCalculationModel: AmountCalculationModel) {
                sqsDDABillsHandler.buildBillValidationResponse(
                    ddaBillTO.copy(
                        tipocalculo = amountCalculationModel.code,
                    ),
                ).shouldBeNull()
                verify(exactly = 0) {
                    calculator.calculate(any(), any(), any(), any(), any(), any())
                }
            }
        }

        @Nested
        @DisplayName("tem mais de uma data e não passou de uma das datas de desconto ")
        inner class TemMaisDeUmaDataDeDesconto {

            private val ddaBillTO: DDABillTO = parseObjectFrom<DDABillTO>(json).copy(
                coddesconto01 = DiscountType.FIXED_UNTIL_DATE.code,
                coddesconto02 = DiscountType.FIXED_UNTIL_DATE.code,
                coddesconto03 = DiscountType.FIXED_UNTIL_DATE.code,
                datadesconto01 = getLocalDate().minusDays(1).atStartOfDay().format(dateTimeFormatter),
                datadesconto02 = getLocalDate().atStartOfDay().format(dateTimeFormatter),
                datadesconto03 = getLocalDate().plusDays(1).atStartOfDay().format(dateTimeFormatter),
                datavencimento = getLocalDate().plusDays(2).atStartOfDay().format(dateTimeFormatter),
                codmora = InterestType.FREE.code,
                codmulta = FineType.FREE.code,
            )

            @Test
            fun `deve validar a conta antes de adicionar e remover da fila`() {
                every {
                    fichaCompensacaoService.createFichaDeCompensacao(
                        any(),
                        any(),
                        any(),
                    )
                } returns CreateBillResult.SUCCESS(
                    Bill.build(
                        billAdded,
                    ),
                )

                val message = Message.builder().body(
                    getObjectMapper().writeValueAsString(
                        ddaBillTO.copy(
                            tipocalculo = AmountCalculationModel.BENEFICIARY_ONLY.code,
                            coddesconto01 = DiscountType.PERCENT_UNTIL_DATE.code,
                        ),
                    ),
                ).build()

                val result = sqsDDABillsHandler.handleMessage(message)

                result.shouldDeleteMessage shouldBe true

                verify {
                    fichaCompensacaoService.createFichaDeCompensacao(any(), null, any())
                }
            }

            @ParameterizedTest
            @EnumSource(
                AmountCalculationModel::class,
                mode = EnumSource.Mode.INCLUDE,
                names = ["ANYONE", "BENEFICIARY_AFTER_DUE_DATE"],
            )
            fun `deve calcular o valor quando o modelo de calculo for`(amountCalculationModel: AmountCalculationModel) {
                val actualDdaBillTO = ddaBillTO.copy(
                    tipocalculo = amountCalculationModel.code,
                )
                sqsDDABillsHandler.buildBillValidationResponse(actualDdaBillTO).shouldNotBeNull()
                verify {
                    calculator.calculate(any(), any(), any(), any(), any(), any(), any())
                }
            }

            @ParameterizedTest
            @EnumSource(
                AmountCalculationModel::class,
                mode = EnumSource.Mode.INCLUDE,
                names = ["BENEFICIARY_ONLY", "UNKNOWN"],
            )
            fun `deve validar o boleto quando o modelo de calculo for `(amountCalculationModel: AmountCalculationModel) {
                sqsDDABillsHandler.buildBillValidationResponse(
                    ddaBillTO.copy(
                        tipocalculo = amountCalculationModel.code,
                    ),
                ).shouldBeNull()
                verify(exactly = 0) {
                    calculator.calculate(any(), any(), any(), any(), any(), any())
                }
            }
        }
    }

    @Nested
    @DisplayName("caso o título já exista e não possua multa, juros ou desconto")
    inner class BoletoExistente {
        @Test
        fun `pode adicionar a conta e remover da fila`() {
            every { ddaRepository.find(barCode = any(), document = any(), dueDate = any()) } returns DDABill(
                barcode = BarCode.ofDigitable(DIGITABLE_LINE),
                document = "***********",
                dueDate = getLocalDate(),
                walletId = WalletId(ACCOUNT_ID),
                billId = BillId(BILL_ID),
                lastStatus = BillStatus.ACTIVE,
            )

            every { updateBillService.synchronizeBill(BillId(BILL_ID), any()) } returns SynchronizeBillResponse(
                BillSynchronizationStatus.BillNotModified,
            )

            val slot = slot<BillValidationResponse>()

            val resource = Thread.currentThread().contextClassLoader.getResource("arbi/DDAInformation.json")
            val message = Message.builder()
                .body(IOUtils.toString(FileInputStream(resource!!.path), StandardCharsets.UTF_8.name())).build()

            val result = sqsDDABillsHandler.handleMessage(message)

            result.shouldDeleteMessage shouldBe true

            verify {
                updateBillService.synchronizeBill(BillId(BILL_ID), capture(slot))
                billValidationService wasNot called
            }

            slot.captured.billRegisterData?.amountTotal shouldBe 304255L
        }
    }

    @Test
    fun `caso nao encontre o usuario do titulo deve apagar a mensagem`() {
        every { accountRepository.findAccountByDocument(any()) } throws AccountNotFoundException("")

        val resource = Thread.currentThread().contextClassLoader.getResource("arbi/DDAInformation.json")
        val message =
            Message.builder().body(IOUtils.toString(FileInputStream(resource!!.path), StandardCharsets.UTF_8.name()))
                .build()

        val result = sqsDDABillsHandler.handleMessage(message)

        result.shouldDeleteMessage shouldBe true
    }

    @Test
    fun `caso de erro na criacao do titulo nao deve apagar a mensagem`() {
        every {
            fichaCompensacaoService.createFichaDeCompensacao(
                any(),
                any(),
                any(),
            )
        } returns CreateBillResult.FAILURE.ServerError(
            NoStackTraceException(),
        )

        val resource = Thread.currentThread().contextClassLoader.getResource("arbi/DDAInformation.json")
        val message =
            Message.builder().body(IOUtils.toString(FileInputStream(resource!!.path), StandardCharsets.UTF_8.name()))
                .build()

        val result = sqsDDABillsHandler.handleMessage(message)

        result.shouldDeleteMessage shouldBe false

        verify {
            fichaCompensacaoService.createFichaDeCompensacao(any(), any(), any())
        }
    }

    @Test
    fun `deve apagar a mensagem quando ocorrer erro não retentável na criacao do titulo`() {
        every {
            fichaCompensacaoService.createFichaDeCompensacao(
                any(),
                any(),
                any(),
            )
        } returns CreateBillResult.FAILURE.BillUnableToValidate(
            "107: Grupo de cálculo não informado!",
            false,
        )

        val resource = Thread.currentThread().contextClassLoader.getResource("arbi/DDAInformation.json")
        val message =
            Message.builder().body(IOUtils.toString(FileInputStream(resource!!.path), StandardCharsets.UTF_8.name()))
                .build()

        val result = sqsDDABillsHandler.handleMessage(message)

        result.shouldDeleteMessage shouldBe true

        verify {
            fichaCompensacaoService.createFichaDeCompensacao(any(), any(), any())
        }
    }

    @Test
    fun `quando o pagamento deve ser calculado mas o calculo não devolver resultado não deve retornar uma resposta`() {
        every { calculator.calculate(any(), any(), any(), any(), any(), any(), any()) } returns ServerError().left()
        val resource = Thread.currentThread().contextClassLoader.getResource("arbi/DDAInformation.json")
        val json = IOUtils.toString(FileInputStream(resource!!.path), StandardCharsets.UTF_8.name())
        val ddaBillTO: DDABillTO =
            parseObjectFrom<DDABillTO>(json).copy(
                tipocalculo = AmountCalculationModel.ANYONE.code,
                datavencimento = "2022-07-13T00:00:00",
            )
        sqsDDABillsHandler.buildBillValidationResponse(ddaBillTO) shouldBe null
    }

    @Nested
    @DisplayName("baixa efetiva")
    inner class BaixasEfetivas {

        @Nested
        @DisplayName("com multiplas baixas")
        inner class MultiplosValores {
            @Test
            internal fun `deve agrupar os valores das baixas com mesmo identificador e considerar o somatório dos valores`() {
                val resource = Thread.currentThread().contextClassLoader.getResource("arbi/DDAInformation.json")
                val json = IOUtils.toString(FileInputStream(resource!!.path), StandardCharsets.UTF_8.name())
                val ddaBillTO: DDABillTO =
                    parseObjectFrom<DDABillTO>(json).copy(
                        baixasEfetivas = listOf(
                            baixa,
                            baixa.copy(vlrbaixa = 100.0),
                            baixa.copy(vlrbaixa = 50.0, numidentbaixa = 2022070600352635742),
                        ),
                    )
                val validationResponse = ddaBillTO.toValidationResponse(paymentTimeLimitConfig)
                validationResponse!!.billRegisterData!!.amountPaid shouldBe 15000
                validationResponse.billRegisterData!!.paidDate shouldBe getLocalDate()
            }

            @Test
            internal fun `e todas com mesmo identificador, deve considerar a última baixa com valor diferente de zero`() {
                val resource = Thread.currentThread().contextClassLoader.getResource("arbi/DDAInformation.json")
                val json = IOUtils.toString(FileInputStream(resource!!.path), StandardCharsets.UTF_8.name())
                val ddaBillTO: DDABillTO =
                    parseObjectFrom<DDABillTO>(json).copy(
                        baixasEfetivas = listOf(
                            baixa.copy(
                                dataprocbaixa = getZonedDateTime().minusDays(1).format(dateTimeFormatter),
                            ),
                            baixa.copy(vlrbaixa = 100.0, dataprocbaixa = getZonedDateTime().format(dateTimeFormatter)),
                        ),
                    )
                val validationResponse = ddaBillTO.toValidationResponse(paymentTimeLimitConfig)
                validationResponse!!.billRegisterData!!.amountPaid shouldBe 10000
                validationResponse.billRegisterData!!.paidDate shouldBe getZonedDateTime().toLocalDate()
            }

            @Test
            internal fun `mesmo identificador e valores iguais, deve considerar apenas um dos valores`() {
                val resource = Thread.currentThread().contextClassLoader.getResource("arbi/DDAInformation.json")
                val json = IOUtils.toString(FileInputStream(resource!!.path), StandardCharsets.UTF_8.name())
                val ddaBillTO: DDABillTO =
                    parseObjectFrom<DDABillTO>(json).copy(
                        baixasEfetivas = listOf(
                            baixa.copy(vlrbaixa = 100.0),
                            baixa.copy(vlrbaixa = 100.0),
                        ),
                    )
                val validationResponse = ddaBillTO.toValidationResponse(paymentTimeLimitConfig)
                validationResponse!!.billRegisterData!!.amountPaid shouldBe 10000
                validationResponse.billRegisterData!!.paidDate shouldBe getLocalDate()
            }

            @Test
            internal fun `mesmo identificador mas valor zerado, deve considerar zero`() {
                val resource = Thread.currentThread().contextClassLoader.getResource("arbi/DDAInformation.json")
                val json = IOUtils.toString(FileInputStream(resource!!.path), StandardCharsets.UTF_8.name())
                val ddaBillTO: DDABillTO =
                    parseObjectFrom<DDABillTO>(json).copy(
                        baixasEfetivas = listOf(
                            baixa.copy(vlrbaixa = 0.0),
                            baixa.copy(vlrbaixa = 0.0),
                        ),
                    )
                val validationResponse = ddaBillTO.toValidationResponse(paymentTimeLimitConfig)
                validationResponse!!.billRegisterData!!.amountPaid shouldBe 0
                validationResponse.billRegisterData!!.paidDate shouldBe null
            }
        }

        @Nested
        @DisplayName("com apenas uma baixa")
        inner class UmaBaixa {
            @Test
            internal fun `deve considerar como zero caso haja apenas uma baixa efetiva de valor zerado`() {
                val resource = Thread.currentThread().contextClassLoader.getResource("arbi/DDAInformation.json")
                val json = IOUtils.toString(FileInputStream(resource!!.path), StandardCharsets.UTF_8.name())
                val ddaBillTO: DDABillTO =
                    parseObjectFrom<DDABillTO>(json).copy(
                        baixasEfetivas = listOf(
                            baixa,
                        ),
                    )
                val validationResponse = ddaBillTO.toValidationResponse(paymentTimeLimitConfig)
                validationResponse!!.billRegisterData!!.amountPaid shouldBe 0
                validationResponse.billRegisterData!!.paidDate shouldBe null
            }

            @Test
            internal fun `deve considerar o valor da baixa caso haja apenas uma baixa efetiva de valor não zerado`() {
                val resource = Thread.currentThread().contextClassLoader.getResource("arbi/DDAInformation.json")
                val json = IOUtils.toString(FileInputStream(resource!!.path), StandardCharsets.UTF_8.name())
                val ddaBillTO: DDABillTO =
                    parseObjectFrom<DDABillTO>(json).copy(
                        baixasEfetivas = listOf(
                            baixa.copy(vlrbaixa = 100.0),
                        ),
                    )
                val validationResponse = ddaBillTO.toValidationResponse(paymentTimeLimitConfig)
                validationResponse!!.billRegisterData!!.amountPaid shouldBe 10000
                validationResponse.billRegisterData!!.paidDate shouldBe getLocalDate()
            }
        }
    }

    @Nested
    @DisplayName("baixa operacional")
    inner class BaixasOperacionais {

        @Nested
        @DisplayName("com multiplas baixas")
        inner class MultiplosValores {
            @Test
            internal fun `deve agrupar os valores das baixas com mesmo identificador e considerar o somatório dos valores`() {
                val resource = Thread.currentThread().contextClassLoader.getResource("arbi/DDAInformation.json")
                val json = IOUtils.toString(FileInputStream(resource!!.path), StandardCharsets.UTF_8.name())
                val ddaBillTO: DDABillTO =
                    parseObjectFrom<DDABillTO>(json).copy(
                        baixasOperacionais = listOf(
                            baixa,
                            baixa.copy(vlrbaixa = 100.0),
                            baixa.copy(vlrbaixa = 50.0, numidentbaixa = 2022070600352635742),
                        ),
                    )
                val validationResponse = ddaBillTO.toValidationResponse(paymentTimeLimitConfig)
                validationResponse!!.billRegisterData!!.amountPaid shouldBe 15000
                validationResponse.billRegisterData!!.paidDate shouldBe getLocalDate()
            }

            @Test
            internal fun `e todas com mesmo identificador, deve considerar a última baixa com valor diferente de zero`() {
                val resource = Thread.currentThread().contextClassLoader.getResource("arbi/DDAInformation.json")
                val json = IOUtils.toString(FileInputStream(resource!!.path), StandardCharsets.UTF_8.name())
                val ddaBillTO: DDABillTO =
                    parseObjectFrom<DDABillTO>(json).copy(
                        baixasOperacionais = listOf(
                            baixa.copy(
                                dataprocbaixa = getZonedDateTime().minusDays(1).format(dateTimeFormatter),
                            ),
                            baixa.copy(vlrbaixa = 100.0),
                        ),
                    )
                val validationResponse = ddaBillTO.toValidationResponse(paymentTimeLimitConfig)
                validationResponse!!.billRegisterData!!.amountPaid shouldBe 10000
                validationResponse.billRegisterData!!.paidDate shouldBe getLocalDate()
            }

            @Test
            internal fun `mesmo identificador e valores iguais, deve considerar apenas um dos valores`() {
                val resource = Thread.currentThread().contextClassLoader.getResource("arbi/DDAInformation.json")
                val json = IOUtils.toString(FileInputStream(resource!!.path), StandardCharsets.UTF_8.name())
                val ddaBillTO: DDABillTO =
                    parseObjectFrom<DDABillTO>(json).copy(
                        baixasOperacionais = listOf(
                            baixa.copy(vlrbaixa = 100.0),
                            baixa.copy(vlrbaixa = 100.0),
                        ),
                    )
                val validationResponse = ddaBillTO.toValidationResponse(paymentTimeLimitConfig)
                validationResponse!!.billRegisterData!!.amountPaid shouldBe 10000
                validationResponse.billRegisterData!!.paidDate shouldBe getLocalDate()
            }

            @Test
            internal fun `mesmo identificador mas valor zerado, deve considerar zero`() {
                val resource = Thread.currentThread().contextClassLoader.getResource("arbi/DDAInformation.json")
                val json = IOUtils.toString(FileInputStream(resource!!.path), StandardCharsets.UTF_8.name())
                val ddaBillTO: DDABillTO =
                    parseObjectFrom<DDABillTO>(json).copy(
                        baixasOperacionais = listOf(
                            baixa.copy(vlrbaixa = 0.0),
                            baixa.copy(vlrbaixa = 0.0),
                        ),
                    )
                val validationResponse = ddaBillTO.toValidationResponse(paymentTimeLimitConfig)
                validationResponse!!.billRegisterData!!.amountPaid shouldBe 0
                validationResponse.billRegisterData!!.paidDate shouldBe null
            }
        }

        @Test
        fun `quando a validação está sem situação de baixa deve retornar null`() {
            val resource = Thread.currentThread().contextClassLoader.getResource("arbi/DDAInformation.json")
            val json = IOUtils.toString(FileInputStream(resource!!.path), StandardCharsets.UTF_8.name())
            val ddaBillTO: DDABillTO =
                parseObjectFrom<DDABillTO>(json).copy(
                    sitpagamentocip = "",
                )
            val validationResponse = ddaBillTO.toValidationResponse(paymentTimeLimitConfig)
            validationResponse shouldBe null
        }

        @Nested
        @DisplayName("com apenas uma baixa")
        inner class UmaBaixa {
            @Test
            internal fun `deve considerar como zero caso haja apenas uma baixa efetiva de valor zerado`() {
                val resource = Thread.currentThread().contextClassLoader.getResource("arbi/DDAInformation.json")
                val json = IOUtils.toString(FileInputStream(resource!!.path), StandardCharsets.UTF_8.name())
                val ddaBillTO: DDABillTO =
                    parseObjectFrom<DDABillTO>(json).copy(
                        baixasOperacionais = listOf(
                            baixa,
                        ),
                    )
                val validationResponse = ddaBillTO.toValidationResponse(paymentTimeLimitConfig)
                validationResponse!!.billRegisterData!!.amountPaid shouldBe 0
                validationResponse.billRegisterData!!.paidDate shouldBe null
            }

            @Test
            internal fun `deve considerar o valor da baixa caso haja apenas uma baixa efetiva de valor não zerado`() {
                val resource = Thread.currentThread().contextClassLoader.getResource("arbi/DDAInformation.json")
                val json = IOUtils.toString(FileInputStream(resource!!.path), StandardCharsets.UTF_8.name())
                val ddaBillTO: DDABillTO =
                    parseObjectFrom<DDABillTO>(json).copy(
                        baixasOperacionais = listOf(
                            baixa.copy(vlrbaixa = 100.0),
                        ),
                    )
                val validationResponse = ddaBillTO.toValidationResponse(paymentTimeLimitConfig)
                validationResponse!!.billRegisterData!!.amountPaid shouldBe 10000
                validationResponse.billRegisterData!!.paidDate shouldBe getLocalDate()
            }
        }
    }
}