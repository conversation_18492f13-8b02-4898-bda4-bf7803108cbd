package ai.friday.billpayment.adapters.messaging

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.adapters.dynamodb.BillComingDueDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillComingDueDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.bill.BillComingDue
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.FichaCompensacaoAdded
import ai.friday.billpayment.app.bill.UpdatedRegisterData
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billRegisterUpdatedNewDueDate
import ai.friday.billpayment.billScheduleCanceled
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.timeFormat
import ai.friday.morning.date.withGivenDateTime
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import java.time.LocalDate
import java.time.LocalTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message

internal class BillComingDueHandlerTest {

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbEnhancedClient = setupDynamoDB()
    private val repository =
        BillComingDueDBRepository(BillComingDueDynamoDAO(dynamoDbEnhancedClient), mockk(relaxed = true))

    private val billComingDueHandler = BillComingDueHandler(
        amazonSQS = mockk(),
        amazonSNS = mockk(),
        configuration = mockk() {
            every { billComingDueQueueName } returns ""
        },
        topicArn = "",
        billComingDueRepository = repository,
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
    }

    @Nested
    @DisplayName("quando for um evento de criação de ficha de compensação")
    inner class FichaCompensacaoAddedContext {

        @Test
        fun `deve criar o registro billComingDue quando não estiver vencido`() {
            val fichaCompensacaoAdded = billAddedFicha.copy(
                dueDate = LocalDate.now().plusDays(1),
                effectiveDueDate = LocalDate.now().plusDays(1),
            )

            val result = billComingDueHandler.handleMessage(buildMessage(fichaCompensacaoAdded))

            val billComingDue = repository.findById(fichaCompensacaoAdded.billId).shouldNotBeNull()
            assertSoftly {
                with(billComingDue) {
                    billId shouldBe fichaCompensacaoAdded.billId
                    walletId shouldBe fichaCompensacaoAdded.walletId
                    dueDate shouldBe fichaCompensacaoAdded.effectiveDueDate
                    paymentLimitTime shouldBe fichaCompensacaoAdded.paymentLimitTime
                    isScheduled shouldBe false
                }
                result.shouldDeleteMessage shouldBe true
            }
        }

        @Test
        fun `deve criar o registro billComingDue quando o vencimento for hoje mas não ultrapassou o horario limite de pagamento`() {
            val fichaCompensacaoAdded = billAddedFicha.copy(
                dueDate = LocalDate.now(),
                effectiveDueDate = LocalDate.now(),
                paymentLimitTime = LocalTime.parse("20:00", timeFormat),
            )

            withGivenDateTime(getZonedDateTime().withHour(12)) {
                val result = billComingDueHandler.handleMessage(buildMessage(fichaCompensacaoAdded))

                val billComingDue = repository.findById(fichaCompensacaoAdded.billId).shouldNotBeNull()
                assertSoftly {
                    with(billComingDue) {
                        billId shouldBe fichaCompensacaoAdded.billId
                        walletId shouldBe fichaCompensacaoAdded.walletId
                        dueDate shouldBe fichaCompensacaoAdded.effectiveDueDate
                        paymentLimitTime shouldBe fichaCompensacaoAdded.paymentLimitTime
                        isScheduled shouldBe false
                    }
                    result.shouldDeleteMessage shouldBe true
                }
            }
        }

        @Test
        fun `não deve criar o registro billComingDue se já estiver vencido`() {
            val fichaCompensacaoAdded = billAddedFicha.copy(
                dueDate = getLocalDate(),
                effectiveDueDate = getLocalDate(),
                paymentLimitTime = LocalTime.parse("20:00", timeFormat),
            )

            val result = withGivenDateTime(getZonedDateTime().withHour(21)) {
                billComingDueHandler.handleMessage(buildMessage(fichaCompensacaoAdded))
            }

            repository.findById(fichaCompensacaoAdded.billId).shouldBeNull()
            result.shouldDeleteMessage shouldBe true
        }
    }

    @Nested
    @DisplayName("quando for um evento de alteração de data de vencimento")
    inner class BillRegisterUpdatedNewDueDateContext {

        @Test
        fun `deve atualizar a data de vencimento do registro`() {
            val fichaCompensacaoAdded = billAddedFicha.copy(
                dueDate = LocalDate.now().plusDays(1),
                effectiveDueDate = LocalDate.now().plusDays(2),
            )

            loadBillComingDue(fichaCompensacaoAdded)

            repository.findById(fichaCompensacaoAdded.billId)
                .shouldNotBeNull().dueDate shouldBe fichaCompensacaoAdded.effectiveDueDate

            val updatedRegisterData = UpdatedRegisterData.NewDueDate(
                dueDate = LocalDate.now().plusDays(8),
                effectiveDueDate = LocalDate.now().plusDays(10),
            )

            val result = billComingDueHandler.handleMessage(
                buildMessage(
                    billRegisterUpdatedNewDueDate.copy(
                        billId = fichaCompensacaoAdded.billId,
                        updatedRegisterData = updatedRegisterData,
                    ),
                ),
            )

            val billComingDue = repository.findById(fichaCompensacaoAdded.billId).shouldNotBeNull()

            billComingDue.dueDate shouldBe updatedRegisterData.effectiveDueDate

            result.shouldDeleteMessage shouldBe true
        }
    }

    @Nested
    @DisplayName("quando for um evento de agendamento")
    inner class BillPaymentScheduledContext {

        @Test
        fun `deve marcar o registro como agendado`() {
            val fichaCompensacaoAdded = billAddedFicha.copy(
                dueDate = LocalDate.now().plusDays(1),
                effectiveDueDate = LocalDate.now().plusDays(2),
            )

            loadBillComingDue(fichaCompensacaoAdded)

            repository.findById(fichaCompensacaoAdded.billId)
                .shouldNotBeNull()
                .isScheduled.shouldBeFalse()

            val result = billComingDueHandler.handleMessage(
                buildMessage(
                    billPaymentScheduled.copy(
                        billId = fichaCompensacaoAdded.billId,
                    ),
                ),
            )

            repository.findById(fichaCompensacaoAdded.billId)
                .shouldNotBeNull()
                .isScheduled.shouldBeTrue()

            result.shouldDeleteMessage shouldBe true
        }
    }

    @Nested
    @DisplayName("quando for um evento de cancelamento de agendamento")
    inner class BillPaymentScheduleCanceledContext {

        @Test
        fun `deve marcar o registro como aberto`() {
            val fichaCompensacaoAdded = billAddedFicha.copy(
                dueDate = LocalDate.now().plusDays(1),
                effectiveDueDate = LocalDate.now().plusDays(2),
            )

            fichaCompensacaoAdded
                .toBillComingDue()
                .copy(isScheduled = true)
                .save()

            repository.findById(fichaCompensacaoAdded.billId)
                .shouldNotBeNull()
                .isScheduled.shouldBeTrue()

            val result = billComingDueHandler.handleMessage(
                buildMessage(
                    billScheduleCanceled.copy(
                        billId = fichaCompensacaoAdded.billId,
                    ),
                ),
            )

            repository.findById(fichaCompensacaoAdded.billId)
                .shouldNotBeNull()
                .isScheduled.shouldBeFalse()

            result.shouldDeleteMessage shouldBe true
        }
    }

    private fun loadBillComingDue(event: FichaCompensacaoAdded) {
        event.toBillComingDue().save()
    }

    private fun BillComingDue.save() {
        repository.save(this)
    }

    private fun FichaCompensacaoAdded.toBillComingDue() = BillComingDue(
        billId = this.billId,
        walletId = this.walletId,
        created = getZonedDateTime(),
        dueDate = this.effectiveDueDate,
        paymentLimitTime = this.paymentLimitTime,
        isScheduled = false,
    )

    private fun buildMessage(event: BillEvent) = Message.builder()
        .body(
            getObjectMapper().writeValueAsString(
                event.toBillEventDetailEntity(),
            ),
        )
        .build()
}