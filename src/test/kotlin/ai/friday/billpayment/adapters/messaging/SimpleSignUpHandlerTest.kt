package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.register.SimpleSignUpPendingMessage
import ai.friday.billpayment.app.register.SimpleSignUpService
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message

class SimpleSignUpHandlerTest {
    private val simpleSignUpService: SimpleSignUpService = mockk(relaxed = true) {
        every {
            continueSignUp(any())
        } returns SimpleSignUpPendingMessage(
            livenessId = "",
            userContractKey = "",
            userContractSignature = "",
            selfieCaptured = false,
            userContractCaptured = false,
            kycDossierGenerated = false,
            hasPassedRiskAnalysis = false,
            ecmDocumentsSent = false,
            accountActivated = false,
            deduplicationVerified = false,
            accountId = "",
            identityVerified = false,
        ).right()
    }

    private val handler = SimpleSignUpHandler(mockk(relaxed = true), mockk(relaxed = true), simpleSignUpService)

    private val simpleSignUpMessage = SimpleSignUpPendingMessage(
        livenessId = "",
        userContractKey = "",
        userContractSignature = "",
        selfieCaptured = false,
        userContractCaptured = false,
        kycDossierGenerated = false,
        hasPassedRiskAnalysis = false,
        ecmDocumentsSent = false,
        accountActivated = false,
        deduplicationVerified = false,
        accountId = "",
        identityVerified = false,
    )

    private val message = Message.builder().body(getObjectMapper().writeValueAsString(simpleSignUpMessage))
        .build()

    @Test
    fun `deve excluir a mensagem da fila caso não dê erro`() {
        val response = handler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true
    }

    @Test
    fun `não deve excluir a mensagem da fila se o erro não for de análise de risco`() {
        val response = handler.handleError(message, IllegalStateException())

        response.shouldDeleteMessage shouldBe false
    }
}