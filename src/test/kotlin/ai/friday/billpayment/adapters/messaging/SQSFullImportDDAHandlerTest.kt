package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.dda.DDAFullImportResult
import ai.friday.billpayment.app.dda.DDARegister
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.dda.DDAStatus
import ai.friday.billpayment.app.integrations.DDARepository
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

internal class SQSFullImportDDAHandlerTest {

    @Nested
    @DisplayName("ao receber uma mensagem com o registro de dda")
    internal class WhenReceiveMessage {
        private val message = mockk<Message>(relaxed = true)
        private val sqsClient = mockk<SqsClient>(relaxed = true)
        private val ddaService = mockk<DDAService>(relaxed = true)
        private val configuration = mockk<SQSMessageHandlerConfiguration>(relaxed = true)
        private val ddaRepository = mockk<DDARepository>()

        private val handler = SQSFullImportDDAHandler(sqsClient, configuration, ddaService, ddaRepository)

        @Test
        fun `deve invocar a importação do registro de DDA se o status for REQUESTED`() {
            every { message.body() } returns """{ "accountId": {"value": "${REQUESTED_DDA_REGISTER.accountId.value}"}, "document":"$DOCUMENT" }"""
            every { ddaRepository.find(REQUESTED_DDA_REGISTER.accountId) } returns REQUESTED_DDA_REGISTER

            every { ddaService.executeFullImport(REQUESTED_DDA_REGISTER) } returns DDAFullImportResult.Imported
            handler.handleMessage(message) shouldBe SQSHandlerResponse(true)
            verify { ddaService.executeFullImport(REQUESTED_DDA_REGISTER) }
        }

        @ParameterizedTest
        @EnumSource(DDAStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["REQUESTED"])
        fun `não deve invocar a importação do registro de DDA se o status for diferente de REQUESTED`(ddaStatus: DDAStatus) {
            every { message.body() } returns """{ "accountId": {"value": "${NOT_REQUESTED_DDA_REGISTER.accountId.value}"}, "document":"$DOCUMENT" }"""
            every { ddaRepository.find(NOT_REQUESTED_DDA_REGISTER.accountId) } returns NOT_REQUESTED_DDA_REGISTER.copy(
                status = ddaStatus,
            )

            handler.handleMessage(message) shouldBe SQSHandlerResponse(true)
            verify { ddaService wasNot Called }
        }

        @Test
        fun `não deve invocar a importação se o registro de DDA não for encontrado`() {
            every { message.body() } returns """{ "accountId": {"value": "${REQUESTED_DDA_REGISTER.accountId.value}"}, "document":"$DOCUMENT" }"""
            every { ddaRepository.find(REQUESTED_DDA_REGISTER.accountId) } returns null

            handler.handleMessage(message) shouldBe SQSHandlerResponse(true)
            verify { ddaService wasNot Called }
        }

        @Test
        fun `e a importação de DDA resultar em um erro não deve remover a mensagem da fila`() {
            every { message.body() } returns """{ "accountId": {"value": "${REQUESTED_DDA_REGISTER.accountId.value}"}, "document":"$DOCUMENT" }"""
            every { ddaRepository.find(REQUESTED_DDA_REGISTER.accountId) } returns REQUESTED_DDA_REGISTER

            every { ddaService.executeFullImport(REQUESTED_DDA_REGISTER) } returns DDAFullImportResult.Error(Exception(), "Exception")
            handler.handleMessage(message) shouldBe SQSHandlerResponse(false)
        }

        @Test
        fun `e a importação de DDA resultar em not added, não deve remover a mensagem da fila`() {
            every { message.body() } returns """{ "accountId": {"value": "${REQUESTED_DDA_REGISTER.accountId.value}"}, "document":"$DOCUMENT" }"""
            every { ddaRepository.find(REQUESTED_DDA_REGISTER.accountId) } returns REQUESTED_DDA_REGISTER

            every { ddaService.executeFullImport(REQUESTED_DDA_REGISTER) } returns DDAFullImportResult.NotImported
            handler.handleMessage(message) shouldBe SQSHandlerResponse(false)
        }
    }

    companion object {
        private val REQUESTED_ACCOUNT_ID = AccountId(ACCOUNT_ID)
        private val OTHER_ACCOUNT_ID = AccountId(ACCOUNT_ID_2)

        val REQUESTED_DDA_REGISTER = DDARegister(
            accountId = REQUESTED_ACCOUNT_ID,
            document = DOCUMENT,
            created = getZonedDateTime(),
            status = DDAStatus.REQUESTED,
        )

        val NOT_REQUESTED_DDA_REGISTER =
            REQUESTED_DDA_REGISTER.copy(status = DDAStatus.PENDING, accountId = OTHER_ACCOUNT_ID)
    }
}