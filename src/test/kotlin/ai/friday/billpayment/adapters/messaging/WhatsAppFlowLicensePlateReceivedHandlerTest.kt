package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.security.EncryptionService
import ai.friday.billpayment.app.vehicledebts.EnrollVehicleSource
import ai.friday.billpayment.app.vehicledebts.LicensePlate
import ai.friday.billpayment.app.vehicledebts.VehicleDebtsService
import ai.friday.billpayment.app.whatsapp.WhatsAppFlowConfiguration
import io.kotest.matchers.booleans.shouldBeTrue
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message

// FIXME Quebrando o lint sem motivo
private interface Foo

internal class WhatsAppFlowLicensePlateReceivedHandlerTest {
    private val expectedAccountId = "accountId"

    private val expectedLicensePlate = "ACV1234"

    private val encryptionService = mockk<EncryptionService>(relaxed = true) {
        every { decrypt(any(), any()) } returns expectedAccountId
    }

    private val vehicleDebtsService = mockk<VehicleDebtsService>(relaxed = true)

    private val flowConfiguration: WhatsAppFlowConfiguration = mockk(relaxed = true) {
        every { salt } returns "friday-salt"
    }

    private val handler = WhatsAppFlowLicensePlateReceivedHandler(
        amazonSQS = mockk(),
        amazonSNS = mockk(),
        configuration = mockk(),
        arnPrefix = "",
        topicName = "topic",
        encryptionService = encryptionService,
        vehicleDebtsService = vehicleDebtsService,
        flowConfiguration = flowConfiguration,
        queueName = "queue",
    )

    private val message = mockk<Message> {
        every { body() } returns """{"id":"id","licensePlate":"$expectedLicensePlate"}"""
        every { attributes() } returns mapOf()
    }

    @Test
    fun `deve descriptografar a identificação`() {
        val response = handler.handleMessage(message)

        response.shouldDeleteMessage.shouldBeTrue()

        verify { encryptionService.decrypt(any(), any()) }
    }

    @Test
    fun `deve chamar o enrolll`() {
        val response = handler.handleMessage(message)

        response.shouldDeleteMessage.shouldBeTrue()

        verify {
            vehicleDebtsService.enrollByVehicle(
                AccountId(expectedAccountId),
                LicensePlate(expectedLicensePlate),
                true,
                null,
                EnrollVehicleSource.FLOW,
            )
        }
    }
}