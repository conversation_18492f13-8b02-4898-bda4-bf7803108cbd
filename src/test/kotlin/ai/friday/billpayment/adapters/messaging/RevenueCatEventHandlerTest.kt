package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.app.inappsubscription.InAppSubscription
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionEvent
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionService
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStatus
import ai.friday.billpayment.app.inappsubscription.SubscriptionError
import arrow.core.left
import arrow.core.right
import io.kotest.inspectors.forOne
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import software.amazon.awssdk.services.sqs.model.Message

class RevenueCatEventHandlerTest {
    private val inAppSubscriptionService: InAppSubscriptionService =
        mockk(relaxUnitFun = true) {
            every { createSubscription(any()) } returns mockk<InAppSubscription>().right()
            every { extendSubscription(any()) } returns mockk<InAppSubscription>().right()
            every { expireSubscription(any()) } returns mockk<InAppSubscription>().right()
            every { uncancelSubscription(any()) } returns mockk<InAppSubscription>().right()
            every { cancelSubscription(any()) } returns mockk<InAppSubscription>().right()
            every { renewSubscription(any()) } returns mockk<InAppSubscription>().right()
            every { handleSubscriptionRefund(any()) } returns mockk<InAppSubscription>().right()
            every { transferSubscription(any(), any()) } returns mockk<InAppSubscription>().right()
        }

    private val revenueCatEventHandler =
        RevenueCatEventHandler(
            amazonSQS = mockk(relaxed = true),
            configuration = mockk(relaxed = true),
            inAppSubscriptionService = inAppSubscriptionService,
            dlqArn = "revenuecat-dlq-arn",
            messagePublisher = mockk(relaxed = true),
        )

    private val contextClassLoader =
        Thread
            .currentThread()
            .contextClassLoader

    @Test
    fun `se for um evento de sandbox deve ignorar`() {
        val message = Message.builder().body(getJson("sandbox.json")).build()

        val response = revenueCatEventHandler.handleMessage(message)

        verify { inAppSubscriptionService wasNot Called }

        response.shouldDeleteMessage.shouldBeTrue()
    }

    @Test
    fun `deve jogar exception quando algo da errado no processamento do evento`() {
        every { inAppSubscriptionService.createSubscription(any()) } returns SubscriptionError.SubscriptionNotFound.left()
        val message = Message.builder().body(getJson("initial-purchase.json")).build()

        assertThrows<Exception> { revenueCatEventHandler.handleMessage(message) }
    }

    @Test
    fun `no evento de compra inicial deve criar uma subscription e salvar o evento`() {
        val message = Message.builder().body(getJson("initial-purchase.json")).build()

        val response = revenueCatEventHandler.handleMessage(message)

        verify {
            inAppSubscriptionService.saveEvent(any())
            inAppSubscriptionService.createSubscription(any())
        }

        response.shouldDeleteMessage.shouldBeTrue()
    }

    @Test
    fun `no evento de renovação deve renovar a subscription e salvar o evento`() {
        val message = Message.builder().body(getJson("renewal.json")).build()

        val response = revenueCatEventHandler.handleMessage(message)

        verify {
            inAppSubscriptionService.saveEvent(any())
            inAppSubscriptionService.renewSubscription(any())
        }

        response.shouldDeleteMessage.shouldBeTrue()
    }

    @Test
    fun `no evento de expirar deve expirar a subscription e salvar o evento`() {
        val message = Message.builder().body(getJson("expiration.json")).build()

        val response = revenueCatEventHandler.handleMessage(message)

        val inAppSubscription = slot<InAppSubscription>()
        verify {
            inAppSubscriptionService.saveEvent(any())
            inAppSubscriptionService.expireSubscription(capture(inAppSubscription))
        }

        inAppSubscription.captured.status shouldBe InAppSubscriptionStatus.EXPIRED
        response.shouldDeleteMessage.shouldBeTrue()
    }

    @Test
    fun `no evento de extender a subscription deve extender a subscription e salvar o evento`() {
        val message = Message.builder().body(getJson("subscription-extended.json")).build()

        val response = revenueCatEventHandler.handleMessage(message)

        verify {
            inAppSubscriptionService.saveEvent(any())
            inAppSubscriptionService.extendSubscription(any())
        }

        response.shouldDeleteMessage.shouldBeTrue()
    }

    @Test
    fun `no evento refund a subscription deve ser cancelada e deve salvar o evento`() {
        val message = Message.builder().body(getJson("refund.json")).build()

        val response = revenueCatEventHandler.handleMessage(message)

        val slot = slot<InAppSubscription>()
        verify {
            inAppSubscriptionService.handleSubscriptionRefund(capture(slot)) // TODO ver se esta expired
        }
        slot.captured.status shouldBe InAppSubscriptionStatus.EXPIRED
        response.shouldDeleteMessage.shouldBeTrue()
    }

    @Test
    fun `deve chamar a transferencia quando o evento for um transfer`() {
        val message = Message.builder().body(getJson("transfer.json")).build()

        val response = revenueCatEventHandler.handleMessage(message)

        val slot = mutableListOf<InAppSubscriptionEvent>()
        verify {
            inAppSubscriptionService.saveEvent(capture(slot))
            inAppSubscriptionService.transferSubscription(any(), any())
        }
        slot.shouldHaveSize(2)
        slot.forOne { it.accountId.value shouldBe "00005A1C-6091-4F81-BE77-F0A83A271AB6" }
        slot.forOne { it.accountId.value shouldBe "4BEDB450-8EF2-11E9-B475-0800200C9A66" }

        response.shouldDeleteMessage.shouldBeTrue()
    }

    @Test
    fun `deve chamar o cancelamento quando o evento for de cancellation`() {
        val message = Message.builder().body(getJson("cancellation.json")).build()

        val response = revenueCatEventHandler.handleMessage(message)

        verify {
            inAppSubscriptionService.cancelSubscription(any())
        }
        response.shouldDeleteMessage.shouldBeTrue()
    }

    @Test
    fun `deve chamar o cancelamento quando o evento for de subscription paused`() {
        val message = Message.builder().body(getJson("subscription-paused.json")).build()

        val response = revenueCatEventHandler.handleMessage(message)

        verify {
            inAppSubscriptionService.cancelSubscription(any())
        }
        response.shouldDeleteMessage.shouldBeTrue()
    }

    @Test
    fun `deve chamar o descancelamento quando o evento for de uncancellation`() {
        val message = Message.builder().body(getJson("uncancellation.json")).build()

        val response = revenueCatEventHandler.handleMessage(message)

        verify {
            inAppSubscriptionService.uncancelSubscription(any())
        }
        response.shouldDeleteMessage.shouldBeTrue()
    }

    @ParameterizedTest
    @ValueSource(
        strings = [
            "billing-issue.json",
            "product-change.json",
        ],
    )
    fun `deve salvar os seguintes eventos`(fileName: String) {
        val message = Message.builder().body(getJson(fileName)).build()

        val response = revenueCatEventHandler.handleMessage(message)

        verify {
            inAppSubscriptionService.saveEvent(any())
        }

        response.shouldDeleteMessage.shouldBeTrue()
    }

    @Test
    fun `deve jogar exception quando chega um evento não parseavel`() {
        val message = Message.builder().body(getJson("weird-event.json")).build()

        assertThrows<Exception> { revenueCatEventHandler.handleMessage(message) }

        verify(exactly = 0) {
            inAppSubscriptionService.saveEvent(any())
            inAppSubscriptionService.transferSubscription(any(), any())
        }
    }

    private fun getJson(fileName: String): String =
        contextClassLoader
            .getResource("revenuecat/$fileName")
            ?.readText()!!
}