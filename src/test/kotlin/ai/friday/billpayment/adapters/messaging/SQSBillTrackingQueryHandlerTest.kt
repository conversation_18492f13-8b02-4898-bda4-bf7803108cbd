package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.ServerError
import ai.friday.billpayment.TrackableBillFixture
import ai.friday.billpayment.app.bill.BillTrackingCalculateOptions
import ai.friday.billpayment.app.bill.NotSupported
import ai.friday.billpayment.app.bill.tracking.BillTrackingCalculateHandlerService
import ai.friday.billpayment.app.bill.tracking.BillTrackingCalculateResult
import ai.friday.billpayment.app.bill.tracking.SyncRetryable
import ai.friday.billpayment.app.bill.tracking.UnableToSync
import arrow.core.Either
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

internal class SQSBillTrackingQueryHandlerTest {
    private lateinit var billTrackingHandler: SQSBillTrackingQueryHandler

    private lateinit var service: BillTrackingCalculateHandlerService

    @BeforeEach
    internal fun setup() {
        service = mockk()

        billTrackingHandler = SQSBillTrackingQueryHandler(
            queue = "queue",
            amazonSQS = mockk(relaxed = true),
            configuration = mockk(relaxed = true),
            service = service,
        )
    }

    @Test
    internal fun `deve enviar a mensagem de volta para a fila caso ocorra um erro inesperado`() {
        val message = SQSMessageFixture.create(TrackableBillFixture.create())

        every { service.execute(any(), BillTrackingCalculateOptions.QUERY) } returns Either.Left(ServerError())

        val response = billTrackingHandler.handleMessage(message)

        verify { service.execute(any(), BillTrackingCalculateOptions.QUERY) }

        response.shouldDeleteMessage shouldBe false
    }

    @Test
    internal fun `deve remover da fila caso ocorra um erro na calculadora de calculo não suportado`() {
        val message = SQSMessageFixture.create(TrackableBillFixture.create())

        every { service.execute(any(), BillTrackingCalculateOptions.QUERY) } returns Either.Left(NotSupported(""))

        val response = billTrackingHandler.handleMessage(message)

        verify { service.execute(any(), BillTrackingCalculateOptions.QUERY) }

        response.shouldDeleteMessage shouldBe true
    }

    @ParameterizedTest
    @EnumSource(BillTrackingCalculateResult::class)
    internal fun `deve remover da fila caso mensagem seja consumida com sucesso`(result: BillTrackingCalculateResult) {
        val message = SQSMessageFixture.create(TrackableBillFixture.create())

        every { service.execute(any(), BillTrackingCalculateOptions.QUERY) } returns result.right()

        val response = billTrackingHandler.handleMessage(message)

        verify { service.execute(any(), BillTrackingCalculateOptions.QUERY) }

        response.shouldDeleteMessage shouldBe true
    }

    @Test
    internal fun `deve recolocar a mensagem na fila caso seja retentável`() {
        every { service.execute(any(), BillTrackingCalculateOptions.QUERY) } returns Either.Left(SyncRetryable)

        val message = SQSMessageFixture.create(TrackableBillFixture.create())

        val response = billTrackingHandler.handleMessage(message)

        verify { service.execute(any(), BillTrackingCalculateOptions.QUERY) }

        response.shouldDeleteMessage shouldBe false
    }

    @Test
    internal fun `deve excluir a mensagem na fila caso não seja retentável`() {
        every { service.execute(any(), BillTrackingCalculateOptions.QUERY) } returns Either.Left(UnableToSync)

        val message = SQSMessageFixture.create(TrackableBillFixture.create())

        val response = billTrackingHandler.handleMessage(message)

        verify { service.execute(any(), BillTrackingCalculateOptions.QUERY) }

        response.shouldDeleteMessage shouldBe true
    }
}