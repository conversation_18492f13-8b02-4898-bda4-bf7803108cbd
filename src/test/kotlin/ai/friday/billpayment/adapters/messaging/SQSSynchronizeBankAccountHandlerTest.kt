package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.jobs.SynchronizeBankAccountService
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message

internal class SQSSynchronizeBankAccountHandlerTest {
    private val synchronizeBankAccountService: SynchronizeBankAccountService = mockk(relaxUnitFun = true)
    private val sqsSynchronizeBankAccountHandler = SQSSynchronizeBankAccountHandler(
        amazonSQS = mockk(),
        configuration = mockk(),
        queueName = "",
        synchronizeBankAccountService = synchronizeBankAccountService,
    )

    private val synchronizeBankAccountMessageObject =
        SynchronizeBankAccountMessage(
            accountId = AccountId(ACCOUNT_ID),
            paymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID),
        )

    private val mockSynchronizeBankAccountMessage = mockk<Message>() {
        every { body() } returns getObjectMapper().writeValueAsString(synchronizeBankAccountMessageObject)
    }

    @Test
    fun `deve deserealizar a mensagem e sincronizar o payment method`() {
        val response = sqsSynchronizeBankAccountHandler.handleMessage(mockSynchronizeBankAccountMessage)

        response.shouldDeleteMessage shouldBe true

        verify {
            synchronizeBankAccountService.synchronizePaymentMethod(
                synchronizeBankAccountMessageObject.paymentMethodId,
                synchronizeBankAccountMessageObject.accountId,
            )
        }
    }
}