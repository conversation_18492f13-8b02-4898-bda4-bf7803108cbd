package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.notification.BillComingDueService
import ai.friday.billpayment.app.notification.NotifyBillComingDueMessage
import ai.friday.billpayment.app.notification.NotifyBillComingDueType
import ai.friday.billpayment.app.wallet.WalletId
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message

class SQSNotifyBillComingDueHandlerTest {
    private val service: BillComingDueService = mockk(relaxUnitFun = true)
    private val handler = SQSNotifyBillComingDueHandler(
        amazonSQS = mockk(),
        configuration = mockk(),
        queueName = "",
        billComingDueService = service,
    )

    private val walletId = WalletId("WALLET_ID")

    @Test
    fun `caso seja do tipo REGULAR deve deserealizar a mensagem e chamar o servico`() {
        val mockMessage = buildMessage(NotifyBillComingDueType.REGULAR)
        val response = handler.handleMessage(mockMessage)

        response.shouldDeleteMessage shouldBe true

        verify {
            service.executeRegular(walletId)
        }
    }

    @Test
    fun `caso seja do tipo YESTERDAY deve deserealizar a mensagem e chamar o servico`() {
        val mockMessage = buildMessage(NotifyBillComingDueType.YESTERDAY)
        val response = handler.handleMessage(mockMessage)

        response.shouldDeleteMessage shouldBe true

        verify {
            service.executeOverdueYesterday(walletId)
        }
    }

    @Test
    fun `caso seja do tipo LAST_WARN deve deserealizar a mensagem e chamar o servico`() {
        val mockMessage = buildMessage(NotifyBillComingDueType.LAST_WARN)
        val response = handler.handleMessage(mockMessage)

        response.shouldDeleteMessage shouldBe true

        verify {
            service.executeLastWarn(walletId)
        }
    }

    private fun buildMessage(notifyBillComingDueType: NotifyBillComingDueType): Message {
        val message =
            NotifyBillComingDueMessage(
                walletId = walletId,
                type = notifyBillComingDueType,
            )

        return mockk() {
            every { body() } returns getObjectMapper().writeValueAsString(message)
        }
    }
}