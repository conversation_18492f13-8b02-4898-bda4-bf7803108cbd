package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.messaging.SQSDDABatchAddOrderHandlerTest.Companion.DOCUMENT_LIST
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.FichaCompensacaoService
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.dda.BatchAddOrder
import ai.friday.billpayment.app.dda.CreateDDARegister
import ai.friday.billpayment.app.dda.DDARegister
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.dda.DDAStatus
import ai.friday.billpayment.app.dda.toDDARegisterMessage
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BillValidationService
import ai.friday.billpayment.app.integrations.DDAProviderService
import ai.friday.billpayment.app.integrations.DDAQueryBatchAddResult
import ai.friday.billpayment.app.integrations.DDARepository
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.createDDAConfig
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.DOCUMENT_2
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.util.stream.Stream
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtensionContext
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.ArgumentsProvider
import org.junit.jupiter.params.provider.ArgumentsSource
import software.amazon.awssdk.services.sqs.model.Message

internal class SQSDDABatchAddOrderHandlerTest {

    private val ddaProviderService: DDAProviderService = mockk(relaxed = true)
    private val fichaCompensacaoService: FichaCompensacaoService = mockk()
    private val findBillService: FindBillService = mockk()
    private val featureConfiguration: FeatureConfiguration = mockk()
    private val billValidationService: BillValidationService = mockk()
    private val updateBillService: UpdateBillService = mockk()
    private val messagePublisher: MessagePublisher = mockk(relaxed = true)

    private val accountRepository: AccountRepository = mockk(relaxUnitFun = true) {
        every { findAccountByDocument(ACCOUNT.document) } returns ACCOUNT
        every { findAccountByDocument(DOCUMENT_2) } returns ACCOUNT.copy(
            document = DOCUMENT_2,
            accountId = AccountId(ACCOUNT_ID_2),
        )
    }

    private val ddaRepository: DDARepository = mockk(relaxUnitFun = true, relaxed = true) {
        every { find(ACCOUNT.accountId) } returns DDA_REGISTER
        every { find(AccountId(ACCOUNT_ID_2)) } returns DDA_REGISTER.copy(document = DOCUMENT_2)
    }

    private val configuration: SQSMessageHandlerConfiguration = mockk(relaxed = true) {
        every { ddaBatchAddOrdersQueueName } returns "test-queue"
    }

    private val ddaConfig = createDDAConfig()

    private val createDDARegister = CreateDDARegister(
        ddaRepository = ddaRepository,
        ddaConfig = ddaConfig,
    )

    private val ddaService = DDAService(
        ddaProviderService = ddaProviderService,
        findBillService = findBillService,
        featureConfiguration = featureConfiguration,
        accountRepository = accountRepository,
        billValidationService = billValidationService,
        updateBillService = updateBillService,
        ddaRepository = ddaRepository,
        fullDDAPostProcessor = mockk(relaxed = true),
        messagePublisher = messagePublisher,
        ddaConfig = ddaConfig,
        createDDARegister = createDDARegister,
        fichaCompensacaoService = fichaCompensacaoService,
        walletService = mockk(),
        openFinanceIncentiveService = mockk(relaxed = true),
        ddaFullImportQueueName = "queue",

    )

    private val handler = SQSDDABatchAddOrderHandler(
        amazonSQS = mockk(),
        configuration = configuration,
        ddaService = ddaService,
        ddaRepository = ddaRepository,
        ddaProviderService = ddaProviderService,
        messagePublisher = messagePublisher,
    )

    @Test
    fun `ao receber uma mensagem, deve chamar provedor de DDA com o identificador da requisição para verificar o status do lote`() {
        handler.handleMessage(MESSAGE)
        verify(exactly = 1) { ddaProviderService.queryBatchAdd(REQUEST_ID) }
    }

    @Test
    fun `ao invocar a consulta ao lote e uma Exception for lançada, deve manter a mensagem na fila`() {
        every { ddaProviderService.queryBatchAdd(REQUEST_ID) } throws Exception()
        val response = handler.handleMessage(MESSAGE)

        verify(exactly = 1) { ddaProviderService.queryBatchAdd(REQUEST_ID) }
        verify(exactly = 0) { ddaRepository.save(any<DDARegister>()) }

        response shouldBe SQSHandlerResponse(false)
    }

    @Nested
    @DisplayName("ao consultar o status do lote ")
    inner class QueryBatchAddResultScope {
        @Test
        fun `e receber sucesso total para o lote, deve atualizar cada registro de DDA para REQUESTED`() {
            every { ddaProviderService.queryBatchAdd(REQUEST_ID) } returns DDAQueryBatchAddResult.Success
            val response = handler.handleMessage(MESSAGE)

            val slots = mutableListOf<DDARegister>()
            verify { ddaRepository.save(capture(slots)) }

            slots.forEach {
                it.status shouldBe DDAStatus.REQUESTED
            }

            slots.map { it.document } shouldContainExactlyInAnyOrder BATCH_ADD_ORDER.documents
            verify {
                messagePublisher.sendBatchDDARequestedMessage(
                    withArg { it shouldContainExactlyInAnyOrder slots.map { dda -> dda.toDDARegisterMessage() } },
                )
            }
            response shouldBe SQSHandlerResponse(true)
        }

        @Test
        fun `e receber sucesso parcial, deve atualizar os status dos casos de sucessos para REQUESTED e das falhas para PENDING`() {
            every { ddaProviderService.queryBatchAdd(REQUEST_ID) } returns DDAQueryBatchAddResult.PartialSuccess(
                failed = listOf(BATCH_ADD_ORDER.documents.first().let { Document(it) }),
                success = listOf(BATCH_ADD_ORDER.documents.last().let { Document(it) }),
            )

            val response = handler.handleMessage(MESSAGE)

            val slots = mutableListOf<DDARegister>()
            verify { ddaRepository.save(capture(slots)) }

            slots.first().status shouldBe DDAStatus.PENDING
            slots.last().status shouldBe DDAStatus.REQUESTED

            verify { messagePublisher.sendBatchDDARequestedMessage(listOf(slots.last().toDDARegisterMessage())) }
            response shouldBe SQSHandlerResponse(true)
        }

        @ParameterizedTest
        @ArgumentsSource(DDAQueryBatchResultSourceProvider::class)
        fun `e receber falha, deve atualizar o status dos registros de DDA que falharam para PENDING`(
            result: DDAQueryBatchAddResult,
            expectedResponse: SQSHandlerResponse,
        ) {
            every { ddaProviderService.queryBatchAdd(REQUEST_ID) } returns result
            val response = handler.handleMessage(MESSAGE)

            val slots = mutableListOf<DDARegister>()
            verify {
                ddaRepository.save(capture(slots))
                messagePublisher wasNot Called
            }

            slots.forEach {
                it.status shouldBe DDAStatus.PENDING
            }

            slots.map { it.document } shouldContainExactlyInAnyOrder BATCH_ADD_ORDER.documents
            response shouldBe expectedResponse
        }

        @Test
        fun `e receber lote em processamento, deve manter a mensagem na fila para verificação posterior`() {
            every { ddaProviderService.queryBatchAdd(REQUEST_ID) } returns DDAQueryBatchAddResult.ProcessingRequest
            val response = handler.handleMessage(MESSAGE)
            verify { messagePublisher wasNot Called }
            response shouldBe SQSHandlerResponse(false)
        }
    }

    companion object {
        private const val REQUEST_ID = "request-id#1"
        val DOCUMENT_LIST = listOf(DOCUMENT, DOCUMENT_2)

        private val BATCH_ADD_ORDER = BatchAddOrder(
            requestId = REQUEST_ID,
            documents = DOCUMENT_LIST,
        )

        private val MESSAGE = Message.builder().body(getObjectMapper().writeValueAsString(BATCH_ADD_ORDER)).build()

        private val DDA_REGISTER = DDARegister(
            accountId = ACCOUNT.accountId,
            document = ACCOUNT.document,
            created = mockk(),
            status = DDAStatus.PENDING,
            lastUpdated = mockk(),
            lastSuccessfullExecution = null,
        )
    }
}

internal class DDAQueryBatchResultSourceProvider : ArgumentsProvider {
    override fun provideArguments(context: ExtensionContext): Stream<out Arguments> {
        return Stream.of(
            Arguments.arguments(
                DDAQueryBatchAddResult.Failed(DOCUMENT_LIST.map { Document(it) }),
                SQSHandlerResponse(true),
            ),
            Arguments.arguments(DDAQueryBatchAddResult.PendingRequestError, SQSHandlerResponse(true)),
            Arguments.arguments(DDAQueryBatchAddResult.UnknownResponse, SQSHandlerResponse(true)),
        )
    }
}

/*
    arquivo falha por causa de um usuario negado pela CIP
     - marca todos como PENDING e o usuario negado como DENIED ??

    - tratar documento com status diferente de REQUESTING
    - tratar lote com retorno incompleto (documentos nao constam na consulta de resultado do lote)
*/