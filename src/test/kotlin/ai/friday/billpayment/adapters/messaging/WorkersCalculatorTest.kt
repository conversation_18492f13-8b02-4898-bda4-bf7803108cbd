package ai.friday.billpayment.adapters.messaging

import io.kotest.matchers.shouldBe
import java.util.stream.Stream
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource

internal class WorkersCalculatorTest {

    @ParameterizedTest
    @CsvSource(value = ["1,10,1,10", "4,8,4,8", "20,30,10,10"])
    fun `should calculate limit constraints appropriately`(min: Int, max: Int, expectedMin: Int, expectedMax: Int) {
        val workersCalculator = WorkersCalculator(min, max)
        workersCalculator.minWorkers.shouldBe(expectedMin)
        workersCalculator.maxWorkers.shouldBe(expectedMax)
    }

    @ParameterizedTest
    @CsvSource(value = ["-1,9", "1,-9", "10,5"])
    fun `should throw exception if constraints are invalid ones`(min: Int, max: Int) {
        assertThrows<IllegalStateException> { WorkersCalculator(min, max) }
    }

    @ParameterizedTest
    @MethodSource("simpleStatistics")
    fun `should scale workers`(computedTimes: List<Long>, hardDownScale: Boolean, expectedWorkers: Int) {
        val workersCalculator =
            WorkersCalculator(
                minParallelism = 2,
                maxParallelism = 8,
                healthIndicatorTimeInMillis = 5000,
                hardDownScale = hardDownScale,
            )

        workersCalculator.currentWorkers().shouldBe(2)

        // increment
        repeat(2) {
            for (i in 1..10) {
                workersCalculator.computeJobElapsedTimeInMillis(1000)
            }
            workersCalculator.currentWorkers()
        }

        workersCalculator.currentWorkers().shouldBe(4)

        computedTimes.forEach {
            workersCalculator.computeJobElapsedTimeInMillis(it)
            workersCalculator.computeJobElapsedTimeInMillis(it)
        }

        // should decrement
        workersCalculator.currentWorkers().shouldBe(expectedWorkers)
    }

    @ParameterizedTest
    @MethodSource("simpleStatisticsWithoutScale")
    fun `should not scale workers`(computedTimes: List<Long>, min: Int, max: Int, expectedWorkers: Int) {
        val workersCalculator = WorkersCalculator(min, max, healthIndicatorTimeInMillis = 5000)

        workersCalculator.currentWorkers().shouldBe(min)

        computedTimes.forEach {
            workersCalculator.computeJobElapsedTimeInMillis(it)
            workersCalculator.computeJobElapsedTimeInMillis(it)
        }

        workersCalculator.currentWorkers().shouldBe(expectedWorkers)
    }

    companion object {
        @JvmStatic
        fun `simpleStatistics`(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(listOf(1000, 10000, 15000, 11000, 5000), true, 2),
                Arguments.of(listOf(1000, 10000, 15000, 11000, 5000), false, 3),
                Arguments.of(listOf(1000, 3000, 1500, 1000, 4000), false, 5),
            )
        }

        @JvmStatic
        fun `simpleStatisticsWithoutScale`(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(listOf(1000, 10000, 15000, 11000, 5000), 1, 10, 1), // hard limits
                Arguments.of(listOf(1000, 3000, 1500, 1000, 4000), 10, 10, 10), // hard limits
                Arguments.of(listOf(1000, 10000, 15000, 11000, 5000), 5, 5, 5), // soft limits
                Arguments.of(listOf(1000, 3000, 1500, 1000, 4000), 5, 5, 5), // soft limits
            )
        }
    }
}