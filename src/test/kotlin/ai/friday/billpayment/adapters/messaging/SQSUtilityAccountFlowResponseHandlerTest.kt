package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.ConcessionariaRequestWithDueDate
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateBillService
import ai.friday.billpayment.app.bill.CreateFichaDeCompensacaoRequest
import ai.friday.billpayment.app.bill.ExternalBillId
import ai.friday.billpayment.app.bill.ExternalBillProvider
import ai.friday.billpayment.app.bill.FichaCompensacaoService
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.UtilityAccountRepository
import ai.friday.billpayment.app.manualentry.ManualEntry
import ai.friday.billpayment.app.manualentry.ManualEntryId
import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.app.manualentry.ManualEntryStatus
import ai.friday.billpayment.app.manualentry.ManualEntryType
import ai.friday.billpayment.app.utilityaccount.Utility
import ai.friday.billpayment.app.utilityaccount.UtilityAccount
import ai.friday.billpayment.app.utilityaccount.UtilityAccountAdditionalInfoTO
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionDetails
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionStatus
import ai.friday.billpayment.app.utilityaccount.UtilityAccountInvoiceStatus
import ai.friday.billpayment.app.utilityaccount.UtilityAccountService
import ai.friday.billpayment.app.utilityaccount.UtilityAccountUpdateInfo
import ai.friday.billpayment.app.utilityaccount.UtilityConnectionMethod
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.fixture.UtilityAccountFixture
import ai.friday.billpayment.getInvoiceBill
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.BillFixture
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_BARCODE
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.date.withGivenDateTime
import arrow.core.right
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import io.via1.communicationcentre.app.integrations.EmailSenderService
import java.time.LocalDate
import java.time.ZonedDateTime
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import software.amazon.awssdk.services.sqs.model.Message

internal class SQSUtilityAccountFlowResponseHandlerTest {
    private val wallet = WalletFixture(founderAccountId = ACCOUNT.accountId, defaultWalletId = ACCOUNT.defaultWalletId()).buildWallet()

    private val utilityAccount = UtilityAccountFixture.create(
        walletId = wallet.id,
        accountId = wallet.founder.accountId,
        utility = Utility.LIGHT_RJ,
        status = UtilityAccountConnectionStatus.CONNECTED,
        connectionDetails = UtilityAccountConnectionDetails.create(Utility.LIGHT_RJ, mapOf("cpf" to "***********", "password" to "password", "installationNumber" to "**********")),
        connectionMethod = UtilityConnectionMethod.FLOW,
    )
    private val pendingUtilityAccount = utilityAccount.copy(status = UtilityAccountConnectionStatus.PENDING)
    private val bill = Bill.build(BillFixture(wallet).billAdded)
    private val bill2 = Bill.build(BillFixture(wallet).billAdded.copy(dueDate = bill.dueDate.plusMonths(1)))
    private val bill3 = Bill.build(BillFixture(wallet).billAdded.copy(dueDate = bill.dueDate.plusDays(15)))

    private val successWithInvoicesMessage = UtilityAccountFlowResponseTO(
        connectionId = "2",
        invoices = listOf(
            InvoiceTO(barcode = bill.barcode!!.digitable, dueDate = bill.dueDate.toString(), status = UtilityAccountInvoiceStatus.ACTIVE, externalId = "EXTERNAL-ID"),
            InvoiceTO(barcode = bill2.barcode!!.digitable, dueDate = bill2.dueDate.toString(), status = UtilityAccountInvoiceStatus.ACTIVE),
            InvoiceTO(barcode = bill2.barcode!!.digitable, dueDate = bill3.dueDate.toString(), status = UtilityAccountInvoiceStatus.ACTIVE),
        ),
        additionalInfo = UtilityAccountAdditionalInfoTO(
            lastDueDateFound = bill2.dueDate.format(dateFormat),
        ),
        status = UtilityAccountConnectionStatus.CONNECTED,
        statusMessage = null,
        success = true,
    )

    private val successWithoutInvoicesMessage = successWithInvoicesMessage.copy(
        invoices = listOf(),
        additionalInfo = UtilityAccountAdditionalInfoTO(lastDueDateFound = null),
    )

    private val failureMessage = UtilityAccountFlowResponseTO(
        connectionId = "2",
        invoices = listOf(),
        additionalInfo = UtilityAccountAdditionalInfoTO(lastDueDateFound = null),
        status = UtilityAccountConnectionStatus.CONNECTION_ERROR,
        statusMessage = null,
        success = false,
    )

    private val accountRepository = mockk<AccountRepository>(relaxed = true) {
        every { findById(any()) } returns ACCOUNT
    }
    private val utilityAccountRepository = mockk<UtilityAccountRepository>(relaxed = true)
    private val createBillService = mockk<CreateBillService>(relaxed = true)
    private val fichaCompensacaoService = mockk<FichaCompensacaoService>(relaxed = true)
    private val manualEntryService = mockk<ManualEntryService>(relaxed = true)

    val walletService = mockk<WalletService>() {
        every { findWalletOrNull(any()) } returns wallet
    }

    val notificationAdapter: NotificationAdapter = mockk(relaxed = true)
    val emailSenderService: EmailSenderService = mockk(relaxed = true)

    private val utilityAccountService = UtilityAccountService(
        utilityAccountRepository = utilityAccountRepository,
        accountRepository = accountRepository,
        sqsMessagePublisher = mockk(relaxed = true),
        notificationAdapter = notificationAdapter,
        intercomAdapter = mockk(relaxed = true),
        kmsAdapter = mockk(relaxed = true),
        emailSenderService = emailSenderService,
        utilityAccountMonitorService = mockk(relaxed = true),
        externalFilesRepositorySA = mockk(relaxed = true),
        utilityAccountActivationQueueName = "",
        utilityFlowRequestInvoicesQueueName = "",
        sender = "",
        emailDomain = "",
        manualWorkFlowEmail = "",
        createBillService = createBillService,
        fichaCompensacaoService = fichaCompensacaoService,
        walletService = walletService,
        userJourneyService = mockk(relaxed = true),
        manualEntryService = manualEntryService,
        userEventService = mockk(relaxed = true),
    )

    private val handler = SQSUtilityAccountFlowResponseHandler(
        sqsClient = mockk(),
        configuration = mockk(relaxed = true),
        utilityAccountService = utilityAccountService,
    )

    @ParameterizedTest
    @EnumSource(UtilityAccountConnectionStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["PENDING", "CONNECTED"])
    internal fun `deve deletar mensagem da fila caso a utility não esteja conectada nem pendente`(connectionStatus: UtilityAccountConnectionStatus) {
        val message = buildMessage(successWithInvoicesMessage)

        every { utilityAccountRepository.find(any()) } returns utilityAccount.copy(status = connectionStatus)

        val response = handler.handleMessage(message)

        response shouldBe SQSHandlerResponse(true)

        verify {
            createBillService wasNot called
        }
    }

    @ParameterizedTest
    @EnumSource(UtilityAccountConnectionStatus::class, mode = EnumSource.Mode.INCLUDE, names = ["PENDING", "CONNECTED"])
    internal fun `deve criar os boletos caso a utility esteja conectada ou pendente`(connectionStatus: UtilityAccountConnectionStatus) {
        val message = buildMessage(
            successWithInvoicesMessage.copy(
                invoices = listOf(successWithInvoicesMessage.invoices.first()),
                additionalInfo = UtilityAccountAdditionalInfoTO(lastDueDateFound = bill.dueDate.format(dateFormat)),
            ),
        )

        every { utilityAccountRepository.find(any()) } returns utilityAccount.copy(
            status = connectionStatus,
            lastDueDateFound = null,
        )
        every { createBillService.createConcessionaria(request = any()) } returns CreateBillResult.SUCCESS(bill)

        val date = getZonedDateTime()

        withGivenDateTime(date) {
            val response = handler.handleMessage(message)

            response shouldBe SQSHandlerResponse(true)

            val slot = slot<ConcessionariaRequestWithDueDate>()

            verify {
                createBillService.createConcessionaria(request = capture(slot))
                if (connectionStatus == UtilityAccountConnectionStatus.PENDING) {
                    utilityAccountRepository.save(
                        utilityAccount.copy(
                            notificatedAt = date,
                            status = UtilityAccountConnectionStatus.CONNECTED,
                        ),
                    )
                }
                utilityAccountRepository.save(
                    utilityAccount.copy(
                        lastBillIdFound = bill.billId,
                        lastBillFound = date.toLocalDate(),
                        lastDueDateFound = bill.dueDate,
                        lastSuccessfulScan = date.toLocalDate(),
                    ),
                )
            }

            with(slot.captured) {
                description shouldBe "Fatura ${utilityAccount.utility.viewName}"
                dueDate shouldBe bill.dueDate
                barcode shouldBe bill.barcode
                walletId shouldBe wallet.id
                member shouldBe null
                securityValidationErrors shouldBe listOf()
                source shouldBe ActionSource.ConnectUtility(accountId = wallet.founder.accountId)
                externalBillId shouldBe ExternalBillId("EXTERNAL-ID", ExternalBillProvider.UTILITY_ACCOUNT)
            }
        }
    }

    @Test
    internal fun `deve conectar a conta caso esteja pendente e sem boletos`() {
        val message = buildMessage(
            successWithInvoicesMessage.copy(
                invoices = listOf(),
                additionalInfo = UtilityAccountAdditionalInfoTO(lastDueDateFound = null),
            ),
        )

        every { utilityAccountRepository.find(any()) } returns utilityAccount.copy(status = UtilityAccountConnectionStatus.PENDING)

        val date = getZonedDateTime()

        withGivenDateTime(date) {
            val response = handler.handleMessage(message)
            response shouldBe SQSHandlerResponse(true)

            verify {
                createBillService wasNot called
                utilityAccountRepository.save(
                    utilityAccount.copy(
                        notificatedAt = date,
                        status = UtilityAccountConnectionStatus.CONNECTED,

                    ),
                )
                utilityAccountRepository.save(
                    utilityAccount.copy(
                        lastSuccessfulScan = date.toLocalDate(),
                        status = UtilityAccountConnectionStatus.CONNECTED,
                    ),
                )
            }
        }
    }

    @Test
    internal fun `deve salvar a data de vencimento mais recente na conta de consumo`() {
        val message = buildMessage(successWithInvoicesMessage)

        every { utilityAccountRepository.find(any()) } returns utilityAccount
        every { createBillService.createConcessionaria(request = any()) } returns CreateBillResult.SUCCESS(bill) andThen CreateBillResult.SUCCESS(
            bill2,
        ) andThen CreateBillResult.SUCCESS(bill3)

        val date = getZonedDateTime()

        withGivenDateTime(date) {
            val response = handler.handleMessage(message)

            response shouldBe SQSHandlerResponse(true)
        }

        verify(exactly = 3) {
            createBillService.createConcessionaria(request = any())
        }

        verify {
            utilityAccountRepository.save(
                utilityAccount.copy(
                    lastBillIdFound = bill2.billId,
                    lastBillFound = date.toLocalDate(),
                    lastDueDateFound = bill2.dueDate,
                    lastSuccessfulScan = date.toLocalDate(),
                ),
            )
        }
    }

    @Test
    internal fun `deve salvar a data de vencimento recebida da resposta mesmo que não seja o vencimento de uma das contas`() {
        val newLastDueDate = bill2.dueDate.plusMonths(2)
        val message = buildMessage(
            successWithInvoicesMessage.copy(
                additionalInfo = UtilityAccountAdditionalInfoTO(lastDueDateFound = newLastDueDate.format(dateFormat)),
            ),
        )

        every { utilityAccountRepository.find(any()) } returns utilityAccount
        every { createBillService.createConcessionaria(request = any()) } returns CreateBillResult.SUCCESS(bill) andThen CreateBillResult.SUCCESS(
            bill2,
        ) andThen CreateBillResult.SUCCESS(bill3)

        val date = getZonedDateTime()

        withGivenDateTime(date) {
            val response = handler.handleMessage(message)

            response shouldBe SQSHandlerResponse(true)
        }

        verify(exactly = 3) {
            createBillService.createConcessionaria(request = any())
        }

        verify {
            utilityAccountRepository.save(
                utilityAccount.copy(
                    lastBillIdFound = bill2.billId,
                    lastBillFound = date.toLocalDate(),
                    lastDueDateFound = newLastDueDate,
                    lastSuccessfulScan = date.toLocalDate(),
                ),
            )
        }
    }

    @Test
    internal fun `deve salvar a data de vencimento recebida da resposta mesmo que não haja novas contas`() {
        val newLastDueDate = bill2.dueDate.plusMonths(2)
        val message = buildMessage(
            successWithInvoicesMessage.copy(
                additionalInfo = UtilityAccountAdditionalInfoTO(lastDueDateFound = newLastDueDate.format(dateFormat)),
                invoices = listOf(),
            ),
        )

        every { utilityAccountRepository.find(any()) } returns utilityAccount
        every { createBillService.createConcessionaria(request = any()) } returns CreateBillResult.SUCCESS(bill) andThen CreateBillResult.SUCCESS(
            bill2,
        ) andThen CreateBillResult.SUCCESS(bill3)

        val date = getZonedDateTime()

        withGivenDateTime(date) {
            val response = handler.handleMessage(message)

            response shouldBe SQSHandlerResponse(true)
        }

        verify {
            utilityAccountRepository.save(
                utilityAccount.copy(
                    lastDueDateFound = newLastDueDate,
                    lastSuccessfulScan = date.toLocalDate(),
                ),
            )
        }
    }

    @Test
    internal fun `não deve alterar data do último vencimento se a a data recebida for antes da que está salva no banco`() {
        val newLastDueDate = bill2.dueDate.plusMonths(2)
        val message = buildMessage(
            successWithInvoicesMessage.copy(
                additionalInfo = UtilityAccountAdditionalInfoTO(lastDueDateFound = newLastDueDate.format(dateFormat)),
            ),
        )

        every { utilityAccountRepository.find(any()) } returns utilityAccount.copy(
            lastDueDateFound = newLastDueDate.plusDays(
                1,
            ),
        )
        every { createBillService.createConcessionaria(request = any()) } returns CreateBillResult.SUCCESS(bill) andThen CreateBillResult.SUCCESS(
            bill2,
        ) andThen CreateBillResult.SUCCESS(bill3)

        val date = getZonedDateTime()

        withGivenDateTime(date) {
            val response = handler.handleMessage(message)

            response shouldBe SQSHandlerResponse(true)
        }

        verify(exactly = 3) {
            createBillService.createConcessionaria(request = any())
        }

        verify {
            utilityAccountRepository.save(
                utilityAccount.copy(
                    lastBillIdFound = bill2.billId,
                    lastBillFound = date.toLocalDate(),
                    lastDueDateFound = newLastDueDate.plusDays(1),
                    lastSuccessfulScan = date.toLocalDate(),
                ),
            )
        }
    }

    @Test
    fun `não deve aceitar boletos com codigo de barras invalido`() {
        val message = buildMessage(
            successWithInvoicesMessage.copy(
                invoices = listOf(
                    InvoiceTO(
                        barcode = "000000000000000000000000000000000000000000000000",
                        dueDate = "2021-10-10",
                        status = UtilityAccountInvoiceStatus.ACTIVE,
                    ),
                ),
            ),
        )
        every { utilityAccountRepository.find(any()) } returns utilityAccount

        val date = getZonedDateTime()
        withGivenDateTime(date) {
            val response = handler.handleMessage(message)

            response.shouldDeleteMessage.shouldBeTrue()
        }

        verify(exactly = 0) {
            createBillService.createConcessionaria(request = any())
            fichaCompensacaoService.createFichaDeCompensacao(request = any())
            utilityAccountRepository.save(any())
        }
    }

    @Test
    fun `deve adicionar ficha de compensacao`() {
        val message = buildMessage(
            successWithInvoicesMessage.copy(
                connectionId = "123",
                invoices = listOf(
                    InvoiceTO(
                        barcode = FICHA_DE_COMPENSACAO_BARCODE,
                        dueDate = "2024-05-04",
                        status = UtilityAccountInvoiceStatus.ACTIVE,
                        externalId = "EXTERNAL-ID",
                    ),
                ),
                additionalInfo = UtilityAccountAdditionalInfoTO(lastDueDateFound = "2024-05-04"),
            ),
        )

        every { utilityAccountRepository.find(any()) } returns utilityAccount.copy(lastDueDateFound = null)
        every { fichaCompensacaoService.createFichaDeCompensacao(request = any()) } returns CreateBillResult.SUCCESS(
            bill,
        )

        val date = getZonedDateTime()

        withGivenDateTime(date) {
            val response = handler.handleMessage(message)

            response shouldBe SQSHandlerResponse(true)
        }

        val slot = slot<CreateFichaDeCompensacaoRequest>()

        verify(exactly = 1) {
            fichaCompensacaoService.createFichaDeCompensacao(request = capture(slot))
        }

        slot.captured.externalBillId shouldBe ExternalBillId("EXTERNAL-ID", ExternalBillProvider.UTILITY_ACCOUNT)

        verify(exactly = 0) {
            createBillService.createConcessionaria(any())
        }

        verify {
            utilityAccountRepository.save(
                utilityAccount.copy(
                    lastBillIdFound = bill.billId,
                    lastBillFound = date.toLocalDate(),
                    lastDueDateFound = LocalDate.of(2024, 5, 4),
                    lastSuccessfulScan = date.toLocalDate(),
                ),
            )
        }
    }

    @Test
    internal fun `deve zerar contagem de erros em caso de sucesso`() {
        val message =
            buildMessage(successWithInvoicesMessage.copy(invoices = listOf(successWithInvoicesMessage.invoices.first())))

        every { utilityAccountRepository.find(any()) } returns utilityAccount.copy(scanFailureCount = 5)
        every { createBillService.createConcessionaria(request = any()) } returns CreateBillResult.SUCCESS(bill)

        val date = getZonedDateTime()

        withGivenDateTime(date) {
            handler.handleMessage(message)
        }

        val slot = slot<UtilityAccount>()

        verify { utilityAccountRepository.save(capture(slot)) }

        slot.captured.scanFailureCount shouldBe 0
    }

    @Test
    internal fun `deve zerar contagem de erros em caso de sucesso mesmo sem invoices`() {
        val message = buildMessage(successWithInvoicesMessage.copy(invoices = listOf()))

        every { utilityAccountRepository.find(any()) } returns utilityAccount.copy(scanFailureCount = 5)
        every { createBillService.createConcessionaria(request = any()) } returns CreateBillResult.SUCCESS(bill)

        val date = getZonedDateTime()

        withGivenDateTime(date) {
            handler.handleMessage(message)
        }

        val slot = slot<UtilityAccount>()

        verify { utilityAccountRepository.save(capture(slot)) }

        slot.captured.scanFailureCount shouldBe 0
    }

    @Test
    internal fun `deve zerar contagem de Invalid Credentials em caso de sucesso`() {
        val message =
            buildMessage(successWithInvoicesMessage.copy(invoices = listOf(successWithInvoicesMessage.invoices.first())))

        every { utilityAccountRepository.find(any()) } returns utilityAccount.copy(invalidCredentialsCount = 5)
        every { createBillService.createConcessionaria(request = any()) } returns CreateBillResult.SUCCESS(bill)

        val date = getZonedDateTime()

        withGivenDateTime(date) {
            handler.handleMessage(message)
        }

        val slot = slot<UtilityAccount>()

        verify { utilityAccountRepository.save(capture(slot)) }

        slot.captured.invalidCredentialsCount shouldBe 0
    }

    @Test
    internal fun `deve zerar contagem de Invalid Credentials em caso de sucesso mesmo sem invoices`() {
        val message = buildMessage(successWithInvoicesMessage.copy(invoices = listOf()))

        every { utilityAccountRepository.find(any()) } returns utilityAccount.copy(invalidCredentialsCount = 5)
        every { createBillService.createConcessionaria(request = any()) } returns CreateBillResult.SUCCESS(bill)

        val date = getZonedDateTime()

        withGivenDateTime(date) {
            handler.handleMessage(message)
        }

        val slot = slot<UtilityAccount>()

        verify { utilityAccountRepository.save(capture(slot)) }

        slot.captured.invalidCredentialsCount shouldBe 0
    }

    @Test
    internal fun `deve criar lançamento para invoices pagos que não estejam duplicados`() {
        val walletFixture = WalletFixture()

        // Wallet de um usuário criado depois da data de cutoff
        every { walletService.findWalletOrNull(any()) } returns walletFixture.buildWallet(
            walletFounder = walletFixture.founder.copy(
                created = ZonedDateTime.parse("2024-07-02 00:01:00", dateTimeFormat),
            ),
        )

        val manualEntry = ManualEntry(
            id = ManualEntryId("TEST-MANUAL-ENTRY"),
            walletId = WalletId(WALLET_ID),
            title = "Test manual entry",
            amount = 12345L,
            dueDate = getLocalDate(),
            type = ManualEntryType.EXTERNAL_PAYMENT,
            status = ManualEntryStatus.PAID,
            source = ActionSource.Api(accountId = AccountId(ACCOUNT_ID)),
            categorySuggestions = listOf(),
            createdAt = getZonedDateTime(),
            updatedAt = getZonedDateTime(),
            externalId = ExternalBillId("external-id-duplicate-entry", ExternalBillProvider.UTILITY_ACCOUNT),
        )

        val message = buildMessage(
            successWithInvoicesMessage.copy(
                invoices = listOf(
                    InvoiceTO(
                        barcode = "",
                        dueDate = "2024-04-05",
                        status = UtilityAccountInvoiceStatus.PAID,
                        externalId = "external-id-duplicate-bill",
                        amount = 100_00,
                    ),
                    InvoiceTO(
                        barcode = "",
                        dueDate = "2024-05-05",
                        status = UtilityAccountInvoiceStatus.PAID,
                        externalId = "external-id-duplicate-entry",
                        amount = 100_00,
                    ),
                    InvoiceTO(
                        barcode = "",
                        dueDate = "2024-06-05",
                        status = UtilityAccountInvoiceStatus.PAID,
                        externalId = "external-id-new",
                        amount = 100_00,
                    ),
                    InvoiceTO(
                        barcode = "",
                        dueDate = "2024-07-05",
                        status = UtilityAccountInvoiceStatus.PAID,
                        externalId = "external-id-bill-same-assignor-value-duedate",
                        amount = 123_45,
                    ),
                ),
            ),
        )

        every { utilityAccountRepository.find(any()) } returns utilityAccount.copy(
            utility = Utility.LIGHT_RJ,
            createdAt = ZonedDateTime.parse("2024-06-27 00:01:00", dateTimeFormat),
        )

        every {
            createBillService.findBillsByWalletAndEffectiveDueDate(utilityAccount.walletId, LocalDate.of(2024, 4, 5))
        } returns listOf(
            getInvoiceBill().copy(
                externalId = ExternalBillId(
                    "external-id-duplicate-bill",
                    ExternalBillProvider.UTILITY_ACCOUNT,
                ),
            ),
        )

        every {
            createBillService.findBillsByWalletAndEffectiveDueDate(utilityAccount.walletId, LocalDate.of(2024, 7, 5))
        } returns listOf(getInvoiceBill().copy(externalId = null, amount = 123_45, assignor = "LIGHT"))

        every {
            manualEntryService.findAllByWalletAndDueDate(utilityAccount.walletId, LocalDate.of(2024, 5, 5))
        } returns listOf(manualEntry)

        every {
            manualEntryService.create(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns manualEntry.right()

        val date = getZonedDateTime()

        withGivenDateTime(date) {
            handler.handleMessage(message)
        }

        verify(exactly = 1) {
            manualEntryService.create(
                walletId = any(),
                title = any(),
                amount = any(),
                categoryId = any(),
                dueDate = any(),
                source = any(),
                status = any(),
                type = any(),
                externalId = any(),
            )
        }

        verify {
            manualEntryService.create(
                walletId = utilityAccount.walletId,
                title = "Fatura Light - RJ",
                amount = 100_00,
                categoryId = null,
                dueDate = LocalDate.of(2024, 6, 5),
                source = ActionSource.ConnectUtility(utilityAccount.addedBy),
                status = ManualEntryStatus.PAID,
                type = ManualEntryType.UTILITY_INVOICE,
                externalId = ExternalBillId("external-id-new", ExternalBillProvider.UTILITY_ACCOUNT),
            )
        }
    }

    @Test
    internal fun `não deve criar lançamento de contas pagas para usuários antigos`() {
        val walletFixture = WalletFixture()

        // Wallet de um usuário criado antes da data de cutoff
        every { walletService.findWalletOrNull(any()) } returns walletFixture.buildWallet(
            walletFounder = walletFixture.founder.copy(
                created = ZonedDateTime.parse("2024-07-01 00:01:00", dateTimeFormat),
            ),
        )

        val message = buildMessage(
            successWithInvoicesMessage.copy(
                invoices = listOf(
                    InvoiceTO(
                        barcode = "",
                        dueDate = "2024-06-05",
                        status = UtilityAccountInvoiceStatus.PAID,
                        externalId = "external-id-new",
                        amount = 100_00,
                    ),
                ),
            ),
        )

        every { utilityAccountRepository.find(any()) } returns utilityAccount.copy(
            createdAt = ZonedDateTime.parse(
                "2024-06-27 23:59:00",
                dateTimeFormat,
            ),
        )

        every {
            createBillService.findBillsByWalletAndEffectiveDueDate(utilityAccount.walletId, LocalDate.of(2024, 4, 5))
        } returns listOf()

        every {
            manualEntryService.findAllByWalletAndDueDate(utilityAccount.walletId, LocalDate.of(2024, 5, 5))
        } returns listOf()

        val date = getZonedDateTime()

        withGivenDateTime(date) {
            handler.handleMessage(message)
        }

        verify(exactly = 0) {
            manualEntryService.create(
                walletId = any(),
                title = any(),
                amount = any(),
                categoryId = any(),
                dueDate = any(),
                source = any(),
                status = any(),
                type = any(),
                externalId = any(),
            )
        }
    }

    @ParameterizedTest
    @EnumSource(
        value = UtilityAccountConnectionStatus::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["INVALID_CREDENTIALS", "DISCONNECTED"],
    )
    internal fun `deve enviar notificação caso o status de conciliação status permita`(status: UtilityAccountConnectionStatus) {
        val payload = successWithoutInvoicesMessage.copy(
            connectionId = pendingUtilityAccount.id.value,
            status = status,
            success = false,
        )

        val message = buildMessage(payload)

        every { utilityAccountRepository.find(any()) } returns pendingUtilityAccount

        val response = handler.handleMessage(message)

        verify {
            notificationAdapter.notifyUtilityAccountUpdatedStatus(any())
        }

        response shouldBe SQSHandlerResponse(true)

        val slot = mutableListOf<UtilityAccount>()
        verify {
            utilityAccountRepository.save(capture(slot))
        }
        with(slot[0]) {
            this.id.value shouldBe payload.connectionId
            this.status shouldBe status
        }
    }

    @Test
    internal fun `deve enviar notificação caso o status seja conectado`() {
        val payload = successWithoutInvoicesMessage.copy(
            connectionId = pendingUtilityAccount.id.value,
            status = UtilityAccountConnectionStatus.CONNECTED,
            success = true,
        )

        val message = buildMessage(payload)

        every { utilityAccountRepository.find(any()) } returns pendingUtilityAccount

        val response = handler.handleMessage(message)

        verify {
            notificationAdapter.notifyUtilityAccountConnected(any(), any())
        }

        response shouldBe SQSHandlerResponse(true)

        val slot = mutableListOf<UtilityAccount>()
        verify {
            utilityAccountRepository.save(capture(slot))
        }
        with(slot[0]) {
            this.id.value shouldBe payload.connectionId
            this.status shouldBe status
        }
    }

    @Test
    internal fun `deve calcular média das faturas caso o status seja conectado`() {
        val payload = successWithInvoicesMessage.copy(
            connectionId = pendingUtilityAccount.id.value,
            status = UtilityAccountConnectionStatus.CONNECTED,
            success = true,
            invoices = listOf(
                InvoiceTO(barcode = "", dueDate = "2024-07-10", amount = 3530, status = UtilityAccountInvoiceStatus.PAID, externalId = "invoice-1"),
                InvoiceTO(barcode = "", dueDate = "2024-06-10", amount = 1500, status = UtilityAccountInvoiceStatus.PAID, externalId = "invoice-2"),
                InvoiceTO(barcode = "", dueDate = "2024-05-10", amount = 1000, status = UtilityAccountInvoiceStatus.PAID, externalId = "invoice-3"),
            ),
        )

        val message = buildMessage(payload)

        every { utilityAccountRepository.find(any()) } returns pendingUtilityAccount

        val response = handler.handleMessage(message)

        response shouldBe SQSHandlerResponse(true)

        val slot = slot<UtilityAccountUpdateInfo?>()
        verify {
            notificationAdapter.notifyUtilityAccountConnected(any(), captureNullable(slot))
        }

        slot.captured shouldBe UtilityAccountUpdateInfo.BillsFound(amount = 3, averageValue = 2010)
    }

    @Test
    internal fun `deve ignorar faturas sem valor ao calcular média das faturas passadas`() {
        val payload = successWithInvoicesMessage.copy(
            connectionId = pendingUtilityAccount.id.value,
            status = UtilityAccountConnectionStatus.CONNECTED,
            success = true,
            invoices = listOf(
                InvoiceTO(barcode = "", dueDate = "2024-07-10", amount = 4000, status = UtilityAccountInvoiceStatus.PAID, externalId = "invoice-1"),
                InvoiceTO(barcode = "", dueDate = "2024-06-10", amount = 2000, status = UtilityAccountInvoiceStatus.PAID, externalId = "invoice-2"),
                InvoiceTO(barcode = bill.barcode!!.digitable, dueDate = "2024-05-10", amount = null, status = UtilityAccountInvoiceStatus.ACTIVE, externalId = "invoice-3"),
            ),
        )

        val message = buildMessage(payload)

        every { utilityAccountRepository.find(any()) } returns pendingUtilityAccount

        every { createBillService.createConcessionaria(request = any()) } returns CreateBillResult.SUCCESS(bill)

        val response = handler.handleMessage(message)

        response shouldBe SQSHandlerResponse(true)

        val slot = slot<UtilityAccountUpdateInfo?>()
        verify {
            notificationAdapter.notifyUtilityAccountConnected(any(), captureNullable(slot))
        }

        slot.captured shouldBe UtilityAccountUpdateInfo.BillsFound(amount = 3, averageValue = 3000)
    }

    @Test
    internal fun `não deve calcular média das faturas passadas se nenhuma tiver valor`() {
        val payload = successWithInvoicesMessage.copy(
            connectionId = pendingUtilityAccount.id.value,
            status = UtilityAccountConnectionStatus.CONNECTED,
            success = true,
            invoices = successWithInvoicesMessage.invoices,
        )

        val message = buildMessage(payload)

        every { utilityAccountRepository.find(any()) } returns pendingUtilityAccount

        every { createBillService.createConcessionaria(request = any()) } returns CreateBillResult.SUCCESS(bill)

        val response = handler.handleMessage(message)

        response shouldBe SQSHandlerResponse(true)

        val slot = slot<UtilityAccountUpdateInfo?>()
        verify {
            notificationAdapter.notifyUtilityAccountConnected(any(), captureNullable(slot))
        }

        slot.captured shouldBe null
    }

    @ParameterizedTest
    @EnumSource(
        value = UtilityAccountConnectionStatus::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["CONNECTION_ERROR", "DISCONNECTION_ERROR"],
    )
    internal fun `deve enviar notificação interna caso o status de conciliação seja uma falha que a operação será manual`(
        status: UtilityAccountConnectionStatus,
    ) {
        val payload = failureMessage.copy(status = status)
        val message = buildMessage(payload)

        every { utilityAccountService.find(any()) } returns pendingUtilityAccount

        val response = handler.handleMessage(message)

        response shouldBe SQSHandlerResponse(true)

        verify(exactly = 0) {
            notificationAdapter.notifyUtilityAccountUpdatedStatus(any())
        }

        val slot = slot<UtilityAccount>()
        verify {
            emailSenderService.sendRawEmail(any(), any(), any(), any(), any())
            utilityAccountRepository.save(capture(slot))
        }
        with(slot.captured) {
            this.id.value shouldBe pendingUtilityAccount.id.value
            this.status shouldBe status
            this.attempts shouldBe payload.attempts
        }
    }

    @Test
    internal fun `deve atualizar status de conta conectada quando o novo status for FINAL_ERROR`() {
        every { utilityAccountRepository.find(any()) } returns utilityAccount.copy(
            status = UtilityAccountConnectionStatus.CONNECTED,
        )

        val message = buildMessage(failureMessage.copy(status = UtilityAccountConnectionStatus.FINAL_ERROR))

        val response = handler.handleMessage(message)

        verify {
            notificationAdapter.notifyUtilityAccountUpdatedStatus(any())
        }

        val slot = slot<UtilityAccount>()
        verify {
            utilityAccountRepository.save(capture(slot))
        }

        with(slot.captured) {
            this.id.value shouldBe utilityAccount.id.value
            this.status shouldBe UtilityAccountConnectionStatus.FINAL_ERROR
        }

        response shouldBe SQSHandlerResponse(true)
    }

    @ParameterizedTest
    @EnumSource(
        value = UtilityAccountConnectionStatus::class,
        mode = EnumSource.Mode.EXCLUDE,
        names = ["INVALID_CREDENTIALS", "CONNECTION_ERROR", "FINAL_ERROR", "REQUIRES_RECONNECTION"],
    )
    internal fun `não deve atualizar conta conectada quando não for INVALID_CREDENTIALS, CONNECTION_ERROR, REQUIRES_RECONNECTION ou FINAL_ERROR`(
        status: UtilityAccountConnectionStatus,
    ) {
        every { utilityAccountRepository.find(any()) } returns utilityAccount.copy(
            status = UtilityAccountConnectionStatus.CONNECTED,
            utility = Utility.CEMIG,
        )

        val message = buildMessage(failureMessage.copy(status = status))

        val response = handler.handleMessage(message)

        verify(exactly = 0) {
            notificationAdapter.notifyUtilityAccountUpdatedStatus(any())
        }

        verify(exactly = 0) {
            utilityAccountRepository.save(any())
        }

        response shouldBe SQSHandlerResponse(true)
    }

    @ParameterizedTest
    @EnumSource(
        UtilityAccountConnectionStatus::class,
        mode = EnumSource.Mode.EXCLUDE,
        names = ["PENDING", "PENDING_DISCONNECTION", "CONNECTED"],
    )
    fun `se a utility não estiver no status pending, não deve atualizar para conectado`(status: UtilityAccountConnectionStatus) {
        every { utilityAccountRepository.find(any()) } returns utilityAccount.copy(status = status)

        val message = buildMessage(successWithoutInvoicesMessage)

        handler.handleMessage(message) shouldBe SQSHandlerResponse(true)

        verify(exactly = 0) {
            utilityAccountRepository.save(any())
        }
    }

    @ParameterizedTest
    @EnumSource(
        value = UtilityAccountConnectionStatus::class,
        mode = EnumSource.Mode.EXCLUDE,
        names = ["CONNECTED", "INVALID_CREDENTIALS", "DISCONNECTED", "FINAL_ERROR", "REQUIRES_RECONNECTION"],
    )
    internal fun `não deve enviar notificação para status não permitido`(status: UtilityAccountConnectionStatus) {
        every { utilityAccountRepository.find(any()) } returns pendingUtilityAccount

        val message = buildMessage(failureMessage.copy(status = status))

        val response = handler.handleMessage(message)

        verify {
            notificationAdapter wasNot called
        }

        response shouldBe SQSHandlerResponse(true)
    }

    @Test
    internal fun `deve atualizar contador de falhas de conta conectada quando resposta for CONNECTION_ERROR`() {
        every { utilityAccountRepository.find(utilityAccount.id) } returns utilityAccount.copy(
            status = UtilityAccountConnectionStatus.CONNECTED,
            utility = Utility.CEMIG,
            scanFailureCount = 3,
        )

        val message = buildMessage(
            failureMessage.copy(
                status = UtilityAccountConnectionStatus.CONNECTION_ERROR,
                connectionId = utilityAccount.id.value,
            ),
        )

        val response = handler.handleMessage(message)

        verify(exactly = 0) {
            notificationAdapter.notifyUtilityAccountUpdatedStatus(any())
        }

        val slot = slot<UtilityAccount>()
        verify {
            utilityAccountRepository.save(capture(slot))
        }

        response shouldBe SQSHandlerResponse(true)

        slot.captured.scanFailureCount shouldBe 4

        // Não deve atualizar status
        slot.captured.status shouldBe UtilityAccountConnectionStatus.CONNECTED
    }

    @ParameterizedTest
    @EnumSource(
        value = Utility::class,
        mode = EnumSource.Mode.MATCH_ALL,
    )
    internal fun `deve atualizar contador de invalid credentials de conta conectada quando resposta for INVALID_CREDENTIALS`(
        utility: Utility,
    ) {
        every { utilityAccountRepository.find(utilityAccount.id) } returns utilityAccount.copy(
            status = UtilityAccountConnectionStatus.CONNECTED,
            utility = utility,
            invalidCredentialsCount = 3,
        )

        val message = buildMessage(
            failureMessage.copy(
                status = UtilityAccountConnectionStatus.INVALID_CREDENTIALS,
                connectionId = utilityAccount.id.value,
            ),
        )

        val response = handler.handleMessage(message)

        verify(exactly = 0) {
            notificationAdapter.notifyUtilityAccountUpdatedStatus(any())
        }

        val slot = slot<UtilityAccount>()
        verify {
            utilityAccountRepository.save(capture(slot))
        }

        response shouldBe SQSHandlerResponse(true)

        val invalidCredentialsCount = 4
        slot.captured.invalidCredentialsCount shouldBe invalidCredentialsCount

        // Não deve atualizar status
        slot.captured.status shouldBe UtilityAccountConnectionStatus.CONNECTED
    }

    private fun buildMessage(message: UtilityAccountFlowResponseTO): Message {
        return Message.builder()
            .body(getObjectMapper().writeValueAsString(message))
            .build()
    }
}