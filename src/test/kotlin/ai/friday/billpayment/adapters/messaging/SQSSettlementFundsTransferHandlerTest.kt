package ai.friday.billpayment.adapters.messaging

import DynamoDBUtils
import ai.friday.billpayment.adapters.arbi.ArbiAdapter
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.CreditCardChargebackTrackDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbTransactionRepository
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.fingerprint.DeviceId
import ai.friday.billpayment.app.integrations.PixPaymentService
import ai.friday.billpayment.app.payment.PixPaymentResult
import ai.friday.billpayment.app.payment.PixPaymentStatus
import ai.friday.billpayment.app.payment.SettlementFundsTransfer
import ai.friday.billpayment.app.payment.SettlementFundsTransferType
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.pix.PixTransactionError
import ai.friday.billpayment.balance
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billIgnored
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentFailedRetryable
import ai.friday.billpayment.billPaymentSchedulePostponedByInsufficientFunds
import ai.friday.billpayment.billPaymentScheduleStarted
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.billReactivated
import ai.friday.billpayment.billRegisterUpdatedNotPayable
import ai.friday.billpayment.billScheduleCanceled
import ai.friday.billpayment.defaultTransactionEntityConverter
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.invoicePaid
import ai.friday.billpayment.pixKeyPaid
import ai.friday.billpayment.pixPaid
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDateTime
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.Called
import io.mockk.CapturingSlot
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDateTime
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource
import software.amazon.awssdk.services.sqs.model.Message
import software.amazon.awssdk.services.sqs.model.MessageAttributeValue

abstract class SQSSettlementFundsTransferHandlerTest {

    protected val pixPaymentService: PixPaymentService = mockk()

    protected val settlementFundsTransferConfiguration = SettlementFundsTransferConfiguration(
        payerName = "payerName",
        payerDocument = "payerDocument",
        recipientName = "recipientName",
        recipientDocument = "*************",
        recipientBankAccount = SettlementFundsTransferConfiguration.RecipientBankAccount(
            accountType = AccountType.CHECKING,
            routingNo = 2,
            accountNo = 2222,
            accountDv = "2",
            ispb = "********",
        ),
        originSettlementBankAccount = SettlementFundsTransferConfiguration.OriginSettlementBankAccount(
            accountType = AccountType.CHECKING,
            bankNo = 1,
            routingNo = 1,
            accountNo = 1111,
            accountDv = "1",
        ),
        originCashinBankAccount = SettlementFundsTransferConfiguration.OriginCashinBankAccount(
            accountType = AccountType.CHECKING,
            bankNo = 1,
            routingNo = 1,
            accountNo = 2222,
            accountDv = "2",
            ispb = "123456",
        ),
        description = "description",
    )
    protected val transactionId = TransactionId("********")

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val enhancedClient = DynamoDBUtils.setupDynamoDB()

    protected val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(enhancedClient),
        refundedClient = RefundedBillDynamoDAO(enhancedClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(enhancedClient),
    )
    private val billEventDAO = BillEventDynamoDAO(enhancedClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)
    private val transactionDAO = TransactionDynamoDAO(enhancedClient)
    private val creditCardChargebackTrackDAO = CreditCardChargebackTrackDynamoDAO(enhancedClient)
    private val billEventRepository = BillEventDBRepository(
        billEventDAO = billEventDAO,
        uniqueConstraintDAO = uniqueConstraintDAO,
        featureConfiguration = mockk(),
        transactionDynamo = transactionDynamo,
    )
    protected val transactionRepository = DynamoDbTransactionRepository(
        transactionDAO = transactionDAO,
        creditCardChargebackTrackDAO = creditCardChargebackTrackDAO,
        accountRepository = mockk {
            every {
                findAccountPaymentMethodByIdAndAccountId(any(), any())
            } returns balance
        },
        transactionEntityConverter = defaultTransactionEntityConverter(billEventRepository, mockk(), transactionDynamo),
    )

    private val deviceFingerprintService = mockk<DeviceFingerprintService> {
        every { getInternalDevicesAccount() } returns mockk {
            every { deviceIds } returns mapOf(AccountNumber("123") to DeviceId("mocked-device-id"))
        }
    }

    protected val arbiAdapterMock = mockk<ArbiAdapter> {}

    protected val handler = SQSSettlementFundsTransferHandler(
        amazonSQS = mockk(),
        amazonSNS = mockk(),
        pixPaymentService,
        handlerConfiguration = mockk(relaxed = true),
        transferConfiguration = settlementFundsTransferConfiguration,
        billRepository = billRepository,
        transactionRepository = transactionRepository,
        topicArn = "mockArn",
        deviceFingerprintService = deviceFingerprintService,
        arbiAdapter = arbiAdapterMock,
        features = mockk { every { getAll() } returns mockk { every { tefAtSettlementHandler } returns true } },
    )

    private val events = createEvents()

    protected val bill: Bill = Bill.build(events)

    private val fundsTransferSourceAccountType = fundsTransferSourceAccountType()

    protected val message = Message.builder()
        .body(getObjectMapper().writeValueAsString(billPaid.toBillEventDetailEntity()))
        .messageAttributes(
            mapOf(
                billTypeAttributeName to MessageAttributeValue.builder().dataType("String")
                    .stringValue(BillType.CONCESSIONARIA.name).build(),
            ),
        )
        .build()

    abstract fun createEvents(): MutableList<BillEvent>
    abstract fun fundsTransferSourceAccountType(): SettlementFundsTransferType
    abstract fun transaction(): Transaction
    abstract fun originBankAccount(): SettlementFundsTransferConfiguration.OriginBankAccount

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)

        events.forEach {
            billEventRepository.save(it)
        }
        billRepository.save(bill)

        transactionRepository.save(transaction())
    }

    @ParameterizedTest
    @EnumSource(PixPaymentStatus::class, mode = EnumSource.Mode.INCLUDE, names = ["ACKNOWLEDGED", "UNKNOWN"])
    fun `should remove message from queue when transfer is successful on first attempt`(
        pixPaymentStatus: PixPaymentStatus,
    ) {
        val slot = mockInitPayment(status = pixPaymentStatus, error = null)
        mockCheckPayment(status = PixPaymentStatus.SUCCESS, error = null)

        val response = handler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true

        verify {
            pixPaymentService.checkPaymentStatus(any(), bankOperationId = slot.captured)
            pixPaymentService.initPayment(
                command = withArg {
                    it.originBankAccount.accountNo shouldBe originBankAccount().accountNo
                },
                bankOperationId = any(),
                deviceId = null,
            )
        }

        val transfers = billRepository.findSettlementFundsTransfers(bill.billId)

        transfers.size shouldBe 1
        with(transfers.first()) {
            bankOperationId shouldBe slot.captured
            amount shouldBe bill.amountTotal
            status shouldBe BankOperationStatus.SUCCESS
            sourceAccountType shouldBe fundsTransferSourceAccountType
        }
    }

    @ParameterizedTest
    @EnumSource(BankOperationStatus::class, mode = EnumSource.Mode.INCLUDE, names = ["TIMEOUT", "UNKNOWN"])
    fun `should remove message from queue when transfer is successful on retry`(
        bankOperationStatus: BankOperationStatus,
    ) {
        billRepository.saveSettlementFundsTransfer(
            settlementFundsTransfer = SettlementFundsTransfer(
                bankOperationId = BankOperationId(value = "mocked-bank-operation-id"),
                billId = bill.billId,
                status = bankOperationStatus,
                amount = 0,
                created = getLocalDateTime(),
                transactionId = billPaid.transactionId!!,
                sourceAccountType = fundsTransferSourceAccountType,
                sameBank = false,
            ),
        )

        val slot = mockCheckPayment(status = PixPaymentStatus.SUCCESS, error = null)

        val response = handler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true

        verify(exactly = 0) {
            pixPaymentService.initPayment(command = any(), bankOperationId = any(), deviceId = null)
        }

        val transfers = billRepository.findSettlementFundsTransfers(bill.billId)

        transfers.size shouldBe 1
        with(transfers.first()) {
            bankOperationId shouldBe slot.captured
            amount shouldBe bill.amountTotal
            status shouldBe BankOperationStatus.SUCCESS
            sourceAccountType shouldBe fundsTransferSourceAccountType
        }
    }

    @ParameterizedTest
    @EnumSource(
        BankOperationStatus::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["INSUFFICIENT_FUNDS", "ERROR", "INVALID_DATA"],
    )
    fun `should restart payment when retrying a failed transfer`(bankOperationStatus: BankOperationStatus) {
        val settlementFundsTransfer = SettlementFundsTransfer(
            bankOperationId = BankOperationId(value = "mocked-bank-operation-id"),
            billId = bill.billId,
            status = bankOperationStatus,
            amount = 0,
            created = LocalDateTime.now(),
            transactionId = billPaid.transactionId!!,
            sourceAccountType = fundsTransferSourceAccountType,
            sameBank = false,
        )
        billRepository.saveSettlementFundsTransfer(settlementFundsTransfer = settlementFundsTransfer)

        val initSlot = mockInitPayment(status = PixPaymentStatus.ACKNOWLEDGED, error = null)
        val checkSlot = mockCheckPayment(status = PixPaymentStatus.SUCCESS, error = null)

        val response = handler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true

        initSlot.captured shouldNotBe settlementFundsTransfer.bankOperationId
        checkSlot.captured shouldBe initSlot.captured

        val transfers = billRepository.findSettlementFundsTransfers(bill.billId)

        transfers.size shouldBe 2
        with(transfers.first { it.bankOperationId == initSlot.captured }) {
            bankOperationId shouldBe initSlot.captured
            amount shouldBe bill.amountTotal
            status shouldBe BankOperationStatus.SUCCESS
            sourceAccountType shouldBe fundsTransferSourceAccountType
        }
    }

    @Test
    fun `should remove message from queue when retrying a previously successful transfer`() {
        val settlementFundsTransfer = SettlementFundsTransfer(
            bankOperationId = BankOperationId(value = "mocked-bank-operation-id"),
            billId = bill.billId,
            status = BankOperationStatus.SUCCESS,
            amount = 0,
            created = LocalDateTime.now(),
            transactionId = billPaid.transactionId!!,
            sourceAccountType = fundsTransferSourceAccountType,
            sameBank = false,
        )
        billRepository.saveSettlementFundsTransfer(settlementFundsTransfer = settlementFundsTransfer)

        val response = handler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true

        verify {
            pixPaymentService wasNot Called
        }
    }

    @Test
    fun `should not process bill with different transaction id`() {
        mockInitPayment(status = PixPaymentStatus.ACKNOWLEDGED, error = null)
        mockCheckPayment(status = PixPaymentStatus.ACKNOWLEDGED, error = null)
        val settlementFundsTransfer = SettlementFundsTransfer(
            bankOperationId = BankOperationId(value = "mocked-bank-operation-id"),
            billId = bill.billId,
            status = BankOperationStatus.SUCCESS,
            amount = 0,
            created = LocalDateTime.now(),
            transactionId = TransactionId("transaction"),
            sourceAccountType = fundsTransferSourceAccountType,
            sameBank = false,
        )
        billRepository.saveSettlementFundsTransfer(settlementFundsTransfer = settlementFundsTransfer)

        handler.handleMessage(message)

        verify(exactly = 1) {
            pixPaymentService.initPayment(
                command = any(),
                bankOperationId = any(),
                deviceId = null,
            )
            pixPaymentService.initPayment(
                command = withArg {
                    it.originBankAccount.accountNo shouldBe originBankAccount().accountNo
                },
                bankOperationId = any(),
                deviceId = null,
            )
        }
    }

    @ParameterizedTest
    @MethodSource("checkStatusErrorProvider")
    fun `should not delete message from queue on error checking status`(
        pixPaymentStatus: PixPaymentStatus,
        pixTransactionError: PixTransactionError?,
        expectedTransferStatus: BankOperationStatus,
    ) {
        val slot = mockInitPayment(status = PixPaymentStatus.ACKNOWLEDGED, error = null)
        mockCheckPayment(status = pixPaymentStatus, error = pixTransactionError)

        val response = handler.handleMessage(message)

        response.shouldDeleteMessage shouldBe false

        verify {
            pixPaymentService.checkPaymentStatus(any(), bankOperationId = slot.captured)
            pixPaymentService.initPayment(
                command = withArg {
                    it.originBankAccount.accountNo shouldBe originBankAccount().accountNo
                },
                bankOperationId = any(),
                deviceId = null,
            )
        }

        val transfers = billRepository.findSettlementFundsTransfers(bill.billId)

        transfers.size shouldBe 1
        with(transfers.first()) {
            bankOperationId shouldBe slot.captured
            amount shouldBe bill.amountTotal
            status shouldBe expectedTransferStatus
            sourceAccountType shouldBe fundsTransferSourceAccountType
        }
    }

    @ParameterizedTest
    @MethodSource("initPaymentErrorProvider")
    fun `should not delete message from queue on error starting payment`(
        pixPaymentStatus: PixPaymentStatus,
        pixTransactionError: PixTransactionError?,
        expectedTransferStatus: BankOperationStatus,
    ) {
        val slot = mockInitPayment(status = pixPaymentStatus, error = pixTransactionError)

        val response = handler.handleMessage(message)

        response.shouldDeleteMessage shouldBe false

        verify(exactly = 0) {
            pixPaymentService.checkPaymentStatus(any(), bankOperationId = any())
        }
        verify(exactly = 1) {
            pixPaymentService.initPayment(
                command = withArg {
                    it.originBankAccount.accountNo shouldBe originBankAccount().accountNo
                },
                bankOperationId = any(),
                deviceId = null,
            )
        }

        val transfers = billRepository.findSettlementFundsTransfers(bill.billId)

        transfers.size shouldBe 1
        with(transfers.first()) {
            bankOperationId shouldBe slot.captured
            amount shouldBe bill.amountTotal
            status shouldBe expectedTransferStatus
            sourceAccountType shouldBe fundsTransferSourceAccountType
        }
    }

    @Test
    fun `should use most recent transfer status on retry`() {
        billRepository.saveSettlementFundsTransfer(
            settlementFundsTransfer = SettlementFundsTransfer(
                bankOperationId = BankOperationId(value = "mocked-older-bank-operation-id"),
                billId = bill.billId,
                status = BankOperationStatus.ERROR,
                amount = 0,
                created = LocalDateTime.now().minusMinutes(5),
                transactionId = billPaid.transactionId!!,
                sourceAccountType = fundsTransferSourceAccountType,
                sameBank = false,
            ),
        )
        billRepository.saveSettlementFundsTransfer(
            settlementFundsTransfer = SettlementFundsTransfer(
                bankOperationId = BankOperationId(value = "mocked-recent-bank-operation-id"),
                billId = bill.billId,
                status = BankOperationStatus.UNKNOWN,
                amount = 0,
                created = LocalDateTime.now(),
                transactionId = billPaid.transactionId!!,
                sourceAccountType = fundsTransferSourceAccountType,
                sameBank = false,
            ),
        )

        val slot = mockCheckPayment(status = PixPaymentStatus.SUCCESS, error = null)

        val response = handler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true

        verify(exactly = 0) {
            pixPaymentService.initPayment(command = any(), bankOperationId = any(), deviceId = null)
        }

        val transfers = billRepository.findSettlementFundsTransfers(bill.billId)

        transfers.size shouldBe 2
        with(transfers.first { it.bankOperationId.value == "mocked-recent-bank-operation-id" }) {
            bankOperationId shouldBe slot.captured
            amount shouldBe bill.amountTotal
            status shouldBe BankOperationStatus.SUCCESS
            sourceAccountType shouldBe fundsTransferSourceAccountType
        }
    }

    @ParameterizedTest
    @MethodSource("ignoredEvents")
    fun `should ignore events other then boleto billPaid`(ignoredEvent: BillEvent, billType: BillType) {
        val ignoredMessage = Message.builder()
            .body(getObjectMapper().writeValueAsString(ignoredEvent.toBillEventDetailEntity()))
            .messageAttributes(
                mapOf(
                    billTypeAttributeName to MessageAttributeValue.builder().dataType("String")
                        .stringValue(billType.name).build(),
                ),
            )
            .build()

        val response = handler.handleMessage(ignoredMessage)

        verify {
            pixPaymentService wasNot Called
        }

        response.shouldDeleteMessage shouldBe true
    }

    protected fun mockInitPayment(
        status: PixPaymentStatus,
        error: PixTransactionError?,
    ): CapturingSlot<BankOperationId> {
        val slot = slot<BankOperationId>()
        every {
            pixPaymentService.initPayment(command = any(), bankOperationId = capture(slot), deviceId = null)
        } returns PixPaymentResult(status = status, idOrdemPagamento = "", endToEnd = "", error = error)
        return slot
    }

    protected fun mockCheckPayment(
        status: PixPaymentStatus,
        error: PixTransactionError?,
    ): CapturingSlot<BankOperationId> {
        val slot = slot<BankOperationId>()
        every {
            pixPaymentService.checkPaymentStatus(bankAccount = any(), bankOperationId = capture(slot))
        } returns PixPaymentResult(status = status, idOrdemPagamento = "", endToEnd = "", error = error)
        return slot
    }

    companion object {
        @JvmStatic
        fun checkStatusErrorProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    PixPaymentStatus.REFUSED,
                    PixTransactionError.SettlementGenericPermanentError,
                    BankOperationStatus.ERROR,
                ),
                Arguments.arguments(
                    PixPaymentStatus.REFUSED,
                    PixTransactionError.SettlementGenericTemporaryError,
                    BankOperationStatus.ERROR,
                ),
                Arguments.arguments(
                    PixPaymentStatus.REFUSED,
                    PixTransactionError.BusinessInsufficientBalance,
                    BankOperationStatus.INSUFFICIENT_FUNDS,
                ),
                Arguments.arguments(
                    PixPaymentStatus.FAILED,
                    PixTransactionError.BusinessInsufficientBalance,
                    BankOperationStatus.INSUFFICIENT_FUNDS,
                ),
                Arguments.arguments(
                    PixPaymentStatus.FAILED,
                    PixTransactionError.SettlementGenericTemporaryError,
                    BankOperationStatus.ERROR,
                ),
                Arguments.arguments(
                    PixPaymentStatus.UNKNOWN,
                    PixTransactionError.SettlementGenericTemporaryError,
                    BankOperationStatus.UNKNOWN,
                ),
                Arguments.arguments(PixPaymentStatus.ACKNOWLEDGED, null, BankOperationStatus.UNKNOWN),
            )
        }

        @JvmStatic
        fun initPaymentErrorProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    PixPaymentStatus.REFUSED,
                    PixTransactionError.SettlementDestinationAccountNotFound,
                    BankOperationStatus.INVALID_DATA,
                ),
                Arguments.arguments(
                    PixPaymentStatus.REFUSED,
                    PixTransactionError.PaymentGenericTemporaryError,
                    BankOperationStatus.ERROR,
                ),
                Arguments.arguments(
                    PixPaymentStatus.FAILED,
                    PixTransactionError.SettlementDestinationAccountNotFound,
                    BankOperationStatus.INVALID_DATA,
                ),
                Arguments.arguments(
                    PixPaymentStatus.FAILED,
                    PixTransactionError.PaymentGenericTemporaryError,
                    BankOperationStatus.ERROR,
                ),
            )
        }

        @JvmStatic
        fun ignoredEvents(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(billAdded, BillType.CONCESSIONARIA),
                Arguments.arguments(billIgnored, BillType.CONCESSIONARIA),
                Arguments.arguments(billRegisterUpdatedNotPayable, BillType.CONCESSIONARIA),
                Arguments.arguments(billPaymentStart, BillType.CONCESSIONARIA),
                Arguments.arguments(billPaymentFailedRetryable, BillType.CONCESSIONARIA),
                Arguments.arguments(billReactivated, BillType.CONCESSIONARIA),
                Arguments.arguments(billPaymentScheduled, BillType.CONCESSIONARIA),
                Arguments.arguments(billScheduleCanceled, BillType.CONCESSIONARIA),
                Arguments.arguments(billPaymentScheduleStarted, BillType.CONCESSIONARIA),
                Arguments.arguments(billPaymentSchedulePostponedByInsufficientFunds, BillType.CONCESSIONARIA),
                Arguments.arguments(invoicePaid, BillType.INVOICE),
                Arguments.arguments(pixPaid, BillType.PIX),
                Arguments.arguments(pixKeyPaid, BillType.PIX),
            )
        }
    }
}