package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.AmountUpdated
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.UpdatedScheduleData
import ai.friday.billpayment.app.integrations.PaymentSchedulingForecastService
import ai.friday.billpayment.app.payment.PaymentSchedulingEventService
import ai.friday.billpayment.app.payment.PaymentSchedulingService
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billScheduleCanceled
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.BillEventFixture.scheduleUpdated
import ai.friday.billpayment.scheduledBill
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message
import software.amazon.awssdk.services.sqs.model.MessageAttributeValue

internal class SQSSchedulingHandlerTest {

    private val amazonSQS: SqsClient = mockk()
    private val amazonSNS: SnsClient = mockk()
    private val configuration: SQSMessageHandlerConfiguration = mockk(relaxed = true)
    private val paymentSchedulingService: PaymentSchedulingService = mockk(relaxed = true)
    private val paymentSchedulingEventService: PaymentSchedulingEventService = mockk(relaxed = true)
    private val paymentSchedulingForecastService: PaymentSchedulingForecastService = mockk(relaxed = true)

    private val sqsSchedulingHandler = SQSSchedulingHandler(
        amazonSQS = amazonSQS,
        amazonSNS = amazonSNS,
        topicArn = "topic-arn",
        configuration = configuration,
        paymentSchedulingService = paymentSchedulingService,
        paymentSchedulingEventService = paymentSchedulingEventService,
        schedulingForecastService = paymentSchedulingForecastService,
        arnPrefix = "prefix",
    )

    @Test
    fun `deve processar um evento de atualização de valor de uma conta`() {
        val updatedAmountEvent = AmountUpdated(
            billId = scheduledBill.billId,
            created = getZonedDateTime().toInstant().toEpochMilli(),
            walletId = scheduledBill.walletId,
            actionSource = ActionSource.Api(AccountId(ACCOUNT_ID)),
            amount = 100,
        )

        val message =
            Message.builder().body(getObjectMapper().writeValueAsString(updatedAmountEvent.toBillEventDetailEntity()))
                .messageAttributes(
                    mapOf(
                        eventTypeAttributeName to MessageAttributeValue.builder().dataType("String")
                            .stringValue(BillEventType.AMOUNT_UPDATED.name).build(),
                    ),
                )
                .build()

        sqsSchedulingHandler.handleMessage(message)

        verify(exactly = 1) {
            paymentSchedulingEventService.updateAmount(
                walletId = scheduledBill.walletId,
                billId = scheduledBill.billId,
                actionSource = ActionSource.Api(AccountId(ACCOUNT_ID)),
                updatedAmount = 100L,
                shouldProcessScheduledBills = true,
            )
        }
    }

    @Test
    fun `deve processar um evento de agendamento`() {
        val event = billPaymentScheduled

        val message = Message.builder().body(getObjectMapper().writeValueAsString(event.toBillEventDetailEntity()))
            .messageAttributes(
                mapOf(
                    eventTypeAttributeName to MessageAttributeValue.builder().dataType("String")
                        .stringValue(BillEventType.PAYMENT_SCHEDULED.name).build(),
                ),
            ).build()

        sqsSchedulingHandler.handleMessage(message)

        verify(exactly = 1) {
            paymentSchedulingForecastService.provision(event)
        }
    }

    @Test
    fun `deve processar um cancelamento de agendamento`() {
        val event = billScheduleCanceled

        val message = Message.builder().body(getObjectMapper().writeValueAsString(event.toBillEventDetailEntity()))
            .messageAttributes(
                mapOf(
                    eventTypeAttributeName to MessageAttributeValue.builder().dataType("String")
                        .stringValue(BillEventType.PAYMENT_SCHEDULE_CANCELED.name).build(),
                ),
            ).build()

        sqsSchedulingHandler.handleMessage(message)

        verify(exactly = 1) {
            paymentSchedulingForecastService.deprovision(event)
            paymentSchedulingService.cancelSchedule(event.walletId, event.billId, event.batchSchedulingId)
        }
    }

    @Test
    fun `deve processar um evento de atualizacao do calculation_id`() {
        every { paymentSchedulingEventService.updateCalculationId(any()) } just runs

        sqsSchedulingHandler.handleMessage(SQSMessageFixture.createByEvent(scheduleUpdated()))

        verify {
            paymentSchedulingEventService.updateCalculationId(
                withArg {
                    it.updatedScheduleData.shouldBeTypeOf<UpdatedScheduleData.NewCalculationId>()
                },
            )
        }
    }
}