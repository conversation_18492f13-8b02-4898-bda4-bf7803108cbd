package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.payment.BoletoSettlementResult
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.app.payment.PixPaymentStatus
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.SettlementFundsTransferType
import ai.friday.billpayment.balance
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentScheduleStarted
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billScheduleCanceled
import ai.friday.billpayment.concessionariaBill
import ai.friday.billpayment.createSinglePaymentDataWithBalance
import ai.friday.billpayment.createTransaction
import ai.friday.billpayment.integration.ACCOUNT_ID
import io.kotest.matchers.shouldBe
import io.mockk.verify
import org.junit.jupiter.api.Test

class SQSSettlementFundsTransferHandlerWithBalanceTest : SQSSettlementFundsTransferHandlerTest() {

    override fun createEvents() =
        mutableListOf(
            billAdded,
            billPaymentScheduled,
            billPaymentScheduleStarted,
            billPaid,
            billScheduleCanceled,
        )

    override fun fundsTransferSourceAccountType(): SettlementFundsTransferType =
        SettlementFundsTransferType.SETTLEMENT_ACCOUNT

    override fun transaction() = createTransaction(
        gateway = FinancialServiceGateway.CELCOIN,
        finalPartnerName = null,
    )

    private fun createTransaction(gateway: FinancialServiceGateway, finalPartnerName: FinancialServiceGateway?) =
        createTransaction(
            transactionId = transactionId,
            paymentData = createSinglePaymentDataWithBalance(balance, concessionariaBill.amountTotal),
            settlementData = SettlementData(
                settlementTarget = concessionariaBill,
                serviceAmountTax = 0,
                totalAmount = concessionariaBill.amountTotal,
                settlementOperation = BoletoSettlementResult(
                    gateway = gateway,
                    status = BoletoSettlementStatus.AUTHORIZED,
                    bankTransactionId = "1",
                    externalNsu = 1,
                    externalTerminal = ACCOUNT_ID,
                    errorCode = "",
                    errorDescription = "",
                    finalPartnerName = finalPartnerName,
                ),
            ),
            nsu = 1,
            walletId = concessionariaBill.walletId,
        )

    override fun originBankAccount() = settlementFundsTransferConfiguration.originSettlementBankAccount

    @Test
    fun `quando o gateway é CELCOIN deve transferir fundos para a CELCOIN`() {
        val transaction = createTransaction(
            gateway = FinancialServiceGateway.CELCOIN,
            finalPartnerName = null,
        )
        transactionRepository.save(transaction)

        val slot = mockInitPayment(status = PixPaymentStatus.ACKNOWLEDGED, error = null)
        mockCheckPayment(status = PixPaymentStatus.SUCCESS, error = null)

        val response = handler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true

        verify {
            pixPaymentService.checkPaymentStatus(any(), bankOperationId = slot.captured)
            pixPaymentService.initPayment(
                command = withArg {
                    it.originBankAccount.accountNo shouldBe originBankAccount().accountNo
                },
                bankOperationId = any(),
                deviceId = null,
            )
        }
    }

    @Test
    fun `quando o gateway é FRIDAY e o finalPartnerName é ARBI não deve transferir fundos para a CELCOIN`() {
        val transaction = createTransaction(
            gateway = FinancialServiceGateway.FRIDAY,
            finalPartnerName = FinancialServiceGateway.ARBI,
        )
        transactionRepository.save(transaction)

        val response = handler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true

        verify(exactly = 0) {
            pixPaymentService.checkPaymentStatus(any(), any())
            pixPaymentService.initPayment(command = any(), bankOperationId = any(), deviceId = null)
        }
    }

    @Test
    fun `quando o gateway é FRIDAY e o finalPartnerName é CELCOIN deve transferir fundos para a CELCOIN`() {
        val transaction = createTransaction(
            gateway = FinancialServiceGateway.FRIDAY,
            finalPartnerName = FinancialServiceGateway.CELCOIN,
        )
        transactionRepository.save(transaction)

        val slot = mockInitPayment(status = PixPaymentStatus.ACKNOWLEDGED, error = null)
        mockCheckPayment(status = PixPaymentStatus.SUCCESS, error = null)

        val response = handler.handleMessage(message)

        response.shouldDeleteMessage shouldBe true

        verify {
            pixPaymentService.checkPaymentStatus(any(), bankOperationId = slot.captured)
            pixPaymentService.initPayment(
                command = withArg {
                    it.originBankAccount.accountNo shouldBe originBankAccount().accountNo
                },
                bankOperationId = any(),
                deviceId = null,
            )
        }
    }
}