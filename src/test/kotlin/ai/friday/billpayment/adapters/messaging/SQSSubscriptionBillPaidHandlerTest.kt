package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.subscription.SubscriptionService
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.getPaidBill
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message

class SQSSubscriptionBillPaidHandlerTest {

    private val billRepository: BillRepository = mockk()

    val accountId = ACCOUNT.accountId
    val bill = getPaidBill(
        walletId = billPaid.walletId,
        accountId = accountId,
        subscriptionFee = true,
        source = ActionSource.SubscriptionRecurrence(
            accountId = accountId,
            recurrenceId = RecurrenceId("FOO"),
        ),
    )

    private val configurationMock: SQSMessageHandlerConfiguration = mockk {
        every { subscriptionBillPaidQueueName } returns "queue-name"
    }

    private val subscriptionService = mockk<SubscriptionService>(relaxed = true)

    private val handler = SQSSubscriptionBillPaidHandler(
        amazonSQS = mockk(),
        amazonSNS = mockk(),
        configuration = configurationMock,
        topicArn = "topic-name",
        billRepository = billRepository,
        subscriptionService = subscriptionService,
        accountService = mockk {
            every { findAccountById(accountId) } returns ACCOUNT
        },
    )

    private val message =
        Message.builder().body(getObjectMapper().writeValueAsString(billPaid.toBillEventDetailEntity()))
            .build()

    @Test
    fun `deve manter a mensagem na fila quando nao encontra a conta associada ao evento de pagamento`() {
        every {
            billRepository.findBill(billPaid.billId, billPaid.walletId)
        } throws IllegalStateException()

        val result = handler.handleMessage(message)

        result.shouldDeleteMessage shouldBe false
    }

    @Test
    fun `deve remover a mensagem da fila quando o evento de pagamento eh de uma conta que nao eh de assinatura`() {
        every {
            billRepository.findBill(billPaid.billId, billPaid.walletId)
        } returns bill.copy(subscriptionFee = false)

        val result = handler.handleMessage(message)

        result.shouldDeleteMessage shouldBe true
    }

    @Test
    fun `deve sincronizar a assinatura e remover a mensagem da fila quando o evento de pagamento eh de uma conta de assinatura`() {
        every {
            billRepository.findBill(billPaid.billId, billPaid.walletId)
        } returns bill.copy(subscriptionFee = true)

        every {
            subscriptionService.handleSubscriptionPayment(ACCOUNT.accountId)
        } returns Unit.right()

        val result = handler.handleMessage(message)

        verify {
            subscriptionService.handleSubscriptionPayment(ACCOUNT.accountId)
        }

        result.shouldDeleteMessage shouldBe true
    }
}