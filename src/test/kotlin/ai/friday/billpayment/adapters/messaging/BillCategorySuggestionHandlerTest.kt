package ai.friday.billpayment.adapters.messaging

import DynamoDBUtils
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.billcategory.BillCategorySuggestion
import ai.friday.billpayment.app.billcategory.BillCategorySuggestionService
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.message
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class BillCategorySuggestionHandlerTest {
    private val billCategorySuggestionService: BillCategorySuggestionService = mockk(relaxed = true) {
        every {
            setBillCategorySuggestions(any(), any(), any())
        } returns Unit.right()
    }

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dbEnhancedClient = DynamoDBUtils.setupDynamoDB()

    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(dbEnhancedClient),
        refundedClient = RefundedBillDynamoDAO(dbEnhancedClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(dbEnhancedClient),
    )

    private val billCategoryHandler =
        BillCategorySuggestionHandler(
            amazonSQS = mockk(),
            amazonSNS = mockk(),
            configuration = mockk(relaxed = true),
            topicArn = "",
            billCategorySuggestionService = billCategorySuggestionService,
            billRepository = billRepository,
        )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        billRepository.save(Bill.build(billAdded))
    }

    @Test
    fun `quando chegar uma adicao de conta, deve tentar sugerir uma categoria`() {
        val message = message(billAdded.toBillEventDetailEntity())

        val suggestions = listOf(BillCategorySuggestion(PFMCategoryId("1"), 80), BillCategorySuggestion(PFMCategoryId("2"), 70))
        val bill = Bill.build(billAdded)

        every {
            billCategorySuggestionService.suggestCategories(bill.toBillView())
        } returns suggestions

        every {
            billCategorySuggestionService.filterCategories(suggestions)
        } returns suggestions

        billCategoryHandler.handleMessage(message) shouldBe SQSHandlerResponse(shouldDeleteMessage = true)

        verify {
            billCategorySuggestionService.suggestCategories(bill.toBillView())
            billCategorySuggestionService.filterCategories(suggestions)
            billCategorySuggestionService.setBillCategorySuggestions(
                billId = bill.billId,
                walletId = bill.walletId,
                categorySuggestions = suggestions,
            )
        }
    }

    private fun Bill.toBillView() = billRepository.findBill(billId, walletId)
}