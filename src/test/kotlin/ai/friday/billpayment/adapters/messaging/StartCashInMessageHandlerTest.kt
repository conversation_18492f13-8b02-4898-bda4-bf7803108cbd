package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.cashIn.CashInExecutor
import ai.friday.billpayment.app.cashIn.StartCashInMessage
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.creditCardCashInTransaction
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import software.amazon.awssdk.services.sqs.model.Message

class StartCashInMessageHandlerTest {

    private val transactionService = mockk<TransactionService>() {
        every {
            findTransactionById(creditCardCashInTransaction.id)
        } returns creditCardCashInTransaction
    }

    private val cashInExecutor = mockk<CashInExecutor>(relaxUnitFun = true)

    private val handler = StartCashInMessageHandler(
        amazonSQS = mockk(),
        configuration = mockk(),
        transactionService = transactionService,
        cashInExecutor = cashInExecutor,
        startCashInQueueName = "queueName",
    )

    private val messageBody = StartCashInMessage(
        transactionId = creditCardCashInTransaction.id.value,
    )

    private val message = Message.builder()
        .body(
            getObjectMapper().writeValueAsString(
                messageBody,
            ),
        ).build()

    @Test
    fun `deve remover da fila em caso de sucesso`() {
        val result = handler.handleMessage(message)

        result.shouldDeleteMessage shouldBe true

        val slot = slot<Transaction>()

        verify {
            cashInExecutor.execute(capture(slot))
        }

        slot.captured shouldBe creditCardCashInTransaction
    }

    @Test
    fun `deve manter na fila em caso de erro retentavel`() {
        every {
            transactionService.findTransactionById(any())
        } throws NoStackTraceException()

        assertThrows<NoStackTraceException> {
            handler.handleMessage(message)
        }
    }

    @Test
    fun `deve remover da fila em caso de erro nao retentavel`() {
        every {
            cashInExecutor.execute(any())
        } throws NoStackTraceException()

        val result = handler.handleMessage(message)

        result.shouldDeleteMessage shouldBe true
    }

    @Test
    fun `deve manter na fila em caso de erro`() {
        val result = handler.handleError(message, IllegalStateException())

        result.shouldDeleteMessage shouldBe false
    }
}