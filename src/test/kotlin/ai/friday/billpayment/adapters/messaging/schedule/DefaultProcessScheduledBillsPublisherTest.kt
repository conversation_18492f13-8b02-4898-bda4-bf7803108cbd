package ai.friday.billpayment.adapters.messaging.schedule

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.wallet.WalletId
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

class DefaultProcessScheduledBillsPublisherTest {

    private val messagePublisher = mockk<MessagePublisher>()

    private val publisher = DefaultProcessScheduledBillsPublisher(messagePublisher, "queue")

    private val walletId = WalletId("walletId")

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `deve enviar a mensagem pra fila`(includeSubscription: Boolean) {
        val scheduleDate = LocalDate.of(2023, 8, 10)

        publisher.publish(walletId, scheduleDate, includeSubscription)

        verify {
            messagePublisher.sendMessage(
                "queue",
                withArg<ProcessScheduledBillsCommand> {
                    it.walletId shouldBe walletId
                    it.scheduleDate shouldBe scheduleDate
                    it.includeSubscription shouldBe includeSubscription
                },
            )
        }
    }

    @Test
    fun `nao deve lancar exception quando ocorrer um erro`() {
        val scheduleDate = LocalDate.of(2023, 8, 10)
        every {
            messagePublisher.sendMessage(any(), any())
        } throws NoStackTraceException()
        assertDoesNotThrow {
            publisher.publish(walletId, scheduleDate, true)
        }
    }
}