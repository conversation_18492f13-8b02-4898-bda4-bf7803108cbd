package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.banking.SynchronizeBankAccountTO
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message
import software.amazon.awssdk.services.sqs.model.MessageSystemAttributeName

class SQSPixDepositNotificationHandlerTest {

    private val internalBankServiceMock: InternalBankService = mockk()

    private val handler = SQSPixDepositNotificationHandler(
        internalBankService = internalBankServiceMock,
        maxRetries = 30,
    )

    private val synchronizeTO = SynchronizeBankAccountTO(
        bankNo = 123L,
        routingNo = 456L,
        fullAccountNumber = "7890",
    )

    @Test
    fun `quando conseguir sincronizar a conta não deve retentar mensagem`() {
        every { internalBankServiceMock.synchronizeBankAccount(any(), any(), any(), any()) } returns true

        val message = Message.builder().body(
            getObjectMapper().writeValueAsString(synchronizeTO),
        ).attributes(
            mapOf(
                MessageSystemAttributeName.APPROXIMATE_RECEIVE_COUNT to "0",
            ),
        ).build()

        val response = handler.handleMessage(message)

        verify {
            internalBankServiceMock.synchronizeBankAccount(
                bankNo = synchronizeTO.bankNo,
                routingNo = synchronizeTO.routingNo,
                accountNumber = AccountNumber(synchronizeTO.fullAccountNumber),
                any(),
            )
        }

        response.shouldDeleteMessage shouldBe true
    }

    @Test
    fun `quando não conseguir sincronizar a conta deve retentar mensagem`() {
        every { internalBankServiceMock.synchronizeBankAccount(any(), any(), any(), any()) } returns false

        val message = Message.builder().body(
            getObjectMapper().writeValueAsString(synchronizeTO),
        ).attributes(
            mapOf(
                MessageSystemAttributeName.APPROXIMATE_RECEIVE_COUNT to "0",
            ),
        ).build()

        val response = handler.handleMessage(message)

        verify {
            internalBankServiceMock.synchronizeBankAccount(
                bankNo = synchronizeTO.bankNo,
                routingNo = synchronizeTO.routingNo,
                accountNumber = AccountNumber(synchronizeTO.fullAccountNumber),
                any(),
            )
        }

        response.shouldDeleteMessage shouldBe false
    }

    @Test
    fun `quando exceder o número de tentativas não deveria retentar a mensagem`() {
        every { internalBankServiceMock.synchronizeBankAccount(any(), any(), any(), any()) } returns false

        val message = Message.builder().body(
            getObjectMapper().writeValueAsString(synchronizeTO),
        ).attributes(
            mapOf(
                MessageSystemAttributeName.APPROXIMATE_RECEIVE_COUNT to "30",
            ),
        ).build()

        val response = handler.handleMessage(message)

        verify {
            internalBankServiceMock.synchronizeBankAccount(
                bankNo = synchronizeTO.bankNo,
                routingNo = synchronizeTO.routingNo,
                accountNumber = AccountNumber(synchronizeTO.fullAccountNumber),
                any(),
            )
        }

        response.shouldDeleteMessage shouldBe true
    }
}