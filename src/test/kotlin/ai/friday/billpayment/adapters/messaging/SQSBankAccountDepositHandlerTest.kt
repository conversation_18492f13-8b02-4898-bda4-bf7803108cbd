package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.balance.Balance
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.BankOperationExecuted
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.banking.InternalBankStatementItem
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.CashInInstrumentationService
import ai.friday.billpayment.app.integrations.InternalBankRepository
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.OnePixPayInstrumentation
import ai.friday.billpayment.app.integrations.ScheduledBillRepository
import ai.friday.billpayment.app.integrations.TEDService
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.payment.DefaultScheduledBillPaymentService
import ai.friday.billpayment.app.payment.DirectTEDStatus
import ai.friday.billpayment.app.payment.DirectTEDUndoneResult
import ai.friday.billpayment.app.payment.NotifyDepositService
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.toPayer
import ai.friday.billpayment.app.payment.transaction.UndoTransaction
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.bankStatementItem
import ai.friday.billpayment.createPaymentMethodsDetailWithBalance
import ai.friday.billpayment.createSinglePaymentDataWithBalance
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.messaging.MessageHandlerResponse
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.micronaut.http.client.exceptions.ReadTimeoutException
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.spyk
import io.mockk.verify
import net.javacrumbs.shedlock.core.SimpleLock
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import software.amazon.awssdk.services.sqs.model.Message

class SQSBankAccountDepositHandlerTest {

    private val walletFixture = WalletFixture()
    private val wallet =
        walletFixture.buildWallet(otherMembers = listOf(walletFixture.participant, walletFixture.cantPayParticipant))
    private val walletService: WalletService = mockk {
        every {
            findWallet(wallet.id)
        } returns wallet
        every {
            findWallet(wallet.founder.accountId, wallet.paymentMethodId)
        } returns wallet
    }

    private val walletRepository: WalletRepository = mockk {
        every {
            findWallet(wallet.id)
        } returns wallet
    }

    private val arbiBalance =
        ai.friday.billpayment.arbiBalance.copy(accountId = wallet.founder.accountId, id = wallet.paymentMethodId)
    private val invoiceAdded = ai.friday.billpayment.invoiceAdded.copy(walletId = wallet.id)
    private val invoiceBill = Bill.build(invoiceAdded)
    private val scheduledBill =
        ai.friday.billpayment.scheduledBill.copy(
            walletId = wallet.id,
            paymentMethodsDetail = createPaymentMethodsDetailWithBalance(
                wallet.paymentMethodId,
                ai.friday.billpayment.scheduledBill.amount,
            ),
        )

    private val notificationAdapter: NotificationAdapter = mockk(relaxUnitFun = true)
    private val scheduledBillRepository: ScheduledBillRepository = mockk {
        every { findScheduledBillsByWalletIdAndUntilScheduledDate(any(), any()) } returns listOf()
    }
    private val balanceService: BalanceService = mockk()
    private val cashInInstrumentationService: CashInInstrumentationService = mockk(relaxUnitFun = true)
    private val systemActivityService: SystemActivityService = mockk(relaxUnitFun = true)

    private val walletLimitsService =
        WalletLimitsService(
            walletRepository = walletRepository,
            billRepository = mockk(),
            paymentSchedulingService = mockk(),
            accountService = mockk(),
            internalLock = mockk(),
        )

    private val scheduledBillPaymentService = DefaultScheduledBillPaymentService(
        billPaymentService = mockk(relaxed = true),
        balanceService = balanceService,
        paymentSchedulingService = mockk(relaxed = true),
        updateBillService = mockk(relaxed = true),
        walletService = walletService,
        walletLimitsService = walletLimitsService,
        lockProvider = mockk(relaxed = true),
        walletLockProvider = mockk(relaxed = true),
        scheduledBillPaymentServicePreProcessor = mockk(),
    )

    private val tedService: TEDService = mockk()
    private val accountRepository: AccountRepository = mockk()
    private val transactionRepository: TransactionRepository = mockk()
    private val undoTransaction: UndoTransaction = mockk()
    private val internalLock: InternalLock = mockk()
    private val internalBankRepository: InternalBankRepository = mockk() {
        every { update(any<InternalBankStatementItem>()) } just runs
        every { findBankStatementItem(any(), any(), any()) } returns bankStatementItem.right()
    }

    private val onePixPayService: OnePixPayService = mockk(relaxUnitFun = true)
    private val onePixPayInstrumentation = mockk<OnePixPayInstrumentation>(relaxed = true)

    private val notifyDepositService = NotifyDepositService(
        scheduledBillRepository = scheduledBillRepository,
        balanceService = balanceService,
        notificationAdapter = notificationAdapter,
        walletService = walletService,
    )

    private val bankAccountDepositHandler = SQSBankAccountDepositHandler(
        scheduledBillPaymentService = spyk(scheduledBillPaymentService) {
            every { process(any()) } just runs
        },
        cashInInstrumentationService = cashInInstrumentationService,
        accountRepository = accountRepository,
        walletService = walletService,
        tedService = tedService,
        transactionRepository = transactionRepository,
        undoTransaction = undoTransaction,
        internalLock = internalLock,
        systemActivityService = systemActivityService,
        onePixPayService = onePixPayService,
        onePixPayInstrumentation = onePixPayInstrumentation,
        internalBankRepository = internalBankRepository,
        notifyDepositService = notifyDepositService,
    )

    @Nested
    @DisplayName("given a deposit message")
    inner class DepositMessage {

        private val deposit = BankOperationExecuted(
            accountId = wallet.founder.accountId,
            accountPaymentMethodId = wallet.paymentMethodId,
            date = getLocalDate().format(dateFormat),
            created = 0,
            flow = BankStatementItemFlow.CREDIT,
            type = BankStatementItemType.PIX,
            description = "",
            operationNumber = "000001",
            amount = 1000,
            counterpartName = wallet.founder.name,
            counterpartDocument = wallet.founder.document,
            counterpartAccountNo = "000-1",
            counterpartBankName = "BankName",
            documentNumber = "0000200",
        )

        private val depositFromOthers = BankOperationExecuted(
            accountId = wallet.founder.accountId,
            accountPaymentMethodId = wallet.paymentMethodId,
            date = getLocalDate().format(dateFormat),
            created = 0,
            flow = BankStatementItemFlow.CREDIT,
            type = BankStatementItemType.PIX,
            description = "",
            operationNumber = "000001",
            amount = 1000,
            counterpartName = "Fulano",
            counterpartDocument = "**********-1",
            counterpartAccountNo = "000-1",
            counterpartBankName = "BankName",
            documentNumber = "0000200",
        )

        @BeforeEach
        fun setup() {
            every {
                accountRepository.findAccountPaymentMethodByIdAndAccountId(
                    accountPaymentMethodId = deposit.accountPaymentMethodId,
                    accountId = deposit.accountId,
                )
            } returns arbiBalance
            every {
                accountRepository.findById(any())
            } returns ACCOUNT
        }

        @Test
        fun `deve chamar o processamento do one pix pay`() {
            val message = Message.builder().body(getObjectMapper().writeValueAsString(deposit)).build()

            bankAccountDepositHandler.handleMessage(message)

            verify(exactly = 1) {
                onePixPayService.process(any(), any())
            }
        }

        @Test
        fun `quando falhar o processamento do one-pix-pay por conta da consulta do pix, não deve apagar a mensagem da fila`() {
            val message = Message.builder().body(getObjectMapper().writeValueAsString(deposit)).build()

            val error = ReadTimeoutException.TIMEOUT_EXCEPTION
            every { onePixPayService.process(any(), any()) } throws error

            assertThrows<Exception> { bankAccountDepositHandler.handleMessage(message) }

            verify(exactly = 1) {
                internalBankRepository.update(any<InternalBankStatementItem>())
            }

            val response = bankAccountDepositHandler.handleException(error)

            response shouldBe MessageHandlerResponse.keep()
        }

        @Test
        fun `ao retentar processar um cash-in, não deve notificar novamente o usuário`() {
            val message = Message.builder().body(getObjectMapper().writeValueAsString(deposit)).build()

            every { internalBankRepository.findBankStatementItem(any(), any(), any()) } returns bankStatementItem.copy(
                notificatedAt = getZonedDateTime(),
            ).right()

            val response = bankAccountDepositHandler.handleMessage(message)

            response.shouldDeleteMessage shouldBe true

            verify(exactly = 0) {
                notificationAdapter.notifyCashIn(
                    members = any(),
                    walletId = any(),
                    walletName = any(),
                    senderName = any(),
                    senderDocument = any(),
                    amount = any(),
                )
                internalBankRepository.update(any<InternalBankStatementItem>())
            }
            verify(exactly = 1) {
                onePixPayService.process(any(), any())
            }
        }

        @Test
        fun `should notify cash in for members with approval permission or view balance when wallet receives a deposit`() {
            val message = Message.builder().body(getObjectMapper().writeValueAsString(deposit)).build()

            val response = bankAccountDepositHandler.handleMessage(message)
            response.shouldDeleteMessage shouldBe true

            verify(exactly = 1) {
                notificationAdapter.notifyCashIn(
                    members = listOf(wallet.founder, walletFixture.participant, walletFixture.cantPayParticipant),
                    walletId = wallet.id,
                    walletName = wallet.name,
                    senderName = wallet.founder.name,
                    senderDocument = wallet.founder.document,
                    amount = deposit.amount,
                )

                onePixPayService.process(any(), any())
                systemActivityService.setHasCashedIn(wallet.id)
                cashInInstrumentationService.depositReceived(deposit)
            }
        }

        @Test
        fun `should notify cash in, scheduled bills for today and sufficient balance`() {
            every {
                scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(
                    any(),
                    any(),
                )
            } returns listOf(
                scheduledBill.copy(scheduledDate = getLocalDate()),
            )
            every {
                balanceService.getBalanceFrom(
                    wallet.founder.accountId,
                    wallet.paymentMethodId,
                )
            } returns Balance(amount = scheduledBill.amount)
            val message = Message.builder().body(getObjectMapper().writeValueAsString(deposit)).build()

            val response = bankAccountDepositHandler.handleMessage(message)
            response.shouldDeleteMessage shouldBe true

            verify(exactly = 1) {
                notificationAdapter.notifyFounderSelfCashInPaymentSufficientBalanceToday(
                    members = listOf(wallet.founder),
                    walletId = wallet.id,
                    walletName = wallet.name,
                    senderName = deposit.counterpartName,
                    senderDocument = deposit.counterpartDocument,
                    amount = deposit.amount,
                    senderAccountNo = deposit.counterpartAccountNo,
                    senderBankName = deposit.counterpartBankName,
                )
                notificationAdapter.notifyCashInPaymentSufficientBalanceToday(
                    members = listOf(walletFixture.participant, walletFixture.cantPayParticipant),
                    walletId = wallet.id,
                    walletName = wallet.name,
                    senderName = deposit.counterpartName,
                    senderDocument = deposit.counterpartDocument,
                    amount = deposit.amount,
                )

                systemActivityService.setHasCashedIn(wallet.id)
                cashInInstrumentationService.depositReceived(deposit)
            }
        }

        @Test
        fun `should notify TED cash in, scheduled bills for today and sufficient balance`() {
            val depositData = deposit.copy(type = BankStatementItemType.TED_DIF_TITULARIDADE)

            every {
                scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(
                    any(),
                    any(),
                )
            } returns listOf(
                scheduledBill.copy(scheduledDate = getLocalDate()),
            )
            every {
                balanceService.getBalanceFrom(
                    wallet.founder.accountId,
                    wallet.paymentMethodId,
                )
            } returns Balance(amount = scheduledBill.amount)
            val message = Message.builder().body(getObjectMapper().writeValueAsString(depositData)).build()

            val response = bankAccountDepositHandler.handleMessage(message)
            response.shouldDeleteMessage shouldBe true

            verify(exactly = 1) {
                notificationAdapter.notifyCashInPaymentSufficientBalanceToday(
                    members = listOf(wallet.founder, walletFixture.participant, walletFixture.cantPayParticipant),
                    walletId = wallet.id,
                    walletName = wallet.name,
                    senderName = wallet.founder.name,
                    senderDocument = wallet.founder.document,
                    amount = depositData.amount,
                )

                systemActivityService.setHasCashedIn(wallet.id)
                cashInInstrumentationService.depositReceived(depositData)
            }
        }

        @Test
        fun `should notify TED cash in with same ownership, scheduled bills for today and sufficient balance`() {
            val depositData = deposit.copy(
                documentNumber = wallet.founder.document,
                type = BankStatementItemType.TED_MESMA_TITULARIDADE,
            )

            every {
                scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(
                    any(),
                    any(),
                )
            } returns listOf(
                scheduledBill.copy(scheduledDate = getLocalDate()),
            )
            every {
                balanceService.getBalanceFrom(
                    wallet.founder.accountId,
                    wallet.paymentMethodId,
                )
            } returns Balance(amount = scheduledBill.amount)

            val message = Message.builder().body(getObjectMapper().writeValueAsString(depositData)).build()

            val response = bankAccountDepositHandler.handleMessage(message)
            response.shouldDeleteMessage shouldBe true

            verify(exactly = 1) {
                notificationAdapter.notifyCashInPaymentSufficientBalanceToday(
                    members = listOf(wallet.founder, walletFixture.participant, walletFixture.cantPayParticipant),
                    walletId = wallet.id,
                    walletName = wallet.name,
                    senderName = wallet.founder.name,
                    senderDocument = wallet.founder.document,
                    amount = depositData.amount,
                )

                systemActivityService.setHasCashedIn(wallet.id)
                cashInInstrumentationService.depositReceived(depositData)
            }
        }

        @Test
        fun `should notify cash in, scheduled bills for today and insufficient balance`() {
            val scheduledAmount = 5000L
            every {
                scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(
                    any(),
                    any(),
                )
            } returns listOf(
                scheduledBill.copy(scheduledDate = getLocalDate(), amount = scheduledAmount),
            )
            every {
                balanceService.getBalanceFrom(
                    wallet.founder.accountId,
                    wallet.paymentMethodId,
                )
            } returns Balance(amount = deposit.amount)
            val message = Message.builder().body(getObjectMapper().writeValueAsString(deposit)).build()

            val response = bankAccountDepositHandler.handleMessage(message)
            response.shouldDeleteMessage shouldBe true

            verify(exactly = 1) {
                notificationAdapter.notifyCashInPaymentInsufficientBalanceToday(
                    members = listOf(wallet.founder, walletFixture.participant, walletFixture.cantPayParticipant),
                    walletId = wallet.id,
                    walletName = wallet.name,
                    pendingAmountToday = 4000L,
                    senderName = wallet.founder.name,
                    senderDocument = wallet.founder.document,
                    amount = deposit.amount,
                )

                systemActivityService.setHasCashedIn(wallet.id)
                cashInInstrumentationService.depositReceived(deposit)
            }
        }
    }

    @Nested
    @DisplayName("given a TED undone message")
    inner class TEDUndoneMessage {

        private val tedUndone = BankOperationExecuted(
            accountId = wallet.founder.accountId,
            accountPaymentMethodId = wallet.paymentMethodId,
            date = getLocalDate().format(dateFormat),
            created = 0,
            flow = BankStatementItemFlow.CREDIT,
            type = BankStatementItemType.DEVOLUCAO_TED,
            description = "",
            operationNumber = "000001",
            amount = 1000,
            counterpartName = wallet.founder.name,
            counterpartDocument = wallet.founder.document,
            documentNumber = "0000200",
        )
        private val tedUndoneMessage = Message.builder().body(getObjectMapper().writeValueAsString(tedUndone)).build()

        private val invoiceTransaction = Transaction(
            type = TransactionType.INVOICE_PAYMENT,
            payer = walletFixture.founderAccount.toPayer(),
            paymentData = createSinglePaymentDataWithBalance(arbiBalance, invoiceBill.amountTotal),
            settlementData = SettlementData(invoiceBill, 0, invoiceBill.amountTotal),
            nsu = 1,
            actionSource = ActionSource.System,
            walletId = wallet.id,
        )

        @BeforeEach
        fun setup() {
            every {
                accountRepository.findAccountPaymentMethodByIdAndAccountId(
                    accountPaymentMethodId = tedUndone.accountPaymentMethodId,
                    accountId = tedUndone.accountId,
                )
            } returns arbiBalance

            every {
                undoTransaction.undoDirectInvoice(
                    transaction = any(),
                    status = any(),
                )
            } just Runs
        }

        @Test
        fun `should not delete message when operation is not found`() {
            every {
                tedService.checkTEDUndone(
                    tedUndone.amount,
                    any(),
                    any(),
                    (arbiBalance.method as InternalBankAccount).buildFullAccountNumber(),
                )
            } returns listOf()

            val response = bankAccountDepositHandler.handleMessage(tedUndoneMessage)
            response.shouldDeleteMessage shouldBe false
        }

        @Nested
        @DisplayName("and one operation is found")
        inner class OneOperationFound {
            private val result = DirectTEDUndoneResult(
                amount = tedUndone.amount,
                status = DirectTEDStatus.Error,
                operationId = BankOperationId(value = "BANK-OPERATION-1111"),
            )

            @BeforeEach
            fun setup() {
                every {
                    tedService.checkTEDUndone(
                        tedUndone.amount,
                        any(),
                        any(),
                        (arbiBalance.method as InternalBankAccount).buildFullAccountNumber(),
                    )
                } returns listOf(result)
                val simpleLock: SimpleLock = mockk(relaxed = true)
                every {
                    internalLock.acquireLock(result.operationId.value)
                } returns simpleLock
            }

            @Test
            fun `should not delete message when transaction is not found`() {
                every {
                    transactionRepository.findByWalletAndStatusAndBankOperationId(
                        walletId = wallet.id,
                        status = any(),
                        bankOperationId = result.operationId,
                    )
                } throws ItemNotFoundException("operation not found")

                val response = bankAccountDepositHandler.handleMessage(tedUndoneMessage)
                response.shouldDeleteMessage shouldBe false
            }

            @Test
            fun `should delete message when transaction is already undone`() {
                every {
                    transactionRepository.findByWalletAndStatusAndBankOperationId(
                        walletId = wallet.id,
                        status = TransactionStatus.COMPLETED,
                        bankOperationId = result.operationId,
                    )
                } throws ItemNotFoundException("operation not found")

                every {
                    transactionRepository.findByWalletAndStatusAndBankOperationId(
                        walletId = wallet.id,
                        status = TransactionStatus.UNDONE,
                        bankOperationId = result.operationId,
                    )
                } returns invoiceTransaction

                val response = bankAccountDepositHandler.handleMessage(tedUndoneMessage)
                response.shouldDeleteMessage shouldBe true

                verify(exactly = 0) {
                    undoTransaction.undoDirectInvoice(
                        transaction = any(),
                        status = any(),
                    )
                }
            }

            @Test
            fun `should undo payment when one transaction is found`() {
                every {
                    transactionRepository.findByWalletAndStatusAndBankOperationId(
                        walletId = wallet.id,
                        status = TransactionStatus.COMPLETED,
                        bankOperationId = result.operationId,
                    )
                } returns invoiceTransaction

                val response = bankAccountDepositHandler.handleMessage(tedUndoneMessage)
                response.shouldDeleteMessage shouldBe true

                verify {
                    undoTransaction.undoDirectInvoice(
                        transaction = any(),
                        status = result.status,
                    )
                }
            }
        }

        @Nested
        @DisplayName("and two operations are found")
        inner class TwoOperationsFound {
            private val result1 = DirectTEDUndoneResult(
                amount = tedUndone.amount,
                status = DirectTEDStatus.Error,
                operationId = BankOperationId(value = "BANK-OPERATION-1111"),
            )
            private val result2 = DirectTEDUndoneResult(
                amount = tedUndone.amount,
                status = DirectTEDStatus.Failure.INVALID_BANK_DATA,
                operationId = BankOperationId(value = "BANK-OPERATION-2222"),
            )

            @BeforeEach
            fun setup() {
                every {
                    tedService.checkTEDUndone(
                        tedUndone.amount,
                        any(),
                        any(),
                        (arbiBalance.method as InternalBankAccount).buildFullAccountNumber(),
                    )
                } returns listOf(result1, result2)
            }

            @Test
            fun `should undo payment when first transaction is completed`() {
                val simpleLock: SimpleLock = mockk(relaxed = true)
                every {
                    internalLock.acquireLock(result1.operationId.value)
                } returns simpleLock

                every {
                    transactionRepository.findByWalletAndStatusAndBankOperationId(
                        walletId = wallet.id,
                        status = TransactionStatus.COMPLETED,
                        bankOperationId = result1.operationId,
                    )
                } returns invoiceTransaction

                val response = bankAccountDepositHandler.handleMessage(tedUndoneMessage)
                response.shouldDeleteMessage shouldBe true

                verify {
                    undoTransaction.undoDirectInvoice(
                        transaction = any(),
                        status = result1.status,
                    )
                    transactionRepository.findByWalletAndStatusAndBankOperationId(
                        walletId = wallet.id,
                        status = TransactionStatus.COMPLETED,
                        bankOperationId = result1.operationId,
                    )
                    simpleLock.unlock()
                }
            }

            @Test
            fun `should undo payment when first transaction is undone and second is completed`() {
                val simpleLock: SimpleLock = mockk(relaxed = true)
                every {
                    internalLock.acquireLock(any())
                } returns simpleLock

                every {
                    transactionRepository.findByWalletAndStatusAndBankOperationId(
                        walletId = wallet.id,
                        status = TransactionStatus.COMPLETED,
                        bankOperationId = result1.operationId,
                    )
                } throws ItemNotFoundException("transaction not found")

                every {
                    transactionRepository.findByWalletAndStatusAndBankOperationId(
                        walletId = wallet.id,
                        status = TransactionStatus.UNDONE,
                        bankOperationId = result1.operationId,
                    )
                } returns invoiceTransaction

                every {
                    transactionRepository.findByWalletAndStatusAndBankOperationId(
                        walletId = wallet.id,
                        status = TransactionStatus.COMPLETED,
                        bankOperationId = result2.operationId,
                    )
                } returns invoiceTransaction

                val response = bankAccountDepositHandler.handleMessage(tedUndoneMessage)
                response.shouldDeleteMessage shouldBe true

                verify {
                    undoTransaction.undoDirectInvoice(
                        transaction = any(),
                        status = result2.status,
                    )
                    transactionRepository.findByWalletAndStatusAndBankOperationId(
                        walletId = wallet.id,
                        status = TransactionStatus.COMPLETED,
                        bankOperationId = result2.operationId,
                    )
                }
            }

            @Test
            fun `should undo payment when first transaction is locked and second is completed`() {
                val simpleLock: SimpleLock = mockk(relaxed = true)
                every {
                    internalLock.acquireLock(result1.operationId.value)
                } returns null
                every {
                    internalLock.acquireLock(result2.operationId.value)
                } returns simpleLock

                every {
                    transactionRepository.findByWalletAndStatusAndBankOperationId(
                        walletId = wallet.id,
                        status = TransactionStatus.COMPLETED,
                        bankOperationId = result2.operationId,
                    )
                } returns invoiceTransaction

                val response = bankAccountDepositHandler.handleMessage(tedUndoneMessage)
                response.shouldDeleteMessage shouldBe true

                verify {
                    undoTransaction.undoDirectInvoice(
                        transaction = any(),
                        status = result2.status,
                    )
                    transactionRepository.findByWalletAndStatusAndBankOperationId(
                        walletId = wallet.id,
                        status = TransactionStatus.COMPLETED,
                        bankOperationId = result2.operationId,
                    )
                    simpleLock.unlock()
                }
            }

            @Test
            fun `should not delete message when all transaction are already locked`() {
                every {
                    internalLock.acquireLock(any())
                } returns null

                val response = bankAccountDepositHandler.handleMessage(tedUndoneMessage)
                response.shouldDeleteMessage shouldBe false
            }
        }
    }
}