package ai.friday.billpayment.adapters.whatsappflow

import ai.friday.billpayment.app.whatsapp.WhatsAppFlowConfiguration
import io.mockk.every
import io.mockk.mockk
import java.util.Base64
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
class WhatsAppFlowAdapterTest {

    private val whatsAppFlowAdapter = WhatsAppFlowCipherProcessor(
        configuration = mockk<WhatsAppFlowConfiguration> {
            every { salt } returns "friday-salt"
        },
    )

    @Test
    fun `deve decriptar a mensagem corretamente`() {
        val encryptedFlowData = Base64.getDecoder().decode("h66z6pwVYffqRIQrZc+x9Kmx31h5Q4I8eQP0cVNf7R9ClxMjMaqXGHMvVBOyZAvIIw==")
        val encryptedAesKey = Base64.getDecoder().decode("I2ZCE+7EjuOV10fEQvAhJXm8mJbAl3m+RzRK1yIoWB/2zrdhTP1vsHMrcbVBYkip3+/9ESOXJppGEkiw4asSCWQE2mZiM0MHy6DjP9zbJSyOOd672RFyP9dPQoyLQS2ZgIy/1PtgJjsAoLcFY/R3t7y+Rbbxe+uqwML0WJLYj44gpE4XKpvlgWYpJysJls+0TImy42fCPqmTy3X69UBAQ8DCSpsXjAe2qr+X8iBmPQYMyNll6gnPN90tT39FTw9xGXBResA1ZioYRrBjsdtInGYvwTP8iPFKduooeUfR+YBwc+zBl7HFg8cNbtMa1LNAhqSC1qGz2cFQTGcF2wSOGw==")
        val initialVector = Base64.getDecoder().decode("kQG54jEeLICFD67ZL0jMUQ==")

        val result = whatsAppFlowAdapter.decrypt(encryptedAesKey, encryptedFlowData, initialVector)

        println(result)
    }
}