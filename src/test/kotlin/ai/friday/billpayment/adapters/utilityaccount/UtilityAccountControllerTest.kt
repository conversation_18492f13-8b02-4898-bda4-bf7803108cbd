package ai.friday.billpayment.adapters.utilityaccount

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.adapters.dynamodb.UtilityAccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.UtilityAccountDynamoDAO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.KmsService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.UserEventService
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.utilityaccount.Utility
import ai.friday.billpayment.app.utilityaccount.UtilityAccount
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionDetails
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionStatus
import ai.friday.billpayment.app.utilityaccount.UtilityConnectionMethod
import ai.friday.billpayment.fixture.UtilityAccountFixture
import ai.friday.billpayment.integration.AccountFixture
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.toSignedCookie
import ai.friday.billpayment.integration.withCognitoGroups
import ai.friday.billpayment.integration.withCognitoUsername
import ai.friday.billpayment.integration.withCustomAccountId
import ai.friday.billpayment.integration.withDefaultSetup
import ai.friday.billpayment.integration.withIssuer
import ai.friday.billpayment.integration.withSubject
import com.nimbusds.jwt.JWTClaimsSet
import io.kotest.common.runBlocking
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.HttpClient
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.via1.communicationcentre.app.integrations.EmailSenderService
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource

@MicronautTest(environments = [FRIDAY_ENV])
internal class UtilityAccountControllerTest(embeddedServer: EmbeddedServer) {

    private lateinit var repository: UtilityAccountDbRepository

    @MockBean(MessagePublisher::class)
    fun publisherFunction() = publisher
    private val publisher = mockk<MessagePublisher>(relaxUnitFun = true)

    @MockBean(KmsService::class)
    fun kms() = kmsAdapter
    private val kmsAdapter = mockk<KmsService>(relaxed = true)

    @MockBean(UserEventService::class)
    fun userEventService() = userEventService
    private val userEventService = mockk<UserEventService>(relaxed = true)

    @MockBean(AccountRepository::class)
    fun accountRepository() = accountRepository
    private val accountRepository = mockk<AccountRepository> {
        every { findById(any()) } returns AccountFixture.createAccount()
    }

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @MockBean(ManualEntryService::class)
    fun manualEntryService() = manualEntryService
    private val manualEntryService: ManualEntryService = mockk(relaxUnitFun = true)

    @MockBean(WalletRepository::class)
    fun walletRepository() = walletRepository
    private val walletRepository = mockk<WalletRepository>(relaxed = true)

    @MockBean(CrmService::class)
    fun crmService() = crmService
    private val crmService = mockk<CrmService>(relaxed = true)

    @MockBean(NotificationAdapter::class)
    fun notificationAdapter() = notificationAdapter
    private val notificationAdapter = mockk<NotificationAdapter>(relaxed = true)

    @MockBean(EmailSenderService::class)
    fun emailSenderService() = emailSenderService
    private val emailSenderService = mockk<EmailSenderService>(relaxed = true)

    val client: HttpClient = embeddedServer.applicationContext.createBean(HttpClient::class.java, embeddedServer.url)

    private val connectUtilityRequest = UtilityAccountConnectionRequestTO(
        utility = Utility.CLARO_MOBILE.name,
        connectionDetails = mapOf(
            "loginType" to "document",
            "emailOrCpf" to "***********",
            "password" to "123",
        ),
    )

    private val walletFixture = WalletFixture()

    private val ownerCookie = JWTClaimsSet.Builder().withSubject("7fd44993-43dd-4f79-9e27-c2c379fe3767").withIssuer("bill-payment-service").withCognitoGroups().withCognitoUsername().withCustomAccountId(walletFixture.founder.accountId).withDefaultSetup().toSignedCookie()
    private val assistantCookie = JWTClaimsSet.Builder().withSubject("7fd44993-43dd-4f79-9e27-c2c379fe3767").withIssuer("bill-payment-service").withCognitoGroups().withCognitoUsername().withCustomAccountId(walletFixture.assistant.accountId).withDefaultSetup().toSignedCookie()

    private val wallet = walletFixture.buildPrimaryWallet(
        founderAccount = walletFixture.founderAccount,
        otherMembers = listOf(walletFixture.assistant),
    )

    private val utilityAccount: UtilityAccount = UtilityAccountFixture.create(walletId = wallet.id, accountId = wallet.founder.accountId)

    @BeforeEach
    fun beforeEach() {
        repository = UtilityAccountDbRepository(UtilityAccountDynamoDAO(setupDynamoDB()))

        every {
            walletRepository.findWalletOrNull(any())
        } returns wallet
    }

    private val headers: MutableMap<CharSequence, CharSequence> = mutableMapOf(
        "X-API-VERSION" to "2",
        "X-Wallet-Id" to wallet.id.value,
    )

    @Test
    fun `deve listar as contas de consumo da cateira que ele participe`() {
        val comgasAccount = utilityAccount.copy(
            utility = Utility.COMGAS,
            status = UtilityAccountConnectionStatus.CONNECTED,
            connectionMethod = UtilityConnectionMethod.SCRAPING,
        )

        repository.save(comgasAccount)
        val response =
            client.toBlocking().retrieve(
                HttpRequest.GET<List<UtilityAccountResponseTO>>("/utilityAccount/list")
                    .cookie(assistantCookie)
                    .headers(headers),
                Argument.listOf(UtilityAccountResponseTO::class.java),
            )

        val first = response.first()
        first.status shouldBe UtilityAccountConnectionStatus.CONNECTED
        first.utility.utility shouldBe Utility.COMGAS.name
        first.utility.category shouldBe "gas"
        first.utility.name shouldBe "Comgás"
    }

    @Test
    fun `deve adicionar uma conta de consumo de uma carteira que ele participe`() {
        val response = client.toBlocking().exchange(
            HttpRequest.POST("/utilityAccount/connect", connectUtilityRequest)
                .cookie(assistantCookie)
                .headers(headers),
            Argument.of(Unit::class.java),
        )
        response.status shouldBe HttpStatus.CREATED

        with(repository.findByIndex(wallet.id).first()) {
            status shouldBe UtilityAccountConnectionStatus.PENDING
            accountEmail shouldBe "<EMAIL>"
            walletId shouldBe wallet.id
            addedBy shouldBe walletFixture.assistant.accountId
        }

        verify(exactly = 1) {
            publisher.sendMessage(any())
        }
    }

    @Test
    fun `deve remover uma conta de consumo de uma carteira que ele participe`() {
        repository.save(utilityAccount.copy(status = UtilityAccountConnectionStatus.CONNECTED))

        val savedUtilityAccount = repository.findByIndex(wallet.id).first()

        savedUtilityAccount.status shouldBe UtilityAccountConnectionStatus.CONNECTED
        val uri = "/utilityAccount/${savedUtilityAccount.id.value}"

        val response = client.toBlocking().exchange(
            HttpRequest.DELETE<Unit>(uri)
                .cookie(assistantCookie)
                .headers(headers),
            Argument.of(Void::class.java),
        )

        val disconnectedAccount = repository.findByIndex(wallet.id).first()

        disconnectedAccount.status shouldBe UtilityAccountConnectionStatus.PENDING_DISCONNECTION

        response.status shouldBe HttpStatus.NO_CONTENT

        verify(exactly = 1) {
            publisher.sendMessage(any())
        }
    }

    @Test
    fun `deve retornar erro ao tentar criar uma conexão de conta com mesmo login e tipo de concessionária`() {
        repository.save(
            utilityAccount.copy(
                connectionDetails = UtilityAccountConnectionDetails.WithCpfAndPassword(cpf = "***********", password = "1234"),
                utility = Utility.OI_HOME,
            ),
        )

        val response = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(
                HttpRequest.POST(
                    "/utilityAccount/connect",
                    connectUtilityRequest.copy(
                        connectionDetails = connectUtilityRequest.connectionDetails + mapOf(
                            "cpf" to "***********",
                            "password" to "1234",
                        ),
                        utility = Utility.OI_HOME.name,
                    ),
                )
                    .cookie(ownerCookie)
                    .headers(headers),
                Argument.of(ConnectUtilityResponseTO::class.java),
            )
        }

        verify {
            kmsAdapter wasNot called
        }

        response.status shouldBe HttpStatus.BAD_REQUEST
        response.response.getBody(ErrorResponseTO::class.java)
            .get().code shouldBe "4002"
    }

    @Test
    fun `deve permitir criar uma conexão de conta com mesmo login e tipo de concessionária se a conta estiver desconectada`() {
        repository.save(utilityAccount.copy(status = UtilityAccountConnectionStatus.DISCONNECTED))

        val response = client.toBlocking().exchange(
            HttpRequest.POST("/utilityAccount/connect", connectUtilityRequest)
                .cookie(ownerCookie)
                .headers(headers),
            Argument.of(ConnectUtilityResponseTO::class.java),
        )

        response.status shouldBe HttpStatus.CREATED

        verify {
            runBlocking { kmsAdapter.encryptData(any()) }
        }

        verify(exactly = 1) {
            publisher.sendMessage(any())
        }
    }

    @Test
    fun `deve retornar erro ao tentar conectar a uma concessionária que não existe`() {
        val response = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(
                HttpRequest.POST("/utilityAccount/connect", connectUtilityRequest.copy(utility = "NAO_EXISTE"))
                    .cookie(ownerCookie)
                    .headers(headers),
                Argument.of(ConnectUtilityResponseTO::class.java),
                Argument.of(ErrorResponseTO::class.java),
            )
        }

        verify {
            kmsAdapter wasNot called
        }

        response.status shouldBe HttpStatus.BAD_REQUEST
        response.response.getBody(ErrorResponseTO::class.java)
            .get().code shouldBe "4000"
    }

    @Test
    fun `deve desconectar a conta de um usuário com status CONNECTED`() {
        repository.save(utilityAccount.copy(status = UtilityAccountConnectionStatus.CONNECTED))

        val savedUtilityAccount = repository.findByIndex(utilityAccount.walletId).first()

        savedUtilityAccount.status shouldBe UtilityAccountConnectionStatus.CONNECTED
        val uri = "/utilityAccount/${savedUtilityAccount.id.value}"

        val response =
            client.toBlocking().exchange(
                HttpRequest.DELETE<Unit>(uri)
                    .cookie(ownerCookie)
                    .headers(headers),
                Argument.of(Void::class.java),
            )

        val disconnectedAccount = repository.findByIndex(utilityAccount.walletId).first()

        disconnectedAccount.status shouldBe UtilityAccountConnectionStatus.PENDING_DISCONNECTION

        response.status shouldBe HttpStatus.NO_CONTENT

        verify(exactly = 1) {
            publisher.sendMessage(any())
        }
    }

    @ParameterizedTest
    @EnumSource(
        value = UtilityAccountConnectionStatus::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["CONNECTION_ERROR", "DISCONNECTION_ERROR", "INVALID_CREDENTIALS"],
    )
    fun `ao desconectar uma conta não deve inserir mensagem na fila se o status for`(status: UtilityAccountConnectionStatus) {
        repository.save(utilityAccount.copy(status = status))

        val savedUtilityAccount = repository.findByIndex(utilityAccount.walletId).first()

        savedUtilityAccount.status shouldBe status
        val uri = "/utilityAccount/${savedUtilityAccount.id.value}"

        val response =
            client.toBlocking().exchange(
                HttpRequest.DELETE<Unit>(uri)
                    .cookie(ownerCookie)
                    .headers(headers),
                Argument.of(Void::class.java),
            )

        val disconnectedAccount = repository.findByIndex(utilityAccount.walletId).first()

        disconnectedAccount.status shouldBe UtilityAccountConnectionStatus.DISCONNECTED

        response.status shouldBe HttpStatus.NO_CONTENT

        verify(exactly = 0) {
            publisher.sendMessage(any())
        }
    }

    @ParameterizedTest
    @EnumSource(
        value = UtilityAccountConnectionStatus::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["PENDING", "PENDING_DISCONNECTION", "DISCONNECTED"],
    )
    fun `deve retornar erro ao tentar desconectar contas com status `(status: UtilityAccountConnectionStatus) {
        repository.save(utilityAccount.copy(status = status))

        val savedUtilityAccount = repository.findByIndex(utilityAccount.walletId).first()

        savedUtilityAccount.status shouldBe status
        val uri = "/utilityAccount/${savedUtilityAccount.id.value}"

        val response =
            assertThrows<HttpClientResponseException> {
                client.toBlocking().exchange(
                    HttpRequest.DELETE<Unit>(uri)
                        .cookie(ownerCookie)
                        .headers(headers),
                    Argument.of(Void::class.java),
                )
            }

        response.response.getBody(ErrorResponseTO::class.java)
            .get().code shouldBe ConnectUtilityErrorResponse.CANNOT_DISCONNECT_ACCOUNT.name

        verify(exactly = 0) {
            publisher.sendMessage(any())
        }
    }

    @ParameterizedTest
    @MethodSource("flowRequests")
    fun `deve criar conexão quando for uma concessionaria de método de conexão FLOW`(requestTO: UtilityAccountConnectionRequestTO) {
        val response =
            client.toBlocking().exchange(
                HttpRequest.POST("/utilityAccount/connect", requestTO)
                    .cookie(ownerCookie)
                    .headers(headers),
                Argument.of(ConnectUtilityResponseTO::class.java),
            )

        val found = repository.findAll(utilityAccount.walletId, UtilityAccountConnectionStatus.PENDING)

        found.shouldNotBeEmpty()

        response.status shouldBe HttpStatus.CREATED

        verify(exactly = 1) {
            publisher.sendMessage(any())
        }
    }

    @Test
    fun `deve desconectar diretamente uma conexão COMGÁS com método scraping`() {
        val comgasAccount = utilityAccount.copy(
            utility = Utility.COMGAS,
            status = UtilityAccountConnectionStatus.CONNECTED,
            connectionMethod = UtilityConnectionMethod.SCRAPING,
        )

        repository.save(comgasAccount)

        val response =
            client.toBlocking().exchange(
                HttpRequest.DELETE("/utilityAccount/${comgasAccount.id.value}", null)
                    .cookie(ownerCookie)
                    .headers(headers),
                Argument.of(Unit::class.java),
            )

        response.status shouldBe HttpStatus.NO_CONTENT
        verify(exactly = 0) {
            publisher.sendMessage(any())
        }
        verify(exactly = 1) {
            notificationAdapter.notifyUtilityAccountUpdatedStatus(any())
        }
        repository.findById(comgasAccount.id, comgasAccount.walletId)?.status shouldBe UtilityAccountConnectionStatus.DISCONNECTED
    }

    @Test
    fun `deve retornar as contas conectadas com as informações necessárias da utility`() {
        val comgasAccount = utilityAccount.copy(
            utility = Utility.COMGAS,
            status = UtilityAccountConnectionStatus.CONNECTED,
            connectionMethod = UtilityConnectionMethod.SCRAPING,
        )
        repository.save(comgasAccount)

        val response =
            client.toBlocking().retrieve(
                HttpRequest.GET<List<UtilityAccountResponseTO>>("/utilityAccount/list")
                    .cookie(ownerCookie)
                    .headers(headers),
                Argument.listOf(UtilityAccountResponseTO::class.java),
            )

        with(response.first()) {
            status shouldBe UtilityAccountConnectionStatus.CONNECTED
            utility.utility shouldBe Utility.COMGAS.name
            utility.category shouldBe "gas"
            utility.name shouldBe "Comgás"
        }
    }

    @Test
    fun `deve encontrar uma conta de consumo`() {
        val savedUtilityAccount = utilityAccount.copy(
            utility = Utility.LIGHT_RJ,
            status = UtilityAccountConnectionStatus.CONNECTED,
            connectionMethod = UtilityConnectionMethod.FLOW,
            connectionDetails = UtilityAccountConnectionDetails.WithCpfInstallationNumberAndPassword(
                cpf = "***********",
                password = "encryptedPassword", // Não deve ser retornado na resposta final,
                installationNumber = "*********",
            ),
        )

        repository.save(savedUtilityAccount)
        val response =
            client.toBlocking().retrieve(
                HttpRequest.GET<FindUtiltiyAccountResponseTO>("/utilityAccount/${savedUtilityAccount.id.value}")
                    .cookie(ownerCookie)
                    .headers(headers),
                FindUtiltiyAccountResponseTO::class.java,
            )

        response.id shouldBe savedUtilityAccount.id.value
        response.status shouldBe UtilityAccountConnectionStatus.CONNECTED
        response.utility shouldBe Utility.LIGHT_RJ
        response.connectionDetails shouldBe mapOf(
            "cpf" to "***********",
            "installationNumber" to "*********",
        )
    }

    @Test
    fun `não deve encontrar uma conta de consumo que não existe`() {
        val exception = assertThrows<HttpClientResponseException> {
            val response: HttpResponse<Unit> =
                client.toBlocking().exchange(
                    HttpRequest.GET<Unit>("/utilityAccount/UA-THAT-DOESNT-EXIST")
                        .cookie(ownerCookie)
                        .headers(headers),
                )
        }

        exception.status shouldBe HttpStatus.NOT_FOUND
    }

    companion object {
        @JvmStatic
        fun flowRequests(): Stream<Arguments> {
            val requests = listOf(
                UtilityAccountConnectionRequestTO(
                    utility = Utility.NATURGY.name,
                    connectionDetails = mapOf("cpf" to "***********", "password" to "********", "clientNumber" to "********"),
                ),
                UtilityAccountConnectionRequestTO(
                    utility = Utility.ENEL_SP.name,
                    connectionDetails = mapOf("email" to "<EMAIL>", "password" to "********", "installationNumber" to "134124"),
                ),
            )
            return requests.stream().map { Arguments.of(it) }
        }
    }
}