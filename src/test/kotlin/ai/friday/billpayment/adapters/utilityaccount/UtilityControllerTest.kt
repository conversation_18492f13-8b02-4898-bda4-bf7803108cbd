package ai.friday.billpayment.adapters.utilityaccount

import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.integrations.UserEventService
import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.utilityaccount.Utility
import ai.friday.billpayment.app.utilityaccount.UtilityAccount
import ai.friday.billpayment.app.utilityaccount.UtilityAccountFormData
import ai.friday.billpayment.app.utilityaccount.cpfField
import ai.friday.billpayment.app.utilityaccount.emailField
import ai.friday.billpayment.app.utilityaccount.installationNumber
import ai.friday.billpayment.app.utilityaccount.passwordField
import ai.friday.billpayment.app.utilityaccount.phoneNumberField
import ai.friday.billpayment.integration.SecurityFixture
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.client.HttpClient
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.mockk
import org.junit.jupiter.api.Test

@MicronautTest(environments = [FRIDAY_ENV])
internal class UtilityControllerTest(embeddedServer: EmbeddedServer) {

    private val client: HttpClient =
        embeddedServer.applicationContext.createBean(HttpClient::class.java, embeddedServer.url)

    private val securityFixture = SecurityFixture()

    private val cookie = securityFixture.cookieAuthOwner

    private val validUtilities = Utility.entries.filter { it != Utility.UNKNOWN && it.enabled }

    @MockBean(ManualEntryService::class)
    fun manualEntryService() = manualEntryService
    private val manualEntryService: ManualEntryService = mockk(relaxUnitFun = true)

    @MockBean(UserEventService::class)
    fun userEventService() = userEventService
    private val userEventService = mockk<UserEventService>(relaxed = true)

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @Test // TODO - aparentemente ninguem está chamando esse cara
    fun `deve listar todas as utilities disponíveis com indicação de manual`() {
        val response = client.toBlocking().retrieve(
            HttpRequest.GET<List<UtilityTO>>("/utility/v2").cookie(cookie).header("X-API-VERSION", "2"),
            Argument.listOf(UtilityTO::class.java),
        )
        println(response)
        response.size shouldBe validUtilities.size
    }

    @Test
    fun `deve listar todas as utilities disponíveis para criação de conta`() {
        val response = client.toBlocking().retrieve(
            HttpRequest.GET<List<UtilityAccount>>("/utility/").cookie(cookie).header("X-API-VERSION", "2"),
            Argument.listOf(Utility::class.java),
        )

        response.size shouldBe validUtilities.size
    }

    @Test
    fun `deve listar todas as utilities disponíveis e seus respectivos formulários`() {
        val response = client.toBlocking().retrieve(
            HttpRequest.GET<List<UtilityAccountWithDynamicFormTO>>("/utility/list").cookie(cookie).header("X-API-VERSION", "2"),
            Argument.listOf(UtilityAccountWithDynamicFormTO::class.java),
        )

        response.size shouldBe validUtilities.size
        val claroHome = response.find { it.utility == Utility.CLARO_HOME.name }
        val claroMobile = response.find { it.utility == Utility.CLARO_MOBILE.name }
        val timMobile = response.find { it.utility == Utility.TIM_MOBILE.name }
        val oiHome = response.find { it.utility == Utility.OI_HOME.name }
        val lightRj = response.find { it.utility == Utility.LIGHT_RJ.name }
        val naturgy = response.find { it.utility == Utility.NATURGY.name }
        val edpES = response.find { it.utility == Utility.EDP_ES.name }
        with(claroHome!!) {
            name shouldBe "Claro Residencial"
            category shouldBe "internet-and-cable-tv"
            form shouldContainExactly listOf(
                UtilityAccountFormData(
                    type = "text",
                    name = "username",
                    placeholder = "CPF, e-mail ou nome de usuário",
                    label = "CPF, e-mail ou nome de usuário",
                    errorText = "Informe um CPF/email/nome de usuário válido",
                ),
                passwordField,
            )
        }
        with(claroMobile!!) {
            name shouldBe "Claro Móvel"
            category shouldBe "mobile"
            form shouldContainExactly listOf(
                UtilityAccountFormData(
                    type = "text",
                    name = "emailOrCpf",
                    placeholder = "CPF ou e-mail",
                    label = "CPF ou e-mail",
                    parsers = listOf("email", "cpf"),
                    errorText = "Informe um CPF ou e-mail válido",
                ),
                passwordField,
            )
        }
        with(timMobile!!) {
            name shouldBe "Tim Móvel"
            category shouldBe "mobile"
            form shouldContainExactly listOf(
                phoneNumberField,
                passwordField,
            )
        }
        with(oiHome!!) {
            name shouldBe "Oi Residencial"
            category shouldBe "internet-and-cable-tv"
            form shouldContainExactly listOf(
                cpfField,
                passwordField,
            )
        }
        with(lightRj!!) {
            name shouldBe "Light - RJ"
            category shouldBe "electricity"
            form shouldContainExactly listOf(
                cpfField,
                passwordField,
                UtilityAccountFormData(
                    type = "numeric",
                    name = "installationNumber",
                    placeholder = "000000",
                    label = "Código de instalação",
                    description = "Confira se o código é da instalação e __não do cliente__",
                    errorText = "Digite um código de instalação válido",
                ),
            )
        }
        /* FIXME: desabilitada
        with(naturgy!!) {
            name shouldBe "Naturgy"
            category shouldBe "gas"
            form shouldContainExactly listOf(
                cpfField,
                UtilityAccountFormData(
                    type = "numeric",
                    name = "clientNumber",
                    placeholder = "000000",
                    label = "Número do cliente",
                    errorText = "Digite um número do cliente válido",
                ),
            )
        }*/
        with(edpES!!) {
            name shouldBe "EDP - ES"
            category shouldBe "electricity"
            form shouldContainExactly listOf(
                emailField,
                passwordField,
                installationNumber,
            )
        }
    }
}