package ai.friday.billpayment.adapters.lock

import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.util.Optional
import net.javacrumbs.shedlock.core.LockConfiguration
import net.javacrumbs.shedlock.core.LockProvider
import org.junit.jupiter.api.Test

class InternalLockProviderTest {

    private val lockProvider: LockProvider = mockk(relaxed = true)
    private val internalLockProvider = InternalLockProvider(lockProvider, mockk(relaxed = true))

    @Test
    fun `should acquire lock immediately`() {
        every { lockProvider.lock(any()) } returns Optional.of(mockk())
        val lock = internalLockProvider.waitForAcquireLock("LOCK_NAME")
        lock shouldNotBe null
    }

    @Test
    fun `should acquire lock before timeout`() {
        every { lockProvider.lock(any()) } returns Optional.empty() andThen Optional.of(mockk())
        val lock = internalLockProvider.waitForAcquireLock("LOCK_NAME")
        lock shouldNotBe null
    }

    @Test
    fun `deve pegar o lock sem indice quando não tiver nenhum lock`() {
        every { lockProvider.lock(any()) } returns Optional.of(mockk())
        val lock = internalLockProvider.acquireLock(lockName = "LOCK_NAME", simultaneousLock = 3)

        verify(exactly = 1) {
            lockProvider.lock(
                withArg {
                    it.name shouldBe "LOCK_NAME"
                },
            )
        }
        lock.shouldNotBeNull()
    }

    @Test
    fun `deve pegar o lock com indice quando não já tiver um lock`() {
        every { lockProvider.lock(any()) } returns Optional.empty() andThen Optional.of(mockk())
        val lock = internalLockProvider.acquireLock(lockName = "LOCK_NAME", simultaneousLock = 3)

        val slot = mutableListOf<LockConfiguration>()
        verify(exactly = 2) {
            lockProvider.lock(capture(slot))
        }
        lock.shouldNotBeNull()
        slot.size shouldBe 2
        slot[0].name shouldBe "LOCK_NAME"
        slot[1].name shouldBe "LOCK_NAME#2"
    }

    @Test
    fun `não deve pegar o lock se já tiver um lock a quantidade de vezes do limite`() {
        every { lockProvider.lock(any()) } returns Optional.empty() andThen Optional.empty() andThen Optional.empty() andThen Optional.of(
            mockk(),
        )
        val lock = internalLockProvider.acquireLock(lockName = "LOCK_NAME", simultaneousLock = 3)

        val slot = mutableListOf<LockConfiguration>()
        verify(exactly = 3) {
            lockProvider.lock(capture(slot))
        }
        lock.shouldBeNull()
        slot.size shouldBe 3
        slot[0].name shouldBe "LOCK_NAME"
        slot[1].name shouldBe "LOCK_NAME#2"
        slot[2].name shouldBe "LOCK_NAME#3"
    }

    /*
    [x] pegar o lock dentro do tempo imediatamente
    [-] pegar o lock dentro do tempo esperando x ms
    [ ] nao pegar o lock fora do tempo

    */
}