package ai.friday.billpayment.adapters.lock

import DynamoDBUtils.getDynamoDbClient
import ai.friday.billpayment.adapters.lock.Setup.LOCK_NAME
import ai.friday.billpayment.adapters.lock.Setup.TABLE_NAME
import ai.friday.billpayment.adapters.lock.Setup.internalLock
import ai.friday.billpayment.adapters.lock.Setup.releaser
import ai.friday.billpayment.integration.LocalDbCreationRule.Companion.dynamoDB
import ai.friday.billpayment.integration.createLockTable
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds
import kotlin.time.toJavaDuration
import net.javacrumbs.shedlock.provider.dynamodb2.DynamoDBLockProvider
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class InternalLockReleaserTest {

    @BeforeEach
    fun before() {
        createLockTable(amazonDynamoDB = dynamoDB, lockTableName = TABLE_NAME)
    }

    @Test
    fun `should release lock appropriately`() {
        // must acquire
        internalLock.acquireLock(LOCK_NAME).shouldNotBeNull()

        // must not acquire
        internalLock.acquireLock(LOCK_NAME).shouldBeNull()

        releaser.release(LOCK_NAME).isSuccess.shouldBeTrue()

        // must acquire again
        internalLock.acquireLock(LOCK_NAME).shouldNotBeNull()
    }
}

private object Setup {
    const val TABLE_NAME = "ShedLockTest"
    const val LOCK_NAME = "LockTest"

    val configuration = InternalLockConfiguration(
        name = "LOCK",
        maxDuration = 1.minutes.toJavaDuration(),
        minDuration = 30.seconds.toJavaDuration(),
        prefix = "PREFIX_",
    )
    val dynamoDBLockProvider = DynamoDBLockProvider(getDynamoDbClient(), TABLE_NAME)
    val internalLock = InternalLockProvider(dynamoDBLockProvider, configuration)
    val releaser = InternalLockReleaser(getDynamoDbClient(), configuration, TABLE_NAME)
}