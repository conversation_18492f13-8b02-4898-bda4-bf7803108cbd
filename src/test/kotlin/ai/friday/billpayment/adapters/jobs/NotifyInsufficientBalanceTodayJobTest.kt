package ai.friday.billpayment.adapters.jobs

import DynamoDBUtils
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.ChatbotType
import ai.friday.billpayment.app.balance.Balance
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.DefaultBillService
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.ScheduledBillRepository
import ai.friday.billpayment.app.integrations.ScheduledWalletRepository
import ai.friday.billpayment.app.payment.sumAmount
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAdded2
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.fichaCompensacaoCreditCardAdded
import ai.friday.billpayment.integration.BILL_ID_7
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.scheduledBill
import ai.friday.billpayment.scheduledBill2
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.shouldBe
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import reactor.core.publisher.Flux

internal class NotifyInsufficientBalanceTodayJobTest {

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet(
        id = scheduledBill.walletId,
        otherMembers = listOf(
            walletFixture.participant,
            walletFixture.limitedParticipant,
            walletFixture.cantPayParticipant,
        ),
    )

    private val notificationAdapter: NotificationAdapter = mockk(relaxUnitFun = true)

    private val scheduledBillRepository: ScheduledBillRepository = mockk {
        every { findScheduledBillsByWalletIdAndUntilScheduledDate(wallet.id, any()) } returns listOf()
    }

    private val scheduledWalletRepository: ScheduledWalletRepository = mockk {
        every { findAllWalletsWithScheduledBills() } returns Flux.just(wallet.id)
    }

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val enhancedClient = DynamoDBUtils.setupDynamoDB()

    private val billRepository: BillRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(enhancedClient),
        refundedClient = RefundedBillDynamoDAO(enhancedClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(enhancedClient),
    )

    private val internalBankService: InternalBankService = mockk()

    private val balanceService: BalanceService = mockk()

    private val accountService: AccountService = mockk()

    private val walletService: WalletService = mockk() {
        every {
            findWallet(wallet.id)
        } returns wallet
    }

    private val job = NotifyInsufficientBalanceTodayService(
        notificationAdapter,
        scheduledBillRepository,
        scheduledWalletRepository,
        internalBankService,
        balanceService,
        walletService,
        billService = spyk(
            DefaultBillService(
                repository = billRepository,
                mailBoxService = mockk(),
                accountRepository = mockk(),
            ),
        ),
        accountService = accountService,
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `should not notify when user has no scheduled bill for today`() {
        job.execute(shouldNotifyIfOnlyHasSubscription = true)
        verify { notificationAdapter wasNot called }
    }

    @Test
    fun `should not notify when user has bills scheduled for today and has enough balance to pay`() {
        every {
            scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(
                wallet.id,
                any(),
            )
        } returns listOf(scheduledBill, scheduledBill2)
        every {
            balanceService.getBalanceFrom(
                wallet.founder.accountId,
                wallet.paymentMethodId,
            )
        } returns Balance(
            scheduledBill.amount + scheduledBill2.amount,
        )

        job.execute(shouldNotifyIfOnlyHasSubscription = true)

        verify { notificationAdapter wasNot called }
    }

    @Test
    fun `should notify when user has bills scheduled for today but has not enough balance on wallet to pay`() {
        val bill1 = Bill.build(
            billAdded,
            billPaymentScheduled.copy(
                scheduledDate = getLocalDate(),
            ),
        )
        val bill2 = Bill.build(
            billAdded2.copy(amountTotal = 34562L),
            billPaymentScheduled.copy(
                billId = billAdded2.billId,
                scheduledDate = getLocalDate(),
            ),
        )
        billRepository.save(bill1)
        billRepository.save(bill2)
        billRepository.save(
            Bill.build(
                fichaCompensacaoCreditCardAdded.copy(
                    effectiveDueDate = getLocalDate().plusDays(6),
                ),
            ),
        )
        billRepository.save(
            Bill.build(
                billAddedFicha.copy(
                    billId = BillId(BILL_ID_7),
                    effectiveDueDate = getLocalDate().plusDays(10),
                ),
            ),
        )

        every {
            walletService.findWallet(scheduledBill.walletId)
        } returns wallet

        every { accountService.findAccountById(any()) } returns walletFixture.founderAccount

        every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHATBOT_LEGACY

        val scheduledBills = listOf(
            scheduledBill.copy(
                scheduledDate = getLocalDate(),
                amount = bill1.amountTotal,
            ),
            scheduledBill2.copy(
                scheduledDate = getLocalDate(),
                amount = bill2.amountTotal,
            ),
        )
        val userCurrentBalance = scheduledBills.sumAmount() - 1
        every {
            scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(
                wallet.id,
                getLocalDate(),
            )
        } returns scheduledBills
        every {
            scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(
                wallet.id,
                getLocalDate().plusWeeks(1),
            )
        } returns scheduledBills
        every {
            balanceService.getBalanceFrom(
                wallet.founder.accountId,
                wallet.paymentMethodId,
            )
        } returns Balance(
            userCurrentBalance,
        )
        every { internalBankService.isAfterHour(any()) } returns false

        withGivenDateTime(getZonedDateTime().withHour(16)) {
            job.execute(shouldNotifyIfOnlyHasSubscription = true)
        }

        val pendingAmount = scheduledBills.sumAmount() - userCurrentBalance
        val activePendingAmount7Days =
            scheduledBills.sumAmount() + fichaCompensacaoCreditCardAdded.amountTotal - userCurrentBalance
        val activePendingAmount15Days =
            scheduledBills.sumAmount() + fichaCompensacaoCreditCardAdded.amountTotal + billAddedFicha.amountTotal - userCurrentBalance
        verify {
            notificationAdapter.notifyInsufficientBalanceToday(
                listOf(
                    walletFixture.founder,
                    walletFixture.participant,
                    walletFixture.limitedParticipant,
                ),
                pendingAmount,
                activePendingAmount7Days,
                activePendingAmount15Days,
                wallet.id,
                wallet.name,
                false,
            )
        }
    }

    @Test
    fun `não deve notificar quando as únicas contas forem de assinaturas vencidas`() {
        val scheduledDate = getLocalDate().minusDays(1)

        val bill1 = Bill.build(
            billAdded.copy(subscriptionFee = true, effectiveDueDate = scheduledDate),
            billPaymentScheduled.copy(
                scheduledDate = scheduledDate,
            ),
        )
        billRepository.save(bill1)

        every {
            walletService.findWallet(scheduledBill.walletId)
        } returns wallet

        val balance = billAdded.amountTotal - 1

        every {
            scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(
                wallet.id,
                getLocalDate(),
            )
        } returns listOf(scheduledBill.copy(expires = false, scheduledDate = scheduledDate))

        every {
            balanceService.getBalanceFrom(
                wallet.founder.accountId,
                wallet.paymentMethodId,
            )
        } returns Balance(
            balance,
        )
        every { internalBankService.isAfterHour(any()) } returns false

        job.execute(shouldNotifyIfOnlyHasSubscription = true)

        verify(exactly = 0) {
            notificationAdapter.notifyInsufficientBalanceToday(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        }
    }

    @Test
    fun `should notify when user has bills scheduled for today but has not enough balance on wallet to pay and is after hour`() {
        val bill1 = Bill.build(
            billAdded,
            billPaymentScheduled.copy(
                scheduledDate = getLocalDate(),
            ),
        )
        billRepository.save(bill1)

        every {
            walletService.findWallet(scheduledBill.walletId)
        } returns wallet

        every { accountService.findAccountById(any()) } returns walletFixture.founderAccount

        every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHATBOT_LEGACY

        val scheduledBillsToday = listOf(
            scheduledBill.copy(
                scheduledDate = getLocalDate(),
                amount = bill1.amountTotal,
            ),
        )
        val balance = scheduledBillsToday.sumAmount() - 1
        every {
            scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(
                wallet.id,
                getLocalDate(),
            )
        } returns scheduledBillsToday
        every {
            balanceService.getBalanceFrom(
                wallet.founder.accountId,
                wallet.paymentMethodId,
            )
        } returns Balance(
            balance,
        )
        every { internalBankService.isAfterHour(any()) } returns true

        job.execute(shouldNotifyIfOnlyHasSubscription = true)

        val pendingAmount = scheduledBillsToday.sumAmount() - balance
        verify {
            notificationAdapter.notifyInsufficientBalanceToday(
                listOf(
                    walletFixture.founder,
                    walletFixture.participant,
                    walletFixture.limitedParticipant,
                ),
                pendingAmount,
                pendingAmount,
                pendingAmount,
                wallet.id,
                wallet.name,
                true,
            )
        }
    }

    @Test
    fun `caso a única conta vencendo hoje seja uma assinatura, e o usuário não tenha o chatbot habilitado deve enviar notificação de assinatura vencendo`() {
        val scheduledDate = getLocalDate()

        val subscriptionOverDue = scheduledBill.copy(expires = false, scheduledDate = scheduledDate.minusMonths(1))
        val subscriptionDueToday = scheduledBill.copy(expires = false, scheduledDate = scheduledDate, amount = 990)

        every {
            walletService.findWallet(scheduledBill.walletId)
        } returns wallet

        every { accountService.findAccountById(any()) } returns walletFixture.founderAccount

        every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHATBOT_LEGACY

        val balance = billAdded.amountTotal - 1

        every {
            scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(
                wallet.id,
                getLocalDate(),
            )
        } returns listOf(subscriptionOverDue, subscriptionDueToday)

        every {
            balanceService.getBalanceFrom(
                wallet.founder.accountId,
                wallet.paymentMethodId,
            )
        } returns Balance(
            balance,
        )
        every { internalBankService.isAfterHour(any()) } returns false

        job.execute(shouldNotifyIfOnlyHasSubscription = true)

        val slot = slot<Long>()
        verify {
            notificationAdapter.notifySubscriptionInsufficientBalance(
                members = any(),
                walletId = any(),
                walletName = any(),
                amount = capture(slot),
            )
        }

        slot.captured shouldBe subscriptionDueToday.amount
    }

    @Test
    fun `caso exista uma assinatura e outras contas vencendo hoje, deve notificar normalmente`() {
        val scheduledDate = getLocalDate()

        every {
            walletService.findWallet(scheduledBill.walletId)
        } returns wallet

        every { accountService.findAccountById(any()) } returns walletFixture.founderAccount

        every { accountService.getChatbotType(any<Account>()) } returns ChatbotType.CHATBOT_LEGACY

        val balance = billAdded.amountTotal - 1

        every {
            scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(
                wallet.id,
                getLocalDate(),
            )
        } returns listOf(
            scheduledBill.copy(expires = false, scheduledDate = scheduledDate),
            scheduledBill2.copy(expires = true, scheduledDate = scheduledDate),
        )

        every {
            balanceService.getBalanceFrom(
                wallet.founder.accountId,
                wallet.paymentMethodId,
            )
        } returns Balance(
            balance,
        )
        every { internalBankService.isAfterHour(any()) } returns false

        job.execute(shouldNotifyIfOnlyHasSubscription = true)

        verify(exactly = 0) {
            notificationAdapter.notifySubscriptionInsufficientBalance(
                members = any(),
                walletId = any(),
                walletName = any(),
                amount = any(),
            )
        }

        verify {
            notificationAdapter.notifyInsufficientBalanceToday(
                members = any(),
                pendingScheduledAmountToday = any(),
                pendingAmountTotal7Days = any(),
                pendingAmountTotal15Days = any(),
                walletId = any(),
                walletName = any(),
                isAfterHours = any(),
            )
        }
    }

    @Test
    fun `caso seja uma carteira PJ, e o membro tiver o chatbot habilitado, deve receber uma notificação diferente`() {
        val scheduledDate = getLocalDate()

        val pjWallet = walletFixture.buildPJWallet(walletId = scheduledBill.walletId)
        every {
            walletService.findWallet(scheduledBill.walletId)
        } returns pjWallet

        every { scheduledWalletRepository.findAllWalletsWithScheduledBills() } returns Flux.just(pjWallet.id)

        every { accountService.findAccountById(pjWallet.founder.accountId) } returns walletFixture.pjFounderAccount
        every { accountService.findAccountById(pjWallet.activeMembers.single { it.type == MemberType.COFOUNDER }.accountId) } returns walletFixture.founderAccount

        every {
            accountService.getChatbotType(walletFixture.pjFounderAccount)
        } returns ChatbotType.CHATBOT_LEGACY
        every {
            accountService.getChatbotType(walletFixture.founderAccount)
        } returns ChatbotType.CHABOT_AI

        val balance = billAdded.amountTotal - 1

        every {
            scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(
                pjWallet.id,
                getLocalDate(),
            )
        } returns listOf(
            scheduledBill.copy(expires = false, scheduledDate = scheduledDate),
            scheduledBill2.copy(expires = true, scheduledDate = scheduledDate),
        )

        every {
            balanceService.getBalanceFrom(
                pjWallet.founder.accountId,
                pjWallet.paymentMethodId,
            )
        } returns Balance(
            balance,
        )
        every { internalBankService.isAfterHour(any()) } returns false

        job.execute(shouldNotifyIfOnlyHasSubscription = true)

        verify(exactly = 0) {
            notificationAdapter.notifySubscriptionInsufficientBalance(
                members = any(),
                walletId = any(),
                walletName = any(),
                amount = any(),
            )
        }

        verify(exactly = 0) {
            notificationAdapter.notifyInsufficientBalanceToday(
                members = any(),
                pendingScheduledAmountToday = any(),
                pendingAmountTotal7Days = any(),
                pendingAmountTotal15Days = any(),
                walletId = any(),
                walletName = any(),
                isAfterHours = any(),
            )
        }

        verify {
            notificationAdapter.notifyInsufficientBalanceTodaySecondaryWallet(
                members = any(),
                pendingScheduledAmountToday = any(),
                walletId = any(),
                walletName = any(),
            )
        }
    }
}