package ai.friday.billpayment.adapters.jobs

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.buildInvite
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.withUnlockedLock
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.shouldBe
import io.mockk.called
import io.mockk.mockk
import io.mockk.verify
import java.time.Duration
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class NotifyInviteReminderJobTest {
    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val enhancedClient = getDynamoDB()

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountDbRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletDbRepository: WalletDbRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = accountDbRepository,
    )

    private val expectedDurationToNotify = Duration.ofDays(10)

    private val commCentreAdapter: NotificationAdapter = mockk(relaxUnitFun = true)
    private val notifyInviteReminderJob = NotifyInviteReminderJob(
        walletDbRepository = walletDbRepository,
        commCentreAdapter = commCentreAdapter,
        timeToRemind = expectedDurationToNotify,
        accountRepository = accountDbRepository,
        cron = "",
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `quando a data de criação do convite for menor que a duração da configuração não deve enviar notificação`() {
        val inviteToNotify = buildInvite(
            created = getZonedDateTime().minusDays(11),
            memberDocument = Document("***********"),
        )
        walletDbRepository.save(
            buildInvite(
                created = getZonedDateTime().minusDays(9),
                memberDocument = Document("***********"),
            ),
        )
        walletDbRepository.save(inviteToNotify)

        withUnlockedLock { notifyInviteReminderJob.execute() }

        verify(exactly = 1) {
            commCentreAdapter.notifyInviteReminder(
                inviteToNotify,
                null,
            )
        }

        walletDbRepository.isInviteReminderSent(inviteToNotify) shouldBe true
    }

    @Test
    fun `o convite não pode ser notificado novamente mais de uma vez`() {
        val inviteAlreadyNotified = buildInvite(
            created = getZonedDateTime().minusDays(11),
            memberDocument = Document("***********"),
        )
        walletDbRepository.save(inviteAlreadyNotified)
        walletDbRepository.markInviteReminderAsSent(inviteAlreadyNotified)

        withUnlockedLock { notifyInviteReminderJob.execute() }

        verify { commCentreAdapter wasNot called }

        walletDbRepository.isInviteReminderSent(inviteAlreadyNotified) shouldBe true
    }

    @Test
    fun `quando convite for para um usuário existente na base deve enviar o convite com suas informações`() {
        val invite = buildInvite(
            created = getZonedDateTime().minusDays(11),
            memberDocument = Document(ACCOUNT.document),
        )
        walletDbRepository.save(invite)
        accountDbRepository.save(ACCOUNT)

        withUnlockedLock { notifyInviteReminderJob.execute() }

        verify { commCentreAdapter.notifyInviteReminder(invite, ACCOUNT) }

        walletDbRepository.isInviteReminderSent(invite) shouldBe true
    }
}