package ai.friday.billpayment.adapters.jobs

import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.shouldBe
import java.time.ZonedDateTime
import org.junit.jupiter.api.Test

class UpdatePartitionedBillTrackingCalculateGetNextExecutionTimeJobTest {
    private val job = UpdatePartitionedBillTracking

    @Test
    fun `deve retornar 4 horas e 1 minuto`() {
        withGivenDateTime(ZonedDateTime.now().withHour(20).withMinute(0).withSecond(0)) {
            val timeWindow = job.getLockTimeWindow()
            timeWindow.toString() shouldBe "PT7H"
        }
    }
}