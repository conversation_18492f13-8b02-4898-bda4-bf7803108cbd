package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.messaging.SQSMessagePublisher
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.dda.DDARegister
import ai.friday.billpayment.app.dda.DDARegisterMessage
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.dda.DDAStatus
import ai.friday.billpayment.app.dda.toDDARegisterMessage
import ai.friday.billpayment.app.message.QueueMessage
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.withUnlockedLock
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class StartDDABillJobTest {

    private val ddaService = mockk<DDAService>()

    private val messagePublisher = mockk<SQSMessagePublisher>(relaxUnitFun = true)

    val configuration = mockk<SQSMessageHandlerConfiguration>() {
        every { ddaQueueName } returns "test-queue"
    }

    private val ddaBillJob = StartDDABillJob(
        ddaService = ddaService,
        configuration = configuration,
        messagePublisher = messagePublisher,
    )

    private val ddaRegister = DDARegister(
        accountId = AccountId(ACCOUNT_ID),
        document = DOCUMENT,
        created = getZonedDateTime(),
        status = DDAStatus.ACTIVE,
        lastUpdated = getZonedDateTime(),
    )

    @BeforeEach
    fun setup() {
        every {
            ddaService.findActive()
        } returns listOf(ddaRegister)
    }

    @Test
    fun `should publish dda register into dda queue`() {
        withUnlockedLock {
            ddaBillJob.execute()
        }

        val slot = mutableListOf<QueueMessage>()
        verify(exactly = 1) {
            messagePublisher.sendMessage(capture(slot))
        }

        slot.first().queueName shouldBe "test-queue"

        val messageBody = slot.map {
            parseObjectFrom<DDARegisterMessage>(it.jsonObject)
        }.first()

        messageBody shouldBe ddaRegister.toDDARegisterMessage()
    }
}