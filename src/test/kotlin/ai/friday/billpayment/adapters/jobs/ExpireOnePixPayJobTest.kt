package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.onepixpay.OnePixPay
import ai.friday.billpayment.app.onepixpay.OnePixPayRepository
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.onepixpay.OnePixPayStatus
import ai.friday.billpayment.app.onepixpay.UUIDOnePixPayId
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalTime
import org.junit.jupiter.api.Test

class ExpireOnePixPayJobTest {

    private val onePixPayRepository: OnePixPayRepository = mockk()

    private val onePixPayService: OnePixPayService = mockk() {
        every { save(any()) } returns mockk()
    }

    private val expireOnePixPayJob = ExpireOnePixPayJob(
        onePixPayRepository = onePixPayRepository,
        onePixPayService = onePixPayService,
    )

    private val onePixPay = OnePixPay(
        id = UUIDOnePixPayId(),
        walletId = WalletId(),
        billIds = listOf(BillId()),
        qrCode = "teste",
        status = OnePixPayStatus.CREATED,
        paymentLimitTime = LocalTime.NOON,
        fundsReceived = false,
        fundsReceivedAt = null,
        created = getZonedDateTime().minusDays(1),
    )

    @Test
    fun `deve expirar todos one pix pay que estiverem como CREATED ou REQUESTED após 1 dia`() {
        every {
            onePixPayRepository.findAll(any())
        } returns listOf(onePixPay)

        expireOnePixPayJob.execute()

        val slot = slot<OnePixPay>()

        verify {
            onePixPayService.save(capture(slot))
        }

        slot.captured.status shouldBe OnePixPayStatus.EXPIRED
    }

    @Test
    fun `não deve expirar one pix pay quando estiver como PROCESSED ou ERROR após 1 dia`() {
        val yesterday = getZonedDateTime().minusDays(1)

        every {
            onePixPayRepository.findAll(yesterday.toLocalDate())
        } returns listOf(
            onePixPay,
            onePixPay.copy(status = OnePixPayStatus.ERROR),
            onePixPay.copy(status = OnePixPayStatus.PROCESSED),
        )

        expireOnePixPayJob.execute()

        val slot = slot<OnePixPay>()
        verify(exactly = 1) {
            onePixPayService.save(capture(slot))
        }
        slot.captured.status shouldBe OnePixPayStatus.EXPIRED
    }
}