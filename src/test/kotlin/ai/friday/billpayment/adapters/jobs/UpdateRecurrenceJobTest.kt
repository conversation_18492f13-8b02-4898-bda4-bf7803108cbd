package ai.friday.billpayment.adapters.jobs

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory
import io.kotest.matchers.date.shouldBeAfter
import io.kotest.matchers.shouldNotBe
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

class UpdateRecurrenceJobTest {

    @ParameterizedTest
    @ValueSource(strings = ["application.yml"])
    fun `ultima data da recorrencia deve estar pelo menos um ano para frente`(resourceName: String) {
        val resourceStream = Thread.currentThread().contextClassLoader.getResourceAsStream(resourceName)

        resourceStream shouldNotBe null

        val om = ObjectMapper(YAMLFactory())

        val properties: Map<String, Any> = om.readValue(resourceStream, Map::class.java) as (Map<String, Any>)

        val limitDate = (properties["recurrence"] as (Map<String, Any>))["limitDate"]

        val date = LocalDate.parse(limitDate as String, DateTimeFormatter.ISO_DATE)

        date shouldBeAfter LocalDate.now().plusYears(1)
    }
}