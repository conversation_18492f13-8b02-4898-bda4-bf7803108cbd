package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.payment.StartProcessScheduledBillsService
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

class ProcessExpirableScheduledBillsJobTest {

    private val startProcessScheduledBillsService: StartProcessScheduledBillsService = mockk(relaxed = true)

    private val job = ProcessExpirableScheduledBillsJob(
        startProcessScheduledBillsService = startProcessScheduledBillsService,
        cron = "",
    )

    @Test
    fun `should start process scheduled bills service`() {
        job.execute()
        verify {
            startProcessScheduledBillsService.start(includeSubscription = false)
        }
    }
}