package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.ScheduledBillRepository
import ai.friday.billpayment.app.integrations.ScheduledWalletRepository
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.scheduledBill
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import org.junit.jupiter.api.Test
import reactor.core.publisher.Flux

internal class ExpireScheduledBillJobTest {

    private val walletId = WalletId(WALLET_ID)

    private val updateBillService: UpdateBillService = mockk {
        every { cancelScheduledPayment(any(), any(), any()) } returns true
    }
    private val scheduledWalletRepository: ScheduledWalletRepository = mockk {
        every { findWalletsWithScheduledBillsUntil(any()) } returns Flux.just(walletId)
    }
    private val scheduledBillRepository: ScheduledBillRepository = mockk {
        every { findScheduledBillsByWalletIdAndUntilScheduledDate(walletId, any()) } returns emptyList()
    }

    private val billRepository: BillRepository = mockk {
        every { findBill(any(), walletId) } returns getActiveBill(walletId)
    }

    private val expireScheduledBillJob = ExpireScheduledBillJob(
        updateBillService = updateBillService,
        scheduledBillRepository = scheduledBillRepository,
        scheduledWalletRepository = scheduledWalletRepository,
        billRepository = billRepository,
        cron = "0 0 0 * * *",
        jobExecutor = ThreadPoolExecutor(
            1,
            1,
            1,
            TimeUnit.MINUTES,
            LinkedBlockingQueue(),
        ),
    )

    @Test
    fun `should do nothing when there is no bill schedule expiring`() {
        expireScheduledBillJob.execute()

        verify {
            updateBillService wasNot called
        }
    }

    @Test
    fun `should expire bill schedule when schedule has more than 1 day`() {
        every {
            scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(
                walletId,
                any(),
            )
        } returns listOf(scheduledBill.copy(expires = true))

        expireScheduledBillJob.execute()

        verify {
            scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(
                walletId,
                getZonedDateTime().toLocalDate().minusDays(1),
            )
            updateBillService.cancelScheduledPayment(
                walletId,
                scheduledBill.billId,
                ScheduleCanceledReason.EXPIRATION,
            )
        }
    }

    @Test
    fun `should not expire bill schedule when schedule has more than 1 day and is not expirable`() {
        every {
            scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(
                walletId,
                any(),
            )
        } returns listOf(scheduledBill.copy(expires = false))

        expireScheduledBillJob.execute()

        verify {
            scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(
                walletId,
                getZonedDateTime().toLocalDate().minusDays(1),
            )

            updateBillService wasNot called
        }
    }

    @Test
    fun `should not expire bill schedule when schedule has more than 1 day, is expirable but is processing`() {
        every {
            scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(
                walletId,
                any(),
            )
        } returns listOf(scheduledBill.copy(expires = true))

        every {
            billRepository.findBill(any(), walletId)
        } returns getActiveBill(walletId).copy(status = BillStatus.PROCESSING)

        expireScheduledBillJob.execute()

        verify {
            updateBillService wasNot called
        }
    }
}