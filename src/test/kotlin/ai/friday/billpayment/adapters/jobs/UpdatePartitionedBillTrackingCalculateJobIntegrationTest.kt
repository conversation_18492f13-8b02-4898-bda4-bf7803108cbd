/*
package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.DynamoContainerTest
import ai.friday.billpayment.TrackableBillFixture
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_TABLE_NAME
import ai.friday.billpayment.adapters.dynamodb.BillTrackingDbRepository
import ai.friday.billpayment.adapters.dynamodb.BillTrackingDynamoDAO
import ai.friday.billpayment.adapters.lock.InternalLockConfiguration
import ai.friday.billpayment.adapters.lock.InternalLockProvider
import ai.friday.billpayment.app.bill.BillTrackingCalculateOptions
import ai.friday.billpayment.app.feature.FeaturesRepository
import ai.friday.billpayment.app.integrations.BillTrackingRepository
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.message.QueueMessageBatch
import ai.friday.billpayment.fixture.BillTrackingFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import java.time.Duration
import java.time.temporal.ChronoUnit
import java.util.UUID
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import net.javacrumbs.shedlock.core.LockProvider
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.services.dynamodb.DynamoDbClient

@Disabled("verificar se o docker está iniciado antes de executar esse teste")
@DynamoContainerTest(locker = true)
class UpdatePartitionedBillTrackingCalculateJobIntegrationTest {
    private lateinit var calculateJob: UpdatePartitionedBillTrackingCalculateJob
    private lateinit var updateBillTracking: UpdatePartitionedBillTracking
    private lateinit var billTrackingRepository: BillTrackingRepository
    private lateinit var messagePublisher: MessagePublisher
    private lateinit var featuresRepository: FeaturesRepository
    private lateinit var calculateLocker: InternalLock
    private lateinit var queryLocker: InternalLock

    @BeforeEach
    internal fun setUp(enhancedAsync: DynamoDbEnhancedAsyncClient, locker: LockProvider) {
        billTrackingRepository =
            spyk(BillTrackingDbRepository(BillTrackingDynamoDAO(enhancedAsync), BillTrackingFixture.properties()))
        this.calculateLocker = InternalLockProvider(
            locker,
            InternalLockConfiguration(
                "bill-tracking-calculate",
                Duration.of(5, ChronoUnit.MINUTES),
                Duration.of(0, ChronoUnit.MINUTES),
                "BILL_TRACKING_CALCULATE#",
            ),
        )

        this.queryLocker = InternalLockProvider(
            locker,
            InternalLockConfiguration(
                "bill-tracking-query",
                Duration.of(5, ChronoUnit.MINUTES),
                Duration.of(0, ChronoUnit.MINUTES),
                "BILL_TRACKING_QUERY#",
            ),
        )

        messagePublisher = mockk()
        featuresRepository = mockk()
        val executor = ThreadPoolExecutor(
            1,
            2,
            1,
            TimeUnit.MINUTES,
            LinkedBlockingQueue(),
        )

        updateBillTracking = UpdatePartitionedBillTracking(
            properties = BillTrackingFixture.properties(),
            billTrackingRepository = billTrackingRepository,
            messagePublisher = messagePublisher,
            featuresRepository = featuresRepository,
            calculateLocker = this.calculateLocker,
            queryLocker = this.queryLocker,
            jobExecutor = executor,
        )

        calculateJob = UpdatePartitionedBillTrackingCalculateJob(updateBillTracking)
    }

    @Test
    internal fun `should process only bill tracking with calculate index`() {
        every { featuresRepository.getAll().maintenanceMode } returns false
        every { messagePublisher.sendMessageBatch(any()) } just runs

        repeat(5) {
            billTrackingRepository.save(
                TrackableBillFixture.create(
                    billId = UUID.randomUUID().toString(),
                ),
                billTrackingType = BillTrackingCalculateOptions.QUERY,
                processingDate = getLocalDate(),
                1,
            )
        }

        repeat(7) {
            billTrackingRepository.save(
                TrackableBillFixture.create(
                    billId = UUID.randomUUID().toString(),
                ),
                billTrackingType = BillTrackingCalculateOptions.CALCULATE,
                processingDate = getLocalDate(),
                1,
            )
        }

        calculateJob.execute()
        val slot = slot<QueueMessageBatch>()
        verify {
            messagePublisher.sendMessageBatch(capture(slot))
        }

        slot.captured.messages.size shouldBe 7
    }

    @Test
    internal fun `should not process any bill tracking`() {
        every { featuresRepository.getAll().maintenanceMode } returns false
        every { messagePublisher.sendMessageBatch(any()) } just runs

        billTrackingRepository.save(
            TrackableBillFixture.create(
                billId = UUID.randomUUID().toString(),
            ),
            billTrackingType = BillTrackingCalculateOptions.QUERY,
            processingDate = getLocalDate(),
            1,
        )

        billTrackingRepository.save(
            TrackableBillFixture.create(
                billId = UUID.randomUUID().toString(),
            ),
            billTrackingType = BillTrackingCalculateOptions.CALCULATE,
            processingDate = getLocalDate(),
            2,
        )

        billTrackingRepository.save(
            TrackableBillFixture.create(
                billId = UUID.randomUUID().toString(),
            ),
            billTrackingType = BillTrackingCalculateOptions.CALCULATE,
            processingDate = getLocalDate().plusDays(1),
            1,
        )

        calculateJob.execute()

        verify {
            messagePublisher wasNot Called
        }
    }

    @Test
    fun `test`(cli: DynamoDbClient) {
        every { featuresRepository.getAll().maintenanceMode } returns false
        every { messagePublisher.sendMessageBatch(any()) } just runs

        val id = UUID.randomUUID()
        val trackableBill = BillTrackingFixture.trackableBill(id = id)

        billTrackingRepository.save(
            trackableBill,
            billTrackingType = BillTrackingCalculateOptions.CALCULATE,
            partition = 4,
            processingDate = getLocalDate().plusDays(1),
        )

        cli.scan {
            it.tableName(BILL_PAYMENT_TABLE_NAME)
        }.items().forEach(::println)

        billTrackingRepository.save(
            trackableBill,
            billTrackingType = BillTrackingCalculateOptions.QUERY,
            processingDate = getLocalDate().plusDays(2),
        )

        cli.scan {
            it.tableName(BILL_PAYMENT_TABLE_NAME)
        }.items().forEach(::println)
    }
}*/