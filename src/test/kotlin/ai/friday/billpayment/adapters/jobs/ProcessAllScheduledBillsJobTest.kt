package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.payment.StartProcessScheduledBillsService
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

class ProcessAllScheduledBillsJobTest {

    private val startProcessScheduledBillsService: StartProcessScheduledBillsService = mockk(relaxed = true)

    private val job = ProcessAllScheduledBillsJob(
        startProcessScheduledBillsService = startProcessScheduledBillsService,
    )

    @Test
    fun `should start process scheduled bills service`() {
        job.execute()
        verify {
            startProcessScheduledBillsService.start(includeSubscription = true)
        }
    }
}