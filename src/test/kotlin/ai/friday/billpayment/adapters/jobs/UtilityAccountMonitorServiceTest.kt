package ai.friday.billpayment.adapters.jobs

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.UtilityAccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.UtilityAccountDynamoDAO
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.utilityaccount.Utility
import ai.friday.billpayment.app.utilityaccount.UtilityAccount
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionDetails
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionStatus
import ai.friday.billpayment.app.utilityaccount.UtilityAccountId
import ai.friday.billpayment.app.utilityaccount.UtilityAccountMonitorService
import ai.friday.billpayment.app.utilityaccount.UtilityConnectionMethod
import ai.friday.billpayment.app.utilityaccount.matches
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WALLET_ID_2
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.toMember
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import java.time.LocalDate
import java.time.ZonedDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.EnumSource

internal class UtilityAccountMonitorServiceTest {
    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val enhancedClient = setupDynamoDB()
    private val utilityAccountRepository =
        UtilityAccountDbRepository(client = UtilityAccountDynamoDAO(cli = enhancedClient))

    private val accountService: AccountService = mockk()
    private val walletService: WalletService = mockk()
    private val billRepository: BillRepository = mockk()

    private val utilityAccountMonitorService = UtilityAccountMonitorService(
        accountService = accountService,
        walletService = walletService,
        billRepository = billRepository,
    )

    private val utilityAccount = UtilityAccount(
        id = UtilityAccountId("test"),
        status = UtilityAccountConnectionStatus.CONNECTED,
        walletId = WalletId(ACCOUNT_ID),
        accountEmail = "<EMAIL>",
        attempts = 0,
        utility = Utility.CLARO_HOME,
        updatedAt = ZonedDateTime.now(),
        createdAt = ZonedDateTime.now().minusMonths(1).minusDays(1),
        lastBillIdFound = BillId("old-bill-id"),
        lastScannedAt = ZonedDateTime.now().minusMonths(1).minusDays(1),
        connectionMethod = UtilityConnectionMethod.SCRAPING,
        connectionDetails = UtilityAccountConnectionDetails.create(Utility.CLARO_HOME, mapOf("login" to "test", "password" to "1234")),
        addedBy = AccountId(ACCOUNT_ID),
    )

    private val walletFixture = WalletFixture()

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        utilityAccountRepository.save(utilityAccount)
        every { walletService.findWalletOrNull(WalletId(ACCOUNT.accountId.value)) } returns walletFixture.buildWallet(id = WalletId(ACCOUNT.accountId.value), walletFounder = ACCOUNT.toMember(type = MemberType.FOUNDER, permissions = MemberPermissions.of(MemberType.FOUNDER)))
        every { accountService.findAccountByIdOrNull(ACCOUNT.accountId) } returns ACCOUNT.copy(configuration = ACCOUNT.configuration.copy(defaultWalletId = WalletId(ACCOUNT.accountId.value)))
        every { billRepository.findByWallet(WalletId(ACCOUNT.accountId.value)) } returns emptyList()
    }

    @Test
    fun `deve retornar a conta encontrada caso encontre uma conta criada nos últimos 30 dias`() {
        val validBill = getActiveBill(
            dueDate = LocalDate.now().plusDays(15),
        ).copy(
            barCode = BarCode.of("84611111111111101582222222222222222222222222"),
            createdOn = getZonedDateTime().minusDays(15).toLocalDateTime(),
        )

        every { billRepository.findByWallet(any()) } returns listOf(
            validBill,
        )

        val executeTime = ZonedDateTime.now()

        withGivenDateTime(time = executeTime) {
            val result = utilityAccountMonitorService.findMatchingBill(utilityAccount)
            result.shouldNotBeNull()
            result.billId shouldBe validBill.billId
        }
    }

    @Test
    fun `deve retornar a conta encontrada caso encontre uma conta criada nos últimos 30 dias na carteira padrão do usuário`() {
        val validBill = getActiveBill(
            walletId = WalletId(WALLET_ID_2),
            dueDate = LocalDate.now().plusDays(15),
        ).copy(
            barCode = BarCode.of("84611111111111101582222222222222222222222222"),
            createdOn = getZonedDateTime().minusDays(15).toLocalDateTime(),
        )
        every { accountService.findAccountByIdOrNull(ACCOUNT.accountId) } returns ACCOUNT.copy(configuration = ACCOUNT.configuration.copy(defaultWalletId = WalletId(WALLET_ID_2)))
        every { billRepository.findByWallet(WalletId(WALLET_ID_2)) } returns listOf(validBill)

        val result = utilityAccountMonitorService.findMatchingBill(utilityAccount)

        result.shouldNotBeNull()
        result.billId shouldBe validBill.billId
    }

    @Test
    fun `deve retornar a conta encontrada caso encontre uma conta criada nos últimos 30 dias via ConnectUtility`() {
        val validBill = getActiveBill(
            dueDate = LocalDate.now().plusDays(15),
        ).copy(
            barCode = BarCode.of("84611111111111101582222222222222222222222222"),
            createdOn = getZonedDateTime().minusDays(15).toLocalDateTime(),
            source = ActionSource.ConnectUtility(accountId = utilityAccount.addedBy),
        )

        every { billRepository.findByWallet(any()) } returns listOf(
            validBill,
        )

        val executeTime = ZonedDateTime.now()

        withGivenDateTime(time = executeTime) {
            val result = utilityAccountMonitorService.findMatchingBill(utilityAccount)
            result.shouldNotBeNull()
            result.billId shouldBe validBill.billId
        }
    }

    @Test
    fun `deve retornar null caso não encontre uma conta nos últimos 30 dias`() {
        val executeTime = ZonedDateTime.now()

        withGivenDateTime(time = executeTime) {
            utilityAccountMonitorService.findMatchingBill(utilityAccount).shouldBeNull()
        }
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "84999999999999900419999999999999999999999999,VIVO_MOBILE",
            "84999999999999900829999999999999999999999999,VIVO_COMBO",
            "84699999999999901589999999999999999999999999,CLARO_HOME",
            "84899999999999901589999999999999999999999999,CLARO_MOBILE",
            "84999999999999900349999999999999999999999999,TIM_MOBILE",
            "84999999999999901139999999999999999999999999,OI_HOME",
            "83999999999999900539999999999999999999999999,LIGHT_RJ",
            "83999999999999901009999999999999999999999999,NATURGY",
            "83999999999999900489999999999999999999999999,ENEL_SP",
            "82999999999999900979999999999999999999999999,SABESP",
            "83999999999999900579999999999999999999999999,COMGAS",
            "83999999999999901389999999999999999999999999,CEMIG",
        ],
    )
    fun `deve retornar o Utitlity correto para o código de barras`(barCodeNumber: String, expectedUtility: Utility) {
        val barCode = BarCode.of(barCodeNumber)

        val barCodeUtility = Utility.entries.singleOrNull { utility ->
            utility.matches(barCode)
        }

        barCodeUtility shouldBe expectedUtility
    }

    @ParameterizedTest
    @EnumSource(Utility::class)
    fun `deve retornar apenas uma utility para cada padrão de código de barras`(utility: Utility) {
        utility.codes.forEach { regex ->
            val barCode = BarCode.of(regex.pattern.replace(".", "9"))

            val barCodeUtility = Utility.entries.singleOrNull { utility ->
                utility.matches(barCode)
            }

            barCodeUtility.shouldNotBeNull()
        }
    }
}