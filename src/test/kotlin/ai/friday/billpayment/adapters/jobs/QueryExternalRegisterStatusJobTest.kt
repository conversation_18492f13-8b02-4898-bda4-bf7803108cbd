package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.accountRegisterDataMissingAcceptedAt
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.ExternalRegisterStatus
import ai.friday.billpayment.app.account.PartialAccount
import ai.friday.billpayment.app.account.RegisterService
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.ExternalAccountRegister
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

internal class QueryExternalRegisterStatusJobTest() {

    val document = accountRegisterDataMissingAcceptedAt.documentInfo!!.cpf
    val name = accountRegisterDataMissingAcceptedAt.documentInfo!!.name

    private val registerService: RegisterService = mockk(relaxed = true)

    private val accountRepository: AccountRepository = mockk() {
        every {
            findPartialAccountByStatus(AccountStatus.UNDER_EXTERNAL_REVIEW)
        } returns listOf(
            PartialAccount(
                id = AccountId(ACCOUNT_ID),
                name = name,
                emailAddress = EmailAddress(email = "<EMAIL>"),
                status = AccountStatus.UNDER_EXTERNAL_REVIEW,
                role = Role.GUEST,
                statusUpdated = null,
                groups = listOf(),
                registrationType = RegistrationType.FULL,
                subscriptionType = SubscriptionType.PIX,
            ),
        )

        every {
            findAccountByUpgradeStatus(any())
        } returns listOf()
    }

    private val accountRegisterRepository: AccountRegisterRepository = mockk() {
        every {
            findByAccountId(AccountId(ACCOUNT_ID))
        } returns accountRegisterDataMissingAcceptedAt.copy(
            agreementData = accountRegisterDataMissingAcceptedAt.agreementData?.copy(
                acceptedAt = getZonedDateTime(),
            ),
        )
    }

    private val externalAccountRegister: ExternalAccountRegister = mockk()

    private val job = QueryExternalRegisterStatusJob(
        accountRepository = accountRepository,
        accountRegisterRepository = accountRegisterRepository,
        externalAccountRegister = externalAccountRegister,
        registerService = registerService,
        featuresRepository = mockk() {
            every {
                getAll().maintenanceMode
            } returns false
        },
    )

    @Test
    fun `should do nothing when account is not UNDER_EXTERNAL_REVIEW`() {
        every {
            accountRepository.findPartialAccountByStatus(AccountStatus.UNDER_EXTERNAL_REVIEW)
        } returns listOf()

        job.execute()

        verify {
            accountRepository.findPartialAccountByStatus(AccountStatus.UNDER_EXTERNAL_REVIEW)
            externalAccountRegister wasNot Called
            registerService wasNot Called
        }
    }

    @Test
    fun `should do nothing when account is UNDER_EXTERNAL_REVIEW and external state is null`() {
        every {
            externalAccountRegister.queryExternalRegisterStatus(name, document)
        } returns null

        job.execute()

        verify {
            accountRepository.findPartialAccountByStatus(AccountStatus.UNDER_EXTERNAL_REVIEW)
            externalAccountRegister.queryExternalRegisterStatus(name, document)
            registerService wasNot Called
        }
    }

    @Test
    fun `should update account status to REJECTED when account is UNDER_EXTERNAL_REVIEW and external state is REJECTED`() {
        every {
            externalAccountRegister.queryExternalRegisterStatus(name, document)
        } returns ExternalRegisterStatus.REJECTED

        job.execute()

        verify {
            accountRepository.findPartialAccountByStatus(AccountStatus.UNDER_EXTERNAL_REVIEW)
            externalAccountRegister.queryExternalRegisterStatus(name, document)
            registerService.updateAccountStatus(document, ExternalRegisterStatus.REJECTED)
        }
    }

    @Test
    fun `should update account status to APPROVED when account is UNDER_EXTERNAL_REVIEW and external state is APPROVED`() {
        every {
            externalAccountRegister.queryExternalRegisterStatus(name, document)
        } returns ExternalRegisterStatus.APPROVED

        job.execute()

        verify {
            accountRepository.findPartialAccountByStatus(AccountStatus.UNDER_EXTERNAL_REVIEW)
            externalAccountRegister.queryExternalRegisterStatus(name, document)
            registerService.updateAccountStatus(document, ExternalRegisterStatus.APPROVED)
        }
    }
}