package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.adapters.dynamodb.CreditCardChallengeDbRepository
import ai.friday.billpayment.app.account.CreditCardChallengeStatus
import ai.friday.billpayment.app.account.DefaultCreditCardService
import ai.friday.billpayment.app.integrations.AcquirerService
import ai.friday.billpayment.buildCreditCardChallenge
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Test

internal class ExpireCreditCardChallengeJobTest {

    private val acquirerServiceMock = mockk<AcquirerService>(relaxUnitFun = true)
    private val creditCardChallengeRepositoryMock = mockk<CreditCardChallengeDbRepository>() {
        every { save(any()) } just runs
    }
    private val creditCardService = DefaultCreditCardService(
        accountRepository = mockk(),
        acquirerService = acquirerServiceMock,
        featureConfiguration = mockk(),
        creditCardChallengeRepository = creditCardChallengeRepositoryMock,
        generalCreditCardConfiguration = mockk(),
        limitService = mockk(relaxed = true),
        hmacService = mockk(relaxed = true),
        lockProvider = mockk(relaxed = true),
        creditCardScoreService = mockk(),
        creditCardInformationService = mockk(),
    )

    private val expireCreditCardChallengeJob = ExpireCreditCardChallengeJob(
        creditCardService = creditCardService,
        creditCardChallengeRepository = creditCardChallengeRepositoryMock,
        cron = "* * * * *",
    )

    private val activeCreditCardChallenge = buildCreditCardChallenge()

    @Test
    fun `deve expirar a validação quando passar do tempo de expiração`() {
        val creditCardChallenge = buildCreditCardChallenge(expirationDate = getZonedDateTime().minusDays(1))
        every {
            creditCardChallengeRepositoryMock.findAll(CreditCardChallengeStatus.ACTIVE)
        } returns listOf(creditCardChallenge, activeCreditCardChallenge)

        expireCreditCardChallengeJob.execute()

        verify(exactly = 1) { creditCardChallengeRepositoryMock.save(any()) }
    }

    @Test
    fun `deve realizar o void quando uma validação for expirada`() {
        val creditCardChallenge = buildCreditCardChallenge(expirationDate = getZonedDateTime().minusDays(1))
        every {
            creditCardChallengeRepositoryMock.findAll(CreditCardChallengeStatus.ACTIVE)
        } returns listOf(creditCardChallenge, activeCreditCardChallenge)

        expireCreditCardChallengeJob.execute()

        verify(exactly = 1) {
            acquirerServiceMock.cancel("order-id")
            acquirerServiceMock.cancel("alternative-order-id")
        }
    }
}