package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.account.AccountConfiguration
import ai.friday.billpayment.app.account.AccountConfigurationService
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.account.SystemActivityType
import ai.friday.billpayment.app.subscription.SubscriptionService
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class SubscriptionJobTest {

    private val systemActivityService: SystemActivityService = mockk()

    private val walletService: WalletService = mockk()
    private val accountService: AccountService = mockk<AccountService> {
        every {
            findAccountById(any())
        } returns ACCOUNT.copy(subscriptionType = SubscriptionType.PIX)
    }

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet(otherMembers = listOf(walletFixture.participant))

    private val accountConfigurationService = mockk<AccountConfigurationService> {
        every {
            find(any())
        } answers {
            AccountConfiguration(
                accountId = firstArg(),
                pushNotificationTokens = emptySet(),
                receiveMonthlyStatement = false,
                freeOfFridaySubscription = false,
            )
        }
    }

    private val defaultAmount = 100L
    private val defaultDay = 10

    @Nested
    @DisplayName("create subscription")
    inner class CreateSubscription {
        private val subscriptionService: SubscriptionService = mockk(relaxed = true)

        private val job = SubscriptionJobService(
            subscriptionService = subscriptionService,
            systemActivityService = systemActivityService,
            walletService = walletService,
            subscriptionConfiguration = mockk {
                every {
                    amount
                } returns defaultAmount
                every {
                    dayOfMonth
                } returns defaultDay
            },
            accountConfigurationService = accountConfigurationService,
            accountService = accountService,
        )

        @BeforeEach
        fun setup() {
            every {
                walletService.findWallets(any())
            } returns listOf(wallet)
        }

        @Test
        fun `deve criar assinatura quando o usuario ja teve um boleto de assinatura emitido contra ele`() {
            every {
                systemActivityService.findAccountIdsThat(SystemActivityType.ReceivedDDASubscriptionBill)
            } returns listOf(walletFixture.founder.accountId)

            every {
                systemActivityService.findAccountIdsThat(SystemActivityType.ViewedBillingCarousel)
            } returns listOf()

            every {
                systemActivityService.findAccountIdsThat(SystemActivityType.AcceptedTrial)
            } returns listOf()

            every {
                systemActivityService.findAccountIdsLowerThan(SystemActivityType.AccountActivated, any())
            } returns listOf()

            every {
                systemActivityService.findAccountIdsLowerThan(SystemActivityType.TrialActivated, any())
            } returns listOf()

            job.createSubscriptions()

            verify {
                subscriptionService.subscribe(
                    accountId = walletFixture.founder.accountId,
                    amount = defaultAmount,
                    dayOfMonth = defaultDay,
                )
            }
        }

        @Test
        fun `nao deve criar assinatura quando o usuario for cobrado via in app subscription`() {
            every {
                systemActivityService.findAccountIdsThat(SystemActivityType.ReceivedDDASubscriptionBill)
            } returns listOf(walletFixture.founder.accountId)

            every {
                systemActivityService.findAccountIdsThat(SystemActivityType.ViewedBillingCarousel)
            } returns listOf()

            every {
                systemActivityService.findAccountIdsThat(SystemActivityType.AcceptedTrial)
            } returns listOf()

            every {
                systemActivityService.findAccountIdsLowerThan(SystemActivityType.AccountActivated, any())
            } returns listOf()

            every {
                systemActivityService.findAccountIdsLowerThan(SystemActivityType.TrialActivated, any())
            } returns listOf()

            every {
                accountService.findAccountById(any())
            } returns ACCOUNT.copy(subscriptionType = SubscriptionType.IN_APP)

            job.createSubscriptions()

            verify {
                subscriptionService wasNot Called
            }
        }

        @Test
        fun `deve criar assinatura para os usuarios que viram o carousel de pagamento e foram ativados ha mais de 45 dias`() {
            every {
                systemActivityService.findAccountIdsThat(SystemActivityType.ReceivedDDASubscriptionBill)
            } returns listOf()

            every {
                systemActivityService.findAccountIdsThat(SystemActivityType.ViewedBillingCarousel)
            } returns listOf(walletFixture.founder.accountId)

            every {
                systemActivityService.findAccountIdsThat(SystemActivityType.AcceptedTrial)
            } returns listOf()

            every {
                systemActivityService.findAccountIdsLowerThan(SystemActivityType.TrialActivated, any())
            } returns listOf()

            val now = getLocalDate()

            every {
                systemActivityService.findAccountIdsLowerThan(
                    activityType = SystemActivityType.AccountActivated,
                    activityValue = now.withCutoffDays(45),
                )
            } returns listOf(walletFixture.founder.accountId, AccountId(ACCOUNT_ID))

            every {
                systemActivityService.findAccountIdsLowerThan(
                    activityType = SystemActivityType.AccountActivated,
                    activityValue = now.withCutoffDays(90),
                )
            } returns listOf()

            job.createSubscriptions()

            verify {
                subscriptionService.subscribe(
                    accountId = walletFixture.founder.accountId,
                    amount = defaultAmount,
                    dayOfMonth = defaultDay,
                )
            }
        }

        @Test
        fun `deve criar assinatura para os usuarios que viram o carousel de pagamento com trial e o periodo de trial se encerrou`() {
            every {
                systemActivityService.findAccountIdsThat(
                    SystemActivityType.ReceivedDDASubscriptionBill,
                )
            } returns listOf()

            every {
                systemActivityService.findAccountIdsThat(
                    SystemActivityType.ViewedBillingCarousel,
                )
            } returns listOf()

            every {
                systemActivityService.findAccountIdsThat(SystemActivityType.AcceptedTrial)
            } returns listOf()

            every {
                systemActivityService.findAccountIdsLowerThan(SystemActivityType.TrialActivated, any())
            } returns listOf(walletFixture.founder.accountId)

            val now = getLocalDate()

            every {
                systemActivityService.findAccountIdsLowerThan(
                    SystemActivityType.AccountActivated,
                    now.withCutoffDays(45),
                )
            } returns listOf()

            job.createSubscriptions()

            verify {
                subscriptionService.subscribe(
                    accountId = walletFixture.founder.accountId,
                    amount = defaultAmount,
                    dayOfMonth = defaultDay,
                )
            }
        }

        @Test
        fun `nao deve criar assinatura para os usuarios que viram o carousel de pagamento com trial mas o periodo de trial nao se encerrou`() {
            every {
                systemActivityService.findAccountIdsThat(
                    SystemActivityType.ReceivedDDASubscriptionBill,
                )
            } returns listOf()

            every {
                systemActivityService.findAccountIdsThat(
                    SystemActivityType.ViewedBillingCarousel,
                )
            } returns listOf()

            every {
                systemActivityService.findAccountIdsLowerThan(SystemActivityType.TrialActivated, any())
            } returns listOf()

            val now = getLocalDate()

            every {
                systemActivityService.findAccountIdsLowerThan(
                    SystemActivityType.AccountActivated,
                    now.withCutoffDays(45),
                )
            } returns listOf()

            job.createSubscriptions()

            verify {
                subscriptionService wasNot Called
            }
        }

        @Test
        fun `deve criar assinatura quando o usuario participar de outra carteira e tiver uma conta paga na propria carteira`() {
            every {
                systemActivityService.findAccountIdsThat(SystemActivityType.ViewedBillingCarousel)
            } returns listOf(walletFixture.participant.accountId)

            every {
                systemActivityService.findAccountIdsThat(SystemActivityType.AcceptedTrial)
            } returns listOf(walletFixture.participant.accountId)

            every {
                systemActivityService.findAccountIdsLowerThan(SystemActivityType.TrialActivated, any())
            } returns listOf()

            every {
                systemActivityService.getBillPaidOnOwnWallet(walletFixture.participant.accountId)
            } returns true

            every {
                systemActivityService.getDDABillPaid(walletFixture.participant.accountId)
            } returns false

            val now = getLocalDate()

            every {
                systemActivityService.findAccountIdsLowerThan(
                    activityType = SystemActivityType.AccountActivated,
                    activityValue = now.withCutoffDays(45),
                )
            } returns listOf(walletFixture.participant.accountId)

            every {
                systemActivityService.findAccountIdsThat(SystemActivityType.ReceivedDDASubscriptionBill)
            } returns emptyList()

            job.createSubscriptions()

            verify {
                subscriptionService.subscribe(any(), any(), any())
            }
        }

        @Test
        fun `deve criar assinatura quando o usuario participar de outra carteira e tiver uma conta DDA emitida em seu cpf paga`() {
            every {
                systemActivityService.findAccountIdsThat(SystemActivityType.ViewedBillingCarousel)
            } returns listOf(walletFixture.participant.accountId)

            every {
                systemActivityService.findAccountIdsThat(SystemActivityType.AcceptedTrial)
            } returns listOf(walletFixture.participant.accountId)

            every {
                systemActivityService.findAccountIdsLowerThan(SystemActivityType.TrialActivated, any())
            } returns listOf()

            every {
                systemActivityService.getBillPaidOnOwnWallet(walletFixture.participant.accountId)
            } returns false

            every {
                systemActivityService.getDDABillPaid(walletFixture.participant.accountId)
            } returns true

            val now = getLocalDate()

            every {
                systemActivityService.findAccountIdsLowerThan(
                    activityType = SystemActivityType.AccountActivated,
                    activityValue = now.withCutoffDays(45),
                )
            } returns listOf(walletFixture.participant.accountId)

            every {
                systemActivityService.findAccountIdsThat(SystemActivityType.ReceivedDDASubscriptionBill)
            } returns emptyList()

            job.createSubscriptions()

            verify {
                subscriptionService.subscribe(any(), any(), any())
            }
        }

        @Test
        fun `nao deve criar assinatura quando o usuario participar de outra carteira e nao tiver uma conta DDA emitida em seu cpf paga nem ter pago uma conta na propria carteira`() {
            every {
                systemActivityService.findAccountIdsThat(SystemActivityType.ViewedBillingCarousel)
            } returns listOf(walletFixture.participant.accountId)

            every {
                systemActivityService.findAccountIdsThat(SystemActivityType.AcceptedTrial)
            } returns listOf(walletFixture.participant.accountId)

            every {
                systemActivityService.findAccountIdsLowerThan(SystemActivityType.TrialActivated, any())
            } returns listOf()

            val now = getLocalDate()

            every {
                systemActivityService.findAccountIdsLowerThan(
                    activityType = SystemActivityType.AccountActivated,
                    activityValue = now.withCutoffDays(45),
                )
            } returns listOf(walletFixture.participant.accountId)

            every {
                systemActivityService.findAccountIdsLowerThan(
                    activityType = SystemActivityType.AccountActivated,
                    activityValue = now.withCutoffDays(90),
                )
            } returns listOf()

            every {
                systemActivityService.findAccountIdsThat(SystemActivityType.ReceivedDDASubscriptionBill)
            } returns listOf(walletFixture.participant.accountId)

            every {
                systemActivityService.getBillPaidOnOwnWallet(walletFixture.participant.accountId)
            } returns false

            every {
                systemActivityService.getDDABillPaid(walletFixture.participant.accountId)
            } returns false

            job.createSubscriptions()

            verify(exactly = 0) {
                subscriptionService.subscribe(any(), any(), any())
            }
        }

        @ParameterizedTest
        @CsvSource(value = ["2022-10-14,2022-08-26", "2022-10-09,2022-08-26", "2022-10-10,2022-08-26", "2022-12-11,2022-10-26"])
        fun `deve calcular a data de corte de ativacao de conta`(now: String, expectedNextDueDate: String) {
            val nextDueDate =
                job.calcAccountActivationCutoffDate(LocalDate.parse(now, DateTimeFormatter.ISO_DATE), 45)

            nextDueDate.format(DateTimeFormatter.ISO_DATE) shouldBe expectedNextDueDate
        }
    }

    private fun LocalDate.withCutoffDays(days: Long): String {
        return withDayOfMonth(10).minusDays(days).format(DateTimeFormatter.ISO_DATE)
    }
}