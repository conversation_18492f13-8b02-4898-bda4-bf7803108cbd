package ai.friday.billpayment.adapters.bill

import DynamoDBUtils
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillPaymentScheduled
import ai.friday.billpayment.app.bill.BillPaymentScheduledBalanceInfo
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.createLockTable
import io.kotest.matchers.shouldBe
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import java.time.LocalDate
import kotlin.test.fail
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class DefaultBillEventPublisherTest {

    @Test
    fun `should add scheduledBy to the bill and the billView`() {
        val bill = Bill.build(billAdded)

        val event = BillPaymentScheduled(
            billId = bill.billId,
            created = 1387,
            walletId = bill.walletId,
            actionSource = ActionSource.Api(accountId = AccountId("MFONTES")),
            scheduledDate = LocalDate.now(),
            amount = 9339,
            paymentLimitTime = null,
            infoData = BillPaymentScheduledBalanceInfo(amount = 9339, paymentMethodId = "Test", calculationId = "Test"),
            batchSchedulingId = null,
            fingerprint = null,
        )

        // when
        billEventPublisher.publish(bill, event)

        val storedBill = eventRepository.getBillById(bill.billId).getOrNull() ?: fail("Bill not found")

        // check bill
        listOf(storedBill, bill).forEach {
            it.scheduledBy shouldBe AccountId("MFONTES")
        }

        // check bill view
        billRepository.findBill(bill.billId, bill.walletId).run {
            scheduledBy shouldBe AccountId("MFONTES")
        }

        // then
        verify {
            eventRepository.save(event)
            billRepository.save(bill)
            billEventPublisher.publish(bill, event)
        }
    }

    @Test
    fun `should add paymentWalletId to the bill and the billView`() {
        val bill = Bill.build(billAdded)

        val event = BillPaymentScheduled(
            billId = bill.billId,
            created = 1387,
            walletId = WalletId("MFONTES"),
            actionSource = ActionSource.Api(accountId = AccountId("MFONTES")),
            scheduledDate = LocalDate.now(),
            amount = 9339,
            paymentLimitTime = null,
            infoData = BillPaymentScheduledBalanceInfo(amount = 9339, paymentMethodId = "Test", calculationId = "Test"),
            batchSchedulingId = null,
            fingerprint = null,
            paymentWalletId = WalletId("DGOMES"),
        )

        // when
        billEventPublisher.publish(bill, event)

        val storedBill = eventRepository.getBillById(bill.billId).getOrNull() ?: fail("Bill not found")

        // check bill
        listOf(storedBill, bill).forEach {
            it.paymentWalletId shouldBe WalletId("DGOMES")
        }

        // check bill view
        billRepository.findBill(bill.billId, bill.walletId).run {
            paymentWalletId shouldBe WalletId("DGOMES")
        }

        // then
        verify {
            eventRepository.save(event)
            billRepository.save(bill)
            billEventPublisher.publish(bill, event)
        }
    }

    @BeforeEach
    fun beforeEach() = setup()

    private companion object {
        val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
        private val enhancedClient = DynamoDBUtils.setupDynamoDB()
        private val billEventDAO = BillEventDynamoDAO(enhancedClient)
        private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(enhancedClient)

        val transactionDynamo = TransactionDynamo(enhancedClient)
        val eventRepository = spyk(
            BillEventDBRepository(
                billEventDAO = billEventDAO,
                uniqueConstraintDAO = uniqueConstraintDAO,
                featureConfiguration = mockk(),
                transactionDynamo = transactionDynamo,
            ),
        )
        val billRepository = spyk(
            DynamoDbBillRepository(
                billClient = BillDynamoDAO(enhancedClient),
                refundedClient = RefundedBillDynamoDAO(enhancedClient),
                settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(enhancedClient),
            ),
        )
        val eventPublisher = mockk<EventPublisher>(relaxed = true)

        val billEventPublisher = spyk(DefaultBillEventPublisher(eventRepository = eventRepository, billRepository = billRepository, eventPublisher = eventPublisher))

        fun setup() {
            createBillPaymentTable(dynamoDB)
            createBillEventTable(dynamoDB)
            createLockTable(dynamoDB, "Shedlock")
        }
    }
}