package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.adapters.circuitbreaker.CircuitBreakerManager
import ai.friday.billpayment.app.FRIDAY_ENV
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponseFactory
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.client.netty.DefaultHttpClient
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.reactivex.Flowable
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

@MicronautTest(rebuildContext = false, environments = [FRIDAY_ENV])
class ArbiAdapterCircuitBreakerTest(private val arbiAdapter: ArbiAdapter, private val manager: CircuitBreakerManager) {

    @MockBean(DefaultHttpClient::class)
    fun getClient(): RxHttpClient {
        return mockClient
    }

    private val mockClient: RxHttpClient = mockk(relaxed = true) {
        every {
            stop()
        } returns this
    }

    @BeforeEach
    fun setup() {
        every {
            mockClient.retrieve(
                any<HttpRequest<GrantCodeTO>>(),
                Argument.of(GrantCodeResponseTO::class.java),
            )
        } answers {
            Flowable.just(GrantCodeResponseTO("http://localhost/?code=XXXX_GRANT_CODE_XXXX"))
        }

        every {
            mockClient.retrieve(
                any<HttpRequest<Map<String, String>>>(),
                Argument.of(AccessTokenTO::class.java),
            )
        } answers {
            Flowable.just(
                AccessTokenTO(
                    accessToken = "XXXX_ACCESS_TOKEN_XXXX",
                    refreshToken = "XXXX_REFRESH_TOKEN_XXXX",
                    expiresIn = 365,
                    tokenType = "ACCESS_TOKEN",
                ),
            )
        }
    }

    @Test
    fun `deve ignorar ArbiInvalidBalanceException`() {
        every {
            mockClient.retrieve(
                any<HttpRequest<CheckingRequestTO>>(),
                Argument.of(ArbiResponseTO::class.java),
                Argument.STRING,
            )
        } answers {
            Flowable.just(
                ArbiResponseTO(
                    idTransacao = 3,
                    resultado = "-26.09",
                    idStatus = 201,
                    idRequisicaoParceiro = "51a12856-714a-4640-bd5f-ea3bff71179d",
                    idModulo = 1,
                    idRequisicaoArbi = "151220211831246460@7331585624016",
                    descricaoStatus = "Sucesso",
                ),
            )
        }

        org.junit.jupiter.api.assertThrows<ArbiInvalidBalanceException> {
            arbiAdapter.getBalance("123")
        }
        manager.getRegistry().circuitBreaker("GetBalance").metrics.numberOfSuccessfulCalls shouldBe 1
        manager.getRegistry().circuitBreaker("GetBalance").metrics.numberOfFailedCalls shouldBe 0
        manager.getRegistry().circuitBreaker("GetBalance").reset()
    }

    @Test
    fun `deve ignorar ArbiAccountMissingPermissionsException`() {
        every {
            mockClient.retrieve(
                any<HttpRequest<CheckingRequestTO>>(),
                Argument.of(ArbiResponseTO::class.java),
                Argument.STRING,
            )
        } answers {
            Flowable.error(
                HttpClientResponseException(
                    "erro",
                    HttpResponseFactory.INSTANCE.status<Any>(HttpStatus.FORBIDDEN).body("Usuario/Modulo/transacao/Conta sem permissão."),
                ),
            )
        }

        org.junit.jupiter.api.assertThrows<ArbiAccountMissingPermissionsException> {
            arbiAdapter.getBalance("123")
        }
        manager.getRegistry().circuitBreaker("GetBalance").metrics.numberOfSuccessfulCalls shouldBe 1
        manager.getRegistry().circuitBreaker("GetBalance").metrics.numberOfFailedCalls shouldBe 0
        manager.getRegistry().circuitBreaker("GetBalance").reset()
    }

    @Test
    fun `deve ignorar ArbiInvalidAccountException`() {
        every {
            mockClient.retrieve(
                any<HttpRequest<CheckingRequestTO>>(),
                Argument.of(ArbiResponseTO::class.java),
                Argument.STRING,
            )
        } answers {
            Flowable.error(
                HttpClientResponseException(
                    "erro",
                    HttpResponseFactory.INSTANCE.status<Any>(HttpStatus.UNPROCESSABLE_ENTITY).body("Conta origem não cadastrado e/ou invalido."),
                ),
            )
        }

        org.junit.jupiter.api.assertThrows<ArbiInvalidAccountException> {
            arbiAdapter.getBalance("123")
        }
        manager.getRegistry().circuitBreaker("GetBalance").metrics.numberOfSuccessfulCalls shouldBe 1
        manager.getRegistry().circuitBreaker("GetBalance").metrics.numberOfFailedCalls shouldBe 0
        manager.getRegistry().circuitBreaker("GetBalance").reset()
    }

    @Test
    fun `deve considerar excecoes de ArbiAdapterException`() {
        every {
            mockClient.retrieve(
                any<HttpRequest<CheckingRequestTO>>(),
                Argument.of(ArbiResponseTO::class.java),
                Argument.STRING,
            )
        } answers {
            Flowable.just(
                ArbiResponseTO(
                    idTransacao = 3,
                    resultado = null,
                    idStatus = 201,
                    idRequisicaoParceiro = "51a12856-714a-4640-bd5f-ea3bff71179d",
                    idModulo = 1,
                    idRequisicaoArbi = "151220211831246460@7331585624016",
                    descricaoStatus = "Sucesso",
                ),
            )
        }

        org.junit.jupiter.api.assertThrows<ArbiAdapterException> {
            arbiAdapter.getBalance("123")
        }
        manager.getRegistry().circuitBreaker("GetBalance").metrics.numberOfSuccessfulCalls shouldBe 0
        manager.getRegistry().circuitBreaker("GetBalance").metrics.numberOfFailedCalls shouldBe 1
        manager.getRegistry().circuitBreaker("GetBalance").reset()
    }
}