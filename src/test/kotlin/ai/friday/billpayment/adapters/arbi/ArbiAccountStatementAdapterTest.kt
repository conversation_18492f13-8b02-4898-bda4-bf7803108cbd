package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.balance.Balance
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.mockk.every
import io.mockk.mockk
import io.reactivex.Flowable
import java.time.LocalDate
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ArbiAccountStatementAdapterTest {

    private val mockClient: RxHttpClient = mockk()

    private val configuration = ArbiConfiguration().apply {
        newHost = ""
        host = ""
        grantCodePath = ""
        accessTokenPath = ""
        validatePath = ""
        checkingPath = ""
        checkingV2Path = ""
        getStatementV2Path = ""
        accountStatementPath = ""
        ddaV2Path = ""
        domainPath = ""
        contaCashin = ""
        userToken = ""
        clientId = ""
        clientSecret = ""
        contaTitular = "8745632"
        inscricao = "**************"
        tipoPessoa = "J"
        ddaCadastroPath = ""
        paymentTimeLimit = "18:00"
    }

    private val arbiAdapter =
        ArbiAccountStatementAdapter(
            httpClient = mockClient,
            configuration = configuration,
            authenticationManager = mockk(relaxed = true),
        )

    @BeforeEach
    fun setup() {
        every {
            mockClient.retrieve(
                any<HttpRequest<GrantCodeTO>>(),
                Argument.of(GrantCodeResponseTO::class.java),
            )
        } answers {
            Flowable.just(GrantCodeResponseTO("http://localhost/?code=XXXX_GRANT_CODE_XXXX"))
        }

        every {
            mockClient.retrieve(
                any<HttpRequest<Map<String, String>>>(),
                Argument.of(AccessTokenTO::class.java),
            )
        } answers {
            Flowable.just(
                AccessTokenTO(
                    accessToken = "XXXX_ACCESS_TOKEN_XXXX",
                    refreshToken = "XXXX_REFRESH_TOKEN_XXXX",
                    expiresIn = 365,
                    tokenType = "ACCESS_TOKEN",
                ),
            )
        }
    }

    @Test
    fun `deve tratar consulta de extrato com movimentos no periodo`() {
        every {
            mockClient.exchange(
                any<HttpRequest<Any>>(),
                any<Argument<Any>>(),
                any<Argument<Any>>(),
            )
        } answers {
            Flowable.just(
                HttpResponse.ok(
                    listOf(
                        ArbiBankAccountStatementItem(
                            counterpartyAgency = "",
                            counterpartyBank = "",
                            counterpartyAccount = "",
                            counterpartyCpfCnpj = "",
                            counterpartyName = "",
                            name = "Eloise Hayes",
                            agencyName = "001-9 RIO DE JANEIRO",
                            documentNumber = "0000000",
                            movementNumber = *********,
                            cpmfProvision = 0.00,
                            category = "209",
                            alineaCode = null,
                            historicalCode = "00264",
                            historyComplement = "",
                            inclusionDate = "01/10/2023 08:51:10",
                            releaseDate = "01/10/2023",
                            historyDescription = "PIX- RECEBIMENTO",
                            reversed = "N",
                            limit = 0.00,
                            transactionType = "C",
                            previousBalance = 12198.76,
                            previousBalanceMovement = 0.00,
                            balanceAfterUltimatelyMovementDay = "0.00",
                            updatedBalanceMovement = 0.01,
                            blockedBalance = 0.00,
                            transactionValue = 0.01,
                            purchaseAmount = 0.00,
                            updateBalance = 0.01,
                            blockedAmount = 0.00,
                            balanceAvailable = 0.00,
                            cashAmount = 0.00,
                            errorCode = 0,
                            errorMessage = "",
                        ),
                    ),
                ),
            )
        }

        val statement = arbiAdapter.getStatement(
            accountNo = "**********",
            document = Document("***********"),
            initialDate = LocalDate.of(2023, 10, 1),
            endDate = LocalDate.of(2023, 10, 1),
            logBody = true,
        )
        statement.items.size shouldBe 1
        statement.items[0].amount shouldBe 1
        statement.items[0].type shouldBe BankStatementItemType.PIX
        statement.items[0].flow shouldBe BankStatementItemFlow.CREDIT
    }

    @Test
    fun `deve tratar consulta de extrato sem movimentos no periodo`() {
        every {
            mockClient.exchange(
                any<HttpRequest<Any>>(),
                any<Argument<Any>>(),
                any<Argument<Any>>(),
            )
        } answers {
            Flowable.just(
                HttpResponse.ok(
                    listOf(
                        ArbiBankAccountStatementItem(
                            counterpartyAgency = "",
                            counterpartyBank = "",
                            counterpartyAccount = "",
                            counterpartyCpfCnpj = "",
                            counterpartyName = "",
                            name = "Eloise Hayes",
                            agencyName = "",
                            documentNumber = "",
                            movementNumber = 0,
                            cpmfProvision = 0.00,
                            category = "",
                            alineaCode = "",
                            historicalCode = "",
                            historyComplement = "",
                            inclusionDate = "",
                            releaseDate = null,
                            historyDescription = "",
                            reversed = "",
                            limit = 0.00,
                            transactionType = "",
                            previousBalance = 12198.76,
                            previousBalanceMovement = 0.00,
                            balanceAfterUltimatelyMovementDay = "0.00",
                            updatedBalanceMovement = 0.00,
                            blockedBalance = 0.00,
                            transactionValue = 0.00,
                            purchaseAmount = 0.00,
                            updateBalance = 11898.76,
                            blockedAmount = 0.00,
                            balanceAvailable = 11898.76,
                            cashAmount = 0.00,
                            errorCode = 0,
                            errorMessage = "",
                        ),
                    ),
                ),
            )
        }

        val statement = arbiAdapter.getStatement(
            accountNo = "**********",
            document = Document("***********"),
            initialDate = LocalDate.of(2023, 10, 1),
            endDate = LocalDate.of(2023, 10, 1),
            logBody = true,
        )
        statement.items.size shouldBe 0
        statement.initialBalance shouldBe Balance(12198_76)
        statement.finalBalance shouldBe Balance(12198_76)
    }

    /*@Disabled
    @Test
    fun `deve filtrar os movimentos no periodo consultado`() {
        every {
            mockClient.exchange(
                any<HttpRequest<Any>>(),
                any<Argument<Any>>(),
                any<Argument<Any>>(),
            )
        } answers {
            Flowable.just(
                HttpResponse.ok(setupResponse()),
            )
        }
        val statement = arbiAdapter.getStatement(
            accountNo = "**********",
            document = Document("0000000"),
            initialDate = LocalDate.of(2023, 1, 10),
            endDate = LocalDate.of(2023, 1, 10),
            logBody = true,
        )
        statement.items.size shouldBe 2
        statement.initialBalance shouldBe Balance(200_00)
        statement.finalBalance shouldBe Balance(100_00)
    }

    @Disabled
    @Test
    fun `deve retornar o saldo mesmo se filtrar todos movimentos no periodo consultado`() {
        every {
            mockClient.exchange(
                any<HttpRequest<Any>>(),
                any<Argument<Any>>(),
                any<Argument<Any>>(),
            )
        } answers {
            Flowable.just(
                HttpResponse.ok(setupResponse()),
            )
        }
        val statement = arbiAdapter.getStatement(
            accountNo = "**********",
            document = Document("0000000"),
            initialDate = LocalDate.of(2023, 1, 12),
            endDate = LocalDate.of(2023, 1, 12),
            logBody = true,
        )
        statement.items.size shouldBe 0
        statement.initialBalance shouldBe Balance(200_00)
        statement.finalBalance shouldBe Balance(200_00)
    }

    @Disabled
    @Test
    fun `deve retornar o saldo mesmo se filtrar todos movimentos no periodo consultado 2`() {
        every {
            mockClient.exchange(
                any<HttpRequest<Any>>(),
                any<Argument<Any>>(),
                any<Argument<Any>>(),
            )
        } answers {
            Flowable.just(
                HttpResponse.ok(setupResponse()),
            )
        }
        val statement = arbiAdapter.getStatement(
            accountNo = "**********",
            document = Document("0000000"),
            initialDate = LocalDate.of(2023, 1, 8),
            endDate = LocalDate.of(2023, 1, 8),
            logBody = true,
        )
        statement.items.size shouldBe 0
        statement.initialBalance shouldBe Balance(0)
        statement.finalBalance shouldBe Balance(0)
    }

    private fun setupResponse() = listOf(
        ArbiBankAccountStatementItem(
            counterpartyAgency = "",
            counterpartyBank = "",
            counterpartyAccount = "",
            counterpartyCpfCnpj = "",
            counterpartyName = "",
            name = "Eloise Hayes",
            agencyName = "001-9 RIO DE JANEIRO",
            documentNumber = "0000000",
            movementNumber = *********,
            cpmfProvision = 0.00,
            category = "209",
            alineaCode = null,
            historicalCode = "00264",
            historyComplement = "",
            inclusionDate = "09/01/2023 08:51:10",
            releaseDate = "09/01/2023",
            historyDescription = "PIX- RECEBIMENTO",
            reversed = "N",
            limit = 0.00,
            transactionType = "C",
            previousBalance = 0.00,
            previousBalanceMovement = 0.00,
            balanceAfterUltimatelyMovementDay = "0.00",
            updatedBalanceMovement = 200.00,
            blockedBalance = 0.00,
            transactionValue = 200.00,
            purchaseAmount = 0.00,
            updateBalance = 0.00,
            blockedAmount = 0.00,
            balanceAvailable = 0.00,
            cashAmount = 0.00,
            errorCode = 0,
            errorMessage = "",
        ),
        ArbiBankAccountStatementItem(
            counterpartyAgency = "",
            counterpartyBank = "",
            counterpartyAccount = "",
            counterpartyCpfCnpj = "",
            counterpartyName = "",
            name = "Eloise Hayes",
            agencyName = "001-9 RIO DE JANEIRO",
            documentNumber = "0000000",
            movementNumber = *********,
            cpmfProvision = 0.00,
            category = "209",
            alineaCode = null,
            historicalCode = "00264",
            historyComplement = "",
            inclusionDate = "10/01/2023 08:51:10",
            releaseDate = "10/01/2023",
            historyDescription = "PIX- REMESSA",
            reversed = "N",
            limit = 0.00,
            transactionType = "D",
            previousBalance = 200.00,
            previousBalanceMovement = 200.00,
            balanceAfterUltimatelyMovementDay = "0.00",
            updatedBalanceMovement = 150.00,
            blockedBalance = 0.00,
            transactionValue = 50.00,
            purchaseAmount = 0.00,
            updateBalance = 0.00,
            blockedAmount = 0.00,
            balanceAvailable = 0.00,
            cashAmount = 0.00,
            errorCode = 0,
            errorMessage = "",
        ),
        ArbiBankAccountStatementItem(
            counterpartyAgency = "",
            counterpartyBank = "",
            counterpartyAccount = "",
            counterpartyCpfCnpj = "",
            counterpartyName = "",
            name = "Eloise Hayes",
            agencyName = "001-9 RIO DE JANEIRO",
            documentNumber = "0000000",
            movementNumber = *********,
            cpmfProvision = 0.00,
            category = "209",
            alineaCode = null,
            historicalCode = "00264",
            historyComplement = "",
            inclusionDate = "10/01/2023 08:51:10",
            releaseDate = "10/01/2023",
            historyDescription = "PIX- REMESSA",
            reversed = "N",
            limit = 0.00,
            transactionType = "D",
            previousBalance = 200.00,
            previousBalanceMovement = 150.00,
            balanceAfterUltimatelyMovementDay = "0.00",
            updatedBalanceMovement = 100.00,
            blockedBalance = 0.00,
            transactionValue = 50.00,
            purchaseAmount = 0.00,
            updateBalance = 0.00,
            blockedAmount = 0.00,
            balanceAvailable = 0.00,
            cashAmount = 0.00,
            errorCode = 0,
            errorMessage = "",
        ),
        ArbiBankAccountStatementItem(
            counterpartyAgency = "",
            counterpartyBank = "",
            counterpartyAccount = "",
            counterpartyCpfCnpj = "",
            counterpartyName = "",
            name = "Eloise Hayes",
            agencyName = "001-9 RIO DE JANEIRO",
            documentNumber = "0000000",
            movementNumber = *********,
            cpmfProvision = 0.00,
            category = "209",
            alineaCode = null,
            historicalCode = "00264",
            historyComplement = "",
            inclusionDate = "11/01/2023 08:51:10",
            releaseDate = "11/01/2023",
            historyDescription = "PIX- RECEBIMENTO",
            reversed = "N",
            limit = 0.00,
            transactionType = "C",
            previousBalance = 100.00,
            previousBalanceMovement = 100.00,
            balanceAfterUltimatelyMovementDay = null,
            updatedBalanceMovement = 200.00,
            blockedBalance = 0.00,
            transactionValue = 100.00,
            purchaseAmount = 0.00,
            updateBalance = 0.00,
            blockedAmount = 0.00,
            balanceAvailable = 0.00,
            cashAmount = 0.00,
            errorCode = 0,
            errorMessage = "",
        ),
    )
     */
}