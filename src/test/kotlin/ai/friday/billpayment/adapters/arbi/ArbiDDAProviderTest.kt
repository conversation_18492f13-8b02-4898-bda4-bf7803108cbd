package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.integrations.DDABatchResult
import ai.friday.billpayment.app.integrations.DDAQueryBatchAddResult
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpResponseFactory
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.mockk.every
import io.mockk.mockk
import io.reactivex.Flowable
import java.util.UUID
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class ArbiDDAProviderTest {

    private val mockClient: RxHttpClient = mockk {
        every {
            retrieve(
                any<HttpRequest<GrantCodeTO>>(),
                Argument.of(GrantCodeResponseTO::class.java),
            )
        } answers {
            Flowable.just(GrantCodeResponseTO("http://localhost/?code=XXXX_GRANT_CODE_XXXX"))
        }

        every {
            retrieve(
                any<HttpRequest<Map<String, String>>>(),
                Argument.of(AccessTokenTO::class.java),
            )
        } answers {
            Flowable.just(
                AccessTokenTO(
                    accessToken = "XXXX_ACCESS_TOKEN_XXXX",
                    refreshToken = "XXXX_REFRESH_TOKEN_XXXX",
                    expiresIn = 365,
                    tokenType = "ACCESS_TOKEN",
                ),
            )
        }
    }

    private val configuration = ArbiConfiguration().apply {
        newHost = ""
        host = ""
        grantCodePath = ""
        accessTokenPath = ""
        validatePath = ""
        checkingPath = ""
        ddaPath = ""
        ddaCadastroLotePath = ""
        domainPath = ""
        contaCashin = ""
        userToken = ""
        clientId = ""
        clientSecret = ""
        contaTitular = "8745632"
        inscricao = "12345678000123"
        tipoPessoa = "J"
        ddaCadastroPath = ""
        paymentTimeLimit = "18:00"
    }

    private val arbiAdapter = ArbiAdapter(
        httpClient = mockClient,
        configuration = configuration,
        authenticationManager = mockk(relaxed = true),
        fallbackCheckTransfer = false,
    )

    @Nested
    @DisplayName("na consulta do resultado do lote ")
    inner class QueryBatchAdd {

        @Test
        fun `quando o arbi retornar que nenhum agregado está apto deve retornar os casos de falha`() {
            val idRequisicaoArbi = "10620221831816238@5488560692659"

            every {
                mockClient.retrieve(
                    ofType<HttpRequest<Unit>>(),
                    Argument.listOf(ArbiResponseTO::class.java),
                    Argument.listOf(ArbiResponseTO::class.java),
                )
            } answers {
                Flowable.just(
                    listOf(
                        ArbiResponseTO(
                            idTransacao = 9,
                            resultado = """[
                                |{ "status":"Erro", "cpfCnpj": "08650436714", "descricao" : "excluído na base." },
                                |{ "status":"Erro", "cpfCnpj": "12345678900", "descricao" : "excluído na base." }
                                |]
                            """.trimMargin(),
                            idStatus = 400,
                            idRequisicaoParceiro = UUID.randomUUID().toString(),
                            idModulo = 2,
                            idRequisicaoArbi = idRequisicaoArbi,
                            descricaoStatus = "Nenhum Agregado da lista está Apto para envio.",
                        ),
                    ),
                )
            }

            val batchAddResult = arbiAdapter.queryBatchAdd(idRequisicaoArbi)
            batchAddResult.shouldBeTypeOf<DDAQueryBatchAddResult.Failed>()
            batchAddResult.failed.size shouldBe 2
            batchAddResult.failed shouldContainExactlyInAnyOrder DocumentListFailed
        }

        @Test
        fun `quando o arbi retornar que nenhum agregado está apto porque todos já existem, deve retornar sucesso parcial`() {
            val idRequisicaoArbi = "10620221831816238@5488560692659"

            every {
                mockClient.retrieve(
                    ofType<HttpRequest<Unit>>(),
                    Argument.listOf(ArbiResponseTO::class.java),
                    Argument.listOf(ArbiResponseTO::class.java),
                )
            } answers {
                Flowable.just(
                    listOf(
                        ArbiResponseTO(
                            idTransacao = 9,
                            resultado = """[
                                |{ "status":"Erro", "cpfCnpj": "08650436714", "descricao" : "Já existe/excluído na base." },
                                |{ "status":"Erro", "cpfCnpj": "12345678900", "descricao" : "Já existe/excluído na base." }
                                |]
                            """.trimMargin(),
                            idStatus = 400,
                            idRequisicaoParceiro = UUID.randomUUID().toString(),
                            idModulo = 2,
                            idRequisicaoArbi = idRequisicaoArbi,
                            descricaoStatus = "Nenhum Agregado da lista está Apto para envio.",
                        ),
                    ),
                )
            }

            val batchAddResult = arbiAdapter.queryBatchAdd(idRequisicaoArbi)
            batchAddResult.shouldBeTypeOf<DDAQueryBatchAddResult.PartialSuccess>()
            batchAddResult.failed.size shouldBe 0
            batchAddResult.success.size shouldBe 2
            batchAddResult.success shouldContainExactlyInAnyOrder DocumentListFailed
        }

        @Test
        fun `quando o arbi retornar pedido pendente deve devolver falha`() {
            val idRequisicaoArbi = "10620221831816238@5488560692659"

            every {
                mockClient.retrieve(
                    ofType<HttpRequest<Unit>>(),
                    Argument.listOf(ArbiResponseTO::class.java),
                    Argument.listOf(ArbiResponseTO::class.java),
                )
            } answers {
                Flowable.just(
                    listOf(
                        ArbiResponseTO(
                            idTransacao = 9,
                            resultado = "Todos os agregados estão Aptos.".trimMargin(),
                            idStatus = 400,
                            idRequisicaoParceiro = UUID.randomUUID().toString(),
                            idModulo = 2,
                            idRequisicaoArbi = idRequisicaoArbi,
                            descricaoStatus = "erroPedido pendendente para o pagador de CPF/CNPJ 22896431000110",
                        ),
                    ),
                )
            }

            val batchAddResult = arbiAdapter.queryBatchAdd(idRequisicaoArbi)
            batchAddResult.shouldBeTypeOf<DDAQueryBatchAddResult.PendingRequestError>()
        }

        @Nested
        @DisplayName("quando o arbi retornar pedido com erro de agregado ja cadastrado")
        inner class AgregadoJaCadastrado {

            @Test
            fun `e o resultado for todos os agregados estao aptos, deve devolver successo`() {
                val idRequisicaoArbi = "10620221831816238@5488560692659"

                every {
                    mockClient.retrieve(
                        ofType<HttpRequest<Unit>>(),
                        Argument.listOf(ArbiResponseTO::class.java),
                        Argument.listOf(ArbiResponseTO::class.java),
                    )
                } answers {
                    Flowable.just(
                        listOf(
                            ArbiResponseTO(
                                idTransacao = 9,
                                resultado = "Todos os agregados estão Aptos.".trimMargin(),
                                idStatus = 400,
                                idRequisicaoParceiro = UUID.randomUUID().toString(),
                                idModulo = 2,
                                idRequisicaoArbi = idRequisicaoArbi,
                                descricaoStatus = "erroAgregado de CPF/CNPJ 91445418215 já cadastrado para o pagador 22896431000110",
                            ),
                        ),
                    )
                }

                val batchAddResult = arbiAdapter.queryBatchAdd(idRequisicaoArbi)
                batchAddResult.shouldBeTypeOf<DDAQueryBatchAddResult.Success>()
            }

            // TODO reavaliar cenario se houver necessidade de inclusao em massa no futuro no Arbi
            @Test
            fun `e o resultado for uma lista, deve devolver os casos de sucesso e falha separadamente`() {
                val idRequisicaoArbi = "10620221831816238@5488560692659"

                every {
                    mockClient.retrieve(
                        ofType<HttpRequest<Unit>>(),
                        Argument.listOf(ArbiResponseTO::class.java),
                        Argument.listOf(ArbiResponseTO::class.java),
                    )
                } answers {
                    Flowable.just(
                        listOf(
                            ArbiResponseTO(
                                idTransacao = 9,
                                resultado = """[
                                    |{ "status":"Erro", "cpfCnpj": "08650436714", "descricao" : "Não é um CPF/CNPJ válido." },
                                    |{ "status":"Erro", "cpfCnpj": "12345678900", "descricao" : "Não é um CPF/CNPJ válido." },
                                    |{ "status":"Apto", "cpfCnpj": "00000000000", "descricao" : "Enviado" },
                                    |{ "status":"Apto", "cpfCnpj": "11111111111", "descricao" : "Enviado" }
                                    |]
                                """.trimMargin(),
                                idStatus = 400,
                                idRequisicaoParceiro = UUID.randomUUID().toString(),
                                idModulo = 2,
                                idRequisicaoArbi = idRequisicaoArbi,
                                descricaoStatus = "erroAgregado de CPF/CNPJ 11111111111 já cadastrado para o pagador 22896431000110",
                            ),
                        ),
                    )
                }

                val batchAddResult = arbiAdapter.queryBatchAdd(idRequisicaoArbi)
                batchAddResult.shouldBeTypeOf<DDAQueryBatchAddResult.PartialSuccess>()

                batchAddResult.failed.size shouldBe DocumentListFailed.size
                batchAddResult.failed shouldContainExactlyInAnyOrder DocumentListFailed

                batchAddResult.success.size shouldBe DocumentListSuccess.size
                batchAddResult.success shouldContainExactlyInAnyOrder DocumentListSuccess
            }
        }

        @Test
        fun `quando o arbi retornar pedido processando deve devolver falha`() {
            val idRequisicaoArbi = "10620221831816238@5488560692659"

            every {
                mockClient.retrieve(
                    ofType<HttpRequest<Unit>>(),
                    Argument.listOf(ArbiResponseTO::class.java),
                    Argument.listOf(ArbiResponseTO::class.java),
                )
            } answers {
                Flowable.just(
                    listOf(
                        ArbiResponseTO(
                            idTransacao = 9,
                            resultado = "Em Andamento, Aguarde e consulte novamente.".trimMargin(),
                            idStatus = 102,
                            idRequisicaoParceiro = UUID.randomUUID().toString(),
                            idModulo = 2,
                            idRequisicaoArbi = idRequisicaoArbi,
                            descricaoStatus = "Processando.",
                        ),
                    ),
                )
            }

            val batchAddResult = arbiAdapter.queryBatchAdd(idRequisicaoArbi)
            batchAddResult.shouldBeTypeOf<DDAQueryBatchAddResult.ProcessingRequest>()
        }

        @Test
        fun `quando o arbi retornar sucesso parcial deve retornar os casos de falha e sucesso separadamente`() {
            val idRequisicaoArbi = "10620221831816238@5488560692659"

            every {
                mockClient.retrieve(
                    ofType<HttpRequest<Unit>>(),
                    Argument.listOf(ArbiResponseTO::class.java),
                    Argument.listOf(ArbiResponseTO::class.java),
                )
            } answers {
                Flowable.just(
                    listOf(
                        ArbiResponseTO(
                            idTransacao = 9,
                            resultado = """[
                                |{ "status":"Erro", "cpfCnpj": "08650436714", "descricao" : "Não é um CPF/CNPJ válido." },
                                |{ "status":"Erro", "cpfCnpj": "12345678900", "descricao" : "Não é um CPF/CNPJ válido." },
                                |{ "status":"Erro", "cpfCnpj": "00000000000", "descricao" : "Já existe/excluído na base." },
                                |{ "status":"Apto", "cpfCnpj": "11111111111", "descricao" : "Enviado" }
                                |]
                            """.trimMargin(),
                            idStatus = 206,
                            idRequisicaoParceiro = UUID.randomUUID().toString(),
                            idModulo = 2,
                            idRequisicaoArbi = idRequisicaoArbi,
                            descricaoStatus = "Sucesso Parcial, veja a lista em resultado",
                        ),
                    ),
                )
            }

            val batchAddResult = arbiAdapter.queryBatchAdd(idRequisicaoArbi)
            batchAddResult.shouldBeTypeOf<DDAQueryBatchAddResult.PartialSuccess>()

            batchAddResult.failed.size shouldBe DocumentListFailed.size
            batchAddResult.failed shouldContainExactlyInAnyOrder DocumentListFailed

            batchAddResult.success.size shouldBe DocumentListSuccess.size
            batchAddResult.success shouldContainExactlyInAnyOrder DocumentListSuccess
        }

        @Test
        fun `quando o arbi retornar sucesso para todos os agregados deve retornar sucesso sem os respectivos documentos`() {
            val idRequisicaoArbi = "10620221831816238@5488560692659"

            every {
                mockClient.retrieve(
                    ofType<HttpRequest<Unit>>(),
                    Argument.listOf(ArbiResponseTO::class.java),
                    Argument.listOf(ArbiResponseTO::class.java),
                )
            } answers {
                Flowable.just(
                    listOf(
                        ArbiResponseTO(
                            idTransacao = 9,
                            resultado = "Todos os agregados estão Aptos.".trimMargin(),
                            idStatus = 200,
                            idRequisicaoParceiro = UUID.randomUUID().toString(),
                            idModulo = 2,
                            idRequisicaoArbi = idRequisicaoArbi,
                            descricaoStatus = "Sucesso",
                        ),
                    ),
                )
            }

            val batchAddResult = arbiAdapter.queryBatchAdd(idRequisicaoArbi)
            batchAddResult.shouldBeTypeOf<DDAQueryBatchAddResult.Success>()
        }

        @Test
        fun `quando o arbi retornar erro deve jogar excecao`() {
            val httpResponse = HttpClientResponseException(
                "",
                HttpResponseFactory.INSTANCE.status(
                    HttpStatus.BAD_REQUEST,
                    listOf(
                        ArbiResponseTO(
                            idTransacao = 9,
                            resultado = "Não foi possível localizar o idRequisicaoArbi enviado, entre em contato com administrador",
                            idStatus = 500,
                            idRequisicaoParceiro = UUID.randomUUID().toString(),
                            idModulo = 2,
                            idRequisicaoArbi = "10620221831816238@5488560692659",
                            descricaoStatus = "Erro",
                        ),
                    ),
                ),
            )

            every {
                mockClient.retrieve(
                    ofType<HttpRequest<Unit>>(),
                    Argument.listOf(ArbiResponseTO::class.java),
                    Argument.listOf(ArbiResponseTO::class.java),
                )
            } answers {
                Flowable.error(httpResponse)
            }

            assertThrows<ArbiAdapterException> { arbiAdapter.queryBatchAdd("10620221831816238@5488560692659") }
        }
    }

    @Nested
    @DisplayName("ao realizar um pedido de inclusão em lote ")
    inner class BatchAdd {

        @Test
        fun `e receber sucesso, deve retornar o id da requisicao para consultas posteriores`() {
            val idRequisicaoArbi = "10620221831816238@5488560692659"

            every {
                mockClient.exchange(
                    ofType<HttpRequest<Unit>>(),
                    Argument.listOf(ArbiResponseTO::class.java),
                    Argument.listOf(ArbiResponseTO::class.java),
                )
            } answers {
                Flowable.just(
                    HttpResponse.ok(
                        listOf(
                            ArbiResponseTO(
                                idTransacao = 9,
                                resultado = "Solicitação recebida, consulte o resultado usando o idRequisicaoArbi",
                                idStatus = 200,
                                idRequisicaoParceiro = UUID.randomUUID().toString(),
                                idModulo = 2,
                                idRequisicaoArbi = idRequisicaoArbi,
                                descricaoStatus = "Sucesso",
                            ),
                        ),
                    ),
                )
            }

            val response = arbiAdapter.batchAdd(DocumentListSuccess)

            response shouldBe DDABatchResult.Accepted(idRequisicaoArbi)
        }

        @Test
        fun `e receber falha, deve retornar o status de erro`() {
            val idRequisicaoArbi = "10620221831816238@5488560692659"

            every {
                mockClient.exchange(
                    ofType<HttpRequest<Unit>>(),
                    Argument.listOf(ArbiResponseTO::class.java),
                    Argument.listOf(ArbiResponseTO::class.java),
                )
            } throws HttpClientResponseException(
                "error",
                HttpResponse.serverError(
                    listOf(
                        ArbiResponseTO(
                            idTransacao = 9,
                            resultado = "Ocorreu um erro, Reenvio necessário.",
                            idStatus = 500,
                            idRequisicaoParceiro = UUID.randomUUID().toString(),
                            idModulo = 2,
                            idRequisicaoArbi = idRequisicaoArbi,
                            descricaoStatus = "Não Processado.",
                        ),
                    ),
                ),
            )

            assertThrows<ArbiAdapterException> {
                arbiAdapter.batchAdd(DocumentListSuccess)
            }
        }
    }

    companion object {
        val DocumentListSuccess = listOf(
            Document("00000000000"),
            Document("11111111111"),
        )

        val DocumentListFailed = listOf(
            Document("08650436714"),
            Document("12345678900"),
        )
    }
}