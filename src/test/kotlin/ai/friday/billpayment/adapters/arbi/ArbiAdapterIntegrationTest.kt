/*
package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.FineType
import ai.friday.billpayment.app.payment.InterestType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import java.math.BigInteger
import java.util.UUID
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

@MicronautTest(environments = [FRIDAY_ENV])
@Disabled
class ArbiAdapterIntegrationTest(private val arbiTedAdapter: ArbiTedAdapter, private val adapter: ArbiAdapter) {

    @Test
    fun ted() {
        val bankOperationId = BankOperationId(UUID.randomUUID().toString())
        val bankAccount = BankAccount(
            accountType = AccountType.CHECKING,
            bankNo = 341,
            routingNo = 3830,
            accountNo = BigInteger("*********"),
            accountDv = "01",
            document = "***********",
        )
        val recipient = Recipient(name = "Eduardo", document = "***********", bankAccount = bankAccount)
        val response =
            arbiTedAdapter.transfer(originAccountNo = "**********", recipient = recipient, totalAmount = 100L)
        println(response)
        // val checkStatus = arbiAdapter.checkTED(bankOperationId.value, "**********")
        // println(checkStatus)
    }

    @Test
    fun checkTed() {
        val bankOperationId = "d98ac673-bdd7-4cc3-a001-33c90503a9bf"
        // val checkStatus = arbiAdapter.checkTEDTransferStatus(bankOperationId, getLocalDate(), getLocalDate(), "313094")
        // println(checkStatus)
    }

    @ParameterizedTest
    @EnumSource(AmountCalculationModel::class, mode = EnumSource.Mode.INCLUDE, names = ["ANYONE", "ON_DEMAND"])
    fun `deve calcular o novo valor do boleto com juros e multa`(amountCalculationModel: AmountCalculationModel) {
        val dueDate = getLocalDate().minusDays(3)
        val actualCalculatedAmount = adapter.calculateBillAmount(
            billAmountCalculationRequest = BillAmountCalculationRequest(
                amountCalculationModel = amountCalculationModel,
                originalAmount = 10.000,
                dueDate = dueDate,
                interest = AmountCalculationData(type = "1", date = dueDate.plusDays(1), value = 1.50),
                fine = AmountCalculationData(type = "1", date = dueDate.plusDays(1), value = 1.00),
                discount1 = AmountCalculationData(type = "", date = null, value = null),
                discount2 = AmountCalculationData(type = "", date = null, value = null),
                discount3 = AmountCalculationData(type = "", date = null, value = null),
                abatement = 0.0,
            ),
        )

        with(actualCalculatedAmount!!) {
            calculatedAmount shouldBe 1550
            calculatedFinesAmount shouldBe 100
            calculatedInterestAmount shouldBe 450
            calculatedDiscountAmount shouldBe 0
        }
    }

    @Test
    fun `deve calcular o novo valor do boleto com percentual de juros`() {
        val dueDate = getLocalDate().minusDays(3)
        val actualCalculatedAmount = adapter.calculateBillAmount(
            billAmountCalculationRequest = BillAmountCalculationRequest(
                amountCalculationModel = AmountCalculationModel.ANYONE,
                originalAmount = 10.000,
                dueDate = dueDate,
                interest = AmountCalculationData(
                    type = InterestType.PERCENT_BY_MONTH.code,
                    date = dueDate.plusDays(1),
                    value = 1.50,
                ),
                fine = AmountCalculationData(type = FineType.FREE.code, date = null, value = null),
                discount1 = AmountCalculationData(type = "", date = null, value = null),
                discount2 = AmountCalculationData(type = "", date = null, value = null),
                discount3 = AmountCalculationData(type = "", date = null, value = null),
                abatement = 0.0,
            ),
        )

        with(actualCalculatedAmount!!) {
            calculatedAmount shouldBe 1001
            calculatedFinesAmount shouldBe 0
            calculatedInterestAmount shouldBe 1
            calculatedDiscountAmount shouldBe 0
        }
    }

    @ParameterizedTest
    @EnumSource(
        AmountCalculationModel::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["UNKNOWN", "BENEFICIARY_AFTER_DUE_DATE", "BENEFICIARY_ONLY"],
    )
    fun `deve retornar erro ao calcular o novo valor do boleto com juros e multa`(amountCalculationModel: AmountCalculationModel) {
        val dueDate = getLocalDate().minusDays(3)
        adapter.calculateBillAmount(
            billAmountCalculationRequest = BillAmountCalculationRequest(
                amountCalculationModel = amountCalculationModel,
                originalAmount = 10.000,
                dueDate = dueDate,
                interest = AmountCalculationData(type = "1", date = dueDate.plusDays(1), value = 1.50),
                fine = AmountCalculationData(type = "1", date = dueDate.plusDays(1), value = 1.00),
                discount1 = AmountCalculationData(type = "", date = null, value = null),
                discount2 = AmountCalculationData(type = "", date = null, value = null),
                discount3 = AmountCalculationData(type = "", date = null, value = null),
                abatement = 0.0,
            ),
        ).shouldBeNull()
    }

    @ParameterizedTest
    @EnumSource(
        AmountCalculationModel::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["ANYONE", "BENEFICIARY_AFTER_DUE_DATE", "ON_DEMAND"],
    )
    fun `deve calcular o novo valor do boleto com desconto`(amountCalculationModel: AmountCalculationModel) {
        val dueDate = getLocalDate().plusDays(5)
        val actualCalculatedAmount = adapter.calculateBillAmount(
            billAmountCalculationRequest = BillAmountCalculationRequest(
                amountCalculationModel = amountCalculationModel,
                originalAmount = 100.00,
                dueDate = dueDate,
                interest = AmountCalculationData(type = "1", date = dueDate.plusDays(1), value = 1.50),
                fine = AmountCalculationData(type = "1", date = dueDate.plusDays(1), value = 1.00),
                discount1 = AmountCalculationData(type = "1", date = dueDate.minusDays(3), value = 5.00),
                discount2 = AmountCalculationData(type = "", date = null, value = null),
                discount3 = AmountCalculationData(type = "", date = null, value = null),
                abatement = 0.0,
            ),
        )

        with(actualCalculatedAmount!!) {
            calculatedAmount shouldBe 9500
            calculatedFinesAmount shouldBe 0
            calculatedInterestAmount shouldBe 0
            calculatedDiscountAmount shouldBe 500
        }
    }

    @ParameterizedTest
    @EnumSource(
        AmountCalculationModel::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["UNKNOWN", "BENEFICIARY_ONLY"],
    )
    fun `deve retornar nulo ao calcular o novo valor do boleto com desconto`(amountCalculationModel: AmountCalculationModel) {
        val dueDate = getLocalDate().plusDays(5)
        adapter.calculateBillAmount(
            billAmountCalculationRequest = BillAmountCalculationRequest(
                amountCalculationModel = amountCalculationModel,
                originalAmount = 10.000,
                dueDate = dueDate,
                interest = AmountCalculationData(type = "1", date = dueDate.plusDays(1), value = 1.50),
                fine = AmountCalculationData(type = "1", date = dueDate.plusDays(1), value = 1.00),
                discount1 = AmountCalculationData(type = "1", date = dueDate.minusDays(3), value = 5.00),
                discount2 = AmountCalculationData(type = "", date = null, value = null),
                discount3 = AmountCalculationData(type = "", date = null, value = null),
                abatement = 0.0,
            ),
        ).shouldBeNull()
    }

    @ParameterizedTest
    @EnumSource(
        AmountCalculationModel::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["ANYONE", "BENEFICIARY_AFTER_DUE_DATE", "ON_DEMAND"],
    )
    fun `deve calcular o novo valor do boleto com abatimento`(amountCalculationModel: AmountCalculationModel) {
        val dueDate = getLocalDate().plusDays(5)
        val actualCalculatedAmount = adapter.calculateBillAmount(
            billAmountCalculationRequest = BillAmountCalculationRequest(
                amountCalculationModel = amountCalculationModel,
                originalAmount = 100.00,
                dueDate = dueDate,
                interest = AmountCalculationData(type = "1", date = dueDate.plusDays(1), value = 1.50),
                fine = AmountCalculationData(type = "1", date = dueDate.plusDays(1), value = 1.00),
                discount1 = AmountCalculationData(type = "", date = null, value = null),
                discount2 = AmountCalculationData(type = "", date = null, value = null),
                discount3 = AmountCalculationData(type = "", date = null, value = null),
                abatement = 1.0,
            ),
        )

        with(actualCalculatedAmount!!) {
            calculatedAmount shouldBe 9900
            calculatedFinesAmount shouldBe 0
            calculatedInterestAmount shouldBe 0
            calculatedDiscountAmount shouldBe 0
        }
    }
}*/