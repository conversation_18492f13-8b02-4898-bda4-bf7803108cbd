package ai.friday.billpayment.adapters.arbi

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.messaging.SQSMessagePublisher
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankStatement
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.DefaultBankStatementItem
import ai.friday.billpayment.app.banking.ExternalBankAccount
import ai.friday.billpayment.app.banking.ExternalBankAccountService
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.integrations.BankAccountService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.PixPaymentService
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.shouldBe
import io.micronaut.context.annotation.Primary
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource

@MicronautTest(environments = [FRIDAY_ENV])
class ArbiCallbackIntegrationTest(embeddedServer: EmbeddedServer) {

    private val internalBankService: InternalBankService = mockk(relaxUnitFun = true)

    @MockBean(InternalBankService::class)
    fun internalBankService(): InternalBankService = internalBankService

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val enhancedClient = getDynamoDB()

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val bankAccountService: BankAccountService = mockk()

    private val accountService: AccountService = mockk()

    @MockBean(ArbiAdapter::class)
    fun bankAccountService(): BankAccountService = bankAccountService

    val messagePublisher = mockk<MessagePublisher>(relaxUnitFun = true)

    @MockBean(AccountService::class)
    fun accountService(): AccountService = accountService

    @Primary
    @MockBean(SQSMessagePublisher::class)
    fun messagePublisher(): MessagePublisher = messagePublisher

    @Primary
    @MockBean(NewArbiPixPaymentAdapter::class)
    fun getPixPaymentService(): PixPaymentService = mockk(relaxed = true)

    private val externalBankAccountService: ExternalBankAccountService = mockk(relaxUnitFun = true)

    @MockBean(ExternalBankAccountService::class)
    fun externalBankAccountService(): ExternalBankAccountService = externalBankAccountService

    @field:Property(name = "celcoin-callback.identity")
    lateinit var celcoinIdentity: String

    @field:Property(name = "celcoin-callback.secret")
    lateinit var celcoinSecret: String

    @field:Property(name = "arbi-callback.identity")
    lateinit var arbiIdentity: String

    @field:Property(name = "arbi-callback.secret")
    lateinit var arbiSecret: String

    private val bankAccount = BankAccount(
        accountType = AccountType.CHECKING,
        bankNo = bankNo.toLong(),
        routingNo = routingNo.toLong(),
        accountNo = accountNo.toBigInteger(),
        accountDv = accountDv,
        document = document,
    )

    private lateinit var accountPaymentMethodId: AccountPaymentMethodId

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        accountPaymentMethodId = accountRepository.createAccountPaymentMethod(AccountId(ACCOUNT_ID), bankAccount, 1).id

        every {
            accountService.findAccountIdByPhysicalBankAccountNo(any(), any(), any())
        } returns ACCOUNT.accountId

        every {
            accountService.findAccountById(ACCOUNT.accountId)
        } returns ACCOUNT
    }

    private val arbiCallbackRequest = ArbiCallbackRequest(
        valor = 100,
        codHistorico = "00056",
        descricao = "**********.00",
        bancoorigem = "",
        agenciaorigem = "",
        contaorigem = "",
        inscricao = "",
        nome = "",
        conta = ArbiCallbackContaRequest(
            banco = bankNo,
            agencia = routingNo + routingDv,
            conta = accountNo + accountDv,
        ),
    )

    private val parte = ArbiPixPart(
        codIspb = "********",
        codAgencia = routingNo,
        digitoAgencia = routingDv,
        nroConta = bankAccount.buildFullAccountNumber(),
        tipoConta = "CACC",
        cpfCnpj = bankAccount.document,
        nome = "Joe Montana",
        nomeInstituicao = "BCO ARBI S.A.",
    )

    private val contraParte = parte.copy(nroConta = "0001234", cpfCnpj = "***********")

    private val mensagem = ArbiPixMovementNotificationMessage(
        parte = parte,
        contraParte = contraParte,
        endToEnd = "E********202205161316rbh9xGerhTc",
        tipo = ArbiPixMovementType.CREDITO_EXTERNO,
        valor = 999f,
        dataHoraMovimento = "2022-05-16T10:16:07.257767",
        chave = "<EMAIL>",
        nroMovimento = "*********",
        origemMovimento = ArbiPixMovementOrigin.QR_CODE,
        naturezaMovimento = ArbiPixMovementNature.C,
        indDevolucao = false,
        codOperacao = "00267",
        ehAgendado = false,
        idIdempotente = "e40a97dc-966b-4ec5-abf4-e1a45959dcff",
        canalEntrada = null,
        indExterno = false,
        finalidadeDaTransacao = null,
        txId = "NuC6M4J5HUf4AjEV361GAr",
        reasons = listOf(),
        endToEndOriginal = null,
        campoLivre = "teste",
        infoDevolucao = null,
        infoErro = null,
        campoExtra = null,
        idDevolucao = null,
        cnpjIniciadoraPagamento = "59787242000110",
        uuidBloqueioDevolucaoEspecial = null,
        uuidSolicitacaoDevolucaoEspecial = null,
        motivoMED = null,
        prestadorDoServicoDeSaque = null,
        modalidadeAgente = null,
        valorCompra = null,
        valorTroco = null,
        valorSaque = null,
    )

    @Test
    fun `should return forbidden when role is invalid`() {
        val request = HttpRequest.POST("/arbi/deposit", arbiCallbackRequest)
            .basicAuth(celcoinIdentity, celcoinSecret)
        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java))
        }
        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `should return bad request when value is not present`() {
        val request = HttpRequest.POST(
            "/arbi/deposit",
            """
            {
                "conta": {
                    "banco": "213",
                    "agencia": "00019",
                    "conta": "*********0"
                },
                "codHistorico": "00058",
                "descricao": "**********.00",
                "bancoorigem": "",
                "agenciaorigem": "",
                "contaorigem": "",
                "inscricao": "",
                "nome": ""
            }
            """.trimIndent(),
        )
            .basicAuth(arbiIdentity, arbiSecret)
        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java))
        }
        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    @Test
    fun `should return bad request when bank account from request does not exist`() {
        val request = HttpRequest.POST(
            "/arbi/deposit",
            arbiCallbackRequest.copy(
                conta = ArbiCallbackContaRequest(
                    banco = bankNo,
                    agencia = routingNo + routingDv,
                    conta = "**********",
                ),
            ),
        )
            .basicAuth(arbiIdentity, arbiSecret)
        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java))
        }
        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    @Test
    fun `should return no content on successful deposit validation`() {
        every {
            bankAccountService.getStatement(any(), any(), any(), any())
        } returns BankStatement(listOf())

        val request = HttpRequest.POST("/arbi/deposit", arbiCallbackRequest)
            .basicAuth(arbiIdentity, arbiSecret)
        val response = client.toBlocking().exchange(request, Argument.of(String::class.java))

        verify(exactly = 1) {
            bankAccountService.getStatement(
                AccountNumber(accountNo.toBigInteger(), accountDv),
                "***********",
                any(),
                any(),
            )
        }

        response.status shouldBe HttpStatus.NO_CONTENT
    }

    @ParameterizedTest
    @MethodSource("incompletedArbiCallbackRequests")
    fun `não deve salvar a última conta usada para cash-in se algum campo vier vazio`(incompletedArbiCallbackRequest: ArbiCallbackRequest) {
        every {
            bankAccountService.getStatement(any(), any(), any(), any())
        } returns BankStatement(listOf())

        val request = HttpRequest.POST("/arbi/deposit", incompletedArbiCallbackRequest)
            .basicAuth(arbiIdentity, arbiSecret)
        val response = client.toBlocking().exchange(request, Argument.of(String::class.java))

        response.status shouldBe HttpStatus.NO_CONTENT

        verify(exactly = 0) {
            externalBankAccountService.saveLastUsed(any())
        }
    }

    @ParameterizedTest
    @EnumSource(ArbiPixNotificationType::class, mode = EnumSource.Mode.EXCLUDE, names = ["MOVIMENTO"])
    fun `não deve processar mensagens diferente do tipo MOVIMENTO`(tipoNotificacao: ArbiPixNotificationType) {
        val request = HttpRequest.POST(
            "/arbi/deposit-pix",
            """
            {
                "cpfCnpj": "***********",
                "tipoNotificacao": "${tipoNotificacao.name}",
                "mensagem": {
                    "bancoorigem": "",
                    "agenciaorigem": "",
                    "contaorigem": "",
                    "inscricao": "",
                    "nome": ""
                }
            }
            """.trimIndent(),
        )
            .basicAuth(arbiIdentity, arbiSecret)

        val response = client.toBlocking().exchange(request, Argument.of(String::class.java))

        response.status shouldBe HttpStatus.NO_CONTENT

        verify {
            bankAccountService wasNot called
            externalBankAccountService wasNot called
        }
    }

    @Test
    fun `deve sincronizar um crédito de PIX com os dados do callback`() {
        val arbiPixCallbackRequest = ArbiPixMovementCallbackRequest(
            cpfCnpj = DOCUMENT,
            mensagem = mensagem,
        )

        val request = HttpRequest.POST("/arbi/deposit-pix", arbiPixCallbackRequest).basicAuth(arbiIdentity, arbiSecret)
        client.toBlocking().exchange(request, Argument.of(String::class.java)).status shouldBe HttpStatus.NO_CONTENT

        val bankStatementItemSlot = slot<DefaultBankStatementItem>()
        verify {
            internalBankService.synchronizePixBankStatementItem(
                bankNo = any(),
                routingNo = any(),
                accountNumber = any(),
                bankStatementItem = capture(bankStatementItemSlot),
            )
        }
        with(bankStatementItemSlot.captured) {
            assertSoftly {
                flow shouldBe BankStatementItemFlow.CREDIT
                type shouldBe BankStatementItemType.PIX
                description shouldBe "teste"
                operationNumber shouldBe "*********"
                amount shouldBe 99900
                counterpartName shouldBe "Joe Montana"
                counterpartDocument shouldBe "***********"
                documentNumber shouldBe "***********"
                ref shouldBe "NuC6M4J5HUf4AjEV361GAr"
            }
        }
    }

    @Test
    fun `não deve sincronizar a conta para mensagens do tipo MOVIMENTO de natureza DÉBITO`() {
        every {
            bankAccountService.getStatement(any(), any(), any(), any())
        } returns BankStatement(listOf())

        val arbiPixCallbackRequest = ArbiPixMovementCallbackRequest(
            cpfCnpj = DOCUMENT,
            mensagem = mensagem.copy(naturezaMovimento = ArbiPixMovementNature.D),
        )

        val request = HttpRequest.POST("/arbi/deposit-pix", arbiPixCallbackRequest)
            .basicAuth(arbiIdentity, arbiSecret)
        val response = client.toBlocking().exchange(request, Argument.of(String::class.java))

        response.status shouldBe HttpStatus.NO_CONTENT

        verify {
            bankAccountService wasNot called
        }
    }

    @ParameterizedTest
    @EnumSource(
        ArbiPixMovementType::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["CREDITO_EXTERNO"],
    )
    fun `deve salvar os dados de depósito apenas para tipo de mensagem CREDITO_EXTERNO`(tipoMovimento: ArbiPixMovementType) {
        every {
            bankAccountService.getStatement(any(), any(), any(), any())
        } returns BankStatement(listOf())

        val arbiPixCallbackRequest = ArbiPixMovementCallbackRequest(
            cpfCnpj = DOCUMENT,
            mensagem = mensagem.copy(
                tipo = ArbiPixMovementType.CREDITO_EXTERNO,
                parte = parte.copy(cpfCnpj = "***********"),
            ),
        )

        val request = HttpRequest.POST("/arbi/deposit-pix", arbiPixCallbackRequest)
            .basicAuth(arbiIdentity, arbiSecret)
        val response = client.toBlocking().exchange(request, Argument.of(String::class.java))

        response.status shouldBe HttpStatus.NO_CONTENT

        val slot = slot<ExternalBankAccount>()
        verify {
            externalBankAccountService.saveLastUsed(capture(slot))
        }

        with(slot.captured) {
            this.document.value shouldBe contraParte.cpfCnpj
            this.bankISPB shouldBe contraParte.codIspb
            this.routingNo shouldBe contraParte.codAgencia.toLong()
            this.accountNo shouldBe contraParte.nroConta.dropLast(1).toLong()
            this.accountDv shouldBe contraParte.nroConta.takeLast(1)
        }
    }

    @Test
    fun `não deve salvar os dados de depósito quando não for um cashin para a própria titularidade`() {
        every {
            bankAccountService.getStatement(any(), any(), any(), any())
        } returns BankStatement(listOf())

        val arbiPixCallbackRequest = ArbiPixMovementCallbackRequest(
            cpfCnpj = DOCUMENT,
            mensagem = mensagem.copy(tipo = ArbiPixMovementType.CREDITO_EXTERNO),
        )

        val request = HttpRequest.POST("/arbi/deposit-pix", arbiPixCallbackRequest)
            .basicAuth(arbiIdentity, arbiSecret)
        val response = client.toBlocking().exchange(request, Argument.of(String::class.java))

        response.status shouldBe HttpStatus.NO_CONTENT

        verify(exactly = 0) {
            externalBankAccountService.saveLastUsed(any())
        }
    }

    @Test
    fun `deve salvar os dados de depósito quando for um cashin para a para a própria titularidade`() {
        every {
            bankAccountService.getStatement(any(), any(), any(), any())
        } returns BankStatement(listOf())

        val arbiPixCallbackRequest = ArbiPixMovementCallbackRequest(
            cpfCnpj = DOCUMENT,
            mensagem = mensagem.copy(
                tipo = ArbiPixMovementType.CREDITO_EXTERNO,
                parte = parte.copy(cpfCnpj = "***********"),
                contraParte = contraParte.copy(cpfCnpj = "***********"),
            ),
        )

        val request = HttpRequest.POST("/arbi/deposit-pix", arbiPixCallbackRequest)
            .basicAuth(arbiIdentity, arbiSecret)
        val response = client.toBlocking().exchange(request, Argument.of(String::class.java))

        response.status shouldBe HttpStatus.NO_CONTENT

        verify {
            externalBankAccountService.saveLastUsed(any())
        }
    }

    @Test
    fun `deve completar o cpf com zero a esquerda`() {
        val document = "1111111"

        val externalBankAccount = ArbiPixPart(
            codIspb = "000000",
            codAgencia = "1",
            digitoAgencia = null,
            nroConta = "123",
            tipoConta = "CACC",
            cpfCnpj = document,
            nome = "mocked name",
            nomeInstituicao = null,
        ).toExternalBankAccount()

        externalBankAccount.document.value shouldBe "***********"
    }

    @ParameterizedTest
    @EnumSource(
        ArbiPixMovementType::class,
        mode = EnumSource.Mode.EXCLUDE,
        names = [
            "CREDITO_EXTERNO",
            "CREDITO_EXTERNO_NAO_FINALIZADO",
            "DEBITO_INTERNO",
            "DEBITO_EXTERNO",
            "DEBITO_DEVOLUCAO",
            "DEBITO_EXTERNO_NAO_FINALIZADO",
            "DEBITO_INTERNO_NAO_FINALIZADO",
            "DEBITO_DEVOLUCAO_NAO_FINALIZADO",
            "DEBITO_EXTERNO_CANCELADO",
            "DEBITO_INTERNO_CANCELADO",
            "DEBITO_DEVOLUCAO_CANCELADO",
        ],
    )
    fun `não deve salvar os dados de depósito para tipos de mensagem diferentes de CREDITO_EXTERNO_NAO_FINALIZADO, CREDITO_EXTERNO`(
        tipoMovimento: ArbiPixMovementType,
    ) {
        every {
            bankAccountService.getStatement(any(), any(), any(), any())
        } returns BankStatement(listOf())

        val arbiPixCallbackRequest = ArbiPixMovementCallbackRequest(
            cpfCnpj = DOCUMENT,
            mensagem = mensagem.copy(tipo = tipoMovimento),
        )

        val request = HttpRequest.POST("/arbi/deposit-pix", arbiPixCallbackRequest)
            .basicAuth(arbiIdentity, arbiSecret)
        val response = client.toBlocking().exchange(request, Argument.of(String::class.java))

        response.status shouldBe HttpStatus.NO_CONTENT

        verify {
            externalBankAccountService wasNot called
        }
    }

    @Test
    fun `deve retornar NO_CONTENT para payload com campos nulos e desconhecidos`() {
        val request = HttpRequest.POST(
            "/arbi/deposit-pix",
            """	{"cpfCnpj":"**********9","tipoNotificacao":"MOVIMENTO","mensagem":{"parte":{"codIspb":"********","codAgencia":"0001","digitoAgencia":"9","nroConta":"**********","tipoConta":"CACC","cpfCnpj":"**********9","nome":"GERALDO TESTE","nomeInstituicao":"BCO ARBI S.A."},"contraParte":{"codIspb":"********","codAgencia":"1","nroConta":"1234566","tipoConta":"TRAN","cpfCnpj":"***********","nome":"Débora TESTE","nomeInstituicao":"NU PAGAMENTOS - IP"},"endToEnd":"E12345563202207221926k58knjx000g","tipo":"DEBITO_EXTERNO_CANCELADO","valor":1200,"reasons":[{"code":"PIXAB03"}],"dataHoraMovimento":"2022-07-22T16:26:04.063","dataHoraSolicitacao":"2022-07-22T16:26:03.52","chave":"***********","nroMovimento":"0","origemMovimento":"CHAVE","naturezaMovimento":"D","indDevolucao":false,"campoLivre":"","codOperacao":"00265","infoErro":"Controle de timeout no SPI / Extrapolação do limite de tempo de timeout no PSP do Recebedor.","ehAgendado":false,"idIdempotente":"OPERATION-********-49ff-00bf-a8d1-9b7546ea07fe","indExterno":true,"finalidadeDaTransacao":"IPAY"}}	""".trimIndent(),
        )
            .basicAuth(arbiIdentity, arbiSecret)

        val response = client.toBlocking().exchange(request, Argument.of(String::class.java))

        response.status shouldBe HttpStatus.NO_CONTENT

        verify {
            bankAccountService wasNot called
            externalBankAccountService wasNot called
        }
    }

    companion object {
        private const val bankNo = "213"
        private const val routingNo = "0001"
        private const val routingDv = "9"
        private const val accountNo = "*********"
        private const val accountDv = "0"
        private const val document = "***********"

        private val arbiCallbackRequest = ArbiCallbackRequest(
            valor = 100,
            codHistorico = "00056",
            descricao = "**********.00",
            bancoorigem = "213",
            agenciaorigem = "1234",
            contaorigem = "23231",
            inscricao = "***********",
            nome = "",
            conta = ArbiCallbackContaRequest(
                banco = bankNo,
                agencia = routingNo + routingDv,
                conta = accountNo + accountDv,
            ),
        )

        @JvmStatic
        fun incompletedArbiCallbackRequests(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(arbiCallbackRequest.copy(inscricao = "")),
                Arguments.arguments(arbiCallbackRequest.copy(bancoorigem = "")),
                Arguments.arguments(arbiCallbackRequest.copy(contaorigem = "")),
                Arguments.arguments(arbiCallbackRequest.copy(agenciaorigem = "")),
            )
        }
    }
}