package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.adapters.messaging.DDABillTO
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.BillRegisterData
import ai.friday.billpayment.app.payment.BillValidationStatus
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.integration.DEPENDENT_DOCUMENT_2
import ai.friday.billpayment.integration.DOCUMENT_2
import ai.friday.billpayment.integration.DOCUMENT_3
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import ai.friday.billpayment.integration.SANITATION_BARCODE_LINE
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import org.junit.jupiter.api.Test

internal class ArbiValidationResponseTest {
    @Test
    fun `deve estar marcado como pago por fora caso tenha um amount paid apesar de estar aberto`() {
        val validationResponse = ArbiValidationResponse(
            billRegisterData = BillRegisterData(
                billType = BillType.CONCESSIONARIA,
                assignor = "",
                amount = 0,
                discount = 0,
                interest = 0,
                fine = 0,
                amountTotal = 0,
                amountCalculationModel = AmountCalculationModel.UNKNOWN,
                amountPaid = 123,
                payerDocument = null,
                recipientChain = null,

                divergentPayment = null,
            ),
            paymentStatus = CIPSitTitPgto.SUCESSO.code,
        )
        validationResponse.alreadyPaid() shouldBe true
        validationResponse.getStatus() shouldBe BillValidationStatus.AlreadyPaid
    }

    @Test
    fun `deve estar marcado como aberto caso não tenha um amount paid`() {
        val validationResponse = ArbiValidationResponse(
            billRegisterData = BillRegisterData(
                billType = BillType.CONCESSIONARIA,
                assignor = "",
                amount = 0,
                discount = 0,
                interest = 0,
                fine = 0,
                amountTotal = 0,
                amountCalculationModel = AmountCalculationModel.UNKNOWN,
                payerDocument = null,
                recipientChain = null,

                divergentPayment = null,
            ),
            paymentStatus = CIPSitTitPgto.SUCESSO.code,
        )
        validationResponse.alreadyPaid() shouldBe false
        validationResponse.getStatus() shouldBe BillValidationStatus.Payable
    }

    @Test
    fun `deve considerar que a resposta é antiga caso o número de cadastro do título for maior`() {
        val validationResponse = ArbiValidationResponse(
            billRegisterData = BillRegisterData(
                billType = BillType.FICHA_COMPENSACAO,
                assignor = "",
                amount = 0,
                discount = 0,
                interest = 0,
                fine = 0,
                amountTotal = 0,
                amountCalculationModel = AmountCalculationModel.UNKNOWN,
                payerDocument = null,
                recipientChain = null,
                registrationUpdateNumber = 10,

                divergentPayment = null,
            ),
            paymentStatus = CIPSitTitPgto.SUCESSO.code,
        )
        validationResponse.isRegistrationResponseOlderThan(11).shouldBeTrue()
    }

    @Test
    fun `não deve considerar que a resposta é antiga caso o número de cadastro do título for menor`() {
        val validationResponse = ArbiValidationResponse(
            billRegisterData = BillRegisterData(
                billType = BillType.FICHA_COMPENSACAO,
                assignor = "",
                amount = 0,
                discount = 0,
                interest = 0,
                fine = 0,
                amountTotal = 0,
                amountCalculationModel = AmountCalculationModel.UNKNOWN,
                payerDocument = null,
                recipientChain = null,
                registrationUpdateNumber = 10,

                divergentPayment = null,
            ),
            paymentStatus = CIPSitTitPgto.SUCESSO.code,
        )
        validationResponse.isRegistrationResponseOlderThan(9).shouldBeFalse()
    }

    @Test
    fun `não deve considerar que a resposta é antiga caso o número de cadastro do título for igual`() {
        val validationResponse = ArbiValidationResponse(
            billRegisterData = BillRegisterData(
                billType = BillType.FICHA_COMPENSACAO,
                assignor = "",
                amount = 0,
                discount = 0,
                interest = 0,
                fine = 0,
                amountTotal = 0,
                amountCalculationModel = AmountCalculationModel.UNKNOWN,
                payerDocument = null,
                recipientChain = null,
                registrationUpdateNumber = 10,

                divergentPayment = null,
            ),
            paymentStatus = CIPSitTitPgto.SUCESSO.code,
        )
        validationResponse.isRegistrationResponseOlderThan(10).shouldBeFalse()
    }

    @Test
    internal fun `não deve marcar boleto como pago caso uma baixa efetiva venha zerada e o boleto já estiver baixado`() {
        val response = """
            
            {
"numidentcdda": 2022101301424541591,
"ultnumrefcadtit": 1666951196438001028,
"ultnumseqcadtit": 1,
"codifcedente": 212,
"tipopescedenteori": "J",
"cnpj_cpf_cedenteori": "$DOCUMENT_2",
"nomecedenteori": "BANCO ORIGINAL SA",
"nomefantasiacedenteori": "BANCO ORIGINAL SA",
"tipopescedente": "J",
"cnpj_cpf_cedente": "$DOCUMENT_3",
"nomecedente": "BANCO ORIGINAL SA",
"nomefantasiacedente": "BANCO ORIGINAL SA",
"tipopessacado": "F",
"cnpj_cpf_sacado": "$DEPENDENT_DOCUMENT_2",
"nomesacado": "JOHN DOE",
"nomefantasiasacado": "",
"tipopessacador": "0",
"cnpj_cpf_sacador": "",
"nomesacador": "",
"identnossonumero": "0001/112/00246596938",
"numcodbarras": "$SANITATION_BARCODE_LINE",
"linhadigitavel": "$FICHA_DE_COMPENSACAO_DIGITABLE_LINE",
"datavencimento": "2022-10-27T00:00:00",
"vlrtitulo": 590.37,
"codespeciedoc": "99",
"dataemissao": "2022-10-13T00:00:00",
"qtdediasprotesto": null,
"datalimpagto": "2022-10-27T00:00:00",
"tipopagto": 3,
"numparcela": null,
"qtdparcela": null,
"indtitulonegociado": "N",
"indbloqueio": "N",
"indparcial": "N",
"vlrabatimento": 0,
"datamora": null,
"codmora": "5",
"vlrpercmora": 0,
"datamulta": null,
"codmulta": "3",
"vlrpercmulta": 0,
"datadesconto01": null,
"coddesconto01": "0",
"vlrpercdesconto01": 0,
"datadesconto02": null,
"coddesconto02": null,
"vlrpercdesconto02": 0,
"datadesconto03": null,
"coddesconto03": null,
"vlrpercdesconto03": 0,
"indvalorperc_min": "",
"vlrmintitulo": 0,
"indvalorperc_max": "",
"vlrmaxtitulo": 0,
"tipocalculo": "01",
"tipoautrecdivergente": "3",
"aceite": null,
"sitpagamento": null,
"sitpagamentocip": "01",
"dthrsittitulo": "2022-10-13T06:35:01",
"datahoradda": "2022-10-13T06:35:32",
"baixasOperacionais": [],
"baixasEfetivas": [
{
"agebcobaixa": null,
"codbcobaixa": null,
"canal": null,
"cnpj_cpf_port": "",
"nome_port": "",
"dataprocbaixa": "2022-10-28T07:00:10",
"meio": null,
"numidentbaixa": 2022102800459717584,
"seqbaixa": 1,
"tpbaixa": 2,
"vlrbaixa": 0
}
]
}
        """.trimIndent()
        val ddaBill = parseObjectFrom<DDABillTO>(response)

        val validationResponse = ddaBill.toValidationResponse()

        validationResponse.alreadyPaid() shouldBe false
        validationResponse.getStatus() shouldBe BillValidationStatus.NotPayable
    }

    private val dateTimeFormat: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")

    private fun DDABillTO.toValidationResponse(): ArbiValidationResponse {
        return ArbiValidationResponse(
            billRegisterData = BillRegisterData(
                billType = BillType.FICHA_COMPENSACAO,
                assignor = "",
                recipient = Recipient(name = ""),
                recipientChain = null,
                payerDocument = formatDocument(this.cnpj_cpf_sacado, this.tipopessacado),
                payerName = this.nomesacado,
                amount = convertToLongOrException(this.vlrtitulo.toString()),
                discount = 0,
                interest = 0,
                fine = 0,
                amountTotal = convertToLongOrException(this.vlrtitulo.toString()),
                expirationDate = LocalDate.parse(this.datalimpagto, dateTimeFormat),
                settleDate = getLocalDate(),
                paymentLimitTime = "20:00",
                dueDate = LocalDate.parse(datavencimento, dateTimeFormat),
                amountCalculationModel = AmountCalculationModel.getByCode(this.tipocalculo),
                fichaCompensacaoType = FichaCompensacaoType.findByCode((this.codespeciedoc.toInt())),
                amountPaid = 0L,
                idNumber = this.numidentcdda.toString(),
                abatement = this.vlrabatimento,
                divergentPayment = null,
            ),
            paymentStatus = this.sitpagamentocip.toInt(),
            resultado = ArbiValidationResponse.convertStatusToErrorDescription(this.sitpagamentocip.toInt()),
        )
    }
}