package ai.friday.billpayment.adapters.arbi

import io.kotest.matchers.shouldBe
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class ArbiAdapterValuesConvertionTest {
    @ParameterizedTest
    @CsvSource(value = ["1.00000,1.00", "1.234444,1.23", "1.9,1.90", "0.0,0.00", "0,0.00", "1000000.0,1000000.00", "1232454.**********,1232454.93"])
    fun convertToStringTest(value: Double, stringExpected: String) {
        convertToString(value) shouldBe stringExpected
    }
}