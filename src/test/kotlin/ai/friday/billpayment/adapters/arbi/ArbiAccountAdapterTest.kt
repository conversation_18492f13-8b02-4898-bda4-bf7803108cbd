package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Address
import ai.friday.billpayment.app.account.DocumentInfo
import ai.friday.billpayment.app.account.ExternalAccount
import ai.friday.billpayment.app.account.ExternalRegisterStatus
import ai.friday.billpayment.app.account.Gender
import ai.friday.billpayment.app.account.MonthlyIncome
import ai.friday.billpayment.app.account.PoliticallyExposed
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.stripAccents
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldNotContain
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponseFactory
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import io.reactivex.Flowable
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class ArbiAccountAdapterTest {
    private val customer = ExternalAccount(
        documentInfo = DocumentInfo(
            name = "Ze das Couves",
            cpf = "***********",
            birthDate = LocalDate.parse("08/11/1975", DateTimeFormatter.ofPattern("dd/MM/yyyy")),
            fatherName = "Pai do Ze",
            motherName = "Mae do Ze",
            rg = "*********",
            docType = DocumentType.of("RG"),
            cnhNumber = "123456",
            orgEmission = "DETRAN",
            expeditionDate = LocalDate.parse("09/09/1999", DateTimeFormatter.ofPattern("dd/MM/yyyy")),
            birthCity = "Rio de Janeiro",
            birthState = "RJ",
        ),
        address = Address(
            streetType = "Rua",
            streetName = "Domingos Ferreira",
            number = "325",
            complement = "ap 11",
            neighborhood = "Copacafona",
            city = "Rio de Janeiro",
            state = "RJ",
            zipCode = "20000-000",
        ),
        email = "<EMAIL>",
        politicallyExposed = PoliticallyExposed(selfDeclared = false, query = null),
        mobilePhone = MobilePhone("+*************"),
        monthlyIncome = MonthlyIncome(lowerBound = 0, upperBound = null),
        calculatedGender = Gender.F,
    )

    private val mockClient: RxHttpClient = mockk()
    private val configuration: ArbiConfiguration = mockk(relaxed = true)

    private val adapter = ArbiAccountAdapter(
        newGWhttpClient = mockClient,
        configuration = configuration,
        newGWAuthenticationManager = NewArbiAuthenticationManager(
            httpClient = mockClient,
            arbiConfiguration = configuration,
        ),
    )

    private fun setupArbiTokenResponse() {
        every {
            mockClient.retrieve(
                any<HttpRequest<GrantCodeTO>>(),
                Argument.of(GrantCodeResponseTO::class.java),
            )
        } answers {
            Flowable.just(GrantCodeResponseTO("http://localhost/?code=XXXX_GRANT_CODE_XXXX"))
        }

        every {
            mockClient.retrieve(
                any<HttpRequest<Map<String, String>>>(),
                Argument.of(AccessTokenTO::class.java),
            )
        } answers {
            Flowable.just(
                AccessTokenTO(
                    accessToken = "XXXX_ACCESS_TOKEN_XXXX",
                    refreshToken = "XXXX_REFRESH_TOKEN_XXXX",
                    expiresIn = 365,
                    tokenType = "ACCESS_TOKEN",
                ),
            )
        }
    }

    @Test
    fun `should return account number and account dv on success`() {
        setupArbiTokenResponse()
        setupArbiSuccessResponse(resultado = "{\"codcliente\":\"123\",\"contadepositovista\":\"123456\",\"resultado\":\"Sucesso\"}")

        val response = adapter.registerNaturalPerson(customer)

        response.success shouldBe true
        response.accountNumber shouldBe 12345L
        response.accountDv shouldBe "6"
    }

    @Test
    fun `should truncate neighborhood on 20 chars`() {
        val neighborhoodWithLongName = "BAIRRO' COM NOME MUI'TO LONGO e ASPAS SIMPLES'"
        val neighborhoodWithTruncatedName = "BAIRRO COM NOME MUIT"
        val targetCustomer = customer.copy(address = customer.address.copy(neighborhood = neighborhoodWithLongName))
        val request = targetCustomer.toRegisterNaturalPerson(configuration)
        request.bairro shouldBe neighborhoodWithTruncatedName
        request.bairro.length shouldBe 20
    }

    @Test
    fun `deve remover aspas simples`() {
        val invalidCustomer = customer.copy(
            documentInfo = customer.documentInfo.copy(
                name = "Ze d'as Couves",
                fatherName = "Pai d'o Ze",
                motherName = "Mae do Ze d'as Couves",
                birthCity = "Rio d'as Ostras",
            ),
            address = customer.address.copy(
                streetName = "Domingos Ferreira d'Avila d'Agua",
                complement = "ap' 11",
                neighborhood = "Copac'afona",
                city = "Rio 'de' Janeiro",
            ),
        )

        val request = invalidCustomer.toRegisterNaturalPerson(configuration)
        assertSoftly {
            request.bairro shouldNotContain "'"
            request.cidade shouldNotContain "'"
            request.complemento shouldNotContain "'"
            request.endereco shouldNotContain "'"
            request.naturalidade shouldNotContain "'"
            request.nome shouldNotContain "'"
            request.nomepai shouldNotContain "'"
            request.nomemae shouldNotContain "'"
        }
    }

    @Test
    fun `should send rg and received org emission when document type is RG`() {
        val request = customer.copy(
            documentInfo = customer.documentInfo.copy(
                rg = "1111111111",
                docType = DocumentType.RG,
                cnhNumber = null,
                orgEmission = "SSP",
            ),
        ).toRegisterNaturalPerson(configuration)

        request.numeroident shouldBe "1111111111"
        request.orgaoident shouldBe "SSP"
    }

    @Test
    fun `should send "DETRAN" as org emission and cnhNumber when document type is CNH`() {
        val request = customer.copy(
            documentInfo = customer.documentInfo.copy(
                rg = "1111111111",
                docType = DocumentType.CNH,
                cnhNumber = "000000000",
                orgEmission = "SSP",
            ),
        ).toRegisterNaturalPerson(configuration)

        request.numeroident shouldBe "000000000"
        request.orgaoident shouldBe "DETRAN"
    }

    @Test
    fun `deve enviar DETRAN como orgão emissor quando o orgão for detran + uf`() {
        val request = customer.copy(
            documentInfo = customer.documentInfo.copy(
                rg = "1111111111",
                docType = DocumentType.RG,
                cnhNumber = "000000000",
                orgEmission = "detran/rj",
            ),
        ).toRegisterNaturalPerson(configuration)

        request.numeroident shouldBe "1111111111"
        request.orgaoident shouldBe "DETRAN"
    }

    @Test
    fun `should return success false on register natural person error`() {
        setupArbiTokenResponse()

        every {
            mockClient.retrieve(
                ofType(HttpRequest::class),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.of(String::class.java),
            )
        } answers {
            Flowable.error(
                HttpClientResponseException(
                    "erro",
                    HttpResponseFactory.INSTANCE.status<Any>(HttpStatus.BAD_REQUEST).body(""),
                ),
            )
        }

        val response = adapter.registerNaturalPerson(customer)

        response.success shouldBe false
    }

    private fun setupArbiSuccessResponse(resultado: String) {
        every {
            mockClient.retrieve(
                ofType(HttpRequest::class),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.of(String::class.java),
            )
        } answers {
            Flowable.just(
                listOf(
                    ArbiResponseTO(
                        idTransacao = 1,
                        resultado = resultado,
                        idStatus = 200,
                        idRequisicaoParceiro = "b083da37-9dd0-4dec-88b2-6739795ecc86",
                        idModulo = 4,
                        idRequisicaoArbi = "30920211832130614@165**********",
                        descricaoStatus = "Sucesso",
                    ),
                ),
            )
        }
    }

    @Test
    fun `should return success on simple register natural person`() {
        setupArbiTokenResponse()
        setupArbiSuccessResponse(resultado = "{\"codcliente\":\"123\",\"contadepositovista\":\"123456\",\"resultado\":\"Sucesso\"}")

        val response = adapter.simpleRegisterNaturalPerson(customer)

        response.success shouldBe true
        response.accountNumber shouldBe 12345L
        response.accountDv shouldBe "6"
        val slot = slot<HttpRequest<ArbiAccountRequest>>()
        verify {
            mockClient.retrieve(
                capture(slot),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.of(String::class.java),
            )
        }
        with(slot.captured.body.get()) {
            assertSoftly {
                cadastropf.tipoident shouldBe "CPF"
                cadastropf.numeroident shouldBe customer.documentInfo.cpf
                cadastropf.orgaoident shouldBe "SRF"
                cadastropf.uforgaoident shouldBe ""
                cadastropf.dataident shouldBe ""
                cadastropf.tipoproduto shouldBe "24"
            }
        }
    }

    @ParameterizedTest
    @CsvSource(
        value = ["CANCELADO,null", "APROVADO,APPROVED", "REPROVADO,REJECTED", "PROPOSTA NAO CHEGOU AO KYC. ENVIAR NOVAMENTE,null", "Em analise,null"],
        nullValues = ["null"],
    )
    fun `should return success ou external register status query`(situacao: String, status: ExternalRegisterStatus?) {
        setupArbiTokenResponse()
        setupArbiSuccessResponse(resultado = "{\"cpf\": \"79789145772\", \"statuskyc\": \"$situacao\", \"nroconta\": \"\"}")

        val response = adapter.queryExternalRegisterStatus(document = "79789145772", name = "ARTEINA GERSON PIRES")

        response shouldBe status
    }

    @Test
    fun `should return success false on external register status query error`() {
        setupArbiTokenResponse()

        every {
            mockClient.retrieve(
                ofType(HttpRequest::class),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.of(String::class.java),
            )
        } answers {
            Flowable.error(
                HttpClientResponseException(
                    "erro",
                    HttpResponseFactory.INSTANCE.status<Any>(HttpStatus.BAD_REQUEST).body(""),
                ),
            )
        }

        val response = adapter.queryExternalRegisterStatus(document = "79789145772", name = "ARTEINA GERSON PIRES")

        response shouldBe null
    }

    @ParameterizedTest
    @CsvSource(value = ["Xênia,Xenia", "João,Joao", "Emílio,Emilio", "Graça,Graca", "Niña,Nina", "Àvila,Avila"])
    fun `should replace diacritical characters`(originalValue: String, sanitizedValue: String) {
        originalValue.stripAccents() shouldBe sanitizedValue
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "100001;200000;2000",
            "200001;300010;3000.1",
            "000000;300000;3000",
            "2000001;null;20000.02",
        ],
        nullValues = ["null"],
        delimiter = ';',
    )
    fun `should format monthly income`(lowerBound: Long, upperBound: Long?, expectedValue: String) {
        val monthlyIncome = MonthlyIncome(lowerBound, upperBound)

        val formatedValue = monthlyIncome.toRenda()

        formatedValue shouldBe expectedValue
    }
}