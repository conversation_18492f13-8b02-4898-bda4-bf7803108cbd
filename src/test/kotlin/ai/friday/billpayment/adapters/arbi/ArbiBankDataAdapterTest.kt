package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import org.junit.jupiter.api.Test

class ArbiBankDataAdapterTest {

    @Test
    fun `deve converter o payload do Arbi corretamente`() {
        val payload = """"{\"codErro\":null,\"descErro\":null,\"stringBase64\":\"UVVBTFFVRVJfVkFMT1JfUVVFX0RFVkVSSUFfRVNUQVJfRU1fQkFTRTY0\"}""""
        val parsed = parseObjectFrom<String>(payload)
        val parsed2 = parseObjectFrom<ArbiInformeDeRendimentosResponseTO>(parsed)
        parsed2.codErro.shouldBeNull()
        parsed2.descErro.shouldBeNull()
        parsed2.stringBase64.shouldNotBeNull()
    }
}