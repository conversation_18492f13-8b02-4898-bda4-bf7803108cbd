/*
package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyException
import ai.friday.billpayment.app.pix.PixKeyType
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponseFactory
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.reactivex.Flowable
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
@Property(name = "integrations.arbi.findPixKeyPath", value = "stuff")
@Disabled("o Micronaut esta tentando fechar o httpclient e dando BeanDestructionException")
internal class ArbiPixKeyManagementAdapterIntegrationTest(val arbiPixKeyManagementAdapter: NewArbiPixKeyManagementAdapter) {

    @MockBean(RxHttpClient::class)
    fun httpClient() = mockClient
    private val mockClient: RxHttpClient = mockk(relaxed = true)

    @MockBean(ArbiAuthenticationManager::class)
    fun arbiAuthenticationManager() = authenticationManager
    private val authenticationManager: ArbiAuthenticationManager = mockk() {
        every {
            getToken()
        } returns "fakeToken"
    }

    private val responseMock = KeyDetailsResponseTO(
        agencia = "4444",
        conta = "12345",
        cpfCnpj = "",
        instituicao = "",
        tipoConta = "SVGS",
        confirmado = true,
        cid = "",
        nome = "",
        tipoPessoa = "",
        chave = "11111111111",
        tipoChave = "CPF",
        dataCriacao = "",
        dataPosse = "",
        endToEnd = "",
        nomePsp = "",
        dataAbertura = "",
        estatisticas = "",
    )

    @Test
    fun `should use cache when same call is made`() {
        every {
            mockClient.retrieve(
                ofType(HttpRequest::class),
                Argument.listOf(ResponseTO::class.java),
                Argument.listOf(ResponseTO::class.java),
            )
        } answers {
            Flowable.just(
                listOf(
                    ResponseTO(
                        response = jacksonObjectMapper().writeValueAsString(responseMock),
                        status = 200,
                    ),
                ),
            )
        }

        arbiPixKeyManagementAdapter.findKeyDetailsCacheable(
            key = PixKey(value = "11111111111", type = PixKeyType.CPF),
            document = "22222222222",
        )
        arbiPixKeyManagementAdapter.findKeyDetailsCacheable(
            key = PixKey(value = "11111111111", type = PixKeyType.CPF),
            document = "22222222222",
        )
        arbiPixKeyManagementAdapter.findKeyDetailsCacheable(
            key = PixKey(value = "22222222222", type = PixKeyType.CPF),
            document = "22222222222",
        )

        verify(exactly = 2) {
            mockClient.retrieve(
                ofType(HttpRequest::class),
                Argument.listOf(ResponseTO::class.java),
                Argument.listOf(ResponseTO::class.java),
            )
        }
    }

    @Test
    fun `should not use cache if first call fails`() {
        every {
            mockClient.retrieve(
                ofType(HttpRequest::class),
                Argument.listOf(ResponseTO::class.java),
                Argument.listOf(ResponseTO::class.java),
            )
        } answers {
            Flowable.error(
                HttpClientResponseException(
                    "erro",
                    HttpResponseFactory.INSTANCE.status<Any>(HttpStatus.NOT_FOUND).body(
                        listOf(
                            ResponseTO(
                                "{\"erroDict\":\"NOT_FOUND\",\"codErro\":404,\"descricaoErro\":\"Entry not found\"}",
                                412,
                            ),
                        ),
                    ),
                ),
            )
        } andThenAnswer {
            Flowable.just(
                listOf(
                    ResponseTO(
                        response = jacksonObjectMapper().writeValueAsString(responseMock),
                        status = 200,
                    ),
                ),
            )
        }

        assertThrows<PixKeyException> {
            arbiPixKeyManagementAdapter.findKeyDetailsCacheable(
                key = PixKey(
                    value = "33333333333",
                    type = PixKeyType.CPF,
                ),
                document = "22222222222",
            )
        }
        arbiPixKeyManagementAdapter.findKeyDetailsCacheable(
            key = PixKey(value = "33333333333", type = PixKeyType.CPF),
            document = "22222222222",
        )

        verify(exactly = 2) {
            mockClient.retrieve(
                ofType(HttpRequest::class),
                Argument.listOf(ResponseTO::class.java),
                Argument.listOf(ResponseTO::class.java),
            )
        }
    }
}*/