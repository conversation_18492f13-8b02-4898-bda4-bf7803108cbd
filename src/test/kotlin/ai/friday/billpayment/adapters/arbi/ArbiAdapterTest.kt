package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.adapters.parsers.parseListFrom
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.Bank
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.BillValidationStatus
import ai.friday.billpayment.app.payment.DirectTEDStatus
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.payment.PartialPaymentAmountType
import ai.friday.billpayment.bankAccount
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.DOCUMENT_2
import ai.friday.billpayment.integration.DOCUMENT_3
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_BARCODE
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import ai.friday.billpayment.withBankList
import ai.friday.billpayment.withHolidays
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.withGivenDateTime
import com.fasterxml.jackson.core.json.JsonReadFeature
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpResponseFactory
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.client.exceptions.ReadTimeoutException
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.mockk.CapturingSlot
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.reactivex.Flowable
import java.text.DecimalFormat
import java.text.NumberFormat
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeFormatter.ISO_DATE
import java.util.Locale
import java.util.UUID
import java.util.stream.Stream
import kotlin.random.Random
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.Arguments.arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource

class ArbiAdapterTest {

    private val mockClient: RxHttpClient = mockk()

    private val configuration = ArbiConfiguration().apply {
        newHost = ""
        host = ""
        grantCodePath = ""
        accessTokenPath = ""
        validatePath = ""
        checkingPath = ""
        checkingV2Path = ""
        getStatementV2Path = ""
        ddaV2Path = ""
        domainPath = ""
        contaCashin = ""
        userToken = ""
        clientId = ""
        clientSecret = ""
        contaTitular = "8745632"
        inscricao = "12345678000123"
        tipoPessoa = "J"
        ddaCadastroPath = ""
        paymentTimeLimit = "18:00"
    }

    private val arbiAdapter =
        ArbiAdapter(
            httpClient = mockClient,
            configuration = configuration,
            authenticationManager = mockk(relaxed = true),
            fallbackCheckTransfer = true,
        ).apply {
            paymentTimeLimit = "22:00"
        }

    private val arbiTedAdapter = ArbiTedAdapter(
        httpClient = mockClient,
        configuration = configuration,
        authenticationManager = mockk(relaxed = true),
    )

    @BeforeEach
    fun setup() {
        every {
            mockClient.retrieve(
                any<HttpRequest<GrantCodeTO>>(),
                Argument.of(GrantCodeResponseTO::class.java),
            )
        } answers {
            Flowable.just(GrantCodeResponseTO("http://localhost/?code=XXXX_GRANT_CODE_XXXX"))
        }

        every {
            mockClient.retrieve(
                any<HttpRequest<Map<String, String>>>(),
                Argument.of(AccessTokenTO::class.java),
            )
        } answers {
            Flowable.just(
                AccessTokenTO(
                    accessToken = "XXXX_ACCESS_TOKEN_XXXX",
                    refreshToken = "XXXX_REFRESH_TOKEN_XXXX",
                    expiresIn = 365,
                    tokenType = "ACCESS_TOKEN",
                ),
            )
        }
    }

    @Test
    fun convertToValidationResponse() {
        val response =
            """[{"DDA0110R1": {"NumCtrlPart":"DDA20200522000341775","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20200522000771184702","NumIdentcTit":"2020042207658657449","NumRefAtlCadTit":"1588742057601000505","NumSeqAtlzCadTit":"2","DtHrSitTit":"2020-05-06T02:14:17","ISPBPartDestinatario":"90400888","CodPartDestinatario":"033","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"**************","Nom_RzSocBenfcrioOr":"ASSOCIACAO BRITANICA DE EDUCACAO","NomFantsBenfcrioOr":"ASSOCIACAO BRITANICA DE EDUCACAO","LogradBenfcrioOr":"R DA MATRIZ 76 86","CidBenfcrioOr":"RIO DE JANEIRO","UFBenfcrioOr":"RJ","CEPBenfcrioOr":"22260100","TpPessoaBenfcrioFinl":"J","CNPJ_CPFBenfcrioFinl":"**************","Nom_RzSocBenfcrioFinl":"ASSOCIACAO BRITANICA DE EDUCACAO","NomFantsBenfcrioFinl":"ASSOCIACAO BRITANICA DE EDUCACAO","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"8650436714","Nom_RzSocPagdr":"OLIVIA PIMENTA DE OLIVEIRA CASTRO","NomFantsPagdr":"OLIVIA PIMENTA DE OLIVEIRA CASTRO","TpIdentcSacdrAvalst":"","IdentcSacdrAvalst":"","Nom_RzSocSacdrAvalst":"","CodMoedaCNAB":"09","NumCodBarras":"03391824600000843809847075800000013008220101","NumLinhaDigtvl":"03399847007580000001230082201010182460000084380","DtVencTit":"2020-05-05","VlrTit":"843.80","CodEspTit":"4","QtdDiaPrott":"","DtLimPgtoTit":"2020-08-12","IndrBloqPgto":"N","IndrPgtoParcl":"N","QtdPgtoParcl":"","VlrAbattTit":"0.00","DtJurosTit":"2020-05-06","CodJurosTit":"1","Vlr_PercJurosTit":"0.28","CodMultaTit":"1","DtMultaTit":"2020-05-06","Vlr_PercMultaTit":"16.88","DtDesctTit":"","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"P","Vlr_PercMinTit":"100.00000","TpVlr_PercMaxTit":"P","Vlr_PercMaxTit":"100.00000","TpModlCalc":"01","TpAutcRecbtVlrDivgte":"3","QtdPgtoParclRegtd":"","VlrSldTotAtlPgtoTit":"","NumIdentcBaixaEft":"2020050500599955100","NumRefAtlBaixaEft":"1588742057257000505","NumSeqAtlzBaixaEft":"1","DtProcBaixaEft":"2020-05-05","DtHrProcBaixaEft":"2020-05-06T00:20:13","VlrBaixaEftTit":"843.80","NumCodBarrasBaixaEft":"03391824600000843809847075800000013008220101","CanPgto":"","MeioPgto":"","DtHrSitBaixaEft":"2020-05-06T02:14:17","SitTitPgto":"12","DtHrDDA":"2020-05-22T15:33:04","DtMovto":"2020-05-22"}},{"ValorCalculado": {"valorTituloOriginal":"843.80","valorTotalCobrar":"865.44","valorDescontoCalculado":"0","valorJurosCalculado":"4.76","valorMultaCalculado":"16.88","valorAbatimento":"0.00","dataOperacao":"22/05/2020"}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BANCO ARBI S.A."}}]"""

        val santander = Bank(number = 33, name = "BANCO SANTANDER (BRASIL) S.A.")

        val validation = withBankList(santander) {
            arbiAdapter.convertToValidationResponse(response)
        }

        assertEquals(86544L, validation.billRegisterData!!.amountTotal)
        assertEquals(LocalDate.parse("2020-05-05", ISO_DATE), validation.billRegisterData!!.dueDate)
        assertEquals(BillValidationStatus.AlreadyPaid, validation.getStatus())
        assertEquals(santander.name, validation.billRegisterData!!.assignor)
        assertEquals("ASSOCIACAO BRITANICA DE EDUCACAO", validation.billRegisterData!!.recipient!!.name)
        assertEquals("**************", validation.billRegisterData!!.recipient!!.document)
        assertEquals(0L, validation.billRegisterData!!.discount)
        assertEquals(LocalDate.parse("2020-08-12", ISO_DATE), validation.billRegisterData!!.expirationDate)
        assertEquals(1688L, validation.billRegisterData!!.fine)
        assertEquals(476L, validation.billRegisterData!!.interest)
        assertEquals("***********", validation.billRegisterData!!.payerDocument)
        assertEquals(84380L, validation.billRegisterData!!.amount)
        assertEquals(84380L, validation.billRegisterData!!.amountPaid)
        assertEquals(AmountCalculationModel.ANYONE, validation.billRegisterData!!.amountCalculationModel)

        validation.billRegisterData!!.fichaCompensacaoType shouldBe FichaCompensacaoType.DS_DUPLICATA_DE_SERVICO
    }

    @Test
    fun convertToCreditCardType() {
        val response =
            """[{"DDA0110R1": {"NumCtrlPart":"DDA20200522000341775","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20200522000771184702","NumIdentcTit":"2020042207658657449","NumRefAtlCadTit":"1588742057601000505","NumSeqAtlzCadTit":"2","DtHrSitTit":"2020-05-06T02:14:17","ISPBPartDestinatario":"90400888","CodPartDestinatario":"033","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"**************","Nom_RzSocBenfcrioOr":"ASSOCIACAO BRITANICA DE EDUCACAO","NomFantsBenfcrioOr":"ASSOCIACAO BRITANICA DE EDUCACAO","LogradBenfcrioOr":"R DA MATRIZ 76 86","CidBenfcrioOr":"RIO DE JANEIRO","UFBenfcrioOr":"RJ","CEPBenfcrioOr":"22260100","TpPessoaBenfcrioFinl":"J","CNPJ_CPFBenfcrioFinl":"**************","Nom_RzSocBenfcrioFinl":"ASSOCIACAO BRITANICA DE EDUCACAO","NomFantsBenfcrioFinl":"ASSOCIACAO BRITANICA DE EDUCACAO","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"8650436714","Nom_RzSocPagdr":"OLIVIA PIMENTA DE OLIVEIRA CASTRO","NomFantsPagdr":"OLIVIA PIMENTA DE OLIVEIRA CASTRO","TpIdentcSacdrAvalst":"","IdentcSacdrAvalst":"","Nom_RzSocSacdrAvalst":"","CodMoedaCNAB":"09","NumCodBarras":"03391824600000843809847075800000013008220101","NumLinhaDigtvl":"03399847007580000001230082201010182460000084380","DtVencTit":"2020-05-05","VlrTit":"843.80","CodEspTit":"31","QtdDiaPrott":"","DtLimPgtoTit":"2020-08-12","IndrBloqPgto":"N","IndrPgtoParcl":"N","QtdPgtoParcl":"","VlrAbattTit":"0.00","DtJurosTit":"2020-05-06","CodJurosTit":"1","Vlr_PercJurosTit":"0.28","CodMultaTit":"1","DtMultaTit":"2020-05-06","Vlr_PercMultaTit":"16.88","DtDesctTit":"","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"P","Vlr_PercMinTit":"100.00000","TpVlr_PercMaxTit":"P","Vlr_PercMaxTit":"100.00000","TpModlCalc":"01","TpAutcRecbtVlrDivgte":"3","QtdPgtoParclRegtd":"","VlrSldTotAtlPgtoTit":"","NumIdentcBaixaEft":"2020050500599955100","NumRefAtlBaixaEft":"1588742057257000505","NumSeqAtlzBaixaEft":"1","DtProcBaixaEft":"2020-05-05","DtHrProcBaixaEft":"2020-05-06T00:20:13","VlrBaixaEftTit":"843.80","NumCodBarrasBaixaEft":"03391824600000843809847075800000013008220101","CanPgto":"","MeioPgto":"","DtHrSitBaixaEft":"2020-05-06T02:14:17","SitTitPgto":"12","DtHrDDA":"2020-05-22T15:33:04","DtMovto":"2020-05-22"}},{"ValorCalculado": {"valorTituloOriginal":"843.80","valorTotalCobrar":"865.44","valorDescontoCalculado":"0","valorJurosCalculado":"4.76","valorMultaCalculado":"16.88","valorAbatimento":"0.00","dataOperacao":"22/05/2020"}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BANCO ARBI S.A."}}]"""
        val validation = arbiAdapter.convertToValidationResponse(response)

        validation.billRegisterData!!.fichaCompensacaoType shouldBe FichaCompensacaoType.CARTAO_DE_CREDITO
    }

    @Test
    fun convertToNuBankCreditCard() {
        val response =
            """[{"DDA0110R1": {"NumCtrlPart":"DDA20220222007889619","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20220222000175619683","NumIdentcTit":"2022020105131025653","NumRefAtlCadTit":"1643772523554000201","NumSeqAtlzCadTit":"1","DtHrSitTit":"2022-02-02T00:28:43","ISPBPartDestinatario":"********","CodPartDestinatario":"237","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"**************","Nom_RzSocBenfcrioOr":"NU PAGAMENTOS S.A.","NomFantsBenfcrioOr":"NU PAGAMENTOS S.A.","LogradBenfcrioOr":"R CAPOTE VALENTE","CidBenfcrioOr":"SAO PAULO","UFBenfcrioOr":"SP","CEPBenfcrioOr":"5409000","TpPessoaBenfcrioFinl":"J","CNPJ_CPFBenfcrioFinl":"","Nom_RzSocBenfcrioFinl":"","NomFantsBenfcrioFinl":"","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"***********","Nom_RzSocPagdr":"VICTOR RIBEIRO MAC MAHON","NomFantsPagdr":"","TpIdentcSacdrAvalst":"","IdentcSacdrAvalst":"","Nom_RzSocSacdrAvalst":"","CodMoedaCNAB":"09","NumCodBarras":"23791889100000295893381260079488711300060920","NumLinhaDigtvl":"23793381286007948871813000609209188910000029589","DtVencTit":"2022-02-09","VlrTit":"295.89","CodEspTit":"1","QtdDiaPrott":"","DtLimPgtoTit":"2022-03-06","IndrBloqPgto":"N","IndrPgtoParcl":"N","QtdPgtoParcl":"","VlrAbattTit":"0.00","DtJurosTit":"","CodJurosTit":"5","Vlr_PercJurosTit":"0.00","CodMultaTit":"3","DtMultaTit":"","Vlr_PercMultaTit":"0.00","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"","Vlr_PercMinTit":"","TpVlr_PercMaxTit":"","Vlr_PercMaxTit":"","TpModlCalc":"01","TpAutcRecbtVlrDivgte":"1","QtdPgtoParclRegtd":"","VlrSldTotAtlPgtoTit":"","SitTitPgto":"12","DtHrDDA":"2022-02-22T11:11:22","DtMovto":"2022-02-22"}},{"ValorCalculado": {"valorTituloOriginal":"295.89","valorTotalCobrar":"295.89","valorDescontoCalculado":"0","valorJurosCalculado":"0","valorMultaCalculado":"0","valorAbatimento":"0.00","dataOperacao":"22/02/2022","codigoerro":"","descricaoerro":""}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BCO ARBI S.A."}}]"""
        val validation = arbiAdapter.convertToValidationResponse(response)

        validation.billRegisterData!!.fichaCompensacaoType shouldBe FichaCompensacaoType.CH_CHEQUE
        validation.billRegisterData!!.recipient!!.document shouldBe "**************"
        validation.billRegisterData!!.recipient!!.name shouldBe "NU PAGAMENTOS S.A."
    }

    @Test
    fun convertToValidationResponseWithCodigoErro() {
        val response =
            """[{"DDA0110R1": {"NumCtrlPart":"DDA20210209001489309","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20210209000517842928","NumIdentcTit":"2021020806507276296","NumRefAtlCadTit":"1612791695803000208","NumSeqAtlzCadTit":"1","DtHrSitTit":"2021-02-08T10:41:35","ISPBPartDestinatario":"********","CodPartDestinatario":"237","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"42498675000152","Nom_RzSocBenfcrioOr":"SEFAZ RJ - IPVA","NomFantsBenfcrioOr":"SEFAZ SECRETARIA DA FAZENDA","LogradBenfcrioOr":"AVENIDA PRESIDENTE VARGAS, 670","CidBenfcrioOr":"RIO DE JANEIRO","UFBenfcrioOr":"RJ","CEPBenfcrioOr":"20071001","TpPessoaBenfcrioFinl":"J","CNPJ_CPFBenfcrioFinl":"42498675000152","Nom_RzSocBenfcrioFinl":"SEFAZ SECRETARIA DA FAZENDA","NomFantsBenfcrioFinl":"SEFAZ SECRETARIA DA FAZENDA","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"2573516717","Nom_RzSocPagdr":"CONTRIBUINTE","NomFantsPagdr":"","TpIdentcSacdrAvalst":"","IdentcSacdrAvalst":"","Nom_RzSocSacdrAvalst":"","CodMoedaCNAB":"09","NumCodBarras":"23794852500001710794600092102671867399999990","NumLinhaDigtvl":"23794600059210267186173999999904485250000171079","DtVencTit":"2021-02-08","VlrTit":"1710.79","CodEspTit":"99","QtdDiaPrott":"","DtLimPgtoTit":"2027-02-07","IndrBloqPgto":"N","IndrPgtoParcl":"N","QtdPgtoParcl":"","VlrAbattTit":"0.00","DtJurosTit":"","CodJurosTit":"5","Vlr_PercJurosTit":"0.00","CodMultaTit":"3","DtMultaTit":"","Vlr_PercMultaTit":"0.00","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"","Vlr_PercMinTit":"","TpVlr_PercMaxTit":"","Vlr_PercMaxTit":"","TpModlCalc":"02","TpAutcRecbtVlrDivgte":"3","QtdPgtoParclRegtd":"","VlrSldTotAtlPgtoTit":"","SitTitPgto":"12","DtHrDDA":"2021-02-09T11:49:01","DtMovto":"2021-02-09"}},{"ValorCalculado": {"valorTituloOriginal":"","valorTotalCobrar":"","valorDescontoCalculado":"","valorJurosCalculado":"","valorMultaCalculado":"","valorAbatimento":"","dataOperacao":"","codigoerro":"2","descricaoerro":"Erro!"}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BANCO ARBI S.A."}}]"""
        val validation = arbiAdapter.convertToValidationResponse(response)
        val status = validation.getStatus()
        status.shouldBeTypeOf<BillValidationStatus.UnableToValidate>()
        status.shouldRetryValidation.shouldBeTrue()
    }

    @Test
    fun `should return already paid when credit card is active but has amount paid`() {
        val response =
            """[{"DDA0110R1": {"NumCtrlPart":"DDA20200522000341775","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20200522000771184702","NumIdentcTit":"2020042207658657449","NumRefAtlCadTit":"1588742057601000505","NumSeqAtlzCadTit":"2","DtHrSitTit":"2020-05-06T02:14:17","ISPBPartDestinatario":"90400888","CodPartDestinatario":"033","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"**************","Nom_RzSocBenfcrioOr":"ASSOCIACAO BRITANICA DE EDUCACAO","NomFantsBenfcrioOr":"ASSOCIACAO BRITANICA DE EDUCACAO","LogradBenfcrioOr":"R DA MATRIZ 76 86","CidBenfcrioOr":"RIO DE JANEIRO","UFBenfcrioOr":"RJ","CEPBenfcrioOr":"22260100","TpPessoaBenfcrioFinl":"J","CNPJ_CPFBenfcrioFinl":"**************","Nom_RzSocBenfcrioFinl":"ASSOCIACAO BRITANICA DE EDUCACAO","NomFantsBenfcrioFinl":"ASSOCIACAO BRITANICA DE EDUCACAO","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"8650436714","Nom_RzSocPagdr":"OLIVIA PIMENTA DE OLIVEIRA CASTRO","NomFantsPagdr":"OLIVIA PIMENTA DE OLIVEIRA CASTRO","TpIdentcSacdrAvalst":"","IdentcSacdrAvalst":"","Nom_RzSocSacdrAvalst":"","CodMoedaCNAB":"09","NumCodBarras":"03391824600000843809847075800000013008220101","NumLinhaDigtvl":"03399847007580000001230082201010182460000084380","DtVencTit":"2020-05-05","VlrTit":"843.80","CodEspTit":"31","QtdDiaPrott":"","DtLimPgtoTit":"2020-08-12","IndrBloqPgto":"N","IndrPgtoParcl":"N","QtdPgtoParcl":"","VlrAbattTit":"0.00","DtJurosTit":"2020-05-06","CodJurosTit":"1","Vlr_PercJurosTit":"0.28","CodMultaTit":"1","DtMultaTit":"2020-05-06","Vlr_PercMultaTit":"16.88","DtDesctTit":"","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"P","Vlr_PercMinTit":"100.00000","TpVlr_PercMaxTit":"P","Vlr_PercMaxTit":"100.00000","TpModlCalc":"01","TpAutcRecbtVlrDivgte":"3","QtdPgtoParclRegtd":"","VlrSldTotAtlPgtoTit":"","NumIdentcBaixaEft":"2020050500599955100","NumRefAtlBaixaEft":"1588742057257000505","NumSeqAtlzBaixaEft":"1","DtProcBaixaEft":"2020-05-05","DtHrProcBaixaEft":"2020-05-06T00:20:13","VlrBaixaEftTit":"843.80","NumCodBarrasBaixaEft":"03391824600000843809847075800000013008220101","CanPgto":"","MeioPgto":"","DtHrSitBaixaEft":"2020-05-06T02:14:17","SitTitPgto":"12","DtHrDDA":"2020-05-22T15:33:04","DtMovto":"2020-05-22"}},{"ValorCalculado": {"valorTituloOriginal":"843.80","valorTotalCobrar":"865.44","valorDescontoCalculado":"0","valorJurosCalculado":"4.76","valorMultaCalculado":"16.88","valorAbatimento":"0.00","dataOperacao":"22/05/2020"}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BANCO ARBI S.A."}}]"""
        val validation = arbiAdapter.convertToValidationResponse(response)
        validation.getStatus() shouldBe BillValidationStatus.AlreadyPaid
    }

    @Test
    fun `should return non-retryable unable to validate`() {
        val response =
            """[{"DDA0110R1": {"NumCtrlPart":"DDA20220912050445503","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20220912000170335953","NumIdentcTit":"2022082204350278143","NumRefAtlCadTit":"1662695275091000908","NumSeqAtlzCadTit":"13","DtHrSitTit":"2022-09-09T00:47:55","ISPBPartDestinatario":"60701190","CodPartDestinatario":"341","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"60701190000104","Nom_RzSocBenfcrioOr":"ITAU UNIBANCO S.A.","NomFantsBenfcrioOr":"ITAU UNIBANCO S.A.","LogradBenfcrioOr":"PRACA ALFREDO EGYDIO DE SOUZA ARANHA, 10","CidBenfcrioOr":"SAO PAULO","UFBenfcrioOr":"SP","CEPBenfcrioOr":"","TpPessoaBenfcrioFinl":"","CNPJ_CPFBenfcrioFinl":"","Nom_RzSocBenfcrioFinl":"","NomFantsBenfcrioFinl":"","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"8473333705","Nom_RzSocPagdr":"NILZA DE SOUZA VIANA","NomFantsPagdr":"","TpIdentcSacdrAvalst":"","IdentcSacdrAvalst":"","Nom_RzSocSacdrAvalst":"","CodMoedaCNAB":"09","NumCodBarras":"34195909500000011788613331480672525043440000","NumLinhaDigtvl":"34198613353148067252150434400003590950000001178","DtVencTit":"2022-09-01","VlrTit":"47.56","CodEspTit":"99","QtdDiaPrott":"","DtLimPgtoTit":"2022-09-10","IndrBloqPgto":"N","IndrPgtoParcl":"N","QtdPgtoParcl":"","VlrAbattTit":"0.00","DtJurosTit":"2022-09-02","CodJurosTit":"3","Vlr_PercJurosTit":"0.05000","CodMultaTit":"1","DtMultaTit":"2022-09-02","Vlr_PercMultaTit":"0.93","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"","Vlr_PercMinTit":"","TpVlr_PercMaxTit":"","Vlr_PercMaxTit":"","TpModlCalc":"03","TpAutcRecbtVlrDivgte":"3","QtdPgtoParclRegtd":"","VlrSldTotAtlPgtoTit":"","SitTitPgto":"12","DtHrDDA":"2022-09-12T14:46:58","DtMovto":"2022-09-12"}},{"ValorCalculado": {"valorTituloOriginal":"","valorTotalCobrar":"","valorDescontoCalculado":"","valorJurosCalculado":"","valorMultaCalculado":"","valorAbatimento":"","dataOperacao":"","codigoerro":"107","descricaoerro":"Grupo de cálculo não informado!"}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BCO ARBI S.A."}}]"""
        val validation = arbiAdapter.convertToValidationResponse(response)
        val status = validation.getStatus()
        status.shouldBeTypeOf<BillValidationStatus.UnableToValidate>()
        status.shouldRetryValidation.shouldBeFalse()
        validation.errorDescription shouldBe "107: Grupo de cálculo não informado!"
    }

    @Test
    fun `should also return non-retryable unable to validate`() {
        val response =
            """[{"DDA0110R1":{"NumCtrlPart":"DDA20240419100499589","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20240419000809935007","NumIdentcTit":"2021090604875467339","NumRefAtlCadTit":"1713496990626000418","NumSeqAtlzCadTit":"21","DtHrSitTit":"2024-04-19T00:23:10","ISPBPartDestinatario":"60701190","CodPartDestinatario":"341","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"17192451000170","Nom_RzSocBenfcrioOr":"BANCO ITAUCARD S.A.","NomFantsBenfcrioOr":"ITAUCARD S.A.","LogradBenfcrioOr":"CAIXA POSTAL 26507","CidBenfcrioOr":"SAO PAULO","UFBenfcrioOr":"SP","CEPBenfcrioOr":"4311990","TpPessoaBenfcrioFinl":"J","CNPJ_CPFBenfcrioFinl":"17192451000170","Nom_RzSocBenfcrioFinl":"BANCO ITAUCARD S.A.","NomFantsBenfcrioFinl":"ITAUCARD S.A.","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"11080679995","Nom_RzSocPagdr":"GIOVANNE MANFRON BAGATIM","NomFantsPagdr":"","TpIdentcSacdrAvalst":"","IdentcSacdrAvalst":"","Nom_RzSocSacdrAvalst":"","CodMoedaCNAB":"09","NumCodBarras":"34197966400001248700313115087460000000000000","NumLinhaDigtvl":"34190313191508746**************0796640000124870","DtVencTit":"2024-03-23","VlrTit":"1248.70","CodEspTit":"99","QtdDiaPrott":"","DtLimPgtoTit":"2024-05-22","IndrBloqPgto":"N","IndrPgtoParcl":"N","QtdPgtoParcl":"","VlrAbattTit":"0.00","DtJurosTit":"2024-03-24","CodJurosTit":"3","Vlr_PercJurosTit":"2.89000","CodMultaTit":"1","DtMultaTit":"2024-03-24","Vlr_PercMultaTit":"2.00","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"","Vlr_PercMinTit":"","TpVlr_PercMaxTit":"","Vlr_PercMaxTit":"","TpModlCalc":"03","TpAutcRecbtVlrDivgte":"3","QtdPgtoParclRegtd":"","VlrSldTotAtlPgtoTit":"","SitTitPgto":"12","DtHrDDA":"2024-04-19T10:20:34","DtMovto":"2024-04-19"}},{"ValorCalculado":{"valorTituloOriginal":"","valorTotalCobrar":"","valorDescontoCalculado":"","valorJurosCalculado":"","valorMultaCalculado":"","valorAbatimento":"","dataOperacao":"","codigoerro":"108","descricaoerro":"Não existe Data da Validade do Cálculo na consulta do boleto para a data de hoje. O título só poderá ser pago no Banco Emissor!"}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BCO ARBI S.A."}}]"""
        val validation = arbiAdapter.convertToValidationResponse(response)
        val status = validation.getStatus()
        status.shouldBeTypeOf<BillValidationStatus.UnableToValidate>()
        status.shouldRetryValidation.shouldBeFalse()
        validation.errorDescription shouldBe "108: Não existe Data da Validade do Cálculo na consulta do boleto para a data de hoje. O título só poderá ser pago no Banco Emissor!"
    }

    // tipo 1 aceita pagamento parcial com percentual (minimo e maximo)
    val response1 =
        """[{"DDA0110R1": {"NumCtrlPart":"DDA20230620000168099","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20230620000580485389","NumIdentcTit":"2017062701001886827","NumRefAtlCadTit":"1498586234483000627","NumSeqAtlzCadTit":"1","DtHrSitTit":"2018-06-03T04:37:17","ISPBPartDestinatario":"02038232","CodPartDestinatario":"756","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"2038232000164","Nom_RzSocBenfcrioOr":"IVANIR ANDRADE","NomFantsBenfcrioOr":"Apelido 28339","LogradBenfcrioOr":"Endereco","CidBenfcrioOr":"Cidade","UFBenfcrioOr":"DF","CEPBenfcrioOr":"70610460","TpPessoaBenfcrioFinl":"","CNPJ_CPFBenfcrioFinl":"","Nom_RzSocBenfcrioFinl":"","NomFantsBenfcrioFinl":"","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"65442245704","Nom_RzSocPagdr":"AAAAAAAAAAAAAAAAAAAAAAAAISGINPILTVKSKUYH","NomFantsPagdr":"","TpIdentcSacdrAvalst":"","IdentcSacdrAvalst":"","Nom_RzSocSacdrAvalst":"","CodMoedaCNAB":"09","NumCodBarras":"75696**************6000103000283390000016914","NumLinhaDigtvl":"756960001303000283394000001691446**************","DtVencTit":"2017-08-01","VlrTit":"313.18","CodEspTit":"31","QtdDiaPrott":"","DtLimPgtoTit":"2030-12-31","IndrBloqPgto":"N","IndrPgtoParcl":"S","QtdPgtoParcl":"99","VlrAbattTit":"0.00","DtJurosTit":"","CodJurosTit":"5","Vlr_PercJurosTit":"0.00","CodMultaTit":"3","DtMultaTit":"","Vlr_PercMultaTit":"0.00","QtdPgtoParclRegtd":"","VlrSldTotAtlPgtoTit":"","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"P","Vlr_PercMinTit":"0.01000","TpVlr_PercMaxTit":"P","Vlr_PercMaxTit":"999.99000","TpModlCalc":"01","TpAutcRecbtVlrDivgte":"1","SitTitPgto":"12","DtHrDDA":"2023-06-20T15:56:08","DtMovto":"2023-06-20"}},{"ValorCalculado": {"valorTituloOriginal":"313.18","valorTotalCobrar":"313.18","valorDescontoCalculado":"0","valorJurosCalculado":"0","valorMultaCalculado":"0","valorAbatimento":"0.00","dataOperacao":"20/06/2023","codigoerro":"","descricaoerro":""}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BANCO ARBI S.A."}}]"""

    @Test
    fun `quando aceitar pagamento parcial com percentual minimo e maximo`() {
        val validation = arbiAdapter.convertToValidationResponse(response1)
        val status = validation.getStatus()
        status.shouldBeTypeOf<BillValidationStatus.Payable>()

        validation.billRegisterData?.divergentPayment?.amountType shouldBe PartialPaymentAmountType.ANY_VALUE
        validation.billRegisterData?.divergentPayment?.maximumPercentage.shouldNotBeNull()
        validation.billRegisterData?.divergentPayment?.minimumPercentage.shouldNotBeNull()
    }

    // tipo 1 aceita pagamento parcial com valor (apenas minimo)
    val response2 =
        """[{"DDA0110R1": {"NumCtrlPart":"DDA20240126096311791","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20240126000370732742","NumIdentcTit":"3023122902999099197","NumRefAtlCadTit":"1704537480837000106","NumSeqAtlzCadTit":"2","DtHrSitTit":"2024-01-06T07:38:00","ISPBPartDestinatario":"58160789","CodPartDestinatario":"422","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"58160789000128","Nom_RzSocBenfcrioOr":"BANCO SAFRA SA","NomFantsBenfcrioOr":"BANCO SAFRA SA","LogradBenfcrioOr":"AV PAULISTA NUMERO 2100","CidBenfcrioOr":"SAO PAULO","UFBenfcrioOr":"SP","CEPBenfcrioOr":"1310930","TpPessoaBenfcrioFinl":"","CNPJ_CPFBenfcrioFinl":"","Nom_RzSocBenfcrioFinl":"","NomFantsBenfcrioFinl":"","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"22839807807","Nom_RzSocPagdr":"WILLIAN J NOGUEIRA","NomFantsPagdr":"","TpIdentcSacdrAvalst":"","IdentcSacdrAvalst":"","Nom_RzSocSacdrAvalst":"","CodMoedaCNAB":"09","NumCodBarras":"42291**************7584053422240100701668554","NumLinhaDigtvl":"422975840653422240100070166855421**************","DtVencTit":"2024-01-05","VlrTit":"1104.25","CodEspTit":"31","QtdDiaPrott":"","DtLimPgtoTit":"${getLocalDate().format(
            ISO_DATE,
        )}","IndrBloqPgto":"N","IndrPgtoParcl":"S","QtdPgtoParcl":"99","VlrAbattTit":"0.00","DtJurosTit":"","CodJurosTit":"5","Vlr_PercJurosTit":"0.00","CodMultaTit":"3","DtMultaTit":"","Vlr_PercMultaTit":"0.00","QtdPgtoParclRegtd":"1","VlrSldTotAtlPgtoTit":"","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"V","Vlr_PercMinTit":"0.01","TpVlr_PercMaxTit":"","Vlr_PercMaxTit":"","TpModlCalc":"01","TpAutcRecbtVlrDivgte":"1","SitTitPgto":"12","DtHrDDA":"2024-01-26T07:18:58","DtMovto":"2024-01-26"}},{"ValorCalculado": {"valorTituloOriginal":"1104.25","valorTotalCobrar":"1104.25","valorDescontoCalculado":"0","valorJurosCalculado":"0","valorMultaCalculado":"0","valorAbatimento":"0.00","dataOperacao":"26/01/2024","codigoerro":"","descricaoerro":""}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BCO ARBI S.A."}}]"""

    @Test
    fun `quando aceitar pagamento parcial com apenas valor minimo`() {
        val validation = arbiAdapter.convertToValidationResponse(response2)
        val status = validation.getStatus()
        status.shouldBeTypeOf<BillValidationStatus.Payable>()

        validation.billRegisterData?.divergentPayment?.amountType shouldBe PartialPaymentAmountType.ANY_VALUE
        validation.billRegisterData?.divergentPayment?.maximumAmount shouldBe null
        validation.billRegisterData?.divergentPayment?.minimumAmount.shouldNotBeNull()
    }

    // tipo 1 aceita pagamento parcial com valor (minimo e maximo)
    val response3 =
        """[{"DDA0110R1": {"NumCtrlPart":"DDA20240126096311922","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20240126000371279692","NumIdentcTit":"2023030301557013431","NumRefAtlCadTit":"1704532853984000106","NumSeqAtlzCadTit":"11","DtHrSitTit":"2024-01-06T06:20:54","ISPBPartDestinatario":"00416968","CodPartDestinatario":"077","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"416968000101","Nom_RzSocBenfcrioOr":"BANCO INTER SA","NomFantsBenfcrioOr":"BANCO INTER SA","LogradBenfcrioOr":"Avenida Barbacena","CidBenfcrioOr":"Belo Horizonte","UFBenfcrioOr":"MG","CEPBenfcrioOr":"30190131","TpPessoaBenfcrioFinl":"J","CNPJ_CPFBenfcrioFinl":"416968000101","Nom_RzSocBenfcrioFinl":"BANCO INTER SA","NomFantsBenfcrioFinl":"BANCO INTER SA","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"2176710479","Nom_RzSocPagdr":"EDUARDO MAGALHAES SILVA                 ","NomFantsPagdr":"","TpIdentcSacdrAvalst":"","IdentcSacdrAvalst":"","Nom_RzSocSacdrAvalst":"","CodMoedaCNAB":"09","NumCodBarras":"07795928700000275190001101001305298840050453","NumLinhaDigtvl":"07790001160100130529988400504531592870000027519","DtVencTit":"2024-01-12","VlrTit":"42.90","CodEspTit":"31","QtdDiaPrott":"","DtLimPgtoTit":"2032-03-30","IndrBloqPgto":"N","IndrPgtoParcl":"S","QtdPgtoParcl":"99","VlrAbattTit":"0.00","DtJurosTit":"","CodJurosTit":"5","Vlr_PercJurosTit":"0.00","CodMultaTit":"3","DtMultaTit":"","Vlr_PercMultaTit":"0.00","QtdPgtoParclRegtd":"","VlrSldTotAtlPgtoTit":"","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"V","Vlr_PercMinTit":"0.01","TpVlr_PercMaxTit":"V","Vlr_PercMaxTit":"9999999999.99","TpModlCalc":"01","TpAutcRecbtVlrDivgte":"1","SitTitPgto":"12","DtHrDDA":"2024-01-26T07:20:33","DtMovto":"2024-01-26"}},{"ValorCalculado": {"valorTituloOriginal":"42.90","valorTotalCobrar":"42.9","valorDescontoCalculado":"0","valorJurosCalculado":"0","valorMultaCalculado":"0","valorAbatimento":"0.00","dataOperacao":"26/01/2024","codigoerro":"","descricaoerro":""}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BCO ARBI S.A."}}]"""

    @Test
    fun `quando aceitar pagamento parcial com valor minimo e máximo`() {
        val validation = arbiAdapter.convertToValidationResponse(response3)
        val status = validation.getStatus()
        status.shouldBeTypeOf<BillValidationStatus.Payable>()

        validation.billRegisterData?.divergentPayment?.amountType shouldBe PartialPaymentAmountType.ANY_VALUE
        validation.billRegisterData?.divergentPayment?.maximumAmount.shouldNotBeNull()
        validation.billRegisterData?.divergentPayment?.minimumAmount.shouldNotBeNull()
    }

    // tipo 1 aceita pagamento parcial (sem minimo nem maximo)
    val response4 =
        """[{"DDA0110R1": {"NumCtrlPart":"DDA20240126096312082","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20240126000371870505","NumIdentcTit":"3023121106596329112","NumRefAtlCadTit":"1704892694653000110","NumSeqAtlzCadTit":"2","DtHrSitTit":"2024-01-10T10:18:14","ISPBPartDestinatario":"00360305","CodPartDestinatario":"104","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"18727053000174","Nom_RzSocBenfcrioOr":"PAGAR.ME PAGAMENTOS S.A.","NomFantsBenfcrioOr":"PAGAR.ME PAGAMENTOS S.A.","LogradBenfcrioOr":"FIDENCIO RAMOS  9 ANDAR","CidBenfcrioOr":"SAO PAULO","UFBenfcrioOr":"SP","CEPBenfcrioOr":"4551010","TpPessoaBenfcrioFinl":"","CNPJ_CPFBenfcrioFinl":"","Nom_RzSocBenfcrioFinl":"","NomFantsBenfcrioFinl":"","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"30009054804","Nom_RzSocPagdr":"FERNANDO Cesar De Jesus","NomFantsPagdr":"","TpIdentcSacdrAvalst":"2","IdentcSacdrAvalst":"017607744000171","Nom_RzSocSacdrAvalst":"Simples Dental","CodMoedaCNAB":"09","NumCodBarras":"10492958900000279901103388000100041334064869","NumLinhaDigtvl":"10491103398800010004513340648693295890000027990","DtVencTit":"2024-01-08","VlrTit":"279.90","CodEspTit":"99","QtdDiaPrott":"","DtLimPgtoTit":"${getLocalDate().format(ISO_DATE)}","IndrBloqPgto":"N","IndrPgtoParcl":"S","QtdPgtoParcl":"1","VlrAbattTit":"0.00","DtJurosTit":"","CodJurosTit":"5","Vlr_PercJurosTit":"0.00","CodMultaTit":"3","DtMultaTit":"","Vlr_PercMultaTit":"0.00","QtdPgtoParclRegtd":"1","VlrSldTotAtlPgtoTit":"","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"","Vlr_PercMinTit":"","TpVlr_PercMaxTit":"","Vlr_PercMaxTit":"","TpModlCalc":"01","TpAutcRecbtVlrDivgte":"1","SitTitPgto":"12","DtHrDDA":"2024-01-26T07:21:51","DtMovto":"2024-01-26"}},{"ValorCalculado": {"valorTituloOriginal":"279.90","valorTotalCobrar":"279.9","valorDescontoCalculado":"0","valorJurosCalculado":"0","valorMultaCalculado":"0","valorAbatimento":"0.00","dataOperacao":"26/01/2024","codigoerro":"","descricaoerro":""}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BCO ARBI S.A."}}]"""

    @Test
    fun `quando aceitar pagamento parcial sem valor minimo e máximo`() {
        val validation = arbiAdapter.convertToValidationResponse(response4)
        val status = validation.getStatus()
        status.shouldBeTypeOf<BillValidationStatus.Payable>()

        validation.billRegisterData?.divergentPayment?.amountType shouldBe PartialPaymentAmountType.ANY_VALUE
        validation.billRegisterData?.divergentPayment?.maximumAmount.shouldBeNull()
        validation.billRegisterData?.divergentPayment?.minimumAmount.shouldBeNull()
        validation.billRegisterData?.divergentPayment?.minimumPercentage.shouldBeNull()
        validation.billRegisterData?.divergentPayment?.maximumPercentage.shouldBeNull()
    }

    // tipo 1 aceita pagamento parcial percentual (apenas minimo)
    val response5 =
        """[{"DDA0110R1": {"NumCtrlPart":"DDA20240126096320141","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20240126000399182702","NumIdentcTit":"2020121503352322222","NumRefAtlCadTit":"1704554351412000106","NumSeqAtlzCadTit":"66","DtHrSitTit":"2024-01-06T12:19:15","ISPBPartDestinatario":"59588111","CodPartDestinatario":"655","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"1858774000110","Nom_RzSocBenfcrioOr":"BANCO BV S.A","NomFantsBenfcrioOr":"BANCO BV","LogradBenfcrioOr":"","CidBenfcrioOr":"","UFBenfcrioOr":"","CEPBenfcrioOr":"","TpPessoaBenfcrioFinl":"","CNPJ_CPFBenfcrioFinl":"","Nom_RzSocBenfcrioFinl":"","NomFantsBenfcrioFinl":"","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"20717806200","Nom_RzSocPagdr":"AGENOR SANTOS FARIAS","NomFantsPagdr":"AGENOR SANTOS FARIAS","TpIdentcSacdrAvalst":"2","IdentcSacdrAvalst":"01858774000110","Nom_RzSocSacdrAvalst":"BANCO BV S.A","CodMoedaCNAB":"09","NumCodBarras":"65595**************0000000248500067131405000","NumLinhaDigtvl":"655900000200248500068713140500075**************","DtVencTit":"2024-01-15","VlrTit":"28.90","CodEspTit":"31","QtdDiaPrott":"","DtLimPgtoTit":"${getLocalDate().format(ISO_DATE)}","IndrBloqPgto":"N","IndrPgtoParcl":"S","QtdPgtoParcl":"99","VlrAbattTit":"0.00","DtJurosTit":"","CodJurosTit":"5","Vlr_PercJurosTit":"0.00","CodMultaTit":"3","DtMultaTit":"","Vlr_PercMultaTit":"0.00","QtdPgtoParclRegtd":"","VlrSldTotAtlPgtoTit":"","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"P","Vlr_PercMinTit":"0.10000","TpVlr_PercMaxTit":"","Vlr_PercMaxTit":"","TpModlCalc":"01","TpAutcRecbtVlrDivgte":"1","SitTitPgto":"12","DtHrDDA":"2024-01-26T09:51:00","DtMovto":"2024-01-26"}},{"ValorCalculado": {"valorTituloOriginal":"28.90","valorTotalCobrar":"28.9","valorDescontoCalculado":"0","valorJurosCalculado":"0","valorMultaCalculado":"0","valorAbatimento":"0.00","dataOperacao":"26/01/2024","codigoerro":"","descricaoerro":""}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BCO ARBI S.A."}}]"""

    val response5Expired =
        """[{"DDA0110R1": {"NumCtrlPart":"DDA20240126096320141","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20240126000399182702","NumIdentcTit":"2020121503352322222","NumRefAtlCadTit":"1704554351412000106","NumSeqAtlzCadTit":"66","DtHrSitTit":"2024-01-06T12:19:15","ISPBPartDestinatario":"59588111","CodPartDestinatario":"655","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"1858774000110","Nom_RzSocBenfcrioOr":"BANCO BV S.A","NomFantsBenfcrioOr":"BANCO BV","LogradBenfcrioOr":"","CidBenfcrioOr":"","UFBenfcrioOr":"","CEPBenfcrioOr":"","TpPessoaBenfcrioFinl":"","CNPJ_CPFBenfcrioFinl":"","Nom_RzSocBenfcrioFinl":"","NomFantsBenfcrioFinl":"","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"20717806200","Nom_RzSocPagdr":"AGENOR SANTOS FARIAS","NomFantsPagdr":"AGENOR SANTOS FARIAS","TpIdentcSacdrAvalst":"2","IdentcSacdrAvalst":"01858774000110","Nom_RzSocSacdrAvalst":"BANCO BV S.A","CodMoedaCNAB":"09","NumCodBarras":"65595**************0000000248500067131405000","NumLinhaDigtvl":"655900000200248500068713140500075**************","DtVencTit":"2024-01-15","VlrTit":"28.90","CodEspTit":"31","QtdDiaPrott":"","DtLimPgtoTit":"${getLocalDate().minusDays(1).format(ISO_DATE)}","IndrBloqPgto":"N","IndrPgtoParcl":"S","QtdPgtoParcl":"99","VlrAbattTit":"0.00","DtJurosTit":"","CodJurosTit":"5","Vlr_PercJurosTit":"0.00","CodMultaTit":"3","DtMultaTit":"","Vlr_PercMultaTit":"0.00","QtdPgtoParclRegtd":"","VlrSldTotAtlPgtoTit":"","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"P","Vlr_PercMinTit":"0.10000","TpVlr_PercMaxTit":"","Vlr_PercMaxTit":"","TpModlCalc":"01","TpAutcRecbtVlrDivgte":"1","SitTitPgto":"12","DtHrDDA":"2024-01-26T09:51:00","DtMovto":"2024-01-26"}},{"ValorCalculado": {"valorTituloOriginal":"28.90","valorTotalCobrar":"28.9","valorDescontoCalculado":"0","valorJurosCalculado":"0","valorMultaCalculado":"0","valorAbatimento":"0.00","dataOperacao":"26/01/2024","codigoerro":"","descricaoerro":""}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BCO ARBI S.A."}}]"""

    @Test
    fun `quando aceitar pagamento parcial percentual minimo`() {
        val validation = arbiAdapter.convertToValidationResponse(response5)
        val status = validation.getStatus()
        status.shouldBeTypeOf<BillValidationStatus.Payable>()

        validation.billRegisterData?.divergentPayment?.amountType shouldBe PartialPaymentAmountType.ANY_VALUE
        validation.billRegisterData?.divergentPayment?.maximumAmount.shouldBeNull()
        validation.billRegisterData?.divergentPayment?.minimumAmount.shouldBeNull()
        validation.billRegisterData?.divergentPayment?.minimumPercentage.shouldNotBeNull()
        validation.billRegisterData?.divergentPayment?.maximumPercentage.shouldBeNull()

        validation.billRegisterData?.partialPayment?.acceptPartialPayment shouldBe true
        validation.billRegisterData?.partialPayment?.qtdPagamentoParcial shouldBe 99
        validation.billRegisterData?.partialPayment?.qtdPagamentoParcialRegistrado shouldBe 0
        validation.billRegisterData?.partialPayment?.saldoAtualPagamento shouldBe 0
    }

    @Test
    fun `quando o limite for no passado a conta deve ser not payable`() {
        val validation = arbiAdapter.convertToValidationResponse(response5Expired)
        val status = validation.getStatus()
        status.shouldBeTypeOf<BillValidationStatus.NotPayable>()
    }

    // tipo 3 sem pagamento parcial
    val response6 =
        """[{"DDA0110R1": {"NumCtrlPart":"DDA20240125096258349","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20240125000214820331","NumIdentcTit":"3023121504711567790","NumRefAtlCadTit":"1706161870014000124","NumSeqAtlzCadTit":"27","DtHrSitTit":"2024-01-25T02:51:10","ISPBPartDestinatario":"60701190","CodPartDestinatario":"341","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"3625*********","Nom_RzSocBenfcrioOr":"CROPCHEM LTDA","NomFantsBenfcrioOr":"CROPCHEM LTDA","LogradBenfcrioOr":"AVENIDA CRISTOVAO COLOMBO      2834","CidBenfcrioOr":"PORTO ALEGRE","UFBenfcrioOr":"RS","CEPBenfcrioOr":"90560002","TpPessoaBenfcrioFinl":"","CNPJ_CPFBenfcrioFinl":"","Nom_RzSocBenfcrioFinl":"","NomFantsBenfcrioFinl":"","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"2207272109","Nom_RzSocPagdr":"LUCAS ANTONIOLLI","NomFantsPagdr":"","TpIdentcSacdrAvalst":"","IdentcSacdrAvalst":"","Nom_RzSocSacdrAvalst":"","CodMoedaCNAB":"02","NumCodBarras":"34196982600000000001501181411940579258708000","NumLinhaDigtvl":"34191501198141194057092587080000698260000000000","DtVencTit":"2024-09-01","VlrTit":"7870.72","CodEspTit":"3","QtdDiaPrott":"999","DtLimPgtoTit":"${getLocalDate().plusDays(1).format(
            ISO_DATE,
        )}","IndrBloqPgto":"N","IndrPgtoParcl":"N","QtdPgtoParcl":"","VlrAbattTit":"0.00","DtJurosTit":"2024-09-02","CodJurosTit":"1","Vlr_PercJurosTit":"0.80","CodMultaTit":"3","DtMultaTit":"","Vlr_PercMultaTit":"0.00","QtdPgtoParclRegtd":"","VlrSldTotAtlPgtoTit":"","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"","Vlr_PercMinTit":"","TpVlr_PercMaxTit":"","Vlr_PercMaxTit":"","TpModlCalc":"03","TpAutcRecbtVlrDivgte":"3","SitTitPgto":"12","DtHrDDA":"2024-01-25T06:22:35","DtMovto":"2024-01-25"}},{"ValorCalculado": {"valorTituloOriginal":"7870.72","valorTotalCobrar":"0","valorDescontoCalculado":"0","valorJurosCalculado":"0","valorMultaCalculado":"0","valorAbatimento":"0.00","dataOperacao":"26/01/2024","codigoerro":"","descricaoerro":""}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BCO ARBI S.A."}}]"""

    @Test
    fun `quando não aceitar pagamento divergente`() {
        val validation = arbiAdapter.convertToValidationResponse(response6)
        val status = validation.getStatus()
        status.shouldBeTypeOf<BillValidationStatus.Payable>()

        validation.billRegisterData?.divergentPayment?.amountType shouldBe PartialPaymentAmountType.NOT_ACCEPT_DIFFERENT_VALUE
        validation.billRegisterData?.divergentPayment?.maximumAmount.shouldBeNull()
        validation.billRegisterData?.divergentPayment?.minimumAmount.shouldBeNull()
        validation.billRegisterData?.divergentPayment?.minimumPercentage.shouldBeNull()
        validation.billRegisterData?.divergentPayment?.maximumPercentage.shouldBeNull()
    }

    // tipo 2 sem pagamento parcial
    val response7 =
        """[{"DDA0110R1": {"NumCtrlPart":"DDA20240126096311479","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20240126000371058332","NumIdentcTit":"3024012407563171409","NumRefAtlCadTit":"1706096350858000124","NumSeqAtlzCadTit":"1","DtHrSitTit":"2024-01-24T08:39:10","ISPBPartDestinatario":"60701190","CodPartDestinatario":"341","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"8667833000112","Nom_RzSocBenfcrioOr":"GA SP LIBER LOCACAO ESP LTDA","NomFantsBenfcrioOr":"GA SP LIBER LOCACAO ESP LTDA","LogradBenfcrioOr":"RUA GLICERIO                   389","CidBenfcrioOr":"SAO PAULO","UFBenfcrioOr":"SP","CEPBenfcrioOr":"1514000","TpPessoaBenfcrioFinl":"","CNPJ_CPFBenfcrioFinl":"","Nom_RzSocBenfcrioFinl":"","NomFantsBenfcrioFinl":"","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"44022710888","Nom_RzSocPagdr":"MAYCON  DOS SANTOS EUZEBIO","NomFantsPagdr":"","TpIdentcSacdrAvalst":"","IdentcSacdrAvalst":"","Nom_RzSocSacdrAvalst":"","CodMoedaCNAB":"09","NumCodBarras":"34191962000010326711090003995732938142599000","NumLinhaDigtvl":"34191090080399573293481425990009196200001032671","DtVencTit":"2024-02-08","VlrTit":"10326.71","CodEspTit":"3","QtdDiaPrott":"","DtLimPgtoTit":"2034-02-08","IndrBloqPgto":"N","IndrPgtoParcl":"N","QtdPgtoParcl":"","VlrAbattTit":"0.00","DtJurosTit":"2024-02-09","CodJurosTit":"1","Vlr_PercJurosTit":"3.44","CodMultaTit":"1","DtMultaTit":"2024-02-09","Vlr_PercMultaTit":"206.53","QtdPgtoParclRegtd":"","VlrSldTotAtlPgtoTit":"","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"P","Vlr_PercMinTit":"95.00000","TpVlr_PercMaxTit":"P","Vlr_PercMaxTit":"105.00000","TpModlCalc":"01","TpAutcRecbtVlrDivgte":"2","SitTitPgto":"12","DtHrDDA":"2024-01-26T07:16:00","DtMovto":"2024-01-26"}},{"ValorCalculado": {"valorTituloOriginal":"10326.71","valorTotalCobrar":"10326.71","valorDescontoCalculado":"0","valorJurosCalculado":"0","valorMultaCalculado":"0","valorAbatimento":"0.00","dataOperacao":"26/01/2024","codigoerro":"","descricaoerro":""}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BCO ARBI S.A."}}]"""

    @Test
    fun `quando não aceitar pagamento parcial mas aceita divergente com valor mínimo e máximo`() {
        val validation = arbiAdapter.convertToValidationResponse(response7)
        val status = validation.getStatus()
        status.shouldBeTypeOf<BillValidationStatus.Payable>()

        validation.billRegisterData?.divergentPayment?.amountType shouldBe PartialPaymentAmountType.BETWEEN_MINIMUM_AND_MAXIMUM
        validation.billRegisterData?.divergentPayment?.maximumAmount.shouldBeNull()
        validation.billRegisterData?.divergentPayment?.minimumAmount.shouldBeNull()
        validation.billRegisterData?.divergentPayment?.minimumPercentage.shouldNotBeNull()
        validation.billRegisterData?.divergentPayment?.maximumPercentage.shouldNotBeNull()
        validation.billRegisterData?.partialPayment?.acceptPartialPayment shouldBe false
        validation.billRegisterData?.partialPayment?.qtdPagamentoParcialRegistrado shouldBe 0
        validation.billRegisterData?.partialPayment?.saldoAtualPagamento shouldBe 0L
    }

    // tipo 4 sem pagamento parcial
    val response8 =
        """[{"DDA0110R1": {"NumCtrlPart":"DDA20240124096229909","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20240124000110189894","NumIdentcTit":"3024011202317593244","NumRefAtlCadTit":"1705084099526000112","NumSeqAtlzCadTit":"1","DtHrSitTit":"2024-01-12T15:28:19","ISPBPartDestinatario":"31872495","CodPartDestinatario":"336","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"31872495000172","Nom_RzSocBenfcrioOr":"BANCO C6 S.A.","NomFantsBenfcrioOr":"BANCO C6 S.A.","LogradBenfcrioOr":"AV NOVE DE JULHO, 3186","CidBenfcrioOr":"SAO PAULO","UFBenfcrioOr":"SP","CEPBenfcrioOr":"1406000","TpPessoaBenfcrioFinl":"","CNPJ_CPFBenfcrioFinl":"","Nom_RzSocBenfcrioFinl":"","NomFantsBenfcrioFinl":"","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"*********","Nom_RzSocPagdr":"BARBARA RODRIGUES MONTEIRO","NomFantsPagdr":"BARBARA RODRIGUES MONTEIRO","TpIdentcSacdrAvalst":"2","IdentcSacdrAvalst":"31872495000172","Nom_RzSocSacdrAvalst":"BANCO C6 S.A.","CodMoedaCNAB":"09","NumCodBarras":"33693961300001045930000000000010165658374043","NumLinhaDigtvl":"33690000090000001016556583740438396130000104593","DtVencTit":"2024-02-01","VlrTit":"1045.93","CodEspTit":"99","QtdDiaPrott":"","DtLimPgtoTit":"${getLocalDate().format(ISO_DATE)}","IndrBloqPgto":"N","IndrPgtoParcl":"N","QtdPgtoParcl":"","VlrAbattTit":"0.00","DtJurosTit":"2024-02-02","CodJurosTit":"1","Vlr_PercJurosTit":"0.35","CodMultaTit":"2","DtMultaTit":"2024-02-02","Vlr_PercMultaTit":"2.00000","QtdPgtoParclRegtd":"","VlrSldTotAtlPgtoTit":"","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"P","Vlr_PercMinTit":"100.00000","TpVlr_PercMaxTit":"","Vlr_PercMaxTit":"","TpModlCalc":"01","TpAutcRecbtVlrDivgte":"4","SitTitPgto":"12","DtHrDDA":"2024-01-24T11:40:57","DtMovto":"2024-01-24"}},{"ValorCalculado": {"valorTituloOriginal":"1045.93","valorTotalCobrar":"1045.93","valorDescontoCalculado":"0","valorJurosCalculado":"0","valorMultaCalculado":"0","valorAbatimento":"0.00","dataOperacao":"24/01/2024","codigoerro":"","descricaoerro":""}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BCO ARBI S.A."}}]"""

    @Test
    fun `quando não aceitar pagamento parcial mas aceita divergente somente com valor mínimo`() {
        val validation = arbiAdapter.convertToValidationResponse(response8)
        val status = validation.getStatus()
        status.shouldBeTypeOf<BillValidationStatus.Payable>()

        validation.billRegisterData?.divergentPayment?.amountType shouldBe PartialPaymentAmountType.ONLY_MINIMUM_VALUE
        validation.billRegisterData?.divergentPayment?.maximumAmount.shouldBeNull()
        validation.billRegisterData?.divergentPayment?.minimumAmount.shouldBeNull()
        validation.billRegisterData?.divergentPayment?.minimumPercentage.shouldNotBeNull()
        validation.billRegisterData?.divergentPayment?.maximumPercentage.shouldBeNull()
        validation.billRegisterData?.partialPayment?.acceptPartialPayment shouldBe false
        validation.billRegisterData?.partialPayment?.qtdPagamentoParcialRegistrado shouldBe 0
        validation.billRegisterData?.partialPayment?.saldoAtualPagamento shouldBe 0L
    }

    @Nested
    @DisplayName("ao fazer o cálculo do valor pago")
    inner class CalculateAmountPaid {

        @Nested
        @DisplayName("quando aceita pagamento parcial")
        inner class IsPartialAmount {

            @Test
            fun `e possui um valor pago, deve retornar já pago com o valor do campo VlrSldTotAtlPgtoTit`() {
                val response =
                    """[{"DDA0110R1": {"NumCtrlPart":"DDA20230616000166908","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20230616000578333441","NumIdentcTit":"2021040604051460682","NumRefAtlCadTit":"1686943836856000616","NumSeqAtlzCadTit":"29","DtHrSitTit":"2023-06-16T16:30:36","ISPBPartDestinatario":"90400888","CodPartDestinatario":"033","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"90400888000142","Nom_RzSocBenfcrioOr":"SANTANDER VISA EMPRESARIAL CENTRALIZADO - 9005","NomFantsBenfcrioOr":"SANTANDER VISA EMPRESARIAL CENTRALIZADO - 9005","LogradBenfcrioOr":"Av. Pres. Juscelino Kubitschek, 2235","CidBenfcrioOr":"Sao Paulo","UFBenfcrioOr":"SP","CEPBenfcrioOr":"4543011","TpPessoaBenfcrioFinl":"","CNPJ_CPFBenfcrioFinl":"","Nom_RzSocBenfcrioFinl":"","NomFantsBenfcrioFinl":"","TpPessoaPagdr":"J","CNPJ_CPFPagdr":"72849326000107","Nom_RzSocPagdr":"UAFI JALRO A.Y. XEI NMEDO HJKO","NomFantsPagdr":"UAFI JALRO A.Y. XEI NMEDO HJKO","TpIdentcSacdrAvalst":"","IdentcSacdrAvalst":"","Nom_RzSocSacdrAvalst":"","CodMoedaCNAB":"09","NumCodBarras":"03391**************9492836982109600018610102","NumLinhaDigtvl":"033994928136982109609001861010281**************","DtVencTit":"2023-06-18","VlrTit":"146.62","CodEspTit":"31","QtdDiaPrott":"","DtLimPgtoTit":"2032-06-18","IndrBloqPgto":"N","IndrPgtoParcl":"S","QtdPgtoParcl":"99","VlrAbattTit":"0.00","DtJurosTit":"","CodJurosTit":"5","Vlr_PercJurosTit":"0.00","CodMultaTit":"3","DtMultaTit":"","Vlr_PercMultaTit":"0.00","QtdPgtoParclRegtd":"2","VlrSldTotAtlPgtoTit":"293.24","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"V","Vlr_PercMinTit":"0.01","TpVlr_PercMaxTit":"V","Vlr_PercMaxTit":"999999999999.00","TpModlCalc":"01","TpAutcRecbtVlrDivgte":"1","SitTitPgto":"12","DtHrDDA":"2023-06-16T16:35:42","DtMovto":"2023-06-16"}},{"ValorCalculado": {"valorTituloOriginal":"146.62","valorTotalCobrar":"146.62","valorDescontoCalculado":"0","valorJurosCalculado":"0","valorMultaCalculado":"0","valorAbatimento":"0.00","dataOperacao":"16/06/2023","codigoerro":"","descricaoerro":""}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BANCO ARBI S.A."}}]"""
                val validation = arbiAdapter.convertToValidationResponse(response)
                validation.getStatus() shouldBe BillValidationStatus.AlreadyPaid
                validation.billRegisterData!!.amountPaid shouldBe 29324L
            }

            @Test
            fun `e não possui um valor pago, amountPaid deve ser nulo`() {
                val response =
                    """[{"DDA0110R1": {"NumCtrlPart":"DDA20230620000168099","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20230620000580485389","NumIdentcTit":"2017062701001886827","NumRefAtlCadTit":"1498586234483000627","NumSeqAtlzCadTit":"1","DtHrSitTit":"2018-06-03T04:37:17","ISPBPartDestinatario":"02038232","CodPartDestinatario":"756","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"2038232000164","Nom_RzSocBenfcrioOr":"IVANIR ANDRADE","NomFantsBenfcrioOr":"Apelido 28339","LogradBenfcrioOr":"Endereco","CidBenfcrioOr":"Cidade","UFBenfcrioOr":"DF","CEPBenfcrioOr":"70610460","TpPessoaBenfcrioFinl":"","CNPJ_CPFBenfcrioFinl":"","Nom_RzSocBenfcrioFinl":"","NomFantsBenfcrioFinl":"","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"65442245704","Nom_RzSocPagdr":"AAAAAAAAAAAAAAAAAAAAAAAAISGINPILTVKSKUYH","NomFantsPagdr":"","TpIdentcSacdrAvalst":"","IdentcSacdrAvalst":"","Nom_RzSocSacdrAvalst":"","CodMoedaCNAB":"09","NumCodBarras":"75696**************6000103000283390000016914","NumLinhaDigtvl":"756960001303000283394000001691446**************","DtVencTit":"2017-08-01","VlrTit":"313.18","CodEspTit":"31","QtdDiaPrott":"","DtLimPgtoTit":"2030-12-31","IndrBloqPgto":"N","IndrPgtoParcl":"S","QtdPgtoParcl":"99","VlrAbattTit":"0.00","DtJurosTit":"","CodJurosTit":"5","Vlr_PercJurosTit":"0.00","CodMultaTit":"3","DtMultaTit":"","Vlr_PercMultaTit":"0.00","QtdPgtoParclRegtd":"","VlrSldTotAtlPgtoTit":"","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"V","Vlr_PercMinTit":"0.01","TpVlr_PercMaxTit":"V","Vlr_PercMaxTit":"99999999.99","TpModlCalc":"01","TpAutcRecbtVlrDivgte":"1","SitTitPgto":"12","DtHrDDA":"2023-06-20T15:56:08","DtMovto":"2023-06-20"}},{"ValorCalculado": {"valorTituloOriginal":"313.18","valorTotalCobrar":"313.18","valorDescontoCalculado":"0","valorJurosCalculado":"0","valorMultaCalculado":"0","valorAbatimento":"0.00","dataOperacao":"20/06/2023","codigoerro":"","descricaoerro":""}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BANCO ARBI S.A."}}]"""
                val validation = arbiAdapter.convertToValidationResponse(response)
                validation.getStatus() shouldBe BillValidationStatus.Payable
                validation.billRegisterData!!.amountPaid.shouldBeNull()
            }

            @Test
            fun `e for um pagamento full, deve colocar o valor de amountPaid do campo VlrSldTotAtlPgtoTit`() {
                val response =
                    """[{"DDA0110R1": {"NumCtrlPart":"DDA20230616000166908","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20230616000578333441","NumIdentcTit":"2021040604051460682","NumRefAtlCadTit":"1686943836856000616","NumSeqAtlzCadTit":"29","DtHrSitTit":"2023-06-16T16:30:36","ISPBPartDestinatario":"90400888","CodPartDestinatario":"033","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"90400888000142","Nom_RzSocBenfcrioOr":"SANTANDER VISA EMPRESARIAL CENTRALIZADO - 9005","NomFantsBenfcrioOr":"SANTANDER VISA EMPRESARIAL CENTRALIZADO - 9005","LogradBenfcrioOr":"Av. Pres. Juscelino Kubitschek, 2235","CidBenfcrioOr":"Sao Paulo","UFBenfcrioOr":"SP","CEPBenfcrioOr":"4543011","TpPessoaBenfcrioFinl":"","CNPJ_CPFBenfcrioFinl":"","Nom_RzSocBenfcrioFinl":"","NomFantsBenfcrioFinl":"","TpPessoaPagdr":"J","CNPJ_CPFPagdr":"72849326000107","Nom_RzSocPagdr":"UAFI JALRO A.Y. XEI NMEDO HJKO","NomFantsPagdr":"UAFI JALRO A.Y. XEI NMEDO HJKO","TpIdentcSacdrAvalst":"","IdentcSacdrAvalst":"","Nom_RzSocSacdrAvalst":"","CodMoedaCNAB":"09","NumCodBarras":"03391**************9492836982109600018610102","NumLinhaDigtvl":"033994928136982109609001861010281**************","DtVencTit":"2023-06-18","VlrTit":"146.62","CodEspTit":"31","QtdDiaPrott":"","DtLimPgtoTit":"2032-06-18","IndrBloqPgto":"N","IndrPgtoParcl":"S","QtdPgtoParcl":"99","VlrAbattTit":"0.00","DtJurosTit":"","CodJurosTit":"5","Vlr_PercJurosTit":"0.00","CodMultaTit":"3","DtMultaTit":"","Vlr_PercMultaTit":"0.00","QtdPgtoParclRegtd":"2","VlrSldTotAtlPgtoTit":"500.00","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"V","Vlr_PercMinTit":"0.01","TpVlr_PercMaxTit":"V","Vlr_PercMaxTit":"999999999999.00","TpModlCalc":"01","TpAutcRecbtVlrDivgte":"1","SitTitPgto":"12","DtHrDDA":"2023-06-16T16:35:42","DtMovto":"2023-06-16"}},{"ValorCalculado": {"valorTituloOriginal":"500.00","valorTotalCobrar":"500.00","valorDescontoCalculado":"0","valorJurosCalculado":"0","valorMultaCalculado":"0","valorAbatimento":"0.00","dataOperacao":"16/06/2023","codigoerro":"","descricaoerro":""}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BANCO ARBI S.A."}}]"""

                val validation = arbiAdapter.convertToValidationResponse(response)
                validation.getStatus() shouldBe BillValidationStatus.AlreadyPaid
                validation.billRegisterData!!.amountPaid shouldBe 50000L
            }

            @Test
            fun `e for um pagamento parcelado ou minimo, deve colocar o valor de amountPaid do campo VlrSldTotAtlPgtoTit`() {
                val response =
                    """[{"DDA0110R1": {"NumCtrlPart":"DDA20230616000166908","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20230616000578333441","NumIdentcTit":"2021040604051460682","NumRefAtlCadTit":"1686943836856000616","NumSeqAtlzCadTit":"29","DtHrSitTit":"2023-06-16T16:30:36","ISPBPartDestinatario":"90400888","CodPartDestinatario":"033","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"90400888000142","Nom_RzSocBenfcrioOr":"SANTANDER VISA EMPRESARIAL CENTRALIZADO - 9005","NomFantsBenfcrioOr":"SANTANDER VISA EMPRESARIAL CENTRALIZADO - 9005","LogradBenfcrioOr":"Av. Pres. Juscelino Kubitschek, 2235","CidBenfcrioOr":"Sao Paulo","UFBenfcrioOr":"SP","CEPBenfcrioOr":"4543011","TpPessoaBenfcrioFinl":"","CNPJ_CPFBenfcrioFinl":"","Nom_RzSocBenfcrioFinl":"","NomFantsBenfcrioFinl":"","TpPessoaPagdr":"J","CNPJ_CPFPagdr":"72849326000107","Nom_RzSocPagdr":"UAFI JALRO A.Y. XEI NMEDO HJKO","NomFantsPagdr":"UAFI JALRO A.Y. XEI NMEDO HJKO","TpIdentcSacdrAvalst":"","IdentcSacdrAvalst":"","Nom_RzSocSacdrAvalst":"","CodMoedaCNAB":"09","NumCodBarras":"03391**************9492836982109600018610102","NumLinhaDigtvl":"033994928136982109609001861010281**************","DtVencTit":"2023-06-18","VlrTit":"146.62","CodEspTit":"31","QtdDiaPrott":"","DtLimPgtoTit":"2032-06-18","IndrBloqPgto":"N","IndrPgtoParcl":"S","QtdPgtoParcl":"99","VlrAbattTit":"0.00","DtJurosTit":"","CodJurosTit":"5","Vlr_PercJurosTit":"0.00","CodMultaTit":"3","DtMultaTit":"","Vlr_PercMultaTit":"0.00","QtdPgtoParclRegtd":"1","VlrSldTotAtlPgtoTit":"1.24","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"V","Vlr_PercMinTit":"0.01","TpVlr_PercMaxTit":"V","Vlr_PercMaxTit":"999999999999.00","TpModlCalc":"01","TpAutcRecbtVlrDivgte":"1","SitTitPgto":"12","DtHrDDA":"2023-06-16T16:35:42","DtMovto":"2023-06-16"}},{"ValorCalculado": {"valorTituloOriginal":"146.62","valorTotalCobrar":"146.62","valorDescontoCalculado":"0","valorJurosCalculado":"0","valorMultaCalculado":"0","valorAbatimento":"0.00","dataOperacao":"16/06/2023","codigoerro":"","descricaoerro":""}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BANCO ARBI S.A."}}]"""
                val validation = arbiAdapter.convertToValidationResponse(response)
                validation.getStatus() shouldBe BillValidationStatus.AlreadyPaid
                validation.billRegisterData!!.amountPaid shouldBe 124L
            }
        }

        @Nested
        @DisplayName("quando não for um pagamento parcial")
        inner class IsNotPartialAmount {

            @Test
            fun `deve retonar o total pago igual o ao valorTotalCobrar se for um boleto com pagamento já efetuado`() {
                val response =
                    """[{"DDA0110R1": {"NumCtrlPart":"DDA20230620000168066","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20230620000580417463","NumIdentcTit":"2017040500000409808","NumRefAtlCadTit":"1491394235954000405","NumSeqAtlzCadTit":"1","DtHrSitTit":"2018-06-03T04:50:17","ISPBPartDestinatario":"02038232","CodPartDestinatario":"756","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"7912816000130","Nom_RzSocBenfcrioOr":"VIANA MOVEIS E ELETRODOMESTICOS LTDA ME","NomFantsBenfcrioOr":"Edner","LogradBenfcrioOr":"Endereco","CidBenfcrioOr":"Cidade","UFBenfcrioOr":"SC","CEPBenfcrioOr":"89670000","TpPessoaBenfcrioFinl":"","CNPJ_CPFBenfcrioFinl":"","Nom_RzSocBenfcrioFinl":"","NomFantsBenfcrioFinl":"","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"59346957972","Nom_RzSocPagdr":"000051 - VANIA MARIA COVOLAN PICINATTO","NomFantsPagdr":"","TpIdentcSacdrAvalst":"","IdentcSacdrAvalst":"","Nom_RzSocSacdrAvalst":"","CodMoedaCNAB":"09","NumCodBarras":"75699772300150150121306601017321500170191037","NumLinhaDigtvl":"75691306640101732150401701910372977230015015012","DtVencTit":"2017-03-30","VlrTit":"150150.12","CodEspTit":"2","QtdDiaPrott":"","DtLimPgtoTit":"2030-06-05","IndrBloqPgto":"N","IndrPgtoParcl":"N","QtdPgtoParcl":"","VlrAbattTit":"0.00","DtJurosTit":"2017-03-31","CodJurosTit":"8","Vlr_PercJurosTit":"0.32000","CodMultaTit":"3","DtMultaTit":"","Vlr_PercMultaTit":"0.00","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"","Vlr_PercMinTit":"","TpVlr_PercMaxTit":"","Vlr_PercMaxTit":"","TpModlCalc":"01","TpAutcRecbtVlrDivgte":"1","QtdPgtoParclRegtd":"","VlrSldTotAtlPgtoTit":"","SitTitPgto":"7","DtHrDDA":"2023-06-20T14:53:18","DtMovto":"2023-06-20"}},{"ValorCalculado": {"valorTituloOriginal":"150150.12","valorTotalCobrar":"185797.18","valorDescontoCalculado":"0","valorJurosCalculado":"35647.06","valorMultaCalculado":"0","valorAbatimento":"0.00","dataOperacao":"20/06/2023","codigoerro":"","descricaoerro":""}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BANCO ARBI S.A."}}]"""
                val validation = arbiAdapter.convertToValidationResponse(response)
                validation.getStatus() shouldBe BillValidationStatus.AlreadyPaid
                validation.billRegisterData!!.amountPaid shouldBe 18579718L
            }

            @Test
            fun `deve retonar o total pago igual nulo se for um boleto já baixado`() {
                val response =
                    """[{"DDA0110R1": {"NumCtrlPart":"DDA20230620000168066","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20230620000580417463","NumIdentcTit":"2017040500000409808","NumRefAtlCadTit":"1491394235954000405","NumSeqAtlzCadTit":"1","DtHrSitTit":"2018-06-03T04:50:17","ISPBPartDestinatario":"02038232","CodPartDestinatario":"756","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"7912816000130","Nom_RzSocBenfcrioOr":"VIANA MOVEIS E ELETRODOMESTICOS LTDA ME","NomFantsBenfcrioOr":"Edner","LogradBenfcrioOr":"Endereco","CidBenfcrioOr":"Cidade","UFBenfcrioOr":"SC","CEPBenfcrioOr":"89670000","TpPessoaBenfcrioFinl":"","CNPJ_CPFBenfcrioFinl":"","Nom_RzSocBenfcrioFinl":"","NomFantsBenfcrioFinl":"","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"59346957972","Nom_RzSocPagdr":"000051 - VANIA MARIA COVOLAN PICINATTO","NomFantsPagdr":"","TpIdentcSacdrAvalst":"","IdentcSacdrAvalst":"","Nom_RzSocSacdrAvalst":"","CodMoedaCNAB":"09","NumCodBarras":"75699772300150150121306601017321500170191037","NumLinhaDigtvl":"75691306640101732150401701910372977230015015012","DtVencTit":"2017-03-30","VlrTit":"150150.12","CodEspTit":"2","QtdDiaPrott":"","DtLimPgtoTit":"2030-06-05","IndrBloqPgto":"N","IndrPgtoParcl":"N","QtdPgtoParcl":"","VlrAbattTit":"0.00","DtJurosTit":"2017-03-31","CodJurosTit":"8","Vlr_PercJurosTit":"0.32000","CodMultaTit":"3","DtMultaTit":"","Vlr_PercMultaTit":"0.00","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"","Vlr_PercMinTit":"","TpVlr_PercMaxTit":"","Vlr_PercMaxTit":"","TpModlCalc":"01","TpAutcRecbtVlrDivgte":"1","QtdPgtoParclRegtd":"","VlrSldTotAtlPgtoTit":"","SitTitPgto":"1","DtHrDDA":"2023-06-20T14:53:18","DtMovto":"2023-06-20"}},{"ValorCalculado": {"valorTituloOriginal":"150150.12","valorTotalCobrar":"185797.18","valorDescontoCalculado":"0","valorJurosCalculado":"35647.06","valorMultaCalculado":"0","valorAbatimento":"0.00","dataOperacao":"20/06/2023","codigoerro":"","descricaoerro":""}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BANCO ARBI S.A."}}]"""
                val validation = arbiAdapter.convertToValidationResponse(response)
                validation.getStatus() shouldBe BillValidationStatus.NotPayable
                validation.billRegisterData!!.amountPaid.shouldBeNull()
            }

            @Test
            fun `deve retornar null como total pago se for um boleto ainda não pago ou não baixado`() {
                val response =
                    """[{"DDA0110R1": {"NumCtrlPart":"DDA20230620000168066","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20230620000580417463","NumIdentcTit":"2017040500000409808","NumRefAtlCadTit":"1491394235954000405","NumSeqAtlzCadTit":"1","DtHrSitTit":"2018-06-03T04:50:17","ISPBPartDestinatario":"02038232","CodPartDestinatario":"756","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"7912816000130","Nom_RzSocBenfcrioOr":"VIANA MOVEIS E ELETRODOMESTICOS LTDA ME","NomFantsBenfcrioOr":"Edner","LogradBenfcrioOr":"Endereco","CidBenfcrioOr":"Cidade","UFBenfcrioOr":"SC","CEPBenfcrioOr":"89670000","TpPessoaBenfcrioFinl":"","CNPJ_CPFBenfcrioFinl":"","Nom_RzSocBenfcrioFinl":"","NomFantsBenfcrioFinl":"","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"59346957972","Nom_RzSocPagdr":"000051 - VANIA MARIA COVOLAN PICINATTO","NomFantsPagdr":"","TpIdentcSacdrAvalst":"","IdentcSacdrAvalst":"","Nom_RzSocSacdrAvalst":"","CodMoedaCNAB":"09","NumCodBarras":"75699772300150150121306601017321500170191037","NumLinhaDigtvl":"75691306640101732150401701910372977230015015012","DtVencTit":"2017-03-30","VlrTit":"150150.12","CodEspTit":"2","QtdDiaPrott":"","DtLimPgtoTit":"2030-06-05","IndrBloqPgto":"N","IndrPgtoParcl":"N","QtdPgtoParcl":"","VlrAbattTit":"0.00","DtJurosTit":"2017-03-31","CodJurosTit":"8","Vlr_PercJurosTit":"0.32000","CodMultaTit":"3","DtMultaTit":"","Vlr_PercMultaTit":"0.00","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"","Vlr_PercMinTit":"","TpVlr_PercMaxTit":"","Vlr_PercMaxTit":"","TpModlCalc":"01","TpAutcRecbtVlrDivgte":"1","QtdPgtoParclRegtd":"","VlrSldTotAtlPgtoTit":"","SitTitPgto":"12","DtHrDDA":"2023-06-20T14:53:18","DtMovto":"2023-06-20"}},{"ValorCalculado": {"valorTituloOriginal":"150150.12","valorTotalCobrar":"185797.18","valorDescontoCalculado":"0","valorJurosCalculado":"35647.06","valorMultaCalculado":"0","valorAbatimento":"0.00","dataOperacao":"20/06/2023","codigoerro":"","descricaoerro":""}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BANCO ARBI S.A."}}]"""
                val validation = arbiAdapter.convertToValidationResponse(response)
                validation.getStatus() shouldBe BillValidationStatus.Payable
                validation.billRegisterData!!.amountPaid.shouldBeNull()
            }
        }
    }

    @Test
    fun `should return already paid when nu bank credit card is active but has amount paid`() {
        val response =
            """[{"DDA0110R1": {"VlrBaixaEftTit":"100", "NumCtrlPart":"DDA20220222007889619","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20220222000175619683","NumIdentcTit":"2022020105131025653","NumRefAtlCadTit":"1643772523554000201","NumSeqAtlzCadTit":"1","DtHrSitTit":"2022-02-02T00:28:43","ISPBPartDestinatario":"********","CodPartDestinatario":"237","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"**************","Nom_RzSocBenfcrioOr":"NU PAGAMENTOS S.A.","NomFantsBenfcrioOr":"NU PAGAMENTOS S.A.","LogradBenfcrioOr":"R CAPOTE VALENTE","CidBenfcrioOr":"SAO PAULO","UFBenfcrioOr":"SP","CEPBenfcrioOr":"5409000","TpPessoaBenfcrioFinl":"J","CNPJ_CPFBenfcrioFinl":"**************","Nom_RzSocBenfcrioFinl":"não deve aparecer como recipient name","NomFantsBenfcrioFinl":"não deve aparecer como recipient name","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"***********","Nom_RzSocPagdr":"VICTOR RIBEIRO MAC MAHON","NomFantsPagdr":"","TpIdentcSacdrAvalst":"","IdentcSacdrAvalst":"","Nom_RzSocSacdrAvalst":"","CodMoedaCNAB":"09","NumCodBarras":"23791889100000295893381260079488711300060920","NumLinhaDigtvl":"23793381286007948871813000609209188910000029589","DtVencTit":"2022-02-09","VlrTit":"295.89","CodEspTit":"1","QtdDiaPrott":"","DtLimPgtoTit":"2022-03-06","IndrBloqPgto":"N","IndrPgtoParcl":"N","QtdPgtoParcl":"","VlrAbattTit":"0.00","DtJurosTit":"","CodJurosTit":"5","Vlr_PercJurosTit":"0.00","CodMultaTit":"3","DtMultaTit":"","Vlr_PercMultaTit":"0.00","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"","Vlr_PercMinTit":"","TpVlr_PercMaxTit":"","Vlr_PercMaxTit":"","TpModlCalc":"01","TpAutcRecbtVlrDivgte":"1","QtdPgtoParclRegtd":"","VlrSldTotAtlPgtoTit":"","SitTitPgto":"12","DtHrDDA":"2022-02-22T11:11:22","DtMovto":"2022-02-22"}},{"ValorCalculado": {"valorTituloOriginal":"295.89","valorTotalCobrar":"295.89","valorDescontoCalculado":"0","valorJurosCalculado":"0","valorMultaCalculado":"0","valorAbatimento":"0.00","dataOperacao":"22/02/2022","codigoerro":"","descricaoerro":""}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BCO ARBI S.A."}}]"""
        val validation = arbiAdapter.convertToValidationResponse(response)
        validation.getStatus() shouldBe BillValidationStatus.AlreadyPaid
    }

    @Test
    fun convertToValidationResponseWithSacadorAvalista() {
        val response =
            """[{"DDA0110R1": {"NumCtrlPart":"DDA20200522000341775","ISPBPartRecbdrPrincipal":"********","ISPBPartRecbdrAdmtd":"********","NumCtrlDDA":"20200522000771184702","NumIdentcTit":"2020042207658657449","NumRefAtlCadTit":"1588742057601000505","NumSeqAtlzCadTit":"2","DtHrSitTit":"2020-05-06T02:14:17","ISPBPartDestinatario":"90400888","CodPartDestinatario":"033","TpPessoaBenfcrioOr":"J","CNPJ_CPFBenfcrioOr":"**************","Nom_RzSocBenfcrioOr":"ASSOCIACAO BRITANICA DE EDUCACAO","NomFantsBenfcrioOr":"ASSOCIACAO BRITANICA DE EDUCACAO","LogradBenfcrioOr":"R DA MATRIZ 76 86","CidBenfcrioOr":"RIO DE JANEIRO","UFBenfcrioOr":"RJ","CEPBenfcrioOr":"22260100","TpPessoaBenfcrioFinl":"J","CNPJ_CPFBenfcrioFinl":"**************","Nom_RzSocBenfcrioFinl":"ASSOCIACAO BRITANICA DE EDUCACAO","NomFantsBenfcrioFinl":"ASSOCIACAO BRITANICA DE EDUCACAO","TpPessoaPagdr":"F","CNPJ_CPFPagdr":"8650436714","Nom_RzSocPagdr":"OLIVIA PIMENTA DE OLIVEIRA CASTRO","NomFantsPagdr":"OLIVIA PIMENTA DE OLIVEIRA CASTRO","TpIdentcSacdrAvalst":"2","IdentcSacdrAvalst":"010573521000191","Nom_RzSocSacdrAvalst":"ESCOLA BRITANICA","CodMoedaCNAB":"09","NumCodBarras":"03391824600000843809847075800000013008220101","NumLinhaDigtvl":"03399847007580000001230082201010182460000084380","DtVencTit":"2020-05-05","VlrTit":"843.80","CodEspTit":"15","QtdDiaPrott":"","DtLimPgtoTit":"2020-08-12","IndrBloqPgto":"N","IndrPgtoParcl":"N","QtdPgtoParcl":"","VlrAbattTit":"0.00","DtJurosTit":"2020-05-06","CodJurosTit":"1","Vlr_PercJurosTit":"0.28","CodMultaTit":"1","DtMultaTit":"2020-05-06","Vlr_PercMultaTit":"16.88","DtDesctTit":"","CodDesctTit":"0","Vlr_PercDesctTit":"0.00","TpVlr_PercMinTit":"P","Vlr_PercMinTit":"100.00000","TpVlr_PercMaxTit":"P","Vlr_PercMaxTit":"100.00000","TpModlCalc":"01","TpAutcRecbtVlrDivgte":"3","QtdPgtoParclRegtd":"","VlrSldTotAtlPgtoTit":"","NumIdentcBaixaEft":"2020050500599955100","NumRefAtlBaixaEft":"1588742057257000505","NumSeqAtlzBaixaEft":"1","DtProcBaixaEft":"2020-05-05","DtHrProcBaixaEft":"2020-05-06T00:20:13","VlrBaixaEftTit":"843.80","NumCodBarrasBaixaEft":"03391824600000843809847075800000013008220101","CanPgto":"","MeioPgto":"","DtHrSitBaixaEft":"2020-05-06T02:14:17","SitTitPgto":"12","DtHrDDA":"2020-05-22T15:33:04","DtMovto":"2020-05-22"}},{"ValorCalculado": {"valorTituloOriginal":"843.80","valorTotalCobrar":"865.44","valorDescontoCalculado":"0","valorJurosCalculado":"4.76","valorMultaCalculado":"16.88","valorAbatimento":"0.00","dataOperacao":"22/05/2020"}},{"Emissor":{"ISPBPartRecbdr":"********","Codigo":"213","razaosocialemissor":"BANCO ARBI S.A."}}]"""
        val validation = arbiAdapter.convertToValidationResponse(response)
        assertEquals("ESCOLA BRITANICA", validation.billRegisterData!!.recipient!!.name)
        assertEquals("10573521000191", validation.billRegisterData!!.recipient!!.document)

        validation.billRegisterData!!.fichaCompensacaoType shouldBe FichaCompensacaoType.TS_TRIPLICATA_DE_SERVICO
    }

    @ParameterizedTest
    @MethodSource("valuesProvider")
    fun convertTolong(value: String, expectedValue: Long) {
        assertEquals(expectedValue, convertToLong(value))
    }

    @Test
    fun convertToString() {
        assertEquals("180457.60", convertToString(18045760))
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "2020-09-07,2020-09-04", "2020-09-05,2020-09-04", "2020-09-06,2020-09-04", "2020-09-04,2020-09-04",
            "2020-09-08,2020-09-08", "2021-01-01,2020-12-31",
        ],
    )
    fun `Should return correct first past valid date`(input: String, expected: String) {
        val holidays = listOf(
            LocalDate.of(2020, 9, 7),
            LocalDate.of(2020, 10, 12),
            LocalDate.of(2020, 11, 2),
            LocalDate.of(2020, 12, 25),
            LocalDate.of(2021, 1, 1),
        )

        withHolidays(*holidays.toTypedArray()) {
            val date = LocalDate.parse(input, ISO_DATE)
            withGivenDateTime(ZonedDateTime.of(date, LocalTime.NOON, brazilTimeZone)) {
                getScheduleDate() shouldBe expected
            }
        }
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "2020-09-05,2020-09-08",
            "2020-09-06,2020-09-08",
            "2020-09-07,2020-09-08",
            "2020-09-04,2020-09-04",
            "2020-09-08,2020-09-08",
            "2021-01-01,2021-01-04",
        ],
    )
    fun `Should return correct first next valid date`(input: String, expected: String) {
        val holidays = listOf(
            LocalDate.of(2020, 9, 7),
            LocalDate.of(2020, 10, 12),
            LocalDate.of(2020, 11, 2),
            LocalDate.of(2020, 12, 25),
            LocalDate.of(2021, 1, 1),
        )

        withHolidays(*holidays.toTypedArray()) {
            val date = LocalDate.parse(input, ISO_DATE)
            arbiAdapter.getFirstWorkingDate(date) shouldBe LocalDate.parse(expected)
        }
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "2020-09-05,17,00,2020-09-08",
            "2020-09-05,18,00,2020-09-08",
            "2020-09-06,17,00,2020-09-08",
            "2020-09-06,18,00,2020-09-08",
            "2020-09-07,17,00,2020-09-08",
            "2020-09-07,18,00,2020-09-08",
            "2020-09-04,17,00,2020-09-04",
            "2020-09-04,18,00,2020-09-08",
            "2020-09-08,17,00,2020-09-08",
            "2020-09-08,18,00,2020-09-09",
            "2021-01-01,17,00,2021-01-04",
            "2021-01-01,18,00,2021-01-04",
        ],
    )
    fun `Should return correct first next valid date based on working time`(
        inputDate: String,
        inputHour: Int,
        inputMinute: Int,
        expected: String,
    ) {
        val holidays = listOf(
            LocalDate.of(2020, 9, 7),
            LocalDate.of(2020, 10, 12),
            LocalDate.of(2020, 11, 2),
            LocalDate.of(2020, 12, 25),
            LocalDate.of(2021, 1, 1),
        )

        withHolidays(*holidays.toTypedArray()) {
            val time = LocalTime.of(inputHour, inputMinute)
            val date = LocalDate.parse(inputDate, ISO_DATE)
            withGivenDateTime(ZonedDateTime.of(date, time, brazilTimeZone)) {
                arbiAdapter.getFirstWorkingPaymentDate() shouldBe expected
            }
        }
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "776574000156,2,00776574000156",
            "7131174785,1,07131174785",
            "0000776574000156,2,00776574000156",
            "00007131174785,1,07131174785",
            "00776574000156,2,00776574000156",
            "07131174785,1,07131174785",
        ],
    )
    fun `should format document with fixed length`(document: String, type: String, expected: String) {
        val formattedDocument = formatDocumentSacadorAvalista(document, type)

        formattedDocument shouldBe expected
    }

    @Test
    fun `should return UNKNOWN on a success without a response body`() {
        mockRetrieveDomainTO()

        val response = spyk(HttpResponse.created("")) {
            every { getBody(Argument.listOf(ArbiResponseTO::class.java)) } throws ClassCastException("")
        }
        mockRetrieveArbiResponseTOError(HttpClientResponseException("Teste", response))

        val transfer = arbiAdapter.transfer("", "", 0L, BankOperationId(""))
        transfer.status shouldBe BankOperationStatus.UNKNOWN
    }

    @Test
    fun `should return LocalDates before limit date`() {
        val now = getLocalDate()
        val expectedLocalDate = now.minusDays(1)
        val ofPattern = DateTimeFormatter.ofPattern("dd/MM/yyyy")
        val a: List<DomainTO> = listOf(
            DomainTO(12, "Feriado FOO", expectedLocalDate.format(ofPattern)),
            DomainTO(12, "Feriado FOO", now.format(ofPattern)),
            DomainTO(12, "Feriado FOO", now.plusMonths(1).format(ofPattern)),
        )
        val result = a.mapUntilDate(now)
        result shouldHaveSize 2
        result[0] shouldBe expectedLocalDate
        result[1] shouldBe now
    }

    @Test
    fun `should return Success on successful invoice transfer`() {
        val bankOperationId = BankOperationId("fakeId")
        val authentication = "STR20210803004115962"

        mockRetrieveDomainTO()

        mockRetrieveArbiResponseTO(
            ArbiResponseTO(
                idTransacao = 2,
                resultado = authentication,
                idStatus = 201,
                idRequisicaoParceiro = bankOperationId.value,
                idModulo = 1,
                idRequisicaoArbi = "mock",
                descricaoStatus = "Sucesso",
            ),
        )

        val recipient = Recipient(name = "Eduardo", document = "***********", bankAccount = bankAccount)

        val now = ZonedDateTime.of(LocalDateTime.of(2021, 9, 9, 12, 0), brazilTimeZone)
        withGivenDateTime(now) {
            val result = arbiTedAdapter.transfer(
                originAccountNo = "123456",
                recipient = recipient,
                totalAmount = 0L,
            )

            with(result) {
                status shouldBe DirectTEDStatus.Success
                authentication shouldBe authentication
                amount shouldBe 0
                gateway shouldBe FinancialServiceGateway.ARBI
                settleDate shouldBe now.toLocalDate()
            }
        }
    }

    @Test
    fun `should return Unknown on invoice transfer with status 504`() {
        mockRetrieveDomainTO()

        mockRetrieveArbiResponseTOError(
            HttpClientResponseException(
                "gateway timeout",
                HttpResponseFactory.INSTANCE.status(
                    HttpStatus.GATEWAY_TIMEOUT,
                    listOf(
                        ArbiResponseTO(
                            idTransacao = 2,
                            resultado = "",
                            idStatus = 504,
                            idRequisicaoParceiro = "",
                            idModulo = 1,
                            idRequisicaoArbi = "mock",
                            descricaoStatus = "Erro",
                        ),
                    ),
                ),
            ),
        )

        val recipient = Recipient(name = "Eduardo", document = "***********", bankAccount = bankAccount)

        val result = arbiTedAdapter.transfer(
            originAccountNo = "123456",
            recipient = recipient,
            totalAmount = 0L,
        )

        result.status shouldBe DirectTEDStatus.Unknown
        result.authentication shouldBe null
        result.gateway shouldBe FinancialServiceGateway.ARBI
    }

    @Test
    fun `should return Failure on invoice transfer with unmapped status`() {
        mockRetrieveDomainTO()

        val httpStatus = HttpStatus.UNPROCESSABLE_ENTITY

        mockRetrieveArbiResponseTOError(
            HttpClientResponseException(
                httpStatus.name,
                HttpResponseFactory.INSTANCE.status(
                    httpStatus,
                    listOf(
                        ArbiResponseTO(
                            idTransacao = 2,
                            resultado = httpStatus.name,
                            idStatus = httpStatus.code,
                            idRequisicaoParceiro = "",
                            idModulo = 1,
                            idRequisicaoArbi = "mock",
                            descricaoStatus = "Erro",
                        ),
                    ),
                ),
            ),
        )

        val recipient = Recipient(name = "Eduardo", document = "***********", bankAccount = bankAccount)

        val result = arbiTedAdapter.transfer(
            originAccountNo = "123456",
            recipient = recipient,
            totalAmount = 0L,
        )

        result.status shouldBe DirectTEDStatus.Error
        result.authentication shouldBe null
        result.gateway shouldBe FinancialServiceGateway.ARBI
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "Saldo disponivel insuficiente.,INSUFFICIENT_FUNDS",
            "Hora/Minuto do agendamento fora do Limte para TED.,AFTER_HOURS",
            "Banco destino não cadastrado e/ou invalido.,INVALID_BANK_DATA",
        ],
    )
    fun `should return Failure on invoice transfer with mapped descricaoStatus`(
        descricaoStatus: String,
        failureTEDStatus: DirectTEDStatus.Failure,
    ) {
        mockRetrieveDomainTO()

        val httpStatus = HttpStatus.UNPROCESSABLE_ENTITY

        mockRetrieveArbiResponseTOError(
            HttpClientResponseException(
                httpStatus.name,
                HttpResponseFactory.INSTANCE.status(
                    httpStatus,
                    listOf(
                        ArbiResponseTO(
                            idTransacao = 2,
                            resultado = httpStatus.name,
                            idStatus = httpStatus.code,
                            idRequisicaoParceiro = "",
                            idModulo = 1,
                            idRequisicaoArbi = "mock",
                            descricaoStatus = descricaoStatus,
                        ),
                    ),
                ),
            ),
        )

        val recipient = Recipient(name = "Eduardo", document = "***********", bankAccount = bankAccount)

        val result = arbiTedAdapter.transfer(
            originAccountNo = "123456",
            recipient = recipient,
            totalAmount = 0L,
        )

        result.status shouldBe failureTEDStatus
        result.authentication shouldBe null
        result.gateway shouldBe FinancialServiceGateway.ARBI
    }

    @ParameterizedTest
    @MethodSource("undoneTed")
    fun `should return mapped status on invoice refunded with mapped descricaoStatus`(
        statusDescription: String,
        status: DirectTEDStatus,
    ) {
        val result = jacksonObjectMapper().writeValueAsString(
            TEDUndoneTO(
                dataCadastro = "",
                idRequisicaoArbiOriginal = "",
                idRequisicaoParceiroOriginal = "",
                statusLiquidacao = "Devolvida",
                motivoDevolucao = statusDescription,
                valor = "1.0",
                historico = "",
                numeroMovimentoDebito = "",
                numeroMovimentoCredito = "",
            ),
        )

        mockExchangeArbiResponseTO(
            HttpStatus.OK,
            ArbiResponseTO(
                idTransacao = 2,
                resultado = result,
                idStatus = 201,
                idRequisicaoParceiro = UUID.randomUUID().toString(),
                idModulo = 1,
                idRequisicaoArbi = "mock",
                descricaoStatus = "Sucesso",
            ),
        )

        val response = arbiTedAdapter.checkTEDUndone(
            amount = 100,
            startDate = getLocalDate(),
            endDate = getLocalDate(),
            originAccountNo = "1",
        )

        response.shouldHaveSize(1)
        response.first().status shouldBe status
    }

    @Test
    fun `should return Unknown on invoice transfer with exception`() {
        mockRetrieveDomainTO()

        mockRetrieveArbiResponseTOError(ReadTimeoutException.TIMEOUT_EXCEPTION)

        val recipient = Recipient(name = "Eduardo", document = "***********", bankAccount = bankAccount)

        val result = arbiTedAdapter.transfer(
            originAccountNo = "123456",
            recipient = recipient,
            totalAmount = 0L,
        )

        result.status shouldBe DirectTEDStatus.Unknown
        result.authentication shouldBe null
        result.gateway shouldBe FinancialServiceGateway.ARBI
    }

    @Test
    fun `should get bills from friday account with document`() {
        val queryDate = LocalDate.of(2021, 11, 1)

        val httpRequestSlot = slot<HttpRequest<DDATO>>()

        mockDDaResponseTO(httpRequestSlot)

        val bills =
            arbiAdapter.getBills(queryDate, "***********")

        bills.shouldNotBeEmpty()

        val httpRequest = httpRequestSlot.captured

        with(httpRequest.body.get().dda) {
            contaTitular shouldBe configuration.contaTitular
            inscricao shouldBe "***********"
            tipoPessoa shouldBe "F"
            dataVctoTitulo shouldBe queryDate.format(dateFormat)
        }
    }

    @Test
    fun `should throw exception when balance result is null`() {
        every {
            mockClient.retrieve(
                any<HttpRequest<CheckingRequestTO>>(),
                Argument.of(ArbiResponseTO::class.java),
                Argument.STRING,
            )
        } answers {
            Flowable.just(
                ArbiResponseTO(
                    idTransacao = 3,
                    resultado = null,
                    idStatus = 201,
                    idRequisicaoParceiro = "51a12856-714a-4640-bd5f-ea3bff71179d",
                    idModulo = 1,
                    idRequisicaoArbi = "151220211831246460@*************",
                    descricaoStatus = "Sucesso",
                ),
            )
        }

        assertThrows<ArbiAdapterException> {
            arbiAdapter.getBalance("123")
        }
    }

    @Test
    fun `quando o arbi retorna pedido pendente deve retornar uma lista vazia`() {
        val httpResponse = HttpClientResponseException(
            "",
            HttpResponseFactory.INSTANCE.status(
                HttpStatus.UNPROCESSABLE_ENTITY,
                listOf(
                    ArbiResponseTO(
                        idTransacao = 9,
                        resultado = """[{ "status":"Apto", "cpfCnpj": "$DOCUMENT", "descricao" : "Enviado" }]""",
                        idStatus = 422,
                        idRequisicaoParceiro = UUID.randomUUID().toString(),
                        idModulo = 2,
                        idRequisicaoArbi = "10620221831816238@5488560692659",
                        descricaoStatus = "Erro - Pedido pendendente para o pagador de CPF/CNPJ 34701685000115 Corrija a requisição e faça o reenvio.",
                    ),
                ),
            ),
        )

        every {
            mockClient.exchange(
                ofType<HttpRequest<DDAAgregadoTO>>(),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.listOf(ArbiResponseTO::class.java),
            )
        } answers {
            Flowable.error(httpResponse)
        }

        arbiAdapter.add(listOf(Document(DOCUMENT)), DocumentType.CPF).shouldBeEmpty()
    }

    @Test
    fun `quando o arbi retorna agregado existente com apenas um documento deve retornar este documento`() {
        val httpResponse = HttpClientResponseException(
            "",
            HttpResponseFactory.INSTANCE.status(
                HttpStatus.UNPROCESSABLE_ENTITY,
                listOf(
                    ArbiResponseTO(
                        idTransacao = 9,
                        resultado = "Requisição não Processada.",
                        idStatus = 422,
                        idRequisicaoParceiro = UUID.randomUUID().toString(),
                        idModulo = 2,
                        idRequisicaoArbi = "10620221831816238@5488560692659",
                        descricaoStatus = "Agregado ja existe.",
                    ),
                ),
            ),
        )

        every {
            mockClient.exchange(
                ofType<HttpRequest<DDAAgregadoTO>>(),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.listOf(ArbiResponseTO::class.java),
            )
        } answers {
            Flowable.error(httpResponse)
        }

        val documents = listOf(Document(DOCUMENT))
        arbiAdapter.add(documents, DocumentType.CPF) shouldContainExactly documents
    }

    @Test
    fun `quando o arbi retorna que todos os documentos existem no dda deve retornar uma lista com os documentos já adicionados`() {
        val arbiResponseTO = ArbiResponseTO(
            idTransacao = 9,
            resultado = """[
                            { "status":"Erro", "cpfCnpj": "$DOCUMENT_3", "descricao" : "Erro desconhecido" },
                            { "status":"Erro", "cpfCnpj": "$DOCUMENT", "descricao" : "Já existe/excluído na base." }
                        ]""",
            idStatus = 422,
            idRequisicaoParceiro = UUID.randomUUID().toString(),
            idModulo = 2,
            idRequisicaoArbi = "10620221831816238@5488560692659",
            descricaoStatus = "Erro - Inclusão/Exclusão de Contas e/ou Agregados não preenchido Corrija a requisição e faça o reenvio.",
        )
        val httpResponse = HttpClientResponseException(
            "",
            HttpResponseFactory.INSTANCE.status(HttpStatus.UNPROCESSABLE_ENTITY, listOf(arbiResponseTO)),
        )

        every {
            mockClient.exchange(
                ofType<HttpRequest<DDAAgregadoTO>>(),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.listOf(ArbiResponseTO::class.java),
            )
        } answers {
            Flowable.error(httpResponse)
        }

        arbiAdapter.add(listOf(Document(DOCUMENT)), DocumentType.CPF) shouldContainExactlyInAnyOrder listOf(Document(DOCUMENT))
    }

    @Test
    fun `quando o arbi retorna que alguns documentos foram adicionados deve retornar a lista com os documentos adicionados`() {
        val arbiResponseTO = ArbiResponseTO(
            idTransacao = 9,
            resultado = """[
                { "status":"Erro", "cpfCnpj": "$DOCUMENT", "descricao" : "Já existe/excluído na base." },
                { "status":"Apto", "cpfCnpj": "$DOCUMENT_2", "descricao" : "Enviado" },
                { "status":"Erro", "cpfCnpj": "$DOCUMENT_3", "descricao" : "Erro desconhecido" }
            ]""",
            idStatus = 206,
            idRequisicaoParceiro = UUID.randomUUID().toString(),
            idModulo = 2,
            idRequisicaoArbi = "10620221831816238@5488560692659",
            descricaoStatus = "Sucesso Parcial, veja a lista em resultado",
        )
        every {
            mockClient.exchange(
                ofType<HttpRequest<DDAAgregadoTO>>(),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.listOf(ArbiResponseTO::class.java),
            )
        } answers {
            Flowable.just(
                HttpResponse.status<MutableList<ArbiResponseTO>?>(HttpStatus.PARTIAL_CONTENT, "")
                    .body(listOf(arbiResponseTO)),
            )
        }

        arbiAdapter.add(
            listOf(
                Document(DOCUMENT),
                Document(DOCUMENT_2),
                Document(DOCUMENT_3),
            ),
            DocumentType.CPF,
        ) shouldContainExactlyInAnyOrder listOf(
            Document(DOCUMENT),
            Document(DOCUMENT_2),
        )
    }

    @Test
    fun `quando o arbi retorna que todos os documentos foram adicionados deve retornar a lista com todos os documentos enviados`() {
        val arbiResponseTO = ArbiResponseTO(
            idTransacao = 9,
            resultado = "Agregado(s) incluido",
            idStatus = 201,
            idRequisicaoParceiro = UUID.randomUUID().toString(),
            idModulo = 2,
            idRequisicaoArbi = "10620221831816238@5488560692659",
            descricaoStatus = "Sucesso",
        )
        every {
            mockClient.exchange(
                ofType<HttpRequest<DDAAgregadoTO>>(),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.listOf(ArbiResponseTO::class.java),
            )
        } answers {
            Flowable.just(HttpResponse.created(listOf(arbiResponseTO)))
        }

        arbiAdapter.add(
            listOf(
                Document(DOCUMENT),
                Document(DOCUMENT_2),
            ),
            DocumentType.CPF,
        ) shouldContainExactlyInAnyOrder listOf(
            Document(DOCUMENT),
            Document(DOCUMENT_2),
        )
    }

    @Test
    fun `should return empty statement list when arbi returns no statement items`() {
        every {
            mockClient.exchange(
                any<HttpRequest<CheckingRequestTO>>(),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.STRING,
            )
        } answers {
            Flowable.just(
                HttpResponse.ok(
                    listOf(
                        ArbiResponseTO(
                            idTransacao = 4,
                            resultado = "Sem movimentos para o período informado.",
                            idStatus = 200,
                            idRequisicaoParceiro = "51a12856-714a-4640-bd5f-ea3bff71179d",
                            idModulo = 1,
                            idRequisicaoArbi = "151220211831246460@*************",
                            descricaoStatus = "Sem registros",
                        ),
                    ),
                ),
            )
        }

        arbiAdapter.getStatement(
            accountNumber = AccountNumber("123"),
            document = "***********",
            initialDate = getZonedDateTime(),
            endDate = getZonedDateTime(),
        ).items.shouldBeEmpty()
    }

    @Test
    fun `should return statement list when arbi returns statement items`() {
        every {
            mockClient.exchange(
                any<HttpRequest<CheckingRequestTO>>(),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.STRING,
            )
        } answers {
            Flowable.just(
                HttpResponse.created(
                    listOf(
                        ArbiResponseTO(
                            idTransacao = 4,
                            resultado = "{\"datamovimento\":\"23/02/2022\",\"conta\":\"**********\",\"natureza\":\"D\",\"historico\":\"00055-TED - REMESSA\",\"nromovimento\":\"*********\",\"valor\":\"890.00\",\"inscricaocontraparte\":\"\",\"nomecontraparte\":\"\",\"finalidade\":\"00019 ********** FRIDAY PAGAMENTOS DIGITAIS LTDA\",\"evento\":\"\",\"sisorigem\":\"APISC\",\"codhist\":\"00011\",\"dataatualizacao\":\"2022-02-23 18:13:39\",\"nrodocto\":\"0000001\"}",
                            idStatus = 200,
                            idRequisicaoParceiro = "51a12856-714a-4640-bd5f-ea3bff71179d",
                            idModulo = 1,
                            idRequisicaoArbi = "151220211831246460@*************",
                            descricaoStatus = "Sucesso",
                        ),
                        ArbiResponseTO(
                            idTransacao = 4,
                            resultado = "{\"datamovimento\":\"23/02/2022\",\"conta\":\"**********\",\"natureza\":\"D\",\"historico\":\"00055-TED - REMESSA\",\"nromovimento\":\"*********\",\"valor\":\"890.00\",\"inscricaocontraparte\":\"\",\"nomecontraparte\":\"\",\"finalidade\":\"**********dc25ec66-a7da-4426-a8fa-1b3458f4edf3\",\"evento\":\"\",\"sisorigem\":\"APISC\",\"codhist\":\"00011\",\"dataatualizacao\":\"2022-02-23 18:13:39\",\"nrodocto\":\"0000001\"}",
                            idStatus = 200,
                            idRequisicaoParceiro = "51a12856-714a-4640-bd5f-ea3bff71179d",
                            idModulo = 1,
                            idRequisicaoArbi = "151220211831246460@*************",
                            descricaoStatus = "Sucesso",
                        ),
                    ),
                ),
            )
        }

        with(
            arbiAdapter.getStatement(
                accountNumber = AccountNumber("123"),
                document = "***********",
                initialDate = getZonedDateTime(),
                endDate = getZonedDateTime(),
            ),
        ) {
            items shouldHaveSize 2
            items[0].operationNumber shouldBe "*********"
            items[0].counterpartAccountNo shouldBe "**********"
            items[1].counterpartAccountNo shouldBe "**********"
        }
    }

    @Test
    fun `should return false when transfer status is not found in transacao 4 and transacao 5`() {
        every {
            mockClient.exchange(
                any<HttpRequest<CheckingRequestTO>>(),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.listOf(String::class.java),
            )
        } answers {
            Flowable.just(
                HttpResponse.ok(
                    listOf(
                        ArbiResponseTO(
                            idTransacao = 5,
                            resultado = "Sem requisicões/liquidações para o período informado.",
                            idStatus = 200,
                            idRequisicaoParceiro = "51a12856-714a-4640-bd5f-ea3bff71179d",
                            idModulo = 1,
                            idRequisicaoArbi = "151220211831246460@*************",
                            descricaoStatus = "Sem registros",
                        ),
                    ),
                ),
            )
        }

        every {
            mockClient.exchange(
                any<HttpRequest<CheckingRequestTO>>(),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.STRING,
            )
        } answers {
            Flowable.just(
                HttpResponse.ok(
                    listOf(
                        ArbiResponseTO(
                            idTransacao = 4,
                            resultado = "Sem movimentos para o período informado.",
                            idStatus = 200,
                            idRequisicaoParceiro = "51a12856-714a-4640-bd5f-ea3bff71179d",
                            idModulo = 1,
                            idRequisicaoArbi = "151220211831246460@*************",
                            descricaoStatus = "Sem registros",
                        ),
                    ),
                ),
            )
        }

        arbiAdapter.checkTransferStatus(
            idRequisicaoParceiroOriginal = "51a12856-714a-4640-bd5f-ea3bff71179d",
            startDate = getLocalDate(),
            endDate = getLocalDate(),
            originAccountNo = "123",
        ) shouldBe false
    }

    @Test
    fun `should fallback to transacao 4 when transfer status is not found on transacao 5`() {
        every {
            mockClient.exchange(
                any<HttpRequest<CheckingRequestTO>>(),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.listOf(String::class.java),
            )
        } answers {
            Flowable.just(
                HttpResponse.ok(
                    listOf(
                        ArbiResponseTO(
                            idTransacao = 5,
                            resultado = "Sem requisicões/liquidações para o período informado.",
                            idStatus = 200,
                            idRequisicaoParceiro = "51a12856-714a-4640-bd5f-ea3bff71179d",
                            idModulo = 1,
                            idRequisicaoArbi = "151220211831246460@*************",
                            descricaoStatus = "Sem registros",
                        ),
                    ),
                ),
            )
        }

        every {
            mockClient.exchange(
                any<HttpRequest<CheckingRequestTO>>(),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.STRING,
            )
        } answers {
            Flowable.just(
                HttpResponse.ok(
                    listOf(
                        ArbiResponseTO(
                            idTransacao = 4,
                            resultado = "{\"datamovimento\":\"23/02/2022\",\"conta\":\"**********\",\"natureza\":\"D\",\"historico\":\"00055-TED - REMESSA\",\"nromovimento\":\"*********\",\"valor\":\"890.00\",\"inscricaocontraparte\":\"\",\"nomecontraparte\":\"\",\"finalidade\":\"**********dc25ec66-a7da-4426-a8fa-1b3458f4edf5\",\"evento\":\"\",\"sisorigem\":\"APISC\",\"codhist\":\"00102\",\"dataatualizacao\":\"2022-02-23 18:13:39\",\"nrodocto\":\"0000001\"}",
                            idStatus = 200,
                            idRequisicaoParceiro = "51a12856-714a-4640-bd5f-ea3bff71179d",
                            idModulo = 1,
                            idRequisicaoArbi = "151220211831246460@*************",
                            descricaoStatus = "Sem registros",
                        ),
                        ArbiResponseTO(
                            idTransacao = 4,
                            resultado = "{\"datamovimento\":\"23/02/2022\",\"conta\":\"**********\",\"natureza\":\"D\",\"historico\":\"00055-TED - REMESSA\",\"nromovimento\":\"*********\",\"valor\":\"890.00\",\"inscricaocontraparte\":\"\",\"nomecontraparte\":\"\",\"finalidade\":\"**********dc25ec66-a7da-4426-a8fa-1b3458f4edf3\",\"evento\":\"\",\"sisorigem\":\"APISC\",\"codhist\":\"00102\",\"dataatualizacao\":\"2022-02-23 18:13:39\",\"nrodocto\":\"0000001\"}",
                            idStatus = 200,
                            idRequisicaoParceiro = "51a12856-714a-4640-bd5f-ea3bff71179d",
                            idModulo = 1,
                            idRequisicaoArbi = "151220211831246460@*************",
                            descricaoStatus = "Sem registros",
                        ),
                    ),
                ),
            )
        }

        arbiAdapter.checkTransferStatus(
            idRequisicaoParceiroOriginal = "OPERATION-dc25ec66-a7da-4426-a8fa-1b3458f4edf3",
            startDate = getLocalDate(),
            endDate = getLocalDate(),
            originAccountNo = "*********",
        ) shouldBe true
    }

    @Test
    fun `deve conseguir fazer o parse do payload do exemplo`() {
        val payload = """[
				{
					"idtransacao": 8,
					"resultado": "{\"inscricaosacado\":\"***********\",\"nomesacado\":\"\tLENISEA PEREIRA TOMASI\",\"banco\":\"077\",\"inscricaocedente\":\"**************\",\"nomecedente\":\"HIPERVALOR CONSULTORIA E COBRANCA L\",\"datavctotitulo\":\"28/05/2025\",\"valor\":\"721.25000\",\"codbarras\":\"07794109500000721250001112032066790285493527\",\"dataemissaotitulo\":\"28/04/2025\",\"linhadigitavel\":\"07790001161203206679202854935273410950000072125\",\"inscricaocedenteori\":\"**************\",\"nomecedenteori\":\"HIPERVALOR CONSULTORIA E COBRANCA L\",\"dthrsittitulo\":\"28/04/2025 11:51:04\",\"situacao\":\"Aberto\",\"seunumero\":\"********\",\"numidentcdda\":\"3025042800803563214\"}",
					"idstatus": 201,
					"idrequisicaoparceiro": "c0df3353-c4f4-4d48-8201-cff610592e1a",
					"idmodulo": 2,
					"idrequisicaoarbi": "**************@*************",
					"descricaostatus": "Sucesso"
				},
				{
					"idtransacao": 8,
					"resultado": "{\"inscricaosacado\":\"***********\",\"nomesacado\":\"6443 - LENISEA PEREIRA TOMASI\",\"banco\":\"001\",\"inscricaocedente\":\"\",\"nomecedente\":\"\",\"datavctotitulo\":\"07/05/2025\",\"valor\":\"90.51000\",\"codbarras\":\"00193107400000090510000002961755001379829517\",\"dataemissaotitulo\":\"24/12/2024\",\"linhadigitavel\":\"00190000090296175500213798295179310740000009051\",\"inscricaocedenteori\":\"60984473000100\",\"nomecedenteori\":\"CONSELHO FEDERAL DE FARMACIA\",\"dthrsittitulo\":\"24/12/2024 10:50:41\",\"situacao\":\"Aberto\",\"seunumero\":\"296175500137982\",\"numidentcdda\":\"3024122404980593028\"}",
					"idstatus": 201,
					"idrequisicaoparceiro": "c0df3353-c4f4-4d48-8201-cff610592e1a",
					"idmodulo": 2,
					"idrequisicaoarbi": "**************@*************",
					"descricaostatus": "Sucesso"
				},
				{
					"idtransacao": 8,
					"resultado": "{\"inscricaosacado\":\"***********\",\"nomesacado\":\"6443 - LENISEA PEREIRA TOMASI\",\"banco\":\"001\",\"inscricaocedente\":\"\",\"nomecedente\":\"\",\"datavctotitulo\":\"06/06/2025\",\"valor\":\"90.51000\",\"codbarras\":\"00196110400000090510000002961755001379830917\",\"dataemissaotitulo\":\"24/12/2024\",\"linhadigitavel\":\"00190000090296175500213798309178611040000009051\",\"inscricaocedenteori\":\"60984473000100\",\"nomecedenteori\":\"CONSELHO FEDERAL DE FARMACIA\",\"dthrsittitulo\":\"24/12/2024 10:54:07\",\"situacao\":\"Aberto\",\"seunumero\":\"296175500137983\",\"numidentcdda\":\"3024122400980989788\"}",
					"idstatus": 201,
					"idrequisicaoparceiro": "c0df3353-c4f4-4d48-8201-cff610592e1a",
					"idmodulo": 2,
					"idrequisicaoarbi": "**************@*************",
					"descricaostatus": "Sucesso"
				},
				{
					"idtransacao": 8,
					"resultado": "{\"inscricaosacado\":\"***********\",\"nomesacado\":\"6443 - LENISEA PEREIRA TOMASI\",\"banco\":\"001\",\"inscricaocedente\":\"\",\"nomecedente\":\"\",\"datavctotitulo\":\"07/07/2025\",\"valor\":\"90.53000\",\"codbarras\":\"00192113500000090530000002961755001379831717\",\"dataemissaotitulo\":\"24/12/2024\",\"linhadigitavel\":\"00190000090296175500213798317171211350000009053\",\"inscricaocedenteori\":\"60984473000100\",\"nomecedenteori\":\"CONSELHO FEDERAL DE FARMACIA\",\"dthrsittitulo\":\"24/12/2024 10:52:48\",\"situacao\":\"Aberto\",\"seunumero\":\"296175500137983\",\"numidentcdda\":\"3024122401980784358\"}",
					"idstatus": 201,
					"idrequisicaoparceiro": "c0df3353-c4f4-4d48-8201-cff610592e1a",
					"idmodulo": 2,
					"idrequisicaoarbi": "**************@*************",
					"descricaostatus": "Sucesso"
				},
				{
					"idtransacao": 8,
					"resultado": "{\"inscricaosacado\":\"***********\",\"nomesacado\":\"LENISEA P. TOMASI\",\"banco\":\"481\",\"inscricaocedente\":\"\",\"nomecedente\":\"\",\"datavctotitulo\":\"03/05/2025\",\"valor\":\"245.99000\",\"codbarras\":\"48194107000000245990000000005150*********014\",\"dataemissaotitulo\":\"29/05/2024\",\"linhadigitavel\":\"48190000030000515036262779470145410700000024599\",\"inscricaocedenteori\":\"**************\",\"nomecedenteori\":\"PJBANK PAGAMENTOS S A\",\"dthrsittitulo\":\"29/05/2024 08:38:28\",\"situacao\":\"Aberto\",\"seunumero\":\"*********\",\"numidentcdda\":\"3024052900342049936\"}",
					"idstatus": 201,
					"idrequisicaoparceiro": "c0df3353-c4f4-4d48-8201-cff610592e1a",
					"idmodulo": 2,
					"idrequisicaoarbi": "**************@*************",
					"descricaostatus": "Sucesso"
				},
				{
					"idtransacao": 8,
					"resultado": "{\"inscricaosacado\":\"***********\",\"nomesacado\":\"LENISEA PEREIRA TOMASI\",\"banco\":\"001\",\"inscricaocedente\":\"\",\"nomecedente\":\"\",\"datavctotitulo\":\"20/05/2025\",\"valor\":\"467.94000\",\"codbarras\":\"00196108700000467940000003113867**********17\",\"dataemissaotitulo\":\"17/04/2025\",\"linhadigitavel\":\"0019000009031138670**********177610870000046794\",\"inscricaocedenteori\":\"**************\",\"nomecedenteori\":\"COMPANHIA THERMAS DO RIO QUENTE\",\"dthrsittitulo\":\"17/04/2025 17:54:12\",\"situacao\":\"Aberto\",\"seunumero\":\"**********\",\"numidentcdda\":\"3025041702628716258\"}",
					"idstatus": 201,
					"idrequisicaoparceiro": "c0df3353-c4f4-4d48-8201-cff610592e1a",
					"idmodulo": 2,
					"idrequisicaoarbi": "**************@*************",
					"descricaostatus": "Sucesso"
				},
				{
					"idtransacao": 8,
					"resultado": "{\"inscricaosacado\":\"***********\",\"nomesacado\":\"LENISEA PEREIRA TOMASI\",\"banco\":\"104\",\"inscricaocedente\":\"\",\"nomecedente\":\"\",\"datavctotitulo\":\"05/05/2025\",\"valor\":\"0.00000\",\"codbarras\":\"10495983000000000008185409002119246558502458\",\"dataemissaotitulo\":\"29/08/2024\",\"linhadigitavel\":\"10498185430900211924765585024584598300000000000\",\"inscricaocedenteori\":\"00360305000104\",\"nomecedenteori\":\"CARTOES CAIXA ELO PF\",\"dthrsittitulo\":\"28/04/2025 10:12:20\",\"situacao\":\"Aberto\",\"seunumero\":\"00219265585\",\"numidentcdda\":\"3024082903311311789\"}",
					"idstatus": 201,
					"idrequisicaoparceiro": "c0df3353-c4f4-4d48-8201-cff610592e1a",
					"idmodulo": 2,
					"idrequisicaoarbi": "**************@*************",
					"descricaostatus": "Sucesso"
				},
				{
					"idtransacao": 8,
					"resultado": "{\"inscricaosacado\":\"***********\",\"nomesacado\":\"Lenisea Pereira Tomasi\",\"banco\":\"237\",\"inscricaocedente\":\"\",\"nomecedente\":\"\",\"datavctotitulo\":\"12/05/2025\",\"valor\":\"2125.00000\",\"codbarras\":\"23793107900002125002042092501639426900564690\",\"dataemissaotitulo\":\"16/04/2025\",\"linhadigitavel\":\"23792042059250163942869005646901310790000212500\",\"inscricaocedenteori\":\"51855716000101\",\"nomecedenteori\":\"RODOBENS ADM DE CONSORCIO\",\"dthrsittitulo\":\"17/04/2025 19:44:55\",\"situacao\":\"Aberto\",\"seunumero\":\"0003748128\",\"numidentcdda\":\"3025041705631089877\"}",
					"idstatus": 201,
					"idrequisicaoparceiro": "c0df3353-c4f4-4d48-8201-cff610592e1a",
					"idmodulo": 2,
					"idrequisicaoarbi": "**************@*************",
					"descricaostatus": "Sucesso"
				},
				{
					"idtransacao": 8,
					"resultado": "{\"inscricaosacado\":\"***********\",\"nomesacado\":\"LENISEA PEREIRA TOMASI\",\"banco\":\"393\",\"inscricaocedente\":\"59109165000149\",\"nomecedente\":\"BANCO VOLKSWAGEN S.A.\",\"datavctotitulo\":\"30/04/2025\",\"valor\":\"1998.75000\",\"codbarras\":\"39397106700001998750001100000001400179516775\",\"dataemissaotitulo\":\"04/10/2024\",\"linhadigitavel\":\"39390001170000000140401795167756710670000199875\",\"inscricaocedenteori\":\"59109165000149\",\"nomecedenteori\":\"BANCO VOLKSWAGEN S.A.\",\"dthrsittitulo\":\"08/04/2025 02:09:24\",\"situacao\":\"Aberto\",\"seunumero\":\"000005269677007\",\"numidentcdda\":\"3025040704395145137\"}",
					"idstatus": 201,
					"idrequisicaoparceiro": "c0df3353-c4f4-4d48-8201-cff610592e1a",
					"idmodulo": 2,
					"idrequisicaoarbi": "**************@*************",
					"descricaostatus": "Sucesso"
				},
				{
					"idtransacao": 8,
					"resultado": "{\"inscricaosacado\":\"***********\",\"nomesacado\":\"LENISEA PEREIRA TOMASI\",\"banco\":\"422\",\"inscricaocedente\":\"88649660000149\",\"nomecedente\":\"RECREIO DA JUVENTUDE\",\"datavctotitulo\":\"10/05/2025\",\"valor\":\"478.45000\",\"codbarras\":\"42291107700000478457039000058056946322197692\",\"dataemissaotitulo\":\"30/04/2025\",\"linhadigitavel\":\"42297039070005805694663221976929110770000047845\",\"inscricaocedenteori\":\"88649660000149\",\"nomecedenteori\":\"RECREIO DA JUVENTUDE\",\"dthrsittitulo\":\"30/04/2025 18:23:05\",\"situacao\":\"Aberto\",\"seunumero\":\"0063221976\",\"numidentcdda\":\"3025043004882158073\"}",
					"idstatus": 201,
					"idrequisicaoparceiro": "c0df3353-c4f4-4d48-8201-cff610592e1a",
					"idmodulo": 2,
					"idrequisicaoarbi": "**************@*************",
					"descricaostatus": "Sucesso"
				}
			]"""

        val response = parseListFrom<ArbiResponseTO>(payload)
        val mappedResponse: List<Map<String, String>> = response.map {
            jacksonObjectMapper().enable(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature())
                .readValue(
                    it.resultado,
                    Map::class.java,
                )
        } as List<Map<String, String>>

        mappedResponse.size shouldBe response.size
        mappedResponse.size shouldBe 10
    }

    private fun mockDDaResponseTO(httpRequestSlot: CapturingSlot<HttpRequest<DDATO>>) {
        every {
            mockClient.retrieve(
                capture(httpRequestSlot),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.STRING,
            )
        } answers {
            Flowable.just(listOf(buildArbiResponseTO()))
        }
    }

    private fun buildArbiResponseTO(): ArbiResponseTO {
        val bankOperationId = BankOperationId("fakeId")

        val result = getObjectMapper().writeValueAsString(
            mutableMapOf(
                "valor" to "1234",
                "codbarras" to FICHA_DE_COMPENSACAO_BARCODE,
                "linhadigitavel" to FICHA_DE_COMPENSACAO_DIGITABLE_LINE,
                "inscricaosacado" to "***********",
                "datavctotitulo" to "01/12/2021",
            ),
        )

        return ArbiResponseTO(
            idTransacao = 2,
            resultado = result,
            idStatus = 201,
            idRequisicaoParceiro = bankOperationId.value,
            idModulo = 1,
            idRequisicaoArbi = "mock",
            descricaoStatus = "Sucesso",
        )
    }

    private fun mockRetrieveDomainTO(domains: List<DomainTO> = listOf()) {
        every {
            mockClient.retrieve(
                ofType(HttpRequest::class),
                Argument.listOf(DomainTO::class.java),
                Argument.STRING,
            )
        } answers {
            Flowable.just(domains)
        }
    }

    private fun mockExchangeArbiResponseTO(status: HttpStatus, vararg response: ArbiResponseTO) {
        every {
            mockClient.exchange(
                ofType(HttpRequest::class),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.listOf(String::class.java),
            )
        } answers {
            Flowable.just(HttpResponseFactory.INSTANCE.status(status, response.asList()))
        }
    }

    private fun mockRetrieveArbiResponseTO(vararg response: ArbiResponseTO) {
        every {
            mockClient.retrieve(
                ofType(HttpRequest::class),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.listOf(ArbiResponseTO::class.java),
            )
        } answers {
            Flowable.just(response.asList())
        }
    }

    private fun mockRetrieveArbiResponseTOError(throwable: Throwable) {
        every {
            mockClient.retrieve(
                ofType(HttpRequest::class),
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.listOf(ArbiResponseTO::class.java),
            )
        } answers {
            Flowable.error(throwable)
        }
    }

    companion object {
        @JvmStatic
        fun valuesProvider(): Stream<Arguments> {
            val nf = NumberFormat.getNumberInstance(Locale.US) as DecimalFormat
            nf.applyPattern("#.##")
            return (1..25).map {
                val randomNumber = Random.nextLong(0L, 10000000L)
                arguments(nf.format(randomNumber / 100.0), randomNumber)
            }.stream()
        }

        @JvmStatic
        fun undoneTed(): Stream<Arguments> {
            return Stream.of(
                arguments(
                    "1-Conta Destinatária do Crédito Encerrada",
                    DirectTEDStatus.Failure.INVALID_BANK_DATA,
                ),
                arguments(
                    "2-Agência ou Conta Destinatária do Crédito Inválida",
                    DirectTEDStatus.Failure.INVALID_BANK_DATA,
                ),
                arguments(
                    "3-Ausência ou Divergência na Indicação do CPF/CNPJ",
                    DirectTEDStatus.Failure.INVALID_BANK_DATA,
                ),
                arguments("5-Divergência na Titularidade", DirectTEDStatus.Failure.INVALID_BANK_DATA),
                arguments(
                    "31-CPF/CNPJ inapto junto à Receita Federal do Brasil",
                    DirectTEDStatus.Failure.INVALID_BANK_DATA,
                ),
                arguments(
                    "84-Conta destinatária do crédito inválida para o tipo de transação ou finalidade",
                    DirectTEDStatus.Failure.INVALID_BANK_DATA,
                ),
                arguments(
                    "61-Transferência supera limite para o tipo de conta destino",
                    DirectTEDStatus.Failure.NOT_ALLOWED,
                ),
                arguments(
                    "999-Erro generico",
                    DirectTEDStatus.Error,
                ),
            )
        }
    }
}