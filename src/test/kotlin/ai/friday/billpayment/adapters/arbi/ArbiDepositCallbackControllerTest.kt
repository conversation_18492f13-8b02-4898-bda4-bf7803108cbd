package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.CPF_SIZE
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.DefaultBankStatementItem
import ai.friday.billpayment.app.banking.ExternalBankAccountService
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.banking.OmnibusBankAccountConfiguration
import ai.friday.billpayment.app.banking.SynchronizeBankAccountTO
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.integrations.ConfirmSweepingCashInError
import ai.friday.billpayment.app.integrations.EndToEnd
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.ModattaProvider
import ai.friday.billpayment.app.integrations.SweepingAccountServiceInterface
import ai.friday.billpayment.app.journey.UserJourneyEvent
import ai.friday.billpayment.app.journey.UserJourneyService
import ai.friday.billpayment.app.modatta.ModattaDepositCallback
import ai.friday.billpayment.app.onepixpay.EndToEndOnePixPayId
import ai.friday.billpayment.app.onepixpay.OnePixPayId
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.onepixpay.UUIDOnePixPayId
import ai.friday.billpayment.fixture.AccountPaymentMethodFixture
import ai.friday.billpayment.fixture.BalanceAccountPaymentMethodFixture
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.morning.date.brazilTimeZone
import arrow.core.Either
import arrow.core.left
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.http.HttpStatus
import io.mockk.Runs
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import java.math.BigInteger
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

internal class ArbiDepositCallbackControllerTest {
    private val internalBankService: InternalBankService = mockk {
        every {
            synchronizePixBankStatementItem(any(), any(), any(), any())
        } returns Unit
        every {
            getCashInByPeriod(any(), any(), any())
        } returns 10_000_01
    }

    private val externalBankAccountService: ExternalBankAccountService = mockk()
    private val accountService: AccountService = mockk()
    private val modattaProvider: ModattaProvider = mockk()
    private val messagePublisher: MessagePublisher = mockk {
        every { sendMessage(any(), any()) } returns Unit
    }
    private val omnibusBankAccountConfiguration: OmnibusBankAccountConfiguration = mockk() {
        every {
            isOmnibusBankAccount(any(), any(), any())
        } returns false
    }
    private val userJourneyService: UserJourneyService = mockk() {
        every { trackEventAsync(any(), any()) } returns Unit
    }

    val cnpjModatta = "**************"

    private val onePixPayService = mockk<OnePixPayService>() {
        every { process(any<AccountNumber>(), any(), any()) } just Runs
    }

    private val sweepingAccountService = mockk<SweepingAccountServiceInterface> {
        every { confirmSweepingCashIn(any()) } answers {
            ConfirmSweepingCashInError.ItemNotFound(firstArg()).left()
        }
    }

    private val arbiDepositCallbackController = ArbiDepositCallbackController(
        internalBankService = internalBankService,
        externalBankAccountService = externalBankAccountService,
        modattaProvider = modattaProvider,
        accountService = accountService,
        omnibusBankAccountConfiguration = omnibusBankAccountConfiguration,
        userJourneyService = userJourneyService,
        messagePublisher = messagePublisher,
        pixDepositNotificationQueue = "test-synchronize-queue",
        cashInReceivedQueue = "cash-in-received",
        onePixPayService = onePixPayService,
        sweepingAccountService = sweepingAccountService,
        billEventDBRepository = mockk(),
        walletService = mockk(),
    )

    private val bankAccount = BankAccount(
        accountType = AccountType.CHECKING,
        bankNo = 213,
        routingNo = 1,
        accountNo = BigInteger("1"),
        accountDv = "1",
        document = "***********",
    )

    private val parte = ArbiPixPart(
        codIspb = "********",
        codAgencia = bankAccount.routingNo.toString(),
        digitoAgencia = null,
        nroConta = bankAccount.buildFullAccountNumber(),
        tipoConta = "CACC",
        cpfCnpj = bankAccount.document,
        nome = "Joe Montana",
        nomeInstituicao = "BCO ARBI S.A.",
    )

    private val contraParte = parte.copy(nroConta = "0001234", cpfCnpj = "***********")

    private val mensagem = ArbiPixMovementNotificationMessage(
        parte = parte,
        contraParte = contraParte,
        endToEnd = "E********202205161316rbh9xGerhTc",
        tipo = ArbiPixMovementType.CREDITO_EXTERNO,
        valor = 999f,
        dataHoraMovimento = "2022-05-16T10:16:07.257767",
        chave = "<EMAIL>",
        nroMovimento = "*********",
        origemMovimento = ArbiPixMovementOrigin.QR_CODE,
        naturezaMovimento = ArbiPixMovementNature.C,
        indDevolucao = false,
        codOperacao = "00267",
        ehAgendado = false,
        idIdempotente = "e40a97dc-966b-4ec5-abf4-e1a45959dcff",
        canalEntrada = null,
        indExterno = false,
        finalidadeDaTransacao = null,
        txId = "NuC6M4J5HUf4AjEV361GAr",
        reasons = listOf(),
        endToEndOriginal = null,
        campoLivre = "teste",
        infoDevolucao = null,
        infoErro = null,
        campoExtra = null,
        idDevolucao = null,
        cnpjIniciadoraPagamento = "59787242000110",
        uuidBloqueioDevolucaoEspecial = null,
        uuidSolicitacaoDevolucaoEspecial = null,
        motivoMED = null,
        prestadorDoServicoDeSaque = null,
        modalidadeAgente = null,
        valorCompra = null,
        valorTroco = null,
        valorSaque = null,
    )

    private val despositTedCallbackRequest = ArbiCallbackRequest(
        valor = 100,
        codHistorico = "00056",
        descricao = "**********.00",
        bancoorigem = "",
        agenciaorigem = "",
        contaorigem = "",
        inscricao = DOCUMENT,
        nome = "",
        conta = ArbiCallbackContaRequest(banco = "213", agencia = "00019", conta = "0000011"),
    )

    @BeforeEach
    fun setUp() {
        arbiDepositCallbackController.cnpjModatta = cnpjModatta
        arbiDepositCallbackController.modattaBankAccounts = listOf("**********", "**********", "**********")
    }

    @Test
    fun `Deve fazer o sync interno quando o operation number for maior que 7 dígitos`() {
        val account = setupAccount(type = UserAccountType.BASIC_ACCOUNT)
        val arbiPixCallbackRequest = setupRequest(account)

        every {
            internalBankService.getCashInByPeriod(any(), any(), any())
        } returns 10_000_01

        val response = arbiDepositCallbackController.arbiDepositPixCallback(getObjectMapper().writeValueAsString(arbiPixCallbackRequest))

        response.status shouldBe HttpStatus.NO_CONTENT

        verify { internalBankService.synchronizePixBankStatementItem(any(), any(), any(), any()) }
        verify(exactly = 0) { internalBankService.synchronizeBankAccount(any(), any(), any()) }
    }

    @Test
    fun `Deve fazer o sync externo quando o operation number for menor ou igual que 7 dígitos`() {
        val account = setupAccount(type = UserAccountType.BASIC_ACCOUNT)
        val arbiPixCallbackRequest = setupRequest(account, nroMovimento = "123456")

        val response = arbiDepositCallbackController.arbiDepositPixCallback(getObjectMapper().writeValueAsString(arbiPixCallbackRequest))

        response.status shouldBe HttpStatus.NO_CONTENT

        verify(exactly = 0) {
            internalBankService.synchronizePixBankStatementItem(any(), any(), any(), any())
            internalBankService.synchronizeBankAccount(any(), any(), any())
        }
        verify {
            messagePublisher.sendMessage(
                "test-synchronize-queue",
                SynchronizeBankAccountTO(
                    bankNo = 213L,
                    routingNo = mensagem.parte.codAgencia.toLong(),
                    fullAccountNumber = mensagem.parte.nroConta,
                ),
            )
        }
    }

    private fun setupAccount(type: UserAccountType): Account {
        val account = ACCOUNT.copy(type = type)

        every {
            accountService.findAccountIdByPhysicalBankAccountNo(any(), any(), any())
        } returns account.accountId
        every {
            accountService.findAccountById(account.accountId)
        } returns account
        every {
            accountService.findAccountByDocument(account.document)
        } returns account
        every {
            accountService.findPhysicalAccountPaymentMethod(any(), any(), any())
        } returns Either.Right(AccountPaymentMethodFixture.create(method = BalanceAccountPaymentMethodFixture.create()))

        return account
    }

    private fun setupRequest(
        account: Account,
        nroMovimento: String? = mensagem.nroMovimento,
        origemMovimento: ArbiPixMovementOrigin = ArbiPixMovementOrigin.MANUAL,
        parte: ArbiPixPart = mensagem.parte,
    ) = ArbiPixMovementCallbackRequest(
        cpfCnpj = account.document,
        mensagem = mensagem.copy(
            contraParte = contraParte.copy(cpfCnpj = account.document),
            nroMovimento = nroMovimento,
            origemMovimento = origemMovimento,
            parte = parte,
        ),
    )

    @Test
    fun `quando for um depósito pix não deve alarmar se o limite de cash in for ultrapassado por uma conta full`() {
        val depositCallbackController = spyk(arbiDepositCallbackController)

        val account = setupAccount(type = UserAccountType.FULL_ACCOUNT)
        val arbiPixCallbackRequest = setupRequest(account)

        every {
            accountService.findAccountByDocument(any())
        } returns ACCOUNT.copy(type = UserAccountType.FULL_ACCOUNT)

        val response = depositCallbackController.arbiDepositPixCallback(getObjectMapper().writeValueAsString(arbiPixCallbackRequest))

        response.status shouldBe HttpStatus.NO_CONTENT

        verify(exactly = 0) {
            depositCallbackController.logCashInLimitExceeded(any(), any(), any(), any(), any(), any(), any())
            internalBankService.getCashInByPeriod(any(), any(), any())
            accountService.findPhysicalAccountPaymentMethod(any(), any(), any())
        }
    }

    @Test
    fun `quando for um depósito pix não deve alarmar se um usuário com conta básica mas não ultrapassou o limite de cashin`() {
        val depositCallbackController = spyk(arbiDepositCallbackController)

        val account = setupAccount(type = UserAccountType.BASIC_ACCOUNT)
        val arbiPixCallbackRequest = setupRequest(account)

        every {
            accountService.findAccountByDocument(any())
        } returns ACCOUNT.copy(type = UserAccountType.BASIC_ACCOUNT)

        every {
            internalBankService.getCashInByPeriod(any(), any(), any())
        } returns 7_999_99

        val response = depositCallbackController.arbiDepositPixCallback(getObjectMapper().writeValueAsString(arbiPixCallbackRequest))

        response.status shouldBe HttpStatus.NO_CONTENT

        verify(exactly = 0) {
            depositCallbackController.logCashInLimitExceeded(any(), any(), any(), any(), any(), any(), any())
        }
    }

    @Test
    fun `quando for um depósito pix deve alarmar se um usuário com conta básica ultrapassar o limite de cashin`() {
        val depositCallbackController = spyk(arbiDepositCallbackController)

        val account = setupAccount(type = UserAccountType.BASIC_ACCOUNT)
        val arbiPixCallbackRequest = setupRequest(account)

        every {
            internalBankService.getCashInByPeriod(any(), any(), any())
        } returns 10_000_01

        val response = depositCallbackController.arbiDepositPixCallback(getObjectMapper().writeValueAsString(arbiPixCallbackRequest))

        response.status shouldBe HttpStatus.NO_CONTENT

        verify {
            depositCallbackController.logCashInLimitExceeded(any(), any(), any(), any(), any(), any(), any())
        }
    }

    @Test
    fun `quando for um depósito pix deve gerar evento de cash-in completo e processar o opp com transactionId`() {
        val account = setupAccount(type = UserAccountType.BASIC_ACCOUNT)
        val arbiPixCallbackRequest = setupRequest(account)

        every { onePixPayService.process(any<AccountNumber>(), any<Long>(), any()) } returns Unit

        val response = arbiDepositCallbackController.arbiDepositPixCallback(getObjectMapper().writeValueAsString(arbiPixCallbackRequest))

        response.status shouldBe HttpStatus.NO_CONTENT

        val oppIdSlot = slot<OnePixPayId<*>>()
        verify {
            onePixPayService.process(any<AccountNumber>(), any<Long>(), capture(oppIdSlot))
            userJourneyService.trackEventAsync(ACCOUNT.accountId, UserJourneyEvent.CashInCompleted)
            sweepingAccountService wasNot called
        }
        with(oppIdSlot.captured) {
            this.shouldBeTypeOf<UUIDOnePixPayId>()
            this.source.value shouldBe arbiPixCallbackRequest.mensagem.txId
        }
    }

    @Test
    fun `quando for um depósito pix iniciado pelo open finance deve gerar evento de cash-in completo, processar o opp com endToEnd e confirmar a transferência inteligente`() {
        val account = setupAccount(type = UserAccountType.BASIC_ACCOUNT)
        val arbiPixCallbackRequest = setupRequest(account, origemMovimento = ArbiPixMovementOrigin.INIC_PAG_MANU)

        every { onePixPayService.process(any<AccountNumber>(), any<Long>(), any<OnePixPayId<*>>()) } returns Unit

        val response = arbiDepositCallbackController.arbiDepositPixCallback(getObjectMapper().writeValueAsString(arbiPixCallbackRequest))

        response.status shouldBe HttpStatus.NO_CONTENT

        val oppIdSlot = slot<OnePixPayId<*>>()
        val endToEndSlot = slot<EndToEnd>()
        verify {
            onePixPayService.process(any<AccountNumber>(), any<Long>(), capture(oppIdSlot))
            userJourneyService.trackEventAsync(ACCOUNT.accountId, UserJourneyEvent.CashInCompleted)
            sweepingAccountService.confirmSweepingCashIn(capture(endToEndSlot))
        }
        with(oppIdSlot.captured) {
            this.shouldBeTypeOf<EndToEndOnePixPayId>()
            this.source.value shouldBe arbiPixCallbackRequest.mensagem.endToEnd
        }
        endToEndSlot.captured.value shouldBe arbiPixCallbackRequest.mensagem.endToEnd
    }

    @Test
    fun `quando for um depósito ted não deve alarmar se o limite de cash in for ultrapassado por uma conta full`() {
        val depositCallbackController = spyk(arbiDepositCallbackController)

        every {
            internalBankService.synchronizeBankAccount(any(), any(), any(), any(), any())
        } returns true

        every {
            accountService.findAccountByDocument(any())
        } returns ACCOUNT.copy(type = UserAccountType.FULL_ACCOUNT)

        depositCallbackController.arbiDepositCallback(getObjectMapper().writeValueAsString(despositTedCallbackRequest))

        verify(exactly = 0) {
            depositCallbackController.logCashInLimitExceeded(any(), any(), any(), any(), any(), any(), any())
            internalBankService.getCashInByPeriod(any(), any(), any())
            accountService.findPhysicalAccountPaymentMethod(any(), any(), any())
        }
    }

    @Test
    fun `quando for um depósito ted não deve alarmar se um usuário com conta básica mas não ultrapassou o limite de cashin`() {
        val depositCallbackController = spyk(arbiDepositCallbackController)

        every {
            internalBankService.synchronizeBankAccount(any(), any(), any(), any(), any())
        } returns true

        every {
            accountService.findAccountByDocument(any())
        } returns ACCOUNT.copy(type = UserAccountType.BASIC_ACCOUNT)

        every {
            internalBankService.getCashInByPeriod(any(), any(), any())
        } returns 9_999_99

        depositCallbackController.arbiDepositCallback(getObjectMapper().writeValueAsString(despositTedCallbackRequest))

        verify(exactly = 0) {
            depositCallbackController.logCashInLimitExceeded(any(), any(), any(), any(), any(), any(), any())
        }
    }

    @Test
    fun `quando for um depósito ted deve alarmar se um usuário com conta básica ultrapassar o limite de cashin`() {
        setupAccount(type = UserAccountType.BASIC_ACCOUNT)
        val depositCallbackController = spyk(arbiDepositCallbackController)

        every {
            internalBankService.synchronizeBankAccount(any(), any(), any())
        } returns true

        every {
            internalBankService.getCashInByPeriod(any(), any(), any())
        } returns 10_000_01

        depositCallbackController.arbiDepositCallback(getObjectMapper().writeValueAsString(despositTedCallbackRequest))

        verify {
            depositCallbackController.logCashInLimitExceeded(any(), any(), any(), any(), any(), any(), any())
        }
    }

    @Test
    fun `quando for um depósito ted deve gerar evento de cash-in completo`() {
        setupAccount(type = UserAccountType.BASIC_ACCOUNT)
        every {
            internalBankService.synchronizeBankAccount(any(), any(), any())
        } returns true

        arbiDepositCallbackController.arbiDepositCallback(getObjectMapper().writeValueAsString(despositTedCallbackRequest))

        verify() {
            userJourneyService.trackEventAsync(ACCOUNT.accountId, UserJourneyEvent.CashInCompleted)
        }
    }

    @Nested
    @DisplayName("quando for um callback de contas modatta")
    inner class ModattaCallbacks {
        @Test
        fun `deve repassar o callback para o backend modatta quando for pix`() {
            val account = setupAccount(type = UserAccountType.BASIC_ACCOUNT)
            val arbiPixCallbackRequest = setupRequest(account, parte = mensagem.parte.copy(cpfCnpj = cnpjModatta))

            every {
                modattaProvider.depositPixCallback(any())
            } just runs

            val response = arbiDepositCallbackController.arbiDepositPixCallback(getObjectMapper().writeValueAsString(arbiPixCallbackRequest))

            response.status shouldBe HttpStatus.NO_CONTENT

            verify {
                modattaProvider.depositPixCallback(arbiPixCallbackRequest.mensagem.toBankStatementItem())
            }
        }

        @Test
        fun `deve repassar o callback para o backend modatta quando for ted`() {
            val arbiCallbackRequest = ArbiCallbackRequest(
                valor = 400,
                chave = null,
                conta = ArbiCallbackContaRequest(banco = "", agencia = "", conta = "**********"),
                codHistorico = "faucibus",
                descricao = null,
                bancoorigem = null,
                agenciaorigem = null,
                contaorigem = null,
                inscricao = null,
                nome = null,
                motivodevolucao = null,
                numorigemstr = null,
            )

            arbiDepositCallbackController.arbiDepositCallback(getObjectMapper().writeValueAsString(arbiCallbackRequest))

            verify {
                modattaProvider.depositCallback(arbiCallbackRequest.toModattaDepositCallback())
            }
        }
    }

    @Nested
    @DisplayName("quando não for um callback de contas modatta")
    inner class NotModattaCallback {
        @Test
        fun `não deve repassar o callback para o backend modatta quando for pix`() {
            val account = setupAccount(type = UserAccountType.BASIC_ACCOUNT)
            val arbiPixCallbackRequest = setupRequest(account, nroMovimento = "123456")

            val response = arbiDepositCallbackController.arbiDepositPixCallback(getObjectMapper().writeValueAsString(arbiPixCallbackRequest))

            response.status shouldBe HttpStatus.NO_CONTENT

            verify(exactly = 0) {
                modattaProvider.depositPixCallback(arbiPixCallbackRequest.mensagem.toBankStatementItem())
            }
        }

        @Test
        fun `não deve repassar o callback para o backend modatta quando for ted`() {
            val arbiCallbackRequest = ArbiCallbackRequest(
                valor = 400,
                chave = null,
                conta = ArbiCallbackContaRequest(banco = "", agencia = "", conta = "**********"),
                codHistorico = "faucibus",
                descricao = null,
                bancoorigem = null,
                agenciaorigem = null,
                contaorigem = null,
                inscricao = null,
                nome = null,
                motivodevolucao = null,
                numorigemstr = null,
            )

            arbiDepositCallbackController.arbiDepositCallback(getObjectMapper().writeValueAsString(arbiCallbackRequest))

            verify(exactly = 0) {
                modattaProvider.depositCallback(arbiCallbackRequest.toModattaDepositCallback())
            }
        }
    }
}

private fun ArbiPixMovementNotificationMessage.toBankStatementItem(): DefaultBankStatementItem {
    val dataHoraMovimentoPattern =
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss").withZone(brazilTimeZone)
    val formattedDataHoraMovimento =
        ZonedDateTime.parse(dataHoraMovimento.take(19), dataHoraMovimentoPattern)

    val flow =
        if (this.naturezaMovimento == ArbiPixMovementNature.C) BankStatementItemFlow.CREDIT else BankStatementItemFlow.DEBIT

    return DefaultBankStatementItem(
        date = formattedDataHoraMovimento.toLocalDate(),
        flow = flow,
        type = BankStatementItemType.PIX,
        description = campoLivre.orEmpty(),
        operationNumber = nroMovimento!!,
        isTemporaryOperationNumber = isTemporaryOperationNumber(nroMovimento!!),
        amount = valor.parseAmount(),
        counterpartName = contraParte.nome,
        counterpartDocument = contraParte.cpfCnpj,
        counterpartAccountNo = contraParte.nroConta,
        counterpartBankName = contraParte.nomeInstituicao,
        documentNumber = parte.cpfCnpj,
        ref = txId,
        lastUpdate = formattedDataHoraMovimento,
    )
}

private fun ArbiCallbackRequest.toModattaDepositCallback(): ModattaDepositCallback {
    val callbackRequest = this
    val document = inscricao?.padStart(CPF_SIZE, '0').orEmpty()
    return ModattaDepositCallback(
        amount = callbackRequest.valor.toLong(),
        counterpartName = callbackRequest.nome.orEmpty(),
        counterpartDocument = document,
        counterpartAccountNo = callbackRequest.contaorigem.orEmpty(),
    )
}