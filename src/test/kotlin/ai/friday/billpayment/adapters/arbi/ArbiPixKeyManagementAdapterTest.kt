package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixKeyType
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponseFactory
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.mockk.every
import io.mockk.mockk
import io.reactivex.Flowable
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

internal class ArbiPixKeyManagementAdapterTest {

    @ParameterizedTest
    @CsvSource(value = ["SLRY,SALARY", "CACC,CHECKING", "SVGS,SAVINGS"])
    fun `should return SLRY when type is salary`(tipoConta: String, accountType: AccountType) {
        val keyDetails = KeyDetailsResponseTO(
            agencia = "",
            conta = "",
            cpfCnpj = "",
            instituicao = "",
            tipoConta = tipoConta,
            confirmado = true,
            cid = "",
            nome = "",
            tipoPessoa = "",
            chave = "",
            tipoChave = "",
            dataCriacao = "",
            dataPosse = "",
            endToEnd = "",
            nomePsp = "",
            dataAbertura = "",
            estatisticas = null,
        )

        val type = keyDetails.getAccountType()

        type shouldBe accountType
    }

    private val mockClient: RxHttpClient = mockk()
    private val authenticationManager: NewArbiAuthenticationManager = mockk() {
        every {
            getToken()
        } returns "fakeToken"
    }
    private val configuration = NewArbiPixKeyManagementConfiguration().apply {
        clientId = ""
        clientSecret = ""
        pixKeyV2Path = ""
        findPixKeyV2Path = ""
        findInternalPixKeysPath = ""
        claimPath = ""
        cancelClaimPath = ""
        confirmClaimPath = ""
        listClaimsPath = ""
        completeClaimPath = ""
    }
    private val arbiPixKeyManagementAdapter = NewArbiPixKeyManagementAdapter(
        httpClient = mockClient,
        configuration = configuration,
        authenticationManager = authenticationManager,
    )

    @Test
    fun `should return UnknownError on other codErro`() {
        val responseBody =
            ResponseTO("{\"erroDict\":\"FORBIDDEN\",\"codErro\":403,\"descricaoErro\":\"mockk error\"}", 412)
        testFindKeyDetailsError(HttpStatus.BAD_REQUEST, responseBody, PixKeyError.UnknownError)
    }

    @Test
    fun `should return UnknownError on codErro 400 - BAD_REQUEST`() {
        val responseBody = ResponseTO(
            "{\"erroDict\":\"BAD_REQUEST\",\"codErro\":400,\"descricaoErro\":\"Participant is not authenticated\"}",
            412,
        )
        testFindKeyDetailsError(HttpStatus.PRECONDITION_FAILED, responseBody, PixKeyError.UnknownError)
    }

    @Test
    fun `should return KeyNotFound on codErro 107 - CHAVE_NAO_ENCONTRADA`() {
        val responseBody = ResponseTO("{\"erroDict\":\"NOT_FOUND\",\"codErro\":107}", 404)
        testFindKeyDetailsError(HttpStatus.NOT_FOUND, responseBody, PixKeyError.KeyNotFound)
    }

    @Test
    fun `should return KeyNotFound on codErro 404 - NOT_FOUND`() {
        val responseBody =
            ResponseTO("{\"erroDict\":\"NOT_FOUND\",\"codErro\":404,\"descricaoErro\":\"Entry not found\"}", 412)
        testFindKeyDetailsError(HttpStatus.NOT_FOUND, responseBody, PixKeyError.KeyNotFound)
    }

    @Test
    fun `should return MalformedKey on codErro 7 - CHAVE_REIVINDICADA`() {
        val responseBody = ResponseTO("{\"erroDict\":\"CHAVE_REIVINDICADA\",\"codErro\":7}", 400)
        testFindKeyDetailsError(HttpStatus.BAD_REQUEST, responseBody, PixKeyError.MalformedKey)
    }

    @Test
    fun `should return KeyNotFound on codErro 107 - CHAVE_NAO_ENCONTRADA on deletePix`() {
        val responseBody = """[{"status":412,"response":"{\"erroDict\":\"CHAVE_NAO_EXISTE\",\"codErro\":107,\"descricaoErro\":\"Chave informada não encontrada para a instituição 54403563.\"}"}]"""
        testDeleteKeyDetailsError(HttpStatus.NOT_FOUND, responseBody, PixKeyError.KeyNotFound)
    }

    private fun testFindKeyDetailsError(httpStatus: HttpStatus, responseTO: ResponseTO, expectedError: PixKeyError) {
        every {
            mockClient.retrieve(
                ofType(HttpRequest::class),
                Argument.listOf(ResponseTO::class.java),
                Argument.listOf(ResponseTO::class.java),
            )
        } answers {
            Flowable.error(
                HttpClientResponseException(
                    "erro",
                    HttpResponseFactory.INSTANCE.status<Any>(httpStatus).body(listOf(responseTO)),
                ),
            )
        }

        val key = arbiPixKeyManagementAdapter.findKeyDetails(
            key = PixKey(value = "11111111111", type = PixKeyType.CPF),
            document = "22222222222",
        )

        key.isLeft() shouldBe true
        key.mapLeft {
            it shouldBe expectedError
        }
    }

    private fun testDeleteKeyDetailsError(httpStatus: HttpStatus, response: String, expectedError: PixKeyError) {
        every {
            mockClient.exchange(
                ofType(HttpRequest::class),
                Argument.of(Unit::class.java),
                Argument.STRING,
            )
        } answers {
            Flowable.error(
                HttpClientResponseException(
                    "erro",
                    HttpResponseFactory.INSTANCE.status<Any>(httpStatus).body(response),
                ),
            )
        }

        val key = arbiPixKeyManagementAdapter.deleteKey(
            key = "11111111111",
            document = Document("22222222222"),
            null,
        )

        key.isLeft() shouldBe true
        key.mapLeft {
            it shouldBe expectedError
        }
    }
}