package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.payment.getRecipientByPayer
import io.kotest.matchers.shouldBe
import java.util.stream.Stream
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class ArbiAdapterRecipientTest {
    @ParameterizedTest(name = "{index} {0}")
    @MethodSource("getArbiBoletoResponses")
    fun getRecipientTest(testName: String, response: MutableMap<String, String>, expected: Recipient) {
        val assignor = "AssignorTest"
        getRecipientChain(response).getRecipientByPayer("46779213187", assignor) shouldBe expected
    }

    companion object {
        @JvmStatic
        fun getArbiBoletoResponses(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    "deve retornar o assinor quando o beneficiario é o pagador",
                    arbiResponseWithSameBeneficiarioAndPagador,
                    Recipient("AssignorTest"),
                ),
                Arguments.of(
                    "deve retornar o beneficiario original quando o sacador avalista é vazio",
                    arbiResponseWithSacadorAvalistaIsEmpty,
                    Recipient("CAIXA ECONOMICA FEDERAL - SIGA", "00360305000104"),
                ),
                Arguments.of(
                    "deve retornar o beneficiario original quando o sacador avalista é o pagador",
                    arbiResponseWithSameSacadorAvalistaAndPagador,
                    Recipient("CAIXA ECONOMICA FEDERAL - SIGA", "00360305000104"),
                ),
                Arguments.of(
                    "deve retornar o nome razao social quando o nome fantasia não existe",
                    arbiResponseWithSameBeneficiarioAndPagadorWithoutNomeFantasia,
                    Recipient("BANCO DO BRASIL - TESTE", "00360305000104"),
                ),
                Arguments.of(
                    "deve retornar o beneficiario original quando o beneficiario final não existe",
                    arbiResponseWithSameBeneficiarioOriginalAndPagador,
                    Recipient("BANCO BRADESCO - TESTE NOME FANSASIA", "00360305000104"),
                ),
                Arguments.of(
                    "deve desconsiderar nome inválido",
                    arbiResponseWithInvalidNames,
                    Recipient("AssignorTest"),
                ),
            )
        }
    }
}

private val PAYER = "46779213187"

private val arbiResponseWithSameBeneficiarioAndPagador = mapOf(
    "TpPessoaBenfcrioOr" to "F",
    "CNPJ_CPFBenfcrioOr" to "46779213187",
    "Nom_RzSocBenfcrioOr" to "LEVY PARANAGUA BORGES",
    "NomFantsBenfcrioOr" to "",
    "TpPessoaBenfcrioFinl" to "",
    "CNPJ_CPFBenfcrioFinl" to "",
    "Nom_RzSocBenfcrioFinl" to "",
    "NomFantsBenfcrioFinl" to "",
    "TpPessoaPagdr" to "F",
    "CNPJ_CPFPagdr" to PAYER,
    "Nom_RzSocPagdr" to "LEVY PARANAGUA BORGES",
    "NomFantsPagdr" to "LEVY PARANAGUA BORGES",
    "TpIdentcSacdrAvalst" to "",
    "IdentcSacdrAvalst" to "",
    "Nom_RzSocSacdrAvalst" to "",
)

val arbiResponseWithSameSacadorAvalistaAndPagador = mapOf(
    "TpPessoaBenfcrioOr" to "J",
    "CNPJ_CPFBenfcrioOr" to "360305000104",
    "Nom_RzSocBenfcrioOr" to "CAIXA ECONOMICA FEDERAL - SIGA",
    "NomFantsBenfcrioOr" to "CAIXA ECONOMICA FEDERAL - SIGA",
    "TpPessoaBenfcrioFinl" to "",
    "CNPJ_CPFBenfcrioFinl" to "",
    "Nom_RzSocBenfcrioFinl" to "",
    "NomFantsBenfcrioFinl" to "",
    "TpPessoaPagdr" to "F",
    "CNPJ_CPFPagdr" to PAYER,
    "Nom_RzSocPagdr" to "LEONARDO LINS ARAGAO",
    "NomFantsPagdr" to "",
    "TpIdentcSacdrAvalst" to "1",
    "IdentcSacdrAvalst" to "00000$PAYER",
    "Nom_RzSocSacdrAvalst" to "LEONARDO LINS ARAGAO",
)

val arbiResponseWithInvalidNames = mapOf(
    "TpPessoaBenfcrioOr" to "F",
    "CNPJ_CPFBenfcrioOr" to "22222222222",
    "Nom_RzSocBenfcrioOr" to "22222222222",
    "NomFantsBenfcrioOr" to "22222222222",
    "TpPessoaBenfcrioFinl" to "F",
    "CNPJ_CPFBenfcrioFinl" to "11111111111",
    "Nom_RzSocBenfcrioFinl" to "11111111111",
    "NomFantsBenfcrioFinl" to "11111111111",
    "TpPessoaPagdr" to "F",
    "CNPJ_CPFPagdr" to PAYER,
    "Nom_RzSocPagdr" to "LEONARDO LINS ARAGAO",
    "NomFantsPagdr" to "",
    "TpIdentcSacdrAvalst" to "1",
    "IdentcSacdrAvalst" to "00000000000",
    "Nom_RzSocSacdrAvalst" to "0000000000000000000000000",
)

val arbiResponseWithSacadorAvalistaIsEmpty = mapOf(
    "TpPessoaBenfcrioOr" to "J",
    "CNPJ_CPFBenfcrioOr" to "360305000104",
    "Nom_RzSocBenfcrioOr" to "CAIXA ECONOMICA FEDERAL - SIGA",
    "NomFantsBenfcrioOr" to "CAIXA ECONOMICA FEDERAL - SIGA",
    "TpPessoaBenfcrioFinl" to "",
    "CNPJ_CPFBenfcrioFinl" to "",
    "Nom_RzSocBenfcrioFinl" to "",
    "NomFantsBenfcrioFinl" to "",
    "TpPessoaPagdr" to "F",
    "CNPJ_CPFPagdr" to PAYER,
    "Nom_RzSocPagdr" to "LEONARDO LINS ARAGAO",
    "NomFantsPagdr" to "",
    "TpIdentcSacdrAvalst" to "",
    "IdentcSacdrAvalst" to "",
    "Nom_RzSocSacdrAvalst" to " ",
)

val arbiResponseWithSameBeneficiarioAndPagadorWithoutNomeFantasia = mapOf(
    "TpPessoaBenfcrioOr" to "J",
    "CNPJ_CPFBenfcrioOr" to "360305000104",
    "Nom_RzSocBenfcrioOr" to "BANCO DO BRASIL - TESTE",
    "NomFantsBenfcrioOr" to "",
    "TpPessoaBenfcrioFinl" to "",
    "CNPJ_CPFBenfcrioFinl" to "",
    "Nom_RzSocBenfcrioFinl" to "",
    "NomFantsBenfcrioFinl" to "",
    "TpPessoaPagdr" to "F",
    "CNPJ_CPFPagdr" to PAYER,
    "Nom_RzSocPagdr" to "LEONARDO LINS ARAGAO",
    "NomFantsPagdr" to "",
    "TpIdentcSacdrAvalst" to "1",
    "IdentcSacdrAvalst" to "00000$PAYER",
    "Nom_RzSocSacdrAvalst" to "LEONARDO LINS ARAGAO",
)

val arbiResponseWithSameBeneficiarioOriginalAndPagador = mapOf(
    "TpPessoaBenfcrioOr" to "F",
    "CNPJ_CPFBenfcrioOr" to "000004307345495",
    "Nom_RzSocBenfcrioOr" to "LEONARDO LINS ARAGAO",
    "NomFantsBenfcrioOr" to "",
    "TpPessoaBenfcrioFinl" to "J",
    "CNPJ_CPFBenfcrioFinl" to "360305000104",
    "Nom_RzSocBenfcrioFinl" to "BANCO BRADESCO - TESTE",
    "NomFantsBenfcrioFinl" to "BANCO BRADESCO - TESTE NOME FANSASIA",
    "TpPessoaPagdr" to "F",
    "CNPJ_CPFPagdr" to PAYER,
    "Nom_RzSocPagdr" to "LEONARDO LINS ARAGAO",
    "NomFantsPagdr" to "",
    "TpIdentcSacdrAvalst" to "1",
    "IdentcSacdrAvalst" to "00000$PAYER",
    "Nom_RzSocSacdrAvalst" to "LEONARDO LINS ARAGAO",
)