package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.payment.PixPaymentStatus
import ai.friday.billpayment.app.pix.PixKeyDetailsResult
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixTransactionError
import ai.friday.billpayment.balance
import ai.friday.billpayment.pixKeyAdded
import ai.friday.billpayment.pixKeyPaymentStarted
import arrow.core.Either
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.client.exceptions.ReadTimeoutException
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.reactivex.Flowable
import java.util.stream.Stream
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource
import org.junit.jupiter.params.provider.ValueSource

class ArbiPixPaymentAdapterTest {

    private val httpClientMock: RxHttpClient = mockk()

    private val pixKeyManagement: PixKeyManagement = mockk()

    private val arbiPixPaymentAdapter = NewArbiPixPaymentAdapter(
        httpClient = httpClientMock,
        configuration = mockk(relaxed = true),
        authenticationManager = mockk(relaxed = true),
        pixKeyManagement = pixKeyManagement,
        accountRepository = mockk {
            every { findById(any()) } returns ACCOUNT
        },
    )

    private val expectedE2e = "C54403563202101041828L3HNWdRDHxy"

    private val idOrdemPagamento = 10256
    private val successArbiPixResponseTO = listOf(
        ArbiPixResponseTO(
            response = "{\"idOrdemPagamento\":$idOrdemPagamento,\"endToEnd\":\"$expectedE2e\",\"statusOrdemPagamento\":\"ENVIADA\",\"infoOrdemPagamento\":\"\"}",
            status = 202,
        ),
    )

    @Test
    fun `successful init payment with pix key`() {
        val bankOperationId = BankOperationId(idOrdemPagamento.toString())
        val bill = Bill.build(pixKeyAdded, pixKeyPaymentStarted)

        every {
            pixKeyManagement.findKeyDetails(
                any(),
                any(),
            )
        } returns Either.Right(PixKeyDetailsResult(bill.recipient!!.pixKeyDetails!!, expectedE2e))

        every {
            httpClientMock.retrieve(
                ofType(HttpRequest::class),
                ofType(Argument::class),
                ofType(Argument::class),
            )
        } answers {
            Flowable.just(successArbiPixResponseTO)
        }

        val pixResult = arbiPixPaymentAdapter.initPayment(bill, balance, bankOperationId)

        pixResult.idOrdemPagamento shouldBe bankOperationId.value
        pixResult.error shouldBe null
        pixResult.status shouldBe PixPaymentStatus.ACKNOWLEDGED
        pixResult.endToEnd shouldBe expectedE2e

        verify { pixKeyManagement.findKeyDetails(bill.recipient?.pixKeyDetails!!.key, any()) }
        verify { httpClientMock.retrieve(ofType(HttpRequest::class), ofType(Argument::class), ofType(Argument::class)) }
    }

    @Test
    fun `should throw exception when timeout occurs during payment call`() {
        val bankOperationId = BankOperationId(idOrdemPagamento.toString())
        val bill = Bill.build(pixKeyAdded, pixKeyPaymentStarted)

        every {
            pixKeyManagement.findKeyDetails(
                any(),
                any(),
            )
        } returns Either.Right(PixKeyDetailsResult(bill.recipient!!.pixKeyDetails!!, expectedE2e))

        every {
            httpClientMock.retrieve(
                ofType(HttpRequest::class),
                ofType(Argument::class),
                ofType(Argument::class),
            )
        } throws ReadTimeoutException.TIMEOUT_EXCEPTION

        assertThrows<ReadTimeoutException> {
            arbiPixPaymentAdapter.initPayment(bill, balance, bankOperationId)
        }
    }

    @ParameterizedTest
    @MethodSource("pixKeyErrors")
    fun `should fail with expected return on error retrieving pix key`(
        error: PixKeyError,
        expectedReturn: PixTransactionError,
    ) {
        val bankOperationId = BankOperationId(idOrdemPagamento.toString())
        val bill = Bill.build(pixKeyAdded, pixKeyPaymentStarted)

        every { pixKeyManagement.findKeyDetails(any(), any()) } returns Either.Left(error)

        val pixResult = arbiPixPaymentAdapter.initPayment(bill, balance, bankOperationId)

        pixResult.idOrdemPagamento shouldBe ""
        pixResult.error shouldBe expectedReturn
        pixResult.status shouldBe PixPaymentStatus.REFUSED
        pixResult.endToEnd shouldBe ""

        verify { pixKeyManagement.findKeyDetails(bill.recipient?.pixKeyDetails!!.key, any()) }
        verify(exactly = 0) {
            httpClientMock.retrieve(
                ofType(HttpRequest::class),
                ofType(Argument::class),
                ofType(Argument::class),
            )
        }
    }

    @Test
    fun `should return unknown status when init payment returns a 412`() {
        val bankOperationId = BankOperationId(idOrdemPagamento.toString())
        val bill = Bill.build(pixKeyAdded, pixKeyPaymentStarted)

        every {
            pixKeyManagement.findKeyDetails(
                any(),
                any(),
            )
        } returns Either.Right(PixKeyDetailsResult(bill.recipient!!.pixKeyDetails!!, expectedE2e))

        every {
            httpClientMock.retrieve(
                ofType(HttpRequest::class),
                ofType(Argument::class),
                ofType(Argument::class),
            )
        } answers {
            val response = HttpResponse.status<List<ArbiPixResponseTO>>(HttpStatus.PRECONDITION_FAILED)
                .body(listOf(ArbiPixResponseTO(response = "", status = 412)))
            Flowable.error(HttpClientResponseException("erro", response))
        }

        val pixResult = arbiPixPaymentAdapter.initPayment(bill, balance, bankOperationId)

        pixResult.idOrdemPagamento shouldBe "10256"
        pixResult.status shouldBe PixPaymentStatus.UNKNOWN
        pixResult.endToEnd shouldBe ""
        pixResult.error shouldBe PixTransactionError.PaymentGenericTemporaryError

        verify { pixKeyManagement.findKeyDetails(bill.recipient?.pixKeyDetails!!.key, any()) }
        verify { httpClientMock.retrieve(ofType(HttpRequest::class), ofType(Argument::class), ofType(Argument::class)) }
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "RECUSADA,REFUSED,BL00001,SALDO DO CLIENTE EM CONTA INSUFICIENTE,60168",
        ],
    )
    fun `should return BusinessInsufficientBalance on checkPaymentStatus with insufficient balance error`(
        statusOrdemPagamento: String,
        pixPaymentStatus: PixPaymentStatus,
        codigoErro: String,
        descricaoErro: String,
        codigoErroComplementar: String?,
    ) {
        testErrorHandlingOnCheckPaymentStatus(
            statusOrdemPagamento = statusOrdemPagamento,
            pixPaymentStatus = pixPaymentStatus,
            codigoErro = codigoErro,
            descricaoErro = descricaoErro,
            codigoErroComplementar = codigoErroComplementar,
            expectedResponse = PixTransactionError.BusinessInsufficientBalance,
        )
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "RECUSADA,REFUSED,AB00002,Valor da transação superior ao limite.,",
        ],
    )
    fun `should return BusinessSingleTransactionLimit on checkPaymentStatus with single transaction limit error`(
        statusOrdemPagamento: String,
        pixPaymentStatus: PixPaymentStatus,
        codigoErro: String,
        descricaoErro: String,
        codigoErroComplementar: String?,
    ) {
        testErrorHandlingOnCheckPaymentStatus(
            statusOrdemPagamento = statusOrdemPagamento,
            pixPaymentStatus = pixPaymentStatus,
            codigoErro = codigoErro,
            descricaoErro = descricaoErro,
            codigoErroComplementar = codigoErroComplementar,
            expectedResponse = PixTransactionError.BusinessSingleTransactionLimit,
        )
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "RECUSADA,REFUSED,AB00003,Limite diário ultrapassado. Valor disponivel: R\$ 999.99,",
        ],
    )
    fun `should return BusinessDailyLimit on checkPaymentStatus with daily limit error`(
        statusOrdemPagamento: String,
        pixPaymentStatus: PixPaymentStatus,
        codigoErro: String,
        descricaoErro: String,
        codigoErroComplementar: String?,
    ) {
        testErrorHandlingOnCheckPaymentStatus(
            statusOrdemPagamento = statusOrdemPagamento,
            pixPaymentStatus = pixPaymentStatus,
            codigoErro = codigoErro,
            descricaoErro = descricaoErro,
            codigoErroComplementar = codigoErroComplementar,
            expectedResponse = PixTransactionError.BusinessDailyLimit,
        )
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "RECUSADA,REFUSED,AB00004,Limite de movimentos iguais ultrapassado.,",
        ],
    )
    fun `should return BusinessSameValuePaymentsExceeded on checkPaymentStatus with same value payments exceeded error`(
        statusOrdemPagamento: String,
        pixPaymentStatus: PixPaymentStatus,
        codigoErro: String,
        descricaoErro: String,
        codigoErroComplementar: String?,
    ) {
        testErrorHandlingOnCheckPaymentStatus(
            statusOrdemPagamento = statusOrdemPagamento,
            pixPaymentStatus = pixPaymentStatus,
            codigoErro = codigoErro,
            descricaoErro = descricaoErro,
            codigoErroComplementar = codigoErroComplementar,
            expectedResponse = PixTransactionError.BusinessSameValuePaymentsExceeded,
        )
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "RECUSADA,REFUSED,BL00001,CONTA NÃO ENCONTRADA,60743",
            "RECUSADA,REFUSED,PIXAC03,Número da conta transacional do PSP do Recebedor inexistente ou inválido.,",
            "RECUSADA,REFUSED,PIXBE01,CPF/CNPJ do usuário recebedor não é consistente com o titular da conta creditada.,",
            "ERRO,FAILED,AB00015,Dados da conta recebedora não localizados,",
            "RECUSADA,REFUSED,TI00001,CONTA NÃO ENCONTRADA,60743",
        ],
    )
    fun `should return SettlementDestinationNotFound error on checkPaymentStatus with invalid pix destination account`(
        statusOrdemPagamento: String,
        pixPaymentStatus: PixPaymentStatus,
        codigoErro: String,
        descricaoErro: String,
        codigoErroComplementar: String?,
    ) {
        testErrorHandlingOnCheckPaymentStatus(
            statusOrdemPagamento = statusOrdemPagamento,
            pixPaymentStatus = pixPaymentStatus,
            codigoErro = codigoErro,
            descricaoErro = descricaoErro,
            codigoErroComplementar = codigoErroComplementar,
            expectedResponse = PixTransactionError.SettlementDestinationAccountNotFound,
        )
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "RECUSADA,REFUSED,PIXAC14,Tipo de conta transacional do PSP do Recebedor inexistente ou inválido.,",
        ],
    )
    fun `should return SettlementDestinationAccountTypeInvalid when account type is invalid`(
        statusOrdemPagamento: String,
        pixPaymentStatus: PixPaymentStatus,
        codigoErro: String,
        descricaoErro: String,
        codigoErroComplementar: String?,
    ) {
        testErrorHandlingOnCheckPaymentStatus(
            statusOrdemPagamento = statusOrdemPagamento,
            pixPaymentStatus = pixPaymentStatus,
            codigoErro = codigoErro,
            descricaoErro = descricaoErro,
            codigoErroComplementar = codigoErroComplementar,
            expectedResponse = PixTransactionError.SettlementDestinationAccountTypeInvalid,
        )
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "ERRO,FAILED,PIXAC06,A conta especificada encontra-se bloqueada.,",
            "ERRO,FAILED,PIXAC07,Número da conta transacional do PSP do Recebedor encerrada.,",
        ],
    )
    fun `should return SettlementDestinationAccountNotAvailable error on checkPaymentStatus with pix destination account not available`(
        statusOrdemPagamento: String,
        pixPaymentStatus: PixPaymentStatus,
        codigoErro: String,
        descricaoErro: String,
        codigoErroComplementar: String?,
    ) {
        testErrorHandlingOnCheckPaymentStatus(
            statusOrdemPagamento = statusOrdemPagamento,
            pixPaymentStatus = pixPaymentStatus,
            codigoErro = codigoErro,
            descricaoErro = descricaoErro,
            codigoErroComplementar = codigoErroComplementar,
            expectedResponse = PixTransactionError.SettlementDestinationAccountNotAvailable,
        )
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "ERRO,FAILED,AB00006,Transferência interna de participante indireto deve ser realizada na origem.,",
            "ERRO,FAILED,AB00021,Valor informado inválido.,",
            "ERRO,FAILED,PIXAG03,Tipo de transação não é suportada/autorizada nessa conta.,",
            "ERRO,FAILED,PIXAM01,SPI não processará ordens com valor zero.,",
            "ERRO,FAILED,PIXAM18,Número da transações inválida.,",
            "ERRO,FAILED,PIXDS0G,Participante que assinou a mensagem não é autorizado a operar a conta PI debitada. No caso em que o participante que assinou a mensagem não é o titular da conta PI debitada no SPI nem é o liquidante da conta PI debitada,",
            "ERRO,FAILED,PIXRR01,Nos casos de pagamento que não sejam da Secretaria do Tesouro Nacional (STN) o preenchimento das informações da conta do usuário pagador são obrigatórias.,",
        ],
    )
    fun `should return SettlementGenericPermanentError error on checkPaymentStatus with permanent errors`(
        statusOrdemPagamento: String,
        pixPaymentStatus: PixPaymentStatus,
        codigoErro: String,
        descricaoErro: String,
        codigoErroComplementar: String?,
    ) {
        testErrorHandlingOnCheckPaymentStatus(
            statusOrdemPagamento = statusOrdemPagamento,
            pixPaymentStatus = pixPaymentStatus,
            codigoErro = codigoErro,
            descricaoErro = descricaoErro,
            codigoErroComplementar = codigoErroComplementar,
            expectedResponse = PixTransactionError.SettlementGenericPermanentError,
        )
    }

    @Test
    fun `deve retornar AccountFundsBlocked quando a conta está sob bloqueio de valores`() {
        testErrorHandlingOnCheckPaymentStatus(
            statusOrdemPagamento = "ERRO",
            pixPaymentStatus = PixPaymentStatus.FAILED,
            codigoErro = "BL00001",
            descricaoErro = "SALDO DO CLIENTE EM CONTA SOB BLOQUEIO DE VALORES",
            codigoErroComplementar = "60432",
            expectedResponse = PixTransactionError.AccountFundsBlocked,
        )
    }

    @ParameterizedTest
    @ValueSource(strings = ["TI00001", "BL00001"])
    fun `should return PaymentSourceAccountLocked when account is locked`(errorCode: String) {
        testErrorHandlingOnCheckPaymentStatus(
            statusOrdemPagamento = "ERRO",
            pixPaymentStatus = PixPaymentStatus.FAILED,
            codigoErro = errorCode,
            descricaoErro = "SITUAÇÃO DA CONTA NÃO PERMITE MOVIMENTAÇÃO À DÉBITO",
            codigoErroComplementar = "60217",
            expectedResponse = PixTransactionError.PaymentSourceAccountLocked,
        )
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "ERRO,FAILED,PIXDS04,Ordem rejeitada pelo PSP do Recebedor.,",
        ],
    )
    fun `should return SettlementPaymentRefusedByDestination error on checkPaymentStatus with payment refused error`(
        statusOrdemPagamento: String,
        pixPaymentStatus: PixPaymentStatus,
        codigoErro: String,
        descricaoErro: String,
        codigoErroComplementar: String?,
    ) {
        testErrorHandlingOnCheckPaymentStatus(
            statusOrdemPagamento = statusOrdemPagamento,
            pixPaymentStatus = pixPaymentStatus,
            codigoErro = codigoErro,
            descricaoErro = descricaoErro,
            codigoErroComplementar = codigoErroComplementar,
            expectedResponse = PixTransactionError.SettlementPaymentRefusedByDestination,
        )
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "ERRO,FAILED,AB00001,Pagamentos da Instituição bloqueados. Sistema temporariamente indisponível. Por favor tente novamente mais tarde.,",
            "ERRO,FAILED,AB00007,Pagamentos agendados só podem ser realizados até as 23:30 do dia anterior à operação.,",
            "ERRO,FAILED,AB00014,Dados da conta pagadora não localizados.,",
            "ERRO,FAILED,AB00016,Erro no processamento da pacs.008 na ICOM.,",
            "ERRO,FAILED,AB00017,Tempo limite para aguardo de resposta expirado.,",
            "ERRO,FAILED,AB00018,End To End duplicado.,",
            "ERRO,FAILED,AB00019,End To End inválido.,",
            "ERRO,FAILED,AB00020,Sistema Indisponível para esta operação.,",
            "ERRO,FAILED,AB99999,Erro desconhecido.,",
            "ERRO,FAILED,PIXAB03,Controle de timeout no SPI / Extrapolação do limite de tempo de timeout no PSP do Recebedor.,",
            "ERRO,FAILED,PIXAB09,Transação interrompida devido a erro no PSP do Recebedor.,",
            "ERRO,FAILED,PIXAM04,Saldo insuficiente na conta PI do PSP do pagador.,",
            "ERRO,FAILED,PIXAM05,Identificador EndtoEnd em duplicidade.,",
            "ERRO,FAILED,PIXCH16,Elemento da mensagem incorreto.,",
            "ERRO,FAILED,PIXDT02,Data e Hora do envio da mensagem inválida.,",
            "ERRO,FAILED,PIXDU04,Identificador EndtoEnd não é único (duplicidade).,",
            "ERRO,FAILED,PIXED05,Erro no processamento do pagamento (erro genérico).,",
            "ERRO,FAILED,PIXFF08,Identificador EndtoEnd inválido ou inexistente.,",
            "ERRO,FAILED,PIXRC09,Identificador Debtor ClearingSystemMember inválido ou inexistente.,",
            "ERRO,FAILED,PIXRC10,Identificador Creditor ClearingSystemMember inválido ou inexistente.,",
            "ERRO,FAILED,BL00001,Mensagem retornada pelo api-pagamento-instantaneo-ebank - POST /gi/eb/conta/bloqueio - O código de erro original será exibido no campo codigoErroComplementar,XXXX",
            "ERRO,FAILED,BL00002,Chave de bloqueio não gerada.MV00001 - Mensagem retornada pelo api-pagamento-instantaneo-ebank - POST /gi/eb/conta/movimento - O código de erro original será exibido no campo codigoErroComplementar,XXXX",
            "ERRO,FAILED,CT00001,Mensagem retornada pelo api-pagamento-instantaneo-ebank - POST /gi/eb/conta/valida_conta_recebedor - O código de erro original será exibido no campo codigoErroComplementar,XXXX",
            "ERRO,FAILED,TI00001,Mensagem retornada pelo api-pagamento-instantaneo-ebank - POST /gi/eb/conta/transferencia_interna - O código de erro original será exibido no campo codigoErroComplementar,XXXX",
            "ERRO,FAILED,XXXXXXX,Erro nao mapeado.,",
        ],
    )
    fun `should return SettlementGenericTemporaryError error on checkPaymentStatus with temporary errors`(
        statusOrdemPagamento: String,
        pixPaymentStatus: PixPaymentStatus,
        codigoErro: String,
        descricaoErro: String,
        codigoErroComplementar: String?,
    ) {
        testErrorHandlingOnCheckPaymentStatus(
            statusOrdemPagamento = statusOrdemPagamento,
            pixPaymentStatus = pixPaymentStatus,
            codigoErro = codigoErro,
            descricaoErro = descricaoErro,
            codigoErroComplementar = codigoErroComplementar,
            expectedResponse = PixTransactionError.SettlementGenericTemporaryError,
        )
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "ERRO,FAILED,AB00005,Não são permitidos pagamentos para essa instituição.,",
        ],
    )
    fun `should return SettlementDestinationInstitutionNotAllowed error on checkPaymentStatus with destination institution not allowed`(
        statusOrdemPagamento: String,
        pixPaymentStatus: PixPaymentStatus,
        codigoErro: String,
        descricaoErro: String,
        codigoErroComplementar: String?,
    ) {
        testErrorHandlingOnCheckPaymentStatus(
            statusOrdemPagamento = statusOrdemPagamento,
            pixPaymentStatus = pixPaymentStatus,
            codigoErro = codigoErro,
            descricaoErro = descricaoErro,
            codigoErroComplementar = codigoErroComplementar,
            expectedResponse = PixTransactionError.SettlementDestinationInstitutionNotAllowed,
        )
    }

    @Test
    fun `should return unknown on checkPaymentStatus with empty response field`() {
        val bankOperationId = BankOperationId(idOrdemPagamento.toString())
        every {
            httpClientMock.retrieve(
                ofType(HttpRequest::class),
                ofType(Argument::class),
                ofType(Argument::class),
            )
        } answers {
            val response = HttpResponse.status<List<ArbiPixResponseTO>>(HttpStatus.PRECONDITION_FAILED)
                .body(listOf(ArbiPixResponseTO(response = "", status = 412)))
            Flowable.error(HttpClientResponseException("erro", response))
        }
        val pixResult = arbiPixPaymentAdapter.checkPaymentStatus(null, bankOperationId)
        pixResult.idOrdemPagamento shouldBe bankOperationId.value
        pixResult.status shouldBe PixPaymentStatus.UNKNOWN
        pixResult.error shouldBe PixTransactionError.PaymentGenericTemporaryError
        verify { httpClientMock.retrieve(ofType(HttpRequest::class), ofType(Argument::class), ofType(Argument::class)) }
    }

    @Test
    fun `should return failed on checkPaymentStatus with response Nao Localizado`() {
        val bankOperationId = BankOperationId(idOrdemPagamento.toString())
        every {
            httpClientMock.retrieve(
                ofType(HttpRequest::class),
                ofType(Argument::class),
                ofType(Argument::class),
            )
        } answers {
            val response = HttpResponse.status<List<ArbiPixResponseTO>>(HttpStatus.PRECONDITION_FAILED)
                .body(listOf(ArbiPixResponseTO(response = "Não Localizado/Inválido", status = 412)))
            Flowable.error(HttpClientResponseException("erro", response))
        }
        val pixResult = arbiPixPaymentAdapter.checkPaymentStatus(null, bankOperationId)
        pixResult.idOrdemPagamento shouldBe bankOperationId.value
        pixResult.status shouldBe PixPaymentStatus.FAILED
        pixResult.error shouldBe PixTransactionError.PaymentGenericTemporaryError
        verify { httpClientMock.retrieve(ofType(HttpRequest::class), ofType(Argument::class), ofType(Argument::class)) }
    }

    @Test
    fun `should return unknown on checkPaymentStatus with invalid response body`() {
        val bankOperationId = BankOperationId(idOrdemPagamento.toString())
        every {
            httpClientMock.retrieve(
                ofType(HttpRequest::class),
                ofType(Argument::class),
                ofType(Argument::class),
            )
        } answers {
            val response = HttpResponse.status<Unit>(HttpStatus.PRECONDITION_FAILED)
                .body(null)
            Flowable.error(HttpClientResponseException("erro", response))
        }
        val pixResult = arbiPixPaymentAdapter.checkPaymentStatus(null, bankOperationId)
        pixResult.idOrdemPagamento shouldBe bankOperationId.value
        pixResult.status shouldBe PixPaymentStatus.UNKNOWN
        pixResult.error shouldBe PixTransactionError.PaymentGenericTemporaryError
        verify { httpClientMock.retrieve(ofType(HttpRequest::class), ofType(Argument::class), ofType(Argument::class)) }
    }

    private fun testErrorHandlingOnCheckPaymentStatus(
        statusOrdemPagamento: String,
        pixPaymentStatus: PixPaymentStatus,
        codigoErro: String,
        descricaoErro: String,
        codigoErroComplementar: String?,
        expectedResponse: PixTransactionError,
    ) {
        val bankOperationId = BankOperationId(idOrdemPagamento.toString())
        every {
            httpClientMock.retrieve(
                ofType(HttpRequest::class),
                ofType(Argument::class),
                ofType(Argument::class),
            )
        } answers {
            val response = HttpResponse.status<List<ArbiPixResponseTO>>(HttpStatus.PRECONDITION_FAILED)
                .body(
                    listOf(
                        ArbiPixResponseTO(
                            response = buildResponse(
                                bankOperationId,
                                statusOrdemPagamento,
                                codigoErro,
                                descricaoErro,
                                codigoErroComplementar,
                            ),
                            status = 412,
                        ),
                    ),
                )
            Flowable.error(HttpClientResponseException("erro", response))
        }
        val pixResult = arbiPixPaymentAdapter.checkPaymentStatus(null, bankOperationId)
        pixResult.idOrdemPagamento shouldBe bankOperationId.value
        pixResult.status shouldBe pixPaymentStatus
        pixResult.error shouldBe expectedResponse
        verify { httpClientMock.retrieve(ofType(HttpRequest::class), ofType(Argument::class), ofType(Argument::class)) }
    }

    private fun buildResponse(
        bankOperationId: BankOperationId,
        statusOrdemPagamento: String,
        codigoErro: String,
        descricaoErro: String,
        codigoErroComplementar: String?,
    ): String {
        return jacksonObjectMapper().writeValueAsString(
            ArbiPixPaymentResponseTO(
                idOrdemPagamento = bankOperationId.value,
                endToEnd = "E54403563202101051202UgxpyJ9JNz3",
                statusOrdemPagamento = statusOrdemPagamento,
                infoOrdemPagamento = "",
                erros = listOf(
                    ArbiPixErrorResponseTO(
                        codigoErro = codigoErro,
                        descricaoErro = descricaoErro,
                        codigoErroComplementar = codigoErroComplementar,
                    ),
                ),
            ),
        )
    }

    companion object {
        @JvmStatic
        fun pixKeyErrors(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(PixKeyError.SystemUnavailable, PixTransactionError.SystemUnavailable),
                Arguments.of(PixKeyError.UnknownError, PixTransactionError.SettlementGenericTemporaryError),
                Arguments.of(PixKeyError.KeyNotConfirmed, PixTransactionError.SettlementGenericTemporaryError),
                Arguments.of(PixKeyError.MalformedKey, PixTransactionError.InvalidPixkey),
                Arguments.of(PixKeyError.KeyNotFound, PixTransactionError.InvalidPixkey),
            )
        }
    }
}