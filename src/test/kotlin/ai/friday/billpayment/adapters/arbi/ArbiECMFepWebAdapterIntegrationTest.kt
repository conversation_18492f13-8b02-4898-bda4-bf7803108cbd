/*
package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountRegisterDocument
import ai.friday.billpayment.app.account.SendSimpleSignUpDocumentsRequest
import ai.friday.billpayment.app.integrations.ECMProvider
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@MicronautTest(environments = [FRIDAY_ENV, "staging"])
@Disabled
class ArbiECMFepWebAdapterIntegrationTest(private val ecmProvider: ECMProvider) {

    val currentName = "Nome aleatorio do usuario"

    @Test
    fun sendSimpleSignUpDocuments() {
        val selfie = Thread.currentThread().contextClassLoader.getResourceAsStream("images/gringo-foto.png")!!
        val contract = Thread.currentThread().contextClassLoader.getResourceAsStream("arbi/sample.pdf")!!
        val kyc = Thread.currentThread().contextClassLoader.getResourceAsStream("arbi/sample.pdf")!!

        ecmProvider.sendSimpleSignUpDocuments(
            SendSimpleSignUpDocumentsRequest(
                id = AccountId().value,
                name = "Nome aleatorio",
                document = "***********",
                emailAddress = EmailAddress(email = "${currentName.replace(" ", "")}@fakeemail.com"),
            ).apply {
                userContract = AccountRegisterDocument(content = contract.readAllBytes(), name = "contrato", extension = "pdf")
                userKYC = AccountRegisterDocument(content = kyc.readAllBytes(), name = "kyc", extension = "pdf")
                userSelfie = AccountRegisterDocument(content = selfie.readAllBytes(), name = "selfie", extension = "png")
            },
        )
    }
}*/