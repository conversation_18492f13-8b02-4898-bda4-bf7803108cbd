/*
package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.fingerprint.DeviceId
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
@MicronautTest(environments = [FRIDAY_ENV])
internal class ArbiPIXAdapterTest(private val arbiPIXAdapter: NewArbiPixKeyManagementAdapter) {

    enum class ClaimType {
        PORTABILITY,
    }

    enum class KeyType {
        CNPJ, EMAIL, EVP, CPF
    }

    enum class Reason {
        USER_REQUESTED,
    }

    @Test
    fun testRegisterKey() {
        val returnedKey: PixKey = arbiPIXAdapter.registerKey(
            accountNo = AccountNumber("**********"),
            key = PixKey("***********", PixKeyType.PHONE),
            document = "***********",
            name = "teste",
            deviceId = DeviceId("***********"),
        )
        println(returnedKey)
    }

    @Test
    fun testFindKey() {
        val a = arbiPIXAdapter.findKeyDetails(key = PixKey("", PixKeyType.PHONE), document = "***********")
        println()
        println(a)
    }

    @Test
    fun deleteKey() {
        arbiPIXAdapter.deleteKey(key = "***********", document = Document(""), null)
    }

    @Test
    fun testRunClaim() {
        runClaim(KeyType.CPF.name, "***********", ClaimType.PORTABILITY.name)
    }

    @Test
    fun testCancelClaim() {
        arbiPIXAdapter.cancelClaim("", Reason.USER_REQUESTED.name)
    }

    @Test
    fun testConfirmClaim() {
        arbiPIXAdapter.confirmClaim("4503c623-6ec8-439a-9415-01e95a509c71", Reason.USER_REQUESTED.name)
    }

    @Test
    fun completeClaim() {
        arbiPIXAdapter.completeClaim("4503c623-6ec8-439a-9415-01e95a509c71")
    }

    private fun runClaim(documentType: String, key: String, type: String) {
        val claimId = arbiPIXAdapter.startClaim(key, documentType, type)
        arbiPIXAdapter.completeClaim(claimId)
    }
}*/