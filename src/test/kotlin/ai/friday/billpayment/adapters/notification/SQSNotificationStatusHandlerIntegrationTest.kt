/*
package ai.friday.billpayment.adapters.notification

import ai.friday.billpayment.app.notification.MessageProcessorServiceImpl
import io.mockk.every
import io.mockk.mockk
import io.via1.communicationcentre.app.notification.NotificationStatus
import io.via1.communicationcentre.app.notification.NotificationStatusResult
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import software.amazon.awssdk.core.client.config.ClientOverrideConfiguration
import software.amazon.awssdk.core.retry.RetryPolicy
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.sqs.SqsClient

@Disabled
internal class SQSNotificationStatusHandlerIntegrationTest {

    private val sqsClient = SqsClient.builder()
        .region(Region.of("us-east-1"))
        .overrideConfiguration(
            ClientOverrideConfiguration.builder()
                .retryPolicy(RetryPolicy.builder().numRetries(25).build()).build(),
        )
        .build()

    private val sqsNotificationStatusHandler = SQSNotificationStatusHandler(
        amazonSQS = sqsClient,
        configuration = mockk() {
            every { visibilityTimeout } returns 300
            every { maxNumberOfMessages } returns 1
            every { sqsWaitTime } returns 20
        },
        whatsappNotificationVerifierQueueName = "whatsapp_notification_verifier",
        messageProcessorService = MessageProcessorServiceImpl(
            checkableNotificationService = mockk {
                every { checkStatus(any()) } returns NotificationStatusResult(NotificationStatus.PROCESSING)
            },
            notificationService = mockk(),
            accountRegisterService = mockk(),
            loginService = mockk(),
            messagePublisher = mockk(),
            whatsappNotificationVerifierQueueName = "whatsapp_notification_verifier",
        ),

        timeWindowNotificationsConfiguration = mockk {
            every { cron } returns "* * * * * *"
            every { toleranceInSeconds } returns 1
        },
    )

    @Test
    fun `test ApproximateReceiveCount`() {
        sqsNotificationStatusHandler.onApplicationEvent(mockk())
        sqsNotificationStatusHandler.receiveMessages()
    }
}*/