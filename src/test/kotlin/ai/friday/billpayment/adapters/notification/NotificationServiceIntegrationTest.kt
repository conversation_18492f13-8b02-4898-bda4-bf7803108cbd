/*
package ai.friday.billpayment.adapters.notification

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.FRIDAY_ENV
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.via1.communicationcentre.app.integrations.NotificationService
import io.via1.communicationcentre.app.notification.Notification
import io.via1.communicationcentre.app.notification.NotificationChannel
import io.via1.communicationcentre.app.notification.NotificationStatus
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
@MicronautTest(environments = [FRIDAY_ENV])
internal class NotificationServiceIntegrationTest(
    private val notificationService: NotificationService,
) {
    @Test
    fun sendMessage() {
        val messageId = "d3dc845f-1157-4f00-8a31-2dd2d302dc73"
        val jsonNotification =
            """{"notificationId":"$messageId","externalUserId":null,"email":null,"mobilePhone":"5521980007494","template":"wallet_one_pix_pay_failure__4_16_0","notificationChannel":"WHATSAPP","emailParameters":null,"whatsAppParameters":[],"buttonWhatsAppParameter":"entrar","quickReplyButtonsWhatsAppParameter":null}"""

        val notification = parseObjectFrom<Notification>(jsonNotification)

        notificationService.notify(notification)
        println(notification.notificationId)
    }

    @Test
    fun checkMessageStatusFailed() {
        val response =
            notificationService.checkStatus("d3dc845f-1157-4f00-8a31-2dd2d302dc73", NotificationChannel.WHATSAPP)
        response.status shouldBe NotificationStatus.FAILED
        response.reason.description shouldBe "WhatsApp API response status code does not indicate success: 500 (InternalServerError). Error: Internal error: Connection timed out. Please check if wacore is running: **************:6250 (1014)"
    }

    @Test
    fun checkMessageStatusSuccess() {
        val response =
            notificationService.checkStatus("7223354c-a4c9-4222-84c4-5aadf9584b83", NotificationChannel.WHATSAPP)
        response.status shouldBe NotificationStatus.SUCCESS
        response.reason.shouldBeNull()
    }
}*/