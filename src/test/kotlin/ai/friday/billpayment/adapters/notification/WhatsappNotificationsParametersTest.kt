package ai.friday.billpayment.adapters.notification

import ai.friday.billpayment.EMAIL_ADDRESS
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentStatus
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.account.isAlphaGroup
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.AddBillError
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.FichaCompensacaoAdded
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.bill.calculateEffectiveDate
import ai.friday.billpayment.app.bill.name
import ai.friday.billpayment.app.bill.toSourceName
import ai.friday.billpayment.app.chatbot.ChatbotMessagePublisher
import ai.friday.billpayment.app.forecast.ForecastPeriod
import ai.friday.billpayment.app.formatBrazilCurrency
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.itp.PaymentIntentId
import ai.friday.billpayment.app.notification.BillPaymentNotification
import ai.friday.billpayment.app.notification.ButtonDeeplinkParameter
import ai.friday.billpayment.app.notification.ButtonRawParameter
import ai.friday.billpayment.app.notification.ButtonWebParameter
import ai.friday.billpayment.app.notification.DefaultNotificationService
import ai.friday.billpayment.app.notification.EmailNotification
import ai.friday.billpayment.app.notification.MultiChannelNotification
import ai.friday.billpayment.app.notification.NotificationTemplate
import ai.friday.billpayment.app.notification.TemplatesConfiguration
import ai.friday.billpayment.app.notification.WhatsappNotification
import ai.friday.billpayment.app.notification.WhatsappNotificationBuilder
import ai.friday.billpayment.app.notification.buildReceiptBillButtonPath
import ai.friday.billpayment.app.notification.buildWalletBillButtonPath
import ai.friday.billpayment.app.onepixpay.UUIDOnePixPayId
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.BillRegisterData
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.payment.ReceiptFilesData
import ai.friday.billpayment.app.payment.buildFormattedAmount
import ai.friday.billpayment.app.payment.formatAccountNumber
import ai.friday.billpayment.app.payment.formatBankData
import ai.friday.billpayment.app.payment.formatRoutingNumber
import ai.friday.billpayment.app.payment.maskDocumentSpecialAsterisk
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionStatus
import ai.friday.billpayment.app.utilityaccount.UtilityAccountUpdateInfo
import ai.friday.billpayment.app.utilityaccount.UtilityConnectionMethod
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.Invite
import ai.friday.billpayment.app.wallet.InviteStatus
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.simpleName
import ai.friday.billpayment.fixture.ReceiptFixture.boleto
import ai.friday.billpayment.fixture.ReceiptFixture.invoice
import ai.friday.billpayment.fixture.ReceiptFixture.pix
import ai.friday.billpayment.fixture.UtilityAccountFixture
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.AccountFixture.createAccountConfiguration
import ai.friday.billpayment.integration.BILL_ID_4
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.internalBankAccount
import ai.friday.billpayment.toAmountFormat
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.timeFormat
import ai.friday.morning.date.withGivenDateTime
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.context.annotation.Property
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import io.via1.communicationcentre.app.notification.NotificationFormatter
import java.time.Duration
import java.time.Instant
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
open class WhatsappNotificationsParametersTest(
    templatesConfiguration: TemplatesConfiguration,
    private val whatsappNotificationBuilder: WhatsappNotificationBuilder,
) {
    private val accountId = AccountId("ACCOUNT-ID-Para-testar")
    private val emailAddress = EmailAddress("<EMAIL>")
    private val mobilePhone = MobilePhone("***********")

    @field:Property(name = "urls.site")
    lateinit var urlsSite: String

    private val account: Account = mockk() {
        every {
            accountId
        } returns <EMAIL>
        every {
            mobilePhone
        } returns "***********"
        every {
            emailAddress
        } returns <EMAIL>
        every {
            hasGroup(any())
        } returns false
        every {
            configuration
        } returns createAccountConfiguration()

        every {
            paymentStatus
        } returns AccountPaymentStatus.UpToDate
    }

    private val accountRepository = mockk<AccountRepository>() {
        every {
            findById(any())
        } returns account
    }

    private val chatbotMessagePublisher: ChatbotMessagePublisher = mockk(relaxed = true)

    private val messagePublisher = mockk<MessagePublisher>(relaxUnitFun = true)
    private val defaultNotificationService = spyk(
        DefaultNotificationService(
            accountRepository = accountRepository,
            shouldNotifyUser = mockk() {
                every { shouldNotify(any(), any()) } returns true
            },
            messagePublisher = messagePublisher,
            notificationQueueName = "notification-queue",
            pushNotificationService = mockk(relaxUnitFun = true) {
                every {
                    canSendViaPush(any())
                } returns false
            },
            chatbotMessagePublisher = chatbotMessagePublisher,
        ),
    )

    val systemActivityService = mockk<SystemActivityService>()
    private val notificationAdapter: CommCentreAdapter = CommCentreAdapter(
        accountRepository = accountRepository,
        billPaymentEmailSenderService = mockk(),
        emailSenderConfiguration = mockk(),
        systemActivityService = systemActivityService,
        notificationService = defaultNotificationService,
        fee = 0.0,
        templatesConfiguration = templatesConfiguration,
        featureConfiguration = allFalseFeatureConfiguration,
    )

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    private val member = wallet.founder

    private val members = listOf(member)
    private val dueDate = getZonedDateTime().toLocalDate()
    private val timeLimit = LocalTime.of(19, 0)
    private val actionSource = ActionSource.Api(wallet.founder.accountId)
    private val mailBoxActionSource = ActionSource.WalletMailBox(accountId, emailAddress.value)
    private val billView = getActiveBill().copy(walletId = wallet.id, source = actionSource)
    private val amount = billView.amount
    private val payee = billView.payee
    private val billId = billView.billId
    private val walletId = wallet.id
    private val walletName = wallet.name
    private val author = wallet.founder
    private val description = billView.billDescription
    private val barCode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE)
    private val hint = "hint"
    private val from = emailAddress
    private val subject = "Subject da notificação"
    private val errorMessage = "errorMessage"
    private val recipient = Recipient(name = "Claro", document = DOCUMENT)
    private val invite = Invite(
        walletId = walletId,
        memberDocument = "***********",
        emailAddress = EmailAddress("<EMAIL>"),
        memberName = "nome do convidado",
        memberType = MemberType.COFOUNDER,
        permissions = MemberPermissions(
            viewBills = BillPermission.BY_CRITERIA,
            scheduleBills = BillPermission.BY_CRITERIA,
            criteriaForViewingBills = null,
            founderContactsEnabled = false,
            manageMembers = false,
            viewBalance = false,
            notification = true,
        ),
        status = InviteStatus.PENDING,
        validUntil = getLocalDate(),
        walletName = walletName,
        founderName = wallet.founder.name,
        founderDocument = wallet.founder.document,
        created = getZonedDateTime().minusDays(1),
        updatedAt = getZonedDateTime().minusDays(1),
    )
    private val receiver = EmailAddress("<EMAIL>")
    private val utilityAccount = UtilityAccountFixture.create()

    @BeforeEach
    fun init() {
        notificationAdapter.apply {
            siteUrl = urlsSite
        }
    }

    private fun validateEmailNotificationSent(validationBlock: (notification: EmailNotification) -> Unit) {
        val notificationSlot = slot<BillPaymentNotification>()
        verify {
            defaultNotificationService.sendNotification(
                any(),
                any(),
                capture<BillPaymentNotification>(notificationSlot),
            )
        }

        val notification = notificationSlot.captured
        assertSoftly {
            notification.shouldBeTypeOf<EmailNotification>()
            validationBlock(notification)
        }
    }

    private fun validateChatbotNotificationSent(validationBlock: (notification: WhatsappNotification) -> Unit) {
        val notificationSlot = slot<WhatsappNotification>()
        verify {
            chatbotMessagePublisher.publishGenericWhatsappNotification(
                account = any(),
                notification = capture(notificationSlot),
            )
        }

        val notification = notificationSlot.captured
        assertSoftly {
            notification.shouldBeTypeOf<WhatsappNotification>()
            validationBlock(notification)
        }
    }

    private fun validateChatbotNotificationMultichannel(validationBlock: (notification: WhatsappNotification) -> Unit) {
        val notificationSlot = slot<MultiChannelNotification>()
        verify {
            chatbotMessagePublisher.publishNotification(capture(notificationSlot))
        }

        val notification = notificationSlot.captured
        assertSoftly {
            notification.shouldBeTypeOf<MultiChannelNotification>()
            val whatsNotification = whatsappNotificationBuilder.buildFromMultiChannelNotification(notification)
            validationBlock(whatsNotification)
        }
    }

    /*
        @Test
        fun `enviar notifyBillCreated quando é boleto e usuário está atrasado`() {
            every {
                account.configuration
            } returns createAccountConfiguration().copy(
                receiveNotification = true,
            )

            every { account.subscriptionType } returns SubscriptionType.PIX
            every { account.paymentStatus } returns AccountPaymentStatus.Overdue

            every { account.subscriptionType } returns SubscriptionType.PIX

            notificationAdapter.notifyBillCreated(
                members,
                payee,
                amount,
                dueDate,
                BillType.FICHA_COMPENSACAO,
                billId,
                BillStatus.ACTIVE,
                walletId,
                walletName,
                author,
                description,
                ActionSource.Api(accountId),
                hint,
            )

            validateChatbotNotificationSent { notification ->
                notification.template shouldBe _root_ide_package_.ai.friday.billpayment.app.notification.NotificationTemplate("barcode_bill_add_simple__0_0_4")
                notification.parameters shouldBe buildList {
                    add(buildMaskedBillName(payee))
                    add(NotificationFormatter.buildFormattedDueDate(dueDate))
                    add(actionSource.toSourceName())
                    add(author.simpleName())
                    add(walletName)
                }
                notification.quickReplyButtonsWhatsAppParameter shouldBe buildList {
                    add("""{"payload": "", "action": "SEND_SUBSCRIPTION_PIX_CODE"}""")
                }
                notification.buttonWhatsAppParameter shouldBe null
            }
        }
    */
    /*
    @Test
    fun `enviar notifyBillCreated quando é boleto e usuário está atrasado e não tem author`() {
        every {
            account.configuration
        } returns createAccountConfiguration().copy(
            receiveDDANotification = true,
        )

        every { account.subscriptionType } returns SubscriptionType.PIX
        every { account.paymentStatus } returns AccountPaymentStatus.Overdue

        notificationAdapter.notifyBillCreated(
            members,
            payee,
            amount,
            dueDate,
            BillType.FICHA_COMPENSACAO,
            billId,
            BillStatus.ACTIVE,
            walletId,
            walletName,
            null,
            description,
            ActionSource.Api(accountId),
            hint,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe _root_ide_package_.ai.friday.billpayment.app.notification.NotificationTemplate("barcode_bill_add_simple_no_author__0_0_5")
            notification.parameters shouldBe buildList {
                add(buildMaskedBillName(payee))
                add(NotificationFormatter.buildFormattedDueDate(dueDate))
                add(actionSource.toSourceName())
                add(walletName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe buildList {
                add("""{"payload": "", "action": "SEND_SUBSCRIPTION_PIX_CODE"}""")
            }
            notification.buttonWhatsAppParameter shouldBe null
        }
    }
*/
    @Test
    fun `enviar notifyBillCreated quando é boleto e usuário não está atrasado (barcode_bill_add_known_author_with_description_success)`() {
        every {
            account.configuration
        } returns createAccountConfiguration().copy(
            receiveDDANotification = true,
        )

        every { account.subscriptionType } returns SubscriptionType.PIX
        every { account.paymentStatus } returns AccountPaymentStatus.UpToDate

        notificationAdapter.notifyBillCreated(
            members,
            payee,
            amount,
            dueDate,
            BillType.FICHA_COMPENSACAO,
            billId,
            BillStatus.ACTIVE,
            walletId,
            walletName,
            author,
            description,
            ActionSource.Api(accountId),
            hint,
        )

        validateChatbotNotificationMultichannel { notification ->
            notification.template shouldBe NotificationTemplate("barcode_bill_add_known_author_with_description_success__4_11_0")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedDueDate(dueDate))
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(description)
                add(actionSource.toSourceName())
                add(author.simpleName())
                add(walletName)
                add(null)
            }.filterNotNull()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("contas/carteira/${walletId.value}/bill-timeline-entry@${billId.value}")
        }
    }

    @Test
    fun `enviar notifyBillCreated quando é boleto e usuário não está atrasado (barcode_bill_add_known_author_no_description_success)`() {
        every {
            account.configuration
        } returns createAccountConfiguration().copy(
            receiveDDANotification = true,
        )

        every { account.subscriptionType } returns SubscriptionType.PIX
        every { account.paymentStatus } returns AccountPaymentStatus.UpToDate

        notificationAdapter.notifyBillCreated(
            members,
            payee,
            amount,
            dueDate,
            BillType.FICHA_COMPENSACAO,
            billId,
            BillStatus.ACTIVE,
            walletId,
            walletName,
            author,
            null,
            ActionSource.Api(accountId),
            null,
        )

        validateChatbotNotificationMultichannel { notification ->
            notification.template shouldBe NotificationTemplate("barcode_bill_add_known_author_no_description_success__4_11_0")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedDueDate(dueDate))
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(null)
                add(actionSource.toSourceName())
                add(author.simpleName())
                add(walletName)
                add(null)
            }.filterNotNull()
            notification.buttonWhatsAppParameter shouldBe buildWalletBillButtonPath(walletId, billId)
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
        }
    }

    @Test
    fun `enviar notifyBillCreated quando é boleto e usuário não está atrasado (barcode_bill_add_known_author_no_description_success_with_hint)`() {
        every {
            account.configuration
        } returns createAccountConfiguration().copy(
            receiveDDANotification = true,
        )

        every { account.subscriptionType } returns SubscriptionType.PIX
        every { account.paymentStatus } returns AccountPaymentStatus.UpToDate

        notificationAdapter.notifyBillCreated(
            members,
            payee,
            amount,
            dueDate,
            BillType.FICHA_COMPENSACAO,
            billId,
            BillStatus.ACTIVE,
            walletId,
            walletName,
            author,
            null,
            ActionSource.Api(accountId),
            hint,
        )

        validateChatbotNotificationMultichannel { notification ->
            notification.template shouldBe NotificationTemplate("barcode_bill_add_known_author_no_description_success_with_hint__4_11_1")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedDueDate(dueDate))
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(null)
                add(actionSource.toSourceName())
                add(author.simpleName())
                add(walletName)
                add(hint)
            }.filterNotNull()
            notification.buttonWhatsAppParameter shouldBe buildWalletBillButtonPath(walletId, billId)
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
        }
    }

    @Test
    fun `enviar notifyBillCreated quando é boleto e usuário não está atrasado (barcode_bill_add_no_author_with_description_success)`() {
        every {
            account.configuration
        } returns createAccountConfiguration().copy(
            receiveDDANotification = true,
        )

        every { account.subscriptionType } returns SubscriptionType.PIX
        every { account.paymentStatus } returns AccountPaymentStatus.UpToDate

        notificationAdapter.notifyBillCreated(
            members,
            payee,
            amount,
            dueDate,
            BillType.FICHA_COMPENSACAO,
            billId,
            BillStatus.ACTIVE,
            walletId,
            walletName,
            null,
            description,
            ActionSource.Api(accountId),
            null,
        )

        validateChatbotNotificationMultichannel { notification ->
            notification.template shouldBe NotificationTemplate("barcode_bill_add_no_author_with_description_success__4_12_0")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedDueDate(dueDate))
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(description)
                add(actionSource.toSourceName())
                add(null)
                add(walletName)
                add(null)
            }.filterNotNull()
            notification.buttonWhatsAppParameter shouldBe buildWalletBillButtonPath(walletId, billId)
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
        }
    }

    @Test
    fun `enviar notifyBillCreated quando é boleto e usuário não está atrasado (barcode_bill_add_no_author_no_description_success)`() {
        every {
            account.configuration
        } returns createAccountConfiguration().copy(
            receiveDDANotification = true,
        )

        every { account.subscriptionType } returns SubscriptionType.PIX
        every { account.paymentStatus } returns AccountPaymentStatus.UpToDate

        notificationAdapter.notifyBillCreated(
            members,
            payee,
            amount,
            dueDate,
            BillType.FICHA_COMPENSACAO,
            billId,
            BillStatus.ACTIVE,
            walletId,
            walletName,
            null,
            null,
            ActionSource.Api(accountId),
            null,
        )

        validateChatbotNotificationMultichannel { notification ->
            notification.template shouldBe NotificationTemplate("barcode_bill_add_no_author_no_description_success__4_11_0")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedDueDate(dueDate))
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(null)
                add(actionSource.toSourceName())
                add(null)
                add(walletName)
                add(null)
            }.filterNotNull()
            notification.buttonWhatsAppParameter shouldBe buildWalletBillButtonPath(walletId, billId)
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
        }
    }

    @Test
    fun `enviar notifyBillCreated quando não é boleto (invoice_add_with_description_success)`() {
        every {
            account.configuration
        } returns createAccountConfiguration().copy(
            receiveDDANotification = true,
        )

        every {
            account.paymentStatus
        } returns AccountPaymentStatus.UpToDate

        notificationAdapter.notifyBillCreated(
            members,
            payee,
            amount,
            dueDate,
            BillType.PIX,
            billId,
            BillStatus.ACTIVE,
            walletId,
            walletName,
            author,
            description,
            ActionSource.Api(accountId),
            hint,
        )

        validateChatbotNotificationMultichannel { notification ->
            notification.template shouldBe NotificationTemplate("invoice_add_with_description_success__4_11_1")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedDueDate(dueDate))
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(description)
                add(actionSource.toSourceName())
                add(author.simpleName())
                add(walletName)
            }.filterNotNull()
            notification.buttonWhatsAppParameter shouldBe buildWalletBillButtonPath(walletId, billId)
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
        }
    }

    @Test
    fun `enviar notifyBillCreate quando conta chegou por email e não está aprovada`() {
        every {
            account.configuration
        } returns createAccountConfiguration().copy(
            receiveDDANotification = true,
        )

        every {
            account.paymentStatus
        } returns AccountPaymentStatus.UpToDate

        notificationAdapter.notifyBillCreated(
            members,
            payee,
            amount,
            dueDate,
            BillType.FICHA_COMPENSACAO,
            billId,
            BillStatus.WAITING_APPROVAL,
            walletId,
            walletName,
            author,
            null,
            mailBoxActionSource,
            hint,
        )

        validateChatbotNotificationMultichannel { notification ->
            notification.template shouldBe NotificationTemplate("barcode_bill_add_waiting_approval__1_0_0")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedDueDate(dueDate))
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(mailBoxActionSource.name())
                add(emailAddress.value)
                add(walletName)
            }.filterNotNull()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("contas/carteira/${walletId.value}/bill-timeline-entry@${billId.value}")
        }
    }

    @Test
    fun `enviar notifyBillCreated quando não é boleto (invoice_add_no_description_success)`() {
        every {
            account.configuration
        } returns createAccountConfiguration().copy(
            receiveDDANotification = true,
        )

        every {
            account.paymentStatus
        } returns AccountPaymentStatus.UpToDate

        notificationAdapter.notifyBillCreated(
            members,
            payee,
            amount,
            dueDate,
            BillType.PIX,
            billId,
            BillStatus.ACTIVE,
            walletId,
            walletName,
            author,
            null,
            ActionSource.Api(accountId),
            hint,
        )

        validateChatbotNotificationMultichannel { notification ->
            notification.template shouldBe NotificationTemplate("invoice_add_no_description_success__4_11_1")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedDueDate(dueDate))
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(null)
                add(actionSource.toSourceName())
                add(author.simpleName())
                add(walletName)
            }.filterNotNull()
            notification.buttonWhatsAppParameter shouldBe buildWalletBillButtonPath(walletId, billId)
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
        }
    }

    @Test
    fun `enviar notifyBillComingDue quando é boleto`() {
        notificationAdapter.notifyBillComingDue(
            members,
            author,
            walletName,
            walletId,
            billView,
            hint,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("barcode_bill_close_overdue_known_author_with_description__4_11_0")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(description)
                add(actionSource.toSourceName())
                add(author.simpleName())
                add(walletName)
                add(null)
            }.filterNotNull()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("contas/carteira/${walletId.value}/bill-timeline-entry@${billId.value}")
        }
    }

    @Test
    fun `enviar notifyBillComingDue quando é boleto (barcode_bill_close_overdue_known_author_no_description)`() {
        val localBillView = getActiveBill().copy(
            walletId = wallet.id,
            source = actionSource,
            billDescription = "",
        )

        notificationAdapter.notifyBillComingDue(
            members,
            author,
            walletName,
            walletId,
            localBillView,
            null,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("barcode_bill_close_overdue_known_author_no_description__4_11_0")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(null)
                add(actionSource.toSourceName())
                add(author.simpleName())
                add(walletName)
                add(null)
            }.filterNotNull()
            notification.buttonWhatsAppParameter shouldBe buildWalletBillButtonPath(walletId, billView.billId)
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
        }
    }

    @Test
    fun `enviar notifyBillComingDue quando é boleto (barcode_bill_close_overdue_known_author_no_description_with_hint)`() {
        val localBillView = getActiveBill().copy(
            walletId = wallet.id,
            source = actionSource,
            billDescription = "",
        )

        notificationAdapter.notifyBillComingDue(
            members,
            author,
            walletName,
            walletId,
            localBillView,
            hint,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("barcode_bill_close_overdue_known_author_no_description_with_hint__4_11_1")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(null)
                add(actionSource.toSourceName())
                add(author.simpleName())
                add(walletName)
                add(hint)
            }.filterNotNull()
            notification.buttonWhatsAppParameter shouldBe buildWalletBillButtonPath(walletId, billView.billId)
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
        }
    }

    @Test
    fun `enviar notifyBillComingDue quando é boleto (barcode_bill_close_overdue_no_author_with_description)`() {
        notificationAdapter.notifyBillComingDue(
            members,
            null,
            walletName,
            walletId,
            billView,
            hint,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("barcode_bill_close_overdue_no_author_with_description__4_17_0")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(billView.billDescription)
                add(actionSource.toSourceName())
                add(null)
                add(walletName)
                add(null)
            }.filterNotNull()
            notification.buttonWhatsAppParameter shouldBe buildWalletBillButtonPath(walletId, billView.billId)
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
        }
    }

    @Test
    fun `enviar notifyBillComingDue quando é boleto (barcode_bill_close_overdue_no_author_no_description)`() {
        val localBillView = getActiveBill().copy(
            walletId = wallet.id,
            source = actionSource,
            billDescription = "",
        )

        notificationAdapter.notifyBillComingDue(
            members,
            null,
            walletName,
            walletId,
            localBillView,
            hint,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("barcode_bill_close_overdue_no_author_no_description__4_17_0")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(null)
                add(actionSource.toSourceName())
                add(null)
                add(walletName)
                add(null)
            }.filterNotNull()
            notification.buttonWhatsAppParameter shouldBe buildWalletBillButtonPath(walletId, billView.billId)
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
        }
    }

    @Test
    fun `enviar notifyBillComingDue quando não é boleto (invoice_close_overdue_with_description)`() {
        notificationAdapter.notifyBillComingDue(
            members,
            author,
            walletName,
            walletId,
            billView.copy(billType = BillType.PIX),
            hint,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("invoice_close_overdue_with_description__4_11_0")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(description)
                add(actionSource.toSourceName())
                add(author.simpleName())
                add(walletName)
            }.filterNotNull()
            notification.buttonWhatsAppParameter shouldBe buildWalletBillButtonPath(walletId, billView.billId)
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
        }
    }

    @Test
    fun `enviar notifyBillComingDue quando não é boleto (invoice_close_overdue_no_description)`() {
        notificationAdapter.notifyBillComingDue(
            members,
            author,
            walletName,
            walletId,
            billView.copy(billType = BillType.PIX, billDescription = ""),
            hint,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("invoice_close_overdue_no_description__4_17_0")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(null)
                add(actionSource.toSourceName())
                add(author.simpleName())
                add(walletName)
            }.filterNotNull()
            notification.buttonWhatsAppParameter shouldBe buildWalletBillButtonPath(walletId, billView.billId)
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
        }
    }

    @Test
    fun `enviar notifyOnePixPay (singular)`() {
        val bills = listOf(billView)
        val oppid = UUIDOnePixPayId()
        val paymentLimitTime = bills.minOf { it.paymentLimitTime }

        withGivenDateTime(getZonedDateTime().withHour(12).withMinute(0).withSecond(0)) {
            val mapper = jacksonObjectMapper()
            val payload = mapper.writeValueAsString(
                mapOf(
                    "onePixPayId" to oppid.value,
                    "paymentLimitTimestamp" to getZonedDateTime().with(paymentLimitTime)
                        .toEpochSecond(),
                    "limitExceeded" to false,
                ),
            )

            notificationAdapter.notifyOnePixPay(
                members = members,
                walletName = walletName,
                onePixPayId = oppid,
                bills = bills,
                delay = Duration.ZERO,
                hasBasicAccountLimitExceeded = false,
            )

            validateChatbotNotificationSent { notification ->
                notification.template shouldBe NotificationTemplate("wallet_one_pix_pay_singular__4_17_0")
                notification.parameters shouldBe buildList {
                    add(bills.size.toString())
                    add(NotificationFormatter.buildFormattedAmount(bills.sumOf { bill -> bill.amountTotal }))
                    add(walletName)
                    add(NotificationFormatter.buildFormattedTime(bills[0].paymentLimitTime))
                }
                notification.quickReplyButtonsWhatsAppParameter shouldBe buildList {
                    add(payload)
                }
                notification.buttonWhatsAppParameter shouldBe null
            }
        }
    }

    @Test
    fun `enviar notifyOnePixPay (plural)`() {
        val bills = listOf(billView, billView)
        val oppid = UUIDOnePixPayId()
        val paymentLimitTime = bills.minOf { it.paymentLimitTime }

        withGivenDateTime(getZonedDateTime().withHour(12).withMinute(0).withSecond(0)) {
            val mapper = jacksonObjectMapper()
            val payload = mapper.writeValueAsString(
                mapOf(
                    "onePixPayId" to oppid.value,
                    "paymentLimitTimestamp" to getZonedDateTime().with(paymentLimitTime)
                        .toEpochSecond(),
                    "limitExceeded" to false,
                ),
            )

            notificationAdapter.notifyOnePixPay(
                members = members,
                walletName = walletName,
                onePixPayId = oppid,
                bills = bills,
                delay = Duration.ZERO,
                hasBasicAccountLimitExceeded = false,
            )

            validateChatbotNotificationSent { notification ->
                notification.template shouldBe NotificationTemplate("wallet_one_pix_pay__4_17_0")
                notification.parameters shouldBe buildList {
                    add(bills.size.toString())
                    add(NotificationFormatter.buildFormattedAmount(bills.sumOf { bill -> bill.amountTotal }))
                    add(walletName)
                    add(NotificationFormatter.buildFormattedTime(bills[0].paymentLimitTime))
                }
                notification.quickReplyButtonsWhatsAppParameter shouldBe buildList {
                    add(payload)
                }
                notification.buttonWhatsAppParameter shouldBe null
            }
        }
    }

    @Test
    fun `enviar notifyBillComingDueSecondaryWallet`() {
        val bills = listOf(billView)

        notificationAdapter.notifyBillComingDueSecondaryWallet(
            members,
            wallet = wallet,
            bills = bills,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("bill_coming_due_secondary_wallet_singular__1_0_5")
            notification.parameters shouldBe buildList {
                add(walletName)
                add(NotificationFormatter.buildFormattedAmount(bills.sumOf { it.amountTotal }))
                add(bills.first().formattedDescription())
                add(bills.first().amount.toAmountFormat())
            }.filterNotNull()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("contas/carteira/${walletId.value}")
        }
    }

    @Test
    fun `enviar notifyCashInFailure`() {
        notificationAdapter.notifyCashInFailure(
            members = members,
            amount = amount,
            errorMessage = errorMessage,
            walletId = walletId,
            walletName = walletName,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_cash_in_with_credit_card_failed__4_17_0")
            notification.parameters shouldBe buildList {
                add(NotificationFormatter.buildFormattedAmount(amount))
                add("errorMessage")
                add(walletName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("carteira/${walletId.value}/resumo")
        }
    }

    @Test
    fun `enviar notifyAddFailure para BillAlreadyExists`() {
        val billAddedFicha = FichaCompensacaoAdded(
            billId = BillId(BILL_ID_4),
            created = Instant.now().toEpochMilli(),
            walletId = WalletId(WALLET_ID),
            description = "ACTIVE BILL",
            dueDate = dueDate,
            amount = 15591L,
            barcode = barCode,
            recipient = Recipient(name = "Claro", document = DOCUMENT),
            recipientChain = null,
            assignor = "BRADESCO",
            document = DOCUMENT,
            paymentLimitTime = LocalTime.parse("17:00", timeFormat),
            lastSettleDate = getLocalDate().plusDays(1).format(dateFormat),
            expirationDate = "2020-10-05",
            actionSource = ActionSource.WalletMailBox(from = EMAIL_ADDRESS.toString()),
            effectiveDueDate = calculateEffectiveDate(
                getLocalDate().plusDays(3),
                BillType.FICHA_COMPENSACAO,
                barCode,
            ),
            amountTotal = 15591L,
            discount = 0,
            fine = 0,
            interest = 0,
            payerName = null,
            payerAlias = null,
            amountCalculationModel = AmountCalculationModel.UNKNOWN,
            fichaCompensacaoType = FichaCompensacaoType.CH_CHEQUE,
            interestData = null,
            fineData = null,
            discountData = null,
            abatement = null,
            divergentPayment = null,
        )

        val billFailure =
            CreateBillResult.FAILURE.BillAlreadyExists(Bill.build(billAddedFicha))

        notificationAdapter.notifyAddFailure(
            members = members,
            source = actionSource,
            from = from,
            subject = subject,
            walletName = walletName,
            billId = billId,
            walletId = walletId,
            result = billFailure,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_postal_box_add_duplicate__4_17_0")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedDueDate(dueDate))
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(actionSource.toSourceName())
                add(from.value)
                add(walletName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("contas/carteira/${walletId.value}/bill-timeline-entry@${billId.value}")
        }
    }

    @Test
    fun `enviar notifyAddFailure para BillNotPayable (PAYMENT_NOT_AUTHORIZED)`() {
        val billFailure =
            CreateBillResult.FAILURE.BillNotPayable(
                description = "description",
                code = AddBillError.PAYMENT_NOT_AUTHORIZED.code,
                billRegisterData = null,
            )

        val time = getZonedDateTime().withHour(9).withMinute(0).withSecond(0)
        withGivenDateTime(time) {
            notificationAdapter.notifyAddFailure(
                members = members,
                source = actionSource,
                from = from,
                subject = subject,
                walletName = walletName,
                billId = billId,
                walletId = walletId,
                result = billFailure,
            )
        }

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_postal_box_add_not_authorized__4_12_0")
            notification.parameters shouldBe buildList {
                add(from.value)
                add(walletName)
                add(NotificationFormatter.buildFormattedDateTime(time))
                add(NotificationFormatter.sanitize(subject))
                add(actionSource.toSourceName())
                add(from.value)
                add(walletName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyAddFailure para BillNotPayable (ALREADY_PAID)`() {
        val billFailure =
            CreateBillResult.FAILURE.BillNotPayable(
                description = "description",
                code = AddBillError.ALREADY_PAID.code,
                billRegisterData = null,
            )

        val time = getZonedDateTime().withHour(9).withMinute(0).withSecond(0)
        withGivenDateTime(time) {
            notificationAdapter.notifyAddFailure(
                members = members,
                source = actionSource,
                from = from,
                subject = subject,
                walletName = walletName,
                billId = billId,
                walletId = walletId,
                result = billFailure,
            )
        }

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_postal_box_add_non_payable_no_data__4_11_1")
            notification.parameters shouldBe buildList {
                add(from.value)
                add(walletName)
                add(NotificationFormatter.buildFormattedDateTime(time))
                add(NotificationFormatter.sanitize(subject))
                add(actionSource.toSourceName())
                add(from.value)
                add(walletName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyAddFailure para BillNotPayable (with BillRegisterData)`() {
        val billRegisterData = BillRegisterData(
            billType = BillType.CONCESSIONARIA,
            assignor = "assignorTest",
            recipient = Recipient(name = "Claro"),
            recipientChain = null,
            payerDocument = "payerTest",
            amount = 155_91L,
            discount = 0,
            interest = 0,
            fine = 0,
            amountTotal = 155_91L,
            dueDate = dueDate,
            paymentLimitTime = "00:00",
            expirationDate = getLocalDate(),
            settleDate = getLocalDate(),
            amountCalculationModel = AmountCalculationModel.UNKNOWN,
            divergentPayment = null,
        )

        val billFailure =
            CreateBillResult.FAILURE.BillNotPayable(
                description = "description",
                code = AddBillError.ALREADY_PAID.code,
                billRegisterData = billRegisterData,
            )

        notificationAdapter.notifyAddFailure(
            members = members,
            source = actionSource,
            from = from,
            subject = subject,
            walletName = walletName,
            billId = billId,
            walletId = walletId,
            result = billFailure,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_postal_box_add_non_payable__4_11_0")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedDueDate(dueDate))
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(actionSource.toSourceName())
                add(from.value)
                add(walletName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyAddFailure para AlreadyPaid WithData`() {
        val billRegisterData = BillRegisterData(
            billType = BillType.CONCESSIONARIA,
            assignor = "assignorTest",
            recipient = Recipient(name = "Claro"),
            recipientChain = null,
            payerDocument = "payerTest",
            amount = 155_91L,
            discount = 0,
            interest = 0,
            fine = 0,
            amountTotal = 155_91L,
            dueDate = dueDate,
            paymentLimitTime = "00:00",
            expirationDate = getLocalDate(),
            settleDate = getLocalDate(),
            amountCalculationModel = AmountCalculationModel.UNKNOWN,
            divergentPayment = null,
        )

        val billFailure =
            CreateBillResult.FAILURE.AlreadyPaid.WithData(
                description = "description",
                billRegisterData = billRegisterData,
            )

        notificationAdapter.notifyAddFailure(
            members = members,
            source = actionSource,
            from = from,
            subject = subject,
            walletName = walletName,
            billId = billId,
            walletId = walletId,
            result = billFailure,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_postal_box_add_paid_externally__4_11_1")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedDueDate(dueDate))
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(actionSource.toSourceName())
                add(from.value)
                add(walletName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyAddFailure para AlreadyPaid WithoutData`() {
        val billFailure =
            CreateBillResult.FAILURE.AlreadyPaid.WithoutData(
                description = "description",
                barCode = barCode,
                dueDate = dueDate,
            )

        notificationAdapter.notifyAddFailure(
            members = members,
            source = actionSource,
            from = from,
            subject = subject,
            walletName = walletName,
            billId = billId,
            walletId = walletId,
            result = billFailure,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_postal_box_add_paid_externally_without_data__4_12_0")
            notification.parameters shouldBe buildList {
                add(NotificationFormatter.buildFormattedDueDate(dueDate))
                add(barCode.formattedDigitable())
                add(actionSource.toSourceName())
                add(from.value)
                add(walletName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyAddFailure para BillUnableToValidate`() {
        val billFailure =
            CreateBillResult.FAILURE.BillUnableToValidate(
                description = "description",
                isRetryable = false,
            )

        val time = getZonedDateTime().withHour(9).withMinute(0).withSecond(0)
        withGivenDateTime(time) {
            notificationAdapter.notifyAddFailure(
                members = members,
                source = actionSource,
                from = from,
                subject = subject,
                walletName = walletName,
                billId = billId,
                walletId = walletId,
                result = billFailure,
            )
        }

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_postal_box_add_validation_failure__4_11_0")
            notification.parameters shouldBe buildList {
                add(from.value)
                add(walletName)
                add(NotificationFormatter.buildFormattedDateTime(time))
                add(NotificationFormatter.sanitize(subject))
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyTransferNotPayable`() {
        val recipientName = billView.recipient!!.name

        notificationAdapter.notifyTransferNotPayable(
            members,
            walletId,
            walletName,
            billId,
            author,
            recipientName,
            dueDate,
            amount,
            errorMessage,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_invoice_payment_failure__4_17_0")
            notification.parameters shouldBe buildList {
                add(recipientName)
                add(NotificationFormatter.buildFormattedDueDate(dueDate))
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(errorMessage)
                add(author.simpleName())
                add(walletName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("contas/carteira/${walletId.value}/bill-timeline-entry@${billId.value}")
        }
    }

    @Test
    fun `enviar notifyBillComingDueLastWarn`() {
        val withoutHint = null
        notificationAdapter.notifyBillComingDueLastWarn(
            members = members,
            wallet = wallet,
            dueDate = dueDate,
            paymentLimitTime = timeLimit,
            bills = listOf(billView),
            hint = withoutHint,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_last_alert_payment_overdue_today_singular__4_19_0")
            notification.parameters shouldBe buildList {
                add(walletName)
                add(NotificationFormatter.buildFormattedTime(timeLimit))
                add(billView.formattedDescription())
                add(billView.amount.toAmountFormat())
                add(withoutHint)
            }.filterNotNull()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("contas/carteira/${walletId.value}")
        }
    }

    @Test
    fun `enviar notifyBillComingDueLastWarn (with Hint)`() {
        notificationAdapter.notifyBillComingDueLastWarn(
            members = members,
            wallet = wallet,
            dueDate = dueDate,
            paymentLimitTime = timeLimit,
            bills = listOf(billView),
            hint = hint,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_last_alert_payment_overdue_today_with_hint_singular__4_12_0")
            notification.parameters shouldBe buildList {
                add(walletName)
                add(NotificationFormatter.buildFormattedTime(timeLimit))
                add(billView.formattedDescription())
                add(billView.amount.toAmountFormat())
                add(hint)
            }.filterNotNull()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("contas/carteira/${walletId.value}")
        }
    }

    @Test
    fun `enviar notifyBillOverdueYesterday`() {
        val withoutHint = null
        notificationAdapter.notifyBillOverdueYesterday(
            members = members,
            walletId = walletId,
            walletName = walletName,
            hint = withoutHint,
        )

        validateChatbotNotificationMultichannel { notification ->
            notification.template shouldBe NotificationTemplate("wallet_payment_overdue_yesterday__4_20_1")
            notification.parameters shouldBe buildList {
                add(walletName)
                add(withoutHint)
            }.filterNotNull()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("contas/carteira/${walletId.value}")
        }
    }

    // TODO: veja todo o commit com esta alteração para entender o motivo de teste ter sido apenas desabilitado
    @Disabled
    @Test
    fun `enviar notifyBillOverdueYesterday (with Hint)`() {
        notificationAdapter.notifyBillOverdueYesterday(
            members = members,
            walletId = walletId,
            walletName = walletName,
            hint = hint,
        )

        validateChatbotNotificationMultichannel { notification ->
            notification.template shouldBe NotificationTemplate("wallet_payment_overdue_yesterday_with_hint__4_12_1")
            notification.parameters shouldBe buildList {
                add(walletName)
                add(hint)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("contas/carteira/${walletId.value}")
        }
    }

    @Test
    fun `enviar notifyUserActivated`() {
        notificationAdapter.notifyUserActivated(account = account)

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("welcome_account_created_without_chatbot__1_0_0")
            notification.parameters shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("entrar?primeiro-acesso")
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
        }
    }

    @Test
    fun `enviar notifyUpgradeCompleted`() {
        notificationAdapter.notifyUpgradeCompleted(account = account)

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("upgrade_completed__1_0_2")
            notification.parameters shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("entrar")
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
        }
    }

    @Test
    fun `enviar notifyRegisterDenied`() {
        notificationAdapter.notifyRegisterDenied(
            accountId = accountId,
            mobilePhone = mobilePhone,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("register_denied__4_26_0")
            notification.parameters shouldBe emptyList()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyRegisterUpgraded`() {
        notificationAdapter.notifyRegisterUpgraded(
            accountId = accountId,
            mobilePhone = mobilePhone,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("register_upgraded__4_26_0")
            notification.parameters shouldBe emptyList()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyWalletMemberJoined`() {
        val founder = member
        val founderFirstName = "Fulano"
        val inviteeFullName = "Brian O'Conner"

        notificationAdapter.notifyWalletMemberJoined(
            founder = founder,
            inviteeFullName = inviteeFullName,
            walletName = walletName,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_member_joined__4_17_0")
            notification.parameters shouldBe buildList {
                add(founderFirstName)
                add(inviteeFullName)
                add(walletName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyUserAuthenticationRequired`() {
        notificationAdapter.notifyUserAuthenticationRequired(accountId = accountId)

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("user_authentication_required__4_12_0")
            notification.parameters shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("?xp=1")
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
        }
    }

    @Test
    fun `enviar notifyRegisterUpdated`() {
        notificationAdapter.notifyRegisterUpdated(
            accountId = accountId,
            mobilePhone = mobilePhone,
            name = wallet.founder.name,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("register__updated__4_12_1")
            notification.parameters shouldBe buildList {
                add(wallet.founder.name)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyInvoicePaymentUndone`() {
        val settleDate = getLocalDate()
        notificationAdapter.notifyInvoicePaymentUndone(
            members = members,
            walletName = walletName,
            author = author,
            recipient = recipient,
            amount = amount,
            settleDate = settleDate,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_invoice_payment_returned__4_17_0")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedDueDate(settleDate))
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(author.simpleName())
                add(walletName)
            }
            notification.buttonWhatsAppParameter!! shouldBe ButtonRawParameter(urlsSite)
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
        }
    }

    @Test
    fun `enviar notifyBoletoReceipt`() {
        every {
            account.isAlphaGroup()
        } returns false

        val receiptData = boleto().copy(
            walletName = walletName,
            scheduledBy = "scheduledBy",
        )

        notificationAdapter.notifyBoletoReceipt(
            members = members,
            receiptData = receiptData,
            receiptFilesData = ReceiptFilesData(imageBytes = ByteArray(0), imageFormat = "png", pdfBytes = ByteArray(0), imageUrl = "imageUrl", pdfUrl = "pdfUrl"),
            mailReceiptHtml = CompiledHtml(""),
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_barcode_bill_receipt__4_17_0")
            notification.parameters shouldBe buildList {
                add(receiptData.recipient!!.name)
                add(NotificationFormatter.buildFormattedDueDate(dueDate))
                add(NotificationFormatter.buildFormattedAmount(receiptData.totalAmount))
                add(receiptData.barcode.formattedDigitable())
                add(receiptData.scheduledBy!!)
                add(walletName)
                add("https://friday.ai")
                add(NotificationFormatter.buildFormattedPaymentDateTime(receiptData.dateTime))
                add(receiptData.authentication)
            }
            notification.buttonWhatsAppParameter shouldBe buildReceiptBillButtonPath(
                walletId = receiptData.walletId,
                billId = receiptData.billId,
            )
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
        }
    }

    @Test
    fun `enviar notifyInvoiceReceipt`() {
        every {
            account.isAlphaGroup()
        } returns false

        val receiptData = invoice().copy(
            payerFinancialInstitution = FinancialInstitution(name = "banco do pagador", ispb = "123456", compe = 234),
            payerBankAccount = internalBankAccount,
            walletName = wallet.name,
            scheduledBy = "scheduledBy",
        )
        notificationAdapter.notifyInvoiceReceipt(
            members = members,
            receiptData = receiptData,
            receiptFilesData = ReceiptFilesData(imageBytes = ByteArray(0), imageFormat = "png", pdfBytes = ByteArray(0), imageUrl = "imageUrl", pdfUrl = "pdfUrl"),
            mailReceiptHtml = CompiledHtml(""),
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_invoice_bill_receipt__4_11_1")
            notification.parameters shouldBe buildList {
                add(NotificationFormatter.buildFormattedAmount(receiptData.totalAmount))
                add(receiptData.payer.name!!)
                add(maskDocumentSpecialAsterisk(receiptData.recipient.document!!))
                add("TED")
                add(receiptData.scheduledBy!!)
                add(walletName)
                add(receiptData.payeeBank)
                add(formatRoutingNumber(receiptData.recipient.bankAccount!!.routingNo.toString()))
                add(
                    formatAccountNumber(
                        receiptData.recipient.bankAccount!!.accountNo,
                        receiptData.recipient.bankAccount!!.accountDv,
                    ),
                )
                add(receiptData.payer.name!!)
                add(NotificationFormatter.getDocumentType(receiptData.payer.document))
                add(maskDocumentSpecialAsterisk(receiptData.payer.document))
                add(receiptData.payerFinancialInstitution!!.name)
                add(formatRoutingNumber(receiptData.payerBankAccount!!.routingNo.toString()))
                add(
                    formatAccountNumber(
                        receiptData.payerBankAccount!!.accountNo.toBigInteger(),
                        receiptData.payerBankAccount!!.accountDv,
                    ),
                )
                add("https://friday.ai") // TODO - trocar para config
                add(NotificationFormatter.buildFormattedPaymentDateTime(receiptData.dateTime))
                add(receiptData.authentication)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("comprovante/${receiptData.walletId.value}/${receiptData.billId.value}")
        }
    }

    @Test
    fun `enviar notifyPixReceipt`() {
        every {
            account.isAlphaGroup()
        } returns false

        val receiptData = pix().copy(
            walletName = walletName,
            scheduledBy = "scheduledBy",
        )

        notificationAdapter.notifyPixReceipt(
            members = members,
            receiptData = receiptData,
            receiptFilesData = ReceiptFilesData(imageBytes = ByteArray(0), imageFormat = "png", pdfBytes = ByteArray(0), imageUrl = "imageUrl", pdfUrl = "pdfUrl"),
            mailReceiptHtml = CompiledHtml(""),
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_pix_bill_receipt__4_11_1")
            notification.parameters shouldBe buildList {
                add(NotificationFormatter.buildFormattedAmount(receiptData.totalAmount))
                add(receiptData.recipient.name)
                add(maskDocumentSpecialAsterisk(receiptData.recipient.document))
                add("PIX")
                add(receiptData.scheduledBy!!)
                add(walletName)
                add(receiptData.payeeFinancialInstitution.name)
                add(formatRoutingNumber(receiptData.payeeRoutingNo.toString()))
                add(formatAccountNumber(receiptData.payeeAccountNo, receiptData.payeeAccountDv))
                add(receiptData.payer.name!!)
                add(maskDocumentSpecialAsterisk(receiptData.payer.document))
                add(formatBankData(receiptData.payerFinancialInstitution))
                add(formatRoutingNumber(receiptData.payerBankAccount.routingNo.toString()))
                add(
                    formatAccountNumber(
                        receiptData.payerBankAccount.accountNo.toBigInteger(),
                        receiptData.payerBankAccount.accountDv,
                    ),
                )
                add(receiptData.authentication)
                add("https://friday.ai")
                add(NotificationFormatter.buildFormattedPaymentDateTime(receiptData.dateTime))
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("comprovante/${receiptData.walletId.value}/${receiptData.billId.value}")
        }
    }

    @Test
    fun `enviar notifyCashIn`() {
        val senderName = "senderName"
        val senderDocument = "***********"
        notificationAdapter.notifyCashIn(
            members = members,
            walletId = walletId,
            walletName = walletName,
            senderName = senderName,
            senderDocument = senderDocument,
            amount = amount,
        )

        validateChatbotNotificationMultichannel { notification ->
            notification.template shouldBe NotificationTemplate("wallet_cash_in_no_payments_pending__4_17_1")
            notification.parameters shouldBe buildList {
                add(walletName)
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(walletName)
                add(
                    String.format(
                        "%s (%s)",
                        senderName,
                        NotificationFormatter.formatDocument(maskDocumentSpecialAsterisk(senderDocument)),
                    ),
                )
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("carteira/${walletId.value}/resumo")
        }
    }

    @Test
    fun `enviar notifyCashInPaymentInsufficientBalanceToday`() {
        val senderName = "senderName"
        val senderDocument = "***********"
        val missingAmountToday = 50_00L
        notificationAdapter.notifyCashInPaymentInsufficientBalanceToday(
            members = members,
            walletId = walletId,
            walletName = walletName,
            pendingAmountToday = missingAmountToday,
            senderName = senderName,
            senderDocument = senderDocument,
            amount = amount,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_cash_in_insufficient_balance__4_17_0")
            notification.parameters shouldBe buildList {
                add(walletName)
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(walletName)
                add(String.format("%s (%s)", senderName, maskDocumentSpecialAsterisk(senderDocument)))
                add(NotificationFormatter.buildFormattedAmount(missingAmountToday))
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("carteira/${walletId.value}/resumo")
        }
    }

    @Test
    fun `enviar notifyCashInPaymentSufficientBalanceToday`() {
        val senderName = "senderName"
        val senderDocument = "***********"
        notificationAdapter.notifyCashInPaymentSufficientBalanceToday(
            members = members,
            walletId = walletId,
            walletName = walletName,
            senderName = senderName,
            senderDocument = senderDocument,
            amount = amount,
        )

        validateChatbotNotificationMultichannel { notification ->
            notification.template shouldBe NotificationTemplate("wallet_cash_in_sufficient_balance__4_17_2")
            notification.parameters shouldBe buildList {
                add(walletName)
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(walletName)
                add(
                    String.format(
                        "%s (%s)",
                        senderName,
                        NotificationFormatter.formatDocument(maskDocumentSpecialAsterisk(senderDocument)),
                    ),
                )
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("carteira/${walletId.value}/resumo")
        }
    }

    @Test
    fun `enviar notifyScheduledBillNotPayable`() {
        notificationAdapter.notifyScheduledBillNotPayable(
            members = members,
            walletId = walletId,
            walletName = walletName,
            author = author,
            payee = payee,
            dueDate = dueDate,
            totalAmount = amount,
            description = description,
            billId = billId,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_barcode_bill_approved_payment_non_payable__4_17_0")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedDueDate(dueDate))
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(author.simpleName())
                add(walletName)
                add(description)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("contas/carteira/${walletId.value}/bill-timeline-entry@${billId.value}")
        }
    }

    @Test
    fun `enviar notifyInsufficientBalanceToday quando já fez cashin (wallet_insufficient_balance_after_hours)`() {
        every {
            systemActivityService.hasCashedIn(walletId)
        } returns true

        val pendingScheduledAmountToday = 1234_01L
        val pendingAmountTotal7Days = 1234_07L
        val pendingAmountTotal15Days = 1234_15L

        notificationAdapter.notifyInsufficientBalanceToday(
            members = members,
            pendingScheduledAmountToday = pendingScheduledAmountToday,
            pendingAmountTotal7Days = pendingAmountTotal7Days,
            pendingAmountTotal15Days = pendingAmountTotal15Days,
            walletId = walletId,
            walletName = walletName,
            isAfterHours = true,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_insufficient_balance_after_hours__4_12_4")
            notification.parameters shouldBe buildList {
                add(NotificationFormatter.buildFormattedAmount(pendingScheduledAmountToday))
                add(NotificationFormatter.buildFormattedAmount(pendingAmountTotal7Days))
                add(NotificationFormatter.buildFormattedAmount(pendingAmountTotal15Days))
                add(walletName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe buildList {
                add("${walletId.value}#${ForecastPeriod.TODAY}")
                add("${walletId.value}#${ForecastPeriod.SEVEN_DAYS}")
                add("${walletId.value}#${ForecastPeriod.FIFTEEN_DAYS}")
            }
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyInsufficientBalanceToday quando já fez cashin (wallet_insufficient_balance)`() {
        every {
            systemActivityService.hasCashedIn(walletId)
        } returns true

        val pendingScheduledAmountToday = 1234_01L
        val pendingAmountTotal7Days = 1234_07L
        val pendingAmountTotal15Days = 1234_15L

        notificationAdapter.notifyInsufficientBalanceToday(
            members = members,
            pendingScheduledAmountToday = pendingScheduledAmountToday,
            pendingAmountTotal7Days = pendingAmountTotal7Days,
            pendingAmountTotal15Days = pendingAmountTotal15Days,
            walletId = walletId,
            walletName = walletName,
            isAfterHours = false,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_insufficient_balance__4_12_3")
            notification.parameters shouldBe buildList {
                add(NotificationFormatter.buildFormattedAmount(pendingScheduledAmountToday))
                add(NotificationFormatter.buildFormattedAmount(pendingAmountTotal7Days))
                add(NotificationFormatter.buildFormattedAmount(pendingAmountTotal15Days))
                add(walletName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe buildList {
                add("${walletId.value}#${ForecastPeriod.TODAY}")
                add("${walletId.value}#${ForecastPeriod.SEVEN_DAYS}")
                add("${walletId.value}#${ForecastPeriod.FIFTEEN_DAYS}")
            }
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyInsufficientBalanceToday quando não fez cashin`() {
        every {
            systemActivityService.hasCashedIn(walletId)
        } returns false

        val pendingScheduledAmountToday = 1234_01L
        val pendingAmountTotal7Days = 1234_07L
        val pendingAmountTotal15Days = 1234_15L

        notificationAdapter.notifyInsufficientBalanceToday(
            members = members,
            pendingScheduledAmountToday = pendingScheduledAmountToday,
            pendingAmountTotal7Days = pendingAmountTotal7Days,
            pendingAmountTotal15Days = pendingAmountTotal15Days,
            walletId = walletId,
            walletName = walletName,
            isAfterHours = true,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("insufficient_balance_first_cash_in__4_12_0")
            notification.parameters shouldBe buildList {
                add(NotificationFormatter.buildFormattedAmount(pendingScheduledAmountToday))
                add(walletName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe buildList {
                add(walletId.value)
            }
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyInsufficientBalanceTodaySecondaryWallet`() {
        val pendingScheduledAmountToday = 1234_01L

        notificationAdapter.notifyInsufficientBalanceTodaySecondaryWallet(
            members = members,
            walletId = walletId,
            walletName = walletName,
            pendingScheduledAmountToday = pendingScheduledAmountToday,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_insufficient_balance_secondary_wallet__1_0_2")
            notification.parameters shouldBe buildList {
                add(NotificationFormatter.buildFormattedAmount(pendingScheduledAmountToday))
                add(walletName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe buildList {
                add("contas/carteira/${walletId.value}")
            }
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyBillSchedulePostponedDueLimitReached`() {
        notificationAdapter.notifyBillSchedulePostponedDueLimitReached(
            members = members,
            walletId = walletId,
            walletName = walletName,
            payee = payee,
            totalAmount = amount,
            type = BillType.PIX,
            author = author,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_bill_schedule_postponed_due_limit_reached__4_11_2")
            notification.parameters shouldBe buildList {
                add(NotificationFormatter.buildFormattedAmount(amount))
                add("PIX")
                add(payee)
                add(walletName)
                add(author.simpleName())
            }
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("carteira/${walletId.value}/limites")
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
        }
    }

    @Test
    fun `enviar notifyBillScheduleCanceledDueAmountHigherThanDailyLimit`() {
        notificationAdapter.notifyBillScheduleCanceledDueAmountHigherThanDailyLimit(
            members = members,
            walletId = walletId,
            walletName = walletName,
            payee = payee,
            totalAmount = amount,
            type = BillType.PIX,
            author = author,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_bill_schedule_canceled_due_amount_higher_than_daily_limit__4_11_4")
            notification.parameters shouldBe buildList {
                add(NotificationFormatter.buildFormattedAmount(amount))
                add("PIX")
                add(payee)
                add(walletName)
                add(author.simpleName())
            }
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("carteira/${walletId.value}/limites")
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
        }
    }

    @Test
    fun `enviar notifyHumanChatWelcome`() {
        notificationAdapter.notifyHumanChatWelcome(
            accountId = accountId,
            mobilePhone = mobilePhone,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("fred_onboarding_welcome_1_0_9")
            notification.parameters shouldBe emptyList()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyTriPixNextDayReminder`() {
        notificationAdapter.notifyTriPixNextDayReminder(
            account = account,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("tri_pix_reminder_next_day__1_0_0")
            notification.parameters shouldBe emptyList()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyTriPixLastDayReminder`() {
        notificationAdapter.notifyTriPixLastDayReminder(
            account = account,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("tri_pix_reminder_last_day__1_0_0")
            notification.parameters shouldBe emptyList()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyTriPixExpired`() {
        notificationAdapter.notifyTriPixExpired(
            account = account,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("tri_pix_expired_1_0_3")
            notification.parameters shouldBe emptyList()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyScheduleBillCanceledDueCreditCardDenied`() {
        notificationAdapter.notifyScheduleBillCanceledDueCreditCardDenied(
            members = members,
            walletId = walletId,
            billId = billId,
            payee = payee,
            amount = amount,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_bill_schedule_canceled_due_credit_card_denied__4_24_1")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedAmount(amount))
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("contas/carteira/${walletId.value}/bill-timeline-entry@${billId.value}")
        }
    }

    @Test
    fun `enviar notifyNotVisibleBillAlreadyExists`() {
        notificationAdapter.notifyNotVisibleBillAlreadyExists(
            member = member,
            walletId = walletId,
            billId = billId,
            payee = payee,
            dueDate = dueDate,
            totalAmount = amount,
            walletName = walletName,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("postal_box_add_not_visible_bill_already_exists__4_12_2")
            notification.parameters shouldBe buildList {
                add(payee)
                add(NotificationFormatter.buildFormattedDueDate(dueDate))
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(walletName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("contas/carteira/${walletId.value}/bill-timeline-entry@${billId.value}")
        }
    }

    @Test
    fun `enviar notifyInvitedMember quando já possui account`() {
        every {
            accountRepository.findAccountByDocumentOrNull(invite.memberDocument)
        } returns mockk() {
            every { accountId } returns AccountId("INVITED_ACCOUNT_ID")
            every { mobilePhone } returns "+*************"
            every {
                hasGroup(any())
            } returns false
            every {
                configuration
            } returns createAccountConfiguration()
            every {
                paymentStatus
            } returns AccountPaymentStatus.UpToDate
        }
        val founderName = invite.founderName.split(" ")[0]
        val memberName = invite.memberName.split(" ")[0]

        notificationAdapter.notifyInvitedMember(
            invite = invite,
            receiver = receiver,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_invite_assistant_with_account__4_13_3")
            notification.parameters shouldBe buildList {
                add(memberName)
                add(founderName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("meus-convites/${walletId.value}")
        }
    }

    @Test
    fun `enviar notifyInvitedMember quando não possui account (cofounder)`() { // TODO - esse template é de email
        every {
            accountRepository.findAccountByDocumentOrNull(invite.memberDocument)
        } returns null

        notificationAdapter.notifyInvitedMember(
            invite = invite,
            receiver = receiver,
        )

        validateEmailNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_invite_collaborator_without_account__4_13_0")
            notification.parameters shouldBe mapOf(
                "founderName" to invite.founderName.split(" ")[0],
                "memberName" to invite.memberName.split(" ")[0],
            )
        }
    }

    @Test
    fun `enviar notifyInvitedMember quando não possui account (assistant)`() { // TODO - esse template é de email
        every {
            accountRepository.findAccountByDocumentOrNull(invite.memberDocument)
        } returns null

        notificationAdapter.notifyInvitedMember(
            invite = invite.copy(memberType = MemberType.ASSISTANT),
            receiver = receiver,
        )

        validateEmailNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_invite_assistant_without_account__4_13_0")
            notification.parameters shouldBe mapOf(
                "founderName" to invite.founderName.split(" ")[0],
                "memberName" to invite.memberName.split(" ")[0],
            )
        }
    }

    @Test
    fun `enviar notifyInviteReminder`() {
        val founderName = invite.founderName.split(" ")[0]
        val memberName = invite.memberName.split(" ")[0]

        notificationAdapter.notifyInviteReminder(
            invite = invite,
            account = walletFixture.cofounderAccount,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_invite_reminder_assistant_with_account__4_13_0")
            notification.parameters shouldBe buildList {
                add(memberName)
                add(founderName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("meus-convites/${walletId.value}")
        }
    }

    @Test
    fun `enviar notifyInviteReminder (cofounder)`() { // TODO: esse cara aqui é um e-mail
        every {
            accountRepository.findAccountByDocumentOrNull(invite.memberDocument)
        } returns null

        val founderName = invite.founderName.split(" ")[0]
        val memberName = invite.memberName.split(" ")[0]

        notificationAdapter.notifyInviteReminder(
            invite = invite,
            account = null,
        )

        validateEmailNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_invite_reminder_collaborator_without_account__4_13_0")
            notification.parameters shouldBe mapOf(
                "founderName" to founderName,
                "memberName" to memberName,
            )
        }
    }

    @Test
    fun `enviar notifyInviteReminder (assistant)`() { // TODO: esse cara aqui é um e-mail
        val founderName = invite.founderName.split(" ")[0]
        val memberName = invite.memberName.split(" ")[0]

        notificationAdapter.notifyInviteReminder(
            invite = invite.copy(memberType = MemberType.ASSISTANT),
            account = null,
        )

        validateEmailNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_invite_reminder_assistant_without_account__4_13_0")
            notification.parameters shouldBe mapOf(
                "founderName" to founderName,
                "memberName" to memberName,
            )
        }
    }

    @Test
    fun `enviar notifyFirstBillScheduled`() {
        notificationAdapter.notifyFirstBillScheduled(
            account = account,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("first_bill_scheduled__4_12_0")
            notification.parameters shouldBe emptyList()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `nao enviar notifyOptOutNotifications`() {
        notificationAdapter.notifyOptOutNotifications(
            account = account,
        )

        verify {
            defaultNotificationService wasNot Called
        }
    }

    @Test
    fun `enviar notifyOnePixPayFailure`() {
        notificationAdapter.notifyOnePixPayFailure(
            accountId = accountId,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("wallet_one_pix_pay_failure__4_17_0")
            notification.parameters shouldBe emptyList()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("entrar")
        }
    }

    @Test
    fun `enviar notifySubscriptionCreated`() {
        notificationAdapter.notifySubscriptionCreated(
            accountId = accountId,
            dueDate = dueDate,
            amount = amount,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("subscription_created__4_17_1")
            notification.parameters shouldBe buildList {
                add(NotificationFormatter.buildFormattedDueDate(dueDate))
                add(NotificationFormatter.buildFormattedAmount(amount))
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifySubscriptionOverdue quando é alternativeVersion`() {
        notificationAdapter.notifySubscriptionOverdue(
            accountId = accountId,
            effectiveDueDate = dueDate,
            amount = amount,
            alternativeVersion = true,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("subscription_overdue_day02__1_00_2")
            notification.parameters shouldBe buildList {
                add(dueDate.format(DateTimeFormatter.ofPattern("dd/MM")))
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe buildList {
                add("""{"payload": "", "action": "SEND_SUBSCRIPTION_PIX_CODE"}""")
            }
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifySubscriptionOverdue quando não é alternativeVersion`() {
        notificationAdapter.notifySubscriptionOverdue(
            accountId = accountId,
            effectiveDueDate = dueDate,
            amount = amount,
            alternativeVersion = false,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("subscription_overdue_day01__1_00_3")
            notification.parameters shouldBe buildList {
                add(dueDate.format(DateTimeFormatter.ofPattern("dd/MM")))
                add(buildFormattedAmount(amount))
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe buildList {
                add("""{"payload": "", "action": "SEND_SUBSCRIPTION_PIX_CODE"}""")
            }
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifySubscriptionOverdueWarningDetailedNotificationsShutdown`() {
        notificationAdapter.notifySubscriptionOverdueWarningDetailedNotificationsShutdown(
            accountId = accountId,
            effectiveDueDate = dueDate,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("subscription_overdue_day04__1_00_0")
            notification.parameters shouldBe buildList {
                add(dueDate.format(DateTimeFormatter.ofPattern("dd/MM")))
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe buildList {
                add("""{"payload": "", "action": "SEND_SUBSCRIPTION_PIX_CODE"}""")
            }
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifySubscriptionOverdueWarningBillNotificationsAndDDAShutdown quando é no futuro`() {
        notificationAdapter.notifySubscriptionOverdueWarningBillNotificationsAndDDAShutdown(
            accountId = accountId,
            accountClosureDate = getLocalDate().plusDays(1),
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("subscription_overdue_day32__1_00_3")
            notification.parameters shouldBe buildList {
                add(getLocalDate().plusDays(1).format(DateTimeFormatter.ofPattern("dd/MM")))
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe buildList {
                add("""{"payload": "", "action": "SEND_SUBSCRIPTION_PIX_CODE"}""")
            }
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifySubscriptionOverdueWarningBillNotificationsAndDDAShutdown quando é hoje`() {
        notificationAdapter.notifySubscriptionOverdueWarningBillNotificationsAndDDAShutdown(
            accountId = accountId,
            accountClosureDate = getLocalDate(),
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("subscription_overdue_day32__1_00_3")
            notification.parameters shouldBe buildList {
                add("o fim do dia de hoje")
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe buildList {
                add("""{"payload": "", "action": "SEND_SUBSCRIPTION_PIX_CODE"}""")
            }
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifySubscriptionOverdueWarningAccountClosure`() {
        val daysUntilClosure: Long = 1
        val closeDate = getLocalDate().plusDays(daysUntilClosure)
        notificationAdapter.notifySubscriptionOverdueWarningAccountClosure(
            accountId = accountId,
            daysUntilClosure = daysUntilClosure,
            closeDate = closeDate,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("subscription_overdue_warning_account_closure__1_0_0")
            notification.parameters shouldBe buildList {
                add(daysUntilClosure.toString())
                add(closeDate.format(DateTimeFormatter.ofPattern("dd/MM")))
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe buildList {
                add("""{"payload": "", "action": "SEND_SUBSCRIPTION_PIX_CODE"}""")
            }
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifySubscriptionOverdueCloseAccount`() {
        notificationAdapter.notifySubscriptionOverdueCloseAccount(
            accountId = accountId,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("subscription_overdue_close_account__1_0_0")
            notification.parameters shouldBe emptyList()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyLegacySubscriptionOverdue`() {
        val days = 2L
        notificationAdapter.notifyLegacySubscriptionOverdue(
            accountId = accountId,
            days = days,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("subscription_overdue__4_18_2")
            notification.parameters shouldBe buildList {
                add(getLocalDate().minusDays(days).format(DateTimeFormatter.ofPattern("dd/MM")))
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyUtilityAccountUpdatedStatus (utility_account_update_status_with_details)`() {
        val utilityAccountLocal = utilityAccount.copy(
            status = UtilityAccountConnectionStatus.INVALID_CREDENTIALS,
            connectionMethod = UtilityConnectionMethod.FLOW,
            statusMessage = "statusMessage",
        )
        notificationAdapter.notifyUtilityAccountUpdatedStatus(
            utilityAccount = utilityAccountLocal,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("utility_account_update_status_with_details__1_0_0")
            notification.parameters shouldBe buildList {
                add(utilityAccountLocal.utility.viewName)
                add(utilityAccountLocal.status.toViewName())
                add(utilityAccountLocal.statusMessage!!)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("carteira/${utilityAccount.walletId.value}/contas-de-consumo")
        }
    }

    @Test
    fun `enviar notifyUtilityAccountUpdatedStatus (utility_account_update_status 2)`() {
        val utilityAccountLocal = utilityAccount.copy(
            status = UtilityAccountConnectionStatus.INVALID_CREDENTIALS,
            connectionMethod = UtilityConnectionMethod.FLOW,
            statusMessage = null,
        )
        notificationAdapter.notifyUtilityAccountUpdatedStatus(
            utilityAccount = utilityAccountLocal,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("utility_account_update_status__4_22_0")
            notification.parameters shouldBe buildList {
                add(utilityAccountLocal.utility.viewName)
                add(utilityAccountLocal.status.toViewName())
                add(utilityAccountLocal.statusMessage)
            }.filterNotNull()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("carteira/${utilityAccount.walletId.value}/contas-de-consumo")
        }
    }

    @Test
    fun `enviar notifyUtilityAccountUpdatedStatus (connected_flow_utility_account)`() {
        val utilityAccountLocal = utilityAccount.copy(
            status = UtilityAccountConnectionStatus.CONNECTED,
            connectionMethod = UtilityConnectionMethod.FLOW,
            statusMessage = null,
        )
        assertThrows<IllegalArgumentException> {
            notificationAdapter.notifyUtilityAccountUpdatedStatus(
                utilityAccount = utilityAccountLocal,
            )
        }

        verify(exactly = 0) {
            defaultNotificationService.sendNotification(
                any(),
                any(),
                any(),
            )
        }
    }

    @Test
    fun `enviar notifyUtilityAccountUpdatedStatus (connected_utility_account)`() {
        val utilityAccountLocal = utilityAccount.copy(
            status = UtilityAccountConnectionStatus.CONNECTED,
            connectionMethod = UtilityConnectionMethod.USER,
            statusMessage = null,
        )
        assertThrows<IllegalArgumentException> {
            notificationAdapter.notifyUtilityAccountUpdatedStatus(
                utilityAccount = utilityAccountLocal,
            )
        }

        verify(exactly = 0) {
            defaultNotificationService.sendNotification(
                any(),
                any(),
                any(),
            )
        }
    }

    @Test
    fun `enviar notifyUtilityAccountUpdatedStatus (utility_account_request_reconnection)`() {
        val utilityAccountLocal = utilityAccount.copy(
            status = UtilityAccountConnectionStatus.REQUIRES_RECONNECTION,
            connectionMethod = UtilityConnectionMethod.USER,
            statusMessage = null,
        )
        notificationAdapter.notifyUtilityAccountUpdatedStatus(
            utilityAccount = utilityAccountLocal,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("utility_account_request_reconnection__1_0_1")
            notification.parameters shouldBe buildList {
                add(utilityAccountLocal.utility.viewName)
                add(utilityAccountLocal.statusMessage)
            }.filterNotNull()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("carteira/${utilityAccount.walletId.value}/contas-de-consumo")
        }
    }

    @Test
    fun `enviar notifyUtilityAccountUpdatedStatus (disconnected_scraping_utility_account)`() {
        val utilityAccountLocal = utilityAccount.copy(
            status = UtilityAccountConnectionStatus.DISCONNECTED,
            connectionMethod = UtilityConnectionMethod.SCRAPING,
            statusMessage = null,
        )
        notificationAdapter.notifyUtilityAccountUpdatedStatus(
            utilityAccount = utilityAccountLocal,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("disconnected_scraping_utility_account__1_0_0")
            notification.parameters shouldBe buildList {
                add(utilityAccountLocal.utility.viewName)
                add(utilityAccountLocal.status.toViewName())
                add(utilityAccountLocal.statusMessage)
            }.filterNotNull()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("carteira/${utilityAccount.walletId.value}/contas-de-consumo")
        }
    }

    @Test
    fun `enviar notifyUtilityAccountUpdatedStatus (utility_account_update_status)`() {
        val utilityAccountLocal = utilityAccount.copy(
            status = UtilityAccountConnectionStatus.DISCONNECTED,
            connectionMethod = UtilityConnectionMethod.FLOW,
            statusMessage = null,
        )
        notificationAdapter.notifyUtilityAccountUpdatedStatus(
            utilityAccount = utilityAccountLocal,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("utility_account_update_status__4_22_0")
            notification.parameters shouldBe buildList {
                add(utilityAccountLocal.utility.viewName)
                add(utilityAccountLocal.status.toViewName())
                add(utilityAccountLocal.statusMessage)
            }.filterNotNull()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("carteira/${utilityAccount.walletId.value}/contas-de-consumo")
        }
    }

    @Test
    fun `enviar notifyUtilityAccountConnected quando encontra bills`() {
        every {
            account.name
        } returns wallet.founder.name

        val utilityAccountLocal = utilityAccount.copy(
            status = UtilityAccountConnectionStatus.CONNECTED,
            connectionMethod = UtilityConnectionMethod.FLOW,
            statusMessage = "statusMessage",
        )
        val firstName = wallet.founder.name.substringBefore(" ").lowercase().replaceFirstChar { c -> c.uppercase() }
        val totalBillsFound = 1
        notificationAdapter.notifyUtilityAccountConnected(
            utilityAccount = utilityAccountLocal,
            info = UtilityAccountUpdateInfo.BillsFound(totalBillsFound, amount),
        )

        // o check não passa porque é uma FridayWhatsappNotification ao inves de WhatsappNotification
        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("connected_flow_utility_account_with_bills__2_3_0")
            notification.parameters shouldBe buildList {
                add(firstName)
                add(utilityAccount.utility.viewName)
                add(totalBillsFound.toString())
                add(formatBrazilCurrency(amount))
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe buildList {
                add("""{"action":"ADD_NEW_CONNECTION","payload":""}""")
            }
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyUtilityAccountConnected para os demais cenários`() {
        every {
            account.name
        } returns wallet.founder.name

        val utilityAccountLocal = utilityAccount.copy(
            status = UtilityAccountConnectionStatus.CONNECTED,
            connectionMethod = UtilityConnectionMethod.FLOW,
            statusMessage = "statusMessage",
        )
        val firstName = wallet.founder.name.substringBefore(" ").lowercase().replaceFirstChar { c -> c.uppercase() }
        notificationAdapter.notifyUtilityAccountConnected(
            utilityAccount = utilityAccountLocal,
            info = null,
        )

        // o check não passa porque é uma FridayWhatsappNotification ao inves de WhatsappNotification
        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("connected_flow_utility_account__2_3_0")
            notification.parameters shouldBe buildList {
                add(firstName)
                add(utilityAccount.utility.viewName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe buildList {
                add("""{"action":"ADD_NEW_CONNECTION","payload":""}""")
            }
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyUtilityAccountRequestReconnection`() {
        notificationAdapter.notifyUtilityAccountRequestReconnection(utilityAccount = utilityAccount)

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("utility_account_request_reconnection__1_0_1")
            notification.parameters shouldBe buildList {
                add(utilityAccount.utility.viewName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("carteira/${utilityAccount.walletId.value}/contas-de-consumo/reconectar-conta/${utilityAccount.id.value}")
        }
    }

    @Test
    fun `enviar notifyDisconnectLegacyUtilityAccount`() {
        notificationAdapter.notifyDisconnectLegacyUtilityAccount(utilityAccount = utilityAccount)

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("utility_account_disconnect_legacy_account__1_0_2")
            notification.parameters shouldBe buildList {
                add(utilityAccount.utility.viewName)
                add(utilityAccount.utility.viewName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter(
                "carteira/${utilityAccount.walletId.value}/contas-de-consumo/conectar-conta/${
                utilityAccount.utility.name.lowercase().replace("_", "-")
                }",
            )
        }
    }

    @Test
    fun `enviar notifyInvoicesNotFoundUtilityAccount`() {
        notificationAdapter.notifyInvoicesNotFoundUtilityAccount(utilityAccount = utilityAccount)

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("utility_account_invoice_not_found__1_0_0")
            notification.parameters shouldBe buildList {
                add(utilityAccount.utility.viewName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyInvoicesScanErrorUtilityAccount`() {
        notificationAdapter.notifyInvoicesScanErrorUtilityAccount(utilityAccount = utilityAccount)

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("utility_account_invoice_scan_error__1_0_0")
            notification.parameters shouldBe buildList {
                add(utilityAccount.utility.viewName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyCreditCardEnabled`() {
        val quota = 0L

        notificationAdapter.notifyCreditCardEnabled(
            accountId = accountId,
            quota = quota,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("credit_card_enabled__4_20_0")
            notification.parameters shouldBe buildList {
                add(NotificationFormatter.buildFormattedAmount(quota))
                add("por mês")
                add("0%")
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifySubscriptionInsufficientBalance`() {
        notificationAdapter.notifySubscriptionInsufficientBalance(
            members = members,
            walletId = walletId,
            walletName = walletName,
            amount = amount,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("subscription_insufficient_balance__4_17_0")
            notification.parameters shouldBe buildList {
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(walletName)
            }
            notification.quickReplyButtonsWhatsAppParameter shouldBe buildList {
                add("${walletId.value}#${ForecastPeriod.TODAY}")
                add("${walletId.value}#${ForecastPeriod.SEVEN_DAYS}")
                add("${walletId.value}#${ForecastPeriod.FIFTEEN_DAYS}")
            }
            notification.buttonWhatsAppParameter shouldBe null
        }
    }

    @Test
    fun `enviar notifyPixNotReceivedFailure`() {
        notificationAdapter.notifyPixNotReceivedFailure(
            accountId = accountId,
            wallet = wallet,
            amount = amount,
            senderName = wallet.founder.name,
            senderDocument = wallet.founder.document,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("pix_not_received_failure__4_21_0")
            notification.parameters shouldBe buildList {
                add(walletName)
                add(NotificationFormatter.buildFormattedAmount(amount))
                add(walletName)
                add(String.format("%s (%s)", wallet.founder.name, maskDocumentSpecialAsterisk(wallet.founder.document)))
            }.filterNotNull()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("carteira/${walletId.value}/resumo")
        }
    }

    @Test
    fun `enviar notifyBasicSignUpReopened`() {
        notificationAdapter.notifyBasicSignUpReopened(
            accountId = accountId,
            mobilePhone = mobilePhone,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("sign_up_basic_update_data_needed__4_18_0")
            notification.parameters shouldBe emptyList()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter shouldBe ButtonDeeplinkParameter("entrar")
        }
    }

    @Test
    fun `enviar notifyPaymentIntentFailed`() {
        val paymentId = PaymentIntentId()
        notificationAdapter.notifyPaymentIntentFailed(
            accountId = accountId,
            paymentIntentId = paymentId,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("itp_transaction_failed__4_17_1")
            notification.parameters shouldBe emptyList()
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter!! shouldBe ButtonWebParameter("adicionar-saldo/${paymentId.value}")
        }
    }

    @Test
    fun `enviar notifyToken`() {
        val token = "token"
        notificationAdapter.notifyToken(
            accountId = accountId,
            mobilePhone = mobilePhone,
            token = token,
        )

        validateChatbotNotificationSent { notification ->
            notification.template shouldBe NotificationTemplate("register_token__4_14_0")
            notification.parameters shouldBe listOf(token)
            notification.quickReplyButtonsWhatsAppParameter shouldBe emptyList()
            notification.buttonWhatsAppParameter!!.value shouldBe token
        }
    }
}