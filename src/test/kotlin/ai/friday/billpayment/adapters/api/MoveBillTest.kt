package ai.friday.billpayment.adapters.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillRecurrenceDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillRecurrenceDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DDABillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DDADbRepository
import ai.friday.billpayment.adapters.dynamodb.DDADynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.MoveBillErrorReason
import ai.friday.billpayment.app.bill.RegisterUpdated
import ai.friday.billpayment.app.bill.UpdatedRegisterData
import ai.friday.billpayment.app.bill.billLockProvider
import ai.friday.billpayment.app.dda.DDABill
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.recurrence.RecurrenceStatus
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billIgnored
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.billRegisterUpdatedAlreadyPaid
import ai.friday.billpayment.billRegisterUpdatedNotPayable
import ai.friday.billpayment.fichaCompensacaoCreditCardAdded
import ai.friday.billpayment.integration.BILL_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.createLockTable
import ai.friday.billpayment.integration.generateCookie
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.pixKeyAdded
import ai.friday.billpayment.pixPaymentScheduled
import ai.friday.billpayment.weeklyRecurrenceNoEndDate
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.mockk
import jakarta.inject.Named
import java.util.UUID
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

@MicronautTest(environments = [FRIDAY_ENV])
class MoveBillTest(
    embeddedServer: EmbeddedServer,
    val dynamoDB: AmazonDynamoDB,
    @Named(billLockProvider) val lockProvider: InternalLock,
) {

    @field:Property(name = "lock.tableName")
    lateinit var lockTableName: String

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    val enhancedClient = getDynamoDB()
    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = mockk(),
    )

    private val billEventDAO = BillEventDynamoDAO(enhancedClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)
    private val billEventRepository = BillEventDBRepository(
        billEventDAO = billEventDAO,
        uniqueConstraintDAO = uniqueConstraintDAO,
        featureConfiguration = allFalseFeatureConfiguration,
        transactionDynamo = transactionDynamo,
    )
    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(enhancedClient),
        refundedClient = RefundedBillDynamoDAO(enhancedClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(enhancedClient),
    )
    private val dynamoDbEnhancedClient = enhancedClient
    private val ddaRepository = DDADbRepository(
        ddaDAO = DDADynamoDAO(dynamoDbEnhancedClient),
        ddaBillDAO = DDABillDynamoDAO(dynamoDbEnhancedClient),
    )
    private val billRecurrenceDynamoDAO = BillRecurrenceDynamoDAO(dynamoDbEnhancedClient)
    private val recurrenceRepository =
        BillRecurrenceDBRepository(billRecurrenceDynamoDAO, "2021-12-31") // TODO pegar do application

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        createLockTable(dynamoDB, lockTableName)
        loadAccountIntoDb(dynamoDB, accountId = walletFixture.founderAccount.accountId)
        walletRepository.save(originWallet)
        walletRepository.save(destinationWallet)
    }

    private fun buildRequest(
        cookie: Cookie,
        originWalletId: WalletId,
        destinationWalletId: WalletId,
        vararg billId: BillId,
    ): MutableHttpRequest<*> {
        val to = MoveBillsTO(billId.map { it.value })
        return HttpRequest.POST("/wallet/${originWalletId.value}/moveBillsTo/${destinationWalletId.value}", to)
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    @Test
    fun `should return bad request on duplicated billIds`() {
        val to = DuplicatedMoveBillsTO(listOf(BILL_ID, BILL_ID))

        val request = HttpRequest.POST("/wallet/${originWallet.id.value}/moveBillsTo/${destinationWallet.id.value}", to)
            .cookie(generateCookie(walletFixture.founderAccount.accountId, Role.OWNER))
            .header("X-API-VERSION", "2")

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(MoveBillsResultTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    @Test
    fun `should return not found when moving bill from nonexisting wallet`() {
        val request = buildRequest(
            generateCookie(walletFixture.founderAccount.accountId, Role.OWNER),
            WalletId("NOT_FOUND"),
            destinationWallet.id,
            BillId(BILL_ID),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(MoveBillsResultTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
    }

    @Test
    fun `should return not found when moving bill to nonexisting wallet`() {
        val request = buildRequest(
            generateCookie(walletFixture.founderAccount.accountId, Role.OWNER),
            originWallet.id,
            WalletId("NOT_FOUND"),
            BillId(BILL_ID),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(MoveBillsResultTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
    }

    @Test
    fun `should return forbidden when user is not a member on origin wallet`() {
        val request = buildRequest(
            generateCookie(walletFixture.cantPayParticipantAccount.accountId, Role.OWNER),
            originWallet.id,
            destinationWallet.id,
            BillId(BILL_ID),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(MoveBillsResultTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `should return forbidden when user is not a member on destination wallet`() {
        val request = buildRequest(
            generateCookie(walletFixture.participantAccount.accountId, Role.OWNER),
            originWallet.id,
            destinationWallet.id,
            BillId(BILL_ID),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(MoveBillsResultTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `should return BILL_NOT_FOUND when bill does not exist on origin wallet`() {
        val billId = BillId(BILL_ID)

        val request = buildRequest(
            generateCookie(walletFixture.founderAccount.accountId, Role.OWNER),
            originWallet.id,
            destinationWallet.id,
            billId,
        )

        val response = client.toBlocking()
            .exchange(request, Argument.of(MoveBillsResultTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get().billList.first()) {
            this.billId shouldBe billId.value
            this.moved shouldBe false
            this.reason shouldBe MoveBillErrorReason.BILL_NOT_FOUND
        }
    }

    @Test
    fun `should return USER_NOT_ALLOWED when user does not have permission to view bill on origin wallet`() {
        billEventRepository.save(billAdded.copy(walletId = originWallet.id))

        val request = buildRequest(
            generateCookie(walletFixture.ultraLimitedParticipant.accountId, Role.OWNER),
            originWallet.id,
            destinationWallet.id,
            billAdded.billId,
        )

        val response = client.toBlocking()
            .exchange(request, Argument.of(MoveBillsResultTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get().billList.first()) {
            this.billId shouldBe billAdded.billId.value
            this.moved shouldBe false
            this.reason shouldBe MoveBillErrorReason.USER_NOT_ALLOWED
        }
    }

    @Test
    fun `should return INVALID_STATUS when bills are scheduled`() {
        billEventRepository.save(billAdded.copy(walletId = originWallet.id))
        billEventRepository.save(billPaymentScheduled.copy(walletId = originWallet.id))

        billEventRepository.save(pixAdded.copy(walletId = originWallet.id))
        billEventRepository.save(pixPaymentScheduled.copy(walletId = originWallet.id))

        val request = buildRequest(
            generateCookie(walletFixture.founderAccount.accountId, Role.OWNER),
            originWallet.id,
            destinationWallet.id,
            billAdded.billId,
            pixAdded.billId,
        )

        val response = client.toBlocking()
            .exchange(request, Argument.of(MoveBillsResultTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        response.body.get().billList shouldContainExactlyInAnyOrder listOf(
            MoveBillResultTO(billAdded.billId.value, false, MoveBillErrorReason.INVALID_STATUS),
            MoveBillResultTO(pixAdded.billId.value, false, MoveBillErrorReason.INVALID_STATUS),
        )
    }

    @ParameterizedTest
    @MethodSource("notActiveEvents")
    fun `should return INVALID_STATUS when bill is not active`(billEvent: BillEvent) {
        billEventRepository.save(billAdded.copy(walletId = originWallet.id))
        billEventRepository.save(billEvent)

        val request = buildRequest(
            generateCookie(walletFixture.founderAccount.accountId, Role.OWNER),
            originWallet.id,
            destinationWallet.id,
            billAdded.billId,
        )

        val response = client.toBlocking()
            .exchange(request, Argument.of(MoveBillsResultTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get().billList.first()) {
            this.billId shouldBe billAdded.billId.value
            this.moved shouldBe false
            this.reason shouldBe MoveBillErrorReason.INVALID_STATUS
        }
    }

    @Test
    fun `should return BILL_ALREADY_EXISTS when theres is a bill with same barcode and due date on destination wallet`() {
        billEventRepository.save(billAdded.copy(walletId = originWallet.id))
        billEventRepository.save(
            billAdded.copy(
                walletId = destinationWallet.id,
                billId = BillId("BILL-${UUID.randomUUID()}"),
            ),
        )

        val request = buildRequest(
            generateCookie(walletFixture.founderAccount.accountId, Role.OWNER),
            originWallet.id,
            destinationWallet.id,
            billAdded.billId,
        )

        val response = client.toBlocking()
            .exchange(request, Argument.of(MoveBillsResultTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get().billList.first()) {
            this.billId shouldBe billAdded.billId.value
            this.moved shouldBe false
            this.reason shouldBe MoveBillErrorReason.BILL_ALREADY_EXISTS
        }
    }

    @Test
    fun `should return BILL_ALREADY_EXISTS when theres is a bill with same barcode and same due date on destination wallet`() {
        billEventRepository.save(billAdded.copy(walletId = originWallet.id))
        billEventRepository.save(
            billAdded.copy(
                walletId = destinationWallet.id,
                billId = BillId("BILL-${UUID.randomUUID()}"),
                dueDate = billAdded.dueDate,
            ),
        )

        val request = buildRequest(
            generateCookie(walletFixture.founderAccount.accountId, Role.OWNER),
            originWallet.id,
            destinationWallet.id,
            billAdded.billId,
        )

        val response = client.toBlocking()
            .exchange(request, Argument.of(MoveBillsResultTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get().billList.first()) {
            this.billId shouldBe billAdded.billId.value
            this.moved shouldBe false
            this.reason shouldBe MoveBillErrorReason.BILL_ALREADY_EXISTS
        }
    }

    @Test
    fun `should move bill when there is a bill with same barcode and another due date on destination wallet`() {
        billEventRepository.save(billAdded.copy(walletId = originWallet.id))
        billEventRepository.save(
            billAdded.copy(
                walletId = destinationWallet.id,
                billId = BillId("BILL-${UUID.randomUUID()}"),
                dueDate = billAdded.dueDate.plusDays(1),
            ),
        )

        val request = buildRequest(
            generateCookie(walletFixture.founderAccount.accountId, Role.OWNER),
            originWallet.id,
            destinationWallet.id,
            billAdded.billId,
        )

        val response = client.toBlocking()
            .exchange(request, Argument.of(MoveBillsResultTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get().billList.first()) {
            this.billId shouldBe billAdded.billId.value
            this.moved shouldBe true
            this.reason shouldBe null
        }
    }

    @Test
    fun `should return BILL_ALREADY_LOCKED when bill is locked`() {
        val billId = BillId(BILL_ID)

        val lock = lockProvider.acquireLock(billId.value)

        val request = buildRequest(
            generateCookie(walletFixture.founderAccount.accountId, Role.OWNER),
            originWallet.id,
            destinationWallet.id,
            billId,
        )

        val response = client.toBlocking()
            .exchange(request, Argument.of(MoveBillsResultTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get().billList.first()) {
            this.billId shouldBe billId.value
            this.moved shouldBe false
            this.reason shouldBe MoveBillErrorReason.BILL_ALREADY_LOCKED
        }

        lock!!.unlock()
    }

    @ParameterizedTest
    @MethodSource("billAddedInvoice")
    fun `should move bill to destination wallet`(billAdded: BillAdded) {
        billEventRepository.save(billAdded.copy(walletId = originWallet.id))
        val request = buildRequest(
            generateCookie(walletFixture.founderAccount.accountId, Role.OWNER),
            originWallet.id,
            destinationWallet.id,
            billAdded.billId,
        )
        val response = client.toBlocking()
            .exchange(request, Argument.of(MoveBillsResultTO::class.java), Argument.of(ResponseTO::class.java))
        response.status shouldBe HttpStatus.OK
        with(response.body.get().billList.first()) {
            this.billId shouldBe billAdded.billId.value
            this.moved shouldBe true
            this.reason shouldBe null
        }
        val originalBill = billEventRepository.getBillById(billAdded.billId)
        originalBill.isRight() shouldBe true
        originalBill.map {
            it.status shouldBe BillStatus.MOVED
        }

        val movedBill = billRepository.findByWallet(destinationWallet.id).single()
        with(movedBill) {
            status shouldBe BillStatus.ACTIVE
            amountTotal shouldBe billAdded.amountTotal
            recipient shouldBe billAdded.recipient
            billDescription shouldBe billAdded.description
            dueDate shouldBe billAdded.dueDate
            source shouldBe ActionSource.Api(walletFixture.founderAccount.accountId, billAdded.billId)
        }
    }

    @Test
    fun `should move recurrent bill to destination wallet`() {
        val recurrence =
            weeklyRecurrenceNoEndDate.plusBillId(pixAdded.billId).copy(walletId = originWallet.id)
        val event = pixAdded.copy(
            recurrenceRule = recurrence.rule,
            actionSource = ActionSource.WalletRecurrence(originWallet.founder.accountId, recurrence.id),
        )
        recurrenceRepository.save(recurrence)
        billEventRepository.save(event.copy(walletId = originWallet.id))
        val request = buildRequest(
            generateCookie(walletFixture.founderAccount.accountId, Role.OWNER),
            originWallet.id,
            destinationWallet.id,
            event.billId,
        )
        val response = client.toBlocking()
            .exchange(request, Argument.of(MoveBillsResultTO::class.java), Argument.of(ResponseTO::class.java))
        response.status shouldBe HttpStatus.OK
        with(response.body.get().billList.first()) {
            this.billId shouldBe event.billId.value
            this.moved shouldBe true
            this.reason shouldBe null
        }
        val originalBill = billEventRepository.getBillById(event.billId)
        originalBill.isRight() shouldBe true
        originalBill.map {
            it.status shouldBe BillStatus.MOVED
            it.recurrenceRule shouldBe recurrence.rule
        }

        val movedBill = billRepository.findByWallet(destinationWallet.id).single()
        with(movedBill) {
            status shouldBe BillStatus.ACTIVE
            amountTotal shouldBe event.amountTotal
            recipient shouldBe event.recipient
            billDescription shouldBe event.description
            dueDate shouldBe event.dueDate
            recurrenceRule shouldBe null
            source shouldBe ActionSource.Api(walletFixture.founderAccount.accountId, event.billId)
        }

        with(recurrenceRepository.find(recurrence.id, originWallet.id)) {
            bills.size shouldBe 0
            status shouldBe RecurrenceStatus.IGNORED
        }
    }

    @ParameterizedTest
    @MethodSource("boletos")
    fun `should move boleto to destination wallet`(events: MutableList<BillEvent>) {
        val bill = Bill.build(events)
        events.forEach { billEventRepository.save(it) }

        val request = buildRequest(
            generateCookie(walletFixture.founderAccount.accountId, Role.OWNER),
            originWallet.id,
            destinationWallet.id,
            bill.billId,
        )
        ddaRepository.save(
            DDABill(
                barcode = bill.barcode!!,
                document = bill.payer!!.document,
                dueDate = bill.dueDate,
                walletId = bill.walletId,
                billId = bill.billId,
                lastStatus = BillStatus.ACTIVE,
            ),
        )

        val response = client.toBlocking()
            .exchange(request, Argument.of(MoveBillsResultTO::class.java), Argument.of(ResponseTO::class.java))
        response.status shouldBe HttpStatus.OK
        with(response.body.get().billList.first()) {
            this.billId shouldBe bill.billId.value
            this.moved shouldBe true
            this.reason shouldBe null
        }
        val originalBill = billEventRepository.getBillById(bill.billId)
        originalBill.isRight() shouldBe true
        originalBill.map {
            it.status shouldBe BillStatus.MOVED
        }

        val movedBill = billRepository.findByWallet(destinationWallet.id).single()
        with(movedBill) {
            status shouldBe BillStatus.ACTIVE
            amount shouldBe bill.amount
            fine shouldBe bill.fine
            interest shouldBe bill.interest
            discount shouldBe bill.discount
            amountTotal shouldBe bill.amountTotal
            recipient shouldBe bill.recipient
            billDescription shouldBe bill.description
            dueDate shouldBe bill.dueDate
            barCode shouldBe bill.barcode
            assignor shouldBe bill.assignor
            source shouldBe ActionSource.Api(walletFixture.founderAccount.accountId, bill.billId)
        }

        var result = billEventRepository.findLastBill(bill.barcode!!, originWallet.id)
        result.isLeft() shouldBe true
        result.mapLeft {
            it.shouldBeTypeOf<ItemNotFoundException>()
        }
        result = billEventRepository.findLastBill(bill.barcode!!, destinationWallet.id)
        result.isRight() shouldBe true
        result.map {
            it.billId shouldBe movedBill.billId
        }

        val ddaBill =
            ddaRepository.find(barCode = bill.barcode!!, document = bill.payer!!.document, dueDate = bill.dueDate)
        ddaBill!!.billId shouldBe movedBill.billId
        ddaBill.walletId shouldBe destinationWallet.id
    }

    @Test
    fun `should move boleto to destination wallet when boleto has idNumber`() {
        val events = mutableListOf(fichaCompensacaoAdded.copy(idNumber = "123456"), registerUpdated)

        val bill = Bill.build(events)
        events.forEach { billEventRepository.save(it) }

        val request = buildRequest(
            generateCookie(walletFixture.founderAccount.accountId, Role.OWNER),
            originWallet.id,
            destinationWallet.id,
            bill.billId,
        )
        ddaRepository.save(
            DDABill(
                barcode = bill.barcode!!,
                document = bill.payer!!.document,
                dueDate = bill.dueDate,
                walletId = bill.walletId,
                billId = bill.billId,
                lastStatus = BillStatus.ACTIVE,
            ),
        )

        val response = client.toBlocking()
            .exchange(request, Argument.of(MoveBillsResultTO::class.java), Argument.of(ResponseTO::class.java))
        response.status shouldBe HttpStatus.OK
        with(response.body.get().billList.first()) {
            this.billId shouldBe bill.billId.value
            this.moved shouldBe true
            this.reason shouldBe null
        }
        val originalBill = billEventRepository.getBillById(bill.billId)
        originalBill.isRight() shouldBe true
        originalBill.map {
            it.status shouldBe BillStatus.MOVED
        }

        val movedBill = billRepository.findByWallet(destinationWallet.id).single()
        with(movedBill) {
            status shouldBe BillStatus.ACTIVE
            amount shouldBe bill.amount
            fine shouldBe bill.fine
            interest shouldBe bill.interest
            discount shouldBe bill.discount
            amountTotal shouldBe bill.amountTotal
            recipient shouldBe bill.recipient
            billDescription shouldBe bill.description
            dueDate shouldBe bill.dueDate
            barCode shouldBe bill.barcode
            assignor shouldBe bill.assignor
            source shouldBe ActionSource.Api(walletFixture.founderAccount.accountId, bill.billId)
        }

        billEventRepository.findLastBill(bill.idNumber!!, originWallet.id).isLeft() shouldBe true
        val result = billEventRepository.findLastBill(bill.idNumber!!, destinationWallet.id)
        result.isRight() shouldBe true
        result.map {
            it.billId shouldBe movedBill.billId
        }

        val ddaBill =
            ddaRepository.find(barCode = bill.barcode!!, document = bill.payer!!.document, dueDate = bill.dueDate)
        ddaBill!!.billId shouldBe movedBill.billId
        ddaBill.walletId shouldBe destinationWallet.id
    }

    @Test
    fun `should move concessionaria when theres is an ignored bill with same barcode and same due date on destination wallet`() {
        billEventRepository.save(billAdded.copy(walletId = originWallet.id))
        val ignoredBillId = BillId("BILL-${UUID.randomUUID()}")
        val oldBillAdded = billAdded.copy(
            walletId = destinationWallet.id,
            billId = ignoredBillId,
        )
        val oldBillIgnored = billIgnored.copy(walletId = destinationWallet.id, billId = ignoredBillId)
        billEventRepository.save(oldBillAdded)
        billEventRepository.save(oldBillIgnored)
        billRepository.save(Bill.build(oldBillAdded, oldBillIgnored))

        val request = buildRequest(
            generateCookie(walletFixture.founderAccount.accountId, Role.OWNER),
            originWallet.id,
            destinationWallet.id,
            billAdded.billId,
        )

        val response = client.toBlocking()
            .exchange(request, Argument.of(MoveBillsResultTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get().billList.first()) {
            this.billId shouldBe billAdded.billId.value
            this.moved shouldBe true
            this.reason shouldBe null
        }

        val originalBill = billEventRepository.getBillById(billAdded.billId)
        originalBill.isRight() shouldBe true
        originalBill.map {
            it.status shouldBe BillStatus.MOVED
        }

        val movedBill = billRepository.findByWallet(destinationWallet.id).single()
        with(movedBill) {
            barCode shouldBe billAdded.barcode
            status shouldBe BillStatus.ACTIVE
            amountTotal shouldBe billAdded.amountTotal
            recipient shouldBe billAdded.recipient
            billDescription shouldBe billAdded.description
            dueDate shouldBe billAdded.dueDate
            source shouldBe ActionSource.Api(walletFixture.founderAccount.accountId, billAdded.billId)
        }
    }

    @ParameterizedTest
    @MethodSource("commonWalletMembers")
    fun `should move all bills to destination wallet`(member: Member) {
        val billEvents = listOf(pixAdded, pixKeyAdded, invoiceAdded)

        billEvents.forEach {
            billEventRepository.save(it.copy(walletId = originWallet.id))
        }

        val request = buildRequest(
            generateCookie(member.accountId, Role.OWNER),
            originWallet.id,
            destinationWallet.id,
            *billEvents.map { it.billId }.toTypedArray(),
        )

        val response = client.toBlocking()
            .exchange(request, Argument.of(MoveBillsResultTO::class.java), Argument.of(ResponseTO::class.java))
        response.status shouldBe HttpStatus.OK
        response.body.get().billList shouldContainExactlyInAnyOrder billEvents.map {
            MoveBillResultTO(
                it.billId.value,
                true,
                null,
            )
        }

        val movedBills = billRepository.findByWallet(destinationWallet.id)

        billEvents.forEach { billAdded ->
            val originalBill = billEventRepository.getBillById(billAdded.billId)
            originalBill.isRight() shouldBe true
            originalBill.map {
                it.status shouldBe BillStatus.MOVED
            }

            val movedBill = movedBills.single { (it.source as ActionSource.Api).originalBillId == billAdded.billId }
            with(movedBill) {
                status shouldBe BillStatus.ACTIVE
                amountTotal shouldBe billAdded.amountTotal
                recipient shouldBe billAdded.recipient
                billDescription shouldBe billAdded.description
                dueDate shouldBe billAdded.dueDate
                source shouldBe ActionSource.Api(member.accountId, billAdded.billId)
            }
        }
    }

    data class DuplicatedMoveBillsTO(val billList: List<String>)

    companion object {
        private val walletFixture = WalletFixture()
        private val originWallet = walletFixture.buildWallet(
            "carteira origem",
            otherMembers = listOf(
                walletFixture.assistant,
                walletFixture.limitedParticipant,
                walletFixture.ultraLimitedParticipant,
                walletFixture.participant,
            ),
        )
        private val destinationWallet = walletFixture.buildWallet(
            "carteira destino",
            id = WalletId("WALLET-${UUID.randomUUID()}"),
            otherMembers = listOf(
                walletFixture.assistant,
                walletFixture.limitedParticipant,
                walletFixture.ultraLimitedParticipant,
            ),
        )

        @JvmStatic
        fun notActiveEvents(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(billPaymentStart.copy(walletId = originWallet.id)),
                Arguments.arguments(billPaid.copy(walletId = originWallet.id)),
                Arguments.arguments(billRegisterUpdatedNotPayable.copy(walletId = originWallet.id)),
                Arguments.arguments(billRegisterUpdatedAlreadyPaid.copy(walletId = originWallet.id)),
                Arguments.arguments(billIgnored.copy(walletId = originWallet.id)),
            )
        }

        @JvmStatic
        fun billAddedInvoice(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(pixAdded),
                Arguments.arguments(pixKeyAdded),
                Arguments.arguments(invoiceAdded),
            )
        }

        private val fichaCompensacaoAdded = fichaCompensacaoCreditCardAdded.copy(
            walletId = originWallet.id,
            actionSource = ActionSource.DDA(accountId = walletFixture.founderAccount.accountId),
        )
        private val registerUpdated = RegisterUpdated(
            billId = fichaCompensacaoAdded.billId,
            walletId = fichaCompensacaoAdded.walletId,
            updatedRegisterData = UpdatedRegisterData.NewTotalAmount(
                amount = fichaCompensacaoAdded.amount,
                abatement = 0,
                discount = 0,
                interest = 100,
                fine = 200,
                amountTotal = fichaCompensacaoAdded.amount + 300,
                lastSettleDate = "2020-05-09",
                registrationUpdateNumber = null,
                source = null,
            ),
        )

        @JvmStatic
        fun boletos(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    listOf(
                        billAdded.copy(
                            walletId = originWallet.id,
                            actionSource = ActionSource.DDA(accountId = walletFixture.founderAccount.accountId),
                        ),
                    ),
                ),
                Arguments.arguments(listOf(fichaCompensacaoAdded, registerUpdated)),
            )
        }

        @JvmStatic
        fun commonWalletMembers(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(walletFixture.founder),
                Arguments.arguments(walletFixture.assistant),
                Arguments.arguments(walletFixture.limitedParticipant),
            )
        }
    }
}