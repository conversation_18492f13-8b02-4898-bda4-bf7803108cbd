package ai.friday.billpayment.adapters.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.adapters.lock.walletDailyPaymentLimitLockProvider
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.DailyPaymentLimitType
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.createLockTable
import ai.friday.billpayment.integration.generateCookie
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.core.type.Argument.VOID_OBJECT
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import jakarta.inject.Named
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
class PutWalletDailyPaymentLimitTest(
    embeddedServer: EmbeddedServer,
    private val dynamoDB: AmazonDynamoDB,
    @Named(walletDailyPaymentLimitLockProvider) private val internalLock: InternalLock,
) {
    @field:Property(name = "lock.tableName")
    lateinit var lockTableName: String

    private val enhancedClient = getDynamoDB()

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = accountRepository,
    )

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private fun buildUpdateDailyPaymentLimitRequest(
        cookie: Cookie,
        walletId: WalletId,
        dailyPaymentLimitRequestTO: DailyPaymentLimitRequestTO,
    ): MutableHttpRequest<DailyPaymentLimitRequestTO> {
        return HttpRequest.PUT("/wallet/${walletId.value}/limit", dailyPaymentLimitRequestTO)
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    private val walletFixture = WalletFixture()
    private val primaryWallet = walletFixture.buildWallet(walletFounder = walletFixture.founder.copy(document = "***********"))
    private val primaryWalletId = primaryWallet.id
    private val accountId = primaryWallet.founder.accountId

    private val cookie: Cookie = generateCookie(accountId, Role.OWNER)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createLockTable(dynamoDB, lockTableName)
        walletRepository.save(primaryWallet)

        loadAccountIntoDb(dynamoDB, accountId = accountId, defaultWalletId = primaryWalletId)
        loadAccountIntoDb(dynamoDB, accountId = AccountId(ACCOUNT_ID), defaultWalletId = primaryWalletId)
    }

    @Test
    fun `should return BAD_REQUEST when amount value is invalid`() {
        val request = buildUpdateDailyPaymentLimitRequest(
            cookie,
            primaryWalletId,
            DailyPaymentLimitRequestTO(amount = -1, type = DailyPaymentLimitType.DAILY),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, VOID_OBJECT, Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    @Test
    fun `should return BAD_REQUEST when amount value is above than 100k`() {
        val request = buildUpdateDailyPaymentLimitRequest(
            cookie,
            primaryWalletId,
            DailyPaymentLimitRequestTO(amount = 100_000_01, type = DailyPaymentLimitType.DAILY),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, VOID_OBJECT, Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    @Test
    fun `deve retornar NO_CONTENT quando usuário for BASIC_ACCOUNT`() {
        loadAccountIntoDb(type = UserAccountType.BASIC_ACCOUNT, accountId = accountId, amazonDynamoDB = dynamoDB)
        val request = buildUpdateDailyPaymentLimitRequest(
            cookie,
            primaryWalletId,
            DailyPaymentLimitRequestTO(amount = 10_000_01, type = DailyPaymentLimitType.DAILY),
        )

        val response = client.toBlocking().exchange(request, VOID_OBJECT, Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.NO_CONTENT
    }

    @Test
    fun `should return BAD_REQUEST when nighttime amount is above 10k`() {
        withGivenDateTime(getZonedDateTime().minusDays(2)) {
            walletRepository.saveLimit(primaryWalletId, DailyPaymentLimitType.DAILY, 20_000_00, null)
        }

        val request = buildUpdateDailyPaymentLimitRequest(
            cookie,
            primaryWalletId,
            DailyPaymentLimitRequestTO(amount = 10_000_01, type = DailyPaymentLimitType.NIGHTTIME),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, VOID_OBJECT, Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4011"
    }

    @Test
    fun `should return NOT_FOUND when wallet is not found`() {
        val request = buildUpdateDailyPaymentLimitRequest(
            cookie,
            WalletId("not-found-wallet"),
            DailyPaymentLimitRequestTO(amount = 1000, type = DailyPaymentLimitType.DAILY),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, VOID_OBJECT, Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
    }

    @Test
    fun `should return NOT_FOUND when wallet is not accessible by account id`() {
        val request = buildUpdateDailyPaymentLimitRequest(
            cookie,
            WalletId("WALLET-NOT-FOUND"),
            DailyPaymentLimitRequestTO(amount = 1000, type = DailyPaymentLimitType.DAILY),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, VOID_OBJECT, Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
    }

    @Test
    fun `should return FORBIDDEN when account id is not founder`() {
        walletRepository.upsertMember(
            primaryWalletId,
            Member(
                accountId = AccountId(value = ACCOUNT_ID),
                document = "***********",
                name = "qny",
                emailAddress = EmailAddress(email = "<EMAIL>"),
                type = MemberType.COLLABORATOR,
                status = MemberStatus.ACTIVE,
                permissions = MemberPermissions(
                    viewBills = BillPermission.ALL_BILLS,
                    scheduleBills = BillPermission.ALL_BILLS,
                    founderContactsEnabled = false,
                    viewBalance = true,
                    notification = true,
                ),
                created = getZonedDateTime(),
                updated = getZonedDateTime(),
            ),
        )

        val request = buildUpdateDailyPaymentLimitRequest(
            generateCookie(AccountId(ACCOUNT_ID), Role.OWNER),
            primaryWalletId,
            DailyPaymentLimitRequestTO(amount = 1000, type = DailyPaymentLimitType.DAILY),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, VOID_OBJECT, Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `should return NO CONTENT when updates first limit`() {
        val request = buildUpdateDailyPaymentLimitRequest(
            cookie,
            primaryWalletId,
            DailyPaymentLimitRequestTO(amount = 1_000_00, type = DailyPaymentLimitType.DAILY),
        )

        val response = client.toBlocking().exchange(request, VOID_OBJECT, Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.NO_CONTENT
    }

    @Test
    fun `should return CONFLICT when change daily payment limit is in progress`() {
        walletRepository.saveLimit(primaryWalletId, DailyPaymentLimitType.DAILY, 20_000_00, 10_000_00)
        walletRepository.saveLimit(primaryWalletId, DailyPaymentLimitType.NIGHTTIME, 20_000_00, 10_000_00)

        val request = buildUpdateDailyPaymentLimitRequest(
            cookie,
            primaryWalletId,
            DailyPaymentLimitRequestTO(amount = 1_000_00, type = DailyPaymentLimitType.DAILY),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, VOID_OBJECT, Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.CONFLICT
    }

    @Test
    fun `should return CONFLICT when change daily payment is locked`() {
        internalLock.acquireLock(primaryWalletId.value)

        val request = buildUpdateDailyPaymentLimitRequest(
            cookie,
            primaryWalletId,
            DailyPaymentLimitRequestTO(amount = 1_000_00, type = DailyPaymentLimitType.DAILY),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, VOID_OBJECT, Argument.of(ResponseTO::class.java))
        }
        thrown.status shouldBe HttpStatus.CONFLICT
    }

    @Test
    fun `should return NO CONTENT and not add a new register when update daily payment limit with the same value`() {
        val lastLimitUpdatedAt = getZonedDateTime().minusDays(2)

        withGivenDateTime(lastLimitUpdatedAt) {
            walletRepository.saveLimit(primaryWalletId, DailyPaymentLimitType.DAILY, 20_000_00, null)
        }

        val request = buildUpdateDailyPaymentLimitRequest(
            cookie,
            primaryWalletId,
            DailyPaymentLimitRequestTO(amount = 20_000_00, type = DailyPaymentLimitType.DAILY),
        )

        val response = client.toBlocking().exchange(request, VOID_OBJECT, Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.NO_CONTENT

        walletRepository.findLimitOrNull(
            walletId = primaryWalletId,
            DailyPaymentLimitType.DAILY,
        )!!.updatedAt shouldBe lastLimitUpdatedAt
    }

    @Test
    fun `should return NO CONTENT when increase daily payment limit is successfully`() {
        val now = getZonedDateTime()

        withGivenDateTime(now.minusDays(2)) {
            walletRepository.saveLimit(primaryWalletId, DailyPaymentLimitType.DAILY, 10_000_00, null)
        }

        withGivenDateTime(now) {
            val request = buildUpdateDailyPaymentLimitRequest(
                cookie,
                primaryWalletId,
                DailyPaymentLimitRequestTO(amount = 20_000_00, type = DailyPaymentLimitType.DAILY),
            )

            val response = client.toBlocking()
                .exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))

            response.status shouldBe HttpStatus.NO_CONTENT

            with(walletRepository.findLimitOrNull(walletId = primaryWalletId, DailyPaymentLimitType.DAILY)!!) {
                updatedAt shouldBe now
                activeAmount shouldBe 10_000_00
                nextAmount shouldBe 20_000_00
            }
        }
    }

    @Test
    fun `should return NO CONTENT when decrease daily payment limit is successfully`() {
        val now = getZonedDateTime()

        withGivenDateTime(now.minusDays(2)) {
            walletRepository.saveLimit(primaryWalletId, DailyPaymentLimitType.DAILY, 20_000_00, null)
        }

        withGivenDateTime(now) {
            val request = buildUpdateDailyPaymentLimitRequest(
                cookie,
                primaryWalletId,
                DailyPaymentLimitRequestTO(amount = 10_000_00, type = DailyPaymentLimitType.DAILY),
            )

            val response = client.toBlocking()
                .exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))

            response.status shouldBe HttpStatus.NO_CONTENT

            with(walletRepository.findLimitOrNull(walletId = primaryWalletId, DailyPaymentLimitType.DAILY)!!) {
                updatedAt shouldBe now
                activeAmount shouldBe 10_000_00
                nextAmount shouldBe null
            }
        }
    }

    @Test
    fun `should return bad request when nighttly limit is greater than daily limit`() {
        val now = getZonedDateTime()

        withGivenDateTime(now.minusDays(2)) {
            walletRepository.saveLimit(primaryWalletId, DailyPaymentLimitType.DAILY, 200_00, null)
        }

        val thrown = withGivenDateTime(now) {
            val request = buildUpdateDailyPaymentLimitRequest(
                cookie,
                primaryWalletId,
                DailyPaymentLimitRequestTO(amount = 1_000_00, type = DailyPaymentLimitType.NIGHTTIME),
            )

            assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))
            }
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4010"
    }

    @Test
    fun `when daily limit is reduced to 10k and nighttime limit is 15k then nighttime limit should be changed to 10k`() {
        val now = getZonedDateTime()
        withGivenDateTime(now.minusDays(2)) {
            walletRepository.saveLimit(primaryWalletId, DailyPaymentLimitType.DAILY, 20_000_00, null)
            walletRepository.saveLimit(primaryWalletId, DailyPaymentLimitType.NIGHTTIME, 15_000_00, null)
        }

        withGivenDateTime(now) {
            val request = buildUpdateDailyPaymentLimitRequest(
                cookie,
                primaryWalletId,
                DailyPaymentLimitRequestTO(amount = 10_000_00, type = DailyPaymentLimitType.DAILY),
            )

            val response = client.toBlocking()
                .exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))

            response.status shouldBe HttpStatus.NO_CONTENT

            with(walletRepository.findLimitOrNull(walletId = primaryWalletId, DailyPaymentLimitType.NIGHTTIME)!!) {
                updatedAt shouldBe now
                activeAmount shouldBe 10_000_00
                nextAmount shouldBe null
            }
        }
    }

    @Test
    fun `when daily limit is reduced to 10k and nighttime limit is 9k with increase pending to 15k then nighttime limit change should be aborted`() {
        val now = getZonedDateTime()
        withGivenDateTime(now.minusDays(2)) {
            walletRepository.saveLimit(primaryWalletId, DailyPaymentLimitType.DAILY, 20_000_00, null)
        }

        val lastMinute = now.minusMinutes(1)
        withGivenDateTime(lastMinute) {
            walletRepository.saveLimit(primaryWalletId, DailyPaymentLimitType.NIGHTTIME, 15_000_00, 9_000_00)
        }

        withGivenDateTime(now) {
            val request = buildUpdateDailyPaymentLimitRequest(
                cookie,
                primaryWalletId,
                DailyPaymentLimitRequestTO(amount = 10_000_00, type = DailyPaymentLimitType.DAILY),
            )

            val response = client.toBlocking()
                .exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))

            response.status shouldBe HttpStatus.NO_CONTENT

            with(walletRepository.findLimitOrNull(walletId = primaryWalletId, DailyPaymentLimitType.NIGHTTIME)!!) {
                updatedAt shouldBe now
                activeAmount shouldBe 9_000_00
                nextAmount shouldBe null
            }
        }
    }

    @Test
    fun `when daily limit is reduced to 10k and nighttime limit is 11k with increase pending to 15k then nighttime limit should change to 10k`() {
        val now = getZonedDateTime()
        withGivenDateTime(now.minusDays(2)) {
            walletRepository.saveLimit(primaryWalletId, DailyPaymentLimitType.DAILY, 20_000_00, null)
        }

        val lastMinute = now.minusMinutes(1)
        withGivenDateTime(lastMinute) {
            walletRepository.saveLimit(primaryWalletId, DailyPaymentLimitType.NIGHTTIME, 15_000_00, 11_000_00)
        }

        withGivenDateTime(now) {
            val request = buildUpdateDailyPaymentLimitRequest(
                cookie,
                primaryWalletId,
                DailyPaymentLimitRequestTO(amount = 10_000_00, type = DailyPaymentLimitType.DAILY),
            )

            val response = client.toBlocking()
                .exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))

            response.status shouldBe HttpStatus.NO_CONTENT

            with(walletRepository.findLimitOrNull(walletId = primaryWalletId, DailyPaymentLimitType.NIGHTTIME)!!) {
                updatedAt shouldBe now
                activeAmount shouldBe 10_000_00
                nextAmount shouldBe null
            }
        }
    }

    @Test
    fun `when daily limit is reduced to 10k and nighttime limit is 8k with increase pending to 9k then nighttime limit change will not be affected`() {
        val now = getZonedDateTime()
        withGivenDateTime(now.minusDays(2)) {
            walletRepository.saveLimit(primaryWalletId, DailyPaymentLimitType.DAILY, 20_000_00, null)
        }

        val lastMinute = now.minusMinutes(1)
        withGivenDateTime(lastMinute) {
            walletRepository.saveLimit(primaryWalletId, DailyPaymentLimitType.NIGHTTIME, 9_000_00, 8_000_00)
        }

        withGivenDateTime(now) {
            val request = buildUpdateDailyPaymentLimitRequest(
                cookie,
                primaryWalletId,
                DailyPaymentLimitRequestTO(amount = 10_000_00, type = DailyPaymentLimitType.DAILY),
            )

            val response = client.toBlocking()
                .exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))

            response.status shouldBe HttpStatus.NO_CONTENT

            with(walletRepository.findLimitOrNull(walletId = primaryWalletId, DailyPaymentLimitType.NIGHTTIME)!!) {
                updatedAt shouldBe lastMinute
                activeAmount shouldBe 8_000_00
                nextAmount shouldBe 9_000_00
            }
        }
    }

    @Test
    fun `when no limit exist and daily limit is reduced to 500 then nighttime limit should also be reduced to 500`() {
        val now = getZonedDateTime()
        withGivenDateTime(now) {
            val request = buildUpdateDailyPaymentLimitRequest(
                cookie,
                primaryWalletId,
                DailyPaymentLimitRequestTO(amount = 500_00, type = DailyPaymentLimitType.DAILY),
            )

            val response = client.toBlocking()
                .exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))

            response.status shouldBe HttpStatus.NO_CONTENT

            with(walletRepository.findLimitOrNull(walletId = primaryWalletId, DailyPaymentLimitType.DAILY)!!) {
                updatedAt shouldBe now
                activeAmount shouldBe 500_00
                nextAmount shouldBe null
            }

            with(walletRepository.findLimitOrNull(walletId = primaryWalletId, DailyPaymentLimitType.NIGHTTIME)!!) {
                updatedAt shouldBe now
                activeAmount shouldBe 500_00
                nextAmount shouldBe null
            }
        }
    }
}