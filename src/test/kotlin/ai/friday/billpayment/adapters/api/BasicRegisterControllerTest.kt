package ai.friday.billpayment.adapters.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.EMAIL_ADDRESS
import ai.friday.billpayment.accountRegisterCompleted
import ai.friday.billpayment.accountRegisterData
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.OriginalOcrAndPersonDataDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.s3.S3LinkGenerator
import ai.friday.billpayment.adapters.serpro.BiometriaTO
import ai.friday.billpayment.adapters.serpro.SerproBiometriaFaceResponseTO
import ai.friday.billpayment.adapters.serpro.SerproBiometriaProbability
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.DocumentNumber
import ai.friday.billpayment.app.account.IdentityValidationStatus
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.DocumentValidationService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.msisdnauth.TEMPORARY_NICKNAME
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.ACCOUNT_ID_3
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.DOCUMENT_2
import ai.friday.billpayment.integration.SecurityFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.toSignedCookie
import ai.friday.billpayment.integration.withAudience
import ai.friday.billpayment.integration.withClaim
import ai.friday.billpayment.integration.withIssuer
import ai.friday.billpayment.integration.withSubject
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.nimbusds.jwt.JWTClaimsSet
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.micronaut.context.annotation.Primary
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.PropertySource
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

const val agreementFileUrl2 = "https://s3.file/termos_de_uso.pdf"
const val upgradedAccountId = ACCOUNT_ID_3
val upgradedAccountCookie = JWTClaimsSet.Builder()
    .withSubject(upgradedAccountId)
    .withIssuer("https://friday.ai")
    .withAudience("friday.ai")
    .withClaim("email", "+<EMAIL>")
    .toSignedCookie()

@MicronautTest(environments = [FRIDAY_ENV])
@PropertySource(
    value = [
        Property(name = "urls.termsOfUse", value = agreementFileUrl2),
        Property(name = "sqs.simpleSignUpQueueName", value = "simpleSignUpQueueName"),
    ],
)
class BasicRegisterControllerTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {

    private val dynamoDbEnhancedClient = getDynamoDB()

    private val accountDAO = AccountDynamoDAO(dynamoDbEnhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(dynamoDbEnhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(dynamoDbEnhancedClient)
    private val nsuDAO = NSUDynamoDAO(dynamoDbEnhancedClient)
    private val transactionDynamo = TransactionDynamo(dynamoDbEnhancedClient)

    private val accountDbRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )
    private val accountRegisterDAO = AccountRegisterDynamoDAO(dynamoDbEnhancedClient)
    private val originalOcrAndPersonDataDAO = OriginalOcrAndPersonDataDynamoDAO(dynamoDbEnhancedClient)
    private val accountRegisterRepository = AccountRegisterDbRepository(accountRegisterDAO, originalOcrAndPersonDataDAO, mockk(relaxed = true))
    private val securityFixture = SecurityFixture()

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)
    private val s3LinkGeneratorMock = mockk<S3LinkGenerator>(relaxed = true)
    private val documentValidationServiceMock = mockk<DocumentValidationService>(relaxed = true) {
        every {
            validateWithSelfie(
                ofType<Document>(),
                any(),
                any(),
            )
        } returns SerproBiometriaFaceResponseTO(
            cpfDisponivel = false,
            cnhDisponivel = false,
            biometriaFace = BiometriaTO(
                disponivel = true,
                probabilidade = SerproBiometriaProbability.HIGH,
                similaridade = 100.0,
            ),
        ).right()
    }

    @Primary
    @MockBean
    fun mocks3LinkGenerator(): S3LinkGenerator {
        return s3LinkGeneratorMock
    }

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @MockBean(value = MessagePublisher::class)
    fun messagePublisher(): MessagePublisher = messagePublisher
    private val messagePublisher: MessagePublisher = mockk(relaxed = true)

    @Primary
    @MockBean(DocumentValidationService::class)
    fun documentValidationService(): DocumentValidationService {
        return documentValidationServiceMock
    }

    @Primary
    @MockBean(AccountRegisterRepository::class)
    fun accountRegisterRepository(): AccountRegisterRepository = accountRegisterRepository

    @MockBean(NotificationAdapter::class)
    fun notificationAdapter(): NotificationAdapter = notificationAdapter
    private val notificationAdapter: NotificationAdapter = mockk(relaxed = true)

    @BeforeEach
    fun setUp() {
        createBillPaymentTable(dynamoDB)

        // Primeiro usuário

        accountDbRepository.create(
            username = TEMPORARY_NICKNAME,
            emailAddress = EmailAddress("<EMAIL>"),
            accountId = AccountId(ACCOUNT_ID),
            registrationType = RegistrationType.BASIC,
        )
        accountRegisterRepository.save(
            accountRegisterCompleted.copy(
                accountId = AccountId(ACCOUNT_ID),
                documentInfo = null,
                document = Document(DOCUMENT),
                agreementData = null,
                birthDate = LocalDate.now().minusYears(18),
                livenessId = LivenessId("livenessId"),
                registrationType = RegistrationType.BASIC,
            ),
        )

        // Segundo usuário - simplificado novo

        accountDbRepository.create(
            username = TEMPORARY_NICKNAME,
            emailAddress = EmailAddress("<EMAIL>"),
            accountId = AccountId(ACCOUNT_ID_2),
            registrationType = RegistrationType.BASIC,
        )
        accountRegisterRepository.save(
            accountRegisterData.copy(
                accountId = AccountId(ACCOUNT_ID_2),
                registrationType = RegistrationType.BASIC,
            ),
        )

        // Terceiro usuário - upgraded reaberto

        accountDbRepository.create(
            username = TEMPORARY_NICKNAME,
            emailAddress = EmailAddress("<EMAIL>"),
            accountId = AccountId(upgradedAccountId),
            registrationType = RegistrationType.BASIC,
        )
        accountRegisterRepository.save(
            accountRegisterCompleted.copy(
                document = Document("***********"),
                accountId = AccountId(upgradedAccountId),
                registrationType = RegistrationType.UPGRADED,
                openForUserReview = true,
            ),
        )
    }

    @Test
    fun `deve marcar usuário como fraudador caso ele esteja na lista de fraudadores`() {
        val request = buildAddDocumentNumberRequest(
            "***********",
            securityFixture.cookieMsisdnAuth,
        )
        val response = client.toBlocking()
            .exchange(request, Argument.of(BasicAccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))
        response.status shouldBe HttpStatus.OK

        accountRegisterRepository.findByAccountId(AccountId(ACCOUNT_ID_2)).fraudListMatch.shouldBeTrue()
    }

    @Test
    fun `deve atualizar documento se o documento nao estiver cadastrado para outro usuario`() {
        val request = buildAddDocumentNumberRequest(
            DOCUMENT_2,
            securityFixture.cookieMsisdnAuth,
        )
        val response = client.toBlocking()
            .exchange(request, Argument.of(BasicAccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))
        response.status shouldBe HttpStatus.OK
        response.body()?.document shouldBe DOCUMENT_2
    }

    @Test
    fun `retornar sucesso ao atualizar documento e o documento já estiver cadastrado para o mesmo usuario`() {
        val request = buildAddDocumentNumberRequest(
            DOCUMENT,
            JWTClaimsSet.Builder()
                .withSubject(ACCOUNT_ID)
                .withIssuer("https://friday.ai")
                .withAudience("friday.ai")
                .withClaim("email", "+<EMAIL>")
                .toSignedCookie(),
        )
        val response = client.toBlocking()
            .exchange(request, Argument.of(BasicAccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))
        response.status shouldBe HttpStatus.OK
        response.body()?.document shouldBe DOCUMENT
    }

    @Test
    fun `deve recusar um cadastro ao receber um documento já cadastrado`() {
        val request = buildAddDocumentNumberRequest(
            DOCUMENT,
            securityFixture.cookieMsisdnAuth,
        )
        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(
                    request,
                    Argument.of(BasicAccountRegisterDataTO::class.java),
                    Argument.of(ResponseTO::class.java),
                )
        }
        thrown.status shouldBe HttpStatus.CONFLICT
        thrown.response.getBody(Argument.of(ResponseTO::class.java)).get() shouldBe ResponseTO(
            "4036",
            "Documento já está cadastrado",
        )
    }

    @Test
    fun `deve atualizar nome e data de nascimento sem alterar no documentInfo para um cadastro sem documentInfo`() {
        val newName = "João da Silva"
        val newBirthDate = LocalDate.of(1990, 1, 1)
        val request = buildAddPersonalInfoRequest(
            newName,
            newBirthDate,
            securityFixture.cookieMsisdnAuth,
        )
        val response = client.toBlocking()
            .exchange(request, Argument.of(BasicAccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))
        response.status shouldBe HttpStatus.OK
        response.body()?.name shouldBe newName
        response.body()?.birthDate shouldBe "01/01/1990"

        with(accountDbRepository.findPartialAccountById(AccountId(ACCOUNT_ID_2))) {
            name shouldBe newName
        }

        with(accountRegisterRepository.findByAccountId(AccountId(ACCOUNT_ID_2))) {
            nickname shouldBe newName
            birthDate shouldBe newBirthDate
            documentInfo?.name shouldNotBe newName
            documentInfo?.birthDate shouldNotBe newBirthDate
        }
    }

    @Test
    fun `deve atualizar o nome e data de nascimento também no documentInfo se for um cadastro que já tem documentInfo`() {
        val newName = "Novo nome"
        val newBirthDate = LocalDate.of(1989, 2, 20)

        val request = buildAddPersonalInfoRequest(newName, newBirthDate, upgradedAccountCookie)
        val response = client.toBlocking()
            .exchange(request, Argument.of(BasicAccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        response.body()?.name shouldBe newName
        response.body()?.birthDate shouldBe "20/02/1989"

        with(accountDbRepository.findPartialAccountById(AccountId(upgradedAccountId))) {
            name shouldBe newName
        }

        with(accountRegisterRepository.findByAccountId(AccountId(upgradedAccountId))) {
            nickname shouldBe newName
            birthDate shouldBe newBirthDate
            documentInfo?.name shouldBe newName
            documentInfo?.birthDate shouldBe newBirthDate
        }
    }

    @Test
    fun `deve atualizar a informação de PEP em um cadastro que ainda não tem PEP`() {
        val request = buildPoliticallyExposedRequest(true, upgradedAccountCookie)
        val response = client.toBlocking()
            .exchange(request, Argument.of(BasicAccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        response.body()?.politicallyExposed?.isExposed shouldBe true

        with(accountRegisterRepository.findByAccountId(AccountId(upgradedAccountId))) {
            politicallyExposed?.selfDeclared shouldBe true
        }
    }

    @Test
    fun `deve atualizar a informação de PEP em um cadastro que ainda já possui PEP`() {
        client.toBlocking()
            .exchange(
                buildPoliticallyExposedRequest(true, upgradedAccountCookie),
                Argument.of(BasicAccountRegisterDataTO::class.java),
                Argument.of(ResponseTO::class.java),
            )

        val request = buildPoliticallyExposedRequest(false, upgradedAccountCookie)
        val response = client.toBlocking()
            .exchange(request, Argument.of(BasicAccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        response.body()?.politicallyExposed?.isExposed shouldBe false

        with(accountRegisterRepository.findByAccountId(AccountId(upgradedAccountId))) {
            politicallyExposed?.selfDeclared shouldBe false
        }
    }

    @Test
    fun `deve retornar um account register de um usuário existente`() {
        val request = HttpRequest.GET<Unit>("/basicRegister")
            .cookie(securityFixture.cookieMsisdnAuth).header("X-API-VERSION", "2")

        val response = client.toBlocking()
            .exchange(request, Argument.of(BasicAccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get()) {
            assertSoftly {
                email shouldBe "<EMAIL>"
                name shouldBe "Skywalker"
                identityValidationStatus shouldBe null
            }
        }
    }

    @Test
    fun `deve iniciar o processo de conclusao de cadastro ao receber um aceite do usuario`() {
        accountDbRepository.create(
            username = "Skywalker",
            emailAddress = EMAIL_ADDRESS,
            accountId = AccountId(ACCOUNT_ID),
            registrationType = RegistrationType.BASIC,
        )

        val cookie = JWTClaimsSet.Builder()
            .withSubject(ACCOUNT_ID)
            .withIssuer("https://friday.ai")
            .withAudience("friday.ai")
            .withClaim("email", EMAIL_ADDRESS.value)
            .toSignedCookie()
        val request = agreementRequest(cookie)
        val response = client.toBlocking()
            .exchange(request, Argument.of(BasicAccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))
        response.status shouldBe HttpStatus.OK
        response.body()?.agreement shouldNotBe null
        verify {
            messagePublisher.sendMessage("simpleSignUpQueueName", any()) // TODO validar a chamada
        }
        val account = accountDbRepository.findPartialAccountById(AccountId(ACCOUNT_ID))
        account.status shouldBe AccountStatus.UNDER_REVIEW
    }

    @Test
    fun `se a informacao da serpro já exitir no banco, não chama a serpro`() {
        accountRegisterRepository.save(
            accountRegisterRepository.findByAccountId(AccountId(ACCOUNT_ID))
                .copy(
                    identityValidationStatus = IdentityValidationStatus.CHECKED,
                    identityValidationPercentage = 1.0,
                ),
        )

        val response = client.toBlocking()
            .exchange(
                validateIdentityRequest(),
                Argument.of(VerifyIdentityResponseTO::class.java),
                Argument.of(ResponseTO::class.java),
            )
        response.status shouldBe HttpStatus.OK
        response.body.get().registrationType shouldBe RegistrationType.BASIC.name

        accountDbRepository.findPartialAccountById(accountId = AccountId(ACCOUNT_ID)).registrationType shouldBe RegistrationType.BASIC

        verify(exactly = 0) {
            documentValidationServiceMock.validateWithSelfie(ofType<Document>(), any(), any())
        }
    }

    @Test
    fun `deve devolver cadastro upgraded quando o usuário não passa na serpro`() {
        every {
            documentValidationServiceMock.validateWithSelfie(
                ofType<Document>(),
                any(),
                any(),
            )
        } returns SerproBiometriaFaceResponseTO(
            cpfDisponivel = false,
            cnhDisponivel = false,
            biometriaFace = BiometriaTO(
                disponivel = true,
                probabilidade = SerproBiometriaProbability.LOW,
                similaridade = 10.0,
            ),
        ).right()

        val response = client.toBlocking()
            .exchange(
                validateIdentityRequest(),
                Argument.of(VerifyIdentityResponseTO::class.java),
                Argument.of(ResponseTO::class.java),
            )

        response.status shouldBe HttpStatus.OK
        response.body.get().registrationType shouldBe RegistrationType.UPGRADED.name

        verify {
            documentValidationServiceMock.validateWithSelfie(ofType<Document>(), any(), any())
        }

        verify(exactly = 0) {
            notificationAdapter.notifyRegisterUpgraded(any(), any())
        }

        accountDbRepository.findPartialAccountById(accountId = AccountId(ACCOUNT_ID)).registrationType shouldBe RegistrationType.UPGRADED
    }

    @Test
    fun `deve atualizar o document info quando editar os detalhes do documento`() {
        val request = buildUpdateDocumentDetailsRequest(upgradedAccountCookie)
        client.toBlocking()
            .exchange(request, Argument.of(BasicAccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))

        with(accountRegisterRepository.findByAccountId(AccountId(upgradedAccountId))) {
            documentInfo?.name shouldBe nickname
            documentInfo?.birthDate shouldBe birthDate
            documentInfo?.cpf shouldBe document?.value
        }
    }

    @Test
    fun `deve devolver um cadastro basico quando passa na serpro`() {
        every {
            documentValidationServiceMock.validateWithSelfie(
                ofType<Document>(),
                any(),
                any(),
            )
        } returns SerproBiometriaFaceResponseTO(
            cpfDisponivel = false,
            cnhDisponivel = false,
            biometriaFace = BiometriaTO(
                disponivel = true,
                probabilidade = SerproBiometriaProbability.HIGH,
                similaridade = 100.0,
            ),
        ).right()

        val request = validateIdentityRequest()

        val response = client.toBlocking()
            .exchange(request, Argument.of(VerifyIdentityResponseTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        response.body.get().registrationType shouldBe RegistrationType.BASIC.name

        verify {
            documentValidationServiceMock.validateWithSelfie(ofType<Document>(), any(), any())
        }

        accountDbRepository.findPartialAccountById(accountId = AccountId(ACCOUNT_ID)).registrationType shouldBe RegistrationType.BASIC
    }

    @Test
    fun `deve recusar um aceite do usuario se o cadastro não estiver completo`() {
        val request = agreementRequest(securityFixture.cookieMsisdnAuth)
        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(
                    request,
                    Argument.of(BasicAccountRegisterDataTO::class.java),
                    Argument.of(ResponseTO::class.java),
                )
        }
        thrown.response.status shouldBe HttpStatus.BAD_REQUEST
        verify(exactly = 0) {
            messagePublisher.sendMessage("simpleSignUpQueueName", any())
        }
    }

    private fun agreementRequest(cookie: Cookie): MutableHttpRequest<AgreementRequestTO>? =
        HttpRequest.PUT("/basicRegister/agreement", AgreementRequestTO(true))
            .cookie(cookie).header("X-API-VERSION", "2")

    private fun buildAddDocumentNumberRequest(
        document: String,
        cookie: Cookie,
    ): MutableHttpRequest<DocumentTO> {
        return HttpRequest.PUT("/basicRegister/document", DocumentTO(document))
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    private fun validateIdentityRequest(): MutableHttpRequest<Unit>? {
        val cookie = JWTClaimsSet.Builder()
            .withSubject(ACCOUNT_ID)
            .withIssuer("https://friday.ai")
            .withAudience("friday.ai")
            .withClaim("email", "+<EMAIL>")
            .toSignedCookie()
        val request = HttpRequest.PUT("/basicRegister/validateIdentity", Unit)
            .cookie(cookie).header("X-API-VERSION", "2")
        return request
    }

    private fun buildAddPersonalInfoRequest(
        name: String,
        birthDate: LocalDate,
        cookie: Cookie,
    ): MutableHttpRequest<PersonalInfoTO> {
        return HttpRequest.PUT(
            "/basicRegister/personalInfo",
            PersonalInfoTO(
                name,
                birthDate.format(
                    DateTimeFormatter.ofPattern("dd/MM/yyyy"),
                ),
            ),
        ).cookie(cookie)
            .header("X-API-VERSION", "2")
    }

    private fun buildPoliticallyExposedRequest(
        isExposed: Boolean,
        cookie: Cookie,
    ): MutableHttpRequest<PoliticallyExposedRequestTO> {
        return HttpRequest.PUT(
            "/basicRegister/politicallyExposed",
            PoliticallyExposedRequestTO(
                isExposed = isExposed,
            ),
        ).cookie(cookie)
            .header("X-API-VERSION", "2")
    }

    private fun buildUpdateDocumentDetailsRequest(
        cookie: Cookie,
    ): MutableHttpRequest<DocumentDetailsTO> {
        return HttpRequest.PUT(
            "/basicRegister/document/details",
            DocumentDetailsTO(
                fatherName = "John Doe",
                motherName = "Jane Doe",
                birthCity = "Rio de Janeiro",
                birthState = "RJ",
                orgEmission = "SSP-RJ",
                documentNumber = DocumentNumber(
                    type = ai.friday.billpayment.app.account.DocumentDetailsType.DRIVERS_LICENSE,
                    value = "1231415",
                ),
                expeditionDate = "23/08/1988",
            ),
        ).cookie(cookie)
            .header("X-API-VERSION", "2")
    }
}