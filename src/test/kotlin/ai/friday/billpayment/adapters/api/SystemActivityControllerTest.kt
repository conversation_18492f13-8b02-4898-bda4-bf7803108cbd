package ai.friday.billpayment.adapters.api

import DynamoDBUtils
import ai.friday.billpayment.PARTIAL_ACCOUNT
import ai.friday.billpayment.accountRegisterCompleted
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.OriginalOcrAndPersonDataDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SystemActivityDbRepository
import ai.friday.billpayment.adapters.dynamodb.SystemActivityDynamoDAO
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.SystemActivityType
import ai.friday.billpayment.app.account.toSystemActivityKey
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.integration.generateCookie
import ai.friday.billpayment.integration.loadPartialAccountIntoDb
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import java.time.ZonedDateTime
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

@MicronautTest(environments = [FRIDAY_ENV])
class SystemActivityControllerTest(
    embeddedServer: EmbeddedServer,
    val dynamoDB: AmazonDynamoDB,
) {
    private val enhancedClient = DynamoDBUtils.setupDynamoDB()

    private val repository = SystemActivityDbRepository(SystemActivityDynamoDAO(enhancedClient))
    private val accountRegisterDAO = AccountRegisterDynamoDAO(enhancedClient)
    private val originalOcrAndPersonDataDAO = OriginalOcrAndPersonDataDynamoDAO(enhancedClient)
    private val accountRegisterRepository = AccountRegisterDbRepository(accountRegisterDAO, originalOcrAndPersonDataDAO, mockk())

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @MockBean(AccountService::class)
    fun accountService() = accountService
    private val accountService = mockk<AccountService> {
        every {
            findPartialAccountById(any())
        } returns PARTIAL_ACCOUNT
    }

    @MockBean(NotificationAdapter::class)
    fun notificationAdapter() = notificationAdapter
    private val notificationAdapter = mockk<NotificationAdapter>(relaxed = true)

    @MockBean(MessagePublisher::class)
    fun messagePublisherMock() = messagePublisher
    private val messagePublisher: MessagePublisher = mockk(relaxed = true)

    private val accountId = PARTIAL_ACCOUNT.id

    @BeforeEach
    fun setup() {
        loadPartialAccountIntoDb(dynamoDB, accountId = accountId)
        accountRegisterRepository.save(accountRegisterCompleted.copy(accountId = PARTIAL_ACCOUNT.id, document = Document("***********")))
    }

    @ParameterizedTest
    @MethodSource("activities")
    fun `deve registrar corretamente uma atividade`(
        path: String,
        type: SystemActivityType,
        now: ZonedDateTime,
        expected: String,
    ) {
        val request = buildActivityRequest(
            cookie = generateCookie(accountId, Role.GUEST),
            activity = path,
        )

        withGivenDateTime(now) {
            val response = client.toBlocking()
                .exchange(request, Argument.VOID)

            response.status shouldBe HttpStatus.ACCEPTED

            val activity = repository.find(accountId.toSystemActivityKey(), type)

            activity?.value shouldBe expected
        }
    }

    @Test
    fun `deve retornar 400 quando a atividade não existir`() {
        val request = buildActivityRequest(
            cookie = generateCookie(accountId, Role.GUEST),
            activity = "atividade-que-nao-existe",
        )

        val response = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.VOID)
        }

        response.status shouldBe HttpStatus.BAD_REQUEST
    }

    @Test
    fun `deve registar uma resposta do questionário corretamente`() {
        val reply = SurveyReplyTO(
            expenses = listOf("Aluguel", "IPVA", "Luz"),
            bankPreference = "nubank",
            billsManagementQuantity = null,
            billsManagementPeople = null,
        )

        val request =
            buildSurveyReplyRequest(cookie = generateCookie(accountId, Role.GUEST), reply)

        val response = client.toBlocking()
            .exchange(request, Argument.VOID)

        response.status shouldBe HttpStatus.ACCEPTED

        val activity = repository.find(accountId.toSystemActivityKey(), SystemActivityType.SurveyReplied)

        val answers = getObjectMapper().readValue(activity?.value, Map::class.java)
        answers["expenses"] shouldBe listOf("Aluguel", "IPVA", "Luz")
        answers["bankPreference"] shouldBe "nubank"

        activity?.value shouldBe "{\"expenses\":[\"Aluguel\",\"IPVA\",\"Luz\"],\"bankPreference\":\"nubank\",\"billsManagementQuantity\":null,\"billsManagementPeople\":null}"
    }

    @Test
    fun `quando o questionário foi respondido parcialmente, deve retornar false`() {
        val reply = SurveyReplyTO(
            expenses = listOf("Aluguel", "IPVA", "Luz"),
            bankPreference = "nubank",
            billsManagementQuantity = null,
            billsManagementPeople = null,
        )

        val request =
            buildSurveyReplyRequest(cookie = generateCookie(accountId, Role.GUEST), reply)

        val response = client.toBlocking()
            .exchange(request, Argument.VOID)

        response.status shouldBe HttpStatus.ACCEPTED

        val repliedResponse = client.toBlocking().exchange(buildSurveyRepliedRequest(cookie = generateCookie(accountId, Role.GUEST)), String::class.java)

        repliedResponse.status shouldBe HttpStatus.OK
        repliedResponse.body.get() shouldBe "{\"replied\":false}"
    }

    @Test
    fun `quando o questionário foi respondido completamente, deve retornar true`() {
        val reply = SurveyReplyTO(
            expenses = listOf("Aluguel", "IPVA", "Luz"),
            bankPreference = "nubank",
            billsManagementQuantity = "ALL",
            billsManagementPeople = listOf("MYSELF", "SPOUSE"),
        )

        val request =
            buildSurveyReplyRequest(cookie = generateCookie(accountId, Role.GUEST), reply)

        val response = client.toBlocking()
            .exchange(request, Argument.VOID)

        response.status shouldBe HttpStatus.ACCEPTED

        val repliedResponse = client.toBlocking().exchange(buildSurveyRepliedRequest(cookie = generateCookie(accountId, Role.GUEST)), String::class.java)

        repliedResponse.status shouldBe HttpStatus.OK
        repliedResponse.body.get() shouldBe "{\"replied\":true}"
    }

    private fun buildActivityRequest(
        cookie: Cookie,
        activity: String,
    ): MutableHttpRequest<*> {
        return HttpRequest.PUT("/activity/$activity", object {})
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    private fun buildSurveyReplyRequest(
        cookie: Cookie,
        reply: SurveyReplyTO,
    ): MutableHttpRequest<*> {
        return HttpRequest.PUT("/activity/survey/replied", reply)
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    private fun buildSurveyRepliedRequest(
        cookie: Cookie,
    ): MutableHttpRequest<*> {
        return HttpRequest.GET<String>("/activity/survey/has-replied")
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    companion object {
        @JvmStatic
        fun activities(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    "signed-in",
                    SystemActivityType.LastSignedIn,
                    ZonedDateTime.of(2024, 6, 15, 10, 12, 13, 0, brazilTimeZone),
                    "2024-06-15 10:12:13",
                ),
                Arguments.arguments(
                    "viewed-category-summary",
                    SystemActivityType.ViewedCategorySummary,
                    ZonedDateTime.of(2023, 6, 1, 1, 23, 13, 0, brazilTimeZone),
                    "2023-06-01 01:23:13",
                ),
                Arguments.arguments(
                    "subscription-not-supported-by-device",
                    SystemActivityType.SubscriptionNotSupportedByDevice,
                    ZonedDateTime.of(2001, 1, 1, 10, 12, 13, 0, brazilTimeZone),
                    "2001-01-01 10:12:13",
                ),
            )
        }
    }
}