package ai.friday.billpayment.adapters.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.wallet.InviteStatus
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.buildInvite
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.buildCookie
import ai.friday.billpayment.integration.createBillPaymentTable
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.mockk
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource

@MicronautTest(environments = [FRIDAY_ENV])
class CancelInviteTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {

    private val enhancedClient = getDynamoDB()

    @MockBean(WalletRepository::class)
    fun walletRepository(): WalletRepository = walletRepository

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletRepository: WalletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = mockk(),
    )

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    val walletFixture = WalletFixture()
    val wallet = walletFixture.buildWallet(otherMembers = listOf(walletFixture.participant))

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `should return NOT FOUND when wallet does not exist`() {
        val request = buildRequest(buildCookie(walletFixture.founderAccount), wallet.id, "***********")

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(Unit::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4004"
        thrown.response.getBody(ResponseTO::class.java).get().message shouldBe "WalletId ${wallet.id.value} not found"
    }

    @Test
    fun `should return NOT FOUND when invite does not exist`() {
        walletRepository.save(wallet)

        val request = buildRequest(buildCookie(walletFixture.founderAccount), wallet.id, "***********")

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(Unit::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4005"
        thrown.response.getBody(ResponseTO::class.java).get().message shouldBe "Invite not found"
    }

    @ParameterizedTest
    @MethodSource("notFounderAccounts")
    fun `should return FORBIDDEN when user is not the founder`(memberAccount: Account) {
        walletRepository.save(wallet)

        val request = buildRequest(buildCookie(memberAccount), wallet.id, "***********")

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(Unit::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @ParameterizedTest
    @EnumSource(InviteStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["PENDING", "CANCELED"])
    fun `should return GONE when invite is not PENDING nor CANCELED`(inviteStatus: InviteStatus) {
        walletRepository.save(wallet)
        val invite = buildInvite(inviteStatus)
        walletRepository.save(invite)

        val request = buildRequest(buildCookie(walletFixture.founderAccount), invite.walletId, invite.memberDocument)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(Unit::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.GONE
    }

    @ParameterizedTest
    @EnumSource(InviteStatus::class, mode = EnumSource.Mode.INCLUDE, names = ["PENDING", "CANCELED"])
    fun `should return ACCEPTED when invite is PENDING or CANCELED`(inviteStatus: InviteStatus) {
        walletRepository.save(wallet)
        val invite = buildInvite(inviteStatus)
        walletRepository.save(invite)

        val request = buildRequest(buildCookie(walletFixture.founderAccount), invite.walletId, invite.memberDocument)

        val response =
            client.toBlocking().exchange(request, Argument.of(Unit::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.ACCEPTED

        val savedInvite = walletRepository.findInvite(invite.walletId, invite.memberDocument)
        savedInvite.status shouldBe InviteStatus.CANCELED
    }

    private fun buildRequest(cookie: Cookie, walletId: WalletId, document: String): MutableHttpRequest<*> {
        return HttpRequest.DELETE<Any>("/wallet/${walletId.value}/invite/$document")
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    companion object {
        private val walletFixture = WalletFixture()

        @JvmStatic
        fun notFounderAccounts(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(walletFixture.participantAccount),
                Arguments.arguments(walletFixture.assistantAccount),
            )
        }
    }
}