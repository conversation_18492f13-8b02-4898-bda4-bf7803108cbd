package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.arbi.DomainTO
import ai.friday.billpayment.app.FinancialInstitutionGlobalData
import ai.friday.billpayment.app.banking.Bank
import ai.friday.billpayment.app.banking.FinancialInstitution
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.kotest.inspectors.forAll
import io.kotest.inspectors.forExactly
import io.kotest.inspectors.forNone
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.unmockkObject
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class FinancialInstitutionControllerTest {

    private val financialInstitutionController = FinancialInstitutionController()

    private val BANCO_SANTANDER_BRASIL_COMPE = 353L

    private val itau = FinancialInstitutionTO(
        code = 341,
        name = "Banco Itau S.A.",
        ispb = "********",
        isPixEnabled = true,
        isTedEnabled = true,
        isCommon = true,
        shortLabel = "Itaú",
        labels = listOf("341 - banco itaú s.a.", "341 - banco itau s.a."),
    )

    private val itaucardName = "BANCO ITAUCARD S.A."
    private val itaucard = FinancialInstitutionTO(
        code = null,
        name = itaucardName,
        ispb = "17192451",
        isPixEnabled = true,
        isTedEnabled = false,
        isCommon = false,
        labels = listOf(itaucardName.lowercase()),
    )
    private val xpCorretora = FinancialInstitutionTO(
        code = 102,
        name = "Xp Investimentos Corretora",
        ispb = null,
        isPixEnabled = false,
        isTedEnabled = true,
        isCommon = true,
        labels = listOf("102 - xp investimentos corretora"),
    )

    private val dominio2Stream =
        Thread.currentThread().contextClassLoader.getResourceAsStream("arbi/dominio_2_bancos.json")
    private val dominio2Data =
        jacksonObjectMapper().readerForListOf(DomainTO::class.java).readValue<List<DomainTO>>(dominio2Stream)
    private val dominio41Stream =
        Thread.currentThread().contextClassLoader.getResourceAsStream("arbi/dominio_41_pix.json")
    private val dominio41Data =
        jacksonObjectMapper().readerForListOf(DomainTO::class.java).readValue<List<DomainTO>>(dominio41Stream)

    private val DEFAULT_BANK_LIST = dominio2Data.map {
        Bank(
            number = it.codigodominio.toLong(),
            name = it.descricaodominio.orEmpty(),
        )
    }

    private val DEFAULT_PIX_LIST = dominio41Data.map {
        FinancialInstitution(
            compe = it.codigodominio.takeLast(3).toLongOrNull(),
            name = it.descricaodominio.orEmpty(),
            ispb = it.codigodominio.take(8),
        )
    }

    @BeforeEach
    fun init() {
        mockkObject(FinancialInstitutionGlobalData)
        every { FinancialInstitutionGlobalData.pixParticipants } returns DEFAULT_PIX_LIST
        every { FinancialInstitutionGlobalData.bankList } returns DEFAULT_BANK_LIST
    }

    @AfterEach
    fun clean() {
        unmockkObject(FinancialInstitutionGlobalData)
    }

    @Test
    fun `should contain itau on get financial institution`() {
        val response = financialInstitutionController.getAllFinancialInstitutions()

        response.financialInstitutions.forExactly(1) {
            it.code shouldBe itau.code
            it.isTedEnabled shouldBe true
            it.name shouldBe itau.name
            it.isPixEnabled shouldBe true
            it.ispb shouldBe itau.ispb
            it.isCommon shouldBe itau.isCommon
            it.shortLabel
            it.labels shouldContainExactlyInAnyOrder itau.labels
        }
    }

    @Test
    fun `should contain itaucard on get financial institution`() {
        val response = financialInstitutionController.getAllFinancialInstitutions()
        response.financialInstitutions.forExactly(1) {
            it.code shouldBe itaucard.code
            it.isTedEnabled shouldBe false
            it.name shouldBe itaucard.name
            it.isPixEnabled shouldBe true
            it.ispb shouldBe itaucard.ispb
            it.isCommon shouldBe itaucard.isCommon
            it.labels shouldContainExactlyInAnyOrder itaucard.labels
        }
    }

    @Test
    fun `should contain xp on get financial institution`() {
        val response = financialInstitutionController.getAllFinancialInstitutions()
        response.financialInstitutions.forExactly(1) {
            it.code shouldBe xpCorretora.code
            it.isTedEnabled shouldBe true
            it.name shouldBe xpCorretora.name
            it.isPixEnabled shouldBe false
            it.ispb shouldBe xpCorretora.ispb
            it.isCommon shouldBe xpCorretora.isCommon
            it.labels shouldContainExactlyInAnyOrder xpCorretora.labels
        }
    }

    @Test
    fun `should not contain Banco Santander Brasil on get financial institution`() {
        val response = financialInstitutionController.getAllFinancialInstitutions()
        response.financialInstitutions.filter {
            it.code == BANCO_SANTANDER_BRASIL_COMPE
        }.shouldBeEmpty()
    }

    @Test
    fun `should find none ted and pix disabled`() {
        val response = financialInstitutionController.getAllFinancialInstitutions()
        response.financialInstitutions.forNone {
            it.isTedEnabled shouldBe false
            it.isPixEnabled shouldBe false
        }
    }

    @Test
    fun `should find all ted enabled`() {
        val response = financialInstitutionController.getAllFinancialInstitutions()
        response.financialInstitutions.forExactly(430 - 181 + 30) { // TOTAL-EXCLUSIONS-NAO EXITEM NO ARBI
            it.isTedEnabled shouldBe true
        }
    }

    @Test
    fun `should find all pix enabled`() {
        val response = financialInstitutionController.getAllFinancialInstitutions()
        response.financialInstitutions.forExactly(734) {
            it.isPixEnabled shouldBe true
        }
    }

    @Test
    fun `should find all common banks`() {
        val response = financialInstitutionController.getAllFinancialInstitutions()
        response.financialInstitutions.forExactly(11) {
            it.isCommon shouldBe true
        }
    }

    @Test
    fun `list should be ordered by isCommon`() {
        val response = financialInstitutionController.getAllFinancialInstitutions()
        response.financialInstitutions.take(11).forAll {
            it.isCommon shouldBe true
        }
        response.financialInstitutions.drop(11).forAll {
            it.isCommon shouldBe false
        }
    }

    @Test
    fun `should have at least one element at labels list`() {
        val response = financialInstitutionController.getAllFinancialInstitutions()
        response.financialInstitutions.filter {
            it.labels.isEmpty()
        }.shouldBeEmpty()
    }
}